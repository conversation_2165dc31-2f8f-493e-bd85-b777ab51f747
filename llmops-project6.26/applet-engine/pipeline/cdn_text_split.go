package pipeline

import (
	"strconv"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

const (
	defaultSeparator = "\n"
	defaultChunkSize = 200
	minChunkSize     = 50
)

// TextSplitNode
//
//	stream
//	    |textSplit()
type TextSplitNode struct {
	chainnode `json:"-"`
	Separator string `json:"separator"`
	ChunkSize int64  `json:"chunk_size"`
	models.NodeMeta
}

func newTextSplitNode() *TextSplitNode {
	return &TextSplitNode{chainnode: newBasicChainNode("textSplit", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *TextSplitNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	if s.Separator == "" {
		stdlog.Warnf("set default separator of text spliter '%s'", defaultSeparator)
		s.Separator = defaultSeparator
	} else {
		sep, err := strconv.Unquote(`"` + s.Separator + `"`)
		if err != nil {
			return stderr.Wrap(err, "unquote Separator %s", s.Separator)
		}
		stdlog.Debugf("Separator '%s' has been unquoted to '%s'", s.Separator, sep)
		s.Separator = sep
	}
	if s.ChunkSize <= minChunkSize {
		stdlog.Warnf("set default chunk size of text splitter '%d', cause giving chunk size less than minimum: %d", defaultChunkSize, minChunkSize)
		s.ChunkSize = defaultChunkSize
	}
	return nil
}

func (n *chainnode) TextSplit() *TextSplitNode {
	f := newTextSplitNode()
	n.linkChild(f)
	return f
}

func (n *TextSplitNode) Meta() models.NodeMeta {
	return n.NodeMeta
}
