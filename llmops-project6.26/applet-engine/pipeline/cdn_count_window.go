package pipeline

import "transwarp.io/applied-ai/applet-engine/models"

type CountWindowNode struct {
	chainnode `json:"-"`
	Size      int64 `json:"size"`  // 缓存的数据窗口长度
	Every     int64 `json:"every"` // 向下发送数据的间隔条数
	models.NodeMeta
}

func newCountWindowNode() *CountWindowNode {
	return &CountWindowNode{
		chainnode: newBasicChainNode("countWindow", StreamEdge, BatchEdge),
	}
}

func (c *CountWindowNode) Meta() models.NodeMeta {
	return c.NodeMeta
}

func (n *CountWindowNode) validate() error {
	return n.NodeMeta.Validate()
}
