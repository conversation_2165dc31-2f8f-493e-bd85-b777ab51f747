package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-engine/models"
)

// ConditionalSwitchNode
//
//	stream
//		|conditionalSwitch()
//			.condition('input == 1')

type ConditionalSwitchNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params       string                               `json:"params"`
	ParsedParams engine.WidgetParamsConditionalSwitch `json:"parsed_params"`
}

func newConditionalSwitchNode() *ConditionalSwitchNode {
	return &ConditionalSwitchNode{chainnode: newBasicChainNode("conditionalSwitch", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *ConditionalSwitchNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Errorf("Params of %s node is empty", n.Name())
	}

	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	return nil
}

func (n *ConditionalSwitchNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) ConditionalSwitch() *ConditionalSwitchNode {
	f := newConditionalSwitchNode()
	n.linkChild(f)
	return f
}
