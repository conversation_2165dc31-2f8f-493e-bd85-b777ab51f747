package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
)

// QuestionClassifierNode
//
//	stream
//	    |questionClassifier()
type QuestionClassifierNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`
	// 内部变量，解析Params值
	ParsedParams engine.WidgetParamsQuestionClassifier
}

func newQuestionClassifierNode() *QuestionClassifierNode {
	return &QuestionClassifierNode{chainnode: newBasicChainNode("questionClassifier", StreamEdge, StreamEdge)}
}

// TODO 每个算子的validate部分,存在部分相同的逻辑,可否优化 或者提供一个算子的模板,简化开发
func (n *QuestionClassifierNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Errorf("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}

	defaultNums := 0
	exist := make(map[string]bool)
	for _, c := range n.ParsedParams.Categories {
		if exist[c.CategoryName] {
			return stderr.Errorf("category name[%s] is replicated", c.CategoryName)
		}
		exist[c.CategoryName] = true
		if c.IsDefault {
			defaultNums++
		}
	}

	if defaultNums != 1 {
		return stderr.Errorf("the nums of default category must be 1")
	}
	return nil
}

func (n *QuestionClassifierNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) QuestionClassifier() *QuestionClassifierNode {
	f := newQuestionClassifierNode()
	n.linkChild(f)
	return f
}
