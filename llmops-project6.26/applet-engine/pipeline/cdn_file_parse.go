package pipeline

// import (
// 	"transwarp.io/applied-ai/applet-engine/models"
// )

// // FileParseNode 读取文件内容
// // |fileParse()
// //
// //	// 必填， 用于多个输入算子的情况下，标识数据来源，过滤不感兴趣字段
// //	.nodeID('WidgetKeyFileParse')
// type FileParseNode struct {
// 	chainnode `json:"-"`
// 	models.NodeMeta
// }

// func newFileParseNode() *FileParseNode {
// 	return &FileParseNode{chainnode: newBasicChainNode("fileParse", StreamEdge, StreamEdge)}
// }

// // validate 判断算子初始化的一些属性是否合法
// func (s *FileParseNode) validate() error {
// 	return s.NodeMeta.Validate()
// }

// func (n *FileParseNode) Meta() models.NodeMeta {
// 	return n.NodeMeta
// }

// func (n *chainnode) FileParse() *FileParseNode {
// 	f := newFileParseNode()
// 	n.linkChild(f)
// 	return f
// }
