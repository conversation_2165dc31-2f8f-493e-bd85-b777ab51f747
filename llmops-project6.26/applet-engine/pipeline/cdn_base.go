package pipeline

import (
	"fmt"
	url_ "net/url"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

const DELIMITER = "##"

type inputnode struct {
	InputKey string `json:"input_key"` // 提取的数据字段，默认为 TextInput/JsonInput/FileInput/ChatInput
}

func (n *inputnode) validate() error {
	return nil
}

func (n *inputnode) InputFieldKey(Id string) string {
	return fmt.Sprintf("%s%s%s", Id, DELIMITER, n.InputKey)
}

// checkUrlConn 检测url是否可以连接
//   - url: 运行代码的url, egg: https://127.0.0.1:8080
func checkUrlConn(url string) (bool, string) {
	parsedUrl, err := url_.Parse(url)
	if err != nil {
		return false, fmt.Sprintf("parse url failed: %v", err)
	}
	// 补全省略的80端口
	if !strings.Contains(parsedUrl.Host, ":") {
		parsedUrl.Host = parsedUrl.Host + ":80"
	}
	if utils.GetConnStatus(parsedUrl.Host) != utils.ConnectSucceed {
		return false, fmt.Sprintf("status of connection %s is not succeeded", parsedUrl.Host)
	}
	return true, ""
}

func checkUrlHealth(serviceID, serviceName, url string) health.ServiceHealth {
	healthy, detail := checkUrlConn(url)
	serviceHealth := health.ServiceHealth{
		ID:      serviceID,
		Name:    serviceName,
		Healthy: healthy,
		Detail:  detail,
	}
	return serviceHealth
}
