package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
)

// JsonnetNode
//
//	stream
//	    |jsonnet()
type JsonnetNode struct {
	chainnode `json:"-"`
	Code      string `json:"code"`
	models.NodeMeta
}

func newJsonnetNode() *JsonnetNode {
	return &JsonnetNode{chainnode: newBasicChainNode("jsonnet", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (j *JsonnetNode) validate() error {
	if err := j.NodeMeta.Validate(); err != nil {
		return err
	}
	if j.Code == "" {
		return stderr.InvalidParam.Error("code field is unspecified")
	}
	return nil
}

func (n *JsonnetNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) Jsonnet() *JsonnetNode {
	f := newJsonnetNode()
	n.linkChild(f)
	return f
}
