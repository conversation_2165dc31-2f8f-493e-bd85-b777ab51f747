package pipeline

import (
	"encoding/json"
	"fmt"
	"net/url"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/models"
)

// AgentAssistantNode
//
//	stream
//	    |agentAssistant()
type AgentAssistantNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params           string
	MaxThoughtRounds int64
	// 内部变量，解析Params值
	AgentConfig *agent_definition.LLMAgentConfig
}

func newAgentAssistantNode() *AgentAssistantNode {
	return &AgentAssistantNode{chainnode: newBasicChainNode("agentAssistant", StreamEdge, StreamEdge)}
}

func (n *AgentAssistantNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(n.Params), &n.AgentConfig); err != nil {
		return stderr.Wrap(err, "failed to parse agent params")
	}

	if n.AgentConfig == nil {
		return stderr.InvalidParam.Errorf("agent config is nil")
	}
	if n.AgentConfig.AgentMode == "" {
		n.AgentConfig.AgentMode = agent_definition.AgentModeReAct
		stdlog.Warnf("agent mode is empty, use default mode: %s", n.AgentConfig.AgentMode)
	}
	// validate llm model service
	llmModelSvc := n.AgentConfig.LLMModelSvc
	if llmModelSvc == nil {
		return stderr.InvalidParam.Errorf("llm model service is nil")
	}
	if llmModelSvc.FullUrl == "" {
		return stderr.InvalidParam.Errorf("llm model service full url is empty")
	}
	if _, err := url.Parse(llmModelSvc.FullUrl); err != nil {
		return stderr.InvalidParam.Errorf("llm model service full url is invalid")
	}
	if llmModelSvc.Host == "" {
		return stderr.InvalidParam.Errorf("llm model service host is empty")
	}
	if llmModelSvc.Schema != pb.ModelServiceSchema_MODEL_SERVICE_SCHEMA_HTTP {
		if llmModelSvc.Port <= 0 || llmModelSvc.Port > 65535 {
			return stderr.InvalidParam.Errorf("llm model service port is invalid")
		}
	}

	return nil
}

func (n *AgentAssistantNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *AgentAssistantNode) GetHealth() []health.ServiceHealth {

	llmModelService := n.AgentConfig.LLMModelSvc
	llmModelServiceName := "LLMModelService"
	llmModelServiceID := llmModelService.Id

	healthy, detail := clients2.CheckModelConn(llmModelService)
	serviceHealth := health.ServiceHealth{
		ID:      llmModelServiceID,
		Name:    llmModelServiceName,
		Healthy: healthy,
		Detail:  stdsrv.AnyToString(detail),
	}
	healths := []health.ServiceHealth{serviceHealth}

	// TODO: check health with tool describer inferface
	// check api tool health
	for _, collection := range n.AgentConfig.APICollections {
		if collection == nil {
			continue
		}
		for _, apiTool := range collection.AgentTools {
			apiToolDefinition := apiTool.Definition()
			serviceName := apiToolDefinition.NameForHuman
			serviceID := apiToolDefinition.ID
			errorHealth := health.ServiceHealth{
				ID:      serviceID,
				Name:    serviceName,
				Healthy: false,
				Detail:  "",
			}
			apiToolHealth, err := clients.ApiToolExecutor.CheckHealth(apiTool)
			if err != nil {
				errorHealth.Detail = fmt.Sprintf("Failed to check api tool health: %v", err)
				healths = append(healths, errorHealth)
				continue
			}
			healths = append(healths, apiToolHealth)
		}
	}

	// check knowledge tool health
	for _, knowTool := range n.AgentConfig.KnowledgeBases.KnowledgeBaseDesc {
		if knowTool == nil {
			continue
		}
		knowToolDefinition := knowTool.Definition()
		serviceName := knowToolDefinition.NameForHuman
		serviceID := knowToolDefinition.ID
		errorHealth := health.ServiceHealth{
			ID:      serviceID,
			Name:    serviceName,
			Healthy: false,
			Detail:  "",
		}
		knowToolHealth, err := clients.ApiToolExecutor.CheckHealth(knowTool)
		if err != nil {
			errorHealth.Detail = fmt.Sprintf("Failed to check knowledge tool health: %v", err)
			healths = append(healths, errorHealth)
			continue
		}
		healths = append(healths, knowToolHealth)
	}

	// check model tool health
	for _, systemService := range n.AgentConfig.SystemServices {
		// if systemService.ServiceType == agent_definition.ServiceTypeEnumModel &&
		if systemService.ModelService != nil {
			modelTool := systemService.ModelService
			modelToolDefinition := modelTool.Definition()
			serviceName := modelToolDefinition.NameForHuman
			serviceID := modelToolDefinition.ID
			errorHealth := health.ServiceHealth{
				ID:      serviceID,
				Name:    serviceName,
				Healthy: false,
				Detail:  "",
			}
			modelToolHealth, err := clients.ModelToolExecutor.CheckHealth(modelTool)
			if err != nil {
				errorHealth.Detail = fmt.Sprintf("Failed to check model tool health: %v", err)
				healths = append(healths, errorHealth)
				continue
			}
			healths = append(healths, modelToolHealth)
		}
	}
	return healths
}

func (n *chainnode) AgentAssistant() *AgentAssistantNode {
	f := newAgentAssistantNode()
	n.linkChild(f)
	return f
}
