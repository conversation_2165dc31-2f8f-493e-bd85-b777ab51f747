package pipeline

import (
	"net/url"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/tools/vecdb"
)

// VecInsertNode
//
//	stream
//	    |vecInsert()
type VecInsertNode struct {
	chainnode  `json:"-"`
	Url        string `json:"url"`        // 向量数据库地址 ['mem', 'milvus://*************:19530', 'hippo://*************:19530', 'file:///tmp/temp_vec.db']
	Database   string `json:"database"`   // 向量数据库DB
	Collection string `json:"collection"` // 向量数据库DB

	models.NodeMeta
	U *url.URL
}

func newVecInsertNode() *VecInsertNode {
	return &VecInsertNode{
		chainnode: newBasicChainNode("vecInsert", StreamEdge, StreamEdge),
	}
}

// validate 判断算子初始化的一些属性是否合法
func (s *VecInsertNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	u, err := vecdb.ValidURL(s.Url)
	if err != nil {
		return stderr.Wrap(err, "invalid vector db url: %s", s.Url)
	}
	s.U = u
	return nil
}

func (n *VecInsertNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) VecInsert() *VecInsertNode {
	f := newVecInsertNode()
	n.linkChild(f)
	return f
}

func (n *VecInsertNode) GetHealth() []health.ServiceHealth {
	serviceName := "VecInsert"
	serviceID := n.Name() + "-" + serviceName
	// TODO: 不校验 mem 与 file
	serviceHealth := checkUrlHealth(serviceID, serviceName, n.Url)
	return []health.ServiceHealth{serviceHealth}
}
