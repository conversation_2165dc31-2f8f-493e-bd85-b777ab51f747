package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// JsonInputNode
//
//	stream
//	    |jsonInput()
type JsonInputNode struct {
	chainnode `json:"-"`
	inputnode
	models.NodeMeta
}

func newJsonInputNode() *JsonInputNode {
	return &JsonInputNode{chainnode: newBasicChainNode("jsonInput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *JsonInputNode) validate() error {
	if err := s.inputnode.validate(); err != nil {
		return err
	}
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	if s.InputKey == "" {
		stdlog.Warnf("using the default value of InputKey of text input node: %s", models.PredefinedFieldJsonInput)
		s.InputKey = models.PredefinedFieldJsonInput
	}
	return nil
}

func (n *JsonInputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) JsonInput() *JsonInputNode {
	f := newJsonInputNode()
	n.linkChild(f)
	return f
}
