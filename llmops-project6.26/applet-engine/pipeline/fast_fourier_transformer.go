package pipeline

import (
	"encoding/json"
	"fmt"
)

// An FastFourierTransformerNode will
// 		take the incoming 【time domain data set】 and
// 		return 【frequency domain data set】
// 		using fast fourier transform.
//
// Example:
//    stream
//        |window()
//            .period(10m)
//			  .every(5m)
//        |fastFourierTransformer()
//			  .field('field')
//			  .inverse(false)

type FastFourierTransformerNode struct {
	// If the new node have its successor node, we should compose a chainnode to send data to its successor node
	chainnode

	// Specify field from its upstream node to do FFT operation
	// This property can't be took from its upstream node, so we must get it manually or by default
	// tick:ignore
	Field string `json:"field"`

	//// The incoming time domain data set
	//// This property can be took from its upstream node
	//// tick:ignore
	//Inset []float64 `json:"inset"`

	// If inverse is true, fft returns the discrete Fourier transform of Inset or the (normalised) inverse transform
	// This property can't be took from its upstream node, so we must get it manually or by default
	// tick:ignore
	Inverse int64 `json:"inverse"`
}

// Create a new FastFourierTransformerNode that accepts any edge type and an integer type data
// Inverse can't be took from its upstream node, so we must put explicitly it to node, and lowercase its initial
func newFastFourierTransformerNode(wants EdgeType, field string, inverse int64) *FastFourierTransformerNode {
	return &FastFourierTransformerNode{
		chainnode: newBasicChainNode("fastFourierTransformer", wants, wants),
		Field:     field,
		Inverse:   inverse,
	}
}

// MarshalJSON converts current FastFourierTransformerNode to JSON
// tick:ignore
func (n *FastFourierTransformerNode) MarshalJSON() ([]byte, error) {
	type Alias FastFourierTransformerNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		TypeOf: TypeOf{
			Type: "fastFourierTransformer",
			ID:   n.ID(),
		},
		Alias: (*Alias)(n),
	}
	return json.Marshal(raw)
}

// UnmarshalJSON converts JSON to a FastFourierTransformerNode
func (n *FastFourierTransformerNode) UnmarshalJSON(data []byte) error {
	type Alias FastFourierTransformerNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		Alias: (*Alias)(n),
	}
	err := json.Unmarshal(data, raw)
	if err != nil {
		return err
	}
	if raw.Type != "fastFourierTransformer" {
		return fmt.Errorf("error unmarshaling node %d of type %s as FastFourierTransformerNode", raw.ID, raw.Type)
	}
	n.setID(raw.ID)
	return nil
}

// tick:ignore
func (n *FastFourierTransformerNode) validate() error {
	return nil
}
