package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/models"
)

type ChunksOutputNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params       string                           `json:"params"`
	WidgetParams widgets.WidgetParamsChunksOutPut `json:"widget_params"`
}

func newChunksOutputNode() *ChunksOutputNode {
	return &ChunksOutputNode{chainnode: newBasicChainNode("ChunksOutput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *ChunksOutputNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Errorf("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(n.Params), &n.WidgetParams); err != nil {
		return stderr.Wrap(err, "failed to parse widget params")
	}
	if n.WidgetParams.MaxSplitLen <= 0 {
		n.WidgetParams.MaxSplitLen = widgets.DefaultValueMaxSplitLen
	}
	return nil
}

func (n *ChunksOutputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) ChunksOutput() *ChunksOutputNode {
	f := newChunksOutputNode()
	n.linkChild(f)
	return f
}

type QuestionClassifierOutPutNode struct {
	chainnode `json:"-"`
	models.NodeMeta
}

func newQuestionClassifierOutPut() *QuestionClassifierOutPutNode {
	return &QuestionClassifierOutPutNode{chainnode: newBasicChainNode("QuestionClassifierOutPutNode", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *QuestionClassifierOutPutNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	return nil
}

func (n *QuestionClassifierOutPutNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) QuestionClassifierOutPut() *QuestionClassifierOutPutNode {
	f := newQuestionClassifierOutPut()
	n.linkChild(f)
	return f
}
