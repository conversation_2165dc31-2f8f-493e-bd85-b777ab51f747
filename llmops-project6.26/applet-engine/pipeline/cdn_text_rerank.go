package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/models"
)

type TextRerankNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string
	// 内部变量，解析Params值
	WidgetParamsTextRerank widgets.WidgetParamsTextRerank
}

func newTextRerankNode() *TextRerankNode {
	return &TextRerankNode{chainnode: newBasicChainNode("textRerank", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *TextRerankNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(n.Params), &n.WidgetParamsTextRerank); err != nil {
		return stderr.Wrap(err, "failed to parse TextEnhanceNode params")
	}

	return nil
}

func (n *TextRerankNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) TextRerank() *TextRerankNode {
	f := newTextRerankNode()
	n.linkChild(f)
	return f
}
