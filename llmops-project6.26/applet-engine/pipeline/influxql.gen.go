// Generated by tmpl
// https://github.com/benbjohnson/tmpl
//
// DO NOT EDIT!
// Source: influxql.gen.go.tmpl

package pipeline

import "github.com/influxdata/influxdb/influxql"

//tick:ignore
type ReduceCreater struct {
	CreateFloatReducer func() (influxql.FloatPointAggregator, influxql.FloatPointEmitter)

	CreateFloatIntegerReducer func() (influxql.FloatPointAggregator, influxql.IntegerPointEmitter)

	CreateFloatStringReducer func() (influxql.FloatPointAggregator, influxql.StringPointEmitter)

	CreateFloatBooleanReducer func() (influxql.FloatPointAggregator, influxql.BooleanPointEmitter)

	CreateIntegerFloatReducer func() (influxql.IntegerPointAggregator, influxql.FloatPointEmitter)

	CreateIntegerReducer func() (influxql.IntegerPointAggregator, influxql.IntegerPointEmitter)

	CreateIntegerStringReducer func() (influxql.IntegerPointAggregator, influxql.StringPointEmitter)

	CreateIntegerBooleanReducer func() (influxql.IntegerPointAggregator, influxql.BooleanPointEmitter)

	CreateStringFloatReducer func() (influxql.StringPointAggregator, influxql.FloatPointEmitter)

	CreateStringIntegerReducer func() (influxql.StringPointAggregator, influxql.IntegerPointEmitter)

	CreateStringReducer func() (influxql.StringPointAggregator, influxql.StringPointEmitter)

	CreateStringBooleanReducer func() (influxql.StringPointAggregator, influxql.BooleanPointEmitter)

	CreateBooleanFloatReducer func() (influxql.BooleanPointAggregator, influxql.FloatPointEmitter)

	CreateBooleanIntegerReducer func() (influxql.BooleanPointAggregator, influxql.IntegerPointEmitter)

	CreateBooleanStringReducer func() (influxql.BooleanPointAggregator, influxql.StringPointEmitter)

	CreateBooleanReducer func() (influxql.BooleanPointAggregator, influxql.BooleanPointEmitter)

	TopBottomCallInfo      *TopBottomCallInfo
	IsSimpleSelector       bool
	IsStreamTransformation bool
	IsEmptyOK              bool
}
