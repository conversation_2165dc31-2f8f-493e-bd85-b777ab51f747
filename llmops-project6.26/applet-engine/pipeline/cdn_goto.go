package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-engine/models"
)

// GotoNode
//
//	stream
//	    |goto()
type GotoNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	// 内部变量
	Params       string `json:"params"`
	WidgetParams engine.WidgetParamsGoto
}

func newGotoNode() *GotoNode {
	return &GotoNode{chainnode: newBasicChainNode("goto", StreamEdge, StreamEdge)}
}

func (n *GotoNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}

	if n.Params == "" {
		return stderr.InvalidParam.Errorf("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.WidgetParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}

	if len(n.WidgetParams.TargetNodes) == 0 {
		return stderr.InvalidParam.Errorf("target nodes of %s node is empty", n.Name())
	}
	if n.WidgetParams.MaxLoopRounds <= 0 {
		return stderr.InvalidParam.Errorf("max loop rounds of %s is %d, must greater than 0", n.Name(), n.WidgetParams.MaxLoopRounds)
	}
	return nil
}

func (n *GotoNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) Goto() *GotoNode {
	f := newGotoNode()
	n.linkChild(f)
	return f
}
