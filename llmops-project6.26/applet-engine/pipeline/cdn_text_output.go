package pipeline

import (
	"transwarp.io/applied-ai/applet-engine/models"
)

type TextOutputNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	StdText string
}

func newTextOutputNode() *TextOutputNode {
	return &TextOutputNode{chainnode: newBasicChainNode("textOutput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *TextOutputNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	return nil
}

func (n *TextOutputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) TextOutput() *TextOutputNode {
	f := newTextOutputNode()
	n.linkChild(f)
	return f
}
