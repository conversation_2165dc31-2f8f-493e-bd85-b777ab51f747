package pipeline

import (
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"
)

const DEFAULT_HTTP_RSP_NODE_NAME = "http_rsp"

// A HttpRspNode selects a subset of the data flowing through a StreamNode.
// The stream node allows you to select which portion of the stream you want to process.
//
// Example:
//
//			stream
//			    |listenHttp()
//			       .port('')
//			       .path('/')
//		       	   .handler('default')
//	         |httpRsp()
type HttpRspNode struct {
	chainnode `json:"-"`
	models.NodeMeta
}

func newHttpRspNode() *HttpRspNode {
	return &HttpRspNode{
		chainnode: newBasicChainNode("httpRsp", StreamEdge, StreamEdge),
	}
}

func (n *HttpRspNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (s *HttpRspNode) validate() error {
	// 初始化NodeMeta
	if s.Id == "" {
		s.Id = uuid.New().String()
	}
	if s.NodeID == "" {
		s.NodeID = uuid.New().String()
	}
	s.NodeName = DEFAULT_HTTP_RSP_NODE_NAME
	s.InnerNode = true
	return nil
}
