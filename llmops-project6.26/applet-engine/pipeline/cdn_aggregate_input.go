package pipeline

import "transwarp.io/applied-ai/applet-engine/models"

// AggregateInputNode
//
//	stream
//	    |aggregateInput()
type AggregateInputNode struct {
	chainnode `json:"-"`
	models.NodeMeta
}

func newAggregateInputNode() *AggregateInputNode {
	return &AggregateInputNode{chainnode: newBasicChainNode("aggregateInput", StreamEdge, StreamEdge)}
}

func (n *AggregateInputNode) validate() error {
	return n.NodeMeta.Validate()
}

func (n *AggregateInputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) AggregateInput() *AggregateInputNode {
	f := newAggregateInputNode()
	n.linkChild(f)
	return f
}
