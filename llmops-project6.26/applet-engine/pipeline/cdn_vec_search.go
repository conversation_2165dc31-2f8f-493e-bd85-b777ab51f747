package pipeline

import (
	"net/url"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/tools/vecdb"
)

// VecSearchNode
//
//	stream|vecSearch()
//			.dbType('mem')
//			.database('test_db')
//			.url('')
//			.topK(10)
//			.threshold(0.5)
type VecSearchNode struct {
	chainnode `json:"-"`
	Url       string  `json:"url"`       // 向量数据库地址 ['mem', 'milvus://*************:19530', 'hippo://*************:19530', 'file:///tmp/temp_vec.db']
	Database  string  `json:"database"`  // 向量数据库DB
	TopK      int64   `json:"top_k"`     // 向量召回最大数量 [1, 100], default: 5
	Threshold float64 `json:"threshold"` // 向量相似度阈值，仅返回大于该值的向量
	models.NodeMeta
	U *url.URL `json:"-"`
}

func newVecSearchNode() *VecSearchNode {
	return &VecSearchNode{
		chainnode: newBasicChainNode("vecSearch", StreamEdge, StreamEdge),
	}
}

// validate 判断算子初始化的一些属性是否合法
func (s *VecSearchNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	u, err := vecdb.ValidURL(s.Url)
	if err != nil {
		return stderr.Wrap(err, "invalid vector db url: %s ", s.Url)
	}
	s.U = u
	if s.TopK == 0 {
		s.TopK = 1
	}
	if s.TopK > 100 || s.TopK <= 0 {
		return stderr.InvalidParam.Error("top k exceed should between [1, 100]")
	}
	if s.Database == "" {
		s.Database = "default"
	}

	if s.Threshold < 0 || s.Threshold > 1 {
		return stderr.InvalidParam.Error("threshold should between [0.0, 1.0]")
	}
	return nil
}

func (n *VecSearchNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) VecSearch() *VecSearchNode {
	f := newVecSearchNode()
	n.linkChild(f)
	return f
}

func (n *VecSearchNode) GetHealth() []health.ServiceHealth {
	serviceName := "VecSearch"
	serviceID := n.Name() + "-" + serviceName
	// TODO: 不校验 mem 与 file
	serviceHealth := checkUrlHealth(serviceID, serviceName, n.Url)
	return []health.ServiceHealth{serviceHealth}
}
