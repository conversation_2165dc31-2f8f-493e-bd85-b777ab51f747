package pipeline

import (
	"encoding/json"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/tools/http"
)

type HTTPCallNode struct {
	chainnode
	Url         string        `json:"url"`
	Method      string        `json:"method"`
	Timeout     time.Duration `json:"timeout"`
	Header      string        `json:"header"`
	ServiceType string        `json:"service_type"`
	Escape      bool          `json:"escape"` // 是否向后透传,目前类型为BingSearch才会处理
	Body        string        `json:"body"`
	models.NodeMeta

	DocLoadRawReq widgets.DocLoadRawReqMerge `json:"doc_load_raw_req"`

	// 内部变量
	// TODO: 相同key有多个value，用英文逗号","分隔，暂未添加对多个value的支持，未测试直接用多个逗号分隔的value发送http请求
	HeaderMap map[string]string
}

func newHTTPCallNode() *HTTPCallNode {
	return &HTTPCallNode{chainnode: newBasicChainNode("httpCall", StreamEdge, StreamEdge)}
}

func (h *HTTPCallNode) validate() error {
	if err := h.NodeMeta.Validate(); err != nil {
		return err
	}
	// validate url
	if h.Url == "" {
		return stderr.InvalidParam.Error("url field is unspecified")
	}
	// validate method
	if h.Method == "" {
		return stderr.InvalidParam.Error("method field is unspecified")
	}
	if !http.AllowedMethodSet[h.Method] {
		return stderr.InvalidParam.Error("only support GET POST PUT DELETE method")
	}
	// validate header
	if h.Header == "" {
		h.HeaderMap = http.DefaultHeader
		stdlog.Warnf("header is null, using the default value of header %s", http.DefaultHeader)
	} else {
		headerMap := make(map[string]string, 0)
		if err := json.Unmarshal([]byte(h.Header), &headerMap); err != nil {
			return stderr.Wrap(err, "unmarshal %s, header must be json string of map[string]string", h.Header)
		}
		h.HeaderMap = headerMap
	}
	if h.Body != "" {
		switch h.ServiceType {
		case string(script.HTTPCallServiceTypeTextParse):
			if err := json.Unmarshal([]byte(h.Body), &h.DocLoadRawReq); err != nil {
				return stderr.Wrap(err, "failed to parse Body of %s", h.NodeName)
			}
		}
	}
	return nil
}

func (n *HTTPCallNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) HttpCall() *HTTPCallNode {
	f := newHTTPCallNode()
	n.linkChild(f)
	return f
}

func (n *HTTPCallNode) GetHealth() []health.ServiceHealth {
	serviceName := "HttpCall"
	serviceID := n.Name() + "-" + serviceName
	serviceHealth := checkUrlHealth(serviceID, serviceName, n.Url)
	return []health.ServiceHealth{serviceHealth}
}
