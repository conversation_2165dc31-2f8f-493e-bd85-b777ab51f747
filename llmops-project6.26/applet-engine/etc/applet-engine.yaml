# 是否开启 EdgeDebug 模式
edge_debug: "true"

# 应用发布时,由backend添加对应的环境变量
tenant:
  tenant_id: ""
  project_id: ""
  #为发布后的服务 记录系统服务所处的命名空间
  system_namespace: ""

sfs:
  enable: true
  local_root: /sfs

backend_task:
  run_code_url: "http://autocv-testbed-service"
#  run_code_url: "http://**************:32421"
  security_censor_url: "http://autocv-applet-guardrails-service:8008"

# 通过mqtt转发

transport:
  type: redis

#mqtt:
#  broker_addr: "127.0.0.1:31883"
#  qos: 2
#  conn_timeout: 3s
#  persistent_session: true
#  store: "data/mqtt/local/"

redis:
  addrs: "autocv-redis-service:6379"
  username: "default"
  password: "Warp!CV@2022#"
  database: "0"
  masterName: ""


agent_tool_executor:
  knowledge:
    host: autocv-applet-service
    port: 31080
  applet:
    #工具-应用链服务
    mlops_config:
#      host: "**************"
#      port: "32340"
#      istio_gateway_addr: "http://**************:31380"
      host: "llmops-sophon-serving"  #部署时加.ns
      port: "8752"
      istio_gateway_addr: "http://istio-ingressgateway.istio-system"
    app_svc_config:
      default_inner_port: 1884
      default_api_path: "/api/v1"
      default_health_api_path: "/api/v1/health"


milvus:
  server:
    host: *************
    port: 19530
  options:
    collection: applet_engine
    partition: default
    dimension:  1024
    data_storage_path: ./data

citation:
  min_paragraph_length: 15
  relevance_score_threshold: 0.9
  top_k: 3

# Agent 配置
agent:
  max_thought_rounds: 5  # 最大思考轮次
  re_act_detection_length: 50
applet_backend:
#  host: **************
#  port: 31057
  host: 127.0.0.1
  port: 30080
  http_timeout: 3600s
