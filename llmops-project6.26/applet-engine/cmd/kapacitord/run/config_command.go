package run

import (
	"flag"
	"fmt"
	"io"
	"os"

	"github.com/BurntSushi/toml"

	"transwarp.io/applied-ai/applet-engine/server"
)

// PrintConfigCommand represents the command executed by "kapacitord config".
type PrintConfigCommand struct {
	Stdin  io.Reader
	Stdout io.Writer
	Stderr io.Writer
}

// NewPrintConfigCommand return a new instance of PrintConfigCommand.
func NewPrintConfigCommand() *PrintConfigCommand {
	return &PrintConfigCommand{
		Stdin:  os.Stdin,
		Stdout: os.Stdout,
		Stderr: os.Stderr,
	}
}

// Run parses and prints the current config loaded.
func (cmd *PrintConfigCommand) Run(args ...string) error {
	// Parse command flags.
	fs := flag.NewFlagSet("", flag.ContinueOnError)
	configPath := fs.String("config", "", "")
	hostname := fs.String("hostname", "", "")
	fs.Usage = func() { fmt.Fprintln(cmd.Stderr, printConfigUsage) }
	if err := fs.Parse(args); err != nil {
		return err
	}

	// Parse config from path.
	config, err := server.InitConfig(*configPath)
	if err != nil {
		return fmt.Errorf("parse config: %s", err)
	}

	// Override config properties.
	if *hostname != "" {
		config.Hostname = *hostname
	}

	_ = toml.NewEncoder(cmd.Stdout).Encode(config)
	_, _ = fmt.Fprint(cmd.Stdout, "\n")

	return nil
}

var printConfigUsage = `usage: config

	config displays the default configuration
`
