package clients

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-engine/conf"
)

// import (
//
//	"crypto/tls"
//	"fmt"
//	docker "github.com/docker/docker/client"
//	httptransport "github.com/go-openapi/runtime/client"
//	"github.com/go-openapi/strfmt"
//	kpct "github.com/influxdata/kapacitor/client/v1"
//	edmc "github.com/thingio/edmc/client"
//	edmco "github.com/thingio/edmc/client/operations"
//	"net/http"
//	"strings"
//	"transwarp.io/applied-ai/aiot/vision-backend/conf"
//	"transwarp.io/applied-ai/aiot/vision-std/boot"
//	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
//	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
//	"transwarp.io/applied-ai/aiot/vision-std/license"
//	"transwarp.io/applied-ai/aiot/vision-std/srs"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
//	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
//	"transwarp.io/applied-ai/aiot/vision-std/stdstatus"
//	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
//	"transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"
//	"transwarp.io/applied-ai/aiot/vision-std/transport/mqtt"
//	mwc "transwarp.io/applied-ai/model-whs/mw-client/client"
//	"transwarp.io/applied-ai/model-whs/mw-client/client/operations"
//
// )
//
// const DefaultKapacitorAddr = "http://127.0.0.1:9092"

var (
	AppletBackendCli      *AppletBackendClient
	ApiToolExecutor       *agent_executor.ApiToolExecutor
	KnowledgeToolExecutor *agent_executor.KnowlHubExecutor
	ModelToolExecutor     *agent_executor.ModelToolExecutor
	AppletToolExecutor    *agent_executor.AppletServiceExecutor
	//MqCli                 tran2.MqClient
	// InfluxdbCli *influxdb.InfluxClient // 用于查询时序数据库中的数据
	// MilvusCli        *milvus.Client           // 用于存储、查询向量的数据库
	// DefaultMilvusCli *milvus.ConfiguredClient // 用于存储、查询向量的数据库, 以使用默认配置进行初始化
	// 	StatusMgr    stdstatus.StatusManager  // 状态管理组件, 提供可运行系统资源的统一收集与存储等
	// 	DockerCli    *docker.Client           // 与Docker守护进程通信, 用于镜像启动(boot)/ 镜像构建(faas)
	// 	K8sCli       *k8s.Client              // 与K8S API SERVER 通信
	// InfluxWriter influxdb.InfluxWriter // 高并发的时序数据库写入客户端
	// 	BootMgr      boot.Manager             // 管理边缘平台的容器
	// 	KapacitorCli *kpct.Client             // 用于与时序流处理引擎通信
	// 	Notifier     *stdstatus.MsgSender     // 用于向所有前端界面发送系统通知消息(右下角弹窗形式)
	// 	SrsCli       srs.MMGatewayAPI         // 用户和多媒体网关交互的客户端
	// 	MWhsCli      operations.ClientService // 模型仓库的客户端
	// 	EdmCli       edmco.ClientService      // edge-device-manager 的客户端
	// 	DLIEClients  DLIEClientCache          // Deep learning Inference Engine(Triton Inference Server)的客户端
	// 	ImageHubCli  dockerimage.ImageHubAPI  // Docker Registry 客户端
	// 	Verifier     *license.Verifier        // 与授权服务器交互的客户端
	//
	// OssgwCli   *minio.Client
	// RscHub     stdhub.ResourceHub
	// StatusMgr  stdstatus.StatusManager
	// Verifier   *license.Verifier
	// BootMgr    boot.Manager
	// SvcGrpcCli pb.ModelServiceManagerClient
	// 	HttpCli               *http.Client // 用于一般请求
	// 	HttpCliWithoutTimeout *http.Client // 用于进行文件传输等耗时较长的请求
	//
	// 	// 注意，初始化时各客户端之间存在依赖，因此需要按顺序进行。
	commonInitializers = []cliInitializer{
		initApiToolExecutor,
		initKnowledgeToolExecutor,
		initModelToolExecutor,
		initAppletToolExecutor,
		initBackendCli,
		// initMqCli,
		// initMilvusCli,
		// initDefaultMilvusCli,
		// initInfluxDBCli,
		// initOssgwCli,
		// initRscHubCli,
		// initStatusMgr,
		// initBootMgrCli,
		// initGrpcClients,
		// 		initNotifier,
		// 		initHttpCli,
		// 		initHttpCliWithoutTimeout,
		// 		initDockerCli,
		// 		initK8SCli,
		// 		initMwhCli,
		// 		initEdmCli,
		// 		initLicenseVerifier,
	}
	// 	edgeInitializers = []cliInitializer{
	// 		initInfluxDBCli,
	// 		initSrsCli,
	// 		initKapacitorCli,
	// 		initBootMgrCli,
	// 		initEdgeStatusMgr,
	// 		initTritonClients,
	// 		initImageHubCli,
	// 	}
)

type cliInitializer func() error

func Init() {
	if err := startCommonClients(); err != nil {
		panic(any(fmt.Errorf("failed to start common clients: %+v", err)))
	}
}

func startCommonClients() error {
	return initClients(commonInitializers)
}

func initClients(initializers []cliInitializer) error {
	for _, initializer := range initializers {
		name := strings.TrimPrefix(toolkit.GetFunctionName(initializer), "init")
		if err := initializer(); err != nil {
			stdlog.WithError(err).Errorf("failed to initialize %s", name)
			return stderr.Wrap(err, "failed to initialize %s", name)
		}
		stdlog.Infof("succeed to initialize %s", name)
	}
	return nil
}

type AppletBackendClient struct {
	HttpCli *http.Client
	Cfg     *conf.AppletBackendConfig
}

func initBackendCli() error {
	httpCli := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: conf.Config.AppletBackend.TimeOut * time.Second,
	}
	AppletBackendCli = &AppletBackendClient{
		HttpCli: httpCli,
		Cfg:     &conf.Config.AppletBackend,
	}

	stdlog.Infof("init http client success")
	return nil
}

func (a AppletBackendClient) CancelChat(ctx context.Context, chatID string, debugMsg []byte) error {
	var reqBody io.Reader
	reqBody = bytes.NewBufferString(string(debugMsg))
	url := fmt.Sprintf("http://%s:%s/%s", a.Cfg.Host, a.Cfg.Port, helper.GetCancelPath(chatID))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, reqBody)
	if err != nil {
		return stderr.Wrap(err, "新建请求体出错")
	}

	req.Header.Set("Content-Type", "application/json")

	q := req.URL.Query()
	project := helper.GetProjectID(ctx)

	if project != "" {
		q.Add("project_id", project)
	}
	req.URL.RawQuery = q.Encode()

	resp, err := a.HttpCli.Do(req)
	if err != nil {
		return stderr.Wrap(err, "request url :%v", url)
	}
	defer resp.Body.Close()

	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		return fmt.Errorf("illegal response: [%d] -> %s", resp.StatusCode, resp.Status)
	}

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return stderr.Wrap(err, "read response body err")
	}

	return nil
}

//func initMqCli() error {
//	switch conf.Config.Transport.Type {
//	case conf2.TypeMqtt:
//		mqttCfg := conf.Config.Mqtt
//		mqttCli := mqtt.NewMQTTClient(conf2.MqttConfig{
//			BrokerAddr:  mqttCfg.BrokerAddr,
//			ClientId:    fmt.Sprintf("applet-engine-%s-%s", mqttCfg.ClientId, time.Now().String()),
//			Qos:         conf.Config.Mqtt.Qos,
//			ConnTimeOut: time.Minute,
//		})
//		if err := mqttCli.Start(); err != nil {
//			return stderr.Wrap(err, "init mqtt client with config %+v", mqttCfg)
//		}
//		MqCli = mqttCli
//	case conf2.TypeRedis:
//		redisCfg := conf.Config.Redis
//		redisCli, err := redis.NewRedisMqClientV2(redisCfg)
//		if err != nil {
//			return stderr.Wrap(err, "init redisCli with config %+v", redisCfg)
//		}
//		MqCli = redisCli
//	}
//	return nil
//}

//func initMQTTCli() error {
//	mc := conf.Config.Mqtt
//	MqttCli = mqtt.NewMQTTClient(conf2.MqttConfig{
//		BrokerAddr:  mc.BrokerAddr,
//		ClientId:    fmt.Sprintf("applet-engine-%s-%s", mc.ClientId, time.Now().String()),
//		Qos:         mc.Qos,
//		ConnTimeOut: time.Minute,
//	})
//	if err := MqttCli.Start(); err != nil {
//		return stderr.Wrap(err, "init mqtt client with config %+v", mc)
//	}
//	return nil
//}

func initApiToolExecutor() (err error) {
	apiConf := conf.Config.AgentToolExecutor.Api
	if ApiToolExecutor, err = agent_executor.NewApiToolExecutor(apiConf); err != nil {
		return stderr.Wrap(err, "init api tool executor with config %+v", apiConf)
	}
	return nil
}

func initKnowledgeToolExecutor() (err error) {
	knowledgeConf := conf.Config.AgentToolExecutor.Knowledge
	if KnowledgeToolExecutor, err = agent_executor.NewKnowledgeToolExecutor(knowledgeConf); err != nil {
		return stderr.Wrap(err, "init knowledge tool executor with config %+v", knowledgeConf)
	}
	return nil
}

// initModelToolExecutor
func initModelToolExecutor() (err error) {
	modelConf := conf.Config.AgentToolExecutor.Model
	if ModelToolExecutor, err = agent_executor.NewModelToolExecutor(modelConf); err != nil {
		return stderr.Wrap(err, "init model tool executor with config %+v", modelConf)
	}
	return nil
}

func initAppletToolExecutor() (err error) {
	appletConfig := conf.Config.AgentToolExecutor.Applet
	if AppletToolExecutor, err = agent_executor.NewAppletToolExecutor(appletConfig); err != nil {
		return stderr.Wrap(err, "init model tool executor with config %+v", appletConfig)
	}
	return nil
}

// func initMilvusCli() error {
// 	c, err := milvus.NewMilvusClient(conf.Config.Milvus.Server)
// 	if err != nil {
// 		return stderr.Wrap(err, "init milvus client")
// 	}
// 	MilvusCli = c
// 	return nil
// }
// func initDefaultMilvusCli() error {
// 	c, err := MilvusCli.NewConfiguredClient(conf.Config.Milvus.Options)
// 	if err != nil {
// 		return stderr.Wrap(err, "init milvus client with default options")
// 	}
// 	if err := c.Init(); err != nil {
// 		return stderr.Wrap(err, "init milvus client with default options")
// 	}
// 	DefaultMilvusCli = c
// 	return nil
// }
