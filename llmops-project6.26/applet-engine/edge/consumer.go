package edge

import (
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

// Consumer reads messages off an edge and passes them to a receiver.
type Consumer interface {
	// Consume reads messages off an edge until the edge is closed or aborted.
	// An error is returned if either the edge or receiver errors.
	Consume() error
}

type PnMetaGetter interface {
	// PnMeta 返回底层node对应的 pipeline.Node 的Meta信息
	PnMeta() models.NodeMeta
}

// Receiver handles messages as they arrive via a consumer.
type Receiver interface {
	BeginBatch(begin BeginBatchMessage) error
	BatchPoint(bp BatchPointMessage) error
	EndBatch(end EndBatchMessage) error
	Point(p PointMessage) error
	Barrier(b BarrierMessage) error
	DeleteGroup(d DeleteGroupMessage) error

	// Done is called once the receiver will no longer receive any messages.
	Done()
	// PnMetaGetter
}

type consumer struct {
	edge Edge
	r    Receiver
}

// NewConsumerWithReceiver creates a new consumer for the edge e and receiver r.
func NewConsumerWithReceiver(e Edge, r Receiver) Consumer {
	return &consumer{
		edge: e,
		r:    r,
	}
}

func (ec *consumer) Consume() error {
	defer ec.r.Done()
	for msg, ok := ec.edge.Emit(); ok; msg, ok = ec.edge.Emit() {
		switch m := msg.(type) {
		case BeginBatchMessage:
			if err := ec.r.BeginBatch(m); err != nil {
				return err
			}
		case BatchPointMessage:
			if err := ec.r.BatchPoint(m); err != nil {
				return err
			}
		case EndBatchMessage:
			if err := ec.r.EndBatch(m); err != nil {
				return err
			}
		case BufferedBatchMessage:
			err := receiveBufferedBatch(ec.r, m)
			if err != nil {
				return err
			}
		case PointMessage:
			if err := ec.r.Point(m); err != nil {
				return err
			}
		case BarrierMessage:
			if err := ec.r.Barrier(m); err != nil {
				return err
			}
		default:
			return fmt.Errorf("unexpected message of type %T", msg)
		}
	}
	return nil
}

func receiveBufferedBatch(r Receiver, batch BufferedBatchMessage) error {
	b, ok := r.(BufferedReceiver)
	// If we have a buffered receiver pass the batch straight through.
	if ok {
		return b.BufferedBatch(batch)
	}

	// Pass the batch non buffered.
	if err := r.BeginBatch(batch.Begin()); err != nil {
		return err
	}
	for _, bp := range batch.Points() {
		if err := r.BatchPoint(bp); err != nil {
			return err
		}
	}
	return r.EndBatch(batch.End())
}

// commonReceiver
// 简化multiConsumer及consumer对象中对receivePointMessage方法的复用
// Receiver.Point(msg)
// MultiReceiver.Point(srcInt,msg)
type commonReceiver struct {
	SingleReceiver Receiver

	MultiReceiver       MultiReceiver
	MultiReceiverSrcInt int
}

func NewSingleReceiver(r Receiver) *commonReceiver {
	return &commonReceiver{SingleReceiver: r}
}
func NewMultiReceiver(mr MultiReceiver, srcInt int) *commonReceiver {
	return &commonReceiver{MultiReceiver: mr, MultiReceiverSrcInt: srcInt}
}
func (cr *commonReceiver) isSingleReceiver() bool {
	return cr.SingleReceiver != nil && cr.MultiReceiver == nil
}

func (cr *commonReceiver) isMultiReceiver() bool {
	return cr.MultiReceiver != nil && cr.SingleReceiver == nil
}

func (cr *commonReceiver) Point(msg PointMessage) (err error) {
	// TODO 移除各算子的point等方法中,对field的检查与logError
	defer func() { logError(msg, err) }()
	if err = checkFields(msg); err != nil {
		return err
	}
	if cr.isSingleReceiver() {
		err = cr.SingleReceiver.Point(msg)
		if err == nil {
			return nil
		}
		if pm, ok := cr.SingleReceiver.(PnMetaGetter); ok {
			return stderr.Wrap(err, "[%s]receive Point", pm.PnMeta().NodeName)
		}
		return stderr.Wrap(err, "use single receiver Point function to receive point message")

	} else {
		err = cr.MultiReceiver.Point(cr.MultiReceiverSrcInt, msg)
		if err == nil {
			return nil
		}
		if pm, ok := cr.MultiReceiver.(PnMetaGetter); ok {
			return stderr.Wrap(err, "[%s]receive Point", pm.PnMeta().NodeName)
		}
		return stderr.Wrap(err, "use multi receiver Point function to receive point message")
	}
	return nil
}

func (cr *commonReceiver) BeginBatch(msg BeginBatchMessage) (err error) {
	defer func() { logError(msg, err) }()
	if cr.isSingleReceiver() {
		return cr.SingleReceiver.BeginBatch(msg)
	}
	return cr.MultiReceiver.BeginBatch(cr.MultiReceiverSrcInt, msg)
}

func (cr *commonReceiver) BatchPoint(msg BatchPointMessage) (err error) {
	defer func() { logError(msg, err) }()
	if err = checkFields(msg); err != nil {
		return err
	}
	if cr.isSingleReceiver() {
		return cr.SingleReceiver.BatchPoint(msg)
	}
	return cr.MultiReceiver.BatchPoint(cr.MultiReceiverSrcInt, msg)
}

func (cr *commonReceiver) EndBatch(msg EndBatchMessage) (err error) {
	defer func() { logError(msg, err) }()
	if cr.isSingleReceiver() {
		return cr.SingleReceiver.EndBatch(msg)
	}
	return cr.MultiReceiver.EndBatch(cr.MultiReceiverSrcInt, msg)
}

type MultiReceiver interface {
	BufferedBatch(src int, batch BufferedBatchMessage) error
	BeginBatch(src int, begin BeginBatchMessage) error
	BatchPoint(src int, bp BatchPointMessage) error
	EndBatch(src int, end EndBatchMessage) error
	Point(src int, p PointMessage) error
	Barrier(src int, b BarrierMessage) error
	Finish() error
}

func NewMultiConsumerWithStats(ins []StatsEdge, r MultiReceiver, eh MsgErrHandler) Consumer {
	edges := make([]Edge, len(ins))
	for i := range ins {
		edges[i] = ins[i]
	}
	return NewMultiConsumer(edges, r, eh)
}

func NewMultiConsumer(ins []Edge, r MultiReceiver, eh MsgErrHandler) Consumer {
	return &multiConsumer{
		ins:      ins,
		r:        r,
		messages: make(chan srcMessage),
		eh:       eh,
	}
}

type multiConsumer struct {
	ins []Edge

	r MultiReceiver

	messages chan srcMessage
	eh       MsgErrHandler
}

type srcMessage struct {
	Src int
	Msg Message
}

func (c *multiConsumer) Consume() error {
	errC := make(chan error, len(c.ins))
	for i, in := range c.ins {
		go func(src int, in Edge) {
			errC <- c.readEdge(src, in)
		}(i, in)
	}

	firstErr := make(chan error, 1)
	go func() {
		for range c.ins {
			err := <-errC
			if err != nil {
				firstErr <- err
			}
		}
		// Close messages now that all readEdge goroutines have finished.
		close(c.messages)
	}()

LOOP:
	for {
		select {
		case err := <-firstErr:
			// One of the parents errored out, return the error.
			return err
		case m, ok := <-c.messages:
			if !ok {
				break LOOP
			}
			// FIXME: 目前JoinNode只能处理PointMessage，只记录了PointMessage的Trace
			var err error
			switch msg := m.Msg.(type) {
			case BufferedBatchMessage:
				err = c.r.BufferedBatch(m.Src, msg)
			case BarrierMessage:
				err = c.r.Barrier(m.Src, msg)
			case PointMessage:
				err = receivePointMessage(NewMultiReceiver(c.r, m.Src), msg)
			case BeginBatchMessage:
				err = receiveBeginBatchMessage(NewMultiReceiver(c.r, m.Src), msg)
			case BatchPointMessage:
				err = receiveBatchPointMessage(NewMultiReceiver(c.r, m.Src), msg)
			case EndBatchMessage:
				err = receiveEndBatchMessage(NewMultiReceiver(c.r, m.Src), msg)
			}
			if err != nil && c.eh != nil {
				err = handleMultiReceiverError(m.Msg, c.r, c.eh, err)
			}
			if err != nil {
				return err
			}
		}
	}

	return c.r.Finish()
}

func (c *multiConsumer) readEdge(src int, in Edge) error {
	for m, ok := in.Emit(); ok; m, ok = in.Emit() {
		c.messages <- srcMessage{
			Src: src,
			Msg: m,
		}
	}
	return nil
}

//func receivePointMessageWithMultiReceiver(r MultiReceiver, src int, msg PointMessage) error {
//	cpMsg, err := msg.DeepCopy()
//	if err != nil {
//		return stderr.Wrap(err, "deep copy point message")
//	}
//	if err := setPointMessageTrace(cpMsg); err != nil {
//		return stderr.Wrap(err, "set point message trace")
//	}
//	// 获取发送调试信息需要的参数
//	taskID, call, preNodeMeta, err := getContextProperty(msg)
//	if err != nil {
//		return stderr.Wrap(err, "get task id and call from point message")
//	}
//	preOutput, err := getPreOutput(msg)
//	if err != nil {
//		return stderr.Wrap(err, "get pre output from point message")
//	}
//	// 设置context中的meta为当前算子meta
//	curNodeMeta, err := getCurNodeMetaByMultiReceiver(r)
//	if err != nil {
//		return stderr.Wrap(err, "get cur node meta from receiver")
//	}
//	if err := cpMsg.SetMeta(curNodeMeta); err != nil {
//		return stderr.Wrap(err, "set meta of point message")
//	}
//	reqID := call.ReqID()
//	// 发送上一个算子的输出数据
//	if err := PubOutput(reqID, preNodeMeta, preOutput, debug.DEBUG_SCOPE_NODE, false); err != nil {
//		return stderr.Wrap(err, "publish output of pre node %s", preNodeMeta.NodeName)
//	}
//	// 处理子链，发送上一个子链的输出数据以及运行成功状态
//	preSubChainID := preNodeMeta.SubChainID
//	curSubChainID := curNodeMeta.SubChainID
//	// 上一个算子是子链内的算子，并且当前join算子不是子链内的算子，表示上一个子链结束
//	if preSubChainID != curSubChainID && preSubChainID != "" {
//		if err := PubOutput(reqID, preNodeMeta, preOutput, debug.DEBUG_SCOPE_SUB_CHAIN, false); err != nil {
//			return stderr.Wrap(err, "publish running status of pre sub chain %s", preNodeMeta.SubChainName)
//		}
//		if err := PubSuccessStatus(reqID, preNodeMeta, debug.DEBUG_SCOPE_SUB_CHAIN); err != nil {
//			return stderr.Wrap(err, "publish success status of pre sub chain %s", preNodeMeta.SubChainName)
//		}
//	}
//
//	callCtx := call.Ctx()
//	// 判断用户是否主动停止了请求
//	defer func() {
//		if err != nil && callCtx.Err() == context.Canceled {
//			err = context.Canceled
//		}
//	}()
//	// 判断ctx是否取消，上层函数会直接处理此错误，并把当前节点设置为failed或canceled状态
//	if call.Done() {
//		errMsg := fmt.Sprintf("request ctx is done before %s node starts", curNodeMeta.NodeName)
//		stdlog.Warnf(errMsg)
//		return stderr.Error(errMsg)
//	}
//	// join、union算子不存在作为子链开始算子的情况，无需判断当前子链开始的情况
//	// if preSubChainID != curSubChainID && curSubChainID != "" { pubRunningStatus pubInput }
//
//	// join算子是内部算子，不用发送debug信息，需要发送union算子debug信息
//	curInput := preOutput
//	// 发送当前算子的正在运行状态以及输入数据
//	if err := PubRunningStatus(taskID, call, curNodeMeta, debug.DEBUG_SCOPE_NODE); err != nil {
//		return stderr.Wrap(err, "publish running status of cur node %s", curNodeMeta.NodeName)
//	}
//
//	if err := PubInput(reqID, curNodeMeta, curInput, debug.DEBUG_SCOPE_NODE, false); err != nil {
//		return stderr.Wrap(err, "publish input of cur node %s", curNodeMeta.NodeName)
//	}
//	if err := r.Point(src, cpMsg); err != nil {
//		if pm, ok := r.(PnMetaGetter); ok {
//			return stderr.Wrap(err, "[%s]receive Point", pm.PnMeta().NodeName)
//		}
//		return stderr.Wrap(err, "use  multireceiver Point function to receive pointmessage")
//	}
//	// 发送当前算子的运行成功状态
//	if err := PubSuccessStatus(reqID, curNodeMeta, debug.DEBUG_SCOPE_NODE); err != nil {
//		return stderr.Wrap(err, "publish success status of cur node %s", curNodeMeta.NodeName)
//	}
//
//	return nil
//}

func getCurNodeMetaByMultiReceiver(r MultiReceiver) (models.NodeMeta, error) {
	node, ok := r.(pipeline.Node)
	if !ok {
		return models.NodeMeta{}, stderr.Error("multi receiver is not pipeline node")
	}
	return node.Meta(), nil
}

func handleMultiReceiverError(m Message, r MultiReceiver, eh MsgErrHandler, err error) error {
	meta, metaErr := getCurNodeMetaByMultiReceiver(r)
	if metaErr != nil {
		return stderr.Wrap(metaErr, "get receiver node meta")
	}
	return eh(m, meta, err)
}
