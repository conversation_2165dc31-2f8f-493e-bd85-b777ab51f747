package edge

import (
	"errors"
	"fmt"
	"github.com/sirupsen/logrus"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

// Edge represents the connection between two nodes that communicate via messages.
// Edge communication is unidirectional and asynchronous.
// Edges are safe for concurrent use.
type Edge interface {
	// Collect instructs the edge to accept a new message.
	Collect(Message) error
	// Emit blocks until a message is available and returns it or returns false if the edge has been closed or aborted.
	Emit() (Message, bool)
	// Close stops the edge, all messages currently buffered will be processed.
	// Future calls to Collect will panic.
	Close() error
	// Abort immediately stops the edge and all currently buffered messages are dropped.
	// Future calls to Collect return the error ErrAborted.
	Abort()
	// Type indicates whether the edge will emit stream or batch data.
	Type() pipeline.EdgeType
}

type edgeState int

const (
	edgeOpen edgeState = iota
	edgeClosed
	edgeAborted
)

// channelEdge is an implementation of Edge using channels.
type channelEdge struct {
	aborting chan struct{}
	messages chan Message

	typ pipeline.EdgeType

	mu    sync.Mutex
	state edgeState
}

// NewChannelEdge returns a new edge that uses channels as the underlying transport.
func NewChannelEdge(typ pipeline.EdgeType, size int) Edge {
	return newChannelEdge(typ, size)
}

func newChannelEdge(typ pipeline.EdgeType, size int) *channelEdge {
	return &channelEdge{
		aborting: make(chan struct{}),
		messages: make(chan Message, size),
		state:    edgeOpen,
		typ:      typ,
	}
}

func (e *channelEdge) Collect(m Message) error {
	select {
	case e.messages <- m:
		return nil
	case <-e.aborting:
		return ErrAborted
	}
}

func (e *channelEdge) Emit() (m Message, ok bool) {
	select {
	case m, ok = <-e.messages:
	case <-e.aborting:
	}
	return
}

func (e *channelEdge) Close() error {
	e.mu.Lock()
	defer e.mu.Unlock()
	if e.state != edgeOpen {
		return errors.New("edge not open cannot close")
	}
	close(e.messages)
	e.state = edgeClosed
	return nil
}

func (e *channelEdge) Abort() {
	e.mu.Lock()
	defer e.mu.Unlock()
	if e.state == edgeAborted {
		// nothing to do, already aborted
		return
	}
	close(e.aborting)
	e.state = edgeAborted
}

func (e *channelEdge) Type() pipeline.EdgeType {
	return e.typ
}

func NewDebugChannelEdge(taskName, parentName, childName string, t pipeline.EdgeType, size int) Edge {
	le := stdlog.WithFields("task", taskName, "parent", parentName, "child", childName)
	le.Level = logrus.DebugLevel
	return &debugChannelEdge{
		c:          newChannelEdge(t, size),
		Entry:      le,
		taskName:   taskName,
		parentName: parentName,
		childName:  childName,
		//mqCli:      clients.MqCli,
	}
}

type debugChannelEdge struct {
	//mqCli      transport.MqClient
	c          *channelEdge
	taskName   string
	parentName string
	childName  string
	*logrus.Entry
}

// Collect 用于从当前的 Edge 连接边中，将接收到的点位数据发送给下游算子进行处理，如果处理失败则返回error
func (d *debugChannelEdge) Collect(message Message) error {
	log := fmt.Sprintf("collect-message: %T, collect-detail: %+v", message, message)
	d.logAndSend(ActEdgeCollect, log)
	return d.c.Collect(message)
}

// Emit 用于从当前的 Edge 连接边中，读取下一条由上游传输下来的数据点
func (d *debugChannelEdge) Emit() (Message, bool) {
	m, ok := d.c.Emit()
	log := ""
	defer d.logAndSend(ActEdgeEmit, log)
	if !ok {
		log = "edge aborated while emiting"
		return m, ok
	}

	if m == nil {
		log = "emit-empty"
		return m, ok
	}

	log = fmt.Sprintf("emit-message: %T, emit-detail: %+v", m, m)
	return m, ok
}

func (d *debugChannelEdge) Close() error {
	d.logAndSend(ActEdgeClose, "close edge !! ")
	return d.c.Close()
}

func (d *debugChannelEdge) Abort() {
	d.logAndSend(ActEdgeAbort, "abort edge !! ")
	d.c.Abort()
}

func (d *debugChannelEdge) Type() pipeline.EdgeType {
	return d.c.Type()
}

type logMsg struct {
	Task    string  `json:"task,omitempty"`
	Parent  string  `json:"parent,omitempty"`
	Child   string  `json:"child,omitempty"`
	Content string  `json:"content,omitempty"`
	Action  edgeAct `json:"action,omitempty"`
}

func (d *debugChannelEdge) mqttTopic() string {
	return fmt.Sprintf("APP_LOG/%s", d.taskName)
}

type edgeAct string

const (
	ActEdgeEmit    edgeAct = "emit"
	ActEdgeCollect edgeAct = "collect"
	ActEdgeClose   edgeAct = "close"
	ActEdgeAbort   edgeAct = "abort"
)

func (d *debugChannelEdge) logAndSend(act edgeAct, log string) {
	//d.WithField("act", act).Info(log)
	//lm := &logMsg{
	//	Task:    d.taskName,
	//	Parent:  d.parentName,
	//	Child:   d.childName,
	//	Action:  act,
	//	Content: log,
	//}
	//bs, _ := json.Marshal(lm)
	//msg := &mqtt2.Msg{
	//	Topic:   d.mqttTopic(),
	//	Payload: bs,
	//}
	//if err := d.mqCli.Pub(msg); err != nil {
	//	d.WithError(err).Errorf("publish edge collect log")
	//}
	return
}

type ErrorHandler interface {
	HandleError(err error)
}
