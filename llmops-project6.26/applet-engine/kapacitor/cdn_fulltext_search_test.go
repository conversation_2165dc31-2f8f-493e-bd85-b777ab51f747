package kapacitor

import (
	"context"
	"github.com/elastic/go-elasticsearch/v6"
	"testing"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func TestES(t *testing.T) {
	esClient, _ := stdes.NewESClientByCfg(elasticsearch.Config{
		Addresses: []string{
			// "http://*************:9200",
			// "http://**************:9200",
			"http://*************:7788",
		},
		Username:          "shiva",
		Password:          "shiva",
		EnableDebugLogger: true,
	})

	ctx := context.Background()
	index := "test_fulltext12231"
	// 判断索引是否存在，不存在则创建
	indexExist, err := esClient.IndexExists(ctx, index)
	if err != nil {
		stdlog.Info(err)
	}

	//esClient.CatIndices(ctx)
	stdlog.Info(indexExist)

	//// 插入数据
	//data := make(map[string]any)
	//data["text"] = "以后是大数据和人工智能的天下"
	//err = esClient.Index(ctx, index, data)
	//if err != nil {
	//	stdlog.Info(err)
	//}

	// 查询
	query := map[string]interface{}{
		"size": 3,
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"text": "123123",
			},
		},
	}
	rsp := new(stdes.RecallResp)
	err = esClient.Search(ctx, index, query, rsp)
	if err != nil {
		stdlog.Info(err)
	}
	chunks, _ := ResultHandler(*rsp)
	stdlog.Info(chunks)
	stdlog.Info("执行完毕")
}
