package kapacitor

import (
	"fmt"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextKnowledgeSearchNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextKnowledgeSearchNode // 算子的参数定义
}

func newTextKnowledgeSearchNode(et *ExecutingTask, n *pipeline.TextKnowledgeSearchNode, d NodeDiagnostic) (*TextKnowledgeSearchNode, error) {
	hn := &TextKnowledgeSearchNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextKnowledgeSearchNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextKnowledgeSearchNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	call, err := util.GetCallFromPointMessages(p)
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	chunks, err := extracChunksFromOutputField(p.Fields())
	if err != nil {
		return
	}
	queries := make([]string, len(chunks))
	for i, c := range chunks {
		queries[i] = c.Content
	}
	var SSEMutex sync.Mutex
	tasks := make([]func() error, 0)
	outChunks := make([]*pb.Chunk, 0)
	kbs := agent_definition.KnowledgeBases(n.pn.WidgetParams)
	// 存在知识库信息时作检索，不存在时向后传递空的outChunks
	kbNum := len(kbs.KnowledgeBaseDesc)
	if kbNum != 0 {
		if kbNum > 1 && (kbs.RerankParams == nil || kbs.RerankParams.Model == nil) {
			return stderr.Errorf("must set rerank model to do cross search")
		}

		for _, query := range queries {
			// 单个知识库检索
			if kbNum == 1 {
				tasks = append(tasks, createSearchTask(call, kbs.KnowledgeBaseDesc[0], query, &outChunks, &SSEMutex))
			} else {
				tasks = append(tasks, createCrossSearchTask(call, &kbs, query, &outChunks, &SSEMutex))
			}
		}
		if err := helper.SyncRunTasks(tasks); err != nil {
			return err
		}
	}

	if err := setOutputIntoFields(p.Fields(), outChunks); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", outChunks)
	}
	return n.forwardMsg(p, true)
}

// 单个问题,单个知识库检索
func createSearchTask(call *debug.Call, kbDescriber *agent_definition.KnowlHubDescriber,
	query string, allChunks *[]*pb.Chunk, SSEMutex *sync.Mutex) func() error {
	return func() error {
		// 保证thought、action、observation的连续
		SSEMutex.Lock()
		defer SSEMutex.Unlock()
		toolDefinition := kbDescriber.Definition()

		// 执行前
		// 1、thought
		thought := fmt.Sprintf(ToolThoughtTemplate, toolDefinition.NameForHuman)
		if err := sendThoughtSSE(call, thought); err != nil {
			return stderr.Wrap(err, "failed to send thought event")
		}
		// 2、action
		if err := sendActionSSE(call, toolDefinition, query); err != nil {
			return stderr.Wrap(err, "failed to send action event")
		}

		// 执行查询
		curChunks, err := clients.KnowledgeToolExecutor.Search(call.Ctx(), kbDescriber, query)
		if err != nil {
			return err
		}
		*allChunks = append(*allChunks, curChunks...)

		// 执行结果
		// 3、observation
		if err := sendObservationSSE(call, toolDefinition, string(stdsrv.AnyToBytes(curChunks))); err != nil {
			return stderr.Wrap(err, "failed to send observation event")
		}
		return nil
	}
}

// 单个问题,跨知识库检索
func createCrossSearchTask(call *debug.Call, kbs *agent_definition.KnowledgeBases,
	query string, allChunks *[]*pb.Chunk, SSEMutex *sync.Mutex) func() error {
	return func() error {
		// 保证thought、action、observation的连续
		SSEMutex.Lock()
		defer SSEMutex.Unlock()
		toolDefinition := getCrossSearchToolDef()

		// 执行前
		// 1、thought
		thought := fmt.Sprintf(ToolThoughtTemplate, toolDefinition.NameForHuman)
		if err := sendThoughtSSE(call, thought); err != nil {
			return stderr.Wrap(err, "failed to send thought event")
		}

		// 2、action
		if err := sendActionSSE(call, toolDefinition, query); err != nil {
			return stderr.Wrap(err, "failed to send action event")
		}

		// 执行查询
		curChunks, err := clients.KnowledgeToolExecutor.SearchCrossKb(call.Ctx(), kbs, query)
		if err != nil {
			return err
		}
		*allChunks = append(*allChunks, curChunks...)

		// 执行结果
		// 3、observation
		if err := sendObservationSSE(call, toolDefinition, string(stdsrv.AnyToBytes(curChunks))); err != nil {
			return stderr.Wrap(err, "failed to send observation event")
		}

		return nil
	}
}

func getCrossSearchToolDef() agent_definition.Tool {
	return agent_definition.Tool{
		ID:           uuid.New().String(),
		Type:         agent_definition.ToolTypeKnowledgeHub,
		NameForModel: "跨知识库检索",
		NameForHuman: "跨知识库检索",
		Description:  "从多个知识库中对文本进行查询,同时保留相关性最高的若干文本",
		Parameters: triton.FunctionParameters{
			Type: triton.ParameterTypeObject,
			Properties: map[string]triton.ParameterProperty{
				agent_definition.KnowlHubParamQuery: {
					Type:        triton.ParameterTypeString,
					Description: agent_definition.KnowlHubParamQueryDesc,
				},
			},
		},
	}
}
