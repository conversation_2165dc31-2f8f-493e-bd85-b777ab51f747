package kapacitor

import (
	"net/url"
	"time"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-engine/tools/vecdb"

	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type VecInsertNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.VecInsertNode // 算子的参数定义

	params    url.Values
	vecDb     vecdb.Client
	url       *url.URL
	collName  string
	partition string
}

// newVecInsertNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newVecInsertNode(et *ExecutingTask, n *pipeline.VecInsertNode, d NodeDiagnostic) (*VecInsertNode, error) {
	hn := &VecInsertNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	if n.U == nil {
		return nil, stderr.Internal.Error("db url is nil")
	}

	hn.params = n.U.Query()
	dbc, err := vecdb.NewVecDBClientFromURL(et.ctx, n.U)
	if err != nil {
		return nil, stderr.Wrap(err, "new vector db client with URL %s", n.Url)
	}
	hn.vecDb = dbc
	hn.url = n.U
	hn.node.runF = hn.run
	hn.node.stopF = dbc.Close
	return hn, nil
}

func (n *VecInsertNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *VecInsertNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}

	nodeInPort, err := extracNodeInPortFromFields[widgets.NodeInPortVectorInsert](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to get node inPort values")
	}

	fileName := nodeInPort.FileName
	openaiReq := nodeInPort.OpenAiTextVectorReq
	openaiResp := nodeInPort.OpenAiTextVectorResp

	if len(openaiReq.Input) != len(openaiResp.Data) {
		return stderr.Wrap(err, "the length of texts is not same with embedded data")
	}
	if len(openaiReq.Input) == 0 {
		return stderr.Internal.Errorf("not data in openaiReq")
	}
	fText := vecdb.FieldColumnData{
		FieldName: vecdb.VectorDBFieldText, //string切片
		Data:      utils.CvcT2AnySlice[string](openaiReq.Input),
	}

	fFeature := vecdb.FieldColumnData{
		FieldName: vecdb.VectorDBFieldVector, //二维float32切片
		Data:      utils.CvcT2AnySlice[[]float32](models.CvtFloatSlices[float64, float32](openaiResp.GetFloat64Slice())),
	}

	fTitle := vecdb.FieldColumnData{
		FieldName: vecdb.VectorDBFieldTitle,
		Data:      utils.CvcT2AnySlice(utils.Duplicate2Slice(fileName, len(openaiReq.Input))),
	}
	begin := time.Now()
	ids, err := n.vecDb.Insert(n.et.ctx, fText, fFeature, fTitle)
	if err != nil {
		return stderr.Wrap(err, "insert features")
	}
	res := &engine.VecInsertRes{
		Records:   len(ids),
		Dimension: len(openaiResp.Data[0].Embedding),
	}
	for _, text := range openaiReq.Input {
		res.BodySize += len(text)
	}
	res.Consumed = time.Now().Sub(begin).String()
	if err := setOutputIntoFields(p.Fields(), res); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", res)
	}
	return n.forwardMsg(p, true)
}
