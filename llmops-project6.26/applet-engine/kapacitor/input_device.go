package kapacitor

//
// import (
// 	"fmt"
// 	"github.com/influxdata/kapacitor/edge"
// 	kmodels "github.com/influxdata/kapacitor/models"
// 	"github.com/influxdata/kapacitor/pipeline"
// 	"github.com/thingio/edge-device-std/models"
// 	"github.com/thingio/edge-device-std/operations"
// 	"sync"
// 	"time"
// )
//
// type InputDeviceNode struct {
// 	node
//
// 	s *pipeline.InputDeviceNode
//
// 	ms      operations.ManagerService
// 	closing chan struct{}
// 	closed  bool
// 	mu      sync.Mutex
//
// 	protocol string
// 	product  string
// 	device   string
// }
//
// func newInputDeviceNode(et *ExecutingTask, n *pipeline.InputDeviceNode, d NodeDiagnostic) (*InputDeviceNode, error) {
// 	sn := &InputDeviceNode{
// 		node: node{
// 			Node: n,
// 			et:   et,
// 			diag: d,
// 		},
// 		s:        n,
// 		ms:       et.tm.EDMService.GetInstance(),
// 		closing:  make(chan struct{}),
// 		protocol: n.Protocol,
// 		product:  n.Product,
// 		device:   n.Device,
// 	}
// 	sn.node.runF = sn.runSubscribe
// 	sn.node.stopF = sn.stopSubscribe
// 	return sn, nil
// }
//
// func (n *InputDeviceNode) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
// 	return nil, fmt.Errorf("%s does not support batch data", pipeline.NodeInputDevice)
// }
//
// func (n *InputDeviceNode) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
// 	return nil, fmt.Errorf("%s does not support batch data", pipeline.NodeInputDevice)
// }
//
// func (n *InputDeviceNode) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
// 	return nil, fmt.Errorf("%s does not support batch data", pipeline.NodeInputDevice)
// }
//
// func (n *InputDeviceNode) Point(p edge.PointMessage) (edge.Message, error) {
// 	return p, nil
// }
//
// func (n *InputDeviceNode) Barrier(b edge.BarrierMessage) (edge.Message, error) {
// 	return b, nil
// }
//
// func (n *InputDeviceNode) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
// 	return d, nil
// }
//
// func (n *InputDeviceNode) Done() {}
//
// func (n *InputDeviceNode) runSubscribe([]byte) error {
// 	go func() {
// 		bus, closeBus, err := n.ms.SubscribeDeviceProps(n.protocol, n.product, n.device, operations.TopicSingleLevelWildcard)
// 		if err != nil {
// 			n.diag.Error("failed to subscribe device's props: "+n.device, err)
// 			return
// 		}
// 		for {
// 			select {
// 			case data, ok := <-bus:
// 				if !ok {
// 					return
// 				}
//
// 				props, ok := data.(map[models.ProductPropertyID]*models.DeviceData)
// 				if !ok {
// 					n.diag.UDFLog(fmt.Sprintf("format of edge device data is invalid: %+v", data))
// 					break
// 				}
// 				var ts time.Time
// 				fields := make(map[string]interface{}, len(props))
// 				for _, prop := range props {
// 					fields[prop.Name] = prop.Value
// 					ts = prop.Ts
// 				}
// 				if err = n.emit(fields, ts); err != nil {
// 					n.diag.Error(fmt.Sprintf("failed to emit data from subscribing: %+v", fields), err)
// 				}
// 			case <-n.closing:
// 				closeBus()
// 				n.diag.UDFLog("stop to subscribe device's props: " + n.device)
// 				return
// 			}
// 		}
// 	}()
//
// 	consumer := edge.NewConsumerWithReceiver(
// 		n.ins[0],
// 		edge.NewReceiverFromForwardReceiverWithStats(
// 			n.outs,
// 			edge.NewTimedForwardReceiver(n.timer, n),
// 		),
// 	)
// 	return consumer.Consume()
// }
// func (n *InputDeviceNode) emit(fields map[string]interface{}, ts time.Time) error {
// 	dbrp := n.et.Task.DBRPs[0]
// 	point := edge.NewPointMessage(n.device, dbrp.Database, dbrp.RetentionPolicy,
// 		kmodels.Dimensions{}, fields, map[string]string{
// 			pipeline.PropertyProtocol: n.protocol,
// 			pipeline.PropertyProduct:  n.product,
// 			pipeline.PropertyDevice:   n.device,
// 		}, ts,
// 	)
// 	return n.et.tm.WriteKapacitorPoint(point)
// }
//
// func (n *InputDeviceNode) stopSubscribe() {
// 	n.mu.Lock()
// 	defer n.mu.Unlock()
// 	if !n.closed {
// 		n.closed = true
// 		close(n.closing)
// 	}
// }
