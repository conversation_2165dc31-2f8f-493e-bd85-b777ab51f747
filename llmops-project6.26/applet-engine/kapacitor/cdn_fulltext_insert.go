package kapacitor

import (
	"context"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

const (
	defaultEmptyMapping = `{
		"mappings": {
			"default_type_": {
				"properties": {
					"text": {
						"type": "text",
						"analyzer": "ik_max_word"
					},
					"id": {
						"type": "keyword"
					},
					"ori_id": {
						"type": "keyword"
					},
					"doc_id": {
						"type": "keyword"
					}
				}
			}
		},
		"settings": {
			"index": {
				"number_of_replicas": 1,
				"number_of_shards": 1
			}
		}
	}`
)

type FullTextInsert struct {
	node
	unsupportedBatchNode
	pn *pipeline.FullTextInsert // 算子的参数定义
}

func newFullTextInsert(et *ExecutingTask, n *pipeline.FullTextInsert, d NodeDiagnostic) (*FullTextInsert, error) {
	hn := &FullTextInsert{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}
func (n *FullTextInsert) run([]byte) error {
	widgetParams := n.pn.WidgetParams
	esClient, err := getEsClientByConfig(&widgetParams.ESConfig)
	if err != nil {
		return stderr.Wrap(err, "failed to create elasticsearch client")
	}
	ctx := context.Background()
	// 判断索引是否存在，不存在则创建
	indexExist, err := esClient.IndexExists(ctx, widgetParams.IndexName)
	if err != nil {
		return stderr.Wrap(err, "failed to check index exist")
	}
	if !indexExist {
		stdlog.Warnf("index %s not exist, creating...", widgetParams.IndexName)
		err = esClient.CreateIndex(ctx, widgetParams.IndexName, defaultEmptyMapping)
		if err != nil {
			return stderr.Wrap(err, "failed to create index")
		}
	}

	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *FullTextInsert) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	ctx, err := util.GetContextFromPointMessages(p)
	if err != nil {
		return err
	}

	stdlog.Infof("FullTextInsert: %v", ctx)
	widgetParams := n.pn.WidgetParams
	esClient, err := getEsClientByConfig(&widgetParams.ESConfig)
	if err != nil {
		return stderr.Wrap(err, "failed to create elasticsearch client")
	}
	stdlog.Infof("elastic search client created")

	// content 是整个文段的内容
	content, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}
	if len(content) == 0 {
		return stderr.Internal.Error("content is empty")
	}
	// 插入数据
	data := make(map[string]any)
	data["text"] = content
	err = esClient.Index(ctx, widgetParams.IndexName, data)
	if err != nil {
		return stderr.Wrap(err, "failed to insert data")
	}

	rs := []*pb.Chunk{&pb.Chunk{Extra: map[string]string{agent_executor.FieldQuery: content}}}
	if err := setOutputIntoFields(p.Fields(), rs); err != nil {
		return stderr.Wrap(err, "set output into fields")
	}
	return n.forwardMsg(p, true)
}
