package kapacitor

import (
	"github.com/nsqio/go-nsq"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/jsonnet"
)

type NsqPubNode struct {
	node
	c       *pipeline.NsqPubNode
	np      *nsq.Producer
	topic   string
	payload string
}

// Create a new  NsqPubNode which submits received items via POST to an HTTP endpoint
func newNsqPubNode(et *ExecutingTask, n *pipeline.NsqPubNode, d NodeDiagnostic) (*NsqPubNode, error) {
	nn := &NsqPubNode{
		node:    node{Node: n, et: et, diag: d},
		c:       n,
		topic:   n.Topic,
		payload: n.Payload,
	}

	p, err := nsq.NewProducer(n.Address, nsq.NewConfig())
	if err != nil {
		d.<PERSON>rror("fail to new a nsq pub node", err)
		return nil, err
	}
	nn.np = p
	nn.node.runF = nn.runPub
	return nn, nil
}

func (n *NsqPubNode) runPub([]byte) error {
	consumer := edge.NewGroupedConsumer(n.ins[0], n)
	n.statMap.Set(statCardinalityGauge, consumer.CardinalityVar())
	return consumer.Consume()
}

func (n *NsqPubNode) NewGroup(group edge.GroupInfo, first edge.PointMeta) (edge.Receiver, error) {
	g := &nsqPubGroup{
		n:      n,
		buffer: new(edge.BatchBuffer),
	}
	return edge.NewReceiverFromForwardReceiverWithStats(
		n.outs,
		edge.NewTimedForwardReceiver(n.timer, g),
	), nil
}

type nsqPubGroup struct {
	n      *NsqPubNode
	buffer *edge.BatchBuffer
}

func (g *nsqPubGroup) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
	return nil, g.buffer.BeginBatch(begin)
}

func (g *nsqPubGroup) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
	return nil, g.buffer.BatchPoint(bp)
}

func (g *nsqPubGroup) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
	return g.BufferedBatch(g.buffer.BufferedBatchMessage(end))
}

func (g *nsqPubGroup) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	row := batch.ToRow()
	g.n.doNsqPub(row)
	return batch, nil
}

func (g *nsqPubGroup) Point(p edge.PointMessage) (edge.Message, error) {
	row := p.ToRow()
	g.n.doNsqPub(row)
	return p, nil
}

func (g *nsqPubGroup) Barrier(b edge.BarrierMessage) (edge.Message, error) {
	return b, nil
}

func (g *nsqPubGroup) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
	return d, nil
}

func (g *nsqPubGroup) Done() {
	g.n.np.Stop()
}

func (n *NsqPubNode) doNsqPub(row *models.Row) {
	message, err := jsonnet.FillJsonnet(row.ToMappedRow(), n.payload)
	if err != nil {
		n.diag.Error("fail to render message", err)
		return
	}
	err = n.np.Publish(n.topic, []byte(message))
	if err != nil {
		n.diag.Error("fail to publish nsq message", err)
	}
}
