package kapacitor

import (
	"encoding/json"
	"strconv"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type QaSearchNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.QaSearchNode // 算子的参数定义
}

// newQaSearchNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newQaSearchNode(et *ExecutingTask, n *pipeline.QaSearchNode, d NodeDiagnostic) (*QaSearchNode, error) {
	hn := &QaSearchNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *QaSearchNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *QaSearchNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Wrap(err, "get call from message context")
	}

	question, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}

	ctx.Infof("params: %+v", n.pn.ParsedParams)

	var curChunks []*pb.Chunk
	kbs := n.pn.ParsedParams.KnowledgeBases

	// Check if we have any knowledge bases
	if len(kbs.KnowledgeBaseDesc) == 0 {
		setOutputIntoFields(p.Fields(), "")
		return n.forwardMsg(p, false)
	}

	// Handle multiple knowledge bases
	if len(kbs.KnowledgeBaseDesc) > 1 {
		curChunks, err = clients.KnowledgeToolExecutor.SearchCrossKb(call.Ctx(), kbs, question)
	} else {
		// Single knowledge base
		curChunks, err = clients.KnowledgeToolExecutor.Search(call.Ctx(), kbs.KnowledgeBaseDesc[0], question)
	}

	if err != nil {
		return stderr.Wrap(err, "failed to search knowledge")
	}
	ctx.Infof("search result: %+v", curChunks)

	// Handle empty chunks case
	if len(curChunks) == 0 || curChunks[0] == nil {
		setOutputIntoFields(p.Fields(), "")
		return n.forwardMsg(p, false)
	}

	// Get score from first chunk
	score, err := strconv.ParseFloat(curChunks[0].Extra[agent_executor.FieldScore], 64)
	if err != nil {
		return stderr.Wrap(err, "failed to parse score of %+v", curChunks[0])
	}
	// Check threshold
	if score < n.pn.ParsedParams.ScoreThreshold {
		setOutputIntoFields(p.Fields(), "")
		return n.forwardMsg(p, false)
	}

	// Parse content as map
	var contentMap map[string]any
	if err := json.Unmarshal([]byte(curChunks[0].Content), &contentMap); err != nil {
		return stderr.Wrap(err, "failed to unmarshal chunk content")
	}

	// Get answer field
	answer, ok := contentMap[n.pn.ParsedParams.AnswerField]
	if !ok {
		return stderr.Errorf("answer field %s not found in content map %+v", n.pn.ParsedParams.AnswerField, contentMap)
	}

	setOutputIntoFields(p.Fields(), answer)
	return n.forwardMsg(p, false)
}
