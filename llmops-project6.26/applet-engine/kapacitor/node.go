package kapacitor

import (
	"bytes"
	"expvar"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/alert"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/server/vars"
	kexpvar "transwarp.io/applied-ai/applet-engine/tools/expvar"
	"transwarp.io/applied-ai/applet-engine/tools/keyvalue"
	"transwarp.io/applied-ai/applet-engine/tools/timer"
)

const (
	statErrorCount       = "errors"
	statCardinalityGauge = "working_cardinality"
	statAverageExecTime  = "avg_exec_time_ns"
)

type NodeDiagnostic interface {
	Error(msg string, err error, ctx ...keyvalue.T)

	// AlertNode
	AlertTriggered(level alert.Level, id string, message string, rows *models.Row)

	// AutoscaleNode
	SettingReplicas(new int, old int, id string)

	// QueryNode
	StartingBatchQuery(q string)

	// LogNode
	LogPointData(key, prefix string, data edge.PointMessage)
	LogBatchData(key, prefix string, data edge.BufferedBatchMessage)

	// UDF
	UDFLog(s string)
}

type nodeDiagnostic struct {
	NodeDiagnostic
	node *node
}

func newNodeDiagnostic(n *node, diag NodeDiagnostic) *nodeDiagnostic {
	return &nodeDiagnostic{
		NodeDiagnostic: diag,
		node:           n,
	}
}

func (n *nodeDiagnostic) Error(msg string, err error, ctx ...keyvalue.T) {
	n.node.incrementErrorCount()
	if !n.node.quiet {
		n.NodeDiagnostic.Error(msg, err, ctx...)
	}
}

// A node that can be  in an executor.
type Node interface {
	pipeline.Node

	addParentEdge(edge.StatsEdge)

	init(quiet bool)

	// start the node and its children
	start(snapshot []byte)
	stop()

	// snapshot running state
	snapshot() ([]byte, error)
	restore(snapshot []byte) error

	// wait for the node to finish processing and return any errors
	Wait() error

	// link specified child
	linkChild(c Node) error
	addParent(p Node)

	// close children edges
	closeChildEdges()
	// abort parent edges
	abortParentEdges()

	// executing dot
	edot(buf *bytes.Buffer, labels bool)

	nodeStatsByGroup() map[models.GroupID]nodeStats

	collectedCount() int64

	emittedCount() int64

	incrementErrorCount()

	stats() map[string]interface{}
}

type KapacitorNodeGetter interface {
	KapacitorNode() *node
}

// implementation of Node
type node struct {
	*logrus.Entry
	pipeline.Node
	et         *ExecutingTask
	parents    []Node
	children   []Node
	runF       func(snapshot []byte) error
	stopF      func()
	errCh      chan error
	err        error
	finishedMu sync.Mutex
	finished   bool
	ins        []edge.StatsEdge
	outs       []edge.StatsEdge
	diag       NodeDiagnostic
	timer      timer.Timer
	statsKey   string
	statMap    *kexpvar.Map

	quiet bool

	nodeErrors *kexpvar.Int
}

func (n *node) KapacitorNode() *node {
	return n
}

// PnMeta 返回当前node对应的 pipeline.Node 对应的meta信息
func (n *node) PnMeta() models.NodeMeta {
	return n.Meta()
}

func (n *node) addParentEdge(e edge.StatsEdge) {
	n.ins = append(n.ins, e)
}

func (n *node) abortParentEdges() {
	for _, in := range n.ins {
		in.Abort()
	}
}

func (n *node) init(quiet bool) {
	tags := map[string]string{
		"task": n.et.Task.ID,
		"node": n.Name(),
		"type": n.et.Task.Type.String(),
		"kind": n.Desc(),
	}
	n.statsKey, n.statMap = vars.NewStatistic("nodes", tags)
	avgExecVar := &MaxDuration{}
	n.statMap.Set(statAverageExecTime, avgExecVar)
	n.nodeErrors = &kexpvar.Int{}
	n.statMap.Set(statErrorCount, n.nodeErrors)
	n.diag = newNodeDiagnostic(n, n.diag)
	n.statMap.Set(statCardinalityGauge, kexpvar.NewIntFuncGauge(nil))
	n.timer = n.et.tm.TimingService.NewTimer(avgExecVar)
	n.errCh = make(chan error, 1)
	n.quiet = quiet

	// init logger of this node
	n.Entry = logrus.NewEntry(logrus.StandardLogger())
	logFields := make(map[string]interface{}, len(tags))
	for k, v := range tags {
		logFields[k] = v
	}
	n.Entry = n.Entry.WithFields(logFields)
}

func (n *node) start(snapshot []byte) {
	go func() {
		var err error
		defer func() {
			// Always close children edges
			n.closeChildEdges()
			// Propagate error up
			if err != nil {
				// Handle panic in runF
				r := recover()
				if r != nil {
					trace := make([]byte, 512)
					n := runtime.Stack(trace, false)
					err = fmt.Errorf("%s: Trace:%s", r, string(trace[:n]))
				}
				n.abortParentEdges()
				n.diag.Error("node failed", err)
				stdlog.WithError(err).Errorf("[%s] node failed", n.Name())

				err = errors.Wrap(err, n.Name())
			}
			n.errCh <- err
		}()
		// Run node
		err = n.runF(snapshot)
	}()
}

func (n *node) stop() {
	if n.stopF != nil {
		n.stopF()
	}
	vars.DeleteStatistic(n.statsKey)
}

// no-op snapshot
func (n *node) snapshot() (b []byte, err error) { return }

// no-op restore
func (n *node) restore([]byte) error { return nil }

func (n *node) Wait() error {
	n.Infof("Enter node wait function")
	// 加锁，防止多个goroutine同时访问n.finished，避免同时等待 errCh
	n.finishedMu.Lock()
	defer n.finishedMu.Unlock()
	if n.finished {
		n.Infof("node finished")
		return n.err
	}

	n.Infof("node not finished, waiting exit")
	n.finished = true
	n.Infof("set n.finished to true and start waiting errCh")
	n.err = <-n.errCh
	n.Infof("get err from errCh")
	return n.err
}

func (n *node) addChild(c Node) (edge.StatsEdge, error) {
	if n.Provides() != c.Wants() {
		return nil, fmt.Errorf("cannot add child mismatched edges: %s:%s -> %s:%s", n.Name(), n.Provides(), c.Name(), c.Wants())
	}
	if n.Provides() == pipeline.NoEdge {
		return nil, fmt.Errorf("cannot add child no edge expected: %s:%s -> %s:%s", n.Name(), n.Provides(), c.Name(), c.Wants())
	}
	n.children = append(n.children, c)

	d := n.et.tm.diag.WithEdgeContext(n.et.Task.ID, n.Name(), c.Name())
	edge := newEdge(conf.Config.EdgeDebug, n.et.Task.ID, n.Name(), c.Name(), n.Provides(), defaultEdgeBufferSize, d)
	if edge == nil {
		return nil, fmt.Errorf("unknown edge type %s", n.Provides())
	}
	c.addParentEdge(edge)
	return edge, nil
}

func (n *node) addParent(p Node) {
	n.parents = append(n.parents, p)
}

func (n *node) linkChild(c Node) error {
	// add child
	edge, err := n.addChild(c)
	if err != nil {
		return err
	}

	// add parent
	c.addParent(n)

	// store edge to child
	n.outs = append(n.outs, edge)
	return nil
}

func (n *node) closeChildEdges() {
	for _, child := range n.outs {
		child.Close()
	}
}

func (n *node) edot(buf *bytes.Buffer, labels bool) {
	if labels {
		// Print all stats on node.
		buf.WriteString(
			fmt.Sprintf("\n%s [xlabel=\"",
				n.Name(),
			),
		)
		i := 0
		n.statMap.DoSorted(func(kv expvar.KeyValue) {
			if i != 0 {
				// NOTE: A literal \r, indicates a newline right justified in graphviz syntax.
				buf.WriteString(`\r`)
			}
			i++
			var s string
			if sv, ok := kv.Value.(kexpvar.StringVar); ok {
				s = sv.StringValue()
			} else {
				s = kv.Value.String()
			}
			buf.WriteString(
				fmt.Sprintf("%s=%s",
					kv.Key,
					s,
				),
			)
		})
		buf.Write([]byte("\"];\n"))

		for i, c := range n.children {
			buf.Write([]byte(
				fmt.Sprintf("%s -> %s [label=\"processed=%d\"];\n",
					n.Name(),
					c.Name(),
					n.outs[i].Collected(),
				),
			))
		}

	} else {
		// Print all stats on node.
		buf.Write([]byte(
			fmt.Sprintf("\n%s [",
				n.Name(),
			),
		))
		n.statMap.DoSorted(func(kv expvar.KeyValue) {
			var s string
			if sv, ok := kv.Value.(kexpvar.StringVar); ok {
				s = sv.StringValue()
			} else {
				s = kv.Value.String()
			}
			buf.Write([]byte(
				fmt.Sprintf("%s=\"%s\" ",
					kv.Key,
					s,
				),
			))
		})
		buf.Write([]byte("];\n"))
		for i, c := range n.children {
			buf.Write([]byte(
				fmt.Sprintf("%s -> %s [processed=\"%d\"];\n",
					n.Name(),
					c.Name(),
					n.outs[i].Collected(),
				),
			))
		}
	}
}

// node collected count is the sum of emitted counts of parent edges
func (n *node) collectedCount() (count int64) {
	for _, in := range n.ins {
		count += in.Emitted()
	}
	return
}

// node emitted count is the sum of collected counts of children edges
func (n *node) emittedCount() (count int64) {
	for _, out := range n.outs {
		count += out.Collected()
	}
	return
}

// node increment error count increments a nodes error_count stat
func (n *node) incrementErrorCount() {
	n.nodeErrors.Add(1)
}
func (n *node) ForwardMsg(p edge.PointMessage, output any) error {
	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", output)
	}
	return n.forwardMsg(p, true)
}

func (n *node) ForwardMsg(p edge.PointMessage, output any) error {
	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", output)
	}
	return n.forwardMsg(p, true)
}

// forwardMsg 向后面的所有下游算子，发送消息
func (n *node) forwardMsg(m edge.Message, skipErr bool) error {
	for _, out := range n.outs {
		if err := out.Collect(m); err != nil {
			if skipErr {
				n.WithError(err).Errorf("send message to next node")
				continue
			}
			return stderr.Wrap(err, "")
		}
	}
	return nil
}

func (n *node) getPreviousNode() (*node, error) {
	if len(n.parents) != 1 {
		return nil, stderr.Errorf("node %s has more than one parent or no parents", n.Name())
	}

	preNodeInterface := n.parents[0]
	kapacitorNodeGetter, ok := preNodeInterface.(KapacitorNodeGetter)
	if !ok {
		return nil, stderr.Errorf("failed to convert %T to KapacitorNodeGetter", preNodeInterface)
	}

	return kapacitorNodeGetter.KapacitorNode(), nil
}

// forward2TargetNode
// targetId: 跳转的目标节点id
// m: 发送的消息
func (n *node) forward2TargetNode(targetId string, m edge.Message) error {
	rootNode := n
	// 当向上遍历node，当node无parents时，断定该node为rootNode
	for len(rootNode.parents) > 0 {
		nodeInterface := rootNode.parents[0]
		var ok bool
		rootNode, ok = nodeInterface.(*node)
		if !ok {
			return stderr.Errorf("failed to convert %T to %T", nodeInterface, rootNode)
		}
	}
	// 变量chain node，寻找target node
	var targetNode *node
	curNodes := []*node{rootNode}
LOOP:
	for len(curNodes) > 0 {
		nextNodes := []*node{}
		for _, curNode := range curNodes {
			if curNode.Meta().Id == targetId {
				targetNode = curNode
				break LOOP
			}
			for _, nodeInterface := range curNode.children {
				kapacitorNodeGetter, ok := nodeInterface.(KapacitorNodeGetter)
				if !ok {
					return stderr.Errorf("failed to convert %T to %T", nodeInterface, kapacitorNodeGetter)
				}
				nextNodes = append(nextNodes, kapacitorNodeGetter.KapacitorNode())
			}
			curNodes = nextNodes
		}
	}
	if targetNode == nil {
		return stderr.Errorf("failed to find target node %s", targetId)
	}
	targetInportIndex := 0 // 目标节点的目标端口索引号，默认只有一个端口，index是0
	// 判断targetNode的父节点是否是join节点
	preNodeIsJoin, err := targetNode.isPreNodeJoin()
	if err != nil {
		return stderr.Wrap(err, "failed to check if target previous node %s is join node", targetNode.Meta().NodeName)
	}
	// 如果前一个节点不是join节点，直接发送给目标节点
	if !preNodeIsJoin {
		targetInput := targetNode.ins[targetInportIndex]
		if err := targetInput.Collect(m); err != nil {
			return stderr.Wrap(err, "send message to %s node", targetNode.Meta().NodeName)
		}
		return nil
	}
	// 如果前一个节点是join节点，join节点作为目标节点
	targetNode, err = targetNode.getPreviousNode()
	if err != nil {
		return stderr.Wrap(err, "failed to get previous node of join node %s", targetNode.Meta().NodeName)
	}
	// 如果是从goto算子发送过来的消息，直接发送给目标节点
	_, currentNodeIsGoto := n.Node.(*pipeline.GotoNode)
	if currentNodeIsGoto {
		// TODO: goto算子无法推断出targetInportIndex，需要后端传递
		targetInput := targetNode.ins[targetInportIndex]
		if err := targetInput.Collect(m); err != nil {
			return stderr.Wrap(err, "send message to %s node", targetNode.Meta().NodeName)
		}
		return nil
	}
	// 如果是从非goto算子：问题分类、条件判断算子发送过来的消息, 可以从父节点推断出targetInportIndex
	// 遍历parents，如果parentId 与 n.Meta().Id 一致, 说明是从此节点发送过来的消息
	// targetInportIndex为当前parent的index
	findTargetInportIndex := false
	for index, parent := range targetNode.parents {
		if parent.Meta().Id == n.Meta().Id {
			targetInportIndex = index
			findTargetInportIndex = true
		}
	}
	if !findTargetInportIndex {
		return stderr.Errorf("failed to find target node inport index of join node %s", targetNode.Meta().NodeName)
	}
	targetInput := targetNode.ins[targetInportIndex]
	if err := targetInput.Collect(m); err != nil {
		return stderr.Wrap(err, "send message to %s node", targetNode.Meta().NodeName)
	}
	return nil
}

func (n *node) forward2TargetNodes(targetIds []string, m edge.Message) error {
	// TODO 暂时不考虑效率问题
	for _, targetId := range targetIds {
		err := n.forward2TargetNode(targetId, m)
		if err != nil {
			return err
		}
	}
	return nil
}

// isPreNode 检查指定node的前一个节点是否是指定类型T
func isPreNode[T pipeline.Node](n *node) (bool, error) {
	preNode, err := n.getPreviousNode()
	if err != nil {
		return false, stderr.Wrap(err, "get previous node of %s", n.Meta().NodeName)
	}
	_, isTargetType := preNode.Node.(T)
	return isTargetType, nil
}

// isPreNodeListenHttp 检查当前node的前一个节点是否是ListenHttpNode类型
func (n *node) isPreNodeListenHttp() (bool, error) {
	return isPreNode[*pipeline.ListenHttpNode](n)
}

// isPreNodeJoin 检查当前node的前一个节点是否是JoinNode类型
func (n *node) isPreNodeJoin() (bool, error) {
	// UnionNode也是多输入节点，提前判断，防止报错
	_, isUnionNode := n.Node.(*pipeline.UnionNode)
	if isUnionNode {
		return false, nil
	}
	return isPreNode[*pipeline.JoinNode](n)
}

func (n *node) stats() map[string]interface{} {
	stats := make(map[string]interface{})

	n.statMap.Do(func(kv expvar.KeyValue) {
		switch v := kv.Value.(type) {
		case kexpvar.IntVar:
			stats[kv.Key] = v.IntValue()
		case kexpvar.FloatVar:
			stats[kv.Key] = v.FloatValue()
		default:
			stats[kv.Key] = v.String()
		}
	})

	return stats
}

// Statistics for a node
type nodeStats struct {
	Fields     models.Fields
	Tags       models.Tags
	Dimensions models.Dimensions
}

// Return a copy of the current node statistics.
// If if no groups have been seen yet a NilGroup will be created with zero stats.
func (n *node) nodeStatsByGroup() (stats map[models.GroupID]nodeStats) {
	// Get the counts for just one output.
	stats = make(map[models.GroupID]nodeStats)
	if len(n.outs) > 0 {
		n.outs[0].ReadGroupStats(func(g *edge.GroupStats) {
			stats[g.GroupInfo.ID] = nodeStats{
				Fields: models.Fields{
					// A node's emitted count is the collected count of its output.
					"emitted": g.Collected,
				},
				Tags:       g.GroupInfo.Tags,
				Dimensions: g.GroupInfo.Dimensions,
			}
		})
	}
	if len(stats) == 0 {
		// If we have no groups/stats add nil group with emitted = 0
		stats[""] = nodeStats{
			Fields: models.Fields{
				"emitted": int64(0),
			},
		}
	}
	return
}

// MaxDuration is a 64-bit int variable representing a duration in nanoseconds,that satisfies the expvar.Var interface.
// When setting a value it will only be set if it is greater than the current value.
type MaxDuration struct {
	d      int64
	setter timer.Setter
}

func (v *MaxDuration) String() string {
	return `"` + v.StringValue() + `"`
}

func (v *MaxDuration) StringValue() string {
	return time.Duration(v.IntValue()).String()
}

func (v *MaxDuration) IntValue() int64 {
	return atomic.LoadInt64(&v.d)
}

// Set sets value if it is greater than current value.
// If set was successful and a setter exists, will pass on value to setter.
func (v *MaxDuration) Set(next int64) {
	for {
		cur := v.IntValue()
		if next > cur {
			if atomic.CompareAndSwapInt64(&v.d, cur, next) {
				if v.setter != nil {
					v.setter.Set(next)
				}
				return
			}
		} else {
			return
		}
	}
}
