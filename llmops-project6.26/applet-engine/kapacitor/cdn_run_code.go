package kapacitor

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/code_service"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/http"
)

type RunCodeNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.RunCodeNode // 算子的参数定义
}

// newRunCodeNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newRunCodeNode(et *ExecutingTask, n *pipeline.RunCodeNode, d NodeDiagnostic) (*RunCodeNode, error) {
	hn := &RunCodeNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *RunCodeNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *RunCodeNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardCodeResult(p, ctx)
}

func (n *RunCodeNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardMsg(begin, false)
}

func (n *RunCodeNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardCodeResult(bp, ctx)
}

func (n *RunCodeNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardMsg(end, false)
}

func (n *RunCodeNode) forwardCodeResult(m edge.Message, ctx models.Context) (err error) {
	if ctx.Call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	callCtx := ctx.Call.Ctx()

	fieldGetter, ok := m.(edge.FieldGetter)
	if !ok {
		return stderr.InvalidParam.Errorf("message %+v is not a FieldGetter", m)
	}
	fields := fieldGetter.Fields()
	inputs, exist, err := models.GetValueOfFields[any](fields, models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}

	pythonCodeRequest := &code_service.TestPythonCodeRequest{
		Code:   n.pn.Code,
		Inputs: inputs,
	}
	bs, err := json.Marshal(pythonCodeRequest)
	if err != nil {
		return stderr.Wrap(err, "marshal http request payload")
	}

	responseBody, _, err := http.SendHttpRequest(http.POST, conf.Config.BackendTask.RunCodeUrl, bs, http.DefaultHeader, callCtx)
	if err != nil {
		return stderr.Wrap(err, "send http request to get code result")
	}
	result, err := http.ReadResponseBody(responseBody)
	if err != nil {
		return stderr.Wrap(err, "read http response body")
	}
	var pythonCodeResponse code_service.TestPythonCodeResponse
	if err := json.Unmarshal(result, &pythonCodeResponse); err != nil {
		return stderr.Wrap(err, "unmarshal code result, make sure the code result is dict")
	}

	output := pythonCodeResponse.Output
	strSlice, err2 := util.CastToStrSlice(output)
	if err2 == nil {
		output = strSlice
	}

	if pythonCodeResponse.Log != "" {
		ctx.Info("\ncode output:\n" + pythonCodeResponse.Log)
	}

	if pythonCodeResponse.Error != "" {
		return stderr.Errorf(pythonCodeResponse.Error)
	}

	if err := setOutputIntoFields(fields, output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", pythonCodeResponse.Output)
	}

	return n.forwardMsg(m, false)
}
