package kapacitor

import (
	"context"
	"fmt"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextSensitiveFilterNode struct {
	node
	unsupportedBatchNode
	pn     *pipeline.TextSensitiveFilterNode // 算子的参数定义
	filter *triton.StreamTextFilter
}

// newTextSensitiveFilterNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextSensitiveFilterNode(et *ExecutingTask, n *pipeline.TextSensitiveFilterNode, d NodeDiagnostic) (*TextSensitiveFilterNode, error) {
	hn := &TextSensitiveFilterNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextSensitiveFilterNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextSensitiveFilterNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()
	basicConfig := triton.FilterBasicConfig{
		WindowSize: 10,
		StepSize:   5,
	}
	securityServiceConfig := triton.FilterSecurityServiceConfig{
		ConfigId: "146fc6cf-4227-494d-851c-d91d4e604819",
		Address:  "http://**************:8008",
		Timeout:  triton.DefaultSecurityCensorTimeout,
		Ctx:      context.Background(),
	}
	passTextHandler := func(text string) error {
		fmt.Println(text)
		return nil
	}
	filter, err := triton.NewStreamTextFilter(basicConfig, securityServiceConfig)
	if err != nil {
		return err
	}
	filter.SetPassTextHandler(passTextHandler)
	n.filter = filter
	return n.forwardMsg(begin, false)
}

func (n *TextSensitiveFilterNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	output, exist, err := models.GetValueOfFields[string](bp.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	passTextHanlder := func(text string) error {
		if err := setOutputIntoFields(bp.Fields(), text); err != nil {
			return stderr.Wrap(err, "failed to set output into fields")
		}
		return n.forwardMsg(bp, false)
	}
	n.filter.SetPassTextHandler(passTextHanlder)
	stopTextHandler := func(response *triton.CheckSecurityResponse) error {
		stdsrv.SSESendReplace(ctx.Call.W(), ctx.Call.ReqID(), n.pn.DisplayText)
		ctx.Call.Cancel()
		return nil
	}
	n.filter.SetStopTextHandler(stopTextHandler)
	n.filter.Append(output)
	return nil
}

func (n *TextSensitiveFilterNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()
	n.filter.Done()
	return n.forwardMsg(end, false)
}

func (n *TextSensitiveFilterNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if ctx.Call == nil {
		return stderr.Wrap(err, "get call from message context")
	}

	text, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	if w, ok := n.isSensitive(text); ok {
		ctx.Warnf("sensitive word: %s", w)
		if err := stdsrv.SSESendReplace(ctx.Call.W(), ctx.Call.ReqID(), n.pn.DisplayText); err != nil {
			return stderr.Wrap(err, "failed to send replace sse")
		}
		if err := setOutputIntoFields(p.Fields(), n.pn.DisplayText); err != nil {
			return stderr.Wrap(err, "failed to set output into fields")
		}
	}
	return n.forwardMsg(p, true)
}

func (n *TextSensitiveFilterNode) isSensitive(s string) (string, bool) {
	for _, w := range n.pn.Words {
		if strings.Contains(s, w) {
			return w, true
		}
	}
	return "", false
}

func (n *TextSensitiveFilterNode) isSensitiveV2(s string) (string, bool) {
	securityCensor := triton.NewSecurityCensor("http://**************:8008", triton.DefaultSecurityCensorTimeout)
	result, err := securityCensor.CheckOutputSensitiveWords(context.Background(), &triton.CheckSecurityRequest{Sentence: s, ConfigId: "095d52df-7d86-4dab-a501-b2772ff6ca9a"})
	if err != nil {
		return "", false
	}
	return result.Response, result.Risk
}
