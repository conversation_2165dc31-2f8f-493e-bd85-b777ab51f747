package kapacitor

// import (
// 	"path/filepath"

// 	"transwarp.io/applied-ai/aiot/vision-std/stderr"
// 	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
// 	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
// 	"transwarp.io/applied-ai/applet-engine/edge"
// 	"transwarp.io/applied-ai/applet-engine/pipeline"
// 	"transwarp.io/applied-ai/applet-engine/tools/file"
// )

// type FileParseNode struct {
// 	node
// 	unsupportedBatchNode
// 	pn *pipeline.FileParseNode // 算子的参数定义
// }

// // newFileParseNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
// func newFileParseNode(et *ExecutingTask, n *pipeline.FileParseNode, d NodeDiagnostic) (*FileParseNode, error) {
// 	hn := &FileParseNode{
// 		node: node{Node: n, et: et, diag: d},
// 		pn:   n,
// 	}
// 	hn.node.runF = hn.run
// 	return hn, nil
// }

// func (n *FileParseNode) run([]byte) error {
// 	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
// }

// func (n *FileParseNode) Point(p edge.PointMessage) (err error) {
// 	ctx, err := p.Context()
// 	if err != nil {
// 		return stderr.Wrap(err, "get context from point message")
// 	}
// 	defer func() {
// 		logError(ctx, err)
// 	}()

// 	if p == nil || p.Fields() == nil {
// 		return stderr.InvalidParam.Error("recved message is nil or has no fields")
// 	}
// 	output, err := extracOutputFromFields[*engine.SFSFile](p.Fields())
// 	if err != nil {
// 		return stderr.Wrap(err, "failed to extract output from fields")
// 	}
// 	if ctx.FilePath == "" {
// 		ctx.Warnf("file path is empty, forward empty value")
// 		return n.forwardParseResult(p, "")
// 	}
// 	localFilePath, err := stdfs.GetSFSLocalPath(ctx.FilePath)
// 	if err != nil {
// 		return stderr.Wrap(err, "get local path from sfs path %s", ctx.FilePath)
// 	}

// 	fileText, err := n.ReadContent(localFilePath, output.Content)
// 	if err != nil {
// 		return stderr.Internal.Cause(err, "read content")
// 	}
// 	return n.forwardParseResult(p, fileText)
// }

// func (n *FileParseNode) ReadContent(path string, fileBytes []byte) (string, error) {
// 	var text string
// 	var err error
// 	switch filepath.Ext(path) {
// 	case file.DOCX:
// 		text, err = file.ReadDOCXFileContent(path)
// 	case file.PDF:
// 		text, err = file.ReadPDFFileContent(path)
// 	case file.TXT, file.MD, file.CSV:
// 		text = string(fileBytes)
// 	default:
// 		return "", stderr.InvalidParam.Error("only support %v file", []string{file.DOCX, file.PDF, file.TXT, file.CSV, file.MD})
// 	}
// 	if err != nil {
// 		return "", stderr.Wrap(err, "read content from file %s", path)
// 	}
// 	return text, nil
// }

// func (n *FileParseNode) forwardParseResult(p edge.PointMessage, text string) error {
// 	if err := setOutputIntoFields(p.Fields(), text); err != nil {
// 		return stderr.Wrap(err, "set output %+v into fields", text)
// 	}
// 	return n.forwardMsg(p, true)
// }
