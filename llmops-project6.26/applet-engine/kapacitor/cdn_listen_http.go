package kapacitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"

	"github.com/sirupsen/logrus"

	"github.com/patrickmn/go-cache"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"
)

type ListenHttpNode struct {
	node
	pn       *pipeline.ListenHttpNode
	srv      *http.Server
	ctx      context.Context
	cancel   context.CancelFunc
	listener *httpListener
	taskID   string
}

// Create a new  ListenHttpNode which submits received items via POST to an HTTP endpoint
func newListenHttpNode(et *ExecutingTask, n *pipeline.ListenHttpNode, d NodeDiagnostic) (*ListenHttpNode, error) {
	if et.Task == nil || et.Task.ID == "" {
		return nil, stderr.Error("task is nil or task id is empty")
	}
	ctx, cancel := context.WithCancel(context.Background())
	hn := &ListenHttpNode{
		node: node{
			Node: n,
			et:   et,
			diag: d,
		},
		pn:     n,
		ctx:    ctx,
		cancel: cancel,
		taskID: et.Task.ID,
	}
	hn.node.runF = hn.startServe
	hn.node.stopF = hn.stopServe
	return hn, nil
}

func (n *ListenHttpNode) startServe([]byte) error {
	if n == nil || n.ctx == nil {
		return stderr.Error("listen http node init incorrectly")
	}
	l, err := register(n)
	if err != nil {
		return stderr.Wrap(err, "new listen http node")
	}
	n.listener = l
	<-n.ctx.Done()
	return nil
}

func (n *ListenHttpNode) RecvMessage(name string, m edge.Message) error {
	return n.listener.RecvMessage(name, m)
}

func (n *ListenHttpNode) stopServe() {
	n.Infof("stopping HTTP server")

	defer func() {
		n.cancel()
		n.abortParentEdges()
		n.finished = true
		n.listener = nil
	}()
	err := unregister(n)
	if err != nil {
		stdlog.WithError(err).Errorf("unsegister node")
	}
}

// type httpListener interface {
// 	http.Handler
// 	// RecvMessage 用于异步的接受响应的消息
// 	RecvMessage(name string, m edge.Message) error
// }

type httpListener struct {
	port  int                        // 当前listener监听的http端口
	ns    map[string]*ListenHttpNode // path -> listen node
	calls *cache.Cache
	srv   *http.Server
}

const (
	defaultHttpTimeout  = time.Hour * 1 // http请求超时时间1小时，文档自定义加工应用链需要较长的处理时间
	defaultCacheCleanup = time.Second * 10
)

func newHttpListener(port int) *httpListener {
	l := &httpListener{
		port:  port,
		calls: cache.New(defaultHttpTimeout, defaultCacheCleanup),
	}

	l.calls.OnEvicted(func(reqID string, v interface{}) {
		c, ok := v.(*debug.Call)
		if !ok {
			return
		}
		c.EndWithTimeout()
	})

	return l
}

func (h *httpListener) stopServe() error {
	if h.srv == nil {
		return nil
	}
	stdlog.Infof("stop serving on :%d", h.port)
	if err := h.srv.Close(); err != nil {
		return stderr.Error("shutting down the server, cause %s", err.Error())
	}
	return nil
}

func (h *httpListener) serve() error {
	stdlog.Infof("starting http server on ':%d'", h.port)
	h.srv = &http.Server{Addr: fmt.Sprintf(":%d", h.port)}
	h.srv.Handler = h

	// always returns error. ErrServerClosed on graceful close
	err := h.srv.ListenAndServe()
	if !errors.Is(err, http.ErrServerClosed) {
		// unexpected error. port in use?
		stdlog.Errorf("listen and server port: %d, error: %s", h.port, err.Error())
		return err
	}
	// wait for goroutine started in startHttpServer() to stop
	stdlog.Infof("server listening on %d exited", h.port)
	return nil
}

func (h *httpListener) getNodeByPath(path string) (*ListenHttpNode, bool) {
	if h == nil || h.ns == nil {
		return nil, false
	}
	n, ok := h.ns[path]
	return n, ok
}

// register 将 *ListenHttpNode 注册为一个 httpListener 的某个路由的 handler
// 如果 path 已被使用，则返回 error
func (h *httpListener) register(n *ListenHttpNode) error {
	port := n.pn.Port
	path := n.pn.Path
	if h.ns == nil {
		h.ns = make(map[string]*ListenHttpNode)
	}
	if h.port != int(port) {
		return stderr.InvalidParam.Error("unmatched port while registering *ListenHttpNode %d != %d", h.port, n.pn.Port)
	}
	_, ok := h.ns[path]
	if ok {
		return stderr.InvalidParam.Error("already exist listen node using port %d and path %s", n.pn.Port, n.pn.Path)
	}

	stdlog.Infof("registered for ListenHttpNode on port %d and path %s", port, path)
	h.ns[path] = n
	return nil
}

func (h *httpListener) unregister(n *ListenHttpNode) {
	path := n.pn.Path
	if h.ns == nil {
		return
	}
	delete(h.ns, path)
}

func (h *httpListener) req2Point(reqID string, body map[string]any) *ReqPoint {
	tags := map[string]string{
		"node": "listen http",
	}
	output := body
	fields := models.Fields{
		models.PredefinedFieldOutput: output,
	}
	return &ReqPoint{
		PointMessage: edge.NewPointMessage(reqID, "thinger", "autogen", models.Dimensions{}, fields, tags, time.Now()),
		body:         body,
		reqID:        reqID,
	}
}

// ServeHTTP 开始处理请求
func (h *httpListener) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	w = stdsrv.GetCurrentWriter(w)
	if r.URL.Path == CheckHealthPath {
		checkHealthHandler(w, h.ns)
		return
	}

	bs, err := io.ReadAll(r.Body)
	if err != nil {
		stdlog.Debugf("read request body error: %s", err.Error())
		return
	}

	path, kvs, err := handleRequestFormat(r.URL.Path, bs)
	if err != nil {
		stdlog.WithError(err).Error("failed to handle request format")
		return
	}
	reqID := uuid.New().String()
	n, ok := h.getNodeByPath(path)
	if !ok {
		stdlog.Errorf("node with path %s not found", path)
		_ = stdsrv.SSESendError(w, reqID, stderr.Errorf("node with path %s not found", path))
		return
	}
	taskID := n.taskID
	nodeMeta := n.pn.NodeMeta
	p := h.req2Point(reqID, kvs)
	// 获取链中的所有节点
	chainNodes, err := getChainNodes(n)
	if err != nil {
		stdlog.Errorf("get chain nodes error: %s", err.Error())
		_ = stdsrv.SSESendError(w, reqID, stderr.Wrap(err, "get chain nodes error"))
		return
	}
	// 为每个请求的每个算子创建一个互斥锁，保证并发时正确的消息顺序
	mus := make(map[string]*sync.Mutex)
	for _, node := range chainNodes {
		// node.Name() = n.Desc() + n.ID(), 链中唯一名称
		mus[node.Name()] = &sync.Mutex{}
	}
	c, err := p.newCall(defaultHttpTimeout, n.Entry, r, w, mus)
	if err != nil {
		return
	}
	h.calls.Set(p.reqID, c, defaultHttpTimeout)
	taskCtx := models.Context{
		TaskID: taskID,
		Call:   c,
		Meta:   nodeMeta,
	}

	if err = p.SetContext(taskCtx); err != nil {
		n.WithError(err).Errorf("set context of point message")
		return
	}
	// 设置当前应用链开始运行状态
	if err := edge.PubRunningStatus(taskID, c, nodeMeta, debug.DEBUG_SCOPE_CHAIN); err != nil {
		_ = stdsrv.SSESendError(w, reqID, stderr.Wrap(err, "set running status of chain %s", nodeMeta.ChainName))
		return
	}
	for _, out := range n.outs {
		if err = out.Collect(p); err != nil {
			fmt.Printf("sending point message to edge error: %s", err.Error())
			continue
		}
	}
	c.Wait()
	h.calls.Delete(p.reqID)
}

// RecvMessage  接收httpRespNode节点的消息,作为最后输出
func (h *httpListener) RecvMessage(name string, m edge.Message) error {
	v, ok := h.calls.Get(name)
	if !ok {
		stdlog.Warnf("no corressponding session found for request %s", name)
		return nil
	}
	c, ok := v.(*debug.Call)
	if !ok {
		stdlog.Warnf("unexpected value type in cache, got %T", v)
		return nil
	}
	return handleMsg(c, m)
}

// handleMsg 处理请求过程中，下游算子返回的数据
func handleMsg(c *debug.Call, m edge.Message) (err error) {
	if c == nil {
		return errors.New("handle msg with nil call")
	}
	if c.Done() {
		return errors.New("handle msg with a completed call")
	}
	defer func() {
		if err != nil {
			http.Error(c.W(), err.Error(), http.StatusInternalServerError)
		}
	}()

	overallDebugMessage := c.GetOverallDebugMessage()
	switch msg := m.(type) {
	case edge.PointMessage: // 正常响应
		var trace models.Trace
		trace, err = msg.Trace()
		if err != nil {
			return stderr.Wrap(err, "get trace from point message")
		}
		output, e := extracOutputFromFields[any](msg.Fields())
		if e != nil {
			return stderr.Wrap(err, "failed to extract output from fields")
		}
		meta, e := msg.Meta()
		if e != nil {
			stdlog.Warnf("get meta from point message %v", e)
		}
		// 设置回复时延
		overallDebugMessage.FirstRespLatencyMs = time.Now().UnixMilli() - overallDebugMessage.StartTime
		// 设置输出
		overallDebugMessage.SetOutput(output, false)
		// 设置应用链运行成功状态
		if err = edge.PubSuccessStatus(c.ReqID(), c, meta, debug.DEBUG_SCOPE_CHAIN, false); err != nil {
			return stderr.Wrap(err, "set success status of chain %s", meta.ChainName)
		}
		if c.GetResponseModel() == debug.ResponseModeBlocking {
			err = sendPointMessageBlockingResponse(c, output, overallDebugMessage, nil)
		} else {
			err = sendPointMessageStreamingResponse(c, output, trace)
		}
		if err != nil {
			return err
		}
		c.Complete()

	case edge.BeginBatchMessage: // 开始进行流式返回
		stdlog.Infof("start sending server event")
		trace := msg.Trace()
		if err := sendTraceSSE(c, trace); err != nil {
			return err
		}
	case edge.BatchPointMessage: // 流式返回过程中的一个事件
		output, e := extracOutputFromFields[any](msg.Fields())
		if e != nil {
			return stderr.Wrap(err, "failed to extract output from fields")
		}
		sendData, e := putDataToResponse(output, c.IsPretty())
		if e != nil {
			return stderr.Wrap(e, "put data to response %+v", output)
		}
		if err = stdsrv.SSESendData(c.W(), c.ReqID(), sendData); err != nil {
			return
		}
		// 计算首字时延
		if overallDebugMessage.FirstRespLatencyMs == 0 {
			overallDebugMessage.FirstRespLatencyMs = time.Now().UnixMilli() - overallDebugMessage.StartTime
		}
		// 累加输出
		return overallDebugMessage.SetOutput(output, true)
	case edge.EndBatchMessage: // 流式返回结束
		// 设置应用链运行成功状态
		meta, _ := msg.Meta()
		if err = edge.PubSuccessStatus(c.ReqID(), c, meta, debug.DEBUG_SCOPE_CHAIN, true); err != nil {
			return stderr.Wrap(err, "set success status of chain %s", meta.ChainName)
		}
		// 发送debug event
		if err = sendDebugSSE(c, debug.NODE_STATUS_SUCCESS); err != nil {
			return stderr.Wrap(err, "failed to send debug event")
		}
		if err = stdsrv.SendClose(c.W(), c.ReqID()); err != nil {
			return
		}
		c.Complete()
	// case edge.BufferedBatchMessage: // 流式响应，但一次性返回
	default:
		return stderr.Error("invalid message type %T", m)
	}
	return nil
}

// ReqPoint 将输入的请求 Point 转换为一个标准的请求体结构，并保留上下文
type ReqPoint struct {
	edge.PointMessage
	reqID string
	body  map[string]any

	call *debug.Call
}

const (
	ReqPointKeyID   = "reqID"
	ReqPointKeyBody = "body"
)

func (r *ReqPoint) newCall(timeout time.Duration, entry *logrus.Entry, req *http.Request, w http.ResponseWriter, mus map[string]*sync.Mutex) (*debug.Call, error) {
	if r.call != nil {
		return r.call, nil
	}
	ctx, ok := helper.GenCtxFromRequest(req)
	if !ok {
		return nil, stderr.Errorf("failed to gen ctx from request")
	}
	ctx, cancel := context.WithTimeout(ctx, timeout)
	// make overalldebug message
	overallDebugMessage := &debug.OverallDebugMessage{
		ReqID:         r.reqID,
		Status:        debug.NODE_STATUS_RUNNING,
		StartTime:     time.Now().UnixMilli(),
		EndTime:       0,
		Input:         r.body,
		DebugMessages: []*debug.DebugMessage{},
	}
	r.call = debug.NewCall(entry, r.reqID, req, w, ctx, cancel, mus, overallDebugMessage)
	return r.call, nil
}

// HandleError 用于处理在本次调用过程中发生的错误，返回给调用者
func HandleError(call *debug.Call, meta models.NodeMeta, err error) {
	if err == nil {
		return
	}
	if call == nil {
		stdlog.WithError(err).Errorf("recv error but call not initialized")
		return
	}
	isCanceledStatus := err == context.Canceled
	reqID := call.ReqID()
	// 设置报错算子错误状态
	if e := edge.PubErrorStatus(reqID, call, meta, debug.DEBUG_SCOPE_NODE, isCanceledStatus); e != nil {
		err = stderr.JoinErrors(stderr.Error("publish error status of node %s", meta.NodeName), e, err)
	}
	// 子链错误状态，如果SubChainID不为空，此节点为子链内部节点
	if meta.SubChainID != "" {
		if e := edge.PubErrorStatus(reqID, call, meta, debug.DEBUG_SCOPE_SUB_CHAIN, isCanceledStatus); e != nil {
			err = stderr.JoinErrors(stderr.Error("publish error status of sub chain %s", meta.SubChainName), e, err)
		}
	}
	// 设置主链错误状态
	if e := edge.PubErrorStatus(reqID, call, meta, debug.DEBUG_SCOPE_CHAIN, isCanceledStatus); e != nil {
		err = stderr.JoinErrors(stderr.Error("publish error status of chain %s", meta.ChainName), e, err)
	}
	// 发送json形式的error数据
	if call.GetResponseModel() == debug.ResponseModeBlocking {
		// TODO: 设置debug message字段
		sendErr := sendPointMessageBlockingResponse(call, nil, nil, err)
		if sendErr != nil {
			stdlog.WithError(sendErr).Errorf("error when sending error %+v", err)
		}
		call.Complete()
		return
	}
	if e := sendDebugSSE(call, debug.NODE_STATUS_FAILED); e != nil {
		err = stderr.JoinErrors(stderr.Error("failed to send debug event"), e, err)
	}
	if newErr := call.EndWithError(err); newErr != nil {
		stdlog.WithError(newErr).Errorf("anothor error occurred while handling error %s", err)
	}
}

// HandleMsgErr 仅用于处理
func HandleMsgErr(msg edge.Message, meta models.NodeMeta, err error) error {
	if err == nil {
		return nil
	}
	contextGetter, ok := msg.(edge.ContextGetter)
	if !ok {
		return stderr.Error("failed to convert %T to %T", msg, contextGetter)
	}
	ctx, ctxErr := contextGetter.Context()
	if ctxErr != nil {
		return stderr.Wrap(ctxErr, "get context from message")
	}
	HandleError(ctx.Call, meta, err)
	return nil
}

// sendPointMessageBlockingResponse 处理阻塞模式的响应
func sendPointMessageBlockingResponse(c *debug.Call, output any, overallDebugMessage *debug.OverallDebugMessage, err error) error {
	response := &stdsrv.ChainBlockingResponse{
		Id:       c.ReqID(),
		Response: output,
		Error:    err,
	}
	if c.IsDebug() && overallDebugMessage != nil {
		// TODO: 补全overallDebugMessage 信息，在sendDebugMessage函数中有处理逻辑
		response.Debug = overallDebugMessage
	}
	if err := writeJsonResponse(c.W(), response); err != nil {
		return stderr.Wrap(err, "write json response")
	}
	return nil
}

// sendPointMessageStreamingResponse 处理流式模式的响应
func sendPointMessageStreamingResponse(c *debug.Call, output any, trace models.Trace) error {
	if err := sendTraceSSE(c, trace); err != nil {
		return err
	}
	sendData, err := putDataToResponse(output, c.IsPretty())
	if err != nil {
		return stderr.Wrap(err, "put data to response %+v", output)
	}
	if err = stdsrv.SSESendData(c.W(), c.ReqID(), sendData); err != nil {
		return err
	}

	// 发送debug event
	if err = sendDebugSSE(c, debug.NODE_STATUS_SUCCESS); err != nil {
		return stderr.Wrap(err, "failed to send debug event")
	}
	if err = stdsrv.SendClose(c.W(), c.ReqID()); err != nil {
		return err
	}
	return nil
}

func marshalWithPretty(data any, pretty bool) (result string, err error) {
	var bs []byte
	if pretty {
		bs, err = json.MarshalIndent(data, "", "  ")
	} else {
		bs, err = json.Marshal(data)
	}
	if err != nil {
		return
	}
	result = string(bs)
	return
}

func putDataToResponse(data any, pretty bool) (any, error) {
	var rsp any
	if isJson(data) {
		bs, err := marshalWithPretty(data, pretty)
		if err != nil {
			return nil, err
		}
		rsp = formatJsonAsMarkdown(string(bs), pretty)
	} else {
		rsp = data
	}
	return &stdsrv.Response{
		Response: rsp,
	}, nil
}

func isJson(v any) bool {
	switch v.(type) {
	case bool:
		return false
	case int, int8, int16, int32, int64:
		return false
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return false
	case float32, float64:
		return false
	case complex64, complex128:
		return false
	case string:
		return false
	default:
		return true
	}
}

const (
	JSON_MARKDOWN_PREFIX = "```json\n"
	JSON_MARKDOWN_SUFFIX = "\n```"
)

// TODO: 后期定义MarkdownFormat接口，定义不同类型的Output结构体，实现MarkdownFormat接口
func formatJsonAsMarkdown(jsonStr string, pretty bool) string {
	if pretty {
		return JSON_MARKDOWN_PREFIX + jsonStr + JSON_MARKDOWN_SUFFIX
	} else {
		return jsonStr
	}
}

const (
	DEBUG_QUERY_NAME       = "debug"
	DEBUG_QUERY_VALUE_TRUE = "true"
)
