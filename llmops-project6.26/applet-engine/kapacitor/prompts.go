package kapacitor

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"transwarp.io/applied-ai/aiot/vision-std/triton"

	//"strconv"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"
	"unicode/utf8"

	"github.com/sashabaranov/go-openai"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"

	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

const (
	// doFunctionCallingChat函数中读取channel数据的超时时间
	ReadChannelTimeout = 20 * time.Second
)

type AgentAssistantNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.AgentAssistantNode // 算子的参数定义
}

// newAgentAssistantNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newAgentAssistantNode(et *ExecutingTask, n *pipeline.AgentAssistantNode, d NodeDiagnostic) (*AgentAssistantNode, error) {
	hn := &AgentAssistantNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *AgentAssistantNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

// TODO: Final Answer使用流式输出，api模式下流式输出开关，应用体验页面是否展示思考过程，当后面的算子无法支持流式消息时，要发送pointmessage
func (n *AgentAssistantNode) Point(p edge.PointMessage) (err error) {
	_, ctxPtr, call, err := util.GetContexts(p)
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	ctx := *ctxPtr

	history := ""
	citations := []*agent_executor.Citation{}
	// 先尝试获取 string 类型的 output
	question, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		// 如果获取 map 类型失败，尝试获取 map[string]any 类型
		outputMap, mapErr := extracOutputFromFields[map[string]any](p.Fields())
		if mapErr != nil {
			return stderr.Wrap(err, "extract output from fields failed for both map and string types")
		}
		// 提取问题，不允许问题不存在
		var questionErr error
		var exist bool
		question, exist, questionErr = models.GetValueOfFields[string](outputMap, models.PredefinedFieldQuestion)
		if questionErr != nil || !exist {
			return stderr.Wrap(questionErr, "extract question from fields")
		}
		// 提取历史记录，允许历史记录不存在
		var historyErr error
		history, _, historyErr = models.GetValueOfFields[string](outputMap, models.PredefinedFieldHistory)
		if historyErr != nil {
			return stderr.Wrap(historyErr, "extract history from fields")
		}
		// 提取知识库检索结果，允许知识库检索结果不存在
		knowledgeCitations, knowledgeCitationsErr := agent_executor.TryCvt2Citations(outputMap[models.PredefinedFieldKnowledge], agent_executor.CitationTypeTextKnowledge)
		if knowledgeCitationsErr != nil {
			return stderr.Wrap(knowledgeCitationsErr, "extract knowledgeCitations from fields")
		}
		citations = append(citations, knowledgeCitations...)
		// 提取互联网搜索结果，允许互联网搜索结果不存在
		internetCitations, internetCitationsErr := agent_executor.TryCvt2Citations(outputMap[models.PredefinedFieldInternet], agent_executor.CitationTypeInternetSearch)
		if internetCitationsErr != nil {
			return stderr.Wrap(internetCitationsErr, "extract internetCitations from fields")
		}
		citations = append(citations, internetCitations...)
	}
	citationTexts := agent_executor.CvtCitations2CitationTexts(citations)
	ctx.Infof("====== citation texts ======\n%s", citationTexts)
	if err := sendCitationSSE(call, citations); err != nil {
		return stderr.Wrap(err, "send citation sse")
	}

	fileUrl := fmt.Sprintf("file sfs protocol url: %s\n file http protocol url: %s", ctx.FileSfsUrl, ctx.FileHttpUrl)
	ctx.Infof("====== file url ======\n%s", fileUrl)

	agentConfig := n.pn.AgentConfig
	customInstruction := agentConfig.Prompt
	// 在遍历tool的时候保存 name_for_model 到 describer的映射，传给执行器直接执行
	name2Describer := make(map[string]agent_definition.ToolDescriber)
	// 保存api tools
	tools := make([]agent_definition.Tool, 0)
	for _, collection := range agentConfig.APICollections {
		if collection == nil {
			continue
		}
		for _, apiTool := range collection.AgentTools {
			apiToolDef := apiTool.Definition()
			tools = append(tools, apiToolDef)
			apiToolCopy := apiTool
			name2Describer[apiToolDef.NameForModel] = &apiToolCopy
		}
	}
	// 保存knowledge tools
	for _, knowTool := range agentConfig.KnowledgeBases.KnowledgeBaseDesc {
		if knowTool == nil {
			continue
		}
		knowToolDef := knowTool.Definition()
		tools = append(tools, knowToolDef)
		knowToolCopy := knowTool
		name2Describer[knowToolDef.NameForModel] = knowToolCopy
	}

	// 保存 system service tools
	for _, systemService := range agentConfig.SystemServices {
		var tool agent_definition.ToolDescriber
		if systemService.ModelService != nil {
			tool = systemService.ModelService
		} else if systemService.AppletService != nil {
			tool = systemService.AppletService
		}
		if tool != nil {
			toolDef := tool.Definition()
			tools = append(tools, toolDef)
			name2Describer[toolDef.NameForModel] = tool
		}
	}

	toolNameList := make([]string, 0)
	toolDescriptionList := make([]string, 0)
	for _, tool := range tools {
		toolNameList = append(toolNameList, tool.NameForModel)
		description, err := models.FillToolDescription(tool)
		if err != nil {
			return stderr.Wrap(err, "fill tool description")
		}
		toolDescriptionList = append(toolDescriptionList, description)
	}
	toolNames := strings.Join(toolNameList, ",")
	toolDescriptions := strings.Join(toolDescriptionList, "")

	ctx.Infof("====== toolNames ======\n%s", toolNames)
	ctx.Infof("====== toolDescriptions ======\n%s", toolDescriptions)

	// 填充普通提示词模板
	simplePrompt := models.FillSimplePrompt(customInstruction, history, citationTexts, fileUrl, question)
	ctx.Infof("====== simplePrompt ======\n%s", simplePrompt)
	// 如果需要调用工具，则根据不同的AgentMode选择不同的chat方法
	if toolNames != "" {
		// 根据不同的AgentMode选择不同的chat方法
		switch n.pn.AgentConfig.AgentMode {
		case agent_definition.AgentModeFunctionCalling:
			return n.doFunctionCallingChat(ctx, p, name2Describer, simplePrompt, tools, citations)
		case agent_definition.AgentModeReAct:
			reActPrompt := models.FillPromptWithReAct(customInstruction, history, citationTexts, fileUrl, toolNames, toolDescriptions, question)
			ctx.Infof("====== reActPrompt ======\n%s", reActPrompt)
			return n.doReActChat(ctx, p, name2Describer, reActPrompt, citations)
		default:
			return stderr.Errorf("unsupported agent mode: %s", n.pn.AgentConfig.AgentMode)
		}
	}
	// 如果不需要调用工具，则使用普通提示词模板
	return n.doSimpleChat(ctx, p, simplePrompt, citations)
}

func (n *AgentAssistantNode) doReActChat(ctx models.Context, p edge.PointMessage, name2Describer map[string]agent_definition.ToolDescriber, reActPrompt string, citations []*agent_executor.Citation) (err error) {
	// callCtx 模型调用上下文
	// forwarder  往下游发送信息
	callCtx, forwarder, err := prepareUtil(n, p)
	if err != nil {
		return stderr.Wrap(err, "failed to prepare util")
	}
	defer func() {
		err = stderr.JoinErrors(err, forwarder.Close())
	}()

	// 记录本轮次调用的各种情况
	var inThink bool                       // 处于think中
	var totalBuffer string                 // 记录本轮次调用,完整的文本
	var pendingBuffer string               // 记录本轮次调用,还未输出的文本
	var hasFinalAnswer bool                // 发现最终结果
	var hasStopWordObservation bool        // 发现stop_word
	var callCtxCopy context.Context        // 用于发起本轮次模型调用
	var stopCurrentChat context.CancelFunc //用于主动终止本轮次模型调用

	// 初始化引用匹配相关变量
	var lastParagraph string
	var inCodeBlock bool
	var inHeading bool

	// 实时处理模型的流式输出
	streamHandler := func(textContent string) error {
		totalBuffer += textContent
		// 4、直接输出FinalAnswer之后的内容,并添加引用
		if hasFinalAnswer {
			// 累加段落
			lastParagraph += textContent
			// 检测代码块
			if strings.Contains(textContent, "```") {
				inCodeBlock = !inCodeBlock
			}
			// 检测标题
			if strings.Contains(textContent, "#") {
				inHeading = true
			}
			// 检测换行符
			if strings.Contains(textContent, "\n") {
				// 段落去匹配上下文的相似度并添加引用序号
				textContent, err = n.handleCitations(ctx, textContent, citations, &lastParagraph, &inCodeBlock, &inHeading)
				if err != nil {
					return stderr.Wrap(err, "handle citations")
				}
			}
			return forwarder.Emit(textContent)
		}

		// 等待输出的文本
		pendingBuffer += textContent

		if hasStopWordObservation {
			return nil
		}

		// 检测到stop_word,则去除文本中的stop_word并往后传递。(正常情况模型在输出stop_word之前会停止)
		if index := strings.Index(pendingBuffer, models.StopWordObservation); index != -1 {
			stopCurrentChat()
			hasStopWordObservation = true
			pendingBuffer = pendingBuffer[:index]
		}

		// 1、思考内容直接发送。
		if index := strings.Index(pendingBuffer, models.ThinkStart); index != -1 {
			inThink = true
			temp := pendingBuffer
			pendingBuffer = ""
			return forwarder.Emit(temp)
		}

		if inThink {
			//  2、截留ThinkEnd之后的文本不输出,用于reAct调用
			if index := strings.Index(pendingBuffer, models.ThinkEnd); index != -1 {
				inThink = false
				prefix := pendingBuffer[:index+len(models.ThinkEnd)]
				pendingBuffer = strings.TrimPrefix(pendingBuffer, prefix)
				return forwarder.Emit(prefix)
			} else {
				temp := pendingBuffer
				pendingBuffer = ""
				return forwarder.Emit(temp)
			}
		}

		// 3、模型输出FinalAnswer,reAct成功。直接输出后续文本并停止下一轮模型调用
		if index := strings.Index(pendingBuffer, models.FinalAnswer); index != -1 {
			hasFinalAnswer = true
			subfix := pendingBuffer[index+len(models.FinalAnswer):]
			pendingBuffer = ""
			return forwarder.Emit(subfix)
		}

		// 4、检测到模型输出不遵循reAct指令,按情况3处理,直接输出后续文本并停止下一轮模型调用
		// 标准reAct格式  Thought:xx Action:xx Action Input:xx 或 Thought:xx Final Answer:xx
		if len(pendingBuffer) >= conf.Config.Agent.ReActDetectionLength && !util.ContainsAny(pendingBuffer, []string{models.Thought, models.Action, models.ActionInput, models.FinalAnswer}) {
			hasFinalAnswer = true
			temp := pendingBuffer
			pendingBuffer = ""
			return forwarder.Emit(temp)
		}
		return nil
	}

	// ReAct 循环
	var thoughtRounds int
	maxThoughtRounds := conf.Config.Agent.MaxThoughtRounds
	for thoughtRounds = 0; thoughtRounds < maxThoughtRounds; thoughtRounds++ {
		//0、初始化各标志位
		inThink = false
		totalBuffer = ""
		pendingBuffer = ""
		hasFinalAnswer = false
		hasStopWordObservation = false
		callCtxCopy, stopCurrentChat = context.WithCancel(callCtx)

		//1、调用模型
		modelCallLog := newModelCallLog(n, p, reActPrompt)
		if err = n.doChatStream(reActPrompt, callCtxCopy, streamHandler); err != nil {
			stopCurrentChat()
			modelCallLog.Failed(totalBuffer)
			return stderr.Wrap(err, "do chat stream")
		}
		modelCallLog.Succeed(totalBuffer)

		if inThink { // 处理模型不输出ThinEnd
			forwarder.Emit("\n" + models.ThinkEnd)
		}

		if hasFinalAnswer {
			break
		}

		//2、提取reAct文本
		reActAction, err := models.ExtractReActText(totalBuffer)
		if err != nil {
			ctx.Errorf("failed to extract reAct text from %s: %v", totalBuffer, err)
			return forwarder.Emit(pendingBuffer) // reAct失败,将剩余文本输出并终止
		}

		//3、执行工具
		reActAction, err = n.toolCall(p, reActAction, name2Describer, &citations)
		if err != nil {
			return stderr.Wrap(err, "do tool call ") // 工具执行时Thought、Action等事件发送失败,终止执行
		}

		//4、更新提示词
		reActPrompt += models.FillReActScratchpad(reActAction.Thought, reActAction.Action, reActAction.ActionInput, reActAction.Observation)
	}

	if thoughtRounds >= maxThoughtRounds {
		errInfo := fmt.Sprintf("thought rounds %d reached max", thoughtRounds)
		ctx.Errorf(errInfo)
		return stderr.Errorf(errInfo)
	}

	// 处理最后一段文本的引用匹配
	if lastParagraph != "" && len(citations) > 0 {
		finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
		if err != nil {
			return stderr.Wrap(err, "citation matching for last paragraph")
		}
		// 如果引用编号不为空，则发送引用编号
		if finalCitationNumStr != "" {
			if err := forwarder.Emit(finalCitationNumStr); err != nil {
				return stderr.Wrap(err, "emit final citation numbers")
			}
		}
	}
	return nil
}

// doToolCall 调用工具
func (n *AgentAssistantNode) toolCall(p edge.PointMessage, actionInfo *models.ReActAction, name2Describer map[string]agent_definition.ToolDescriber, citations *[]*agent_executor.Citation) (*models.ReActAction, error) {
	callCtx, ctx, call, err := util.GetContexts(p)
	if err != nil {
		return nil, stderr.Wrap(err, "get context from point message")
	}
	thought := actionInfo.Thought
	action := actionInfo.Action
	actionInput := actionInfo.ActionInput

	if err = sendThoughtSSE(call, thought); err != nil {
		return nil, stderr.Wrap(err, "failed to send thought event")
	}

	var structActionInput any
	if err = json.Unmarshal([]byte(actionInput), &structActionInput); err != nil {
		return nil, stderr.Wrap(err, "unmarshal action input: %s", actionInput)
	}

	tool, ok := name2Describer[action]
	if !ok {
		return nil, stderr.Errorf("unknown tool %s", action)
	}

	toolDef := tool.Definition()
	if err = sendActionSSE(call, toolDef, structActionInput); err != nil {
		return nil, stderr.Wrap(err, "failed to send action event")
	}

	tooCallLog := newToolCallLog(n, p, structActionInput, tool)
	// 执行对应工具
	var observation string
	var executionErr error
	switch tool.Type() {
	case agent_definition.ToolTypeAPITool:
		observation, executionErr = n.executeAPITool(*ctx, tool, structActionInput, citations)
	case agent_definition.ToolTypeKnowledgeHub:
		observation, executionErr = n.executeKnowledgeHubTool(*ctx, tool, structActionInput, citations)
	case agent_definition.ToolTypeModelService:
		observation, executionErr = clients.ModelToolExecutor.Execute(callCtx, tool, structActionInput)
	case agent_definition.ToolTypeAppletService:
		observation, executionErr = clients.AppletToolExecutor.Execute(callCtx, tool, structActionInput)
	default:
		return nil, stderr.Errorf("unsupported tool type %s", tool.Type())
	}

	if executionErr != nil {
		observation = fmt.Sprintf("Tool execution error - %s: %v", toolDef.NameForModel, executionErr)
	}

	tooCallLog.Succeed(observation)
	if err = sendObservationSSE(call, toolDef, observation); err != nil {
		return nil, stderr.Wrap(err, "failed to send observation event")
	}

	return &models.ReActAction{Thought: thought, Action: action, ActionInput: actionInput, Observation: observation}, nil
}

// doFunctionCallingChat 实现function calling方式的chat
func (n *AgentAssistantNode) doFunctionCallingChat(ctx models.Context, p edge.PointMessage, name2Describer map[string]agent_definition.ToolDescriber, simplePrompt string, tools []agent_definition.Tool, citations []*agent_executor.Citation) error {
	// TODO: debugMessage设置
	call := ctx.Call
	callCtx := call.Ctx()
	needStream := needStreamOutput(n.children)
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin")
		}
	}

	// 将 agent_definition.Tool 转换为 openai.Tool
	openaiTools, err := convertToOpenAITools(tools)
	if err != nil {
		return stderr.Wrap(err, "convert to openai tools")
	}

	// 初始化消息
	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleUser,
			Content: simplePrompt,
		},
	}

	// 初始化引用匹配相关变量
	var lastParagraph string
	var inCodeBlock bool
	var inHeading bool

	// 创建OpenAI客户端
	llmModelSvc := n.pn.AgentConfig.LLMModelSvc
	openaiBaseUrl, err := getOpenaiBaseUrl(llmModelSvc.FullUrl)
	if err != nil {
		return stderr.Wrap(err, "failed to get openai base url")
	}
	openaiApiKey, err := getOpenaiApiKey(callCtx)
	if err != nil {
		return stderr.Wrap(err, "failed to get openai api key")
	}
	openaiClient := clients2.NewOpenaiClient(openaiBaseUrl, openaiApiKey)
	var thoughtRounds int
	maxThoughtRounds := conf.Config.Agent.MaxThoughtRounds
	for thoughtRounds = 0; thoughtRounds < maxThoughtRounds; thoughtRounds++ {
		ctx.Infof("开始第 %d 轮function calling", thoughtRounds+1)
		ctx.Infof("messages:\n %+v", messages)
		baseChatReq, err := clients2.GetBaseChatReq(llmModelSvc)
		if err != nil {
			return stderr.Wrap(err, "failed to get base chat req")
		}
		openaiReq := openai.ChatCompletionRequest{
			Model:       baseChatReq.Model,
			Messages:    messages,
			Tools:       openaiTools,
			ToolChoice:  "auto",
			Temperature: float32(baseChatReq.Temperature),
			TopP:        float32(baseChatReq.TopP),
			MaxTokens:   baseChatReq.MaxTokens,
			Stop:        baseChatReq.Stop,
		}

		currentRoundToolCalls := make([]openai.ToolCall, 0)
		currentRoundContent := ""

		if needStream {
			streamChan := openaiClient.ChatWithToolsStream(callCtx, openaiReq)
			timer := time.NewTimer(ReadChannelTimeout)

			// 接收模型输出
		streamLoop:
			for {
				select {
				case response, ok := <-streamChan:
					if !ok {
						ctx.Infof("chat stream channel 关闭，正常退出流式输出")
						break streamLoop
					}
					// 收到数据后重置定时器
					if !timer.Stop() {
						<-timer.C
					}
					timer.Reset(ReadChannelTimeout)

					if response.Error != nil {
						return response.Error
					}

					if response.Content != "" {
						currentRoundContent += response.Content

						// 处理引用匹配
						// 累加段落
						lastParagraph += response.Content
						// 检测代码块
						if strings.Contains(response.Content, "```") {
							inCodeBlock = !inCodeBlock
						}
						// 检测标题
						if strings.Contains(response.Content, "#") {
							inHeading = true
						}
						// 如果检测到换行符，进行引用匹配
						if strings.Contains(response.Content, "\n") {
							var citationErr error
							citatedContent, citationErr := n.handleCitations(ctx, response.Content, citations, &lastParagraph, &inCodeBlock, &inHeading)
							if citationErr != nil {
								return stderr.Wrap(citationErr, "handle citations")
							}
							// 使用添加了引用的内容进行发送
							if err := n.emitBatch(ctx, citatedContent); err != nil {
								return stderr.Wrap(err, "emit content batch")
							}
						} else {
							// 没有换行符时直接发送内容
							if err := n.emitBatch(ctx, response.Content); err != nil {
								return stderr.Wrap(err, "emit content batch")
							}
						}
					}

					if response.ToolCalls != nil {
						ctx.Infof("收到工具调用: %+v", response.ToolCalls)
						currentRoundToolCalls = response.ToolCalls
					}

				case <-timer.C:
					ctx.Infof("chat stream 读取响应超时, 抛出错误")
					return stderr.Errorf("chat stream 读取响应超时")
				}
			}

			// 处理最后一段文本的引用匹配
			if lastParagraph != "" && len(citations) > 0 {
				finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
				if err != nil {
					return stderr.Wrap(err, "citation matching for last paragraph")
				}
				// 如果引用编号不为空，则发送引用编号
				if finalCitationNumStr != "" {
					if err := n.emitBatch(ctx, finalCitationNumStr); err != nil {
						return stderr.Wrap(err, "emit final citation numbers")
					}
				}
			}
		} else {
			// 非流式工具调用逻辑
			content, toolCalls, err := openaiClient.ChatWithToolsBlock(callCtx, openaiReq)
			if err != nil {
				return stderr.Wrap(err, "chat with tools block")
			}
			currentRoundContent = content
			currentRoundToolCalls = toolCalls

			// 如果模型返回结果，且没有工具调用，则作为最终结果发送
			if content != "" && len(toolCalls) == 0 {
				if err := n.emitPoint(p, content); err != nil {
					return stderr.Wrap(err, "emit content point")
				}
			}
			// 如果模型返回结果，且有工具调用，则作为中间思考过程发送
			if content != "" && len(toolCalls) > 0 {
				if err := sendThoughtSSE(call, content); err != nil {
					return stderr.Wrap(err, "failed to send thought event")
				}
			}
		}

		// 如果没有工具调用，结束对话
		if len(currentRoundToolCalls) == 0 {
			break
		}
		currentRoundMessage := openai.ChatCompletionMessage{
			Role:      openai.ChatMessageRoleAssistant,
			Content:   currentRoundContent,
			ToolCalls: currentRoundToolCalls,
		}
		messages = append(messages, currentRoundMessage)
		// 处理每个工具调用
		for _, toolCall := range currentRoundToolCalls {
			toolName := toolCall.Function.Name
			toolInput := toolCall.Function.Arguments

			// 查找工具
			tool, ok := name2Describer[toolName]
			if !ok {
				return stderr.Errorf("未知工具 %s", toolName)
			}

			// 解析工具输入
			var unmarshaledToolInput any
			if err := json.Unmarshal([]byte(toolInput), &unmarshaledToolInput); err != nil {
				return stderr.Wrap(err, "unmarshal tool input: %s", toolInput)
			}

			// 发送工具调用事件
			toolDef := tool.Definition()
			if err := sendActionSSE(call, toolDef, unmarshaledToolInput); err != nil {
				return stderr.Wrap(err, "failed to send action event")
			}

			// 执行工具
			var toolResult string
			var executionErr error

			switch tool.Type() {
			case agent_definition.ToolTypeAPITool:
				toolResult, executionErr = n.executeAPITool(ctx, tool, unmarshaledToolInput, &citations)
			case agent_definition.ToolTypeKnowledgeHub:
				toolResult, executionErr = n.executeKnowledgeHubTool(ctx, tool, unmarshaledToolInput, &citations)
			case agent_definition.ToolTypeModelService:
				toolResult, executionErr = clients.ModelToolExecutor.Execute(callCtx, tool, unmarshaledToolInput)
			case agent_definition.ToolTypeAppletService:
				toolResult, executionErr = clients.AppletToolExecutor.Execute(callCtx, tool, unmarshaledToolInput)
			default:
				return stderr.Errorf("不支持的工具类型 %s", tool.Type())
			}

			if executionErr != nil {
				toolResult = fmt.Sprintf("工具执行错误 - %s: %v", toolDef.NameForModel, executionErr)
			}

			// 发送工具执行结果事件
			if err := sendObservationSSE(call, toolDef, toolResult); err != nil {
				return stderr.Wrap(err, "failed to send observation event")
			}

			// 将工具响应添加到消息中
			toolResponseMessage := openai.ChatCompletionMessage{
				Role:       openai.ChatMessageRoleTool,
				Content:    toolResult,
				ToolCallID: toolCall.ID,
			}
			messages = append(messages, toolResponseMessage)
		}
	}
	if thoughtRounds >= maxThoughtRounds {
		err_info := fmt.Sprintf("function calling rounds %d reached max", thoughtRounds)
		ctx.Errorf(err_info)
		return stderr.Errorf(err_info)
	}

	if needStream {
		return n.emitEnd(ctx)
	}

	return nil
}

func (n *AgentAssistantNode) executeAPITool(
	ctx models.Context,
	tool agent_definition.ToolDescriber,
	unmarshaledActionInput interface{},
	citations *[]*agent_executor.Citation,
) (string, error) {
	apiCitations, observation, err := clients.ApiToolExecutor.ExecuteReturnCitations(ctx.Call.Ctx(), tool, unmarshaledActionInput)
	if err != nil {
		return "", stderr.Wrap(err, "failed to execute api tool")
	}
	if len(apiCitations) == 0 {
		return observation, nil
	}
	*citations = append(*citations, apiCitations...)
	if err := sendCitationSSE(ctx.Call, *citations); err != nil {
		ctx.Errorf("failed to send citation event: %+v in bing search", err)
	}
	return observation, nil
}

func (n *AgentAssistantNode) executeKnowledgeHubTool(
	ctx models.Context,
	tool agent_definition.ToolDescriber,
	unmarshaledActionInput interface{},
	citations *[]*agent_executor.Citation,
) (string, error) {
	KnowledgeCitations, observation, err := clients.KnowledgeToolExecutor.ExecuteReturnCitations(ctx.Call.Ctx(), tool, unmarshaledActionInput)
	if err != nil {
		return "", stderr.Wrap(err, "failed to execute knowledge hub tool")
	}
	if len(KnowledgeCitations) == 0 {
		return observation, nil
	}
	*citations = append(*citations, KnowledgeCitations...)
	if err := sendCitationSSE(ctx.Call, *citations); err != nil {
		ctx.Errorf("failed to send citation event: %+v in knowledge hub", err)
	}
	return observation, nil
}

// TODO
//  debug.DebugMessage中需要挺多参数,最好提供构造方法来进行初始化。
// 当前各节点、每一次执行时,应该都会初始化各自的debugMsg,并缓存在debugMsgStack中。
// 对于调试日志的推送,只需要在 listen_http节点[链整体] 以及 cdnConsumer.consumer方法[所有单个节点都会经过] 进行处理
// listen_http + cdnConsumer 两个节点会对debugMsg进行初始化、改变、推送、缓存等操作。
// 推送方式的具体实现则取决于debugMsg.pub方法的具体实现  redis、mqtt、sse

func (n *AgentAssistantNode) doSimpleChat(ctx models.Context, p edge.PointMessage, simplePrompt string, citations []*agent_executor.Citation) (err error) {
	callCtx, forwarder, err := prepareUtil(n, p)
	if err != nil {
		return stderr.Wrap(err, "failed to prepare util")
	}
	defer func() {
		err = stderr.JoinErrors(err, forwarder.Close())
	}()

	// 记录完整响应
	var totalBuffer string

	// 初始化引用匹配相关变量
	var inHeading bool
	var inCodeBlock bool
	var lastParagraph string

	// 实时处理模型的流式输出
	streamHandler := func(textContent string) error {
		// 累加段落
		totalBuffer += textContent
		lastParagraph += textContent

		// 检测代码块
		if strings.Contains(textContent, "```") {
			inCodeBlock = !inCodeBlock
		}
		// 检测标题
		if strings.Contains(textContent, "#") {
			inHeading = true
		}
		// 检测换行符
		if strings.Contains(textContent, "\n") {
			// 段落去匹配上下文的相似度并添加引用序号
			textContent, err = n.handleCitations(ctx, textContent, citations, &lastParagraph, &inCodeBlock, &inHeading)
			if err != nil {
				return err
			}
		}
		return forwarder.Emit(textContent)
	}

	modelCallLog := newModelCallLog(n, p, simplePrompt)
	if err = n.doChatStream(simplePrompt, callCtx, streamHandler); err != nil {
		modelCallLog.Failed(totalBuffer)
		return stderr.Wrap(err, "do chat stream")
	}
	modelCallLog.Succeed(totalBuffer)

	// 处理最后一段文本的引用匹配
	if lastParagraph != "" && len(citations) > 0 {
		finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
		if err != nil {
			return stderr.Wrap(err, "citation matching for last paragraph")
		}
		if err = forwarder.Emit(finalCitationNumStr); err != nil {
			return stderr.Wrap(err, "emit final citation numbers")
		}
	}
	return
}

func (n *AgentAssistantNode) doChatBlock(query string, callCtx context.Context, handler func(textContent string) error) error {
	model := n.pn.AgentConfig.LLMModelSvc
	chatReq, err := getAgentChatReq(model, query)
	if err != nil {
		return stderr.Wrap(err, "failed to getAgentChatReq")
	}
	if err := clients2.SyncChat(callCtx, model, chatReq, handler); err != nil {
		return stderr.Wrap(err, "do sync model infer")
	}
	return nil
}

func (n *AgentAssistantNode) doChatStream(query string, callCtx context.Context, handler func(textContent string) error) error {
	model := n.pn.AgentConfig.LLMModelSvc
	chatReq, err := getAgentChatReq(model, query)
	if err != nil {
		return stderr.Wrap(err, "failed to getAgentChatReq")
	}
	if err := clients2.StreamChat(callCtx, model, chatReq, handler); err != nil {
		return stderr.Wrap(err, "do stream model infer")
	}
	return nil
}

//// TODO: 优化此函数代码 and 模型不爱输出"Thought:"
//func (n *AgentAssistantNode) extractContent(text string) (thought, action, actionInput string, err error) {
//	// 定义关键字
//	keywords := []string{"Thought:", "Action:", "Action Input:"}
//	// 检查关键字是否存在且顺序正确，且每个关键字只出现一次
//	lastIndex := -1
//	indexes := make([]int, len(keywords))
//	for i, keyword := range keywords {
//		index := strings.Index(text, keyword)
//		if index == -1 || index <= lastIndex {
//			return "", "", "", stderr.Error("text does not contain the keywords in the correct order or a keyword is missing")
//		}
//		// 检查关键字是否只出现一次
//		if strings.Contains(text[index+len(keyword):], keyword) {
//			return "", "", "", stderr.Error("a keyword appears more than once")
//		}
//		lastIndex = index
//		indexes[i] = index
//	}
//
//	// 提取Thought内容
//	thoughtStart := indexes[0] + len(keywords[0])
//	thoughtEnd := indexes[1]
//	thought = strings.TrimSpace(text[thoughtStart:thoughtEnd])
//
//	// 提取Action内容
//	actionStart := indexes[1] + len(keywords[1])
//	actionEnd := indexes[2]
//	action = strings.TrimSpace(text[actionStart:actionEnd])
//
//	// 提取Action Input内容
//	actionInputStart := indexes[2] + len(keywords[2])
//	actionInput = strings.TrimSpace(text[actionInputStart:])
//
//	return thought, action, actionInput, nil
//}

// 提取Thought/Action/Action Input对应的文本
func (n *AgentAssistantNode) emitPoint(p edge.PointMessage, output string) error {
	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %s into fields", output)
	}
	return n.forwardMsg(p, false)
}

func (n *AgentAssistantNode) emitBegin(m edge.PointMessage) error {
	trace, err := m.Trace()
	if err != nil {
		return stderr.Wrap(err, "get trace from point message")
	}
	ctx, err := m.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	bm := edge.NewBeginBatchMessage(m.Name(), n.pn.NodeMeta, trace, ctx, nil, false, m.Time(), 0)
	return n.forwardMsg(bm, true)
}

func (n *AgentAssistantNode) emitBatch(ctx models.Context, output string) error {
	stdlog.Infof("recv : %s", output)
	fields := models.Fields{
		models.PredefinedFieldOutput:  output,
		models.PredefinedFieldContext: ctx,
	}
	bpm := edge.NewBatchPointMessage(fields, models.Tags{}, time.Now())
	return n.forwardMsg(bpm, true)
}

func (n *AgentAssistantNode) emitEnd(ctx models.Context) error {
	em := edge.NewEndBatchMessage()
	em.SetContext(ctx)
	return n.forwardMsg(em, true)
}

func (n *AgentAssistantNode) handleCitations(ctx models.Context, textContent string, citations []*agent_executor.Citation, lastParagraph *string, inCodeBlock *bool, inHeading *bool) (string, error) {
	if len(citations) == 0 {
		return textContent, nil
	}
	// TODO: 暂时使用知识库中的rerank模型进行引用匹配，后续使用专门的rerank
	rerankModel := n.pn.AgentConfig.RerankModelSvc
	if rerankModel == nil {
		ctx.Warnf("there is no rerank model, skip citation matching")
		return textContent, nil
	}
	// 对非代码、非标题段，且长度超过一定长度，才进行引用匹配
	// TODO: 设置引用阈值0.9，设置引用topK为3
	if !*inCodeBlock && !*inHeading && utf8.RuneCountInString(*lastParagraph) > conf.Config.Citation.MinParagraphLength {
		citationNums, err := matchCitationNums(ctx, *lastParagraph, citations, rerankModel)
		if err != nil {
			return "", stderr.Wrap(err, "match citation nums")
		}
		if len(citationNums) > 0 {
			citationNumStr := " "
			for _, num := range citationNums {
				citationNumStr += fmt.Sprintf("[[%d]]", num+1)
			}
			// 引用序号插入到段落末尾，换行符之前
			if strings.Contains(textContent, "\n") {
				textContent = strings.Replace(textContent, "\n", citationNumStr+"\n", 1)
			} else {
				textContent = textContent + citationNumStr
			}
		}
	}
	// 重置段落和标题状态
	*lastParagraph = ""
	*inHeading = false
	return textContent, nil
}

func matchCitationNums(ctx models.Context, paragraph string, citations []*agent_executor.Citation, rerankModel *pb.ModelService) ([]int, error) {
	if len(citations) == 0 {
		return nil, nil
	}
	call := ctx.Call
	callCtx := call.Ctx()
	texts := make([]string, len(citations))
	text2citationNum := make(map[string]int)
	for i, citation := range citations {
		texts[i] = citation.Content
		text2citationNum[citation.Content] = i
	}
	rerankReq := &triton.RerankReq{
		Query: paragraph,
		Texts: texts,
	}
	rerankRsp, err := clients2.Rerank(callCtx, rerankModel, rerankReq)
	if err != nil {
		ctx.Errorf("rerank request %+v failed: %+v", rerankReq, err)
		return nil, stderr.Wrap(err, "rerank request %+v failed", rerankReq)
	}
	rspTexts := rerankRsp.Texts
	rspScores := rerankRsp.Scores
	if len(rspTexts) != len(rspScores) {
		return nil, stderr.Errorf("rerank response texts and scores length not equal")
	}
	threshold := conf.Config.Citation.RelevanceScoreThreshold
	topK := conf.Config.Citation.TopK
	ctx.Infof("rerank scores: %+v, threshold: %f, topk: %d", rspScores, threshold, topK)
	// 遍历返回的texts 和 scores，如果 score 大于阈值，则将对应的索引添加到 citationNums
	citationNums := make([]int, 0)
	for i, text := range rspTexts {
		citationNum, ok := text2citationNum[text]
		if !ok {
			ctx.Warnf("rerank response text %s not found in citations %+v", text, citations)
			continue
		}
		score := rspScores[i]
		if score > threshold {
			citationNums = append(citationNums, citationNum)
		}
		// rerank接口以文本相似度分数降序返回，取前 k 个
		if len(citationNums) >= topK {
			break
		}
	}
	// 升序排序引用序号
	sort.Ints(citationNums)
	return citationNums, nil
}

func getAgentChatReq(model *pb.ModelService, query string) (*triton.OpenAiChatReq, error) {
	chatReq, err := clients2.GetBaseChatReq(model)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get BaseChatReq")
	}
	chatReq.Stop = append(chatReq.Stop, models.StopWordObservation)
	chatReq.Messages = []triton.MultimodalMessageItem{
		{
			Role:    triton.OpenaiUserMsg,
			Content: query,
		},
	}
	return chatReq, nil
}

func prepareUtil(n *AgentAssistantNode, p edge.PointMessage) (callCtx context.Context, forwarder *autoForwarder, err error) {
	forwarder, err = newAutoForwarder(n, p)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "failed to new auto forwarder")
	}
	callCtx, _, _, err = util.GetContexts(p)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "get context from point message")
	}
	return callCtx, forwarder, nil
}

// autoForwarder 根据下游算子自动维护流式输出或者非流式输出,当前仅限于string类型的output
type autoForwarder struct {
	n             *AgentAssistantNode
	msg           edge.PointMessage
	modelCtx      models.Context
	stream        bool // 是否流式往下游节点发送
	streamStarted bool // 流式发送时,用于记录是否需要发送BeginBatch以及EndBatch
	closed        bool
	buffer        strings.Builder
}

func newAutoForwarder(node *AgentAssistantNode, point edge.PointMessage) (*autoForwarder, error) {
	modelCtx, err := point.Context()
	if err != nil {
		return nil, err
	}
	return &autoForwarder{
		n:        node,
		msg:      point,
		modelCtx: modelCtx,
		stream:   needStreamOutput(node.children),
	}, nil
}

func (e *autoForwarder) Emit(output string) error {
	if e.closed {
		return stderr.Errorf("autoForwarder already closed")
	}
	if e.stream {
		if !e.streamStarted {
			if err := e.n.emitBegin(e.msg); err != nil {
				return err
			}
			e.streamStarted = true
		}
		if len(output) < 10 {
			return e.n.emitBatch(e.modelCtx, output)
		}
		runes := []rune(output)
		for i := 0; i < len(runes); i += 3 {
			end := i + 3
			if end > len(runes) {
				end = len(runes)
			}
			batch := string(runes[i:end])
			if err := e.n.emitBatch(e.modelCtx, batch); err != nil {
				return err
			}
			time.Sleep(100 * time.Millisecond)
		}
		return nil
	} else {
		e.buffer.WriteString(output)
		return nil
	}
}

func (e *autoForwarder) Close() error {
	if e.closed {
		return nil
	}
	e.closed = true
	if e.stream {
		if !e.streamStarted {
			return nil
		}
		//  没有发送BeginBatch而直接发送EndBatch会有问题
		return e.n.emitEnd(e.modelCtx)
	} else {
		return e.n.emitPoint(e.msg, e.buffer.String())
	}
}

type toolCallLogger struct {
	n            *AgentAssistantNode
	msg          edge.PointMessage
	curDebugMsg  *debug.DebugMessage
	roundTracker map[string]int
}

type toolCallLog struct {
	n         *AgentAssistantNode
	msg       edge.PointMessage
	Input     any
	Output    any
	IsSuccess bool
	StartTime int64
	EndTime   int64
	Tool      agent_definition.ToolDescriber
}

func newToolCallLog(node *AgentAssistantNode, pointMsg edge.PointMessage, input any, tool agent_definition.ToolDescriber) *toolCallLog {
	return &toolCallLog{n: node, msg: pointMsg, Input: input, StartTime: time.Now().UnixMilli(), Tool: tool}
}

func newModelCallLog(node *AgentAssistantNode, pointMsg edge.PointMessage, input any) *toolCallLog {
	tool := &agent_definition.ModelToolDescriber{ModelService: *node.pn.AgentConfig.LLMModelSvc}
	return newToolCallLog(node, pointMsg, input, tool)
}

func (t *toolCallLog) Failed(output any) error {
	t.IsSuccess = false
	t.Output = output
	t.EndTime = time.Now().UnixMilli()
	return t.pub()
}
func (t *toolCallLog) Succeed(output any) error {
	t.IsSuccess = true
	t.Output = output
	t.EndTime = time.Now().UnixMilli()
	return t.pub()
}

func (t *toolCallLog) pub() error {
	if t.Tool == nil {
		return stderr.Errorf("tool is nil")
	}
	cDebugMsg, err := util.GetCurDebugMsg(t.n.pn.NodeMeta, t.msg)
	if err != nil {
		return nil
	}
	toolDef := t.Tool.Definition()
	usage := debug.TokenUsage{}
	widgetKey := widgets.WidgetKeyAgent
	switch toolDef.Type {
	case agent_definition.ToolTypeAPITool:
		widgetKey = widgets.WidgetKeyToolCall
	case agent_definition.ToolTypeKnowledgeHub:
		widgetKey = widgets.WidgetKeyTextKnowledgeSearch
	case agent_definition.ToolTypeAppletService:
		widgetKey = widgets.WidgetKeySubChain
	case agent_definition.ToolTypeModelService:
		usage.PromptTokens = int64(len(stdsrv.AnyToString(t.Input)))
		usage.CompletionTokens = int64(len(stdsrv.AnyToString(t.Output)))
		usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
		widgetKey = widgets.WidgetKeyLLM
	default:
		return stderr.Errorf("tool type[%s] is not supported", toolDef.Type)
	}
	status := debug.NODE_STATUS_SUCCESS
	if !t.IsSuccess {
		status = debug.NODE_STATUS_FAILED
	}
	toolLogMsg := &debug.DebugMessage{
		IsDebug: cDebugMsg.IsDebug,
		Call:    cDebugMsg.Call,
		Meta: debug.NodeMeta{
			NodeID:     toolDef.ID + "_" + uuid.New().String(),
			SubChainID: cDebugMsg.Meta.SubChainID,
			NodeName:   toolDef.NameForHuman,
			WidgetKey:  widgetKey,
		},
		DebugMeta: debug.DebugMeta{
			ID:   toolDef.ID,
			Type: debug.DEBUG_TYPE_TOOL,
			Name: toolDef.NameForHuman,
		},
		Status:    status,
		Input:     t.Input,
		Output:    t.Output,
		Usage:     usage,
		StartTime: t.StartTime,
		EndTime:   t.EndTime,
	}

	cDebugMsg.Children = append(cDebugMsg.Children, toolLogMsg)
	return toolLogMsg.Pub()
}
