package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type InputGuardrailNode struct {
	node
	unsupportedBatchNode
	pn             *pipeline.InputGuardrailNode // 算子的参数定义
	securityCensor *triton.SecurityCensor
}

// newInputGuardrailNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newInputGuardrailNode(et *ExecutingTask, n *pipeline.InputGuardrailNode, d NodeDiagnostic) (*InputGuardrailNode, error) {
	securityCensor := triton.NewSecurityCensor(conf.Config.BackendTask.SecurityCensorUrl, triton.DefaultSecurityCensorTimeout)
	hn := &InputGuardrailNode{
		node:           node{Node: n, et: et, diag: d},
		pn:             n,
		securityCensor: securityCensor,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *InputGuardrailNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *InputGuardrailNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	// 未开启安全防护时，直接透明转发
	if !n.pn.ParsedParams.Enable {
		return n.forwardMsg(p, false)
	}
	text, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("call is nil")
	}
	checkSecurityReq := &triton.CheckSecurityRequest{
		ConfigId: n.pn.ParsedParams.SafetyConfig.ID,
		Sentence: text,
	}
	checkSecurityRsp, err := n.securityCensor.CheckInputRisk(call.Ctx(), checkSecurityReq)
	if err != nil {
		return stderr.Wrap(err, "failed to check security")
	}
	// 没有安全风险，直接透明转发
	if !checkSecurityRsp.Risk {
		return n.forwardMsg(p, false)
	}
	return replaceAndCancel(call, checkSecurityRsp.Response)
}
