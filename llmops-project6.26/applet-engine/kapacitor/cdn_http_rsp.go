package kapacitor

import (
	"context"
	"errors"
	"net/http"
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type HttpRspNode struct {
	node
	callers []*ListenHttpNode // 将接受到的结果返回给各个调用者
	pn      *pipeline.HttpRspNode
	srv     *http.Server
	ctx     context.Context
	exitWg  sync.WaitGroup
}

func (n *HttpRspNode) BeginBatch(begin edge.BeginBatchMessage) error {
	ctx, err := begin.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from begin message")
	}
	if ctx.Call == nil {
		return stderr.Error("call of begin message context is nil")
	}
	if ctx.Call.ReqID() == "" {
		return stderr.Error("request id of begin message is empty")
	}
	return n.handleMsg(ctx.Call.ReqID(), begin)
}

func (n *HttpRspNode) BatchPoint(bp edge.BatchPointMessage) error {
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from batch point message")
	}
	if ctx.Call == nil {
		return stderr.Error("call of batch point message context is nil")
	}
	if ctx.Call.ReqID() == "" {
		return stderr.Error("request id of batch point message is empty")
	}
	return n.handleMsg(ctx.Call.ReqID(), bp)
}

func (n *HttpRspNode) EndBatch(end edge.EndBatchMessage) error {
	ctx, err := end.Context()
	if err != nil {
		return stderr.Wrap(err, "failed to get context from end batch message")
	}
	if ctx.Call == nil {
		return stderr.Error("call of end message context is nil")
	}
	if ctx.Call.ReqID() == "" {
		return stderr.Error("request id of end batch message is empty")
	}
	return n.handleMsg(ctx.Call.ReqID(), end)
}

func (n *HttpRspNode) Point(p edge.PointMessage) error {
	return n.handleMsg(p.Name(), p)
}

func (n *HttpRspNode) handleMsg(name string, msg edge.Message) error {
	// TODO: 为什么需要遍历callers
	for _, caller := range n.callers {
		if err := caller.RecvMessage(name, msg); err != nil {
			n.WithError(err).Errorf("send %T msg to caller", msg)
			return nil
		}
	}
	return nil
}

func (n *HttpRspNode) Barrier(b edge.BarrierMessage) error {
	n.Warnf("received unexpected message: %T{%+v}", b, b)
	return nil
}

func (n *HttpRspNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	n.Warnf("received unexpected message: %T{%+v}", d, d)
	return nil
}

func (n *HttpRspNode) Done() {
	return
}

// Create a new  HttpRspNode which submits received items via POST to an HTTP endpoint
func newHttpRspNode(et *ExecutingTask, n *pipeline.HttpRspNode, d NodeDiagnostic) (*HttpRspNode, error) {
	hn := &HttpRspNode{
		node: node{
			Node: n,
			et:   et,
			diag: d,
		},
		pn:  n,
		ctx: context.Background(),
	}
	hn.node.runF = hn.startRsp
	return hn, nil
}

func (n *HttpRspNode) startRsp([]byte) error {
	// 查找类型为 ListenHttpNode 类型的
	for _, nn := range n.et.nodes {
		switch caller := nn.(type) {
		case *ListenHttpNode:
			n.Infof("found a caller: %+v", caller.Name())
			n.callers = append(n.callers, caller)
		}
	}
	if len(n.callers) == 0 {
		n.Errorf("no caller found")
		return errors.New("no caller found")
	}
	consumer := edge.NewCdnConsumerWithReceiverAndErrHandler(
		n.ins[0],
		n,
		HandleMsgErr,
	)
	return consumer.Consume()
}
