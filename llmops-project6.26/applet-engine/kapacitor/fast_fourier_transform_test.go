package kapacitor

import (
	"strconv"
	"testing"
	"time"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

func TestFastFourierTransform(t *testing.T) {
	// test fastFourierTransformerGroup{}.doTransform()
	testCases := []struct {
		input   []complex128
		field   string
		inverse int64
		expect  []complex128
	}{
		{
			input: []complex128{
				1.0 + 0i,
				2.0 + 0i,
				3.0 + 0i,
				4.0 + 0i,
				5.0,
				6.0,
				7.0,
				8.0,
				9.0 + 0i,
				10.0 + 0i,
			},
			field:   "temperature",
			inverse: 1,
			expect: []complex128{
				5.499999999999998 - 3.108624468950438e-16i,
				-0.49999999999999967 - 1.5388417685876246i,
				-0.4999999999999991 - 0.6881909602355856i,
				-0.49999999999999883 - 0.36327126400268i,
				-0.49999999999999983 - 0.16245984811645306i,
				-0.49999999999999983 - 1.0681644771942533e-15i,
				-0.5000000000000003 + 0.16245984811645142i,
				-0.5000000000000001 + 0.36327126400268056i,
				-0.5000000000000003 + 0.688190960235588i,
				-0.5000000000000002 + 1.5388417685876263i,
			},
		},
		{
			input:   []complex128{},
			field:   "temperature",
			inverse: 1,
			expect:  []complex128{},
		},
		{
			input: []complex128{
				1.0,
			},
			field:   "humidity",
			inverse: 1,
			expect:  nil,
		},
	}

	for _, tc := range testCases {
		group := (&FastFourierTransformerNode{
			f: &pipeline.FastFourierTransformerNode{
				Field:   tc.field,
				Inverse: tc.inverse,
			},
		}).newGroup()

		row := new(models.Row)
		row.Name = "fft_1"
		row.Tags = nil
		row.Columns = []string{
			"time",
			"data",
			"temperature",
		}
		row.Values = make([][]interface{}, len(tc.input))
		for i, point := range tc.input {
			r := make([]interface{}, 3)
			r[0] = time.Now()
			r[1] = 1
			r[2] = strconv.FormatFloat(real(point), 'E', -1, 64)
			row.Values[i] = r
		}

		expect := tc.expect
		var got []complex128
		msg, err := group.doTransform(row)
		if err != nil {
			got = nil
			continue
		} else {
			got = (msg.Fields()[tc.field+"_fft"]).([]complex128)
		}
		if expect == nil {
			if got != nil {
				t.Errorf("got %v exp %v", got, expect)
			} else {
				continue
			}
		}
		for i, point := range expect {
			if point != got[i] {
				t.Errorf("got %v exp %v", got, expect)
			}
		}
	}
}
