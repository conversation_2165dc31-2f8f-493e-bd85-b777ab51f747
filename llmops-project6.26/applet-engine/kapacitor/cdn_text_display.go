package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

// TextDisplayNode -完整示例/*
// var var_1000 = stream          //对应streamNode
//
//	|listenHttp()              //对应listenhttpnode
//		.path('/api/v1')
//		.port(1884)
//		.timeout(60s)
//	|textInput()              //对应textInputNode
//		.nodeID('WidgetKeyTextInput')
//		.inputKey('TextInput')
//	|dlieInfer()              //对应DlieInferNode
//		.addr('*************')
//		.port(32428)
//		.modelName('atom')
//		.isStream(TRUE)
//		.modelType('text-gen')
//		.timeout(5000s)
//	|textDisplay()       		 //对应TextDisplayNode
//		.prefix('前缀prefix-')
//		.suffix('-后缀suffix')
//	|httpRsp()              //对应httpRspNode
type TextDisplayNode struct {
	node
	pn               *pipeline.TextDisplayNode // 算子的参数定义
	batchMessageInfo edge.BeginBatchMessage    //暂存BeginBatchMessage的信息
}

// newTextDisplayNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextDisplayNode(et *ExecutingTask, n *pipeline.TextDisplayNode, d NodeDiagnostic) (*TextDisplayNode, error) {
	hn := &TextDisplayNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextDisplayNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

// BeginBatch 处理一批数据点开始的信号
func (n *TextDisplayNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()

	n.batchMessageInfo = begin
	begin.SetMeta(n.pn.NodeMeta)
	n.forwardMsg(begin, true)

	//使用BatchPointMessage输出前缀
	fields := map[string]any{
		models.PredefinedFieldOutput:  n.pn.Prefix,
		models.PredefinedFieldMeta:    n.pn.NodeMeta,
		models.PredefinedFieldContext: ctx,
	}
	bpm := edge.NewBatchPointMessage(fields, begin.Tags(), begin.Time())
	return n.forwardMsg(bpm, true)
}

// BatchPoint 为 BeginBatch EndBatch 两个起止信号之间的单个数据点位
func (n *TextDisplayNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	text, err := extracOutputFromFields[string](bp.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	if err != nil {
		return stderr.Wrap(err, "failed to extract context from batch point")
	}
	bp.SetFields(map[string]any{
		models.PredefinedFieldMeta:    n.pn.NodeMeta,
		models.PredefinedFieldContext: ctx,
		models.PredefinedFieldOutput:  text,
	})
	return n.forwardMsg(bp, true)
}

// EndBatch 处理一批数据点结束的信号
func (n *TextDisplayNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()

	//使用BatchPointMessage输出后缀
	fields := map[string]any{
		models.PredefinedFieldMeta:    n.pn.NodeMeta,
		models.PredefinedFieldContext: ctx,
		models.PredefinedFieldOutput:  n.pn.Suffix,
	}
	bpm := edge.NewBatchPointMessage(fields, n.batchMessageInfo.Tags(), n.batchMessageInfo.Time())
	n.forwardMsg(bpm, true)
	end.SetMeta(n.pn.NodeMeta)
	return n.forwardMsg(end, true)
}

func (n *TextDisplayNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}

	input, err := extracOutputFromFields[interface{}](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}

	// string, []string 文本分割
	var output interface{}
	switch input.(type) {
	case string:
		output = n.pn.Prefix + input.(string) + n.pn.Suffix
	case []string:
		temp := input.([]string)
		for index, value := range temp {
			temp[index] = n.pn.Prefix + value + n.pn.Suffix
		}
		output = temp
	default:
		return stderr.InvalidParam.Error("text display node only support string or []string")
	}

	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", output)
	}
	return n.forwardMsg(p, true)
}
func (n *TextDisplayNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (n *TextDisplayNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (n *TextDisplayNode) Done() {}
