package kapacitor

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/jsonnet"
)

const KafkaAddrSep = "," // the separator to split multiple kafka addresses

type KafkaPubNode struct {
	node
	c       *pipeline.KafkaPubNode
	kw      *kafka.Writer
	topic   string
	payload string
}

// Create a new  KafkaPubNode which submits received items via POST to an HTTP endpoint
func newKafkaPubNode(et *ExecutingTask, n *pipeline.KafkaPubNode, d NodeDiagnostic) (kn *KafkaPubNode, err error) {
	kn = &KafkaPubNode{
		c:       n,
		node:    node{Node: n, et: et, diag: d},
		topic:   n.Topic,
		payload: n.Payload,
	}

	// check is param valid
	if n.Topic == "" || n.Address == "" {
		return nil, errors.New("cloud not new kafka pub node without kafka address and topic")
	}

	// check are all addresses available
	brokers := strings.Split(n.Address, KafkaAddrSep)
	ctx, _ := context.WithTimeout(context.Background(), time.Second*5)
	for _, broker := range brokers {
		conn, err := kafka.DialContext(ctx, "tcp", broker)
		if err != nil {
			d.Error(fmt.Sprintf("error while connecting to kafka broker %q", broker), err)
			return nil, err
		}
		if err = conn.Close(); err != nil {
			d.Error(fmt.Sprintf("failed to close kafka connection to %s", broker), err)
			return nil, err
		}
		// if len(ps) == 0 {
		//	err = errors.New(fmt.Sprintf("failed to find any partitions for topic %q on broker %q", n.Topic, broker))
		//	d.Error("", err)
		//	//return nil, err
		// }
	}

	// create kafka writer(producer) by giving kafka addresses
	kn.kw = kafka.NewWriter(kafka.WriterConfig{
		Brokers:  brokers,
		Topic:    n.Topic,
		Balancer: &kafka.LeastBytes{},
		Async:    true,
	})
	kn.node.runF = kn.runPub
	kn.node.stopF = kn.stopPub
	return kn, nil
}

func (n *KafkaPubNode) runPub([]byte) error {
	consumer := edge.NewGroupedConsumer(n.ins[0], n)
	n.statMap.Set(statCardinalityGauge, consumer.CardinalityVar())
	return consumer.Consume()

}

// stopPub will close kafka writer asynchronously to avoid blocking kapacitor server.
func (n *KafkaPubNode) stopPub() {
	if n.kw == nil {
		return
	}
	go func() {
		if err := n.kw.Close(); err != nil {
			n.diag.Error("error while stopping kafka writer", err)
		}
		n.kw = nil
	}()
}

func (n *KafkaPubNode) NewGroup(group edge.GroupInfo, first edge.PointMeta) (edge.Receiver, error) {
	g := &kafkaPubGroup{
		n:      n,
		buffer: new(edge.BatchBuffer),
	}
	return edge.NewReceiverFromForwardReceiverWithStats(
		n.outs,
		edge.NewTimedForwardReceiver(n.timer, g),
	), nil
}

type kafkaPubGroup struct {
	n      *KafkaPubNode
	buffer *edge.BatchBuffer
}

func (g *kafkaPubGroup) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
	return nil, nil
}

func (g *kafkaPubGroup) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
	return nil, g.buffer.BatchPoint(bp)
}

func (g *kafkaPubGroup) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
	return g.BufferedBatch(g.buffer.BufferedBatchMessage(end))
}

func (g *kafkaPubGroup) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	row := batch.ToRow()
	g.n.doKafkaPub(row)
	return batch, nil
}

func (g *kafkaPubGroup) Point(p edge.PointMessage) (edge.Message, error) {
	row := p.ToRow()
	g.n.doKafkaPub(row)
	return p, nil
}

func (g *kafkaPubGroup) Barrier(b edge.BarrierMessage) (edge.Message, error) {
	return b, nil
}

func (g *kafkaPubGroup) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
	return d, nil
}

func (g *kafkaPubGroup) Done() {
}

func (n *KafkaPubNode) doKafkaPub(row *models.Row) {
	message, err := jsonnet.FillJsonnet(row.ToMappedRow(), n.payload)
	if err != nil {
		n.diag.Error("fail to render jsonnet message", err)
		return
	}
	msg := kafka.Message{
		Topic: n.topic,
		Value: []byte(message),
	}
	err = n.kw.WriteMessages(context.Background(), msg)
	if err != nil {
		n.diag.Error("fail to publish kafka message", err)
	}
}
