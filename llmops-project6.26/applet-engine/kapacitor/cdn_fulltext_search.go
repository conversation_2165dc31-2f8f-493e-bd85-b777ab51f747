package kapacitor

import (
	"fmt"

	"github.com/elastic/go-elasticsearch/v6"

	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type FullTextSearch struct {
	node
	unsupportedBatchNode
	pn *pipeline.FullTextSearch
}

// TODO:注册算子
func newFullTextSearch(et *ExecutingTask, n *pipeline.FullTextSearch, d NodeDiagnostic) (*FullTextSearch, error) {
	hn := &FullTextSearch{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *FullTextSearch) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *FullTextSearch) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	ctx, err := util.GetContextFromPointMessages(p)
	if err != nil {
		return err
	}
	if n.pn == nil {
		return stderr.Internal.Error("n.pn is nil")
	}
	stdlog.Infof("FullTextSearch: %v", ctx)
	widgetParams := n.pn.WidgetParams

	esClient, err := getEsClientByConfig(&widgetParams.ESConfig)
	if err != nil {
		return stderr.Wrap(err, "failed to create elasticsearch client")
	}
	stdlog.Infof("elastic search client created")

	// queryStr 是查询的内容
	queryStr, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}
	if len(queryStr) == 0 {
		return stderr.Internal.Error("queryStr is empty")
	}

	// 查询
	query := map[string]interface{}{
		"size": widgetParams.TopK,
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"text": queryStr,
			},
		},
	}
	rsp := new(stdes.RecallResp)
	err = esClient.Search(ctx, widgetParams.IndexName, query, rsp)
	if err != nil {
		return stderr.Wrap(err, "failed to search")
	}

	// 设置输出
	chunks, err := ResultHandler(*rsp)
	if err != nil {
		return stderr.Wrap(err, "failed to ResultHandler")
	}
	if err := setOutputIntoFields(p.Fields(), chunks); err != nil {
		return stderr.Wrap(err, "set output into fields")
	}
	return n.forwardMsg(p, true)
}

func ResultHandler(rsp stdes.RecallResp) ([]*pb.Chunk, error) {
	chunks := make([]*pb.Chunk, 0)
	for _, hit := range rsp.Hits.Hits {
		content, ok := hit.Source["text"].(string)
		if !ok {
			return nil, stderr.Error("failed to cast source[text] to string")
		}
		extra := map[string]string{
			"score": fmt.Sprintf("%f", hit.Score),
		}
		chunks = append(chunks, &pb.Chunk{
			Content: content,
			Extra:   extra,
		})
	}
	return chunks, nil
}

func getEsClientByConfig(config *widgets.SimpleESConfig) (*stdes.Client, error) {
	cfg := elasticsearch.Config{
		Username:  config.Username,
		Password:  config.Password,
		Addresses: config.Addresses,
	}
	return stdes.NewESClientByCfg(cfg)
}
