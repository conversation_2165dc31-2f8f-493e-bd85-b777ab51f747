package kapacitor

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type ChunksOutputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ChunksOutputNode // 算子的参数定义
}

// newChunksSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newChunksOutputNode(et *ExecutingTask, n *pipeline.ChunksOutputNode, d NodeDiagnostic) (*ChunksOutputNode, error) {
	hn := &ChunksOutputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ChunksOutputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *ChunksOutputNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	outputMap, err := extracOutputFromFields[map[string]any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}

	chunks, err := tryCvt2Chunks(outputMap[widgets.ParamIDChunks])
	if err != nil {
		return stderr.Wrap(err, "extract chunks from outputMap")
	}

	elements, err := tryCvt2Elements(outputMap[widgets.ParamIDElements])
	if err != nil {
		return stderr.Wrap(err, "extract elements from outputMap")
	}

	outputResp := new(pb.DocSvcLoadChunkRsp)
	if temp, ok := outputMap[widgets.ParamIDLoadChunkResp]; ok {
		// 上游直接DocSvcLoadChunkRsp类型的结构
		err = stdsrv.UnmarshalMixWithProto(temp, outputResp)
		if err != nil {
			return stderr.Wrap(err, "extract LoadChunkResp from outputMap")
		}
	}
	outputResp.Chunks = append(outputResp.Chunks, chunks...)
	outputResp.Elements = append(outputResp.Elements, elements...)

	// 1、整体使用pointMsg往后发送
	enableSplit := n.pn.WidgetParams.EnableSplit
	if !enableSplit {
		return n.ForwardMsg(p, outputResp)
	}
	// 2、分批次往后发送
	return n.batchForward(p, outputResp, modelCtx)
}

// batchForward 分批次发送outputResp
func (n *ChunksOutputNode) batchForward(p edge.PointMessage, outputResp *pb.DocSvcLoadChunkRsp, modelCtx models.Context) error {
	maxSplitLen := n.pn.WidgetParams.MaxSplitLen

	totalChunks := len(outputResp.Chunks)
	totalElements := len(outputResp.Elements)

	// 计算需要多少个批次，以最大的为准
	chunkBatches := (totalChunks + maxSplitLen - 1) / maxSplitLen
	elementBatches := (totalElements + maxSplitLen - 1) / maxSplitLen
	totalBatches := chunkBatches
	if elementBatches > totalBatches {
		totalBatches = elementBatches
	}

	// 发送开始批次消息
	trace, _ := p.Trace()
	beginBatch := edge.NewBeginBatchMessage(p.Name(), n.pn.NodeMeta, trace, modelCtx, p.Tags(), p.Dimensions().ByName, p.Time(), 0)
	if err := n.BeginBatch(beginBatch); err != nil {
		return stderr.Wrap(err, "forward begin batch message")
	}

	// 分批次发送，以最大批次为准
	for i := 0; i < totalBatches; i++ {
		batchResp := new(pb.DocSvcLoadChunkRsp)

		// 计算当前批次的chunks范围
		chunkStart := i * maxSplitLen
		chunkEnd := chunkStart + maxSplitLen
		if chunkEnd > totalChunks {
			chunkEnd = totalChunks
		}
		if chunkStart < totalChunks {
			batchResp.Chunks = outputResp.Chunks[chunkStart:chunkEnd]
		} else {
			// 没有对应的chunks数据，发送空切片
			batchResp.Chunks = make([]*pb.Chunk, 0)
		}

		// 计算当前批次的elements范围
		elementStart := i * maxSplitLen
		elementEnd := elementStart + maxSplitLen
		if elementEnd > totalElements {
			elementEnd = totalElements
		}
		if elementStart < totalElements {
			batchResp.Elements = outputResp.Elements[elementStart:elementEnd]
		} else {
			// 没有对应的elements数据，发送空切片
			batchResp.Elements = make([]*pb.DocElement, 0)
		}

		// 发送批次数据点
		fields := models.Fields{
			models.PredefinedFieldOutput:  batchResp,
			models.PredefinedFieldContext: modelCtx,
		}
		batchPoint := edge.NewBatchPointMessage(fields, p.Tags(), p.Time())
		if err := n.BatchPoint(batchPoint); err != nil {
			return stderr.Wrap(err, "forward batch point message")
		}
	}

	// 发送结束批次消息
	eb := edge.NewEndBatchMessage()
	eb.SetContext(modelCtx)
	if err := n.EndBatch(eb); err != nil {
		return stderr.Wrap(err, "forward end batch message")
	}
	return nil
}

func (n *ChunksOutputNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	return n.forwardMsg(begin, true)
}

func (n *ChunksOutputNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	return n.forwardMsg(bp, true)
}

// EndBatch 处理一批数据点结束的信号
func (n *ChunksOutputNode) EndBatch(end edge.EndBatchMessage) (err error) {
	return n.forwardMsg(end, true)
}

type QuestionClassifierOutPutNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.QuestionClassifierOutPutNode // 算子的参数定义
}

// newChunksSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newQuestionClassifierOutPutNode(et *ExecutingTask, n *pipeline.QuestionClassifierOutPutNode, d NodeDiagnostic) (*QuestionClassifierOutPutNode, error) {
	hn := &QuestionClassifierOutPutNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *QuestionClassifierOutPutNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *QuestionClassifierOutPutNode) Point(p edge.PointMessage) (err error) {
	output, err := extracOutputFromFields[any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}
	cvt2QuestionClassifyResp, err := tryCvt2QuestionClassifyResp(output)
	if err != nil {
		return stderr.Wrap(err, "cvt question classify resp ")
	}
	return n.ForwardMsg(p, cvt2QuestionClassifyResp)
}
