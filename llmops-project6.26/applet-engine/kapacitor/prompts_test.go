package kapacitor

import (
	"encoding/json"
	"fmt"
	"testing"
	"transwarp.io/applied-ai/applet-engine/models"
)

const (
	stdReAct0 = `Thought: 思考部分0。 Action: 孤独星球0 Action Input: {}`
	stdReAct1 = `Thought: 思考部分1。 Action: 孤独星球1 Action Input: {"query": "1", "doc_range": {"a":"aa","c":1231,"d":{"e":"1"}}, "retrieval_config": {}}`
	stdReAct2 = `Thought: 思考部分2。 Action: 孤独星球2 Action Input: {"query": "2", "doc_range": {"a":"aa","c":1231,"d":{"e":"1"}}, "retrieval_config": {}}`
	stdReAct3 = `Thought: 思考部分3。 Action: 孤独星球3 Action Input: {"query": "3", "doc_range": {"a":"aa","c":1231,"d":{"e":"1"}}, "retrieval_config": {}`
)

func TestExtractReActText(t *testing.T) {
	strs := []string{
		stdReAct0,
		stdReAct1,           // 标准reAct文本
		"全量文本1" + stdReAct1, // reAct文本在最后
		"全量文本1" + stdReAct1 + "全量文本2", // reAct文本在中间
		"<think>think模型输出部分</think>" + stdReAct1 + stdReAct2 + "干扰数据",
		"<think>think模型输出部分</think>" + stdReAct1 + stdReAct3 + "干扰数据",
		"<think>think模型输出部分</think>" + stdReAct3 + stdReAct2 + "干扰数据",
	}
	for _, str := range strs {
		reActAction, err := models.ExtractReActText(str)
		if err != nil {
			panic(err)
		}
		bs, _ := json.MarshalIndent(reActAction, "", "  ")
		fmt.Println(string(bs))
	}
}
