package kapacitor

import (
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextSplitNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextSplitNode // 算子的参数定义
}

// newTextSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextSplitNode(et *ExecutingTask, n *pipeline.TextSplitNode, d NodeDiagnostic) (*TextSplitNode, error) {
	hn := &TextSplitNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextSplitNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextSplitNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		n.Warnf("recved message is nil or has no fields")
		return nil
	}
	//TODO: 暂时不处理fileName，之后可以在fileds中增加PassthroughParams，传递Output之外的参数，并且需要DeepCopy PassthroughParams
	// fName, ok := output[models.PredefinedFieldFileName]
	text, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	sepParts := strings.Split(text, n.pn.Separator)
	fnParts := make([]string, 0)
	for _, part := range sepParts {
		if len(part) <= int(n.pn.ChunkSize) {
			fnParts = append(fnParts, part)
			continue
		}
		fnParts = append(fnParts, cutString(part, int(n.pn.ChunkSize))...)
	}
	if err := setOutputIntoFields(p.Fields(), fnParts); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", fnParts)
	}
	return n.forwardMsg(p, true)
}

func cutString(s string, length int) []string {
	rs := []rune(s)
	if len(rs) < length {
		return []string{s}
	}

	l := 0
	ret := make([]string, 0, len(rs)/length)
	for r := length; r < len(rs); r += length {
		ret = append(ret, string(rs[l:r]))
		l = r
	}
	return ret
}
