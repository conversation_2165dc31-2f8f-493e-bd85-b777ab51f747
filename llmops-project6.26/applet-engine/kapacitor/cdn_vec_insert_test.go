package kapacitor

import (
	"math"
	"testing"
)

func Test_newVecInsertNode(t *testing.T) {
	// collection := "applet_eddasdddd22"
	//
	// type TestRes struct {
	// 	CtxTexts         []string
	// 	CtxFeatures      [][]float32
	// 	QuestionTexts    []string
	// 	QuestionFeatures [][]float32
	// }
	//
	// c, err := milvus.NewConfiguredClient(milvus.Config{
	// 	Server: milvus.ServerConfig{
	// 		Host: "localhost",
	// 		Port: "19530",
	// 	},
	// 	Options: milvus.ConfiguredConfig{
	// 		Collection:      collection,
	// 		Partition:       "default",
	// 		Dimension:       768,
	// 		DataStoragePath: "../data/milvus-data2",
	// 	},
	// })
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// res := new(TestRes)
	// bs, err := os.ReadFile("../data/test-res.json")
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// if err := json.Unmarshal(bs, res); err != nil {
	// 	t.Fatal(err)
	// }
	// entities := make([]milvus.Entity, 0)
	// for i, text := range res.CtxTexts {
	// 	feature := res.CtxFeatures[i]
	// 	nf := normalizeVector(feature)
	// 	fsum := float32(0.0)
	// 	for _, num := range nf {
	// 		fsum += num * num
	// 	}
	// 	t.Logf("featuren sum %.4f, length: %d", fsum, len(feature))
	// 	entities = append(entities, milvus.NewEntity(int64(i), nf, []byte(text)))
	// }
	// if err := c.InsertEntities(entities...); err != nil {
	// 	t.Fatal(err)
	// }
	// if err := c.CreateIndex(collection); err != nil {
	// 	t.Fatal(err)
	// }
	// if err := c.FlushCollection(collection); err != nil {
	// 	t.Fatal(err)
	// }
	// if err := c.LoadCollection(collection); err != nil {
	// 	t.Fatal(err)
	// }
	// for i, text := range res.QuestionTexts {
	// 	searchRes, err := c.SearchEntities(normalizeVector(res.QuestionFeatures[i]), 3, true, true)
	// 	if err != nil {
	// 		t.Fatal(err)
	// 	}
	// 	t.Logf("\n\n[Q]: %s", text)
	// 	searchRes.SortByDistance(false)
	// 	for j, item := range searchRes {
	// 		t.Logf("[A%d]%f: %s", j, item.Distance, item.AsBytes())
	// 	}
	// }
}

func normalizeVector(vector []float32) []float32 {
	var sumSquares float32

	// 计算向量元素的平方和
	for _, val := range vector {
		sumSquares += val * val
	}

	// 计算向量的长度
	vectorLength := math.Sqrt(float64(sumSquares))

	// 标准化向量
	normalizedVector := make([]float32, len(vector))
	for i, val := range vector {
		normalizedVector[i] = val / float32(vectorLength)
	}

	return normalizedVector
}
