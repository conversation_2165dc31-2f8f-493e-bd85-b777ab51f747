package kapacitor

import (
	"context"
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	eclients "transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type QuestionClassifierNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.QuestionClassifierNode // 算子的参数定义
}

// newQuestionClassifierNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newQuestionClassifierNode(et *ExecutingTask, n *pipeline.QuestionClassifierNode, d NodeDiagnostic) (*QuestionClassifierNode, error) {
	hn := &QuestionClassifierNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *QuestionClassifierNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *QuestionClassifierNode) Point(p edge.PointMessage) error {
	ctx, modelCtx, _, err := util.GetContexts(p)
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}

	query, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	widgetParams := n.pn.ParsedParams
	classifyReq := &engine.QuestionClassifyReq{Question: query}
	defaultTargetNodes := make([]string, 0)
	for _, c := range widgetParams.Categories {
		if c.IsDefault {
			defaultTargetNodes = append(defaultTargetNodes, c.TargetNodes...)
		}
		classifyReq.CategoryNames = append(classifyReq.CategoryNames, c.CategoryName)
	}

	classifyResp, err := n.questionClassify(ctx, p, classifyReq)
	if err != nil {
		modelCtx.Infof("do question classify failed, replace with default category")
		return n.forward2TargetNodes(defaultTargetNodes, p)
	}

	if !widgetParams.EnableMutilMode && len(classifyResp.DecidedCategoryNames) > 1 {
		modelCtx.Infof("mutil mode is not enabled and the len of decided category is larger chan 1, replace with default category")
		return n.forward2TargetNodes(defaultTargetNodes, p)
	}

	targetNodes := make([]string, 0)
	for _, cName := range classifyResp.DecidedCategoryNames {
		for _, category := range widgetParams.Categories {
			if category.CategoryName == cName {
				targetNodes = append(targetNodes, category.TargetNodes...)
			}
		}
	}
	return n.forward2TargetNodes(targetNodes, p)
}

func (n *QuestionClassifierNode) questionClassify(ctx context.Context, p edge.PointMessage, req *engine.QuestionClassifyReq) (*engine.QuestionClassifyResp, error) {
	if n.pn.ParsedParams.EnableAppMode {
		return n.doClassifyByAppSvc(ctx, p, req)
	}
	return n.doClassifyByModelSvc(ctx, p, req)
}
func (n *QuestionClassifierNode) doClassifyByModelSvc(ctx context.Context, p edge.PointMessage, req *engine.QuestionClassifyReq) (*engine.QuestionClassifyResp, error) {
	widgetParams := n.pn.ParsedParams
	ret := new(engine.QuestionClassifyResp)
	ret.QuestionClassifyReq = *req
	totalQuery := models.GenerateQuestionClassifierPrompt(req.Question, ret.CategoryNames, widgetParams.EnableMutilMode, widgetParams.CustomPrompt)
	chatReq, err := clients.GetTotalChatReq(widgetParams.ModelService, &clients.SimpleReq{Query: totalQuery})
	if err != nil {
		return nil, stderr.Wrap(err, "get chat req")
	}
	clients.SyncChat(ctx, widgetParams.ModelService, chatReq, func(textContent string) error {
		return json.Unmarshal([]byte(textContent), &ret.DecidedCategoryNames)
	})
	return ret, ret.Validate()
}

func (n *QuestionClassifierNode) doClassifyByAppSvc(ctx context.Context, p edge.PointMessage, req *engine.QuestionClassifyReq) (*engine.QuestionClassifyResp, error) {
	ret, err := eclients.AppletToolExecutor.ExecuteQuestionClassify(ctx, n.pn.ParsedParams.AppletService.MlOpsSvcID, req)
	if err != nil {
		return nil, stderr.Wrap(err, "fail to exec app svc")
	}
	return ret, ret.Validate()
}

type simpleCategory struct {
	CategoryNames []string `json:"category_names"`
}
