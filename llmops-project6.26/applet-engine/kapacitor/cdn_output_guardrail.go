package kapacitor

import (
	"errors"
	"github.com/patrickmn/go-cache"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

type OutputGuardrailNode struct {
	node
	unsupportedBatchNode
	pn             *pipeline.OutputGuardrailNode // 算子的参数定义
	textFilters    *cache.Cache
	securityCensor *triton.SecurityCensor
}

const (
	TEXT_FILTER_CACHE_EXPIRATION_TIME  = 300 * time.Second
	TEXT_FILTER_CACHE_CLEANUP_INTERVAL = 10 * time.Second
)

// newOutputGuardrailNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newOutputGuardrailNode(et *ExecutingTask, n *pipeline.OutputGuardrailNode, d NodeDiagnostic) (*OutputGuardrailNode, error) {
	filterCache := cache.New(TEXT_FILTER_CACHE_EXPIRATION_TIME, TEXT_FILTER_CACHE_CLEANUP_INTERVAL)
	securityCensor := triton.NewSecurityCensor(conf.Config.BackendTask.SecurityCensorUrl, triton.DefaultSecurityCensorTimeout)
	hn := &OutputGuardrailNode{
		node:           node{Node: n, et: et, diag: d},
		pn:             n,
		textFilters:    filterCache,
		securityCensor: securityCensor,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *OutputGuardrailNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *OutputGuardrailNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	// 未开启安全防护时，直接透明转发
	if !n.pn.ParsedParams.Enable {
		return n.forwardMsg(p, false)
	}

	text, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("call is nil")
	}
	checkSecurityReq := &triton.CheckSecurityRequest{
		ConfigId: n.pn.ParsedParams.SafetyConfig.ID,
		Sentence: text,
	}
	checkSecurityRsp, err := n.securityCensor.CheckOutputSensitiveWords(call.Ctx(), checkSecurityReq)
	if err != nil {
		return stderr.Wrap(err, "failed to check security")
	}
	// 没有安全风险，直接透明转发
	if !checkSecurityRsp.Risk {
		return n.forwardMsg(p, false)
	}
	return replaceAndCancel(call, checkSecurityRsp.Response)

}

func (n *OutputGuardrailNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardMsg(begin, false)
}

func (n *OutputGuardrailNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	// 未开启安全防护时，直接透明转发
	if !n.pn.ParsedParams.Enable {
		return n.forwardMsg(bp, false)
	}

	text, exist, err := models.GetValueOfFields[string](bp.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("received message has no call context")
	}
	reqId := call.ReqID()
	textFilter, exist, err := n.getFilter(reqId)
	if err != nil {
		return stderr.Wrap(err, "failed to get text filter")
	}
	if !exist {
		textFilter, err = n.newFilter(ctx, bp)
		if err != nil {
			return stderr.Wrap(err, "failed to create text filter")
		}
	}
	err = textFilter.Append(text)
	if err == nil {
		return nil
	}
	// 不抛出检测到风险文本的错误
	if errors.Is(err, triton.ErrFoundRiskWord) {
		ctx.Warnf("found risk word when append")
	}
	// 抛出其他错误
	return stderr.Wrap(err, "failed to append text")
}

func (n *OutputGuardrailNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()

	// 未开启安全防护时，直接透明转发
	if !n.pn.ParsedParams.Enable {
		return n.forwardMsg(end, false)
	}

	call := ctx.Call
	if call == nil {
		return stderr.Error("no call found in context")
	}
	filter, exist, err := n.getFilter(call.ReqID())
	if err != nil {
		return stderr.Wrap(err, "failed to get text filter")
	}
	if !exist {
		return stderr.Error("no text filter found for reqId %s", call.ReqID())
	}
	checkSecurityResponse, err := filter.Done()
	// 不是因为拦截错误，则抛出，否则打印日志
	if err != nil && !errors.Is(err, triton.ErrFoundRiskWord) {
		return stderr.Wrap(err, "failed for text filter done")
	}
	if errors.Is(err, triton.ErrFoundRiskWord) {
		ctx.Warnf("found risk word when filtering the remaining text")
	}
	// 判断是否有安全风险，无风险透明转发
	if checkSecurityResponse == nil {
		return stderr.Error("check security response is nil")
	}
	if !checkSecurityResponse.Risk {
		return n.forwardMsg(end, false)
	}
	return nil
}

func (n *OutputGuardrailNode) getFilter(reqId string) (*triton.StreamTextFilter, bool, error) {
	v, exist := n.textFilters.Get(reqId)
	if !exist {
		return nil, false, nil
	}
	filter, ok := v.(*triton.StreamTextFilter)
	if !ok {
		return nil, true, stderr.Internal.Error("cannot convert %T to %T", v, filter)
	}
	return filter, true, nil
}

func (n *OutputGuardrailNode) newFilter(ctx models.Context, bp edge.BatchPointMessage) (*triton.StreamTextFilter, error) {
	call := ctx.Call
	basicConfig := triton.FilterBasicConfig{
		WindowSize: triton.DefaultWindowSize,
		StepSize:   triton.DefaultStepSize,
	}
	securityServiceConfig := triton.FilterSecurityServiceConfig{
		ConfigId: n.pn.ParsedParams.SafetyConfig.ID,
		Address:  conf.Config.BackendTask.SecurityCensorUrl,
		Timeout:  triton.DefaultSecurityCensorTimeout,
		Ctx:      call.Ctx(),
	}
	textFilter, err := triton.NewStreamTextFilter(basicConfig, securityServiceConfig)
	if err != nil {
		return nil, stderr.Wrap(err, "new stream text filter error")
	}
	passTextHandler := func(text string) error {
		cpMsg, err := bp.DeepCopy()
		if err != nil {
			return stderr.Wrap(err, "deep copy batch point message error")
		}
		if err := setOutputIntoFields(cpMsg.Fields(), text); err != nil {
			return stderr.Wrap(err, "failed to set output %s into fields", text)
		}
		return n.forwardMsg(cpMsg, false)
	}
	textFilter.SetPassTextHandler(passTextHandler)
	stopTextHandler := func(response *triton.CheckSecurityResponse) error {
		// 当前节点设置为成功状态
		if err := edge.PubSuccessStatus(call.ReqID(), call, n.Meta(), debug.DEBUG_SCOPE_NODE, true); err != nil {
			return stderr.Wrap(err, "failed to publish success status for node %s", n.Meta().NodeName)
		}
		if err := stdsrv.SSESendReplace(call.W(), call.ReqID(), response.Response); err != nil {
			return stderr.Wrap(err, "failed to send replace message %+v", response)
		}
		em := edge.NewEndBatchMessage()
		em.SetContext(ctx)
		return n.forwardMsg(em, false)
	}
	textFilter.SetStopTextHandler(stopTextHandler)
	n.textFilters.Set(call.ReqID(), textFilter, 0)
	return textFilter, nil
}
