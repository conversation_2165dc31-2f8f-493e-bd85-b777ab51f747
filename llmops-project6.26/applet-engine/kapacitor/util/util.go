package util

import (
	"context"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

func GetContextFromPointMessages(p edge.PointMessage) (context.Context, error) {
	call, err := GetCallFromPointMessages(p)
	if err != nil {
		return nil, err
	}
	return call.Ctx(), nil
}
func GetCallFromPointMessages(p edge.PointMessage) (*debug.Call, error) {
	modelCtx, err := GetModelCtxFromPointMessage(p)
	if err != nil {
		return nil, stderr.Wrap(err, "get modelCtx from point message")
	}
	call := modelCtx.Call
	if call == nil {
		return nil, stderr.Wrap(err, "get call from message context")
	}
	return call, nil
}
func GetModelCtxFromPointMessage(p edge.PointMessage) (*models.Context, error) {
	modelCtx, err := p.Context()
	if err != nil {
		return nil, stderr.Wrap(err, "get context from point message")
	}
	return &modelCtx, nil
}

func GetContexts(p edge.PointMessage) (ctx context.Context, modelCtx *models.Context, call *debug.Call, err error) {
	ctx, err = GetContextFromPointMessages(p)
	if err != nil {
		return
	}
	modelCtx, err = GetModelCtxFromPointMessage(p)
	if err != nil {
		return
	}
	call, err = GetCallFromPointMessages(p)
	if err != nil {
		return
	}
	return ctx, modelCtx, call, nil
}

// GetCurDebugMsg 用于在个算子的point方法中,直接拿到后台自动维护的DebugMessage
func GetCurDebugMsg(nodeMate models.NodeMeta, p edge.PointMessage) (*debug.DebugMessage, error) {
	_, _, call, err := GetContexts(p)
	if err != nil {
		return nil, err
	}
	return debug.GetDebugMessage(call.ReqID(), nodeMate.Id, call.GetLoopRound(nodeMate.Id)-1)
}

// CastToStrSlice  显示转为[]string
func CastToStrSlice(data any) ([]string, error) {
	res := make([]string, 0)
	slice, ok := data.([]interface{})
	if !ok {
		return nil, stderr.Errorf("the type of data: %v is not []string", data)
	}
	for _, item := range slice {
		str, ok := item.(string)
		if !ok {
			return nil, stderr.Errorf("the type of data: %v is not []string", data)
		}
		res = append(res, str)
	}
	return res, nil
}

func IsStrSlice(data any) bool {
	_, err := CastToStrSlice(data)
	return err == nil
}

func GetSystemNamespace() string {
	if sysNs := conf.Config.Tenant.SystemNamespace; sysNs != "" {
		return sysNs
	}
	return k8s.CurrentNamespaceInCluster()
}
func ContainsAny(s string, keywords []string) bool {
	for _, key := range keywords {
		if strings.Contains(s, key) {
			return true
		}
	}
	return false
}

func IsPublished() bool {
	return conf.Config.Tenant.SystemNamespace != "" ||
		conf.Config.Tenant.TenantId != "" ||
		conf.Config.Tenant.ProjectId != ""
}
