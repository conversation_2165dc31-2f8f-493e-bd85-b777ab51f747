package kapacitor

import (
	"bufio"
	"context"
	"encoding/json"
	"io"
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	my_http "transwarp.io/applied-ai/applet-engine/tools/http"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"
)

type HTTPCallNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.HTTPCallNode // 算子的参数定义
}

// newHTTPCallNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newHTTPCallNode(et *ExecutingTask, n *pipeline.HTTPCallNode, d NodeDiagnostic) (*HTTPCallNode, error) {
	hn := &HTTPCallNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *HTTPCallNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *HTTPCallNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.InvalidParam.Error("recved message is nil or has no fields")
	}
	output, err := extracOutputFromFields[any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	if ctx.Call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	callCtx := ctx.Call.Ctx()
	switch n.pn.ServiceType {
	case string(script.HTTPCallServiceTypeTextSplit):
		return n.handleTextSplit(output, p, callCtx)
	case string(script.HTTPCallServiceTypeBingSearch), string(script.HTTPCallServiceTypeSearXNGSearch):
		if n.pn.Escape {
			emptyInternetCitations := []*agent_executor.Citation{}
			if err := setOutputIntoFields(p.Fields(), emptyInternetCitations); err != nil {
				return stderr.Wrap(err, "set empty internet citations array into fields")
			}
			return n.forwardMsg(p, false)
		}
		return n.handleInternetSearch(output, p, callCtx, n.pn.ServiceType)
	case string(script.HTTPCallServiceTypeTextParse):
		outputBytes, err := json.Marshal(output)
		if err != nil {
			return stderr.Wrap(err, "unable to Marshal output into bytes")
		}
		localDocLoadRawReq := n.pn.DocLoadRawReq // 防止发布为服务的时候出现并发问题，需要把 n.pn 的值复制成本地变量
		err = json.Unmarshal(outputBytes, &localDocLoadRawReq)
		if err != nil {
			return stderr.Wrap(err, "failed to do unmarshal with string: %s", string(outputBytes))
		}
		return n.handleDefaultService(localDocLoadRawReq, p, callCtx)
	case string(script.HTTPCallServiceTypeParagraphAggregate):
		outputMap := make(map[string]any)
		err = stdsrv.Unmarshal(stdsrv.AnyToBytes(output), &outputMap)
		if err != nil {
			return stderr.Wrap(err, "unable to UnMarshal outputBytes into map[string]any{}")
		}
		outputElements, ok := outputMap["elements"]
		if !ok {
			return stderr.Wrap(err, "failed to find elements from input")
		}
		return n.handleDefaultService(outputElements, p, callCtx)
	default:
		return n.handleDefaultService(output, p, callCtx)
	}
}

func (n *HTTPCallNode) handleTextSplit(input any, p edge.PointMessage, ctx context.Context) error {
	fieldsByte, err := json.Marshal(input)
	if err != nil {
		return stderr.Wrap(err, "marshal %+v", p.Fields())
	}
	responseBody, _, err := my_http.SendHttpRequest(n.pn.Method, n.pn.Url, fieldsByte, n.pn.HeaderMap, ctx)
	if err != nil {
		return stderr.Wrap(err, "send http request")
	}
	data, err := my_http.ReadResponseBody(responseBody)
	if err != nil {
		return stderr.Wrap(err, "read response body")
	}
	var splitResult engine.SplitResult
	if err = json.Unmarshal(data, &splitResult); err != nil {
		return stderr.Wrap(err, "unmarshal response body to SplitResult")
	}
	chunks := []*pb.Chunk{}
	for _, text := range splitResult.Texts {
		chunk := &pb.Chunk{
			Id:                      uuid.New().String(),
			Content:                 text,
			ElementIds:              nil,
			SourceType:              pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
			ContentType:             pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT, // TODO: 如何区分是文本还是表格
			DisableVectorIndexing:   false,
			DisableFullTextIndexing: false,
			AugmentedChunks:         nil,
		}
		chunks = append(chunks, chunk)
	}
	if err := setOutputIntoFields(p.Fields(), chunks); err != nil {
		return stderr.Wrap(err, "set %d chunks into fields", len(chunks))
	}
	return n.forwardMsg(p, true)
}

// handleInternetSearch 处理互联网搜索请求，包括Bing和SearXNG
func (n *HTTPCallNode) handleInternetSearch(input any, p edge.PointMessage, ctx context.Context, serviceType string) error {
	query, ok := input.(string)
	if !ok {
		return stderr.InvalidParam.Error("cannot convert %+v to %s search query string", input, serviceType)
	}
	searchRequest := agent_executor.InternetSearchRequest{
		Query: query,
	}
	fieldsByte, err := json.Marshal(searchRequest)
	if err != nil {
		return stderr.Wrap(err, "marshal %+v", p.Fields())
	}
	responseBody, _, err := my_http.SendHttpRequest(n.pn.Method, n.pn.Url, fieldsByte, n.pn.HeaderMap, ctx)
	if err != nil {
		return stderr.Wrap(err, "send http request")
	}
	data, err := my_http.ReadResponseBody(responseBody)
	if err != nil {
		return stderr.Wrap(err, "read response body")
	}
	var searchResponse agent_executor.InternetSearchResponse
	if err = json.Unmarshal(data, &searchResponse); err != nil {
		return stderr.Wrap(err, "unmarshal response body %s to InternetSearchResponse", string(data))
	}
	if !searchResponse.Success {
		return stderr.Wrap(err, "%s search failed, msg %s, code %d", serviceType, searchResponse.Msg, searchResponse.Code)
	}
	if err := setOutputIntoFields(p.Fields(), searchResponse.Citations); err != nil {
		return stderr.Wrap(err, "set %d internet citations into fields", len(searchResponse.Citations))
	}
	return n.forwardMsg(p, false)
}

func (n *HTTPCallNode) handleDefaultService(input any, p edge.PointMessage, ctx context.Context) error {
	fieldsByte, err := json.Marshal(input)
	if err != nil {
		return stderr.Wrap(err, "marshal %+v", p.Fields())
	}
	responseBody, header, err := my_http.SendHttpRequest(n.pn.Method, n.pn.Url, fieldsByte, n.pn.HeaderMap, ctx)
	if err != nil {
		return stderr.Wrap(err, "send http request")
	}
	// TODO: header为map[string][]string类型，header.Get()方法返回第一个值，没有处理返回多种Content-Type的情况，暂时根据第一个值判断类型
	contentType := header.Get(my_http.CONTENT_TYPE)
	var handleResult any
	// content-Type可能为"application/json;charset=utf-8"，分号后面有其他额外信息
	// 使用strings.Contains()方法判断Content-Type类型
	switch {
	case strings.Contains(contentType, my_http.JSON_CONTENT_TYPE):
		handleResult, err = n.handleJson(responseBody)
	case strings.Contains(contentType, my_http.PLAIN_CONTENT_TYPE) || strings.Contains(contentType, my_http.HTML_CONTENT_TYPE):
		handleResult, err = n.handleText(responseBody)
	case strings.Contains(contentType, my_http.SSE_CONTENT_TYPE):
		// sse 类型消息单独处理，发送batch类型message
		return n.handleSse(responseBody, p)
	default:
		handleResult, err = n.handleOtherContentType(responseBody)
	}
	if err != nil {
		return stderr.Wrap(err, "handle response, content type %s", contentType)
	}
	if err := setOutputIntoFields(p.Fields(), handleResult); err != nil {
		return stderr.Wrap(err, "set %+v into output fields", handleResult)
	}
	return n.forwardMsg(p, true)
}

func (n *HTTPCallNode) handleJson(responseBody io.ReadCloser) (any, error) {
	data, err := my_http.ReadResponseBody(responseBody)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body")
	}
	var output any
	if err = json.Unmarshal(data, &output); err != nil {
		return nil, stderr.Wrap(err, "unmarshal response body to any")
	}
	return output, nil
}

func (n *HTTPCallNode) handleText(responseBody io.ReadCloser) (string, error) {
	data, err := my_http.ReadResponseBody(responseBody)
	if err != nil {
		return "", stderr.Wrap(err, "read response body")
	}
	return string(data), nil
}

func (n *HTTPCallNode) handleOtherContentType(responseBody io.ReadCloser) ([]byte, error) {
	data, err := my_http.ReadResponseBody(responseBody)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body")
	}
	return data, nil
}

func (n *HTTPCallNode) handleSse(responseBody io.ReadCloser, p edge.PointMessage) error {
	// begin batch
	trace, err := p.Trace()
	if err != nil {
		return stderr.Wrap(err, "get message trace")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get message context")
	}
	beginBatch := edge.NewBeginBatchMessage(p.Name(), n.pn.NodeMeta, trace, ctx, p.Tags(), p.Dimensions().ByName, p.Time(), 0)
	if err := n.forwardMsg(beginBatch, true); err != nil {
		return stderr.Wrap(err, "forward begin batch message")
	}
	// batch point
	reader := bufio.NewReader(responseBody)
	for {
		sseData, readErr := stdsrv.SSEReadData(reader)
		if readErr != nil && readErr != io.EOF {
			return stderr.Wrap(readErr, "read sse data")
		}
		events, decodeErr := stdsrv.SSEDecodeData(sseData)
		if decodeErr != nil {
			return stderr.Wrap(decodeErr, "decode sse data")
		}
		for _, event := range events {
			// 尝试将event消息中的data解析为json数据，如果不是json数据则保持原样
			if data, ok := tryParseJson(event.Data); ok {
				event.Data = data
			}
			fields := models.Fields{
				models.PredefinedFieldOutput:  event,
				models.PredefinedFieldContext: ctx,
			}
			batchPoint := edge.NewBatchPointMessage(fields, p.Tags(), p.Time())
			if err := n.forwardMsg(batchPoint, true); err != nil {
				return stderr.Wrap(err, "forward batch point message")
			}
		}
		if readErr == io.EOF {
			// end batch
			eb := edge.NewEndBatchMessage()
			// TODO: 在NewEndBatchMessage函数中添加context参数
			eb.SetContext(ctx)
			if err := n.forwardMsg(eb, true); err != nil {
				return stderr.Wrap(err, "forward end batch message")
			}
			break
		}
	}
	return nil
}

func tryParseJson(data any) (any, bool) {
	str, ok := data.(string)
	if !ok {
		return nil, false
	}
	var output any
	err := json.Unmarshal([]byte(str), &output)
	if err != nil {
		return nil, false
	}
	return output, true
}
