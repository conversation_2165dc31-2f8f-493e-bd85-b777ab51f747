package kapacitor

import (
	"bytes"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type ChatHistoryNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ChatHistoryNode // 算子的参数定义
}

// newChatHistoryNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newChatHistoryNode(et *ExecutingTask, n *pipeline.ChatHistoryNode, d NodeDiagnostic) (*ChatHistoryNode, error) {
	hn := &ChatHistoryNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ChatHistoryNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *ChatHistoryNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	isListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}

	if !isListenHttp {
		chatHistory, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
		if err != nil || !exist {
			return stderr.Wrap(err, "failed to extract chat history string from fields")
		}
		return n.forwardChatHistory(p, chatHistory)
	}

	output, exist, err := models.GetValueOfFields[map[string]any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	// 尝试从 {nodeid}##{InputKey} key中获取历史对话
	inputFieldKey := n.pn.InputFieldKey(n.pn.Id)
	chatMessageAny, exist, err := models.GetValueOfFields[any](output, inputFieldKey)
	if err != nil {
		return stderr.Wrap(err, "failed to extract %s from output", inputFieldKey)
	}
	if !exist {
		// 如果找不到 {nodeid}##{InputKey} key，则尝试从 {InputKey} key中获取历史对话
		chatMessageAny, exist, err = models.GetValueOfFields[any](output, n.pn.InputKey)
		if err != nil {
			return stderr.Wrap(err, "failed to extract %s from output", n.pn.InputKey)
		}
		if !exist {
			ctx.Warnf("there is no %s key, forward empty value", n.pn.InputKey)
			// 如果 {nodeid}##{InputKey} 与 {InputKey} 都不存在，直接向后传递空字符串
			return n.forwardChatHistory(p, "")
		}
	}
	chatMessageSlice, ok := chatMessageAny.([]any)
	if !ok {
		return stderr.Errorf("failed to convert %T to %T", chatMessageAny, chatMessageSlice)
	}
	// 截断历史对话，丢弃最早的对话
	if len(chatMessageSlice) > int(n.pn.MaxRounds) {
		chatMessageSlice = chatMessageSlice[len(chatMessageSlice)-int(n.pn.MaxRounds):]
	}
	buf := bytes.NewBuffer(make([]byte, 0))
	if err := n.pn.GoTmpl.Execute(buf, chatMessageSlice); err != nil {
		return stderr.Wrap(err, "execute tmpl with text %s and data %+v", n.pn.Tmpl, chatMessageSlice)
	}
	return n.forwardChatHistory(p, buf.String())
}

func (n *ChatHistoryNode) forwardChatHistory(p edge.PointMessage, chatHistory string) error {
	if err := setOutputIntoFields(p.Fields(), chatHistory); err != nil {
		return stderr.Wrap(err, "set chatHistory %+v into output fields", chatHistory)
	}
	return n.forwardMsg(p, true)
}
