package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextOutputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextOutputNode // 算子的参数定义
}

// newTextSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextOutputNode(et *ExecutingTask, n *pipeline.TextOutputNode, d NodeDiagnostic) (*TextOutputNode, error) {
	hn := &TextOutputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextOutputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextOutputNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	if err := setOutputIntoFields(p.Fields(), n.pn.StdText); err != nil {
		return err
	}
	return n.forwardMsg(p, true)
}
