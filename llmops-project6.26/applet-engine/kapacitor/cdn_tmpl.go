package kapacitor

import (
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

// CDNTmplNode 添加自定义开发算子的示例
// kapacitor package 下的算子为算子处理逻辑的核心实现，
// 主要通过实现 edge.Receiver 接口来进行
type CDNTmplNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.CdnTmplNode // 算子需要的参数定义等，需要在 pipeline package 进行定义
}

// newCDNTmplNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newCDNTmplNode(et *ExecutingTask, n *pipeline.CdnTmplNode, d NodeDiagnostic) (*CDNTmplNode, error) {

	hn := &CDNTmplNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}

	hn.node.runF = hn.run
	return hn, nil
}

// run 为算子协程启动的入口， 主要负责同步接收上游产生的数据并进行处理
func (n *CDNTmplNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()

}

// BeginBatch 处理一批数据点开始的
func (n *CDNTmplNode) BeginBatch(begin edge.BeginBatchMessage) error {
	return stderr.Internal.Error("unsupported begin batch message")
}

func (n *CDNTmplNode) BatchPoint(bp edge.BatchPointMessage) error {
	return stderr.Internal.Error("unsupported batch point message")
}

func (n *CDNTmplNode) EndBatch(end edge.EndBatchMessage) error {
	return stderr.Internal.Error("unsupported end batch message")
}

// BufferedBatch 处理接收到的一批数据点
func (n *CDNTmplNode) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	return nil, stderr.Internal.Error("unsupported buffered batch message")
}

// Point 处理接收到的单个数据点
func (n *CDNTmplNode) Point(p edge.PointMessage) (err error) {
	panic(" do somthing to handler received message here")
	// ctx, err := p.Context()
	// if err != nil {
	// 	return stderr.Wrap(err, "get context from point message")
	// }
	// defer func() {
	// 	logError(ctx, err)
	// }()

	// 1. 在fields中的outputKey提取上游节点的输出, 如果是input类型节点，期望的output类型是map[string]any
	// output, err := extracOutputFromFields[map[string]any](p.Fields())
	// if err != nil {
	// 	return stderr.Wrap(err, "failed to extract output from fields")
	// }
	// 2. 如果是input类型节点，需要一进步提取出inputFieldKey对应的value
	// inputFieldKey := n.pipeline.InputFieldKey(n.pipeline.NodeID)
	// text, err := extracValueFromMapOutput[string](output, inputFieldKey)
	// if err != nil {
	// 	return stderr.Wrap(err, "failed to extract text from output")
	// }
	// 3. 处理数据，把处理后的数据放到fields中
	// if err := setOutputIntoFields(p.Fields(), output); err != nil {
	// 	return stderr.Wrap(err, "set output %+v into fields", output)
	// }
	// 4. 将数据发送到下游节点
	// return n.forwardMsg(p, true)
}

func (n *CDNTmplNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (n *CDNTmplNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (n *CDNTmplNode) Done() {}
