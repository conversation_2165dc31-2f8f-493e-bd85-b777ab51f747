package kapacitor

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/influxdata/influxdb/influxql"
	"github.com/pkg/errors"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/expvar"
)

const (
	joinTimeout = time.Hour * 1 // join算子等待一次请求中上游待合并数据的最大时间，超过该时间会丢弃掉该次请求已经来到join算子的数据
)

type JoinNode struct {
	node
	j         *pipeline.JoinNode
	fill      influxql.FillOption
	fillValue interface{}

	groupsMu sync.RWMutex
	groups   map[models.GroupID]*joinGroup

	// Represents the lower bound of times per group per source
	lowMarks map[srcGroup]time.Time

	// Buffer for caching points that need to be matched with specific points.
	matchGroupsBuffer map[models.GroupID][]srcPoint
	// Buffer for caching specific points until their match arrivces.
	specificGroupsBuffer map[models.GroupID][]srcPoint

	reported    map[int]bool
	allReported bool

	batchBuffers map[int]*edge.BatchBuffer // 存储begin、end等流式信息的汇总
}

// Create a new JoinNode, which takes pairs from parent streams combines them into a single point.
func newJoinNode(et *ExecutingTask, n *pipeline.JoinNode, d NodeDiagnostic) (*JoinNode, error) {
	jn := &JoinNode{
		j:                    n,
		node:                 node{Node: n, et: et, diag: d},
		groups:               make(map[models.GroupID]*joinGroup),
		matchGroupsBuffer:    make(map[models.GroupID][]srcPoint),
		specificGroupsBuffer: make(map[models.GroupID][]srcPoint),
		lowMarks:             make(map[srcGroup]time.Time),
		reported:             make(map[int]bool),
	}
	// Set fill
	switch fill := n.Fill.(type) {
	case string:
		switch fill {
		case "null":
			jn.fill = influxql.NullFill
		case "none":
			jn.fill = influxql.NoFill
		default:
			return nil, fmt.Errorf("unexpected fill option %s", fill)
		}
	case int64, float64:
		jn.fill = influxql.NumberFill
		jn.fillValue = fill
	default:
		jn.fill = influxql.NoFill
	}
	jn.node.runF = jn.runJoin
	return jn, nil
}

func (n *JoinNode) runJoin([]byte) error {
	consumer := edge.NewMultiConsumerWithStats(n.ins, n, HandleMsgErr)
	valueF := func() int64 {
		n.groupsMu.RLock()
		l := len(n.groups)
		n.groupsMu.RUnlock()
		return int64(l)
	}
	n.statMap.Set(statCardinalityGauge, expvar.NewIntFuncGauge(valueF))

	return consumer.Consume()
}

func (n *JoinNode) BufferedBatch(src int, batch edge.BufferedBatchMessage) error {
	pointMsg, err := batch.ToPointMessage()
	if err != nil {
		return stderr.Wrap(err, "failed while cast BufferedBatchMessage to PointMessage")
	}
	return n.doMessage(src, pointMsg)
}

// TODO JoinNode
// 1、考虑一次执行,需要将完整的流式消息转为pointMessage
// 2、考虑部署后的并发-共用同一份算子。 要避免互相影响,应当保持节点是无状态的,或者能相互区分开
// 3、对于第二点,当前不太好支持,需要req_id才能区分

func (n *JoinNode) BeginBatch(src int, begin edge.BeginBatchMessage) error {
	return stderr.Errorf("join node can not support batch message")
	//n.checkBuffers(src)
	//return n.batchBuffers[src].BeginBatch(begin)
}
func (n *JoinNode) BatchPoint(src int, batchPoint edge.BatchPointMessage) error {
	return stderr.Errorf("join node can not support batch message")
	//n.checkBuffers(src)
	//return n.batchBuffers[src].BatchPoint(batchPoint)
}
func (n *JoinNode) EndBatch(src int, end edge.EndBatchMessage) error {
	return stderr.Errorf("join node can not support batch message")
	//n.checkBuffers(src)
	//bufferedBatchMessage := n.batchBuffers[src].BufferedBatchMessage(end)
	//return n.BufferedBatch(src, bufferedBatchMessage)
}

func (n *JoinNode) Point(src int, p edge.PointMessage) error {
	return n.doMessage(src, p)
}

func (n *JoinNode) Barrier(src int, b edge.BarrierMessage) error {
	return edge.Forward(n.outs, b)
}
func (n *JoinNode) Finish() error {
	// No more points are coming signal all groups to finish up.
	for _, group := range n.groups {
		if err := group.Finish(); err != nil {
			return err
		}
	}
	return nil
}

type messageMeta interface {
	edge.Message
	edge.PointMeta
}
type srcPoint struct {
	Src int
	Msg messageMeta
}

func (n *JoinNode) doMessage(src int, m messageMeta) error {
	n.timer.Start()
	defer n.timer.Stop()
	if len(n.j.Dimensions) > 0 {
		// Match points with their group based on join dimensions.
		n.matchPoints(srcPoint{Src: src, Msg: m})
	} else {
		// Just send point on to group, we are not joining on specific dimensions.
		group := n.getOrCreateGroup(m.GroupID())
		group.Collect(src, m)
	}
	return nil
}

// The purpose of this method is to match more specific points
// with the less specific points as they arrive.
//
// Where 'more specific' means, that a point has more dimensions than the join.on dimensions.
func (n *JoinNode) matchPoints(p srcPoint) {
	// Specific points may be sent to the joinset without a matching point, but not the other way around.
	// This is because the specific points have the needed specific tag data.
	// The joinset will later handle the fill inner/outer join operations.

	if !n.allReported {
		n.reported[p.Src] = true
		n.allReported = len(n.reported) == len(n.ins)
	}
	t := p.Msg.Time().Round(n.j.Tolerance)

	groupId := models.ToGroupID(
		p.Msg.Name(),
		p.Msg.GroupInfo().Tags,
		models.Dimensions{
			ByName:   p.Msg.Dimensions().ByName,
			TagNames: n.j.Dimensions,
		},
	)
	// Update current srcGroup lowMark
	srcG := srcGroup{src: p.Src, groupId: groupId}
	n.lowMarks[srcG] = t

	// Determine lowMark, the oldest time per parent per group.
	var lowMark time.Time
	if n.allReported {
		for s := 0; s < len(n.ins); s++ {
			sg := srcGroup{src: s, groupId: groupId}
			if lm := n.lowMarks[sg]; lowMark.IsZero() || lm.Before(lowMark) {
				lowMark = lm
			}
		}
	}

	// Check for cached specific points that can now be sent alone.
	if n.allReported {
		// Send all cached specific point that won't match anymore.
		var i int
		buf := n.specificGroupsBuffer[groupId]
		l := len(buf)
		for i = 0; i < l; i++ {
			st := buf[i].Msg.Time().Round(n.j.Tolerance)
			if st.Before(lowMark) {
				// Send point by itself since it won't get a match.
				n.sendSpecificPoint(buf[i])
			} else {
				break
			}
		}
		// Remove all sent points.
		n.specificGroupsBuffer[groupId] = buf[i:]
	}

	if len(p.Msg.Dimensions().TagNames) > len(n.j.Dimensions) {
		// We have a specific point and three options:
		// 1. Find the cached match point and send both to group.
		// 2. Cache the specific point for later.
		// 3. Send the specific point alone if it is no longer possible that a match will arrive.

		// Search for a match.
		// Also purge any old match points.
		matches := n.matchGroupsBuffer[groupId]
		matched := false
		var i int
		l := len(matches)
		for i = 0; i < l; i++ {
			match := matches[i]
			pt := match.Msg.Time().Round(n.j.Tolerance)
			if pt.Equal(t) {
				// Option 1, send both points
				n.sendMatchPoint(p, match)
				matched = true
			}
			if !pt.Before(lowMark) {
				break
			}
		}
		if n.allReported {
			// Can't trust lowMark until all parents have reported.
			// Remove any unneeded match points.
			n.matchGroupsBuffer[groupId] = matches[i:]
		}

		// If the point didn't match that leaves us with options 2 and 3.
		if !matched {
			if n.allReported && t.Before(lowMark) {
				// Option 3
				// Send this specific point by itself since it won't get a match.
				n.sendSpecificPoint(p)
			} else {
				// Option 2
				// Cache this point for when its match arrives.
				n.specificGroupsBuffer[groupId] = append(n.specificGroupsBuffer[groupId], p)
			}
		}
	} else {
		// Cache match point.
		n.matchGroupsBuffer[groupId] = append(n.matchGroupsBuffer[groupId], p)

		// Send all specific points that match, to the group.
		var i int
		buf := n.specificGroupsBuffer[groupId]
		l := len(buf)
		for i = 0; i < l; i++ {
			st := buf[i].Msg.Time().Round(n.j.Tolerance)
			if st.Equal(t) {
				n.sendMatchPoint(buf[i], p)
			} else {
				break
			}
		}
		// Remove all sent points
		n.specificGroupsBuffer[groupId] = buf[i:]
	}
}

// Add the specific tags from the specific point to the matched point
// and then send both on to the group.
func (n *JoinNode) sendMatchPoint(specific, matched srcPoint) {
	var newMatched messageMeta
	switch msg := matched.Msg.(type) {
	case edge.BufferedBatchMessage:
		b := msg.ShallowCopy()
		b.SetBegin(b.Begin().ShallowCopy())
		b.Begin().SetTags(specific.Msg.GroupInfo().Tags)
		newMatched = b
	case edge.PointMessage:
		p := msg.ShallowCopy()
		info := specific.Msg.GroupInfo()
		p.SetTagsAndDimensions(info.Tags, info.Dimensions)
		newMatched = p
	}
	group := n.getOrCreateGroup(specific.Msg.GroupID())
	// Collect specific point
	group.Collect(specific.Src, specific.Msg)
	// Collect new matched point
	group.Collect(matched.Src, newMatched)
}

// Send only the specific point to the group
func (n *JoinNode) sendSpecificPoint(specific srcPoint) {
	group := n.getOrCreateGroup(specific.Msg.GroupID())
	group.Collect(specific.Src, specific.Msg)
}

// safely get the group for the point or create one if it doesn't exist.
func (n *JoinNode) getOrCreateGroup(groupID models.GroupID) *joinGroup {
	group := n.groups[groupID]
	if group == nil {
		group = n.newGroup(len(n.ins))
		n.groupsMu.Lock()
		n.groups[groupID] = group
		n.groupsMu.Unlock()
	}
	return group
}

func (n *JoinNode) newGroup(count int) *joinGroup {
	return &joinGroup{
		n:           n,
		sets:        make(map[string]*joinset),
		emittedSets: make(map[string]*joinset),
		head:        make([]time.Time, count),
	}
}

// handles emitting joined sets once enough data has arrived from parents.
type joinGroup struct {
	n           *JoinNode
	sets        map[string]*joinset
	emittedSets map[string]*joinset
	head        []time.Time
	oldestTime  time.Time
}

func (g *joinGroup) Finish() error {
	return g.emitAll()
}

// Collect a point from a given parent.
// emit the oldest set if we have collected enough data.
func (g *joinGroup) Collect(src int, p timeMessage) error {
	pointMessage, ok := p.(edge.PointMessage)
	if !ok {
		return fmt.Errorf("failed to convert timeMessage to PointMessage: unexpected type %T", p)
	}
	ctx, err := pointMessage.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Wrap(err, "call from ctx %+v is nil", ctx)
	}
	reqID := call.ReqID()

	// 获取已经发送过的joinset，用于goto循环
	// 只有开启缓存且存在已发送的joinset时才处理
	if g.n.j.CacheEmittedSets && g.emittedSets[reqID] != nil {
		emittedSet := g.emittedSets[reqID]
		emittedSet.Set(src, p)
		if err := g.emitJoinedSet(emittedSet); err != nil {
			return stderr.Wrap(err, "failed to emit cached joinset")
		}
		return nil
	}

	// Use sets to store joinset with reqID as key
	// 一个joinset中存储等待被join的所有PointMessage
	// g.sets结构为: map[reqID]*joinset，是一个map，它的value是joinset
	set, ok := g.sets[reqID]
	if !ok {
		set = g.newJoinset(p.Time())
		g.sets[reqID] = set
	}
	// src为channel编号
	set.Set(src, p)
	if err := g.emit(true); err != nil {
		return stderr.Wrap(err, "failed to emit new joinset")
	}
	return nil
}

func (g *joinGroup) newJoinset(t time.Time) *joinset {
	return newJoinset(
		g.n,
		g.n.j.StreamName,
		g.n.fill,
		g.n.fillValue,
		g.n.j.Names,
		g.n.j.Delimiter,
		g.n.j.Tolerance,
		t,
		g.n.diag,
	)
}

// emit a set and update the oldestTime.
func (g *joinGroup) emit(onlyReadySets bool) error {
	now := time.Now()

	// 处理当前的sets
	for reqID, set := range g.sets {
		// 删除等待超过joinTimeout的数据，防止g.sets保存过多无用数据，导致内存泄露
		if now.Sub(set.time) > joinTimeout {
			delete(g.sets, reqID)
			continue
		}

		// 如果set未就绪且要求只处理就绪的set，则跳过
		if onlyReadySets && !set.Ready() {
			continue
		}

		// 发送并处理set
		if err := g.emitJoinedSet(set); err != nil {
			return stderr.Wrap(err, "failed to emit joinset")
		}

		// 缓存已经发送的set，用于goto循环发送
		if g.n.j.CacheEmittedSets {
			g.emittedSets[reqID] = set
		}
		delete(g.sets, reqID)
	}

	// 对缓存的已发送set进行清理，防止内存泄露
	if g.n.j.CacheEmittedSets {
		for reqID, emittedSet := range g.emittedSets {
			if now.Sub(emittedSet.time) > joinTimeout {
				delete(g.emittedSets, reqID)
			}
		}
	}
	return nil
}

// emit sets until we have none left.
func (g *joinGroup) emitAll() error {
	return g.emit(false)
}

// emit a single joined set
func (g *joinGroup) emitJoinedSet(set *joinset) error {
	if set.name == "" {
		set.name = set.First().(edge.NameGetter).Name()
	}
	// FIXME: 目前tick中的消息类型全是StreamEdge，batchmessage流经到join会报错，目前限制了batchmessage只传递给下游的TextDispalyNode与HttpRspNode
	switch g.n.Wants() {
	case pipeline.StreamEdge:
		p, err := set.JoinIntoPoint()
		if err != nil {
			return errors.Wrap(err, "failed to join into point")
		}
		if p != nil {
			fields, err := trimKeysAndMergeValues(p.Fields(), g.n.j.Names)
			if err != nil {
				return stderr.Wrap(err, "failed to trim keys and merge values")
			}
			p.SetFields(fields)
			if err := edge.Forward(g.n.outs, p); err != nil {
				return err
			}
		}
	case pipeline.BatchEdge:
		b, err := set.JoinIntoBatch()
		if err != nil {
			return errors.Wrap(err, "failed to join into batch")
		}
		if b != nil {
			dealedPoints := make([]edge.BatchPointMessage, 0, len(b.Points()))
			for _, p := range b.Points() {
				dealedPoints = append(dealedPoints, p)
			}
			b.SetPoints(dealedPoints)
			if err := edge.Forward(g.n.outs, b); err != nil {
				return err
			}
		}
	}
	return nil
}

// A groupId and its parent
type srcGroup struct {
	src     int
	groupId models.GroupID
}

// represents a set of points or batches from the same joined time
type joinset struct {
	j         *JoinNode
	name      string
	fill      influxql.FillOption
	fillValue interface{}
	prefixes  []string
	delimiter string

	time      time.Time
	tolerance time.Duration
	values    []edge.Message

	expected int
	size     int
	finished int

	first int

	diag NodeDiagnostic
}

func newJoinset(
	n *JoinNode,
	name string,
	fill influxql.FillOption,
	fillValue interface{},
	prefixes []string,
	delimiter string,
	tolerance time.Duration,
	time time.Time,
	d NodeDiagnostic,
) *joinset {
	expected := len(prefixes)
	return &joinset{
		j:         n,
		name:      name,
		fill:      fill,
		fillValue: fillValue,
		prefixes:  prefixes,
		delimiter: delimiter,
		expected:  expected,
		values:    make([]edge.Message, expected),
		first:     expected,
		time:      time,
		tolerance: tolerance,
		diag:      d,
	}
}

func (js *joinset) Ready() bool {
	return js.size == js.expected
}

func (js *joinset) Has(i int) bool {
	return js.values[i] != nil
}

// add a point to the set from a given parent index.
func (js *joinset) Set(i int, v edge.Message) {
	if i < js.first {
		js.first = i
	}
	js.values[i] = v
	// 当接收到足够消息后，不再增加size(goto算子循环join)
	if js.size >= js.expected {
		return
	}
	js.size++
}

// a valid point in the set
func (js *joinset) First() edge.Message {
	return js.values[js.first]
}

// join all points into a single point
func (js *joinset) JoinIntoPoint() (edge.PointMessage, error) {
	first, ok := js.First().(edge.PointMessage)
	if !ok {
		return nil, fmt.Errorf("unexpected type of first value %T", js.First())
	}
	firstFields := first.Fields()
	fields := make(models.Fields, js.size*len(firstFields))
	for i, v := range js.values {
		if v == nil {
			switch js.fill {
			case influxql.NullFill:
				for k := range firstFields {
					fields[js.prefixes[i]+js.delimiter+k] = nil
				}
			case influxql.NumberFill:
				for k := range firstFields {
					fields[js.prefixes[i]+js.delimiter+k] = js.fillValue
				}
			default:
				// inner join no valid point possible
				return nil, nil
			}
		} else {
			p, ok := v.(edge.FieldGetter)
			if !ok {
				return nil, fmt.Errorf("unexpected type %T", v)
			}
			for k, v := range p.Fields() {
				fields[js.prefixes[i]+js.delimiter+k] = v
			}
		}
	}
	np := edge.NewPointMessage(
		js.name, "", "",
		first.Dimensions(),
		fields,
		first.GroupInfo().Tags,
		js.time,
	)
	return np, nil
}

// join all batches the set into a single batch
func (js *joinset) JoinIntoBatch() (edge.BufferedBatchMessage, error) {
	first, ok := js.First().(edge.BufferedBatchMessage)
	if !ok {
		return nil, fmt.Errorf("unexpected type of first value %T", js.First())
	}
	newBegin := edge.NewBeginBatchMessage(
		js.name,
		models.NodeMeta{},
		models.Trace{},
		models.Context{},
		first.Tags(),
		first.Dimensions().ByName,
		js.time,
		0,
	)
	newPoints := make([]edge.BatchPointMessage, 0, len(first.Points()))
	empty := make([]bool, js.expected)
	emptyCount := 0
	indexes := make([]int, js.expected)
	var fieldNames []string

BATCH_POINT:
	for emptyCount < js.expected {
		set := make([]edge.BatchPointMessage, js.expected)
		setTime := time.Time{}
		count := 0
		for i, batch := range js.values {
			if empty[i] {
				continue
			}
			if batch == nil {
				emptyCount++
				empty[i] = true
				continue
			}
			b, ok := batch.(edge.BufferedBatchMessage)
			if !ok {
				return nil, fmt.Errorf("unexpected type of batch value %T", batch)
			}
			if indexes[i] == len(b.Points()) {
				emptyCount++
				empty[i] = true
				continue
			}
			bp := b.Points()[indexes[i]]
			t := bp.Time().Round(js.tolerance)
			if setTime.IsZero() {
				setTime = t
			}
			if t.Before(setTime) {
				// We need to backup
				setTime = t
				for j := range set {
					if set[j] != nil {
						indexes[j]--
					}
					set[j] = nil
				}
				set[i] = bp
				indexes[i]++
				count = 1
			} else if t.Equal(setTime) {
				if fieldNames == nil {
					for k := range bp.Fields() {
						fieldNames = append(fieldNames, k)
					}
				}
				set[i] = bp
				indexes[i]++
				count++
			}
		}
		// we didn't get any points from any group we must be empty
		// skip this set
		if count == 0 {
			continue
		}
		// Join all batch points in set
		fields := make(models.Fields, js.expected*len(fieldNames))
		for i, bp := range set {
			if bp == nil {
				switch js.fill {
				case influxql.NullFill:
					for _, k := range fieldNames {
						fields[js.prefixes[i]+js.delimiter+k] = nil
					}
				case influxql.NumberFill:
					for _, k := range fieldNames {
						fields[js.prefixes[i]+js.delimiter+k] = js.fillValue
					}
				default:
					// inner join no valid point possible
					continue BATCH_POINT
				}
			} else {
				for k, v := range bp.Fields() {
					fields[js.prefixes[i]+js.delimiter+k] = v
				}
			}
		}
		bp := edge.NewBatchPointMessage(
			fields,
			newBegin.Tags(),
			setTime,
		)
		newPoints = append(newPoints, bp)
	}
	newBegin.SetSizeHint(len(newPoints))
	return edge.NewBufferedBatchMessage(
		newBegin,
		newPoints,
		edge.NewEndBatchMessage(),
	), nil
}

type durationVar struct {
	expvar.Int
}

func (d *durationVar) String() string {
	return time.Duration(d.IntValue()).String()
}

const JOIN_DELIMITER = "."

// trimKeysAndMergeValues 去除key的alias前缀，合并多个上游节点传来的Output与Trace
// 参数：
//   - fields models.Fields:
//     {
//     "alias1.Trace":   models.Trace,
//     "alias1.Context": models.Context,
//     "alias1.Output":  any,
//     "alias2.Trace":   models.Trace,
//     "alias2.Context": models.Context,
//     "alias2.Output":  any
//     ...
//     }
//   - aliases []string: ["alias1", "alias2", ...]
//
// 返回值：
//
//   - result models.Fields:
//
//     {
//     "Trace": models.Trace,
//     "Context": models.Context,
//     "Output": {
//     "alias1": any,
//     "alias2": any,
//     ...
//     }
//     }
//
//   - err error
func trimKeysAndMergeValues(fields models.Fields, aliases []string) (models.Fields, error) {
	// key以alias+delimiter开头，value放到同一个map[string]any中
	trimedMap := make(map[string]map[string]any)
	for k, v := range fields {
		// 判断key是否以alias+delimiter开头
		for _, alias := range aliases {
			prefix := alias + JOIN_DELIMITER
			if strings.HasPrefix(k, prefix) {
				// 创建alias对应的map[string]any
				if _, ok := trimedMap[alias]; !ok {
					trimedMap[alias] = make(map[string]any)
				}
				// 提取原始的key
				originalKey := strings.TrimPrefix(k, prefix)
				if originalKey == "" {
					return nil, stderr.Error("key %s is not valid. it does not have original key", k)
				}
				trimedMap[alias][originalKey] = v
				break
			}
		}
	}

	// 合并多个上游节点传来的Output与Trace数据
	// trimedMap:
	// {
	//     "alias1": {
	//         "Output": any,
	//         "Context": models.Context,
	//         "Trace": models.Trace
	//     },
	//     "alias2": {
	//         "Output": any,
	//         "Context": models.Context,
	//         "Trace": models.Trace
	//     },
	//     ...
	// }
	traceKey := models.PredefinedFieldTrace
	outputKey := models.PredefinedFieldOutput
	contextKey := models.PredefinedFieldContext
	mergedTrace := make(models.Trace)
	mergedOutput := make(map[string]any)
	mergedContext := models.Context{}
	for alias, aliasMap := range trimedMap {
		// TODO: 如果之后分叉的message共用Trace，无需合并Trace
		// merge Trace
		trace, ok := aliasMap[traceKey].(models.Trace)
		if !ok {
			return nil, stderr.Error("unexpected type %T of fields %s", aliasMap[traceKey], traceKey)
		}
		for k, v := range trace {
			mergedTrace[k] = v
		}
		// merge Output
		output, ok := aliasMap[outputKey]
		if !ok {
			return nil, stderr.Error("there is no %s in %s alias fields", outputKey, alias)
		}
		mergedOutput[alias] = output
		// merge Context
		// Context: TaskID、Call、Meta、FileSfsUrl、FileHttpUrl
		// 保存第一个fileSfsUrl不为空的Context
		if mergedContext.FileSfsUrl == "" {
			context, ok := aliasMap[contextKey].(models.Context)
			if !ok {
				return nil, stderr.Error("unexpected type %T of fields %s", aliasMap[contextKey], contextKey)
			}
			mergedContext = context
		}
	}
	finalFields := make(models.Fields)
	finalFields[traceKey] = mergedTrace
	finalFields[outputKey] = mergedOutput
	finalFields[contextKey] = mergedContext
	return finalFields, nil
}
