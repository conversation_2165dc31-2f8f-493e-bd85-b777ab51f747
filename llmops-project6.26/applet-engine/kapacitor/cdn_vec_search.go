package kapacitor

import (
	"strings"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/vecdb"
)

type VecSearchNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.VecSearchNode // 算子的参数定义
	c  vecdb.Client
}

// newVecSearchNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newVecSearchNode(et *ExecutingTask, n *pipeline.VecSearchNode, d NodeDiagnostic) (*VecSearchNode, error) {
	hn := &VecSearchNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	if n.U == nil {
		return nil, stderr.Internal.Error("db url is nil")
	}

	dbc, err := vecdb.NewVecDBClientFromURL(et.ctx, n.U)
	if err != nil {
		return nil, stderr.Wrap(err, "new vec db client with url: %s", n.Url)
	}

	hn.c = dbc
	hn.node.runF = hn.run
	hn.node.stopF = dbc.Close
	return hn, nil
}

func (n *VecSearchNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *VecSearchNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	nodeInPort, err := extracNodeInPortFromFields[widgets.NodeInPortVectorSearch](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to get node inPort values")
	}

	if nodeInPort.OpenAiTextVectorResp == nil {
		return stderr.Wrap(err, "the field of openai_embedding_resp can not be nil")
	}

	embeddedData := nodeInPort.OpenAiTextVectorResp.GetFloat64Slice()

	texts := make([]string, 0)
	outs := []string{vecdb.VectorDBFieldID, vecdb.VectorDBFieldTitle, vecdb.VectorDBFieldText}
	var vecs [][]float32
	for _, emb := range embeddedData {
		vecs = append(vecs, models.CvtFloatSlice[float64, float32](emb))
	}
	params := &vecdb.SearchParams{
		TopK:         int(n.pn.TopK),
		Vectors:      vecs,
		OutputFields: outs,
		Expr:         "",
		Params: map[string]any{
			"nprobe": 10,
		},
	}
	searchRes, err := n.c.Search(n.et.ctx, params)
	if err != nil {
		return stderr.Wrap(err, "vec search")
	}

	for _, item := range searchRes.Results {
		tc := item.GetField(vecdb.VectorDBFieldText)
		if tc == nil {
			return stderr.Internal.Error("expect field '%s' not found", vecdb.VectorDBFieldText)
		}

		ts, err := utils.CvtAny2TSlice[string](tc.Data)
		if err != nil {
			return stderr.Internal.Cause(err, "convert text field as string")
		}
		texts = append(texts, ts...)
	}
	if len(texts) == 0 {
		ctx.Warnf("no text found")
	}
	ctx.Infof("total found %d texts", len(texts))
	text := strings.Join(texts, "\n")
	if err := setOutputIntoFields(p.Fields(), text); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", text)
	}
	return n.forwardMsg(p, true)
}
