package kapacitor

import (
	"fmt"

	"transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextRerankNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextRerankNode // 算子的参数定义
}

// newTextSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextRerankNode(et *ExecutingTask, n *pipeline.TextRerankNode, d NodeDiagnostic) (*TextRerankNode, error) {
	hn := &TextRerankNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextRerankNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextRerankNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	ctx, err := util.GetContextFromPointMessages(p)
	if err != nil {
		return err
	}
	outputMap, err := extracOutputFromFields[map[string]any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}

	question, err := extracValueFromMapOutput[string](outputMap, widgets.ParamIDQuestion)
	if err != nil {
		return stderr.Wrap(err, "extract question from fields")
	}

	retrieveChunks := make([]*pb.ChunkRetrieveResult, 0)
	// Store original extra fields
	originalExtras := make(map[string]map[string]string)

	chunks, err := tryCvt2Chunks(outputMap[widgets.ParamIDTexts])
	if err != nil {
		return
	}
	for _, chunk := range chunks {
		retrieveChunks = append(retrieveChunks, &pb.ChunkRetrieveResult{Chunk: chunk})
		if chunk.Extra != nil && chunk.Id != "" {
			originalExtras[chunk.Id] = chunk.Extra
		}
	}

	widgetParams := n.pn.WidgetParamsTextRerank
	rerankParams := &pb.RerankParams{
		TopK:           int32(widgetParams.TopK),
		ScoreThreshold: float32(widgetParams.Threshold),
		Model:          &widgetParams.ModelToolDescriber.ModelService,
	}

	rerankRes, err := clients.RerankChunksAndFilter(ctx, question, rerankParams, retrieveChunks)
	if err != nil {
		return err
	}
	outChunks := make([]*pb.Chunk, 0)
	for _, rc := range rerankRes {
		chunk := rc.Chunk
		// Restore original extra fields and update score
		if origExtra, exists := originalExtras[chunk.Id]; exists {
			chunk.Extra = origExtra
		} else {
			chunk.Extra = make(map[string]string)
		}
		chunk.Extra["score"] = fmt.Sprintf("%f", rc.Score)
		outChunks = append(outChunks, chunk)
	}
	// pb.chunk  score字段
	if err := setOutputIntoFields(p.Fields(), outChunks); err != nil {
		return err
	}
	return n.forwardMsg(p, true)
}
