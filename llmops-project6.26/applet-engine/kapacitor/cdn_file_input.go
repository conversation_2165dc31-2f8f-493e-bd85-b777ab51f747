package kapacitor

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"github.com/google/uuid"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type FileInputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.FileInputNode // 算子的参数定义
}

// newFileInputNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newFileInputNode(et *ExecutingTask, n *pipeline.FileInputNode, d NodeDiagnostic) (*FileInputNode, error) {
	hn := &FileInputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *FileInputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *FileInputNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}

	if n.pn.ParsedParams.IsEnhanceMode() {
		return n.handleEnhanceMode(p)
	}

	isListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}
	if !isListenHttp {
		sfsFile, exist, err := models.GetValueOfFields[*engine.SFSFile](p.Fields(), models.PredefinedFieldOutput)
		if err != nil || !exist {
			return stderr.Wrap(err, "failed to extract filesBytes from fields")
		}
		return n.forwardSFSFile(p, sfsFile)
	}

	output, exist, err := models.GetValueOfFields[map[string]any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	// 尝试从 {nodeid}##{InputKey} key中获取文件
	inputFieldKey := n.pn.InputFieldKey(n.pn.Id)
	filesAny, exist, err := models.GetValueOfFields[[]any](output, inputFieldKey)
	if err != nil {
		return stderr.Wrap(err, "failed to extract %s from output", inputFieldKey)
	}
	if !exist {
		// 如果找不到 {nodeid}##{InputKey} key，则尝试从 {InputKey} key中获取文件
		filesAny, exist, err = models.GetValueOfFields[[]any](output, n.pn.InputKey)
		if err != nil {
			return stderr.Wrap(err, "failed to extract %s from output", n.pn.InputKey)
		}
		if !exist {
			// 如果 {nodeid}##{InputKey} 与 {InputKey} 都不存在，直接向后传递空字节数组
			ctx.Warnf("there is no %s key, forward empty value", n.pn.InputKey)
			return n.forwardSFSFile(p, new(engine.SFSFile))
		}
	}
	sfsFiles := make(SFSFiles, 0)
	if err := sfsFiles.FromAnySlice(filesAny); err != nil {
		return stderr.Wrap(err, "parse SFSFiles from any slice with type %T", filesAny)
	}
	if sfsFiles.Len() == 0 {
		ctx.Warnf("length of sfsFiles is 0, forward empty sfsfile")
		return n.forwardSFSFile(p, new(engine.SFSFile))
	}
	if sfsFiles.Len() > 1 {
		return stderr.Errorf("multi files not supported yet")
	}

	sfsFile := sfsFiles[0]
	// 获取租户和项目ID
	tenantId := conf.Config.Tenant.TenantId
	projectId := conf.Config.Tenant.ProjectId
	isPublished := tenantId != "" || projectId != ""
	if isPublished && (tenantId == "" || projectId == "") {
		return stderr.Errorf("service is published but tenantId: %s or projectId: %s is empty", tenantId, projectId)
	}
	if sfsFile.Url == "" {
		// 检查 Content 是否存在
		if len(sfsFile.Content) == 0 {
			// 如果 url 和 content 都为空，则直接转发空文件
			ctx.Warnf("both sfs file url and content are empty, forward empty sfsfile")
			return n.forwardSFSFile(p, new(engine.SFSFile))
		}
		// Content 的文件名不能为空
		if sfsFile.Name == "" {
			return stderr.Errorf("sfs content has no file name, can not save to file")
		}
		if !isPublished {
			// 未发布服务，从请求上下文获取租户和项目ID
			if ctx.Call == nil {
				return stderr.Errorf("call context is nil")
			}
			callCtx := ctx.Call.Ctx()
			tenantId = helper.GetTenantID(callCtx)
			projectId = helper.GetProjectID(callCtx)
			ctx.Infof("get tenantId: %s, projectId: %s from call context", tenantId, projectId)
			if tenantId == "" || projectId == "" {
				return stderr.Errorf("tenant ID or project ID is empty. tenantId: %s, projectId: %s", tenantId, projectId)
			}
		}
		// 使用 saveContentToFile 保存文件
		sfsPath, err := saveContentToFile(tenantId, projectId, sfsFile.Name, sfsFile.Content, isPublished)
		if err != nil {
			return stderr.Wrap(err, "failed to save content to file")
		}
		// 更新文件路径为 sfs 路径
		sfsFile.Url = sfsPath
		ctx.Infof("saved file content to %s", sfsPath)
	}
	// 获取文件的本地路径
	localFilePath, err := stdfs.GetSFSLocalPath(sfsFile.Url)
	if err != nil {
		return stderr.Wrap(err, "get local path of sfs file %s", sfsFile.Url)
	}
	// 设置http url
	sfsFile.HttpUrl = stdfs.SFSFilePath(sfsFile.Url).ToHttpUrlV2(util.GetSystemNamespace())
	// 已发布服务，需要将路径中的租户ID去掉
	if isPublished {
		// /sfs/tenants/llmops-assets/projs/assets/xxx  ->  /sfs/projs/assets/xxx
		localFilePath = sfsTenantRootRe.ReplaceAllString(localFilePath, "")
	}

	// 检查文件大小
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return stderr.Wrap(err, "get file info for %s", localFilePath)
	}
	fileSizeMB := float64(fileInfo.Size()) / script.MB
	if fileSizeMB > float64(n.pn.ParsedParams.MaxFileSizeMB) {
		return stderr.Errorf("file size %.2f MB exceeds maximum allowed size of %d MB", fileSizeMB, n.pn.ParsedParams.MaxFileSizeMB)
	}

	// 检查文件扩展名
	if len(n.pn.ParsedParams.AllowedExtensions) > 0 {
		fileExt := strings.ToLower(filepath.Ext(localFilePath))
		isAllowed := false
		for _, allowedExt := range n.pn.ParsedParams.AllowedExtensions {
			if allowedExt == "*" || allowedExt == fileExt {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			return stderr.Error("file extension %s is not allowed", fileExt)
		}
	}

	if n.pn.ParsedParams.IsFileContentRead {
		fileBytes, err := os.ReadFile(localFilePath)
		if err != nil {
			return stderr.Internal.Cause(err, "read file %s", sfsFile.Url)
		}
		sfsFile.Content = fileBytes
	}
	ctx.FileSfsUrl = sfsFile.Url
	ctx.FileHttpUrl = sfsFile.HttpUrl
	p.SetContext(ctx)
	return n.forwardSFSFile(p, sfsFile)
}

// handleEnhanceMode  输入文件列表,输出也是文件列表
func (n *FileInputNode) handleEnhanceMode(p edge.PointMessage) error {
	isPreListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}

	inputFiles := new([]*engine.SFSFile)
	if isPreListenHttp {
		inputFiles, err = extractInputNode[[]*engine.SFSFile](n.pn.InputKey, p)
	} else {
		inputFiles, err = extractOutPut[[]*engine.SFSFile](p)
	}
	if err != nil {
		return stderr.Wrap(err, "failed to extra sfs file")
	}

	if !n.pn.ParsedParams.EnableMutilFileMode && len(*inputFiles) > 1 {
		return stderr.Errorf("exist mutil files, please enable mutil file mode ")
	}

	allFiles := make([]*engine.SFSFile, 0)
	for _, sfsFile := range *inputFiles {
		fileInfo, err := StatLocalFile(sfsFile.Url)
		if err != nil {
			stderr.Wrap(err, "stat local file: %s", sfsFile.Url)
		}
		if !n.isAllowedFileSize(fileInfo.Size()) {
			return stderr.Errorf("the size of file is too larger")
		}

		if !n.isAllowedExtension(sfsFile.Url) {
			return stderr.Errorf("the ext of file is not allowed")
		}

		if n.pn.ParsedParams.EnableUnpackMode && isArchiveFile(sfsFile.Url) {
			files, err := extractFromArchiveFile(sfsFile.Url)
			if err != nil {
				return stderr.Wrap(err, "extract files from archive file")
			}
			allFiles = append(allFiles, files...)
		} else {
			allFiles = append(allFiles, sfsFile)
		}
	}

	// 读取文件内容并设置httpUrl
	for _, f := range allFiles {
		f.HttpUrl = stdfs.SFSFilePath(f.Url).ToHttpUrlV2(util.GetSystemNamespace())
		if n.pn.ParsedParams.IsFileContentRead {
			content, err := ReadLocalFile(f.Url)
			if err != nil {
				return stderr.Wrap(err, "read local file %s", f.Url)
			}
			f.Content = content
		}
	}
	return n.ForwardMsg(p, allFiles)
}

// isAllowedExtension 检查文件扩展名是否允许
func (n *FileInputNode) isAllowedExtension(sfsUrl string) bool {
	fileExt := getStdExt(sfsUrl)
	if len(n.pn.ParsedParams.AllowedExtensions) == 0 {
		return true
	}
	for _, allowedExt := range n.pn.ParsedParams.AllowedExtensions {
		if allowedExt == "*" || allowedExt == fileExt {
			return true
		}
	}
	return false
}

// isAllowedFileSize 检查文件大小是否允许
func (n *FileInputNode) isAllowedFileSize(size int64) bool {
	fileSizeMB := float64(size) / script.MB
	return fileSizeMB <= float64(n.pn.ParsedParams.MaxFileSizeMB)
}

// isArchiveFile 判断文件名是否为压缩包
func isArchiveFile(sfsUrl string) bool {
	fileExt := getStdExt(sfsUrl)
	return fileExt == ExtZip || fileExt == ExtTar || fileExt == ExtGz
}

func getStdExt(sfsUrl string) string {
	return strings.ToLower(filepath.Ext(sfsUrl))
}

// extractFromArchiveFile 解压tar/gz/zip文件到同级目录，并收集文件信息（只解压一层）
func extractFromArchiveFile(sfsUrl string) ([]*engine.SFSFile, error) {
	ret := make([]*engine.SFSFile, 0)
	localFilePath, err := GetLocalFilePath(sfsUrl)
	if err != nil {
		return nil, stderr.Wrap(err, "get local file path")
	}
	// 创建与压缩文件同名的目录,用于存放解压后的文件
	stdExt := getStdExt(localFilePath)
	targetDir := filepath.Join(filepath.Dir(localFilePath), uuid.New().String())
	switch stdExt {
	case ExtZip:
		zf, err := zip.OpenReader(localFilePath)
		if err != nil {
			return nil, stderr.Wrap(err, "open reader")
		}
		defer zf.Close()
		for _, f := range zf.File {
			if f.FileInfo().IsDir() {
				return nil, stderr.Wrap(err, "is not support dir in archive file")
			}
			rc, err := f.Open()
			if err != nil {
				return nil, stderr.Wrap(err, "open file")
			}
			// 构建目标路径，并写入文件
			targetPath := filepath.Join(targetDir, f.Name)
			if err := saveFile(targetPath, rc); err != nil {
				return nil, stderr.Wrap(err, "save file")
			}
			ret = append(ret, &engine.SFSFile{
				Name: f.Name,
				Url:  ToSFSUrl(targetPath),
			})
		}
	case ExtTar, ExtGz:
		file, err := os.Open(localFilePath)
		if err != nil {
			return nil, stderr.Wrap(err, "open file")
		}
		defer file.Close()

		var tr *tar.Reader
		if stdExt == ExtGz {
			gzReader, err := gzip.NewReader(file)
			if err != nil {
				return nil, stderr.Wrap(err, "open reader")
			}
			defer gzReader.Close()
			tr = tar.NewReader(gzReader)
		} else {
			tr = tar.NewReader(file)
		}

		for {
			hdr, err := tr.Next()
			if err == io.EOF {
				break
			}
			if err != nil {
				return nil, err
			}
			if hdr.FileInfo().IsDir() {
				return nil, stderr.Wrap(err, "is not support dir in archive file")
			}
			// 构建目标路径，并写入文件
			targetPath := filepath.Join(targetDir, hdr.Name)
			if err := saveFile(targetPath, tr); err != nil {
				return nil, stderr.Wrap(err, "save file")
			}
			ret = append(ret, &engine.SFSFile{
				Name: hdr.Name,
				Url:  ToSFSUrl(targetPath),
			})
		}
	default:
		return nil, stderr.Errorf("the ext of archive file is not supported")
	}
	return ret, nil
}
func saveFile(targetPath string, r io.Reader) error {
	content, err := io.ReadAll(r)
	if err != nil {
		return stderr.Wrap(err, "read io.Reader")
	}
	// 创建父目录
	if err := os.MkdirAll(filepath.Dir(targetPath), fs.ModePerm); err != nil {
		return stderr.Wrap(err, "mk dir")
	}
	// 写入文件（覆盖模式）
	if err := os.WriteFile(targetPath, content, fs.ModePerm); err != nil {
		return stderr.Wrap(err, "write file")
	}
	return nil
}

func (n *FileInputNode) forwardSFSFile(p edge.PointMessage, file *engine.SFSFile) error {
	if err := setOutputIntoFields(p.Fields(), file); err != nil {
		return stderr.Wrap(err, "set sfs file %s into output field", file.Name)
	}
	return n.forwardMsg(p, true)
}
