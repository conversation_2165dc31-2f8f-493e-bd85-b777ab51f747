package kapacitor

import (
	"sync"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func TestSyncRunTasks(t *testing.T) {
	queries := []string{"11", "22"}
	var SSEMutex sync.Mutex
	tasks := make([]func() error, 0)
	outChunks := make([]*pb.Chunk, 0)
	for _, query := range queries {
		task := createSearchTask(nil, nil, query, &outChunks, &SSEMutex)
		tasks = append(tasks, task)
	}
	if err := helper.SyncRunTasks(tasks); err != nil {
	}
	stdlog.Info(outChunks)
}
