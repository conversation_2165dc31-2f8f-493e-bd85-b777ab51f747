// netstat_alert_batch

// metric: 'tcp_established'
// available_fields:  "tcp_close","tcp_close_wait","tcp_closing","tcp_established","tcp_fin_wait1","tcp_fin_wait2","tcp_last_ack","tcp_listen","tcp_none","tcp_syn_recv","cp_syn_sent","tcp_time_wait","udp_socket","tcp_listen"

// TELEGRAF CONFIGURATION
// [[inputs.netstat]]

// DEFINE: kapacitor define netstat_alert_batch -type batch -tick netstat/netstat_alert_batch.tick -dbrp telegraf.autogen
// ENABLE: kapacitor enable netstat_alert_batch

// Parameters
var info = 20
var warn = 40
var crit = 60
var infoSig = 2.5
var warnSig = 3
var critSig = 3.5
var period = 10s
var every = 10s

// Dataframe
var data = batch
  |query('''SELECT mean(tcp_established) AS stat FROM "telegraf"."autogen"."netstat" ''')
    .period(period)
    .every(every)
    .groupBy('host')
    
// Thresholds
var alert = data
  |eval(lambda: sigma("stat"))
    .as('sigma')
    .keep()
  |alert()
    .id('{{ index .Tags "host"}}/tcp_conns')
    .message('{{ .ID }}:{{ index .Fields "stat" }}')
    .info(lambda: "stat" > info OR "sigma" > infoSig)
    .warn(lambda: "stat" > warn OR "sigma" > warnSig)
    .crit(lambda: "stat" > crit OR "sigma" > critSig)

// Alert
alert
  .log('/tmp/netstat_alert_batch_log.txt')