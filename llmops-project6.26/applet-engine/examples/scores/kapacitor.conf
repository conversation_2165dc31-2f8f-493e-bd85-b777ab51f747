# The hostname of this node.
# Must be resolvable by any configured InfluxDB hosts.
hostname = "localhost"
# Directory for storing a small amount of metadata about the server.
data_dir = "/var/lib/kapacitor"
# Your Enterprise token. By using Enterprise you can
# send all internal statistics to the Enterprise
# endpoints which will store and report on the
# activity of your instances.
token = ""

[http]
  # HTTP API Server for Kapacitor
  # This server is always on,
  # it servers both as a write endpoint
  # and as the API endpoint for all other
  # Kapacitor calls.
  bind-address = ":9092"
  auth-enabled = false
  log-enabled = true
  write-tracing = false
  pprof-enabled = false
  https-enabled = false
  https-certificate = "/etc/ssl/kapacitor.pem"


[[udp]]
  # UDP input for the scores stream
  enabled = false
  bind-address = ":9100"
  database = "game"
  retention-policy = "default"


[logging]
    # Destination for logs
    # Can be a path to a file or 'STDOUT', 'STDERR'.
    file = "/var/log/kapacitor/kapacitor.log"
    # Logging level can be one of:
    # DEBUG, INFO, WARN, ERROR, or OFF
    level = "INFO"

[replay]
  # Where to store replay files, aka recordings.
  dir = "/var/lib/kapacitor/replay"

[task]
  # Where to store the tasks database
  dir = "/var/lib/kapacitor/tasks"

[influxdb]
  # Connect to an InfluxDB cluster
  # Kapacitor can subscribe, query and write to this cluster.
  # Using InfluxDB is not required and can be disabled.
  enabled = true
  urls = ["http://localhost:8086"]
  username = ""
  password = ""
  timeout = 0
  [influxdb.subscriptions]
    # Set of databases and retention policies to subscribe to.
    # If empty will subscribe to all.
    #
    # Format
    # db_name = <list of retention policies>
    #
    # Example:
    # my_database = [ "default", "longterm" ]

[smtp]
  # Configure an SMTP email server
  # Will use TLS and authentication if possible
  # Only necessary for sending emails from alerts.
  enabled = false
  host = "localhost"
  port = 25
  username = ""
  password = ""
  # Close idle connections after timeout
  idle-timeout = "30s"

[reporting]
  # Send usage statistics
  # every 12 hours to Enterprise.
  enabled = false
  enterprise-url = "https://enterprise.influxdata.com"
  # The interval at which to send all
  # internal statistics to Enterprise.
  # If no token is specified this
  # setting has no effect.
  stats-interval = "1m0s"

##################################
# Input Methods, same as InfluxDB
#

[collectd]
  enabled = false
  bind-address = ":25826"
  database = "collectd"
  retention-policy = ""
  batch-size = 1000
  batch-pending = 5
  batch-timeout = "10s"
  typesdb = "/usr/share/collectd/types.db"

[opentsdb]
  enabled = false
  bind-address = ":4242"
  database = "opentsdb"
  retention-policy = ""
  consistency-level = "one"
  tls-enabled = false
  certificate = "/etc/ssl/influxdb.pem"
  batch-size = 1000
  batch-pending = 5
  batch-timeout = "1s"

