package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

// agent算子提示词
const (
	TempReplaceString   = "__TEMP_REPLACE_STRING__"
	StopWordObservation = "Observation"
	FinalAnswer         = "Final Answer:"
	ThinkStart          = "<think>"
	ThinkEnd            = "</think>"
	Thought             = "Thought:"
	Action              = "Action:"
	ActionInput         = "Action Input:"
	JsonStart           = '{'
	JsonEnd             = '}'
)

//const DefaultLanguageInstruction = "如果没有特别说明，请使用和问题（任务）一致的语言进行回答用户问题"

const ChatHistoryInstructionTemplate = `
The content within the <chat-history></chat-history> XML tags is your historical chat record. Use it if necessary.
<chat-history>
%s
</chat-history>
`

const ContextInstructionTemplate = `
The content within the <context></context> XML tags is your context. Use it if necessary. Please use markdown format "![image name](image link)" to display any images you need to show.
<context>
%s
</context>
`

const FileInstructionTemplate = `
The content within the <file></file> XML tags are files user uploaded.
<file>
%s
</file>
`

const ReActInstructionTemplate = `
Answer the following questions as best you can. You have access to the following tools:
%s

Use the following format:

Question: the input question you must answer
Thought: you should alwaws OUTPUT the "Thought:" and you should always think about what to do
Action: the action to take, should be one of [%s]
Action Input: the input to the action 
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can be repeated zero or more times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!
`

const ToolDescriptionTemplate = `
%s: Call this tool to interact with the %s API. What is the %s API useful for? %s. Parameters: %s. Format the arguments as a JSON object.
`

const ReActScratchpadTemplate = `
Thought: %s
Action: %s
Action Input: %s
Observation: %s
`

const QuestionTemplate = `
Unless otherwise specified, please answer the question in the same language as the Question.
Question: %s
`

func FillChatHistory(chatHistory string) string {
	return fmt.Sprintf(ChatHistoryInstructionTemplate, chatHistory)
}

func FillContext(context string) string {
	return fmt.Sprintf(ContextInstructionTemplate, context)
}

func FillFile(file string) string {
	return fmt.Sprintf(FileInstructionTemplate, file)
}

func FillReAct(toolNames, toolDescriptions string) string {
	return fmt.Sprintf(ReActInstructionTemplate, toolDescriptions, toolNames)
}

func FillToolDescription(tool agent_definition.Tool) (string, error) {
	bs, err := json.Marshal(tool.Parameters)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf(ToolDescriptionTemplate, tool.NameForModel, tool.NameForHuman, tool.NameForHuman, tool.Description, string(bs)), nil
}

func FillReActScratchpad(thought, action, actionInput, observation string) string {
	return fmt.Sprintf(ReActScratchpadTemplate, thought, action, actionInput, observation)
}

func FillQuestion(question string) string {
	return fmt.Sprintf(QuestionTemplate, question)
}

func FillEssentialPrompt(customInstruction, chatHistory, context, file string) string {
	defaultTimeInstruction := "current time: " + GetCurrentTime()
	essentialPrompt := customInstruction + "\n" + defaultTimeInstruction + "\n"
	if strings.TrimSpace(chatHistory) != "" {
		essentialPrompt += FillChatHistory(chatHistory)
	}
	if strings.TrimSpace(context) != "" {
		essentialPrompt += FillContext(context)
	}
	if strings.TrimSpace(file) != "" {
		essentialPrompt += FillFile(file)
	}
	return essentialPrompt
}

func FillSimplePrompt(customInstruction, chatHistory, context, file, question string) string {
	simplePrompt := FillEssentialPrompt(customInstruction, chatHistory, context, file)
	simplePrompt += FillQuestion(question)
	return simplePrompt
}

func FillPromptWithReAct(customInstruction, chatHistory, context, file, toolNames, toolDescriptions, question string) string {
	reActPrompt := FillEssentialPrompt(customInstruction, chatHistory, context, file)
	reActPrompt += FillReAct(toolNames, toolDescriptions)
	reActPrompt += FillQuestion(question)
	return reActPrompt
}

func GetCurrentTime() string {
	timeLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		stdlog.Warnf("Failed to load Asia/Shanghai timezone: %v. Using system default.", err)
		timeLocation = time.Local
	}

	now := time.Now().In(timeLocation)
	return now.Format("2006-01-02 15:04")
}

// 其他算子

const ParameterExtractorPromptTemplate = `
You are a helpful assistant tasked with extracting structured information based on specific criteria provided. Follow the guidelines below to ensure consistency and accuracy.

### Task
Extract the correct parameters. Ensure that the information extraction is contextual and aligns with the provided criteria.

### Instructions:
Always adhere to these instructions as closely as possible:
Steps:
1. Extract the relevant information based on the criteria given, output multiple values if there is multiple relevant information that match the criteria in the given text.
2. Generate a well-formatted output using the defined functions and arguments.
3. Generate structured outputs with appropriate parameters.
4. Do not include any XML tags in your output.

### Example
#### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
{"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the weather information", "required": true}}, "required": ["location"]}
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
What is the weather today in SF?
</text>
#### Assistant Output
{"location": "San Francisco"}
#### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
{"type": "object", "properties": {"food": {"type": "string", "description": "The food to eat", "required": true}}, "required": ["food"]}
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
I want to eat some apple pie.
</text>
#### Assistant Output
{"result": "apple pie"}

Begin extracting parameters.
### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
%s
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
%s
</text>
### Assistant Output
`

const QuestionClassifierUserInputTemplate = `{"input_text": "%s", "categories": %s}`
const QuestionClassifierPromptTemplate = `
### Job Description
You are a text classification engine. Your job is to analyze user input and assign it to one or more appropriate categories from a given list.

### Extra Info
%s 

### Classification Mode
%s

### Task
Given a user input string ("input_text") and a list of category names ("category_names"), return a JSON array containing the category or categories that best match the input.

### Input Format
Input will be provided in the following JSON format:
{
  "input_text": "User input goes here.",
  "category_names": ["Category A", "Category B", "Category C"]
}

### Output Format
Return ONLY a JSON array of category names, for example:
- If only one category is appropriate: ["Category A"]
- If multiple categories are appropriate: ["Category A", "Category B"]

### Constraints
- Only choose from the provided "category_names" list.
- Do not invent or modify category names.
- Do not return anything other than the JSON array.
- Do not include explanations, comments, or text outside the JSON.
- The array can contain one or more category names, as appropriate.

### Examples
<example>
User:{"input_text": "The delivery was extremely slow and my package arrived late.", "category_names": ["Customer Service", "Delivery", "Product Quality"]}
Assistant:["Delivery"]

User:{"input_text": "The price is too high and the product did not meet my expectations.", "category_names": ["Price", "Product Quality", "Support"]}
Assistant:["Price", "Product Quality"]

</example>

### User Input
%s

### Assistant Output
`

// GenerateQuestionClassifierPrompt 构建分类任务的prompt
func GenerateQuestionClassifierPrompt(query string, categoryNames []string, enableMultiMode bool, extraInfo string) string {
	var classifyMode string
	if enableMultiMode {
		classifyMode = "You may assign the input to one or more most appropriate categories."
	} else {
		classifyMode = "You must assign the input to only one most appropriate category."
	}
	userInput := fmt.Sprintf(QuestionClassifierUserInputTemplate, query, stdsrv.AnyToString(categoryNames))
	return fmt.Sprintf(QuestionClassifierPromptTemplate, extraInfo, classifyMode, userInput)
}

var (
	splitWords = []string{StopWordObservation, FinalAnswer, ThinkStart, ThinkEnd, Thought, Action, ActionInput, TempReplaceString}
)

type ReActAction struct {
	Thought     string
	Action      string
	ActionInput string
	Observation string
}

// ExtractReActText 查找text中,最后出现的有效ReAct文本块
func ExtractReActText(text string) (*ReActAction, error) {
	thoughtIdx, actIdx, actInputIdx := lastReActIndex(text)
	if actIdx == -1 || actInputIdx == -1 {
		return nil, stderr.Errorf("can not find react block ")
	}

	ret := &ReActAction{}
	// 提取Thought对应的思考描述
	if thoughtIdx != -1 {
		ret.Thought = extractStr(text[thoughtIdx+len(Thought):])
	}

	// 提取Action对应的插件名称
	if actIdx != -1 {
		ret.Action = extractStr(text[actIdx+len(Action):])
	}

	// 提取ActionInput对应json格式参数
	if actInputIdx != -1 {
		ret.ActionInput = extractActionInput(text[actInputIdx+len(ActionInput):])
	}

	if !helper.IsEmpty(ret.Action) && helper.IsStructJsonStr(ret.ActionInput) {
		return ret, nil
	}

	newText := text[:actInputIdx]
	return ExtractReActText(newText)
}

// 查询react文本块中各动作的位置
func lastReActIndex(text string) (thoughtIdx, actIdx, actInputIdx int) {
	actInputIdx = strings.LastIndex(text, ActionInput)
	if actInputIdx == -1 {
		return -1, -1, -1
	}
	actIdx = strings.LastIndex(text[:actInputIdx], Action)
	if actIdx == -1 {
		return -1, -1, -1
	}
	thoughtIdx = strings.LastIndex(text[:actIdx], Thought)
	return thoughtIdx, actIdx, actInputIdx
}

func extractStr(text string) string {
	minIndex := len(text)
	for _, word := range splitWords {
		if idx := strings.Index(text, word); idx != -1 {
			if idx < minIndex {
				minIndex = idx
			}
		}
	}
	return strings.TrimSpace(text[:minIndex])
}
func extractActionInput(text string) string {
	// 1、满足json格式的action input参数
	str := extractStr(text)
	if helper.IsStructJsonStr(str) {
		return str
	}
	// 2、手动提取其中的json字符串
	startTagIndex := -1
	startTagCount := 0
	for i := 0; i < len(text); i++ {
		if text[i] == JsonStart && startTagIndex == -1 {
			startTagIndex = i
			startTagCount++
			continue
		}
		if text[i] == JsonStart && startTagIndex != -1 {
			startTagCount++
		} else if text[i] == JsonEnd && startTagIndex != -1 {
			startTagCount--
			if startTagCount == 0 {
				return strings.TrimSpace(text[startTagIndex : i+1])
			}
		}
	}
	return ""
}

// extractJsonStr 尝试从字符串中提取第一个完整的JSON字符串
func extractJsonStr(text string) (string, error) {
	start := -1
	count := 0
	for i := 0; i < len(text); i++ {
		if text[i] == JsonStart && start == -1 {
			start = i
			count++
			continue
		}
		if text[i] == JsonStart && start != -1 {
			count++
		} else if text[i] == JsonEnd && start != -1 {
			count--
			if count == 0 {
				return strings.TrimSpace(text[start : i+1]), nil
			}
		}
	}
	return "", stderr.Errorf("incomplete or no JSON found")
}
