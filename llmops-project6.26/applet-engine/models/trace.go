package models

import (
	"fmt"
	"strings"
	"time"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"

	"github.com/sirupsen/logrus"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type Trace map[string]*TraceItem

func (t Trace) Copy() Trace {
	ct := make(Trace, len(t))
	for k, v := range t {
		ct[k] = v
	}
	return ct
}

type NodeMeta struct {
	Id           string `json:"id"`           // 全局唯一id
	NodeID       string `json:"node_id"`      // 与画布中的普通算子的node_id对应，不同子链中的node_id可能重复
	SubChainID   string `json:"sub_chain_id"` // 与画布中的应用链调用算子的node_id对应
	NodeName     string `json:"node_name"`
	ChainName    string `json:"chain_name"`
	SubChainName string `json:"sub_chain_name"`
	InnerNode    bool   `json:"inner_node"`
	WidgetKey    string `json:"widget_key"` //算子类别id
}

func (n *NodeMeta) Validate() error {
	if n.Id == "" {
		return stderr.InvalidParam.Error("id is unspecified")
	}
	if n.NodeID == "" {
		return stderr.InvalidParam.Error("node id is unspecified")
	}
	if n.NodeName == "" {
		return stderr.InvalidParam.Error("node name is unspecified")
	}
	if n.ChainName == "" {
		return stderr.InvalidParam.Error("chain name is unspecified")
	}
	return nil
}

type TraceItem struct {
	Index   int                   `json:"index"`
	Time    time.Time             `json:"time"`
	Meta    NodeMeta              `json:"meta"`
	Cost    time.Duration         `json:"cost"`
	Parents map[string]*TraceItem `json:"parents"`
	Outputs []any                 `json:"outputs"` // outputs为啥需要多个? outputs是parents的outputs吗？还是一个节点的数据流经多个方向？
}

type Context struct {
	TaskID      string      `json:"task_id"`
	Call        *debug.Call `json:"call"`
	Meta        NodeMeta    `json:"meta"`
	FileSfsUrl  string      `json:"sfs_file_url"`
	FileHttpUrl string      `json:"file_http_url"`
	*logrus.Entry
}

func (c Context) pubLog(level string, format string, args ...any) {
	if c.Call == nil {
		c.Entry.Error("get nil call when publish log info")
		return
	}
	call := c.Call
	if !call.IsDebug() {
		return
	}
	reqID := call.ReqID()
	meta := c.Meta
	nodeName := meta.NodeName
	subChainName := meta.SubChainName
	if format == "" {
		format = c.getFormatString(args...)
	}
	logString := fmt.Sprintf(format, args...)
	logStringWithTime := fmt.Sprintf("time=%s level=%s subchain=%s node=%s log=\"%s\"\n", time.Now().Format(time.RFC3339), level, subChainName, nodeName, logString)
	// 发送算子日志信息，内部算子不用发送日志信息
	if !meta.InnerNode && meta.Id != "" {
		nodeMessage, err := debug.GetDebugMessage(reqID, meta.Id, call.GetLoopRound(meta.Id)-1)
		if err != nil {
			c.Entry.Errorf("get debug message of node %s : %s", meta.NodeName, err.Error())
			return
		}
		if err := nodeMessage.SetLog(logStringWithTime); err != nil {
			c.Entry.Errorf("set log of node %s : %s", nodeName, err.Error())
			return
		}
	}
	// 发送子链日志信息
	if meta.SubChainID != "" {
		subChainMessage, err := debug.GetDebugMessage(reqID, meta.SubChainID, call.GetLoopRound(meta.Id)-1)
		if err != nil {
			c.Entry.Errorf("get debug message of sub chain %s : %s", subChainName, err.Error())
			return
		}
		if err := subChainMessage.SetLog(logStringWithTime); err != nil {
			c.Entry.Errorf("set log of sub chain %s : %s", subChainName, err.Error())
			return
		}
	}
}

func (c Context) getFormatString(args ...any) string {
	var formatBuilder strings.Builder
	for i := 0; i < len(args); i++ {
		formatBuilder.WriteString("%v ")
	}
	return strings.TrimSpace(formatBuilder.String())
}

func (c Context) Errorf(format string, args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Errorf(format, args...)
	c.pubLog("error", format, args...)
}

func (c Context) Error(args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Error(args...)
	c.pubLog("error", "", args...)
}

func (c Context) Warnf(format string, args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Warnf(format, args...)
	c.pubLog("warn", format, args...)
}

func (c Context) Warn(args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Warn(args...)
	c.pubLog("warn", "", args...)
}

// Infof  本地打印日志的同时,补充对应log事件相应字段
func (c Context) Infof(format string, args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Infof(format, args...)
	c.pubLog("info", format, args...)
}

func (c Context) Info(args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Info(args...)
	c.pubLog("info", "", args...)
}

func (c Context) Debugf(format string, args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Debugf(format, args...)
	c.pubLog("debug", format, args...)
}

func (c Context) Debug(args ...any) {
	if c.Entry == nil {
		c.Entry = logrus.NewEntry(logrus.New())
	}
	c.Entry.Debug(args...)
	c.pubLog("debug", "", args...)
}

type RawInput map[string]any

type DataType string

// 还需要更具体的类型
const (
	String  DataType = "string"
	Number  DataType = "number"
	Boolean DataType = "boolean"
	Object  DataType = "object"
	List    DataType = "list"
	Null    DataType = "null"
	Unknown DataType = "unknown"
)

// type Fields struct {
// 	RawInput any                   `json:"raw_input"`
// 	Output   any                   `json:"output"`
// 	Type     DataType              `json:"type"`
// 	Trace    map[string]*TraceItem `json:"trace"`
// 	Meta     NodeMeta              `json:"meta"`
// 	Context  Context               `json:"context"`
// }
