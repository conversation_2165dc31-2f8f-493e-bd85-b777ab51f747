import re
import sys
import io
import traceback

def execute_python_code(code: str) -> dict:
    """ 
    执行给定的Python代码字符串，并捕获其标准输出和错误输出。
     - 参数:
    code (str): 要执行的Python代码字符串。

     - 返回:
    dict: 包含执行结果信息的字典，包括标准输出("stdout")、错误输出("stderr")和错误消息("message")。
    """
    # 创建字符串 IO 对象来捕获标准输出和错误输出
    stdout_capture = io.StringIO()
    # 保存原始的标准输出和错误输出
    original_stdout = sys.stdout
    result = {
        "stdout": "",
        "stderr": "",
        "message": ""
    }
    # 使用当前的全局命名空间
    global_namespace = globals().copy()
    # 创建一个新的局部命名空间
    local_namespace = {}
    try:
        # 重定向标准输出
        sys.stdout = stdout_capture
        # 在隔离的局部环境中执行代码，但允许访问全局命名空间
        exec(code, global_namespace, local_namespace)
        # 获取捕获的输出
        result["stdout"] = stdout_capture.getvalue()
    except Exception as e:
        # 捕获异常并获取错误信息
        all_traceback = traceback.format_exc()
        # 使用正则表达式匹配 "exec(code, global_namespace, local_namespace)" 后的所有内容
        pattern = r'exec\(code, global_namespace, local_namespace\)\n([\s\S]*)'
        match = re.search(pattern, all_traceback)
        if match:
            filtered_traceback = match.group(1)
            result["stderr"] = filtered_traceback
        result["message"] = str(e)

    finally:
        # 恢复原始的标准输出和错误输出
        sys.stdout = original_stdout
        # 关闭 StringIO 对象
        stdout_capture.close()

    return result
