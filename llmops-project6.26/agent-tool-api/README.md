
# 背景

使用 flask+gunicorn+docker 搭建一个http服务，用于提供agent 插件工具服务，快速提供工具让用户体验<br />项目地址[https://gitlab.transwarp.io/fangyuan.han/agent-tool-api](https://gitlab.transwarp.io/fangyuan.han/agent-tool-api)

# 已实现的工具api

- bing_search 必应搜索
- jina_search jina搜索，返回llm友好的搜索结果，后续可以考虑私有部署，作为LLMOps平台的内置服务 [https://github.com/jina-ai/reader](https://github.com/jina-ai/reader)
- send_emails  发送邮件
- parse_url  使用jina解析网页，返回llm友好的结果，后续可以考虑私有部署 [https://github.com/jina-ai/reader](https://github.com/jina-ai/reader)
- wecom_group_bot 微信群机器人
- firecrawler 爬取某个网站的多个网页内容，返回llm友好的结果，也可以作为知识库导入网站的工具 [https://github.com/mendableai/firecrawl](https://github.com/mendableai/firecrawl)
- judge0ce 代码执行器，支持python java c c++ go javascript
- eval_expression 计算器
- gaode_weather 高德天气
- arxiv_search arxiv搜索，在arxiv网站上搜索论文
- pubmed_search pubmed搜索，在pubmed网站上搜索生命科学论文
- generate_ppt 生成ppt
- github_repositories 搜索github网站上的仓库
- chart 图表工具，生成折线图、柱状图、饼图
