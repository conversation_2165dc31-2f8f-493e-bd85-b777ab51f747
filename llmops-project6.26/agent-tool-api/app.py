# app.py
import logging

from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
import os

from config import conf
from sodeck.sodeck import generate_ppt
from utils.response import make_success_response, make_error_response, make_pretty_response, make_citations_response
from parse_url import parse_url
from search_engine import bing_search, jina_search, tushare_search, searxng_search
from dify_tools import dify_send_email
from dify_tools import send_email_by_proxy
from dify_tools import send_email_by_dify
from wecom import send_wecom_group_message
from code_interpreter.python_interpreter import execute_python_code
from read_file.read_file import read_file
from dify_tools import EvaluateExpressionTool, arxiv_search_tool, fire_crawl_tool, pubmed_search_tool, judgece_execute_code_tool
from dify_tools import gaode_weather_tool, github_repositories_tool, line_chart_tool, bar_chart_tool, pie_chart_tool
from openapi_schema.openapi import load_openapi_schemas
from dify_tools import send_dingtalk_group_message
from dify_plugin_api import init_dify_plugin_api

app = Flask(__name__) # 创建flask应用
CORS(app) # 支持跨域访问

logging.info("*******************config info:*******************")
logging.info(conf())

# 初始化Dify插件API
dify_plugin_api = init_dify_plugin_api(app)
logging.info("Dify插件API初始化完成")

@app.route('/v1/parse_url', methods=['POST'])
def parse_url_handler():
    payload = request.json
    url = payload.get("url")
    if not url:
        return make_error_response("url is invalid")
    
    print("1")

    url_content, err = parse_url(url)
    if err:
        return make_error_response(err)
    return make_success_response({"url_content": url_content})

@app.route('/v1/bing_search', methods=['POST'])
def bing_search_handler():
    payload = request.json
    # 从header中读取api-key
    api_key = request.headers.get("Api-Key")
    query_params = request.args
    query = payload.get("query")
    if not query:
        return make_error_response("query is invalid")
    parse = payload.get("parse") if payload.get("parse") else query_params.get("parse") == 'true'
    search_result, err = bing_search(query, parse, api_key)
    if err:
        return make_error_response(err)
    return make_citations_response(search_result)

@app.route('/v1/searxng_search', methods=['POST'])
def searxng_search_handler():
    payload = request.json
    query_params = request.args
    query = payload.get("query")
    if not query:
        return make_error_response("query is invalid")
    # parse 默认为True
    parse = True
    if not payload.get("parse", True):
        parse = False
    if query_params.get("parse") == 'false':
        parse = False
    search_result, err = searxng_search(query, parse)
    if err:
        return make_error_response(err)
    return make_citations_response(search_result)


@app.route('/v1/finance_search', methods=['POST'])
def finance_search_handler():
    payload = request.json
    query_params = request.args
    query = payload.get("query")
    if not query:
        return make_error_response("query is invalid")
    # 只搜索财经信息
    query = f"财经信息 {query}"
    # parse 默认为True
    parse = True
    if not payload.get("parse", True):
        parse = False
    if query_params.get("parse") == 'false':
        parse = False
    search_result, err = bing_search(query, parse)
    if err:
        return make_error_response(err)
    return make_success_response(search_result)

@app.route('/v1/tushare_search', methods=['POST'])
def tushare_search_handler():
    payload = request.json
    query = payload.get("query")
    if not query:
        return make_error_response("query is invalid")
    content, err = tushare_search(query)
    if err:
        return make_error_response(err)
    return make_success_response(content)

# test dify bing search
# @app.route('/v1/dify_bing_search', methods=['POST'])
# def dify_bing_search_handler():
#     payload = request.json
#     query = payload.get("query")
#     if not query:
#         return jsonify({"error": "query is invalid"})
#     from dify_tools import BingSearchTool
#     search_result = BingSearchTool()._invoke(tool_parameters=payload)
#     return jsonify({"search_result": search_result})

# sead_emails
@app.route('/v1/send_emails', methods=['POST'])
def send_emails_handler():
    payload = request.json
    send_to = payload.get("send_to")
    subject = payload.get("subject")
    content = payload.get("content")
    if not send_to:
        return make_error_response("收件人邮箱账号不能为空")
    if not subject:
        return make_error_response("邮件主题不能为空")
    if not content:
        return make_error_response("邮件内容不能为空")
    result, err = send_email_by_dify(send_to, subject, content)
    
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})


@app.route('/v1/jina_search', methods=['POST'])
def jina_search_handler():
    payload = request.json
    query = payload.get("query")
    if not query:
        return make_error_response("query is invalid")
    search_result, err = jina_search(query)
    if err:
        return make_error_response(err)
    return make_success_response({"search_result": search_result})

@app.route("/v1/wecom_group_msg", methods=["POST"])
def wecom_group_msg_handler():
    payload = request.json
    # 从header中读取api-key
    api_key = request.headers.get("Api-Key")
    message = payload.get("message")
    if not message:
        return make_error_response("message is required")
    success_message, error_info = send_wecom_group_message(message, api_key)
    if error_info:
        return make_error_response(error_info)
    return make_success_response({"send_result": success_message})

@app.route('/v1/dingtalk_send', methods=['POST'])
def dingtalk_group_bot_handler():
    payload = request.json
    api_key = request.headers.get("Api-Key")
    message = payload.get("message")
    if not message:
        return make_error_response("message is required")
    success_message, error_info = send_dingtalk_group_message(message, api_key)
    if error_info:
        return make_error_response(error_info)
    return make_success_response({"send_result": success_message})

@app.route("/v1/read_file", methods=["POST"])
def read_file_handler():
    data = request.json
    url = data.get('url')
    name = data.get('name')
    
    if not url or not name:
        return make_error_response("Missing url or name parameter")
        
    file_content, err = read_file(url, name)
    if err:
        return make_error_response(err)
    return make_success_response({"file_content": file_content})

# generate_ppt
@app.route('/v1/generate_ppt', methods=['POST'])
def generate_ppt_handler():
    payload = request.json
    title = payload.get("title")
    content = payload.get("content")
    if not title:
        return make_error_response("title is invalid")
    if not content:
        return make_error_response("content is invalid")
    file, err = generate_ppt(title, content)
    if err:
        return make_error_response(err)
    return make_success_response({"file": file})

@app.route('/v1/eval_expression', methods=['POST'])
def eval_expression_handler():
    payload = request.json
    result, err = EvaluateExpressionTool()._invoke(tool_parameters=payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/arxiv_search', methods=['POST'])
def arxiv_search_handler():
    payload = request.json
    result, err = arxiv_search_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/fire_crawl', methods=['POST'])
def fire_crawl_handler():
    payload = request.json
    # 从header中读取api-key
    api_key = request.headers.get("Api-Key")
    if api_key:
        payload = dict(payload)  # 确保payload可写
        payload['api_key'] = api_key
    result, err = fire_crawl_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/pubmed_search', methods=['POST'])
def pubmed_search_handler():
    payload = request.json
    result, err = pubmed_search_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/judgece_execute_code', methods=['POST'])
def judgece_execute_code_handler():
    payload = request.json
    result, err = judgece_execute_code_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/gaode_weather', methods=['POST'])
def gaode_weather_handler():
    payload = request.json
    # 从header中读取api-key
    api_key = request.headers.get("Api-Key")
    if api_key:
        payload = dict(payload)  # 确保payload可写
        payload['api_key'] = api_key
    result, err = gaode_weather_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/github_repositories', methods=['POST'])
def github_repositories_handler():
    payload = request.json
    # 从header中读取api-key
    api_key = request.headers.get("Api-Key")
    if api_key:
        payload = dict(payload)  # 确保payload可写
        payload['api_key'] = api_key
    result, err = github_repositories_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/chart/line', methods=['POST'])
def line_chart_handler():
    payload = request.json
    result, err = line_chart_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/chart/bar', methods=['POST'])
def bar_chart_handler():
    payload = request.json
    result, err = bar_chart_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/chart/pie', methods=['POST'])
def pie_chart_handler():
    payload = request.json
    result, err = pie_chart_tool._invoke(payload)
    if err:
        return make_error_response(err)
    return make_success_response({"result": result})

@app.route('/v1/openapi_schemas', methods=['GET'])
def get_openapi_schemas():
    lang = request.args.get('lang')
    try:
        schemas = load_openapi_schemas(lang)
        logging.info(f'Loaded schemas for language: {lang if lang else "all"}')
        return make_pretty_response(schemas)
    except ValueError as e:
        return make_error_response(str(e))


@app.route('/v1/code_interpreter/python', methods=['POST'])
def python_interpreter_handler():
    payload = request.json
    code = payload.get("code")
    if not code:
        return make_error_response("code is required")
    result = execute_python_code(code)
    return make_success_response(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=False)
