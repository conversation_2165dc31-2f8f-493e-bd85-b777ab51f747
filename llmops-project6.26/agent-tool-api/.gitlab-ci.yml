image: harbor.transwarp.io/aip/base/go1.20.1-builder:sophon-0.0 # 复用go-builder镜像构建python项目

stages:
  - build
  - deploy

include:
  - project: applied-ai/aiot/common-ci
    file: deploy.yml

before_script:
  - set -e
  - source /etc/profile
  - export GITLAB_URL="http://gitlab-ci-token:${CI_JOB_TOKEN}@***********:10080"
  - echo ${CI_COMMIT_REF_NAME}

  # docker alias
  - shopt -s expand_aliases
  - echo "************ harbor.transwarp.io" >> /etc/hosts
  - if [ -f /etc/docker/daemon.json ]; then sed -i 's/***********/harbor.transwarp.io/g' /etc/docker/daemon.json; fi;
  - if [ -f /usr/bin/startdocker.sh ]; then sed -i 's/***********/harbor.transwarp.io/g' /usr/bin/startdocker.sh; fi;
  - if [ -f /root/.docker/config.json ]; then sed -i 's/***********/harbor.transwarp.io/g' /root/.docker/config.json; fi;
  - if [ -f ~/.docker/config.json ]; then sed -i 's/***********/harbor.transwarp.io/g' ~/.docker/config.json; fi
  - if [ -f /buildkitd.toml ]; then sed -i 's/***********/harbor.transwarp.io/g' /buildkitd.toml; fi
  - if [ -f ./Dockerfile ]; then sed -i 's/***********/harbor.transwarp.io/g' ./Dockerfile; fi
  - alias "killdocker=ps aux | grep docker | grep -v grep | awk '{print \$2}' | xargs kill -9 || true"
  - alias "startdocker=(startdocker.sh &) ; sleep 10"

  # docker image
  - export DOCKER_REPO_URL=harbor.transwarp.io
  - export BUILD_IMAGE_PREFIX=${DOCKER_REPO_URL}/aip
  - export BUILD_AMD_IMAGE_PREFIX=${DOCKER_REPO_URL}/aip-amd
  - export BUILD_ARM_IMAGE_PREFIX=${DOCKER_REPO_URL}/aip-arm
  - export IMAGE_TAG=${CI_COMMIT_REF_NAME}

  # external cmd from envs
  - echo "$BEFORE_SCRIPT_COMMAND"
  - eval $BEFORE_SCRIPT_COMMAND

image_build_x86:
  stage: build
  only:
    - master
    - /^llm-\d+(\.\d+){1,2}(-.*)?$/
    - /^branch-\d+(\.\d+){1,2}(-.*)?$/
    - /^llm-.*$/
    - dev
  script:
    - startdocker
    - export COMPONENT=agent-tool-api
    - docker build -t ${BUILD_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG} -f Dockerfile .
    - docker tag ${BUILD_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG} ${BUILD_AMD_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG}
    - export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}"
    - docker push ${BUILD_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG}
    - docker push ${BUILD_AMD_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG}
    - killdocker
  tags:
    - k8s

image_build_arm:
  stage: build
  only:
    - master
    - /^llm-\d+(\.\d+){1,2}(-.*)?$/
    - /^branch-\d+(\.\d+){1,2}(-.*)?$/
    - /^llm-.*$/
    - dev
  script:
    - startdocker
    - export COMPONENT=agent-tool-api
    - docker build -t ${BUILD_ARM_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG} -f Dockerfile.arm_64 .
    - export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}"
    - docker push ${BUILD_ARM_IMAGE_PREFIX}/${COMPONENT}:${IMAGE_TAG}
    - killdocker
  tags:
    - k8s