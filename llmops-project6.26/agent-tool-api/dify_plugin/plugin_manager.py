"""
Dify插件管理器
整合插件安装、授权、工具适配等功能
"""

import os
import json
import logging
import requests
from typing import Dict, List, Any

from .models import DifyApiConfig
from .plugin_installer import DifyPluginInstaller


logger = logging.getLogger(__name__)


class DifyPluginManager:
    """Dify插件管理器主类"""
    
    def __init__(self, dify_config: DifyApiConfig, storage_dir: str = "/tmp/dify_plugins"):
        self.dify_config = dify_config
        self.storage_dir = storage_dir
        
        # 初始化插件安装器
        self.installer = DifyPluginInstaller(dify_config, storage_dir)
    
    def _get_headers(self) -> Dict[str, str]:
        """获取Dify API请求头"""
        return {
            'X-Api-Key': self.dify_config.api_key,
            'Authorization': self.dify_config.auth_token,
            'Content-Type': 'application/json'
        }
    

    
    def upload_and_install_plugin(self, difypkg_file_path: str) -> Dict[str, Any]:
        """上传并安装插件"""
        try:
            result = self.installer.upload_and_install(difypkg_file_path)

            # 将PluginInfo转换为可序列化的字典
            plugin_info_dict = None
            if result.plugin_info:
                plugin_info_dict = {
                    'plugin_id': result.plugin_info.plugin_id,
                    'name': result.plugin_info.name,
                    'version': result.plugin_info.version,
                    'author': result.plugin_info.author,
                    'description': result.plugin_info.description,
                    'unique_identifier': result.plugin_info.unique_identifier,
                    'status': result.plugin_info.status.value,  # 转换枚举为字符串
                    'auth_status': result.plugin_info.auth_status.value,  # 转换枚举为字符串
                    'install_time': result.plugin_info.install_time.isoformat() if result.plugin_info.install_time else None,
                    'permissions': result.plugin_info.permissions,
                    'metadata': result.plugin_info.metadata
                }

            return {
                'success': result.success,
                'message': result.message,
                'plugin_info': plugin_info_dict
            }

        except Exception as e:
            logger.error(f"安装插件失败: {e}")
            return {
                'success': False,
                'message': f"安装插件时发生错误: {str(e)}",
                'plugin_info': None
            }
    

    


    


    def get_all_installed_plugins_with_auth_status(self) -> Dict[str, Any]:
        """获取所有已安装插件信息及授权状态"""
        try:
            logger.info("开始获取所有已安装插件信息及授权状态")

            # 第一步：获取插件列表，提取provider_name和plugin_unique_identifier
            plugin_info_list = self._get_plugin_list_with_provider_info()
            if not plugin_info_list:
                logger.warning("未能提取到任何插件的provider信息，可能是数据结构问题")
                return {
                    'success': False,
                    'message': '未能提取到插件的provider信息，请检查插件数据结构',
                    'data': []
                }

            logger.info(f"获取到 {len(plugin_info_list)} 个插件的基本信息")

            # 第二步：并行获取授权状态和授权字段信息
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import time

            start_time = time.time()
            provider_names = [plugin_info['provider_name'] for plugin_info in plugin_info_list]

            with ThreadPoolExecutor(max_workers=2) as executor:
                # 并行执行两个主要任务
                auth_status_future = executor.submit(self._get_plugins_auth_status)
                auth_fields_future = executor.submit(self._get_all_auth_fields_parallel, provider_names)

                # 等待结果
                auth_status_map = auth_status_future.result()
                auth_fields_map = auth_fields_future.result()

            parallel_time = time.time() - start_time
            logger.info(f"并行获取完成，耗时 {parallel_time:.2f}秒，授权状态: {len(auth_status_map)}，授权字段: {len(auth_fields_map)}")

            # 第三步：快速合并数据
            result_plugins = []
            for plugin_info in plugin_info_list:
                provider_name = plugin_info['provider_name']
                plugin_unique_identifier = plugin_info['plugin_unique_identifier']

                # 查找授权状态和字段
                auth_info = auth_status_map.get(provider_name, {})
                auth_fields = auth_fields_map.get(provider_name, [])

                # 确定授权状态
                if not auth_fields:
                    auth_status = "no_auth_required"
                elif auth_info and auth_info.get('is_team_authorization', False):
                    auth_status = "authorized"
                else:
                    auth_status = "unauthorized"

                result_plugin = {
                    'provider_name': provider_name,
                    'plugin_unique_identifier': plugin_unique_identifier,
                    'plugin_name': plugin_info.get('plugin_name', provider_name),
                    'plugin_description': plugin_info.get('plugin_description', ''),
                    'plugin_version': plugin_info.get('plugin_version', ''),
                    'auth_status': auth_status
                }

                # 只有需要授权的插件才返回auth_fields
                if auth_status != "no_auth_required":
                    result_plugin['auth_fields'] = auth_fields

                result_plugins.append(result_plugin)

            logger.info(f"成功处理 {len(result_plugins)} 个插件的信息")

            return {
                'success': True,
                'message': f'成功获取 {len(result_plugins)} 个插件信息',
                'data': result_plugins
            }

        except Exception as e:
            logger.error(f"获取插件信息及授权状态失败: {e}")
            return {
                'success': False,
                'message': f"获取插件信息失败: {str(e)}",
                'data': []
            }

    def _get_all_auth_fields_parallel(self, provider_names: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """并行获取所有插件的授权字段信息"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        auth_fields_map = {}
        logger.info(f"开始并行获取 {len(provider_names)} 个插件的授权字段信息")

        with ThreadPoolExecutor(max_workers=min(len(provider_names), 10)) as executor:
            # 提交所有任务
            future_to_provider = {
                executor.submit(self._get_plugin_auth_fields, provider_name): provider_name
                for provider_name in provider_names
            }

            # 收集结果
            for future in as_completed(future_to_provider):
                provider_name = future_to_provider[future]
                try:
                    auth_fields = future.result()
                    auth_fields_map[provider_name] = auth_fields
                    logger.debug(f"✓ {provider_name}: {len(auth_fields)} 个授权字段")
                except Exception as e:
                    logger.warning(f"✗ {provider_name}: {e}")
                    auth_fields_map[provider_name] = []

        return auth_fields_map

    def _get_plugin_list_with_provider_info(self) -> List[Dict[str, Any]]:
        """第一步：获取插件列表并提取provider信息"""
        try:
            url = f"{self.dify_config.base_url}/console/api/workspaces/current/plugin/list"
            headers = self._get_headers()

            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求头: {headers}")

            # 禁用代理并设置较短的超时时间
            proxies = {
                'http': None,
                'https': None
            }
            response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
            logger.info(f"插件列表API响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"获取插件列表失败: HTTP {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return []

            data = response.json()
            plugins = data.get('plugins', [])
            logger.info(f"从Dify获取到 {len(plugins)} 个插件")

            # 如果没有插件，尝试从工具提供者API获取
            if not plugins:
                logger.warning("插件列表为空，尝试从工具提供者API获取")
                return self._get_plugins_from_tool_providers()

            plugin_info_list = []

            for plugin in plugins:
                try:
                    plugin_unique_identifier = plugin.get('plugin_unique_identifier', '')
                    declaration = plugin.get('declaration', {})

                    # 确保declaration不是None
                    if declaration is None:
                        declaration = {}

                    # 提取插件基本信息
                    plugin_name = plugin.get('name', '')
                    plugin_description = declaration.get('description', {})
                    plugin_author = declaration.get('author', '')
                    plugin_version = plugin.get('version', '')

                    # 处理多语言描述
                    if isinstance(plugin_description, dict):
                        plugin_description = plugin_description.get('zh_Hans', plugin_description.get('en_US', ''))
                    elif plugin_description is None:
                        plugin_description = ''

                    logger.debug(f"处理插件: {plugin_name} ({plugin_unique_identifier})")

                    # 提取provider信息
                    provider_name = None

                    # 方法1: 从declaration.plugins.tools中提取（这是实际的数据结构）
                    plugins_info = declaration.get('plugins', {})
                    if plugins_info and plugins_info is not None:
                        tools_list = plugins_info.get('tools', [])
                        logger.debug(f"Found tools list: {tools_list}")

                        # 确保tools_list不是None且是可迭代的
                        if tools_list is not None and hasattr(tools_list, '__iter__'):
                            for tool_path in tools_list:
                                # 格式是 "provider/dingtalk.yaml"
                                if isinstance(tool_path, str) and '/' in tool_path:
                                    parts = tool_path.split('/')
                                    if len(parts) >= 2:
                                        provider_name = parts[1]  # 获取文件名部分
                                        if provider_name.endswith('.yaml'):
                                            provider_name = provider_name[:-5]  # 去掉.yaml后缀
                                        logger.debug(f"从tools路径提取provider_name: {provider_name}")
                                        break

                    # 方法2: 从declaration.tool.identity.name中提取
                    if not provider_name:
                        tool_info = declaration.get('tool', {})
                        if tool_info:
                            tool_identity = tool_info.get('identity', {})
                            if tool_identity:
                                provider_name = tool_identity.get('name', '')
                                logger.debug(f"从tool identity提取provider_name: {provider_name}")

                    # 方法3: 从plugin_id中提取
                    if not provider_name:
                        plugin_id = plugin.get('plugin_id', '')
                        if plugin_id and '/' in plugin_id:
                            provider_name = plugin_id.split('/')[-1]  # langgenius/dingtalk -> dingtalk
                            logger.debug(f"从plugin_id提取provider_name: {provider_name}")

                    # 方法4: 使用插件名称作为fallback
                    if not provider_name:
                        provider_name = plugin_name
                        logger.debug(f"使用插件名称作为provider_name: {provider_name}")

                    if provider_name:
                        # 获取插件显示名称
                        display_name = plugin_name
                        tool_info = declaration.get('tool', {})
                        if tool_info:
                            tool_identity = tool_info.get('identity', {})
                            if tool_identity:
                                label = tool_identity.get('label', {})
                                if isinstance(label, dict):
                                    display_name = label.get('zh_Hans', label.get('en_US', plugin_name))
                                elif label:
                                    display_name = str(label)

                        plugin_info = {
                            'provider_name': provider_name,
                            'plugin_unique_identifier': plugin_unique_identifier,
                            'plugin_name': display_name,
                            'plugin_description': plugin_description,
                            'plugin_author': plugin_author,
                            'plugin_version': plugin_version,
                            'original_provider': provider_name
                        }

                        plugin_info_list.append(plugin_info)
                        logger.debug(f"成功提取插件信息: {provider_name} -> {plugin_unique_identifier}")
                    else:
                        logger.warning(f"无法提取插件 {plugin_name} 的provider信息")

                except Exception as e:
                    logger.warning(f"处理插件信息时出错: {e}")
                    logger.debug(f"插件数据: {json.dumps(plugin, indent=2, ensure_ascii=False)}")
                    continue

            logger.info(f"成功提取 {len(plugin_info_list)} 个插件的provider信息")
            return plugin_info_list

        except Exception as e:
            logger.error(f"获取插件列表失败: {e}")
            return []

    def _get_plugins_auth_status(self) -> Dict[str, Dict[str, Any]]:
        """第二步：获取插件授权状态"""
        try:
            url = f"{self.dify_config.base_url}/console/api/workspaces/current/tool-providers"
            headers = self._get_headers()

            response = requests.get(url, headers=headers, timeout=30)
            logger.debug(f"工具提供者API响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"获取工具提供者列表失败: HTTP {response.status_code}")
                return {}

            providers = response.json()
            logger.info(f"从Dify获取到 {len(providers)} 个工具提供者")

            auth_status_map = {}

            for provider in providers:
                try:
                    provider_name = provider.get('name', '')
                    is_team_authorization = provider.get('is_team_authorization', False)
                    team_credentials = provider.get('team_credentials', {})
                    provider_type = provider.get('type', 'unknown')

                    # 处理复合名称（如 langgenius/wecom -> wecom）
                    if '/' in provider_name:
                        simple_name = provider_name.split('/')[-1]
                    else:
                        simple_name = provider_name

                    auth_info = {
                        'is_team_authorization': is_team_authorization,
                        'team_credentials': team_credentials,
                        'type': provider_type,
                        'full_name': provider_name
                    }

                    # 同时用完整名称和简化名称作为key
                    auth_status_map[provider_name] = auth_info
                    if simple_name != provider_name:
                        auth_status_map[simple_name] = auth_info

                    logger.debug(f"工具提供者: {provider_name} -> 授权状态: {is_team_authorization}")

                except Exception as e:
                    logger.warning(f"处理工具提供者信息时出错: {e}")
                    continue

            logger.info(f"成功获取 {len(auth_status_map)} 个工具提供者的授权状态")
            return auth_status_map

        except Exception as e:
            logger.error(f"获取工具提供者授权状态失败: {e}")
            return {}

    def _get_plugins_from_tool_providers(self) -> List[Dict[str, Any]]:
        """Fallback方法：从工具提供者API获取插件信息"""
        try:
            logger.info("使用fallback方法从工具提供者API获取插件信息")

            url = f"{self.dify_config.base_url}/console/api/workspaces/current/tool-providers"
            headers = self._get_headers()

            response = requests.get(url, headers=headers, timeout=30)
            logger.debug(f"工具提供者API响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"获取工具提供者列表失败: HTTP {response.status_code}")
                return []

            providers = response.json()
            logger.info(f"从工具提供者API获取到 {len(providers)} 个提供者")

            plugin_info_list = []

            for provider in providers:
                try:
                    provider_name = provider.get('name', '')
                    provider_type = provider.get('type', 'unknown')

                    # 只处理插件类型的提供者
                    if provider_type in ['plugin', 'builtin']:
                        # 处理复合名称（如 langgenius/wecom -> wecom）
                        if '/' in provider_name:
                            simple_name = provider_name.split('/')[-1]
                            plugin_unique_identifier = provider_name + ":latest"  # 假设版本
                        else:
                            simple_name = provider_name
                            plugin_unique_identifier = f"builtin/{provider_name}:latest"

                        plugin_info = {
                            'provider_name': simple_name,
                            'plugin_unique_identifier': plugin_unique_identifier,
                            'plugin_name': provider.get('label', {}).get('zh_Hans', simple_name) if isinstance(provider.get('label'), dict) else simple_name,
                            'plugin_description': provider.get('description', {}).get('zh_Hans', '') if isinstance(provider.get('description'), dict) else '',
                            'plugin_author': provider.get('author', ''),
                            'plugin_version': 'latest',
                            'original_provider': provider_name
                        }

                        plugin_info_list.append(plugin_info)
                        logger.debug(f"从工具提供者提取插件信息: {simple_name} -> {plugin_unique_identifier}")

                except Exception as e:
                    logger.warning(f"处理工具提供者信息时出错: {e}")
                    continue

            logger.info(f"从工具提供者API成功提取 {len(plugin_info_list)} 个插件信息")
            return plugin_info_list

        except Exception as e:
            logger.error(f"从工具提供者API获取插件信息失败: {e}")
            return []

    def _get_plugin_auth_fields(self, provider_name: str) -> List[Dict[str, Any]]:
        """获取插件授权字段信息"""
        try:
            # 构造credentials_schema API URL
            url = f"{self.dify_config.base_url}/console/api/workspaces/current/tool-provider/builtin/{provider_name}/credentials_schema"
            headers = self._get_headers()

            response = requests.get(url, headers=headers, timeout=30)
            logger.debug(f"获取 {provider_name} 授权字段API响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.debug(f"获取 {provider_name} 授权字段失败: HTTP {response.status_code}")
                return []

            credentials_schema = response.json()
            logger.debug(f"获取到 {provider_name} 的授权字段: {len(credentials_schema)} 个")

            # 提取需要的字段信息
            auth_fields = []
            for field in credentials_schema:
                try:
                    field_name = field.get('name', '')
                    field_label = field.get('label', {})
                    field_required = field.get('required', False)

                    # 提取中文标签
                    if isinstance(field_label, dict):
                        display_label = field_label.get('zh_Hans', field_label.get('en_US', field_name))
                    else:
                        display_label = str(field_label) if field_label else field_name

                    auth_field = {
                        'name': field_name,
                        'label': display_label,
                        'required': field_required
                    }

                    auth_fields.append(auth_field)
                    logger.debug(f"提取授权字段: {field_name} -> {display_label} (必填: {field_required})")

                except Exception as e:
                    logger.warning(f"处理授权字段时出错: {e}")
                    continue

            return auth_fields

        except Exception as e:
            logger.debug(f"获取 {provider_name} 授权字段信息失败: {e}")
            return []

