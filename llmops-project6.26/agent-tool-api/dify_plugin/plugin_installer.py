"""
Dify插件安装器
负责处理.difypkg文件的上传、安装和管理
"""

import os
import json
import requests
import tempfile
import zipfile
import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime

try:
    import yaml
except ImportError:
    yaml = None

from .models import (
    PluginInfo, PluginUploadResult, PluginStatus, 
    AuthStatus, DifyApiConfig
)


logger = logging.getLogger(__name__)


class DifyPluginInstaller:
    """Dify插件安装器"""
    
    def __init__(self, dify_config: DifyApiConfig, storage_dir: str = "/tmp/dify_plugins"):
        self.dify_config = dify_config
        self.storage_dir = storage_dir
        self.plugins_db_file = os.path.join(storage_dir, "plugins.json")
        
        # 确保存储目录存在
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_headers(self) -> Dict[str, str]:
        """获取Dify API请求头"""
        return {
            'X-Api-Key': self.dify_config.api_key,
            'Authorization': self.dify_config.auth_token
        }
    
    def _load_plugins_db(self) -> Dict[str, PluginInfo]:
        """加载插件数据库"""
        if not os.path.exists(self.plugins_db_file):
            return {}
        
        try:
            with open(self.plugins_db_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            plugins = {}
            for plugin_id, plugin_data in data.items():
                # 转换状态枚举
                plugin_data['status'] = PluginStatus(plugin_data['status'])
                plugin_data['auth_status'] = AuthStatus(plugin_data['auth_status'])
                
                # 转换时间
                if plugin_data.get('install_time'):
                    plugin_data['install_time'] = datetime.fromisoformat(plugin_data['install_time'])
                
                plugins[plugin_id] = PluginInfo(**plugin_data)
            
            return plugins
        except Exception as e:
            logger.error(f"加载插件数据库失败: {e}")
            return {}
    
    def _save_plugins_db(self, plugins: Dict[str, PluginInfo]):
        """保存插件数据库"""
        try:
            data = {}
            for plugin_id, plugin_info in plugins.items():
                plugin_dict = {
                    'plugin_id': plugin_info.plugin_id,
                    'name': plugin_info.name,
                    'version': plugin_info.version,
                    'author': plugin_info.author,
                    'description': plugin_info.description,
                    'unique_identifier': plugin_info.unique_identifier,
                    'status': plugin_info.status.value,
                    'auth_status': plugin_info.auth_status.value,
                    'install_time': plugin_info.install_time.isoformat() if plugin_info.install_time else None,
                    'permissions': plugin_info.permissions,
                    'metadata': plugin_info.metadata
                }
                data[plugin_id] = plugin_dict
            
            with open(self.plugins_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存插件数据库失败: {e}")
    
    def _extract_plugin_manifest(self, difypkg_path: str) -> Optional[Dict[str, Any]]:
        """从.difypkg文件中提取插件清单"""
        try:
            with zipfile.ZipFile(difypkg_path, 'r') as zip_file:
                # 查找manifest文件
                manifest_files = []
                for file_info in zip_file.filelist:
                    if file_info.filename.endswith(('manifest.yaml', 'manifest.yml', 'manifest.json')):
                        manifest_files.append(file_info.filename)
                
                if not manifest_files:
                    logger.error("未找到插件清单文件")
                    return None
                
                # 读取第一个找到的清单文件
                manifest_file = manifest_files[0]
                manifest_content = zip_file.read(manifest_file).decode('utf-8')
                
                if manifest_file.endswith('.json'):
                    return json.loads(manifest_content)
                elif yaml and manifest_file.endswith(('.yaml', '.yml')):
                    return yaml.safe_load(manifest_content)
                else:
                    logger.error(f"不支持的清单文件格式: {manifest_file}")
                    return None
                    
        except Exception as e:
            logger.error(f"提取插件清单失败: {e}")
            return None
    
    def _upload_to_dify(self, difypkg_path: str) -> Tuple[bool, str, Optional[str]]:
        """上传.difypkg文件到Dify"""
        try:
            # 使用正确的Dify Console API端点
            url = f"{self.dify_config.base_url}/console/api/workspaces/current/plugin/upload/pkg"

            with open(difypkg_path, 'rb') as f:
                files = {
                    'pkg': ('plugin.difypkg', f, 'application/octet-stream')
                }

                response = requests.post(
                    url,
                    files=files,
                    headers=self._get_headers(),
                    timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                # 从响应中提取unique_identifier
                # 根据Dify API文档，上传成功后会返回插件信息
                unique_identifier = result.get('unique_identifier')
                if not unique_identifier:
                    # 如果没有直接返回unique_identifier，尝试从其他字段构造
                    plugin_info = result.get('plugin', {})
                    name = plugin_info.get('name', '')
                    version = plugin_info.get('version', '')
                    if name and version:
                        unique_identifier = f"{name}:{version}"

                return True, "上传成功", unique_identifier
            else:
                error_msg = f"上传失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('message', response.text)
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {response.text}"
                return False, error_msg, None

        except requests.RequestException as e:
            return False, f"网络请求失败: {str(e)}", None
        except Exception as e:
            return False, f"上传过程出错: {str(e)}", None
    
    def _install_plugin(self, unique_identifier: str) -> Tuple[bool, str]:
        """通过unique_identifier安装插件到Dify"""
        try:
            # 使用正确的Dify Console API端点
            url = f"{self.dify_config.base_url}/console/api/workspaces/current/plugin/install/pkg"

            payload = {
                "plugin_unique_identifiers": [unique_identifier]
            }

            headers = self._get_headers()
            headers['Content-Type'] = 'application/json'

            response = requests.post(url, json=payload, headers=headers, timeout=60)

            if response.status_code == 200:
                return True, "安装成功"
            elif response.status_code == 400:
                # 处理插件已存在的情况
                try:
                    error_detail = response.json().get('message', response.text)
                    if 'already installed' in error_detail.lower():
                        logger.warning(f"插件已存在: {error_detail}")
                        return True, "插件已存在，跳过安装"
                    else:
                        return False, f"安装失败: HTTP {response.status_code} - {error_detail}"
                except:
                    error_text = response.text
                    if 'already installed' in error_text.lower():
                        logger.warning(f"插件已存在: {error_text}")
                        return True, "插件已存在，跳过安装"
                    else:
                        return False, f"安装失败: HTTP {response.status_code} - {error_text}"
            else:
                error_msg = f"安装失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('message', response.text)
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {response.text}"
                return False, error_msg

        except requests.RequestException as e:
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            return False, f"安装过程出错: {str(e)}"
    
    def _check_auth_required(self, manifest: Dict[str, Any]) -> bool:
        """检查插件是否需要授权"""
        try:
            # 检查Tool Provider的credentials_schema
            tool_provider = manifest.get('tool', {})
            if tool_provider:
                credentials_schema = tool_provider.get('credentials_schema', [])
                if credentials_schema and len(credentials_schema) > 0:
                    logger.debug(f"插件需要授权，credentials_schema: {len(credentials_schema)} 个字段")
                    return True

            # 检查Model Provider的credentials_schema
            model_provider = manifest.get('model', {})
            if model_provider:
                credentials_schema = model_provider.get('credentials_schema', [])
                if credentials_schema and len(credentials_schema) > 0:
                    logger.debug(f"模型插件需要授权，credentials_schema: {len(credentials_schema)} 个字段")
                    return True

            # 检查Agent Strategy Provider的credentials_schema
            agent_strategy_provider = manifest.get('agent_strategy', {})
            if agent_strategy_provider:
                credentials_schema = agent_strategy_provider.get('credentials_schema', [])
                if credentials_schema and len(credentials_schema) > 0:
                    logger.debug(f"智能体策略插件需要授权，credentials_schema: {len(credentials_schema)} 个字段")
                    return True

            logger.debug("插件不需要授权")
            return False
        except Exception as e:
            logger.warning(f"检查授权需求失败: {e}")
            return False
    
    def upload_and_install(self, difypkg_file_path: str) -> PluginUploadResult:
        """上传并安装.difypkg文件"""
        try:
            # 1. 验证文件
            if not os.path.exists(difypkg_file_path):
                return PluginUploadResult(False, "文件不存在")
            
            if not difypkg_file_path.endswith('.difypkg'):
                return PluginUploadResult(False, "文件格式不正确，必须是.difypkg文件")
            
            # 2. 提取插件清单
            manifest = self._extract_plugin_manifest(difypkg_file_path)
            if not manifest:
                return PluginUploadResult(False, "无法读取插件清单")
            
            # 3. 上传到Dify
            upload_success, upload_message, unique_identifier = self._upload_to_dify(difypkg_file_path)
            if not upload_success:
                return PluginUploadResult(False, f"上传失败: {upload_message}")
            
            # 4. 安装插件
            install_success, install_message = self._install_plugin(unique_identifier)
            if not install_success:
                return PluginUploadResult(False, f"安装失败: {install_message}")

            # 5. 创建插件信息
            plugin_id = manifest.get('name', unique_identifier)
            auth_required = self._check_auth_required(manifest)

            plugin_info = PluginInfo(
                plugin_id=plugin_id,
                name=manifest.get('label', manifest.get('name', 'Unknown Plugin')),
                version=manifest.get('version', '1.0.0'),
                author=manifest.get('author', 'Unknown'),
                description=manifest.get('description', ''),
                unique_identifier=unique_identifier,
                status=PluginStatus.INSTALLED,
                auth_status=AuthStatus.REQUIRED if auth_required else AuthStatus.NOT_REQUIRED,
                install_time=datetime.now(),
                permissions=manifest.get('permissions', {}),
                metadata={'manifest': manifest}
            )

            # 6. 保存到数据库
            plugins_db = self._load_plugins_db()
            plugins_db[plugin_id] = plugin_info
            self._save_plugins_db(plugins_db)

            # 7. 保存插件文件副本
            plugin_file_dest = os.path.join(self.storage_dir, f"{plugin_id}.difypkg")
            import shutil
            shutil.copy2(difypkg_file_path, plugin_file_dest)

            # 根据安装消息确定最终消息
            final_message = "插件安装成功"
            if "已存在" in install_message:
                final_message = "插件已存在，更新成功"

            return PluginUploadResult(
                True,
                final_message,
                plugin_info
            )
            
        except Exception as e:
            logger.error(f"安装插件失败: {e}")
            return PluginUploadResult(False, f"安装过程出错: {str(e)}")
    
    def list_plugins(self) -> List[PluginInfo]:
        """获取已安装插件列表"""
        plugins_db = self._load_plugins_db()
        return list(plugins_db.values())
    
    def get_plugin(self, plugin_id: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        plugins_db = self._load_plugins_db()
        return plugins_db.get(plugin_id)
    
    def uninstall_plugin(self, plugin_id: str) -> Tuple[bool, str]:
        """卸载插件"""
        try:
            plugins_db = self._load_plugins_db()
            
            if plugin_id not in plugins_db:
                return False, "插件不存在"
            
            plugin_info = plugins_db[plugin_id]
            
            # TODO: 调用Dify API卸载插件
            # 目前Dify可能没有提供卸载API，所以先从本地移除
            
            # 删除本地文件
            plugin_file = os.path.join(self.storage_dir, f"{plugin_id}.difypkg")
            if os.path.exists(plugin_file):
                os.remove(plugin_file)
            
            # 从数据库中移除
            del plugins_db[plugin_id]
            self._save_plugins_db(plugins_db)
            
            return True, "插件卸载成功"
            
        except Exception as e:
            logger.error(f"卸载插件失败: {e}")
            return False, f"卸载失败: {str(e)}"
    
    def update_plugin_status(self, plugin_id: str, status: PluginStatus):
        """更新插件状态"""
        try:
            plugins_db = self._load_plugins_db()
            if plugin_id in plugins_db:
                plugins_db[plugin_id].status = status
                self._save_plugins_db(plugins_db)
        except Exception as e:
            logger.error(f"更新插件状态失败: {e}")
    
    def get_plugin_manifest(self, plugin_id: str) -> Optional[Dict[str, Any]]:
        """获取插件清单"""
        plugin_info = self.get_plugin(plugin_id)
        if plugin_info and 'manifest' in plugin_info.metadata:
            return plugin_info.metadata['manifest']
        return None