import logging
import time
from typing import Tuple

import requests

CHARACTER_LIMIT = 10000 # 最大字符限制

def jina_search(query: str, retry_count: int = 2) -> Tuple[str, str]:
    try:
        jina_search_url = "https://s.jina.ai/" + query
        encoded_url = requests.utils.quote(jina_search_url, safe=":/")
        logging.info(f"[JINA_SEARCH] encoded url: {encoded_url}")
        response = requests.get(encoded_url, timeout=15)
        response.raise_for_status()
        return response.text[:CHARACTER_LIMIT], ""
    except Exception as e:
        error_info = f"[JINA_SEARCH] search {query} error: {e}"
        logging.error(error_info)
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[JINA_SEARCH] retrying search {query}, remaining retry count: {retry_count}")
            return jina_search(query, retry_count - 1)
        return "", error_info
