import requests
import logging
import concurrent.futures
import time
from typing import List, Dict, Any, <PERSON><PERSON>

from config import conf
from parse_url import parse_url_with_bs
# 从配置中获取搜索URL
searxng_search_url = conf().get("searxng_base_url", "http://localhost:8080")

# 从配置中获取最大结果数量
searxng_max_results = int(conf().get("searxng_max_results", 5))


def searxng_search(query: str, parse: bool, retry_count: int = 2) -> Tuple[List[Dict[str, Any]], str]:
    try:
        response = requests.get(f"{searxng_search_url}/search", params={"q": query, "format": "json"})
        logging.info(f"searxng search response: {response.text[:100]}...")
        response.raise_for_status()  # 确保请求成功
        search_results = response.json()
        citations = []
        if "results" in search_results:
            # 最多返回max_results条结果
            results = search_results["results"][:searxng_max_results]
            
            # 使用ThreadPoolExecutor进行并行处理
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = [executor.submit(_fetch_and_parse_result, result, parse) for result in results]
                for future in concurrent.futures.as_completed(futures):
                    citations.append(future.result())
        return citations, ""
    except Exception as e:
        error_info = f"Error search {query} with searxng: {e}"
        logging.error(error_info)
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"Retrying search {query} with searxng, Remaining retry count: {retry_count}")
            return searxng_search(query, parse, retry_count - 1)
        return [], error_info


def _fetch_and_parse_result(result, parse):
    """
    处理单个搜索结果，如果需要则解析网页内容
    """
    url = result.get("url", "")
    title = result.get("title", "")
    content = result.get("content", "")
    
    if parse:
        logging.info(f"parsing url: {url}")
        parsed_content, err = parse_url_with_bs(url, retry_count=0, timeout=5)
        if err:
            logging.warning(f"Error parsing url: {url}, error: {err}, use original content instead")
        else:
            content = parsed_content

    return {
        "citation_type": "internet_search",
        "content": content,
        "internet_search_details": {
            "title": title,
            "url": url
        }
    }
