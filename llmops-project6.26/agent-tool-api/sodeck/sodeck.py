import base64
import logging
import os
import random
import time
from typing import Tuple

from config import conf

SFS_ROOT = conf().get("sfs_root")
SFS_BASE_URL = conf().get("sfs_base_url")
QUARTO_BIN_PATH = "bin/quarto/bin/quarto"
QUARTO_HEADER_TEMPLATE = """
---
title: "{SOPHON_DOC_TITLE_NAME}"
format:
  pptx:
    reference-doc: template.potx
---
"""


class SophonDeck:
    
    def __init__(self, sophon_doc_title_name: str = '') -> None:
        # TODO: base64 to text，更好理解
        
        if sophon_doc_title_name == '':
            self.quarto_header = QUARTO_HEADER_TEMPLATE.format(sophon_doc_title_name = 'demo ')
        else:
            self.quarto_header = QUARTO_HEADER_TEMPLATE.format(SOPHON_DOC_TITLE_NAME = sophon_doc_title_name)
    
    def sophon_deck(self):
        return self.quarto_header

def generate_ppt(title: str, content: str) -> Tuple[str, str]:
    try:
        doc = SophonDeck(title).sophon_deck() + '\n' + content
        document_name = 'document_' + base64.b64encode(str(time.time()/10000000 + 100 * random.random()).encode('ascii')).decode("ascii")[2:10]
        qmd_file_name = f'{document_name}.qmd'
        ppt_file_name = f'{document_name}.pptx'
        qmd_file_path = os.path.join(SFS_ROOT, qmd_file_name)
        with open(qmd_file_path, 'w') as f:
            f.write(doc)
        # TODO: 默认会从{qmd_file_path}目录寻找template.potx，在magic_header_str指定template目录或者在当前目录生成qmd文件，并删除
        cmd = f"{QUARTO_BIN_PATH} render {qmd_file_path} --to pptx"
        logging.info(f"[SODECK] executing '{cmd}'")
        exit_status = os.system(cmd)
        if exit_status != 0:
            raise Exception(f"execute '{cmd}' failed")
        sfs_url = os.path.join(SFS_BASE_URL, ppt_file_name)
        return f"[{ppt_file_name}]({sfs_url})", ""
    except Exception as e:
        error_info = f"generate ppt failed: {e}"
        logging.error(f"[SODECK] {error_info}")
        return "", error_info
