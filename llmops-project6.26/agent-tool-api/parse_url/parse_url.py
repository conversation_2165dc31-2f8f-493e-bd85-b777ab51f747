import logging
import time
from typing import Tuple
import requests
from bs4 import BeautifulSoup
from config import conf
import os

parse_url_character_limit = int(conf().get("parse_url_character_limit", 10000))

def parse_url(url: str, retry_count: int = 2, timeout: int = 20) -> Tuple[str, str]:
    try:
        jina_url = f"https://r.jina.ai/{url}"

        print("2")

        response = requests.get(jina_url, timeout=timeout)
        response.raise_for_status()
        url_content = response.text[:parse_url_character_limit] # 限制返回内容长度，防止超过 LLM 上下文长度
        return url_content, ""
    except Exception as e:
        error_info = f"Error parsing URL {url}: {e}"
        logging.error(error_info)
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"Retrying URL parsing..., Remaining retry count: {retry_count}")
            return parse_url(url, retry_count - 1, timeout=timeout)
        return "", error_info




browser_headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Arch': '"x86"',
    'Sec-Ch-Ua-Bitness': '"64"',
    'Sec-Ch-Ua-Full-Version': '"120.0.6099.217"',
    'Sec-Ch-Ua-Full-Version-List': '"Not_A Brand";v="8.0.0.0", "Chromium";v="120.0.6099.217", "Google Chrome";v="120.0.6099.217"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Model': '""',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Ch-Ua-Platform-Version': '"15.0.0"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
}

def parse_url_with_bs(url: str, retry_count: int = 0, timeout: int = 5) -> Tuple[str, str]:
    """
    Parse URL content using requests and BeautifulSoup with retry mechanism.
    
    Args:
        url (str): The URL to parse
        retry_count (int): Number of retries if request fails
        timeout (int): Request timeout in seconds
        
    Returns:
        Tuple[str, str]: (content, error_message)
    """
    http_proxy = os.getenv('http_proxy') or os.getenv('HTTP_PROXY')
    print("HTTP Proxy:", http_proxy)
    try:
        # 发送GET请求
        response = requests.get(
            url,
            headers=browser_headers, 
            timeout=timeout
        )
        response.raise_for_status()
        
        # 1. 首先尝试从HTTP头获取编码
        encoding = response.encoding if response.encoding != 'ISO-8859-1' else None
        
        if not encoding:
            # 2. 尝试从HTML meta标签获取编码
            soup = BeautifulSoup(response.content, 'html.parser')
            meta_charset = soup.find('meta', charset=True)
            if meta_charset:
                encoding = meta_charset.get('charset')
            else:
                meta_content_type = soup.find('meta', {'http-equiv': lambda x: x and x.lower() == 'content-type'})
                if meta_content_type:
                    content = meta_content_type.get('content', '')
                    if 'charset=' in content:
                        encoding = content.split('charset=')[-1]
        
        # 3. 如果以上方法都失败，使用apparent_encoding
        if not encoding:
            encoding = response.apparent_encoding
            
        response.encoding = encoding
        
        # 使用BeautifulSoup解析内容
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 找到body标签
        body = soup.find('body')
        if body:
            # 删除script标签
            for script in body.find_all('script'):
                script.decompose()
            
            # 删除style标签  
            for style in body.find_all('style'):
                style.decompose()
                
            # 提取清理后的文本内容
            content = body.get_text(strip=True)
            content = content[:parse_url_character_limit]
            return content, ""
        else:
            return "", "No body tag found in the HTML"
    except Exception as e:
        error_info = f"Error parsing URL {url}: {e}"
        logging.error(error_info)
        
        # 重试机制
        if retry_count > 0:
            time.sleep(1)  # 等待1秒后重试
            logging.info(f"Retrying URL parsing... Remaining retry count: {retry_count}")
            return parse_url_with_bs(url, retry_count - 1, timeout=timeout)

        return "", error_info
