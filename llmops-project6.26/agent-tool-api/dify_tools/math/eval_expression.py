import logging
from typing import Any, Union

import numexpr as ne

from dify_tools.tool import Tool, ToolInvokeMessage

class EvaluateExpressionTool(Tool):
    def _invoke(self, 
               tool_parameters: dict[str, Any], 
               user_id: str = "default"
        ) -> tuple[str, str]:
        """
            invoke tools
        """
        err = ""
        # get expression
        expression = tool_parameters.get('expression', '').strip()
        if not expression:
            err = 'Invalid expression'
            return self.create_text_message(err), err

        try:
            result = ne.evaluate(expression)
            result_str = str(result)
            return self.create_text_message(f'The result of the expression "{expression}" is {result_str}'), err
        except Exception as e:
            logging.exception(f'Error evaluating expression: {expression}')
            err = f'Invalid expression: {expression}, error: {str(e)}'
            return self.create_text_message(err), err