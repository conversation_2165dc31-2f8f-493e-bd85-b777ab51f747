import os
import requests
import PyPDF2
import pandas as pd
from docx import Document
from io import BytesIO
from typing import <PERSON>ple

def read_file(url: str, name: str) -> <PERSON><PERSON>[str, str]:
    """
    Download a file from URL, save it with given name, and extract text based on file extension
    
    Args:
        url: URL of the file to download
        name: Name to save the file as
    
    Returns:
        Tuple[str, str]: (file_content, error_message)
    """
    try:
        # Download file
        response = requests.get(url)
        response.raise_for_status()
        
        # Get file extension
        _, ext = os.path.splitext(name)
        ext = ext.lower()
        
        # Save file content to BytesIO
        file_content = BytesIO(response.content)
        
        # Extract text based on extension
        if ext in ['.txt', '.md', '.csv']:
            return file_content.getvalue().decode('utf-8'), ""

        elif ext == '.pdf':
            text = ""
            pdf_reader = PyPDF2.PdfReader(file_content)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text, ""

        elif ext == '.docx':
            doc = Document(file_content)
            text = ""
            for para in doc.paragraphs:
                text += para.text + "\n"
            return text, ""

        elif ext in ['.xlsx', '.xls']:
            df_dict = pd.read_excel(file_content, sheet_name=None)
            text = ""
            # 遍历所有sheet
            for sheet_name, df in df_dict.items():
                text += f"\n=== Sheet: {sheet_name} ===\n"
                # 将DataFrame转换为字符串，保持表格格式
                text += df.to_string(index=False) + "\n"
            return text, ""

        else:
            return "", f"Unsupported file extension: {ext}"
            
    except requests.exceptions.RequestException as e:
        return "", f"Failed to download file: {str(e)}"
    except Exception as e:
        return "", f"Error processing file: {str(e)}"
