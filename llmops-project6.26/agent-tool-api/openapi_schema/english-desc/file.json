{"openapi": "3.0.0", "info": {"title": "File reader", "description": "Read the content of various file formats from URL, supporting only txt, md, csv, pdf, docx, xlsx, and xls formats.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"read_file": {"post": {"description": "Download the file from the specified URL and extract its text content, supporting only txt, md, csv, pdf, docx, xlsx, and xls formats.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The HTTP protocol URL address of the file"}, "name": {"type": "string", "description": "File name, including the extension, used to determine the file type, such as temp.txt, book.pdf"}}, "required": ["url", "name"]}}}}}}}}