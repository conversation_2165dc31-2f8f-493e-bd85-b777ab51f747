{"openapi": "3.1.0", "info": {"title": "URL content parsing", "description": "Parse the given link into LLM-understandable text.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"parse_url": {"post": {"description": "Parse the URL link and return the string content in the link.", "operationId": "parseUrl", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL link that needs to be parsed, such as www.baidu.com"}}, "required": ["url"]}}}}}}}, "components": {"schemas": {}}}