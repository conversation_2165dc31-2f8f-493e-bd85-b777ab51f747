{"openapi": "3.1.0", "info": {"title": "Web scraping", "description": "Use the Firecrawl API to crawl web pages.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"fire_crawl": {"post": {"description": "The tool launches a web crawler to extract data from specified URLs. It allows configuring crawler options, such as including or excluding specific URLs, generating alternative text for images using LLMs, and specifying the maximum number of pages to crawl. Depending on the provided options, the tool can return a list of crawled documents or a list of URLs.", "operationId": "fire_crawl", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL of the website to be scraped. This is a mandatory parameter."}, "crawl_limit": {"type": "integer", "description": "The maximum limit for the number of pages to be crawled, after which the crawling will stop.", "default": 3}}, "required": ["url", "crawl_limit"]}}}}}}}}