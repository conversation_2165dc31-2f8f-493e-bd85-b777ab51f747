{"openapi": "3.0.0", "info": {"title": "Bing Search", "description": "A tool for obtaining information using a search engine.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"bing_search": {"post": {"description": "A tool for obtaining information using a search engine.", "operationId": "必应搜索", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Content to be searched", "default": ""}}, "required": ["query"]}}}}}}}}