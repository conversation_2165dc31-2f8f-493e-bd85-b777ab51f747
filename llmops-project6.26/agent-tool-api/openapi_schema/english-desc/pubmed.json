{"openapi": "3.1.0", "info": {"title": "PubMed search", "description": "A search engine for biomedical literature.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"pubmed_search": {"post": {"description": "A tool for searching PubMed biomedical literature, including citations from MEDLINE, life science journals, and online books.", "operationId": "pubmed_search", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Content to be searched, only supports English queries, so you need to translate the Chinese query into English.", "default": ""}}, "required": ["query"]}}}}}}}}