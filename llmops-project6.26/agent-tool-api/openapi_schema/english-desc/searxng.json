{"openapi": "3.0.0", "info": {"title": "SearXNG search", "description": "A tool for obtaining information using a search engine.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"searxng_search": {"post": {"description": "A tool that uses a search engine to obtain information.", "operationId": "SearXNG搜索", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Content that needs to be searched"}}, "required": ["query"]}}}}}}}}