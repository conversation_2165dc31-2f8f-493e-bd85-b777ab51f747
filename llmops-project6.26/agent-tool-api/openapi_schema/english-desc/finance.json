{"openapi": "3.0.0", "info": {"title": "Financial search", "description": "A tool for obtaining financial information using a search engine.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"finance_search": {"post": {"description": "A tool for obtaining financial information using a search engine.", "operationId": "财经搜索", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The content that needs to be searched", "default": ""}}, "required": ["query"]}}}}}}, "tushare_search": {"post": {"description": "A tool for searching financial information using the Tushare website.", "operationId": "tushare财经搜索", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Content that needs to be searched", "default": ""}}, "required": ["query"]}}}}}}}}