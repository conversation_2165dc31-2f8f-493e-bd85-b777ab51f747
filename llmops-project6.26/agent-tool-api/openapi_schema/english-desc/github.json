{"openapi": "3.1.0", "info": {"title": "GitHub repository", "description": "GitHub is an online service for hosting software source code.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"github_repositories": {"post": {"description": "A tool for searching popular repositories or open-source projects on GitHub by keywords.", "operationId": "github_repositories", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Content that needs to be searched"}, "access_token": {"type": "string", "description": "GitHub access token", "default": "*********************************************************************************************"}}, "required": ["query", "access_token"]}}}}}}}}