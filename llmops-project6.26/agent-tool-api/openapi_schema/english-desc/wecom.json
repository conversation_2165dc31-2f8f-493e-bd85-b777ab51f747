{"openapi": "3.0.0", "info": {"title": "Enterprise WeChat", "description": "Enterprise WeChat Toolkit", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"wecom_group_msg": {"post": {"description": "A tool for sending messages to a WeCom (Work WeChat) group for businesses.", "operationId": "sendWeComGroupMessage", "requestBody": {"description": "Message payload", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "The content of the message that needs to be sent"}}, "required": ["message"]}}}}}}}}