{"openapi": "3.1.0", "info": {"title": "Chart generation", "description": "Chart generation is a tool used to create visual charts, allowing you to generate various types of charts such as bar charts, line charts, pie charts, and more.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/chart/"}], "paths": {"line": {"post": {"description": "Generate a linear chart that includes the input data.", "operationId": "line", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "Data for generating a line chart, the data should be a string containing a list of numbers, \"1;2;3;4;5\""}, "x_axis": {"type": "string", "description": "For the x-axis of a linear chart, the x-axis should be a string containing a series of texts, such as \"a;b;c;1;2\", to match the data."}}, "required": ["data"]}}}}}}, "bar": {"post": {"description": "Generate a bar chart using the input data.", "operationId": "bar", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "Data used to generate a bar chart, the data should be a string containing a list of numbers, for example \"1;2;3;4;5\""}, "x_axis": {"type": "string", "description": "The x-axis of the bar chart should be a string containing a list of texts, such as \"a;b;c;1;2\", to match the data."}}, "required": ["data"]}}}}}}, "pie": {"post": {"description": "Generate a pie chart using the input data.", "operationId": "pie", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "The data used to generate the pie chart should be a string containing a list of numbers, such as \"1;2;3;4;5\"."}, "categories": {"type": "string", "description": "The categories of the pie chart should be a string containing a list of texts, such as \"a;b;c;1;2\", to match the data."}}, "required": ["data"]}}}}}}}}