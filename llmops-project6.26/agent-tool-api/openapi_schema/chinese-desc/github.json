{"openapi": "3.1.0", "info": {"title": "github仓库", "description": "GitHub 是一个在线软件源代码托管服务。", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"github_repositories": {"post": {"description": "一个通过关键词搜索github热门仓库或开源项目的工具。", "operationId": "github_repositories", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "需要搜索的内容"}, "access_token": {"type": "string", "description": "github access token", "default": "*********************************************************************************************"}}, "required": ["query", "access_token"]}}}}}}}}