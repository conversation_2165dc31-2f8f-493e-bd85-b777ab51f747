{"openapi": "3.0.0", "info": {"title": "代码解释器", "description": "运行一段代码并返回结果", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/code_interpreter/"}], "paths": {"python": {"post": {"description": "一个用于运行python代码并获取运行结果的工具。在代码中请使用print()函数输出运行结果，否则结果将为空。", "operationId": "python代码解释器", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "description": "需要执行的python代码，请输入有效的代码，并且在代码中使用print()函数输出运行结果，否则结果将为空。"}}, "required": ["code"]}}}}}}}}