{"openapi": "3.1.0", "info": {"title": "高德", "description": "高德开放平台服务工具集。", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"gaode_weather": {"post": {"description": "当你想询问天气或与天气相关的问题时使用的工具。", "operationId": "gaode_weather", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"city": {"type": "string", "description": "用于天气查询的城市名称。如果没有城市信息，你可以询问城市名称。你需要从问题中提取出中文城市名称。"}}, "required": ["city"]}}}}}}}}