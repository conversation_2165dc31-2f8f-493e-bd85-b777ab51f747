{"openapi": "3.1.0", "info": {"title": "网页爬取", "description": "使用Firecrawl API爬取网页", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"fire_crawl": {"post": {"description": "该工具启动网络爬虫，从指定的URL中提取数据。它允许配置爬虫选项，例如包括或排除特定的url，使用LLM生成图像的替代文本，指定爬取页面的最大数量。根据提供的选项，该工具可以返回已爬取文档列表或URL列表。", "operationId": "fire_crawl", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "需要抓取的网站的URL。这是一个必填参数。"}, "crawl_limit": {"type": "integer", "description": "爬取页面的最大数量限制，达到该数量后会停止爬取", "default": 3}}, "required": ["url", "crawl_limit"]}}}}}}}}