{"openapi": "3.1.0", "info": {"title": "URL内容解析", "description": "把给定的链接解析成LLM理解友好的文本", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"parse_url": {"post": {"description": "解析url链接并返回链接中的字符串内容。", "operationId": "parseUrl", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "需要被解析的url链接，如www.baidu.com"}}, "required": ["url"]}}}}}}}, "components": {"schemas": {}}}