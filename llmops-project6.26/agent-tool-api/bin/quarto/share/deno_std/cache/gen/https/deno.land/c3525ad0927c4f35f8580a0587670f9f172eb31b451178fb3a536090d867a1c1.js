// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { DEFAULT_SCHEMA } from "./schema/mod.ts";
export class State {
  schema;
  constructor(schema = DEFAULT_SCHEMA){
    this.schema = schema;
  }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3lhbWwvX3N0YXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFBvcnRlZCBmcm9tIGpzLXlhbWwgdjMuMTMuMTpcbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9ub2RlY2EvanMteWFtbC9jb21taXQvNjY1YWFkZGE0MjM0OWRjYWU4NjlmMTIwNDBkOWIxMGVmMThkMTJkYVxuLy8gQ29weXJpZ2h0IDIwMTEtMjAxNSBieSBWaXRhbHkgUHV6cmluLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG5cbmltcG9ydCB0eXBlIHsgU2NoZW1hRGVmaW5pdGlvbiB9IGZyb20gXCIuL3NjaGVtYS50c1wiO1xuaW1wb3J0IHsgREVGQVVMVF9TQ0hFTUEgfSBmcm9tIFwiLi9zY2hlbWEvbW9kLnRzXCI7XG5cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBTdGF0ZSB7XG4gIGNvbnN0cnVjdG9yKHB1YmxpYyBzY2hlbWE6IFNjaGVtYURlZmluaXRpb24gPSBERUZBVUxUX1NDSEVNQSkge31cbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwrQkFBK0I7QUFDL0Isb0ZBQW9GO0FBQ3BGLDBFQUEwRTtBQUMxRSwwRUFBMEU7QUFHMUUsU0FBUyxjQUFjLFFBQVEsa0JBQWtCO0FBRWpELE9BQU8sTUFBZTtFQUNEO0VBQW5CLFlBQW1CLFNBQTJCLGNBQWMsQ0FBRTtrQkFBM0M7RUFBNEM7QUFDakUifQ==