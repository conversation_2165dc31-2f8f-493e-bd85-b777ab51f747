// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * An abstraction of multiple Uint8Arrays
 *
 * @deprecated (will be removed in 0.205.0) Use a plain array of Uint8Arrays instead.
 */ export class BytesList {
  #len = 0;
  #chunks = [];
  constructor(){}
  /**
   * Total size of bytes
   *
   * @deprecated
   */ size() {
    return this.#len;
  }
  /**
   * Push bytes with given offset infos
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   * Adding into the array can be done with {@linkcode Array#push}.
   * If {@linkcode start} or {@linkcode end} parameters are
   * used then use {@linkcode Uint8Array#subarray}
   * to slice the needed part without copying.
   */ add(value, start = 0, end = value.byteLength) {
    if (value.byteLength === 0 || end - start === 0) {
      return;
    }
    checkRange(start, end, value.byteLength);
    this.#chunks.push({
      value,
      end,
      start,
      offset: this.#len
    });
    this.#len += end - start;
  }
  /**
   * Drop head `n` bytes.
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   * Shifting from the array can be done using conditional
   * {@linkcode Array#shift}s against the number of bytes left
   * to be dropped.
   *
   * If the next item in the array is longer than the number
   * of bytes left to be dropped, then instead of shifting it out
   * it should be replaced in-place with a subarray of itself that
   * drops the remaining bytes from the front.
   */ shift(n) {
    if (n === 0) {
      return;
    }
    if (this.#len <= n) {
      this.#chunks = [];
      this.#len = 0;
      return;
    }
    const idx = this.getChunkIndex(n);
    this.#chunks.splice(0, idx);
    const [chunk] = this.#chunks;
    if (chunk) {
      const diff = n - chunk.offset;
      chunk.start += diff;
    }
    let offset = 0;
    for (const chunk of this.#chunks){
      chunk.offset = offset;
      offset += chunk.end - chunk.start;
    }
    this.#len = offset;
  }
  /**
   * Find chunk index in which `pos` locates by binary-search
   * returns -1 if out of range
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   * Finding the index of a chunk in the array can be
   * done using {@linkcode Array#findIndex} with a counter
   * for the number of bytes already encountered from past
   * chunks' {@linkcode Uint8Array#byteLength}.
   */ getChunkIndex(pos) {
    let max = this.#chunks.length;
    let min = 0;
    while(true){
      const i = min + Math.floor((max - min) / 2);
      if (i < 0 || this.#chunks.length <= i) {
        return -1;
      }
      const { offset, start, end } = this.#chunks[i];
      const len = end - start;
      if (offset <= pos && pos < offset + len) {
        return i;
      } else if (offset + len <= pos) {
        min = i + 1;
      } else {
        max = i - 1;
      }
    }
  }
  /**
   * Get indexed byte from chunks
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   * See {@linkcode getChunkIndex} for finding a chunk
   * by number of bytes.
   */ get(i) {
    if (i < 0 || this.#len <= i) {
      throw new Error("out of range");
    }
    const idx = this.getChunkIndex(i);
    const { value, offset, start } = this.#chunks[idx];
    return value[start + i - offset];
  }
  /**
   * Iterator of bytes from given position
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   */ *iterator(start = 0) {
    const startIdx = this.getChunkIndex(start);
    if (startIdx < 0) return;
    const first = this.#chunks[startIdx];
    let firstOffset = start - first.offset;
    for(let i = startIdx; i < this.#chunks.length; i++){
      const chunk = this.#chunks[i];
      for(let j = chunk.start + firstOffset; j < chunk.end; j++){
        yield chunk.value[j];
      }
      firstOffset = 0;
    }
  }
  /**
   * Returns subset of bytes copied
   *
   * @deprecated Use a plain array of Uint8Arrays instead.
   * For copying the whole list see {@linkcode concat}.
   * For copying subarrays find the start and end chunk indexes
   * and the internal indexes within those Uint8Arrays, prepare
   * a Uint8Array of size `end - start` and set the chunks (or
   * chunk subarrays) into that at proper offsets.
   */ slice(start, end = this.#len) {
    if (end === start) {
      return new Uint8Array();
    }
    checkRange(start, end, this.#len);
    const result = new Uint8Array(end - start);
    const startIdx = this.getChunkIndex(start);
    const endIdx = this.getChunkIndex(end - 1);
    let written = 0;
    for(let i = startIdx; i <= endIdx; i++){
      const { value: chunkValue, start: chunkStart, end: chunkEnd, offset: chunkOffset } = this.#chunks[i];
      const readStart = chunkStart + (i === startIdx ? start - chunkOffset : 0);
      const readEnd = i === endIdx ? end - chunkOffset + chunkStart : chunkEnd;
      const len = readEnd - readStart;
      result.set(chunkValue.subarray(readStart, readEnd), written);
      written += len;
    }
    return result;
  }
  /**
   * Concatenate chunks into single Uint8Array copied.
   *
   * @deprecated Use a plain array of Uint8Arrays and the `concat.ts` module  instead.
   */ concat() {
    const result = new Uint8Array(this.#len);
    let sum = 0;
    for (const { value, start, end } of this.#chunks){
      result.set(value.subarray(start, end), sum);
      sum += end - start;
    }
    return result;
  }
}
function checkRange(start, end, len) {
  if (start < 0 || len < start || end < 0 || len < end || end < start) {
    throw new Error("invalid range");
  }
}
//# sourceMappingURL=data:application/json;base64,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