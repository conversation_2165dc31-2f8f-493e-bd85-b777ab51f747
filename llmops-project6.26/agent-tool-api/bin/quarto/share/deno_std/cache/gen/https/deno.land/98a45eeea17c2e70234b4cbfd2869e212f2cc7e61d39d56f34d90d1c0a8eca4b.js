// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { comparatorIntersects } from "./comparator_intersects.ts";
function rangesSatisfiable(ranges) {
  return ranges.every((r)=>{
    // For each OR at least one AND must be satisfiable
    return r.ranges.some((comparators)=>comparatorsSatisfiable(comparators));
  });
}
function comparatorsSatisfiable(comparators) {
  // Comparators are satisfiable if they all intersect with each other
  for(let i = 0; i < comparators.length - 1; i++){
    const c0 = comparators[i];
    for (const c1 of comparators.slice(i + 1)){
      if (!comparatorIntersects(c0, c1)) {
        return false;
      }
    }
  }
  return true;
}
/**
 * The ranges intersect every range of AND comparators intersects with a least one range of OR ranges.
 * @param r0 range 0
 * @param r1 range 1
 * @returns returns true if any
 */ export function rangeIntersects(r0, r1) {
  return rangesSatisfiable([
    r0,
    r1
  ]) && r0.ranges.some((r00)=>{
    return r1.ranges.some((r11)=>{
      return r00.every((c0)=>{
        return r11.every((c1)=>comparatorIntersects(c0, c1));
      });
    });
  });
}
//# sourceMappingURL=data:application/json;base64,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