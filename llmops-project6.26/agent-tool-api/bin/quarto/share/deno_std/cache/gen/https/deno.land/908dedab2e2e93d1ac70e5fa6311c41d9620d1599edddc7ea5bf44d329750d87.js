// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Merge multiple streams into a single one, taking order into account, and each stream
 * will wait for a chunk to enqueue before the next stream can append another chunk.
 * If a stream ends before other ones, the others will continue adding data in order,
 * and the finished one will not add any more data.
 */ export function zipReadableStreams(...streams) {
  const readers = new Set(streams.map((s)=>s.getReader()));
  return new ReadableStream({
    async start (controller) {
      try {
        let resolved = 0;
        while(resolved !== streams.length){
          for (const reader of readers){
            const { value, done } = await reader.read();
            if (!done) {
              controller.enqueue(value);
            } else {
              resolved++;
              readers.delete(reader);
            }
          }
        }
        controller.close();
      } catch (e) {
        controller.error(e);
      }
    }
  });
}
//# sourceMappingURL=data:application/json;base64,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