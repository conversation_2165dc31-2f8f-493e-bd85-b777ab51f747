// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { YAMLError } from "../_error.ts";
import * as common from "../_utils.ts";
import { DumperState } from "./dumper_state.ts";
const _toString = Object.prototype.toString;
const { hasOwn } = Object;
const CHAR_TAB = 0x09; /* Tab */ 
const CHAR_LINE_FEED = 0x0a; /* LF */ 
const CHAR_SPACE = 0x20; /* Space */ 
const CHAR_EXCLAMATION = 0x21; /* ! */ 
const CHAR_DOUBLE_QUOTE = 0x22; /* " */ 
const CHAR_SHARP = 0x23; /* # */ 
const CHAR_PERCENT = 0x25; /* % */ 
const CHAR_AMPERSAND = 0x26; /* & */ 
const CHAR_SINGLE_QUOTE = 0x27; /* ' */ 
const CHAR_ASTERISK = 0x2a; /* * */ 
const CHAR_COMMA = 0x2c; /* , */ 
const CHAR_MINUS = 0x2d; /* - */ 
const CHAR_COLON = 0x3a; /* : */ 
const CHAR_GREATER_THAN = 0x3e; /* > */ 
const CHAR_QUESTION = 0x3f; /* ? */ 
const CHAR_COMMERCIAL_AT = 0x40; /* @ */ 
const CHAR_LEFT_SQUARE_BRACKET = 0x5b; /* [ */ 
const CHAR_RIGHT_SQUARE_BRACKET = 0x5d; /* ] */ 
const CHAR_GRAVE_ACCENT = 0x60; /* ` */ 
const CHAR_LEFT_CURLY_BRACKET = 0x7b; /* { */ 
const CHAR_VERTICAL_LINE = 0x7c; /* | */ 
const CHAR_RIGHT_CURLY_BRACKET = 0x7d; /* } */ 
const ESCAPE_SEQUENCES = {};
ESCAPE_SEQUENCES[0x00] = "\\0";
ESCAPE_SEQUENCES[0x07] = "\\a";
ESCAPE_SEQUENCES[0x08] = "\\b";
ESCAPE_SEQUENCES[0x09] = "\\t";
ESCAPE_SEQUENCES[0x0a] = "\\n";
ESCAPE_SEQUENCES[0x0b] = "\\v";
ESCAPE_SEQUENCES[0x0c] = "\\f";
ESCAPE_SEQUENCES[0x0d] = "\\r";
ESCAPE_SEQUENCES[0x1b] = "\\e";
ESCAPE_SEQUENCES[0x22] = '\\"';
ESCAPE_SEQUENCES[0x5c] = "\\\\";
ESCAPE_SEQUENCES[0x85] = "\\N";
ESCAPE_SEQUENCES[0xa0] = "\\_";
ESCAPE_SEQUENCES[0x2028] = "\\L";
ESCAPE_SEQUENCES[0x2029] = "\\P";
const DEPRECATED_BOOLEANS_SYNTAX = [
  "y",
  "Y",
  "yes",
  "Yes",
  "YES",
  "on",
  "On",
  "ON",
  "n",
  "N",
  "no",
  "No",
  "NO",
  "off",
  "Off",
  "OFF"
];
function encodeHex(character) {
  const string = character.toString(16).toUpperCase();
  let handle;
  let length;
  if (character <= 0xff) {
    handle = "x";
    length = 2;
  } else if (character <= 0xffff) {
    handle = "u";
    length = 4;
  } else if (character <= 0xffffffff) {
    handle = "U";
    length = 8;
  } else {
    throw new YAMLError("code point within a string may not be greater than 0xFFFFFFFF");
  }
  return `\\${handle}${common.repeat("0", length - string.length)}${string}`;
}
// Indents every line in a string. Empty lines (\n only) are not indented.
function indentString(string, spaces) {
  const ind = common.repeat(" ", spaces), length = string.length;
  let position = 0, next = -1, result = "", line;
  while(position < length){
    next = string.indexOf("\n", position);
    if (next === -1) {
      line = string.slice(position);
      position = length;
    } else {
      line = string.slice(position, next + 1);
      position = next + 1;
    }
    if (line.length && line !== "\n") result += ind;
    result += line;
  }
  return result;
}
function generateNextLine(state, level) {
  return `\n${common.repeat(" ", state.indent * level)}`;
}
function testImplicitResolving(state, str) {
  let type;
  for(let index = 0, length = state.implicitTypes.length; index < length; index += 1){
    type = state.implicitTypes[index];
    if (type.resolve(str)) {
      return true;
    }
  }
  return false;
}
// [33] s-white ::= s-space | s-tab
function isWhitespace(c) {
  return c === CHAR_SPACE || c === CHAR_TAB;
}
// Returns true if the character can be printed without escaping.
// From YAML 1.2: "any allowed characters known to be non-printable
// should also be escaped. [However,] This isn’t mandatory"
// Derived from nb-char - \t - #x85 - #xA0 - #x2028 - #x2029.
function isPrintable(c) {
  return 0x00020 <= c && c <= 0x00007e || 0x000a1 <= c && c <= 0x00d7ff && c !== 0x2028 && c !== 0x2029 || 0x0e000 <= c && c <= 0x00fffd && c !== 0xfeff || 0x10000 <= c && c <= 0x10ffff;
}
// Simplified test for values allowed after the first character in plain style.
function isPlainSafe(c) {
  // Uses a subset of nb-char - c-flow-indicator - ":" - "#"
  // where nb-char ::= c-printable - b-char - c-byte-order-mark.
  return isPrintable(c) && c !== 0xfeff && // - c-flow-indicator
  c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET && // - ":" - "#"
  c !== CHAR_COLON && c !== CHAR_SHARP;
}
// Simplified test for values allowed as the first character in plain style.
function isPlainSafeFirst(c) {
  // Uses a subset of ns-char - c-indicator
  // where ns-char = nb-char - s-white.
  return isPrintable(c) && c !== 0xfeff && !isWhitespace(c) && // - s-white
  // - (c-indicator ::=
  // “-” | “?” | “:” | “,” | “[” | “]” | “{” | “}”
  c !== CHAR_MINUS && c !== CHAR_QUESTION && c !== CHAR_COLON && c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET && // | “#” | “&” | “*” | “!” | “|” | “>” | “'” | “"”
  c !== CHAR_SHARP && c !== CHAR_AMPERSAND && c !== CHAR_ASTERISK && c !== CHAR_EXCLAMATION && c !== CHAR_VERTICAL_LINE && c !== CHAR_GREATER_THAN && c !== CHAR_SINGLE_QUOTE && c !== CHAR_DOUBLE_QUOTE && // | “%” | “@” | “`”)
  c !== CHAR_PERCENT && c !== CHAR_COMMERCIAL_AT && c !== CHAR_GRAVE_ACCENT;
}
// Determines whether block indentation indicator is required.
function needIndentIndicator(string) {
  const leadingSpaceRe = /^\n* /;
  return leadingSpaceRe.test(string);
}
const STYLE_PLAIN = 1, STYLE_SINGLE = 2, STYLE_LITERAL = 3, STYLE_FOLDED = 4, STYLE_DOUBLE = 5;
// Determines which scalar styles are possible and returns the preferred style.
// lineWidth = -1 => no limit.
// Pre-conditions: str.length > 0.
// Post-conditions:
//  STYLE_PLAIN or STYLE_SINGLE => no \n are in the string.
//  STYLE_LITERAL => no lines are suitable for folding (or lineWidth is -1).
//  STYLE_FOLDED => a line > lineWidth and can be folded (and lineWidth !== -1).
function chooseScalarStyle(string, singleLineOnly, indentPerLevel, lineWidth, testAmbiguousType) {
  const shouldTrackWidth = lineWidth !== -1;
  let hasLineBreak = false, hasFoldableLine = false, previousLineBreak = -1, plain = isPlainSafeFirst(string.charCodeAt(0)) && !isWhitespace(string.charCodeAt(string.length - 1));
  let char, i;
  if (singleLineOnly) {
    // Case: no block styles.
    // Check for disallowed characters to rule out plain and single.
    for(i = 0; i < string.length; i++){
      char = string.charCodeAt(i);
      if (!isPrintable(char)) {
        return STYLE_DOUBLE;
      }
      plain = plain && isPlainSafe(char);
    }
  } else {
    // Case: block styles permitted.
    for(i = 0; i < string.length; i++){
      char = string.charCodeAt(i);
      if (char === CHAR_LINE_FEED) {
        hasLineBreak = true;
        // Check if any line can be folded.
        if (shouldTrackWidth) {
          hasFoldableLine = hasFoldableLine || // Foldable line = too long, and not more-indented.
          i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== " ";
          previousLineBreak = i;
        }
      } else if (!isPrintable(char)) {
        return STYLE_DOUBLE;
      }
      plain = plain && isPlainSafe(char);
    }
    // in case the end is missing a \n
    hasFoldableLine = hasFoldableLine || shouldTrackWidth && i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== " ";
  }
  // Although every style can represent \n without escaping, prefer block styles
  // for multiline, since they're more readable and they don't add empty lines.
  // Also prefer folding a super-long line.
  if (!hasLineBreak && !hasFoldableLine) {
    // Strings interpretable as another type have to be quoted;
    // e.g. the string 'true' vs. the boolean true.
    return plain && !testAmbiguousType(string) ? STYLE_PLAIN : STYLE_SINGLE;
  }
  // Edge case: block indentation indicator can only have one digit.
  if (indentPerLevel > 9 && needIndentIndicator(string)) {
    return STYLE_DOUBLE;
  }
  // At this point we know block styles are valid.
  // Prefer literal style unless we want to fold.
  return hasFoldableLine ? STYLE_FOLDED : STYLE_LITERAL;
}
// Greedy line breaking.
// Picks the longest line under the limit each time,
// otherwise settles for the shortest line over the limit.
// NB. More-indented lines *cannot* be folded, as that would add an extra \n.
function foldLine(line, width) {
  if (line === "" || line[0] === " ") return line;
  // Since a more-indented line adds a \n, breaks can't be followed by a space.
  const breakRe = / [^ ]/g; // note: the match index will always be <= length-2.
  let match;
  // start is an inclusive index. end, curr, and next are exclusive.
  let start = 0, end, curr = 0, next = 0;
  let result = "";
  // Invariants: 0 <= start <= length-1.
  //   0 <= curr <= next <= max(0, length-2). curr - start <= width.
  // Inside the loop:
  //   A match implies length >= 2, so curr and next are <= length-2.
  // tslint:disable-next-line:no-conditional-assignment
  while(match = breakRe.exec(line)){
    next = match.index;
    // maintain invariant: curr - start <= width
    if (next - start > width) {
      end = curr > start ? curr : next; // derive end <= length-2
      result += `\n${line.slice(start, end)}`;
      // skip the space that was output as \n
      start = end + 1; // derive start <= length-1
    }
    curr = next;
  }
  // By the invariants, start <= length-1, so there is something left over.
  // It is either the whole string or a part starting from non-whitespace.
  result += "\n";
  // Insert a break if the remainder is too long and there is a break available.
  if (line.length - start > width && curr > start) {
    result += `${line.slice(start, curr)}\n${line.slice(curr + 1)}`;
  } else {
    result += line.slice(start);
  }
  return result.slice(1); // drop extra \n joiner
}
// (See the note for writeScalar.)
function dropEndingNewline(string) {
  return string[string.length - 1] === "\n" ? string.slice(0, -1) : string;
}
// Note: a long line without a suitable break point will exceed the width limit.
// Pre-conditions: every char in str isPrintable, str.length > 0, width > 0.
function foldString(string, width) {
  // In folded style, $k$ consecutive newlines output as $k+1$ newlines—
  // unless they're before or after a more-indented line, or at the very
  // beginning or end, in which case $k$ maps to $k$.
  // Therefore, parse each chunk as newline(s) followed by a content line.
  const lineRe = /(\n+)([^\n]*)/g;
  // first line (possibly an empty line)
  let result = (()=>{
    let nextLF = string.indexOf("\n");
    nextLF = nextLF !== -1 ? nextLF : string.length;
    lineRe.lastIndex = nextLF;
    return foldLine(string.slice(0, nextLF), width);
  })();
  // If we haven't reached the first content line yet, don't add an extra \n.
  let prevMoreIndented = string[0] === "\n" || string[0] === " ";
  let moreIndented;
  // rest of the lines
  let match;
  // tslint:disable-next-line:no-conditional-assignment
  while(match = lineRe.exec(string)){
    const prefix = match[1], line = match[2];
    moreIndented = line[0] === " ";
    result += prefix + (!prevMoreIndented && !moreIndented && line !== "" ? "\n" : "") + foldLine(line, width);
    prevMoreIndented = moreIndented;
  }
  return result;
}
// Escapes a double-quoted string.
function escapeString(string) {
  let result = "";
  let char, nextChar;
  let escapeSeq;
  for(let i = 0; i < string.length; i++){
    char = string.charCodeAt(i);
    // Check for surrogate pairs (reference Unicode 3.0 section "3.7 Surrogates").
    if (char >= 0xd800 && char <= 0xdbff /* high surrogate */ ) {
      nextChar = string.charCodeAt(i + 1);
      if (nextChar >= 0xdc00 && nextChar <= 0xdfff /* low surrogate */ ) {
        // Combine the surrogate pair and store it escaped.
        result += encodeHex((char - 0xd800) * 0x400 + nextChar - 0xdc00 + 0x10000);
        // Advance index one extra since we already used that char here.
        i++;
        continue;
      }
    }
    escapeSeq = ESCAPE_SEQUENCES[char];
    result += !escapeSeq && isPrintable(char) ? string[i] : escapeSeq || encodeHex(char);
  }
  return result;
}
// Pre-conditions: string is valid for a block scalar, 1 <= indentPerLevel <= 9.
function blockHeader(string, indentPerLevel) {
  const indentIndicator = needIndentIndicator(string) ? String(indentPerLevel) : "";
  // note the special case: the string '\n' counts as a "trailing" empty line.
  const clip = string[string.length - 1] === "\n";
  const keep = clip && (string[string.length - 2] === "\n" || string === "\n");
  const chomp = keep ? "+" : clip ? "" : "-";
  return `${indentIndicator}${chomp}\n`;
}
// Note: line breaking/folding is implemented for only the folded style.
// NB. We drop the last trailing newline (if any) of a returned block scalar
//  since the dumper adds its own newline. This always works:
//    • No ending newline => unaffected; already using strip "-" chomping.
//    • Ending newline    => removed then restored.
//  Importantly, this keeps the "+" chomp indicator from gaining an extra line.
function writeScalar(state, string, level, iskey) {
  state.dump = (()=>{
    if (string.length === 0) {
      return "''";
    }
    if (!state.noCompatMode && DEPRECATED_BOOLEANS_SYNTAX.indexOf(string) !== -1) {
      return `'${string}'`;
    }
    const indent = state.indent * Math.max(1, level); // no 0-indent scalars
    // As indentation gets deeper, let the width decrease monotonically
    // to the lower bound min(state.lineWidth, 40).
    // Note that this implies
    //  state.lineWidth ≤ 40 + state.indent: width is fixed at the lower bound.
    //  state.lineWidth > 40 + state.indent: width decreases until the lower
    //  bound.
    // This behaves better than a constant minimum width which disallows
    // narrower options, or an indent threshold which causes the width
    // to suddenly increase.
    const lineWidth = state.lineWidth === -1 ? -1 : Math.max(Math.min(state.lineWidth, 40), state.lineWidth - indent);
    // Without knowing if keys are implicit/explicit,
    // assume implicit for safety.
    const singleLineOnly = iskey || // No block styles in flow mode.
    state.flowLevel > -1 && level >= state.flowLevel;
    function testAmbiguity(str) {
      return testImplicitResolving(state, str);
    }
    switch(chooseScalarStyle(string, singleLineOnly, state.indent, lineWidth, testAmbiguity)){
      case STYLE_PLAIN:
        return string;
      case STYLE_SINGLE:
        return `'${string.replace(/'/g, "''")}'`;
      case STYLE_LITERAL:
        return `|${blockHeader(string, state.indent)}${dropEndingNewline(indentString(string, indent))}`;
      case STYLE_FOLDED:
        return `>${blockHeader(string, state.indent)}${dropEndingNewline(indentString(foldString(string, lineWidth), indent))}`;
      case STYLE_DOUBLE:
        return `"${escapeString(string)}"`;
      default:
        throw new YAMLError("impossible error: invalid scalar style");
    }
  })();
}
function writeFlowSequence(state, level, object) {
  let _result = "";
  const _tag = state.tag;
  for(let index = 0, length = object.length; index < length; index += 1){
    // Write only valid elements.
    if (writeNode(state, level, object[index], false, false)) {
      if (index !== 0) _result += `,${!state.condenseFlow ? " " : ""}`;
      _result += state.dump;
    }
  }
  state.tag = _tag;
  state.dump = `[${_result}]`;
}
function writeBlockSequence(state, level, object, compact = false) {
  let _result = "";
  const _tag = state.tag;
  for(let index = 0, length = object.length; index < length; index += 1){
    // Write only valid elements.
    if (writeNode(state, level + 1, object[index], true, true)) {
      if (!compact || index !== 0) {
        _result += generateNextLine(state, level);
      }
      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
        _result += "-";
      } else {
        _result += "- ";
      }
      _result += state.dump;
    }
  }
  state.tag = _tag;
  state.dump = _result || "[]"; // Empty sequence if no valid values.
}
function writeFlowMapping(state, level, object) {
  let _result = "";
  const _tag = state.tag, objectKeyList = Object.keys(object);
  let pairBuffer, objectKey, objectValue;
  for(let index = 0, length = objectKeyList.length; index < length; index += 1){
    pairBuffer = state.condenseFlow ? '"' : "";
    if (index !== 0) pairBuffer += ", ";
    objectKey = objectKeyList[index];
    objectValue = object[objectKey];
    if (!writeNode(state, level, objectKey, false, false)) {
      continue; // Skip this pair because of invalid key;
    }
    if (state.dump.length > 1024) pairBuffer += "? ";
    pairBuffer += `${state.dump}${state.condenseFlow ? '"' : ""}:${state.condenseFlow ? "" : " "}`;
    if (!writeNode(state, level, objectValue, false, false)) {
      continue; // Skip this pair because of invalid value.
    }
    pairBuffer += state.dump;
    // Both key and value are valid.
    _result += pairBuffer;
  }
  state.tag = _tag;
  state.dump = `{${_result}}`;
}
function writeBlockMapping(state, level, object, compact = false) {
  const _tag = state.tag, objectKeyList = Object.keys(object);
  let _result = "";
  // Allow sorting keys so that the output file is deterministic
  if (state.sortKeys === true) {
    // Default sorting
    objectKeyList.sort();
  } else if (typeof state.sortKeys === "function") {
    // Custom sort function
    objectKeyList.sort(state.sortKeys);
  } else if (state.sortKeys) {
    // Something is wrong
    throw new YAMLError("sortKeys must be a boolean or a function");
  }
  let pairBuffer = "", objectKey, objectValue, explicitPair;
  for(let index = 0, length = objectKeyList.length; index < length; index += 1){
    pairBuffer = "";
    if (!compact || index !== 0) {
      pairBuffer += generateNextLine(state, level);
    }
    objectKey = objectKeyList[index];
    objectValue = object[objectKey];
    if (!writeNode(state, level + 1, objectKey, true, true, true)) {
      continue; // Skip this pair because of invalid key.
    }
    explicitPair = state.tag !== null && state.tag !== "?" || state.dump && state.dump.length > 1024;
    if (explicitPair) {
      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
        pairBuffer += "?";
      } else {
        pairBuffer += "? ";
      }
    }
    pairBuffer += state.dump;
    if (explicitPair) {
      pairBuffer += generateNextLine(state, level);
    }
    if (!writeNode(state, level + 1, objectValue, true, explicitPair)) {
      continue; // Skip this pair because of invalid value.
    }
    if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
      pairBuffer += ":";
    } else {
      pairBuffer += ": ";
    }
    pairBuffer += state.dump;
    // Both key and value are valid.
    _result += pairBuffer;
  }
  state.tag = _tag;
  state.dump = _result || "{}"; // Empty mapping if no valid pairs.
}
function detectType(state, object, explicit = false) {
  const typeList = explicit ? state.explicitTypes : state.implicitTypes;
  let type;
  let style;
  let _result;
  for(let index = 0, length = typeList.length; index < length; index += 1){
    type = typeList[index];
    if ((type.instanceOf || type.predicate) && (!type.instanceOf || typeof object === "object" && object instanceof type.instanceOf) && (!type.predicate || type.predicate(object))) {
      state.tag = explicit ? type.tag : "?";
      if (type.represent) {
        style = state.styleMap[type.tag] || type.defaultStyle;
        if (_toString.call(type.represent) === "[object Function]") {
          _result = type.represent(object, style);
        } else if (hasOwn(type.represent, style)) {
          _result = type.represent[style](object, style);
        } else {
          throw new YAMLError(`!<${type.tag}> tag resolver accepts not "${style}" style`);
        }
        state.dump = _result;
      }
      return true;
    }
  }
  return false;
}
// Serializes `object` and writes it to global `result`.
// Returns true on success, or false on invalid object.
//
function writeNode(state, level, object, block, compact, iskey = false) {
  state.tag = null;
  state.dump = object;
  if (!detectType(state, object, false)) {
    detectType(state, object, true);
  }
  const type = _toString.call(state.dump);
  if (block) {
    block = state.flowLevel < 0 || state.flowLevel > level;
  }
  const objectOrArray = type === "[object Object]" || type === "[object Array]";
  let duplicateIndex = -1;
  let duplicate = false;
  if (objectOrArray) {
    duplicateIndex = state.duplicates.indexOf(object);
    duplicate = duplicateIndex !== -1;
  }
  if (state.tag !== null && state.tag !== "?" || duplicate || state.indent !== 2 && level > 0) {
    compact = false;
  }
  if (duplicate && state.usedDuplicates[duplicateIndex]) {
    state.dump = `*ref_${duplicateIndex}`;
  } else {
    if (objectOrArray && duplicate && !state.usedDuplicates[duplicateIndex]) {
      state.usedDuplicates[duplicateIndex] = true;
    }
    if (type === "[object Object]") {
      if (block && Object.keys(state.dump).length !== 0) {
        writeBlockMapping(state, level, state.dump, compact);
        if (duplicate) {
          state.dump = `&ref_${duplicateIndex}${state.dump}`;
        }
      } else {
        writeFlowMapping(state, level, state.dump);
        if (duplicate) {
          state.dump = `&ref_${duplicateIndex} ${state.dump}`;
        }
      }
    } else if (type === "[object Array]") {
      const arrayLevel = state.noArrayIndent && level > 0 ? level - 1 : level;
      if (block && state.dump.length !== 0) {
        writeBlockSequence(state, arrayLevel, state.dump, compact);
        if (duplicate) {
          state.dump = `&ref_${duplicateIndex}${state.dump}`;
        }
      } else {
        writeFlowSequence(state, arrayLevel, state.dump);
        if (duplicate) {
          state.dump = `&ref_${duplicateIndex} ${state.dump}`;
        }
      }
    } else if (type === "[object String]") {
      if (state.tag !== "?") {
        writeScalar(state, state.dump, level, iskey);
      }
    } else {
      if (state.skipInvalid) return false;
      throw new YAMLError(`unacceptable kind of an object to dump ${type}`);
    }
    if (state.tag !== null && state.tag !== "?") {
      state.dump = `!<${state.tag}> ${state.dump}`;
    }
  }
  return true;
}
function inspectNode(object, objects, duplicatesIndexes) {
  if (object !== null && typeof object === "object") {
    const index = objects.indexOf(object);
    if (index !== -1) {
      if (duplicatesIndexes.indexOf(index) === -1) {
        duplicatesIndexes.push(index);
      }
    } else {
      objects.push(object);
      if (Array.isArray(object)) {
        for(let idx = 0, length = object.length; idx < length; idx += 1){
          inspectNode(object[idx], objects, duplicatesIndexes);
        }
      } else {
        const objectKeyList = Object.keys(object);
        for(let idx = 0, length = objectKeyList.length; idx < length; idx += 1){
          inspectNode(object[objectKeyList[idx]], objects, duplicatesIndexes);
        }
      }
    }
  }
}
function getDuplicateReferences(object, state) {
  const objects = [], duplicatesIndexes = [];
  inspectNode(object, objects, duplicatesIndexes);
  const length = duplicatesIndexes.length;
  for(let index = 0; index < length; index += 1){
    state.duplicates.push(objects[duplicatesIndexes[index]]);
  }
  state.usedDuplicates = Array.from({
    length
  });
}
export function dump(input, options) {
  options = options || {};
  const state = new DumperState(options);
  if (!state.noRefs) getDuplicateReferences(input, state);
  if (writeNode(state, 0, input, true, true)) return `${state.dump}\n`;
  return "";
}
//# sourceMappingURL=data:application/json;base64,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