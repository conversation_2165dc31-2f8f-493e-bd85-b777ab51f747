// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { deferred } from "../async/deferred.ts";
/**
 * Merge multiple streams into a single one, not taking order into account.
 * If a stream ends before other ones, the other will continue adding data,
 * and the finished one will not add any more data.
 */ export function mergeReadableStreams(...streams) {
  const resolvePromises = streams.map(()=>deferred());
  return new ReadableStream({
    start (controller) {
      let mustClose = false;
      Promise.all(resolvePromises).then(()=>{
        controller.close();
      }).catch((error)=>{
        mustClose = true;
        controller.error(error);
      });
      for (const [index, stream] of streams.entries()){
        (async ()=>{
          try {
            for await (const data of stream){
              if (mustClose) {
                break;
              }
              controller.enqueue(data);
            }
            resolvePromises[index].resolve();
          } catch (error) {
            resolvePromises[index].reject(error);
          }
        })();
      }
    }
  });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3N0cmVhbXMvbWVyZ2VfcmVhZGFibGVfc3RyZWFtcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuXG5pbXBvcnQgeyBkZWZlcnJlZCB9IGZyb20gXCIuLi9hc3luYy9kZWZlcnJlZC50c1wiO1xuXG4vKipcbiAqIE1lcmdlIG11bHRpcGxlIHN0cmVhbXMgaW50byBhIHNpbmdsZSBvbmUsIG5vdCB0YWtpbmcgb3JkZXIgaW50byBhY2NvdW50LlxuICogSWYgYSBzdHJlYW0gZW5kcyBiZWZvcmUgb3RoZXIgb25lcywgdGhlIG90aGVyIHdpbGwgY29udGludWUgYWRkaW5nIGRhdGEsXG4gKiBhbmQgdGhlIGZpbmlzaGVkIG9uZSB3aWxsIG5vdCBhZGQgYW55IG1vcmUgZGF0YS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1lcmdlUmVhZGFibGVTdHJlYW1zPFQ+KFxuICAuLi5zdHJlYW1zOiBSZWFkYWJsZVN0cmVhbTxUPltdXG4pOiBSZWFkYWJsZVN0cmVhbTxUPiB7XG4gIGNvbnN0IHJlc29sdmVQcm9taXNlcyA9IHN0cmVhbXMubWFwKCgpID0+IGRlZmVycmVkPHZvaWQ+KCkpO1xuICByZXR1cm4gbmV3IFJlYWRhYmxlU3RyZWFtPFQ+KHtcbiAgICBzdGFydChjb250cm9sbGVyKSB7XG4gICAgICBsZXQgbXVzdENsb3NlID0gZmFsc2U7XG4gICAgICBQcm9taXNlLmFsbChyZXNvbHZlUHJvbWlzZXMpXG4gICAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgICBjb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICAgIH0pXG4gICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgICBtdXN0Q2xvc2UgPSB0cnVlO1xuICAgICAgICAgIGNvbnRyb2xsZXIuZXJyb3IoZXJyb3IpO1xuICAgICAgICB9KTtcbiAgICAgIGZvciAoY29uc3QgW2luZGV4LCBzdHJlYW1dIG9mIHN0cmVhbXMuZW50cmllcygpKSB7XG4gICAgICAgIChhc3luYyAoKSA9PiB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGZvciBhd2FpdCAoY29uc3QgZGF0YSBvZiBzdHJlYW0pIHtcbiAgICAgICAgICAgICAgaWYgKG11c3RDbG9zZSkge1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShkYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlc29sdmVQcm9taXNlc1tpbmRleF0ucmVzb2x2ZSgpO1xuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICByZXNvbHZlUHJvbWlzZXNbaW5kZXhdLnJlamVjdChlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9KSgpO1xuICAgICAgfVxuICAgIH0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUFTLFFBQVEsUUFBUSx1QkFBdUI7QUFFaEQ7Ozs7Q0FJQyxHQUNELE9BQU8sU0FBUyxxQkFDZCxHQUFHLE9BQTRCO0VBRS9CLE1BQU0sa0JBQWtCLFFBQVEsR0FBRyxDQUFDLElBQU07RUFDMUMsT0FBTyxJQUFJLGVBQWtCO0lBQzNCLE9BQU0sVUFBVTtNQUNkLElBQUksWUFBWTtNQUNoQixRQUFRLEdBQUcsQ0FBQyxpQkFDVCxJQUFJLENBQUM7UUFDSixXQUFXLEtBQUs7TUFDbEIsR0FDQyxLQUFLLENBQUMsQ0FBQztRQUNOLFlBQVk7UUFDWixXQUFXLEtBQUssQ0FBQztNQUNuQjtNQUNGLEtBQUssTUFBTSxDQUFDLE9BQU8sT0FBTyxJQUFJLFFBQVEsT0FBTyxHQUFJO1FBQzlDLENBQUE7VUFDQyxJQUFJO1lBQ0YsV0FBVyxNQUFNLFFBQVEsT0FBUTtjQUMvQixJQUFJLFdBQVc7Z0JBQ2I7Y0FDRjtjQUNBLFdBQVcsT0FBTyxDQUFDO1lBQ3JCO1lBQ0EsZUFBZSxDQUFDLE1BQU0sQ0FBQyxPQUFPO1VBQ2hDLEVBQUUsT0FBTyxPQUFPO1lBQ2QsZUFBZSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUM7VUFDaEM7UUFDRixDQUFBO01BQ0Y7SUFDRjtFQUNGO0FBQ0YifQ==