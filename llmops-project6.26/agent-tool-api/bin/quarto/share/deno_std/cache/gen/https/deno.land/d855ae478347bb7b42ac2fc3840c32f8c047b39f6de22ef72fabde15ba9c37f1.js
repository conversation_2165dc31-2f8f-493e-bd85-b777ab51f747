// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new record with all entries of the given record except the ones that
 * have a key that does not match the given predicate.
 *
 * @example
 * ```ts
 * import { filterKeys } from "https://deno.land/std@$STD_VERSION/collections/filter_keys.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const menu = {
 *   "Salad": 11,
 *   "Soup": 8,
 *   "Pasta": 13,
 * };
 * const menuWithoutSalad = filterKeys(menu, (it) => it !== "Salad");
 *
 * assertEquals(
 *   menuWithoutSalad,
 *   {
 *     "Soup": 8,
 *     "Pasta": 13,
 *   },
 * );
 * ```
 */ export function filterKeys(record, predicate) {
  const ret = {};
  const keys = Object.keys(record);
  for (const key of keys){
    if (predicate(key)) {
      ret[key] = record[key];
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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