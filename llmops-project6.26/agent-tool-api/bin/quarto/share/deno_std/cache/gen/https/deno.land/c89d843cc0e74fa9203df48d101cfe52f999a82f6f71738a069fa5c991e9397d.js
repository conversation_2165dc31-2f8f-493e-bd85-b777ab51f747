// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Options for joinToString
 */ /**
 * Transforms the elements in the given array to strings using the given
 * selector. Joins the produced strings into one using the given `separator`
 * and applying the given `prefix` and `suffix` to the whole string afterwards.
 * If the array could be huge, you can specify a non-negative value of `limit`,
 * in which case only the first `limit` elements will be appended, followed by
 * the `truncated` string. Returns the resulting string.
 *
 * @example
 * ```ts
 * import { joinToString } from "https://deno.land/std@$STD_VERSION/collections/join_to_string.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const users = [
 *   { name: "<PERSON>" },
 *   { name: "<PERSON>" },
 *   { name: "<PERSON>" },
 * ];
 *
 * const message = joinToString(users, (it) => it.name, {
 *   suffix: " are winners",
 *   prefix: "result: ",
 *   separator: " and ",
 *   limit: 1,
 *   truncated: "others",
 * });
 *
 * assertEquals(message, "result: <PERSON> and others are winners");
 * ```
 */ export function joinToString(array, selector, { separator = ",", prefix = "", suffix = "", limit = -1, truncated = "..." } = {}) {
  let result = "";
  let index = -1;
  for (const el of array){
    index++;
    if (index > 0) {
      result += separator;
    }
    if (limit > -1 && index >= limit) {
      result += truncated;
      break;
    }
    result += selector(el);
  }
  result = prefix + result + suffix;
  return result;
}
//# sourceMappingURL=data:application/json;base64,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