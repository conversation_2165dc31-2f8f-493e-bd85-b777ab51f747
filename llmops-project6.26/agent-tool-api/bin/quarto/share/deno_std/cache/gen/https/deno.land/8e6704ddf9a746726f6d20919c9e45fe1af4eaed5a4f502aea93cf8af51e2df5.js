// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { dirname } from "../path/dirname.ts";
import { ensureDir, ensureDirSync } from "./ensure_dir.ts";
import { toPathString } from "./_util.ts";
/**
 * Ensures that the hard link exists.
 * If the directory structure does not exist, it is created.
 *
 * @example
 * ```ts
 * import { ensureSymlink } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * ensureSymlink("./folder/targetFile.dat", "./folder/targetFile.link.dat"); // returns promise
 * ```
 *
 * @param src the source file path. Directory hard links are not allowed.
 * @param dest the destination link path
 */ export async function ensureLink(src, dest) {
  dest = toPathString(dest);
  await ensureDir(dirname(dest));
  await Deno.link(toPathString(src), dest);
}
/**
 * Ensures that the hard link exists.
 * If the directory structure does not exist, it is created.
 *
 * @example
 * ```ts
 * import { ensureSymlinkSync } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * ensureSymlinkSync("./folder/targetFile.dat", "./folder/targetFile.link.dat"); // void
 * ```
 *
 * @param src the source file path. Directory hard links are not allowed.
 * @param dest the destination link path
 */ export function ensureLinkSync(src, dest) {
  dest = toPathString(dest);
  ensureDirSync(dirname(dest));
  Deno.linkSync(toPathString(src), dest);
}
//# sourceMappingURL=data:application/json;base64,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