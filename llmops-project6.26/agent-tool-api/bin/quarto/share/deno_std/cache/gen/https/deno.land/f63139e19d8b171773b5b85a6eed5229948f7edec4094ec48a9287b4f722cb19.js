// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { MAP_FORMAT_TO_EXTRACTOR_RX, MAP_FORMAT_TO_RECOGNIZER_RX } from "./_formats.ts";
function _extract(str, rx, parse) {
  const match = rx.exec(str);
  if (!match || match.index !== 0) {
    throw new TypeError("Unexpected end of input");
  }
  const frontMatter = match.at(-1)?.replace(/^\s+|\s+$/g, "") || "";
  const attrs = parse(frontMatter);
  const body = str.replace(match[0], "");
  return {
    frontMatter,
    body,
    attrs
  };
}
/**
 * Recognizes the format of the front matter in a string. Supports YAML, TOML and JSON.
 *
 * @param str String to recognize.
 * @param formats A list of formats to recognize. Defaults to all supported formats.
 *
 * ```ts
 * import { recognize } from "https://deno.land/std@$STD_VERSION/front_matter/mod.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * assertEquals(recognize("---\ntitle: Three dashes marks the spot\n---\n"), "yaml");
 * assertEquals(recognize("---toml\ntitle = 'Three dashes followed by format marks the spot'\n---\n"), "toml");
 * assertEquals(recognize("---json\n{\"title\": \"Three dashes followed by format marks the spot\"}\n---\n"), "json");
 * assertEquals(recognize("---xml\n<title>Three dashes marks the spot</title>\n---\n"), "unknown");
 *
 * assertEquals(recognize("---json\n<title>Three dashes marks the spot</title>\n---\n", ["yaml"]), "unknown");
 */ function recognize(str, formats) {
  if (!formats) {
    formats = Object.keys(MAP_FORMAT_TO_RECOGNIZER_RX);
  }
  const [firstLine] = str.split(/(\r?\n)/);
  for (const format of formats){
    if (format === "unknown") {
      continue;
    }
    if (MAP_FORMAT_TO_RECOGNIZER_RX[format].test(firstLine)) {
      return format;
    }
  }
  return "unknown";
}
/**
 * Factory that creates a function that extracts front matter from a string with the given parsers.
 * Supports YAML, TOML and JSON.
 *
 * @param formats A descriptor containing Format-parser pairs to use for each format.
 * @returns A function that extracts front matter from a string with the given parsers.
 *
 * ```ts
 * import { createExtractor, Parser } from "https://deno.land/std@$STD_VERSION/front_matter/mod.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 * import { parse as parseYAML } from "https://deno.land/std@$STD_VERSION/yaml/parse.ts";
 * import { parse as parseTOML } from "https://deno.land/std@$STD_VERSION/toml/parse.ts";
 * const extractYAML = createExtractor({ yaml: parseYAML as Parser });
 * const extractTOML = createExtractor({ toml: parseTOML as Parser });
 * const extractJSON = createExtractor({ json: JSON.parse as Parser });
 * const extractYAMLOrJSON = createExtractor({
 *     yaml: parseYAML as Parser,
 *     json: JSON.parse as Parser,
 * });
 *
 * let { attrs, body, frontMatter } = extractYAML<{ title: string }>("---\ntitle: Three dashes marks the spot\n---\nferret");
 * assertEquals(attrs.title, "Three dashes marks the spot");
 * assertEquals(body, "ferret");
 * assertEquals(frontMatter, "title: Three dashes marks the spot");
 *
 * ({ attrs, body, frontMatter } = extractTOML<{ title: string }>("---toml\ntitle = 'Three dashes followed by format marks the spot'\n---\n"));
 * assertEquals(attrs.title, "Three dashes followed by format marks the spot");
 * assertEquals(body, "");
 * assertEquals(frontMatter, "title = 'Three dashes followed by format marks the spot'");
 *
 * ({ attrs, body, frontMatter } = extractJSON<{ title: string }>("---json\n{\"title\": \"Three dashes followed by format marks the spot\"}\n---\ngoat"));
 * assertEquals(attrs.title, "Three dashes followed by format marks the spot");
 * assertEquals(body, "goat");
 * assertEquals(frontMatter, "{\"title\": \"Three dashes followed by format marks the spot\"}");
 *
 * ({ attrs, body, frontMatter } = extractYAMLOrJSON<{ title: string }>("---\ntitle: Three dashes marks the spot\n---\nferret"));
 * assertEquals(attrs.title, "Three dashes marks the spot");
 * assertEquals(body, "ferret");
 * assertEquals(frontMatter, "title: Three dashes marks the spot");
 *
 * ({ attrs, body, frontMatter } = extractYAMLOrJSON<{ title: string }>("---json\n{\"title\": \"Three dashes followed by format marks the spot\"}\n---\ngoat"));
 * assertEquals(attrs.title, "Three dashes followed by format marks the spot");
 * assertEquals(body, "goat");
 * assertEquals(frontMatter, "{\"title\": \"Three dashes followed by format marks the spot\"}");
 * ```
 */ export function createExtractor(formats) {
  const formatKeys = Object.keys(formats);
  return function extract(str) {
    const format = recognize(str, formatKeys);
    const parser = formats[format];
    if (format === "unknown" || !parser) {
      throw new TypeError(`Unsupported front matter format`);
    }
    return _extract(str, MAP_FORMAT_TO_EXTRACTOR_RX[format], parser);
  };
}
//# sourceMappingURL=data:application/json;base64,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