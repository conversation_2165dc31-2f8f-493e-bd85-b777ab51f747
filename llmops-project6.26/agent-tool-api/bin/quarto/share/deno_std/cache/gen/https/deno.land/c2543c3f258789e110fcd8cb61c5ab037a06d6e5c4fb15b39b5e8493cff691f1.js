// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { gte } from "./gte.ts";
import { lte } from "./lte.ts";
/**
 * Returns true if the range of possible versions intersects with the other comparators set of possible versions
 * @param c0 The left side comparator
 * @param c1 The right side comparator
 * @returns True if any part of the comparators intersect
 */ export function comparatorIntersects(c0, c1) {
  const l0 = c0.min;
  const l1 = c0.max;
  const r0 = c1.min;
  const r1 = c1.max;
  // We calculate the min and max ranges of both comparators.
  // The minimum min is 0.0.0, the maximum max is ANY.
  //
  // Comparators with equality operators have the same min and max.
  //
  // We then check to see if the min's of either range falls within the span of the other range.
  //
  // A couple of intersection examples:
  // ```
  // l0 ---- l1
  //     r0 ---- r1
  // ```
  // ```
  //     l0 ---- l1
  // r0 ---- r1
  // ```
  // ```
  // l0 ------ l1
  //    r0--r1
  // ```
  // ```
  // l0 - l1
  // r0 - r1
  // ```
  //
  // non-intersection example
  // ```
  // l0 -- l1
  //          r0 -- r1
  // ```
  return gte(l0, r0) && lte(l0, r1) || gte(r0, l0) && lte(r0, l1);
}
//# sourceMappingURL=data:application/json;base64,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