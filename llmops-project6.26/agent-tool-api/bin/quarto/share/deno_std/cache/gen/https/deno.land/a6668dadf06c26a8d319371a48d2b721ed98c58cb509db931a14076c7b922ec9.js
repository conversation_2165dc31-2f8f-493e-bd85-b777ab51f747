// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { INVALID } from "./constants.ts";
import { sort } from "./sort.ts";
import { testRange } from "./test_range.ts";
/**
 * The minimum valid SemVer for a given range or INVALID
 * @param range The range to calculate the min for
 * @returns A valid SemVer or INVALID
 */ export function rangeMin(range) {
  // For or's, you take the smallest min
  //[ [1 and 2] or [2 and 3] ] = [ 2 or 3 ] = 2
  return sort(range.ranges.map((r)=>sort(r.filter((c)=>testRange(c.min, range)).map((c)=>c.min)).pop()).filter((v)=>v)).shift() ?? INVALID;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9yYW5nZV9taW4udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbmltcG9ydCB7IElOVkFMSUQgfSBmcm9tIFwiLi9jb25zdGFudHMudHNcIjtcbmltcG9ydCB7IHNvcnQgfSBmcm9tIFwiLi9zb3J0LnRzXCI7XG5pbXBvcnQgdHlwZSB7IFNlbVZlciwgU2VtVmVyUmFuZ2UgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgdGVzdFJhbmdlIH0gZnJvbSBcIi4vdGVzdF9yYW5nZS50c1wiO1xuXG4vKipcbiAqIFRoZSBtaW5pbXVtIHZhbGlkIFNlbVZlciBmb3IgYSBnaXZlbiByYW5nZSBvciBJTlZBTElEXG4gKiBAcGFyYW0gcmFuZ2UgVGhlIHJhbmdlIHRvIGNhbGN1bGF0ZSB0aGUgbWluIGZvclxuICogQHJldHVybnMgQSB2YWxpZCBTZW1WZXIgb3IgSU5WQUxJRFxuICovXG5leHBvcnQgZnVuY3Rpb24gcmFuZ2VNaW4ocmFuZ2U6IFNlbVZlclJhbmdlKTogU2VtVmVyIHsgLy8gRm9yIGFuZCdzLCB5b3UgdGFrZSB0aGUgYmlnZ2VzdCBtaW5cbiAgLy8gRm9yIG9yJ3MsIHlvdSB0YWtlIHRoZSBzbWFsbGVzdCBtaW5cbiAgLy9bIFsxIGFuZCAyXSBvciBbMiBhbmQgM10gXSA9IFsgMiBvciAzIF0gPSAyXG4gIHJldHVybiBzb3J0KFxuICAgIHJhbmdlLnJhbmdlcy5tYXAoKHIpID0+XG4gICAgICBzb3J0KHIuZmlsdGVyKChjKSA9PiB0ZXN0UmFuZ2UoYy5taW4sIHJhbmdlKSkubWFwKChjKSA9PiBjLm1pbikpLnBvcCgpIVxuICAgICkuZmlsdGVyKCh2KSA9PiB2KSxcbiAgKS5zaGlmdCgpID8/IElOVkFMSUQ7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLFNBQVMsT0FBTyxRQUFRLGlCQUFpQjtBQUN6QyxTQUFTLElBQUksUUFBUSxZQUFZO0FBRWpDLFNBQVMsU0FBUyxRQUFRLGtCQUFrQjtBQUU1Qzs7OztDQUlDLEdBQ0QsT0FBTyxTQUFTLFNBQVMsS0FBa0I7RUFDekMsc0NBQXNDO0VBQ3RDLDZDQUE2QztFQUM3QyxPQUFPLEtBQ0wsTUFBTSxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFDaEIsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDLElBQU0sVUFBVSxFQUFFLEdBQUcsRUFBRSxRQUFRLEdBQUcsQ0FBQyxDQUFDLElBQU0sRUFBRSxHQUFHLEdBQUcsR0FBRyxJQUNwRSxNQUFNLENBQUMsQ0FBQyxJQUFNLElBQ2hCLEtBQUssTUFBTTtBQUNmIn0=