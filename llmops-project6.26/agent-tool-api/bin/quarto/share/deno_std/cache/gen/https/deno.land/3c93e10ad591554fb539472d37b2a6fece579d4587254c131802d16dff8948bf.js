// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { filterInPlace } from "./_utils.ts";
const { hasOwn } = Object;
export function deepMerge(record, other, options) {
  return deepMergeInternal(record, other, new Set(), options);
}
function deepMergeInternal(record, other, seen, options) {
  const result = {};
  const keys = new Set([
    ...getKeys(record),
    ...getKeys(other)
  ]);
  // Iterate through each key of other object and use correct merging strategy
  for (const key of keys){
    // Skip to prevent Object.prototype.__proto__ accessor property calls on non-Deno platforms
    if (key === "__proto__") {
      continue;
    }
    const a = record[key];
    if (!hasOwn(other, key)) {
      result[key] = a;
      continue;
    }
    const b = other[key];
    if (isNonNullObject(a) && isNonNullObject(b) && !seen.has(a) && !seen.has(b)) {
      seen.add(a);
      seen.add(b);
      result[key] = mergeObjects(a, b, seen, options);
      continue;
    }
    // Override value
    result[key] = b;
  }
  return result;
}
function mergeObjects(left, right, seen, options = {
  arrays: "merge",
  sets: "merge",
  maps: "merge"
}) {
  // Recursively merge mergeable objects
  if (isMergeable(left) && isMergeable(right)) {
    return deepMergeInternal(left, right, seen, options);
  }
  if (isIterable(left) && isIterable(right)) {
    // Handle arrays
    if (Array.isArray(left) && Array.isArray(right)) {
      if (options.arrays === "merge") {
        return left.concat(right);
      }
      return right;
    }
    // Handle maps
    if (left instanceof Map && right instanceof Map) {
      if (options.maps === "merge") {
        return new Map([
          ...left,
          ...right
        ]);
      }
      return right;
    }
    // Handle sets
    if (left instanceof Set && right instanceof Set) {
      if (options.sets === "merge") {
        return new Set([
          ...left,
          ...right
        ]);
      }
      return right;
    }
  }
  return right;
}
/**
 * Test whether a value is mergeable or not
 * Builtins that look like objects, null and user defined classes
 * are not considered mergeable (it means that reference will be copied)
 */ function isMergeable(value) {
  return Object.getPrototypeOf(value) === Object.prototype;
}
function isIterable(value) {
  return typeof value[Symbol.iterator] === "function";
}
function isNonNullObject(value) {
  return value !== null && typeof value === "object";
}
function getKeys(record) {
  const ret = Object.getOwnPropertySymbols(record);
  filterInPlace(ret, (key)=>Object.prototype.propertyIsEnumerable.call(record, key));
  ret.push(...Object.keys(record));
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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