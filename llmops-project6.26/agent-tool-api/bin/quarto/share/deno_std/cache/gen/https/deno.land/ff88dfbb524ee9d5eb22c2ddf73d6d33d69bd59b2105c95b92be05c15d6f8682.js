// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns all elements in the given array that produce a distinct value using
 * the given selector, preserving order by first occurrence.
 *
 * @example
 * ```ts
 * import { distinctBy } from "https://deno.land/std@$STD_VERSION/collections/distinct_by.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
 * const exampleNamesByFirstLetter = distinctBy(names, (it) => it.charAt(0));
 *
 * assertEquals(exampleNamesByFirstLetter, ["<PERSON>", "<PERSON>"]);
 * ```
 */ export function distinctBy(array, selector) {
  const selectedValues = new Set();
  const ret = [];
  for (const element of array){
    const currentSelectedValue = selector(element);
    if (!selectedValues.has(currentSelectedValue)) {
      selectedValues.add(currentSelectedValue);
      ret.push(element);
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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