// Copyright <PERSON> and Contributors. All rights reserved. ISC license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * The semantic version parser.
 *
 * Adapted directly from [semver](https://github.com/npm/node-semver).
 *
 * ## Versions
 *
 * A "version" is described by the `v2.0.0` specification found at
 * <https://semver.org>.
 *
 * A leading `"="` or `"v"` character is stripped off and ignored.
 *
 * ## Format
 *
 * Semantic versions can be formatted as strings, by default they
 * are formatted as `full`. Below is a diagram showing the various
 * formatting options.
 *
 * ```
 *           ┌───── full
 *       ┌───┴───┐
 *       ├───────── release
 *   ┌───┴───┐   │
 *   ├───────────── primary
 * ┌─┴─┐     │   │
 * 1.2.3-pre.1+b.1
 * │ │ │ └─┬─┘ └┬┘
 * │ │ │   │    └── build
 * │ │ │   └─────── pre
 * │ │ └─────────── patch
 * │ └───────────── minor
 * └─────────────── major
 * ```
 *
 * ## Ranges
 *
 * A `version range` is a set of `comparators` which specify versions that satisfy
 * the range.
 *
 * A `comparator` is composed of an `operator` and a `version`. The set of
 * primitive `operators` is:
 *
 * - `<` Less than
 * - `<=` Less than or equal to
 * - `>` Greater than
 * - `>=` Greater than or equal to
 * - `=` Equal. If no operator is specified, then equality is assumed, so this
 *   operator is optional, but MAY be included.
 *
 * For example, the comparator `>=1.2.7` would match the versions `1.2.7`, `1.2.8`,
 * `2.5.3`, and `1.3.9`, but not the versions `1.2.6` or `1.1.0`.
 *
 * Comparators can be joined by whitespace to form a `comparator set`, which is
 * satisfied by the **intersection** of all of the comparators it includes.
 *
 * A range is composed of one or more comparator sets, joined by `||`. A version
 * matches a range if and only if every comparator in at least one of the
 * `||`-separated comparator sets is satisfied by the version.
 *
 * For example, the range `>=1.2.7 <1.3.0` would match the versions `1.2.7`,
 * `1.2.8`, and `1.2.99`, but not the versions `1.2.6`, `1.3.0`, or `1.1.0`.
 *
 * The range `1.2.7 || >=1.2.9 <2.0.0` would match the versions `1.2.7`, `1.2.9`,
 * and `1.4.6`, but not the versions `1.2.8` or `2.0.0`.
 *
 * ### Prerelease Tags
 *
 * If a version has a prerelease tag (for example, `1.2.3-alpha.3`) then it will
 * only be allowed to satisfy comparator sets if at least one comparator with the
 * same `[major, minor, patch]` tuple also has a prerelease tag.
 *
 * For example, the range `>1.2.3-alpha.3` would be allowed to match the version
 * `1.2.3-alpha.7`, but it would _not_ be satisfied by `3.4.5-alpha.9`, even though
 * `3.4.5-alpha.9` is technically "greater than" `1.2.3-alpha.3` according to the
 * SemVer sort rules. The version range only accepts prerelease tags on the `1.2.3`
 * version. The version `3.4.5` _would_ satisfy the range, because it does not have
 * a prerelease flag, and `3.4.5` is greater than `1.2.3-alpha.7`.
 *
 * The purpose for this behavior is twofold. First, prerelease versions frequently
 * are updated very quickly, and contain many breaking changes that are (by the
 * author"s design) not yet fit for public consumption. Therefore, by default, they
 * are excluded from range matching semantics.
 *
 * Second, a user who has opted into using a prerelease version has clearly
 * indicated the intent to use _that specific_ set of alpha/beta/rc versions. By
 * including a prerelease tag in the range, the user is indicating that they are
 * aware of the risk. However, it is still not appropriate to assume that they have
 * opted into taking a similar risk on the _next_ set of prerelease versions.
 *
 * Note that this behavior can be suppressed (treating all prerelease versions as
 * if they were normal versions, for the purpose of range matching) by setting the
 * `includePrerelease` flag on the options object to any [functions](#functions)
 * that do range matching.
 *
 * #### Prerelease Identifiers
 *
 * The method `.increment` takes an additional `identifier` string argument that
 * will append the value of the string as a prerelease identifier:
 *
 * ```javascript
 * semver.increment(parse("1.2.3"), "prerelease", "beta");
 * // "1.2.4-beta.0"
 * ```
 *
 * ### Build Metadata
 *
 * Build metadata is `.` delimited alpha-numeric string.
 * When parsing a version it is retained on the `build: string[]` field
 * of the semver instance. When incrementing there is an additional parameter that
 * can set the build metadata on the semver instance.
 *
 * ### Advanced Range Syntax
 *
 * Advanced range syntax desugars to primitive comparators in deterministic ways.
 *
 * Advanced ranges may be combined in the same way as primitive comparators using
 * white space or `||`.
 *
 * #### Hyphen Ranges `X.Y.Z - A.B.C`
 *
 * Specifies an inclusive set.
 *
 * - `1.2.3 - 2.3.4` := `>=1.2.3 <=2.3.4`
 *
 * If a partial version is provided as the first version in the inclusive range,
 * then the missing pieces are replaced with zeroes.
 *
 * - `1.2 - 2.3.4` := `>=1.2.0 <=2.3.4`
 *
 * If a partial version is provided as the second version in the inclusive range,
 * then all versions that start with the supplied parts of the tuple are accepted,
 * but nothing that would be greater than the provided tuple parts.
 *
 * - `1.2.3 - 2.3` := `>=1.2.3 <2.4.0`
 * - `1.2.3 - 2` := `>=1.2.3 <3.0.0`
 *
 * #### X-Ranges `1.2.x` `1.X` `1.2.*` `*`
 *
 * Any of `X`, `x`, or `*` may be used to "stand in" for one of the numeric values
 * in the `[major, minor, patch]` tuple.
 *
 * - `*` := `>=0.0.0` (Any version satisfies)
 * - `1.x` := `>=1.0.0 <2.0.0` (Matching major version)
 * - `1.2.x` := `>=1.2.0 <1.3.0` (Matching major and minor versions)
 *
 * A partial version range is treated as an X-Range, so the special character is in
 * fact optional.
 *
 * - `""` (empty string) := `*` := `>=0.0.0`
 * - `1` := `1.x.x` := `>=1.0.0 <2.0.0`
 * - `1.2` := `1.2.x` := `>=1.2.0 <1.3.0`
 *
 * #### Tilde Ranges `~1.2.3` `~1.2` `~1`
 *
 * Allows patch-level changes if a minor version is specified on the comparator.
 * Allows minor-level changes if not.
 *
 * - `~1.2.3` := `>=1.2.3 <1.(2+1).0` := `>=1.2.3 <1.3.0`
 * - `~1.2` := `>=1.2.0 <1.(2+1).0` := `>=1.2.0 <1.3.0` (Same as `1.2.x`)
 * - `~1` := `>=1.0.0 <(1+1).0.0` := `>=1.0.0 <2.0.0` (Same as `1.x`)
 * - `~0.2.3` := `>=0.2.3 <0.(2+1).0` := `>=0.2.3 <0.3.0`
 * - `~0.2` := `>=0.2.0 <0.(2+1).0` := `>=0.2.0 <0.3.0` (Same as `0.2.x`)
 * - `~0` := `>=0.0.0 <(0+1).0.0` := `>=0.0.0 <1.0.0` (Same as `0.x`)
 * - `~1.2.3-beta.2` := `>=1.2.3-beta.2 <1.3.0` Note that prereleases in the
 *   `1.2.3` version will be allowed, if they are greater than or equal to
 *   `beta.2`. So, `1.2.3-beta.4` would be allowed, but `1.2.4-beta.2` would not,
 *   because it is a prerelease of a different `[major, minor, patch]` tuple.
 *
 * #### Caret Ranges `^1.2.3` `^0.2.5` `^0.0.4`
 *
 * Allows changes that do not modify the left-most non-zero element in the
 * `[major, minor, patch]` tuple. In other words, this allows patch and minor
 * updates for versions `1.0.0` and above, patch updates for versions
 * `0.X >=0.1.0`, and _no_ updates for versions `0.0.X`.
 *
 * Many authors treat a `0.x` version as if the `x` were the major
 * "breaking-change" indicator.
 *
 * Caret ranges are ideal when an author may make breaking changes between `0.2.4`
 * and `0.3.0` releases, which is a common practice. However, it presumes that
 * there will _not_ be breaking changes between `0.2.4` and `0.2.5`. It allows for
 * changes that are presumed to be additive (but non-breaking), according to
 * commonly observed practices.
 *
 * - `^1.2.3` := `>=1.2.3 <2.0.0`
 * - `^0.2.3` := `>=0.2.3 <0.3.0`
 * - `^0.0.3` := `>=0.0.3 <0.0.4`
 * - `^1.2.3-beta.2` := `>=1.2.3-beta.2 <2.0.0` Note that prereleases in the
 *   `1.2.3` version will be allowed, if they are greater than or equal to
 *   `beta.2`. So, `1.2.3-beta.4` would be allowed, but `1.2.4-beta.2` would not,
 *   because it is a prerelease of a different `[major, minor, patch]` tuple.
 * - `^0.0.3-beta` := `>=0.0.3-beta <0.0.4` Note that prereleases in the `0.0.3`
 *   version _only_ will be allowed, if they are greater than or equal to `beta`.
 *   So, `0.0.3-pr.2` would be allowed.
 *
 * When parsing caret ranges, a missing `patch` value desugars to the number `0`,
 * but will allow flexibility within that value, even if the major and minor
 * versions are both `0`.
 *
 * - `^1.2.x` := `>=1.2.0 <2.0.0`
 * - `^0.0.x` := `>=0.0.0 <0.1.0`
 * - `^0.0` := `>=0.0.0 <0.1.0`
 *
 * A missing `minor` and `patch` values will desugar to zero, but also allow
 * flexibility within those values, even if the major version is zero.
 *
 * - `^1.x` := `>=1.0.0 <2.0.0`
 * - `^0.x` := `>=0.0.0 <1.0.0`
 *
 * ### Range Grammar
 *
 * Putting all this together, here is a Backus-Naur grammar for ranges, for the
 * benefit of parser authors:
 *
 * ```bnf
 * range-set  ::= range ( logical-or range ) *
 * logical-or ::= ( " " ) * "||" ( " " ) *
 * range      ::= hyphen | simple ( " " simple ) * | ""
 * hyphen     ::= partial " - " partial
 * simple     ::= primitive | partial | tilde | caret
 * primitive  ::= ( "<" | ">" | ">=" | "<=" | "=" ) partial
 * partial    ::= xr ( "." xr ( "." xr qualifier ? )? )?
 * xr         ::= "x" | "X" | "*" | nr
 * nr         ::= "0" | ["1"-"9"] ( ["0"-"9"] ) *
 * tilde      ::= "~" partial
 * caret      ::= "^" partial
 * qualifier  ::= ( "-" pre )? ( "+" build )?
 * pre        ::= parts
 * build      ::= parts
 * parts      ::= part ( "." part ) *
 * part       ::= nr | [-0-9A-Za-z]+
 * ```
 *
 * Note that, since ranges may be non-contiguous, a version might not be greater
 * than a range, less than a range, _or_ satisfy a range! For example, the range
 * `1.2 <1.2.9 || >2.0.0` would have a hole from `1.2.9` until `2.0.0`, so the
 * version `1.2.10` would not be greater than the range (because `2.0.1` satisfies,
 * which is higher), nor less than the range (since `1.2.8` satisfies, which is
 * lower), and it also does not satisfy the range.
 *
 * If you want to know if a version satisfies or does not satisfy a range, use the
 * {@linkcode satisfies} function.
 *
 * This module is browser compatible.
 *
 * @example
 * ```ts
 * import {
 *   parse,
 *   parseComparator,
 *   parseRange,
 *   gt,
 *   lt,
 *   format
 * } from "https://deno.land/std@$STD_VERSION/semver/mod.ts";
 *
 * const semver = parse("1.2.3");
 * const range = parseRange("1.x || >=2.5.0 || 5.0.0 - 7.2.3");
 *
 * const s0 = parse("1.2.3");
 * const s1 = parse("9.8.7");
 * gt(s0, s1); // false
 * lt(s0, s1); // true
 *
 * format(semver) // "1.2.3"
 * ```
 *
 * @module
 */ export * from "./cmp.ts";
export * from "./comparator_format.ts";
export * from "./comparator_intersects.ts";
export * from "./comparator_max.ts";
export * from "./comparator_min.ts";
export * from "./compare_build.ts";
export * from "./compare.ts";
export * from "./constants.ts";
export * from "./difference.ts";
export * from "./eq.ts";
export * from "./format.ts";
export * from "./gt.ts";
export * from "./gte.ts";
export * from "./gtr.ts";
export * from "./test_comparator.ts";
export * from "./test_range.ts";
export * from "./increment.ts";
export * from "./is_semver_comparator.ts";
export * from "./is_semver_range.ts";
export * from "./is_semver.ts";
export * from "./lt.ts";
export * from "./lte.ts";
export * from "./ltr.ts";
export * from "./max_satisfying.ts";
export * from "./min_satisfying.ts";
export * from "./neq.ts";
export * from "./outside.ts";
export * from "./parse_comparator.ts";
export * from "./parse_range.ts";
export * from "./parse.ts";
export * from "./range_format.ts";
export * from "./range_intersects.ts";
export * from "./range_max.ts";
export * from "./range_min.ts";
export * from "./rcompare.ts";
export * from "./rsort.ts";
export * from "./sort.ts";
export * from "./types.ts";
export * from "./lte.ts";
export * from "./lte.ts";
export const SEMVER_SPEC_VERSION = "2.0.0";
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9tb2QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IElzYWFjIFouIFNjaGx1ZXRlciBhbmQgQ29udHJpYnV0b3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBJU0MgbGljZW5zZS5cbi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogVGhlIHNlbWFudGljIHZlcnNpb24gcGFyc2VyLlxuICpcbiAqIEFkYXB0ZWQgZGlyZWN0bHkgZnJvbSBbc2VtdmVyXShodHRwczovL2dpdGh1Yi5jb20vbnBtL25vZGUtc2VtdmVyKS5cbiAqXG4gKiAjIyBWZXJzaW9uc1xuICpcbiAqIEEgXCJ2ZXJzaW9uXCIgaXMgZGVzY3JpYmVkIGJ5IHRoZSBgdjIuMC4wYCBzcGVjaWZpY2F0aW9uIGZvdW5kIGF0XG4gKiA8aHR0cHM6Ly9zZW12ZXIub3JnPi5cbiAqXG4gKiBBIGxlYWRpbmcgYFwiPVwiYCBvciBgXCJ2XCJgIGNoYXJhY3RlciBpcyBzdHJpcHBlZCBvZmYgYW5kIGlnbm9yZWQuXG4gKlxuICogIyMgRm9ybWF0XG4gKlxuICogU2VtYW50aWMgdmVyc2lvbnMgY2FuIGJlIGZvcm1hdHRlZCBhcyBzdHJpbmdzLCBieSBkZWZhdWx0IHRoZXlcbiAqIGFyZSBmb3JtYXR0ZWQgYXMgYGZ1bGxgLiBCZWxvdyBpcyBhIGRpYWdyYW0gc2hvd2luZyB0aGUgdmFyaW91c1xuICogZm9ybWF0dGluZyBvcHRpb25zLlxuICpcbiAqIGBgYFxuICogICAgICAgICAgIOKUjOKUgOKUgOKUgOKUgOKUgCBmdWxsXG4gKiAgICAgICDilIzilIDilIDilIDilLTilIDilIDilIDilJBcbiAqICAgICAgIOKUnOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgCByZWxlYXNlXG4gKiAgIOKUjOKUgOKUgOKUgOKUtOKUgOKUgOKUgOKUkCAgIOKUglxuICogICDilJzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIAgcHJpbWFyeVxuICog4pSM4pSA4pS04pSA4pSQICAgICDilIIgICDilIJcbiAqIDEuMi4zLXByZS4xK2IuMVxuICog4pSCIOKUgiDilIIg4pSU4pSA4pSs4pSA4pSYIOKUlOKUrOKUmFxuICog4pSCIOKUgiDilIIgICDilIIgICAg4pSU4pSA4pSAIGJ1aWxkXG4gKiDilIIg4pSCIOKUgiAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgCBwcmVcbiAqIOKUgiDilIIg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSAIHBhdGNoXG4gKiDilIIg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSAIG1pbm9yXG4gKiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIAgbWFqb3JcbiAqIGBgYFxuICpcbiAqICMjIFJhbmdlc1xuICpcbiAqIEEgYHZlcnNpb24gcmFuZ2VgIGlzIGEgc2V0IG9mIGBjb21wYXJhdG9yc2Agd2hpY2ggc3BlY2lmeSB2ZXJzaW9ucyB0aGF0IHNhdGlzZnlcbiAqIHRoZSByYW5nZS5cbiAqXG4gKiBBIGBjb21wYXJhdG9yYCBpcyBjb21wb3NlZCBvZiBhbiBgb3BlcmF0b3JgIGFuZCBhIGB2ZXJzaW9uYC4gVGhlIHNldCBvZlxuICogcHJpbWl0aXZlIGBvcGVyYXRvcnNgIGlzOlxuICpcbiAqIC0gYDxgIExlc3MgdGhhblxuICogLSBgPD1gIExlc3MgdGhhbiBvciBlcXVhbCB0b1xuICogLSBgPmAgR3JlYXRlciB0aGFuXG4gKiAtIGA+PWAgR3JlYXRlciB0aGFuIG9yIGVxdWFsIHRvXG4gKiAtIGA9YCBFcXVhbC4gSWYgbm8gb3BlcmF0b3IgaXMgc3BlY2lmaWVkLCB0aGVuIGVxdWFsaXR5IGlzIGFzc3VtZWQsIHNvIHRoaXNcbiAqICAgb3BlcmF0b3IgaXMgb3B0aW9uYWwsIGJ1dCBNQVkgYmUgaW5jbHVkZWQuXG4gKlxuICogRm9yIGV4YW1wbGUsIHRoZSBjb21wYXJhdG9yIGA+PTEuMi43YCB3b3VsZCBtYXRjaCB0aGUgdmVyc2lvbnMgYDEuMi43YCwgYDEuMi44YCxcbiAqIGAyLjUuM2AsIGFuZCBgMS4zLjlgLCBidXQgbm90IHRoZSB2ZXJzaW9ucyBgMS4yLjZgIG9yIGAxLjEuMGAuXG4gKlxuICogQ29tcGFyYXRvcnMgY2FuIGJlIGpvaW5lZCBieSB3aGl0ZXNwYWNlIHRvIGZvcm0gYSBgY29tcGFyYXRvciBzZXRgLCB3aGljaCBpc1xuICogc2F0aXNmaWVkIGJ5IHRoZSAqKmludGVyc2VjdGlvbioqIG9mIGFsbCBvZiB0aGUgY29tcGFyYXRvcnMgaXQgaW5jbHVkZXMuXG4gKlxuICogQSByYW5nZSBpcyBjb21wb3NlZCBvZiBvbmUgb3IgbW9yZSBjb21wYXJhdG9yIHNldHMsIGpvaW5lZCBieSBgfHxgLiBBIHZlcnNpb25cbiAqIG1hdGNoZXMgYSByYW5nZSBpZiBhbmQgb25seSBpZiBldmVyeSBjb21wYXJhdG9yIGluIGF0IGxlYXN0IG9uZSBvZiB0aGVcbiAqIGB8fGAtc2VwYXJhdGVkIGNvbXBhcmF0b3Igc2V0cyBpcyBzYXRpc2ZpZWQgYnkgdGhlIHZlcnNpb24uXG4gKlxuICogRm9yIGV4YW1wbGUsIHRoZSByYW5nZSBgPj0xLjIuNyA8MS4zLjBgIHdvdWxkIG1hdGNoIHRoZSB2ZXJzaW9ucyBgMS4yLjdgLFxuICogYDEuMi44YCwgYW5kIGAxLjIuOTlgLCBidXQgbm90IHRoZSB2ZXJzaW9ucyBgMS4yLjZgLCBgMS4zLjBgLCBvciBgMS4xLjBgLlxuICpcbiAqIFRoZSByYW5nZSBgMS4yLjcgfHwgPj0xLjIuOSA8Mi4wLjBgIHdvdWxkIG1hdGNoIHRoZSB2ZXJzaW9ucyBgMS4yLjdgLCBgMS4yLjlgLFxuICogYW5kIGAxLjQuNmAsIGJ1dCBub3QgdGhlIHZlcnNpb25zIGAxLjIuOGAgb3IgYDIuMC4wYC5cbiAqXG4gKiAjIyMgUHJlcmVsZWFzZSBUYWdzXG4gKlxuICogSWYgYSB2ZXJzaW9uIGhhcyBhIHByZXJlbGVhc2UgdGFnIChmb3IgZXhhbXBsZSwgYDEuMi4zLWFscGhhLjNgKSB0aGVuIGl0IHdpbGxcbiAqIG9ubHkgYmUgYWxsb3dlZCB0byBzYXRpc2Z5IGNvbXBhcmF0b3Igc2V0cyBpZiBhdCBsZWFzdCBvbmUgY29tcGFyYXRvciB3aXRoIHRoZVxuICogc2FtZSBgW21ham9yLCBtaW5vciwgcGF0Y2hdYCB0dXBsZSBhbHNvIGhhcyBhIHByZXJlbGVhc2UgdGFnLlxuICpcbiAqIEZvciBleGFtcGxlLCB0aGUgcmFuZ2UgYD4xLjIuMy1hbHBoYS4zYCB3b3VsZCBiZSBhbGxvd2VkIHRvIG1hdGNoIHRoZSB2ZXJzaW9uXG4gKiBgMS4yLjMtYWxwaGEuN2AsIGJ1dCBpdCB3b3VsZCBfbm90XyBiZSBzYXRpc2ZpZWQgYnkgYDMuNC41LWFscGhhLjlgLCBldmVuIHRob3VnaFxuICogYDMuNC41LWFscGhhLjlgIGlzIHRlY2huaWNhbGx5IFwiZ3JlYXRlciB0aGFuXCIgYDEuMi4zLWFscGhhLjNgIGFjY29yZGluZyB0byB0aGVcbiAqIFNlbVZlciBzb3J0IHJ1bGVzLiBUaGUgdmVyc2lvbiByYW5nZSBvbmx5IGFjY2VwdHMgcHJlcmVsZWFzZSB0YWdzIG9uIHRoZSBgMS4yLjNgXG4gKiB2ZXJzaW9uLiBUaGUgdmVyc2lvbiBgMy40LjVgIF93b3VsZF8gc2F0aXNmeSB0aGUgcmFuZ2UsIGJlY2F1c2UgaXQgZG9lcyBub3QgaGF2ZVxuICogYSBwcmVyZWxlYXNlIGZsYWcsIGFuZCBgMy40LjVgIGlzIGdyZWF0ZXIgdGhhbiBgMS4yLjMtYWxwaGEuN2AuXG4gKlxuICogVGhlIHB1cnBvc2UgZm9yIHRoaXMgYmVoYXZpb3IgaXMgdHdvZm9sZC4gRmlyc3QsIHByZXJlbGVhc2UgdmVyc2lvbnMgZnJlcXVlbnRseVxuICogYXJlIHVwZGF0ZWQgdmVyeSBxdWlja2x5LCBhbmQgY29udGFpbiBtYW55IGJyZWFraW5nIGNoYW5nZXMgdGhhdCBhcmUgKGJ5IHRoZVxuICogYXV0aG9yXCJzIGRlc2lnbikgbm90IHlldCBmaXQgZm9yIHB1YmxpYyBjb25zdW1wdGlvbi4gVGhlcmVmb3JlLCBieSBkZWZhdWx0LCB0aGV5XG4gKiBhcmUgZXhjbHVkZWQgZnJvbSByYW5nZSBtYXRjaGluZyBzZW1hbnRpY3MuXG4gKlxuICogU2Vjb25kLCBhIHVzZXIgd2hvIGhhcyBvcHRlZCBpbnRvIHVzaW5nIGEgcHJlcmVsZWFzZSB2ZXJzaW9uIGhhcyBjbGVhcmx5XG4gKiBpbmRpY2F0ZWQgdGhlIGludGVudCB0byB1c2UgX3RoYXQgc3BlY2lmaWNfIHNldCBvZiBhbHBoYS9iZXRhL3JjIHZlcnNpb25zLiBCeVxuICogaW5jbHVkaW5nIGEgcHJlcmVsZWFzZSB0YWcgaW4gdGhlIHJhbmdlLCB0aGUgdXNlciBpcyBpbmRpY2F0aW5nIHRoYXQgdGhleSBhcmVcbiAqIGF3YXJlIG9mIHRoZSByaXNrLiBIb3dldmVyLCBpdCBpcyBzdGlsbCBub3QgYXBwcm9wcmlhdGUgdG8gYXNzdW1lIHRoYXQgdGhleSBoYXZlXG4gKiBvcHRlZCBpbnRvIHRha2luZyBhIHNpbWlsYXIgcmlzayBvbiB0aGUgX25leHRfIHNldCBvZiBwcmVyZWxlYXNlIHZlcnNpb25zLlxuICpcbiAqIE5vdGUgdGhhdCB0aGlzIGJlaGF2aW9yIGNhbiBiZSBzdXBwcmVzc2VkICh0cmVhdGluZyBhbGwgcHJlcmVsZWFzZSB2ZXJzaW9ucyBhc1xuICogaWYgdGhleSB3ZXJlIG5vcm1hbCB2ZXJzaW9ucywgZm9yIHRoZSBwdXJwb3NlIG9mIHJhbmdlIG1hdGNoaW5nKSBieSBzZXR0aW5nIHRoZVxuICogYGluY2x1ZGVQcmVyZWxlYXNlYCBmbGFnIG9uIHRoZSBvcHRpb25zIG9iamVjdCB0byBhbnkgW2Z1bmN0aW9uc10oI2Z1bmN0aW9ucylcbiAqIHRoYXQgZG8gcmFuZ2UgbWF0Y2hpbmcuXG4gKlxuICogIyMjIyBQcmVyZWxlYXNlIElkZW50aWZpZXJzXG4gKlxuICogVGhlIG1ldGhvZCBgLmluY3JlbWVudGAgdGFrZXMgYW4gYWRkaXRpb25hbCBgaWRlbnRpZmllcmAgc3RyaW5nIGFyZ3VtZW50IHRoYXRcbiAqIHdpbGwgYXBwZW5kIHRoZSB2YWx1ZSBvZiB0aGUgc3RyaW5nIGFzIGEgcHJlcmVsZWFzZSBpZGVudGlmaWVyOlxuICpcbiAqIGBgYGphdmFzY3JpcHRcbiAqIHNlbXZlci5pbmNyZW1lbnQocGFyc2UoXCIxLjIuM1wiKSwgXCJwcmVyZWxlYXNlXCIsIFwiYmV0YVwiKTtcbiAqIC8vIFwiMS4yLjQtYmV0YS4wXCJcbiAqIGBgYFxuICpcbiAqICMjIyBCdWlsZCBNZXRhZGF0YVxuICpcbiAqIEJ1aWxkIG1ldGFkYXRhIGlzIGAuYCBkZWxpbWl0ZWQgYWxwaGEtbnVtZXJpYyBzdHJpbmcuXG4gKiBXaGVuIHBhcnNpbmcgYSB2ZXJzaW9uIGl0IGlzIHJldGFpbmVkIG9uIHRoZSBgYnVpbGQ6IHN0cmluZ1tdYCBmaWVsZFxuICogb2YgdGhlIHNlbXZlciBpbnN0YW5jZS4gV2hlbiBpbmNyZW1lbnRpbmcgdGhlcmUgaXMgYW4gYWRkaXRpb25hbCBwYXJhbWV0ZXIgdGhhdFxuICogY2FuIHNldCB0aGUgYnVpbGQgbWV0YWRhdGEgb24gdGhlIHNlbXZlciBpbnN0YW5jZS5cbiAqXG4gKiAjIyMgQWR2YW5jZWQgUmFuZ2UgU3ludGF4XG4gKlxuICogQWR2YW5jZWQgcmFuZ2Ugc3ludGF4IGRlc3VnYXJzIHRvIHByaW1pdGl2ZSBjb21wYXJhdG9ycyBpbiBkZXRlcm1pbmlzdGljIHdheXMuXG4gKlxuICogQWR2YW5jZWQgcmFuZ2VzIG1heSBiZSBjb21iaW5lZCBpbiB0aGUgc2FtZSB3YXkgYXMgcHJpbWl0aXZlIGNvbXBhcmF0b3JzIHVzaW5nXG4gKiB3aGl0ZSBzcGFjZSBvciBgfHxgLlxuICpcbiAqICMjIyMgSHlwaGVuIFJhbmdlcyBgWC5ZLlogLSBBLkIuQ2BcbiAqXG4gKiBTcGVjaWZpZXMgYW4gaW5jbHVzaXZlIHNldC5cbiAqXG4gKiAtIGAxLjIuMyAtIDIuMy40YCA6PSBgPj0xLjIuMyA8PTIuMy40YFxuICpcbiAqIElmIGEgcGFydGlhbCB2ZXJzaW9uIGlzIHByb3ZpZGVkIGFzIHRoZSBmaXJzdCB2ZXJzaW9uIGluIHRoZSBpbmNsdXNpdmUgcmFuZ2UsXG4gKiB0aGVuIHRoZSBtaXNzaW5nIHBpZWNlcyBhcmUgcmVwbGFjZWQgd2l0aCB6ZXJvZXMuXG4gKlxuICogLSBgMS4yIC0gMi4zLjRgIDo9IGA+PTEuMi4wIDw9Mi4zLjRgXG4gKlxuICogSWYgYSBwYXJ0aWFsIHZlcnNpb24gaXMgcHJvdmlkZWQgYXMgdGhlIHNlY29uZCB2ZXJzaW9uIGluIHRoZSBpbmNsdXNpdmUgcmFuZ2UsXG4gKiB0aGVuIGFsbCB2ZXJzaW9ucyB0aGF0IHN0YXJ0IHdpdGggdGhlIHN1cHBsaWVkIHBhcnRzIG9mIHRoZSB0dXBsZSBhcmUgYWNjZXB0ZWQsXG4gKiBidXQgbm90aGluZyB0aGF0IHdvdWxkIGJlIGdyZWF0ZXIgdGhhbiB0aGUgcHJvdmlkZWQgdHVwbGUgcGFydHMuXG4gKlxuICogLSBgMS4yLjMgLSAyLjNgIDo9IGA+PTEuMi4zIDwyLjQuMGBcbiAqIC0gYDEuMi4zIC0gMmAgOj0gYD49MS4yLjMgPDMuMC4wYFxuICpcbiAqICMjIyMgWC1SYW5nZXMgYDEuMi54YCBgMS5YYCBgMS4yLipgIGAqYFxuICpcbiAqIEFueSBvZiBgWGAsIGB4YCwgb3IgYCpgIG1heSBiZSB1c2VkIHRvIFwic3RhbmQgaW5cIiBmb3Igb25lIG9mIHRoZSBudW1lcmljIHZhbHVlc1xuICogaW4gdGhlIGBbbWFqb3IsIG1pbm9yLCBwYXRjaF1gIHR1cGxlLlxuICpcbiAqIC0gYCpgIDo9IGA+PTAuMC4wYCAoQW55IHZlcnNpb24gc2F0aXNmaWVzKVxuICogLSBgMS54YCA6PSBgPj0xLjAuMCA8Mi4wLjBgIChNYXRjaGluZyBtYWpvciB2ZXJzaW9uKVxuICogLSBgMS4yLnhgIDo9IGA+PTEuMi4wIDwxLjMuMGAgKE1hdGNoaW5nIG1ham9yIGFuZCBtaW5vciB2ZXJzaW9ucylcbiAqXG4gKiBBIHBhcnRpYWwgdmVyc2lvbiByYW5nZSBpcyB0cmVhdGVkIGFzIGFuIFgtUmFuZ2UsIHNvIHRoZSBzcGVjaWFsIGNoYXJhY3RlciBpcyBpblxuICogZmFjdCBvcHRpb25hbC5cbiAqXG4gKiAtIGBcIlwiYCAoZW1wdHkgc3RyaW5nKSA6PSBgKmAgOj0gYD49MC4wLjBgXG4gKiAtIGAxYCA6PSBgMS54LnhgIDo9IGA+PTEuMC4wIDwyLjAuMGBcbiAqIC0gYDEuMmAgOj0gYDEuMi54YCA6PSBgPj0xLjIuMCA8MS4zLjBgXG4gKlxuICogIyMjIyBUaWxkZSBSYW5nZXMgYH4xLjIuM2AgYH4xLjJgIGB+MWBcbiAqXG4gKiBBbGxvd3MgcGF0Y2gtbGV2ZWwgY2hhbmdlcyBpZiBhIG1pbm9yIHZlcnNpb24gaXMgc3BlY2lmaWVkIG9uIHRoZSBjb21wYXJhdG9yLlxuICogQWxsb3dzIG1pbm9yLWxldmVsIGNoYW5nZXMgaWYgbm90LlxuICpcbiAqIC0gYH4xLjIuM2AgOj0gYD49MS4yLjMgPDEuKDIrMSkuMGAgOj0gYD49MS4yLjMgPDEuMy4wYFxuICogLSBgfjEuMmAgOj0gYD49MS4yLjAgPDEuKDIrMSkuMGAgOj0gYD49MS4yLjAgPDEuMy4wYCAoU2FtZSBhcyBgMS4yLnhgKVxuICogLSBgfjFgIDo9IGA+PTEuMC4wIDwoMSsxKS4wLjBgIDo9IGA+PTEuMC4wIDwyLjAuMGAgKFNhbWUgYXMgYDEueGApXG4gKiAtIGB+MC4yLjNgIDo9IGA+PTAuMi4zIDwwLigyKzEpLjBgIDo9IGA+PTAuMi4zIDwwLjMuMGBcbiAqIC0gYH4wLjJgIDo9IGA+PTAuMi4wIDwwLigyKzEpLjBgIDo9IGA+PTAuMi4wIDwwLjMuMGAgKFNhbWUgYXMgYDAuMi54YClcbiAqIC0gYH4wYCA6PSBgPj0wLjAuMCA8KDArMSkuMC4wYCA6PSBgPj0wLjAuMCA8MS4wLjBgIChTYW1lIGFzIGAwLnhgKVxuICogLSBgfjEuMi4zLWJldGEuMmAgOj0gYD49MS4yLjMtYmV0YS4yIDwxLjMuMGAgTm90ZSB0aGF0IHByZXJlbGVhc2VzIGluIHRoZVxuICogICBgMS4yLjNgIHZlcnNpb24gd2lsbCBiZSBhbGxvd2VkLCBpZiB0aGV5IGFyZSBncmVhdGVyIHRoYW4gb3IgZXF1YWwgdG9cbiAqICAgYGJldGEuMmAuIFNvLCBgMS4yLjMtYmV0YS40YCB3b3VsZCBiZSBhbGxvd2VkLCBidXQgYDEuMi40LWJldGEuMmAgd291bGQgbm90LFxuICogICBiZWNhdXNlIGl0IGlzIGEgcHJlcmVsZWFzZSBvZiBhIGRpZmZlcmVudCBgW21ham9yLCBtaW5vciwgcGF0Y2hdYCB0dXBsZS5cbiAqXG4gKiAjIyMjIENhcmV0IFJhbmdlcyBgXjEuMi4zYCBgXjAuMi41YCBgXjAuMC40YFxuICpcbiAqIEFsbG93cyBjaGFuZ2VzIHRoYXQgZG8gbm90IG1vZGlmeSB0aGUgbGVmdC1tb3N0IG5vbi16ZXJvIGVsZW1lbnQgaW4gdGhlXG4gKiBgW21ham9yLCBtaW5vciwgcGF0Y2hdYCB0dXBsZS4gSW4gb3RoZXIgd29yZHMsIHRoaXMgYWxsb3dzIHBhdGNoIGFuZCBtaW5vclxuICogdXBkYXRlcyBmb3IgdmVyc2lvbnMgYDEuMC4wYCBhbmQgYWJvdmUsIHBhdGNoIHVwZGF0ZXMgZm9yIHZlcnNpb25zXG4gKiBgMC5YID49MC4xLjBgLCBhbmQgX25vXyB1cGRhdGVzIGZvciB2ZXJzaW9ucyBgMC4wLlhgLlxuICpcbiAqIE1hbnkgYXV0aG9ycyB0cmVhdCBhIGAwLnhgIHZlcnNpb24gYXMgaWYgdGhlIGB4YCB3ZXJlIHRoZSBtYWpvclxuICogXCJicmVha2luZy1jaGFuZ2VcIiBpbmRpY2F0b3IuXG4gKlxuICogQ2FyZXQgcmFuZ2VzIGFyZSBpZGVhbCB3aGVuIGFuIGF1dGhvciBtYXkgbWFrZSBicmVha2luZyBjaGFuZ2VzIGJldHdlZW4gYDAuMi40YFxuICogYW5kIGAwLjMuMGAgcmVsZWFzZXMsIHdoaWNoIGlzIGEgY29tbW9uIHByYWN0aWNlLiBIb3dldmVyLCBpdCBwcmVzdW1lcyB0aGF0XG4gKiB0aGVyZSB3aWxsIF9ub3RfIGJlIGJyZWFraW5nIGNoYW5nZXMgYmV0d2VlbiBgMC4yLjRgIGFuZCBgMC4yLjVgLiBJdCBhbGxvd3MgZm9yXG4gKiBjaGFuZ2VzIHRoYXQgYXJlIHByZXN1bWVkIHRvIGJlIGFkZGl0aXZlIChidXQgbm9uLWJyZWFraW5nKSwgYWNjb3JkaW5nIHRvXG4gKiBjb21tb25seSBvYnNlcnZlZCBwcmFjdGljZXMuXG4gKlxuICogLSBgXjEuMi4zYCA6PSBgPj0xLjIuMyA8Mi4wLjBgXG4gKiAtIGBeMC4yLjNgIDo9IGA+PTAuMi4zIDwwLjMuMGBcbiAqIC0gYF4wLjAuM2AgOj0gYD49MC4wLjMgPDAuMC40YFxuICogLSBgXjEuMi4zLWJldGEuMmAgOj0gYD49MS4yLjMtYmV0YS4yIDwyLjAuMGAgTm90ZSB0aGF0IHByZXJlbGVhc2VzIGluIHRoZVxuICogICBgMS4yLjNgIHZlcnNpb24gd2lsbCBiZSBhbGxvd2VkLCBpZiB0aGV5IGFyZSBncmVhdGVyIHRoYW4gb3IgZXF1YWwgdG9cbiAqICAgYGJldGEuMmAuIFNvLCBgMS4yLjMtYmV0YS40YCB3b3VsZCBiZSBhbGxvd2VkLCBidXQgYDEuMi40LWJldGEuMmAgd291bGQgbm90LFxuICogICBiZWNhdXNlIGl0IGlzIGEgcHJlcmVsZWFzZSBvZiBhIGRpZmZlcmVudCBgW21ham9yLCBtaW5vciwgcGF0Y2hdYCB0dXBsZS5cbiAqIC0gYF4wLjAuMy1iZXRhYCA6PSBgPj0wLjAuMy1iZXRhIDwwLjAuNGAgTm90ZSB0aGF0IHByZXJlbGVhc2VzIGluIHRoZSBgMC4wLjNgXG4gKiAgIHZlcnNpb24gX29ubHlfIHdpbGwgYmUgYWxsb3dlZCwgaWYgdGhleSBhcmUgZ3JlYXRlciB0aGFuIG9yIGVxdWFsIHRvIGBiZXRhYC5cbiAqICAgU28sIGAwLjAuMy1wci4yYCB3b3VsZCBiZSBhbGxvd2VkLlxuICpcbiAqIFdoZW4gcGFyc2luZyBjYXJldCByYW5nZXMsIGEgbWlzc2luZyBgcGF0Y2hgIHZhbHVlIGRlc3VnYXJzIHRvIHRoZSBudW1iZXIgYDBgLFxuICogYnV0IHdpbGwgYWxsb3cgZmxleGliaWxpdHkgd2l0aGluIHRoYXQgdmFsdWUsIGV2ZW4gaWYgdGhlIG1ham9yIGFuZCBtaW5vclxuICogdmVyc2lvbnMgYXJlIGJvdGggYDBgLlxuICpcbiAqIC0gYF4xLjIueGAgOj0gYD49MS4yLjAgPDIuMC4wYFxuICogLSBgXjAuMC54YCA6PSBgPj0wLjAuMCA8MC4xLjBgXG4gKiAtIGBeMC4wYCA6PSBgPj0wLjAuMCA8MC4xLjBgXG4gKlxuICogQSBtaXNzaW5nIGBtaW5vcmAgYW5kIGBwYXRjaGAgdmFsdWVzIHdpbGwgZGVzdWdhciB0byB6ZXJvLCBidXQgYWxzbyBhbGxvd1xuICogZmxleGliaWxpdHkgd2l0aGluIHRob3NlIHZhbHVlcywgZXZlbiBpZiB0aGUgbWFqb3IgdmVyc2lvbiBpcyB6ZXJvLlxuICpcbiAqIC0gYF4xLnhgIDo9IGA+PTEuMC4wIDwyLjAuMGBcbiAqIC0gYF4wLnhgIDo9IGA+PTAuMC4wIDwxLjAuMGBcbiAqXG4gKiAjIyMgUmFuZ2UgR3JhbW1hclxuICpcbiAqIFB1dHRpbmcgYWxsIHRoaXMgdG9nZXRoZXIsIGhlcmUgaXMgYSBCYWNrdXMtTmF1ciBncmFtbWFyIGZvciByYW5nZXMsIGZvciB0aGVcbiAqIGJlbmVmaXQgb2YgcGFyc2VyIGF1dGhvcnM6XG4gKlxuICogYGBgYm5mXG4gKiByYW5nZS1zZXQgIDo6PSByYW5nZSAoIGxvZ2ljYWwtb3IgcmFuZ2UgKSAqXG4gKiBsb2dpY2FsLW9yIDo6PSAoIFwiIFwiICkgKiBcInx8XCIgKCBcIiBcIiApICpcbiAqIHJhbmdlICAgICAgOjo9IGh5cGhlbiB8IHNpbXBsZSAoIFwiIFwiIHNpbXBsZSApICogfCBcIlwiXG4gKiBoeXBoZW4gICAgIDo6PSBwYXJ0aWFsIFwiIC0gXCIgcGFydGlhbFxuICogc2ltcGxlICAgICA6Oj0gcHJpbWl0aXZlIHwgcGFydGlhbCB8IHRpbGRlIHwgY2FyZXRcbiAqIHByaW1pdGl2ZSAgOjo9ICggXCI8XCIgfCBcIj5cIiB8IFwiPj1cIiB8IFwiPD1cIiB8IFwiPVwiICkgcGFydGlhbFxuICogcGFydGlhbCAgICA6Oj0geHIgKCBcIi5cIiB4ciAoIFwiLlwiIHhyIHF1YWxpZmllciA/ICk/ICk/XG4gKiB4ciAgICAgICAgIDo6PSBcInhcIiB8IFwiWFwiIHwgXCIqXCIgfCBuclxuICogbnIgICAgICAgICA6Oj0gXCIwXCIgfCBbXCIxXCItXCI5XCJdICggW1wiMFwiLVwiOVwiXSApICpcbiAqIHRpbGRlICAgICAgOjo9IFwiflwiIHBhcnRpYWxcbiAqIGNhcmV0ICAgICAgOjo9IFwiXlwiIHBhcnRpYWxcbiAqIHF1YWxpZmllciAgOjo9ICggXCItXCIgcHJlICk/ICggXCIrXCIgYnVpbGQgKT9cbiAqIHByZSAgICAgICAgOjo9IHBhcnRzXG4gKiBidWlsZCAgICAgIDo6PSBwYXJ0c1xuICogcGFydHMgICAgICA6Oj0gcGFydCAoIFwiLlwiIHBhcnQgKSAqXG4gKiBwYXJ0ICAgICAgIDo6PSBuciB8IFstMC05QS1aYS16XStcbiAqIGBgYFxuICpcbiAqIE5vdGUgdGhhdCwgc2luY2UgcmFuZ2VzIG1heSBiZSBub24tY29udGlndW91cywgYSB2ZXJzaW9uIG1pZ2h0IG5vdCBiZSBncmVhdGVyXG4gKiB0aGFuIGEgcmFuZ2UsIGxlc3MgdGhhbiBhIHJhbmdlLCBfb3JfIHNhdGlzZnkgYSByYW5nZSEgRm9yIGV4YW1wbGUsIHRoZSByYW5nZVxuICogYDEuMiA8MS4yLjkgfHwgPjIuMC4wYCB3b3VsZCBoYXZlIGEgaG9sZSBmcm9tIGAxLjIuOWAgdW50aWwgYDIuMC4wYCwgc28gdGhlXG4gKiB2ZXJzaW9uIGAxLjIuMTBgIHdvdWxkIG5vdCBiZSBncmVhdGVyIHRoYW4gdGhlIHJhbmdlIChiZWNhdXNlIGAyLjAuMWAgc2F0aXNmaWVzLFxuICogd2hpY2ggaXMgaGlnaGVyKSwgbm9yIGxlc3MgdGhhbiB0aGUgcmFuZ2UgKHNpbmNlIGAxLjIuOGAgc2F0aXNmaWVzLCB3aGljaCBpc1xuICogbG93ZXIpLCBhbmQgaXQgYWxzbyBkb2VzIG5vdCBzYXRpc2Z5IHRoZSByYW5nZS5cbiAqXG4gKiBJZiB5b3Ugd2FudCB0byBrbm93IGlmIGEgdmVyc2lvbiBzYXRpc2ZpZXMgb3IgZG9lcyBub3Qgc2F0aXNmeSBhIHJhbmdlLCB1c2UgdGhlXG4gKiB7QGxpbmtjb2RlIHNhdGlzZmllc30gZnVuY3Rpb24uXG4gKlxuICogVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHtcbiAqICAgcGFyc2UsXG4gKiAgIHBhcnNlQ29tcGFyYXRvcixcbiAqICAgcGFyc2VSYW5nZSxcbiAqICAgZ3QsXG4gKiAgIGx0LFxuICogICBmb3JtYXRcbiAqIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vc2VtdmVyL21vZC50c1wiO1xuICpcbiAqIGNvbnN0IHNlbXZlciA9IHBhcnNlKFwiMS4yLjNcIik7XG4gKiBjb25zdCByYW5nZSA9IHBhcnNlUmFuZ2UoXCIxLnggfHwgPj0yLjUuMCB8fCA1LjAuMCAtIDcuMi4zXCIpO1xuICpcbiAqIGNvbnN0IHMwID0gcGFyc2UoXCIxLjIuM1wiKTtcbiAqIGNvbnN0IHMxID0gcGFyc2UoXCI5LjguN1wiKTtcbiAqIGd0KHMwLCBzMSk7IC8vIGZhbHNlXG4gKiBsdChzMCwgczEpOyAvLyB0cnVlXG4gKlxuICogZm9ybWF0KHNlbXZlcikgLy8gXCIxLjIuM1wiXG4gKiBgYGBcbiAqXG4gKiBAbW9kdWxlXG4gKi9cbmV4cG9ydCAqIGZyb20gXCIuL2NtcC50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29tcGFyYXRvcl9mb3JtYXQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbXBhcmF0b3JfaW50ZXJzZWN0cy50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29tcGFyYXRvcl9tYXgudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbXBhcmF0b3JfbWluLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb21wYXJlX2J1aWxkLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb21wYXJlLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb25zdGFudHMudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2RpZmZlcmVuY2UudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2VxLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9mb3JtYXQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2d0LnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9ndGUudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2d0ci50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdGVzdF9jb21wYXJhdG9yLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90ZXN0X3JhbmdlLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pbmNyZW1lbnQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2lzX3NlbXZlcl9jb21wYXJhdG9yLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pc19zZW12ZXJfcmFuZ2UudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2lzX3NlbXZlci50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbHQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2x0ZS50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbHRyLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9tYXhfc2F0aXNmeWluZy50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbWluX3NhdGlzZnlpbmcudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL25lcS50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vb3V0c2lkZS50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcGFyc2VfY29tcGFyYXRvci50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcGFyc2VfcmFuZ2UudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3BhcnNlLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yYW5nZV9mb3JtYXQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3JhbmdlX2ludGVyc2VjdHMudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3JhbmdlX21heC50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmFuZ2VfbWluLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yY29tcGFyZS50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcnNvcnQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NvcnQudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R5cGVzLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9sdGUudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2x0ZS50c1wiO1xuZXhwb3J0IGNvbnN0IFNFTVZFUl9TUEVDX1ZFUlNJT04gPSBcIjIuMC4wXCI7XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsbUZBQW1GO0FBQ25GLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0E0UUMsR0FDRCxjQUFjLFdBQVc7QUFDekIsY0FBYyx5QkFBeUI7QUFDdkMsY0FBYyw2QkFBNkI7QUFDM0MsY0FBYyxzQkFBc0I7QUFDcEMsY0FBYyxzQkFBc0I7QUFDcEMsY0FBYyxxQkFBcUI7QUFDbkMsY0FBYyxlQUFlO0FBQzdCLGNBQWMsaUJBQWlCO0FBQy9CLGNBQWMsa0JBQWtCO0FBQ2hDLGNBQWMsVUFBVTtBQUN4QixjQUFjLGNBQWM7QUFDNUIsY0FBYyxVQUFVO0FBQ3hCLGNBQWMsV0FBVztBQUN6QixjQUFjLFdBQVc7QUFDekIsY0FBYyx1QkFBdUI7QUFDckMsY0FBYyxrQkFBa0I7QUFDaEMsY0FBYyxpQkFBaUI7QUFDL0IsY0FBYyw0QkFBNEI7QUFDMUMsY0FBYyx1QkFBdUI7QUFDckMsY0FBYyxpQkFBaUI7QUFDL0IsY0FBYyxVQUFVO0FBQ3hCLGNBQWMsV0FBVztBQUN6QixjQUFjLFdBQVc7QUFDekIsY0FBYyxzQkFBc0I7QUFDcEMsY0FBYyxzQkFBc0I7QUFDcEMsY0FBYyxXQUFXO0FBQ3pCLGNBQWMsZUFBZTtBQUM3QixjQUFjLHdCQUF3QjtBQUN0QyxjQUFjLG1CQUFtQjtBQUNqQyxjQUFjLGFBQWE7QUFDM0IsY0FBYyxvQkFBb0I7QUFDbEMsY0FBYyx3QkFBd0I7QUFDdEMsY0FBYyxpQkFBaUI7QUFDL0IsY0FBYyxpQkFBaUI7QUFDL0IsY0FBYyxnQkFBZ0I7QUFDOUIsY0FBYyxhQUFhO0FBQzNCLGNBQWMsWUFBWTtBQUMxQixjQUFjLGFBQWE7QUFDM0IsY0FBYyxXQUFXO0FBQ3pCLGNBQWMsV0FBVztBQUN6QixPQUFPLE1BQU0sc0JBQXNCLFFBQVEifQ==