// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { eq } from "./eq.ts";
import { neq } from "./neq.ts";
import { gte } from "./gte.ts";
import { gt } from "./gt.ts";
import { lt } from "./lt.ts";
import { lte } from "./lte.ts";
/**
 * Do a comparison of two semantic version objects based on the given operator
 * @param s0 The left side of the comparison
 * @param operator The operator to use for the comparison
 * @param s1 The right side of the comparison
 * @returns True or false based on the operator
 */ export function cmp(s0, operator, s1) {
  switch(operator){
    case "":
    case "=":
    case "==":
    case "===":
      return eq(s0, s1);
    case "!=":
    case "!==":
      return neq(s0, s1);
    case ">":
      return gt(s0, s1);
    case ">=":
      return gte(s0, s1);
    case "<":
      return lt(s0, s1);
    case "<=":
      return lte(s0, s1);
    default:
      throw new TypeError(`Invalid operator: ${operator}`);
  }
}
//# sourceMappingURL=data:application/json;base64,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