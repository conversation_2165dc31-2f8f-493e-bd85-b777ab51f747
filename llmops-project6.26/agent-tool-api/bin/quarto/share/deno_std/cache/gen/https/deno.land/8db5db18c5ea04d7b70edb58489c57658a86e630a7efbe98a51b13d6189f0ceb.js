// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
function checkTagFormat(tag) {
  return tag;
}
export class Type {
  tag;
  kind = null;
  instanceOf;
  predicate;
  represent;
  defaultStyle;
  styleAliases;
  loadKind;
  constructor(tag, options){
    this.tag = checkTagFormat(tag);
    if (options) {
      this.kind = options.kind;
      this.resolve = options.resolve || (()=>true);
      this.construct = options.construct || ((data)=>data);
      this.instanceOf = options.instanceOf;
      this.predicate = options.predicate;
      this.represent = options.represent;
      this.defaultStyle = options.defaultStyle;
      this.styleAliases = options.styleAliases;
    }
  }
  resolve = ()=>true;
  construct = (data)=>data;
}
//# sourceMappingURL=data:application/json;base64,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