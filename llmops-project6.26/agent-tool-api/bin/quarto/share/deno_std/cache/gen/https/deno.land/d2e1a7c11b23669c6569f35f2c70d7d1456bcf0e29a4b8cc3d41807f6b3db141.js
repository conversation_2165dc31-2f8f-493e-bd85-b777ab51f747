// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { gt } from "./gt.ts";
import { gte } from "./gte.ts";
import { lte } from "./lte.ts";
import { lt } from "./lt.ts";
import { ALL, ANY } from "./constants.ts";
import { testRange } from "./test_range.ts";
/**
 * Returns true if the version is outside the bounds of the range in either the
 * high or low direction. The hilo argument must be either the string '>' or
 * '<'. (This is the function called by {@linkcode gtr} and {@linkcode ltr}.)
 * @param version The version to compare to the range
 * @param range The range of possible versions
 * @param hilo The operator for the comparison or both if undefined.
 * @returns True if the version is outside of the range based on the operator
 */ export function outside(version, range, hilo) {
  if (!hilo) {
    return outside(version, range, ">") || outside(version, range, "<");
  }
  const [gtfn, ltefn, ltfn, comp, ecomp] = (()=>{
    switch(hilo){
      case ">":
        return [
          gt,
          lte,
          lt,
          ">",
          ">="
        ];
      case "<":
        return [
          lt,
          gte,
          gt,
          "<",
          "<="
        ];
    }
  })();
  if (testRange(version, range)) {
    return false;
  }
  for (const comparators of range.ranges){
    let high = undefined;
    let low = undefined;
    for (let comparator of comparators){
      if (comparator.semver === ANY) {
        comparator = ALL;
      }
      high = high || comparator;
      low = low || comparator;
      if (gtfn(comparator.semver, high.semver)) {
        high = comparator;
      } else if (ltfn(comparator.semver, low.semver)) {
        low = comparator;
      }
    }
    if (!high || !low) return true;
    // If the edge version comparator has a operator then our version
    // isn't outside it
    if (high.operator === comp || high.operator === ecomp) {
      return false;
    }
    // If the lowest version comparator has an operator and our version
    // is less than it then it isn't higher than the range
    if ((!low.operator || low.operator === comp) && ltefn(version, low.semver)) {
      return false;
    } else if (low.operator === ecomp && ltfn(version, low.semver)) {
      return false;
    }
  }
  return true;
}
//# sourceMappingURL=data:application/json;base64,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