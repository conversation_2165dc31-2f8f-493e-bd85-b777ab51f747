// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Extensions to the
 * [Web Crypto](https://developer.mozilla.org/en-US/docs/Web/API/Web_Crypto_API)
 * supporting additional encryption APIs, but also delegating to the built-in
 * APIs when possible.
 *
 * Provides additional digest algorithms that are not part of the WebCrypto
 * standard as well as a `subtle.digest` and `subtle.digestSync` methods. It
 * also provides a `subtle.timingSafeEqual()` method to compare array buffers
 * or data views in a way that isn't prone to timing based attacks.
 *
 * The "polyfill" delegates to `WebCrypto` where possible.
 *
 * The {@linkcode KeyStack} export implements the {@linkcode KeyRing} interface
 * for managing rotatable keys for signing data to prevent tampering, like with
 * HTTP cookies.
 *
 * ## Supported algorithms
 *
 * Here is a list of supported algorithms. If the algorithm name in WebCrypto
 * and Wasm/Rust is the same, this library prefers to use algorithms that are
 * supported by WebCrypto.
 *
 * WebCrypto
 *
 * ```ts
 * // https://deno.land/std/crypto/crypto.ts
 * const webCryptoDigestAlgorithms = [
 *   "SHA-384",
 *   "SHA-256",
 *   "SHA-512",
 *   // insecure (length-extendable and collidable):
 *   "SHA-1",
 * ] as const;
 * ```
 *
 * Wasm/Rust
 *
 * ```ts
 * // https://deno.land/std/crypto/_wasm/mod.ts
 * export const digestAlgorithms = [
 *   "BLAKE2B-128",
 *   "BLAKE2B-224",
 *   "BLAKE2B-256",
 *   "BLAKE2B-384",
 *   "BLAKE2B",
 *   "BLAKE2S",
 *   "BLAKE3",
 *   "KECCAK-224",
 *   "KECCAK-256",
 *   "KECCAK-384",
 *   "KECCAK-512",
 *   "SHA-384",
 *   "SHA3-224",
 *   "SHA3-256",
 *   "SHA3-384",
 *   "SHA3-512",
 *   "SHAKE128",
 *   "SHAKE256",
 *   "TIGER",
 *   // insecure (length-extendable):
 *   "RIPEMD-160",
 *   "SHA-224",
 *   "SHA-256",
 *   "SHA-512",
 *   // insecure (collidable and length-extendable):
 *   "MD4",
 *   "MD5",
 *   "SHA-1",
 * ] as const;
 * ```
 *
 * @example
 * ```ts
 * import { crypto } from "https://deno.land/std@$STD_VERSION/crypto/mod.ts";
 *
 * // This will delegate to the runtime's WebCrypto implementation.
 * console.log(
 *   new Uint8Array(
 *     await crypto.subtle.digest(
 *       "SHA-384",
 *       new TextEncoder().encode("hello world"),
 *     ),
 *   ),
 * );
 *
 * // This will use a bundled Wasm/Rust implementation.
 * console.log(
 *   new Uint8Array(
 *     await crypto.subtle.digest(
 *       "BLAKE3",
 *       new TextEncoder().encode("hello world"),
 *     ),
 *   ),
 * );
 * ```
 *
 * @example Convert hash to a string
 *
 * ```ts
 * import {
 *   crypto,
 *   toHashString,
 * } from "https://deno.land/std@$STD_VERSION/crypto/mod.ts";
 *
 * const hash = await crypto.subtle.digest(
 *   "SHA-384",
 *   new TextEncoder().encode("You hear that Mr. Anderson?"),
 * );
 *
 * // Hex encoding by default
 * console.log(toHashString(hash));
 *
 * // Or with base64 encoding
 * console.log(toHashString(hash, "base64"));
 * ```
 *
 * @module
 */ import { digestAlgorithms as wasmDigestAlgorithms, instantiateWasm } from "./_wasm/mod.ts";
import { timingSafeEqual } from "./timing_safe_equal.ts";
import { fnv } from "./_fnv/mod.ts";
/**
 * A copy of the global WebCrypto interface, with methods bound so they're
 * safe to re-export.
 */ const webCrypto = ((crypto)=>({
    getRandomValues: crypto.getRandomValues?.bind(crypto),
    randomUUID: crypto.randomUUID?.bind(crypto),
    subtle: {
      decrypt: crypto.subtle?.decrypt?.bind(crypto.subtle),
      deriveBits: crypto.subtle?.deriveBits?.bind(crypto.subtle),
      deriveKey: crypto.subtle?.deriveKey?.bind(crypto.subtle),
      digest: crypto.subtle?.digest?.bind(crypto.subtle),
      encrypt: crypto.subtle?.encrypt?.bind(crypto.subtle),
      exportKey: crypto.subtle?.exportKey?.bind(crypto.subtle),
      generateKey: crypto.subtle?.generateKey?.bind(crypto.subtle),
      importKey: crypto.subtle?.importKey?.bind(crypto.subtle),
      sign: crypto.subtle?.sign?.bind(crypto.subtle),
      unwrapKey: crypto.subtle?.unwrapKey?.bind(crypto.subtle),
      verify: crypto.subtle?.verify?.bind(crypto.subtle),
      wrapKey: crypto.subtle?.wrapKey?.bind(crypto.subtle)
    }
  }))(globalThis.crypto);
const bufferSourceBytes = (data)=>{
  let bytes;
  if (data instanceof Uint8Array) {
    bytes = data;
  } else if (ArrayBuffer.isView(data)) {
    bytes = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);
  } else if (data instanceof ArrayBuffer) {
    bytes = new Uint8Array(data);
  }
  return bytes;
};
/**
 * An wrapper for WebCrypto adding support for additional non-standard
 * algorithms, but delegating to the runtime WebCrypto implementation whenever
 * possible.
 */ const stdCrypto = ((x)=>x)({
  ...webCrypto,
  subtle: {
    ...webCrypto.subtle,
    /**
     * Polyfills stream support until the Web Crypto API does so:
     * @see {@link https://github.com/wintercg/proposal-webcrypto-streams}
     */ async digest (algorithm, data) {
      const { name, length } = normalizeAlgorithm(algorithm);
      const bytes = bufferSourceBytes(data);
      if (FNVAlgorithms.includes(name)) {
        return fnv(name, bytes);
      }
      // We delegate to WebCrypto whenever possible,
      if (// if the algorithm is supported by the WebCrypto standard,
      webCryptoDigestAlgorithms.includes(name) && // and the data is a single buffer,
      bytes) {
        return webCrypto.subtle.digest(algorithm, bytes);
      } else if (wasmDigestAlgorithms.includes(name)) {
        if (bytes) {
          // Otherwise, we use our bundled Wasm implementation via digestSync
          // if it supports the algorithm.
          return stdCrypto.subtle.digestSync(algorithm, bytes);
        } else if (data[Symbol.iterator]) {
          return stdCrypto.subtle.digestSync(algorithm, data);
        } else if (data[Symbol.asyncIterator]) {
          const wasmCrypto = instantiateWasm();
          const context = new wasmCrypto.DigestContext(name);
          for await (const chunk of data){
            const chunkBytes = bufferSourceBytes(chunk);
            if (!chunkBytes) {
              throw new TypeError("data contained chunk of the wrong type");
            }
            context.update(chunkBytes);
          }
          return context.digestAndDrop(length).buffer;
        } else {
          throw new TypeError("data must be a BufferSource or [Async]Iterable<BufferSource>");
        }
      } else if (webCrypto.subtle?.digest) {
        // (TypeScript type definitions prohibit this case.) If they're trying
        // to call an algorithm we don't recognize, pass it along to WebCrypto
        // in case it's a non-standard algorithm supported by the the runtime
        // they're using.
        return webCrypto.subtle.digest(algorithm, data);
      } else {
        throw new TypeError(`unsupported digest algorithm: ${algorithm}`);
      }
    },
    digestSync (algorithm, data) {
      algorithm = normalizeAlgorithm(algorithm);
      const bytes = bufferSourceBytes(data);
      if (FNVAlgorithms.includes(algorithm.name)) {
        return fnv(algorithm.name, bytes);
      }
      const wasmCrypto = instantiateWasm();
      if (bytes) {
        return wasmCrypto.digest(algorithm.name, bytes, algorithm.length).buffer;
      } else if (data[Symbol.iterator]) {
        const context = new wasmCrypto.DigestContext(algorithm.name);
        for (const chunk of data){
          const chunkBytes = bufferSourceBytes(chunk);
          if (!chunkBytes) {
            throw new TypeError("data contained chunk of the wrong type");
          }
          context.update(chunkBytes);
        }
        return context.digestAndDrop(algorithm.length).buffer;
      } else {
        throw new TypeError("data must be a BufferSource or Iterable<BufferSource>");
      }
    },
    // TODO(@kitsonk): rework when https://github.com/w3c/webcrypto/issues/270 resolved
    timingSafeEqual
  }
});
const FNVAlgorithms = [
  "FNV32",
  "FNV32A",
  "FNV64",
  "FNV64A"
];
/** Digest algorithms supported by WebCrypto. */ const webCryptoDigestAlgorithms = [
  "SHA-384",
  "SHA-256",
  "SHA-512",
  // insecure (length-extendable and collidable):
  "SHA-1"
];
function normalizeAlgorithm(algorithm) {
  return typeof algorithm === "string" ? {
    name: algorithm.toUpperCase()
  } : {
    ...algorithm,
    name: algorithm.name.toUpperCase()
  };
}
export { stdCrypto as crypto };
//# sourceMappingURL=data:application/json;base64,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