// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { <PERSON>uff<PERSON> } from "../io/buffer.ts";
import { writeAll } from "./write_all.ts";
/**
 * @deprecated (will be removed after 1.0.0) Convert to ReadableStream using ReadableStream.from instead.
 *
 * Create a `Reader` from an iterable of `Uint8Array`s.
 *
 * ```ts
 *      import { readerFromIterable } from "https://deno.land/std@$STD_VERSION/streams/reader_from_iterable.ts";
 *      import { copy } from "https://deno.land/std@$STD_VERSION/streams/copy.ts";
 *
 *      const file = await Deno.open("metrics.txt", { write: true });
 *      const reader = readerFromIterable((async function* () {
 *        while (true) {
 *          await new Promise((r) => setTimeout(r, 1000));
 *          const message = `data: ${JSON.stringify(Deno.metrics())}\n\n`;
 *          yield new TextEncoder().encode(message);
 *        }
 *      })());
 *      await copy(reader, file);
 * ```
 */ export function readerFromIterable(iterable) {
  const iterator = iterable[Symbol.asyncIterator]?.() ?? iterable[Symbol.iterator]?.();
  const buffer = new Buffer();
  return {
    async read (p) {
      if (buffer.length === 0) {
        const result = await iterator.next();
        if (result.done) {
          return null;
        } else {
          if (result.value.byteLength <= p.byteLength) {
            p.set(result.value);
            return result.value.byteLength;
          }
          p.set(result.value.subarray(0, p.byteLength));
          await writeAll(buffer, result.value.subarray(p.byteLength));
          return p.byteLength;
        }
      } else {
        const n = await buffer.read(p);
        if (n === null) {
          return this.read(p);
        }
        return n;
      }
    }
  };
}
//# sourceMappingURL=data:application/json;base64,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