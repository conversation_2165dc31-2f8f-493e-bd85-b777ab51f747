// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given selector to all elements of the provided collection and
 * returns the max value of all elements. If an empty array is provided the
 * function will return undefined
 *
 * @example
 * ```ts
 * import { maxOf } from "https://deno.land/std@$STD_VERSION/collections/max_of.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const inventory = [
 *   { name: "mustard", count: 2 },
 *   { name: "soy", count: 4 },
 *   { name: "tomato", count: 32 },
 * ];
 *
 * const maxCount = maxOf(inventory, (i) => i.count);
 *
 * assertEquals(maxCount, 32);
 * ```
 */ export function maxOf(array, selector) {
  let maximumValue = undefined;
  for (const i of array){
    const currentValue = selector(i);
    if (maximumValue === undefined || currentValue > maximumValue) {
      maximumValue = currentValue;
      continue;
    }
    if (Number.isNaN(currentValue)) {
      return currentValue;
    }
  }
  return maximumValue;
}
//# sourceMappingURL=data:application/json;base64,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