// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { ParserFactory, Toml } from "./_parser.ts";
/**
 * Parse parses TOML string into an object.
 * @param tomlString
 */ export const parse = ParserFactory(Toml);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3RvbWwvcGFyc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuaW1wb3J0IHsgUGFyc2VyRmFjdG9yeSwgVG9tbCB9IGZyb20gXCIuL19wYXJzZXIudHNcIjtcblxuLyoqXG4gKiBQYXJzZSBwYXJzZXMgVE9NTCBzdHJpbmcgaW50byBhbiBvYmplY3QuXG4gKiBAcGFyYW0gdG9tbFN0cmluZ1xuICovXG5leHBvcnQgY29uc3QgcGFyc2UgPSBQYXJzZXJGYWN0b3J5KFRvbWwpO1xuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsU0FBUyxhQUFhLEVBQUUsSUFBSSxRQUFRLGVBQWU7QUFFbkQ7OztDQUdDLEdBQ0QsT0FBTyxNQUFNLFFBQVEsY0FBYyxNQUFNIn0=