// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
export async function toBlob(readableStream) {
  const reader = readableStream.getReader();
  const chunks = [];
  while(true){
    const { done, value } = await reader.read();
    if (done) {
      break;
    }
    chunks.push(value);
  }
  return new Blob(chunks);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3N0cmVhbXMvdG9fYmxvYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdG9CbG9iKFxuICByZWFkYWJsZVN0cmVhbTogUmVhZGFibGVTdHJlYW0sXG4pOiBQcm9taXNlPEJsb2I+IHtcbiAgY29uc3QgcmVhZGVyID0gcmVhZGFibGVTdHJlYW0uZ2V0UmVhZGVyKCk7XG4gIGNvbnN0IGNodW5rczogVWludDhBcnJheVtdID0gW107XG5cbiAgd2hpbGUgKHRydWUpIHtcbiAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuXG4gICAgaWYgKGRvbmUpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGNodW5rcy5wdXNoKHZhbHVlKTtcbiAgfVxuXG4gIHJldHVybiBuZXcgQmxvYihjaHVua3MpO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsT0FBTyxlQUFlLE9BQ3BCLGNBQThCO0VBRTlCLE1BQU0sU0FBUyxlQUFlLFNBQVM7RUFDdkMsTUFBTSxTQUF1QixFQUFFO0VBRS9CLE1BQU8sS0FBTTtJQUNYLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLEdBQUcsTUFBTSxPQUFPLElBQUk7SUFFekMsSUFBSSxNQUFNO01BQ1I7SUFDRjtJQUVBLE9BQU8sSUFBSSxDQUFDO0VBQ2Q7RUFFQSxPQUFPLElBQUksS0FBSztBQUNsQiJ9