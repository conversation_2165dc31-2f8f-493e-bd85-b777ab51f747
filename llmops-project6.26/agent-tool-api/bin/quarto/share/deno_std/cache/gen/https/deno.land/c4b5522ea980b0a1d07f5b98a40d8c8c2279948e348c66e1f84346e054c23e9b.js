// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { bytesToUuid, uuidToBytes } from "./_common.ts";
import { concat } from "../bytes/concat.ts";
import { assert } from "../assert/assert.ts";
import { crypto } from "../crypto/crypto.ts";
const UUID_RE = /^[0-9a-f]{8}-[0-9a-f]{4}-[3][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
/**
 * Validate that the passed UUID is an RFC4122 v3 UUID.
 *
 * @example
 * ```ts
 * import { generate as generateV3, validate } from "https://deno.land/std@$STD_VERSION/uuid/v3.ts";
 *
 * validate(await generateV3("6ba7b811-9dad-11d1-80b4-00c04fd430c8", new Uint8Array())); // true
 * validate(crypto.randomUUID()); // false
 * validate("this-is-not-a-uuid"); // false
 * ```
 */ export function validate(id) {
  return UUID_RE.test(id);
}
/**
 * Generate a RFC4122 v3 UUID (MD5 namespace).
 *
 * @example
 * ```js
 * import { generate } from "https://deno.land/std@$STD_VERSION/uuid/v3.ts";
 *
 * const NAMESPACE_URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
 *
 * const uuid = await generate(NAMESPACE_URL, new TextEncoder().encode("python.org"));
 * uuid === "22fe6191-c161-3d86-a432-a81f343eda08" // true
 * ```
 *
 * @param namespace The namespace to use, encoded as a UUID.
 * @param data The data to hash to calculate the MD5 digest for the UUID.
 */ export async function generate(namespace, data) {
  // TODO(lino-levan): validate that `namespace` is a valid UUID.
  const space = uuidToBytes(namespace);
  assert(space.length === 16, "namespace must be a valid UUID");
  const toHash = concat(new Uint8Array(space), data);
  const buffer = await crypto.subtle.digest("MD5", toHash);
  const bytes = new Uint8Array(buffer);
  bytes[6] = bytes[6] & 0x0f | 0x30;
  bytes[8] = bytes[8] & 0x3f | 0x80;
  return bytesToUuid(bytes);
}
//# sourceMappingURL=data:application/json;base64,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