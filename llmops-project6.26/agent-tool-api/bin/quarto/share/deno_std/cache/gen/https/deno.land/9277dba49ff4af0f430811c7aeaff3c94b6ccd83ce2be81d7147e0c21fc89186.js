// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given selector to each element in the given array, returning a
 * Record containing the results as keys and all values that produced that key
 * as values.
 *
 * @example
 * ```ts
 * import { groupBy } from "https://deno.land/std@$STD_VERSION/collections/group_by.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = [
 *   { name: "<PERSON>" },
 *   { name: "<PERSON>" },
 *   { name: "<PERSON>" },
 * ];
 * const peopleByFirstLetter = groupBy(people, (it) => it.name.charAt(0));
 *
 * assertEquals(
 *   peopleByFirstLetter,
 *   {
 *     "A": [{ name: "<PERSON>" }, { name: "<PERSON>" }],
 *     "K": [{ name: "<PERSON>" }],
 *   },
 * );
 * ```
 */ export function groupBy(iterable, selector) {
  const ret = {};
  let i = 0;
  for (const element of iterable){
    const key = selector(element, i++);
    const arr = ret[key] ??= [];
    arr.push(element);
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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