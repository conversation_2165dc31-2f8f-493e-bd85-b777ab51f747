// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given selector to all elements of the given collection and
 * returns the min value of all elements. If an empty array is provided the
 * function will return undefined.
 *
 * @example
 * ```ts
 * import { minOf } from "https://deno.land/std@$STD_VERSION/collections/min_of.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const inventory = [
 *   { name: "mustard", count: 2 },
 *   { name: "soy", count: 4 },
 *   { name: "tomato", count: 32 },
 * ];
 * const minCount = minOf(inventory, (i) => i.count);
 *
 * assertEquals(minCount, 2);
 * ```
 */ export function minOf(array, selector) {
  let minimumValue = undefined;
  for (const i of array){
    const currentValue = selector(i);
    if (minimumValue === undefined || currentValue < minimumValue) {
      minimumValue = currentValue;
      continue;
    }
    if (Number.isNaN(currentValue)) {
      return currentValue;
    }
  }
  return minimumValue;
}
//# sourceMappingURL=data:application/json;base64,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