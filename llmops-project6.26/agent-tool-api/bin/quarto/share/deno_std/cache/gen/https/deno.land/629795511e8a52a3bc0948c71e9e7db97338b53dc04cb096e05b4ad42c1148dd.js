// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { ascend } from "./comparators.ts";
import { BinarySearchTree } from "./binary_search_tree.ts";
import { RedBlackNode } from "./_red_black_node.ts";
/**
 * A red-black tree. This is a kind of self-balancing binary search tree. The
 * values are in ascending order by default, using JavaScript's built-in
 * comparison operators to sort the values.
 *
 * Red-Black Trees require fewer rotations than AVL Trees, so they can provide
 * faster insertions and removal operations. If you need faster lookups, you
 * should use an AVL Tree instead. AVL Trees are more strictly balanced than
 * Red-Black Trees, so they can provide faster lookups.
 *
 * | Method        | Average Case | Worst Case |
 * | ------------- | ------------ | ---------- |
 * | find(value)   | O(log n)     | O(log n)   |
 * | insert(value) | O(log n)     | O(log n)   |
 * | remove(value) | O(log n)     | O(log n)   |
 * | min()         | O(log n)     | O(log n)   |
 * | max()         | O(log n)     | O(log n)   |
 *
 * @example
 * ```ts
 * import {
 *   ascend,
 *   descend,
 *   RedBlackTree,
 * } from "https://deno.land/std@$STD_VERSION/collections/red_black_tree.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const values = [3, 10, 13, 4, 6, 7, 1, 14];
 * const tree = new RedBlackTree<number>();
 * values.forEach((value) => tree.insert(value));
 * assertEquals([...tree], [1, 3, 4, 6, 7, 10, 13, 14]);
 * assertEquals(tree.min(), 1);
 * assertEquals(tree.max(), 14);
 * assertEquals(tree.find(42), null);
 * assertEquals(tree.find(7), 7);
 * assertEquals(tree.remove(42), false);
 * assertEquals(tree.remove(7), true);
 * assertEquals([...tree], [1, 3, 4, 6, 10, 13, 14]);
 *
 * const invertedTree = new RedBlackTree<number>(descend);
 * values.forEach((value) => invertedTree.insert(value));
 * assertEquals([...invertedTree], [14, 13, 10, 7, 6, 4, 3, 1]);
 * assertEquals(invertedTree.min(), 14);
 * assertEquals(invertedTree.max(), 1);
 * assertEquals(invertedTree.find(42), null);
 * assertEquals(invertedTree.find(7), 7);
 * assertEquals(invertedTree.remove(42), false);
 * assertEquals(invertedTree.remove(7), true);
 * assertEquals([...invertedTree], [14, 13, 10, 6, 4, 3, 1]);
 *
 * const words = new RedBlackTree<string>((a, b) =>
 *   ascend(a.length, b.length) || ascend(a, b)
 * );
 * ["truck", "car", "helicopter", "tank", "train", "suv", "semi", "van"]
 *   .forEach((value) => words.insert(value));
 * assertEquals([...words], [
 *   "car",
 *   "suv",
 *   "van",
 *   "semi",
 *   "tank",
 *   "train",
 *   "truck",
 *   "helicopter",
 * ]);
 * assertEquals(words.min(), "car");
 * assertEquals(words.max(), "helicopter");
 * assertEquals(words.find("scooter"), null);
 * assertEquals(words.find("tank"), "tank");
 * assertEquals(words.remove("scooter"), false);
 * assertEquals(words.remove("tank"), true);
 * assertEquals([...words], [
 *   "car",
 *   "suv",
 *   "van",
 *   "semi",
 *   "train",
 *   "truck",
 *   "helicopter",
 * ]);
 * ```
 */ export class RedBlackTree extends BinarySearchTree {
  constructor(compare = ascend){
    super(compare);
  }
  static from(collection, options) {
    let result;
    let unmappedValues = [];
    if (collection instanceof RedBlackTree) {
      result = new RedBlackTree(options?.compare ?? collection.compare);
      if (options?.compare || options?.map) {
        unmappedValues = collection;
      } else {
        const nodes = [];
        if (collection.root) {
          result.root = RedBlackNode.from(collection.root);
          nodes.push(result.root);
        }
        while(nodes.length){
          const node = nodes.pop();
          const left = node.left ? RedBlackNode.from(node.left) : null;
          const right = node.right ? RedBlackNode.from(node.right) : null;
          if (left) {
            left.parent = node;
            nodes.push(left);
          }
          if (right) {
            right.parent = node;
            nodes.push(right);
          }
        }
      }
    } else {
      result = options?.compare ? new RedBlackTree(options.compare) : new RedBlackTree();
      unmappedValues = collection;
    }
    const values = options?.map ? Array.from(unmappedValues, options.map, options.thisArg) : unmappedValues;
    for (const value of values)result.insert(value);
    return result;
  }
  removeFixup(parent, current) {
    while(parent && !current?.red){
      const direction = parent.left === current ? "left" : "right";
      const siblingDirection = direction === "right" ? "left" : "right";
      let sibling = parent[siblingDirection];
      if (sibling?.red) {
        sibling.red = false;
        parent.red = true;
        this.rotateNode(parent, direction);
        sibling = parent[siblingDirection];
      }
      if (sibling) {
        if (!sibling.left?.red && !sibling.right?.red) {
          sibling.red = true;
          current = parent;
          parent = current.parent;
        } else {
          if (!sibling[siblingDirection]?.red) {
            sibling[direction].red = false;
            sibling.red = true;
            this.rotateNode(sibling, siblingDirection);
            sibling = parent[siblingDirection];
          }
          sibling.red = parent.red;
          parent.red = false;
          sibling[siblingDirection].red = false;
          this.rotateNode(parent, direction);
          current = this.root;
          parent = null;
        }
      }
    }
    if (current) current.red = false;
  }
  /**
   * Adds the value to the binary search tree if it does not already exist in it.
   * Returns true if successful.
   */ insert(value) {
    let node = this.insertNode(RedBlackNode, value);
    if (node) {
      while(node.parent?.red){
        let parent = node.parent;
        const parentDirection = parent.directionFromParent();
        const uncleDirection = parentDirection === "right" ? "left" : "right";
        const uncle = parent.parent[uncleDirection] ?? null;
        if (uncle?.red) {
          parent.red = false;
          uncle.red = false;
          parent.parent.red = true;
          node = parent.parent;
        } else {
          if (node === parent[uncleDirection]) {
            node = parent;
            this.rotateNode(node, parentDirection);
            parent = node.parent;
          }
          parent.red = false;
          parent.parent.red = true;
          this.rotateNode(parent.parent, uncleDirection);
        }
      }
      this.root.red = false;
    }
    return !!node;
  }
  /**
   * Removes node value from the binary search tree if found.
   * Returns true if found and removed.
   */ remove(value) {
    const node = this.findNode(value);
    if (!node) {
      return false;
    }
    const removedNode = this.removeNode(node);
    if (removedNode && !removedNode.red) {
      this.removeFixup(removedNode.parent, removedNode.left ?? removedNode.right);
    }
    return true;
  }
}
//# sourceMappingURL=data:application/json;base64,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