// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * Helpers for interacting with <PERSON><PERSON>'s permissions system.
 * @module
 * @deprecated (will be removed in 1.0.0) Use the Deno permission API instead
 */ const { PermissionDenied } = Deno.errors;
function getPermissionString(descriptors) {
  return descriptors.length ? `  ${descriptors.map((pd)=>{
    switch(pd.name){
      case "read":
      case "write":
        return pd.path ? `--allow-${pd.name}=${pd.path}` : `--allow-${pd.name}`;
      case "net":
        return pd.host ? `--allow-${pd.name}=${pd.host}` : `--allow-${pd.name}`;
      default:
        return `--allow-${pd.name}`;
    }
  }).join("\n  ")}` : "";
}
export async function grant(descriptor, ...descriptors) {
  const result = [];
  descriptors = Array.isArray(descriptor) ? descriptor : [
    descriptor,
    ...descriptors
  ];
  for (const descriptor of descriptors){
    let state = (await Deno.permissions.query(descriptor)).state;
    if (state === "prompt") {
      state = (await Deno.permissions.request(descriptor)).state;
    }
    if (state === "granted") {
      result.push(descriptor);
    }
  }
  return result.length ? result : undefined;
}
export async function grantOrThrow(descriptor, ...descriptors) {
  const denied = [];
  descriptors = Array.isArray(descriptor) ? descriptor : [
    descriptor,
    ...descriptors
  ];
  for (const descriptor of descriptors){
    const { state } = await Deno.permissions.request(descriptor);
    if (state !== "granted") {
      denied.push(descriptor);
    }
  }
  if (denied.length) {
    throw new PermissionDenied(`The following permissions have not been granted:\n${getPermissionString(denied)}`);
  }
}
//# sourceMappingURL=data:application/json;base64,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