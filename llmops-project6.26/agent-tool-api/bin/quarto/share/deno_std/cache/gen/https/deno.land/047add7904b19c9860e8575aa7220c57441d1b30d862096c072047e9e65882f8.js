// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns all elements in the given array after the last element that does not
 * match the given predicate.
 *
 * @example
 * ```ts
 * import { takeLastWhile } from "https://deno.land/std@$STD_VERSION/collections/take_last_while.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const arr = [1, 2, 3, 4, 5, 6];
 *
 * assertEquals(
 *   takeLastWhile(arr, (i) => i > 4),
 *   [5, 6],
 * );
 * ```
 */ export function takeLastWhile(array, predicate) {
  let offset = array.length;
  while(0 < offset && predicate(array[offset - 1]))offset--;
  return array.slice(offset, array.length);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3Rha2VfbGFzdF93aGlsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG4vKipcbiAqIFJldHVybnMgYWxsIGVsZW1lbnRzIGluIHRoZSBnaXZlbiBhcnJheSBhZnRlciB0aGUgbGFzdCBlbGVtZW50IHRoYXQgZG9lcyBub3RcbiAqIG1hdGNoIHRoZSBnaXZlbiBwcmVkaWNhdGUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyB0YWtlTGFzdFdoaWxlIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vY29sbGVjdGlvbnMvdGFrZV9sYXN0X3doaWxlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IGFyciA9IFsxLCAyLCAzLCA0LCA1LCA2XTtcbiAqXG4gKiBhc3NlcnRFcXVhbHMoXG4gKiAgIHRha2VMYXN0V2hpbGUoYXJyLCAoaSkgPT4gaSA+IDQpLFxuICogICBbNSwgNl0sXG4gKiApO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWtlTGFzdFdoaWxlPFQ+KFxuICBhcnJheTogcmVhZG9ubHkgVFtdLFxuICBwcmVkaWNhdGU6IChlbDogVCkgPT4gYm9vbGVhbixcbik6IFRbXSB7XG4gIGxldCBvZmZzZXQgPSBhcnJheS5sZW5ndGg7XG4gIHdoaWxlICgwIDwgb2Zmc2V0ICYmIHByZWRpY2F0ZShhcnJheVtvZmZzZXQgLSAxXSkpIG9mZnNldC0tO1xuXG4gIHJldHVybiBhcnJheS5zbGljZShvZmZzZXQsIGFycmF5Lmxlbmd0aCk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7Ozs7OztDQWdCQyxHQUNELE9BQU8sU0FBUyxjQUNkLEtBQW1CLEVBQ25CLFNBQTZCO0VBRTdCLElBQUksU0FBUyxNQUFNLE1BQU07RUFDekIsTUFBTyxJQUFJLFVBQVUsVUFBVSxLQUFLLENBQUMsU0FBUyxFQUFFLEVBQUc7RUFFbkQsT0FBTyxNQUFNLEtBQUssQ0FBQyxRQUFRLE1BQU0sTUFBTTtBQUN6QyJ9