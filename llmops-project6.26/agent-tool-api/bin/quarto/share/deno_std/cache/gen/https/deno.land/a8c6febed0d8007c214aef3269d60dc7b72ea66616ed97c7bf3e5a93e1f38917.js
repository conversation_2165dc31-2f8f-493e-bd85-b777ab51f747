// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { checkIdentifier, compareIdentifier, compareNumber } from "./_shared.ts";
/**
 * Compare two semantic version objects including build metadata.
 *
 * Returns `0` if `v1 === v2`, or `1` if `v1` is greater, or `-1` if `v2` is
 * greater.
 *
 * Sorts in ascending order if passed to `Array.sort()`,
 * @param s0
 * @param s1
 * @returns
 */ export function compareBuild(s0, s1) {
  if (s0 === s1) return 0;
  return compareNumber(s0.major, s1.major) || compareNumber(s0.minor, s1.minor) || compareNumber(s0.patch, s1.patch) || checkIdentifier(s0.prerelease, s1.prerelease) || compareIdentifier(s0.prerelease, s1.prerelease) || checkIdentifier(s1.build, s0.build) || compareIdentifier(s0.build, s1.build);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9jb21wYXJlX2J1aWxkLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG5pbXBvcnQgdHlwZSB7IFNlbVZlciB9IGZyb20gXCIuL3R5cGVzLnRzXCI7XG5pbXBvcnQge1xuICBjaGVja0lkZW50aWZpZXIsXG4gIGNvbXBhcmVJZGVudGlmaWVyLFxuICBjb21wYXJlTnVtYmVyLFxufSBmcm9tIFwiLi9fc2hhcmVkLnRzXCI7XG5cbi8qKlxuICogQ29tcGFyZSB0d28gc2VtYW50aWMgdmVyc2lvbiBvYmplY3RzIGluY2x1ZGluZyBidWlsZCBtZXRhZGF0YS5cbiAqXG4gKiBSZXR1cm5zIGAwYCBpZiBgdjEgPT09IHYyYCwgb3IgYDFgIGlmIGB2MWAgaXMgZ3JlYXRlciwgb3IgYC0xYCBpZiBgdjJgIGlzXG4gKiBncmVhdGVyLlxuICpcbiAqIFNvcnRzIGluIGFzY2VuZGluZyBvcmRlciBpZiBwYXNzZWQgdG8gYEFycmF5LnNvcnQoKWAsXG4gKiBAcGFyYW0gczBcbiAqIEBwYXJhbSBzMVxuICogQHJldHVybnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbXBhcmVCdWlsZChcbiAgczA6IFNlbVZlcixcbiAgczE6IFNlbVZlcixcbik6IDEgfCAwIHwgLTEge1xuICBpZiAoczAgPT09IHMxKSByZXR1cm4gMDtcbiAgcmV0dXJuIChcbiAgICBjb21wYXJlTnVtYmVyKHMwLm1ham9yLCBzMS5tYWpvcikgfHxcbiAgICBjb21wYXJlTnVtYmVyKHMwLm1pbm9yLCBzMS5taW5vcikgfHxcbiAgICBjb21wYXJlTnVtYmVyKHMwLnBhdGNoLCBzMS5wYXRjaCkgfHxcbiAgICBjaGVja0lkZW50aWZpZXIoczAucHJlcmVsZWFzZSwgczEucHJlcmVsZWFzZSkgfHxcbiAgICBjb21wYXJlSWRlbnRpZmllcihzMC5wcmVyZWxlYXNlLCBzMS5wcmVyZWxlYXNlKSB8fFxuICAgIGNoZWNrSWRlbnRpZmllcihzMS5idWlsZCwgczAuYnVpbGQpIHx8XG4gICAgY29tcGFyZUlkZW50aWZpZXIoczAuYnVpbGQsIHMxLmJ1aWxkKVxuICApO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUNFLGVBQWUsRUFDZixpQkFBaUIsRUFDakIsYUFBYSxRQUNSLGVBQWU7QUFFdEI7Ozs7Ozs7Ozs7Q0FVQyxHQUNELE9BQU8sU0FBUyxhQUNkLEVBQVUsRUFDVixFQUFVO0VBRVYsSUFBSSxPQUFPLElBQUksT0FBTztFQUN0QixPQUNFLGNBQWMsR0FBRyxLQUFLLEVBQUUsR0FBRyxLQUFLLEtBQ2hDLGNBQWMsR0FBRyxLQUFLLEVBQUUsR0FBRyxLQUFLLEtBQ2hDLGNBQWMsR0FBRyxLQUFLLEVBQUUsR0FBRyxLQUFLLEtBQ2hDLGdCQUFnQixHQUFHLFVBQVUsRUFBRSxHQUFHLFVBQVUsS0FDNUMsa0JBQWtCLEdBQUcsVUFBVSxFQUFFLEdBQUcsVUFBVSxLQUM5QyxnQkFBZ0IsR0FBRyxLQUFLLEVBQUUsR0FBRyxLQUFLLEtBQ2xDLGtCQUFrQixHQUFHLEtBQUssRUFBRSxHQUFHLEtBQUs7QUFFeEMifQ==