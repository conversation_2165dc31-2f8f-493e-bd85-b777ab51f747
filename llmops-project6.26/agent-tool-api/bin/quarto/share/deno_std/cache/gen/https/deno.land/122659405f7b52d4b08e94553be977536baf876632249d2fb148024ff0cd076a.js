// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { deepMerge } from "../collections/deep_merge.ts";
export class TOMLParseError extends Error {
}
export class Scanner {
  source;
  #whitespace;
  #position;
  constructor(source){
    this.source = source;
    this.#whitespace = /[ \t]/;
    this.#position = 0;
  }
  /**
   * Get current character
   * @param index - relative index from current position
   */ char(index = 0) {
    return this.source[this.#position + index] ?? "";
  }
  /**
   * Get sliced string
   * @param start - start position relative from current position
   * @param end - end position relative from current position
   */ slice(start, end) {
    return this.source.slice(this.#position + start, this.#position + end);
  }
  /**
   * Move position to next
   */ next(count) {
    if (typeof count === "number") {
      for(let i = 0; i < count; i++){
        this.#position++;
      }
    } else {
      this.#position++;
    }
  }
  /**
   * Move position until current char is not a whitespace, EOL, or comment.
   * @param options.inline - skip only whitespaces
   */ nextUntilChar(options = {
    comment: true
  }) {
    if (options.inline) {
      while(this.#whitespace.test(this.char()) && !this.eof()){
        this.next();
      }
    } else {
      while(!this.eof()){
        const char = this.char();
        if (this.#whitespace.test(char) || this.isCurrentCharEOL()) {
          this.next();
        } else if (options.comment && this.char() === "#") {
          // entering comment
          while(!this.isCurrentCharEOL() && !this.eof()){
            this.next();
          }
        } else {
          break;
        }
      }
    }
    // Invalid if current char is other kinds of whitespace
    if (!this.isCurrentCharEOL() && /\s/.test(this.char())) {
      const escaped = "\\u" + this.char().charCodeAt(0).toString(16);
      throw new TOMLParseError(`Contains invalid whitespaces: \`${escaped}\``);
    }
  }
  /**
   * Position reached EOF or not
   */ eof() {
    return this.position() >= this.source.length;
  }
  /**
   * Get current position
   */ position() {
    return this.#position;
  }
  isCurrentCharEOL() {
    return this.char() === "\n" || this.slice(0, 2) === "\r\n";
  }
}
// -----------------------
// Utilities
// -----------------------
function success(body) {
  return {
    ok: true,
    body
  };
}
function failure() {
  return {
    ok: false
  };
}
export const Utils = {
  unflat (keys, values = {}, cObj) {
    const out = {};
    if (keys.length === 0) {
      return cObj;
    } else {
      if (!cObj) {
        cObj = values;
      }
      const key = keys[keys.length - 1];
      if (typeof key === "string") {
        out[key] = cObj;
      }
      return this.unflat(keys.slice(0, -1), values, out);
    }
  },
  deepAssignWithTable (target, table) {
    if (table.key.length === 0) {
      throw new Error("Unexpected key length");
    }
    const value = target[table.key[0]];
    if (typeof value === "undefined") {
      Object.assign(target, this.unflat(table.key, table.type === "Table" ? table.value : [
        table.value
      ]));
    } else if (Array.isArray(value)) {
      if (table.type === "TableArray" && table.key.length === 1) {
        value.push(table.value);
      } else {
        const last = value[value.length - 1];
        Utils.deepAssignWithTable(last, {
          type: table.type,
          key: table.key.slice(1),
          value: table.value
        });
      }
    } else if (typeof value === "object" && value !== null) {
      Utils.deepAssignWithTable(value, {
        type: table.type,
        key: table.key.slice(1),
        value: table.value
      });
    } else {
      throw new Error("Unexpected assign");
    }
  }
};
// ---------------------------------
// Parser combinators and generators
// ---------------------------------
function or(parsers) {
  return function Or(scanner) {
    for (const parse of parsers){
      const result = parse(scanner);
      if (result.ok) {
        return result;
      }
    }
    return failure();
  };
}
function join(parser, separator) {
  const Separator = character(separator);
  return function Join(scanner) {
    const first = parser(scanner);
    if (!first.ok) {
      return failure();
    }
    const out = [
      first.body
    ];
    while(!scanner.eof()){
      if (!Separator(scanner).ok) {
        break;
      }
      const result = parser(scanner);
      if (result.ok) {
        out.push(result.body);
      } else {
        throw new TOMLParseError(`Invalid token after "${separator}"`);
      }
    }
    return success(out);
  };
}
function kv(keyParser, separator, valueParser) {
  const Separator = character(separator);
  return function Kv(scanner) {
    const key = keyParser(scanner);
    if (!key.ok) {
      return failure();
    }
    const sep = Separator(scanner);
    if (!sep.ok) {
      throw new TOMLParseError(`key/value pair doesn't have "${separator}"`);
    }
    const value = valueParser(scanner);
    if (!value.ok) {
      throw new TOMLParseError(`Value of key/value pair is invalid data format`);
    }
    return success(Utils.unflat(key.body, value.body));
  };
}
function merge(parser) {
  return function Merge(scanner) {
    const result = parser(scanner);
    if (!result.ok) {
      return failure();
    }
    let body = {};
    for (const record of result.body){
      if (typeof body === "object" && body !== null) {
        // deno-lint-ignore no-explicit-any
        body = deepMerge(body, record);
      }
    }
    return success(body);
  };
}
function repeat(parser) {
  return function Repeat(scanner) {
    const body = [];
    while(!scanner.eof()){
      const result = parser(scanner);
      if (result.ok) {
        body.push(result.body);
      } else {
        break;
      }
      scanner.nextUntilChar();
    }
    if (body.length === 0) {
      return failure();
    }
    return success(body);
  };
}
function surround(left, parser, right) {
  const Left = character(left);
  const Right = character(right);
  return function Surround(scanner) {
    if (!Left(scanner).ok) {
      return failure();
    }
    const result = parser(scanner);
    if (!result.ok) {
      throw new TOMLParseError(`Invalid token after "${left}"`);
    }
    if (!Right(scanner).ok) {
      throw new TOMLParseError(`Not closed by "${right}" after started with "${left}"`);
    }
    return success(result.body);
  };
}
function character(str) {
  return function character(scanner) {
    scanner.nextUntilChar({
      inline: true
    });
    if (scanner.slice(0, str.length) === str) {
      scanner.next(str.length);
    } else {
      return failure();
    }
    scanner.nextUntilChar({
      inline: true
    });
    return success(undefined);
  };
}
// -----------------------
// Parser components
// -----------------------
const Patterns = {
  BARE_KEY: /[A-Za-z0-9_-]/,
  FLOAT: /[0-9_\.e+\-]/i,
  END_OF_VALUE: /[ \t\r\n#,}\]]/
};
export function BareKey(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (!scanner.char() || !Patterns.BARE_KEY.test(scanner.char())) {
    return failure();
  }
  const acc = [];
  while(scanner.char() && Patterns.BARE_KEY.test(scanner.char())){
    acc.push(scanner.char());
    scanner.next();
  }
  const key = acc.join("");
  return success(key);
}
function EscapeSequence(scanner) {
  if (scanner.char() === "\\") {
    scanner.next();
    // See https://toml.io/en/v1.0.0-rc.3#string
    switch(scanner.char()){
      case "b":
        scanner.next();
        return success("\b");
      case "t":
        scanner.next();
        return success("\t");
      case "n":
        scanner.next();
        return success("\n");
      case "f":
        scanner.next();
        return success("\f");
      case "r":
        scanner.next();
        return success("\r");
      case "u":
      case "U":
        {
          // Unicode character
          const codePointLen = scanner.char() === "u" ? 4 : 6;
          const codePoint = parseInt("0x" + scanner.slice(1, 1 + codePointLen), 16);
          const str = String.fromCodePoint(codePoint);
          scanner.next(codePointLen + 1);
          return success(str);
        }
      case '"':
        scanner.next();
        return success('"');
      case "\\":
        scanner.next();
        return success("\\");
      default:
        scanner.next();
        return success(scanner.char());
    }
  } else {
    return failure();
  }
}
export function BasicString(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (scanner.char() === '"') {
    scanner.next();
  } else {
    return failure();
  }
  const acc = [];
  while(scanner.char() !== '"' && !scanner.eof()){
    if (scanner.char() === "\n") {
      throw new TOMLParseError("Single-line string cannot contain EOL");
    }
    const escapedChar = EscapeSequence(scanner);
    if (escapedChar.ok) {
      acc.push(escapedChar.body);
    } else {
      acc.push(scanner.char());
      scanner.next();
    }
  }
  if (scanner.eof()) {
    throw new TOMLParseError(`Single-line string is not closed:\n${acc.join("")}`);
  }
  scanner.next(); // skip last '""
  return success(acc.join(""));
}
export function LiteralString(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (scanner.char() === "'") {
    scanner.next();
  } else {
    return failure();
  }
  const acc = [];
  while(scanner.char() !== "'" && !scanner.eof()){
    if (scanner.char() === "\n") {
      throw new TOMLParseError("Single-line string cannot contain EOL");
    }
    acc.push(scanner.char());
    scanner.next();
  }
  if (scanner.eof()) {
    throw new TOMLParseError(`Single-line string is not closed:\n${acc.join("")}`);
  }
  scanner.next(); // skip last "'"
  return success(acc.join(""));
}
export function MultilineBasicString(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (scanner.slice(0, 3) === '"""') {
    scanner.next(3);
  } else {
    return failure();
  }
  if (scanner.char() === "\n") {
    // The first newline is trimmed
    scanner.next();
  }
  const acc = [];
  while(scanner.slice(0, 3) !== '"""' && !scanner.eof()){
    // line ending backslash
    if (scanner.slice(0, 2) === "\\\n") {
      scanner.next();
      scanner.nextUntilChar({
        comment: false
      });
      continue;
    }
    const escapedChar = EscapeSequence(scanner);
    if (escapedChar.ok) {
      acc.push(escapedChar.body);
    } else {
      acc.push(scanner.char());
      scanner.next();
    }
  }
  if (scanner.eof()) {
    throw new TOMLParseError(`Multi-line string is not closed:\n${acc.join("")}`);
  }
  // if ends with 4 `"`, push the fist `"` to string
  if (scanner.char(3) === '"') {
    acc.push('"');
    scanner.next();
  }
  scanner.next(3); // skip last '""""
  return success(acc.join(""));
}
export function MultilineLiteralString(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (scanner.slice(0, 3) === "'''") {
    scanner.next(3);
  } else {
    return failure();
  }
  if (scanner.char() === "\n") {
    // The first newline is trimmed
    scanner.next();
  }
  const acc = [];
  while(scanner.slice(0, 3) !== "'''" && !scanner.eof()){
    acc.push(scanner.char());
    scanner.next();
  }
  if (scanner.eof()) {
    throw new TOMLParseError(`Multi-line string is not closed:\n${acc.join("")}`);
  }
  // if ends with 4 `'`, push the fist `'` to string
  if (scanner.char(3) === "'") {
    acc.push("'");
    scanner.next();
  }
  scanner.next(3); // skip last "'''"
  return success(acc.join(""));
}
const symbolPairs = [
  [
    "true",
    true
  ],
  [
    "false",
    false
  ],
  [
    "inf",
    Infinity
  ],
  [
    "+inf",
    Infinity
  ],
  [
    "-inf",
    -Infinity
  ],
  [
    "nan",
    NaN
  ],
  [
    "+nan",
    NaN
  ],
  [
    "-nan",
    NaN
  ]
];
export function Symbols(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  const found = symbolPairs.find(([str])=>scanner.slice(0, str.length) === str);
  if (!found) {
    return failure();
  }
  const [str, value] = found;
  scanner.next(str.length);
  return success(value);
}
export const DottedKey = join(or([
  BareKey,
  BasicString,
  LiteralString
]), ".");
export function Integer(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  // If binary / octal / hex
  const first2 = scanner.slice(0, 2);
  if (first2.length === 2 && /0(?:x|o|b)/i.test(first2)) {
    scanner.next(2);
    const acc = [
      first2
    ];
    while(/[0-9a-f_]/i.test(scanner.char()) && !scanner.eof()){
      acc.push(scanner.char());
      scanner.next();
    }
    if (acc.length === 1) {
      return failure();
    }
    return success(acc.join(""));
  }
  const acc = [];
  if (/[+-]/.test(scanner.char())) {
    acc.push(scanner.char());
    scanner.next();
  }
  while(/[0-9_]/.test(scanner.char()) && !scanner.eof()){
    acc.push(scanner.char());
    scanner.next();
  }
  if (acc.length === 0 || acc.length === 1 && /[+-]/.test(acc[0])) {
    return failure();
  }
  const int = parseInt(acc.filter((char)=>char !== "_").join(""));
  return success(int);
}
export function Float(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  // lookahead validation is needed for integer value is similar to float
  let position = 0;
  while(scanner.char(position) && !Patterns.END_OF_VALUE.test(scanner.char(position))){
    if (!Patterns.FLOAT.test(scanner.char(position))) {
      return failure();
    }
    position++;
  }
  const acc = [];
  if (/[+-]/.test(scanner.char())) {
    acc.push(scanner.char());
    scanner.next();
  }
  while(Patterns.FLOAT.test(scanner.char()) && !scanner.eof()){
    acc.push(scanner.char());
    scanner.next();
  }
  if (acc.length === 0) {
    return failure();
  }
  const float = parseFloat(acc.filter((char)=>char !== "_").join(""));
  if (isNaN(float)) {
    return failure();
  }
  return success(float);
}
export function DateTime(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  let dateStr = scanner.slice(0, 10);
  // example: 1979-05-27
  if (/^\d{4}-\d{2}-\d{2}/.test(dateStr)) {
    scanner.next(10);
  } else {
    return failure();
  }
  const acc = [];
  // example: 1979-05-27T00:32:00Z
  while(/[ 0-9TZ.:-]/.test(scanner.char()) && !scanner.eof()){
    acc.push(scanner.char());
    scanner.next();
  }
  dateStr += acc.join("");
  const date = new Date(dateStr.trim());
  // invalid date
  if (isNaN(date.getTime())) {
    throw new TOMLParseError(`Invalid date string "${dateStr}"`);
  }
  return success(date);
}
export function LocalTime(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  let timeStr = scanner.slice(0, 8);
  if (/^(\d{2}):(\d{2}):(\d{2})/.test(timeStr)) {
    scanner.next(8);
  } else {
    return failure();
  }
  const acc = [];
  if (scanner.char() === ".") {
    acc.push(scanner.char());
    scanner.next();
  } else {
    return success(timeStr);
  }
  while(/[0-9]/.test(scanner.char()) && !scanner.eof()){
    acc.push(scanner.char());
    scanner.next();
  }
  timeStr += acc.join("");
  return success(timeStr);
}
export function ArrayValue(scanner) {
  scanner.nextUntilChar({
    inline: true
  });
  if (scanner.char() === "[") {
    scanner.next();
  } else {
    return failure();
  }
  const array = [];
  while(!scanner.eof()){
    scanner.nextUntilChar();
    const result = Value(scanner);
    if (result.ok) {
      array.push(result.body);
    } else {
      break;
    }
    scanner.nextUntilChar({
      inline: true
    });
    // may have a next item, but trailing comma is allowed at array
    if (scanner.char() === ",") {
      scanner.next();
    } else {
      break;
    }
  }
  scanner.nextUntilChar();
  if (scanner.char() === "]") {
    scanner.next();
  } else {
    throw new TOMLParseError("Array is not closed");
  }
  return success(array);
}
export function InlineTable(scanner) {
  scanner.nextUntilChar();
  if (scanner.char(1) === "}") {
    scanner.next(2);
    return success({});
  }
  const pairs = surround("{", join(Pair, ","), "}")(scanner);
  if (!pairs.ok) {
    return failure();
  }
  let table = {};
  for (const pair of pairs.body){
    table = deepMerge(table, pair);
  }
  return success(table);
}
export const Value = or([
  MultilineBasicString,
  MultilineLiteralString,
  BasicString,
  LiteralString,
  Symbols,
  DateTime,
  LocalTime,
  Float,
  Integer,
  ArrayValue,
  InlineTable
]);
export const Pair = kv(DottedKey, "=", Value);
export function Block(scanner) {
  scanner.nextUntilChar();
  const result = merge(repeat(Pair))(scanner);
  if (result.ok) {
    return success({
      type: "Block",
      value: result.body
    });
  } else {
    return failure();
  }
}
export const TableHeader = surround("[", DottedKey, "]");
export function Table(scanner) {
  scanner.nextUntilChar();
  const header = TableHeader(scanner);
  if (!header.ok) {
    return failure();
  }
  scanner.nextUntilChar();
  const block = Block(scanner);
  return success({
    type: "Table",
    key: header.body,
    value: block.ok ? block.body.value : {}
  });
}
export const TableArrayHeader = surround("[[", DottedKey, "]]");
export function TableArray(scanner) {
  scanner.nextUntilChar();
  const header = TableArrayHeader(scanner);
  if (!header.ok) {
    return failure();
  }
  scanner.nextUntilChar();
  const block = Block(scanner);
  return success({
    type: "TableArray",
    key: header.body,
    value: block.ok ? block.body.value : {}
  });
}
export function Toml(scanner) {
  const blocks = repeat(or([
    Block,
    TableArray,
    Table
  ]))(scanner);
  if (!blocks.ok) {
    return failure();
  }
  let body = {};
  for (const block of blocks.body){
    switch(block.type){
      case "Block":
        {
          body = deepMerge(body, block.value);
          break;
        }
      case "Table":
        {
          Utils.deepAssignWithTable(body, block);
          break;
        }
      case "TableArray":
        {
          Utils.deepAssignWithTable(body, block);
          break;
        }
    }
  }
  return success(body);
}
export function ParserFactory(parser) {
  return function parse(tomlString) {
    const scanner = new Scanner(tomlString);
    let parsed = null;
    let err = null;
    try {
      parsed = parser(scanner);
    } catch (e) {
      err = e instanceof Error ? e : new Error("[non-error thrown]");
    }
    if (err || !parsed || !parsed.ok || !scanner.eof()) {
      const position = scanner.position();
      const subStr = tomlString.slice(0, position);
      const lines = subStr.split("\n");
      const row = lines.length;
      const column = (()=>{
        let count = subStr.length;
        for (const line of lines){
          if (count > line.length) {
            count -= line.length + 1;
          } else {
            return count;
          }
        }
        return count;
      })();
      const message = `Parse error on line ${row}, column ${column}: ${err ? err.message : `Unexpected character: "${scanner.char()}"`}`;
      throw new TOMLParseError(message);
    }
    return parsed.body;
  };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3RvbWwvX3BhcnNlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5pbXBvcnQgeyBkZWVwTWVyZ2UgfSBmcm9tIFwiLi4vY29sbGVjdGlvbnMvZGVlcF9tZXJnZS50c1wiO1xuXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIEludGVyZmFjZXMgYW5kIGJhc2UgY2xhc3Nlc1xuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbmludGVyZmFjZSBTdWNjZXNzPFQ+IHtcbiAgb2s6IHRydWU7XG4gIGJvZHk6IFQ7XG59XG5pbnRlcmZhY2UgRmFpbHVyZSB7XG4gIG9rOiBmYWxzZTtcbn1cbnR5cGUgUGFyc2VSZXN1bHQ8VD4gPSBTdWNjZXNzPFQ+IHwgRmFpbHVyZTtcblxudHlwZSBQYXJzZXJDb21wb25lbnQ8VCA9IHVua25vd24+ID0gKHNjYW5uZXI6IFNjYW5uZXIpID0+IFBhcnNlUmVzdWx0PFQ+O1xuXG50eXBlIEJsb2NrUGFyc2VSZXN1bHRCb2R5ID0ge1xuICB0eXBlOiBcIkJsb2NrXCI7XG4gIHZhbHVlOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPjtcbn0gfCB7XG4gIHR5cGU6IFwiVGFibGVcIjtcbiAga2V5OiBzdHJpbmdbXTtcbiAgdmFsdWU6IFJlY29yZDxzdHJpbmcsIHVua25vd24+O1xufSB8IHtcbiAgdHlwZTogXCJUYWJsZUFycmF5XCI7XG4gIGtleTogc3RyaW5nW107XG4gIHZhbHVlOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPjtcbn07XG5cbmV4cG9ydCBjbGFzcyBUT01MUGFyc2VFcnJvciBleHRlbmRzIEVycm9yIHt9XG5cbmV4cG9ydCBjbGFzcyBTY2FubmVyIHtcbiAgI3doaXRlc3BhY2UgPSAvWyBcXHRdLztcbiAgI3Bvc2l0aW9uID0gMDtcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBzb3VyY2U6IHN0cmluZykge31cblxuICAvKipcbiAgICogR2V0IGN1cnJlbnQgY2hhcmFjdGVyXG4gICAqIEBwYXJhbSBpbmRleCAtIHJlbGF0aXZlIGluZGV4IGZyb20gY3VycmVudCBwb3NpdGlvblxuICAgKi9cbiAgY2hhcihpbmRleCA9IDApIHtcbiAgICByZXR1cm4gdGhpcy5zb3VyY2VbdGhpcy4jcG9zaXRpb24gKyBpbmRleF0gPz8gXCJcIjtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2xpY2VkIHN0cmluZ1xuICAgKiBAcGFyYW0gc3RhcnQgLSBzdGFydCBwb3NpdGlvbiByZWxhdGl2ZSBmcm9tIGN1cnJlbnQgcG9zaXRpb25cbiAgICogQHBhcmFtIGVuZCAtIGVuZCBwb3NpdGlvbiByZWxhdGl2ZSBmcm9tIGN1cnJlbnQgcG9zaXRpb25cbiAgICovXG4gIHNsaWNlKHN0YXJ0OiBudW1iZXIsIGVuZDogbnVtYmVyKTogc3RyaW5nIHtcbiAgICByZXR1cm4gdGhpcy5zb3VyY2Uuc2xpY2UodGhpcy4jcG9zaXRpb24gKyBzdGFydCwgdGhpcy4jcG9zaXRpb24gKyBlbmQpO1xuICB9XG5cbiAgLyoqXG4gICAqIE1vdmUgcG9zaXRpb24gdG8gbmV4dFxuICAgKi9cbiAgbmV4dChjb3VudD86IG51bWJlcikge1xuICAgIGlmICh0eXBlb2YgY291bnQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY291bnQ7IGkrKykge1xuICAgICAgICB0aGlzLiNwb3NpdGlvbisrO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLiNwb3NpdGlvbisrO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBNb3ZlIHBvc2l0aW9uIHVudGlsIGN1cnJlbnQgY2hhciBpcyBub3QgYSB3aGl0ZXNwYWNlLCBFT0wsIG9yIGNvbW1lbnQuXG4gICAqIEBwYXJhbSBvcHRpb25zLmlubGluZSAtIHNraXAgb25seSB3aGl0ZXNwYWNlc1xuICAgKi9cbiAgbmV4dFVudGlsQ2hhcihcbiAgICBvcHRpb25zOiB7IGlubGluZT86IGJvb2xlYW47IGNvbW1lbnQ/OiBib29sZWFuIH0gPSB7IGNvbW1lbnQ6IHRydWUgfSxcbiAgKSB7XG4gICAgaWYgKG9wdGlvbnMuaW5saW5lKSB7XG4gICAgICB3aGlsZSAodGhpcy4jd2hpdGVzcGFjZS50ZXN0KHRoaXMuY2hhcigpKSAmJiAhdGhpcy5lb2YoKSkge1xuICAgICAgICB0aGlzLm5leHQoKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgd2hpbGUgKCF0aGlzLmVvZigpKSB7XG4gICAgICAgIGNvbnN0IGNoYXIgPSB0aGlzLmNoYXIoKTtcbiAgICAgICAgaWYgKHRoaXMuI3doaXRlc3BhY2UudGVzdChjaGFyKSB8fCB0aGlzLmlzQ3VycmVudENoYXJFT0woKSkge1xuICAgICAgICAgIHRoaXMubmV4dCgpO1xuICAgICAgICB9IGVsc2UgaWYgKG9wdGlvbnMuY29tbWVudCAmJiB0aGlzLmNoYXIoKSA9PT0gXCIjXCIpIHtcbiAgICAgICAgICAvLyBlbnRlcmluZyBjb21tZW50XG4gICAgICAgICAgd2hpbGUgKCF0aGlzLmlzQ3VycmVudENoYXJFT0woKSAmJiAhdGhpcy5lb2YoKSkge1xuICAgICAgICAgICAgdGhpcy5uZXh0KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIC8vIEludmFsaWQgaWYgY3VycmVudCBjaGFyIGlzIG90aGVyIGtpbmRzIG9mIHdoaXRlc3BhY2VcbiAgICBpZiAoIXRoaXMuaXNDdXJyZW50Q2hhckVPTCgpICYmIC9cXHMvLnRlc3QodGhpcy5jaGFyKCkpKSB7XG4gICAgICBjb25zdCBlc2NhcGVkID0gXCJcXFxcdVwiICsgdGhpcy5jaGFyKCkuY2hhckNvZGVBdCgwKS50b1N0cmluZygxNik7XG4gICAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoYENvbnRhaW5zIGludmFsaWQgd2hpdGVzcGFjZXM6IFxcYCR7ZXNjYXBlZH1cXGBgKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUG9zaXRpb24gcmVhY2hlZCBFT0Ygb3Igbm90XG4gICAqL1xuICBlb2YoKSB7XG4gICAgcmV0dXJuIHRoaXMucG9zaXRpb24oKSA+PSB0aGlzLnNvdXJjZS5sZW5ndGg7XG4gIH1cblxuICAvKipcbiAgICogR2V0IGN1cnJlbnQgcG9zaXRpb25cbiAgICovXG4gIHBvc2l0aW9uKCkge1xuICAgIHJldHVybiB0aGlzLiNwb3NpdGlvbjtcbiAgfVxuXG4gIGlzQ3VycmVudENoYXJFT0woKSB7XG4gICAgcmV0dXJuIHRoaXMuY2hhcigpID09PSBcIlxcblwiIHx8IHRoaXMuc2xpY2UoMCwgMikgPT09IFwiXFxyXFxuXCI7XG4gIH1cbn1cblxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIFV0aWxpdGllc1xuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuZnVuY3Rpb24gc3VjY2VzczxUPihib2R5OiBUKTogU3VjY2VzczxUPiB7XG4gIHJldHVybiB7XG4gICAgb2s6IHRydWUsXG4gICAgYm9keSxcbiAgfTtcbn1cbmZ1bmN0aW9uIGZhaWx1cmUoKTogRmFpbHVyZSB7XG4gIHJldHVybiB7XG4gICAgb2s6IGZhbHNlLFxuICB9O1xufVxuXG5leHBvcnQgY29uc3QgVXRpbHMgPSB7XG4gIHVuZmxhdChcbiAgICBrZXlzOiBzdHJpbmdbXSxcbiAgICB2YWx1ZXM6IHVua25vd24gPSB7fSxcbiAgICBjT2JqPzogdW5rbm93bixcbiAgKTogUmVjb3JkPHN0cmluZywgdW5rbm93bj4ge1xuICAgIGNvbnN0IG91dDogUmVjb3JkPHN0cmluZywgdW5rbm93bj4gPSB7fTtcbiAgICBpZiAoa2V5cy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBjT2JqIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+O1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoIWNPYmopIHtcbiAgICAgICAgY09iaiA9IHZhbHVlcztcbiAgICAgIH1cbiAgICAgIGNvbnN0IGtleTogc3RyaW5nIHwgdW5kZWZpbmVkID0ga2V5c1trZXlzLmxlbmd0aCAtIDFdO1xuICAgICAgaWYgKHR5cGVvZiBrZXkgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgb3V0W2tleV0gPSBjT2JqO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRoaXMudW5mbGF0KGtleXMuc2xpY2UoMCwgLTEpLCB2YWx1ZXMsIG91dCk7XG4gICAgfVxuICB9LFxuICBkZWVwQXNzaWduV2l0aFRhYmxlKHRhcmdldDogUmVjb3JkPHN0cmluZywgdW5rbm93bj4sIHRhYmxlOiB7XG4gICAgdHlwZTogXCJUYWJsZVwiIHwgXCJUYWJsZUFycmF5XCI7XG4gICAga2V5OiBzdHJpbmdbXTtcbiAgICB2YWx1ZTogUmVjb3JkPHN0cmluZywgdW5rbm93bj47XG4gIH0pIHtcbiAgICBpZiAodGFibGUua2V5Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVW5leHBlY3RlZCBrZXkgbGVuZ3RoXCIpO1xuICAgIH1cbiAgICBjb25zdCB2YWx1ZSA9IHRhcmdldFt0YWJsZS5rZXlbMF1dO1xuXG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgT2JqZWN0LmFzc2lnbihcbiAgICAgICAgdGFyZ2V0LFxuICAgICAgICB0aGlzLnVuZmxhdChcbiAgICAgICAgICB0YWJsZS5rZXksXG4gICAgICAgICAgdGFibGUudHlwZSA9PT0gXCJUYWJsZVwiID8gdGFibGUudmFsdWUgOiBbdGFibGUudmFsdWVdLFxuICAgICAgICApLFxuICAgICAgKTtcbiAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICBpZiAodGFibGUudHlwZSA9PT0gXCJUYWJsZUFycmF5XCIgJiYgdGFibGUua2V5Lmxlbmd0aCA9PT0gMSkge1xuICAgICAgICB2YWx1ZS5wdXNoKHRhYmxlLnZhbHVlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGxhc3QgPSB2YWx1ZVt2YWx1ZS5sZW5ndGggLSAxXTtcbiAgICAgICAgVXRpbHMuZGVlcEFzc2lnbldpdGhUYWJsZShsYXN0LCB7XG4gICAgICAgICAgdHlwZTogdGFibGUudHlwZSxcbiAgICAgICAgICBrZXk6IHRhYmxlLmtleS5zbGljZSgxKSxcbiAgICAgICAgICB2YWx1ZTogdGFibGUudmFsdWUsXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSBcIm9iamVjdFwiICYmIHZhbHVlICE9PSBudWxsKSB7XG4gICAgICBVdGlscy5kZWVwQXNzaWduV2l0aFRhYmxlKHZhbHVlIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+LCB7XG4gICAgICAgIHR5cGU6IHRhYmxlLnR5cGUsXG4gICAgICAgIGtleTogdGFibGUua2V5LnNsaWNlKDEpLFxuICAgICAgICB2YWx1ZTogdGFibGUudmFsdWUsXG4gICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVW5leHBlY3RlZCBhc3NpZ25cIik7XG4gICAgfVxuICB9LFxufTtcblxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBQYXJzZXIgY29tYmluYXRvcnMgYW5kIGdlbmVyYXRvcnNcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG5mdW5jdGlvbiBvcjxUPihwYXJzZXJzOiBQYXJzZXJDb21wb25lbnQ8VD5bXSk6IFBhcnNlckNvbXBvbmVudDxUPiB7XG4gIHJldHVybiBmdW5jdGlvbiBPcihzY2FubmVyOiBTY2FubmVyKTogUGFyc2VSZXN1bHQ8VD4ge1xuICAgIGZvciAoY29uc3QgcGFyc2Ugb2YgcGFyc2Vycykge1xuICAgICAgY29uc3QgcmVzdWx0ID0gcGFyc2Uoc2Nhbm5lcik7XG4gICAgICBpZiAocmVzdWx0Lm9rKSB7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIGpvaW48VD4oXG4gIHBhcnNlcjogUGFyc2VyQ29tcG9uZW50PFQ+LFxuICBzZXBhcmF0b3I6IHN0cmluZyxcbik6IFBhcnNlckNvbXBvbmVudDxUW10+IHtcbiAgY29uc3QgU2VwYXJhdG9yID0gY2hhcmFjdGVyKHNlcGFyYXRvcik7XG4gIHJldHVybiBmdW5jdGlvbiBKb2luKHNjYW5uZXI6IFNjYW5uZXIpOiBQYXJzZVJlc3VsdDxUW10+IHtcbiAgICBjb25zdCBmaXJzdCA9IHBhcnNlcihzY2FubmVyKTtcbiAgICBpZiAoIWZpcnN0Lm9rKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZSgpO1xuICAgIH1cbiAgICBjb25zdCBvdXQ6IFRbXSA9IFtmaXJzdC5ib2R5XTtcbiAgICB3aGlsZSAoIXNjYW5uZXIuZW9mKCkpIHtcbiAgICAgIGlmICghU2VwYXJhdG9yKHNjYW5uZXIpLm9rKSB7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY29uc3QgcmVzdWx0ID0gcGFyc2VyKHNjYW5uZXIpO1xuICAgICAgaWYgKHJlc3VsdC5vaykge1xuICAgICAgICBvdXQucHVzaChyZXN1bHQuYm9keSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoYEludmFsaWQgdG9rZW4gYWZ0ZXIgXCIke3NlcGFyYXRvcn1cImApO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gc3VjY2VzcyhvdXQpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBrdjxUPihcbiAga2V5UGFyc2VyOiBQYXJzZXJDb21wb25lbnQ8c3RyaW5nW10+LFxuICBzZXBhcmF0b3I6IHN0cmluZyxcbiAgdmFsdWVQYXJzZXI6IFBhcnNlckNvbXBvbmVudDxUPixcbik6IFBhcnNlckNvbXBvbmVudDx7IFtrZXk6IHN0cmluZ106IHVua25vd24gfT4ge1xuICBjb25zdCBTZXBhcmF0b3IgPSBjaGFyYWN0ZXIoc2VwYXJhdG9yKTtcbiAgcmV0dXJuIGZ1bmN0aW9uIEt2KFxuICAgIHNjYW5uZXI6IFNjYW5uZXIsXG4gICk6IFBhcnNlUmVzdWx0PHsgW2tleTogc3RyaW5nXTogdW5rbm93biB9PiB7XG4gICAgY29uc3Qga2V5ID0ga2V5UGFyc2VyKHNjYW5uZXIpO1xuICAgIGlmICgha2V5Lm9rKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZSgpO1xuICAgIH1cbiAgICBjb25zdCBzZXAgPSBTZXBhcmF0b3Ioc2Nhbm5lcik7XG4gICAgaWYgKCFzZXAub2spIHtcbiAgICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihga2V5L3ZhbHVlIHBhaXIgZG9lc24ndCBoYXZlIFwiJHtzZXBhcmF0b3J9XCJgKTtcbiAgICB9XG4gICAgY29uc3QgdmFsdWUgPSB2YWx1ZVBhcnNlcihzY2FubmVyKTtcbiAgICBpZiAoIXZhbHVlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoXG4gICAgICAgIGBWYWx1ZSBvZiBrZXkvdmFsdWUgcGFpciBpcyBpbnZhbGlkIGRhdGEgZm9ybWF0YCxcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBzdWNjZXNzKFV0aWxzLnVuZmxhdChrZXkuYm9keSwgdmFsdWUuYm9keSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBtZXJnZShcbiAgcGFyc2VyOiBQYXJzZXJDb21wb25lbnQ8dW5rbm93bltdPixcbik6IFBhcnNlckNvbXBvbmVudDxSZWNvcmQ8c3RyaW5nLCB1bmtub3duPj4ge1xuICByZXR1cm4gZnVuY3Rpb24gTWVyZ2UoXG4gICAgc2Nhbm5lcjogU2Nhbm5lcixcbiAgKTogUGFyc2VSZXN1bHQ8UmVjb3JkPHN0cmluZywgdW5rbm93bj4+IHtcbiAgICBjb25zdCByZXN1bHQgPSBwYXJzZXIoc2Nhbm5lcik7XG4gICAgaWYgKCFyZXN1bHQub2spIHtcbiAgICAgIHJldHVybiBmYWlsdXJlKCk7XG4gICAgfVxuICAgIGxldCBib2R5ID0ge307XG4gICAgZm9yIChjb25zdCByZWNvcmQgb2YgcmVzdWx0LmJvZHkpIHtcbiAgICAgIGlmICh0eXBlb2YgYm9keSA9PT0gXCJvYmplY3RcIiAmJiBib2R5ICE9PSBudWxsKSB7XG4gICAgICAgIC8vIGRlbm8tbGludC1pZ25vcmUgbm8tZXhwbGljaXQtYW55XG4gICAgICAgIGJvZHkgPSBkZWVwTWVyZ2UoYm9keSwgcmVjb3JkIGFzIFJlY29yZDxhbnksIGFueT4pO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gc3VjY2Vzcyhib2R5KTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gcmVwZWF0PFQ+KFxuICBwYXJzZXI6IFBhcnNlckNvbXBvbmVudDxUPixcbik6IFBhcnNlckNvbXBvbmVudDxUW10+IHtcbiAgcmV0dXJuIGZ1bmN0aW9uIFJlcGVhdChcbiAgICBzY2FubmVyOiBTY2FubmVyLFxuICApIHtcbiAgICBjb25zdCBib2R5OiBUW10gPSBbXTtcbiAgICB3aGlsZSAoIXNjYW5uZXIuZW9mKCkpIHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IHBhcnNlcihzY2FubmVyKTtcbiAgICAgIGlmIChyZXN1bHQub2spIHtcbiAgICAgICAgYm9keS5wdXNoKHJlc3VsdC5ib2R5KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKCk7XG4gICAgfVxuICAgIGlmIChib2R5Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgICB9XG4gICAgcmV0dXJuIHN1Y2Nlc3MoYm9keSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIHN1cnJvdW5kPFQ+KFxuICBsZWZ0OiBzdHJpbmcsXG4gIHBhcnNlcjogUGFyc2VyQ29tcG9uZW50PFQ+LFxuICByaWdodDogc3RyaW5nLFxuKTogUGFyc2VyQ29tcG9uZW50PFQ+IHtcbiAgY29uc3QgTGVmdCA9IGNoYXJhY3RlcihsZWZ0KTtcbiAgY29uc3QgUmlnaHQgPSBjaGFyYWN0ZXIocmlnaHQpO1xuICByZXR1cm4gZnVuY3Rpb24gU3Vycm91bmQoc2Nhbm5lcjogU2Nhbm5lcikge1xuICAgIGlmICghTGVmdChzY2FubmVyKS5vaykge1xuICAgICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gcGFyc2VyKHNjYW5uZXIpO1xuICAgIGlmICghcmVzdWx0Lm9rKSB7XG4gICAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoYEludmFsaWQgdG9rZW4gYWZ0ZXIgXCIke2xlZnR9XCJgKTtcbiAgICB9XG4gICAgaWYgKCFSaWdodChzY2FubmVyKS5vaykge1xuICAgICAgdGhyb3cgbmV3IFRPTUxQYXJzZUVycm9yKFxuICAgICAgICBgTm90IGNsb3NlZCBieSBcIiR7cmlnaHR9XCIgYWZ0ZXIgc3RhcnRlZCB3aXRoIFwiJHtsZWZ0fVwiYCxcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBzdWNjZXNzKHJlc3VsdC5ib2R5KTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gY2hhcmFjdGVyKHN0cjogc3RyaW5nKSB7XG4gIHJldHVybiBmdW5jdGlvbiBjaGFyYWN0ZXIoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHZvaWQ+IHtcbiAgICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG4gICAgaWYgKHNjYW5uZXIuc2xpY2UoMCwgc3RyLmxlbmd0aCkgPT09IHN0cikge1xuICAgICAgc2Nhbm5lci5uZXh0KHN0ci5sZW5ndGgpO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gZmFpbHVyZSgpO1xuICAgIH1cbiAgICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG4gICAgcmV0dXJuIHN1Y2Nlc3ModW5kZWZpbmVkKTtcbiAgfTtcbn1cblxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIFBhcnNlciBjb21wb25lbnRzXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG5jb25zdCBQYXR0ZXJucyA9IHtcbiAgQkFSRV9LRVk6IC9bQS1aYS16MC05Xy1dLyxcbiAgRkxPQVQ6IC9bMC05X1xcLmUrXFwtXS9pLFxuICBFTkRfT0ZfVkFMVUU6IC9bIFxcdFxcclxcbiMsfVxcXV0vLFxufTtcblxuZXhwb3J0IGZ1bmN0aW9uIEJhcmVLZXkoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHN0cmluZz4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG4gIGlmICghc2Nhbm5lci5jaGFyKCkgfHwgIVBhdHRlcm5zLkJBUkVfS0VZLnRlc3Qoc2Nhbm5lci5jaGFyKCkpKSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxuICBjb25zdCBhY2M6IHN0cmluZ1tdID0gW107XG4gIHdoaWxlIChzY2FubmVyLmNoYXIoKSAmJiBQYXR0ZXJucy5CQVJFX0tFWS50ZXN0KHNjYW5uZXIuY2hhcigpKSkge1xuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICBjb25zdCBrZXkgPSBhY2Muam9pbihcIlwiKTtcbiAgcmV0dXJuIHN1Y2Nlc3Moa2V5KTtcbn1cblxuZnVuY3Rpb24gRXNjYXBlU2VxdWVuY2Uoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHN0cmluZz4ge1xuICBpZiAoc2Nhbm5lci5jaGFyKCkgPT09IFwiXFxcXFwiKSB7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gICAgLy8gU2VlIGh0dHBzOi8vdG9tbC5pby9lbi92MS4wLjAtcmMuMyNzdHJpbmdcbiAgICBzd2l0Y2ggKHNjYW5uZXIuY2hhcigpKSB7XG4gICAgICBjYXNlIFwiYlwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXGJcIik7XG4gICAgICBjYXNlIFwidFwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXHRcIik7XG4gICAgICBjYXNlIFwiblwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXG5cIik7XG4gICAgICBjYXNlIFwiZlwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXGZcIik7XG4gICAgICBjYXNlIFwiclwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXHJcIik7XG4gICAgICBjYXNlIFwidVwiOlxuICAgICAgY2FzZSBcIlVcIjoge1xuICAgICAgICAvLyBVbmljb2RlIGNoYXJhY3RlclxuICAgICAgICBjb25zdCBjb2RlUG9pbnRMZW4gPSBzY2FubmVyLmNoYXIoKSA9PT0gXCJ1XCIgPyA0IDogNjtcbiAgICAgICAgY29uc3QgY29kZVBvaW50ID0gcGFyc2VJbnQoXG4gICAgICAgICAgXCIweFwiICsgc2Nhbm5lci5zbGljZSgxLCAxICsgY29kZVBvaW50TGVuKSxcbiAgICAgICAgICAxNixcbiAgICAgICAgKTtcbiAgICAgICAgY29uc3Qgc3RyID0gU3RyaW5nLmZyb21Db2RlUG9pbnQoY29kZVBvaW50KTtcbiAgICAgICAgc2Nhbm5lci5uZXh0KGNvZGVQb2ludExlbiArIDEpO1xuICAgICAgICByZXR1cm4gc3VjY2VzcyhzdHIpO1xuICAgICAgfVxuICAgICAgY2FzZSAnXCInOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoJ1wiJyk7XG4gICAgICBjYXNlIFwiXFxcXFwiOlxuICAgICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgICAgcmV0dXJuIHN1Y2Nlc3MoXCJcXFxcXCIpO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgc2Nhbm5lci5uZXh0KCk7XG4gICAgICAgIHJldHVybiBzdWNjZXNzKHNjYW5uZXIuY2hhcigpKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gQmFzaWNTdHJpbmcoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHN0cmluZz4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG4gIGlmIChzY2FubmVyLmNoYXIoKSA9PT0gJ1wiJykge1xuICAgIHNjYW5uZXIubmV4dCgpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgY29uc3QgYWNjID0gW107XG4gIHdoaWxlIChzY2FubmVyLmNoYXIoKSAhPT0gJ1wiJyAmJiAhc2Nhbm5lci5lb2YoKSkge1xuICAgIGlmIChzY2FubmVyLmNoYXIoKSA9PT0gXCJcXG5cIikge1xuICAgICAgdGhyb3cgbmV3IFRPTUxQYXJzZUVycm9yKFwiU2luZ2xlLWxpbmUgc3RyaW5nIGNhbm5vdCBjb250YWluIEVPTFwiKTtcbiAgICB9XG4gICAgY29uc3QgZXNjYXBlZENoYXIgPSBFc2NhcGVTZXF1ZW5jZShzY2FubmVyKTtcbiAgICBpZiAoZXNjYXBlZENoYXIub2spIHtcbiAgICAgIGFjYy5wdXNoKGVzY2FwZWRDaGFyLmJvZHkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBhY2MucHVzaChzY2FubmVyLmNoYXIoKSk7XG4gICAgICBzY2FubmVyLm5leHQoKTtcbiAgICB9XG4gIH1cbiAgaWYgKHNjYW5uZXIuZW9mKCkpIHtcbiAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoXG4gICAgICBgU2luZ2xlLWxpbmUgc3RyaW5nIGlzIG5vdCBjbG9zZWQ6XFxuJHthY2Muam9pbihcIlwiKX1gLFxuICAgICk7XG4gIH1cbiAgc2Nhbm5lci5uZXh0KCk7IC8vIHNraXAgbGFzdCAnXCJcIlxuICByZXR1cm4gc3VjY2VzcyhhY2Muam9pbihcIlwiKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMaXRlcmFsU3RyaW5nKHNjYW5uZXI6IFNjYW5uZXIpOiBQYXJzZVJlc3VsdDxzdHJpbmc+IHtcbiAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKHsgaW5saW5lOiB0cnVlIH0pO1xuICBpZiAoc2Nhbm5lci5jaGFyKCkgPT09IFwiJ1wiKSB7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxuICBjb25zdCBhY2M6IHN0cmluZ1tdID0gW107XG4gIHdoaWxlIChzY2FubmVyLmNoYXIoKSAhPT0gXCInXCIgJiYgIXNjYW5uZXIuZW9mKCkpIHtcbiAgICBpZiAoc2Nhbm5lci5jaGFyKCkgPT09IFwiXFxuXCIpIHtcbiAgICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihcIlNpbmdsZS1saW5lIHN0cmluZyBjYW5ub3QgY29udGFpbiBFT0xcIik7XG4gICAgfVxuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICBpZiAoc2Nhbm5lci5lb2YoKSkge1xuICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihcbiAgICAgIGBTaW5nbGUtbGluZSBzdHJpbmcgaXMgbm90IGNsb3NlZDpcXG4ke2FjYy5qb2luKFwiXCIpfWAsXG4gICAgKTtcbiAgfVxuICBzY2FubmVyLm5leHQoKTsgLy8gc2tpcCBsYXN0IFwiJ1wiXG4gIHJldHVybiBzdWNjZXNzKGFjYy5qb2luKFwiXCIpKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE11bHRpbGluZUJhc2ljU3RyaW5nKFxuICBzY2FubmVyOiBTY2FubmVyLFxuKTogUGFyc2VSZXN1bHQ8c3RyaW5nPiB7XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcih7IGlubGluZTogdHJ1ZSB9KTtcbiAgaWYgKHNjYW5uZXIuc2xpY2UoMCwgMykgPT09ICdcIlwiXCInKSB7XG4gICAgc2Nhbm5lci5uZXh0KDMpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgaWYgKHNjYW5uZXIuY2hhcigpID09PSBcIlxcblwiKSB7XG4gICAgLy8gVGhlIGZpcnN0IG5ld2xpbmUgaXMgdHJpbW1lZFxuICAgIHNjYW5uZXIubmV4dCgpO1xuICB9XG4gIGNvbnN0IGFjYzogc3RyaW5nW10gPSBbXTtcbiAgd2hpbGUgKHNjYW5uZXIuc2xpY2UoMCwgMykgIT09ICdcIlwiXCInICYmICFzY2FubmVyLmVvZigpKSB7XG4gICAgLy8gbGluZSBlbmRpbmcgYmFja3NsYXNoXG4gICAgaWYgKHNjYW5uZXIuc2xpY2UoMCwgMikgPT09IFwiXFxcXFxcblwiKSB7XG4gICAgICBzY2FubmVyLm5leHQoKTtcbiAgICAgIHNjYW5uZXIubmV4dFVudGlsQ2hhcih7IGNvbW1lbnQ6IGZhbHNlIH0pO1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IGVzY2FwZWRDaGFyID0gRXNjYXBlU2VxdWVuY2Uoc2Nhbm5lcik7XG4gICAgaWYgKGVzY2FwZWRDaGFyLm9rKSB7XG4gICAgICBhY2MucHVzaChlc2NhcGVkQ2hhci5ib2R5KTtcbiAgICB9IGVsc2Uge1xuICAgICAgYWNjLnB1c2goc2Nhbm5lci5jaGFyKCkpO1xuICAgICAgc2Nhbm5lci5uZXh0KCk7XG4gICAgfVxuICB9XG5cbiAgaWYgKHNjYW5uZXIuZW9mKCkpIHtcbiAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoXG4gICAgICBgTXVsdGktbGluZSBzdHJpbmcgaXMgbm90IGNsb3NlZDpcXG4ke2FjYy5qb2luKFwiXCIpfWAsXG4gICAgKTtcbiAgfVxuICAvLyBpZiBlbmRzIHdpdGggNCBgXCJgLCBwdXNoIHRoZSBmaXN0IGBcImAgdG8gc3RyaW5nXG4gIGlmIChzY2FubmVyLmNoYXIoMykgPT09ICdcIicpIHtcbiAgICBhY2MucHVzaCgnXCInKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICBzY2FubmVyLm5leHQoMyk7IC8vIHNraXAgbGFzdCAnXCJcIlwiXCJcbiAgcmV0dXJuIHN1Y2Nlc3MoYWNjLmpvaW4oXCJcIikpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTXVsdGlsaW5lTGl0ZXJhbFN0cmluZyhcbiAgc2Nhbm5lcjogU2Nhbm5lcixcbik6IFBhcnNlUmVzdWx0PHN0cmluZz4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG4gIGlmIChzY2FubmVyLnNsaWNlKDAsIDMpID09PSBcIicnJ1wiKSB7XG4gICAgc2Nhbm5lci5uZXh0KDMpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgaWYgKHNjYW5uZXIuY2hhcigpID09PSBcIlxcblwiKSB7XG4gICAgLy8gVGhlIGZpcnN0IG5ld2xpbmUgaXMgdHJpbW1lZFxuICAgIHNjYW5uZXIubmV4dCgpO1xuICB9XG4gIGNvbnN0IGFjYzogc3RyaW5nW10gPSBbXTtcbiAgd2hpbGUgKHNjYW5uZXIuc2xpY2UoMCwgMykgIT09IFwiJycnXCIgJiYgIXNjYW5uZXIuZW9mKCkpIHtcbiAgICBhY2MucHVzaChzY2FubmVyLmNoYXIoKSk7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gIH1cbiAgaWYgKHNjYW5uZXIuZW9mKCkpIHtcbiAgICB0aHJvdyBuZXcgVE9NTFBhcnNlRXJyb3IoXG4gICAgICBgTXVsdGktbGluZSBzdHJpbmcgaXMgbm90IGNsb3NlZDpcXG4ke2FjYy5qb2luKFwiXCIpfWAsXG4gICAgKTtcbiAgfVxuICAvLyBpZiBlbmRzIHdpdGggNCBgJ2AsIHB1c2ggdGhlIGZpc3QgYCdgIHRvIHN0cmluZ1xuICBpZiAoc2Nhbm5lci5jaGFyKDMpID09PSBcIidcIikge1xuICAgIGFjYy5wdXNoKFwiJ1wiKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICBzY2FubmVyLm5leHQoMyk7IC8vIHNraXAgbGFzdCBcIicnJ1wiXG4gIHJldHVybiBzdWNjZXNzKGFjYy5qb2luKFwiXCIpKTtcbn1cblxuY29uc3Qgc3ltYm9sUGFpcnM6IFtzdHJpbmcsIHVua25vd25dW10gPSBbXG4gIFtcInRydWVcIiwgdHJ1ZV0sXG4gIFtcImZhbHNlXCIsIGZhbHNlXSxcbiAgW1wiaW5mXCIsIEluZmluaXR5XSxcbiAgW1wiK2luZlwiLCBJbmZpbml0eV0sXG4gIFtcIi1pbmZcIiwgLUluZmluaXR5XSxcbiAgW1wibmFuXCIsIE5hTl0sXG4gIFtcIituYW5cIiwgTmFOXSxcbiAgW1wiLW5hblwiLCBOYU5dLFxuXTtcbmV4cG9ydCBmdW5jdGlvbiBTeW1ib2xzKHNjYW5uZXI6IFNjYW5uZXIpOiBQYXJzZVJlc3VsdDx1bmtub3duPiB7XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcih7IGlubGluZTogdHJ1ZSB9KTtcbiAgY29uc3QgZm91bmQgPSBzeW1ib2xQYWlycy5maW5kKChbc3RyXSkgPT5cbiAgICBzY2FubmVyLnNsaWNlKDAsIHN0ci5sZW5ndGgpID09PSBzdHJcbiAgKTtcbiAgaWYgKCFmb3VuZCkge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgY29uc3QgW3N0ciwgdmFsdWVdID0gZm91bmQ7XG4gIHNjYW5uZXIubmV4dChzdHIubGVuZ3RoKTtcbiAgcmV0dXJuIHN1Y2Nlc3ModmFsdWUpO1xufVxuXG5leHBvcnQgY29uc3QgRG90dGVkS2V5ID0gam9pbihcbiAgb3IoW0JhcmVLZXksIEJhc2ljU3RyaW5nLCBMaXRlcmFsU3RyaW5nXSksXG4gIFwiLlwiLFxuKTtcblxuZXhwb3J0IGZ1bmN0aW9uIEludGVnZXIoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PG51bWJlciB8IHN0cmluZz4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG5cbiAgLy8gSWYgYmluYXJ5IC8gb2N0YWwgLyBoZXhcbiAgY29uc3QgZmlyc3QyID0gc2Nhbm5lci5zbGljZSgwLCAyKTtcbiAgaWYgKGZpcnN0Mi5sZW5ndGggPT09IDIgJiYgLzAoPzp4fG98YikvaS50ZXN0KGZpcnN0MikpIHtcbiAgICBzY2FubmVyLm5leHQoMik7XG4gICAgY29uc3QgYWNjID0gW2ZpcnN0Ml07XG4gICAgd2hpbGUgKC9bMC05YS1mX10vaS50ZXN0KHNjYW5uZXIuY2hhcigpKSAmJiAhc2Nhbm5lci5lb2YoKSkge1xuICAgICAgYWNjLnB1c2goc2Nhbm5lci5jaGFyKCkpO1xuICAgICAgc2Nhbm5lci5uZXh0KCk7XG4gICAgfVxuICAgIGlmIChhY2MubGVuZ3RoID09PSAxKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZSgpO1xuICAgIH1cbiAgICByZXR1cm4gc3VjY2VzcyhhY2Muam9pbihcIlwiKSk7XG4gIH1cblxuICBjb25zdCBhY2MgPSBbXTtcbiAgaWYgKC9bKy1dLy50ZXN0KHNjYW5uZXIuY2hhcigpKSkge1xuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICB3aGlsZSAoL1swLTlfXS8udGVzdChzY2FubmVyLmNoYXIoKSkgJiYgIXNjYW5uZXIuZW9mKCkpIHtcbiAgICBhY2MucHVzaChzY2FubmVyLmNoYXIoKSk7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gIH1cblxuICBpZiAoYWNjLmxlbmd0aCA9PT0gMCB8fCAoYWNjLmxlbmd0aCA9PT0gMSAmJiAvWystXS8udGVzdChhY2NbMF0pKSkge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cblxuICBjb25zdCBpbnQgPSBwYXJzZUludChhY2MuZmlsdGVyKChjaGFyKSA9PiBjaGFyICE9PSBcIl9cIikuam9pbihcIlwiKSk7XG4gIHJldHVybiBzdWNjZXNzKGludCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBGbG9hdChzY2FubmVyOiBTY2FubmVyKTogUGFyc2VSZXN1bHQ8bnVtYmVyPiB7XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcih7IGlubGluZTogdHJ1ZSB9KTtcblxuICAvLyBsb29rYWhlYWQgdmFsaWRhdGlvbiBpcyBuZWVkZWQgZm9yIGludGVnZXIgdmFsdWUgaXMgc2ltaWxhciB0byBmbG9hdFxuICBsZXQgcG9zaXRpb24gPSAwO1xuICB3aGlsZSAoXG4gICAgc2Nhbm5lci5jaGFyKHBvc2l0aW9uKSAmJlxuICAgICFQYXR0ZXJucy5FTkRfT0ZfVkFMVUUudGVzdChzY2FubmVyLmNoYXIocG9zaXRpb24pKVxuICApIHtcbiAgICBpZiAoIVBhdHRlcm5zLkZMT0FULnRlc3Qoc2Nhbm5lci5jaGFyKHBvc2l0aW9uKSkpIHtcbiAgICAgIHJldHVybiBmYWlsdXJlKCk7XG4gICAgfVxuICAgIHBvc2l0aW9uKys7XG4gIH1cblxuICBjb25zdCBhY2MgPSBbXTtcbiAgaWYgKC9bKy1dLy50ZXN0KHNjYW5uZXIuY2hhcigpKSkge1xuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICB3aGlsZSAoUGF0dGVybnMuRkxPQVQudGVzdChzY2FubmVyLmNoYXIoKSkgJiYgIXNjYW5uZXIuZW9mKCkpIHtcbiAgICBhY2MucHVzaChzY2FubmVyLmNoYXIoKSk7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gIH1cblxuICBpZiAoYWNjLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgY29uc3QgZmxvYXQgPSBwYXJzZUZsb2F0KGFjYy5maWx0ZXIoKGNoYXIpID0+IGNoYXIgIT09IFwiX1wiKS5qb2luKFwiXCIpKTtcbiAgaWYgKGlzTmFOKGZsb2F0KSkge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cblxuICByZXR1cm4gc3VjY2VzcyhmbG9hdCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBEYXRlVGltZShzY2FubmVyOiBTY2FubmVyKTogUGFyc2VSZXN1bHQ8RGF0ZT4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG5cbiAgbGV0IGRhdGVTdHIgPSBzY2FubmVyLnNsaWNlKDAsIDEwKTtcbiAgLy8gZXhhbXBsZTogMTk3OS0wNS0yN1xuICBpZiAoL15cXGR7NH0tXFxkezJ9LVxcZHsyfS8udGVzdChkYXRlU3RyKSkge1xuICAgIHNjYW5uZXIubmV4dCgxMCk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxuXG4gIGNvbnN0IGFjYyA9IFtdO1xuICAvLyBleGFtcGxlOiAxOTc5LTA1LTI3VDAwOjMyOjAwWlxuICB3aGlsZSAoL1sgMC05VFouOi1dLy50ZXN0KHNjYW5uZXIuY2hhcigpKSAmJiAhc2Nhbm5lci5lb2YoKSkge1xuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICBkYXRlU3RyICs9IGFjYy5qb2luKFwiXCIpO1xuICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0ci50cmltKCkpO1xuICAvLyBpbnZhbGlkIGRhdGVcbiAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkge1xuICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihgSW52YWxpZCBkYXRlIHN0cmluZyBcIiR7ZGF0ZVN0cn1cImApO1xuICB9XG5cbiAgcmV0dXJuIHN1Y2Nlc3MoZGF0ZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2NhbFRpbWUoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHN0cmluZz4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG5cbiAgbGV0IHRpbWVTdHIgPSBzY2FubmVyLnNsaWNlKDAsIDgpO1xuICBpZiAoL14oXFxkezJ9KTooXFxkezJ9KTooXFxkezJ9KS8udGVzdCh0aW1lU3RyKSkge1xuICAgIHNjYW5uZXIubmV4dCg4KTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gZmFpbHVyZSgpO1xuICB9XG5cbiAgY29uc3QgYWNjID0gW107XG4gIGlmIChzY2FubmVyLmNoYXIoKSA9PT0gXCIuXCIpIHtcbiAgICBhY2MucHVzaChzY2FubmVyLmNoYXIoKSk7XG4gICAgc2Nhbm5lci5uZXh0KCk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHN1Y2Nlc3ModGltZVN0cik7XG4gIH1cblxuICB3aGlsZSAoL1swLTldLy50ZXN0KHNjYW5uZXIuY2hhcigpKSAmJiAhc2Nhbm5lci5lb2YoKSkge1xuICAgIGFjYy5wdXNoKHNjYW5uZXIuY2hhcigpKTtcbiAgICBzY2FubmVyLm5leHQoKTtcbiAgfVxuICB0aW1lU3RyICs9IGFjYy5qb2luKFwiXCIpO1xuICByZXR1cm4gc3VjY2Vzcyh0aW1lU3RyKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEFycmF5VmFsdWUoc2Nhbm5lcjogU2Nhbm5lcik6IFBhcnNlUmVzdWx0PHVua25vd25bXT4ge1xuICBzY2FubmVyLm5leHRVbnRpbENoYXIoeyBpbmxpbmU6IHRydWUgfSk7XG5cbiAgaWYgKHNjYW5uZXIuY2hhcigpID09PSBcIltcIikge1xuICAgIHNjYW5uZXIubmV4dCgpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cblxuICBjb25zdCBhcnJheTogdW5rbm93bltdID0gW107XG4gIHdoaWxlICghc2Nhbm5lci5lb2YoKSkge1xuICAgIHNjYW5uZXIubmV4dFVudGlsQ2hhcigpO1xuICAgIGNvbnN0IHJlc3VsdCA9IFZhbHVlKHNjYW5uZXIpO1xuICAgIGlmIChyZXN1bHQub2spIHtcbiAgICAgIGFycmF5LnB1c2gocmVzdWx0LmJvZHkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKHsgaW5saW5lOiB0cnVlIH0pO1xuICAgIC8vIG1heSBoYXZlIGEgbmV4dCBpdGVtLCBidXQgdHJhaWxpbmcgY29tbWEgaXMgYWxsb3dlZCBhdCBhcnJheVxuICAgIGlmIChzY2FubmVyLmNoYXIoKSA9PT0gXCIsXCIpIHtcbiAgICAgIHNjYW5uZXIubmV4dCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gIH1cbiAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKCk7XG5cbiAgaWYgKHNjYW5uZXIuY2hhcigpID09PSBcIl1cIikge1xuICAgIHNjYW5uZXIubmV4dCgpO1xuICB9IGVsc2Uge1xuICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihcIkFycmF5IGlzIG5vdCBjbG9zZWRcIik7XG4gIH1cblxuICByZXR1cm4gc3VjY2VzcyhhcnJheSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBJbmxpbmVUYWJsZShcbiAgc2Nhbm5lcjogU2Nhbm5lcixcbik6IFBhcnNlUmVzdWx0PFJlY29yZDxzdHJpbmcsIHVua25vd24+PiB7XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcigpO1xuICBpZiAoc2Nhbm5lci5jaGFyKDEpID09PSBcIn1cIikge1xuICAgIHNjYW5uZXIubmV4dCgyKTtcbiAgICByZXR1cm4gc3VjY2Vzcyh7fSk7XG4gIH1cbiAgY29uc3QgcGFpcnMgPSBzdXJyb3VuZChcbiAgICBcIntcIixcbiAgICBqb2luKFBhaXIsIFwiLFwiKSxcbiAgICBcIn1cIixcbiAgKShzY2FubmVyKTtcbiAgaWYgKCFwYWlycy5vaykge1xuICAgIHJldHVybiBmYWlsdXJlKCk7XG4gIH1cbiAgbGV0IHRhYmxlID0ge307XG4gIGZvciAoY29uc3QgcGFpciBvZiBwYWlycy5ib2R5KSB7XG4gICAgdGFibGUgPSBkZWVwTWVyZ2UodGFibGUsIHBhaXIpO1xuICB9XG4gIHJldHVybiBzdWNjZXNzKHRhYmxlKTtcbn1cblxuZXhwb3J0IGNvbnN0IFZhbHVlID0gb3IoW1xuICBNdWx0aWxpbmVCYXNpY1N0cmluZyxcbiAgTXVsdGlsaW5lTGl0ZXJhbFN0cmluZyxcbiAgQmFzaWNTdHJpbmcsXG4gIExpdGVyYWxTdHJpbmcsXG4gIFN5bWJvbHMsXG4gIERhdGVUaW1lLFxuICBMb2NhbFRpbWUsXG4gIEZsb2F0LFxuICBJbnRlZ2VyLFxuICBBcnJheVZhbHVlLFxuICBJbmxpbmVUYWJsZSxcbl0pO1xuXG5leHBvcnQgY29uc3QgUGFpciA9IGt2KERvdHRlZEtleSwgXCI9XCIsIFZhbHVlKTtcblxuZXhwb3J0IGZ1bmN0aW9uIEJsb2NrKFxuICBzY2FubmVyOiBTY2FubmVyLFxuKTogUGFyc2VSZXN1bHQ8QmxvY2tQYXJzZVJlc3VsdEJvZHk+IHtcbiAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKCk7XG4gIGNvbnN0IHJlc3VsdCA9IG1lcmdlKHJlcGVhdChQYWlyKSkoc2Nhbm5lcik7XG4gIGlmIChyZXN1bHQub2spIHtcbiAgICByZXR1cm4gc3VjY2Vzcyh7XG4gICAgICB0eXBlOiBcIkJsb2NrXCIsXG4gICAgICB2YWx1ZTogcmVzdWx0LmJvZHksXG4gICAgfSk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgVGFibGVIZWFkZXIgPSBzdXJyb3VuZChcIltcIiwgRG90dGVkS2V5LCBcIl1cIik7XG5cbmV4cG9ydCBmdW5jdGlvbiBUYWJsZShcbiAgc2Nhbm5lcjogU2Nhbm5lcixcbik6IFBhcnNlUmVzdWx0PEJsb2NrUGFyc2VSZXN1bHRCb2R5PiB7XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcigpO1xuICBjb25zdCBoZWFkZXIgPSBUYWJsZUhlYWRlcihzY2FubmVyKTtcbiAgaWYgKCFoZWFkZXIub2spIHtcbiAgICByZXR1cm4gZmFpbHVyZSgpO1xuICB9XG4gIHNjYW5uZXIubmV4dFVudGlsQ2hhcigpO1xuICBjb25zdCBibG9jayA9IEJsb2NrKHNjYW5uZXIpO1xuICByZXR1cm4gc3VjY2Vzcyh7XG4gICAgdHlwZTogXCJUYWJsZVwiLFxuICAgIGtleTogaGVhZGVyLmJvZHksXG4gICAgdmFsdWU6IGJsb2NrLm9rID8gYmxvY2suYm9keS52YWx1ZSA6IHt9LFxuICB9KTtcbn1cblxuZXhwb3J0IGNvbnN0IFRhYmxlQXJyYXlIZWFkZXIgPSBzdXJyb3VuZChcbiAgXCJbW1wiLFxuICBEb3R0ZWRLZXksXG4gIFwiXV1cIixcbik7XG5cbmV4cG9ydCBmdW5jdGlvbiBUYWJsZUFycmF5KFxuICBzY2FubmVyOiBTY2FubmVyLFxuKTogUGFyc2VSZXN1bHQ8QmxvY2tQYXJzZVJlc3VsdEJvZHk+IHtcbiAgc2Nhbm5lci5uZXh0VW50aWxDaGFyKCk7XG4gIGNvbnN0IGhlYWRlciA9IFRhYmxlQXJyYXlIZWFkZXIoc2Nhbm5lcik7XG4gIGlmICghaGVhZGVyLm9rKSB7XG4gICAgcmV0dXJuIGZhaWx1cmUoKTtcbiAgfVxuICBzY2FubmVyLm5leHRVbnRpbENoYXIoKTtcbiAgY29uc3QgYmxvY2sgPSBCbG9jayhzY2FubmVyKTtcbiAgcmV0dXJuIHN1Y2Nlc3Moe1xuICAgIHR5cGU6IFwiVGFibGVBcnJheVwiLFxuICAgIGtleTogaGVhZGVyLmJvZHksXG4gICAgdmFsdWU6IGJsb2NrLm9rID8gYmxvY2suYm9keS52YWx1ZSA6IHt9LFxuICB9KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRvbWwoXG4gIHNjYW5uZXI6IFNjYW5uZXIsXG4pOiBQYXJzZVJlc3VsdDxSZWNvcmQ8c3RyaW5nLCB1bmtub3duPj4ge1xuICBjb25zdCBibG9ja3MgPSByZXBlYXQob3IoW0Jsb2NrLCBUYWJsZUFycmF5LCBUYWJsZV0pKShzY2FubmVyKTtcbiAgaWYgKCFibG9ja3Mub2spIHtcbiAgICByZXR1cm4gZmFpbHVyZSgpO1xuICB9XG4gIGxldCBib2R5ID0ge307XG4gIGZvciAoY29uc3QgYmxvY2sgb2YgYmxvY2tzLmJvZHkpIHtcbiAgICBzd2l0Y2ggKGJsb2NrLnR5cGUpIHtcbiAgICAgIGNhc2UgXCJCbG9ja1wiOiB7XG4gICAgICAgIGJvZHkgPSBkZWVwTWVyZ2UoYm9keSwgYmxvY2sudmFsdWUpO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJUYWJsZVwiOiB7XG4gICAgICAgIFV0aWxzLmRlZXBBc3NpZ25XaXRoVGFibGUoYm9keSwgYmxvY2spO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJUYWJsZUFycmF5XCI6IHtcbiAgICAgICAgVXRpbHMuZGVlcEFzc2lnbldpdGhUYWJsZShib2R5LCBibG9jayk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gc3VjY2Vzcyhib2R5KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFBhcnNlckZhY3Rvcnk8VD4ocGFyc2VyOiBQYXJzZXJDb21wb25lbnQ8VD4pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIHBhcnNlKHRvbWxTdHJpbmc6IHN0cmluZyk6IFQge1xuICAgIGNvbnN0IHNjYW5uZXIgPSBuZXcgU2Nhbm5lcih0b21sU3RyaW5nKTtcblxuICAgIGxldCBwYXJzZWQ6IFBhcnNlUmVzdWx0PFQ+IHwgbnVsbCA9IG51bGw7XG4gICAgbGV0IGVycjogRXJyb3IgfCBudWxsID0gbnVsbDtcbiAgICB0cnkge1xuICAgICAgcGFyc2VkID0gcGFyc2VyKHNjYW5uZXIpO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGVyciA9IGUgaW5zdGFuY2VvZiBFcnJvciA/IGUgOiBuZXcgRXJyb3IoXCJbbm9uLWVycm9yIHRocm93bl1cIik7XG4gICAgfVxuXG4gICAgaWYgKGVyciB8fCAhcGFyc2VkIHx8ICFwYXJzZWQub2sgfHwgIXNjYW5uZXIuZW9mKCkpIHtcbiAgICAgIGNvbnN0IHBvc2l0aW9uID0gc2Nhbm5lci5wb3NpdGlvbigpO1xuICAgICAgY29uc3Qgc3ViU3RyID0gdG9tbFN0cmluZy5zbGljZSgwLCBwb3NpdGlvbik7XG4gICAgICBjb25zdCBsaW5lcyA9IHN1YlN0ci5zcGxpdChcIlxcblwiKTtcbiAgICAgIGNvbnN0IHJvdyA9IGxpbmVzLmxlbmd0aDtcbiAgICAgIGNvbnN0IGNvbHVtbiA9ICgoKSA9PiB7XG4gICAgICAgIGxldCBjb3VudCA9IHN1YlN0ci5sZW5ndGg7XG4gICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykge1xuICAgICAgICAgIGlmIChjb3VudCA+IGxpbmUubGVuZ3RoKSB7XG4gICAgICAgICAgICBjb3VudCAtPSBsaW5lLmxlbmd0aCArIDE7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBjb3VudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvdW50O1xuICAgICAgfSkoKTtcbiAgICAgIGNvbnN0IG1lc3NhZ2UgPSBgUGFyc2UgZXJyb3Igb24gbGluZSAke3Jvd30sIGNvbHVtbiAke2NvbHVtbn06ICR7XG4gICAgICAgIGVyciA/IGVyci5tZXNzYWdlIDogYFVuZXhwZWN0ZWQgY2hhcmFjdGVyOiBcIiR7c2Nhbm5lci5jaGFyKCl9XCJgXG4gICAgICB9YDtcbiAgICAgIHRocm93IG5ldyBUT01MUGFyc2VFcnJvcihtZXNzYWdlKTtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnNlZC5ib2R5O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsU0FBUyxTQUFTLFFBQVEsK0JBQStCO0FBOEJ6RCxPQUFPLE1BQU0sdUJBQXVCO0FBQU87QUFFM0MsT0FBTyxNQUFNO0VBR1M7RUFGcEIsQ0FBQyxVQUFVLENBQVc7RUFDdEIsQ0FBQyxRQUFRLENBQUs7RUFDZCxZQUFvQixPQUFnQjtrQkFBaEI7U0FGcEIsQ0FBQyxVQUFVLEdBQUc7U0FDZCxDQUFDLFFBQVEsR0FBRztFQUN5QjtFQUVyQzs7O0dBR0MsR0FDRCxLQUFLLFFBQVEsQ0FBQyxFQUFFO0lBQ2QsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLFFBQVEsR0FBRyxNQUFNLElBQUk7RUFDaEQ7RUFFQTs7OztHQUlDLEdBQ0QsTUFBTSxLQUFhLEVBQUUsR0FBVyxFQUFVO0lBQ3hDLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsUUFBUSxHQUFHLE9BQU8sSUFBSSxDQUFDLENBQUMsUUFBUSxHQUFHO0VBQ3BFO0VBRUE7O0dBRUMsR0FDRCxLQUFLLEtBQWMsRUFBRTtJQUNuQixJQUFJLE9BQU8sVUFBVSxVQUFVO01BQzdCLElBQUssSUFBSSxJQUFJLEdBQUcsSUFBSSxPQUFPLElBQUs7UUFDOUIsSUFBSSxDQUFDLENBQUMsUUFBUTtNQUNoQjtJQUNGLE9BQU87TUFDTCxJQUFJLENBQUMsQ0FBQyxRQUFRO0lBQ2hCO0VBQ0Y7RUFFQTs7O0dBR0MsR0FDRCxjQUNFLFVBQW1EO0lBQUUsU0FBUztFQUFLLENBQUMsRUFDcEU7SUFDQSxJQUFJLFFBQVEsTUFBTSxFQUFFO01BQ2xCLE1BQU8sSUFBSSxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsR0FBSTtRQUN4RCxJQUFJLENBQUMsSUFBSTtNQUNYO0lBQ0YsT0FBTztNQUNMLE1BQU8sQ0FBQyxJQUFJLENBQUMsR0FBRyxHQUFJO1FBQ2xCLE1BQU0sT0FBTyxJQUFJLENBQUMsSUFBSTtRQUN0QixJQUFJLElBQUksQ0FBQyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsU0FBUyxJQUFJLENBQUMsZ0JBQWdCLElBQUk7VUFDMUQsSUFBSSxDQUFDLElBQUk7UUFDWCxPQUFPLElBQUksUUFBUSxPQUFPLElBQUksSUFBSSxDQUFDLElBQUksT0FBTyxLQUFLO1VBQ2pELG1CQUFtQjtVQUNuQixNQUFPLENBQUMsSUFBSSxDQUFDLGdCQUFnQixNQUFNLENBQUMsSUFBSSxDQUFDLEdBQUcsR0FBSTtZQUM5QyxJQUFJLENBQUMsSUFBSTtVQUNYO1FBQ0YsT0FBTztVQUNMO1FBQ0Y7TUFDRjtJQUNGO0lBQ0EsdURBQXVEO0lBQ3ZELElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLE1BQU0sS0FBSyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksS0FBSztNQUN0RCxNQUFNLFVBQVUsUUFBUSxJQUFJLENBQUMsSUFBSSxHQUFHLFVBQVUsQ0FBQyxHQUFHLFFBQVEsQ0FBQztNQUMzRCxNQUFNLElBQUksZUFBZSxDQUFDLGdDQUFnQyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQ3pFO0VBQ0Y7RUFFQTs7R0FFQyxHQUNELE1BQU07SUFDSixPQUFPLElBQUksQ0FBQyxRQUFRLE1BQU0sSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNO0VBQzlDO0VBRUE7O0dBRUMsR0FDRCxXQUFXO0lBQ1QsT0FBTyxJQUFJLENBQUMsQ0FBQyxRQUFRO0VBQ3ZCO0VBRUEsbUJBQW1CO0lBQ2pCLE9BQU8sSUFBSSxDQUFDLElBQUksT0FBTyxRQUFRLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxPQUFPO0VBQ3REO0FBQ0Y7QUFFQSwwQkFBMEI7QUFDMUIsWUFBWTtBQUNaLDBCQUEwQjtBQUUxQixTQUFTLFFBQVcsSUFBTztFQUN6QixPQUFPO0lBQ0wsSUFBSTtJQUNKO0VBQ0Y7QUFDRjtBQUNBLFNBQVM7RUFDUCxPQUFPO0lBQ0wsSUFBSTtFQUNOO0FBQ0Y7QUFFQSxPQUFPLE1BQU0sUUFBUTtFQUNuQixRQUNFLElBQWMsRUFDZCxTQUFrQixDQUFDLENBQUMsRUFDcEIsSUFBYztJQUVkLE1BQU0sTUFBK0IsQ0FBQztJQUN0QyxJQUFJLEtBQUssTUFBTSxLQUFLLEdBQUc7TUFDckIsT0FBTztJQUNULE9BQU87TUFDTCxJQUFJLENBQUMsTUFBTTtRQUNULE9BQU87TUFDVDtNQUNBLE1BQU0sTUFBMEIsSUFBSSxDQUFDLEtBQUssTUFBTSxHQUFHLEVBQUU7TUFDckQsSUFBSSxPQUFPLFFBQVEsVUFBVTtRQUMzQixHQUFHLENBQUMsSUFBSSxHQUFHO01BQ2I7TUFDQSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksUUFBUTtJQUNoRDtFQUNGO0VBQ0EscUJBQW9CLE1BQStCLEVBQUUsS0FJcEQ7SUFDQyxJQUFJLE1BQU0sR0FBRyxDQUFDLE1BQU0sS0FBSyxHQUFHO01BQzFCLE1BQU0sSUFBSSxNQUFNO0lBQ2xCO0lBQ0EsTUFBTSxRQUFRLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7SUFFbEMsSUFBSSxPQUFPLFVBQVUsYUFBYTtNQUNoQyxPQUFPLE1BQU0sQ0FDWCxRQUNBLElBQUksQ0FBQyxNQUFNLENBQ1QsTUFBTSxHQUFHLEVBQ1QsTUFBTSxJQUFJLEtBQUssVUFBVSxNQUFNLEtBQUssR0FBRztRQUFDLE1BQU0sS0FBSztPQUFDO0lBRzFELE9BQU8sSUFBSSxNQUFNLE9BQU8sQ0FBQyxRQUFRO01BQy9CLElBQUksTUFBTSxJQUFJLEtBQUssZ0JBQWdCLE1BQU0sR0FBRyxDQUFDLE1BQU0sS0FBSyxHQUFHO1FBQ3pELE1BQU0sSUFBSSxDQUFDLE1BQU0sS0FBSztNQUN4QixPQUFPO1FBQ0wsTUFBTSxPQUFPLEtBQUssQ0FBQyxNQUFNLE1BQU0sR0FBRyxFQUFFO1FBQ3BDLE1BQU0sbUJBQW1CLENBQUMsTUFBTTtVQUM5QixNQUFNLE1BQU0sSUFBSTtVQUNoQixLQUFLLE1BQU0sR0FBRyxDQUFDLEtBQUssQ0FBQztVQUNyQixPQUFPLE1BQU0sS0FBSztRQUNwQjtNQUNGO0lBQ0YsT0FBTyxJQUFJLE9BQU8sVUFBVSxZQUFZLFVBQVUsTUFBTTtNQUN0RCxNQUFNLG1CQUFtQixDQUFDLE9BQWtDO1FBQzFELE1BQU0sTUFBTSxJQUFJO1FBQ2hCLEtBQUssTUFBTSxHQUFHLENBQUMsS0FBSyxDQUFDO1FBQ3JCLE9BQU8sTUFBTSxLQUFLO01BQ3BCO0lBQ0YsT0FBTztNQUNMLE1BQU0sSUFBSSxNQUFNO0lBQ2xCO0VBQ0Y7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQ3BDLG9DQUFvQztBQUNwQyxvQ0FBb0M7QUFFcEMsU0FBUyxHQUFNLE9BQTZCO0VBQzFDLE9BQU8sU0FBUyxHQUFHLE9BQWdCO0lBQ2pDLEtBQUssTUFBTSxTQUFTLFFBQVM7TUFDM0IsTUFBTSxTQUFTLE1BQU07TUFDckIsSUFBSSxPQUFPLEVBQUUsRUFBRTtRQUNiLE9BQU87TUFDVDtJQUNGO0lBQ0EsT0FBTztFQUNUO0FBQ0Y7QUFFQSxTQUFTLEtBQ1AsTUFBMEIsRUFDMUIsU0FBaUI7RUFFakIsTUFBTSxZQUFZLFVBQVU7RUFDNUIsT0FBTyxTQUFTLEtBQUssT0FBZ0I7SUFDbkMsTUFBTSxRQUFRLE9BQU87SUFDckIsSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFO01BQ2IsT0FBTztJQUNUO0lBQ0EsTUFBTSxNQUFXO01BQUMsTUFBTSxJQUFJO0tBQUM7SUFDN0IsTUFBTyxDQUFDLFFBQVEsR0FBRyxHQUFJO01BQ3JCLElBQUksQ0FBQyxVQUFVLFNBQVMsRUFBRSxFQUFFO1FBQzFCO01BQ0Y7TUFDQSxNQUFNLFNBQVMsT0FBTztNQUN0QixJQUFJLE9BQU8sRUFBRSxFQUFFO1FBQ2IsSUFBSSxJQUFJLENBQUMsT0FBTyxJQUFJO01BQ3RCLE9BQU87UUFDTCxNQUFNLElBQUksZUFBZSxDQUFDLHFCQUFxQixFQUFFLFVBQVUsQ0FBQyxDQUFDO01BQy9EO0lBQ0Y7SUFDQSxPQUFPLFFBQVE7RUFDakI7QUFDRjtBQUVBLFNBQVMsR0FDUCxTQUFvQyxFQUNwQyxTQUFpQixFQUNqQixXQUErQjtFQUUvQixNQUFNLFlBQVksVUFBVTtFQUM1QixPQUFPLFNBQVMsR0FDZCxPQUFnQjtJQUVoQixNQUFNLE1BQU0sVUFBVTtJQUN0QixJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUU7TUFDWCxPQUFPO0lBQ1Q7SUFDQSxNQUFNLE1BQU0sVUFBVTtJQUN0QixJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUU7TUFDWCxNQUFNLElBQUksZUFBZSxDQUFDLDZCQUE2QixFQUFFLFVBQVUsQ0FBQyxDQUFDO0lBQ3ZFO0lBQ0EsTUFBTSxRQUFRLFlBQVk7SUFDMUIsSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFO01BQ2IsTUFBTSxJQUFJLGVBQ1IsQ0FBQyw4Q0FBOEMsQ0FBQztJQUVwRDtJQUNBLE9BQU8sUUFBUSxNQUFNLE1BQU0sQ0FBQyxJQUFJLElBQUksRUFBRSxNQUFNLElBQUk7RUFDbEQ7QUFDRjtBQUVBLFNBQVMsTUFDUCxNQUFrQztFQUVsQyxPQUFPLFNBQVMsTUFDZCxPQUFnQjtJQUVoQixNQUFNLFNBQVMsT0FBTztJQUN0QixJQUFJLENBQUMsT0FBTyxFQUFFLEVBQUU7TUFDZCxPQUFPO0lBQ1Q7SUFDQSxJQUFJLE9BQU8sQ0FBQztJQUNaLEtBQUssTUFBTSxVQUFVLE9BQU8sSUFBSSxDQUFFO01BQ2hDLElBQUksT0FBTyxTQUFTLFlBQVksU0FBUyxNQUFNO1FBQzdDLG1DQUFtQztRQUNuQyxPQUFPLFVBQVUsTUFBTTtNQUN6QjtJQUNGO0lBQ0EsT0FBTyxRQUFRO0VBQ2pCO0FBQ0Y7QUFFQSxTQUFTLE9BQ1AsTUFBMEI7RUFFMUIsT0FBTyxTQUFTLE9BQ2QsT0FBZ0I7SUFFaEIsTUFBTSxPQUFZLEVBQUU7SUFDcEIsTUFBTyxDQUFDLFFBQVEsR0FBRyxHQUFJO01BQ3JCLE1BQU0sU0FBUyxPQUFPO01BQ3RCLElBQUksT0FBTyxFQUFFLEVBQUU7UUFDYixLQUFLLElBQUksQ0FBQyxPQUFPLElBQUk7TUFDdkIsT0FBTztRQUNMO01BQ0Y7TUFDQSxRQUFRLGFBQWE7SUFDdkI7SUFDQSxJQUFJLEtBQUssTUFBTSxLQUFLLEdBQUc7TUFDckIsT0FBTztJQUNUO0lBQ0EsT0FBTyxRQUFRO0VBQ2pCO0FBQ0Y7QUFFQSxTQUFTLFNBQ1AsSUFBWSxFQUNaLE1BQTBCLEVBQzFCLEtBQWE7RUFFYixNQUFNLE9BQU8sVUFBVTtFQUN2QixNQUFNLFFBQVEsVUFBVTtFQUN4QixPQUFPLFNBQVMsU0FBUyxPQUFnQjtJQUN2QyxJQUFJLENBQUMsS0FBSyxTQUFTLEVBQUUsRUFBRTtNQUNyQixPQUFPO0lBQ1Q7SUFDQSxNQUFNLFNBQVMsT0FBTztJQUN0QixJQUFJLENBQUMsT0FBTyxFQUFFLEVBQUU7TUFDZCxNQUFNLElBQUksZUFBZSxDQUFDLHFCQUFxQixFQUFFLEtBQUssQ0FBQyxDQUFDO0lBQzFEO0lBQ0EsSUFBSSxDQUFDLE1BQU0sU0FBUyxFQUFFLEVBQUU7TUFDdEIsTUFBTSxJQUFJLGVBQ1IsQ0FBQyxlQUFlLEVBQUUsTUFBTSxzQkFBc0IsRUFBRSxLQUFLLENBQUMsQ0FBQztJQUUzRDtJQUNBLE9BQU8sUUFBUSxPQUFPLElBQUk7RUFDNUI7QUFDRjtBQUVBLFNBQVMsVUFBVSxHQUFXO0VBQzVCLE9BQU8sU0FBUyxVQUFVLE9BQWdCO0lBQ3hDLFFBQVEsYUFBYSxDQUFDO01BQUUsUUFBUTtJQUFLO0lBQ3JDLElBQUksUUFBUSxLQUFLLENBQUMsR0FBRyxJQUFJLE1BQU0sTUFBTSxLQUFLO01BQ3hDLFFBQVEsSUFBSSxDQUFDLElBQUksTUFBTTtJQUN6QixPQUFPO01BQ0wsT0FBTztJQUNUO0lBQ0EsUUFBUSxhQUFhLENBQUM7TUFBRSxRQUFRO0lBQUs7SUFDckMsT0FBTyxRQUFRO0VBQ2pCO0FBQ0Y7QUFFQSwwQkFBMEI7QUFDMUIsb0JBQW9CO0FBQ3BCLDBCQUEwQjtBQUUxQixNQUFNLFdBQVc7RUFDZixVQUFVO0VBQ1YsT0FBTztFQUNQLGNBQWM7QUFDaEI7QUFFQSxPQUFPLFNBQVMsUUFBUSxPQUFnQjtFQUN0QyxRQUFRLGFBQWEsQ0FBQztJQUFFLFFBQVE7RUFBSztFQUNyQyxJQUFJLENBQUMsUUFBUSxJQUFJLE1BQU0sQ0FBQyxTQUFTLFFBQVEsQ0FBQyxJQUFJLENBQUMsUUFBUSxJQUFJLEtBQUs7SUFDOUQsT0FBTztFQUNUO0VBQ0EsTUFBTSxNQUFnQixFQUFFO0VBQ3hCLE1BQU8sUUFBUSxJQUFJLE1BQU0sU0FBUyxRQUFRLENBQUMsSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFLO0lBQy9ELElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSTtJQUNyQixRQUFRLElBQUk7RUFDZDtFQUNBLE1BQU0sTUFBTSxJQUFJLElBQUksQ0FBQztFQUNyQixPQUFPLFFBQVE7QUFDakI7QUFFQSxTQUFTLGVBQWUsT0FBZ0I7RUFDdEMsSUFBSSxRQUFRLElBQUksT0FBTyxNQUFNO0lBQzNCLFFBQVEsSUFBSTtJQUNaLDRDQUE0QztJQUM1QyxPQUFRLFFBQVEsSUFBSTtNQUNsQixLQUFLO1FBQ0gsUUFBUSxJQUFJO1FBQ1osT0FBTyxRQUFRO01BQ2pCLEtBQUs7UUFDSCxRQUFRLElBQUk7UUFDWixPQUFPLFFBQVE7TUFDakIsS0FBSztRQUNILFFBQVEsSUFBSTtRQUNaLE9BQU8sUUFBUTtNQUNqQixLQUFLO1FBQ0gsUUFBUSxJQUFJO1FBQ1osT0FBTyxRQUFRO01BQ2pCLEtBQUs7UUFDSCxRQUFRLElBQUk7UUFDWixPQUFPLFFBQVE7TUFDakIsS0FBSztNQUNMLEtBQUs7UUFBSztVQUNSLG9CQUFvQjtVQUNwQixNQUFNLGVBQWUsUUFBUSxJQUFJLE9BQU8sTUFBTSxJQUFJO1VBQ2xELE1BQU0sWUFBWSxTQUNoQixPQUFPLFFBQVEsS0FBSyxDQUFDLEdBQUcsSUFBSSxlQUM1QjtVQUVGLE1BQU0sTUFBTSxPQUFPLGFBQWEsQ0FBQztVQUNqQyxRQUFRLElBQUksQ0FBQyxlQUFlO1VBQzVCLE9BQU8sUUFBUTtRQUNqQjtNQUNBLEtBQUs7UUFDSCxRQUFRLElBQUk7UUFDWixPQUFPLFFBQVE7TUFDakIsS0FBSztRQUNILFFBQVEsSUFBSTtRQUNaLE9BQU8sUUFBUTtNQUNqQjtRQUNFLFFBQVEsSUFBSTtRQUNaLE9BQU8sUUFBUSxRQUFRLElBQUk7SUFDL0I7RUFDRixPQUFPO0lBQ0wsT0FBTztFQUNUO0FBQ0Y7QUFFQSxPQUFPLFNBQVMsWUFBWSxPQUFnQjtFQUMxQyxRQUFRLGFBQWEsQ0FBQztJQUFFLFFBQVE7RUFBSztFQUNyQyxJQUFJLFFBQVEsSUFBSSxPQUFPLEtBQUs7SUFDMUIsUUFBUSxJQUFJO0VBQ2QsT0FBTztJQUNMLE9BQU87RUFDVDtFQUNBLE1BQU0sTUFBTSxFQUFFO0VBQ2QsTUFBTyxRQUFRLElBQUksT0FBTyxPQUFPLENBQUMsUUFBUSxHQUFHLEdBQUk7SUFDL0MsSUFBSSxRQUFRLElBQUksT0FBTyxNQUFNO01BQzNCLE1BQU0sSUFBSSxlQUFlO0lBQzNCO0lBQ0EsTUFBTSxjQUFjLGVBQWU7SUFDbkMsSUFBSSxZQUFZLEVBQUUsRUFBRTtNQUNsQixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUk7SUFDM0IsT0FBTztNQUNMLElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSTtNQUNyQixRQUFRLElBQUk7SUFDZDtFQUNGO0VBQ0EsSUFBSSxRQUFRLEdBQUcsSUFBSTtJQUNqQixNQUFNLElBQUksZUFDUixDQUFDLG1DQUFtQyxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQztFQUV4RDtFQUNBLFFBQVEsSUFBSSxJQUFJLGdCQUFnQjtFQUNoQyxPQUFPLFFBQVEsSUFBSSxJQUFJLENBQUM7QUFDMUI7QUFFQSxPQUFPLFNBQVMsY0FBYyxPQUFnQjtFQUM1QyxRQUFRLGFBQWEsQ0FBQztJQUFFLFFBQVE7RUFBSztFQUNyQyxJQUFJLFFBQVEsSUFBSSxPQUFPLEtBQUs7SUFDMUIsUUFBUSxJQUFJO0VBQ2QsT0FBTztJQUNMLE9BQU87RUFDVDtFQUNBLE1BQU0sTUFBZ0IsRUFBRTtFQUN4QixNQUFPLFFBQVEsSUFBSSxPQUFPLE9BQU8sQ0FBQyxRQUFRLEdBQUcsR0FBSTtJQUMvQyxJQUFJLFFBQVEsSUFBSSxPQUFPLE1BQU07TUFDM0IsTUFBTSxJQUFJLGVBQWU7SUFDM0I7SUFDQSxJQUFJLElBQUksQ0FBQyxRQUFRLElBQUk7SUFDckIsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxJQUFJLFFBQVEsR0FBRyxJQUFJO0lBQ2pCLE1BQU0sSUFBSSxlQUNSLENBQUMsbUNBQW1DLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDO0VBRXhEO0VBQ0EsUUFBUSxJQUFJLElBQUksZ0JBQWdCO0VBQ2hDLE9BQU8sUUFBUSxJQUFJLElBQUksQ0FBQztBQUMxQjtBQUVBLE9BQU8sU0FBUyxxQkFDZCxPQUFnQjtFQUVoQixRQUFRLGFBQWEsQ0FBQztJQUFFLFFBQVE7RUFBSztFQUNyQyxJQUFJLFFBQVEsS0FBSyxDQUFDLEdBQUcsT0FBTyxPQUFPO0lBQ2pDLFFBQVEsSUFBSSxDQUFDO0VBQ2YsT0FBTztJQUNMLE9BQU87RUFDVDtFQUNBLElBQUksUUFBUSxJQUFJLE9BQU8sTUFBTTtJQUMzQiwrQkFBK0I7SUFDL0IsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxNQUFNLE1BQWdCLEVBQUU7RUFDeEIsTUFBTyxRQUFRLEtBQUssQ0FBQyxHQUFHLE9BQU8sU0FBUyxDQUFDLFFBQVEsR0FBRyxHQUFJO0lBQ3RELHdCQUF3QjtJQUN4QixJQUFJLFFBQVEsS0FBSyxDQUFDLEdBQUcsT0FBTyxRQUFRO01BQ2xDLFFBQVEsSUFBSTtNQUNaLFFBQVEsYUFBYSxDQUFDO1FBQUUsU0FBUztNQUFNO01BQ3ZDO0lBQ0Y7SUFDQSxNQUFNLGNBQWMsZUFBZTtJQUNuQyxJQUFJLFlBQVksRUFBRSxFQUFFO01BQ2xCLElBQUksSUFBSSxDQUFDLFlBQVksSUFBSTtJQUMzQixPQUFPO01BQ0wsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJO01BQ3JCLFFBQVEsSUFBSTtJQUNkO0VBQ0Y7RUFFQSxJQUFJLFFBQVEsR0FBRyxJQUFJO0lBQ2pCLE1BQU0sSUFBSSxlQUNSLENBQUMsa0NBQWtDLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDO0VBRXZEO0VBQ0Esa0RBQWtEO0VBQ2xELElBQUksUUFBUSxJQUFJLENBQUMsT0FBTyxLQUFLO0lBQzNCLElBQUksSUFBSSxDQUFDO0lBQ1QsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxRQUFRLElBQUksQ0FBQyxJQUFJLGtCQUFrQjtFQUNuQyxPQUFPLFFBQVEsSUFBSSxJQUFJLENBQUM7QUFDMUI7QUFFQSxPQUFPLFNBQVMsdUJBQ2QsT0FBZ0I7RUFFaEIsUUFBUSxhQUFhLENBQUM7SUFBRSxRQUFRO0VBQUs7RUFDckMsSUFBSSxRQUFRLEtBQUssQ0FBQyxHQUFHLE9BQU8sT0FBTztJQUNqQyxRQUFRLElBQUksQ0FBQztFQUNmLE9BQU87SUFDTCxPQUFPO0VBQ1Q7RUFDQSxJQUFJLFFBQVEsSUFBSSxPQUFPLE1BQU07SUFDM0IsK0JBQStCO0lBQy9CLFFBQVEsSUFBSTtFQUNkO0VBQ0EsTUFBTSxNQUFnQixFQUFFO0VBQ3hCLE1BQU8sUUFBUSxLQUFLLENBQUMsR0FBRyxPQUFPLFNBQVMsQ0FBQyxRQUFRLEdBQUcsR0FBSTtJQUN0RCxJQUFJLElBQUksQ0FBQyxRQUFRLElBQUk7SUFDckIsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxJQUFJLFFBQVEsR0FBRyxJQUFJO0lBQ2pCLE1BQU0sSUFBSSxlQUNSLENBQUMsa0NBQWtDLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDO0VBRXZEO0VBQ0Esa0RBQWtEO0VBQ2xELElBQUksUUFBUSxJQUFJLENBQUMsT0FBTyxLQUFLO0lBQzNCLElBQUksSUFBSSxDQUFDO0lBQ1QsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxRQUFRLElBQUksQ0FBQyxJQUFJLGtCQUFrQjtFQUNuQyxPQUFPLFFBQVEsSUFBSSxJQUFJLENBQUM7QUFDMUI7QUFFQSxNQUFNLGNBQW1DO0VBQ3ZDO0lBQUM7SUFBUTtHQUFLO0VBQ2Q7SUFBQztJQUFTO0dBQU07RUFDaEI7SUFBQztJQUFPO0dBQVM7RUFDakI7SUFBQztJQUFRO0dBQVM7RUFDbEI7SUFBQztJQUFRLENBQUM7R0FBUztFQUNuQjtJQUFDO0lBQU87R0FBSTtFQUNaO0lBQUM7SUFBUTtHQUFJO0VBQ2I7SUFBQztJQUFRO0dBQUk7Q0FDZDtBQUNELE9BQU8sU0FBUyxRQUFRLE9BQWdCO0VBQ3RDLFFBQVEsYUFBYSxDQUFDO0lBQUUsUUFBUTtFQUFLO0VBQ3JDLE1BQU0sUUFBUSxZQUFZLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUNuQyxRQUFRLEtBQUssQ0FBQyxHQUFHLElBQUksTUFBTSxNQUFNO0VBRW5DLElBQUksQ0FBQyxPQUFPO0lBQ1YsT0FBTztFQUNUO0VBQ0EsTUFBTSxDQUFDLEtBQUssTUFBTSxHQUFHO0VBQ3JCLFFBQVEsSUFBSSxDQUFDLElBQUksTUFBTTtFQUN2QixPQUFPLFFBQVE7QUFDakI7QUFFQSxPQUFPLE1BQU0sWUFBWSxLQUN2QixHQUFHO0VBQUM7RUFBUztFQUFhO0NBQWMsR0FDeEMsS0FDQTtBQUVGLE9BQU8sU0FBUyxRQUFRLE9BQWdCO0VBQ3RDLFFBQVEsYUFBYSxDQUFDO0lBQUUsUUFBUTtFQUFLO0VBRXJDLDBCQUEwQjtFQUMxQixNQUFNLFNBQVMsUUFBUSxLQUFLLENBQUMsR0FBRztFQUNoQyxJQUFJLE9BQU8sTUFBTSxLQUFLLEtBQUssY0FBYyxJQUFJLENBQUMsU0FBUztJQUNyRCxRQUFRLElBQUksQ0FBQztJQUNiLE1BQU0sTUFBTTtNQUFDO0tBQU87SUFDcEIsTUFBTyxhQUFhLElBQUksQ0FBQyxRQUFRLElBQUksT0FBTyxDQUFDLFFBQVEsR0FBRyxHQUFJO01BQzFELElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSTtNQUNyQixRQUFRLElBQUk7SUFDZDtJQUNBLElBQUksSUFBSSxNQUFNLEtBQUssR0FBRztNQUNwQixPQUFPO0lBQ1Q7SUFDQSxPQUFPLFFBQVEsSUFBSSxJQUFJLENBQUM7RUFDMUI7RUFFQSxNQUFNLE1BQU0sRUFBRTtFQUNkLElBQUksT0FBTyxJQUFJLENBQUMsUUFBUSxJQUFJLEtBQUs7SUFDL0IsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJO0lBQ3JCLFFBQVEsSUFBSTtFQUNkO0VBQ0EsTUFBTyxTQUFTLElBQUksQ0FBQyxRQUFRLElBQUksT0FBTyxDQUFDLFFBQVEsR0FBRyxHQUFJO0lBQ3RELElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSTtJQUNyQixRQUFRLElBQUk7RUFDZDtFQUVBLElBQUksSUFBSSxNQUFNLEtBQUssS0FBTSxJQUFJLE1BQU0sS0FBSyxLQUFLLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEdBQUk7SUFDakUsT0FBTztFQUNUO0VBRUEsTUFBTSxNQUFNLFNBQVMsSUFBSSxNQUFNLENBQUMsQ0FBQyxPQUFTLFNBQVMsS0FBSyxJQUFJLENBQUM7RUFDN0QsT0FBTyxRQUFRO0FBQ2pCO0FBRUEsT0FBTyxTQUFTLE1BQU0sT0FBZ0I7RUFDcEMsUUFBUSxhQUFhLENBQUM7SUFBRSxRQUFRO0VBQUs7RUFFckMsdUVBQXVFO0VBQ3ZFLElBQUksV0FBVztFQUNmLE1BQ0UsUUFBUSxJQUFJLENBQUMsYUFDYixDQUFDLFNBQVMsWUFBWSxDQUFDLElBQUksQ0FBQyxRQUFRLElBQUksQ0FBQyxXQUN6QztJQUNBLElBQUksQ0FBQyxTQUFTLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsWUFBWTtNQUNoRCxPQUFPO0lBQ1Q7SUFDQTtFQUNGO0VBRUEsTUFBTSxNQUFNLEVBQUU7RUFDZCxJQUFJLE9BQU8sSUFBSSxDQUFDLFFBQVEsSUFBSSxLQUFLO0lBQy9CLElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSTtJQUNyQixRQUFRLElBQUk7RUFDZDtFQUNBLE1BQU8sU0FBUyxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsSUFBSSxPQUFPLENBQUMsUUFBUSxHQUFHLEdBQUk7SUFDNUQsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJO0lBQ3JCLFFBQVEsSUFBSTtFQUNkO0VBRUEsSUFBSSxJQUFJLE1BQU0sS0FBSyxHQUFHO0lBQ3BCLE9BQU87RUFDVDtFQUNBLE1BQU0sUUFBUSxXQUFXLElBQUksTUFBTSxDQUFDLENBQUMsT0FBUyxTQUFTLEtBQUssSUFBSSxDQUFDO0VBQ2pFLElBQUksTUFBTSxRQUFRO0lBQ2hCLE9BQU87RUFDVDtFQUVBLE9BQU8sUUFBUTtBQUNqQjtBQUVBLE9BQU8sU0FBUyxTQUFTLE9BQWdCO0VBQ3ZDLFFBQVEsYUFBYSxDQUFDO0lBQUUsUUFBUTtFQUFLO0VBRXJDLElBQUksVUFBVSxRQUFRLEtBQUssQ0FBQyxHQUFHO0VBQy9CLHNCQUFzQjtFQUN0QixJQUFJLHFCQUFxQixJQUFJLENBQUMsVUFBVTtJQUN0QyxRQUFRLElBQUksQ0FBQztFQUNmLE9BQU87SUFDTCxPQUFPO0VBQ1Q7RUFFQSxNQUFNLE1BQU0sRUFBRTtFQUNkLGdDQUFnQztFQUNoQyxNQUFPLGNBQWMsSUFBSSxDQUFDLFFBQVEsSUFBSSxPQUFPLENBQUMsUUFBUSxHQUFHLEdBQUk7SUFDM0QsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJO0lBQ3JCLFFBQVEsSUFBSTtFQUNkO0VBQ0EsV0FBVyxJQUFJLElBQUksQ0FBQztFQUNwQixNQUFNLE9BQU8sSUFBSSxLQUFLLFFBQVEsSUFBSTtFQUNsQyxlQUFlO0VBQ2YsSUFBSSxNQUFNLEtBQUssT0FBTyxLQUFLO0lBQ3pCLE1BQU0sSUFBSSxlQUFlLENBQUMscUJBQXFCLEVBQUUsUUFBUSxDQUFDLENBQUM7RUFDN0Q7RUFFQSxPQUFPLFFBQVE7QUFDakI7QUFFQSxPQUFPLFNBQVMsVUFBVSxPQUFnQjtFQUN4QyxRQUFRLGFBQWEsQ0FBQztJQUFFLFFBQVE7RUFBSztFQUVyQyxJQUFJLFVBQVUsUUFBUSxLQUFLLENBQUMsR0FBRztFQUMvQixJQUFJLDJCQUEyQixJQUFJLENBQUMsVUFBVTtJQUM1QyxRQUFRLElBQUksQ0FBQztFQUNmLE9BQU87SUFDTCxPQUFPO0VBQ1Q7RUFFQSxNQUFNLE1BQU0sRUFBRTtFQUNkLElBQUksUUFBUSxJQUFJLE9BQU8sS0FBSztJQUMxQixJQUFJLElBQUksQ0FBQyxRQUFRLElBQUk7SUFDckIsUUFBUSxJQUFJO0VBQ2QsT0FBTztJQUNMLE9BQU8sUUFBUTtFQUNqQjtFQUVBLE1BQU8sUUFBUSxJQUFJLENBQUMsUUFBUSxJQUFJLE9BQU8sQ0FBQyxRQUFRLEdBQUcsR0FBSTtJQUNyRCxJQUFJLElBQUksQ0FBQyxRQUFRLElBQUk7SUFDckIsUUFBUSxJQUFJO0VBQ2Q7RUFDQSxXQUFXLElBQUksSUFBSSxDQUFDO0VBQ3BCLE9BQU8sUUFBUTtBQUNqQjtBQUVBLE9BQU8sU0FBUyxXQUFXLE9BQWdCO0VBQ3pDLFFBQVEsYUFBYSxDQUFDO0lBQUUsUUFBUTtFQUFLO0VBRXJDLElBQUksUUFBUSxJQUFJLE9BQU8sS0FBSztJQUMxQixRQUFRLElBQUk7RUFDZCxPQUFPO0lBQ0wsT0FBTztFQUNUO0VBRUEsTUFBTSxRQUFtQixFQUFFO0VBQzNCLE1BQU8sQ0FBQyxRQUFRLEdBQUcsR0FBSTtJQUNyQixRQUFRLGFBQWE7SUFDckIsTUFBTSxTQUFTLE1BQU07SUFDckIsSUFBSSxPQUFPLEVBQUUsRUFBRTtNQUNiLE1BQU0sSUFBSSxDQUFDLE9BQU8sSUFBSTtJQUN4QixPQUFPO01BQ0w7SUFDRjtJQUNBLFFBQVEsYUFBYSxDQUFDO01BQUUsUUFBUTtJQUFLO0lBQ3JDLCtEQUErRDtJQUMvRCxJQUFJLFFBQVEsSUFBSSxPQUFPLEtBQUs7TUFDMUIsUUFBUSxJQUFJO0lBQ2QsT0FBTztNQUNMO0lBQ0Y7RUFDRjtFQUNBLFFBQVEsYUFBYTtFQUVyQixJQUFJLFFBQVEsSUFBSSxPQUFPLEtBQUs7SUFDMUIsUUFBUSxJQUFJO0VBQ2QsT0FBTztJQUNMLE1BQU0sSUFBSSxlQUFlO0VBQzNCO0VBRUEsT0FBTyxRQUFRO0FBQ2pCO0FBRUEsT0FBTyxTQUFTLFlBQ2QsT0FBZ0I7RUFFaEIsUUFBUSxhQUFhO0VBQ3JCLElBQUksUUFBUSxJQUFJLENBQUMsT0FBTyxLQUFLO0lBQzNCLFFBQVEsSUFBSSxDQUFDO0lBQ2IsT0FBTyxRQUFRLENBQUM7RUFDbEI7RUFDQSxNQUFNLFFBQVEsU0FDWixLQUNBLEtBQUssTUFBTSxNQUNYLEtBQ0E7RUFDRixJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUU7SUFDYixPQUFPO0VBQ1Q7RUFDQSxJQUFJLFFBQVEsQ0FBQztFQUNiLEtBQUssTUFBTSxRQUFRLE1BQU0sSUFBSSxDQUFFO0lBQzdCLFFBQVEsVUFBVSxPQUFPO0VBQzNCO0VBQ0EsT0FBTyxRQUFRO0FBQ2pCO0FBRUEsT0FBTyxNQUFNLFFBQVEsR0FBRztFQUN0QjtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0NBQ0QsRUFBRTtBQUVILE9BQU8sTUFBTSxPQUFPLEdBQUcsV0FBVyxLQUFLLE9BQU87QUFFOUMsT0FBTyxTQUFTLE1BQ2QsT0FBZ0I7RUFFaEIsUUFBUSxhQUFhO0VBQ3JCLE1BQU0sU0FBUyxNQUFNLE9BQU8sT0FBTztFQUNuQyxJQUFJLE9BQU8sRUFBRSxFQUFFO0lBQ2IsT0FBTyxRQUFRO01BQ2IsTUFBTTtNQUNOLE9BQU8sT0FBTyxJQUFJO0lBQ3BCO0VBQ0YsT0FBTztJQUNMLE9BQU87RUFDVDtBQUNGO0FBRUEsT0FBTyxNQUFNLGNBQWMsU0FBUyxLQUFLLFdBQVcsS0FBSztBQUV6RCxPQUFPLFNBQVMsTUFDZCxPQUFnQjtFQUVoQixRQUFRLGFBQWE7RUFDckIsTUFBTSxTQUFTLFlBQVk7RUFDM0IsSUFBSSxDQUFDLE9BQU8sRUFBRSxFQUFFO0lBQ2QsT0FBTztFQUNUO0VBQ0EsUUFBUSxhQUFhO0VBQ3JCLE1BQU0sUUFBUSxNQUFNO0VBQ3BCLE9BQU8sUUFBUTtJQUNiLE1BQU07SUFDTixLQUFLLE9BQU8sSUFBSTtJQUNoQixPQUFPLE1BQU0sRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFDO0VBQ3hDO0FBQ0Y7QUFFQSxPQUFPLE1BQU0sbUJBQW1CLFNBQzlCLE1BQ0EsV0FDQSxNQUNBO0FBRUYsT0FBTyxTQUFTLFdBQ2QsT0FBZ0I7RUFFaEIsUUFBUSxhQUFhO0VBQ3JCLE1BQU0sU0FBUyxpQkFBaUI7RUFDaEMsSUFBSSxDQUFDLE9BQU8sRUFBRSxFQUFFO0lBQ2QsT0FBTztFQUNUO0VBQ0EsUUFBUSxhQUFhO0VBQ3JCLE1BQU0sUUFBUSxNQUFNO0VBQ3BCLE9BQU8sUUFBUTtJQUNiLE1BQU07SUFDTixLQUFLLE9BQU8sSUFBSTtJQUNoQixPQUFPLE1BQU0sRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFDO0VBQ3hDO0FBQ0Y7QUFFQSxPQUFPLFNBQVMsS0FDZCxPQUFnQjtFQUVoQixNQUFNLFNBQVMsT0FBTyxHQUFHO0lBQUM7SUFBTztJQUFZO0dBQU0sR0FBRztFQUN0RCxJQUFJLENBQUMsT0FBTyxFQUFFLEVBQUU7SUFDZCxPQUFPO0VBQ1Q7RUFDQSxJQUFJLE9BQU8sQ0FBQztFQUNaLEtBQUssTUFBTSxTQUFTLE9BQU8sSUFBSSxDQUFFO0lBQy9CLE9BQVEsTUFBTSxJQUFJO01BQ2hCLEtBQUs7UUFBUztVQUNaLE9BQU8sVUFBVSxNQUFNLE1BQU0sS0FBSztVQUNsQztRQUNGO01BQ0EsS0FBSztRQUFTO1VBQ1osTUFBTSxtQkFBbUIsQ0FBQyxNQUFNO1VBQ2hDO1FBQ0Y7TUFDQSxLQUFLO1FBQWM7VUFDakIsTUFBTSxtQkFBbUIsQ0FBQyxNQUFNO1VBQ2hDO1FBQ0Y7SUFDRjtFQUNGO0VBQ0EsT0FBTyxRQUFRO0FBQ2pCO0FBRUEsT0FBTyxTQUFTLGNBQWlCLE1BQTBCO0VBQ3pELE9BQU8sU0FBUyxNQUFNLFVBQWtCO0lBQ3RDLE1BQU0sVUFBVSxJQUFJLFFBQVE7SUFFNUIsSUFBSSxTQUFnQztJQUNwQyxJQUFJLE1BQW9CO0lBQ3hCLElBQUk7TUFDRixTQUFTLE9BQU87SUFDbEIsRUFBRSxPQUFPLEdBQUc7TUFDVixNQUFNLGFBQWEsUUFBUSxJQUFJLElBQUksTUFBTTtJQUMzQztJQUVBLElBQUksT0FBTyxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJO01BQ2xELE1BQU0sV0FBVyxRQUFRLFFBQVE7TUFDakMsTUFBTSxTQUFTLFdBQVcsS0FBSyxDQUFDLEdBQUc7TUFDbkMsTUFBTSxRQUFRLE9BQU8sS0FBSyxDQUFDO01BQzNCLE1BQU0sTUFBTSxNQUFNLE1BQU07TUFDeEIsTUFBTSxTQUFTLEFBQUMsQ0FBQTtRQUNkLElBQUksUUFBUSxPQUFPLE1BQU07UUFDekIsS0FBSyxNQUFNLFFBQVEsTUFBTztVQUN4QixJQUFJLFFBQVEsS0FBSyxNQUFNLEVBQUU7WUFDdkIsU0FBUyxLQUFLLE1BQU0sR0FBRztVQUN6QixPQUFPO1lBQ0wsT0FBTztVQUNUO1FBQ0Y7UUFDQSxPQUFPO01BQ1QsQ0FBQTtNQUNBLE1BQU0sVUFBVSxDQUFDLG9CQUFvQixFQUFFLElBQUksU0FBUyxFQUFFLE9BQU8sRUFBRSxFQUM3RCxNQUFNLElBQUksT0FBTyxHQUFHLENBQUMsdUJBQXVCLEVBQUUsUUFBUSxJQUFJLEdBQUcsQ0FBQyxDQUFDLENBQ2hFLENBQUM7TUFDRixNQUFNLElBQUksZUFBZTtJQUMzQjtJQUNBLE9BQU8sT0FBTyxJQUFJO0VBQ3BCO0FBQ0YifQ==