// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { isWindows } from "./_os.ts";
import { isAbsolute as posixIsAbsolute } from "./posix/is_absolute.ts";
import { isAbsolute as windowsIsAbsolute } from "./windows/is_absolute.ts";
/**
 * Verifies whether provided path is absolute
 * @param path to be verified as absolute
 */ export function isAbsolute(path) {
  return isWindows ? windowsIsAbsolute(path) : posixIsAbsolute(path);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3BhdGgvaXNfYWJzb2x1dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuaW1wb3J0IHsgaXNXaW5kb3dzIH0gZnJvbSBcIi4vX29zLnRzXCI7XG5pbXBvcnQgeyBpc0Fic29sdXRlIGFzIHBvc2l4SXNBYnNvbHV0ZSB9IGZyb20gXCIuL3Bvc2l4L2lzX2Fic29sdXRlLnRzXCI7XG5pbXBvcnQgeyBpc0Fic29sdXRlIGFzIHdpbmRvd3NJc0Fic29sdXRlIH0gZnJvbSBcIi4vd2luZG93cy9pc19hYnNvbHV0ZS50c1wiO1xuXG4vKipcbiAqIFZlcmlmaWVzIHdoZXRoZXIgcHJvdmlkZWQgcGF0aCBpcyBhYnNvbHV0ZVxuICogQHBhcmFtIHBhdGggdG8gYmUgdmVyaWZpZWQgYXMgYWJzb2x1dGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzQWJzb2x1dGUocGF0aDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBpc1dpbmRvd3MgPyB3aW5kb3dzSXNBYnNvbHV0ZShwYXRoKSA6IHBvc2l4SXNBYnNvbHV0ZShwYXRoKTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDLFNBQVMsU0FBUyxRQUFRLFdBQVc7QUFDckMsU0FBUyxjQUFjLGVBQWUsUUFBUSx5QkFBeUI7QUFDdkUsU0FBUyxjQUFjLGlCQUFpQixRQUFRLDJCQUEyQjtBQUUzRTs7O0NBR0MsR0FDRCxPQUFPLFNBQVMsV0FBVyxJQUFZO0VBQ3JDLE9BQU8sWUFBWSxrQkFBa0IsUUFBUSxnQkFBZ0I7QUFDL0QifQ==