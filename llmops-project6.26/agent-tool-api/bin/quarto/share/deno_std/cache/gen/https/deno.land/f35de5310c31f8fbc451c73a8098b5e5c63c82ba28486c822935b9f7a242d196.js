// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { load, loadAll } from "./_loader/loader.ts";
/**
 * Parses `content` as single YAML document.
 *
 * Returns a JavaScript object or throws `YAMLError` on error.
 * By default, does not support regexps, functions and undefined. This method is safe for untrusted data.
 */ export function parse(content, options) {
  return load(content, options);
}
export function parseAll(content, iterator, options) {
  return loadAll(content, iterator, options);
}
//# sourceMappingURL=data:application/json;base64,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