// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Name string is a fully-qualified domain name.
 *
 * @example
 * ```ts
 * import { NAMESPACE_DNS } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_DNS); // => 6ba7b810-9dad-11d1-80b4-00c04fd430c8
 * ```
 */ export const NAMESPACE_DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is a URL.
 *
 * @example
 * ```ts
 * import { NAMESPACE_URL } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_URL); // => 6ba7b811-9dad-11d1-80b4-00c04fd430c8
 * ```
 */ export const NAMESPACE_URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is an ISO OID.
 *
 * @example
 * ```ts
 * import { NAMESPACE_OID } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_OID); // => 6ba7b812-9dad-11d1-80b4-00c04fd430c8
 * ```
 */ export const NAMESPACE_OID = "6ba7b812-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is an X.500 DN (in DER or a text output format).
 *
 * @example
 * ```ts
 * import { NAMESPACE_X500 } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_X500); // => 6ba7b814-9dad-11d1-80b4-00c04fd430c8
 * ```
 */ export const NAMESPACE_X500 = "6ba7b814-9dad-11d1-80b4-00c04fd430c8";
//# sourceMappingURL=data:application/json;base64,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