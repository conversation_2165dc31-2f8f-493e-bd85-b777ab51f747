// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
export { DigestContext, instantiate as instantiateWasm } from "./lib/deno_std_wasm_crypto.generated.mjs";
/**
 * All cryptographic hash/digest algorithms supported by std/crypto/_wasm.
 *
 * For algorithms that are supported by WebCrypto, the name here must match the
 * one used by WebCrypto. Otherwise we should prefer the formatting used in the
 * official specification. All names are uppercase to facilitate case-insensitive
 * comparisons required by the WebCrypto spec.
 */ export const digestAlgorithms = [
  "BLAKE2B-128",
  "BLAKE2B-224",
  "BLAKE2B-256",
  "BLAKE2B-384",
  "BLAKE2B",
  "BLAKE2S",
  "BLAKE3",
  "KECCAK-224",
  "KECCAK-256",
  "KECCAK-384",
  "KECCAK-512",
  "SHA-384",
  "SHA3-224",
  "SHA3-256",
  "SHA3-384",
  "SHA3-512",
  "SHAKE128",
  "SHAKE256",
  "TIGER",
  // insecure (length-extendable):
  "RIPEMD-160",
  "SHA-224",
  "SHA-256",
  "SHA-512",
  // insecure (collidable and length-extendable):
  "MD4",
  "MD5",
  "SHA-1"
];
//# sourceMappingURL=data:application/json;base64,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