// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given transformer to all entries in the given record and returns
 * a new record containing the results.
 *
 * @example
 * ```ts
 * import { mapEntries } from "https://deno.land/std@$STD_VERSION/collections/map_entries.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const usersById = {
 *   "a2e": { name: "<PERSON>", age: 22 },
 *   "dfe": { name: "<PERSON>", age: 31 },
 *   "34b": { name: "<PERSON>", age: 58 },
 * } as const;
 * const agesByNames = mapEntries(usersById, ([id, { name, age }]) => [name, age]);
 *
 * assertEquals(
 *   agesByNames,
 *   {
 *     "Kim": 22,
 *     "<PERSON>": 31,
 *     "<PERSON>": 58,
 *   },
 * );
 * ```
 */ export function mapEntries(record, transformer) {
  const ret = {};
  const entries = Object.entries(record);
  for (const entry of entries){
    const [mappedKey, mappedValue] = transformer(entry);
    ret[mappedKey] = mappedValue;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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