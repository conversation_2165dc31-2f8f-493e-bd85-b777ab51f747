// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/** This module is browser compatible. */ /** Compares its two arguments for ascending order using JavaScript's built in comparison operators. */ export function ascend(a, b) {
  return a < b ? -1 : a > b ? 1 : 0;
}
/** Compares its two arguments for descending order using JavaScript's built in comparison operators. */ export function descend(a, b) {
  return a < b ? 1 : a > b ? -1 : 0;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3Vuc3RhYmxlL2NvbXBhcmF0b3JzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vKiogVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLiAqL1xuXG4vKiogQ29tcGFyZXMgaXRzIHR3byBhcmd1bWVudHMgZm9yIGFzY2VuZGluZyBvcmRlciB1c2luZyBKYXZhU2NyaXB0J3MgYnVpbHQgaW4gY29tcGFyaXNvbiBvcGVyYXRvcnMuICovXG5leHBvcnQgZnVuY3Rpb24gYXNjZW5kPFQ+KGE6IFQsIGI6IFQpIHtcbiAgcmV0dXJuIGEgPCBiID8gLTEgOiBhID4gYiA/IDEgOiAwO1xufVxuXG4vKiogQ29tcGFyZXMgaXRzIHR3byBhcmd1bWVudHMgZm9yIGRlc2NlbmRpbmcgb3JkZXIgdXNpbmcgSmF2YVNjcmlwdCdzIGJ1aWx0IGluIGNvbXBhcmlzb24gb3BlcmF0b3JzLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlc2NlbmQ8VD4oYTogVCwgYjogVCkge1xuICByZXR1cm4gYSA8IGIgPyAxIDogYSA+IGIgPyAtMSA6IDA7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHVDQUF1QyxHQUV2QyxxR0FBcUcsR0FDckcsT0FBTyxTQUFTLE9BQVUsQ0FBSSxFQUFFLENBQUk7RUFDbEMsT0FBTyxJQUFJLElBQUksQ0FBQyxJQUFJLElBQUksSUFBSSxJQUFJO0FBQ2xDO0FBRUEsc0dBQXNHLEdBQ3RHLE9BQU8sU0FBUyxRQUFXLENBQUksRUFBRSxDQUFJO0VBQ25DLE9BQU8sSUFBSSxJQUFJLElBQUksSUFBSSxJQUFJLENBQUMsSUFBSTtBQUNsQyJ9