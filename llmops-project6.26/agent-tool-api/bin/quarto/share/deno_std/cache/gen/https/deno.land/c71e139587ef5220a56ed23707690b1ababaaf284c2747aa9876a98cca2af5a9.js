// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * The possible release types are used as an operator for the
 * increment function and as a result of the difference function.
 */ //# sourceMappingURL=data:application/json;base64,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