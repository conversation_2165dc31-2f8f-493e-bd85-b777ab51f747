// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { mapValues } from "./map_values.ts";
/**
 * Applies the given reducer to each group in the given grouping, returning the
 * results together with the respective group keys.
 *
 * @template T input type of an item in a group in the given grouping.
 * @template A type of the accumulator value, which will match the returned record's values.
 * @example
 * ```ts
 * import { reduceGroups } from "https://deno.land/std@$STD_VERSION/collections/reduce_groups.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const votes = {
 *   "Woody": [2, 3, 1, 4],
 *   "Buzz": [5, 9],
 * };
 *
 * const totalVotes = reduceGroups(votes, (sum, it) => sum + it, 0);
 *
 * assertEquals(totalVotes, {
 *   "Woody": 10,
 *   "Buzz": 14,
 * });
 * ```
 */ export function reduceGroups(record, reducer, initialValue) {
  return mapValues(record, (it)=>it.reduce(reducer, initialValue));
}
//# sourceMappingURL=data:application/json;base64,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