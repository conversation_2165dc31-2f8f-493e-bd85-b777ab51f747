// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { validateBinaryLike } from "./_util.ts";
const rfc1924 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!#$%&()*+-;<=>?@^_`{|}~";
const Z85 = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#";
/**
 * @deprecated (will be removed in 0.210.0) Use a `encodeAscii85` instead.
 *
 * Encodes a given Uint8Array into ascii85, supports multiple standards
 * @param uint8 input to encode
 * @param [options] encoding options
 * @param [options.standard=Adobe] encoding standard (Adobe, btoa, RFC 1924 or Z85)
 * @param [options.delimiter] whether to use a delimiter, if supported by encoding standard
 */ export const encode = encodeAscii85;
/**
 * Encodes a given Uint8Array into ascii85, supports multiple standards
 * @param uint8 input to encode
 * @param [options] encoding options
 * @param [options.standard=Adobe] encoding standard (Adobe, btoa, RFC 1924 or Z85)
 * @param [options.delimiter] whether to use a delimiter, if supported by encoding standard
 */ export function encodeAscii85(data, options) {
  let uint8 = validateBinaryLike(data);
  const standard = options?.standard ?? "Adobe";
  let output = [], v, n = 0, difference = 0;
  if (uint8.length % 4 !== 0) {
    const tmp = uint8;
    difference = 4 - tmp.length % 4;
    uint8 = new Uint8Array(tmp.length + difference);
    uint8.set(tmp);
  }
  const view = new DataView(uint8.buffer, uint8.byteOffset, uint8.byteLength);
  for(let i = 0, len = uint8.length; i < len; i += 4){
    v = view.getUint32(i);
    // Adobe and btoa standards compress 4 zeroes to single "z" character
    if ((standard === "Adobe" || standard === "btoa") && v === 0 && i < len - difference - 3) {
      output[n++] = "z";
      continue;
    }
    // btoa compresses 4 spaces - that is, bytes equal to 32 - into single "y" character
    if (standard === "btoa" && v === 538976288) {
      output[n++] = "y";
      continue;
    }
    for(let j = 4; j >= 0; j--){
      output[n + j] = String.fromCharCode(v % 85 + 33);
      v = Math.trunc(v / 85);
    }
    n += 5;
  }
  switch(standard){
    case "Adobe":
      if (options?.delimiter) {
        return `<~${output.slice(0, output.length - difference).join("")}~>`;
      }
      break;
    case "btoa":
      if (options?.delimiter) {
        return `xbtoa Begin\n${output.slice(0, output.length - difference).join("")}\nxbtoa End`;
      }
      break;
    case "RFC 1924":
      output = output.map((val)=>rfc1924[val.charCodeAt(0) - 33]);
      break;
    case "Z85":
      output = output.map((val)=>Z85[val.charCodeAt(0) - 33]);
      break;
  }
  return output.slice(0, output.length - difference).join("");
}
/**
 * @deprecated (will be removed in 0.210.0) Use a `decodeAscii85` instead.
 *
 * Decodes a given ascii85 encoded string.
 * @param ascii85 input to decode
 * @param [options] decoding options
 * @param [options.standard=Adobe] encoding standard used in the input string (Adobe, btoa, RFC 1924 or Z85)
 */ export const decode = decodeAscii85;
/**
 * Decodes a given ascii85 encoded string.
 * @param ascii85 input to decode
 * @param [options] decoding options
 * @param [options.standard=Adobe] encoding standard used in the input string (Adobe, btoa, RFC 1924 or Z85)
 */ export function decodeAscii85(ascii85, options) {
  const encoding = options?.standard ?? "Adobe";
  // translate all encodings to most basic adobe/btoa one and decompress some special characters ("z" and "y")
  switch(encoding){
    case "Adobe":
      ascii85 = ascii85.replaceAll(/(<~|~>)/g, "").replaceAll("z", "!!!!!");
      break;
    case "btoa":
      ascii85 = ascii85.replaceAll(/(xbtoa Begin|xbtoa End|\n)/g, "").replaceAll("z", "!!!!!").replaceAll("y", "+<VdL");
      break;
    case "RFC 1924":
      ascii85 = ascii85.replaceAll(/./g, (match)=>String.fromCharCode(rfc1924.indexOf(match) + 33));
      break;
    case "Z85":
      ascii85 = ascii85.replaceAll(/./g, (match)=>String.fromCharCode(Z85.indexOf(match) + 33));
      break;
  }
  //remove all invalid characters
  ascii85 = ascii85.replaceAll(/[^!-u]/g, "");
  const len = ascii85.length, output = new Uint8Array(len + 4 - len % 4);
  const view = new DataView(output.buffer);
  let v = 0, n = 0, max = 0;
  for(let i = 0; i < len;){
    for(max += 5; i < max; i++){
      v = v * 85 + (i < len ? ascii85.charCodeAt(i) : 117) - 33;
    }
    view.setUint32(n, v);
    v = 0;
    n += 4;
  }
  return output.slice(0, Math.trunc(len * 0.8));
}
//# sourceMappingURL=data:application/json;base64,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