// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { descend } from "./comparators.ts";
/** Swaps the values at two indexes in an array. */ function swap(array, a, b) {
  const temp = array[a];
  array[a] = array[b];
  array[b] = temp;
}
/** Returns the parent index for a child index. */ function getParentIndex(index) {
  return Math.floor((index + 1) / 2) - 1;
}
/**
 * A priority queue implemented with a binary heap. The heap is in descending
 * order by default, using JavaScript's built-in comparison operators to sort
 * the values.
 *
 * | Method      | Average Case | Worst Case |
 * | ----------- | ------------ | ---------- |
 * | peek()      | O(1)         | O(1)       |
 * | pop()       | O(log n)     | O(log n)   |
 * | push(value) | O(1)         | O(log n)   |
 *
 * @example
 * ```ts
 * import {
 *   ascend,
 *   BinaryHeap,
 *   descend,
 * } from "https://deno.land/std@$STD_VERSION/collections/binary_heap.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const maxHeap = new BinaryHeap<number>();
 * maxHeap.push(4, 1, 3, 5, 2);
 * assertEquals(maxHeap.peek(), 5);
 * assertEquals(maxHeap.pop(), 5);
 * assertEquals([...maxHeap], [4, 3, 2, 1]);
 * assertEquals([...maxHeap], []);
 *
 * const minHeap = new BinaryHeap<number>(ascend);
 * minHeap.push(4, 1, 3, 5, 2);
 * assertEquals(minHeap.peek(), 1);
 * assertEquals(minHeap.pop(), 1);
 * assertEquals([...minHeap], [2, 3, 4, 5]);
 * assertEquals([...minHeap], []);
 *
 * const words = new BinaryHeap<string>((a, b) => descend(a.length, b.length));
 * words.push("truck", "car", "helicopter", "tank");
 * assertEquals(words.peek(), "helicopter");
 * assertEquals(words.pop(), "helicopter");
 * assertEquals([...words], ["truck", "tank", "car"]);
 * assertEquals([...words], []);
 * ```
 */ export class BinaryHeap {
  compare;
  #data;
  constructor(compare = descend){
    this.compare = compare;
    this.#data = [];
  }
  /** Returns the underlying cloned array in arbitrary order without sorting */ toArray() {
    return Array.from(this.#data);
  }
  static from(collection, options) {
    let result;
    let unmappedValues = [];
    if (collection instanceof BinaryHeap) {
      result = new BinaryHeap(options?.compare ?? collection.compare);
      if (options?.compare || options?.map) {
        unmappedValues = collection.#data;
      } else {
        result.#data = Array.from(collection.#data);
      }
    } else {
      result = options?.compare ? new BinaryHeap(options.compare) : new BinaryHeap();
      unmappedValues = collection;
    }
    const values = options?.map ? Array.from(unmappedValues, options.map, options.thisArg) : unmappedValues;
    result.push(...values);
    return result;
  }
  /** The amount of values stored in the binary heap. */ get length() {
    return this.#data.length;
  }
  /** Returns the greatest value in the binary heap, or undefined if it is empty. */ peek() {
    return this.#data[0];
  }
  /** Removes the greatest value from the binary heap and returns it, or null if it is empty. */ pop() {
    const size = this.#data.length - 1;
    swap(this.#data, 0, size);
    let parent = 0;
    let right = 2 * (parent + 1);
    let left = right - 1;
    while(left < size){
      const greatestChild = right === size || this.compare(this.#data[left], this.#data[right]) <= 0 ? left : right;
      if (this.compare(this.#data[greatestChild], this.#data[parent]) < 0) {
        swap(this.#data, parent, greatestChild);
        parent = greatestChild;
      } else {
        break;
      }
      right = 2 * (parent + 1);
      left = right - 1;
    }
    return this.#data.pop();
  }
  /** Adds values to the binary heap. */ push(...values) {
    for (const value of values){
      let index = this.#data.length;
      let parent = getParentIndex(index);
      this.#data.push(value);
      while(index !== 0 && this.compare(this.#data[index], this.#data[parent]) < 0){
        swap(this.#data, parent, index);
        index = parent;
        parent = getParentIndex(index);
      }
    }
    return this.#data.length;
  }
  /** Removes all values from the binary heap. */ clear() {
    this.#data = [];
  }
  /** Checks if the binary heap is empty. */ isEmpty() {
    return this.#data.length === 0;
  }
  /** Returns an iterator for retrieving and removing values from the binary heap. */ *drain() {
    while(!this.isEmpty()){
      yield this.pop();
    }
  }
  *[Symbol.iterator]() {
    yield* this.drain();
  }
}
//# sourceMappingURL=data:application/json;base64,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