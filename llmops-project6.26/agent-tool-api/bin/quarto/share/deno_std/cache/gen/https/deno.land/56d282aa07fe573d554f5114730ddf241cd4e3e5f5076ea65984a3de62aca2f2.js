// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { ANY, MAX, MIN } from "./constants.ts";
import { gt } from "./gt.ts";
import { increment } from "./increment.ts";
/**
 * The minimum semantic version that could match this comparator
 * @param semver The semantic version of the comparator
 * @param operator The operator of the comparator
 * @returns The minimum valid semantic version
 */ export function comparatorMin(semver, operator) {
  if (semver === ANY) {
    return MIN;
  }
  switch(operator){
    case ">":
      return semver.prerelease.length > 0 ? increment(semver, "pre") : increment(semver, "patch");
    case "!=":
    case "!==":
    case "<=":
    case "<":
      // The min(<0.0.0) is MAX
      return gt(semver, MIN) ? MIN : MAX;
    case ">=":
    case "":
    case "=":
    case "==":
    case "===":
      return semver;
  }
}
//# sourceMappingURL=data:application/json;base64,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