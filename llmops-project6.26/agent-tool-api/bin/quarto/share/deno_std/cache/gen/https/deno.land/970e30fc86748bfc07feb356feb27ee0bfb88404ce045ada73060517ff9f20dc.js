// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * Higher level API for dealing with OS signals.
 *
 * @module
 * @deprecated (will be removed in 1.0.0) Use the Deno signals API instead
 */ import { MuxAsyncIterator } from "../async/mux_async_iterator.ts";
import { deferred } from "../async/deferred.ts";
/**
 * Generates an AsyncIterable which can be awaited on for one or more signals.
 * `dispose()` can be called when you are finished waiting on the events.
 *
 * Example:
 *
 * ```ts
 * import { signal } from "https://deno.land/std@$STD_VERSION/signal/mod.ts";
 *
 * const sig = signal("SIGUSR1", "SIGINT");
 * setTimeout(() => {}, 5000); // Prevents exiting immediately
 *
 * for await (const _ of sig) {
 *   // ..
 * }
 *
 * // At some other point in your code when finished listening:
 * sig.dispose();
 * ```
 *
 * @param signals - one or more signals to listen to
 *
 * @deprecated (will be removed in 1.0.0) Use the Deno signals API instead
 */ export function signal(...signals) {
  const mux = new MuxAsyncIterator();
  if (signals.length < 1) {
    throw new Error("No signals are given. You need to specify at least one signal to create a signal stream.");
  }
  const streams = signals.map(createSignalStream);
  streams.forEach((stream)=>{
    mux.add(stream);
  });
  // Create dispose method for the muxer of signal streams.
  const dispose = ()=>{
    streams.forEach((stream)=>{
      stream.dispose();
    });
  };
  return Object.assign(mux, {
    dispose
  });
}
function createSignalStream(signal) {
  let streamContinues = deferred();
  const handler = ()=>{
    streamContinues.resolve(true);
  };
  Deno.addSignalListener(signal, handler);
  const gen = async function*() {
    while(await streamContinues){
      streamContinues = deferred();
      yield undefined;
    }
  };
  return Object.assign(gen(), {
    dispose () {
      streamContinues.resolve(false);
      Deno.removeSignalListener(signal, handler);
    }
  });
}
//# sourceMappingURL=data:application/json;base64,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