// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/** Supporting functions for media_types that do not make part of the public
 * API.
 *
 * @module
 * @private
 */ /** A map of extensions for a given media type. */ export const extensions = new Map();
export function consumeToken(v) {
  const notPos = indexOf(v, isNotTokenChar);
  if (notPos === -1) {
    return [
      v,
      ""
    ];
  }
  if (notPos === 0) {
    return [
      "",
      v
    ];
  }
  return [
    v.slice(0, notPos),
    v.slice(notPos)
  ];
}
export function consumeValue(v) {
  if (!v) {
    return [
      "",
      v
    ];
  }
  if (v[0] !== `"`) {
    return consumeToken(v);
  }
  let value = "";
  for(let i = 1; i < v.length; i++){
    const r = v[i];
    if (r === `"`) {
      return [
        value,
        v.slice(i + 1)
      ];
    }
    if (r === "\\" && i + 1 < v.length && isTSpecial(v[i + 1])) {
      value += v[i + 1];
      i++;
      continue;
    }
    if (r === "\r" || r === "\n") {
      return [
        "",
        v
      ];
    }
    value += v[i];
  }
  return [
    "",
    v
  ];
}
export function consumeMediaParam(v) {
  let rest = v.trimStart();
  if (!rest.startsWith(";")) {
    return [
      "",
      "",
      v
    ];
  }
  rest = rest.slice(1);
  rest = rest.trimStart();
  let param;
  [param, rest] = consumeToken(rest);
  param = param.toLowerCase();
  if (!param) {
    return [
      "",
      "",
      v
    ];
  }
  rest = rest.slice(1);
  rest = rest.trimStart();
  const [value, rest2] = consumeValue(rest);
  if (value === "" && rest2 === rest) {
    return [
      "",
      "",
      v
    ];
  }
  rest = rest2;
  return [
    param,
    value,
    rest
  ];
}
export function decode2331Encoding(v) {
  const sv = v.split(`'`, 3);
  if (sv.length !== 3) {
    return undefined;
  }
  const charset = sv[0].toLowerCase();
  if (!charset) {
    return undefined;
  }
  if (charset !== "us-ascii" && charset !== "utf-8") {
    return undefined;
  }
  const encv = decodeURI(sv[2]);
  if (!encv) {
    return undefined;
  }
  return encv;
}
function indexOf(s, fn) {
  let i = -1;
  for (const v of s){
    i++;
    if (fn(v)) {
      return i;
    }
  }
  return -1;
}
export function isIterator(obj) {
  if (obj === null || obj === undefined) {
    return false;
  }
  // deno-lint-ignore no-explicit-any
  return typeof obj[Symbol.iterator] === "function";
}
export function isToken(s) {
  if (!s) {
    return false;
  }
  return indexOf(s, isNotTokenChar) < 0;
}
function isNotTokenChar(r) {
  return !isTokenChar(r);
}
function isTokenChar(r) {
  const code = r.charCodeAt(0);
  return code > 0x20 && code < 0x7f && !isTSpecial(r);
}
function isTSpecial(r) {
  return `()<>@,;:\\"/[]?=`.includes(r[0]);
}
const CHAR_CODE_SPACE = " ".charCodeAt(0);
const CHAR_CODE_TILDE = "~".charCodeAt(0);
export function needsEncoding(s) {
  for (const b of s){
    const charCode = b.charCodeAt(0);
    if ((charCode < CHAR_CODE_SPACE || charCode > CHAR_CODE_TILDE) && b !== "\t") {
      return true;
    }
  }
  return false;
}
//# sourceMappingURL=data:application/json;base64,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