// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { writeAll } from "./write_all.ts";
function isCloser(value) {
  return typeof value === "object" && value !== null && value !== undefined && "close" in value && // deno-lint-ignore no-explicit-any
  typeof value["close"] === "function";
}
/**
 * @deprecated (will be removed after 1.0.0) Use WritableStream directly.
 *
 * Create a `WritableStream` from a `Writer`.
 */ export function writableStreamFromWriter(writer, options = {}) {
  const { autoClose = true } = options;
  return new WritableStream({
    async write (chunk, controller) {
      try {
        await writeAll(writer, chunk);
      } catch (e) {
        controller.error(e);
        if (isCloser(writer) && autoClose) {
          writer.close();
        }
      }
    },
    close () {
      if (isCloser(writer) && autoClose) {
        writer.close();
      }
    },
    abort () {
      if (isCloser(writer) && autoClose) {
        writer.close();
      }
    }
  });
}
//# sourceMappingURL=data:application/json;base64,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