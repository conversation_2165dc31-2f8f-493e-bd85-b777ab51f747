// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
const textDecoder = new TextDecoder();
export async function toText(readableStream) {
  const reader = readableStream.getReader();
  let result = "";
  while(true){
    const { done, value } = await reader.read();
    if (done) {
      break;
    }
    result += typeof value === "string" ? value : textDecoder.decode(value);
  }
  return result;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3N0cmVhbXMvdG9fdGV4dC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5jb25zdCB0ZXh0RGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdG9UZXh0KFxuICByZWFkYWJsZVN0cmVhbTogUmVhZGFibGVTdHJlYW0sXG4pOiBQcm9taXNlPHN0cmluZz4ge1xuICBjb25zdCByZWFkZXIgPSByZWFkYWJsZVN0cmVhbS5nZXRSZWFkZXIoKTtcbiAgbGV0IHJlc3VsdCA9IFwiXCI7XG5cbiAgd2hpbGUgKHRydWUpIHtcbiAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuXG4gICAgaWYgKGRvbmUpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIHJlc3VsdCArPSB0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIgPyB2YWx1ZSA6IHRleHREZWNvZGVyLmRlY29kZSh2YWx1ZSk7XG4gIH1cblxuICByZXR1cm4gcmVzdWx0O1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsTUFBTSxjQUFjLElBQUk7QUFFeEIsT0FBTyxlQUFlLE9BQ3BCLGNBQThCO0VBRTlCLE1BQU0sU0FBUyxlQUFlLFNBQVM7RUFDdkMsSUFBSSxTQUFTO0VBRWIsTUFBTyxLQUFNO0lBQ1gsTUFBTSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsR0FBRyxNQUFNLE9BQU8sSUFBSTtJQUV6QyxJQUFJLE1BQU07TUFDUjtJQUNGO0lBRUEsVUFBVSxPQUFPLFVBQVUsV0FBVyxRQUFRLFlBQVksTUFBTSxDQUFDO0VBQ25FO0VBRUEsT0FBTztBQUNUIn0=