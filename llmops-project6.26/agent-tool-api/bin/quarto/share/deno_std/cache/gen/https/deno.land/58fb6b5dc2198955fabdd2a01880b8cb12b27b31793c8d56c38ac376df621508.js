// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { ascend } from "./comparators.ts";
import { BinarySearchNode } from "./_binary_search_node.ts";
/**
 * An unbalanced binary search tree. The values are in ascending order by default,
 * using JavaScript's built-in comparison operators to sort the values.
 *
 * For performance, it's recommended that you use a self-balancing binary search
 * tree instead of this one unless you are extending this to create a
 * self-balancing tree. See RedBlackTree for an example of how BinarySearchTree
 *  can be extended to create a self-balancing binary search tree.
 *
 * | Method        | Average Case | Worst Case |
 * | ------------- | ------------ | ---------- |
 * | find(value)   | O(log n)     | O(n)       |
 * | insert(value) | O(log n)     | O(n)       |
 * | remove(value) | O(log n)     | O(n)       |
 * | min()         | O(log n)     | O(n)       |
 * | max()         | O(log n)     | O(n)       |
 *
 * @example
 * ```ts
 * import {
 *   BinarySearchTree,
 * } from "https://deno.land/std@$STD_VERSION/collections/unstable/binary_search_tree.ts";
 * import { ascend, descend } from "https://deno.land/std@$STD_VERSION/collections/unstable/comparators.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const values = [3, 10, 13, 4, 6, 7, 1, 14];
 * const tree = new BinarySearchTree<number>();
 * values.forEach((value) => tree.insert(value));
 * assertEquals([...tree], [1, 3, 4, 6, 7, 10, 13, 14]);
 * assertEquals(tree.min(), 1);
 * assertEquals(tree.max(), 14);
 * assertEquals(tree.find(42), null);
 * assertEquals(tree.find(7), 7);
 * assertEquals(tree.remove(42), false);
 * assertEquals(tree.remove(7), true);
 * assertEquals([...tree], [1, 3, 4, 6, 10, 13, 14]);
 *
 * const invertedTree = new BinarySearchTree<number>(descend);
 * values.forEach((value) => invertedTree.insert(value));
 * assertEquals([...invertedTree], [14, 13, 10, 7, 6, 4, 3, 1]);
 * assertEquals(invertedTree.min(), 14);
 * assertEquals(invertedTree.max(), 1);
 * assertEquals(invertedTree.find(42), null);
 * assertEquals(invertedTree.find(7), 7);
 * assertEquals(invertedTree.remove(42), false);
 * assertEquals(invertedTree.remove(7), true);
 * assertEquals([...invertedTree], [14, 13, 10, 6, 4, 3, 1]);
 *
 * const words = new BinarySearchTree<string>((a, b) =>
 *   ascend(a.length, b.length) || ascend(a, b)
 * );
 * ["truck", "car", "helicopter", "tank", "train", "suv", "semi", "van"]
 *   .forEach((value) => words.insert(value));
 * assertEquals([...words], [
 *   "car",
 *   "suv",
 *   "van",
 *   "semi",
 *   "tank",
 *   "train",
 *   "truck",
 *   "helicopter",
 * ]);
 * assertEquals(words.min(), "car");
 * assertEquals(words.max(), "helicopter");
 * assertEquals(words.find("scooter"), null);
 * assertEquals(words.find("tank"), "tank");
 * assertEquals(words.remove("scooter"), false);
 * assertEquals(words.remove("tank"), true);
 * assertEquals([...words], [
 *   "car",
 *   "suv",
 *   "van",
 *   "semi",
 *   "train",
 *   "truck",
 *   "helicopter",
 * ]);
 * ```
 */ export class BinarySearchTree {
  compare;
  root;
  _size;
  constructor(compare = ascend){
    this.compare = compare;
    this.root = null;
    this._size = 0;
  }
  static from(collection, options) {
    let result;
    let unmappedValues = [];
    if (collection instanceof BinarySearchTree) {
      result = new BinarySearchTree(options?.compare ?? collection.compare);
      if (options?.compare || options?.map) {
        unmappedValues = collection;
      } else {
        const nodes = [];
        if (collection.root) {
          result.root = BinarySearchNode.from(collection.root);
          nodes.push(result.root);
        }
        while(nodes.length){
          const node = nodes.pop();
          const left = node.left ? BinarySearchNode.from(node.left) : null;
          const right = node.right ? BinarySearchNode.from(node.right) : null;
          if (left) {
            left.parent = node;
            nodes.push(left);
          }
          if (right) {
            right.parent = node;
            nodes.push(right);
          }
        }
      }
    } else {
      result = options?.compare ? new BinarySearchTree(options.compare) : new BinarySearchTree();
      unmappedValues = collection;
    }
    const values = options?.map ? Array.from(unmappedValues, options.map, options.thisArg) : unmappedValues;
    for (const value of values)result.insert(value);
    return result;
  }
  /** The amount of values stored in the binary search tree. */ get size() {
    return this._size;
  }
  findNode(value) {
    let node = this.root;
    while(node){
      const order = this.compare(value, node.value);
      if (order === 0) break;
      const direction = order < 0 ? "left" : "right";
      node = node[direction];
    }
    return node;
  }
  rotateNode(node, direction) {
    const replacementDirection = direction === "left" ? "right" : "left";
    if (!node[replacementDirection]) {
      throw new TypeError(`cannot rotate ${direction} without ${replacementDirection} child`);
    }
    const replacement = node[replacementDirection];
    node[replacementDirection] = replacement[direction] ?? null;
    if (replacement[direction]) replacement[direction].parent = node;
    replacement.parent = node.parent;
    if (node.parent) {
      const parentDirection = node === node.parent[direction] ? direction : replacementDirection;
      node.parent[parentDirection] = replacement;
    } else {
      this.root = replacement;
    }
    replacement[direction] = node;
    node.parent = replacement;
  }
  insertNode(Node, value) {
    if (!this.root) {
      this.root = new Node(null, value);
      this._size++;
      return this.root;
    } else {
      let node = this.root;
      while(true){
        const order = this.compare(value, node.value);
        if (order === 0) break;
        const direction = order < 0 ? "left" : "right";
        if (node[direction]) {
          node = node[direction];
        } else {
          node[direction] = new Node(node, value);
          this._size++;
          return node[direction];
        }
      }
    }
    return null;
  }
  /** Removes the given node, and returns the node that was physically removed from the tree. */ removeNode(node) {
    /**
     * The node to physically remove from the tree.
     * Guaranteed to have at most one child.
     */ const flaggedNode = !node.left || !node.right ? node : node.findSuccessorNode();
    /** Replaces the flagged node. */ const replacementNode = flaggedNode.left ?? flaggedNode.right;
    if (replacementNode) replacementNode.parent = flaggedNode.parent;
    if (!flaggedNode.parent) {
      this.root = replacementNode;
    } else {
      flaggedNode.parent[flaggedNode.directionFromParent()] = replacementNode;
    }
    if (flaggedNode !== node) {
      /** Swaps values, in case value of the removed node is still needed by consumer. */ const swapValue = node.value;
      node.value = flaggedNode.value;
      flaggedNode.value = swapValue;
    }
    this._size--;
    return flaggedNode;
  }
  /**
   * Adds the value to the binary search tree if it does not already exist in it.
   * Returns true if successful.
   */ insert(value) {
    return !!this.insertNode(BinarySearchNode, value);
  }
  /**
   * Removes node value from the binary search tree if found.
   * Returns true if found and removed.
   */ remove(value) {
    const node = this.findNode(value);
    if (node) this.removeNode(node);
    return node !== null;
  }
  /** Returns node value if found in the binary search tree. */ find(value) {
    return this.findNode(value)?.value ?? null;
  }
  /** Returns the minimum value in the binary search tree or null if empty. */ min() {
    return this.root ? this.root.findMinNode().value : null;
  }
  /** Returns the maximum value in the binary search tree or null if empty. */ max() {
    return this.root ? this.root.findMaxNode().value : null;
  }
  /** Removes all values from the binary search tree. */ clear() {
    this.root = null;
    this._size = 0;
  }
  /** Checks if the binary search tree is empty. */ isEmpty() {
    return this.size === 0;
  }
  /**
   * Returns an iterator that uses in-order (LNR) tree traversal for
   * retrieving values from the binary search tree.
   */ *lnrValues() {
    const nodes = [];
    let node = this.root;
    while(nodes.length || node){
      if (node) {
        nodes.push(node);
        node = node.left;
      } else {
        node = nodes.pop();
        yield node.value;
        node = node.right;
      }
    }
  }
  /**
   * Returns an iterator that uses reverse in-order (RNL) tree traversal for
   * retrieving values from the binary search tree.
   */ *rnlValues() {
    const nodes = [];
    let node = this.root;
    while(nodes.length || node){
      if (node) {
        nodes.push(node);
        node = node.right;
      } else {
        node = nodes.pop();
        yield node.value;
        node = node.left;
      }
    }
  }
  /**
   * Returns an iterator that uses pre-order (NLR) tree traversal for
   * retrieving values from the binary search tree.
   */ *nlrValues() {
    const nodes = [];
    if (this.root) nodes.push(this.root);
    while(nodes.length){
      const node = nodes.pop();
      yield node.value;
      if (node.right) nodes.push(node.right);
      if (node.left) nodes.push(node.left);
    }
  }
  /**
   * Returns an iterator that uses post-order (LRN) tree traversal for
   * retrieving values from the binary search tree.
   */ *lrnValues() {
    const nodes = [];
    let node = this.root;
    let lastNodeVisited = null;
    while(nodes.length || node){
      if (node) {
        nodes.push(node);
        node = node.left;
      } else {
        const lastNode = nodes[nodes.length - 1];
        if (lastNode.right && lastNode.right !== lastNodeVisited) {
          node = lastNode.right;
        } else {
          yield lastNode.value;
          lastNodeVisited = nodes.pop();
        }
      }
    }
  }
  /**
   * Returns an iterator that uses level order tree traversal for
   * retrieving values from the binary search tree.
   */ *lvlValues() {
    const children = [];
    let cursor = this.root;
    while(cursor){
      yield cursor.value;
      if (cursor.left) children.push(cursor.left);
      if (cursor.right) children.push(cursor.right);
      cursor = children.shift() ?? null;
    }
  }
  /**
   * Returns an iterator that uses in-order (LNR) tree traversal for
   * retrieving values from the binary search tree.
   */ *[Symbol.iterator]() {
    yield* this.lnrValues();
  }
}
//# sourceMappingURL=data:application/json;base64,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