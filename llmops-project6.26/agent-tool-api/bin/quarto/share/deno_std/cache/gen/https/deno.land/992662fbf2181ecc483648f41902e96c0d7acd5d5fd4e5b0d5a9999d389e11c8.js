// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// Structured similarly to Go's cookie.go
// https://github.com/golang/go/blob/master/src/net/http/cookie.go
// This module is browser compatible.
import { assert } from "../assert/assert.ts";
const FIELD_CONTENT_REGEXP = /^(?=[\x20-\x7E]*$)[^()@<>,;:\\"\[\]?={}\s]+$/;
function toString(cookie) {
  if (!cookie.name) {
    return "";
  }
  const out = [];
  validateName(cookie.name);
  validateValue(cookie.name, cookie.value);
  out.push(`${cookie.name}=${cookie.value}`);
  // Fallback for invalid Set-Cookie
  // ref: https://tools.ietf.org/html/draft-ietf-httpbis-cookie-prefixes-00#section-3.1
  if (cookie.name.startsWith("__Secure")) {
    cookie.secure = true;
  }
  if (cookie.name.startsWith("__Host")) {
    cookie.path = "/";
    cookie.secure = true;
    delete cookie.domain;
  }
  if (cookie.secure) {
    out.push("Secure");
  }
  if (cookie.httpOnly) {
    out.push("HttpOnly");
  }
  if (typeof cookie.maxAge === "number" && Number.isInteger(cookie.maxAge)) {
    assert(cookie.maxAge >= 0, "Max-Age must be an integer superior or equal to 0");
    out.push(`Max-Age=${cookie.maxAge}`);
  }
  if (cookie.domain) {
    validateDomain(cookie.domain);
    out.push(`Domain=${cookie.domain}`);
  }
  if (cookie.sameSite) {
    out.push(`SameSite=${cookie.sameSite}`);
  }
  if (cookie.path) {
    validatePath(cookie.path);
    out.push(`Path=${cookie.path}`);
  }
  if (cookie.expires) {
    const { expires } = cookie;
    const date = typeof expires === "number" ? new Date(expires) : expires;
    out.push(`Expires=${date.toUTCString()}`);
  }
  if (cookie.unparsed) {
    out.push(cookie.unparsed.join("; "));
  }
  return out.join("; ");
}
/**
 * Validate Cookie Name.
 * @param name Cookie name.
 */ function validateName(name) {
  if (name && !FIELD_CONTENT_REGEXP.test(name)) {
    throw new TypeError(`Invalid cookie name: "${name}".`);
  }
}
/**
 * Validate Path Value.
 * See {@link https://tools.ietf.org/html/rfc6265#section-4.1.2.4}.
 * @param path Path value.
 */ function validatePath(path) {
  if (path === null) {
    return;
  }
  for(let i = 0; i < path.length; i++){
    const c = path.charAt(i);
    if (c < String.fromCharCode(0x20) || c > String.fromCharCode(0x7E) || c === ";") {
      throw new Error(path + ": Invalid cookie path char '" + c + "'");
    }
  }
}
/**
 * Validate Cookie Value.
 * See {@link https://tools.ietf.org/html/rfc6265#section-4.1}.
 * @param value Cookie value.
 */ function validateValue(name, value) {
  if (value === null) return;
  for(let i = 0; i < value.length; i++){
    const c = value.charAt(i);
    if (c < String.fromCharCode(0x21) || c === String.fromCharCode(0x22) || c === String.fromCharCode(0x2c) || c === String.fromCharCode(0x3b) || c === String.fromCharCode(0x5c) || c === String.fromCharCode(0x7f)) {
      throw new Error("RFC2616 cookie '" + name + "' cannot contain character '" + c + "'");
    }
    if (c > String.fromCharCode(0x80)) {
      throw new Error("RFC2616 cookie '" + name + "' can only have US-ASCII chars as value" + c.charCodeAt(0).toString(16));
    }
  }
}
/**
 * Validate Cookie Domain.
 * See {@link https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.2.3}.
 * @param domain Cookie domain.
 */ function validateDomain(domain) {
  const char1 = domain.charAt(0);
  const charN = domain.charAt(domain.length - 1);
  if (char1 === "-" || charN === "." || charN === "-") {
    throw new Error("Invalid first/last char in cookie domain: " + domain);
  }
}
/**
 * Parse cookies of a header
 *
 * @example
 * ```ts
 * import { getCookies } from "https://deno.land/std@$STD_VERSION/http/cookie.ts";
 *
 * const headers = new Headers();
 * headers.set("Cookie", "full=of; tasty=chocolate");
 *
 * const cookies = getCookies(headers);
 * console.log(cookies); // { full: "of", tasty: "chocolate" }
 * ```
 *
 * @param headers The headers instance to get cookies from
 * @return Object with cookie names as keys
 */ export function getCookies(headers) {
  const cookie = headers.get("Cookie");
  if (cookie !== null) {
    const out = {};
    const c = cookie.split(";");
    for (const kv of c){
      const [cookieKey, ...cookieVal] = kv.split("=");
      assert(cookieKey !== undefined);
      const key = cookieKey.trim();
      out[key] = cookieVal.join("=");
    }
    return out;
  }
  return {};
}
/**
 * Set the cookie header properly in the headers
 *
 * @example
 * ```ts
 * import {
 *   Cookie,
 *   setCookie,
 * } from "https://deno.land/std@$STD_VERSION/http/cookie.ts";
 *
 * const headers = new Headers();
 * const cookie: Cookie = { name: "Space", value: "Cat" };
 * setCookie(headers, cookie);
 *
 * const cookieHeader = headers.get("set-cookie");
 * console.log(cookieHeader); // Space=Cat
 * ```
 *
 * @param headers The headers instance to set the cookie to
 * @param cookie Cookie to set
 */ export function setCookie(headers, cookie) {
  // Parsing cookie headers to make consistent set-cookie header
  // ref: https://tools.ietf.org/html/rfc6265#section-4.1.1
  const v = toString(cookie);
  if (v) {
    headers.append("Set-Cookie", v);
  }
}
/**
 * Set the cookie header with empty value in the headers to delete it
 *
 * > Note: Deleting a `Cookie` will set its expiration date before now. Forcing
 * > the browser to delete it.
 *
 * @example
 * ```ts
 * import { deleteCookie } from "https://deno.land/std@$STD_VERSION/http/cookie.ts";
 *
 * const headers = new Headers();
 * deleteCookie(headers, "deno");
 *
 * const cookieHeader = headers.get("set-cookie");
 * console.log(cookieHeader); // deno=; Expires=Thus, 01 Jan 1970 00:00:00 GMT
 * ```
 *
 * @param headers The headers instance to delete the cookie from
 * @param name Name of cookie
 * @param attributes Additional cookie attributes
 */ export function deleteCookie(headers, name, attributes) {
  setCookie(headers, {
    name: name,
    value: "",
    expires: new Date(0),
    ...attributes
  });
}
function parseSetCookie(value) {
  const attrs = value.split(";").map((attr)=>{
    const [key, ...values] = attr.trim().split("=");
    return [
      key,
      values.join("=")
    ];
  });
  const cookie = {
    name: attrs[0][0],
    value: attrs[0][1]
  };
  for (const [key, value] of attrs.slice(1)){
    switch(key.toLocaleLowerCase()){
      case "expires":
        cookie.expires = new Date(value);
        break;
      case "max-age":
        cookie.maxAge = Number(value);
        if (cookie.maxAge < 0) {
          console.warn("Max-Age must be an integer superior or equal to 0. Cookie ignored.");
          return null;
        }
        break;
      case "domain":
        cookie.domain = value;
        break;
      case "path":
        cookie.path = value;
        break;
      case "secure":
        cookie.secure = true;
        break;
      case "httponly":
        cookie.httpOnly = true;
        break;
      case "samesite":
        cookie.sameSite = value;
        break;
      default:
        if (!Array.isArray(cookie.unparsed)) {
          cookie.unparsed = [];
        }
        cookie.unparsed.push([
          key,
          value
        ].join("="));
    }
  }
  if (cookie.name.startsWith("__Secure-")) {
    /** This requirement is mentioned in https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie but not the RFC. */ if (!cookie.secure) {
      console.warn("Cookies with names starting with `__Secure-` must be set with the secure flag. Cookie ignored.");
      return null;
    }
  }
  if (cookie.name.startsWith("__Host-")) {
    if (!cookie.secure) {
      console.warn("Cookies with names starting with `__Host-` must be set with the secure flag. Cookie ignored.");
      return null;
    }
    if (cookie.domain !== undefined) {
      console.warn("Cookies with names starting with `__Host-` must not have a domain specified. Cookie ignored.");
      return null;
    }
    if (cookie.path !== "/") {
      console.warn("Cookies with names starting with `__Host-` must have path be `/`. Cookie has been ignored.");
      return null;
    }
  }
  return cookie;
}
/**
 * Parse set-cookies of a header
 *
 * @example
 * ```ts
 * import { getSetCookies } from "https://deno.land/std@$STD_VERSION/http/cookie.ts";
 *
 * const headers = new Headers([
 *   ["Set-Cookie", "lulu=meow; Secure; Max-Age=3600"],
 *   ["Set-Cookie", "booya=kasha; HttpOnly; Path=/"],
 * ]);
 *
 * const cookies = getSetCookies(headers);
 * console.log(cookies); // [{ name: "lulu", value: "meow", secure: true, maxAge: 3600 }, { name: "booya", value: "kahsa", httpOnly: true, path: "/ }]
 * ```
 *
 * @param headers The headers instance to get set-cookies from
 * @return List of cookies
 */ export function getSetCookies(headers) {
  // TODO(lino-levan): remove this ts-ignore when Typescript 5.2 lands in Deno
  // @ts-ignore Typescript's TS Dom types will be out of date until 5.2
  return headers.getSetCookie()/** Parse each `set-cookie` header separately */ .map(parseSetCookie)/** Skip empty cookies */ .filter(Boolean);
}
//# sourceMappingURL=data:application/json;base64,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