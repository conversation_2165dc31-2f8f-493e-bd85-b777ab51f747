// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/** Functions for specific common tasks around collection types like `Array` and
 * `Record`. This module is heavily inspired by `<PERSON><PERSON><PERSON>`s stdlib.
 *
 * - All provided functions are **pure**, which also means that they do **not
 *   mutate** your inputs, **returning a new value** instead.
 * - All functions are importable on their own by referencing their snake_case
 *   named file (e.g. `collections/sort_by.ts`)
 *
 * This module re-exports several modules, and importing this module directly
 * will likely include a lot of code that you might not use.
 *
 * Consider importing the function directly. For example to import
 * {@linkcode groupBy} import the module using the snake cased version of the
 * module:
 *
 * ```ts
 * import { groupBy } from "https://deno.land/std@$STD_VERSION/collections/group_by.ts";
 * ```
 *
 * @module
 */ export * from "./aggregate_groups.ts";
export * from "./associate_by.ts";
export * from "./associate_with.ts";
export * from "./chunk.ts";
export * from "./deep_merge.ts";
export * from "./distinct.ts";
export * from "./distinct_by.ts";
export * from "./drop_while.ts";
export * from "./filter_entries.ts";
export * from "./filter_keys.ts";
export * from "./filter_values.ts";
export * from "./group_by.ts";
export * from "./intersect.ts";
export * from "./map_entries.ts";
export * from "./map_keys.ts";
export * from "./map_not_nullish.ts";
export * from "./map_values.ts";
export * from "./partition.ts";
export * from "./partition_entries.ts";
export * from "./permutations.ts";
export * from "./find_single.ts";
export * from "./sliding_windows.ts";
export * from "./sum_of.ts";
export * from "./max_by.ts";
export * from "./max_of.ts";
export * from "./min_by.ts";
export * from "./min_of.ts";
export * from "./sort_by.ts";
export * from "./union.ts";
export * from "./without_all.ts";
export * from "./unzip.ts";
export * from "./zip.ts";
export * from "./join_to_string.ts";
export * from "./max_with.ts";
export * from "./min_with.ts";
export * from "./includes_value.ts";
export * from "./take_last_while.ts";
export * from "./take_while.ts";
export * from "./first_not_nullish_of.ts";
export * from "./drop_last_while.ts";
export * from "./reduce_groups.ts";
export * from "./sample.ts";
export * from "./running_reduce.ts";
export * from "./binary_heap.ts";
export * from "./binary_search_tree.ts";
export { RedBlackTree } from "./red_black_tree.ts";
//# sourceMappingURL=data:application/json;base64,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