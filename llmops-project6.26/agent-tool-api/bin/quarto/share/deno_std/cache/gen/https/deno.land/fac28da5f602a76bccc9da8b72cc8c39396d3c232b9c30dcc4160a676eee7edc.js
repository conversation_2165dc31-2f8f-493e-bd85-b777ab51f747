// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { cmp } from "./cmp.ts";
/**
 * Test to see if a semantic version falls within the range of the comparator.
 * @param version The version to compare
 * @param comparator The comparator
 * @returns True if the version is within the comparators set otherwise false
 */ export function testComparator(version, comparator) {
  return cmp(version, comparator.operator, comparator.semver);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci90ZXN0X2NvbXBhcmF0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbmltcG9ydCB0eXBlIHsgU2VtVmVyLCBTZW1WZXJDb21wYXJhdG9yIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcbmltcG9ydCB7IGNtcCB9IGZyb20gXCIuL2NtcC50c1wiO1xuXG4vKipcbiAqIFRlc3QgdG8gc2VlIGlmIGEgc2VtYW50aWMgdmVyc2lvbiBmYWxscyB3aXRoaW4gdGhlIHJhbmdlIG9mIHRoZSBjb21wYXJhdG9yLlxuICogQHBhcmFtIHZlcnNpb24gVGhlIHZlcnNpb24gdG8gY29tcGFyZVxuICogQHBhcmFtIGNvbXBhcmF0b3IgVGhlIGNvbXBhcmF0b3JcbiAqIEByZXR1cm5zIFRydWUgaWYgdGhlIHZlcnNpb24gaXMgd2l0aGluIHRoZSBjb21wYXJhdG9ycyBzZXQgb3RoZXJ3aXNlIGZhbHNlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXN0Q29tcGFyYXRvcihcbiAgdmVyc2lvbjogU2VtVmVyLFxuICBjb21wYXJhdG9yOiBTZW1WZXJDb21wYXJhdG9yLFxuKTogYm9vbGVhbiB7XG4gIHJldHVybiBjbXAodmVyc2lvbiwgY29tcGFyYXRvci5vcGVyYXRvciwgY29tcGFyYXRvci5zZW12ZXIpO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUFTLEdBQUcsUUFBUSxXQUFXO0FBRS9COzs7OztDQUtDLEdBQ0QsT0FBTyxTQUFTLGVBQ2QsT0FBZSxFQUNmLFVBQTRCO0VBRTVCLE9BQU8sSUFBSSxTQUFTLFdBQVcsUUFBUSxFQUFFLFdBQVcsTUFBTTtBQUM1RCJ9