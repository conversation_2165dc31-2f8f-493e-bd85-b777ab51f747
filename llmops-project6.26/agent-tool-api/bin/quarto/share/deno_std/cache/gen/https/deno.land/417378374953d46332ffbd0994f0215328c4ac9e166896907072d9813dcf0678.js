// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
export const ERROR_WHILE_MAPPING_MESSAGE = "Threw while mapping.";
/**
 * pooledMap transforms values from an (async) iterable into another async
 * iterable. The transforms are done concurrently, with a max concurrency
 * defined by the poolLimit.
 *
 * If an error is thrown from `iterableFn`, no new transformations will begin.
 * All currently executing transformations are allowed to finish and still
 * yielded on success. After that, the rejections among them are gathered and
 * thrown by the iterator in an `AggregateError`.
 *
 * @example
 * ```typescript
 * import { pooledMap } from "https://deno.land/std@$STD_VERSION/async/pool.ts";
 *
 * const results = pooledMap(
 *   2,
 *   [1, 2, 3],
 *   (i) => new Promise((r) => setTimeout(() => r(i), 1000)),
 * );
 *
 * for await (const value of results) {
 *   // ...
 * }
 * ```
 *
 * @param poolLimit The maximum count of items being processed concurrently.
 * @param array The input array for mapping.
 * @param iteratorFn The function to call for every item of the array.
 */ export function pooledMap(poolLimit, array, iteratorFn) {
  // Create the async iterable that is returned from this function.
  const res = new TransformStream({
    async transform (p, controller) {
      try {
        const s = await p;
        controller.enqueue(s);
      } catch (e) {
        if (e instanceof AggregateError && e.message === ERROR_WHILE_MAPPING_MESSAGE) {
          controller.error(e);
        }
      }
    }
  });
  // Start processing items from the iterator
  (async ()=>{
    const writer = res.writable.getWriter();
    const executing = [];
    try {
      for await (const item of array){
        const p = Promise.resolve().then(()=>iteratorFn(item));
        // Only write on success. If we `writer.write()` a rejected promise,
        // that will end the iteration. We don't want that yet. Instead let it
        // fail the race, taking us to the catch block where all currently
        // executing jobs are allowed to finish and all rejections among them
        // can be reported together.
        writer.write(p);
        const e = p.then(()=>executing.splice(executing.indexOf(e), 1));
        executing.push(e);
        if (executing.length >= poolLimit) {
          await Promise.race(executing);
        }
      }
      // Wait until all ongoing events have processed, then close the writer.
      await Promise.all(executing);
      writer.close();
    } catch  {
      const errors = [];
      for (const result of (await Promise.allSettled(executing))){
        if (result.status === "rejected") {
          errors.push(result.reason);
        }
      }
      writer.write(Promise.reject(new AggregateError(errors, ERROR_WHILE_MAPPING_MESSAGE))).catch(()=>{});
    }
  })();
  // Feature test until browser coverage is adequate
  return Symbol.asyncIterator in res.readable && typeof res.readable[Symbol.asyncIterator] === "function" ? res.readable[Symbol.asyncIterator]() : async function*() {
    const reader = res.readable.getReader();
    while(true){
      const { done, value } = await reader.read();
      if (done) break;
      yield value;
    }
    reader.releaseLock();
  }();
}
//# sourceMappingURL=data:application/json;base64,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