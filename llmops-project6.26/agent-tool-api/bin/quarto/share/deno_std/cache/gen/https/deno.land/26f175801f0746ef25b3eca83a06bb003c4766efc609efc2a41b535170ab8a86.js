// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new array, containing all elements in the given array transformed
 * using the given transformer, except the ones that were transformed to `null`
 * or `undefined`.
 *
 * @example
 * ```ts
 * import { mapNotNullish } from "https://deno.land/std@$STD_VERSION/collections/map_not_nullish.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = [
 *   { middleName: null },
 *   { middleName: "<PERSON>" },
 *   { middleName: undefined },
 *   { middleName: "<PERSON>" },
 * ];
 * const foundMiddleNames = mapNotNullish(people, (it) => it.middleName);
 *
 * assertEquals(foundMiddleNames, ["<PERSON>", "<PERSON>"]);
 * ```
 */ export function mapNotNullish(array, transformer) {
  const ret = [];
  for (const element of array){
    const transformedElement = transformer(element);
    if (transformedElement !== undefined && transformedElement !== null) {
      ret.push(transformedElement);
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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