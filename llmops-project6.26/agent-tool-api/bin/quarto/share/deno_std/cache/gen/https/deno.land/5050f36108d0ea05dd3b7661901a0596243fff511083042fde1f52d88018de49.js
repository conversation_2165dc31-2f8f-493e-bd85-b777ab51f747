// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Builds two separate arrays from the given array of 2-tuples, with the first
 * returned array holding all first tuple elements and the second one holding
 * all the second elements.
 *
 * ```ts
 * import { unzip } from "https://deno.land/std@$STD_VERSION/collections/unzip.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const parents = [
 *   ["<PERSON>", "<PERSON>"],
 *   ["<PERSON>", "<PERSON>"],
 *   ["<PERSON>", "<PERSON>"],
 * ] as [string, string][];
 *
 * const [moms, dads] = unzip(parents);
 *
 * assertEquals(moms, ["<PERSON>", "<PERSON>", "<PERSON>"]);
 * assertEquals(dads, ["<PERSON>", "<PERSON>", "<PERSON>"]);
 * ```
 */ export function unzip(pairs) {
  const { length } = pairs;
  const ret = [
    Array(length),
    Array(length)
  ];
  pairs.forEach(([first, second], index)=>{
    ret[0][index] = first;
    ret[1][index] = second;
  });
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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