// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
export class Tokenizer {
  rules;
  constructor(rules = []){
    this.rules = rules;
  }
  addRule(test, fn) {
    this.rules.push({
      test,
      fn
    });
    return this;
  }
  tokenize(string, receiver = (token)=>token) {
    function* generator(rules) {
      let index = 0;
      for (const rule of rules){
        const result = rule.test(string);
        if (result) {
          const { value, length } = result;
          index += length;
          string = string.slice(length);
          const token = {
            ...rule.fn(value),
            index
          };
          yield receiver(token);
          yield* generator(rules);
        }
      }
    }
    const tokenGenerator = generator(this.rules);
    const tokens = [];
    for (const token of tokenGenerator){
      tokens.push(token);
    }
    if (string.length) {
      throw new Error(`parser error: string not fully parsed! ${string.slice(0, 25)}`);
    }
    return tokens;
  }
}
function digits(value, count = 2) {
  return String(value).padStart(count, "0");
}
function createLiteralTestFunction(value) {
  return (string)=>{
    return string.startsWith(value) ? {
      value,
      length: value.length
    } : undefined;
  };
}
function createMatchTestFunction(match) {
  return (string)=>{
    const result = match.exec(string);
    if (result) return {
      value: result,
      length: result[0].length
    };
  };
}
// according to unicode symbols (http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table)
const defaultRules = [
  {
    test: createLiteralTestFunction("yyyy"),
    fn: ()=>({
        type: "year",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("yy"),
    fn: ()=>({
        type: "year",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("MM"),
    fn: ()=>({
        type: "month",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("M"),
    fn: ()=>({
        type: "month",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("dd"),
    fn: ()=>({
        type: "day",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("d"),
    fn: ()=>({
        type: "day",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("HH"),
    fn: ()=>({
        type: "hour",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("H"),
    fn: ()=>({
        type: "hour",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("hh"),
    fn: ()=>({
        type: "hour",
        value: "2-digit",
        hour12: true
      })
  },
  {
    test: createLiteralTestFunction("h"),
    fn: ()=>({
        type: "hour",
        value: "numeric",
        hour12: true
      })
  },
  {
    test: createLiteralTestFunction("mm"),
    fn: ()=>({
        type: "minute",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("m"),
    fn: ()=>({
        type: "minute",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("ss"),
    fn: ()=>({
        type: "second",
        value: "2-digit"
      })
  },
  {
    test: createLiteralTestFunction("s"),
    fn: ()=>({
        type: "second",
        value: "numeric"
      })
  },
  {
    test: createLiteralTestFunction("SSS"),
    fn: ()=>({
        type: "fractionalSecond",
        value: 3
      })
  },
  {
    test: createLiteralTestFunction("SS"),
    fn: ()=>({
        type: "fractionalSecond",
        value: 2
      })
  },
  {
    test: createLiteralTestFunction("S"),
    fn: ()=>({
        type: "fractionalSecond",
        value: 1
      })
  },
  {
    test: createLiteralTestFunction("a"),
    fn: (value)=>({
        type: "dayPeriod",
        value: value
      })
  },
  // quoted literal
  {
    test: createMatchTestFunction(/^(')(?<value>\\.|[^\']*)\1/),
    fn: (match)=>({
        type: "literal",
        value: match.groups.value
      })
  },
  // literal
  {
    test: createMatchTestFunction(/^.+?\s*/),
    fn: (match)=>({
        type: "literal",
        value: match[0]
      })
  }
];
export class DateTimeFormatter {
  #format;
  constructor(formatString, rules = defaultRules){
    const tokenizer = new Tokenizer(rules);
    this.#format = tokenizer.tokenize(formatString, ({ type, value, hour12 })=>{
      const result = {
        type,
        value
      };
      if (hour12) result.hour12 = hour12;
      return result;
    });
  }
  format(date, options = {}) {
    let string = "";
    const utc = options.timeZone === "UTC";
    for (const token of this.#format){
      const type = token.type;
      switch(type){
        case "year":
          {
            const value = utc ? date.getUTCFullYear() : date.getFullYear();
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2).slice(-2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "month":
          {
            const value = (utc ? date.getUTCMonth() : date.getMonth()) + 1;
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "day":
          {
            const value = utc ? date.getUTCDate() : date.getDate();
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "hour":
          {
            let value = utc ? date.getUTCHours() : date.getHours();
            if (token.hour12) {
              if (value === 0) value = 12;
              else if (value > 12) value -= 12;
            }
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "minute":
          {
            const value = utc ? date.getUTCMinutes() : date.getMinutes();
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "second":
          {
            const value = utc ? date.getUTCSeconds() : date.getSeconds();
            switch(token.value){
              case "numeric":
                {
                  string += value;
                  break;
                }
              case "2-digit":
                {
                  string += digits(value, 2);
                  break;
                }
              default:
                throw Error(`FormatterError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "fractionalSecond":
          {
            const value = utc ? date.getUTCMilliseconds() : date.getMilliseconds();
            string += digits(value, Number(token.value));
            break;
          }
        // FIXME(bartlomieju)
        case "timeZoneName":
          {
            break;
          }
        case "dayPeriod":
          {
            string += token.value ? date.getHours() >= 12 ? "PM" : "AM" : "";
            break;
          }
        case "literal":
          {
            string += token.value;
            break;
          }
        default:
          throw Error(`FormatterError: { ${token.type} ${token.value} }`);
      }
    }
    return string;
  }
  parseToParts(string) {
    const parts = [];
    for (const token of this.#format){
      const type = token.type;
      let value = "";
      switch(token.type){
        case "year":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,4}/.exec(string)?.[0];
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  break;
                }
            }
            break;
          }
        case "month":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{2}/.exec(string)?.[0];
                  break;
                }
              case "narrow":
                {
                  value = /^[a-zA-Z]+/.exec(string)?.[0];
                  break;
                }
              case "short":
                {
                  value = /^[a-zA-Z]+/.exec(string)?.[0];
                  break;
                }
              case "long":
                {
                  value = /^[a-zA-Z]+/.exec(string)?.[0];
                  break;
                }
              default:
                throw Error(`ParserError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "day":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{2}/.exec(string)?.[0];
                  break;
                }
              default:
                throw Error(`ParserError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "hour":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  if (token.hour12 && parseInt(value) > 12) {
                    console.error(`Trying to parse hour greater than 12. Use 'H' instead of 'h'.`);
                  }
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{2}/.exec(string)?.[0];
                  if (token.hour12 && parseInt(value) > 12) {
                    console.error(`Trying to parse hour greater than 12. Use 'HH' instead of 'hh'.`);
                  }
                  break;
                }
              default:
                throw Error(`ParserError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "minute":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{2}/.exec(string)?.[0];
                  break;
                }
              default:
                throw Error(`ParserError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "second":
          {
            switch(token.value){
              case "numeric":
                {
                  value = /^\d{1,2}/.exec(string)?.[0];
                  break;
                }
              case "2-digit":
                {
                  value = /^\d{2}/.exec(string)?.[0];
                  break;
                }
              default:
                throw Error(`ParserError: value "${token.value}" is not supported`);
            }
            break;
          }
        case "fractionalSecond":
          {
            value = new RegExp(`^\\d{${token.value}}`).exec(string)?.[0];
            break;
          }
        case "timeZoneName":
          {
            value = token.value;
            break;
          }
        case "dayPeriod":
          {
            value = /^(A|P)M/.exec(string)?.[0];
            break;
          }
        case "literal":
          {
            if (!string.startsWith(token.value)) {
              throw Error(`Literal "${token.value}" not found "${string.slice(0, 25)}"`);
            }
            value = token.value;
            break;
          }
        default:
          throw Error(`${token.type} ${token.value}`);
      }
      if (!value) {
        throw Error(`value not valid for token { ${type} ${value} } ${string.slice(0, 25)}`);
      }
      parts.push({
        type,
        value
      });
      string = string.slice(value.length);
    }
    if (string.length) {
      throw Error(`datetime string was not fully parsed! ${string.slice(0, 25)}`);
    }
    return parts;
  }
  /** sort & filter dateTimeFormatPart */ sortDateTimeFormatPart(parts) {
    let result = [];
    const typeArray = [
      "year",
      "month",
      "day",
      "hour",
      "minute",
      "second",
      "fractionalSecond"
    ];
    for (const type of typeArray){
      const current = parts.findIndex((el)=>el.type === type);
      if (current !== -1) {
        result = result.concat(parts.splice(current, 1));
      }
    }
    result = result.concat(parts);
    return result;
  }
  partsToDate(parts) {
    const date = new Date();
    const utc = parts.find((part)=>part.type === "timeZoneName" && part.value === "UTC");
    const dayPart = parts.find((part)=>part.type === "day");
    utc ? date.setUTCHours(0, 0, 0, 0) : date.setHours(0, 0, 0, 0);
    for (const part of parts){
      switch(part.type){
        case "year":
          {
            const value = Number(part.value.padStart(4, "20"));
            utc ? date.setUTCFullYear(value) : date.setFullYear(value);
            break;
          }
        case "month":
          {
            const value = Number(part.value) - 1;
            if (dayPart) {
              utc ? date.setUTCMonth(value, Number(dayPart.value)) : date.setMonth(value, Number(dayPart.value));
            } else {
              utc ? date.setUTCMonth(value) : date.setMonth(value);
            }
            break;
          }
        case "day":
          {
            const value = Number(part.value);
            utc ? date.setUTCDate(value) : date.setDate(value);
            break;
          }
        case "hour":
          {
            let value = Number(part.value);
            const dayPeriod = parts.find((part)=>part.type === "dayPeriod");
            if (dayPeriod?.value === "PM") value += 12;
            utc ? date.setUTCHours(value) : date.setHours(value);
            break;
          }
        case "minute":
          {
            const value = Number(part.value);
            utc ? date.setUTCMinutes(value) : date.setMinutes(value);
            break;
          }
        case "second":
          {
            const value = Number(part.value);
            utc ? date.setUTCSeconds(value) : date.setSeconds(value);
            break;
          }
        case "fractionalSecond":
          {
            const value = Number(part.value);
            utc ? date.setUTCMilliseconds(value) : date.setMilliseconds(value);
            break;
          }
      }
    }
    return date;
  }
  parse(string) {
    const parts = this.parseToParts(string);
    const sortParts = this.sortDateTimeFormatPart(parts);
    return this.partsToDate(sortParts);
  }
}
//# sourceMappingURL=data:application/json;base64,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