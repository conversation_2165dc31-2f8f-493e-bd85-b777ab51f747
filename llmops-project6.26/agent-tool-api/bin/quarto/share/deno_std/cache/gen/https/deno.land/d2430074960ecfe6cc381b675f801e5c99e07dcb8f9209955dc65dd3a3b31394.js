// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a tuple of two arrays with the first one containing all elements in
 * the given array that match the given predicate and the second one containing
 * all that do not.
 *
 * @example
 * ```ts
 * import { partition } from "https://deno.land/std@$STD_VERSION/collections/partition.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [5, 6, 7, 8, 9];
 * const [even, odd] = partition(numbers, (it) => it % 2 === 0);
 *
 * assertEquals(even, [6, 8]);
 * assertEquals(odd, [5, 7, 9]);
 * ```
 */ export function partition(array, predicate) {
  const matches = [];
  const rest = [];
  for (const element of array){
    if (predicate(element)) {
      matches.push(element);
    } else {
      rest.push(element);
    }
  }
  return [
    matches,
    rest
  ];
}
//# sourceMappingURL=data:application/json;base64,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