// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * {@linkcode parse} and {@linkcode stringify} for handling
 * [TOML](https://toml.io/en/latest) encoded data. Be sure to read the supported
 * types as not every spec is supported at the moment and the handling in
 * TypeScript side is a bit different.
 *
 * ## Supported types and handling
 *
 * - :heavy_check_mark: [Keys](https://toml.io/en/latest#keys)
 * - :exclamation: [String](https://toml.io/en/latest#string)
 * - :heavy_check_mark: [Multiline String](https://toml.io/en/latest#string)
 * - :heavy_check_mark: [Literal String](https://toml.io/en/latest#string)
 * - :exclamation: [Integer](https://toml.io/en/latest#integer)
 * - :heavy_check_mark: [Float](https://toml.io/en/latest#float)
 * - :heavy_check_mark: [Boolean](https://toml.io/en/latest#boolean)
 * - :heavy_check_mark:
 *   [Offset Date-time](https://toml.io/en/latest#offset-date-time)
 * - :heavy_check_mark:
 *   [Local Date-time](https://toml.io/en/latest#local-date-time)
 * - :heavy_check_mark: [Local Date](https://toml.io/en/latest#local-date)
 * - :exclamation: [Local Time](https://toml.io/en/latest#local-time)
 * - :heavy_check_mark: [Table](https://toml.io/en/latest#table)
 * - :heavy_check_mark: [Inline Table](https://toml.io/en/latest#inline-table)
 * - :exclamation: [Array of Tables](https://toml.io/en/latest#array-of-tables)
 *
 * :exclamation: _Supported with warnings see [Warning](#Warning)._
 *
 * ### :warning: Warning
 *
 * #### String
 *
 * - Regex : Due to the spec, there is no flag to detect regex properly in a TOML
 *   declaration. So the regex is stored as string.
 *
 * #### Integer
 *
 * For **Binary** / **Octal** / **Hexadecimal** numbers, they are stored as string
 * to be not interpreted as Decimal.
 *
 * #### Local Time
 *
 * Because local time does not exist in JavaScript, the local time is stored as a
 * string.
 *
 * #### Inline Table
 *
 * Inline tables are supported. See below:
 *
 * ```toml
 * animal = { type = { name = "pug" } }
 * ## Output { animal: { type: { name: "pug" } } }
 * animal = { type.name = "pug" }
 * ## Output { animal: { type : { name : "pug" } }
 * animal.as.leaders = "tosin"
 * ## Output { animal: { as: { leaders: "tosin" } } }
 * "tosin.abasi" = "guitarist"
 * ## Output { tosin.abasi: "guitarist" }
 * ```
 *
 * #### Array of Tables
 *
 * At the moment only simple declarations like below are supported:
 *
 * ```toml
 * [[bin]]
 * name = "deno"
 * path = "cli/main.rs"
 *
 * [[bin]]
 * name = "deno_core"
 * path = "src/foo.rs"
 *
 * [[nib]]
 * name = "node"
 * path = "not_found"
 * ```
 *
 * will output:
 *
 * ```json
 * {
 *   "bin": [
 *     { "name": "deno", "path": "cli/main.rs" },
 *     { "name": "deno_core", "path": "src/foo.rs" }
 *   ],
 *   "nib": [{ "name": "node", "path": "not_found" }]
 * }
 * ```
 *
 * This module is browser compatible.
 *
 * @example
 * ```ts
 * import {
 *   parse,
 *   stringify,
 * } from "https://deno.land/std@$STD_VERSION/toml/mod.ts";
 * const obj = {
 *   bin: [
 *     { name: "deno", path: "cli/main.rs" },
 *     { name: "deno_core", path: "src/foo.rs" },
 *   ],
 *   nib: [{ name: "node", path: "not_found" }],
 * };
 * const tomlString = stringify(obj);
 * console.log(tomlString);
 *
 * // =>
 * // [[bin]]
 * // name = "deno"
 * // path = "cli/main.rs"
 *
 * // [[bin]]
 * // name = "deno_core"
 * // path = "src/foo.rs"
 *
 * // [[nib]]
 * // name = "node"
 * // path = "not_found"
 *
 * const tomlObject = parse(tomlString);
 * console.log(tomlObject);
 *
 * // =>
 * // {
 * //   bin: [
 * //     { name: "deno", path: "cli/main.rs" },
 * //     { name: "deno_core", path: "src/foo.rs" }
 * //   ],
 * //   nib: [ { name: "node", path: "not_found" } ]
 * // }
 * ```
 *
 * @module
 */ export * from "./stringify.ts";
export * from "./parse.ts";
//# sourceMappingURL=data:application/json;base64,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