// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new record with all entries of the given record except the ones
 * that do not match the given predicate.
 *
 * @example
 * ```ts
 * import { filterEntries } from "https://deno.land/std@$STD_VERSION/collections/filter_entries.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const menu = {
 *   "Salad": 11,
 *   "Soup": 8,
 *   "Pasta": 13,
 * } as const;
 * const myOptions = filterEntries(
 *   menu,
 *   ([item, price]) => item !== "Pasta" && price < 10,
 * );
 *
 * assertEquals(
 *   myOptions,
 *   {
 *     "Soup": 8,
 *   },
 * );
 * ```
 */ export function filterEntries(record, predicate) {
  const ret = {};
  const entries = Object.entries(record);
  for (const [key, value] of entries){
    if (predicate([
      key,
      value
    ])) {
      ret[key] = value;
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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