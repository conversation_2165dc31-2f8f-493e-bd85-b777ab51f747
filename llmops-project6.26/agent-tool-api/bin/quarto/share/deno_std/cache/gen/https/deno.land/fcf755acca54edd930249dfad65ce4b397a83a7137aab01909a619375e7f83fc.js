// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { INVALID } from "./constants.ts";
import { sort } from "./sort.ts";
import { testRange } from "./test_range.ts";
/**
 * The maximum valid SemVer for a given range or INVALID
 * @param range The range to calculate the max for
 * @returns A valid SemVer or INVALID
 */ export function rangeMax(range) {
  // For and's, you take the smallest max
  // For or's, you take the biggest max
  //[ [1 and 2] or [2 and 3] ] = [ 1 or 2 ] = 2
  return sort(range.ranges.map((r)=>sort(r.filter((c)=>testRange(c.max, range)).map((c)=>c.max)).shift())).filter((v)=>v).pop() ?? INVALID;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9yYW5nZV9tYXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbmltcG9ydCB7IElOVkFMSUQgfSBmcm9tIFwiLi9jb25zdGFudHMudHNcIjtcbmltcG9ydCB7IHNvcnQgfSBmcm9tIFwiLi9zb3J0LnRzXCI7XG5pbXBvcnQgdHlwZSB7IFNlbVZlciwgU2VtVmVyUmFuZ2UgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgdGVzdFJhbmdlIH0gZnJvbSBcIi4vdGVzdF9yYW5nZS50c1wiO1xuXG4vKipcbiAqIFRoZSBtYXhpbXVtIHZhbGlkIFNlbVZlciBmb3IgYSBnaXZlbiByYW5nZSBvciBJTlZBTElEXG4gKiBAcGFyYW0gcmFuZ2UgVGhlIHJhbmdlIHRvIGNhbGN1bGF0ZSB0aGUgbWF4IGZvclxuICogQHJldHVybnMgQSB2YWxpZCBTZW1WZXIgb3IgSU5WQUxJRFxuICovXG5leHBvcnQgZnVuY3Rpb24gcmFuZ2VNYXgocmFuZ2U6IFNlbVZlclJhbmdlKTogU2VtVmVyIHwgdW5kZWZpbmVkIHtcbiAgLy8gRm9yIGFuZCdzLCB5b3UgdGFrZSB0aGUgc21hbGxlc3QgbWF4XG4gIC8vIEZvciBvcidzLCB5b3UgdGFrZSB0aGUgYmlnZ2VzdCBtYXhcbiAgLy9bIFsxIGFuZCAyXSBvciBbMiBhbmQgM10gXSA9IFsgMSBvciAyIF0gPSAyXG4gIHJldHVybiBzb3J0KFxuICAgIHJhbmdlLnJhbmdlcy5tYXAoKHIpID0+XG4gICAgICBzb3J0KHIuZmlsdGVyKChjKSA9PiB0ZXN0UmFuZ2UoYy5tYXgsIHJhbmdlKSkubWFwKChjKSA9PiBjLm1heCkpLnNoaWZ0KCkhXG4gICAgKSxcbiAgKS5maWx0ZXIoKHYpID0+IHYpLnBvcCgpID8/IElOVkFMSUQ7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLFNBQVMsT0FBTyxRQUFRLGlCQUFpQjtBQUN6QyxTQUFTLElBQUksUUFBUSxZQUFZO0FBRWpDLFNBQVMsU0FBUyxRQUFRLGtCQUFrQjtBQUU1Qzs7OztDQUlDLEdBQ0QsT0FBTyxTQUFTLFNBQVMsS0FBa0I7RUFDekMsdUNBQXVDO0VBQ3ZDLHFDQUFxQztFQUNyQyw2Q0FBNkM7RUFDN0MsT0FBTyxLQUNMLE1BQU0sTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQ2hCLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyxJQUFNLFVBQVUsRUFBRSxHQUFHLEVBQUUsUUFBUSxHQUFHLENBQUMsQ0FBQyxJQUFNLEVBQUUsR0FBRyxHQUFHLEtBQUssS0FFeEUsTUFBTSxDQUFDLENBQUMsSUFBTSxHQUFHLEdBQUcsTUFBTTtBQUM5QiJ9