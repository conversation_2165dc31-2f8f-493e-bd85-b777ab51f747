// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { sort } from "./sort.ts";
import { testRange } from "./test_range.ts";
/**
 * Returns the highest version in the list that satisfies the range, or `undefined`
 * if none of them do.
 * @param versions The versions to check.
 * @param range The range of possible versions to compare to.
 * @returns The highest version in versions that satisfies the range.
 */ export function maxSatisfying(versions, range) {
  const satisfying = versions.filter((v)=>testRange(v, range));
  const sorted = sort(satisfying);
  return sorted.pop();
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9tYXhfc2F0aXNmeWluZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHR5cGUgeyBTZW1WZXIsIFNlbVZlclJhbmdlIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcbmltcG9ydCB7IHNvcnQgfSBmcm9tIFwiLi9zb3J0LnRzXCI7XG5pbXBvcnQgeyB0ZXN0UmFuZ2UgfSBmcm9tIFwiLi90ZXN0X3JhbmdlLnRzXCI7XG5cbi8qKlxuICogUmV0dXJucyB0aGUgaGlnaGVzdCB2ZXJzaW9uIGluIHRoZSBsaXN0IHRoYXQgc2F0aXNmaWVzIHRoZSByYW5nZSwgb3IgYHVuZGVmaW5lZGBcbiAqIGlmIG5vbmUgb2YgdGhlbSBkby5cbiAqIEBwYXJhbSB2ZXJzaW9ucyBUaGUgdmVyc2lvbnMgdG8gY2hlY2suXG4gKiBAcGFyYW0gcmFuZ2UgVGhlIHJhbmdlIG9mIHBvc3NpYmxlIHZlcnNpb25zIHRvIGNvbXBhcmUgdG8uXG4gKiBAcmV0dXJucyBUaGUgaGlnaGVzdCB2ZXJzaW9uIGluIHZlcnNpb25zIHRoYXQgc2F0aXNmaWVzIHRoZSByYW5nZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1heFNhdGlzZnlpbmcoXG4gIHZlcnNpb25zOiBTZW1WZXJbXSxcbiAgcmFuZ2U6IFNlbVZlclJhbmdlLFxuKTogU2VtVmVyIHwgdW5kZWZpbmVkIHtcbiAgY29uc3Qgc2F0aXNmeWluZyA9IHZlcnNpb25zLmZpbHRlcigodikgPT4gdGVzdFJhbmdlKHYsIHJhbmdlKSk7XG4gIGNvbnN0IHNvcnRlZCA9IHNvcnQoc2F0aXNmeWluZyk7XG4gIHJldHVybiBzb3J0ZWQucG9wKCk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBRTFFLFNBQVMsSUFBSSxRQUFRLFlBQVk7QUFDakMsU0FBUyxTQUFTLFFBQVEsa0JBQWtCO0FBRTVDOzs7Ozs7Q0FNQyxHQUNELE9BQU8sU0FBUyxjQUNkLFFBQWtCLEVBQ2xCLEtBQWtCO0VBRWxCLE1BQU0sYUFBYSxTQUFTLE1BQU0sQ0FBQyxDQUFDLElBQU0sVUFBVSxHQUFHO0VBQ3ZELE1BQU0sU0FBUyxLQUFLO0VBQ3BCLE9BQU8sT0FBTyxHQUFHO0FBQ25CIn0=