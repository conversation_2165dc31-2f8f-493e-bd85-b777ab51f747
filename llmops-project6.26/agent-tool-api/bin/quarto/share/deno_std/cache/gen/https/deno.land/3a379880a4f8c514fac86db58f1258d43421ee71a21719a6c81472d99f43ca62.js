// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { ALL } from "./constants.ts";
import { CARET, HYPHENRANGE, re, STAR, TILDE, XRANGE } from "./_shared.ts";
import { parseComparator } from "./parse_comparator.ts";
// ~, ~> --> * (any, kinda silly)
// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0
// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0
// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0
// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0
// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0
function replaceTildes(comp) {
  return comp.trim().split(/\s+/).map((comp)=>replaceTilde(comp)).join(" ");
}
function replaceTilde(comp) {
  const r = re[TILDE];
  return comp.replace(r, (_, M, m, p, pr)=>{
    let ret;
    if (isX(M)) {
      ret = "";
    } else if (isX(m)) {
      ret = ">=" + M + ".0.0 <" + (+M + 1) + ".0.0";
    } else if (isX(p)) {
      // ~1.2 == >=1.2.0 <1.3.0
      ret = ">=" + M + "." + m + ".0 <" + M + "." + (+m + 1) + ".0";
    } else if (pr) {
      ret = ">=" + M + "." + m + "." + p + "-" + pr + " <" + M + "." + (+m + 1) + ".0";
    } else {
      // ~1.2.3 == >=1.2.3 <1.3.0
      ret = ">=" + M + "." + m + "." + p + " <" + M + "." + (+m + 1) + ".0";
    }
    return ret;
  });
}
// ^ --> * (any, kinda silly)
// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0
// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0
// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0
// ^1.2.3 --> >=1.2.3 <2.0.0
// ^1.2.0 --> >=1.2.0 <2.0.0
function replaceCarets(comp) {
  return comp.trim().split(/\s+/).map((comp)=>replaceCaret(comp)).join(" ");
}
function replaceCaret(comp) {
  const r = re[CARET];
  return comp.replace(r, (_, M, m, p, pr)=>{
    let ret;
    if (isX(M)) {
      ret = "";
    } else if (isX(m)) {
      ret = ">=" + M + ".0.0 <" + (+M + 1) + ".0.0";
    } else if (isX(p)) {
      if (M === "0") {
        ret = ">=" + M + "." + m + ".0 <" + M + "." + (+m + 1) + ".0";
      } else {
        ret = ">=" + M + "." + m + ".0 <" + (+M + 1) + ".0.0";
      }
    } else if (pr) {
      if (M === "0") {
        if (m === "0") {
          ret = ">=" + M + "." + m + "." + p + "-" + pr + " <" + M + "." + m + "." + (+p + 1);
        } else {
          ret = ">=" + M + "." + m + "." + p + "-" + pr + " <" + M + "." + (+m + 1) + ".0";
        }
      } else {
        ret = ">=" + M + "." + m + "." + p + "-" + pr + " <" + (+M + 1) + ".0.0";
      }
    } else {
      if (M === "0") {
        if (m === "0") {
          ret = ">=" + M + "." + m + "." + p + " <" + M + "." + m + "." + (+p + 1);
        } else {
          ret = ">=" + M + "." + m + "." + p + " <" + M + "." + (+m + 1) + ".0";
        }
      } else {
        ret = ">=" + M + "." + m + "." + p + " <" + (+M + 1) + ".0.0";
      }
    }
    return ret;
  });
}
function replaceXRanges(comp) {
  return comp.split(/\s+/).map((comp)=>replaceXRange(comp)).join(" ");
}
function replaceXRange(comp) {
  comp = comp.trim();
  const r = re[XRANGE];
  return comp.replace(r, (ret, gtlt, M, m, p, _pr)=>{
    const xM = isX(M);
    const xm = xM || isX(m);
    const xp = xm || isX(p);
    const anyX = xp;
    if (gtlt === "=" && anyX) {
      gtlt = "";
    }
    if (xM) {
      if (gtlt === ">" || gtlt === "<") {
        // nothing is allowed
        ret = "<0.0.0";
      } else {
        // nothing is forbidden
        ret = "*";
      }
    } else if (gtlt && anyX) {
      // we know patch is an x, because we have any x at all.
      // replace X with 0
      if (xm) {
        m = 0;
      }
      p = 0;
      if (gtlt === ">") {
        // >1 => >=2.0.0
        // >1.2 => >=1.3.0
        // >1.2.3 => >= 1.2.4
        gtlt = ">=";
        if (xm) {
          M = +M + 1;
          m = 0;
          p = 0;
        } else {
          m = +m + 1;
          p = 0;
        }
      } else if (gtlt === "<=") {
        // <=0.7.x is actually <0.8.0, since any 0.7.x should
        // pass.  Similarly, <=7.x is actually <8.0.0, etc.
        gtlt = "<";
        if (xm) {
          M = +M + 1;
        } else {
          m = +m + 1;
        }
      }
      ret = gtlt + M + "." + m + "." + p;
    } else if (xm) {
      ret = ">=" + M + ".0.0 <" + (+M + 1) + ".0.0";
    } else if (xp) {
      ret = ">=" + M + "." + m + ".0 <" + M + "." + (+m + 1) + ".0";
    }
    return ret;
  });
}
// Because * is AND-ed with everything else in the comparator,
// and '' means "any version", just remove the *s entirely.
function replaceStars(comp) {
  return comp.trim().replace(re[STAR], "");
}
// This function is passed to string.replace(re[HYPHENRANGE])
// M, m, patch, prerelease, build
// 1.2 - 3.4.5 -> >=1.2.0 <=3.4.5
// 1.2.3 - 3.4 -> >=1.2.0 <3.5.0 Any 3.4.x will do
// 1.2 - 3.4 -> >=1.2.0 <3.5.0
function hyphenReplace(_$0, from, fM, fm, fp, _fpr, _fb, to, tM, tm, tp, tpr, _tb) {
  if (isX(fM)) {
    from = "";
  } else if (isX(fm)) {
    from = ">=" + fM + ".0.0";
  } else if (isX(fp)) {
    from = ">=" + fM + "." + fm + ".0";
  } else {
    from = ">=" + from;
  }
  if (isX(tM)) {
    to = "";
  } else if (isX(tm)) {
    to = "<" + (+tM + 1) + ".0.0";
  } else if (isX(tp)) {
    to = "<" + tM + "." + (+tm + 1) + ".0";
  } else if (tpr) {
    to = "<=" + tM + "." + tm + "." + tp + "-" + tpr;
  } else {
    to = "<=" + to;
  }
  return (from + " " + to).trim();
}
function isX(id) {
  return !id || id.toLowerCase() === "x" || id === "*";
}
/**
 * Parses a range string into a SemVerRange object or throws a TypeError.
 * @param range The range string
 * @returns A valid semantic version range
 */ export function parseRange(range) {
  // handle spaces around and between comparator and version
  range = range.trim().replaceAll(/(?<=<|>|=) /g, "");
  if (range === "") {
    return {
      ranges: [
        [
          ALL
        ]
      ]
    };
  }
  // Split into groups of comparators, these are considered OR'd together.
  const ranges = range.trim().split(/\s*\|\|\s*/).map((range)=>{
    // convert `1.2.3 - 1.2.4` into `>=1.2.3 <=1.2.4`
    const hr = re[HYPHENRANGE];
    range = range.replace(hr, hyphenReplace);
    range = replaceCarets(range);
    range = replaceTildes(range);
    range = replaceXRanges(range);
    range = replaceStars(range);
    // At this point, the range is completely trimmed and
    // ready to be split into comparators.
    // These are considered AND's
    if (range === "") {
      return [
        ALL
      ];
    } else {
      return range.split(" ").filter((r)=>r).map((r)=>parseComparator(r));
    }
  });
  return {
    ranges
  };
}
//# sourceMappingURL=data:application/json;base64,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