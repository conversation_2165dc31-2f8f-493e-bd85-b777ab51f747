// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { parse } from "./parse.ts";
import { COMPARATOR, re } from "./_shared.ts";
import { comparatorMax } from "./comparator_max.ts";
import { comparatorMin } from "./comparator_min.ts";
import { ANY, NONE } from "./constants.ts";
/**
 * Parses a comparator string into a valid SemVerComparator.
 * @param comparator
 * @returns A valid SemVerComparator
 */ export function parseComparator(comparator) {
  const r = re[COMPARATOR];
  const m = comparator.match(r);
  if (!m) {
    return NONE;
  }
  const operator = m[1] ?? "";
  const semver = m[2] ? parse(m[2]) : ANY;
  const min = comparatorMin(semver, operator);
  const max = comparatorMax(semver, operator);
  return {
    operator,
    semver,
    min,
    max
  };
}
//# sourceMappingURL=data:application/json;base64,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