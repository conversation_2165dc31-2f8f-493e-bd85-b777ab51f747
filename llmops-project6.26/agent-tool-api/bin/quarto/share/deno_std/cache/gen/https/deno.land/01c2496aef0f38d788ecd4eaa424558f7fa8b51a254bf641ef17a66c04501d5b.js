// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { convertRowToObject, ERR_BARE_QUOTE, ERR_FIELD_COUNT, ERR_INVALID_DELIM, ERR_QUOTE, ParseError } from "./_io.ts";
import { assert } from "../assert/assert.ts";
export { /** @deprecated (will be removed in 0.205.0) */ ERR_BARE_QUOTE, /** @deprecated (will be removed in 0.205.0) */ ERR_FIELD_COUNT, /** @deprecated (will be removed in 0.205.0) */ ERR_INVALID_DELIM, /** @deprecated (will be removed in 0.205.0) */ ERR_QUOTE, ParseError };
const BYTE_ORDER_MARK = "\ufeff";
class Parser {
  #input = "";
  #cursor = 0;
  #options;
  constructor({ separator = ",", trimLeadingSpace = false, comment, lazyQuotes, fieldsPerRecord } = {}){
    this.#options = {
      separator,
      trimLeadingSpace,
      comment,
      lazyQuotes,
      fieldsPerRecord
    };
  }
  #readLine() {
    if (this.#isEOF()) return null;
    if (!this.#input.startsWith("\r\n", this.#cursor) || !this.#input.startsWith("\n", this.#cursor)) {
      let buffer = "";
      let hadNewline = false;
      while(this.#cursor < this.#input.length){
        if (this.#input.startsWith("\r\n", this.#cursor)) {
          hadNewline = true;
          this.#cursor += 2;
          break;
        }
        if (this.#input.startsWith("\n", this.#cursor)) {
          hadNewline = true;
          this.#cursor += 1;
          break;
        }
        buffer += this.#input[this.#cursor];
        this.#cursor += 1;
      }
      if (!hadNewline && buffer.endsWith("\r")) {
        buffer = buffer.slice(0, -1);
      }
      return buffer;
    }
    return null;
  }
  #isEOF() {
    return this.#cursor >= this.#input.length;
  }
  #parseRecord(startLine) {
    let line = this.#readLine();
    if (line === null) return null;
    if (line.length === 0) {
      return [];
    }
    function runeCount(s) {
      // Array.from considers the surrogate pair.
      return Array.from(s).length;
    }
    let lineIndex = startLine + 1;
    // line starting with comment character is ignored
    if (this.#options.comment && line[0] === this.#options.comment) {
      return [];
    }
    let fullLine = line;
    let quoteError = null;
    const quote = '"';
    const quoteLen = quote.length;
    const separatorLen = this.#options.separator.length;
    let recordBuffer = "";
    const fieldIndexes = [];
    parseField: for(;;){
      if (this.#options.trimLeadingSpace) {
        line = line.trimStart();
      }
      if (line.length === 0 || !line.startsWith(quote)) {
        // Non-quoted string field
        const i = line.indexOf(this.#options.separator);
        let field = line;
        if (i >= 0) {
          field = field.substring(0, i);
        }
        // Check to make sure a quote does not appear in field.
        if (!this.#options.lazyQuotes) {
          const j = field.indexOf(quote);
          if (j >= 0) {
            const col = runeCount(fullLine.slice(0, fullLine.length - line.slice(j).length));
            quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_BARE_QUOTE);
            break parseField;
          }
        }
        recordBuffer += field;
        fieldIndexes.push(recordBuffer.length);
        if (i >= 0) {
          line = line.substring(i + separatorLen);
          continue parseField;
        }
        break parseField;
      } else {
        // Quoted string field
        line = line.substring(quoteLen);
        for(;;){
          const i = line.indexOf(quote);
          if (i >= 0) {
            // Hit next quote.
            recordBuffer += line.substring(0, i);
            line = line.substring(i + quoteLen);
            if (line.startsWith(quote)) {
              // `""` sequence (append quote).
              recordBuffer += quote;
              line = line.substring(quoteLen);
            } else if (line.startsWith(this.#options.separator)) {
              // `","` sequence (end of field).
              line = line.substring(separatorLen);
              fieldIndexes.push(recordBuffer.length);
              continue parseField;
            } else if (0 === line.length) {
              // `"\n` sequence (end of line).
              fieldIndexes.push(recordBuffer.length);
              break parseField;
            } else if (this.#options.lazyQuotes) {
              // `"` sequence (bare quote).
              recordBuffer += quote;
            } else {
              // `"*` sequence (invalid non-escaped quote).
              const col = runeCount(fullLine.slice(0, fullLine.length - line.length - quoteLen));
              quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
              break parseField;
            }
          } else if (line.length > 0 || !this.#isEOF()) {
            // Hit end of line (copy all data so far).
            recordBuffer += line;
            const r = this.#readLine();
            lineIndex++;
            line = r ?? ""; // This is a workaround for making this module behave similarly to the encoding/csv/reader.go.
            fullLine = line;
            if (r === null) {
              // Abrupt end of file (EOF or error).
              if (!this.#options.lazyQuotes) {
                const col = runeCount(fullLine);
                quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
                break parseField;
              }
              fieldIndexes.push(recordBuffer.length);
              break parseField;
            }
            recordBuffer += "\n"; // preserve line feed (This is because TextProtoReader removes it.)
          } else {
            // Abrupt end of file (EOF on error).
            if (!this.#options.lazyQuotes) {
              const col = runeCount(fullLine);
              quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
              break parseField;
            }
            fieldIndexes.push(recordBuffer.length);
            break parseField;
          }
        }
      }
    }
    if (quoteError) {
      throw quoteError;
    }
    const result = [];
    let preIdx = 0;
    for (const i of fieldIndexes){
      result.push(recordBuffer.slice(preIdx, i));
      preIdx = i;
    }
    return result;
  }
  parse(input) {
    this.#input = input.startsWith(BYTE_ORDER_MARK) ? input.slice(1) : input;
    this.#cursor = 0;
    const result = [];
    let _nbFields;
    let lineResult;
    let first = true;
    let lineIndex = 0;
    const INVALID_RUNE = [
      "\r",
      "\n",
      '"'
    ];
    const options = this.#options;
    if (INVALID_RUNE.includes(options.separator) || typeof options.comment === "string" && INVALID_RUNE.includes(options.comment) || options.separator === options.comment) {
      throw new Error(ERR_INVALID_DELIM);
    }
    for(;;){
      const r = this.#parseRecord(lineIndex);
      if (r === null) break;
      lineResult = r;
      lineIndex++;
      // If fieldsPerRecord is 0, Read sets it to
      // the number of fields in the first record
      if (first) {
        first = false;
        if (options.fieldsPerRecord !== undefined) {
          if (options.fieldsPerRecord === 0) {
            _nbFields = lineResult.length;
          } else {
            _nbFields = options.fieldsPerRecord;
          }
        }
      }
      if (lineResult.length > 0) {
        if (_nbFields && _nbFields !== lineResult.length) {
          throw new ParseError(lineIndex, lineIndex, null, ERR_FIELD_COUNT);
        }
        result.push(lineResult);
      }
    }
    return result;
  }
}
export function parse(input, opt = {
  skipFirstRow: false
}) {
  const parser = new Parser(opt);
  const r = parser.parse(input);
  if (opt.skipFirstRow || opt.columns) {
    let headers = [];
    if (opt.skipFirstRow) {
      const head = r.shift();
      assert(head !== undefined);
      headers = head;
    }
    if (opt.columns) {
      headers = opt.columns;
    }
    const firstLineIndex = opt.skipFirstRow ? 1 : 0;
    return r.map((row, i)=>{
      return convertRowToObject(row, headers, firstLineIndex + i);
    });
  }
  return r;
}
//# sourceMappingURL=data:application/json;base64,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