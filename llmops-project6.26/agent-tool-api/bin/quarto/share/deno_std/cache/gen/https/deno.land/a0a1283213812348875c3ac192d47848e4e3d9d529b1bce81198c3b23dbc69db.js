// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/** {@linkcode parse} function for parsing
 * [JSONC](https://code.visualstudio.com/docs/languages/json#_json-with-comments)
 * (JSON with Comments) strings.
 *
 * This module is browser compatible.
 *
 * @module
 */ import { assert } from "../assert/assert.ts";
/**
 * Converts a JSON with Comments (JSONC) string into an object.
 * If a syntax error is found, throw a SyntaxError.
 *
 * @example
 *
 * ```ts
 * import * as JSO<PERSON> from "https://deno.land/std@$STD_VERSION/jsonc/mod.ts";
 *
 * console.log(JSONC.parse('{"foo": "bar", } // comment')); //=> { foo: "bar" }
 * console.log(JSONC.parse('{"foo": "bar", } /* comment *\/')); //=> { foo: "bar" }
 * console.log(JSONC.parse('{"foo": "bar" } // comment', {
 *   allowTrailingComma: false,
 * })); //=> { foo: "bar" }
 * ```
 *
 * @param text A valid JSONC string.
 */ export function parse(text, { allowTrailingComma = true } = {}) {
  if (new.target) {
    throw new TypeError("parse is not a constructor");
  }
  return new JSONCParser(text, {
    allowTrailingComma
  }).parse();
}
var TokenType;
(function(TokenType) {
  TokenType[TokenType["BeginObject"] = 0] = "BeginObject";
  TokenType[TokenType["EndObject"] = 1] = "EndObject";
  TokenType[TokenType["BeginArray"] = 2] = "BeginArray";
  TokenType[TokenType["EndArray"] = 3] = "EndArray";
  TokenType[TokenType["NameSeparator"] = 4] = "NameSeparator";
  TokenType[TokenType["ValueSeparator"] = 5] = "ValueSeparator";
  TokenType[TokenType["NullOrTrueOrFalseOrNumber"] = 6] = "NullOrTrueOrFalseOrNumber";
  TokenType[TokenType["String"] = 7] = "String";
})(TokenType || (TokenType = {}));
const originalJSONParse = globalThis.JSON.parse;
// First tokenize and then parse the token.
class JSONCParser {
  #whitespace = new Set(" \t\r\n");
  #numberEndToken = new Set([
    ..."[]{}:,/",
    ...this.#whitespace
  ]);
  #text;
  #length;
  #tokenized;
  #options;
  constructor(text, options){
    this.#text = `${text}`;
    this.#length = this.#text.length;
    this.#tokenized = this.#tokenize();
    this.#options = options;
  }
  parse() {
    const token = this.#getNext();
    const res = this.#parseJsonValue(token);
    // make sure all characters have been read
    const { done, value } = this.#tokenized.next();
    if (!done) {
      throw new SyntaxError(buildErrorMessage(value));
    }
    return res;
  }
  /** Read the next token. If the token is read to the end, it throws a SyntaxError. */ #getNext() {
    const { done, value } = this.#tokenized.next();
    if (done) {
      throw new SyntaxError("Unexpected end of JSONC input");
    }
    return value;
  }
  /** Split the JSONC string into token units. Whitespace and comments are skipped. */ *#tokenize() {
    for(let i = 0; i < this.#length; i++){
      // skip whitespace
      if (this.#whitespace.has(this.#text[i])) {
        continue;
      }
      // skip multi line comment (`/*...*/`)
      if (this.#text[i] === "/" && this.#text[i + 1] === "*") {
        i += 2;
        let hasEndOfComment = false;
        for(; i < this.#length; i++){
          if (this.#text[i] === "*" && this.#text[i + 1] === "/") {
            hasEndOfComment = true;
            break;
          }
        }
        if (!hasEndOfComment) {
          throw new SyntaxError("Unexpected end of JSONC input");
        }
        i++;
        continue;
      }
      // skip single line comment (`//...`)
      if (this.#text[i] === "/" && this.#text[i + 1] === "/") {
        i += 2;
        for(; i < this.#length; i++){
          if (this.#text[i] === "\n" || this.#text[i] === "\r") {
            break;
          }
        }
        continue;
      }
      switch(this.#text[i]){
        case "{":
          yield {
            type: TokenType.BeginObject,
            position: i
          };
          break;
        case "}":
          yield {
            type: TokenType.EndObject,
            position: i
          };
          break;
        case "[":
          yield {
            type: TokenType.BeginArray,
            position: i
          };
          break;
        case "]":
          yield {
            type: TokenType.EndArray,
            position: i
          };
          break;
        case ":":
          yield {
            type: TokenType.NameSeparator,
            position: i
          };
          break;
        case ",":
          yield {
            type: TokenType.ValueSeparator,
            position: i
          };
          break;
        case '"':
          {
            const startIndex = i;
            // Need to handle consecutive backslashes correctly
            // '"\\""' => '"'
            // '"\\\\"' => '\\'
            // '"\\\\\\""' => '\\"'
            // '"\\\\\\\\"' => '\\\\'
            let shouldEscapeNext = false;
            i++;
            for(; i < this.#length; i++){
              if (this.#text[i] === '"' && !shouldEscapeNext) {
                break;
              }
              shouldEscapeNext = this.#text[i] === "\\" && !shouldEscapeNext;
            }
            yield {
              type: TokenType.String,
              sourceText: this.#text.substring(startIndex, i + 1),
              position: startIndex
            };
            break;
          }
        default:
          {
            const startIndex = i;
            for(; i < this.#length; i++){
              if (this.#numberEndToken.has(this.#text[i])) {
                break;
              }
            }
            i--;
            yield {
              type: TokenType.NullOrTrueOrFalseOrNumber,
              sourceText: this.#text.substring(startIndex, i + 1),
              position: startIndex
            };
          }
      }
    }
  }
  #parseJsonValue(value) {
    switch(value.type){
      case TokenType.BeginObject:
        return this.#parseObject();
      case TokenType.BeginArray:
        return this.#parseArray();
      case TokenType.NullOrTrueOrFalseOrNumber:
        return this.#parseNullOrTrueOrFalseOrNumber(value);
      case TokenType.String:
        return this.#parseString(value);
      default:
        throw new SyntaxError(buildErrorMessage(value));
    }
  }
  #parseObject() {
    const target = {};
    //   ┌─token1
    // { }
    //      ┌─────────────token1
    //      │   ┌─────────token2
    //      │   │   ┌─────token3
    //      │   │   │   ┌─token4
    //  { "key" : value }
    //      ┌───────────────token1
    //      │   ┌───────────token2
    //      │   │   ┌───────token3
    //      │   │   │   ┌───token4
    //      │   │   │   │ ┌─token1
    //  { "key" : value , }
    //      ┌─────────────────────────────token1
    //      │   ┌─────────────────────────token2
    //      │   │   ┌─────────────────────token3
    //      │   │   │   ┌─────────────────token4
    //      │   │   │   │   ┌─────────────token1
    //      │   │   │   │   │   ┌─────────token2
    //      │   │   │   │   │   │   ┌─────token3
    //      │   │   │   │   │   │   │   ┌─token4
    //  { "key" : value , "key" : value }
    for(let isFirst = true;; isFirst = false){
      const token1 = this.#getNext();
      if ((isFirst || this.#options.allowTrailingComma) && token1.type === TokenType.EndObject) {
        return target;
      }
      if (token1.type !== TokenType.String) {
        throw new SyntaxError(buildErrorMessage(token1));
      }
      const key = this.#parseString(token1);
      const token2 = this.#getNext();
      if (token2.type !== TokenType.NameSeparator) {
        throw new SyntaxError(buildErrorMessage(token2));
      }
      const token3 = this.#getNext();
      Object.defineProperty(target, key, {
        value: this.#parseJsonValue(token3),
        writable: true,
        enumerable: true,
        configurable: true
      });
      const token4 = this.#getNext();
      if (token4.type === TokenType.EndObject) {
        return target;
      }
      if (token4.type !== TokenType.ValueSeparator) {
        throw new SyntaxError(buildErrorMessage(token4));
      }
    }
  }
  #parseArray() {
    const target = [];
    //   ┌─token1
    // [ ]
    //      ┌─────────────token1
    //      │   ┌─────────token2
    //  [ value ]
    //      ┌───────token1
    //      │   ┌───token2
    //      │   │ ┌─token1
    //  [ value , ]
    //      ┌─────────────token1
    //      │   ┌─────────token2
    //      │   │   ┌─────token1
    //      │   │   │   ┌─token2
    //  [ value , value ]
    for(let isFirst = true;; isFirst = false){
      const token1 = this.#getNext();
      if ((isFirst || this.#options.allowTrailingComma) && token1.type === TokenType.EndArray) {
        return target;
      }
      target.push(this.#parseJsonValue(token1));
      const token2 = this.#getNext();
      if (token2.type === TokenType.EndArray) {
        return target;
      }
      if (token2.type !== TokenType.ValueSeparator) {
        throw new SyntaxError(buildErrorMessage(token2));
      }
    }
  }
  #parseString(value) {
    let parsed;
    try {
      // Use JSON.parse to handle `\u0000` etc. correctly.
      parsed = originalJSONParse(value.sourceText);
    } catch  {
      throw new SyntaxError(buildErrorMessage(value));
    }
    assert(typeof parsed === "string");
    return parsed;
  }
  #parseNullOrTrueOrFalseOrNumber(value) {
    if (value.sourceText === "null") {
      return null;
    }
    if (value.sourceText === "true") {
      return true;
    }
    if (value.sourceText === "false") {
      return false;
    }
    let parsed;
    try {
      // Use JSON.parse to handle `+100`, `Infinity` etc. correctly.
      parsed = originalJSONParse(value.sourceText);
    } catch  {
      throw new SyntaxError(buildErrorMessage(value));
    }
    assert(typeof parsed === "number");
    return parsed;
  }
}
function buildErrorMessage({ type, sourceText, position }) {
  let token = "";
  switch(type){
    case TokenType.BeginObject:
      token = "{";
      break;
    case TokenType.EndObject:
      token = "}";
      break;
    case TokenType.BeginArray:
      token = "[";
      break;
    case TokenType.EndArray:
      token = "]";
      break;
    case TokenType.NameSeparator:
      token = ":";
      break;
    case TokenType.ValueSeparator:
      token = ",";
      break;
    case TokenType.NullOrTrueOrFalseOrNumber:
    case TokenType.String:
      // Truncate the string so that it is within 30 lengths.
      token = 30 < sourceText.length ? `${sourceText.slice(0, 30)}...` : sourceText;
      break;
    default:
      throw new Error("unreachable");
  }
  return `Unexpected token ${token} in JSONC at position ${position}`;
}
//# sourceMappingURL=data:application/json;base64,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