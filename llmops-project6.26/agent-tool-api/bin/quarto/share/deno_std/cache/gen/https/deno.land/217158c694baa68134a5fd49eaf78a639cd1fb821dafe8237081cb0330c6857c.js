// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Generates sliding views of the given array of the given size and returns a
 * new array containing all of them.
 *
 * If step is set, each window will start that many elements after the last
 * window's start. (Default: 1)
 *
 * If partial is set, windows will be generated for the last elements of the
 * collection, resulting in some undefined values if size is greater than 1.
 *
 * @example
 * ```ts
 * import { slidingWindows } from "https://deno.land/std@$STD_VERSION/collections/sliding_windows.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 * const numbers = [1, 2, 3, 4, 5];
 *
 * const windows = slidingWindows(numbers, 3);
 * assertEquals(windows, [
 *   [1, 2, 3],
 *   [2, 3, 4],
 *   [3, 4, 5],
 * ]);
 *
 * const windowsWithStep = slidingWindows(numbers, 3, { step: 2 });
 * assertEquals(windowsWithStep, [
 *   [1, 2, 3],
 *   [3, 4, 5],
 * ]);
 *
 * const windowsWithPartial = slidingWindows(numbers, 3, { partial: true });
 * assertEquals(windowsWithPartial, [
 *   [1, 2, 3],
 *   [2, 3, 4],
 *   [3, 4, 5],
 *   [4, 5],
 *   [5],
 * ]);
 * ```
 */ export function slidingWindows(array, size, { step = 1, partial = false } = {}) {
  if (!Number.isInteger(size) || !Number.isInteger(step) || size <= 0 || step <= 0) {
    throw new RangeError("Both size and step must be positive integer.");
  }
  /** length of the return array */ const length = Math.floor((array.length - (partial ? 1 : size)) / step + 1);
  const result = [];
  for(let i = 0; i < length; i++){
    result.push(array.slice(i * step, i * step + size));
  }
  return result;
}
//# sourceMappingURL=data:application/json;base64,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