// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Splits the given array into chunks of the given size and returns them.
 *
 * @example
 * ```ts
 * import { chunk } from "https://deno.land/std@$STD_VERSION/collections/chunk.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const words = [
 *   "lorem",
 *   "ipsum",
 *   "dolor",
 *   "sit",
 *   "amet",
 *   "consetetur",
 *   "sadipscing",
 * ];
 * const chunks = chunk(words, 3);
 *
 * assertEquals(
 *   chunks,
 *   [
 *     ["lorem", "ipsum", "dolor"],
 *     ["sit", "amet", "consetetur"],
 *     ["sadipscing"],
 *   ],
 * );
 * ```
 */ export function chunk(array, size) {
  if (size <= 0 || !Number.isInteger(size)) {
    throw new Error(`Expected size to be an integer greater than 0 but found ${size}`);
  }
  if (array.length === 0) {
    return [];
  }
  const ret = Array.from({
    length: Math.ceil(array.length / size)
  });
  let readIndex = 0;
  let writeIndex = 0;
  while(readIndex < array.length){
    ret[writeIndex] = array.slice(readIndex, readIndex + size);
    writeIndex += 1;
    readIndex += size;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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