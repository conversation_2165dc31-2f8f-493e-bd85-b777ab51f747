// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
const UUID_RE = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
/**
 * Validate that the passed UUID is an RFC4122 v4 UUID.
 *
 * @example
 * ```ts
 * import { validate } from "https://deno.land/std@$STD_VERSION/uuid/v4.ts";
 * import { generate as generateV1 } from "https://deno.land/std@$STD_VERSION/uuid/v1.ts";
 *
 * validate(crypto.randomUUID()); // true
 * validate(generateV1() as string); // false
 * validate("this-is-not-a-uuid"); // false
 * ```
 */ export function validate(id) {
  return UUID_RE.test(id);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3V1aWQvdjQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuY29uc3QgVVVJRF9SRSA9XG4gIC9eWzAtOWEtZl17OH0tWzAtOWEtZl17NH0tNFswLTlhLWZdezN9LVs4OWFiXVswLTlhLWZdezN9LVswLTlhLWZdezEyfSQvaTtcblxuLyoqXG4gKiBWYWxpZGF0ZSB0aGF0IHRoZSBwYXNzZWQgVVVJRCBpcyBhbiBSRkM0MTIyIHY0IFVVSUQuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyB2YWxpZGF0ZSB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL3V1aWQvdjQudHNcIjtcbiAqIGltcG9ydCB7IGdlbmVyYXRlIGFzIGdlbmVyYXRlVjEgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi91dWlkL3YxLnRzXCI7XG4gKlxuICogdmFsaWRhdGUoY3J5cHRvLnJhbmRvbVVVSUQoKSk7IC8vIHRydWVcbiAqIHZhbGlkYXRlKGdlbmVyYXRlVjEoKSBhcyBzdHJpbmcpOyAvLyBmYWxzZVxuICogdmFsaWRhdGUoXCJ0aGlzLWlzLW5vdC1hLXV1aWRcIik7IC8vIGZhbHNlXG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlKGlkOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIFVVSURfUkUudGVzdChpZCk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQyxNQUFNLFVBQ0o7QUFFRjs7Ozs7Ozs7Ozs7O0NBWUMsR0FDRCxPQUFPLFNBQVMsU0FBUyxFQUFVO0VBQ2pDLE9BQU8sUUFBUSxJQUFJLENBQUM7QUFDdEIifQ==