// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Get log level numeric values through enum constants.
 * Defaults to INFO.
 */ export var LogLevels;
(function(LogLevels) {
  LogLevels[LogLevels["NOTSET"] = 0] = "NOTSET";
  LogLevels[LogLevels["DEBUG"] = 10] = "DEBUG";
  LogLevels[LogLevels["INFO"] = 20] = "INFO";
  LogLevels[LogLevels["WARNING"] = 30] = "WARNING";
  LogLevels[LogLevels["ERROR"] = 40] = "ERROR";
  LogLevels[LogLevels["CRITICAL"] = 50] = "CRITICAL";
})(LogLevels || (LogLevels = {}));
/** Permitted log level names */ export const LogLevelNames = Object.keys(LogLevels).filter((key)=>isNaN(Number(key)));
const byLevel = {
  [String(LogLevels.NOTSET)]: "NOTSET",
  [String(LogLevels.DEBUG)]: "DEBUG",
  [String(LogLevels.INFO)]: "INFO",
  [String(LogLevels.WARNING)]: "WARNING",
  [String(LogLevels.ERROR)]: "ERROR",
  [String(LogLevels.CRITICAL)]: "CRITICAL"
};
/** Returns the numeric log level associated with the passed,
 * stringy log level name.
 */ export function getLevelByName(name) {
  switch(name){
    case "NOTSET":
      return LogLevels.NOTSET;
    case "DEBUG":
      return LogLevels.DEBUG;
    case "INFO":
      return LogLevels.INFO;
    case "WARNING":
      return LogLevels.WARNING;
    case "ERROR":
      return LogLevels.ERROR;
    case "CRITICAL":
      return LogLevels.CRITICAL;
    default:
      throw new Error(`no log level found for "${name}"`);
  }
}
/** Returns the stringy log level name provided the numeric log level */ export function getLevelName(level) {
  const levelName = byLevel[level];
  if (levelName) {
    return levelName;
  }
  throw new Error(`no level name found for level: ${level}`);
}
//# sourceMappingURL=data:application/json;base64,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