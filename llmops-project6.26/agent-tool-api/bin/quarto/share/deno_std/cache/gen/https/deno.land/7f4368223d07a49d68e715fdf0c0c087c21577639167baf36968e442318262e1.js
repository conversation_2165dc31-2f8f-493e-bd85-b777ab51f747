// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Converts the byte array to a UUID string
 * @param bytes Used to convert Byte to Hex
 */ export function bytesToUuid(bytes) {
  const bits = [
    ...bytes
  ].map((bit)=>{
    const s = bit.toString(16);
    return bit < 0x10 ? "0" + s : s;
  });
  return [
    ...bits.slice(0, 4),
    "-",
    ...bits.slice(4, 6),
    "-",
    ...bits.slice(6, 8),
    "-",
    ...bits.slice(8, 10),
    "-",
    ...bits.slice(10, 16)
  ].join("");
}
/**
 * Converts a string to a byte array by converting the hex value to a number.
 * @param uuid Value that gets converted.
 */ export function uuidToBytes(uuid) {
  const bytes = [];
  uuid.replace(/[a-fA-F0-9]{2}/g, (hex)=>{
    bytes.push(parseInt(hex, 16));
    return "";
  });
  return bytes;
}
//# sourceMappingURL=data:application/json;base64,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