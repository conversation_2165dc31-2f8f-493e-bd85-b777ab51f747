// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { isValidNumber } from "./_shared.ts";
import { isSemVer } from "./is_semver.ts";
import { FULL, MAX_LENGTH, NUMERICIDENTIFIER, re, src } from "./_shared.ts";
/**
 * Attempt to parse a string as a semantic version, returning either a `SemVer`
 * object or throws a TypeError.
 * @param version The version string to parse
 * @returns A valid SemVer
 */ export function parse(version) {
  if (typeof version === "object") {
    if (isSemVer(version)) {
      return version;
    } else {
      throw new TypeError(`not a valid SemVer object`);
    }
  }
  if (typeof version !== "string") {
    throw new TypeError(`version must be a string`);
  }
  if (version.length > MAX_LENGTH) {
    throw new TypeError(`version is longer than ${MAX_LENGTH} characters`);
  }
  version = version.trim();
  const r = re[FULL];
  const m = version.match(r);
  if (!m) {
    throw new TypeError(`Invalid Version: ${version}`);
  }
  // these are actually numbers
  const major = parseInt(m[1]);
  const minor = parseInt(m[2]);
  const patch = parseInt(m[3]);
  if (major > Number.MAX_SAFE_INTEGER || major < 0) {
    throw new TypeError("Invalid major version");
  }
  if (minor > Number.MAX_SAFE_INTEGER || minor < 0) {
    throw new TypeError("Invalid minor version");
  }
  if (patch > Number.MAX_SAFE_INTEGER || patch < 0) {
    throw new TypeError("Invalid patch version");
  }
  // number-ify any prerelease numeric ids
  const numericIdentifier = new RegExp(`^(${src[NUMERICIDENTIFIER]})$`);
  const prerelease = (m[4] ?? "").split(".").filter((id)=>id).map((id)=>{
    const num = parseInt(id);
    if (id.match(numericIdentifier) && isValidNumber(num)) {
      return num;
    } else {
      return id;
    }
  });
  const build = m[5]?.split(".")?.filter((m)=>m) ?? [];
  return {
    major,
    minor,
    patch,
    prerelease,
    build
  };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9wYXJzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHsgU2VtVmVyIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcbmltcG9ydCB7IGlzVmFsaWROdW1iZXIgfSBmcm9tIFwiLi9fc2hhcmVkLnRzXCI7XG5pbXBvcnQgeyBpc1NlbVZlciB9IGZyb20gXCIuL2lzX3NlbXZlci50c1wiO1xuaW1wb3J0IHsgRlVMTCwgTUFYX0xFTkdUSCwgTlVNRVJJQ0lERU5USUZJRVIsIHJlLCBzcmMgfSBmcm9tIFwiLi9fc2hhcmVkLnRzXCI7XG5cbi8qKlxuICogQXR0ZW1wdCB0byBwYXJzZSBhIHN0cmluZyBhcyBhIHNlbWFudGljIHZlcnNpb24sIHJldHVybmluZyBlaXRoZXIgYSBgU2VtVmVyYFxuICogb2JqZWN0IG9yIHRocm93cyBhIFR5cGVFcnJvci5cbiAqIEBwYXJhbSB2ZXJzaW9uIFRoZSB2ZXJzaW9uIHN0cmluZyB0byBwYXJzZVxuICogQHJldHVybnMgQSB2YWxpZCBTZW1WZXJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlKHZlcnNpb246IHN0cmluZyB8IFNlbVZlcik6IFNlbVZlciB7XG4gIGlmICh0eXBlb2YgdmVyc2lvbiA9PT0gXCJvYmplY3RcIikge1xuICAgIGlmIChpc1NlbVZlcih2ZXJzaW9uKSkge1xuICAgICAgcmV0dXJuIHZlcnNpb247XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYG5vdCBhIHZhbGlkIFNlbVZlciBvYmplY3RgKTtcbiAgICB9XG4gIH1cbiAgaWYgKHR5cGVvZiB2ZXJzaW9uICE9PSBcInN0cmluZ1wiKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIGB2ZXJzaW9uIG11c3QgYmUgYSBzdHJpbmdgLFxuICAgICk7XG4gIH1cblxuICBpZiAodmVyc2lvbi5sZW5ndGggPiBNQVhfTEVOR1RIKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIGB2ZXJzaW9uIGlzIGxvbmdlciB0aGFuICR7TUFYX0xFTkdUSH0gY2hhcmFjdGVyc2AsXG4gICAgKTtcbiAgfVxuXG4gIHZlcnNpb24gPSB2ZXJzaW9uLnRyaW0oKTtcblxuICBjb25zdCByID0gcmVbRlVMTF07XG4gIGNvbnN0IG0gPSB2ZXJzaW9uLm1hdGNoKHIpO1xuICBpZiAoIW0pIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBJbnZhbGlkIFZlcnNpb246ICR7dmVyc2lvbn1gKTtcbiAgfVxuXG4gIC8vIHRoZXNlIGFyZSBhY3R1YWxseSBudW1iZXJzXG4gIGNvbnN0IG1ham9yID0gcGFyc2VJbnQobVsxXSk7XG4gIGNvbnN0IG1pbm9yID0gcGFyc2VJbnQobVsyXSk7XG4gIGNvbnN0IHBhdGNoID0gcGFyc2VJbnQobVszXSk7XG5cbiAgaWYgKG1ham9yID4gTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIgfHwgbWFqb3IgPCAwKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkludmFsaWQgbWFqb3IgdmVyc2lvblwiKTtcbiAgfVxuXG4gIGlmIChtaW5vciA+IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSIHx8IG1pbm9yIDwgMCkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJJbnZhbGlkIG1pbm9yIHZlcnNpb25cIik7XG4gIH1cblxuICBpZiAocGF0Y2ggPiBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUiB8fCBwYXRjaCA8IDApIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCBwYXRjaCB2ZXJzaW9uXCIpO1xuICB9XG5cbiAgLy8gbnVtYmVyLWlmeSBhbnkgcHJlcmVsZWFzZSBudW1lcmljIGlkc1xuICBjb25zdCBudW1lcmljSWRlbnRpZmllciA9IG5ldyBSZWdFeHAoYF4oJHtzcmNbTlVNRVJJQ0lERU5USUZJRVJdfSkkYCk7XG4gIGNvbnN0IHByZXJlbGVhc2UgPSAobVs0XSA/PyBcIlwiKVxuICAgIC5zcGxpdChcIi5cIilcbiAgICAuZmlsdGVyKChpZCkgPT4gaWQpXG4gICAgLm1hcCgoaWQ6IHN0cmluZykgPT4ge1xuICAgICAgY29uc3QgbnVtID0gcGFyc2VJbnQoaWQpO1xuICAgICAgaWYgKGlkLm1hdGNoKG51bWVyaWNJZGVudGlmaWVyKSAmJiBpc1ZhbGlkTnVtYmVyKG51bSkpIHtcbiAgICAgICAgcmV0dXJuIG51bTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBpZDtcbiAgICAgIH1cbiAgICB9KTtcblxuICBjb25zdCBidWlsZCA9IG1bNV0/LnNwbGl0KFwiLlwiKT8uZmlsdGVyKChtKSA9PiBtKSA/PyBbXTtcbiAgcmV0dXJuIHtcbiAgICBtYWpvcixcbiAgICBtaW5vcixcbiAgICBwYXRjaCxcbiAgICBwcmVyZWxlYXNlLFxuICAgIGJ1aWxkLFxuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUFTLGFBQWEsUUFBUSxlQUFlO0FBQzdDLFNBQVMsUUFBUSxRQUFRLGlCQUFpQjtBQUMxQyxTQUFTLElBQUksRUFBRSxVQUFVLEVBQUUsaUJBQWlCLEVBQUUsRUFBRSxFQUFFLEdBQUcsUUFBUSxlQUFlO0FBRTVFOzs7OztDQUtDLEdBQ0QsT0FBTyxTQUFTLE1BQU0sT0FBd0I7RUFDNUMsSUFBSSxPQUFPLFlBQVksVUFBVTtJQUMvQixJQUFJLFNBQVMsVUFBVTtNQUNyQixPQUFPO0lBQ1QsT0FBTztNQUNMLE1BQU0sSUFBSSxVQUFVLENBQUMseUJBQXlCLENBQUM7SUFDakQ7RUFDRjtFQUNBLElBQUksT0FBTyxZQUFZLFVBQVU7SUFDL0IsTUFBTSxJQUFJLFVBQ1IsQ0FBQyx3QkFBd0IsQ0FBQztFQUU5QjtFQUVBLElBQUksUUFBUSxNQUFNLEdBQUcsWUFBWTtJQUMvQixNQUFNLElBQUksVUFDUixDQUFDLHVCQUF1QixFQUFFLFdBQVcsV0FBVyxDQUFDO0VBRXJEO0VBRUEsVUFBVSxRQUFRLElBQUk7RUFFdEIsTUFBTSxJQUFJLEVBQUUsQ0FBQyxLQUFLO0VBQ2xCLE1BQU0sSUFBSSxRQUFRLEtBQUssQ0FBQztFQUN4QixJQUFJLENBQUMsR0FBRztJQUNOLE1BQU0sSUFBSSxVQUFVLENBQUMsaUJBQWlCLEVBQUUsUUFBUSxDQUFDO0VBQ25EO0VBRUEsNkJBQTZCO0VBQzdCLE1BQU0sUUFBUSxTQUFTLENBQUMsQ0FBQyxFQUFFO0VBQzNCLE1BQU0sUUFBUSxTQUFTLENBQUMsQ0FBQyxFQUFFO0VBQzNCLE1BQU0sUUFBUSxTQUFTLENBQUMsQ0FBQyxFQUFFO0VBRTNCLElBQUksUUFBUSxPQUFPLGdCQUFnQixJQUFJLFFBQVEsR0FBRztJQUNoRCxNQUFNLElBQUksVUFBVTtFQUN0QjtFQUVBLElBQUksUUFBUSxPQUFPLGdCQUFnQixJQUFJLFFBQVEsR0FBRztJQUNoRCxNQUFNLElBQUksVUFBVTtFQUN0QjtFQUVBLElBQUksUUFBUSxPQUFPLGdCQUFnQixJQUFJLFFBQVEsR0FBRztJQUNoRCxNQUFNLElBQUksVUFBVTtFQUN0QjtFQUVBLHdDQUF3QztFQUN4QyxNQUFNLG9CQUFvQixJQUFJLE9BQU8sQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDLGtCQUFrQixDQUFDLEVBQUUsQ0FBQztFQUNwRSxNQUFNLGFBQWEsQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsRUFDM0IsS0FBSyxDQUFDLEtBQ04sTUFBTSxDQUFDLENBQUMsS0FBTyxJQUNmLEdBQUcsQ0FBQyxDQUFDO0lBQ0osTUFBTSxNQUFNLFNBQVM7SUFDckIsSUFBSSxHQUFHLEtBQUssQ0FBQyxzQkFBc0IsY0FBYyxNQUFNO01BQ3JELE9BQU87SUFDVCxPQUFPO01BQ0wsT0FBTztJQUNUO0VBQ0Y7RUFFRixNQUFNLFFBQVEsQ0FBQyxDQUFDLEVBQUUsRUFBRSxNQUFNLE1BQU0sT0FBTyxDQUFDLElBQU0sTUFBTSxFQUFFO0VBQ3RELE9BQU87SUFDTDtJQUNBO0lBQ0E7SUFDQTtJQUNBO0VBQ0Y7QUFDRiJ9