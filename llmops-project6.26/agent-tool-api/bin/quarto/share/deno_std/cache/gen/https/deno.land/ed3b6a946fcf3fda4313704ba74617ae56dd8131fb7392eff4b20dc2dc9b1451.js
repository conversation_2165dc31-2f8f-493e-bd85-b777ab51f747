// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/*!
 * Ported and modified from: https://github.com/beatgammit/tar-js and
 * licensed as:
 *
 * (The MIT License)
 *
 * Copyright (c) 2011 T<PERSON> <PERSON>
 * Copyright (c) 2019 Jun Kato
 * Copyright (c) 2018-2023 the Deno authors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */ import { FileTypes, ustarStructure } from "./_common.ts";
import { MultiReader } from "../io/multi_reader.ts";
import { Buffer } from "../io/buffer.ts";
import { assert } from "../assert/assert.ts";
import { HEADER_LENGTH } from "./_common.ts";
const USTAR_MAGIC_HEADER = "ustar\u000000";
/**
 * Simple file reader
 */ class FileReader {
  filePath;
  #file;
  constructor(filePath){
    this.filePath = filePath;
  }
  async read(p) {
    if (!this.#file) {
      this.#file = await Deno.open(this.filePath, {
        read: true
      });
    }
    const res = await this.#file.read(p);
    if (res === null) {
      this.#file.close();
      this.#file = undefined;
    }
    return res;
  }
}
/**
 * Initialize Uint8Array of the specified length filled with 0
 * @param length
 */ function clean(length) {
  const buffer = new Uint8Array(length);
  return buffer;
}
function pad(num, bytes, base = 8) {
  const numString = num.toString(base);
  return "000000000000".slice(numString.length + 12 - bytes) + numString;
}
/**
 * Create header for a file in a tar archive
 */ function formatHeader(data) {
  const encoder = new TextEncoder();
  const buffer = clean(HEADER_LENGTH);
  let offset = 0;
  ustarStructure.forEach(function(value) {
    const entry = encoder.encode(data[value.field] || "");
    buffer.set(entry, offset);
    offset += value.length; // space it out with nulls
  });
  return buffer;
}
/**
 * ### Overview
 * A class to create a tar archive.  Tar archives allow for storing multiple files in a
 * single file (called an archive, or sometimes a tarball).  These archives typically
 * have the '.tar' extension.
 *
 * ### Usage
 * The workflow is to create a Tar instance, append files to it, and then write the
 * tar archive to the filesystem (or other output stream).  See the worked example
 * below for details.
 *
 * ### Compression
 * Tar archives are not compressed by default.  If you want to compress the archive,
 * you may compress the tar archive after creation, but this capability is not provided
 * here.
 *
 * ### File format and limitations
 *
 * The ustar file format is used for creating the archive file.
 * While this format is compatible with most tar readers,
 * the format has several limitations, including:
 * * Files must be smaller than 8GiB
 * * Filenames (including path) must be shorter than 256 characters
 * * Filenames (including path) cannot contain non-ASCII characters
 * * Sparse files are not supported
 *
 * @example
 * ```ts
 * import { Tar } from "https://deno.land/std@$STD_VERSION/archive/tar.ts";
 * import { Buffer } from "https://deno.land/std@$STD_VERSION/io/buffer.ts";
 * import { copy } from "https://deno.land/std@$STD_VERSION/streams/copy.ts";
 *
 * const tar = new Tar();
 *
 * // Now that we've created our tar, let's add some files to it:
 *
 * const content = new TextEncoder().encode("Some arbitrary content");
 * await tar.append("deno.txt", {
 *   reader: new Buffer(content),
 *   contentSize: content.byteLength,
 * });
 *
 * // This file is sourced from the filesystem (and renamed in the archive)
 * await tar.append("filename_in_archive.txt", {
 *   filePath: "./filename_on_filesystem.txt",
 * });
 *
 * // Now let's write the tar (with it's two files) to the filesystem
 * // use tar.getReader() to read the contents.
 *
 * const writer = await Deno.open("./out.tar", { write: true, create: true });
 * await copy(tar.getReader(), writer);
 * writer.close();
 * ```
 */ export class Tar {
  data;
  constructor(){
    this.data = [];
  }
  /**
   * Append a file or reader of arbitrary content to this tar archive. Directories
   * appended to the archive append only the directory itself to the archive, not
   * its contents.  To add a directory and its contents, recursively append the
   * directory's contents.  Directories and subdirectories will be created automatically
   * in the archive as required.
   *
   * @param filenameInArchive file name of the content in the archive
   *                 e.g., test.txt; use slash for directory separators
   * @param source details of the source of the content including the
   *               reference to the content itself and potentially any
   *               related metadata.
   */ async append(filenameInArchive, source) {
    if (typeof filenameInArchive !== "string") {
      throw new Error("file name not specified");
    }
    let fileName = filenameInArchive;
    /**
     * Ustar format has a limitation of file name length.  Specifically:
     * 1. File names can contain at most 255 bytes.
     * 2. File names longer than 100 bytes must be split at a directory separator in two parts,
     * the first being at most 155 bytes long. So, in most cases file names must be a bit shorter
     * than 255 bytes.
     */ // separate file name into two parts if needed
    let fileNamePrefix;
    if (fileName.length > 100) {
      let i = fileName.length;
      while(i >= 0){
        i = fileName.lastIndexOf("/", i);
        if (i <= 155) {
          fileNamePrefix = fileName.slice(0, i);
          fileName = fileName.slice(i + 1);
          break;
        }
        i--;
      }
      const errMsg = "ustar format does not allow a long file name (length of [file name" + "prefix] + / + [file name] must be shorter than 256 bytes)";
      if (i < 0 || fileName.length > 100) {
        throw new Error(errMsg);
      } else {
        assert(fileNamePrefix !== undefined);
        if (fileNamePrefix.length > 155) {
          throw new Error(errMsg);
        }
      }
    }
    source = source || {};
    // set meta data
    let info;
    if (source.filePath) {
      info = await Deno.stat(source.filePath);
      if (info.isDirectory) {
        info.size = 0;
        source.reader = new Buffer();
      }
    }
    const mode = source.fileMode || info && info.mode || parseInt("777", 8) & 0xfff /* 511 */ ;
    const mtime = Math.floor(source.mtime ?? (info?.mtime ?? new Date()).valueOf() / 1000);
    const uid = source.uid || 0;
    const gid = source.gid || 0;
    if (typeof source.owner === "string" && source.owner.length >= 32) {
      throw new Error("ustar format does not allow owner name length >= 32 bytes");
    }
    if (typeof source.group === "string" && source.group.length >= 32) {
      throw new Error("ustar format does not allow group name length >= 32 bytes");
    }
    const fileSize = info?.size ?? source.contentSize;
    assert(fileSize !== undefined, "fileSize must be set");
    const type = source.type ? FileTypes[source.type] : info?.isDirectory ? FileTypes.directory : FileTypes.file;
    const tarData = {
      fileName,
      fileNamePrefix,
      fileMode: pad(mode, 7),
      uid: pad(uid, 7),
      gid: pad(gid, 7),
      fileSize: pad(fileSize, 11),
      mtime: pad(mtime, 11),
      checksum: "        ",
      type: type.toString(),
      ustar: USTAR_MAGIC_HEADER,
      owner: source.owner || "",
      group: source.group || "",
      filePath: source.filePath,
      reader: source.reader
    };
    // calculate the checksum
    let checksum = 0;
    const encoder = new TextEncoder();
    Object.keys(tarData).filter((key)=>[
        "filePath",
        "reader"
      ].indexOf(key) < 0).forEach(function(key) {
      checksum += encoder.encode(tarData[key]).reduce((p, c)=>p + c, 0);
    });
    tarData.checksum = pad(checksum, 6) + "\u0000 ";
    this.data.push(tarData);
  }
  /**
   * Get a Reader instance for this tar archive.
   */ getReader() {
    const readers = [];
    this.data.forEach((tarData)=>{
      let { reader } = tarData;
      const { filePath } = tarData;
      const headerArr = formatHeader(tarData);
      readers.push(new Buffer(headerArr));
      if (!reader) {
        assert(filePath !== undefined);
        reader = new FileReader(filePath);
      }
      readers.push(reader);
      // to the nearest multiple of recordSize
      assert(tarData.fileSize !== undefined, "fileSize must be set");
      readers.push(new Buffer(clean(HEADER_LENGTH - (parseInt(tarData.fileSize, 8) % HEADER_LENGTH || HEADER_LENGTH))));
    });
    // append 2 empty records
    readers.push(new Buffer(clean(HEADER_LENGTH * 2)));
    return new MultiReader(readers);
  }
}
//# sourceMappingURL=data:application/json;base64,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