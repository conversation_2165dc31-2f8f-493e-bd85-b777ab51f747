// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Contains the constant {@linkcode HTTP_METHODS} and the type
 * {@linkcode HttpMethod} and the type guard {@linkcode isHttpMethod} for
 * working with HTTP methods with type safety.
 *
 * @module
 */ /** A constant array of common HTTP methods.
 *
 * This list is compatible with Node.js `http` module.
 */ export const HTTP_METHODS = [
  "ACL",
  "BIND",
  "CHECKOUT",
  "CONNECT",
  "COPY",
  "DELETE",
  "GET",
  "HEAD",
  "LINK",
  "LOCK",
  "M-<PERSON>AR<PERSON>",
  "MER<PERSON>",
  "MKACTIVITY",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "MOVE",
  "NOTIFY",
  "OPTIONS",
  "PATCH",
  "POST",
  "PROPFIND",
  "PROPPATCH",
  "PURGE",
  "PUT",
  "REBI<PERSON>",
  "REPOR<PERSON>",
  "<PERSON>AR<PERSON>",
  "<PERSON>OUR<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>RA<PERSON>",
  "<PERSON><PERSON>ND",
  "UN<PERSON><PERSON><PERSON>",
  "UNLOCK",
  "UNSUBSCRIBE"
];
/** A type guard that determines if a value is a valid HTTP method. */ export function isHttpMethod(value) {
  return HTTP_METHODS.includes(value);
}
//# sourceMappingURL=data:application/json;base64,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