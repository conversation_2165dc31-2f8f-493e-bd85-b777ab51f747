// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { getLevelByName, getLevelName, LogLevels } from "./levels.ts";
/**
 * An object that encapsulates provided message and arguments as well some
 * metadata that can be later used when formatting a message.
 */ export class LogRecord {
  msg;
  #args;
  #datetime;
  level;
  levelName;
  loggerName;
  constructor(options){
    this.msg = options.msg;
    this.#args = [
      ...options.args
    ];
    this.level = options.level;
    this.loggerName = options.loggerName;
    this.#datetime = new Date();
    this.levelName = getLevelName(options.level);
  }
  get args() {
    return [
      ...this.#args
    ];
  }
  get datetime() {
    return new Date(this.#datetime.getTime());
  }
}
export class Logger {
  #level;
  #handlers;
  #loggerName;
  constructor(loggerName, levelName, options = {}){
    this.#loggerName = loggerName;
    this.#level = getLevelByName(levelName);
    this.#handlers = options.handlers || [];
  }
  get level() {
    return this.#level;
  }
  set level(level) {
    this.#level = level;
  }
  get levelName() {
    return getLevelName(this.#level);
  }
  set levelName(levelName) {
    this.#level = getLevelByName(levelName);
  }
  get loggerName() {
    return this.#loggerName;
  }
  set handlers(hndls) {
    this.#handlers = hndls;
  }
  get handlers() {
    return this.#handlers;
  }
  /** If the level of the logger is greater than the level to log, then nothing
   * is logged, otherwise a log record is passed to each log handler.  `msg` data
   * passed in is returned.  If a function is passed in, it is only evaluated
   * if the msg will be logged and the return value will be the result of the
   * function, not the function itself, unless the function isn't called, in which
   * case undefined is returned.  All types are coerced to strings for logging.
   */ #_log(level, msg, ...args) {
    if (this.level > level) {
      return msg instanceof Function ? undefined : msg;
    }
    let fnResult;
    let logMessage;
    if (msg instanceof Function) {
      fnResult = msg();
      logMessage = this.asString(fnResult);
    } else {
      logMessage = this.asString(msg);
    }
    const record = new LogRecord({
      msg: logMessage,
      args: args,
      level: level,
      loggerName: this.loggerName
    });
    this.#handlers.forEach((handler)=>{
      handler.handle(record);
    });
    return msg instanceof Function ? fnResult : msg;
  }
  asString(data, isProperty = false) {
    if (typeof data === "string") {
      if (isProperty) return `"${data}"`;
      return data;
    } else if (data === null || typeof data === "number" || typeof data === "bigint" || typeof data === "boolean" || typeof data === "undefined" || typeof data === "symbol") {
      return String(data);
    } else if (data instanceof Error) {
      return data.stack;
    } else if (typeof data === "object") {
      return `{${Object.entries(data).map(([k, v])=>`"${k}":${this.asString(v, true)}`).join(",")}}`;
    }
    return "undefined";
  }
  debug(msg, ...args) {
    return this.#_log(LogLevels.DEBUG, msg, ...args);
  }
  info(msg, ...args) {
    return this.#_log(LogLevels.INFO, msg, ...args);
  }
  warning(msg, ...args) {
    return this.#_log(LogLevels.WARNING, msg, ...args);
  }
  error(msg, ...args) {
    return this.#_log(LogLevels.ERROR, msg, ...args);
  }
  critical(msg, ...args) {
    return this.#_log(LogLevels.CRITICAL, msg, ...args);
  }
}
//# sourceMappingURL=data:application/json;base64,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