// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns the first element that is the smallest value of the given function or
 * undefined if there are no elements
 *
 * @example
 * ```ts
 * import { minBy } from "https://deno.land/std@$STD_VERSION/collections/min_by.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = [
 *   { name: "<PERSON>", age: 34 },
 *   { name: "<PERSON>", age: 42 },
 *   { name: "<PERSON>", age: 23 },
 * ];
 *
 * const personWithMinAge = minBy(people, (i) => i.age);
 *
 * assertEquals(personWithMinAge, { name: "<PERSON>", age: 23 });
 * ```
 */ export function minBy(array, selector) {
  let min = undefined;
  let minValue = undefined;
  for (const current of array){
    const currentValue = selector(current);
    if (minValue === undefined || currentValue < minValue) {
      min = current;
      minValue = currentValue;
    }
  }
  return min;
}
//# sourceMappingURL=data:application/json;base64,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