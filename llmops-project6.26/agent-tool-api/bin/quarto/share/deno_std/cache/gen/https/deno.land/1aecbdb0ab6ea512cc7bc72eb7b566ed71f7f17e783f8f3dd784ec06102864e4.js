// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { mapEntries } from "./map_entries.ts";
/**
 * Applies the given aggregator to each group in the given grouping, returning the
 * results together with the respective group keys
 *
 * @template T input type of an item in a group in the given grouping.
 * @template A type of the accumulator value, which will match the returned record's values.
 * @example
 * ```ts
 * import { aggregateGroups } from "https://deno.land/std@$STD_VERSION/collections/aggregate_groups.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const foodProperties = {
 *   "Curry": ["spicy", "vegan"],
 *   "Omelette": ["creamy", "vegetarian"],
 * };
 * const descriptions = aggregateGroups(
 *   foodProperties,
 *   (current, key, first, acc) => {
 *     if (first) {
 *       return `${key} is ${current}`;
 *     }
 *
 *     return `${acc} and ${current}`;
 *   },
 * );
 *
 * assertEquals(descriptions, {
 *   "Curry": "<PERSON> is spicy and vegan",
 *   "Omelette": "Omelette is creamy and vegetarian",
 * });
 * ```
 */ export function aggregateGroups(record, aggregator) {
  return mapEntries(record, ([key, values])=>[
      key,
      // Need the type assertions here because the reduce type does not support the type transition we need
      values.reduce((accumulator, current, currentIndex)=>aggregator(current, key, currentIndex === 0, accumulator), undefined)
    ]);
}
//# sourceMappingURL=data:application/json;base64,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