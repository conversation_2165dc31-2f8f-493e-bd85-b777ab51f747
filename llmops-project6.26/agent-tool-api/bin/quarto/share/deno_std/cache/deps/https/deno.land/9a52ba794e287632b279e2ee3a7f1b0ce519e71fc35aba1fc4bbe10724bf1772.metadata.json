{"headers": {"via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "access-control-allow-origin": "*", "x-amz-cf-id": "yuf0QdBrzEoDpA9FaAJdHz3nxANM6FlLmD8aWSdE23DtTIGUtTehEA==", "cross-origin-resource-policy": "same-origin", "x-amz-version-id": "PTHz_uEiloPuwKkjV1FpTkAco7d_R5Rq", "x-frame-options": "DENY", "cache-control": "public, max-age=31536000, immutable", "date": "Wed, 01 May 2024 21:41:49 GMT", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "server-timing": "fetchSource;dur=114", "vary": "Accept-Encoding, Origin", "etag": "\"ca83303fc39810e9fb05aaf8aeeaee9d\"", "x-cache": "Miss from cloudfront", "x-content-type-options": "nosniff", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-embedder-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "server": "deno/gcp-us-east4", "x-amz-replication-status": "COMPLETED", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-length": "719", "cross-origin-opener-policy": "same-origin"}, "url": "https://deno.land/std@0.204.0/collections/binary_heap.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 530179362}}