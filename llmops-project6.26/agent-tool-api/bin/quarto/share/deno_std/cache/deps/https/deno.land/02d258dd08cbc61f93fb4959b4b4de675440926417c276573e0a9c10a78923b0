// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.

/**
 * Name string is a fully-qualified domain name.
 *
 * @example
 * ```ts
 * import { NAMESPACE_DNS } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_DNS); // => 6ba7b810-9dad-11d1-80b4-00c04fd430c8
 * ```
 */
export const NAMESPACE_DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is a URL.
 *
 * @example
 * ```ts
 * import { NAMESPACE_URL } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_URL); // => 6ba7b811-9dad-11d1-80b4-00c04fd430c8
 * ```
 */
export const NAMESPACE_URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is an ISO OID.
 *
 * @example
 * ```ts
 * import { NAMESPACE_OID } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_OID); // => 6ba7b812-9dad-11d1-80b4-00c04fd430c8
 * ```
 */
export const NAMESPACE_OID = "6ba7b812-9dad-11d1-80b4-00c04fd430c8";
/**
 * Name string is an X.500 DN (in DER or a text output format).
 *
 * @example
 * ```ts
 * import { NAMESPACE_X500 } from "https://deno.land/std@$STD_VERSION/uuid/constants.ts";
 *
 * console.log(NAMESPACE_X500); // => 6ba7b814-9dad-11d1-80b4-00c04fd430c8
 * ```
 */
export const NAMESPACE_X500 = "6ba7b814-9dad-11d1-80b4-00c04fd430c8";
