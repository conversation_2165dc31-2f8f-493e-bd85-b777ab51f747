{"headers": {"age": "51224", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "x-amz-cf-pop": "IAD61-P2", "x-frame-options": "DENY", "accept-ranges": "bytes", "x-content-type-options": "nosniff", "cross-origin-embedder-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "date": "Wed, 01 May 2024 07:28:06 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-replication-status": "COMPLETED", "via": "http/2 edgeproxy-h", "server-timing": "fetchSource;dur=480", "content-length": "286", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "x-amz-cf-id": "aPZmvkkwE_CrJgU4LmuJ8GShECId_RqpU_zFOUAzZtncWZnn38LRYA==", "vary": "Accept-Encoding, Origin", "server": "deno/gcp-us-east4", "x-amz-version-id": "Em05cr2bSXkWZiJlFBZFh_swIWWr9VsA"}, "url": "https://deno.land/std@0.204.0/streams/to_json.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 645941120}}