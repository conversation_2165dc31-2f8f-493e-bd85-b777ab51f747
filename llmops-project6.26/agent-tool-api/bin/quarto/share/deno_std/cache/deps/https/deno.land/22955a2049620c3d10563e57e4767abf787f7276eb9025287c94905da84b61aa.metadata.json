{"headers": {"cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "x-amz-server-side-encryption": "AES256", "x-frame-options": "DENY", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "referrer-policy": "strict-origin-when-cross-origin", "accept-ranges": "bytes", "etag": "\"46829c54f39c8068a39ee2c690e84752\"", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-amz-version-id": "uU3vZtK5hXhObHjvLhDDyFNNZF_mxLaf", "cache-control": "public, max-age=31536000, immutable", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-content-type-options": "nosniff", "cross-origin-opener-policy": "same-origin", "x-amz-cf-id": "DTwNBAFZFURKtMej-w8WQ20SoKR1LR4Hfz-BFviYID4342jtB4JMIA==", "server": "deno/gcp-us-east4", "x-cache": "Hit from cloudfront", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "age": "659983", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "cross-origin-resource-policy": "same-origin", "server-timing": "fetchSource;dur=32", "content-length": "639", "via": "http/2 edgeproxy-h", "content-type": "application/typescript; charset=utf-8"}, "url": "https://deno.land/std@0.204.0/yaml/_error.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 755523150}}