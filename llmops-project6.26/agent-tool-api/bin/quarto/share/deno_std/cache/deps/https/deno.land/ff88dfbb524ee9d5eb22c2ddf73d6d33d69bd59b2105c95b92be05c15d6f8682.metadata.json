{"headers": {"strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-version-id": "VMtCS7ejxkva3J3aTqaZj3HQ46hlgGT4", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "etag": "\"e5cb18ddcb4d19cf2cce3187c413388f\"", "x-cache": "Hit from cloudfront", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cache-control": "public, max-age=31536000, immutable", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "content-length": "1072", "date": "Wed, 01 May 2024 19:05:03 GMT", "referrer-policy": "strict-origin-when-cross-origin", "cross-origin-resource-policy": "same-origin", "server": "deno/gcp-us-east4", "x-amz-cf-pop": "IAD61-P2", "via": "http/2 edgeproxy-h", "age": "9406", "accept-ranges": "bytes", "x-amz-replication-status": "COMPLETED", "content-type": "application/typescript; charset=utf-8", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "cross-origin-opener-policy": "same-origin", "vary": "Accept-Encoding, Origin", "server-timing": "fetchSource;dur=16", "x-amz-cf-id": "c8u2UZDy68RxGFEiHXMF84ktCF99fmYClZo8rB22Hn2bddAOPYdLbA==", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/collections/distinct_by.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 430776533}}