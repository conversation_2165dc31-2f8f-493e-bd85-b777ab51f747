// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.

/**
 * Contains the constant {@linkcode HTTP_METHODS} and the type
 * {@linkcode HttpMethod} and the type guard {@linkcode isHttpMethod} for
 * working with HTTP methods with type safety.
 *
 * @module
 */

/** A constant array of common HTTP methods.
 *
 * This list is compatible with Node.js `http` module.
 */
export const HTTP_METHODS = [
  "ACL",
  "BIND",
  "CHECKOUT",
  "CONNECT",
  "COPY",
  "DELETE",
  "GET",
  "HEAD",
  "LINK",
  "LOCK",
  "M-<PERSON>AR<PERSON>",
  "MER<PERSON>",
  "MKACTIVITY",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "MOVE",
  "NOTIFY",
  "OPTIONS",
  "PATCH",
  "POST",
  "PROPFIND",
  "PROPPATCH",
  "PURGE",
  "PUT",
  "REBI<PERSON>",
  "REPOR<PERSON>",
  "<PERSON>AR<PERSON>",
  "<PERSON>OUR<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "TRA<PERSON>",
  "<PERSON>BIND",
  "<PERSON><PERSON><PERSON><PERSON>",
  "UNLOC<PERSON>",
  "UNSUBSCRIBE",
] as const;

/** A type representing string literals of each of the common HTTP method. */
export type HttpMethod = typeof HTTP_METHODS[number];

/** A type guard that determines if a value is a valid HTTP method. */
export function isHttpMethod(value: unknown): value is HttpMethod {
  return HTTP_METHODS.includes(value as HttpMethod);
}
