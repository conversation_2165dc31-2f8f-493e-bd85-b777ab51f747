{"headers": {"x-cache": "Hit from cloudfront", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "date": "Wed, 24 Apr 2024 07:06:42 GMT", "content-type": "application/typescript; charset=utf-8", "x-amz-version-id": "IOyyWJhMZus6ulXVKW4TNMo4M4ChqJoJ", "etag": "\"2e41e4ca9b9eabef8ec42cb2f9a1e62b\"", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=10", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "content-length": "1801", "via": "http/2 edgeproxy-h", "server": "deno/gcp-us-east4", "accept-ranges": "bytes", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-amz-cf-id": "b1eIajXouFyqkxzGiE5xkgjLnoFmr7gB0idIBACb-Qfpmsi5Cop3rw==", "x-amz-cf-pop": "IAD61-P2", "cross-origin-opener-policy": "same-origin", "cache-control": "public, max-age=31536000, immutable", "age": "657309", "cross-origin-resource-policy": "same-origin", "vary": "Accept-Encoding, Origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/http/_negotiation/common.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 626369271}}