{"headers": {"server-timing": "fetchSource;dur=46", "etag": "\"39bd0ec966403170c082a18ab6397ad3\"", "cross-origin-embedder-policy": "same-origin", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "x-amz-cf-pop": "IAD61-P2", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "cross-origin-resource-policy": "same-origin", "access-control-allow-origin": "*", "x-cache": "Hit from cloudfront", "x-amz-cf-id": "SYjR_lKv7i1ey106RGHEI260zMSIaCdNf2B8-w1X6zEr_F-XViqg3A==", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "3qv1dHPSwMRYuWE9oAbnUfPaOEmyZi.e", "x-frame-options": "DENY", "cache-control": "public, max-age=31536000, immutable", "accept-ranges": "bytes", "content-length": "4543", "server": "deno/gcp-us-east4", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "age": "659983", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/yaml/_dumper/dumper_state.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 220381173}}