{"headers": {"x-amz-replication-status": "COMPLETED", "cross-origin-embedder-policy": "same-origin", "age": "617358", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "x-amz-cf-id": "MupoY2Qt0ORQKYtsKA940Lx7fzMMp6ZjYQtd783MxR70-8phz8-Ldg==", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "content-type": "application/typescript; charset=utf-8", "cache-control": "public, max-age=31536000, immutable", "etag": "\"9165cfe8c497d8ad6f59615563327de1\"", "x-amz-version-id": "N9._Z90rVwPneyLfu3bp0ue3Yae5BmXm", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "access-control-allow-origin": "*", "server": "deno/gcp-us-east4", "vary": "Accept-Encoding, Origin", "x-cache": "Hit from cloudfront", "server-timing": "fetchSource;dur=80", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "content-length": "282", "accept-ranges": "bytes", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/semver/gt.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 146488008}}