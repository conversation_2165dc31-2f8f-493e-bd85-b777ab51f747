{"headers": {"vary": "Accept-Encoding, Origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "referrer-policy": "strict-origin-when-cross-origin", "cross-origin-resource-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "x-amz-cf-id": "oPLx-zZiPraFVXKJnrQwKJmclqdfUse_kDKzqtrF2NsGER1JvV379Q==", "x-amz-cf-pop": "IAD61-P2", "date": "Wed, 01 May 2024 21:41:49 GMT", "access-control-allow-origin": "*", "etag": "\"4ace8b84f5aa6c4da32da636550e7496\"", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "via": "http/2 edgeproxy-h", "x-frame-options": "DENY", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "4VcjI67Dljsvqvo1ZLzAOAy9OehG1a1K", "cross-origin-embedder-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "server-timing": "fetchSource;dur=48", "x-cache": "Hit from cloudfront", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-length": "2217", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8"}, "url": "https://deno.land/std@0.204.0/signal/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 106958809}}