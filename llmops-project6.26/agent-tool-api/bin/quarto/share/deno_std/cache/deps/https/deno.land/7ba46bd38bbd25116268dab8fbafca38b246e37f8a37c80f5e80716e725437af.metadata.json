{"headers": {"content-type": "application/typescript; charset=utf-8", "content-length": "1976", "x-amz-cf-id": "U1wewbjdnCiHYq_dVEveW5fDNQMmk4_Rhw8LG3i65AM3rHiAL5B2Wg==", "x-amz-server-side-encryption": "AES256", "cache-control": "public, max-age=31536000, immutable", "via": "http/2 edgeproxy-h", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "access-control-allow-origin": "*", "cross-origin-opener-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "x-amz-version-id": "jeqUVi_bVUpyzQ8VDvFrkZeg3JekWuqp", "vary": "Accept-Encoding, Origin", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "DENY", "date": "Wed, 24 Apr 2024 06:36:39 GMT", "age": "659112", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=56", "cross-origin-resource-policy": "same-origin", "accept-ranges": "bytes", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "cross-origin-embedder-policy": "same-origin"}, "url": "https://deno.land/std@0.204.0/path/posix/extname.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 199744083}}