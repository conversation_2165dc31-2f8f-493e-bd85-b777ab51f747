{"headers": {"vary": "Accept-Encoding, Origin", "x-amz-cf-pop": "IAD61-P2", "x-amz-server-side-encryption": "AES256", "content-length": "935", "x-amz-replication-status": "COMPLETED", "age": "617358", "x-amz-version-id": "sXD6BeJ3phrYVe8qYVurlJ71XzUuDHzm", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "ZhA8Np9_395t37OtZQAgv3W-092sIgeA2cvfj-r8FEaCdpa6ULKJkQ==", "cross-origin-resource-policy": "same-origin", "x-cache": "Hit from cloudfront", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "etag": "\"eb00fbb578fecc1851bdae7d78a8278b\"", "x-content-type-options": "nosniff", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "content-type": "application/typescript; charset=utf-8", "server-timing": "fetchSource;dur=500", "cache-control": "public, max-age=31536000, immutable", "via": "http/2 edgeproxy-h", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "server": "deno/gcp-us-east4", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "accept-ranges": "bytes", "access-control-allow-origin": "*", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/semver/is_semver_range.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 603522193}}