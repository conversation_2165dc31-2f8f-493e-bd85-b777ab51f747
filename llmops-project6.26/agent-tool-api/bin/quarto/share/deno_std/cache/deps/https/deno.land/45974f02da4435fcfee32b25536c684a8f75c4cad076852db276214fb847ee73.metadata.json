{"headers": {"x-frame-options": "DENY", "referrer-policy": "strict-origin-when-cross-origin", "accept-ranges": "bytes", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "ZRt7ooAwQiYAGzQ1uHnM3h1tHXk32bwP", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "cross-origin-opener-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "content-length": "2080", "cross-origin-resource-policy": "same-origin", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "x-amz-server-side-encryption": "AES256", "age": "617358", "cache-control": "public, max-age=31536000, immutable", "etag": "\"b50878829b5eafbbc4e9566a2e5405a4\"", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "server-timing": "fetchSource;dur=84", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "server": "deno/gcp-us-east4", "access-control-allow-origin": "*", "x-amz-cf-id": "9gWH2CyfmdAoHLTuF3qslOBzgk6qOtxjwmoP-YJ8k7UEdkczUwACVw==", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin"}, "url": "https://deno.land/std@0.204.0/semver/constants.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 146351443}}