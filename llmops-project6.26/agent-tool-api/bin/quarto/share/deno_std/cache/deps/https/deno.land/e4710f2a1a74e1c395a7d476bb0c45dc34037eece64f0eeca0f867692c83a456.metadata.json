{"headers": {"x-frame-options": "DENY", "age": "659982", "content-length": "2135", "via": "http/2 edgeproxy-h", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-version-id": "fUXj.KsroabKtmQRfASNEq3HWhPwwnI3", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "server-timing": "fetchSource;dur=10", "cross-origin-embedder-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "server": "deno/gcp-us-east4", "accept-ranges": "bytes", "vary": "Accept-Encoding, Origin", "x-amz-cf-id": "tNGP_YkfxpzI__zIysd8gmMYDSPuo-pmEXnF-s7wPbn5Sh7w_qwFrQ==", "access-control-allow-origin": "*", "cross-origin-opener-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-server-side-encryption": "AES256", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-replication-status": "COMPLETED", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "etag": "\"b5c6a0a1d1450a16b0e6d5586f56a68b\"", "cache-control": "public, max-age=31536000, immutable", "cross-origin-resource-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "content-type": "application/typescript; charset=utf-8"}, "url": "https://deno.land/std@0.204.0/yaml/_utils.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 982452230}}