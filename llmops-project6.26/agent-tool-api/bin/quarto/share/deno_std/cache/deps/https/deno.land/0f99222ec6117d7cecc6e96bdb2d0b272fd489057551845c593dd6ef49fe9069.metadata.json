{"headers": {"age": "51224", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "server-timing": "fetchSource;dur=462", "x-content-type-options": "nosniff", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-frame-options": "DENY", "x-amz-version-id": "BLWSgZ067hwT7Kejwo6o3L3pTdF2_85W", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "accept-ranges": "bytes", "x-amz-cf-id": "VFKwRi4jcIXEMfjCzHZKCFQ1VyypmA1nm0npGOQSsOroAH_rGfN0GA==", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-opener-policy": "same-origin", "access-control-allow-origin": "*", "content-length": "2075", "date": "Wed, 01 May 2024 07:28:06 GMT", "referrer-policy": "strict-origin-when-cross-origin", "content-type": "application/typescript; charset=utf-8", "etag": "\"f9fa2f46f9a72f76906eafb441f0a1d7\"", "server": "deno/gcp-us-east4", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "cache-control": "public, max-age=31536000, immutable", "cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "x-amz-replication-status": "COMPLETED"}, "url": "https://deno.land/std@0.204.0/streams/text_line_stream.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 595847465}}