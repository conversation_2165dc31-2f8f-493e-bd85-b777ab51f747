{"headers": {"cross-origin-embedder-policy": "same-origin", "age": "658100", "x-cache": "Hit from cloudfront", "cross-origin-opener-policy": "same-origin", "date": "Wed, 24 Apr 2024 06:53:29 GMT", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED", "x-amz-cf-id": "O7TGWI-9FfDe3nZChNZYUk7jdFqbGM-3sYtTMbbSW_HEVVKOj47vjQ==", "access-control-allow-origin": "*", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "referrer-policy": "strict-origin-when-cross-origin", "cache-control": "public, max-age=31536000, immutable", "server": "deno/gcp-us-east4", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=68", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "accept-ranges": "bytes", "content-length": "1179", "x-amz-version-id": "LBvP1JhNJm7fg_vBRNDO2p1jZBNZ7Gom"}, "url": "https://deno.land/std@0.204.0/io/read_string_delim.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 28965157}}