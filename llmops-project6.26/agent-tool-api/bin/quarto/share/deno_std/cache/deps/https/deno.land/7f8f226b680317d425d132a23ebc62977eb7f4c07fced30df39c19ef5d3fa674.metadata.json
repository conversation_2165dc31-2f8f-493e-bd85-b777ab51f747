{"headers": {"x-amz-version-id": "hV7Sq8hKTZ3YWWKniaTaXX.qF7Lz_n2J", "content-type": "application/typescript; charset=utf-8", "cross-origin-opener-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "cross-origin-resource-policy": "same-origin", "content-length": "1109", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "x-amz-cf-id": "EejsODnjWsCdPFOSRONq6XsSedqdT1Up_8tvmAiUSdUd_JU1ZR46Dw==", "x-amz-server-side-encryption": "AES256", "etag": "\"18ef5f15fa81efb1aa08572cc8d3658a\"", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server-timing": "fetchSource;dur=8", "via": "http/2 edgeproxy-h", "cross-origin-embedder-policy": "same-origin", "server": "deno/gcp-us-east4", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "x-frame-options": "DENY", "vary": "Accept-Encoding, Origin", "x-amz-replication-status": "COMPLETED", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "age": "659196", "accept-ranges": "bytes"}, "url": "https://deno.land/std@0.204.0/path/posix/dirname.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 977911120}}