{"headers": {"cross-origin-opener-policy": "same-origin", "x-cache": "Hit from cloudfront", "x-amz-version-id": "Cy651rthnTVbCB0uBZ144RNyaVovByEV", "referrer-policy": "strict-origin-when-cross-origin", "content-type": "application/typescript; charset=utf-8", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "content-length": "1394", "vary": "Accept-Encoding, Origin", "x-amz-replication-status": "COMPLETED", "etag": "\"1f1a75c16a156feb856e6ed5d2a048b8\"", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "access-control-allow-origin": "*", "cross-origin-embedder-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "date": "Wed, 01 May 2024 21:41:49 GMT", "via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "accept-ranges": "bytes", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=56", "x-amz-cf-id": "ixXNo7RhKMkuEIUoJdaMhPjYbUCx6aw6EWwtPao2eg5Euk94ooWBtQ=="}, "url": "https://deno.land/std@0.204.0/bytes/index_of_needle.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 488540684}}