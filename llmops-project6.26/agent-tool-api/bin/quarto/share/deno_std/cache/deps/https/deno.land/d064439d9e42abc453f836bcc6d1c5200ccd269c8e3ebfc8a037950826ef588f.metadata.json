{"headers": {"x-frame-options": "DENY", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "m6sdwOg5_oJv8OzDv12OO6eFBgc7im22aifyYvnkh88jTgxAAaFbiw==", "x-amz-replication-status": "COMPLETED", "via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "content-length": "142", "cross-origin-embedder-policy": "same-origin", "access-control-allow-origin": "*", "cross-origin-resource-policy": "same-origin", "cache-control": "public, max-age=31536000, immutable", "date": "Wed, 01 May 2024 21:41:49 GMT", "accept-ranges": "bytes", "referrer-policy": "strict-origin-when-cross-origin", "etag": "\"b2438b0bcae4217c986f89fa7b659a42\"", "server": "deno/gcp-us-east4", "content-type": "application/typescript; charset=utf-8", "server-timing": "fetchSource;dur=12", "x-amz-version-id": "Ml1xTyIyCa7XiwSSZGViiqAoPlTOyp4p", "cross-origin-opener-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/jsonc/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 152937131}}