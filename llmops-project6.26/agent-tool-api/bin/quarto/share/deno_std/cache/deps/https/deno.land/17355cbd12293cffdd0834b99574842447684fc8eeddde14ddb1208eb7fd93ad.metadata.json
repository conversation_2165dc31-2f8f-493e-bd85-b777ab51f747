{"headers": {"x-amz-cf-pop": "IAD61-P2", "date": "Wed, 24 Apr 2024 06:53:28 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-replication-status": "COMPLETED", "x-cache": "Hit from cloudfront", "x-frame-options": "DENY", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "content-length": "263", "x-amz-version-id": "p.mFriCnpbo8N9ExOt8Rebf84WquAUvb", "accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "etag": "\"776fb15a8653f2c11208704e3c52e177\"", "server-timing": "fetchSource;dur=168", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "age": "658103", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "cross-origin-embedder-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "x-amz-cf-id": "iTYOp3vg3jVG5WA1obfudPU5RwesPm-zERdPSmi-acmfkGasKSrJHw==", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/path/_common/dirname.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 813312011}}