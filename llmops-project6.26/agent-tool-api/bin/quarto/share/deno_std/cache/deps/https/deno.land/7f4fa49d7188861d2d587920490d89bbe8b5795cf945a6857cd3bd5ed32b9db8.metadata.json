{"headers": {"strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cross-origin-resource-policy": "same-origin", "via": "http/2 edgeproxy-h", "x-amz-version-id": "Lu5S6ODFAuKPWag8cp5iEOeJZgv_vsun", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "server": "deno/gcp-us-east4", "content-length": "1585", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED", "vary": "Accept-Encoding, Origin", "age": "659197", "cache-control": "public, max-age=31536000, immutable", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=48", "etag": "\"e18740cbb79f14bd2d865a089f0b66d1\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "accept-ranges": "bytes", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "cross-origin-opener-policy": "same-origin", "cross-origin-embedder-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "x-amz-cf-id": "xkKHnxLusmi0fqy69tP63oOjj-cLIyffnkLNy7ZeePhp_O6f6-mx6Q==", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/path/posix/resolve.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 223144268}}