{"headers": {"x-amz-version-id": "33OW3H7PITWvf7ilT8PG3_pk5F1QrsvB", "x-amz-cf-id": "B5uqvIdAsKZO25RLOx7HqB-xOAnBufvtv-XhrQx-Bn4z3GpbQsjm1Q==", "x-cache": "Hit from cloudfront", "etag": "\"c0beef612485c7cad2560611ed85b4e3\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "age": "223222", "accept-ranges": "bytes", "cross-origin-embedder-policy": "same-origin", "via": "http/2 edgeproxy-h", "access-control-allow-origin": "*", "x-frame-options": "DENY", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=50", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-pop": "IAD61-P2", "x-amz-server-side-encryption": "AES256", "date": "Mon, 29 Apr 2024 07:41:27 GMT", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "content-length": "1804", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/io/string_writer.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 529821814}}