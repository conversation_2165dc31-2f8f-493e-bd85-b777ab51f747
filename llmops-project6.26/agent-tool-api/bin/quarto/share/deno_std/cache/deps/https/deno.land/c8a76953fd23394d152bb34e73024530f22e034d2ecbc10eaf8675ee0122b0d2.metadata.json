{"headers": {"x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin", "age": "51224", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cache-control": "public, max-age=31536000, immutable", "etag": "\"51a6532237710dfcec50b474a059a2ec\"", "accept-ranges": "bytes", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "via": "http/2 edgeproxy-h", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=496", "date": "Wed, 01 May 2024 07:28:06 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "x-amz-cf-id": "mzaIW-UGZd7-owWFhw-m6OW7DD-DlskdvkaDbr6KQ2ruvlaE9BA-yw==", "x-frame-options": "DENY", "cross-origin-opener-policy": "same-origin", "x-amz-version-id": "YuEkoXZlS3jzg_CU3CA8vYVdiLhIMf6r", "access-control-allow-origin": "*", "cross-origin-embedder-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "content-length": "2426", "cross-origin-resource-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/streams/text_delimiter_stream.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 596911494}}