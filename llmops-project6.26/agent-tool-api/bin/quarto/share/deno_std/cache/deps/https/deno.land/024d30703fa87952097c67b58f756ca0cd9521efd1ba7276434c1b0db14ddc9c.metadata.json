{"headers": {"cache-control": "public, max-age=31536000, immutable", "cross-origin-resource-policy": "same-origin", "etag": "\"4c27a397dd21cf13813fdd1620d1f381\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-cache": "Hit from cloudfront", "content-type": "application/typescript; charset=utf-8", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=148", "cross-origin-embedder-policy": "same-origin", "access-control-allow-origin": "*", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "accept-ranges": "bytes", "x-frame-options": "DENY", "x-amz-cf-id": "v-qrodnNhUHQJYFW1xTZaLucQ5_MQmD_OK7tm6_9Hj_oylD1cT40Ig==", "date": "Wed, 24 Apr 2024 10:33:23 GMT", "x-amz-replication-status": "COMPLETED", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "x-content-type-options": "nosniff", "x-amz-version-id": "vJ5t_L.7YN7SMA6fhGReRQZNm4niIZi2", "content-length": "2437", "cross-origin-opener-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-server-side-encryption": "AES256", "age": "644906"}, "url": "https://deno.land/std@0.204.0/http/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 336157855}}