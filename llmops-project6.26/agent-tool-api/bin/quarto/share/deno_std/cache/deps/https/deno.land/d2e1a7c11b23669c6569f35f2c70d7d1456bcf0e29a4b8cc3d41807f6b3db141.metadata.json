{"headers": {"referrer-policy": "strict-origin-when-cross-origin", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-frame-options": "DENY", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8", "etag": "\"0d902a5c23caee09928f8f0c05141622\"", "server-timing": "fetchSource;dur=64", "cross-origin-embedder-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-resource-policy": "same-origin", "content-length": "2412", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "age": "617358", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "c5lrin9mstU9EO47unutsVyFDPh1h6_p5ekLCoorp2mBOVnC9DhFow==", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "via": "http/2 edgeproxy-h", "x-amz-version-id": "vUUCadwTzcgR6dW.eESBL384xhi8epFX", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "x-amz-server-side-encryption": "AES256", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/semver/outside.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 78458530}}