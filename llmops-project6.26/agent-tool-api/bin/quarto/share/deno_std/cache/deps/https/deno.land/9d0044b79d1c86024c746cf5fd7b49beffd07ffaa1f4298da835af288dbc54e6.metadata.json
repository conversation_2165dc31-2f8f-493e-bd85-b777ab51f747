{"headers": {"date": "Wed, 01 May 2024 21:41:49 GMT", "x-amz-cf-id": "fHUy1NOFILeJ1gXSwmBTZBV40abyznZD-IW22zX2_XSiC56ijbYtsQ==", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "cache-control": "public, max-age=31536000, immutable", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "etag": "\"9b75d0f25a602b37f0c153685664877d\"", "x-amz-server-side-encryption": "AES256", "access-control-allow-origin": "*", "x-amz-version-id": "tByPr8h4_qy9fLSfQ6Bz5FnCLRGHMJnQ", "vary": "Accept-Encoding, Origin", "x-frame-options": "DENY", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-content-type-options": "nosniff", "x-amz-replication-status": "COMPLETED", "content-length": "27726", "x-cache": "Hit from cloudfront", "accept-ranges": "bytes", "server-timing": "fetchSource;dur=10", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-opener-policy": "same-origin", "via": "http/2 edgeproxy-h"}, "url": "https://deno.land/std@0.204.0/fmt/printf.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 336735265}}