{"headers": {"vary": "Accept-Encoding, Origin", "cross-origin-opener-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "x-cache": "Miss from cloudfront", "x-amz-cf-id": "FjCwaIKB3aKL3H1_OrJtsKytIA3GEqUBqpeiu8hpWVaUY7vfTwt1Ww==", "x-amz-cf-pop": "IAD61-P2", "x-content-type-options": "nosniff", "cross-origin-embedder-policy": "same-origin", "via": "http/2 edgeproxy-h", "date": "Wed, 01 May 2024 21:41:49 GMT", "x-amz-version-id": "vJyveoY96QWcWyz4muW0q4VxQOoOQvb3", "x-amz-server-side-encryption": "AES256", "accept-ranges": "bytes", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "cross-origin-resource-policy": "same-origin", "cache-control": "public, max-age=31536000, immutable", "access-control-allow-origin": "*", "content-length": "1069", "etag": "\"113df3cf0a0568e0701c0f77ced5d89f\"", "referrer-policy": "strict-origin-when-cross-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=68", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/collections/unzip.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 439245116}}