{"headers": {"accept-ranges": "bytes", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "cross-origin-embedder-policy": "same-origin", "x-cache": "Hit from cloudfront", "age": "659195", "etag": "\"06bf8905762ae15e1bc84b8d183bb580\"", "content-type": "application/typescript; charset=utf-8", "cache-control": "public, max-age=31536000, immutable", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=132", "x-amz-cf-id": "NxzX9ZPBJ6C4qWDbQXpq5bA8_kIMb_THXgzdkbQs593VndBuLb44wg==", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin", "cross-origin-opener-policy": "same-origin", "content-length": "521", "via": "http/2 edgeproxy-h", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "cross-origin-resource-policy": "same-origin", "x-amz-version-id": "ppfIOTK_LSwJpfdjsSTyjIDckl7aPG9K", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/path/is_absolute.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 820098313}}