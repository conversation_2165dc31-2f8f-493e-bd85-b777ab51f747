{"headers": {"x-frame-options": "DENY", "content-length": "2066", "vary": "Accept-Encoding, Origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-resource-policy": "same-origin", "via": "http/2 edgeproxy-h", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "GQEdSLBOGWyKXehTN7PergjHxlCKEDPfqH6WwjKQ2jl5qppkasw3pg==", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "6D3mMFSGebnYbYz1Jl_Ns5bY95mv9zlC", "x-cache": "Hit from cloudfront", "etag": "\"49e753ce4042f1ce229d0e8d2c17c1fb\"", "x-content-type-options": "nosniff", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "cache-control": "public, max-age=31536000, immutable", "referrer-policy": "strict-origin-when-cross-origin", "access-control-allow-origin": "*", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "accept-ranges": "bytes", "cross-origin-opener-policy": "same-origin", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=98", "x-amz-cf-pop": "IAD61-P2", "cross-origin-embedder-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "age": "617358", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/semver/parse.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 142429340}}