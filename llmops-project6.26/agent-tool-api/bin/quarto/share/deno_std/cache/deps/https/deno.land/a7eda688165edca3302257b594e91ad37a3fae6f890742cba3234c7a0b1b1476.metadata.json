{"headers": {"cross-origin-resource-policy": "same-origin", "x-amz-cf-id": "ahH1pgmmtLR-IjFhxPryBJCml6oErXDVM4xWbtRpeZflHdAR7IYtgg==", "etag": "\"606d8d655dbe36c276605b358df59fd8\"", "x-amz-version-id": "mVJnk7SlJSmEDSZX88Fa6HyuRYK7j4eP", "cache-control": "public, max-age=31536000, immutable", "access-control-allow-origin": "*", "server-timing": "fetchSource;dur=164", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-pop": "IAD61-P2", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "referrer-policy": "strict-origin-when-cross-origin", "server": "deno/gcp-us-east4", "date": "Wed, 01 May 2024 21:41:49 GMT", "content-length": "700", "cross-origin-embedder-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "accept-ranges": "bytes", "x-frame-options": "DENY", "x-amz-server-side-encryption": "AES256", "x-amz-replication-status": "COMPLETED", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "cross-origin-opener-policy": "same-origin", "x-cache": "Miss from cloudfront", "x-content-type-options": "nosniff", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT"}, "url": "https://deno.land/std@0.204.0/collections/sample.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 570319179}}