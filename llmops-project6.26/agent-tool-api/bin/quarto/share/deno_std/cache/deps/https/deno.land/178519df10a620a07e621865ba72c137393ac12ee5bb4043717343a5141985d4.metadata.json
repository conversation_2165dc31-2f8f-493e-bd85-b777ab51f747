{"headers": {"x-frame-options": "DENY", "x-amz-replication-status": "COMPLETED", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "date": "Wed, 24 Apr 2024 06:53:29 GMT", "content-type": "application/typescript; charset=utf-8", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "access-control-allow-origin": "*", "x-amz-cf-pop": "IAD61-P2", "x-amz-version-id": "IcWGqxQF5aU5gEVdP7WZn_PHZ1rkGyNa", "cross-origin-resource-policy": "same-origin", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "server-timing": "fetchSource;dur=66", "age": "658100", "via": "http/2 edgeproxy-h", "x-amz-cf-id": "eS1r3J0g_q9OfgIUs3VXZawXT6UNbF0ZftAaAUfe-FgFTyftPkeCSQ==", "x-amz-server-side-encryption": "AES256", "cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "content-length": "728", "cache-control": "public, max-age=31536000, immutable", "referrer-policy": "strict-origin-when-cross-origin"}, "url": "https://deno.land/std@0.204.0/io/multi_reader.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 439516854}}