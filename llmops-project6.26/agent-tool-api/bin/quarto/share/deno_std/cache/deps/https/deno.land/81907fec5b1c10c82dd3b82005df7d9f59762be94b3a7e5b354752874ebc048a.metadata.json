{"headers": {"access-control-allow-origin": "*", "cross-origin-opener-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-version-id": "ZbwQ156LD0wY2_ynqia1L.bbT2eu4_x3", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin", "content-length": "2712", "content-type": "application/typescript; charset=utf-8", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=24", "cross-origin-embedder-policy": "same-origin", "x-cache": "Hit from cloudfront", "x-amz-replication-status": "COMPLETED", "accept-ranges": "bytes", "age": "656263", "x-frame-options": "DENY", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "date": "Wed, 24 Apr 2024 07:24:08 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-cf-id": "GsLRk7_SPFFIHbKN8fT-2XzF1n4kOdxnSopfVSUXnIBKaohtZs4i3A==", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "etag": "\"ac58620d54da5905772938815eba049f\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-server-side-encryption": "AES256", "cache-control": "public, max-age=31536000, immutable"}, "url": "https://deno.land/std@0.204.0/yaml/_type/timestamp.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 950987987}}