{"headers": {"x-content-type-options": "nosniff", "via": "http/2 edgeproxy-h", "server-timing": "fetchSource;dur=22", "age": "639406", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "yDdmhY51W9pPT6w3zLUGYOlgaI9r8q7Iin_tft7ogtoiPPWQMnG0ZQ==", "access-control-allow-origin": "*", "content-length": "1368", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-pop": "IAD61-P2", "server": "deno/gcp-us-east4", "x-amz-replication-status": "COMPLETED", "date": "Wed, 24 Apr 2024 12:05:02 GMT", "etag": "\"d3250184307738884f3e50efaddeaccb\"", "cross-origin-embedder-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "cache-control": "public, max-age=31536000, immutable", "vary": "Accept-Encoding, Origin", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "accept-ranges": "bytes", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "4_NYejSL7ZqsjBWWCutAXRqRTOCKQqLD", "cross-origin-opener-policy": "same-origin", "x-cache": "Hit from cloudfront"}, "url": "https://deno.land/std@0.204.0/datetime/constants.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": *********}}