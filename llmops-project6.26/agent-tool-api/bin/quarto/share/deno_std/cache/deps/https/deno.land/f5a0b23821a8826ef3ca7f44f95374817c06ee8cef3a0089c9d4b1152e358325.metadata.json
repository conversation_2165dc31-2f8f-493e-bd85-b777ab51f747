{"headers": {"cross-origin-opener-policy": "same-origin", "access-control-allow-origin": "*", "date": "Wed, 24 Apr 2024 07:12:20 GMT", "x-frame-options": "DENY", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cross-origin-embedder-policy": "same-origin", "x-amz-cf-id": "3cZsoAtLckrYivi2vIPliI2oiUTebeb9k_FSm98ziXTNAQu_aEG5Sg==", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "j0T8.7TX_xu0zqWve1VQOMozJOdJnVZd", "content-length": "621", "server": "deno/gcp-us-east4", "etag": "\"ed3a6ac9e749068a1fac56dc5809165c\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "age": "656970", "content-type": "application/typescript; charset=utf-8", "server-timing": "fetchSource;dur=14", "vary": "Accept-Encoding, Origin", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "x-cache": "Hit from cloudfront", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "via": "http/2 edgeproxy-h", "accept-ranges": "bytes"}, "url": "https://deno.land/std@0.204.0/crypto/_fnv/mod.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 456325755}}