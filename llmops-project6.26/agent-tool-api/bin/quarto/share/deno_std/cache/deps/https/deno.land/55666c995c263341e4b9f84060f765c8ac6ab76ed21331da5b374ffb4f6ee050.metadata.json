{"headers": {"access-control-allow-origin": "*", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server-timing": "fetchSource;dur=42", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "x-amz-cf-id": "7Eaz0xell0Qgg9LKJKJm1nyp0sRpNToJJVGYC4C-rDUE3J74iJKOdg==", "x-amz-version-id": "LsxdIC5Oo3Brbi0kLFvOgm31gLSx1DCn", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "content-type": "application/typescript; charset=utf-8", "date": "Wed, 01 May 2024 21:41:49 GMT", "etag": "\"1a291f63941e6121174570c861d9a04a\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "content-length": "7873", "x-frame-options": "DENY", "cross-origin-opener-policy": "same-origin", "x-amz-server-side-encryption": "AES256", "server": "deno/gcp-us-east4", "x-amz-cf-pop": "IAD61-P2", "cross-origin-embedder-policy": "same-origin", "x-cache": "Miss from cloudfront", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable"}, "url": "https://deno.land/std@0.204.0/csv/stringify.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 535708570}}