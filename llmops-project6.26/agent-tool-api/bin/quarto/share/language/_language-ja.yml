toc-title-document: "目次"
toc-title-website: "目次"

related-formats-title: "その他のフォーマット"
related-notebooks-title: "Notebooks"
source-notebooks-prefix: "ソース"
other-links-title: "その他のリンク"
code-links-title: "コードリンクス"
launch-dev-container-title: "Devコンテナを起動する"
launch-binder-title: "ランチ Binder"

article-notebook-label: "記事ノート"
notebook-preview-download: "ノートブックをダウンロード"
notebook-preview-download-src: "ソースコードをダウンロードする"
notebook-preview-back: "記事に戻る"
manuscript-meca-bundle: "MECAアーカイブ"

section-title-abstract: "概要"
section-title-appendices: "付録"
section-title-footnotes: "脚注"
section-title-references: "参考文献"
section-title-reuse: "ライセンス"
section-title-copyright: "著作権"
section-title-citation: "引用"

appendix-attribution-cite-as: "引用方法"
appendix-attribution-bibtex: "BibTeX"

title-block-author-single: "作者"
title-block-author-plural: "作者"
title-block-affiliation-single: "所属"
title-block-affiliation-plural: "所属"
title-block-published: "公開"
title-block-modified: "更新日"
title-block-keywords: "キーワード"

callout-tip-title: "ヒント"
callout-note-title: "ノート"
callout-warning-title: "警告"
callout-important-title: "重要"
callout-caution-title: "注意"

code-summary: "コード"

code-line: "ライン"
code-lines: "ライン"

code-tools-menu-caption: "コード"
code-tools-show-all-code: "すべてのコードを表示"
code-tools-hide-all-code: "すべてのコードを非表示"
code-tools-view-source: "ソースコードを表示"
code-tools-source-code: "ソースコード"

copy-button-tooltip: "コピー"
copy-button-tooltip-success: "コピーしました"

repo-action-links-edit: "編集"
repo-action-links-source: "ソースコード"
repo-action-links-issue: "問題を報告"

back-to-top: "トップに戻る"

search-no-results-text: "一致なし"
search-matching-documents-text: "一致した文書"
search-copy-link-title: "検索へのリンクをコピー"
search-hide-matches-text: "追加の検索結果を非表示"
search-more-match-text: "追加の検索結果"
search-more-matches-text: "追加の検索結果"
search-clear-button-title: "消去"
search-text-placeholder: ""
search-detached-cancel-button-title: "取消"
search-submit-button-title: "検索"
search-label: "サーチ"

toggle-section: "セクションを切り替え"
toggle-sidebar: "サイドバーを切り替える"
toggle-dark-mode: "ダークモードの切り替え"
toggle-reader-mode: "リーダーモードの切り替え"
toggle-navigation: "ナビゲーションを切り替える"

crossref-fig-title: "図"
crossref-tbl-title: "表"
crossref-lst-title: "コード"
crossref-thm-title: "定理"
crossref-lem-title: "補題"
crossref-cor-title: "系"
crossref-prp-title: "命題"
crossref-cnj-title: "予想"
crossref-def-title: "定義"
crossref-exm-title: "例"
crossref-exr-title: "練習"
crossref-ch-prefix: "チャプター"
crossref-apx-prefix: "付録"
crossref-sec-prefix: "セクション"
crossref-eq-prefix: "式"
crossref-lof-title: "図一覧"
crossref-lot-title: "表一覧"
crossref-lol-title: "コード一覧"

environment-proof-title: "証明"
environment-remark-title: "注釈"
environment-solution-title: "解答"

listing-page-order-by: "並び替え"
listing-page-order-by-default: "デフォルト"
listing-page-order-by-date-asc: "日付（昇順）"
listing-page-order-by-date-desc: "日付（降順）"
listing-page-order-by-number-desc: "ページ番号（降順）"
listing-page-order-by-number-asc: "ページ番号（昇順）"
listing-page-field-date: "日付"
listing-page-field-title: "題名"
listing-page-field-description: "説明"
listing-page-field-author: "作者"
listing-page-field-filename: "ファイル名"
listing-page-field-filemodified: "更新日"
listing-page-field-subtitle: "副題"
listing-page-field-readingtime: "読了目安"
listing-page-field-categories: "分類"
listing-page-minutes-compact: "{0} 分"
listing-page-category-all: "すべて"
listing-page-no-matches: "一致なし"
listing-page-field-wordcount: "単語数"
listing-page-words: "{0}語"
