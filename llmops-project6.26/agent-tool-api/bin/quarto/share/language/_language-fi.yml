toc-title-document: "Sisällysluettelo"
toc-title-website: "Tällä sivulla"

related-formats-title: "Muut muodot"
related-notebooks-title: "Notebooks"
source-notebooks-prefix: "<PERSON><PERSON><PERSON><PERSON>"
other-links-title: "Muut linkit"
code-links-title: "Koodilinkit"
launch-dev-container-title: "Käynnistä Dev Container"
launch-binder-title: "Käynnistä Binder"

article-notebook-label: "Artikkelin muistiinpanovihko"
notebook-preview-download: "Lataa muistiinpanovihko"
notebook-preview-download-src: "Lataa lähdekoodi"
notebook-preview-back: "Takaisin artikkeliin"
manuscript-meca-bundle: "MECA-arkisto"

section-title-abstract: "Tiivistelmä"
section-title-appendices: "Liitteet"
section-title-footnotes: "Alaviitteet"
section-title-references: "Lähteet"
section-title-reuse: "Uudelleenkäyttö"
section-title-copyright: "Te<PERSON>jänoikeus"
section-title-citation: "Viittaus"

appendix-attribution-cite-as: "Viitatkaa tähän teokseen seuraavasti:"
appendix-attribution-bibtex: "BibTeX-viittaus:"

title-block-author-single: "Tekijä"
title-block-author-plural: "Tekijät"
title-block-affiliation-single: "Affiliaatio"
title-block-affiliation-plural: "Affiliaatiot"
title-block-published: "Julkaistu"
title-block-modified: "Muokattu"
title-block-keywords: "Avainsanat"

callout-tip-title: "Vihje"
callout-note-title: "Huomautus"
callout-warning-title: "Varoitus"
callout-important-title: "Tärkeää"
callout-caution-title: "Vaara"

code-summary: "Koodi"

code-line: "Linja"
code-lines: "Linjat"

code-tools-menu-caption: "Koodi"
code-tools-show-all-code: "Näytä kaikki koodi"
code-tools-hide-all-code: "Piilota kaikki koodi"
code-tools-view-source: "Näytä lähdekoodi"
code-tools-source-code: "Lähdekoodi"

copy-button-tooltip: "Kopioi leikepöydälle"
copy-button-tooltip-success: "Kopioitu!"

repo-action-links-edit: "Muokkaa sivua"
repo-action-links-source: "Näytä lähdekoodi"
repo-action-links-issue: "Ilmoita ongelmasta"

back-to-top: "Takaisin alkuun"

search-no-results-text: "Ei tuloksia"
search-matching-documents-text: "tulokset"
search-copy-link-title: "Kopioi linkki hakuun"
search-hide-matches-text: "Piilota loput tulokset"
search-more-match-text: "tulos tässä dokumentissa"
search-more-matches-text: "tulosta tässä dokumentissa"
search-clear-button-title: "Tyhjennä"
search-text-placeholder: ""
search-detached-cancel-button-title: "Peruuta"
search-submit-button-title: "Lähetä"
search-label: "Hae"

toggle-section: "Vaihda osio"
toggle-sidebar: "Vaihda sivupalkki"
toggle-dark-mode: "Tumma tila päälle/pois"
toggle-reader-mode: "Vaihda lukijatilaa"
toggle-navigation: "Vaihda navigointi"

crossref-fig-title: "Kuva"
crossref-tbl-title: "Taulukko"
crossref-lst-title: "Listaus"
crossref-thm-title: "Lause"
crossref-lem-title: "Apulause"
crossref-cor-title: "Korollaari"
crossref-prp-title: "Väittämä"
crossref-cnj-title: "Konjektuuri"
crossref-def-title: "Määritelmä"
crossref-exm-title: "Esimerkki"
crossref-exr-title: "Harjoitus"
crossref-ch-prefix: "Luku"
crossref-sec-prefix: "Alaluku"
crossref-eq-prefix: "Yhtälö"
crossref-lof-title: "Kuvien luettelo"
crossref-lot-title: "Taulukoiden luettelo"
crossref-lol-title: "Listausten luettelo"
crossref-apx-prefix: "Liite"

environment-proof-title: "Todistus"
environment-remark-title: "Huomautus"
environment-solution-title: "Ratkaisu"

listing-page-order-by: "Järjestys:"
listing-page-order-by-default: "Oletusarvo"
listing-page-order-by-date-asc: "Vanhin"
listing-page-order-by-date-desc: "Uusin"
listing-page-order-by-number-desc: "Laskeva"
listing-page-order-by-number-asc: "Nouseva"
listing-page-field-date: "Päiväys"
listing-page-field-title: "Otsikko"
listing-page-field-description: "Kuvaus"
listing-page-field-author: "Tekijä"
listing-page-field-filename: "Tiedostonimi"
listing-page-field-filemodified: "Muokattu"
listing-page-field-subtitle: "Alaotsikko"
listing-page-field-readingtime: "Lukuaika"
listing-page-field-categories: "Luokat"
listing-page-minutes-compact: "{0} min"
listing-page-category-all: "Kaikki"
listing-page-no-matches: "Ei hakutuloksia"
listing-page-field-wordcount: "Sanamäärä"
listing-page-words: "{0} sanaa"
