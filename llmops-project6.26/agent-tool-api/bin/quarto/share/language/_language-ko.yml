toc-title-document: "목차"
toc-title-website: "목차"

related-formats-title: "기타 형식"
related-notebooks-title: "Notebooks"
source-notebooks-prefix: "원천"
other-links-title: "기타 링크"
code-links-title: "코드 링크"
launch-dev-container-title: "Dev 컨테이너 실행"
launch-binder-title: "랜치 Binder"

article-notebook-label: "기사 노트북"
notebook-preview-download: "노트북 다운로드"
notebook-preview-download-src: "소스 다운로드"
notebook-preview-back: "기사로 돌아가기"
manuscript-meca-bundle: "MECA 아카이브"

section-title-abstract: "초록"
section-title-appendices: "부록"
section-title-footnotes: "각주"
section-title-references: "참고문헌"
section-title-reuse: "라이센스"
section-title-copyright: "저작권"
section-title-citation: "인용"

appendix-attribution-cite-as: "인용방법"
appendix-attribution-bibtex: "BibTeX 인용:"

title-block-author-single: "저자"
title-block-author-plural: "저자"
title-block-affiliation-single: "소속"
title-block-affiliation-plural: "소속"
title-block-published: "공개"
title-block-keywords: "키워드"

callout-tip-title: "힌트"
callout-note-title: "노트"
callout-warning-title: "경고"
callout-important-title: "중요"
callout-caution-title: "주의"

code-summary: "코드"

code-line: "선"
code-lines: "윤곽"

code-tools-menu-caption: "코드"
code-tools-show-all-code: "전체 코드 표시"
code-tools-hide-all-code: "전체 코드 숨기기"
code-tools-view-source: "소스 코드 표시"
code-tools-source-code: "소스 코드"

copy-button-tooltip: "클립보드 복사"
copy-button-tooltip-success: "복사완료!"

repo-action-links-edit: "편집"
repo-action-links-source: "소스코드 보기"
repo-action-links-issue: "이슈 보고"

back-to-top: "맨 위로"

search-no-results-text: "일치 없음"
search-matching-documents-text: "일치된 문서"
search-copy-link-title: "검색 링크 복사"
search-hide-matches-text: "추가 검색 결과 숨기기"
search-more-match-text: "추가 검색결과"
search-more-matches-text: "추가 검색결과"
search-clear-button-title: "제거"
search-text-placeholder: ""
search-detached-cancel-button-title: "취소"
search-submit-button-title: "검색"
search-label: "검색"

toggle-section: "토글 섹션"
toggle-sidebar: "사이드바 전환"
toggle-dark-mode: "다크 모드 전환"
toggle-reader-mode: "리더 모드 전환"
toggle-navigation: "탐색 전환"

crossref-fig-title: "그림"
crossref-tbl-title: "표"
crossref-lst-title: "목록"
crossref-thm-title: "정리"
crossref-lem-title: "보조정리"
crossref-cor-title: "따름정리"
crossref-prp-title: "명제"
crossref-cnj-title: "추측"
crossref-def-title: "정의"
crossref-exm-title: "보기"
crossref-exr-title: "예제"
crossref-ch-prefix: "장"
crossref-apx-prefix: "부록"
crossref-sec-prefix: "섹션"
crossref-eq-prefix: "방정식"
crossref-lof-title: "그림 목록"
crossref-lot-title: "표 목록"
crossref-lol-title: "코드 목록"

environment-proof-title: "증명"
environment-remark-title: "주석"
environment-solution-title: "해답"

listing-page-order-by: "정렬"
listing-page-order-by-default: "디폴트"
listing-page-order-by-date-asc: "날짜(오름차순)"
listing-page-order-by-date-desc: "날짜(내림차순)"
listing-page-order-by-number-desc: "페이지 번호(내림차순)"
listing-page-order-by-number-asc: "페이지 번호(오름차순)"
listing-page-field-date: "날짜"
listing-page-field-title: "제목"
listing-page-field-description: "설명"
listing-page-field-author: "저자"
listing-page-field-filename: "파일명"
listing-page-field-filemodified: "갱신일"
listing-page-field-subtitle: "부제목"
listing-page-field-readingtime: "읽기 시간"
listing-page-field-categories: "분류"
listing-page-minutes-compact: "{0} 분"
listing-page-category-all: "전체"
listing-page-no-matches: "일치 없음"
listing-page-field-wordcount: "단어 수"
listing-page-words: "{0} 단어"
