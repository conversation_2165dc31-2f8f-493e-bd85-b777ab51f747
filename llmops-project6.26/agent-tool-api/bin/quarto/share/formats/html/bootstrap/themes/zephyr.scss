/*-- scss:defaults --*/

$theme: "zephyr" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #6c757d !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$blue:    #3459e6 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #d63384 !default;
$red:     #da292e !default;
$orange:  #f8765f !default;
$yellow:  #f4bd61 !default;
$green:   #2fb380 !default;
$teal:    #20c997 !default;
$cyan:    #287bb5 !default;

$primary:       $blue !default;
$secondary:     $white !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-900 !default;

$min-contrast-ratio:   1.65 !default;

$enable-shadows:              true !default;

// Body

$body-color:                $gray-700 !default;

$headings-color:              $gray-900 !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:      Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;
$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`
$font-size-sm:                $font-size-base * .875 !default;

// Components

$box-shadow:                  0 1px 2px rgba($black, .05) !default;
$box-shadow-lg:               0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px 0 rgba(0, 0, 0, .06) !default;

// Tables

$table-cell-padding-y:        1rem !default;
$table-cell-padding-x:        1rem !default;
$table-cell-padding-y-sm:     .5rem !default;
$table-cell-padding-x-sm:     .5rem !default;

$table-th-font-weight:        500 !default;

// Buttons + Forms

$input-btn-padding-y:         .5rem !default;
$input-btn-padding-x:         1rem !default;
$input-btn-font-size:         $font-size-sm !default;

// Buttons

$btn-font-weight:             500 !default;
$btn-box-shadow:              $box-shadow !default;
$btn-focus-box-shadow:        $box-shadow !default;
$btn-active-box-shadow:       $box-shadow !default;

// Forms

$form-label-font-weight:                500 !default;

$input-box-shadow:                      $box-shadow !default;

$input-group-addon-bg:                  $gray-100 !default;

// Navs

$nav-link-color:                    $body-color !default;
$nav-link-hover-color:              $body-color !default;

$nav-tabs-border-radius:            0 !default;
$nav-tabs-link-active-color:        $primary !default;

// Navbar

$navbar-padding-y:                  .85rem !default;

$navbar-nav-link-padding-x:         .75rem !default;

// Dropdowns

$dropdown-font-size:                $font-size-sm !default;
$dropdown-border-color:             $gray-300 !default;
$dropdown-divider-bg:               $gray-200 !default;

$dropdown-link-hover-color:         $white !default;
$dropdown-link-hover-bg:            $primary !default;

$dropdown-item-padding-y:           .5rem !default;
$dropdown-item-padding-x:           1rem !default;

// Pagination

$pagination-padding-y:              .5rem !default;
$pagination-padding-x:              1rem !default;

$pagination-color:                  $gray-700 !default;

$pagination-focus-color:            $pagination-color !default;

$pagination-hover-color:            $pagination-color !default;
$pagination-hover-bg:               $gray-100 !default;

// Cards
$card-spacer-x:                     1.5rem !default;
$card-cap-padding-y:                1rem !default;
$card-cap-padding-x:                1.5rem !default;

// Toasts

$toast-header-color:                $headings-color !default;

// Modals

$modal-content-border-color:        $gray-300 !default;
$modal-header-border-width:         0 !default;

// List group

$list-group-item-padding-y:         1rem !default;
$list-group-item-padding-x:         1.5rem !default;

// Breadcrumbs

$breadcrumb-padding-x:              1rem !default;

$breadcrumb-divider:                quote(">") !default;



/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

// Navbar

.navbar {
  font-size: $font-size-sm;
  font-weight: 500;

  .nav-item {
    margin-right: .5rem;
    margin-left: .5rem;
  }

  .navbar-nav {
    .nav-link {
      border-radius: $border-radius;
    }
  }
}

.navbar-dark {
  .navbar-nav {
    .nav-link {
      &:hover {
        background-color: rgba(255, 255, 255, .1);
      }

      &.active {
        background-color: rgba(0, 0, 0, .5);
      }
    }
  }
}

.navbar-light {
  .navbar-nav {
    .nav-link {
      &:hover {
        background-color: rgba(0, 0, 0, .03);
      }

      &.active {
        background-color: rgba(0, 0, 0, .05);
      }
    }
  }
}

.navbar-nav {
  --bs-nav-link-padding-x: .5rem;
}

// Buttons

.btn-secondary,
.btn-light,
.btn-outline-secondary,
.btn-outline-light {
  color: $gray-900;

  &:disabled,
  &.disabled {
    border: 1px solid shade-color($secondary, 10%);
  }
}

.btn-secondary,
.btn-outline-secondary {
  border-color: shade-color($secondary, 10%);

  &:hover,
  &:active {
    background-color: shade-color($secondary, 10%);
    border-color: shade-color($secondary, 10%);
  }
}

.btn-light,
.btn-outline-light {
  border-color: shade-color($light, 10%);

  &:hover,
  &:active {
    background-color: shade-color($light, 10%);
    border-color: shade-color($light, 10%);
  }
}

// Tables

.table {
  font-size: $font-size-sm;
  box-shadow: $box-shadow-lg;
}

thead th {
  font-size: $font-size-sm;
  text-transform: uppercase;
}

// Forms

.input-group-text {
  box-shadow: $box-shadow;
}

// Navs

.nav-tabs {
  font-weight: 500;

  .nav-link {
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-width: 0 0 1px;
  }

  .nav-link.active,
  .nav-item.show .nav-link {
    box-shadow: inset 0 -2px 0 $primary;
  }
}

.nav-pills {
  font-weight: 500;
}

.pagination {
  font-size: $font-size-sm;
  font-weight: 500;

  .page-link {
    box-shadow: $box-shadow;
  }
}

.breadcrumb {
  font-size: $font-size-sm;
  font-weight: 500;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  &-item {
    padding: 1rem .5rem 1rem 0;
  }
}

.breadcrumb-item + .breadcrumb-item::before {
  padding-right: 1rem;
}

// Indicators

.alert {
  .btn-close {
    color: inherit;
  }
}

.badge {
  &.bg-secondary,
  &.bg-light {
    color: $gray-900;
  }
}

// Containers

.list-group-item,
.card {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    color: inherit;
  }
}

.list-group {
  box-shadow: $box-shadow-lg;
}

.card {
  box-shadow: $box-shadow-lg;
}

.modal-footer {
  background-color: $gray-100;
}

.modal-content {
  box-shadow: $box-shadow-lg;
}


