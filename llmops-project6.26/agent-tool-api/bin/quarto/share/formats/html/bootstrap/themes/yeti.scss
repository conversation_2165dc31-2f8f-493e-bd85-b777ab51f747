/*-- scss:defaults --*/

$theme: "yeti" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #eee !default;
$gray-300: #dee2e6 !default;
$gray-400: #ccc !default;
$gray-500: #adb5bd !default;
$gray-600: #888 !default;
$gray-700: #495057 !default;
$gray-800: #333 !default;
$gray-900: #222 !default;
$black:    #000 !default;

$blue:    #008cba !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #f04124 !default;
$orange:  #fd7e14 !default;
$yellow:  #e99002 !default;
$green:   #43ac6a !default;
$teal:    #20c997 !default;
$cyan:    #5bc0de !default;

$primary:       $blue !default;
$secondary:     $gray-200 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-200 !default;
$dark:          $gray-800 !default;

$min-contrast-ratio:   1.9 !default;

// Components

$border-radius:               0 !default;
$border-radius-lg:            0 !default;
$border-radius-sm:            0 !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:      "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;
$headings-font-weight:        300 !default;

// Tables

$table-bg-scale:              0% !default;

// Buttons

$input-btn-padding-x:         .5rem !default;
$btn-font-weight:             300 !default;

// Dropdowns

$dropdown-border-color:             rgba($black, .1) !default;
$dropdown-divider-bg:               rgba($black, .1) !default;

// Navbar

$navbar-dark-color:                 rgba($white, .7) !default;
$navbar-dark-hover-color:           $white !default;

// Pagination

$pagination-color:                  $gray-600 !default;
// $pagination-border-color:           $nav-tabs-border-color !default;
$pagination-active-border-color:    darken($primary, 5%) !default;
$pagination-disabled-bg:            transparent !default;
$pagination-disabled-color:         $gray-400 !default;

// Cards

$card-inner-border-radius:          0 !default;

// Badges

$badge-font-weight:                 300 !default;
$badge-padding-x:                   1rem !default;

// Alerts

$alert-link-font-weight:            400 !default;

// Breadcrumbs

$breadcrumb-padding-y:              .375rem !default;
$breadcrumb-padding-x:              .75rem !default;
$breadcrumb-border-radius:          3px !default;

// Close

$btn-close-color:            $gray-600 !default;
$btn-close-opacity:          .6 !default;
$btn-close-hover-opacity:    1 !default;



/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

// Navbar

.navbar {
  font-weight: $headings-font-weight;
}

.bg-primary {
  .dropdown-menu {
    background-color: $primary;

    .dropdown-item,
    .dropdown-item:focus {
      color: $navbar-dark-color;
    }

    .dropdown-item.active,
    .dropdown-item:hover,
    .dropdown-item:focus {
      color: $white;
      background-color: shade-color($primary, 10%);
    }
  }
}

.bg-dark {
  background-color: $gray-800 !important;

  .dropdown-menu {
    background-color: $gray-800;

    .dropdown-item,
    .dropdown-item:focus {
      color: $navbar-dark-color;
    }

    .dropdown-item.active,
    .dropdown-item:hover,
    .dropdown-item:focus  {
      color: $white;
      background-color: shade-color($gray-800, 10%);
    }
  }
}

.bg-light {
  .dropdown-menu {
    background-color: $gray-200;

    .dropdown-item,
    .dropdown-item:focus {
      color: $navbar-light-color;
    }

    .dropdown-item.active,
    .dropdown-item:hover,
    .dropdown-item:focus  {
      color: $navbar-light-hover-color;
      background-color: tint-color($gray-200, 10%);
    }
  }
}

// Buttons

.btn {
  &-primary {
    border-color: shade-color($primary, 10%);
  }

  &-default {
    border-color: shade-color($secondary, 10%);
  }

  &-secondary {
    border-color: shade-color($secondary, 10%);
  }

  &-success {
    border-color: shade-color($success, 10%);
  }

  &-info {
    color: $white;
    border-color: shade-color($info, 10%);
  }

  &-danger {
    border-color: shade-color($danger, 10%);
  }

  &-warning {
    color: $white;
    border-color: shade-color($warning, 10%);
  }

  &-light {
    border-color: shade-color($light, 10%);
  }

  &-dark {
    border-color: shade-color($dark, 10%);
  }
}

.btn-outline-secondary {
  color: var(--bs-primary-color);
}

.btn-group {
  .dropdown-menu {
    border-top-width: 0;
  }

  .dropdown-toggle {
    &.btn-primary ~ .dropdown-menu {
      background-color: $primary;
      border-color: shade-color($primary, 10%);

      .dropdown-item {
        color: $white;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($primary, 16%);
      }
    }

    &.btn-secondary ~ .dropdown-menu {
      background-color: $secondary;
      border-color: shade-color($secondary, 10%);

      .dropdown-item {
        color: $body-color;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($secondary, 16%);
      }
    }

    &.btn-success ~ .dropdown-menu {
      background-color: $success;
      border-color: shade-color($success, 10%);

      .dropdown-item {
        color: $white;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($success, 16%);
      }
    }

    &.btn-info ~ .dropdown-menu {
      background-color: $info;
      border-color: shade-color($info, 10%);

      .dropdown-item {
        color: $white;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($info, 16%);
      }
    }

    &.btn-warning ~ .dropdown-menu {
      background-color: $warning;
      border-color: shade-color($warning, 10%);

      .dropdown-item {
        color: $white;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($warning, 16%);
      }
    }

    &.btn-danger ~ .dropdown-menu {
      background-color: $danger;
      border-color: shade-color($danger, 10%);

      .dropdown-item {
        color: $white;
      }

      .dropdown-item:hover,
      .dropdown-item:focus {
        background-color: shade-color($danger, 16%);
      }
    }
  }
}

// Typography

.text-secondary {
  color: $gray-700 !important;
}

.blockquote-footer {
  color: $gray-600;
}

// Tables

table {
  font-size: $font-size-sm;
}

// Forms

label,
.control-label,
.help-block,
.checkbox,
.radio,
.form-control-feedback {
  font-size: $font-size-sm;
}

// Navs

.dropdown-item {
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: $font-size-sm;
  font-weight: 300;
}

.nav-pills {
  .active {
    border: 1px solid shade-color($primary, 10%);
  }
}

.breadcrumb {
  font-size: $font-size-sm;
  font-weight: 300;
  text-transform: uppercase;
  border: 1px solid $dropdown-border-color;
}

.pagination {
  font-weight: 300;

  .page-link {
    margin-right: .1em;
    margin-left: .1em;
    border-color: transparent;
    border-radius: 3px;

    &:hover,
    &:focus {
      text-decoration: none;
    }
  }

  .page-item.disabled {
    .page-link {
      border-color: transparent;
    }
  }
}

.pagination,
.pagination-lg,
.pagination-sm {
  .page-item:first-child,
  .page-item:last-child {
    .page-link {
      border-radius: 3px;
    }
  }
}

@include color-mode(dark) {
  .pagination {
    --bs-pagination-disabled-color: #{$gray-700};
  }
}

.list-group {
  font-size: $font-size-sm;
  font-weight: 300;
}

.list-group-item.disabled,
.list-group-item:disabled {
  background-color: var(--bs-secondary-bg);
}

// Indicators

.alert {
  font-size: $font-size-sm;
  font-weight: 300;

  .alert-link {
    text-decoration: underline;
  }
}

.badge {
  &.bg-secondary,
  &.bg-light {
    color: $dark;
  }
}

// Progress bars

.progress[value] {
  height: 22px;
  padding: 2px;
  background-color: #f6f6f6;
  border: 1px solid #ccc;
  @include box-shadow(none);
}

// Popovers

.popover-header {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}


