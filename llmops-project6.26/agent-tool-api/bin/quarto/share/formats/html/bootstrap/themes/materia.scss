/*-- scss:defaults --*/

$theme: "materia" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #eee !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #bbb !default;
$gray-600: #666 !default;
$gray-700: #444 !default;
$gray-800: #222 !default;
$gray-900: #212121 !default;
$black:    #000 !default;

$blue:    #2196f3 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #e51c23 !default;
$orange:  #fd7e14 !default;
$yellow:  #ff9800 !default;
$green:   #4caf50 !default;
$teal:    #20c997 !default;
$cyan:    #9c27b0 !default;

$primary:       $blue !default;
$secondary:     $white !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$min-contrast-ratio:   2.15 !default;

$enable-gradients: true !default;

// Body

$body-color:    $gray-700 !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:    Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif !default;
$font-size-base:            1rem !default;
$font-weight-base:          400 !default;

// Buttons

$input-btn-padding-y:       .8rem !default;
$input-btn-padding-x:       1rem !default;

// Forms

$input-padding-y:                1rem !default;
$input-padding-x:                0 !default;
$input-padding-y-sm:             0 !default;
$input-padding-x-sm:             0 !default;
$input-padding-y-lg:             ($font-size-base * 1.25) !default;
$input-padding-x-lg:             0 !default;
$input-bg:                       transparent !default;
$input-disabled-bg:              transparent !default;
$input-color:                    $gray-600 !default;
$input-border-color:             transparent !default;
$input-border-width:             0 !default;
$input-border-radius:            0 !default;
$input-border-radius-lg:         0 !default;
$input-border-radius-sm:         0 !default;
$input-placeholder-color:        rgba(0, 0, 0, .4) !default;
$input-group-addon-bg:           transparent !default;

// Navs

$nav-link-disabled-color:       $gray-500 !default;
$nav-tabs-border-color:         transparent !default;

// Navbar

$navbar-padding-y:                  1rem !default;
$navbar-dark-color:                 rgba($white, .75) !default;
$navbar-dark-hover-color:           $white !default;

// Cards

$card-border-width:                 0 !default;
$card-border-color:                 transparent !default;

// Tooltips

$tooltip-bg:                        $gray-700 !default;

// Modals

$modal-content-border-color:        transparent !default;

// Progress bars

$progress-height:               .375rem !default;
$progress-border-radius:        0 !default;

// Close

$btn-close-color:            $white !default;
$btn-close-opacity:          .6 !default;
$btn-close-hover-opacity:    1 !default;


// Box shadows
$enable-shadows: true !default;
$btn-box-shadow: 0 1px 4px mix($white, $black, 60%) !default;
$btn-active-box-shadow: 2px 2px 4px mix($white, $black, 60%) !default;
$btn-focus-box-shadow: 0 0 0 2px mix($white, $black, 40%) !default;
$input-box-shadow: inset 0 -1px 0 mix($white, $black, 86.7%) !default;
$input-focus-box-shadow: inset 0 -2px 0 $primary  !default;


/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

// Mixins

@mixin ripple($color) {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    margin-left: 0;
    pointer-events: none;
    content: "";
    background-position: 50%;
    background-size: 1000% 1000%;
    border: none;
    opacity: 0;
    transition: background .5s, opacity 1s;
    @include gradient-radial($color 10%, transparent 10.01%);
  }

  &:active::before {
    background-size: 0 0;
    opacity: .2;
    transition: none;
  }

  &.disabled,
  &[disabled] {
    &::before {
      display: none;
    }
  }
}

@mixin btn($class,$bg,$color) {
  .btn-#{$class} {
    &:focus {
      background-color: $bg;
      box-shadow: $btn-focus-box-shadow;
    }

    &:hover,
    &:active:hover {
      background-color: shade-color($bg, 12%);
    }

    &:active {
      box-shadow: $btn-active-box-shadow;
    }

    @include ripple($color);
  }

  .btn-outline-#{$class} {
    @include ripple($color);
  }
}

.dropdown-toggle::after,
.dropup .dropdown-toggle::after,
.dropstart .dropdown-toggle::after,
.dropend .dropdown-toggle::after {
  border-width: 4px;
}

// Navbar

.navbar {
  border: none;
  box-shadow: 0 1px 2px rgba($black, .3);

  &-brand {
    font-size: 24px;
  }

  &-nav .nav-link {
    padding-top: .9rem;
    padding-bottom: .9rem;
  }

  &.bg-dark,
  &.bg-primary {
    input[type="search"],
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    input[type="tel"] {
      color: $white;
      box-shadow: inset 0 -1px 0 rgba($white, .5);

      &:focus {
        box-shadow: inset 0 -2px 0 $white;
      }

      &::placeholder {
        color: rgba($white, .5);
      }
    }
  }
}

// Buttons

@include btn(primary, $primary, $white);
@include btn(secondary, $secondary, $gray-500);
@include btn(success, $success, $white);
@include btn(info, $info, $white);
@include btn(warning, $warning, $white);
@include btn(danger, $danger, $white);
@include btn(dark, $dark, $white);
@include btn(light, $light, $white);

.btn {
  text-transform: uppercase;
  border: none;
  box-shadow: $btn-box-shadow;
  transition: color .4s, background-color .4s, border-color .4s, box-shadow .4s;

  &-link {
    color: $link-color;
    box-shadow: none;

    &:hover,
    &:focus {
      color: $link-hover-color;
      text-decoration: $link-hover-decoration;
      -webkit-text-decoration: $link-hover-decoration;
      -moz-text-decoration: $link-hover-decoration;
      -ms-text-decoration: $link-hover-decoration;
      -o-text-decoration: $link-hover-decoration;
      box-shadow: none;
    }

    &.disabled, // Although btn-link is intended for buttons, which want to look like link, I include here a.disable for the sake of consistency
    &[disabled],
    fieldset[disabled] & {
      &:hover,
      &:active:hover {
        color: $btn-link-disabled-color;
        text-decoration: none;
      }
    }
  }

  &-secondary {
    &.disabled,
    &[disabled],
    fieldset[disabled] & {
      color: rgba($black, .4);
      background-color: rgba($black, .1);
      opacity: 1;

      &:hover,
      &:focus {
        background-color: rgba($black, .1);
      }
    }
  }

  &-outline-secondary {
    color: $gray-300;
    border-color: $gray-200;
  }

  &-warning {
    color: $white;
  }
}

.btn-group {
  .btn + .btn,
  .btn + .btn-group,
  .btn-group + .btn,
  .btn-group + .btn-group {
    margin-left: 0;
  }

  &-vertical {
    > .btn + .btn,
    > .btn + .btn-group,
    > .btn-group + .btn,
    > .btn-group + .btn-group {
      margin-top: 0;
    }
  }

  .btn + .btn,
  .btn + .btn-group > .dropdown-toggle {
    box-shadow: 1px 1px 4px rgba($black, .4);
  }
}

.border-secondary {
  border: 1px solid mix($white, $black, 86.7%) !important;
}

// Typography

body,
input,
button {
  letter-spacing: .1px;
}

p {
  margin: 0 0 1em;
}

.text-secondary {
  color: $gray-500 !important;
}

// Tables

.table-hover {
  > tbody > tr,
  > tbody > tr > th,
  > tbody > tr > td {
    transition: background-color .2s, color .2s;
  }
}

.thead-inverse th {
  color: $white;
  background-color: $primary;
}

// Forms

.col-form-label {
  font-size: 16px;

  &-sm {
    font-size: $font-size-sm;
  }

  &-lg {
    font-size: $font-size-lg;
  }
}

textarea,
textarea.form-control,
input.form-control,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
[type="text"].form-control,
[type="password"].form-control,
[type="email"].form-control,
[type="tel"].form-control,
[contenteditable].form-control {
  box-shadow: $input-box-shadow;
  transition: box-shadow .2s;

  &:focus {
    box-shadow: $input-focus-box-shadow;
  }

  &[disabled],
  &[readonly] {
    border-bottom: 1px dotted mix($white, $black, 86.7%);
    box-shadow: none;
  }

  &[disabled],
  &[disabled]::placeholder {
    color: mix($white, $black, 86.7%);
  }
}

select,
select.form-control {
  padding: .5rem 0;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 4'><path fill='#{$gray-600}' d='M8 0 4 4 0 0z'/></svg>"));
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 8px 4px;
  box-shadow: $input-box-shadow;

  &.input {
    &-sm {
      font-size: $font-size-sm;
    }

    &-lg {
      font-size: $font-size-lg;
    }
  }

  &:focus {
    background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 4'><path fill='#{$gray-900}' d='M8 0 4 4 0 0z'/></svg>"));
    box-shadow: $input-focus-box-shadow;
  }

  &[multiple] {
    background: none;
  }
}

.form-check-input {
  width: 1.25em;
  height: 1.25em;
  margin-top: 1px;
  border: 2px solid $gray-400;

  &:checked[type="radio"] {
    background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$blue}' stroke='#{$white}'/></svg>")), var(--bs-gradient);
    background-size: 1.8em;
  }
}

.form-switch {
  .form-check-input {
    position: relative;
    height: .8em;
    margin-top: .29em;
    background-color: $gray-400;
    background-image: none;
    border: none;

    &:focus {
      box-shadow: none;
    }

    &::before {
      position: absolute;
      top: -.2em;
      left: -.2em;
      width: 1.2em;
      height: 1.2em;
      content: "";
      background-color: $white;
      border-radius: 50%;
      box-shadow: 0 1px 4px rgba($black, .3);
      transition: left .15s ease-in-out;
    }

    &::after {
      position: absolute;
      top: -.2em;
      left: -.2em;
      z-index: -1;
      width: 1.2em;
      height: 1.2em;
      content: "";
      border-radius: 50%;
      box-shadow: 0 0 0 9px rgba($black, .05);
      transition: left .15s ease-in-out, transform .15s ease-in-out, box-shadow .15s ease-in-out;
      transform: scale(0);
    }

    &:hover:not(.disabled),
    &:focus:not(.disabled) {

      &::after {
        transform: scale(.9);
      }
    }

    &:focus:not(.disabled) {
      &::after {
        box-shadow: 0 0 0 9px rgba($black, .1);
      }
    }

    &:checked {
      background-color: rgba($primary, .3);

      &::before {
        left: calc(100% - .8em);
        background-color: rgba($primary, 1);
      }

      &::after {
        left: calc(100% - .8em);
        box-shadow: 0 0 0 9px rgba($primary, .1);
      }

      &:hover:not(.disabled),
      &:focus:not(.disabled) {
        &::after {
          transform: scale(.9);
        }
      }

      &:focus:not(.disabled) {
        &::after {
          box-shadow: 0 0 0 9px rgba($primary, .2);
        }
      }
    }
  }
}

.form-check:not(.form-switch) {
  .form-check-input {
    &:checked[type="checkbox"] {
      background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$white}' stroke-width='2' d='m6 10 3 3 6-6'/></svg>")), var(--bs-gradient);
      background-size: 1.6em;
    }
  }
}

.has-warning {
  input:not([type="checkbox"]),
  .form-control,
  input.form-control[readonly],
  input[type="text"][readonly],
  [type="text"].form-control[readonly],
  input:not([type="checkbox"]):focus,
  .form-control:focus {
    border-bottom: none;
    box-shadow: inset 0 -2px 0 $warning;
  }
}

.has-danger {
  input:not([type="checkbox"]),
  .form-control,
  input.form-control[readonly],
  input[type="text"][readonly],
  [type="text"].form-control[readonly],
  input:not([type="checkbox"]):focus,
  .form-control:focus {
    border-bottom: none;
    box-shadow: inset 0 -2px 0 $danger;
  }
}

.has-success {
  input:not([type="checkbox"]),
  .form-control,
  input.form-control[readonly],
  input[type="text"][readonly],
  [type="text"].form-control[readonly],
  input:not([type="checkbox"]):focus,
  .form-control:focus {
    border-bottom: none;
    box-shadow: inset 0 -2px 0 $success;
  }
}

// Remove the Bootstrap feedback styles for input addons
.input-group-addon {
  .has-warning &,
  .has-danger &,
  .has-success & {
    color: $input-color;
    background-color: $input-group-addon-bg;
    border-color: $input-group-addon-border-color;
  }
}

.form-group-lg {
  select,
  select.form-control {
    line-height: 1.5;
  }
}

// Navs

.nav-tabs {
  .nav-item + .nav-item {
    margin-left: 0;
  }

  .nav-link,
  .nav-link:focus {
    margin-right: 0;
    color: $body-color;
    background-color: transparent;
    border: none;
    box-shadow: inset 0 -1px 0 #ddd;
    transition: color .2s, box-shadow .2s;

    &:hover {
      color: $primary;
      background-color: transparent;
      box-shadow: inset 0 -2px 0 $primary;
    }
  }

  .nav-link.active,
  .nav-link.active:focus {
    color: $primary;
    border: none;
    box-shadow: inset 0 -2px 0 $primary;

    &:hover {
      color: $primary;
      border: none;
    }
  }

  .nav-link.disabled {
    box-shadow: inset 0 -1px 0 #ddd;
  }

  &.nav-justified {
    .nav-link,
    .nav-link:hover,
    .nav-link:focus,
    .nav-link.active,
    .nav-link.active:hover,
    .nav-link.active:focus {
      border: none;
    }
  }

  .dropdown-menu {
    margin-top: 0;
  }
}

.dropdown-menu {
  margin-top: 0;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .3);
}

// Indicators

.alert {
  padding-right: 2.5rem;
  border: none;

  &,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $white;
  }

  @each $color, $value in $theme-colors {
    &-#{$color} {
      @if $enable-gradients {
        background: $value linear-gradient(180deg, mix($body-bg, $value, 15%), $value) repeat-x;
      } @else {
        background-color: $value;
      }
    }
  }

  a:not(.btn),
  .alert-link {
    font-weight: 700;
    color: $white;
  }

  &-secondary,
  &-light {
    &,
    a:not(.btn),
    .alert-link {
      color: $body-color;
    }
  }
}

.badge {
  &.bg-secondary,
  &.bg-light {
    color: $dark;
  }
}

// Containers

.card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, .4);

  &.border-primary,
  &.border-secondary,
  &.border-success,
  &.border-info,
  &.border-warning,
  &.border-danger,
  &.border-light,
  &.border-dark {
    border-width: 1px;
  }
}

.list-group {
  &-item-action.active {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: $white;
    }
  }
}

.modal-content {
  border-radius: .2rem;
  box-shadow: 0 6px 36px rgba(0, 0, 0, .3);
}

.modal,
.toast,
.offcanvas {
  .btn-close {
    background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$black}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>"));
  }
}

.popover {
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .3);
}

.carousel {
  &-caption {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: inherit;
    }
  }
}


