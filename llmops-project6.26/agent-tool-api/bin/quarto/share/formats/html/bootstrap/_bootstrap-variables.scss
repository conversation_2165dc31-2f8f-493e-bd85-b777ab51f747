// Default the theme name
$theme-name: if(variable-exists(theme), $theme, "");

// Colors that must be defined
$blue: #0d6efd !default;
$primary: $blue !default;
$white: #ffffff !default;
$gray-200: #e9ecef !default;
$gray-100: #f8f9fa !default;
$gray-900: #212529 !default;
$link-color: $primary !default;

$link-color: if(
  $link-color == $blue,
  theme-override-value($theme-name, "link-color", $link-color),
  $link-color
);

/* Code Block Formatting */
// Code Block Border Treatment
$code-block-border-left: false !default;
$code-block-border-left-style: solid !default;
$code-block-border-left-size: 3px !default;
$code-block-padding-left: 0.6em !default;

// Code Block Background Treatment
// $code-block-bg, $code-block-bg-padding, $code-block-bg-alpha
$code-block-bg: true !default;
$code-block-bg-padding: 0.4em !default;
$code-block-bg-alpha: -0.35 !default;

// Controls when the code block will switch to a dark
// version of a theme
$code-block-theme-dark-threshhold: 40% !default;

/* Inline Code Formatting */
// $code-bg, $code-color, $code-padding
$code-color: #7d12ba !default;

// Set a default body emphasis color
$code-bg: $gray-100 !default;

// toc variables
$toc-color: $link-color !default;
$toc-font-size: 0.875rem !default;
$toc-active-border: $toc-color !default;
$toc-inactive-border: $gray-200 !default;

$toc-tools-font-size: 0.8rem !default;

/* Callout customization */
// Formatting
$callout-border-width: 5px !default;
$callout-border-scale: 0% !default;
$callout-icon-scale: 10% !default;
$callout-margin-top: 1.25rem !default;
$callout-margin-bottom: 1.25rem !default;

// Navbar
$navbar-default: if(
  variable-exists(theme),
  if(variable-exists(primary), $primary, #517699),
  #517699
);

// If the user provides a navbar-bg, we ned to ignore the
// theme overide and just recalculate a good value
$navbar-hl-override: if(
  variable-exists(navbar-bg) and variable-exists(link-color),
  theme-contrast($link-color, $navbar-bg),
  false
);
$navbar-bg: theme-override-value(
  $theme-name,
  "navbar-bg",
  $navbar-default
) !default;

$btn-bg: if(variable-exists(secondary), $secondary, #6c757d) !default;
$btn-fg: theme-contrast($btn-bg, $btn-bg) !default;

$body-contrast-bg: if(variable-exists(body-bg), $body-bg, $white);
$body-contrast-color: if(variable-exists(body-color), $body-color, $gray-900);
$navbar-fg: if(
  $navbar-bg == transparent,
  theme-override-value(
    $theme-name,
    "navbar-fg",
    theme-contrast($body-contrast-color, $body-contrast-bg)
  ),
  theme-override-value(
    $theme-name,
    "navbar-fg",
    theme-contrast($navbar-bg, $navbar-bg)
  )
) !default;

$navbar-hl: if(
  $navbar-hl-override != false,
  $navbar-hl-override,
  theme-override-value(
    $theme-name,
    "navbar-hl",
    if(
      variable-exists(link-color),
      theme-contrast($link-color, $navbar-bg),
      $navbar-fg
    )
  )
) !default;
$navbar-brand: theme-override-value(
  $theme-name,
  "navbar-brand",
  $navbar-fg
) !default;
$navbar-brand-hl: theme-override-value(
  $theme-name,
  "navbar-brand-hl",
  $navbar-hl
) !default;

$navbar-toggler-icon-bg: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-fg}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>") !default;
$navbar-toggler-border-color: rgba($navbar-fg, 0) !default;
$navbar-hover-color: rgba($navbar-hl, 0.8) !default;
$navbar-disabled-color: rgba($navbar-fg, 0.75) !default;
$navbar-toggler-padding-x: 0 !default;
$navbar-toggler-padding-y: 0.25 !default;

// We omit the !default here b/c the dark and light variants
// are not meaningful in our usage of bootstrap. Instead, we will explicitly
// manage these using the above documented variables
$navbar-dark-bg: $navbar-bg;
$navbar-dark-color: $navbar-fg;
$navbar-dark-hover-color: $navbar-hover-color;
$navbar-dark-active-color: $navbar-hl;
$navbar-dark-disabled-color: $navbar-disabled-color;
$navbar-dark-toggler-icon-bg: $navbar-toggler-icon-bg;
$navbar-dark-toggler-border-color: $navbar-toggler-border-color;

$navbar-light-bg: $navbar-bg;
$navbar-light-color: $navbar-fg;
$navbar-light-hover-color: $navbar-hover-color;
$navbar-light-active-color: $navbar-hl;
$navbar-light-disabled-color: $navbar-disabled-color;
$navbar-light-toggler-icon-bg: $navbar-toggler-icon-bg;
$navbar-light-toggler-border-color: $navbar-toggler-border-color;

$navbar-light-brand-color: $navbar-brand;
$navbar-light-brand-hover-color: $navbar-brand-hl;
$navbar-dark-brand-color: $navbar-brand;
$navbar-dark-brand-hover-color: $navbar-brand-hl;

// Sidebar coloring
$sidebar-bg: if(variable-exists(light), $light, #fff) !default;
$sidebar-fg: null !default;
@if $sidebar-bg == transparent {
  $sidebar-fg: theme-contrast($body-contrast-color, $body-contrast-bg) !default;
} @else {
  $sidebar-fg: theme-contrast($sidebar-bg, $sidebar-bg) !default;
}
$sidebar-hl: null;
$sidebar-font-size: 0.925rem !default;
$sidebar-font-size-section: 0.875rem !default;
$sidebar-font-size-collapse: 1rem !default;
$sidebar-font-size-section-collapse: 1.1rem !default;
$sidebar-border: false !default;

// Title block variables
$title-block-color: $body-contrast-color !default;
$title-block-contast-color: $body-contrast-bg !default;
$title-block-padding-top: 2.5em !default;

// Footer coloring
$footer-bg: if(variable-exists(body-bg), $body-bg, #fff) !default;
$footer-fg: theme-contrast($footer-bg, $footer-bg, "AA") !default;
$footer-font-size: 0.825em !default;
$footer-left-font-size: $footer-font-size !default;
$footer-center-font-size: $footer-font-size !default;
$footer-right-font-size: $footer-font-size !default;

// Disable default grid system and switch to CSS grid
$enable-grid-classes: false;
$enable-cssgrid: true;

$zindex-pagelayout: 998;

$popover-bg: if(variable-exists(body-bg), $body-bg, null) !default;
$input-bg: if(variable-exists(body-bg), $body-bg, null) !default;

// Note that 'default' is intentionally omitted from this
// because we're using the default value if one is defined at this
// point (the if variable exists check in the default).
// This is a change to override the input border color for
// darkly, which sets the border color to the body color for
// whatever reason.
$input-border-color: theme-override-value(
  $theme-name,
  "input-border-color",
  if(variable-exists(input-border-color), $input-border-color, null)
);

// Same as above (default is respected if there is not override
// so the `!default` keyword is omitted). Some themes don't provide
// active tab border colors and they customize the main border
// color which results in the tabs looking slightly weird since the
// colors may not match (for example, simplex).
$nav-tabs-link-active-border-color: theme-override-value(
  $theme-name,
  "nav-tabs-link-active-border-color",
  if(
    variable-exists(nav-tabs-link-active-border-color),
    $nav-tabs-link-active-border-color,
    null
  )
);

/* GRID VARIABLES */
// The left hand sidebar
$grid-sidebar-width: 250px !default;
// The main body
$grid-body-width: 800px !default;
// The right hand margin bar
$grid-margin-width: 250px !default;
// The gutter that appears between the above columns
$grid-column-gutter-width: 1.5em !default;

/* CODE ANNOTATION COLORS */
$code-annotation-higlight-color: #aaaaaa44 !default;
$code-annotation-higlight-bg: #aaaaaa22 !default;

$breadcrumb-divider: quote(">") !default;

// table variable overrides
$table-group-separator-color: lighten(
  if(variable-exists(body-color), $body-color, $gray-900),
  50%
) !default;
$table-group-separator-color-lighter: lighten(
  if(variable-exists(body-color), $body-color, $gray-900),
  70%
) !default;

$bootstrap-version: 5;
