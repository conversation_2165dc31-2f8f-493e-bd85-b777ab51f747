/*-- scss:defaults --*/

$theme: "vapor" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #6c757d !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #170229 !default;
$black:    #000 !default;

$blue:    #1ba2f6 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #ea39b8 !default;
$red:     #e44c55 !default;
$orange:  #f1b633 !default;
$yellow:  #ffc107 !default;
$green:   #3cf281 !default;
$teal:    #3f81a2 !default;
$cyan:    #32fbe2 !default;

$primary:       $purple !default;
$secondary:     $pink !default;
$success:       $green !default;
$info:          $blue !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         #44d9e8 !default;
$dark:          $gray-900 !default;

$min-contrast-ratio:   1.2 !default;

// Options

// $enable-rounded:              false !default;

// Body

$body-bg:                   #1a0933 !default;
$body-color:                $cyan !default;

// Links

$link-color:                              $body-color !default;

// Components

$border-width:                0 !default;

$border-radius:               .15rem !default;
$border-radius-sm:            .05rem !default;

$component-active-bg:         $pink !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:      Lato, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;

$text-muted:                  rgba($body-color, .3) !default;

$blockquote-footer-color:     $text-muted !default;

// Tables

$table-color:                 $white !default;

$table-bg-scale:              0% !default;

// Forms

$input-bg:                              lighten($body-bg, 10%) !default;
$input-disabled-color:                  $gray-500 !default;
$input-disabled-bg:                     lighten($body-bg, 5%) !default;

$input-color:                           $white !default;

$input-placeholder-color:               rgba($white, .4) !default;

$input-group-addon-bg:                  $input-disabled-bg !default;

$form-select-disabled-color:        $input-placeholder-color !default;
$form-select-disabled-bg:           $input-disabled-bg !default;

$form-file-button-bg:             $input-group-addon-bg !default;
$form-file-button-hover-bg:       darken($form-file-button-bg, 2%) !default;

$form-range-track-bg:             $input-bg !default;

$form-range-thumb-disabled-bg:             $purple !default;

// Navs

$nav-link-disabled-color:           $text-muted !default;

$nav-tabs-link-active-color:        $component-active-bg !default;

// Dropdowns

$dropdown-link-hover-bg:            rgba($white, .15) !default;

// Pagination

$pagination-bg:                     transparent !default;

$pagination-focus-bg:               transparent !default;

$pagination-hover-bg:               transparent !default;

$pagination-disabled-color:         $text-muted !default;
$pagination-disabled-bg:            transparent !default;

// Cards

$card-border-width:                 2px !default;
$card-cap-color:                    $white !default;
$card-color:                        $white !default;

// Tooltips

$tooltip-opacity:                   1 !default;

// Popovers

$popover-bg:                        $primary !default;

$popover-header-color:              $white !default;

$popover-body-color:                $white !default;

// Toasts

$toast-color:                       $white !default;
$toast-background-color:            $primary !default;

$toast-header-color:                $toast-color !default;
$toast-header-background-color:     $toast-background-color !default;

// Modals

$modal-content-color:               $white !default;
$modal-content-bg:                  $primary !default;

// Progress bars

$progress-bg:                       $input-disabled-bg !default;

// List group

$list-group-color:                  $white !default;
$list-group-bg:                     $input-disabled-bg !default;
$list-group-hover-bg:               $pink !default;
$list-group-disabled-color:         $text-muted !default;
$list-group-action-color:           $body-color !default;
$list-group-action-hover-color:     $white !default;
$list-group-action-active-bg:       $list-group-hover-bg !default;

// Breadcrumbs

$breadcrumb-divider-color:          $text-muted !default;
$breadcrumb-active-color:           $component-active-bg !default;



/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

$outrun: false !default;

// Mixins

@mixin text-shadow($color) {
  text-shadow: 0 0 1px rgba($color, .6), 0 0 3px rgba($color, .5), 0 0 .5rem rgba($color, .3), 0 0 2rem rgba($color, .2);
}

@mixin text-shadow-sm($color) {
  text-shadow: 0 0 1px rgba($color, .3), 0 0 2px rgba($color, .3), 0 0 5px rgba($color, .2);
}

@mixin box-shadow($color) {
  box-shadow: 0 0 2px rgba($color, .9), 0 0 4px rgba($color, .4), 0 0 1rem rgba($color, .3), 0 0 4rem rgba($color, .1);
}

@mixin box-shadow-lg($color) {
  box-shadow: 0 0 2rem rgba(tint-color($color, 10%), .4), 0 0 8rem rgba(tint-color($color, 10%), .3);
}

@mixin header-shadow($color) {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    @include text-shadow($color);
  }
}

// Body

body {
  min-height: 100vh;
  background-image: linear-gradient(shade-color($body-bg, 10%) 0%, $body-bg 7%, $body-bg 80%, shade-color(#173e98, 50%) 100%);
  @include text-shadow-sm($body-color);

  @if ($outrun) {
    position: relative;
    overflow-x: hidden;

    &::after {
      position: absolute;
      bottom: 0;
      left: -50vw;
      z-index: -1;
      display: block;
      width: 200vw;
      height: 100vh;
      content: "";
      background-image: repeating-linear-gradient(rgba($blue, .6) 0 1px, transparent 1px 100%), repeating-linear-gradient(90deg, rgba($blue, .6) 0 1px, transparent 1px 100%);
      background-size: 200px 200px;
      transform: perspective(50vh) rotateX(50deg) translateY(-11.3vh) translateZ(10px);
      transform-origin: center center;
    }
  }
}

// Typography

@include header-shadow($cyan);

.text {
  @each $color, $value in $theme-colors {
    &-#{$color} {
      @include text-shadow-sm($value);
    }
  }

  &-white {
    @include text-shadow-sm($white);
    @include header-shadow($white);
  }

  &-muted {
    @include text-shadow-sm(shade-color($text-muted, 20%));
  }
}

a {
  @include text-shadow-sm($link-color);
}

.blockquote {

  &-footer {
    @include text-shadow-sm(shade-color($blockquote-footer-color, 20%));
  }
}

hr {
  border-top: 1px solid $primary;
  @include box-shadow($primary);
  opacity: 1;
}

// Tables

table,
.table {
  @include text-shadow-sm($white);
}

// Buttons

.btn {
  @each $color, $value in $theme-colors {
    @if (($color != dark) or ($color != link)) {
      &-#{$color},
      &-outline-#{$color} {
        @include box-shadow($value);
      }
    }

    &-#{$color} {
      @include text-shadow-sm($white);
    }

    &-outline-#{$color} {
      color: $white;
      border-width: 2px;
    }
  }

  &-dark {
    @include box-shadow($primary);
  }

  &-link {
    box-shadow: none;
    @include text-shadow($body-color);
  }

  &-outline-dark {
    color: $white;
  }
}

// Navbars

.navbar {
  @each $color, $value in $theme-colors {
    &.bg-#{$color} {
      @include box-shadow($value);
    }
  }

  &[data-bs-theme="dark"] {
    &,
    a {
      @include text-shadow-sm($white);
    }

    .navbar-brand {
      @include text-shadow($white);
    }
  }

  &[data-bs-theme="light"] {
    &,
    a {
      @include text-shadow-sm($black);
    }

    .navbar-brand {
      @include text-shadow($black);
    }
  }
}

// Navs

.nav-link {
  &.disabled {
    @include text-shadow-sm($nav-link-disabled-color);
  }
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  @include text-shadow-sm($component-active-bg);
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  @include box-shadow($component-active-bg);
}

.breadcrumb {
  &-item.active {
    @include text-shadow-sm($breadcrumb-active-color);
  }

  &-item + .breadcrumb-item::before {
    @include text-shadow-sm($breadcrumb-divider-color);
  }
}

.page-link {
  border-radius: $border-radius;
}

.page-item.active {
  .page-link {
    @include box-shadow($component-active-bg);
  }
}

// Forms

legend {
  @include text-shadow($body-color);
}

.valid-feedback {
  @include text-shadow-sm($success);
}

.invalid-feedback {
  @include text-shadow-sm($danger);
}

// Indicators

.alert {
  @each $color, $value in $theme-colors {
    &-#{$color} {
      color: $white;
      background-color: $value;
      @include text-shadow-sm($white);
      @include box-shadow-lg($value);
    }
  }

  .alert-link,
  a {
    color: $white;
  }

  @include header-shadow($white);
}

.progress {
  overflow: visible;

  &-bar {
    @include box-shadow($primary);

    @each $color, $value in $theme-colors {
      &.bg-#{$color} {
        @include box-shadow($value);
      }
    }
  }
}

.tooltip {
  --bs-tooltip-bg: var(--bs-body-bg);
  --bs-tooltip-color: var(--bs-body-color);

  &-inner,
  .arrow {
    @include box-shadow-lg($primary);
  }
}

.modal,
.popover,
.toast {
  @include text-shadow-sm($white);
  @include header-shadow($white);
}

.popover,
.toast {
  @include box-shadow-lg($primary);
}

.modal {
  &-content {
    @include box-shadow-lg($primary);
  }
}

// Containers

.list-group {

  &-item.active {
    @include header-shadow($white);
  }
}

.card {
  background-color: transparent;
  @include text-shadow-sm($white);

  @each $color, $value in $theme-colors {
    @if ($color != dark) {
      &.border-#{$color} {
        @include box-shadow($value);
      }
    }
  }

  &.border-dark {
    @include box-shadow($primary);
  }

  @include header-shadow($white);
}


