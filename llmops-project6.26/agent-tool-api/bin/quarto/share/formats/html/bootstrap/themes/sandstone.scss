/*-- scss:defaults --*/

$theme: "sandstone" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #f8f5f0 !default;
$gray-300: #dfd7ca !default;
$gray-400: #ced4da !default;
$gray-500: #98978b !default;
$gray-600: #8e8c84 !default;
$gray-700: #495057 !default;
$gray-800: #3e3f3a !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$blue:    #325d88 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #d9534f !default;
$orange:  #f47c3c !default;
$yellow:  #ffc107 !default;
$green:   #93c54b !default;
$teal:    #20c997 !default;
$cyan:    #29abe0 !default;

$primary:       $blue !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $orange !default;
$danger:        $red !default;
$light:         $gray-200 !default;
$dark:          $gray-800 !default;

$min-contrast-ratio:   2 !default;

// Body

$body-color:                $gray-800 !default;

// Links

$link-color:                $success !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:      Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;
$headings-font-weight:        400 !default;

// Dropdowns

$dropdown-link-color:               $gray-600 !default;
$dropdown-link-hover-color:         $dropdown-link-color !default;
$dropdown-link-hover-bg:            $gray-200 !default;
$dropdown-link-active-color:        $dropdown-link-color !default;
$dropdown-link-active-bg:           $dropdown-link-hover-bg !default;

// Navs

$nav-link-padding-x:                .9rem !default;
$nav-link-disabled-color:           $gray-300 !default;
$nav-tabs-border-color:             $gray-300 !default;
$nav-tabs-link-hover-border-color:  $gray-300 !default;
$nav-tabs-link-active-bg:           $white !default;
$nav-pills-link-active-color:       $gray-600 !default;
$nav-pills-link-active-bg:          $gray-200 !default;

// Navbar

// Pagination

$pagination-color:                  $gray-600 !default;
$pagination-bg:                     $gray-200 !default;
$pagination-border-color:           $gray-300 !default;
$pagination-hover-color:            $pagination-color !default;
$pagination-active-color:           $pagination-color !default;
$pagination-active-bg:              $gray-300 !default;
$pagination-active-border-color:    $gray-300 !default;
$pagination-disabled-color:         $gray-300 !default;
$pagination-disabled-bg:            $gray-200 !default;
$pagination-disabled-border-color:  $pagination-border-color !default;

// Cards

$card-border-color:                 rgba($gray-300, .75) !default;
$card-cap-bg:                       rgba($gray-200, .25) !default;

// Popovers

$popover-header-bg:                 $gray-200 !default;

// Modals

$modal-content-border-color:        $gray-300 !default;
$modal-header-border-color:         $modal-content-border-color !default;

// Progress bars

$progress-bg:                       $gray-300 !default;
$progress-border-radius:            10px !default;
$progress-bar-color:                $primary !default;

// List group

$list-group-border-color:           $gray-300 !default;
$list-group-hover-bg:               $gray-200 !default;
$list-group-active-color:           $body-color !default;
$list-group-active-bg:              $gray-200 !default;
$list-group-active-border-color:    $gray-300 !default;
$list-group-disabled-color:         $gray-500 !default;
$list-group-disabled-bg:            $white !default;
$list-group-action-color:           $list-group-active-color !default;
$list-group-action-active-color:    $list-group-active-color !default;
$list-group-action-active-bg:       $gray-300 !default;

// Breadcrumbs

$breadcrumb-padding-y:              .375rem !default;
$breadcrumb-padding-x:              .75rem !default;
$breadcrumb-bg:                     $pagination-bg !default;
$breadcrumb-border-radius:          .25rem !default;

// Close

$btn-close-color:            $white !default;
$btn-close-opacity:          .8 !default;
$btn-close-hover-opacity:    1 !default;



/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

// Navbar

.sandstone {
  font-size: 13px;
  font-weight: 500;
  line-height: 22px;
  text-transform: uppercase;
}

.navbar {
  .nav-link {
    @extend .sandstone;
  }

  &-form input,
  &-form .form-control {
    border: none;
  }
}

// Buttons

.btn {
  @extend .sandstone;

  &:hover {
    border-color: transparent;
  }

  &-success,
  &-warning {
    color: $white;
  }
}

// Tables

.table {
  .thead-dark th {
    background-color: $dark;
  }
}

// Forms

input,
.form-control {
  @include box-shadow(none);

  &:focus {
    @include box-shadow(none);
  }
}

// Navs

.nav-tabs {
  .nav-link {
    @extend .sandstone;
    background-color: $gray-200;
    border-color: $gray-300;
  }

  .nav-link,
  .nav-link:hover,
  .nav-link:focus {
    color: $gray-600;
  }

  .nav-link.disabled,
  .nav-link.disabled:hover,
  .nav-link.disabled:focus {
    color: $nav-link-disabled-color;
    background-color: $gray-200;
    border-color: $gray-300;
  }
}

.nav-pills {
  .nav-link {
    @extend .sandstone;
    color: $gray-600;
    border: 1px solid transparent;
  }

  .nav-link.active,
  .nav-link:hover,
  .nav-link:focus {
    background-color: $gray-200;
    border-color: $gray-300;
  }

  .nav-link.disabled,
  .nav-link.disabled:hover {
    color: $gray-300;
    background-color: transparent;
    border-color: transparent;
  }
}


.breadcrumb {
  @extend .sandstone;
  border: 1px solid $gray-300;
}

.pagination {
  @extend .sandstone;

  a:hover {
    text-decoration: none;
  }
}

.dropdown-menu {
  .dropdown-item {
    @extend .sandstone;
  }
}

// Indicators

.alert {
  color: $white;

  a,
  .alert-link {
    color: $white;
    text-decoration: underline;
  }

  &-primary {
    &,
    > th,
    > td {
      background-color: $primary;
    }
  }

  &-secondary {
    &,
    > th,
    > td {
      background-color: $secondary;
    }
  }

  &-success {
    &,
    > th,
    > td {
      background-color: $success;
    }
  }

  &-info {
    &,
    > th,
    > td {
      background-color: $info;
    }
  }

  &-danger {
    &,
    > th,
    > td {
      background-color: $danger;
    }
  }

  &-warning {
    &,
    > th,
    > td {
      background-color: $warning;
    }
  }

  &-dark {
    &,
    > th,
    > td {
      background-color: $dark;
    }
  }

  &-light {
    &,
    > th,
    > td {
      background-color: $light;
    }

    &,
    a:not(.btn),
    .alert-link {
      color: $body-color;
    }
  }
}

.badge {
  &.bg-light {
    color: $dark;
  }
}

.tooltip {
  @extend .sandstone;
}

// Containers

.modal,
.toast,
.offcanvas {
  .btn-close {
    background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$gray-300}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>"));
  }
}


