/*-- scss:defaults --*/

$theme: "superhero" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #ebebeb !default;
$gray-200: #dee2e6 !default;
$gray-300: #ced4da !default;
$gray-400: #adb5bd !default;
$gray-500: #868e96 !default;
$gray-600: #4e5d6c !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$blue:    #4c9be8 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #d9534f !default;
$orange:  #df6919 !default;
$yellow:  #ffc107 !default;
$green:   #5cb85c !default;
$teal:    #20c997 !default;
$cyan:    #5bc0de !default;

 // Body
$body-bg: #0f2537 !default;
$body-color: $gray-100 !default;

@function body-mix($weight) {
  @return mix($body-bg, $body-color, $weight);
}

$contrast-bg: color-contrast($body-bg) !default;
$contrast-fg: color-contrast($contrast-bg) !default;

$primary:       $orange !default;
$secondary:     body-mix(80%) !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         body-mix(48%) !default;
$dark:          body-mix(80%) !default;

$min-contrast-ratio:   1.6 !default;

// Components

$border-radius:               0 !default;
$border-radius-lg:            0 !default;
$border-radius-sm:            0 !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:      Lato, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;

$text-muted:                  rgba($contrast-bg, .4) !default;

// Tables

$table-accent-bg:             rgba($contrast-bg, .05) !default;
$table-hover-bg:              rgba($contrast-bg, .075) !default;
$table-border-color:          rgba($contrast-fg, .15) !default;
$table-head-bg:               $light !default;
$table-dark-bg:               $light !default;
$table-dark-border-color:     body-mix(80%) !default;
$table-dark-color:            $body-bg !default;

$table-bg-scale:              0% !default;

// Forms

$input-bg:                          $contrast-bg !default;
$input-disabled-color:              body-mix(80%) !default;
$input-disabled-bg:                 $body-color !default;

$input-color:                       $gray-900 !default;
$input-border-color:                transparent !default;
$input-border-width:                0 !default;

$input-placeholder-color:               $gray-500 !default;

$input-group-addon-color:           $body-color !default;
$input-group-addon-bg:              body-mix(80%) !default;

$form-select-disabled-bg:           $input-disabled-bg !default;
$form-select-disabled-color:        $input-disabled-color !default;

$form-check-input-bg:                     $contrast-bg !default;
$form-check-input-border:                 none !default;

$form-file-button-color:          $input-group-addon-color !default;
$form-file-button-bg:             $input-group-addon-bg !default;
$form-file-button-hover-bg:       darken($form-file-button-bg, 5%) !default;

$form-floating-label-opacity:     1 !default;

// Dropdowns

$dropdown-bg:                       body-mix(80%) !default;
$dropdown-divider-bg:               rgba($contrast-fg, .15) !default;
$dropdown-link-color:               $body-color !default;
$dropdown-link-hover-color:         $dropdown-link-color !default;
$dropdown-link-hover-bg:            $table-hover-bg !default;

// Navs

$nav-link-disabled-color:           rgba(255, 255, 255, .4) !default;
$nav-tabs-border-color:             body-mix(80%) !default;
$nav-tabs-link-active-color:        $body-color !default;
$nav-tabs-link-active-border-color: body-mix(80%) !default;

// Navbar


// Pagination

$pagination-color:                  $contrast-bg !default;
$pagination-bg:                     body-mix(80%) !default;
$pagination-border-color:           transparent !default;
$pagination-hover-color:            $contrast-bg !default;
$pagination-hover-bg:               $nav-link-disabled-color !default;
$pagination-hover-border-color:     $pagination-border-color !default;
$pagination-disabled-color:         $nav-link-disabled-color !default;
$pagination-disabled-bg:            $pagination-bg !default;
$pagination-disabled-border-color:  $pagination-border-color !default;

// Cards

$card-cap-bg:                       $table-hover-bg !default;
$card-bg:                           body-mix(80%) !default;
$card-inner-border-radius:          0 !default;

// Accordion

$accordion-bg:                            $card-bg !default;
$accordion-border-width:                  0 !default;
$accordion-button-bg:                     $card-cap-bg !default;
$accordion-button-active-bg:              $primary !default;
$accordion-button-active-color:           $body-color !default;


// Popovers

$popover-bg:                        body-mix(80%) !default;
$popover-header-bg:                 $table-hover-bg !default;

// Toasts

$toast-background-color:            body-mix(80%) !default;
$toast-border-color:                rgba(0, 0, 0, .2) !default;
$toast-header-color:                $body-color !default;
$toast-header-background-color:     $toast-background-color !default;
$toast-header-border-color:         $toast-border-color !default;

// Modals

$modal-content-bg:                  body-mix(80%) !default;
$modal-header-border-color:         rgba(0, 0, 0, .2) !default;

// Progress bars

$progress-bg:                       body-mix(80%) !default;

// List group

$list-group-color:                  $contrast-bg !default;
$list-group-bg:                     body-mix(80%) !default;
$list-group-border-color:           transparent !default;
$list-group-hover-bg:               $nav-link-disabled-color !default;
$list-group-disabled-color:         $nav-link-disabled-color !default;
$list-group-action-color:           $contrast-bg !default;
$list-group-action-hover-color:     $contrast-bg !default;

// Breadcrumbs

$breadcrumb-padding-y:              .375rem !default;
$breadcrumb-padding-x:              .75rem !default;
$breadcrumb-bg:                     body-mix(80%) !default;
$breadcrumb-divider-color:          $body-color !default;
$breadcrumb-active-color:           $body-color !default;

// Close

$btn-close-color:            $contrast-bg !default;
$btn-close-opacity:          .5 !default;
$btn-close-hover-opacity:    1 !default;

// Code

$pre-color:                         inherit !default;



/*-- scss:rules --*/


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap" !default;
@if $web-font-path {
  @import url($web-font-path);
}

// Buttons

.btn {
  @each $color, $value in $theme-colors {
    &-#{$color} {
      @if $enable-gradients {
        background: $value linear-gradient(180deg, mix($white, $value, 15%), $value) repeat-x;
      } @else {
        background-color: $value;
      }
    }
  }
}

// Typography

.dropdown-menu {
  font-size: $font-size-sm;
}

.dropdown-header {
  font-size: $font-size-sm;
}

.blockquote-footer {
  color: $body-color;
}

// Tables

.table {
  font-size: $font-size-sm;

  .thead-dark th {
    color: $white;
  }

  a:not(.btn) {
    color: $white;
    text-decoration: underline;
  }

  .dropdown-menu a {
    text-decoration: none;
  }

  .text-muted {
    color: $text-muted;
  }
}

// Forms

label,
.radio label,
.checkbox label,
.help-block {
  font-size: $font-size-sm;
}

.form-floating {
  > label,
  > .form-control:focus ~ label,
  > .form-control:not(:placeholder-shown) ~ label {
    color: $input-placeholder-color;
  }
}

// Navs

.nav-tabs,
.nav-pills {
  .nav-link,
  .nav-link:hover {
    color: $body-color;
  }

  .nav-link.disabled {
    color: $nav-link-disabled-color;
  }
}

.page-link:hover,
.page-link:focus {
  color: $white;
  text-decoration: none;
}

// Indicators

.alert {
  color: $white;
  border: none;

  a,
  .alert-link {
    color: $white;
    text-decoration: underline;
  }

  @each $color, $value in $theme-colors {
    &-#{$color} {
      @if $enable-gradients {
        background: $value linear-gradient(180deg, mix($white, $value, 15%), $value) repeat-x;
      } @else {
        background-color: $value;
      }
    }
  }
}

.badge {
  &-warning,
  &-info {
    color: $white;
  }
}

.tooltip {
  --bs-tooltip-bg: var(--bs-tertiary-bg);
  --bs-tooltip-color: var(--bs-emphasis-color);
}

// Popovers

.popover-header {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

// Containers

.modal {
  &-header,
  &-footer {
    background-color: $table-hover-bg;
  }
}


