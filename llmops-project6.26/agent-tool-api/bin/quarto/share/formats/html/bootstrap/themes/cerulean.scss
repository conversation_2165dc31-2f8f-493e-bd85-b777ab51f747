/*-- scss:defaults --*/

$theme: "cerulean" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #868e96 !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$blue:    #033c73 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #c71c22 !default;
$orange:  #fd7e14 !default;
$yellow:  #dd5600 !default;
$green:   #73a839 !default;
$teal:    #20c997 !default;
$cyan:    #2fa4e7 !default;

$primary:       $cyan !default;
$secondary:     $gray-200 !default;
$success:       $green !default;
$info:          $blue !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$min-contrast-ratio:   2.75 !default;

// Body

$body-color:    $gray-700 !default;

// Fonts

$headings-color:         $cyan !default;

// Dropdowns

$dropdown-link-color:           $body-color !default;
$dropdown-link-hover-color:     $white !default;
$dropdown-link-hover-bg:        $primary !default;

// Navbar

$navbar-dark-color:             rgba($white, .8) !default;
$navbar-dark-hover-color:       $white !default;



/*-- scss:rules --*/


// Variables

$text-shadow: 0 1px 0 rgba(0, 0, 0, .05) !default;

// Mixins

@mixin btn-shadow($color){
  @include gradient-y-three-colors(tint-color($color, 16%), $color, 60%, shade-color($color, 6%));
}

// Navbar

.navbar {
  @each $color, $value in $theme-colors {
    &.bg-#{$color} {
      @include btn-shadow($value);
    }
  }
}

.navbar-brand,
.nav-link {
  text-shadow: $text-shadow;
}

// Buttons

.btn {
  text-shadow: $text-shadow;
}

.btn-secondary,
.btn-outline-secondary {
  color: $gray-700;
}

@each $color, $value in $theme-colors {
  .btn-#{$color} {
    @include btn-shadow($value);
  }
}

// Typography

.text-secondary {
  color: $gray-500 !important;
}

.bg-primary,
.bg-success,
.bg-info,
.bg-warning,
.bg-danger,
.bg-dark {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $white;
  }
}

// Navs

.dropdown-menu {
  .dropdown-header {
    color: $gray-600;
  }
}

// Indicators

.badge {
  &.bg-secondary,
  &.bg-light {
    color: $dark;
  }
}


