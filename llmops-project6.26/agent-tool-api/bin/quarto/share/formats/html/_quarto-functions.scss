@function colorToRGB($color) {
  @return "rgb(" + red($color) + ", " + green($color) + ", " + blue($color) +
    ")";
}

@function colorToRGBA($color) {
  @return "rgba(" + red($color) + ", " + green($color) + ", " + blue($color) +
    ", " + alpha($color) + ")";
}

@function str-replace($string, $search, $replace: "") {
  $index: str-index($string, $search);
  @if $index {
    @return str-slice($string, 1, $index - 1) + $replace +
      str-replace(
        str-slice($string, $index + str-length($search)),
        $search,
        $replace
      );
  }
  @return $string;
}
