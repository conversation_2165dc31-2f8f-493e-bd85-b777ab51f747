/* these are CSS rules that need to be passed directly to the
   mermaid library as a string. If you edit this file, then you must
   minify it and replace the value of the defaultCSS declaration
   in mermaid-init.js.

   (Quart<PERSON> doesn't control this: mermaid does textual substitution in the
   CSS you pass them. So you can't just pass a URL to a CSS file.)
*/

.label text {
  fill: var(--mermaid-fg-color);
}

.node circle,
.node ellipse,
.node path,
.node polygon,
.node rect {
  fill: var(--mermaid-node-bg-color);
  stroke: var(--mermaid-node-fg-color);
}
marker {
  fill: var(--mermaid-edge-color) !important;
}
.edgeLabel .label rect {
  fill: #0000;
}
.label {
  color: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.label foreignObject {
  line-height: normal;
  overflow: visible;
}
.label div .edgeLabel {
  color: var(--mermaid-label-fg-color);
}
.edgeLabel,
.edgeLabel rect,
.label div .edgeLabel {
  background-color: var(--mermaid-label-bg-color);
}
.edgeLabel,
.edgeLabel rect {
  fill: var(--mermaid-label-bg-color);
  color: var(--mermaid-edge-color);
}
.edgePath .path,
.flowchart-link {
  stroke: var(--mermaid-edge-color);
}
.edgePath .arrowheadPath {
  fill: var(--mermaid-edge-color);
  stroke: none;
}
.cluster rect {
  fill: var(--mermaid-fg-color--lightest);
  stroke: var(--mermaid-fg-color--lighter);
}
.cluster span {
  color: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
defs #flowchart-circleEnd,
defs #flowchart-circleStart,
defs #flowchart-crossEnd,
defs #flowchart-crossStart,
defs #flowchart-pointEnd,
defs #flowchart-pointStart {
  stroke: none;
}
g.classGroup line,
g.classGroup rect {
  fill: var(--mermaid-node-bg-color);
  stroke: var(--mermaid-node-fg-color);
}
g.classGroup text {
  fill: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.classLabel .box {
  fill: var(--mermaid-label-bg-color);
  background-color: var(--mermaid-label-bg-color);
  opacity: 1;
}
.classLabel .label {
  fill: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.node .divider {
  stroke: var(--mermaid-node-fg-color);
}
.relation {
  stroke: var(--mermaid-edge-color);
}
.cardinality {
  fill: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.cardinality text {
  fill: inherit !important;
}
defs #classDiagram-compositionEnd,
defs #classDiagram-compositionStart,
defs #classDiagram-dependencyEnd,
defs #classDiagram-dependencyStart,
defs #classDiagram-extensionEnd,
defs #classDiagram-extensionStart {
  fill: var(--mermaid-edge-color) !important;
  stroke: var(--mermaid-edge-color) !important;
}
defs #classDiagram-aggregationEnd,
defs #classDiagram-aggregationStart {
  fill: var(--mermaid-label-bg-color) !important;
  stroke: var(--mermaid-edge-color) !important;
}
g.stateGroup rect {
  fill: var(--mermaid-node-bg-color);
  stroke: var(--mermaid-node-fg-color);
}
g.stateGroup .state-title {
  fill: var(--mermaid-label-fg-color) !important;
  font-family: var(--mermaid-font-family);
}
g.stateGroup .composit {
  fill: var(--mermaid-label-bg-color);
}
.nodeLabel {
  color: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.node circle.state-end,
.node circle.state-start,
.start-state {
  fill: var(--mermaid-edge-color);
  stroke: none;
}
.end-state-inner,
.end-state-outer {
  fill: var(--mermaid-edge-color);
}
.end-state-inner,
.node circle.state-end {
  stroke: var(--mermaid-label-bg-color);
}
.transition {
  stroke: var(--mermaid-edge-color);
}
[id^="state-fork"] rect,
[id^="state-join"] rect {
  fill: var(--mermaid-edge-color) !important;
  stroke: none !important;
}
.statediagram-cluster.statediagram-cluster .inner {
  fill: var(--mermaid-bg-color);
}
.statediagram-cluster rect {
  fill: var(--mermaid-node-bg-color);
  stroke: var(--mermaid-node-fg-color);
}
.statediagram-state rect.divider {
  fill: var(--mermaid-fg-color--lightest);
  stroke: var(--mermaid-fg-color--lighter);
}
defs #statediagram-barbEnd {
  stroke: var(--mermaid-edge-color);
}
.entityBox {
  fill: var(--mermaid-label-bg-color);
  stroke: var(--mermaid-node-fg-color);
}
.entityLabel {
  fill: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
.relationshipLabelBox {
  fill: var(--mermaid-label-bg-color);
  fill-opacity: 1;
  background-color: var(--mermaid-label-bg-color);
  opacity: 1;
}
.relationshipLabel {
  fill: var(--mermaid-label-fg-color);
}
.relationshipLine {
  stroke: var(--mermaid-edge-color);
}
defs #ONE_OR_MORE_END *,
defs #ONE_OR_MORE_START *,
defs #ONLY_ONE_END *,
defs #ONLY_ONE_START *,
defs #ZERO_OR_MORE_END *,
defs #ZERO_OR_MORE_START *,
defs #ZERO_OR_ONE_END *,
defs #ZERO_OR_ONE_START * {
  stroke: var(--mermaid-edge-color) !important;
}
.actor,
defs #ZERO_OR_MORE_END circle,
defs #ZERO_OR_MORE_START circle {
  fill: var(--mermaid-label-bg-color);
}
.actor {
  stroke: var(--mermaid-node-fg-color);
}
text.actor > tspan {
  fill: var(--mermaid-label-fg-color);
  font-family: var(--mermaid-font-family);
}
line {
  stroke: var(--mermaid-fg-color--lighter);
}
.messageLine0,
.messageLine1 {
  stroke: var(--mermaid-edge-color);
}
.loopText > tspan,
.messageText,
.noteText > tspan {
  fill: var(--mermaid-edge-color);
  stroke: none;
  font-family: var(--mermaid-font-family) !important;
}
.noteText > tspan {
  fill: #000;
}
#arrowhead path {
  fill: var(--mermaid-edge-color);
  stroke: none;
}
.loopLine {
  stroke: var(--mermaid-node-fg-color);
}
.labelBox,
.loopLine {
  fill: var(--mermaid-node-bg-color);
}
.labelBox {
  stroke: none;
}
.labelText,
.labelText > span {
  fill: var(--mermaid-node-fg-color);
  font-family: var(--mermaid-font-family);
}
