/*-- scss: functions --*/

@function bannerColor() {
  @if $title-banner-color {
    @return $title-banner-color;
  } @else {
    @if variable-exists(navbar-fg) {
      @return $navbar-fg;
    } @else {
      @return $body-bg;
    }
  }
}

@function bannerDim() {
  @return theme-fade(bannerColor(), bannerBg(), 20%);
}

@function bannerBg() {
  @if $title-banner-bg {
    @return $title-banner-bg;
  } @else {
    // figure out default background, navbar  of body color
    @if variable-exists(navbar-bg) {
      @return $navbar-bg;
    } @else {
      @return $body-color;
    }
  }
}

/*-- scss:variables --*/
$title-banner-color: null !default;
$title-banner-bg: null !default;
$title-banner-image: null !default;

/*-- scss:rules --*/

/* Title Banner */
.quarto-title-banner {
  margin-bottom: 1em;

  color: bannerColor();
  background: bannerBg();
  @if $title-banner-image {
    background-image: $title-banner-image;
    background-size: cover;
  }
  a {
    color: bannerColor();
  }

  h1,
  h2 {
    color: bannerColor();
  }

  .code-tools-button {
    color: bannerDim();
  }
  .code-tools-button:hover {
    color: bannerColor();
  }

  .code-tools-button > .bi::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGB(bannerDim())}" viewBox="0 0 16 16"><path d="M10.478 1.647a.5.5 0 1 0-.956-.294l-4 13a.5.5 0 0 0 .956.294l4-13zM4.854 4.146a.5.5 0 0 1 0 .708L1.707 8l3.147 3.146a.5.5 0 0 1-.708.708l-3.5-3.5a.5.5 0 0 1 0-.708l3.5-3.5a.5.5 0 0 1 .708 0zm6.292 0a.5.5 0 0 0 0 .708L14.293 8l-3.147 3.146a.5.5 0 0 0 .708.708l3.5-3.5a.5.5 0 0 0 0-.708l-3.5-3.5a.5.5 0 0 0-.708 0z"/></svg>');
  }

  .code-tools-button:hover > .bi::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGB(bannerColor())}" viewBox="0 0 16 16"><path d="M10.478 1.647a.5.5 0 1 0-.956-.294l-4 13a.5.5 0 0 0 .956.294l4-13zM4.854 4.146a.5.5 0 0 1 0 .708L1.707 8l3.147 3.146a.5.5 0 0 1-.708.708l-3.5-3.5a.5.5 0 0 1 0-.708l3.5-3.5a.5.5 0 0 1 .708 0zm6.292 0a.5.5 0 0 0 0 .708L14.293 8l-3.147 3.146a.5.5 0 0 0 .708.708l3.5-3.5a.5.5 0 0 0 0-.708l-3.5-3.5a.5.5 0 0 0-.708 0z"/></svg>');
  }

  .quarto-title .title {
    font-weight: 600;
  }

  .quarto-categories {
    margin-top: 0.75em;
  }

  @include media-breakpoint-up(lg) {
    padding-top: 2.5em;
    padding-bottom: 2.5em;
  }

  @include media-breakpoint-down(lg) {
    padding-top: 1em;
    padding-bottom: 1em;
  }
}
@include media-breakpoint-down(md) {
  body.hypothesis-enabled {
    #title-block-header > * {
      padding-right: 20px;
    }
  }
}

main.quarto-banner-title-block > section:first-child > h2,
main.quarto-banner-title-block > section:first-child > h3,
main.quarto-banner-title-block > section:first-child > h4 {
  margin-top: 0;
}

.quarto-title {
  .quarto-categories {
    display: flex;
    flex-wrap: wrap;
    row-gap: 0.5em;
    column-gap: 0.4em;
    padding-bottom: 0.5em;
    margin-top: 0.75em;

    .quarto-category {
      padding: 0.25em 0.75em;
      font-size: 0.65em;
      text-transform: uppercase;
      border: solid 1px;
      border-radius: $border-radius;
      opacity: 0.6;

      a {
        color: inherit;
      }
    }
  }
}

/* Manuscript customization */
.quarto-title-meta-container {
  display: grid;
  grid-template-columns: 1fr auto;
}

.quarto-title-meta-column-end {
  display: flex;
  flex-direction: column;
  padding-left: 1em;
}

.quarto-title-meta-column-end a .bi {
  margin-right: 0.3em;
}

/* Title Default */
#title-block-header.quarto-title-block.default {
  .quarto-title-meta {
    display: grid;
    grid-template-columns: minmax(max-content, 1fr) 1fr;
    grid-column-gap: 1em;
  }

  .quarto-title {
    .title {
      margin-bottom: 0;
    }
  }

  .quarto-title-author-orcid {
    img {
      margin-top: -0.2em;
      height: 0.8em;
      width: 0.8em;
    }
  }

  .quarto-title-author-email {
    opacity: 0.7;
  }

  .quarto-description {
    p:last-of-type {
      margin-bottom: 0;
    }
  }

  .quarto-title-meta-contents p,
  .quarto-title-authors p,
  .quarto-title-affiliations p {
    margin-bottom: 0.1em;
  }

  .quarto-title-meta-heading {
    text-transform: uppercase;
    margin-top: 1em;
    font-size: 0.8em;
    opacity: 0.8;
    font-weight: 400;
  }

  .quarto-title-meta-contents {
    font-size: 0.9em;
    p.affiliation:last-of-type {
      margin-bottom: 0.1em;
    }
  }

  p.affiliation {
    margin-bottom: 0.1em;
  }

  .keywords,
  .description,
  .abstract {
    margin-top: 0;

    & > p {
      font-size: 0.9em;
    }
    & > p:last-of-type {
      margin-bottom: 0;
    }

    .block-title {
      margin-top: 1em;
      text-transform: uppercase;
      font-size: 0.8em;
      opacity: 0.8;
      font-weight: 400;
    }
  }

  .quarto-title-meta-author {
    display: grid;
    grid-template-columns: minmax(max-content, 1fr) 1fr;
    grid-column-gap: 1em;
  }
}

.quarto-title-tools-only {
  display: flex;
  justify-content: right;
}
