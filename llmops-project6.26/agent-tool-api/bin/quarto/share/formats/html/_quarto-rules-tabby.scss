$body-bg: #fff !default;
$tabset-border-color: rgb(222, 226, 230) !default;

.panel-tabset [role="tablist"] {
  border-bottom: 1px solid $tabset-border-color;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

.panel-tabset [role="tablist"] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media (min-width: 30em) {
  .panel-tabset [role="tablist"] li {
    display: inline-block;
  }
}

.panel-tabset [role="tab"] {
  border: 1px solid transparent;
  border-top-color: $tabset-border-color;
  display: block;
  padding: 0.5em 1em;
  text-decoration: none;
}

@media (min-width: 30em) {
  .panel-tabset [role="tab"] {
    border-top-color: transparent;
    display: inline-block;
    margin-bottom: -1px;
  }
}

.panel-tabset [role="tab"][aria-selected="true"] {
  background-color: $tabset-border-color;
}

@media (min-width: 30em) {
  .panel-tabset [role="tab"][aria-selected="true"] {
    background-color: transparent;
    border: 1px solid $tabset-border-color;
    border-bottom-color: $body-bg;
  }
}

@media (min-width: 30em) {
  .panel-tabset [role="tab"]:hover:not([aria-selected="true"]) {
    border: 1px solid $tabset-border-color;
  }
}
