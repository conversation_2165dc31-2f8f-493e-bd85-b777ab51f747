<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$>

<head>

<meta charset="utf-8">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="pandoc" />
<meta name="viewport" content="width=device-width, initial-scale=1">

<style>
  $styles.html()$
</style>

<style>
body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
  padding: 45px;
}
</style>  

$for(header-includes)$
$header-includes$
$endfor$

$if(math)$
  $math$
$endif$

</head>

<body class="markdown-body">

$body$

</body>
</html>