<style type="text/css">
  
  /* Figure formatting */
  .quarto-layout-panel>figure>figcaption,
  .quarto-layout-panel>.panel-caption {
    margin-top: 10pt;
  }

  .quarto-layout-row {
    display: flex;
    align-items: flex-start;
  }

  .quarto-layout-valign-top {
    align-items: flex-start;
  }

  .quarto-layout-valign-bottom {
    align-items: flex-end;
  }

  .quarto-layout-valign-center {
    align-items: center;
  }

  .quarto-layout-cell {
    position: relative;
    margin-right: 20px;
  }

  .quarto-layout-cell:last-child {
    margin-right: 0;
  }

  .quarto-layout-cell figure,
  .quarto-layout-cell>p {
    margin: 0.2em;
  }

  .quarto-layout-cell img {
    max-width: 100%;
  }

  .quarto-layout-cell .html-widget {
    width: 100% !important;
  }

  .quarto-layout-cell div figure p {
    margin: 0;
  }

  .quarto-layout-cell figure {
    display: inline-block;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  .quarto-layout-cell table {
    display: inline-table;
  }

  .quarto-layout-cell-subref figcaption {
    font-style: italic;
    text-align: center;
  }

  .quarto-figure>figure {
    width: 100%;
  }

  .quarto-figure-left>figure>p {
    text-align: left;
  }

  .quarto-figure-center>figure>p {
    text-align: center;
  }

  .quarto-figure-right>figure>p {
    text-align: right;
  }

  figure>p:empty {
    display: none;
  }

  figure>p:first-child {
    margin-top: 0;
    margin-bottom: 0;
  }

  figure>figcaption {
    margin-top: 0.5em;
  }

  figcaption {
    font-size: 0.8em;
  }

  details {
    margin-bottom: 1em;
  }

  details[show] {
    margin-bottom: 0;
  }

  .quarto-unresolved-ref {
    font-weight: 600;
  }

  .quarto-cover-image {
    max-width: 35%;
    float: right;
    margin-left: 30px;
  }

  .cell-output-display {
    overflow-x: scroll;
  }

  .hidden {
    display: none;
  }
</style>