$if(titleblock)$
= $title$
$if(author)$
$for(author)$$author$$sep$; $endfor$
$if(date)$
$date$
$endif$
$elseif(date)$
:revdate: $date$
$endif$
$if(keywords)$
:keywords: $for(keywords)$$keywords$$sep$, $endfor$
$endif$
$if(lang)$
:lang: $lang$
$endif$
$if(toc)$
:toc:
$endif$
$if(math)$
:stem: latexmath
$endif$

$endif$
$if(abstract)$
[abstract]
== Abstract
$abstract$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
