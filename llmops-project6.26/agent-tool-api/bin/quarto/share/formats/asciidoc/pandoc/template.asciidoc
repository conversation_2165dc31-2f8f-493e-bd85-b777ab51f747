$if(titleblock)$
$if(title-prefix)$
$title-prefix$
$endif$
= $title$
$if(by-author)$
$for(by-author)$$it.name.literal$$if(it.email)$ <$it.email$>$endif$$sep$; $endfor$
$if(date)$
$date$
$endif$
$elseif(date)$
:revdate: $date$
$endif$
$if(description)$
:description: $description/chomp$
$endif$
$if(keywords)$
:keywords: $for(keywords)$$keywords$$sep$, $endfor$
$endif$
$if(lang)$
:lang: $lang$
$endif$
$if(toc)$
:toc:
$endif$
$if(asciidoc-doctype)$
:doctype: $asciidoc-doctype$
$endif$
$if(asciidoc-stem)$
:stem: $asciidoc-stem$
$endif$

$endif$
$if(abstract)$
[abstract]
== Abstract
$abstract$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$