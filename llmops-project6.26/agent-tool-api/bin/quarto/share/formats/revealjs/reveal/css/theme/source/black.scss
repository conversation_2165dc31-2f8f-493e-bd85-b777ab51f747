/**
 * Black theme for reveal.js. This is the opposite of the 'white' theme.
 *
 * By <PERSON><PERSON>, http://hakim.se
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------


// Include theme-specific fonts
@import url(./fonts/source-sans-pro/source-sans-pro.css);


// Override theme settings (see ../template/settings.scss)
$backgroundColor: #191919;

$mainColor: #fff;
$headingColor: #fff;

$mainFontSize: 42px;
$mainFont: 'Source Sans Pro', Helvetica, sans-serif;
$headingFont: 'Source Sans Pro', Helvetica, sans-serif;
$headingTextShadow: none;
$headingLetterSpacing: normal;
$headingTextTransform: uppercase;
$headingFontWeight: 600;
$linkColor: #42affa;
$linkColorHover: lighten( $linkColor, 15% );
$selectionBackgroundColor: lighten( $linkColor, 25% );

$heading1Size: 2.5em;
$heading2Size: 1.6em;
$heading3Size: 1.3em;
$heading4Size: 1.0em;

// Change text colors against light slide backgrounds
@include light-bg-text-color(#222);


// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
