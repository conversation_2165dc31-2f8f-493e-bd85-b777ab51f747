/**
 * Beige theme for reveal.js.
 *
 * Copyright (C) 2011-2012 <PERSON><PERSON>, http://hakim.se
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------



// Include theme-specific fonts
@import url(./fonts/league-gothic/league-gothic.css);
@import url(https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic);


// Override theme settings (see ../template/settings.scss)
$mainColor: #333;
$headingColor: #333;
$headingTextShadow: none;
$backgroundColor: #f7f3de;
$linkColor: #8b743d;
$linkColorHover: lighten( $linkColor, 20% );
$selectionBackgroundColor: rgba(79, 64, 28, 0.99);
$heading1TextShadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25), 0 20px 20px rgba(0,0,0,.15);

// Background generator
@mixin bodyBackground() {
	@include radial-gradient( rgba(247,242,211,1), rgba(255,255,255,1) );
}

// Change text colors against dark slide backgrounds
@include dark-bg-text-color(#fff);


// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
