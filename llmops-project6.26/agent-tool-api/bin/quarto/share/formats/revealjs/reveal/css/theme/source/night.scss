/**
 * Black theme for reveal.js.
 *
 * Copyright (C) 2011-2012 <PERSON><PERSON>, http://hakim.se
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------


// Include theme-specific fonts
@import url(https://fonts.googleapis.com/css?family=Montserrat:700);
@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,700,400italic,700italic);


// Override theme settings (see ../template/settings.scss)
$backgroundColor: #111;

$mainFont: 'Open Sans', sans-serif;
$linkColor: #e7ad52;
$linkColorHover: lighten( $linkColor, 20% );
$headingFont: 'Montserrat', Impact, sans-serif;
$headingTextShadow: none;
$headingLetterSpacing: -0.03em;
$headingTextTransform: none;
$selectionBackgroundColor: #e7ad52;

// Change text colors against light slide backgrounds
@include light-bg-text-color(#222);


// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------