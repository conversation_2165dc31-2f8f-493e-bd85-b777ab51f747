/**
 * Sky theme for reveal.js.
 *
 * Copyright (C) 2011-2012 <PERSON><PERSON>, http://hakim.se
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------



// Include theme-specific fonts
@import url(https://fonts.googleapis.com/css?family=Quicksand:400,700,400italic,700italic);
@import url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,400,700);


// Override theme settings (see ../template/settings.scss)
$mainFont: 'Open Sans', sans-serif;
$mainColor: #333;
$headingFont: 'Quicksand', sans-serif;
$headingColor: #333;
$headingLetterSpacing: -0.08em;
$headingTextShadow: none;
$backgroundColor: #f7fbfc;
$linkColor: #3b759e;
$linkColorHover: lighten( $linkColor, 20% );
$selectionBackgroundColor: #134674;

// Fix links so they are not cut off
.reveal a {
	line-height: 1.3em;
}

// Background generator
@mixin bodyBackground() {
	@include radial-gradient( #add9e4, #f7fbfc );
}

// Change text colors against dark slide backgrounds
@include dark-bg-text-color(#fff);



// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
