/**
 * This stylesheet is used to print reveal.js
 * presentations to PDF.
 *
 * https://revealjs.com/pdf-export/
 */

html.print-pdf {
	* {
		-webkit-print-color-adjust: exact;
	}

	& {
		width: 100%;
		height: 100%;
		overflow: visible;
	}

	body {
		margin: 0 auto !important;
		border: 0;
		padding: 0;
		float: none !important;
		overflow: visible;
	}

	/* Remove any elements not needed in print. */
	.nestedarrow,
	.reveal .controls,
	.reveal .progress,
	.reveal .playback,
	.reveal.overview,
	.state-background {
		display: none !important;
	}

	.reveal pre code {
		overflow: hidden !important;
		font-family: Courier, 'Courier New', monospace !important;
	}

	.reveal {
		width: auto !important;
		height: auto !important;
		overflow: hidden !important;
	}
	.reveal .slides {
		position: static;
		width: 100% !important;
		height: auto !important;
		zoom: 1 !important;
		pointer-events: initial;

		left: auto;
		top: auto;
		margin: 0 !important;
		padding: 0 !important;

		overflow: visible;
		display: block;

		perspective: none;
		perspective-origin: 50% 50%;
	}

	.reveal .slides .pdf-page {
		position: relative;
		overflow: hidden;
		z-index: 1;

		page-break-after: always;
	}

	.reveal .slides section {
		visibility: visible !important;
		display: block !important;
		position: absolute !important;

		margin: 0 !important;
		padding: 0 !important;
		box-sizing: border-box !important;
		min-height: 1px;

		opacity: 1 !important;

		transform-style: flat !important;
		transform: none !important;
	}

	.reveal section.stack {
		position: relative !important;
		margin: 0 !important;
		padding: 0 !important;
		page-break-after: avoid !important;
		height: auto !important;
		min-height: auto !important;
	}

	.reveal img {
		box-shadow: none;
	}


	/* Slide backgrounds are placed inside of their slide when exporting to PDF */
	.reveal .backgrounds {
		display: none;
	}
	.reveal .slide-background {
		display: block !important;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: auto !important;
	}

	/* Display slide speaker notes when 'showNotes' is enabled */
	.reveal.show-notes {
		max-width: none;
		max-height: none;
	}
	.reveal .speaker-notes-pdf {
		display: block;
		width: 100%;
		height: auto;
		max-height: none;
		top: auto;
		right: auto;
		bottom: auto;
		left: auto;
		z-index: 100;
	}

	/* Layout option which makes notes appear on a separate page */
	.reveal .speaker-notes-pdf[data-layout="separate-page"] {
		position: relative;
		color: inherit;
		background-color: transparent;
		padding: 20px;
		page-break-after: always;
		border: 0;
	}

	/* Display slide numbers when 'slideNumber' is enabled */
	.reveal .slide-number-pdf {
		display: block;
		position: absolute;
		font-size: 14px;
	}

	/* This accessibility tool is not useful in PDF and breaks it visually */
	.aria-status {
		display: none;
	}
}
