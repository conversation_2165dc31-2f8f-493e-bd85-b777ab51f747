/*-- scss:uses --*/
@use "sass:color" as sass-color;

/*-- scss:defaults --*/

@import url(./fonts/source-sans-pro/source-sans-pro.css);

// fonts
$font-family-sans-serif: "Source Sans Pro", Helvetica, sans-serif !default;
$font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
  "Liberation Mono", "Courier New", monospace !default;
$presentation-font-size-root: 40px !default;
$presentation-font-smaller: 0.7 !default;
$presentation-line-height: 1.3 !default;

// main colors
$body-bg: #fff !default;
$body-color: #222 !default;
$text-muted: lighten($body-color, 30%) !default;

// link colors
$link-color: #2a76dd !default;
$link-color-hover: lighten($link-color, 10%) !default;

// selection colors
$selection-bg: lighten($link-color, 25%) !default;
$selection-color: $body-bg !default;

// border colors
$border-color: lighten($body-color, 30%) !default;
$border-width: 1px !default;
$border-radius: 4px !default;

// headings
$presentation-heading-font: $font-family-sans-serif !default;
$presentation-heading-color: $body-color !default;
$presentation-heading-line-height: 1.2 !default;
$presentation-heading-letter-spacing: normal !default;
$presentation-heading-text-transform: none !default;
$presentation-heading-text-shadow: none !default;
$presentation-h1-text-shadow: none !default;
$presentation-heading-font-weight: 600 !default;
$presentation-h1-font-size: 2.5em !default;
$presentation-h2-font-size: 1.6em !default;
$presentation-h3-font-size: 1.3em !default;
$presentation-h4-font-size: 1em !default;

// margins
$presentation-block-margin: 12px !default;

// text alignment
$presentation-slide-text-align: left !default;
$presentation-title-slide-text-align: center !default;
$reveal-slide-text-align: $presentation-slide-text-align !default;
$reveal-title-slide-text-align: $presentation-title-slide-text-align !default;

// code blocks
$code-block-bg: $body-bg !default;
$code-block-border-color: lighten($body-color, 60%) !default;
$code-block-font-size: 0.55em !default;
$code-block-height: 500px !default;

// inline code
$code-color: var(--quarto-hl-fu-color) !default;
$code-bg: transparent !default;

// tabset
$tabset-border-color: $code-block-border-color !default;

// table
$table-border-color: $code-block-border-color !default;

// input panel
$input-panel-border-color: $code-block-border-color !default;
$input-panel-border-width: $border-width !default;
$input-panel-border-radius: $border-radius !default;
$input-panel-bg: rgba(248, 249, 250, 1) !default;

// alternate colors for when the background changes
$light-bg-text-color: #222 !default;
$dark-bg-text-color: #fff !default;
$light-bg-link-color: #2a76dd !default;
$dark-bg-link-color: #42affa !default;
$light-bg-code-color: #4758ab !default;
$dark-bg-code-color: #ffa07a !default;

// --- derive reveal versions of presentation variables for finer-grained override ---

$revealjs-font-size-root: $presentation-font-size-root !default;
$revealjs-h1-font-size: $presentation-h1-font-size !default;
$revealjs-h2-font-size: $presentation-h2-font-size !default;
$revealjs-h3-font-size: $presentation-h3-font-size !default;
$revealjs-h4-font-size: $presentation-h4-font-size !default;
$revealjs-heading-font: $presentation-heading-font !default;
$revealjs-heading-color: $presentation-heading-color !default;
$revealjs-heading-line-height: $presentation-heading-line-height !default;
$revealjs-heading-letter-spacing: $presentation-heading-letter-spacing !default;
$revealjs-heading-text-transform: $presentation-heading-text-transform !default;
$revealjs-heading-text-shadow: $presentation-heading-text-shadow !default;
$revealjs-h1-text-shadow: $presentation-h1-text-shadow !default;

$revealjs-heading-font-weight: $presentation-heading-font-weight !default;
$revealjs-block-margin: $presentation-block-margin !default;
$revealjs-line-height: $presentation-line-height !default;

// ---- map to reveal scss variables ---

// Background of the presentation
$backgroundColor: $body-bg !default;

// Primary/body text
$mainFont: $font-family-sans-serif !default;
$mainFontSize: $revealjs-font-size-root !default;
$mainColor: $body-color !default;

// Vertical spacing between blocks of text
$blockMargin: $revealjs-block-margin !default;

// Headings
$headingMargin: 0 0 $blockMargin 0 !default;
$headingFont: $revealjs-heading-font !default;
$headingColor: $revealjs-heading-color !default;
$headingLineHeight: $revealjs-heading-line-height !default;
$headingLetterSpacing: $revealjs-heading-letter-spacing !default;
$headingTextTransform: $revealjs-heading-text-transform !default;
$headingTextShadow: $revealjs-heading-text-shadow !default;
$heading1TextShadow: $revealjs-h1-text-shadow !default;
$headingFontWeight: $revealjs-heading-font-weight !default;

$heading1Size: $revealjs-h1-font-size !default;
$heading2Size: $revealjs-h2-font-size !default;
$heading3Size: $revealjs-h3-font-size !default;
$heading4Size: $revealjs-h4-font-size !default;

$codeFont: $font-family-monospace !default;

$code-block-theme-dark-threshhold: 40% !default;

// Links and actions
$linkColor: $link-color !default;
$linkColorHover: $link-color-hover !default;

// Text selection
$selectionBackgroundColor: $selection-bg !default;
$selectionColor: $selection-color !default;

/*-- scss:functions --*/

@function colorToRGB($color) {
  @return "rgb(" + red($color) + ", " + green($color) + ", " + blue($color) +
    ")";
}

/*-- scss:mixins --*/

// Generates the presentation background, can be overridden
// to return a background image or gradient
@mixin bodyBackground() {
  background: $backgroundColor;
}

/*-- scss:rules --*/

.reveal.center .slide aside,
.reveal.center .slide div.aside {
  position: initial;
}

section.has-light-background {
  &,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $light-bg-text-color;
  }
  a,
  a:hover {
    color: $light-bg-link-color;
  }
  code {
    color: $light-bg-code-color;
  }
}

section.has-dark-background {
  &,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $dark-bg-text-color;
  }
  a,
  a:hover {
    color: $dark-bg-link-color;
  }
  code {
    color: $dark-bg-code-color;
  }
}

#title-slide,
/* for when hash-type: number as identifier removed*/
div.reveal div.slides section.quarto-title-block {
  text-align: $presentation-title-slide-text-align;
  .subtitle {
    margin-bottom: 2.5rem;
  }
}

.reveal .slides {
  text-align: $reveal-slide-text-align;
}

.reveal .title-slide h1 {
  font-size: $revealjs-h2-font-size;
}

.reveal[data-navigation-mode="linear"] .title-slide h1 {
  font-size: $revealjs-h1-font-size;
}

.reveal div.sourceCode {
  border: $border-width solid $code-block-border-color;
  border-radius: $border-radius;
}

.reveal pre {
  width: 100%;
  box-shadow: none;
  background-color: $code-block-bg;
  border: none;
  margin: 0;
  font-size: $code-block-font-size;
}

.reveal code {
  color: $code-color;
  background-color: $code-bg;
  white-space: pre-wrap;
}

.reveal pre.sourceCode code {
  @if variable-exists(code-block-color) {
    color: $code-block-color;
  }
  background-color: $code-block-bg;
  padding: 6px 9px;
  max-height: $code-block-height;
  white-space: pre;
}

.reveal pre code {
  background-color: $body-bg;
  color: $body-color;
}

.reveal .column-output-location {
  display: flex;
  align-items: stretch;
}

.reveal .column-output-location .column:first-of-type div.sourceCode {
  height: 100%;
  background-color: $code-block-bg;
}

.reveal blockquote {
  display: block;
  position: relative;
  color: $border-color;
  width: unset;
  margin: var(--r-block-margin) auto;
  padding: 0.625rem 1.75rem;
  border-left: 0.25rem solid $text-muted;
  font-style: normal;
  background: none;
  box-shadow: none;
}
.reveal blockquote p:first-child,
.reveal blockquote p:last-child {
  display: block;
}

.reveal .slide aside,
.reveal .slide div.aside {
  position: absolute;
  bottom: 20px;
  font-size: #{$presentation-font-smaller}em;
  color: $text-muted;
}

.reveal .slide sup {
  font-size: #{$presentation-font-smaller}em;
}

.reveal .slide.scrollable aside,
.reveal .slide.scrollable div.aside {
  position: relative;
  margin-top: 1em;
}

.reveal .slide aside .aside-footnotes {
  margin-bottom: 0;
}

.reveal .slide aside .aside-footnotes li:first-of-type {
  margin-top: 0;
}

$panel-sidebar-width: 270px;
$panel-sidebar-padding: 0.5em;

.reveal .layout-sidebar {
  display: flex;
  width: 100%;
  margin-top: 0.8em;
}

.reveal .layout-sidebar .panel-sidebar {
  width: $panel-sidebar-width;
}

.reveal .layout-sidebar-left .panel-sidebar {
  margin-right: calc(#{$panel-sidebar-padding} * 2);
}

.reveal .layout-sidebar-right .panel-sidebar {
  margin-left: calc(#{$panel-sidebar-padding} * 2);
}

.reveal .layout-sidebar .panel-fill,
.reveal .layout-sidebar .panel-center,
.reveal .layout-sidebar .panel-tabset {
  flex: 1;
}

.reveal .panel-input,
.reveal .panel-sidebar {
  font-size: 0.5em;
  padding: $panel-sidebar-padding;
  border-style: solid;
  border-color: $input-panel-border-color;
  border-width: $input-panel-border-width;
  border-radius: $input-panel-border-radius;
  background-color: $input-panel-bg;
}

.reveal .panel-sidebar :first-child,
.reveal .panel-fill :first-child {
  margin-top: 0;
}

.reveal .panel-sidebar :last-child,
.reveal .panel-fill :last-child {
  margin-bottom: 0;
}

.panel-input > div,
.panel-input > div > div {
  vertical-align: middle;
  padding-right: 1em;
}

.reveal p,
.reveal .slides section,
.reveal .slides section > section {
  line-height: $revealjs-line-height;
}

.reveal.smaller .slides section,
.reveal .slides section.smaller,
.reveal .slides section .callout {
  font-size: #{$presentation-font-smaller}em;
}
// avoid applying twice the reduction when using nested section
.reveal.smaller .slides section section {
  font-size: inherit;
}

.reveal.smaller .slides h1,
.reveal .slides section.smaller h1 {
  font-size: calc(#{$revealjs-h1-font-size} / #{$presentation-font-smaller});
}

.reveal.smaller .slides h2,
.reveal .slides section.smaller h2 {
  font-size: calc(#{$revealjs-h2-font-size} / #{$presentation-font-smaller});
}

.reveal.smaller .slides h3,
.reveal .slides section.smaller h3 {
  font-size: calc(#{$revealjs-h3-font-size} / #{$presentation-font-smaller});
}

.reveal .columns > .column > :not(ul, ol) {
  margin-left: 0.25em;
  margin-right: 0.25em;
}

.reveal .columns > .column:first-child > :not(ul, ol) {
  margin-right: 0.5em;
  margin-left: 0;
}
.reveal .columns > .column:last-child > :not(ul, ol) {
  margin-right: 0;
  margin-left: 0.5em;
}

.reveal .slide-number {
  color: $linkColorHover;
  background-color: $body-bg;
}

.reveal .footer {
  color: $text-muted;

  a {
    color: $linkColor;
  }

  &.has-dark-background {
    color: sass-color.scale($dark-bg-text-color, $whiteness: 30%);

    a {
      color: sass-color.scale($dark-bg-link-color, $whiteness: 30%);
    }
  }

  &.has-light-background {
    color: sass-color.scale($light-bg-text-color, $whiteness: 30%);

    a {
      color: sass-color.scale($light-bg-link-color, $whiteness: 30%);
    }
  }
}

.reveal .slide-number {
  color: $text-muted;

  &.has-dark-background {
    color: sass-color.scale($dark-bg-text-color, $whiteness: 30%);
  }

  &.has-light-background {
    color: sass-color.scale($light-bg-text-color, $whiteness: 30%);
  }
}

// handle caption for figures
.reveal .slide {
  figure > figcaption,
  img.stretch + p.caption,
  img.r-stretch + p.caption {
    font-size: #{$presentation-font-smaller}em;
  }
}

@media screen and (min-width: 500px) {
  $arrow-spacing: 0.2em;
  $control-arrow-spacing: 1.4em;

  .reveal .controls[data-controls-layout="edges"] .navigate-left {
    left: $arrow-spacing;
  }

  .reveal .controls[data-controls-layout="edges"] .navigate-right {
    right: $arrow-spacing;
  }

  .reveal .controls[data-controls-layout="edges"] .navigate-up {
    top: $arrow-spacing * 2;
  }

  .reveal .controls[data-controls-layout="edges"] .navigate-down {
    bottom: $arrow-spacing - $control-arrow-spacing + 3.5em;
  }
}

.tippy-box[data-theme~="light-border"] {
  background-color: $backgroundColor;
  color: $mainColor;
  border-radius: $border-radius;
  border: solid $border-width $border-color;
  font-size: 0.6em;
}

.tippy-box[data-theme~="light-border"] .tippy-arrow {
  color: $border-color;
}

.tippy-box[data-placement^="bottom"] > .tippy-content {
  padding: 7px 10px;
  z-index: 1;
}

.reveal .callout.callout-style-simple .callout-body,
.reveal .callout.callout-style-default .callout-body,
.reveal .callout.callout-style-simple div.callout-title,
.reveal .callout.callout-style-default div.callout-title {
  font-size: inherit;
}

.reveal .callout.callout-style-default .callout-icon::before,
.reveal .callout.callout-style-simple .callout-icon::before {
  height: 2rem;
  width: 2rem;
  background-size: 2rem 2rem;
}

.reveal .callout.callout-titled .callout-title p {
  margin-top: 0.5em;
}

.reveal .callout.callout-titled .callout-icon::before {
  margin-top: 1rem;
}

.reveal .callout.callout-titled .callout-body > .callout-content > :last-child {
  margin-bottom: 1rem;
}

.reveal .panel-tabset [role="tab"] {
  padding: 0.25em 0.7em;
}

.reveal .slide-menu-button .fa-bars::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGB($linkColor)}" class="bi bi-list" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"/></svg>');
}

.reveal .slide-chalkboard-buttons .fa-easel2::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGB($linkColor)}" class="bi bi-easel2" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 0a.5.5 0 0 1 .447.276L8.81 1h4.69A1.5 1.5 0 0 1 15 2.5V11h.5a.5.5 0 0 1 0 1h-2.86l.845 3.379a.5.5 0 0 1-.97.242L12.11 14H3.89l-.405 1.621a.5.5 0 0 1-.97-.242L3.36 12H.5a.5.5 0 0 1 0-1H1V2.5A1.5 1.5 0 0 1 2.5 1h4.691l.362-.724A.5.5 0 0 1 8 0ZM2 11h12V2.5a.5.5 0 0 0-.5-.5h-11a.5.5 0 0 0-.5.5V11Zm9.61 1H4.39l-.25 1h7.72l-.25-1Z"/></svg>');
}

.reveal .slide-chalkboard-buttons .fa-brush::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGB($linkColor)}" class="bi bi-brush" viewBox="0 0 16 16"><path d="M15.825.12a.5.5 0 0 1 .132.584c-1.53 3.43-4.743 8.17-7.095 10.64a6.067 6.067 0 0 1-2.373 1.534c-.018.227-.06.538-.16.868-.201.659-.667 1.479-1.708 1.74a8.118 8.118 0 0 1-3.078.132 3.659 3.659 0 0 1-.562-.135 1.382 1.382 0 0 1-.466-.247.714.714 0 0 1-.204-.288.622.622 0 0 1 .004-.443c.095-.245.316-.38.461-.452.394-.197.625-.453.867-.826.095-.144.184-.297.287-.472l.117-.198c.151-.255.326-.54.546-.848.528-.739 1.201-.925 1.746-.896.126.007.243.025.348.048.062-.172.142-.38.238-.608.261-.619.658-1.419 1.187-2.069 2.176-2.67 6.18-6.206 9.117-8.104a.5.5 0 0 1 .596.04zM4.705 11.912a1.23 1.23 0 0 0-.419-.1c-.246-.013-.573.05-.879.479-.197.275-.355.532-.5.777l-.105.177c-.106.181-.213.362-.32.528a3.39 3.39 0 0 1-.76.861c.69.112 1.736.111 2.657-.12.559-.139.843-.569.993-1.06a3.122 3.122 0 0 0 .126-.75l-.793-.792zm1.44.026c.12-.04.277-.1.458-.183a5.068 5.068 0 0 0 1.535-1.1c1.9-1.996 4.412-5.57 6.052-8.631-2.59 1.927-5.566 4.66-7.302 6.792-.442.543-.795 1.243-1.042 1.826-.121.288-.214.54-.275.72v.001l.575.575zm-4.973 3.04.007-.005a.031.031 0 0 1-.007.004zm3.582-3.043.002.001h-.002z"/></svg>');
}

// This is a sentinel value that renderers can use to determine
// whether the theme is dark or light
@if (
  sass-color.blackness($backgroundColor) > $code-block-theme-dark-threshhold
) {
  /*! dark */
  .reveal div.callout.callout-style-default .callout-title {
    color: #222;
  }
} @else {
  /*! light */
}

/* override theme.scss style for the default styles
 
  Note the need for both the case sensitive flag and the
  case-oblivious selectors. This is a workaround for what's
  apparently a Chrome and Safari bug:

  https://github.com/quarto-dev/quarto-cli/issues/1902#issuecomment-1219783059
 */

.reveal ol[type="a"] {
  list-style-type: lower-alpha;
}

.reveal ol[type="a" s] {
  list-style-type: lower-alpha;
}

.reveal ol[type="A" s] {
  list-style-type: upper-alpha;
}

.reveal ol[type="i"] {
  list-style-type: lower-roman;
}

.reveal ol[type="i" s] {
  list-style-type: lower-roman;
}

.reveal ol[type="I" s] {
  list-style-type: upper-roman;
}

.reveal ol[type="1"] {
  list-style-type: decimal;
}

// https://github.com/quarto-dev/quarto-cli/issues/2834
// temporary workaround while we come up with a pure CSS solution

.reveal ul.task-list {
  list-style: none;
}
.reveal ul.task-list li input[type="checkbox"] {
  width: 2em;
  height: 2em;
  margin: 0 1em 0.5em -1.6em;
  vertical-align: middle;
}

// https://github.com/quarto-dev/quarto-cli/issues/3380

div.cell-output-display div.pagedtable-wrapper table.table {
  font-size: 0.6em;
}

.reveal .code-annotation-container-hidden {
  display: none;
}

.reveal code.sourceCode button.code-annotation-anchor,
.reveal code.sourceCode .code-annotation-anchor {
  font-family: $font-family-monospace;
  color: var(--quarto-hl-co-color);
  border: solid var(--quarto-hl-co-color) 1px;
  border-radius: 50%;
  font-size: 0.7em;
  line-height: 1.2em;
  margin-top: 2px;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
}

.reveal code.sourceCode button.code-annotation-anchor {
  cursor: pointer;
}

.reveal code.sourceCode a.code-annotation-anchor {
  text-align: center;
  vertical-align: middle;
  text-decoration: none;
  cursor: default;
  height: 1.2em;
  width: 1.2em;
}

.reveal code.sourceCode.fragment a.code-annotation-anchor {
  left: auto;
}

.reveal #code-annotation-line-highlight-gutter {
  width: 100%;
  border-top: solid var(--quarto-hl-co-color) 1px;
  border-bottom: solid var(--quarto-hl-co-color) 1px;
  z-index: 2;
}

.reveal #code-annotation-line-highlight {
  margin-left: -8em;
  width: calc(100% + 4em);
  border-top: solid var(--quarto-hl-co-color) 1px;
  border-bottom: solid var(--quarto-hl-co-color) 1px;
  z-index: 2;
  margin-bottom: -2px;
}

.reveal code.sourceCode .code-annotation-anchor.code-annotation-active {
  background-color: var(--quarto-hl-normal-color, #aaaaaa);
  border: solid var(--quarto-hl-normal-color, #aaaaaa) 1px;
  color: rgb(red($code-block-bg), green($code-block-bg), blue($code-block-bg));
  font-weight: bolder;
}

.reveal pre.code-annotation-code {
  padding-top: 0;
  padding-bottom: 0;
  code {
    z-index: 3;
    padding-left: 0px;
  }
}

.reveal dl.code-annotation-container-grid {
  margin-left: 0.1em;
  dt {
    margin-top: 0.65rem;
    font-family: $font-family-monospace;
    border: solid $body-color 1px;
    border-radius: 50%;
    height: 1.3em;
    width: 1.3em;
    line-height: 1.3em;
    font-size: 0.5em;
    text-align: center;
    vertical-align: middle;

    text-decoration: none;
  }

  dd {
    margin-left: 0.25em;
  }
}

/*

  https://github.com/quarto-dev/quarto-cli/issues/4283

  The trick itself is from here:

  https://stackoverflow.com/questions/8720931/can-css-detect-the-number-of-children-an-element-has

*/

.reveal .scrollable ol li:first-child:nth-last-child(n + 10),
.reveal .scrollable ol li:first-child:nth-last-child(n + 10) ~ li {
  margin-left: 1em;
}

// Patch to revealjs' pdf.scss to avoid last blank slide
// https://github.com/quarto-dev/quarto-cli/issues/3997
html.print-pdf .reveal .slides .pdf-page:last-child {
  page-break-after: avoid;
}
