/*-- scss:defaults --*/

@import url(./fonts/league-gothic/league-gothic.css);
@import url(https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic);

// fonts
$font-family-sans-serif: Lato, sans-serif !default;

// colors
$body-bg: #000 !default;
$body-color: #fff !default;
$link-color: #13daec !default;
$selection-bg: #ff5e99 !default;
$input-panel-bg: rgba(233, 236, 239, 0.2) !default;

// headings
$presentation-heading-font: "League Gothic", sans-serif !default;
$presentation-heading-text-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2) !default;
$presentation-h1-font-size: 3.77em !default;
$presentation-h1-text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb,
  0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0, 0, 0, 0.1),
  0 0 5px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.3),
  0 3px 5px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.25),
  0 20px 20px rgba(0, 0, 0, 0.15) !default;
$presentation-heading-text-transform: uppercase !default;

// code blocks
$code-block-bg: transparent !default;

/*-- scss:mixins --*/

// Background generator
@mixin bodyBackground() {
  @include radial-gradient(rgba(28, 30, 32, 1), rgba(85, 90, 95, 1));
}
