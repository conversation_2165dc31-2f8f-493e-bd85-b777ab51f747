/*-- scss:defaults --*/

@import url(./fonts/league-gothic/league-gothic.css);
@import url(https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic);

// fonts
$font-family-sans-serif: Lato, sans-serif !default;

// colors
$body-bg: #f7f3de !default;
$body-color: #333 !default;
$link-color: #8b743d !default;
$selection-bg: rgba(79, 64, 28, 0.99) !default;

// headings
$presentation-heading-font: "League Gothic", sans-serif !default;
$presentation-heading-text-transform: uppercase !default;
$presentation-h1-font-size: 3.77em !default;
$presentation-h1-text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb,
  0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0, 0, 0, 0.1),
  0 0 5px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.3),
  0 3px 5px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.25),
  0 20px 20px rgba(0, 0, 0, 0.15);

// code blocks
$code-block-bg: transparent !default;

/*-- scss:mixins --*/

@mixin bodyBackground() {
  @include radial-gradient(rgba(247, 242, 211, 1), rgba(255, 255, 255, 1));
}
