<!DOCTYPE html>
<html$if(lang)$ lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta charset="utf-8">
$if(quarto-version)$
  <meta name="generator" content="quarto-$quarto-version$" />
$else$
  <meta name="generator" content="quarto" />
$endif$

$for(author-meta)$
  <meta name="author" content="$author-meta$">
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$">
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$">
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, minimal-ui">
  <link rel="stylesheet" href="$revealjs-url$/dist/reset.css">
  <link rel="stylesheet" href="$revealjs-url$/dist/reveal.css">
  <style>
    $styles.html()$
  </style>
$if(theme)$
  <link rel="stylesheet" href="$revealjs-url$/dist/theme/$theme$.css">
$else$
  <link rel="stylesheet" href="$revealjs-url$/dist/theme/black.css">
$endif$
$for(css)$
  <link rel="stylesheet" href="$css$"/>
$endfor$
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
  <div class="reveal">
    <div class="slides">

$if(title)$
$title-slide.html()$
$endif$
$if(toc)$
$toc-slide.html()$
$endif$

$body$
    </div>
  </div>

  <script>window.backupDefine = window.define; window.define = undefined;</script>
  <script src="$revealjs-url$/dist/reveal.js"></script>
  <!-- reveal.js plugins -->
  $for(reveal-jsscripts)$<script src="$reveal-jsscripts$"></script>
  $endfor$

  <script src="$revealjs-url$/plugin/notes/notes.js"></script>
  <script src="$revealjs-url$/plugin/search/search.js"></script>
  <script src="$revealjs-url$/plugin/zoom/zoom.js"></script>
$if(mathjax)$
  <script src="$revealjs-url$/plugin/math/math.js"></script>
$endif$
  <script>window.define = window.backupDefine; window.backupDefine = undefined;</script>

  <script>

      // Full list of configuration options available at:
      // https://revealjs.com/config/
      Reveal.initialize({ 
        // Display controls in the bottom right corner
        controls: $controls$,

        // Help the user learn the controls by providing hints, for example by
        // bouncing the down arrow when they first encounter a vertical slide
        controlsTutorial: $controlsTutorial$,

        // Determines where controls appear, "edges" or "bottom-right"
        controlsLayout: '$controlsLayout$',

        // Visibility rule for backwards navigation arrows; "faded", "hidden"
        // or "visible"
        controlsBackArrows: '$controlsBackArrows$',

        // Display a presentation progress bar
        progress: $progress$,

        // Display the page number of the current slide
        slideNumber: $slideNumber$,

        // 'all', 'print', or 'speaker'
        showSlideNumber: '$showSlideNumber$',

        // Add the current slide number to the URL hash so that reloading the
        // page/copying the URL will return you to the same slide
        hash: $hash$,

        // Start with 1 for the hash rather than 0
        hashOneBasedIndex: $hashOneBasedIndex$,

        // Flags if we should monitor the hash and change slides accordingly
        respondToHashChanges: $respondToHashChanges$,

        // Push each slide change to the browser history
        history: $history$,

        // Enable keyboard shortcuts for navigation
        keyboard: $keyboard$,

        // Enable the slide overview mode
        overview: $overview$,

        // Disables the default reveal.js slide layout (scaling and centering)
        // so that you can use custom CSS layout
        disableLayout: $disableLayout$,

        // Vertical centering of slides
        center: $center$,

        // Enables touch navigation on devices with touch input
        touch: $touch$,

        // Loop the presentation
        loop: $loop$,

        // Change the presentation direction to be RTL
        rtl: $rtl$,

        // see https://revealjs.com/vertical-slides/#navigation-mode
        navigationMode: '$navigationMode$',

        // Randomizes the order of slides each time the presentation loads
        shuffle: $shuffle$,

        // Turns fragments on and off globally
        fragments: $fragments$,

        // Flags whether to include the current fragment in the URL,
        // so that reloading brings you to the same fragment position
        fragmentInURL: $fragmentInURL$,

        // Flags if the presentation is running in an embedded mode,
        // i.e. contained within a limited portion of the screen
        embedded: $embedded$,

        // Flags if we should show a help overlay when the questionmark
        // key is pressed
        help: $help$,

        // Flags if it should be possible to pause the presentation (blackout)
        pause: $pause$,

        // Flags if speaker notes should be visible to all viewers
        showNotes: $showNotes$,

        // Global override for autoplaying embedded media (null/true/false)
        autoPlayMedia: $autoPlayMedia$,

        // Global override for preloading lazy-loaded iframes (null/true/false)
        preloadIframes: $preloadIframes$,

        // Number of milliseconds between automatically proceeding to the
        // next slide, disabled when set to 0, this value can be overwritten
        // by using a data-autoslide attribute on your slides
        autoSlide: $autoSlide$,

        // Stop auto-sliding after user input
        autoSlideStoppable: $autoSlideStoppable$,

        // Use this method for navigation when auto-sliding
        autoSlideMethod: $autoSlideMethod$,

        // Specify the average time in seconds that you think you will spend
        // presenting each slide. This is used to show a pacing timer in the
        // speaker view
        defaultTiming: $defaultTiming$,

        // Enable slide navigation via mouse wheel
        mouseWheel: $mouseWheel$,

        // The display mode that will be used to show slides
        display: '$display$',

        // Hide cursor if inactive
        hideInactiveCursor: $hideInactiveCursor$,

        // Time before the cursor is hidden (in ms)
        hideCursorTime: $hideCursorTime$,

        // Opens links in an iframe preview overlay
        previewLinks: $previewLinks$,

        // Transition style (none/fade/slide/convex/concave/zoom)
        transition: '$transition$',

        // Transition speed (default/fast/slow)
        transitionSpeed: '$transitionSpeed$',

        // Transition style for full page slide backgrounds
        // (none/fade/slide/convex/concave/zoom)
        backgroundTransition: '$backgroundTransition$',

        // Number of slides away from the current that are visible
        viewDistance: $viewDistance$,

        // Number of slides away from the current that are visible on mobile
        // devices. It is advisable to set this to a lower number than
        // viewDistance in order to save resources.
        mobileViewDistance: $mobileViewDistance$,
$if(parallaxBackgroundImage)$

        // Parallax background image
        parallaxBackgroundImage: '$parallaxBackgroundImage/nowrap$', // e.g. "'https://s3.amazonaws.com/hakim-static/reveal-js/reveal-parallax-1.jpg'"
$else$
$if(background-image)$

       // Parallax background image
       parallaxBackgroundImage: '$background-image$', // e.g. "'https://s3.amazonaws.com/hakim-static/reveal-js/reveal-parallax-1.jpg'"
$endif$
$endif$
$if(parallaxBackgroundSize)$

        // Parallax background size
        parallaxBackgroundSize: '$parallaxBackgroundSize/nowrap$', // CSS syntax, e.g. "2100px 900px"
$endif$
$if(parallaxBackgroundHorizontal)$

        // Amount to move parallax background (horizontal and vertical) on slide change
        // Number, e.g. 100
        parallaxBackgroundHorizontal: $parallaxBackgroundHorizontal/nowrap$,
$endif$
$if(parallaxBackgroundVertical)$

        parallaxBackgroundVertical: $parallaxBackgroundVertical/nowrap$,
$endif$
$if(width)$

        // The "normal" size of the presentation, aspect ratio will be preserved
        // when the presentation is scaled to fit different resolutions. Can be
        // specified using percentage units.
        width: $width$,
$endif$
$if(height)$

        height: $height$,
$endif$
$if(margin)$

        // Factor of the display size that should remain empty around the content
        margin: $margin$,
$endif$
$if(minScale)$

        // Bounds for smallest/largest possible scale to apply to content
        minScale: $minScale$,
$endif$
$if(maxScale)$

        maxScale: $maxScale$,
$endif$
$if(mathjax)$

        math: {
          mathjax: '$mathjaxurl$',
          config: 'TeX-AMS_HTML-full',
          tex2jax: {
            inlineMath: [['\\(','\\)']],
            displayMath: [['\\[','\\]']],
            balanceBraces: true,
            processEscapes: false,
            processRefs: true,
            processEnvironments: true,
            preview: 'TeX',
            skipTags: ['script','noscript','style','textarea','pre','code'],
            ignoreClass: 'tex2jax_ignore',
            processClass: 'tex2jax_process'
          },
        },
$endif$

        // reveal.js plugins
        plugins: [
$if(mathjax)$
          RevealMath,
$endif$
          RevealNotes,
          RevealSearch,
          RevealZoom
        ]
      });
    </script>
  $for(include-after)$
  $include-after$
  $endfor$
  </body>
</html>
