<sub-article article-type="notebook" id="$jats-subarticle-id$">
<front-stub>
$if(title)$
<title-group>
<article-title>$title$</article-title>
$if(subtitle)$
<subtitle>${subtitle}</subtitle>
$endif$
</title-group>
$endif$
${ authors.xml() }
$if(abstract)$
<abstract>
$abstract$
</abstract>
$endif$
</front-stub>

<body>
$body$
</body>

$for(include-after)$
$include-after$
$endfor$


<back>
$if(back)$
$back$
$endif$
</back>


</sub-article>