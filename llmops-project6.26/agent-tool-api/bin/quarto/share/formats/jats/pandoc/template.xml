<?xml version="1.0" encoding="utf-8" ?>
$if(xml-stylesheet)$
<?xml-stylesheet type="text/xsl" href="$xml-stylesheet$"?>
$endif$
$if(quarto-internal.jats-dtd)$
<!DOCTYPE article PUBLIC "$quarto-internal.jats-dtd.name$" "$quarto-internal.jats-dtd.location$">
$endif$

$if(citation.jats-type)$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="$citation.jats-type$">
$elseif(citation.type)$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="$citation.type$">
$else$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="other">
$endif$

<front>
$front.xml()$
</front>

<body>
$body$
</body>

<back>
$if(back)$
$back$
$endif$
</back>

$for(include-after)$
$include-after$
$endfor$

</article>