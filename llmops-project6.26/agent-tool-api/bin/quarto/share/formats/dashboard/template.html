<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="$lang$" xml:lang="$lang$"$if(dir)$ dir="$dir$"$endif$>

<head>

$metadata.html()$

<style>
$styles.html()$
</style>

<!-- htmldependencies:E3FAD763 -->
$for(header-includes)$
$header-includes$
$endfor$

$if(math)$
$if(mathjax)$
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
$endif$
  $math$
$endif$

$for(css)$
<link rel="stylesheet" href="$css$" />
$endfor$
</head>

<body class="quarto-dashboard">

$title-block.html()$

$for(include-before)$
$include-before$
$endfor$

$body$

$for(include-after)$
$include-after$
$endfor$

<script type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (_event) {
  if (window.bslib.Card) {
    window.bslib.Card.initializeAllCards();
  }
}); 
</script>
  
</body>

</html>
