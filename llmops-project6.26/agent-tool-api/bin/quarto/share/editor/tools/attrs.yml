# dashboardDiv

- group: aDashboardDiv
  contexts: [div]
  formats: [dashboard]
  completions:
    - value: ".card"
      doc: Markdown content with its own card
    - value: ".valuebox"
      doc: Prominently display a value

- group: aDashboardCard
  contexts: [div]
  formats: [dashboard]
  filter: "\\.card"
  completions:
    - value: 'title="$0"'
      doc: Title for markdown content card
    - value: 'padding="$0"'
      doc: Padding around card
    - value: 'expandable="true"'
      doc: Make card contents expandable
    - value: 'expandable="false"'
      doc: Do not make card contents expandable
    - value: 'width="$0"'
      doc: Width (% or absolute pixels)
    - value: 'height="$0"'
      doc: Height (% or absolute pixels)

- group: aDashboardValueBox
  contexts: [div]
  formats: [dashboard]
  filter: "\\.valuebox"
  completions:
    - value: 'icon="$0"'
      doc: Bootstrap icon for value box
    - value: 'color="$0"'
    - value: 'color="primary"'
    - value: 'color="secondary"'
    - value: 'color="success"'
    - value: 'color="info"'
    - value: 'color="warning"'
    - value: 'color="danger"'
    - value: 'color="light"'
    - value: 'color="dark"'

- group: aDashboardSidebar
  contexts: [heading]
  formats: [dashboard]
  filter: "^##? "
  completions:
    - value: ".sidebar"
      doc: Layout contents in a sidebar

- group: aDashboardPage
  contexts: [heading]
  formats: [dashboard]
  filter: "^# "
  completions:
    - value: 'orientation="rows"'
      doc: Orient layout by rows
    - value: 'orientation="columns"'
      doc: Orient layout by columns

- group: aDashboardHeading
  contexts: [heading]
  formats: [dashboard]
  filter: "^#{2,} "
  completions:
    - value: ".fill"
      doc: Contents should fill available layout space
    - value: ".flow"
      doc: Contents should flow to their natural size
    - value: ".tabset"
      doc: Arrange contents into tabs
    - value: 'width="$0"'
      doc: Width (% or absolute pixels)
    - value: 'height="$0"'
      doc: Height (% or absolute pixels)

## callouts

- group: "callout"
  contexts: [div]
  completions:
    - value: .callout
      doc: "Simple callout with no color, icon, or header"
    - value: .callout-note
      doc: "Include additional explanation or context."
    - value: .callout-tip
      doc: "Provide a productivity tip or other helpful guideance. "
    - value: .callout-important
      doc: "Emphasize content that readers should be sure to read."
    - value: .callout-caution
      doc: "Advise the reader to act carefully (i.e., exercise care)."
    - value: .callout-warning
      doc: "Inform the reader of danger, harm, or consequences that exist."

- group: "calloutAttribs"
  contexts: [div]
  filter: "\\.callout"
  completions:
    - value: 'title="$0"'
      doc: "Title displayed in callout header"
    - value: 'icon="true"'
      doc: Include an icon in the callout.
    - value: 'icon="false"'
      doc: Do not include an icon in the callout.
    - value: 'appearance="default"'
      doc: The default appearance with colored header and an icon.
    - value: 'appearance="simple"'
      doc: "A lighter weight appearance that doesn’t include a colored header background."
    - value: 'appearance="minimal"'
      doc: |
        A minimal treatment that applies borders to the callout, but doesn’t include 
        a header background color or icon.
    - value: 'collapse="true"'
      doc: Make the callout collapsable (initially collapsed).
    - value: 'collapse="false"'
      doc: Make the callout collapsable (initially expanded).

## content-visibile

- group: "content"
  contexts: [div]
  completions:
    - value: '.content-visible when-format="$0"'
      doc: "Make content visible for a given format."
    - value: '.content-visible unless-format="$0"'
      doc: "Make content visible except for a given format."
    - value: '.content-hidden when-format="$0"'
      doc: "Make content hidden for a given format."
    - value: '.content-hidden unless-format="$0"'
      doc: "Make content hidden except for a given format."
    - value: '.content-visible when-profile="$0"'
      doc: "Make content visible for a given profile."
    - value: '.content-visible unless-profile="$0"'
      doc: "Make content visible except for a given profile."
    - value: '.content-hidden when-profile="$0"'
      doc: "Make content hidden for a given profile."
    - value: '.content-hidden unless-profile"$0"'
      doc: "Make content hidden except for a given profile."

- group: "contentAttribs"
  contexts: [div]
  completions:
    - value: 'when-format="$0"'
      doc: "Show or hide content when format matches"
    - value: 'unless-format="$0"'
      doc: "Show or hide content unless format matches"
    - value: 'when-profile="$0"'
      doc: "Show or hide content when profile matches"
    - value: 'unless-profile="$0"'
      doc: "Show or hide content unless profile matches"

## columns

- group: "columns"
  contexts: [div, figure]
  completions:
    - value: ".column-margin"
      doc: Position content in the right margin
    - value: ".column-body"
      doc: Default column for article content
    - value: ".column-body-outset"
      doc: Extend content slightly outside the bounds of the body
    - value: ".column-body-outset-left"
      doc: Extend content slightly outside the left side of the body
    - value: ".column-body-outset-right"
      doc: Extend content slightly outside the right side of the body
    - value: ".column-page"
      doc: Extend content to a wider region
    - value: ".column-page-left"
      doc: Extend content on the left to a wider region
    - value: ".column-page-right"
      doc: Extend content on the right to a wider region
    - value: ".column-screen-inset"
      doc: Extend content to full width (with a small margin)
    - value: ".column-screen-inset-shaded"
      doc: Extend content to full width (with a small margin and shading)
    - value: ".column-screen-inset-left"
      doc: Extend content fully to the left (with a small margin)
    - value: ".column-screen-inset-right"
      doc: Extend content fully to the right (with a small margin)
    - value: ".column-screen"
      doc: Extend content to full width
    - value: ".column-screen-left"
      doc: Extend content fully to the left
    - value: ".column-screen-right"
      doc: Extend content fully to the right

## panels

- group: "panels"
  contexts: [div]
  completions:
    - value: .panel-tabset
      doc: Panel containing a set of tabs
    - value: .panel-sidebar
      doc: Panel that appears to the side of the main content
    - value: .panel-fill
      doc: Panel that fills the region not taken by the sidebar
    - value: .panel-center
      doc: Panel that centers content in the region not taken by the sidebar
    - value: .panel-input
      doc: Panel for horizontal layout of input controls

## theorem

- group: "theorem"
  contexts: [div]
  completions:
    - value: 'name="$0"'
    - value: .proof
      doc: Proof environment
    - value: .remark
      doc: Remark environment
    - value: .solution
      doc: Solution environment

## layout

- group: "layout"
  contexts: [div]
  completions:
    - value: 'layout="$0"'
      doc: Custom layout specification for content
    - value: 'layout-ncol="$0"'
      doc: Layout output blocks into columns
    - value: 'layout-nrow="$0"'
      doc: Layout output blocks into rows
    - value: 'layout-align="default"'
      doc: Use default horizontal alignment
    - value: 'layout-align="left"'
      doc: Use left horizontal alignment
    - value: 'layout-align="center"'
      doc: Use center horizontal alignment
    - value: 'layout-align="right"'
      doc: Use right horizontal alignment
    - value: 'layout-valign="default"'
      doc: Use default vertical alignment
    - value: 'layout-valign="top"'
      doc: Use top vertical alignment
    - value: 'layout-valign="center"'
      doc: Use center vertical alignment
    - value: 'layout-valign="bottom"'
      doc: Use bottom vertical alignment

## style

- group: div-style
  contexts: [div]
  completions:
    - value: 'style="$0"'
      doc: CSS style attributes

## figure

- group: figure
  contexts: [figure]
  completions:
    - value: 'fig-alt="$0"'
      doc: Alternative text for figure
    - value: 'fig-align="default"'
      doc: Default figure alignment
    - value: 'fig-align="left"'
      doc: Left figure alignment
    - value: 'fig-align="center"'
      doc: Center figure alignment
    - value: 'fig-align="right"'
      doc: Right figure alignment
    - value: 'fig-env="$0"'
      doc: LaTeX environment for figure
    - value: 'fig-pos="$0"'
      doc: |
        LaTeX figure position arrangement to be used in `\\begin{figure}[]`
    - value: 'fig-scap="$0"'
      doc: LaTeX short caption
    - value: 'style="$0"'
      doc: CSS style attributes
    - value: 'width="$0"'
    - value: 'height="$0"'

## heading

- group: heading
  contexts: [heading]
  completions:
    - value: .unnumbered
      doc: Do not apply numbering to heading
    - value: .unlisted
      doc: Do not include heading in table of contents

## codeblock

- group: codeblock
  contexts: [codeblock]
  completions:
    - value: 'code-fold="true"'
      doc: |
        Fold code into an HTML `<details>` tag so the user can display it on-demand
    - value: 'code-fold="show"'
      doc: Show code and allow it to be optionally folded out of view
    - value: 'code-fold="false"'
      doc: Do not fold this code block
    - value: 'code-summary="$0"'
      doc: Summary text for folded code (defaults to 'Code')
    - value: 'code-line-numbers="true"'
      doc: Show line numbers for code block
    - value: 'code-line-numbers="false"'
      doc: Do not show line numbers for code block
    - value: 'shortcodes="false"'
      doc: Do not process shortcodes in this code block

## hidden

- group: hidden
  contexts: [div, heading, figure, codeblock]
  completions:
    - value: .hidden
      doc: Hide content from view

# revealDiv

- group: revealDiv
  contexts: [div]
  formats: [revealjs]
  completions:
    - value: .incremental
      doc: Display bullet list incrementally
    - value: .nonincremental
      doc: Display bullets list all at once
    - value: .columns
      doc: Split content into multiple columns
    - value: .column
      doc: Provide column for `.columns` div
    - value: .notes
      doc: Slide speaker notes
    - value: .footer
      doc: Slide footer text
    - value: .r-fit-text
      doc: Fit div contents to available space

- group: revealFigure
  contexts: [figure]
  formats: [revealjs]
  completions:
    - value: .r-stretch
      doc: Stretch figure to fill available space on slide

- group: revealContent
  contexts: [div, figure]
  formats: [revealjs]
  completions:
    - value: .absolute
      doc: Absolutely position content
    - value: 'top="$0"'
      doc: Position relative to top of slide
    - value: 'left="$0"'
      doc: Position relative to left of slide
    - value: 'bottom="$0"'
      doc: Position relative to bottom of slide
    - value: 'right="$0"'
      doc: Position relative to right of slide

- group: revealFragment
  contexts: [figure, div]
  formats: [revealjs]
  completions:
    - value: .fragment
      doc: Incrementally reveal content
    - value: .fade-up
      doc: Slide up while fading in
    - value: .fade-down
      doc: Slide down while fading in
    - value: .fade-left
      doc: Slide left while fading in
    - value: .fade-right
      doc: Slide right while fading in
    - value: .fade-in-then-out
      doc: Fades in, then out on the next step
    - value: .fade-in-then-semi-out
      doc: Fades in, then to 50% on the next step
    - value: .grow
      doc: Scale up
    - value: .semi-fade-out
      doc: Fade out to 50%
    - value: .shrink
      doc: Scale down
    - value: .strike
      doc: Strike through
    - value: .highlight-red
      doc: Turn text red
    - value: .highlight-green
      doc: Turn text green
    - value: .highlight-blue
      doc: Turn text blue
    - value: .highlight-current-red
      doc: Turn text red, then back to original on next step
    - value: .highlight-current-green
      doc: Turn text green, then back to original on next step
    - value: .highlight-current-blue
      doc: Turn text blue, then back to original on next step

- group: revealHeading
  contexts: [heading]
  formats: [revealjs]
  completions:
    - value: .smaller
      doc: Use a smaller text side for slide
    - value: .scrollable
      doc: Make slide contents scrollable
    - value: .nostretch
      doc: Do not automatically stretch images on this slide
    - value: 'autoslide="$0"'
      doc: Autoslide duration for slide (in milliseconds)
    - value: 'background-color="$0"'
      doc: Slide background color
    - value: 'background-image="$0"'
      doc: URL of the image to show. GIFs restart when the slide opens.
    - value: 'background-size="$0"'
      doc: |
        Background size (see [background-size](https://developer.mozilla.org/docs/Web/CSS/background-size) on MDN). 
        Defaults to `cover`.
    - value: 'background-position="$0"'
      doc: |
        Background position (see [background-position](https://developer.mozilla.org/docs/Web/CSS/background-position) on MDN). 
        Defaults to `center`.
    - value: 'background-repeat="$0"'
      doc: |
        Background repeat (see [background-repeat](https://developer.mozilla.org/docs/Web/CSS/background-repeat) on MDN). 
        Defaults to `no-repeat`.
    - value: 'background-opacity="$0"'
      doc: |
        Opacity of the background image on a 0-1 scale. 0 is transparent and 1 is fully opaque. 
        Defaults to 1.
    - value: 'background-video="$0"'
      doc: A single video source, or a comma separated list of video sources.
    - value: 'background-video-loop="$0"'
      doc: Flags if the video should play repeatedly. Defaults to `false`.
    - value: 'background-video-muted="$0"'
      doc: Flags if the audio should be muted. Defaults to `false`.
    - value: 'background-iframe="$0"'
      doc: URL of the iframe to load
    - value: background-interactive
      doc:
        Include this attribute to make it possible to interact with the iframe contents.
        Enabling this will prevent interaction with the slide content.

- group: revealTransition
  contexts: [heading]
  formats: [revealjs]
  completions:
    - value: 'transition="none"'
      doc: No transition (switch instantly)
    - value: 'transition="fade"'
      doc: Cross fade
    - value: 'transition="slide"'
      doc: Slide horizontally
    - value: 'transition="convex"'
      doc: Slide at a convex angle
    - value: 'transition="concave"'
      doc: Slide at a concave angle
    - value: 'transition="zoom"'
      doc: Scale the incoming slide so it grows in from the center of the screen
    - value: 'transition-speed="default"'
      doc: Use default transition speed
    - value: 'transition-speed="fast"'
      doc: Use a faster transition speed
    - value: 'transition-speed="slow"'
      doc: Use a slower transition speed
    - value: 'visibility="hidden"'
      doc: Hide a slide
    - value: 'visibility="uncounted"'
      doc: |
        Show the slide but don't have it count towards the total slides
        (useful for appendix slides)

- group: revealAutoAnimateSlide
  contexts: [heading]
  formats: [revealjs]
  completions:
    - value: 'auto-animate="true"'
      doc: Automatically animate elements across this slide and adjacent slide(s).
    - value: 'auto-animate-easing="$0"'
      doc: A CSS [easing](https://developer.mozilla.org/en-US/docs/Web/CSS/easing-function) function. Defaults to `ease`.
    - value: 'auto-animate-unmatched="$0"'
      doc: |
        Determines whether elements with no matching auto-animate target should fade in. 
        Defaults to `true`. Set to `false` to make them appear instantly.
    - value: 'auto-animate-duration="$0"'
      doc: |
        Animation duration in seconds (defaults to 1.0)
    - value: 'auto-animate-id="$0"'
      doc: An id tying auto-animate slides together
    - value: auto-animate-restart
      doc: Breaks apart two adjacent auto-animate slides (even with the same id)

- group: revealAutoAnimateElement
  contexts: [div, figure]
  formats: [revealjs]
  completions:
    - value: 'data-id="$0"'
      doc: |
        Provide objects that you want to animate between a matching data-id attribute
        (useful in situations where automatic matching is not feasible)
    - value: 'auto-animate-delay="$0"'
      doc: Animation delay in seconds (defaults to 0)
