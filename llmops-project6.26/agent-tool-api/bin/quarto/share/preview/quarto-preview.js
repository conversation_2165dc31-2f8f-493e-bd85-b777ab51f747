(function(){"use strict";try{if(typeof document<"u"){var a=document.createElement("style");a.appendChild(document.createTextNode(".ansi-display{font-family:monospace;white-space:pre-wrap;--ansiBlack: #000000;--ansiRed: #cd3131;--ansiGreen: #00BC00;--ansiYellow: #949800;--ansiBlue: #0451a5;--ansiMagenta: #bc05bc;--ansiCyan: #0598bc;--ansiWhite: #555555;--ansiBrightBlack: #666666;--ansiBrightRed: #cd3131;--ansiBrightGreen: #14CE14;--ansiBrightYellow: #b5ba00;--ansiBrightBlue: #0451a5;--ansiBrightMagenta: #bc05bc;--ansiBrightCyan: #0598bc;--ansiBrightWhite: #a5a5a5}.dark-mode .ansi-display{--ansiBlack: #000000;--ansiRed: #cd3131;--ansiGreen: #0DBC79;--ansiYellow: #e5e510;--ansiBlue: #2472c8;--ansiMagenta: #bc3fbc;--ansiCyan: #11a8cd;--ansiWhite: #e5e5e5;--ansiBrightBlack: #666666;--ansiBrightRed: #f14c4c;--ansiBrightGreen: #23d18b;--ansiBrightYellow: #f5f543;--ansiBrightBlue: #3b8eea;--ansiBrightMagenta: #d670d6;--ansiBrightCyan: #29b8db;--ansiBrightWhite: #e5e5e5}@keyframes ansi-displaly-run-blink{50%{opacity:0}}.fui-FluentProvider h2{border-bottom:none;padding-bottom:0}")),document.head.appendChild(a)}}catch(i){console.error("vite-plugin-css-injected-by-js",i)}})();
(function(ot,Me){typeof exports=="object"&&typeof module<"u"?Me(exports):typeof define=="function"&&define.amd?define(["exports"],Me):(ot=typeof globalThis<"u"?globalThis:ot||self,Me(ot.QuartoPreview={}))})(this,function(ot){"use strict";var yS=Object.defineProperty;var kS=(ot,Me,Pr)=>Me in ot?yS(ot,Me,{enumerable:!0,configurable:!0,writable:!0,value:Pr}):ot[Me]=Pr;var fe=(ot,Me,Pr)=>(kS(ot,typeof Me!="symbol"?Me+"":Me,Pr),Pr);function Me(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(typeof o!="string"&&!Array.isArray(o)){for(const n in o)if(n!=="default"&&!(n in e)){const i=Object.getOwnPropertyDescriptor(o,n);i&&Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:()=>o[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function Pr(e){const t=window.location.protocol==="https:"?"wss:":"ws:";let r=window.location.pathname;/\/$/.test(r)||(r+="/");let o=!0;addEventListener("beforeunload",()=>{o=!1});const n=new WebSocket(t+"//"+window.location.host+r);n.onopen=()=>{console.log("Socket connection open. Listening for events."),Jv(n,()=>o)};const i=()=>{o=!1,n.send("stop")},l=e.map(s=>s(i));return n.onmessage=s=>{for(const a of l)if(a(s))break},()=>{try{n.close()}catch(s){console.error(s)}}}function Jv(e,t){e.onclose=()=>{t()&&(console.log("Socket connection closed. Reloading."),window.location.reload())}}function Zv(){return()=>{const e=t=>t.replace(/\/index\.html/,"/");return t=>{if(t.data.startsWith("reload")){let r=e(t.data.replace(/^reload/,""));if(r){const o=window.location.pathname.match(/^.*?\/p\/\w+\//)||window.location.pathname.match(/^.*?\/user\/[\w\d]+\/proxy\/\d+\//);o&&(r=o+r.slice(1))}return r&&r!==e(window.location.pathname)?window.location.replace(r):window.location.reload(),!0}else return!1}}}function Xl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var x={exports:{}},W={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vo=Symbol.for("react.element"),Gv=Symbol.for("react.portal"),e0=Symbol.for("react.fragment"),t0=Symbol.for("react.strict_mode"),r0=Symbol.for("react.profiler"),o0=Symbol.for("react.provider"),n0=Symbol.for("react.context"),i0=Symbol.for("react.forward_ref"),l0=Symbol.for("react.suspense"),s0=Symbol.for("react.memo"),a0=Symbol.for("react.lazy"),mc=Symbol.iterator;function u0(e){return e===null||typeof e!="object"?null:(e=mc&&e[mc]||e["@@iterator"],typeof e=="function"?e:null)}var yc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},kc=Object.assign,bc={};function io(e,t,r){this.props=e,this.context=t,this.refs=bc,this.updater=r||yc}io.prototype.isReactComponent={},io.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},io.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function wc(){}wc.prototype=io.prototype;function Yl(e,t,r){this.props=e,this.context=t,this.refs=bc,this.updater=r||yc}var Ql=Yl.prototype=new wc;Ql.constructor=Yl,kc(Ql,io.prototype),Ql.isPureReactComponent=!0;var Bc=Array.isArray,_c=Object.prototype.hasOwnProperty,Jl={current:null},xc={key:!0,ref:!0,__self:!0,__source:!0};function Sc(e,t,r){var o,n={},i=null,l=null;if(t!=null)for(o in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)_c.call(t,o)&&!xc.hasOwnProperty(o)&&(n[o]=t[o]);var s=arguments.length-2;if(s===1)n.children=r;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];n.children=a}if(e&&e.defaultProps)for(o in s=e.defaultProps,s)n[o]===void 0&&(n[o]=s[o]);return{$$typeof:Vo,type:e,key:i,ref:l,props:n,_owner:Jl.current}}function c0(e,t){return{$$typeof:Vo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Zl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Vo}function d0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Ec=/\/+/g;function Gl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?d0(""+e.key):t.toString(36)}function Gn(e,t,r,o,n){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Vo:case Gv:l=!0}}if(l)return l=e,n=n(l),e=o===""?"."+Gl(l,0):o,Bc(n)?(r="",e!=null&&(r=e.replace(Ec,"$&/")+"/"),Gn(n,t,r,"",function(u){return u})):n!=null&&(Zl(n)&&(n=c0(n,r+(!n.key||l&&l.key===n.key?"":(""+n.key).replace(Ec,"$&/")+"/")+e)),t.push(n)),1;if(l=0,o=o===""?".":o+":",Bc(e))for(var s=0;s<e.length;s++){i=e[s];var a=o+Gl(i,s);l+=Gn(i,t,r,a,n)}else if(a=u0(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=o+Gl(i,s++),l+=Gn(i,t,r,a,n);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function ei(e,t,r){if(e==null)return e;var o=[],n=0;return Gn(e,o,"","",function(i){return t.call(r,i,n++)}),o}function f0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Pe={current:null},ti={transition:null},g0={ReactCurrentDispatcher:Pe,ReactCurrentBatchConfig:ti,ReactCurrentOwner:Jl};function Nc(){throw Error("act(...) is not supported in production builds of React.")}W.Children={map:ei,forEach:function(e,t,r){ei(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return ei(e,function(){t++}),t},toArray:function(e){return ei(e,function(t){return t})||[]},only:function(e){if(!Zl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},W.Component=io,W.Fragment=e0,W.Profiler=r0,W.PureComponent=Yl,W.StrictMode=t0,W.Suspense=l0,W.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=g0,W.act=Nc,W.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=kc({},e.props),n=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Jl.current),t.key!==void 0&&(n=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)_c.call(t,a)&&!xc.hasOwnProperty(a)&&(o[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)o.children=r;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];o.children=s}return{$$typeof:Vo,type:e.type,key:n,ref:i,props:o,_owner:l}},W.createContext=function(e){return e={$$typeof:n0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:o0,_context:e},e.Consumer=e},W.createElement=Sc,W.createFactory=function(e){var t=Sc.bind(null,e);return t.type=e,t},W.createRef=function(){return{current:null}},W.forwardRef=function(e){return{$$typeof:i0,render:e}},W.isValidElement=Zl,W.lazy=function(e){return{$$typeof:a0,_payload:{_status:-1,_result:e},_init:f0}},W.memo=function(e,t){return{$$typeof:s0,type:e,compare:t===void 0?null:t}},W.startTransition=function(e){var t=ti.transition;ti.transition={};try{e()}finally{ti.transition=t}},W.unstable_act=Nc,W.useCallback=function(e,t){return Pe.current.useCallback(e,t)},W.useContext=function(e){return Pe.current.useContext(e)},W.useDebugValue=function(){},W.useDeferredValue=function(e){return Pe.current.useDeferredValue(e)},W.useEffect=function(e,t){return Pe.current.useEffect(e,t)},W.useId=function(){return Pe.current.useId()},W.useImperativeHandle=function(e,t,r){return Pe.current.useImperativeHandle(e,t,r)},W.useInsertionEffect=function(e,t){return Pe.current.useInsertionEffect(e,t)},W.useLayoutEffect=function(e,t){return Pe.current.useLayoutEffect(e,t)},W.useMemo=function(e,t){return Pe.current.useMemo(e,t)},W.useReducer=function(e,t,r){return Pe.current.useReducer(e,t,r)},W.useRef=function(e){return Pe.current.useRef(e)},W.useState=function(e){return Pe.current.useState(e)},W.useSyncExternalStore=function(e,t,r){return Pe.current.useSyncExternalStore(e,t,r)},W.useTransition=function(){return Pe.current.useTransition()},W.version="18.3.1",function(e){e.exports=W}(x);const V=Xl(x.exports),lo=Me({__proto__:null,default:V},[x.exports]);var ri={exports:{}},Xe={},Fc={exports:{}},Cc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(S,T){var D=S.length;S.push(T);e:for(;0<D;){var j=D-1>>>1,$=S[j];if(0<n($,T))S[j]=T,S[D]=$,D=j;else break e}}function r(S){return S.length===0?null:S[0]}function o(S){if(S.length===0)return null;var T=S[0],D=S.pop();if(D!==T){S[0]=D;e:for(var j=0,$=S.length,tr=$>>>1;j<tr;){var Le=2*(j+1)-1,no=S[Le],Ie=Le+1,rr=S[Ie];if(0>n(no,D))Ie<$&&0>n(rr,no)?(S[j]=rr,S[Ie]=D,j=Ie):(S[j]=no,S[Le]=D,j=Le);else if(Ie<$&&0>n(rr,D))S[j]=rr,S[Ie]=D,j=Ie;else break e}}return T}function n(S,T){var D=S.sortIndex-T.sortIndex;return D!==0?D:S.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,f=null,g=3,y=!1,v=!1,k=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function c(S){for(var T=r(u);T!==null;){if(T.callback===null)o(u);else if(T.startTime<=S)o(u),T.sortIndex=T.expirationTime,t(a,T);else break;T=r(u)}}function p(S){if(k=!1,c(S),!v)if(r(a)!==null)v=!0,re(b);else{var T=r(u);T!==null&&ce(p,T.startTime-S)}}function b(S,T){v=!1,k&&(k=!1,m(E),E=-1),y=!0;var D=g;try{for(c(T),f=r(a);f!==null&&(!(f.expirationTime>T)||S&&!A());){var j=f.callback;if(typeof j=="function"){f.callback=null,g=f.priorityLevel;var $=j(f.expirationTime<=T);T=e.unstable_now(),typeof $=="function"?f.callback=$:f===r(a)&&o(a),c(T)}else o(a);f=r(a)}if(f!==null)var tr=!0;else{var Le=r(u);Le!==null&&ce(p,Le.startTime-T),tr=!1}return tr}finally{f=null,g=D,y=!1}}var w=!1,B=null,E=-1,O=5,F=-1;function A(){return!(e.unstable_now()-F<O)}function I(){if(B!==null){var S=e.unstable_now();F=S;var T=!0;try{T=B(!0,S)}finally{T?X():(w=!1,B=null)}}else w=!1}var X;if(typeof h=="function")X=function(){h(I)};else if(typeof MessageChannel<"u"){var L=new MessageChannel,H=L.port2;L.port1.onmessage=I,X=function(){H.postMessage(null)}}else X=function(){_(I,0)};function re(S){B=S,w||(w=!0,X())}function ce(S,T){E=_(function(){S(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(S){S.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,re(b))},e.unstable_forceFrameRate=function(S){0>S||125<S?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<S?Math.floor(1e3/S):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return r(a)},e.unstable_next=function(S){switch(g){case 1:case 2:case 3:var T=3;break;default:T=g}var D=g;g=T;try{return S()}finally{g=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(S,T){switch(S){case 1:case 2:case 3:case 4:case 5:break;default:S=3}var D=g;g=S;try{return T()}finally{g=D}},e.unstable_scheduleCallback=function(S,T,D){var j=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?j+D:j):D=j,S){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=D+$,S={id:d++,callback:T,priorityLevel:S,startTime:D,expirationTime:$,sortIndex:-1},D>j?(S.sortIndex=D,t(u,S),r(a)===null&&S===r(u)&&(k?(m(E),E=-1):k=!0,ce(p,D-j))):(S.sortIndex=$,t(a,S),v||y||(v=!0,re(b))),S},e.unstable_shouldYield=A,e.unstable_wrapCallback=function(S){var T=g;return function(){var D=g;g=T;try{return S.apply(this,arguments)}finally{g=D}}}})(Cc),function(e){e.exports=Cc}(Fc);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var p0=x.exports,Ye=Fc.exports;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Pc=new Set,Ko={};function Tr(e,t){so(e,t),so(e+"Capture",t)}function so(e,t){for(Ko[e]=t,e=0;e<t.length;e++)Pc.add(t[e])}var Lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),es=Object.prototype.hasOwnProperty,h0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Tc={},Dc={};function v0(e){return es.call(Dc,e)?!0:es.call(Tc,e)?!1:h0.test(e)?Dc[e]=!0:(Tc[e]=!0,!1)}function m0(e,t,r,o){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return o?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function y0(e,t,r,o){if(t===null||typeof t>"u"||m0(e,t,r,o))return!0;if(o)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Te(e,t,r,o,n,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=o,this.attributeNamespace=n,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var be={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){be[e]=new Te(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];be[t]=new Te(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){be[e]=new Te(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){be[e]=new Te(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){be[e]=new Te(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){be[e]=new Te(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){be[e]=new Te(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){be[e]=new Te(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){be[e]=new Te(e,5,!1,e.toLowerCase(),null,!1,!1)});var ts=/[\-:]([a-z])/g;function rs(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ts,rs);be[t]=new Te(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ts,rs);be[t]=new Te(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ts,rs);be[t]=new Te(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){be[e]=new Te(e,1,!1,e.toLowerCase(),null,!1,!1)}),be.xlinkHref=new Te("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){be[e]=new Te(e,1,!1,e.toLowerCase(),null,!0,!0)});function os(e,t,r,o){var n=be.hasOwnProperty(t)?be[t]:null;(n!==null?n.type!==0:o||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(y0(t,r,n,o)&&(r=null),o||n===null?v0(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):n.mustUseProperty?e[n.propertyName]=r===null?n.type===3?!1:"":r:(t=n.attributeName,o=n.attributeNamespace,r===null?e.removeAttribute(t):(n=n.type,r=n===3||n===4&&r===!0?"":""+r,o?e.setAttributeNS(o,t,r):e.setAttribute(t,r))))}var It=p0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,oi=Symbol.for("react.element"),ao=Symbol.for("react.portal"),uo=Symbol.for("react.fragment"),ns=Symbol.for("react.strict_mode"),is=Symbol.for("react.profiler"),Oc=Symbol.for("react.provider"),Rc=Symbol.for("react.context"),ls=Symbol.for("react.forward_ref"),ss=Symbol.for("react.suspense"),as=Symbol.for("react.suspense_list"),us=Symbol.for("react.memo"),or=Symbol.for("react.lazy"),zc=Symbol.for("react.offscreen"),Ac=Symbol.iterator;function Xo(e){return e===null||typeof e!="object"?null:(e=Ac&&e[Ac]||e["@@iterator"],typeof e=="function"?e:null)}var ne=Object.assign,cs;function Yo(e){if(cs===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);cs=t&&t[1]||""}return`
`+cs+e}var ds=!1;function fs(e,t){if(!e||ds)return"";ds=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var o=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){o=u}e.call(t.prototype)}else{try{throw Error()}catch(u){o=u}e()}}catch(u){if(u&&o&&typeof u.stack=="string"){for(var n=u.stack.split(`
`),i=o.stack.split(`
`),l=n.length-1,s=i.length-1;1<=l&&0<=s&&n[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(n[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||n[l]!==i[s]){var a=`
`+n[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{ds=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Yo(e):""}function k0(e){switch(e.tag){case 5:return Yo(e.type);case 16:return Yo("Lazy");case 13:return Yo("Suspense");case 19:return Yo("SuspenseList");case 0:case 2:case 15:return e=fs(e.type,!1),e;case 11:return e=fs(e.type.render,!1),e;case 1:return e=fs(e.type,!0),e;default:return""}}function gs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case uo:return"Fragment";case ao:return"Portal";case is:return"Profiler";case ns:return"StrictMode";case ss:return"Suspense";case as:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Rc:return(e.displayName||"Context")+".Consumer";case Oc:return(e._context.displayName||"Context")+".Provider";case ls:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case us:return t=e.displayName||null,t!==null?t:gs(e.type)||"Memo";case or:t=e._payload,e=e._init;try{return gs(e(t))}catch{}}return null}function b0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gs(t);case 8:return t===ns?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function nr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Lc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function w0(e){var t=Lc(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var n=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(l){o=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return o},setValue:function(l){o=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ni(e){e._valueTracker||(e._valueTracker=w0(e))}function Ic(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),o="";return e&&(o=Lc(e)?e.checked?"true":"false":e.value),e=o,e!==r?(t.setValue(e),!0):!1}function ii(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ps(e,t){var r=t.checked;return ne({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r!=null?r:e._wrapperState.initialChecked})}function Mc(e,t){var r=t.defaultValue==null?"":t.defaultValue,o=t.checked!=null?t.checked:t.defaultChecked;r=nr(t.value!=null?t.value:r),e._wrapperState={initialChecked:o,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function jc(e,t){t=t.checked,t!=null&&os(e,"checked",t,!1)}function hs(e,t){jc(e,t);var r=nr(t.value),o=t.type;if(r!=null)o==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(o==="submit"||o==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vs(e,t.type,r):t.hasOwnProperty("defaultValue")&&vs(e,t.type,nr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Hc(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!(o!=="submit"&&o!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function vs(e,t,r){(t!=="number"||ii(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Qo=Array.isArray;function co(e,t,r,o){if(e=e.options,t){t={};for(var n=0;n<r.length;n++)t["$"+r[n]]=!0;for(r=0;r<e.length;r++)n=t.hasOwnProperty("$"+e[r].value),e[r].selected!==n&&(e[r].selected=n),n&&o&&(e[r].defaultSelected=!0)}else{for(r=""+nr(r),t=null,n=0;n<e.length;n++){if(e[n].value===r){e[n].selected=!0,o&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function ms(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return ne({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Wc(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(P(92));if(Qo(r)){if(1<r.length)throw Error(P(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:nr(r)}}function $c(e,t){var r=nr(t.value),o=nr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),o!=null&&(e.defaultValue=""+o)}function qc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Uc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ys(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Uc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var li,Vc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,o,n){MSApp.execUnsafeLocalFunction(function(){return e(t,r,o,n)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(li=li||document.createElement("div"),li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Jo(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Zo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},B0=["Webkit","ms","Moz","O"];Object.keys(Zo).forEach(function(e){B0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zo[t]=Zo[e]})});function Kc(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Zo.hasOwnProperty(e)&&Zo[e]?(""+t).trim():t+"px"}function Xc(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var o=r.indexOf("--")===0,n=Kc(r,t[r],o);r==="float"&&(r="cssFloat"),o?e.setProperty(r,n):e[r]=n}}var _0=ne({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ks(e,t){if(t){if(_0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function bs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ws=null;function Bs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _s=null,fo=null,go=null;function Yc(e){if(e=wn(e)){if(typeof _s!="function")throw Error(P(280));var t=e.stateNode;t&&(t=Ci(t),_s(e.stateNode,e.type,t))}}function Qc(e){fo?go?go.push(e):go=[e]:fo=e}function Jc(){if(fo){var e=fo,t=go;if(go=fo=null,Yc(e),t)for(e=0;e<t.length;e++)Yc(t[e])}}function Zc(e,t){return e(t)}function Gc(){}var xs=!1;function ed(e,t,r){if(xs)return e(t,r);xs=!0;try{return Zc(e,t,r)}finally{xs=!1,(fo!==null||go!==null)&&(Gc(),Jc())}}function Go(e,t){var r=e.stateNode;if(r===null)return null;var o=Ci(r);if(o===null)return null;r=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(P(231,t,typeof r));return r}var Ss=!1;if(Lt)try{var en={};Object.defineProperty(en,"passive",{get:function(){Ss=!0}}),window.addEventListener("test",en,en),window.removeEventListener("test",en,en)}catch{Ss=!1}function x0(e,t,r,o,n,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(d){this.onError(d)}}var tn=!1,si=null,ai=!1,Es=null,S0={onError:function(e){tn=!0,si=e}};function E0(e,t,r,o,n,i,l,s,a){tn=!1,si=null,x0.apply(S0,arguments)}function N0(e,t,r,o,n,i,l,s,a){if(E0.apply(this,arguments),tn){if(tn){var u=si;tn=!1,si=null}else throw Error(P(198));ai||(ai=!0,Es=u)}}function Dr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function td(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function rd(e){if(Dr(e)!==e)throw Error(P(188))}function F0(e){var t=e.alternate;if(!t){if(t=Dr(e),t===null)throw Error(P(188));return t!==e?null:e}for(var r=e,o=t;;){var n=r.return;if(n===null)break;var i=n.alternate;if(i===null){if(o=n.return,o!==null){r=o;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===r)return rd(n),e;if(i===o)return rd(n),t;i=i.sibling}throw Error(P(188))}if(r.return!==o.return)r=n,o=i;else{for(var l=!1,s=n.child;s;){if(s===r){l=!0,r=n,o=i;break}if(s===o){l=!0,o=n,r=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===r){l=!0,r=i,o=n;break}if(s===o){l=!0,o=i,r=n;break}s=s.sibling}if(!l)throw Error(P(189))}}if(r.alternate!==o)throw Error(P(190))}if(r.tag!==3)throw Error(P(188));return r.stateNode.current===r?e:t}function od(e){return e=F0(e),e!==null?nd(e):null}function nd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=nd(e);if(t!==null)return t;e=e.sibling}return null}var id=Ye.unstable_scheduleCallback,ld=Ye.unstable_cancelCallback,C0=Ye.unstable_shouldYield,P0=Ye.unstable_requestPaint,de=Ye.unstable_now,T0=Ye.unstable_getCurrentPriorityLevel,Ns=Ye.unstable_ImmediatePriority,sd=Ye.unstable_UserBlockingPriority,ui=Ye.unstable_NormalPriority,D0=Ye.unstable_LowPriority,ad=Ye.unstable_IdlePriority,ci=null,Et=null;function O0(e){if(Et&&typeof Et.onCommitFiberRoot=="function")try{Et.onCommitFiberRoot(ci,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:A0,R0=Math.log,z0=Math.LN2;function A0(e){return e>>>=0,e===0?32:31-(R0(e)/z0|0)|0}var di=64,fi=4194304;function rn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function gi(e,t){var r=e.pendingLanes;if(r===0)return 0;var o=0,n=e.suspendedLanes,i=e.pingedLanes,l=r&268435455;if(l!==0){var s=l&~n;s!==0?o=rn(s):(i&=l,i!==0&&(o=rn(i)))}else l=r&~n,l!==0?o=rn(l):i!==0&&(o=rn(i));if(o===0)return 0;if(t!==0&&t!==o&&(t&n)===0&&(n=o&-o,i=t&-t,n>=i||n===16&&(i&4194240)!==0))return t;if((o&4)!==0&&(o|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=o;0<t;)r=31-gt(t),n=1<<r,o|=e[r],t&=~n;return o}function L0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function I0(e,t){for(var r=e.suspendedLanes,o=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-gt(i),s=1<<l,a=n[l];a===-1?((s&r)===0||(s&o)!==0)&&(n[l]=L0(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Fs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ud(){var e=di;return di<<=1,(di&4194240)===0&&(di=64),e}function Cs(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function on(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=r}function M0(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<r;){var n=31-gt(r),i=1<<n;t[n]=0,o[n]=-1,e[n]=-1,r&=~i}}function Ps(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var o=31-gt(r),n=1<<o;n&t|e[o]&t&&(e[o]|=t),r&=~n}}var Y=0;function cd(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var dd,Ts,fd,gd,pd,Ds=!1,pi=[],ir=null,lr=null,sr=null,nn=new Map,ln=new Map,ar=[],j0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function hd(e,t){switch(e){case"focusin":case"focusout":ir=null;break;case"dragenter":case"dragleave":lr=null;break;case"mouseover":case"mouseout":sr=null;break;case"pointerover":case"pointerout":nn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ln.delete(t.pointerId)}}function sn(e,t,r,o,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:o,nativeEvent:i,targetContainers:[n]},t!==null&&(t=wn(t),t!==null&&Ts(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function H0(e,t,r,o,n){switch(t){case"focusin":return ir=sn(ir,e,t,r,o,n),!0;case"dragenter":return lr=sn(lr,e,t,r,o,n),!0;case"mouseover":return sr=sn(sr,e,t,r,o,n),!0;case"pointerover":var i=n.pointerId;return nn.set(i,sn(nn.get(i)||null,e,t,r,o,n)),!0;case"gotpointercapture":return i=n.pointerId,ln.set(i,sn(ln.get(i)||null,e,t,r,o,n)),!0}return!1}function vd(e){var t=Or(e.target);if(t!==null){var r=Dr(t);if(r!==null){if(t=r.tag,t===13){if(t=td(r),t!==null){e.blockedOn=t,pd(e.priority,function(){fd(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function hi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Rs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var o=new r.constructor(r.type,r);ws=o,r.target.dispatchEvent(o),ws=null}else return t=wn(r),t!==null&&Ts(t),e.blockedOn=r,!1;t.shift()}return!0}function md(e,t,r){hi(e)&&r.delete(t)}function W0(){Ds=!1,ir!==null&&hi(ir)&&(ir=null),lr!==null&&hi(lr)&&(lr=null),sr!==null&&hi(sr)&&(sr=null),nn.forEach(md),ln.forEach(md)}function an(e,t){e.blockedOn===t&&(e.blockedOn=null,Ds||(Ds=!0,Ye.unstable_scheduleCallback(Ye.unstable_NormalPriority,W0)))}function un(e){function t(n){return an(n,e)}if(0<pi.length){an(pi[0],e);for(var r=1;r<pi.length;r++){var o=pi[r];o.blockedOn===e&&(o.blockedOn=null)}}for(ir!==null&&an(ir,e),lr!==null&&an(lr,e),sr!==null&&an(sr,e),nn.forEach(t),ln.forEach(t),r=0;r<ar.length;r++)o=ar[r],o.blockedOn===e&&(o.blockedOn=null);for(;0<ar.length&&(r=ar[0],r.blockedOn===null);)vd(r),r.blockedOn===null&&ar.shift()}var po=It.ReactCurrentBatchConfig,vi=!0;function $0(e,t,r,o){var n=Y,i=po.transition;po.transition=null;try{Y=1,Os(e,t,r,o)}finally{Y=n,po.transition=i}}function q0(e,t,r,o){var n=Y,i=po.transition;po.transition=null;try{Y=4,Os(e,t,r,o)}finally{Y=n,po.transition=i}}function Os(e,t,r,o){if(vi){var n=Rs(e,t,r,o);if(n===null)Js(e,t,o,mi,r),hd(e,o);else if(H0(n,e,t,r,o))o.stopPropagation();else if(hd(e,o),t&4&&-1<j0.indexOf(e)){for(;n!==null;){var i=wn(n);if(i!==null&&dd(i),i=Rs(e,t,r,o),i===null&&Js(e,t,o,mi,r),i===n)break;n=i}n!==null&&o.stopPropagation()}else Js(e,t,o,null,r)}}var mi=null;function Rs(e,t,r,o){if(mi=null,e=Bs(o),e=Or(e),e!==null)if(t=Dr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=td(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return mi=e,null}function yd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(T0()){case Ns:return 1;case sd:return 4;case ui:case D0:return 16;case ad:return 536870912;default:return 16}default:return 16}}var ur=null,zs=null,yi=null;function kd(){if(yi)return yi;var e,t=zs,r=t.length,o,n="value"in ur?ur.value:ur.textContent,i=n.length;for(e=0;e<r&&t[e]===n[e];e++);var l=r-e;for(o=1;o<=l&&t[r-o]===n[i-o];o++);return yi=n.slice(e,1<o?1-o:void 0)}function ki(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bi(){return!0}function bd(){return!1}function Qe(e){function t(r,o,n,i,l){this._reactName=r,this._targetInst=n,this.type=o,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(r=e[s],this[s]=r?r(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?bi:bd,this.isPropagationStopped=bd,this}return ne(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=bi)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=bi)},persist:function(){},isPersistent:bi}),t}var ho={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},As=Qe(ho),cn=ne({},ho,{view:0,detail:0}),U0=Qe(cn),Ls,Is,dn,wi=ne({},cn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:js,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==dn&&(dn&&e.type==="mousemove"?(Ls=e.screenX-dn.screenX,Is=e.screenY-dn.screenY):Is=Ls=0,dn=e),Ls)},movementY:function(e){return"movementY"in e?e.movementY:Is}}),wd=Qe(wi),V0=ne({},wi,{dataTransfer:0}),K0=Qe(V0),X0=ne({},cn,{relatedTarget:0}),Ms=Qe(X0),Y0=ne({},ho,{animationName:0,elapsedTime:0,pseudoElement:0}),Q0=Qe(Y0),J0=ne({},ho,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Z0=Qe(J0),G0=ne({},ho,{data:0}),Bd=Qe(G0),em={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},tm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},rm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function om(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=rm[e])?!!t[e]:!1}function js(){return om}var nm=ne({},cn,{key:function(e){if(e.key){var t=em[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ki(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?tm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:js,charCode:function(e){return e.type==="keypress"?ki(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ki(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),im=Qe(nm),lm=ne({},wi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),_d=Qe(lm),sm=ne({},cn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:js}),am=Qe(sm),um=ne({},ho,{propertyName:0,elapsedTime:0,pseudoElement:0}),cm=Qe(um),dm=ne({},wi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),fm=Qe(dm),gm=[9,13,27,32],Hs=Lt&&"CompositionEvent"in window,fn=null;Lt&&"documentMode"in document&&(fn=document.documentMode);var pm=Lt&&"TextEvent"in window&&!fn,xd=Lt&&(!Hs||fn&&8<fn&&11>=fn),Sd=String.fromCharCode(32),Ed=!1;function Nd(e,t){switch(e){case"keyup":return gm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Fd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var vo=!1;function hm(e,t){switch(e){case"compositionend":return Fd(t);case"keypress":return t.which!==32?null:(Ed=!0,Sd);case"textInput":return e=t.data,e===Sd&&Ed?null:e;default:return null}}function vm(e,t){if(vo)return e==="compositionend"||!Hs&&Nd(e,t)?(e=kd(),yi=zs=ur=null,vo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return xd&&t.locale!=="ko"?null:t.data;default:return null}}var mm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Cd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!mm[e.type]:t==="textarea"}function Pd(e,t,r,o){Qc(o),t=Ei(t,"onChange"),0<t.length&&(r=new As("onChange","change",null,r,o),e.push({event:r,listeners:t}))}var gn=null,pn=null;function ym(e){Xd(e,0)}function Bi(e){var t=wo(e);if(Ic(t))return e}function km(e,t){if(e==="change")return t}var Td=!1;if(Lt){var Ws;if(Lt){var $s="oninput"in document;if(!$s){var Dd=document.createElement("div");Dd.setAttribute("oninput","return;"),$s=typeof Dd.oninput=="function"}Ws=$s}else Ws=!1;Td=Ws&&(!document.documentMode||9<document.documentMode)}function Od(){gn&&(gn.detachEvent("onpropertychange",Rd),pn=gn=null)}function Rd(e){if(e.propertyName==="value"&&Bi(pn)){var t=[];Pd(t,pn,e,Bs(e)),ed(ym,t)}}function bm(e,t,r){e==="focusin"?(Od(),gn=t,pn=r,gn.attachEvent("onpropertychange",Rd)):e==="focusout"&&Od()}function wm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bi(pn)}function Bm(e,t){if(e==="click")return Bi(t)}function _m(e,t){if(e==="input"||e==="change")return Bi(t)}function xm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:xm;function hn(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),o=Object.keys(t);if(r.length!==o.length)return!1;for(o=0;o<r.length;o++){var n=r[o];if(!es.call(t,n)||!pt(e[n],t[n]))return!1}return!0}function zd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ad(e,t){var r=zd(e);e=0;for(var o;r;){if(r.nodeType===3){if(o=e+r.textContent.length,e<=t&&o>=t)return{node:r,offset:t-e};e=o}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=zd(r)}}function Ld(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ld(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Id(){for(var e=window,t=ii();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=ii(e.document)}return t}function qs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sm(e){var t=Id(),r=e.focusedElem,o=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Ld(r.ownerDocument.documentElement,r)){if(o!==null&&qs(r)){if(t=o.start,e=o.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var n=r.textContent.length,i=Math.min(o.start,n);o=o.end===void 0?i:Math.min(o.end,n),!e.extend&&i>o&&(n=o,o=i,i=n),n=Ad(r,i);var l=Ad(r,o);n&&l&&(e.rangeCount!==1||e.anchorNode!==n.node||e.anchorOffset!==n.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(n.node,n.offset),e.removeAllRanges(),i>o?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Em=Lt&&"documentMode"in document&&11>=document.documentMode,mo=null,Us=null,vn=null,Vs=!1;function Md(e,t,r){var o=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Vs||mo==null||mo!==ii(o)||(o=mo,"selectionStart"in o&&qs(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),vn&&hn(vn,o)||(vn=o,o=Ei(Us,"onSelect"),0<o.length&&(t=new As("onSelect","select",null,t,r),e.push({event:t,listeners:o}),t.target=mo)))}function _i(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var yo={animationend:_i("Animation","AnimationEnd"),animationiteration:_i("Animation","AnimationIteration"),animationstart:_i("Animation","AnimationStart"),transitionend:_i("Transition","TransitionEnd")},Ks={},jd={};Lt&&(jd=document.createElement("div").style,"AnimationEvent"in window||(delete yo.animationend.animation,delete yo.animationiteration.animation,delete yo.animationstart.animation),"TransitionEvent"in window||delete yo.transitionend.transition);function xi(e){if(Ks[e])return Ks[e];if(!yo[e])return e;var t=yo[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in jd)return Ks[e]=t[r];return e}var Hd=xi("animationend"),Wd=xi("animationiteration"),$d=xi("animationstart"),qd=xi("transitionend"),Ud=new Map,Vd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function cr(e,t){Ud.set(e,t),Tr(t,[e])}for(var Xs=0;Xs<Vd.length;Xs++){var Ys=Vd[Xs],Nm=Ys.toLowerCase(),Fm=Ys[0].toUpperCase()+Ys.slice(1);cr(Nm,"on"+Fm)}cr(Hd,"onAnimationEnd"),cr(Wd,"onAnimationIteration"),cr($d,"onAnimationStart"),cr("dblclick","onDoubleClick"),cr("focusin","onFocus"),cr("focusout","onBlur"),cr(qd,"onTransitionEnd"),so("onMouseEnter",["mouseout","mouseover"]),so("onMouseLeave",["mouseout","mouseover"]),so("onPointerEnter",["pointerout","pointerover"]),so("onPointerLeave",["pointerout","pointerover"]),Tr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Tr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Tr("onBeforeInput",["compositionend","keypress","textInput","paste"]),Tr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Tr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Tr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var mn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cm=new Set("cancel close invalid load scroll toggle".split(" ").concat(mn));function Kd(e,t,r){var o=e.type||"unknown-event";e.currentTarget=r,N0(o,t,void 0,e),e.currentTarget=null}function Xd(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var o=e[r],n=o.event;o=o.listeners;e:{var i=void 0;if(t)for(var l=o.length-1;0<=l;l--){var s=o[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&n.isPropagationStopped())break e;Kd(n,s,u),i=a}else for(l=0;l<o.length;l++){if(s=o[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&n.isPropagationStopped())break e;Kd(n,s,u),i=a}}}if(ai)throw e=Es,ai=!1,Es=null,e}function ee(e,t){var r=t[oa];r===void 0&&(r=t[oa]=new Set);var o=e+"__bubble";r.has(o)||(Yd(t,e,2,!1),r.add(o))}function Qs(e,t,r){var o=0;t&&(o|=4),Yd(r,e,o,t)}var Si="_reactListening"+Math.random().toString(36).slice(2);function yn(e){if(!e[Si]){e[Si]=!0,Pc.forEach(function(r){r!=="selectionchange"&&(Cm.has(r)||Qs(r,!1,e),Qs(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Si]||(t[Si]=!0,Qs("selectionchange",!1,t))}}function Yd(e,t,r,o){switch(yd(t)){case 1:var n=$0;break;case 4:n=q0;break;default:n=Os}r=n.bind(null,t,r,e),n=void 0,!Ss||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),o?n!==void 0?e.addEventListener(t,r,{capture:!0,passive:n}):e.addEventListener(t,r,!0):n!==void 0?e.addEventListener(t,r,{passive:n}):e.addEventListener(t,r,!1)}function Js(e,t,r,o,n){var i=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var l=o.tag;if(l===3||l===4){var s=o.stateNode.containerInfo;if(s===n||s.nodeType===8&&s.parentNode===n)break;if(l===4)for(l=o.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===n||a.nodeType===8&&a.parentNode===n))return;l=l.return}for(;s!==null;){if(l=Or(s),l===null)return;if(a=l.tag,a===5||a===6){o=i=l;continue e}s=s.parentNode}}o=o.return}ed(function(){var u=i,d=Bs(r),f=[];e:{var g=Ud.get(e);if(g!==void 0){var y=As,v=e;switch(e){case"keypress":if(ki(r)===0)break e;case"keydown":case"keyup":y=im;break;case"focusin":v="focus",y=Ms;break;case"focusout":v="blur",y=Ms;break;case"beforeblur":case"afterblur":y=Ms;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=wd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=K0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=am;break;case Hd:case Wd:case $d:y=Q0;break;case qd:y=cm;break;case"scroll":y=U0;break;case"wheel":y=fm;break;case"copy":case"cut":case"paste":y=Z0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=_d}var k=(t&4)!==0,_=!k&&e==="scroll",m=k?g!==null?g+"Capture":null:g;k=[];for(var h=u,c;h!==null;){c=h;var p=c.stateNode;if(c.tag===5&&p!==null&&(c=p,m!==null&&(p=Go(h,m),p!=null&&k.push(kn(h,p,c)))),_)break;h=h.return}0<k.length&&(g=new y(g,v,null,r,d),f.push({event:g,listeners:k}))}}if((t&7)===0){e:{if(g=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",g&&r!==ws&&(v=r.relatedTarget||r.fromElement)&&(Or(v)||v[Mt]))break e;if((y||g)&&(g=d.window===d?d:(g=d.ownerDocument)?g.defaultView||g.parentWindow:window,y?(v=r.relatedTarget||r.toElement,y=u,v=v?Or(v):null,v!==null&&(_=Dr(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=u),y!==v)){if(k=wd,p="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(k=_d,p="onPointerLeave",m="onPointerEnter",h="pointer"),_=y==null?g:wo(y),c=v==null?g:wo(v),g=new k(p,h+"leave",y,r,d),g.target=_,g.relatedTarget=c,p=null,Or(d)===u&&(k=new k(m,h+"enter",v,r,d),k.target=c,k.relatedTarget=_,p=k),_=p,y&&v)t:{for(k=y,m=v,h=0,c=k;c;c=ko(c))h++;for(c=0,p=m;p;p=ko(p))c++;for(;0<h-c;)k=ko(k),h--;for(;0<c-h;)m=ko(m),c--;for(;h--;){if(k===m||m!==null&&k===m.alternate)break t;k=ko(k),m=ko(m)}k=null}else k=null;y!==null&&Qd(f,g,y,k,!1),v!==null&&_!==null&&Qd(f,_,v,k,!0)}}e:{if(g=u?wo(u):window,y=g.nodeName&&g.nodeName.toLowerCase(),y==="select"||y==="input"&&g.type==="file")var b=km;else if(Cd(g))if(Td)b=_m;else{b=wm;var w=bm}else(y=g.nodeName)&&y.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(b=Bm);if(b&&(b=b(e,u))){Pd(f,b,r,d);break e}w&&w(e,g,u),e==="focusout"&&(w=g._wrapperState)&&w.controlled&&g.type==="number"&&vs(g,"number",g.value)}switch(w=u?wo(u):window,e){case"focusin":(Cd(w)||w.contentEditable==="true")&&(mo=w,Us=u,vn=null);break;case"focusout":vn=Us=mo=null;break;case"mousedown":Vs=!0;break;case"contextmenu":case"mouseup":case"dragend":Vs=!1,Md(f,r,d);break;case"selectionchange":if(Em)break;case"keydown":case"keyup":Md(f,r,d)}var B;if(Hs)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else vo?Nd(e,r)&&(E="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(E="onCompositionStart");E&&(xd&&r.locale!=="ko"&&(vo||E!=="onCompositionStart"?E==="onCompositionEnd"&&vo&&(B=kd()):(ur=d,zs="value"in ur?ur.value:ur.textContent,vo=!0)),w=Ei(u,E),0<w.length&&(E=new Bd(E,e,null,r,d),f.push({event:E,listeners:w}),B?E.data=B:(B=Fd(r),B!==null&&(E.data=B)))),(B=pm?hm(e,r):vm(e,r))&&(u=Ei(u,"onBeforeInput"),0<u.length&&(d=new Bd("onBeforeInput","beforeinput",null,r,d),f.push({event:d,listeners:u}),d.data=B))}Xd(f,t)})}function kn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Ei(e,t){for(var r=t+"Capture",o=[];e!==null;){var n=e,i=n.stateNode;n.tag===5&&i!==null&&(n=i,i=Go(e,r),i!=null&&o.unshift(kn(e,i,n)),i=Go(e,t),i!=null&&o.push(kn(e,i,n))),e=e.return}return o}function ko(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qd(e,t,r,o,n){for(var i=t._reactName,l=[];r!==null&&r!==o;){var s=r,a=s.alternate,u=s.stateNode;if(a!==null&&a===o)break;s.tag===5&&u!==null&&(s=u,n?(a=Go(r,i),a!=null&&l.unshift(kn(r,a,s))):n||(a=Go(r,i),a!=null&&l.push(kn(r,a,s)))),r=r.return}l.length!==0&&e.push({event:t,listeners:l})}var Pm=/\r\n?/g,Tm=/\u0000|\uFFFD/g;function Jd(e){return(typeof e=="string"?e:""+e).replace(Pm,`
`).replace(Tm,"")}function Ni(e,t,r){if(t=Jd(t),Jd(e)!==t&&r)throw Error(P(425))}function Fi(){}var Zs=null,Gs=null;function ea(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ta=typeof setTimeout=="function"?setTimeout:void 0,Dm=typeof clearTimeout=="function"?clearTimeout:void 0,Zd=typeof Promise=="function"?Promise:void 0,Om=typeof queueMicrotask=="function"?queueMicrotask:typeof Zd<"u"?function(e){return Zd.resolve(null).then(e).catch(Rm)}:ta;function Rm(e){setTimeout(function(){throw e})}function ra(e,t){var r=t,o=0;do{var n=r.nextSibling;if(e.removeChild(r),n&&n.nodeType===8)if(r=n.data,r==="/$"){if(o===0){e.removeChild(n),un(t);return}o--}else r!=="$"&&r!=="$?"&&r!=="$!"||o++;r=n}while(r);un(t)}function dr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Gd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var bo=Math.random().toString(36).slice(2),Nt="__reactFiber$"+bo,bn="__reactProps$"+bo,Mt="__reactContainer$"+bo,oa="__reactEvents$"+bo,zm="__reactListeners$"+bo,Am="__reactHandles$"+bo;function Or(e){var t=e[Nt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Mt]||r[Nt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=Gd(e);e!==null;){if(r=e[Nt])return r;e=Gd(e)}return t}e=r,r=e.parentNode}return null}function wn(e){return e=e[Nt]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function wo(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function Ci(e){return e[bn]||null}var na=[],Bo=-1;function fr(e){return{current:e}}function te(e){0>Bo||(e.current=na[Bo],na[Bo]=null,Bo--)}function J(e,t){Bo++,na[Bo]=e.current,e.current=t}var gr={},Se=fr(gr),je=fr(!1),Rr=gr;function _o(e,t){var r=e.type.contextTypes;if(!r)return gr;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var n={},i;for(i in r)n[i]=t[i];return o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=n),n}function He(e){return e=e.childContextTypes,e!=null}function Pi(){te(je),te(Se)}function ef(e,t,r){if(Se.current!==gr)throw Error(P(168));J(Se,t),J(je,r)}function tf(e,t,r){var o=e.stateNode;if(t=t.childContextTypes,typeof o.getChildContext!="function")return r;o=o.getChildContext();for(var n in o)if(!(n in t))throw Error(P(108,b0(e)||"Unknown",n));return ne({},r,o)}function Ti(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||gr,Rr=Se.current,J(Se,e),J(je,je.current),!0}function rf(e,t,r){var o=e.stateNode;if(!o)throw Error(P(169));r?(e=tf(e,t,Rr),o.__reactInternalMemoizedMergedChildContext=e,te(je),te(Se),J(Se,e)):te(je),J(je,r)}var jt=null,Di=!1,ia=!1;function of(e){jt===null?jt=[e]:jt.push(e)}function Lm(e){Di=!0,of(e)}function pr(){if(!ia&&jt!==null){ia=!0;var e=0,t=Y;try{var r=jt;for(Y=1;e<r.length;e++){var o=r[e];do o=o(!0);while(o!==null)}jt=null,Di=!1}catch(n){throw jt!==null&&(jt=jt.slice(e+1)),id(Ns,pr),n}finally{Y=t,ia=!1}}return null}var xo=[],So=0,Oi=null,Ri=0,nt=[],it=0,zr=null,Ht=1,Wt="";function Ar(e,t){xo[So++]=Ri,xo[So++]=Oi,Oi=e,Ri=t}function nf(e,t,r){nt[it++]=Ht,nt[it++]=Wt,nt[it++]=zr,zr=e;var o=Ht;e=Wt;var n=32-gt(o)-1;o&=~(1<<n),r+=1;var i=32-gt(t)+n;if(30<i){var l=n-n%5;i=(o&(1<<l)-1).toString(32),o>>=l,n-=l,Ht=1<<32-gt(t)+n|r<<n|o,Wt=i+e}else Ht=1<<i|r<<n|o,Wt=e}function la(e){e.return!==null&&(Ar(e,1),nf(e,1,0))}function sa(e){for(;e===Oi;)Oi=xo[--So],xo[So]=null,Ri=xo[--So],xo[So]=null;for(;e===zr;)zr=nt[--it],nt[it]=null,Wt=nt[--it],nt[it]=null,Ht=nt[--it],nt[it]=null}var Je=null,Ze=null,oe=!1,ht=null;function lf(e,t){var r=ut(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function sf(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Je=e,Ze=dr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Je=e,Ze=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=zr!==null?{id:Ht,overflow:Wt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ut(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,Je=e,Ze=null,!0):!1;default:return!1}}function aa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ua(e){if(oe){var t=Ze;if(t){var r=t;if(!sf(e,t)){if(aa(e))throw Error(P(418));t=dr(r.nextSibling);var o=Je;t&&sf(e,t)?lf(o,r):(e.flags=e.flags&-4097|2,oe=!1,Je=e)}}else{if(aa(e))throw Error(P(418));e.flags=e.flags&-4097|2,oe=!1,Je=e}}}function af(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Je=e}function zi(e){if(e!==Je)return!1;if(!oe)return af(e),oe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ea(e.type,e.memoizedProps)),t&&(t=Ze)){if(aa(e))throw uf(),Error(P(418));for(;t;)lf(e,t),t=dr(t.nextSibling)}if(af(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Ze=dr(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Ze=null}}else Ze=Je?dr(e.stateNode.nextSibling):null;return!0}function uf(){for(var e=Ze;e;)e=dr(e.nextSibling)}function Eo(){Ze=Je=null,oe=!1}function ca(e){ht===null?ht=[e]:ht.push(e)}var Im=It.ReactCurrentBatchConfig;function Bn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(P(309));var o=r.stateNode}if(!o)throw Error(P(147,e));var n=o,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=n.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(P(284));if(!r._owner)throw Error(P(290,e))}return e}function Ai(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cf(e){var t=e._init;return t(e._payload)}function df(e){function t(m,h){if(e){var c=m.deletions;c===null?(m.deletions=[h],m.flags|=16):c.push(h)}}function r(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function o(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function n(m,h){return m=Br(m,h),m.index=0,m.sibling=null,m}function i(m,h,c){return m.index=c,e?(c=m.alternate,c!==null?(c=c.index,c<h?(m.flags|=2,h):c):(m.flags|=2,h)):(m.flags|=1048576,h)}function l(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,h,c,p){return h===null||h.tag!==6?(h=tu(c,m.mode,p),h.return=m,h):(h=n(h,c),h.return=m,h)}function a(m,h,c,p){var b=c.type;return b===uo?d(m,h,c.props.children,p,c.key):h!==null&&(h.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===or&&cf(b)===h.type)?(p=n(h,c.props),p.ref=Bn(m,h,c),p.return=m,p):(p=ll(c.type,c.key,c.props,null,m.mode,p),p.ref=Bn(m,h,c),p.return=m,p)}function u(m,h,c,p){return h===null||h.tag!==4||h.stateNode.containerInfo!==c.containerInfo||h.stateNode.implementation!==c.implementation?(h=ru(c,m.mode,p),h.return=m,h):(h=n(h,c.children||[]),h.return=m,h)}function d(m,h,c,p,b){return h===null||h.tag!==7?(h=qr(c,m.mode,p,b),h.return=m,h):(h=n(h,c),h.return=m,h)}function f(m,h,c){if(typeof h=="string"&&h!==""||typeof h=="number")return h=tu(""+h,m.mode,c),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case oi:return c=ll(h.type,h.key,h.props,null,m.mode,c),c.ref=Bn(m,null,h),c.return=m,c;case ao:return h=ru(h,m.mode,c),h.return=m,h;case or:var p=h._init;return f(m,p(h._payload),c)}if(Qo(h)||Xo(h))return h=qr(h,m.mode,c,null),h.return=m,h;Ai(m,h)}return null}function g(m,h,c,p){var b=h!==null?h.key:null;if(typeof c=="string"&&c!==""||typeof c=="number")return b!==null?null:s(m,h,""+c,p);if(typeof c=="object"&&c!==null){switch(c.$$typeof){case oi:return c.key===b?a(m,h,c,p):null;case ao:return c.key===b?u(m,h,c,p):null;case or:return b=c._init,g(m,h,b(c._payload),p)}if(Qo(c)||Xo(c))return b!==null?null:d(m,h,c,p,null);Ai(m,c)}return null}function y(m,h,c,p,b){if(typeof p=="string"&&p!==""||typeof p=="number")return m=m.get(c)||null,s(h,m,""+p,b);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case oi:return m=m.get(p.key===null?c:p.key)||null,a(h,m,p,b);case ao:return m=m.get(p.key===null?c:p.key)||null,u(h,m,p,b);case or:var w=p._init;return y(m,h,c,w(p._payload),b)}if(Qo(p)||Xo(p))return m=m.get(c)||null,d(h,m,p,b,null);Ai(h,p)}return null}function v(m,h,c,p){for(var b=null,w=null,B=h,E=h=0,O=null;B!==null&&E<c.length;E++){B.index>E?(O=B,B=null):O=B.sibling;var F=g(m,B,c[E],p);if(F===null){B===null&&(B=O);break}e&&B&&F.alternate===null&&t(m,B),h=i(F,h,E),w===null?b=F:w.sibling=F,w=F,B=O}if(E===c.length)return r(m,B),oe&&Ar(m,E),b;if(B===null){for(;E<c.length;E++)B=f(m,c[E],p),B!==null&&(h=i(B,h,E),w===null?b=B:w.sibling=B,w=B);return oe&&Ar(m,E),b}for(B=o(m,B);E<c.length;E++)O=y(B,m,E,c[E],p),O!==null&&(e&&O.alternate!==null&&B.delete(O.key===null?E:O.key),h=i(O,h,E),w===null?b=O:w.sibling=O,w=O);return e&&B.forEach(function(A){return t(m,A)}),oe&&Ar(m,E),b}function k(m,h,c,p){var b=Xo(c);if(typeof b!="function")throw Error(P(150));if(c=b.call(c),c==null)throw Error(P(151));for(var w=b=null,B=h,E=h=0,O=null,F=c.next();B!==null&&!F.done;E++,F=c.next()){B.index>E?(O=B,B=null):O=B.sibling;var A=g(m,B,F.value,p);if(A===null){B===null&&(B=O);break}e&&B&&A.alternate===null&&t(m,B),h=i(A,h,E),w===null?b=A:w.sibling=A,w=A,B=O}if(F.done)return r(m,B),oe&&Ar(m,E),b;if(B===null){for(;!F.done;E++,F=c.next())F=f(m,F.value,p),F!==null&&(h=i(F,h,E),w===null?b=F:w.sibling=F,w=F);return oe&&Ar(m,E),b}for(B=o(m,B);!F.done;E++,F=c.next())F=y(B,m,E,F.value,p),F!==null&&(e&&F.alternate!==null&&B.delete(F.key===null?E:F.key),h=i(F,h,E),w===null?b=F:w.sibling=F,w=F);return e&&B.forEach(function(I){return t(m,I)}),oe&&Ar(m,E),b}function _(m,h,c,p){if(typeof c=="object"&&c!==null&&c.type===uo&&c.key===null&&(c=c.props.children),typeof c=="object"&&c!==null){switch(c.$$typeof){case oi:e:{for(var b=c.key,w=h;w!==null;){if(w.key===b){if(b=c.type,b===uo){if(w.tag===7){r(m,w.sibling),h=n(w,c.props.children),h.return=m,m=h;break e}}else if(w.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===or&&cf(b)===w.type){r(m,w.sibling),h=n(w,c.props),h.ref=Bn(m,w,c),h.return=m,m=h;break e}r(m,w);break}else t(m,w);w=w.sibling}c.type===uo?(h=qr(c.props.children,m.mode,p,c.key),h.return=m,m=h):(p=ll(c.type,c.key,c.props,null,m.mode,p),p.ref=Bn(m,h,c),p.return=m,m=p)}return l(m);case ao:e:{for(w=c.key;h!==null;){if(h.key===w)if(h.tag===4&&h.stateNode.containerInfo===c.containerInfo&&h.stateNode.implementation===c.implementation){r(m,h.sibling),h=n(h,c.children||[]),h.return=m,m=h;break e}else{r(m,h);break}else t(m,h);h=h.sibling}h=ru(c,m.mode,p),h.return=m,m=h}return l(m);case or:return w=c._init,_(m,h,w(c._payload),p)}if(Qo(c))return v(m,h,c,p);if(Xo(c))return k(m,h,c,p);Ai(m,c)}return typeof c=="string"&&c!==""||typeof c=="number"?(c=""+c,h!==null&&h.tag===6?(r(m,h.sibling),h=n(h,c),h.return=m,m=h):(r(m,h),h=tu(c,m.mode,p),h.return=m,m=h),l(m)):r(m,h)}return _}var No=df(!0),ff=df(!1),Li=fr(null),Ii=null,Fo=null,da=null;function fa(){da=Fo=Ii=null}function ga(e){var t=Li.current;te(Li),e._currentValue=t}function pa(e,t,r){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===r)break;e=e.return}}function Co(e,t){Ii=e,da=Fo=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(We=!0),e.firstContext=null)}function lt(e){var t=e._currentValue;if(da!==e)if(e={context:e,memoizedValue:t,next:null},Fo===null){if(Ii===null)throw Error(P(308));Fo=e,Ii.dependencies={lanes:0,firstContext:e}}else Fo=Fo.next=e;return t}var Lr=null;function ha(e){Lr===null?Lr=[e]:Lr.push(e)}function gf(e,t,r,o){var n=t.interleaved;return n===null?(r.next=r,ha(t)):(r.next=n.next,n.next=r),t.interleaved=r,$t(e,o)}function $t(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var hr=!1;function va(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function pf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function vr(e,t,r){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(q&2)!==0){var n=o.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),o.pending=t,$t(e,r)}return n=o.interleaved,n===null?(t.next=t,ha(o)):(t.next=n.next,n.next=t),o.interleaved=t,$t(e,r)}function Mi(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var o=t.lanes;o&=e.pendingLanes,r|=o,t.lanes=r,Ps(e,r)}}function hf(e,t){var r=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,r===o)){var n=null,i=null;if(r=r.firstBaseUpdate,r!==null){do{var l={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};i===null?n=i=l:i=i.next=l,r=r.next}while(r!==null);i===null?n=i=t:i=i.next=t}else n=i=t;r={baseState:o.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:o.shared,effects:o.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function ji(e,t,r,o){var n=e.updateQueue;hr=!1;var i=n.firstBaseUpdate,l=n.lastBaseUpdate,s=n.shared.pending;if(s!==null){n.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var f=n.baseState;l=0,d=u=a=null,s=i;do{var g=s.lane,y=s.eventTime;if((o&g)===g){d!==null&&(d=d.next={eventTime:y,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var v=e,k=s;switch(g=t,y=r,k.tag){case 1:if(v=k.payload,typeof v=="function"){f=v.call(y,f,g);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=k.payload,g=typeof v=="function"?v.call(y,f,g):v,g==null)break e;f=ne({},f,g);break e;case 2:hr=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,g=n.effects,g===null?n.effects=[s]:g.push(s))}else y={eventTime:y,lane:g,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=y,a=f):d=d.next=y,l|=g;if(s=s.next,s===null){if(s=n.shared.pending,s===null)break;g=s,s=g.next,g.next=null,n.lastBaseUpdate=g,n.shared.pending=null}}while(1);if(d===null&&(a=f),n.baseState=a,n.firstBaseUpdate=u,n.lastBaseUpdate=d,t=n.shared.interleaved,t!==null){n=t;do l|=n.lane,n=n.next;while(n!==t)}else i===null&&(n.shared.lanes=0);jr|=l,e.lanes=l,e.memoizedState=f}}function vf(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var o=e[t],n=o.callback;if(n!==null){if(o.callback=null,o=r,typeof n!="function")throw Error(P(191,n));n.call(o)}}}var _n={},Ft=fr(_n),xn=fr(_n),Sn=fr(_n);function Ir(e){if(e===_n)throw Error(P(174));return e}function ma(e,t){switch(J(Sn,t),J(xn,e),J(Ft,_n),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ys(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ys(t,e)}te(Ft),J(Ft,t)}function Po(){te(Ft),te(xn),te(Sn)}function mf(e){Ir(Sn.current);var t=Ir(Ft.current),r=ys(t,e.type);t!==r&&(J(xn,e),J(Ft,r))}function ya(e){xn.current===e&&(te(Ft),te(xn))}var ie=fr(0);function Hi(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ka=[];function ba(){for(var e=0;e<ka.length;e++)ka[e]._workInProgressVersionPrimary=null;ka.length=0}var Wi=It.ReactCurrentDispatcher,wa=It.ReactCurrentBatchConfig,Mr=0,le=null,he=null,ye=null,$i=!1,En=!1,Nn=0,Mm=0;function Ee(){throw Error(P(321))}function Ba(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!pt(e[r],t[r]))return!1;return!0}function _a(e,t,r,o,n,i){if(Mr=i,le=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Wi.current=e===null||e.memoizedState===null?$m:qm,e=r(o,n),En){i=0;do{if(En=!1,Nn=0,25<=i)throw Error(P(301));i+=1,ye=he=null,t.updateQueue=null,Wi.current=Um,e=r(o,n)}while(En)}if(Wi.current=Vi,t=he!==null&&he.next!==null,Mr=0,ye=he=le=null,$i=!1,t)throw Error(P(300));return e}function xa(){var e=Nn!==0;return Nn=0,e}function Ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?le.memoizedState=ye=e:ye=ye.next=e,ye}function st(){if(he===null){var e=le.alternate;e=e!==null?e.memoizedState:null}else e=he.next;var t=ye===null?le.memoizedState:ye.next;if(t!==null)ye=t,he=e;else{if(e===null)throw Error(P(310));he=e,e={memoizedState:he.memoizedState,baseState:he.baseState,baseQueue:he.baseQueue,queue:he.queue,next:null},ye===null?le.memoizedState=ye=e:ye=ye.next=e}return ye}function Fn(e,t){return typeof t=="function"?t(e):t}function Sa(e){var t=st(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var o=he,n=o.baseQueue,i=r.pending;if(i!==null){if(n!==null){var l=n.next;n.next=i.next,i.next=l}o.baseQueue=n=i,r.pending=null}if(n!==null){i=n.next,o=o.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((Mr&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),o=u.hasEagerState?u.eagerState:e(o,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=f,l=o):a=a.next=f,le.lanes|=d,jr|=d}u=u.next}while(u!==null&&u!==i);a===null?l=o:a.next=s,pt(o,t.memoizedState)||(We=!0),t.memoizedState=o,t.baseState=l,t.baseQueue=a,r.lastRenderedState=o}if(e=r.interleaved,e!==null){n=e;do i=n.lane,le.lanes|=i,jr|=i,n=n.next;while(n!==e)}else n===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Ea(e){var t=st(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var o=r.dispatch,n=r.pending,i=t.memoizedState;if(n!==null){r.pending=null;var l=n=n.next;do i=e(i,l.action),l=l.next;while(l!==n);pt(i,t.memoizedState)||(We=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),r.lastRenderedState=i}return[i,o]}function yf(){}function kf(e,t){var r=le,o=st(),n=t(),i=!pt(o.memoizedState,n);if(i&&(o.memoizedState=n,We=!0),o=o.queue,Na(Bf.bind(null,r,o,e),[e]),o.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(r.flags|=2048,Cn(9,wf.bind(null,r,o,n,t),void 0,null),ke===null)throw Error(P(349));(Mr&30)!==0||bf(r,t,n)}return n}function bf(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function wf(e,t,r,o){t.value=r,t.getSnapshot=o,_f(t)&&xf(e)}function Bf(e,t,r){return r(function(){_f(t)&&xf(e)})}function _f(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!pt(e,r)}catch{return!0}}function xf(e){var t=$t(e,1);t!==null&&kt(t,e,1,-1)}function Sf(e){var t=Ct();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Fn,lastRenderedState:e},t.queue=e,e=e.dispatch=Wm.bind(null,le,e),[t.memoizedState,e]}function Cn(e,t,r,o){return e={tag:e,create:t,destroy:r,deps:o,next:null},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(o=r.next,r.next=e,e.next=o,t.lastEffect=e)),e}function Ef(){return st().memoizedState}function qi(e,t,r,o){var n=Ct();le.flags|=e,n.memoizedState=Cn(1|t,r,void 0,o===void 0?null:o)}function Ui(e,t,r,o){var n=st();o=o===void 0?null:o;var i=void 0;if(he!==null){var l=he.memoizedState;if(i=l.destroy,o!==null&&Ba(o,l.deps)){n.memoizedState=Cn(t,r,i,o);return}}le.flags|=e,n.memoizedState=Cn(1|t,r,i,o)}function Nf(e,t){return qi(8390656,8,e,t)}function Na(e,t){return Ui(2048,8,e,t)}function Ff(e,t){return Ui(4,2,e,t)}function Cf(e,t){return Ui(4,4,e,t)}function Pf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Tf(e,t,r){return r=r!=null?r.concat([e]):null,Ui(4,4,Pf.bind(null,t,e),r)}function Fa(){}function Df(e,t){var r=st();t=t===void 0?null:t;var o=r.memoizedState;return o!==null&&t!==null&&Ba(t,o[1])?o[0]:(r.memoizedState=[e,t],e)}function Of(e,t){var r=st();t=t===void 0?null:t;var o=r.memoizedState;return o!==null&&t!==null&&Ba(t,o[1])?o[0]:(e=e(),r.memoizedState=[e,t],e)}function Rf(e,t,r){return(Mr&21)===0?(e.baseState&&(e.baseState=!1,We=!0),e.memoizedState=r):(pt(r,t)||(r=ud(),le.lanes|=r,jr|=r,e.baseState=!0),t)}function jm(e,t){var r=Y;Y=r!==0&&4>r?r:4,e(!0);var o=wa.transition;wa.transition={};try{e(!1),t()}finally{Y=r,wa.transition=o}}function zf(){return st().memoizedState}function Hm(e,t,r){var o=br(e);if(r={lane:o,action:r,hasEagerState:!1,eagerState:null,next:null},Af(e))Lf(t,r);else if(r=gf(e,t,r,o),r!==null){var n=Oe();kt(r,e,o,n),If(r,t,o)}}function Wm(e,t,r){var o=br(e),n={lane:o,action:r,hasEagerState:!1,eagerState:null,next:null};if(Af(e))Lf(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,r);if(n.hasEagerState=!0,n.eagerState=s,pt(s,l)){var a=t.interleaved;a===null?(n.next=n,ha(t)):(n.next=a.next,a.next=n),t.interleaved=n;return}}catch{}finally{}r=gf(e,t,n,o),r!==null&&(n=Oe(),kt(r,e,o,n),If(r,t,o))}}function Af(e){var t=e.alternate;return e===le||t!==null&&t===le}function Lf(e,t){En=$i=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function If(e,t,r){if((r&4194240)!==0){var o=t.lanes;o&=e.pendingLanes,r|=o,t.lanes=r,Ps(e,r)}}var Vi={readContext:lt,useCallback:Ee,useContext:Ee,useEffect:Ee,useImperativeHandle:Ee,useInsertionEffect:Ee,useLayoutEffect:Ee,useMemo:Ee,useReducer:Ee,useRef:Ee,useState:Ee,useDebugValue:Ee,useDeferredValue:Ee,useTransition:Ee,useMutableSource:Ee,useSyncExternalStore:Ee,useId:Ee,unstable_isNewReconciler:!1},$m={readContext:lt,useCallback:function(e,t){return Ct().memoizedState=[e,t===void 0?null:t],e},useContext:lt,useEffect:Nf,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,qi(4194308,4,Pf.bind(null,t,e),r)},useLayoutEffect:function(e,t){return qi(4194308,4,e,t)},useInsertionEffect:function(e,t){return qi(4,2,e,t)},useMemo:function(e,t){var r=Ct();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var o=Ct();return t=r!==void 0?r(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=Hm.bind(null,le,e),[o.memoizedState,e]},useRef:function(e){var t=Ct();return e={current:e},t.memoizedState=e},useState:Sf,useDebugValue:Fa,useDeferredValue:function(e){return Ct().memoizedState=e},useTransition:function(){var e=Sf(!1),t=e[0];return e=jm.bind(null,e[1]),Ct().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var o=le,n=Ct();if(oe){if(r===void 0)throw Error(P(407));r=r()}else{if(r=t(),ke===null)throw Error(P(349));(Mr&30)!==0||bf(o,t,r)}n.memoizedState=r;var i={value:r,getSnapshot:t};return n.queue=i,Nf(Bf.bind(null,o,i,e),[e]),o.flags|=2048,Cn(9,wf.bind(null,o,i,r,t),void 0,null),r},useId:function(){var e=Ct(),t=ke.identifierPrefix;if(oe){var r=Wt,o=Ht;r=(o&~(1<<32-gt(o)-1)).toString(32)+r,t=":"+t+"R"+r,r=Nn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Mm++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},qm={readContext:lt,useCallback:Df,useContext:lt,useEffect:Na,useImperativeHandle:Tf,useInsertionEffect:Ff,useLayoutEffect:Cf,useMemo:Of,useReducer:Sa,useRef:Ef,useState:function(){return Sa(Fn)},useDebugValue:Fa,useDeferredValue:function(e){var t=st();return Rf(t,he.memoizedState,e)},useTransition:function(){var e=Sa(Fn)[0],t=st().memoizedState;return[e,t]},useMutableSource:yf,useSyncExternalStore:kf,useId:zf,unstable_isNewReconciler:!1},Um={readContext:lt,useCallback:Df,useContext:lt,useEffect:Na,useImperativeHandle:Tf,useInsertionEffect:Ff,useLayoutEffect:Cf,useMemo:Of,useReducer:Ea,useRef:Ef,useState:function(){return Ea(Fn)},useDebugValue:Fa,useDeferredValue:function(e){var t=st();return he===null?t.memoizedState=e:Rf(t,he.memoizedState,e)},useTransition:function(){var e=Ea(Fn)[0],t=st().memoizedState;return[e,t]},useMutableSource:yf,useSyncExternalStore:kf,useId:zf,unstable_isNewReconciler:!1};function vt(e,t){if(e&&e.defaultProps){t=ne({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Ca(e,t,r,o){t=e.memoizedState,r=r(o,t),r=r==null?t:ne({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Ki={isMounted:function(e){return(e=e._reactInternals)?Dr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var o=Oe(),n=br(e),i=qt(o,n);i.payload=t,r!=null&&(i.callback=r),t=vr(e,i,n),t!==null&&(kt(t,e,n,o),Mi(t,e,n))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var o=Oe(),n=br(e),i=qt(o,n);i.tag=1,i.payload=t,r!=null&&(i.callback=r),t=vr(e,i,n),t!==null&&(kt(t,e,n,o),Mi(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=Oe(),o=br(e),n=qt(r,o);n.tag=2,t!=null&&(n.callback=t),t=vr(e,n,o),t!==null&&(kt(t,e,o,r),Mi(t,e,o))}};function Mf(e,t,r,o,n,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,i,l):t.prototype&&t.prototype.isPureReactComponent?!hn(r,o)||!hn(n,i):!0}function jf(e,t,r){var o=!1,n=gr,i=t.contextType;return typeof i=="object"&&i!==null?i=lt(i):(n=He(t)?Rr:Se.current,o=t.contextTypes,i=(o=o!=null)?_o(e,n):gr),t=new t(r,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ki,e.stateNode=t,t._reactInternals=e,o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=i),t}function Hf(e,t,r,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,o),t.state!==e&&Ki.enqueueReplaceState(t,t.state,null)}function Pa(e,t,r,o){var n=e.stateNode;n.props=r,n.state=e.memoizedState,n.refs={},va(e);var i=t.contextType;typeof i=="object"&&i!==null?n.context=lt(i):(i=He(t)?Rr:Se.current,n.context=_o(e,i)),n.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ca(e,t,i,r),n.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof n.getSnapshotBeforeUpdate=="function"||typeof n.UNSAFE_componentWillMount!="function"&&typeof n.componentWillMount!="function"||(t=n.state,typeof n.componentWillMount=="function"&&n.componentWillMount(),typeof n.UNSAFE_componentWillMount=="function"&&n.UNSAFE_componentWillMount(),t!==n.state&&Ki.enqueueReplaceState(n,n.state,null),ji(e,r,n,o),n.state=e.memoizedState),typeof n.componentDidMount=="function"&&(e.flags|=4194308)}function To(e,t){try{var r="",o=t;do r+=k0(o),o=o.return;while(o);var n=r}catch(i){n=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:n,digest:null}}function Ta(e,t,r){return{value:e,source:null,stack:r!=null?r:null,digest:t!=null?t:null}}function Da(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Vm=typeof WeakMap=="function"?WeakMap:Map;function Wf(e,t,r){r=qt(-1,r),r.tag=3,r.payload={element:null};var o=t.value;return r.callback=function(){el||(el=!0,Ka=o),Da(e,t)},r}function $f(e,t,r){r=qt(-1,r),r.tag=3;var o=e.type.getDerivedStateFromError;if(typeof o=="function"){var n=t.value;r.payload=function(){return o(n)},r.callback=function(){Da(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(r.callback=function(){Da(e,t),typeof o!="function"&&(yr===null?yr=new Set([this]):yr.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),r}function qf(e,t,r){var o=e.pingCache;if(o===null){o=e.pingCache=new Vm;var n=new Set;o.set(t,n)}else n=o.get(t),n===void 0&&(n=new Set,o.set(t,n));n.has(r)||(n.add(r),e=l1.bind(null,e,t,r),t.then(e,e))}function Uf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Vf(e,t,r,o,n){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=qt(-1,1),t.tag=2,vr(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=n,e)}var Km=It.ReactCurrentOwner,We=!1;function De(e,t,r,o){t.child=e===null?ff(t,null,r,o):No(t,e.child,r,o)}function Kf(e,t,r,o,n){r=r.render;var i=t.ref;return Co(t,n),o=_a(e,t,r,o,i,n),r=xa(),e!==null&&!We?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n,Ut(e,t,n)):(oe&&r&&la(t),t.flags|=1,De(e,t,o,n),t.child)}function Xf(e,t,r,o,n){if(e===null){var i=r.type;return typeof i=="function"&&!eu(i)&&i.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=i,Yf(e,t,i,o,n)):(e=ll(r.type,null,o,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,(e.lanes&n)===0){var l=i.memoizedProps;if(r=r.compare,r=r!==null?r:hn,r(l,o)&&e.ref===t.ref)return Ut(e,t,n)}return t.flags|=1,e=Br(i,o),e.ref=t.ref,e.return=t,t.child=e}function Yf(e,t,r,o,n){if(e!==null){var i=e.memoizedProps;if(hn(i,o)&&e.ref===t.ref)if(We=!1,t.pendingProps=o=i,(e.lanes&n)!==0)(e.flags&131072)!==0&&(We=!0);else return t.lanes=e.lanes,Ut(e,t,n)}return Oa(e,t,r,o,n)}function Qf(e,t,r){var o=t.pendingProps,n=o.children,i=e!==null?e.memoizedState:null;if(o.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},J(Oo,Ge),Ge|=r;else{if((r&1073741824)===0)return e=i!==null?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,J(Oo,Ge),Ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=i!==null?i.baseLanes:r,J(Oo,Ge),Ge|=o}else i!==null?(o=i.baseLanes|r,t.memoizedState=null):o=r,J(Oo,Ge),Ge|=o;return De(e,t,n,r),t.child}function Jf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Oa(e,t,r,o,n){var i=He(r)?Rr:Se.current;return i=_o(t,i),Co(t,n),r=_a(e,t,r,o,i,n),o=xa(),e!==null&&!We?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n,Ut(e,t,n)):(oe&&o&&la(t),t.flags|=1,De(e,t,r,n),t.child)}function Zf(e,t,r,o,n){if(He(r)){var i=!0;Ti(t)}else i=!1;if(Co(t,n),t.stateNode===null)Yi(e,t),jf(t,r,o),Pa(t,r,o,n),o=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=r.contextType;typeof u=="object"&&u!==null?u=lt(u):(u=He(r)?Rr:Se.current,u=_o(t,u));var d=r.getDerivedStateFromProps,f=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";f||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==o||a!==u)&&Hf(t,l,o,u),hr=!1;var g=t.memoizedState;l.state=g,ji(t,o,l,n),a=t.memoizedState,s!==o||g!==a||je.current||hr?(typeof d=="function"&&(Ca(t,r,d,o),a=t.memoizedState),(s=hr||Mf(t,r,s,o,g,a,u))?(f||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=a),l.props=o,l.state=a,l.context=u,o=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{l=t.stateNode,pf(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:vt(t.type,s),l.props=u,f=t.pendingProps,g=l.context,a=r.contextType,typeof a=="object"&&a!==null?a=lt(a):(a=He(r)?Rr:Se.current,a=_o(t,a));var y=r.getDerivedStateFromProps;(d=typeof y=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==f||g!==a)&&Hf(t,l,o,a),hr=!1,g=t.memoizedState,l.state=g,ji(t,o,l,n);var v=t.memoizedState;s!==f||g!==v||je.current||hr?(typeof y=="function"&&(Ca(t,r,y,o),v=t.memoizedState),(u=hr||Mf(t,r,u,o,g,v,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(o,v,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(o,v,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=v),l.props=o,l.state=v,l.context=a,o=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),o=!1)}return Ra(e,t,r,o,i,n)}function Ra(e,t,r,o,n,i){Jf(e,t);var l=(t.flags&128)!==0;if(!o&&!l)return n&&rf(t,r,!1),Ut(e,t,i);o=t.stateNode,Km.current=t;var s=l&&typeof r.getDerivedStateFromError!="function"?null:o.render();return t.flags|=1,e!==null&&l?(t.child=No(t,e.child,null,i),t.child=No(t,null,s,i)):De(e,t,s,i),t.memoizedState=o.state,n&&rf(t,r,!0),t.child}function Gf(e){var t=e.stateNode;t.pendingContext?ef(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ef(e,t.context,!1),ma(e,t.containerInfo)}function eg(e,t,r,o,n){return Eo(),ca(n),t.flags|=256,De(e,t,r,o),t.child}var za={dehydrated:null,treeContext:null,retryLane:0};function Aa(e){return{baseLanes:e,cachePool:null,transitions:null}}function tg(e,t,r){var o=t.pendingProps,n=ie.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(n&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(n|=1),J(ie,n&1),e===null)return ua(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(l=o.children,e=o.fallback,i?(o=t.mode,i=t.child,l={mode:"hidden",children:l},(o&1)===0&&i!==null?(i.childLanes=0,i.pendingProps=l):i=sl(l,o,0,null),e=qr(e,o,r,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Aa(r),t.memoizedState=za,e):La(t,l));if(n=e.memoizedState,n!==null&&(s=n.dehydrated,s!==null))return Xm(e,t,l,o,s,n,r);if(i){i=o.fallback,l=t.mode,n=e.child,s=n.sibling;var a={mode:"hidden",children:o.children};return(l&1)===0&&t.child!==n?(o=t.child,o.childLanes=0,o.pendingProps=a,t.deletions=null):(o=Br(n,a),o.subtreeFlags=n.subtreeFlags&14680064),s!==null?i=Br(s,i):(i=qr(i,l,r,null),i.flags|=2),i.return=t,o.return=t,o.sibling=i,t.child=o,o=i,i=t.child,l=e.child.memoizedState,l=l===null?Aa(r):{baseLanes:l.baseLanes|r,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~r,t.memoizedState=za,o}return i=e.child,e=i.sibling,o=Br(i,{mode:"visible",children:o.children}),(t.mode&1)===0&&(o.lanes=r),o.return=t,o.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=o,t.memoizedState=null,o}function La(e,t){return t=sl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Xi(e,t,r,o){return o!==null&&ca(o),No(t,e.child,null,r),e=La(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xm(e,t,r,o,n,i,l){if(r)return t.flags&256?(t.flags&=-257,o=Ta(Error(P(422))),Xi(e,t,l,o)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=o.fallback,n=t.mode,o=sl({mode:"visible",children:o.children},n,0,null),i=qr(i,n,l,null),i.flags|=2,o.return=t,i.return=t,o.sibling=i,t.child=o,(t.mode&1)!==0&&No(t,e.child,null,l),t.child.memoizedState=Aa(l),t.memoizedState=za,i);if((t.mode&1)===0)return Xi(e,t,l,null);if(n.data==="$!"){if(o=n.nextSibling&&n.nextSibling.dataset,o)var s=o.dgst;return o=s,i=Error(P(419)),o=Ta(i,o,void 0),Xi(e,t,l,o)}if(s=(l&e.childLanes)!==0,We||s){if(o=ke,o!==null){switch(l&-l){case 4:n=2;break;case 16:n=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:n=32;break;case 536870912:n=268435456;break;default:n=0}n=(n&(o.suspendedLanes|l))!==0?0:n,n!==0&&n!==i.retryLane&&(i.retryLane=n,$t(e,n),kt(o,e,n,-1))}return Ga(),o=Ta(Error(P(421))),Xi(e,t,l,o)}return n.data==="$?"?(t.flags|=128,t.child=e.child,t=s1.bind(null,e),n._reactRetry=t,null):(e=i.treeContext,Ze=dr(n.nextSibling),Je=t,oe=!0,ht=null,e!==null&&(nt[it++]=Ht,nt[it++]=Wt,nt[it++]=zr,Ht=e.id,Wt=e.overflow,zr=t),t=La(t,o.children),t.flags|=4096,t)}function rg(e,t,r){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),pa(e.return,t,r)}function Ia(e,t,r,o,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:r,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=o,i.tail=r,i.tailMode=n)}function og(e,t,r){var o=t.pendingProps,n=o.revealOrder,i=o.tail;if(De(e,t,o.children,r),o=ie.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&rg(e,r,t);else if(e.tag===19)rg(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(J(ie,o),(t.mode&1)===0)t.memoizedState=null;else switch(n){case"forwards":for(r=t.child,n=null;r!==null;)e=r.alternate,e!==null&&Hi(e)===null&&(n=r),r=r.sibling;r=n,r===null?(n=t.child,t.child=null):(n=r.sibling,r.sibling=null),Ia(t,!1,n,r,i);break;case"backwards":for(r=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Hi(e)===null){t.child=n;break}e=n.sibling,n.sibling=r,r=n,n=e}Ia(t,!0,r,null,i);break;case"together":Ia(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yi(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ut(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),jr|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,r=Br(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Br(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function Ym(e,t,r){switch(t.tag){case 3:Gf(t),Eo();break;case 5:mf(t);break;case 1:He(t.type)&&Ti(t);break;case 4:ma(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,n=t.memoizedProps.value;J(Li,o._currentValue),o._currentValue=n;break;case 13:if(o=t.memoizedState,o!==null)return o.dehydrated!==null?(J(ie,ie.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?tg(e,t,r):(J(ie,ie.current&1),e=Ut(e,t,r),e!==null?e.sibling:null);J(ie,ie.current&1);break;case 19:if(o=(r&t.childLanes)!==0,(e.flags&128)!==0){if(o)return og(e,t,r);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),J(ie,ie.current),o)break;return null;case 22:case 23:return t.lanes=0,Qf(e,t,r)}return Ut(e,t,r)}var ng,Ma,ig,lg;ng=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Ma=function(){},ig=function(e,t,r,o){var n=e.memoizedProps;if(n!==o){e=t.stateNode,Ir(Ft.current);var i=null;switch(r){case"input":n=ps(e,n),o=ps(e,o),i=[];break;case"select":n=ne({},n,{value:void 0}),o=ne({},o,{value:void 0}),i=[];break;case"textarea":n=ms(e,n),o=ms(e,o),i=[];break;default:typeof n.onClick!="function"&&typeof o.onClick=="function"&&(e.onclick=Fi)}ks(r,o);var l;r=null;for(u in n)if(!o.hasOwnProperty(u)&&n.hasOwnProperty(u)&&n[u]!=null)if(u==="style"){var s=n[u];for(l in s)s.hasOwnProperty(l)&&(r||(r={}),r[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ko.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in o){var a=o[u];if(s=n!=null?n[u]:void 0,o.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(r||(r={}),r[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(r||(r={}),r[l]=a[l])}else r||(i||(i=[]),i.push(u,r)),r=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ko.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&ee("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}r&&(i=i||[]).push("style",r);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},lg=function(e,t,r,o){r!==o&&(t.flags|=4)};function Pn(e,t){if(!oe)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var o=null;r!==null;)r.alternate!==null&&(o=r),r=r.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function Ne(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,o=0;if(t)for(var n=e.child;n!==null;)r|=n.lanes|n.childLanes,o|=n.subtreeFlags&14680064,o|=n.flags&14680064,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)r|=n.lanes|n.childLanes,o|=n.subtreeFlags,o|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=o,e.childLanes=r,t}function Qm(e,t,r){var o=t.pendingProps;switch(sa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ne(t),null;case 1:return He(t.type)&&Pi(),Ne(t),null;case 3:return o=t.stateNode,Po(),te(je),te(Se),ba(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(e===null||e.child===null)&&(zi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ht!==null&&(Qa(ht),ht=null))),Ma(e,t),Ne(t),null;case 5:ya(t);var n=Ir(Sn.current);if(r=t.type,e!==null&&t.stateNode!=null)ig(e,t,r,o,n),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(t.stateNode===null)throw Error(P(166));return Ne(t),null}if(e=Ir(Ft.current),zi(t)){o=t.stateNode,r=t.type;var i=t.memoizedProps;switch(o[Nt]=t,o[bn]=i,e=(t.mode&1)!==0,r){case"dialog":ee("cancel",o),ee("close",o);break;case"iframe":case"object":case"embed":ee("load",o);break;case"video":case"audio":for(n=0;n<mn.length;n++)ee(mn[n],o);break;case"source":ee("error",o);break;case"img":case"image":case"link":ee("error",o),ee("load",o);break;case"details":ee("toggle",o);break;case"input":Mc(o,i),ee("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!i.multiple},ee("invalid",o);break;case"textarea":Wc(o,i),ee("invalid",o)}ks(r,i),n=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?o.textContent!==s&&(i.suppressHydrationWarning!==!0&&Ni(o.textContent,s,e),n=["children",s]):typeof s=="number"&&o.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Ni(o.textContent,s,e),n=["children",""+s]):Ko.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&ee("scroll",o)}switch(r){case"input":ni(o),Hc(o,i,!0);break;case"textarea":ni(o),qc(o);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(o.onclick=Fi)}o=n,t.updateQueue=o,o!==null&&(t.flags|=4)}else{l=n.nodeType===9?n:n.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Uc(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof o.is=="string"?e=l.createElement(r,{is:o.is}):(e=l.createElement(r),r==="select"&&(l=e,o.multiple?l.multiple=!0:o.size&&(l.size=o.size))):e=l.createElementNS(e,r),e[Nt]=t,e[bn]=o,ng(e,t,!1,!1),t.stateNode=e;e:{switch(l=bs(r,o),r){case"dialog":ee("cancel",e),ee("close",e),n=o;break;case"iframe":case"object":case"embed":ee("load",e),n=o;break;case"video":case"audio":for(n=0;n<mn.length;n++)ee(mn[n],e);n=o;break;case"source":ee("error",e),n=o;break;case"img":case"image":case"link":ee("error",e),ee("load",e),n=o;break;case"details":ee("toggle",e),n=o;break;case"input":Mc(e,o),n=ps(e,o),ee("invalid",e);break;case"option":n=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},n=ne({},o,{value:void 0}),ee("invalid",e);break;case"textarea":Wc(e,o),n=ms(e,o),ee("invalid",e);break;default:n=o}ks(r,n),s=n;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Xc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Vc(e,a)):i==="children"?typeof a=="string"?(r!=="textarea"||a!=="")&&Jo(e,a):typeof a=="number"&&Jo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ko.hasOwnProperty(i)?a!=null&&i==="onScroll"&&ee("scroll",e):a!=null&&os(e,i,a,l))}switch(r){case"input":ni(e),Hc(e,o,!1);break;case"textarea":ni(e),qc(e);break;case"option":o.value!=null&&e.setAttribute("value",""+nr(o.value));break;case"select":e.multiple=!!o.multiple,i=o.value,i!=null?co(e,!!o.multiple,i,!1):o.defaultValue!=null&&co(e,!!o.multiple,o.defaultValue,!0);break;default:typeof n.onClick=="function"&&(e.onclick=Fi)}switch(r){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ne(t),null;case 6:if(e&&t.stateNode!=null)lg(e,t,e.memoizedProps,o);else{if(typeof o!="string"&&t.stateNode===null)throw Error(P(166));if(r=Ir(Sn.current),Ir(Ft.current),zi(t)){if(o=t.stateNode,r=t.memoizedProps,o[Nt]=t,(i=o.nodeValue!==r)&&(e=Je,e!==null))switch(e.tag){case 3:Ni(o.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ni(o.nodeValue,r,(e.mode&1)!==0)}i&&(t.flags|=4)}else o=(r.nodeType===9?r:r.ownerDocument).createTextNode(o),o[Nt]=t,t.stateNode=o}return Ne(t),null;case 13:if(te(ie),o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(oe&&Ze!==null&&(t.mode&1)!==0&&(t.flags&128)===0)uf(),Eo(),t.flags|=98560,i=!1;else if(i=zi(t),o!==null&&o.dehydrated!==null){if(e===null){if(!i)throw Error(P(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(P(317));i[Nt]=t}else Eo(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ne(t),i=!1}else ht!==null&&(Qa(ht),ht=null),i=!0;if(!i)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(o=o!==null,o!==(e!==null&&e.memoizedState!==null)&&o&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(ie.current&1)!==0?ve===0&&(ve=3):Ga())),t.updateQueue!==null&&(t.flags|=4),Ne(t),null);case 4:return Po(),Ma(e,t),e===null&&yn(t.stateNode.containerInfo),Ne(t),null;case 10:return ga(t.type._context),Ne(t),null;case 17:return He(t.type)&&Pi(),Ne(t),null;case 19:if(te(ie),i=t.memoizedState,i===null)return Ne(t),null;if(o=(t.flags&128)!==0,l=i.rendering,l===null)if(o)Pn(i,!1);else{if(ve!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(l=Hi(e),l!==null){for(t.flags|=128,Pn(i,!1),o=l.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=r,r=t.child;r!==null;)i=r,e=o,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return J(ie,ie.current&1|2),t.child}e=e.sibling}i.tail!==null&&de()>Ro&&(t.flags|=128,o=!0,Pn(i,!1),t.lanes=4194304)}else{if(!o)if(e=Hi(l),e!==null){if(t.flags|=128,o=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Pn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!oe)return Ne(t),null}else 2*de()-i.renderingStartTime>Ro&&r!==1073741824&&(t.flags|=128,o=!0,Pn(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(r=i.last,r!==null?r.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=de(),t.sibling=null,r=ie.current,J(ie,o?r&1|2:r&1),t):(Ne(t),null);case 22:case 23:return Za(),o=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==o&&(t.flags|=8192),o&&(t.mode&1)!==0?(Ge&1073741824)!==0&&(Ne(t),t.subtreeFlags&6&&(t.flags|=8192)):Ne(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function Jm(e,t){switch(sa(t),t.tag){case 1:return He(t.type)&&Pi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Po(),te(je),te(Se),ba(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return ya(t),null;case 13:if(te(ie),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));Eo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return te(ie),null;case 4:return Po(),null;case 10:return ga(t.type._context),null;case 22:case 23:return Za(),null;case 24:return null;default:return null}}var Qi=!1,Fe=!1,Zm=typeof WeakSet=="function"?WeakSet:Set,R=null;function Do(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(o){ae(e,t,o)}else r.current=null}function ja(e,t,r){try{r()}catch(o){ae(e,t,o)}}var sg=!1;function Gm(e,t){if(Zs=vi,e=Id(),qs(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var o=r.getSelection&&r.getSelection();if(o&&o.rangeCount!==0){r=o.anchorNode;var n=o.anchorOffset,i=o.focusNode;o=o.focusOffset;try{r.nodeType,i.nodeType}catch{r=null;break e}var l=0,s=-1,a=-1,u=0,d=0,f=e,g=null;t:for(;;){for(var y;f!==r||n!==0&&f.nodeType!==3||(s=l+n),f!==i||o!==0&&f.nodeType!==3||(a=l+o),f.nodeType===3&&(l+=f.nodeValue.length),(y=f.firstChild)!==null;)g=f,f=y;for(;;){if(f===e)break t;if(g===r&&++u===n&&(s=l),g===i&&++d===o&&(a=l),(y=f.nextSibling)!==null)break;f=g,g=f.parentNode}f=y}r=s===-1||a===-1?null:{start:s,end:a}}else r=null}r=r||{start:0,end:0}}else r=null;for(Gs={focusedElem:e,selectionRange:r},vi=!1,R=t;R!==null;)if(t=R,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,R=e;else for(;R!==null;){t=R;try{var v=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var k=v.memoizedProps,_=v.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?k:vt(t.type,k),_);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var c=t.stateNode.containerInfo;c.nodeType===1?c.textContent="":c.nodeType===9&&c.documentElement&&c.removeChild(c.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(p){ae(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,R=e;break}R=t.return}return v=sg,sg=!1,v}function Tn(e,t,r){var o=t.updateQueue;if(o=o!==null?o.lastEffect:null,o!==null){var n=o=o.next;do{if((n.tag&e)===e){var i=n.destroy;n.destroy=void 0,i!==void 0&&ja(t,r,i)}n=n.next}while(n!==o)}}function Ji(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var o=r.create;r.destroy=o()}r=r.next}while(r!==t)}}function Ha(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function ag(e){var t=e.alternate;t!==null&&(e.alternate=null,ag(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[bn],delete t[oa],delete t[zm],delete t[Am])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ug(e){return e.tag===5||e.tag===3||e.tag===4}function cg(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ug(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Wa(e,t,r){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Fi));else if(o!==4&&(e=e.child,e!==null))for(Wa(e,t,r),e=e.sibling;e!==null;)Wa(e,t,r),e=e.sibling}function $a(e,t,r){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(o!==4&&(e=e.child,e!==null))for($a(e,t,r),e=e.sibling;e!==null;)$a(e,t,r),e=e.sibling}var we=null,mt=!1;function mr(e,t,r){for(r=r.child;r!==null;)dg(e,t,r),r=r.sibling}function dg(e,t,r){if(Et&&typeof Et.onCommitFiberUnmount=="function")try{Et.onCommitFiberUnmount(ci,r)}catch{}switch(r.tag){case 5:Fe||Do(r,t);case 6:var o=we,n=mt;we=null,mr(e,t,r),we=o,mt=n,we!==null&&(mt?(e=we,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):we.removeChild(r.stateNode));break;case 18:we!==null&&(mt?(e=we,r=r.stateNode,e.nodeType===8?ra(e.parentNode,r):e.nodeType===1&&ra(e,r),un(e)):ra(we,r.stateNode));break;case 4:o=we,n=mt,we=r.stateNode.containerInfo,mt=!0,mr(e,t,r),we=o,mt=n;break;case 0:case 11:case 14:case 15:if(!Fe&&(o=r.updateQueue,o!==null&&(o=o.lastEffect,o!==null))){n=o=o.next;do{var i=n,l=i.destroy;i=i.tag,l!==void 0&&((i&2)!==0||(i&4)!==0)&&ja(r,t,l),n=n.next}while(n!==o)}mr(e,t,r);break;case 1:if(!Fe&&(Do(r,t),o=r.stateNode,typeof o.componentWillUnmount=="function"))try{o.props=r.memoizedProps,o.state=r.memoizedState,o.componentWillUnmount()}catch(s){ae(r,t,s)}mr(e,t,r);break;case 21:mr(e,t,r);break;case 22:r.mode&1?(Fe=(o=Fe)||r.memoizedState!==null,mr(e,t,r),Fe=o):mr(e,t,r);break;default:mr(e,t,r)}}function fg(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Zm),t.forEach(function(o){var n=a1.bind(null,e,o);r.has(o)||(r.add(o),o.then(n,n))})}}function yt(e,t){var r=t.deletions;if(r!==null)for(var o=0;o<r.length;o++){var n=r[o];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:we=s.stateNode,mt=!1;break e;case 3:we=s.stateNode.containerInfo,mt=!0;break e;case 4:we=s.stateNode.containerInfo,mt=!0;break e}s=s.return}if(we===null)throw Error(P(160));dg(i,l,n),we=null,mt=!1;var a=n.alternate;a!==null&&(a.return=null),n.return=null}catch(u){ae(n,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)gg(t,e),t=t.sibling}function gg(e,t){var r=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(yt(t,e),Pt(e),o&4){try{Tn(3,e,e.return),Ji(3,e)}catch(k){ae(e,e.return,k)}try{Tn(5,e,e.return)}catch(k){ae(e,e.return,k)}}break;case 1:yt(t,e),Pt(e),o&512&&r!==null&&Do(r,r.return);break;case 5:if(yt(t,e),Pt(e),o&512&&r!==null&&Do(r,r.return),e.flags&32){var n=e.stateNode;try{Jo(n,"")}catch(k){ae(e,e.return,k)}}if(o&4&&(n=e.stateNode,n!=null)){var i=e.memoizedProps,l=r!==null?r.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&jc(n,i),bs(s,l);var u=bs(s,i);for(l=0;l<a.length;l+=2){var d=a[l],f=a[l+1];d==="style"?Xc(n,f):d==="dangerouslySetInnerHTML"?Vc(n,f):d==="children"?Jo(n,f):os(n,d,f,u)}switch(s){case"input":hs(n,i);break;case"textarea":$c(n,i);break;case"select":var g=n._wrapperState.wasMultiple;n._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?co(n,!!i.multiple,y,!1):g!==!!i.multiple&&(i.defaultValue!=null?co(n,!!i.multiple,i.defaultValue,!0):co(n,!!i.multiple,i.multiple?[]:"",!1))}n[bn]=i}catch(k){ae(e,e.return,k)}}break;case 6:if(yt(t,e),Pt(e),o&4){if(e.stateNode===null)throw Error(P(162));n=e.stateNode,i=e.memoizedProps;try{n.nodeValue=i}catch(k){ae(e,e.return,k)}}break;case 3:if(yt(t,e),Pt(e),o&4&&r!==null&&r.memoizedState.isDehydrated)try{un(t.containerInfo)}catch(k){ae(e,e.return,k)}break;case 4:yt(t,e),Pt(e);break;case 13:yt(t,e),Pt(e),n=e.child,n.flags&8192&&(i=n.memoizedState!==null,n.stateNode.isHidden=i,!i||n.alternate!==null&&n.alternate.memoizedState!==null||(Va=de())),o&4&&fg(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(Fe=(u=Fe)||d,yt(t,e),Fe=u):yt(t,e),Pt(e),o&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&(e.mode&1)!==0)for(R=e,d=e.child;d!==null;){for(f=R=d;R!==null;){switch(g=R,y=g.child,g.tag){case 0:case 11:case 14:case 15:Tn(4,g,g.return);break;case 1:Do(g,g.return);var v=g.stateNode;if(typeof v.componentWillUnmount=="function"){o=g,r=g.return;try{t=o,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(k){ae(o,r,k)}}break;case 5:Do(g,g.return);break;case 22:if(g.memoizedState!==null){vg(f);continue}}y!==null?(y.return=g,R=y):vg(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{n=f.stateNode,u?(i=n.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=f.stateNode,a=f.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=Kc("display",l))}catch(k){ae(e,e.return,k)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(k){ae(e,e.return,k)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:yt(t,e),Pt(e),o&4&&fg(e);break;case 21:break;default:yt(t,e),Pt(e)}}function Pt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(ug(r)){var o=r;break e}r=r.return}throw Error(P(160))}switch(o.tag){case 5:var n=o.stateNode;o.flags&32&&(Jo(n,""),o.flags&=-33);var i=cg(e);$a(e,i,n);break;case 3:case 4:var l=o.stateNode.containerInfo,s=cg(e);Wa(e,s,l);break;default:throw Error(P(161))}}catch(a){ae(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function e1(e,t,r){R=e,pg(e)}function pg(e,t,r){for(var o=(e.mode&1)!==0;R!==null;){var n=R,i=n.child;if(n.tag===22&&o){var l=n.memoizedState!==null||Qi;if(!l){var s=n.alternate,a=s!==null&&s.memoizedState!==null||Fe;s=Qi;var u=Fe;if(Qi=l,(Fe=a)&&!u)for(R=n;R!==null;)l=R,a=l.child,l.tag===22&&l.memoizedState!==null?mg(n):a!==null?(a.return=l,R=a):mg(n);for(;i!==null;)R=i,pg(i),i=i.sibling;R=n,Qi=s,Fe=u}hg(e)}else(n.subtreeFlags&8772)!==0&&i!==null?(i.return=n,R=i):hg(e)}}function hg(e){for(;R!==null;){var t=R;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Fe||Ji(5,t);break;case 1:var o=t.stateNode;if(t.flags&4&&!Fe)if(r===null)o.componentDidMount();else{var n=t.elementType===t.type?r.memoizedProps:vt(t.type,r.memoizedProps);o.componentDidUpdate(n,r.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&vf(t,i,o);break;case 3:var l=t.updateQueue;if(l!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}vf(t,l,r)}break;case 5:var s=t.stateNode;if(r===null&&t.flags&4){r=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break;case"img":a.src&&(r.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&un(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Fe||t.flags&512&&Ha(t)}catch(g){ae(t,t.return,g)}}if(t===e){R=null;break}if(r=t.sibling,r!==null){r.return=t.return,R=r;break}R=t.return}}function vg(e){for(;R!==null;){var t=R;if(t===e){R=null;break}var r=t.sibling;if(r!==null){r.return=t.return,R=r;break}R=t.return}}function mg(e){for(;R!==null;){var t=R;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Ji(4,t)}catch(a){ae(t,r,a)}break;case 1:var o=t.stateNode;if(typeof o.componentDidMount=="function"){var n=t.return;try{o.componentDidMount()}catch(a){ae(t,n,a)}}var i=t.return;try{Ha(t)}catch(a){ae(t,i,a)}break;case 5:var l=t.return;try{Ha(t)}catch(a){ae(t,l,a)}}}catch(a){ae(t,t.return,a)}if(t===e){R=null;break}var s=t.sibling;if(s!==null){s.return=t.return,R=s;break}R=t.return}}var t1=Math.ceil,Zi=It.ReactCurrentDispatcher,qa=It.ReactCurrentOwner,at=It.ReactCurrentBatchConfig,q=0,ke=null,ge=null,Be=0,Ge=0,Oo=fr(0),ve=0,Dn=null,jr=0,Gi=0,Ua=0,On=null,$e=null,Va=0,Ro=1/0,Vt=null,el=!1,Ka=null,yr=null,tl=!1,kr=null,rl=0,Rn=0,Xa=null,ol=-1,nl=0;function Oe(){return(q&6)!==0?de():ol!==-1?ol:ol=de()}function br(e){return(e.mode&1)===0?1:(q&2)!==0&&Be!==0?Be&-Be:Im.transition!==null?(nl===0&&(nl=ud()),nl):(e=Y,e!==0||(e=window.event,e=e===void 0?16:yd(e.type)),e)}function kt(e,t,r,o){if(50<Rn)throw Rn=0,Xa=null,Error(P(185));on(e,r,o),((q&2)===0||e!==ke)&&(e===ke&&((q&2)===0&&(Gi|=r),ve===4&&wr(e,Be)),qe(e,o),r===1&&q===0&&(t.mode&1)===0&&(Ro=de()+500,Di&&pr()))}function qe(e,t){var r=e.callbackNode;I0(e,t);var o=gi(e,e===ke?Be:0);if(o===0)r!==null&&ld(r),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(r!=null&&ld(r),t===1)e.tag===0?Lm(kg.bind(null,e)):of(kg.bind(null,e)),Om(function(){(q&6)===0&&pr()}),r=null;else{switch(cd(o)){case 1:r=Ns;break;case 4:r=sd;break;case 16:r=ui;break;case 536870912:r=ad;break;default:r=ui}r=Ng(r,yg.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function yg(e,t){if(ol=-1,nl=0,(q&6)!==0)throw Error(P(327));var r=e.callbackNode;if(zo()&&e.callbackNode!==r)return null;var o=gi(e,e===ke?Be:0);if(o===0)return null;if((o&30)!==0||(o&e.expiredLanes)!==0||t)t=il(e,o);else{t=o;var n=q;q|=2;var i=wg();(ke!==e||Be!==t)&&(Vt=null,Ro=de()+500,Wr(e,t));do try{n1();break}catch(s){bg(e,s)}while(1);fa(),Zi.current=i,q=n,ge!==null?t=0:(ke=null,Be=0,t=ve)}if(t!==0){if(t===2&&(n=Fs(e),n!==0&&(o=n,t=Ya(e,n))),t===1)throw r=Dn,Wr(e,0),wr(e,o),qe(e,de()),r;if(t===6)wr(e,o);else{if(n=e.current.alternate,(o&30)===0&&!r1(n)&&(t=il(e,o),t===2&&(i=Fs(e),i!==0&&(o=i,t=Ya(e,i))),t===1))throw r=Dn,Wr(e,0),wr(e,o),qe(e,de()),r;switch(e.finishedWork=n,e.finishedLanes=o,t){case 0:case 1:throw Error(P(345));case 2:$r(e,$e,Vt);break;case 3:if(wr(e,o),(o&130023424)===o&&(t=Va+500-de(),10<t)){if(gi(e,0)!==0)break;if(n=e.suspendedLanes,(n&o)!==o){Oe(),e.pingedLanes|=e.suspendedLanes&n;break}e.timeoutHandle=ta($r.bind(null,e,$e,Vt),t);break}$r(e,$e,Vt);break;case 4:if(wr(e,o),(o&4194240)===o)break;for(t=e.eventTimes,n=-1;0<o;){var l=31-gt(o);i=1<<l,l=t[l],l>n&&(n=l),o&=~i}if(o=n,o=de()-o,o=(120>o?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*t1(o/1960))-o,10<o){e.timeoutHandle=ta($r.bind(null,e,$e,Vt),o);break}$r(e,$e,Vt);break;case 5:$r(e,$e,Vt);break;default:throw Error(P(329))}}}return qe(e,de()),e.callbackNode===r?yg.bind(null,e):null}function Ya(e,t){var r=On;return e.current.memoizedState.isDehydrated&&(Wr(e,t).flags|=256),e=il(e,t),e!==2&&(t=$e,$e=r,t!==null&&Qa(t)),e}function Qa(e){$e===null?$e=e:$e.push.apply($e,e)}function r1(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var o=0;o<r.length;o++){var n=r[o],i=n.getSnapshot;n=n.value;try{if(!pt(i(),n))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function wr(e,t){for(t&=~Ua,t&=~Gi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-gt(t),o=1<<r;e[r]=-1,t&=~o}}function kg(e){if((q&6)!==0)throw Error(P(327));zo();var t=gi(e,0);if((t&1)===0)return qe(e,de()),null;var r=il(e,t);if(e.tag!==0&&r===2){var o=Fs(e);o!==0&&(t=o,r=Ya(e,o))}if(r===1)throw r=Dn,Wr(e,0),wr(e,t),qe(e,de()),r;if(r===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$r(e,$e,Vt),qe(e,de()),null}function Ja(e,t){var r=q;q|=1;try{return e(t)}finally{q=r,q===0&&(Ro=de()+500,Di&&pr())}}function Hr(e){kr!==null&&kr.tag===0&&(q&6)===0&&zo();var t=q;q|=1;var r=at.transition,o=Y;try{if(at.transition=null,Y=1,e)return e()}finally{Y=o,at.transition=r,q=t,(q&6)===0&&pr()}}function Za(){Ge=Oo.current,te(Oo)}function Wr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Dm(r)),ge!==null)for(r=ge.return;r!==null;){var o=r;switch(sa(o),o.tag){case 1:o=o.type.childContextTypes,o!=null&&Pi();break;case 3:Po(),te(je),te(Se),ba();break;case 5:ya(o);break;case 4:Po();break;case 13:te(ie);break;case 19:te(ie);break;case 10:ga(o.type._context);break;case 22:case 23:Za()}r=r.return}if(ke=e,ge=e=Br(e.current,null),Be=Ge=t,ve=0,Dn=null,Ua=Gi=jr=0,$e=On=null,Lr!==null){for(t=0;t<Lr.length;t++)if(r=Lr[t],o=r.interleaved,o!==null){r.interleaved=null;var n=o.next,i=r.pending;if(i!==null){var l=i.next;i.next=n,o.next=l}r.pending=o}Lr=null}return e}function bg(e,t){do{var r=ge;try{if(fa(),Wi.current=Vi,$i){for(var o=le.memoizedState;o!==null;){var n=o.queue;n!==null&&(n.pending=null),o=o.next}$i=!1}if(Mr=0,ye=he=le=null,En=!1,Nn=0,qa.current=null,r===null||r.return===null){ve=1,Dn=t,ge=null;break}e:{var i=e,l=r.return,s=r,a=t;if(t=Be,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,f=d.tag;if((d.mode&1)===0&&(f===0||f===11||f===15)){var g=d.alternate;g?(d.updateQueue=g.updateQueue,d.memoizedState=g.memoizedState,d.lanes=g.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Uf(l);if(y!==null){y.flags&=-257,Vf(y,l,s,i,t),y.mode&1&&qf(i,u,t),t=y,a=u;var v=t.updateQueue;if(v===null){var k=new Set;k.add(a),t.updateQueue=k}else v.add(a);break e}else{if((t&1)===0){qf(i,u,t),Ga();break e}a=Error(P(426))}}else if(oe&&s.mode&1){var _=Uf(l);if(_!==null){(_.flags&65536)===0&&(_.flags|=256),Vf(_,l,s,i,t),ca(To(a,s));break e}}i=a=To(a,s),ve!==4&&(ve=2),On===null?On=[i]:On.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=Wf(i,a,t);hf(i,m);break e;case 1:s=a;var h=i.type,c=i.stateNode;if((i.flags&128)===0&&(typeof h.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(yr===null||!yr.has(c)))){i.flags|=65536,t&=-t,i.lanes|=t;var p=$f(i,s,t);hf(i,p);break e}}i=i.return}while(i!==null)}_g(r)}catch(b){t=b,ge===r&&r!==null&&(ge=r=r.return);continue}break}while(1)}function wg(){var e=Zi.current;return Zi.current=Vi,e===null?Vi:e}function Ga(){(ve===0||ve===3||ve===2)&&(ve=4),ke===null||(jr&268435455)===0&&(Gi&268435455)===0||wr(ke,Be)}function il(e,t){var r=q;q|=2;var o=wg();(ke!==e||Be!==t)&&(Vt=null,Wr(e,t));do try{o1();break}catch(n){bg(e,n)}while(1);if(fa(),q=r,Zi.current=o,ge!==null)throw Error(P(261));return ke=null,Be=0,ve}function o1(){for(;ge!==null;)Bg(ge)}function n1(){for(;ge!==null&&!C0();)Bg(ge)}function Bg(e){var t=Eg(e.alternate,e,Ge);e.memoizedProps=e.pendingProps,t===null?_g(e):ge=t,qa.current=null}function _g(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=Qm(r,t,Ge),r!==null){ge=r;return}}else{if(r=Jm(r,t),r!==null){r.flags&=32767,ge=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,ge=null;return}}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);ve===0&&(ve=5)}function $r(e,t,r){var o=Y,n=at.transition;try{at.transition=null,Y=1,i1(e,t,r,o)}finally{at.transition=n,Y=o}return null}function i1(e,t,r,o){do zo();while(kr!==null);if((q&6)!==0)throw Error(P(327));r=e.finishedWork;var n=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var i=r.lanes|r.childLanes;if(M0(e,i),e===ke&&(ge=ke=null,Be=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||tl||(tl=!0,Ng(ui,function(){return zo(),null})),i=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||i){i=at.transition,at.transition=null;var l=Y;Y=1;var s=q;q|=4,qa.current=null,Gm(e,r),gg(r,e),Sm(Gs),vi=!!Zs,Gs=Zs=null,e.current=r,e1(r),P0(),q=s,Y=l,at.transition=i}else e.current=r;if(tl&&(tl=!1,kr=e,rl=n),i=e.pendingLanes,i===0&&(yr=null),O0(r.stateNode),qe(e,de()),t!==null)for(o=e.onRecoverableError,r=0;r<t.length;r++)n=t[r],o(n.value,{componentStack:n.stack,digest:n.digest});if(el)throw el=!1,e=Ka,Ka=null,e;return(rl&1)!==0&&e.tag!==0&&zo(),i=e.pendingLanes,(i&1)!==0?e===Xa?Rn++:(Rn=0,Xa=e):Rn=0,pr(),null}function zo(){if(kr!==null){var e=cd(rl),t=at.transition,r=Y;try{if(at.transition=null,Y=16>e?16:e,kr===null)var o=!1;else{if(e=kr,kr=null,rl=0,(q&6)!==0)throw Error(P(331));var n=q;for(q|=4,R=e.current;R!==null;){var i=R,l=i.child;if((R.flags&16)!==0){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(R=u;R!==null;){var d=R;switch(d.tag){case 0:case 11:case 15:Tn(8,d,i)}var f=d.child;if(f!==null)f.return=d,R=f;else for(;R!==null;){d=R;var g=d.sibling,y=d.return;if(ag(d),d===u){R=null;break}if(g!==null){g.return=y,R=g;break}R=y}}}var v=i.alternate;if(v!==null){var k=v.child;if(k!==null){v.child=null;do{var _=k.sibling;k.sibling=null,k=_}while(k!==null)}}R=i}}if((i.subtreeFlags&2064)!==0&&l!==null)l.return=i,R=l;else e:for(;R!==null;){if(i=R,(i.flags&2048)!==0)switch(i.tag){case 0:case 11:case 15:Tn(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,R=m;break e}R=i.return}}var h=e.current;for(R=h;R!==null;){l=R;var c=l.child;if((l.subtreeFlags&2064)!==0&&c!==null)c.return=l,R=c;else e:for(l=h;R!==null;){if(s=R,(s.flags&2048)!==0)try{switch(s.tag){case 0:case 11:case 15:Ji(9,s)}}catch(b){ae(s,s.return,b)}if(s===l){R=null;break e}var p=s.sibling;if(p!==null){p.return=s.return,R=p;break e}R=s.return}}if(q=n,pr(),Et&&typeof Et.onPostCommitFiberRoot=="function")try{Et.onPostCommitFiberRoot(ci,e)}catch{}o=!0}return o}finally{Y=r,at.transition=t}}return!1}function xg(e,t,r){t=To(r,t),t=Wf(e,t,1),e=vr(e,t,1),t=Oe(),e!==null&&(on(e,1,t),qe(e,t))}function ae(e,t,r){if(e.tag===3)xg(e,e,r);else for(;t!==null;){if(t.tag===3){xg(t,e,r);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(yr===null||!yr.has(o))){e=To(r,e),e=$f(t,e,1),t=vr(t,e,1),e=Oe(),t!==null&&(on(t,1,e),qe(t,e));break}}t=t.return}}function l1(e,t,r){var o=e.pingCache;o!==null&&o.delete(t),t=Oe(),e.pingedLanes|=e.suspendedLanes&r,ke===e&&(Be&r)===r&&(ve===4||ve===3&&(Be&130023424)===Be&&500>de()-Va?Wr(e,0):Ua|=r),qe(e,t)}function Sg(e,t){t===0&&((e.mode&1)===0?t=1:(t=fi,fi<<=1,(fi&130023424)===0&&(fi=4194304)));var r=Oe();e=$t(e,t),e!==null&&(on(e,t,r),qe(e,r))}function s1(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Sg(e,r)}function a1(e,t){var r=0;switch(e.tag){case 13:var o=e.stateNode,n=e.memoizedState;n!==null&&(r=n.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(P(314))}o!==null&&o.delete(t),Sg(e,r)}var Eg;Eg=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||je.current)We=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return We=!1,Ym(e,t,r);We=(e.flags&131072)!==0}else We=!1,oe&&(t.flags&1048576)!==0&&nf(t,Ri,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;Yi(e,t),e=t.pendingProps;var n=_o(t,Se.current);Co(t,r),n=_a(null,t,o,e,n,r);var i=xa();return t.flags|=1,typeof n=="object"&&n!==null&&typeof n.render=="function"&&n.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,He(o)?(i=!0,Ti(t)):i=!1,t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,va(t),n.updater=Ki,t.stateNode=n,n._reactInternals=t,Pa(t,o,e,r),t=Ra(null,t,o,!0,i,r)):(t.tag=0,oe&&i&&la(t),De(null,t,n,r),t=t.child),t;case 16:o=t.elementType;e:{switch(Yi(e,t),e=t.pendingProps,n=o._init,o=n(o._payload),t.type=o,n=t.tag=c1(o),e=vt(o,e),n){case 0:t=Oa(null,t,o,e,r);break e;case 1:t=Zf(null,t,o,e,r);break e;case 11:t=Kf(null,t,o,e,r);break e;case 14:t=Xf(null,t,o,vt(o.type,e),r);break e}throw Error(P(306,o,""))}return t;case 0:return o=t.type,n=t.pendingProps,n=t.elementType===o?n:vt(o,n),Oa(e,t,o,n,r);case 1:return o=t.type,n=t.pendingProps,n=t.elementType===o?n:vt(o,n),Zf(e,t,o,n,r);case 3:e:{if(Gf(t),e===null)throw Error(P(387));o=t.pendingProps,i=t.memoizedState,n=i.element,pf(e,t),ji(t,o,null,r);var l=t.memoizedState;if(o=l.element,i.isDehydrated)if(i={element:o,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){n=To(Error(P(423)),t),t=eg(e,t,o,r,n);break e}else if(o!==n){n=To(Error(P(424)),t),t=eg(e,t,o,r,n);break e}else for(Ze=dr(t.stateNode.containerInfo.firstChild),Je=t,oe=!0,ht=null,r=ff(t,null,o,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Eo(),o===n){t=Ut(e,t,r);break e}De(e,t,o,r)}t=t.child}return t;case 5:return mf(t),e===null&&ua(t),o=t.type,n=t.pendingProps,i=e!==null?e.memoizedProps:null,l=n.children,ea(o,n)?l=null:i!==null&&ea(o,i)&&(t.flags|=32),Jf(e,t),De(e,t,l,r),t.child;case 6:return e===null&&ua(t),null;case 13:return tg(e,t,r);case 4:return ma(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=No(t,null,o,r):De(e,t,o,r),t.child;case 11:return o=t.type,n=t.pendingProps,n=t.elementType===o?n:vt(o,n),Kf(e,t,o,n,r);case 7:return De(e,t,t.pendingProps,r),t.child;case 8:return De(e,t,t.pendingProps.children,r),t.child;case 12:return De(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(o=t.type._context,n=t.pendingProps,i=t.memoizedProps,l=n.value,J(Li,o._currentValue),o._currentValue=l,i!==null)if(pt(i.value,l)){if(i.children===n.children&&!je.current){t=Ut(e,t,r);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===o){if(i.tag===1){a=qt(-1,r&-r),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=r,a=i.alternate,a!==null&&(a.lanes|=r),pa(i.return,r,t),s.lanes|=r;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(P(341));l.lanes|=r,s=l.alternate,s!==null&&(s.lanes|=r),pa(l,r,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}De(e,t,n.children,r),t=t.child}return t;case 9:return n=t.type,o=t.pendingProps.children,Co(t,r),n=lt(n),o=o(n),t.flags|=1,De(e,t,o,r),t.child;case 14:return o=t.type,n=vt(o,t.pendingProps),n=vt(o.type,n),Xf(e,t,o,n,r);case 15:return Yf(e,t,t.type,t.pendingProps,r);case 17:return o=t.type,n=t.pendingProps,n=t.elementType===o?n:vt(o,n),Yi(e,t),t.tag=1,He(o)?(e=!0,Ti(t)):e=!1,Co(t,r),jf(t,o,n),Pa(t,o,n,r),Ra(null,t,o,!0,e,r);case 19:return og(e,t,r);case 22:return Qf(e,t,r)}throw Error(P(156,t.tag))};function Ng(e,t){return id(e,t)}function u1(e,t,r,o){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ut(e,t,r,o){return new u1(e,t,r,o)}function eu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function c1(e){if(typeof e=="function")return eu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ls)return 11;if(e===us)return 14}return 2}function Br(e,t){var r=e.alternate;return r===null?(r=ut(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ll(e,t,r,o,n,i){var l=2;if(o=e,typeof e=="function")eu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case uo:return qr(r.children,n,i,t);case ns:l=8,n|=8;break;case is:return e=ut(12,r,t,n|2),e.elementType=is,e.lanes=i,e;case ss:return e=ut(13,r,t,n),e.elementType=ss,e.lanes=i,e;case as:return e=ut(19,r,t,n),e.elementType=as,e.lanes=i,e;case zc:return sl(r,n,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Oc:l=10;break e;case Rc:l=9;break e;case ls:l=11;break e;case us:l=14;break e;case or:l=16,o=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=ut(l,r,t,n),t.elementType=e,t.type=o,t.lanes=i,t}function qr(e,t,r,o){return e=ut(7,e,o,t),e.lanes=r,e}function sl(e,t,r,o){return e=ut(22,e,o,t),e.elementType=zc,e.lanes=r,e.stateNode={isHidden:!1},e}function tu(e,t,r){return e=ut(6,e,null,t),e.lanes=r,e}function ru(e,t,r){return t=ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function d1(e,t,r,o,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Cs(0),this.expirationTimes=Cs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cs(0),this.identifierPrefix=o,this.onRecoverableError=n,this.mutableSourceEagerHydrationData=null}function ou(e,t,r,o,n,i,l,s,a){return e=new d1(e,t,r,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ut(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:o,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},va(i),e}function f1(e,t,r){var o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ao,key:o==null?null:""+o,children:e,containerInfo:t,implementation:r}}function Fg(e){if(!e)return gr;e=e._reactInternals;e:{if(Dr(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(He(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var r=e.type;if(He(r))return tf(e,r,t)}return t}function Cg(e,t,r,o,n,i,l,s,a){return e=ou(r,o,!0,e,n,i,l,s,a),e.context=Fg(null),r=e.current,o=Oe(),n=br(r),i=qt(o,n),i.callback=t!=null?t:null,vr(r,i,n),e.current.lanes=n,on(e,n,o),qe(e,o),e}function al(e,t,r,o){var n=t.current,i=Oe(),l=br(n);return r=Fg(r),t.context===null?t.context=r:t.pendingContext=r,t=qt(i,l),t.payload={element:e},o=o===void 0?null:o,o!==null&&(t.callback=o),e=vr(n,t,l),e!==null&&(kt(e,n,l,i),Mi(e,n,l)),l}function ul(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Pg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function nu(e,t){Pg(e,t),(e=e.alternate)&&Pg(e,t)}function g1(){return null}var Tg=typeof reportError=="function"?reportError:function(e){console.error(e)};function iu(e){this._internalRoot=e}cl.prototype.render=iu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));al(e,t,null,null)},cl.prototype.unmount=iu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Hr(function(){al(null,e,null,null)}),t[Mt]=null}};function cl(e){this._internalRoot=e}cl.prototype.unstable_scheduleHydration=function(e){if(e){var t=gd();e={blockedOn:null,target:e,priority:t};for(var r=0;r<ar.length&&t!==0&&t<ar[r].priority;r++);ar.splice(r,0,e),r===0&&vd(e)}};function lu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function dl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Dg(){}function p1(e,t,r,o,n){if(n){if(typeof o=="function"){var i=o;o=function(){var u=ul(l);i.call(u)}}var l=Cg(t,o,e,0,null,!1,!1,"",Dg);return e._reactRootContainer=l,e[Mt]=l.current,yn(e.nodeType===8?e.parentNode:e),Hr(),l}for(;n=e.lastChild;)e.removeChild(n);if(typeof o=="function"){var s=o;o=function(){var u=ul(a);s.call(u)}}var a=ou(e,0,!1,null,null,!1,!1,"",Dg);return e._reactRootContainer=a,e[Mt]=a.current,yn(e.nodeType===8?e.parentNode:e),Hr(function(){al(t,a,r,o)}),a}function fl(e,t,r,o,n){var i=r._reactRootContainer;if(i){var l=i;if(typeof n=="function"){var s=n;n=function(){var a=ul(l);s.call(a)}}al(t,l,e,n)}else l=p1(r,t,e,n,o);return ul(l)}dd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=rn(t.pendingLanes);r!==0&&(Ps(t,r|1),qe(t,de()),(q&6)===0&&(Ro=de()+500,pr()))}break;case 13:Hr(function(){var o=$t(e,1);if(o!==null){var n=Oe();kt(o,e,1,n)}}),nu(e,1)}},Ts=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var r=Oe();kt(t,e,134217728,r)}nu(e,134217728)}},fd=function(e){if(e.tag===13){var t=br(e),r=$t(e,t);if(r!==null){var o=Oe();kt(r,e,t,o)}nu(e,t)}},gd=function(){return Y},pd=function(e,t){var r=Y;try{return Y=e,t()}finally{Y=r}},_s=function(e,t,r){switch(t){case"input":if(hs(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var o=r[t];if(o!==e&&o.form===e.form){var n=Ci(o);if(!n)throw Error(P(90));Ic(o),hs(o,n)}}}break;case"textarea":$c(e,r);break;case"select":t=r.value,t!=null&&co(e,!!r.multiple,t,!1)}},Zc=Ja,Gc=Hr;var h1={usingClientEntryPoint:!1,Events:[wn,wo,Ci,Qc,Jc,Ja]},zn={findFiberByHostInstance:Or,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},v1={bundleType:zn.bundleType,version:zn.version,rendererPackageName:zn.rendererPackageName,rendererConfig:zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=od(e),e===null?null:e.stateNode},findFiberByHostInstance:zn.findFiberByHostInstance||g1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gl.isDisabled&&gl.supportsFiber)try{ci=gl.inject(v1),Et=gl}catch{}}Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=h1,Xe.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!lu(t))throw Error(P(200));return f1(e,t,null,r)},Xe.createRoot=function(e,t){if(!lu(e))throw Error(P(299));var r=!1,o="",n=Tg;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onRecoverableError!==void 0&&(n=t.onRecoverableError)),t=ou(e,1,!1,null,null,r,!1,o,n),e[Mt]=t.current,yn(e.nodeType===8?e.parentNode:e),new iu(t)},Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=od(t),e=e===null?null:e.stateNode,e},Xe.flushSync=function(e){return Hr(e)},Xe.hydrate=function(e,t,r){if(!dl(t))throw Error(P(200));return fl(null,e,t,!0,r)},Xe.hydrateRoot=function(e,t,r){if(!lu(e))throw Error(P(405));var o=r!=null&&r.hydratedSources||null,n=!1,i="",l=Tg;if(r!=null&&(r.unstable_strictMode===!0&&(n=!0),r.identifierPrefix!==void 0&&(i=r.identifierPrefix),r.onRecoverableError!==void 0&&(l=r.onRecoverableError)),t=Cg(t,null,e,1,r!=null?r:null,n,!1,i,l),e[Mt]=t.current,yn(e),o)for(e=0;e<o.length;e++)r=o[e],n=r._getVersion,n=n(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,n]:t.mutableSourceEagerHydrationData.push(r,n);return new cl(t)},Xe.render=function(e,t,r){if(!dl(t))throw Error(P(200));return fl(null,e,t,!1,r)},Xe.unmountComponentAtNode=function(e){if(!dl(e))throw Error(P(40));return e._reactRootContainer?(Hr(function(){fl(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1},Xe.unstable_batchedUpdates=Ja,Xe.unstable_renderSubtreeIntoContainer=function(e,t,r,o){if(!dl(r))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return fl(e,t,r,!1,o)},Xe.version="18.3.1-next-f1338f8080-20240426",function(e){function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}t(),e.exports=Xe}(ri);const pl=Xl(ri.exports);var su,Og=ri.exports;su=Og.createRoot,Og.hydrateRoot;function m1(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Q={exports:{}};(function(e,t){(function(r){{var o=r(m1,t);o!==void 0&&(e.exports=o)}})(function(r,o){Object.defineProperty(o,"__esModule",{value:!0}),o.ANSIOutput=o.ANSIColor=o.ANSIFont=o.ANSIStyle=void 0;let n=0;const i=()=>`${++n}`.padStart(16,"0");var l;(function(c){c.Bold="ansiBold",c.Dim="ansiDim",c.Italic="ansiItalic",c.Underlined="ansiUnderlined",c.SlowBlink="ansiSlowBlink",c.RapidBlink="ansiRapidBlink",c.Hidden="ansiHidden",c.CrossedOut="ansiCrossedOut",c.Fraktur="ansiFraktur",c.DoubleUnderlined="ansiDoubleUnderlined",c.Framed="ansiFramed",c.Encircled="ansiEncircled",c.Overlined="ansiOverlined",c.Superscript="ansiSuperscript",c.Subscript="ansiSubscript"})(l||(o.ANSIStyle=l={}));var s;(function(c){c.AlternativeFont1="ansiAlternativeFont1",c.AlternativeFont2="ansiAlternativeFont2",c.AlternativeFont3="ansiAlternativeFont3",c.AlternativeFont4="ansiAlternativeFont4",c.AlternativeFont5="ansiAlternativeFont5",c.AlternativeFont6="ansiAlternativeFont6",c.AlternativeFont7="ansiAlternativeFont7",c.AlternativeFont8="ansiAlternativeFont8",c.AlternativeFont9="ansiAlternativeFont9"})(s||(o.ANSIFont=s={}));var a;(function(c){c.Black="ansiBlack",c.Red="ansiRed",c.Green="ansiGreen",c.Yellow="ansiYellow",c.Blue="ansiBlue",c.Magenta="ansiMagenta",c.Cyan="ansiCyan",c.White="ansiWhite",c.BrightBlack="ansiBrightBlack",c.BrightRed="ansiBrightRed",c.BrightGreen="ansiBrightGreen",c.BrightYellow="ansiBrightYellow",c.BrightBlue="ansiBrightBlue",c.BrightMagenta="ansiBrightMagenta",c.BrightCyan="ansiBrightCyan",c.BrightWhite="ansiBrightWhite"})(a||(o.ANSIColor=a={}));class u{constructor(){fe(this,"_parserState",y.BufferingOutput);fe(this,"_controlSequence","");fe(this,"_sgrState");fe(this,"_outputLines",[]);fe(this,"_outputLine",0);fe(this,"_outputColumn",0);fe(this,"_buffer","");fe(this,"_pendingNewline",!1)}get outputLines(){return this.flushBuffer(),this._outputLines}static processOutput(p){const b=new u;return b.processOutput(p),b.outputLines}processOutput(p){for(let b=0;b<p.length;b++){this._pendingNewline&&(this.flushBuffer(),this._outputLine++,this._outputColumn=0,this._pendingNewline=!1);const w=p.charAt(b);this._parserState===y.BufferingOutput?w==="\x1B"?(this.flushBuffer(),this._parserState=y.ControlSequenceStarted):w==="\x9B"?(this.flushBuffer(),this._parserState=y.ParsingControlSequence):this.processCharacter(w):this._parserState===y.ControlSequenceStarted?w==="["?this._parserState=y.ParsingControlSequence:(this._parserState=y.BufferingOutput,this.processCharacter(w)):this._parserState===y.ParsingControlSequence&&(this._controlSequence+=w,w.match(/^[A-Za-z]$/)&&this.processControlSequence())}this.flushBuffer()}flushBuffer(){for(let p=this._outputLines.length;p<this._outputLine+1;p++)this._outputLines.push(new k);this._buffer&&(this._outputLines[this._outputLine].insert(this._buffer,this._outputColumn,this._sgrState),this._outputColumn+=this._buffer.length,this._buffer="")}processCharacter(p){switch(p){case`
`:this._pendingNewline=!0;break;case"\r":this.flushBuffer(),this._outputColumn=0;break;default:this._buffer+=p;break}}processControlSequence(){switch(this._controlSequence.charAt(this._controlSequence.length-1)){case"A":this.processCUU();break;case"B":this.processCUD();break;case"C":this.processCUF();break;case"D":this.processCUB();break;case"H":this.processCUP();break;case"J":this.processED();break;case"K":this.processEL();break;case"m":this.processSGR();break}this._controlSequence="",this._parserState=y.BufferingOutput}processCUU(){const p=this._controlSequence.match(/^([0-9]*)A$/);!p||(this._outputLine=Math.max(this._outputLine-m(p[1],1,1,1024),0))}processCUD(){const p=this._controlSequence.match(/^([0-9]*)B$/);!p||(this._outputLine=Math.max(this._outputLine-m(p[1],1,1,1024),0))}processCUF(){const p=this._controlSequence.match(/^([0-9]*)C$/);!p||(this._outputColumn+=m(p[1],1,1,1024))}processCUB(){const p=this._controlSequence.match(/^([0-9]*)D$/);!p||(this._outputColumn=Math.max(this._outputColumn-m(p[1],1,1,1024),0))}processCUP(){const p=this._controlSequence.match(/^([0-9]*)(?:;?([0-9]*))H$/);!p||(this._outputLine=this.rangeParam(p[1],1,1,1024)-1,this._outputColumn=this.rangeParam(p[2],1,1,1024)-1)}processED(){const p=this._controlSequence.match(/^([0-9]*)J$/);if(!!p)switch(this.getParam(p[1],0)){case 0:this._outputLines[this._outputLine].clearToEndOfLine(this._outputColumn);for(let b=this._outputLine+1;b<this._outputLines.length;b++)this._outputLines[b].clearEntireLine();break;case 1:this._outputLines[this._outputLine].clearToBeginningOfLine(this._outputColumn);for(let b=0;b<this._outputLine;b++)this._outputLines[b].clearEntireLine();break;case 2:for(let b=0;b<this._outputLines.length;b++)this._outputLines[b].clearEntireLine();break}}processEL(){const p=this._controlSequence.match(/^([0-9]*)K$/);if(!p)return;const b=this._outputLines[this._outputLine];switch(this.getParam(p[1],0)){case 0:b.clearToEndOfLine(this._outputColumn);break;case 1:b.clearToBeginningOfLine(this._outputColumn);break;case 2:b.clearEntireLine();break}}processSGR(){const p=this._sgrState?this._sgrState.copy():new v,b=this._controlSequence.slice(0,-1).split(";").map(w=>w===""?d.Reset:parseInt(w,10));for(let w=0;w<b.length;w++){const B=b[w],E=()=>{if(w+1!==b.length)switch(b[++w]){case f.Color256:{if(w+1===b.length)return;const O=b[++w];switch(O){case g.Black:return a.Black;case g.Red:return a.Red;case g.Green:return a.Green;case g.Yellow:return a.Yellow;case g.Blue:return a.Blue;case g.Magenta:return a.Magenta;case g.Cyan:return a.Cyan;case g.White:return a.White;case g.BrightBlack:return a.BrightBlack;case g.BrightRed:return a.BrightRed;case g.BrightGreen:return a.BrightGreen;case g.BrightYellow:return a.BrightYellow;case g.BrightBlue:return a.BrightBlue;case g.BrightMagenta:return a.BrightMagenta;case g.BrightCyan:return a.Cyan;case g.BrightWhite:return a.BrightWhite;default:if(O%1!==0)return;if(O>=16&&O<=231){let F=O-16,A=F%6;F=(F-A)/6;let I=F%6;F=(F-I)/6;let X=F;return A=Math.round(A*255/5),I=Math.round(I*255/5),X=Math.round(X*255/5),"#"+h(X)+h(I)+h(A)}else if(O>=232&&O<=255){const F=Math.round((O-232)/23*255),A=h(F);return"#"+A+A+A}else return}}case f.ColorRGB:{const O=[0,0,0];for(let F=0;F<3&&w+1<b.length;F++)O[F]=b[++w];return"#"+h(O[0])+h(O[1])+h(O[2])}}};switch(B){case d.Reset:p.reset();break;case d.Bold:p.setStyle(l.Bold);break;case d.Dim:p.setStyle(l.Dim);break;case d.Italic:p.setStyle(l.Italic);break;case d.Underlined:p.setStyle(l.Underlined,l.DoubleUnderlined);break;case d.SlowBlink:p.setStyle(l.SlowBlink,l.RapidBlink);break;case d.RapidBlink:p.setStyle(l.RapidBlink,l.SlowBlink);break;case d.Reversed:p.setReversed(!0);break;case d.Hidden:p.setStyle(l.Hidden);break;case d.CrossedOut:p.setStyle(l.CrossedOut);break;case d.PrimaryFont:p.setFont();break;case d.AlternativeFont1:p.setFont(s.AlternativeFont1);break;case d.AlternativeFont2:p.setFont(s.AlternativeFont2);break;case d.AlternativeFont3:p.setFont(s.AlternativeFont3);break;case d.AlternativeFont4:p.setFont(s.AlternativeFont4);break;case d.AlternativeFont5:p.setFont(s.AlternativeFont5);break;case d.AlternativeFont6:p.setFont(s.AlternativeFont6);break;case d.AlternativeFont7:p.setFont(s.AlternativeFont7);break;case d.AlternativeFont8:p.setFont(s.AlternativeFont8);break;case d.AlternativeFont9:p.setFont(s.AlternativeFont9);break;case d.Fraktur:p.setStyle(l.Fraktur);break;case d.DoubleUnderlined:p.setStyle(l.DoubleUnderlined,l.Underlined);break;case d.NormalIntensity:p.deleteStyles(l.Bold,l.Dim);break;case d.NotItalicNotFraktur:p.deleteStyles(l.Italic,l.Fraktur);break;case d.NotUnderlined:p.deleteStyles(l.Underlined,l.DoubleUnderlined);break;case d.NotBlinking:p.deleteStyles(l.SlowBlink,l.RapidBlink);break;case d.ProportionalSpacing:break;case d.NotReversed:p.setReversed(!1);break;case d.Reveal:p.deleteStyles(l.Hidden);break;case d.NotCrossedOut:p.deleteStyles(l.CrossedOut);break;case d.ForegroundBlack:p.setForegroundColor(a.Black);break;case d.ForegroundRed:p.setForegroundColor(a.Red);break;case d.ForegroundGreen:p.setForegroundColor(a.Green);break;case d.ForegroundYellow:p.setForegroundColor(a.Yellow);break;case d.ForegroundBlue:p.setForegroundColor(a.Blue);break;case d.ForegroundMagenta:p.setForegroundColor(a.Magenta);break;case d.ForegroundCyan:p.setForegroundColor(a.Cyan);break;case d.ForegroundWhite:p.setForegroundColor(a.White);break;case d.SetForeground:{const O=E();O&&p.setForegroundColor(O);break}case d.DefaultForeground:p.setForegroundColor();break;case d.BackgroundBlack:p.setBackgroundColor(a.Black);break;case d.BackgroundRed:p.setBackgroundColor(a.Red);break;case d.BackgroundGreen:p.setBackgroundColor(a.Green);break;case d.BackgroundYellow:p.setBackgroundColor(a.Yellow);break;case d.BackgroundBlue:p.setBackgroundColor(a.Blue);break;case d.BackgroundMagenta:p.setBackgroundColor(a.Magenta);break;case d.BackgroundCyan:p.setBackgroundColor(a.Cyan);break;case d.BackgroundWhite:p.setBackgroundColor(a.White);break;case d.SetBackground:{const O=E();O&&p.setBackgroundColor(O);break}case d.DefaultBackground:p.setBackgroundColor();break;case d.ForegroundBrightBlack:p.setForegroundColor(a.BrightBlack);break;case d.ForegroundBrightRed:p.setForegroundColor(a.BrightRed);break;case d.ForegroundBrightGreen:p.setForegroundColor(a.BrightGreen);break;case d.ForegroundBrightYellow:p.setForegroundColor(a.BrightYellow);break;case d.ForegroundBrightBlue:p.setForegroundColor(a.BrightBlue);break;case d.ForegroundBrightMagenta:p.setForegroundColor(a.BrightMagenta);break;case d.ForegroundBrightCyan:p.setForegroundColor(a.BrightCyan);break;case d.ForegroundBrightWhite:p.setForegroundColor(a.BrightWhite);break;case d.BackgroundBrightBlack:p.setBackgroundColor(a.BrightBlack);break;case d.BackgroundBrightRed:p.setBackgroundColor(a.BrightRed);break;case d.BackgroundBrightGreen:p.setBackgroundColor(a.BrightGreen);break;case d.BackgroundBrightYellow:p.setBackgroundColor(a.BrightYellow);break;case d.BackgroundBrightBlue:p.setBackgroundColor(a.BrightBlue);break;case d.BackgroundBrightMagenta:p.setBackgroundColor(a.BrightMagenta);break;case d.BackgroundBrightCyan:p.setBackgroundColor(a.BrightCyan);break;case d.BackgroundBrightWhite:p.setBackgroundColor(a.BrightWhite);break}}v.equivalent(p,this._sgrState)||(this._sgrState=p)}rangeParam(p,b,w,B){const E=this.getParam(p,b);return Math.min(Math.max(E,w),B)}getParam(p,b){const w=parseInt(p);return Number.isNaN(w)?b:w}}o.ANSIOutput=u;var d;(function(c){c[c.Reset=0]="Reset",c[c.Bold=1]="Bold",c[c.Dim=2]="Dim",c[c.Italic=3]="Italic",c[c.Underlined=4]="Underlined",c[c.SlowBlink=5]="SlowBlink",c[c.RapidBlink=6]="RapidBlink",c[c.Reversed=7]="Reversed",c[c.Hidden=8]="Hidden",c[c.CrossedOut=9]="CrossedOut",c[c.PrimaryFont=10]="PrimaryFont",c[c.AlternativeFont1=11]="AlternativeFont1",c[c.AlternativeFont2=12]="AlternativeFont2",c[c.AlternativeFont3=13]="AlternativeFont3",c[c.AlternativeFont4=14]="AlternativeFont4",c[c.AlternativeFont5=15]="AlternativeFont5",c[c.AlternativeFont6=16]="AlternativeFont6",c[c.AlternativeFont7=17]="AlternativeFont7",c[c.AlternativeFont8=18]="AlternativeFont8",c[c.AlternativeFont9=19]="AlternativeFont9",c[c.Fraktur=20]="Fraktur",c[c.DoubleUnderlined=21]="DoubleUnderlined",c[c.NormalIntensity=22]="NormalIntensity",c[c.NotItalicNotFraktur=23]="NotItalicNotFraktur",c[c.NotUnderlined=24]="NotUnderlined",c[c.NotBlinking=25]="NotBlinking",c[c.ProportionalSpacing=26]="ProportionalSpacing",c[c.NotReversed=27]="NotReversed",c[c.Reveal=28]="Reveal",c[c.NotCrossedOut=29]="NotCrossedOut",c[c.ForegroundBlack=30]="ForegroundBlack",c[c.ForegroundRed=31]="ForegroundRed",c[c.ForegroundGreen=32]="ForegroundGreen",c[c.ForegroundYellow=33]="ForegroundYellow",c[c.ForegroundBlue=34]="ForegroundBlue",c[c.ForegroundMagenta=35]="ForegroundMagenta",c[c.ForegroundCyan=36]="ForegroundCyan",c[c.ForegroundWhite=37]="ForegroundWhite",c[c.SetForeground=38]="SetForeground",c[c.DefaultForeground=39]="DefaultForeground",c[c.BackgroundBlack=40]="BackgroundBlack",c[c.BackgroundRed=41]="BackgroundRed",c[c.BackgroundGreen=42]="BackgroundGreen",c[c.BackgroundYellow=43]="BackgroundYellow",c[c.BackgroundBlue=44]="BackgroundBlue",c[c.BackgroundMagenta=45]="BackgroundMagenta",c[c.BackgroundCyan=46]="BackgroundCyan",c[c.BackgroundWhite=47]="BackgroundWhite",c[c.SetBackground=48]="SetBackground",c[c.DefaultBackground=49]="DefaultBackground",c[c.DisableProportionalSpacing=50]="DisableProportionalSpacing",c[c.Framed=51]="Framed",c[c.Encircled=52]="Encircled",c[c.Overlined=53]="Overlined",c[c.NotFramedNotEncircled=54]="NotFramedNotEncircled",c[c.NotOverlined=55]="NotOverlined",c[c.SetUnderline=58]="SetUnderline",c[c.DefaultUnderline=59]="DefaultUnderline",c[c.IdeogramUnderlineOrRightSideLine=60]="IdeogramUnderlineOrRightSideLine",c[c.IdeogramDoubleUnderlineOrDoubleRightSideLine=61]="IdeogramDoubleUnderlineOrDoubleRightSideLine",c[c.IdeogramOverlineOrLeftSideLine=62]="IdeogramOverlineOrLeftSideLine",c[c.IdeogramDoubleOverlineOrDoubleLeftSideLine=63]="IdeogramDoubleOverlineOrDoubleLeftSideLine",c[c.IdeogramStressMarking=64]="IdeogramStressMarking",c[c.NoIdeogramAttributes=65]="NoIdeogramAttributes",c[c.Superscript=73]="Superscript",c[c.Subscript=74]="Subscript",c[c.NotSuperscriptNotSubscript=75]="NotSuperscriptNotSubscript",c[c.ForegroundBrightBlack=90]="ForegroundBrightBlack",c[c.ForegroundBrightRed=91]="ForegroundBrightRed",c[c.ForegroundBrightGreen=92]="ForegroundBrightGreen",c[c.ForegroundBrightYellow=93]="ForegroundBrightYellow",c[c.ForegroundBrightBlue=94]="ForegroundBrightBlue",c[c.ForegroundBrightMagenta=95]="ForegroundBrightMagenta",c[c.ForegroundBrightCyan=96]="ForegroundBrightCyan",c[c.ForegroundBrightWhite=97]="ForegroundBrightWhite",c[c.BackgroundBrightBlack=100]="BackgroundBrightBlack",c[c.BackgroundBrightRed=101]="BackgroundBrightRed",c[c.BackgroundBrightGreen=102]="BackgroundBrightGreen",c[c.BackgroundBrightYellow=103]="BackgroundBrightYellow",c[c.BackgroundBrightBlue=104]="BackgroundBrightBlue",c[c.BackgroundBrightMagenta=105]="BackgroundBrightMagenta",c[c.BackgroundBrightCyan=106]="BackgroundBrightCyan",c[c.BackgroundBrightWhite=107]="BackgroundBrightWhite"})(d||(d={}));var f;(function(c){c[c.Color256=5]="Color256",c[c.ColorRGB=2]="ColorRGB"})(f||(f={}));var g;(function(c){c[c.Black=0]="Black",c[c.Red=1]="Red",c[c.Green=2]="Green",c[c.Yellow=3]="Yellow",c[c.Blue=4]="Blue",c[c.Magenta=5]="Magenta",c[c.Cyan=6]="Cyan",c[c.White=7]="White",c[c.BrightBlack=8]="BrightBlack",c[c.BrightRed=9]="BrightRed",c[c.BrightGreen=10]="BrightGreen",c[c.BrightYellow=11]="BrightYellow",c[c.BrightBlue=12]="BrightBlue",c[c.BrightMagenta=13]="BrightMagenta",c[c.BrightCyan=14]="BrightCyan",c[c.BrightWhite=15]="BrightWhite"})(g||(g={}));var y;(function(c){c[c.BufferingOutput=0]="BufferingOutput",c[c.ControlSequenceStarted=1]="ControlSequenceStarted",c[c.ParsingControlSequence=2]="ParsingControlSequence"})(y||(y={}));class v{constructor(){fe(this,"_styles");fe(this,"_foregroundColor");fe(this,"_backgroundColor");fe(this,"_underlinedColor");fe(this,"_reversed");fe(this,"_font")}reset(){this._styles=void 0,this._foregroundColor=void 0,this._backgroundColor=void 0,this._underlinedColor=void 0,this._reversed=void 0,this._font=void 0}copy(){const p=new v;if(this._styles&&this._styles.size){const b=new Set;this._styles.forEach(w=>b.add(w)),p._styles=b}return p._foregroundColor=this._foregroundColor,p._backgroundColor=this._backgroundColor,p._underlinedColor=this._underlinedColor,p._reversed=this._reversed,p._font=this._font,p}setStyle(p,...b){if(this._styles)for(const w of b)this._styles.delete(w);else this._styles=new Set;this._styles.add(p)}deleteStyles(...p){if(this._styles){for(const b of p)this._styles.delete(b);this._styles.size||(this._styles=void 0)}}setForegroundColor(p){this._reversed?this._backgroundColor=p:this._foregroundColor=p}setBackgroundColor(p){this._reversed?this._foregroundColor=p:this._backgroundColor=p}setReversed(p){p?this._reversed||(this._reversed=!0,this.reverseForegroundAndBackgroundColors()):this._reversed&&(this._reversed=void 0,this.reverseForegroundAndBackgroundColors())}setFont(p){this._font=p}static equivalent(p,b){const w=(B,E)=>E instanceof Set?E.size?[...E]:void 0:E;return p===b||JSON.stringify(p,w)===JSON.stringify(b,w)}get styles(){return this._styles?[...this._styles]:[]}get foregroundColor(){if(this._backgroundColor&&!this._foregroundColor)switch(this._backgroundColor){case a.Black:case a.BrightBlack:case a.Red:case a.BrightRed:return a.White;case a.Green:case a.BrightGreen:case a.Yellow:case a.BrightYellow:case a.Blue:case a.BrightBlue:case a.Magenta:case a.BrightMagenta:case a.Cyan:case a.BrightCyan:case a.White:case a.BrightWhite:return a.Black}return this._foregroundColor}get backgroundColor(){return this._backgroundColor}get underlinedColor(){return this._underlinedColor}get font(){return this._font}reverseForegroundAndBackgroundColors(){const p=this._foregroundColor;this._foregroundColor=this._backgroundColor,this._backgroundColor=p}}class k{constructor(){fe(this,"_id",i());fe(this,"_outputRuns",[]);fe(this,"_totalLength",0)}clearEntireLine(){this._totalLength&&(this._outputRuns=[new _(" ".repeat(this._totalLength))])}clearToEndOfLine(p){if(p=Math.max(p,0),p>=this._totalLength)return;if(p===0){this.clearEntireLine();return}let b=0,w,B;for(let A=0;A<this._outputRuns.length;A++){const I=this._outputRuns[A];if(p<b+I.text.length){w=I,B=A;break}b+=I.text.length}if(w===void 0||B===void 0)return;const E=p-b,O=" ".repeat(this._totalLength-p),F=[];if(!E)F.push(new _(O));else{const A=w.text.slice(0,E);F.push(new _(A,w.sgrState)),F.push(new _(O))}this.outputRuns.splice(B,this._outputRuns.length-B,...F)}clearToBeginningOfLine(p){if(p=Math.max(p,0),p===0)return;if(p>=this._totalLength){this.clearEntireLine();return}let b=0,w,B;for(let A=this._outputRuns.length-1;A>=0;A--){const I=this._outputRuns[A];if(p>=b-I.text.length){w=I,B=A;break}b-=I.text.length}if(w===void 0||B===void 0)return;const E=b-p,O=" ".repeat(p),F=[new _(O)];if(E){const A=w.text.slice(-E);F.push(new _(A,w.sgrState))}this.outputRuns.splice(0,this._outputRuns.length-B,...F)}insert(p,b,w){if(!p.length)return;if(b===this._totalLength){if(this._totalLength+=p.length,this._outputRuns.length){const L=this._outputRuns[this._outputRuns.length-1];if(v.equivalent(L.sgrState,w)){L.appendText(p);return}}this._outputRuns.push(new _(p,w));return}if(b>this._totalLength){const L=" ".repeat(b-this._totalLength);if(this._totalLength+=L.length+p.length,!w&&this._outputRuns.length){const H=this._outputRuns[this._outputRuns.length-1];if(!H.sgrState){H.appendText(L),H.appendText(p);return}}this._outputRuns.push(new _(L)),this._outputRuns.push(new _(p,w));return}let B=0,E;for(let L=0;L<this._outputRuns.length;L++){const H=this._outputRuns[L];if(b<B+H.text.length){E=L;break}B+=H.text.length}if(E===void 0){this._outputRuns.push(new _(p,w));return}if(b+p.length>=this._totalLength){const L=b-B,H=[];if(!L)H.push(new _(p,w));else{const re=this._outputRuns[E],ce=re.text.slice(0,L);v.equivalent(re.sgrState,w)?H.push(new _(ce+p,w)):(H.push(new _(ce,re.sgrState)),H.push(new _(p,w)))}this.outputRuns.splice(E,1,...H),this._totalLength=B+L+p.length;return}let O=this._totalLength,F;for(let L=this._outputRuns.length-1;L>=0;L--){const H=this._outputRuns[L];if(b+p.length>O-H.text.length){F=L;break}O-=H.text.length}if(F===void 0){this._outputRuns.push(new _(p,w));return}const A=[],I=b-B;if(I){const L=this._outputRuns[E],H=L.text.slice(0,I);A.push(new _(H,L.sgrState))}A.push(new _(p,w));const X=O-(b+p.length);if(X){const L=this._outputRuns[F],H=L.text.slice(-X);A.push(new _(H,L.sgrState))}this._outputRuns.splice(E,F-E+1,...A),this._outputRuns=_.optimizeOutputRuns(this._outputRuns),this._totalLength=this._outputRuns.reduce((L,H)=>L+H.text.length,0)}get id(){return this._id}get outputRuns(){return this._outputRuns}}class _{constructor(p,b){fe(this,"_id",i());fe(this,"_sgrState");fe(this,"_text");this._sgrState=b,this._text=p}get sgrState(){return this._sgrState}static optimizeOutputRuns(p){if(p.length<2)return p;{const b=[p[0]];for(let w=1,B=0;w<p.length;w++){const E=p[w];v.equivalent(b[B].sgrState,E.sgrState)?b[B]._text+=E.text:b[++B]=E}return b}}appendText(p){this._text+=p}insert(p,b){this._id=i();const w=this.text.slice(0,b),B=this.text.slice(b+p.length);this._text=w+p+B}get id(){return this._id}get format(){return this._sgrState}get text(){return this._text}}const m=(c,p,b,w)=>{const B=parseInt(c);return Number.isNaN(B)?p:Math.min(Math.max(B,b),w)},h=c=>{if(c<0)return"00";if(c>255)return"ff";const p=c.toString(16);return p.length===2?p:"0"+p}})})(Q,Q.exports);function y1(e){return Array.isArray(e)?e:[e]}const au=typeof window>"u"?global:window,uu="@griffel/";function k1(e,t){return au[Symbol.for(uu+e)]||(au[Symbol.for(uu+e)]=t),au[Symbol.for(uu+e)]}const An=k1("DEFINITION_LOOKUP_TABLE",{}),hl="data-make-styles-bucket",cu="f",du=7,Ao="___",Rg=Ao.length+du,b1=0,w1=1,B1={all:1,animation:1,animationRange:1,background:1,backgroundPosition:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockStart:1,borderBottom:1,borderColor:1,borderImage:1,borderInline:1,borderInlineEnd:1,borderInlineStart:1,borderLeft:1,borderRadius:1,borderRight:1,borderStyle:1,borderTop:1,borderWidth:1,caret:1,columns:1,columnRule:1,containIntrinsicSize:1,container:1,flex:1,flexFlow:1,font:1,gap:1,grid:1,gridArea:1,gridColumn:1,gridRow:1,gridTemplate:1,inset:1,insetBlock:1,insetInline:1,lineClamp:1,listStyle:1,margin:1,marginBlock:1,marginInline:1,mask:1,maskBorder:1,motion:1,offset:1,outline:1,overflow:1,overscrollBehavior:1,padding:1,paddingBlock:1,paddingInline:1,placeItems:1,placeContent:1,placeSelf:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginInline:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingInline:1,scrollSnapMargin:1,scrollTimeline:1,textDecoration:1,textEmphasis:1,transition:1,viewTimeline:1};function _1(e,t,r){const o=[];if(r[hl]=t,e)for(const i in r)e.setAttribute(i,r[i]);function n(i){return e!=null&&e.sheet?e.sheet.insertRule(i,e.sheet.cssRules.length):o.push(i)}return{elementAttributes:r,insertRule:n,element:e,bucketName:t,cssRules(){return e!=null&&e.sheet?Array.from(e.sheet.cssRules).map(i=>i.cssText):o}}}const zg=["r","d","l","v","w","f","i","h","a","s","k","t","m","c"].reduce((e,t,r)=>(e[t]=r,e),{});function x1(e,t,r,o,n={}){const i=e==="m",l=i?e+n.m:e;if(!o.stylesheets[l]){const s=t&&t.createElement("style"),a=_1(s,e,Object.assign({},o.styleElementAttributes,i&&{media:n.m}));o.stylesheets[l]=a,t&&s&&t.head.insertBefore(s,S1(t,r,e,o,n))}return o.stylesheets[l]}function S1(e,t,r,o,n){const i=zg[r];let l=d=>i-zg[d.getAttribute(hl)],s=e.head.querySelectorAll(`[${hl}]`);if(r==="m"&&n){const d=e.head.querySelectorAll(`[${hl}="${r}"]`);d.length&&(s=d,l=f=>o.compareMediaQueries(n.m,f.media))}const a=s.length;let u=a-1;for(;u>=0;){const d=s.item(u);if(l(d)>0)return d.nextSibling;u--}return a>0?s.item(0):t?t.nextSibling:null}function Ag(e,t){try{e.insertRule(t)}catch{}}const wS=(()=>{try{var e;return Boolean(typeof window<"u"&&((e=window.sessionStorage)==null?void 0:e.getItem("__GRIFFEL_DEVTOOLS__")))}catch{return!1}})();function Ln(e){for(var t=0,r,o=0,n=e.length;n>=4;++o,n-=4)r=e.charCodeAt(o)&255|(e.charCodeAt(++o)&255)<<8|(e.charCodeAt(++o)&255)<<16|(e.charCodeAt(++o)&255)<<24,r=(r&65535)*1540483477+((r>>>16)*59797<<16),r^=r>>>24,t=(r&65535)*1540483477+((r>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(n){case 3:t^=(e.charCodeAt(o+2)&255)<<16;case 2:t^=(e.charCodeAt(o+1)&255)<<8;case 1:t^=e.charCodeAt(o)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}function E1(e){const t=e.length;if(t===du)return e;for(let r=t;r<du;r++)e+="0";return e}function Lg(e,t,r=[]){return Ao+E1(Ln(e+t))}function Ig(e,t){let r="";for(const o in e){const n=e[o];if(n){const i=Array.isArray(n);t==="rtl"?r+=(i?n[1]:n)+" ":r+=(i?n[0]:n)+" "}}return r.slice(0,-1)}function vl(e,t){const r={};for(const o in e){const n=Ig(e[o],t);if(n===""){r[o]="";continue}const i=Lg(n,t),l=i+" "+n;An[i]=[e[o],t],r[o]=l}return r}const ml={};function _e(){let e=null,t="",r="";const o=new Array(arguments.length);for(let u=0;u<arguments.length;u++){const d=arguments[u];if(typeof d=="string"&&d!==""){const f=d.indexOf(Ao);if(f===-1)t+=d+" ";else{const g=d.substr(f,Rg);f>0&&(t+=d.slice(0,f)),r+=g,o[u]=g}}}if(r==="")return t.slice(0,-1);const n=ml[r];if(n!==void 0)return t+n;const i=[];for(let u=0;u<arguments.length;u++){const d=o[u];if(d){const f=An[d];f&&(i.push(f[b1]),e=f[w1])}}const l=Object.assign.apply(Object,[{}].concat(i));let s=Ig(l,e);const a=Lg(s,e,o);return s=a+" "+s,ml[r]=s,An[a]=[l,e],t+s}const Mg={},jg=new Set,fu={getChildrenSequences:e=>{const t=Object.keys(ml).find(r=>ml[r].startsWith(e));return t?t.split(Ao).filter(r=>r.length).map(r=>Ao+r):[]},addCSSRule:e=>{jg.add(e)},addSequenceDetails:(e,t)=>{Object.entries(e).forEach(([r,o])=>{Mg[o.substring(0,Rg)]={slotName:r,sourceURL:t}})},getCSSRules:()=>Array.from(jg),getSequenceDetails:e=>Mg[e]};function Hg(e,t){return Array.isArray(e)?t==="rtl"?e[1]:e[0]:e}function N1(e,t,r,o){const n=e[0],i=e[1];return Object.entries(n).map(([l,s])=>{const a=Hg(s,i);let u;if(r&&t){const d=r.find(({className:f})=>f===a);!d&&t[0][l]?u=Hg(t[0][l],t[1]):d&&t[0][l]?u=(o?o.filter(({debugClassNames:g})=>g.filter(({className:y})=>y===a).length>0).length>0:!1)?d.className:d.overriddenBy:(!d&&!t[0][l]||d&&!t[0][l])&&(u=void 0)}return{className:a,overriddenBy:u}})}function Wg(e,t){const r=An[e];if(r===void 0)return;const o=t?An[t.sequenceHash]:void 0,n=N1(r,o,t==null?void 0:t.debugClassNames,t==null?void 0:t.children),i={sequenceHash:e,direction:r[1],children:[],debugClassNames:n};return fu.getChildrenSequences(i.sequenceHash).reverse().forEach(s=>{const a=Wg(s,i);a&&i.children.push(a)}),i.children.length||(i.rules={},i.debugClassNames.forEach(({className:s})=>{const a=fu.getSequenceDetails(e);a&&(i.slot=a.slotName,i.sourceURL=a.sourceURL);const u=fu.getCSSRules().find(d=>d.includes(s));i.rules[s]=u})),i}function BS(e){const t=e.defaultView;if(!t||t.__GRIFFEL_DEVTOOLS__)return;const r={getInfo:o=>{const n=Array.from(o.classList).find(i=>i.startsWith(Ao));if(n!==void 0)return Wg(n)}};Object.defineProperty(t,"__GRIFFEL_DEVTOOLS__",{configurable:!1,enumerable:!1,get(){return r}})}let F1=0;const C1=(e,t)=>e<t?-1:e>t?1:0;function P1(e=typeof document>"u"?void 0:document,t={}){const{unstable_filterCSSRule:r,insertionPoint:o,styleElementAttributes:n,compareMediaQueries:i=C1}=t,l={insertionCache:{},stylesheets:{},styleElementAttributes:Object.freeze(n),compareMediaQueries:i,id:`d${F1++}`,insertCSSRules(s){for(const a in s){const u=s[a];for(let d=0,f=u.length;d<f;d++){const[g,y]=y1(u[d]),v=x1(a,e,o||null,l,y);l.insertionCache[g]||(l.insertionCache[g]=a,r?r(g)&&Ag(v,g):Ag(v,g))}}}};return l}const gu=()=>{const e={};return function(r,o){e[r.id]===void 0&&(r.insertCSSRules(o),e[r.id]=!0)}};function $g(e){return e.reduce(function(t,r){var o=r[0],n=r[1];return t[o]=n,t[n]=o,t},{})}function T1(e){return typeof e=="boolean"}function D1(e){return typeof e=="function"}function In(e){return typeof e=="number"}function O1(e){return e===null||typeof e>"u"}function R1(e){return e&&typeof e=="object"}function z1(e){return typeof e=="string"}function yl(e,t){return e.indexOf(t)!==-1}function A1(e){return parseFloat(e)===0?e:e[0]==="-"?e.slice(1):"-"+e}function kl(e,t,r,o){return t+A1(r)+o}function L1(e){var t=e.indexOf(".");if(t===-1)e=100-parseFloat(e)+"%";else{var r=e.length-t-2;e=100-parseFloat(e),e=e.toFixed(r)+"%"}return e}function qg(e){return e.replace(/ +/g," ").split(" ").map(function(t){return t.trim()}).filter(Boolean).reduce(function(t,r){var o=t.list,n=t.state,i=(r.match(/\(/g)||[]).length,l=(r.match(/\)/g)||[]).length;return n.parensDepth>0?o[o.length-1]=o[o.length-1]+" "+r:o.push(r),n.parensDepth+=i-l,{list:o,state:n}},{list:[],state:{parensDepth:0}}).list}function Ug(e){var t=qg(e);if(t.length<=3||t.length>4)return e;var r=t[0],o=t[1],n=t[2],i=t[3];return[r,i,n,o].join(" ")}function I1(e){return!T1(e)&&!O1(e)}function M1(e){for(var t=[],r=0,o=0,n=!1;o<e.length;)!n&&e[o]===","?(t.push(e.substring(r,o).trim()),o++,r=o):e[o]==="("?(n=!0,o++):(e[o]===")"&&(n=!1),o++);return r!=o&&t.push(e.substring(r,o+1)),t}var C={padding:function(t){var r=t.value;return In(r)?r:Ug(r)},textShadow:function(t){var r=t.value,o=M1(r).map(function(n){return n.replace(/(^|\s)(-*)([.|\d]+)/,function(i,l,s,a){if(a==="0")return i;var u=s===""?"-":"";return""+l+u+a})});return o.join(",")},borderColor:function(t){var r=t.value;return Ug(r)},borderRadius:function(t){var r=t.value;if(In(r))return r;if(yl(r,"/")){var o=r.split("/"),n=o[0],i=o[1],l=C.borderRadius({value:n.trim()}),s=C.borderRadius({value:i.trim()});return l+" / "+s}var a=qg(r);switch(a.length){case 2:return a.reverse().join(" ");case 4:{var u=a[0],d=a[1],f=a[2],g=a[3];return[d,u,g,f].join(" ")}default:return r}},background:function(t){var r=t.value,o=t.valuesToConvert,n=t.isRtl,i=t.bgImgDirectionRegex,l=t.bgPosDirectionRegex;if(In(r))return r;var s=r.replace(/(url\(.*?\))|(rgba?\(.*?\))|(hsl\(.*?\))|(#[a-fA-F0-9]+)|((^| )(\D)+( |$))/g,"").trim();return r=r.replace(s,C.backgroundPosition({value:s,valuesToConvert:o,isRtl:n,bgPosDirectionRegex:l})),C.backgroundImage({value:r,valuesToConvert:o,bgImgDirectionRegex:i})},backgroundImage:function(t){var r=t.value,o=t.valuesToConvert,n=t.bgImgDirectionRegex;return!yl(r,"url(")&&!yl(r,"linear-gradient(")?r:r.replace(n,function(i,l,s){return i.replace(s,o[s])})},backgroundPosition:function(t){var r=t.value,o=t.valuesToConvert,n=t.isRtl,i=t.bgPosDirectionRegex;return r.replace(n?/^((-|\d|\.)+%)/:null,function(l,s){return L1(s)}).replace(i,function(l){return o[l]})},backgroundPositionX:function(t){var r=t.value,o=t.valuesToConvert,n=t.isRtl,i=t.bgPosDirectionRegex;return In(r)?r:C.backgroundPosition({value:r,valuesToConvert:o,isRtl:n,bgPosDirectionRegex:i})},transition:function(t){var r=t.value,o=t.propertiesToConvert;return r.split(/,\s*/g).map(function(n){var i=n.split(" ");return i[0]=o[i[0]]||i[0],i.join(" ")}).join(", ")},transitionProperty:function(t){var r=t.value,o=t.propertiesToConvert;return r.split(/,\s*/g).map(function(n){return o[n]||n}).join(", ")},transform:function(t){var r=t.value,o="[^\\u0020-\\u007e]",n="(?:(?:(?:\\[0-9a-f]{1,6})(?:\\r\\n|\\s)?)|\\\\[^\\r\\n\\f0-9a-f])",i="((?:-?"+("(?:[0-9]*\\.[0-9]+|[0-9]+)(?:\\s*(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)|"+("-?"+("(?:[_a-z]|"+o+"|"+n+")")+("(?:[_a-z0-9-]|"+o+"|"+n+")")+"*")+")?")+")|(?:inherit|auto))",l=new RegExp("(translateX\\s*\\(\\s*)"+i+"(\\s*\\))","gi"),s=new RegExp("(translate\\s*\\(\\s*)"+i+"((?:\\s*,\\s*"+i+"){0,1}\\s*\\))","gi"),a=new RegExp("(translate3d\\s*\\(\\s*)"+i+"((?:\\s*,\\s*"+i+"){0,2}\\s*\\))","gi"),u=new RegExp("(rotate[ZY]?\\s*\\(\\s*)"+i+"(\\s*\\))","gi");return r.replace(l,kl).replace(s,kl).replace(a,kl).replace(u,kl)}};C.objectPosition=C.backgroundPosition,C.margin=C.padding,C.borderWidth=C.padding,C.boxShadow=C.textShadow,C.webkitBoxShadow=C.boxShadow,C.mozBoxShadow=C.boxShadow,C.WebkitBoxShadow=C.boxShadow,C.MozBoxShadow=C.boxShadow,C.borderStyle=C.borderColor,C.webkitTransform=C.transform,C.mozTransform=C.transform,C.WebkitTransform=C.transform,C.MozTransform=C.transform,C.transformOrigin=C.backgroundPosition,C.webkitTransformOrigin=C.transformOrigin,C.mozTransformOrigin=C.transformOrigin,C.WebkitTransformOrigin=C.transformOrigin,C.MozTransformOrigin=C.transformOrigin,C.webkitTransition=C.transition,C.mozTransition=C.transition,C.WebkitTransition=C.transition,C.MozTransition=C.transition,C.webkitTransitionProperty=C.transitionProperty,C.mozTransitionProperty=C.transitionProperty,C.WebkitTransitionProperty=C.transitionProperty,C.MozTransitionProperty=C.transitionProperty,C["text-shadow"]=C.textShadow,C["border-color"]=C.borderColor,C["border-radius"]=C.borderRadius,C["background-image"]=C.backgroundImage,C["background-position"]=C.backgroundPosition,C["background-position-x"]=C.backgroundPositionX,C["object-position"]=C.objectPosition,C["border-width"]=C.padding,C["box-shadow"]=C.textShadow,C["-webkit-box-shadow"]=C.textShadow,C["-moz-box-shadow"]=C.textShadow,C["border-style"]=C.borderColor,C["-webkit-transform"]=C.transform,C["-moz-transform"]=C.transform,C["transform-origin"]=C.transformOrigin,C["-webkit-transform-origin"]=C.transformOrigin,C["-moz-transform-origin"]=C.transformOrigin,C["-webkit-transition"]=C.transition,C["-moz-transition"]=C.transition,C["transition-property"]=C.transitionProperty,C["-webkit-transition-property"]=C.transitionProperty,C["-moz-transition-property"]=C.transitionProperty;var Vg=$g([["paddingLeft","paddingRight"],["marginLeft","marginRight"],["left","right"],["borderLeft","borderRight"],["borderLeftColor","borderRightColor"],["borderLeftStyle","borderRightStyle"],["borderLeftWidth","borderRightWidth"],["borderTopLeftRadius","borderTopRightRadius"],["borderBottomLeftRadius","borderBottomRightRadius"],["padding-left","padding-right"],["margin-left","margin-right"],["border-left","border-right"],["border-left-color","border-right-color"],["border-left-style","border-right-style"],["border-left-width","border-right-width"],["border-top-left-radius","border-top-right-radius"],["border-bottom-left-radius","border-bottom-right-radius"]]),j1=["content"],Kg=$g([["ltr","rtl"],["left","right"],["w-resize","e-resize"],["sw-resize","se-resize"],["nw-resize","ne-resize"]]),H1=new RegExp("(^|\\W|_)((ltr)|(rtl)|(left)|(right))(\\W|_|$)","g"),W1=new RegExp("(left)|(right)");function Xg(e){return Object.keys(e).reduce(function(t,r){var o=e[r];if(z1(o)&&(o=o.trim()),yl(j1,r))return t[r]=o,t;var n=pu(r,o),i=n.key,l=n.value;return t[i]=l,t},Array.isArray(e)?[]:{})}function pu(e,t){var r=/\/\*\s?@noflip\s?\*\//.test(t),o=r?e:$1(e),n=r?t:q1(o,t);return{key:o,value:n}}function $1(e){return Vg[e]||e}function q1(e,t){if(!I1(t))return t;if(R1(t))return Xg(t);var r=In(t),o=D1(t),n=r||o?t:t.replace(/ !important.*?$/,""),i=!r&&n.length!==t.length,l=C[e],s;return l?s=l({value:n,valuesToConvert:Kg,propertiesToConvert:Vg,isRtl:!0,bgImgDirectionRegex:H1,bgPosDirectionRegex:W1}):s=Kg[n]||n,i?s+" !important":s}const U1=/[A-Z]/g,V1=/^ms-/,hu={};function K1(e){return"-"+e.toLowerCase()}function Mn(e){if(Object.prototype.hasOwnProperty.call(hu,e))return hu[e];if(e.substr(0,2)==="--")return e;const t=e.replace(U1,K1);return hu[e]=V1.test(t)?"-"+t:t}function Yg(e){return e.charAt(0)==="&"?e.slice(1):e}var jn="-moz-",bt="-webkit-",Qg="comm",bl="rule",vu="decl",X1="@media",Y1="@import",Q1="@supports",J1="@keyframes",Jg="@layer",Zg=Math.abs,wl=String.fromCharCode,Z1=Object.assign;function G1(e,t){return Re(e,0)^45?(((t<<2^Re(e,0))<<2^Re(e,1))<<2^Re(e,2))<<2^Re(e,3):0}function Gg(e){return e.trim()}function ep(e,t){return(e=t.exec(e))?e[0]:e}function Ue(e,t,r){return e.replace(t,r)}function tp(e,t,r){return e.indexOf(t,r)}function Re(e,t){return e.charCodeAt(t)|0}function Hn(e,t,r){return e.slice(t,r)}function Tt(e){return e.length}function rp(e){return e.length}function Ur(e,t){return t.push(e),e}function ey(e,t){return e.map(t).join("")}var Bl=1,Lo=1,op=0,et=0,se=0,Io="";function _l(e,t,r,o,n,i,l,s){return{value:e,root:t,parent:r,type:o,props:n,children:i,line:Bl,column:Lo,length:l,return:"",siblings:s}}function mu(e,t){return Z1(_l("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function ty(){return se}function ry(){return se=et>0?Re(Io,--et):0,Lo--,se===10&&(Lo=1,Bl--),se}function ct(){return se=et<op?Re(Io,et++):0,Lo++,se===10&&(Lo=1,Bl++),se}function Vr(){return Re(Io,et)}function xl(){return et}function Sl(e,t){return Hn(Io,e,t)}function El(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function np(e){return Bl=Lo=1,op=Tt(Io=e),et=0,[]}function ip(e){return Io="",e}function Nl(e){return Gg(Sl(et-1,yu(e===91?e+2:e===40?e+1:e)))}function oy(e){return ip(iy(np(e)))}function ny(e){for(;(se=Vr())&&se<33;)ct();return El(e)>2||El(se)>3?"":" "}function iy(e){for(;ct();)switch(El(se)){case 0:Ur(lp(et-1),e);break;case 2:Ur(Nl(se),e);break;default:Ur(wl(se),e)}return e}function ly(e,t){for(;--t&&ct()&&!(se<48||se>102||se>57&&se<65||se>70&&se<97););return Sl(e,xl()+(t<6&&Vr()==32&&ct()==32))}function yu(e){for(;ct();)switch(se){case e:return et;case 34:case 39:e!==34&&e!==39&&yu(se);break;case 40:e===41&&yu(e);break;case 92:ct();break}return et}function sy(e,t){for(;ct()&&e+se!==47+10;)if(e+se===42+42&&Vr()===47)break;return"/*"+Sl(t,et-1)+"*"+wl(e===47?e:ct())}function lp(e){for(;!El(Vr());)ct();return Sl(e,et)}function sp(e){return ip(Fl("",null,null,null,[""],e=np(e),0,[0],e))}function Fl(e,t,r,o,n,i,l,s,a){for(var u=0,d=0,f=l,g=0,y=0,v=0,k=1,_=1,m=1,h=0,c="",p=n,b=i,w=o,B=c;_;)switch(v=h,h=ct()){case 40:if(v!=108&&Re(B,f-1)==58){tp(B+=Ue(Nl(h),"&","&\f"),"&\f",Zg(u?s[u-1]:0))!=-1&&(m=-1);break}case 34:case 39:case 91:B+=Nl(h);break;case 9:case 10:case 13:case 32:B+=ny(v);break;case 92:B+=ly(xl()-1,7);continue;case 47:switch(Vr()){case 42:case 47:Ur(ay(sy(ct(),xl()),t,r,a),a);break;default:B+="/"}break;case 123*k:s[u++]=Tt(B)*m;case 125*k:case 59:case 0:switch(h){case 0:case 125:_=0;case 59+d:m==-1&&(B=Ue(B,/\f/g,"")),y>0&&Tt(B)-f&&Ur(y>32?up(B+";",o,r,f-1,a):up(Ue(B," ","")+";",o,r,f-2,a),a);break;case 59:B+=";";default:if(Ur(w=ap(B,t,r,u,d,n,s,c,p=[],b=[],f,i),i),h===123)if(d===0)Fl(B,t,w,w,p,i,f,s,b);else switch(g===99&&Re(B,3)===110?100:g){case 100:case 108:case 109:case 115:Fl(e,w,w,o&&Ur(ap(e,w,w,0,0,n,s,c,n,p=[],f,b),b),n,b,f,s,o?p:b);break;default:Fl(B,w,w,w,[""],b,0,s,b)}}u=d=y=0,k=m=1,c=B="",f=l;break;case 58:f=1+Tt(B),y=v;default:if(k<1){if(h==123)--k;else if(h==125&&k++==0&&ry()==125)continue}switch(B+=wl(h),h*k){case 38:m=d>0?1:(B+="\f",-1);break;case 44:s[u++]=(Tt(B)-1)*m,m=1;break;case 64:Vr()===45&&(B+=Nl(ct())),g=Vr(),d=f=Tt(c=B+=lp(xl())),h++;break;case 45:v===45&&Tt(B)==2&&(k=0)}}return i}function ap(e,t,r,o,n,i,l,s,a,u,d,f){for(var g=n-1,y=n===0?i:[""],v=rp(y),k=0,_=0,m=0;k<o;++k)for(var h=0,c=Hn(e,g+1,g=Zg(_=l[k])),p=e;h<v;++h)(p=Gg(_>0?y[h]+" "+c:Ue(c,/&\f/g,y[h])))&&(a[m++]=p);return _l(e,t,r,n===0?bl:s,a,u,d,f)}function ay(e,t,r,o){return _l(e,t,r,Qg,wl(ty()),Hn(e,2,-2),0,o)}function up(e,t,r,o,n){return _l(e,t,r,vu,Hn(e,0,o),Hn(e,o+1,-1),o,n)}function Mo(e,t){for(var r="",o=0;o<e.length;o++)r+=t(e[o],o,e,t)||"";return r}function cp(e,t,r,o){switch(e.type){case Jg:if(e.children.length)break;case Y1:case vu:return e.return=e.return||e.value;case Qg:return"";case J1:return e.return=e.value+"{"+Mo(e.children,o)+"}";case bl:if(!Tt(e.value=e.props.join(",")))return""}return Tt(r=Mo(e.children,o))?e.return=e.value+"{"+r+"}":""}function dp(e){var t=rp(e);return function(r,o,n,i){for(var l="",s=0;s<t;s++)l+=e[s](r,o,n,i)||"";return l}}function fp(e){return function(t){t.root||(t=t.return)&&e(t)}}const uy=e=>{switch(e.type){case bl:if(typeof e.props=="string")return;e.props=e.props.map(t=>t.indexOf(":global(")===-1?t:oy(t).reduce((r,o,n,i)=>{if(o==="")return r;if(o===":"&&i[n+1]==="global"){const l=i[n+2].slice(1,-1)+" ";return r.unshift(l),i[n+1]="",i[n+2]="",r}return r.push(o),r},[]).join(""))}};function gp(e,t,r){switch(G1(e,t)){case 5103:return bt+"print-"+e+e;case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:return bt+e+e;case 4215:if(Re(e,9)===102||Re(e,t+1)===116)return bt+e+e;break;case 4789:return jn+e+e;case 5349:case 4246:case 6968:return bt+e+jn+e+e;case 6187:if(!ep(e,/grab/))return Ue(Ue(Ue(e,/(zoom-|grab)/,bt+"$1"),/(image-set)/,bt+"$1"),e,"")+e;case 5495:case 3959:return Ue(e,/(image-set\([^]*)/,bt+"$1$`$1");case 4095:case 3583:case 4068:case 2532:return Ue(e,/(.+)-inline(.+)/,bt+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Tt(e)-1-t>6)switch(Re(e,t+1)){case 102:if(Re(e,t+3)===108)return Ue(e,/(.+:)(.+)-([^]+)/,"$1"+bt+"$2-$3$1"+jn+(Re(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~tp(e,"stretch")?gp(Ue(e,"stretch","fill-available"),t)+e:e}break}return e}function pp(e,t,r,o){if(e.length>-1&&!e.return)switch(e.type){case vu:e.return=gp(e.value,e.length);return;case bl:if(e.length)return ey(e.props,function(n){switch(ep(n,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Mo([mu(e,{props:[Ue(n,/:(read-\w+)/,":"+jn+"$1")]})],o);case"::placeholder":return Mo([mu(e,{props:[Ue(n,/:(plac\w+)/,":"+bt+"input-$1")]}),mu(e,{props:[Ue(n,/:(plac\w+)/,":"+jn+"$1")]})],o)}return""})}}function cy(e){switch(e.type){case"@container":case X1:case Q1:case Jg:return!0}return!1}const dy=e=>{cy(e)&&Array.isArray(e.children)&&e.children.sort((t,r)=>t.props[0]>r.props[0]?1:-1)};function fy(){}function gy(e,t){const r=[];return Mo(sp(e),dp([uy,t?dy:fy,pp,cp,fp(o=>r.push(o))])),r}const py=/,( *[^ &])/g;function hy(e){return"&"+Yg(e.replace(py,",&$1"))}function hp(e,t,r){let o=t;return r.length>0&&(o=r.reduceRight((n,i)=>`${hy(i)} { ${n} }`,t)),`${e}{${o}}`}function vp(e){const{className:t,media:r,layer:o,selectors:n,support:i,property:l,rtlClassName:s,rtlProperty:a,rtlValue:u,value:d,container:f}=e,g=`.${t}`,y=Array.isArray(d)?`${d.map(k=>`${Mn(l)}: ${k}`).join(";")};`:`${Mn(l)}: ${d};`;let v=hp(g,y,n);if(a&&s){const k=`.${s}`,_=Array.isArray(u)?`${u.map(m=>`${Mn(a)}: ${m}`).join(";")};`:`${Mn(a)}: ${u};`;v+=hp(k,_,n)}return r&&(v=`@media ${r} { ${v} }`),o&&(v=`@layer ${o} { ${v} }`),i&&(v=`@supports ${i} { ${v} }`),f&&(v=`@container ${f} { ${v} }`),gy(v,!0)}function vy(e){let t="";for(const r in e){const o=e[r];typeof o!="string"&&typeof o!="number"||(t+=Mn(r)+":"+o+";")}return t}function mp(e){let t="";for(const r in e)t+=`${r}{${vy(e[r])}}`;return t}function yp(e,t){const r=`@keyframes ${e} {${t}}`,o=[];return Mo(sp(r),dp([cp,pp,fp(n=>o.push(n))])),o}function kp(e,t){return e.length===0?t:`${e} and ${t}`}function my(e){return e.substr(0,6)==="@media"}function yy(e){return e.substr(0,6)==="@layer"}const ky=/^(:|\[|>|&)/;function by(e){return ky.test(e)}function wy(e){return e.substr(0,9)==="@supports"}function By(e){return e.substring(0,10)==="@container"}function _y(e){return e!=null&&typeof e=="object"&&Array.isArray(e)===!1}const bp={"us-w":"w","us-v":"i",nk:"l",si:"v",cu:"f",ve:"h",ti:"a"};function wp(e,t,r,o,n){if(r)return"m";if(t||o)return"t";if(n)return"c";if(e.length>0){const i=e[0].trim();if(i.charCodeAt(0)===58)return bp[i.slice(4,8)]||bp[i.slice(3,5)]||"d"}return"d"}function Cl({container:e,media:t,layer:r,property:o,selector:n,support:i,value:l}){const s=Ln(n+e+t+r+i+o+l.trim());return cu+s}function Bp(e,t,r,o,n){const i=e+t+r+o+n,l=Ln(i),s=l.charCodeAt(0);return s>=48&&s<=57?String.fromCharCode(s+17)+l.slice(1):l}function _p(e){return e.replace(/>\s+/g,">")}function xy(e,t){const r=JSON.stringify(t,null,2);" ".repeat(2)+""," ".repeat(4)+""," ".repeat(6)+`"${e}": ${r.split(`
`).map((o,n)=>" ".repeat(n===0?0:6)+o).join(`
`)}`," ".repeat(4)+""," ".repeat(2)+"",e.indexOf("&")}function _S(e,t){}function xp(e,t,r,o){e[t]=o?[r,o]:r}function Sp(e,t){return t?[e,t]:e}function ku(e,t,r,o,n){var i;let l;t==="m"&&n&&(l={m:n}),(i=e[t])!=null||(e[t]=[]),r&&e[t].push(Sp(r,l)),o&&e[t].push(Sp(o,l))}function Kr(e,t=[],r="",o="",n="",i="",l={},s={},a){for(const u in e){if(B1.hasOwnProperty(u)){e[u];continue}const d=e[u];if(d!=null){if(typeof d=="string"||typeof d=="number"){const f=_p(t.join("")),g=Bp(f,i,r,n,u),y=Cl({container:i,media:r,layer:o,value:d.toString(),support:n,selector:f,property:u}),v=a&&{key:u,value:a}||pu(u,d),k=v.key!==u||v.value!==d,_=k?Cl({container:i,value:v.value.toString(),property:v.key,selector:f,media:r,layer:o,support:n}):void 0,m=k?{rtlClassName:_,rtlProperty:v.key,rtlValue:v.value}:void 0,h=wp(t,o,r,n,i),[c,p]=vp(Object.assign({className:y,media:r,layer:o,selectors:t,property:u,support:n,container:i,value:d},m));xp(l,g,y,_),ku(s,h,c,p,r)}else if(u==="animationName"){const f=Array.isArray(d)?d:[d],g=[],y=[];for(const v of f){const k=mp(v),_=mp(Xg(v)),m=cu+Ln(k);let h;const c=yp(m,k);let p=[];k===_?h=m:(h=cu+Ln(_),p=yp(h,_));for(let b=0;b<c.length;b++)ku(s,"k",c[b],p[b],r);g.push(m),y.push(h)}Kr({animationName:g.join(", ")},t,r,o,n,i,l,s,y.join(", "))}else if(Array.isArray(d)){if(d.length===0)continue;const f=_p(t.join("")),g=Bp(f,i,r,n,u),y=Cl({container:i,media:r,layer:o,value:d.map(w=>(w!=null?w:"").toString()).join(";"),support:n,selector:f,property:u}),v=d.map(w=>pu(u,w));if(!!v.some(w=>w.key!==v[0].key))continue;const _=v[0].key!==u||v.some((w,B)=>w.value!==d[B]),m=_?Cl({container:i,value:v.map(w=>{var B;return((B=w==null?void 0:w.value)!=null?B:"").toString()}).join(";"),property:v[0].key,selector:f,layer:o,media:r,support:n}):void 0,h=_?{rtlClassName:m,rtlProperty:v[0].key,rtlValue:v.map(w=>w.value)}:void 0,c=wp(t,o,r,n,i),[p,b]=vp(Object.assign({className:y,media:r,layer:o,selectors:t,property:u,support:n,container:i,value:d},h));xp(l,g,y,m),ku(s,c,p,b,r)}else if(_y(d))if(by(u))Kr(d,t.concat(Yg(u)),r,o,n,i,l,s);else if(my(u)){const f=kp(r,u.slice(6).trim());Kr(d,t,f,o,n,i,l,s)}else if(yy(u)){const f=(o?`${o}.`:"")+u.slice(6).trim();Kr(d,t,r,f,n,i,l,s)}else if(wy(u)){const f=kp(n,u.slice(9).trim());Kr(d,t,r,o,f,i,l,s)}else if(By(u)){const f=u.slice(10).trim();Kr(d,t,r,o,n,f,l,s)}else xy(u,d)}}return[l,s]}function Sy(e){const t={},r={};for(const o in e){const n=e[o],[i,l]=Kr(n);t[o]=i,Object.keys(l).forEach(s=>{r[s]=(r[s]||[]).concat(l[s])})}return[t,r]}function Ey(e,t=gu){const r=t();let o=null,n=null,i=null,l=null;function s(a){const{dir:u,renderer:d}=a;o===null&&([o,n]=Sy(e));const f=u==="ltr";return f?i===null&&(i=vl(o,u)):l===null&&(l=vl(o,u)),r(d,n),f?i:l}return s}function Ep(e,t,r=gu){const o=r();let n=null,i=null;function l(s){const{dir:a,renderer:u}=s,d=a==="ltr";return d?n===null&&(n=vl(e,a)):i===null&&(i=vl(e,a)),o(u,t),d?n:i}return l}function Ny(e,t,r,o=gu){const n=o();function i(l){const{dir:s,renderer:a}=l,u=s==="ltr"?e:t||e;return n(a,Array.isArray(r)?{r}:r),u}return i}const Fy=["Top","Right","Bottom","Left"];function Wn(e,t,...r){const[o,n=o,i=o,l=n]=r,s=[o,n,i,l],a={};for(let u=0;u<s.length;u+=1)if(s[u]||s[u]===0){const d=e+Fy[u]+t;a[d]=s[u]}return a}function bu(...e){return Wn("border","Width",...e)}function wu(...e){return Wn("border","Style",...e)}function Bu(...e){return Wn("border","Color",...e)}const Cy=["none","hidden","dotted","dashed","solid","double","groove","ridge","inset","outset"];function $n(e){return Cy.includes(e)}function Py(...e){return $n(e[0])?Object.assign({},wu(e[0]),e[1]&&bu(e[1]),e[2]&&Bu(e[2])):Object.assign({},bu(e[0]),e[1]&&wu(e[1]),e[2]&&Bu(e[2]))}function Ty(...e){return $n(e[0])?Object.assign({borderLeftStyle:e[0]},e[1]&&{borderLeftWidth:e[1]},e[2]&&{borderLeftColor:e[2]}):Object.assign({borderLeftWidth:e[0]},e[1]&&{borderLeftStyle:e[1]},e[2]&&{borderLeftColor:e[2]})}function Dy(...e){return $n(e[0])?Object.assign({borderBottomStyle:e[0]},e[1]&&{borderBottomWidth:e[1]},e[2]&&{borderBottomColor:e[2]}):Object.assign({borderBottomWidth:e[0]},e[1]&&{borderBottomStyle:e[1]},e[2]&&{borderBottomColor:e[2]})}function Oy(...e){return $n(e[0])?Object.assign({borderRightStyle:e[0]},e[1]&&{borderRightWidth:e[1]},e[2]&&{borderRightColor:e[2]}):Object.assign({borderRightWidth:e[0]},e[1]&&{borderRightStyle:e[1]},e[2]&&{borderRightColor:e[2]})}function Ry(...e){return $n(e[0])?Object.assign({borderTopStyle:e[0]},e[1]&&{borderTopWidth:e[1]},e[2]&&{borderTopColor:e[2]}):Object.assign({borderTopWidth:e[0]},e[1]&&{borderTopStyle:e[1]},e[2]&&{borderTopColor:e[2]})}function zy(e,t=e,r=e,o=t){return{borderBottomRightRadius:r,borderBottomLeftRadius:o,borderTopRightRadius:t,borderTopLeftRadius:e}}const Ay=e=>typeof e=="string"&&/(\d+(\w+|%))/.test(e),Pl=e=>typeof e=="number"&&!Number.isNaN(e),Ly=e=>e==="initial",Np=e=>e==="auto",Iy=e=>e==="none",My=["content","fit-content","max-content","min-content"],_u=e=>My.some(t=>e===t)||Ay(e);function jy(...e){const t=e.length===1,r=e.length===2,o=e.length===3;if(t){const[n]=e;if(Ly(n))return{flexGrow:0,flexShrink:1,flexBasis:"auto"};if(Np(n))return{flexGrow:1,flexShrink:1,flexBasis:"auto"};if(Iy(n))return{flexGrow:0,flexShrink:0,flexBasis:"auto"};if(Pl(n))return{flexGrow:n,flexShrink:1,flexBasis:0};if(_u(n))return{flexGrow:1,flexShrink:1,flexBasis:n}}if(r){const[n,i]=e;if(Pl(i))return{flexGrow:n,flexShrink:i,flexBasis:0};if(_u(i))return{flexGrow:n,flexShrink:1,flexBasis:i}}if(o){const[n,i,l]=e;if(Pl(n)&&Pl(i)&&(Np(l)||_u(l)))return{flexGrow:n,flexShrink:i,flexBasis:l}}return{}}function Hy(e,t=e){return{columnGap:e,rowGap:t}}const Wy=/var\(.*\)/gi;function $y(e){return e===void 0||typeof e=="number"||typeof e=="string"&&!Wy.test(e)}const qy=/^[a-zA-Z0-9\-_\\#;]+$/,Uy=/^-moz-initial$|^auto$|^initial$|^inherit$|^revert$|^unset$|^span \d+$|^\d.*/;function xu(e){return e!==void 0&&typeof e=="string"&&qy.test(e)&&!Uy.test(e)}function Vy(...e){if(e.some(i=>!$y(i)))return{};const t=e[0]!==void 0?e[0]:"auto",r=e[1]!==void 0?e[1]:xu(t)?t:"auto",o=e[2]!==void 0?e[2]:xu(t)?t:"auto",n=e[3]!==void 0?e[3]:xu(r)?r:"auto";return{gridRowStart:t,gridColumnStart:r,gridRowEnd:o,gridColumnEnd:n}}function Ky(...e){return Wn("margin","",...e)}function Xy(e,t=e){return{marginBlockStart:e,marginBlockEnd:t}}function Yy(e,t=e){return{marginInlineStart:e,marginInlineEnd:t}}function Qy(...e){return Wn("padding","",...e)}function Jy(e,t=e){return{paddingBlockStart:e,paddingBlockEnd:t}}function Zy(e,t=e){return{paddingInlineStart:e,paddingInlineEnd:t}}function Gy(e,t=e){return{overflowX:e,overflowY:t}}function ek(...e){const[t,r=t,o=t,n=r]=e;return{top:t,right:r,bottom:o,left:n}}function tk(e,t,r){return Object.assign({outlineWidth:e},t&&{outlineStyle:t},r&&{outlineColor:r})}function rk(...e){return nk(e)?{transitionDelay:e[0],transitionDuration:e[0],transitionProperty:e[0],transitionTimingFunction:e[0]}:ik(e).reduce((r,[o,n="0s",i="0s",l="ease"],s)=>(s===0?(r.transitionProperty=o,r.transitionDuration=n,r.transitionDelay=i,r.transitionTimingFunction=l):(r.transitionProperty+=`, ${o}`,r.transitionDuration+=`, ${n}`,r.transitionDelay+=`, ${i}`,r.transitionTimingFunction+=`, ${l}`),r),{})}const ok=["-moz-initial","inherit","initial","revert","unset"];function nk(e){return e.length===1&&ok.includes(e[0])}function ik(e){return e.length===1&&Array.isArray(e[0])?e[0]:[e]}function lk(e,...t){if(t.length===0)return ak(e)?{textDecorationStyle:e}:{textDecorationLine:e};const[r,o,n]=t;return Object.assign({textDecorationLine:e},r&&{textDecorationStyle:r},o&&{textDecorationColor:o},n&&{textDecorationThickness:n})}const sk=["dashed","dotted","double","solid","wavy"];function ak(e){return sk.includes(e)}const Fp={border:Py,borderLeft:Ty,borderBottom:Dy,borderRight:Oy,borderTop:Ry,borderColor:Bu,borderStyle:wu,borderRadius:zy,borderWidth:bu,flex:jy,gap:Hy,gridArea:Vy,margin:Ky,marginBlock:Xy,marginInline:Yy,padding:Qy,paddingBlock:Jy,paddingInline:Zy,overflow:Gy,inset:ek,outline:tk,transition:rk,textDecoration:lk};function uk(){return typeof window<"u"&&!!(window.document&&window.document.createElement)}const Cp=lo["useInsertionEffect"]?lo["useInsertionEffect"]:void 0,Su=()=>{const e={};return function(r,o){if(Cp&&uk()){Cp(()=>{r.insertCSSRules(o)},[r,o]);return}e[r.id]===void 0&&(r.insertCSSRules(o),e[r.id]=!0)}},ck=x.exports.createContext(P1());function qn(){return x.exports.useContext(ck)}const Pp=x.exports.createContext("ltr"),dk=({children:e,dir:t})=>x.exports.createElement(Pp.Provider,{value:t},e);function Eu(){return x.exports.useContext(Pp)}function Tp(e){const t=Ey(e,Su);return function(){const o=Eu(),n=qn();return t({dir:o,renderer:n})}}function Ve(e,t){const r=Ep(e,t,Su);return function(){const n=Eu(),i=qn();return r({dir:n,renderer:i})}}function Kt(e,t,r){const o=Ny(e,t,r,Su);return function(){const i=Eu(),l=qn();return o({dir:i,renderer:l})}}function fk(e,t){if(t){const r=Object.keys(t).reduce((o,n)=>`${o}--${n}: ${t[n]}; `,"");return`${e} { ${r} }`}return`${e} {}`}const Dp=Symbol.for("fui.slotRenderFunction"),Tl=Symbol.for("fui.slotElementType");function wt(e,t){const{defaultProps:r,elementType:o}=t,n=gk(e),i={...r,...n,[Tl]:o};return n&&typeof n.children=="function"&&(i[Dp]=n.children,i.children=r==null?void 0:r.children),i}function Dl(e,t){if(!(e===null||e===void 0&&!t.renderByDefault))return wt(e,t)}function gk(e){return typeof e=="string"||typeof e=="number"||Array.isArray(e)||x.exports.isValidElement(e)?{children:e}:e}function pk(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&!x.exports.isValidElement(e)}function Op(e){return Boolean(e==null?void 0:e.hasOwnProperty(Tl))}const Z=(...e)=>{const t={};for(const r of e){const o=Array.isArray(r)?r:Object.keys(r);for(const n of o)t[n]=1}return t},hk=Z(["onAuxClick","onAnimationEnd","onAnimationStart","onCopy","onCut","onPaste","onCompositionEnd","onCompositionStart","onCompositionUpdate","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onInput","onSubmit","onLoad","onError","onKeyDown","onKeyDownCapture","onKeyPress","onKeyUp","onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting","onClick","onClickCapture","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onMouseUpCapture","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onScroll","onWheel","onPointerCancel","onPointerDown","onPointerEnter","onPointerLeave","onPointerMove","onPointerOut","onPointerOver","onPointerUp","onGotPointerCapture","onLostPointerCapture"]),vk=Z(["accessKey","children","className","contentEditable","dir","draggable","hidden","htmlFor","id","lang","ref","role","style","tabIndex","title","translate","spellCheck","name"]),mk=Z(["itemID","itemProp","itemRef","itemScope","itemType"]),pe=Z(vk,hk,mk),yk=Z(pe,["form"]),Rp=Z(pe,["height","loop","muted","preload","src","width"]),kk=Z(Rp,["poster"]),bk=Z(pe,["start"]),wk=Z(pe,["value"]),Bk=Z(pe,["download","href","hrefLang","media","rel","target","type"]),_k=Z(pe,["dateTime"]),Ol=Z(pe,["autoFocus","disabled","form","formAction","formEncType","formMethod","formNoValidate","formTarget","type","value"]),xk=Z(Ol,["accept","alt","autoCapitalize","autoComplete","checked","dirname","form","height","inputMode","list","max","maxLength","min","multiple","pattern","placeholder","readOnly","required","src","step","size","type","value","width"]),Sk=Z(Ol,["autoCapitalize","cols","dirname","form","maxLength","placeholder","readOnly","required","rows","wrap"]),Ek=Z(Ol,["form","multiple","required"]),Nk=Z(pe,["selected","value"]),Fk=Z(pe,["cellPadding","cellSpacing"]),Ck=pe,Pk=Z(pe,["colSpan","rowSpan","scope"]),Tk=Z(pe,["colSpan","headers","rowSpan","scope"]),Dk=Z(pe,["span"]),Ok=Z(pe,["span"]),Rk=Z(pe,["disabled","form"]),zk=Z(pe,["acceptCharset","action","encType","encType","method","noValidate","target"]),Ak=Z(pe,["allow","allowFullScreen","allowPaymentRequest","allowTransparency","csp","height","importance","referrerPolicy","sandbox","src","srcDoc","width"]),Lk=Z(pe,["alt","crossOrigin","height","src","srcSet","useMap","width"]),Ik=Z(pe,["open","onCancel","onClose"]);function Mk(e,t,r){const o=Array.isArray(t),n={},i=Object.keys(e);for(const l of i)(!o&&t[l]||o&&t.indexOf(l)>=0||l.indexOf("data-")===0||l.indexOf("aria-")===0)&&(!r||(r==null?void 0:r.indexOf(l))===-1)&&(n[l]=e[l]);return n}const jk={label:yk,audio:Rp,video:kk,ol:bk,li:wk,a:Bk,button:Ol,input:xk,textarea:Sk,select:Ek,option:Nk,table:Fk,tr:Ck,th:Pk,td:Tk,colGroup:Dk,col:Ok,fieldset:Rk,form:zk,iframe:Ak,img:Lk,time:_k,dialog:Ik};function Hk(e,t,r){const o=e&&jk[e]||pe;return o.as=1,Mk(t,o,r)}const Xt=(e,t,r)=>{var o;return Hk((o=t.as)!==null&&o!==void 0?o:e,t,r)};function Nu(){return typeof window<"u"&&!!(window.document&&window.document.createElement)}function Wk(e){return typeof e=="function"}const zp=e=>{const[t,r]=x.exports.useState(()=>e.defaultState===void 0?e.initialState:$k(e.defaultState)?e.defaultState():e.defaultState),o=x.exports.useRef(e.state);x.exports.useEffect(()=>{o.current=e.state},[e.state]);const n=x.exports.useCallback(i=>{Wk(i)&&i(o.current)},[]);return qk(e.state)?[e.state,n]:[t,r]};function $k(e){return typeof e=="function"}const qk=e=>{const[t]=x.exports.useState(()=>e!==void 0);return t},Uk={current:0},Vk=x.exports.createContext(void 0);function Kk(){var e;return(e=x.exports.useContext(Vk))!==null&&e!==void 0?e:Uk}const Yt=Nu()?x.exports.useLayoutEffect:x.exports.useEffect,ze=e=>{const t=x.exports.useRef(()=>{throw new Error("Cannot call an event handler while rendering")});return Yt(()=>{t.current=e},[e]),x.exports.useCallback((...r)=>{const o=t.current;return o(...r)},[t])},Ap=x.exports.createContext(void 0);Ap.Provider;function Xk(){return x.exports.useContext(Ap)||""}function Fu(e="fui-",t){const r=Kk(),o=Xk(),n=lo["useId"];if(n){const i=n(),l=x.exports.useMemo(()=>i.replace(/:/g,""),[i]);return t||`${o}${e}${l}`}return x.exports.useMemo(()=>t||`${o}${e}${++r.current}`,[o,e,t,r])}function Cu(...e){const t=x.exports.useCallback(r=>{t.current=r;for(const o of e)typeof o=="function"?o(r):o&&(o.current=r)},[...e]);return t}const Lp=x.exports.createContext(void 0),Yk=Lp.Provider,Ip=x.exports.createContext(void 0),Qk="",Jk=Ip.Provider;function Zk(){var e;return(e=x.exports.useContext(Ip))!==null&&e!==void 0?e:Qk}const Gk=x.exports.createContext(void 0).Provider,Mp=x.exports.createContext(void 0),eb={targetDocument:typeof document=="object"?document:void 0,dir:"ltr"},tb=Mp.Provider;function Dt(){var e;return(e=x.exports.useContext(Mp))!==null&&e!==void 0?e:eb}const jp=x.exports.createContext(void 0),rb=jp.Provider;function ob(){var e;return(e=x.exports.useContext(jp))!==null&&e!==void 0?e:{}}const Pu=x.exports.createContext(void 0),nb=()=>{},ib=Pu.Provider,_r=e=>{var t,r;return(r=(t=x.exports.useContext(Pu))===null||t===void 0?void 0:t[e])!==null&&r!==void 0?r:nb},Hp=x.exports.createContext(void 0);Hp.Provider;function lb(){return x.exports.useContext(Hp)}const Wp=(e,t)=>!!(e!=null&&e.contains(t)),sb=e=>{const{targetDocument:t}=Dt(),r=t==null?void 0:t.defaultView,{refs:o,callback:n,element:i,disabled:l,disabledFocusOnIframe:s,contains:a=Wp}=e,u=x.exports.useRef(void 0);ub({element:i,disabled:s||l,callback:n,refs:o,contains:a});const d=x.exports.useRef(!1),f=ze(y=>{if(d.current){d.current=!1;return}const v=y.composedPath()[0];o.every(_=>!a(_.current||null,v))&&!l&&n(y)}),g=ze(y=>{d.current=o.some(v=>a(v.current||null,y.target))});x.exports.useEffect(()=>{if(l)return;let y=ab(r);const v=k=>{if(k===y){y=void 0;return}f(k)};return i==null||i.addEventListener("click",v,!0),i==null||i.addEventListener("touchstart",v,!0),i==null||i.addEventListener("contextmenu",v,!0),i==null||i.addEventListener("mousedown",g,!0),u.current=r==null?void 0:r.setTimeout(()=>{y=void 0},1),()=>{i==null||i.removeEventListener("click",v,!0),i==null||i.removeEventListener("touchstart",v,!0),i==null||i.removeEventListener("contextmenu",v,!0),i==null||i.removeEventListener("mousedown",g,!0),r==null||r.clearTimeout(u.current),y=void 0}},[f,i,l,g,r])},ab=e=>{if(e){var t,r;if(typeof e.window=="object"&&e.window===e)return e.event;var o;return(o=(r=e.ownerDocument)===null||r===void 0||(t=r.defaultView)===null||t===void 0?void 0:t.event)!==null&&o!==void 0?o:void 0}},Tu="fuiframefocus",ub=e=>{const{disabled:t,element:r,callback:o,contains:n=Wp,pollDuration:i=1e3,refs:l}=e,s=x.exports.useRef(),a=ze(u=>{l.every(f=>!n(f.current||null,u.target))&&!t&&o(u)});x.exports.useEffect(()=>{if(!t)return r==null||r.addEventListener(Tu,a,!0),()=>{r==null||r.removeEventListener(Tu,a,!0)}},[r,t,a]),x.exports.useEffect(()=>{var u;if(!t)return s.current=r==null||(u=r.defaultView)===null||u===void 0?void 0:u.setInterval(()=>{const d=r==null?void 0:r.activeElement;if((d==null?void 0:d.tagName)==="IFRAME"||(d==null?void 0:d.tagName)==="WEBVIEW"){const f=new CustomEvent(Tu,{bubbles:!0});d.dispatchEvent(f)}},i),()=>{var d;r==null||(d=r.defaultView)===null||d===void 0||d.clearTimeout(s.current)}},[r,t,i])},cb=e=>{const{refs:t,callback:r,element:o,disabled:n,contains:i}=e,l=ze(s=>{const a=i||((f,g)=>!!(f!=null&&f.contains(g))),u=s.composedPath()[0];t.every(f=>!a(f.current||null,u))&&!n&&r(s)});x.exports.useEffect(()=>{if(!n)return o==null||o.addEventListener("wheel",l),o==null||o.addEventListener("touchmove",l),()=>{o==null||o.removeEventListener("wheel",l),o==null||o.removeEventListener("touchmove",l)}},[l,o,n])};function Un(e,t){var r;const o=e;var n;return Boolean((o==null||(r=o.ownerDocument)===null||r===void 0?void 0:r.defaultView)&&o instanceof o.ownerDocument.defaultView[(n=t==null?void 0:t.constructorName)!==null&&n!==void 0?n:"HTMLElement"])}function $p(e){return Boolean(e.type.isFluentTriggerComponent)}function db(e,t){return typeof e=="function"?e(t):e?qp(e,t):e||null}function qp(e,t){if(!x.exports.isValidElement(e)||e.type===x.exports.Fragment)throw new Error("A trigger element must be a single element for this component. Please ensure that you're not using React Fragments.");if($p(e)){const r=qp(e.props.children,t);return x.exports.cloneElement(e,void 0,r)}else return x.exports.cloneElement(e,t)}function Up(e){return x.exports.isValidElement(e)?$p(e)?Up(e.props.children):e:null}function fb(e){return e&&!!e._virtual}function gb(e){return fb(e)&&e._virtual.parent||null}function Vp(e,t={}){if(!e)return null;if(!t.skipVirtual){const o=gb(e);if(o)return o}const r=e.parentNode;return r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}function Kp(e,t){if(!e||!t)return!1;if(e===t)return!0;{const r=new WeakSet;for(;t;){const o=Vp(t,{skipVirtual:r.has(t)});if(r.add(t),o===e)return!0;t=o}}return!1}function Xp(e,t){if(!e)return;const r=e;r._virtual||(r._virtual={}),r._virtual.parent=t}function pb(e,t){return{...t,[Tl]:e}}function Yp(e,t){return function(o,n,i,l,s){return Op(n)?t(pb(o,n),null,i,l,s):Op(o)?t(o,n,i,l,s):e(o,n,i,l,s)}}function Qp(e){const{as:t,[Tl]:r,[Dp]:o,...n}=e,i=n,l=typeof r=="string"&&t!=null?t:r;return typeof l!="string"&&t&&(i.as=t),{elementType:l,props:i,renderFunction:o}}var Du={exports:{}},Rl={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hb=x.exports,vb=Symbol.for("react.element"),mb=Symbol.for("react.fragment"),yb=Object.prototype.hasOwnProperty,kb=hb.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,bb={key:!0,ref:!0,__self:!0,__source:!0};function Jp(e,t,r){var o,n={},i=null,l=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(o in t)yb.call(t,o)&&!bb.hasOwnProperty(o)&&(n[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps,t)n[o]===void 0&&(n[o]=t[o]);return{$$typeof:vb,type:e,key:i,ref:l,props:n,_owner:kb.current}}Rl.Fragment=mb,Rl.jsx=Jp,Rl.jsxs=Jp,function(e){e.exports=Rl}(Du);const wb=Xl(Du.exports),Xr=Me({__proto__:null,default:wb},[Du.exports]),Bb=(e,t,r)=>{const{elementType:o,renderFunction:n,props:i}=Qp(e),l={...i,...t};return n?Xr.jsx(x.exports.Fragment,{children:n(o,l)},r):Xr.jsx(o,l,r)},_b=(e,t,r)=>{const{elementType:o,renderFunction:n,props:i}=Qp(e),l={...i,...t};return n?Xr.jsx(x.exports.Fragment,{children:n(o,{...l,children:Xr.jsxs(x.exports.Fragment,{children:l.children},void 0)})},r):Xr.jsxs(o,l,r)},G=Yp(Xr.jsx,Bb),Yr=Yp(Xr.jsxs,_b),Ou=x.exports.createContext(void 0),xb={},Sb=Ou.Provider,Eb=()=>x.exports.useContext(Ou)?x.exports.useContext(Ou):xb,Nb=Ve({root:{mc9l5x:"f1w7gpdv",Bg96gwp:"fez10in",ycbfsm:"fg4l7m0"},rtl:{Bz10aip:"f13rod7r"}},{d:[".f1w7gpdv{display:inline;}",".fez10in{line-height:0;}",".f13rod7r{-webkit-transform:scaleX(-1);-moz-transform:scaleX(-1);-ms-transform:scaleX(-1);transform:scaleX(-1);}"],t:["@media (forced-colors: active){.fg4l7m0{forced-color-adjust:auto;}}"]}),Fb=(e,t)=>{const{title:r,primaryFill:o="currentColor",...n}=e,i={...n,title:void 0,fill:o},l=Nb(),s=Eb();return i.className=_e(l.root,(t==null?void 0:t.flipInRtl)&&(s==null?void 0:s.textDirection)==="rtl"&&l.rtl,i.className),r&&(i["aria-label"]=r),!i["aria-label"]&&!i["aria-labelledby"]?i["aria-hidden"]=!0:i.role="img",i},Zp=(e,t,r,o)=>{const n=t==="1em"?"20":t,i=x.exports.forwardRef((l,s)=>{const a={...Fb(l,{flipInRtl:o==null?void 0:o.flipInRtl}),ref:s,width:t,height:t,viewBox:`0 0 ${n} ${n}`,xmlns:"http://www.w3.org/2000/svg"};return x.exports.createElement("svg",a,...r.map(u=>x.exports.createElement("path",{d:u,fill:a.fill})))});return i.displayName=e,i},Cb=Zp("Dismiss20Regular","20",["m4.09 4.22.06-.07a.5.5 0 0 1 .63-.06l.07.06L10 9.29l5.15-5.14a.5.5 0 0 1 .63-.06l.07.06c.**********.06.63l-.06.07L10.71 10l5.14 5.15c.**********.06.63l-.06.07a.5.5 0 0 1-.63.06l-.07-.06L10 10.71l-5.15 5.14a.5.5 0 0 1-.63.06l-.07-.06a.5.5 0 0 1-.06-.63l.06-.07L9.29 10 4.15 4.85a.5.5 0 0 1-.06-.63l.06-.07-.06.07Z"]),Pb=Zp("Dismiss24Regular","24",["m4.4 4.55.07-.08a.75.75 0 0 1 .98-.07l.08.07L12 10.94l6.47-6.47a.75.75 0 1 1 1.06 1.06L13.06 12l6.47 6.47c.**********.07.98l-.07.08a.75.75 0 0 1-.98.07l-.08-.07L12 13.06l-6.47 6.47a.75.75 0 0 1-1.06-1.06L10.94 12 4.47 5.53a.75.75 0 0 1-.07-.98l.07-.08-.07.08Z"]),Tb=(e,t)=>G(tb,{value:t.provider,children:G(Yk,{value:t.theme,children:G(Jk,{value:t.themeClassName,children:G(ib,{value:t.customStyleHooks_unstable,children:G(Gk,{value:t.tooltip,children:G(dk,{dir:t.textDirection,children:G(Sb,{value:t.iconDirection,children:G(rb,{value:t.overrides_unstable,children:Yr(e.root,{children:[Nu()?null:G("style",{dangerouslySetInnerHTML:{__html:e.serverStyleProps.cssRule},...e.serverStyleProps.attributes}),e.root.children]})})})})})})})})});var Db=typeof WeakRef<"u",Gp=class{constructor(e){Db&&typeof e=="object"?this._weakRef=new WeakRef(e):this._instance=e}deref(){var e,t;let r;return this._weakRef?(r=(e=this._weakRef)==null?void 0:e.deref(),r||delete this._weakRef):(r=this._instance,(t=r==null?void 0:r.isDisposed)!=null&&t.call(r)&&delete this._instance),r}},Qt="keyborg:focusin",Vn="keyborg:focusout";function Ob(e){const t=e.HTMLElement,r=t.prototype.focus;let o=!1;return t.prototype.focus=function(){o=!0},e.document.createElement("button").focus(),t.prototype.focus=r,o}var Ru=!1;function Qr(e){const t=e.focus;t.__keyborgNativeFocus?t.__keyborgNativeFocus.call(e):e.focus()}function Rb(e){const t=e;Ru||(Ru=Ob(t));const r=t.HTMLElement.prototype.focus;if(r.__keyborgNativeFocus)return;t.HTMLElement.prototype.focus=a;const o=new Set,n=d=>{const f=d.target;if(!f)return;const g=new CustomEvent(Vn,{cancelable:!0,bubbles:!0,composed:!0,detail:{originalEvent:d}});f.dispatchEvent(g)},i=d=>{const f=d.target;if(!f)return;let g=d.composedPath()[0];const y=new Set;for(;g;)g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?(y.add(g),g=g.host):g=g.parentNode;for(const v of o){const k=v.deref();(!k||!y.has(k))&&(o.delete(v),k&&(k.removeEventListener("focusin",i,!0),k.removeEventListener("focusout",n,!0)))}l(f,d.relatedTarget||void 0)},l=(d,f,g)=>{var y;const v=d.shadowRoot;if(v){for(const m of o)if(m.deref()===v)return;v.addEventListener("focusin",i,!0),v.addEventListener("focusout",n,!0),o.add(new Gp(v));return}const k={relatedTarget:f,originalEvent:g},_=new CustomEvent(Qt,{cancelable:!0,bubbles:!0,composed:!0,detail:k});_.details=k,(Ru||s.lastFocusedProgrammatically)&&(k.isFocusedProgrammatically=d===((y=s.lastFocusedProgrammatically)==null?void 0:y.deref()),s.lastFocusedProgrammatically=void 0),d.dispatchEvent(_)},s=t.__keyborgData={focusInHandler:i,focusOutHandler:n,shadowTargets:o};t.document.addEventListener("focusin",t.__keyborgData.focusInHandler,!0),t.document.addEventListener("focusout",t.__keyborgData.focusOutHandler,!0);function a(){const d=t.__keyborgData;return d&&(d.lastFocusedProgrammatically=new Gp(this)),r.apply(this,arguments)}let u=t.document.activeElement;for(;u&&u.shadowRoot;)l(u),u=u.shadowRoot.activeElement;a.__keyborgNativeFocus=r}function zb(e){const t=e,r=t.HTMLElement.prototype,o=r.focus.__keyborgNativeFocus,n=t.__keyborgData;if(n){t.document.removeEventListener("focusin",n.focusInHandler,!0),t.document.removeEventListener("focusout",n.focusOutHandler,!0);for(const i of n.shadowTargets){const l=i.deref();l&&(l.removeEventListener("focusin",n.focusInHandler,!0),l.removeEventListener("focusout",n.focusOutHandler,!0))}n.shadowTargets.clear(),delete t.__keyborgData}o&&(r.focus=o)}var Ab=500,eh=0,Lb=class{constructor(e,t){this._isNavigatingWithKeyboard_DO_NOT_USE=!1,this._onFocusIn=o=>{if(this._isMouseUsedTimer||this.isNavigatingWithKeyboard)return;const n=o.detail;!n.relatedTarget||n.isFocusedProgrammatically||n.isFocusedProgrammatically===void 0||(this.isNavigatingWithKeyboard=!0)},this._onMouseDown=o=>{if(o.buttons===0||o.clientX===0&&o.clientY===0&&o.screenX===0&&o.screenY===0)return;const n=this._win;n&&(this._isMouseUsedTimer&&n.clearTimeout(this._isMouseUsedTimer),this._isMouseUsedTimer=n.setTimeout(()=>{delete this._isMouseUsedTimer},1e3)),this.isNavigatingWithKeyboard=!1},this._onKeyDown=o=>{this.isNavigatingWithKeyboard?this._shouldDismissKeyboardNavigation(o)&&this._scheduleDismiss():this._shouldTriggerKeyboardNavigation(o)&&(this.isNavigatingWithKeyboard=!0)},this.id="c"+ ++eh,this._win=e;const r=e.document;if(t){const o=t.triggerKeys,n=t.dismissKeys;o!=null&&o.length&&(this._triggerKeys=new Set(o)),n!=null&&n.length&&(this._dismissKeys=new Set(n))}r.addEventListener(Qt,this._onFocusIn,!0),r.addEventListener("mousedown",this._onMouseDown,!0),e.addEventListener("keydown",this._onKeyDown,!0),Rb(e)}get isNavigatingWithKeyboard(){return this._isNavigatingWithKeyboard_DO_NOT_USE}set isNavigatingWithKeyboard(e){this._isNavigatingWithKeyboard_DO_NOT_USE!==e&&(this._isNavigatingWithKeyboard_DO_NOT_USE=e,this.update())}dispose(){const e=this._win;if(e){this._isMouseUsedTimer&&(e.clearTimeout(this._isMouseUsedTimer),this._isMouseUsedTimer=void 0),this._dismissTimer&&(e.clearTimeout(this._dismissTimer),this._dismissTimer=void 0),zb(e);const t=e.document;t.removeEventListener(Qt,this._onFocusIn,!0),t.removeEventListener("mousedown",this._onMouseDown,!0),e.removeEventListener("keydown",this._onKeyDown,!0),delete this._win}}isDisposed(){return!!this._win}update(){var e,t;const r=(t=(e=this._win)==null?void 0:e.__keyborg)==null?void 0:t.refs;if(r)for(const o of Object.keys(r))zu.update(r[o],this.isNavigatingWithKeyboard)}_shouldTriggerKeyboardNavigation(e){var t;if(e.key==="Tab")return!0;const r=(t=this._win)==null?void 0:t.document.activeElement,o=!this._triggerKeys||this._triggerKeys.has(e.keyCode),n=r&&(r.tagName==="INPUT"||r.tagName==="TEXTAREA"||r.isContentEditable);return o&&!n}_shouldDismissKeyboardNavigation(e){var t;return(t=this._dismissKeys)==null?void 0:t.has(e.keyCode)}_scheduleDismiss(){const e=this._win;if(e){this._dismissTimer&&(e.clearTimeout(this._dismissTimer),this._dismissTimer=void 0);const t=e.document.activeElement;this._dismissTimer=e.setTimeout(()=>{this._dismissTimer=void 0;const r=e.document.activeElement;t&&r&&t===r&&(this.isNavigatingWithKeyboard=!1)},Ab)}}},zu=class Qv{constructor(t,r){this._cb=[],this._id="k"+ ++eh,this._win=t;const o=t.__keyborg;o?(this._core=o.core,o.refs[this._id]=this):(this._core=new Lb(t,r),t.__keyborg={core:this._core,refs:{[this._id]:this}})}static create(t,r){return new Qv(t,r)}static dispose(t){t.dispose()}static update(t,r){t._cb.forEach(o=>o(r))}dispose(){var t;const r=(t=this._win)==null?void 0:t.__keyborg;r!=null&&r.refs[this._id]&&(delete r.refs[this._id],Object.keys(r.refs).length===0&&(r.core.dispose(),delete this._win.__keyborg)),this._cb=[],delete this._core,delete this._win}isNavigatingWithKeyboard(){var t;return!!((t=this._core)!=null&&t.isNavigatingWithKeyboard)}subscribe(t){this._cb.push(t)}unsubscribe(t){const r=this._cb.indexOf(t);r>=0&&this._cb.splice(r,1)}setVal(t){this._core&&(this._core.isNavigatingWithKeyboard=t)}};function th(e,t){return zu.create(e,t)}function rh(e){zu.dispose(e)}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 *//*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */const Jt="data-tabster",oh="data-tabster-dummy",Au=["a[href]","button:not([disabled])","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","*[tabindex]","*[contenteditable]","details > summary","audio[controls]","video[controls]"].join(", "),Jr={EscapeGroupper:1,Restorer:2,Deloser:3},Ib={Any:0,Accessible:1,Focusable:2},Mb={History:0,DeloserDefault:1,RootDefault:2,DeloserFirst:3,RootFirst:4},jb={Auto:0,Manual:1},Hb={Invisible:0,PartiallyVisible:1,Visible:2},Kn={Source:0,Target:1},Wb={Both:0,Vertical:1,Horizontal:2,Grid:3,GridLinear:4},$b={ArrowUp:1,ArrowDown:2,ArrowLeft:3,ArrowRight:4,PageUp:5,PageDown:6,Home:7,End:8},qb={Unlimited:0,Limited:1,LimitedTrapFocus:2},Ub={Enter:1,Escape:2},nh={Auto:0,Inside:1,Outside:2};var Lu=Object.freeze({__proto__:null,TabsterAttributeName:Jt,TabsterDummyInputAttributeName:oh,FocusableSelector:Au,AsyncFocusSources:Jr,ObservedElementAccesibilities:Ib,RestoreFocusOrders:Mb,DeloserStrategies:jb,Visibilities:Hb,RestorerTypes:Kn,MoverDirections:Wb,MoverKeys:$b,GroupperTabbabilities:qb,GroupperMoveFocusActions:Ub,SysDummyInputsPositions:nh});/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */function Ot(e,t){var r;return(r=e.storageEntry(t))===null||r===void 0?void 0:r.tabster}function ih(e,t,r){var o,n;const i=r||e._noop?void 0:t.getAttribute(Jt);let l=e.storageEntry(t),s;if(i)if(i!==((o=l==null?void 0:l.attr)===null||o===void 0?void 0:o.string))try{const f=JSON.parse(i);if(typeof f!="object")throw new Error(`Value is not a JSON object, got '${i}'.`);s={string:i,object:f}}catch{}else return;else if(!l)return;l||(l=e.storageEntry(t,!0)),l.tabster||(l.tabster={});const a=l.tabster||{},u=((n=l.attr)===null||n===void 0?void 0:n.object)||{},d=(s==null?void 0:s.object)||{};for(const f of Object.keys(u))if(!d[f]){if(f==="root"){const g=a[f];g&&e.root.onRoot(g,!0)}switch(f){case"deloser":case"root":case"groupper":case"modalizer":case"restorer":case"mover":const g=a[f];g&&(g.dispose(),delete a[f]);break;case"observed":delete a[f],e.observedElement&&e.observedElement.onObservedElementUpdate(t);break;case"focusable":case"outline":case"uncontrolled":case"sys":delete a[f];break}}for(const f of Object.keys(d)){const g=d.sys;switch(f){case"deloser":a.deloser?a.deloser.setProps(d.deloser):e.deloser&&(a.deloser=e.deloser.createDeloser(t,d.deloser));break;case"root":a.root?a.root.setProps(d.root):a.root=e.root.createRoot(t,d.root,g),e.root.onRoot(a.root);break;case"modalizer":a.modalizer?a.modalizer.setProps(d.modalizer):e.modalizer&&(a.modalizer=e.modalizer.createModalizer(t,d.modalizer,g));break;case"restorer":a.restorer?a.restorer.setProps(d.restorer):e.restorer&&d.restorer&&(a.restorer=e.restorer.createRestorer(t,d.restorer));break;case"focusable":a.focusable=d.focusable;break;case"groupper":a.groupper?a.groupper.setProps(d.groupper):e.groupper&&(a.groupper=e.groupper.createGroupper(t,d.groupper,g));break;case"mover":a.mover?a.mover.setProps(d.mover):e.mover&&(a.mover=e.mover.createMover(t,d.mover,g));break;case"observed":e.observedElement&&(a.observed=d.observed,e.observedElement.onObservedElementUpdate(t));break;case"uncontrolled":a.uncontrolled=d.uncontrolled;break;case"outline":e.outline&&(a.outline=d.outline);break;case"sys":a.sys=d.sys;break;default:console.error(`Unknown key '${f}' in data-tabster attribute value.`)}}s?l.attr=s:(Object.keys(a).length===0&&(delete l.tabster,delete l.attr),e.storageEntry(t,!1))}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */const Vb="tabster:focusin",Kb="tabster:focusout",Xb="tabster:movefocus",Yb="tabster:modalizer:active",Qb="tabster:modalizer:inactive",Iu="tabster:restorer:restore-focus",Jb="tabster:root:focus",Zb="tabster:root:blur",Gb=typeof CustomEvent<"u"?CustomEvent:function(){};class xr extends Gb{constructor(t,r){super(t,{bubbles:!0,cancelable:!0,composed:!0,detail:r}),this.details=r}}class ew extends xr{constructor(t){super(Vb,t)}}class tw extends xr{constructor(t){super(Kb,t)}}class Xn extends xr{constructor(t){super(Xb,t)}}class rw extends xr{constructor(t){super(Yb,t)}}class ow extends xr{constructor(t){super(Qb,t)}}class lh extends xr{constructor(){super(Iu)}}class nw extends xr{constructor(t){super(Jb,t)}}class iw extends xr{constructor(t){super(Zb,t)}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */const z={createMutationObserver:e=>new MutationObserver(e),createTreeWalker:(e,t,r,o)=>e.createTreeWalker(t,r,o),getParentNode:e=>e?e.parentNode:null,getParentElement:e=>e?e.parentElement:null,nodeContains:(e,t)=>!!(t&&(e==null?void 0:e.contains(t))),getActiveElement:e=>e.activeElement,querySelector:(e,t)=>e.querySelector(t),querySelectorAll:(e,t)=>Array.prototype.slice.call(e.querySelectorAll(t),0),getElementById:(e,t)=>e.getElementById(t),getFirstChild:e=>(e==null?void 0:e.firstChild)||null,getLastChild:e=>(e==null?void 0:e.lastChild)||null,getNextSibling:e=>(e==null?void 0:e.nextSibling)||null,getPreviousSibling:e=>(e==null?void 0:e.previousSibling)||null,getFirstElementChild:e=>(e==null?void 0:e.firstElementChild)||null,getLastElementChild:e=>(e==null?void 0:e.lastElementChild)||null,getNextElementSibling:e=>(e==null?void 0:e.nextElementSibling)||null,getPreviousElementSibling:e=>(e==null?void 0:e.previousElementSibling)||null,appendChild:(e,t)=>e.appendChild(t),insertBefore:(e,t,r)=>e.insertBefore(t,r),getSelection:e=>{var t;return((t=e.ownerDocument)===null||t===void 0?void 0:t.getSelection())||null}};function lw(e){for(const t of Object.keys(e))z[t]=e[t]}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */let Mu,sw=0;try{document.createTreeWalker(document,NodeFilter.SHOW_ELEMENT),Mu=!1}catch{Mu=!0}const ju=100;function Zr(e){const t=e();let r=t.__tabsterInstanceContext;return r||(r={elementByUId:{},basics:{Promise:t.Promise||void 0,WeakRef:t.WeakRef||void 0},containerBoundingRectCache:{},lastContainerBoundingRectCacheId:0,fakeWeakRefs:[],fakeWeakRefsStarted:!1},t.__tabsterInstanceContext=r),r}function aw(e){const t=e.__tabsterInstanceContext;t&&(t.elementByUId={},delete t.WeakRef,t.containerBoundingRectCache={},t.containerBoundingRectCacheTimer&&e.clearTimeout(t.containerBoundingRectCacheTimer),t.fakeWeakRefsTimer&&e.clearTimeout(t.fakeWeakRefsTimer),t.fakeWeakRefs=[],delete e.__tabsterInstanceContext)}function uw(e){const t=e.__tabsterInstanceContext;return new((t==null?void 0:t.basics.WeakMap)||WeakMap)}function cw(e){return!!e.querySelector(Au)}class sh{constructor(t){this._target=t}deref(){return this._target}static cleanup(t,r){return t._target?r||!Hu(t._target.ownerDocument,t._target)?(delete t._target,!0):!1:!0}}class Bt{constructor(t,r,o){const n=Zr(t);let i;n.WeakRef?i=new n.WeakRef(r):(i=new sh(r),n.fakeWeakRefs.push(i)),this._ref=i,this._data=o}get(){const t=this._ref;let r;return t&&(r=t.deref(),r||delete this._ref),r}getData(){return this._data}}function ah(e,t){const r=Zr(e);r.fakeWeakRefs=r.fakeWeakRefs.filter(o=>!sh.cleanup(o,t))}function uh(e){const t=Zr(e);t.fakeWeakRefsStarted||(t.fakeWeakRefsStarted=!0,t.WeakRef=vw(t)),t.fakeWeakRefsTimer||(t.fakeWeakRefsTimer=e().setTimeout(()=>{t.fakeWeakRefsTimer=void 0,ah(e),uh(e)},2*60*1e3))}function dw(e){const t=Zr(e);t.fakeWeakRefsStarted=!1,t.fakeWeakRefsTimer&&(e().clearTimeout(t.fakeWeakRefsTimer),t.fakeWeakRefsTimer=void 0,t.fakeWeakRefs=[])}function ch(e,t,r){if(t.nodeType!==Node.ELEMENT_NODE)return;const o=Mu?r:{acceptNode:r};return z.createTreeWalker(e,t,NodeFilter.SHOW_ELEMENT,o,!1)}function fw(e){e.__shouldIgnoreFocus=!0}function dh(e){return!!e.__shouldIgnoreFocus}function gw(e){const t=new Uint32Array(4);if(e.crypto&&e.crypto.getRandomValues)e.crypto.getRandomValues(t);else if(e.msCrypto&&e.msCrypto.getRandomValues)e.msCrypto.getRandomValues(t);else for(let o=0;o<t.length;o++)t[o]=4294967295*Math.random();const r=[];for(let o=0;o<t.length;o++)r.push(t[o].toString(36));return r.push("|"),r.push((++sw).toString(36)),r.push("|"),r.push(Date.now().toString(36)),r.join("")}function pw(e,t){const r=Zr(e);let o=t.__tabsterElementUID;return o||(o=t.__tabsterElementUID=gw(e())),!r.elementByUId[o]&&Hu(t.ownerDocument,t)&&(r.elementByUId[o]=new Bt(e,t)),o}function fh(e,t){const r=Zr(e);for(const o of Object.keys(r.elementByUId)){const n=r.elementByUId[o],i=n&&n.get();i&&t&&!z.nodeContains(t,i)||delete r.elementByUId[o]}}function Hu(e,t){return z.nodeContains(e==null?void 0:e.body,t)}function hw(e,t){const r=e.matches||e.matchesSelector||e.msMatchesSelector||e.webkitMatchesSelector;return r&&r.call(e,t)}function vw(e){return e.basics.WeakRef}let mw=0;class Wu{constructor(t,r,o){const n=t.getWindow;this._tabster=t,this._element=new Bt(n,r),this._props={...o},this.id="i"+ ++mw}getElement(){return this._element.get()}getProps(){return this._props}setProps(t){this._props={...t}}}class zl{constructor(t,r,o,n,i){var l;this._focusIn=d=>{if(this._fixedTarget){const g=this._fixedTarget.get();g&&Qr(g);return}const f=this.input;if(this.onFocusIn&&f){const g=d.relatedTarget;this.onFocusIn(this,this._isBackward(!0,f,g),g)}},this._focusOut=d=>{if(this._fixedTarget)return;this.useDefaultAction=!1;const f=this.input;if(this.onFocusOut&&f){const g=d.relatedTarget;this.onFocusOut(this,this._isBackward(!1,f,g),g)}};const s=t(),a=s.document.createElement("i");a.tabIndex=0,a.setAttribute("role","none"),a.setAttribute(oh,""),a.setAttribute("aria-hidden","true");const u=a.style;u.position="fixed",u.width=u.height="1px",u.opacity="0.001",u.zIndex="-1",u.setProperty("content-visibility","hidden"),fw(a),this.input=a,this.isFirst=o.isFirst,this.isOutside=r,this._isPhantom=(l=o.isPhantom)!==null&&l!==void 0?l:!1,this._fixedTarget=i,a.addEventListener("focusin",this._focusIn),a.addEventListener("focusout",this._focusOut),a.__tabsterDummyContainer=n,this._isPhantom&&(this._disposeTimer=s.setTimeout(()=>{delete this._disposeTimer,this.dispose()},0),this._clearDisposeTimeout=()=>{this._disposeTimer&&(s.clearTimeout(this._disposeTimer),delete this._disposeTimer),delete this._clearDisposeTimeout})}dispose(){var t;this._clearDisposeTimeout&&this._clearDisposeTimeout();const r=this.input;!r||(delete this._fixedTarget,delete this.onFocusIn,delete this.onFocusOut,delete this.input,r.removeEventListener("focusin",this._focusIn),r.removeEventListener("focusout",this._focusOut),delete r.__tabsterDummyContainer,(t=z.getParentNode(r))===null||t===void 0||t.removeChild(r))}setTopLeft(t,r){var o;const n=(o=this.input)===null||o===void 0?void 0:o.style;n&&(n.top=`${t}px`,n.left=`${r}px`)}_isBackward(t,r,o){return t&&!o?!this.isFirst:!!(o&&r.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING)}}const gh={Root:1,Modalizer:2,Mover:3,Groupper:4};class Al{constructor(t,r,o,n,i,l){this._element=r,this._instance=new kw(t,r,this,o,n,i,l)}_setHandlers(t,r){this._onFocusIn=t,this._onFocusOut=r}moveOut(t){var r;(r=this._instance)===null||r===void 0||r.moveOut(t)}moveOutWithDefaultAction(t,r){var o;(o=this._instance)===null||o===void 0||o.moveOutWithDefaultAction(t,r)}getHandler(t){return t?this._onFocusIn:this._onFocusOut}setTabbable(t){var r;(r=this._instance)===null||r===void 0||r.setTabbable(this,t)}dispose(){this._instance&&(this._instance.dispose(this),delete this._instance),delete this._onFocusIn,delete this._onFocusOut}static moveWithPhantomDummy(t,r,o,n,i){var l;const a=new zl(t.getWindow,!0,{isPhantom:!0,isFirst:!0}).input;if(a){let u,d;if(r.tagName==="BODY")u=r,d=o&&n||!o&&!n?z.getFirstElementChild(r):null;else{o&&(!n||n&&!t.focusable.isFocusable(r,!1,!0,!0))?(u=r,d=n?r.firstElementChild:null):(u=z.getParentElement(r),d=o&&n||!o&&!n?r:z.getNextElementSibling(r));let f,g;do f=o&&n||!o&&!n?z.getPreviousElementSibling(d):d,g=(l=f==null?void 0:f.__tabsterDummyContainer)===null||l===void 0?void 0:l.get(),g===r?d=o&&n||!o&&!n?f:z.getNextElementSibling(f):g=void 0;while(g)}u!=null&&u.dispatchEvent(new Xn({by:"root",owner:u,next:null,relatedEvent:i}))&&(z.insertBefore(u,a,d),Qr(a))}}static addPhantomDummyWithTarget(t,r,o,n){const l=new zl(t.getWindow,!0,{isPhantom:!0,isFirst:!0},void 0,new Bt(t.getWindow,n)).input;if(l){let s,a;cw(r)&&!o?(s=r,a=z.getFirstElementChild(r)):(s=z.getParentElement(r),a=o?r:z.getNextElementSibling(r)),s&&z.insertBefore(s,l,a)}}}class yw{constructor(t){this._updateQueue=new Set,this._lastUpdateQueueTime=0,this._changedParents=new WeakSet,this._dummyElements=[],this._dummyCallbacks=new WeakMap,this._domChanged=r=>{var o;this._changedParents.has(r)||(this._changedParents.add(r),!this._updateDummyInputsTimer&&(this._updateDummyInputsTimer=(o=this._win)===null||o===void 0?void 0:o.call(this).setTimeout(()=>{delete this._updateDummyInputsTimer;for(const n of this._dummyElements){const i=n.get();if(i){const l=this._dummyCallbacks.get(i);if(l){const s=z.getParentNode(i);(!s||this._changedParents.has(s))&&l()}}}this._changedParents=new WeakSet},ju)))},this._win=t}add(t,r){!this._dummyCallbacks.has(t)&&this._win&&(this._dummyElements.push(new Bt(this._win,t)),this._dummyCallbacks.set(t,r),this.domChanged=this._domChanged)}remove(t){this._dummyElements=this._dummyElements.filter(r=>{const o=r.get();return o&&o!==t}),this._dummyCallbacks.delete(t),this._dummyElements.length===0&&delete this.domChanged}dispose(){var t;const r=(t=this._win)===null||t===void 0?void 0:t.call(this);this._updateTimer&&(r==null||r.clearTimeout(this._updateTimer),delete this._updateTimer),this._updateDummyInputsTimer&&(r==null||r.clearTimeout(this._updateDummyInputsTimer),delete this._updateDummyInputsTimer),this._changedParents=new WeakSet,this._dummyCallbacks=new WeakMap,this._dummyElements=[],this._updateQueue.clear(),delete this.domChanged,delete this._win}updatePositions(t){!this._win||(this._updateQueue.add(t),this._lastUpdateQueueTime=Date.now(),this._scheduledUpdatePositions())}_scheduledUpdatePositions(){var t;this._updateTimer||(this._updateTimer=(t=this._win)===null||t===void 0?void 0:t.call(this).setTimeout(()=>{if(delete this._updateTimer,this._lastUpdateQueueTime+ju<=Date.now()){const r=new Map,o=[];for(const n of this._updateQueue)o.push(n(r));this._updateQueue.clear();for(const n of o)n();r.clear()}else this._scheduledUpdatePositions()},ju))}}class kw{constructor(t,r,o,n,i,l,s){this._wrappers=[],this._isOutside=!1,this._transformElements=new Set,this._onFocusIn=(y,v,k)=>{this._onFocus(!0,y,v,k)},this._onFocusOut=(y,v,k)=>{this._onFocus(!1,y,v,k)},this.moveOut=y=>{var v;const k=this._firstDummy,_=this._lastDummy;if(k&&_){this._ensurePosition();const m=k.input,h=_.input,c=(v=this._element)===null||v===void 0?void 0:v.get();if(m&&h&&c){let p;y?(m.tabIndex=0,p=m):(h.tabIndex=0,p=h),p&&Qr(p)}}},this.moveOutWithDefaultAction=(y,v)=>{var k;const _=this._firstDummy,m=this._lastDummy;if(_&&m){this._ensurePosition();const h=_.input,c=m.input,p=(k=this._element)===null||k===void 0?void 0:k.get();if(h&&c&&p){let b;y?!_.isOutside&&this._tabster.focusable.isFocusable(p,!0,!0,!0)?b=p:(_.useDefaultAction=!0,h.tabIndex=0,b=h):(m.useDefaultAction=!0,c.tabIndex=0,b=c),b&&p.dispatchEvent(new Xn({by:"root",owner:p,next:null,relatedEvent:v}))&&Qr(b)}}},this.setTabbable=(y,v)=>{var k,_;for(const h of this._wrappers)if(h.manager===y){h.tabbable=v;break}const m=this._getCurrent();if(m){const h=m.tabbable?0:-1;let c=(k=this._firstDummy)===null||k===void 0?void 0:k.input;c&&(c.tabIndex=h),c=(_=this._lastDummy)===null||_===void 0?void 0:_.input,c&&(c.tabIndex=h)}},this._addDummyInputs=()=>{this._addTimer||(this._addTimer=this._getWindow().setTimeout(()=>{delete this._addTimer,this._ensurePosition(),this._addTransformOffsets()},0))},this._addTransformOffsets=()=>{this._tabster._dummyObserver.updatePositions(this._computeTransformOffsets)},this._computeTransformOffsets=y=>{var v,k;const _=((v=this._firstDummy)===null||v===void 0?void 0:v.input)||((k=this._lastDummy)===null||k===void 0?void 0:k.input),m=this._transformElements,h=new Set;let c=0,p=0;const b=this._getWindow();for(let w=_;w&&w.nodeType===Node.ELEMENT_NODE;w=z.getParentElement(w)){let B=y.get(w);if(B===void 0){const E=b.getComputedStyle(w).transform;E&&E!=="none"&&(B={scrollTop:w.scrollTop,scrollLeft:w.scrollLeft}),y.set(w,B||null)}B&&(h.add(w),m.has(w)||w.addEventListener("scroll",this._addTransformOffsets),c+=B.scrollTop,p+=B.scrollLeft)}for(const w of m)h.has(w)||w.removeEventListener("scroll",this._addTransformOffsets);return this._transformElements=h,()=>{var w,B;(w=this._firstDummy)===null||w===void 0||w.setTopLeft(c,p),(B=this._lastDummy)===null||B===void 0||B.setTopLeft(c,p)}};const a=r.get();if(!a)throw new Error("No element");this._tabster=t,this._getWindow=t.getWindow,this._callForDefaultAction=s;const u=a.__tabsterDummy;if((u||this)._wrappers.push({manager:o,priority:n,tabbable:!0}),u)return u;a.__tabsterDummy=this;const d=i==null?void 0:i.dummyInputsPosition,f=a.tagName;this._isOutside=d?d===nh.Outside:(l||f==="UL"||f==="OL"||f==="TABLE")&&!(f==="LI"||f==="TD"||f==="TH"),this._firstDummy=new zl(this._getWindow,this._isOutside,{isFirst:!0},r),this._lastDummy=new zl(this._getWindow,this._isOutside,{isFirst:!1},r);const g=this._firstDummy.input;g&&t._dummyObserver.add(g,this._addDummyInputs),this._firstDummy.onFocusIn=this._onFocusIn,this._firstDummy.onFocusOut=this._onFocusOut,this._lastDummy.onFocusIn=this._onFocusIn,this._lastDummy.onFocusOut=this._onFocusOut,this._element=r,this._addDummyInputs()}dispose(t,r){var o,n,i,l;if((this._wrappers=this._wrappers.filter(a=>a.manager!==t&&!r)).length===0){delete((o=this._element)===null||o===void 0?void 0:o.get()).__tabsterDummy;for(const d of this._transformElements)d.removeEventListener("scroll",this._addTransformOffsets);this._transformElements.clear();const a=this._getWindow();this._addTimer&&(a.clearTimeout(this._addTimer),delete this._addTimer);const u=(n=this._firstDummy)===null||n===void 0?void 0:n.input;u&&this._tabster._dummyObserver.remove(u),(i=this._firstDummy)===null||i===void 0||i.dispose(),(l=this._lastDummy)===null||l===void 0||l.dispose()}}_onFocus(t,r,o,n){var i;const l=this._getCurrent();l&&(!r.useDefaultAction||this._callForDefaultAction)&&((i=l.manager.getHandler(t))===null||i===void 0||i(r,o,n))}_getCurrent(){return this._wrappers.sort((t,r)=>t.tabbable!==r.tabbable?t.tabbable?-1:1:t.priority-r.priority),this._wrappers[0]}_ensurePosition(){var t,r,o;const n=(t=this._element)===null||t===void 0?void 0:t.get(),i=(r=this._firstDummy)===null||r===void 0?void 0:r.input,l=(o=this._lastDummy)===null||o===void 0?void 0:o.input;if(!(!n||!i||!l))if(this._isOutside){const s=z.getParentNode(n);if(s){const a=z.getNextSibling(n);a!==l&&z.insertBefore(s,l,a),z.getPreviousElementSibling(n)!==i&&z.insertBefore(s,i,n)}}else{z.getLastElementChild(n)!==l&&z.appendChild(n,l);const s=z.getFirstElementChild(n);s&&s!==i&&s.parentNode&&z.insertBefore(s.parentNode,i,s)}}}function ph(e){let t=null;for(let r=z.getLastElementChild(e);r;r=z.getLastElementChild(r))t=r;return t||void 0}function $u(e,t,r,o){const n=e.storageEntry(t,!0);let i=!1;if(!n.aug){if(o===void 0)return i;n.aug={}}if(o===void 0){if(r in n.aug){const l=n.aug[r];delete n.aug[r],l===null?t.removeAttribute(r):t.setAttribute(r,l),i=!0}}else{let l;r in n.aug||(l=t.getAttribute(r)),l!==void 0&&l!==o&&(n.aug[r]=l,o===null?t.removeAttribute(r):t.setAttribute(r,o),i=!0)}return o===void 0&&Object.keys(n.aug).length===0&&(delete n.aug,e.storageEntry(t,!1)),i}function bw(e){var t,r;const o=e.ownerDocument,n=(t=o.defaultView)===null||t===void 0?void 0:t.getComputedStyle(e);return e.offsetParent===null&&o.body!==e&&(n==null?void 0:n.position)!=="fixed"||(n==null?void 0:n.visibility)==="hidden"||(n==null?void 0:n.position)==="fixed"&&(n.display==="none"||((r=e.parentElement)===null||r===void 0?void 0:r.offsetParent)===null&&o.body!==e.parentElement)}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */function hh(e,t){const r=JSON.stringify(e);return t===!0?r:{[Jt]:r}}function ww(e,t){for(const r of Object.keys(t)){const o=t[r];o?e[r]=o:delete e[r]}}function Bw(e,t,r){let o;if(r){const n=e.getAttribute(Jt);if(n)try{o=JSON.parse(n)}catch{}}o||(o={}),ww(o,t),Object.keys(o).length>0?e.setAttribute(Jt,hh(o,!0)):e.removeAttribute(Jt)}class vh extends Al{constructor(t,r,o,n){super(t,r,gh.Root,n,void 0,!0),this._onDummyInputFocus=i=>{var l;if(i.useDefaultAction)this._setFocused(!1);else{this._tabster.keyboardNavigation.setNavigatingWithKeyboard(!0);const s=this._element.get();if(s){this._setFocused(!0);const a=this._tabster.focusedElement.getFirstOrLastTabbable(i.isFirst,{container:s,ignoreAccessibility:!0});if(a){Qr(a);return}}(l=i.input)===null||l===void 0||l.blur()}},this._setHandlers(this._onDummyInputFocus),this._tabster=t,this._setFocused=o}}class _w extends Wu{constructor(t,r,o,n,i){super(t,r,n),this._isFocused=!1,this._setFocused=u=>{var d;if(this._setFocusedTimer&&(this._tabster.getWindow().clearTimeout(this._setFocusedTimer),delete this._setFocusedTimer),this._isFocused===u)return;const f=this._element.get();f&&(u?(this._isFocused=!0,(d=this._dummyManager)===null||d===void 0||d.setTabbable(!1),f.dispatchEvent(new nw({element:f}))):this._setFocusedTimer=this._tabster.getWindow().setTimeout(()=>{var g;delete this._setFocusedTimer,this._isFocused=!1,(g=this._dummyManager)===null||g===void 0||g.setTabbable(!0),f.dispatchEvent(new iw({element:f}))},0))},this._onFocusIn=u=>{const d=this._tabster.getParent,f=this._element.get();let g=u.composedPath()[0];do{if(g===f){this._setFocused(!0);return}g=g&&d(g)}while(g)},this._onFocusOut=()=>{this._setFocused(!1)},this._onDispose=o;const l=t.getWindow;this.uid=pw(l,r),this._sys=i,(t.controlTab||t.rootDummyInputs)&&this.addDummyInputs();const a=l().document;a.addEventListener(Qt,this._onFocusIn),a.addEventListener(Vn,this._onFocusOut),this._add()}addDummyInputs(){this._dummyManager||(this._dummyManager=new vh(this._tabster,this._element,this._setFocused,this._sys))}dispose(){var t;this._onDispose(this);const r=this._tabster.getWindow(),o=r.document;o.removeEventListener(Qt,this._onFocusIn),o.removeEventListener(Vn,this._onFocusOut),this._setFocusedTimer&&(r.clearTimeout(this._setFocusedTimer),delete this._setFocusedTimer),(t=this._dummyManager)===null||t===void 0||t.dispose(),this._remove()}moveOutWithDefaultAction(t,r){const o=this._dummyManager;if(o)o.moveOutWithDefaultAction(t,r);else{const n=this.getElement();n&&vh.moveWithPhantomDummy(this._tabster,n,!0,t,r)}}_add(){}_remove(){}}class xe{constructor(t,r){this._autoRootWaiting=!1,this._roots={},this._forceDummy=!1,this.rootById={},this._autoRootCreate=()=>{var o;const n=this._win().document,i=n.body;if(i){this._autoRootUnwait(n);const l=this._autoRoot;if(l)return Bw(i,{root:l},!0),ih(this._tabster,i),(o=Ot(this._tabster,i))===null||o===void 0?void 0:o.root}else this._autoRootWaiting||(this._autoRootWaiting=!0,n.addEventListener("readystatechange",this._autoRootCreate))},this._onRootDispose=o=>{delete this._roots[o.id]},this._tabster=t,this._win=t.getWindow,this._autoRoot=r,t.queueInit(()=>{this._autoRoot&&this._autoRootCreate()})}_autoRootUnwait(t){t.removeEventListener("readystatechange",this._autoRootCreate),this._autoRootWaiting=!1}dispose(){const t=this._win();this._autoRootUnwait(t.document),delete this._autoRoot,Object.keys(this._roots).forEach(r=>{this._roots[r]&&(this._roots[r].dispose(),delete this._roots[r])}),this.rootById={}}createRoot(t,r,o){const n=new _w(this._tabster,t,this._onRootDispose,r,o);return this._roots[n.id]=n,this._forceDummy&&n.addDummyInputs(),n}addDummyInputs(){this._forceDummy=!0;const t=this._roots;for(const r of Object.keys(t))t[r].addDummyInputs()}static getRootByUId(t,r){const o=t().__tabsterInstance;return o&&o.root.rootById[r]}static getTabsterContext(t,r,o){o===void 0&&(o={});var n,i,l,s;if(!r.ownerDocument)return;const{checkRtl:a,referenceElement:u}=o,d=t.getParent;t.drainInitQueue();let f,g,y,v,k=!1,_,m,h,c,p=u||r;const b={};for(;p&&(!f||a);){const B=Ot(t,p);if(a&&h===void 0){const I=p.dir;I&&(h=I.toLowerCase()==="rtl")}if(!B){p=d(p);continue}const E=p.tagName;(B.uncontrolled||E==="IFRAME"||E==="WEBVIEW")&&(c=p),!v&&((n=B.focusable)===null||n===void 0?void 0:n.excludeFromMover)&&!y&&(k=!0);const O=B.modalizer,F=B.groupper,A=B.mover;!g&&O&&(g=O),!y&&F&&(!g||O)&&(g?(!F.isActive()&&F.getProps().tabbability&&g.userId!==((i=t.modalizer)===null||i===void 0?void 0:i.activeId)&&(g=void 0,y=F),m=F):y=F),!v&&A&&(!g||O)&&(!F||p!==r)&&(v=A,_=!!y&&y!==F),B.root&&(f=B.root),!((l=B.focusable)===null||l===void 0)&&l.ignoreKeydown&&Object.assign(b,B.focusable.ignoreKeydown),p=d(p)}if(!f){const B=t.root;B._autoRoot&&!((s=r.ownerDocument)===null||s===void 0)&&s.body&&(f=B._autoRootCreate())}return y&&!v&&(_=!0),f?{root:f,modalizer:g,groupper:y,mover:v,groupperBeforeMover:_,modalizerInGroupper:m,rtl:a?!!h:void 0,uncontrolled:c,excludedFromMover:k,ignoreKeydown:B=>!!b[B.key]}:void 0}static getRoot(t,r){var o;const n=t.getParent;for(let i=r;i;i=n(i)){const l=(o=Ot(t,i))===null||o===void 0?void 0:o.root;if(l)return l}}onRoot(t,r){r?delete this.rootById[t.uid]:this.rootById[t.uid]=t}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */class mh{constructor(){this._callbacks=[]}dispose(){this._callbacks=[],delete this._val}subscribe(t){const r=this._callbacks;r.indexOf(t)<0&&r.push(t)}subscribeFirst(t){const r=this._callbacks,o=r.indexOf(t);o>=0&&r.splice(o,1),r.unshift(t)}unsubscribe(t){const r=this._callbacks.indexOf(t);r>=0&&this._callbacks.splice(r,1)}setVal(t,r){this._val!==t&&(this._val=t,this._callCallbacks(t,r))}getVal(){return this._val}trigger(t,r){this._callCallbacks(t,r)}_callCallbacks(t,r){this._callbacks.forEach(o=>o(t,r))}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */class xw{constructor(t){this._tabster=t}dispose(){}getProps(t){const r=Ot(this._tabster,t);return r&&r.focusable||{}}isFocusable(t,r,o,n){return hw(t,Au)&&(r||t.tabIndex!==-1)?(o||this.isVisible(t))&&(n||this.isAccessible(t)):!1}isVisible(t){if(!t.ownerDocument||t.nodeType!==Node.ELEMENT_NODE||bw(t))return!1;const r=t.ownerDocument.body.getBoundingClientRect();return!(r.width===0&&r.height===0)}isAccessible(t){var r;for(let o=t;o;o=z.getParentElement(o)){const n=Ot(this._tabster,o);if(this._isHidden(o)||!((r=n==null?void 0:n.focusable)===null||r===void 0?void 0:r.ignoreAriaDisabled)&&this._isDisabled(o))return!1}return!0}_isDisabled(t){return t.hasAttribute("disabled")}_isHidden(t){var r;const o=t.getAttribute("aria-hidden");return!!(o&&o.toLowerCase()==="true"&&!(!((r=this._tabster.modalizer)===null||r===void 0)&&r.isAugmented(t)))}findFirst(t,r){return this.findElement({...t},r)}findLast(t,r){return this.findElement({isBackward:!0,...t},r)}findNext(t,r){return this.findElement({...t},r)}findPrev(t,r){return this.findElement({...t,isBackward:!0},r)}findDefault(t,r){return this.findElement({...t,acceptCondition:o=>this.isFocusable(o,t.includeProgrammaticallyFocusable)&&!!this.getProps(o).isDefault},r)||null}findAll(t){return this._findElements(!0,t)||[]}findElement(t,r){const o=this._findElements(!1,t,r);return o&&o[0]}_findElements(t,r,o){var n,i,l;const{container:s,currentElement:a=null,includeProgrammaticallyFocusable:u,useActiveModalizer:d,ignoreAccessibility:f,modalizerId:g,isBackward:y,onElement:v}=r;o||(o={});const k=[];let{acceptCondition:_}=r;const m=!!_;if(!s)return null;_||(_=b=>this.isFocusable(b,u,!1,f));const h={container:s,modalizerUserId:g===void 0&&d?(n=this._tabster.modalizer)===null||n===void 0?void 0:n.activeId:g||((l=(i=xe.getTabsterContext(this._tabster,s))===null||i===void 0?void 0:i.modalizer)===null||l===void 0?void 0:l.userId),from:a||s,isBackward:y,acceptCondition:_,hasCustomCondition:m,includeProgrammaticallyFocusable:u,ignoreAccessibility:f,cachedGrouppers:{}},c=ch(s.ownerDocument,s,b=>this._acceptElement(b,h));if(!c)return null;const p=b=>{var w,B;const E=(w=h.foundElement)!==null&&w!==void 0?w:h.foundBackward;return E&&k.push(E),t?E&&(h.found=!1,delete h.foundElement,delete h.foundBackward,delete h.fromCtx,h.from=E,v&&!v(E))?!1:!!(E||b):(E&&o&&(o.uncontrolled=(B=xe.getTabsterContext(this._tabster,E))===null||B===void 0?void 0:B.uncontrolled),!!(b&&!E))};if(a||(o.outOfDOMOrder=!0),a&&z.nodeContains(s,a))c.currentNode=a;else if(y){const b=ph(s);if(!b)return null;if(this._acceptElement(b,h)===NodeFilter.FILTER_ACCEPT&&!p(!0))return h.skippedFocusable&&(o.outOfDOMOrder=!0),k;c.currentNode=b}do y?c.previousNode():c.nextNode();while(p());return h.skippedFocusable&&(o.outOfDOMOrder=!0),k.length?k:null}_acceptElement(t,r){var o,n,i;if(r.found)return NodeFilter.FILTER_ACCEPT;const l=r.foundBackward;if(l&&(t===l||!z.nodeContains(l,t)))return r.found=!0,r.foundElement=l,NodeFilter.FILTER_ACCEPT;const s=r.container;if(t===s)return NodeFilter.FILTER_SKIP;if(!z.nodeContains(s,t)||t.__tabsterDummyContainer||z.nodeContains(r.rejectElementsFrom,t))return NodeFilter.FILTER_REJECT;const a=r.currentCtx=xe.getTabsterContext(this._tabster,t);if(!a)return NodeFilter.FILTER_SKIP;if(dh(t))return this.isFocusable(t,void 0,!0,!0)&&(r.skippedFocusable=!0),NodeFilter.FILTER_SKIP;if(!r.hasCustomCondition&&(t.tagName==="IFRAME"||t.tagName==="WEBVIEW"))return((o=a.modalizer)===null||o===void 0?void 0:o.userId)===((n=this._tabster.modalizer)===null||n===void 0?void 0:n.activeId)?(r.found=!0,r.rejectElementsFrom=r.foundElement=t,NodeFilter.FILTER_ACCEPT):NodeFilter.FILTER_REJECT;if(!r.ignoreAccessibility&&!this.isAccessible(t))return this.isFocusable(t,!1,!0,!0)&&(r.skippedFocusable=!0),NodeFilter.FILTER_REJECT;let u,d=r.fromCtx;d||(d=r.fromCtx=xe.getTabsterContext(this._tabster,r.from));const f=d==null?void 0:d.mover;let g=a.groupper,y=a.mover;if(u=(i=this._tabster.modalizer)===null||i===void 0?void 0:i.acceptElement(t,r),u!==void 0&&(r.skippedFocusable=!0),u===void 0&&(g||y||f)){const v=g==null?void 0:g.getElement(),k=f==null?void 0:f.getElement();let _=y==null?void 0:y.getElement();_&&z.nodeContains(k,_)&&z.nodeContains(s,k)&&(!v||!y||z.nodeContains(k,v))&&(y=f,_=k),v&&(v===s||!z.nodeContains(s,v))&&(g=void 0),_&&!z.nodeContains(s,_)&&(y=void 0),g&&y&&(_&&v&&!z.nodeContains(v,_)?y=void 0:g=void 0),g&&(u=g.acceptElement(t,r)),y&&(u=y.acceptElement(t,r))}return u===void 0&&(u=r.acceptCondition(t)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP,u===NodeFilter.FILTER_SKIP&&this.isFocusable(t,!1,!0,!0)&&(r.skippedFocusable=!0)),u===NodeFilter.FILTER_ACCEPT&&!r.found&&(r.isBackward?(r.foundBackward=t,u=NodeFilter.FILTER_SKIP):(r.found=!0,r.foundElement=t)),u}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */const yh={Tab:"Tab",Enter:"Enter",Escape:"Escape",Space:" ",PageUp:"PageUp",PageDown:"PageDown",End:"End",Home:"Home",ArrowLeft:"ArrowLeft",ArrowUp:"ArrowUp",ArrowRight:"ArrowRight",ArrowDown:"ArrowDown"};/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */function Sw(e,t){var r;const o=e.getParent;let n=t;do{const i=(r=Ot(e,n))===null||r===void 0?void 0:r.uncontrolled;if(i&&e.uncontrolled.isUncontrolledCompletely(n,!!i.completely))return n;n=o(n)}while(n)}const kh={[Jr.Restorer]:0,[Jr.Deloser]:1,[Jr.EscapeGroupper]:2};class me extends mh{constructor(t,r){super(),this._init=()=>{const o=this._win(),n=o.document;n.addEventListener(Qt,this._onFocusIn,!0),n.addEventListener(Vn,this._onFocusOut,!0),o.addEventListener("keydown",this._onKeyDown,!0);const i=z.getActiveElement(n);i&&i!==n.body&&this._setFocusedElement(i),this.subscribe(this._onChanged)},this._onFocusIn=o=>{const n=o.composedPath()[0];n&&this._setFocusedElement(n,o.detail.relatedTarget,o.detail.isFocusedProgrammatically)},this._onFocusOut=o=>{var n;this._setFocusedElement(void 0,(n=o.detail)===null||n===void 0?void 0:n.originalEvent.relatedTarget)},this._validateFocusedElement=o=>{},this._onKeyDown=o=>{if(o.key!==yh.Tab||o.ctrlKey)return;const n=this.getVal();if(!n||!n.ownerDocument||n.contentEditable==="true")return;const i=this._tabster,l=i.controlTab,s=xe.getTabsterContext(i,n);if(!s||s.ignoreKeydown(o))return;const a=o.shiftKey,u=me.findNextTabbable(i,s,void 0,n,void 0,a,!0),d=s.root.getElement();if(!d)return;const f=u==null?void 0:u.element,g=Sw(i,n);if(f){const y=u.uncontrolled;if(s.uncontrolled||z.nodeContains(y,n)){if(!u.outOfDOMOrder&&y===s.uncontrolled||g&&!z.nodeContains(g,f))return;Al.addPhantomDummyWithTarget(i,n,a,f);return}if(y||f.tagName==="IFRAME"){d.dispatchEvent(new Xn({by:"root",owner:d,next:f,relatedEvent:o}))&&Al.moveWithPhantomDummy(this._tabster,y!=null?y:f,!1,a,o);return}(l||(u==null?void 0:u.outOfDOMOrder))&&d.dispatchEvent(new Xn({by:"root",owner:d,next:f,relatedEvent:o}))&&(o.preventDefault(),o.stopImmediatePropagation(),Qr(f))}else!g&&d.dispatchEvent(new Xn({by:"root",owner:d,next:null,relatedEvent:o}))&&s.root.moveOutWithDefaultAction(a,o)},this._onChanged=(o,n)=>{var i,l;if(o)o.dispatchEvent(new ew(n));else{const s=(i=this._lastVal)===null||i===void 0?void 0:i.get();if(s){const a={...n},u=xe.getTabsterContext(this._tabster,s),d=(l=u==null?void 0:u.modalizer)===null||l===void 0?void 0:l.userId;d&&(a.modalizerId=d),s.dispatchEvent(new tw(a))}}},this._tabster=t,this._win=r,t.queueInit(this._init)}dispose(){super.dispose();const t=this._win(),r=t.document;r.removeEventListener(Qt,this._onFocusIn,!0),r.removeEventListener(Vn,this._onFocusOut,!0),t.removeEventListener("keydown",this._onKeyDown,!0),this.unsubscribe(this._onChanged);const o=this._asyncFocus;o&&(t.clearTimeout(o.timeout),delete this._asyncFocus),delete me._lastResetElement,delete this._nextVal,delete this._lastVal}static forgetMemorized(t,r){var o,n;let i=me._lastResetElement,l=i&&i.get();l&&z.nodeContains(r,l)&&delete me._lastResetElement,l=(n=(o=t._nextVal)===null||o===void 0?void 0:o.element)===null||n===void 0?void 0:n.get(),l&&z.nodeContains(r,l)&&delete t._nextVal,i=t._lastVal,l=i&&i.get(),l&&z.nodeContains(r,l)&&delete t._lastVal}getFocusedElement(){return this.getVal()}getLastFocusedElement(){var t;let r=(t=this._lastVal)===null||t===void 0?void 0:t.get();return(!r||r&&!Hu(r.ownerDocument,r))&&(this._lastVal=r=void 0),r}focus(t,r,o){return this._tabster.focusable.isFocusable(t,r,!1,o)?(t.focus(),!0):!1}focusDefault(t){const r=this._tabster.focusable.findDefault({container:t});return r?(this._tabster.focusedElement.focus(r),!0):!1}getFirstOrLastTabbable(t,r){var o;const{container:n,ignoreAccessibility:i}=r;let l;if(n){const s=xe.getTabsterContext(this._tabster,n);s&&(l=(o=me.findNextTabbable(this._tabster,s,n,void 0,void 0,!t,i))===null||o===void 0?void 0:o.element)}return l&&!z.nodeContains(n,l)&&(l=void 0),l||void 0}_focusFirstOrLast(t,r){const o=this.getFirstOrLastTabbable(t,r);return o?(this.focus(o,!1,!0),!0):!1}focusFirst(t){return this._focusFirstOrLast(!0,t)}focusLast(t){return this._focusFirstOrLast(!1,t)}resetFocus(t){if(!this._tabster.focusable.isVisible(t))return!1;if(this._tabster.focusable.isFocusable(t,!0,!0,!0))this.focus(t);else{const r=t.getAttribute("tabindex"),o=t.getAttribute("aria-hidden");t.tabIndex=-1,t.setAttribute("aria-hidden","true"),me._lastResetElement=new Bt(this._win,t),this.focus(t,!0,!0),this._setOrRemoveAttribute(t,"tabindex",r),this._setOrRemoveAttribute(t,"aria-hidden",o)}return!0}requestAsyncFocus(t,r,o){const n=this._tabster.getWindow(),i=this._asyncFocus;if(i){if(kh[t]>kh[i.source])return;n.clearTimeout(i.timeout)}this._asyncFocus={source:t,callback:r,timeout:n.setTimeout(()=>{this._asyncFocus=void 0,r()},o)}}cancelAsyncFocus(t){const r=this._asyncFocus;(r==null?void 0:r.source)===t&&(this._tabster.getWindow().clearTimeout(r.timeout),this._asyncFocus=void 0)}_setOrRemoveAttribute(t,r,o){o===null?t.removeAttribute(r):t.setAttribute(r,o)}_setFocusedElement(t,r,o){var n,i;if(this._tabster._noop)return;const l={relatedTarget:r};if(t){const a=(n=me._lastResetElement)===null||n===void 0?void 0:n.get();if(me._lastResetElement=void 0,a===t||dh(t))return;l.isFocusedProgrammatically=o;const u=xe.getTabsterContext(this._tabster,t),d=(i=u==null?void 0:u.modalizer)===null||i===void 0?void 0:i.userId;d&&(l.modalizerId=d)}const s=this._nextVal={element:t?new Bt(this._win,t):void 0,detail:l};t&&t!==this._val&&this._validateFocusedElement(t),this._nextVal===s&&this.setVal(t,l),this._nextVal=void 0}setVal(t,r){super.setVal(t,r),t&&(this._lastVal=new Bt(this._win,t))}static findNextTabbable(t,r,o,n,i,l,s){const a=o||r.root.getElement();if(!a)return null;let u=null;const d=me._isTabbingTimer,f=t.getWindow();d&&f.clearTimeout(d),me.isTabbing=!0,me._isTabbingTimer=f.setTimeout(()=>{delete me._isTabbingTimer,me.isTabbing=!1},0);const g=r.modalizer,y=r.groupper,v=r.mover,k=_=>{if(u=_.findNextTabbable(n,i,l,s),n&&!(u!=null&&u.element)){const m=_!==g&&z.getParentElement(_.getElement());if(m){const h=xe.getTabsterContext(t,n,{referenceElement:m});if(h){const c=_.getElement(),p=l?c:c&&ph(c)||c;p&&(u=me.findNextTabbable(t,h,o,p,m,l,s),u&&(u.outOfDOMOrder=!0))}}}};if(y&&v)k(r.groupperBeforeMover?y:v);else if(y)k(y);else if(v)k(v);else if(g)k(g);else{const _={container:a,currentElement:n,referenceElement:i,ignoreAccessibility:s,useActiveModalizer:!0},m={};u={element:t.focusable[l?"findPrev":"findNext"](_,m),outOfDOMOrder:m.outOfDOMOrder,uncontrolled:m.uncontrolled}}return u}}me.isTabbing=!1;/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */class Ew extends mh{constructor(t){super(),this._onChange=r=>{this.setVal(r,void 0)},this._keyborg=th(t()),this._keyborg.subscribe(this._onChange)}dispose(){super.dispose(),this._keyborg&&(this._keyborg.unsubscribe(this._onChange),rh(this._keyborg),delete this._keyborg)}setNavigatingWithKeyboard(t){var r;(r=this._keyborg)===null||r===void 0||r.setVal(t)}isNavigatingWithKeyboard(){var t;return!!(!((t=this._keyborg)===null||t===void 0)&&t.isNavigatingWithKeyboard())}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */let Nw=0;const qu="aria-hidden";class Fw extends Al{constructor(t,r,o){super(r,t,gh.Modalizer,o),this._setHandlers((n,i)=>{var l,s,a;const u=t.get(),d=u&&((l=xe.getRoot(r,u))===null||l===void 0?void 0:l.getElement()),f=n.input;let g;if(d&&f){const y=(s=f.__tabsterDummyContainer)===null||s===void 0?void 0:s.get(),v=xe.getTabsterContext(r,y||f);v&&(g=(a=me.findNextTabbable(r,v,d,f,void 0,i,!0))===null||a===void 0?void 0:a.element),g&&Qr(g)}})}}class Cw extends Wu{constructor(t,r,o,n,i,l){super(t,r,n),this._wasFocused=0,this.userId=n.id,this._onDispose=o,this._activeElements=l,t.controlTab||(this.dummyManager=new Fw(this._element,t,i))}makeActive(t){if(this._isActive!==t){this._isActive=t;const r=this.getElement();if(r){const o=this._activeElements,n=o.map(i=>i.get()).indexOf(r);t?n<0&&o.push(new Bt(this._tabster.getWindow,r)):n>=0&&o.splice(n,1)}this._dispatchEvent(t)}}focused(t){return t||(this._wasFocused=++Nw),this._wasFocused}setProps(t){t.id&&(this.userId=t.id),this._props={...t}}dispose(){var t;this.makeActive(!1),this._onDispose(this),(t=this.dummyManager)===null||t===void 0||t.dispose(),delete this.dummyManager,this._activeElements=[],this._remove()}isActive(){return!!this._isActive}contains(t){return z.nodeContains(this.getElement(),t)}findNextTabbable(t,r,o,n){var i,l;if(!this.getElement())return null;const a=this._tabster;let u=null,d=!1,f;const g=t&&((i=xe.getRoot(a,t))===null||i===void 0?void 0:i.getElement());if(g){const y={container:g,currentElement:t,referenceElement:r,ignoreAccessibility:n,useActiveModalizer:!0},v={};u=a.focusable[o?"findPrev":"findNext"](y,v),!u&&this._props.isTrapped&&((l=a.modalizer)===null||l===void 0?void 0:l.activeId)?(u=a.focusable[o?"findLast":"findFirst"]({container:g,ignoreAccessibility:n,useActiveModalizer:!0},v),d=!0):d=!!v.outOfDOMOrder,f=v.uncontrolled}return{element:u,uncontrolled:f,outOfDOMOrder:d}}_dispatchEvent(t,r){const o=this.getElement();let n=!1;if(o){const i=r?this._activeElements.map(l=>l.get()):[o];for(const l of i)if(l){const s={id:this.userId,element:o},a=t?new rw(s):new ow(s);l.dispatchEvent(a),a.defaultPrevented&&(n=!0)}}return n}_remove(){}}class Pw{constructor(t,r,o){this._onModalizerDispose=i=>{const l=i.id,s=i.userId,a=this._parts[s];delete this._modalizers[l],a&&(delete a[l],Object.keys(a).length===0&&(delete this._parts[s],this.activeId===s&&this.setActive(void 0)))},this._onKeyDown=i=>{var l;if(i.key!==yh.Escape)return;const s=this._tabster,a=s.focusedElement.getFocusedElement();if(a){const u=xe.getTabsterContext(s,a),d=u==null?void 0:u.modalizer;if(u&&!u.groupper&&(d==null?void 0:d.isActive())&&!u.ignoreKeydown(i)){const f=d.userId;if(f){const g=this._parts[f];if(g){const y=Object.keys(g).map(v=>{var k;const _=g[v],m=_.getElement();let h;return m&&(h=(k=Ot(this._tabster,m))===null||k===void 0?void 0:k.groupper),_&&m&&h?{el:m,focusedSince:_.focused(!0)}:{focusedSince:0}}).filter(v=>v.focusedSince>0).sort((v,k)=>v.focusedSince>k.focusedSince?-1:v.focusedSince<k.focusedSince?1:0);if(y.length){const v=y[0].el;v&&((l=s.groupper)===null||l===void 0||l.handleKeyPress(v,i,!0))}}}}}},this._onFocus=(i,l)=>{var s,a;const u=i&&xe.getTabsterContext(this._tabster,i);if(!u||!i)return;const d=this._augMap;for(let g=i;g;g=z.getParentElement(g))d.has(g)&&(d.delete(g),$u(this._tabster,g,qu));const f=u.modalizer;if((a=f||((s=Ot(this._tabster,i))===null||s===void 0?void 0:s.modalizer))===null||a===void 0||a.focused(),(f==null?void 0:f.userId)===this.activeId){this.currentIsOthersAccessible=f==null?void 0:f.getProps().isOthersAccessible;return}if(l.isFocusedProgrammatically||this.currentIsOthersAccessible||(f==null?void 0:f.getProps().isAlwaysAccessible))this.setActive(f);else{const g=this._win();g.clearTimeout(this._restoreModalizerFocusTimer),this._restoreModalizerFocusTimer=g.setTimeout(()=>this._restoreModalizerFocus(i),100)}},this._tabster=t,this._win=t.getWindow,this._modalizers={},this._parts={},this._augMap=new WeakMap,this._aug=[],this._alwaysAccessibleSelector=r,this._accessibleCheck=o,this.activeElements=[],t.controlTab||t.root.addDummyInputs(),this._win().addEventListener("keydown",this._onKeyDown,!0),t.queueInit(()=>{this._tabster.focusedElement.subscribe(this._onFocus)})}dispose(){const t=this._win();t.removeEventListener("keydown",this._onKeyDown,!0),Object.keys(this._modalizers).forEach(r=>{this._modalizers[r]&&(this._modalizers[r].dispose(),delete this._modalizers[r])}),t.clearTimeout(this._restoreModalizerFocusTimer),t.clearTimeout(this._hiddenUpdateTimer),this._parts={},delete this.activeId,this.activeElements=[],this._augMap=new WeakMap,this._aug=[],this._tabster.focusedElement.unsubscribe(this._onFocus)}createModalizer(t,r,o){var n;const i=new Cw(this._tabster,t,this._onModalizerDispose,r,o,this.activeElements),l=i.id,s=r.id;this._modalizers[l]=i;let a=this._parts[s];return a||(a=this._parts[s]={}),a[l]=i,z.nodeContains(t,(n=this._tabster.focusedElement.getFocusedElement())!==null&&n!==void 0?n:null)&&(s!==this.activeId?this.setActive(i):i.makeActive(!0)),i}isAugmented(t){return this._augMap.has(t)}hiddenUpdate(){this._hiddenUpdateTimer||(this._hiddenUpdateTimer=this._win().setTimeout(()=>{delete this._hiddenUpdateTimer,this._hiddenUpdate()},250))}setActive(t){const r=t==null?void 0:t.userId,o=this.activeId;if(o!==r){if(this.activeId=r,o){const n=this._parts[o];if(n)for(const i of Object.keys(n))n[i].makeActive(!1)}if(r){const n=this._parts[r];if(n)for(const i of Object.keys(n))n[i].makeActive(!0)}this.currentIsOthersAccessible=t==null?void 0:t.getProps().isOthersAccessible,this.hiddenUpdate()}}focus(t,r,o){const n=xe.getTabsterContext(this._tabster,t),i=n==null?void 0:n.modalizer;if(i){this.setActive(i);const l=i.getProps(),s=i.getElement();if(s){if(r===void 0&&(r=l.isNoFocusFirst),!r&&this._tabster.keyboardNavigation.isNavigatingWithKeyboard()&&this._tabster.focusedElement.focusFirst({container:s})||(o===void 0&&(o=l.isNoFocusDefault),!o&&this._tabster.focusedElement.focusDefault(s)))return!0;this._tabster.focusedElement.resetFocus(s)}}return!1}acceptElement(t,r){var o;const n=r.modalizerUserId,i=(o=r.currentCtx)===null||o===void 0?void 0:o.modalizer;if(n)for(const s of this.activeElements){const a=s.get();if(a&&(z.nodeContains(t,a)||a===t))return NodeFilter.FILTER_SKIP}const l=n===(i==null?void 0:i.userId)||!n&&(i==null?void 0:i.getProps().isAlwaysAccessible)?void 0:NodeFilter.FILTER_SKIP;return l!==void 0&&(r.skippedFocusable=!0),l}_hiddenUpdate(){var t;const r=this._tabster,o=r.getWindow().document.body,n=this.activeId,i=this._parts,l=[],s=[],a=this._alwaysAccessibleSelector,u=a?Array.from(z.querySelectorAll(o,a)):[],d=[];for(const m of Object.keys(i)){const h=i[m];for(const c of Object.keys(h)){const p=h[c],b=p.getElement(),B=p.getProps().isAlwaysAccessible;b&&(m===n?(d.push(b),this.currentIsOthersAccessible||l.push(b)):B?u.push(b):s.push(b))}}const f=this._augMap,g=l.length>0?[...l,...u]:void 0,y=[],v=new WeakMap,k=(m,h)=>{var c;const p=m.tagName;if(p==="SCRIPT"||p==="STYLE")return;let b=!1;f.has(m)?h?b=!0:(f.delete(m),$u(r,m,qu)):h&&!(!((c=this._accessibleCheck)===null||c===void 0)&&c.call(this,m,d))&&$u(r,m,qu,"true")&&(f.set(m,!0),b=!0),b&&(y.push(new Bt(r.getWindow,m)),v.set(m,!0))},_=m=>{var h;for(let c=z.getFirstElementChild(m);c;c=z.getNextElementSibling(c)){let p=!1,b=!1,w=!1;if(g){const B=r.getParent(c);for(const E of g){if(c===E){p=!0;break}if(z.nodeContains(c,E)){b=!0;break}else z.nodeContains(E,B)&&(w=!0)}b||((h=c.__tabsterElementFlags)===null||h===void 0?void 0:h.noDirectAriaHidden)?_(c):!p&&!w&&k(c,!0)}else k(c,!1)}};g||u.forEach(m=>k(m,!1)),s.forEach(m=>k(m,!0)),o&&_(o),(t=this._aug)===null||t===void 0||t.map(m=>m.get()).forEach(m=>{m&&!v.get(m)&&k(m,!1)}),this._aug=y,this._augMap=v}_restoreModalizerFocus(t){const r=t==null?void 0:t.ownerDocument;if(!t||!r)return;const o=xe.getTabsterContext(this._tabster,t),n=o==null?void 0:o.modalizer,i=this.activeId;if(!n&&!i||n&&i===n.userId)return;const l=o==null?void 0:o.root.getElement();if(l){let s=this._tabster.focusable.findFirst({container:l,useActiveModalizer:!0});if(s){if(t.compareDocumentPosition(s)&document.DOCUMENT_POSITION_PRECEDING&&(s=this._tabster.focusable.findLast({container:l,useActiveModalizer:!0}),!s))throw new Error("Something went wrong.");this._tabster.focusedElement.focus(s);return}}t.blur()}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */function Tw(e,t,r,o){if(typeof MutationObserver>"u")return()=>{};const n=t.getWindow;let i;const l=d=>{var f,g,y,v,k;const _=new Set;for(const m of d){const h=m.target,c=m.removedNodes,p=m.addedNodes;if(m.type==="attributes")m.attributeName===Jt&&(_.has(h)||r(t,h));else{for(let b=0;b<c.length;b++){const w=c[b];_.add(w),s(w,!0),(g=(f=t._dummyObserver).domChanged)===null||g===void 0||g.call(f,h)}for(let b=0;b<p.length;b++)s(p[b]),(v=(y=t._dummyObserver).domChanged)===null||v===void 0||v.call(y,h)}}_.clear(),(k=t.modalizer)===null||k===void 0||k.hiddenUpdate()};function s(d,f){i||(i=Zr(n).elementByUId),a(d,f);const g=ch(e,d,y=>a(y,f));if(g)for(;g.nextNode(););}function a(d,f){var g;if(!d.getAttribute)return NodeFilter.FILTER_SKIP;const y=d.__tabsterElementUID;return y&&i&&(f?delete i[y]:(g=i[y])!==null&&g!==void 0||(i[y]=new Bt(n,d))),(Ot(t,d)||d.hasAttribute(Jt))&&r(t,d,f),NodeFilter.FILTER_SKIP}const u=z.createMutationObserver(l);return o&&s(n().document.body),u.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:[Jt]}),()=>{u.disconnect()}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */class Dw{constructor(t){this._isUncontrolledCompletely=t}isUncontrolledCompletely(t,r){var o;const n=(o=this._isUncontrolledCompletely)===null||o===void 0?void 0:o.call(this,t,r);return n===void 0?r:n}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */const Ow=10;class Rw extends Wu{constructor(t,r,o){var n;if(super(t,r,o),this._hasFocus=!1,this._onFocusOut=i=>{var l;const s=(l=this._element)===null||l===void 0?void 0:l.get();s&&i.relatedTarget===null&&s.dispatchEvent(new lh),s&&!z.nodeContains(s,i.relatedTarget)&&(this._hasFocus=!1)},this._onFocusIn=()=>{this._hasFocus=!0},this._props.type===Kn.Source){const i=(n=this._element)===null||n===void 0?void 0:n.get();i==null||i.addEventListener("focusout",this._onFocusOut),i==null||i.addEventListener("focusin",this._onFocusIn),this._hasFocus=z.nodeContains(i,i&&z.getActiveElement(i.ownerDocument))}}dispose(){var t;if(this._props.type===Kn.Source){const r=(t=this._element)===null||t===void 0?void 0:t.get();r==null||r.removeEventListener("focusout",this._onFocusOut),r==null||r.removeEventListener("focusin",this._onFocusIn),this._hasFocus&&this._tabster.getWindow().document.body.dispatchEvent(new lh)}}}class zw{constructor(t){this._history=[],this._onRestoreFocus=r=>{this._focusedElementState.cancelAsyncFocus(Jr.Restorer);const o=r.composedPath()[0];o&&this._focusedElementState.requestAsyncFocus(Jr.Restorer,()=>this._restoreFocus(o),0)},this._onFocusIn=r=>{var o;if(!r)return;const n=Ot(this._tabster,r);((o=n==null?void 0:n.restorer)===null||o===void 0?void 0:o.getProps().type)===Kn.Target&&this._addToHistory(r)},this._restoreFocus=r=>{var o;const n=this._getWindow().document;if(z.getActiveElement(n)!==n.body||!this._keyboardNavState.isNavigatingWithKeyboard()&&z.nodeContains(n.body,r))return;let i=this._history.pop();for(;i&&!z.nodeContains(n.body,z.getParentElement(i.get()));)i=this._history.pop();(o=i==null?void 0:i.get())===null||o===void 0||o.focus()},this._tabster=t,this._getWindow=t.getWindow,this._getWindow().addEventListener(Iu,this._onRestoreFocus),this._keyboardNavState=t.keyboardNavigation,this._focusedElementState=t.focusedElement,this._focusedElementState.subscribe(this._onFocusIn)}dispose(){const t=this._getWindow();this._focusedElementState.unsubscribe(this._onFocusIn),this._focusedElementState.cancelAsyncFocus(Jr.Restorer),t.removeEventListener(Iu,this._onRestoreFocus)}_addToHistory(t){var r;((r=this._history[this._history.length-1])===null||r===void 0?void 0:r.get())!==t&&(this._history.length>Ow&&this._history.shift(),this._history.push(new Bt(this._getWindow,t)))}createRestorer(t,r){const o=new Rw(this._tabster,t,r);return r.type===Kn.Target&&z.getActiveElement(t.ownerDocument)===t&&this._addToHistory(t),o}}/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */class Aw{constructor(t){this.keyboardNavigation=t.keyboardNavigation,this.focusedElement=t.focusedElement,this.focusable=t.focusable,this.root=t.root,this.uncontrolled=t.uncontrolled,this.core=t}}class Lw{constructor(t,r){var o,n;this._forgetMemorizedElements=[],this._wrappers=new Set,this._initQueue=[],this._version="7.1.1",this._noop=!1,this.getWindow=()=>{if(!this._win)throw new Error("Using disposed Tabster.");return this._win},this._storage=uw(t),this._win=t;const i=this.getWindow;r!=null&&r.DOMAPI&&lw({...r.DOMAPI}),this.keyboardNavigation=new Ew(i),this.focusedElement=new me(this,i),this.focusable=new xw(this),this.root=new xe(this,r==null?void 0:r.autoRoot),this.uncontrolled=new Dw((r==null?void 0:r.checkUncontrolledCompletely)||(r==null?void 0:r.checkUncontrolledTrappingFocus)),this.controlTab=(o=r==null?void 0:r.controlTab)!==null&&o!==void 0?o:!0,this.rootDummyInputs=!!(r!=null&&r.rootDummyInputs),this._dummyObserver=new yw(i),this.getParent=(n=r==null?void 0:r.getParent)!==null&&n!==void 0?n:z.getParentNode,this.internal={stopObserver:()=>{this._unobserve&&(this._unobserve(),delete this._unobserve)},resumeObserver:l=>{if(!this._unobserve){const s=i().document;this._unobserve=Tw(s,this,ih,l)}}},uh(i),this.queueInit(()=>{this.internal.resumeObserver(!0)})}_mergeProps(t){var r;!t||(this.getParent=(r=t.getParent)!==null&&r!==void 0?r:this.getParent)}createTabster(t,r){const o=new Aw(this);return t||this._wrappers.add(o),this._mergeProps(r),o}disposeTabster(t,r){r?this._wrappers.clear():this._wrappers.delete(t),this._wrappers.size===0&&this.dispose()}dispose(){var t,r,o,n,i,l,s,a;this.internal.stopObserver();const u=this._win;u==null||u.clearTimeout(this._initTimer),delete this._initTimer,this._initQueue=[],this._forgetMemorizedElements=[],u&&this._forgetMemorizedTimer&&(u.clearTimeout(this._forgetMemorizedTimer),delete this._forgetMemorizedTimer),(t=this.outline)===null||t===void 0||t.dispose(),(r=this.crossOrigin)===null||r===void 0||r.dispose(),(o=this.deloser)===null||o===void 0||o.dispose(),(n=this.groupper)===null||n===void 0||n.dispose(),(i=this.mover)===null||i===void 0||i.dispose(),(l=this.modalizer)===null||l===void 0||l.dispose(),(s=this.observedElement)===null||s===void 0||s.dispose(),(a=this.restorer)===null||a===void 0||a.dispose(),this.keyboardNavigation.dispose(),this.focusable.dispose(),this.focusedElement.dispose(),this.root.dispose(),this._dummyObserver.dispose(),dw(this.getWindow),fh(this.getWindow),this._storage=new WeakMap,this._wrappers.clear(),u&&(aw(u),delete u.__tabsterInstance,delete this._win)}storageEntry(t,r){const o=this._storage;let n=o.get(t);return n?r===!1&&Object.keys(n).length===0&&o.delete(t):r===!0&&(n={},o.set(t,n)),n}forceCleanup(){!this._win||(this._forgetMemorizedElements.push(this._win.document.body),!this._forgetMemorizedTimer&&(this._forgetMemorizedTimer=this._win.setTimeout(()=>{delete this._forgetMemorizedTimer;for(let t=this._forgetMemorizedElements.shift();t;t=this._forgetMemorizedElements.shift())fh(this.getWindow,t),me.forgetMemorized(this.focusedElement,t)},0),ah(this.getWindow,!0)))}queueInit(t){var r;!this._win||(this._initQueue.push(t),this._initTimer||(this._initTimer=(r=this._win)===null||r===void 0?void 0:r.setTimeout(()=>{delete this._initTimer,this.drainInitQueue()},0)))}drainInitQueue(){if(!this._win)return;const t=this._initQueue;this._initQueue=[],t.forEach(r=>r())}}function Iw(e,t){let r=Ww(e);return r?r.createTabster(!1,t):(r=new Lw(e,t),e.__tabsterInstance=r,r.createTabster())}function Mw(e,t,r){const o=e.core;return o.modalizer||(o.modalizer=new Pw(o,t,r)),o.modalizer}function jw(e){const t=e.core;return t.restorer||(t.restorer=new zw(t)),t.restorer}function Hw(e,t){e.core.disposeTabster(e,t)}function Ww(e){return e.__tabsterInstance}const Uu=()=>{const{targetDocument:e}=Dt(),t=(e==null?void 0:e.defaultView)||void 0,r=t==null?void 0:t.__tabsterShadowDOMAPI,o=x.exports.useMemo(()=>t?Iw(t,{autoRoot:{},controlTab:!1,getParent:Vp,checkUncontrolledTrappingFocus:n=>{var i;return!!(!((i=n.firstElementChild)===null||i===void 0)&&i.hasAttribute("data-is-focus-trap-zone-bumper"))},DOMAPI:r}):null,[t,r]);return Yt(()=>()=>{o&&Hw(o)},[o]),o},bh=e=>{Uu();const t=hh(e,!0);return x.exports.useMemo(()=>({[Lu.TabsterAttributeName]:t}),[t])},wh=()=>{const e=Uu(),{targetDocument:t}=Dt(),r=x.exports.useCallback((s,a)=>(e==null?void 0:e.focusable.findAll({container:s,acceptCondition:a}))||[],[e]),o=x.exports.useCallback(s=>e==null?void 0:e.focusable.findFirst({container:s}),[e]),n=x.exports.useCallback(s=>e==null?void 0:e.focusable.findLast({container:s}),[e]),i=x.exports.useCallback((s,a={})=>{if(!e||!t)return null;const{container:u=t.body}=a;return e.focusable.findNext({currentElement:s,container:u})},[e,t]),l=x.exports.useCallback((s,a={})=>{if(!e||!t)return null;const{container:u=t.body}=a;return e.focusable.findPrev({currentElement:s,container:u})},[e,t]);return{findAllFocusable:r,findFirstFocusable:o,findLastFocusable:n,findNextFocusable:i,findPrevFocusable:l}},Bh="data-fui-focus-visible";function $w(e,t){if(_h(e))return()=>{};const r={current:void 0},o=th(t);function n(a){o.isNavigatingWithKeyboard()&&Un(a)&&(r.current=a,a.setAttribute(Bh,""))}function i(){r.current&&(r.current.removeAttribute(Bh),r.current=void 0)}o.subscribe(a=>{a||i()});const l=a=>{i();const u=a.composedPath()[0];n(u)},s=a=>{(!a.relatedTarget||Un(a.relatedTarget)&&!e.contains(a.relatedTarget))&&i()};return e.addEventListener(Qt,l),e.addEventListener("focusout",s),e.focusVisible=!0,e.contains(t.document.activeElement)&&n(t.document.activeElement),()=>{i(),e.removeEventListener(Qt,l),e.removeEventListener("focusout",s),delete e.focusVisible,rh(o)}}function _h(e){return e?e.focusVisible?!0:_h(e==null?void 0:e.parentElement):!1}function xh(e={}){const t=Dt(),r=x.exports.useRef(null);var o;const n=(o=e.targetDocument)!==null&&o!==void 0?o:t.targetDocument;return x.exports.useEffect(()=>{if((n==null?void 0:n.defaultView)&&r.current)return $w(r.current,n.defaultView)},[r,n]),r}const Vu=(e={})=>{const{trapFocus:t,alwaysFocusable:r,legacyTrapFocus:o}=e,n=Uu();n&&(Mw(n),jw(n));const i=Fu("modal-",e.id),l=bh({restorer:{type:Lu.RestorerTypes.Source},...t&&{modalizer:{id:i,isOthersAccessible:!t,isAlwaysAccessible:r,isTrapped:o&&t}}}),s=bh({restorer:{type:Lu.RestorerTypes.Target}});return{modalAttributes:l,triggerAttributes:s}},N={2:"#050505",4:"#0a0a0a",6:"#0f0f0f",8:"#141414",10:"#1a1a1a",12:"#1f1f1f",14:"#242424",16:"#292929",18:"#2e2e2e",20:"#333333",22:"#383838",24:"#3d3d3d",26:"#424242",28:"#474747",30:"#4d4d4d",32:"#525252",34:"#575757",36:"#5c5c5c",38:"#616161",40:"#666666",42:"#6b6b6b",44:"#707070",46:"#757575",48:"#7a7a7a",50:"#808080",52:"#858585",54:"#8a8a8a",56:"#8f8f8f",58:"#949494",60:"#999999",62:"#9e9e9e",64:"#a3a3a3",66:"#a8a8a8",68:"#adadad",70:"#b3b3b3",72:"#b8b8b8",74:"#bdbdbd",76:"#c2c2c2",78:"#c7c7c7",80:"#cccccc",82:"#d1d1d1",84:"#d6d6d6",86:"#dbdbdb",88:"#e0e0e0",90:"#e6e6e6",92:"#ebebeb",94:"#f0f0f0",96:"#f5f5f5",98:"#fafafa"},Ce={5:"rgba(255, 255, 255, 0.05)",10:"rgba(255, 255, 255, 0.1)",20:"rgba(255, 255, 255, 0.2)",30:"rgba(255, 255, 255, 0.3)",40:"rgba(255, 255, 255, 0.4)",50:"rgba(255, 255, 255, 0.5)",60:"rgba(255, 255, 255, 0.6)",70:"rgba(255, 255, 255, 0.7)",80:"rgba(255, 255, 255, 0.8)",90:"rgba(255, 255, 255, 0.9)"},dt={5:"rgba(0, 0, 0, 0.05)",10:"rgba(0, 0, 0, 0.1)",20:"rgba(0, 0, 0, 0.2)",30:"rgba(0, 0, 0, 0.3)",40:"rgba(0, 0, 0, 0.4)",50:"rgba(0, 0, 0, 0.5)",60:"rgba(0, 0, 0, 0.6)",70:"rgba(0, 0, 0, 0.7)",80:"rgba(0, 0, 0, 0.8)",90:"rgba(0, 0, 0, 0.9)"},qw={5:"rgba(26, 26, 26, 0.05)",10:"rgba(26, 26, 26, 0.1)",20:"rgba(26, 26, 26, 0.2)",30:"rgba(26, 26, 26, 0.3)",40:"rgba(26, 26, 26, 0.4)",50:"rgba(26, 26, 26, 0.5)",60:"rgba(26, 26, 26, 0.6)",70:"rgba(26, 26, 26, 0.7)",80:"rgba(26, 26, 26, 0.8)",90:"rgba(26, 26, 26, 0.9)"},Uw={5:"rgba(31, 31, 31, 0.05)",10:"rgba(31, 31, 31, 0.1)",20:"rgba(31, 31, 31, 0.2)",30:"rgba(31, 31, 31, 0.3)",40:"rgba(31, 31, 31, 0.4)",50:"rgba(31, 31, 31, 0.5)",60:"rgba(31, 31, 31, 0.6)",70:"rgba(31, 31, 31, 0.7)",80:"rgba(31, 31, 31, 0.8)",90:"rgba(31, 31, 31, 0.9)"},Sh={5:"rgba(36, 36, 36, 0.05)",10:"rgba(36, 36, 36, 0.1)",20:"rgba(36, 36, 36, 0.2)",30:"rgba(36, 36, 36, 0.3)",40:"rgba(36, 36, 36, 0.4)",50:"rgba(36, 36, 36, 0.5)",60:"rgba(36, 36, 36, 0.6)",70:"rgba(36, 36, 36, 0.7)",80:"rgba(36, 36, 36, 0.8)",90:"rgba(36, 36, 36, 0.9)"},M="#ffffff",Ll="#000000",Vw={shade50:"#130204",shade40:"#230308",shade30:"#420610",shade20:"#590815",shade10:"#690a19",primary:"#750b1c",tint10:"#861b2c",tint20:"#962f3f",tint30:"#ac4f5e",tint40:"#d69ca5",tint50:"#e9c7cd",tint60:"#f9f0f2"},Eh={shade50:"#200205",shade40:"#3b0509",shade30:"#6e0811",shade20:"#960b18",shade10:"#b10e1c",primary:"#c50f1f",tint10:"#cc2635",tint20:"#d33f4c",tint30:"#dc626d",tint40:"#eeacb2",tint50:"#f6d1d5",tint60:"#fdf3f4"},Kw={shade50:"#210809",shade40:"#3f1011",shade30:"#751d1f",shade20:"#9f282b",shade10:"#bc2f32",primary:"#d13438",tint10:"#d7494c",tint20:"#dc5e62",tint30:"#e37d80",tint40:"#f1bbbc",tint50:"#f8dadb",tint60:"#fdf6f6"},Xw={shade50:"#230900",shade40:"#411200",shade30:"#7a2101",shade20:"#a62d01",shade10:"#c43501",primary:"#da3b01",tint10:"#de501c",tint20:"#e36537",tint30:"#e9835e",tint40:"#f4bfab",tint50:"#f9dcd1",tint60:"#fdf6f3"},Yw={shade50:"#200d03",shade40:"#3d1805",shade30:"#712d09",shade20:"#9a3d0c",shade10:"#b6480e",primary:"#ca5010",tint10:"#d06228",tint20:"#d77440",tint30:"#df8e64",tint40:"#efc4ad",tint50:"#f7dfd2",tint60:"#fdf7f4"},Qw={shade50:"#271002",shade40:"#4a1e04",shade30:"#8a3707",shade20:"#bc4b09",shade10:"#de590b",primary:"#f7630c",tint10:"#f87528",tint20:"#f98845",tint30:"#faa06b",tint40:"#fdcfb4",tint50:"#fee5d7",tint60:"#fff9f5"},Jw={shade50:"#291600",shade40:"#4d2a00",shade30:"#8f4e00",shade20:"#c26a00",shade10:"#e67e00",primary:"#ff8c00",tint10:"#ff9a1f",tint20:"#ffa83d",tint30:"#ffba66",tint40:"#ffddb3",tint50:"#ffedd6",tint60:"#fffaf5"},Zw={shade50:"#251a00",shade40:"#463100",shade30:"#835b00",shade20:"#b27c00",shade10:"#d39300",primary:"#eaa300",tint10:"#edad1c",tint20:"#efb839",tint30:"#f2c661",tint40:"#f9e2ae",tint50:"#fcefd3",tint60:"#fefbf4"},Gw={shade50:"#282400",shade40:"#4c4400",shade30:"#817400",shade20:"#c0ad00",shade10:"#e4cc00",primary:"#fde300",tint10:"#fde61e",tint20:"#fdea3d",tint30:"#feee66",tint40:"#fef7b2",tint50:"#fffad6",tint60:"#fffef5"},eB={shade50:"#1f1900",shade40:"#3a2f00",shade30:"#6c5700",shade20:"#937700",shade10:"#ae8c00",primary:"#c19c00",tint10:"#c8a718",tint20:"#d0b232",tint30:"#dac157",tint40:"#ecdfa5",tint50:"#f5eece",tint60:"#fdfbf2"},tB={shade50:"#181202",shade40:"#2e2103",shade30:"#553e06",shade20:"#745408",shade10:"#89640a",primary:"#986f0b",tint10:"#a47d1e",tint20:"#b18c34",tint30:"#c1a256",tint40:"#e0cea2",tint50:"#efe4cb",tint60:"#fbf8f2"},rB={shade50:"#170e07",shade40:"#2b1a0e",shade30:"#50301a",shade20:"#6c4123",shade10:"#804d29",primary:"#8e562e",tint10:"#9c663f",tint20:"#a97652",tint30:"#bb8f6f",tint40:"#ddc3b0",tint50:"#edded3",tint60:"#faf7f4"},oB={shade50:"#0c1501",shade40:"#162702",shade30:"#294903",shade20:"#376304",shade10:"#427505",primary:"#498205",tint10:"#599116",tint20:"#6ba02b",tint30:"#85b44c",tint40:"#bdd99b",tint50:"#dbebc7",tint60:"#f6faf0"},nB={shade50:"#002111",shade40:"#003d20",shade30:"#00723b",shade20:"#009b51",shade10:"#00b85f",primary:"#00cc6a",tint10:"#19d279",tint20:"#34d889",tint30:"#5ae0a0",tint40:"#a8f0cd",tint50:"#cff7e4",tint60:"#f3fdf8"},iB={shade50:"#031a02",shade40:"#063004",shade30:"#0b5a08",shade20:"#0e7a0b",shade10:"#11910d",primary:"#13a10e",tint10:"#27ac22",tint20:"#3db838",tint30:"#5ec75a",tint40:"#a7e3a5",tint50:"#cef0cd",tint60:"#f2fbf2"},Nh={shade50:"#031403",shade40:"#052505",shade30:"#094509",shade20:"#0c5e0c",shade10:"#0e700e",primary:"#107c10",tint10:"#218c21",tint20:"#359b35",tint30:"#54b054",tint40:"#9fd89f",tint50:"#c9eac9",tint60:"#f1faf1"},lB={shade50:"#021102",shade40:"#032003",shade30:"#063b06",shade20:"#085108",shade10:"#0a5f0a",primary:"#0b6a0b",tint10:"#1a7c1a",tint20:"#2d8e2d",tint30:"#4da64d",tint40:"#9ad29a",tint50:"#c6e7c6",tint60:"#f0f9f0"},sB={shade50:"#001d1f",shade40:"#00373a",shade30:"#00666d",shade20:"#008b94",shade10:"#00a5af",primary:"#00b7c3",tint10:"#18bfca",tint20:"#32c8d1",tint30:"#58d3db",tint40:"#a6e9ed",tint50:"#cef3f5",tint60:"#f2fcfd"},aB={shade50:"#001516",shade40:"#012728",shade30:"#02494c",shade20:"#026467",shade10:"#037679",primary:"#038387",tint10:"#159195",tint20:"#2aa0a4",tint30:"#4cb4b7",tint40:"#9bd9db",tint50:"#c7ebec",tint60:"#f0fafa"},uB={shade50:"#000f12",shade40:"#001b22",shade30:"#00333f",shade20:"#004555",shade10:"#005265",primary:"#005b70",tint10:"#0f6c81",tint20:"#237d92",tint30:"#4496a9",tint40:"#94c8d4",tint50:"#c3e1e8",tint60:"#eff7f9"},cB={shade50:"#001322",shade40:"#002440",shade30:"#004377",shade20:"#005ba1",shade10:"#006cbf",primary:"#0078d4",tint10:"#1a86d9",tint20:"#3595de",tint30:"#5caae5",tint40:"#a9d3f2",tint50:"#d0e7f8",tint60:"#f3f9fd"},dB={shade50:"#000c16",shade40:"#00172a",shade30:"#002c4e",shade20:"#003b6a",shade10:"#00467e",primary:"#004e8c",tint10:"#125e9a",tint20:"#286fa8",tint30:"#4a89ba",tint40:"#9abfdc",tint50:"#c7dced",tint60:"#f0f6fa"},fB={shade50:"#0d1126",shade40:"#182047",shade30:"#2c3c85",shade20:"#3c51b4",shade10:"#4760d5",primary:"#4f6bed",tint10:"#637cef",tint20:"#778df1",tint30:"#93a4f4",tint40:"#c8d1fa",tint50:"#e1e6fc",tint60:"#f7f9fe"},gB={shade50:"#00061d",shade40:"#000c36",shade30:"#001665",shade20:"#001e89",shade10:"#0023a2",primary:"#0027b4",tint10:"#173bbd",tint20:"#3050c6",tint30:"#546fd2",tint40:"#a3b2e8",tint50:"#ccd5f3",tint60:"#f2f4fc"},pB={shade50:"#120f25",shade40:"#221d46",shade30:"#3f3682",shade20:"#5649b0",shade10:"#6656d1",primary:"#7160e8",tint10:"#8172eb",tint20:"#9184ee",tint30:"#a79cf1",tint40:"#d2ccf8",tint50:"#e7e4fb",tint60:"#f9f8fe"},hB={shade50:"#0f0717",shade40:"#1c0e2b",shade30:"#341a51",shade20:"#46236e",shade10:"#532982",primary:"#5c2e91",tint10:"#6b3f9e",tint20:"#7c52ab",tint30:"#9470bd",tint40:"#c6b1de",tint50:"#e0d3ed",tint60:"#f7f4fb"},vB={shade50:"#160418",shade40:"#29072e",shade30:"#4c0d55",shade20:"#671174",shade10:"#7a1589",primary:"#881798",tint10:"#952aa4",tint20:"#a33fb1",tint30:"#b55fc1",tint40:"#d9a7e0",tint50:"#eaceef",tint60:"#faf2fb"},mB={shade50:"#1f091d",shade40:"#3a1136",shade30:"#6d2064",shade20:"#932b88",shade10:"#af33a1",primary:"#c239b3",tint10:"#c94cbc",tint20:"#d161c4",tint30:"#da7ed0",tint40:"#edbbe7",tint50:"#f5daf2",tint60:"#fdf5fc"},yB={shade50:"#1c0b1f",shade40:"#35153a",shade30:"#63276d",shade20:"#863593",shade10:"#9f3faf",primary:"#b146c2",tint10:"#ba58c9",tint20:"#c36bd1",tint30:"#cf87da",tint40:"#e6bfed",tint50:"#f2dcf5",tint60:"#fcf6fd"},kB={shade50:"#24091b",shade40:"#441232",shade30:"#80215d",shade20:"#ad2d7e",shade10:"#cd3595",primary:"#e43ba6",tint10:"#e750b0",tint20:"#ea66ba",tint30:"#ef85c8",tint40:"#f7c0e3",tint50:"#fbddf0",tint60:"#fef6fb"},bB={shade50:"#1f0013",shade40:"#390024",shade30:"#6b0043",shade20:"#91005a",shade10:"#ac006b",primary:"#bf0077",tint10:"#c71885",tint20:"#ce3293",tint30:"#d957a8",tint40:"#eca5d1",tint50:"#f5cee6",tint60:"#fcf2f9"},wB={shade50:"#13000c",shade40:"#240017",shade30:"#43002b",shade20:"#5a003b",shade10:"#6b0045",primary:"#77004d",tint10:"#87105d",tint20:"#98246f",tint30:"#ad4589",tint40:"#d696c0",tint50:"#e9c4dc",tint60:"#faf0f6"},BB={shade50:"#141313",shade40:"#252323",shade30:"#444241",shade20:"#5d5958",shade10:"#6e6968",primary:"#7a7574",tint10:"#8a8584",tint20:"#9a9594",tint30:"#afabaa",tint40:"#d7d4d4",tint50:"#eae8e8",tint60:"#faf9f9"},_B={shade50:"#0f0e0e",shade40:"#1c1b1a",shade30:"#343231",shade20:"#474443",shade10:"#54514f",primary:"#5d5a58",tint10:"#706d6b",tint20:"#84817e",tint30:"#9e9b99",tint40:"#cecccb",tint50:"#e5e4e3",tint60:"#f8f8f8"},xB={shade50:"#111314",shade40:"#1f2426",shade30:"#3b4447",shade20:"#505c60",shade10:"#5f6d71",primary:"#69797e",tint10:"#79898d",tint20:"#89989d",tint30:"#a0adb2",tint40:"#cdd6d8",tint50:"#e4e9ea",tint60:"#f8f9fa"},SB={shade50:"#090a0b",shade40:"#111315",shade30:"#202427",shade20:"#2b3135",shade10:"#333a3f",primary:"#394146",tint10:"#4d565c",tint20:"#626c72",tint30:"#808a90",tint40:"#bcc3c7",tint50:"#dbdfe1",tint60:"#f6f7f8"},K={red:Kw,green:Nh,darkOrange:Xw,yellow:Gw,berry:mB,lightGreen:iB,marigold:Zw},Sr={darkRed:Vw,cranberry:Eh,pumpkin:Yw,peach:Jw,gold:eB,brass:tB,brown:rB,forest:oB,seafoam:nB,darkGreen:lB,lightTeal:sB,teal:aB,steel:uB,blue:cB,royalBlue:dB,cornflower:fB,navy:gB,lavender:pB,purple:hB,grape:vB,lilac:yB,pink:kB,magenta:bB,plum:wB,beige:BB,mink:_B,platinum:xB,anchor:SB},U={cranberry:Eh,green:Nh,orange:Qw},Fh=["red","green","darkOrange","yellow","berry","lightGreen","marigold"],Ch=["darkRed","cranberry","pumpkin","peach","gold","brass","brown","forest","seafoam","darkGreen","lightTeal","teal","steel","blue","royalBlue","cornflower","navy","lavender","purple","grape","lilac","pink","magenta","plum","beige","mink","platinum","anchor"],Ke={success:"green",warning:"orange",danger:"cranberry"},Yn=Fh.reduce((e,t)=>{const r=t.slice(0,1).toUpperCase()+t.slice(1),o={[`colorPalette${r}Background1`]:K[t].tint60,[`colorPalette${r}Background2`]:K[t].tint40,[`colorPalette${r}Background3`]:K[t].primary,[`colorPalette${r}Foreground1`]:K[t].shade10,[`colorPalette${r}Foreground2`]:K[t].shade30,[`colorPalette${r}Foreground3`]:K[t].primary,[`colorPalette${r}BorderActive`]:K[t].primary,[`colorPalette${r}Border1`]:K[t].tint40,[`colorPalette${r}Border2`]:K[t].primary};return Object.assign(e,o)},{});Yn.colorPaletteYellowForeground1=K.yellow.shade30,Yn.colorPaletteRedForegroundInverted=K.red.tint20,Yn.colorPaletteGreenForegroundInverted=K.green.tint20,Yn.colorPaletteYellowForegroundInverted=K.yellow.tint40;const EB=Ch.reduce((e,t)=>{const r=t.slice(0,1).toUpperCase()+t.slice(1),o={[`colorPalette${r}Background2`]:Sr[t].tint40,[`colorPalette${r}Foreground2`]:Sr[t].shade30,[`colorPalette${r}BorderActive`]:Sr[t].primary};return Object.assign(e,o)},{}),NB={...Yn,...EB},jo=Object.entries(Ke).reduce((e,[t,r])=>{const o=t.slice(0,1).toUpperCase()+t.slice(1),n={[`colorStatus${o}Background1`]:U[r].tint60,[`colorStatus${o}Background2`]:U[r].tint40,[`colorStatus${o}Background3`]:U[r].primary,[`colorStatus${o}Foreground1`]:U[r].shade10,[`colorStatus${o}Foreground2`]:U[r].shade30,[`colorStatus${o}Foreground3`]:U[r].primary,[`colorStatus${o}ForegroundInverted`]:U[r].tint30,[`colorStatus${o}BorderActive`]:U[r].primary,[`colorStatus${o}Border1`]:U[r].tint40,[`colorStatus${o}Border2`]:U[r].primary};return Object.assign(e,n)},{});jo.colorStatusDangerBackground3Hover=U[Ke.danger].shade10,jo.colorStatusDangerBackground3Pressed=U[Ke.danger].shade20,jo.colorStatusWarningForeground1=U[Ke.warning].shade20,jo.colorStatusWarningForeground3=U[Ke.warning].shade20,jo.colorStatusWarningBorder2=U[Ke.warning].shade20;const FB=e=>({colorNeutralForeground1:N[14],colorNeutralForeground1Hover:N[14],colorNeutralForeground1Pressed:N[14],colorNeutralForeground1Selected:N[14],colorNeutralForeground2:N[26],colorNeutralForeground2Hover:N[14],colorNeutralForeground2Pressed:N[14],colorNeutralForeground2Selected:N[14],colorNeutralForeground2BrandHover:e[80],colorNeutralForeground2BrandPressed:e[70],colorNeutralForeground2BrandSelected:e[80],colorNeutralForeground3:N[38],colorNeutralForeground3Hover:N[26],colorNeutralForeground3Pressed:N[26],colorNeutralForeground3Selected:N[26],colorNeutralForeground3BrandHover:e[80],colorNeutralForeground3BrandPressed:e[70],colorNeutralForeground3BrandSelected:e[80],colorNeutralForeground4:N[44],colorNeutralForegroundDisabled:N[74],colorNeutralForegroundInvertedDisabled:Ce[40],colorBrandForegroundLink:e[70],colorBrandForegroundLinkHover:e[60],colorBrandForegroundLinkPressed:e[40],colorBrandForegroundLinkSelected:e[70],colorNeutralForeground2Link:N[26],colorNeutralForeground2LinkHover:N[14],colorNeutralForeground2LinkPressed:N[14],colorNeutralForeground2LinkSelected:N[14],colorCompoundBrandForeground1:e[80],colorCompoundBrandForeground1Hover:e[70],colorCompoundBrandForeground1Pressed:e[60],colorBrandForeground1:e[80],colorBrandForeground2:e[70],colorBrandForeground2Hover:e[60],colorBrandForeground2Pressed:e[30],colorNeutralForeground1Static:N[14],colorNeutralForegroundStaticInverted:M,colorNeutralForegroundInverted:M,colorNeutralForegroundInvertedHover:M,colorNeutralForegroundInvertedPressed:M,colorNeutralForegroundInvertedSelected:M,colorNeutralForegroundInverted2:M,colorNeutralForegroundOnBrand:M,colorNeutralForegroundInvertedLink:M,colorNeutralForegroundInvertedLinkHover:M,colorNeutralForegroundInvertedLinkPressed:M,colorNeutralForegroundInvertedLinkSelected:M,colorBrandForegroundInverted:e[100],colorBrandForegroundInvertedHover:e[110],colorBrandForegroundInvertedPressed:e[100],colorBrandForegroundOnLight:e[80],colorBrandForegroundOnLightHover:e[70],colorBrandForegroundOnLightPressed:e[50],colorBrandForegroundOnLightSelected:e[60],colorNeutralBackground1:M,colorNeutralBackground1Hover:N[96],colorNeutralBackground1Pressed:N[88],colorNeutralBackground1Selected:N[92],colorNeutralBackground2:N[98],colorNeutralBackground2Hover:N[94],colorNeutralBackground2Pressed:N[86],colorNeutralBackground2Selected:N[90],colorNeutralBackground3:N[96],colorNeutralBackground3Hover:N[92],colorNeutralBackground3Pressed:N[84],colorNeutralBackground3Selected:N[88],colorNeutralBackground4:N[94],colorNeutralBackground4Hover:N[98],colorNeutralBackground4Pressed:N[96],colorNeutralBackground4Selected:M,colorNeutralBackground5:N[92],colorNeutralBackground5Hover:N[96],colorNeutralBackground5Pressed:N[94],colorNeutralBackground5Selected:N[98],colorNeutralBackground6:N[90],colorNeutralBackgroundInverted:N[16],colorNeutralBackgroundStatic:N[20],colorNeutralBackgroundAlpha:Ce[50],colorNeutralBackgroundAlpha2:Ce[80],colorSubtleBackground:"transparent",colorSubtleBackgroundHover:N[96],colorSubtleBackgroundPressed:N[88],colorSubtleBackgroundSelected:N[92],colorSubtleBackgroundLightAlphaHover:Ce[70],colorSubtleBackgroundLightAlphaPressed:Ce[50],colorSubtleBackgroundLightAlphaSelected:"transparent",colorSubtleBackgroundInverted:"transparent",colorSubtleBackgroundInvertedHover:dt[10],colorSubtleBackgroundInvertedPressed:dt[30],colorSubtleBackgroundInvertedSelected:dt[20],colorTransparentBackground:"transparent",colorTransparentBackgroundHover:"transparent",colorTransparentBackgroundPressed:"transparent",colorTransparentBackgroundSelected:"transparent",colorNeutralBackgroundDisabled:N[94],colorNeutralBackgroundInvertedDisabled:Ce[10],colorNeutralStencil1:N[90],colorNeutralStencil2:N[98],colorNeutralStencil1Alpha:dt[10],colorNeutralStencil2Alpha:dt[5],colorBackgroundOverlay:dt[40],colorScrollbarOverlay:dt[50],colorBrandBackground:e[80],colorBrandBackgroundHover:e[70],colorBrandBackgroundPressed:e[40],colorBrandBackgroundSelected:e[60],colorCompoundBrandBackground:e[80],colorCompoundBrandBackgroundHover:e[70],colorCompoundBrandBackgroundPressed:e[60],colorBrandBackgroundStatic:e[80],colorBrandBackground2:e[160],colorBrandBackground2Hover:e[150],colorBrandBackground2Pressed:e[130],colorBrandBackground3Static:e[60],colorBrandBackground4Static:e[40],colorBrandBackgroundInverted:M,colorBrandBackgroundInvertedHover:e[160],colorBrandBackgroundInvertedPressed:e[140],colorBrandBackgroundInvertedSelected:e[150],colorNeutralCardBackground:N[98],colorNeutralCardBackgroundHover:M,colorNeutralCardBackgroundPressed:N[96],colorNeutralCardBackgroundSelected:N[92],colorNeutralCardBackgroundDisabled:N[94],colorNeutralStrokeAccessible:N[38],colorNeutralStrokeAccessibleHover:N[34],colorNeutralStrokeAccessiblePressed:N[30],colorNeutralStrokeAccessibleSelected:e[80],colorNeutralStroke1:N[82],colorNeutralStroke1Hover:N[78],colorNeutralStroke1Pressed:N[70],colorNeutralStroke1Selected:N[74],colorNeutralStroke2:N[88],colorNeutralStroke3:N[94],colorNeutralStrokeSubtle:N[88],colorNeutralStrokeOnBrand:M,colorNeutralStrokeOnBrand2:M,colorNeutralStrokeOnBrand2Hover:M,colorNeutralStrokeOnBrand2Pressed:M,colorNeutralStrokeOnBrand2Selected:M,colorBrandStroke1:e[80],colorBrandStroke2:e[140],colorBrandStroke2Hover:e[120],colorBrandStroke2Pressed:e[80],colorBrandStroke2Contrast:e[140],colorCompoundBrandStroke:e[80],colorCompoundBrandStrokeHover:e[70],colorCompoundBrandStrokePressed:e[60],colorNeutralStrokeDisabled:N[88],colorNeutralStrokeInvertedDisabled:Ce[40],colorTransparentStroke:"transparent",colorTransparentStrokeInteractive:"transparent",colorTransparentStrokeDisabled:"transparent",colorNeutralStrokeAlpha:dt[5],colorNeutralStrokeAlpha2:Ce[20],colorStrokeFocus1:M,colorStrokeFocus2:Ll,colorNeutralShadowAmbient:"rgba(0,0,0,0.12)",colorNeutralShadowKey:"rgba(0,0,0,0.14)",colorNeutralShadowAmbientLighter:"rgba(0,0,0,0.06)",colorNeutralShadowKeyLighter:"rgba(0,0,0,0.07)",colorNeutralShadowAmbientDarker:"rgba(0,0,0,0.20)",colorNeutralShadowKeyDarker:"rgba(0,0,0,0.24)",colorBrandShadowAmbient:"rgba(0,0,0,0.30)",colorBrandShadowKey:"rgba(0,0,0,0.25)"}),Ph={borderRadiusNone:"0",borderRadiusSmall:"2px",borderRadiusMedium:"4px",borderRadiusLarge:"6px",borderRadiusXLarge:"8px",borderRadiusCircular:"10000px"},Th={curveAccelerateMax:"cubic-bezier(0.9,0.1,1,0.2)",curveAccelerateMid:"cubic-bezier(1,0,1,1)",curveAccelerateMin:"cubic-bezier(0.8,0,0.78,1)",curveDecelerateMax:"cubic-bezier(0.1,0.9,0.2,1)",curveDecelerateMid:"cubic-bezier(0,0,0,1)",curveDecelerateMin:"cubic-bezier(0.33,0,0.1,1)",curveEasyEaseMax:"cubic-bezier(0.8,0,0.2,1)",curveEasyEase:"cubic-bezier(0.33,0,0.67,1)",curveLinear:"cubic-bezier(0,0,1,1)"},Dh={durationUltraFast:"50ms",durationFaster:"100ms",durationFast:"150ms",durationNormal:"200ms",durationGentle:"250ms",durationSlow:"300ms",durationSlower:"400ms",durationUltraSlow:"500ms"},Oh={fontSizeBase100:"10px",fontSizeBase200:"12px",fontSizeBase300:"14px",fontSizeBase400:"16px",fontSizeBase500:"20px",fontSizeBase600:"24px",fontSizeHero700:"28px",fontSizeHero800:"32px",fontSizeHero900:"40px",fontSizeHero1000:"68px"},Rh={lineHeightBase100:"14px",lineHeightBase200:"16px",lineHeightBase300:"20px",lineHeightBase400:"22px",lineHeightBase500:"28px",lineHeightBase600:"32px",lineHeightHero700:"36px",lineHeightHero800:"40px",lineHeightHero900:"52px",lineHeightHero1000:"92px"},zh={fontWeightRegular:400,fontWeightMedium:500,fontWeightSemibold:600,fontWeightBold:700},Ah={fontFamilyBase:"'Segoe UI', 'Segoe UI Web (West European)', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif",fontFamilyMonospace:"Consolas, 'Courier New', Courier, monospace",fontFamilyNumeric:"Bahnschrift, 'Segoe UI', 'Segoe UI Web (West European)', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif"},ue={none:"0",xxs:"2px",xs:"4px",sNudge:"6px",s:"8px",mNudge:"10px",m:"12px",l:"16px",xl:"20px",xxl:"24px",xxxl:"32px"},Lh={spacingHorizontalNone:ue.none,spacingHorizontalXXS:ue.xxs,spacingHorizontalXS:ue.xs,spacingHorizontalSNudge:ue.sNudge,spacingHorizontalS:ue.s,spacingHorizontalMNudge:ue.mNudge,spacingHorizontalM:ue.m,spacingHorizontalL:ue.l,spacingHorizontalXL:ue.xl,spacingHorizontalXXL:ue.xxl,spacingHorizontalXXXL:ue.xxxl},Ih={spacingVerticalNone:ue.none,spacingVerticalXXS:ue.xxs,spacingVerticalXS:ue.xs,spacingVerticalSNudge:ue.sNudge,spacingVerticalS:ue.s,spacingVerticalMNudge:ue.mNudge,spacingVerticalM:ue.m,spacingVerticalL:ue.l,spacingVerticalXL:ue.xl,spacingVerticalXXL:ue.xxl,spacingVerticalXXXL:ue.xxxl},Mh={strokeWidthThin:"1px",strokeWidthThick:"2px",strokeWidthThicker:"3px",strokeWidthThickest:"4px"},Il={colorNeutralForeground1:"var(--colorNeutralForeground1)",colorNeutralForeground1Hover:"var(--colorNeutralForeground1Hover)",colorNeutralForeground1Pressed:"var(--colorNeutralForeground1Pressed)",colorNeutralForeground1Selected:"var(--colorNeutralForeground1Selected)",colorNeutralForeground2:"var(--colorNeutralForeground2)",colorNeutralForeground2Hover:"var(--colorNeutralForeground2Hover)",colorNeutralForeground2Pressed:"var(--colorNeutralForeground2Pressed)",colorNeutralForeground2Selected:"var(--colorNeutralForeground2Selected)",colorNeutralForeground2BrandHover:"var(--colorNeutralForeground2BrandHover)",colorNeutralForeground2BrandPressed:"var(--colorNeutralForeground2BrandPressed)",colorNeutralForeground2BrandSelected:"var(--colorNeutralForeground2BrandSelected)",colorNeutralForeground3:"var(--colorNeutralForeground3)",colorNeutralForeground3Hover:"var(--colorNeutralForeground3Hover)",colorNeutralForeground3Pressed:"var(--colorNeutralForeground3Pressed)",colorNeutralForeground3Selected:"var(--colorNeutralForeground3Selected)",colorNeutralForeground3BrandHover:"var(--colorNeutralForeground3BrandHover)",colorNeutralForeground3BrandPressed:"var(--colorNeutralForeground3BrandPressed)",colorNeutralForeground3BrandSelected:"var(--colorNeutralForeground3BrandSelected)",colorNeutralForeground4:"var(--colorNeutralForeground4)",colorNeutralForegroundDisabled:"var(--colorNeutralForegroundDisabled)",colorBrandForegroundLink:"var(--colorBrandForegroundLink)",colorBrandForegroundLinkHover:"var(--colorBrandForegroundLinkHover)",colorBrandForegroundLinkPressed:"var(--colorBrandForegroundLinkPressed)",colorBrandForegroundLinkSelected:"var(--colorBrandForegroundLinkSelected)",colorNeutralForeground2Link:"var(--colorNeutralForeground2Link)",colorNeutralForeground2LinkHover:"var(--colorNeutralForeground2LinkHover)",colorNeutralForeground2LinkPressed:"var(--colorNeutralForeground2LinkPressed)",colorNeutralForeground2LinkSelected:"var(--colorNeutralForeground2LinkSelected)",colorCompoundBrandForeground1:"var(--colorCompoundBrandForeground1)",colorCompoundBrandForeground1Hover:"var(--colorCompoundBrandForeground1Hover)",colorCompoundBrandForeground1Pressed:"var(--colorCompoundBrandForeground1Pressed)",colorNeutralForegroundOnBrand:"var(--colorNeutralForegroundOnBrand)",colorNeutralForegroundInverted:"var(--colorNeutralForegroundInverted)",colorNeutralForegroundInvertedHover:"var(--colorNeutralForegroundInvertedHover)",colorNeutralForegroundInvertedPressed:"var(--colorNeutralForegroundInvertedPressed)",colorNeutralForegroundInvertedSelected:"var(--colorNeutralForegroundInvertedSelected)",colorNeutralForegroundInverted2:"var(--colorNeutralForegroundInverted2)",colorNeutralForegroundStaticInverted:"var(--colorNeutralForegroundStaticInverted)",colorNeutralForegroundInvertedLink:"var(--colorNeutralForegroundInvertedLink)",colorNeutralForegroundInvertedLinkHover:"var(--colorNeutralForegroundInvertedLinkHover)",colorNeutralForegroundInvertedLinkPressed:"var(--colorNeutralForegroundInvertedLinkPressed)",colorNeutralForegroundInvertedLinkSelected:"var(--colorNeutralForegroundInvertedLinkSelected)",colorNeutralForegroundInvertedDisabled:"var(--colorNeutralForegroundInvertedDisabled)",colorBrandForeground1:"var(--colorBrandForeground1)",colorBrandForeground2:"var(--colorBrandForeground2)",colorBrandForeground2Hover:"var(--colorBrandForeground2Hover)",colorBrandForeground2Pressed:"var(--colorBrandForeground2Pressed)",colorNeutralForeground1Static:"var(--colorNeutralForeground1Static)",colorBrandForegroundInverted:"var(--colorBrandForegroundInverted)",colorBrandForegroundInvertedHover:"var(--colorBrandForegroundInvertedHover)",colorBrandForegroundInvertedPressed:"var(--colorBrandForegroundInvertedPressed)",colorBrandForegroundOnLight:"var(--colorBrandForegroundOnLight)",colorBrandForegroundOnLightHover:"var(--colorBrandForegroundOnLightHover)",colorBrandForegroundOnLightPressed:"var(--colorBrandForegroundOnLightPressed)",colorBrandForegroundOnLightSelected:"var(--colorBrandForegroundOnLightSelected)",colorNeutralBackground1:"var(--colorNeutralBackground1)",colorNeutralBackground1Hover:"var(--colorNeutralBackground1Hover)",colorNeutralBackground1Pressed:"var(--colorNeutralBackground1Pressed)",colorNeutralBackground1Selected:"var(--colorNeutralBackground1Selected)",colorNeutralBackground2:"var(--colorNeutralBackground2)",colorNeutralBackground2Hover:"var(--colorNeutralBackground2Hover)",colorNeutralBackground2Pressed:"var(--colorNeutralBackground2Pressed)",colorNeutralBackground2Selected:"var(--colorNeutralBackground2Selected)",colorNeutralBackground3:"var(--colorNeutralBackground3)",colorNeutralBackground3Hover:"var(--colorNeutralBackground3Hover)",colorNeutralBackground3Pressed:"var(--colorNeutralBackground3Pressed)",colorNeutralBackground3Selected:"var(--colorNeutralBackground3Selected)",colorNeutralBackground4:"var(--colorNeutralBackground4)",colorNeutralBackground4Hover:"var(--colorNeutralBackground4Hover)",colorNeutralBackground4Pressed:"var(--colorNeutralBackground4Pressed)",colorNeutralBackground4Selected:"var(--colorNeutralBackground4Selected)",colorNeutralBackground5:"var(--colorNeutralBackground5)",colorNeutralBackground5Hover:"var(--colorNeutralBackground5Hover)",colorNeutralBackground5Pressed:"var(--colorNeutralBackground5Pressed)",colorNeutralBackground5Selected:"var(--colorNeutralBackground5Selected)",colorNeutralBackground6:"var(--colorNeutralBackground6)",colorNeutralBackgroundInverted:"var(--colorNeutralBackgroundInverted)",colorNeutralBackgroundStatic:"var(--colorNeutralBackgroundStatic)",colorNeutralBackgroundAlpha:"var(--colorNeutralBackgroundAlpha)",colorNeutralBackgroundAlpha2:"var(--colorNeutralBackgroundAlpha2)",colorSubtleBackground:"var(--colorSubtleBackground)",colorSubtleBackgroundHover:"var(--colorSubtleBackgroundHover)",colorSubtleBackgroundPressed:"var(--colorSubtleBackgroundPressed)",colorSubtleBackgroundSelected:"var(--colorSubtleBackgroundSelected)",colorSubtleBackgroundLightAlphaHover:"var(--colorSubtleBackgroundLightAlphaHover)",colorSubtleBackgroundLightAlphaPressed:"var(--colorSubtleBackgroundLightAlphaPressed)",colorSubtleBackgroundLightAlphaSelected:"var(--colorSubtleBackgroundLightAlphaSelected)",colorSubtleBackgroundInverted:"var(--colorSubtleBackgroundInverted)",colorSubtleBackgroundInvertedHover:"var(--colorSubtleBackgroundInvertedHover)",colorSubtleBackgroundInvertedPressed:"var(--colorSubtleBackgroundInvertedPressed)",colorSubtleBackgroundInvertedSelected:"var(--colorSubtleBackgroundInvertedSelected)",colorTransparentBackground:"var(--colorTransparentBackground)",colorTransparentBackgroundHover:"var(--colorTransparentBackgroundHover)",colorTransparentBackgroundPressed:"var(--colorTransparentBackgroundPressed)",colorTransparentBackgroundSelected:"var(--colorTransparentBackgroundSelected)",colorNeutralBackgroundDisabled:"var(--colorNeutralBackgroundDisabled)",colorNeutralBackgroundInvertedDisabled:"var(--colorNeutralBackgroundInvertedDisabled)",colorNeutralStencil1:"var(--colorNeutralStencil1)",colorNeutralStencil2:"var(--colorNeutralStencil2)",colorNeutralStencil1Alpha:"var(--colorNeutralStencil1Alpha)",colorNeutralStencil2Alpha:"var(--colorNeutralStencil2Alpha)",colorBackgroundOverlay:"var(--colorBackgroundOverlay)",colorScrollbarOverlay:"var(--colorScrollbarOverlay)",colorBrandBackground:"var(--colorBrandBackground)",colorBrandBackgroundHover:"var(--colorBrandBackgroundHover)",colorBrandBackgroundPressed:"var(--colorBrandBackgroundPressed)",colorBrandBackgroundSelected:"var(--colorBrandBackgroundSelected)",colorCompoundBrandBackground:"var(--colorCompoundBrandBackground)",colorCompoundBrandBackgroundHover:"var(--colorCompoundBrandBackgroundHover)",colorCompoundBrandBackgroundPressed:"var(--colorCompoundBrandBackgroundPressed)",colorBrandBackgroundStatic:"var(--colorBrandBackgroundStatic)",colorBrandBackground2:"var(--colorBrandBackground2)",colorBrandBackground2Hover:"var(--colorBrandBackground2Hover)",colorBrandBackground2Pressed:"var(--colorBrandBackground2Pressed)",colorBrandBackground3Static:"var(--colorBrandBackground3Static)",colorBrandBackground4Static:"var(--colorBrandBackground4Static)",colorBrandBackgroundInverted:"var(--colorBrandBackgroundInverted)",colorBrandBackgroundInvertedHover:"var(--colorBrandBackgroundInvertedHover)",colorBrandBackgroundInvertedPressed:"var(--colorBrandBackgroundInvertedPressed)",colorBrandBackgroundInvertedSelected:"var(--colorBrandBackgroundInvertedSelected)",colorNeutralCardBackground:"var(--colorNeutralCardBackground)",colorNeutralCardBackgroundHover:"var(--colorNeutralCardBackgroundHover)",colorNeutralCardBackgroundPressed:"var(--colorNeutralCardBackgroundPressed)",colorNeutralCardBackgroundSelected:"var(--colorNeutralCardBackgroundSelected)",colorNeutralCardBackgroundDisabled:"var(--colorNeutralCardBackgroundDisabled)",colorNeutralStrokeAccessible:"var(--colorNeutralStrokeAccessible)",colorNeutralStrokeAccessibleHover:"var(--colorNeutralStrokeAccessibleHover)",colorNeutralStrokeAccessiblePressed:"var(--colorNeutralStrokeAccessiblePressed)",colorNeutralStrokeAccessibleSelected:"var(--colorNeutralStrokeAccessibleSelected)",colorNeutralStroke1:"var(--colorNeutralStroke1)",colorNeutralStroke1Hover:"var(--colorNeutralStroke1Hover)",colorNeutralStroke1Pressed:"var(--colorNeutralStroke1Pressed)",colorNeutralStroke1Selected:"var(--colorNeutralStroke1Selected)",colorNeutralStroke2:"var(--colorNeutralStroke2)",colorNeutralStroke3:"var(--colorNeutralStroke3)",colorNeutralStrokeSubtle:"var(--colorNeutralStrokeSubtle)",colorNeutralStrokeOnBrand:"var(--colorNeutralStrokeOnBrand)",colorNeutralStrokeOnBrand2:"var(--colorNeutralStrokeOnBrand2)",colorNeutralStrokeOnBrand2Hover:"var(--colorNeutralStrokeOnBrand2Hover)",colorNeutralStrokeOnBrand2Pressed:"var(--colorNeutralStrokeOnBrand2Pressed)",colorNeutralStrokeOnBrand2Selected:"var(--colorNeutralStrokeOnBrand2Selected)",colorBrandStroke1:"var(--colorBrandStroke1)",colorBrandStroke2:"var(--colorBrandStroke2)",colorBrandStroke2Hover:"var(--colorBrandStroke2Hover)",colorBrandStroke2Pressed:"var(--colorBrandStroke2Pressed)",colorBrandStroke2Contrast:"var(--colorBrandStroke2Contrast)",colorCompoundBrandStroke:"var(--colorCompoundBrandStroke)",colorCompoundBrandStrokeHover:"var(--colorCompoundBrandStrokeHover)",colorCompoundBrandStrokePressed:"var(--colorCompoundBrandStrokePressed)",colorNeutralStrokeDisabled:"var(--colorNeutralStrokeDisabled)",colorNeutralStrokeInvertedDisabled:"var(--colorNeutralStrokeInvertedDisabled)",colorTransparentStroke:"var(--colorTransparentStroke)",colorTransparentStrokeInteractive:"var(--colorTransparentStrokeInteractive)",colorTransparentStrokeDisabled:"var(--colorTransparentStrokeDisabled)",colorNeutralStrokeAlpha:"var(--colorNeutralStrokeAlpha)",colorNeutralStrokeAlpha2:"var(--colorNeutralStrokeAlpha2)",colorStrokeFocus1:"var(--colorStrokeFocus1)",colorStrokeFocus2:"var(--colorStrokeFocus2)",colorNeutralShadowAmbient:"var(--colorNeutralShadowAmbient)",colorNeutralShadowKey:"var(--colorNeutralShadowKey)",colorNeutralShadowAmbientLighter:"var(--colorNeutralShadowAmbientLighter)",colorNeutralShadowKeyLighter:"var(--colorNeutralShadowKeyLighter)",colorNeutralShadowAmbientDarker:"var(--colorNeutralShadowAmbientDarker)",colorNeutralShadowKeyDarker:"var(--colorNeutralShadowKeyDarker)",colorBrandShadowAmbient:"var(--colorBrandShadowAmbient)",colorBrandShadowKey:"var(--colorBrandShadowKey)",colorPaletteRedBackground1:"var(--colorPaletteRedBackground1)",colorPaletteRedBackground2:"var(--colorPaletteRedBackground2)",colorPaletteRedBackground3:"var(--colorPaletteRedBackground3)",colorPaletteRedBorderActive:"var(--colorPaletteRedBorderActive)",colorPaletteRedBorder1:"var(--colorPaletteRedBorder1)",colorPaletteRedBorder2:"var(--colorPaletteRedBorder2)",colorPaletteRedForeground1:"var(--colorPaletteRedForeground1)",colorPaletteRedForeground2:"var(--colorPaletteRedForeground2)",colorPaletteRedForeground3:"var(--colorPaletteRedForeground3)",colorPaletteRedForegroundInverted:"var(--colorPaletteRedForegroundInverted)",colorPaletteGreenBackground1:"var(--colorPaletteGreenBackground1)",colorPaletteGreenBackground2:"var(--colorPaletteGreenBackground2)",colorPaletteGreenBackground3:"var(--colorPaletteGreenBackground3)",colorPaletteGreenBorderActive:"var(--colorPaletteGreenBorderActive)",colorPaletteGreenBorder1:"var(--colorPaletteGreenBorder1)",colorPaletteGreenBorder2:"var(--colorPaletteGreenBorder2)",colorPaletteGreenForeground1:"var(--colorPaletteGreenForeground1)",colorPaletteGreenForeground2:"var(--colorPaletteGreenForeground2)",colorPaletteGreenForeground3:"var(--colorPaletteGreenForeground3)",colorPaletteGreenForegroundInverted:"var(--colorPaletteGreenForegroundInverted)",colorPaletteDarkOrangeBackground1:"var(--colorPaletteDarkOrangeBackground1)",colorPaletteDarkOrangeBackground2:"var(--colorPaletteDarkOrangeBackground2)",colorPaletteDarkOrangeBackground3:"var(--colorPaletteDarkOrangeBackground3)",colorPaletteDarkOrangeBorderActive:"var(--colorPaletteDarkOrangeBorderActive)",colorPaletteDarkOrangeBorder1:"var(--colorPaletteDarkOrangeBorder1)",colorPaletteDarkOrangeBorder2:"var(--colorPaletteDarkOrangeBorder2)",colorPaletteDarkOrangeForeground1:"var(--colorPaletteDarkOrangeForeground1)",colorPaletteDarkOrangeForeground2:"var(--colorPaletteDarkOrangeForeground2)",colorPaletteDarkOrangeForeground3:"var(--colorPaletteDarkOrangeForeground3)",colorPaletteYellowBackground1:"var(--colorPaletteYellowBackground1)",colorPaletteYellowBackground2:"var(--colorPaletteYellowBackground2)",colorPaletteYellowBackground3:"var(--colorPaletteYellowBackground3)",colorPaletteYellowBorderActive:"var(--colorPaletteYellowBorderActive)",colorPaletteYellowBorder1:"var(--colorPaletteYellowBorder1)",colorPaletteYellowBorder2:"var(--colorPaletteYellowBorder2)",colorPaletteYellowForeground1:"var(--colorPaletteYellowForeground1)",colorPaletteYellowForeground2:"var(--colorPaletteYellowForeground2)",colorPaletteYellowForeground3:"var(--colorPaletteYellowForeground3)",colorPaletteYellowForegroundInverted:"var(--colorPaletteYellowForegroundInverted)",colorPaletteBerryBackground1:"var(--colorPaletteBerryBackground1)",colorPaletteBerryBackground2:"var(--colorPaletteBerryBackground2)",colorPaletteBerryBackground3:"var(--colorPaletteBerryBackground3)",colorPaletteBerryBorderActive:"var(--colorPaletteBerryBorderActive)",colorPaletteBerryBorder1:"var(--colorPaletteBerryBorder1)",colorPaletteBerryBorder2:"var(--colorPaletteBerryBorder2)",colorPaletteBerryForeground1:"var(--colorPaletteBerryForeground1)",colorPaletteBerryForeground2:"var(--colorPaletteBerryForeground2)",colorPaletteBerryForeground3:"var(--colorPaletteBerryForeground3)",colorPaletteMarigoldBackground1:"var(--colorPaletteMarigoldBackground1)",colorPaletteMarigoldBackground2:"var(--colorPaletteMarigoldBackground2)",colorPaletteMarigoldBackground3:"var(--colorPaletteMarigoldBackground3)",colorPaletteMarigoldBorderActive:"var(--colorPaletteMarigoldBorderActive)",colorPaletteMarigoldBorder1:"var(--colorPaletteMarigoldBorder1)",colorPaletteMarigoldBorder2:"var(--colorPaletteMarigoldBorder2)",colorPaletteMarigoldForeground1:"var(--colorPaletteMarigoldForeground1)",colorPaletteMarigoldForeground2:"var(--colorPaletteMarigoldForeground2)",colorPaletteMarigoldForeground3:"var(--colorPaletteMarigoldForeground3)",colorPaletteLightGreenBackground1:"var(--colorPaletteLightGreenBackground1)",colorPaletteLightGreenBackground2:"var(--colorPaletteLightGreenBackground2)",colorPaletteLightGreenBackground3:"var(--colorPaletteLightGreenBackground3)",colorPaletteLightGreenBorderActive:"var(--colorPaletteLightGreenBorderActive)",colorPaletteLightGreenBorder1:"var(--colorPaletteLightGreenBorder1)",colorPaletteLightGreenBorder2:"var(--colorPaletteLightGreenBorder2)",colorPaletteLightGreenForeground1:"var(--colorPaletteLightGreenForeground1)",colorPaletteLightGreenForeground2:"var(--colorPaletteLightGreenForeground2)",colorPaletteLightGreenForeground3:"var(--colorPaletteLightGreenForeground3)",colorPaletteAnchorBackground2:"var(--colorPaletteAnchorBackground2)",colorPaletteAnchorBorderActive:"var(--colorPaletteAnchorBorderActive)",colorPaletteAnchorForeground2:"var(--colorPaletteAnchorForeground2)",colorPaletteBeigeBackground2:"var(--colorPaletteBeigeBackground2)",colorPaletteBeigeBorderActive:"var(--colorPaletteBeigeBorderActive)",colorPaletteBeigeForeground2:"var(--colorPaletteBeigeForeground2)",colorPaletteBlueBackground2:"var(--colorPaletteBlueBackground2)",colorPaletteBlueBorderActive:"var(--colorPaletteBlueBorderActive)",colorPaletteBlueForeground2:"var(--colorPaletteBlueForeground2)",colorPaletteBrassBackground2:"var(--colorPaletteBrassBackground2)",colorPaletteBrassBorderActive:"var(--colorPaletteBrassBorderActive)",colorPaletteBrassForeground2:"var(--colorPaletteBrassForeground2)",colorPaletteBrownBackground2:"var(--colorPaletteBrownBackground2)",colorPaletteBrownBorderActive:"var(--colorPaletteBrownBorderActive)",colorPaletteBrownForeground2:"var(--colorPaletteBrownForeground2)",colorPaletteCornflowerBackground2:"var(--colorPaletteCornflowerBackground2)",colorPaletteCornflowerBorderActive:"var(--colorPaletteCornflowerBorderActive)",colorPaletteCornflowerForeground2:"var(--colorPaletteCornflowerForeground2)",colorPaletteCranberryBackground2:"var(--colorPaletteCranberryBackground2)",colorPaletteCranberryBorderActive:"var(--colorPaletteCranberryBorderActive)",colorPaletteCranberryForeground2:"var(--colorPaletteCranberryForeground2)",colorPaletteDarkGreenBackground2:"var(--colorPaletteDarkGreenBackground2)",colorPaletteDarkGreenBorderActive:"var(--colorPaletteDarkGreenBorderActive)",colorPaletteDarkGreenForeground2:"var(--colorPaletteDarkGreenForeground2)",colorPaletteDarkRedBackground2:"var(--colorPaletteDarkRedBackground2)",colorPaletteDarkRedBorderActive:"var(--colorPaletteDarkRedBorderActive)",colorPaletteDarkRedForeground2:"var(--colorPaletteDarkRedForeground2)",colorPaletteForestBackground2:"var(--colorPaletteForestBackground2)",colorPaletteForestBorderActive:"var(--colorPaletteForestBorderActive)",colorPaletteForestForeground2:"var(--colorPaletteForestForeground2)",colorPaletteGoldBackground2:"var(--colorPaletteGoldBackground2)",colorPaletteGoldBorderActive:"var(--colorPaletteGoldBorderActive)",colorPaletteGoldForeground2:"var(--colorPaletteGoldForeground2)",colorPaletteGrapeBackground2:"var(--colorPaletteGrapeBackground2)",colorPaletteGrapeBorderActive:"var(--colorPaletteGrapeBorderActive)",colorPaletteGrapeForeground2:"var(--colorPaletteGrapeForeground2)",colorPaletteLavenderBackground2:"var(--colorPaletteLavenderBackground2)",colorPaletteLavenderBorderActive:"var(--colorPaletteLavenderBorderActive)",colorPaletteLavenderForeground2:"var(--colorPaletteLavenderForeground2)",colorPaletteLightTealBackground2:"var(--colorPaletteLightTealBackground2)",colorPaletteLightTealBorderActive:"var(--colorPaletteLightTealBorderActive)",colorPaletteLightTealForeground2:"var(--colorPaletteLightTealForeground2)",colorPaletteLilacBackground2:"var(--colorPaletteLilacBackground2)",colorPaletteLilacBorderActive:"var(--colorPaletteLilacBorderActive)",colorPaletteLilacForeground2:"var(--colorPaletteLilacForeground2)",colorPaletteMagentaBackground2:"var(--colorPaletteMagentaBackground2)",colorPaletteMagentaBorderActive:"var(--colorPaletteMagentaBorderActive)",colorPaletteMagentaForeground2:"var(--colorPaletteMagentaForeground2)",colorPaletteMinkBackground2:"var(--colorPaletteMinkBackground2)",colorPaletteMinkBorderActive:"var(--colorPaletteMinkBorderActive)",colorPaletteMinkForeground2:"var(--colorPaletteMinkForeground2)",colorPaletteNavyBackground2:"var(--colorPaletteNavyBackground2)",colorPaletteNavyBorderActive:"var(--colorPaletteNavyBorderActive)",colorPaletteNavyForeground2:"var(--colorPaletteNavyForeground2)",colorPalettePeachBackground2:"var(--colorPalettePeachBackground2)",colorPalettePeachBorderActive:"var(--colorPalettePeachBorderActive)",colorPalettePeachForeground2:"var(--colorPalettePeachForeground2)",colorPalettePinkBackground2:"var(--colorPalettePinkBackground2)",colorPalettePinkBorderActive:"var(--colorPalettePinkBorderActive)",colorPalettePinkForeground2:"var(--colorPalettePinkForeground2)",colorPalettePlatinumBackground2:"var(--colorPalettePlatinumBackground2)",colorPalettePlatinumBorderActive:"var(--colorPalettePlatinumBorderActive)",colorPalettePlatinumForeground2:"var(--colorPalettePlatinumForeground2)",colorPalettePlumBackground2:"var(--colorPalettePlumBackground2)",colorPalettePlumBorderActive:"var(--colorPalettePlumBorderActive)",colorPalettePlumForeground2:"var(--colorPalettePlumForeground2)",colorPalettePumpkinBackground2:"var(--colorPalettePumpkinBackground2)",colorPalettePumpkinBorderActive:"var(--colorPalettePumpkinBorderActive)",colorPalettePumpkinForeground2:"var(--colorPalettePumpkinForeground2)",colorPalettePurpleBackground2:"var(--colorPalettePurpleBackground2)",colorPalettePurpleBorderActive:"var(--colorPalettePurpleBorderActive)",colorPalettePurpleForeground2:"var(--colorPalettePurpleForeground2)",colorPaletteRoyalBlueBackground2:"var(--colorPaletteRoyalBlueBackground2)",colorPaletteRoyalBlueBorderActive:"var(--colorPaletteRoyalBlueBorderActive)",colorPaletteRoyalBlueForeground2:"var(--colorPaletteRoyalBlueForeground2)",colorPaletteSeafoamBackground2:"var(--colorPaletteSeafoamBackground2)",colorPaletteSeafoamBorderActive:"var(--colorPaletteSeafoamBorderActive)",colorPaletteSeafoamForeground2:"var(--colorPaletteSeafoamForeground2)",colorPaletteSteelBackground2:"var(--colorPaletteSteelBackground2)",colorPaletteSteelBorderActive:"var(--colorPaletteSteelBorderActive)",colorPaletteSteelForeground2:"var(--colorPaletteSteelForeground2)",colorPaletteTealBackground2:"var(--colorPaletteTealBackground2)",colorPaletteTealBorderActive:"var(--colorPaletteTealBorderActive)",colorPaletteTealForeground2:"var(--colorPaletteTealForeground2)",colorStatusSuccessBackground1:"var(--colorStatusSuccessBackground1)",colorStatusSuccessBackground2:"var(--colorStatusSuccessBackground2)",colorStatusSuccessBackground3:"var(--colorStatusSuccessBackground3)",colorStatusSuccessForeground1:"var(--colorStatusSuccessForeground1)",colorStatusSuccessForeground2:"var(--colorStatusSuccessForeground2)",colorStatusSuccessForeground3:"var(--colorStatusSuccessForeground3)",colorStatusSuccessForegroundInverted:"var(--colorStatusSuccessForegroundInverted)",colorStatusSuccessBorderActive:"var(--colorStatusSuccessBorderActive)",colorStatusSuccessBorder1:"var(--colorStatusSuccessBorder1)",colorStatusSuccessBorder2:"var(--colorStatusSuccessBorder2)",colorStatusWarningBackground1:"var(--colorStatusWarningBackground1)",colorStatusWarningBackground2:"var(--colorStatusWarningBackground2)",colorStatusWarningBackground3:"var(--colorStatusWarningBackground3)",colorStatusWarningForeground1:"var(--colorStatusWarningForeground1)",colorStatusWarningForeground2:"var(--colorStatusWarningForeground2)",colorStatusWarningForeground3:"var(--colorStatusWarningForeground3)",colorStatusWarningForegroundInverted:"var(--colorStatusWarningForegroundInverted)",colorStatusWarningBorderActive:"var(--colorStatusWarningBorderActive)",colorStatusWarningBorder1:"var(--colorStatusWarningBorder1)",colorStatusWarningBorder2:"var(--colorStatusWarningBorder2)",colorStatusDangerBackground1:"var(--colorStatusDangerBackground1)",colorStatusDangerBackground2:"var(--colorStatusDangerBackground2)",colorStatusDangerBackground3:"var(--colorStatusDangerBackground3)",colorStatusDangerBackground3Hover:"var(--colorStatusDangerBackground3Hover)",colorStatusDangerBackground3Pressed:"var(--colorStatusDangerBackground3Pressed)",colorStatusDangerForeground1:"var(--colorStatusDangerForeground1)",colorStatusDangerForeground2:"var(--colorStatusDangerForeground2)",colorStatusDangerForeground3:"var(--colorStatusDangerForeground3)",colorStatusDangerForegroundInverted:"var(--colorStatusDangerForegroundInverted)",colorStatusDangerBorderActive:"var(--colorStatusDangerBorderActive)",colorStatusDangerBorder1:"var(--colorStatusDangerBorder1)",colorStatusDangerBorder2:"var(--colorStatusDangerBorder2)",borderRadiusNone:"var(--borderRadiusNone)",borderRadiusSmall:"var(--borderRadiusSmall)",borderRadiusMedium:"var(--borderRadiusMedium)",borderRadiusLarge:"var(--borderRadiusLarge)",borderRadiusXLarge:"var(--borderRadiusXLarge)",borderRadiusCircular:"var(--borderRadiusCircular)",fontFamilyBase:"var(--fontFamilyBase)",fontFamilyMonospace:"var(--fontFamilyMonospace)",fontFamilyNumeric:"var(--fontFamilyNumeric)",fontSizeBase100:"var(--fontSizeBase100)",fontSizeBase200:"var(--fontSizeBase200)",fontSizeBase300:"var(--fontSizeBase300)",fontSizeBase400:"var(--fontSizeBase400)",fontSizeBase500:"var(--fontSizeBase500)",fontSizeBase600:"var(--fontSizeBase600)",fontSizeHero700:"var(--fontSizeHero700)",fontSizeHero800:"var(--fontSizeHero800)",fontSizeHero900:"var(--fontSizeHero900)",fontSizeHero1000:"var(--fontSizeHero1000)",fontWeightRegular:"var(--fontWeightRegular)",fontWeightMedium:"var(--fontWeightMedium)",fontWeightSemibold:"var(--fontWeightSemibold)",fontWeightBold:"var(--fontWeightBold)",lineHeightBase100:"var(--lineHeightBase100)",lineHeightBase200:"var(--lineHeightBase200)",lineHeightBase300:"var(--lineHeightBase300)",lineHeightBase400:"var(--lineHeightBase400)",lineHeightBase500:"var(--lineHeightBase500)",lineHeightBase600:"var(--lineHeightBase600)",lineHeightHero700:"var(--lineHeightHero700)",lineHeightHero800:"var(--lineHeightHero800)",lineHeightHero900:"var(--lineHeightHero900)",lineHeightHero1000:"var(--lineHeightHero1000)",shadow2:"var(--shadow2)",shadow4:"var(--shadow4)",shadow8:"var(--shadow8)",shadow16:"var(--shadow16)",shadow28:"var(--shadow28)",shadow64:"var(--shadow64)",shadow2Brand:"var(--shadow2Brand)",shadow4Brand:"var(--shadow4Brand)",shadow8Brand:"var(--shadow8Brand)",shadow16Brand:"var(--shadow16Brand)",shadow28Brand:"var(--shadow28Brand)",shadow64Brand:"var(--shadow64Brand)",strokeWidthThin:"var(--strokeWidthThin)",strokeWidthThick:"var(--strokeWidthThick)",strokeWidthThicker:"var(--strokeWidthThicker)",strokeWidthThickest:"var(--strokeWidthThickest)",spacingHorizontalNone:"var(--spacingHorizontalNone)",spacingHorizontalXXS:"var(--spacingHorizontalXXS)",spacingHorizontalXS:"var(--spacingHorizontalXS)",spacingHorizontalSNudge:"var(--spacingHorizontalSNudge)",spacingHorizontalS:"var(--spacingHorizontalS)",spacingHorizontalMNudge:"var(--spacingHorizontalMNudge)",spacingHorizontalM:"var(--spacingHorizontalM)",spacingHorizontalL:"var(--spacingHorizontalL)",spacingHorizontalXL:"var(--spacingHorizontalXL)",spacingHorizontalXXL:"var(--spacingHorizontalXXL)",spacingHorizontalXXXL:"var(--spacingHorizontalXXXL)",spacingVerticalNone:"var(--spacingVerticalNone)",spacingVerticalXXS:"var(--spacingVerticalXXS)",spacingVerticalXS:"var(--spacingVerticalXS)",spacingVerticalSNudge:"var(--spacingVerticalSNudge)",spacingVerticalS:"var(--spacingVerticalS)",spacingVerticalMNudge:"var(--spacingVerticalMNudge)",spacingVerticalM:"var(--spacingVerticalM)",spacingVerticalL:"var(--spacingVerticalL)",spacingVerticalXL:"var(--spacingVerticalXL)",spacingVerticalXXL:"var(--spacingVerticalXXL)",spacingVerticalXXXL:"var(--spacingVerticalXXXL)",durationUltraFast:"var(--durationUltraFast)",durationFaster:"var(--durationFaster)",durationFast:"var(--durationFast)",durationNormal:"var(--durationNormal)",durationGentle:"var(--durationGentle)",durationSlow:"var(--durationSlow)",durationSlower:"var(--durationSlower)",durationUltraSlow:"var(--durationUltraSlow)",curveAccelerateMax:"var(--curveAccelerateMax)",curveAccelerateMid:"var(--curveAccelerateMid)",curveAccelerateMin:"var(--curveAccelerateMin)",curveDecelerateMax:"var(--curveDecelerateMax)",curveDecelerateMid:"var(--curveDecelerateMid)",curveDecelerateMin:"var(--curveDecelerateMin)",curveEasyEaseMax:"var(--curveEasyEaseMax)",curveEasyEase:"var(--curveEasyEase)",curveLinear:"var(--curveLinear)"};function Ml(e,t,r=""){return{[`shadow2${r}`]:`0 0 2px ${e}, 0 1px 2px ${t}`,[`shadow4${r}`]:`0 0 2px ${e}, 0 2px 4px ${t}`,[`shadow8${r}`]:`0 0 2px ${e}, 0 4px 8px ${t}`,[`shadow16${r}`]:`0 0 2px ${e}, 0 8px 16px ${t}`,[`shadow28${r}`]:`0 0 8px ${e}, 0 14px 28px ${t}`,[`shadow64${r}`]:`0 0 8px ${e}, 0 32px 64px ${t}`}}const CB=e=>{const t=FB(e);return{...Ph,...Oh,...Rh,...Ah,...zh,...Mh,...Lh,...Ih,...Dh,...Th,...t,...NB,...jo,...Ml(t.colorNeutralShadowAmbient,t.colorNeutralShadowKey),...Ml(t.colorBrandShadowAmbient,t.colorBrandShadowKey,"Brand")}},jh={10:"#061724",20:"#082338",30:"#0a2e4a",40:"#0c3b5e",50:"#0e4775",60:"#0f548c",70:"#115ea3",80:"#0f6cbd",90:"#2886de",100:"#479ef5",110:"#62abf5",120:"#77b7f7",130:"#96c6fa",140:"#b4d6fa",150:"#cfe4fa",160:"#ebf3fc"},Rt=Fh.reduce((e,t)=>{const r=t.slice(0,1).toUpperCase()+t.slice(1),o={[`colorPalette${r}Background1`]:K[t].shade40,[`colorPalette${r}Background2`]:K[t].shade30,[`colorPalette${r}Background3`]:K[t].primary,[`colorPalette${r}Foreground1`]:K[t].tint30,[`colorPalette${r}Foreground2`]:K[t].tint40,[`colorPalette${r}Foreground3`]:K[t].tint20,[`colorPalette${r}BorderActive`]:K[t].tint30,[`colorPalette${r}Border1`]:K[t].primary,[`colorPalette${r}Border2`]:K[t].tint20};return Object.assign(e,o)},{});Rt.colorPaletteRedForeground3=K.red.tint30,Rt.colorPaletteRedBorder2=K.red.tint30,Rt.colorPaletteGreenForeground3=K.green.tint40,Rt.colorPaletteGreenBorder2=K.green.tint40,Rt.colorPaletteDarkOrangeForeground3=K.darkOrange.tint30,Rt.colorPaletteDarkOrangeBorder2=K.darkOrange.tint30,Rt.colorPaletteRedForegroundInverted=K.red.primary,Rt.colorPaletteGreenForegroundInverted=K.green.primary,Rt.colorPaletteYellowForegroundInverted=K.yellow.shade30;const Ku=Ch.reduce((e,t)=>{const r=t.slice(0,1).toUpperCase()+t.slice(1),o={[`colorPalette${r}Background2`]:Sr[t].shade30,[`colorPalette${r}Foreground2`]:Sr[t].tint40,[`colorPalette${r}BorderActive`]:Sr[t].tint30};return Object.assign(e,o)},{});Ku.colorPaletteDarkRedBackground2=Sr.darkRed.shade20,Ku.colorPalettePlumBackground2=Sr.plum.shade20;const PB={...Rt,...Ku},Er=Object.entries(Ke).reduce((e,[t,r])=>{const o=t.slice(0,1).toUpperCase()+t.slice(1),n={[`colorStatus${o}Background1`]:U[r].shade40,[`colorStatus${o}Background2`]:U[r].shade30,[`colorStatus${o}Background3`]:U[r].primary,[`colorStatus${o}Foreground1`]:U[r].tint30,[`colorStatus${o}Foreground2`]:U[r].tint40,[`colorStatus${o}Foreground3`]:U[r].tint20,[`colorStatus${o}BorderActive`]:U[r].tint30,[`colorStatus${o}ForegroundInverted`]:U[r].shade10,[`colorStatus${o}Border1`]:U[r].primary,[`colorStatus${o}Border2`]:U[r].tint20};return Object.assign(e,n)},{});Er.colorStatusDangerBackground3Hover=U[Ke.danger].shade10,Er.colorStatusDangerBackground3Pressed=U[Ke.danger].shade20,Er.colorStatusDangerForeground3=U[Ke.danger].tint40,Er.colorStatusDangerBorder2=U[Ke.danger].tint30,Er.colorStatusSuccessForeground3=U[Ke.success].tint40,Er.colorStatusSuccessBorder2=U[Ke.success].tint40,Er.colorStatusWarningForegroundInverted=U[Ke.warning].shade20;const Hh=CB(jh),TB=e=>({colorNeutralForeground1:M,colorNeutralForeground1Hover:M,colorNeutralForeground1Pressed:M,colorNeutralForeground1Selected:M,colorNeutralForeground2:N[84],colorNeutralForeground2Hover:M,colorNeutralForeground2Pressed:M,colorNeutralForeground2Selected:M,colorNeutralForeground2BrandHover:e[100],colorNeutralForeground2BrandPressed:e[90],colorNeutralForeground2BrandSelected:e[100],colorNeutralForeground3:N[68],colorNeutralForeground3Hover:N[84],colorNeutralForeground3Pressed:N[84],colorNeutralForeground3Selected:N[84],colorNeutralForeground3BrandHover:e[100],colorNeutralForeground3BrandPressed:e[90],colorNeutralForeground3BrandSelected:e[100],colorNeutralForeground4:N[60],colorNeutralForegroundDisabled:N[36],colorNeutralForegroundInvertedDisabled:Ce[40],colorBrandForegroundLink:e[100],colorBrandForegroundLinkHover:e[110],colorBrandForegroundLinkPressed:e[90],colorBrandForegroundLinkSelected:e[100],colorNeutralForeground2Link:N[84],colorNeutralForeground2LinkHover:M,colorNeutralForeground2LinkPressed:M,colorNeutralForeground2LinkSelected:M,colorCompoundBrandForeground1:e[100],colorCompoundBrandForeground1Hover:e[110],colorCompoundBrandForeground1Pressed:e[90],colorBrandForeground1:e[100],colorBrandForeground2:e[110],colorBrandForeground2Hover:e[130],colorBrandForeground2Pressed:e[160],colorNeutralForeground1Static:N[14],colorNeutralForegroundStaticInverted:M,colorNeutralForegroundInverted:N[14],colorNeutralForegroundInvertedHover:N[14],colorNeutralForegroundInvertedPressed:N[14],colorNeutralForegroundInvertedSelected:N[14],colorNeutralForegroundInverted2:N[14],colorNeutralForegroundOnBrand:M,colorNeutralForegroundInvertedLink:M,colorNeutralForegroundInvertedLinkHover:M,colorNeutralForegroundInvertedLinkPressed:M,colorNeutralForegroundInvertedLinkSelected:M,colorBrandForegroundInverted:e[80],colorBrandForegroundInvertedHover:e[70],colorBrandForegroundInvertedPressed:e[60],colorBrandForegroundOnLight:e[80],colorBrandForegroundOnLightHover:e[70],colorBrandForegroundOnLightPressed:e[50],colorBrandForegroundOnLightSelected:e[60],colorNeutralBackground1:N[16],colorNeutralBackground1Hover:N[24],colorNeutralBackground1Pressed:N[12],colorNeutralBackground1Selected:N[22],colorNeutralBackground2:N[12],colorNeutralBackground2Hover:N[20],colorNeutralBackground2Pressed:N[8],colorNeutralBackground2Selected:N[18],colorNeutralBackground3:N[8],colorNeutralBackground3Hover:N[16],colorNeutralBackground3Pressed:N[4],colorNeutralBackground3Selected:N[14],colorNeutralBackground4:N[4],colorNeutralBackground4Hover:N[12],colorNeutralBackground4Pressed:Ll,colorNeutralBackground4Selected:N[10],colorNeutralBackground5:Ll,colorNeutralBackground5Hover:N[8],colorNeutralBackground5Pressed:N[2],colorNeutralBackground5Selected:N[6],colorNeutralBackground6:N[20],colorNeutralBackgroundInverted:M,colorNeutralBackgroundStatic:N[24],colorNeutralBackgroundAlpha:qw[50],colorNeutralBackgroundAlpha2:Uw[70],colorSubtleBackground:"transparent",colorSubtleBackgroundHover:N[22],colorSubtleBackgroundPressed:N[18],colorSubtleBackgroundSelected:N[20],colorSubtleBackgroundLightAlphaHover:Sh[80],colorSubtleBackgroundLightAlphaPressed:Sh[50],colorSubtleBackgroundLightAlphaSelected:"transparent",colorSubtleBackgroundInverted:"transparent",colorSubtleBackgroundInvertedHover:dt[10],colorSubtleBackgroundInvertedPressed:dt[30],colorSubtleBackgroundInvertedSelected:dt[20],colorTransparentBackground:"transparent",colorTransparentBackgroundHover:"transparent",colorTransparentBackgroundPressed:"transparent",colorTransparentBackgroundSelected:"transparent",colorNeutralBackgroundDisabled:N[8],colorNeutralBackgroundInvertedDisabled:Ce[10],colorNeutralStencil1:N[34],colorNeutralStencil2:N[20],colorNeutralStencil1Alpha:Ce[10],colorNeutralStencil2Alpha:Ce[5],colorBackgroundOverlay:dt[50],colorScrollbarOverlay:Ce[60],colorBrandBackground:e[70],colorBrandBackgroundHover:e[80],colorBrandBackgroundPressed:e[40],colorBrandBackgroundSelected:e[60],colorCompoundBrandBackground:e[100],colorCompoundBrandBackgroundHover:e[110],colorCompoundBrandBackgroundPressed:e[90],colorBrandBackgroundStatic:e[80],colorBrandBackground2:e[20],colorBrandBackground2Hover:e[40],colorBrandBackground2Pressed:e[10],colorBrandBackground3Static:e[60],colorBrandBackground4Static:e[40],colorBrandBackgroundInverted:M,colorBrandBackgroundInvertedHover:e[160],colorBrandBackgroundInvertedPressed:e[140],colorBrandBackgroundInvertedSelected:e[150],colorNeutralCardBackground:N[20],colorNeutralCardBackgroundHover:N[24],colorNeutralCardBackgroundPressed:N[18],colorNeutralCardBackgroundSelected:N[22],colorNeutralCardBackgroundDisabled:N[8],colorNeutralStrokeAccessible:N[68],colorNeutralStrokeAccessibleHover:N[74],colorNeutralStrokeAccessiblePressed:N[70],colorNeutralStrokeAccessibleSelected:e[100],colorNeutralStroke1:N[40],colorNeutralStroke1Hover:N[46],colorNeutralStroke1Pressed:N[42],colorNeutralStroke1Selected:N[44],colorNeutralStroke2:N[32],colorNeutralStroke3:N[24],colorNeutralStrokeSubtle:N[4],colorNeutralStrokeOnBrand:N[16],colorNeutralStrokeOnBrand2:M,colorNeutralStrokeOnBrand2Hover:M,colorNeutralStrokeOnBrand2Pressed:M,colorNeutralStrokeOnBrand2Selected:M,colorBrandStroke1:e[100],colorBrandStroke2:e[50],colorBrandStroke2Hover:e[50],colorBrandStroke2Pressed:e[30],colorBrandStroke2Contrast:e[50],colorCompoundBrandStroke:e[100],colorCompoundBrandStrokeHover:e[110],colorCompoundBrandStrokePressed:e[90],colorNeutralStrokeDisabled:N[26],colorNeutralStrokeInvertedDisabled:Ce[40],colorTransparentStroke:"transparent",colorTransparentStrokeInteractive:"transparent",colorTransparentStrokeDisabled:"transparent",colorNeutralStrokeAlpha:Ce[10],colorNeutralStrokeAlpha2:Ce[20],colorStrokeFocus1:Ll,colorStrokeFocus2:M,colorNeutralShadowAmbient:"rgba(0,0,0,0.24)",colorNeutralShadowKey:"rgba(0,0,0,0.28)",colorNeutralShadowAmbientLighter:"rgba(0,0,0,0.12)",colorNeutralShadowKeyLighter:"rgba(0,0,0,0.14)",colorNeutralShadowAmbientDarker:"rgba(0,0,0,0.40)",colorNeutralShadowKeyDarker:"rgba(0,0,0,0.48)",colorBrandShadowAmbient:"rgba(0,0,0,0.30)",colorBrandShadowKey:"rgba(0,0,0,0.25)"}),Wh=(e=>{const t=TB(e);return{...Ph,...Oh,...Rh,...Ah,...zh,...Mh,...Lh,...Ih,...Dh,...Th,...t,...PB,...Er,...Ml(t.colorNeutralShadowAmbient,t.colorNeutralShadowKey),...Ml(t.colorBrandShadowAmbient,t.colorBrandShadowKey,"Brand")}})(jh),$h={root:"fui-FluentProvider"},DB=Ep({root:{sj55zd:"f19n0e5",De3pzq:"fxugw4r",fsow6f:["f1o700av","fes3tcz"],Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bhrd7zp:"figsok6",Bg96gwp:"f1i3iumi"}},{d:[".f19n0e5{color:var(--colorNeutralForeground1);}",".fxugw4r{background-color:var(--colorNeutralBackground1);}",".f1o700av{text-align:left;}",".fes3tcz{text-align:right;}",".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".f1i3iumi{line-height:var(--lineHeightBase300);}"]}),OB=e=>{const t=qn(),r=DB({dir:e.dir,renderer:t});return e.root.className=_e($h.root,e.themeClassName,r.root,e.root.className),e},RB=lo["useInsertionEffect"]?lo["useInsertionEffect"]:Yt,zB=(e,t)=>{if(!e)return;const r=e.createElement("style");return Object.keys(t).forEach(o=>{r.setAttribute(o,t[o])}),e.head.appendChild(r),r},AB=(e,t)=>{const r=e.sheet;r&&(r.cssRules.length>0&&r.deleteRule(0),r.insertRule(t,0))},LB=e=>{const{targetDocument:t,theme:r,rendererAttributes:o}=e,n=x.exports.useRef(),i=Fu($h.root),l=o,s=x.exports.useMemo(()=>fk(`.${i}`,r),[r,i]);return IB(t,i),RB(()=>{const a=t==null?void 0:t.getElementById(i);return a?n.current=a:(n.current=zB(t,{...l,id:i}),n.current&&AB(n.current,s)),()=>{var u;(u=n.current)===null||u===void 0||u.remove()}},[i,t,s,l]),{styleTagId:i,rule:s}};function IB(e,t){x.exports.useState(()=>{if(!e)return;const r=e.getElementById(t);r&&e.head.append(r)})}const MB={},jB={},HB=(e,t)=>{const r=Dt(),o=WB(),n=ob(),i=x.exports.useContext(Pu)||MB,{applyStylesToPortals:l=!0,customStyleHooks_unstable:s,dir:a=r.dir,targetDocument:u=r.targetDocument,theme:d,overrides_unstable:f={}}=e,g=Xu(o,d),y=Xu(n,f),v=Xu(i,s),k=qn();var _;const{styleTagId:m,rule:h}=LB({theme:g,targetDocument:u,rendererAttributes:(_=k.styleElementAttributes)!==null&&_!==void 0?_:jB});return{applyStylesToPortals:l,customStyleHooks_unstable:v,dir:a,targetDocument:u,theme:g,overrides_unstable:y,themeClassName:m,components:{root:"div"},root:wt(Xt("div",{...e,dir:a,ref:Cu(t,xh({targetDocument:u}))}),{elementType:"div"}),serverStyleProps:{cssRule:h,attributes:{...k.styleElementAttributes,id:m}}}};function Xu(e,t){return e&&t?{...e,...t}:e||t}function WB(){return x.exports.useContext(Lp)}function $B(e){const{applyStylesToPortals:t,customStyleHooks_unstable:r,dir:o,root:n,targetDocument:i,theme:l,themeClassName:s,overrides_unstable:a}=e,u=x.exports.useMemo(()=>({dir:o,targetDocument:i}),[o,i]),[d]=x.exports.useState(()=>({})),f=x.exports.useMemo(()=>({textDirection:o}),[o]);return{customStyleHooks_unstable:r,overrides_unstable:a,provider:u,textDirection:o,iconDirection:f,tooltip:d,theme:l,themeClassName:t?n.className:s}}const Yu=x.exports.forwardRef((e,t)=>{const r=HB(e,t);OB(r);const o=$B(r);return Tb(r,o)});Yu.displayName="FluentProvider";var Qu={exports:{}},qh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(S,T){var D=S.length;S.push(T);e:for(;0<D;){var j=D-1>>>1,$=S[j];if(0<n($,T))S[j]=T,S[D]=$,D=j;else break e}}function r(S){return S.length===0?null:S[0]}function o(S){if(S.length===0)return null;var T=S[0],D=S.pop();if(D!==T){S[0]=D;e:for(var j=0,$=S.length,tr=$>>>1;j<tr;){var Le=2*(j+1)-1,no=S[Le],Ie=Le+1,rr=S[Ie];if(0>n(no,D))Ie<$&&0>n(rr,no)?(S[j]=rr,S[Ie]=D,j=Ie):(S[j]=no,S[Le]=D,j=Le);else if(Ie<$&&0>n(rr,D))S[j]=rr,S[Ie]=D,j=Ie;else break e}}return T}function n(S,T){var D=S.sortIndex-T.sortIndex;return D!==0?D:S.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,f=null,g=3,y=!1,v=!1,k=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function c(S){for(var T=r(u);T!==null;){if(T.callback===null)o(u);else if(T.startTime<=S)o(u),T.sortIndex=T.expirationTime,t(a,T);else break;T=r(u)}}function p(S){if(k=!1,c(S),!v)if(r(a)!==null)v=!0,re(b);else{var T=r(u);T!==null&&ce(p,T.startTime-S)}}function b(S,T){v=!1,k&&(k=!1,m(E),E=-1),y=!0;var D=g;try{for(c(T),f=r(a);f!==null&&(!(f.expirationTime>T)||S&&!A());){var j=f.callback;if(typeof j=="function"){f.callback=null,g=f.priorityLevel;var $=j(f.expirationTime<=T);T=e.unstable_now(),typeof $=="function"?f.callback=$:f===r(a)&&o(a),c(T)}else o(a);f=r(a)}if(f!==null)var tr=!0;else{var Le=r(u);Le!==null&&ce(p,Le.startTime-T),tr=!1}return tr}finally{f=null,g=D,y=!1}}var w=!1,B=null,E=-1,O=5,F=-1;function A(){return!(e.unstable_now()-F<O)}function I(){if(B!==null){var S=e.unstable_now();F=S;var T=!0;try{T=B(!0,S)}finally{T?X():(w=!1,B=null)}}else w=!1}var X;if(typeof h=="function")X=function(){h(I)};else if(typeof MessageChannel<"u"){var L=new MessageChannel,H=L.port2;L.port1.onmessage=I,X=function(){H.postMessage(null)}}else X=function(){_(I,0)};function re(S){B=S,w||(w=!0,X())}function ce(S,T){E=_(function(){S(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(S){S.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,re(b))},e.unstable_forceFrameRate=function(S){0>S||125<S?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<S?Math.floor(1e3/S):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return r(a)},e.unstable_next=function(S){switch(g){case 1:case 2:case 3:var T=3;break;default:T=g}var D=g;g=T;try{return S()}finally{g=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(S,T){switch(S){case 1:case 2:case 3:case 4:case 5:break;default:S=3}var D=g;g=S;try{return T()}finally{g=D}},e.unstable_scheduleCallback=function(S,T,D){var j=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?j+D:j):D=j,S){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=D+$,S={id:d++,callback:T,priorityLevel:S,startTime:D,expirationTime:$,sortIndex:-1},D>j?(S.sortIndex=D,t(u,S),r(a)===null&&S===r(u)&&(k?(m(E),E=-1):k=!0,ce(p,D-j))):(S.sortIndex=$,t(a,S),v||y||(v=!0,re(b))),S},e.unstable_shouldYield=A,e.unstable_wrapCallback=function(S){var T=g;return function(){var D=g;g=T;try{return S.apply(this,arguments)}finally{g=D}}}})(qh),function(e){e.exports=qh}(Qu);const qB=e=>r=>{const o=x.exports.useRef(r.value),n=x.exports.useRef(0),i=x.exports.useRef();return i.current||(i.current={value:o,version:n,listeners:[]}),Yt(()=>{o.current=r.value,n.current+=1,Qu.exports.unstable_runWithPriority(Qu.exports.unstable_NormalPriority,()=>{i.current.listeners.forEach(l=>{l([n.current,r.value])})})},[r.value]),x.exports.createElement(e,{value:i.current},r.children)},Uh=e=>{const t=x.exports.createContext({value:{current:e},version:{current:-1},listeners:[]});return t.Provider=qB(t.Provider),delete t.Consumer,t},Vh=(e,t)=>{const r=x.exports.useContext(e),{value:{current:o},version:{current:n},listeners:i}=r,l=t(o),[s,a]=x.exports.useReducer((u,d)=>{if(!d)return[o,l];if(d[0]<=n)return jl(u[1],l)?u:[o,l];try{if(jl(u[0],d[1]))return u;const f=t(d[1]);return jl(u[1],f)?u:[d[1],f]}catch{}return[u[0],u[1]]},[o,l]);return jl(s[1],l)||a(void 0),Yt(()=>(i.push(a),()=>{const u=i.indexOf(a);i.splice(u,1)}),[i]),s[1]};function UB(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const jl=typeof Object.is=="function"?Object.is:UB;function VB(e){const t=x.exports.useContext(e);return t.version?t.version.current!==-1:!1}const Ju="Enter",Hl=" ",KB="Escape";function Kh(e,t){const{disabled:r,disabledFocusable:o=!1,["aria-disabled"]:n,onClick:i,onKeyDown:l,onKeyUp:s,...a}=t!=null?t:{},u=typeof n=="string"?n==="true":n,d=r||o||u,f=ze(v=>{d?(v.preventDefault(),v.stopPropagation()):i==null||i(v)}),g=ze(v=>{if(l==null||l(v),v.isDefaultPrevented())return;const k=v.key;if(d&&(k===Ju||k===Hl)){v.preventDefault(),v.stopPropagation();return}if(k===Hl){v.preventDefault();return}else k===Ju&&(v.preventDefault(),v.currentTarget.click())}),y=ze(v=>{if(s==null||s(v),v.isDefaultPrevented())return;const k=v.key;if(d&&(k===Ju||k===Hl)){v.preventDefault(),v.stopPropagation();return}k===Hl&&(v.preventDefault(),v.currentTarget.click())});if(e==="button"||e===void 0)return{...a,disabled:r&&!o,"aria-disabled":o?!0:u,onClick:o?void 0:f,onKeyUp:o?void 0:s,onKeyDown:o?void 0:l};{const v={role:"button",tabIndex:r&&!o?void 0:0,...a,onClick:f,onKeyUp:y,onKeyDown:g,"aria-disabled":r||o||u};return e==="a"&&d&&(v.href=void 0),v}}function XB(e){const t=e.clientX,r=e.clientY,o=t+1,n=r+1;function i(){return{left:t,top:r,right:o,bottom:n,x:t,y:r,height:1,width:1}}return{getBoundingClientRect:i}}const Xh="data-popper-is-intersecting",Yh="data-popper-escaped",Qh="data-popper-reference-hidden",YB="data-popper-placement",Zu="fui-positioningend",Jh=["top","right","bottom","left"],Zh=["start","end"],Gh=Jh.reduce((e,t)=>e.concat(t,t+"-"+Zh[0],t+"-"+Zh[1]),[]),zt=Math.min,Ae=Math.max,Wl=Math.round,Nr=e=>({x:e,y:e}),QB={left:"right",right:"left",bottom:"top",top:"bottom"},JB={start:"end",end:"start"};function Gu(e,t,r){return Ae(e,zt(t,r))}function _t(e,t){return typeof e=="function"?e(t):e}function tt(e){return e.split("-")[0]}function xt(e){return e.split("-")[1]}function ec(e){return e==="x"?"y":"x"}function tc(e){return e==="y"?"height":"width"}function Gr(e){return["top","bottom"].includes(tt(e))?"y":"x"}function rc(e){return ec(Gr(e))}function ev(e,t,r){r===void 0&&(r=!1);const o=xt(e),n=rc(e),i=tc(n);let l=n==="x"?o===(r?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ql(l)),[l,ql(l)]}function ZB(e){const t=ql(e);return[$l(e),t,$l(t)]}function $l(e){return e.replace(/start|end/g,t=>JB[t])}function GB(e,t,r){const o=["left","right"],n=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return r?t?n:o:t?o:n;case"left":case"right":return t?i:l;default:return[]}}function e_(e,t,r,o){const n=xt(e);let i=GB(tt(e),r==="start",o);return n&&(i=i.map(l=>l+"-"+n),t&&(i=i.concat(i.map($l)))),i}function ql(e){return e.replace(/left|right|bottom|top/g,t=>QB[t])}function t_(e){return{top:0,right:0,bottom:0,left:0,...e}}function oc(e){return typeof e!="number"?t_(e):{top:e,right:e,bottom:e,left:e}}function Ho(e){const{x:t,y:r,width:o,height:n}=e;return{width:o,height:n,top:r,left:t,right:t+o,bottom:r+n,x:t,y:r}}function tv(e,t,r){let{reference:o,floating:n}=e;const i=Gr(t),l=rc(t),s=tc(l),a=tt(t),u=i==="y",d=o.x+o.width/2-n.width/2,f=o.y+o.height/2-n.height/2,g=o[s]/2-n[s]/2;let y;switch(a){case"top":y={x:d,y:o.y-n.height};break;case"bottom":y={x:d,y:o.y+o.height};break;case"right":y={x:o.x+o.width,y:f};break;case"left":y={x:o.x-n.width,y:f};break;default:y={x:o.x,y:o.y}}switch(xt(t)){case"start":y[l]-=g*(r&&u?-1:1);break;case"end":y[l]+=g*(r&&u?-1:1);break}return y}const r_=async(e,t,r)=>{const{placement:o="bottom",strategy:n="absolute",middleware:i=[],platform:l}=r,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:n}),{x:d,y:f}=tv(u,o,a),g=o,y={},v=0;for(let k=0;k<s.length;k++){const{name:_,fn:m}=s[k],{x:h,y:c,data:p,reset:b}=await m({x:d,y:f,initialPlacement:o,placement:g,strategy:n,middlewareData:y,rects:u,platform:l,elements:{reference:e,floating:t}});d=h!=null?h:d,f=c!=null?c:f,y={...y,[_]:{...y[_],...p}},b&&v<=50&&(v++,typeof b=="object"&&(b.placement&&(g=b.placement),b.rects&&(u=b.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:n}):b.rects),{x:d,y:f}=tv(u,g,a)),k=-1)}return{x:d,y:f,placement:g,strategy:n,middlewareData:y}};async function eo(e,t){var r;t===void 0&&(t={});const{x:o,y:n,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:g=!1,padding:y=0}=_t(t,e),v=oc(y),_=s[g?f==="floating"?"reference":"floating":f],m=Ho(await i.getClippingRect({element:(r=await(i.isElement==null?void 0:i.isElement(_)))==null||r?_:_.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:d,strategy:a})),h=f==="floating"?{x:o,y:n,width:l.floating.width,height:l.floating.height}:l.reference,c=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),p=await(i.isElement==null?void 0:i.isElement(c))?await(i.getScale==null?void 0:i.getScale(c))||{x:1,y:1}:{x:1,y:1},b=Ho(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:h,offsetParent:c,strategy:a}):h);return{top:(m.top-b.top+v.top)/p.y,bottom:(b.bottom-m.bottom+v.bottom)/p.y,left:(m.left-b.left+v.left)/p.x,right:(b.right-m.right+v.right)/p.x}}const o_=e=>({name:"arrow",options:e,async fn(t){const{x:r,y:o,placement:n,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:u,padding:d=0}=_t(e,t)||{};if(u==null)return{};const f=oc(d),g={x:r,y:o},y=rc(n),v=tc(y),k=await l.getDimensions(u),_=y==="y",m=_?"top":"left",h=_?"bottom":"right",c=_?"clientHeight":"clientWidth",p=i.reference[v]+i.reference[y]-g[y]-i.floating[v],b=g[y]-i.reference[y],w=await(l.getOffsetParent==null?void 0:l.getOffsetParent(u));let B=w?w[c]:0;(!B||!await(l.isElement==null?void 0:l.isElement(w)))&&(B=s.floating[c]||i.floating[v]);const E=p/2-b/2,O=B/2-k[v]/2-1,F=zt(f[m],O),A=zt(f[h],O),I=F,X=B-k[v]-A,L=B/2-k[v]/2+E,H=Gu(I,L,X),re=!a.arrow&&xt(n)!=null&&L!==H&&i.reference[v]/2-(L<I?F:A)-k[v]/2<0,ce=re?L<I?L-I:L-X:0;return{[y]:g[y]+ce,data:{[y]:H,centerOffset:L-H-ce,...re&&{alignmentOffset:ce}},reset:re}}});function n_(e,t,r){return(e?[...r.filter(n=>xt(n)===e),...r.filter(n=>xt(n)!==e)]:r.filter(n=>tt(n)===n)).filter(n=>e?xt(n)===e||(t?$l(n)!==n:!1):!0)}const i_=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var r,o,n;const{rects:i,middlewareData:l,placement:s,platform:a,elements:u}=t,{crossAxis:d=!1,alignment:f,allowedPlacements:g=Gh,autoAlignment:y=!0,...v}=_t(e,t),k=f!==void 0||g===Gh?n_(f||null,y,g):g,_=await eo(t,v),m=((r=l.autoPlacement)==null?void 0:r.index)||0,h=k[m];if(h==null)return{};const c=ev(h,i,await(a.isRTL==null?void 0:a.isRTL(u.floating)));if(s!==h)return{reset:{placement:k[0]}};const p=[_[tt(h)],_[c[0]],_[c[1]]],b=[...((o=l.autoPlacement)==null?void 0:o.overflows)||[],{placement:h,overflows:p}],w=k[m+1];if(w)return{data:{index:m+1,overflows:b},reset:{placement:w}};const B=b.map(F=>{const A=xt(F.placement);return[F.placement,A&&d?F.overflows.slice(0,2).reduce((I,X)=>I+X,0):F.overflows[0],F.overflows]}).sort((F,A)=>F[1]-A[1]),O=((n=B.filter(F=>F[2].slice(0,xt(F[0])?2:3).every(A=>A<=0))[0])==null?void 0:n[0])||B[0][0];return O!==s?{data:{index:m+1,overflows:b},reset:{placement:O}}:{}}}},l_=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,o;const{placement:n,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:u}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:g,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:k=!0,..._}=_t(e,t);if((r=i.arrow)!=null&&r.alignmentOffset)return{};const m=tt(n),h=tt(s)===s,c=await(a.isRTL==null?void 0:a.isRTL(u.floating)),p=g||(h||!k?[ql(s)]:ZB(s));!g&&v!=="none"&&p.push(...e_(s,k,v,c));const b=[s,...p],w=await eo(t,_),B=[];let E=((o=i.flip)==null?void 0:o.overflows)||[];if(d&&B.push(w[m]),f){const I=ev(n,l,c);B.push(w[I[0]],w[I[1]])}if(E=[...E,{placement:n,overflows:B}],!B.every(I=>I<=0)){var O,F;const I=(((O=i.flip)==null?void 0:O.index)||0)+1,X=b[I];if(X)return{data:{index:I,overflows:E},reset:{placement:X}};let L=(F=E.filter(H=>H.overflows[0]<=0).sort((H,re)=>H.overflows[1]-re.overflows[1])[0])==null?void 0:F.placement;if(!L)switch(y){case"bestFit":{var A;const H=(A=E.map(re=>[re.placement,re.overflows.filter(ce=>ce>0).reduce((ce,S)=>ce+S,0)]).sort((re,ce)=>re[1]-ce[1])[0])==null?void 0:A[0];H&&(L=H);break}case"initialPlacement":L=s;break}if(n!==L)return{reset:{placement:L}}}return{}}}};function rv(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ov(e){return Jh.some(t=>e[t]>=0)}const s_=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:r}=t,{strategy:o="referenceHidden",...n}=_t(e,t);switch(o){case"referenceHidden":{const i=await eo(t,{...n,elementContext:"reference"}),l=rv(i,r.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:ov(l)}}}case"escaped":{const i=await eo(t,{...n,altBoundary:!0}),l=rv(i,r.floating);return{data:{escapedOffsets:l,escaped:ov(l)}}}default:return{}}}}};function nv(e){const t=zt(...e.map(i=>i.left)),r=zt(...e.map(i=>i.top)),o=Ae(...e.map(i=>i.right)),n=Ae(...e.map(i=>i.bottom));return{x:t,y:r,width:o-t,height:n-r}}function a_(e){const t=e.slice().sort((n,i)=>n.y-i.y),r=[];let o=null;for(let n=0;n<t.length;n++){const i=t[n];!o||i.y-o.y>o.height/2?r.push([i]):r[r.length-1].push(i),o=i}return r.map(n=>Ho(nv(n)))}const u_=function(e){return e===void 0&&(e={}),{name:"inline",options:e,async fn(t){const{placement:r,elements:o,rects:n,platform:i,strategy:l}=t,{padding:s=2,x:a,y:u}=_t(e,t),d=Array.from(await(i.getClientRects==null?void 0:i.getClientRects(o.reference))||[]),f=a_(d),g=Ho(nv(d)),y=oc(s);function v(){if(f.length===2&&f[0].left>f[1].right&&a!=null&&u!=null)return f.find(_=>a>_.left-y.left&&a<_.right+y.right&&u>_.top-y.top&&u<_.bottom+y.bottom)||g;if(f.length>=2){if(Gr(r)==="y"){const F=f[0],A=f[f.length-1],I=tt(r)==="top",X=F.top,L=A.bottom,H=I?F.left:A.left,re=I?F.right:A.right,ce=re-H,S=L-X;return{top:X,bottom:L,left:H,right:re,width:ce,height:S,x:H,y:X}}const _=tt(r)==="left",m=Ae(...f.map(F=>F.right)),h=zt(...f.map(F=>F.left)),c=f.filter(F=>_?F.left===h:F.right===m),p=c[0].top,b=c[c.length-1].bottom,w=h,B=m,E=B-w,O=b-p;return{top:p,bottom:b,left:w,right:B,width:E,height:O,x:w,y:p}}return g}const k=await i.getElementRects({reference:{getBoundingClientRect:v},floating:o.floating,strategy:l});return n.reference.x!==k.reference.x||n.reference.y!==k.reference.y||n.reference.width!==k.reference.width||n.reference.height!==k.reference.height?{reset:{rects:k}}:{}}}};async function c_(e,t){const{placement:r,platform:o,elements:n}=e,i=await(o.isRTL==null?void 0:o.isRTL(n.floating)),l=tt(r),s=xt(r),a=Gr(r)==="y",u=["left","top"].includes(l)?-1:1,d=i&&a?-1:1,f=_t(t,e);let{mainAxis:g,crossAxis:y,alignmentAxis:v}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return s&&typeof v=="number"&&(y=s==="end"?v*-1:v),a?{x:y*d,y:g*u}:{x:g*u,y:y*d}}const d_=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,o;const{x:n,y:i,placement:l,middlewareData:s}=t,a=await c_(t,e);return l===((r=s.offset)==null?void 0:r.placement)&&(o=s.arrow)!=null&&o.alignmentOffset?{}:{x:n+a.x,y:i+a.y,data:{...a,placement:l}}}}},f_=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:r,y:o,placement:n}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:_=>{let{x:m,y:h}=_;return{x:m,y:h}}},...a}=_t(e,t),u={x:r,y:o},d=await eo(t,a),f=Gr(tt(n)),g=ec(f);let y=u[g],v=u[f];if(i){const _=g==="y"?"top":"left",m=g==="y"?"bottom":"right",h=y+d[_],c=y-d[m];y=Gu(h,y,c)}if(l){const _=f==="y"?"top":"left",m=f==="y"?"bottom":"right",h=v+d[_],c=v-d[m];v=Gu(h,v,c)}const k=s.fn({...t,[g]:y,[f]:v});return{...k,data:{x:k.x-r,y:k.y-o}}}}},g_=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:r,y:o,placement:n,rects:i,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=_t(e,t),d={x:r,y:o},f=Gr(n),g=ec(f);let y=d[g],v=d[f];const k=_t(s,t),_=typeof k=="number"?{mainAxis:k,crossAxis:0}:{mainAxis:0,crossAxis:0,...k};if(a){const c=g==="y"?"height":"width",p=i.reference[g]-i.floating[c]+_.mainAxis,b=i.reference[g]+i.reference[c]-_.mainAxis;y<p?y=p:y>b&&(y=b)}if(u){var m,h;const c=g==="y"?"width":"height",p=["top","left"].includes(tt(n)),b=i.reference[f]-i.floating[c]+(p&&((m=l.offset)==null?void 0:m[f])||0)+(p?0:_.crossAxis),w=i.reference[f]+i.reference[c]+(p?0:((h=l.offset)==null?void 0:h[f])||0)-(p?_.crossAxis:0);v<b?v=b:v>w&&(v=w)}return{[g]:y,[f]:v}}}},p_=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:r,rects:o,platform:n,elements:i}=t,{apply:l=()=>{},...s}=_t(e,t),a=await eo(t,s),u=tt(r),d=xt(r),f=Gr(r)==="y",{width:g,height:y}=o.floating;let v,k;u==="top"||u==="bottom"?(v=u,k=d===(await(n.isRTL==null?void 0:n.isRTL(i.floating))?"start":"end")?"left":"right"):(k=u,v=d==="end"?"top":"bottom");const _=y-a[v],m=g-a[k],h=!t.middlewareData.shift;let c=_,p=m;if(f){const w=g-a.left-a.right;p=d||h?zt(m,w):w}else{const w=y-a.top-a.bottom;c=d||h?zt(_,w):w}if(h&&!d){const w=Ae(a.left,0),B=Ae(a.right,0),E=Ae(a.top,0),O=Ae(a.bottom,0);f?p=g-2*(w!==0||B!==0?w+B:Ae(a.left,a.right)):c=y-2*(E!==0||O!==0?E+O:Ae(a.top,a.bottom))}await l({...t,availableWidth:p,availableHeight:c});const b=await n.getDimensions(i.floating);return g!==b.width||y!==b.height?{reset:{rects:!0}}:{}}}};function Fr(e){return iv(e)?(e.nodeName||"").toLowerCase():"#document"}function rt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Cr(e){var t;return(t=(iv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function iv(e){return e instanceof Node||e instanceof rt(e).Node}function Zt(e){return e instanceof Element||e instanceof rt(e).Element}function At(e){return e instanceof HTMLElement||e instanceof rt(e).HTMLElement}function lv(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof rt(e).ShadowRoot}function Qn(e){const{overflow:t,overflowX:r,overflowY:o,display:n}=ft(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+r)&&!["inline","contents"].includes(n)}function h_(e){return["table","td","th"].includes(Fr(e))}function nc(e){const t=ic(),r=ft(e);return r.transform!=="none"||r.perspective!=="none"||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(r.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(r.contain||"").includes(o))}function v_(e){let t=Wo(e);for(;At(t)&&!Ul(t);){if(nc(t))return t;t=Wo(t)}return null}function ic(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ul(e){return["html","body","#document"].includes(Fr(e))}function ft(e){return rt(e).getComputedStyle(e)}function Vl(e){return Zt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Wo(e){if(Fr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||lv(e)&&e.host||Cr(e);return lv(t)?t.host:t}function sv(e){const t=Wo(e);return Ul(t)?e.ownerDocument?e.ownerDocument.body:e.body:At(t)&&Qn(t)?t:sv(t)}function lc(e,t,r){var o;t===void 0&&(t=[]),r===void 0&&(r=!0);const n=sv(e),i=n===((o=e.ownerDocument)==null?void 0:o.body),l=rt(n);return i?t.concat(l,l.visualViewport||[],Qn(n)?n:[],l.frameElement&&r?lc(l.frameElement):[]):t.concat(n,lc(n,[],r))}function av(e){const t=ft(e);let r=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const n=At(e),i=n?e.offsetWidth:r,l=n?e.offsetHeight:o,s=Wl(r)!==i||Wl(o)!==l;return s&&(r=i,o=l),{width:r,height:o,$:s}}function uv(e){return Zt(e)?e:e.contextElement}function $o(e){const t=uv(e);if(!At(t))return Nr(1);const r=t.getBoundingClientRect(),{width:o,height:n,$:i}=av(t);let l=(i?Wl(r.width):r.width)/o,s=(i?Wl(r.height):r.height)/n;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const m_=Nr(0);function cv(e){const t=rt(e);return!ic()||!t.visualViewport?m_:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function y_(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==rt(e)?!1:t}function Jn(e,t,r,o){t===void 0&&(t=!1),r===void 0&&(r=!1);const n=e.getBoundingClientRect(),i=uv(e);let l=Nr(1);t&&(o?Zt(o)&&(l=$o(o)):l=$o(e));const s=y_(i,r,o)?cv(i):Nr(0);let a=(n.left+s.x)/l.x,u=(n.top+s.y)/l.y,d=n.width/l.x,f=n.height/l.y;if(i){const g=rt(i),y=o&&Zt(o)?rt(o):o;let v=g,k=v.frameElement;for(;k&&o&&y!==v;){const _=$o(k),m=k.getBoundingClientRect(),h=ft(k),c=m.left+(k.clientLeft+parseFloat(h.paddingLeft))*_.x,p=m.top+(k.clientTop+parseFloat(h.paddingTop))*_.y;a*=_.x,u*=_.y,d*=_.x,f*=_.y,a+=c,u+=p,v=rt(k),k=v.frameElement}}return Ho({width:d,height:f,x:a,y:u})}const k_=[":popover-open",":modal"];function dv(e){return k_.some(t=>{try{return e.matches(t)}catch{return!1}})}function b_(e){let{elements:t,rect:r,offsetParent:o,strategy:n}=e;const i=n==="fixed",l=Cr(o),s=t?dv(t.floating):!1;if(o===l||s&&i)return r;let a={scrollLeft:0,scrollTop:0},u=Nr(1);const d=Nr(0),f=At(o);if((f||!f&&!i)&&((Fr(o)!=="body"||Qn(l))&&(a=Vl(o)),At(o))){const g=Jn(o);u=$o(o),d.x=g.x+o.clientLeft,d.y=g.y+o.clientTop}return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-a.scrollLeft*u.x+d.x,y:r.y*u.y-a.scrollTop*u.y+d.y}}function w_(e){return Array.from(e.getClientRects())}function fv(e){return Jn(Cr(e)).left+Vl(e).scrollLeft}function B_(e){const t=Cr(e),r=Vl(e),o=e.ownerDocument.body,n=Ae(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=Ae(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let l=-r.scrollLeft+fv(e);const s=-r.scrollTop;return ft(o).direction==="rtl"&&(l+=Ae(t.clientWidth,o.clientWidth)-n),{width:n,height:i,x:l,y:s}}function __(e,t){const r=rt(e),o=Cr(e),n=r.visualViewport;let i=o.clientWidth,l=o.clientHeight,s=0,a=0;if(n){i=n.width,l=n.height;const u=ic();(!u||u&&t==="fixed")&&(s=n.offsetLeft,a=n.offsetTop)}return{width:i,height:l,x:s,y:a}}function x_(e,t){const r=Jn(e,!0,t==="fixed"),o=r.top+e.clientTop,n=r.left+e.clientLeft,i=At(e)?$o(e):Nr(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=n*i.x,u=o*i.y;return{width:l,height:s,x:a,y:u}}function gv(e,t,r){let o;if(t==="viewport")o=__(e,r);else if(t==="document")o=B_(Cr(e));else if(Zt(t))o=x_(t,r);else{const n=cv(e);o={...t,x:t.x-n.x,y:t.y-n.y}}return Ho(o)}function pv(e,t){const r=Wo(e);return r===t||!Zt(r)||Ul(r)?!1:ft(r).position==="fixed"||pv(r,t)}function S_(e,t){const r=t.get(e);if(r)return r;let o=lc(e,[],!1).filter(s=>Zt(s)&&Fr(s)!=="body"),n=null;const i=ft(e).position==="fixed";let l=i?Wo(e):e;for(;Zt(l)&&!Ul(l);){const s=ft(l),a=nc(l);!a&&s.position==="fixed"&&(n=null),(i?!a&&!n:!a&&s.position==="static"&&!!n&&["absolute","fixed"].includes(n.position)||Qn(l)&&!a&&pv(e,l))?o=o.filter(d=>d!==l):n=s,l=Wo(l)}return t.set(e,o),o}function E_(e){let{element:t,boundary:r,rootBoundary:o,strategy:n}=e;const l=[...r==="clippingAncestors"?S_(t,this._c):[].concat(r),o],s=l[0],a=l.reduce((u,d)=>{const f=gv(t,d,n);return u.top=Ae(f.top,u.top),u.right=zt(f.right,u.right),u.bottom=zt(f.bottom,u.bottom),u.left=Ae(f.left,u.left),u},gv(t,s,n));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function N_(e){const{width:t,height:r}=av(e);return{width:t,height:r}}function F_(e,t,r){const o=At(t),n=Cr(t),i=r==="fixed",l=Jn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=Nr(0);if(o||!o&&!i)if((Fr(t)!=="body"||Qn(n))&&(s=Vl(t)),o){const f=Jn(t,!0,i,t);a.x=f.x+t.clientLeft,a.y=f.y+t.clientTop}else n&&(a.x=fv(n));const u=l.left+s.scrollLeft-a.x,d=l.top+s.scrollTop-a.y;return{x:u,y:d,width:l.width,height:l.height}}function hv(e,t){return!At(e)||ft(e).position==="fixed"?null:t?t(e):e.offsetParent}function vv(e,t){const r=rt(e);if(!At(e)||dv(e))return r;let o=hv(e,t);for(;o&&h_(o)&&ft(o).position==="static";)o=hv(o,t);return o&&(Fr(o)==="html"||Fr(o)==="body"&&ft(o).position==="static"&&!nc(o))?r:o||v_(e)||r}const C_=async function(e){const t=this.getOffsetParent||vv,r=this.getDimensions,o=await r(e.floating);return{reference:F_(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function P_(e){return ft(e).direction==="rtl"}const T_={convertOffsetParentRelativeRectToViewportRelativeRect:b_,getDocumentElement:Cr,getClippingRect:E_,getOffsetParent:vv,getElementRects:C_,getClientRects:w_,getDimensions:N_,getScale:$o,isElement:Zt,isRTL:P_},D_=eo,O_=d_,R_=f_,z_=l_,A_=p_,mv=s_,L_=o_,I_=g_,M_=(e,t,r)=>{const o=new Map,n={platform:T_,...r},i={...n.platform,_c:o};return r_(e,t,{...n,platform:i})};function yv(e){const t=e.split("-");return{side:t[0],alignment:t[1]}}const j_=e=>e.nodeName==="HTML"?e:e.parentNode||e.host,H_=e=>{var t;return e.nodeType!==1?{}:((t=e.ownerDocument)===null||t===void 0?void 0:t.defaultView).getComputedStyle(e,null)},Kl=e=>{const t=e&&j_(e);if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}const{overflow:r,overflowX:o,overflowY:n}=H_(t);return/(auto|scroll|overlay)/.test(r+n+o)?t:Kl(t)},W_=e=>{var t;const r=Kl(e);return r?r!==((t=r.ownerDocument)===null||t===void 0?void 0:t.body):!1};function sc(e,t){if(t==="window")return e==null?void 0:e.ownerDocument.documentElement;if(t==="clippingParents")return"clippingAncestors";if(t==="scrollParent"){let r=Kl(e);return r.nodeName==="BODY"&&(r=e==null?void 0:e.ownerDocument.documentElement),r}return t}function $_(e,t){return typeof e=="number"||typeof e=="object"&&e!==null?ac(e,t):typeof e=="function"?r=>{const o=e(r);return ac(o,t)}:{mainAxis:t}}const ac=(e,t)=>{if(typeof e=="number")return{mainAxis:e+t};var r;return{...e,mainAxis:((r=e.mainAxis)!==null&&r!==void 0?r:0)+t}};function q_(e,t){if(typeof e=="number")return e;const{start:r,end:o,...n}=e,i=n,l=t?"end":"start",s=t?"start":"end";return e[l]&&(i.left=e[l]),e[s]&&(i.right=e[s]),i}const U_=e=>({above:"top",below:"bottom",before:e?"right":"left",after:e?"left":"right"}),V_=()=>({start:"start",end:"end",top:"start",bottom:"end",center:void 0}),K_=(e,t)=>{const r=e==="above"||e==="below",o=t==="top"||t==="bottom";return r&&o||!r&&!o},kv=(e,t,r)=>{const o=K_(t,e)?"center":e,n=t&&U_(r)[t],i=o&&V_()[o];return n&&i?`${n}-${i}`:n},X_=()=>({top:"above",bottom:"below",right:"after",left:"before"}),Y_=e=>e==="above"||e==="below"?{start:"start",end:"end"}:{start:"top",end:"bottom"},Q_=e=>{const{side:t,alignment:r}=yv(e),o=X_()[t],n=r&&Y_(o)[r];return{position:o,alignment:n}},J_={above:{position:"above",align:"center"},"above-start":{position:"above",align:"start"},"above-end":{position:"above",align:"end"},below:{position:"below",align:"center"},"below-start":{position:"below",align:"start"},"below-end":{position:"below",align:"end"},before:{position:"before",align:"center"},"before-top":{position:"before",align:"top"},"before-bottom":{position:"before",align:"bottom"},after:{position:"after",align:"center"},"after-top":{position:"after",align:"top"},"after-bottom":{position:"after",align:"bottom"}};function bv(e){return e==null?{}:typeof e=="string"?J_[e]:e}function uc(e,t,r){const o=x.exports.useRef(!0),[n]=x.exports.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(i){const l=n.value;if(l!==i){if(n.value=i,r&&o.current)return;n.callback(i,l)}}}}));return Yt(()=>{o.current=!1},[]),n.callback=t,n.facade}function Z_(e){let t;return()=>(t||(t=new Promise(r=>{Promise.resolve().then(()=>{t=void 0,r(e())})})),t)}function G_(e){const{arrow:t,middlewareData:r}=e;if(!r.arrow||!t)return;const{x:o,y:n}=r.arrow;Object.assign(t.style,{left:`${o}px`,top:`${n}px`})}function ex(e){var t,r,o;const{container:n,placement:i,middlewareData:l,strategy:s,lowPPI:a,coordinates:u,useTransform:d=!0}=e;if(!n)return;n.setAttribute(YB,i),n.removeAttribute(Xh),l.intersectionObserver.intersecting&&n.setAttribute(Xh,""),n.removeAttribute(Yh),!((t=l.hide)===null||t===void 0)&&t.escaped&&n.setAttribute(Yh,""),n.removeAttribute(Qh),!((r=l.hide)===null||r===void 0)&&r.referenceHidden&&n.setAttribute(Qh,"");const f=((o=n.ownerDocument.defaultView)===null||o===void 0?void 0:o.devicePixelRatio)||1,g=Math.round(u.x*f)/f,y=Math.round(u.y*f)/f;if(Object.assign(n.style,{position:s}),d){Object.assign(n.style,{transform:a?`translate(${g}px, ${y}px)`:`translate3d(${g}px, ${y}px, 0)`});return}Object.assign(n.style,{left:`${g}px`,top:`${y}px`})}const tx=e=>{switch(e){case"always":case!0:return{applyMaxWidth:!0,applyMaxHeight:!0};case"width-always":case"width":return{applyMaxWidth:!0,applyMaxHeight:!1};case"height-always":case"height":return{applyMaxWidth:!1,applyMaxHeight:!0};default:return!1}};function rx(){return{name:"coverTarget",fn:e=>{const{placement:t,rects:r,x:o,y:n}=e,i=yv(t).side,l={x:o,y:n};switch(i){case"bottom":l.y-=r.reference.height;break;case"top":l.y+=r.reference.height;break;case"left":l.x+=r.reference.width;break;case"right":l.x-=r.reference.width;break}return l}}}function ox(e){const{hasScrollableElement:t,flipBoundary:r,container:o,fallbackPositions:n=[],isRtl:i}=e,l=n.reduce((s,a)=>{const{position:u,align:d}=bv(a),f=kv(d,u,i);return f&&s.push(f),s},[]);return z_({...t&&{boundary:"clippingAncestors"},...r&&{altBoundary:!0,boundary:sc(o,r)},fallbackStrategy:"bestFit",...l.length&&{fallbackPlacements:l}})}function nx(){return{name:"intersectionObserver",fn:async e=>{const t=e.rects.floating,r=await D_(e,{altBoundary:!0}),o=r.top<t.height&&r.top>0,n=r.bottom<t.height&&r.bottom>0;return{data:{intersecting:o||n}}}}}const ix=e=>({name:"resetMaxSize",fn({middlewareData:t,elements:r}){var o;if(!((o=t.resetMaxSize)===null||o===void 0)&&o.maxSizeAlreadyReset)return{};const{applyMaxWidth:n,applyMaxHeight:i}=e;return n&&(r.floating.style.removeProperty("box-sizing"),r.floating.style.removeProperty("max-width"),r.floating.style.removeProperty("width")),i&&(r.floating.style.removeProperty("box-sizing"),r.floating.style.removeProperty("max-height"),r.floating.style.removeProperty("height")),{data:{maxSizeAlreadyReset:!0},reset:{rects:!0}}}});function lx(e,t){const{container:r,overflowBoundary:o}=t;return A_({...o&&{altBoundary:!0,boundary:sc(r,o)},apply({availableHeight:n,availableWidth:i,elements:l,rects:s}){const a=(f,g,y)=>{if(!!f&&(l.floating.style.setProperty("box-sizing","border-box"),l.floating.style.setProperty(`max-${g}`,`${y}px`),s.floating[g]>y)){l.floating.style.setProperty(g,`${y}px`);const v=g==="width"?"x":"y";l.floating.style.getPropertyValue(`overflow-${v}`)||l.floating.style.setProperty(`overflow-${v}`,"auto")}},{applyMaxWidth:u,applyMaxHeight:d}=e;a(u,"width",i),a(d,"height",n)}})}function sx(e){return!e||typeof e=="number"||typeof e=="object"?e:({rects:{floating:t,reference:r},placement:o})=>{const{position:n,alignment:i}=Q_(o);return e({positionedRect:t,targetRect:r,position:n,alignment:i})}}function ax(e){const t=sx(e);return O_(t)}function ux(e){const{hasScrollableElement:t,disableTether:r,overflowBoundary:o,container:n,overflowBoundaryPadding:i,isRtl:l}=e;return R_({...t&&{boundary:"clippingAncestors"},...r&&{crossAxis:r==="all",limiter:I_({crossAxis:r!=="all",mainAxis:!1})},...i&&{padding:q_(i,l)},...o&&{altBoundary:!0,boundary:sc(n,o)}})}const wv="--fui-match-target-size";function cx(){return{name:"matchTargetSize",fn:async e=>{const{rects:{reference:t,floating:r},elements:{floating:o},middlewareData:{matchTargetSize:{matchTargetSizeAttempt:n=!1}={}}}=e;if(t.width===r.width||n)return{};const{width:i}=t;return o.style.setProperty(wv,`${i}px`),o.style.width||(o.style.width=`var(${wv})`),{data:{matchTargetSizeAttempt:!0},reset:{rects:!0}}}}}function Bv(e){const t=[];let r=e;for(;r;){const o=Kl(r);if(e.ownerDocument.body===o){t.push(o);break}if(o.nodeName==="BODY"&&o!==e.ownerDocument.body)break;t.push(o),r=o}return t}function dx(e,t){return new e.ResizeObserver(t)}function fx(e){let t=!1;const{container:r,target:o,arrow:n,strategy:i,middleware:l,placement:s,useTransform:a=!0,disableUpdateOnResize:u=!1}=e,d=r.ownerDocument.defaultView;if(!o||!r||!d)return{updatePosition:()=>{},dispose:()=>{}};const f=u?null:dx(d,m=>{m.every(c=>c.contentRect.width>0&&c.contentRect.height>0)&&k()});let g=!0;const y=new Set;Object.assign(r.style,{position:"fixed",left:0,top:0,margin:0});const v=()=>{t||(g&&(Bv(r).forEach(m=>y.add(m)),Un(o)&&Bv(o).forEach(m=>y.add(m)),y.forEach(m=>{m.addEventListener("scroll",k,{passive:!0})}),f==null||f.observe(r),Un(o)&&(f==null||f.observe(o)),g=!1),Object.assign(r.style,{position:i}),M_(o,r,{placement:s,middleware:l,strategy:i}).then(({x:m,y:h,middlewareData:c,placement:p})=>{t||(G_({arrow:n,middlewareData:c}),ex({container:r,middlewareData:c,placement:p,coordinates:{x:m,y:h},lowPPI:((d==null?void 0:d.devicePixelRatio)||1)<=1,strategy:i,useTransform:a}),r.dispatchEvent(new CustomEvent(Zu)))}).catch(m=>{}))},k=Z_(()=>v()),_=()=>{t=!0,d&&(d.removeEventListener("scroll",k),d.removeEventListener("resize",k)),y.forEach(m=>{m.removeEventListener("scroll",k)}),y.clear(),f==null||f.disconnect()};return d&&(d.addEventListener("scroll",k,{passive:!0}),d.addEventListener("resize",k)),k(),{updatePosition:k,dispose:_}}function gx(e){const t=x.exports.useRef(null),r=x.exports.useRef(null),o=x.exports.useRef(null),n=x.exports.useRef(null),i=x.exports.useRef(null),{enabled:l=!0}=e,s=px(e),a=x.exports.useCallback(()=>{t.current&&t.current.dispose(),t.current=null;var v;const k=(v=o.current)!==null&&v!==void 0?v:r.current;l&&Nu()&&k&&n.current&&(t.current=fx({container:n.current,target:k,arrow:i.current,...s(n.current,i.current)}))},[l,s]),u=ze(v=>{o.current=v,a()});x.exports.useImperativeHandle(e.positioningRef,()=>({updatePosition:()=>{var v;return(v=t.current)===null||v===void 0?void 0:v.updatePosition()},setTarget:v=>{e.target,u(v)}}),[e.target,u]),Yt(()=>{var v;u((v=e.target)!==null&&v!==void 0?v:null)},[e.target,u]),Yt(()=>{a()},[a]);const d=uc(null,v=>{r.current!==v&&(r.current=v,a())}),f=ze(()=>{var v;return(v=e.onPositioningEnd)===null||v===void 0?void 0:v.call(e)}),g=uc(null,v=>{if(n.current!==v){var k;(k=n.current)===null||k===void 0||k.removeEventListener(Zu,f),v==null||v.addEventListener(Zu,f),n.current=v,a()}}),y=uc(null,v=>{i.current!==v&&(i.current=v,a())});return{targetRef:d,containerRef:g,arrowRef:y}}function px(e){const{align:t,arrowPadding:r,autoSize:o,coverTarget:n,flipBoundary:i,offset:l,overflowBoundary:s,pinned:a,position:u,unstable_disableTether:d,positionFixed:f,strategy:g,overflowBoundaryPadding:y,fallbackPositions:v,useTransform:k,matchTargetSize:_,disableUpdateOnResize:m=!1}=e,{dir:h,targetDocument:c}=Dt(),p=h==="rtl",b=(g!=null?g:f)?"fixed":"absolute",w=tx(o);return x.exports.useCallback((B,E)=>{const O=W_(B),F=[w&&ix(w),_&&cx(),l&&ax(l),n&&rx(),!a&&ox({container:B,flipBoundary:i,hasScrollableElement:O,isRtl:p,fallbackPositions:v}),ux({container:B,hasScrollableElement:O,overflowBoundary:s,disableTether:d,overflowBoundaryPadding:y,isRtl:p}),w&&lx(w,{container:B,overflowBoundary:s}),nx(),E&&L_({element:E,padding:r}),mv({strategy:"referenceHidden"}),mv({strategy:"escaped"}),!1].filter(Boolean);return{placement:kv(t,u,p),middleware:F,strategy:b,useTransform:k,disableUpdateOnResize:m}},[t,r,w,n,d,i,p,l,s,a,u,b,y,v,k,_,c,m])}const hx=e=>{const[t,r]=x.exports.useState(e);return[t,n=>{if(n==null){r(void 0);return}let i;n instanceof MouseEvent?i=n:i=n.nativeEvent,i instanceof MouseEvent;const l=XB(i);r(l)}]},cc=Uh(void 0),vx={open:!1,setOpen:()=>null,toggleOpen:()=>null,triggerRef:{current:null},contentRef:{current:null},arrowRef:{current:null},openOnContext:!1,openOnHover:!1,size:"medium",trapFocus:!1,inline:!1};cc.Provider;const St=e=>Vh(cc,(t=vx)=>e(t)),mx=(e,t)=>{const r=St(h=>h.contentRef),o=St(h=>h.openOnHover),n=St(h=>h.setOpen),i=St(h=>h.mountNode),l=St(h=>h.arrowRef),s=St(h=>h.size),a=St(h=>h.withArrow),u=St(h=>h.appearance),d=St(h=>h.trapFocus),f=St(h=>h.inertTrapFocus),g=St(h=>h.inline),{modalAttributes:y}=Vu({trapFocus:d,legacyTrapFocus:!f,alwaysFocusable:!d}),v={inline:g,appearance:u,withArrow:a,size:s,arrowRef:l,mountNode:i,components:{root:"div"},root:wt(Xt("div",{ref:Cu(t,r),role:d?"dialog":"group","aria-modal":d?!0:void 0,...y,...e}),{elementType:"div"})},{onMouseEnter:k,onMouseLeave:_,onKeyDown:m}=v.root;return v.root.onMouseEnter=h=>{o&&n(h,!0),k==null||k(h)},v.root.onMouseLeave=h=>{o&&n(h,!1),_==null||_(h)},v.root.onKeyDown=h=>{var c;h.key==="Escape"&&((c=r.current)===null||c===void 0?void 0:c.contains(h.target))&&(h.preventDefault(),n(h,!1)),m==null||m(h)},v};function yx(e){return Un(e)?{element:e}:typeof e=="object"?e===null?{element:null}:e:{}}var _v=()=>x.exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner.current,kx=()=>!1,xv=new WeakSet;function bx(e,t){const r=_v();x.exports.useEffect(()=>{if(!xv.has(r)){xv.add(r),e();return}return e()},t)}var Sv=new WeakSet;function wx(e,t){return x.exports.useMemo(()=>{const r=_v();return Sv.has(r)?e():(Sv.add(r),null)},t)}function Bx(e,t){var r;const o=kx()&&!1,n=o?wx:x.exports.useMemo,i=o?bx:x.exports.useEffect,[l,s]=(r=n(()=>e(),t))!=null?r:[null,()=>null];return i(()=>s,t),l}const _x=Ve({root:{qhf8xq:"f1euv43f",Bhzewxz:"f15twtuk",oyh7mz:["f1vgc2s3","f1e31b4d"],j35jbq:["f1e31b4d","f1vgc2s3"],Bj3rh1h:"f494woh"}},{d:[".f1euv43f{position:absolute;}",".f15twtuk{top:0;}",".f1vgc2s3{left:0;}",".f1e31b4d{right:0;}",".f494woh{z-index:1000000;}"]}),Ev=lo["useInsertionEffect"],xx=e=>{const{targetDocument:t,dir:r}=Dt(),o=lb(),n=xh(),i=_x(),l=Zk(),s=_e(l,i.root,e.className),a=o!=null?o:t==null?void 0:t.body,u=Bx(()=>{if(a===void 0||e.disabled)return[null,()=>null];const d=a.ownerDocument.createElement("div");return a.appendChild(d),[d,()=>d.remove()]},[a]);return Ev?Ev(()=>{if(!u)return;const d=s.split(" ").filter(Boolean);return u.classList.add(...d),u.setAttribute("dir",r),u.setAttribute("data-portal-node","true"),n.current=u,()=>{u.classList.remove(...d),u.removeAttribute("dir")}},[s,r,u,n]):x.exports.useMemo(()=>{!u||(u.className=s,u.setAttribute("dir",r),u.setAttribute("data-portal-node","true"),n.current=u)},[s,r,u,n]),u},Sx=e=>{const{element:t,className:r}=yx(e.mountNode),o=x.exports.useRef(null),n=xx({disabled:!!t,className:r}),i=t!=null?t:n,l={children:e.children,mountNode:i,virtualParentRootRef:o};return x.exports.useEffect(()=>{if(!i)return;const s=o.current,a=i.contains(s);if(s&&!a)return Xp(i,s),()=>{Xp(i,void 0)}},[o,i]),l},Ex=e=>x.exports.createElement("span",{hidden:!0,ref:e.virtualParentRootRef},e.mountNode&&ri.exports.createPortal(e.children,e.mountNode)),dc=e=>{const t=Sx(e);return Ex(t)};dc.displayName="Portal";const Nx=e=>{const t=Yr(e.root,{children:[e.withArrow&&G("div",{ref:e.arrowRef,className:e.arrowClassName}),e.root.children]});return e.inline?t:G(dc,{mountNode:e.mountNode,children:t})},Fx={root:"fui-PopoverSurface"},Cx={small:6,medium:8,large:8},Px=Ve({root:{sj55zd:"f19n0e5",De3pzq:"fxugw4r",E5pizo:"f1hg901r",Bbmb7ep:["f1aa9q02","f16jpd5f"],Beyfa6y:["f16jpd5f","f1aa9q02"],B7oj6ja:["f1jar5jt","fyu767a"],Btl43ni:["fyu767a","f1jar5jt"],B4j52fo:"f5ogflp",Bekrc4i:["f1hqa2wf","finvdd3"],Bn0qgzm:"f1f09k3d",ibv6hh:["finvdd3","f1hqa2wf"],icvyot:"fzkkow9",vrafjx:["fcdblym","fjik90z"],oivjwe:"fg706s2",wvpqe5:["fjik90z","fcdblym"],g2u3we:"fghlq4f",h3c5rm:["f1gn591s","fjscplz"],B9xav0g:"fb073pr",zhjwy3:["fjscplz","f1gn591s"],Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bhrd7zp:"figsok6",Bg96gwp:"f1i3iumi",B93otf3:"f18k4bn6",vin17d:"fo1kyvf",Ezkn3b:"fetxo7e",nyiy2g:"f8x1vz1",swvrvq:"f8g0anz",Bkovbt3:"fezwn9i",hgjdhn:"fz5efge",fsy9dk:"f1ydixl4",B3ogreh:"f8dgqj5",jv49x5:"fnyfnr8",Bk7o48c:"fgw77r4",Bv12yb3:"ftje0s4",z0t1cu:"fi19xcv",Bks05zx:"f1mzajhk",Bvtglag:"fjp4h9y"},inline:{Bj3rh1h:"f19g0ac"},inverted:{De3pzq:"fg3r6xk",sj55zd:"fonrgv7"},brand:{De3pzq:"ffp7eso",sj55zd:"f1phragk"},smallPadding:{z8tnut:"f1kcqot9",z189sj:["f11qrl6u","fjlbh76"],Byoj8tv:"fpe6lb7",uwmqm3:["fjlbh76","f11qrl6u"]},mediumPadding:{z8tnut:"fqag9an",z189sj:["f1gbmcue","f1rh9g5y"],Byoj8tv:"fp67ikv",uwmqm3:["f1rh9g5y","f1gbmcue"]},largePadding:{z8tnut:"fc7z3ec",z189sj:["fat0sn4","fekwl8i"],Byoj8tv:"fe2my4m",uwmqm3:["fekwl8i","fat0sn4"]},smallArrow:{a9b677:"f1ekdpwm",Bqenvij:"f83vc9z"},mediumLargeArrow:{a9b677:"f1kmc0fn",Bqenvij:"fb6lvc5"},arrow:{qhf8xq:"f1euv43f",De3pzq:"f1u2r49w",Bcdw1i0:"fd7fpy0",Bj3rh1h:"f1bsuimh",Ftih45:"f1wl9k8s",B1puzpu:"f1wkw4r9",Brfgrao:"f1j7ml58",Bcvre1j:"fyl8oag",Ccq8qp:"frdoeuz",Baz25je:"fb81m9q",cmx5o7:"f1ljr5q2",B4f6apu:"fyfemzf",m598lv:"focyt6c",Bk5zm6e:"fnhxbxj",y0oebl:"fdw6hkg",qa3bma:"f11yjt3y",Bqjgrrk:"f1172wan",Budzafs:["f9e5op9","f112wvtl"],Hv9wc6:"f16cagkn",hl6cv3:"f1773hnp",c8svkw:"fw7o64x",yayu3t:"f1v7783n",nr3p0k:"f1f0d6v",rhl9o9:"fh2hsk5",wiz9v7:"f1gj3y7g",B6q6orb:"f11yvu4",ndpsmx:"f17lejdj"}},{d:[".f19n0e5{color:var(--colorNeutralForeground1);}",".fxugw4r{background-color:var(--colorNeutralBackground1);}",".f1hg901r{box-shadow:var(--shadow16);}",".f1aa9q02{border-bottom-right-radius:var(--borderRadiusMedium);}",".f16jpd5f{border-bottom-left-radius:var(--borderRadiusMedium);}",".f1jar5jt{border-top-right-radius:var(--borderRadiusMedium);}",".fyu767a{border-top-left-radius:var(--borderRadiusMedium);}",".f5ogflp{border-top-width:1px;}",".f1hqa2wf{border-right-width:1px;}",".finvdd3{border-left-width:1px;}",".f1f09k3d{border-bottom-width:1px;}",".fzkkow9{border-top-style:solid;}",".fcdblym{border-right-style:solid;}",".fjik90z{border-left-style:solid;}",".fg706s2{border-bottom-style:solid;}",".fghlq4f{border-top-color:var(--colorTransparentStroke);}",".f1gn591s{border-right-color:var(--colorTransparentStroke);}",".fjscplz{border-left-color:var(--colorTransparentStroke);}",".fb073pr{border-bottom-color:var(--colorTransparentStroke);}",".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".f1i3iumi{line-height:var(--lineHeightBase300);}",".f18k4bn6{animation-composition:accumulate;}",".fo1kyvf{animation-duration:var(--durationSlower);}",".fetxo7e{animation-timing-function:var(--curveDecelerateMid);}",".f8x1vz1{--fui-positioning-slide-distance-x:0px;}",".f8g0anz{--fui-positioning-slide-distance-y:10px;}",".fezwn9i[data-popper-placement^=right]{--fui-positioning-slide-distance-x:-10px;}",".fz5efge[data-popper-placement^=right]{--fui-positioning-slide-distance-y:0px;}",".f1ydixl4[data-popper-placement^=bottom]{--fui-positioning-slide-distance-x:0px;}",".f8dgqj5[data-popper-placement^=bottom]{--fui-positioning-slide-distance-y:-10px;}",".fnyfnr8[data-popper-placement^=left]{--fui-positioning-slide-distance-x:10px;}",".fgw77r4[data-popper-placement^=left]{--fui-positioning-slide-distance-y:0px;}",".ftje0s4{animation-name:f5j8bii,f79suad;}",".f19g0ac{z-index:1;}",".fg3r6xk{background-color:var(--colorNeutralBackgroundStatic);}",".fonrgv7{color:var(--colorNeutralForegroundStaticInverted);}",".ffp7eso{background-color:var(--colorBrandBackground);}",".f1phragk{color:var(--colorNeutralForegroundOnBrand);}",".f1kcqot9{padding-top:12px;}",".f11qrl6u{padding-right:12px;}",".fjlbh76{padding-left:12px;}",".fpe6lb7{padding-bottom:12px;}",".fqag9an{padding-top:16px;}",".f1gbmcue{padding-right:16px;}",".f1rh9g5y{padding-left:16px;}",".fp67ikv{padding-bottom:16px;}",".fc7z3ec{padding-top:20px;}",".fat0sn4{padding-right:20px;}",".fekwl8i{padding-left:20px;}",".fe2my4m{padding-bottom:20px;}",".f1ekdpwm{width:8.484px;}",".f83vc9z{height:8.484px;}",".f1kmc0fn{width:11.312px;}",".fb6lvc5{height:11.312px;}",".f1euv43f{position:absolute;}",".f1u2r49w{background-color:inherit;}",".fd7fpy0{visibility:hidden;}",".f1bsuimh{z-index:-1;}",'.f1wl9k8s::before{content:"";}',".f1wkw4r9::before{visibility:visible;}",".f1j7ml58::before{position:absolute;}",".fyl8oag::before{box-sizing:border-box;}",".frdoeuz::before{width:inherit;}",".fb81m9q::before{height:inherit;}",".f1ljr5q2::before{background-color:inherit;}",".fyfemzf::before{border-right-width:1px;}",".focyt6c::before{border-right-style:solid;}",".fnhxbxj::before{border-right-color:var(--colorTransparentStroke);}",".fdw6hkg::before{border-bottom-width:1px;}",".f11yjt3y::before{border-bottom-style:solid;}",".f1172wan::before{border-bottom-color:var(--colorTransparentStroke);}",".f9e5op9::before{border-bottom-right-radius:var(--borderRadiusSmall);}",".f112wvtl::before{border-bottom-left-radius:var(--borderRadiusSmall);}",".f16cagkn::before{transform:rotate(var(--fui-positioning-angle)) translate(0, 50%) rotate(45deg);}",'[data-popper-placement^="top"] .f1773hnp{bottom:-1px;}','[data-popper-placement^="top"] .fw7o64x{--fui-positioning-angle:0;}','[data-popper-placement^="right"] .f1v7783n{left:-1px;}','[data-popper-placement^="right"] .f1f0d6v{--fui-positioning-angle:90deg;}','[data-popper-placement^="bottom"] .fh2hsk5{top:-1px;}','[data-popper-placement^="bottom"] .f1gj3y7g{--fui-positioning-angle:180deg;}','[data-popper-placement^="left"] .f11yvu4{right:-1px;}','[data-popper-placement^="left"] .f17lejdj{--fui-positioning-angle:270deg;}'],k:["@keyframes f5j8bii{from{opacity:0;}to{opacity:1;}}","@keyframes f79suad{from{transform:translate(var(--fui-positioning-slide-distance-x), var(--fui-positioning-slide-distance-y));}}"],m:[["@media (prefers-reduced-motion){.fi19xcv[data-popper-placement]{animation-duration:1ms;}}",{m:"(prefers-reduced-motion)"}],["@media (prefers-reduced-motion){.f1mzajhk[data-popper-placement]{animation-name:f5j8bii;}}",{m:"(prefers-reduced-motion)"}]],t:["@supports not (animation-composition: accumulate){.fjp4h9y[data-popper-placement]{animation-name:f5j8bii;}}"]}),Tx=e=>{const t=Px();return e.root.className=_e(Fx.root,t.root,e.inline&&t.inline,e.size==="small"&&t.smallPadding,e.size==="medium"&&t.mediumPadding,e.size==="large"&&t.largePadding,e.appearance==="inverted"&&t.inverted,e.appearance==="brand"&&t.brand,e.root.className),e.arrowClassName=_e(t.arrow,e.size==="small"?t.smallArrow:t.mediumLargeArrow),e},Nv=x.exports.forwardRef((e,t)=>{const r=mx(e,t);return Tx(r),_r("usePopoverSurfaceStyles_unstable")(r),Nx(r)});Nv.displayName="PopoverSurface";const Dx=4,Ox=e=>{const[t,r]=hx(),o={size:"medium",contextTarget:t,setContextTarget:r,...e},n=x.exports.Children.toArray(e.children);let i,l;n.length===2?(i=n[0],l=n[1]):n.length===1&&(l=n[0]);const[s,a]=Rx(o),u=x.exports.useRef(0),d=ze((c,p)=>{if(clearTimeout(u.current),!(c instanceof Event)&&c.persist&&c.persist(),c.type==="mouseleave"){var b;u.current=setTimeout(()=>{a(c,p)},(b=e.mouseLeaveDelay)!==null&&b!==void 0?b:500)}else a(c,p)});x.exports.useEffect(()=>()=>{clearTimeout(u.current)},[]);const f=x.exports.useCallback(c=>{d(c,!s)},[d,s]),g=zx(o),{targetDocument:y}=Dt();var v;sb({contains:Kp,element:y,callback:c=>d(c,!1),refs:[g.triggerRef,g.contentRef],disabled:!s,disabledFocusOnIframe:!(!((v=e.closeOnIframeFocus)!==null&&v!==void 0)||v)});const k=o.openOnContext||o.closeOnScroll;cb({contains:Kp,element:y,callback:c=>d(c,!1),refs:[g.triggerRef,g.contentRef],disabled:!s||!k});const{findFirstFocusable:_}=wh();x.exports.useEffect(()=>{if(!e.unstable_disableAutoFocus&&s&&g.contentRef.current){var c;const p=(c=g.contentRef.current.getAttribute("tabIndex"))!==null&&c!==void 0?c:void 0,b=isNaN(p)?_(g.contentRef.current):g.contentRef.current;b==null||b.focus()}},[_,s,g.contentRef,e.unstable_disableAutoFocus]);var m,h;return{...o,...g,inertTrapFocus:(m=e.inertTrapFocus)!==null&&m!==void 0?m:e.legacyTrapFocus===void 0?!1:!e.legacyTrapFocus,popoverTrigger:i,popoverSurface:l,open:s,setOpen:d,toggleOpen:f,setContextTarget:r,contextTarget:t,inline:(h=e.inline)!==null&&h!==void 0?h:!1}};function Rx(e){const t=ze((l,s)=>{var a;return(a=e.onOpenChange)===null||a===void 0?void 0:a.call(e,l,s)}),[r,o]=zp({state:e.open,defaultState:e.defaultOpen,initialState:!1});e.open=r!==void 0?r:e.open;const n=e.setContextTarget,i=x.exports.useCallback((l,s)=>{s&&l.type==="contextmenu"&&n(l),s||n(void 0),o(s),t==null||t(l,{open:s})},[o,t,n]);return[r,i]}function zx(e){const t={position:"above",align:"center",arrowPadding:2*Dx,target:e.openOnContext?e.contextTarget:void 0,...bv(e.positioning)};t.coverTarget&&(e.withArrow=!1),e.withArrow&&(t.offset=$_(t.offset,Cx[e.size]));const{targetRef:r,containerRef:o,arrowRef:n}=gx(t);return{triggerRef:r,contentRef:o,arrowRef:n}}const Ax=e=>{const{appearance:t,arrowRef:r,contentRef:o,inline:n,mountNode:i,open:l,openOnContext:s,openOnHover:a,setOpen:u,size:d,toggleOpen:f,trapFocus:g,triggerRef:y,withArrow:v,inertTrapFocus:k}=e;return x.exports.createElement(cc.Provider,{value:{appearance:t,arrowRef:r,contentRef:o,inline:n,mountNode:i,open:l,openOnContext:s,openOnHover:a,setOpen:u,toggleOpen:f,triggerRef:y,size:d,trapFocus:g,inertTrapFocus:k,withArrow:v}},e.popoverTrigger,e.open&&e.popoverSurface)},Fv=e=>{const t=Ox(e);return Ax(t)};Fv.displayName="Popover";const Lx=e=>{const{iconOnly:t,iconPosition:r}=e;return Yr(e.root,{children:[r!=="after"&&e.icon&&G(e.icon,{}),!t&&e.root.children,r==="after"&&e.icon&&G(e.icon,{})]})},Cv=x.exports.createContext(void 0),Ix={};Cv.Provider;const Mx=()=>{var e;return(e=x.exports.useContext(Cv))!==null&&e!==void 0?e:Ix},jx=(e,t)=>{const{size:r}=Mx(),{appearance:o="secondary",as:n="button",disabled:i=!1,disabledFocusable:l=!1,icon:s,iconPosition:a="before",shape:u="rounded",size:d=r!=null?r:"medium"}=e,f=Dl(s,{elementType:"span"});return{appearance:o,disabled:i,disabledFocusable:l,iconPosition:a,shape:u,size:d,iconOnly:Boolean((f==null?void 0:f.children)&&!e.children),components:{root:"button",icon:"span"},root:wt(Xt(n,Kh(e.as,e)),{elementType:"button",defaultProps:{ref:t,type:"button"}}),icon:f}},Pv={root:"fui-Button",icon:"fui-Button__icon"},Hx=Kt("r1alrhcs",null,{r:[".r1alrhcs{align-items:center;box-sizing:border-box;display:inline-flex;justify-content:center;text-decoration-line:none;vertical-align:middle;margin:0;overflow:hidden;background-color:var(--colorNeutralBackground1);color:var(--colorNeutralForeground1);border:var(--strokeWidthThin) solid var(--colorNeutralStroke1);font-family:var(--fontFamilyBase);outline-style:none;padding:5px var(--spacingHorizontalM);min-width:96px;border-radius:var(--borderRadiusMedium);font-size:var(--fontSizeBase300);font-weight:var(--fontWeightSemibold);line-height:var(--lineHeightBase300);transition-duration:var(--durationFaster);transition-property:background,border,color;transition-timing-function:var(--curveEasyEase);}",".r1alrhcs:hover{background-color:var(--colorNeutralBackground1Hover);border-color:var(--colorNeutralStroke1Hover);color:var(--colorNeutralForeground1Hover);cursor:pointer;}",".r1alrhcs:hover:active{background-color:var(--colorNeutralBackground1Pressed);border-color:var(--colorNeutralStroke1Pressed);color:var(--colorNeutralForeground1Pressed);outline-style:none;}",".r1alrhcs[data-fui-focus-visible]{border-color:var(--colorStrokeFocus2);border-radius:var(--borderRadiusMedium);border-width:1px;outline:var(--strokeWidthThick) solid var(--colorTransparentStroke);box-shadow:0 0 0 var(--strokeWidthThin) var(--colorStrokeFocus2) inset;z-index:1;}"],s:["@media screen and (prefers-reduced-motion: reduce){.r1alrhcs{transition-duration:0.01ms;}}","@media (forced-colors: active){.r1alrhcs:focus{border-color:ButtonText;}.r1alrhcs:hover{background-color:HighlightText;border-color:Highlight;color:Highlight;forced-color-adjust:none;}.r1alrhcs:hover:active{background-color:HighlightText;border-color:Highlight;color:Highlight;forced-color-adjust:none;}}","@supports (-moz-appearance:button){.r1alrhcs[data-fui-focus-visible]{box-shadow:0 0 0 calc(var(--strokeWidthThin) + 0.25px) var(--colorStrokeFocus2) inset;}}"]}),Wx=Kt("rywnvv2",null,[".rywnvv2{align-items:center;display:inline-flex;justify-content:center;font-size:20px;height:20px;width:20px;--fui-Button__icon--spacing:var(--spacingHorizontalSNudge);}"]),$x=Ve({outline:{De3pzq:"f1c21dwh",Jwef8y:"fjxutwb",iro3zm:"fwiml72"},primary:{De3pzq:"ffp7eso",g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],sj55zd:"f1phragk",Jwef8y:"f15wkkf3",Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],Bi91k9c:"f1rq72xc",iro3zm:"fnp9lpt",b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"],B2d53fq:"f1d6v5y2",Bsw6fvg:"f1rirnrt",Bjwas2f:"f1uu00uk",Bn1d65q:["fkvaka8","f9a0qzu"],Bxeuatn:"f1ux7til",n51gp8:["f9a0qzu","fkvaka8"],Bbusuzp:"f1lkg8j3",ycbfsm:"fkc42ay",Bqrx1nm:"fq7113v",pgvf35:"ff1wgvm",Bh7lczh:["fiob0tu","f1x4h75k"],dpv3f4:"f1j6scgf",Bpnjhaq:["f1x4h75k","fiob0tu"],ze5xyy:"f4xjyn1",g2kj27:"fbgcvur",Bf756sw:"f1ks1yx8",Bow2dr7:["f1o6qegi","fmxjhhp"],Bvhedfk:"fcnxywj",Gye4lf:["fmxjhhp","f1o6qegi"],pc6evw:"f9ddjv3"},secondary:{},subtle:{De3pzq:"fhovq9v",g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],sj55zd:"fkfq4zb",Jwef8y:"f1t94bn6",Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],Bi91k9c:"fnwyq0v",Bk3fhr4:"ft1hn21",Bmfj8id:"fuxngvv",Bbdnnc7:"fy5bs14",iro3zm:"fsv2rcd",b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"],B2d53fq:"f1omzyqd",em6i61:"f1dfjoow",vm6p8p:"f1j98vj9",x3br3k:"fj8yq94",ze5xyy:"f4xjyn1",Bx3q9su:"f1et0tmh",pc6evw:"f9ddjv3",xd2cci:"f1wi8ngl"},transparent:{De3pzq:"f1c21dwh",g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],sj55zd:"fkfq4zb",Jwef8y:"fjxutwb",Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],Bi91k9c:"f139oj5f",Bk3fhr4:"ft1hn21",Bmfj8id:"fuxngvv",iro3zm:"fwiml72",b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"],B2d53fq:"f1fg1p5m",em6i61:"f1dfjoow",vm6p8p:"f1j98vj9",Bqrx1nm:"f1tme0vf",ze5xyy:"f4xjyn1",g2kj27:"f18onu3q",pc6evw:"f9ddjv3"},circular:{Bbmb7ep:["f8fbkgy","f1nfllo7"],Beyfa6y:["f1nfllo7","f8fbkgy"],B7oj6ja:["f1djnp8u","f1s8kh49"],Btl43ni:["f1s8kh49","f1djnp8u"]},rounded:{},square:{Bbmb7ep:["fzi6hpg","fyowgf4"],Beyfa6y:["fyowgf4","fzi6hpg"],B7oj6ja:["f3fg2lr","f13av6d4"],Btl43ni:["f13av6d4","f3fg2lr"]},small:{Bf4jedk:"fh7ncta",z8tnut:"f1khb0e9",z189sj:["f1vdfbxk","f1f5gg8d"],Byoj8tv:"f1jnq6q7",uwmqm3:["f1f5gg8d","f1vdfbxk"],Bbmb7ep:["f1aa9q02","f16jpd5f"],Beyfa6y:["f16jpd5f","f1aa9q02"],B7oj6ja:["f1jar5jt","fyu767a"],Btl43ni:["fyu767a","f1jar5jt"],Be2twd7:"fy9rknc",Bhrd7zp:"figsok6",Bg96gwp:"fwrc4pm"},smallWithIcon:{Byoj8tv:"f1brlhvm",z8tnut:"f1sl3k7w"},medium:{},large:{Bf4jedk:"f14es27b",z8tnut:"fp9bwmr",z189sj:["fjodcmx","fhx4nu"],Byoj8tv:"f150uoa4",uwmqm3:["fhx4nu","fjodcmx"],Bbmb7ep:["f1aa9q02","f16jpd5f"],Beyfa6y:["f16jpd5f","f1aa9q02"],B7oj6ja:["f1jar5jt","fyu767a"],Btl43ni:["fyu767a","f1jar5jt"],Be2twd7:"fod5ikn",Bhrd7zp:"fl43uef",Bg96gwp:"faaz57k"},largeWithIcon:{Byoj8tv:"fy7v416",z8tnut:"f1a1bwwz"}},{d:[".f1c21dwh{background-color:var(--colorTransparentBackground);}",".ffp7eso{background-color:var(--colorBrandBackground);}",".f1p3nwhy{border-top-color:transparent;}",".f11589ue{border-right-color:transparent;}",".f1pdflbu{border-left-color:transparent;}",".f1q5o8ev{border-bottom-color:transparent;}",".f1phragk{color:var(--colorNeutralForegroundOnBrand);}",".fhovq9v{background-color:var(--colorSubtleBackground);}",".fkfq4zb{color:var(--colorNeutralForeground2);}",".f8fbkgy{border-bottom-right-radius:var(--borderRadiusCircular);}",".f1nfllo7{border-bottom-left-radius:var(--borderRadiusCircular);}",".f1djnp8u{border-top-right-radius:var(--borderRadiusCircular);}",".f1s8kh49{border-top-left-radius:var(--borderRadiusCircular);}",".fzi6hpg{border-bottom-right-radius:var(--borderRadiusNone);}",".fyowgf4{border-bottom-left-radius:var(--borderRadiusNone);}",".f3fg2lr{border-top-right-radius:var(--borderRadiusNone);}",".f13av6d4{border-top-left-radius:var(--borderRadiusNone);}",".fh7ncta{min-width:64px;}",".f1khb0e9{padding-top:3px;}",".f1vdfbxk{padding-right:var(--spacingHorizontalS);}",".f1f5gg8d{padding-left:var(--spacingHorizontalS);}",".f1jnq6q7{padding-bottom:3px;}",".f1aa9q02{border-bottom-right-radius:var(--borderRadiusMedium);}",".f16jpd5f{border-bottom-left-radius:var(--borderRadiusMedium);}",".f1jar5jt{border-top-right-radius:var(--borderRadiusMedium);}",".fyu767a{border-top-left-radius:var(--borderRadiusMedium);}",".fy9rknc{font-size:var(--fontSizeBase200);}",".figsok6{font-weight:var(--fontWeightRegular);}",".fwrc4pm{line-height:var(--lineHeightBase200);}",".f1brlhvm{padding-bottom:1px;}",".f1sl3k7w{padding-top:1px;}",".f14es27b{min-width:96px;}",".fp9bwmr{padding-top:8px;}",".fjodcmx{padding-right:var(--spacingHorizontalL);}",".fhx4nu{padding-left:var(--spacingHorizontalL);}",".f150uoa4{padding-bottom:8px;}",".fod5ikn{font-size:var(--fontSizeBase400);}",".fl43uef{font-weight:var(--fontWeightSemibold);}",".faaz57k{line-height:var(--lineHeightBase400);}",".fy7v416{padding-bottom:7px;}",".f1a1bwwz{padding-top:7px;}"],h:[".fjxutwb:hover{background-color:var(--colorTransparentBackgroundHover);}",".fwiml72:hover:active{background-color:var(--colorTransparentBackgroundPressed);}",".f15wkkf3:hover{background-color:var(--colorBrandBackgroundHover);}",".f1s2uweq:hover{border-top-color:transparent;}",".fr80ssc:hover{border-right-color:transparent;}",".fecsdlb:hover{border-left-color:transparent;}",".f1ukrpxl:hover{border-bottom-color:transparent;}",".f1rq72xc:hover{color:var(--colorNeutralForegroundOnBrand);}",".fnp9lpt:hover:active{background-color:var(--colorBrandBackgroundPressed);}",".f1h0usnq:hover:active{border-top-color:transparent;}",".fs4ktlq:hover:active{border-right-color:transparent;}",".fx2bmrt:hover:active{border-left-color:transparent;}",".f16h9ulv:hover:active{border-bottom-color:transparent;}",".f1d6v5y2:hover:active{color:var(--colorNeutralForegroundOnBrand);}",".f1t94bn6:hover{background-color:var(--colorSubtleBackgroundHover);}",".fnwyq0v:hover{color:var(--colorNeutralForeground2Hover);}",".ft1hn21:hover .fui-Icon-filled{display:inline;}",".fuxngvv:hover .fui-Icon-regular{display:none;}",".fy5bs14:hover .fui-Button__icon{color:var(--colorNeutralForeground2BrandHover);}",".fsv2rcd:hover:active{background-color:var(--colorSubtleBackgroundPressed);}",".f1omzyqd:hover:active{color:var(--colorNeutralForeground2Pressed);}",".f1dfjoow:hover:active .fui-Icon-filled{display:inline;}",".f1j98vj9:hover:active .fui-Icon-regular{display:none;}",".fj8yq94:hover:active .fui-Button__icon{color:var(--colorNeutralForeground2BrandPressed);}",".f139oj5f:hover{color:var(--colorNeutralForeground2BrandHover);}",".f1fg1p5m:hover:active{color:var(--colorNeutralForeground2BrandPressed);}"],m:[["@media (forced-colors: active){.f1rirnrt{background-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1uu00uk{border-top-color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f9a0qzu{border-left-color:HighlightText;}.fkvaka8{border-right-color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1ux7til{border-bottom-color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1lkg8j3{color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fkc42ay{forced-color-adjust:none;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fq7113v:hover{background-color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.ff1wgvm:hover{border-top-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1x4h75k:hover{border-left-color:Highlight;}.fiob0tu:hover{border-right-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1j6scgf:hover{border-bottom-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f4xjyn1:hover{color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fbgcvur:hover:active{background-color:HighlightText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1ks1yx8:hover:active{border-top-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1o6qegi:hover:active{border-right-color:Highlight;}.fmxjhhp:hover:active{border-left-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fcnxywj:hover:active{border-bottom-color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f9ddjv3:hover:active{color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1et0tmh:hover .fui-Button__icon{color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1wi8ngl:hover:active .fui-Button__icon{color:Highlight;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1tme0vf:hover{background-color:var(--colorTransparentBackground);}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f18onu3q:hover:active{background-color:var(--colorTransparentBackground);}}",{m:"(forced-colors: active)"}]]}),qx=Ve({base:{De3pzq:"f1bg9a2p",g2u3we:"f1jj8ep1",h3c5rm:["f15xbau","fy0fskl"],B9xav0g:"f4ikngz",zhjwy3:["fy0fskl","f15xbau"],sj55zd:"f1s2aq7o",Bceei9c:"fdrzuqr",Bfinmwp:"f15x8b5r",Jwef8y:"f1falr9n",Bgoe8wy:"f12mpcsy",Bwzppfd:["f1gwvigk","f18rmfxp"],oetu4i:"f1jnshp0",gg5e9n:["f18rmfxp","f1gwvigk"],Bi91k9c:"fvgxktp",eoavqd:"fphbwmw",Bk3fhr4:"f19vpps7",Bmfj8id:"fv5swzo",Bbdnnc7:"f1al02dq",iro3zm:"f1t6o4dc",b661bw:"f10ztigi",Bk6r4ia:["f1ft5sdu","f1gzf82w"],B9zn80p:"f12zbtn2",Bpld233:["f1gzf82w","f1ft5sdu"],B2d53fq:"fcvwxyo",c3iz72:"f8w4c43",em6i61:"f1ol4fw6",vm6p8p:"f1q1lw4e",x3br3k:"f1dwjv2g"},highContrast:{Bsw6fvg:"f4lkoma",Bjwas2f:"fg455y9",Bn1d65q:["f1rvyvqg","f14g86mu"],Bxeuatn:"f1cwzwz",n51gp8:["f14g86mu","f1rvyvqg"],Bbusuzp:"f1dcs8yz",G867l3:"fjwq6ea",gdbnj:["f1lr3nhc","f1mbxvi6"],mxns5l:"fn5gmvv",o3nasb:["f1mbxvi6","f1lr3nhc"],Bqrx1nm:"f1vmkb5g",pgvf35:"f53ppgq",Bh7lczh:["f1663y11","f80fkiy"],dpv3f4:"f18v5270",Bpnjhaq:["f80fkiy","f1663y11"],ze5xyy:"f1kc2mi9",g2kj27:"f1y0svfh",Bf756sw:"fihuait",Bow2dr7:["fnxhupq","fyd6l6x"],Bvhedfk:"fx507ft",Gye4lf:["fyd6l6x","fnxhupq"],pc6evw:"fb3rf2x"},outline:{De3pzq:"f1c21dwh",Jwef8y:"f9ql6rf",iro3zm:"f3h1zc4"},primary:{g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"]},secondary:{},subtle:{De3pzq:"f1c21dwh",g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],Jwef8y:"f9ql6rf",Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],iro3zm:"f3h1zc4",b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"]},transparent:{De3pzq:"f1c21dwh",g2u3we:"f1p3nwhy",h3c5rm:["f11589ue","f1pdflbu"],B9xav0g:"f1q5o8ev",zhjwy3:["f1pdflbu","f11589ue"],Jwef8y:"f9ql6rf",Bgoe8wy:"f1s2uweq",Bwzppfd:["fr80ssc","fecsdlb"],oetu4i:"f1ukrpxl",gg5e9n:["fecsdlb","fr80ssc"],iro3zm:"f3h1zc4",b661bw:"f1h0usnq",Bk6r4ia:["fs4ktlq","fx2bmrt"],B9zn80p:"f16h9ulv",Bpld233:["fx2bmrt","fs4ktlq"]}},{d:[".f1bg9a2p{background-color:var(--colorNeutralBackgroundDisabled);}",".f1jj8ep1{border-top-color:var(--colorNeutralStrokeDisabled);}",".f15xbau{border-right-color:var(--colorNeutralStrokeDisabled);}",".fy0fskl{border-left-color:var(--colorNeutralStrokeDisabled);}",".f4ikngz{border-bottom-color:var(--colorNeutralStrokeDisabled);}",".f1s2aq7o{color:var(--colorNeutralForegroundDisabled);}",".fdrzuqr{cursor:not-allowed;}",".f15x8b5r .fui-Button__icon{color:var(--colorNeutralForegroundDisabled);}",".f1c21dwh{background-color:var(--colorTransparentBackground);}",".f1p3nwhy{border-top-color:transparent;}",".f11589ue{border-right-color:transparent;}",".f1pdflbu{border-left-color:transparent;}",".f1q5o8ev{border-bottom-color:transparent;}"],h:[".f1falr9n:hover{background-color:var(--colorNeutralBackgroundDisabled);}",".f12mpcsy:hover{border-top-color:var(--colorNeutralStrokeDisabled);}",".f1gwvigk:hover{border-right-color:var(--colorNeutralStrokeDisabled);}",".f18rmfxp:hover{border-left-color:var(--colorNeutralStrokeDisabled);}",".f1jnshp0:hover{border-bottom-color:var(--colorNeutralStrokeDisabled);}",".fvgxktp:hover{color:var(--colorNeutralForegroundDisabled);}",".fphbwmw:hover{cursor:not-allowed;}",".f19vpps7:hover .fui-Icon-filled{display:none;}",".fv5swzo:hover .fui-Icon-regular{display:inline;}",".f1al02dq:hover .fui-Button__icon{color:var(--colorNeutralForegroundDisabled);}",".f1t6o4dc:hover:active{background-color:var(--colorNeutralBackgroundDisabled);}",".f10ztigi:hover:active{border-top-color:var(--colorNeutralStrokeDisabled);}",".f1ft5sdu:hover:active{border-right-color:var(--colorNeutralStrokeDisabled);}",".f1gzf82w:hover:active{border-left-color:var(--colorNeutralStrokeDisabled);}",".f12zbtn2:hover:active{border-bottom-color:var(--colorNeutralStrokeDisabled);}",".fcvwxyo:hover:active{color:var(--colorNeutralForegroundDisabled);}",".f8w4c43:hover:active{cursor:not-allowed;}",".f1ol4fw6:hover:active .fui-Icon-filled{display:none;}",".f1q1lw4e:hover:active .fui-Icon-regular{display:inline;}",".f1dwjv2g:hover:active .fui-Button__icon{color:var(--colorNeutralForegroundDisabled);}",".f9ql6rf:hover{background-color:var(--colorTransparentBackground);}",".f3h1zc4:hover:active{background-color:var(--colorTransparentBackground);}",".f1s2uweq:hover{border-top-color:transparent;}",".fr80ssc:hover{border-right-color:transparent;}",".fecsdlb:hover{border-left-color:transparent;}",".f1ukrpxl:hover{border-bottom-color:transparent;}",".f1h0usnq:hover:active{border-top-color:transparent;}",".fs4ktlq:hover:active{border-right-color:transparent;}",".fx2bmrt:hover:active{border-left-color:transparent;}",".f16h9ulv:hover:active{border-bottom-color:transparent;}"],m:[["@media (forced-colors: active){.f4lkoma{background-color:ButtonFace;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fg455y9{border-top-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f14g86mu{border-left-color:GrayText;}.f1rvyvqg{border-right-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1cwzwz{border-bottom-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1dcs8yz{color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fjwq6ea:focus{border-top-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1lr3nhc:focus{border-right-color:GrayText;}.f1mbxvi6:focus{border-left-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fn5gmvv:focus{border-bottom-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1vmkb5g:hover{background-color:ButtonFace;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f53ppgq:hover{border-top-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1663y11:hover{border-right-color:GrayText;}.f80fkiy:hover{border-left-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f18v5270:hover{border-bottom-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1kc2mi9:hover{color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.f1y0svfh:hover:active{background-color:ButtonFace;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fihuait:hover:active{border-top-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fnxhupq:hover:active{border-right-color:GrayText;}.fyd6l6x:hover:active{border-left-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fx507ft:hover:active{border-bottom-color:GrayText;}}",{m:"(forced-colors: active)"}],["@media (forced-colors: active){.fb3rf2x:hover:active{color:GrayText;}}",{m:"(forced-colors: active)"}]]}),Ux=Ve({circular:{kdpuga:["fanj13w","f1gou5sz"],Bw81rd7:["f1gou5sz","fanj13w"],B6xbmo0:["fulf6x3","foeb2x"],dm238s:["foeb2x","fulf6x3"]},rounded:{},square:{kdpuga:["f1ndz5i7","f1co4qro"],Bw81rd7:["f1co4qro","f1ndz5i7"],B6xbmo0:["f146y5a9","f1k2ftg"],dm238s:["f1k2ftg","f146y5a9"]},primary:{B8q5s1w:"f17t0x8g",Bci5o5g:["f194v5ow","fk7jm04"],n8qw10:"f1qgg65p",Bdrgwmp:["fk7jm04","f194v5ow"],j6ew2k:["fhgccpy","fjo7pq6"],he4mth:"f32wu9k",Byr4aka:"fu5nqqq",lks7q5:["f13prjl2","f1nl83rv"],Bnan3qt:"f1czftr5",k1dn9:["f1nl83rv","f13prjl2"],Boium3a:["f12k37oa","fdnykm2"],tm8e47:"fr96u23"},small:{kdpuga:["fg3gtdo","fwii5mg"],Bw81rd7:["fwii5mg","fg3gtdo"],B6xbmo0:["f1palphq","f12nxie7"],dm238s:["f12nxie7","f1palphq"]},medium:{},large:{kdpuga:["ft3lys4","f1la4x2g"],Bw81rd7:["f1la4x2g","ft3lys4"],B6xbmo0:["f156y0zm","fakimq4"],dm238s:["fakimq4","f156y0zm"]}},{d:[".fanj13w[data-fui-focus-visible]{border-bottom-right-radius:var(--borderRadiusCircular);}",".f1gou5sz[data-fui-focus-visible]{border-bottom-left-radius:var(--borderRadiusCircular);}",".fulf6x3[data-fui-focus-visible]{border-top-right-radius:var(--borderRadiusCircular);}",".foeb2x[data-fui-focus-visible]{border-top-left-radius:var(--borderRadiusCircular);}",".f1ndz5i7[data-fui-focus-visible]{border-bottom-right-radius:var(--borderRadiusNone);}",".f1co4qro[data-fui-focus-visible]{border-bottom-left-radius:var(--borderRadiusNone);}",".f146y5a9[data-fui-focus-visible]{border-top-right-radius:var(--borderRadiusNone);}",".f1k2ftg[data-fui-focus-visible]{border-top-left-radius:var(--borderRadiusNone);}",".f17t0x8g[data-fui-focus-visible]{border-top-color:var(--colorStrokeFocus2);}",".f194v5ow[data-fui-focus-visible]{border-right-color:var(--colorStrokeFocus2);}",".fk7jm04[data-fui-focus-visible]{border-left-color:var(--colorStrokeFocus2);}",".f1qgg65p[data-fui-focus-visible]{border-bottom-color:var(--colorStrokeFocus2);}",".fhgccpy[data-fui-focus-visible]{box-shadow:var(--shadow2),0 0 0 var(--strokeWidthThin) var(--colorStrokeFocus2) inset,0 0 0 var(--strokeWidthThick) var(--colorNeutralForegroundOnBrand) inset;}",".fjo7pq6[data-fui-focus-visible]{box-shadow:var(--shadow2),0 0 0 var(--strokeWidthThin) var(--colorStrokeFocus2) inset,0 0 0 var(--strokeWidthThick) var(--colorNeutralForegroundOnBrand) inset;}",".f32wu9k[data-fui-focus-visible]:hover{box-shadow:var(--shadow2),0 0 0 var(--strokeWidthThin) var(--colorStrokeFocus2) inset;}",".fu5nqqq[data-fui-focus-visible]:hover{border-top-color:var(--colorStrokeFocus2);}",".f13prjl2[data-fui-focus-visible]:hover{border-right-color:var(--colorStrokeFocus2);}",".f1nl83rv[data-fui-focus-visible]:hover{border-left-color:var(--colorStrokeFocus2);}",".f1czftr5[data-fui-focus-visible]:hover{border-bottom-color:var(--colorStrokeFocus2);}",".fg3gtdo[data-fui-focus-visible]{border-bottom-right-radius:var(--borderRadiusSmall);}",".fwii5mg[data-fui-focus-visible]{border-bottom-left-radius:var(--borderRadiusSmall);}",".f1palphq[data-fui-focus-visible]{border-top-right-radius:var(--borderRadiusSmall);}",".f12nxie7[data-fui-focus-visible]{border-top-left-radius:var(--borderRadiusSmall);}",".ft3lys4[data-fui-focus-visible]{border-bottom-right-radius:var(--borderRadiusLarge);}",".f1la4x2g[data-fui-focus-visible]{border-bottom-left-radius:var(--borderRadiusLarge);}",".f156y0zm[data-fui-focus-visible]{border-top-right-radius:var(--borderRadiusLarge);}",".fakimq4[data-fui-focus-visible]{border-top-left-radius:var(--borderRadiusLarge);}"],t:["@supports (-moz-appearance:button){.f12k37oa[data-fui-focus-visible]{box-shadow:var(--shadow2),0 0 0 calc(var(--strokeWidthThin) + 0.25px) var(--colorStrokeFocus2) inset,0 0 0 var(--strokeWidthThick) var(--colorNeutralForegroundOnBrand) inset;}.fdnykm2[data-fui-focus-visible]{box-shadow:var(--shadow2),0 0 0 calc(var(--strokeWidthThin) + 0.25px) var(--colorStrokeFocus2) inset,0 0 0 var(--strokeWidthThick) var(--colorNeutralForegroundOnBrand) inset;}}","@supports (-moz-appearance:button){.fr96u23[data-fui-focus-visible]:hover{box-shadow:var(--shadow2),0 0 0 calc(var(--strokeWidthThin) + 0.25px) var(--colorStrokeFocus2) inset;}}"]}),Vx=Ve({small:{z8tnut:"f1sl3k7w",z189sj:["f136y8j8","f10xn8zz"],Byoj8tv:"f1brlhvm",uwmqm3:["f10xn8zz","f136y8j8"],Bf4jedk:"f17fgpbq",B2u0y6b:"f1jt17bm"},medium:{z8tnut:"f1sbtcvk",z189sj:["fwiuce9","f15vdbe4"],Byoj8tv:"fdghr9",uwmqm3:["f15vdbe4","fwiuce9"],Bf4jedk:"fwbmr0d",B2u0y6b:"f44c6la"},large:{z8tnut:"f1a1bwwz",z189sj:["f18k1jr3","f1rtp3s9"],Byoj8tv:"fy7v416",uwmqm3:["f1rtp3s9","f18k1jr3"],Bf4jedk:"f12clzc2",B2u0y6b:"fjy1crr"}},{d:[".f1sl3k7w{padding-top:1px;}",".f136y8j8{padding-right:1px;}",".f10xn8zz{padding-left:1px;}",".f1brlhvm{padding-bottom:1px;}",".f17fgpbq{min-width:24px;}",".f1jt17bm{max-width:24px;}",".f1sbtcvk{padding-top:5px;}",".fwiuce9{padding-right:5px;}",".f15vdbe4{padding-left:5px;}",".fdghr9{padding-bottom:5px;}",".fwbmr0d{min-width:32px;}",".f44c6la{max-width:32px;}",".f1a1bwwz{padding-top:7px;}",".f18k1jr3{padding-right:7px;}",".f1rtp3s9{padding-left:7px;}",".fy7v416{padding-bottom:7px;}",".f12clzc2{min-width:40px;}",".fjy1crr{max-width:40px;}"]}),Kx=Ve({small:{Be2twd7:"fe5j1ua",Bqenvij:"fjamq6b",a9b677:"f64fuq3",Bqrlyyl:"fbaiahx"},medium:{},large:{Be2twd7:"f1rt2boy",Bqenvij:"frvgh55",a9b677:"fq4mcun",Bqrlyyl:"f1exjqw5"},before:{t21cq0:["f1nizpg2","f1a695kz"]},after:{Frg6f3:["f1a695kz","f1nizpg2"]}},{d:[".fe5j1ua{font-size:20px;}",".fjamq6b{height:20px;}",".f64fuq3{width:20px;}",".fbaiahx{--fui-Button__icon--spacing:var(--spacingHorizontalXS);}",".f1rt2boy{font-size:24px;}",".frvgh55{height:24px;}",".fq4mcun{width:24px;}",".f1exjqw5{--fui-Button__icon--spacing:var(--spacingHorizontalSNudge);}",".f1nizpg2{margin-right:var(--fui-Button__icon--spacing);}",".f1a695kz{margin-left:var(--fui-Button__icon--spacing);}"]}),Xx=e=>{const t=Hx(),r=Wx(),o=$x(),n=qx(),i=Ux(),l=Vx(),s=Kx(),{appearance:a,disabled:u,disabledFocusable:d,icon:f,iconOnly:g,iconPosition:y,shape:v,size:k}=e;return e.root.className=_e(Pv.root,t,a&&o[a],o[k],f&&k==="small"&&o.smallWithIcon,f&&k==="large"&&o.largeWithIcon,o[v],(u||d)&&n.base,(u||d)&&n.highContrast,a&&(u||d)&&n[a],a==="primary"&&i.primary,i[k],i[v],g&&l[k],e.root.className),e.icon&&(e.icon.className=_e(Pv.icon,r,!!e.root.children&&s[y],s[k],e.icon.className)),e},fc=x.exports.forwardRef((e,t)=>{const r=jx(e,t);return Xx(r),_r("useButtonStyles_unstable")(r),Lx(r)});fc.displayName="Button";const Tv=x.exports.createContext(void 0);Tv.Provider;const Yx=()=>x.exports.useContext(Tv),Qx=(e,t)=>{const{disabled:r=!1,required:o=!1,weight:n="regular",size:i="medium"}=e;return{disabled:r,required:Dl(o===!0?"*":o||void 0,{defaultProps:{"aria-hidden":"true"},elementType:"span"}),weight:n,size:i,components:{root:"label",required:"span"},root:wt(Xt("label",{ref:t,...e}),{elementType:"label"})}},Jx=e=>Yr(e.root,{children:[e.root.children,e.required&&G(e.required,{})]}),Dv={root:"fui-Label",required:"fui-Label__required"},Zx=Ve({root:{Bahqtrf:"fk6fouc",sj55zd:"f19n0e5"},disabled:{sj55zd:"f1s2aq7o",Bbusuzp:"f1dcs8yz"},required:{sj55zd:"f1whyuy6",uwmqm3:["fruq291","f7x41pl"]},small:{Be2twd7:"fy9rknc",Bg96gwp:"fwrc4pm"},medium:{Be2twd7:"fkhj508",Bg96gwp:"f1i3iumi"},large:{Be2twd7:"fod5ikn",Bg96gwp:"faaz57k",Bhrd7zp:"fl43uef"},semibold:{Bhrd7zp:"fl43uef"}},{d:[".fk6fouc{font-family:var(--fontFamilyBase);}",".f19n0e5{color:var(--colorNeutralForeground1);}",".f1s2aq7o{color:var(--colorNeutralForegroundDisabled);}",".f1whyuy6{color:var(--colorPaletteRedForeground3);}",".fruq291{padding-left:var(--spacingHorizontalXS);}",".f7x41pl{padding-right:var(--spacingHorizontalXS);}",".fy9rknc{font-size:var(--fontSizeBase200);}",".fwrc4pm{line-height:var(--lineHeightBase200);}",".fkhj508{font-size:var(--fontSizeBase300);}",".f1i3iumi{line-height:var(--lineHeightBase300);}",".fod5ikn{font-size:var(--fontSizeBase400);}",".faaz57k{line-height:var(--lineHeightBase400);}",".fl43uef{font-weight:var(--fontWeightSemibold);}"],m:[["@media (forced-colors: active){.f1dcs8yz{color:GrayText;}}",{m:"(forced-colors: active)"}]]}),Gx=e=>{const t=Zx();return e.root.className=_e(Dv.root,t.root,e.disabled&&t.disabled,t[e.size],e.weight==="semibold"&&t.semibold,e.root.className),e.required&&(e.required.className=_e(Dv.required,t.required,e.disabled&&t.disabled,e.required.className)),e},Ov=x.exports.forwardRef((e,t)=>{const r=Qx(e,t);return Gx(r),_r("useLabelStyles_unstable")(r),Jx(r)});Ov.displayName="Label";const to="__fluentDisableScrollElement";function e2(){const{targetDocument:e}=Dt();return x.exports.useCallback(()=>{if(e)return t2(e.body)},[e])}function t2(e){var t;const{clientWidth:r}=e.ownerDocument.documentElement;var o;const n=(o=(t=e.ownerDocument.defaultView)===null||t===void 0?void 0:t.innerWidth)!==null&&o!==void 0?o:0;return r2(e),e[to].count===0&&(e.style.overflow="hidden",e.style.paddingRight=`${n-r}px`),e[to].count++,()=>{e[to].count--,e[to].count===0&&(e.style.overflow=e[to].previousOverflowStyle,e.style.paddingRight=e[to].previousPaddingRightStyle)}}function r2(e){var t,r,o;(o=(t=e)[r=to])!==null&&o!==void 0||(t[r]={count:0,previousOverflowStyle:e.style.overflow,previousPaddingRightStyle:e.style.paddingRight})}function o2(e,t){const{findFirstFocusable:r}=wh(),{targetDocument:o}=Dt(),n=x.exports.useRef(null);return x.exports.useEffect(()=>{if(!e)return;const i=n.current&&r(n.current);if(i)i.focus();else{var l;(l=n.current)===null||l===void 0||l.focus()}},[r,e,t,o]),n}const n2={open:!1,inertTrapFocus:!1,modalType:"modal",isNestedDialog:!1,dialogRef:{current:null},requestOpenChange(){}},gc=Uh(void 0),i2=gc.Provider,Gt=e=>Vh(gc,(t=n2)=>e(t)),l2=!1,Rv=x.exports.createContext(void 0),zv=Rv.Provider,s2=()=>{var e;return(e=x.exports.useContext(Rv))!==null&&e!==void 0?e:l2},a2=e=>{const{children:t,modalType:r="modal",onOpenChange:o,inertTrapFocus:n=!1}=e,[i,l]=u2(t),[s,a]=zp({state:e.open,defaultState:e.defaultOpen,initialState:!1}),u=ze(k=>{o==null||o(k.event,k),k.event.isDefaultPrevented()||a(k.open)}),d=o2(s,r),f=e2(),g=Boolean(s&&r!=="non-modal");Yt(()=>{if(g)return f()},[f,g]);const{modalAttributes:y,triggerAttributes:v}=Vu({trapFocus:r!=="non-modal",legacyTrapFocus:!n});return{components:{backdrop:"div"},inertTrapFocus:n,open:s,modalType:r,content:l,trigger:i,requestOpenChange:u,dialogTitleId:Fu("dialog-title-"),isNestedDialog:VB(gc),dialogRef:d,modalAttributes:r!=="non-modal"?y:void 0,triggerAttributes:v}};function u2(e){const t=x.exports.Children.toArray(e);switch(t.length){case 2:return t;case 1:return[void 0,t[0]];default:return[void 0,void 0]}}function c2(e,t){if(e==null)return{};var r={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function pc(e,t){return pc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,n){return o.__proto__=n,o},pc(e,t)}function d2(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,pc(e,t)}const Av={disabled:!1},Lv=V.createContext(null);var f2=function(t){return t.scrollTop},Zn="unmounted",ro="exited",oo="entering",qo="entered",hc="exiting",er=function(e){d2(t,e);function t(o,n){var i;i=e.call(this,o,n)||this;var l=n,s=l&&!l.isMounting?o.enter:o.appear,a;return i.appearStatus=null,o.in?s?(a=ro,i.appearStatus=oo):a=qo:o.unmountOnExit||o.mountOnEnter?a=Zn:a=ro,i.state={status:a},i.nextCallback=null,i}t.getDerivedStateFromProps=function(n,i){var l=n.in;return l&&i.status===Zn?{status:ro}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(n){var i=null;if(n!==this.props){var l=this.state.status;this.props.in?l!==oo&&l!==qo&&(i=oo):(l===oo||l===qo)&&(i=hc)}this.updateStatus(!1,i)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var n=this.props.timeout,i,l,s;return i=l=s=n,n!=null&&typeof n!="number"&&(i=n.exit,l=n.enter,s=n.appear!==void 0?n.appear:l),{exit:i,enter:l,appear:s}},r.updateStatus=function(n,i){if(n===void 0&&(n=!1),i!==null)if(this.cancelNextCallback(),i===oo){if(this.props.unmountOnExit||this.props.mountOnEnter){var l=this.props.nodeRef?this.props.nodeRef.current:pl.findDOMNode(this);l&&f2(l)}this.performEnter(n)}else this.performExit();else this.props.unmountOnExit&&this.state.status===ro&&this.setState({status:Zn})},r.performEnter=function(n){var i=this,l=this.props.enter,s=this.context?this.context.isMounting:n,a=this.props.nodeRef?[s]:[pl.findDOMNode(this),s],u=a[0],d=a[1],f=this.getTimeouts(),g=s?f.appear:f.enter;if(!n&&!l||Av.disabled){this.safeSetState({status:qo},function(){i.props.onEntered(u)});return}this.props.onEnter(u,d),this.safeSetState({status:oo},function(){i.props.onEntering(u,d),i.onTransitionEnd(g,function(){i.safeSetState({status:qo},function(){i.props.onEntered(u,d)})})})},r.performExit=function(){var n=this,i=this.props.exit,l=this.getTimeouts(),s=this.props.nodeRef?void 0:pl.findDOMNode(this);if(!i||Av.disabled){this.safeSetState({status:ro},function(){n.props.onExited(s)});return}this.props.onExit(s),this.safeSetState({status:hc},function(){n.props.onExiting(s),n.onTransitionEnd(l.exit,function(){n.safeSetState({status:ro},function(){n.props.onExited(s)})})})},r.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(n,i){i=this.setNextCallback(i),this.setState(n,i)},r.setNextCallback=function(n){var i=this,l=!0;return this.nextCallback=function(s){l&&(l=!1,i.nextCallback=null,n(s))},this.nextCallback.cancel=function(){l=!1},this.nextCallback},r.onTransitionEnd=function(n,i){this.setNextCallback(i);var l=this.props.nodeRef?this.props.nodeRef.current:pl.findDOMNode(this),s=n==null&&!this.props.addEndListener;if(!l||s){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var a=this.props.nodeRef?[this.nextCallback]:[l,this.nextCallback],u=a[0],d=a[1];this.props.addEndListener(u,d)}n!=null&&setTimeout(this.nextCallback,n)},r.render=function(){var n=this.state.status;if(n===Zn)return null;var i=this.props,l=i.children;i.in,i.mountOnEnter,i.unmountOnExit,i.appear,i.enter,i.exit,i.timeout,i.addEndListener,i.onEnter,i.onEntering,i.onEntered,i.onExit,i.onExiting,i.onExited,i.nodeRef;var s=c2(i,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return V.createElement(Lv.Provider,{value:null},typeof l=="function"?l(n,s):V.cloneElement(V.Children.only(l),s))},t}(V.Component);er.contextType=Lv,er.propTypes={};function Uo(){}er.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Uo,onEntering:Uo,onEntered:Uo,onExit:Uo,onExiting:Uo,onExited:Uo},er.UNMOUNTED=Zn,er.EXITED=ro,er.ENTERING=oo,er.ENTERED=qo,er.EXITING=hc;const g2=er,p2=void 0,Iv=x.exports.createContext(void 0),h2=Iv.Provider,v2=()=>{var e;return(e=x.exports.useContext(Iv))!==null&&e!==void 0?e:p2},m2=(e,t)=>{const{content:r,trigger:o}=e;return G(i2,{value:t.dialog,children:Yr(zv,{value:t.dialogSurface,children:[o,G(g2,{mountOnEnter:!0,unmountOnExit:!0,in:e.open,nodeRef:e.dialogRef,appear:!0,timeout:250,children:n=>G(h2,{value:n,children:r})})]})})};function y2(e){const{modalType:t,open:r,dialogRef:o,dialogTitleId:n,isNestedDialog:i,inertTrapFocus:l,requestOpenChange:s,modalAttributes:a,triggerAttributes:u}=e;return{dialog:{open:r,modalType:t,dialogRef:o,dialogTitleId:n,isNestedDialog:i,inertTrapFocus:l,modalAttributes:a,triggerAttributes:u,requestOpenChange:s},dialogSurface:!1}}const Mv=x.exports.memo(e=>{const t=a2(e),r=y2(t);return m2(t,r)});Mv.displayName="Dialog";const k2=e=>{const t=s2(),{children:r,disableButtonEnhancement:o=!1,action:n=t?"close":"open"}=e,i=Up(r),l=Gt(f=>f.requestOpenChange),{triggerAttributes:s}=Vu(),a=ze(f=>{var g,y;i==null||(g=(y=i.props).onClick)===null||g===void 0||g.call(y,f),f.isDefaultPrevented()||l({event:f,type:"triggerClick",open:n==="open"})}),u={...i==null?void 0:i.props,ref:i==null?void 0:i.ref,onClick:a,...s},d=Kh((i==null?void 0:i.type)==="button"||(i==null?void 0:i.type)==="a"?i.type:"div",{...u,type:"button"});return{children:db(r,o?u:d)}},b2=e=>e.children,vc=e=>{const t=k2(e);return b2(t)};vc.displayName="DialogTrigger",vc.isFluentTriggerComponent=!0;const w2=(e,t)=>{var r;return{components:{root:"div"},root:wt(Xt((r=e.as)!==null&&r!==void 0?r:"div",{ref:t,...e}),{elementType:"div"})}},B2=e=>G(e.root,{}),_2={root:"fui-DialogBody"},x2=Kt("r1h3qql9",null,{r:[".r1h3qql9{overflow:unset;gap:8px;display:grid;max-height:calc(100vh - 2 * 24px);box-sizing:border-box;grid-template-rows:auto 1fr;grid-template-columns:1fr 1fr auto;}"],s:["@media screen and (max-width: 480px){.r1h3qql9{max-width:100vw;grid-template-rows:auto 1fr auto;}}","@media screen and (max-height: 359px){.r1h3qql9{max-height:unset;}}"]}),S2=e=>{const t=x2();return e.root.className=_e(_2.root,t,e.root.className),e},jv=x.exports.forwardRef((e,t)=>{const r=w2(e,t);return S2(r),_r("useDialogBodyStyles_unstable")(r),B2(r)});jv.displayName="DialogBody";const Hv={root:"fui-DialogTitle",action:"fui-DialogTitle__action"},E2=Kt("rxjm636",null,[".rxjm636{font-family:var(--fontFamilyBase);font-size:var(--fontSizeBase500);font-weight:var(--fontWeightSemibold);line-height:var(--lineHeightBase500);margin:0;grid-row-start:1;grid-row-end:1;grid-column-start:1;grid-column-end:3;}"]),N2=Ve({rootWithoutAction:{Bw0ie65:"fsyjsko"}},{d:[".fsyjsko{grid-column-end:4;}"]}),F2=Kt("r13kcrze",null,[".r13kcrze{grid-row-start:1;grid-row-end:1;grid-column-start:3;justify-self:end;align-self:start;}"]),C2=Kt("r8i4vpr","r15ezg2h",{r:[".r8i4vpr{overflow:visible;padding:0;border-style:none;position:relative;box-sizing:content-box;background-color:inherit;color:inherit;font-family:inherit;font-size:inherit;cursor:pointer;line-height:0;-webkit-appearance:button;text-align:unset;}",".r8i4vpr:focus{outline-style:none;}",".r8i4vpr:focus-visible{outline-style:none;}",".r8i4vpr[data-fui-focus-visible]{border-top-color:transparent;border-right-color:transparent;border-bottom-color:transparent;border-left-color:transparent;}",'.r8i4vpr[data-fui-focus-visible]::after{content:"";position:absolute;pointer-events:none;z-index:1;border-top-style:solid;border-right-style:solid;border-bottom-style:solid;border-left-style:solid;border-top-width:2px;border-right-width:2px;border-bottom-width:2px;border-left-width:2px;border-bottom-right-radius:var(--borderRadiusMedium);border-bottom-left-radius:var(--borderRadiusMedium);border-top-right-radius:var(--borderRadiusMedium);border-top-left-radius:var(--borderRadiusMedium);border-top-color:var(--colorStrokeFocus2);border-right-color:var(--colorStrokeFocus2);border-bottom-color:var(--colorStrokeFocus2);border-left-color:var(--colorStrokeFocus2);top:calc(2px * -1);right:calc(2px * -1);bottom:calc(2px * -1);left:calc(2px * -1);}',".r15ezg2h{overflow:visible;padding:0;border-style:none;position:relative;box-sizing:content-box;background-color:inherit;color:inherit;font-family:inherit;font-size:inherit;cursor:pointer;line-height:0;-webkit-appearance:button;text-align:unset;}",".r15ezg2h:focus{outline-style:none;}",".r15ezg2h:focus-visible{outline-style:none;}",".r15ezg2h[data-fui-focus-visible]{border-top-color:transparent;border-left-color:transparent;border-bottom-color:transparent;border-right-color:transparent;}",'.r15ezg2h[data-fui-focus-visible]::after{content:"";position:absolute;pointer-events:none;z-index:1;border-top-style:solid;border-left-style:solid;border-bottom-style:solid;border-right-style:solid;border-top-width:2px;border-left-width:2px;border-bottom-width:2px;border-right-width:2px;border-bottom-left-radius:var(--borderRadiusMedium);border-bottom-right-radius:var(--borderRadiusMedium);border-top-left-radius:var(--borderRadiusMedium);border-top-right-radius:var(--borderRadiusMedium);border-top-color:var(--colorStrokeFocus2);border-left-color:var(--colorStrokeFocus2);border-bottom-color:var(--colorStrokeFocus2);border-right-color:var(--colorStrokeFocus2);top:calc(2px * -1);left:calc(2px * -1);bottom:calc(2px * -1);right:calc(2px * -1);}'],s:["@media (forced-colors: active){.r8i4vpr[data-fui-focus-visible]::after{border-top-color:Highlight;border-right-color:Highlight;border-bottom-color:Highlight;border-left-color:Highlight;}}","@media (forced-colors: active){.r15ezg2h[data-fui-focus-visible]::after{border-top-color:Highlight;border-left-color:Highlight;border-bottom-color:Highlight;border-right-color:Highlight;}}"]}),P2=e=>{const t=E2(),r=F2(),o=N2();return e.root.className=_e(Hv.root,t,!e.action&&o.rootWithoutAction,e.root.className),e.action&&(e.action.className=_e(Hv.action,r,e.action.className)),e},T2=(e,t)=>{const{action:r}=e,o=Gt(i=>i.modalType),n=C2();return{components:{root:"h2",action:"div"},root:wt(Xt("h2",{ref:t,id:Gt(i=>i.dialogTitleId),...e}),{elementType:"h2"}),action:Dl(r,{renderByDefault:o==="non-modal",defaultProps:{children:x.exports.createElement(vc,{disableButtonEnhancement:!0,action:"close"},x.exports.createElement("button",{type:"button",className:n,"aria-label":"close"},x.exports.createElement(Cb,null)))},elementType:"div"})}},D2=e=>Yr(x.exports.Fragment,{children:[G(e.root,{children:e.root.children}),e.action&&G(e.action,{})]}),Wv=x.exports.forwardRef((e,t)=>{const r=T2(e,t);return P2(r),_r("useDialogTitleStyles_unstable")(r),D2(r)});Wv.displayName="DialogTitle";const O2=(e,t)=>{const r=Gt(g=>g.modalType),o=Gt(g=>g.isNestedDialog),n=v2(),i=Gt(g=>g.modalAttributes),l=Gt(g=>g.dialogRef),s=Gt(g=>g.requestOpenChange),a=Gt(g=>g.dialogTitleId),u=ze(g=>{if(pk(e.backdrop)){var y,v;(y=(v=e.backdrop).onClick)===null||y===void 0||y.call(v,g)}r==="modal"&&!g.isDefaultPrevented()&&s({event:g,open:!1,type:"backdropClick"})}),d=ze(g=>{var y;(y=e.onKeyDown)===null||y===void 0||y.call(e,g),g.key===KB&&!g.isDefaultPrevented()&&(s({event:g,open:!1,type:"escapeKeyDown"}),g.preventDefault())}),f=Dl(e.backdrop,{renderByDefault:r!=="non-modal",defaultProps:{"aria-hidden":"true"},elementType:"div"});return f&&(f.onClick=u),{components:{backdrop:"div",root:"div"},backdrop:f,isNestedDialog:o,transitionStatus:n,mountNode:e.mountNode,root:wt(Xt("div",{tabIndex:-1,"aria-modal":r!=="non-modal",role:r==="alert"?"alertdialog":"dialog","aria-labelledby":e["aria-label"]?void 0:a,...e,...i,onKeyDown:d,ref:Cu(t,l)}),{elementType:"div"})}},R2=(e,t)=>Yr(dc,{mountNode:e.mountNode,children:[e.backdrop&&G(e.backdrop,{}),G(zv,{value:t.dialogSurface,children:G(e.root,{})})]}),$v={root:"fui-DialogSurface",backdrop:"fui-DialogSurface__backdrop"},z2=Kt("rhzkxut","r1dhpx9",{r:[".rhzkxut{inset:0;padding:24px;margin:auto;border-style:none;overflow:unset;border:1px solid var(--colorTransparentStroke);border-radius:var(--borderRadiusXLarge);display:block;-webkit-user-select:unset;-moz-user-select:unset;-ms-user-select:unset;user-select:unset;visibility:unset;position:fixed;height:fit-content;max-width:600px;max-height:100vh;box-sizing:border-box;background-color:var(--colorNeutralBackground1);color:var(--colorNeutralForeground1);}",".rhzkxut:focus{outline-style:none;}",".rhzkxut:focus-visible{outline-style:none;}",".rhzkxut[data-fui-focus-visible]{border-top-color:transparent;border-right-color:transparent;border-bottom-color:transparent;border-left-color:transparent;}",'.rhzkxut[data-fui-focus-visible]::after{content:"";position:absolute;pointer-events:none;z-index:1;border-top-style:solid;border-right-style:solid;border-bottom-style:solid;border-left-style:solid;border-top-width:2px;border-right-width:2px;border-bottom-width:2px;border-left-width:2px;border-bottom-right-radius:var(--borderRadiusMedium);border-bottom-left-radius:var(--borderRadiusMedium);border-top-right-radius:var(--borderRadiusMedium);border-top-left-radius:var(--borderRadiusMedium);border-top-color:var(--colorStrokeFocus2);border-right-color:var(--colorStrokeFocus2);border-bottom-color:var(--colorStrokeFocus2);border-left-color:var(--colorStrokeFocus2);top:calc(2px * -1);right:calc(2px * -1);bottom:calc(2px * -1);left:calc(2px * -1);}',".r1dhpx9{inset:0;padding:24px;margin:auto;border-style:none;overflow:unset;border:1px solid var(--colorTransparentStroke);border-radius:var(--borderRadiusXLarge);display:block;-webkit-user-select:unset;-moz-user-select:unset;-ms-user-select:unset;user-select:unset;visibility:unset;position:fixed;height:fit-content;max-width:600px;max-height:100vh;box-sizing:border-box;background-color:var(--colorNeutralBackground1);color:var(--colorNeutralForeground1);}",".r1dhpx9:focus{outline-style:none;}",".r1dhpx9:focus-visible{outline-style:none;}",".r1dhpx9[data-fui-focus-visible]{border-top-color:transparent;border-left-color:transparent;border-bottom-color:transparent;border-right-color:transparent;}",'.r1dhpx9[data-fui-focus-visible]::after{content:"";position:absolute;pointer-events:none;z-index:1;border-top-style:solid;border-left-style:solid;border-bottom-style:solid;border-right-style:solid;border-top-width:2px;border-left-width:2px;border-bottom-width:2px;border-right-width:2px;border-bottom-left-radius:var(--borderRadiusMedium);border-bottom-right-radius:var(--borderRadiusMedium);border-top-left-radius:var(--borderRadiusMedium);border-top-right-radius:var(--borderRadiusMedium);border-top-color:var(--colorStrokeFocus2);border-left-color:var(--colorStrokeFocus2);border-bottom-color:var(--colorStrokeFocus2);border-right-color:var(--colorStrokeFocus2);top:calc(2px * -1);left:calc(2px * -1);bottom:calc(2px * -1);right:calc(2px * -1);}'],s:["@media (forced-colors: active){.rhzkxut[data-fui-focus-visible]::after{border-top-color:Highlight;border-right-color:Highlight;border-bottom-color:Highlight;border-left-color:Highlight;}}","@media screen and (max-width: 480px){.rhzkxut{max-width:100vw;}}","@media screen and (max-height: 359px){.rhzkxut{overflow-y:auto;padding-right:calc(24px - 4px);border-right-width:4px;border-top-width:4px;border-bottom-width:4px;}}","@media (forced-colors: active){.r1dhpx9[data-fui-focus-visible]::after{border-top-color:Highlight;border-left-color:Highlight;border-bottom-color:Highlight;border-right-color:Highlight;}}","@media screen and (max-width: 480px){.r1dhpx9{max-width:100vw;}}","@media screen and (max-height: 359px){.r1dhpx9{overflow-y:auto;padding-left:calc(24px - 4px);border-left-width:4px;border-top-width:4px;border-bottom-width:4px;}}"]}),A2=Ve({animated:{abs64n:"fk73vx1",E5pizo:"f1yzz98r",Bz10aip:"f15ofi6c"},static:{E5pizo:"f10nrhrw"},unmounted:{},entering:{B3o57yi:"fc397y7",Bmy1vo4:"f1b86uth",Bkqvd7p:"f18ad807",E5pizo:"f10nrhrw",Bz10aip:"f186d0ee",abs64n:"f5p0z4x"},entered:{E5pizo:"f10nrhrw",Bz10aip:"f186d0ee",abs64n:"f5p0z4x"},idle:{E5pizo:"f10nrhrw",Bz10aip:"f186d0ee",abs64n:"f5p0z4x"},exiting:{B3o57yi:"fc397y7",Bmy1vo4:"f1b86uth",Bkqvd7p:"f1mfizis"},exited:{}},{d:[".fk73vx1{opacity:0;}",".f1yzz98r{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.1);}",".f15ofi6c{transform:scale(0.85) translateZ(0);}",".f10nrhrw{box-shadow:var(--shadow64);}",".fc397y7{transition-duration:var(--durationGentle);}",".f1b86uth{transition-property:opacity,transform,box-shadow;}",".f18ad807{transition-timing-function:var(--curveDecelerateMid);}",".f186d0ee{transform:scale(1) translateZ(0);}",".f5p0z4x{opacity:1;}",".f1mfizis{transition-timing-function:var(--curveAccelerateMin);}"]}),L2=Kt("r19ug08i",null,[".r19ug08i{inset:0px;background-color:rgba(0, 0, 0, 0.4);position:fixed;transition-duration:var(--durationGentle);transition-timing-function:var(--curveLinear);transition-property:opacity;will-change:opacity;opacity:0;}"]),I2=Ve({nestedDialogBackdrop:{De3pzq:"f1c21dwh"},unmounted:{},entering:{abs64n:"f5p0z4x"},entered:{abs64n:"f5p0z4x"},idle:{abs64n:"f5p0z4x"},exiting:{Bkqvd7p:"f1mfizis"},exited:{}},{d:[".f1c21dwh{background-color:var(--colorTransparentBackground);}",".f5p0z4x{opacity:1;}",".f1mfizis{transition-timing-function:var(--curveAccelerateMin);}"]}),M2=e=>{const{isNestedDialog:t,root:r,backdrop:o,transitionStatus:n}=e,i=z2(),l=A2(),s=L2(),a=I2();return r.className=_e($v.root,i,n?l.animated:l.static,n&&l[n],r.className),o&&(o.className=_e($v.backdrop,s,t&&a.nestedDialogBackdrop,n&&a[n],o.className)),e};function j2(e){return{dialogSurface:!0}}const qv=x.exports.forwardRef((e,t)=>{const r=O2(e,t),o=j2();return M2(r),_r("useDialogSurfaceStyles_unstable")(r),R2(r,o)});qv.displayName="DialogSurface";const H2=(e,t)=>{var r;return{components:{root:"div"},root:wt(Xt((r=e.as)!==null&&r!==void 0?r:"div",{ref:t,...e}),{elementType:"div"})}},W2=e=>G(e.root,{}),$2={root:"fui-DialogContent"},q2=Kt("r1v5zwsm",null,{r:[".r1v5zwsm{padding:var(--strokeWidthThick);margin:calc(var(--strokeWidthThick) * -1);font-family:var(--fontFamilyBase);font-size:var(--fontSizeBase300);font-weight:var(--fontWeightRegular);line-height:var(--lineHeightBase300);overflow-y:auto;min-height:32px;box-sizing:border-box;grid-row-start:2;grid-row-end:2;grid-column-start:1;grid-column-end:4;}"],s:["@media screen and (max-height: 359px){.r1v5zwsm{overflow-y:unset;}}"]}),U2=e=>{const t=q2();return e.root.className=_e($2.root,t,e.root.className),e},Uv=x.exports.forwardRef((e,t)=>{const r=H2(e,t);return U2(r),_r("useDialogContentStyles_unstable")(r),W2(r)});Uv.displayName="DialogContent";const V2=e=>e<=0?1:e,K2=(e,t)=>e===void 0?e:e<0?0:e>t?t:e,X2=(e,t)=>{const r=Yx(),o=r==null?void 0:r.validationState,{color:n=o==="error"||o==="warning"||o==="success"?o:"brand",shape:i="rounded",thickness:l="medium"}=e;var s;const a=V2((s=e.max)!==null&&s!==void 0?s:1),u=K2(e.value,a),d=wt(Xt("div",{ref:t,role:"progressbar","aria-valuemin":u!==void 0?0:void 0,"aria-valuemax":u!==void 0?a:void 0,"aria-valuenow":u,"aria-labelledby":r==null?void 0:r.labelId,...e}),{elementType:"div"});r&&(r.validationMessageId||r.hintId)&&(d["aria-describedby"]=[r==null?void 0:r.validationMessageId,r==null?void 0:r.hintId,d["aria-describedby"]].filter(Boolean).join(" "));const f=wt(e.bar,{elementType:"div"});return{color:n,max:a,shape:i,thickness:l,value:u,components:{root:"div",bar:"div"},root:d,bar:f}},Y2=e=>G(e.root,{children:e.bar&&G(e.bar,{})}),Vv={root:"fui-ProgressBar",bar:"fui-ProgressBar__bar"},Q2=.01,J2=Ve({root:{mc9l5x:"ftgm304",De3pzq:"f18f03hv",a9b677:"fly5x3f",B68tc82:"f1p9o1ba",Bmxbyg5:"f1sil6mw",Bpep1pd:"fu42dvn"},rounded:{Bbmb7ep:["f1aa9q02","f16jpd5f"],Beyfa6y:["f16jpd5f","f1aa9q02"],B7oj6ja:["f1jar5jt","fyu767a"],Btl43ni:["fyu767a","f1jar5jt"]},square:{Bbmb7ep:["fzi6hpg","fyowgf4"],Beyfa6y:["fyowgf4","fzi6hpg"],B7oj6ja:["f3fg2lr","f13av6d4"],Btl43ni:["f13av6d4","f3fg2lr"]},medium:{Bqenvij:"f4t8t6x"},large:{Bqenvij:"f6ywr7j"}},{d:[".ftgm304{display:block;}",".f18f03hv{background-color:var(--colorNeutralBackground6);}",".fly5x3f{width:100%;}",".f1p9o1ba{overflow-x:hidden;}",".f1sil6mw{overflow-y:hidden;}",".f1aa9q02{border-bottom-right-radius:var(--borderRadiusMedium);}",".f16jpd5f{border-bottom-left-radius:var(--borderRadiusMedium);}",".f1jar5jt{border-top-right-radius:var(--borderRadiusMedium);}",".fyu767a{border-top-left-radius:var(--borderRadiusMedium);}",".fzi6hpg{border-bottom-right-radius:var(--borderRadiusNone);}",".fyowgf4{border-bottom-left-radius:var(--borderRadiusNone);}",".f3fg2lr{border-top-right-radius:var(--borderRadiusNone);}",".f13av6d4{border-top-left-radius:var(--borderRadiusNone);}",".f4t8t6x{height:2px;}",".f6ywr7j{height:4px;}"],m:[["@media screen and (forced-colors: active){.fu42dvn{background-color:CanvasText;}}",{m:"screen and (forced-colors: active)"}]]}),Z2=Ve({base:{Bpep1pd:"f1neahkh",Bbmb7ep:["f1d9uwra","fzibvwi"],Beyfa6y:["fzibvwi","f1d9uwra"],B7oj6ja:["fuoumxm","f1vtqnvc"],Btl43ni:["f1vtqnvc","fuoumxm"],Bqenvij:"f1l02sjl"},nonZeroDeterminate:{Bmy1vo4:"fjt6zfz",B3o57yi:"f1wofebd",Bkqvd7p:"fv71qf3"},indeterminate:{B2u0y6b:"fa0wk36",qhf8xq:"f10pi13n",Bcmaq0h:["fpo0yib","f1u5hf6c"],Bv12yb3:["fwd2bol","f14gig94"],vin17d:"f1a27w2r",Ezkn3b:"f452v7t",w3vfg9:"f1cpbl36",Gqtpxc:"f4akx1t",B3vm3ge:"f18p5put"},brand:{De3pzq:"ftywsgz"},error:{De3pzq:"fdl5y0r"},warning:{De3pzq:"f1s438gw"},success:{De3pzq:"flxk52p"}},{m:[["@media screen and (forced-colors: active){.f1neahkh{background-color:Highlight;}}",{m:"screen and (forced-colors: active)"}],["@media screen and (prefers-reduced-motion: reduce){.f4akx1t{animation-duration:0.01ms;}}",{m:"screen and (prefers-reduced-motion: reduce)"}],["@media screen and (prefers-reduced-motion: reduce){.f18p5put{animation-iteration-count:1;}}",{m:"screen and (prefers-reduced-motion: reduce)"}]],d:[".f1d9uwra{border-bottom-right-radius:inherit;}",".fzibvwi{border-bottom-left-radius:inherit;}",".fuoumxm{border-top-right-radius:inherit;}",".f1vtqnvc{border-top-left-radius:inherit;}",".f1l02sjl{height:100%;}",".fjt6zfz{transition-property:width;}",".f1wofebd{transition-duration:0.3s;}",".fv71qf3{transition-timing-function:ease;}",".fa0wk36{max-width:33%;}",".f10pi13n{position:relative;}",`.fpo0yib{background-image:linear-gradient(
      to right,
      var(--colorNeutralBackground6) 0%,
      var(--colorTransparentBackground) 50%,
      var(--colorNeutralBackground6) 100%
    );}`,`.f1u5hf6c{background-image:linear-gradient(
      to left,
      var(--colorNeutralBackground6) 0%,
      var(--colorTransparentBackground) 50%,
      var(--colorNeutralBackground6) 100%
    );}`,".fwd2bol{animation-name:f1keuaan;}",".f14gig94{animation-name:f10x8f8u;}",".f1a27w2r{animation-duration:3s;}",".f452v7t{animation-timing-function:linear;}",".f1cpbl36{animation-iteration-count:infinite;}",".ftywsgz{background-color:var(--colorCompoundBrandBackground);}",".fdl5y0r{background-color:var(--colorPaletteRedBackground3);}",".f1s438gw{background-color:var(--colorPaletteDarkOrangeBackground3);}",".flxk52p{background-color:var(--colorPaletteGreenBackground3);}"],k:["@keyframes f1keuaan{0%{left:-33%;}100%{left:100%;}}","@keyframes f10x8f8u{0%{right:-33%;}100%{right:100%;}}"]}),G2=e=>{const{color:t,max:r,shape:o,thickness:n,value:i}=e,l=J2(),s=Z2();return e.root.className=_e(Vv.root,l.root,l[o],l[n],e.root.className),e.bar&&(e.bar.className=_e(Vv.bar,s.base,s.brand,i===void 0&&s.indeterminate,i!==void 0&&i>Q2&&s.nonZeroDeterminate,t&&i!==void 0&&s[t],e.bar.className)),e.bar&&i!==void 0&&(e.bar.style={width:Math.min(100,Math.max(0,i/r*100))+"%",...e.bar.style}),e},Kv=x.exports.forwardRef((e,t)=>{const r=X2(e,t);return G2(r),_r("useProgressBarStyles_unstable")(r),Y2(r)});Kv.displayName="ProgressBar";const KS="",eS=({lines:e})=>{let t=!1;return x.exports.createElement("div",{className:"ansi-display"},e.map(r=>(t=t||!!r.outputRuns.length,x.exports.createElement("div",{className:"ansi-display-line"},r.outputRuns.length?r.outputRuns.map(o=>x.exports.createElement(tS,{key:o.id,outputRun:o})):t?x.exports.createElement("br",null):null))))},tS=({outputRun:e})=>{let t;(i=>{i[i.Foreground=0]="Foreground",i[i.Background=1]="Background"})(t||(t={}));const r=i=>{let l={};return i&&i.forEach(s=>{switch(s){case Q.exports.ANSIStyle.Bold:l={...l,fontWeight:"bold"};break;case Q.exports.ANSIStyle.Dim:l={...l,fontWeight:"lighter"};break;case Q.exports.ANSIStyle.Italic:l={...l,fontStyle:"italic"};break;case Q.exports.ANSIStyle.Underlined:l={...l,textDecorationLine:"underline",textDecorationStyle:"solid"};break;case Q.exports.ANSIStyle.SlowBlink:l={...l,animation:"ansi-display-run-blink 1s linear infinite"};break;case Q.exports.ANSIStyle.RapidBlink:l={...l,animation:"ansi-display-run-blink 0.5s linear infinite"};break;case Q.exports.ANSIStyle.Hidden:l={...l,visibility:"hidden"};break;case Q.exports.ANSIStyle.CrossedOut:l={...l,textDecorationLine:"line-through",textDecorationStyle:"solid"};break;case Q.exports.ANSIStyle.DoubleUnderlined:l={...l,textDecorationLine:"underline",textDecorationStyle:"double"};break}}),l},o=(i,l)=>{switch(l){case void 0:return{};case Q.exports.ANSIColor.Black:case Q.exports.ANSIColor.Red:case Q.exports.ANSIColor.Green:case Q.exports.ANSIColor.Yellow:case Q.exports.ANSIColor.Blue:case Q.exports.ANSIColor.Magenta:case Q.exports.ANSIColor.Cyan:case Q.exports.ANSIColor.White:case Q.exports.ANSIColor.BrightBlack:case Q.exports.ANSIColor.BrightRed:case Q.exports.ANSIColor.BrightGreen:case Q.exports.ANSIColor.BrightYellow:case Q.exports.ANSIColor.BrightBlue:case Q.exports.ANSIColor.BrightMagenta:case Q.exports.ANSIColor.BrightCyan:case Q.exports.ANSIColor.BrightWhite:return i===0?{color:`var(--${l})`}:{background:`var(--${l})`};default:return i===0?{color:l}:{background:l}}},n=i=>i.format?{...r(i.format.styles),...o(0,i.format.foregroundColor),...o(1,i.format.backgroundColor)}:{};return x.exports.createElement("span",{style:n(e)},e.text)},Xv=2,rS=120,Yv=50,oS=70;function nS(e){const t=iS(),r=Il.colorNeutralForeground3,o=x.exports.useRef(null);x.exports.useEffect(()=>{var l;(l=o.current)==null||l.scrollIntoView()},[e.lines,e.error]);const n=x.exports.useMemo(()=>e.rendering?V.createElement(fc,{style:{color:r},onClick:e.onCancel},"Cancel"):V.createElement(fc,{appearance:"subtle","aria-label":"close",icon:V.createElement(Pb,null),onClick:e.onClose}),[e.rendering]),i=(l,s)=>{s.type==="escapeKeyDown"&&(e.rendering?e.onCancel():e.onClose())};return V.createElement(Mv,{modalType:"non-modal",open:e.open,onOpenChange:i},V.createElement(qv,{className:t.surface,style:{borderTop:e.error&&!e.darkMode?`${Xv}px solid ${Il.colorPaletteDarkOrangeBorder2}`:"none"}},V.createElement(jv,{style:{gridTemplateRows:e.error?"auto auto auto":`auto ${rS}px auto`}},V.createElement(Wv,{action:n,style:{color:r}},e.error?"Error":"Render"),V.createElement(Uv,{className:t.content,style:{maxHeight:e.error?`calc(100vh - ${Yv+oS}px)`:"none"}},V.createElement(eS,{lines:e.lines}),V.createElement("div",{ref:o})))))}const iS=Tp({surface:{boxShadow:Il.shadow4,...Fp.borderRadius(0),position:"fixed",top:Xv+"px",left:0,right:0,marginTop:"1px",marginBottom:"0px",paddingTop:"12px",paddingBottom:"16px",maxWidth:"none",maxHeight:`calc(100% - ${Yv}px)`},content:{...Fp.border("1px","solid",Il.colorNeutralStroke1),overflowY:"scroll"}}),lS=Tp({root:{position:"fixed",top:0,left:0,right:0}}),sS=e=>{const t=lS();return V.createElement(V.Fragment,null,e.visible?V.createElement(Kv,{className:t.root}):null)},aS=e=>{const[t,r]=x.exports.useState(!1);return x.exports.useEffect(()=>{e.rendering&&r(!1)},[e.rendering]),V.createElement(Yu,{theme:e.darkMode?Wh:Hh},V.createElement(sS,{visible:e.rendering&&!t}),V.createElement(nS,{open:e.dialog&&!t,rendering:e.rendering,error:e.error,lines:e.lines,onClose:()=>r(!0),onCancel:()=>{r(!0),e.onCancel()},darkMode:e.darkMode}))};function uS(e){return t=>{const r={rendering:!1,dialog:!1,error:!1,output:new Q.exports.ANSIOutput,lines:new Array},o=document.createElement("div");document.body.appendChild(o);const n=su(o),i=()=>{n.render(V.createElement(aS,{rendering:r.rendering,dialog:r.dialog,error:r.error,lines:r.lines,onCancel:t,darkMode:e}))},l=d=>{r.rendering=!0,r.error=!1,r.output=new Q.exports.ANSIOutput,r.lines=[];const f=2e3;d===void 0||d>f?r.dialog=!0:setTimeout(()=>{r.rendering&&(r.dialog=!0,i())},f),i()},s=d=>{r.rendering=!1,r.dialog=!d,r.error=!d,i(),d||window.parent.postMessage&&window.parent.postMessage({type:"error"},"*")},a=d=>{d=typeof d=="string"?d:d.msgFormatted,r.output.processOutput(d),r.lines=[...r.output.outputLines],i()},u=document.getElementById("quarto-render-error");return u?(l(),a(u.innerHTML.trim()),s(!1)):i(),d=>{if(d.data.startsWith("render:")){const[f,g,y]=d.data.split(":");return g==="start"?l(parseInt(y)):g==="stop"&&s(y==="true"),!0}else if(d.data.startsWith("log:")){const f=JSON.parse(d.data.substr(4));return a(f),!0}else return!1}}}function cS(e,t){if(window.self===window.top)return;function r(i){return i.startsWith(e)}function o(i){t.includes("quartoPreviewReqId=")&&window.parent.postMessage&&i.addEventListener("click",function(a){return window.parent.postMessage({type:"openExternal",url:i.href},"*"),a.preventDefault(),!1}),t.includes("capabilities=")&&(i.target="_blank")}const n=document.getElementsByTagName("a");for(let i=0;i<n.length;i++){const l=n[i];l.href&&!r(l.href)&&o(l)}}function dS(e){const t=document.body.querySelectorAll('a[data-meca-link="true"]');for(let r=0;r<t.length;r++)t[r].addEventListener("click",n=>{const i=globalThis.document.createElement("div"),l=su(i);return l.render(V.createElement(fS,{darkMode:e,linkEl:t[r],onClosed:()=>{l.unmount(),i.remove()}})),n.preventDefault(),!1})}const fS=e=>{const[t,r]=x.exports.useState(!0),o=(n,i)=>r(i.open||!1);return V.createElement(Yu,{theme:e.darkMode?Wh:Hh},V.createElement(Fv,{open:t,withArrow:!0,closeOnScroll:!0,onOpenChange:o,positioning:{target:e.linkEl,overflowBoundaryPadding:30}},V.createElement(Nv,null,V.createElement(Ov,null,V.createElement("strong",null,"Preview Not Available"),V.createElement("p",{style:{marginBottom:0}},"The MECA file is not available during preview.",V.createElement("br",null),"It will be included with the final rendered output.")))))};function gS(e){function t(a,u){window.addEventListener("message",function(d){d.data.message===a&&u(d.data.data)})}function r(a,u){window.parent.postMessage&&window.parent.postMessage({type:a,message:a,data:u},"*")}function o(a){const u=a.querySelectorAll("h1, h2, h3, h4, h5, h6"),d=Reveal.getIndices(a);return{id:a.id||"",title:u.item(0)&&u.item(0).textContent||"",h:d.h,v:d.v,f:d.f}}function n(a){r("reveal-init",{slides:a})}function i(){r("reveal-slidechange",{first:Reveal.isFirstSlide(),last:Reveal.isLastSlide(),slide:o(Reveal.getCurrentSlide())})}function l(){r("reveal-hashchange",{href:window.location.href})}const s=()=>{Reveal.configure({history:!0});const a=Reveal.getSlides().map(o);n(a),t("reveal-ready",function(){i(),l(),window.addEventListener("hashchange",l)}),/print-pdf/gi.test(window.location.search)&&(e(),setTimeout(function(){window.print()},1e3))};Reveal.isReady()?s():Reveal.on("ready",s),Reveal.on("slidechanged",i),t("reveal-next",function(){Reveal.next()}),t("reveal-prev",function(){Reveal.prev()}),t("reveal-slide",a=>{Reveal.slide(a.h,a.v,a.f)}),t("reveal-refresh",function(){window.location.reload()}),t("reveal-home",function(){Reveal.slide(0,0,0)}),t("reveal-fullscreen",function(){Reveal.triggerKey(70)})}function pS(e){window.parent.postMessage&&(window.addEventListener("message",function(t){t.data.type==="devhost-init"?window.quartoDevhost={openInputFile:function(r,o,n){if(e===null){console.warn("Missing inputFile when atempting to open input file.");return}window.parent.postMessage({type:"openfile",file:e,line:r,column:o,highlight:n},t.origin)}}:t.data.type==="goback"?window.history.back():t.data.type==="goforward"&&window.history.forward()},!0),e===null?console.warn("Missing inputFile when atempting to post message."):window.parent.postMessage({type:"navigate",href:window.location.href,file:e},"*"))}function hS(){window.parent.postMessage&&window.document.addEventListener("keydown",e=>{const t={type:"keydown",data:{altKey:e.altKey,code:e.code,ctrlKey:e.ctrlKey,isComposing:e.isComposing,key:e.key,location:e.location,metaKey:e.metaKey,repeat:e.repeat,shiftKey:e.shiftKey}};window.parent.postMessage(t,"*")}),window.addEventListener("message",function(e){e.data.type==="devhost-exec-command"&&window.document.execCommand(e.data.data)},!0)}const XS="";function vS(e){try{const t=mS(),r=Pr([uS(t),Zv()]);hS(),e.origin&&e.search&&cS(e.origin,e.search),dS(t),e.isPresentation?gS(r):pS(e.inputFile)}catch(t){console.error(t)}}function mS(){return document.body.classList.contains("quarto-dark")?!0:new URLSearchParams(window.location.search).get("quartoPreviewThemeCategory")==="dark"}ot.init=vS,Object.defineProperties(ot,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
