<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE language SYSTEM "language.dtd">
<!-- Kate 2.5 (KDE 3.5) highlighting module for R
	based on an earlier version by <PERSON><PERSON><PERSON><PERSON>. Code folding code by <PERSON>
	version 2.0: (c) 2006 <PERSON>, <PERSON><PERSON>, and the RKWard Team
	license: GPL v2
	Kate   : http://kate.kde.org/
	R      : http://www.r-project.org/
	RKWard : http://rkward.kde.org/
	-->
<language version="12" kateversion="5.0" name="R Script" section="Scientific" extensions="*.R;*.r;*.S;*.s;*.q" mimetype="" license="GPLv2">
<highlighting>

	<list name="controls">
		<item>for</item>
		<item>in</item>
		<item>next</item>
		<item>break</item>
		<item>while</item>
		<item>repeat</item>
		<item>if</item>
		<item>else</item>
		<item>switch</item>
		<item>function</item>
	</list>
	<list name="words">
		<item>TRUE</item>
		<item>FALSE</item>
		<item>NULL</item>
		<item>NA</item>
		<item>NA_integer_</item>
		<item>NA_real_</item>
		<item>NA_complex_</item>
		<item>NA_character_</item>
		<item>Inf</item>
		<item>NaN</item>
	</list>

	<contexts>
		<!-- This context is really only good for detecting unexpected closing braces '}'. Since opening braces go to ctx0 (and nesting in there), this context is only active on the base level -->
		<context attribute="Normal Text" lineEndContext="#stay" name="level0">
			<IncludeRules context="CommonRules"/>

			<AnyChar attribute="Error" context="#stay" String="})"/>
		</context>

		<context attribute="Normal Text" lineEndContext="#stay" name="ctx0">
			<IncludeRules context="CommonRules"/>

			<DetectChar attribute="Symbol" context="#pop" char="}" endRegion="Brace1" />
			<DetectChar attribute="Error" context="#stay" char=")"/>
		</context>

		<context attribute="Normal Text" lineEndContext="#stay" name="parenthesis">
			<LineContinue attribute="Operator" context="#stay"/>
			<DetectChar attribute="Symbol" context="#pop" char=")"/>

			<RegExpr attribute="Identifier" context="#stay" String="[a-zA-Z_\.][0-9a-zA-Z_\.]*[\s]*[:]?=(?=[^=]|$)"/>

			<IncludeRules context="CommonRules"/>
			<DetectChar attribute="Error" context="#stay" char="}" />
		</context>

		<context attribute="String" lineEndContext="#stay" name="string">
			<DetectChar attribute="String" context="#pop" char="&quot;"/>
			<HlCStringChar attribute="String Char" context="#stay"/>
		</context>

		<context attribute="String" lineEndContext="#stay" name="string2">
			<DetectChar attribute="String" context="#pop" char="'"/>
			<HlCStringChar attribute="String Char" context="#stay"/>
		</context>

		<context attribute="Identifier" lineEndContext="#stay" name="backquotedsymbol">
			<DetectChar attribute="String" context="#pop" char="`"/>
			<HlCStringChar attribute="String Char" context="#stay"/>
		</context>

		<context attribute="Normal Text" lineEndContext="#stay" name="operator_rhs" fallthrough="true" fallthroughContext="#pop">
			<!-- While there is nothing of interest, stay in the context -->
			<DetectSpaces />
			<IncludeRules context="FindComments"/>
			<!-- Operators other than +, -, and ! directly after another operator are an error. -->
			<Detect2Chars attribute="Error" context="#stay" char="!" char1="="/>
			<AnyChar attribute="Error" context="#stay" String="*/&lt;&gt;=|&amp;:^@$~"/>
		</context>

		<context attribute="Normal Text" lineEndContext="#stay" name="FindComments">
			<Detect2Chars attribute="Headline" context="Headline" char="#" char1="#"/>
			<DetectChar attribute="Comment" context="Comment" char="#"/>
		</context>
		<context attribute="Headline" lineEndContext="#pop" name="Headline">
			<DetectSpaces />
			<IncludeRules context="##Comments" />
		</context>
		<context attribute="Comment" lineEndContext="#pop" name="Comment">
			<DetectSpaces />
			<IncludeRules context="##Comments" />
		</context>

		<!-- This context is not really used, but contains the common rules -->
		<context name="CommonRules" lineEndContext="#stay" attribute="Normal Text" >
			<DetectSpaces />
			<IncludeRules context="FindComments"/>
			<DetectChar attribute="String" context="string" char="&quot;"/>
			<DetectChar attribute="String" context="string2" char="'"/>
			<DetectChar attribute="String" context="backquotedsymbol" char="`"/>
			<keyword attribute="Control Structure" context="#stay" String="controls"/>
			<keyword attribute="Reserved Words" context="#stay" String="words"/>
			<Float attribute="Float" context="#stay"/>
			<Int attribute="Int" context="#stay"/>
			<RegExpr attribute="Keyword" context="#stay" String="[a-zA-Z_]+[a-zA-Z_\.0-9]*(?=[\s]*[(])|\.[a-zA-Z_\.]+[a-zA-Z_\.0-9]*(?=[\s]*[(])"/>
			<DetectChar attribute="Symbol" context="parenthesis" char="("/>

			<!-- For (assignment) operators, enter a new context operator_rhs to check what follows (generally, that should not be another op) -->
			<StringDetect attribute="Assign" context="operator_rhs" String="&lt;&lt;-"/>
			<Detect2Chars attribute="Assign" context="operator_rhs" char="&lt;" char1="-"/>
			<StringDetect attribute="Assign" context="operator_rhs" String="-&gt;&gt;"/>
			<Detect2Chars attribute="Assign" context="operator_rhs" char="-" char1="&gt;"/>
			<RegExpr attribute="Assign" context="operator_rhs" String="=(?!(=|&gt;))"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="*" char1="*"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="&lt;" char1="="/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="&gt;" char1="="/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="=" char1="="/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="=" char1="&gt;"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="!" char1="="/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="|" char1="&gt;"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="|" char1="|"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char="&amp;" char1="&amp;"/>
			<StringDetect attribute="Operator" context="operator_rhs" String=":::"/>
			<Detect2Chars attribute="Operator" context="operator_rhs" char=":" char1=":"/>
			<AnyChar attribute="Operator" context="operator_rhs" String="+-*/&lt;&gt;=!|&amp;:^@$~"/>
			<RangeDetect attribute="Operator" context="operator_rhs" char="%" char1="%"/>

			<DetectChar attribute="Symbol" context="ctx0" char="{" beginRegion="Brace1" />

			<!-- This is needed only to assist variable based indentation -->
			<AnyChar attribute="Symbol" context="#stay" String="[]" />
		</context>
	</contexts>

	<itemDatas>
		<itemData name="Normal Text" defStyleNum="dsNormal" spellChecking="false"/>
		<itemData name="Symbol" defStyleNum="dsNormal" spellChecking="false"/>
		<itemData name="Keyword" defStyleNum="dsFunction" spellChecking="false"/>
		<itemData name="Identifier" defStyleNum="dsAttribute" spellChecking="false"/>
		<itemData name="String" defStyleNum="dsString"/>
		<itemData name="Headline" defStyleNum="dsDocumentation" bold="1"/>
		<itemData name="Comment" defStyleNum="dsComment"/>
		<itemData name="Assign" defStyleNum="dsOthers" bold="1" italic="0" spellChecking="false"/>
		<itemData name="Control Structure" defStyleNum="dsControlFlow" spellChecking="false"/>
		<itemData name="Reserved Words" defStyleNum="dsConstant" spellChecking="false"/>
		<itemData name="Error" defStyleNum="dsError" spellChecking="false"/>
		<itemData name="Operator" defStyleNum="dsSpecialChar" spellChecking="false"/>
		<itemData name="String Char"  defStyleNum="dsSpecialChar" spellChecking="false"/>
		<itemData name="Float" defStyleNum="dsFloat" spellChecking="false"/>
		<itemData name="Int" defStyleNum="dsDecVal" spellChecking="false"/>
	</itemDatas>
</highlighting>

<general>
	<comments>
		<comment name="singleLine" start="#"/>
	</comments>
	<keywords casesensitive="true" weakDeliminator="." additionalDeliminator="$"/>
</general>
</language>
<!-- kate: replace-tabs off; -->
