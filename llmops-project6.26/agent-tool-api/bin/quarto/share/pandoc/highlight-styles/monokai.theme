{"custom-styles": {"C": {"Prep. Lib": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}}, "C++": {"Qt Classes": {"bold": false, "selected-text-color": "#66d9ef", "text-color": "#66d9ef"}, "Qt Macros": {"bold": false, "selected-text-color": "#f92672", "text-color": "#f92672"}, "Qt Types": {"bold": false, "selected-text-color": "#66D9EF", "text-color": "#66D9EF"}}, "CMake": {"Builtin Variable": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "False Special Arg": {"selected-text-color": "#e03232", "text-color": "#e03232"}, "True Special Arg": {"selected-text-color": "#38bd38", "text-color": "#38bd38"}}, "Doxygen": {"Tags": {"bold": false, "selected-text-color": "#52afbf", "text-color": "#52afbf"}, "Word": {"bold": false, "selected-text-color": "#7ba822", "text-color": "#7ba822", "underline": true}}, "Diff": {"Added line": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Changed line (new)": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Changed line (old)": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "Removed line": {"selected-text-color": "#f92672", "text-color": "#f92672"}}, "Go": {"Builtin Function": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Predeclared Identifier": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}}, "ISO C++": {"Attribute": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Boost Stuff": {"bold": false}, "Prep. Lib": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}, "Standard Attribute": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Standard Macros": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "Standard Suffix": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "UDL Numeric Suffix": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "UDL String Suffix": {"selected-text-color": "#f92672", "text-color": "#f92672"}}, "JSON": {"Style_String_Key": {"italic": false, "selected-text-color": "#f92672", "text-color": "#f92672"}}, "JavaScript": {"Object Member": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Substitution": {"selected-text-color": "#f92672", "text-color": "#f92672"}}, "JavaScript React (JSX)": {"Component Tag": {"bold": false, "selected-text-color": "#66d9ef", "text-color": "#66d9ef"}}, "Makefile": {"FuncParam": {"selected-text-color": "#fd971f", "text-color": "#fd971f"}, "Target": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Variable": {"italic": false, "selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}}, "Markdown": {"Emphasis Text": {"selected-text-color": "#66D9EF", "text-color": "#66D9EF"}, "Reference-Link ID": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "Reference-Link Name": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "Reference-Link Target": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "Strong Text": {"selected-text-color": "#66d9ef", "text-color": "#66d9ef"}}, "Python": {"Import": {"selected-text-color": "#f92672", "text-color": "#f92672"}}, "Rust": {"Definition": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Lifetime": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "Macro": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Self": {"selected-text-color": "#fd971f", "text-color": "#fd971f"}}, "TypeScript": {"Function (Built-in)": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Object Member": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Substitution": {"selected-text-color": "#f92672", "text-color": "#f92672"}}, "TypeScript React (TSX)": {"Component Tag": {"bold": false, "selected-text-color": "#66d9ef", "text-color": "#66d9ef"}, "Substitution": {"selected-text-color": "#f92672", "text-color": "#f92672"}}}, "background-color": "#2e3440", "editor-colors": {"BackgroundColor": "#272822", "BracketMatching": "#5b5a4a", "CodeFolding": "#3a3b32", "CurrentLine": "#3e3d32", "CurrentLineNumber": "#d1d931", "IconBorder": "#272822", "IndentationLine": "#6272a4", "LineNumbers": "#909194", "MarkBookmark": "#66D9EF", "MarkBreakpointActive": "#ff5555", "MarkBreakpointDisabled": "#bd93f9", "MarkBreakpointReached": "#f1fa8c", "MarkError": "#ff5555", "MarkExecution": "#44475a", "MarkWarning": "#ffb86c", "ModifiedLines": "#ff473d", "ReplaceHighlight": "#735d16", "SavedLines": "#20e852", "SearchHighlight": "#245676", "Separator": "#45474e", "SpellChecking": "#ff5555", "TabMarker": "#6272a4", "TemplateBackground": "#23241e", "TemplateFocusedPlaceholder": "#22231d", "TemplatePlaceholder": "#22231d", "TemplateReadOnlyPlaceholder": "#262721", "TextSelection": "#3f413e", "WordWrapMarker": "#282a36"}, "metadata": {"copyright": ["SPDX-FileCopyrightText: 2006 Wimer <PERSON>berg", "SPDX-FileCopyrightText: 2020 <PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SPDX-License-Identifier: MIT", "name": "Monokai", "revision": 5}, "text-styles": {"Alert": {"bold": true, "selected-text-color": "#ff5555", "text-color": "#ff5555"}, "Annotation": {"selected-text-color": "#75715e", "text-color": "#75715e"}, "Attribute": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "BaseN": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "BuiltIn": {"selected-text-color": "#66D9EF", "text-color": "#66D9EF"}, "Char": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}, "Comment": {"selected-text-color": "#75715e", "text-color": "#75715e"}, "CommentVar": {"selected-text-color": "#75715e", "text-color": "#75715e"}, "Constant": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "ControlFlow": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "DataType": {"italic": true, "selected-text-color": "#66d9ef", "text-color": "#66d9ef"}, "DecVal": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "Documentation": {"selected-text-color": "#75715e", "text-color": "#75715e"}, "Error": {"selected-text-color": "#ff5555", "text-color": "#ff5555", "underline": true}, "Extension": {"bold": true, "selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Float": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "Function": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Import": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "Information": {"selected-text-color": "#f1fa8c", "text-color": "#f1fa8c"}, "Keyword": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "Normal": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Operator": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "Others": {"selected-text-color": "#a6e22e", "text-color": "#a6e22e"}, "Preprocessor": {"selected-text-color": "#f92672", "text-color": "#f92672"}, "RegionMarker": {"selected-text-color": "#75715e", "text-color": "#75715e"}, "SpecialChar": {"selected-text-color": "#ae81ff", "text-color": "#ae81ff"}, "SpecialString": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}, "String": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}, "Variable": {"selected-text-color": "#f8f8f2", "text-color": "#f8f8f2"}, "VerbatimString": {"selected-text-color": "#e6db74", "text-color": "#e6db74"}, "Warning": {"selected-text-color": "#ff5555", "text-color": "#ff5555"}}}