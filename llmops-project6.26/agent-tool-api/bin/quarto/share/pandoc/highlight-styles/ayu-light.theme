{"_comments": ["Last update: Sep 21, 2020 (revision 2)", "This file has been converted from: https://github.com/dempfi/ayu", "Also see: https://github.com/ayu-theme"], "metadata": {"copyright": ["SPDX-FileCopyrightText: 2016 <PERSON><PERSON>", "SPDX-FileCopyrightText: 2020 <PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SPDX-License-Identifier: MIT", "name": "ayu Light", "revision": 2}, "editor-colors": {"BackgroundColor": "#fafafa", "BracketMatching": "#d9dbdd", "CodeFolding": "#ffe6dc", "CurrentLine": "#eff0f2", "CurrentLineNumber": "#767676", "IconBorder": "#f3f3f3", "IndentationLine": "#dcdee1", "LineNumbers": "#9f9f9f", "MarkBookmark": "#399ee6", "MarkBreakpointActive": "#f07171", "MarkBreakpointDisabled": "#a37acc", "MarkBreakpointReached": "#e6ba7e", "MarkError": "#f51818", "MarkExecution": "#4cbf99", "MarkWarning": "#86b300", "ModifiedLines": "#709ecc", "ReplaceHighlight": "#b0d4e4", "SavedLines": "#99bf4d", "SearchHighlight": "#fdd1bc", "Separator": "#e0dfdf", "SpellChecking": "#f27983", "TabMarker": "#dcdee1", "TemplateBackground": "#f7f7f7", "TemplateFocusedPlaceholder": "#bec1c6", "TemplatePlaceholder": "#dddfe1", "TemplateReadOnlyPlaceholder": "#ffffff", "TextSelection": "#d1e4f4", "WordWrapMarker": "#e9eaeb"}, "text-styles": {"Alert": {"background-color": "#faefef", "bold": true, "selected-text-color": "#f51818", "text-color": "#f51818"}, "Annotation": {"selected-text-color": "#e6ba7e", "text-color": "#e6ba7e"}, "Attribute": {"selected-text-color": "#399ee6", "text-color": "#399ee6"}, "BaseN": {"selected-text-color": "#ff9940", "text-color": "#ff9940"}, "BuiltIn": {"selected-text-color": "#4cbf99", "text-color": "#4cbf99"}, "Char": {"selected-text-color": "#4cbf99", "text-color": "#4cbf99"}, "Comment": {"italic": true, "selected-text-color": "#607880", "text-color": "#607880"}, "CommentVar": {"selected-text-color": "#a37acc", "text-color": "#a37acc"}, "Constant": {"selected-text-color": "#a37acc", "text-color": "#a37acc"}, "ControlFlow": {"bold": true, "selected-text-color": "#fa8d3e", "text-color": "#fa8d3e"}, "DataType": {"selected-text-color": "#fa8d3e", "text-color": "#fa8d3e"}, "DecVal": {"selected-text-color": "#ff9940", "text-color": "#ff9940"}, "Documentation": {"selected-text-color": "#607880", "text-color": "#607880"}, "Error": {"selected-text-color": "#f51818", "text-color": "#f51818", "underline": true}, "Extension": {"bold": true, "selected-text-color": "#399ee6", "text-color": "#399ee6"}, "Float": {"selected-text-color": "#ff9940", "text-color": "#ff9940"}, "Function": {"selected-text-color": "#f2ae49", "text-color": "#f2ae49"}, "Import": {"selected-text-color": "#86b300", "text-color": "#86b300"}, "Information": {"selected-text-color": "#ff9940", "text-color": "#ff9940"}, "Keyword": {"bold": true, "selected-text-color": "#fa8d3e", "text-color": "#fa8d3e"}, "Normal": {"selected-text-color": "#575f66", "text-color": "#575f66"}, "Operator": {"selected-text-color": "#ed9366", "text-color": "#ed9366"}, "Others": {"selected-text-color": "#55b4d4", "text-color": "#55b4d4"}, "Preprocessor": {"selected-text-color": "#f07171", "text-color": "#f07171"}, "RegionMarker": {"background-color": "#ddecf3", "selected-text-color": "#399ee6", "text-color": "#399ee6"}, "SpecialChar": {"selected-text-color": "#4cbf99", "text-color": "#4cbf99"}, "SpecialString": {"selected-text-color": "#4cbf99", "text-color": "#4cbf99"}, "String": {"selected-text-color": "#86b300", "text-color": "#86b300"}, "Variable": {"selected-text-color": "#55b4d4", "text-color": "#55b4d4"}, "VerbatimString": {"selected-text-color": "#86b300", "text-color": "#86b300"}, "Warning": {"selected-text-color": "#f07171", "text-color": "#f07171"}}}