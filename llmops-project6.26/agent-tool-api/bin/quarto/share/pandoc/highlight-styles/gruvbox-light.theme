{"_comments": ["Last update: Sep 17, 2020 (revision 2)", "This file has been converted from: https://github.com/morhetz/gruvbox"], "metadata": {"copyright": ["SPDX-FileCopyrightText: 2017 <PERSON> <<EMAIL>>", "SPDX-FileCopyrightText: 2020 <PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SPDX-License-Identifier: MIT", "name": "gruvbox Light", "revision": 2}, "text-styles": {"Normal": {"text-color": "#3c3836", "selected-text-color": "#3c3836", "bold": false, "italic": false, "underline": false, "strike-through": false}, "Keyword": {"text-color": "#3c3836", "selected-text-color": "#3c3836", "bold": true}, "Function": {"text-color": "#689d6a", "selected-text-color": "#427b58"}, "Variable": {"text-color": "#458588", "selected-text-color": "#076678"}, "ControlFlow": {"text-color": "#cc241d", "selected-text-color": "#9d0006", "bold": true}, "Operator": {"text-color": "#3c3836", "selected-text-color": "#3c3836"}, "BuiltIn": {"text-color": "#d65d0e", "selected-text-color": "#af3a03"}, "Extension": {"text-color": "#689d6a", "selected-text-color": "#427b58", "bold": true}, "Preprocessor": {"text-color": "#d65d0e", "selected-text-color": "#af3a03"}, "Attribute": {"text-color": "#d79921", "selected-text-color": "#b57614"}, "Char": {"text-color": "#b16286", "selected-text-color": "#8f3f71"}, "SpecialChar": {"text-color": "#b16286", "selected-text-color": "#8f3f71"}, "String": {"text-color": "#98971a", "selected-text-color": "#79740e"}, "VerbatimString": {"text-color": "#98971a", "selected-text-color": "#79740e"}, "SpecialString": {"text-color": "#98971a", "selected-text-color": "#79740e"}, "Import": {"text-color": "#689d6a", "selected-text-color": "#427b58"}, "DataType": {"text-color": "#d79921", "selected-text-color": "#b57614"}, "DecVal": {"text-color": "#f67400", "selected-text-color": "#f67400"}, "BaseN": {"text-color": "#f67400", "selected-text-color": "#f67400"}, "Float": {"text-color": "#f67400", "selected-text-color": "#f67400"}, "Constant": {"text-color": "#b16286", "selected-text-color": "#8f3f71", "bold": true}, "Comment": {"text-color": "#928374", "selected-text-color": "#a89984"}, "Documentation": {"text-color": "#98971a", "selected-text-color": "#79740e"}, "Annotation": {"text-color": "#98971a", "selected-text-color": "#79740e"}, "CommentVar": {"text-color": "#928374", "selected-text-color": "#a89984"}, "RegionMarker": {"text-color": "#928374", "selected-text-color": "#a89984", "background-color": "#f9f5d7"}, "Information": {"text-color": "#282828", "selected-text-color": "#282828", "background-color": "#83a598"}, "Warning": {"text-color": "#282828", "selected-text-color": "#282828", "background-color": "#fabd2f"}, "Alert": {"text-color": "#282828", "selected-text-color": "#282828", "background-color": "#cc241d", "bold": true}, "Error": {"text-color": "#cc241d", "selected-text-color": "#9d0006", "underline": true}, "Others": {"text-color": "#689d6a", "selected-text-color": "#427b58"}}, "editor-colors": {"BackgroundColor": "#fbf1c7", "CodeFolding": "#f9f5d7", "BracketMatching": "#a89984", "CurrentLine": "#f2e5bc", "IconBorder": "#fbf1c7", "IndentationLine": "#d5c4a1", "LineNumbers": "#3c3836", "CurrentLineNumber": "#3c3836", "MarkBookmark": "#458588", "MarkBreakpointActive": "#cc241d", "MarkBreakpointReached": "#98971a", "MarkBreakpointDisabled": "#b16286", "MarkExecution": "#3c3836", "MarkWarning": "#d65d0e", "MarkError": "#cc241d", "ModifiedLines": "#af3a03", "ReplaceHighlight": "#79740e", "SavedLines": "#689d6a", "SearchHighlight": "#427b58", "TextSelection": "#d5c4a1", "Separator": "#d5c4a1", "SpellChecking": "#cc241d", "TabMarker": "#d5c4a1", "TemplateBackground": "#fbf1c7", "TemplatePlaceholder": "#98971a", "TemplateFocusedPlaceholder": "#79740e", "TemplateReadOnlyPlaceholder": "#9d0006", "WordWrapMarker": "#a89984"}}