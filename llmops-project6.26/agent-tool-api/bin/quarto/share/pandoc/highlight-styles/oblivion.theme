{"_comments": ["This theme has been adapted from the GtkSourceView Oblivion theme"], "metadata": {"copyright": ["SPDX-FileCopyrightText: 2007 <PERSON> <<EMAIL>>, GtkSourceView team", "SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>"], "name": "Oblivion", "revision": 2, "license": "SPDX-License-Identifier: MIT"}, "background-color": "#201f1f", "text-styles": {"Alert": {"background-color": "#451e1a", "bold": true, "selected-text-color": "#e85848", "text-color": "#e85848"}, "Annotation": {"selected-text-color": "#ad7fa8", "text-color": "#ad7fa8"}, "Attribute": {"selected-text-color": "#ad7fa8", "text-color": "#ad7fa8"}, "BaseN": {"selected-text-color": "#fce94f", "text-color": "#edd400"}, "BuiltIn": {"selected-text-color": "#729fcf", "text-color": "#729fcf"}, "Char": {"selected-text-color": "#fcaf3e", "text-color": "#ce5c00"}, "Comment": {"selected-text-color": "#8ae234", "text-color": "#30a100"}, "CommentVar": {"selected-text-color": "#ad7fa8", "text-color": "#ad7fa8"}, "Constant": {"bold": true, "selected-text-color": "#ffffff", "text-color": "#edd400"}, "ControlFlow": {"bold": true, "selected-text-color": "#ffffff", "text-color": "#ffffff"}, "DataType": {"selected-text-color": "#508ed8", "text-color": "#508ed8"}, "DecVal": {"selected-text-color": "#fce94f", "text-color": "#edd400"}, "Documentation": {"selected-text-color": "#8ae234", "text-color": "#4e9a06"}, "Error": {"selected-text-color": "#e85848", "text-color": "#e85848", "underline": true}, "Extension": {"bold": true, "selected-text-color": "#508ed8", "text-color": "#508ed8"}, "Float": {"selected-text-color": "#fcaf3e", "text-color": "#ce5c00"}, "Function": {"bold": true, "selected-text-color": "#729fcf", "text-color": "#729fcf"}, "Import": {"selected-text-color": "#ad7fa8", "text-color": "#ad7fa8"}, "Information": {"selected-text-color": "#c0a25f", "text-color": "#c0a25f"}, "Keyword": {"bold": true, "selected-text-color": "#ffffff", "text-color": "#ffffff"}, "Normal": {"selected-text-color": "#eeeeec", "text-color": "#d3d7c1"}, "Operator": {"selected-text-color": "#eeeeec", "text-color": "#eeeeec"}, "Others": {"selected-text-color": "#fce94f", "text-color": "#edd400"}, "Preprocessor": {"selected-text-color": "#ad7fa8", "text-color": "#ad7fa8"}, "RegionMarker": {"background-color": "#1c2c3f", "selected-text-color": "#508ed8", "text-color": "#508ed8"}, "SpecialChar": {"selected-text-color": "#fcaf3e", "text-color": "#ce5c00"}, "SpecialString": {"selected-text-color": "#fce94f", "text-color": "#fce94f"}, "String": {"selected-text-color": "#fce94f", "text-color": "#edd400"}, "Variable": {"selected-text-color": "#ce5c00", "text-color": "#ce5c00"}, "VerbatimString": {"selected-text-color": "#fce94f", "text-color": "#c4a000"}, "Warning": {"selected-text-color": "#e85848", "text-color": "#e85848"}}, "editor-colors": {"BackgroundColor": "#201f1f", "BracketMatching": "#8f5902", "CodeFolding": "#19395f", "CurrentLine": "#2e3436", "CurrentLineNumber": "#ffffff", "IconBorder": "#302f2f", "IndentationLine": "#989595", "LineNumbers": "#e0dedb", "MarkBookmark": "#0000cc", "MarkBreakpointActive": "#cc0000", "MarkBreakpointDisabled": "#cc00cc", "MarkBreakpointReached": "#00cc00", "MarkError": "#cc0000", "MarkExecution": "#888a85", "MarkWarning": "#ad7fa8", "ModifiedLines": "#451e1a", "ReplaceHighlight": "#356703", "SavedLines": "#23321a", "SearchHighlight": "#4e9a06", "Separator": "#787775", "SpellChecking": "#e85848", "TabMarker": "#555753", "TemplateBackground": "#302f2f", "TemplateFocusedPlaceholder": "#23321a", "TemplatePlaceholder": "#23321a", "TemplateReadOnlyPlaceholder": "#451e1a", "TextSelection": "#184880", "WordWrapMarker": "#3c3a3a"}}