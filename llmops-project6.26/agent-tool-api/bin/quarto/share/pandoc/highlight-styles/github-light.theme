{"custom-styles": {"INI Files": {"Section": {"selected-text-color": "#6f42c1", "text-color": "#6f42c1"}}, "Python": {"Builtin Function": {"selected-text-color": "#6f42c1", "text-color": "#6f42c1"}, "Import": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "Special Variable": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}}, "Rust": {"Attribute": {"selected-text-color": "#24292e", "text-color": "#24292e"}, "Macro": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "Self": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "Trait": {"selected-text-color": "#24292e", "text-color": "#24292e"}}, "XML": {"Element": {"selected-text-color": "#22863a", "text-color": "#22863a"}}}, "editor-colors": {"BackgroundColor": "#ffffff", "BracketMatching": "#bef5cb", "CodeFolding": "#f6f8fa", "CurrentLine": "#f6f8fa", "CurrentLineNumber": "#24292e", "IconBorder": "#ffffff", "IndentationLine": "#d7dbe0", "LineNumbers": "#c7c2bc", "MarkBookmark": "#8be9fd", "MarkBreakpointActive": "#ff5555", "MarkBreakpointDisabled": "#bd93f9", "MarkBreakpointReached": "#f1fa8c", "MarkError": "#b31d28", "MarkExecution": "#44475a", "MarkWarning": "#e36209", "ModifiedLines": "#d73a49", "ReplaceHighlight": "#50fa7b", "SavedLines": "#28a745", "SearchHighlight": "#ffea7f", "Separator": "#e1e4e8", "SpellChecking": "#ff5555", "TabMarker": "#d1d5da", "TemplateBackground": "#23241e", "TemplateFocusedPlaceholder": "#22231d", "TemplatePlaceholder": "#22231d", "TemplateReadOnlyPlaceholder": "#262721", "TextSelection": "#dee6fc", "WordWrapMarker": "#e1e4e8"}, "metadata": {"copyright": ["SPDX-FileCopyrightText: 2020 GitHub Inc.", "SPDX-FileCopyrightText: 2020 <PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SPDX-License-Identifier: MIT", "name": "GitHub Light", "revision": 2}, "text-styles": {"Alert": {"bold": true, "selected-text-color": "#ff5555", "text-color": "#ff5555"}, "Annotation": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "Attribute": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "BaseN": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "BuiltIn": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "Char": {"selected-text-color": "#032f62", "text-color": "#032f62"}, "Comment": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "CommentVar": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "Constant": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "ControlFlow": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "DataType": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "DecVal": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "Documentation": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "Error": {"selected-text-color": "#ff5555", "text-color": "#ff5555", "underline": true}, "Extension": {"bold": true, "selected-text-color": "#d73a49", "text-color": "#d73a49"}, "Float": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "Function": {"selected-text-color": "#6f42c1", "text-color": "#6f42c1"}, "Import": {"selected-text-color": "#032f62", "text-color": "#032f62"}, "Information": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "Keyword": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "Normal": {"selected-text-color": "#24292e", "text-color": "#24292e"}, "Operator": {"selected-text-color": "#24292e", "text-color": "#24292e"}, "Others": {"selected-text-color": "#6f42c1", "text-color": "#6f42c1"}, "Preprocessor": {"selected-text-color": "#d73a49", "text-color": "#d73a49"}, "RegionMarker": {"selected-text-color": "#6a737d", "text-color": "#6a737d"}, "SpecialChar": {"selected-text-color": "#005cc5", "text-color": "#005cc5"}, "SpecialString": {"selected-text-color": "#032f62", "text-color": "#032f62"}, "String": {"selected-text-color": "#032f62", "text-color": "#032f62"}, "Variable": {"selected-text-color": "#e36209", "text-color": "#e36209"}, "VerbatimString": {"selected-text-color": "#032f62", "text-color": "#032f62"}, "Warning": {"selected-text-color": "#ff5555", "text-color": "#ff5555"}}}