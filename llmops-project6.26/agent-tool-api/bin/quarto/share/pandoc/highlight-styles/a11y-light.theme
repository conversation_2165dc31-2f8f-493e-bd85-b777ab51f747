{"text-color": null, "background-color": "#fefefe", "line-number-color": "#767676", "line-number-background-color": null, "_comments": ["Last update: Aug 23, 2022 (revision 1.1)", "This file has been adapted from: https://github.com/ericwbailey/a11y-syntax-highlighting#a11y-light", "Also see: https://github.com/ericwbailey/a11y-syntax-highlighting/blob/main/dist/highlight/a11y-light.css"], "metadata": {"copyright": ["SPDX-FileCopyrightText: 2017 <PERSON>", "SPDX-FileCopyrightText: 2022 <PERSON>"], "license": "SPDX-License-Identifier: MIT", "name": "a11y light", "revision": 1}, "text-styles": {"Normal": {"text-color": "#545454"}, "Other": {"text-color": "#d91e18", "background-color": null, "bold": false, "italic": false, "underline": false}, "Attribute": {"text-color": "#a55a00", "background-color": null, "bold": false, "italic": false, "underline": false}, "SpecialString": {"text-color": "#008000", "background-color": null, "bold": false, "italic": false, "underline": false}, "Annotation": {"text-color": "#696969", "background-color": null, "bold": false, "italic": false, "underline": false}, "Function": {"text-color": "#06287e", "background-color": null, "bold": false, "italic": false, "underline": false}, "String": {"text-color": "#008000", "background-color": null, "bold": false, "italic": false, "underline": false}, "ControlFlow": {"text-color": "#d91e18", "background-color": null, "bold": false, "italic": false, "underline": false}, "Operator": {"text-color": "#00769e", "background-color": null, "bold": false, "italic": false, "underline": false}, "Error": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "BaseN": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "Alert": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "Variable": {"text-color": "#a55a00", "background-color": null, "bold": false, "italic": false, "underline": false}, "BuiltIn": {"text-color": null, "background-color": null, "bold": false, "italic": false, "underline": false}, "Extension": {"text-color": null, "background-color": null, "bold": false, "italic": false, "underline": false}, "Preprocessor": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "Information": {"text-color": "#696969", "background-color": null, "bold": false, "italic": false, "underline": false}, "VerbatimString": {"text-color": "#008000", "background-color": null, "bold": false, "italic": false, "underline": false}, "Warning": {"text-color": "#696969", "background-color": null, "bold": false, "italic": true, "underline": false}, "Documentation": {"text-color": "#696969", "background-color": null, "bold": false, "italic": true, "underline": false}, "Import": {"text-color": null, "background-color": null, "bold": false, "italic": false, "underline": false}, "Char": {"text-color": "#008000", "background-color": null, "bold": false, "italic": false, "underline": false}, "DataType": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "Float": {"text-color": "#a55a00", "background-color": null, "bold": false, "italic": false, "underline": false}, "Comment": {"text-color": "#696969", "background-color": null, "bold": false, "italic": false, "underline": false}, "CommentVar": {"text-color": "#696969", "background-color": null, "bold": false, "italic": true, "underline": false}, "Constant": {"text-color": "#d91e18", "background-color": null, "bold": false, "italic": false, "underline": false}, "SpecialChar": {"text-color": "#00769e", "background-color": null, "bold": false, "italic": false, "underline": false}, "DecVal": {"text-color": "#7928a1", "background-color": null, "bold": false, "italic": false, "underline": false}, "Keyword": {"text-color": "#d91e18", "background-color": null, "bold": false, "italic": false, "underline": false}}}