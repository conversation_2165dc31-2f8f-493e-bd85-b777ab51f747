<% 
const imgClz = about.options['image-shape'] ? `
  ${about.options['image-shape']}` : ""; 

const imageSize = () => {
  const width = about.options['image-width'];
  if (width !== undefined) {
    if (about.options['image-shape'] === "round") {
      return `style='height: ${width}; width: ${width};'`;
    } else {
      return `style='width: ${width};'`;
    }
  } else {
    return "";
  }
}

const altText = () => {
  if (about['image-alt']) {
    return `alt="${about['image-alt']}" `;
  } else {
    return "";
  }
}

const title = () => {
  if (about['image-title']) {
    return ` title="${about['image-title']}"`;
  } else {
    return "";
  }
}

const customClz = imgOpts.classes !== undefined ? imgOpts.classes : "";
%> 

<% if (about.image) { %>
<img src="<%= about.image %>" <%= altText() %>class="about-image<%= imgClz %> <%= customClz %>" <%= imageSize() %><%= title() %> />
<%}%>
