/*! @algolia/autocomplete-js 1.4.0 | MIT License | © Algolia, Inc. and contributors | https://github.com/algolia/autocomplete */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["@algolia/autocomplete-js"]={})}(this,(function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function a(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e){return{current:e}}function s(e,t){var n=void 0;return function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];n&&clearTimeout(n),n=setTimeout((function(){return e.apply(void 0,o)}),t)}}function p(e){return e.reduce((function(e,t){return e.concat(t)}),[])}var f=0;function d(){return"autocomplete-".concat(f++)}function m(e,t){return t.reduce((function(e,t){return e&&e[t]}),e)}function v(e){return 0===e.collections.length?0:e.collections.reduce((function(e,t){return e+t.items.length}),0)}var h=function(){};function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e,t,n){var r=t.initialState;return{getState:function(){return r},dispatch:function(o,i){var u=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},r);r=e(r,{type:o,props:t,payload:i}),n({state:r,prevState:u})}}}function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){P(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t,n,r){if(!n)return null;if(e<0&&(null===t||null!==r&&0===t))return n+e;var o=(null===t?-1:t)+e;return o<=-1||o>=n?null===r?null:0:o}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function I(e,t){var n=[];return Promise.resolve(e(t)).then((function(e){return Promise.all(e.filter((function(e){return Boolean(e)})).map((function(e){if(e.sourceId,n.includes(e.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(e.sourceId)," is not unique."));n.push(e.sourceId);var t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){w(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({getItemInputValue:function(e){return e.state.query},getItemUrl:function(){},onSelect:function(e){(0,e.setIsOpen)(!1)},onActive:h},e);return Promise.resolve(t)})))}))}function E(e){var t=function(e){var t=e.collections.map((function(e){return e.items.length})).reduce((function(e,t,n){var r=(e[n-1]||0)+t;return e.push(r),e}),[]).reduce((function(t,n){return n<=e.activeItemId?t+1:t}),0);return e.collections[t]}(e);if(!t)return null;var n=t.items[function(e){for(var t=e.state,n=e.collection,r=!1,o=0,i=0;!1===r;){var u=t.collections[o];if(u===n){r=!0;break}i+=u.items.length,o++}return t.activeItemId-i}({state:e,collection:t})],r=t.source;return{item:n,itemInputValue:r.getItemInputValue({item:n,state:e}),itemUrl:r.getItemUrl({item:n,state:e}),source:r}}function A(e,t){return e===t||e.contains(t)}function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){return function(e){if(Array.isArray(e))return N(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return N(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return N(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach((function(t){L(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function R(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e){return function(e){if(Array.isArray(e))return M(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return M(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function H(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?H(Object(n),!0).forEach((function(t){W(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function W(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Q(e){return Boolean(e.execute)}function $(e,t){return n=e,Boolean(null==n?void 0:n.execute)?V(V({},e),{},{requests:e.queries.map((function(n){return{query:n,sourceId:t,transformResponse:e.transformResponse}}))}):{items:e,sourceId:t};var n}function z(e){var t=e.reduce((function(e,t){if(!Q(t))return e.push(t),e;var n=t.searchClient,r=t.execute,o=t.requests,i=e.find((function(e){return Q(t)&&Q(e)&&e.searchClient===n&&e.execute===r}));if(i){var u;(u=i.items).push.apply(u,U(o))}else{var a={execute:r,items:o,searchClient:n};e.push(a)}return e}),[]).map((function(e){if(!Q(e))return Promise.resolve(e);var t=e,n=t.execute,r=t.items;return n({searchClient:t.searchClient,requests:r})}));return Promise.all(t).then((function(e){return p(e)}))}function G(e,t){return t.map((function(t){var n=e.filter((function(e){return e.sourceId===t.sourceId})),r=n.map((function(e){return e.items})),o=n[0].transformResponse,i=o?o(function(e){var t=e.map((function(e){var t;return C(C({},e),{},{hits:null===(t=e.hits)||void 0===t?void 0:t.map((function(t){return C(C({},t),{},{__autocomplete_indexName:e.index,__autocomplete_queryID:e.queryID})}))})}));return{results:t,hits:t.map((function(e){return e.hits})).filter(Boolean),facetHits:t.map((function(e){var t;return null===(t=e.facetHits)||void 0===t?void 0:t.map((function(e){return{label:e.value,count:e.count,_highlightResult:{label:{value:e.highlighted}}}}))})).filter(Boolean)}}(r)):r;return i.every(Boolean),'The `getItems` function from source "'.concat(t.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),".\n\nDid you forget to return items?\n\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"),{source:t,items:i}}))}function K(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?K(Object(n),!0).forEach((function(t){Y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function X(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Z=null;function ee(e){var t=e.event,n=e.nextState,r=void 0===n?{}:n,o=e.props,i=e.query,u=e.refresh,a=e.store,c=X(e,["event","nextState","props","query","refresh","store"]);Z&&o.environment.clearTimeout(Z);var l,s=c.setCollections,f=c.setIsOpen,d=c.setQuery,m=c.setActiveItemId,v=c.setStatus;return d(i),m(o.defaultActiveItemId),i||!1!==o.openOnFocus?(v("loading"),Z=o.environment.setTimeout((function(){v("stalled")}),o.stallThreshold),o.getSources(J({query:i,refresh:u,state:a.getState()},c)).then((function(e){return v("loading"),Promise.all(e.map((function(e){return Promise.resolve(e.getItems(J({query:i,refresh:u,state:a.getState()},c))).then((function(t){return $(t,e.sourceId)}))}))).then(z).then((function(t){return G(t,e)})).then((function(e){return function(e){var t=e.collections,n=e.props,r=e.state,o=t.reduce((function(e,t){return F(F({},e),{},R({},t.source.sourceId,F(F({},t.source),{},{getItems:function(){return p(t.items)}})))}),{});return p(n.reshape({sources:Object.values(o),sourcesBySourceId:o,state:r})).filter(Boolean).map((function(e){return{source:e,items:e.getItems()}}))}({collections:e,props:o,state:a.getState()})})).then((function(e){var n;v("idle"),s(e);var l=o.shouldPanelOpen({state:a.getState()});f(null!==(n=r.isOpen)&&void 0!==n?n:o.openOnFocus&&!i&&l||l);var p=E(a.getState());if(null!==a.getState().activeItemId&&p){var d=p.item,m=p.itemInputValue,h=p.itemUrl,g=p.source;g.onActive(J({event:t,item:d,itemInputValue:m,itemUrl:h,refresh:u,source:g,state:a.getState()},c))}})).finally((function(){Z&&o.environment.clearTimeout(Z)}))}))):(v("idle"),s(a.getState().collections.map((function(e){return J(J({},e),{},{items:[]})}))),f(null!==(l=r.isOpen)&&void 0!==l?l:o.shouldPanelOpen({state:a.getState()})),Promise.resolve())}function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){re(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function re(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oe(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(n),!0).forEach((function(t){ae(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ae(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ce(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function le(e){var t=e.props,n=e.refresh,r=e.store,o=ce(e,["props","refresh","store"]);return{getEnvironmentProps:function(e){var n=e.inputElement,o=e.formElement,i=e.panelElement;return ue({onTouchStart:function(e){!1!==r.getState().isOpen&&e.target!==n&&(!1===[o,i].some((function(n){return A(n,e.target)||A(n,t.environment.document.activeElement)}))&&r.dispatch("blur",null))},onTouchMove:function(e){!1!==r.getState().isOpen&&n===t.environment.document.activeElement&&e.target!==n&&n.blur()}},ce(e,["inputElement","formElement","panelElement"]))},getRootProps:function(e){return ue({role:"combobox","aria-expanded":r.getState().isOpen,"aria-haspopup":"listbox","aria-owns":r.getState().isOpen?"".concat(t.id,"-list"):void 0,"aria-labelledby":"".concat(t.id,"-label")},e)},getFormProps:function(e){e.inputElement;return ue({action:"",noValidate:!0,role:"search",onSubmit:function(i){var u;i.preventDefault(),t.onSubmit(ue({event:i,refresh:n,state:r.getState()},o)),r.dispatch("submit",null),null===(u=e.inputElement)||void 0===u||u.blur()},onReset:function(i){var u;i.preventDefault(),t.onReset(ue({event:i,refresh:n,state:r.getState()},o)),r.dispatch("reset",null),null===(u=e.inputElement)||void 0===u||u.focus()}},ce(e,["inputElement"]))},getLabelProps:function(e){return ue({htmlFor:"".concat(t.id,"-input"),id:"".concat(t.id,"-label")},e)},getInputProps:function(e){function i(e){(t.openOnFocus||Boolean(r.getState().query))&&ee(ue({event:e,props:t,query:r.getState().completion||r.getState().query,refresh:n,store:r},o)),r.dispatch("focus",null)}var u="ontouchstart"in t.environment,a=e||{},c=(a.inputElement,a.maxLength),l=void 0===c?512:c,s=ce(a,["inputElement","maxLength"]),p=E(r.getState());return ue({"aria-autocomplete":"both","aria-activedescendant":r.getState().isOpen&&null!==r.getState().activeItemId?"".concat(t.id,"-item-").concat(r.getState().activeItemId):void 0,"aria-controls":r.getState().isOpen?"".concat(t.id,"-list"):void 0,"aria-labelledby":"".concat(t.id,"-label"),value:r.getState().completion||r.getState().query,id:"".concat(t.id,"-input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:null!=p&&p.itemUrl?"go":"search",spellCheck:"false",autoFocus:t.autoFocus,placeholder:t.placeholder,maxLength:l,type:"search",onChange:function(e){ee(ue({event:e,props:t,query:e.currentTarget.value.slice(0,l),refresh:n,store:r},o))},onKeyDown:function(e){!function(e){var t=e.event,n=e.props,r=e.refresh,o=e.store,i=oe(e,["event","props","refresh","store"]);if("ArrowUp"===t.key||"ArrowDown"===t.key){var u=function(){var e=n.environment.document.getElementById("".concat(n.id,"-item-").concat(o.getState().activeItemId));e&&(e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e.scrollIntoView(!1))},a=function(){var e=E(o.getState());if(null!==o.getState().activeItemId&&e){var n=e.item,u=e.itemInputValue,a=e.itemUrl,c=e.source;c.onActive(ne({event:t,item:n,itemInputValue:u,itemUrl:a,refresh:r,source:c,state:o.getState()},i))}};t.preventDefault(),!1===o.getState().isOpen&&(n.openOnFocus||Boolean(o.getState().query))?ee(ne({event:t,props:n,query:o.getState().query,refresh:r,store:o},i)).then((function(){o.dispatch(t.key,{nextActiveItemId:n.defaultActiveItemId}),a(),setTimeout(u,0)})):(o.dispatch(t.key,{}),a(),u())}else if("Escape"===t.key)t.preventDefault(),o.dispatch(t.key,null);else if("Enter"===t.key){if(null===o.getState().activeItemId||o.getState().collections.every((function(e){return 0===e.items.length})))return;t.preventDefault();var c=E(o.getState()),l=c.item,s=c.itemInputValue,p=c.itemUrl,f=c.source;if(t.metaKey||t.ctrlKey)void 0!==p&&(f.onSelect(ne({event:t,item:l,itemInputValue:s,itemUrl:p,refresh:r,source:f,state:o.getState()},i)),n.navigator.navigateNewTab({itemUrl:p,item:l,state:o.getState()}));else if(t.shiftKey)void 0!==p&&(f.onSelect(ne({event:t,item:l,itemInputValue:s,itemUrl:p,refresh:r,source:f,state:o.getState()},i)),n.navigator.navigateNewWindow({itemUrl:p,item:l,state:o.getState()}));else if(t.altKey);else{if(void 0!==p)return f.onSelect(ne({event:t,item:l,itemInputValue:s,itemUrl:p,refresh:r,source:f,state:o.getState()},i)),void n.navigator.navigate({itemUrl:p,item:l,state:o.getState()});ee(ne({event:t,nextState:{isOpen:!1},props:n,query:s,refresh:r,store:o},i)).then((function(){f.onSelect(ne({event:t,item:l,itemInputValue:s,itemUrl:p,refresh:r,source:f,state:o.getState()},i))}))}}}(ue({event:e,props:t,refresh:n,store:r},o))},onFocus:i,onBlur:function(){u||r.dispatch("blur",null)},onClick:function(n){e.inputElement!==t.environment.document.activeElement||r.getState().isOpen||i(n)}},s)},getPanelProps:function(e){return ue({onMouseDown:function(e){e.preventDefault()},onMouseLeave:function(){r.dispatch("mouseleave",null)}},e)},getListProps:function(e){return ue({role:"listbox","aria-labelledby":"".concat(t.id,"-label"),id:"".concat(t.id,"-list")},e)},getItemProps:function(e){var i=e.item,u=e.source,a=ce(e,["item","source"]);return ue({id:"".concat(t.id,"-item-").concat(i.__autocomplete_id),role:"option","aria-selected":r.getState().activeItemId===i.__autocomplete_id,onMouseMove:function(e){if(i.__autocomplete_id!==r.getState().activeItemId){r.dispatch("mousemove",i.__autocomplete_id);var t=E(r.getState());if(null!==r.getState().activeItemId&&t){var u=t.item,a=t.itemInputValue,c=t.itemUrl,l=t.source;l.onActive(ue({event:e,item:u,itemInputValue:a,itemUrl:c,refresh:n,source:l,state:r.getState()},o))}}},onMouseDown:function(e){e.preventDefault()},onClick:function(e){var a=u.getItemInputValue({item:i,state:r.getState()}),c=u.getItemUrl({item:i,state:r.getState()});(c?Promise.resolve():ee(ue({event:e,nextState:{isOpen:!1},props:t,query:a,refresh:n,store:r},o))).then((function(){u.onSelect(ue({event:e,item:i,itemInputValue:a,itemUrl:c,refresh:n,source:u,state:r.getState()},o))}))}},a)}}}function se(e){var t,n=e.state;return!1===n.isOpen||null===n.activeItemId?null:(null===(t=E(n))||void 0===t?void 0:t.itemInputValue)||null}function pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pe(Object(n),!0).forEach((function(t){de(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var me=function(e,t){switch(t.type){case"setActiveItemId":return fe(fe({},e),{},{activeItemId:t.payload});case"setQuery":return fe(fe({},e),{},{query:t.payload,completion:null});case"setCollections":return fe(fe({},e),{},{collections:t.payload});case"setIsOpen":return fe(fe({},e),{},{isOpen:t.payload});case"setStatus":return fe(fe({},e),{},{status:t.payload});case"setContext":return fe(fe({},e),{},{context:fe(fe({},e.context),t.payload)});case"ArrowDown":var n=fe(fe({},e),{},{activeItemId:t.payload.hasOwnProperty("nextActiveItemId")?t.payload.nextActiveItemId:j(1,e.activeItemId,v(e),t.props.defaultActiveItemId)});return fe(fe({},n),{},{completion:se({state:n})});case"ArrowUp":var r=fe(fe({},e),{},{activeItemId:j(-1,e.activeItemId,v(e),t.props.defaultActiveItemId)});return fe(fe({},r),{},{completion:se({state:r})});case"Escape":return e.isOpen?fe(fe({},e),{},{activeItemId:null,isOpen:!1,completion:null}):fe(fe({},e),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return fe(fe({},e),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return fe(fe({},e),{},{activeItemId:!0===t.props.openOnFocus?t.props.defaultActiveItemId:null,status:"idle",query:""});case"focus":return fe(fe({},e),{},{activeItemId:t.props.defaultActiveItemId,isOpen:(t.props.openOnFocus||Boolean(e.query))&&t.props.shouldPanelOpen({state:e})});case"blur":return t.props.debug?e:fe(fe({},e),{},{isOpen:!1,activeItemId:null});case"mousemove":return fe(fe({},e),{},{activeItemId:t.payload});case"mouseleave":return fe(fe({},e),{},{activeItemId:t.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(t.type)," is not supported."),e}};function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function he(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach((function(t){ge(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ge(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ye(e){var t=[],n=function(e,t){var n,r="undefined"!=typeof window?window:{},o=e.plugins||[];return T(T({debug:!1,openOnFocus:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,environment:r,shouldPanelOpen:function(e){return v(e.state)>0},reshape:function(e){return e.sources}},e),{},{id:null!==(n=e.id)&&void 0!==n?n:d(),plugins:o,initialState:T({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},e.initialState),onStateChange:function(t){var n;null===(n=e.onStateChange)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onStateChange)||void 0===n?void 0:n.call(e,t)}))},onSubmit:function(t){var n;null===(n=e.onSubmit)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onSubmit)||void 0===n?void 0:n.call(e,t)}))},onReset:function(t){var n;null===(n=e.onReset)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onReset)||void 0===n?void 0:n.call(e,t)}))},getSources:function(n){return Promise.all([].concat(x(o.map((function(e){return e.getSources}))),[e.getSources]).filter(Boolean).map((function(e){return I(e,n)}))).then((function(e){return p(e)})).then((function(e){return e.map((function(e){return T(T({},e),{},{onSelect:function(n){e.onSelect(n),t.forEach((function(e){var t;return null===(t=e.onSelect)||void 0===t?void 0:t.call(e,n)}))},onActive:function(n){e.onActive(n),t.forEach((function(e){var t;return null===(t=e.onActive)||void 0===t?void 0:t.call(e,n)}))}})}))}))},navigator:T({navigate:function(e){var t=e.itemUrl;r.location.assign(t)},navigateNewTab:function(e){var t=e.itemUrl,n=r.open(t,"_blank","noopener");null==n||n.focus()},navigateNewWindow:function(e){var t=e.itemUrl;r.open(t,"_blank","noopener")}},e.navigator)})}(e,t),r=b(me,n,(function(e){var t=e.prevState,r=e.state;n.onStateChange(he({prevState:t,state:r,refresh:u},o))})),o=function(e){var t=e.store;return{setActiveItemId:function(e){t.dispatch("setActiveItemId",e)},setQuery:function(e){t.dispatch("setQuery",e)},setCollections:function(e){var n=0,r=e.map((function(e){return _(_({},e),{},{items:p(e.items).map((function(e){return _(_({},e),{},{__autocomplete_id:n++})}))})}));t.dispatch("setCollections",r)},setIsOpen:function(e){t.dispatch("setIsOpen",e)},setStatus:function(e){t.dispatch("setStatus",e)},setContext:function(e){t.dispatch("setContext",e)}}}({store:r}),i=le(he({props:n,refresh:u,store:r},o));function u(){return ee(he({event:new Event("input"),nextState:{isOpen:r.getState().isOpen},props:n,query:r.getState().query,refresh:u,store:r},o))}return n.plugins.forEach((function(e){var n;return null===(n=e.subscribe)||void 0===n?void 0:n.call(e,he(he({},o),{},{refresh:u,onSelect:function(e){t.push({onSelect:e})},onActive:function(e){t.push({onActive:e})}}))})),he(he({refresh:u},i),o)}var be=function(e){var t=e.environment,n=t.document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("class","aa-ClearIcon"),n.setAttribute("viewBox","0 0 24 24"),n.setAttribute("width","18"),n.setAttribute("height","18"),n.setAttribute("fill","currentColor");var r=t.document.createElementNS("http://www.w3.org/2000/svg","path");return r.setAttribute("d","M5.293 6.707l5.293 5.293-5.293 5.293c-0.391 0.391-0.391 1.024 0 1.414s1.024 0.391 1.414 0l5.293-5.293 5.293 5.293c0.391 0.391 1.024 0.391 1.414 0s0.391-1.024 0-1.414l-5.293-5.293 5.293-5.293c0.391-0.391 0.391-1.024 0-1.414s-1.024-0.391-1.414 0l-5.293 5.293-5.293-5.293c-0.391-0.391-1.024-0.391-1.414 0s-0.391 1.024 0 1.414z"),n.appendChild(r),n};function Oe(e,t){if("string"==typeof t){var n=e.document.querySelector(t);return"The element ".concat(JSON.stringify(t)," is not in the document."),n}return t}function _e(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((function(e,t){return Object.keys(t).forEach((function(n){var r=e[n],o=t[n];r!==o&&(e[n]=[r,o].filter(Boolean).join(" "))})),e}),{})}var Pe=function(e){return e&&"object"===t(e)};function je(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((function(e,t){return Object.keys(t).forEach((function(n){var r=e[n],o=t[n];Array.isArray(r)&&Array.isArray(o)?e[n]=r.concat.apply(r,a(o)):Pe(r)&&Pe(o)?e[n]=je(r,o):e[n]=o})),e}),{})}function Se(e,t,n){e[t]=null===n?"":"number"!=typeof n?n:n+"px"}function we(e){this._listeners[e.type](e)}function Ie(e,t,n){var r,o,i=e[t];if("style"===t)if("string"==typeof n)e.style=n;else if(null===n)e.style="";else for(t in n)i&&n[t]===i[t]||Se(e.style,t,n[t]);else"o"===t[0]&&"n"===t[1]?(r=t!==(t=t.replace(/Capture$/,"")),(o=t.toLowerCase())in e&&(t=o),t=t.slice(2),e._listeners||(e._listeners={}),e._listeners[t]=n,n?i||e.addEventListener(t,we,r):e.removeEventListener(t,we,r)):"list"!==t&&"tagName"!==t&&"form"!==t&&"type"!==t&&"size"!==t&&"download"!==t&&"href"!==t&&t in e?e[t]=null==n?"":n:"function"!=typeof n&&"dangerouslySetInnerHTML"!==t&&(null==n||!1===n&&!/^ar/.test(t)?e.removeAttribute(t):e.setAttribute(t,n))}function Ee(e){switch(e){case"onChange":return"onInput";default:return e}}function Ae(e,t){for(var n in t)Ie(e,Ee(n),t[n])}function De(e,t){for(var n in t)"o"===n[0]&&"n"===n[1]||Ie(e,Ee(n),t[n])}function Ce(e){return function(t,n){var r=n.children,o=void 0===r?[]:r,i=u(n,["children"]),c=e.document.createElement(t);return Ae(c,i),c.append.apply(c,a(o)),c}}var ke=function(e){var t=e.environment.document.createElementNS("http://www.w3.org/2000/svg","svg");return t.setAttribute("class","aa-LoadingIcon"),t.setAttribute("viewBox","0 0 100 100"),t.setAttribute("width","20"),t.setAttribute("height","20"),t.innerHTML='<circle\n  cx="50"\n  cy="50"\n  fill="none"\n  r="35"\n  stroke="currentColor"\n  stroke-dasharray="164.93361431346415 56.97787143782138"\n  stroke-width="6"\n>\n  <animateTransform\n    attributeName="transform"\n    type="rotate"\n    repeatCount="indefinite"\n    dur="1s"\n    values="0 50 50;90 50 50;180 50 50;360 50 50"\n    keyTimes="0;0.40;0.65;1"\n  />\n</circle>',t},xe=function(e){var t=e.environment,n=t.document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("class","aa-SubmitIcon"),n.setAttribute("viewBox","0 0 24 24"),n.setAttribute("width","20"),n.setAttribute("height","20"),n.setAttribute("fill","currentColor");var r=t.document.createElementNS("http://www.w3.org/2000/svg","path");return r.setAttribute("d","M16.041 15.856c-0.034 0.026-0.067 0.055-0.099 0.087s-0.060 0.064-0.087 0.099c-1.258 1.213-2.969 1.958-4.855 1.958-1.933 0-3.682-0.782-4.95-2.050s-2.050-3.017-2.050-4.95 0.782-3.682 2.050-4.95 3.017-2.050 4.95-2.050 3.682 0.782 4.95 2.050 2.050 3.017 2.050 4.95c0 1.886-0.745 3.597-1.959 4.856zM21.707 20.293l-3.675-3.675c1.231-1.54 1.968-3.493 1.968-5.618 0-2.485-1.008-4.736-2.636-6.364s-3.879-2.636-6.364-2.636-4.736 1.008-6.364 2.636-2.636 3.879-2.636 6.364 1.008 4.736 2.636 6.364 3.879 2.636 6.364 2.636c2.125 0 4.078-0.737 5.618-1.968l3.675 3.675c0.391 0.391 1.024 0.391 1.414 0s0.391-1.024 0-1.414z"),n.appendChild(r),n};function Ne(e){var t=e.autocomplete,n=e.autocompleteScopeApi,r=e.classNames,o=e.environment,a=e.isDetached,c=e.placeholder,l=void 0===c?"Search":c,s=e.propGetters,p=e.setIsModalOpen,f=e.state,d=e.translations,m=Ce(o),v=s.getRootProps(i({state:f,props:t.getRootProps({})},n)),h=m("div",i({class:r.root},v)),g=m("div",{class:r.detachedContainer,onMouseDown:function(e){e.stopPropagation()}}),y=m("div",{class:r.detachedOverlay,children:[g],onMouseDown:function(){p(!1),t.setIsOpen(!1)}}),b=s.getLabelProps(i({state:f,props:t.getLabelProps({})},n)),O=m("button",{class:r.submitButton,type:"submit",title:d.submitButtonTitle,children:[xe({environment:o})]}),_=m("label",i({class:r.label,children:[O]},b)),P=m("button",{class:r.clearButton,type:"reset",title:d.clearButtonTitle,children:[be({environment:o})]}),j=m("div",{class:r.loadingIndicator,children:[ke({environment:o})]}),S=function(e){var t=e.autocompleteScopeApi,n=e.environment,r=(e.classNames,e.getInputProps),o=e.getInputPropsCore,a=e.onDetachedEscape,c=e.state,l=u(e,["autocompleteScopeApi","environment","classNames","getInputProps","getInputPropsCore","onDetachedEscape","state"]),s=Ce(n)("input",l),p=r(i({state:c,props:o({inputElement:s}),inputElement:s},t));return Ae(s,i(i({},p),{},{onKeyDown:function(e){if(a&&"Escape"===e.key)return e.preventDefault(),void a();p.onKeyDown(e)}})),s}({class:r.input,environment:o,state:f,getInputProps:s.getInputProps,getInputPropsCore:t.getInputProps,autocompleteScopeApi:n,onDetachedEscape:a?function(){t.setIsOpen(!1),p(!1)}:void 0}),w=m("div",{class:r.inputWrapperPrefix,children:[_,j]}),I=m("div",{class:r.inputWrapperSuffix,children:[P]}),E=m("div",{class:r.inputWrapper,children:[S]}),A=s.getFormProps(i({state:f,props:t.getFormProps({inputElement:S})},n)),D=m("form",i({class:r.form,children:[w,E,I]},A)),C=s.getPanelProps(i({state:f,props:t.getPanelProps({})},n)),k=m("div",i({class:r.panel},C));if(a){var x=m("div",{class:r.detachedSearchButtonIcon,children:[xe({environment:o})]}),N=m("div",{class:r.detachedSearchButtonPlaceholder,textContent:l}),B=m("button",{class:r.detachedSearchButton,onClick:function(e){e.preventDefault(),p(!0)},children:[x,N]}),T=m("button",{class:r.detachedCancelButton,textContent:d.detachedCancelButtonText,onClick:function(){t.setIsOpen(!1),p(!1)}}),L=m("div",{class:r.detachedFormContainer,children:[D,T]});g.appendChild(L),h.appendChild(B)}else h.appendChild(D);return{detachedContainer:g,detachedOverlay:y,inputWrapper:E,input:S,root:h,form:D,label:_,submitButton:O,clearButton:P,loadingIndicator:j,panel:k}}var Be,Te,Le,qe,Fe={},Re=[],Ue=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function Me(e,t){for(var n in t)e[n]=t[n];return e}function He(e){var t=e.parentNode;t&&t.removeChild(e)}function Ve(e,t,n){var r,o,i,u=arguments,a={};for(i in t)"key"==i?r=t[i]:"ref"==i?o=t[i]:a[i]=t[i];if(arguments.length>3)for(n=[n],i=3;i<arguments.length;i++)n.push(u[i]);if(null!=n&&(a.children=n),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===a[i]&&(a[i]=e.defaultProps[i]);return We(e,a,r,o,null)}function We(e,t,n,r,o){var i={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++Be.__v:o};return null!=Be.vnode&&Be.vnode(i),i}function Qe(e){return e.children}function $e(e,t){this.props=e,this.context=t}function ze(e,t){if(null==t)return e.__?ze(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?ze(e):null}function Ge(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return Ge(e)}}function Ke(e){(!e.__d&&(e.__d=!0)&&Te.push(e)&&!Je.__r++||qe!==Be.debounceRendering)&&((qe=Be.debounceRendering)||Le)(Je)}function Je(){for(var e;Je.__r=Te.length;)e=Te.sort((function(e,t){return e.__v.__b-t.__v.__b})),Te=[],e.some((function(e){var t,n,r,o,i,u;e.__d&&(i=(o=(t=e).__v).__e,(u=t.__P)&&(n=[],(r=Me({},o)).__v=o.__v+1,ot(u,o,r,t.__n,void 0!==u.ownerSVGElement,null!=o.__h?[i]:null,n,null==i?ze(o):i,o.__h),it(n,o),o.__e!=i&&Ge(o)))}))}function Ye(e,t,n,r,o,i,u,a,c,l){var s,p,f,d,m,v,h,g=r&&r.__k||Re,y=g.length;for(n.__k=[],s=0;s<t.length;s++)if(null!=(d=n.__k[s]=null==(d=t[s])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?We(null,d,null,null,d):Array.isArray(d)?We(Qe,{children:d},null,null,null):d.__b>0?We(d.type,d.props,d.key,null,d.__v):d)){if(d.__=n,d.__b=n.__b+1,null===(f=g[s])||f&&d.key==f.key&&d.type===f.type)g[s]=void 0;else for(p=0;p<y;p++){if((f=g[p])&&d.key==f.key&&d.type===f.type){g[p]=void 0;break}f=null}ot(e,d,f=f||Fe,o,i,u,a,c,l),m=d.__e,(p=d.ref)&&f.ref!=p&&(h||(h=[]),f.ref&&h.push(f.ref,null,d),h.push(p,d.__c||m,d)),null!=m?(null==v&&(v=m),"function"==typeof d.type&&null!=d.__k&&d.__k===f.__k?d.__d=c=Xe(d,c,e):c=Ze(e,d,f,g,m,c),l||"option"!==n.type?"function"==typeof n.type&&(n.__d=c):e.value=""):c&&f.__e==c&&c.parentNode!=e&&(c=ze(f))}for(n.__e=v,s=y;s--;)null!=g[s]&&("function"==typeof n.type&&null!=g[s].__e&&g[s].__e==n.__d&&(n.__d=ze(r,s+1)),ct(g[s],g[s]));if(h)for(s=0;s<h.length;s++)at(h[s],h[++s],h[++s])}function Xe(e,t,n){var r,o;for(r=0;r<e.__k.length;r++)(o=e.__k[r])&&(o.__=e,t="function"==typeof o.type?Xe(o,t,n):Ze(n,o,o,e.__k,o.__e,t));return t}function Ze(e,t,n,r,o,i){var u,a,c;if(void 0!==t.__d)u=t.__d,t.__d=void 0;else if(null==n||o!=i||null==o.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(o),u=null;else{for(a=i,c=0;(a=a.nextSibling)&&c<r.length;c+=2)if(a==o)break e;e.insertBefore(o,i),u=i}return void 0!==u?u:o.nextSibling}function et(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||Ue.test(t)?n:n+"px"}function tt(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||et(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||et(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?rt:nt,i):e.removeEventListener(t,i?rt:nt,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function nt(e){this.l[e.type+!1](Be.event?Be.event(e):e)}function rt(e){this.l[e.type+!0](Be.event?Be.event(e):e)}function ot(e,t,n,r,o,i,u,a,c){var l,s,p,f,d,m,v,h,g,y,b,O=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(c=n.__h,a=t.__e=n.__e,t.__h=null,i=[a]),(l=Be.__b)&&l(t);try{e:if("function"==typeof O){if(h=t.props,g=(l=O.contextType)&&r[l.__c],y=l?g?g.props.value:l.__:r,n.__c?v=(s=t.__c=n.__c).__=s.__E:("prototype"in O&&O.prototype.render?t.__c=s=new O(h,y):(t.__c=s=new $e(h,y),s.constructor=O,s.render=lt),g&&g.sub(s),s.props=h,s.state||(s.state={}),s.context=y,s.__n=r,p=s.__d=!0,s.__h=[]),null==s.__s&&(s.__s=s.state),null!=O.getDerivedStateFromProps&&(s.__s==s.state&&(s.__s=Me({},s.__s)),Me(s.__s,O.getDerivedStateFromProps(h,s.__s))),f=s.props,d=s.state,p)null==O.getDerivedStateFromProps&&null!=s.componentWillMount&&s.componentWillMount(),null!=s.componentDidMount&&s.__h.push(s.componentDidMount);else{if(null==O.getDerivedStateFromProps&&h!==f&&null!=s.componentWillReceiveProps&&s.componentWillReceiveProps(h,y),!s.__e&&null!=s.shouldComponentUpdate&&!1===s.shouldComponentUpdate(h,s.__s,y)||t.__v===n.__v){s.props=h,s.state=s.__s,t.__v!==n.__v&&(s.__d=!1),s.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),s.__h.length&&u.push(s);break e}null!=s.componentWillUpdate&&s.componentWillUpdate(h,s.__s,y),null!=s.componentDidUpdate&&s.__h.push((function(){s.componentDidUpdate(f,d,m)}))}s.context=y,s.props=h,s.state=s.__s,(l=Be.__r)&&l(t),s.__d=!1,s.__v=t,s.__P=e,l=s.render(s.props,s.state,s.context),s.state=s.__s,null!=s.getChildContext&&(r=Me(Me({},r),s.getChildContext())),p||null==s.getSnapshotBeforeUpdate||(m=s.getSnapshotBeforeUpdate(f,d)),b=null!=l&&l.type===Qe&&null==l.key?l.props.children:l,Ye(e,Array.isArray(b)?b:[b],t,n,r,o,i,u,a,c),s.base=t.__e,t.__h=null,s.__h.length&&u.push(s),v&&(s.__E=s.__=null),s.__e=!1}else null==i&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=ut(n.__e,t,n,r,o,i,u,c);(l=Be.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=i)&&(t.__e=a,t.__h=!!c,i[i.indexOf(a)]=null),Be.__e(e,t,n)}}function it(e,t){Be.__c&&Be.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){Be.__e(e,t.__v)}}))}function ut(e,t,n,r,o,i,u,a){var c,l,s,p,f=n.props,d=t.props,m=t.type,v=0;if("svg"===m&&(o=!0),null!=i)for(;v<i.length;v++)if((c=i[v])&&(c===e||(m?c.localName==m:3==c.nodeType))){e=c,i[v]=null;break}if(null==e){if(null===m)return document.createTextNode(d);e=o?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,d.is&&d),i=null,a=!1}if(null===m)f===d||a&&e.data===d||(e.data=d);else{if(i=i&&Re.slice.call(e.childNodes),l=(f=n.props||Fe).dangerouslySetInnerHTML,s=d.dangerouslySetInnerHTML,!a){if(null!=i)for(f={},p=0;p<e.attributes.length;p++)f[e.attributes[p].name]=e.attributes[p].value;(s||l)&&(s&&(l&&s.__html==l.__html||s.__html===e.innerHTML)||(e.innerHTML=s&&s.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||tt(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||tt(e,i,t[i],n[i],r)}(e,d,f,o,a),s)t.__k=[];else if(v=t.props.children,Ye(e,Array.isArray(v)?v:[v],t,n,r,o&&"foreignObject"!==m,i,u,e.firstChild,a),null!=i)for(v=i.length;v--;)null!=i[v]&&He(i[v]);a||("value"in d&&void 0!==(v=d.value)&&(v!==e.value||"progress"===m&&!v)&&tt(e,"value",v,f.value,!1),"checked"in d&&void 0!==(v=d.checked)&&v!==e.checked&&tt(e,"checked",v,f.checked,!1))}return e}function at(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){Be.__e(e,n)}}function ct(e,t,n){var r,o,i;if(Be.unmount&&Be.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||at(r,null,t)),n||"function"==typeof e.type||(n=null!=(o=e.__e)),e.__e=e.__d=void 0,null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){Be.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&ct(r[i],t,n);null!=o&&He(o)}function lt(e,t,n){return this.constructor(e,n)}Be={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e},__v:0},$e.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Me({},this.state),"function"==typeof e&&(e=e(Me({},n),this.props)),e&&Me(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),Ke(this))},$e.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Ke(this))},$e.prototype.render=Qe,Te=[],Le="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Je.__r=0;var st="__aa-highlight__",pt="__/aa-highlight__";function ft(e){var t=e.highlightedValue.split(st),n=t.shift(),r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return{get:function(){return e},add:function(t){var n=e[e.length-1];(null==n?void 0:n.isHighlighted)===t.isHighlighted?e[e.length-1]={value:n.value+t.value,isHighlighted:n.isHighlighted}:e.push(t)}}}(n?[{value:n,isHighlighted:!1}]:[]);return t.forEach((function(e){var t=e.split(pt);r.add({value:t[0],isHighlighted:!0}),""!==t[1]&&r.add({value:t[1],isHighlighted:!1})})),r.get()}function dt(e){return function(e){if(Array.isArray(e))return mt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return mt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return mt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function vt(e){var t=e.hit,n=e.attribute,r=Array.isArray(n)?n:[n],o=m(t,["_highlightResult"].concat(dt(r),["value"]));return"string"!=typeof o&&(o=m(t,r)||""),ft({highlightedValue:o})}var ht={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},gt=new RegExp(/\w/i),yt=/&(amp|quot|lt|gt|#39);/g,bt=RegExp(yt.source);function Ot(e,t){var n,r,o,i=e[t],u=(null===(n=e[t+1])||void 0===n?void 0:n.isHighlighted)||!0,a=(null===(r=e[t-1])||void 0===r?void 0:r.isHighlighted)||!0;return gt.test((o=i.value)&&bt.test(o)?o.replace(yt,(function(e){return ht[e]})):o)||a!==u?i.isHighlighted:a}function _t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_t(Object(n),!0).forEach((function(t){jt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_t(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function St(e){return e.some((function(e){return e.isHighlighted}))?e.map((function(t,n){return Pt(Pt({},t),{},{isHighlighted:!Ot(e,n)})})):e.map((function(e){return Pt(Pt({},e),{},{isHighlighted:!1})}))}function wt(e){return function(e){if(Array.isArray(e))return It(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return It(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return It(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function It(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Et(e){var t=e.hit,n=e.attribute,r=Array.isArray(n)?n:[n],o=m(t,["_snippetResult"].concat(wt(r),["value"]));return"string"!=typeof o&&(o=m(t,r)||""),ft({highlightedValue:o})}function At(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?At(Object(n),!0).forEach((function(t){Ct(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):At(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var kt="1.4.0";function xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(n),!0).forEach((function(t){Bt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Bt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tt(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Lt(e){return function(e){if(Array.isArray(e))return qt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return qt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ft(e){var t=e.createElement,n=e.Fragment;return function(e){var r,o=e.hit,i=e.attribute,u=e.tagName,a=void 0===u?"mark":u;return t(n,{},(r={hit:o,attribute:i},St(vt(r))).map((function(e,n){return e.isHighlighted?t(a,{key:n},e.value):e.value})))}}function Rt(e){var t=e.createElement,n=e.Fragment;return function(e){var r,o=e.hit,i=e.attribute,u=e.tagName,a=void 0===u?"mark":u;return t(n,{},(r={hit:o,attribute:i},St(Et(r))).map((function(e,n){return e.isHighlighted?t(a,{key:n},e.value):e.value})))}}function Ut(e){var t=e.createElement,n=e.Fragment;return function(e){var r=e.hit,o=e.attribute,i=e.tagName,u=void 0===i?"mark":i;return t(n,{},Et({hit:r,attribute:o}).map((function(e,n){return e.isHighlighted?t(u,{key:n},e.value):e.value})))}}var Mt={clearButton:"aa-ClearButton",detachedCancelButton:"aa-DetachedCancelButton",detachedContainer:"aa-DetachedContainer",detachedFormContainer:"aa-DetachedFormContainer",detachedOverlay:"aa-DetachedOverlay",detachedSearchButton:"aa-DetachedSearchButton",detachedSearchButtonIcon:"aa-DetachedSearchButtonIcon",detachedSearchButtonPlaceholder:"aa-DetachedSearchButtonPlaceholder",form:"aa-Form",input:"aa-Input",inputWrapper:"aa-InputWrapper",inputWrapperPrefix:"aa-InputWrapperPrefix",inputWrapperSuffix:"aa-InputWrapperSuffix",item:"aa-Item",label:"aa-Label",list:"aa-List",loadingIndicator:"aa-LoadingIndicator",panel:"aa-Panel",panelLayout:"aa-PanelLayout aa-Panel--scrollable",root:"aa-Autocomplete",source:"aa-Source",sourceFooter:"aa-SourceFooter",sourceHeader:"aa-SourceHeader",sourceNoResults:"aa-SourceNoResults",submitButton:"aa-SubmitButton"},Ht=function(e,t){!function(e,t,n){var r,o,i;Be.__&&Be.__(e,t),o=(r="function"==typeof n)?null:n&&n.__k||t.__k,i=[],ot(t,e=(!r&&n||t).__k=Ve(Qe,null,[e]),o||Fe,Fe,void 0!==t.ownerSVGElement,!r&&n?[n]:o?null:t.firstChild?Re.slice.call(t.childNodes):null,i,!r&&n?n:o?o.__e:t.firstChild,r),it(i,e)}(e.children,t)},Vt={createElement:Ve,Fragment:Qe};function Wt(e){var t=e.panelPlacement,n=e.container,r=e.form,o=e.environment,i=n.getBoundingClientRect(),u=(o.pageYOffset||o.document.documentElement.scrollTop||o.document.body.scrollTop||0)+i.top+i.height;switch(t){case"start":return{top:u,left:i.left};case"end":return{top:u,right:o.document.documentElement.clientWidth-(i.left+i.width)};case"full-width":return{top:u,left:0,right:0,width:"unset",maxWidth:"unset"};case"input-wrapper-width":var a=r.getBoundingClientRect();return{top:u,left:a.left,right:o.document.documentElement.clientWidth-(a.left+a.width),width:"unset",maxWidth:"unset"};default:throw new Error("[Autocomplete] The `panelPlacement` value ".concat(JSON.stringify(t)," is not valid."))}}var Qt=function(e){function t(t){return e({searchClient:t.searchClient,queries:t.requests.map((function(e){return e.query}))}).then((function(e){return e.map((function(e,n){var r=t.requests[n];return{items:e,sourceId:r.sourceId,transformResponse:r.transformResponse}}))}))}return function(e){return function(n){return Dt(Dt({execute:t},e),n)}}}((function(e){return t=i(i({},e),{},{userAgents:[{segment:"autocomplete-js",version:"1.4.0"}]}),n=t.searchClient,r=t.queries,o=t.userAgents,u=void 0===o?[]:o,"function"==typeof n.addAlgoliaAgent&&[{segment:"autocomplete-core",version:kt}].concat(Lt(u)).forEach((function(e){var t=e.segment,r=e.version;n.addAlgoliaAgent(t,r)})),n.search(r.map((function(e){var t=e.params;return Nt(Nt({},Tt(e,["params"])),{},{params:Nt({hitsPerPage:5,highlightPreTag:st,highlightPostTag:pt},t)})}))).then((function(e){return e.results}));var t,n,r,o,u}));var $t=Qt({transformResponse:function(e){return e.hits}});e.autocomplete=function(e){var t,o=function(){var e=[],t=[];function n(n){e.push(n);var r=n();t.push(r)}return{runEffect:n,cleanupEffects:function(){var e=t;t=[],e.forEach((function(e){e()}))},runEffects:function(){var t=e;e=[],t.forEach((function(e){n(e)}))}}}(),a=o.runEffect,c=o.cleanupEffects,p=o.runEffects,f=(t=[],{reactive:function(e){var n=e(),r={_fn:e,_ref:{current:n},get value(){return this._ref.current},set value(e){this._ref.current=e}};return t.push(r),r},runReactives:function(){t.forEach((function(e){e._ref.current=e._fn()}))}}),m=f.reactive,h=f.runReactives,g=l(!1),y=l(e),b=l(void 0),O=m((function(){return function(e){var t,n=e.classNames,r=e.container,o=e.getEnvironmentProps,a=e.getFormProps,c=e.getInputProps,l=e.getItemProps,s=e.getLabelProps,p=e.getListProps,f=e.getPanelProps,m=e.getRootProps,v=e.panelContainer,h=e.panelPlacement,g=e.render,y=e.renderNoResults,b=e.renderer,O=e.detachedMediaQuery,_=e.components,P=e.translations,j=u(e,["classNames","container","getEnvironmentProps","getFormProps","getInputProps","getItemProps","getLabelProps","getListProps","getPanelProps","getRootProps","panelContainer","panelPlacement","render","renderNoResults","renderer","detachedMediaQuery","components","translations"]),S="undefined"!=typeof window?window:{},w=Oe(S,r);w.tagName;var I,E,A,D=null!=b?b:Vt,C={Highlight:(I=D,E=I.createElement,A=I.Fragment,function(e){var t=e.hit,n=e.attribute,r=e.tagName,o=void 0===r?"mark":r;return E(A,{},vt({hit:t,attribute:n}).map((function(e,t){return e.isHighlighted?E(o,{key:t},e.value):e.value})))}),ReverseHighlight:Ft(D),ReverseSnippet:Rt(D),Snippet:Ut(D)};return{renderer:{classNames:_e(Mt,null!=n?n:{}),container:w,getEnvironmentProps:null!=o?o:function(e){return e.props},getFormProps:null!=a?a:function(e){return e.props},getInputProps:null!=c?c:function(e){return e.props},getItemProps:null!=l?l:function(e){return e.props},getLabelProps:null!=s?s:function(e){return e.props},getListProps:null!=p?p:function(e){return e.props},getPanelProps:null!=f?f:function(e){return e.props},getRootProps:null!=m?m:function(e){return e.props},panelContainer:v?Oe(S,v):S.document.body,panelPlacement:null!=h?h:"input-wrapper-width",render:null!=g?g:Ht,renderNoResults:y,renderer:D,detachedMediaQuery:null!=O?O:getComputedStyle(S.document.documentElement).getPropertyValue("--aa-detached-media-query"),components:i(i({},C),_),translations:i(i({},{clearButtonTitle:"Clear",detachedCancelButtonText:"Cancel",submitButtonTitle:"Submit"}),P)},core:i(i({},j),{},{id:null!==(t=j.id)&&void 0!==t?t:d(),environment:S})}}(y.current)})),_=m((function(){return O.value.core.environment.matchMedia(O.value.renderer.detachedMediaQuery).matches})),P=m((function(){return ye(i(i({},O.value.core),{},{onStateChange:function(e){var t,n,r;g.current=e.state.collections.some((function(e){return e.source.templates.noResults})),null===(t=b.current)||void 0===t||t.call(b,e),null===(n=(r=O.value.core).onStateChange)||void 0===n||n.call(r,e)},shouldPanelOpen:y.current.shouldPanelOpen||function(e){var t=e.state;if(_.value)return!0;var n=v(t)>0;if(!O.value.core.openOnFocus&&!t.query)return n;var r=Boolean(g.current||O.value.renderer.renderNoResults);return!n&&r||n}}))})),j=l(i({collections:[],completion:null,context:{},isOpen:!1,query:"",activeItemId:null,status:"idle"},O.value.core.initialState)),S={getEnvironmentProps:O.value.renderer.getEnvironmentProps,getFormProps:O.value.renderer.getFormProps,getInputProps:O.value.renderer.getInputProps,getItemProps:O.value.renderer.getItemProps,getLabelProps:O.value.renderer.getLabelProps,getListProps:O.value.renderer.getListProps,getPanelProps:O.value.renderer.getPanelProps,getRootProps:O.value.renderer.getRootProps},w={setActiveItemId:P.value.setActiveItemId,setQuery:P.value.setQuery,setCollections:P.value.setCollections,setIsOpen:P.value.setIsOpen,setStatus:P.value.setStatus,setContext:P.value.setContext,refresh:P.value.refresh},I=m((function(){return Ne({autocomplete:P.value,autocompleteScopeApi:w,classNames:O.value.renderer.classNames,environment:O.value.core.environment,isDetached:_.value,placeholder:O.value.core.placeholder,propGetters:S,setIsModalOpen:C,state:j.current,translations:O.value.renderer.translations})}));function E(){Ae(I.value.panel,{style:_.value?{}:Wt({panelPlacement:O.value.renderer.panelPlacement,container:I.value.root,form:I.value.form,environment:O.value.core.environment})})}function A(e){j.current=e;var t={autocomplete:P.value,autocompleteScopeApi:w,classNames:O.value.renderer.classNames,components:O.value.renderer.components,container:O.value.renderer.container,createElement:O.value.renderer.renderer.createElement,dom:I.value,Fragment:O.value.renderer.renderer.Fragment,panelContainer:_.value?I.value.detachedContainer:O.value.renderer.panelContainer,propGetters:S,state:j.current},n=!v(e)&&!g.current&&O.value.renderer.renderNoResults||O.value.renderer.render;!function(e){var t=e.autocomplete,n=e.autocompleteScopeApi,r=e.dom,o=e.propGetters,u=e.state;De(r.root,o.getRootProps(i({state:u,props:t.getRootProps({})},n))),De(r.input,o.getInputProps(i({state:u,props:t.getInputProps({inputElement:r.input}),inputElement:r.input},n))),Ae(r.label,{hidden:"stalled"===u.status}),Ae(r.loadingIndicator,{hidden:"stalled"!==u.status}),Ae(r.clearButton,{hidden:!u.query})}(t),function(e,t){var n=t.autocomplete,o=t.autocompleteScopeApi,u=t.classNames,a=t.createElement,c=t.dom,l=t.Fragment,s=t.panelContainer,p=t.propGetters,f=t.state,d=t.components;if(f.isOpen){s.contains(c.panel)||"loading"===f.status||s.appendChild(c.panel),c.panel.classList.toggle("aa-Panel--stalled","stalled"===f.status);var m=f.collections.filter((function(e){var t=e.source,n=e.items;return t.templates.noResults||n.length>0})).map((function(e,t){var c=e.source,s=e.items;return a("section",{key:t,className:u.source,"data-autocomplete-source-id":c.sourceId},c.templates.header&&a("div",{className:u.sourceHeader},c.templates.header({components:d,createElement:a,Fragment:l,items:s,source:c,state:f})),c.templates.noResults&&0===s.length?a("div",{className:u.sourceNoResults},c.templates.noResults({components:d,createElement:a,Fragment:l,source:c,state:f})):a("ul",r({className:u.list},p.getListProps(i({state:f,props:n.getListProps({})},o))),s.map((function(e){var t=n.getItemProps({item:e,source:c});return a("li",r({key:t.id,className:u.item},p.getItemProps(i({state:f,props:t},o))),c.templates.item({components:d,createElement:a,Fragment:l,item:e,state:f}))}))),c.templates.footer&&a("div",{className:u.sourceFooter},c.templates.footer({components:d,createElement:a,Fragment:l,items:s,source:c,state:f})))})),v=a(l,null,a("div",{className:u.panelLayout},m),a("div",{className:"aa-GradientBottom"})),h=m.reduce((function(e,t){return e[t.props["data-autocomplete-source-id"]]=t,e}),{});e(i({children:v,state:f,sections:m,elements:h,createElement:a,Fragment:l,components:d},o),c.panel)}else s.contains(c.panel)&&s.removeChild(c.panel)}(n,t)}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c(),y.current=je(O.value.renderer,O.value.core,{initialState:j.current},e),h(),p(),P.value.refresh().then((function(){A(j.current)}))}function C(e){requestAnimationFrame((function(){var t=O.value.core.environment.document.body.contains(I.value.detachedOverlay);e!==t&&(e?(O.value.core.environment.document.body.appendChild(I.value.detachedOverlay),O.value.core.environment.document.body.classList.add("aa-Detached"),I.value.input.focus()):(O.value.core.environment.document.body.removeChild(I.value.detachedOverlay),O.value.core.environment.document.body.classList.remove("aa-Detached"),P.value.setQuery(""),P.value.refresh()))}))}return a((function(){var e=P.value.getEnvironmentProps({formElement:I.value.form,panelElement:I.value.panel,inputElement:I.value.input});return Ae(O.value.core.environment,e),function(){Ae(O.value.core.environment,Object.keys(e).reduce((function(e,t){return i(i({},e),{},n({},t,void 0))}),{}))}})),a((function(){var e=_.value?O.value.core.environment.document.body:O.value.renderer.panelContainer,t=_.value?I.value.detachedOverlay:I.value.panel;return _.value&&j.current.isOpen&&C(!0),A(j.current),function(){e.contains(t)&&e.removeChild(t)}})),a((function(){var e=O.value.renderer.container;return e.appendChild(I.value.root),function(){e.removeChild(I.value.root)}})),a((function(){var e=s((function(e){A(e.state)}),0);return b.current=function(t){var n=t.state,r=t.prevState;(_.value&&r.isOpen!==n.isOpen&&C(n.isOpen),_.value||!n.isOpen||r.isOpen||E(),n.query!==r.query)&&O.value.core.environment.document.querySelectorAll(".aa-Panel--scrollable").forEach((function(e){0!==e.scrollTop&&(e.scrollTop=0)}));e({state:n})},function(){b.current=void 0}})),a((function(){var e=s((function(){var e=_.value;_.value=O.value.core.environment.matchMedia(O.value.renderer.detachedMediaQuery).matches,e!==_.value?D({}):requestAnimationFrame(E)}),20);return O.value.core.environment.addEventListener("resize",e),function(){O.value.core.environment.removeEventListener("resize",e)}})),a((function(){if(!_.value)return function(){};function e(e){I.value.detachedContainer.classList.toggle("aa-DetachedContainer--modal",e)}function t(t){e(t.matches)}var n=O.value.core.environment.matchMedia(getComputedStyle(O.value.core.environment.document.documentElement).getPropertyValue("--aa-detached-modal-media-query"));e(n.matches);var r=Boolean(n.addEventListener);return r?n.addEventListener("change",t):n.addListener(t),function(){r?n.removeEventListener("change",t):n.removeListener(t)}})),a((function(){return requestAnimationFrame(E),function(){}})),i(i({},w),{},{update:D,destroy:function(){c()}})},e.getAlgoliaFacets=function(e){var t=Qt({transformResponse:function(e){return e.facetHits}}),n=e.queries.map((function(e){return i(i({},e),{},{type:"facet"})}));return t(i(i({},e),{},{queries:n}))},e.getAlgoliaResults=$t,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=index.production.js.map
