

<% if (item.href) { %>
  <li class="nav-item<%- item.text === undefined ? ' compact' : '' %>">
    <a class="nav-link" href="<%- item.href %>"<%= item.rel ? ` rel="${item.rel}"` : "" %><%= item.target ? ` target="${item.target}"` : "" %>> <% partial('navicon.ejs', { item }) %><span class="menu-text"><%- item.text %></span></a>
  </li>  
<% } else if (item.menu) { %>
  <li class="nav-item dropdown <%- item.text === undefined ? ' compact' : '' %>">
    <a class="nav-link dropdown-toggle" href="#" id="<%- item.id %>" role="button" data-bs-toggle="dropdown" aria-expanded="false" <%= item.rel ? ` rel="${item.rel}"` : "" %><%= item.target ? ` target="${item.target}"` : "" %>>
      <% partial('navicon.ejs', { item }) %> <span class="menu-text"><%- item.text %></span>
    </a>
    <ul class="dropdown-menu<%- align === 'end' ? ' dropdown-menu-end' : ''%>" aria-labelledby="<%- item.id %>">    
      <% for(const menuItem of item.menu) { %>
        <% partial('navitem-dropdown.ejs', { item: menuItem }) %>
      <% } %>
    </ul>
  </li>
<% } else if (item.text) { %>
  <% if (item.text.match(/^\-+$/)) { %>
    <li><hr class="dropdown-divider"></li>
  <% } else { %>
    <li class="dropdown-header"><% partial('navicon.ejs', { item }) %> <span class="menu-text"><%- item.text %></span></li>
  <% } %>
<% } %>

