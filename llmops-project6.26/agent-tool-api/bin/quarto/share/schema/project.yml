- name: project
  description: Project configuration.
  schema:
    object:
      closed: true
      properties:
        title:
          schema: string
        type:
          string:
            completions: [default, website, book, manuscript]
            description: "Project type (`default`, `website`, `book`, or `manuscript`)"
        render:
          arrayOf: path
          description: "Files to render (defaults to all files)"
        execute-dir:
          enum: [file, project]
          description:
            short: "Working directory for computations"
            long: |
              Control the working directory for computations. 

              - `file`: Use the directory of the file that is currently executing.
              - `project`: Use the root directory of the project.
        output-dir:
          path:
            description: "Output directory"
        lib-dir:
          path:
            description: "HTML library (JS/CSS/etc.) directory"
        resources:
          maybeArrayOf:
            schema: path
            description: "Additional file resources to be copied to output directory"
        preview:
          description: Options for `quarto preview`
          schema:
            ref: project-preview
        pre-render:
          description: "Scripts to run as a pre-render step"
          schema:
            maybeArrayOf: string
        post-render:
          description: "Scripts to run as a post-render step"
          schema:
            maybeArrayOf: string
        detect:
          description: "Array of paths used to detect the project type within a directory"
          schema:
            arrayOf:
              arrayOf: string
          hidden: true

- name: website
  description: Website configuration.
  schema:
    ref: base-website

- name: book
  description: Book configuration.
  schema:
    object:
      super:
        - resolveRef: book-schema
        - resolveRef: csl-item-shared

- name: manuscript
  description: Manuscript configuration
  schema:
    ref: manuscript-schema

- name: type
  hidden: true
  schema:
    enum: ["cd93424f-d5ba-4e95-91c6-1890eab59fc7"]
  errorMessage: "type key not supported at project type-level. Use `project: type: ...` instead."
  description: "internal-schema-hack"
  # this is a hack so that we "support" an empty schema.
  # The value in the enum should never appear in actual documents.
  #
  # In order to provide a good error message, we need an error
  # to be reported with instancePath ["type"].
  # This is a hacky way to do it.
  #
  # In general, full json schema would allow negative assertions,
  # but that makes our error localization heuristics worse. So we hack.
