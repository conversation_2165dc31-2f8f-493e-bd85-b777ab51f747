- name: editor
  schema:
    anyOf:
      - enum: [source, visual]
      - object:
          hidden: true # don't complete through a single-key object
          properties:
            mode:
              enum: [source, visual]
              description: "Default editing mode for document"
            markdown:
              object:
                properties:
                  wrap:
                    anyOf:
                      - enum: [sentence, none]
                      - number
                    description: "A column number (e.g. 72), `sentence`, or `none`"
                  canonical:
                    boolean:
                      description: "Write standard visual editor markdown from source mode."
                  references:
                    object:
                      properties:
                        location:
                          schema:
                            enum: [block, section, document]
                            description: "Location to write references (`block`, `section`, or `document`)"
                        links:
                          boolean:
                            description: "Write markdown links as references rather than inline."
                        prefix:
                          string:
                            description: "Unique prefix for references (`none` to prevent automatic prefixes)"
                    description: "Reference writing options for visual editor"
              description: "Markdown writing options for visual editor"
            render-on-save:
              tags:
                engine: [jupyter]
              schema: boolean
              description: |
                Automatically re-render for preview whenever document is saved (note that this requires a preview
                for the saved document be already running). This option currently works only within VS Code.
  description: Visual editor configuration

- name: zotero
  schema:
    anyOf:
      - boolean
      - maybeArrayOf: string
  description: |
    Enable (`true`) or disable (`false`) Zotero for a document. Alternatively, provide a list of one or
    more Zotero group libraries to use with the document.
