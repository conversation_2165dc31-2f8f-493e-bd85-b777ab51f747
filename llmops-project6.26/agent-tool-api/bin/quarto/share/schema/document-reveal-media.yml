- name: preview-links
  tags:
    formats: [revealjs]
  schema:
    anyOf:
      - enum: [auto]
      - boolean
  default: auto
  description:
    short: "Open links in an iframe preview overlay (`true`, `false`, or `auto`)"
    long: |
      Open links in an iframe preview overlay.

      - `true`: Open links in iframe preview overlay
      - `false`: Do not open links in iframe preview overlay
      - `auto` (default): Open links in iframe preview overlay, in fullscreen mode.

- name: auto-play-media
  tags:
    formats: [revealjs]
  schema:
    enum: [null, true, false]
  default: null
  description: |
    Autoplay embedded media (`null`, `true`, or `false`). Default is `null` (only when `autoplay` 
    attribute is specified)

- name: preload-iframes
  tags:
    formats: [revealjs]
  schema:
    enum: [null, true, false]
  default: null
  description:
    short: "Global override for preloading lazy-loaded iframes (`null`, `true`, or `false`)."
    long: |
      Global override for preloading lazy-loaded iframes

      - `null`:   Iframes with data-src AND data-preload will be loaded when within
        the `viewDistance`, iframes with only data-src will be loaded when visible
      - `true`:   All iframes with data-src will be loaded when within the viewDistance
      - `false`:  All iframes with data-src will be loaded only when visible

- name: view-distance
  tags:
    formats: [revealjs]
  schema: number
  default: 3
  description: "Number of slides away from the current slide to pre-load resources for"

- name: mobile-view-distance
  tags:
    formats: [revealjs]
  schema: number
  default: 2
  description: |
    Number of slides away from the current slide to pre-load resources for (on mobile devices).

- name: parallax-background-image
  tags:
    formats: [revealjs]
  schema: path
  description: "Parallax background image"

- name: parallax-background-size
  tags:
    formats: [revealjs]
  schema: string
  description: "Parallax background size (e.g. '2100px 900px')"

- name: parallax-background-horizontal
  tags:
    formats: [revealjs]
  schema: number
  default: 200
  description: "Number of pixels to move the parallax background horizontally per slide."

- name: parallax-background-vertical
  tags:
    formats: [revealjs]
  schema: number
  default: 50
  description: "Number of pixels to move the parallax background vertically per slide."
