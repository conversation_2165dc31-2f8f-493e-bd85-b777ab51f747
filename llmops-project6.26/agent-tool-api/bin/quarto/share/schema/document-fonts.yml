- name: mainfont
  schema: string
  tags:
    formats: [$html-doc, context, $pdf-all, typst]
  description:
    short: Sets the main font for the document.
    long: |
      For HTML output, sets the CSS `font-family` on the HTML element.

      For LaTeX output, the main font family for use with `xelatex` or 
      `lualatex`. Takes the name of any system font, using the
      [`fontspec`](https://ctan.org/pkg/fontspec) package. 

      For ConTeXt output, the main font family. Use the name of any 
      system font. See [ConTeXt Fonts](https://wiki.contextgarden.net/Fonts) for more
      information.

- name: monofont
  schema: string
  tags:
    formats: [$html-doc, context, $pdf-all]
  description:
    short: Sets the font used for when displaying code.
    long: |
      For HTML output, sets the CSS font-family property on code elements.

      For PowerPoint output, sets the font used for code.

      For LaTeX output, the monospace font family for use with `xelatex` or 
      `lualatex`: take the name of any system font, using the
      [`fontspec`](https://ctan.org/pkg/fontspec) package.  

      For ConTeXt output, the monspace font family. Use the name of any 
      system font. See [ConTeXt Fonts](https://wiki.contextgarden.net/Fonts) for more
      information.

- name: fontsize
  schema: string
  tags:
    formats: [$html-doc, context, $pdf-all, typst]
  description:
    short: Sets the main font size for the document.
    long: |
      For HTML output, sets the base CSS `font-size` property.

      For LaTeX and ConTeXt output, sets the font size for the document body text.

- name: fontenc
  schema: string
  tags:
    formats: [$pdf-all]
  default: T1
  description:
    short: Allows font encoding to be specified through `fontenc` package.
    long: |
      Allows font encoding to be specified through [`fontenc`](https://www.ctan.org/pkg/fontenc) package.

      See [LaTeX Font Encodings Guide](https://ctan.org/pkg/encguide) for addition information on font encoding.

- name: fontfamily
  schema: string
  tags:
    formats: [$pdf-all, ms]
  default: "Latin Modern"
  description:
    short: Font package to use when compiling a PDF with the `pdflatex` `pdf-engine`.
    long: |
      Font package to use when compiling a PDf with the `pdflatex` `pdf-engine`. 

      See [The LaTeX Font Catalogue](https://tug.org/FontCatalogue/) for a 
      summary of font options available.

      For groff (`ms`) files, the font family for example, `T` or `P`.

- name: fontfamilyoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: Options for the package used as `fontfamily`.
    long: |
      Options for the package used as `fontfamily`.

      For example, to use the Libertine font with proportional lowercase
      (old-style) figures through the [`libertinus`](https://ctan.org/pkg/libertinus) package:

      ```yaml
      fontfamily: libertinus
      fontfamilyoptions:
        - osf
        - p
      ```

- name: sansfont
  schema: string
  tags:
    formats: [$pdf-all]
  description:
    short: The sans serif font family for use with `xelatex` or `lualatex`.
    long: |
      The sans serif font family for use with `xelatex` or 
      `lualatex`. Takes the name of any system font, using the
      [`fontspec`](https://ctan.org/pkg/fontspec) package.

- name: mathfont
  schema: string
  tags:
    formats: [$pdf-all]
  description:
    short: The math font family for use with `xelatex` or `lualatex`.
    long: |
      The math font family for use with `xelatex` or 
      `lualatex`. Takes the name of any system font, using the
      [`fontspec`](https://ctan.org/pkg/fontspec) package.

- name: CJKmainfont
  schema: string
  tags:
    formats: [$pdf-all]
  description:
    short: The CJK main font family for use with `xelatex` or `lualatex`.
    long: |
      The CJK main font family for use with `xelatex` or 
      `lualatex` using the [`xecjk`](https://ctan.org/pkg/xecjk) package.

# dependencies upon other key values (e.g. pdf-engine: xelatex)
- name: mainfontoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: The main font options for use with `xelatex` or `lualatex`.
    long: |
      The main font options for use with `xelatex` or `lualatex` allowing
      any options available through [`fontspec`](https://ctan.org/pkg/fontspec).

      For example, to use the [TeX Gyre](http://www.gust.org.pl/projects/e-foundry/tex-gyre) 
      version of Palatino with lowercase figures:

      ```yaml
      mainfont: TeX Gyre Pagella
      mainfontoptions:
        - Numbers=Lowercase
        - Numbers=Proportional    
      ```

- name: sansfontoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: The sans serif font options for use with `xelatex` or `lualatex`.
    long: |
      The sans serif font options for use with `xelatex` or `lualatex` allowing
      any options available through [`fontspec`](https://ctan.org/pkg/fontspec).

- name: monofontoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: The monospace font options for use with `xelatex` or `lualatex`.
    long: |
      The monospace font options for use with `xelatex` or `lualatex` allowing
      any options available through [`fontspec`](https://ctan.org/pkg/fontspec).

- name: mathfontoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: The math font options for use with `xelatex` or `lualatex`.
    long: |
      The math font options for use with `xelatex` or `lualatex` allowing
      any options available through [`fontspec`](https://ctan.org/pkg/fontspec).

- name: font-paths
  schema:
    maybeArrayOf: string
  tags:
    formats: [typst]
  description:
    short: "Adds additional directories to search for fonts when compiling with Typst."
    long: |
      Locally, Typst uses installed system fonts. In addition, some custom path 
      can be specified to add directories that should be scanned for fonts.
      Setting this configuration will take precedence over any path set in TYPST_FONT_PATHS environment variable.

- name: CJKoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: The CJK font options for use with `xelatex` or `lualatex`.
    long: |
      The CJK font options for use with `xelatex` or `lualatex` allowing
      any options available through [`fontspec`](https://ctan.org/pkg/fontspec).

- name: microtypeoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: Options to pass to the microtype package.
    long: Options to pass to the [microtype](https://ctan.org/pkg/microtype) package.

- name: pointsize
  schema: string
  tags:
    formats: [ms]
  description: The point size, for example, `10p`.

- name: lineheight
  schema: string
  tags:
    formats: [ms]
  description: The line height, for example, `12p`.

- name: linestretch
  schema:
    anyOf:
      - string
      - number
  tags:
    formats: [$html-doc, context, $pdf-all]
  description:
    short: Sets the line height or spacing for text in the document.
    long: |
      For HTML output sets the CSS `line-height` property on the html 
      element, which is preferred to be unitless.

      For LaTeX output, adjusts line spacing using the 
      [setspace](https://ctan.org/pkg/setspace) package, e.g. 1.25, 1.5.

- name: interlinespace
  schema:
    maybeArrayOf: string
  tags:
    formats: [context]
  description: Adjusts line spacing using the `\setupinterlinespace` command.

- name: linkstyle
  schema:
    string:
      completions:
        - normal
        - bold
        - slanted
        - boldslanted
        - type
        - cap
        - small
  tags:
    formats: [context]
  description: The typeface style for links in the document.

- name: whitespace
  schema: string
  tags:
    formats: [context]
  description:
    short: Set the spacing between paragraphs, for example `none`, `small.
    long: |
      Set the spacing between paragraphs, for example `none`, `small` 
      using the [`setupwhitespace`](https://wiki.contextgarden.net/Command/setupwhitespace) 
      command.
