- name: code-copy
  schema:
    anyOf:
      - enum: [hover]
      - boolean
  tags:
    formats: [$html-all]
  default: "hover"
  description:
    short: Enable a code copy icon for code blocks.
    long: |
      Enable a code copy icon for code blocks. 

      - `true`: Always show the icon
      - `false`: Never show the icon
      - `hover` (default): Show the icon when the mouse hovers over the code block

- name: code-link
  schema: boolean
  tags:
    engine: knitr
    formats: [$html-files]
  default: false
  description:
    short: |
      Enables hyper-linking of functions within code blocks 
      to their online documentation.
    long: |
      Enables hyper-linking of functions within code blocks 
      to their online documentation.

      Code linking is currently implemented only for the knitr engine 
      (via the [downlit](https://downlit.r-lib.org/) package). 
      A limitation of downlit currently prevents code linking 
      if `code-line-numbers` is also `true`.

- name: code-annotations
  schema:
    anyOf:
      - boolean
      - enum: [hover, select, below, none]
  default: below
  description:
    short: The style to use when displaying code annotations
    long: |
      The style to use when displaying code annotations. Set this value
      to false to hide code annotations.

- name: code-tools
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - boolean
      - object:
          closed: true
          properties:
            source:
              anyOf:
                - boolean
                - string
            toggle: boolean
            caption: string
  default: false
  description:
    short: "Include a code tools menu (for hiding and showing code)."
    long: |
      Include a code tools menu (for hiding and showing code).
      Use `true` or `false` to enable or disable the standard code 
      tools menu. Specify sub-properties `source`, `toggle`, and
      `caption` to customize the behavior and appearance of code tools.
- name: code-block-border-left
  tags:
    formats: [$html-doc, $pdf-all]
  schema:
    anyOf:
      - string
      - boolean
  description:
    short: "Show a thick left border on code blocks."
    long: |
      Specifies to apply a left border on code blocks. Provide a hex color to specify that the border is
      enabled as well as the color of the border.
- name: code-block-bg
  tags:
    formats: [$html-doc, $pdf-all]
  schema:
    anyOf:
      - string
      - boolean
  description:
    short: "Show a background color for code blocks."
    long: |
      Specifies to apply a background color on code blocks. Provide a hex color to specify that the background color is
      enabled as well as the color of the background.
- name: highlight-style
  tags:
    formats: [$html-all, docx, ms, $pdf-all]
  schema:
    anyOf:
      - object:
          closed: true
          properties:
            light: path
            dark: path
      - string:
          completions:
            - pygments
            - tango
            - espresso
            - zenburn
            - kate
            - monochrome
            - breezedark
            - haddock
            - arrow
            - atom-one
            - ayu
            - ayu-mirage
            - breeze
            - dracula
            - github
            - gruvbox
            - mokokai
            - nord
            - oblivion
            - printing
            - radical
            - solarized
            - vim-dark
  description:
    short: Specifies the coloring style to be used in highlighted source code.
    long: |
      Specifies the coloring style to be used in highlighted source code.

      Instead of a *STYLE* name, a JSON file with extension
      ` .theme` may be supplied.  This will be parsed as a KDE
      syntax highlighting theme and (if valid) used as the
      highlighting style.

- name: syntax-definition
  tags:
    formats: [$html-all, docx, ms, $pdf-all]
  schema: path
  hidden: true
  description: "KDE language syntax definition file (XML)"

- name: syntax-definitions
  tags:
    formats: [$html-all, docx, ms, $pdf-all]
  schema:
    arrayOf: path
  description: "KDE language syntax definition files (XML)"

- name: listings
  tags:
    formats: [$pdf-all]
  schema: boolean
  description:
    short: "Use the listings package for LaTeX code blocks."
    long: |
      Use the `listings` package for LaTeX code blocks. The package
      does not support multi-byte encoding for source code. To handle UTF-8
      you would need to use a custom template. This issue is fully
      documented here: [Encoding issue with the listings package](https://en.wikibooks.org/wiki/LaTeX/Source_Code_Listings#Encoding_issue)

- name: indented-code-classes
  tags:
    formats: [$html-all, docx, ms, $pdf-all]
  schema:
    arrayOf: string
  description: "Specify classes to use for all indented code blocks"
