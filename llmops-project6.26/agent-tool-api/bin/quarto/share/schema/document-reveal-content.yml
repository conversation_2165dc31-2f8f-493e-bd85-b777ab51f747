- name: logo
  tags:
    formats: [revealjs]
  schema: path
  description: "Logo image (placed in bottom right corner of slides)"

- name: footer
  tags:
    formats: [revealjs]
  schema: string
  description:
    short: "Footer to include on all slides"
    long: |
      Footer to include on all slides. Can also be set per-slide by including a
      div with class `.footer` on the slide.

- name: scrollable
  tags:
    formats: [revealjs]
  schema: boolean
  default: false
  description:
    short: "Allow content that overflows slides vertically to scroll"
    long: |
      `true` to allow content that overflows slides vertically to scroll. This can also
      be set per-slide by including the `.scrollable` class on the slide title.

- name: smaller
  tags:
    formats: [revealjs]
  schema: boolean
  default: false
  description:
    short: "Use a smaller default font for slide content"
    long: |
      `true` to use a smaller default font for slide content. This can also
      be set per-slide by including the `.smaller` class on the slide title.

- name: output-location
  tags:
    formats: [revealjs]
  schema:
    enum: [default, fragment, slide, column, column-fragment]
  description:
    short: Location of output relative to the code that generated it (`default`, `fragment`, `slide`, `column`, or `column-location`)
    long: |
      Location of output relative to the code that generated it. The possible values are as follows:

      - `default`: Normal flow of the slide after the code
      - `fragment`: In a fragment (not visible until you advance)
      - `slide`: On a new slide after the curent one
      - `column`: In an adjacent column 
      - `column-fragment`:   In an adjacent column (not visible until you advance)

      Note that this option is supported only for the `revealjs` format.
