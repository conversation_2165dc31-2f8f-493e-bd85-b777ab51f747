- name: df-print
  schema:
    enum: [default, kable, tibble, paged]
  tags:
    engine: knitr
  default: kable
  description:
    short: |
      Method used to print tables in Knitr engine documents (`default`,
      `kable`, `tibble`, or `paged`). Uses `default` if not specified.
    long: |
      Method used to print tables in Knitr engine documents:

      -  `default`: Use the default S3 method for the data frame.
      -  `kable`: Markdown table using the `knitr::kable()` function.
      -  `tibble`: Plain text table using the `tibble` package.
      -  `paged`: HTML table with paging for row and column overflow.

      The default printing method is `kable`.
