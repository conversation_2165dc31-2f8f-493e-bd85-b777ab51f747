- name: keywords
  schema:
    maybeArrayOf: string
  tags:
    formats: [$asciidoc-all, $html-files, $pdf-all, context, odt, $office-all]
  description: "List of keywords to be included in the document metadata."

- name: subject
  schema: string
  tags:
    formats: [$pdf-all, $office-all, odt]
  description: "The document subject"

- name: description
  schema: string
  tags:
    formats: [odt, $office-all]
  description: "The document description. Some applications show this as `Comments` metadata."

- name: category
  schema: string
  tags:
    formats: [$office-all]
  description: "The document category."

- name: copyright
  schema:
    anyOf:
      - object:
          properties:
            year:
              maybeArrayOf:
                anyOf:
                  - string
                  - number
              description: The year for this copyright
            holder:
              maybeArrayOf:
                string:
                  description: The holder of the copyright.
            statement:
              maybeArrayOf:
                string:
                  description: The text to display for the license.
      - string
  tags:
    formats: [$html-doc, $jats-all]
  description: The copyright for this document, if any.
- name: license
  schema:
    maybeArrayOf:
      anyOf:
        - object:
            properties:
              type:
                string:
                  description: The type of the license.
              link:
                string:
                  description: A URL to the license.
              text:
                string:
                  description: The text to display for the license.
        - string
  tags:
    formats: [$html-doc, $jats-all]
  description:
    short: The License for this document, if any. (e.g. `CC BY`)
    long: |
      The license for this document, if any. 

      Creative Commons licenses `CC BY`, `CC BY-SA`, `CC BY-ND`, `CC BY-NC`, `CC BY-NC-SA`, and `CC BY-NC-ND` will automatically generate a license link
      in the document appendix. Other license text will be placed in the appendix verbatim.

- name: title-meta
  schema: string
  tags:
    formats: [$pdf-all]
  description: "Sets the title metadata for the document"

- name: pagetitle
  tags:
    formats: [$html-files]
  schema: string
  description: "Sets the title metadata for the document"

- name: title-prefix
  tags:
    formats: [$html-files]
  schema: string
  description: |
    Specify STRING as a prefix at the beginning of the title that appears in 
    the HTML header (but not in the title as it appears at the beginning of the body)

- name: description-meta
  schema: string
  tags:
    formats: [$html-files]
  description: "Sets the description metadata for the document"

- name: author-meta
  schema: string
  tags:
    formats: [$pdf-all, $html-files]
  description: "Sets the author metadata for the document"

- name: date-meta
  schema: string
  tags:
    formats: [$html-all, $pdf-all]
  description: "Sets the date metadata for the document"
