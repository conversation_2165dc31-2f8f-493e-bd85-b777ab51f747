- name: eval
  tags:
    contexts: [document-execute]
    execute-only: true
  schema: boolean
  default: true
  description:
    short: Evaluate code cells (if `false` just echos the code into output).
    long: |
      Evaluate code cells (if `false` just echos the code into output).

      - `true` (default): evaluate code cell
      - `false`: don't evaluate code cell
      - `[...]`: A list of positive or negative line numbers to selectively include or exclude lines 
        (explicit inclusion/excusion of lines is available only when using the knitr engine)

- name: echo
  tags:
    contexts: [document-execute]
    execute-only: true
  schema:
    anyOf:
      - boolean
      - enum: [fenced]
    errorDescription: "be `true`, `false`, or `fenced`"
  description:
    short: Include cell source code in rendered output.
    long: |
      Include cell source code in rendered output.

      - `true` (default in most formats): include source code in output
      - `false` (default in presentation formats like `beamer`, `revealjs`, and `pptx`): do not include source code in output
      - `fenced`: in addition to echoing, include the cell delimiter as part of the output.
      - `[...]`: A list of positive or negative line numbers to selectively include or exclude lines
        (explicit inclusion/excusion of lines is available only when using the knitr engine)

- name: code-fold
  tags:
    contexts: [document-code]
    formats: [$html-all]
  schema:
    anyOf:
      - boolean
      - enum: [show]
  default: false
  description:
    short: "Collapse code into an HTML `<details>` tag so the user can display it on-demand."
    long: |
      Collapse code into an HTML `<details>` tag so the user can display it on-demand.

      - `true`: collapse code
      - `false` (default): do not collapse code
      - `show`: use the `<details>` tag, but show the expanded code initially.

- name: code-summary
  tags:
    contexts: [document-code]
    formats: [$html-all]
  schema: string
  default: "Code"
  description: "Summary text to use for code blocks collapsed using `code-fold`"

- name: code-overflow
  tags:
    contexts: [document-code]
    formats: [$html-all]
  schema:
    enum: [scroll, wrap]
  default: scroll
  description:
    short: "Choose whether to `scroll` or `wrap` when code lines are too wide for their container."
    long: |
      Choose how to handle code overflow, when code lines are too wide for their container. One of:

      - `scroll`
      - `wrap`

- name: code-line-numbers
  tags:
    contexts: [document-code]
    formats: [$html-all, ms, $pdf-all]
  schema:
    anyOf:
      - boolean
      - string
    tags:
      doNotNarrowError: true
    errorDescription: "be `true`, `false`, or a string specifying the lines to highlight"
  default: false
  description:
    short: "Include line numbers in code block output (`true` or `false`)"
    long: |
      Include line numbers in code block output (`true` or `false`).

      For revealjs output only, you can also specify a string to highlight
      specific lines (and/or animate between sets of highlighted lines).

      * Sets of lines are denoted with commas:
        * `3,4,5`
        * `1,10,12`
      * Ranges can be denoted with dashes and combined with commas:
        * `1-3,5` 
        * `5-10,12,14`
      * Finally, animation steps are separated by `|`:
        * `1-3|1-3,5` first shows `1-3`, then `1-3,5`
        * `|5|5-10,12` first shows no numbering, then 5, then lines 5-10
          and 12

- name: lst-label
  schema: string
  description: "Unique label for code listing (used in cross references)"

- name: lst-cap
  schema: string
  description: "Caption for code listing"

- name: tidy
  tags:
    engine: knitr
  schema:
    anyOf:
      - boolean
      - enum: [styler, formatR]
  default: false
  description: "Whether to reformat R code."

- name: tidy-opts
  tags:
    engine: knitr
  schema:
    arrayOf: string
  description: "List of options to pass to `tidy` handler"

- name: collapse
  tags:
    engine: knitr
  schema: boolean
  default: false
  description: |
    Collapse all the source and output blocks from one code chunk into a single block

- name: prompt
  tags:
    engine: knitr
  schema: boolean
  default: false
  description:
    short: "Whether to add the prompt characters in R code."
    long: |
      Whether to add the prompt characters in R
      code. See `prompt` and `continue` on the help page `?base::options`. Note
      that adding prompts can make it difficult for readers to copy R code from
      the output, so `prompt: false` may be a better choice. This option may not
      work well when the `engine` is not `R`
      ([#1274](https://github.com/yihui/knitr/issues/1274)).

- name: highlight
  tags:
    engine: knitr
  schema: boolean
  default: false
  hidden: true
  description: "Whether to syntax highlight the source code"

- name: class-source
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Class name(s) for source code blocks"

- name: attr-source
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Attribute(s) for source code blocks"
