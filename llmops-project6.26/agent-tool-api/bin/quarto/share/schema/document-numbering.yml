- name: number-sections
  schema: boolean
  default: false
  description:
    short: "Number section headings"
    long: |
      Number section headings rendered output. By default, sections are not numbered.
      Sections with class `.unnumbered` will never be numbered, even if `number-sections`
      is specified.

- name: number-depth
  schema: number
  tags:
    formats: [$html-all, $pdf-all, docx]
  description:
    short: The depth to which sections should be numbered.
    long: |
      By default, all headings in your document create a 
      numbered section. You customize numbering depth using 
      the `number-depth` option. 

      For example, to only number sections immediately below 
      the chapter level, use this:

      ```yaml  
      number-depth: 1
      ```

- name: secnumdepth
  schema: number
  hidden: true
  tags:
    formats: [$pdf-all]
  description: The numbering depth for sections. (Use `number-depth` instead).

- name: number-offset
  schema:
    maybeArrayOf: number
  default: [0, 0, 0, 0, 0, 0]
  description:
    short: Offset for section headings in output (offsets are 0 by default)
    long: |
      Offset for section headings in output (offsets are 0 by default)
      The first number is added to the section number for
      top-level headings, the second for second-level headings, and so on.
      So, for example, if you want the first top-level heading in your
      document to be numbered "6", specify `number-offset: 5`. If your
      document starts with a level-2 heading which you want to be numbered
      "1.5", specify `number-offset: [1,4]`. Implies `number-sections`

- name: section-numbering
  tags:
    formats: [typst]
  schema: string
  description: "Schema to use for numbering sections, e.g. `1.A.1`"

- name: shift-heading-level-by
  schema: number
  description:
    short: |
      Shift heading levels by a positive or negative integer. For example, with 
      `shift-heading-level-by: -1`, level 2 headings become level 1 headings.
    long: |
      Shift heading levels by a positive or negative integer.
      For example, with `shift-heading-level-by: -1`, level 2
      headings become level 1 headings, and level 3 headings
      become level 2 headings.  Headings cannot have a level
      less than 1, so a heading that would be shifted below level 1
      becomes a regular paragraph.  Exception: with a shift of -N,
      a level-N heading at the beginning of the document
      replaces the metadata title.

- name: pagenumbering
  schema:
    maybeArrayOf: string
  tags:
    formats: [context]
  description:
    short: Sets the page numbering style and location for the document.
    long: |
      Sets the page numbering style and location for the document using the
      `\setuppagenumbering` command. 

      See [ConTeXt Page Numbering](https://wiki.contextgarden.net/Command/setuppagenumbering) 
      for additional information.

- name: top-level-division
  tags:
    formats: [$pdf-all, context, $docbook-all, tei]
  schema:
    enum: [default, section, chapter, part]
  description:
    short: |
      Treat top-level headings as the given division type (`default`, `section`, `chapter`, or `part`). The hierarchy
      order is part, chapter, then section; all headings are shifted such 
      that the top-level heading becomes the specified type.
    long: |
      Treat top-level headings as the given division type (`default`, `section`, `chapter`, or `part`). The hierarchy
      order is part, chapter, then section; all headings are shifted such 
      that the top-level heading becomes the specified type.  

      The default behavior is to determine the
      best division type via heuristics: unless other conditions
      apply, `section` is chosen. When the `documentclass`
      variable is set to `report`, `book`, or `memoir` (unless the
      `article` option is specified), `chapter` is implied as the
      setting for this option. If `beamer` is the output format,
      specifying either `chapter` or `part` will cause top-level
      headings to become `\part{..}`, while second-level headings
      remain as their default type.
