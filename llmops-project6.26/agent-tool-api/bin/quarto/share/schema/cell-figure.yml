- name: fig-width
  tags:
    engine: knitr
  schema: number
  description: "Default width for figures"

- name: fig-height
  tags:
    engine: knitr
  schema: number
  description: "Default height for figures"

- name: fig-cap
  schema:
    maybeArrayOf: string
  description: Figure caption

- name: fig-subcap
  schema:
    anyOf:
      - enum: [true]
      - maybeArrayOf: string
  description: Figure subcaptions

- name: fig-link
  schema:
    maybeArrayOf: string
  description: Hyperlink target for the figure

- name: fig-align
  tags:
    contexts: [document-figures]
    formats: [docx, rtf, $odt-all, $pdf-all, $html-all]
  schema:
    maybeArrayOf:
      enum: [default, left, right, center]
  default: default
  description: Figure horizontal alignment (`default`, `left`, `right`, or `center`)

- name: fig-alt
  tags:
    formats: [$html-all]
  schema:
    maybeArrayOf: string
  description: |
    Alternative text to be used in the `alt` attribute of HTML images.

- name: fig-env
  tags:
    formats: [$pdf-all]
    contexts: [document-figures]
  schema:
    maybeArrayOf: string
  description: LaTeX environment for figure output

- name: fig-pos
  tags:
    formats: [$pdf-all]
    contexts: [document-figures]
  schema:
    anyOf:
      - maybeArrayOf: string
      - enum: [false]
  description:
    short: LaTeX figure position arrangement to be used in `\begin{figure}[]`.
    long: |
      LaTeX figure position arrangement to be used in `\begin{figure}[]`.

      Computational figure output that is accompanied by the code 
      that produced it is given a default value of `fig-pos="H"` (so 
      that the code and figure are not inordinately separated).

      If `fig-pos` is `false`, then we don't use any figure position
      specifier, which is sometimes necessary with custom figure
      environments (such as `sidewaysfigure`).

- name: fig-scap
  tags:
    formats: [$pdf-all]
  schema:
    maybeArrayOf: string
  description:
    short: A short caption (only used in LaTeX output)
    long: |
      A short caption (only used in LaTeX output). A short caption is inserted in `\caption[]`, 
      and usually displayed in the “List of Figures” of a PDF document.

- name: fig-format
  tags:
    engine: knitr
  schema:
    enum: [retina, png, jpeg, svg, pdf]
  description: "Default output format for figures (`retina`, `png`, `jpeg`, `svg`, or `pdf`)"

- name: fig-dpi
  tags:
    engine: knitr
  schema: number
  description: "Default DPI for figures"

- name: fig-asp
  tags:
    engine: knitr
  schema: number
  description: |
    The aspect ratio of the plot, i.e., the ratio of height/width. When `fig-asp` is specified, the height of a plot 
    (the option `fig-height`) is calculated from `fig-width * fig-asp`.

- name: out-width
  tags:
    engine: knitr
  schema:
    anyOf:
      - string
      - schema:
          "null":
            completions: []
  description:
    short: "Width of plot in the output document"
    long: |
      Width of the plot in the output document, which can be different from its physical `fig-width`,
      i.e., plots can be scaled in the output document.
      When used without a unit, the unit is assumed to be pixels. However, any of the following unit 
      identifiers can be used: px, cm, mm, in, inch and %, for example, `3in`, `8cm`, `300px` or `50%`.

- name: out-height
  tags:
    engine: knitr
  schema: string
  description:
    short: "Height of plot in the output document"
    long: |
      Height of the plot in the output document, which can be different from its physical `fig-height`, 
      i.e., plots can be scaled in the output document.
      Depending on the output format, this option can take special values.
      For example, for LaTeX output, it can be `3in`, or `8cm`;
      for HTML, it can be `300px`.

- name: fig-keep
  tags:
    engine: knitr
  schema:
    anyOf:
      - enum: [high, none, all, first, last]
      - maybeArrayOf: number
  default: high
  description:
    short: "How plots in chunks should be kept."
    long: |
      How plots in chunks should be kept. Possible values are as follows:

      -   `high`: Only keep high-level plots (merge low-level changes into
          high-level plots).
      -   `none`: Discard all plots.
      -   `all`: Keep all plots (low-level plot changes may produce new plots).
      -   `first`: Only keep the first plot.
      -   `last`: Only keep the last plot.
      -   A numeric vector: In this case, the values are indices of (low-level) plots
          to keep.

- name: fig-show
  tags:
    engine: knitr
  schema:
    enum: [asis, hold, animate, hide]
  default: asis
  description:
    short: "How to show/arrange the plots"
    long: |
      How to show/arrange the plots. Possible values are as follows:

      -   `asis`: Show plots exactly in places where they were generated (as if
          the code were run in an R terminal).
      -   `hold`: Hold all plots and output them at the end of a code chunk.
      -   `animate`: Concatenate all plots into an animation if there are multiple
          plots in a chunk.
      -   `hide`: Generate plot files but hide them in the output document.

- name: out-extra
  tags:
    engine: knitr
  schema: string
  description: "Additional raw LaTeX or HTML options to be applied to figures"

- name: external
  tags:
    engine: knitr
    formats: [$pdf-all]
  schema: boolean
  default: true
  description: "Externalize tikz graphics (pre-compile to PDF)"

- name: sanitize
  tags:
    engine: knitr
    formats: [$pdf-all]
  schema: boolean
  default: false
  description: "sanitize tikz graphics (escape special LaTeX characters)."

- name: interval
  tags:
    engine: knitr
  schema: number
  default: 1
  description: "Time interval (number of seconds) between animation frames."

- name: aniopts
  tags:
    engine: knitr
  schema: string
  default: "controls, loop"
  description:
    short: "Extra options for animations"
    long: |
      Extra options for animations; see the documentation of the LaTeX [**animate**
      package.](http://ctan.org/pkg/animate)

- name: animation-hook
  tags:
    engine: knitr
  schema:
    string:
      completions: [ffmpeg, gifski]
  default: ffmpeg
  description:
    short: "Hook function to create animations in HTML output"
    long: |
      Hook function to create animations in HTML output. 

      The default hook (`ffmpeg`) uses FFmpeg to convert images to a WebM video.

      Another hook function is `gifski` based on the
      [**gifski**](https://cran.r-project.org/package=gifski) package to
      create GIF animations.
