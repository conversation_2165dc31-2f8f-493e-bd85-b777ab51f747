- name: output
  tags:
    contexts: [document-execute]
    execute-only: true
  schema:
    anyOf:
      - boolean
      - enum: [asis]
      # these are here for compatibility with rmarkdown output: yaml
      - string
      - object
  description:
    short: |
      Include the results of executing the code in the output (specify `asis` to
      treat output as raw markdown with no enclosing containers).
    long: |
      Include the results of executing the code in the output. Possible values:

      - `true`: Include results.
      - `false`: Do not include results.
      - `asis`: Treat output as raw markdown with no enclosing containers.

- name: warning
  tags:
    contexts: [document-execute]
    execute-only: true
  schema: boolean
  description: Include warnings in rendered output.

- name: error
  tags:
    contexts: [document-execute]
    execute-only: true
  schema: boolean
  default: false
  description: |
    Include errors in the output (note that this implies that errors executing code
    will not halt processing of the document).

- name: include
  tags:
    contexts: [document-execute]
    execute-only: true
  schema: boolean
  default: false
  description: |
    Catch all for preventing any output (code or results) from being included in output.

- name: panel
  schema:
    enum: [tabset, input, sidebar, fill, center]
  description: "Panel type for cell output (`tabset`, `input`, `sidebar`, `fill`, `center`)"

- name: output-location
  tags:
    formats: [revealjs]
  schema:
    enum: [default, fragment, slide, column, column-fragment]
  description:
    short: Location of output relative to the code that generated it (`default`, `fragment`, `slide`, `column`, or `column-location`)
    long: |
      Location of output relative to the code that generated it. The possible values are as follows:

      - `default`: Normal flow of the slide after the code
      - `fragment`: In a fragment (not visible until you advance)
      - `slide`: On a new slide after the curent one
      - `column`: In an adjacent column 
      - `column-fragment`:   In an adjacent column (not visible until you advance)

      Note that this option is supported only for the `revealjs` format.

- name: message
  tags:
    engine: knitr
  schema: boolean
  default: true
  description: Include messages in rendered output.

- name: results
  tags:
    engine: knitr
  schema:
    enum: [markup, asis, hold, hide, false]
  default: markup
  description:
    short: "How to display text results"
    long: |
      How to display text results. Note that this option only applies to normal text output (not warnings,
      messages, or errors). The possible values are as follows:

      - `markup`: Mark up text output with the appropriate environments
        depending on the output format. For example, if the text
        output is a character string `"[1] 1 2 3"`, the actual output that
        **knitr** produces will be:

        ```` md
        ```
        [1] 1 2 3
        ```
        ````

        In this case, `results: markup` means to put the text output in fenced
        code blocks (```` ``` ````).

      - `asis`: Write text output as-is, i.e., write the raw text results
        directly into the output document without any markups.

        ```` md
        ```{r}
        #| results: asis
        cat("I'm raw **Markdown** content.\n")
        ```
        ````

      - `hold`: Hold all pieces of text output in a chunk and flush them to the
        end of the chunk.

      - `hide` (or `false`): Hide text output.

- name: comment
  tags:
    engine: knitr
  schema: string
  default: "##"
  description:
    short: "Prefix to be added before each line of text output."
    long: |
      Prefix to be added before each line of text output.
      By default, the text output is commented out by `##`, so if
      readers want to copy and run the source code from the output document, they
      can select and copy everything from the chunk, since the text output is
      masked in comments (and will be ignored when running the copied text). Set
      `comment: ''` to remove the default `##`.

- name: class-output
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Class name(s) for text/console output"

- name: attr-output
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Attribute(s) for text/console output"

- name: class-warning
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Class name(s) for warning output"

- name: attr-warning
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Attribute(s) for warning output"

- name: class-message
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Class name(s) for message output"

- name: attr-message
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Attribute(s) for message output"

- name: class-error
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Class name(s) for error output"

- name: attr-error
  tags:
    engine: knitr
  schema:
    maybeArrayOf: string
  description: "Attribute(s) for error output"
