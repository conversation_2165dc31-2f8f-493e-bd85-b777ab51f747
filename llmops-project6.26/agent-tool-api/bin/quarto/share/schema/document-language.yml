- name: lang
  schema: string
  description:
    short: Identifies the main language of the document (e.g. `en` or `en-GB`).
    long: |
      Identifies the main language of the document using IETF language tags 
      (following the [BCP 47](https://www.rfc-editor.org/info/bcp47) standard), 
      such as `en` or `en-GB`. The [Language subtag lookup](https://r12a.github.io/app-subtags/) 
      tool can look up or verify these tags. 

      This affects most formats, and controls hyphenation 
      in PDF output when using LaTeX (through [`babel`](https://ctan.org/pkg/babel) 
      and [`polyglossia`](https://ctan.org/pkg/polyglossia)) or ConTeXt.

- name: language
  schema:
    anyOf:
      - path
      - object
  description: YAML file containing custom language translations

- name: dir
  schema:
    enum: [rtl, ltr]
  description:
    short: The base script direction for the document (`rtl` or `ltr`).
    long: |
      The base script direction for the document (`rtl` or `ltr`).

      For bidirectional documents, native pandoc `span`s and
      `div`s with the `dir` attribute can
      be used to override the base direction in some output
      formats.  This may not always be necessary if the final
      renderer (e.g. the browser, when generating HTML) supports
      the [Unicode Bidirectional Algorithm].

      When using LaTeX for bidirectional documents, only the
      `xelatex` engine is fully supported (use
      `--pdf-engine=xelatex`).
