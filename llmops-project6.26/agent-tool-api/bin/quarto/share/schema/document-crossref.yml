- name: crossref
  description: Configuration for crossref labels and prefixes.
  schema:
    anyOf:
      - enum: [false]
      - object:
          closed: true
          properties:
            custom:
              arrayOf:
                object:
                  description: A custom cross reference type.
                  closed: true
                  required: ["kind", "reference-prefix", "key"]
                  properties:
                    kind:
                      enum: [float]
                      description: The kind of cross reference (currently only "float" is supported).
                    reference-prefix:
                      string:
                        description: The prefix used in rendered references when referencing this type.
                    caption-prefix:
                      string:
                        description: The prefix used in rendered captions when referencing this type. If omitted, the field `reference-prefix` is used.
                    space-before-numbering:
                      default: true
                      boolean:
                        description: If false, use no space between crossref prefixes and numbering.
                    key:
                      string:
                        description: The key used to prefix reference labels of this type, such as "fig", "tbl", "lst", etc.
                    latex-env:
                      string:
                        description: In LaTeX output, the name of the custom environment to be used.
                    latex-list-of-file-extension:
                      string:
                        description: In LaTeX output, the extension of the auxiliary file used by LaTeX to collect names to be used in the custom "list of" command. If omitted, a string with prefix `lo` and suffix with the value of `ref-type` is used.
                    latex-list-of-description:
                      string:
                        description: The description of the crossreferenceable object to be used in the title of the "list of" command. If omitted, the field `reference-prefix` is used.
                    caption-location:
                      enum: [top, bottom, margin]
                      default: bottom
                      description: The location of the caption relative to the crossreferenceable content.

            chapters:
              boolean:
                description: Use top level sections (H1) in this document as chapters.
                default: false
            title-delim:
              string:
                description: The delimiter used between the prefix and the caption.
            fig-title:
              string:
                description: The title prefix used for figure captions.
            tbl-title:
              string:
                description: The title prefix used for table captions.
            eq-title:
              string:
                description: The title prefix used for equation captions.
            lst-title:
              string:
                description: The title prefix used for listing captions.
            thm-title:
              string:
                description: The title prefix used for theorem captions.
            lem-title:
              string:
                description: The title prefix used for lemma captions.
            cor-title:
              string:
                description: The title prefix used for corollary captions.
            prp-title:
              string:
                description: The title prefix used for proposition captions.
            cnj-title:
              string:
                description: The title prefix used for conjecture captions.
            def-title:
              string:
                description: The title prefix used for definition captions.
            exm-title:
              string:
                description: The title prefix used for example captions.
            exr-title:
              string:
                description: The title prefix used for exercise captions.
            fig-prefix:
              string:
                description: The prefix used for an inline reference to a figure.
            tbl-prefix:
              string:
                description: The prefix used for an inline reference to a table.
            eq-prefix:
              string:
                description: The prefix used for an inline reference to an equation.
            sec-prefix:
              string:
                description: The prefix used for an inline reference to a section.
            lst-prefix:
              string:
                description: The prefix used for an inline reference to a listing.
            thm-prefix:
              string:
                description: The prefix used for an inline reference to a theorem.
            lem-prefix:
              string:
                description: The prefix used for an inline reference to a lemma.
            cor-prefix:
              string:
                description: The prefix used for an inline reference to a corollary.
            prp-prefix:
              string:
                description: The prefix used for an inline reference to a proposition.
            cnj-prefix:
              string:
                description: The prefix used for an inline reference to a conjecture.
            def-prefix:
              string:
                description: The prefix used for an inline reference to a definition.
            exm-prefix:
              string:
                description: The prefix used for an inline reference to an example.
            exr-prefix:
              string:
                description: The prefix used for an inline reference to an exercise.

            fig-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for figures.

            tbl-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for tables.

            eq-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for equations.

            sec-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for sections.

            lst-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for listings.

            thm-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for theorems.

            lem-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for lemmas.

            cor-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for corollaries.

            prp-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for propositions.

            cnj-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for conjectures.

            def-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for definitions.

            exm-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for examples.

            exr-labels:
              ref: crossref-labels-schema
              description: The numbering scheme used for exercises.

            lof-title:
              string:
                description: The title used for the list of figures.

            lot-title:
              string:
                description: The title used for the list of tables.

            lol-title:
              string:
                description: The title used for the list of listings.

            labels:
              ref: crossref-labels-schema
              description: The number scheme used for references.

            subref-labels:
              ref: crossref-labels-schema
              description: The number scheme used for sub references.

            ref-hyperlink:
              boolean:
                default: true
                description: Whether cross references should be hyper-linked.

            appendix-title:
              string:
                description: The title used for appendix.

            appendix-delim:
              string:
                description: The delimiter beween appendix number and title.

- name: crossrefs-hover
  schema: boolean
  tags:
    formats: [$html-files]
  default: true
  description: Enables a hover popup for cross references that shows the item being referenced.
