- name: layout
  schema:
    anyOf:
      - string
      - arrayOf:
          arrayOf: number
  description:
    short: "2d-array of widths where the first dimension specifies columns and the second rows."
    long: |
      2d-array of widths where the first dimension specifies columns and the second rows.

      For example, to layout the first two output blocks side-by-side on the top with the third
      block spanning the full width below, use `[[3,3], [1]]`.

      Use negative values to create margin. For example, to create space between the 
      output blocks in the top row of the previous example, use `[[3,-1, 3], [1]]`.

- name: layout-ncol
  schema: number
  description: Layout output blocks into columns

- name: layout-nrow
  schema: number
  description: Layout output blocks into rows

- name: layout-align
  schema:
    enum: [default, left, center, right]
  default: center
  description: Horizontal alignment for layout content (`default`, `left`, `right`, or `center`)

- name: layout-valign
  schema:
    enum: [default, top, center, bottom]
  default: top
  description: Vertical alignment for layout content (`default`, `top`, `center`, or `bottom`)
