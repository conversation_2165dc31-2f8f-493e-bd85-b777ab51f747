- name: footnotes-hover
  schema: boolean
  tags:
    formats: [$html-files]
  default: true
  description: Enables a hover popup for footnotes that shows the footnote contents.

- name: links-as-notes
  schema: boolean
  tags:
    formats: [$pdf-all]
  default: false
  description: Causes links to be printed as footnotes.

- name: reference-location
  tags:
    formats: [$markdown-all, muse, $html-files, pdf]
  schema:
    enum: [block, section, margin, document]
  default: document
  description:
    short: |
      Location for footnotes and references
    long: |
      Specify location for footnotes. Also controls the location of references, if `reference-links` is set.

      - `block`: Place at end of current top-level block
      - `section`: Place at end of current section
      - `margin`: Place at the margin
      - `document`: Place at end of document
