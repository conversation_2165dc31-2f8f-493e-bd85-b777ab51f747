echo "copy template.potx of sodeck tool to sfs tool dir"
mkdir -p /sfs/tool
cp template.potx /sfs/tool/template.potx # 拷贝ppt生成工具的ppt模板文件到sfs工具根目录下
mkdir -p /sfs/tool/avatars # 首先确保目标目录存在
cp -r openapi_schema/avatars/* /sfs/tool/avatars/ # 拷贝avatars目录中的所有文件到sfs工具根目录下的avatars目录
chmod -R 777 bin/quarto/ # 修改bin目录下quarto目录的权限
gunicorn -w ${WORKER_NUM} --access-logfile - --error-logfile - app:app -b 0.0.0.0:${PORT} 2>&1 | tee -a gunicorn.log
