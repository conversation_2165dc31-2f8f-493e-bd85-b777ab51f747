import os
import json
import requests
import urllib3
import argparse
from translate_schema import translate_json_value

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def main():
    parser = argparse.ArgumentParser(description='Translate a single OpenAPI schema file from Chinese to English')
    parser.add_argument('filename', help='Name of the schema file to translate (e.g., eval_expression.json)')
    
    args = parser.parse_args()
    
    # 构建源文件和目标文件的完整路径
    chinese_file_path = os.path.join('openapi_schema', 'chinese-desc', args.filename)
    english_file_path = os.path.join('openapi_schema', 'english-desc', args.filename)
    
    # 验证输入文件是否存在
    if not os.path.exists(chinese_file_path):
        print(f"Error: File {chinese_file_path} does not exist")
        return
    
    try:
        # 读取中文 schema 文件
        with open(chinese_file_path, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # 翻译 schema
        translated_schema = translate_json_value(schema)
        
        # 确保目标目录存在
        os.makedirs(os.path.dirname(english_file_path), exist_ok=True)
        
        # 保存翻译后的 schema
        with open(english_file_path, 'w', encoding='utf-8') as f:
            json.dump(translated_schema, f, ensure_ascii=False, indent=2)
        
        print(f"Successfully translated {chinese_file_path} to {english_file_path}")
        
    except Exception as e:
        print(f"Error translating {chinese_file_path}: {str(e)}")

if __name__ == "__main__":
    main() 