package knowledge_base

import (
	"transwarp.io/applied-ai/hippo-go/v1"
)

const (
	hippoDefaultIndexName = "ivf_flat_index"
	defaultMetricType     = hippo.MetricTypeCosine
	defaultIndexType      = hippo.IvfFlat
)

// innerEmbeddingIndex 定义了平台内部知识库的hippo表向量索引的配置
func innerEmbeddingIndex() *hippo.Index {
	return &hippo.Index{
		FieldName:  InnerVecField,
		IndexName:  hippoDefaultIndexName,
		MetricType: defaultMetricType,
		IndexType:  defaultIndexType,
		Params: map[string]any{
			"nlist": 10,
		},
	}
}

// innerTableSchema 定义了平台内部知识库的hippo表schema
func innerTableSchema(dimension int) hippo.TableSchema {
	return hippo.TableSchema{
		AutoId: false,
		Fields: []hippo.TableField{
			{
				Name:         InnerIdField,
				IsPrimaryKey: true,
				DataType:     hippo.String,
			},
			{
				Name:     InnerOriIdField,
				DataType: hippo.String,
			},
			{
				Name:     InnerTitleField,
				DataType: hippo.String,
			},
			{ // 优先级字段
				Name:     InnerPriorityField,
				DataType: hippo.Int32,
			},
			{ // 用于筛选的短id
				Name:     InnerShortIdField,
				DataType: hippo.String,
			},
			{
				Name:     InnerVecField,
				DataType: hippo.FloatVector,
				TypeParams: &hippo.FieldTypeParams{
					Dimension: dimension,
				},
			},
		},
	}
}
