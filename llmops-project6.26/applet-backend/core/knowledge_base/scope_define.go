package knowledge_base

import "fmt"

var (
	DefaultMapping = fmt.Sprintf(`{
		"mappings": {
			"default_type_": {
				"properties": {
					"%s": {
						"type": "text",
						"analyzer": "ik_max_word"
					},
					"%s": {
						"type": "keyword"
					},
					"%s": {
						"type": "keyword"
					},
					"%s": {
						"type": "keyword"
					}
				}
			}
		},
		"settings": {
			"index": {
				"number_of_replicas": 1,
				"number_of_shards": 1
			}
		}
	}`, InnerTextField, InnerIdField, InnerOriIdField, InnerTitleField)
)
