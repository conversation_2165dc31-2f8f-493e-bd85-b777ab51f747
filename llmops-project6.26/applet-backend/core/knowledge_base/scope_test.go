package knowledge_base

import (
	"context"
	"testing"

	"github.com/elastic/go-elasticsearch/v6"
	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

var scopeHandler *ScopeHandler
var ctx = context.Background()

func init() {
	cli, _ := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{
			// "http://*************:9200",
			"http://**************:9200",
		},
		Username: "",
		Password: "",
	})

	scopeHandler = &ScopeHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase:      &models.KnowledgeBase{Id: "dummy", Name: "dummy"},
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT},
		},
		Cli:        stdes.NewESClient(cli),
		IndexName:  "test",
		TextField:  "text",
		TitleField: "doc",
	}
}

func TestScopeRecall(t *testing.T) {
	req := &pb.RetrieveKnowledgeBaseReq{
		Query: "hello",
		RetrievalConfig: &pb.RetrievalConfig{
			RecallParams: &pb.RecallParams{
				TopK:           10,
				ScoreThreshold: 0.2,
			},
		},
	}
	rsp, err := scopeHandler.Recall(ctx, req)
	t.Log(rsp, err)
}

func TestScopeListDoc(t *testing.T) {
	t.Log(scopeHandler.ListDocuments(ctx))
}
