package knowledge_base

import (
	"fmt"
	"gorm.io/gen"
	"sync"

	"gorm.io/gorm"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	ChunksMaxPageSize = 2000
)

type ChunkStore interface {
	GetTableName() string
	Load(id string) (*models.Chunk, error)
	LoadBatch(ids []string) ([]*models.Chunk, error)
	LoadByDocument(docId string) ([]*models.Chunk, error)
	LoadByRequest(*pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error)
	LoadByKnowledgeBase(kbId string) ([]*models.Chunk, error)

	IndexAugChunk(c *models.Chunk, id string) int
	// insert or update
	Store(*models.Chunk) error
	// insert batch
	StoreBatch([]*models.Chunk) error
	Delete(id string) error
	DeleteBatch(ids []string) error
	DeleteByDocument(docId string) error
	DeleteByKnowledgeBase(kbId string) error
	CountByDocument(docId string) (int64, error)
	BatchChangeRetrieveStatus(ids []string, enabled bool) error
	CountByDocuments(ids []string, condition func(q *query.Query) []gen.Condition) (map[string]int64, error)
	BatchFlagChunks(ids []string, flag int32) error
	LoadByCondition(condition func(q *query.Query) []gen.Condition) ([]*models.Chunk, error)
}

var cso sync.Once
var csm *chunkStoreMap

func GetChunkStore(projectID string) ChunkStore {
	cso.Do(func() {
		csm = &chunkStoreMap{
			storeMap: make(map[string]*chunkStore),
		}
	})
	csm.Lock()
	defer csm.Unlock()
	if cs, ok := csm.storeMap[projectID]; ok {
		return cs
	}
	cs := &chunkStore{
		Q:         dao.InitQuery(),
		projectID: projectID,
	}
	csm.storeMap[projectID] = cs
	// 检查表是否已经创建
	if !dao.MustInitDB().Migrator().HasTable(cs.GetTableName()) {
		// 创建表
		err := dao.MustInitDB().Table(cs.GetTableName()).AutoMigrate(&models.Chunk{})
		if err != nil {
			panic(err)
		}
	}
	return csm.storeMap[projectID]
}

type chunkStoreMap struct {
	sync.Mutex
	storeMap map[string]*chunkStore
}

type chunkStore struct {
	Q         *query.Query
	projectID string
}

func (cs *chunkStore) GetTableName() string {
	return fmt.Sprintf("chunks_%s", cs.projectID)
}

func (cs *chunkStore) LoadByCondition(condition func(q *query.Query) []gen.Condition) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Where(condition(cs.Q)...).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) BatchFlagChunks(ids []string, flag int32) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.Id.In(ids...)).UpdateSimple(repo.IndexFlag.Value(flag))
	return err
}

// CountByDocuments implements ChunkStore.
func (cs *chunkStore) CountByDocuments(ids []string, condition func(q *query.Query) []gen.Condition) (map[string]int64, error) {
	var res []struct {
		DocumentId string
		Total      int64
	}
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	if condition == nil {
		err := repo.Select(repo.DocumentId, repo.Id.Count().As("total")).Where(repo.DocumentId.In(ids...)).Group(repo.DocumentId).Scan(&res)
		if err != nil {
			return nil, err
		}
	} else {
		c := condition(cs.Q)
		err := repo.Select(repo.DocumentId, repo.Id.Count().As("total")).Where(repo.DocumentId.In(ids...)).Where(c...).Group(repo.DocumentId).Scan(&res)
		if err != nil {
			return nil, err
		}
	}
	ret := make(map[string]int64)
	for _, v := range res {
		ret[v.DocumentId] = v.Total
	}
	return ret, nil
}

func (cs *chunkStore) BatchChangeRetrieveStatus(ids []string, enabled bool) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.Id.In(ids...)).UpdateSimple(repo.RetrieveEnabled.Value(enabled))
	if err != nil {
		return err
	}
	return nil
}

func (cs *chunkStore) LoadBatch(ids []string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Where(repo.Id.In(ids...)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) Load(id string) (*models.Chunk, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	ret, err := repo.Where(repo.Id.Eq(id)).Take()
	if err == gorm.ErrRecordNotFound {
		return nil, stderr.KnowlChunkNotFound.Errorf("id=%s", id)
	}
	return ret, nil
}

func (cs *chunkStore) Store(c *models.Chunk) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Save(c)
}

func (cs *chunkStore) StoreBatch(data []*models.Chunk) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.CreateInBatches(data, 100)
}

func (cs *chunkStore) Delete(id string) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.Id.Eq(id)).Delete()
	return err
}
func (cs *chunkStore) DeleteBatch(ids []string) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.Id.In(ids...)).Delete()
	return err
}
func (cs *chunkStore) DeleteByDocument(docId string) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.DocumentId.Eq(docId)).Delete()
	return err
}
func (cs *chunkStore) DeleteByKnowledgeBase(kbId string) error {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	_, err := repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Delete()
	return err
}

func (cs *chunkStore) LoadByDocument(docId string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Where(repo.DocumentId.Eq(docId)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) LoadByRequest(req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	// 不支持指定排序字段和正反序
	cond := repo.Where(repo.DocumentId.Eq(req.DocId)).Order(repo.OrderId.Asc())
	if len(req.SourceTypeSelector) > 0 {
		s := make([]int32, 0, len(req.SourceTypeSelector))
		for _, v := range req.SourceTypeSelector {
			s = append(s, int32(v))
		}
		cond = cond.Where(repo.SourceType.In(s...))
	}
	if req.SearchContent != "" {
		cond = cond.Where(repo.Content.Like("%" + req.SearchContent + "%"))
	}
	cnt, err := cond.Count()
	if err != nil {
		return nil, stderr.Internal.Errorf("chunkStore.LoadByRequest count: %v", err)
	}
	offset, limit := helper.ParsePageReq(req.PageReq, int(cnt), ChunksMaxPageSize)
	chunks, _, err := cond.FindByPage(offset, limit)
	if err != nil {
		return nil, stderr.Internal.Errorf("chunkStore.LoadByRequest find: %v", err)
	}
	ret := make([]*pb.ChunkInfo, 0, len(chunks))
	for _, c := range chunks {
		ret = append(ret, &pb.ChunkInfo{
			Chunk: c.ToPb(),
		})
	}
	rsp := &pb.ListDocumentChunksRsp{
		Result:   ret,
		Total:    int32(cnt),
		PageNum:  req.PageReq.Page,
		PageSize: req.PageReq.PageSize,
	}
	return rsp, nil
}

func (cs *chunkStore) LoadByKnowledgeBase(kbId string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) IndexAugChunk(c *models.Chunk, id string) int {
	for i, augChunk := range c.AugmentedChunks {
		if augChunk.Id == id {
			return i
		}
	}
	return -1
}
func (cs *chunkStore) CountByDocument(docId string) (int64, error) {
	repo := cs.Q.Chunk.Table(cs.GetTableName())
	return repo.Where(repo.DocumentId.Eq(docId)).Count()
}
