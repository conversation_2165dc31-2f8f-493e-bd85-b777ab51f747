package knowledge_base

import (
	"com/trs/hybase/client"
	"com/trs/hybase/client/params"
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	PropertyDims       = "index.vector.dims"
	PropertySimilarity = "index.vector.similarity"
	COSINE             = "COSINE"
)

var (
	checkTableLockMapHybase = stdsync.NewLockMap()
)

// HybaseBase 基础属性
// 因为hybase同时支持标量和向量查询，所以只需一张表
type HybaseBase struct {
	*HandlerBase
	initOnce        sync.Once
	Cli             *client.TRSConnection
	Table           string // hybase的table实际上称呼为库
	TextField       string // 正文内容，用于标量全文索引
	TitleField      string // doc id
	VecField        string // 正文向量，用于向量索引
	IdField         string // chunk id
	OriginalIdField string // original chunk id, 如果和chunk id一致则为切片，否则为增强切片
	PriorityField   string // 优先级字段
	ShortIdField    string // 文档短id
	TypeField       string // 切片类型
	ExtraField      string // 额外信息，问答对的问题时为回答，表格切片为整行数据
}

func (h *HybaseBase) checkTable(ctx context.Context, kb *models.KnowledgeBase) error {
	lock := checkTableLockMapHybase.Get(kb.Id)
	lock.Lock()
	defer lock.Unlock()
	// 检查库是否存在
	has, err := h.hasTable(ctx, kb)
	if err != nil {
		return err
	}
	if !has {
		err = h.createTable(ctx, kb)
		if err != nil {
			return err
		}
	}
	return nil
}

func (h *HybaseBase) hasTable(ctx context.Context, kb *models.KnowledgeBase) (bool, error) {
	dbs, err := h.Cli.GetDatabases(getInnerHybaseTable(kb), nil)
	if err != nil {
		return false, err
	}
	return len(dbs) > 0, nil
}

func (h *HybaseBase) createTable(ctx context.Context, kb *models.KnowledgeBase) error {
	vecModel := kb.VectorModel
	dim, err := GetVecModelDim(vecModel)
	if err != nil {
		return err
	}
	db := client.NewTRSDatabase(h.Table, client.TYPE_VIEW)
	db.SetReplications(conf.Config.HybaseConfig.Replicas)
	db.AddColumn(func() *client.TRSDatabaseColumn {
		column := client.NewTRSDatabaseColumn(h.VecField, client.TYPE_VECTOR)
		column.SetProperty(PropertyDims, strconv.Itoa(dim))
		column.SetProperty(PropertySimilarity, COSINE)
		return column
	}())
	db.AddColumn(client.NewTRSDatabaseColumn(h.TitleField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.IdField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.OriginalIdField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.TextField, client.TYPE_DOCUMENT))
	db.AddColumn(client.NewTRSDatabaseColumn(h.PriorityField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.ShortIdField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.TypeField, client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn(h.ExtraField, client.TYPE_DOCUMENT))
	_, err = h.Cli.CreateDatabase(db)
	if err != nil {
		return stderr.Internal.Cause(err, "hybase cli: CreateTable")
	}
	return nil
}

func (h *HybaseBase) submitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	startTime := time.Now()

	// 过滤掉不需要向量化索引且不需要标量索引的chunks
	chunks = utils.FilterSlice(chunks, func(i int) bool { return !chunks[i].DisableVectorIndexing || !chunks[i].DisableFullTextIndexing })

	batches := BatchifySlice(chunks, conf.Config.KnowlhubConfig.VectorIndexingBatchSize)
	cnt := 0
	var embddingCosts, hippoCosts time.Duration
	errs := make(map[int]string)
	for i, batch := range batches {
		select {
		case <-ctx.Done():
			// ctx停了，可能是被取消了
			return ctx.Err()
		default:
			// 继续
		}
		sz := len(batch)
		tp := time.Now()
		// 事实上有些chunk是单独不需要向量化的
		texts := make([]string, 0)
		for _, item := range batch {
			if !item.DisableVectorIndexing {
				texts = append(texts, item.Text)
			}
		}
		vecs, err := h.BatchEmbedding(ctx, utils.CvcT2AnySlice(texts))
		if err != nil {
			// 错误不直接返回
			errs[i] = err.Error()
			continue
		}
		// 所以vecs的大小可能会比batch小，需要处理
		newvecs := make([][]float32, sz)
		index := 0
		for _, vec := range vecs {
			for batch[index].DisableVectorIndexing {
				index++
			}
			newvecs[index] = vec
			index++
		}
		embddingCosts += time.Since(tp)

		tp = time.Now()
		recs := make([]client.TRSInputRecord, 0)
		for i := 0; i < sz; i++ {
			rec := client.NewTRSInputRecord()
			rec.AddColumn(client.NewTRSInputColumn(h.IdField, batch[i].Id))
			rec.AddColumn(client.NewTRSInputColumn(h.OriginalIdField, batch[i].OriId))
			rec.AddColumn(client.NewTRSInputColumn(h.TitleField, batch[i].DocId))
			rec.AddColumn(client.NewTRSInputColumn(h.PriorityField, strconv.Itoa(batch[i].Priority)))
			rec.AddColumn(client.NewTRSInputColumn(h.ShortIdField, batch[i].ShortId))
			if !batch[i].DisableFullTextIndexing {
				rec.AddColumn(client.NewTRSInputColumn(h.TextField, batch[i].Text))
			}
			if !batch[i].DisableVectorIndexing {
				vecColumn := client.NewTRSInputColumn(h.VecField, "")
				vecColumn.SetFloatVector(newvecs[i])
				rec.AddColumn(vecColumn)
			}
			rec.AddColumn(client.NewTRSInputColumn(h.TypeField, batch[i].Type))
			rec.AddColumn(client.NewTRSInputColumn(h.ExtraField, batch[i].Extra))
			recs = append(recs, *rec)
		}
		report := client.NewTRSReport(nil)
		params := params.NewOperationParams()
		err = h.Cli.ExecuteInsert(h.Table, recs, params, report, nil)
		if err != nil {
			errs[i] = err.Error()
			continue
		}
		hippoCosts += time.Since(tp)

		if (i+1)%50 == 0 { // print time costs debug info every 50 batches
			stdlog.Infof("HybaseHandler.SubmitChunks [at batch %d] time consumption(avg over recent 50 batches): embedding: %s, hippo: %s", i+1, embddingCosts/50, hippoCosts/50)
			embddingCosts, hippoCosts = 0, 0
		}
		// 修改chunk的flag
		idsMap := make(map[string]struct{})
		ids := make([]string, 0)
		for _, chunk := range batch {
			if _, ok := idsMap[chunk.OriId]; !ok {
				idsMap[chunk.OriId] = struct{}{}
				ids = append(ids, chunk.OriId)
			}
		}
		err = GetChunkStore(h.KnowledgeBase.ProjectId).BatchFlagChunks(ids, models.IndexFlagALL)
		if err != nil {
			errs[i] = err.Error()
		}
		cnt += len(batch)
	}
	stdlog.Infof("SubmitChunks[hybaseVector], total costs: %v seconds, inserted %d chunks with all %d chunks", time.Since(startTime).Seconds(), cnt, len(chunks))
	if len(errs) > 0 {
		return stderr.Errorf("hybase cli: SubmitChunks: %+v", errs)
	}
	return nil
}

func getInnerHybaseTable(kb *models.KnowledgeBase) string {
	return fmt.Sprintf("%s.llm_%s", kb.ProjectId, strings.ReplaceAll(kb.Id, "-", ""))
}

// HybaseVectorHandler 用于实现向量形式的查询
type HybaseVectorHandler struct {
	*HybaseBase
	IsExternal bool // TODO 后续再实现对接外部hybase
}

func NewInnerHybaseVectorHandler(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error) {
	h := &HybaseVectorHandler{
		HybaseBase: &HybaseBase{
			HandlerBase: &HandlerBase{
				KnowledgeBase: kb,
				RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
					pb.KnowledgeBaseRetrieveStrategy_VECTOR,
				},
			},
			Cli:             clients.GetHybaseClientByDatabaseConnection(kb.DatabaseConfig.VectorDb),
			Table:           getInnerHybaseTable(kb),
			TextField:       InnerTextField,
			TitleField:      InnerTitleField,
			VecField:        InnerVecField,
			IdField:         InnerIdField,
			OriginalIdField: InnerOriIdField,
			PriorityField:   InnerPriorityField,
			ShortIdField:    InnerShortIdField,
			TypeField:       "type",
			ExtraField:      "extra",
		},
		IsExternal: false,
	}
	err := h.initStore(ctx)
	if err != nil {
		return nil, err
	}
	return h, nil
}

func (h *HybaseVectorHandler) initStore(ctx context.Context) error {
	if h.IsExternal {
		return nil
	}
	h.initOnce.Do(func() {
		// 如果没有创建库，则创建
		err := h.checkTable(ctx, h.KnowledgeBase)
		if err != nil {
			// 报错了，过10秒后重试
			stdlog.WithError(err).Warnf("vector check table error, retry 10s later.")
			go func() {
				time.Sleep(time.Second * 10)
				err := h.checkTable(ctx, h.KnowledgeBase)
				if err != nil {
					stdlog.WithError(err).Errorf("vector check table error twice.")
				}
			}()
		}
	})
	return nil
}

func (h *HybaseVectorHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseVectorHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseVectorHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseVectorHandler) CountDocuments(ctx context.Context) (int32, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseVectorHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseVectorHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	rc := req.RetrievalConfig
	rp := rc.RecallParams
	if rp == nil {
		return nil, fmt.Errorf("recall params is nil")
	}
	vec, err := h.Embedding(ctx, req.Query)
	if err != nil {
		return nil, err
	}

	roundMp := make(map[int]map[string]*pb.Hit)
	roundHits := make(map[int]map[string]*pb.Hit)
	roundSearchedDocs := make(map[int]map[string]struct{})
	chunks := make([]*pb.ChunkRetrieveResult, 0)
	errs := make([]error, 0)

	// 并发检索数据
	wg := sync.WaitGroup{}
	realMax := maxRecallCount
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_TABLE {
		realMax = 1
	}
	wg.Add(realMax)
	beginTime := time.Now()

	for r := 0; r < realMax; r++ {
		go func(r int) {
			defer wg.Done()
			var query string
			startTime := time.Now()
			if len(req.DocRange) > 0 {
				query = fmt.Sprintf("%s:%d AND %s#LIST:%s AND %s:\"%s\"", h.PriorityField, r, h.ShortIdField, strings.Join(req.DocRange, ","), h.VecField, helper.Vector2String(vec))
			} else {
				query = fmt.Sprintf("%s:%d AND %s:\"%s\"", h.PriorityField, r, h.VecField, helper.Vector2String(vec))
			}
			sp := params.NewSearchParams()
			sp.SetSortMethod(fmt.Sprintf("-%s", h.VecField)) // 根据向量相似度降序
			sp.SetCutSize(100)
			sp.SetReadColumns(fmt.Sprintf("%s;%s;%s", h.IdField, h.OriginalIdField, h.ShortIdField)) // 返回chunk_id, ori_id, short_id
			resultSet, err := h.Cli.ExecuteSelect(h.Table, query, 0, int64(rp.TopK), sp)
			if err != nil {
				errs = append(errs, stderr.Wrap(err, "hybase vector execute select error."))
				return
			}
			stdlog.Infof("hybase round [%d] vector execute select cost: %v seconds", r+1, time.Since(startTime).Seconds())
			if resultSet.GetNumFound() == 0 {
				return
			}
			mp := make(map[string]*pb.Hit)
			hits := make(map[string]*pb.Hit)
			searchedDocs := make(map[string]struct{})
			for i := 0; i < resultSet.Size(); i++ {
				re, err := resultSet.Get()
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get result error."))
					return
				}
				id, err := re.GetString(h.IdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get id field error."))
					return
				}
				oriId, err := re.GetString(h.OriginalIdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get oriId field error."))
					return
				}
				shortId, err := re.GetString(h.ShortIdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get shortId field error."))
					return
				}
				score := re.GetRelevance()
				hit := &pb.Hit{
					Hit:     true,
					Score:   float32(score),
					ShortId: shortId,
					Round:   int32(r),
				}
				if exScore, ok := mp[oriId]; !ok || float32(score) > exScore.Score {
					mp[oriId] = hit
				}
				hits[id] = hit
				searchedDocs[shortId] = struct{}{}
				resultSet.MoveNext()
			}
			roundMp[r] = mp
			roundHits[r] = hits
			roundSearchedDocs[r] = searchedDocs
		}(r)
	}

	// 汇总结果
	wg.Wait()
	if len(errs) > 0 {
		return nil, stderr.Internal.Errorf("hybase vector search failed with %+v", errs)
	}

	hits := make(map[string]*pb.Hit)
	allSearchedDocs := make(map[string]struct{})
	for r := 0; r < realMax; r++ {
		cMp := roundMp[r]
		for k, v := range cMp {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				chunks = append(chunks, &pb.ChunkRetrieveResult{
					Chunk: &pb.Chunk{Id: k},
					Score: v.Score,
				})
			}
		}
		cHits := roundHits[r]
		for k, v := range cHits {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				hits[k] = v
			}
		}
		cSearchedDocs := roundSearchedDocs[r]
		for k, _ := range cSearchedDocs {
			allSearchedDocs[k] = struct{}{}
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, rp.ScoreThreshold)
	stdlog.Infof("hybase vector recall and filter cost: %v seconds", time.Since(beginTime).Seconds())

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks, VectorIndexHits: hits}, nil
}

func (h *HybaseVectorHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	return h.DeleteChunksByDocId(ctx, req.DocId)
}

func (h *HybaseVectorHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	if v := ctx.Value(AlreadySubmitFullText); v == nil {
		err := h.submitChunks(ctx, chunks)
		if err != nil {
			return err
		}
		return nil
	} else {
		// 因为hybase同时支持向量和标量，所以只需要插入一次就行
		return nil
	}
}

func (h *HybaseVectorHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if len(chunkIds) > 0 {
		p := params.NewSearchParams()
		report := client.NewTRSReport(nil)
		query := fmt.Sprintf("%s#LIST:%s", h.IdField, strings.Join(chunkIds, ","))
		deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
		if err != nil {
			return err
		}
		stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseVectorHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if len(oriChunkIds) > 0 {
		p := params.NewSearchParams()
		report := client.NewTRSReport(nil)
		query := fmt.Sprintf("%s#LIST:%s", h.OriginalIdField, strings.Join(oriChunkIds, ","))
		deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
		if err != nil {
			return err
		}
		stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseVectorHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	p := params.NewSearchParams()
	report := client.NewTRSReport(nil)
	query := fmt.Sprintf("%s:%s", h.TitleField, docId)
	deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
	if err != nil {
		return err
	}
	stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	return nil
}

func (h *HybaseVectorHandler) RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error {
	p := params.NewSearchParams()
	report := client.NewTRSReport(nil)
	query := fmt.Sprintf("%s:%s", h.TitleField, strings.Join(docIds, ","))
	deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
	if err != nil {
		return err
	}
	stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	return nil
}

func (h *HybaseVectorHandler) Drop(ctx context.Context) error {
	if h.IsExternal {
		return ErrCouldNotUpdateExternalKB
	}
	lock := checkTableLockMapHybase.Get(h.KnowledgeBase.Id)
	lock.Lock()
	defer lock.Unlock()
	if has, err := h.hasTable(ctx, h.KnowledgeBase); err != nil {
		return err
	} else if has {
		ok, err := h.Cli.DeleteDatabase(getInnerHybaseTable(h.KnowledgeBase), false)
		if err != nil {
			return err
		}
		if !ok {
			return stderr.Internal.Errorf("failed to delete database %s", getInnerHybaseTable(h.KnowledgeBase))
		}
		stdlog.Infof("database %s delete successfully", getInnerHybaseTable(h.KnowledgeBase))
	} else {
		// skip
		stdlog.Infof("database %s not exist, no need to delete", getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseVectorHandler) VectorName() string {
	return pb.ConnectionType_HYBASE.String()
}

func (h *HybaseVectorHandler) Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error) {
	return NewInnerHybaseVectorHandler
}

// HybaseFullTextHandler 用于实现全文形式的查询
type HybaseFullTextHandler struct {
	*HybaseBase
	IsExternal bool // TODO 后续再实现对接外部hybase
}

func NewInnerHybaseFullTextHandler(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	h := &HybaseFullTextHandler{
		HybaseBase: &HybaseBase{
			HandlerBase: &HandlerBase{
				KnowledgeBase: kb,
				RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
					pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT,
				},
			},
			Cli:             clients.GetHybaseClientByDatabaseConnection(kb.DatabaseConfig.FulltextDb),
			Table:           getInnerHybaseTable(kb),
			TextField:       InnerTextField,
			TitleField:      InnerTitleField,
			VecField:        InnerVecField,
			IdField:         InnerIdField,
			OriginalIdField: InnerOriIdField,
			PriorityField:   InnerPriorityField,
			ShortIdField:    InnerShortIdField,
		},
		IsExternal: false,
	}
	err := h.initStore(ctx)
	if err != nil {
		return nil, err
	}
	return h, nil
}

func (h *HybaseFullTextHandler) initStore(ctx context.Context) error {
	if h.IsExternal {
		return nil
	}
	h.initOnce.Do(func() {
		// 如果没有创建库，则创建
		err := h.checkTable(ctx, h.KnowledgeBase)
		if err != nil {
			// 报错了，过10秒后重试
			stdlog.WithError(err).Warnf("fulltext check table error, retry 10s later.")
			go func() {
				time.Sleep(time.Second * 10)
				err := h.checkTable(ctx, h.KnowledgeBase)
				if err != nil {
					stdlog.WithError(err).Errorf("fulltext check table error twice.")
				}
			}()
		}
	})
	return nil
}

func (h *HybaseFullTextHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseFullTextHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseFullTextHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseFullTextHandler) CountDocuments(ctx context.Context) (int32, error) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseFullTextHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	//TODO implement me
	panic("implement me")
}

func (h *HybaseFullTextHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	rc := req.RetrievalConfig
	rp := rc.RecallParams
	if rp == nil {
		return nil, fmt.Errorf("recall params is nil")
	}

	roundMp := make(map[int]map[string]*pb.Hit)
	roundHits := make(map[int]map[string]*pb.Hit)
	roundSearchedDocs := make(map[int]map[string]struct{})
	chunks := make([]*pb.ChunkRetrieveResult, 0)
	lock := sync.Mutex{}
	errs := make([]error, 0)

	// 并发检索数据
	wg := sync.WaitGroup{}
	realMax := maxRecallCount
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_TABLE {
		realMax = 1
	}
	wg.Add(realMax)
	beginTime := time.Now()

	for r := 0; r < realMax; r++ {
		go func(r int) {
			defer wg.Done()
			startTime := time.Now()
			var query string
			if len(req.DocRange) > 0 {
				query = fmt.Sprintf("%s:%d AND %s#LIST:%s AND %s#OR:\"%s\"", h.PriorityField, r, h.ShortIdField, strings.Join(req.DocRange, ","), h.TextField, req.Query)
			} else {
				query = fmt.Sprintf("%s:%d AND %s#OR:\"%s\"", h.PriorityField, r, h.TextField, req.Query)
			}
			sp := params.NewSearchParams()
			sp.SetSortMethod("RELEVANCE") // 根据相关性排序
			sp.SetCutSize(100)
			sp.SetReadColumns(fmt.Sprintf("%s;%s;%s", h.IdField, h.OriginalIdField, h.ShortIdField)) // 返回chunk_id, short_id和ori_id
			resultSet, err := h.Cli.ExecuteSelect(h.Table, query, 0, int64(rp.TopK), sp)
			if err != nil {
				errs = append(errs, stderr.Wrap(err, "hybase fulltext failed to execute select"))
				return
			}
			stdlog.Infof("hybase round [%d] fulltext execute select cost: %v seconds", r+1, time.Since(startTime).Seconds())
			if resultSet.GetNumFound() == 0 {
				return
			}
			mp := make(map[string]*pb.Hit)
			hits := make(map[string]*pb.Hit)
			searchedDocs := make(map[string]struct{})
			for i := 0; i < resultSet.Size(); i++ {
				re, err := resultSet.Get()
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get result error."))
					return
				}
				id, err := re.GetString(h.IdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get id field error."))
					return
				}
				oriId, err := re.GetString(h.OriginalIdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get oriId field error."))
					return
				}
				shortId, err := re.GetString(h.ShortIdField)
				if err != nil {
					errs = append(errs, stderr.Wrap(err, "hybase get shortId field error."))
					return
				}
				score := re.GetRelevance()
				hit := &pb.Hit{
					Hit:     true,
					Score:   float32(score),
					ShortId: shortId,
					Round:   int32(r),
				}
				if exScore, ok := mp[oriId]; !ok || float32(score) > exScore.Score {
					mp[oriId] = hit
				}
				hits[id] = hit
				searchedDocs[shortId] = struct{}{}
				resultSet.MoveNext()
			}
			lock.Lock()
			roundMp[r] = mp
			roundHits[r] = hits
			roundSearchedDocs[r] = searchedDocs
			lock.Unlock()
		}(r)
	}

	// 汇总结果
	wg.Wait()
	if len(errs) > 0 {
		return nil, stderr.Internal.Errorf("hybase fulltext search failed with %+v", errs)
	}

	hits := make(map[string]*pb.Hit)
	allSearchedDocs := make(map[string]struct{})
	for r := 0; r < realMax; r++ {
		cMp := roundMp[r]
		for k, v := range cMp {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				chunks = append(chunks, &pb.ChunkRetrieveResult{
					Chunk: &pb.Chunk{Id: k},
					Score: v.Score,
				})
			}
		}
		cHits := roundHits[r]
		for k, v := range cHits {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				hits[k] = v
			}
		}
		cSearchedDocs := roundSearchedDocs[r]
		for k, _ := range cSearchedDocs {
			allSearchedDocs[k] = struct{}{}
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, rp.ScoreThreshold)
	stdlog.Infof("hybase fulltext recall and filter cost: %v seconds", time.Since(beginTime).Seconds())

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks, FullTextIndexHits: hits}, nil
}

func (h *HybaseFullTextHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	return h.DeleteChunksByDocId(ctx, req.DocId)
}

func (h *HybaseFullTextHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	if v := ctx.Value(AlreadySubmitVector); v == nil {
		err := h.submitChunks(ctx, chunks)
		if err != nil {
			return err
		}
		return nil
	} else {
		// 因为hybase同时支持向量和标量，所以只需要插入一次就行
		return nil
	}
}

func (h *HybaseFullTextHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if len(chunkIds) > 0 {
		p := params.NewSearchParams()
		report := client.NewTRSReport(nil)
		query := fmt.Sprintf("%s#LIST:%s", h.IdField, strings.Join(chunkIds, ","))
		deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
		if err != nil {
			return err
		}
		stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseFullTextHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if len(oriChunkIds) > 0 {
		p := params.NewSearchParams()
		report := client.NewTRSReport(nil)
		query := fmt.Sprintf("%s#LIST:%s", h.OriginalIdField, strings.Join(oriChunkIds, ","))
		deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
		if err != nil {
			return err
		}
		stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseFullTextHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	p := params.NewSearchParams()
	report := client.NewTRSReport(nil)
	query := fmt.Sprintf("%s:%s", h.TitleField, docId)
	deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
	if err != nil {
		return err
	}
	stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	return nil
}

func (h *HybaseFullTextHandler) RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error {
	p := params.NewSearchParams()
	report := client.NewTRSReport(nil)
	query := fmt.Sprintf("%s:%s", h.TitleField, strings.Join(docIds, ","))
	deleteCount, err := h.Cli.ExecuteDeleteQuery(getInnerHybaseTable(h.KnowledgeBase), query, p, report)
	if err != nil {
		return err
	}
	stdlog.Infof("DeleteChunksById[hybase]: succeed to delete %d chunks in table %s", deleteCount, getInnerHybaseTable(h.KnowledgeBase))
	return nil
}

func (h *HybaseFullTextHandler) Drop(ctx context.Context) error {
	if h.IsExternal {
		return ErrCouldNotUpdateExternalKB
	}
	lock := checkTableLockMapHybase.Get(h.KnowledgeBase.Id)
	lock.Lock()
	defer lock.Unlock()
	if has, err := h.hasTable(ctx, h.KnowledgeBase); err != nil {
		return err
	} else if has {
		ok, err := h.Cli.DeleteDatabase(getInnerHybaseTable(h.KnowledgeBase), false)
		if err != nil {
			return err
		}
		if !ok {
			return stderr.Internal.Errorf("failed to delete database %s", getInnerHybaseTable(h.KnowledgeBase))
		}
		stdlog.Infof("database %s delete successfully", getInnerHybaseTable(h.KnowledgeBase))
	} else {
		// skip
		stdlog.Infof("database %s not exist, no need to delete", getInnerHybaseTable(h.KnowledgeBase))
	}
	return nil
}

func (h *HybaseFullTextHandler) FullTextName() string {
	return pb.ConnectionType_HYBASE.String()
}

func (h *HybaseFullTextHandler) Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	return NewInnerHybaseFullTextHandler
}

//// HybaseMixHandler 用于实现向量标量混合查询
//type HybaseMixHandler struct {
//	*HybaseBase
//	IsExternal bool // TODO 后续再实现对接外部hybase
//}
