package knowledge_base

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/google/uuid"

	"transwarp.io/aip/llmops-common/pb"
	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

/**
语料相关的方法定义在这个文件中
*/

const (
	REDIS_TASK_BEGIN_KEY        = "corpus:task:import:examine:begin"
	REDIS_TASK_END_KEY          = "corpus:task:import:examine:end"
	REDIS_ASSET_META_LATEST_KEY = "corpus:asset:meta:latest"
	routineCount                = 5
	maxCapacity                 = 20 * 1024 * 1024

	REDIS_TASK_BEGIN_LOCK        = "corpus:task:import:examine:begin:lock"
	REDIS_TASK_END_LOCK          = "corpus:task:import:examine:end:lock"
	REDIS_ASSET_META_LATEST_LOCK = "corpus:asset:meta:latest:lock"
)

var ctl *CorpusTaskListener

// CorpusTaskListener 用于监听是否有加工任务到 “待审核入库” 或 “入库审核完成” 这一步
// 写表作为知识库文档列表的数据
type CorpusTaskListener struct {
	docChan       chan *pb.ProcessTask
	chunkChan     chan *pb.ProcessTask
	elementChan   chan *pb.ProcessTask
	assetMetaChan chan *pb.FileAsset
	cli           clients2.RedisClient
	q             *query.Query
}

func initCorpusTaskListener() {
	ctl = &CorpusTaskListener{
		docChan:       make(chan *pb.ProcessTask),
		chunkChan:     make(chan *pb.ProcessTask),
		elementChan:   make(chan *pb.ProcessTask),
		assetMetaChan: make(chan *pb.FileAsset),
		cli:           clients.RedisCli,
		q:             dao.InitQuery(),
	}
	ctl.Start()
}

func (l *CorpusTaskListener) Start() {
	go l.FetchBeginTasks()
	go l.FetchEndTasks()
	go l.FetchAssetMetas()
	go l.InsertTaskDocuments()
	go l.InsertDocumentChunks()
	go l.InsertDocumentElements()
	go l.UpdateDocAssetMetas()
}

// FetchBeginTasks 获取待入库审核的任务
func (l *CorpusTaskListener) FetchBeginTasks() {
	// 开始获取redis
	for {
		lock, err := l.cli.Lock(REDIS_TASK_BEGIN_LOCK, time.Minute)
		if err != nil {
			stdlog.WithError(err).Errorf("FetchBeginTasks: Lock failed.")
			continue
		}
		res, err := l.cli.HGetAll(REDIS_TASK_BEGIN_KEY)
		if err != nil {
			stdlog.WithError(err).Errorf("fetch corpus begin tasks error.")
			continue
		}
		if len(res) > 0 {
			for k, v := range res {
				stdlog.Infof("fetch corpus begin task field:%s, ", k)
				pt := new(pb.ProcessTask)
				err := stdsrv.Unmarshal([]byte(v), pt)
				if err != nil {
					stdlog.WithError(err).Errorf("unmarshal task [%s] error.", k)
					continue
				}
				l.docChan <- pt
				// 从redis中删除
				err = l.cli.HDel(REDIS_TASK_BEGIN_KEY, k)
				if err != nil {
					stdlog.WithError(err).Errorf("del redis begin task [%s] error.", k)
				}
			}
		}
		l.cli.Release(lock)
		// 5秒获取一次
		time.Sleep(time.Second * 5)
	}
}

func (l *CorpusTaskListener) FetchEndTasks() {
	// 开始获取redis
	for {
		lock, err := l.cli.Lock(REDIS_TASK_END_LOCK, time.Minute)
		if err != nil {
			stdlog.WithError(err).Errorf("FetchEndTasks: Lock failed.")
			continue
		}
		res, err := l.cli.HGetAll(REDIS_TASK_END_KEY)
		if err != nil {
			stdlog.WithError(err).Errorf("fetch corpus end tasks error.")
			continue
		}
		if len(res) > 0 {
			for k, v := range res {
				stdlog.Infof("fetch corpus end task field:%s, ", k)
				pt := new(pb.ProcessTask)
				err := stdsrv.Unmarshal([]byte(v), pt)
				if err != nil {
					stdlog.WithError(err).Errorf("unmarshal task [%s] error.", k)
					continue
				}
				l.chunkChan <- pt
				l.elementChan <- pt
				// 从redis中删除
				err = l.cli.HDel(REDIS_TASK_END_KEY, k)
				if err != nil {
					stdlog.WithError(err).Errorf("del redis end task [%s] error.", k)
				}
			}
		}
		l.cli.Release(lock)
		// 5秒获取一次
		time.Sleep(time.Second * 5)
	}
}

func (l *CorpusTaskListener) FetchAssetMetas() {
	for {
		lock, err := l.cli.Lock(REDIS_ASSET_META_LATEST_LOCK, time.Minute)
		if err != nil {
			stdlog.WithError(err).Errorf("FetchBeginTasks: Lock failed.")
			continue
		}
		res, err := l.cli.HGetAll(REDIS_ASSET_META_LATEST_KEY)
		if err != nil {
			stdlog.WithError(err).Errorf("fetch corpus asset meta error.")
			continue
		}
		if len(res) > 0 {
			for k, v := range res {
				stdlog.Infof("fetch corpus asset meta field:%s, ", k)
				asset := new(pb.FileAsset)
				err := stdsrv.Unmarshal([]byte(v), asset)
				if err != nil {
					stdlog.WithError(err).Errorf("unmarshal asset [%s] error.", k)
				}
				l.assetMetaChan <- asset
				// 从redis删除
				err = l.cli.HDel(REDIS_ASSET_META_LATEST_KEY, k)
				if err != nil {
					stdlog.WithError(err).Errorf("del redis asset meta [%s] error.", k)
				}
			}
		}
		l.cli.Release(lock)
		time.Sleep(time.Second * 5)
	}
}

// InsertTaskDocuments 待入库审核时需要将文档数据写表
func (l *CorpusTaskListener) InsertTaskDocuments() {
	// 启动5个协程处理任务
	for i := 0; i < routineCount; i++ {
		go func() {
			for task := range l.docChan {
				// 获取知识库
				kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(task.KnowledgeBaseId)
				if err != nil {
					stdlog.WithError(err).Errorf("store task document error when get knowledge base [%s]", task.KnowledgeBaseId)
					continue
				}
				stdlog.Infof("begin to insert document data to mysql with task [%s]", task.Id)
				taskWithoutFiles := &pb.ProcessTask{
					KnowledgeBaseId:       task.KnowledgeBaseId,
					Id:                    task.Id,
					Name:                  task.Name,
					ExportPath:            task.ExportPath,
					ProjectId:             task.ProjectId,
					DocumentFileSource:    task.DocumentFileSource,
					ChunkResPath:          task.ChunkResPath,
					KnowledgeTableSchemas: task.KnowledgeTableSchemas,
				}
				if kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
					// 构造文档数据
					for _, fileAsset := range task.FileAssets {
						stdlog.Infof("begin to insert document with file asset [%s]", fileAsset.Id)
						err := GetDocumentStore().Store(&models.Document{
							Id:                 uuid.NewString(),
							Name:               fileAsset.FileName,
							FileSizeBytes:      int32(fileAsset.FileSize),
							FileFormat:         fileAsset.FileType,
							UploadTime:         time.Now(),
							KnowledgeBaseId:    task.KnowledgeBaseId,
							ProjectId:          task.ProjectId,
							DocumentFileSource: pb.DocumentFileSource_FILE_ASSET,
							Stage:              pb.DocumentTaskStage_WAITING_EXAMINING,
							DbStatus:           pb.DocumentDbStatus_UNSTART,
							ProcessTask:        taskWithoutFiles,
							FileAsset:          fileAsset,
						})
						if err != nil {
							stdlog.WithError(err).Errorf("insert document to db error. kb: [%s]", task.KnowledgeBaseId)
						}
					}
				} else {
					// 构造表格数据
					for _, assetRel := range task.KnowledgeAssetRels {
						stdlog.Infof("begin to insert document with asset rel [%d]", assetRel.AssetRelId)
						err := GetDocumentStore().Store(&models.Document{
							Id:                 uuid.NewString(),
							Name:               assetRel.FileName,
							FileSizeBytes:      int32(assetRel.FileSize),
							FileFormat:         assetRel.FileType,
							UploadTime:         time.Now(),
							KnowledgeBaseId:    task.KnowledgeBaseId,
							ProjectId:          task.ProjectId,
							DocumentFileSource: task.DocumentFileSource,
							Stage:              pb.DocumentTaskStage_WAITING_EXAMINING,
							DbStatus:           pb.DocumentDbStatus_UNSTART,
							ProcessTask:        taskWithoutFiles,
							AssetRel:           assetRel,
						})
						if err != nil {
							stdlog.WithError(err).Errorf("insert document to db error. kb: [%s]", task.KnowledgeBaseId)
						}
					}
				}
			}
		}()
	}
}

// InsertDocumentChunks 入库审核完成后需要将chunk信息写表
func (l *CorpusTaskListener) InsertDocumentChunks() {
	// 启动5个协程处理任务
	for i := 0; i < routineCount; i++ {
		go func() {
			for task := range l.chunkChan {
				// 获取知识库
				kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(task.KnowledgeBaseId)
				if err != nil {
					stdlog.WithError(err).Errorf("store task document error when get knowledge base [%s]", task.KnowledgeBaseId)
					continue
				}
				if kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
					l.processTextKb(task)
				} else if kb.ContentType == pb.KnowledgeBaseContentType_TABLE {
					l.processTableKb(task)
				} else {
					stdlog.Warnf("unsupported kb type [%s]", kb.ContentType.String())
				}
			}
		}()
	}
}

func (l *CorpusTaskListener) processTableKb(task *pb.ProcessTask) {
	// 读取chunk文件写入chunk
	stdlog.Infof("begin to insert chunk data to mysql with task [%s]", task.Id)
	docs, err := GetDocumentStore().LoadByKnowledgeBase(task.KnowledgeBaseId)
	if err != nil {
		stdlog.WithError(err).Errorf("get docs by knowledge [%s] error.", task.KnowledgeBaseId)
	}
	repo := l.q.Document
	toProcess := make(map[int64]*models.Document)
	// 过滤属于taskid的文档
	for _, doc := range docs {
		if doc.ProcessTask != nil && doc.ProcessTask.Id == task.Id {
			toProcess[doc.AssetRel.AssetRelId] = doc
		}
	}
	for _, fileAsset := range task.KnowledgeAssetRels {
		stdlog.Infof("begin to insert chunk with file asset [%d]", fileAsset.AssetRelId)
		// doc状态改为待入库，写表状态改为进行中
		if doc, ok := toProcess[fileAsset.AssetRelId]; !ok {
			// 表中不存在的数据，理论上不会发生
			continue
		} else {
			doc.Stage = pb.DocumentTaskStage_WAITING
			doc.DbStatus = pb.DocumentDbStatus_WRITING
			_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
			if err != nil {
				stdlog.WithError(err).Errorf("update document [%s] stage and dbstatus error", doc.Id)
			}
			// 异步读取文件写表，因为文件可能还没有产生，需要等待
			sfsPath, err := stdfs.NewRelativeFilePath(fileAsset.ExportPath)
			if err != nil {
				stdlog.WithError(err).Errorf("get file asset export result path [%s] error.", fileAsset.ExportPath)
				return
			}
			filePath := sfsPath.ToAbsFilePath()
			go func() {
				// 最多尝试20次
				maxRetry := 20
				interval := time.Second * 5
				for maxRetry > 0 {
					maxRetry--
					if _, err := os.Stat(filePath); err != nil {
						// 姑且都认为是文件不存在
						stdlog.WithError(err).Warnf("file [%s] not exist, waiting for 10 seconds", filePath)
						time.Sleep(interval)
						interval = interval * 2
					} else {
						jsonlFile, err := os.Open(filePath)
						if err != nil {
							// 文件打开有问题，直接退出
							stdlog.WithError(err).Errorf("open file [%s] error.", filePath)
							break
						}
						scanner := bufio.NewScanner(jsonlFile)
						buf := make([]byte, maxCapacity)
						scanner.Buffer(buf, maxCapacity)
						lineNumber := 0
						chunks := make([]*models.Chunk, 0)
						for scanner.Scan() {
							lineNumber++
							line := scanner.Text()
							// 跳过空行
							if line == "" {
								continue
							}
							// 解析JSON行
							var chunk pb.Chunk
							if err := stdsrv.DefaultProtoJsonAccessor().Unmarshal([]byte(line), &chunk); err != nil {
								stdlog.WithError(err).Errorf("第%d行解析错误: %v\n", lineNumber, err)
								continue
							}
							if chunk.Id == "" {
								chunk.Id = uuid.NewString()
							}
							if chunk.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_ABSTRACT {
								doc.AbstractChunk = &chunk
							}
							chunk.DisableVectorIndexing = true
							chunk.DisableFullTextIndexing = true
							for _, ac := range chunk.AugmentedChunks {
								if ac.Id == "" {
									ac.Id = uuid.NewString()
								}
							}
							chunks = append(chunks, models.FromChunkPb(&chunk, task.KnowledgeBaseId, doc.Id))
						}
						if err := scanner.Err(); err != nil {
							// 处理错误，例如：
							stdlog.WithError(err).Errorf("扫描文件错误: %v", err)
						}
						// 将chunks写入表中
						err = GetChunkStore(doc.ProjectId).StoreBatch(chunks)
						if err != nil {
							stdlog.WithError(err).Errorf("store batch chunk of doc [%s] error.", doc.Id)
						}
						jsonlFile.Close()
						doc.DbStatus = pb.DocumentDbStatus_WRITTEN
						doc.NumChunks = int64(len(chunks))
						_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
						if err != nil {
							stdlog.WithError(err).Errorf("update document [%s] dbstatus error", doc.Id)
						}
						break
					}
				}
				// 没有获取到文件，输出日志
				stdlog.Warnf("chunk file [%d] not found.", fileAsset.AssetRelId)
			}()
		}
	}
}

func (l *CorpusTaskListener) processTextKb(task *pb.ProcessTask) {
	// 读取chunk文件写入chunk
	stdlog.Infof("begin to insert chunk data to mysql with task [%s]", task.Id)
	docs, err := GetDocumentStore().LoadByKnowledgeBase(task.KnowledgeBaseId)
	if err != nil {
		stdlog.WithError(err).Errorf("get docs by knowledge [%s] error.", task.KnowledgeBaseId)
	}
	repo := l.q.Document
	toProcess := make(map[string]*models.Document)
	// 过滤属于taskid的文档
	for _, doc := range docs {
		if doc.ProcessTask != nil && doc.ProcessTask.Id == task.Id {
			toProcess[doc.FileAsset.FileAssetVersion.Id] = doc
		}
	}
	for _, fileAsset := range task.FileAssets {
		stdlog.Infof("begin to insert chunk with file asset [%s]", fileAsset.FileAssetVersion.Id)
		// doc状态改为待入库，写表状态改为进行中
		if doc, ok := toProcess[fileAsset.FileAssetVersion.Id]; !ok {
			// 表中不存在的数据，理论上不会发生
			continue
		} else {
			doc.Stage = pb.DocumentTaskStage_WAITING
			doc.DbStatus = pb.DocumentDbStatus_WRITING
			_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
			if err != nil {
				stdlog.WithError(err).Errorf("update document [%s] stage and dbstatus error", doc.Id)
			}
			// 异步读取文件写表，因为文件可能还没有产生，需要等待
			sfsPath, err := stdfs.NewRelativeFilePath(fileAsset.ExportPath)
			if err != nil {
				stdlog.WithError(err).Errorf("get file asset export result path [%s] error.", fileAsset.ExportPath)
				return
			}
			filePath := sfsPath.ToAbsFilePath()
			go func() {
				// 最多尝试20次
				maxRetry := 20
				interval := time.Second * 5
				for maxRetry > 0 {
					maxRetry--
					if _, err := os.Stat(filePath); err != nil {
						// 姑且都认为是文件不存在
						stdlog.WithError(err).Warnf("file [%s] not exist, waiting for 10 seconds", filePath)
						time.Sleep(interval)
						interval = interval * 2
					} else {
						jsonlFile, err := os.Open(filePath)
						if err != nil {
							// 文件打开有问题，直接退出
							stdlog.WithError(err).Errorf("open file [%s] error.", filePath)
							break
						}
						scanner := bufio.NewScanner(jsonlFile)
						buf := make([]byte, maxCapacity)
						scanner.Buffer(buf, maxCapacity)
						lineNumber := 0
						chunks := make([]*models.Chunk, 0)
						for scanner.Scan() {
							lineNumber++
							line := scanner.Text()
							// 跳过空行
							if line == "" {
								continue
							}
							// 解析JSON行
							var chunk pb.Chunk
							if err := stdsrv.DefaultProtoJsonAccessor().Unmarshal([]byte(line), &chunk); err != nil {
								stdlog.WithError(err).Errorf("第%d行解析错误: %v\n", lineNumber, err)
								continue
							}
							if chunk.Id == "" {
								chunk.Id = uuid.NewString()
							}
							if chunk.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_ABSTRACT {
								doc.AbstractChunk = &chunk
							}
							chunks = append(chunks, models.FromChunkPb(&chunk, task.KnowledgeBaseId, doc.Id))
						}
						// 将chunks写入表中
						err = GetChunkStore(doc.ProjectId).StoreBatch(chunks)
						if err != nil {
							stdlog.WithError(err).Errorf("store batch chunk of doc [%s] error.", doc.Id)
						}
						jsonlFile.Close()
						doc.DbStatus = pb.DocumentDbStatus_WRITTEN
						doc.NumChunks = int64(len(chunks))
						_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
						if err != nil {
							stdlog.WithError(err).Errorf("update document [%s] dbstatus error", doc.Id)
						}
						break
					}
				}
				// 没有获取到文件，输出日志
				stdlog.Warnf("chunk file [%s] not found.", fileAsset.FileAssetVersion.Id)
			}()
		}
	}
}

// InsertDocumentElements 入库审核完成后需要将element信息写表
func (l *CorpusTaskListener) InsertDocumentElements() {
	// 启动5个协程处理任务
	for i := 0; i < routineCount; i++ {
		go func() {
			for task := range l.elementChan {
				// 获取知识库
				kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(task.KnowledgeBaseId)
				if err != nil {
					stdlog.WithError(err).Errorf("store task document error when get knowledge base [%s]", task.KnowledgeBaseId)
					continue
				}
				if kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
					// 读取element文件写入elements
					stdlog.Infof("begin to insert elements data to mysql with task [%s]", task.Id)
					sfsPath, err := stdfs.NewRelativeFilePath(task.ChunkResPath)
					if err != nil {
						stdlog.WithError(err).Errorf("get task chunk res path [%s] error.", task.ChunkResPath)
						return
					}
					absPath := sfsPath.ToAbsFilePath()
					docs, err := GetDocumentStore().LoadByKnowledgeBase(task.KnowledgeBaseId)
					if err != nil {
						stdlog.WithError(err).Errorf("get docs by knowledge [%s] error.", task.KnowledgeBaseId)
					}
					toProcess := make(map[string]*models.Document)
					// 过滤属于taskid的文档
					for _, doc := range docs {
						if doc.ProcessTask != nil && doc.ProcessTask.Id == task.Id {
							toProcess[doc.FileAsset.FileAssetVersion.Id] = doc
						}
					}
					for _, fileAsset := range task.FileAssets {
						stdlog.Infof("begin to insert element with file asset [%s]", fileAsset.Id)
						if doc, ok := toProcess[fileAsset.FileAssetVersion.Id]; !ok {
							// 表中不存在的数据，理论上不会发生
							continue
						} else {
							filePath := filepath.Join(absPath, fmt.Sprintf("%s.json", fileAsset.FileAssetVersion.Id))
							if _, err := os.Stat(filePath); err != nil {
								// 姑且都认为是文件不存在
								stdlog.WithError(err).Errorf("file not exist, [%s]", filePath)
								continue
							} else {
								jsonFile, err := os.Open(filePath)
								if err != nil {
									// 文件打开有问题，直接退出
									stdlog.WithError(err).Errorf("open file [%s] error.", filePath)
									continue
								}
								var docElement pb.DocSvcLoadChunkRsp
								// 读取文件内容
								fileContent, err := io.ReadAll(jsonFile)
								if err != nil {
									stdlog.WithError(err).Errorf("读取文件 [%s] 内容错误", filePath)
									jsonFile.Close()
									continue
								}
								jsonFile.Close()

								// 解析JSON内容到docElement
								err = stdsrv.DefaultProtoJsonAccessor().Unmarshal(fileContent, &docElement)
								if err != nil {
									stdlog.WithError(err).Errorf("解析文档元素错误")
									continue
								}
								elements := make([]*models.DocElement, 0)
								for _, element := range docElement.Elements {
									elements = append(elements, models.FromDocElementPb(element))
								}
								// 将elements写入表中
								err = GetDocElementStore().StoreBatch(elements)
								if err != nil {
									stdlog.WithError(err).Errorf("store batch elements of doc [%s] error.", doc.Id)
								}
							}
						}
					}
				} else {
					// 其他类型的知识库没有elements
					stdlog.Warnf("unsupported kb type [%s]", kb.ContentType.String())
				}
			}
		}()
	}
}

func (l *CorpusTaskListener) UpdateDocAssetMetas() {
	// 启动5个协程处理任务
	for i := 0; i < routineCount; i++ {
		go func() {
			for asset := range l.assetMetaChan {
				stdlog.Infof("begin to update document asset [%s]", asset.Id)
				relRepo := l.q.DocTaskAssetRel
				res, err := relRepo.Where(relRepo.AssetID.Eq(asset.Id)).Find()
				if err != nil {
					stdlog.WithError(err).Errorf("find rel [%s] error.", asset.Id)
					continue
				}
				if len(res) > 0 {
					docRepo := l.q.Document
					wg := sync.WaitGroup{}
					wg.Add(len(res))
					for _, rel := range res {
						go func(rel *models.DocTaskAssetRel) {
							defer wg.Done()
							stdlog.Infof("begin to update doc [%d]", rel.DocShortID)
							doc, err := docRepo.Where(docRepo.ShortId.Eq(rel.DocShortID)).First()
							if err != nil {
								stdlog.WithError(err).Errorf("find doc [%d] error.", rel.DocShortID)
								return
							}
							var av *pb.FileAssetVersion
							if doc.FileAsset != nil {
								av = doc.FileAsset.FileAssetVersion
							}
							doc.FileAsset = asset
							doc.FileAsset.FileAssetVersion = av
							_, err = docRepo.Where(docRepo.ShortId.Eq(doc.ShortId)).Updates(doc)
							if err != nil {
								stdlog.WithError(err).Errorf("update doc [%d] error.", rel.DocShortID)
							}
						}(rel)
					}
					wg.Wait()
				}
				stdlog.Infof("finish to update document asset [%s]", asset.Id)
			}
		}()
	}
}
