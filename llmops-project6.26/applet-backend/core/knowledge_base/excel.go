package knowledge_base

import (
	"fmt"
	"github.com/xuri/excelize/v2"
)

type ExportQa struct {
	DocID      string `json:"docID"`
	KbID       string `json:"kbID"`
	ChunkID    string `json:"chunkID"`
	DocName    string `json:"docName"`
	AssetID    string `json:"assetID"`
	Question   string `json:"question"`
	Answer     string `json:"answer"`
	QuestionID string `json:"questionID"`
	AnswerID   string `json:"answerID"`
}

// ExportQaToExcel 将Qa数据导出为Excel文件
func ExportQaToExcel(qaList []*ExportQa) (*excelize.File, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 创建工作表
	sheetName := "QA数据"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, err
	}

	// 设置表头
	headers := []string{
		"文档ID", "知识库ID", "切片ID", "文档名称", "资产ID",
		"问题", "答案", "问题ID", "答案ID",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		return nil, err
	}

	// 应用表头样式
	for i := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// 写入数据
	for rowIndex, qa := range qaList {
		row := rowIndex + 2 // 从第2行开始（第1行是表头）

		values := []interface{}{
			qa.DocID, qa.KbID, qa.ChunkID, qa.DocName, qa.AssetID,
			qa.Question, qa.Answer, qa.QuestionID, qa.AnswerID,
		}

		for colIndex, value := range values {
			cell := fmt.Sprintf("%s%d", string(rune('A'+colIndex)), row)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 15)
	}

	// 设置默认工作表
	f.SetActiveSheet(index)
	f.DeleteSheet("Sheet1") // 删除默认的空工作表

	return f, nil
}
