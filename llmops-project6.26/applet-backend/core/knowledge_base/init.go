package knowledge_base

import (
	"fmt"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
)

var (
	KnowledgeBaseMgr *KnowledgeBaseManager
)

const (
	KbVersion = "2.0.0" // 当前的知识库版本，写死于代码中
)

func Init() {
	initEngineRegistry()
	KnowledgeBaseMgr = GetKnowledgeBaseManager()
	initCorpusTaskListener()
	initDocImportTaskManager()

	lmc := conf.Config.KnowlhubConfig.LoadingMaxConcurrency
	if lmc < 1 {
	}
	sem = stdsync.NewWeighted(int64(lmc))
	initDocTaskAssetRel()
	execDbSql()
}

func execDbSql() {
	go RefreshDocumentChunksNum()
	go SyncChunks()
}

func RefreshDocumentChunksNum() {
	if conf.Config.KnowlhubConfig.RefreshDocumentChunksNum {
		db := dao.MustInitDB()
		if err := db.Exec(`
UPDATE documents d
JOIN (
    SELECT 
        document_id,
        COUNT(*) as num_chunks,
        SUM(CASE WHEN index_flag > 0 THEN 1 ELSE 0 END) as num_success_chunks
    FROM chunks
    GROUP BY document_id
) c ON d.id = c.document_id
SET 
    d.num_chunks = c.num_chunks,
    d.num_success_chunks = c.num_success_chunks;`).Error; err != nil {
			stdlog.WithError(err).Warnf("refresh document chunks num err")
		}
	}
}

// SyncChunks 从旧表中复制数据写入新的分表中
func SyncChunks() {
	if !conf.Config.KnowlhubConfig.SyncChunks {
		return
	}
	// 获取projects和kb
	q := dao.InitQuery()
	docs, err := q.Document.Find()
	if err != nil {
		panic(err)
	}
	projectsMap := make(map[string][]string)
	for _, doc := range docs {
		projectsMap[doc.ProjectId] = append(projectsMap[doc.ProjectId], doc.Id)
	}
	wg := sync.WaitGroup{}
	wg.Add(len(projectsMap))
	for k, v := range projectsMap {
		go func(projectID string, docIDs []string) {
			defer wg.Done()
			stdlog.Infof("=====> begin to sync chunks in project %s with %d docs", projectID, len(docIDs))
			// 建表
			cs := GetChunkStore(projectID)
			db := dao.MustInitDB()
			// 如果doc id数量太多，分批进行复制
			index := 0
			batchSize := 500
			tx := db.Begin()
			for {
				if index >= len(docIDs) {
					break
				}
				end := index + batchSize
				if end > len(docIDs) {
					end = len(docIDs)
				}
				sql := fmt.Sprintf(`insert into %s
                        select id,
                               content,
                               element_ids,
                               source_type,
                               content_type,
                               disable_vector_indexing,
                               disable_full_text_indexing,
                               document_id,
                               knowledge_base_id,
                               augmented_chunks,
                               order_id,
                               edited,
                               retrieve_enable,
                               qa_pairs,
                               index_flag
                        from chunks where chunks.document_id in ?`, fmt.Sprintf("`%s`", cs.GetTableName()))
				err := tx.Exec(sql, docIDs[index:end]).Error
				if err != nil {
					stdlog.WithError(err).Errorf("=====> sync chunks err")
					tx.Rollback()
					break
				}
				stdlog.Infof("=====> sync chunks in project %s with docs %d - %d done", projectID, index, end)
				index += batchSize
			}
			tx.Commit()
			stdlog.Infof("=====> sync chunks in project %s with %d docs done", projectID, len(docIDs))
		}(k, v)
	}
	wg.Wait()
	stdlog.Infof("=====> sync all chunks done")
}
