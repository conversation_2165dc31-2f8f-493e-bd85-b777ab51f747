package knowledge_base

import (
	"bufio"
	"fmt"
	"github.com/google/uuid"
	"os"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func TestChunk(t *testing.T) {
	filePath := "/Users/<USER>/Documents/test.jsonl"
	jsonlFile, err := os.Open(filePath)
	if err != nil {
		// 文件打开有问题，直接退出
		panic("open file error.")
	}
	scanner := bufio.NewScanner(jsonlFile)
	lineNumber := 0
	chunks := make([]*models.Chunk, 0)
	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()
		// 跳过空行
		if line == "" {
			continue
		}
		// 解析JSON行
		var chunk pb.Chunk
		if err := stdsrv.DefaultProtoJsonAccessor().Unmarshal([]byte(line), &chunk); err != nil {
			fmt.Printf("第%d行解析错误: %v\n", lineNumber, err)
			continue
		}
		if chunk.Id == "" {
			chunk.Id = uuid.NewString()
		}

		chunks = append(chunks, models.FromChunkPb(&chunk, "sss", "ddddd"))
	}
	println("end")
}
