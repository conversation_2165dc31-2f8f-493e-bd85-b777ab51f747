package knowledge_base

import (
	"context"
	"fmt"
	"sync"
	"time"

	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/applet"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/serving"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/aiot/vision-std/stdctn"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/aiot/vision-std/triton"

	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	pcli "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

// 知识库的健康检查
// 依赖服务：
// 系统级：doc-svc
// 租户级：hippo、scope
// 每个知识库关联的服务检查：向量模型、rerank模型、知识增强的文本模型、自定义解析策略

type HealthzState struct {
	Running               bool `json:"running"`
	IntervalSeconds       int  `json:"interval_seconds"`
	LastFinishedTimeMills int  `json:"last_finished_time_mills"`
}

func GetHealthzState() HealthzState {
	return *healthzState
}

var (
	healthzState   *HealthzState // 单例
	healthzStateMu sync.Mutex    // 用于保证全局只有一个运行中的healthz； 修改healthzState的属性均需要获得mu

	// system level
	DocSvcHealthCache *pb.HealthStatus = NewHealthyStatus() // global status

	// tenant level
	HippoHealthCache *stdctn.ConcurrentMap[string, *pb.HealthStatus] // project_id | conn_id -> status
	ScopeHealthCache *stdctn.ConcurrentMap[string, *pb.HealthStatus] // project_id | conn_id -> status

	// knowledge base level
	EmbeddingHealthCache      *stdctn.ConcurrentMap[string, *pb.HealthStatus] // model_svc_id -> status
	RerankHealthCache         *stdctn.ConcurrentMap[string, *pb.HealthStatus]
	AugmentHealthCache        *stdctn.ConcurrentMap[string, *pb.HealthStatus]
	CustomStrategyHealthCache *stdctn.ConcurrentMap[string, *pb.HealthStatus] // app_svc_id -> status
	ImageModelHealthCache     *stdctn.ConcurrentMap[string, *pb.HealthStatus] // 图像理解模型 model_svc_id -> status
	OcrLayoutSvcHealthCache   *stdctn.ConcurrentMap[string, *pb.HealthStatus]

	// HealthOverviewCache 缓存知识库对应的整体健康状态  kb_id -> *ho
	HealthOverviewCache *clients2.RedisMap[pb.KnowledgeBaseHealthOverview]

	// test data
	ScopeTestData = map[string]any{
		"text":   "test data",
		"id":     "1",
		"ori_id": "1",
		"doc_id": "1",
	}
	ScopeTestQuery = map[string]any{
		"query": map[string]any{
			"match_all": map[string]any{},
		},
	}

	startHealthCheckOnce sync.Once

	refreshHealthzChan chan struct{}
)

func NewHealthyStatus() *pb.HealthStatus {
	return &pb.HealthStatus{
		Healthy: true,
		Message: "",
	}
}

func TryRefreshHealthz() bool {
	if healthzState.Running {
		return false
	}
	refreshHealthzChan <- struct{}{}
	return true
}

func healthCheck() {
	start := time.Now()
	healthzStateMu.Lock()
	healthzState.Running = true
	defer func() {
		healthzState.Running = false
		healthzState.LastFinishedTimeMills = int(time.Now().UnixMilli())
	}()
	defer healthzStateMu.Unlock()

	ctx, _ := context.WithTimeout(context.Background(), conf.Config.KnowlhubConfig.HealthzTimeout)
	ctx = helper.SetToken(ctx, conf.Config.Token)
	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "", conf.Config.Token)

	checkList := []func(context.Context){
		// 检查系统级服务
		// checkDocSvc,
		// 检查租户级服务
		checkHippoScope,
		checkHybase,
		// 检查每个知识库的模型服务
		checkVectorFulltextEngine,
		checkEmbeddingModelSvc,
		checkRerankModelSvc,
		// checkAugmentModelSvc,
		// checkCustomStrategy,
		// checkImageAugmentModelSvc,
		// checkVlModelSvc,
		// checkOcrLayoutSvc,
	}

	var wg sync.WaitGroup
	wg.Add(len(checkList))
	for _, check := range checkList {
		go func(f func(context.Context)) {
			defer wg.Done()
			f(ctx)
		}(check)
	}
	wg.Wait()

	// 将健康信息统一缓存到redis
	kbs, err := GetKnowledgeBaseManager().ListAllKnowledgeBases(ctx)
	if err != nil {
		stdlog.Errorf("failed to list all kbs")
		return
	}
	for _, kb := range kbs {
		HealthOverviewCache.Set(kb.Id, cvt2Overview(kb))
	}
	stdlog.Infof("cost %v to do knowledge health check", time.Since(start))
}

func recoverCheck(checkName string) {
	if err := recover(); err != nil {
		stdlog.Errorf("recover from %s, err:%+v \n", checkName, err)
	}
}

func checkDocSvc(ctx context.Context) {
	defer recoverCheck("checkDocSvc")
	ok, msg := GetDocHandler().Healthz(ctx)
	if ok != DocSvcHealthCache.Healthy || msg != DocSvcHealthCache.Message {
		stdlog.Infof("[Doc Svc] health updated, healthy: %v, msg: %s", ok, msg)
	}
	DocSvcHealthCache.Healthy = ok
	DocSvcHealthCache.Message = msg
}

// checkVectorFulltextEngine 只检查配置了数据源的连接
func checkVectorFulltextEngine(ctx context.Context) {
	defer recoverCheck("checkVectorFulltextEngine")
	mgr := GetKnowledgeBaseManager()
	repo := mgr.q.KnowledgeBase
	kbs, err := repo.WithContext(ctx).Find()
	if err != nil {
		stdlog.WithError(err).Errorf("checkVectorFulltextEngine find distinct project_id")
		return
	}
	vectorConns := make(map[string]*pb.DataConnection)
	fulltextConns := make(map[string]*pb.DataConnection)
	for _, kb := range kbs {
		if kb.DatabaseConfig != nil && kb.DatabaseConfig.VectorDb != nil && kb.DatabaseConfig.VectorDb.Id != "" {
			vectorConns[kb.Id] = kb.DatabaseConfig.VectorDb
		}
		if kb.DatabaseConfig != nil && kb.DatabaseConfig.FulltextDb != nil && kb.DatabaseConfig.FulltextDb.Id != "" {
			fulltextConns[kb.Id] = kb.DatabaseConfig.FulltextDb
		}
	}

	vectorConnList := stdctn.Values(vectorConns)
	fulltextConnList := stdctn.Values(fulltextConns)

	wg := sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer wg.Done()
		stdsrv.SyncBatchCallGeneric(vectorConnList, func(conn *pb.DataConnection) error {
			hs := new(pb.HealthStatus)
			defer func() {
				HippoHealthCache.Set(conn.Id, hs)
			}()
			switch conn.Type {
			case pb.ConnectionType_HIPPO:
				cli, err := clients.GetDataConnectionHippoCli(ctx, "", conn)
				if err != nil {
					hs.Healthy = false
					hs.Message = err.Error()
					return nil
				}
				hs.Healthy, hs.Message = cli.Healthz()
			case pb.ConnectionType_HYBASE:
				hs.Healthy, hs.Message = clients.HybaseHealthz(conn)
			default:
				err = fmt.Errorf("unsupported vector db type: %s", conn.Type)
			}
			return nil
		})
	}()
	go func() {
		defer wg.Done()
		stdsrv.SyncBatchCallGeneric(fulltextConnList, func(conn *pb.DataConnection) error {
			hs := new(pb.HealthStatus)
			defer func() {
				ScopeHealthCache.Set(conn.Id, hs)
			}()
			switch conn.Type {
			case pb.ConnectionType_SCOPE:
				cli, err := clients.GetDataConnectionScopeCli(ctx, "", conn)
				if err != nil {
					hs.Healthy = false
					hs.Message = err.Error()
					return nil
				}
				hs.Healthy, hs.Message = stdes.NewESClient(cli).Healthz(ctx, DefaultMapping, ScopeTestData, ScopeTestQuery)
			case pb.ConnectionType_HYBASE:
				hs.Healthy, hs.Message = clients.HybaseHealthz(conn)
			default:
				err = fmt.Errorf("unsupported fulltext db type: %s", conn.Type)
			}
			return nil
		})
	}()
	wg.Wait()
}

func checkHybase(ctx context.Context) {
	defer recoverCheck("checkHybase")
	hs := new(pb.HealthStatus)
	if conf.Config.KnowlhubConfig.VectorEngine == pb.ConnectionType_HYBASE.String() || conf.Config.KnowlhubConfig.FullTextEngine == pb.ConnectionType_HYBASE.String() {
		hs.Healthy, hs.Message = clients.HybaseHealthz(nil)
	} else {
		return
	}
	mgr := GetKnowledgeBaseManager()
	// 获取所有知识库的proj_id范围
	var projIds []string
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.ProjectId).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkHybase find distinct project_id")
		return
	} else {
		for _, kb := range kbs {
			projIds = append(projIds, kb.ProjectId)
		}
	}
	if conf.Config.KnowlhubConfig.VectorEngine == pb.ConnectionType_HYBASE.String() {
		for _, projId := range projIds {
			HippoHealthCache.Set(projId, hs)
		}
	}
	if conf.Config.KnowlhubConfig.FullTextEngine == pb.ConnectionType_HYBASE.String() {
		for _, projId := range projIds {
			ScopeHealthCache.Set(projId, hs)
		}
	}
}

// checkHippoScope 检查各项目的hippo和scope的健康状态
func checkHippoScope(ctx context.Context) {
	defer recoverCheck("checkHippoScope")
	mgr := GetKnowledgeBaseManager()
	// 获取所有知识库的proj_id范围
	var projIds []string
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.ProjectId).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkHippo find distinct project_id")
		return
	} else {
		for _, kb := range kbs {
			projIds = append(projIds, kb.ProjectId)
		}
	}

	stdsrv.SyncBatchCall(projIds, func(projId string) error {
		var wg sync.WaitGroup
		wg.Add(2)
		go func(projId string) {
			defer wg.Done()
			hs := new(pb.HealthStatus)
			defer func() {
				if conf.Config.KnowlhubConfig.VectorEngine == pb.ConnectionType_HIPPO.String() {
					HippoHealthCache.Set(projId, hs)
				}
			}()
			if conf.Config.KnowlhubConfig.VectorEngine == pb.ConnectionType_HIPPO.String() {
				hippoCli, err := clients.GetProjHippoCli(ctx, projId)
				if err != nil {
					hs.Healthy = false
					hs.Message = err.Error()
					return
				}
				hs.Healthy, hs.Message = hippoCli.Healthz()
			}
		}(projId)

		go func(projId string) {
			defer wg.Done()
			hs := new(pb.HealthStatus)
			defer func() {
				if conf.Config.KnowlhubConfig.FullTextEngine == pb.ConnectionType_SCOPE.String() {
					ScopeHealthCache.Set(projId, hs)
				}
			}()
			if conf.Config.KnowlhubConfig.FullTextEngine == pb.ConnectionType_SCOPE.String() {
				es, err := clients.GetProjScopeCli(ctx, projId)
				if err != nil {
					hs.Healthy = false
					hs.Message = err.Error()
					return
				}
				hs.Healthy, hs.Message = stdes.NewESClient(es).Healthz(ctx, DefaultMapping, ScopeTestData, ScopeTestQuery)
			}
		}(projId)
		wg.Wait()
		return nil
	})
}

func checkEmbeddingModelSvc(ctx context.Context) {
	defer recoverCheck("checkEmbeddingModelSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.VectorModel).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkEmbModelSvc find distinct model svc")
		return
	} else {
		for _, kb := range kbs {
			ms := kb.VectorModel
			if ms == nil {
				continue
			}
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}
	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			EmbeddingHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = testEmbedding(ctx, ms)
		return nil
	})
}

func checkRerankModelSvc(ctx context.Context) {
	defer recoverCheck("checkRerankModelSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.RetrievalConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkRerankModelSvc find distinct RetrievalConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.RetrievalConfig != nil && kb.RetrievalConfig.RerankParams != nil && kb.RetrievalConfig.RerankParams.Model != nil
		})
		for _, kb := range kbs {
			ms := kb.RetrievalConfig.RerankParams.Model
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}

	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			RerankHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = testRerank(ctx, ms)
		return nil
	})
}

func checkAugmentModelSvc(ctx context.Context) {
	defer recoverCheck("checkAugmentModelSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.DocProcessingConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkAugmentModelSvc find distinct DocProcessingConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.DocProcessingConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig != nil &&
				kb.DocProcessingConfig.ChunkAugmentConfig.Enabled && kb.DocProcessingConfig.ChunkAugmentConfig.TextModel != nil
		})
		for _, kb := range kbs {
			ms := kb.DocProcessingConfig.ChunkAugmentConfig.TextModel
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}

	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			AugmentHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = testAugment(ctx, ms)
		return nil
	})
}

func checkImageAugmentModelSvc(ctx context.Context) {
	defer recoverCheck("checkImageAugmentModelSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.DocProcessingConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkAugmentModelSvc find distinct DocProcessingConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.DocProcessingConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig != nil &&
				kb.DocProcessingConfig.ChunkAugmentConfig.ImageEnable && kb.DocProcessingConfig.ChunkAugmentConfig.ImageModel != nil
		})
		for _, kb := range kbs {
			ms := kb.DocProcessingConfig.ChunkAugmentConfig.ImageModel
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}

	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			ImageModelHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = func() (bool, string) {
			err := pcli.CheckModelHealth(ctx, ms)
			if err != nil {
				return false, err.Error()
			}
			return true, ""
		}()
		return nil
	})
}

func checkVlModelSvc(ctx context.Context) {
	defer recoverCheck("checkVlModelSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.DocProcessingConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkVlModelSvc find distinct DocProcessingConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocLoadConfig != nil &&
				kb.DocProcessingConfig.DocLoadConfig.StrategyType == pb.DocLoadStrategyType_VL_MODEL &&
				kb.DocProcessingConfig.DocLoadConfig.ImageModel != nil
		})
		for _, kb := range kbs {
			ms := kb.DocProcessingConfig.DocLoadConfig.ImageModel
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}

	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			ImageModelHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = func() (bool, string) {
			err := pcli.CheckModelHealth(ctx, ms)
			if err != nil {
				return false, err.Error()
			}
			return true, ""
		}()
		return nil
	})
}

func checkOcrLayoutSvc(ctx context.Context) {
	defer recoverCheck("checkOcrLayoutSvc")
	mgr := GetKnowledgeBaseManager()
	msMap := make(map[string]*pb.ModelService) // ms id -> ms
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.DocProcessingConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkOcrLayoutSvc find distinct DocProcessingConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocLoadConfig != nil &&
				(kb.DocProcessingConfig.DocLoadConfig.StrategyType == pb.DocLoadStrategyType_VL_MODEL || kb.DocProcessingConfig.DocLoadConfig.StrategyType == pb.DocLoadStrategyType_HI_RES) &&
				kb.DocProcessingConfig.DocLoadConfig.DocModel != nil
		})
		for _, kb := range kbs {
			ms := kb.DocProcessingConfig.DocLoadConfig.DocModel
			msId := ms.Id
			if _, ok := msMap[msId]; !ok {
				msMap[msId] = ms
			}
		}
	}
	mss := stdctn.Values(msMap)
	stdsrv.SyncBatchCallGeneric(mss, func(ms *pb.ModelService) error {
		hs := new(pb.HealthStatus)
		defer func() {
			OcrLayoutSvcHealthCache.Set(ms.Id, hs)
		}()
		hs.Healthy, hs.Message = func() (bool, string) {
			err := pcli.CheckModelHealth(ctx, ms)
			if err != nil {
				return false, err.Error()
			}
			return true, ""
		}()
		return nil
	})
}

func checkCustomStrategy(ctx context.Context) {
	defer recoverCheck("checkCustomStrategy")
	mgr := GetKnowledgeBaseManager()
	svcIdSet := make(map[string]struct{})
	repo := mgr.q.KnowledgeBase
	if kbs, err := repo.WithContext(ctx).Distinct(repo.DocProcessingConfig).Find(); err != nil {
		stdlog.WithError(err).Errorf("checkCustomStrategy find distinct DocProcessingConfig")
		return
	} else {
		kbs = utils.FilterSlice(kbs, func(idx int) bool {
			kb := kbs[idx]
			return kb.DocProcessingConfig != nil && kb.DocProcessingConfig.AppletService != nil && kb.DocProcessingConfig.AppletService.ServiceId != ""
		})
		for _, kb := range kbs {
			svcId := kb.DocProcessingConfig.AppletService.ServiceId
			svcIdSet[svcId] = struct{}{}
		}
	}

	svcIds := stdctn.Keys(svcIdSet)
	stdsrv.SyncBatchCall(svcIds, func(svcId string) error {
		hs := new(pb.HealthStatus)
		defer func() {
			CustomStrategyHealthCache.Set(svcId, hs)
		}()
		ch, err := clients.AppletSvcCli.CheckSvcHealth(ctx, svcId)
		if err != nil {
			hs.Healthy = false
			hs.Message = err.Error()
			return nil
		}
		hs.Healthy, hs.Message = ch.Healthy, ch.Detail
		return nil
	})
}

func testEmbedding(ctx context.Context, ms *pb.ModelService) (ok bool, msg string) {
	ret, err := EmbeddingTexts(ctx, ms, []any{"test"})
	if err != nil {
		return false, err.Error()
	}
	if len(ret) != 1 {
		return false, "embedding result length not equal to input"
	}
	dim, err := GetVecModelDim(ms)
	if err != nil {
		return false, fmt.Sprintf("GetVecModelDim, err: %v", err)
	}
	if len(ret[0]) != dim {
		return false, "embedding result dimension not equal to model dimension"
	}
	return true, ""
}

func testRerank(ctx context.Context, ms *pb.ModelService) (ok bool, msg string) {
	ret, score, err := pcli.RerankTexts(ctx, ms, "a", []string{"a", "b"})
	if err != nil {
		return false, err.Error()
	}
	if len(ret) != 2 {
		return false, "rerank result length not equal to input"
	}
	if len(score) != 2 {
		return false, "rerank score length not equal to input"
	}
	return true, ""
}

func testAugment(ctx context.Context, ms *pb.ModelService) (ok bool, msg string) {
	req := &triton.LLMChatReq{
		Prompt: "You are a helpful assistant.",
		Query:  "hi",
		Stream: false,
		Stop:   false,
		Params: map[string]any{"temperature": conf.Config.KnowlhubConfig.AugmentQuestionsTemperature},
	}
	openaiReq, err := req.Cvt2OpenaiChatReq()
	if err != nil {
		return false, err.Error()
	}
	// 同步方式调用
	var ret string
	if err := pcli.SyncChat(ctx, ms, openaiReq, func(s string) error { ret = s; return nil }); err != nil {
		return false, err.Error()
	}
	if ret == "" {
		return false, "testAugment chat result is empty"
	}
	return true, ""
}

func getMLOpsRuntimeInfo(ctx context.Context, svc *serving.MLOpsServiceBaseInfo) (ok bool, msg string) {
	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, svc.ProjectId, conf.Config.Token)
	info, err := clients.MLOpsCli.GetRuntimeInfo(ctx, svc.Id)
	if err != nil {
		return false, err.Error()
	}
	if info.State == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE || info.StateInfo.State == "Available" {
		return true, ""
	} else {
		return false, info.StateInfo.Message
	}
}

func newHealthCache(comp string) *stdctn.ConcurrentMap[string, *pb.HealthStatus] {
	m := stdctn.NewConcurrentMap[string, *pb.HealthStatus]()
	m.BeforeSet(func(key string, oldH *pb.HealthStatus, newH *pb.HealthStatus) {
		if oldH == nil && newH == nil {
			stdlog.Infof("[%s] should not happen, previous and current HealthStatus both are nil]", comp)
			return
		}
		if oldH == nil {
			stdlog.Infof("[%s] health added, key: %s, healthy: %v, msg: %s", comp, key, newH.Healthy, newH.Message)
			return
		}
		if newH == nil {
			stdlog.Infof("[%s] health deleted, key: %s, healthy: %v, msg: %s", comp, key, oldH.Healthy, oldH.Message)
			return
		}
		if oldH.Healthy == newH.Healthy && oldH.Message == newH.Message {
			return
		}
		stdlog.Infof("[%s] health updated, key: %s, healthy: %v -> %v, msg: %v -> %s", comp, key, oldH.Healthy, oldH.Message, newH.Healthy, newH.Message)
	})
	return m
}

func StartHealthCheck() {
	startHealthCheckOnce.Do(func() {
		healthzState = &HealthzState{IntervalSeconds: int(conf.Config.KnowlhubConfig.HealthzInterval / time.Second)}

		HippoHealthCache = newHealthCache("Hippo")
		ScopeHealthCache = newHealthCache("Scope")
		EmbeddingHealthCache = newHealthCache("Embedding")
		RerankHealthCache = newHealthCache("Rerank")
		AugmentHealthCache = newHealthCache("Augment")
		CustomStrategyHealthCache = newHealthCache("Custom Strategy")
		ImageModelHealthCache = newHealthCache("Image Model")
		OcrLayoutSvcHealthCache = newHealthCache("Ocr Layout")

		HealthOverviewCache = clients2.NewRedisMap[pb.KnowledgeBaseHealthOverview](
			clients.RedisCli, applet.RedisKeyKnowledgeHealthCheck)

		refreshHealthzChan = make(chan struct{}, 1)
		// health check loop
		go func() {
			healthCheck()
			ticker := time.NewTicker(conf.Config.KnowlhubConfig.HealthzInterval)
			for {
				select {
				case <-ticker.C:
					healthCheck()
				case <-refreshHealthzChan:
					healthCheck()
				}
			}
		}()
	})
}

func getDefaultOverview() *pb.KnowledgeBaseHealthOverview {
	return &pb.KnowledgeBaseHealthOverview{Healthy: true}
}

func cvt2Overview(kb *models.KnowledgeBase) *pb.KnowledgeBaseHealthOverview {
	ho := new(pb.KnowledgeBaseHealthOverview)
	healthy := true

	ho.DocSvcHealth = DocSvcHealthCache
	healthy = healthy && DocSvcHealthCache.Healthy
	projId := kb.ProjectId
	if kb.DatabaseConfig != nil && kb.DatabaseConfig.VectorDb != nil && kb.DatabaseConfig.VectorDb.Id != "" {
		connId := kb.DatabaseConfig.VectorDb.Id
		if h, ok := HippoHealthCache.Get(connId); ok {
			ho.HippoHealth = h
			healthy = healthy && h.Healthy
		}
	} else {
		if h, ok := HippoHealthCache.Get(projId); ok {
			ho.HippoHealth = h
			healthy = healthy && h.Healthy
		}
	}
	if kb.DatabaseConfig != nil && kb.DatabaseConfig.FulltextDb != nil && kb.DatabaseConfig.FulltextDb.Id != "" {
		connId := kb.DatabaseConfig.FulltextDb.Id
		if h, ok := ScopeHealthCache.Get(connId); ok {
			ho.ScopeHealth = h
			healthy = healthy && h.Healthy
		}
	} else {
		if h, ok := ScopeHealthCache.Get(projId); ok {
			ho.ScopeHealth = h
			healthy = healthy && h.Healthy
		}
	}
	if h, ok := EmbeddingHealthCache.Get(kb.VectorModel.Id); ok {
		ho.EmbeddingHealth = h
		healthy = healthy && h.Healthy
	}
	if kb.RetrievalConfig != nil && kb.RetrievalConfig.RerankParams != nil && kb.RetrievalConfig.RerankParams.Model != nil {
		if h, ok := RerankHealthCache.Get(kb.RetrievalConfig.RerankParams.Model.Id); ok {
			ho.RerankHealth = h
			healthy = healthy && h.Healthy
		}
	}
	// if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig.TextModel != nil {
	// 	if h, ok := AugmentHealthCache.Get(kb.DocProcessingConfig.ChunkAugmentConfig.TextModel.Id); ok {
	// 		ho.AugmentHealth = h
	// 		healthy = healthy && h.Healthy
	// 	}
	// }
	// if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig != nil && kb.DocProcessingConfig.ChunkAugmentConfig.ImageModel != nil {
	// 	if h, ok := ImageModelHealthCache.Get(kb.DocProcessingConfig.ChunkAugmentConfig.ImageModel.Id); ok {
	// 		ho.ImageAugmentHealth = h
	// 		healthy = healthy && h.Healthy
	// 	}
	// }
	// if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.AppletService != nil && kb.DocProcessingConfig.AppletService.ServiceId != "" {
	// 	if h, ok := CustomStrategyHealthCache.Get(kb.DocProcessingConfig.AppletService.ServiceId); ok {
	// 		ho.CustomStrategyHealth = h
	// 		healthy = healthy && h.Healthy
	// 	}
	// }
	// if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocLoadConfig != nil && kb.DocProcessingConfig.DocLoadConfig.ImageModel != nil {
	// 	if h, ok := ImageModelHealthCache.Get(kb.DocProcessingConfig.DocLoadConfig.ImageModel.Id); ok {
	// 		ho.VlHealth = h
	// 		healthy = healthy && h.Healthy
	// 	}
	// }
	// if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocLoadConfig != nil && kb.DocProcessingConfig.DocLoadConfig.DocModel != nil {
	// 	if h, ok := OcrLayoutSvcHealthCache.Get(kb.DocProcessingConfig.DocLoadConfig.DocModel.Id); ok {
	// 		ho.OcrLayoutHealth = h
	// 		healthy = healthy && h.Healthy
	// 	}
	// }
	ho.Healthy = healthy
	return ho
}

// ApplyCache 健康信息、关联服务等信息会依赖redis
func ApplyCache(ctx context.Context, kbInfos []*pb.KnowledgeBaseInfo) error {
	if err := SetRelatedApps(ctx, kbInfos); err != nil {
		return stderr.Wrap(err, "set related app")
	}
	if err := SetHealthOverview(ctx, kbInfos); err != nil {
		return stderr.Wrap(err, "set health overview")
	}
	return nil
}

func SetHealthOverview(ctx context.Context, kbInfos []*pb.KnowledgeBaseInfo) error {
	hos, err := HealthOverviewCache.GetAll()
	if err != nil {
		return err
	}
	for _, kbInfo := range kbInfos {
		if kbInfo.KnowledgeBase == nil {
			continue
		}
		ho, ok := hos[kbInfo.KnowledgeBase.Id]
		if ok {
			kbInfo.Health = ho
		} else {
			// 新建知识库缺少缓存的健康信息
			kbInfo.Health = getDefaultOverview()
		}
	}
	return nil
}
