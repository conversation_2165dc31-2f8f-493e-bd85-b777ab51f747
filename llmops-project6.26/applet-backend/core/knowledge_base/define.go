package knowledge_base

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"reflect"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"unicode/utf8"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb/common"

	"transwarp.io/applied-ai/applet-backend/core/applet"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/applet-backend/clients"
	pclients "transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/applied-ai/applet-backend/conf"

	"github.com/google/uuid"
	"gorm.io/gen/field"
	"gorm.io/gorm"

	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	vstd "transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	docEngine "transwarp.io/applied-ai/doc-engine-go/v1"
)

type KnowledgeBaseManager struct {
	pb.UnimplementedKnowledgeBaseManagerServer
	q    *query.Query
	rbac *cas.RBACApi
}

var (
	kbm                                 *KnowledgeBaseManager
	kbmOnce                             sync.Once
	ErrCouldNotUpdateExternalKB         = stderr.BadRequest.Error("could not update external knowledge base")
	ErrNilDocProcessingConfig           = stderr.Internal.Error("doc processing config is nil")
	ErrNilChunkAugmentConfig            = stderr.Internal.Error("chunk augment config is nil")
	ErrNilRetrievalConfig               = stderr.BadRequest.Error("retrieval config is nil")
	ErrMixedStrategyWithNilRerankParams = stderr.BadRequest.Error("mixed strategy with nil rerank params")

	DefaultDocProcessingConfig = &pb.DocProcessingConfig{
		AutoConfigured:              true,
		DocProcessingStrategyOrigin: pb.StrategyOrigin_PRESET,
		DocSplitConfig: &pb.DocSplitConfig{
			Separators:        []string{"\n\n", "\n", "。"},
			ChunkSize:         500,
			ChunkOverlap:      30,
			SplitStrategyType: pb.DocSplitStrategyType_CHARACTER,
		},
	}
)

const (
	DefaultTopK               = 20
	DefaultScoreThreshold     = 0.0
	EmbeddingContentMaxLength = 500 // 向量化文本的最大长度

	InnerIdField       = "chunk_id" // chunk id
	InnerOriIdField    = "ori_id"   // original chunk id
	InnerTitleField    = "doc_id"   // document id
	InnerVecField      = "feature"
	InnerTextField     = "text"
	InnerPriorityField = "priority" // 优先级属性
	InnerShortIdField  = "short_id" // 意义等同doc_id, 用于压缩查询语句的长度

	AgentConfigKbNamePrefix = "agent-"
	KeyOutputVectorDims     = "output_vector_dims"
	KeyInputMaxToken        = "input_max_token"

	PreviewHeadPages    = 3
	NonPreviewHeadPages = 0

	AlreadySubmitVector   = "already-submit-vector"
	AlreadySubmitFullText = "already-submit-fulltext"

	maxRecallCount = 5

	chunkTagIndex    = "indexed"
	chunkTagReturned = "returned"
	chunkTagVector   = "vector"
	chunkTagCut      = "cut"

	updateImportStatusUrlFmt = "/api/process/tasks/%s/update-import-status"
	getFileAsset             = "/api/assets/file-assets/%s"
	exportFileAsset          = "/api/assets/file-asset/%s/export"
	queryByAssetIds          = "/api/assets/file-assets/query-by-ids"
	deletetaskFiles          = "/api/process/tasks/files"
	deleteKbTasks            = "/api/process/tasks/%s/kb?project=%s"
)

type KbMetricsType string

const (
	KbMetricsTypeVisit   KbMetricsType = "VISIT"
	KbMetricsTypeClone   KbMetricsType = "CLONE"
	KbMetricsTypeExecute KbMetricsType = "EXECUTE"
)

func GetKnowledgeBaseManager() *KnowledgeBaseManager {
	kbmOnce.Do(func() {
		kbm = &KnowledgeBaseManager{
			q:    dao.InitQuery(),
			rbac: cas.NewRBACApi("", conf.Config.Token),
		}
	})
	return kbm
}

func (m *KnowledgeBaseManager) ListAllKnowledgeBases(ctx context.Context) ([]*models.KnowledgeBase, error) {
	return m.q.KnowledgeBase.WithContext(ctx).Find()
}

func (m *KnowledgeBaseManager) ListKnowledgeBases(ctx context.Context, req *pb.ListKnowledgeBasesReq) (*pb.ListKnowledgeBasesRsp, error) {
	if err := m.checkUserContextAllowEmptyProjectID(ctx, req.UserContext); err != nil {
		return nil, err
	}
	// 知识体验界面-仅查询已发布的
	isKbExperienceMode := false
	if req.ListSelector != nil && len(req.ListSelector.IsPublished) == 1 && req.ListSelector.IsPublished[0] == true {
		isKbExperienceMode = true
	}

	// 从mlops中查询已发布的知识库服务
	svcMap, err := m.listKbSvcAsMap(ctx, req.UserContext.ProjectId)
	if err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase

	projectId := req.UserContext.ProjectId
	condition := repo.Where(repo.IsVisible.Is(true))

	if projectId != "" {
		oriCon := repo.Where(repo.ProjectId.Eq(projectId))
		if req.IsPublic {
			oriCon = oriCon.Or(repo.IsPublic.Is(true))
		}
		condition = condition.Where(oriCon)
	} else if req.IsPublic {
		condition = condition.Where(repo.IsPublic.Is(true))
	}

	if req.ListSelector != nil {
		if len(req.ListSelector.ContentTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.ContentTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.ContentType.In(fields...))
		}

		if len(req.ListSelector.RegistryTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.RegistryTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.RegistryType.In(fields...))
		}

		if len(req.ListSelector.SourceTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.SourceTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.SourceType.In(fields...))
		}

		if len(req.ListSelector.IsPublished) == 1 && !conf.Config.IsSimpleMode {
			condition = condition.Where(repo.IsPublished.Is(req.ListSelector.IsPublished[0]))
		}

		if len(req.ListSelector.SceneTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.SceneTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.SceneType.In(fields...))
		}

		if len(req.ListSelector.Creators) > 0 {
			condition = condition.Where(repo.Creator.In(req.ListSelector.Creators...))
		}

	}

	kbs, err := condition.Find()
	if err != nil {
		return nil, stderr.Wrap(err, "list kb error.")
	}

	// 权限获取
	adminFlag := false
	ids := make(map[string]cas.Act)
	username := req.UserContext.UserName
	if username != "" {
		rbac, err := m.rbac.ListObject(context.Background(), username, cas.WithProjectId(projectId), cas.WithObjType(cas.ObjType_KnowledgeBase))
		if err != nil {
			return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, get user permission error.")
		}
		if rbac.AccessType == cas.AccessType_Unrestricted {
			// 说明有所有权限
			adminFlag = true
		} else {
			// 有可能是新用户，obj为空
			for _, o := range rbac.Objects {
				ids[o.ObjId] = o.Act
			}
		}
	}

	kbs = utils.FilterSlice(kbs, func(idx int) bool {
		// 1.1、如果是知识管理界面,根据权限过滤。如果是知识体验界面,返回true表示不进行过滤,在1.3步骤进行过滤
		if isKbExperienceMode {
			return true
		}
		kb := kbs[idx]
		// 根据权限过滤
		if !adminFlag {
			if kb.Creator == username {
				kb.PermissionAction = cas.Act_All
				if kb.PermissionCfg == nil {
					kb.PermissionCfg = &cas.PermissionCfg{
						PermissionMode: cas.PermissionMode_Public,
						PublicType:     cas.PublicType_All,
					}
				}
			} else if kb.PermissionCfg == nil {
				// 老数据，认为有所有权限
				kb.PermissionAction = cas.Act_All
				kb.PermissionCfg = &cas.PermissionCfg{
					PermissionMode: cas.PermissionMode_Public,
					PublicType:     cas.PublicType_All,
				}
			} else if kb.PermissionCfg.PermissionMode == cas.PermissionMode_Public {
				if kb.PermissionCfg.PublicType == cas.PublicType_All {
					kb.PermissionAction = cas.Act_All
				} else if kb.PermissionCfg.PublicType == cas.PublicType_Readonly {
					kb.PermissionAction = cas.Act_ReadOnly
				}
			} else if kb.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
				if act, ok := ids[kb.Id]; ok {
					kb.PermissionAction = act
				} else {
					return false
				}
			} else {
				return false
			}
		} else {
			kb.PermissionAction = cas.Act_All
			if kb.PermissionCfg == nil {
				kb.PermissionCfg = &cas.PermissionCfg{
					PermissionMode: cas.PermissionMode_Public,
					PublicType:     cas.PublicType_All,
				}
			}
		}
		// 根据Labels过滤
		if req.ListSelector != nil && len(req.ListSelector.Labels) > 0 {
			// 如果kb.Labels为空，直接返回false
			if kb.Labels == nil {
				return false
			}

			// 遍历所有标签条件
			for key, values := range req.ListSelector.Labels {
				// 如果Values为空，忽略此条件
				if len(values.Values) == 0 {
					continue
				}

				// 检查kb.Labels中是否有对应的key
				if v, ok := kb.Labels[key]; ok {
					// 只要kb.Labels中的标签值在values.Values中，就满足条件
					if slices.Contains(values.Values, v) {
						return true
					}
				}
			}
			// 如果遍历完所有条件都不满足，返回false
			return false
		}
		return true
	})

	ret := make([]*pb.KnowledgeBaseInfo, 0)
	// 改为使用一个sql查询
	kbIds := make([]string, 0)
	for _, kb := range kbs {
		kbIds = append(kbIds, kb.Id)
	}
	kbCount, err := GetDocumentStore().CountByKnowledgeBases(kbIds)
	if err != nil {
		return nil, stderr.Wrap(err, "Count kb docs error.")
	}
	err = stdsrv.SyncBatchCallGenericWithIdx(kbs, func(i int, kb *models.KnowledgeBase) error {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			stdlog.Errorf("kb [%s] get handler error:%v", kb.Id, err)
			ret = append(ret, &pb.KnowledgeBaseInfo{KnowledgeBase: kb.ToPb()})
			return nil
		}
		var numDocs int32
		if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			docEngineKb, err := handler.(*InnerHandler).docEngine.GetKnowledgeBase(ctx)
			if err != nil {
				stdlog.WithError(err).Errorf("CountDocEngineDocuments")
			} else {
				numDocs = docEngineKb.Result.DocsNum
			}
		} else {
			if v, ok := kbCount[kb.Id]; ok {
				numDocs = int32(v)
			}
		}
		info := &pb.KnowledgeBaseInfo{
			KnowledgeBase:               kb.ToPb(),
			NumDocs:                     numDocs,
			SupportedRetrieveStrategies: handler.SupportedRetrieveStrategies(),
		}
		if err != nil {
			// skipping
			stdlog.Errorf("query kb info %s, err:%v", kb.Id, err)
			info = &pb.KnowledgeBaseInfo{KnowledgeBase: kb.ToPb()}
		}
		info.KnowledgeBase.IsPublished = false
		if svc, exist := svcMap[info.KnowledgeBase.Id]; exist {
			info.KnowledgeBase.IsPublished = true
			if info.KnowledgeBase.PublishInfo == nil {
				info.KnowledgeBase.PublishInfo = &pb.KnowledgeBasePublishInfo{}
			}
			info.KnowledgeBase.PublishInfo.PermissionCfg = svc.PermissionCfg
			info.KnowledgeBase.PublishInfo.PermissionAction = svc.PermissionAction
		}

		// 1.2、知识管理界面,已发布与未发布的服务都需要展示
		if !isKbExperienceMode {
			ret = append(ret, info)
		}
		// 1.3、知识体验界面,只展示已发布的服务
		if isKbExperienceMode && info.KnowledgeBase.IsPublished {
			ret = append(ret, info)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	start := time.Now()
	if err := ApplyCache(ctx, ret); err != nil {
		return nil, err
	}
	stdlog.Infof("cost %v to apply cache", time.Since(start))
	return &pb.ListKnowledgeBasesRsp{Result: ret}, nil
}

// listKbSvcAsMap 查询有哪些已经部署的知识库服务  kb_id -> svc_info
func (m *KnowledgeBaseManager) listKbSvcAsMap(ctx context.Context, projectId string) (map[string]*serving.MLOpsServiceBaseInfo, error) {
	retMap := make(map[string]*serving.MLOpsServiceBaseInfo)
	if conf.Config.IsSimpleMode {
		return retMap, nil
	}
	start := time.Now()
	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, projectId, conf.Config.Token)
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_KNOWLEDGE},
		SourceMetaExtra: map[string]string{
			KbProjectIdMetaKey: helper.GetProjectID(ctx),
		},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "list kb svc as map")
	}
	elapsed := time.Since(start)
	stdlog.Infof("cost %v to execute clients.MLOpsCli.Cli.List", elapsed)

	for _, s := range svcs.ServiceInfos {
		if s.SourceMeta == nil || s.SourceMeta.Extra == nil {
			return nil, stderr.Internal.Error("svc :%v no source meta", s.Id)
		}
		extra := s.SourceMeta.Extra
		kbId, ok := extra[KbIdMetaKey]
		if !ok {
			return nil, stderr.Internal.Error("svc source meta no chain id key")
		}
		retMap[kbId] = s
	}
	return retMap, nil
}

func (m *KnowledgeBaseManager) GetKnowledgeBase(ctx context.Context, req *pb.GetKnowledgeBaseReq) (*pb.GetKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	ret, err := m.takeKnowledgeBase(req.Id)
	if err != nil {
		return nil, err
	}

	// 权限获取
	adminFlag := false
	ids := make(map[string]cas.Act)
	username := req.UserContext.UserName
	if username != "" {
		rbac, err := m.rbac.ListObject(context.Background(), username, cas.WithProjectId(req.UserContext.ProjectId), cas.WithObjType(cas.ObjType_KnowledgeBase))
		if err != nil {
			return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, get user permission error.")
		}
		if rbac.AccessType == cas.AccessType_Unrestricted {
			// 说明有所有权限
			adminFlag = true
		} else {
			// 有可能是新用户，obj为空
			for _, o := range rbac.Objects {
				ids[o.ObjId] = o.Act
			}
		}
	}
	if !adminFlag {
		if ret.Creator == username {
			// 创建者无视权限
			ret.PermissionAction = cas.Act_All
			if ret.PermissionCfg == nil {
				ret.PermissionCfg = &cas.PermissionCfg{
					PermissionMode: cas.PermissionMode_Public,
					PublicType:     cas.PublicType_All,
				}
			}
		} else if ret.PermissionCfg == nil {
			// 老数据，认为有所有权限
			ret.PermissionAction = cas.Act_All
			ret.PermissionCfg = &cas.PermissionCfg{
				PermissionMode: cas.PermissionMode_Public,
				PublicType:     cas.PublicType_All,
			}
		} else if ret.PermissionCfg.PermissionMode == cas.PermissionMode_Public {
			if ret.PermissionCfg.PublicType == cas.PublicType_All {
				ret.PermissionAction = cas.Act_All
			} else if ret.PermissionCfg.PublicType == cas.PublicType_Readonly {
				ret.PermissionAction = cas.Act_ReadOnly
			}
		} else if ret.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
			if act, ok := ids[ret.Id]; ok {
				ret.PermissionAction = act
			} else {
				return nil, stderr.Unauthorized.Errorf("forbidden")
			}
		} else {
			return nil, stderr.Unauthorized.Errorf("forbidden")
		}
	} else {
		ret.PermissionAction = cas.Act_All
		if ret.PermissionCfg == nil {
			ret.PermissionCfg = &cas.PermissionCfg{
				PermissionMode: cas.PermissionMode_Public,
				PublicType:     cas.PublicType_All,
			}
		}
	}

	info, err := m.queryKnowledgeBaseInfo(ctx, ret)
	if err != nil {
		return nil, stderr.Wrap(err, "query kb info %s", req.Id)
	}

	if err := ApplyCache(ctx, []*pb.KnowledgeBaseInfo{info}); err != nil {
		return nil, err
	}
	return &pb.GetKnowledgeBaseRsp{Result: info}, nil
}

func (m *KnowledgeBaseManager) CreateKnowledgeBase(ctx context.Context, req *pb.CreateKnowledgeBaseReq) (rsp *pb.CreateKnowledgeBaseRsp, err error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb := models.FromKnowledgeBasePb(req.Base)
	kb.Creator = req.UserContext.UserName
	kb.CreateTime = time.Now()
	kb.UpdateTime = time.Now()
	kb.ProjectId = req.UserContext.ProjectId
	kb.Id = uuid.NewString()
	kb.IsVisible = true
	kb.IsRetrievable = true
	kb.CreationType = pb.KnowledgeBaseCreationType_FROM_MANAGEMENT
	kb.Version = KbVersion
	if kb.RetrievalConfig == nil || kb.RetrievalConfig.AutoConfigured {
		rc, err := DefaultRetrievalConfig(kb.ProjectId)
		if err != nil {
			return nil, err
		}
		if kb.DocProcessingConfig != nil &&
			kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		kb.RetrievalConfig = rc
	}
	if kb.DocProcessingConfig == nil || kb.DocProcessingConfig.AutoConfigured {
		kb.DocProcessingConfig = DefaultDocProcessingConfig
	}

	if kb.DocProcessingConfig != nil &&
		kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		docKnowledgeBaseReq := new(docEngine.CreateKnowledgeBaseReq)
		docKnowledgeBaseReq.Name = kb.Id
		docKnowledgeBaseReq.ProjectId = kb.ProjectId
		docKnowledgeBaseReq.TenantId = helper.GetTenantID(ctx)
		docKnowledgeBaseReq.IndexBuildConf = docEngine.IndexBuildConfReq{
			IndexTypeArr: docEngine.DefaultIndexType(),
		}
		docSchema := new(docEngine.EntitySchemaConf)
		err = json.Unmarshal([]byte(kb.DocProcessingConfig.DocLoadConfig.EntitySchemaConf), &docSchema)
		if err != nil {
			return nil, stderr.Wrap(err, "transfer the schema of doc engine failed!")
		}
		docKnowledgeBaseReq.EntitySchemaConf = *docSchema
		handler.(*InnerHandler).docEngine.CreateKnowledgeBase(ctx, docKnowledgeBaseReq)
	}

	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		docKnowledgeBaseReq := &docEngine.CreateKnowledgeBaseV2Req{
			DocEngineConfig: kb.RetrievalConfig.DocEngineConfig,
		}
		docKnowledgeBaseReq.DocEngineConfig.GroupName = kb.Id
		_, err = handler.(*InnerHandler).docEngine.CreateKnowledgeBaseV2(ctx, docKnowledgeBaseReq)
		if err != nil {
			return nil, stderr.Wrap(err, "create knowledge base v2 failed")
		}
	}

	if kb.VectorModel == nil {
		ms, err := DefaultVecModelSvc(kb.ProjectId)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb: get default vec model")
		}
		kb.VectorModel = ms
	}

	// 检查向量维度
	if kb.VectorModel != nil {
		_, err := GetVecModelDim(kb.VectorModel)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb: GetVecModelDim")
		}
	}

	// 如果设置了默认数据连接
	if kb.DatabaseConfig == nil || kb.DatabaseConfig.UseDefault {
		kb.DatabaseConfig = &pb.DatabaseConfig{
			VectorDb: &pb.DataConnection{
				Id:   "",
				Type: pb.ConnectionType(pb.ConnectionType_value[conf.Config.KnowlhubConfig.VectorEngine]),
			},
			FulltextDb: &pb.DataConnection{
				Id:   "",
				Type: pb.ConnectionType(pb.ConnectionType_value[conf.Config.KnowlhubConfig.FullTextEngine]),
			},
			UseDefault: true,
		}
	}

	// 如果权限是自定义，需要单独存入cas
	if kb.PermissionCfg != nil && kb.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
		reqs := make([]*cas.PutObjectReq, 0)
		for _, p := range kb.PermissionCfg.CustomPolicies {
			reqs = append(reqs, p.ToPolicyPutReq(kb.ProjectId))
		}
		_, err = m.rbac.PutObject(context.Background(), kb.Id, cas.ObjType_KnowledgeBase, reqs)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb, put rbac objects error.")
		}
		// 不保存polices
		kb.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
	}

	repo := m.q.KnowledgeBase
	if err := repo.Create(kb); err != nil {
		return nil, stderr.Wrap(err, "create kb err")
	}
	defer func() {
		if err != nil {
			if _, err := repo.Delete(kb); err != nil {
				stdlog.WithError(err).Errorf("delete kb %s after failed to create", kb.Id)
			}
		}
	}()

	ret, err := m.GetKnowledgeBase(ctx, &pb.GetKnowledgeBaseReq{
		UserContext: req.UserContext,
		Id:          kb.Id,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "Create kb: get kb %s", kb.Id)
	}

	return &pb.CreateKnowledgeBaseRsp{Result: ret.Result.KnowledgeBase}, nil
}

func (m *KnowledgeBaseManager) UpdateKnowledgeBase(ctx context.Context, req *pb.UpdateKnowledgeBaseReq) (*pb.UpdateKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase
	kb := models.FromKnowledgeBasePb(req.Base)
	kb.UpdateTime = time.Now()

	if kb.RetrievalConfig == nil || kb.RetrievalConfig.AutoConfigured {
		rc, err := DefaultRetrievalConfig(kb.ProjectId)
		if err != nil {
			return nil, err
		}
		if kb.DocProcessingConfig != nil &&
			kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		kb.RetrievalConfig = rc
	}
	if kb.DocProcessingConfig == nil || kb.DocProcessingConfig.AutoConfigured {
		kb.DocProcessingConfig = DefaultDocProcessingConfig
	}

	// 如果权限是自定义，需要单独存入cas
	if kb.PermissionCfg != nil && kb.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
		reqs := make([]*cas.PutObjectReq, 0)
		for _, p := range kb.PermissionCfg.CustomPolicies {
			reqs = append(reqs, p.ToPolicyPutReq(kb.ProjectId))
		}
		_, err := m.rbac.PutObject(context.Background(), kb.Id, cas.ObjType_KnowledgeBase, reqs)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb, put rbac objects error.")
		}
		// 不保存polices
		kb.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
	}

	if err := repo.Save(kb); err != nil {
		return nil, stderr.Wrap(err, "save kb %s", kb.Id)
	}

	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		docKnowledgeBaseReq := &docEngine.UpdateKnowledgeBaseV2Req{
			DocEngineConfig: kb.RetrievalConfig.DocEngineConfig,
		}
		docKnowledgeBaseReq.DocEngineConfig.GroupName = kb.Id
		_, err = handler.(*InnerHandler).docEngine.UpdateKnowledgeBaseV2(ctx, docKnowledgeBaseReq)
		if err != nil {
			return nil, stderr.Wrap(err, "update knowledge base [%s] v2 failed", kb.Id)
		}
	}

	if kb.DocProcessingConfig != nil &&
		kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		handler.(*InnerHandler).docEngine.UpdateKnowledgeBase(ctx)
	}

	return &pb.UpdateKnowledgeBaseRsp{
		Result: kb.ToPb(),
	}, nil
}

func (m *KnowledgeBaseManager) UpdateKnowledgeMetricsInfo(ctx context.Context, kbId string,
	actionType KbMetricsType,
) error {
	repo := m.q.KnowledgeBase
	model, err := m.takeKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	if model.MetricsInfo == nil {
		model.MetricsInfo = new(pb.MetricsInfo)
	}
	switch actionType {
	case KbMetricsTypeVisit:
		model.MetricsInfo.VisitTimes++
	case KbMetricsTypeClone:
		model.MetricsInfo.CloneTimes++
	case KbMetricsTypeExecute:
		model.MetricsInfo.ExecuteTimes++
	}
	if _, err := repo.Where(repo.Id.Eq(model.Id)).Updates(model); err != nil {
		return err
	}
	return nil
}

func (m *KnowledgeBaseManager) DeleteKnowledgeBases(ctx context.Context, req *pb.DeleteKnowledgeBasesReq) (*pb.DeleteKnowledgeBasesRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	projectID := req.UserContext.ProjectId
	repo := m.q.KnowledgeBase
	kbs, err := repo.Where(repo.Id.In(req.Ids...), repo.ProjectId.Eq(projectID)).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "find kbs %s err", req.Ids)
	}

	// Collect the IDs of the found knowledge bases
	foundIDs := make(map[string]bool)
	for _, kb := range kbs {
		foundIDs[kb.Id] = true
	}

	// Check for IDs that were not found
	var notFoundIDs []string
	for _, id := range req.Ids {
		if !foundIDs[id] {
			notFoundIDs = append(notFoundIDs, id)
		}
	}
	if len(notFoundIDs) > 0 {
		return nil, stderr.Internal.Errorf("kb id [%s] is not found in project [%s]", notFoundIDs, projectID)
	}
	// 删除发布的知识库服务
	for _, kb := range kbs {
		if kb.PublishInfo == nil || !kb.IsPublished {
			continue
		}
		_, err := clients.MLOpsCli.Cli.DeleteService(ctx, &serving.ServiceID{
			Id: kb.PublishInfo.Id,
		})
		if err != nil {
			stdlog.WithError(err).Errorf("failed to delete knowledge %s published service %s", kb.Id, kb.PublishInfo.Id)
		}
	}

	// delete kb records
	if _, err := repo.Where(repo.Id.In(req.Ids...)).Delete(); err != nil {
		return nil, stderr.Wrap(err, "delete kb %s err", req.Ids)
	}

	backCtx, _ := context.WithTimeout(context.Background(), time.Minute)
	helper.CopyBaseInfoBetweenContexts(ctx, backCtx)
	go func(ctx context.Context) {
		// delete related docs, chunks & elements
		for _, kb := range kbs {
			id := kb.Id
			err = GetDocumentStore().DeleteByKnowledgeBase(ctx, id)
			if err != nil {
				stdlog.WithError(err).Errorf("delete kb %s", id)
			}
			// delete indices
			handler, err := NewHandler(ctx, kb)
			if err != nil {
				continue
			}
			err = handler.Drop(ctx)
			if err != nil {
				stdlog.WithError(err).Errorf("delete kb %s", id)
			}
			// 删除知识库中的加工任务
			cvatUrl := fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, fmt.Sprintf(deleteKbTasks, kb.Id, kb.ProjectId))
			_, err = clients.CVATCli.HttpRpcCall(ctx, http.MethodDelete, cvatUrl, nil)
			if err != nil {
				stdlog.WithError(err).Errorf("call cvat api [%s] delete process tasks error.", cvatUrl)
			}
		}
	}(backCtx)

	return &pb.DeleteKnowledgeBasesRsp{}, nil
}

// 获取数据连接的库表列表, 包含表描述信息
func (m *KnowledgeBaseManager) ListConnectionTables(ctx context.Context, req *pb.ListConnectionTablesReq) (rsp *pb.ListConnectionTablesRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	var ret []*pb.TableInfo
	conn := req.Connection
	switch conn.Type {
	case pb.ConnectionType_HIPPO:
		ret, err = DetailHippoConn(ctx, conn)
	case pb.ConnectionType_SCOPE:
		ret, err = DetailESConn(ctx, conn)
	default:
		err = stderr.Internal.Error("unsupported conn type")
	}
	if err != nil {
		return nil, err
	}

	return &pb.ListConnectionTablesRsp{Tables: ret}, nil
}

func DetailESConn(ctx context.Context, conn *pb.DataConnection) (ret []*pb.TableInfo, err error) {
	es, err := Conn2ESCli(conn)
	if err != nil {
		return nil, err
	}
	cli := stdes.NewESClient(es)
	indices, err := cli.CatIndices(ctx)
	if err != nil {
		return nil, err
	}

	var names []string
	for _, idx := range indices {
		names = append(names, idx.Index)
	}

	mappings, err := cli.GetMappings(ctx, names)
	if err != nil {
		return nil, err
	}

	for _, index := range indices {
		ti := new(pb.TableInfo)
		ti.Name = index.Index
		ti.Rows, _ = strconv.ParseInt(index.DocsCount, 10, 64)
		bs, err := json.Marshal(mappings[index.Index])
		if err != nil {
			stdlog.Errorf("DetailESConn marshall mapping: %v", mappings[index.Index])
			continue
		}
		ti.Description = string(bs)
		ret = append(ret, ti)
	}

	return ret, nil
}

func DetailHippoConn(ctx context.Context, conn *pb.DataConnection) (ret []*pb.TableInfo, err error) {
	cli, err := Conn2HippoCli(conn)
	if err != nil {
		return nil, stderr.Wrap(err, "conn2HippoCli, conn:%v", conn)
	}

	tableListRsp, err := cli.ListTables(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "list tables")
	}

	tables := tableListRsp.Tables
	for _, tb := range tables {
		if tb == "" {
			continue
		}
		cntRsp, err := cli.CountTable(ctx, tb)
		if err != nil {
			return nil, stderr.Wrap(err, "count table, table:%v", tb)
		}
		tbDesc, err := cli.GetTable(ctx, conn.Database, tb)
		if err != nil {
			return nil, stderr.Wrap(err, "get table description, table:%v", tb)
		}
		descStr, err := json.Marshal(tbDesc)
		if err != nil {
			return nil, stderr.Wrap(err, "marshal table description, table")
		}

		info := &pb.TableInfo{
			Name:        tb,
			Rows:        cntRsp.Total,
			Description: string(descStr),
		}
		ret = append(ret, info)
	}
	return
}

// 获取知识库的文档列表，平铺形式
func (m *KnowledgeBaseManager) ListDocuments(ctx context.Context, req *pb.ListDocumentsReq) (*pb.ListDocumentsRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	return handler.ListDocuments(ctx)
}

func (m *KnowledgeBaseManager) ListDocumentsFast(ctx context.Context, baseId string) (*pb.ListDocumentsRsp, error) {
	docs, err := GetDocumentStore().LoadByKnowledgeBase(baseId)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.DocumentInfo, 0)
	for _, doc := range docs {
		ret = append(ret, &pb.DocumentInfo{
			Doc: doc.ToPb(),
			Prog: &pb.DocumentProcessingProgress{
				Stage: doc.Stage,
			},
		})
	}
	return &pb.ListDocumentsRsp{Result: ret}, nil
}

func (m *KnowledgeBaseManager) ListDocumentsByPage(ctx context.Context, req *pb.ListDocumentsByPageReq) (*pb.ListDocumentsByPageRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}
	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		return handler.(*InnerHandler).docEngine.ListDocumentsByPage(ctx, req.PageReq)
	}

	docs, err := GetDocumentStore().LoadByKnowledgeBase(kb.Id)
	if err != nil {
		return nil, err
	}
	// 使用内存进行分页，先根据标签进行过滤
	total := utils.FilterSlice(docs, func(idx int) bool {
		doc := docs[idx]
		// 根据文件名过滤
		if req.Name != "" {
			if !strings.Contains(doc.Name, req.Name) {
				return false
			}
		}
		// 根据Labels过滤
		if len(req.Labels) > 0 {
			// 没有文件资产信息，直接返回空
			if doc.FileAsset == nil {
				return false
			}
			if len(doc.FileAsset.Labels) == 0 {
				return false
			}

			// 遍历所有标签条件
			for key, values := range req.Labels {
				// 如果Values为空，忽略此条件
				if len(values.Values) == 0 {
					continue
				}

				// 检查doc.FileAsset.Labels中是否有对应的key
				if v, ok := doc.FileAsset.Labels[key]; ok {
					// v可能是多个值拼接的
					vs := strings.Split(v, "&&")
					flag := false
					for _, vv := range vs {
						if slices.Contains(values.Values, vv) {
							flag = true
						}
					}
					// 如果当前key的任何value都不匹配，直接返回false
					if !flag {
						return false
					}
				} else {
					// 如果doc.FileAsset.Labels中没有这个key，条件不满足
					return false
				}
			}
			// 所有条件都满足，返回true
			return true
		}
		return true
	})
	// 对total进行分页
	pageResult, err := m.docsPagination(total, req.PageReq)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.DocumentInfo, 0, len(pageResult))
	if len(pageResult) == 0 {
		return &pb.ListDocumentsByPageRsp{
			Result:   ret,
			Total:    int32(len(total)),
			PageSize: req.PageReq.PageSize,
			PageNum:  req.PageReq.Page,
		}, nil
	}
	for _, doc := range pageResult {
		strategyOrigin := new(pb.StrategyOrigin)
		if doc.DocProcessingConfig != nil {
			strategyOrigin = &doc.DocProcessingConfig.DocProcessingStrategyOrigin
		}
		di := &pb.DocumentInfo{
			Doc:            doc.ToPb(),
			Prog:           GetDocImportTaskManager().Get(doc.Id).DocumentProcessingProgress,
			StrategyOrigin: *strategyOrigin,
		}
		if doc.Stage == pb.DocumentTaskStage_WAITING_EXAMINING || doc.Stage == pb.DocumentTaskStage_WAITING {
			di.Prog.Stage = doc.Stage
		}
		di.NumChunks = doc.NumChunks
		di.NumSuccessChunks = doc.NumSuccessChunks
		ret = append(ret, di)
	}
	return &pb.ListDocumentsByPageRsp{
		Result:   ret,
		Total:    int32(len(total)),
		PageSize: req.PageReq.PageSize,
		PageNum:  req.PageReq.Page,
	}, nil
}

func (m *KnowledgeBaseManager) docsPagination(total []*models.Document, pageReq *pb.PageReq) ([]*models.Document, error) {
	if pageReq.Page <= 0 {
		pageReq.Page = 1
	}
	if pageReq.PageSize <= 0 {
		pageReq.PageSize = 10
	}
	if len(total) == 0 {
		return []*models.Document{}, nil
	}
	// 排序
	err := sortSlice(total, pageReq.SortBy, pageReq.IsDesc)
	if err != nil {
		stdlog.WithError(err).Warnf("sort error")
	}
	pageSize := int(pageReq.PageSize)
	pageNum := int(pageReq.Page)
	// 计算总页数
	totalPages := (len(total) + pageSize - 1) / pageSize

	// 处理页码边界条件
	if pageNum > totalPages {
		// 如果请求的页码超过总页数，返回空结果
		return []*models.Document{}, nil
	}

	// 计算分页范围
	startIndex := (pageNum - 1) * pageSize
	endIndex := startIndex + pageSize
	if endIndex > len(total) {
		endIndex = len(total)
	}

	// 创建结果切片
	resultSlice := total[startIndex:endIndex]
	return resultSlice, nil
}

// 排序切片
func sortSlice(slice []*models.Document, sortBy string, isDesc bool) error {
	if len(slice) == 0 {
		return nil
	}

	// 检查字段是否存在
	firstElem := reflect.ValueOf(slice[0])
	if firstElem.Kind() == reflect.Ptr {
		firstElem = firstElem.Elem()
	}

	if firstElem.Kind() != reflect.Struct {
		return fmt.Errorf("elements must be structs")
	}

	// 通过json tag查找字段
	fieldName, err := getFieldNameByTag(firstElem.Type(), sortBy)
	if err != nil {
		return err
	}

	fieldValue := firstElem.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		// 没有找到该字段，则不排序
		return nil
	}

	// 根据字段类型进行排序
	sort.Slice(slice, func(i, j int) bool {
		elem1 := reflect.ValueOf(slice[i])
		elem2 := reflect.ValueOf(slice[j])

		if elem1.Kind() == reflect.Ptr {
			elem1 = elem1.Elem()
		}
		if elem2.Kind() == reflect.Ptr {
			elem2 = elem2.Elem()
		}

		field1 := elem1.FieldByName(fieldName)
		field2 := elem2.FieldByName(fieldName)

		var result bool
		switch field1.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			result = field1.Int() < field2.Int()
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			result = field1.Uint() < field2.Uint()
		case reflect.Float32, reflect.Float64:
			result = field1.Float() < field2.Float()
		case reflect.String:
			result = field1.String() < field2.String()
		default:
			// 对于其他类型，尝试转换为字符串比较
			result = fmt.Sprintf("%v", field1.Interface()) < fmt.Sprintf("%v", field2.Interface())
		}

		if isDesc {
			return !result
		}
		return result
	})

	return nil
}

// 通过json tag获取字段名
func getFieldNameByTag(structType reflect.Type, tagValue string) (string, error) {
	for i := 0; i < structType.NumField(); i++ {
		f := structType.Field(i)

		// 首先检查json tag
		jsonTag := f.Tag.Get("json")
		if jsonTag != "" {
			// 处理json tag，去掉可能的选项（如omitempty）
			tagName := jsonTag
			if commaIndex := strings.Index(jsonTag, ","); commaIndex != -1 {
				tagName = jsonTag[:commaIndex]
			}

			// 如果json tag匹配，返回实际字段名
			if tagName == tagValue {
				return f.Name, nil
			}
		}

		// 如果没有json tag或json tag不匹配，检查字段名本身
		if f.Name == tagValue {
			return f.Name, nil
		}
	}

	return "", fmt.Errorf("field with json tag or name '%s' not found", tagValue)
}

// 启用/禁用 知识库文档
func (m *KnowledgeBaseManager) DisableDocument(ctx context.Context, req *pb.DisableDocumentReq) (*pb.DisableDocumentRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}
	repo := m.q.KnowledgeBase
	kb.DisabledDocs = updateDisabledDocs(kb.DisabledDocs, req.DocId, req.Disabled)
	if err := repo.Save(kb); err != nil {
		return nil, err
	}

	return &pb.DisableDocumentRsp{}, nil
}

// 获取知识库文档的分段内容列表，支持排序
func (m *KnowledgeBaseManager) ListDocumentChunks(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return handler.ListDocumentChunks(ctx, req.DocId, req.PageReq)
	}
	res, err := handler.ListDocumentChunksByReq(ctx, req)
	if err != nil {
		return nil, err
	}
	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		return res, err
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, err
	}
	// chunk打标记
	if doc.IndexConfig == nil {
		return res, nil
	}
	enabledMap, returnedMap := makeEnabledReturnedMap(doc.IndexConfig)
	for _, chunkInfo := range res.Result {
		tags := make(map[string]bool)
		if chunkInfo.Chunk.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_ABSTRACT {
			_, indexed := enabledMap[docSummary]
			tags[chunkTagIndex] = indexed
			_, returned := returnedMap[docSummary]
			tags[chunkTagReturned] = returned
		} else {
			_, indexed := enabledMap[docChunk]
			tags[chunkTagIndex] = indexed
			_, returned := returnedMap[docChunk]
			tags[chunkTagReturned] = returned
		}
		tags[chunkTagVector] = !chunkInfo.Chunk.DisableVectorIndexing
		tags[chunkTagCut] = !chunkInfo.Chunk.DisableVectorIndexing && isLongerThanEmedMaxLength(chunkInfo.Chunk.Content, kb.VectorModel)
		chunkInfo.Chunk.Tags = tags
		for _, ac := range chunkInfo.Chunk.AugmentedChunks {
			acTags := make(map[string]bool)
			if columnName, ok := indexConfigMap[ac.AugmentedType]; !ok {
				acTags[chunkTagIndex] = false
				acTags[chunkTagReturned] = false
				acTags[chunkTagVector] = false
				acTags[chunkTagCut] = false
			} else {
				_, indexed := enabledMap[columnName]
				acTags[chunkTagIndex] = indexed
				_, returned := returnedMap[docSummary]
				acTags[chunkTagReturned] = returned
				acTags[chunkTagVector] = !ac.DisableVectorIndexing
				acTags[chunkTagCut] = !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content, kb.VectorModel)
			}
			ac.Tags = acTags
		}
	}
	return res, nil
}

// 知识库检索接口
func (m *KnowledgeBaseManager) RetrieveKnowledgeBase(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	// 请求的RetrievalConfig为空时，使用知识库设置的RetrievalConfig
	rc := req.RetrievalConfig
	if rc == nil {
		rc = kb.RetrievalConfig
		req.RetrievalConfig = rc
	} else {
		if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			req.RetrievalConfig.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
			req.DisableRerank = true
		}
	}
	if rc == nil {
		return nil, ErrNilRetrievalConfig
	}

	if rc.Strategy == pb.KnowledgeBaseRetrieveStrategy_MIXED && rc.RerankParams == nil && !req.DisableRerank {
		return nil, ErrMixedStrategyWithNilRerankParams
	}

	// 1. Recall
	// 某些使用场景 仅指定了RerankRarams
	if rc.RecallParams == nil {
		rc.RecallParams = &pb.RecallParams{
			TopK:           DefaultTopK,
			ScoreThreshold: DefaultScoreThreshold,
		}
	}
	rsp, err := handler.Recall(ctx, req)
	if err != nil {
		return nil, err
	}

	// 1.5. 获取命中切片知识库的上下文
	linkedDocumentBatch := NewLinkedDocumentBatch()
	if req.RetrievalConfig.ContextNum > 0 {
		docs := make(map[string]struct{})
		for _, chunk := range rsp.Result {
			docs[chunk.DocId] = struct{}{}
		}
		go func() {
			linkedDocumentBatch.Start()
			for id := range docs {
				// 如果document数量太多会堵塞一会儿
				linkedDocumentBatch.Submit(id)
			}
			linkedDocumentBatch.End()
		}()
	} else {
		linkedDocumentBatch.Close()
	}

	if req.DisableRerank || rc.RerankParams == nil {
		rsp.Result = linkedDocumentBatch.Process(rsp.Result, req.RetrievalConfig.ContextNum)
		return rsp, nil
	}

	// 2. Rerank
	// RerankParams的模型未指定时，尝试从知识库设置中获取
	if rc.RerankParams.Model == nil {
		if kb.RetrievalConfig.RerankParams != nil && kb.RetrievalConfig.RerankParams.Model != nil {
			rc.RerankParams.Model = kb.RetrievalConfig.RerankParams.Model
		} else {
			return nil, stderr.InvalidParam.Error("rerank model must be set")
		}
	}
	chunks, err := Rerank(ctx, req.Query, req.RetrievalConfig.RerankParams, rsp.Result)
	if err != nil {
		return nil, err
	}

	rsp.Result = linkedDocumentBatch.Process(chunks, req.RetrievalConfig.ContextNum)
	return rsp, nil
}

// 获取知识库的文档列表，文档树形式
func (m *KnowledgeBaseManager) GetDocumentTree(ctx context.Context, req *pb.GetDocumentTreeReq) (*pb.GetDocumentTreeRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	return handler.GetDocumentTree(ctx)
}

// 新增文档到知识库(异步处理),允许自动创建知识库
func (m *KnowledgeBaseManager) SubmitFileToKnowledgeBase(ctx context.Context, req *pb.SubmitFileToKnowledgeBaseReq) (*pb.SubmitFileToKnowledgeBaseRsp, error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeOrCreateKnowledgeBase(req.KnowledgeBaseId, req.UserContext, req.AutoCreateKb)
	if err != nil {
		return nil, err
	}

	doc, err := GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
		FilePath:            req.FilePath,
		TableConfig:         req.TableConfig,
		CorpusConfig:        req.CorpusConfig,
		DocumentFileSource:  pb.DocumentFileSource_LOCAL_FILE, // 姑且都是本地文件
		DocProcessingConfig: req.DocProcessingConfig,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "file %s", req.FilePath)
	}

	dpc := req.DocProcessingConfig
	if dpc == nil {
		dpc = kb.DocProcessingConfig
	}
	if dpc == nil {
		return nil, stderr.Internal.Error("doc processing config must be set.")
	}
	task := NewDocTask(ctx, kb.Id, dpc, doc.ToPb())
	err = GetTaskManger().Submit(task)
	if err != nil {
		return nil, err
	}

	return &pb.SubmitFileToKnowledgeBaseRsp{
		Doc: doc.ToPb(),
	}, nil
}

// 从知识库移除文档
func (m *KnowledgeBaseManager) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) (*pb.RemoveFileFromKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	err = handler.RemoveFileFromKnowledgeBase(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.RemoveFileFromKnowledgeBaseRsp{}, nil
}

// 更新知识库状态(可见、可检索)
func (m *KnowledgeBaseManager) UpdateKnowledgeBaseState(ctx context.Context, req *pb.UpdateKnowledgeBaseStateReq) (*pb.UpdateKnowledgeBaseStateRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase
	cnt, err := repo.Where(repo.Id.Eq(req.Id)).Count()
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState count kb %s", req.Id)
	}
	if cnt < 1 {
		return nil, stderr.InvalidParam.Error("kb %s not found", req.Id)
	}

	_, err = repo.WithContext(ctx).Where(repo.Id.Eq(req.Id)).UpdateSimple(repo.IsVisible.Value(req.IsVisible), repo.IsRetrievable.Value(req.IsRetrievable))
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState update kb %s", req.Id)
	}

	kb, err := repo.Where(repo.Id.Eq(req.Id)).Take()
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState take kb %s after update", req.Id)
	}

	return &pb.UpdateKnowledgeBaseStateRsp{KnowledgeBase: kb.ToPb()}, nil
}

// 手动新建chunk
func (m *KnowledgeBaseManager) CreateChunk(ctx context.Context, req *pb.CreateChunkReq) (*pb.CreateChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	oriChunkId := req.OriChunkId
	var chunk *models.Chunk
	if oriChunkId == "" { // 新增原文chunk
		chunk = &models.Chunk{
			Id:              uuid.NewString(),
			Content:         req.Content,
			SourceType:      pb.ChunkSourceType_SOURCE_TYPE_CREATED,
			ContentType:     req.ContentType,
			DocumentId:      req.DocId,
			KnowledgeBaseId: req.KnowledgeBaseId,
		}
		// store
		err = GetChunkStore(req.UserContext.ProjectId).Store(chunk)
		if err != nil {
			return nil, err
		}
		// 修改document chunk数量
		docRepo := m.q.Document
		_, err = docRepo.Where(docRepo.Id.Eq(doc.Id)).UpdateSimple(docRepo.NumChunks.Add(1))
		if err != nil {
			stdlog.WithError(err).Warnf("add doc [%s] chunks num error.", doc.Id)
		}
		// indexing
		chunks := []*models.Chunk{chunk}
		if kb.ContentType == pb.KnowledgeBaseContentType_TABLE {
			chunks = GetDocImportTaskManager().processTableAugmentChunks(chunks, doc)
		}
		idxChunks := makeChunksForIndexingWithIndexConfig(doc, chunks, kb.VectorModel)
		err = handler.SubmitChunks(ctx, idxChunks)
		if err != nil {
			return nil, err
		}
		// 修改成功chunk数量
		_, err = docRepo.Where(docRepo.Id.Eq(doc.Id)).UpdateSimple(docRepo.NumSuccessChunks.Add(1))
		if err != nil {
			stdlog.WithError(err).Warnf("add doc [%s] chunks success num error.", doc.Id)
		}
	} else { // 新增知识增强chunk
		return nil, stderr.Internal.Errorf("this api does not support add augmented chunks.")
	}

	return &pb.CreateChunkRsp{Chunk: chunk.ToPb()}, nil
}

func (m *KnowledgeBaseManager) CreateAugmentChunk(ctx context.Context, req *pb.CreateAugmentChunkReq) (*pb.CreateAugmentChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}
	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}
	chunk, err := GetChunkStore(req.UserContext.ProjectId).Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, err
	}
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}
	priorityMap, _ := makePriorityWithIndexConfig(doc.IndexConfig)
	if req.AugmentChunk != nil {
		augChunk := req.AugmentChunk
		augChunk.Id = uuid.NewString()
		chunk.AugmentedChunks = append(chunk.AugmentedChunks, augChunk)
		// store
		err = GetChunkStore(req.UserContext.ProjectId).Store(chunk)
		if err != nil {
			return nil, err
		}
		// indexing
		if t, ok := indexConfigMap[req.AugmentChunk.AugmentedType]; ok {
			if p, ok := priorityMap[t]; ok {
				idxChunk := &models.ChunkForIndexing{
					Id:                      augChunk.Id,
					OriId:                   req.OriChunkId,
					DocId:                   chunk.DocumentId,
					KBId:                    chunk.KnowledgeBaseId,
					Text:                    augChunk.Content,
					DisableVectorIndexing:   augChunk.DisableVectorIndexing,
					DisableFullTextIndexing: augChunk.DisableFullTextIndexing,
					Priority:                p,
					ShortId:                 strconv.FormatInt(doc.ShortId, 16),
					Type:                    augChunk.AugmentedType.String(),
				}
				idxChunks := []*models.ChunkForIndexing{idxChunk}
				err = handler.SubmitChunks(ctx, idxChunks)
				if err != nil {
					return nil, err
				}
			}
		}
	} else if req.QaPair != nil {
		qaPair := req.QaPair
		qaPair.Question.Id = uuid.NewString()
		qaPair.Answer.Id = uuid.NewString()
		chunk.QaPairs = append(chunk.QaPairs, qaPair)
		// store
		err = GetChunkStore(req.UserContext.ProjectId).Store(chunk)
		if err != nil {
			return nil, err
		}
		// indexing
		idxChunks := []*models.ChunkForIndexing{}
		if p, ok := priorityMap[question]; ok {
			idxChunks = append(idxChunks, &models.ChunkForIndexing{
				Id:                      qaPair.Question.Id,
				OriId:                   req.OriChunkId,
				DocId:                   chunk.DocumentId,
				KBId:                    chunk.KnowledgeBaseId,
				Text:                    qaPair.Question.Content,
				DisableVectorIndexing:   qaPair.Question.DisableVectorIndexing,
				DisableFullTextIndexing: qaPair.Question.DisableFullTextIndexing,
				Priority:                p,
				Type:                    qaPair.Question.AugmentedType.String(),
				ShortId:                 strconv.FormatInt(doc.ShortId, 16),
				Extra:                   qaPair.Answer.Content,
			})
		}
		if p, ok := priorityMap[answer]; ok {
			idxChunks = append(idxChunks, &models.ChunkForIndexing{
				Id:                      qaPair.Answer.Id,
				OriId:                   req.OriChunkId,
				DocId:                   chunk.DocumentId,
				KBId:                    chunk.KnowledgeBaseId,
				Text:                    qaPair.Answer.Content,
				DisableVectorIndexing:   qaPair.Answer.DisableVectorIndexing,
				DisableFullTextIndexing: qaPair.Answer.DisableFullTextIndexing,
				Priority:                p,
				Type:                    qaPair.Answer.AugmentedType.String(),
				ShortId:                 strconv.FormatInt(doc.ShortId, 16),
			})
		}
		err = handler.SubmitChunks(ctx, idxChunks)
		if err != nil {
			return nil, err
		}
	}
	return &pb.CreateAugmentChunkRsp{
		Chunk: chunk.ToPb(),
	}, nil
}

func (m *KnowledgeBaseManager) DeleteChunk(ctx context.Context, req *pb.DeleteChunkReq) (*pb.DeleteChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	chunk, err := GetChunkStore(req.UserContext.ProjectId).Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}

	kbid := chunk.KnowledgeBaseId

	kb, err := m.takeKnowledgeBase(kbid)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	rsp := &pb.DeleteChunkRsp{}
	if req.ChunkId == req.OriChunkId { // 删除原文chunk
		if err = handler.DeleteChunksByOriId(ctx, []string{req.OriChunkId}); err != nil {
			return nil, err
		}

		// delete chunk
		if err = GetChunkStore(req.UserContext.ProjectId).Delete(req.OriChunkId); err != nil {
			return nil, err
		}
		// 更新chunk数量
		docRepo := m.q.Document
		_, err = docRepo.Where(docRepo.Id.Eq(chunk.DocumentId)).UpdateSimple(docRepo.NumChunks.Sub(1), docRepo.NumSuccessChunks.Sub(1))
		if err != nil {
			stdlog.WithError(err).Warnf("add doc [%s] chunks num error.", chunk.DocumentId)
		}
	} else { // 删除增强chunk
		if err = handler.DeleteChunksById(ctx, []string{req.ChunkId}); err != nil {
			return nil, err
		}

		// update chunk
		chunk.AugmentedChunks = utils.DelElement(chunk.AugmentedChunks, func(i int) bool {
			return chunk.AugmentedChunks[i].Id == req.ChunkId
		})

		if err = GetChunkStore(req.UserContext.ProjectId).Store(chunk); err != nil {
			return nil, err
		}
		rsp.Chunk = chunk.ToPb()
	}

	return rsp, nil
}

func (m *KnowledgeBaseManager) DeleteAugmentChunk(ctx context.Context, req *pb.DeleteAugmentChunkReq) (*pb.DeleteAugmentChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	chunk, err := GetChunkStore(req.UserContext.ProjectId).Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}

	kbid := chunk.KnowledgeBaseId

	kb, err := m.takeKnowledgeBase(kbid)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}
	rsp := &pb.DeleteAugmentChunkRsp{}
	if req.AugmentChunk != nil {
		// 删除增强切片
		if err = handler.DeleteChunksById(ctx, []string{req.AugmentChunk.Id}); err != nil {
			return nil, err
		}

		// update chunk
		chunk.AugmentedChunks = utils.DelElement(chunk.AugmentedChunks, func(i int) bool {
			return chunk.AugmentedChunks[i].Id == req.AugmentChunk.Id
		})

		if err = GetChunkStore(req.UserContext.ProjectId).Store(chunk); err != nil {
			return nil, err
		}
		rsp.Chunk = chunk.ToPb()
	} else if req.QaPair != nil {
		newQas := make([]*pb.QaPair, 0)
		for _, qa := range chunk.QaPairs {
			if qa.Question.Id == req.QaPair.Question.Id && qa.Answer.Id == req.QaPair.Answer.Id {
				// 删除
				ids := []string{qa.Question.Id, qa.Answer.Id}
				if err = handler.DeleteChunksById(ctx, ids); err != nil {
					return nil, err
				}
			} else {
				newQas = append(newQas, qa)
			}
		}
		chunk.QaPairs = newQas
		if err = GetChunkStore(req.UserContext.ProjectId).Store(chunk); err != nil {
			return nil, err
		}
		rsp.Chunk = chunk.ToPb()
	}
	return rsp, nil
}

func (m *KnowledgeBaseManager) UpdateChunk(ctx context.Context, req *pb.UpdateChunkReq) (*pb.UpdateChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, err
	}
	if doc.Stage == pb.DocumentTaskStage_INDEXING {
		// 入库中的切片暂时不允许修改
		return nil, stderr.Errorf("doc is indexing, cannot update chunk.")
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	c, err := GetChunkStore(req.UserContext.ProjectId).Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}

	ci := new(models.ChunkForIndexing)
	var augChunkType string
	if kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
		// 文档知识库
		c.Content = req.Content
		c.Edited = true
		ci = refreshChunkIdxSetting(c, kb.VectorModel)
		augChunkType = docChunk
		if err = GetChunkStore(req.UserContext.ProjectId).Store(c); err != nil {
			return nil, err
		}

		// 需要根据索引配置判断是否需要入库
		if doc.Stage != pb.DocumentTaskStage_INDEXING_DONE {
			// 入库没有完成的，不重新入库
			return &pb.UpdateChunkRsp{Chunk: c.ToPb()}, nil
		}
		if doc.IndexConfig != nil {
			if augChunkType == "" {
				// 直接返回
				return &pb.UpdateChunkRsp{Chunk: c.ToPb()}, nil
			} else {
				priorityMap, _ := makePriorityWithIndexConfig(doc.IndexConfig)
				if p, ok := priorityMap[augChunkType]; !ok {
					// 不需要入库
					return &pb.UpdateChunkRsp{Chunk: c.ToPb()}, nil
				} else {
					if err = handler.DeleteChunksById(ctx, []string{req.ChunkId}); err != nil {
						return nil, err
					}
					if ci != nil {
						ci.Priority = p
						ci.ShortId = strconv.FormatInt(doc.ShortId, 16)
						if err = handler.SubmitChunks(ctx, []*models.ChunkForIndexing{ci}); err != nil {
							return nil, err
						}
					}
				}
			}
		}
	} else if kb.ContentType == pb.KnowledgeBaseContentType_TABLE {
		// 表格
		c.Content = req.Content
		c.Edited = true
		if err = GetChunkStore(req.UserContext.ProjectId).Store(c); err != nil {
			return nil, err
		}
		// 表格需要向量库更新所有该切片相关数据，因为extra有改变
		if err = handler.DeleteChunksByOriId(ctx, []string{req.OriChunkId}); err != nil {
			return nil, err
		}
		chunks := []*models.Chunk{c}
		chunks = GetDocImportTaskManager().processTableAugmentChunks(chunks, doc)
		idxChunks := makeChunksForIndexingWithIndexConfig(doc, chunks, kb.VectorModel)
		if err = handler.SubmitChunks(ctx, idxChunks); err != nil {
			return nil, err
		}
	}
	return &pb.UpdateChunkRsp{Chunk: c.ToPb()}, nil
}

func (m *KnowledgeBaseManager) UpdateAugmentChunk(ctx context.Context, req *pb.UpdateAugmentChunkReq) (*pb.UpdateAugmentChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, err
	}
	if doc.Stage == pb.DocumentTaskStage_INDEXING {
		// 入库中的切片暂时不允许修改
		return nil, stderr.Errorf("doc is indexing, cannot update chunk.")
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	c, err := GetChunkStore(req.UserContext.ProjectId).Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}
	if req.AugmentChunk != nil {
		// 更新增强切片
		i := GetChunkStore(req.UserContext.ProjectId).IndexAugChunk(c, req.AugmentChunk.Id)
		if i < 0 {
			return nil, stderr.Errorf("augmented chunk %s not found", req.AugmentChunk.Id)
		} else {
			ac := c.AugmentedChunks[i]
			augChunkType := indexConfigMap[ac.AugmentedType]
			ac.Content = req.AugmentChunk.Content
			ac.Edited = true
			if err = GetChunkStore(req.UserContext.ProjectId).Store(c); err != nil {
				return nil, err
			}
			ci := refreshAugChunkIdxSetting(c, ac, kb.VectorModel)
			if augChunkType == "" {
				// 直接返回
				return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
			} else {
				priorityMap, _ := makePriorityWithIndexConfig(doc.IndexConfig)
				if p, ok := priorityMap[augChunkType]; !ok {
					// 不需要入库
					return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
				} else {
					if err = handler.DeleteChunksById(ctx, []string{req.AugmentChunk.Id}); err != nil {
						return nil, err
					}
					if ci != nil {
						ci.Priority = p
						ci.ShortId = strconv.FormatInt(doc.ShortId, 16)
						if ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD {
							ci.Extra = c.Content
						}
						if err = handler.SubmitChunks(ctx, []*models.ChunkForIndexing{ci}); err != nil {
							return nil, err
						}
					}
					return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
				}
			}
		}
	} else if req.QaPair != nil {
		for _, qa := range c.QaPairs {
			if qa.Question.Id == req.QaPair.Question.Id && qa.Answer.Id == req.QaPair.Answer.Id {
				cis := make([]*models.ChunkForIndexing, 0)
				toDeleteIds := make([]string, 0)
				priorityMap, _ := makePriorityWithIndexConfig(doc.IndexConfig)
				qc := qa.Question
				qc.Content = req.QaPair.Question.Content
				qc.Edited = true
				if p, ok := priorityMap[question]; ok {
					ci := refreshAugChunkIdxSetting(c, qc, kb.VectorModel)
					ci.Priority = p
					ci.ShortId = strconv.FormatInt(doc.ShortId, 16)
					ci.Extra = req.QaPair.Answer.Content
					cis = append(cis, ci)
					toDeleteIds = append(toDeleteIds, qc.Id)
				}
				ac := qa.Answer
				ac.Content = req.QaPair.Answer.Content
				ac.Edited = true
				if p, ok := priorityMap[answer]; ok {
					ci := refreshAugChunkIdxSetting(c, ac, kb.VectorModel)
					ci.Priority = p
					ci.ShortId = strconv.FormatInt(doc.ShortId, 16)
					cis = append(cis, ci)
					toDeleteIds = append(toDeleteIds, ac.Id)
				}
				if err = GetChunkStore(req.UserContext.ProjectId).Store(c); err != nil {
					return nil, err
				}
				if len(toDeleteIds) > 0 {
					if err = handler.DeleteChunksById(ctx, toDeleteIds); err != nil {
						return nil, err
					}
				}
				if len(cis) > 0 {
					if err = handler.SubmitChunks(ctx, cis); err != nil {
						return nil, err
					}
				}
				return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
			}
		}
		return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
	}
	return &pb.UpdateAugmentChunkRsp{Chunk: c.ToPb()}, nil
}

func (m *KnowledgeBaseManager) SyncSubmitFilesToKnowledgeBase(req *pb.SyncSubmitFilesToKnowledgeBaseReq, server pb.KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseServer) error {
	ctx := server.Context()

	ch, err := m.SyncSubmitFilesToKnowledgeBaseMain(ctx, req)
	if err != nil {
		return err
	}
	for rsp := range ch {
		err := server.Send(rsp)
		if err != nil {
			stdlog.Errorf("SyncSubmitFilesToKnowledgeBase send error: %v", err)
			return err
		}
	}
	return nil
}

func (m *KnowledgeBaseManager) SyncSubmitFilesToKnowledgeBaseMain(ctx context.Context, req *pb.SyncSubmitFilesToKnowledgeBaseReq) (chan *pb.SyncSubmitFilesToKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeOrCreateKnowledgeBase(req.KnowledgeBaseId, req.UserContext, req.AutoCreateKb)
	if err != nil {
		return nil, err
	}
	docs := make([]*pb.Document, 0)
	var filePaths []string
	if req.DocumentFileSource == pb.DocumentFileSource_LOCAL_FILE {
		filePaths = req.FilePaths
	} else {
		// 从CorpusConfig获取parquet文件目录
		if req.CorpusConfig == nil {
			return nil, stderr.Internal.Errorf("CORPUS_DATA_SET source must set corpusConfig")
		}
		dir := req.CorpusConfig.Dir
		relPath, err := stdfs.NewRelativeFilePath(dir)
		if err != nil {
			return nil, err
		}
		absFiles, err := helper.ListFiles(relPath.ToAbsFilePath(), true)
		if err != nil {
			return nil, err
		}
		for _, absFile := range absFiles {
			relFilePath, err := stdfs.NewRelativeFilePath(absFile)
			if err != nil {
				return nil, err
			}
			filePaths = append(filePaths, relFilePath.ToSFSFilePath())
		}
	}

	progs := make([]*pb.DocumentProcessingProgress, 0, len(docs))
	if req.DocProcessingConfig != nil &&
		req.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		files := make([]string, 0, len(filePaths))
		for _, f := range filePaths {
			localPath, err := stdfs.GetSFSLocalPath(f)
			if err != nil {
				return nil, err
			}
			files = append(files, localPath)
		}
		progs, err = handler.(*InnerHandler).docEngine.UploadFiles(ctx, files)
		stdlog.Infof("update successfully")
		if err != nil {
			return nil, err
		}
		docIds := make([]string, 0)
		for _, p := range progs {
			docIds = append(docIds, p.Document.DocId)
		}
	} else {

		for _, filePath := range filePaths {
			doc, err := GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
				FilePath:            filePath,
				TableConfig:         req.TableConfig,
				CorpusConfig:        req.CorpusConfig,
				DocumentFileSource:  req.DocumentFileSource,
				DocProcessingConfig: req.DocProcessingConfig,
			})
			if err != nil {
				return nil, stderr.Wrap(err, "file %s", filePath)
			}
			docs = append(docs, doc.ToPb())
		}

		dpc := req.DocProcessingConfig
		if dpc == nil {
			dpc = kb.DocProcessingConfig
		}
		if dpc == nil && kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
			return nil, stderr.Internal.Errorf("doc processing config must be set.")
		}

		for _, doc := range docs {
			// 主流程
			task := NewDocTask(ctx, kb.Id, dpc, doc)
			err = GetTaskManger().Submit(task)
			if err != nil {
				return nil, err
			}
			progs = append(progs, task.DocumentProcessingProgress)
		}
	}

	rsp := &pb.SyncSubmitFilesToKnowledgeBaseRsp{
		Total:     int32(len(docs)),
		Documents: progs,
	}

	stat := func(progs []*pb.DocumentProcessingProgress) (int32, int32) {
		var numSucceeded, numFailed int32 = 0, 0
		for _, prog := range progs {
			if prog.Finished {
				if isProgSucceeded(prog) {
					numSucceeded += 1
				} else {
					numFailed += 1
				}
			}
		}
		return numSucceeded, numFailed
	}

	ch := make(chan *pb.SyncSubmitFilesToKnowledgeBaseRsp)
	go func() {
		defer close(ch)
		for {
			select {
			case <-ctx.Done():
				return
			default:
				time.Sleep(time.Second)
				ns, nf := stat(progs)
				rsp.NumSucceeded = ns
				rsp.NumFailed = nf
				if ns+nf == rsp.Total {
					rsp.Done = true
					ch <- rsp
					return
				}
				ch <- rsp
			}
		}
	}()

	return ch, nil
}

func (m *KnowledgeBaseManager) SubmitChunksToKnowledgeBase(ctx context.Context, req *pb.SubmitChunksToKnowledgeBaseReq) (rsp *pb.SubmitChunksToKnowledgeBaseRsp, err error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return
	}

	var doc *models.Document
	if req.FilePath != "" {
		doc, err = GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
			FilePath:            req.FilePath,
			TableConfig:         nil,
			CorpusConfig:        nil,
			DocumentFileSource:  pb.DocumentFileSource_LOCAL_FILE,
			DocProcessingConfig: nil,
		})
		if err != nil {
			return nil, err
		}
	} else {
		// TODO impl file base64
		return nil, stderr.Internal.Error("unsupported file base64")
	}

	// err = GetDocElementStore().StoreBatch(models.FromDocElementPbs(req.Elements))
	// if err != nil {
	// 	return nil, err
	// }
	// chunks := models.FromChunkPbs(req.Chunks, kb.Id, doc.Id)

	// for _, c := range chunks {
	// 	if c.Id == "" {
	// 		c.Id = uuid.NewString()
	// 	}
	// }

	// err = GetChunkStore().StoreBatch(chunks)
	// if err != nil {
	// 	return nil, err
	// }

	// // indexing
	// idxChunks := makeChunksForIndexing(chunks)

	// handler, err := NewHandler(ctx, kb)
	// if err != nil {
	// 	return
	// }

	// err = handler.SubmitChunks(ctx, idxChunks)
	// if err != nil {
	// 	return
	// }
	task := NewDocTask(ctx, kb.Id, nil, doc.ToPb())
	if err = GetTaskManger().Create(task); err != nil {
		return nil, err
	}
	defer func() {
		task.DocumentProcessingProgress.Finished = true
		GetTaskManger().Store(doc.Id)
	}()

	if err = storeAndIndexingChunks(ctx, kb.Id,
		&pb.DocSvcLoadChunkRsp{Chunks: req.Chunks, Elements: req.Elements}, task.DocumentProcessingProgress); err != nil {
		return nil, err
	}

	rsp = &pb.SubmitChunksToKnowledgeBaseRsp{
		Document: doc.ToPb(),
	}
	return
}
func (m *KnowledgeBaseManager) RetrieveKnowledgeBaseInIstioMode(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, stderr.Wrap(err, "knowledge base not found")
	}
	publishInfo := kb.PublishInfo
	if publishInfo == nil {
		return nil, stderr.Wrap(err, "knowledge base publish info not found can not use istio")
	}
	u := url.URL{
		Scheme: "http",
		Host:   conf.Config.MLOps.IstioGatewayAddr,
		Path:   publishInfo.VirtualSvcUrl,
	}
	client := &http.Client{}
	pjm := protojson.MarshalOptions{
		Multiline:       true,
		Indent:          "  ",
		AllowPartial:    true,
		UseProtoNames:   true,
		UseEnumNumbers:  false,
		EmitUnpopulated: true,
	}
	reqBs, err := pjm.Marshal(req)
	if err != nil {
		return nil, stderr.Wrap(err, "marshal RetrieveKnowledgeBaseReq)")
	}
	hr, err := http.NewRequestWithContext(ctx, "POST", u.String(), bytes.NewBuffer(reqBs))
	if err != nil {
		return nil, stderr.Wrap(err, "failed to create http request")
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get token in context")
	}
	hr.Header.Set("Authorization", token)
	rsp, err := client.Do(hr)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do http request")
	}
	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to read response body")
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != 200 {
		return nil, stderr.Internal.Errorf("failed to get retrieve, status code: %d, body: %s", rsp.StatusCode, string(body))
	}
	retrieve := new(pb.RetrieveKnowledgeBaseRsp)
	pju := protojson.UnmarshalOptions{
		AllowPartial:   true,
		DiscardUnknown: true,
	}
	err = pju.Unmarshal(body, retrieve)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal response body")
	}
	return retrieve, nil
}
func (m *KnowledgeBaseManager) RetrieveCrossKnowledgeBase(ctx context.Context, req *pb.RetrieveCrossKnowledgeBaseReq) (rsp *pb.RetrieveCrossKnowledgeBaseRsp, err error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)

	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	var lock sync.Mutex
	results := make([]*pb.ChunkRetrieveResult, 0)

	err = stdsrv.SyncBatchCallGeneric(req.Ranges, func(rr *pb.RetrieveRange) error {
		rq := &pb.RetrieveKnowledgeBaseReq{
			UserContext:     req.UserContext,
			KnowledgeBaseId: rr.KnowledgeBaseId,
			Query:           req.Query,
			DocRange:        rr.DocRange,
			RetrievalConfig: rr.RetrievalConfig, // 为nil时使用各知识库默认的检索设置
			DisableRerank:   true,               // 仅召回，关闭重排序
		}
		var err error
		var rsp *pb.RetrieveKnowledgeBaseRsp
		if req.IstioMode {
			rsp, err = m.RetrieveKnowledgeBaseInIstioMode(ctx, rq)
		} else {
			rsp, err = m.RetrieveKnowledgeBase(ctx, rq)
		}
		if err != nil {
			return err
		}
		lock.Lock()
		defer lock.Unlock()
		results = append(results, rsp.Result...)
		return nil
	})
	if err != nil {
		return nil, err
	}

	// 仅召回
	if req.DisableRerank {
		return &pb.RetrieveCrossKnowledgeBaseRsp{Request: req, Result: results}, nil
	}

	if req.RerankParams == nil { // 需要重排序但未指定RerankParams时，采用默认的RerankParams
		projId := req.UserContext.ProjectId
		rc, err := DefaultRetrievalConfig(projId)
		if err != nil {
			return nil, err
		}
		req.RerankParams = rc.RerankParams
	}
	results, err = Rerank(ctx, req.Query, req.RerankParams, results)
	if err != nil {
		return nil, err
	}

	return &pb.RetrieveCrossKnowledgeBaseRsp{Request: req, Result: results}, nil
}

func (m *KnowledgeBaseManager) PreviewDocumentProcess(ctx context.Context, req *pb.PreviewDocumentProcessReq) (rsp *pb.DocSvcLoadChunkRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	if req.FilePath == "" || req.DocProcessingConfig == nil {
		return nil, stderr.BadRequest.Error("nil FilePath or DocProcessingConfig")
	}

	dpc := req.DocProcessingConfig

	proc, err := GetDocProcessor(dpc)
	if err != nil {
		return nil, err
	}
	return proc(ctx, req.FilePath, dpc, nil, nil, PreviewHeadPages)
}

func (m *KnowledgeBaseManager) IsDocumentExistent(ctx context.Context, req *pb.IsDocumentExistentReq) (rsp *pb.IsDocumentExistentRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	doc, err := GetDocumentStore().Exists(req.KnowledgeBaseId, req.FilePath)
	if err != nil {
		return
	}
	rsp = &pb.IsDocumentExistentRsp{
		Existent: doc != nil,
		Document: doc.ToPb(),
	}
	return
}

func (m *KnowledgeBaseManager) CollectKnowledgeBaseStats(ctx context.Context, req *pb.CollectKnowledgeBaseStatsReq) (rsp *pb.CollectKnowledgeBaseStatsRsp, err error) {
	uCtx := req.UserContext
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return nil, stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	projectId := uCtx.ProjectId
	rsp = new(pb.CollectKnowledgeBaseStatsRsp)

	kb := m.q.KnowledgeBase
	cond := kb.Select(kb.Id).Where(kb.IsVisible.Is(true))
	if projectId != "" && projectId != "all" {
		cond = cond.Where(kb.ProjectId.Eq(projectId))
	}
	kbs, err := cond.Find()
	if err != nil {
		return nil, err
	}
	rsp.NumKnowledgeBases = int64(len(kbs))

	kbIds := make([]string, 0, len(kbs))
	for _, x := range kbs {
		kbIds = append(kbIds, x.Id)
	}
	doc := m.q.Document
	rsp.NumDocs, err = doc.Where(doc.KnowledgeBaseId.In(kbIds...)).Count()
	if err != nil {
		return nil, err
	}

	// chunk := m.q.Chunk
	// chunks, err := chunk.Select(chunk.AugmentedChunks).Where(chunk.KnowledgeBaseId.In(kbIds...)).Find()
	// if err != nil {
	// 	return nil, err
	// }
	// rsp.NumChunks = int64(len(chunks))
	// for _, x := range chunks {
	// 	rsp.NumsAugChunks += int64(len(x.AugmentedChunks))
	// }

	// 暂时不需要统计增强分段的个数
	chunk := m.q.Chunk.Table(GetChunkStore(projectId).GetTableName())
	numChunks, err := chunk.Where(chunk.KnowledgeBaseId.In(kbIds...)).Count()
	if err != nil {
		return nil, err
	}
	rsp.NumChunks = numChunks

	return rsp, nil
}

// CollectKnowledgeBaseStoreStats 统计知识库的存储开销, 按用量排序返回topK条
func (m *KnowledgeBaseManager) CollectKnowledgeBaseStoreStats(projectId string, topK int, asc bool) (rsp []*models.KnowledgeBaseStorageCost, err error) {
	// 查询项目内可见的知识库ids
	kb := m.q.KnowledgeBase
	cond := kb.Select(kb.Id).Where(kb.IsVisible.Is(true))
	if projectId != "" && projectId != "all" {
		cond = cond.Where(kb.ProjectId.Eq(projectId))
	}
	kbs, err := cond.Find()
	if err != nil {
		return nil, err
	}
	allKbIds := make([]string, 0, len(kbs))
	for _, x := range kbs {
		allKbIds = append(allKbIds, x.Id)
	}

	var docCosts []struct {
		KnowledgeBaseId string
		DocFilesCost    int64
	}
	doc := m.q.Document
	var orderExpr field.Expr
	if asc {
		orderExpr = field.NewField("", "doc_files_cost").Asc()
	} else {
		orderExpr = field.NewField("", "doc_files_cost").Desc()
	}
	err = doc.Select(doc.KnowledgeBaseId, doc.FileSizeBytes.Sum().As("doc_files_cost")).
		Where(doc.KnowledgeBaseId.In(allKbIds...)).
		Group(doc.KnowledgeBaseId).
		Order(orderExpr).
		Limit(topK).
		Scan(&docCosts)
	if err != nil {
		return nil, err
	}

	tarKbIds := make([]string, 0, len(docCosts))
	for _, x := range docCosts {
		tarKbIds = append(tarKbIds, x.KnowledgeBaseId)
	}

	kbs, err = kb.Select(kb.Id, kb.Name).Where(kb.Id.In(tarKbIds...)).Find()
	if err != nil {
		return nil, err
	}
	mp := make(map[string]*models.KnowledgeBase)
	for _, x := range kbs {
		mp[x.Id] = x
	}

	// 目前只有文档存储开销的统计
	rsp = make([]*models.KnowledgeBaseStorageCost, 0, len(docCosts))
	for _, c := range docCosts {
		rsp = append(rsp, &models.KnowledgeBaseStorageCost{
			TotalCost:    c.DocFilesCost,
			Id:           c.KnowledgeBaseId,
			Name:         mp[c.KnowledgeBaseId].Name,
			DocFilesCost: c.DocFilesCost,
		})
	}

	return rsp, nil
}

func (m *KnowledgeBaseManager) ShareKnowledgeBase(ctx context.Context, req *pb.ShareKnowledgeBaseReq) (rsp *pb.ShareKnowledgeBaseRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	repo := m.q.KnowledgeBase
	_, err = repo.Where(repo.Id.Eq(req.KnowledgeBaseId)).UpdateColumn(repo.IsPublic, req.IsPublic)
	if err != nil {
		return nil, err
	}

	return new(pb.ShareKnowledgeBaseRsp), nil
}

func (m *KnowledgeBaseManager) RetryDocumentProcess(ctx context.Context, req *pb.RetryDocumentProcessReq) (rsp *pb.RetryDocumentProcessRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	var docIds []string

	if len(req.DocIds) == 0 {
		// docId为空时重试所有失败文档
		docRsp, err := m.ListDocuments(ctx, &pb.ListDocumentsReq{KnowledgeBaseId: req.KnowledgeBaseId})
		if err != nil {
			return nil, stderr.Wrap(err, "list documents")
		}
		for _, d := range docRsp.Result {
			if isDocFailed(d.Prog) {
				docIds = append(docIds, d.Doc.DocId)
			}
		}
	} else {
		docIds = req.DocIds
	}
	err = stdsrv.SyncBatchCallGeneric(docIds, func(docId string) error {
		return RetryDocTask(ctx, req.KnowledgeBaseId, docId, nil)
	})
	if err != nil {
		return nil, err
	}

	return &pb.RetryDocumentProcessRsp{}, nil
}

const (
	RetrieveAPI        = "/api/v1/knowlhub/kbs:retrieve"
	AppletBackendSvc   = "autocv-applet-service"
	KbIdMetaKey        = "knowledge_base_id"
	KbProjectIdMetaKey = "project_id"
	KbNameMetaKey      = "model_name" // mlops固定取该字段展示名字

	AssetMetaAPI   = "/api/v1/knowlhub/kbs/assets:meta"
	AssetExportAPI = "/api/v1/knowlhub/kbs/assets:export"
	AssetQueryAPI  = "/api/v1/knowlhub/kbs/assets:query"
)

var (
	metaQueryExample = &pb.MetaQueryConfig{
		Condition: &pb.ExprCondition{
			BoolOp: pb.BoolOperation_AND,
			Expr: []*pb.ExprDefine{
				{
					Field: "资产名称",
					Op:    pb.ExprOperation_EQ,
					Value: "文档名称.pdf",
				},
				{
					Field: "label.标签1",
					Op:    pb.ExprOperation_EQ,
					Value: "标签值",
				},
			},
		},
	}
)

func (m *KnowledgeBaseManager) PublishKnowledgeBase(ctx context.Context, req *pb.PublishKnowledgeBaseReq) (rsp *pb.PublishKnowledgeBaseRsp, err error) {
	getKb, err := m.GetKnowledgeBase(ctx, &pb.GetKnowledgeBaseReq{
		UserContext: req.Ctx,
		Id:          req.Id,
	})
	if err != nil {
		return nil, err
	}
	if getKb.Result == nil || getKb.Result.KnowledgeBase == nil {
		return nil, stderr.Internal.Error("failed to get knowledge base %s", req.Id)
	}
	kb := getKb.Result.KnowledgeBase
	namespace := k8s.CurrentNamespaceInCluster()
	rateLimit := req.RateLimit
	retrieveReqExample := &pb.RetrieveKnowledgeBaseReq{
		Query: "xxx",
	}
	example, _ := json.Marshal(retrieveReqExample)
	mqExample, _ := protojson.Marshal(metaQueryExample)
	kbId := ""
	servingKb, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_KNOWLEDGE},
		SourceMetaExtra: map[string]string{
			KbIdMetaKey:        kb.Id,
			KbProjectIdMetaKey: kb.ProjectId,
		},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "failed list serving knowledge base service")
	}
	if len(servingKb.ServiceInfos) != 0 {
		kbId = servingKb.ServiceInfos[0].Id
	}
	k8sFormatId := vstd.FormatNameAsK8SRsc(kb.Id)
	serviceRsp, err := clients.MLOpsCli.Cli.CreateRemote(ctx, &serving.MLOpsRemoteServiceInfoReq{
		Id:            kbId,
		Name:          req.Name,
		Desc:          req.Desc,
		Creator:       req.Ctx.UserName,
		VirtualSvcUrl: fmt.Sprintf("knowledge_base/%s/%s", namespace, k8sFormatId),
		//Apis: []*serving.API{
		//	{
		//		Port:        80,
		//		Type:        "http",
		//		Url:         []string{RetrieveAPI},
		//		UrlParamMap: map[string]string{RetrieveAPI: string(example)},
		//	},
		//},
		Endpoints: []*common.Endpoint{
			{
				Port: 80,
				Type: common.EndpointType_ENDPOINT_TYPE_HTTP,
				ApiAttrs: []*common.APIAttr{
					{
						ApiPath:    RetrieveAPI,
						ReqExample: string(example),
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					},
					{
						ApiPath: AssetMetaAPI,
						Method:  common.HttpMethod_HTTP_METHOD_GET,
						ApiType: common.APIType_API_TYPE_OTHERS,
					},
					{
						ApiPath: AssetExportAPI,
						Method:  common.HttpMethod_HTTP_METHOD_GET,
						ApiType: common.APIType_API_TYPE_OTHERS,
					},
					{
						ApiPath:    AssetQueryAPI,
						ReqExample: string(mqExample),
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					},
				},
				IsDefault: true,
			},
		},
		RateLimit: rateLimit,
		GuardrailsConfig: &serving.GuardrailsConfig{
			IsSecurity: req.IsSecurity,
		},
		QueryParams: map[string]string{
			"project_id":                     req.Ctx.ProjectId,
			helper.QueryParamKnowledgeBaseID: kb.Id,
		},
		RemoteServiceUrl: fmt.Sprintf("http://%s.%s.%s", AppletBackendSvc, namespace, conf.Config.InvokeConfig.SvcSuffix),
		SourceMeta: &serving.SourceMeta{Extra: map[string]string{
			KbIdMetaKey:        kb.Id,
			KbProjectIdMetaKey: kb.ProjectId,
			KbNameMetaKey:      kb.Name,
		}},
		SourceType:    serving.SourceType_SOURCE_TYPE_KNOWLEDGE,
		LimitTime:     -1, // 无限制
		PermissionCfg: req.PermissionCfg,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "failed to publish knowledge base service")
	}
	deployResult, err := clients.MLOpsCli.Cli.Deploy(ctx, &serving.ServiceID{Id: serviceRsp.Id})
	if err != nil {
		stdlog.WithError(err).Error("failed to deploy published knowledge base service")
	} else {
		stdlog.Infof("success deploy published knowledge base service %s", deployResult.Id)
	}
	kb.PublishInfo = &pb.KnowledgeBasePublishInfo{
		Id:               serviceRsp.Id,
		VirtualSvcUrl:    serviceRsp.VirtualSvcUrl,
		Name:             req.Name,
		Desc:             req.Desc,
		RateLimit:        req.RateLimit,
		IsSecurity:       req.IsSecurity,
		PermissionAction: req.PermissionAction,
		PermissionCfg:    req.PermissionCfg,
	}
	kb.IsPublished = true
	_, _ = m.UpdateKnowledgeBase(ctx, &pb.UpdateKnowledgeBaseReq{
		UserContext: req.Ctx,
		Base:        kb,
	})
	return &pb.PublishKnowledgeBaseRsp{
		Id:            serviceRsp.Id,
		VirtualSvcUrl: serviceRsp.VirtualSvcUrl,
	}, nil
}

func (m *KnowledgeBaseManager) TraceDocElements(ctx context.Context, req *pb.TraceDocElementsReq) (rsp *pb.TraceDocElementsRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	c := m.q.Chunk.Table(GetChunkStore(req.UserContext.ProjectId).GetTableName())
	chunk, err := c.Where(c.Id.Eq(req.ChunkId)).Take()
	if err != nil {
		return nil, err
	}
	if len(chunk.ElementIDs) == 0 {
		return &pb.TraceDocElementsRsp{Elements: []*pb.DocElement{}}, nil
	}
	e := m.q.DocElement
	elements, err := e.Where(e.Id.In(chunk.ElementIDs...)).Find()
	if err != nil {
		return nil, err
	}
	return &pb.TraceDocElementsRsp{Elements: models.ToDocElementPbs(elements)}, nil
}

func (m *KnowledgeBaseManager) RefreshHealthz(ctx context.Context) (err error) {
	TryRefreshHealthz()
	return nil
}

func (m *KnowledgeBaseManager) GetHealthzState(ctx context.Context) HealthzState {
	return GetHealthzState()
}

func (m *KnowledgeBaseManager) RebuildIndex(ctx context.Context, kbId string) error {
	kb, err := m.takeKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return err
	}
	err = handler.Drop(ctx)
	if err != nil {
		return err
	}
	stdlog.Infof("[kb: %s] drop index successfully", kbId)

	docs, err := GetDocumentStore().LoadByKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	for _, doc := range docs {
		task := NewDocTask(ctx, kbId, nil, doc.ToPb())
		err := GetTaskManger().SubmitRebuildIndex(task)
		if err != nil {
			return stderr.Wrap(err, "[kb: %s, doc: %s]submit rebuild index task", kbId, doc.Id)
		}
	}
	return nil
}

func (m *KnowledgeBaseManager) checkUserContext(ctx context.Context, uCtx *pb.UserContext) error {
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	if uCtx == nil {
		return stderr.Internal.Error("user context must be set.")
	}
	if uCtx.ProjectId == "" {
		return stderr.Internal.Error("project id must be set.")
	}
	return nil
}

func (m *KnowledgeBaseManager) checkUserContextAllowEmptyProjectID(ctx context.Context, uCtx *pb.UserContext) error {
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	if uCtx == nil {
		return stderr.Internal.Error("user context must be set.")
	}
	return nil
}

func (m *KnowledgeBaseManager) queryKnowledgeBaseInfo(ctx context.Context, kb *models.KnowledgeBase) (*pb.KnowledgeBaseInfo, error) {
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}
	var numDocs int32
	if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		docEngineKb, err := handler.(*InnerHandler).docEngine.GetKnowledgeBase(ctx)
		if err != nil {
			return nil, stderr.Wrap(err, "CountDocEngineDocuments")
		}
		numDocs = docEngineKb.Result.DocsNum
	} else {
		numDocs, err = handler.CountDocuments(ctx)
	}
	if err != nil {
		return nil, stderr.Wrap(err, "CountDocuments")
	}
	kbInfo := &pb.KnowledgeBaseInfo{
		KnowledgeBase:               kb.ToPb(),
		NumDocs:                     numDocs,
		SupportedRetrieveStrategies: handler.SupportedRetrieveStrategies(),
	}
	return kbInfo, nil
}

func (m *KnowledgeBaseManager) checkProjectID(projectID, id string) error {
	if projectID == "all" {
		return nil
	}
	ret, err := m.takeKnowledgeBase(id)
	if err != nil {
		return err
	}
	if ret.ProjectId != projectID {
		return stderr.Internal.Error("kb id with wrong project id")
	}
	return nil
}

func (m *KnowledgeBaseManager) takeKnowledgeBase(id string) (kb *models.KnowledgeBase, err error) {
	repo := m.q.KnowledgeBase
	kb, err = repo.Where(repo.Id.Eq(id)).Take()
	if err == gorm.ErrRecordNotFound {
		return nil, stderr.KnowlBaseNotFound.Errorf("id=%s", id)
	}
	if err != nil {
		return nil, err
	}
	return kb, nil
}

func (m *KnowledgeBaseManager) takeOrCreateKnowledgeBase(id string, uc *pb.UserContext, autoCreateKb bool) (kb *models.KnowledgeBase, err error) {
	repo := m.q.KnowledgeBase
	cnt, err := repo.Where(repo.Id.Eq(id)).Count()
	if err != nil {
		return nil, stderr.Wrap(err, "count kb, id=%s", id)
	}
	if cnt > 0 {
		kb, err = repo.Where(repo.Id.Eq(id)).Take()
		if err != nil {
			return nil, err
		}
		tp, err := GetStoreType(kb)
		if err != nil || tp != InnerStore {
			return nil, stderr.Wrap(err, "SubmitFileToKnowledgeBase only supports InnerStore")
		}
	} else {
		if !autoCreateKb {
			return nil, fmt.Errorf("SubmitFileToKnowledgeBase: kb not found and AutoCreateKb is false, id:[%v]", id)
		}
		// not exists, create a temp kb
		kb, err = NewKBForAgentConfig(id, uc.UserName, uc.ProjectId)
		if err != nil {
			return nil, stderr.Wrap(err, "NewKBForAgentConfig")
		}
		if err = repo.Create(kb); err != nil {
			return nil, stderr.Wrap(err, "create kb err")
		}
		kb, err = repo.Where(repo.Id.Eq(kb.Id)).Take()
		if err != nil {
			return nil, stderr.Wrap(err, "get kb err")
		}
	}
	return
}

func updateDisabledDocs(disabledDocs []string, docId string, disabled bool) []string {
	index := sort.SearchStrings(disabledDocs, docId)

	// 如果要禁用文档，且文档不存在，则插入
	if disabled && (index >= len(disabledDocs) || disabledDocs[index] != docId) {
		disabledDocs = append(disabledDocs, "")
		copy(disabledDocs[index+1:], disabledDocs[index:])
		disabledDocs[index] = docId
	} else if !disabled && index < len(disabledDocs) && disabledDocs[index] == docId {
		// 如果要启用文档，且文档存在，则删除
		copy(disabledDocs[index:], disabledDocs[index+1:])
		disabledDocs = disabledDocs[:len(disabledDocs)-1]
	}

	return disabledDocs
}

func checkAugmentConfig(kb *models.KnowledgeBase) error {
	if kb.DocProcessingConfig == nil {
		return ErrNilDocProcessingConfig
	}
	if kb.DocProcessingConfig.ChunkAugmentConfig == nil {
		return ErrNilChunkAugmentConfig
	}
	return nil
}

func augmentChunk(ctx context.Context, chunk *pb.Chunk, cfg *pb.ChunkAugmentConfig) error {
	return augmentChunks(ctx, []*pb.Chunk{chunk}, cfg)
}

func augmentChunksWithProg(ctx context.Context, data []*pb.Chunk, cfg *pb.ChunkAugmentConfig, prog *pb.DocumentProcessingProgress, percAddUnit float32) error {
	if cfg == nil || (!cfg.Enabled && !cfg.ImageEnable) {
		return nil
	}
	// 过滤掉表格类型chunk
	txtChunks := make([]*pb.Chunk, 0, len(data))
	tbChunks := make([]*pb.Chunk, 0, len(data)/2)
	imgChunks := make([]*pb.Chunk, 0)

	for _, c := range data {
		switch c.ContentType {
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE:
			tbChunks = append(tbChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT:
			txtChunks = append(txtChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_MARKDOWN:
			txtChunks = append(txtChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE:
			imgChunks = append(imgChunks, c)
		}
	}

	lock := new(sync.Mutex)
	getCallBack := func(div float32) func() {
		return func() {
			lock.Lock()
			defer lock.Unlock()
			if prog != nil {
				prog.Percentage += percAddUnit * div
			}
		}
	}

	var reqs []*pclients.EnhanceReq

	if cfg.Enabled {
		divTab := 1.0 / (float32(cfg.TableDescNum+cfg.TableSummaryNum) + Epsilon)
		// table desc & summary
		if len(tbChunks) > 0 {
			if cfg.TableDescNum > 0 {
				reqs = append(reqs, &pclients.EnhanceReq{
					Prompts:  []string{cfg.TableDescPrompt},
					Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION},
					Num:      int(cfg.TableDescNum),
					Chunks:   tbChunks,
					Callback: getCallBack(divTab),
				})
			}
			if cfg.TableSummaryNum > 0 {
				reqs = append(reqs, &pclients.EnhanceReq{
					Prompts:  []string{cfg.TableSummaryPrompt},
					Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY},
					Num:      int(cfg.TableSummaryNum),
					Chunks:   tbChunks,
					Callback: getCallBack(divTab),
				})
			}
		}

		divTxt := 1.0 / (float32(cfg.QuestionNum+cfg.SummaryNum) + Epsilon)
		// text question
		if cfg.QuestionNum > 0 {
			reqs = append(reqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.QuestionPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
				Num:      int(cfg.QuestionNum),
				Chunks:   txtChunks,
				Callback: getCallBack(divTxt),
				Params: map[string]any{
					"temperature": conf.Config.KnowlhubConfig.AugmentQuestionsTemperature,
				},
			})
		}
		// text summary
		if cfg.SummaryNum > 0 {
			reqs = append(reqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.SummaryPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY},
				Num:      int(cfg.SummaryNum),
				Chunks:   txtChunks,
				Callback: getCallBack(divTxt),
			})
		}
	}

	divImg := 1.0 / (float32(cfg.ImageDescNum) + Epsilon)
	var imgReqs []*pclients.EnhanceReq
	if cfg.ImageEnable && cfg.ImageDescNum > 0 {
		if len(imgChunks) > 0 {
			imgReqs = append(imgReqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.ImageDescPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION},
				Num:      int(cfg.ImageDescNum),
				Chunks:   imgChunks,
				Callback: getCallBack(divImg),
			})
		}
	}

	wg := sync.WaitGroup{}
	errs := make([]error, 0)
	if len(reqs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := pclients.EnhanceWithReq(ctx, cfg.TextModel, conf.Config.KnowlhubConfig.AugmentDefaultConcurrency, reqs)
			if err != nil {
				errs = append(errs, err)
			}
		}()
	}
	if len(imgReqs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := pclients.EnhanceWithReq(ctx, cfg.ImageModel, conf.Config.KnowlhubConfig.AugmentDefaultConcurrency, imgReqs)
			if err != nil {
				errs = append(errs, err)
			}
		}()
	}
	wg.Wait()
	if len(errs) > 0 {
		return fmt.Errorf("enhance failed: %+v", errs)
	}
	return nil
}

func augmentChunks(ctx context.Context, data []*pb.Chunk, cfg *pb.ChunkAugmentConfig) error {
	return augmentChunksWithProg(ctx, data, cfg, nil, 0)
}

func Rerank(ctx context.Context, query string, params *pb.RerankParams, chunks []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	return pclients.RerankChunksAndFilter(ctx, query, params, chunks)
}

func isLongerThanEmedMaxLength(content string, vecModel *pb.ModelService) bool {
	maxToken, err := GetVecModelMaxToken(vecModel)
	if err != nil {
		stdlog.WithError(err).Warnf("get vecmodel max token error.")
		return utf8.RuneCountInString(content) > conf.Config.KnowlhubConfig.EmbeddingMaxLength
	}
	return utf8.RuneCountInString(content) > maxToken
}

func cutContent(content string, vecModel *pb.ModelService) string {
	if isLongerThanEmedMaxLength(content, vecModel) {
		var truncated []rune
		for i, r := range content {
			if i >= conf.Config.KnowlhubConfig.EmbeddingMaxLength {
				break
			}
			truncated = append(truncated, r)
		}

		return string(truncated)
	}
	return content
}

func defaultChunkIdxSetting(chunk *models.Chunk) (DisableVectorIndexing, DisableFullTextIndexing bool) {
	switch chunk.ContentType {
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT:
		return false, false
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE:
		return true, false
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_JSON:
		return true, true
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE:
		return true, true
	default:
		return false, false
	}
}

func refreshChunkIdxSetting(c *models.Chunk, vecModel *pb.ModelService) *models.ChunkForIndexing {
	if c == nil {
		return nil
	}
	ci := new(models.ChunkForIndexing)
	c.DisableVectorIndexing, c.DisableFullTextIndexing = defaultChunkIdxSetting(c)
	ci = ci.FromChunk(c)
	if !c.DisableVectorIndexing && isLongerThanEmedMaxLength(c.Content, vecModel) {
		// c.DisableVectorIndexing = true
		// 不再跳过向量化，改为截断
		ci.Text = cutContent(c.Content, vecModel)
	}
	return ci
}

func defaultAugChunkIdxSetting(ac *pb.AugmentedChunk) (DisableVectorIndexing, DisableFullTextIndexing bool) {
	return false, false
}

func refreshAugChunkIdxSetting(c *models.Chunk, ac *pb.AugmentedChunk, vecModel *pb.ModelService) *models.ChunkForIndexing {
	if ac == nil {
		return nil
	}
	ci := new(models.ChunkForIndexing)
	ac.DisableVectorIndexing, ac.DisableFullTextIndexing = defaultAugChunkIdxSetting(ac)
	ci = ci.FromAugChunk(c, ac)
	if !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content, vecModel) {
		// ac.DisableVectorIndexing = true
		// 不再跳过向量化，改为截断
		ci.Text = cutContent(ac.Content, vecModel)
	}
	return ci
}

func makeChunksForIndexing(chunks []*models.Chunk, vecModel *pb.ModelService) []*models.ChunkForIndexing {
	ics := make([]*models.ChunkForIndexing, 0, len(chunks))
	if len(chunks) == 0 {
		return ics
	}
	for _, c := range chunks {
		embContent := c.Content
		if !c.DisableVectorIndexing && isLongerThanEmedMaxLength(c.Content, vecModel) {
			// 现在不再跳过向量化，改为截断内容
			stdlog.Warnf("chunk content is too long, cut content:[%s]", c.Content)
			// c.DisableVectorIndexing = true
			embContent = cutContent(c.Content, vecModel)
		}
		ics = append(ics, &models.ChunkForIndexing{
			Id:                      c.Id,
			OriId:                   c.Id,
			DocId:                   c.DocumentId,
			KBId:                    c.KnowledgeBaseId,
			Text:                    embContent,
			DisableVectorIndexing:   c.DisableVectorIndexing,
			DisableFullTextIndexing: c.DisableFullTextIndexing,
		})

		for _, ac := range c.AugmentedChunks {
			embContent := ac.Content
			if !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content, vecModel) {
				// 现在不再跳过向量化，改为截断内容
				stdlog.Warnf("augmented chunk content is too long, cut content, orginal:[%s], aug:[%s]", c.Content, ac.Content)
				// ac.DisableVectorIndexing = true
				embContent = cutContent(ac.Content, vecModel)
			}
			ics = append(ics, &models.ChunkForIndexing{
				Id:                      ac.Id,
				OriId:                   c.Id,
				DocId:                   c.DocumentId,
				KBId:                    c.KnowledgeBaseId,
				Text:                    embContent,
				DisableVectorIndexing:   ac.DisableVectorIndexing,
				DisableFullTextIndexing: ac.DisableFullTextIndexing,
			})
		}
	}
	return ics
}

func makeChunksForIndexingWithIndexConfig(doc *models.Document, chunks []*models.Chunk, vecModel *pb.ModelService) []*models.ChunkForIndexing {
	indexConfig := doc.IndexConfig
	ics := make([]*models.ChunkForIndexing, 0, len(chunks))
	if len(chunks) == 0 {
		return ics
	}
	priorityMap, maxP := makePriorityWithIndexConfig(indexConfig)
	for _, c := range chunks {
		if c.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_ABSTRACT {
			// 摘要没有打开索引则跳过
			if _, ok := priorityMap[docSummary]; !ok {
				continue
			}
		}
		// 切片没有打开索引则跳过
		if _, ok := priorityMap[docChunk]; ok {
			embContent := c.Content
			if !c.DisableVectorIndexing && isLongerThanEmedMaxLength(c.Content, vecModel) {
				// 现在不再跳过向量化，改为截断内容
				stdlog.Warnf("chunk content is too long, cut content:[%s]", c.Content)
				// c.DisableVectorIndexing = true
				embContent = cutContent(c.Content, vecModel)
			}
			ics = append(ics, &models.ChunkForIndexing{
				Id:                      c.Id,
				OriId:                   c.Id,
				DocId:                   c.DocumentId,
				KBId:                    c.KnowledgeBaseId,
				Text:                    embContent,
				DisableVectorIndexing:   c.DisableVectorIndexing,
				DisableFullTextIndexing: c.DisableFullTextIndexing,
				ShortId:                 strconv.FormatInt(doc.ShortId, 16),
				Priority: func() int {
					if c.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_JSON {
						return 0
					}
					if c.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_ABSTRACT {
						return priorityMap[docSummary]
					} else {
						return priorityMap[docChunk]
					}
				}(),
				Type: c.ContentType.String(),
			})
		}

		for _, ac := range c.AugmentedChunks {
			if ac.AugmentedType != pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD {
				if column, ok := indexConfigMap[ac.AugmentedType]; !ok {
					// 跳过没设置索引的且不是表格索引的增强切片
					continue
				} else {
					if _, able := priorityMap[column]; !able {
						continue
					}
				}
			}
			embContent := ac.Content
			if !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content, vecModel) {
				// 现在不再跳过向量化，改为截断内容
				stdlog.Warnf("augmented chunk content is too long, cut content, orginal:[%s], aug:[%s]", c.Content, ac.Content)
				// ac.DisableVectorIndexing = true
				embContent = cutContent(ac.Content, vecModel)
			}
			ics = append(ics, &models.ChunkForIndexing{
				Id:                      ac.Id,
				OriId:                   c.Id,
				DocId:                   c.DocumentId,
				KBId:                    c.KnowledgeBaseId,
				Text:                    embContent,
				DisableVectorIndexing:   ac.DisableVectorIndexing,
				DisableFullTextIndexing: ac.DisableFullTextIndexing,
				ShortId:                 strconv.FormatInt(doc.ShortId, 16),
				Priority: func() int {
					if ac.AugmentedType != pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD {
						return 0
					}
					if columnName, ok := indexConfigMap[ac.AugmentedType]; ok {
						return priorityMap[columnName]
					} else {
						return maxP
					}
				}(),
				Type: ac.AugmentedType.String(),
				Extra: func() string {
					if ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD {
						return c.Content
					} else {
						return ""
					}
				}(),
			})
		}

		// 特殊处理qa
		for _, qa := range c.QaPairs {
			q := qa.Question
			//q.AugmentedType = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION
			a := qa.Answer
			//a.AugmentedType = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_ANSWER
			// 问题
			if _, able := priorityMap[question]; able {
				embContent := q.Content
				if !q.DisableVectorIndexing && isLongerThanEmedMaxLength(q.Content, vecModel) {
					// 现在不再跳过向量化，改为截断内容
					stdlog.Warnf("augmented chunk content is too long, cut content, orginal:[%s], aug:[%s]", c.Content, q.Content)
					// ac.DisableVectorIndexing = true
					embContent = cutContent(q.Content, vecModel)
				}
				ics = append(ics, &models.ChunkForIndexing{
					Id:                      q.Id,
					OriId:                   c.Id,
					DocId:                   c.DocumentId,
					KBId:                    c.KnowledgeBaseId,
					Text:                    embContent,
					DisableVectorIndexing:   q.DisableVectorIndexing,
					DisableFullTextIndexing: q.DisableFullTextIndexing,
					ShortId:                 strconv.FormatInt(doc.ShortId, 16),
					Priority: func() int {
						if columnName, ok := indexConfigMap[q.AugmentedType]; ok {
							return priorityMap[columnName]
						} else {
							return maxP
						}
					}(),
					Type:  q.AugmentedType.String(),
					Extra: a.Content,
				})
			}
			// 回答
			if _, able := priorityMap[answer]; able {
				embContent := a.Content
				if !a.DisableVectorIndexing && isLongerThanEmedMaxLength(a.Content, vecModel) {
					// 现在不再跳过向量化，改为截断内容
					stdlog.Warnf("augmented chunk content is too long, cut content, orginal:[%s], aug:[%s]", c.Content, a.Content)
					// ac.DisableVectorIndexing = true
					embContent = cutContent(a.Content, vecModel)
				}
				ics = append(ics, &models.ChunkForIndexing{
					Id:                      a.Id,
					OriId:                   c.Id,
					DocId:                   c.DocumentId,
					KBId:                    c.KnowledgeBaseId,
					Text:                    embContent,
					DisableVectorIndexing:   a.DisableVectorIndexing,
					DisableFullTextIndexing: a.DisableFullTextIndexing,
					ShortId:                 strconv.FormatInt(doc.ShortId, 16),
					Priority: func() int {
						if columnName, ok := indexConfigMap[a.AugmentedType]; ok {
							return priorityMap[columnName]
						} else {
							return maxP
						}
					}(),
					Type: a.AugmentedType.String(),
				})
			}
		}
	}
	return ics
}

func makePriorityWithIndexConfig(indexConfig *pb.IndexConfig) (map[string]int, int) {
	vcs := indexConfig.VectorIndexConfigs
	res := make(map[string]int)
	order := make([]int, 0)
	indexMap := make(map[int][]string)
	m := 0
	for _, vc := range vcs {
		if vc.Enabled {
			if _, ok := indexMap[int(vc.Order)]; !ok {
				order = append(order, int(vc.Order))
			}
			indexMap[int(vc.Order)] = append(indexMap[int(vc.Order)], vc.OriColumn)
		}
	}
	sort.Ints(order)
	for i, o := range order {
		for _, column := range indexMap[o] {
			res[column] = i
		}
		m = i
	}
	return res, m
}

func makeEnabledReturnedMap(indexConfig *pb.IndexConfig) (map[string]bool, map[string]bool) {
	vcs := indexConfig.VectorIndexConfigs
	enabledMap := make(map[string]bool)
	returnedMap := make(map[string]bool)
	for _, vc := range vcs {
		enabledMap[vc.OriColumn] = vc.Enabled
		returnedMap[vc.OriColumn] = vc.Returned
	}
	return enabledMap, returnedMap
}

func isProgSucceeded(prog *pb.DocumentProcessingProgress) bool {
	return math.Abs(float64(100.0-prog.Percentage)) < 1e-5
}

// NewKBForAgentConfig 用于agent配置-本地文件上传的知识库构建
func NewKBForAgentConfig(uuid, creator, projectId string) (*models.KnowledgeBase, error) {
	ms, err := DefaultVecModelSvc(projectId)
	if err != nil {
		return nil, err
	}
	rc, err := DefaultRetrievalConfig(projectId)
	if err != nil {
		return nil, err
	}
	kb := &models.KnowledgeBase{
		Id:                  uuid,
		Name:                AgentConfigKbNamePrefix + uuid,
		ContentType:         pb.KnowledgeBaseContentType_TEXT,
		SourceType:          pb.KnowledgeBaseSourceType_FROM_SCRATCH,
		RegistryType:        pb.KnowledgeBaseRegistryType_NULL,
		Creator:             creator,
		ProjectId:           projectId,
		IsVisible:           false, // 不可见，待agent配置完成后需要调用接口更新为可见
		IsRetrievable:       true,
		CreationType:        pb.KnowledgeBaseCreationType_FROM_AGENT_CONFIGURATION,
		DocProcessingConfig: DefaultDocProcessingConfig,
		RetrievalConfig:     rc,
		VectorModel:         ms,
	}
	return kb, nil
}

func orderAndFilterRetrieveResults(results []*pb.ChunkRetrieveResult, threshold float32) []*pb.ChunkRetrieveResult {
	// desc order
	sort.Slice(results, func(i int, j int) bool {
		return results[i].Score > results[j].Score
	})
	idx := sort.Search(len(results), func(i int) bool { return results[i].Score < threshold })
	return results[:idx]
}

func GetKbsStats(ctx context.Context) (rsp *pb.CollectKnowledgeBaseStatsRsp, err error) {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, err
	}
	req := new(pb.CollectKnowledgeBaseStatsReq)
	req.UserContext = pbUserCtx
	return kbm.CollectKnowledgeBaseStats(ctx, req)
}

func (m *KnowledgeBaseManager) ListKnowledgeBasesSearchData() (*pb.ListKnowledgeBasesRsp, error) {
	repo := m.q.KnowledgeBase

	condition := repo.Where(repo.IsVisible.Is(true))
	oriCon := repo.Where(repo.IsPublished.Is(true))
	condition = condition.Where(oriCon)

	kbs, err := condition.Find()
	if err != nil {
		return nil, stderr.Wrap(err, "list kb simple error.")
	}

	ret := make([]*pb.KnowledgeBaseInfo, len(kbs))
	for i, kb := range kbs {
		ret[i] = &pb.KnowledgeBaseInfo{KnowledgeBase: kb.ToPb()}
	}

	return &pb.ListKnowledgeBasesRsp{Result: ret}, nil
}

func SetRelatedApps(ctx context.Context, kbInfos []*pb.KnowledgeBaseInfo) error {
	dpInfos, err := applet.ChainManager.GetAppDependencyInfos(ctx)
	if err != nil {
		return stderr.Wrap(err, "get app dependency infos")
	}
	for _, kbInfo := range kbInfos {
		if kbInfo.KnowledgeBase == nil {
			continue
		}
		id := kbInfo.KnowledgeBase.Id
		for _, dpInfo := range dpInfos {
			if dpInfo.KnowledgeBaseIds == nil || dpInfo.SimpleChainInfo == nil {
				continue
			}
			if _, ok := dpInfo.KnowledgeBaseIds[id]; ok {
				kbInfo.AppRelations = append(kbInfo.AppRelations, &pb.AppRelation{
					AppId:   dpInfo.SimpleChainInfo.Id,
					AppName: dpInfo.SimpleChainInfo.Name,
				})
			}
		}
		kbInfo.NumApps = int32(len(kbInfo.AppRelations))
	}
	return nil
}

// StoreTaskDocument 任务文档入库（包括入向量库和全文索引库）, 异步处理
func (m *KnowledgeBaseManager) StoreTaskDocument(ctx context.Context, req *pb.StoreTaskDocumentReq) (*pb.StoreTaskDocumentRsp, error) {
	if err := m.checkVectorIndex(req.IndexConfig); err != nil {
		return nil, err
	}
	// 回调知识加工接口修改状态
	body, err := json.Marshal(req.IndexConfig)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "marshal index config error")
	}
	res, err := clients.CVATCli.HttpRpcCall(ctx, http.MethodPost, fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, fmt.Sprintf(updateImportStatusUrlFmt, req.TaskId)), bytes.NewReader(body))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "call cvat update api error: %s", string(res))
	}
	go func() {
		err := GetDocImportTaskManager().ProcessStoreReq(ctx, req)
		if err != nil {
			stdlog.WithError(err).Errorf("store task request error, req: %+v", req)
		}
	}()
	return &pb.StoreTaskDocumentRsp{}, nil
}

// ReStoreDocument 重新选择索引配置入库
func (m *KnowledgeBaseManager) ReStoreDocument(ctx context.Context, req *pb.ReStoreDocumentReq) (*pb.ReStoreDocumentRsp, error) {
	if err := m.checkVectorIndex(req.IndexConfig); err != nil {
		return nil, err
	}
	go func() {
		err := GetDocImportTaskManager().ProcessReStoreReq(ctx, req)
		if err != nil {
			stdlog.WithError(err).Errorf("re store doc request error, req: %+v", req)
		}
	}()
	return &pb.ReStoreDocumentRsp{
		DocId: req.DocId,
	}, nil
}

func (m *KnowledgeBaseManager) checkVectorIndex(indexConfig *pb.IndexConfig) error {
	if indexConfig == nil {
		return stderr.Errorf("IndexConfig is nil!")
	}
	if len(indexConfig.VectorIndexConfigs) == 0 {
		return stderr.Errorf("vector index config is empty")
	}
	returnFlag, enableFlag := false, false
	for _, vectorConfig := range indexConfig.VectorIndexConfigs {
		if vectorConfig.Enabled {
			enableFlag = true
		}
		if vectorConfig.Returned {
			returnFlag = true
		}
	}
	if !enableFlag {
		return stderr.Errorf("no returned column")
	}
	if !returnFlag {
		return stderr.Errorf("no indexed column")
	}
	return nil
}

func (m *KnowledgeBaseManager) GetDocument(ctx context.Context, req *pb.GetDocumentReq) (*pb.GetDocumentRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "get kb [%s] error.", req.KnowledgeBaseId)
	}
	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "get kb [%s] handler error.", kb.Id)
		}
		info, err := handler.(*InnerHandler).docEngine.GetDocument(ctx, req.DocId)
		if err != nil {
			return nil, stderr.Errorf("get doc [%s] error.", req.DocId)
		}
		return &pb.GetDocumentRsp{
			Result: info,
		}, nil
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "get document [%s] error.", req.DocId)
	}
	strategyOrigin := new(pb.StrategyOrigin)
	if doc.DocProcessingConfig != nil {
		strategyOrigin = &doc.DocProcessingConfig.DocProcessingStrategyOrigin
	}
	di := &pb.DocumentInfo{
		Doc:            doc.ToPb(),
		Prog:           GetDocImportTaskManager().Get(doc.Id).DocumentProcessingProgress,
		StrategyOrigin: *strategyOrigin,
	}
	if doc.Stage == pb.DocumentTaskStage_WAITING_EXAMINING || doc.Stage == pb.DocumentTaskStage_WAITING {
		di.Prog.Stage = doc.Stage
	}
	di.NumChunks = doc.NumChunks
	di.NumSuccessChunks = doc.NumSuccessChunks
	return &pb.GetDocumentRsp{
		Result: di,
	}, nil
}

func (m *KnowledgeBaseManager) BatchEnableDisableDocuments(ctx context.Context, req *pb.BatchEnableDisableDocumentsReq) (*pb.BatchEnableDisableDocumentsRsp, error) {
	// 姑且先把状态改掉
	err := GetDocumentStore().BatchChangeRetrieveStatus(req.DocIds, req.Enabled)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "change document retrieve status error with ids %+v", req.DocIds)
	}
	return &pb.BatchEnableDisableDocumentsRsp{
		DocIds: req.DocIds,
	}, nil
}

func (m *KnowledgeBaseManager) BatchEnableDisableChunks(ctx context.Context, req *pb.BatchEnableDisableChunksReq) (*pb.BatchEnableDisableChunksRsp, error) {
	// 姑且先把状态改掉
	err := GetChunkStore(req.UserContext.ProjectId).BatchChangeRetrieveStatus(req.ChunkIds, req.Enabled)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "change chunk retrieve status error with ids %+v", req.ChunkIds)
	}
	return &pb.BatchEnableDisableChunksRsp{
		ChunkIds: req.ChunkIds,
	}, nil
}

func (m *KnowledgeBaseManager) GetDocumentByTaskAndAsset(ctx context.Context, req *pb.GetDocumentByTaskAndAssetReq) (*pb.GetDocumentByTaskAndAssetRsp, error) {
	repo := m.q.DocTaskAssetRel
	res, err := repo.Where(repo.TaskID.Eq(req.TaskId), repo.AssetID.Eq(req.AssetId)).Preload(repo.Document).First()
	if err != nil {
		return nil, stderr.Internal.Cause(err, "Get document by task [%s] and asset [%s] error.", req.TaskId, req.AssetId)
	}
	return &pb.GetDocumentByTaskAndAssetRsp{
		Result: func() *pb.Document {
			if res.Document == nil {
				return nil
			}
			return res.Document.ToPb()
		}(),
	}, nil
}

func (m *KnowledgeBaseManager) GetDocumentAsset(ctx context.Context, req *pb.GetDocumentAssetReq) (*pb.GetDocumentAssetRsp, error) {
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "get document [%s] error.", req.DocId)
	}
	if doc.KnowledgeBaseId != req.KnowledgeBaseId {
		return nil, stderr.Internal.Cause(err, "unknown doc in this knowledge")
	}
	if doc.FileAsset != nil {
		// 从cvat接口获取最新的asset
		res, err := clients.CVATCli.HttpRpcCall(ctx, http.MethodGet, fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, fmt.Sprintf(getFileAsset, doc.FileAsset.Id)), nil)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "call cvat api error.")
		}
		asset := new(pb.FileAsset)
		err = stdsrv.Unmarshal(res, &asset)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "unmarshal result error. %s", string(res))
		}
		return &pb.GetDocumentAssetRsp{
			Result: asset,
		}, nil
	}
	return nil, stderr.Errorf("document without file asset.")
}

func (m *KnowledgeBaseManager) GetDocumentAssetDetail(ctx context.Context, projectID, kbid, docId string) (map[string]any, error) {
	doc, err := GetDocumentStore().Load(docId)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "get document [%s] error.", docId)
	}
	if doc.KnowledgeBaseId != kbid {
		return nil, stderr.Internal.Cause(err, "unknown doc in this knowledge")
	}
	if doc.FileAsset != nil {
		// 从cvat接口获取最新的asset
		rawUrl := fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, fmt.Sprintf(getFileAsset, doc.FileAsset.Id))
		res, err := clients.CVATCli.HttpRpcCall(ctx, http.MethodGet, fmt.Sprintf("%s?project_id=%s", rawUrl, projectID), nil)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "call cvat api error.")
		}
		asset := make(map[string]any)
		err = stdsrv.Unmarshal(res, &asset)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "unmarshal result error. %s", string(res))
		}
		return asset, nil
	}
	return nil, stderr.Errorf("document without file asset.")
}

func (m *KnowledgeBaseManager) ExportDocumentAsset(ctx context.Context, w http.ResponseWriter, projectID, kbid, docId string) error {
	doc, err := GetDocumentStore().Load(docId)
	if err != nil {
		return stderr.Internal.Cause(err, "get document [%s] error.", docId)
	}
	if doc.KnowledgeBaseId != kbid {
		return stderr.Internal.Cause(err, "unknown doc in this knowledge")
	}
	if doc.FileAsset != nil {
		// 从cvat接口转发下载
		rawUrl := fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, fmt.Sprintf(exportFileAsset, doc.FileAsset.Id))
		err = clients.CVATCli.HttpForwardGet(ctx, fmt.Sprintf("%s?project_id=%s", rawUrl, projectID), w)
		if err != nil {
			return stderr.Internal.Cause(err, "call cvat api error.")
		}
		return nil
	}
	return stderr.Errorf("document without file asset.")
}

func (m *KnowledgeBaseManager) RetryStoreDocument(ctx context.Context, req *pb.RetryStoreDocumentReq) (*pb.RetryStoreDocumentRsp, error) {
	if len(req.DocIds) == 0 {
		return &pb.RetryStoreDocumentRsp{}, nil
	}
	err := GetDocImportTaskManager().RetryDocumentChunks(ctx, req)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "retry docs %+v error.", req.DocIds)
	}
	return &pb.RetryStoreDocumentRsp{}, nil
}

func (m *KnowledgeBaseManager) ExportDocumentQaPairs(ctx context.Context, w http.ResponseWriter, kbid string) error {
	kb, err := m.takeKnowledgeBase(kbid)
	if err != nil {
		return stderr.Wrap(err, "get kb [%s] error.", kbid)
	}
	// 获取chunks
	chunks, err := GetChunkStore(kb.ProjectId).LoadByKnowledgeBase(kbid)
	if err != nil {
		return stderr.Internal.Cause(err, "get kb [%s] chunks error", kbid)
	}
	// 获取docs
	docs, err := GetDocumentStore().LoadByKnowledgeBase(kbid)
	if err != nil {
		return stderr.Wrap(err, "get kb [%s] docs error", kbid)
	}
	docMap := make(map[string]*models.Document)
	for _, doc := range docs {
		docMap[doc.Id] = doc
	}
	exportQas := make([]*ExportQa, 0)
	for _, chunk := range chunks {
		if len(chunk.QaPairs) == 0 {
			continue
		}
		for _, qa := range chunk.QaPairs {
			exportQa := &ExportQa{
				DocID:      chunk.DocumentId,
				KbID:       kbid,
				ChunkID:    chunk.Id,
				Question:   qa.Question.Content,
				Answer:     qa.Answer.Content,
				QuestionID: qa.Question.Id,
				AnswerID:   qa.Answer.Id,
			}
			if d, ok := docMap[chunk.DocumentId]; ok {
				exportQa.DocName = d.Name
				if d.FileAsset != nil {
					exportQa.AssetID = d.FileAsset.Id
				} else {
					exportQa.AssetID = ""
				}
			} else {
				exportQa.DocName = ""
			}
			exportQas = append(exportQas, exportQa)
		}
	}
	excelFile, err := ExportQaToExcel(exportQas)
	if err != nil {
		return stderr.Wrap(err, "export qa to excel error.")
	}
	filename := fmt.Sprintf("qa_%s_%s.xlsx", kb.Name, time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", url.QueryEscape(filename)))
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	if err := excelFile.Write(w); err != nil {
		stdlog.WithError(err).Errorf("write excel to response error.")
	}
	return nil
}

func (m *KnowledgeBaseManager) ExportDocumentByIds(docIds []string, kbid string, zos *zip.Writer) error {
	kb, err := m.takeKnowledgeBase(kbid)
	if err != nil {
		return stderr.Wrap(err, "get kb [%s] error.", kbid)
	}
	// 获取docs
	docs, err := GetDocumentStore().LoadBatch(docIds)
	if err != nil {
		return stderr.Wrap(err, "get kb [%s] docs error", kbid)
	}
	for _, doc := range docs {
		if doc.FileAsset == nil {
			continue
		}
		fileAsset := doc.FileAsset
		task := doc.ProcessTask
		chunks, err := GetChunkStore(kb.ProjectId).LoadByDocument(doc.Id)
		if err != nil {
			return stderr.Wrap(err, "get chunks by doc [%s] error", doc.Id)
		}
		fileName := fmt.Sprintf("%s/%s-%s.jsonl", task.Name, fileAsset.Name, fileAsset.Id)
		zosWriter, err := zos.Create(fileName)
		if err != nil {
			return stderr.Wrap(err, "creating file in zip failed for %s", fileName)
		}
		for _, chunk := range chunks {
			jsonBytes, err := stdsrv.MarshalMixWithProto(chunk)
			if err != nil {
				return stderr.Wrap(err, "encoding chunk to json failed for file %s", fileName)
			}
			_, err = zosWriter.Write(append(jsonBytes, '\n'))
			if err != nil {
				return stderr.Wrap(err, "writing to zip file failed for %s", fileName)
			}
		}
	}
	return nil
}

func (m *KnowledgeBaseManager) QueryDocumentAssets(ctx context.Context, kbid string, metaQuery *pb.MetaQueryConfig) ([]*models.Document, error) {
	docs, err := GetDocumentStore().LoadByKnowledgeBase(kbid)
	if err != nil {
		return nil, stderr.Wrap(err, "get docs by knowledge base [%s] error.", kbid)
	}
	docs = parseMetaQuery(docs, metaQuery, false)
	return docs, nil
}

func (m *KnowledgeBaseManager) DeleteDocumentsByTasks(ctx context.Context, req *pb.DeleteDocumentsByTasksReq) (*pb.DeleteDocumentsByTasksRsp, error) {
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, stderr.Wrap(err, "get kb [%s] error.", req.KnowledgeBaseId)
	}
	repo := m.q.DocTaskAssetRel
	docs, err := repo.Where(repo.TaskID.In(req.TaskIds...)).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "get docs with task ids %+v error.", req.TaskIds)
	}
	ids := make([]int64, 0)
	for _, doc := range docs {
		ids = append(ids, doc.DocShortID)
	}
	docRepo := m.q.Document
	documents, err := docRepo.Where(docRepo.ShortId.In(ids...)).Find()
	docIds := make([]string, 0)
	for _, d := range documents {
		if d.Stage == pb.DocumentTaskStage_INDEXING_DONE {
			// 已完成入库的文档不会删除
			continue
		}
		docIds = append(docIds, d.Id)
	}
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, stderr.Wrap(err, "get kb [%s] handler error.", req.KnowledgeBaseId)
	}
	err = handler.RemoveFilesFromKnowledgeBase(ctx, docIds)
	if err != nil {
		return nil, stderr.Wrap(err, "remove docs %+v from kb [%s] error.", docIds, req.KnowledgeBaseId)
	}
	return &pb.DeleteDocumentsByTasksRsp{Ids: docIds}, nil
}
