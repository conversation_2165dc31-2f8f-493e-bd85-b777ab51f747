package knowledge_base

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	// 文档文件的存储目录
	DocumentStoreRoot = "knowlhub/docs"
)

var (
	ErrDocFileExists = stderr.BadRequest.Error("document file exists")
)

type DocumentStore interface {
	Load(id string) (*models.Document, error)
	LoadBatch(ids []string) ([]*models.Document, error)
	LoadByKnowledgeBase(kbId string) ([]*models.Document, error)
	CountByKnowledgeBase(kbId string) (int32, error)
	Store(*models.Document) error
	StoreBatch([]*models.Document) error
	Delete(ctx context.Context, id string) error
	DeleteBatch(ctx context.Context, ids []string) error
	DeleteByKnowledgeBase(ctx context.Context, kbId string) error
	Submit(ctx context.Context, kbId string, submitInfo *models.DocumentSubmitInfo) (*models.Document, error)
	Exists(kbId, filepath string) (*models.Document, error)
	StoreNumChars(id string, numChars int32) error
	// 重置文档的相关数据, 包含删除相关chunks、elements和索引数据
	ResetData(ctx context.Context, id string) error
	BatchChangeRetrieveStatus(ids []string, enabled bool) error
	CountByKnowledgeBases(ids []string) (map[string]int64, error)
}

var ds DocumentStore
var dso sync.Once

func GetDocumentStore() DocumentStore {
	dso.Do(func() {
		ds = &documentStore{
			Q: dao.InitQuery(),
		}
	})
	return ds
}

type documentStore struct {
	Q *query.Query
}

func (ds *documentStore) CountByKnowledgeBases(ids []string) (map[string]int64, error) {
	repo := ds.Q.Document
	var results []struct {
		KnowledgeBaseId string
		Total           int64
	}
	err := repo.Where(repo.KnowledgeBaseId.In(ids...)).Group(repo.KnowledgeBaseId).Select(repo.KnowledgeBaseId, repo.Id.Count().As("total")).Scan(&results)
	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.KnowledgeBaseId] = result.Total
	}
	return countMap, nil
}

func (ds *documentStore) BatchChangeRetrieveStatus(ids []string, enabled bool) error {
	repo := ds.Q.Document
	_, err := repo.Where(repo.Id.In(ids...)).UpdateSimple(repo.RetrieveEnabled.Value(enabled))
	if err != nil {
		return err
	}
	return nil
}

func (ds *documentStore) LoadBatch(ids []string) ([]*models.Document, error) {
	repo := ds.Q.Document
	return repo.Where(repo.Id.In(ids...)).Find()
}

func (ds *documentStore) Load(id string) (*models.Document, error) {
	repo := ds.Q.Document
	ret, err := repo.Where(repo.Id.Eq(id)).Take()
	if err == gorm.ErrRecordNotFound {
		return nil, stderr.KnowlBaseDocNotFound.Errorf("id=%s", id)
	}
	return ret, nil
}

func (ds *documentStore) Store(c *models.Document) error {
	// 现在需要事务的插入rel数据
	return ds.Q.Transaction(func(tx *query.Query) error {
		err := tx.Document.Create(c)
		if err != nil {
			return err
		}
		if c.FileAsset != nil && c.ProcessTask != nil {
			err = tx.DocTaskAssetRel.Create(&models.DocTaskAssetRel{
				DocShortID: c.ShortId,
				TaskID:     c.ProcessTask.Id,
				AssetID:    c.FileAsset.Id,
				KbID:       c.KnowledgeBaseId,
			})
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (ds *documentStore) StoreBatch(data []*models.Document) error {
	repo := ds.Q.Document
	return repo.CreateInBatches(data, 100)
}

func (ds *documentStore) ResetData(ctx context.Context, id string) error {
	doc, err := ds.Load(id)
	if err != nil {
		return stderr.Internal.Errorf("get doc error, id:%s", id)
	}
	if err := GetChunkStore(doc.ProjectId).DeleteByDocument(id); err != nil {
		return stderr.Internal.Errorf("GetChunkStore().DeleteByDocument, id:%s", id)
	}
	if err := GetDocElementStore().DeleteByDocument(id); err != nil {
		return stderr.Internal.Errorf("GetDocElementStore().DeleteByDocument, id:%s", id)
	}
	return nil
}
func (ds *documentStore) Delete(ctx context.Context, id string) error {
	return ds.DeleteBatch(ctx, []string{id})
}
func (ds *documentStore) DeleteBatch(ctx context.Context, ids []string) error {
	// 事务的删除rel
	err := ds.Q.Transaction(func(tx *query.Query) error {
		// 获取doc
		docs, err := tx.Document.Where(tx.Document.Id.In(ids...)).Find()
		if err != nil {
			return err
		}
		if _, err := tx.Document.Where(tx.Document.Id.In(ids...)).Delete(); err != nil {
			return err
		}
		shortIds := make([]int64, 0)
		for _, doc := range docs {
			shortIds = append(shortIds, doc.ShortId)
		}
		if _, err := tx.DocTaskAssetRel.Where(tx.DocTaskAssetRel.DocShortID.In(shortIds...)).Delete(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	projId := helper.GetProjectID(ctx)
	tenantId := helper.GetTenantID(ctx)
	go func() {
		err := deleteDocumentFiles(ids, projId, tenantId)
		if err != nil {
			stdlog.WithError(err).Errorf("deleteDocumentFiles, ids:%s", ids)
		}
		for _, id := range ids {
			GetDocImportTaskManager().Cancel(id) // 取消文档处理任务
			if err := GetChunkStore(projId).DeleteByDocument(id); err != nil {
				stdlog.WithError(err).Errorf("GetChunkStore().DeleteByDocument, id:%s", id)
			}
			if err := GetDocElementStore().DeleteByDocument(id); err != nil {
				stdlog.WithError(err).Errorf("GetDocElementStore().DeleteByDocument, id:%s", id)
			}
		}
	}()
	return nil
}

func (ds *documentStore) DeleteByKnowledgeBase(ctx context.Context, kbId string) error {
	repo := ds.Q.Document
	docs, err := repo.Select(repo.Id).Where(repo.KnowledgeBaseId.Eq(kbId)).Find()
	if err != nil {
		return err
	}
	ids := make([]string, 0, len(docs))
	for _, doc := range docs {
		ids = append(ids, doc.Id)
	}

	return ds.DeleteBatch(ctx, ids)
}

func (ds *documentStore) LoadByKnowledgeBase(kbId string) ([]*models.Document, error) {
	repo := ds.Q.Document
	return repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Find()
}

func (ds *documentStore) Submit(ctx context.Context, kbId string, submitInfo *models.DocumentSubmitInfo) (*models.Document, error) {
	var projectId, tenantId string
	projectId = helper.GetProjectID(ctx)
	tenantId = helper.GetTenantID(ctx)
	localPath, err := stdfs.GetSFSLocalPath(submitInfo.FilePath)
	if err != nil {
		return nil, err
	}
	doc, err := ParseDocSubmission(kbId, submitInfo.FilePath, localPath)
	if err != nil {
		return nil, stderr.Wrap(err, "documentStore.Submit")
	}
	doc.ProjectId = projectId

	repo := ds.Q.Document
	// 避免同知识库下文档重名
	cnt, err := repo.Where(repo.KnowledgeBaseId.Eq(kbId), repo.Name.Eq(doc.Name)).Count()
	if err != nil {
		return nil, stderr.Wrap(err, "documentStore count same doc")
	}
	if cnt > 0 {
		var shortUuid string
		if sp := strings.Split(doc.Id, "-"); len(sp) > 0 {
			shortUuid = sp[0]
		}
		ext := filepath.Ext(doc.Name)
		if ext != "" {
			name := strings.TrimSuffix(doc.Name, ext)
			doc.Name = fmt.Sprintf("%s-%s%s", name, shortUuid, ext)
		} else {
			doc.Name = fmt.Sprintf("%s-%s", doc.Name, shortUuid)
		}
	}

	err = StoreDocumentFile(doc, tenantId, localPath)
	if err != nil {
		return nil, stderr.Wrap(err, "documentStore.Submit")
	}

	// set table config(only used by table kb)
	doc.TableConfig = submitInfo.TableConfig
	doc.CorpusConfig = submitInfo.CorpusConfig
	doc.DocumentFileSource = submitInfo.DocumentFileSource
	err = repo.Create(doc)
	if err != nil {
		return nil, stderr.Wrap(err, "documentStore.Submit")
	}
	return doc, err
}

func (ds *documentStore) Exists(kbId, filePath string) (*models.Document, error) {
	localPath, err := stdfs.GetSFSLocalPath(filePath)
	if err != nil {
		return nil, err
	}
	md5, err := utils.CalculateMD5(localPath)
	if err != nil {
		return nil, err
	}
	repo := ds.Q.Document
	cnt, err := repo.Where(repo.FileMD5.Eq(md5), repo.KnowledgeBaseId.Eq(kbId)).Count()
	if err != nil {
		return nil, err
	}
	if cnt > 0 {
		return repo.Where(repo.FileMD5.Eq(md5), repo.KnowledgeBaseId.Eq(kbId)).Take()
	}
	return nil, nil
}

func (ds *documentStore) StoreNumChars(id string, numChars int32) error {
	repo := ds.Q.Document
	info, err := repo.Where(repo.Id.Eq(id)).Update(repo.NumChars, numChars)
	if err != nil {
		return err
	}
	if info.RowsAffected == 0 {
		stdlog.Warnf("StoreNumChars:RowsAffected == 0, [id=%s, numChars=%v]", id, numChars)
	}
	return nil
}

func (ds *documentStore) CountByKnowledgeBase(kbId string) (int32, error) {
	repo := ds.Q.Document
	cnt, err := repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Count()
	if err != nil {
		return 0, err
	}
	return int32(cnt), nil
}

// ParseDocSubmission
// deprecated
func ParseDocSubmission(kbId, filePath, localPath string) (*models.Document, error) {
	file, err := os.Stat(localPath)
	if err != nil {
		return nil, err
	}
	md5, err := utils.CalculateMD5(localPath)
	if err != nil {
		return nil, err
	}
	mime, err := mimetype.DetectFile(localPath)
	if err != nil {
		return nil, err
	}
	doc := &models.Document{
		Id:              uuid.NewString(),
		Name:            filepath.Base(filePath),
		FilePath:        filePath,
		FileSizeBytes:   int32(file.Size()),
		FileFormat:      mime.String(),
		UploadTime:      time.Now(),
		FileMD5:         md5,
		KnowledgeBaseId: kbId,
	}
	return doc, nil
}

// StoreDocumentFile store uploaded doc file to sfs/store/tenants/${tenant_id}/projs/${project_id}/knowlhub/docs/${doc_id}/ and set doc.FilePath
func StoreDocumentFile(doc *models.Document, tenantId, originPath string) error {
	sfsDir := filepath.Join(helper.StoreDirBase(tenantId, doc.ProjectId), DocumentStoreRoot, doc.Id) // 相对于sfs/store的目录路径
	targetDir := filepath.Join(conf.Config.StorageRoot, sfsDir)
	mode := os.FileMode(0755)
	if err := os.MkdirAll(targetDir, mode); err != nil {
		return err
	}
	targetPath := filepath.Join(targetDir, doc.Name)
	if err := helper.CopyFile(originPath, targetPath); err != nil {
		return err
	}
	doc.FilePath = fmt.Sprintf("sfs:///%s", filepath.Join(sfsDir, doc.Name))
	return nil
}

func deleteDocumentFiles(ids []string, projectId, tenantId string) error {
	dirBase := filepath.Join(conf.Config.StorageRoot, filepath.Join(helper.StoreDirBase(tenantId, projectId), DocumentStoreRoot))
	for _, id := range ids {
		err := os.RemoveAll(filepath.Join(dirBase, id))
		if err != nil {
			return err
		}
	}
	return nil
}

// initDocTaskAssetRel 只有该表为空的时候会初始化rel表，同步两边的数据
func initDocTaskAssetRel() {
	q := dao.InitQuery()
	relRepo := q.DocTaskAssetRel
	res, err := relRepo.Limit(1).Find()
	if err != nil {
		// 忽略
		panic("initDocTaskAssetRel.Limit().Find() error")
	}
	if len(res) == 0 {
		// 分批从document表查询数据写入rel表
		var (
			batchSize int64 = 1000
			cursor    int64 = 0
		)
		docRepo := q.Document
		for {
			docs, err := docRepo.Where(docRepo.ShortId.Between(cursor, cursor+batchSize-1)).Find()
			if err != nil {
				panic("initDocTaskAssetRel.Query().Find() error")
			}
			if len(docs) == 0 {
				break
			}
			rels := make([]*models.DocTaskAssetRel, 0)
			for _, doc := range docs {
				if doc.ProcessTask != nil && doc.FileAsset != nil {
					rels = append(rels, &models.DocTaskAssetRel{
						DocShortID: doc.ShortId,
						TaskID:     doc.ProcessTask.Id,
						AssetID:    doc.FileAsset.Id,
						KbID:       doc.KnowledgeBaseId,
					})
				}
			}
			err = relRepo.CreateInBatches(rels, 100)
			if err != nil {
				panic("initDocTaskAssetRel.Query().CreateInBatches() error")
			}
			cursor += batchSize
		}
	}
}
