package api_tool

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
	mcpClient "transwarp.io/applied-ai/mcp-go/client"
	"transwarp.io/applied-ai/mcp-go/client/transport"
	"transwarp.io/applied-ai/mcp-go/mcp"

	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type IAPITool interface {
	// GetToolCollectionByID 通过id查询工具集详情
	GetToolCollectionByID(ctx context.Context, CollectionID string) (*api_tools.APIToolCollectionDO, error)
	// ListToolCollectionBaseInfos 查询工具集列表
	ListToolCollectionBaseInfos(ctx context.Context) ([]*api_tools.APIToolCollectionBaseDO, error)
	// BatchDelCollectionsByIDs 批量删除工具集
	BatchDelCollectionsByIDs(ctx context.Context, CollectionIDs []string) error
	// ParserMeta 解析元信息
	ParserMeta(ctx context.Context, metaType api_tools.APIToolMetaType, meta []byte) (*api_tools.APIToolCollectionDO, error)
	// ListCollectionDemos 获取工具集示例列表
	ListCollectionDemos(ctx context.Context) ([]*api_tools.APIToolCollectionDemoDO, error)
	// CreateAPIToolCollection 创建工具集
	CreateAPIToolCollection(ctx context.Context, model *api_tools.APIToolCollectionDO) (string, error)
	// UpdateAPIToolCollectionByID 更新工具集
	UpdateAPIToolCollectionByID(ctx context.Context, CollectionID string, model *api_tools.APIToolCollectionDO) error
	// PublishCollectionByID 发布工具集
	PublishCollectionByID(ctx context.Context, CollectionID string) error
	// CancelPublishCollectionByID 取消发布工具集
	CancelPublishCollectionByID(ctx context.Context, CollectionID string) error
	// GetMCPServerTools 获取MCPServer工具
	GetMCPServerTools(ctx context.Context, collectionDO *api_tools.APIToolCollectionDO) ([]*api_tools.APIToolDO, error)
	// TestToolAPI 测试API
	TestToolAPI(ctx context.Context, param *api_tools.APIToolTestParamInfo) (string, error)
	ListPublishedDescriber(ctx context.Context) ([]*agent_definition.APIToolCollectionDescriber, error)
	// CallToolAPI 调用API
	CallToolAPI(ctx context.Context, param *api_tools.APIToolCallParamInfo) (string, error)
}

type APITool struct {
}

func (a APITool) GetMCPServerTools(ctx context.Context, do *api_tools.APIToolCollectionDO) ([]*api_tools.APIToolDO, error) {
	if do == nil {
		return nil, fmt.Errorf("APIToolCollectionDO is nil")
	}

	// 获取基础信息
	baseURL := do.BaseURL
	if baseURL == "" {
		return nil, fmt.Errorf("baseURL is empty")
	}

	httpCli, err := createHttpClient(do.ProxyInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to create custom Http client: %v", err)
	}

	connParams := make(map[string]string)
	for _, p := range do.Params {
		connParams[p.Name] = p.DefaultValue.(string)
	}

	// 创建 sse mcp client
	var client *mcpClient.Client
	if do.BaseInfo.McpType == agent_definition.McpTypeSse {
		client, err = mcpClient.NewSSEMCPClient(baseURL, mcpClient.WithHTTPClient(httpCli), mcpClient.WithHeaders(connParams))
	} else {
		client, err = mcpClient.NewStreamableHttpClient(baseURL, transport.WithStreamableHTTPClient(httpCli), transport.WithHTTPHeaders(connParams))
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create MCP client: %v", err)
	}
	defer client.Close()

	// start the client
	if err := client.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start client，please make sure that the server is running and use proxy for the server on public network: %v", err.Error())
	}

	// Initialize
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name: "get time",
	}
	result, err := client.Initialize(ctx, initRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize: %v", err)
	}

	_ = fmt.Errorf(" server name is  '%s'", result.ServerInfo.Name)

	// Test ListTools
	toolsRequest := mcp.ListToolsRequest{}
	tools, err := client.ListTools(ctx, toolsRequest)
	if err != nil {
		return nil, fmt.Errorf("ListTools failed: %v", err)
	}

	// 将工具列表转换为 APITool 列表
	apiTools := make([]*api_tools.APIToolDO, 0)
	for _, tool := range tools.Tools {
		inputSchema := tool.InputSchema
		paramValues := ConvertToolInputSchemaToAPIToolParams(inputSchema)
		apiTools = append(apiTools, &api_tools.APIToolDO{
			Name:        tool.Name,
			Desc:        tool.Description,
			ParamValues: paramValues,
		})
	}
	return apiTools, nil
}

func ConvertToolInputSchemaToAPIToolParams(schema mcp.ToolInputSchema) []agent_definition.APIToolParam {
	params := make([]agent_definition.APIToolParam, 0)

	// 遍历 properties，将每个属性转换为 APIToolParam
	for key, value := range schema.Properties {
		param := agent_definition.APIToolParam{
			Name:     key,
			Required: contains(schema.Required, key), // 检查是否在 required 列表中
		}

		// 解析属性的详细信息
		if propMap, ok := value.(map[string]interface{}); ok {
			if desc, exists := propMap["description"].(string); exists {
				param.Desc = desc
			}
			if paramType, exists := propMap["type"].(string); exists {
				param.ParamValueType = paramType
			}
			if defaultValue, exists := propMap["default"]; exists {
				param.DefaultValue = defaultValue
			}
		}

		params = append(params, param)
	}

	return params
}

// 辅助函数：检查 key 是否在 required 列表中
func contains(required []string, key string) bool {
	for _, r := range required {
		if r == key {
			return true
		}
	}
	return false
}

func (a APITool) GetToolCollectionByID(ctx context.Context, CollectionID string) (*api_tools.APIToolCollectionDO, error) {
	BuiltinTools := getBuiltinTools(ctx)
	for _, builtinTool := range BuiltinTools {
		if builtinTool.BaseInfo.ID == CollectionID {
			return builtinTool, nil
		}
	}

	// 如果不是内置工具，继续从数据库中查找
	collectionPO, err := dao.APIToolCollectionImpl.GetByID(ctx, CollectionID)
	if err != nil {
		return nil, err
	}
	toolPOs, err := dao.APIToolImpl.ListByParam(ctx, &dao.APIToolQueryParam{
		CollectionID: ptr.String(CollectionID),
	})

	if err != nil {
		return nil, err
	}
	collectionDO := &api_tools.APIToolCollectionDO{}
	if err := collectionDO.CvtFromPO(collectionPO, toolPOs); err != nil {
		return nil, err
	}
	// 补充agent智能体信息
	collectionDO.BuildAgentParam()
	// todo @huangkai 补充references
	references := make([]api_tools.APIToolReference, 0)
	collectionDO.BaseInfo.BuildReference(references)
	return collectionDO, nil
}

func (a APITool) ListToolCollectionBaseInfos(ctx context.Context) ([]*api_tools.APIToolCollectionBaseDO, error) {
	res := make([]*api_tools.APIToolCollectionBaseDO, 0)
	collectionPOs, err := dao.APIToolCollectionImpl.ListByParam(ctx, &dao.APICollectionQueryParam{
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	if err != nil {
		return nil, err
	}
	collectionToolCntMap, err := dao.APIToolImpl.CntByCollectionID(ctx, &dao.APIToolQueryParam{
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	referenceMap := make(map[string][]api_tools.APIToolReference, 0)
	projectID := helper.GetProjectID(ctx)
	// 出于性能考虑，只有当projectID不为空的时候才会查询依赖
	if projectID != "" {
		chains, err := applet.ChainManager.SearchForChainDO(ctx, &applet.SearchChainParam{
			ProjectID: projectID,
		})
		if err != nil {
			return nil, stderr.Wrap(err, "search chains error")
		}
		// build toolID -> chainIDs map
		for _, chain := range chains {
			if chain.ChainDetail == nil {
				continue
			}
			deps, err := chain.GetDependency()
			if err != nil {
				return nil, stderr.Wrap(err, "get chain dependency error,chain id :%v", chain.Base.ID)
			}
			if deps == nil {
				continue
			}
			for _, dep := range deps.APIToolDependencies {
				if r, ok := referenceMap[dep.DependencyID]; ok {
					referenceMap[dep.DependencyID] = append(r, api_tools.APIToolReference{
						Type:        api_tools.ReferenceTypeAgent,
						ReferenceID: chain.Base.ID,
					})
				} else {
					referenceMap[dep.DependencyID] = []api_tools.APIToolReference{{
						Type:        api_tools.ReferenceTypeAgent,
						ReferenceID: chain.Base.ID,
					}}
				}
			}
		}
	}
	for _, p := range collectionPOs {
		DO := &api_tools.APIToolCollectionBaseDO{}
		cnt := int32(0)
		if count, ok := collectionToolCntMap[p.ID]; ok {
			cnt = count
		}

		if err := DO.CvtFromPO(p, cnt); err != nil {
			return nil, err
		}
		reference, ok := referenceMap[p.ID]
		if ok {
			DO.BuildReference(reference)
		}

		res = append(res, DO)
	}
	// todo@huangkai，填充被智能体引用的信息

	// 添加内置(builtin)类型的工具
	BuiltinTools := getBuiltinTools(ctx)
	for _, builtinTool := range BuiltinTools {
		res = append(res, &builtinTool.BaseInfo)
	}

	return res, nil
}

func (a APITool) BatchDelCollectionsByIDs(ctx context.Context, CollectionIDs []string) error {
	// 校验是否包含内置工具
	for _, id := range CollectionIDs {
		if strings.HasPrefix(id, CollectionIDPrefix) {
			return helper.ToolDeleteVerifyErr.Error("cannot delete builtin tool collection: %s", id)
		}
	}

	// 校验状态
	collectionPOs, err := dao.APIToolCollectionImpl.ListByParam(ctx, &dao.APICollectionQueryParam{
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
		IDs:       CollectionIDs,
	})
	if err != nil {
		return err
	}
	for _, c := range collectionPOs {
		if c.ReleasedState == api_tools.APIToolCollectionStateReleased {
			return helper.ToolDeleteVerifyErr.Error("tool %v is released", c.Name)
		}
	}
	// 删除两张表中的信息
	projectID := helper.GetProjectID(ctx)
	if err := query.Q.Transaction(func(tx *query.Query) error {
		collectionQuery := dao.NewAPICollectionDAO(tx)
		toolQuery := dao.NewAPIToolDAO(tx)
		if err := collectionQuery.BatchDelete(ctx, &dao.APICollectionQueryParam{
			ProjectID: ptr.String(projectID),
			IDs:       CollectionIDs,
		}); err != nil {
			return err
		}
		if err := toolQuery.BatchDelete(ctx, &dao.APIToolQueryParam{
			ProjectID:     ptr.String(projectID),
			CollectionIDs: CollectionIDs,
		}); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (a APITool) ParserMeta(ctx context.Context, metaType api_tools.APIToolMetaType, meta []byte) (*api_tools.APIToolCollectionDO, error) {
	metaParser, ok := ParserMap[string(metaType)]
	if !ok {
		return nil, helper.ToolMetaInfoParserErr.Error("un support meta type :%v", metaType)
	}
	apiSpec, err := metaParser.ParserMeta(meta)
	if err != nil {
		return nil, err
	}
	apis, err := apiSpec.GetAPIs()
	if err != nil {
		return nil, err
	}
	res := &api_tools.APIToolCollectionDO{
		BaseInfo: api_tools.APIToolCollectionBaseDO{
			Name: apiSpec.GetAPIName(),
			Desc: apiSpec.GetDesc(),
		},
		Tools:    apis,
		MetaType: metaType,
		MetaInfo: meta,
		BaseURL:  apiSpec.GetBaseURL(),
	}
	return res, nil
}

func (a APITool) ListCollectionDemos(ctx context.Context) ([]*api_tools.APIToolCollectionDemoDO, error) {
	res := make([]*api_tools.APIToolCollectionDemoDO, 0)
	POs, err := dao.APIToolCollectionDemoImpl.ListByParam(ctx, &dao.ToolDemoQueryParam{})
	if err != nil {
		return nil, err
	}
	for _, p := range POs {
		DO := &api_tools.APIToolCollectionDemoDO{}
		if err := DO.CvtFromPO(p); err != nil {
			return nil, err
		}
		res = append(res, DO)
	}
	return res, nil
}

func (a APITool) CreateAPIToolCollection(ctx context.Context, model *api_tools.APIToolCollectionDO) (string, error) {
	if err := model.InitForCreate(ctx); err != nil {
		return "", err
	}
	if err := model.IsValid(); err != nil {
		return "", err
	}
	// 不允许创建内置工具
	if model.BaseInfo.Type == api_tools.APIToolTypeBuiltin {
		return "", stderr.Error("builtin tool collection cannot be created")
	}
	collectionPO, err := model.ToPO()
	if err != nil {
		return "", err
	}
	collectionID := ""
	if err := query.Q.Transaction(func(tx *query.Query) error {
		collectionQuery := dao.NewAPICollectionDAO(tx)
		toolQuery := dao.NewAPIToolDAO(tx)
		collectionID, err = collectionQuery.Create(ctx, collectionPO)
		if err != nil {
			return err
		}
		toolPOs, err := api_tools.ToolBatchDOToPO(model.Tools, collectionID)
		if err != nil {
			return err
		}
		if err := toolQuery.BatchCreate(ctx, toolPOs); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return "", err
	}
	return collectionID, nil
}

func (a APITool) UpdateAPIToolCollectionByID(ctx context.Context, CollectionID string, model *api_tools.APIToolCollectionDO) error {
	// 检查是否为内置工具
	if strings.HasPrefix(CollectionID, CollectionIDPrefix) {
		return helper.ToolUpdateVerifyErr.Error("cannot update builtin tool collection: %s", CollectionID)
	}

	// 检查工具类型是否为内置类型
	if model.BaseInfo.Type == api_tools.APIToolTypeBuiltin {
		return helper.ToolUpdateVerifyErr.Error("cannot update builtin tool type")
	}

	model.InitForUpdate(ctx)
	if err := model.IsValid(); err != nil {
		return err
	}
	collectionPO, err := model.ToPO()
	if err != nil {
		return err
	}
	toolPOs, err := api_tools.ToolBatchDOToPO(model.Tools, CollectionID)
	if err != nil {
		return err
	}
	if err := query.Q.Transaction(func(tx *query.Query) error {
		collectionQuery := dao.NewAPICollectionDAO(tx)
		toolQuery := dao.NewAPIToolDAO(tx)
		if err := collectionQuery.UpdateByID(ctx, CollectionID, collectionPO); err != nil {
			return err
		}
		// bug-fix 更新时会丢弃 projectID
		if err := toolQuery.BatchDelete(ctx, &dao.APIToolQueryParam{
			CollectionID: ptr.String(CollectionID),
		}); err != nil {
			return err
		}
		if err := toolQuery.BatchCreate(ctx, toolPOs); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (a APITool) PublishCollectionByID(ctx context.Context, CollectionID string) error {
	// 校验是否为内置工具
	if strings.HasPrefix(CollectionID, CollectionIDPrefix) {
		return helper.ToolPublishVerifyErr.Error("cannot publish builtin tool collection: %s", CollectionID)
	}

	collection, err := a.GetToolCollectionByID(ctx, CollectionID)
	if err != nil {
		return err
	}
	if len(collection.Tools) == 0 {
		return helper.ToolPublishVerifyErr.Error("tool collection :%v no api", CollectionID)
	}
	// add 20250428 产品变更，不测试可以发布
	//for _, t := range collection.Tools {
	//	if t.TestState != api_tools.APIToolTestStateSuccess {
	//		return helper.ToolPublishVerifyErr.Error("tool %v-%v not test success", CollectionID, t.Name)
	//	}
	//}
	return dao.APIToolCollectionImpl.UpdateByID(ctx, CollectionID, &generated.APIToolCollection{
		ReleasedState:   api_tools.APIToolCollectionStateReleased,
		LastPublishTime: ptr.Time(time.Now()),
	})
}

func (a APITool) CancelPublishCollectionByID(ctx context.Context, CollectionID string) error {
	// 校验是否为内置工具
	if strings.HasPrefix(CollectionID, CollectionIDPrefix) {
		return helper.ToolPublishVerifyErr.Error("cannot cancel publish of builtin tool collection: %s", CollectionID)
	}

	return dao.APIToolCollectionImpl.UpdateByID(ctx, CollectionID, &generated.APIToolCollection{
		ReleasedState: api_tools.APIToolCollectionStateUnReleased,
	})
}

func (a APITool) TestToolAPI(ctx context.Context, param *api_tools.APIToolTestParamInfo) (string, error) {

	if param.ServerType == agent_definition.ServerTypeMCP || param.ServerType == agent_definition.ServerTypeDynamicMCP {
		return a.TestMCPToolAPI(ctx, param)
	}

	apiPath, err := url.JoinPath(param.BaseUrl, param.APIPath)
	if err != nil {
		return "", err
	}
	for _, p := range param.Params {
		if p.ParamType == agent_definition.APIToolParamTypePath {
			apiPath = strings.Replace(apiPath, fmt.Sprintf("{%s}", p.Name), fmt.Sprintf("%v", p.Value), -1)
		}
	}
	header := param.Headers
	queryParam := make(map[string]string)
	reqMap := make(map[string]any)
	reqBody := ""
	var arrBody any
	for _, p := range param.Params {
		switch p.ParamType {
		case agent_definition.APIToolParamTypeQuery:
			queryParam[p.Name] = fmt.Sprintf("%v", p.Value)
		case agent_definition.APIToolParamTypeHeader:
			header[p.Name] = fmt.Sprintf("%v", p.Value)
		case agent_definition.APIToolParamTypeBody:
			reqMap[p.Name] = p.Value
		case agent_definition.APIToolParamTypeArray:
			arrBody = p.Value
		}
	}
	if arrBody != nil {
		bytes, err := json.Marshal(arrBody)
		if err != nil {
			return "", err
		}
		reqBody = string(bytes)
	} else if len(reqMap) > 0 {
		bytes, err := json.Marshal(reqMap)
		if err != nil {
			return "", err
		}
		reqBody = string(bytes)
	}
	var proxy *agent_definition.ProxyInfo
	httpCli := clients.HttpCli
	// 先取默认值
	if conf.Config.APIToolConfig.DefaultProxy.Url != "" {
		schemaValue := pb.ProxyScheme_value[conf.Config.APIToolConfig.DefaultProxy.Schema]
		proxy = &agent_definition.ProxyInfo{
			Scheme: pb.ProxyScheme(schemaValue),
			Url:    conf.Config.APIToolConfig.DefaultProxy.Url,
		}
	}
	// 再从接口取
	if param.ProxyInfo != nil && param.ProxyInfo.Url != "" {
		proxy = param.ProxyInfo
	}
	if proxy != nil {
		transport, err := clients2.HttpTransport(proxy)
		if err != nil {
			return "", err
		}
		httpCli = clients2.NewHttpCliWithProxy(transport)
	}
	return httpCli.HttpCallString(ctx, &clients2.HttpParam{
		Method:     strings.ToUpper(param.Method),
		Url:        apiPath,
		ReqBody:    reqBody,
		Header:     header,
		QueryParam: queryParam,
	})
}

func createHttpClient(proxyInfo *agent_definition.ProxyInfo) (*http.Client, error) {
	var proxy *agent_definition.ProxyInfo
	httpCli := &http.Client{}
	// 先取默认值
	if conf.Config.APIToolConfig.DefaultProxy.Url != "" {
		schemaValue := pb.ProxyScheme_value[conf.Config.APIToolConfig.DefaultProxy.Schema]
		proxy = &agent_definition.ProxyInfo{
			Scheme: pb.ProxyScheme(schemaValue),
			Url:    conf.Config.APIToolConfig.DefaultProxy.Url,
		}
	}
	// 再从接口取
	if proxyInfo != nil && proxyInfo.Url != "" {
		proxy = proxyInfo
	}
	if proxy != nil {
		transport, err := clients2.HttpTransport(proxy)
		if err != nil {
			return nil, err
		}
		httpCli = &http.Client{
			Transport: transport,
		}
	}
	return httpCli, nil
}

func (a APITool) TestMCPToolAPI(ctx context.Context, param *api_tools.APIToolTestParamInfo) (string, error) {
	var client *mcpClient.Client
	connParams := make(map[string]string)
	for _, p := range param.MCPServerParams {
		connParams[p.Name] = p.DefaultValue.(string)
	}
	if param.ServerType == agent_definition.ServerTypeMCP && !strings.Contains(param.BaseUrl, "http://autocv-applet-service") {
		httpCli, err := createHttpClient(param.ProxyInfo)
		if err != nil {
			return "", fmt.Errorf("failed to create custom Http client: %v", err)
		}

		// 创建 sse mcp client
		if param.McpType == agent_definition.McpTypeSse {
			client, err = mcpClient.NewSSEMCPClient(param.BaseUrl, mcpClient.WithHTTPClient(httpCli), mcpClient.WithHeaders(connParams))
		} else {
			client, err = mcpClient.NewStreamableHttpClient(param.BaseUrl, transport.WithStreamableHTTPClient(httpCli), transport.WithHTTPHeaders(connParams))
		}
		if err != nil {
			return "", fmt.Errorf("failed to create MCP client: %v", err)
		}
		defer client.Close()
	} else {
		// 创建自定义的 HTTP 客户端配置
		httpClient := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true, // 忽略证书验证
				},
			},
		}

		// 创建 Streamable MCP client
		var err error
		if !strings.Contains(param.BaseUrl, "project_id") {
			projectId := helper.GetProjectID(ctx)
			param.BaseUrl = param.BaseUrl + "?project_id=" + projectId
		}
		client, err = mcpClient.NewStreamableHttpClient(param.BaseUrl, transport.WithStreamableHTTPClient(httpClient),
			transport.WithHTTPHeaderFunc(func(mcpCtx context.Context) map[string]string {
				token, err := helper.GetToken(ctx)
				if err != nil {
					return connParams
				}
				headers := make(map[string]string)
				headers["Authorization"] = token
				for k, v := range connParams {
					headers[k] = v
				}
				return headers
			}))
		if err != nil {
			return "", fmt.Errorf("failed to create Dynamic MCP client: %v", err)
		}
		defer client.Close()

	}

	if err := client.Start(ctx); err != nil {
		return "", fmt.Errorf("failed to start client，please make sure that the server is running and use proxy for the server on public network: %v", err.Error())
	}

	// 初始化 MCP 请求
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name: "test tool client",
	}

	if _, err := client.Initialize(ctx, initRequest); err != nil {
		return "", fmt.Errorf("failed to initialize MCP client: %v", err)
	}

	//调用指定的 tool
	request := mcp.CallToolRequest{}
	request.Params.Name = param.APIPath
	arguments := make(map[string]interface{})
	for _, param := range param.Params {
		arguments[param.Name] = param.Value
	}
	request.Params.Arguments = arguments

	callResponse, err := client.CallTool(ctx, request)
	if err != nil {
		return "", fmt.Errorf("failed to call tool: %v", err)
	}

	// 返回调用结果
	responseJSON, err := json.Marshal(callResponse)
	if err != nil {
		return "", fmt.Errorf("failed to marshal tool response: %v", err)
	}
	return string(responseJSON), nil
}

func (a APITool) CallToolAPI(ctx context.Context, param *api_tools.APIToolCallParamInfo) (string, error) {
	collection, err := a.GetToolCollectionByID(ctx, param.ToolCollectionID)
	if err != nil {
		return "", fmt.Errorf("failed to get tool collection by ID: %v", err)
	}
	tool := collection.GetToolByPath(param.APIPath) // 假设方法为POST
	if tool == nil {
		return "", fmt.Errorf("tool '%s' does not exist in the collection", param.APIPath)
	}
	// 3. 开始处理参数
	requestParams := make(map[string]interface{})

	for _, mcpParam := range collection.Params {
		requestParams[mcpParam.Name] = mcpParam.DefaultValue
	}
	for _, toolParam := range tool.ParamValues {
		if _, exists := requestParams[toolParam.Name]; !exists {
			requestParams[toolParam.Name] = toolParam.DefaultValue
		}
	}
	for _, callParam := range param.Params {
		requestParams[callParam.Name] = callParam.Value
	}

	httpCli, err := createHttpClient(collection.ProxyInfo)
	if err != nil {
		return "", fmt.Errorf("failed to create custom Http client: %v", err)
	}

	var client *mcpClient.Client
	if param.McpType == agent_definition.McpTypeSse {
		client, err = mcpClient.NewSSEMCPClient(collection.BaseURL, mcpClient.WithHTTPClient(httpCli))
	} else {
		client, err = mcpClient.NewStreamableHttpClient(collection.BaseURL, transport.WithStreamableHTTPClient(httpCli))
	}
	if err != nil {
		return "", fmt.Errorf("failed to create MCP client: %v", err)
	}
	defer client.Close()

	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if err := client.Start(ctx); err != nil {
		return "", fmt.Errorf("failed to start client，please make sure that the server is running and use proxy for the server on public network: %v", err.Error())
	}
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name: "call tool client",
	}

	if _, err := client.Initialize(ctx, initRequest); err != nil {
		return "", fmt.Errorf("failed to initialize MCP client: %v", err)
	}
	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = param.APIPath
	callRequest.Params.Arguments = requestParams

	callResponse, err := client.CallTool(ctx, callRequest)
	if err != nil {
		return "", fmt.Errorf("failed to call tool '%s': %v", param.APIPath, err)
	}

	// 5. 将返回值格式化为 JSON 字符串
	responseJSON, err := json.MarshalIndent(callResponse, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal tool response: %v", err)
	}
	return string(responseJSON), nil
}

func (a APITool) ListPublishedDescriber(ctx context.Context) ([]*agent_definition.APIToolCollectionDescriber, error) {
	res := make([]*agent_definition.APIToolCollectionDescriber, 0)
	projectId := helper.GetProjectID(ctx)
	//  筛选published
	collections, err := dao.APIToolCollectionImpl.ListByParam(ctx, &dao.APICollectionQueryParam{
		ProjectID: ptr.String(projectId), ReleasedState: ptr.String(api_tools.APIToolCollectionStateReleased)},
	)
	if err != nil {
		return nil, err
	}
	tools, err := dao.APIToolImpl.ListByParam(ctx, &dao.APIToolQueryParam{ProjectID: ptr.String(projectId)})
	if err != nil {
		return nil, err
	}

	collMap := make(map[string]*generated.APIToolCollection)
	collDescriberMap := make(map[string]*agent_definition.APIToolCollectionDescriber)
	for _, coll := range collections {
		collMap[coll.ID] = coll
		collDescriberMap[coll.ID] = &agent_definition.APIToolCollectionDescriber{
			ID: coll.ID, Name: coll.Name, Desc: coll.Desc,
		}
	}

	for _, t := range tools {
		coll, ok := collMap[t.CollectionID]
		if !ok {
			// tool表存在未发布的工具
			continue
		}
		collDescriber, ok := collDescriberMap[t.CollectionID]
		if !ok {
			continue
		}

		header := make(map[string]string)
		if err = json.Unmarshal([]byte(coll.Headers), &header); err != nil {
			return nil, err
		}
		paramValues := make([]agent_definition.APIToolParam, 0)
		if err = json.Unmarshal([]byte(t.Params), &paramValues); err != nil {
			return nil, err
		}
		collectionParams := make([]agent_definition.MCPParam, 0)
		if coll.McpParams != "" {
			if err = json.Unmarshal([]byte(coll.McpParams), &collectionParams); err != nil {
				return nil, err
			}
		}
		proxy := agent_definition.ProxyInfo{}
		if coll.ProxyInfo != "" {
			if err = json.Unmarshal([]byte(coll.ProxyInfo), &proxy); err != nil {
				return nil, err
			}
		}

		toolDescriber := agent_definition.APIToolDescriber{
			ID:                t.ID,
			BaseURL:           coll.BaseURL,
			Method:            t.Method,
			CollectionHeaders: header,
			APIPath:           t.Path,
			Name:              t.Name,
			Desc:              t.Desc,
			Params:            paramValues,
			CollectionId:      t.CollectionID,
			CollectionName:    collMap[t.CollectionID].Name,
			ServerType:        agent_definition.ServerType(coll.ServerType),
			CollectionParams:  collectionParams,
			Proxy:             proxy,
			McpType:           agent_definition.McpType(coll.McpType),
		}
		collDescriber.AgentTools = append(collDescriber.AgentTools, toolDescriber)
	}
	for _, v := range collDescriberMap {
		if len(v.AgentTools) != 0 {
			res = append(res, v)
		}
	}

	// 添加内置(builtin)类型的工具
	BuiltinTools := getBuiltinTools(ctx)
	for _, builtinTool := range BuiltinTools {
		res = append(res, builtinTool.AgentToolCollection)
	}

	return res, nil
}

func getBuiltinTools(ctx context.Context) []*api_tools.APIToolCollectionDO {
	if helper.IsChinese(ctx) {
		return BuiltinToolsZh
	} else {
		return BuiltinToolsEn
	}
}
