package api_tool

import (
	"fmt"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
)

const (
	KbToolAPI          = "/api/v1/tool/collections/builtin-tool-kb-dynamic-mcp/dynamic-mcp"
	KbListAPI          = "/api/v1/knowlhub/kbs"
	KbDocumentAPI      = "/api/v1/knowlhub/kbs/{kb_id}/docs"
	KbCrossRetrieveAPI = "/api/v1/knowlhub/kbs:cross-retrieve"
)

func newKbMcpServer() *api_tools.APIToolCollectionDO {
	currentNamespace := k8s.CurrentNamespaceInCluster()
	kbMcpUrl := fmt.Sprintf("http://%s.%s%s%s", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, KbToolAPI)
	kbCollection := &api_tools.APIToolCollectionDO{
		BaseInfo: api_tools.APIToolCollectionBaseDO{
			ID:                 "builtin-tool-kb-dynamic-mcp",
			Name:               "项目知识库mcp",
			Creator:            "",
			ProjectID:          "",
			Desc:               "项目知识库提供获取项目下所有知识库、某个知识库的文档列表、跨知识库检索工具",
			APIToolCnt:         1,
			Released:           ptr.Bool(true),
			LastReleaseTimeSec: 1749091632,
			Type:               api_tools.APIToolTypeBuiltin,
			ServerType:         agent_definition.ServerTypeMCP,
			McpType:            agent_definition.McpTypeStreamableHttp,
			CreateTimeSec:      1749091631,
			UpdateTimeSec:      1749091632,
			LogoUrl:            "",
		},
		Tools: []*api_tools.APIToolDO{
			{
				ID:        "builtin-get-kb-list-tool",
				Name:      "知识库列表",
				ProjectID: "",
				Alias:     "http_request",
				Desc:      "获取当前项目下所有发布的知识库列表",
				Method:    "GET",
				Path:      fmt.Sprintf("http://%s.%s%s%s", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, KbListAPI),
				TestState: api_tools.APIToolTestStateInit,
			},
			{
				ID:        "builtin-get-kb-document-tool",
				Name:      "知识库文档列表",
				ProjectID: "",
				Alias:     "http_request",
				Desc:      "根据知识库id获取知识库下的文档列表",
				Method:    "GET",
				Path:      fmt.Sprintf("http://%s.%s%s%s", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, KbDocumentAPI),
				TestState: api_tools.APIToolTestStateInit,
				ParamValues: []agent_definition.APIToolParam{
					{
						Name:           "kb_id",
						Desc:           "知识库id",
						ParamValueType: "string",
						Required:       true,
						DefaultValue:   "",
						ModelIgnore:    false,
					},
				},
			},
			{
				ID:        "builtin-kb-cross-retrieve-tool",
				Name:      "跨知识库检索",
				ProjectID: "",
				Alias:     "http_request",
				Desc:      "在多个知识库中进行检索",
				Method:    "POST",
				Path:      fmt.Sprintf("http://%s.%s%s%s", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, KbCrossRetrieveAPI),
				TestState: api_tools.APIToolTestStateInit,
				ParamValues: []agent_definition.APIToolParam{
					{
						Name:           "query",
						Desc:           "检索的关键字",
						ParamValueType: "string",
						Required:       true,
						DefaultValue:   "",
						ModelIgnore:    false,
					},
					{
						Name:           "ranges",
						Desc:           "检索的知识库范围",
						ParamValueType: "object",
						Required:       true,
						DefaultValue:   nil,
						ModelIgnore:    false,
					},
					{
						Name:           "rerank_params",
						Desc:           "检索结果的重排配置参数",
						ParamValueType: "object",
						Required:       true,
						DefaultValue:   nil,
						ModelIgnore:    false,
					},
				},
			},
		},
		MetaType: api_tools.APIToolMetaTypeJson,
		MetaInfo: []byte{},
		BaseURL:  kbMcpUrl,
	}

	return kbCollection
}
