package api_tool

import (
	"github.com/getkin/kin-openapi/openapi3"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
)

var ParserMap map[string]APIMetaParser

type APIMetaParser interface {
	ParserMeta(meta []byte) (api_tools.APIInfoParser, error)
}

type YamlParser struct {
}

func (y YamlParser) ParserMeta(meta []byte) (api_tools.APIInfoParser, error) {
	// todo 支持openapi其他版本/非openapi的规范
	res, err := openapi3.NewLoader().LoadFromData(meta)
	if err != nil {
		return nil, err
	}
	return &api_tools.OpenAPI3Parser{Spec: *res}, nil
}

type JsonParser struct {
}

func (j JsonParser) ParserMeta(meta []byte) (api_tools.APIInfoParser, error) {
	res, err := openapi3.NewLoader().LoadFromData(meta)
	if err != nil {
		return nil, err
	}
	return &api_tools.OpenAPI3Parser{Spec: *res}, nil
}
