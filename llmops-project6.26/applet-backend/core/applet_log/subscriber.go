package applet_log

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

//	func getDebugTopic(chatID string) string {
//		return debug.GetTopic(chatID)
//	}
//
//	func getChatIDFromTopic(topic string) (string, error) {
//		splits := strings.Split(topic, "/")
//		if len(splits) < 2 {
//			return "", stderr.Internal.Error("invalid topic :%v", topic)
//		}
//		return splits[1], nil
//	}
//
//	func SyncSub(ctx context.Context, chatID string, debugMsgHandler func(msg *debug.DebugMessage) error) error {
//		ctx, cancel := context.WithCancel(ctx)
//		defer cancel()
//		if err := sub(cancel, chatID, debugMsgHandler); err != nil {
//			return stderr.Errorf("failed to sub log with chat id %s", chatID)
//		}
//
//		// wait
//		select {
//		case <-ctx.Done():
//			stdlog.Infof("sync sub successfully")
//			return nil
//		}
//	}
//
//	func sub(cancel context.CancelFunc, chatID string, debugMsgHandler func(msg *debug.DebugMessage) error) error {
//		msgHandler := func(msg *mqtt.Msg) {
//			var err error
//			defer func() {
//				if err != nil {
//					stdlog.Errorf("handler msg err:", err)
//					unSub(cancel, chatID)
//				}
//			}()
//
//			// 1、cvt
//			debugMessage := new(debug.DebugMessage)
//			if err = helper.String2Struct(string(msg.Payload), debugMessage); err != nil {
//				err = stderr.Wrap(err, "cvt msg to debug message")
//				return
//			}
//
//			// 2、save
//			if debugMessage.IsEndMessage() {
//				UpsertMessageAsDebugLog(context.Background(), chatID, debugMessage)
//			}
//
//			// 3、outPut
//			if err = debugMsgHandler(debugMessage); err != nil {
//				err = stderr.Wrap(err, "debug msg handler")
//				return
//			}
//
//			// 4、done
//			if debugMessage.IsChainEndMessage() {
//				unSub(cancel, chatID)
//			}
//		}
//		return clients.MqCli.Sub(msgHandler, getDebugTopic(chatID))
//	}
//
//	func unSub(cancel context.CancelFunc, chatID string) {
//		cancel()
//		//clients.MqCli.Unsub(getDebugTopic(chatID))
//	}
func UpsertMessageAsDebugLog(ctx context.Context, chatID string, debugMessage *debug.DebugMessage) {
	log := &models.ChainDebugLog{
		ChatID:       chatID,
		NodeID:       models.GetUUNodeID(debugMessage.Meta.SubChainID, debugMessage.Meta.NodeID),
		Round:        debugMessage.Round,
		DebugMessage: debugMessage,
		ChainID:      "", // 待补充
		ProjectID:    "", // 待补充
	}
	if err := dao.ChainDebugLogDAOImpl.UpsertDebugLog(ctx, log); err != nil {
		stderr.Errorf("failed to save message: %+v as debug log with err :%v", debugMessage, err)
	}
}
