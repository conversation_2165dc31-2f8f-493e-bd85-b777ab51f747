package applet_log

//
//import (
//	"sync"
//	"time"
//)
//
//var ManagerFactory *managerFactory
//
//func Init() {
//	ManagerFactory = &managerFactory{
//		//logMap: sync.Map{},
//		mapV2: make(map[string]*LogManager),
//		//cache: make(map[string]chan *Message),
//		mutex: sync.Mutex{},
//	}
//}
//
//type managerFactory struct {
//	//logMap sync.Map
//	mapV2 map[string]*LogManager
//	//cache map[string]chan *Message
//	mutex sync.Mutex
//}
//
////func (m *managerFactory) Range(f func(key, value any) bool) {
////	for k, v := range m.mapV2 {
////		f(k, v)
////	}
////}
//
//func (m *managerFactory) Register(key string) {
//	m.mutex.Lock()
//	defer m.mutex.Unlock()
//	if _, ok := m.mapV2[key]; !ok {
//		m.mapV2[key] = NewManager(key)
//		//m.cache[key] = make(chan *Message)
//	}
//}
//
//func (m *managerFactory) Remove(key string) {
//	m.mutex.Lock()
//	defer m.mutex.Unlock()
//	delete(m.mapV2, key)
//}
//
//func (m *managerFactory) BlockGet(key string) *LogManager {
//	// 处理以下情况：key还没存下来，已经开始调用Get方法
//	for i := 0; i < 3; i++ {
//		if res := m.Get(key); res != nil {
//			return res
//		}
//		time.Sleep(1 * time.Second)
//	}
//	return nil
//}
//
//func (m *managerFactory) Get(key string) *LogManager {
//	m.mutex.Lock()
//	defer m.mutex.Unlock()
//	if v, ok := m.mapV2[key]; ok {
//		return v
//	} else {
//		return nil
//	}
//}
