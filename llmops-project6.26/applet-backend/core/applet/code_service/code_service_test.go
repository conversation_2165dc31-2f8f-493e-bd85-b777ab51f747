package code_service

import (
	"testing"
)

func TestExtractPythonCode(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "没有代码块标记的情况",
			input:    "def plain():\n    print(\"no markers\")",
			expected: "def plain():\n    print(\"no markers\")",
		},
		{
			name:     "带有```python标记的代码块",
			input:    "这是一段说明文字\n```python\ndef hello():\n    print(\"Hello, World!\")\n```\n这是后面的说明",
			expected: "def hello():\n    print(\"Hello, World!\")\n",
		},
		{
			name:     "带有```标记的普通代码块",
			input:    "看这段代码\n```\ndef test():\n    return True\n```\n结束了",
			expected: "def test():\n    return True\n",
		},
		{
			name:     "标记后面没有换行",
			input:    "```pythonprint('no newline')```",
			expected: "print('no newline')",
		},
		{
			name:     "标记后面有多个空格",
			input:    "```python\n    print('xxx')```",
			expected: "    print('xxx')",
		},
		{
			name:     "多个代码块只提取第一个",
			input:    "第一个代码块\n```python\ndef first():\n    pass\n```\n第二个代码块\n```python\ndef second():\n    pass\n```",
			expected: "def first():\n    pass\n",
		},
		{
			name:     "空字符串输入",
			input:    "",
			expected: "",
		},
	}

	service := &CodeServiceImpl{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.extractPythonCode(tt.input)
			if result != tt.expected {
				t.Errorf("\n测试用例：%s\n期望输出：%q\n实际输出：%q",
					tt.name, tt.expected, result)
			}
		})
	}
}
