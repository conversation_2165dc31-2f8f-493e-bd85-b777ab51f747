package applet

//
// import (
//	"context"
//	"encoding/json"
//	"github.com/spf13/viper"
//	"testing"
//	"transwarp.io/applied-ai/applet-backend/conf"
//	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
// )
//
// //func TestTT(t *testing.T) {
// //	idx := 0
// //	f1 := func(ctx context.Context) <-chan int {
// //		fmt.Println("aaaaa")
// //		res := make(chan int, 50)
// //		ticker := time.NewTicker(time.Second)
// //		go func() {
// //			for {
// //				select {
// //				case <-ctx.Done():
// //					fmt.Println("chan closed")
// //					close(res)
// //					ticker.Stop()
// //					return
// //				case <-ticker.C:
// //					res <- idx
// //					idx++
// //				}
// //			}
// //		}()
// //		return res
// //	}
// //
// //	f2 := func(ctx context.Context) {
// //		cc := f1(ctx)
// //		for c := range cc {
// //			time.Sleep(time.Second)
// //			fmt.Println(c)
// //		}
// //		fmt.Println("end")
// //	}
// //	ctx, cancel := context.WithCancel(context.Background())
// //	go f2(ctx)
// //	time.Sleep(10 * time.Second)
// //	cancel()
// //	time.Sleep(10 * time.Second)
// //}
//
// //func TestGenerateScript(t *testing.T) {
// //	testRes := `
// //	var var_1000 = stream
// //    |listenHttp()
// //        .path('api/v1')
// //        .port(1884)
// //        .timeout(60s)
// //
// //var var_1001 = var_1000
// //    |textInput()
// //        .nodeID('WidgetKeyTextInput')
// //        .inputKey('TextInput')
// //
// //var var_1002 = var_1001
// //    |dlieInfer()
// //        .port(8001)
// //        .timeout(30s)
// //        .modelName('atom')
// //        .modelType('text-vec')
// //        .addr('mwh-deployment-ck0prva0sqhf4hu05ldg')
// //
// //var var_1003 = var_1002
// //    |vecSearch()
// //        .database('default')
// //        .url('hippo://shiva:shiva@*************:7788?table=fx&dimension=1024')
// //
// //var var_1004 = var_1003
// //    |join(var_1001)
// //        .as('上下文', '用户输入')
// //        .tolerance(10s)
// //    |promptTmpl('''{{.上下文}}  根据以上背景信息，回答下面的问题： {{.用户输入}}''')
// //
// //var_1004
// //    |dlieInfer()
// //        .modelType('text-gen')
// //        .isStream(TRUE)
// //        .addr('mwh-deployment-ck4jvv20sqh5uhsnjq9g')
// //        .port(8001)
// //        .timeout(30s)
// //        .modelName('atom')
// //    |httpRsp()
// //	`
// //	_ = testRes
// //	res := `
// //	var var_1001 = stream
// //    |listenHttp()
// //        .port('1884')
// //        .path('api/v1')
// //
// //	var_1001
// //    |dlieInfer()
// //        .modelName('AI模型')
// //        .addr('127.0.0.1')
// //        .port('8080')
// //        .timeout('60s')
// //    |httpRsp()
// //	`
// //	_ = res
// //	chainStr := `{
// //    "nodes": [
// //        {
// //            "id": "1",
// //            "widget_id": "WidgetKeyTextInput",
// //            "ui": "",
// //            "values": null
// //        },
// //        {
// //            "id": "2",
// //            "widget_id": "WidgetKeyLLM",
// //            "ui": "",
// //            "values":{"LLMServer":"127.0.0.1:8080"}
// //        }
// //    ],
// //    "edges": [
// //
// //		{
// //			"id": "",
// //			"source": "1",
// //			"source_param": "OutPut",
// //			"target": "2",
// //			"target_param": "Text"
// //		}
// //
// //    ],
// //    "viewport": {
// //        "x": 0,
// //        "y": 0,
// //        "zoom": 0
// //    }
// //}`
// //	chain := &widgets.Chain{}
// //	if err := json.Unmarshal([]byte(chainStr), &chain); err != nil {
// //		t.Fatal(err)
// //	}
// //	builder := NewScriptBuilder(chain, nil)
// //	widgets.Init()
// //	script, err := builder.GenerateScript()
// //	if err != nil {
// //		t.Fatal(err)
// //	}
// //	t.Log(script)
// //}
//
// func TestGenerateScript2(t *testing.T) {
//	chainStr := `{
//        "nodes": [
//            {
//                "id": "06d56982-e51f-4909-8606-ec56dd1c9313",
//                "widget_id": "WidgetKeyTextInput",
//                "ui": "{\"width\":400,\"height\":171,\"id\":\"06d56982-e51f-4909-8606-ec56dd1c9313\",\"position\":{\"x\":-330.37423697438277,\"y\":234.21129063124548},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyTextInput\",\"name\":\"文本输入\",\"desc\":\"将输入的文本原样输出\",\"group\":\"WidgetGroupText\",\"params\":[{\"data_class\":\"string\",\"category\":\"req-input\",\"define\":{\"id\":\"TextInput\",\"name\":\"文本\",\"desc\":\"文本\",\"show\":true,\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":-330.37423697438277,\"y\":234.21129063124548},\"dragging\":false}",
//                "values": {}
//            },
//            {
//                "id": "e29e46aa-5a00-4c52-8dd9-038e5a0ec029",
//                "widget_id": "WidgetKeyVectorModel",
//                "ui": "{\"width\":400,\"height\":237,\"id\":\"e29e46aa-5a00-4c52-8dd9-038e5a0ec029\",\"position\":{\"x\":284.36320566445283,\"y\":15.894354684377276},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyVectorModel\",\"name\":\"向量模型\",\"desc\":\"向量模型服务\",\"group\":\"WidgetGroupAIModel\",\"params\":[{\"data_class\":\"json\",\"category\":\"in-port\",\"define\":{\"id\":\"Text\",\"name\":\"文本\",\"desc\":\"文本\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"ModelServer\",\"name\":\"服务\",\"desc\":\"服务\",\"datasource\":\"mwh-deployment-cjiopf20sqh5re7qim5g:8001/atom@@test_vec_v1_atom\",\"show\":true,\"type\":\"TYPE_SELECTOR\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"json\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\",\"ModelServer\":\"mwh-deployment-cjiopf20sqh5re7qim5g:8001/atom\"}},\"selected\":false,\"positionAbsolute\":{\"x\":284.36320566445283,\"y\":15.894354684377276},\"dragging\":false}",
//                "values": {
//                    "ModelServer": "mwh-deployment-cjiopf20sqh5re7qim5g:8001/atom"
//                }
//            },
//            {
//                "id": "8e5e3dbc-7e29-4068-9205-37f78fd6c3cc",
//                "widget_id": "WidgetKeyVectorDBSearch",
//                "ui": "{\"width\":400,\"height\":237,\"id\":\"8e5e3dbc-7e29-4068-9205-37f78fd6c3cc\",\"position\":{\"x\":816.5694599023882,\"y\":38.36225548005223},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyVectorDBSearch\",\"name\":\"向量搜索\",\"desc\":\"向量搜索\",\"group\":\"WidgetGroupVD\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"SearchText\",\"name\":\"搜索文本\",\"desc\":\"搜索文本\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"DataBase\",\"name\":\"数据库\",\"desc\":\"数据库\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\",\"DataBase\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":816.5694599023882,\"y\":38.36225548005223},\"dragging\":false}",
//                "values": {
//                    "DataBase": "test",
//					"URL":""
//                }
//            },
//            {
//                "id": "2d085327-feae-45e7-8886-60489e8e526e",
//                "widget_id": "角色提示",
//                "ui": "{\"width\":400,\"height\":209,\"id\":\"2d085327-feae-45e7-8886-60489e8e526e\",\"position\":{\"x\":589.7841397538127,\"y\":520.9435640372924},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"角色提示\",\"name\":\"角色提示\",\"desc\":\"作为{{.角色}}生成{{.任务}}\",\"group\":\"WidgetGroupPrompt\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"角色\",\"name\":\"角色\",\"desc\":\"角色\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"任务\",\"name\":\"任务\",\"desc\":\"任务\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":true,\"positionAbsolute\":{\"x\":589.7841397538127,\"y\":520.9435640372924},\"dragging\":false}",
//                "values": {}
//            },
//            {
//                "id": "a9cd770c-cd60-44f5-87fe-72d19e4cb7ae",
//                "widget_id": "WidgetKeyLLMModel",
//                "ui": "{\"width\":400,\"height\":237,\"id\":\"a9cd770c-cd60-44f5-87fe-72d19e4cb7ae\",\"position\":{\"x\":1159.3974530913183,\"y\":527.9172823690444},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyLLMModel\",\"name\":\"对话模型\",\"desc\":\"对话模型服务\",\"group\":\"WidgetGroupAIModel\",\"params\":[{\"data_class\":\"json\",\"category\":\"in-port\",\"define\":{\"id\":\"Text\",\"name\":\"文本\",\"desc\":\"文本\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"ModelServer\",\"name\":\"服务\",\"desc\":\"服务\",\"datasource\":\"mwh-deployment-cip0u4a0sqhb154duts0:8001/atom@@ChatGLM2-Lora-三国_三国-chatglm2-微调2_atom,mwh-deployment-cj1jq04l9ophc1ichv40:8001/atom@@chatglm-分布式微调-法律问答_chatglm-分布式微调-法律问答_atom\",\"show\":true,\"type\":\"TYPE_SELECTOR\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"json\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\",\"ModelServer\":\"mwh-deployment-cip0u4a0sqhb154duts0:8001/atom\"}},\"selected\":false,\"positionAbsolute\":{\"x\":1159.3974530913183,\"y\":527.9172823690444},\"dragging\":false}",
//                "values": {
//                    "ModelServer": "mwh-deployment-cip0u4a0sqhb154duts0:8001/atom"
//                }
//            }
//        ],
//        "edges": [
//            {
//                "id": "77bb0418-5482-4b4d-a89e-44c36f559eca",
//                "source": "06d56982-e51f-4909-8606-ec56dd1c9313",
//                "source_param": "06d56982-e51f-4909-8606-ec56dd1c9313@@OutPut",
//                "target": "e29e46aa-5a00-4c52-8dd9-038e5a0ec029",
//                "target_param": "e29e46aa-5a00-4c52-8dd9-038e5a0ec029@@Text"
//            },
//            {
//                "id": "77bb0418-5482-4b4d-a89e-44c36f559eca",
//                "source": "e29e46aa-5a00-4c52-8dd9-038e5a0ec029",
//                "source_param": "e29e46aa-5a00-4c52-8dd9-038e5a0ec029@@OutPut",
//                "target": "8e5e3dbc-7e29-4068-9205-37f78fd6c3cc",
//                "target_param": "8e5e3dbc-7e29-4068-9205-37f78fd6c3cc@@SearchText"
//            },
//            {
//                "id": "77bb0418-5482-4b4d-a89e-44c36f559eca",
//                "source": "8e5e3dbc-7e29-4068-9205-37f78fd6c3cc",
//                "source_param": "8e5e3dbc-7e29-4068-9205-37f78fd6c3cc@@OutPut",
//                "target": "2d085327-feae-45e7-8886-60489e8e526e",
//                "target_param": "2d085327-feae-45e7-8886-60489e8e526e@@角色"
//            },
//            {
//                "id": "77bb0418-5482-4b4d-a89e-44c36f559eca",
//                "source": "06d56982-e51f-4909-8606-ec56dd1c9313",
//                "source_param": "06d56982-e51f-4909-8606-ec56dd1c9313@@OutPut",
//                "target": "2d085327-feae-45e7-8886-60489e8e526e",
//                "target_param": "2d085327-feae-45e7-8886-60489e8e526e@@任务"
//            },
//            {
//                "id": "77bb0418-5482-4b4d-a89e-44c36f559eca",
//                "source": "2d085327-feae-45e7-8886-60489e8e526e",
//                "source_param": "2d085327-feae-45e7-8886-60489e8e526e@@OutPut",
//                "target": "a9cd770c-cd60-44f5-87fe-72d19e4cb7ae",
//                "target_param": "a9cd770c-cd60-44f5-87fe-72d19e4cb7ae@@Text"
//            }
//        ],
//        "viewport": {
//            "x": 189.772924184369,
//            "y": 112.23099637813766,
//            "zoom": 0.6070974421975234
//        }
//    }`
//	chain := &widgets.Chain{}
//	if err := json.Unmarshal([]byte(chainStr), &chain); err != nil {
//		t.Fatal(err)
//	}
//	builder := NewScriptBuilder(chain, nil)
//	_ = builder
//	widgets.Init()
//	initTestConf()
//	//widgets.WidgetFactoryImpl.RegisterOrUpdate(&Instance{
//	//	widget: MockWidget(),
//	//	script: &widgets.ScriptConfig{
//	//		ScriptClassName:      "promptTmpl",
//	//		ScriptClassParamFunc: widgets.FunPromptParamTransfer,
//	//	},
//	//})
//	//script, err := builder.GenerateScript()
//	//if err != nil {
//	//	t.Fatal(err)
//	//}
//	//
//	//t.Log(script)
// }
//
// func initTestConf() {
//	ctx := context.Background()
//	_ = ctx
//	viper.AddConfigPath("../../etc/")
//	viper.SetConfigName("app")
//	viper.SetConfigType("yaml")
//	viper.ReadInConfig()
//	viper.Unmarshal(&conf.Config, conf.DecConfig)
// }
//
// type Instance struct {
//	widget *widgets.Widget
//	script *widgets.ScriptConfig
// }
//
// func (i Instance) Define() *widgets.Widget {
//	return i.widget
//
// }
//
// func (i Instance) ScriptConfig() *widgets.ScriptConfig {
//	return i.script
// }
//
// func MockWidget() *widgets.Widget {
//	str := `{
//  "id": "角色提示",
//  "name": "角色提示",
//  "desc": "作为{{.角色}}生成{{.任务}}",
//  "group": "WidgetGroupPrompt",
//  "params": [
//    {
//      "data_class": "string",
//      "category": "in-port",
//      "define": {
//        "id": "角色",
//        "name": "角色",
//        "desc": "角色",
//        "type": "TYPE_INPUT",
//        "data_type": "DATA_TYPE_STRING"
//      }
//    },
//    {
//      "data_class": "string",
//      "category": "in-port",
//      "define": {
//        "id": "任务",
//        "name": "任务",
//        "desc": "任务",
//        "type": "TYPE_INPUT",
//        "data_type": "DATA_TYPE_STRING"
//      }
//    },
//    {
//      "data_class": "string",
//      "category": "out-port",
//      "define": {
//        "id": "OutPut",
//        "type": "TYPE_INPUT",
//        "data_type": "DATA_TYPE_STRING"
//      }
//    }
//  ]
// }`
//	w := &widgets.Widget{}
//	json.Unmarshal([]byte(str), &w)
//	return w
// }
