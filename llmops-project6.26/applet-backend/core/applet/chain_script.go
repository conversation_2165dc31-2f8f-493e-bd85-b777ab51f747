package applet

//
// import (
//	"fmt"
//	"github.com/aws/smithy-go/ptr"
//	"github.com/go-faster/errors"
//	"github.com/influxdata/kapacitor/tick"
//	"reflect"
//	"sort"
//	"strings"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
//	"transwarp.io/applied-ai/applet-backend/conf"
//	"transwarp.io/applied-ai/applet-backend/pkg/helper"
//	"transwarp.io/applied-ai/applet-backend/pkg/models"
//	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
// )
//
// const (
//	firstInput = "var_1000"
// )
//
// type ScriptBuilder struct {
//	// 原始数据
//	chain *widgets.Chain
//	// 用户输入算子的值
//	widgetParams models.WidgetParams
//	// 用于生成script的变量 ex:var_1001
//	paramValueGen *ParamValueGenerator
//	// 最终生成script的物料
//	scriptDesc WidgetDescs
// }
//
// // NewScriptBuilder params : 用户输入的参数值，优先级最高，覆盖应用链编排（chain）中的值
// func NewScriptBuilder(chain *widgets.Chain, params models.WidgetParams) *ScriptBuilder {
//	s := &ScriptBuilder{}
//	s.chain = chain
//	s.scriptDesc = WidgetDescs{WidgetMap: make(map[string]*WidgetDesc)}
//	gen := &ParamValueGenerator{
//		currParamIdxDepth: uint16(1),
//		currParamIdx:      uint16(0),
//	}
//	gen.paramIdxGenerator = func() uint16 {
//		res := gen.currParamIdxDepth*1000 + gen.currParamIdx + 1
//		gen.currParamIdx++
//		return res
//	}
//	s.widgetParams = params
//	s.paramValueGen = gen
//	return s
// }
//
// type ParamValueGenerator struct {
//	currParamIdxDepth uint16
//	currParamIdx      uint16
//	paramIdxGenerator func() uint16
// }
//
// type WidgetDescs struct {
//	WidgetMap map[string]*WidgetDesc
// }
//
// func (c *WidgetDescs) append(s *WidgetDesc) error {
//	if _, ok := c.WidgetMap[s.NodeUUID]; ok {
//		return errors.Errorf("node already exists")
//	}
//	c.WidgetMap[s.NodeUUID] = s
//	return nil
// }
//
// func (c *WidgetDescs) mergeInput(nodeUUID string, nodeScriptVal uint16, param string) error {
//	if v, ok := c.WidgetMap[nodeUUID]; !ok {
//		return errors.Errorf("node not exists")
//	} else {
//		v.Inputs[nodeScriptVal] = param
//	}
//	return nil
// }
//
// func (c *WidgetDescs) IncrIndex(nodeUUID string, idx uint32) error {
//	if v, ok := c.WidgetMap[nodeUUID]; !ok {
//		return errors.Errorf("node not exists")
//	} else {
//		v.VisitIdx = idx + v.VisitIdx
//	}
//	return nil
// }
//
// func (c *WidgetDescs) toString() (string, error) {
//	builder := strings.Builder{}
//	// 排序
//	ws := make([]*WidgetDesc, 0)
//	for _, s := range c.WidgetMap {
//		ws = append(ws, s)
//	}
//	sort.Slice(ws, func(i, j int) bool {
//		if ws[i].VisitIdx < ws[j].VisitIdx {
//			return true
//		}
//		return false
//	})
//	// 最前面加上listenHttp算子
//	builder.WriteString(fmt.Sprintf("var var_1000 = stream | listenHttp().path('%s').port(%s).timeout(60s)",
//		conf.Config.Kapacitor.APIPath, conf.Config.Kapacitor.APIPort))
//	for _, s := range ws {
//		if s.Output != nil {
//			builder.WriteString(fmt.Sprintf("var var_%v", *s.Output))
//			builder.WriteString(" = ")
//		}
//		input := s.genInputScript()
//		builder.WriteString(input)
//		builder.WriteString("|")
//		builder.WriteString(fmt.Sprintf("%s(", s.NodeKey))
//		if len(s.NodeConstParam) != 0 {
//			// 目前只支持一个构造参数
//			builder.WriteString(fmt.Sprintf("'''%s'''", s.NodeConstParam[0]))
//		}
//		builder.WriteString(")")
//		for _, f := range s.WidgetParams {
//			if f.ParamValue == nil {
//				stdlog.Warnf("param :%v value is nil", f.ParamValue)
//				continue
//			}
//			valueType := reflect.TypeOf(f.ParamValue).String()
//			switch valueType {
//			case "string":
//				builder.WriteString(fmt.Sprintf(".%s('%s')", f.ParamName, f.ParamValue))
//			case "bool":
//				builder.WriteString(fmt.Sprintf(".%s(%s)", f.ParamName, strings.ToUpper(fmt.Sprintf("%v", f.ParamValue))))
//			default:
//				builder.WriteString(fmt.Sprintf(".%s(%v)", f.ParamName, f.ParamValue))
//			}
//		}
//		builder.WriteString("\n")
//		if s.Output == nil {
//			// 最后一个节点
//			builder.WriteString("|")
//			builder.WriteString(fmt.Sprintf("%s()", "httpRsp"))
//		}
//	}
//	oriStr := builder.String()
//	formatString, err := tick.Format(oriStr)
//	if err != nil {
//		return "", err
//	}
//	return formatString, nil
// }
//
// type WidgetDesc struct {
//	// 用于排序
//	VisitIdx       uint32
//	Inputs         map[uint16]string
//	Output         *uint16
//	WidgetParams   []*WidgetParam
//	NodeKey        string
//	NodeConstParam []string
//	NodeUUID       string
// }
//
// func (w WidgetDesc) genInputScript() string {
//	vars := make([]string, 0)
//	alias := make([]string, 0)
//	for k, v := range w.Inputs {
//		vars = append(vars, fmt.Sprintf("var_%v", k))
//		alias = append(alias, v)
//	}
//	if len(vars) == 0 {
//		return firstInput
//	}
//	if len(vars) == 1 {
//		return vars[0]
//	}
//	res := fmt.Sprintf("%s|join(", vars[0])
//	for _, v := range vars[1 : len(vars)-1] {
//		res = fmt.Sprintf("%s,", v)
//	}
//	res = fmt.Sprintf("%s%s", res, vars[len(vars)-1])
//
//	res = fmt.Sprintf("%s%s", res, ").as(")
//	for _, v := range alias[:len(alias)-1] {
//		res = fmt.Sprintf("%s'%s',", res, v)
//	}
//	res = fmt.Sprintf("%s'%s'", res, alias[len(alias)-1])
//	res = fmt.Sprintf("%s).tolerance(10s)", res)
//	return res
// }
//
// type Class struct {
//	ClassName string
// }
//
// type WidgetParam struct {
//	ParamName  string
//	ParamValue interface{}
// }
//
// type widgetNode struct {
//	Node     *widgets.ChainNode
//	Parents  []*widgetNode
//	Children []*widgetNode
//	// 连接到children的哪个参数
//	ChildrenParam []string
//	UserInput     []*widgets.WidgetParam
//	Visited       bool
// }
//
// func (s *ScriptBuilder) GenerateScript() (string, error) {
//	startNodes, err := s.buildGraph()
//	if err != nil {
//		return "", err
//	}
//	// 第一个节点没有input，所以参数为nil
//	if len(startNodes) > 1 {
//		return "", stderr.Unsupported.Error("too many start node")
//	}
//	if err := s.buildScript(startNodes[0], map[uint16]string{}, 0); err != nil {
//		return "", err
//	}
//	script, err := s.scriptDesc.toString()
//	if err != nil {
//		return "", err
//	}
//	return script, nil
// }
//
// func (s *ScriptBuilder) buildGraph() ([]*widgetNode, error) {
//	widgetNodeMap := make(map[string]*widgetNode)
//	if s.chain == nil || len(s.chain.Nodes) == 0 {
//		return nil, helper.AppletChainInvalidNoWidgetErr.Error("no chain or no chain widgets")
//	}
//	for _, n := range s.chain.Nodes {
//		n := n
//		widgetNodeMap[n.Id] = &widgetNode{
//			Node: &n,
//		}
//	}
//	for _, e := range s.chain.Edges {
//		fromNode, ok := widgetNodeMap[e.SourceNode]
//		if !ok {
//			return nil, errors.Errorf("no chain id :%v", e.SourceNode)
//		}
//		toNode, ok := widgetNodeMap[e.TargetNode]
//		if !ok {
//			return nil, errors.Errorf("no chain id :%v", e.TargetNode)
//		}
//		if fromNode.Children == nil {
//			fromNode.Children = make([]*widgetNode, 0)
//		}
//		if fromNode.ChildrenParam == nil {
//			fromNode.ChildrenParam = make([]string, 0)
//		}
//		if toNode.Parents == nil {
//			toNode.Parents = make([]*widgetNode, 0)
//		}
//		fromNode.Children = append(fromNode.Children, toNode)
//		targetParam, err := e.GetTargetParam()
//		if err != nil {
//			return nil, err
//		}
//		fromNode.ChildrenParam = append(fromNode.ChildrenParam, targetParam)
//		toNode.Parents = append(toNode.Parents, fromNode)
//	}
//	// first node
//	var firstNodes []*widgetNode
//	for _, wn := range widgetNodeMap {
//		if len(wn.Parents) == 0 {
//			firstNodes = append(firstNodes, wn)
//		}
//	}
//	// 没有开始节点（环）
//	if len(firstNodes) < 1 {
//		return nil, errors.Errorf("no start node")
//	}
//	return firstNodes, nil
// }
//
// func (s *ScriptBuilder) getWidgetParams(widget widgets.IWidget, chainNode *widgets.ChainNode) ([]*WidgetParam, error) {
//	params := make([]*WidgetParam, 0)
//	// 算子自定义的到script转换规则优先级最高
//	if widget.ScriptConfig().ParamTransferFunc != nil {
//		ps, err := widget.ScriptConfig().ParamTransferFunc(chainNode)
//		if err != nil {
//			return nil, err
//		}
//		for k, v := range ps {
//			params = append(params, &WidgetParam{
//				ParamName:  k,
//				ParamValue: v,
//			})
//		}
//	} else {
//		// 通过算子的定义自动生成script
//		for _, param := range widget.Define().Params {
//			// func name
//			funcName := param.Define.Id
//			// func value 如果没有则为空
//			var funcValue interface{}
//			if param.Category != widgets.ParamTypeAttribute {
//				continue
//			}
//			if _, ok := widget.ScriptConfig().HideInput[funcName]; ok {
//				continue
//			}
//			if v, ok := chainNode.Values[funcName]; ok {
//				funcValue = v
//			}
//			params = append(params, &WidgetParam{
//				ParamName:  funcName,
//				ParamValue: funcValue,
//			})
//		}
//		if widget.ScriptConfig().ExtraInput != nil {
//			for k, v := range widget.ScriptConfig().ExtraInput {
//				params = append(params, &WidgetParam{
//					ParamName:  k,
//					ParamValue: v,
//				})
//			}
//		}
//	}
//	return params, nil
// }
//
// func (s *ScriptBuilder) buildScript(node *widgetNode, inputs map[uint16]string, visitIdx uint32) error {
//	// 节点已经访问过，不再访问，只需要聚合输入
//	var nextInputParamKey uint16
//	inputMap := make(map[uint16]string)
//	if node.Visited {
//		for k, v := range inputs {
//			if err := s.scriptDesc.mergeInput(node.Node.Id, k, v); err != nil {
//				return err
//			}
//		}
//		s.scriptDesc.IncrIndex(node.Node.Id, visitIdx)
//	} else {
//		widget := widgets.WidgetFactoryImpl.GetWidget(node.Node.WidgetId)
//		if widget == nil {
//			return errors.Errorf("widget is nil,id :%v", node.Node.WidgetId)
//		}
//
//		widgetKey := widget.Define().Id
//		// 生成script的key
//		nodeKey := widgetKey
//		scriptCfg := widget.ScriptConfig()
//		if scriptCfg != nil {
//			nodeKey = widget.ScriptConfig().ScriptClassName
//		}
//		nodeUUID := node.Node.Id
//		// 用户在调试界面输入的值替换掉应用链中的默认值
//		if s.widgetParams != nil {
//			widgetIDParamMap, err := s.widgetParams.ToWidgetParamMap()
//			if err != nil {
//				return err
//			}
//			if params, ok := widgetIDParamMap[node.Node.WidgetId]; ok {
//				for k, v := range params {
//					if node.Node.Values == nil {
//						node.Node.Values = make(map[string]interface{})
//					}
//					node.Node.Values[k] = v
//				}
//			}
//		}
//		// todo node.Node.Values中的值需要和widget的param对比，过滤掉不存在的值
//		params, err := s.getWidgetParams(widget, node.Node)
//		if err != nil {
//			return err
//		}
//		var outPut *uint16
//		if len(node.Children) > 0 {
//			nextInputParamKey = s.paramValueGen.paramIdxGenerator()
//			outPut = ptr.Uint16(nextInputParamKey)
//		}
//		desc := &WidgetDesc{
//			VisitIdx:     visitIdx,
//			Inputs:       inputs,
//			Output:       outPut,
//			NodeKey:      nodeKey,
//			WidgetParams: params,
//			NodeUUID:     nodeUUID,
//		}
//		if widget.ScriptConfig().ScriptClassParamFunc != nil {
//			if classParam, err := widget.ScriptConfig().ScriptClassParamFunc(node.Node); err != nil {
//				return err
//			} else {
//				desc.NodeConstParam = []string{classParam}
//			}
//		}
//		if err := s.scriptDesc.append(desc); err != nil {
//			return err
//		}
//		node.Visited = true
//	}
//
//	for idx, c := range node.Children {
//		if nextInputParamKey != 0 {
//			inputMap = map[uint16]string{nextInputParamKey: node.ChildrenParam[idx]}
//		}
//		if err := s.buildScript(c, inputMap, visitIdx+1); err != nil {
//			return err
//		}
//		// 分流的场景
//		s.paramValueGen.currParamIdxDepth++
//	}
//	return nil
//
// }
