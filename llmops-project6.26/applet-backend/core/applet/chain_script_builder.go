package applet

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type ScriptGenerator struct {
	chainID   string
	chainName string
	APIPath   *string
	nodes     ScriptNodes
	Context   context.Context
}

func NewScriptGenerator(ctx context.Context, chainDO *models.AppletChainDO, APIPath *string) (*ScriptGenerator, error) {
	generator := &ScriptGenerator{
		chainID:   chainDO.Base.ID,
		chainName: chainDO.Base.Name,
		nodes:     ScriptNodes{nodes: map[string]*ScriptNode{}},
		APIPath:   APIPath,
		Context:   ctx,
	}
	nodeMap := chainDO.GetNodeMap()
	// 只有一个节点
	if len(nodeMap) == 1 {
		for k, v := range nodeMap {
			if err := generator.nodes.InsertNodes(k, v); err != nil {
				return nil, err
			}
		}
	} else {
		for _, e := range chainDO.ChainDetail.Edges {
			sourceNode := nodeMap[e.SourceNode]
			targetNode := nodeMap[e.TargetNode]
			if sourceNode == nil || targetNode == nil {
				return nil, helper.AppletChainBuildScriptErr.Error("node :%v or :%v not exist", e.SourceNode, e.TargetNode)
			}
			source := generator.nodes.GetNode(sourceNode.Id)
			target := generator.nodes.GetNode(targetNode.Id)
			if source == nil {
				if err := generator.nodes.InsertNodes(sourceNode.Id, sourceNode); err != nil {
					return nil, err
				}
				source = generator.nodes.GetNode(sourceNode.Id)
			}
			if target == nil {
				if err := generator.nodes.InsertNodes(targetNode.Id, targetNode); err != nil {
					return nil, err
				}
				target = generator.nodes.GetNode(targetNode.Id)
			}
			if err := source.AppendChildren(target, &e); err != nil {
				return nil, err
			}
			if err := target.AppendParent(source, &e); err != nil {
				return nil, err
			}
		}
	}

	return generator, nil
}

type ScriptNodes struct {
	nodes map[string]*ScriptNode
}

func (s *ScriptNodes) GetNode(nodeId string) *ScriptNode {
	return s.nodes[nodeId]
}

func (s *ScriptNodes) InsertNodes(nodeId string, widgetNode *widgets.ChainNode) error {
	widgetDetail := widgetNode.WidgetDetail
	// 兼容老数据
	if widgetDetail == nil {
		iWidget, err := widgets.WidgetFactoryImpl.GetWidget(widgetNode.WidgetId)
		if err != nil {
			return stderr.Wrap(err, "insert node err ,invalid node :%v", widgetNode)
		}
		widgetDetail = iWidget.Define()
	}
	widgetKey := widgetNode.WidgetId
	if widgetKey == "" {
		return helper.AppletChainBuildScriptErr.Error("dynamic widgetDefine no origin widgetDefine key,widgetDefine :%v", widgetDetail)
	}
	widget, err := widgets.WidgetFactoryImpl.GetWidget(widgetKey)
	if err != nil {
		return err
	}

	script := &ScriptNode{
		NodeID:       nodeId,
		NodeInfo:     *widgetNode,
		WidgetDefine: widget,
		PreNodes:     make([]*ScriptNode, 0),
		NextNodes:    make([]*ScriptNode, 0),
	}
	if widgetNode.SubChainBaseInfo != nil {
		script.SubChainBaseInfo = &SubChainBaseInfo{
			SubChainName:     widgetNode.SubChainBaseInfo.SubChainName,
			SubChainWidgetID: widgetNode.SubChainBaseInfo.SubChainWidgetID,
		}
	}
	s.nodes[nodeId] = script

	return nil
}

func (s *ScriptNode) AppendChildren(node *ScriptNode, edge *widgets.ChainEdge) error {
	children := s.NextNodes
	if children == nil {
		children = make([]*ScriptNode, 0)
	}
	children = append(children, node)
	sourceParam, err := edge.GetSourceParam()
	if err != nil {
		return stderr.Wrap(err, "build script,build children err")
	}
	targetParam, err := edge.GetTargetParam()
	if err != nil {
		return stderr.Wrap(err, "build script,build children err")
	}
	outputInfo := &widgets.NodeOutputInfo{
		NextNodeID:             node.NodeID,
		NextNodeInputParam:     targetParam,
		CurrentNodeOutputParam: sourceParam,
		NextNodeVar:            getVarParam(node.NodeID),
	}
	s.NextNodes = children
	s.OutputInfos = append(s.OutputInfos, outputInfo)
	return nil
}

func (s *ScriptNode) AppendParent(node *ScriptNode, edge *widgets.ChainEdge) error {
	pre := s.PreNodes
	if pre == nil {
		pre = make([]*ScriptNode, 0)
	}

	sourceParam, err := edge.GetSourceParam()
	if err != nil {
		return stderr.Wrap(err, "build script,build parent err")
	}
	targetParam, err := edge.GetTargetParam()
	if err != nil {
		return stderr.Wrap(err, "build script,build parent err")
	}
	pre = append(pre, node)
	inputNodeInfo := &widgets.NodeInputInfo{
		PreNodeID:             node.NodeID,
		PreNodeOutPutParam:    sourceParam,
		CurrentNodeInputParam: targetParam,
		PreNodeVar:            getVarParam(node.NodeID),
	}
	s.PreNodes = pre
	s.InputInfos = append(s.InputInfos, inputNodeInfo)
	return nil
}

type PreScriptNode struct {
	ScriptNode
	Connect string // 前置节点与当前节点的连接点
}

//type ConnectModel string
//
//const (
//	ConnectModelJoin  = "join"  // 不同的算子连接到不同的输入端点上
//	ConnectModelUnion = "union" // 不同的算子连接到相同的输入端点上
//)

type ScriptNode struct {
	NodeID           string                    // 节点唯一标识
	SubChainBaseInfo *SubChainBaseInfo         // 子链基础信息
	NodeInfo         widgets.ChainNode         // 节点详情
	WidgetDefine     widgets.IWidget           // 节点定义
	PreNodes         []*ScriptNode             // 前置节点
	NextNodes        []*ScriptNode             // 后置节点
	InputInfos       []*widgets.NodeInputInfo  // 节点输入信息
	OutputInfos      []*widgets.NodeOutputInfo // 节点输出信息
	//NexOutputOfNodes []string          //每个后置节点，对应的输出端点
	//PreConnects      []string          //每个前置节点连接到哪个输入参数上
	//PreOutPuts       []string          //被每个前置节点对应的哪个输出端点连接（1.3.1开始支持多个输出端点）
}

type SubChainBaseInfo struct {
	SubChainName string
	// 子链拖入画布之后的算子ID
	SubChainWidgetID string
}

func (s *ScriptNodes) getOutputNode() ([]*ScriptNode, error) {
	lastNodes := make([]*ScriptNode, 0)
	for _, n := range s.nodes {
		if len(n.NextNodes) == 0 {
			lastNodes = append(lastNodes, n)
		}
	}
	return lastNodes, nil
}

func (s *ScriptNodes) getInputNodes() ([]*ScriptNode, error) {
	firstNodes := make([]*ScriptNode, 0)
	for _, n := range s.nodes {
		if len(n.PreNodes) == 0 {
			firstNodes = append(firstNodes, n)
		}
	}
	if len(firstNodes) == 0 {
		return nil, stderr.Internal.Error("invalid chain :%v,no input node", s)
	}
	return firstNodes, nil
}

func (s *ScriptNode) setConnectNodeInfoToNodeValue(value map[string]interface{}) error {
	//nodeInputInfos := make([]widgets.NodeInputInfo, 0)
	//for i := 0; i < len(s.PreNodes); i++ {
	//	nodeInfo := s.PreNodes[i]
	//	inputInfo, ok := s.InputInfo[nodeInfo.NodeID]
	//	if !ok {
	//		return stderr.Internal.Error("build chain script err,node :%v not exist in input info", nodeInfo.NodeID)
	//	}
	//	nodeInputInfos = append(nodeInputInfos, inputInfo)
	//}
	//nodeOutputInfos := make([]widgets.NodeOutputInfo, 0)
	//for i := 0; i < len(s.NextNodes); i++ {
	//	nodeInfo := s.NextNodes[i]
	//	outputInfo, ok := s.OutputInfo[nodeInfo.NodeID]
	//	if !ok {
	//		return stderr.Internal.Error("build chain script err,node :%v not exist in outpuy info", nodeInfo.NodeID)
	//	}
	//	nodeOutputInfos = append(nodeOutputInfos, outputInfo)
	//}
	//inputInfo := widgets.NodeInputInfos{NodeInputs: nodeInputInfos}
	//outputInfo := widgets.NodeOutputInfos{NodeOutputs: nodeOutputInfos}

	inputInfo := widgets.NodeInputInfos{NodeInputs: s.InputInfos}
	outputInfo := widgets.NodeOutputInfos{NodeOutputs: s.OutputInfos}
	value[widgets.NodeInputInfoKey] = inputInfo
	value[widgets.NodeOutputInfoKey] = outputInfo
	return nil
}
