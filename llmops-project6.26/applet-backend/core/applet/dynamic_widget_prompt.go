package applet

import (
	"context"
	"fmt"
	"github.com/go-faster/errors"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type WidgetPromptSvc struct {
}

func (p WidgetPromptSvc) ListDynamicWidgets(ctx context.Context) ([]*models.DynamicWidgetDesc, error) {
	promptTemplates, err := clients.PromptCli.ListChainPrompts(ctx)
	if err != nil {
		return nil, err
	}
	res := models.PromptsToDynamicWidgetDesc(promptTemplates)
	return res, err
}

func (p WidgetPromptSvc) GetWidget(ctx context.Context, widgetKey string) (*widgets.Widget, error) {
	promptTemplates, err := clients.PromptCli.ListChainPrompts(ctx)
	if err != nil {
		return nil, err
	}
	var target *models.PromptTemplate
	for _, p := range promptTemplates {
		if fmt.Sprintf("%v", p.ID) == widgetKey {
			target = p
			break
		}
	}
	if target == nil {
		return nil, errors.Errorf("get widget key err,key :%v not found ", widgetKey)
	}
	return target.ToWidgetModel()

}
