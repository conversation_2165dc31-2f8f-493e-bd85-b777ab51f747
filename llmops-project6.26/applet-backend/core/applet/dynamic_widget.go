package applet

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

var DynamicWidgetSvcFactory map[string]DynamicWidgetSvc

func init() {
	DynamicWidgetSvcFactory = make(map[string]DynamicWidgetSvc)
	DynamicWidgetSvcFactory[models.DynamicWidgetTypePrompt.Desc] = &WidgetPromptSvc{}
	DynamicWidgetSvcFactory[models.DynamicWidgetTypeSubChain.Desc] = &WidgetSubChainSvc{}
	DynamicWidgetSvcFactory[models.DynamicWidgetTypeExternalAPI.Desc] = &WidgetExternalAPISvc{}
	DynamicWidgetSvcFactory[models.DynamicWidgetTypeCustomWidget.Desc] = &WidgetCustomSvc{}
}

type DynamicWidgetSvc interface {
	ListDynamicWidgets(ctx context.Context) ([]*models.DynamicWidgetDesc, error)

	// GetWidget
	// TODO 尽量将算子定义移动到/pkg/widgets下,生成动态算子时通过widgetFactory获取定义,再作填充
	GetWidget(ctx context.Context, widgetKey string) (*widgets.Widget, error)
}

type DynamicInputWidgetSvc interface {
	GetWidget(ctx context.Context, params []string) (*widgets.Widget, error)
}
