package applet

import (
	"context"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"

	"github.com/aws/smithy-go/ptr"
	"github.com/go-faster/errors"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const (
	NSPlaceholde = "{namespace}"
)

type Chain interface {
	// GenerateTickScriptV2 获取tick script
	GenerateTickScriptV2(ctx context.Context, chainID string, APIPath *string) (string, error)
	// GetChainByID 获取应用链详情
	GetChainByID(ctx context.Context, chainID string) (*models.AppletChainDO, error)
	Exist(ctx context.Context, chainID string) bool
	// DeleteChain 删除应用链AppletChainDO
	DeleteChain(ctx context.Context, chainID string) error
	// BatchDeleteChain 批量删除
	BatchDeleteChain(ctx context.Context, chainIDs []string) error
	// SearchForBaseDO 搜索应用链列表,只展示基础信息
	SearchForBaseDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainBaseDO, error)
	// SearchForSimpleChainDO 搜索应用链列表,排除应用链编排信息和智能体配置信息
	SearchForSimpleChainDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainDO, error)
	// SearchForChainDO 搜索应用链列表,所有信息
	SearchForChainDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainDO, error)

	GetProjectIds() ([]string, error)

	// CreateChainBasic 创建应用链(只能创建基础信息
	CreateChainBasic(ctx context.Context, chain *models.AppletChainDO) (string, error)

	// CreateChain 创建完整应用链
	CreateChain(ctx context.Context, chain *models.AppletChainDO) (string, error)

	// UpdateChainBasic 更新应用链基础信息
	UpdateChainBasic(ctx context.Context, chainID string, chain *models.AppletChainBaseDO) error
	// UpdateChainDetail 更新应用链编排信息
	UpdateChainDetail(ctx context.Context, chainID string, chain *widgets.Chain) error
	// UpdateChainDebugInfo 更新应用链调试信息
	UpdateChainDebugInfo(ctx context.Context, chainID string, info *models.ChainDebugInfo) error
	// UpdateAppletChain 支持更新应用链的全部信息
	UpdateAppletChain(ctx context.Context, chainID string, appletChain *models.AppletChainDO) error
	// GetDeployCfg 生成部署配置
	GetDeployCfg(ctx context.Context, chainID string) (*models.ChainDeployCfg, error)

	UpdateByGenerated(ctx context.Context, chainID string, model *generated.AppletChain) error
	SetSelectedApps(ctx context.Context, setReq *pb.SetSelectedReq,
		actionType SetSelectedAppsActionType) error

	GetAppDependencyInfos(ctx context.Context) (map[string]*models.DependencyInfo, error)

	GetAppDependencyInfo(ctx context.Context, chainID string) (*models.DependencyInfo, error)
}

type SetSelectedAppsActionType string

const (
	ActionTypeAdd    SetSelectedAppsActionType = "add"
	ActionTypeRemove SetSelectedAppsActionType = "remove"
)

type SearchChainParam struct {
	UserID            string
	ProjectID         string
	AssetType         *int32
	CreatedBy         string
	FilterEmpty       *bool //是否过滤无编排信息的应用链
	FilterDebugFailed *bool //是否过滤调试未成功的
}

type ChainImpl struct {
}

func (c ChainImpl) GetDeployCfg(ctx context.Context, chainID string) (*models.ChainDeployCfg, error) {
	chain, err := c.GetChainByID(ctx, chainID)
	if err != nil {
		return nil, err
	}
	script, err := c.GenerateTickScriptV2(ctx, chainID, nil)
	if err != nil {
		return nil, err
	}
	pvcMountCfgs, err := models.PVCMountCfgString2DO(conf.Config.ChainDeployCfg.PVCMountCFGs)
	if err != nil {
		return nil, err
	}

	oriQT, err := chain.GetQueryTemplate(false)
	if err != nil {
		return nil, stderr.Wrap(err, "get origin query template err ,chain :%v", chain)
	}
	stdQT, err := chain.GetQueryTemplate(true)
	if err != nil {
		return nil, stderr.Wrap(err, "get std query template err ,chain :%v", chain)
	}

	chainSnapshotIdStr, err := InsertSnapshot(ctx, chain)
	if err != nil {
		return nil, stderr.Wrap(err, "快照写入失败")
	}

	// 创建 tick_path
	tenantId := helper.GetTenantID(ctx)

	pathEnd := fmt.Sprintf("applet/tick-scripts/%s/%s/tick-script.txt", chainID, chainSnapshotIdStr)
	tickPath := createPath(tenantId, pathEnd)
	// 将 tick 写入 tick_path
	if err := WriteByteToFile(tickPath, []byte(script)); err != nil {
		return nil, err
	}

	// 配置应用发布时使用的环境变量engine
	nameSpace := k8s.CurrentNamespaceInCluster()
	deployConfig := conf.Config.ChainDeployCfg
	chainDeployCfg := &models.ChainDeployCfg{
		ChainID:     chainID,
		ChainDetail: chain,
		TickScript:  script,
		Image:       deployConfig.BaseImage,
		PVCMountCfg: pvcMountCfgs,
		Envs: map[string]string{
			"TASK_ID":                               chainID,
			"GOLANG_PROTOBUF_REGISTRATION_CONFLICT": "warn",
			"KAPACITOR_MQTT_0_ENABLED":              "false",
			"KAPACITOR_SMTP_ENABLED":                "false",
			"KAPACITOR_INFLUXDB_0_ENABLED":          "false",
			"ENGINE_SFS.LOCAL_ROOT":                 deployConfig.SfsLocalRoot,
			"ENGINE_TENANT.TENANT_ID":               helper.GetTenantID(ctx),
			"ENGINE_TENANT.PROJECT_ID":              helper.GetProjectID(ctx),
			// TODO engine中存在许多url需要访问系统服务,发布时依靠backend设置namesapce, 当环境变量有改动时，这样维护不方便。
			//  优化方案  engine中定义环境变量时namespace使用占位符,启动时进行一次渲染。backend发布服务时,则只需要设置好系统namespace即可
			"ENGINE_TENANT.SYSTEM_NAMESPACE": nameSpace,
			ChainTickScriptPathEnv:           pathEnd,
			// 依赖服务配置
			"ENGINE_REDIS.USERNAME":                               deployConfig.Redis.Username,
			"ENGINE_REDIS.PASSWORD":                               deployConfig.Redis.Password,
			"ENGINE_REDIS.DATABASE":                               fmt.Sprintf("%d", deployConfig.Redis.Database),
			"ENGINE_REDIS.MASTERNAME":                             deployConfig.Redis.MasterName,
			"ENGINE_REDIS.ADDRS":                                  strings.ReplaceAll(deployConfig.Redis.Addrs, NSPlaceholde, nameSpace),
			"KAPACITOR_HOSTNAME":                                  strings.ReplaceAll(deployConfig.KapacitorHostName, NSPlaceholde, nameSpace),
			"KAPACITOR_INFLUXDB_0_URLS_0":                         strings.ReplaceAll(deployConfig.KapacitorInfluxdbUrl, NSPlaceholde, nameSpace),
			"KAPACITOR_MQTT_0_URL":                                strings.ReplaceAll(deployConfig.KapacitorMqttUrl, NSPlaceholde, nameSpace),
			"ENGINE_MQTT.BROKER_ADDR":                             strings.ReplaceAll(deployConfig.EngineMqttUrl, NSPlaceholde, nameSpace),
			"ENGINE_AGENT_TOOL_EXECUTOR.KNOWLEDGE.PORT":           strings.ReplaceAll(deployConfig.EngineKnowledgeBasePort, NSPlaceholde, nameSpace),
			"ENGINE_AGENT_TOOL_EXECUTOR.KNOWLEDGE.HOST":           strings.ReplaceAll(deployConfig.EngineKnowledgeBaseHost, NSPlaceholde, nameSpace),
			"ENGINE_AGENT_TOOL_EXECUTOR.APPLET.MLOPS_CONFIG.HOST": strings.ReplaceAll(deployConfig.EngineMlopsHost, NSPlaceholde, nameSpace),
			"ENGINE_BACKEND_TASK.RUN_CODE_URL":                    strings.ReplaceAll(deployConfig.EngineRunCodeUrl, NSPlaceholde, nameSpace),
			"ENGINE_BACKEND_TASK.SECURITY_CENSOR_URL":             strings.ReplaceAll(deployConfig.EngineSecurityUrl, NSPlaceholde, nameSpace),
		},
		EndPoints: []*common.Endpoint{
			{
				Port: 1884,
				Type: common.EndpointType_ENDPOINT_TYPE_HTTP,
				ApiAttrs: []*common.APIAttr{
					{
						ApiPath:    "api/v1/std",
						ReqExample: stdsrv.AnyToString(stdQT),
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					},
					{
						ApiPath:    "api/v1",
						ReqExample: stdsrv.AnyToString(oriQT),
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					},
					{
						ApiPath:    "api/v1/health",
						ReqExample: "",
						Method:     common.HttpMethod_HTTP_METHOD_GET,
						ApiType:    common.APIType_API_TYPE_HEALTH_CHECK,
					},
				},
				IsDefault: true,
			},
		},
		ChainSnapshotId: chainSnapshotIdStr,
	}
	return chainDeployCfg, nil
}

func InsertSnapshot(ctx context.Context, chain *models.AppletChainDO) (string, error) {
	chainSnapshotModel := &generated.ChainSnapshot{
		ChainDetail: stdsrv.AnyToString(chain.ChainDetail),
		Base:        stdsrv.AnyToString(chain.Base),
		ChainID:     chain.Base.ID,
	}
	// 保存应用发布时的快照
	chainSnapshotID, err := dao.ChainSnapshotDAOImpl.Save(ctx, chainSnapshotModel)
	if err != nil {
		return "", err
	}
	return strconv.FormatInt(chainSnapshotID, 10), nil
}

func createPath(tenantID string, pathEnd string) string {
	rootPath := conf.Config.ChainDeployCfg.SfsPvcMountPath
	if rootPath == "" {
		rootPath = SfsPvcMountPath
	}
	// 本地调试用
	if os.Getenv("TICK_ROOT_PATH") != "" {
		rootPath = path.Join(os.Getenv("TICK_ROOT_PATH"), rootPath)
	}
	tickPath := path.Join(rootPath, "tenants", tenantID, pathEnd)

	return tickPath
}

func (c ChainImpl) UpdateChainDebugInfo(ctx context.Context, chainID string, info *models.ChainDebugInfo) error {
	if info == nil {
		return stderr.Internal.Error("no debug info")
	}
	// todo 校验参数是否存在
	debugInfoStr, err := info.ToString()
	if err != nil {
		return err
	}
	if _, err := dao.AppletChainDAOImpl.UpdateChain(ctx, chainID, &generated.AppletChain{
		DebugInfo: debugInfoStr,
	}); err != nil {
		return err
	}
	return nil
}

func (c ChainImpl) BatchDeleteChain(ctx context.Context, chainIDs []string) error {
	if len(chainIDs) == 0 {
		return nil
	}
	userID, err := helper.GetUser(ctx)
	if err != nil {
		return stderr.Wrap(err, "no user in context")
	}
	_ = userID
	return dao.AppletChainDAOImpl.BatchDeleteChain(ctx, chainIDs)
}

func (c ChainImpl) CreateChain(ctx context.Context, chain *models.AppletChainDO) (string, error) {
	if chain == nil {
		stdlog.Errorf("chain is nil")
		return "", errors.Errorf("chain is nil")
	}
	if err := chain.IsValid(); err != nil {
		stdlog.Errorf("validate chain err :%v", err)
		return "", err
	}

	// 检查名称是否重复
	projectIDForVerify := helper.GetProjectID(ctx)
	if chain.Base.ProjectID != "" {
		projectIDForVerify = chain.Base.ProjectID
	}
	if err := ValidChainNameForCreate(ctx, chain.Base.Name, projectIDForVerify); err != nil {
		return "", err
	}

	chainPO, err := chain.ToPO()
	if err != nil {
		stdlog.Errorf("chan convert to po err :%v", err)
		return "", err
	}

	id, err := dao.AppletChainDAOImpl.SaveChain(ctx, chainPO)
	if err != nil {
		stdlog.Errorf("save chain err :%v", err)
		return "", err
	}
	chain.Base.ID = id
	err = chain.SubmitPermission(ctx)
	if err != nil {
		stdlog.Errorf("submit permission err :%v", err)
		return "", err
	}
	return id, nil
}

func (c ChainImpl) UpdateChainBasic(ctx context.Context, chainID string, chain *models.AppletChainBaseDO) error {
	if err := ValidAuthAndState(ctx, chainID, []models.ChainDeployState{models.ChainStateOffline}); err != nil {
		return err
	}
	if chain == nil {
		return errors.Errorf("no chain")
	}
	if err := chain.IsValid(); err != nil {
		stdlog.Errorf("validate chain err :%v", err)
		return err
	}
	if err := ValidChainNameForUpdate(ctx, chainID, chain.Name, helper.GetProjectID(ctx)); err != nil {
		stdlog.Errorf("ValidChainName err :%v", err)
		return err
	}
	chain.UpdateTime = time.Now().UnixMilli()
	chainDO := models.AppletChainDO{
		Base: *chain,
	}
	chainPO, err := chainDO.ToPO()
	if err != nil {
		return err
	}
	if _, err := dao.AppletChainDAOImpl.UpdateBasicInfo(ctx, chainID, chainPO); err != nil {
		return err
	}
	return chainDO.SubmitPermission(ctx)
}

func (c ChainImpl) UpdateChainDetail(ctx context.Context, chainID string, chain *widgets.Chain) error {
	if chain == nil {
		return errors.Errorf("no chain")
	}
	if err := ValidChainDetailForUpdate(ctx, chain); err != nil {
		stdlog.Errorf("validate chain err :%v", err)
		return err
	}
	usageType := models.ChainUsageTypeCommon
	if chain.IsLLMChain() {
		usageType = models.ChainUsageTypeLLM
	}
	chainDetailStr, err := models.ConvertChanDetailDO2Str(chain)
	if err != nil {
		return err
	}
	updateModel := &generated.AppletChain{
		Chain:            chainDetailStr,
		ChainUpdatedTime: time.Now(),
		UsageType:        usageType,
	}
	containsSubChain := chain.ContainsSubChain()
	if containsSubChain {
		updateModel.ContainsSubChain = models.ChainContainsSubChain
	} else {
		updateModel.ContainsSubChain = models.ChainNotContainsSubChain
	}
	if _, err := dao.AppletChainDAOImpl.UpdateChain(ctx, chainID, updateModel); err != nil {
		return err
	}
	return nil
}

// UpdateAppletChain  传入AppletChainDO,转为generated.AppletChain再去更新,0值不更新
func (c ChainImpl) UpdateAppletChain(ctx context.Context, chainID string, appletChain *models.AppletChainDO) error {
	if appletChain == nil {
		return errors.Errorf("no chain")
	}
	if err := appletChain.Base.Validator(); err != nil {
		return stderr.Wrap(err, "chain base is invalid")
	}
	if appletChain.ChainDetail != nil {
		if err := appletChain.ChainDetail.IsValid(); err != nil {
			stdlog.Errorf("validate chain err :%v", err)
			return err
		}
	}
	if appletChain.Base.Name != "" {
		if err := ValidChainNameForUpdate(ctx, chainID, appletChain.Base.Name, helper.GetProjectID(ctx)); err != nil {
			stdlog.Errorf("ValidChainName err :%v", err)
			return err
		}
	}
	appletChain.Base.UpdateTime = time.Now().UnixMilli()
	updateModel, err := appletChain.ToPO()
	if err != nil {
		return err
	}
	id, err := dao.AppletChainDAOImpl.UpdateChain(ctx, chainID, updateModel)
	if err != nil {
		return err
	}
	appletChain.Base.ID = id
	return appletChain.SubmitPermission(ctx)
}

func (c ChainImpl) GetChainByID(ctx context.Context, chainID string) (*models.AppletChainDO, error) {
	chainPO, err := dao.AppletChainDAOImpl.GetByID(ctx, chainID)
	if err != nil {
		stdlog.Errorf("get chain :%v from db err :%v", chainID, err)
		return nil, err
	}

	// 暂时统一使用applet-chain中的last_debug_status,更新chainHistory表时会更新applet-chain表
	state := chainPO.LastDebugState

	//state, err := dao.ChainDebugHistoryImpl.GetLastDebugStateByChainID(ctx, chainID)
	//if err != nil {
	//	stdlog.Errorf("get chain debug history :%v from db err :%v", chainID, err)
	//	return nil, err
	//}

	chain, err := models.ConvertChainPO2ChainDO(chainPO, state)
	if err != nil {
		stdlog.Errorf("convert chain :%v err :%v", chainID, err)
		return nil, err
	}
	_, err = helper.FilterAndSetPermission(ctx, []*models.AppletChainDO{chain}, cas.ObjType_AppletChain)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to filter and set permission")
	}
	return chain, nil
}

func (c ChainImpl) Exist(ctx context.Context, chainID string) bool {
	_, err := c.GetChainByID(ctx, chainID)
	if err != nil {
		return false
	}
	return true
}

func (c ChainImpl) DeleteChain(ctx context.Context, chainID string) error {
	if err := ValidAuthAndState(ctx, chainID, []models.ChainDeployState{models.ChainStateOffline}); err != nil {
		return err
	}
	return dao.AppletChainDAOImpl.DeleteChain(ctx, chainID)
}

func searchSimplePOWithParams(ctx context.Context, searchParam *SearchChainParam) ([]*generated.AppletChain, error) {
	chainQueryParam := generated.AppletChain{}
	if searchParam.UserID != "" {
		chainQueryParam.Creator = searchParam.UserID
	}
	if searchParam.ProjectID != "" {
		chainQueryParam.ProjectID = searchParam.ProjectID
	}
	if searchParam.AssetType != nil {
		chainQueryParam.AssetType = *searchParam.AssetType
	}
	if searchParam.CreatedBy != "" {
		chainQueryParam.CreatedType = searchParam.CreatedBy
	}
	param := &dao.ChainQueryParam{
		FilterEmptyChain: searchParam.FilterEmpty,
	}
	chainPOs, err := dao.AppletChainDAOImpl.SimpleInfoQuery(ctx, &chainQueryParam, param)
	if err != nil {
		stdlog.Errorf("fuzzy query chain err :%v", err)
		return nil, err
	}
	return chainPOs, nil
}

func searchCompletePOWithParams(ctx context.Context, searchParam *SearchChainParam) ([]*generated.AppletChain, error) {
	chainQueryParam := generated.AppletChain{}
	if searchParam.UserID != "" {
		chainQueryParam.Creator = searchParam.UserID
	}
	if searchParam.ProjectID != "" {
		chainQueryParam.ProjectID = searchParam.ProjectID
	}
	if searchParam.AssetType != nil {
		chainQueryParam.AssetType = *searchParam.AssetType
	}
	if searchParam.CreatedBy != "" {
		chainQueryParam.CreatedType = searchParam.CreatedBy
	}
	chainPOs, err := dao.AppletChainDAOImpl.Query(ctx, &chainQueryParam)
	if err != nil {
		stdlog.Errorf("fuzzy query chain err :%v", err)
		return nil, err
	}
	return chainPOs, nil
}

func (c ChainImpl) SearchForBaseDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainBaseDO, error) {
	chainPOs, err := searchSimplePOWithParams(ctx, searchParam)
	if err != nil {
		stdlog.Errorf("fuzzy query chain err :%v", err)
		return nil, err
	}
	chainBaseDOs, err := dao.BatchConvertChainPO2BaseDO(chainPOs)
	if err != nil {
		stdlog.Errorf("convert chan err :%v", err)
		return nil, err
	}
	res := make([]*models.AppletChainBaseDO, 0)
	for _, c := range chainBaseDOs {
		if ptr.ToBool(searchParam.FilterDebugFailed) && c.LastDebugState.Code != models.ChainDebugStateSuccess.Code {
			continue
		}
		res = append(res, c)
	}
	return res, nil
}

func (c ChainImpl) SearchForSimpleChainDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainDO, error) {
	chainQueryParam := generated.AppletChain{}
	if searchParam.UserID != "" {
		chainQueryParam.Creator = searchParam.UserID
	}
	if searchParam.ProjectID != "" {
		chainQueryParam.ProjectID = searchParam.ProjectID
	}
	if searchParam.AssetType != nil {
		chainQueryParam.AssetType = *searchParam.AssetType
	}
	if searchParam.CreatedBy != "" {
		chainQueryParam.CreatedType = searchParam.CreatedBy
	}
	chainPOs, err := dao.AppletChainDAOImpl.SimpleInfoQuery(ctx, &chainQueryParam, nil)
	if err != nil {
		stdlog.Errorf("fuzzy query chain err :%v", err)
		return nil, err
	}
	chainDOs, err := dao.BatchConvertChainPO2ChainDO(chainPOs)
	if err != nil {
		stdlog.Errorf("convert chan err :%v", err)
		return nil, err
	}
	return chainDOs, nil
}

func (c ChainImpl) UpdateByGenerated(ctx context.Context, chainID string, model *generated.AppletChain) error {
	if model.ID != "" {
		return stderr.Error("the id of model must be empty str for update")
	}
	if _, err := dao.AppletChainDAOImpl.UpdateChain(ctx, chainID, model); err != nil {
		return err
	}
	return nil
}
func (c ChainImpl) SetSelectedApps(ctx context.Context, req *pb.SetSelectedReq,
	actionType SetSelectedAppsActionType) error {
	for _, id := range req.Ids {
		model, err := c.GetChainByID(ctx, id)
		if err != nil {
			return err
		}
		switch actionType {
		case ActionTypeAdd:
			model.Base.SpaceInfo.IsSelected[req.Industry] = true
		case ActionTypeRemove:
			delete(model.Base.SpaceInfo.IsSelected, req.Industry)
		}
		updateModel := &models.AppletChainDO{
			Base: models.AppletChainBaseDO{
				SpaceInfo: model.Base.SpaceInfo,
			},
		}
		err = c.UpdateAppletChain(ctx, id, updateModel)
		if err != nil {
			return err
		}
	}
	return nil
}
func (c ChainImpl) SearchForChainDO(ctx context.Context, searchParam *SearchChainParam) ([]*models.AppletChainDO, error) {
	chainPOs, err := searchCompletePOWithParams(ctx, searchParam)
	if err != nil {
		stdlog.Errorf("fuzzy query chain err :%v", err)
		return nil, err
	}
	chainDOs, err := dao.BatchConvertChainPO2ChainDO(chainPOs)
	if err != nil {
		stdlog.Errorf("convert chan err :%v", err)
		return nil, err
	}
	return chainDOs, nil
}

func (c ChainImpl) GetProjectIds() ([]string, error) {
	return dao.AppletChainDAOImpl.GetProjIds(context.Background())
}

func (c ChainImpl) CreateChainBasic(ctx context.Context, chain *models.AppletChainDO) (string, error) {
	if chain == nil {
		stdlog.Errorf("chain is nil")
		return "", errors.Errorf("chain is nil")
	}
	if err := chain.Base.IsValid(); err != nil {
		stdlog.Errorf("validate chain err :%v", err)
		return "", err
	}
	chainPO, err := chain.ToPO()
	if err != nil {
		stdlog.Errorf("chan convert to po err :%v", err)
		return "", err
	}
	if err := ValidChainNameForCreate(ctx, chain.Base.Name, helper.GetProjectID(ctx)); err != nil {
		stdlog.Errorf("ValidChainName err :%v", err)
		return "", err
	}
	err = chain.SubmitPermission(ctx)
	if err != nil {
		return "", stderr.Wrap(err, "提交权限配置")
	}
	var id string
	if err := query.Q.Transaction(func(tx *query.Query) error {
		// 保存标签
		labelPO := models.ConvertLabelDO2PO(chain.Base.Labels, chain.Base.Creator)
		if err := dao.NewAppletLabelDAO(tx).BatchUpsertLabel(ctx, labelPO); err != nil {
			return err
		}
		// 保存应用链
		id, err = dao.NewAppletChainDAO(tx).SaveBasicInfo(ctx, chainPO)
		if err != nil {
			stdlog.Errorf("save chain err :%v", err)
			return err
		}
		return nil
	}); err != nil {
		return "", errors.Wrap(err, "save chain basic err")
	}

	return id, nil
}
func (c ChainImpl) GenerateTickScriptV2(ctx context.Context, chainID string, APIPath *string) (string, error) {
	// 更新算子模板
	// if err := WidgetManager.RegisterDynamicWidget(ctx); err != nil {
	//	return "", err
	// }
	chainDO, err := c.GetChainByID(ctx, chainID)
	if err != nil {
		stdlog.Errorf("convert chain :%v err :%v", chainID, err)
		return "", err
	}
	return GenerateTickWithChain(ctx, chainDO, APIPath)
}

func GenerateTickWithChain(ctx context.Context, chain *models.AppletChainDO, APIPath *string) (string, error) {
	chainDO, err := helper.DeepCopy(chain)
	if err != nil {
		return "", err
	}

	chainID := chainDO.Base.ID
	if chainDO.ChainDetail == nil || chainDO.ChainDetail.IsEmpty() {
		return "", helper.AppletChainBuildScriptErr.Errorf("chain detail is nil")
	}
	// 展开子链
	if chainDO.ChainDetail.ContainsSubChain() {
		chainDO, err = chainDO.ExpandSubChain()
		if err != nil {
			stdlog.Errorf("gen tick script ,expand sub chain :%v err :%v", chainID, err)
			return "", stderr.Wrap(err, "gen tick script ,expand sub chain :%v err :%v", chainID, err)
		}
	}
	// 去除goto算子到链的连接
	if chainDO.ContainsGotoNode() {
		chainDO, err = chainDO.RemoveGotoWidgetEdge()
		if err != nil {
			stdlog.Errorf("chain :%v .gen tick script ,remove goto widget err :%v", chainID, err)
			return "", stderr.Wrap(err, "chain :%v .gen tick script ,remove goto widget err :%v", chainID, err)
		}
	}

	gen, err := NewScriptGenerator(ctx, chainDO, APIPath)
	if err != nil {
		stdlog.Errorf("gen tick script v2 err :%v", err)
		return "", err
	}
	return gen.GenScript()
}

func (c ChainImpl) GetAppDependencyInfos(ctx context.Context) (map[string]*models.DependencyInfo, error) {
	return appDependencyInfos.GetAll()
}

func (c ChainImpl) GetAppDependencyInfo(ctx context.Context, chainID string) (*models.DependencyInfo, error) {
	dp, err := appDependencyInfos.Get(chainID)
	if err != nil {
		return dp, nil
	}
	chain, err := c.GetChainByID(ctx, chainID)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do GetChainByID")
	}
	dp, err = chain.GetDependencyInfo()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do GetDependencyInfo")
	}
	appDependencyInfos.Set(chainID, dp)
	return dp, nil
}

var (
	appDependencyInfos *clients2.RedisMap[models.DependencyInfo] //  chainId -> *dependencyInfo
)

func StartAppDependencyCheck() {
	appDependencyInfos = clients2.NewRedisMap[models.DependencyInfo](clients.RedisCli, RedisKeyAppDependencyCheck)
	go func() {
		doAppDependencyCheck()
		ticker := time.NewTicker(conf.Config.AppSvcConfig.DependencyInterval)
		for range ticker.C {
			doAppDependencyCheck()
		}
	}()
}

func doAppDependencyCheck() {
	ctx := context.Background()
	chainDOs, err := ChainManager.SearchForChainDO(ctx, new(SearchChainParam))
	if err != nil {
		stdlog.WithError(err).Error("failed to SearchForChainDO")
		return
	}
	for _, chain := range chainDOs {
		dpInfo, err := chain.GetDependencyInfo()
		if err != nil {
			stdlog.WithError(err).Errorf("failed to get dependencyInfo of chain[id=%s]", chain.Base.ID)
			continue
		}
		appDependencyInfos.Set(chain.Base.ID, dpInfo)
	}
	stdlog.Infof("update appDependencyInfos successfully")
}
