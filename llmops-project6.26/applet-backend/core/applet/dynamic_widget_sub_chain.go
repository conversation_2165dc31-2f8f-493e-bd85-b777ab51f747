package applet

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type WidgetSubChainSvc struct {
}

func (p WidgetSubChainSvc) ListDynamicWidgets(ctx context.Context) ([]*models.DynamicWidgetDesc, error) {
	res := make([]*models.DynamicWidgetDesc, 0)
	chains, err := dao.AppletChainDAOImpl.Query(ctx, &generated.AppletChain{
		ProjectID: helper.GetProjectID(ctx),
	})
	if err != nil {
		return nil, err
	}
	chainStates, err := dao.ChainDebugHistoryImpl.ListLastDebugState(ctx)
	if err != nil {
		return nil, err
	}
	chainDOs, err := dao.BatchConvertChainPO2DO(chains, chainStates)
	if err != nil {
		return nil, err
	}
	for _, c := range chainDOs {
		if c.ChainDetail == nil {
			continue
		}
		//if c.ContainsSubChain() {
		//	continue
		//}
		// 有脏数据
		if !c.AllNodeHasDetail() {
			continue
		}
		res = append(res, c.ToWidgetDesc())
	}
	return res, nil
}

func (p WidgetSubChainSvc) GetWidget(ctx context.Context, widgetKey string) (*widgets.Widget, error) {
	// if err := WidgetManager.RegisterDynamicWidget(ctx); err != nil {
	//	return nil, err
	// }
	chain, err := ChainManager.GetChainByID(ctx, widgetKey)
	if err != nil {
		return nil, err
	}
	return chain.ToSubChainWidget()
}
