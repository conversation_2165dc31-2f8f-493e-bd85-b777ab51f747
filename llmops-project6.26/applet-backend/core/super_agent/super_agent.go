package super_agent

import (
	"context"
	"encoding/json"
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/core/global_llm"
	pkg_clients "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type SuperAgent struct {
}

type AgentQueryParam struct {
	Prompt string
	Query  string
}

func (s *SuperAgent) GetModelService(ctx context.Context) (*pb.ModelService, error) {
	ProjectID := helper.GetProjectID(ctx)
	if ProjectID == "" {
		stdlog.Errorf("imageGenParam field name can not be empty")
		err := fmt.Errorf("empty Project ID")
		return nil, err
	}
	var LLMBasicConfigDAO global_llm.LLModel
	model, err := LLMBasicConfigDAO.GetModelByProjectID(ctx, ProjectID)
	if err != nil {
		stdlog.Errorf("failed to get model by ID: %v", err)
		return nil, err
	}
	if model.ModelsForAgent.LLMModelSvc == nil {
		return nil, stderr.Internal.Errorf("no default llm model service")
	}
	url := model.ModelsForAgent.LLMModelSvc.FullUrl
	//url := "https://172.17.120.207:30745/llm/dev/gateway/isgw/remote/dev/MWH-REMOTE-SERVICE-d1obl342kb2a81oe5rug/seldon/llmops-assets/service-e34b3d92-ddc8-42aa-a0f3-4ce960b33304/8000/v1/chat/completions"
	return &pb.ModelService{
		FullUrl: url,
	}, nil
}

func (s *SuperAgent) GenerateChain(ctx context.Context, userQuery string, histories []models.ChatHistory) (*widgets.Chain, error) {
	prompt, err := GetAIGenChainPrompt(histories)
	if err != nil {
		return nil, err
	}
	aiRsp, err := s.SyncCallLLM(ctx, AgentQueryParam{
		Prompt: prompt,
		Query:  userQuery,
	})
	if err != nil {
		return nil, err
	}
	aiChain := &models.AIChainDetail{}
	if err := json.Unmarshal([]byte(aiRsp), aiChain); err != nil {
		stdlog.Errorf("invalid json :%v", aiRsp)
		return nil, err
	}
	chain, err := AIChainToAppletChain(aiChain)
	if err != nil {
		return nil, err
	}
	return chain, nil

}

func (s *SuperAgent) SyncCallLLM(ctx context.Context, param AgentQueryParam) (string, error) {

	req := &triton.OpenAiChatReq{
		Messages: []triton.MultimodalMessageItem{
			{
				Role:    triton.OpenaiSystemMsg,
				Content: param.Prompt,
			},
			{
				Role:    triton.OpenaiUserMsg,
				Content: param.Query,
			},
		},
		Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
	}

	var resp string
	resHandler := func(textContent string) error {
		resp = textContent
		return nil
	}
	modelService, err := s.GetModelService(ctx)
	if err != nil {
		return "", err
	}
	err = pkg_clients.SyncChatForTaskMode(ctx, modelService, req, resHandler)
	if err != nil {
		return "", stderr.Wrap(err, "failed to do sync chat")
	}

	return resp, nil
}
