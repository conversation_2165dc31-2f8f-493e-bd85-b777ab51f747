package super_agent

import (
	"bytes"
	"context"
	"text/template"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const aiRecommendChainTemplatePrompt = `
你是一个应用链匹配专家，负责根据用户的需求匹配一到三条合适的应用链，你有以下应用链可以使用：
==================
{{.ChainTemplate}}
===================
模板参数说明：
id: 应用链唯一定义
name: 应用链名字
desc: 应用链的描述
===================
输出格式：
[
  {
  "id":"${应用链id}",
  "name":"${应用链name}"
  }
]

===================
请注意：
** 你只需要输出最终生成的应用链模板列表，不需要有其他额外输出 **
** 至少推荐一个模板，推荐的模板数量需要少于三个**
** 如果用户的问题不是完成一个任务或者生成应用链，则回答：我只负责应用链的匹配 **
===================
示例:
user query: 请帮我搭建一个应用链，实现接收用户输入并用大模型回答用户输入的问题
your output:
[
  {
    "id":"模型对话.json",
    "name":"模型对话"
  }
  
]
以上是你的工作职责

帮我实现知识库问答应用链
`

type aiRecommendChainParam struct {
	ChainTemplate string
}

func getChainTemplateDesc(ctx context.Context) (string, error) {
	templates := make([]models.AIChainTemplateDesc, 0)
	tmps, err := applet.ChainTemplateManger.ListAllTemplates(ctx, false)
	if err != nil {
		return "", err
	}
	for _, t := range tmps {
		templates = append(templates, t.GetAITemplateDesc())
	}
	descStr, err := models.GetAITemplateDesc(templates)
	if err != nil {
		return "", err
	}
	return descStr, nil
}

func GetAIRecommendChainPrompt(ctx context.Context) (string, error) {
	template, err := template.New("Prompt").Parse(aiRecommendChainTemplatePrompt)
	if err != nil {
		return "", err
	}
	tmp, err := getChainTemplateDesc(ctx)
	if err != nil {
		return "", err
	}
	p := aiRecommendChainParam{
		ChainTemplate: tmp,
	}
	var buf bytes.Buffer
	if err := template.Execute(&buf, p); err != nil {
		return "", err
	}
	return buf.String(), nil
}
