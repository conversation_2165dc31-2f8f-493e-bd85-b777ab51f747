package super_agent

import (
	"fmt"
	"strconv"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func AIChainToAppletChain(chain *models.AIChainDetail) (*widgets.Chain, error) {
	nodes := make([]widgets.ChainNode, 0)
	edges := make([]widgets.ChainEdge, 0)

	for _, n := range chain.Nodes {
		w, err := widgets.WidgetFactoryImpl.GetWidget(n.WidgetID)
		if err != nil {
			return nil, err
		}
		nodes = append(nodes, widgets.ChainNode{
			Id:           strconv.Itoa(int(n.ID)),
			Name:         w.Define().Name,
			WidgetId:     n.WidgetID,
			WidgetDetail: w.Define(),
			Values:       n.Params,
		})
	}
	for _, e := range chain.Edges {
		edges = append(edges, widgets.ChainEdge{
			Id: fmt.Sprintf("reactflow__edge-%v%v%v-%v%v%v",
				e.SourceNodeID, e.SourceNodeID, e.SourceParam, e.TargetNodeID, e.TargetNodeID, e.TargetParam),
			SourceNode:  strconv.Itoa(int(e.SourceNodeID)),
			SourceParam: fmt.Sprintf("%v@@%v", e.SourceNodeID, e.SourceParam),
			TargetNode:  strconv.Itoa(int(e.TargetNodeID)),
			TargetParam: fmt.Sprintf("%v@@%v", e.TargetNodeID, e.TargetParam),
		})
	}
	res := &widgets.Chain{
		Nodes: nodes,
		Edges: edges,
	}
	return res, nil
}
