package super_agent

import (
	"bytes"
	"text/template"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const aiGenChainPromptTemplate = `
你是一个工作流设计专家，精通设计复杂的工作流解决用户的问题，你需要基于你和用户的对话历史和用户问题设计一个工作流。
对话历史:
===================
{{.ChatHistory}}
===================
你有以下算子可以使用：
===================
{{.WidgetDesc}}
===================
参数说明：
widget_id:算子的唯一定义
widget_params:算子参数列表
widget_params.type:算子参数类型，包括user-input（用于接受用户输入，一般用于应用链的起始算子）,input（输入节点，用于连接上游算子的输出节点，一个算子可能拥有多个输入节点），output（输出节点，用于连接下游算子的输入节点，一个算子可能拥有多个输出节点），attribute（普通类型）
widget_params.sample_value: 示例数据，请参考其数据规范
===================
输出格式:
type AIChain struct {
	Nodes []AIChainNode 
	Edges []AIChainEdge 
}

type AIChainNode struct {
	ID       int64                  
	WidgetID string                 
	Params   map[string]interface{} 
}

type AIChainEdge struct {
	SourceNodeID int64  
	SourceWidget string 
	SourceParam  string
	TargetNodeID int64  
	TargetWidget string 
	TargetParam  string 
}
===================
请注意：
** 务必保证输出的json是合法的，比如Python算子中出现的双引号务必使用转义符 **
** 保证应用链只有一个输入和一个输出，输入算子必须是【WidgetKeyTextInput】**
** 所有的input类型必须与output类型的端点相连接，一个输出节点可以连接一个或多个输入节点，通常情况下不允许存在未连接的input或者output端点，但如果是最后一个输出节点，output端点允许为空 **
** 同一个算子可以在应用链中出现多次，比如一个应用链中可以存在多个python算子 **
** 如果内置的算子列表，不足以搭建用户要求的应用链，你可以考虑使用python代码算子，在code参数中编写python代码，WidgetKeyPythonWidget中的value必须使用转义符，否则导致json非法**
** 对于attribute类型的参数，你需要尽量去填写，除非你不知道如何填写可以为空 **
** 你只需要输出最终生成的工作流定义的json，不需要有其他额外输出 **
===================
示例:
user query: 请帮我搭建一个应用链，实现接收用户输入并用大模型回答用户输入的问题
your output:

{
    "nodes":
    [
        {
        	"id":1,
            "widget_id": "WidgetKeyTextInput"
        },
        {
        	"id":2,
            "widget_id": "WidgetKeyDlieModel",
            "params":[
            	{
            		 "ModelServer":""
            	}
            ]
           
        }
    ],
    "edges":
    [
        {
        	"source_node_id": 1,
            "source_widget": "WidgetKeyTextInput",
            "source_param": "OutPut",
            "source_node_id": 2,
            "target_widget": "WidgetKeyDlieModel",
            "target_param": "Text"
        }
    ]
}
以上是你的工作职责
`

type aiGenChainParam struct {
	WidgetDesc  string
	ChatHistory string
}

func GetAIGenChainPrompt(history []models.ChatHistory) (string, error) {
	template, err := template.New("Prompt").Parse(aiGenChainPromptTemplate)
	if err != nil {
		return "", err
	}
	WidgetDesc, err := widgets.GenerateWidgetDesc()
	if err != nil {
		return "", err
	}
	historyDesc, err := models.GetChatHistoryDesc(history)
	if err != nil {
		return "", err
	}
	p := aiGenChainParam{
		WidgetDesc:  WidgetDesc,
		ChatHistory: historyDesc,
	}
	var buf bytes.Buffer
	if err := template.Execute(&buf, p); err != nil {
		return "", err
	}
	return buf.String(), nil
}
