package global_llm

import (
	"context"
	"strings"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"

	"github.com/google/uuid"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

type AgentCreateHandler struct {
	llmModel       *pb.ModelService
	embeddingModel *pb.ModelService
	imageGenModel  *pb.ModelService
}

type AutoCreatedAgent struct {
	ID             string                           `json:"id" description:"上传文件时产生的知识库id,没有上传文件时请生成uuid"`
	ExperimentInfo *models.ExperimentInfo           `json:"experiment_info" description:"体验时的基本信息"`
	AgentConfig    *agent_definition.LLMAgentConfig `json:"agent_config" description:"智能体相关配置"`
	mutex          sync.Mutex
}

func GetCreateHandler(ctx context.Context, llmModel *pb.ModelService,
	embeddingModel *pb.ModelService, imageGenModel *pb.ModelService) (*AgentCreateHandler, error) {
	isReady, err := clients.CheckModelConn(llmModel)
	if !isReady {
		return nil, stderr.Internal.Cause(err, "failed to access model by tcp")
	}
	return &AgentCreateHandler{
		llmModel:       llmModel,
		embeddingModel: embeddingModel,
		imageGenModel:  imageGenModel,
	}, nil
}
func (a *AgentCreateHandler) CreateAgent(ctx context.Context, Purpose string) (*models.AppletAssistant, error) {
	var agent AutoCreatedAgent
	agent.ID = uuid.New().String()
	agent.ExperimentInfo = &models.ExperimentInfo{}
	agent.AgentConfig = &agent_definition.LLMAgentConfig{}
	err := a.doCreateAgent(ctx, &agent, Purpose)
	if err != nil {
		return nil, stderr.Wrap(err, "create experiment info failed")
	}
	return &models.AppletAssistant{ID: agent.ID, ExperimentInfo: agent.ExperimentInfo, AssistantInfo: models.AssistantInfo{AgentConfig: agent.AgentConfig}}, nil
}

// createAgentConfig 创建智能体的Prompt
func (a *AgentCreateHandler) createAgentConfig(ctx context.Context, agent *AutoCreatedAgent, purpose string) error {
	promptOptimize := clients.PromptOptimizer{
		Model: a.llmModel,
	}
	optimizedPrompt, err := promptOptimize.PromptOptimize(ctx, "generate", purpose)
	if err != nil {
		stdlog.Errorf("prompt optimize failed: %v", err)
		return err
	}

	agent.AgentConfig.LLMModelSvc = a.llmModel
	agent.AgentConfig.Prompt = optimizedPrompt
	return nil
}

// doCreateAgent 创建智能体的开场白、示例问题等信息
func (a *AgentCreateHandler) doCreateAgent(ctx context.Context, agent *AutoCreatedAgent, purpose string) error {
	tasks := a.createTasks(ctx, purpose, agent, a.llmModel)
	start := time.Now()
	err := helper.SyncRunTasks(tasks)
	if err != nil {
		return err
	}
	elapsed := time.Since(start)
	stdlog.Infof("cost %v to do text gen task", elapsed)

	// image信息非必填,失败时设置为空串
	start = time.Now()
	imageUrl, err := ImageGenerate(ctx, a.imageGenModel, agent.ExperimentInfo.ExamplesInfo.ImageUrl)
	elapsed = time.Since(start)
	stdlog.Infof("cost %v to do image gen task", elapsed)
	if err == nil {
		agent.ExperimentInfo.ExamplesInfo.ImageUrl = imageUrl
	} else {
		stdlog.Errorf("image generating failed: %v", err)
		agent.ExperimentInfo.ExamplesInfo.ImageUrl = ""
	}
	return nil
}

func ImageGenerate(ctx context.Context, model *pb.ModelService, prompt string) (string, error) {
	if model == nil {
		return "", stderr.Error("the model is nil")
	}
	absFilePath, err := clients.SimpleImageGen(ctx, model, prompt, "256x256")
	if err != nil {
		return "", err
	}
	rfp, err := stdfs.NewRelativeFilePath(absFilePath)
	if err != nil {
		return "", err
	}
	return rfp.ToSFSFilePath(), nil
}

func (a *AgentCreateHandler) createTasks(ctx context.Context, instruction string, agent *AutoCreatedAgent, llmModel *pb.ModelService) []func() error {
	var task func() error
	tasks := make([]func() error, 0)
	// Name
	task = func() error {
		handler := func(textContext string) error {
			agent.ExperimentInfo.Name = strings.TrimSpace(textContext)
			return nil
		}
		req := &triton.OpenAiChatReq{
			Stream: false,
			Messages: []triton.MultimodalMessageItem{
				{
					Role:    "system",
					Content: helper.GetLangPrompt(ctx),
				},
				{
					Role:    "user",
					Content: GetNameCreatingPrompt(ctx, instruction),
				},
			},
			Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
		}
		return clients.SyncChatForTaskMode(ctx, llmModel, req, handler)
	}
	tasks = append(tasks, task)

	// Introduction
	task = func() error {
		handler := func(textContext string) error {
			textContext = strings.TrimSpace(textContext)
			agent.ExperimentInfo.ExamplesInfo.Introduction = textContext
			return nil
		}
		req := &triton.OpenAiChatReq{
			Stream: false,
			Messages: []triton.MultimodalMessageItem{
				{
					Role:    "system",
					Content: helper.GetLangPrompt(ctx),
				},
				{
					Role:    "user",
					Content: GetOpeningStatementPrompt(ctx, instruction),
				},
			},
			Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
		}
		return clients.SyncChatForTaskMode(ctx, llmModel, req, handler)
	}
	tasks = append(tasks, task)

	// Examples
	task = func() error {
		generatedQuestions := make([]string, 0, 3)
		handler := func(textContext string) error {
			textContext = strings.TrimSpace(textContext)
			agent.mutex.Lock()
			defer agent.mutex.Unlock()
			generatedQuestions = append(generatedQuestions, textContext)
			agent.ExperimentInfo.ExamplesInfo.MultimodalExamples = append(agent.ExperimentInfo.ExamplesInfo.MultimodalExamples,
				&models.MultimodalExample{Text: textContext})
			return nil
		}
		for i := 0; i < 3; i++ {
			prompt := GetBasicQuestionsPrompt(ctx, instruction, generatedQuestions)
			req := &triton.OpenAiChatReq{
				Stream: false,
				Messages: []triton.MultimodalMessageItem{
					{
						Role:    "system",
						Content: helper.GetLangPrompt(ctx),
					},
					{
						Role:    "user",
						Content: prompt,
					},
				},
				Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
			}
			err := clients.SyncChatForTaskMode(ctx, llmModel, req, handler)
			if err != nil {
				return err
			}
		}
		return nil
	}
	tasks = append(tasks, task)

	// ImagePrompt
	task = func() error {
		handler := func(textContext string) error {
			agent.ExperimentInfo.ExamplesInfo.ImageUrl = textContext
			return nil
		}
		req := &triton.LLMChatReq{
			Query:  instruction,
			Prompt: ImageGeneratePromptTemplate,
		}

		openaiChatReq := clients.Cvt2OpenaiChatReq(req)
		return clients.SyncChat(ctx, llmModel, openaiChatReq, handler)
	}
	tasks = append(tasks, task)

	// Prompt
	task = func() error {
		return a.createAgentConfig(ctx, agent, instruction)
	}
	tasks = append(tasks, task)
	return tasks
}

func CreateOneMoreExample(ctx context.Context, llmModel *pb.ModelService, instruction string) (string, error) {
	var example string
	// Examples
	task := func() error {
		handler := func(textContext string) error {
			example = textContext
			return nil
		}
		prompt := GetBasicQuestionsPrompt(ctx, instruction, []string{})
		req := &triton.LLMChatReq{
			Query: prompt,
		}
		openaiChatReq := clients.Cvt2OpenaiChatReq(req)
		return clients.SyncChatForTaskMode(ctx, llmModel, openaiChatReq, handler)
	}
	tasks := make([]func() error, 0)
	tasks = append(tasks, task)
	err := helper.SyncRunTasks(tasks)
	return example, err
}
