package global_llm

import (
	"context"
	"fmt"
	"strings"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const NameCreatingPromptTemplateZh = `
# 任务
你需要根据任务描述，给出合适的智能助手的名字

# 示例
## 示例1
任务描述：请创建一个可以自动回答用户健康相关问题的智能助手
名字：健康助手
## 示例2
任务描述：请创建一个用于在线购物助手的智能助手
名字：购物导航
## 示例3
任务描述：请创建一个用于辅助老年人进行日常活动的智能助手
名字：老年伴侣

# 回复要求
你只需要输出名字，不要输出其他内容

# 注意
智能助手的名字 仅支持中文、英文、阿拉伯语、数字、短划线、下划线和小数点，必须以中文、英文、阿拉伯语或数字开头和结尾，不能有blank space，如果需要建议用 Underscore 代替

任务描述：%s
名字：
`

const OpeningStatementPromptTemplateZh = `
# 任务
你需要根据任务描述，生成智能助手的简单开场白，务必确保开场白不超过30个字，且不超过2句话。

# 示例
## 示例1
任务描述：请创建一个专注于提供旅游建议的智能助手
开场白：你好，我是你的旅行助手。一起探索新的目的地吧！
## 示例2
任务描述：请创建一个帮助用户学习编程的智能助手
开场白：你好，我对编程有着浓厚的兴趣，让我们一起编码，一起解决编程难题吧！
## 示例3
任务描述：请创建一个专门为孩子讲故事的智能助手
开场白：你好，我是你的故事助手。我来讲个故事，让我们一起进入童话世界。

# 回复要求
你只需要输出开场白，不要输出其他内容

任务描述：%s
开场白：
`

const BasicQuestionsPromptTemplateZh = `
# 任务
你需要根据任务描述，生成用户向智能助手提问的示例问题。

# 示例
创建智能助手的任务描述：创建一个智能助手，它可以根据用户提供的信息回答当地的美食，并详细的描述其具体的做法，它能够使用生动活泼的语言回答用户问题
问题：哪里能找到正宗的当地美食？

# 已经生成的示例问题
%s

# 回复要求
1. 你只能输出一个开场示例问题，直接输出问题，不要输出其他内容
2. 一个问题只能包含一个问句，不要包含多个问句
3. 如果存在已经生成的示例问题，请不要输出和已经生成的示例问题相同或相似的问题
4. 你生成的问题需模拟用户向智能助手提问，你要扮演用户向智能助手提问

创建智能助手的任务描述：%s
问题：
`
const ImageGeneratePromptTemplate = `
请根据用户的需求完成下面的任务

任务描述： 用户输入他们的具体任务目标。
角色识别： 根据用户的目标，确定一个相关且合适的角色，该角色负责实现这个目标。
生成Stable Diffusion Prompt： 针对识别出的角色，创建一个详细且上下文相关的Stable Diffusion prompt。这个prompt应当捕捉角色及其职责的本质，确保与任务目标一致。
注意：请只返回英文的Stable Diffusion Prompt，不需要其他信息。
示例：
-   用户的任务目标： 开发一个儿童教育应用程序。
	识别出的角色： 教育技术开发人员。
	Stable Diffusion Prompt： "A highly skilled educational technology developer is creating an innovative and engaging educational app for children. The app features interactive lessons, colorful graphics, and fun activities that make learning enjoyable. The developer works in a modern office filled with the latest tech gadgets and a collaborative team, brainstorming and testing new ideas to enhance the app's educational value."
-   用户的任务目标： 提高公司内部的环保意识。
	识别出的角色： 环保项目经理。
	Stable Diffusion Prompt： "An enthusiastic environmental project manager is leading a company-wide initiative to raise awareness about sustainability. They organize workshops, create engaging content, and implement green policies. The project manager is seen presenting to a diverse group of employees in a modern conference room, filled with posters and materials promoting environmental conservation."
-  用户的任务目标： 开展一项关于火星探索的研究。
	识别出的角色： 行星科学家。
	Stable Diffusion Prompt： "A dedicated planetary scientist is conducting groundbreaking research on Mars exploration. Surrounded by high-tech laboratory equipment and detailed maps of Mars, the scientist analyzes data and collaborates with a team of experts. The lab is filled with samples, charts, and a large screen displaying the latest findings from Mars missions."
-  用户的任务目标： 创建一个支持远程工作的系统。
	识别出的角色： IT系统管理员。
	Stable Diffusion Prompt： "An experienced IT systems administrator is developing a robust remote work system for the company. They set up secure networks, troubleshoot technical issues, and ensure seamless communication between remote employees. The administrator works in a control room filled with monitors, showing various network metrics and remote connections."
`

func GetNameCreatingPrompt(ctx context.Context, instruction string) string {
	if helper.IsChinese(ctx) {
		return fmt.Sprintf(NameCreatingPromptTemplateZh, instruction)
	} else {
		return fmt.Sprintf(NameCreatingPromptTemplateEn, instruction)
	}
}

func GetOpeningStatementPrompt(ctx context.Context, instruction string) string {
	if helper.IsChinese(ctx) {
		return fmt.Sprintf(OpeningStatementPromptTemplateZh, instruction)
	} else {
		return fmt.Sprintf(OpeningStatementPromptTemplateEn, instruction)
	}
}

func GetBasicQuestionsPrompt(ctx context.Context, instruction string, generatedQuestions []string) string {
	if helper.IsChinese(ctx) {
		return fmt.Sprintf(BasicQuestionsPromptTemplateZh, strings.Join(generatedQuestions, "\n"), instruction)
	} else {
		return fmt.Sprintf(BasicQuestionsPromptTemplateEn, strings.Join(generatedQuestions, "\n"), instruction)
	}
}

const NameCreatingPromptTemplateEn = `
# Task
You need to come up with an appropriate name for the AI assistant based on the task description.

# Examples
## Example 1
Task description: Please create an AI assistant that can automatically answer user health-related questions  
Name: Health Assistant
## Example 2
Task description: Please create an AI assistant for online shopping  
Name: Shopping Navigator
## Example 3
Task description: Please create an AI assistant to help seniors with daily activities  
Name: Senior Companion

# Response Requirements
You only need to output the name; do not output anything else.

# Note
The name may only contain Chinese characters, English letters, Arabic script, digits, hyphens, underscores, and dots; it must begin and end with a Chinese character, English letter, Arabic character, or digit, and cannot contain spaces. If needed, use underscores instead.

Task description: %s  
Name:
`

const OpeningStatementPromptTemplateEn = `
# Task
You need to generate a brief opening statement for the AI assistant based on the task description. Ensure the opening is no more than 30 characters and no more than two sentences.

# Examples
## Example 1
Task description: Please create an AI assistant focused on providing travel advice  
Opening statement: Hello, I’m your travel assistant. Let’s explore new destinations together!
## Example 2
Task description: Please create an AI assistant to help users learn programming  
Opening statement: Hi there! I love programming—let’s code and solve coding challenges together!
## Example 3
Task description: Please create an AI assistant specialized in telling stories to children  
Opening statement: Hello, I’m your story assistant. Let me tell you a tale and take you into a fairy‑tale world.

# Response Requirements
You only need to output the opening statement; do not output anything else.

Task description: %s  
Opening statement:
`

const BasicQuestionsPromptTemplateEn = `
# Task
You need to generate a sample user question that a user might ask the AI assistant based on the task description.

# Example
Task description: Create an AI assistant that can recommend local cuisine based on user‑provided information and describe the cooking steps in detail, using lively and engaging language  
Question: Where can I find authentic local dishes?

# Already Generated Sample Questions
%s

# Response Requirements
1. You may only output one question; output the question directly without any extra content.  
2. The question must contain only one interrogative sentence; do not include multiple questions.  
3. If there are already generated questions, do not output a question identical or similar to them.  
4. Your generated question should simulate a user asking the AI assistant; you are acting as the user.

Task description: %s  
Question:
`
