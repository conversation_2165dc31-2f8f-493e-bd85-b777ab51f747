package core

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/core/api_tool"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/core/guardrails"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/core/model_history"
	"transwarp.io/applied-ai/applet-backend/core/super_agent"
)

func Init(ctx context.Context) error {
	err := applet.Init()
	if err != nil {
		return err
	}
	applet.InitTestWidgets()
	knowledge_base.Init()
	knowledge_base.StartHealthCheck()
	err = api_tool.InitTools(ctx)
	if err != nil {
		return err
	}
	guardrails.Init()
	model_history.Init()
	if err := applet.InitAppletService(); err != nil {
		return err
	}
	if err := applet.InitTriggerManger(); err != nil {
		return err
	}
	applet.StartAppHealthCheck()
	applet.StartAppDependencyCheck()
	applet.StartOrphanRecordCleaner()
	super_agent.Init()
	return nil

}
