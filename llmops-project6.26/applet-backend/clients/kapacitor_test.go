package clients

import (
	"testing"
)

func TestKapacitor(t *testing.T) {
	//ctx := context.Background()
	//_ = ctx
	//viper.AddConfigPath("../etc/")
	//viper.SetConfigName("app")
	//viper.SetConfigType("yaml")
	//if err := viper.ReadInConfig(); err != nil {
	//	t.Fatal(err)
	//}
	//if err := viper.Unmarshal(&conf.Config, conf.DecConfig); err != nil {
	//	t.Fatal(err)
	//}
	//url := conf.Config.Kapacitor.Addr
	//if !strings.HasPrefix(url, "http://") {
	//	url = "http://" + url
	//}
	//cli, err := kpct.New(kpct.Config{URL: url})
	//if err != nil {
	//	t.Fatal(err)
	//}
	//KapacitorCli = &Kpct{KapacitorCli: cli}
	//script := "// 接受HTTP请求\nvar v1 = stream\n    |listenHttp()\n        .port(1885)\n        .path('/api/v1')\n        .handler('defaulttt')\n        .timeout(3s)\n\n// 提取用户输入信息 'text'\nvar v2 = v1\n    |textInput()\n        .nodeID('123')\n        // 必填， 用于多个输入算子的情况下，标识数据来源，过滤不感兴趣字段\n        .inputKey('TextInput')\n    // 可选， 用于指定提取 listenHttp 请求体中特定部分的数据\n    |log()\n\n// 对用户输入进行 embeding\nvar v3 = v2\n    |dlieInfer()\n        .addr('**************')\n        .port(31406) \n        .timeout(2s) \n        .isStream(TRUE)\n        .modelName('atom') \n        .modelType('text-vec')\n\n// 召回上下文\nvar v4 = v3\n    |vecSearch()\n        .database('test_db')\t// 向量数据库DB, 默认值：default\n        .url('')\t\t\t\t// 向量数据库地址 ['mem', 'milvus://*************:19530', 'hippo://*************:19530', 'file:///tmp/temp_vec.db'], 默认值： file:///tmp/temp_vec.db\n        .topK(10) \t\t\t\t// 向量召回最大数量 [1, 100], 默认值: 5\n        .threshold(0.5)\t\t\t// 向量相似度阈值，仅返回大于该值的向量。 默认值 0.5\n\nvar v5 = v2\n    |join(v4)\n        .as('用户输入', '上下文')\n    |promptTmpl('''\n    {{.上下文}}\n    根据以上背景信息，回答下面的问题：\n    {{.用户输入}}\n    ''')\n    |log()\n    |dlieInfer()\n        .addr('**************')\n        .port(31406)\n        .timeout(10s)\n        .isStream(TRUE)\n        .modelName('atom')\n    |httpRsp()"
	//script2 := "var var_1001 = stream\n            |listenHttp()\n                .port('1884')\n                .path('api/v1')\n        \n        var_1001\n            |dlieInfer()\n                .addr('127.0.0.1')\n                .port('8080')\n                .timeout('60s')\n                .modelName('AI模型')\n            |httpRsp()"
	//script3 := "var var_1000 = stream\n            |listenHttp()\n                .path('api/v1')\n                .port(1884)\n                .timeout(30s)\n        \n        var var_1001 = var_1000\n            |textInput()\n                .nodeID('WidgetKeyTextInput')\n                .inputKey('textInput')\n        \n        var var_1002 = var_1001\n            |dlieInfer()\n                .port(8001)\n                .timeout(30s)\n                .modelName('atom')\n                .modelType('text-vec')\n                .addr('mwh-deployment-cjiopf20sqh5re7qim5g')\n        \n        var var_1003 = var_1002\n            |vecSearch()\n                .database('test')\n                .url('')\n        \n        var var_1004 = var_1003\n            |join(var_1001)\n                .as('角色', '任务')\n            |promptTmpl('''作为{{.角色}}生成{{.任务}}''')\n        \n        var_1004\n            |dlieInfer()\n                .addr('mwh-deployment-cip0u4a0sqhb154duts0')\n                .port(8001)\n                .timeout(30s)\n                .modelName('atom')\n                .modelType('atom')\n            |httpRsp()"
	//_ = script
	//_ = script2
	//_ = script3
	//// 创建
	//id, err := KapacitorCli.RegisterTickScript(ctx, "123456", script3)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//t.Log(id)
	//res, err := KapacitorCli.KapacitorCli.ListTasks(&kpct.ListTasksOptions{Pattern: "*"})
	//if err != nil {
	//	t.Fatal(err)
	//}
	//t.Log(res)
	////删除
	//if err := KapacitorCli.DisableTickScript(ctx, "12345"); err != nil {
	//	t.Fatal(err)
	//}
	//res, err = KapacitorCli.kapacitorCli.ListTasks(&kpct.ListTasksOptions{Pattern: "*"})
	//if err != nil {
	//	t.Fatal(err)
	//}
	//t.Log(res)

}
