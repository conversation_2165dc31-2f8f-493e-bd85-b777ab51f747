package clients

import (
	"com/trs/hybase/client"
	"com/trs/hybase/client/params"
	"fmt"
	"sync"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
)

const (
	testTable = "health_test"
)

var (
	hybaseClient  *client.TRSConnection
	hybaseOnce    sync.Once
	connectionMap = make(map[string]*client.TRSConnection)
	mu            sync.Mutex
)

func GetHybaseClient() *client.TRSConnection {
	hybaseOnce.Do(func() {
		hybaseConf := conf.Config.HybaseConfig
		connectParams := params.NewConnectParams()
		if len(hybaseConf.Params) > 0 {
			for key, value := range hybaseConf.Params {
				connectParams.SetProperty(key, value)
			}
		}
		if hybaseConf.Timeout > 0 {
			connectParams.SetTimeout(hybaseConf.Timeout)
		}
		hybaseClient = client.NewTRSConnection(hybaseConf.Url, hybaseConf.Username, hybaseConf.Password, connectParams)
	})
	return hybaseClient
}

func GetHybaseClientByDatabaseConnection(dbConn *pb.DataConnection) *client.TRSConnection {
	if dbConn == nil {
		return GetHybaseClient()
	}
	if dbConn.Id == "" {
		return GetHybaseClient()
	}

	// 使用sync.Mutex保护connectionMap的并发访问
	mu.Lock()
	defer mu.Unlock()

	// 检查连接是否已存在
	if cli, ok := connectionMap[dbConn.Id]; ok {
		return cli
	}

	// 创建新连接
	connectParams := params.NewConnectParams()
	if dbConn.Config != "" {
		// TODO: 解析config中的参数并设置
	}

	serverUrl := fmt.Sprintf("http://%s:%s", dbConn.Address, dbConn.Port)
	cli := client.NewTRSConnection(serverUrl, dbConn.Username, dbConn.Password, connectParams)
	connectionMap[dbConn.Id] = cli
	return cli
}

func HybaseHealthz(dbConn *pb.DataConnection) (ok bool, msg string) {
	hybaseClient := GetHybaseClientByDatabaseConnection(dbConn)
	if hybaseClient == nil {
		return false, "无法获取Hybase客户端连接"
	}
	// 测试是否存在表
	if c, err := testGetDatabases(hybaseClient); err != nil {
		return false, err.Error()
	} else {
		if c > 0 {
			// 测试删除
			err := testDeleteDatabase(hybaseClient)
			if err != nil {
				return false, err.Error()
			}
		}
	}
	// 测试创建表
	err := testCreateDatabase(hybaseClient)
	if err != nil {
		return false, err.Error()
	}
	// 测试写数据
	err = testInsert(hybaseClient)
	if err != nil {
		return false, err.Error()
	}
	// 测试删除数据
	err = testDeleteQuery(hybaseClient)
	if err != nil {
		return false, err.Error()
	}
	return true, ""
}

func testGetDatabases(trsConn *client.TRSConnection) (int, error) {
	dbs, err := trsConn.GetDatabases(testTable, nil)
	if err != nil {
		return 0, err
	}

	return len(dbs), nil
}

func testDeleteDatabase(trsConn *client.TRSConnection) error {
	success, err := trsConn.DeleteDatabase(testTable, false)
	if err != nil {
		return err
	}

	if success {
		return nil
	} else {
		return fmt.Errorf("删除测试数据库失败")
	}
}

func testCreateDatabase(trsConn *client.TRSConnection) error {
	db := client.NewTRSDatabase(testTable, client.TYPE_DATABASE)
	db.SetEngineType("common")
	db.AddColumn(client.NewTRSDatabaseColumn("id", client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn("ori_id", client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn("text", client.TYPE_DOCUMENT))
	co := client.NewTRSDatabaseColumn("vec", client.TYPE_VECTOR)
	co.SetProperty("index.vector.dims", "3")
	db.AddColumn(co)
	success, err := trsConn.CreateDatabase(db)
	if err != nil {
		return err
	}
	if success {
		return nil
	} else {
		return fmt.Errorf("创建测试数据库失败")
	}
}

func testInsert(trsConn *client.TRSConnection) error {
	column2 := client.NewTRSInputColumn("vec", "")
	column2.SetFloatVector([]float32{0.1, 0.1, 0.2})

	rec := client.NewTRSInputRecord()
	rec.AddColumn(client.NewTRSInputColumn("text", "测试用文本测试用文本测试用文本测试用文本测试用文本测试用文本测试用文本测试用文本"))
	rec.AddColumn(column2)
	rec.AddColumn(client.NewTRSInputColumn("id", "abc"))
	rec.AddColumn(client.NewTRSInputColumn("ori_id", "cba"))

	var records []client.TRSInputRecord
	records = append(records, *rec)

	report := client.NewTRSReport(nil)
	params := params.NewOperationParams()
	err := trsConn.ExecuteInsert(testTable, records, params, report, nil)
	if err != nil {
		return err
	}

	return nil
}

func testDeleteQuery(trsConn *client.TRSConnection) error {
	query := "id#LIST:abc"
	params := params.NewSearchParams()
	report := client.NewTRSReport(nil)

	_, err := trsConn.ExecuteDeleteQuery(testTable, query, params, report)
	if err != nil {
		return err
	}

	return nil
}
