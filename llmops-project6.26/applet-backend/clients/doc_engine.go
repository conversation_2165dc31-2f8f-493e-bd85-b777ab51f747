package clients

import (
	"context"
	"fmt"
	"os"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	docEngine "transwarp.io/applied-ai/doc-engine-go/v1"
)

var (
	docEngineCliStore     = make(map[string]*docEngine.DocEngineClient)
	docEngineCliStoreLock = sync.Mutex{}
)

func GetDocEngineCli(ctx context.Context) (*docEngine.DocEngineClient, error) {
	cfg := conf.Config.DocEngineConfig
	docEngineConfig := docEngine.DocEngineConfig{Address: cfg.Address}
	cli, err := docEngine.NewDocEngineCli(ctx, docEngineConfig)
	return cli, err
}

func GetProjDocEngineCli(ctx context.Context, projectID string) (*docEngine.DocEngineClient, error) {
	if os.Getenv("DEBUG_LOCAL") == "true" {
		return GetDocEngineCli(ctx)
	}
	docEngineCliStoreLock.Lock()
	defer docEngineCliStoreLock.Unlock()
	if cli, ok := docEngineCliStore[projectID]; ok {
		return cli, nil
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	tenant, err := stdsrv.GetProjectTenant(projectID, token)
	if err != nil {
		return nil, stderr.Wrap(err, "stdsrv.GetProjectTenant")
	}
	addr := fmt.Sprintf("autocv-doc-engine-service.%s", tenant.TenantUid)
	cfg := conf.Config.DocEngineConfig
	docEngineConfig := docEngine.DocEngineConfig{Address: fmt.Sprintf("%s:%d", addr, cfg.Port)}
	cli, err := docEngine.NewDocEngineCli(ctx, docEngineConfig)
	docEngineCliStore[projectID] = cli
	return cli, err
}
