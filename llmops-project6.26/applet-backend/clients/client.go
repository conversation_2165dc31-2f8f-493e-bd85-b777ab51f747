package clients

import (
	"crypto/tls"
	"fmt"
	kpct "github.com/influxdata/kapacitor/client/v1"
	"github.com/redis/go-redis/v9"
	"net/http"
	"strings"
	"time"
	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/tkh-go"
)

var (
	InfluxdbCli           *influxdb.InfluxClient   // 用于查询时序数据库中的数据
	HttpCli               *clients.HttpClient      // 用于一般请求
	HttpCliWithoutTimeout *http.Client             // 用于进行文件传输等耗时较长的请求
	KapacitorCli          *Kpct                    // 用于与时序流处理引擎通信
	PromptCli             *PromptClient            // 调用prompt接口
	CSMCli                *CSMClient               // 代码仓库客户端
	MWHCli                *MWHClient               // 模型服务客户端
	CVATCli               *CVATClient              // 样本仓库客户端
	MLOpsCli              *clients.MlOpsClient     // mlops客户端
	AppletSvcCli          *clients.AppletSvcClient // applet-svc客户端
	KnowlHubClient        clients.KnowlHubClient   //知识库客户端

	//MqCli                 transport2.MqClient
	TKHClient *tkh.APIClient
	RedisCli  clients2.RedisClient

	// 注意，初始化时各客户端之间存在依赖，因此需要按顺序进行。
	initializers = []cliInitializer{
		InitRedisCli,
		//initMqCli,
		initHttpCli,
		initKapacitorCli,
		initPromptClient,
		initCSMClient,
		initMWHClient,
		initCVATClient,
		initMLOpsClient,
		initAppletSvcClient,
		initTKHCli,
	}

	testInitializers = []cliInitializer{
		// initMQTTCli,
		InitRedisCli,
		initHttpCli,
		// initBootMgrCli,
		initKapacitorCli,
		initPromptClient,
		initCSMClient,
		initMWHClient,
		initCVATClient,
		initMLOpsClient,
		initAppletSvcClient,
		// initTKHCli,
	}
)

type cliInitializer func() error

func Init() {
	if err := startCommonClients(); err != nil {
		panic(any(fmt.Errorf("failed to start common clients: %+v", err)))
	}
}

func TestInit() {
	if err := testStartCommonClients(); err != nil {
		panic(any(fmt.Errorf("failed to start common clients: %+v", err)))
	}
}

func startCommonClients() error {
	return initClients(initializers)
}

func testStartCommonClients() error {
	return initClients(testInitializers)
}

func initClients(initializers []cliInitializer) error {
	for _, initializer := range initializers {
		name := strings.TrimPrefix(toolkit.GetFunctionName(initializer), "init")
		if err := initializer(); err != nil {
			stdlog.WithError(err).Errorf("failed to initialize %s", name)
			return err
		}
		stdlog.Infof("succeed to initialize %s", name)
	}
	return nil
}

func initKapacitorCli() (err error) {
	url := conf.Config.Kapacitor.Addr
	ipPOrt := strings.Split(url, ":")
	if len(ipPOrt) != 2 {
		return stderr.Internal.Error("invalid kapacitor addr :%v", url)
	}
	if !strings.HasPrefix(url, "http://") {
		url = "http://" + url
	}
	cli, err := kpct.New(kpct.Config{URL: url})
	if err != nil {
		return err
	}
	KapacitorCli = &Kpct{
		KapacitorCli: cli,
		httpCli:      HttpCli,
		host:         ipPOrt[0],
	}
	stdlog.Infof("init kapacitor client success")
	return
}

func NewKapacitorCli(url string) (*Kpct, error) {
	ipPOrt := strings.Split(url, ":")
	if len(ipPOrt) != 2 {
		return nil, stderr.Internal.Error("invalid kapacitor addr :%v", url)
	}
	if !strings.HasPrefix(url, "http://") {
		url = "http://" + url
	}
	cli, err := kpct.New(kpct.Config{URL: url})
	if err != nil {
		return nil, err
	}
	KapacitorCli = &Kpct{
		KapacitorCli: cli,
		httpCli:      HttpCli,
		host:         ipPOrt[0],
	}
	stdlog.Infof("init kapacitor client success")
	return KapacitorCli, nil
}

func initPromptClient() (err error) {
	PromptCli = &PromptClient{
		httpCli: HttpCli,
		cfg:     &conf.Config.PromptCfg,
	}
	stdlog.Infof("init prompt client success")
	return
}
func initCSMClient() (err error) {
	CSMCli = &CSMClient{
		httpCli: HttpCli,
		cfg:     &conf.Config.CSMClientCfg,
	}
	stdlog.Infof("init csm client success")
	return
}

func initMLOpsClient() (err error) {
	MLOpsCli, err = clients.NewMLOpsClient(conf.Config.MLOps)
	if err != nil {
		return stderr.Wrap(err, "init mlops client with config %+v", conf.Config.MLOps)
	}
	stdlog.Infof("init mlops client success")
	return
}

func initAppletSvcClient() (err error) {
	kc := conf.Config.Kapacitor
	ac := clients.AppletSvcConfig{
		// DebugAddr:            kc.Addr,
		// DebugPath:            "",
		DefaultInnerPort:     kc.APIPort,
		DefaultAPIPath:       kc.APIPath,
		DefaultHealthAPIPath: kc.APIHealthPath,
	}
	AppletSvcCli, err = clients.NewAppletSvcClientWithMC(MLOpsCli, ac, HttpCli)
	if err != nil {
		return stderr.Wrap(err, "init applet svc client with config %+v", ac)
	}
	stdlog.Infof("init applet service client success")
	return
}

func initMWHClient() (err error) {
	MWHCli = &MWHClient{}
	MWHCli.initClient()
	stdlog.Infof("init mwh client success")
	return
}

func initCVATClient() (err error) {
	CVATCli = &CVATClient{}
	CVATCli.initClient()
	stdlog.Infof("init cvat client success")
	return
}

//func initMqCli() error {
//	switch conf.Config.Transport.Type {
//	case conf2.TypeMqtt:
//		mqttCfg := conf.Config.Mqtt
//		mqttCli := mqtt.NewMQTTClient(conf2.MqttConfig{
//			BrokerAddr:  mqttCfg.BrokerAddr,
//			ClientId:    fmt.Sprintf("applet-engine-%s-%s", mqttCfg.ClientId, time.Now().String()),
//			Qos:         conf.Config.Mqtt.Qos,
//			ConnTimeOut: time.Minute,
//		})
//		if err := mqttCli.Start(); err != nil {
//			return stderr.Wrap(err, "init mqtt client with config %+v", mqttCfg)
//		}
//		MqCli = mqttCli
//	case conf2.TypeRedis:
//		redisCfg := conf.Config.Redis
//		redisCli, err := redis.NewRedisMqClientV2(redisCfg)
//		if err != nil {
//			return stderr.Wrap(err, "init redisCli with config %+v", redisCli)
//		}
//		MqCli = redisCli
//	}
//	return nil
//}

//func initMQTTCli() error {
//	MqttCli = mqtt.NewMQTTClient(conf.Config.Mqtt)
//	if err := MqttCli.Start(); err != nil {
//		return err
//	}
//	return nil
//}

//	func initEdgeStatusMgr() error {
//		StatusMgr = stdstatus.NewEdgeStatusMgrWithConfig(&stdstatus.EdgeConfig{
//			EdgeId: conf.EdgeID(),
//			Influx: conf.Config.InfluxDB,
//		})
//		if err := StatusMgr.Init(); err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initMQTTCli() error {
//		MqttCli = mqtt.NewMQTTClient(conf.Config.Mqtt)
//		if err := MqttCli.Start(); err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initLicenseVerifier() error {
//		Verifier = license.NewVerifier(conf.Config.License.VerifierPath, conf.Config.License.LicensorAddr)
//		return nil
//	}
// func initInfluxDBCli() (err error) {
//	c := conf.Config.InfluxDB
//	InfluxdbCli, err = influxdb.NewInfluxClient(c)
//	if err != nil {
//		return stderr.Wrap(err, "influxdb's config maybe illegal: %+v", c)
//	}
//	if err = InfluxdbCli.CreateDB(); err != nil {
//		return stderr.Wrap(err, "fail to create database by: %+v", c)
//	}
//
//	//InfluxWriter, err = influxdb.NewInfluxWriter(influxdb.InfluxWriterConfig{
//	//	Cfg:           conf.Config.InfluxDB,
//	//	MaxBatch:      1000,
//	//	MaxConcurrent: 20,
//	//}, nil)
//	//if err != nil {
//	//	return stderr.Wrap(err, "failed to init influxdb writer")
//	//}
//	return nil
// }

// func initK8SCli() error {
//	var err error
//	if conf.InCluster() {
//		K8sClientSet, err = k8s.NewClientInCluster()
//	} else {
//		K8sClientSet, err = k8s.NewClientFromConfPath(conf.Config.Engine.KubeconfigPath)
//	}
//	if err != nil {
//		return stderr.Wrap(err, "failed to init k8s client")
//	}
//	return nil
// }

//	func initBootMgrCli() (err error) {
//		BootMgr, err = boot.NewBootManager(conf.Config.Engine)
//		if err != nil {
//			return stderr.Wrap(err, "boot manager's config maybe illegal: %+v", conf.Config.Engine)
//		}
//		return
//	}
//
//	func initK8SCli() (err error) {
//		if !conf.IsK8SMode() {
//			stdlog.Infof("skip initializing k8s client cause engine type is not k8s")
//			return nil
//		}
//		K8sCli, err = k8s.NewClient(k8s.Config{
//			InCluster:  conf.Config.Engine.InCluster,
//			ConfigPath: conf.Config.Engine.KubeconfigPath,
//			Timeout:    conf.Config.Engine.APITimeout,
//		})
//		if err != nil {
//			return
//		}
//		return K8sCli.Start()
//	}
//
//	func initSrsCli() error {
//		c := conf.Config.MMGateway
//		SrsCli = srs.NewMMGatewayAPI(c.Host, c.HttpPort, c.Timeout)
//		if SrsCli == nil {
//			return fmt.Errorf("invalid mm-gateway config: %+v", c)
//		}
//		return nil
//	}
//
//	func initKapacitorCli() (err error) {
//		url := conf.Config.Plmgr.TickRunner.KapacitorAddr
//		if url == "" {
//			url = DefaultKapacitorAddr
//		}
//		if !strings.HasPrefix(url, "http://") {
//			url = "http://" + url
//		}
//		KapacitorCli, err = kpct.New(kpct.Config{URL: url})
//		if err != nil {
//			return err
//		}
//		return
//	}
//
//	func initRscHubCli() (err error) {
//		RscHub, err = stdhub.NewResourceHub(conf.Config.DBPath, stdhub.NewDropRevisionOption(conf.Config.DropRscRevision))
//		if err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initNotifier() error {
//		Notifier = stdstatus.CreateMsgSender()
//		return nil
//	}
//func initBootMgrCli() (err error) {
//	BootMgr, err = boot.NewBootManager(conf.Config.Engine)
//	if err != nil {
//		return stderr.Wrap(err, "boot manager's config maybe illegal: %+v", conf.Config.Engine)
//	}
//	stdlog.Infof("init boot manager client success")
//	return
//}

func initHttpCli() error {
	httpCli := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: conf.Config.Server.HttpTimeout * time.Second,
	}
	HttpCli = &clients.HttpClient{Cli: httpCli}
	stdlog.Infof("init http client success")
	return nil
}
func InitRedisCli() error {
	redisCli, err := clients2.NewRedisClient(conf.Config.Redis)
	if err != nil {
		return err
	}
	RedisCli = redisCli
	return nil
}

func InitHttpCliForTest() error {
	httpCli := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: 60 * time.Second,
	}
	HttpCli = &clients.HttpClient{Cli: httpCli}
	stdlog.Infof("init http client success")
	return nil
}
func initHttpCliWithoutTimeout() error {
	HttpCliWithoutTimeout = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	return nil
}

func GetRedisCli() clients2.RedisClient {
	return RedisCli
}

func GetRedisRDB() redis.UniversalClient {
	return RedisCli.GetRdb()
}
