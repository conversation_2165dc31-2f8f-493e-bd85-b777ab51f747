package clients

import (
	"context"
	"fmt"

	"github.com/robfig/cron/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/tkh-go"
)

func initTKHCli() (err error) {
	if !conf.Config.TKHConfig.Enabled {
		return nil
	}
	c := cron.New()
	TKHClient, err = newTkhCli()
	if err != nil {
		return
	}
	// 定时获取token并更新tkh client
	_, err = c.AddFunc("@every 5m", func() {
		cli, err := newTkhCli()
		if err != nil {
			stdlog.Error(err, "newTkhCli")
			return
		}
		TKHClient = cli
	})
	return
}

func newTkhCli() (*tkh.APIClient, error) {
	token, err := getTkhToken(context.Background())
	if err != nil {
		return nil, err
	}
	cfg := &tkh.Configuration{
		Host:          conf.Config.TKHConfig.Host,
		Scheme:        conf.Config.TKHConfig.Schema,
		UserAgent:     "llmops",
		DefaultHeader: map[string]string{"authorization": fmt.Sprintf("Bearer %s", token)},
		Servers: tkh.ServerConfigurations{
			{
				URL: conf.Config.TKHConfig.BaseURL,
			},
		},
	}
	return tkh.NewAPIClient(cfg), nil
}

func getTkhToken(ctx context.Context) (string, error) {
	param := &clients.HttpParam{
		Method:  "POST",
		Url:     conf.Config.TKHConfig.GetTokenUrl,
		ReqBody: conf.Config.TKHConfig.GetTokenBody,
		Header: map[string]string{
			"Content-Type": "application/json",
		},
	}

	rsp, err := HttpCli.HttpCallString(ctx, param)
	if err != nil {
		return "", stderr.Wrap(err, "getTkhToken")
	}
	return rsp, nil
}
