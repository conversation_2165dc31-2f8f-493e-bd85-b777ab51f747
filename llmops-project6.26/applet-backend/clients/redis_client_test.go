package clients

import (
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

var (
	cli clients.RedisClient
)

func InitRedisConf() conf.RedisConfig {
	return conf.RedisConfig{
		Addrs: "localhost",
	}
}

func init() {
	TestInit()
	cli = GetRedisClient(InitRedisConf())
}

func TestRedisGet(t *testing.T) {
	key := "test_key"
	val := "test_val"
	err := cli.Set(key, val, 0)
	if err != nil {
		t.Fatal(err)
	}
	v, err := cli.Get(key)
	if err != nil {
		t.Fatal(err)
	}
	if v != val {
		t.Fatalf("get value %v not equals to %v", v, val)
	}
}

func TestRedisDel(t *testing.T) {
	key := "test_key"
	val := "test_val"
	err := cli.Set(key, val, 0)
	if err != nil {
		t.Fatal(err)
	}
	err = cli.Del(key)
	if err != nil {
		t.Fatal(err)
	}
	_, err = cli.Get(key)
	if err == nil {
		t.Fatalf("del failed")
	}
}

func TestRedisDelHash(t *testing.T) {
	key := "test_key"
	filed := "test_field"
	val := "test_val"
	err := cli.HSet(key, filed, val)
	if err != nil {
		t.Fatal(err)
	}

	v, err := cli.HGet(key, filed)
	if err != nil {
		t.Fatal(err)
	}
	if v != val {
		t.Fatalf("get value %v not equals to %v", v, val)
	}
	vv, err := cli.Get(key)
	t.Log(vv, err)
	err = cli.Del(key)
	if err != nil {
		t.Fatal(err)
	}
	_, err = cli.HGet(key, filed)
	if err == nil {
		t.Fatalf("del failed")
	}
}

func TestRedisCli(t *testing.T) {

}
