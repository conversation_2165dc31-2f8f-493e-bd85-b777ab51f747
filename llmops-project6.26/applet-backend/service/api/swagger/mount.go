package swagger

import (
	"net/http"
	"path"
	"unicode"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
)

const uiRoot = "public/swagger-ui"

func NewAPI(root string) *restful.WebService {
	api := new(restful.WebService)
	api.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	api.Route(api.GET("/swagger.json").To(GetSwagger))
	api.Route(api.GET("/").To(GetSwaggerUI))
	api.Route(api.GET("/{subpath:*}").To(GetSwaggerUIResources).
		Param(api.PathParameter("subpath", "子路径")))
	return api
}

func GetSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices: restful.RegisteredWebServices(),
		DefinitionNameHandler: func(s string) string {
			if s == "" {
				return ""
			}
			if unicode.IsLower(rune(s[0])) {
				return ""
			}
			return s
		},
	}
	swagger := restfulspec.BuildSwagger(config)
	swagger.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Description: "API Docs for Server Template",
			Title:       "LLM Mlops Chain API DOCS (v1)",
			Version:     "1.0.0",
		},
	}
	// 添加安全定义
	swagger.SecurityDefinitions = map[string]*spec.SecurityScheme{
		"BearerAuth": {
			SecuritySchemeProps: spec.SecuritySchemeProps{
				Type:        "apiKey",
				Name:        "Authorization",
				In:          "header",
				Description: "使用 Bearer Token 进行认证",
			},
		},
	}
	swagger.Schemes = []string{"https", "http"}

	// 应用全局安全配置
	swagger.Security = []map[string][]string{
		{"BearerAuth": {}},
	}

	helper.SuccessResponse(response, swagger)
}

func GetSwaggerUI(request *restful.Request, response *restful.Response) {
	http.ServeFile(
		response.ResponseWriter,
		request.Request,
		uiRoot+"/index.html")
}

func GetSwaggerUIResources(req *restful.Request, resp *restful.Response) {
	actual := path.Join(uiRoot, req.PathParameter("subpath"))
	http.ServeFile(
		resp.ResponseWriter,
		req.Request, actual)
}
