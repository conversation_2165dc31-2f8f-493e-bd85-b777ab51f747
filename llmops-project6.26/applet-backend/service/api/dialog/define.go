package dialog

import (
	"github.com/emicklei/go-restful/v3"
	"strconv"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func (r *Resource) DeleteAppChat(request *restful.Request, response *restful.Response) {

	ctx := helper.GenNewCtx(request)

	chatId := request.PathParameter(helper.PathParamAppletChatID)
	if chatId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty conversation id"))
		return
	}
	appId := request.PathParameter(helper.PathParamAppID)
	if appId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty app id"))
		return
	}

	_, err := applet.DialogManager.GetDialogByChatId(ctx, appId, chatId)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("cannot find dialog by app_id and chat_id"))
		return
	}

	id, err := applet.DialogManager.DeleteSingleDialog(ctx, &pb.MessageReq{ChatId: chatId})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, id)
}

func (r *Resource) GetAppChat(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	chatId := request.PathParameter(helper.PathParamAppletChatID)
	if chatId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty chat id"))
		return
	}

	appId := request.PathParameter(helper.PathParamAppID)
	if appId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty app id"))
		return
	}

	_, err := applet.DialogManager.GetDialogByChatId(ctx, appId, chatId)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("cannot find dialog by app_id and chat_id"))
		return
	}

	page, err := getPage(request)
	if err != nil {
		page = nil
	}

	messagesRes, err := applet.DialogManager.GetAppChat(ctx, &pb.MessageReq{
		ChatId:  chatId,
		PageReq: page,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, messagesRes)

}

func getPage(request *restful.Request) (*pb.PageReq, error) {
	page := request.QueryParameter("page")
	pageSize := request.QueryParameter("page_size")
	pageNum, err := strconv.Atoi(page)
	if err != nil {
		return nil, err
	}
	pageSizeNum, err := strconv.Atoi(pageSize)
	if err != nil {
		return nil, err
	}
	sortBy := request.QueryParameter("sort_by")
	isDesc := strings.ToLower(request.QueryParameter("is_desc")) == "true"
	return &pb.PageReq{
		Page:     int32(pageNum),
		PageSize: int32(pageSizeNum),
		SortBy:   sortBy,
		IsDesc:   isDesc,
	}, nil
}

func (r *Resource) GetAppChats(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	content := request.QueryParameter("content")

	page, err := getPage(request)
	if err != nil {
		page = nil
	}

	appId := request.QueryParameter(helper.PathParamAppID)

	start, err := strconv.Atoi(request.QueryParameter("start_time"))
	if err != nil {
		start = 0
	}
	startTime := int64(start)

	end, err := strconv.Atoi(request.QueryParameter("end_time"))
	if err != nil {
		end = 0
	}
	endTime := int64(end)

	dialogs, err := applet.DialogManager.GetAppChats(ctx, &pb.DialogReq{
		AppId:     appId,
		Content:   content,
		StartTime: startTime,
		EndTime:   endTime,
		PageReq:   page,
	})

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, dialogs)
}

func (r *Resource) GetDialogApps(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	chains, err := applet.DialogManager.GetDialogApps(ctx)

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, chains)
}

func (r *Resource) AddDialogApp(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	chainId := request.PathParameter(helper.PathParamAppID)
	if chainId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
		return
	}

	chains, err := applet.DialogManager.SaveDialogApp(ctx, chainId)

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, chains)
}

func (r *Resource) DeleteDialogApp(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	chainId := request.PathParameter(helper.PathParamAppID)
	if chainId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
		return
	}

	chains, err := applet.DialogManager.DeleteDialogApp(ctx, chainId)

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, chains)
}

func (r *Resource) DownloadDialogs(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	downloadReq := &pb.DownloadDialogReq{}

	if err := request.ReadEntity(downloadReq); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	if downloadReq.ChatIds == nil || len(downloadReq.ChatIds) == 0 {
		helper.ErrorResponse(response, stderr.Internal.Error("empty conversation ids"))
		return
	}

	res, err := applet.DialogManager.DownloadDialogs(ctx, downloadReq)

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, res)
}
