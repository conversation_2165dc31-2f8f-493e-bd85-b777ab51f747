package knowledge_base

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func AsyncUpdateKBMetrics(kbId string, actionType knowledge_base.KbMetricsType) {
	helper.GoFunWithRecover(func() {
		kbm := knowledge_base.GetKnowledgeBaseManager()
		if err := kbm.UpdateKnowledgeMetricsInfo(context.Background(), kbId, actionType); err != nil {
			stdlog.Errorf("metrics err ：%v,kb :%v ,event :%v", err, kbId, actionType)
		}
	})
}
