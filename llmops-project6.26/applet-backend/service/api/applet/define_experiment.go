package applet

import (
	"github.com/aws/smithy-go/ptr"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func (r *Resource) CreateExperiment(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call CreateExperiment"))
			return
		}
	}()

	ctx := helper.GenNewCtx(request)
	createReq := new(models.CreateExperimentReq)
	experimentDO := new(models.AppletExperimentDO)
	if err = request.ReadEntity(createReq); err != nil {
		return
	}
	if err = helper.CopyTheSameFields(createReq, experimentDO); err != nil {
		return
	}
	// 创建方式为应用链创建
	experimentDO.CreatedBy = models.AppletTypeAssistant
	_, err = applet.ExperimentManager.GetExperimentByID(ctx, createReq.ChainID)
	// 查询发现已经存在,更新
	if err == nil {
		experimentDO.ChainID = ""
		experimentDO.Status = models.CanceledStatus
		_, err = applet.ExperimentManager.UpdateExperiment(ctx, createReq.ChainID, experimentDO)
		if err != nil {
			return
		}
	} else {
		// 创建应用体验,需设置创建者等额外信息
		creator, err2 := helper.GetUser(ctx)
		if err2 != nil {
			err = err2
			return
		}
		experimentDO.Creator = creator
		experimentDO.Status = models.CanceledStatus
		experimentDO.ChainID = createReq.ChainID
		experimentDO.ProjectID = helper.GetProjectID(ctx)
		if err = experimentDO.Valid(); err != nil {
			return
		}
		_, err2 = applet.ExperimentManager.CreateExperiment(ctx, experimentDO)
		if err2 != nil {
			err = err2
			return
		}
	}

	res, err := applet.ExperimentManager.GetExperimentByID(ctx, createReq.ChainID)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, res)
}

// PublishExperiment TODO 应用真正部署
func (r *Resource) PublishExperiment(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call PublishExperiment"))
			return
		}
	}()

	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// TODO  应用真正部署 补充 experimentDO的ServiceInfo信息 并通过 go-runtine去检查状态
	experimentDO := new(models.AppletExperimentDO)
	experimentDO.ChainID = ""
	experimentDO.Status = models.PublishedStatus
	_, err = applet.ExperimentManager.UpdateExperiment(ctx, id.ID, experimentDO)
	if err != nil {
		return
	}

	res, err := applet.ExperimentManager.GetExperimentByID(ctx, id.ID)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, res)
}

// CancelExperiment TODO 建应用下线，并清空ServiceInfo信息
func (r *Resource) CancelExperiment(request *restful.Request, response *restful.Response) {
	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err := applet.ExperimentManager.UpdateExperiment(ctx, id.ID,
		&models.AppletExperimentDO{Status: models.CanceledStatus})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// TODO  变为未发布转态,同时将服务下线
	res, err := applet.ExperimentManager.GetExperimentByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) DeleteExperiment(request *restful.Request, response *restful.Response) {
	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := applet.ExperimentManager.GetExperimentByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err = applet.ExperimentManager.DeleteExperimentByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)

}
func (r *Resource) ListExperiment(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	queryParam := new(dao.ExperimentQueryParam)
	if err := request.ReadEntity(queryParam); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if queryParam.ProjectID == nil {
		queryParam.ProjectID = ptr.String(helper.GetProjectID(ctx))
	}
	experimentDOs, err := applet.ExperimentManager.ListExperiment(ctx, queryParam)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, experimentDOs)
}
func (r *Resource) ListExperimentLabels(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectID := helper.GetProjectID(ctx)
	labels, err := applet.LabelManager.ListExperimentLabel(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, labels)
}
