package applet

import (
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func (r *Resource) ChainTemplateService(root string) {
	resourceIDPathParam := r.PathParameter(resourceID, "模板id")
	projectIDQueryParam := r.QueryParameter(projectID, "项目ID")

	r.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	tags := []string{"应用链模板管理"}

	r.Route(r.GET("/template-groups").To(r.ListTemplateGroups).
		Doc("查询应用链模板分组").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.QueryParameter(QueryParamSimple, "是否需要返回模板详情")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.TemplateGroup{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	r.Route(r.GET("/chain-templates").To(r.ListChainTemplates).
		Doc("查询应用链模板列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.QueryParameter(QueryParamSimple, "是否需要返回模板详情")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.ChainTemplate{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	r.Route(r.GET("/chain-templates/{id}").To(r.GetChainTemplate).
		Doc("获取单个应用链模板").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(resourceIDPathParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ChainTemplate{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	//r.Route(r.POST("/chain-templates/{id}").To(r.UpsertChainTemplate).
	//	Doc("创建或更新单个应用链模板").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(projectIDQueryParam).
	//	Param(resourceIDPathParam).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
	//	Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	//
	//r.Route(r.DELETE("/chain-templates/{id}").To(r.DeleteChainTemplate).
	//	Doc("删除单个应用链模板").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(projectIDQueryParam).
	//	Param(resourceIDPathParam).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
	//	Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

}
