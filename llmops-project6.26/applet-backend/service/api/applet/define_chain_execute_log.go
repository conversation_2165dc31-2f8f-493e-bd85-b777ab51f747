package applet

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

//func (r *Resource) ConsumerLogs(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	response.Header().Set("Access-Control-Allow-Origin", "*")
//	response.Header().Set("Access-Control-Allow-Headers", "Content-Type")
//	response.Header().Set("Content-Type", "text/event-stream")
//	response.Header().Set("Cache-Control", "no-cache")
//	response.Header().Set("Connection", "keep-alive")
//	response.Flush()
//
//	runID := request.PathParameter(helper.PathParamAppletRunID)
//	chainID := request.PathParameter(helper.PathParamAppletID)
//	if chainID == "" || runID == "" {
//		helper.ErrorSSEResponse(response, stderr.InvalidParam.Error("no chain id or run id"))
//		return
//	}
//
//	debugMsgHandler := func(msg *debug.DebugMessage) error {
//		msg.Input, msg.Output, msg.Log = nil, nil, nil
//		err := sse.Encode(
//			response,
//			sse.Event{
//				Id:    uuid.New().String(),
//				Event: "message",
//				Data:  stdsrv.AnyToString(msg),
//			})
//		if err != nil {
//			stdlog.Errorf("encode msg err :%v", err)
//		}
//		response.Flush()
//		return nil
//	}
//
//	if err := applet_log.SyncSub(ctx, runID, debugMsgHandler); err != nil {
//		helper.ErrorSSEResponse(response, stderr.Wrap(err, "subscribe log :%v err", runID))
//		return
//	}
//	stdlog.Infof("closing connection")
//}

func (r *Resource) ConsumerNodeLogs(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	runID := request.PathParameter(helper.PathParamAppletRunID)
	uuNodeId := request.PathParameter(helper.PathParamAppletNodeID)
	log, err := dao.ChainDebugLogDAOImpl.ListDebugLog(ctx, runID, uuNodeId)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, log)
}
