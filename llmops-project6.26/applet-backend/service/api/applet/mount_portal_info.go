package applet

import (
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	QueryParamIndustry  = "industry"
	QueryParamProjectID = "project_id"
)

func (r *Resource) PortalInfo(root string) {
	projectIDQueryParam := r.QueryParameter(QueryParamProjectID, "项目ID").Required(true)
	//industryQueryParam := r.QueryParameter(QueryParamIndustry, "行业名称")

	r.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	// READ APIs
	tags := []string{"首页信息"}

	r.Route(r.GET("/popular-experis").To(r.ListExperis).
		Doc("查询受欢迎的体验对象").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(helper.LimitQP.Param()).
		Param(helper.SortByQP.Param()).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.AppletChainBaseDO{}))

	// 精选应用、精选模型在cas维护
	//r.Route(r.GET("/selected-experis").To(r.ListSelectedExperis).
	//	Doc("查询某一行业的精选-应用体验对象,不传行业则查询所有").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(projectIDQueryParam).
	//	Param(industryQueryParam.Required(false)).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.AppletChainBaseDO{}))
	//
	//r.Route(r.POST("/selected-experis:add").To(r.AddSelectedExperis).
	//	Doc("将多个应用体验选择为某一个行业的精选应用").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(pb.SetSelectedReq{}).
	//	Param(projectIDQueryParam).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))
	//
	//r.Route(r.POST("/selected-experis:remove").To(r.RemoveSelectedExperis).
	//	Doc("将多个应用体验从某一行业的精选应用移除").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(projectIDQueryParam).
	//	Reads(pb.SetSelectedReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.GET("/app/stats").To(r.CountNums).
		Doc("统计应用和应用体验的数量").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

}
