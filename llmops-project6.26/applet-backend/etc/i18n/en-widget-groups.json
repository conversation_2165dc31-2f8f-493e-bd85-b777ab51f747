[{"id": "WidgetGroupInput", "name": "Input", "desc": "Processing user input", "sort_flag": 0, "widgets": [{"id": "WidgetKeyChatHistory", "name": "Conversation History", "desc": "Used to provide historical conversations as background knowledge to downstream prompt templates", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "ChatInput", "name": "Dialogue Input", "desc": "Supported Types: Sync-QAItems: [\n  {\n    \"Q\": \"Q1\",\n    \"A\": \"A1\"\n  },\n  {\n    \"Q\": \"Q2\",\n    \"A\": \"A2\"\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-QAItems"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxRounds", "name": "Maximum Conversation Rounds", "desc": "The maximum number of conversation turns to retain in the historical dialogue, any turns exceeding this limit will be automatically truncated. Appropriately setting this property can effectively prevent exceeding the model's maximum context limit.", "number_range": {"min": 1, "max": 99, "step": 1}, "default_value": "5", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Tmpl", "name": "Conversation Template", "desc": "Use GoTemplate to concatenate the input conversation history structure into a string", "default_value": "{{/*\ngo text template\ntransfer conversation history message to plain text, and forward them to the widget in the chain \nstructure of original conversation history:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\nMessage after transition\n[Round0]\nUser:question1\nAssistant:answer1\n\n[Round1]\nUser:question2\nAssistant:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\nUser：{{$qa.Q}}\nAssistant：{{$qa.A}}\n{{end}}", "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported Type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyChatHistory-->\n**Operator Category**: Input\n**Operator Purpose**: Used to provide historical conversation as background knowledge to downstream prompt templates\n**Input Format**: [QAItems](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    QAItems:\n    [\n      {\n        \"Q\": \"Q1\",\n        \"A\": \"A1\"\n      },\n      {\n        \"Q\": \"Q2\",\n        \"A\": \"A2\"\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Detailed Information**: [Conversation History - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupInput)", "md_summary_desc": "<!--WidgetKeyChatHistory-->\n**Operator Category**: Input\n**Operator Purpose**: Used to provide historical conversation as background knowledge to downstream prompt templates"}, {"id": "WidgetKeyFileInput", "name": "File Upload", "desc": "Used for loading a single uploaded file, output SFSFile type data", "group": "WidgetGroupInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "Upload File", "desc": "Upload file, supported types: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"content\"\n  }\n].", "datasource": "Sure, please provide the text you would like translated.", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "Read File Content", "desc": "Whether to read file content, if enabled, it will read the file content and pass the file byte stream to the downstream node; otherwise, it will only pass the file path.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "Maximum File Size (MB)", "desc": "The maximum file size allowed for upload, in MB", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "Allowed File Extensions", "desc": "Allowed file extensions for upload, only one extension per line is permitted, you can input *、txt、pdf、docx, etc. Inputting * or leaving it blank means all file formats are allowed.", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"content\"\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}], "md_full_desc": "<!--WidgetKeyFileInput-->\n**Operator Category**: Input\n**Operator Purpose**: Used to load a single uploaded file, output data of SFSFile type\n**Input Format**: [SFSFiles](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFiles:\n    [\n      {\n        \"name\": \"name\",\n        \"uid\": \"uid\",\n        \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n        \"content\": \"Y29udGVudA==\"\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [SFSFile](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transfer Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transfer Method)\n**Detailed Information**: [File Upload - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupInput)", "md_summary_desc": "<!--WidgetKeyFileInput-->\n**Operator Category**: Input\n**Operator Purpose**: Used to load a single uploaded file, output SFSFile type data"}, {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used to output the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported Type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyTextInput-->\n**Operator Category**: Input\n**Operator Purpose**: Used to output the input text as is\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Text Input - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupInput)", "md_summary_desc": "<!--WidgetKeyTextInput-->\n**Operator Category**: Input\n**Operator Purpose**: Used to output the input text as is"}, {"id": "WidgetKeyUpstreamInput", "name": "Json Input", "desc": "Input a Json string and deserialize it", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "JsonInput", "name": "Json Input", "desc": "Supported Type: Sync-JsonString: \"{\\\"key\\\":\\\"value\\\"}, [\\\"value1\\\"], \\\"text\\\", ...\\\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-JsonString"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Deserialize the input JSON, supported types: Sync-Any: \"{}、text、[{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}], "md_full_desc": "<!--WidgetKeyUpstreamInput-->\n**Operator Category**: Input\n**Operator Purpose**: Input a Json string and deserialize it\n**Input Format**: [JsonString](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    JsonString:\n    \"{\\\"key\\\":\\\"value\\\"}, [\\\"value1\\\"], \\\"text\\\", ...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**More Information**: [Json Input - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupInput)", "md_summary_desc": "<!--WidgetKeyUpstreamInput-->\n**Operator Category**: Input\n**Operator Purpose**: Input Json string and deserialize it"}]}, {"id": "WidgetGroupProcessText", "name": "Text Processing", "desc": "Perform security filtering, concatenation, and other processing on the text", "sort_flag": 1, "widgets": [{"id": "WidgetKeyInputGuardrail", "name": "Input Security Guardrail", "desc": "Conduct security checks on the input and configure output intervention scripts", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "To Be Detected Text", "desc": "Supported Type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "Input Security Policy", "desc": "Click to configure prompt injection, sensitive word protection, and other security policies details", "type": "TYPE_GUARDRAIL_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "Configuration Strategy Id", "desc": "Configuration policy ID", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Through security protection, the supported type is: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyInputGuardrail-->\n**Operator Category**: Text Processing\n**Operator Purpose**: To perform safety checks on the input and configure output intervention scripts\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Input Safety Guardrails - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessText)", "md_summary_desc": "<!--WidgetKeyInputGuardrail-->\n**Operator Category**: Text Processing\n**Operator Purpose**: To perform safety checks on the input and configure output intervention scripts"}, {"id": "WidgetKeyOutputGuardrail", "name": "Output Safety Guardrail", "desc": "Conduct security checks on the output and configure intervention scripts", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "To Be Detected Text", "desc": "Text to be detected, supported types: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "Output Security Policy", "desc": "Click to configure prompt injection, sensitive word protection, and other security policies details", "type": "TYPE_GUARDRAIL_OUTPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "Configuration Strategy Id", "desc": "Configuration policy ID", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Through security protection, supported types: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}], "md_full_desc": "<!--WidgetKeyOutputGuardrail-->\n**Operator Category**: Text Processing\n**Operator Purpose**: To perform safety checks on the output and configure intervention scripts\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**More Information**: [Output Safety Guardrails - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessText)", "md_summary_desc": "<!--WidgetKeyOutputGuardrail-->\n**Operator Category**: Text Processing\n**Operator Purpose**: To perform safety checks on the output and configure intervention scripts"}, {"id": "WidgetKeySampleCubePrompt", "name": "Prompt Template", "desc": "Used to invoke the prompt templates that have been released in the corpus repository for prompt concatenation", "group": "WidgetGroupProcessText", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input", "desc": "Input", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeySampleCubePrompt-->\n**Operator Category**: Text Processing\n**Operator Purpose**: Used to call the prompt template that has been released in the corpus repository for prompt concatenation\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Prompt Template - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessText)", "md_summary_desc": "<!--WidgetKeySampleCubePrompt-->\n**Operator Category**: Text Processing\n**Operator Purpose**: Used to call the prompt template published in the corpus repository for prompt concatenation"}, {"id": "WidgetKeyTextTemplate", "name": "General Text Template", "desc": "Concatenate variables into a piece of text, the text template may not reference any variables or may reference multiple variables", "group": "WidgetGroupProcessText", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Template", "name": "Template", "default_value": "The First Variable:{{.Input1}}, The Second Variable:{{.Input2}}", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input1", "name": "Input1", "desc": "Supported type: Sync-Any: \"{}、text、[{}]...\". \n\nTranslates to: Supported type: Sync-Any: \"{}, text, [{}...]\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input2", "name": "Input2", "desc": "Supported type: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation marks such as \"、\" have been kept as in the original text. If you prefer to use standard English punctuation, it would be: \nSupported type: Sync-Any: \"{}, text, [{}...]\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported Type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "dynamic_end_point": true, "md_full_desc": "<!--WidgetKeyTextTemplate-->\n**Operator Category**: Text Processing\n**Operator Purpose**: Concatenate variables into a piece of text, allowing the text template to not reference any variables or reference multiple variables\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [General Text Template - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessText)", "md_summary_desc": "<!--WidgetKeyTextTemplate-->\n**Operator Category**: Text Processing\n**Operator Purpose**: Concatenate variables into a piece of text, the text template allows not referencing variables or referencing multiple variables"}]}, {"id": "WidgetGroupAIModel", "name": "Model Invocation", "desc": "Invoke text, image, voice, and other model services", "sort_flag": 2, "widgets": [{"id": "WidgetKeyAudioToText", "name": "Audio To Text", "desc": "Convert the input audio to text", "group": "WidgetGroupAIModel", "params": [{"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "Audio", "name": "Audio File", "desc": "Supported Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"content\"\n}.", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Service", "datasource": "{\"kind\":\"MODEL_KIND_SR\",\"subKind\":\"MODEL_SUB_KIND_SR_STT\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Audio file translation result, supported types: Sync-AudioTranResp: {\n  \"language\": \"language\",\n  \"text\": \"text\",\n  \"words\": [\n    {\n      \"word\": \"word\",\n      \"start\": 0,\n      \"end\": 0\n    }\n  ],\n  \"segments\": [\n    {\n      \"id\": 0,\n      \"seek\": 0,\n      \"start\": 0,\n      \"end\": 0,\n      \"text\": \"text\",\n      \"tokens\": null,\n      \"temperature\": 0,\n      \"avg_logprob\": 0,\n      \"compression_ratio\": 0,\n      \"no_speech_prob\": 0\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-AudioTranResp"]}}], "md_full_desc": "<!--WidgetKeyAudioToText-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Convert input audio to text\n**Input Format**: [SFSFile](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    </pre>\n</details>\n\n**Output Format**: [AudioTranResp](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    AudioTranResp:\n    {\n      \"language\": \"language\",\n      \"text\": \"text\",\n      \"words\": [\n        {\n          \"word\": \"word\",\n          \"start\": 0,\n          \"end\": 0\n        }\n      ],\n      \"segments\": [\n        {\n          \"id\": 0,\n          \"seek\": 0,\n          \"start\": 0,\n          \"end\": 0,\n          \"text\": \"text\",\n          \"tokens\": null,\n          \"temperature\": 0,\n          \"avg_logprob\": 0,\n          \"compression_ration\": 0,\n          \"no_speech_prob\": 0\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Audio to Text - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyAudioToText-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Convert the input audio to text"}, {"id": "WidgetKeyDlieModel", "name": "General Inference", "desc": "Callable all available model services", "group": "WidgetGroupAIModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Input", "desc": "Supported type: Sync-Any: \"{}、text、[{}]...\". \n\nTranslates to: Supported type: Sync-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Service", "datasource": "{\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported types: Any-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation marks such as \"、\" have been kept as in the original text. If you prefer to use standard English punctuation, it would be: Supported types: Any-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyDlieModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Can invoke all online model services\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [General Inference - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyDlieModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Can invoke all online model services"}, {"id": "WidgetKeyImageAnalysis", "name": "Image Understanding", "desc": "Analyze and understand the uploaded image", "group": "WidgetGroupAIModel", "params": [{"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "File", "name": "Image", "desc": "Supported Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"content\"\n}.", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "UseBase64", "name": "Base64 Encoding", "desc": "Whether to use base64 to encode images before calling the model, it needs to be enabled for remote and cross-space calls.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Text", "desc": "Text description, assist with image content analysis and understanding, supported types: Sync-String: \"text\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Image Understanding Service", "datasource": "{\"kind\":\"<PERSON>ODEL_KIND_MULTI\",\"subKind\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Image understanding text content, supported types: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}], "md_full_desc": "<!--WidgetKeyImageAnalysis-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: To analyze and understand the uploaded image\n**Input Format**: [SFSFile](doc:///help_document/en/index.html#DataType), [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    \n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [Image Understanding - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyImageAnalysis-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: To analyze and understand the uploaded image"}, {"id": "WidgetKeyLLMModel", "name": "Text Generation", "desc": "Used to invoke the text generation model service", "group": "WidgetGroupAIModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Input prompt for LLM, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "System Prompt Word", "desc": "System prompt of the model, used to define AI behavior and response style, improving the accuracy of responses", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM Model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "Conversation History", "desc": "Conversation History", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "LLM output text, supported types: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}], "md_full_desc": "<!--WidgetKeyLLMModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Used for invoking text generation model services\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [Text Generation - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyLLMModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Used for invoking text generation model services"}, {"id": "WidgetKeyTextEnhance", "name": "Text Enhancement", "desc": "Summarize the segmented paragraphs or try to raise several questions", "group": "WidgetGroupAIModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Input Paragraph", "desc": "Segmented text paragraphs, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnhanceMode", "name": "Enhanced Mode", "desc": "Select the text enhancement mode, summarize each paragraph, and ask questions to increase semantic richness, which is more beneficial for retrieval.", "default_value": "AUGMENTED_CHUNK_TYPE_SUMMARY", "datasource": "AUGMENTED_CHUNK_TYPE_SUMMARY@@Paragraph Summary, AUGMENTED_CHUNK_TYPE_QUESTION@@Paragraph Question, AUGMENTED_CHUNK_TYPE_CUSTOM@@Custom Augmentation", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "CustomSummaryPrompt", "name": "Summary Prompt Words", "desc": "Custom Summary Prompt", "default_value": "Please thoroughly read and study the text below, then summarize it in 3 sentences or less (under 200 words), ensuring the summary is generated in the same language as the original text.", "precondition": "[{\"both\":\"EnhanceMode==AUGMENTED_CHUNK_TYPE_SUMMARY\"}]", "required": true, "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "<PERSON><PERSON>", "name": "Number Of Questions Asked", "desc": "Limit on the number of questions per paragraph", "number_range": {"min": 1, "max": 10}, "default_value": "3", "precondition": "[{\"both\":\"EnhanceMode==AUGMENTED_CHUNK_TYPE_QUESTION\"}]", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "CustomQuestionPrompt", "name": "Question Prompt Words", "desc": "Custom Question Prompt", "default_value": "Carefully read and analyze the text below, then formulate a meaningful question, based on the it, that can be answered directly using information from the text, ensuring the question is generated in the same language as the original material", "precondition": "[{\"both\":\"EnhanceMode==AUGMENTED_CHUNK_TYPE_QUESTION\"}]", "required": true, "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "CustomPrompt", "name": "Custom Prompt Words", "desc": "Custom Enhanced Prompt", "default_value": "Please thoroughly read and study the text below, and translate the original text to English precisely", "precondition": "[{\"both\":\"EnhanceMode==AUGMENTED_CHUNK_TYPE_CUSTOM\"}]", "required": true, "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for text enhancement", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Paragraph with enhanced text, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetKeyTextEnhance-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: To summarize the segmented paragraphs or attempt to propose several questions\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Text Enhancement - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyTextEnhance-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: To summarize the segmented paragraphs or attempt to generate several questions"}, {"id": "WidgetKeyTextRerank", "name": "Text Rearrangement", "desc": "Combine the user's question with multiple segments of text for reordering, and return the reordered text along with the corresponding sorting scores.", "group": "WidgetGroupAIModel", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "User Question", "desc": "Text sorting depends on the user's question, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Texts", "name": "Text Segment", "desc": "User input of multiple segments of text, supported types: Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "TopK", "name": "TopK", "desc": "The maximum number of paragraphs to retain after reordering", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "The minimum threshold requirement for text reflow to be preserved", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for text rearrangement", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Text segments accompanied by relevant scores and sorted in descending order of scores, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetKeyTextRerank-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Combine user questions with multiple text segments for re-ranking, return the re-ranked text and corresponding ranking scores\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Text Re-ranking - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyTextRerank-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Combine user questions with multiple text segments for re-ranking, return the re-ranked text and corresponding ranking scores"}, {"id": "WidgetKeyTextToImage", "name": "Text-To-Image", "desc": "Analyze the text content to generate corresponding images", "group": "WidgetGroupAIModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Supported Type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Service", "datasource": "{\"kind\":\"<PERSON><PERSON>EL_KIND_MULTI\",\"subKind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "UseBase64", "name": "Base64 Encoding", "desc": "Whether the generated image is returned in base64 format, it needs to be enabled for remote and cross-space calls.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported Types: Sync-ImageGenResp: {\n  \"created\": 1742875750392,\n  \"data\": [\n    {\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"b64_json\": \"{b64_json}\",\n      \"revised_prompt\": \"prompt\"\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-ImageGenResp"]}}], "md_full_desc": "<!--WidgetKeyTextToImage-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Analyze text content to generate corresponding images\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [ImageGenResp](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    ImageGenResp:\n    {\n      \"created\": 1742875750392,\n      \"data\": [\n        {\n          \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n          \"b64_json\": \"{b64_json}\",\n          \"revised_prompt\": \"prompt\"\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Text-to-Image - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyTextToImage-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Analyze text content to generate corresponding images"}, {"id": "WidgetKeyVectorModel", "name": "Text Vector", "desc": "Used to invoke the text vector model service, converting text into vectors", "group": "WidgetGroupAIModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Supported Types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Service", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_VECTOR\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Text converted into vectors, supported types: Sync-VectorInsert: {\n  \"file_name\": \"name\",\n  \"openai_embedding_req\": {\n    \"input\": null,\n    \"model\": \"\",\n    \"encoding_format\": \"\",\n    \"user\": \"\"\n  },\n  \"openai_embedding_resp\": {\n    \"object\": \"\",\n    \"data\": null,\n    \"model\": \"\"\n  }\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-VectorInsert"]}}], "md_full_desc": "<!--WidgetKeyVectorModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Used to invoke text vector model services, converting text to vectors\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    </pre>\n</details>\n\n**Output Format**: [VectorInsert](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    VectorInsert:\n    {\n      \"file_name\": \"name\",\n      \"openai_embedding_req\": {\n        \"input\": null,\n        \"model\": \"\",\n        \"encoding_format\": \"\",\n        \"user\": \"\"\n      },\n      \"openai_embedding_resp\": {\n        \"object\": \"\",\n        \"data\": null,\n        \"model\": \"\"\n      }\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Text Vector - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAIModel)", "md_summary_desc": "<!--WidgetKeyVectorModel-->\n**Operator Category**: Model Invocation\n**Operator Purpose**: Used to invoke the text vector model service, converting text into vectors"}]}, {"id": "WidgetGroupProcessKnowledge", "name": "Knowledge Processing", "desc": "To process documents, images, and other data in a structured manner", "sort_flag": 3, "widgets": [{"id": "WidgetGeneralSplitter", "name": "Long Text To Chunks", "desc": "Long Text to Chunks", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text/Json/Html", "desc": "Text to be split/json/html, supported types: Sync-String: \"text\", Sync-JsonString: \"{\\\"key\\\":\\\"value\\\"}, [\\\"value1\\\"], \\\"text\\\", ...\", Sync-HtmlString: \"\\u003c!DOCTYPE html\\u003e\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eHello, World!\\u003c/h1\\u003e\\u003cimg src=\\\"image.jpg\\\" alt=\\\"Example Image\\\" /\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\".", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String", "Sync-JsonString", "Sync-HtmlString"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chunkSize", "name": "Split Length", "desc": "Split length, value range 50-500. If the main body of the document exceeds the set [maximum length], then a segment of [maximum length] is extracted as a new document, and then it traces back [document overlap] characters, continues to check forward, until the end of the document.", "default_value": "500", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chunkOverlap", "name": "Segment Maximum Overlap", "desc": "The length of the text overlap between the current shard and the previous shard, with a value range of 0-50", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SpliterSelected", "name": "Select Split Type", "desc": "Select the split type, please keep consistent with the input", "datasource": "html@@html split, character@@text split, recursive@@recursive text split, json@@json split", "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "separators", "name": "Divider", "desc": "Delimiter, multiple can be set, and the text will be split when the selected delimiter is encountered. When using a recursive splitting strategy, delimiters that appear earlier have higher priority.", "precondition": "[{\"any\":\"SpliterSelected==character,SpliterSelected==recursive,\"}]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "splitHeaders", "name": "Split Head Identifier", "desc": "Split head tag, for example <h1>", "precondition": "[{\"both\":\"SpliterSelected==html\"}]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Split text paragraphs, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetGeneralSplitter-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Long Text to Chunks\n**Input Format**: [String](doc:///doc/index.html#_Transmission_Type), [JsonString](doc:///doc/index.html#_Transmission_Type), [HtmlString](doc:///doc/index.html#_Transmission_Type)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    JsonString:\n    \"{\\\"key\\\":\\\"value\\\"}, [\\\"value1\\\"], \\\"text\\\", ...\"\n    \n    HtmlString:\n    \"\\u003c!DOCTYPE html\\u003e\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eHello, World!\\u003c/h1\\u003e\\u003cimg src=\\\"image.jpg\\\" alt=\\\"Example Image\\\" /\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\"\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///doc/index.html#_Transmission_Type)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///doc/index.html#_Transmission_Method)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///doc/index.html#_Transmission_Method)(Synchronous Transmission Method)\n**More Information**: [Long Text to Chunks - Operator Details Document](doc:///doc/index.html#_Knowledge_Processing)", "md_summary_desc": "<!--WidgetGeneralSplitter-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Long Text to Chunks"}, {"id": "WidgetKeyMergeParagraph", "name": "Slice To Long Text", "desc": "Chunks to Text", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input", "desc": "Text segments to be merged, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Custom Code", "desc": "Customize the code logic according to the requirements", "default_value": "\n{{ $newline := \"\\n\" }}\n{{ range $index,$chunk := . }}\n{{ $chunk.content}}{{ $newline }}\n{{ end}}\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Merged text paragraph, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyMergeParagraph-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Chunks to Text\n**Input Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Slice to Long Text - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessKnowledge)", "md_summary_desc": "<!--WidgetKeyMergeParagraph-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Chunks to Text"}, {"id": "WidgetKeyParagraphAggregator", "name": "Parse Element To Slice", "desc": "Elements to Chunks", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input", "desc": "Elements to be aggregated, supported types: Sync-ElementsV2: {\n  \"duration\": 0.8,\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-ElementsV2"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chunkStrategy", "name": "Segmentation Strategy", "desc": "The strategy for segmenting into []pb.Chunk is currently only supported for basic.", "default_value": "basic", "datasource": "basic@@basic", "required": true, "disabled": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chunkSize", "name": "Segment Length", "desc": "Segment length into []pb.Chunk, default 500", "default_value": "500", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "merge", "name": "Whether To Merge", "desc": "Whether to merge into the specified 'segment length' and return the result", "default_value": "true", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "overlap", "name": "Segment Maximum Overlap", "desc": "Segment Maximum Overlap", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "type", "name": "Segmentation Method", "desc": "Segmenting into []pb.Chunk, when selecting character, there is no priority (default), when selecting recursive, the separators closer to the front are used with higher priority.", "default_value": "CHARACTER", "datasource": "CHARACTER@@character,RECURSIVE@@recursive", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "separators", "name": "Divider", "desc": "Multiple selection delimiter, whether to split according to priority depends on the 'segmentation method'", "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Merged several text paragraphs, supported types: Sync-LoadChunk: {\n  \"chunks\": [\n    {\n      \"id\": \"id\",\n      \"content\": \"content\",\n      \"element_ids\": [\n        \"id1\",\n        \"id2\"\n      ]\n    }\n  ],\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-LoadChunk"]}}], "md_full_desc": "<!--WidgetKeyParagraphAggregator-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Elements to Chunks\n**Input Format**: [ElementsV2](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    ElementsV2:\n    {\n      \"duration\": 0.8,\n      \"elements\": [\n        {\n          \"element_id\": \"id\",\n          \"text\": \"text\"\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Output Format**: [LoadChunk](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    LoadChunk:\n    {\n      \"chunks\": [\n        {\n          \"id\": \"id\",\n          \"content\": \"content\",\n          \"element_ids\": [\n            \"id1\",\n            \"id2\"\n          ]\n        }\n      ],\n      \"elements\": [\n        {\n          \"element_id\": \"id\",\n          \"text\": \"text\"\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Parse Elements to Chunks - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessKnowledge)", "md_summary_desc": "<!--WidgetKeyParagraphAggregator-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Elements to Chunks"}, {"id": "WidgetKeyTextParse", "name": "Standard Document Parsing", "desc": "Use the parsing engine provided by the platform to convert the file into a DocElement structured JSON output (see the usage documentation for the DocElement specification)", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "File", "name": "File", "desc": "Supported type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"content\"\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "forcePartition", "name": "Forced Parsing", "desc": "When forced parsing is enabled, the parsing service will ignore the cache, reducing parsing efficiency.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "strategy", "name": "Parse Strategy", "desc": "File parsing strategy, default is automatic strategy", "default_value": "auto", "datasource": "auto@@automatic strategy, hi_res@@high resolution strategy, fast@@plain text strategy", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AdvancedOptions", "name": "Advanced Options", "desc": "Multiple selection, control the behavior of text parsing", "datasource": "Save2MD@@保留markdown结果, Figure@@保留图片, Table@@保留表格, ChemicalFormula@@识别化学公式, MathFormula@@识别数学公式\n\nTranslates to:\n\nSave2MD@@Keep markdown result, Figure@@Keep image, Table@@Keep table, ChemicalFormula@@Identify chemical formula, MathFormula@@Identify math formula", "precondition": "[{\"both\":\"strategy==hi_res\"}]", "multiple": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "topK", "name": "TopK", "desc": "Limit on the number of return items for text parsing", "number_range": {"min": 1, "max": 100, "step": 1}, "default_value": "1", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SavePath", "name": "Save Path", "desc": "The storage address for saving text parsing information, for example sfs:///tenants/dev-assets/projs/assets/", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "json", "name": "Whether To Return In Json Format", "desc": "Whether to return JSON format, default is false (returns plain text (string) type), check to return a list of document elements ([]*pb.DocElement)", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "separator", "name": "Text Concatenation Character", "desc": "The character used for concatenating text, default is '\\n'", "precondition": "[{\"both\":\"json==false\"}]", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Parsed text, output different types according to the configuration, supported types: Sync-String: \"text\", Sync-ElementsV2: {\n  \"duration\": 0.8,\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-ElementsV2"]}}], "md_full_desc": "<!--WidgetKeyTextParse-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Use the parsing engine provided by the platform to convert files into a json output of DocElement structure (see the usage documentation for DocElement specifications)\n**Input Format**: [SFSFile](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType), [ElementsV2](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    ElementsV2:\n    {\n      \"duration\": 0.8,\n      \"elements\": [\n        {\n          \"element_id\": \"id\",\n          \"text\": \"text\"\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Standard Document Parsing - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupProcessKnowledge)", "md_summary_desc": "<!--WidgetKeyTextParse-->\n**Operator Category**: Knowledge Processing\n**Operator Purpose**: Use the parsing engine provided by the platform to convert files into json output with a DocElement structure (see the usage documentation for the DocElement specification)"}]}, {"id": "WidgetGroupVD", "name": "Knowledge Base", "desc": "Knowledge Base", "sort_flag": 4, "widgets": [{"id": "WidgetKeyFullTextInsert", "name": "Full Text Write In", "desc": "Insert text into the Scope database", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Input Text", "desc": "Text content that needs to be inserted into the database, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DataSourceConnection", "name": "Data Source", "desc": "Select a scope/es data source from the \"Space/Operation Space/Data Source Management\" list", "datasource": "name,id@/applet/widgets/datasource?key=ScopeDataSource", "required": true, "type": "TYPE_SELECTOR_DYNAMIC", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "FullTextIndex", "name": "Index Name", "desc": "Index Name", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output result, supported type: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetKeyFullTextInsert-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Insert text into the Scope database\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Full Text Write - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyFullTextInsert-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Insert text into the Scope database"}, {"id": "WidgetKeyFullTextSearch", "name": "Full Text Search", "desc": "Performing full-text search in the Scope database", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Input Text", "desc": "Text content that needs to be retrieved from the database, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "FullTextTopK", "name": "TopK", "desc": "The maximum number of items returned", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DataSourceConnection", "name": "Data Source", "desc": "Select a scope/es data source from the \"Space/Operation Space/Data Source Management\" list", "datasource": "name,id@/applet/widgets/datasource?key=ScopeDataSource", "required": true, "type": "TYPE_SELECTOR_DYNAMIC", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "FullTextIndex", "name": "Index Name", "desc": "Index Name", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output result, supported type: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetKeyFullTextSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Perform full-text search in the Scope database\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Full-Text Search - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyFullTextSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Perform full-text search in the Scope database"}, {"id": "WidgetKeyInternetSearch", "name": "Internet Search", "desc": "Use a search engine to search for the input text", "group": "WidgetGroupVD", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text", "desc": "Text content that requires online search, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Enable", "name": "Whether To Enable", "desc": "Whether to enable the internet search operator", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Engine", "name": "Search Engine", "desc": "Optional search engine, default is Bing", "default_value": "BingSearch", "datasource": "BingSearch@@Bing Search, SearXNGSearch@@SearXNG Search", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ParseUrl", "name": "Parse Web Page", "desc": "Whether to parse the detailed content of web pages obtained from the search engine, default is not to parse.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "desc": "Search results, supported type: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}], "md_full_desc": "<!--WidgetKeyInternetSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Use a search engine to search for the input text\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [InternetCitations](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    InternetCitations:\n    [\n      {\n        \"citation_type\": \"internet_search\",\n        \"content\": \"content\",\n        \"internet_search_details\": {\n          \"title\": \"title\",\n          \"snippet\": \"snippet\",\n          \"url\": \"url\"\n        }\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Details**: [Internet Search - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyInternetSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Use a search engine to search for the input text"}, {"id": "WidgetKeyQaSearch", "name": "Standard Question And Answer Retrieval", "desc": "Search for similar questions in the standard Q&A knowledge base, output the preset answer", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Question", "desc": "The question to be searched, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ScoreThreshold", "name": "Match Degree Threshold", "desc": "The matching threshold for the standard Q&A knowledge base, when the similarity of the most similar question retrieved is below this threshold, an empty string will be output. It is recommended to set the value no less than 0.6.", "number_range": {"max": 1, "step": 0.01}, "default_value": "0.800000", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AnswerField", "name": "Presets Answer Fields", "desc": "The search result is a dictionary, please choose which field to use as the preset answer, the default is \"Answer\"", "default_value": "Answer", "hidden": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Select model, used for re-ranking and filtering the recalled results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Knowledge", "name": "Standard Question And Answer Knowledge Base", "desc": "Select the knowledge base for standard Q&A scenarios, retrieve the most similar question from the knowledge base, and output the content corresponding to the preset answer field.", "datasource": "is_published_selector=true&scene_type_selector=SceneType_STANDARD", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "The preset answer for the most similar question will output empty when not retrieved, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyQaSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Search for similar questions in a standard Q&A knowledge base, output predefined answers\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Standard Q&A Search - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyQaSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Search for similar questions in the standard Q&A knowledge base, output preset answers"}, {"id": "WidgetKeyTextKnowledgeInsert", "name": "Knowledge Base Writing", "desc": "Write the text into the corresponding knowledge base", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Input", "desc": "Text to be written into the knowledge base, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "Knowledge Base Information", "desc": "Knowledge Base Information", "datasource": "is_published_selector=true", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Text insertion result, string type", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyTextKnowledgeInsert-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Write text to the corresponding knowledge base\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Knowledge Base Writing - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyTextKnowledgeInsert-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Write text to the corresponding knowledge base"}, {"id": "WidgetKeyTextKnowledgeSearch", "name": "Knowledge Base Search", "desc": "Retrieve and sort the results from the knowledge base based on the input text, and then filter them.", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Input", "desc": "Retrieve issues, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "The final number of retained search results", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "The minimum threshold requirement for retaining search results", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "Cross Knowledge Base Search", "desc": "When set to cross-knowledge base search, the rerank model needs to be configured to improve search effectiveness.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "Knowledge Base Information", "desc": "Knowledge Base Information", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "Knowledge Base Information", "desc": "Restricted search knowledge base and document scope", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Select model, used for re-ranking and filtering search results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Recalled text paragraphs from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}], "md_full_desc": "<!--WidgetKeyTextKnowledgeSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Retrieve information from the knowledge base based on the input text, and sort and filter the results\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [Chunks](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Knowledge Base Search - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyTextKnowledgeSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Based on the input text, search from the knowledge base, and sort and filter the results"}, {"id": "WidgetKeyVectorDBInputs", "name": "Vector Library Writing", "desc": "Used for adding vector index and corresponding text", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Insert Data", "desc": "Insert data, supported type: Sync-VectorInsert: {\n  \"file_name\": \"name\",\n  \"openai_embedding_req\": {\n    \"input\": null,\n    \"model\": \"\",\n    \"encoding_format\": \"\",\n    \"user\": \"\"\n  },\n  \"openai_embedding_resp\": {\n    \"object\": \"\",\n    \"data\": null,\n    \"model\": \"\"\n  }\n}.", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-VectorInsert"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DataSourceConnection", "name": "Data Source", "desc": "Select a hippo/milvus data source from the \"Space/Operations Space/Data Source Management\" list.", "datasource": "name,id@/applet/widgets/datasource?key=HippoDataSource", "required": true, "type": "TYPE_SELECTOR_DYNAMIC", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Table", "name": "Table", "desc": "Vector database table name", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Dimension", "name": "Dimension", "desc": "Vector dimension", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output result, supported type: Sync-VecInsertRes: {\n  \"records\": 10,\n  \"dimension\": 1024,\n  \"bodySize\": 1024,\n  \"consumed\": \"0.1s\"\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-VecInsertRes"]}}], "md_full_desc": "<!--WidgetKeyVectorDBInputs-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Used for adding vector indices and corresponding texts\n**Input Format**: [VectorInsert](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    VectorInsert:\n    {\n      \"file_name\": \"name\",\n      \"openai_embedding_req\": {\n        \"input\": null,\n        \"model\": \"\",\n        \"encoding_format\": \"\",\n        \"user\": \"\"\n      },\n      \"openai_embedding_resp\": {\n        \"object\": \"\",\n        \"data\": null,\n        \"model\": \"\"\n      }\n    }\n    </pre>\n</details>\n\n**Output Format**: [VecInsertRes](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    VecInsertRes:\n    {\n      \"records\": 10,\n      \"dimension\": 1024,\n      \"bodySize\": 1024,\n      \"consumed\": \"0.1s\"\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Vector Library Write - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyVectorDBInputs-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Used for adding vector indexes and corresponding text"}, {"id": "WidgetKeyVectorDBSearch", "name": "Vector Library Search", "desc": "Used for text retrieval based on vector similarity", "group": "WidgetGroupVD", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "SearchText", "name": "Text Vector", "desc": "Supported Type: Sync-VectorInsert: {\n  \"file_name\": \"name\",\n  \"openai_embedding_req\": {\n    \"input\": null,\n    \"model\": \"\",\n    \"encoding_format\": \"\",\n    \"user\": \"\"\n  },\n  \"openai_embedding_resp\": {\n    \"object\": \"\",\n    \"data\": null,\n    \"model\": \"\"\n  }\n}", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-VectorInsert"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DataSourceConnection", "name": "Data Source", "desc": "Select a hippo/milvus data source from the \"Space/Operations Space/Data Source Management\" list.", "datasource": "name,id@/applet/widgets/datasource?key=HippoDataSource", "required": true, "type": "TYPE_SELECTOR_DYNAMIC", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Table", "name": "Table", "desc": "Vector database table name", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "TopK", "name": "TopK", "desc": "Limit on the number of paragraphs recalled by the vector library", "number_range": {"min": 1, "max": 100, "step": 1}, "default_value": "5", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "<PERSON><PERSON><PERSON>", "name": "Minimum Matching Degree", "desc": "When recalling paragraphs, paragraphs with a score lower than this value will be considered irrelevant text and will not be returned.", "number_range": {"min": 0.05, "max": 1, "step": 0.05}, "default_value": "0.75", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "json", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output result, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyVectorDBSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Used for text retrieval based on vector similarity\n**Input Format**: [VectorInsert](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    VectorInsert:\n    {\n      \"file_name\": \"name\",\n      \"openai_embedding_req\": {\n        \"input\": null,\n        \"model\": \"\",\n        \"encoding_format\": \"\",\n        \"user\": \"\"\n      },\n      \"openai_embedding_resp\": {\n        \"object\": \"\",\n        \"data\": null,\n        \"model\": \"\"\n      }\n    }\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Vector Database Search - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupVD)", "md_summary_desc": "<!--WidgetKeyVectorDBSearch-->\n**Operator Category**: Knowledge Base\n**Operator Purpose**: Used for text retrieval based on vector similarity"}]}, {"id": "WidgetGroupAdvanced", "name": "Advanced", "desc": "Provide intelligent agents, custom operators, and other functions", "sort_flag": 5, "widgets": [{"id": "WidgetKeyAgent", "name": "Intelligent Agent", "desc": "On the basis of using large models, it can automatically utilize various tools.", "group": "WidgetGroupAdvanced", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Question", "desc": "Input questions to the agent, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Knowledge", "name": "Knowledge Base Content", "desc": "In the content recalled from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Internet", "name": "Internet Content", "desc": "Through the search engine, content retrieved from the Internet is supported. Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "History", "name": "Conversation History", "desc": "User question and model response history records, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "File", "name": "File", "desc": "Uploaded file content, supported type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"content\"\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_llm_model_svc_str", "name": "LLM Model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AgentMode", "name": "Intelligent Agent Pattern", "desc": "Select the reasoning mode of the agent, ReAct mode is a more general tool calling mode, FunctionCalling mode requires LLM to support FunctionCalling tool calls", "default_value": "react", "datasource": "react@@ReAct,function_calling@@FunctionCalling", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_enable_trace", "name": "Knowledge Traceability", "desc": "Knowledge Traceability, Distinguish the Sources of Knowledge References", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_rerank_model_svc_str", "name": "Traceability Model", "desc": "Traceback Model, reorder the reference sources based on the question", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"chain_enable_trace==true\"}]", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_prompt", "name": "Prompt Word", "desc": "Setting LLM's instructions or roles", "type": "TYPE_AGENT_INSTRUCTION", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_api_collections_str", "name": "Plugin API", "desc": "Allowing the agent to call plugin APIs, thereby extending the agent's capabilities and applications", "type": "TYPE_AGENT_SKILL_API_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_knowledge_base_desc_str", "name": "Knowledge Base", "desc": "After adding the knowledge base, the intelligent agent can reference the content of the knowledge to answer user questions.", "datasource": "is_published_selector=true", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_system_services_str", "name": "Platform Services", "desc": "Allow the agent to invoke model services and application services deployed through Sophon LLMOps", "type": "TYPE_AGENT_SKILL_SERVICE_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_created_by_chain", "name": "Marked As Canvas Method Creation", "desc": "Marked as created in canvas mode", "default_value": "true", "hidden": true, "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "LLM Agent provides the final response by integrating multi-round Q&A and results from various tool invocations, supported type: Stream-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Stream-String"]}}], "md_full_desc": "<!--WidgetKeyAgent-->\n**Operator Category**: Advanced\n**Operator Purpose**: On the basis of using large models, it can automatically utilize various tools\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType), [InternetCitations](doc:///help_document/en/index.html#DataType), [SFSFile](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    \n    InternetCitations:\n    [\n      {\n        \"citation_type\": \"internet_search\",\n        \"content\": \"content\",\n        \"internet_search_details\": {\n          \"title\": \"title\",\n          \"snippet\": \"snippet\",\n          \"url\": \"url\"\n        }\n      }\n    ]\n    \n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Stream](doc:///help_document/en/index.html#ModeType)(Streaming Transmission Method)\n**Details**: [Agent-Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAdvanced)", "md_summary_desc": "<!--WidgetKeyAgent-->\n**Operator Category**: Advanced\n**Operator Purpose**: On the basis of using large models, it can automatically utilize various tools"}, {"id": "WidgetKeyCodeInstance", "name": "Custom Operator (Code Development)", "desc": "Used to open the code example for real-time editing and debugging. It is recommended to solidify the code example into a custom operator for practical use.", "group": "WidgetGroupAdvanced", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input Data", "desc": "Supported type: Sync-Any: \"{}、text、[{}]...\". \n\nTranslates to: Supported type: Sync-Any: \"{}, text, [{}...]\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "CodeInstance", "name": "Code Examples", "desc": "Code example, click to view or edit code", "required": true, "type": "TYPE_CODE_INSTANCE_ID", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported types: Any-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation marks such as \"、\" have been kept as in the original text. If you prefer to use standard English punctuation, it would be: Supported types: Any-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyCodeInstance-->\n**Operator Category**: Advanced\n**Operator Purpose**: Used to open code instance for real-time editing and debugging. It is recommended to solidify the code instance into a custom operator for practical application.\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [Custom Operator (Code Development) - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupAdvanced)", "md_summary_desc": "<!--WidgetKeyCodeInstance-->\n**Operator Category**: Advanced\n**Operator Purpose**: Used to open code instance for real-time editing and debugging. It is recommended to solidify the code instance into a custom operator when used in actual applications."}, {"id": "WidgetKeyCustomWidget", "name": "Custom Operator (Published)", "desc": "Used to invoke the published custom operator", "group": "WidgetGroupAdvanced", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input", "desc": "Input", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true, "md_full_desc": "<!--WidgetKeyCustomWidget-->\n**Operator Category**: Advanced\n**Operator Purpose**: Used to invoke the published custom operator\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**Detailed Information**: [Custom Operator (Published)-Operator Details Document](doc:///doc/index.html#_Advanced)", "md_summary_desc": "<!--WidgetKeyCustomWidget-->\n**Operator Category**: Advanced\n**Operator Purpose**: Used to invoke the published custom operator"}]}, {"id": "WidgetGroupControlFlow", "name": "Control Flow", "desc": "Use conditional judgments, loop jumps, etc., to control the data flow of the application chain", "sort_flag": 6, "widgets": [{"id": "WidgetKeyAggregateStream", "name": "Streaming Aggregation", "desc": "Aggregate the streaming output data, combine the streaming data output from upstream into a complete non-streaming data, and pass it to downstream.", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Streaming Data", "desc": "Stream data that needs to be aggregated, supported types: Any-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation inside the supported types has been kept as in the original text, including the use of full-width characters. If you prefer to use half-width characters, it would be: \"{}, text, [{}...]\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Aggregated complete non-streaming data, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nAggregated complete non-streaming data, supported types: Sync-Any: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}], "md_full_desc": "<!--WidgetKeyAggregateStream-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Aggregate streaming output data, consolidating the upstream streaming data into a complete non-streaming data, and passing it to the downstream\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Streaming Aggregation - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyAggregateStream-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Aggregate stream output data, combine the stream data output from upstream into a complete non-stream data, and pass it to downstream"}, {"id": "WidgetKeyConditionJudge", "name": "Conditional Judgment", "desc": "Fork the data stream into if branches and else branches, determining the branch for data flow based on the input conditions.", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input Data", "desc": "Data to be evaluated for conditional judgment, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation inside the supported types has been kept as in the original text, which includes both Chinese and English punctuation. If you prefer to use only English punctuation, it would be: Sync-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "The output endpoint when the condition is met, outputs the input data as is, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation within the supported types has been kept as in the original text, including the Chinese comma (、). If you prefer to use standard English punctuation, it would be: Sync-Any: \"{}\", \"text\", \"[{}]\"...", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Judgment Conditions", "desc": "Use the `input` variable to store data passed from upstream.\nAssuming the data stored in `input` is\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\nYou can use the following syntax to express conditional statements, for more complex conditions please refer to the Jsonnet syntax\n`input.number == 123 && input.number > 100 || input.dict.k == \"v\"`", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "Output endpoint when the condition is not met, outputs the input data as is, supported types: Sync-Any: \"{}、text、[{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}], "md_full_desc": "<!--WidgetKeyConditionJudge-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Forks the data flow into if and else branches, determining the branch of the data flow based on the input condition\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Condition Judgment - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyConditionJudge-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Forks the data flow into if and else branches, determining the branch of the data flow based on the input condition"}, {"id": "WidgetKeyGoTo", "name": "Loop Jump", "desc": "Used to change the direction of data flow, redirecting data transmission to a previous operator to implement a loop", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input Data", "desc": "Data to be redirected, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation inside the supported types has been kept as in the original text, including the Chinese comma (、). If you prefer to use standard English punctuation, it would be: \nData to be redirected, supported types: Sync-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "maxLoopRounds", "name": "Maximum Number Of Rounds", "desc": "Maximum number of iterations, must be a positive integer", "number_range": {"min": 1, "max": 100}, "default_value": "5", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output the input data as is, supported types: Sync-Any: \"{}、text、[{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}], "md_full_desc": "<!--WidgetKeyGoTo-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Used to change the direction of data flow, transferring data to a previous operator to implement loops\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Loop Jump - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyGoTo-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Used to change the direction of data flow, transferring data to a previous operator to implement loops"}, {"id": "WidgetKeyInputAggregation", "name": "Data Merge", "desc": "Compose multiple upstream inputs into a JSON output in KV format (output will only be triggered when all data is in place)", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input1", "name": "Input1", "desc": "Supported type: Sync-Any: \"{}、text、[{}]...\". \n\nTranslates to: Supported type: Sync-Any: \"{}, text, [{}...]\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input2", "name": "Input2", "desc": "Supported types: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation marks \"、\" and \"…\" have been kept as in the original text to maintain the format. However, in English, it would typically be written as: Supported types: Sync-Any: \"{}, text, [{}], ...\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "<PERSON><PERSON>", "name": "Input Data Cache", "desc": "When the data merge operator executes in a loop, if only one input port participates in the loop and the other input ports need to use the previous data, you can enable this switch to prevent the data merge operator from being blocked due to no data on the other input ports.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "desc": "Supported type: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}], "dynamic_end_point": true, "md_full_desc": "<!--WidgetKeyInputAggregation-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Aggregate multiple upstream inputs into a JSON output in KV format (output is triggered only when all data is available)\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [map[string]any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    map[string]any:\n    {\n      \"k1\": \"v1\",\n      \"k2\": 2\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Data Aggregation - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyInputAggregation-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Aggregate multiple upstream inputs into a JSON output in KV format (output is triggered only when all data is available)"}, {"id": "WidgetKeyQuestionClassifier", "name": "Intent Recognition (Input Classification)", "desc": "Classify the user's input questions, control the data flow to different categories", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Unclassified Questions", "desc": "To be classified question text, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for issue classification", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Category-1", "name": "Category 1", "desc": "Question Category", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output-1", "name": "Category 1", "desc": "Output endpoint that conforms to Category 1, outputs the input text as is, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Category-default", "name": "Default Category", "desc": "Default execution branch when no classification issue is matched or classification fails", "default_value": "unmatched with the category above", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output-default", "name": "Default Category", "desc": "Default execution branch when no classification issue is matched or classification fails, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "dynamic_end_point": true, "md_full_desc": "<!--WidgetKeyQuestionClassifier-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Classify user input questions, control data flow to different categories\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [Intent Recognition (Input Classification) - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyQuestionClassifier-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Classify user input questions, control data flow to different categories"}, {"id": "WidgetKeyUnion", "name": "Data Confluence", "desc": "Form a queue from multiple upstream inputs and output them serially.", "group": "WidgetGroupControlFlow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Upstream Data, Any Type", "desc": "The upstream data to be merged should ensure that only one branch has data flowing into this operator. Otherwise, please use the data merging operator. Supported types: Any-Any: \"{}、text、[{}...]\". \n\nNote: The punctuation inside the supported types has been kept as in the original text. For better readability in English, it is recommended to use standard English punctuation: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output the upstream data as it is, supported types: Any-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation and format inside the supported types are kept as in the original text.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyUnion-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Serializes multiple upstream inputs into a queue for sequential output\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [Data Confluence - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupControlFlow)", "md_summary_desc": "<!--WidgetKeyUnion-->\n**Operator Category**: Control Flow\n**Operator Purpose**: Serializes multiple upstream inputs into a queue for output"}]}, {"id": "WidgetGroupChainTool", "name": "Tool Invocation", "desc": "Calling HTTP, parameter extractor, custom tools, etc.", "sort_flag": 7, "widgets": [{"id": "WidgetKeyApiCall", "name": "General <PERSON>tt<PERSON> Call", "desc": "Can invoke all HTTP API services, currently only supports POST requests", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "APIPath", "name": "Request Address", "desc": "URL address, for example http://example.com/api/v1", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Header", "name": "Request Header", "desc": "Request headers, such as Content-Type = application/json or Authorization = Bearer xxx", "multiple": true, "type": "TYPE_KVITEM", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "RequestBody", "name": "Request Body", "desc": "The request body of HTTP should be valid HTTP request data such as dictionaries, lists, numbers, strings, etc., supported types: Sync-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation and format inside the supported types string are kept as in the original text. If standardization is required, it should be: Sync-Any: \"{}, text, [{}], ...\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "The data in the HTTP request response may be a dictionary, list, number, string, or any other valid HTTP response data. Supported types: Any-Any: \"{}、text、[{}]...\". \n\nNote: The punctuation marks \"、\" are kept as in the original text. If you prefer to use standard English punctuation, it would be: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyApiCall-->\n**Operator Category**: Tool Call\n**Operator Purpose**: Can invoke all HTTP API services, currently only supports POST requests\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [General HTTP Invocation - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupChainTool)", "md_summary_desc": "<!--WidgetKeyApiCall-->\n**Operator Category**: Tool Call\n**Operator Purpose**: Can invoke all HTTP API services, currently only supports POST requests"}, {"id": "WidgetKeyExternalAPICall", "name": "Remote Model Invocation", "desc": "Use the same as \"General HTTP Call\". To reduce the need for users to repeatedly enter the API address and request headers when using the same service multiple times, the remote model is used to pre-fill the API address and request headers.", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input", "desc": "Input", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyExternalAPICall-->\n**Operator Category**: Tool Call\n**Operator Purpose**: Same as \"General HTTP Call\", to reduce the need for users to repeatedly fill in API addresses and request headers when using the same service multiple times, the remote model pre-fills the API address and request headers\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transfer Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transfer Method)\n**Detailed Information**: [Remote Model Call - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupChainTool)", "md_summary_desc": "<!--WidgetKeyExternalAPICall-->\n**Operator Category**: Tool Invocation\n**Operator Purpose**: Same as \"General HTTP Call\", to reduce the need for users to repeatedly fill in API addresses and request headers when using the same service multiple times, the remote model is used to pre-fill the API address and request headers."}, {"id": "WidgetKeyParameterExtractor", "name": "Calling Parameter Extraction", "desc": "Extract the specified parameters from the user input", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input Text", "desc": "Input text that requires parameter extraction, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for parameter extraction", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "ParamDef", "name": "Parameter Definition", "desc": "Define the parameters that need to be extracted", "required": true, "type": "TYPE_PARAMETER_EXTRACTOR", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "Extraction Result", "desc": "Extracted parameters, supported type: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}], "md_full_desc": "<!--WidgetKeyParameterExtractor-->\n**Operator Category**: Tool Invocation\n**Operator Purpose**: Extract specified parameters from user input\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [map[string]any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    map[string]any:\n    {\n      \"k1\": \"v1\",\n      \"k2\": 2\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [Parameter Extraction Operator - Detailed Documentation](doc:///help_document/en/index.html#WidgetGroupChainTool)", "md_summary_desc": "<!--WidgetKeyParameterExtractor-->\n**Operator Category**: Tool Invocation\n**Operator Purpose**: Extract specified parameters from user input"}, {"id": "Widget<PERSON>ey<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Application Chain Embedding", "desc": "An application chain fusion technology, where sub-chains are replicated as independent instances and integrated into the parent chain, with multiple embedded sub-chains being independent of each other.", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input", "desc": "Input", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true, "md_full_desc": "<!--WidgetK<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-->\n**Operator Category**: Tool Invocation\n**Operator Purpose**: A technology that integrates sub-chains by copying them as independent instances into the parent chain, ensuring that sub-chains embedded multiple times are mutually independent\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**More Information**: [Application Chain Embedding - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupChainTool)", "md_summary_desc": "<!--Widget<PERSON><PERSON>SubChain-->\n**Operator Category**: Tool Invocation\n**Operator Purpose**: A technology that applies chain fusion, replicating a subchain as an independent instance and integrating it into the parent chain, with multiple embedded subchains being independent of each other"}, {"id": "WidgetKeyToolCall", "name": "Application Plugin Invocation", "desc": "Invoke the published application plugin", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ToolDescriber", "name": "Tool Information", "desc": "Specific description information of the tool", "type": "TYPE_TOOL_CALL", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Params", "name": "Tool Parameters", "desc": "The parameters used when calling the tool, supported type: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Tool invocation result, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKey<PERSON>oolCall-->\n**Operator Category**: Tool Call\n**Operator Purpose**: Call a published application plugin\n**Input Format**: [map[string]any](doc:///doc/index.html#_Transmission_Type)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    map[string]any:\n    {\n      \"k1\": \"v1\",\n      \"k2\": 2\n    }\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///doc/index.html#_Transmission_Type)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///doc/index.html#_Transmission_Method)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///doc/index.html#_Transmission_Method)(Synchronous Transmission Method)\n**More Information**: [Application Plugin Call - Operator Details Document](doc:///doc/index.html#_Tool_Call)", "md_summary_desc": "<!--WidgetKeyToolCall-->\n**Operator Category**: Tool Call\n**Operator Purpose**: Invoke published application plugins"}]}, {"id": "WidgetGroupCodeTool", "name": "Glue Code", "desc": "Write custom code to connect and integrate different components and services", "sort_flag": 8, "widgets": [{"id": "WidgetKeyCustomPrompt", "name": "GoTemplate Code", "desc": "Can be used for flexible definition of data concatenation, data dictionary processing, etc.", "group": "WidgetGroupCodeTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input Data", "desc": "Data that needs to be processed using Go template code, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nThe supported types should be: Sync-Any: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Go Template Code", "desc": "Go template code, click to view or edit the code", "default_value": "\n{{/* \nSuppose the data from upstream widget is\n{\n\t\"string\": \"str\",\n\t\"number\": 123,\n\t\"list\": [1, 2, 3],\n\t\"dict\": { \"k\": \"v\" }\n}\n\n In Go Template \".\" refers to the data forwarded by the upstream. We have the following examples: \n\n1. Directly use data from the upstream as output\n{{.}}\n\n2. Retrieve a dictionary value of a specific field and use it as output：\n{{.string}} From some specific field\n{{.dict.k}} From some field declared inside a field\n\n3.Iteration\n{{range .list}}\n{{.}}\n{{end}}\n*/}}\n\n{{.}}\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "go template code execution result, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyCustomPrompt-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can be used for flexible data concatenation, data dictionary processing, etc.\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**More Information**: [GoTemplate Code - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupCodeTool)", "md_summary_desc": "<!--WidgetKeyCustomPrompt-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can be used for flexible definition of data concatenation, data dictionary processing, etc."}, {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet Code", "desc": "Can conveniently transform the structure of upstream output data, such as extracting the value of a certain field or changing field names, etc.", "group": "WidgetGroupCodeTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input Data", "desc": "Data that needs to be processed using jsonnet code, supported types: Any-Any: \"{}、text、[{}]...\". \n\nThe supported types should be: Any-Any: \"{}, text, [{}]...\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Jsonnet Code", "desc": "jsonnet code, click to view or edit the code", "default_value": "\n/*jsonnet\nUse variable 'input' to store the data passed from upstream\n Assume the data stored in 'input' is\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\nExample usages\n1.Output the original data as-is\ninput\n2.Retrieve value from a specific field：\ninput.list\n3.Reconstruct it in new data structure\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "The execution result of jsonnet code, supported types: Any-Any: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyJsonnetWidget-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can conveniently transform the structure of upstream output data, such as extracting the value of a certain field or changing field names, etc.\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Detailed Information**: [Jsonnet Code - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupCodeTool)", "md_summary_desc": "<!--WidgetKeyJsonnetWidget-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can conveniently transform the structure of upstream output data, such as extracting the value of a certain field or changing field names, etc."}, {"id": "WidgetKeyPythonWidget", "name": "Python Code", "desc": "You can write Python code to accomplish complex business logic, with pre-installed dependencies such as requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input Data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"{}、text、[{}]...\". \n\nThe supported types should be: Any-Any: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Python Code", "desc": "Python code, click to view or edit the code", "default_value": "\n# python version 3.11\n\ndef handler(data):\n    \"\"\"\n    Ensure the function to be executed is named 'handler', accepts exactly 1 parameter that receives output data from the upstream widget and returns the processed data \n   \n    params:\n    data (any): Can be any parameter name and type\n\n    return:\n    any: Can return any type\n\n    \"\"\"\n    # TODO Process data\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "In Python code, the data returned by the handler function supports types: Any-Any: \"{}、text、[{}]...\".\n\nThe data returned by the handler function in Python code supports the following types: Any-Any: \"{}, text, [{}...]\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "md_full_desc": "<!--WidgetKeyPythonWidget-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can write Python code to accomplish complex business logic, with pre-installed dependencies such as requests, pandas, matplotlib\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any Transmission Method)\n**More Information**: [Python Code - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupCodeTool)", "md_summary_desc": "<!--WidgetKeyPythonWidget-->\n**Operator Category**: Glue Code\n**Operator Purpose**: Can write Python code to complete complex business logic, with pre-installed dependencies such as requests, pandas, matplotlib"}]}, {"id": "WidgetGroupOutput", "name": "Output", "desc": "Output processed data", "sort_flag": 9, "widgets": [{"id": "WidgetKeyChunksOutput", "name": "Knowledge Processing Output (Custom Strategy)", "desc": "Marked operator, representing the final output of the chain is pb.DocSvcLoadChunkRsp", "group": "WidgetGroupOutput", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Chunks", "name": "Chunks", "desc": "chunks text, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Elements", "name": "Elements", "desc": "elements text, supported types: Sync-Elements: [\n  {\n    \"element_id\": \"id\",\n    \"text\": \"text\"\n  }\n].", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-Elements"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Supported type: Sync-LoadChunk: {\n  \"chunks\": [\n    {\n      \"id\": \"id\",\n      \"content\": \"content\",\n      \"element_ids\": [\n        \"id1\",\n        \"id2\"\n      ]\n    }\n  ],\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}.", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-LoadChunk"]}}], "md_full_desc": "<!--WidgetKeyChunksOutput-->\n**Operator Category**: Output\n**Operator Purpose**: Marking operator, representing the final output of the chain as pb.DocSvcLoadChunkRsp\n**Input Format**: [String](doc:///help_document/en/index.html#DataType), [Strings](doc:///help_document/en/index.html#DataType), [Chunks](doc:///help_document/en/index.html#DataType), [Elements](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    \n    Strings:\n    [\n      \"text1\",\n      \"text2\"\n    ]\n    \n    Chunks:\n    [\n      {\n        \"id\": \"id\",\n        \"content\": \"content\",\n        \"element_ids\": [\n          \"id1\",\n          \"id2\"\n        ]\n      }\n    ]\n    \n    Elements:\n    [\n      {\n        \"element_id\": \"id\",\n        \"text\": \"text\"\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [LoadChunk](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    LoadChunk:\n    {\n      \"chunks\": [\n        {\n          \"id\": \"id\",\n          \"content\": \"content\",\n          \"element_ids\": [\n            \"id1\",\n            \"id2\"\n          ]\n        }\n      ],\n      \"elements\": [\n        {\n          \"element_id\": \"id\",\n          \"text\": \"text\"\n        }\n      ]\n    }\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Detailed Information**: [Knowledge Processing Output (Custom Strategy)-Operator Details Document](doc:///help_document/en/index.html#WidgetGroupOutput)", "md_summary_desc": "<!--WidgetKeyChunksOutput-->\n**Operator Category**: Output\n**Operator Purpose**: Marking operator, representing the final output of the chain as pb.DocSvcLoadChunkRsp"}, {"id": "WidgetKeyFileSave", "name": "File Save", "desc": "Used to save the final result as a file and store it in the file system", "group": "WidgetGroupOutput", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "ContentInput", "name": "File", "desc": "File to be saved, supported types: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}, Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n], Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}, Sync-[]map[string]any: [\n  {\n    \"k1\": \"v1\",\n    \"k2\": 2\n  },\n  {\n    \"k3\": \"v3\",\n    \"k4\": 4\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile", "Sync-SFSFiles", "Sync-map[string]any", "Sync-[]map[string]any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AdvancedConfig", "name": "Advanced Configuration", "desc": "Whether to enable advanced configuration, when not enabled, files are saved to the root directory of the space by default, and overwriting existing files is allowed.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "FilePath", "name": "File Directory", "desc": "The directory to save the files, which will be created if it does not exist, for example testDir, testDir/temp", "precondition": "[{\"both\":\"AdvancedConfig==true\"}]", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowOverwrite", "name": "Allow Overwrite", "desc": "When saving a file, is overwriting an existing file allowed? When set to not allow overwriting, an error will be reported if the file already exists.", "default_value": "true", "precondition": "[{\"both\":\"AdvancedConfig==true\"}]", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "The saved file supports the following types: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}, Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n].", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile", "Sync-SFSFiles"]}}], "md_full_desc": "<!--WidgetKeyFileSave-->\n**Operator Category**: Output\n**Operator Purpose**: Used to save the final result as a file and store it in the file system\n**Input Format**: [SFSFile](doc:///help_document/en/index.html#DataType), [SFSFiles](doc:///help_document/en/index.html#DataType), [map[string]any](doc:///help_document/en/index.html#DataType), [[]map[string]any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    \n    SFSFiles:\n    [\n      {\n        \"name\": \"name\",\n        \"uid\": \"uid\",\n        \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n        \"content\": \"Y29udGVudA==\"\n      }\n    ]\n    \n    map[string]any:\n    {\n      \"k1\": \"v1\",\n      \"k2\": 2\n    }\n    \n    []map[string]any:\n    [\n      {\n        \"k1\": \"v1\",\n        \"k2\": 2\n      },\n      {\n        \"k3\": \"v3\",\n        \"k4\": 4\n      }\n    ]\n    </pre>\n</details>\n\n**Output Format**: [SFSFile](doc:///help_document/en/index.html#DataType), [SFSFiles](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    SFSFile:\n    {\n      \"name\": \"name\",\n      \"uid\": \"uid\",\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"content\": \"Y29udGVudA==\"\n    }\n    \n    SFSFiles:\n    [\n      {\n        \"name\": \"name\",\n        \"uid\": \"uid\",\n        \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n        \"content\": \"Y29udGVudA==\"\n      }\n    ]\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous Transmission Method)\n**Detailed Information**: [File Save - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupOutput)", "md_summary_desc": "<!--WidgetKeyFileSave-->\n**Operator Category**: Output\n**Operator Purpose**: Used to save the final result as a file and store it in the file system"}, {"id": "WidgetKeyTextOutput", "name": "Standard Response", "desc": "Replace the upstream output with the configured response script", "group": "WidgetGroupOutput", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Input", "desc": "Input data, supported types: Sync-Any: \"{}、text、[{}]...\". \n\nInput data, supported types: Sync-Any: \"{}, text, [{}]...\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StdText", "name": "Script Configuration", "desc": "Response scripts used to replace upstream output", "required": true, "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output configuration of the script, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}], "md_full_desc": "<!--WidgetKeyTextOutput-->\n**Operator Category**: Output\n**Operator Purpose**: Replace the upstream output with the configured response script\n**Input Format**: [Any](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    Any:\n    \"{}、text、[{}]...\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Output Type**: [Sync](doc:///help_document/en/index.html#ModeType)(Synchronous transmission method)\n**Detailed Information**: [Standard Response - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupOutput)", "md_summary_desc": "<!--WidgetKeyTextOutput-->\n**Operator Category**: Output\n**Operator Purpose**: Replace the upstream output with the configured response script"}, {"id": "WidgetKeyTextShow", "name": "Text Presentation", "desc": "Used to add custom prefixes and suffixes to the text output from upstream", "group": "WidgetGroupOutput", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text Input", "desc": "Text input, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Prefix", "name": "Prefix", "desc": "Prefix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Suffix", "name": "Suffix", "desc": "Suffix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "Output, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}], "md_full_desc": "<!--WidgetKeyTextShow-->\n**Operator Category**: Output\n**Operator Purpose**: Used to add custom prefixes and suffixes to the text output from upstream\n**Input Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Output Format**: [String](doc:///help_document/en/index.html#DataType)\n<details>\n    <summary>Example Data</summary>\n    <pre>\n    String:\n    \"text\"\n    </pre>\n</details>\n\n**Input Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**Output Type**: [Any](doc:///help_document/en/index.html#ModeType)(Any transmission method)\n**Detailed Information**: [Text Presentation - Operator Details Document](doc:///help_document/en/index.html#WidgetGroupOutput)", "md_summary_desc": "<!--WidgetKeyTextShow-->\n**Operator Category**: Output\n**Operator Purpose**: Used to add custom prefixes and suffixes to the text output from upstream"}]}]