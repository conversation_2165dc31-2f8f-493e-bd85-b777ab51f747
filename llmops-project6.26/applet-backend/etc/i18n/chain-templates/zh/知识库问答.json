{"id": "知识库问答.json", "name": "知识库问答", "desc": "显式的进行知识库召回，然后拼接为提示词，交由大模型进行问答", "template_group_key": "KnowledgeBaseQA", "template": {"nodes": [{"id": "84ec2172-a374-4500-8c77-62cdb647239a", "name": "文本输入", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "文本输入", "desc": "用于将输入的文本原样输出", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "文本输入", "desc": "文本输入,支持类型: Sync-String: \"text\"。", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"84ec2172-a374-4500-8c77-62cdb647239a\",\"position\":{\"x\":65.66623481972908,\"y\":137.63621309355807},\"positionAbsolute\":{\"x\":65.66623481972908,\"y\":137.63621309355807},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "128a4211-a111-4c5a-aba8-2d450baf0c97", "name": "对话历史", "widget_id": "WidgetKeyChatHistory", "widget_detail": {"id": "WidgetKeyChatHistory", "name": "对话历史", "desc": "用于将历史对话作为背景知识给下游提示词模版", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyChatHistory", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "ChatInput", "name": "对话输入", "desc": "支持类型: Sync-QAItems: [\n  {\n    \"Q\": \"Q1\",\n    \"A\": \"A1\"\n  },\n  {\n    \"Q\": \"Q2\",\n    \"A\": \"A2\"\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-QAItems"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxRounds", "name": "最大对话轮次", "desc": "历史对话中保留的最大对话轮次, 超出的轮次部分将会被自动截断. 适当的指定该属性可以有效避免超出模型的最大上下文上限", "number_range": {"min": 1, "max": 99, "step": 1}, "default_value": "5", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Tmpl", "name": "对话模板", "desc": "使用GoTemplate把输入的对话历史结构拼接成字符串", "default_value": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}", "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":181,\"id\":\"128a4211-a111-4c5a-aba8-2d450baf0c97\",\"position\":{\"x\":297,\"y\":356},\"positionAbsolute\":{\"x\":297,\"y\":356},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Tmpl": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}"}, "sub_chain_base_info": null}, {"id": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb", "name": "文本重排", "widget_id": "WidgetKeyTextRerank", "widget_detail": {"id": "WidgetKeyTextRerank", "name": "文本重排", "desc": "结合用户问题以及多段文本进行重排序，返回排序后的文本以及对应排序得分", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyTextRerank", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "用户问题", "desc": "文本排序时所依赖的用户问题,支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Texts", "name": "文本段", "desc": "用户输入的多段文本,支持类型: Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "TopK", "name": "TopK", "desc": "重排后保留的最大段落数量", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "文本重排后被保留的最低阈值要求", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "模型服务", "desc": "用于进行文本重排的模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "附带相关分数并且按照分数降序排列的文本段落,支持类型: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":387,\"id\":\"ffcc3b7f-ef3b-4f1c-8565-37e463e66afb\",\"position\":{\"x\":1171.0488120863613,\"y\":-54.76220302159031},\"positionAbsolute\":{\"x\":1171.0488120863613,\"y\":-54.76220302159031},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelService": "{\"id\":\"d41dccdd-8a86-4e78-b06d-09aec23fe443\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"TEXTS\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%text%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"model_name\":\"DemoRerank模型\",\"release_name\":\"bge-rerank-v2-m3\",\"release_version\":\"v1\",\"model_id\":\"MWH-MODEL-cqesq4nua89m9vt5185g\",\"release_id\":\"MWH-MODEL-RELEASE-cqesqdnua89m9vt51860\",\"inference_params\":[],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-d41dccdd-8a86-4e78-b06d-09aec23fe443\",\"name\":\"Golden-ShareRerank\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-d41dccdd-8a86-4e78-b06d-09aec23fe443/8011/std/v1/rerank?project_id=assets\",\"invoke_as_tool\":null,\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1723187521000\",\"reference_model\":{\"id\":\"MWH-MODEL-cqesq4nua89m9vt5185g\",\"name\":\"DemoRerank模型\",\"domain\":{\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"重排序模型\",\"user_id\":\"thinger\",\"thumbnail\":\"sfs://tenants/llmops-assets/projs/assets/avatar/475bb133-db7a-43e0-8c08-f9a6c5807d36_w.jpg\",\"is_public\":false,\"create_time_ms\":\"1721617682391\",\"update_time_ms\":\"1731048677649\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":1,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-cqesqdnua89m9vt51860\",\"name\":\"bge-rerank-v2-m3\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-cqesq4nua89m9vt5185g/releases/MWH-MODEL-RELEASE-cqesqdnua89m9vt51860/model-files\",\"model_id\":\"MWH-MODEL-cqesq4nua89m9vt5185g\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"**********\",\"create_time_ms\":\"1721617718229\",\"update_time_ms\":\"1723132902989\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"d41dccdd-8a86-4e78-b06d-09aec23fe443\",\"deployment_status\":\"running\",\"deployment_health\":\"healthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNSPECIFIED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"TEXTS\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%text%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"thinger\",\"training_template\":null}],\"usage_count\":{\"deploys_count\":0,\"views_count\":82,\"downloads_count\":2,\"invokes_count\":0,\"trainings_count\":0,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-cqesq4nua89m9vt5185g\"},\"disk_usage\":2293582600},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"TEXTS\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%text%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-cqesqdnua89m9vt51860\",\"name\":\"bge-rerank-v2-m3\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-cqesq4nua89m9vt5185g/releases/MWH-MODEL-RELEASE-cqesqdnua89m9vt51860/model-files\",\"model_id\":\"MWH-MODEL-cqesq4nua89m9vt5185g\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"**********\",\"create_time_ms\":\"1721617718229\",\"update_time_ms\":\"1723132902989\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":null,\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"TEXTS\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%text%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"thinger\",\"training_template\":null},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1731047691000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"DemoRerank模型\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v1\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"bge-rerank-v2-m3\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_RERANKING\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_RERANKING\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Golden-ShareRerank\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_RERANKING\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_RERANKING\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"d41dccdd-8a86-4e78-b06d-09aec23fe443\"}", "Threshold": 0, "TopK": 5}, "sub_chain_base_info": null}, {"id": "7f27133b-acb4-4972-be6d-a6fc36372d79", "name": "Go Template代码", "widget_id": "WidgetKeyCustomPrompt", "widget_detail": {"id": "WidgetKeyCustomPrompt", "name": "GoTemplate代码", "desc": "可用于灵活定义数据拼接、数据字典处理等过程", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyCustomPrompt", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用go template代码处理的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "go template代码", "desc": "go template代码，点击可查看或编辑代码", "default_value": "\n{{/* \n假设上游传入的数据为\n{\n\t\"string\": \"str\",\n\t\"number\": 123,\n\t\"list\": [1, 2, 3],\n\t\"dict\": { \"k\": \"v\" }\n}\n\n在Go Template语法中使用\".\"指代上游传入的数据，如下为几个使用示例\n\n1.直接输出上游传入的数据\n{{.}}\n\n2.取出字典中某个字段值输出：\n{{.string}} 取出某个字段\n{{.dict.k}} 取出嵌套的字段\n\n3.循环\n{{range .list}}\n{{.}}\n{{end}}\n*/}}\n\n{{.}}\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "go template代码执行的结果，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"7f27133b-acb4-4972-be6d-a6fc36372d79\",\"position\":{\"x\":1518.4802014393547,\"y\":180.5231757577821},\"positionAbsolute\":{\"x\":1518.4802014393547,\"y\":180.5231757577821},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n{{/* \n假设上游传入的数据为\n{\n\t\"string\": \"str\",\n\t\"number\": 123,\n\t\"list\": [1, 2, 3],\n\t\"dict\": { \"k\": \"v\" }\n}\n\n在Go Template语法中使用\".\"指代上游传入的数据，如下为几个使用示例\n\n1.直接输出上游传入的数据\n{{.}}\n\n2.取出字典中某个字段值输出：\n{{.string}} 取出某个字段\n{{.dict.k}} 取出嵌套的字段\n\n3.循环\n{{range .list}}\n{{.}}\n{{end}}\n*/}}\n\n{{range .}}\n  {{.Content}}\n{{end}}\n\n"}, "sub_chain_base_info": null}, {"id": "e11a524d-7e18-4820-9fd1-075d6462bc6b", "name": "文本生成模型", "widget_id": "WidgetKeyLLMModel", "widget_detail": {"id": "WidgetKeyLLMModel", "name": "文本生成", "desc": "用于调用文本生成模型服务", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyLLMModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "文本", "desc": "输入给LLM的提示词,支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "系统提示词", "desc": "模型的系统提示词，用于定义AI行为和回答方式，提高响应准确性", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM模型", "desc": "可用的LLM模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "对话历史", "desc": "对话历史", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "LLM输出的文本,支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"height\":191,\"id\":\"e11a524d-7e18-4820-9fd1-075d6462bc6b\",\"position\":{\"x\":2343.9287251816772,\"y\":353.5498855422304},\"positionAbsolute\":{\"x\":2343.9287251816772,\"y\":353.5498855422304},\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"Qwen2.5\",\"release_name\":\"Qwen2.5-Coder-7B-Instruct\",\"release_version\":\"v2\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"release_id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"name\":\"Golden-Qwen2.5-Coder-7B-Instruct\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-2c1ac526-8da5-48cb-bfe9-8eace385ab30/8011/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5\",\"name_for_human\":\"对话模型:Qwen2.5\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1731579161000\",\"reference_model\":{\"id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"name\":\"Qwen2.5\",\"domain\":{\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"qwen2.5对话模型\",\"user_id\":\"fangyuan.han\",\"thumbnail\":\"sfs://tenants/llmops-assets/projs/assets/avatar/0b56fcc9-ebbc-4cad-90ad-9c0459ad0fdb_w.jpg\",\"is_public\":false,\"create_time_ms\":\"1726800941008\",\"update_time_ms\":\"1731049523481\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":2,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g\",\"name\":\"Qwen2.5-7B-Instruct\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966687\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727177464182\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"12195919-edf2-4684-94f6-d01292a6e903\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.8\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"71e01c35-480d-436b-8dcb-30bc06555de4\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}}],\"usage_count\":{\"deploys_count\":0,\"views_count\":152,\"downloads_count\":2,\"invokes_count\":0,\"trainings_count\":1,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\"},\"disk_usage\":30485934000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"563be5ac-4460-479d-a991-eca6460f206a\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1732188130000\",\"guardrails_config\":{\"is_security\":true,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"Qwen2.5\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v2\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"Qwen2.5-Coder-7B-Instruct\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Golden-Qwen2.5-Coder-7B-Instruct\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\"}"}, "sub_chain_base_info": null}, {"id": "d4ea99b1-3ee6-4932-afef-685923400c21", "name": "文本拼接", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "通用文本模板", "desc": "把变量拼接成一段文本，文本模板中允许不引用变量或引用多个变量", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Template", "name": "模板", "default_value": "=== 角色设定 BEGIN ===\n你是一个乐于助人的知识问答助手，请根据我给你的上下文以及历史对话记录，回答用户的问题。\n\n=== 角色设定 END ===\n\n\n\n=== 背景知识上下文 BEGIN ===\n\n{{.上下文}}\n=== 背景知识上下文 END ===\n\n\n=== 历史对话上下文 BEGIN ===\n{{.对话历史}}\n\n=== 历史对话上下文 END ===\n\n\n\n现在，请回答用户问题：\n{{.问题}}\n\n", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "上下文", "name": "上下文", "desc": "上下文", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "对话历史", "name": "对话历史", "desc": "对话历史", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "问题", "name": "问题", "desc": "问题", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持传输方式:Sync,\n支持数据类型:\nString:text\n", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"d4ea99b1-3ee6-4932-afef-685923400c21\",\"position\":{\"x\":1945.4910723752869,\"y\":245.6066170528498},\"positionAbsolute\":{\"x\":1945.4910723752869,\"y\":245.6066170528498},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "=== 角色设定 BEGIN ===\n你是一个乐于助人的知识问答助手，请根据我给你的上下文以及历史对话记录，回答用户的问题。\n\n=== 角色设定 END ===\n\n\n\n=== 背景知识上下文 BEGIN ===\n\n{{.上下文}}\n=== 背景知识上下文 END ===\n\n\n=== 历史对话上下文 BEGIN ===\n{{.对话历史}}\n\n=== 历史对话上下文 END ===\n\n\n\n现在，请回答用户问题：\n{{.问题}}\n\n"}, "sub_chain_base_info": null}, {"id": "a54a4f15-2d01-45a1-9360-9e2732ee13e3", "name": "知识库检索", "widget_id": "WidgetKeyTextKnowledgeSearch", "widget_detail": {"id": "WidgetKeyTextKnowledgeSearch", "name": "知识库检索", "desc": "依据输入文本从知识库检索,并对结果排序和筛选", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyTextKnowledgeSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "输入", "desc": "检索问题,支持类型: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "最终保留的检索结果数量", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "检索结果被保留的最低阈值要求", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "跨知识库检索", "desc": "设置为跨知识库检索时，需配置rerank模型以提升检索效果", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "知识库信息", "desc": "知识库信息", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "知识库信息", "desc": "限定检索的知识库、及文档范围", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "重排模型", "desc": "选择模型,用于对检索结果进行重排和筛选", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "从知识库中召回的文本段落，支持类型: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":419,\"id\":\"a54a4f15-2d01-45a1-9360-9e2732ee13e3\",\"position\":{\"x\":437.4601155383508,\"y\":-289.03004217102375},\"positionAbsolute\":{\"x\":437.4601155383508,\"y\":-289.03004217102375},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"EnableMutil": false, "RerankThreshold": 0, "RerankTopK": 3, "SimpleKbInfo": "{\"id\":\"0e500c1c-69cd-4ea3-b350-16c0ee07de45\",\"is_public\":true}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-84ec2172-a374-4500-8c77-62cdb647239a84ec2172-a374-4500-8c77-62cdb647239a@@OutPut-ffcc3b7f-ef3b-4f1c-8565-37e463e66afbffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@Question", "source": "84ec2172-a374-4500-8c77-62cdb647239a", "source_param": "84ec2172-a374-4500-8c77-62cdb647239a@@OutPut", "target": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb", "target_param": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@Question"}, {"id": "reactflow__edge-ffcc3b7f-ef3b-4f1c-8565-37e463e66afbffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@OutPut-7f27133b-acb4-4972-be6d-a6fc36372d797f27133b-acb4-4972-be6d-a6fc36372d79@@Content", "source": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb", "source_param": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@OutPut", "target": "7f27133b-acb4-4972-be6d-a6fc36372d79", "target_param": "7f27133b-acb4-4972-be6d-a6fc36372d79@@Content"}, {"id": "reactflow__edge-7f27133b-acb4-4972-be6d-a6fc36372d797f27133b-acb4-4972-be6d-a6fc36372d79@@OutPut-d4ea99b1-3ee6-4932-afef-685923400c21d4ea99b1-3ee6-4932-afef-685923400c21@@上下文", "source": "7f27133b-acb4-4972-be6d-a6fc36372d79", "source_param": "7f27133b-acb4-4972-be6d-a6fc36372d79@@OutPut", "target": "d4ea99b1-3ee6-4932-afef-685923400c21", "target_param": "d4ea99b1-3ee6-4932-afef-685923400c21@@上下文"}, {"id": "reactflow__edge-128a4211-a111-4c5a-aba8-2d450baf0c97128a4211-a111-4c5a-aba8-2d450baf0c97@@OutPut-d4ea99b1-3ee6-4932-afef-685923400c21d4ea99b1-3ee6-4932-afef-685923400c21@@对话历史", "source": "128a4211-a111-4c5a-aba8-2d450baf0c97", "source_param": "128a4211-a111-4c5a-aba8-2d450baf0c97@@OutPut", "target": "d4ea99b1-3ee6-4932-afef-685923400c21", "target_param": "d4ea99b1-3ee6-4932-afef-685923400c21@@对话历史"}, {"id": "reactflow__edge-84ec2172-a374-4500-8c77-62cdb647239a84ec2172-a374-4500-8c77-62cdb647239a@@OutPut-d4ea99b1-3ee6-4932-afef-685923400c21d4ea99b1-3ee6-4932-afef-685923400c21@@问题", "source": "84ec2172-a374-4500-8c77-62cdb647239a", "source_param": "84ec2172-a374-4500-8c77-62cdb647239a@@OutPut", "target": "d4ea99b1-3ee6-4932-afef-685923400c21", "target_param": "d4ea99b1-3ee6-4932-afef-685923400c21@@问题"}, {"id": "reactflow__edge-d4ea99b1-3ee6-4932-afef-685923400c21d4ea99b1-3ee6-4932-afef-685923400c21@@OutPut-e11a524d-7e18-4820-9fd1-075d6462bc6be11a524d-7e18-4820-9fd1-075d6462bc6b@@Text", "source": "d4ea99b1-3ee6-4932-afef-685923400c21", "source_param": "d4ea99b1-3ee6-4932-afef-685923400c21@@OutPut", "target": "e11a524d-7e18-4820-9fd1-075d6462bc6b", "target_param": "e11a524d-7e18-4820-9fd1-075d6462bc6b@@Text"}, {"id": "reactflow__edge-84ec2172-a374-4500-8c77-62cdb647239a84ec2172-a374-4500-8c77-62cdb647239a@@OutPut-a54a4f15-2d01-45a1-9360-9e2732ee13e3a54a4f15-2d01-45a1-9360-9e2732ee13e3@@Question", "source": "84ec2172-a374-4500-8c77-62cdb647239a", "source_param": "84ec2172-a374-4500-8c77-62cdb647239a@@OutPut", "target": "a54a4f15-2d01-45a1-9360-9e2732ee13e3", "target_param": "a54a4f15-2d01-45a1-9360-9e2732ee13e3@@Question"}, {"id": "reactflow__edge-a54a4f15-2d01-45a1-9360-9e2732ee13e3a54a4f15-2d01-45a1-9360-9e2732ee13e3@@OutPut-ffcc3b7f-ef3b-4f1c-8565-37e463e66afbffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@Texts", "source": "a54a4f15-2d01-45a1-9360-9e2732ee13e3", "source_param": "a54a4f15-2d01-45a1-9360-9e2732ee13e3@@OutPut", "target": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb", "target_param": "ffcc3b7f-ef3b-4f1c-8565-37e463e66afb@@Texts"}], "viewport": {"x": 34.41738819929833, "y": 215.077522226994, "zoom": 0.629960542714989}}, "created_time": 0, "updated_time": 0}