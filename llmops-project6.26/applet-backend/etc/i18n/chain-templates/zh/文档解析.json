{"id": "文档解析.json", "name": "文档解析", "desc": "知识库选择文档解析策略时，可以选择已有应用链服务作为自定义解析策略。需要对应应用链输入输出格式符合规范格式要求。", "template_group_key": "CustomDocumentParseTool", "template": {"nodes": [{"id": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"b87ac904-3e66-4894-a7e2-d2842d6b5b9b\",\"position\":{\"x\":1478.1521039135719,\"y\":279.6498725719462},\"positionAbsolute\":{\"x\":1478.1521039135719,\"y\":279.6498725719462},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":3}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.chunks\n\n"}, "sub_chain_base_info": null}, {"id": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"9ca21b35-baff-4f2b-9a77-cec5d05d9b04\",\"position\":{\"x\":1490.0722227084084,\"y\":563.3746392864504},\"positionAbsolute\":{\"x\":1490.0722227084084,\"y\":563.3746392864504},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":2}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.elements\n\n"}, "sub_chain_base_info": null}, {"id": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"0951d7e1-98eb-4799-b1f5-39099b1632b0\",\"position\":{\"x\":749.1766348260853,\"y\":413.9564589651196},\"positionAbsolute\":{\"x\":749.1766348260853,\"y\":413.9564589651196},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.elements\n\n"}, "sub_chain_base_info": null}, {"id": "694d4529-0191-4701-89b1-7732e7560b89", "name": "chunking", "widget_id": "WidgetKeyApiCall", "widget_detail": {"id": "WidgetKeyApiCall", "name": "通用HTTP调用", "desc": "可调用所有HTTP API服务，暂时只支持POST请求", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyApiCall", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "APIPath", "name": "请求地址", "desc": "url地址，例如 http://example.com/api/v1", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Header", "name": "请求头", "desc": "请求头，例如 Content-Type = application/json 或 Authorization = Bearer xxx", "multiple": true, "type": "TYPE_KVITEM", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "RequestBody", "name": "请求体", "desc": "http的请求体，要求是字典、列表、数字、字符串等合法的http请求数据,支持类型: Sync-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "http请求响应的数据，可能是字典、列表、数字、字符串等合法的http响应数据,支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"694d4529-0191-4701-89b1-7732e7560b89\",\"position\":{\"x\":1093.9059097418144,\"y\":271.3745597010198},\"positionAbsolute\":{\"x\":1093.9059097418144,\"y\":271.3745597010198},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"APIName": "chunking", "APIPath": "http://autocv-applet-doc-service.llmops:80/api/v1/doc:chunk"}, "sub_chain_base_info": null}, {"id": "989bc203-459a-4e21-8961-f08c8f31ec82", "name": "chunks输出", "widget_id": "WidgetKeyChunksOutput", "widget_detail": {"id": "WidgetKeyChunksOutput", "name": "知识加工输出(自定义策略)", "desc": "标记算子,代表链的最终输出为pb.DocSvcLoadChunkRsp", "group": "WidgetGroupOutput", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Chunks", "name": "chunks", "desc": "chunks文本，支持类型: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Elements", "name": "elements", "desc": "elements文本，支持类型: Sync-Elements: [\n  {\n    \"element_id\": \"id\",\n    \"text\": \"text\"\n  }\n]。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-Elements"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-LoadChunk: {\n  \"chunks\": [\n    {\n      \"id\": \"id\",\n      \"content\": \"content\",\n      \"element_ids\": [\n        \"id1\",\n        \"id2\"\n      ]\n    }\n  ],\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-LoadChunk"]}}]}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"989bc203-459a-4e21-8961-f08c8f31ec82\",\"position\":{\"x\":1953.77134805062,\"y\":389.52831166853514},\"positionAbsolute\":{\"x\":1953.77134805062,\"y\":389.52831166853514},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "name": "文本解析", "widget_id": "WidgetKeyTextParse", "widget_detail": {"id": "WidgetKeyTextParse", "name": "标准文档解析", "desc": "使用平台提供的解析引擎，将文件转成DocElement结构的json输出（DocElement规范详见使用文档）", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "File", "name": "文件", "desc": "支持类型: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "forcePartition", "name": "强制解析", "desc": "开启强制解析时,解析服务会忽略缓存,降低解析效率", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "strategy", "name": "解析策略", "desc": "文件解析策略, 默认为自动策略", "default_value": "auto", "datasource": "auto@@自动策略,hi_res@@高精度策略,fast@@纯文本策略", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AdvancedOptions", "name": "高级选项", "desc": "多选，控制文本解析的行为", "datasource": "Save2MD@@保留markdown结果,Figure@@保留图片,Table@@保留表格,ChemicalFormula@@识别化学公式,MathFormula@@识别数学公式", "precondition": "[{\"both\":\"strategy==hi_res\"}]", "multiple": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "topK", "name": "TopK", "desc": "文本解析的返回数量限制", "number_range": {"min": 1, "max": 100, "step": 1}, "default_value": "1", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SavePath", "name": "保存路径", "desc": "文本解析信息的的保存地址，例如sfs:///tenants/dev-assets/projs/assets/", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "json", "name": "是否以json格式返回", "desc": "是否返回JSON格式，默认为false（返回纯文本（string）类型）, 勾选则返回文档元素列表（[]*pb.DocElement）", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "separator", "name": "文本拼接字符", "desc": "拼接文本使用的字符，默认为'\\n'", "precondition": "[{\"both\":\"json==false\"}]", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "解析后的文本，根据配置输出不同类型，支持类型: Sync-String: \"text\", Sync-ElementsV2: {\n  \"duration\": 0.8,\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-ElementsV2"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"3184e214-86fe-48f5-b3f2-8bf0bee818a9\",\"position\":{\"x\":379.4506898859386,\"y\":331.4714635209108},\"positionAbsolute\":{\"x\":379.4506898859386,\"y\":331.4714635209108},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"forcePartition": false, "json": true, "strategy": "auto"}, "sub_chain_base_info": null}, {"id": "a13eb23d-421d-4092-a8b7-7a559d0ce294", "name": "文件上传", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "文件上传", "desc": "用于加载上传的单个文件, 输出SFSFile类型数据", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "上传文件", "desc": "上传文件,支持类型: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]。", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "读取文件内容", "desc": "是否读取文件内容，开启后会读取文件内容，向下游节点传递文件字节流；否则只会传递文件路径", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "最大文件大小(MB)", "desc": "允许上传的最大文件大小，单位为MB", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "允许的文件扩展名", "desc": "允许上传的文件扩展名列表，每行只允许输入一个扩展名，可输入*、txt、pdf、docx等，当输入 * 或者未输入任何扩展名时表示允许上传所有格式的文件", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":361,\"id\":\"a13eb23d-421d-4092-a8b7-7a559d0ce294\",\"position\":{\"x\":-61.94624225566133,\"y\":272.07355341301957},\"positionAbsolute\":{\"x\":-61.94624225566133,\"y\":272.07355341301957},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["*"], "IsFileContentRead": true, "MaxFileSizeMB": 20}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-0951d7e1-98eb-4799-b1f5-39099b1632b00951d7e1-98eb-4799-b1f5-39099b1632b0@@OutPut-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@RequestBody", "source": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "source_param": "0951d7e1-98eb-4799-b1f5-39099b1632b0@@OutPut", "target": "694d4529-0191-4701-89b1-7732e7560b89", "target_param": "694d4529-0191-4701-89b1-7732e7560b89@@RequestBody"}, {"id": "reactflow__edge-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@OutPut-9ca21b35-baff-4f2b-9a77-cec5d05d9b049ca21b35-baff-4f2b-9a77-cec5d05d9b04@@Content", "source": "694d4529-0191-4701-89b1-7732e7560b89", "source_param": "694d4529-0191-4701-89b1-7732e7560b89@@OutPut", "target": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "target_param": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04@@Content"}, {"id": "reactflow__edge-9ca21b35-baff-4f2b-9a77-cec5d05d9b049ca21b35-baff-4f2b-9a77-cec5d05d9b04@@OutPut-989bc203-459a-4e21-8961-f08c8f31ec82989bc203-459a-4e21-8961-f08c8f31ec82@@Elements", "source": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "source_param": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04@@OutPut", "target": "989bc203-459a-4e21-8961-f08c8f31ec82", "target_param": "989bc203-459a-4e21-8961-f08c8f31ec82@@Elements"}, {"id": "reactflow__edge-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@OutPut-b87ac904-3e66-4894-a7e2-d2842d6b5b9bb87ac904-3e66-4894-a7e2-d2842d6b5b9b@@Content", "source": "694d4529-0191-4701-89b1-7732e7560b89", "source_param": "694d4529-0191-4701-89b1-7732e7560b89@@OutPut", "target": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "target_param": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b@@Content"}, {"id": "reactflow__edge-b87ac904-3e66-4894-a7e2-d2842d6b5b9bb87ac904-3e66-4894-a7e2-d2842d6b5b9b@@OutPut-989bc203-459a-4e21-8961-f08c8f31ec82989bc203-459a-4e21-8961-f08c8f31ec82@@Chunks", "source": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "source_param": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b@@OutPut", "target": "989bc203-459a-4e21-8961-f08c8f31ec82", "target_param": "989bc203-459a-4e21-8961-f08c8f31ec82@@Chunks"}, {"id": "reactflow__edge-3184e214-86fe-48f5-b3f2-8bf0bee818a93184e214-86fe-48f5-b3f2-8bf0bee818a9@@OutPut-0951d7e1-98eb-4799-b1f5-39099b1632b00951d7e1-98eb-4799-b1f5-39099b1632b0@@Content", "source": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "source_param": "3184e214-86fe-48f5-b3f2-8bf0bee818a9@@OutPut", "target": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "target_param": "0951d7e1-98eb-4799-b1f5-39099b1632b0@@Content"}, {"id": "reactflow__edge-a13eb23d-421d-4092-a8b7-7a559d0ce294a13eb23d-421d-4092-a8b7-7a559d0ce294@@OutPut-3184e214-86fe-48f5-b3f2-8bf0bee818a93184e214-86fe-48f5-b3f2-8bf0bee818a9@@File", "source": "a13eb23d-421d-4092-a8b7-7a559d0ce294", "source_param": "a13eb23d-421d-4092-a8b7-7a559d0ce294@@OutPut", "target": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "target_param": "3184e214-86fe-48f5-b3f2-8bf0bee818a9@@File"}], "viewport": {"x": -91.39273030354457, "y": -53.69631666301234, "zoom": 0.7453551933994605}}, "created_time": 0, "updated_time": 0}