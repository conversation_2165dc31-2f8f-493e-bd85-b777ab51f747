{"id": "条件分支.json", "name": "条件分支", "desc": "使用大模型进行意图识别，然后通过 条件分支 算子，提取不同意图，并进行后续处理", "template_group_key": "CycleAndBranch", "template": {"nodes": [{"id": "04f9e1c4-77a2-42cf-ba19-4327b9184814", "name": "文本输入", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "文本输入", "desc": "用于将输入的文本原样输出", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "文本输入", "desc": "文本输入,支持类型: Sync-String: \"text\"。", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"04f9e1c4-77a2-42cf-ba19-4327b9184814\",\"position\":{\"x\":-55,\"y\":706},\"positionAbsolute\":{\"x\":-55,\"y\":706},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"30c6c32e-3614-49e8-a1e2-cf5287efd83e\",\"position\":{\"x\":357.3720324176933,\"y\":464.8104131600237},\"positionAbsolute\":{\"x\":357.3720324176933,\"y\":464.8104131600237},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n{\n \"messages\": [  {\n   \"role\": \"system\",\n   \"content\": \"你是一个意图识别引擎，你可以识别用户问题的相关意图。返回的意图名称为search、analysis、assess、或other，在你的响应中除了意图名称外不要包含任何其他内容。\\n## Note\\n1、如果用户问题存在明显的查询或询问意图，请返回search\\n2、如果用户问题存在明显的分析意图，或者用户问题中包含同比或环比，请返回analysis\\n3、如果用户问题存在明显的预测或评估意图，请返回assess\\n4、上记以外的场合，例如打招呼、闲聊等意图，请返回other\\n\\n## Example \\n请查询中海油炼化板块近十年吨净利\\nA: search\\n告诉我单位LH001今年92号汽油的单位售价\\nA: search\\n分析今年LH001单位的吨净利同比\\nA: analysis\\n帮我分析2023年10月吨净利环比\\nA: analysis\\n评估LH001明年的吨净利是否合理\\nA: assess\\n预测未来3个月中海油炼化板块预算完成情况\\nA: assess\\n你好\\nA: other\\n你是谁\\nA: other\"\n  },\n  {\n   \"role\": \"user\",\n   \"content\": \"input\"\n  }\n ],\n \"model\":\"atom\",\n \"stream\": false\n}\n\n"}, "sub_chain_base_info": null}, {"id": "2fa99347-86f6-4011-8bd2-dedcc24c4554", "name": "Python代码", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python代码", "desc": "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用python代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python代码", "desc": "python代码，点击可查看或编辑代码", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "python代码中handler函数返回的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"2fa99347-86f6-4011-8bd2-dedcc24c4554\",\"position\":{\"x\":1137.4424167952104,\"y\":509.26386476457594},\"positionAbsolute\":{\"x\":1137.4424167952104,\"y\":509.26386476457594},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\n# {\n#   \"Event\": \"message\",\n#   \"Id\": \"\",\n#   \"Retry\": 0,\n#   \"Data\": {\n#     \"choices\": [\n#       {\n#         \"delta\": {\n#           \"content\": \"需要，我可以为您提供帮助\"\n#         },\n#         \"finish_reason\": null,\n#         \"index\": 0,\n#         \"logprobs\": null\n#       }\n#     ],\n#     \"created\": 1718079522,\n#     \"id\": \"chatcmpl-d6d7e173-51d7-9924-ad21-ec8bd8a2dce1\",\n#     \"model\": \"qwen-turbo\",\n#     \"object\": \"chat.completion.chunk\",\n#     \"system_fingerprint\": null,\n#     \"usage\": null\n#   }\n# }\n\n## handle openai stream\n# def handler(data):\n#     if \"Data\" not in data:\n#         return \"\"\n#     data = data[\"Data\"]\n\n#     if \"choices\" not in data:\n#         return \"\"\n#     data = data[\"choices\"]\n\n#     if len(data) <= 0:\n#         return \"\"\n#     data = data[0]\n#     if \"delta\" not in data:\n#         return \"\"\n#     data = data[\"delta\"]\n\n#     if \"content\" not in data:\n#         return \"\"\n#     return data[\"content\"]\n\n# handle openai non-stream\ndef handler(data):\n\n    if \"choices\" not in data:\n        return \"\"\n    data = data[\"choices\"]\n\n    if len(data) <= 0:\n        return \"\"\n    data = data[0]\n    if \"message\" not in data:\n        return \"\"\n    data = data[\"message\"]\n\n    if \"content\" not in data:\n        return \"\"\n    return data[\"content\"]\n"}, "sub_chain_base_info": null}, {"id": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c", "name": "条件判断", "widget_id": "WidgetKeyConditionJudge", "widget_detail": {"id": "WidgetKeyConditionJudge", "name": "条件判断", "desc": "把数据流分叉成if分支与else分支，根据输入的条件决定数据流向的分支", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyConditionJudge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入数据", "desc": "待执行判断条件的数据,支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "条件成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "判断条件", "desc": "\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\n可以使用下面的语法表示判断条件, 更复杂的判断条件请参考Jsonnet语法\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"\n", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "条件不成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":236,\"id\":\"1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c\",\"position\":{\"x\":1802.7081485702925,\"y\":466.88227045067606},\"positionAbsolute\":{\"x\":1802.7081485702925,\"y\":466.88227045067606},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "input.intention == \"search\""}, "sub_chain_base_info": null}, {"id": "e52a67e7-4ee4-4052-b6b8-4141cd449c99", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"e52a67e7-4ee4-4052-b6b8-4141cd449c99\",\"position\":{\"x\":2164.5481303943593,\"y\":384.1759888908894},\"positionAbsolute\":{\"x\":2164.5481303943593,\"y\":384.1759888908894},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\n\"IF-Branch: \\n intention=<b>\" + input.intention + \"</b>\\nquery=<b>\"+input.query+\"</b>\"\n\n"}, "sub_chain_base_info": null}, {"id": "af3d5470-973b-46bc-985b-40ce2eb44e8c", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"af3d5470-973b-46bc-985b-40ce2eb44e8c\",\"position\":{\"x\":2166.845527104353,\"y\":616.2130566002904},\"positionAbsolute\":{\"x\":2166.845527104353,\"y\":616.2130566002904},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\"ELSE-Branch: \\n intention=<b>\" + input.intention + \"</b>\\nquery=<b>\"+input.query+\"</b>\"\n"}, "sub_chain_base_info": null}, {"id": "44241a7e-c194-4057-8f78-2f6c2b24da46", "name": "数据合流", "widget_id": "WidgetKeyUnion", "widget_detail": {"id": "WidgetKeyUnion", "name": "数据合流", "desc": "将上游多个输入，形成队列串行输出", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyUnion", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "上游数据，任意类型", "desc": "待合流的上游数据，需保证上游只有一个分支有数据流入此算子，否则请用数据合并算子,支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "原样输出输入的上游数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"height\":132,\"id\":\"44241a7e-c194-4057-8f78-2f6c2b24da46\",\"position\":{\"x\":2620.5813773281816,\"y\":554.1833454304506},\"positionAbsolute\":{\"x\":2620.5813773281816,\"y\":554.1833454304506},\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "c56d9f83-6a87-448e-b804-a5694fd7e7e9", "name": "数据合并", "widget_id": "WidgetKeyInputAggregation", "widget_detail": {"id": "WidgetKeyInputAggregation", "name": "数据合并", "desc": "将上游多个输入，以KV的形式组成一个json输出（所有数据到位才会触发输出）", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyInputAggregation", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "intention", "name": "intention", "desc": "intention", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "query", "name": "query", "desc": "query", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"c56d9f83-6a87-448e-b804-a5694fd7e7e9\",\"position\":{\"x\":1457.1796833871854,\"y\":728.7854953899997},\"positionAbsolute\":{\"x\":1457.1796833871854,\"y\":728.7854953899997},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "61f28fc7-6426-48e5-a354-dc2e6f3c7573", "name": "chat/completions接口", "widget_id": "WidgetKeyApiCall", "widget_detail": {"id": "WidgetKeyApiCall", "name": "通用HTTP调用", "desc": "可调用所有HTTP API服务，暂时只支持POST请求", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyApiCall", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "APIPath", "name": "请求地址", "desc": "url地址，例如 http://example.com/api/v1", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Header", "name": "请求头", "desc": "请求头，例如 Content-Type = application/json 或 Authorization = Bearer xxx", "multiple": true, "type": "TYPE_KVITEM", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "RequestBody", "name": "请求体", "desc": "http的请求体，要求是字典、列表、数字、字符串等合法的http请求数据,支持类型: Sync-Any: \"任意数据类型\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "http请求响应的数据，可能是字典、列表、数字、字符串等合法的http响应数据,支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":284,\"id\":\"61f28fc7-6426-48e5-a354-dc2e6f3c7573\",\"position\":{\"x\":799.305101206663,\"y\":130.24431232358927},\"positionAbsolute\":{\"x\":799.305101206663,\"y\":130.24431232358927},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"APIPath": "http://*************:8011/openai/v1/chat/completions"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-04f9e1c4-77a2-42cf-ba19-4327b918481404f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut-30c6c32e-3614-49e8-a1e2-cf5287efd83e30c6c32e-3614-49e8-a1e2-cf5287efd83e@@Content", "source": "04f9e1c4-77a2-42cf-ba19-4327b9184814", "source_param": "04f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut", "target": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "target_param": "30c6c32e-3614-49e8-a1e2-cf5287efd83e@@Content"}, {"id": "reactflow__edge-1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@OutPutIF-e52a67e7-4ee4-4052-b6b8-4141cd449c99e52a67e7-4ee4-4052-b6b8-4141cd449c99@@Content", "source": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c", "source_param": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@OutPutIF", "target": "e52a67e7-4ee4-4052-b6b8-4141cd449c99", "target_param": "e52a67e7-4ee4-4052-b6b8-4141cd449c99@@Content"}, {"id": "reactflow__edge-1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@OutPutElse-af3d5470-973b-46bc-985b-40ce2eb44e8caf3d5470-973b-46bc-985b-40ce2eb44e8c@@Content", "source": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c", "source_param": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@OutPutElse", "target": "af3d5470-973b-46bc-985b-40ce2eb44e8c", "target_param": "af3d5470-973b-46bc-985b-40ce2eb44e8c@@Content"}, {"id": "reactflow__edge-e52a67e7-4ee4-4052-b6b8-4141cd449c99e52a67e7-4ee4-4052-b6b8-4141cd449c99@@OutPut-44241a7e-c194-4057-8f78-2f6c2b24da4644241a7e-c194-4057-8f78-2f6c2b24da46@@Input", "source": "e52a67e7-4ee4-4052-b6b8-4141cd449c99", "source_param": "e52a67e7-4ee4-4052-b6b8-4141cd449c99@@OutPut", "target": "44241a7e-c194-4057-8f78-2f6c2b24da46", "target_param": "44241a7e-c194-4057-8f78-2f6c2b24da46@@Input"}, {"id": "reactflow__edge-af3d5470-973b-46bc-985b-40ce2eb44e8caf3d5470-973b-46bc-985b-40ce2eb44e8c@@OutPut-44241a7e-c194-4057-8f78-2f6c2b24da4644241a7e-c194-4057-8f78-2f6c2b24da46@@Input", "source": "af3d5470-973b-46bc-985b-40ce2eb44e8c", "source_param": "af3d5470-973b-46bc-985b-40ce2eb44e8c@@OutPut", "target": "44241a7e-c194-4057-8f78-2f6c2b24da46", "target_param": "44241a7e-c194-4057-8f78-2f6c2b24da46@@Input"}, {"id": "reactflow__edge-2fa99347-86f6-4011-8bd2-dedcc24c45542fa99347-86f6-4011-8bd2-dedcc24c4554@@OutPut-c56d9f83-6a87-448e-b804-a5694fd7e7e9c56d9f83-6a87-448e-b804-a5694fd7e7e9@@intention", "source": "2fa99347-86f6-4011-8bd2-dedcc24c4554", "source_param": "2fa99347-86f6-4011-8bd2-dedcc24c4554@@OutPut", "target": "c56d9f83-6a87-448e-b804-a5694fd7e7e9", "target_param": "c56d9f83-6a87-448e-b804-a5694fd7e7e9@@intention"}, {"id": "reactflow__edge-c56d9f83-6a87-448e-b804-a5694fd7e7e9c56d9f83-6a87-448e-b804-a5694fd7e7e9@@OutPut-1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@Input", "source": "c56d9f83-6a87-448e-b804-a5694fd7e7e9", "source_param": "c56d9f83-6a87-448e-b804-a5694fd7e7e9@@OutPut", "target": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c", "target_param": "1005cbff-bcbc-4b3d-b82b-7de1c1a4bf2c@@Input"}, {"id": "reactflow__edge-04f9e1c4-77a2-42cf-ba19-4327b918481404f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut-c56d9f83-6a87-448e-b804-a5694fd7e7e9c56d9f83-6a87-448e-b804-a5694fd7e7e9@@query", "source": "04f9e1c4-77a2-42cf-ba19-4327b9184814", "source_param": "04f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut", "target": "c56d9f83-6a87-448e-b804-a5694fd7e7e9", "target_param": "c56d9f83-6a87-448e-b804-a5694fd7e7e9@@query"}, {"id": "reactflow__edge-30c6c32e-3614-49e8-a1e2-cf5287efd83e30c6c32e-3614-49e8-a1e2-cf5287efd83e@@OutPut-61f28fc7-6426-48e5-a354-dc2e6f3c757361f28fc7-6426-48e5-a354-dc2e6f3c7573@@RequestBody", "source": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "source_param": "30c6c32e-3614-49e8-a1e2-cf5287efd83e@@OutPut", "target": "61f28fc7-6426-48e5-a354-dc2e6f3c7573", "target_param": "61f28fc7-6426-48e5-a354-dc2e6f3c7573@@RequestBody"}, {"id": "reactflow__edge-61f28fc7-6426-48e5-a354-dc2e6f3c757361f28fc7-6426-48e5-a354-dc2e6f3c7573@@OutPut-2fa99347-86f6-4011-8bd2-dedcc24c45542fa99347-86f6-4011-8bd2-dedcc24c4554@@Content", "source": "61f28fc7-6426-48e5-a354-dc2e6f3c7573", "source_param": "61f28fc7-6426-48e5-a354-dc2e6f3c7573@@OutPut", "target": "2fa99347-86f6-4011-8bd2-dedcc24c4554", "target_param": "2fa99347-86f6-4011-8bd2-dedcc24c4554@@Content"}], "viewport": {"x": 116.01019206920546, "y": -15.405467795148311, "zoom": 0.6104732563345672}}, "created_time": 0, "updated_time": 0}