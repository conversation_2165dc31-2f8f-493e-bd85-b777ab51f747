{"id": "简单循环示例.json", "name": "Simple Loop Example", "desc": "Implement loop logic within the application chain through loop jumps + conditional judgment operators.", "template_group_key": "CycleAndBranch", "template": {"nodes": [{"id": "63487cfd-4c23-4033-bd5a-15aa22416415", "name": "Text Input - Enter Value", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"63487cfd-4c23-4033-bd5a-15aa22416415\",\"position\":{\"x\":122.12275750260858,\"y\":210.4526550925716},\"positionAbsolute\":{\"x\":122.12275750260858,\"y\":210.4526550925716},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "c8baface-e33d-4071-8d71-982536676948", "name": "Python - Increment Loop", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python code", "desc": "Can write Python code to complete complex business logic, with pre-installed dependencies including requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "python code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"c8baface-e33d-4071-8d71-982536676948\",\"position\":{\"x\":552.8463971599524,\"y\":125.69279431990475},\"positionAbsolute\":{\"x\":552.8463971599524,\"y\":125.69279431990475},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n\n    return str(int(data) + 1)\n\n"}, "sub_chain_base_info": null}, {"id": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "name": "Conditional Judgment - Greater Than 10", "widget_id": "WidgetKeyConditionJudge", "widget_detail": {"id": "WidgetKeyConditionJudge", "name": "Conditional Judgment", "desc": "Fork the data flow into if and else branches, determining the branch based on the input condition.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyConditionJudge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input data", "desc": "Data for pending judgment conditions, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "Output endpoint when the condition is met, outputs the input data as is, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Judgment Conditions", "desc": "Use the input variable to store data passed from upstream. Assuming the data stored in input is:\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\nYou can use the following syntax to represent conditional statements; for more complex conditions, please refer to the Jsonnet syntax:\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "Output endpoint when the condition is not met, outputs the input data as is, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":236,\"id\":\"038d0694-52fb-4ac8-85b1-acadf0e9f5de\",\"position\":{\"x\":1023.2885419478077,\"y\":109.3583271542933},\"positionAbsolute\":{\"x\":1023.2885419478077,\"y\":109.3583271542933},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "std.parseInt(input) > 4"}, "sub_chain_base_info": null}, {"id": "71000714-ce7c-400c-a5d6-bd42b3196bce", "name": "Loop Jump", "widget_id": "WidgetKeyGoTo", "widget_detail": {"id": "WidgetKeyGoTo", "name": "Loop Jump", "desc": "Used to change the direction of data flow, redirecting data transmission to a previous operator to implement loops.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyGoTo", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input data", "desc": "Data to be redirected, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "maxLoopRounds", "name": "Maximum number of rounds", "desc": "Maximum number of cycles, must be a positive integer", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Output the data in its original form, supported types: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":208,\"id\":\"71000714-ce7c-400c-a5d6-bd42b3196bce\",\"position\":{\"x\":656.0459751708086,\"y\":474.0213824860109},\"positionAbsolute\":{\"x\":656.0459751708086,\"y\":474.0213824860109},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"maxLoopRounds": 5}, "sub_chain_base_info": null}, {"id": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1", "name": "Text Presentation", "widget_id": "WidgetKeyTextShow", "widget_detail": {"id": "WidgetKeyTextShow", "name": "Text Presentation", "desc": "Used to add custom prefixes and suffixes to the upstream output text", "group": "WidgetGroupOutput", "oriWidgetKey": "WidgetKeyTextShow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text Input", "desc": "Text input, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Prefix", "name": "Prefix", "desc": "Prefix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Suffix", "name": "Suffix", "desc": "Suffix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Output, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"9ba4aa6d-1a9a-4091-bcb8-b297a99418b1\",\"position\":{\"x\":1621.697363895553,\"y\":193.9490332643993},\"positionAbsolute\":{\"x\":1621.697363895553,\"y\":193.9490332643993},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Prefix": "循环结果：\n```json\n", "Suffix": "\n```"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-63487cfd-4c23-4033-bd5a-15aa2241641563487cfd-4c23-4033-bd5a-15aa22416415@@OutPut-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@Content", "source": "63487cfd-4c23-4033-bd5a-15aa22416415", "source_param": "63487cfd-4c23-4033-bd5a-15aa22416415@@OutPut", "target": "c8baface-e33d-4071-8d71-982536676948", "target_param": "c8baface-e33d-4071-8d71-982536676948@@Content"}, {"id": "reactflow__edge-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@OutPut-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@Input", "source": "c8baface-e33d-4071-8d71-982536676948", "source_param": "c8baface-e33d-4071-8d71-982536676948@@OutPut", "target": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "target_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@Input"}, {"id": "reactflow__edge-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutElse-71000714-ce7c-400c-a5d6-bd42b3196bce71000714-ce7c-400c-a5d6-bd42b3196bce@@Input", "source": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "source_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutElse", "target": "71000714-ce7c-400c-a5d6-bd42b3196bce", "target_param": "71000714-ce7c-400c-a5d6-bd42b3196bce@@Input"}, {"id": "reactflow__edge-71000714-ce7c-400c-a5d6-bd42b3196bce71000714-ce7c-400c-a5d6-bd42b3196bce@@OutPut-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@Content", "source": "71000714-ce7c-400c-a5d6-bd42b3196bce", "source_param": "71000714-ce7c-400c-a5d6-bd42b3196bce@@OutPut", "target": "c8baface-e33d-4071-8d71-982536676948", "target_param": "c8baface-e33d-4071-8d71-982536676948@@Content"}, {"id": "reactflow__edge-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutIF-9ba4aa6d-1a9a-4091-bcb8-b297a99418b19ba4aa6d-1a9a-4091-bcb8-b297a99418b1@@Input", "source": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "source_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutIF", "target": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1", "target_param": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1@@Input"}], "viewport": {"x": -42.694602975749035, "y": 71.031247713129, "zoom": 0.7937005035983871}}, "created_time": 0, "updated_time": 0}