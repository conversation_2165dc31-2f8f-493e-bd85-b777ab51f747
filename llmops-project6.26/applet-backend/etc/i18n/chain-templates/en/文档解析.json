{"id": "文档解析.json", "name": "Document Parsing", "desc": "When selecting a document parsing strategy for the knowledge base, an existing applied chain service can be chosen as a custom parsing strategy. It is necessary for the input and output formats of the corresponding applied chain to comply with the standard format requirements.", "template_group_key": "CustomDocumentParseTool", "template": {"nodes": [{"id": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "name": "Jsonnet code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet code", "desc": "It can conveniently convert the structure of the output data upstream, such as extracting the value of a certain field or changing the field name, etc.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet code", "desc": "jsonnet code, click to view or edit the code", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "The execution result of jsonnet code, supported type: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"b87ac904-3e66-4894-a7e2-d2842d6b5b9b\",\"position\":{\"x\":1478.1521039135719,\"y\":279.6498725719462},\"positionAbsolute\":{\"x\":1478.1521039135719,\"y\":279.6498725719462},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":3}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.chunks\n\n"}, "sub_chain_base_info": null}, {"id": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "name": "Jsonnet code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet code", "desc": "It can conveniently convert the structure of the output data upstream, such as extracting the value of a certain field or changing the field name, etc.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet code", "desc": "jsonnet code, click to view or edit the code", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "The execution result of jsonnet code, supported type: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"9ca21b35-baff-4f2b-9a77-cec5d05d9b04\",\"position\":{\"x\":1490.0722227084084,\"y\":563.3746392864504},\"positionAbsolute\":{\"x\":1490.0722227084084,\"y\":563.3746392864504},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":2}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.elements\n\n"}, "sub_chain_base_info": null}, {"id": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "name": "Jsonnet code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet code", "desc": "It can conveniently convert the structure of the output data upstream, such as extracting the value of a certain field or changing the field name, etc.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet code", "desc": "jsonnet code, click to view or edit the code", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "The execution result of jsonnet code, supported type: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"0951d7e1-98eb-4799-b1f5-39099b1632b0\",\"position\":{\"x\":749.1766348260853,\"y\":413.9564589651196},\"positionAbsolute\":{\"x\":749.1766348260853,\"y\":413.9564589651196},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.elements\n\n"}, "sub_chain_base_info": null}, {"id": "694d4529-0191-4701-89b1-7732e7560b89", "name": "chunking", "widget_id": "WidgetKeyApiCall", "widget_detail": {"id": "WidgetKeyApiCall", "name": "General HTTP Invocation", "desc": "Callable to all HTTP API services, currently only supports POST requests.", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyApiCall", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "APIPath", "name": "Request Address", "desc": "URL address, for example http://example.com/api/v1", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Header", "name": "Request Header", "desc": "Request headers, such as Content-Type = application/json or Authorization = Bearer xxx", "multiple": true, "type": "TYPE_KVITEM", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "RequestBody", "name": "Request Body", "desc": "The HTTP request body should be a valid HTTP request data type, such as a dictionary, list, number, or string. Supported type: Sync-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "http request response data may be a dictionary, list, number, string, or any other valid http response data. Supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"694d4529-0191-4701-89b1-7732e7560b89\",\"position\":{\"x\":1093.9059097418144,\"y\":271.3745597010198},\"positionAbsolute\":{\"x\":1093.9059097418144,\"y\":271.3745597010198},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"APIName": "chunking", "APIPath": "http://autocv-applet-doc-service.llmops:80/api/v1/doc:chunk"}, "sub_chain_base_info": null}, {"id": "989bc203-459a-4e21-8961-f08c8f31ec82", "name": "chunks output", "widget_id": "WidgetKeyChunksOutput", "widget_detail": {"id": "WidgetKeyChunksOutput", "name": "Knowledge Processing Output (Custom Strategy)", "desc": "Marker operator, representing the final output of the chain is pb.DocSvcLoadChunkRsp", "group": "WidgetGroupOutput", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Chunks", "name": "chunks", "desc": "chunks text, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Elements", "name": "elements", "desc": "elements text, supported types: Sync-Elements: [\n  {\n    \"element_id\": \"id\",\n    \"text\": \"text\"\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-Elements"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Support Type: Sync-LoadChunk: {\n  \"chunks\": [\n    {\n      \"id\": \"id\",\n      \"content\": \"content\",\n      \"element_ids\": [\n        \"id1\",\n        \"id2\"\n      ]\n    }\n  ],\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-LoadChunk"]}}]}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"989bc203-459a-4e21-8961-f08c8f31ec82\",\"position\":{\"x\":1953.77134805062,\"y\":389.52831166853514},\"positionAbsolute\":{\"x\":1953.77134805062,\"y\":389.52831166853514},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "name": "Text Analysis", "widget_id": "WidgetKeyTextParse", "widget_detail": {"id": "WidgetKeyTextParse", "name": "Standard Document Parsing", "desc": "Use the parsing engine provided by the platform to convert the file into a JSON output with a DocElement structure (for the DocElement specification, refer to the user manual).", "group": "WidgetGroupProcessKnowledge", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "File", "name": "File", "desc": "Support Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "forcePartition", "name": "Mandatory Parsing", "desc": "When forced parsing is enabled, the parsing service will ignore the cache, reducing parsing efficiency.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "strategy", "name": "Analysis Strategy", "desc": "File parsing strategy, default is automatic strategy", "default_value": "auto", "datasource": "auto@@自动策略,hi_res@@高精度策略,fast@@纯文本策略", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AdvancedOptions", "name": "Advanced Options", "desc": "Multiple selection, control the behavior of text parsing", "datasource": "Save2MD@@保留markdown结果,Figure@@保留图片,Table@@保留表格,ChemicalFormula@@识别化学公式,MathFormula@@识别数学公式", "precondition": "[{\"both\":\"strategy==hi_res\"}]", "multiple": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "topK", "name": "TopK", "desc": "Limit on the Number of Text Analysis Results Returned", "number_range": {"min": 1, "max": 100, "step": 1}, "default_value": "1", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SavePath", "name": "Save Path", "desc": "The storage address for the parsed text information, for example sfs:///tenants/dev-assets/projs/assets/", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "json", "name": "Whether to return in JSON format", "desc": "Whether to return in JSON format, default is false (returns plain text (string) type), check to return a list of document elements ([]*pb.DocElement)", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "separator", "name": "Text concatenation character", "desc": "The character used for concatenating text, default is '\\n'", "precondition": "[{\"both\":\"json==false\"}]", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Parsed text, output different types according to configuration, supported types: Sync-String: \"text\", Sync-ElementsV2: {\n  \"duration\": 0.8,\n  \"elements\": [\n    {\n      \"element_id\": \"id\",\n      \"text\": \"text\"\n    }\n  ]\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-ElementsV2"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"3184e214-86fe-48f5-b3f2-8bf0bee818a9\",\"position\":{\"x\":379.4506898859386,\"y\":331.4714635209108},\"positionAbsolute\":{\"x\":379.4506898859386,\"y\":331.4714635209108},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"forcePartition": false, "json": true, "strategy": "auto"}, "sub_chain_base_info": null}, {"id": "a13eb23d-421d-4092-a8b7-7a559d0ce294", "name": "File Upload", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "File Upload", "desc": "Used for loading a single uploaded file, outputting data of SFSFile type.", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "Upload File", "desc": "Upload file, supported types: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "Read file content", "desc": "Whether to read the file content, if enabled, it will read the file content and pass the file byte stream to the downstream node; otherwise, it will only pass the file path.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "Maximum file size (MB)", "desc": "The maximum file size allowed for upload, in MB.", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "Allowed file extensions", "desc": "List of allowed file extensions, only one extension per line is permitted, you can enter *, txt, pdf, docx, etc. Entering * or leaving it blank means all file formats are allowed.", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Support Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":361,\"id\":\"a13eb23d-421d-4092-a8b7-7a559d0ce294\",\"position\":{\"x\":-61.94624225566133,\"y\":272.07355341301957},\"positionAbsolute\":{\"x\":-61.94624225566133,\"y\":272.07355341301957},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["*"], "IsFileContentRead": true, "MaxFileSizeMB": 20}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-0951d7e1-98eb-4799-b1f5-39099b1632b00951d7e1-98eb-4799-b1f5-39099b1632b0@@OutPut-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@RequestBody", "source": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "source_param": "0951d7e1-98eb-4799-b1f5-39099b1632b0@@OutPut", "target": "694d4529-0191-4701-89b1-7732e7560b89", "target_param": "694d4529-0191-4701-89b1-7732e7560b89@@RequestBody"}, {"id": "reactflow__edge-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@OutPut-9ca21b35-baff-4f2b-9a77-cec5d05d9b049ca21b35-baff-4f2b-9a77-cec5d05d9b04@@Content", "source": "694d4529-0191-4701-89b1-7732e7560b89", "source_param": "694d4529-0191-4701-89b1-7732e7560b89@@OutPut", "target": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "target_param": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04@@Content"}, {"id": "reactflow__edge-9ca21b35-baff-4f2b-9a77-cec5d05d9b049ca21b35-baff-4f2b-9a77-cec5d05d9b04@@OutPut-989bc203-459a-4e21-8961-f08c8f31ec82989bc203-459a-4e21-8961-f08c8f31ec82@@Elements", "source": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04", "source_param": "9ca21b35-baff-4f2b-9a77-cec5d05d9b04@@OutPut", "target": "989bc203-459a-4e21-8961-f08c8f31ec82", "target_param": "989bc203-459a-4e21-8961-f08c8f31ec82@@Elements"}, {"id": "reactflow__edge-694d4529-0191-4701-89b1-7732e7560b89694d4529-0191-4701-89b1-7732e7560b89@@OutPut-b87ac904-3e66-4894-a7e2-d2842d6b5b9bb87ac904-3e66-4894-a7e2-d2842d6b5b9b@@Content", "source": "694d4529-0191-4701-89b1-7732e7560b89", "source_param": "694d4529-0191-4701-89b1-7732e7560b89@@OutPut", "target": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "target_param": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b@@Content"}, {"id": "reactflow__edge-b87ac904-3e66-4894-a7e2-d2842d6b5b9bb87ac904-3e66-4894-a7e2-d2842d6b5b9b@@OutPut-989bc203-459a-4e21-8961-f08c8f31ec82989bc203-459a-4e21-8961-f08c8f31ec82@@Chunks", "source": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b", "source_param": "b87ac904-3e66-4894-a7e2-d2842d6b5b9b@@OutPut", "target": "989bc203-459a-4e21-8961-f08c8f31ec82", "target_param": "989bc203-459a-4e21-8961-f08c8f31ec82@@Chunks"}, {"id": "reactflow__edge-3184e214-86fe-48f5-b3f2-8bf0bee818a93184e214-86fe-48f5-b3f2-8bf0bee818a9@@OutPut-0951d7e1-98eb-4799-b1f5-39099b1632b00951d7e1-98eb-4799-b1f5-39099b1632b0@@Content", "source": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "source_param": "3184e214-86fe-48f5-b3f2-8bf0bee818a9@@OutPut", "target": "0951d7e1-98eb-4799-b1f5-39099b1632b0", "target_param": "0951d7e1-98eb-4799-b1f5-39099b1632b0@@Content"}, {"id": "reactflow__edge-a13eb23d-421d-4092-a8b7-7a559d0ce294a13eb23d-421d-4092-a8b7-7a559d0ce294@@OutPut-3184e214-86fe-48f5-b3f2-8bf0bee818a93184e214-86fe-48f5-b3f2-8bf0bee818a9@@File", "source": "a13eb23d-421d-4092-a8b7-7a559d0ce294", "source_param": "a13eb23d-421d-4092-a8b7-7a559d0ce294@@OutPut", "target": "3184e214-86fe-48f5-b3f2-8bf0bee818a9", "target_param": "3184e214-86fe-48f5-b3f2-8bf0bee818a9@@File"}], "viewport": {"x": -91.39273030354457, "y": -53.69631666301234, "zoom": 0.7453551933994605}}, "created_time": 0, "updated_time": 0}