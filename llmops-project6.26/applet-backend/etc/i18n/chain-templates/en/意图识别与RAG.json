{"id": "意图识别与RAG.json", "name": "Intent Recognition and RAG", "desc": "Use a question classifier to identify user intent, then handle situations accordingly. In this case, based on the type of user's question, answers are provided by either searching the knowledge base or conducting a web search.", "template_group_key": "IntentRecognition", "template": {"nodes": [{"id": "43ddc89d-923c-49cf-93db-12ef30a55f9b", "name": "Knowledge Base Search", "widget_id": "WidgetKeyTextKnowledgeSearch", "widget_detail": {"id": "WidgetKeyTextKnowledgeSearch", "name": "Knowledge Base Search", "desc": "Retrieve from the knowledge base based on the input text, and sort and filter the results.", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyTextKnowledgeSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Input", "desc": "Retrieve the issue, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "The final number of retained search results", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "The minimum threshold requirement for retaining search results", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "Cross-Knowledge Base Retrieval", "desc": "When setting up cross-knowledge base search, it is necessary to configure the rerank model to improve search effectiveness.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "Knowledge Base Information", "desc": "Knowledge Base Information", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "Knowledge Base Information", "desc": "Knowledge Base and Document Scope for Limited Search", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Selecting models for re-ranking and filtering retrieval results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Recall paragraphs from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":419,\"id\":\"43ddc89d-923c-49cf-93db-12ef30a55f9b\",\"position\":{\"x\":885.4989649819518,\"y\":-373.85993902456926},\"positionAbsolute\":{\"x\":885.4989649819518,\"y\":-373.85993902456926},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1}", "values": {"EnableMutil": false, "RerankThreshold": 0, "RerankTopK": 3, "SimpleKbInfo": "{\"id\":\"0e500c1c-69cd-4ea3-b350-16c0ee07de45\",\"is_public\":true}"}, "sub_chain_base_info": null}, {"id": "b6f8f045-584e-4bf6-b052-723c23a3e975", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"b6f8f045-584e-4bf6-b052-723c23a3e975\",\"position\":{\"x\":-117.7792817526456,\"y\":-143.16518330551665},\"positionAbsolute\":{\"x\":-117.7792817526456,\"y\":-143.16518330551665},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "f762db95-45c6-4240-8416-745c4be302c9", "name": "Issue Classification", "widget_id": "WidgetKeyQuestionClassifier", "widget_detail": {"id": "WidgetKeyQuestionClassifier", "name": "Intent Recognition (Input Classification)", "desc": "Classify user input questions and control the data flow to different categories.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyQuestionClassifier", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Unclassified Questions", "desc": "Unclassified question text, the required input data is string", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for problem classification", "datasource": "{\"showPublic\":true,\"showRemote\":true,\"onlyAvailable\":true,\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"includeMWService\":true,\"rawInValue\":true}", "required": true, "type": "TYPE_MODEL_SERVICE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Category-1", "name": "Category 1", "desc": "Category 1", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "", "category": "out-port", "preview": false, "define": {"id": "Output-1", "name": "Category 1", "desc": "Category 1", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Category-2", "name": "Category 2", "desc": "Category 2", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "", "category": "out-port", "preview": false, "define": {"id": "Output-2", "name": "Category 2", "desc": "Category 2", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Category-ae596c6b-ada3-489d-871d-e034280700a7", "name": "Category 3", "desc": "Category 3", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "", "category": "out-port", "preview": false, "define": {"id": "Output-ae596c6b-ada3-489d-871d-e034280700a7", "name": "Category 3", "desc": "Category 3", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":475,\"id\":\"f762db95-45c6-4240-8416-745c4be302c9\",\"position\":{\"x\":408.4562389444943,\"y\":-326.070661824126},\"positionAbsolute\":{\"x\":408.4562389444943,\"y\":-326.070661824126},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Category-1": "Sophon <PERSON>MOps产品问题", "Category-2": "公司IT相关问题，无线网络、打印机等", "Category-ae596c6b-ada3-489d-871d-e034280700a7": "其他或者利用互联网查询即时信息", "ModelService": "{\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"Qwen2.5-72b-instruct-11\",\"name\":\"Qwen2.5-72b-instruct-11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5-72b-instruct-11\",\"name_for_human\":\"对话模型:Qwen2.5-72b-instruct-11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"desc\":\"\",\"create_time_ms\":\"1727252060000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg\",\"name\":\"Qwen2.5-72b-instruct-11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上, 不好用的话找@付鑫或者@天义\",\"user_id\":\"demo\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1730367565346\",\"labels\":{\"场景\":\"文本生成\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"Qwen2.5-72b-instruct-11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"virtual_svc_url\":\"remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions\",\"security_config_id\":\"cqopj4l74ikh414dlqv0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/llmops/tenants/llmops-assets/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fllmops-assets%2Fprojs%2Fassets%2Favatar%2Ffbbc637b-51fe-4270-9906-f200d0e35395_QWEN.png\"},\"update_time_ms\":\"1730795024000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqopj4l74ikh414dlqv0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8644725351081168:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\"}"}, "sub_chain_base_info": null}, {"id": "39539260-eafd-4131-990e-c51f2f73b94f", "name": "Data Convergence", "widget_id": "WidgetKeyUnion", "widget_detail": {"id": "WidgetKeyUnion", "name": "Data Convergence", "desc": "Form a queue from multiple upstream inputs and output them serially.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyUnion", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Upstream data, any type", "desc": "The upstream data to be merged must ensure that only one branch has data flowing into this operator; otherwise, please use the data merging operator. Supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Output the upstream data as is, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":132,\"id\":\"39539260-eafd-4131-990e-c51f2f73b94f\",\"position\":{\"x\":1379.5433073161312,\"y\":-222.9421134789786},\"positionAbsolute\":{\"x\":1379.5433073161312,\"y\":-222.9421134789786},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "4b9cecd6-10a6-4f27-bad6-966376a61079", "name": "Paragraph merging", "widget_id": "WidgetKeyMergeParagraph", "widget_detail": {"id": "WidgetKeyMergeParagraph", "name": "Slice to Long Text", "desc": "Chunks to Text", "group": "WidgetGroupProcessKnowledge", "oriWidgetKey": "WidgetKeyMergeParagraph", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input", "desc": "Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Custom code", "desc": "Custom code for processing logic according to requirements", "default_value": "\n{{ $newline := \"\\n\" }}\n{{ range $index,$chunk := . }}\n{{ $chunk.Content}}{{ $newline }}\n{{ end}}\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Merged text paragraph, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"4b9cecd6-10a6-4f27-bad6-966376a61079\",\"position\":{\"x\":1762.1142456730333,\"y\":-146.41065465415082},\"positionAbsolute\":{\"x\":1762.1142456730333,\"y\":-146.41065465415082},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n{{ $newline := \"\\n\" }}\n{{ range $index,$chunk := . }}\n{{ $chunk.Content}}{{ $newline }}\n{{ end}}\n"}, "sub_chain_base_info": null}, {"id": "890ab631-e21b-4791-bf70-5a26faeaf6fe", "name": "rag", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "General Text Template", "desc": "Concatenate variables into a piece of text, allowing the text template to not reference any variables or reference multiple variables.", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": true, "define": {"id": "Template", "name": "Template", "default_value": "Use the content within the <data></data> XML  tags as your knowledge:\n<data>\n{{.quote}}\n</data>\nAnswer requirements:\n- If you are unsure of the answer, you need to clarify.\n- If the user's question is not related to your knowledge, please say you don't know.\n- Avoid mentioning that your knowledge comes from the data.\n- Keep your answer consistent with the description in the data.\n- Use Markdown syntax to optimize the formatting of your answer.\n- Only use knowledge relevant to the question to answer the user's questions, do not provide answers unrelated to the question.\n- Answer in Chinese.\nQuestion: {{.question}}\n", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "quote", "name": "quote", "desc": "quote", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":252,\"id\":\"890ab631-e21b-4791-bf70-5a26faeaf6fe\",\"position\":{\"x\":2146.1774034751825,\"y\":96.37880092806179},\"positionAbsolute\":{\"x\":2146.1774034751825,\"y\":96.37880092806179},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "Use the content within the <data></data> XML  tags as your knowledge:\n<data>\n{{.quote}}\n</data>\nAnswer requirements:\n- If you are unsure of the answer, you need to clarify.\n- If the user's question is not related to your knowledge, please say you don't know.\n- Avoid mentioning that your knowledge comes from the data.\n- Keep your answer consistent with the description in the data.\n- Use Markdown syntax to optimize the formatting of your answer.\n- Only use knowledge relevant to the question to answer the user's questions, do not provide answers unrelated to the question.\n- Answer in Chinese.\nQuestion: {{.question}}\n"}, "sub_chain_base_info": null}, {"id": "c5ee0089-b1bf-4f07-91e8-2acd2203a790", "name": "Text generation model", "widget_id": "WidgetKeyLLMModel", "widget_detail": {"id": "WidgetKeyLLMModel", "name": "Text Generation", "desc": "Used for invoking text generation model services", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyLLMModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Input prompt words for LLM, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "System Prompt", "desc": "System prompt words for the model, used to define AI behavior and response methods, to improve the accuracy of responses.", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "Dialogue History", "desc": "Dialogue History", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "LLM output text, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":287,\"id\":\"c5ee0089-b1bf-4f07-91e8-2acd2203a790\",\"position\":{\"x\":2654.807709917809,\"y\":133.0287254104918},\"positionAbsolute\":{\"x\":2654.807709917809,\"y\":133.0287254104918},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"Qwen2.5-72b-instruct-11\",\"name\":\"Qwen2.5-72b-instruct-11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5-72b-instruct-11\",\"name_for_human\":\"对话模型:Qwen2.5-72b-instruct-11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"desc\":\"\",\"create_time_ms\":\"1727252060000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg\",\"name\":\"Qwen2.5-72b-instruct-11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上, 不好用的话找@付鑫或者@天义\",\"user_id\":\"demo\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1730367565346\",\"labels\":{\"场景\":\"文本生成\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"Qwen2.5-72b-instruct-11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"virtual_svc_url\":\"remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions\",\"security_config_id\":\"cqopj4l74ikh414dlqv0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/llmops/tenants/llmops-assets/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fllmops-assets%2Fprojs%2Fassets%2Favatar%2Ffbbc637b-51fe-4270-9906-f200d0e35395_QWEN.png\"},\"update_time_ms\":\"1730795024000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqopj4l74ikh414dlqv0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8644725351081168:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\"}", "SystemPrompt": "You are a helpful assistant."}, "sub_chain_base_info": null}, {"id": "26a2fc3d-ccc6-493f-866b-f38891d685e8", "name": "Internet search", "widget_id": "WidgetKeyInternetSearch", "widget_detail": {"id": "WidgetKeyInternetSearch", "name": "Internet search", "desc": "Use a search engine to search for the input text", "group": "WidgetGroupVD", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text", "desc": "The text content that requires online search, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Enable", "name": "Whether to enable", "desc": "Whether to enable the Internet search operator", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Engine", "name": "Search Engine", "desc": "Optional search engine, default is Bing", "default_value": "BingSearch", "datasource": "BingSearch@@必应搜索", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ParseUrl", "name": "Parse web pages", "desc": "Whether to parse the detailed content of web pages obtained from search engines, default is not to parse.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "Output", "desc": "Search results, supported type: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"26a2fc3d-ccc6-493f-866b-f38891d685e8\",\"position\":{\"x\":919.2626495187335,\"y\":281.65701901171826},\"positionAbsolute\":{\"x\":919.2626495187335,\"y\":281.65701901171826},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Enable": true, "Engine": "BingSearch", "ParseUrl": false}, "sub_chain_base_info": null}, {"id": "c356788c-6c3d-46ab-9e85-ed2998763ec2", "name": "Knowledge Base Search", "widget_id": "WidgetKeyTextKnowledgeSearch", "widget_detail": {"id": "WidgetKeyTextKnowledgeSearch", "name": "Knowledge Base Search", "desc": "Retrieve from the knowledge base based on the input text, and sort and filter the results.", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyTextKnowledgeSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Input", "desc": "Retrieve the issue, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "The final number of retained search results", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "The minimum threshold requirement for retaining search results", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "Cross-Knowledge Base Retrieval", "desc": "When setting up cross-knowledge base search, it is necessary to configure the rerank model to improve search effectiveness.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "Knowledge Base Information", "desc": "Knowledge Base Information", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "Knowledge Base Information", "desc": "Knowledge Base and Document Scope for Limited Search", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Selecting models for re-ranking and filtering retrieval results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Recall paragraphs from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":419,\"id\":\"c356788c-6c3d-46ab-9e85-ed2998763ec2\",\"position\":{\"x\":884.2394366314809,\"y\":-833.7206432109899},\"positionAbsolute\":{\"x\":884.2394366314809,\"y\":-833.7206432109899},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"EnableMutil": false, "RerankThreshold": 0, "RerankTopK": 3, "SimpleKbInfo": "{\"id\":\"3c76e857-d3fb-42b3-8bf1-97957607e616\",\"is_public\":true}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-b6f8f045-584e-4bf6-b052-723c23a3e975b6f8f045-584e-4bf6-b052-723c23a3e975@@OutPut-f762db95-45c6-4240-8416-745c4be302c9f762db95-45c6-4240-8416-745c4be302c9@@Input", "source": "b6f8f045-584e-4bf6-b052-723c23a3e975", "source_param": "b6f8f045-584e-4bf6-b052-723c23a3e975@@OutPut", "target": "f762db95-45c6-4240-8416-745c4be302c9", "target_param": "f762db95-45c6-4240-8416-745c4be302c9@@Input"}, {"id": "reactflow__edge-39539260-eafd-4131-990e-c51f2f73b94f39539260-eafd-4131-990e-c51f2f73b94f@@OutPut-4b9cecd6-10a6-4f27-bad6-966376a610794b9cecd6-10a6-4f27-bad6-966376a61079@@Content", "source": "39539260-eafd-4131-990e-c51f2f73b94f", "source_param": "39539260-eafd-4131-990e-c51f2f73b94f@@OutPut", "target": "4b9cecd6-10a6-4f27-bad6-966376a61079", "target_param": "4b9cecd6-10a6-4f27-bad6-966376a61079@@Content"}, {"id": "reactflow__edge-4b9cecd6-10a6-4f27-bad6-966376a610794b9cecd6-10a6-4f27-bad6-966376a61079@@OutPut-890ab631-e21b-4791-bf70-5a26faeaf6fe890ab631-e21b-4791-bf70-5a26faeaf6fe@@quote", "source": "4b9cecd6-10a6-4f27-bad6-966376a61079", "source_param": "4b9cecd6-10a6-4f27-bad6-966376a61079@@OutPut", "target": "890ab631-e21b-4791-bf70-5a26faeaf6fe", "target_param": "890ab631-e21b-4791-bf70-5a26faeaf6fe@@quote"}, {"id": "reactflow__edge-b6f8f045-584e-4bf6-b052-723c23a3e975b6f8f045-584e-4bf6-b052-723c23a3e975@@OutPut-890ab631-e21b-4791-bf70-5a26faeaf6fe890ab631-e21b-4791-bf70-5a26faeaf6fe@@question", "source": "b6f8f045-584e-4bf6-b052-723c23a3e975", "source_param": "b6f8f045-584e-4bf6-b052-723c23a3e975@@OutPut", "target": "890ab631-e21b-4791-bf70-5a26faeaf6fe", "target_param": "890ab631-e21b-4791-bf70-5a26faeaf6fe@@question"}, {"id": "reactflow__edge-890ab631-e21b-4791-bf70-5a26faeaf6fe890ab631-e21b-4791-bf70-5a26faeaf6fe@@OutPut-c5ee0089-b1bf-4f07-91e8-2acd2203a790c5ee0089-b1bf-4f07-91e8-2acd2203a790@@Text", "source": "890ab631-e21b-4791-bf70-5a26faeaf6fe", "source_param": "890ab631-e21b-4791-bf70-5a26faeaf6fe@@OutPut", "target": "c5ee0089-b1bf-4f07-91e8-2acd2203a790", "target_param": "c5ee0089-b1bf-4f07-91e8-2acd2203a790@@Text"}, {"id": "reactflow__edge-f762db95-45c6-4240-8416-745c4be302c9f762db95-45c6-4240-8416-745c4be302c9@@Output-ae596c6b-ada3-489d-871d-e034280700a7-26a2fc3d-ccc6-493f-866b-f38891d685e826a2fc3d-ccc6-493f-866b-f38891d685e8@@Input", "source": "f762db95-45c6-4240-8416-745c4be302c9", "source_param": "f762db95-45c6-4240-8416-745c4be302c9@@Output-ae596c6b-ada3-489d-871d-e034280700a7", "target": "26a2fc3d-ccc6-493f-866b-f38891d685e8", "target_param": "26a2fc3d-ccc6-493f-866b-f38891d685e8@@Input"}, {"id": "reactflow__edge-26a2fc3d-ccc6-493f-866b-f38891d685e826a2fc3d-ccc6-493f-866b-f38891d685e8@@Output-39539260-eafd-4131-990e-c51f2f73b94f39539260-eafd-4131-990e-c51f2f73b94f@@Input", "source": "26a2fc3d-ccc6-493f-866b-f38891d685e8", "source_param": "26a2fc3d-ccc6-493f-866b-f38891d685e8@@Output", "target": "39539260-eafd-4131-990e-c51f2f73b94f", "target_param": "39539260-eafd-4131-990e-c51f2f73b94f@@Input"}, {"id": "reactflow__edge-f762db95-45c6-4240-8416-745c4be302c9f762db95-45c6-4240-8416-745c4be302c9@@Output-1-c356788c-6c3d-46ab-9e85-ed2998763ec2c356788c-6c3d-46ab-9e85-ed2998763ec2@@Question", "source": "f762db95-45c6-4240-8416-745c4be302c9", "source_param": "f762db95-45c6-4240-8416-745c4be302c9@@Output-1", "target": "c356788c-6c3d-46ab-9e85-ed2998763ec2", "target_param": "c356788c-6c3d-46ab-9e85-ed2998763ec2@@Question"}, {"id": "reactflow__edge-c356788c-6c3d-46ab-9e85-ed2998763ec2c356788c-6c3d-46ab-9e85-ed2998763ec2@@OutPut-39539260-eafd-4131-990e-c51f2f73b94f39539260-eafd-4131-990e-c51f2f73b94f@@Input", "source": "c356788c-6c3d-46ab-9e85-ed2998763ec2", "source_param": "c356788c-6c3d-46ab-9e85-ed2998763ec2@@OutPut", "target": "39539260-eafd-4131-990e-c51f2f73b94f", "target_param": "39539260-eafd-4131-990e-c51f2f73b94f@@Input"}, {"id": "reactflow__edge-f762db95-45c6-4240-8416-745c4be302c9f762db95-45c6-4240-8416-745c4be302c9@@Output-2-43ddc89d-923c-49cf-93db-12ef30a55f9b43ddc89d-923c-49cf-93db-12ef30a55f9b@@Question", "source": "f762db95-45c6-4240-8416-745c4be302c9", "source_param": "f762db95-45c6-4240-8416-745c4be302c9@@Output-2", "target": "43ddc89d-923c-49cf-93db-12ef30a55f9b", "target_param": "43ddc89d-923c-49cf-93db-12ef30a55f9b@@Question"}, {"id": "reactflow__edge-43ddc89d-923c-49cf-93db-12ef30a55f9b43ddc89d-923c-49cf-93db-12ef30a55f9b@@OutPut-39539260-eafd-4131-990e-c51f2f73b94f39539260-eafd-4131-990e-c51f2f73b94f@@Input", "source": "43ddc89d-923c-49cf-93db-12ef30a55f9b", "source_param": "43ddc89d-923c-49cf-93db-12ef30a55f9b@@OutPut", "target": "39539260-eafd-4131-990e-c51f2f73b94f", "target_param": "39539260-eafd-4131-990e-c51f2f73b94f@@Input"}], "viewport": {"x": -1654.8015007198683, "y": 162.57561774519587, "zoom": 0.7453551933994603}}, "created_time": 0, "updated_time": 0}