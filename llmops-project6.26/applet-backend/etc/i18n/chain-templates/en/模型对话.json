{"id": "模型对话.json", "name": "Model Dialogue", "desc": "Based on the standard OpenAI conversation interface, provide a streaming conversation interface + historical context recording capability application chain", "template_group_key": "BasicQA", "template": {"nodes": [{"id": "04f9e1c4-77a2-42cf-ba19-4327b9184814", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"04f9e1c4-77a2-42cf-ba19-4327b9184814\",\"position\":{\"x\":-433.0000159917994,\"y\":260.9999811736755},\"positionAbsolute\":{\"x\":-433.0000159917994,\"y\":260.9999811736755},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "name": "Jsonnet code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet code", "desc": "It can conveniently convert the structure of the output data upstream, such as extracting the value of a certain field or changing the field name, etc.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet code", "desc": "jsonnet code, click to view or edit the code", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "The execution result of jsonnet code supports the type: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"30c6c32e-3614-49e8-a1e2-cf5287efd83e\",\"position\":{\"x\":404.37203440609153,\"y\":427.8104115946886},\"positionAbsolute\":{\"x\":404.37203440609153,\"y\":427.8104115946886},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n{\n \"messages\": [  {\n   \"role\": \"system\",\n   \"content\": \"你是一个乐于助人的助手\"\n  },\n  {\n   \"role\": \"user\",\n   \"content\": \"以下是历史用户的历史对话：\\n\"+ input.history\n  },\n  {\n   \"role\": \"user\",\n   \"content\": input.query\n  }\n ],\n \"model\":\"atom\",\n \"stream\": true\n}\n\n"}, "sub_chain_base_info": null}, {"id": "2fa99347-86f6-4011-8bd2-dedcc24c4554", "name": "Python code", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python code", "desc": "Can write Python code to complete complex business logic, with pre-installed dependencies including requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "python code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"2fa99347-86f6-4011-8bd2-dedcc24c4554\",\"position\":{\"x\":1181.4424186566898,\"y\":554.2638666683615},\"positionAbsolute\":{\"x\":1181.4424186566898,\"y\":554.2638666683615},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\n# {\n#   \"Event\": \"message\",\n#   \"Id\": \"\",\n#   \"Retry\": 0,\n#   \"Data\": {\n#     \"choices\": [\n#       {\n#         \"delta\": {\n#           \"content\": \"需要，我可以为您提供帮助\"\n#         },\n#         \"finish_reason\": null,\n#         \"index\": 0,\n#         \"logprobs\": null\n#       }\n#     ],\n#     \"created\": 1718079522,\n#     \"id\": \"chatcmpl-d6d7e173-51d7-9924-ad21-ec8bd8a2dce1\",\n#     \"model\": \"qwen-turbo\",\n#     \"object\": \"chat.completion.chunk\",\n#     \"system_fingerprint\": null,\n#     \"usage\": null\n#   }\n# }\n\n# handle openai stream\ndef handler(data):\n    if \"Data\" not in data:\n        return \"\"\n    data = data[\"Data\"]\n\n    if \"choices\" not in data:\n        return \"\"\n    data = data[\"choices\"]\n\n    if len(data) <= 0:\n        return \"\"\n    data = data[0]\n    if \"delta\" not in data:\n        return \"\"\n    data = data[\"delta\"]\n\n    if \"content\" not in data:\n        return \"\"\n    return data[\"content\"]\n\n# handle openai non-stream\n# def handler(data):\n\n#     if \"choices\" not in data:\n#         return \"\"\n#     data = data[\"choices\"]\n\n#     if len(data) <= 0:\n#         return \"\"\n#     data = data[0]\n#     if \"message\" not in data:\n#         return \"\"\n#     data = data[\"message\"]\n\n#     if \"content\" not in data:\n#         return \"\"\n#     return data[\"content\"]\n"}, "sub_chain_base_info": null}, {"id": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91", "name": "Data Integration", "widget_id": "WidgetKeyInputAggregation", "widget_detail": {"id": "WidgetKeyInputAggregation", "name": "Data Integration", "desc": "Compose multiple upstream inputs into a JSON output in KV format (output is triggered only when all data is available)", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyInputAggregation", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "query", "name": "query", "desc": "query", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "history", "name": "history", "desc": "history", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"f81d9924-a5da-4e7a-ad46-e4d3c9b1df91\",\"position\":{\"x\":33.31733271790779,\"y\":399.6589828026293},\"positionAbsolute\":{\"x\":33.31733271790779,\"y\":399.6589828026293},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "3a059d91-3842-468a-9bda-0852fdd4b329", "name": "chat/completions", "widget_id": "WidgetKeyApiCall", "widget_detail": {"id": "WidgetKeyApiCall", "name": "General HTTP Invocation", "desc": "Callable to all HTTP API services, currently only supports POST requests.", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyApiCall", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "APIPath", "name": "Request Address", "desc": "URL address, for example http://example.com/api/v1", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Header", "name": "Request Header", "desc": "Request headers, such as Content-Type = application/json or Authorization = Bearer xxx", "multiple": true, "type": "TYPE_KVITEM", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "RequestBody", "name": "Request Body", "desc": "The HTTP request body should be a valid HTTP request data type, such as a dictionary, list, number, or string. Supported type: Sync-Any: \"any data type\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "http request response data may be a dictionary, list, number, string, or any other valid http response data. Supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":364,\"id\":\"3a059d91-3842-468a-9bda-0852fdd4b329\",\"position\":{\"x\":798.3122226823828,\"y\":373.0538967845622},\"positionAbsolute\":{\"x\":798.3122226823828,\"y\":373.0538967845622},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"APIPath": "http://*************:8011/openai/v1/chat/completions", "Header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer xxx"}]}, "sub_chain_base_info": null}, {"id": "1a6be285-684c-4d68-a48f-a3b1ae69c6ab", "name": "Dialogue History", "widget_id": "WidgetKeyChatHistory", "widget_detail": {"id": "WidgetKeyChatHistory", "name": "Dialogue History", "desc": "Used to provide historical dialogue as background knowledge for downstream prompt templates", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyChatHistory", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "ChatInput", "name": "Dialogue Input", "desc": "Support Type: Sync-QAItems: [\n  {\n    \"Q\": \"Q1\",\n    \"A\": \"A1\"\n  },\n  {\n    \"Q\": \"Q2\",\n    \"A\": \"A2\"\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-QAItems"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxRounds", "name": "Maximum number of dialogue turns", "desc": "The maximum number of dialogue turns retained in the historical conversation, any turns exceeding this limit will be automatically truncated. Appropriately setting this attribute can effectively prevent exceeding the model's maximum context limit.", "number_range": {"min": 1, "max": 99, "step": 1}, "default_value": "5", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Tmpl", "name": "Dialogue Template", "desc": "Using GoTemplate to Concatenate the Input Conversation History Structure into a String", "default_value": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}", "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":257,\"id\":\"1a6be285-684c-4d68-a48f-a3b1ae69c6ab\",\"position\":{\"x\":-461.3501599389477,\"y\":586.9824969330458},\"positionAbsolute\":{\"x\":-461.3501599389477,\"y\":586.9824969330458},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"MaxRounds": 5, "Tmpl": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-04f9e1c4-77a2-42cf-ba19-4327b918481404f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut-f81d9924-a5da-4e7a-ad46-e4d3c9b1df91f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@query", "source": "04f9e1c4-77a2-42cf-ba19-4327b9184814", "source_param": "04f9e1c4-77a2-42cf-ba19-4327b9184814@@OutPut", "target": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91", "target_param": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@query"}, {"id": "reactflow__edge-f81d9924-a5da-4e7a-ad46-e4d3c9b1df91f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@OutPut-30c6c32e-3614-49e8-a1e2-cf5287efd83e30c6c32e-3614-49e8-a1e2-cf5287efd83e@@Content", "source": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91", "source_param": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@OutPut", "target": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "target_param": "30c6c32e-3614-49e8-a1e2-cf5287efd83e@@Content"}, {"id": "reactflow__edge-30c6c32e-3614-49e8-a1e2-cf5287efd83e30c6c32e-3614-49e8-a1e2-cf5287efd83e@@OutPut-3a059d91-3842-468a-9bda-0852fdd4b3293a059d91-3842-468a-9bda-0852fdd4b329@@RequestBody", "source": "30c6c32e-3614-49e8-a1e2-cf5287efd83e", "source_param": "30c6c32e-3614-49e8-a1e2-cf5287efd83e@@OutPut", "target": "3a059d91-3842-468a-9bda-0852fdd4b329", "target_param": "3a059d91-3842-468a-9bda-0852fdd4b329@@RequestBody"}, {"id": "reactflow__edge-3a059d91-3842-468a-9bda-0852fdd4b3293a059d91-3842-468a-9bda-0852fdd4b329@@OutPut-2fa99347-86f6-4011-8bd2-dedcc24c45542fa99347-86f6-4011-8bd2-dedcc24c4554@@Content", "source": "3a059d91-3842-468a-9bda-0852fdd4b329", "source_param": "3a059d91-3842-468a-9bda-0852fdd4b329@@OutPut", "target": "2fa99347-86f6-4011-8bd2-dedcc24c4554", "target_param": "2fa99347-86f6-4011-8bd2-dedcc24c4554@@Content"}, {"id": "reactflow__edge-1a6be285-684c-4d68-a48f-a3b1ae69c6ab1a6be285-684c-4d68-a48f-a3b1ae69c6ab@@OutPut-f81d9924-a5da-4e7a-ad46-e4d3c9b1df91f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@history", "source": "1a6be285-684c-4d68-a48f-a3b1ae69c6ab", "source_param": "1a6be285-684c-4d68-a48f-a3b1ae69c6ab@@OutPut", "target": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91", "target_param": "f81d9924-a5da-4e7a-ad46-e4d3c9b1df91@@history"}], "viewport": {"x": -46.82615346699981, "y": -101.88829356418125, "zoom": 0.7937004868091029}}, "created_time": 0, "updated_time": 0}