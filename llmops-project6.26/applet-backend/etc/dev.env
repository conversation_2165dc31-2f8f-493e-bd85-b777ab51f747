APPLET_KAPACITOR_CONFIG.HEALTH_API_PATH=/api/v1/health
APPLET_THINGER_LOG.LOGGER.LEVEL=info
APPLET_MYSQL.HOST=autocv-mysql-service
APPLET_MLOPS.HOST=llmops-sophon-serving
APPLET_MLOPS.PORT=8752
APPLET_THINGER_LOG.LOGGER.CONSOLE=true
APPLET_TKH_CONFIG.HOST=*************:8090
APPLET_SERVER.HTTP_TIMEOUT=600s
APPLET_TKH_CONFIG.AUTHORIZATION=Bearer af2149c5-4197-4d4e-b2eb-85441a9af651
APPLET_DOC_SVC_CONFIG.ADDRESS=http://autocv-applet-doc-service
APPLET_PVC.NAME=llmops-pvc
APPLET_MYSQL.DB_NAME=applet_backend
APPLET_DOC_SVC_CONFIG.DEFAULT_STRATEGY=fast
APPLET_MYSQL.PASSWORD=Warp!CV@2022#
APPLET_LICENSE.CHECK_INTERVAL=300s
APPLET_PROMPT_CONFIG.HOST=autocv-cvat-service
APPLET_TKH_CONFIG.BASE_URL=
APPLET_HIPPO_CONFIG.DEFAULT_METRIC_TYPE=IP
APPLET_DEPLOY_CONFIG.PVC_MOUNT_CFGS=autocv-sfs-pvc##sfs/store:/applet-engine/data/store
APPLET_KAPACITOR_CONFIG.API_PATH=/api/v1
APPLET_DEFAULT_AGENT_CONFIG.KB_CONFIG.RERANK_MODEL_URL=dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001
APPLET_MWH.GRPC_PORT=31080
APPLET_KAPACITOR_CONFIG.ADDR=autocv-applet-service:9092
APPLET_MWH.DEFAULT_USER=thinger
APPLET_DEPLOY_CONFIG.BASE_IMAGE_CONF.BASE_IMAGE=***********/aip/applet-engine:dev
APPLET_MLOPS.GATEWAY_HOST=istio-ingressgateway.istio-system
APPLET_KAPACITOR_CONFIG.API_PORT=1884
APPLET_CSM_CONFIG.HTTP_PORT=80
APPLET_HIPPO_CONFIG.DEFAULT_INDEX_TYPE=FLAT
APPLET_DEFAULT_RERANK_MODEL.FULL_URL=dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001
APPLET_DEPLOY_CONFIG.BASE_IMAGE_CONF.PVC_NAME=autocv-sfs-pvc
APPLET_MLOPS.GATEWAY_PORT=80
APPLET_MQTT.BROKER_ADDR=autocv-mosquitto-service:1883
APPLET_MYSQL.PORT=3306
APPLET_HIPPO_CONFIG.PASSWORD=shiva
APPLET_MWH.HOST=autocv-mwh-service
APPLET_DOC_SVC_CONFIG.DEFAULT_SPLIT_TYPE=recursive
APPLET_THINGER_LOG.PATH=/opt/vision/node/.data/store/logs
APPLET_LICENSE.VERIFIER_PATH=/share/verifier
APPLET_HIPPO_CONFIG.DEFAULT_VEC_MODEL.FULL_URL=dlie://mwh-deployment-cm2jda1165iv5308eegg:8001
APPLET_SERVER.ADDR=:80
APPLET_ETCD.ADDR=http://autocv-sophon-etcd:2479
APPLET_DEPLOY_CONFIG.BASE_IMAGE_CONF.VOLUMES=sfs/store:/applet-engine/data/store
APPLET_CAS.URL=http://autocv-cas-service:80/api/v1/users
APPLET_TKH_CONFIG.ENABLED=false
APPLET_LICENSE.LICENSOR_ADDR=http://autocv-licensor-service
APPLET_HIPPO_CONFIG.USERNAME=shiva
APPLET_HIPPO_CONFIG.DATABASE=llmops-kb-131
APPLET_ETCD.DIAL_TIMEOUT=5
APPLET_PROMPT_CONFIG.PORT=80
APPLET_ENGINE.IN_CLUSTER=true
APPLET_KAPACITOR_CONFIG.EXECUTE_API_PORT=1884
APPLET_CSM_CONFIG.GRPC_PORT=31080
APPLET_KAPACITOR_CONFIG.DATABASE=thinger
APPLET_MYSQL.USERNAME=root
APPLET_CSM_CONFIG.HOST=autocv-csm-service
APPLET_DOC_SVC_CONFIG.DEFAULT_CHUNK_OVERLAP=100
APPLET_DOC_SVC_CONFIG.DEFAULT_CHUNK_SIZE=500
APPLET_DATABASE=mysql
APPLET_STORAGE_ROOT=/sfs
APPLET_IMAGE_CACHE=
APPLET_EDGE_ID=applet-backend
APPLET_BASE_LOCATION=
