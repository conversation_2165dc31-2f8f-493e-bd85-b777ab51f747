# 基础配置
edge_id: "local-test-edge"
is_simple_mode: false
edge_host: 127.0.0.1
disable_auth: false
database: mysql
storage_root: /sfs
token: Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA
server:
  addr: ":30080"
  http_timeout: 3600s

# 公共组件配置, 包含以下组件
# - Mysql     仅云端
# - License
license:
  verifier_path: "/usr/local/bin/verifier"
  licensor_addr: "http://autocv-licensor-service:80"
  check_interval: "5s"

thinger_log:
  # 日志所在默认目录
  path: "/opt/vision/node/.data/store/logs"
  # 后端服务的日志配置
  logger:
    # 日志输出级别
    level: debug
    # 日志是否输出到标准输出
    console: true

kapacitor_config:
  addr: **************:32693
  database: thinger
  health_api_path: /api/v1/health
  api_path: /api/v1
  api_port: 1884
  execute_api_port: 31714

mysql:
  username: root
  password: Warp!CV@2022#
  #  basic test on k3s 1.19
  host: **************
  port: 31907
  #  k3s 1.29 on Ubuntu22.04
  #  host: **************
  #  port: 31054
  #  host: *************
  #  port: 31398
  db_name: applet_backend
  max_idle: 5
  max_conn: 20


transport:
  type: redis

redis:
  addrs: "autocv-redis-service:6379"
  database: "0"
  username: "default"
  password: "Warp!CV@2022#"
  masterName: ""

#mqtt:
#  broker_addr: *************:31883
#  qos: 2
#  conn_timeout: 10s
#  persistent_session: true
#  store: data/mqtt/local/

# autocv-cvat-service
prompt_config:
  #  host: autocv-cvat-service
  host: *************
  #  port: 80
  port: 31030

# autocv-csm-service
csm_config:
  host: *************
  http_port: 32472
  grpc_port: 31562
  #  http_port: 30080
  #  grpc_port: 31080

cas_config:
  svc_name: autocv-cas-service

engine:
  engine_type: "k8s"  #docker or k8s
  #for docker_config
  docker_api_version: "1.32"
  # for k8s_config
  in_cluster: false
  kubeconfig_path: "etc/k3s.yaml"
  image_pull_secret: ""
  api_timeout: "30s"

deploy_config:
  call_back_base_url: https://autocv-portal-service:443/gateway/applet/api/v1/app/applications:publish-callback
  sfs_local_root : /sfs
  pvc_mount_cfgs : sfs-pvc##/:/sfs
  sfs_pvc_mount_path:
  # TODO FIXME 优化发布时参数配置
  kapacitor_host_name: autocv-applet-service.{namespace}
  kapacitor_influxdb_url: http://autocv-tsdb-service.{namespace}:8086
  kapacitor_mqtt_url: tcp://autocv-mosquitto-service.{namespace}:1883
  engine_mqtt_url: autocv-mosquitto-service.{namespace}:1883
  engine_knowledge_base_port: 31080
  engine_knowledge_base_host: autocv-applet-service.{namespace}
  engine_mlops_host: llmops-sophon-serving.{namespace}
  engine_run_code_url: http://autocv-testbed-service.{namespace}
  engine_security_url: http://autocv-applet-guardrails-service.{namespace}:8008
  redis:
    addrs: "autocv-redis-service.{namespace}:6379"
    database: "0"
    username: "default"
    password: "Warp!CV@2022#"
    masterName: ""

  base_image_conf:
    cpu_limit: 2
    cpu_request: 0.1
    enable_gpu: false
    privileged: false
    mem_request: 200m
    mem_limit: 2000m
    swap_limit: 500m
    restart: always
    base_image: ***********/aip/applet-engine:dev
    pvc_name: sfs-pvc

app_svc_config:
  init_interval: 30s
  health_interval: 30s
  dependency_interval: 30s

grpc_config:
  max_message_mb: 1024 # MB
  server_host: "localhost"
  server_port: ":31080"

mwh:
  #  # 83
  host: **************
  grpc_port: 31018
  # 207
  #  host: **************
  #  grpc_port: 30434
  default_user: thinger

cvat:
  host: autocv-cvat-service
  grpc_port: 31080
  http_port: 80

mlops:
  #  port: 31170
  #  host: *************
  #  port: 30740
  #  host: **************
  #  port: 30707
  host: **************
  port: 31157
  #  host: 127.0.0.1
  #  port: 8752
  #  gateway: istio-ingressgateway.istio-system
  #  gateway_port: 80
  #  gateway: *************
  #  gateway_port: 31380
  gateway_host: **************
  gateway_port: 31380
  istio_gateway_addr: http://**************:31380
  external_istio_gateway_addr: http://**************:31380

hippo_config:
  address: autocv-hippo-service
  port: 8902
  username: shiva
  password: shiva
  database: llmops-kb
  default_metric_type: IP
  default_index_type: FLAT
  max_chunk_page_size: 10000


scope_config:
  address: http://autocv-hippo-service
  port: 8902
  username: shiva
  password: shiva

hybase_config:
  url: http://trshybase-service.trs.svc.transwarp.local:5555
  username: admin
  password: trsadmin
  timeout: 60000
  replicas: 3

tkh_config:
  enabled: false
  host: *************:8090
  schema: http
  base_url: ""
  get_token_url: https://*************:28190/studio/api/auth/v1/token/getTestToken
  get_token_body: '{
    "clientId": "app",
    "userName": "admin",
    "password": "Warp1234",
    "clientSecret": "secret"
  }'

doc_svc_config:
  address: http://**************:31236
  load_api:
    url: /api/v1/doc:load
    method: POST
  split_api:
    url: /api/v1/doc:split
    method: POST
  load_split_api:
    url: /api/v1/doc:loadsplit
    method: POST
  loadraw_api:
    url: /api/v1/doc:loadraw
    method: POST
  splitraw_api:
    url: /api/v1/doc:splitraw
    method: POST
  load_chunk_api:
    url: /api/v1/doc:loadchunk
    method: POST
  chunk_api:
    url: /api/v1/doc:chunk
    method: POST
  healthz_api:
    url: /api/v1/healthz
    method: GET
  load_table_api:
    url: /api/v1/doc/table/content
    method: POST
  default_strategy: fast
  default_split_type: recursive
  default_chunk_size: 450
  default_chunk_overlap: 80

default_agent_config:
  kb_config:
    rerank_top_k: 3
    recall_top_k: 30
    threshold: 0.01
    name_prefix: agent-

knowlhub:
  # 上传文档处理落库任务的总超时时间
  task_timeout: 1h
  # 健康检查超时时间(不同检查项并行执行)
  healthz_timeout: 3m
  # 健康检查间隔时间
  healthz_interval: 10m
  # 多副本,解析过程中doc_task信息同步间隔
  doc_task_interval: 1s
  # 调用文档解析服务的最大并行度
  loading_max_concurrency: 4
  # chunks构建索引时分批的批大小
  vector_indexing_batch_size: 96
  fulltext_indexing_batch_size: 100
  # 知识增强生成提问时的温度
  augment_questions_temperature: 1.5
  augment_default_concurrency: 10
  vector_engine: HYBASE
  fulltext_engine: HYBASE
  embedding_max_length: 8000
  document_pk_update: false
  embedding_retry_count: 5
  embedding_retry_interval: 1s
  embedding_timeout: 2m
  embedding_model_min_concurrency: 5
  embedding_model_max_concurrency: 10
  refresh_document_chunks_num: false
  sync_chunks: false

api_tool:
  builtin_all_tools_switch: false
  builtin_tool_base_url: http://autocv-agent-tool-api-service.{namespace}
  default_proxy:
    schema: PROXY_SCHEME_HTTP
    url:
  builtin_openapi:
    url: http://autocv-agent-tool-api-service.{namespace}/v1/openapi_schemas
#    url: http://**************:32552/v1/openapi_schemas

invoke_config:
  svc_suffix: svc.transwarp.local

guardrails:
  full_update_addr: "http://autocv-applet-guardrails-service:8008/update_config"

code_service:
  test_python_url: "http://autocv-testbed-service:80"
  max_input_len: 16000

# Dify服务配置
dify_config:
  api_base_url: "http://************"
  workflow_base_url: "https://************/workflow/"
  chatbot_base_url: "https://************/chatbot/"
  bearer_token: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
  use_proxy: true
  proxy_url: "http://*************:3128"

doc_engine_config:
  address: **************:6632
  port: 6634