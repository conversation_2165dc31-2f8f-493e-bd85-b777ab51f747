package widgets

import (
	"github.com/go-faster/errors"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

type IWidgetFactory interface {
	GetWidget(widgetKey string) (IWidget, error)
	RegisterOrUpdate(widget IWidget) error
	Range(fun func(widget IWidget))
	GetWidgetDefine(widgetKey string) (*Widget, error)
}

type WidgetFactory struct {
	widgets map[string]IWidget
}

// GetWidgetDefine 获取算子在画布中的定义
func (w *WidgetFactory) GetWidgetDefine(widgetKey string) (*Widget, error) {
	widget, err := w.GetWidget(widgetKey)
	if err != nil {
		return nil, err
	}
	return helper.DeepCopy(widget.Define())
}

func (w *WidgetFactory) GetWidget(widgetKey string) (IWidget, error) {
	// 动态提示词模板 -> 提示词模板
	if widgetKey == WidgetKeyDynamicPrompt {
		widgetKey = WidgetKeySampleCubePrompt
	}
	widget := w.widgets[widgetKey]
	if widget == nil {
		return nil, helper.AppletChainBuildScriptErr.Errorf("can not find widget from factory with widgetKey: %s", widgetKey)
	}
	return widget, nil
}

func (w *WidgetFactory) Range(fun func(widget IWidget)) {
	for _, widget := range w.widgets {
		fun(widget)
	}
}

func (w *WidgetFactory) RegisterOrUpdate(widget IWidget) error {
	if widget == nil {
		return errors.Errorf("widget is nil")
	}
	if w.widgets == nil {
		w.widgets = make(map[string]IWidget)
	}
	w.widgets[widget.Define().Id] = widget
	return nil
}

func (w *WidgetFactory) register(widget IWidget) {
	if widget == nil {
		panic(errors.Errorf("widget is nil"))
	}

	widgetDefine := widget.Define()
	paramIdMap := make(map[string]bool)
	for _, p := range widgetDefine.Params {
		// 1、检查paramId是否合法
		paramId := p.Define.Id
		if _, exist := paramIdMap[paramId]; exist {
			panic(errors.Errorf("paramId: %s already exists for widget[%s]", paramId, widgetDefine.Id))
		}
		paramIdMap[paramId] = true

		// 2、检查算子的input与output是否进行参数限制
		if !(p.IsInput() || p.IsOutPut()) {
			continue
		}
		limit := p.ParamLimits
		if limit == nil || len(limit.Types) == 0 {
			panic(errors.Errorf("please set %s param limits for widget with id: %s", p.Category, widgetDefine.Id))
		}
	}
	if w.widgets == nil {
		w.widgets = make(map[string]IWidget)
	}

	widgetKey := widgetDefine.Id
	if widgetKey == "" {
		panic(errors.Errorf("widget id with name[%s] is nil", widgetDefine.Name))
	}
	if _, ok := w.widgets[widgetKey]; ok {
		panic(errors.Errorf("widget :%v already exists", widgetDefine.Id))
	}
	var err error
	widgetDefine.MdFullDesc, err = genMdDesc(false, widgetDefine)
	if err != nil {
		panic(err)
	}
	widgetDefine.MdSummaryDesc, err = genMdDesc(true, widgetDefine)
	if err != nil {
		panic(err)
	}

	w.widgets[widgetKey] = widget
}

var WidgetFactoryImpl IWidgetFactory

func initWidgetFactory() {
	factory := &WidgetFactory{}
	factory.register(fileInputs{})
	factory.register(textParse{})
	// factory.register(textSensitiveFilter{}) 废弃

	factory.register(generalSplitter{})
	//factory.register(textSplitter{})
	//factory.register(recursiveTextSplitter{})
	//factory.register(jsonTextSplitter{})
	//factory.register(codeTextSplitter{})
	//factory.register(htmlTextSplitter{})
	//factory.register(markdownTextSplitter{})
	//factory.register(tokenTextSplitter{})

	factory.register(llm{})
	factory.register(vectorModel{})
	factory.register(dlieModel{})
	factory.register(vectorInputs{})
	factory.register(vectorSearch{})
	factory.register(textInputs{})
	// factory.register(dynamicPrompt{})
	factory.register(textTemplate{})
	factory.register(subChain{})
	// factory.register(funcCall{})
	factory.register(jsonnetWidget{})
	factory.register(pythonWidget{})
	// factory.register(batchFileInputs{})
	factory.register(apiCall{})
	factory.register(codeInstance{})

	factory.register(fileSave{})
	factory.register(textShow{})
	factory.register(externalAPICall{})
	factory.register(sampleCubePrompt{})
	factory.register(upstreamInputs{})
	factory.register(inputAggregation{})
	factory.register(aggregateStream{})
	factory.register(customWidget{})
	factory.register(customPrompt{})
	factory.register(chatHistory{})
	factory.register(Agent{})
	factory.register(TextKnowledgeSearch{})
	//factory.register(SingleTextKnowledgeSearch{})
	factory.register(textKnowledgeInsert{})
	factory.register(toolCall{})
	factory.register(textEnhance{})
	factory.register(textRerank{})
	factory.register(textOutput{})
	factory.register(conditionJudge{})
	factory.register(wGoTo{})
	factory.register(union{})
	factory.register(fullTextInsert{})
	factory.register(fullTextSearch{})
	factory.register(questionClassifierOutput{})
	factory.register(chunksOutput{})
	factory.register(questionClassifier{})
	factory.register(inputGuardrail{})
	factory.register(outputGuardrail{})

	factory.register(parameterExtractor{})
	factory.register(mergeParagraph{})
	factory.register(textToImage{})
	factory.register(imageAnalysis{})
	factory.register(audioToText{})
	factory.register(internetSearch{})
	factory.register(QaSearch{})
	factory.register(paragraphAggregator{})

	WidgetFactoryImpl = factory
}
