package script

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

type QaSearch struct {
	Meta   *BaseScript     `json:"meta"`
	Params MultilineString `json:"params"`
}

func (f QaSearch) ScriptName() string {
	return "qaSearch"
}

type WidgetParamsQaSearch struct {
	ScoreThreshold float64                          `json:"score_threshold"`
	AnswerField    string                           `json:"answer_field"`
	KnowledgeBases *agent_definition.KnowledgeBases `json:"knowledge_bases"`
}

func (w *WidgetParamsQaSearch) Validate() error {
	if w.ScoreThreshold < QaSearchMinScoreThreshold || w.ScoreThreshold > QaSearchMaxScoreThreshold {
		return stderr.Errorf("ScoreThreshold must be in range [%f, %f]", QaSearchMinScoreThreshold, QaSearchMaxScoreThreshold)
	}
	if w.AnswerField == "" {
		stdlog.Warnf("AnswerField is empty, using default value %s", QaSearchDefaultAnswerField)
		w.AnswerField = QaSearchDefaultAnswerField
	}
	// 当知识库不为nil时， 校验知识库
	if w.KnowledgeBases != nil {
		// 当包含多个知识库时，需要配置rerank model
		if len(w.KnowledgeBases.KnowledgeBaseDesc) > 1 && (w.KnowledgeBases.RerankParams == nil || w.KnowledgeBases.RerankParams.Model == nil) {
			return stderr.Errorf("must set rerank model to do cross search")
		}
		// 跨知识库检索需要设置 topk与score threshold 参数
		w.KnowledgeBases.RerankParams.TopK = int32(QaSearchDefaultRerankTopK)
		w.KnowledgeBases.RerankParams.ScoreThreshold = float32(w.ScoreThreshold)
	}
	return nil
}
