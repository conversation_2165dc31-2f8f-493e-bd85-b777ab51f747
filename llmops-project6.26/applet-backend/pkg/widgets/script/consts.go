package script

const (
	KB = 1024
	MB = 1024 * KB
	GB = 1024 * MB
	TB = 1024 * GB

	FileInputMinMaxFileSizeMB         = 1
	FileInputMaxMaxFileSizeMB         = 200
	FileInputDefaultMaxFileSizeMB     = 20
	FileInputDefaultAllowedExtensions = `["*"]`

	QaSearchMinScoreThreshold     = 0.0
	QaSearchMaxScoreThreshold     = 1.0
	QaSearchDefaultScoreThreshold = 0.8
	QaSearchScoreThresholdStep    = 0.01
	QaSearchDefaultAnswerField    = "Answer"
	QaSearchDefaultRerankTopK     = 3 // 只需要第一个用于标准回复，召回3个用于打印调试信息

	DlieInferDefaultSystemPrompt         = "You are a helpful assistant."
	DlieInferMinTemperature      float64 = 0.0
	DlieInferMaxTemperature      float64 = 1.0
	DlieInferDefaultTemperature  float64 = 0.7
	DlieInferTemperatureStep     float64 = 0.1
	DlieInferMinTopP             float64 = 0.0
	DlieInferMaxTopP             float64 = 1.0
	DlieInferDefaultTopP         float64 = 1.0
	DlieInferTopPStep            float64 = 0.1
	DlieInferMinMaxTokens        int64   = 1
	DlieInferMaxMaxTokens        int64   = 1024 * 32 // 最大32k输出
	DlieInferDefaultMaxTokens    int64   = 1024 * 4  // 默认4k输出
	DlieInferMaxTokensStep       int64   = 100

	TextEnhanceDefaultQuestionNum = 3
	TextEnhanceMinQuestionNum     = 1
	TextEnhanceMaxQuestionNum     = 10

	GoToDefaultMaxLoopRounds = 5
	GoToMinMaxLoopRounds     = 1
	GoToMaxMaxLoopRounds     = 100
)

var (
	FileInputDefaultAllowedExtensionsSlice = []string{"*"}
)
