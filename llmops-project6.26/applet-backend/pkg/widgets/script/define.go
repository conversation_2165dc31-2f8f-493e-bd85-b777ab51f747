package script

import (
	"fmt"
	"reflect"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type Generator interface {
	// ScriptName 脚本名
	ScriptName() string
}

// TimeoutSecond 超时时间，单位为秒
type TimeoutSecond uint32

// MultilineString 多行string
type MultilineString string

// Val 定义过的变量
type Val string

func GenScript(clz Generator) (string, error) {
	builder := strings.Builder{}
	myReflectValue := reflect.ValueOf(clz)
	myReflectType := myReflectValue.Type()
	builder.WriteString(clz.ScriptName())
	builder.WriteString("(")
	// 构造参数
	for i := 0; i < myReflectType.NumField(); i++ {
		fieldStruct := myReflectType.Field(i)
		if !isConstruct(fieldStruct) {
			continue
		}
		fieldValue := myReflectValue.Field(i)
		value := fieldValue.Interface()
		// builder.WriteString("'''")
		builder.WriteString(fmt.Sprintf("%v", value))
		// builder.WriteString("'''")
		// 目前仅支持一个构造参数
		break
	}
	builder.WriteString(")")
	// meta参数
	for i := 0; i < myReflectType.NumField(); i++ {
		fieldStruct := myReflectType.Field(i)
		if isConstruct(fieldStruct) {
			continue
		}
		if !isMeta(fieldStruct) {
			continue
		}
		fieldValue := myReflectValue.Field(i)
		value := fieldValue.Interface()
		baseScript, ok := value.(*BaseScript)
		if !ok || baseScript == nil {
			return "", stderr.Internal.Error("invalid meta value :%v", value)
		}
		rv := reflect.ValueOf(*baseScript)
		rt := rv.Type()
		for j := 0; j < rt.NumField(); j++ {
			metaFieldStruct := rt.Field(j)
			if metaFieldStruct.Name == "SubChainWidgetID" {
				continue
			}
			metaFieldValue := rv.Field(j)
			metaFieldScript, err := genFiledScript(metaFieldStruct, metaFieldValue)
			if err != nil {
				return "", err
			}
			builder.WriteString(metaFieldScript)
		}
	}
	// 普通参数
	for i := 0; i < myReflectType.NumField(); i++ {
		fieldStruct := myReflectType.Field(i)
		if isMeta(fieldStruct) {
			continue
		}
		if isConstruct(fieldStruct) {
			continue
		}
		fieldValue := myReflectValue.Field(i)
		script, err := genFiledScript(fieldStruct, fieldValue)
		if err != nil {
			return "", err
		}
		builder.WriteString(script)
	}
	return builder.String(), nil
}

func isMeta(fieldStruct reflect.StructField) bool {
	if fieldStruct.Name == "Meta" {
		return true
	}
	return false
}

func isConstruct(fieldStruct reflect.StructField) bool {
	scriptTag := fieldStruct.Tag.Get("scriptType")
	if scriptTag == "constructor" {
		return true
	}
	return false
}

func genParamValue(fieldTypeStr string, fieldValue interface{}) (string, error) {
	builder := strings.Builder{}
	if fieldValue != nil {
		switch fieldTypeStr {
		case "int8", "int64", "int16", "int32",
			"uint8", "uint16", "uint32", "uint64":
			{
				builder.WriteString(fmt.Sprintf("%d", fieldValue))
			}
		case "float32", "float64":
			{
				builder.WriteString(fmt.Sprintf("%f", fieldValue))
			}
		case "script.TimeoutSecond":
			{
				builder.WriteString(fmt.Sprintf("%v", fieldValue))
				builder.WriteString("s")
			}
		case "string", "script.MultilineString", "script.HTTPCallServiceType":
			{
				builder.WriteString("'''")
				// [error]构建应用链脚本失败 : parser: unexpected string line 24 char 4 in "''''')". expected: ")"
				vs := fmt.Sprintf("%v", fieldValue)
				if strings.HasPrefix(vs, "'") {
					vs = " " + vs
				}
				if strings.HasSuffix(vs, "'") {
					vs = vs + " "
				}
				if strings.Contains(vs, "'''") {
					return "", stderr.Internal.Error("invalid string value, cannot contains \"'''\"  :%v", fieldValue)
				}
				builder.WriteString(vs)
				builder.WriteString("'''")
			}
		case "script.Val":
			{
				builder.WriteString(fmt.Sprintf("%v", fieldValue))
			}
		case "bool":
			{
				if fieldValue.(bool) {
					builder.WriteString("TRUE")
				} else {
					builder.WriteString("FALSE")
				}
			}
		default:
			return "", stderr.Internal.Error("invalid type :%v", fieldTypeStr)
		}
	}
	return builder.String(), nil
}

func genFiledScript(fieldStruct reflect.StructField, fieldValue reflect.Value) (string, error) {
	jsonTag := fieldStruct.Tag.Get("json")
	param := jsonTag
	if jsonTag == "" {
		param = fieldStruct.Name
	}
	builder := strings.Builder{}
	builder.WriteString("\n")
	builder.WriteString(".")
	builder.WriteString(param)
	builder.WriteString("(")
	fieldType := fieldValue.Type().String()
	value := fieldValue.Interface()
	paramValueStr, err := genParamValue(fieldType, value)
	if err != nil {
		return "", err
	}
	builder.WriteString(paramValueStr)
	builder.WriteString(")")
	return builder.String(), nil
}

type MockScript struct {
	MockParam string `json:"mockParam"`
}

func (m MockScript) ScriptName() string {
	return "mockScript"
}
