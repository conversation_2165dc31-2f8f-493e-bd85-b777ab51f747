package script

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

type ParameterExtractor struct {
	Meta   *BaseScript     `json:"meta"`
	Params MultilineString `json:"params"`
}

func (p ParameterExtractor) ScriptName() string {
	return "parameterExtractor"
}

type WidgetParamsParameterExtractor struct {
	ModelService *pb.ModelService                 `json:"model_service"`
	Params       []*agent_definition.APIToolParam `json:"params"`
}
