package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeyPythonWidget 自定义函数
	WidgetKeyPythonWidget = "WidgetKeyPythonWidget"
)

type CodeFormat string

type pythonWidget struct {
}

var WidgetCustomFunc = &Widget{
	Id:    WidgetKeyPythonWidget,
	Name:  "Python代码",
	Desc:  "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖",
	Group: WidgetGroupCodeTool,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Content",
				Name: "输入数据",
				Desc: "需要使用python代码处理的数据，" + BaseAnyLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "Code",
				Name:         "python代码",
				Desc:         "python代码，点击可查看或编辑代码",
				Required:     true,
				Type:         pb.DynamicParam_TYPE_CODE_PYTHON,
				DefaultValue: DefaultPythonCode,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "python代码中handler函数返回的数据，" + BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (t pythonWidget) Define() *Widget {
	return WidgetCustomFunc
}

func (l pythonWidget) AIDesc() AIWidgetDesc {
	params := []AIWidgetParamDesc{
		{
			ID:   "Content",
			Type: AIParamTypeInput,
			Desc: "输入端点",
		},
		{
			ID:          "Code",
			Type:        AIParamTypeAttribute,
			Desc:        "python代码",
			SampleValue: DefaultPythonCode,
		},
		{
			ID:   "OutPut",
			Type: AIParamTypeOutPut,
			Desc: "输出端点",
		},
	}
	return AIWidgetDesc{
		WidgetID:     l.Define().Id,
		WidgetName:   "Python代码",
		WidgetDesc:   "自定义python代码",
		WidgetParams: params,
	}
}

func (t pythonWidget) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	code, err := getNodeValueToString(nodeValue, "Code", false)
	if err != nil {
		return nil, err
	}
	return script.CustomCode{
		Meta:    nodeMeta.ToBaseScript(),
		Code:    script.MultilineString(code),
		Runtime: "py36",
	}, nil
}

const DefaultPythonCode = `
# python解释器版本 3.11

# 同步返回
def handler(data):
    """
    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据
   
    params:
    data (any): 参数名任意，任意类型

    return:
    any: 返回任意类型

    """
    # TODO 数据处理
    # xxx
    result = data

    return result

## 流式返回
#import time
#
#def handler(data):
#    """
#    示例生成器 handler：每1秒处理一个元素，并通过 yield 返回部分结果
#    """
#    for i in range(0, 3):
#        # 模拟耗时处理
#        time.sleep(1)
#        yield f"[Step {i+1}] Processed item: {data}-{i+1}"
#
#    yield "All items processed."

`
