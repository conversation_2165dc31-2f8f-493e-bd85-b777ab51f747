package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextToImage = "WidgetKeyTextToImage"
)

type textToImage struct{}

var widgetKeyTextToImage = &Widget{
	Id:    WidgetKeyTextToImage,
	Name:  "文生图",
	Desc:  "分析文本内容生成对应图片",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "Text",
				Name: "文本",
				Desc: BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "服务",
				Desc:       "服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetTextToImageModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDUseBase64,
				Name:         "Base64编码",
				Desc:         "生成图片是否以base64格式返回,远程及跨空间调用时需启用。",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeImageGenResp),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeImageGenResp).String(),
			},
		},
	},
}

func (t textToImage) Define() *Widget {
	return widgetKeyTextToImage
}

func (t textToImage) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	useBase64 := getBoolValueWithDefault(nodeValue, ParamIDUseBase64, true)
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}
	if params.SubKind != pb.ModelSubKind_MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE {
		return nil, stderr.Error("the sub kind of model is error")
	}

	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:      nodeMeta.ToBaseScript(),
		UseBase64: useBase64,
		Params:    script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}
