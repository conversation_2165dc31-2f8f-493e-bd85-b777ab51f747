package widgets

import (
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyQuestionClassifier              = "WidgetKeyQuestionClassifier"
	ParamIDQuestionClassifierCategory1       = "Category-1"
	ParamIDQuestionClassifierDefaultCategory = "Category-default"
	ParamIDQuestionClassifierOutput1         = "Output-1"
	ParamIDQuestionClassifierDefaultOutput   = "Output-default"
	ParamIDQuestionClassifierCategoryPrefix  = "Category-"
	ParamIDQuestionClassifierOutputPrefix    = "Output-"
	ParamIDQuestionClassifierModelService    = "ModelService"
	ParamIDQuestionClassifierAppService      = "AppService"
	ParamIDEnableAppMode                     = "EnableAppMode"   // 使用应用服务进行问题分类
	ParamIDEnableMutilMode                   = "EnableMutilMode" // 问题分类为多类别
	ParamIDQuestionClassifierCustomPrompt    = "CustomPrompt"    // 自定义系统提示词
)

var widgetQuestionClassifier = &Widget{
	DynamicEndPoint: true, // 输出端点不确定
	Id:              WidgetKeyQuestionClassifier,
	Name:            "问题分类器",
	Desc:            "对用户输入的问题进行分类，控制数据流向不同的类别",
	Group:           WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       "Input",
				Name:     "输入问题",
				Desc:     "待分类的问题文本," + BaseSyncLimits(DataTypeString).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnableMutilMode,
				Name:         "多类别模式",
				Desc:         "开启后,支持把输入问题归属到多个问题类别",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueTrue,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnableAppMode,
				Name:         "自定义策略",
				Desc:         "启用后,需要选择具体的应用服务,替代大模型进行问题分类",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueTrue,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQuestionClassifierAppService,
				Name:         "应用服务",
				Desc:         "用于进行问题分类的应用服务",
				Type:         pb.DynamicParam_TYPE_SELECTOR_DYNAMIC,
				Datasource:   "name,data_source_id@/app/applications/-/service-tools?service-type=question-classify",
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildBothPreconditionString(ParamIDEnableAppMode, ParamValueTrue),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQuestionClassifierModelService,
				Name:         "模型服务",
				Desc:         "用于进行问题分类的模型服务",
				Type:         pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource:   GetLLMModelSvcConditionsStr(),
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildBothPreconditionString(ParamIDEnableAppMode, ParamValueFalse),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQuestionClassifierCustomPrompt,
				Name:         "系统提示词",
				Desc:         "通过提示词补充额外知识,优化大模型进行问题分类的具体效果",
				Type:         pb.DynamicParam_TYPE_TEXTAREA,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: script.DlieInferDefaultSystemPrompt,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
				Precondition: BuildBothPreconditionString(ParamIDEnableAppMode, ParamValueFalse),
				Required:     false,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDQuestionClassifierCategory1,
				Name:     "分类1",
				Desc:     "问题类别",
				Required: true,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_INPUT,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDQuestionClassifierOutput1,
				Name: "分类1",
				Desc: "符合分类1的输出端点，原样输出输入的文本，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQuestionClassifierDefaultCategory,
				Name:         "默认分类",
				Desc:         "未匹配到分类问题或分类失败后默认的执行分支",
				Required:     true,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Type:         pb.DynamicParam_TYPE_INPUT,
				DefaultValue: "未匹配到上述类别",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDQuestionClassifierDefaultOutput,
				Name: "默认分类",
				Desc: "未匹配到分类问题或分类失败后的执行分支，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

type questionClassifier struct {
}

func (q questionClassifier) Define() *Widget {
	return widgetQuestionClassifier
}

func (q questionClassifier) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	var err error
	widgetParams := new(engine.WidgetParamsQuestionClassifier)
	widgetParams.CustomPrompt = getStringValueWithDefault(nodeValue, ParamIDQuestionClassifierCustomPrompt, "")
	widgetParams.EnableAppMode = getBoolValueWithDefault(nodeValue, ParamIDEnableAppMode, false)
	widgetParams.EnableMutilMode = getBoolValueWithDefault(nodeValue, ParamIDEnableMutilMode, false)
	widgetParams.ModelService, _ = GetStringNodeValueTo[pb.ModelService](nodeValue, ParamIDQuestionClassifierModelService)
	widgetParams.AppletService, _ = GetStringNodeValueTo[agent_definition.AppletServiceToolDescriber](nodeValue, ParamIDQuestionClassifierAppService)
	widgetParams.Categories, err = q.getCategories(nodeValue)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get categories")
	}
	if err := widgetParams.Validate(); err != nil {
		return nil, stderr.Wrap(err, "invalid widget params for question classifier")
	}
	return script.QuestionClassifier{
		Meta:   nodeMeta.ToBaseScript(),
		Params: stdsrv.AnyToString(widgetParams),
	}, nil
}
func (q questionClassifier) getCategories(nodeValue map[string]any) ([]*engine.Category, error) {
	categoriesMap := make(map[string]*engine.Category)
	for k, v := range nodeValue {
		// Category-xxx
		if !strings.HasPrefix(k, ParamIDQuestionClassifierCategoryPrefix) {
			continue
		}
		categoryName, ok := v.(string)
		if !ok {
			return nil, stderr.Errorf("expected string with key %s,but get %v", k, v)
		}
		// Category-uuid与Output-uuid一一对应
		outputId := ParamIDQuestionClassifierOutputPrefix + strings.TrimPrefix(k, ParamIDQuestionClassifierCategoryPrefix)
		categoriesMap[outputId] = &engine.Category{
			IsDefault:    outputId == ParamIDQuestionClassifierDefaultOutput,
			CategoryName: strings.TrimSpace(categoryName),
		}
	}

	outputInfos, err := getNodeValueToOutPut(nodeValue, false)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get output infos")
	}

	for _, o := range outputInfos.NodeOutputs {
		category, ok := categoriesMap[o.CurrentNodeOutputParam]
		if !ok {
			return nil, stderr.Errorf("can not find category with key %s", o.CurrentNodeOutputParam)
		}
		category.TargetNodes = append(category.TargetNodes, o.NextNodeID)
	}
	// 校验
	if len(categoriesMap) < 2 {
		return nil, stderr.Error("categories must be at least 2")
	}
	return helper.ExtractValues(categoriesMap), nil
}
