package widgets

import (
	"context"
	"net/http"
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

var (
	testWidgetsOptUrl = "http://**************:31919/api/v1/applet/test-widgets"
	//testWidgetsOptUrl = "http://localhost:30080/api/v1/applet/test-widgets"
	ctx = helper.NewContextWithBaseInfo(context.Background(), "dev-assets", "assets",
		"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ")
	// 算子
	widgetDefineExample = widgetQuestionClassifier
)

func TestUpsertTestWidget(t *testing.T) {
	req, _ := helper.GetHttpReq(ctx, http.MethodPost, testWidgetsOptUrl, widgetDefineExample, true)
	err := helper.HttpCallForSync(new(http.Client), req, func(result string) error {
		stdlog.Infof("result=%s", result)
		return nil
	})
	if err != nil {
		stdlog.Info("failed ")
	} else {
		stdlog.Info("success")
	}
}

func TestCleanTestWidget(t *testing.T) {
	req, _ := helper.GetHttpReq(ctx, http.MethodDelete, testWidgetsOptUrl, widgetDefineExample, true)
	helper.HttpCallForSync(new(http.Client), req, func(result string) error {
		stdlog.Info(result)
		return nil
	})
}

func TestInstance(t *testing.T) {
	jsonStr := `{"serviceAddr":"serviceAddr","id":"id"}`
	instance := new(AppletWidgetInstance)
	if err := stdsrv.UnmarshalMixWithProto(jsonStr, instance); err != nil {
		stdlog.Infof("error=%v", err)
	}
	stdlog.Infof("success")
}
