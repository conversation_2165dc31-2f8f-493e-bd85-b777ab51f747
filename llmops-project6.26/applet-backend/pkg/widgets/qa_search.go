package widgets

import (
	"fmt"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyQaSearch             = "WidgetKeyQaSearch"
	ParamIDQaSearchQuestion       = "Question"
	ParamIDQaSearchScoreThreshold = "ScoreThreshold"
	ParamIDQaSearchAnswerField    = "AnswerField"
	ParamIDQaSearchKnowledge      = "Knowledge"
	ParamIDQaSearchRerankModel    = "RerankModel"

	ParamDescQaSearchAnswerField = "检索结果是一个字典，请选择使用哪个字段作为预设答案，默认为 \"%s\""
)

type QaSearch struct {
}

var widgetQaSearch = &Widget{
	Id:    WidgetKeyQaSearch,
	Name:  "标准问答检索",
	Desc:  "在标准问答知识库中检索相似问题，输出预设答案",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       ParamIDQaSearchQuestion,
				Name:     "问题",
				Desc:     "需要检索的问题，" + BaseSyncLimits(DataTypeString).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQaSearchScoreThreshold,
				Name:         "匹配度阈值",
				Desc:         "标准问答知识库的匹配度阈值，当检索到的最相似问题的匹配度低于此阈值时，会输出空字符串，建议设置的值不低于0.6",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_FLOAT,
				DefaultValue: fmt.Sprintf("%f", script.QaSearchDefaultScoreThreshold),
				NumberRange: &pb.NumberRange{
					Min:  script.QaSearchMinScoreThreshold,
					Max:  script.QaSearchMaxScoreThreshold,
					Step: script.QaSearchScoreThresholdStep,
				},
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQaSearchAnswerField,
				Name:         "预设答案字段",
				Desc:         fmt.Sprintf(ParamDescQaSearchAnswerField, script.QaSearchDefaultAnswerField),
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: script.QaSearchDefaultAnswerField,
				Required:     false,
				Hidden:       true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDQaSearchRerankModel,
				Name:       "重排模型",
				Desc:       "选择模型,用于对召回的结果进行重排和筛选",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: GetRerankingModelSvcConditionsStr(),
				Required:   false,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDQaSearchKnowledge,
				Name:     "标准问答知识库",
				Desc:     "选择标准问答场景的知识库，从该知识库中检索最相似的问题，输出预设答案字段对应的内容",
				Type:     pb.DynamicParam_TYPE_AGENT_SKILL_KNOW_TOOLS,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: BuildUrlQueryString(map[string]string{
					helper.UrlQueryNameIsPublishSelector: helper.UrlQueryValueTrue,
					helper.UrlQueryNameSceneTypeSelector: helper.UrlQueryValueSceneTypeStandard,
				}),
				Required: false,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "最相似问题的预设答案，未检索到时会输出空," + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (q QaSearch) Define() *Widget {
	return widgetQaSearch
}

func (q QaSearch) Script(nodeMeta NodeMeta, nodeValue map[string]any) (script.Generator, error) {
	widgetParams, err := q.GetWidgetParams(nodeValue)
	if err != nil {
		return nil, err
	}
	return script.QaSearch{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(widgetParams)),
	}, nil
}

func (q QaSearch) GetWidgetParams(nodeValue map[string]any) (*script.WidgetParamsQaSearch, error) {
	scoreThreshold := getFloat64ValueWithDefault(nodeValue, ParamIDQaSearchScoreThreshold, script.QaSearchDefaultScoreThreshold)
	// 获取预设答案字段，目前该参数隐藏，允许为空，为空时设置为默认值
	answerField := getStringValueWithDefault(nodeValue, ParamIDQaSearchAnswerField, script.QaSearchDefaultAnswerField)
	kbs := &agent_definition.KnowledgeBases{
		MustUse:      true,
		RerankParams: &pb.RerankParams{},
	}

	// Get knowledge base info
	kbDescs, err := getKnowledgeBaseFromNodeValue(nodeValue, ParamIDQaSearchKnowledge)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get KnowledgeBase from node value %+v", nodeValue)
	}
	kbs.KnowledgeBaseDesc = kbDescs

	// Get rerank model info
	model, err := getRerankModelFromNodeValue(nodeValue, ParamIDQaSearchRerankModel)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get RerankModel from node value %+v", nodeValue)
	}
	kbs.RerankParams.Model = model

	widgetParams := &script.WidgetParamsQaSearch{
		ScoreThreshold: scoreThreshold,
		AnswerField:    answerField,
		KnowledgeBases: kbs,
	}

	if err := widgetParams.Validate(); err != nil {
		return nil, stderr.Wrap(err, "invalid widget params %+v", widgetParams)
	}
	return widgetParams, nil
}
