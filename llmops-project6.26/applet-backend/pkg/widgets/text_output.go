package widgets

import (
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextOutput = "WidgetKeyTextOutput"
	ParamIDStdText      = "StdText"
)

type textOutput struct {
}

var widgetTextOutput = &Widget{
	Id:    WidgetKeyTextOutput,
	Name:  "标准回复",
	Desc:  "将上游的输出替换为配置的回复话术",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   ParamIDText,
				Name: "输入",
				Desc: "输入数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDStdText,
				Name:         "话术配置",
				Desc:         "用于替换上游输出的回复话术",
				Type:         pb.DynamicParam_TYPE_TEXTAREA,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: "",
				Required:     true,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出配置的话术，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (f textOutput) Define() *Widget {
	return widgetTextOutput
}

func (f textOutput) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	stdText, err := getNodeValueToString(nodeValue, ParamIDStdText, false)
	if err != nil {
		return nil, err
	}
	stdText = strings.TrimSpace(stdText)
	if stdText == "" {
		return nil, stderr.Error("the field of stdText is empty")
	}
	return script.TextOutput{
		Meta:    nodeMeta.ToBaseScript(),
		StdText: script.MultilineString(stdText),
	}, nil
}
