package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyInputAggregation     = "WidgetKeyInputAggregation"
	ParamIDInputAggregationInput1 = "Input1"
	ParamIDInputAggregationInput2 = "Input2"
	ParamIDInputAggregationOutput = "Output"
	ParamIDInputAggregationCache  = "Cache"
)

type inputAggregation struct {
}

// GetCacheEnabled 获取数据合并节点的缓存开关状态
func GetCacheEnabled(values map[string]interface{}) bool {
	return getBoolValueWithDefault(values, ParamIDInputAggregationCache, false)
}

var widgetInputAggregation = &Widget{
	DynamicEndPoint: true,
	Id:              WidgetKeyInputAggregation,
	Name:            "数据合并",
	Desc:            "将上游多个输入，以KV的形式组成一个json输出（所有数据到位才会触发输出）",
	Group:           WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       ParamIDInputAggregationInput1,
				Name:     ParamIDInputAggregationInput1,
				Desc:     BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       ParamIDInputAggregationInput2,
				Name:     ParamIDInputAggregationInput2,
				Desc:     BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDInputAggregationCache,
				Name:         "输入数据缓存",
				Desc:         "当循环执行数据合并算子时，如果只有一个输入端口参与循环，其他输入端口需要使用之前的数据，可开启此开关，避免其他输入端口无数据阻塞数据合并算子执行",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeGeneralMap),
			Define: DynamicParam{
				Id:   ParamIDInputAggregationOutput,
				Desc: BaseSyncLimits(DataTypeGeneralMap).String(),
			},
		},
	},
}

func (f inputAggregation) Define() *Widget {
	return widgetInputAggregation
}

func (f inputAggregation) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.AggregateInput{
		Meta: nodeMeta.ToBaseScript(),
	}, nil
}
