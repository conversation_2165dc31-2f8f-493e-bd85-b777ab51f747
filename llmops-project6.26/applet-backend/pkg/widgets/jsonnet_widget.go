package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeyJsonnetWidget jsonnet算子
	WidgetKeyJsonnetWidget = "WidgetKeyJsonnetWidget"
)

type jsonnetWidget struct {
}

var WidgetFormatTransfer = &Widget{
	Id:    WidgetKeyJsonnetWidget,
	Name:  "Jsonnet代码",
	Desc:  "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等",
	Group: WidgetGroupCodeTool,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       "Content",
				Name:     "输入数据",
				Desc:     "需要使用jsonnet代码处理的数据，" + BaseAnyLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "Code",
				Name:         "jsonnet代码",
				Desc:         "jsonnet代码，点击可查看或编辑代码",
				Required:     true,
				Type:         pb.DynamicParam_TYPE_CODE_JSONNET,
				DefaultValue: DefaultJsonnetCode,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "jsonnet代码的执行结果，" + BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (j jsonnetWidget) Define() *Widget {
	return WidgetFormatTransfer
}

func (j jsonnetWidget) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	code, err := getNodeValueToString(nodeValue, "Code", false)
	if err != nil {
		return nil, err
	}
	return script.JSONNet{
		Meta: nodeMeta.ToBaseScript(),
		Code: script.MultilineString(code),
	}, nil
}

const DefaultJsonnetCode = `
/*jsonnet
使用input变量存储上游传入的数据
假设input存储的数据为
{
  "string": "str",
  "number": 123,
  "list": [1, 2, 3],
  "dict": { "k": "v" }
}

如下为几个使用示例
1.保持原样输出
input
2.取出某个值输出：
input.list
3.组装成新的数据结构
{ myString: input.string, myList: input.list, myNumber: input.list[0], }
*/

input

`
