package widgets

import (
	"fmt"
	"strings"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyUnion = "WidgetKeyUnion"
)

type union struct {
}

var widgetUnion = &Widget{
	Id:    WidgetKeyUnion,
	Name:  "数据合流",
	Desc:  "将上游多个输入，形成队列串行输出",
	Group: WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "上游数据，任意类型",
				Desc: "待合流的上游数据，需保证上游只有一个分支有数据流入此算子，否则请用数据合并算子," +
					BaseAnyLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "原样输出输入的上游数据，" + BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (f union) Define() *Widget {
	return widgetUnion
}

func (f union) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {

	inputs, err := getNodeValueToInputInfo(nodeValue, false)
	if err != nil {
		return nil, err
	}
	JoinElementBuilder := strings.Builder{}
	nodeInputs := inputs.NodeInputs
	for i := 1; i < len(nodeInputs); i++ {
		JoinElementBuilder.WriteString(fmt.Sprintf("var_%s", nodeInputs[i].PreNodeVar))
		if i != len(nodeInputs)-1 {
			JoinElementBuilder.WriteString(",")
		}
	}

	unionScript := script.Union{
		Meta:         nodeMeta.ToBaseScript(),
		UnionElement: JoinElementBuilder.String(),
	}
	return unionScript, nil
}
