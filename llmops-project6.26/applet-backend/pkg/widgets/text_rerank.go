package widgets

import (
	"fmt"
	"net/url"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	ParamIDTopK         = "TopK"
	ParamIDTexts        = "Texts"
	ParamIDThreshold    = "Threshold"
	WidgetKeyTextRerank = "WidgetKeyTextRerank"
)

func getRerankModelDatasource() string {
	path := helper.BaseDynamicDatasourceUrl
	queryParams := url.Values{}
	queryParams.Add(helper.QueryParamKey, helper.ModelServiceDataSource)
	queryParams.Add(helper.QueryParamSubKind, pb.ModelSubKind_MODEL_SUB_KIND_NLP_RERANKING.String())
	path = fmt.Sprintf("%s?%s", path, queryParams.Encode())
	return path
}

var widgetTextRerank = &Widget{
	Id:    WidgetKeyTextRerank,
	Name:  "文本重排",
	Desc:  "结合用户问题以及多段文本进行重排序，返回排序后的文本以及对应排序得分",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDQuestion,
				Name: "用户问题",
				Desc: "文本排序时所依赖的用户问题," + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:   ParamIDTexts,
				Name: "文本段",
				Desc: "用户输入的多段文本," + BaseSyncLimits(DataTypeStrings, DataTypeChunks).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDTopK,
				Name:         ParamIDTopK,
				Desc:         "重排后保留的最大段落数量",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				DefaultValue: "3",
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDThreshold,
				Name:         ParamIDThreshold,
				Desc:         "文本重排后被保留的最低阈值要求",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_FLOAT,
				DefaultValue: "0",
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDModelService,
				Name:       "模型服务",
				Desc:       "用于进行文本重排的模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetRerankingModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "附带相关分数并且按照分数降序排列的文本段落," + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
	},
}

type WidgetParamsTextRerank struct {
	TopK               int64                               `json:"top_k"`
	Threshold          float64                             `json:"threshold"`
	ModelToolDescriber agent_definition.ModelToolDescriber `json:"model_tool_describe"`
}
type textRerank struct {
}

func (tr textRerank) Define() *Widget {
	return widgetTextRerank
}
func (tr textRerank) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	topK, err := getNodeValueToInt64(nodeValue, ParamIDTopK, false)
	if err != nil {
		return nil, err
	}
	threshold, err := getNodeValueToFloat64(nodeValue, ParamIDThreshold, false)
	if err != nil {
		return nil, err
	}
	modelServiceStr, err := getNodeValueToString(nodeValue, ParamIDModelService, false)
	if err != nil {
		return nil, err
	}
	modelServiceToolDesc := new(agent_definition.ModelToolDescriber)
	if err := stdsrv.UnmarshalMixWithProto(modelServiceStr, modelServiceToolDesc); err != nil {
		return nil, err
	}

	widgetParams := WidgetParamsTextRerank{
		TopK:               topK,
		Threshold:          threshold,
		ModelToolDescriber: *modelServiceToolDesc,
	}

	SimpleModelService(&widgetParams.ModelToolDescriber.ModelService)
	return script.TextRerank{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(widgetParams)),
	}, nil
}
