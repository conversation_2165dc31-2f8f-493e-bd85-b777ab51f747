package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeyCustomPrompt 自定义prompt模板
	WidgetKeyCustomPrompt = "WidgetKeyCustomPrompt"
)

type customPrompt struct {
}

var WidgetCustomPrompt = &Widget{
	Id:    WidgetKeyCustomPrompt,
	Name:  "GoTemplate代码",
	Desc:  "可用于灵活定义数据拼接、数据字典处理等过程",
	Group: WidgetGroupCodeTool,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Content",
				Name: "输入数据",
				Desc: "需要使用go template代码处理的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "Code",
				Name:         "go template代码",
				Desc:         "go template代码，点击可查看或编辑代码",
				Required:     true,
				Type:         pb.DynamicParam_TYPE_CODE_GOTEMPLATE,
				DefaultValue: DefaultGoTemplateCode,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "go template代码执行的结果，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (j customPrompt) Define() *Widget {
	return WidgetCustomPrompt
}

func (j customPrompt) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	code, err := getNodeValueToString(nodeValue, "Code", false)
	if err != nil {
		return nil, err
	}
	return script.Prompt{
		Meta:     nodeMeta.ToBaseScript(),
		Template: script.MultilineString(code),
		RawInput: true,
	}, nil
}

const DefaultGoTemplateCode = `
{{/* 
假设上游传入的数据为
{
	"string": "str",
	"number": 123,
	"list": [1, 2, 3],
	"dict": { "k": "v" }
}

在Go Template语法中使用"."指代上游传入的数据，如下为几个使用示例

1.直接输出上游传入的数据
{{.}}

2.取出字典中某个字段值输出：
{{.string}} 取出某个字段
{{.dict.k}} 取出嵌套的字段

3.循环
{{range .list}}
{{.}}
{{end}}
*/}}

{{.}}

`
