package widgets

import (
	"encoding/json"
	"fmt"
	"net/url"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	//"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyFullTextSearch = "WidgetKeyFullTextSearch"
	WidgetKeyFullTextInsert = "WidgetKeyFullTextInsert"
	ParamIDFullTextIndex    = "FullTextIndex"
	ParamIDFullTextTopK     = "FullTextTopK"
)

func init() {
	widgetFullTextSearch.Params = append(widgetFullTextSearch.Params, sharedConfig...)
	widgetFullTextInsert.Params = append(widgetFullTextInsert.Params, sharedConfig...)
}

func getScopeDataSource() string {
	path := helper.BaseDynamicDatasourceUrl
	queryParams := url.Values{}
	queryParams.Add(helper.QueryParamKey, helper.ScopeDataSource)
	path = fmt.Sprintf("%s?%s", path, queryParams.Encode())
	return path
}

var sharedConfig = []WidgetParam{
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:         WidgetVDSearchParamConnection,
			Name:       "数据源",
			Desc:       "选择“空间/运维空间/数据源管理”列表中的一个scope/es数据源",
			Type:       pb.DynamicParam_TYPE_SELECTOR_DYNAMIC,
			Datasource: getScopeDataSource(),
			DataType:   pb.DynamicParam_DATA_TYPE_STRING,
			Required:   true,
			Multiple:   false,
		},
	},
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:   ParamIDFullTextIndex,
			Name: "索引名称",
			Desc: "索引名称",
			// TODO dataSource
			Type:     pb.DynamicParam_TYPE_INPUT,
			DataType: pb.DynamicParam_DATA_TYPE_STRING,
			Required: true,
		},
	},
	{
		DataClass:   DataClassJson,
		Category:    ParamTypeNodeOutPort,
		ParamLimits: BaseSyncLimits(DataTypeChunks),
		Define: DynamicParam{
			Id:   "OutPut",
			Desc: "输出结果," + BaseSyncLimits(DataTypeChunks).String(),
		},
	},
}

var widgetFullTextSearch = &Widget{
	Id:    WidgetKeyFullTextSearch,
	Name:  "全文检索",
	Desc:  "在Scope数据库中进行全文检索",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       ParamIDText,
				Name:     "输入文本",
				Desc:     "需要在数据库中检索的文本内容," + BaseSyncLimits(DataTypeString).String(),
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDFullTextTopK,
				Name:     "TopK",
				Desc:     "返回的最大数量",
				Type:     pb.DynamicParam_TYPE_NUMBER,
				DataType: pb.DynamicParam_DATA_TYPE_INT,
				Required: true,
			},
		},
	},
}

func constructESConfig(nodeValue map[string]interface{}) (*SimpleESConfig, error) {
	//“空间-运维工具-数据源管理”中单条 DataSource Connection 的所有信息被封装成 JSON
	dataConnectionInfo, err := getNodeValueToString(nodeValue, WidgetVDSearchParamConnection, true)
	if err != nil {
		stdlog.Errorln("unable to get NodeValue in string.")
		return nil, err
	}

	var dataConnection pb.DataConnection

	err = stdsrv.UnmarshalMixWithProto(dataConnectionInfo, &dataConnection)

	if err != nil {
		stdlog.Errorln("unable to Unmarshal response string into type: 'pb.DataConnection'.")
		return nil, err
	}

	Address := fmt.Sprintf("http://%s:%s", dataConnection.Address, dataConnection.Port)

	esConfig := &SimpleESConfig{
		Username:  dataConnection.Username,
		Password:  dataConnection.Password,
		Addresses: []string{Address},
	}

	return esConfig, nil
}

type SimpleESConfig struct {
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	Addresses []string `json:"addresses"`
}

type WidgetParamsFullTextSearch struct {
	TopK      int            `json:"top_k"`
	IndexName string         `json:"index_name"`
	ESConfig  SimpleESConfig `json:"es_config"`
}

type fullTextSearch struct{}

func (es fullTextSearch) Define() *Widget {
	return widgetFullTextSearch
}

func (es fullTextSearch) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	esConfig, err := constructESConfig(nodeValue)
	if err != nil {
		return nil, err
	}
	indexName, err := getNodeValueToString(nodeValue, ParamIDFullTextIndex, false)
	if err != nil {
		return nil, err
	}
	topK, err := getNodeValueToInt64(nodeValue, ParamIDFullTextTopK, false)
	if err != nil {
		return nil, err
	}
	widgetParams := WidgetParamsFullTextSearch{
		IndexName: indexName,
		TopK:      int(topK),
		ESConfig:  *esConfig,
	}
	paramsBytes, err := json.Marshal(widgetParams)
	if err != nil {
		return nil, err
	}
	return script.FullTextSearch{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(paramsBytes),
	}, nil
}

type WidgetParamsFullTextInsert struct {
	IndexName string         `json:"index_name"`
	ESConfig  SimpleESConfig `json:"es_config"`
}

type fullTextInsert struct{}

var widgetFullTextInsert = &Widget{
	Id:    WidgetKeyFullTextInsert,
	Name:  "全文写入",
	Desc:  "在Scope数据库中插入文本",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       ParamIDText,
				Name:     "输入文本",
				Desc:     "需要在数据库中插入的文本内容," + BaseSyncLimits(DataTypeString).String(),
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
	},
}

func (es fullTextInsert) Define() *Widget {
	return widgetFullTextInsert
}

func (es fullTextInsert) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	esConfig, err := constructESConfig(nodeValue)
	if err != nil {
		return nil, err
	}
	indexName, err := getNodeValueToString(nodeValue, ParamIDFullTextIndex, false)

	widgetParams := WidgetParamsFullTextInsert{
		IndexName: indexName,
		ESConfig:  *esConfig,
	}
	paramsBytes, err := json.Marshal(widgetParams)
	if err != nil {
		return nil, err
	}
	return script.FullTextInsert{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(paramsBytes),
	}, nil
}
