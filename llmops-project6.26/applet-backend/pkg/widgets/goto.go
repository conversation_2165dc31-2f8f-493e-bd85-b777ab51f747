package widgets

import (
	"strconv"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyGoTo = "WidgetKeyGoTo"
)

const (
	ParamIDTargetNodes   = "targetNodes" // `[id1,id2]`
	MaxLoopDefault       = 5
	ParamIDMaxLoopRounds = "maxLoopRounds"
)

type wGoTo struct {
}

var widgetWGoTo = &Widget{
	Id:    WidgetKeyGoTo,
	Name:  "循环跳转算子",
	Desc:  "用于改变数据流向,将数据传输给上游算子从而实现循环。",
	Group: WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入数据",
				Desc: "待改变流向的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDMaxLoopRounds,
				Name:     "最大轮次数",
				Desc:     "最大循环次数，要求是正整数",
				DataType: pb.DynamicParam_DATA_TYPE_INT,
				Type:     pb.DynamicParam_TYPE_NUMBER,
				NumberRange: &pb.NumberRange{
					Min: script.GoToMinMaxLoopRounds,
					Max: script.GoToMaxMaxLoopRounds,
				},
				Required:     true,
				DefaultValue: strconv.Itoa(script.GoToDefaultMaxLoopRounds),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "原样输出输入的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
	},
}

func (f wGoTo) Define() *Widget {
	return widgetWGoTo
}

func (f wGoTo) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	maxLoop := getInt64ValueWithDefault(nodeValue, ParamIDMaxLoopRounds, MaxLoopDefault)
	//goto算子的出边会被移除,下游节点信息固定存在nodeValue中的ParamIDTargetNodes字段
	targetNodes, err := GetStringNodeValueTo[[]string](nodeValue, ParamIDTargetNodes)
	if err != nil {
		return nil, stderr.Wrap(err, "goto widget gen script err")
	}
	return script.GOTO{
		Meta: nodeMeta.ToBaseScript(),
		Params: stdsrv.AnyToString(engine.WidgetParamsGoto{
			MaxLoopRounds: maxLoop,
			TargetNodes:   *targetNodes,
		}),
	}, nil
}
