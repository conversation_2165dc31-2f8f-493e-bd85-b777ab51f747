package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyMergeParagraph = "WidgetKeyMergeParagraph"
)

type mergeParagraph struct{}

var widgetKeyMergeParagraph = &Widget{
	Id:    WidgetKeyMergeParagraph,
	Name:  "切片转长文本",
	Desc:  "Chunks转文本",
	Group: WidgetGroupProcessKnowledge,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   "Content",
				Name: "输入",
				Desc: "待合并的文本段落，" + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "Code",
				Name:         "自定义代码",
				Desc:         "按照需求自定义处理逻辑的代码",
				Required:     true,
				Type:         pb.DynamicParam_TYPE_CODE_JSONNET,
				DefaultValue: DefaultParagraphMergeCode,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "合并后的文本段落，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (mp mergeParagraph) Define() *Widget {
	return widgetKeyMergeParagraph
}

func (mp mergeParagraph) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	code, err := getNodeValueToString(nodeValue, "Code", false)
	if err != nil {
		return nil, err
	}
	return script.Prompt{
		Meta:     nodeMeta.ToBaseScript(),
		Template: script.MultilineString(code),
		RawInput: true,
	}, nil
}

const DefaultParagraphMergeCode = `
{{ $newline := "\n" }}
{{ range $index,$chunk := . }}
{{ $chunk.content}}{{ $newline }}
{{ end}}
`
