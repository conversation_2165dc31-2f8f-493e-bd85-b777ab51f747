package widgets

import (
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeySampleCubePrompt = "WidgetKeySampleCubePrompt"
)

type sampleCubePrompt struct {
}

var WidgetSampleCubePrompt = &Widget{
	Id:    WidgetKeySampleCubePrompt,
	Name:  "提示词模板",
	Desc:  "用于调用已在语料仓库发布的提示词模版进行提示词拼接",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入",
				Desc: "输入",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出",
			},
		},
	},
}

func (t sampleCubePrompt) Define() *Widget {
	return WidgetSampleCubePrompt
}

func (t sampleCubePrompt) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	templateStr, err := getNodeValueToString(nodeValue, "Template", true)
	if err != nil {
		return nil, err
	}
	return script.Prompt{
		Meta:     nodeMeta.ToBaseScript(),
		Template: script.MultilineString(templateStr),
		RawInput: false,
	}, nil
}
