package widgets

import (
	"fmt"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyAgent = "WidgetKeyAgent"

	// ParamIDPrompt 通过画布-应用链构建时传递的agent算子参数
	ParamIDPrompt                 = "chain_prompt"
	ParamIDLLMModelSvcStr         = "chain_llm_model_svc_str"
	ParamIDRerankModelSvcStr      = "chain_rerank_model_svc_str"
	ParamIDEnableTrace            = "chain_enable_trace"
	ParamIDApiCollectionsStr      = "chain_api_collections_str"
	ParamIDKnowledgeBaseDescStr   = "chain_knowledge_base_desc_str"
	ParamIDKnowledgeBaseRerankStr = "chain_knowledge_base_rerank_str"
	ParamIDSystemServicesStr      = "chain_system_services_str"
	ParamIDCreatedByChain         = "chain_created_by_chain"
	ParamValueTrue                = "true"
	AgentWidgetParamIDQuestion    = "Question"
	AgentWidgetParamIDKnowledge   = "Knowledge"
	AgentWidgetParamIDInternet    = "Internet"
	AgentWidgetParamIDHistory     = "History"
	AgentWidgetParamIDFile        = "File"
	AgentWidgetParamIDAgentMode   = "AgentMode"
)

func (a Agent) GetWidgetParams(nodeValue map[string]interface{}) (*agent_definition.LLMAgentConfig, error) {
	agentConfig := new(agent_definition.LLMAgentConfig)

	// 提示词-必填
	str, err := getNodeValueToString(nodeValue, ParamIDPrompt, false)
	if err != nil {
		return nil, err
	}
	agentConfig.Prompt = str

	// 模型信息-必填
	str, err = getNodeValueToString(nodeValue, ParamIDLLMModelSvcStr, false)
	if err != nil {
		return nil, err
	}
	agentConfig.LLMModelSvc = new(pb.ModelService)
	if err := stdsrv.UnmarshalMixWithProto(str, agentConfig.LLMModelSvc); err != nil {
		return nil, err
	}

	// 智能体模式-必填
	agentMode := getStringValueWithDefault(nodeValue, AgentWidgetParamIDAgentMode, string(agent_definition.AgentModeReAct))
	agentConfig.AgentMode = agent_definition.AgentMode(agentMode)

	// 溯源rerank模型-选填
	str = getStringValueWithDefault(nodeValue, ParamIDRerankModelSvcStr, "")
	enableTrace := getBoolValueWithDefault(nodeValue, ParamIDEnableTrace, false)
	if str != "" && enableTrace {
		agentConfig.RerankModelSvc = new(pb.ModelService)
		if err := stdsrv.UnmarshalMixWithProto(str, agentConfig.RerankModelSvc); err != nil {
			return nil, err
		}
	}

	// 工具集-选填
	str, _ = getNodeValueToString(nodeValue, ParamIDApiCollectionsStr, true)
	if str != "" {
		if err := stdsrv.UnmarshalMixWithProto(str, &agentConfig.APICollections); err != nil {
			return nil, err
		}
	}

	// 各知识库tool调用信息-选填
	str, _ = getNodeValueToString(nodeValue, ParamIDKnowledgeBaseDescStr, true)
	if str != "" {
		if err := stdsrv.UnmarshalMixWithProto(str, &agentConfig.KnowledgeBases.KnowledgeBaseDesc); err != nil {
			return nil, err
		}
	}

	// 平台服务(模型服务/应用服务)-选填
	str, _ = getNodeValueToString(nodeValue, ParamIDSystemServicesStr, true)
	if str != "" {
		if err := stdsrv.UnmarshalMixWithProto(str, &agentConfig.SystemServices); err != nil {
			return nil, err
		}
	}

	return agentConfig, nil
}

type Agent struct {
}

var widgetAgent = &Widget{
	Id:    WidgetKeyAgent,
	Name:  "智能体",
	Desc:  "在使用大模型的基础上,能自动使用各种工具",
	Group: WidgetGroupAdvanced,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       AgentWidgetParamIDQuestion,
				Name:     "问题",
				Desc:     "输入给智能体的问题," + BaseSyncLimits(DataTypeString).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   AgentWidgetParamIDKnowledge,
				Name: "知识库内容",
				Desc: "在知识库召回的内容," + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeInternetCitations),
			Define: DynamicParam{
				Id:   AgentWidgetParamIDInternet,
				Name: "互联网内容",
				Desc: "通过搜索引擎从互联网检索到的内容," + BaseSyncLimits(DataTypeInternetCitations).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   AgentWidgetParamIDHistory,
				Name: "对话历史",
				Desc: "用户问题与模型回答的历史记录," + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass:   DataClassFile,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile),
			Define: DynamicParam{
				Id:   AgentWidgetParamIDFile,
				Name: "文件",
				Desc: `上传的文件内容,` + BaseSyncLimits(DataTypeSFSFile).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDLLMModelSvcStr,
				Name:       "LLM模型",
				Desc:       "可用的LLM模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetLLMModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       AgentWidgetParamIDAgentMode,
				Name:     "智能体模式",
				Desc:     "选择智能体的推理模式, ReAct模式是比较通用的工具调用模式, FunctionCalling模式需要LLM支持FunctionCalling调用工具",
				Type:     pb.DynamicParam_TYPE_SELECTOR,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
				Datasource: fmt.Sprintf("%s@@ReAct,%s@@FunctionCalling",
					agent_definition.AgentModeReAct, agent_definition.AgentModeFunctionCalling,
				),
				DefaultValue: string(agent_definition.AgentModeReAct),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnableTrace,
				Name:         "知识溯源",
				Desc:         "知识溯源,区分知识的引用来源",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueFalse,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDRerankModelSvcStr,
				Name:         "溯源模型",
				Desc:         "溯源模型,根据问题对引用来源进行重排序",
				Type:         pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource:   GetRerankingModelSvcConditionsStr(),
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildBothPreconditionString(ParamIDEnableTrace, ParamValueTrue),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDPrompt,
				Name:     "提示词",
				Desc:     "设置LLM的指令或者角色",
				Type:     pb.DynamicParam_TYPE_AGENT_INSTRUCTION,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDApiCollectionsStr,
				Name:         "插件API",
				Desc:         "允许智能体调用的插件API，从而扩展智能体的功能和应用程序",
				Type:         pb.DynamicParam_TYPE_AGENT_SKILL_API_TOOLS,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: "",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDKnowledgeBaseDescStr,
				Name:     "知识库",
				Desc:     "添加知识库后智能体即可以引用知识的内容来回答用户问题",
				Type:     pb.DynamicParam_TYPE_AGENT_SKILL_KNOW_TOOLS,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: BuildUrlQueryString(map[string]string{
					helper.UrlQueryNameIsPublishSelector: helper.UrlQueryValueTrue,
				}),
				DefaultValue: "",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDSystemServicesStr,
				Name:         "平台服务",
				Desc:         "允许智能体调用通过Sophon LLMOps部署的模型服务以及应用服务",
				Type:         pb.DynamicParam_TYPE_AGENT_SKILL_SERVICE_TOOLS,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: "",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDCreatedByChain,
				Hidden:       true,
				Disabled:     true,
				Name:         "标记为画布方式创建",
				Desc:         "标记为画布方式创建",
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: ParamValueTrue,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseStreamLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Name: "输出",
				Desc: "LLM Agent 综合多轮问答以及各类工具调用结果给出的最终回答，" + BaseStreamLimits(DataTypeString).String(),
			},
		},
	},
}

func (a Agent) Define() *Widget {
	return widgetAgent
}

func (a Agent) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	// 1、获取agentConfig 兼容智能体创建方式和应用链创建方式
	agentConfig, err := a.GetWidgetParams(nodeValue)
	if err != nil {
		return nil, err
	}
	SimpleModelService(agentConfig.LLMModelSvc)
	return script.Agent{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(agentConfig)),
	}, nil
}
