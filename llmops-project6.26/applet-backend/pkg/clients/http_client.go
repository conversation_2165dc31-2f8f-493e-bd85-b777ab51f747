package clients

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/manucorporat/sse"
	"h12.io/socks"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	ProxySchemePrefix = "PROXY_SCHEME_"
	HttpTimeoutSec    = 60
)

type HttpClient struct {
	Cli *http.Client
}

type HttpParam struct {
	Method     string
	Url        string
	ReqBody    string
	Header     map[string]string
	QueryParam map[string]string
}

func NewHttpCliWithProxy(transport *http.Transport) *HttpClient {
	httpCli := &http.Client{
		Transport: transport,
		Timeout:   HttpTimeoutSec * time.Second,
	}
	return &HttpClient{Cli: httpCli}
}

func HttpTransport(proxy *agent_definition.ProxyInfo) (*http.Transport, error) {
	defaultTransport := http.DefaultTransport.(*http.Transport)
	defaultTransport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	if proxy == nil {
		return defaultTransport, nil
	}
	scheme, err := proxyScheme(proxy.Scheme)
	if err != nil {
		return nil, err
	}
	if !strings.HasPrefix(proxy.Url, scheme) {
		proxy.Url = scheme + "://" + proxy.Url
	}
	proxyURL, err := url.Parse(proxy.Url)
	if err != nil {
		return nil, err
	}
	tr := defaultTransport.Clone()
	switch proxy.Scheme {
	case pb.ProxyScheme_PROXY_SCHEME_HTTP:
		tr.Proxy = http.ProxyURL(proxyURL)
		return tr, nil

	case pb.ProxyScheme_PROXY_SCHEME_HTTPS:
		tr.Proxy = http.ProxyURL(proxyURL)
		tr.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
		return tr, nil
	case pb.ProxyScheme_PROXY_SCHEME_SOCKS4, pb.ProxyScheme_PROXY_SCHEME_SOCKS5:
		dialer := socks.Dial(proxy.Url)
		return &http.Transport{Dial: dialer}, nil

	default:
		return nil, stderr.Internal.Error("unsupported proxy schema")
	}
}

func proxyScheme(schema pb.ProxyScheme) (string, error) {
	if schema == pb.ProxyScheme_PROXY_SCHEME_UNSPECIFIED {
		return "", stderr.Internal.Error("proxy scheme unspecified")
	}
	return strings.ToLower(strings.TrimPrefix(schema.String(), ProxySchemePrefix)), nil
}

func (h *HttpClient) HttpCall(ctx context.Context, param *HttpParam, obj any) error {

	url, err := url.Parse(param.Url)
	if err != nil {
		return err
	}
	q := url.Query()
	// 添加 query参数
	for k, v := range param.QueryParam {
		q.Set(k, v)
	}
	var reqBody io.Reader
	if param.ReqBody != "" {
		reqBody = bytes.NewBufferString(param.ReqBody)
	}
	req, err := http.NewRequestWithContext(ctx, param.Method, url.String(), reqBody)
	if err != nil {
		return err
	}
	// 默认application/json
	req.Header.Add("Content-Type", "application/json")
	for k, v := range param.Header {
		req.Header.Add(k, v)
	}
	// 发送请求并获取响应
	resp, err := h.Cli.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return stderr.Internal.Error("http code not 200, resp:[%v]", resp)
	}
	if err := json.Unmarshal(body, &obj); err != nil {
		return err
	}
	return nil
}

func (h *HttpClient) HttpCallString(ctx context.Context, param *HttpParam) (string, error) {
	url, err := url.Parse(param.Url)
	if err != nil {
		return "", err
	}
	q := url.Query()
	// 添加 query参数
	for k, v := range param.QueryParam {
		q.Set(k, v)
	}
	var reqBody io.Reader
	if param.ReqBody != "" {
		reqBody = bytes.NewBufferString(param.ReqBody)
	}
	fullPath := fmt.Sprintf("%s?%s", url.String(), q.Encode())
	req, err := http.NewRequestWithContext(ctx, param.Method, fullPath, reqBody)
	if err != nil {
		return "", err
	}
	// 默认application/json
	req.Header.Add("Content-Type", "application/json")
	for k, v := range param.Header {
		req.Header.Add(k, v)
	}
	// 发送请求并获取响应
	resp, err := h.Cli.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(body))
	}

	// json编码再解码，解决中文问题
	var res any
	if err := json.Unmarshal(body, &res); err != nil {
		if _, ok := err.(*json.SyntaxError); ok {
			return string(body), nil // 可能body是非json格式，直接返回内容
		}
		return "", err
	}
	bytes, err := json.Marshal(res)
	if err != nil {
		return "", err
	}
	return string(bytes), nil

}
func (h *HttpClient) HttpGetWithToken(ctx context.Context, requestUrl string, result interface{}) error {
	req, err := http.NewRequest("GET", requestUrl, nil)
	if err != nil {
		return err
	}
	// 添加请求头
	tk, err := helper.GetToken(ctx)
	if err != nil {
		return err
	}
	req.Header.Add(helper.AuthHeaderKey, tk)
	req.Header.Add("Content-Type", "application/json")
	// 发送请求并获取响应
	resp, err := h.Cli.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(body))
	}
	if err := json.Unmarshal(body, &result); err != nil {
		stdlog.Debug(fmt.Errorf("failed to unmarshal json:%s", string(body)))
	}
	return nil

}

func (h *HttpClient) HttpPost(ctx context.Context, requestUrl string, postBody string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", requestUrl, bytes.NewBuffer([]byte(postBody)))
	if err != nil {
		return "", err
	}
	// 发送请求并获取响应
	resp, err := h.Cli.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	var responseBody bytes.Buffer
	_, err = responseBody.ReadFrom(resp.Body)
	if err != nil {
		return "", err
	}
	return responseBody.String(), nil
}

func (h *HttpClient) HttpPostStream(ctx context.Context, requestUrl string, postBody string, writer io.Writer) (*bytes.Buffer, error) {
	// 创建一个内存缓冲区
	data := new(bytes.Buffer)
	req, err := http.NewRequestWithContext(ctx, "POST", requestUrl, bytes.NewBuffer([]byte(postBody)))
	if err != nil {
		return nil, err
	}
	tk, err := helper.GetToken(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "get user token")
	}
	req.Header.Set(auth.TokenHeader, tk)
	req.Header.Set("Content-Type", "application/json")
	// 发送请求并获取响应
	resp, err := h.Cli.Do(req)
	if err != nil {
		return nil, err
	}

	// 创建 TeeReader，同时将响应内容写入到 writer 和缓冲区中
	teeReader := io.TeeReader(resp.Body, io.MultiWriter(data, writer))

	defer resp.Body.Close()
	//若返回报错，则不保存上下文
	if resp.StatusCode != http.StatusOK {
		buffer := bytes.NewBuffer([]byte{})
		io.Copy(buffer, resp.Body)
		msg := buffer.String()
		if r, ok := helper.HttpErrCodeMsg[resp.StatusCode]; ok {
			msg = fmt.Sprintf(":%s,resp :%v", r, msg)
		}
		if err := sse.Encode(writer, sse.Event{
			Event: "error",
			Id:    uuid.New().String(),
			Data:  fmt.Sprintf("[error]err code %d,msg:%s", resp.StatusCode, msg),
		}); err != nil {
			return nil, err
		}

	} else {
		// 读取 teeReader 中的内容，并将其写入到 buffer 中
		if _, err := io.Copy(data, teeReader); err != nil {
			return nil, err
		}
	}

	return data, nil

}
func (h *HttpClient) doRequest(ctx context.Context, method string, requestUrl string, requestBody *bytes.Buffer, result interface{}) error {
	var req *http.Request
	var err error
	if requestBody != nil {
		req, err = http.NewRequest(method, requestUrl, requestBody)
		if err != nil {
			return err
		}
	} else {
		req, err = http.NewRequest(method, requestUrl, nil)
		if err != nil {
			return err
		}
	}

	tk, err := helper.GetToken(ctx)
	if err != nil {
		return err
	}
	req.Header.Add(helper.AuthHeaderKey, tk)
	req.Header.Add("Content-Type", "application/json")
	rsp, err := h.Cli.Do(req)
	if err != nil {
		return err
	}
	defer rsp.Body.Close()

	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return err
	}
	if rsp.StatusCode != 200 {
		return fmt.Errorf("status code:%d, err message: %s", rsp.StatusCode, string(body))
	}
	if err := json.Unmarshal(body, &result); err != nil {
		stdlog.Debug(fmt.Errorf("failed to unmarshal json:%s", string(body)))
	}
	return nil
}
