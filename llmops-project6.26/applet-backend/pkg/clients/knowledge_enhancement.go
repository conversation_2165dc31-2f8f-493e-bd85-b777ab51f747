package clients

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"

	"github.com/devchat-ai/gopool"
	"github.com/google/uuid"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	maxEnhancerConcurrency     = 100
	defaultEnhancerConcurrency = 10 // 默认池大小为10
	PromptTextQuesRewrite      = `对于下面的给定问题，请按照以下步骤进行改写：\n\n1. **理解原始问题**：\n   - 识别问题中的关键词和主要主题。\n   
- 确定问题中可能存在的歧义或不明确的表达。\n\n2. **简化和明确化问题**：\n   - 去除冗余词汇，简化表达。\n   
- 明确化任何模糊或含糊的表述。\n\n3. **改写问题**：\n   - 重构问题，使其更加专业和易于理解。\n   - 如果需要，将复杂或多部分的问题分解为更具体的子问题。\n\n4. **输出改写后的问题**：\n   
- 列出改写后的问题，确保它更加直接且易于回答。\n\n例子：\n- 原始问题：“我听说AI可以做很多事情，比如预测股市，这是真的吗？如果是的话，那些AI工具是怎么工作的？有什么好工具推荐给新手吗？”\n- 改写后的问题：\n  1. AI在预测股市方面的应用是什么？\n  2. 这些AI工具具体是如何工作的？\n  3. 有哪些适合新手的AI工具推荐用于股市分析？\n`
	PromptTextQuesRewriteEn = `For the given question below, please rewrite it according to the following steps:\n\n1. **Understand the original question**:\n   - Identify the keywords and main topics in the question.\n   \n
- Determine any ambiguity or unclear expressions in the question.\n\n2. **Simplify and clarify the question**:\n   - Remove redundant words and simplify phrasing.\n   \n
- Clarify any vague or ambiguous statements.\n\n3. **Rewrite the question**:\n   - Restructure the question to make it more professional and easier to understand.\n   
- If necessary, break down complex or multi-part questions into more specific sub-questions.\n\n4. **Output the rewritten question**:\n   \n- List the rewritten question(s), ensuring they are more direct and easier to answer.\n\nExample:\n- Original question: “I've heard that AI can do many things, such as predicting the stock market. Is this true? If so, how do these AI tools work? Are there any good tools you would recommend for beginners?”\n- Rewritten questions:\n  1. What are the applications of AI in predicting the stock market?\n  2. How do these AI tools specifically work?\n  3. Which AI tools are recommended for beginners to analyze the stock market?\n`
	PromptTableDescription   = `请仔细阅读下面以html形式表示的表格，然后用中文准确详细地描述表格内容，尽可能保证信息完整，不要遗漏关键数据。`
	PromptTableDescriptionEn = `Please carefully read the table presented in HTML format below, then accurately and thoroughly describe its content, ensuring completeness of information and avoiding omission of critical data.`
	PromptTableSummary       = `请仔细阅读下面以html形式表示的表格，理解表格内容然后思考可能的表名是什么，并且挖掘表格内容可能表达的结论。按照上述要求生成表格的简短概要，确保你的总结准确。`
	PromptTableSummaryEn     = `Please carefully read the HTML-formatted table below, understand its content, propose potential table titles, and explore conclusions that the table may convey. Based on these requirements, generate a concise summary of the table, ensuring accuracy.`
	PromptImageDescription   = `请用中文准确地描述这张图片。`
	PromptImageDescriptionEn = `Please provide an accurate description of this image.`
)

type EnhanceReq struct {
	Num      int
	Chunks   []*pb.Chunk
	Modes    []pb.AugmentedChunkType
	Prompts  []string // 用户自定义prompt
	Callback func()
	Params   map[string]any // 推理参数
}

type KnowledgeEnhancer struct {
	sync.Mutex
	model *pb.ModelService
	pool  gopool.GoPool
	errs  []error
}

// NewKnowledgeEnhancer 函数用于初始化KnowledgeEnhancement的配置，url为模型的url
func NewKnowledgeEnhancer(ctx context.Context, model *pb.ModelService, concurrency int) (*KnowledgeEnhancer, error) {
	c := concurrency
	if c < 1 || c > maxEnhancerConcurrency {
		c = defaultEnhancerConcurrency
	}

	if ok, err := CheckModelConn(model); !ok {
		return nil, stderr.BadRequest.Cause(err, "failed to access model by tcp")
	}
	return &KnowledgeEnhancer{
		model: model,
		pool:  gopool.NewGoPool(c),
	}, nil
}

func (k *KnowledgeEnhancer) clean() {
	k.errs = nil
}

func (k *KnowledgeEnhancer) Close() {
	k.pool.Release()
	k.errs = nil
}

// Infer 函数用于总结/提问来增强对文段的理解，使用了流式推理的接口，是较为底层的函数
// 增强后的结果将直接添加到Chunk的 augmented_chunks 中
func (k *KnowledgeEnhancer) Infer(ctx context.Context, reqs ...*EnhanceReq) (err error) {
	// 等待上一个任务执行完毕
	k.pool.Wait()

	// 退出时清理错误列表
	defer k.clean()

	// 添加任务至协程池
	for _, req := range reqs {
		// 一个chunk,有多种增强模式。并行执行时将结果写在chunk上,需要加锁
		num := req.Num
		if num < 1 {
			num = 1
		}
		for i, mode := range req.Modes { // 增强模式，支持多种
			var instruction string
			if len(req.Prompts) > i {
				instruction = req.Prompts[i]
			} else {
				instruction, err = getPromptByMode(ctx, mode)
				if err != nil {
					k.errs = append(k.errs, err)
					continue
				}
			}

			for _, chunk := range req.Chunks {
				switch {
				case mode == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION:
					// 生成问题，特殊处理，串行生成多个问题
					k.pool.AddTask(k.newTask(ctx, chunk, instruction, mode, req.Callback, req.Params, num))
				case mode == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY:
					// 生成总结，只允许生成一个总结
					k.pool.AddTask(k.newTask(ctx, chunk, instruction, mode, req.Callback, req.Params, 1))
				default:
					// 生成其他增强类型，并行处理
					for i := 0; i < num; i++ {
						k.pool.AddTask(k.newTask(ctx, chunk, instruction, mode, req.Callback, req.Params, 1))
					}
				}
			}
		}
	}

	// 等待所有任务完成
	k.pool.Wait()
	if len(k.errs) > 0 {
		// 过程存在错误
		for _, e := range k.errs {
			stdlog.WithError(e).Errorf("error happened while handling infer task")
		}
		return stderr.Internal.Cause(stderr.JoinErrors(k.errs...), "error happened while handling task， please check the error list")
	}
	return nil
}

func (k *KnowledgeEnhancer) generateQuestions(ctx context.Context, instruction string, text string, num int, params map[string]any, resHandler func(string) error) error {
	var generatedQuestions []string
	for i := 0; i < num; i++ {
		currentPrompt := GetTextQuestionPrompt(ctx, instruction, text, generatedQuestions)
		req := &triton.LLMChatReq{
			Query:  currentPrompt,
			Stream: false,
			Stop:   false,
			Params: params,
		}
		openaiChatReq := Cvt2OpenaiChatReq(req)
		openaiChatReq.Messages = append(openaiChatReq.Messages, triton.MultimodalMessageItem{
			Role:    "system",
			Content: helper.GetLangPrompt(ctx),
		})
		if err := SyncChatForTaskMode(ctx, k.model, openaiChatReq, func(textContent string) error {
			generatedQuestions = append(generatedQuestions, textContent)
			return resHandler(textContent)
		}); err != nil {
			return err
		}
	}
	return nil
}

func (k *KnowledgeEnhancer) generateSummary(ctx context.Context, instruction string, text string, params map[string]any, resHandler func(string) error) error {
	prompt := GetTextSummaryPrompt(ctx, instruction, text)
	req := &triton.LLMChatReq{
		Query:  prompt,
		Stream: false,
		Stop:   false,
		Params: params,
	}
	openaiChatReq := Cvt2OpenaiChatReq(req)
	openaiChatReq.Messages = append(openaiChatReq.Messages, triton.MultimodalMessageItem{
		Role:    "system",
		Content: helper.GetLangPrompt(ctx),
	})
	return SyncChatForTaskMode(ctx, k.model, openaiChatReq, resHandler)
}

func (k *KnowledgeEnhancer) generateCustom(ctx context.Context, instruction string, text string, params map[string]any, resHandler func(string) error) error {
	prompt := GetTextCustomPrompt(ctx, instruction, text)
	req := &triton.LLMChatReq{
		Query:  prompt,
		Stream: false,
		Stop:   false,
		Params: params,
	}
	openaiChatReq := Cvt2OpenaiChatReq(req)
	openaiChatReq.Messages = append(openaiChatReq.Messages, triton.MultimodalMessageItem{
		Role:    "system",
		Content: helper.GetLangPrompt(ctx),
	})
	return SyncChatForTaskMode(ctx, k.model, openaiChatReq, resHandler)
}

func (k *KnowledgeEnhancer) generateImageDescription(ctx context.Context, file string, instruction string, params map[string]any, resHandler func(string) error) error {
	req, err := GetBaseChatReq(k.model)
	if err != nil {
		return err
	}

	sfsFilePath, err := stdfs.NewRelativeFilePath(file)
	if err != nil {
		return err
	}
	bytes, err := os.ReadFile(sfsFilePath.ToAbsFilePath())
	if err != nil {
		return err
	}
	base64String := base64.StdEncoding.EncodeToString(bytes)
	messages := make([]triton.MultimodalMessageItem, 0)
	messages = append(messages, triton.MultimodalMessageItem{Role: "user", Content: []triton.MultimodalContentItem{
		{
			Text: instruction,
			Type: "text",
		},
		{
			Type: "image_url",
			ImageUrl: map[string]string{
				"url":    fmt.Sprintf("data:image/jpeg;base64,%s", base64String),
				"detail": "high",
			},
		},
	}})
	messages = append(messages, triton.MultimodalMessageItem{
		Role:    "system",
		Content: helper.GetLangPrompt(ctx),
	})
	req.Messages = messages
	return SyncChatForTaskMode(ctx, k.model, req, resHandler)
}

func (k *KnowledgeEnhancer) generateOther(ctx context.Context, text string, instruction string, params map[string]any, resHandler func(string) error) error {
	req := &triton.LLMChatReq{
		Prompt: instruction,
		Query:  text,
		Stream: false,
		Stop:   false,
		Params: params,
	}
	openaiChatReq := Cvt2OpenaiChatReq(req)
	openaiChatReq.Messages = append(openaiChatReq.Messages, triton.MultimodalMessageItem{
		Role:    "system",
		Content: helper.GetLangPrompt(ctx),
	})
	return SyncChatForTaskMode(ctx, k.model, openaiChatReq, resHandler)
}

func (k *KnowledgeEnhancer) newTask(ctx context.Context, chunk *pb.Chunk, instruction string, mode pb.AugmentedChunkType, callback func(), params map[string]any, num int) func() (any, error) {
	return func() (ret any, err error) {
		defer func() {
			if err != nil {
				k.Lock()
				k.errs = append(k.errs, err)
				k.Unlock()
			}
		}()

		resHandler := func(textContent string) error {
			if callback != nil {
				callback()
			}
			k.Lock()
			defer k.Unlock()
			chunk.AugmentedChunks = append(chunk.AugmentedChunks, &pb.AugmentedChunk{
				Id:            uuid.NewString(),
				Content:       textContent,
				AugmentedType: mode,
			})
			return nil
		}

		switch mode {
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION:
			err = k.generateQuestions(ctx, instruction, chunk.Content, num, params, resHandler)
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY:
			err = k.generateSummary(ctx, instruction, chunk.Content, params, resHandler)
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_CUSTOM:
			err = k.generateCustom(ctx, instruction, chunk.Content, params, resHandler)
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION:
			err = k.generateImageDescription(ctx, chunk.Content, instruction, params, resHandler)
		default:
			err = k.generateOther(ctx, chunk.Content, instruction, params, resHandler)
		}
		return nil, err
	}
}

func getPromptByMode(ctx context.Context, mode pb.AugmentedChunkType) (string, error) {
	if helper.IsChinese(ctx) {
		switch mode {
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY:
			return DefaultInstructionTextSummary, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION:
			return DefaultInstructionTextQuestion, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE:
			return PromptTextQuesRewrite, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION:
			return PromptTableDescription, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY:
			return PromptTableSummary, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION:
			return PromptImageDescription, nil
		}
	} else {
		switch mode {
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY:
			return DefaultInstructionTextSummaryEn, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION:
			return DefaultInstructionTextQuestionEn, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE:
			return PromptTextQuesRewriteEn, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION:
			return PromptTableDescriptionEn, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY:
			return PromptTableSummaryEn, nil
		case pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION:
			return PromptImageDescriptionEn, nil
		}
	}
	return "", stderr.Error("the mode of %s is not supported", mode.String())
}

// EnhanceKnowledgeChunks 函数用于增强对文段的理解
func EnhanceKnowledgeChunks(ctx context.Context, model *pb.ModelService, concurrency int, reqs ...*EnhanceReq) error {
	if len(reqs) == 0 {
		err := stderr.BadRequest.Error("enhanceReqList is empty")
		stdlog.Errorf("enhanceReqList is empty")
		return err
	}
	ke, err := NewKnowledgeEnhancer(ctx, model, concurrency)
	if err != nil {
		stdlog.Errorf("failed to init KnowledgeBaseEnhancement config, err: %v", err)
		return err
	}
	defer ke.Close()
	return ke.Infer(ctx, reqs...)
}

func EnhanceWithReq(ctx context.Context, model *pb.ModelService, concurrency int, enhanceReqs []*EnhanceReq) error {
	return EnhanceKnowledgeChunks(ctx, model, concurrency, enhanceReqs...)
}
