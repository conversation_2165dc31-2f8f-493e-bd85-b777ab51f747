package clients

import (
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

func TestTranswarpAdaptor_ResolveInferResp(t *testing.T) {

	body := `{
  "id": "cmpl-76d3235030334008ba9fae682ded1996",
  "created": 678657,
  "model_info": {
    "model_name": "transwarp-reranker-v1",
    "nmodel_version": "1"
  },
  "query": "你好",
  "scores": [
    0.9709177613258362,
    0.023081714287400246
  ],
  "texts": [
    "How are you?",
    "今天天气不错"
  ],
  "usage": {
    "prompt_tokens": 19,
    "total_tokens": 19,
    "completion_tokens": 0
  }
}`

	res, err := getResultFromStdInferResp[triton.RerankRes](body)
	if err != nil {
		panic(err)
	}
	t.Logf("%+v", res)
}
