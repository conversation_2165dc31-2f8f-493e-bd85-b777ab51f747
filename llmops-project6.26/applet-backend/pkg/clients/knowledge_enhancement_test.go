package clients

import (
	"context"
	"fmt"
	"testing"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

var (
	TestModel = new(pb.ModelService)
)

func TestEnhanceWithReq(t *testing.T) {
	ctx := context.Background()
	text1 := "1 虚拟地址\n虚拟地址是指由程序产生的由段选择符和段内偏移地址两个部分组成的地址。因为这两部分组成的地址并没有直接用来访问物理内存，而是需要通过分段地址变换机制处理或映射后才能对应到物理内存上，因此这种地址被称为虚拟地址。\n虚拟地址空间由 GDT 映射的全局地址空间和由 LDT 映射的局部地址空间组成。选择符的索引部分由 13 个比特位表示，加上区分 GDT 和 LDT 的 1 个比特位，因此 Intel 80x86 CPU 一共可以索引 16384（2^14） 个选择符。若每个段的长度都取最大值 4G ，则最大虚拟地址空间范围是 16384*4G = 64T(64*4).\n2 逻辑地址\n逻辑地址是指由程序产生的与段相关的偏移地址部分。在 Intel 保护模式下即是指程序执行代码段限长内的偏移地址（假定代码段、数据段完全一样）。应用程序员仅需要与逻辑地址打交道，而分段和分页机制对他来说是完全透明的，仅由系统编程人员涉及。不过有些资料并不区分逻辑地址和虚拟地址的概念，而是将它们统称为逻辑地址。\n3 线性地址\n线性地址(Linear Address) 是虚拟地址到物理地址变换之间的中间层，是处理器可寻址的内存空间(称为线性地址空间)中的地址。程序代码会产生逻辑地址，或者说是段中的偏移地址，加上相应段的基地址就生成了一个线性地址。如果启用了分页机制，那么线性地址可以再经变换以产生一个物理地址。若没有启用分页机制，那么线性地址直接就是物理地址。Intel 80386 的线性地址空间容量为 4G。\n4 物理地址\n物理地址 (Physical Address) 是指出现在 CPU 外部地址总线上的寻址物理内存的地址信号，是地址变换的最终结果地址。如果启用了分页机制，那么线性地址会使用页目录和页表中的项变换成物理地址。如果没有启用分页机制，那么线性地址就直接成为物理地址了。\n5 虚拟存储（虚拟内存）\n虚拟存储（虚拟内存）(Virtual Memory) 是指计算机呈现出要比实际拥有的内存大得多的内存虚拟存储 (或虚拟内存)量。因此它允许程序员编制并运行比实际系统拥有的内存大得多的程序。这使得许多大型项目也能够在具有有限内存资源的系统上实现。一个很恰当的比喻是:你不需要很长的轨道就可以让一列火车从上海开到北京。你只需要足够长的铁轨(比如说3 公里)就可以完成这个任务。采取的方法是把后面的铁轨广刻铺到火车的前面，只要你的操作足够快并能满足要求，列车就能象在一条完整的轨道上运行。这也就是虚拟内存管理需要完成的任务。在 Linux 0.11 内核中，给每个程序(进程)都划分了总容量为 64MB的虚拟内存空间。因此程序的逻辑地址范围是 0x0000000 到 0x4000000。\n如上所述，有时我们也把逻辑地址称为虚拟地址。因为逻辑地址与虚拟内存空间的概念类似，并且也是与实际物理内存容量无关。"
	text2 := "内存分页管理\n若采用了分页机制，则此时线性地址只是一个中间结果，还需要使用分页机制进行变换，再最终映射到实际物理内存地址上。与分段机制类似，分页机制允许我们重新定向(变换)每次内存引用，以适应我们的特殊要求。使用分页机制最普遍的场合是当系统内存实际上被分成很多凌乱的块时。它可以建立个大而连续的内存空间映像，好让程序不用操心和管理这些分散的内存块。分页机制增强了分段机制的性能。另外，页地址变换建立在段变换基础之上，任何分页机制的保护措施并不会取代段变换的保护措施而只是进行更进一步的检查操作。\n内存分页管理机制的基本原理是将 CPU 整个线性内存区域划分成 4096 字节为1页的内存页面。程序申请使用内存时，系统就以内存页为单位进行分配。内存分页机制的实现方式与分段机制很相似，但并不如分段机制那么完善。因为分页机制是在分段机制之上实现的，所以其结果是对系统内存具有非常灵活的控制权，并且在分段机制的内存保护上更增加了分页保护机制。为了在 80X86 保护模式下使用分页机制，需要把控制寄存器 CRO 的最高比特位(位 31) 置位。\n在使用这种内存分页管理方法时，每个执行中的进程(任务) 可以使用比实际内存容量大得多的连续地址空间。为了在使用分页机制的条件下把线性地址映射到容量相对很小的物理内存空间上，80386审用了页目录表和页表。页目录表项与页表项格式基本相同，都占用4个字节，并日每个页目录表或页表必须只能包含 1024 个页表项。因此一个页目录表或一个页表分别共占用1页内存。页目录项和页表项的小区别在于页表项有个已写位 D (Dirty)，而页目录项则没有。\n线性地址到物理地址的变换过程见图 5-9 所示。图中控制寄存器 CR3 保存着是当前页目录表在物理内存中的基地址(因此 CR3 也被称为页目录基地址寄存器 PDBR)。32 位的线性地址被分成三个部分分别用来在页目录表和页表中定位对应的页目录项和页表项以及在对应的物理内存页面中指定页面内的偏移位置。因为1个页表可有 1024 项，因此一个页表最多可以映射 \n1024\n∗\n4\n�\n�\n=\n4\n�\n�\n1024∗4KB=4MB\n 内存:又因为一人页目录表最多有 1024项，对应1024 个二级页表，因此一个页目录表最多可以映射 \n1024\n∗\n4\n�\n�\n=\n4\n�\n�\n1024∗4MB=4GB\n 容量的内存。即一个页目录表就可以映射整个线性地址空间范围。\n线性地址到物理地址的变换示意图\n线性地址到物理地址的变换示意图\n由于 Linux 0.1x 系统中内核和所有任务都共用同一个页目录表，使得任何时刻处理器线性地址空间到物理地址空间的映射函数都一样。因此为了让内核和所有任务都不互相重叠和干扰，它们都必须从虚拟地址空间映射到线性地址空间的不同位置，即占用不同的线性地址空间范围。\n对于 Intel 80386 系统，其 CPU 可以提供多达 4G 的线性地址空间。一个任务的虚拟地址需要首先通过其局部段描述符变换为 CPU 整个线性地址空间中的地址，然后再使用页目录表 PDT (一级页表)和页表 PT(二级页表)映射到实际物理地址页上。为了使用实际物理内存，每个进程的线性地址通过二级内存页表动态地映射到主内存区域的不同物理内存页上。由于 Linux 0.11 中把每个进程最大可用虚拟内存空间定义为 64MB，因此每个进程的逻辑地址通过加上(任务号)*64MB，即可转换为线性空间中的地址。不过在注释中，在不至于搞混的情况下我们有时将进程中的此类地址简单地称为逻辑地址或线性地址。\n对于 Linux 0.11 系统，内核设置全局描述符表 GDT 中的段描述符项数最大为 256，其中 2 项空闲、2项系统使用，每个进程使用两项。因此，此时系统可以最多容纳(256-4)/2 =126 个任务，并且虚拟地范围是 ((256-4)/2)* 64MB 约等于 8G。但0.11内核中人工定义最大任务数NR TASKS = 64个，每务逻辑地址范围是 64M，并且各个任务在线性地址空间中的起始位置是 (任务号)64MB。因此全部任所使用的线性地址空间范围是 64MB64 =4G，见图 5-10 所示。图中示出了当系统具有 4 个任务时的情况。内核代码段和数据段被映射到线性地址空间的开始 16MB 部分，并且代码和数据段都映射到同一个区域，完全互相重叠。而第 1 个任务(任务 0) 是由内核“人工”启动运行的，其代码和数据包含在内核代码和数据中，因此该任务所占用的线性地址空间范围比较特殊。任务 0的代码段和数据段的长度是从线性地址 0 开始的 640KB 范围，其代码和数据段也完全重叠，并且与内核代码段和数据段有重叠的部分。实际上，Linux 0.11 中所有任务的指今空间I (Instruction) 和数据空间 D (Data) 都合用一块内存即一个进程的所有代码、数据和堆栈部分都处于同一内存段中，也即是 I&D 不分离的一种使用方式。\nLinux 0.11 线性地址空间的使用示意图\nLinux 0.11 线性地址空间的使用示意图\n任务1的线性地址空间范围也只有从 64MB 开始的 640KB 长度。它们之间的详细对应关系见后面说明。任务 2 和任务 3 分别被映射线性地址 128MB 和 192MB 开始的地方，并且它们的逻辑地址范围均是64MB。由于 4G 地址空间范围正好是 CPU 的线性地址空间范围和可寻址的最大物理地址空间范围，而且在把任务 0 和任务 1 的逻辑地址范围看作 64MB 时，系统中同时可有任务的逻辑地址范围总和也是4GB，因此在 0.11 内核中比较容易混淆三种地址概念。\n如果也按照线性空间中任务的排列顺序排列虚拟空间中的任务，那么我们可以有图 5-11 所示的系统同时可拥有所有任务在虚拟地址空间中的示意图，所占用虚拟空间范围也是 4GB。其中没有考虑内核代码和数据在虚拟空间中所占用的范围。另外，在图中对于进程2 和进程3 还分别给出了各自逻辑空间中代码段和数据段(包括数据和堆栈内容) 的位置示意图。\nLinux 0.11 系统任务在虚拟空间中顺序排列所占的空间范围\nLinux 0.11 系统任务在虚拟空间中顺序排列所占的空间范围\n请还需注意，进程逻辑地址空间中代码段 (Code Section) 和数据段 (Data Section)的概念与 CPU分段机制中的代码段和数据段不是同一个概念。CPU 分段机制中段的概念确定了在线性地址空间中一个段的用途以及被执行或访问的约束和限制，每个段可以设置在 4GB 线性地址空间中的任何地方，它们可以相互独立也可以完全重叠或部分重叠。而进程在其逻辑地址空间中的代码段和数据段则是指由编译器在编译程序和操作系统在加载程序时规定的在进程逻辑空间中顺序排列的代码区域、初始化和未初始化的数据区域以及堆栈区域。进程逻辑地址空间中代码段和数据段等结构形式见图所示。有关逻辑地址空间的说明请参见内存管理一章内容。"

	enhanceReqs := make([]*EnhanceReq, 0)
	chunks := []*pb.Chunk{&pb.Chunk{Content: text1}, &pb.Chunk{Content: text2}}
	enhanceReqs = append(enhanceReqs, &EnhanceReq{
		Chunks: chunks,
		Num:    3,
		Modes:  []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY, pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
	})
	enhanceReqs = append(enhanceReqs, &EnhanceReq{
		Chunks: chunks,
		Num:    3,
		Modes:  []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY, pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
	})
	if err := EnhanceWithReq(ctx, TestModel, 0, enhanceReqs); err != nil {
		stdlog.Info(err)
	}
	stdlog.Info(chunks)
}
func TestKnowledgeEnhancementWithChunk(t *testing.T) {
	ctx := context.Background()
	text1 := "1 虚拟地址\n虚拟地址是指由程序产生的由段选择符和段内偏移地址两个部分组成的地址。因为这两部分组成的地址并没有直接用来访问物理内存，而是需要通过分段地址变换机制处理或映射后才能对应到物理内存上，因此这种地址被称为虚拟地址。\n虚拟地址空间由 GDT 映射的全局地址空间和由 LDT 映射的局部地址空间组成。选择符的索引部分由 13 个比特位表示，加上区分 GDT 和 LDT 的 1 个比特位，因此 Intel 80x86 CPU 一共可以索引 16384（2^14） 个选择符。若每个段的长度都取最大值 4G ，则最大虚拟地址空间范围是 16384*4G = 64T(64*4).\n2 逻辑地址\n逻辑地址是指由程序产生的与段相关的偏移地址部分。在 Intel 保护模式下即是指程序执行代码段限长内的偏移地址（假定代码段、数据段完全一样）。应用程序员仅需要与逻辑地址打交道，而分段和分页机制对他来说是完全透明的，仅由系统编程人员涉及。不过有些资料并不区分逻辑地址和虚拟地址的概念，而是将它们统称为逻辑地址。\n3 线性地址\n线性地址(Linear Address) 是虚拟地址到物理地址变换之间的中间层，是处理器可寻址的内存空间(称为线性地址空间)中的地址。程序代码会产生逻辑地址，或者说是段中的偏移地址，加上相应段的基地址就生成了一个线性地址。如果启用了分页机制，那么线性地址可以再经变换以产生一个物理地址。若没有启用分页机制，那么线性地址直接就是物理地址。Intel 80386 的线性地址空间容量为 4G。\n4 物理地址\n物理地址 (Physical Address) 是指出现在 CPU 外部地址总线上的寻址物理内存的地址信号，是地址变换的最终结果地址。如果启用了分页机制，那么线性地址会使用页目录和页表中的项变换成物理地址。如果没有启用分页机制，那么线性地址就直接成为物理地址了。\n5 虚拟存储（虚拟内存）\n虚拟存储（虚拟内存）(Virtual Memory) 是指计算机呈现出要比实际拥有的内存大得多的内存虚拟存储 (或虚拟内存)量。因此它允许程序员编制并运行比实际系统拥有的内存大得多的程序。这使得许多大型项目也能够在具有有限内存资源的系统上实现。一个很恰当的比喻是:你不需要很长的轨道就可以让一列火车从上海开到北京。你只需要足够长的铁轨(比如说3 公里)就可以完成这个任务。采取的方法是把后面的铁轨广刻铺到火车的前面，只要你的操作足够快并能满足要求，列车就能象在一条完整的轨道上运行。这也就是虚拟内存管理需要完成的任务。在 Linux 0.11 内核中，给每个程序(进程)都划分了总容量为 64MB的虚拟内存空间。因此程序的逻辑地址范围是 0x0000000 到 0x4000000。\n如上所述，有时我们也把逻辑地址称为虚拟地址。因为逻辑地址与虚拟内存空间的概念类似，并且也是与实际物理内存容量无关。"
	// text2 := "内存分页管理\n若采用了分页机制，则此时线性地址只是一个中间结果，还需要使用分页机制进行变换，再最终映射到实际物理内存地址上。与分段机制类似，分页机制允许我们重新定向(变换)每次内存引用，以适应我们的特殊要求。使用分页机制最普遍的场合是当系统内存实际上被分成很多凌乱的块时。它可以建立个大而连续的内存空间映像，好让程序不用操心和管理这些分散的内存块。分页机制增强了分段机制的性能。另外，页地址变换建立在段变换基础之上，任何分页机制的保护措施并不会取代段变换的保护措施而只是进行更进一步的检查操作。\n内存分页管理机制的基本原理是将 CPU 整个线性内存区域划分成 4096 字节为1页的内存页面。程序申请使用内存时，系统就以内存页为单位进行分配。内存分页机制的实现方式与分段机制很相似，但并不如分段机制那么完善。因为分页机制是在分段机制之上实现的，所以其结果是对系统内存具有非常灵活的控制权，并且在分段机制的内存保护上更增加了分页保护机制。为了在 80X86 保护模式下使用分页机制，需要把控制寄存器 CRO 的最高比特位(位 31) 置位。\n在使用这种内存分页管理方法时，每个执行中的进程(任务) 可以使用比实际内存容量大得多的连续地址空间。为了在使用分页机制的条件下把线性地址映射到容量相对很小的物理内存空间上，80386审用了页目录表和页表。页目录表项与页表项格式基本相同，都占用4个字节，并日每个页目录表或页表必须只能包含 1024 个页表项。因此一个页目录表或一个页表分别共占用1页内存。页目录项和页表项的小区别在于页表项有个已写位 D (Dirty)，而页目录项则没有。\n线性地址到物理地址的变换过程见图 5-9 所示。图中控制寄存器 CR3 保存着是当前页目录表在物理内存中的基地址(因此 CR3 也被称为页目录基地址寄存器 PDBR)。32 位的线性地址被分成三个部分分别用来在页目录表和页表中定位对应的页目录项和页表项以及在对应的物理内存页面中指定页面内的偏移位置。因为1个页表可有 1024 项，因此一个页表最多可以映射 \n1024\n∗\n4\n�\n�\n=\n4\n�\n�\n1024∗4KB=4MB\n 内存:又因为一人页目录表最多有 1024项，对应1024 个二级页表，因此一个页目录表最多可以映射 \n1024\n∗\n4\n�\n�\n=\n4\n�\n�\n1024∗4MB=4GB\n 容量的内存。即一个页目录表就可以映射整个线性地址空间范围。\n线性地址到物理地址的变换示意图\n线性地址到物理地址的变换示意图\n由于 Linux 0.1x 系统中内核和所有任务都共用同一个页目录表，使得任何时刻处理器线性地址空间到物理地址空间的映射函数都一样。因此为了让内核和所有任务都不互相重叠和干扰，它们都必须从虚拟地址空间映射到线性地址空间的不同位置，即占用不同的线性地址空间范围。\n对于 Intel 80386 系统，其 CPU 可以提供多达 4G 的线性地址空间。一个任务的虚拟地址需要首先通过其局部段描述符变换为 CPU 整个线性地址空间中的地址，然后再使用页目录表 PDT (一级页表)和页表 PT(二级页表)映射到实际物理地址页上。为了使用实际物理内存，每个进程的线性地址通过二级内存页表动态地映射到主内存区域的不同物理内存页上。由于 Linux 0.11 中把每个进程最大可用虚拟内存空间定义为 64MB，因此每个进程的逻辑地址通过加上(任务号)*64MB，即可转换为线性空间中的地址。不过在注释中，在不至于搞混的情况下我们有时将进程中的此类地址简单地称为逻辑地址或线性地址。\n对于 Linux 0.11 系统，内核设置全局描述符表 GDT 中的段描述符项数最大为 256，其中 2 项空闲、2项系统使用，每个进程使用两项。因此，此时系统可以最多容纳(256-4)/2 =126 个任务，并且虚拟地范围是 ((256-4)/2)* 64MB 约等于 8G。但0.11内核中人工定义最大任务数NR TASKS = 64个，每务逻辑地址范围是 64M，并且各个任务在线性地址空间中的起始位置是 (任务号)64MB。因此全部任所使用的线性地址空间范围是 64MB64 =4G，见图 5-10 所示。图中示出了当系统具有 4 个任务时的情况。内核代码段和数据段被映射到线性地址空间的开始 16MB 部分，并且代码和数据段都映射到同一个区域，完全互相重叠。而第 1 个任务(任务 0) 是由内核“人工”启动运行的，其代码和数据包含在内核代码和数据中，因此该任务所占用的线性地址空间范围比较特殊。任务 0的代码段和数据段的长度是从线性地址 0 开始的 640KB 范围，其代码和数据段也完全重叠，并且与内核代码段和数据段有重叠的部分。实际上，Linux 0.11 中所有任务的指今空间I (Instruction) 和数据空间 D (Data) 都合用一块内存即一个进程的所有代码、数据和堆栈部分都处于同一内存段中，也即是 I&D 不分离的一种使用方式。\nLinux 0.11 线性地址空间的使用示意图\nLinux 0.11 线性地址空间的使用示意图\n任务1的线性地址空间范围也只有从 64MB 开始的 640KB 长度。它们之间的详细对应关系见后面说明。任务 2 和任务 3 分别被映射线性地址 128MB 和 192MB 开始的地方，并且它们的逻辑地址范围均是64MB。由于 4G 地址空间范围正好是 CPU 的线性地址空间范围和可寻址的最大物理地址空间范围，而且在把任务 0 和任务 1 的逻辑地址范围看作 64MB 时，系统中同时可有任务的逻辑地址范围总和也是4GB，因此在 0.11 内核中比较容易混淆三种地址概念。\n如果也按照线性空间中任务的排列顺序排列虚拟空间中的任务，那么我们可以有图 5-11 所示的系统同时可拥有所有任务在虚拟地址空间中的示意图，所占用虚拟空间范围也是 4GB。其中没有考虑内核代码和数据在虚拟空间中所占用的范围。另外，在图中对于进程2 和进程3 还分别给出了各自逻辑空间中代码段和数据段(包括数据和堆栈内容) 的位置示意图。\nLinux 0.11 系统任务在虚拟空间中顺序排列所占的空间范围\nLinux 0.11 系统任务在虚拟空间中顺序排列所占的空间范围\n请还需注意，进程逻辑地址空间中代码段 (Code Section) 和数据段 (Data Section)的概念与 CPU分段机制中的代码段和数据段不是同一个概念。CPU 分段机制中段的概念确定了在线性地址空间中一个段的用途以及被执行或访问的约束和限制，每个段可以设置在 4GB 线性地址空间中的任何地方，它们可以相互独立也可以完全重叠或部分重叠。而进程在其逻辑地址空间中的代码段和数据段则是指由编译器在编译程序和操作系统在加载程序时规定的在进程逻辑空间中顺序排列的代码区域、初始化和未初始化的数据区域以及堆栈区域。进程逻辑地址空间中代码段和数据段等结构形式见图所示。有关逻辑地址空间的说明请参见内存管理一章内容。"
	text3 := "Eents 概览\n我们先来做个简单的示例，来看看 Kubernetes 集群中的 events 是什么。\n\n创建一个新的名叫 moelove 的 namespace ，然后在其中创建一个叫做 redis 的 deployment。接下来查看这个 namespace 中的所有 events。\n\n(MoeLove) ➜ kubectl create ns moelove\nnamespace/moelove created\n(MoeLove) ➜ kubectl -n moelove create deployment redis --image=ghcr.io/moelove/redis:alpine \ndeployment.apps/redis created\n(MoeLove) ➜ kubectl -n moelove get deploy\nNAME    READY   UP-TO-DATE   AVAILABLE   AGE\nredis   1/1     1            1           11s\n(MoeLove) ➜ kubectl -n moelove get events\nLAST SEEN   TYPE     REASON              OBJECT                        MESSAGE\n21s         Normal   Scheduled           pod/redis-687967dbc5-27vmr    Successfully assigned moelove/redis-687967dbc5-27vmr to kind-worker3\n21s         Normal   Pulling             pod/redis-687967dbc5-27vmr    Pulling image \"ghcr.io/moelove/redis:alpine\"\n15s         Normal   Pulled              pod/redis-687967dbc5-27vmr    Successfully pulled image \"ghcr.io/moelove/redis:alpine\" in 6.814310968s\n14s         Normal   Created             pod/redis-687967dbc5-27vmr    Created container redis\n14s         Normal   Started             pod/redis-687967dbc5-27vmr    Started container redis\n22s         Normal   SuccessfulCreate    replicaset/redis-687967dbc5   Created pod: redis-687967dbc5-27vmr\n22s         Normal   ScalingReplicaSet   deployment/redis              Scaled up replica set redis-687967dbc5 to 1\n但是我们会发现默认情况下 kubectl get events 并没有按照 events 发生的顺序进行排列，所以我们往往需要为其增加 --sort-by='{.metadata.creationTimestamp}' 参数来让其输出可以按时间进行排列。\n\n这也是为何 Kubernetes v1.23 版本中会新增 kubectl alpha events 命令的原因。我在之前的文章《K8S 生态周报| Kubernetes v1.23.0 正式发布，新特性一览》中已进行了详细的介绍，这里就不展开了。\n\n按时间排序后可以看到如下结果：\n\n(MoeLove) ➜ kubectl -n moelove get events --sort-by='{.metadata.creationTimestamp}'\nLAST SEEN   TYPE     REASON              OBJECT                        MESSAGE\n2m12s       Normal   Scheduled           pod/redis-687967dbc5-27vmr    Successfully assigned moelove/redis-687967dbc5-27vmr to kind-worker3\n2m13s       Normal   SuccessfulCreate    replicaset/redis-687967dbc5   Created pod: redis-687967dbc5-27vmr\n2m13s       Normal   ScalingReplicaSet   deployment/redis              Scaled up replica set redis-687967dbc5 to 1\n2m12s       Normal   Pulling             pod/redis-687967dbc5-27vmr    Pulling image \"ghcr.io/moelove/redis:alpine\"\n2m6s        Normal   Pulled              pod/redis-687967dbc5-27vmr    Successfully pulled image \"ghcr.io/moelove/redis:alpine\" in 6.814310968s\n2m5s        Normal   Created             pod/redis-687967dbc5-27vmr    Created container redis\n2m5s        Normal   Started             pod/redis-687967dbc5-27vmr    Started container redis\n通过以上的操作，我们可以发现 events 实际上是 Kubernetes 集群中的一种资源。当 Kubernetes 集群中资源状态发生变化时，可以产生新的 events。\n\n深入 Events\n单个 Event 对象\n既然 events 是 Kubernetes 集群中的一种资源，正常情况下它的 metadata.name 中应该包含其名称，用于进行单独操作。所以我们可以使用如下命令输出其 name ：\n\n(MoeLove) ➜ kubectl -n moelove get events --sort-by='{.metadata.creationTimestamp}' -o jsonpath='{range .items[*]}{.metadata.name}{\"\\n\"}{end}'\nredis-687967dbc5-27vmr.16c4fb7bde8c69d2\nredis-687967dbc5.16c4fb7bde6b54c4\nredis.16c4fb7bde1bf769\nredis-687967dbc5-27vmr.16c4fb7bf8a0ab35\nredis-687967dbc5-27vmr.16c4fb7d8ecaeff8\nredis-687967dbc5-27vmr.16c4fb7d99709da9\nredis-687967dbc5-27vmr.16c4fb7d9be30c06\n选择其中的任意一条 event 记录，将其输出为 YAML 格式进行查看：\n\n(MoeLove) ➜ kubectl -n moelove get events redis-687967dbc5-27vmr.16c4fb7bde8c69d2 -o yaml\naction: Binding\napiVersion: v1\neventTime: \"2021-12-28T19:31:13.702987Z\"\nfirstTimestamp: null\ninvolvedObject:\n  apiVersion: v1\n  kind: Pod\n  name: redis-687967dbc5-27vmr\n  namespace: moelove\n  resourceVersion: \"330230\"\n  uid: 71b97182-5593-47b2-88cc-b3f59618c7aa\nkind: Event\nlastTimestamp: null\nmessage: Successfully assigned moelove/redis-687967dbc5-27vmr to kind-worker3\nmetadata:\n  creationTimestamp: \"2021-12-28T19:31:13Z\"\n  name: redis-687967dbc5-27vmr.16c4fb7bde8c69d2\n  namespace: moelove\n  resourceVersion: \"330235\"\n  uid: e5c03126-33b9-4559-9585-5e82adcd96b0\nreason: Scheduled\nreportingComponent: default-scheduler\nreportingInstance: default-scheduler-kind-control-plane\nsource: {}\ntype: Normal\n可以看到其中包含了很多信息, 这里我们先不展开。我们看另一个例子。\n\nkubectl describe 中的 Events\n我们可以分别对 Deployment 对象和 Pod 对象执行 describe 的操作，可以得到如下结果（省略掉了中间输出）：\n\n对 Deployment 操作\n(MoeLove) ➜ kubectl -n moelove describe deploy/redis                \nName:                   redis\nNamespace:              moelove\n...\nEvents:\n  Type    Reason             Age   From                   Message\n  ----    ------             ----  ----                   -------\n  Normal  ScalingReplicaSet  15m   deployment-controller  Scaled up replica set redis-687967dbc5 to 1\n对 Pod 操作\n(MoeLove) ➜ kubectl -n moelove describe pods redis-687967dbc5-27vmr\nName:         redis-687967dbc5-27vmr                                                                 \nNamespace:    moelove\nPriority:     0\nEvents:\n  Type    Reason     Age   From               Message\n  ----    ------     ----  ----               -------\n  Normal  Scheduled  18m   default-scheduler  Successfully assigned moelove/redis-687967dbc5-27vmr to kind-worker3\n  Normal  Pulling    18m   kubelet            Pulling image \"ghcr.io/moelove/redis:alpine\"\n  Normal  Pulled     17m   kubelet            Successfully pulled image \"ghcr.io/moelove/redis:alpine\" in 6.814310968s\n  Normal  Created    17m   kubelet            Created container redis\n  Normal  Started    17m   kubelet            Started container redis\n我们可以发现 对不同的资源对象进行 describe 的时候，能看到的 events 内容都是与自己有直接关联的。在 describe Deployment 的时候，看不到 Pod 相关的 Events 。\n\n这说明， Event 对象中是包含它所描述的资源对象的信息的，它们是有直接联系的。\n\n结合前面我们看到的单个 Event 对象，我们发现 involvedObject 字段中内容就是与该 Event 相关联的资源对象的信息。\n\n更进一步了解 Events\n我们来看看如下的示例，创建一个 Deployment ，但是使用一个不存在的镜像：\n\n(MoeLove) ➜ kubectl -n moelove create deployment non-exist --image=ghcr.io/moelove/non-exist\ndeployment.apps/non-exist created\n(MoeLove) ➜ kubectl -n moelove get pods\nNAME                        READY   STATUS         RESTARTS   AGE\nnon-exist-d9ddbdd84-tnrhd   0/1     ErrImagePull   0          11s\nredis-687967dbc5-27vmr      1/1     Running        0          26m\n我们可以看到当前的 Pod 处于一个 ErrImagePull 的状态。查看当前 namespace 中的 events (我省略掉了之前 deploy/redis 的记录)\n\n(MoeLove) ➜ kubectl -n moelove get events --sort-by='{.metadata.creationTimestamp}'                                                           \nLAST SEEN   TYPE      REASON              OBJECT                           MESSAGE\n35s         Normal    SuccessfulCreate    replicaset/non-exist-d9ddbdd84   Created pod: non-exist-d9ddbdd84-tnrhd\n35s         Normal    ScalingReplicaSet   deployment/non-exist             Scaled up replica set non-exist-d9ddbdd84 to 1\n35s         Normal    Scheduled           pod/non-exist-d9ddbdd84-tnrhd    Successfully assigned moelove/non-exist-d9ddbdd84-tnrhd to kind-worker3\n17s         Warning   Failed              pod/non-exist-d9ddbdd84-tnrhd    Error: ErrImagePull\n17s         Warning   Failed              pod/non-exist-d9ddbdd84-tnrhd    Failed to pull image \"ghcr.io/moelove/non-exist\": rpc error: code = Unknown desc = failed to pull and unpack image \"ghcr.io/moelove/non-exist:latest\": failed to resolve reference \"ghcr.io/moelove/non-exist:latest\": failed to authorize: failed to fetch anonymous token: unexpected status: 403 Forbidden\n18s         Normal    Pulling             pod/non-exist-d9ddbdd84-tnrhd    Pulling image \"ghcr.io/moelove/non-exist\"\n4s          Warning   Failed              pod/non-exist-d9ddbdd84-tnrhd    Error: ImagePullBackOff\n4s          Normal    BackOff             pod/non-exist-d9ddbdd84-tnrhd    Back-off pulling image \"ghcr.io/moelove/non-exist\"\n对这个 Pod 执行 describe 操作：\n\n(MoeLove) ➜ kubectl -n moelove describe pods non-exist-d9ddbdd84-tnrhd\n...\nEvents:\n  Type     Reason     Age                    From               Message\n  ----     ------     ----                   ----               -------\n  Normal   Scheduled  4m                     default-scheduler  Successfully assigned moelove/non-exist-d9ddbdd84-tnrhd to kind-worker3\n  Normal   Pulling    2m22s (x4 over 3m59s)  kubelet            Pulling image \"ghcr.io/moelove/non-exist\"\n  Warning  Failed     2m21s (x4 over 3m59s)  kubelet            Failed to pull image \"ghcr.io/moelove/non-exist\": rpc error: code = Unknown desc = failed to pull and unpack image \"ghcr.io/moelove/non-exist:latest\": failed to resolve reference \"ghcr.io/moelove/non-exist:latest\": failed to authorize: failed to fetch anonymous token: unexpected status: 403 Forbidden\n  Warning  Failed     2m21s (x4 over 3m59s)  kubelet            Error: ErrImagePull\n  Warning  Failed     2m9s (x6 over 3m58s)   kubelet            Error: ImagePullBackOff\n  Normal   BackOff    115s (x7 over 3m58s)   kubelet            Back-off pulling image \"ghcr.io/moelove/non-exist\"\n我们可以发现，这里的输出和之前正确运行 Pod 的不一样。最主要的区别在与 Age 列。这里我们看到了类似 115s (x7 over 3m58s) 这样的输出。\n\n它的含义表示： 该类型的 event 在 3m58s 中已经发生了 7 次，最近的一次发生在 115s 之前\n\n但是当我们去直接 kubectl get events 的时候，我们并没有看到有 7 次重复的 event 。这说明 Kubernetes 会自动将重复的 events 进行合并。\n\n选择最后一条 Events (方法前面内容已经讲了) 并将其内容使用 YAML 格式进行输出：\n\n(MoeLove) ➜ kubectl -n moelove get events non-exist-d9ddbdd84-tnrhd.16c4fce570cfba46 -o yaml\napiVersion: v1\ncount: 43\neventTime: null\nfirstTimestamp: \"2021-12-28T19:57:06Z\"\ninvolvedObject:\n  apiVersion: v1\n  fieldPath: spec.containers{non-exist}\n  kind: Pod\n  name: non-exist-d9ddbdd84-tnrhd\n  namespace: moelove\n  resourceVersion: \"333366\"\n  uid: 33045163-146e-4282-b559-fec19a189a10\nkind: Event\nlastTimestamp: \"2021-12-28T18:07:14Z\"\nmessage: Back-off pulling image \"ghcr.io/moelove/non-exist\"\nmetadata:\n  creationTimestamp: \"2021-12-28T19:57:06Z\"\n  name: non-exist-d9ddbdd84-tnrhd.16c4fce570cfba46\n  namespace: moelove\n  resourceVersion: \"334638\"\n  uid: 60708be0-23b9-481b-a290-dd208fed6d47\nreason: BackOff\nreportingComponent: \"\"\nreportingInstance: \"\"\nsource:\n  component: kubelet\n  host: kind-worker3\ntype: Normal\n这里我们可以看到其字段中包括一个 count 字段，表示同类 event 发生了多少次。以及 firstTimestamp 和 lastTimestamp 分别表示了这个 event 首次出现了最近一次出现的时间。这样也就解释了前面的输出中 events 持续的周期了。\n\n彻底搞懂 Events\n以下内容是从 Events 中随便选择的一条，我们可以看到它包含的一些字段信息：\n\napiVersion: v1\ncount: 1\neventTime: null\nfirstTimestamp: \"2021-12-28T19:31:13Z\"\ninvolvedObject:\n  apiVersion: apps/v1\n  kind: ReplicaSet\n  name: redis-687967dbc5\n  namespace: moelove\n  resourceVersion: \"330227\"\n  uid: 11e98a9d-9062-4ccb-92cb-f51cc74d4c1d\nkind: Event\nlastTimestamp: \"2021-12-28T19:31:13Z\"\nmessage: 'Created pod: redis-687967dbc5-27vmr'\nmetadata:\n  creationTimestamp: \"2021-12-28T19:31:13Z\"\n  name: redis-687967dbc5.16c4fb7bde6b54c4\n  namespace: moelove\n  resourceVersion: \"330231\"\n  uid: 8e37ec1e-b3a1-420c-96d4-3b3b2995c300\nreason: SuccessfulCreate\nreportingComponent: \"\"\nreportingInstance: \"\"\nsource:\n  component: replicaset-controller\ntype: Normal\n其中主要字段的含义如下：\n\ncount: 表示当前同类的事件发生了多少次 （前面已经介绍）\ninvolvedObject: 与此 event 有直接关联的资源对象 （前面已经介绍） , 结构如下：\ntype ObjectReference struct {\n\tKind string\n\tNamespace string\n\tName string\n\tUID types.UID\n\tAPIVersion string\n\tResourceVersion string\n\tFieldPath string\n}\nsource: 直接关联的组件, 结构如下：\ntype EventSource struct {\n\tComponent string\n\tHost string\n}\nreason: 简单的总结（或者一个固定的代码），比较适合用于做筛选条件，主要是为了让机器可读，当前有超过 50 种这样的代码；\nmessage: 给一个更易让人读懂的详细说明\ntype: 当前只有 Normal 和 Warning 两种类型, 源码中也分别写了其含义：\n// staging/src/k8s.io/api/core/v1/types.go\nconst (\n\t// Information only and will not cause any problems\n\tEventTypeNormal string = \"Normal\"\n\t// These events are to warn that something might go wrong\n\tEventTypeWarning string = \"Warning\"\n)\n所以，当我们将这些 Events 都作为 tracing 的 span 采集回来后，就可以按照其 source 进行分类，按 involvedObject 进行关联，按时间进行排序了。\n\n总结\n在这篇文章中，我主要通过两个示例，一个正确部署的 Deploy，以及一个使用不存在镜像部署的 Deploy，深入的介绍了 Events 对象的实际的作用及其各个字段的含义。\n\n对于 Kubernetes 而言，Events 中包含了很多有用的信息，但是这些信息却并不会对 Kubernetes 造成什么影响，它们也并不是实际的 Kubernetes 的日志。默认情况下 Kubernetes 中的日志在 1 小时后就会被清理掉，以便释放对 etcd 的资源占用。\n\n所以为了能更好的让集群管理员知道发生了什么，在生产环境中，我们通常会把 Kubernetes 集群的 events 也给采集回来。我个人比较推荐的工具是： https://github.com/opsgenie/kubernetes-event-exporter\n\n当然你也可以按照我之前的文章 《更优雅的 Kubernetes 集群事件度量方案》，利用 Jaeger 利用 tracing 的方式来采集 Kubernetes 集群中的 events 并进行展示。\n\n"
	enhanceReqs := make([]*EnhanceReq, 0)
	chunks := []*pb.Chunk{{Content: text1}, {Content: text3}}
	enhanceReqs = append(enhanceReqs, &EnhanceReq{
		Chunks: chunks,
		Num:    3,
		Modes:  []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY, pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
	})
	enhanceReqs = append(enhanceReqs, &EnhanceReq{
		Chunks: chunks,
		Num:    3,
		Modes:  []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY, pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
	})
	if err := EnhanceKnowledgeChunks(ctx, TestModel, 0, enhanceReqs...); err != nil {
		stdlog.Info(err)
	}

	for _, chunk := range chunks {
		for _, augmentedChunk := range chunk.AugmentedChunks {
			fmt.Println(augmentedChunk.Content)
		}
	}
}
