package clients

import (
	"context"
	"io"

	openai "github.com/sashabaranov/go-openai"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// OpenaiChatResponseStream 定义流式响应的结构
type OpenaiChatResponseStream struct {
	Content   string            `json:"content,omitempty"`
	Error     error             `json:"error,omitempty"`
	ToolCalls []openai.ToolCall `json:"tool_calls,omitempty"`
}

type OpenaiClient struct {
	client *openai.Client
}

func NewOpenaiClient(baseURL, apiKey string) *OpenaiClient {
	config := openai.DefaultConfig(apiKey)
	config.BaseURL = baseURL

	return &OpenaiClient{
		client: openai.NewClientWithConfig(config),
	}
}

// ChatStream 实现流式对话功能
func (c *OpenaiClient) ChatWithToolsStream(
	ctx context.Context,
	req openai.ChatCompletionRequest,
) <-chan OpenaiChatResponseStream {
	responseChan := make(chan OpenaiChatResponseStream)
	req.Stream = true
	go func() {
		defer close(responseChan)
		stream, err := c.client.CreateChatCompletionStream(ctx, req)
		if err != nil {
			stdlog.Errorf("创建流式对话失败: %v", err)
			responseChan <- OpenaiChatResponseStream{
				Error: err,
			}
			return
		}
		defer stream.Close()
		// 使用map存储工具调用，以index的字符串形式为键
		toolCallsMap := make(map[int]openai.ToolCall)
		for {
			response, err := stream.Recv()
			if err != nil {
				if err == io.EOF {
					// 如果有工具调用，发送完整的工具调用信息
					if len(toolCallsMap) > 0 {
						// 将map转换为slice
						finalToolCalls := make([]openai.ToolCall, 0, len(toolCallsMap))
						for _, toolCall := range toolCallsMap {
							finalToolCalls = append(finalToolCalls, toolCall)
						}
						responseChan <- OpenaiChatResponseStream{
							ToolCalls: finalToolCalls,
						}
					}
					return
				}

				stdlog.Errorf("ChatWithToolsStream received error: %v", err)
				responseChan <- OpenaiChatResponseStream{
					Error: err,
				}
				return
			}
			choices := response.Choices
			if len(choices) == 0 {
				stdlog.Warnf("ChatWithToolsStream received empty choices")
				continue
			}
			delta := choices[0].Delta
			// 处理内容
			if delta.Content != "" {
				stdlog.Debugf("ChatWithToolsStream received content: %s", delta.Content)
				responseChan <- OpenaiChatResponseStream{
					Content: delta.Content,
				}
			}

			// 处理工具调用
			if len(delta.ToolCalls) > 0 {
				stdlog.Debugf("ChatWithToolsStream received tool_calls: %+v", delta.ToolCalls)
				c.accumulateToolCallsDelta(
					delta.ToolCalls,
					toolCallsMap,
				)
			}
		}
	}()

	return responseChan
}

// accumulateToolCallsDelta 累加工具调用流式响应数据
// 工具调用流式响应数据示例如下:
//
//	[{"index": 0, "id": "call_DdmO9pD3xa9XTPNJ32zg2hcA", "function": {"arguments": "", "name": "get_weather"}, "type": "function"}]
//	[{"index": 0, "id": null, "function": {"arguments": "{\"", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": "location", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": "\":\"", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": "Paris", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": ",", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": " France", "name": null}, "type": null}]
//	[{"index": 0, "id": null, "function": {"arguments": "\"}", "name": null}, "type": null}]
//
// 第一个消息包含ID和函数名, 后续消息流式返回函数的输入参数，详情可参考openai的官方文档  https://platform.openai.com/docs/guides/function-calling
func (c *OpenaiClient) accumulateToolCallsDelta(toolCallsDelta []openai.ToolCall, toolCallsMap map[int]openai.ToolCall) {
	for _, delta := range toolCallsDelta {
		// 确保Index指针不为空
		if delta.Index == nil {
			stdlog.Warnf("received toolCall without Index: %+v", delta)
			continue
		}

		indexValue := *delta.Index
		if _, exists := toolCallsMap[indexValue]; !exists {
			// 接收到工具调用的第一个消息，保存Index、ID、Type和Function
			toolCallsMap[indexValue] = openai.ToolCall{
				Index: delta.Index,
				ID:    delta.ID,
				Type:  delta.Type,
				Function: openai.FunctionCall{
					Name:      delta.Function.Name,
					Arguments: delta.Function.Arguments,
				},
			}
		} else {
			// 接收到工具调用的后续消息，累积函数的输入参数
			current := toolCallsMap[indexValue]
			current.Function.Arguments += delta.Function.Arguments
			toolCallsMap[indexValue] = current
		}
	}
}

// ChatWithToolsBlock 实现非流式工具调用对话功能
func (c *OpenaiClient) ChatWithToolsBlock(
	ctx context.Context,
	req openai.ChatCompletionRequest,
) (string, []openai.ToolCall, error) {
	// 确保不使用流式模式
	req.Stream = false

	// 调用API
	resp, err := c.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return "", nil, stderr.Errorf("非流式工具调用对话失败: %v", err)
	}

	// 检查响应
	if len(resp.Choices) == 0 {
		return "", nil, stderr.Errorf("ChatWithToolsBlock received empty choices")
	}

	// 提取内容和工具调用
	choice := resp.Choices[0]
	content := choice.Message.Content
	toolCalls := choice.Message.ToolCalls
	if content != "" {
		stdlog.Debugf("ChatWithToolsBlock received content: %s", content)
	}
	if len(toolCalls) > 0 {
		stdlog.Debugf("ChatWithToolsBlock received tool_calls: %+v", toolCalls)
	}
	return content, toolCalls, nil
}
