package clients

import (
	"context"
	"fmt"
	"sort"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

const (
	DefaultModelName       = "atom"
	DefaultTopK            = 10
	RerankContentMaxLength = 500 //重排序文本的最大长度

)

func RerankChunksAndFilter(ctx context.Context, query string, params *pb.RerankParams,
	chunks []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	ret, err := RerankChunks(ctx, params.Model, query, chunks)
	if err != nil {
		return nil, stderr.Wrap(err, "rerank chunks")
	}
	ret, err = FilterRerankResult(ret, params)
	if err != nil {
		return nil, stderr.Wrap(err, "filter reranked chunks")
	}
	return ret, nil
}

func RerankChunks(ctx context.Context, model *pb.ModelService, query string, chunks []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	if len(chunks) == 0 {
		return []*pb.ChunkRetrieveResult{}, nil
	}
	texts := make([]string, 0, len(chunks))
	// replaceMp := make(map[string]string)
	for _, c := range chunks {
		t := getRerankContent(c)
		// 	// 截断文本，避免长度超过重排序模型的限制
		// 	rs := []rune(t)
		// 	if len(rs) > RerankContentMaxLength {
		// 		rs = rs[:RerankContentMaxLength]
		// 		t = string(rs)
		// 		replaceMp[t] = c.Chunk.Content
		// 	}
		texts = append(texts, t)
	}

	ordered_texts, scores, err := RerankTexts(ctx, model, query, texts)
	if err != nil {
		return nil, stderr.Wrap(err, "rerank texts")
	}
	// content -> [order_idx, ...]
	rank := map[string][]int{}
	for i, text := range ordered_texts {
		// if raw, ok := replaceMp[content]; ok {
		// 	content = raw
		// }
		rank[text] = append(rank[text], i)
	}
	ret := make([]*pb.ChunkRetrieveResult, len(ordered_texts))
	for _, c := range chunks {
		t := getRerankContent(c)
		if len(rank[t]) == 0 {
			return nil, fmt.Errorf("chunk content not found in rerank result, content: %s", c.Chunk.Content)
		}
		// pop order
		i := rank[t][0]
		rank[t] = rank[t][1:]
		// set result in place
		ret[i] = c
		// using rerank score
		c.Score = scores[i]
	}
	return ret, nil
}

func getRerankContent(c *pb.ChunkRetrieveResult) string {
	t := c.Chunk.Content
	// 如果是图片chunk，将增强的描述文本作为rerank文本
	if c.Chunk.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE {
		for _, ac := range c.Chunk.AugmentedChunks {
			if ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION || ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_SUMMARY {
				t = ac.Content
				break
			}
		}
	}
	return t
}

func RerankTexts(ctx context.Context, model *pb.ModelService, query string, texts []string) ([]string, []float32, error) {
	req := &triton.RerankReq{
		Query: query,
		Texts: texts,
	}
	rsp, err := Rerank(ctx, model, req)
	if err != nil {
		return nil, nil, err
	}
	if len(rsp.Texts) != len(texts) {
		return nil, nil, fmt.Errorf("length of the rerank result is not equal to the input, %d != %d", len(rsp.Texts), len(texts))
	}
	return rsp.Texts, rsp.Scores, nil
}

func FilterRerankResult(chunks []*pb.ChunkRetrieveResult, params *pb.RerankParams) ([]*pb.ChunkRetrieveResult, error) {
	sz := len(chunks)
	topK := int(params.TopK)
	if topK <= 0 {
		topK = DefaultTopK
	}
	if topK < sz {
		sz = topK
	}

	chunks = chunks[:sz]
	if params.RetainUnqualified {
		return chunks, nil
	}

	threshold := params.ScoreThreshold
	idx := sort.Search(len(chunks), func(i int) bool { return chunks[i].Score < threshold })
	return chunks[:idx], nil
}
