package clients

import (
	"context"
	"net/http"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func GetModelService() (*pb.ReadModelServiceRsp, context.Context, error) {
	ctx := helper.NewContextWithBaseInfo(context.Background(), "llmops-assets-997", "assets",
		"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ")
	callConfig := &CallConfig{
		Method: http.MethodGet,
		FullInferUrl: "https://**************:30443/llm/llmops/tenants/llmops-assets/gateway/mw/api/v1/mwh/svcmgr/services?" +
			"with_remote_service=true&only_running=true&with_asset=false&is_mw_mode=true" +
			"&is_seldon_mode=true&project_id=assets&tenantId=dev-assets",
		Headers:  map[string]string{"Cookie": "SOPHONID=1ec99d3c2ccfa531fe5eb99ebe0bfcae"},
		InferReq: &triton.RerankReq{},
	}
	modelServices := new(pb.ReadModelServiceRsp)
	err := new(HttpCallHelper).SyncCall(ctx, callConfig, func(result string) error {
		return stdsrv.UnmarshalMixWithProto(result, modelServices)
	})
	return modelServices, ctx, err
}

func TestSideCarClient(t *testing.T) {
	modelServices, ctx, err := GetModelService()
	if err != nil {
		stdlog.Info("获取模型失败")
		return
	} else {
		stdlog.Info("获取模型成功")
	}

	for _, model := range modelServices.Services {
		inferErr := CheckModelHealth(ctx, model)
		stdlog.Infof("the model name is %s, id is %s", model.Name, model.Id)
		stdlog.Errorf("the error is %v", inferErr)
	}
	stdlog.Info("test model success")
}
