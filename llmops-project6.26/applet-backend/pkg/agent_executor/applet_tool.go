package agent_executor

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

type AppletServiceExecutor struct {
	ac *clients.AppletSvcClient
}

func (a *AppletServiceExecutor) CheckHealth(tool agent_definition.ToolDescriber) (health.ServiceHealth, error) {
	return health.ServiceHealth{
		ID:      tool.Definition().ID,
		Name:    "",
		Healthy: true,
		Detail:  "",
	}, nil
}
func (a *AppletServiceExecutor) Type() agent_definition.ToolType {
	return agent_definition.ToolTypeAppletService
}

func (a *AppletServiceExecutor) Execute(ctx context.Context, tool agent_definition.ToolDescriber, input any) (string, error) {
	// 1、准备请求参数
	toolDescriber, ok := tool.(*agent_definition.AppletServiceToolDescriber)
	if !ok {
		return "", helper.ToolExecuteErr.Error("tool type is not AppletServiceToolDescriber")
	}
	inputMap, ok := input.(map[string]any)
	if !ok {
		return "", helper.ToolExecuteErr.Error("input type is not map[string]any")
	}
	params := make(map[string]interface{})
	for _, param := range toolDescriber.Params {
		name := param.Name
		value, ok := inputMap[name]
		if ok {
			params[name] = value
		} else {
			params[name] = param.DefaultValue
		}
	}

	return a.ExecuteWithParams(ctx, toolDescriber.MlOpsSvcID, params)
}

func (a *AppletServiceExecutor) ExecuteWithParams(ctx context.Context, svcID string, params map[string]any) (string, error) {
	rsp, err := a.ac.CallAppletSvc(ctx, svcID, params)
	if err != nil {
		return "", stderr.Trace(err)
	}
	if rsp.Error != nil {
		return "", stderr.Trace(err)
	}
	return string(rsp.Body), nil
}

func (a *AppletServiceExecutor) ExecuteQuestionClassify(ctx context.Context, serviceID string, req *engine.QuestionClassifyReq) (*engine.QuestionClassifyResp, error) {
	return a.ac.CallQuestionClassifySvc(ctx, serviceID, req)
}
