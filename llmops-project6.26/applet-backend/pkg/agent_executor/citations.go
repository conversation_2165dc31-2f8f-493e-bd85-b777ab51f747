package agent_executor

import (
	"fmt"
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

type CitationType string

const (
	CitationTypeTextKnowledge  CitationType = "text_knowledge"
	CitationTypeInternetSearch CitationType = "internet_search"
)

type TextKnowledgeDetails struct {
	KnowledgeBaseId   string `json:"knowledge_base_id"`
	KnowledgeBaseName string `json:"knowledge_base_name"`
	DocId             string `json:"doc_id"`
	DocName           string `json:"doc_name"`
	ChunkId           string `json:"chunk_id"`
	ContentType       string `json:"content_type"`
	AboveText         string `json:"above_text"`
	LaterText         string `json:"later_text"`
}

type InternetSearchDetails struct {
	Title   string `json:"title"`
	Snippet string `json:"snippet"`
	Url     string `json:"url"`
}

type Citation struct {
	CitationType         CitationType           `json:"citation_type"`
	Content              string                 `json:"content"`
	TextKnowledgeDetails *TextKnowledgeDetails  `json:"text_knowledge_details,omitempty"`
	InternetSearchDetail *InternetSearchDetails `json:"internet_search_details,omitempty"`
}

type CitationsResponse struct {
	Code      int         `json:"code"`
	Citations []*Citation `json:"citations"`
	Success   bool        `json:"success"`
	Msg       string      `json:"msg"`
}

func (r *CitationsResponse) Validate() bool {
	if len(r.Citations) == 0 {
		return false
	}

	validCitations := make([]*Citation, 0, len(r.Citations))
	for _, citation := range r.Citations {
		if citation == nil || citation.Content == "" {
			continue
		}
		// 校验并补充CitationType相关字段
		switch citation.CitationType {
		case CitationTypeTextKnowledge:
			if citation.TextKnowledgeDetails == nil {
				citation.TextKnowledgeDetails = &TextKnowledgeDetails{}
			}
			if citation.TextKnowledgeDetails.KnowledgeBaseName == "" {
				citation.TextKnowledgeDetails.KnowledgeBaseName = "未知知识库"
			}
			if citation.TextKnowledgeDetails.DocName == "" {
				citation.TextKnowledgeDetails.DocName = "未知文档"
			}
			validCitations = append(validCitations, citation)

		case CitationTypeInternetSearch:
			if citation.InternetSearchDetail == nil {
				citation.InternetSearchDetail = &InternetSearchDetails{}
			}
			if citation.InternetSearchDetail.Title == "" {
				citation.InternetSearchDetail.Title = "未知网站"
			}
			validCitations = append(validCitations, citation)

		default:
			// 忽略未知的引用类型
			continue
		}
	}

	r.Citations = validCitations
	return len(validCitations) > 0
}

type InternetSearchRequest struct {
	Query string `json:"query"`
}

type InternetSearchResponse CitationsResponse

func TryCvt2Citations(v any, citationType CitationType) (citations []*Citation, err error) {
	if v == nil {
		return make([]*Citation, 0), nil
	}
	switch vv := v.(type) {
	// 字符串值需要额外设置引用类型
	case string:
		citations = append(citations, &Citation{CitationType: citationType, Content: vv})
	case []string:
		for _, contentV := range vv {
			citations = append(citations, &Citation{CitationType: CitationTypeTextKnowledge, Content: contentV})
		}
	case *pb.Chunk:
		citations = append(citations, cvtChunk2Citation(vv))
	case []*pb.Chunk:
		for _, chunk := range vv {
			citations = append(citations, cvtChunk2Citation(chunk))
		}
	case *Citation:
		citations = append(citations, vv)
	case []*Citation:
		citations = append(citations, vv...)
	case []any:
		if err = stdsrv.UnmarshalMixWithProto(vv, &citations); err != nil {
			return nil, stderr.Wrap(err, "UnmarshalMixWithProto []any to []*engine.Citation")
		}
	default:
		return nil, stderr.InvalidParam.Errorf("unexpected output type %T, expecting (string, []string, *engine.Citation, []*engine.Citation, []any, []*pb.Chunk)", v)
	}
	return
}

const (
	ImageContentTemplate = "图片链接: %s\n图片描述: %s"
	EmptyImageDesc       = "无"
)

func cvtChunk2Citation(chunk *pb.Chunk) *Citation {
	knowledgeBaseId := chunk.Extra[FieldKnowledgeBaseId]
	knowledgeBaseName := chunk.Extra[FieldKnowledgeBaseName]
	docId := chunk.Extra[FieldDocId]
	docName := chunk.Extra[FieldDocName]
	content := chunk.Content
	// 当内容类型为图片链接时，将图片的描述内容也添加到 citation 的 content 中
	if chunk.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE {
		imageDescs := ""
		for _, augmentedChunk := range chunk.AugmentedChunks {
			if augmentedChunk.Content != "" {
				imageDescs += augmentedChunk.Content + "\n"
			}
		}
		if imageDescs == "" {
			imageDescs = EmptyImageDesc
		}
		content = fmt.Sprintf(ImageContentTemplate, chunk.Content, imageDescs)
	}
	// 将 Chunk 的上下文 Chunks 信息存在 Citation 中，用于转换成 Oberservation，给LLM用
	return &Citation{
		CitationType: CitationTypeTextKnowledge,
		Content:      content,
		TextKnowledgeDetails: &TextKnowledgeDetails{
			KnowledgeBaseId:   knowledgeBaseId,
			KnowledgeBaseName: knowledgeBaseName,
			DocId:             docId,
			DocName:           docName,
			ChunkId:           chunk.Id,
			ContentType:       strings.ToLower(chunk.ContentType.String()),
			AboveText:         chunk.Extra[FieldAboveText],
			LaterText:         chunk.Extra[FieldLaterText],
		},
	}
}

const (
	CitationSeparator              = "----------\n\n"
	TextKnowledgeTextMetaTemplate  = "上文（Previous Context）:\n%s\n\n内容（Main Content）:\n%s\n\n下文（Following Context）:\n%s\n\n来源:知识库\n知识库名:%s\n文档名:%s"
	InternetSearchTextMetaTemplate = "%s\n\n来源:互联网\n网页标题:%s\n链接:%s"
)

func CvtCitations2CitationTexts(citations []*Citation) string {
	var citationContents []string
	for _, citation := range citations {
		// 跳过空引用
		if citation == nil {
			stdlog.Warnf("Skipping nil citation")
			continue
		}

		var formattedContent string
		switch citation.CitationType {
		case CitationTypeTextKnowledge:
			// 处理知识库引用：如果没有详细信息，直接使用原始内容
			if citation.TextKnowledgeDetails == nil {
				formattedContent = citation.Content
			} else {
				// 格式化知识库引用，包含知识库名称和文档名称
				formattedContent = fmt.Sprintf(TextKnowledgeTextMetaTemplate,
					citation.TextKnowledgeDetails.AboveText,
					citation.Content,
					citation.TextKnowledgeDetails.LaterText,
					citation.TextKnowledgeDetails.KnowledgeBaseName,
					citation.TextKnowledgeDetails.DocName)
			}
		case CitationTypeInternetSearch:
			// 处理互联网搜索引用：如果没有详细信息，直接使用原始内容
			if citation.InternetSearchDetail == nil {
				formattedContent = citation.Content
			} else {
				// 格式化互联网引用，包含网页标题和URL
				formattedContent = fmt.Sprintf(InternetSearchTextMetaTemplate,
					citation.Content,
					citation.InternetSearchDetail.Title,
					citation.InternetSearchDetail.Url)
			}
		default:
			// 处理其他类型的引用：直接使用原始内容
			formattedContent = citation.Content
		}
		citationContents = append(citationContents, formattedContent)
	}
	// 使用分隔符连接所有引用内容
	citationTexts := strings.Join(citationContents, CitationSeparator)
	return citationTexts
}
