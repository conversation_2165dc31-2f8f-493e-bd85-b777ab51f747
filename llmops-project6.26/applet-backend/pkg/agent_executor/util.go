package agent_executor

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/mcp-go/client/transport"

	mcpClient "transwarp.io/applied-ai/mcp-go/client"
	"transwarp.io/applied-ai/mcp-go/mcp"
)

var httpCli = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	},
}

type httpParam struct {
	Method     string
	Url        string
	ReqBody    string
	Header     map[string]string
	QueryParam map[string]string
	Proxy      *agent_definition.ProxyInfo
}

func HttpCallString(ctx context.Context, param *httpParam) (string, error) {

	url, err := url.Parse(param.Url)
	if err != nil {
		return "", err
	}
	q := url.Query()
	// 添加 query参数
	for k, v := range param.QueryParam {
		q.Set(k, v)
	}
	var reqBody io.Reader
	if param.ReqBody != "" {
		reqBody = bytes.NewBufferString(param.ReqBody)
	}
	fullPath := fmt.Sprintf("%s?%s", url.String(), q.Encode())
	req, err := http.NewRequestWithContext(ctx, param.Method, fullPath, reqBody)
	if err != nil {
		return "", err
	}
	// 默认application/json
	req.Header.Add("Content-Type", "application/json")
	for k, v := range param.Header {
		req.Header.Add(k, v)
	}
	if param.Proxy != nil {
		transport, err := clients2.HttpTransport(param.Proxy)
		if err != nil {
			return "", err
		}
		httpCli.Transport = transport
	}
	// 发送请求并获取响应
	resp, err := httpCli.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(body))
	}
	// json编码再解码，解决中文问题
	var res any
	if err := json.Unmarshal(body, &res); err != nil {
		return "", err
	}
	bytes, err := json.Marshal(res)
	if err != nil {
		return "", err
	}
	return string(bytes), nil

}

type mcpParam struct {
	ServerType       agent_definition.ServerType
	McpType          agent_definition.McpType
	BaseUrl          string
	ToolName         string
	CollectionParams map[string]any
	Params           map[string]any
	Proxy            *agent_definition.ProxyInfo
}

func mcpCallString(ctx context.Context, param *mcpParam) (string, error) {
	// 开始处理参数
	collectionParams := make(map[string]string)
	requestParams := make(map[string]interface{})

	for k, v := range param.CollectionParams {
		collectionParams[k] = v.(string)
	}

	for k, v := range param.Params {
		requestParams[k] = v
	}

	// 创建client
	var client *mcpClient.Client
	var err error
	if param.ServerType == agent_definition.ServerTypeMCP && !strings.Contains(param.BaseUrl, "http://autocv-applet-service") {
		if param.Proxy != nil && param.Proxy.Url != "" {
			transport, err := clients2.HttpTransport(param.Proxy)
			if err != nil {
				return "", err
			}
			httpCli.Transport = transport
		}
		if param.McpType == agent_definition.McpTypeSse {
			client, err = mcpClient.NewSSEMCPClient(param.BaseUrl, mcpClient.WithHTTPClient(httpCli), mcpClient.WithHeaders(collectionParams))
		} else {
			client, err = mcpClient.NewStreamableHttpClient(param.BaseUrl, transport.WithStreamableHTTPClient(httpCli), transport.WithHTTPHeaders(collectionParams))
		}
		if err != nil {
			return "", fmt.Errorf("failed to create MCP client: %v", err)
		}
		defer client.Close()
	} else {
		// 创建 Streamable MCP client
		if !strings.Contains(param.BaseUrl, "project_id") {
			projectId := helper.GetProjectID(ctx)
			param.BaseUrl = param.BaseUrl + "?project_id=" + projectId
		}
		client, err = mcpClient.NewStreamableHttpClient(param.BaseUrl, transport.WithStreamableHTTPClient(httpCli),
			transport.WithHTTPHeaderFunc(func(mcpCtx context.Context) map[string]string {
				token, err := helper.GetToken(ctx)
				if err != nil {
					return collectionParams
				}
				headers := make(map[string]string)
				headers["Authorization"] = token
				for k, v := range collectionParams {
					headers[k] = v
				}
				return headers
			}))
		if err != nil {
			return "", fmt.Errorf("failed to create Dynamic MCP client: %v", err)
		}
		defer client.Close()
	}

	if err := client.Start(ctx); err != nil {
		return "", fmt.Errorf("failed to start client，please make sure that the server is running and use proxy for the server on public network: %v", err.Error())
	}
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name: "call tool client",
	}

	if _, err := client.Initialize(ctx, initRequest); err != nil {
		return "", fmt.Errorf("failed to initialize MCP client: %v", err)
	}
	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = param.ToolName
	callRequest.Params.Arguments = requestParams

	callResponse, err := client.CallTool(ctx, callRequest)
	if err != nil {
		return "", fmt.Errorf("failed to call tool '%s': %v", param.ToolName, err)
	}

	// 5. 将返回值格式化为 JSON 字符串
	responseJSON, err := json.MarshalIndent(callResponse, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal tool response: %v", err)
	}
	return string(responseJSON), nil
}
