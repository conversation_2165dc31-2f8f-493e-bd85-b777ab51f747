package agent_executor

/*
import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

func TestExecuteAPI(t *testing.T) {
	clients.InitHttpCliForTest()
	ctx := context.Background()
	executor := ApiToolExecutor{}
	describer := &agent_definition.APIToolDescriber{
		ID:      "",
		BaseURL: "https://httpbin.org",
		Method:  "POST",
		CollectionHeaders: map[string]string{
			"Authorization": "TestAuthorization",
		},
		APIPath: "anything",
		Name:    "测试股API",
		Desc:    "测试描述",
		Params: []agent_definition.APIToolParam{
			{
				Name:           "test1",
				Desc:           "测试参数1",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test2",
				Desc:           "测试参数2",
				ParamValueType: "object",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test3",
				Desc:           "测试参数3",
				ParamValueType: "int",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test4",
				Desc:           "测试参数4",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeQuery,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test5",
				Desc:           "测试参数5",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeHeader,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
		},
	}
	input := make(map[string]any)
	input["test1"] = "test string"
	input["test2"] = struct {
		ID string
	}{ID: "中文测试"}
	input["test3"] = 3
	input["test4"] = 88
	input["test5"] = "test header"
	res, err := executor.Execute(ctx, describer, input)
	if err != nil {
		t.Fatal(err)
	}
	stdlog.Infof("execute res :%v", res)
	resMap := make(map[string]any)
	if err := json.Unmarshal([]byte(res), &resMap); err != nil {
		t.Fatal(err)
	}
	r, ok := resMap["data"]
	if !ok {
		t.Fatal("no data")
	}
	if r.(string) != "{\"test1\":\"test string\",\"test2\":{\"ID\":\"中文测试\"},\"test3\":3}" {
		t.Fatal(fmt.Sprintf("res data  err"))
	}

}

func TestExecuteAPIError(t *testing.T) {
	clients.InitHttpCliForTest()
	ctx := context.Background()
	executor := ApiToolExecutor{}
	describer := &agent_definition.APIToolDescriber{
		ID:      "",
		BaseURL: "https://httpbin.org",
		Method:  "POST",
		CollectionHeaders: map[string]string{
			"Authorization": "TestAuthorization",
		},
		APIPath: "anything",
		Name:    "测试股API",
		Desc:    "测试描述",
		Params: []agent_definition.APIToolParam{
			{
				Name:           "test1",
				Desc:           "测试参数1",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test2",
				Desc:           "测试参数2",
				ParamValueType: "object",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test3",
				Desc:           "测试参数3",
				ParamValueType: "int",
				ParamType:      agent_definition.APIToolParamTypeBody,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test4",
				Desc:           "测试参数4",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeQuery,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
			{
				Name:           "test5",
				Desc:           "测试参数5",
				ParamValueType: "string",
				ParamType:      agent_definition.APIToolParamTypeHeader,
				Required:       true,
				DefaultValue:   nil,
				ModelIgnore:    false,
			},
		},
	}
	input := make(map[string]any)
	input["test1"] = "test string"
	input["test2"] = struct {
		ID string
	}{ID: "test struct"}
	input["test3"] = 3
	input["test4"] = 88
	_, err := executor.Execute(ctx, describer, input)
	if err == nil {
		t.Fatal("err should not be nil")
	}
	if !strings.Contains(err.Error(), "required failed :test5 no value") {
		t.Fatalf("err :%v invalid", err.Error())
	}
}

*/
