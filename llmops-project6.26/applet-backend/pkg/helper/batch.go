package helper

import (
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// AsyncBatchCall 使用给定的处理函数处理传入的所有ID, 若在其中发生错误,则仅打印日志
// 函数返回时, 所有调用协程并未完成
func AsyncBatchCall(ids []string, call func(string) error) {
	for _, id := range ids {
		go func(id string) {
			err := call(id)
			if err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%s'", id)
			}
		}(id)
	}
}

// SyncBatchCall 使用给定的处理函数处理传入的所有ID, 若在其中发生错误,则直接返回
// 当所有调用协程完成后,该函数才会返回
func SyncBatchCall(ids []string, call func(string) error) error {
	wg := sync.WaitGroup{}
	wg.Add(len(ids))
	errs := make([]error, 0, len(ids))
	for _, id := range ids {
		go func(id string) {
			defer wg.Done()
			if err := call(id); err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%s'", id)
				errs = append(errs, err)
			}
		}(id)
	}
	// 等待所有调用协程执行完毕
	wg.Wait()
	return stderr.JoinErrors(errs...)
}
