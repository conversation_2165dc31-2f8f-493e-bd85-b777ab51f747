package helper

import (
	"fmt"
	"github.com/thoas/go-funk"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DefaultGoRoutineNums = 20
)

func GoFunWithRecover(fun func()) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				stdlog.Errorf("recover: panic happened :%v", err)
			}
		}()
		fun()
	}()
}

// SyncRunTasks
// 每个task需要处理好参数输入和结果处理
// error 代表是否所有任务都执行成功
func SyncRunTasks(allTasks []func() error) error {
	batches, ok := funk.Chunk(allTasks, DefaultGoRoutineNums).([][]func() error)
	if !ok {
		return stderr.Errorf("failed while split allTasks to batches")
	}
	// 按批次去处理任务
	errs := make([]error, 0)
	for _, batch := range batches {
		w := sync.WaitGroup{}
		w.Add(len(batch))
		var mutex sync.Mutex
		for _, task := range batch {
			// task是循环时的共享变量,类似于for循环中的i
			// go func 启动协程去执行时,访问task的时间不确定，可能延后，
			// 导致访问的task一直是最后一个,因此需要先将task拷贝一次
			localTask := task
			go func() {
				defer func() {
					if r := recover(); r != nil {
						mutex.Lock()
						defer mutex.Unlock()
						errs = append(errs, fmt.Errorf("panic happened: %v", r))
					}
					w.Done()
				}()
				err := localTask()
				if err != nil {
					mutex.Lock()
					defer mutex.Unlock()
					errs = append(errs, err)
				}
			}()
		}
		w.Wait()
	}
	var err error
	if len(errs) != 0 {
		err = stderr.JoinErrors(errs...)
	}
	return err
}
