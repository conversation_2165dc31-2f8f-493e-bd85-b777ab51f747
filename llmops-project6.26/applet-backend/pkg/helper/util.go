package helper

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"github.com/emicklei/go-restful/v3"
	"github.com/jinzhu/copier"
	"github.com/manucorporat/sse"
	"io"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	TypeTimeZoneShanghai    = "Asia/Shanghai"
	QueryParameterDebug     = "debug"
	QueryParameterTrace     = "trace"
	QueryParameterThought   = "thought"
	QueryParameterPretty    = "pretty"
	QueryParameterProjectID = "project_id"
	QueryParameterStack     = "stack"
	QueryParameterTrue      = "true"
	ContentTypeHeader       = "Content-Type"
)

func CopyTheSameFields(fromValuePtr any, toValuePtr any) error {
	if err := copier.Copy(toValuePtr, fromValuePtr); err != nil {
		return err
	}
	return nil
}
func String2Struct(fromValue string, toValuePtr any) error {
	if fromValue == "" {
		// 空串,不做处理
		return nil
	}
	if err := json.Unmarshal([]byte(fromValue), toValuePtr); err != nil {
		return err
	}
	return nil
}

// Struct2String  传入指针为nil时返回空串
func Struct2String(structOrPtr any) string {
	fromValuePtr := stdsrv.GetPtrOfStruct(structOrPtr)
	rv := reflect.ValueOf(fromValuePtr)
	if rv.IsNil() {
		return ""
	}
	bytes, err := json.Marshal(fromValuePtr)
	if err != nil {
		return ""
	}
	return string(bytes)
}

// Time2UnixMilli 转为上海时区,毫秒数
func Time2UnixMilli(current time.Time) int64 {
	location, _ := time.LoadLocation(TypeTimeZoneShanghai)
	if location == nil {
		stdlog.Infof("failed to load location %s, replace with default", TypeTimeZoneShanghai)
		return current.UnixMilli()
	} else {
		return current.In(location).UnixMilli()
	}
}
func AnyToStruct(any any, anyPtr any) error {
	var err error
	bytes := make([]byte, 0)
	switch any.(type) {
	case string:
		bytes = []byte(any.(string))
	default:
		bytes, err = json.Marshal(any)
		if err != nil {
			return err
		}
	}
	if err = json.Unmarshal(bytes, anyPtr); err != nil {
		return err
	}
	return nil
}
func ReadFileToStruct(filePath string, structPtr any) error {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}
	if err = json.Unmarshal(bytes, structPtr); err != nil {
		return err
	}
	return nil
}
func DeepCopy[T any](src T) (T, error) {
	var dst T
	data, err := json.Marshal(src)
	if err != nil {
		return dst, err
	}
	err = json.Unmarshal(data, &dst)
	return dst, err
}

type SSEEventHandler func(event *sse.Event) error

func GetHttpReq(ctx context.Context, method, fullUrl string, params any, needToken bool) (*http.Request, error) {
	reqBytes, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, bytes.NewBuffer(reqBytes))
	if err != nil {
		return nil, err
	}
	req.Header.Add(ContentTypeHeader, restful.MIME_JSON)
	if needToken {
		token, err := GetToken(ctx)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to get user token")
		}
		req.Header.Set(auth.TokenHeader, token)
	}
	return req, nil
}

func HttpCallForStream(client *http.Client, req *http.Request, eventHandler stdsrv.SSEEventHandler) error {
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	reader := bufio.NewReader(resp.Body)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(reader)
		return stderr.Errorf("stream http call failed, with code[%d] and resp[%s]", resp.StatusCode, body)
	}
	return stdsrv.SSEReadEvent(req.Context(), reader, eventHandler)
}

func HttpCallForSync(client *http.Client, req *http.Request, stringHandler func(result string) error) error {
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return stderr.Errorf("sync http call failed, with code[%d] and resp[%s]", resp.StatusCode, body)
	}
	return stringHandler(string(body))
}

func GetEngineExecQueryParams(ctx context.Context, openDebug bool, openTrace bool) url.Values {
	queryParams := url.Values{}
	if openDebug {
		queryParams.Add(QueryParameterDebug, QueryParameterTrue)
	}
	if openTrace {
		queryParams.Add(QueryParameterTrace, QueryParameterTrue)
	}
	queryParams.Add(QueryParameterStack, QueryParameterTrue)
	queryParams.Add(QueryParameterPretty, QueryParameterTrue)
	queryParams.Add(QueryParameterThought, QueryParameterTrue)
	queryParams.Add(QueryParameterProjectID, GetProjectID(ctx))
	queryParams.Add(tenantIDParam, GetTenantID(ctx))
	return queryParams
}

// ReadFileContent 根据path读取文件内容，返回[]string

func StructToJson(data interface{}) string {
	jsonStr, err := json.Marshal(data)
	if err != nil {
		stdlog.Errorf("struct to json err :%v", err)
		return ""
	}
	return string(jsonStr)
}

// MixedToString 如果结构体含有proto结构,将对应的枚举解析为string
func MixedToString(structOrPtr any) string {
	if stdsrv.IsNilPointer(structOrPtr) {
		return ""
	}
	bytes, err := stdsrv.MarshalMixWithProto(structOrPtr)
	if err != nil {
		panic(err)
	}
	return string(bytes)
}

func CastString2Float64(value string) (float64, error) {
	result, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0, err
	}
	return result, nil
}
func CastString2Int(value string) (int, error) {
	result, err := strconv.Atoi(value)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func CastString2Int64(value string) (int64, error) {
	result, err := CastString2Int(value)
	if err != nil {
		return 0, err
	}
	return int64(result), nil
}

func CastStrSlice2Int64(values []string) ([]int64, error) {
	ret := make([]int64, 0)
	for _, v := range values {
		intValue, err := CastString2Int64(v)
		if err != nil {
			return nil, err
		}
		ret = append(ret, intValue)
	}
	return ret, nil
}

func CastString2strs(value string) ([]string, error) {
	res := strings.Split(value, ",")
	return res, nil
}

func CvtSlice2Map[T any](values []T, keyHandler func(value T) string) map[string]T {
	ret := make(map[string]T)
	for _, v := range values {
		key := keyHandler(v)
		ret[key] = v
	}
	return ret
}

// ExtractValues 获取map中的values
func ExtractValues[T any](mapStruct map[string]T) []T {
	ret := make([]T, 0)
	for _, v := range mapStruct {
		ret = append(ret, v)
	}
	return ret
}

// ExtractKeys 获取map中的key
func ExtractKeys[T comparable](mapStruct map[T]any) []T {
	ret := make([]T, 0)
	for k, _ := range mapStruct {
		ret = append(ret, k)
	}
	return ret
}

func StrsJoin[T ~string](elems []T, sep string) string {
	values := make([]string, 0)
	for _, e := range elems {
		values = append(values, string(e))
	}
	return strings.Join(values, sep)
}

// IsEmpty 是否为空串或只包含空格的字符串
func IsEmpty(str string) bool {
	return strings.TrimSpace(str) == ""
}

func GetLangPrompt(ctx context.Context) string {
	if IsChinese(ctx) {
		return `请使用中文回答`
	} else {
		return `Please answer in English`
	}
}

func IsStructJsonStr(str string) bool {
	if IsEmpty(str) {
		return false
	}
	str = strings.TrimSpace(str)
	var ret map[string]interface{}
	if err := json.Unmarshal([]byte(str), &ret); err != nil {
		return false
	}
	return true
}
