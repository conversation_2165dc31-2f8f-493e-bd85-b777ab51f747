package helper

import "transwarp.io/aip/llmops-common/pb"

// dynamic_datasource 动态数据源相关常量定义
const (
	LLMDataSource             = "LLMDatasource"
	VectorDataSource          = "VectorDatasource"
	DlieDataSource            = "DlieDatasource"
	KnowledgeDataSource       = "KnowledgeDataSource"
	ModelServiceDataSource    = "ModelServiceDataSource"
	TextEnhanceModeDataSource = "TextEnhanceModeDataSource"
	HippoDataSource           = "HippoDataSource"
	ScopeDataSource           = "ScopeDataSource"
	ElasticSearchDataSource   = "ElasticSearchDataSource"
	MilvusDataSource          = "MilvusDataSource"
	BaseDynamicDatasourceUrl  = "name,id@/applet/widgets/datasource"
	QueryParamKey             = "key"
	QueryParamSubKind         = "subKind"
)

const (
	UrlQueryNameIsPublishSelector = "is_published_selector"
	UrlQueryNameSceneTypeSelector = "scene_type_selector"
	UrlQueryValueTrue             = "true"
)

var (
	UrlQueryValueSceneTypeStandard = pb.KnowledgeBaseSceneType_SceneType_STANDARD.String()
)
