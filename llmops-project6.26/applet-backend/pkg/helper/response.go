package helper

import (
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/google/uuid"
	"github.com/manucorporat/sse"

	"github.com/emicklei/go-restful/v3"
	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/proto"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

var (
	marshaller   = &jsonpb.Marshaler{OrigName: true}
	unmarshaller = &jsonpb.Unmarshaler{}
)

// WriteFileResponse 将文件写入到Http的响应体附件中, 触发浏览器的下载事件
func WriteFileResponse(response *restful.Response, fileName string, src io.Reader) error {
	WriteFileHeader(response, fileName)
	if _, err := io.Copy(response.ResponseWriter, src); err != nil {
		return err
	}
	return nil
}

// WriteFileHeader 设置返回附件文件的响应头
func WriteFileHeader(response *restful.Response, fileName string) {
	response.Header().Add("Content-Type", "application/octet-stream")
	response.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename=%s`, url.QueryEscape(fileName)))
	response.Header().Add("File-Name", url.QueryEscape(fileName))
}

func SuccessResponse(response *restful.Response, value interface{}) {
	response.WriteHeaderAndEntity(http.StatusOK, value)
}

func SSEResponse(resp *restful.Response, event string, msg string) {
	if err := sse.Encode(resp, sse.Event{
		Id:    uuid.New().String(),
		Event: event,
		Data:  msg,
	}); err != nil {
		stdlog.Errorf("encode msg err :%v", err)
	}
	resp.Flush()
}

func ErrorSSEResponse(resp *restful.Response, e error) {
	stdlog.Errorf("something wrong, send error message to frontend :%v", e)
	errMsg := fmt.Sprintf("%s%s", "[error]", e.Error())
	SSEResponse(resp, "error", errMsg)
}

func ErrorSSEResponseV2(resp *restful.Response, e error) {
	resp.Header().Set("Content-Type", "text/event-stream")
	ErrorSSEResponse(resp, e)
}

func CustomSSEResponse(resp *restful.Response, event string, msg string) {
	if err := sse.Encode(resp, sse.Event{
		Id:    uuid.New().String(),
		Event: event,
		Data:  msg,
	}); err != nil {
		stdlog.Errorf("encode msg err :%v", err)
	}
	resp.Flush()
}

func ErrorResponse(response *restful.Response, e error) {
	httpCode, ge := BuildErrorResponse(e)
	if ge != nil {
		stdlog.WithError(ge).Errorf("Vision Internal Error")
	}
	response.WriteError(httpCode, ge)
}

func WritePbMessage(response *restful.Response, data proto.Message) {
	marshaller.Marshal(response, data)
}

func ReadPbMessage(request *restful.Request, data proto.Message) error {
	return unmarshaller.Unmarshal(request.Request.Body, data)
}

func BuildErrorResponse(e error) (int, *stderr.GatewayError) {
	se := stderr.Unwrap(e)
	return stderr.GetCode(se.Code).HttpCode, &stderr.GatewayError{
		Tag:      se.Tag,
		Code:     se.Code,
		Redirect: se.Redirect,
		Msg:      se.Message(),
		Detail:   se.Message() + "\n" + se.Stack(),
	}
}

func HandleErr(response *restful.Response, err error) {
	if err != nil {
		ErrorResponse(response, err)
		return
	}
}
