package helper

import (
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
)

func InterfaceToString(v interface{}) (string, error) {
	if stringValue, ok := v.(string); ok {
		return stringValue, nil
	} else {
		if bytes, err := json.Marshal(v); err != nil {
			return "", err
		} else {
			return string(bytes), nil
		}
	}
}

func StringToMap(str string) (map[string]string, error) {
	res := make(map[string]string, 0)
	if str == "" {
		return res, nil
	}
	if err := json.Unmarshal([]byte(str), &res); err != nil {
		return nil, err
	}
	return res, nil

}

func StringToInterface(str string, target any) error {
	if str == "" {
		return nil
	}
	if err := json.Unmarshal([]byte(str), &target); err != nil {
		return err
	}
	return nil
}

func StringToEnumSlice[T ~int32](str string, mp map[string]int32) []T {
	var ret []T
	for _, s := range strings.Split(str, ",") {
		if enumVal, ok := mp[s]; ok {
			ret = append(ret, T(enumVal))
		}
	}
	return ret
}

func StringToBoolSlice(str string) (ret []bool) {
	for _, s := range strings.Split(str, ",") {
		if strings.ToLower(s) == "true" {
			ret = append(ret, true)
		} else if strings.ToLower(s) == "false" {
			ret = append(ret, false)
		}
	}
	return
}

func ExtractCodeFromMarkdown(markdown string) string {
	// 定义正则表达式模式
	re := regexp.MustCompile("(?s)```.*?\\s(.*?)\\s```")

	// 查找匹配的子串
	matches := re.FindStringSubmatch(markdown)

	// 检查是否有匹配结果
	if len(matches) > 1 {
		// 返回提取的代码
		return matches[1]
	}

	return ""
}

func Vector2String(vec []float32) string {
	if len(vec) == 0 {
		return ""
	}
	builder := strings.Builder{}
	builder.Grow(len(vec) * 12) // 预分配足够的空间，12是一个预估值
	// 添加第一个元素
	builder.WriteString(strconv.FormatFloat(float64(vec[0]), 'f', -1, 32))
	// 添加剩余元素
	for _, v := range vec[1:] {
		builder.WriteByte(',')
		builder.WriteString(strconv.FormatFloat(float64(v), 'f', -1, 32))
	}
	return builder.String()
}

// String2MapValues 将字符串转换为map[string]*pb.ListValues
// key:v1,v2;key2:v1,v2
func String2MapValues(str string) map[string]*pb.ListValues {
	if str == "" {
		return nil
	}
	blocks := strings.Split(str, ";")
	res := make(map[string]*pb.ListValues, len(blocks))
	for _, block := range blocks {
		parts := strings.Split(block, ":")
		if len(parts) != 2 {
			continue
		}
		key := parts[0]
		values := parts[1]
		res[key] = &pb.ListValues{
			Values: strings.Split(values, ","),
		}
	}
	return res
}

// VersionCheck 比较两个三级版本号的大小
// 如果v1 > v2，返回1
// 如果v1 < v2，返回-1
// 如果v1 == v2，返回0
func VersionCheck(v1, v2 string) int {
	// 解析版本号
	parts1 := parseVersion(v1)
	parts2 := parseVersion(v2)

	// 逐级比较
	for i := 0; i < 3; i++ {
		if parts1[i] > parts2[i] {
			return 1
		} else if parts1[i] < parts2[i] {
			return -1
		}
	}

	return 0
}

// parseVersion 解析版本号字符串，返回[x, y, z]格式的整数数组
func parseVersion(version string) [3]int {
	var result [3]int

	// 按.分割版本号
	parts := strings.Split(version, ".")

	// 转换为整数，缺省部分默认为0
	for i := 0; i < 3; i++ {
		if i < len(parts) && parts[i] != "" {
			if num, err := strconv.Atoi(parts[i]); err == nil {
				result[i] = num
			}
		}
		// 如果解析失败或缺省，保持默认值0
	}

	return result
}
