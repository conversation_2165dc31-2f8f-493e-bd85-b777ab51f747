package helper

import (
	"encoding/json"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	jsonStrProto = `{
    "test": "1231",
    "pbReq1": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    }]
,
    "pbReq2": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    }]
,
    "pbReq3": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    }]
}
`

	jsonStr = `{
    "test": "1231",
    "pbReq1": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    }],
    "pbReq2": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    }]
,
    "pbReq3": [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": 1
    }]
}
`
	jsonStrProto2 = ` [{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    },
{
        "doc_range": [
            "yuangongshouce.pdf"
        ],
        "knowledge_base_id": "5ebc105d-c433-4e9c-b0a3-a79689145bfa",
        "query": "入职材料",
        "recall_params": {
            "score_threshold": 0.1,
            "top_k": 30
        },
        "rerank_params": {
            "score_threshold": 0.1,
            "top_k": 3,
            "model": {
                "full_url": "dlie://mwh-deployment-cnskg6qkbjg7k7qnftc0:8001"
            }
        },
        "strategy": "MIXED"
    }]
`
)

func TestProto(t *testing.T) {
	testProto := new(testProto)
	if err := stdsrv.UnmarshalMixWithProto(jsonStrProto, testProto); err != nil {
		stdlog.Info(err)
	}
	byte, _ := stdsrv.MarshalMixWithProto(testProto)
	stdlog.Info(string(byte))
}

func TestProto2(t *testing.T) {
	testProto2 := new(testProto2)
	pbs := make([]pb.RetrieveKnowledgeBaseReq, 0)
	if err := stdsrv.UnmarshalMixWithProto(jsonStrProto2, &pbs); err != nil {
		stdlog.Info(err)
	}
	if err := stdsrv.UnmarshalMixWithProto(jsonStrProto2, &testProto2.Pbs); err != nil {
		stdlog.Info(err)
	}

	byte1, _ := stdsrv.MarshalMixWithProto(testProto2)
	byte2, _ := stdsrv.MarshalMixWithProto(pbs)

	byte3, _ := stdsrv.MarshalMixWithProto(pbs[0])

	pb := new(pb.RetrieveKnowledgeBaseReq)
	if err := stdsrv.UnmarshalMixWithProto(string(byte3), pb); err != nil {
		stdlog.Info(err)
	}

	stdlog.Info(string(byte1))
	stdlog.Info(string(byte2))

}

type testProto2 struct {
	Pbs []*pb.RetrieveKnowledgeBaseReq `json:"pbs" `
}

type testProto struct {
	PbReq1 []*pb.RetrieveKnowledgeBaseReq `json:"pbReq1" `
	PbReq2 []*pb.RetrieveKnowledgeBaseReq `json:"pbReq2" `
	PbReq3 []*pb.RetrieveKnowledgeBaseReq `json:"pbReq3" `
	Test   string                         `json:"test" `
}

func BenchmarkUnmarshalMixWithProto(b *testing.B) {
	testProto := new(testProto)
	for i := 0; i < b.N; i++ {
		stdsrv.UnmarshalMixWithProto(jsonStrProto, testProto)
	}
}
func BenchmarkUnmarshal(b *testing.B) {
	testProto := new(testProto)
	for i := 0; i < b.N; i++ {
		json.Unmarshal([]byte(jsonStr), testProto)
	}
}

func BenchmarkMarshalMixWithProto(b *testing.B) {
	testProto := new(testProto)
	json.Unmarshal([]byte(jsonStr), testProto)
	for i := 0; i < b.N; i++ {
		stdsrv.MarshalMixWithProto(testProto)
	}
}
func BenchmarkMarshal(b *testing.B) {
	testProto := new(testProto)
	json.Unmarshal([]byte(jsonStr), testProto)
	for i := 0; i < b.N; i++ {
		json.Marshal(testProto)
	}
}
