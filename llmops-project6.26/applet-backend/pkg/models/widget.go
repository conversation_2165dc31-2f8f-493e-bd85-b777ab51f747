package models

import "transwarp.io/applied-ai/applet-backend/pkg/widgets"

type WidgetType struct {
	Code int32
	Desc string
}

var (
	// DynamicWidgetTypeCustomWidget 自定义算子
	DynamicWidgetTypeCustomWidget = WidgetType{
		Code: 0,
		Desc: "CustomWidget",
	}
	// DynamicWidgetTypePrompt 提示词模板
	DynamicWidgetTypePrompt = WidgetType{
		Code: 1,
		Desc: "Prompt",
	}
	// DynamicWidgetTypeSubChain 子链
	DynamicWidgetTypeSubChain = WidgetType{
		Code: 2,
		Desc: "SubChain",
	}
	// DynamicWidgetTypeExternalAPI 外部API
	DynamicWidgetTypeExternalAPI = WidgetType{
		Code: 3,
		Desc: "ExternalAPI",
	}
)

type DynamicWidgetDesc struct {
	Type             string `json:"type"`
	Name             string `json:"name"`
	Key              string `json:"key"`
	ContainsSub<PERSON>hain bool   `json:"contains_sub_chain"`
}

// WidgetGroup 算子组，包含一批功能相近的算子定义
type WidgetGroup struct {
	Id       string            `json:"id"`
	Name     string            `json:"name"`
	Desc     string            `json:"desc"`
	SortFlag widgets.SortFlag  `json:"sort_flag"`
	Widgets  []*widgets.Widget `json:"widgets"`
}

type WidgetGroupText struct {
	Text string `json:"text" description:"算子分组的markdown文档"`
}
