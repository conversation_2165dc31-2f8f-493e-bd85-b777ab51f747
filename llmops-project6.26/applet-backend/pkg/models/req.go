package models

import (
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type DynamicWidgetType string

type GetDynamicWidgetReq struct {
	Type string `json:"type"`
}

var dynamicWidgetTypeList = []string{DynamicWidgetTypePrompt.Desc, DynamicWidgetTypeSubChain.Desc,
	DynamicWidgetTypeExternalAPI.Desc, DynamicWidgetTypeCustomWidget.Desc}

func IsDynamicWidgetTypeValid(widgetType string) error {
	for _, w := range dynamicWidgetTypeList {
		if w == widgetType {
			return nil
		}
	}
	return stderr.Internal.Error("invalid type :%v", widgetType)
}
func (g GetDynamicWidgetReq) IsValid() error {
	return IsDynamicWidgetTypeValid(g.Type)
}

type GetDynamicWidgetDefineReq struct {
	Type string `json:"type"`
	Key  string `json:"key"`
}

type GetDynamicInputWidgetDefineReq struct {
	Params []InputParam `json:"params"`
}

type InputParam struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

func (g GetDynamicInputWidgetDefineReq) IsValid() error {
	if len(g.Params) == 0 {
		return stderr.Internal.Error("no prams")
	}
	m := make(map[string]struct{}, 0)
	for _, p := range g.Params {
		if _, ok := m[p.Name]; ok {
			return stderr.Internal.Error("invalid param : param duplicated :%v", g.Params)
		}
		m[p.Name] = struct{}{}
	}
	return nil
}

func (g GetDynamicWidgetDefineReq) IsValid() error {
	return GetDynamicWidgetReq{Type: g.Type}.IsValid()
}

type ForkChainToTemplateReq struct {
	ChainName string `json:"chain_name"`
}

type ForkChainToProjectReq struct {
	// 原始应用链id
	OriChainID string `json:"ori_chain_id"`
	// 目标project id
	TargetProjectID string `json:"target_project_id"`
	// 目标应用链name
	TargetChainName string `json:"target_chain_name"`
	// 目标应用链描述
	TargetChainDesc string `json:"target_chain_desc"`
	// 是否丢弃标签
	RemoveOriLabel bool `json:"remove_ori_label"`
	// 目标标签
	TargetLabels LabelGroups `json:"target_labels"`
}

type UpdateDebugStateReq struct {
	State string `json:"state"`
}

func (u UpdateDebugStateReq) IsValid() error {
	if u.State == ChainDebugStateSuccess.State {
		return nil
	}
	if u.State == ChainDebugStateFailed.State {
		return nil
	}
	if u.State == ChainDebugStateCanceled.State {
		return nil
	}
	return stderr.Error("invalid debug state :%v", u.State)
}

type PublishApplicationReq struct {
	ID            string                `json:"id"`
	Examination   *ExaminationInfo      `json:"examination"`
	PermissionCfg *common.PermissionCfg `json:"permission_cfg"`
}

type ExaminationInfo struct {
	Detail string         `json:"detail"`
	Module string         `json:"module"`
	Object ExaminationObj `json:"object"`
}

type ExaminationObj struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
