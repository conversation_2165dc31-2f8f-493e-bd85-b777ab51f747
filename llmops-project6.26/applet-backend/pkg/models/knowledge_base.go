package models

import (
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/common"
)

const (
	TableNameKnowledgeBases = "knowledge_bases"
)

type KnowledgeBase struct {
	Id string `gorm:"column:id;primaryKey;type:varchar(191)" json:"id" description:"知识库id"`

	Name string `gorm:"column:name;type:varchar(500)" json:"name" description:"知识库名称"`

	Description string `gorm:"column:description" json:"description" description:"知识库描述"`

	ContentType pb.KnowledgeBaseContentType `gorm:"column:content_type" json:"content_type" description:"知识库类型"`

	SourceType pb.KnowledgeBaseSourceType `gorm:"column:source_type" json:"source_type" description:"知识库来源"`

	RegistryType pb.KnowledgeBaseRegistryType `gorm:"column:registry_type" json:"registry_type" description:"知识库注册类型"`

	Icon string `gorm:"column:icon" json:"icon" description:"知识库图标"`

	ConnectionRegistry *pb.KnowledgeBaseConnectionRegistry `gorm:"column:connection_registry;serializer:json" json:"connection_registry" description:"数据连接类型的注册信息"`

	TkhRegistry *pb.KnowledgeBaseTKHRegistry `gorm:"column:tkh_registry;serializer:json" json:"tkh_registry" description:"TKH类型的注册信息"`

	Creator string `gorm:"column:creator;type:varchar(200)" json:"creator" description:"创建用户"`

	CreateTime time.Time `gorm:"column:create_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" json:"create_time" description:"创建时间"`

	UpdateTime time.Time `gorm:"column:update_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" json:"update_time" description:"更新时间"`

	DisabledDocs []string `gorm:"column:disabled_docs;serializer:json" json:"disabled_docs" description:"禁用文档id列表"`

	ProjectId string `gorm:"column:project_id;type:varchar(191)" json:"projectId"`

	IsVisible bool `gorm:"column:is_visible" json:"is_visible" description:"是否可见"`

	IsRetrievable bool `gorm:"column:is_retrievable" json:"is_retrievable" description:"是否可检索"`

	CreationType pb.KnowledgeBaseCreationType `gorm:"column:creation_type" json:"creation_type" description:"知识库类型"`

	DocProcessingConfig *pb.DocProcessingConfig `gorm:"column:doc_processing_config;serializer:json" json:"doc_processing_config" description:"文档加工配置"`

	RetrievalConfig *pb.RetrievalConfig `gorm:"column:retrieval_config;serializer:json" json:"retrieval_config" description:"检索配置"`

	VectorModel *pb.ModelService `gorm:"column:vector_model;serializer:json" json:"vector_model" description:"向量模型"`

	IsPublic bool `gorm:"column:is_public" json:"is_public" description:"是否共享到公共空间"`

	MetricsInfo *pb.MetricsInfo `gorm:"column:metrics_info;serializer:json;default:'{}';" json:"metrics_info" description:"知识库使用次数"`

	IsPublished bool `gorm:"column:is_published" json:"is_published" description:"是否已发布"`

	SceneType pb.KnowledgeBaseSceneType `gorm:"column:scene_type" json:"scene_type" description:"业务场景类型"`

	PublishInfo *pb.KnowledgeBasePublishInfo `gorm:"column:publish_info;serializer:json" json:"publish_info" description:"服务发布信息"`

	Labels map[string]string `gorm:"column:labels;serializer:json" json:"labels" description:"知识库标签"`

	DatabaseConfig *pb.DatabaseConfig `gorm:"column:database_config;serializer:json" json:"database_config" description:"数据库配置"`

	NoticeEnabled bool `gorm:"column:notice_enabled" json:"notice_enabled" description:"文档更新通知"`

	Version string `json:"version" gorm:"column:version;default:1.0.0" description:"创建知识库的时的后端版本"`

	// rbac
	PermissionCfg    *cas.PermissionCfg `json:"permission_cfg" gorm:"column:permission_cfg;serializer:json"`
	PermissionAction cas.Act            `json:"permission_action" gorm:"-"` // 返回给前端列表时的权限
}

// TableName KnowledgeBase's table name
func (*KnowledgeBase) TableName() string {
	return TableNameKnowledgeBases
}

func (kb *KnowledgeBase) IsExternal() bool {
	return !(kb.SourceType == pb.KnowledgeBaseSourceType_FROM_SCRATCH && kb.RegistryType == pb.KnowledgeBaseRegistryType_NULL)
}

// 将`KnowledgeBase`转换为`pb.KnowledgeBase`的方法
func (kb *KnowledgeBase) ToPb() *pb.KnowledgeBase {
	// 创建pb.KnowledgeBase的实例
	protoKB := &pb.KnowledgeBase{
		Id:                  kb.Id,
		Name:                kb.Name,
		Description:         kb.Description,
		ContentType:         pb.KnowledgeBaseContentType(kb.ContentType),
		SourceType:          pb.KnowledgeBaseSourceType(kb.SourceType),
		RegistryType:        pb.KnowledgeBaseRegistryType(kb.RegistryType),
		Icon:                kb.Icon,
		ConnectionRegistry:  kb.ConnectionRegistry,
		TkhRegistry:         kb.TkhRegistry,
		CreateUser:          kb.Creator,
		CreateTimeMills:     kb.CreateTime.UnixMilli(),
		UpdateTimeMills:     kb.UpdateTime.UnixMilli(),
		DisabledDocs:        kb.DisabledDocs,
		ProjectId:           kb.ProjectId,
		IsVisible:           kb.IsVisible,
		IsRetrievable:       kb.IsRetrievable,
		CreationType:        kb.CreationType,
		DocProcessingConfig: kb.DocProcessingConfig,
		RetrievalConfig:     kb.RetrievalConfig,
		VectorModel:         kb.VectorModel,
		IsPublic:            kb.IsPublic,
		MetricsInfo:         kb.MetricsInfo,
		IsPublished:         kb.IsPublished,
		SceneType:           kb.SceneType,
		PublishInfo:         kb.PublishInfo,
		Labels:              kb.Labels,
		DatabaseConfig:      kb.DatabaseConfig,
		NoticeEnabled:       kb.NoticeEnabled,
		Version:             kb.Version,
		PermissionAction:    string(kb.PermissionAction),
	}
	// 确保该字段为非空
	if protoKB.MetricsInfo == nil {
		protoKB.MetricsInfo = &pb.MetricsInfo{}
	}
	if kb.PermissionCfg != nil {
		protoKB.PermissionCfg = &common.PermissionCfg{
			PermissionMode: string(kb.PermissionCfg.PermissionMode),
			PublicType:     string(kb.PermissionCfg.PublicType),
			CustomPolicies: func() []*common.CustomPolicy {
				if len(kb.PermissionCfg.CustomPolicies) == 0 {
					return []*common.CustomPolicy{}
				}
				res := make([]*common.CustomPolicy, 0, len(kb.PermissionCfg.CustomPolicies))
				for _, policy := range kb.PermissionCfg.CustomPolicies {
					res = append(res, &common.CustomPolicy{
						SubType:  string(policy.SubType),
						Action:   string(policy.Action),
						Username: policy.Username,
						GroupId:  int32(policy.GroupId),
					})
				}
				return res
			}(),
		}
	}
	return protoKB
}

func FromKnowledgeBasePb(protoKB *pb.KnowledgeBase) *KnowledgeBase {
	kb := new(KnowledgeBase)
	// 将proto中的字段赋值给struct KnowledgeBase
	kb.Id = protoKB.Id
	kb.Name = protoKB.Name
	kb.Description = protoKB.Description
	kb.ContentType = pb.KnowledgeBaseContentType(protoKB.ContentType)
	kb.SourceType = pb.KnowledgeBaseSourceType(protoKB.SourceType)
	kb.RegistryType = pb.KnowledgeBaseRegistryType(protoKB.RegistryType)
	kb.Icon = protoKB.Icon
	kb.ConnectionRegistry = protoKB.ConnectionRegistry
	kb.TkhRegistry = protoKB.TkhRegistry
	kb.Creator = protoKB.CreateUser
	kb.CreateTime = time.UnixMilli(protoKB.CreateTimeMills)
	kb.UpdateTime = time.UnixMilli(protoKB.UpdateTimeMills)
	kb.DisabledDocs = protoKB.DisabledDocs
	kb.ProjectId = protoKB.ProjectId
	kb.IsVisible = protoKB.IsVisible
	kb.IsRetrievable = protoKB.IsRetrievable
	kb.CreationType = protoKB.CreationType
	kb.DocProcessingConfig = protoKB.DocProcessingConfig
	kb.RetrievalConfig = protoKB.RetrievalConfig
	kb.VectorModel = protoKB.VectorModel
	kb.IsPublic = protoKB.IsPublic
	kb.MetricsInfo = protoKB.MetricsInfo
	kb.IsPublished = protoKB.IsPublished
	kb.SceneType = protoKB.SceneType
	kb.PublishInfo = protoKB.PublishInfo
	kb.Labels = protoKB.Labels
	kb.DatabaseConfig = protoKB.DatabaseConfig
	kb.NoticeEnabled = protoKB.NoticeEnabled
	kb.Version = protoKB.Version
	kb.PermissionAction = cas.Act(protoKB.PermissionAction)
	if protoKB.PermissionCfg != nil {
		kb.PermissionCfg = &cas.PermissionCfg{
			PermissionMode: cas.PermissionMode(protoKB.PermissionCfg.PermissionMode),
			PublicType:     cas.PublicType(protoKB.PermissionCfg.PublicType),
			CustomPolicies: func() []*cas.PolicyCfg {
				if len(protoKB.PermissionCfg.CustomPolicies) == 0 {
					return []*cas.PolicyCfg{}
				}
				res := make([]*cas.PolicyCfg, 0, len(protoKB.PermissionCfg.CustomPolicies))
				for _, policy := range protoKB.PermissionCfg.CustomPolicies {
					res = append(res, &cas.PolicyCfg{
						SubType:  cas.SubType(policy.SubType),
						Username: policy.Username,
						GroupId:  uint64(policy.GroupId),
						Action:   cas.Act(policy.Action),
					})
				}
				return res
			}(),
		}
	}
	return kb
}

type KnowledgeBaseTree struct {
	Id   string           `json:"id"`
	Tree *pb.DocumentTree `json:"tree"`
}

// KnowledgeBaseStorageCost 知识库的存储开销统计，单位为byte
type KnowledgeBaseStorageCost struct {
	Id   string `json:"id"`
	Name string `json:"name"`

	DocFilesCost  int64 `json:"doc_files_cost"`
	MetaStoreCost int64 `json:"meta_store_cost"`
	HippoCost     int64 `json:"hippo_cost"`
	ScopeCost     int64 `json:"scope_cost"`
	TotalCost     int64 `json:"total_cost"`
}

type KnowledgeEnhancePrompts struct {
	// @gotags: description:"用于生成问题的提示词"
	QuestionPrompt string `protobuf:"bytes,7,opt,name=question_prompt,json=questionPrompt,proto3" json:"question_prompt,omitempty" description:"用于生成问题的提示词"`
	// @gotags: description:"用于生成摘要的提示词"
	SummaryPrompt string `protobuf:"bytes,8,opt,name=summary_prompt,json=summaryPrompt,proto3" json:"summary_prompt,omitempty" description:"用于生成摘要的提示词"`
	// @gotags: description:"用于生成表格描述的提示词"
	TableDescPrompt string `protobuf:"bytes,9,opt,name=table_desc_prompt,json=tableDescPrompt,proto3" json:"table_desc_prompt,omitempty" description:"用于生成表格描述的提示词"`
	// @gotags: description:"用于生成表格摘要的提示词"
	TableSummaryPrompt string `protobuf:"bytes,10,opt,name=table_summary_prompt,json=tableSummaryPrompt,proto3" json:"table_summary_prompt,omitempty" description:"用于生成表格摘要的提示词"`
	// @gotags: description:"用于生成图像描述提示词"
	ImageDescPrompt string `protobuf:"bytes,15,opt,name=image_desc_prompt,json=imageDescPrompt,proto3" json:"image_desc_prompt,omitempty" description:"用于生成图像描述提示词"`
}
