[{"id": "87983e69-3ea3-45c0-91e9-1905d60d945c", "project_id": "assets", "experiment_info": {"image_url": "sfs://tenants/llmops-assets/projs/assets/avatar/3d11033b-3cb7-40af-b445-f76a213e15bc_东吴证券.jpg", "introduction": "我是您的智能专属投顾，您可以向我咨询", "multimodal_examples": [{"text": "东吴证券的实时行情、近期动态以及相关的分析"}, {"text": "介绍一下贵州茅台这支股票"}, {"text": "请告诉我华泰证券的近况"}], "members": null, "triggers": null, "name": "AI诊股", "src_url": "", "label_info": {"机构": ["东吴证券"]}}, "service_info": {"id": "8559211f-1535-4961-9e92-c0f7fe179fa6", "name": "Chain-AI诊股-**********", "virtual_url": "seldon/llmops-assets/service-8559211f-1535-4961-9e92-c0f7fe179fa6", "status": 0, "state_info": {"simple_svc_state": "Offline", "mlops_svc_state": "Offline"}, "health_info": {"name": "", "healthy": false, "detail": "", "timestamp": 0, "dependencies": null}}, "chain_detail": {"nodes": [{"id": "9da11bee-f748-4f7b-96d4-37f4e7a2c34d", "name": "应用链调用-dw-http", "widget_id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "widget_detail": {"id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "name": "应用链调用-dw-http", "desc": "dw-http", "group": "WidgetGroupSubChain", "params": [{"data_class": "string", "category": "attribute", "preview": true, "define": {"id": "SubChainDetail", "name": "应用链", "type": "TYPE_APPLET_CHAIN", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "name": "Json输入", "desc": "Json字符串输入", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}]}, "ui": "{\"id\":\"9da11bee-f748-4f7b-96d4-37f4e7a2c34d\",\"type\":\"custom\",\"position\":{\"x\":3335.9879035314157,\"y\":-2205.673544716269},\"zIndex\":9999,\"width\":320,\"height\":180,\"selected\":false,\"dragging\":false,\"positionAbsolute\":{\"x\":3335.9879035314157,\"y\":-2205.673544716269},\"measured\":{\"width\":320,\"height\":180}}", "values": {"SubChainDetail": {"base": {"asset_type": "ASSET_SHARED", "assistant_info": {"agent_config": null, "input_guardrails": {"prompt_inject_guard": {"intelligent_protection_strategy": {"intelligent_protection_strategy_enabled": false, "knowledge_hub_info": {"id": "", "project_id": ""}, "safety_restrictions_threshold": 0}, "preset_rule_strategy": {"file_infos": null, "preset_rule_strategy_enabled": false, "safety_restrictions_threshold": 0}, "prompt_inject_guard_enabled": false, "voiceover": ""}, "sensitive_protection": {"file_infos": null, "reg_exp_file_infos": null, "sensitive_protection_enabled": false, "voiceover": ""}}, "internet_search_info": {"enable": false, "parse_url": false}, "local_files": null, "output_guardrails": {"sensitive_protection": {"file_infos": null, "reg_exp_file_infos": null, "sensitive_protection_enabled": false, "voiceover": ""}}, "problem_gen_info": null}, "contains_sub_chain": false, "create_time": 1734413895000, "created_type": "Applet-Chain", "creator": "soochow-01", "debug_state": {"code": 0, "state": "init"}, "desc": "", "experiment_info": {"image_url": "", "introduction": "", "label_info": {}, "members": null, "multimodal_examples": null, "name": "dw-http", "src_url": ""}, "id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "labels": {}, "metrics_info": {"clone_times": 0, "execute_times": 0, "visit_times": 72}, "name": "dw-http", "projectID": "dongwu", "published": false, "service_info": null, "source_info": {"source_chain_id": "06c8bb9b-d3dd-4b04-85ad-f41160b28001", "source_project_id": "dongwu"}, "space_info": {}, "update_time": 1734504173000, "usage_type": "Common"}, "chain_detail": {"edges": [{"id": "reactflow__edge-d5532975-4459-416a-95ca-a7153192e9d9d5532975-4459-416a-95ca-a7153192e9d9@@OutPut-cec42d30-8402-44b9-b955-da392ad5b18acec42d30-8402-44b9-b955-da392ad5b18a@@Content", "source": "d5532975-4459-416a-95ca-a7153192e9d9", "source_param": "d5532975-4459-416a-95ca-a7153192e9d9@@OutPut", "target": "cec42d30-8402-44b9-b955-da392ad5b18a", "target_param": "cec42d30-8402-44b9-b955-da392ad5b18a@@Content"}], "nodes": [{"id": "cec42d30-8402-44b9-b955-da392ad5b18a", "name": "Python代码", "sub_chain_base_info": null, "ui": "{\"id\":\"cec42d30-8402-44b9-b955-da392ad5b18a\",\"position\":{\"x\":624.7104294351652,\"y\":55.480497979519996},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyPythonWidget\",\"name\":\"Python代码\",\"desc\":\"可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖\",\"group\":\"WidgetGroupCodeTool\",\"oriWidgetKey\":\"WidgetKeyPythonWidget\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"preview\":false,\"define\":{\"id\":\"Content\",\"name\":\"输入数据\",\"desc\":\"需要使用python代码处理的数据，任意类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}},{\"data_class\":\"code\",\"category\":\"attribute\",\"preview\":false,\"define\":{\"id\":\"Code\",\"name\":\"python代码\",\"desc\":\"python代码，点击可查看或编辑代码\",\"default_value\":\"\\n# python解释器版本 3.11\\n\\ndef handler(data):\\n    \\\"\\\"\\\"\\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\\n   \\n    params:\\n    data (any): 参数名任意，任意类型\\n\\n    return:\\n    any: 返回任意类型\\n\\n    \\\"\\\"\\\"\\n    # TODO 数据处理\\n    # xxx\\n    result = data\\n\\n    return result\\n\\n\",\"required\":true,\"type\":\"TYPE_CODE_PYTHON\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"preview\":false,\"define\":{\"id\":\"OutPut\",\"desc\":\"python代码中handler函数返回的数据，任意类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}}]},\"values\":{\"Code\":\"\\n# python解释器版本 3.11\\n\\ndef handler(data):\\n    \\\"\\\"\\\"\\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\\n   \\n    params:\\n    data (any): 参数名任意，任意类型\\n\\n    return:\\n    any: 返回任意类型\\n\\n    \\\"\\\"\\\"\\n\\n    import requests\\n    import datetime\\n\\n    headers = {\\n        'Content-Type': 'application/json',\\n        'Access-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ2ZXJzaW9uIjoxLCJ1c2VyX3R5cGUiOjMsInNlc3Npb25faWQiOiJiNzI3ZDc5NzQzMzg0NmNmY2IzNGM0Mzk0OWI1YmM1YSIsImxhc3RfZXhwIjoxNzM0Mzk1OTkyLCJleHAiOjIwNDk3ODQ3OTEsInVzZXJfaWQiOiJ6aG9uZ2ppbnN1byIsInVzZXJfbmFtZSI6Ilx1NGUyZFx1OTFkMVx1NjI0MCIsInNvdXJjZSI6IjAifQ.kJLuKLNxwcLHwEZIzW1dtAifCWje9hR9XjR8TKxTflg'\\n    }\\n\\n    if 'headers' in data:\\n        headers.update(data['headers'])\\n\\n    body = {\\n        'timestamp': int(datetime.datetime.now().timestamp() * 1000),\\n    }\\n    body.update(data['body'])\\n\\n    response = requests.request(**{\\n        'method': 'POST',\\n        'url': data['url'],\\n        'headers': headers,\\n        'timeout': 60,\\n        'params': data['params'] if 'params' in data else {},\\n        'allow_redirects': True,\\n        'json': body,\\n    })\\n\\n    # result = response.content.decode('utf-8')\\n    try:\\n        result = response.json()  # requests 自带的方法可以直接解析 JSON\\n        print(result)\\n    except ValueError:\\n    # 如果返回的内容不是 JSON 格式，直接返回原始字符串\\n        result = response.content.decode('utf-8')\\n\\n    return result\\n\\n\"},\"name\":\"Python代码\",\"interactive\":true},\"width\":320,\"height\":180,\"selected\":true,\"positionAbsolute\":{\"x\":624.7104294351652,\"y\":55.480497979519996},\"dragging\":false}", "values": {"Code": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n\n    import requests\n    import datetime\n\n    headers = {\n        'Content-Type': 'application/json',\n        'Access-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ2ZXJzaW9uIjoxLCJ1c2VyX3R5cGUiOjMsInNlc3Npb25faWQiOiJiNzI3ZDc5NzQzMzg0NmNmY2IzNGM0Mzk0OWI1YmM1YSIsImxhc3RfZXhwIjoxNzM0Mzk1OTkyLCJleHAiOjIwNDk3ODQ3OTEsInVzZXJfaWQiOiJ6aG9uZ2ppbnN1byIsInVzZXJfbmFtZSI6Ilx1NGUyZFx1OTFkMVx1NjI0MCIsInNvdXJjZSI6IjAifQ.kJLuKLNxwcLHwEZIzW1dtAifCWje9hR9XjR8TKxTflg'\n    }\n\n    if 'headers' in data:\n        headers.update(data['headers'])\n\n    body = {\n        'timestamp': int(datetime.datetime.now().timestamp() * 1000),\n    }\n    body.update(data['body'])\n\n    response = requests.request(**{\n        'method': 'POST',\n        'url': data['url'],\n        'headers': headers,\n        'timeout': 60,\n        'params': data['params'] if 'params' in data else {},\n        'allow_redirects': True,\n        'json': body,\n    })\n\n    # result = response.content.decode('utf-8')\n    try:\n        result = response.json()  # requests 自带的方法可以直接解析 JSON\n        print(result)\n    except ValueError:\n    # 如果返回的内容不是 JSON 格式，直接返回原始字符串\n        result = response.content.decode('utf-8')\n\n    return result\n\n"}, "widget_detail": {"desc": "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖", "group": "WidgetGroupCodeTool", "id": "WidgetKeyPythonWidget", "name": "Python代码", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"category": "in-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "需要使用python代码处理的数据，任意类型", "id": "Content", "name": "输入数据", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}, {"category": "attribute", "data_class": "code", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "desc": "python代码，点击可查看或编辑代码", "id": "Code", "name": "python代码", "required": true, "type": "TYPE_CODE_PYTHON"}, "param_limits": null, "preview": false}, {"category": "out-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "python代码中handler函数返回的数据，任意类型", "id": "OutPut", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}]}, "widget_id": "WidgetKeyPythonWidget"}, {"id": "d5532975-4459-416a-95ca-a7153192e9d9", "name": "Json输入", "sub_chain_base_info": null, "ui": "{\"id\":\"d5532975-4459-416a-95ca-a7153192e9d9\",\"position\":{\"x\":122.25038763696841,\"y\":57.44906484649918},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyUpstreamInput\",\"name\":\"Json输入\",\"desc\":\"输入Json字符串并将其反序列化\",\"group\":\"WidgetGroupInput\",\"params\":[{\"data_class\":\"string\",\"category\":\"req-input\",\"preview\":false,\"define\":{\"id\":\"JsonInput\",\"name\":\"Json输入\",\"desc\":\"Json字符串输入\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"preview\":false,\"define\":{\"id\":\"OutPut\",\"desc\":\"把输入的Json反序列化为字符串、数字、列表或者字典等对应的类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}}]},\"values\":{},\"name\":\"Json输入\",\"interactive\":true},\"width\":320,\"height\":133,\"selected\":false,\"positionAbsolute\":{\"x\":122.25038763696841,\"y\":57.44906484649918},\"dragging\":false}", "values": {}, "widget_detail": {"desc": "输入Json字符串并将其反序列化", "group": "WidgetGroupInput", "id": "WidgetKeyUpstreamInput", "name": "Json输入", "params": [{"category": "req-input", "data_class": "string", "define": {"data_type": "DATA_TYPE_STRING", "desc": "Json字符串输入", "id": "JsonInput", "name": "Json输入", "type": "TYPE_TEXTAREA"}, "param_limits": null, "preview": false}, {"category": "out-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "把输入的Json反序列化为字符串、数字、列表或者字典等对应的类型", "id": "OutPut", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}]}, "widget_id": "WidgetKeyUpstreamInput"}], "viewport": {"x": 18.415889290692803, "y": 131.79681972169513, "zoom": 0.8705505632961241}}}}, "sub_chain_base_info": null}, {"id": "0a742e03-4df7-4384-841f-a4d3798d38b0", "name": "应用链调用-dw-http", "widget_id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "widget_detail": {"id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "name": "应用链调用-dw-http", "desc": "dw-http", "group": "WidgetGroupSubChain", "params": [{"data_class": "string", "category": "attribute", "preview": true, "define": {"id": "SubChainDetail", "name": "应用链", "type": "TYPE_APPLET_CHAIN", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "name": "Json输入", "desc": "Json字符串输入", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}]}, "ui": "{\"id\":\"0a742e03-4df7-4384-841f-a4d3798d38b0\",\"type\":\"custom\",\"position\":{\"x\":17133.594179890242,\"y\":-1467.6449972985886},\"zIndex\":9999,\"width\":320,\"height\":180,\"selected\":false,\"positionAbsolute\":{\"x\":17133.594179890242,\"y\":-1467.6449972985886},\"dragging\":false,\"measured\":{\"width\":320,\"height\":180}}", "values": {"SubChainDetail": {"base": {"asset_type": "ASSET_SHARED", "assistant_info": {"agent_config": null, "input_guardrails": {"prompt_inject_guard": {"intelligent_protection_strategy": {"intelligent_protection_strategy_enabled": false, "knowledge_hub_info": {"id": "", "project_id": ""}, "safety_restrictions_threshold": 0}, "preset_rule_strategy": {"file_infos": null, "preset_rule_strategy_enabled": false, "safety_restrictions_threshold": 0}, "prompt_inject_guard_enabled": false, "voiceover": ""}, "sensitive_protection": {"file_infos": null, "reg_exp_file_infos": null, "sensitive_protection_enabled": false, "voiceover": ""}}, "internet_search_info": {"enable": false, "parse_url": false}, "local_files": null, "output_guardrails": {"sensitive_protection": {"file_infos": null, "reg_exp_file_infos": null, "sensitive_protection_enabled": false, "voiceover": ""}}, "problem_gen_info": null}, "contains_sub_chain": false, "create_time": 1734413895000, "created_type": "Applet-Chain", "creator": "soochow-01", "debug_state": {"code": 0, "state": "init"}, "desc": "", "experiment_info": {"image_url": "", "introduction": "", "label_info": {}, "members": null, "multimodal_examples": null, "name": "dw-http", "src_url": ""}, "id": "021a5ab9-1105-452c-b7c0-274f0785edaa", "labels": {}, "metrics_info": {"clone_times": 0, "execute_times": 0, "visit_times": 77}, "name": "dw-http", "projectID": "dongwu", "published": false, "service_info": null, "source_info": {"source_chain_id": "06c8bb9b-d3dd-4b04-85ad-f41160b28001", "source_project_id": "dongwu"}, "space_info": {}, "update_time": 1734504173000, "usage_type": "Common"}, "chain_detail": {"edges": [{"id": "reactflow__edge-d5532975-4459-416a-95ca-a7153192e9d9d5532975-4459-416a-95ca-a7153192e9d9@@OutPut-cec42d30-8402-44b9-b955-da392ad5b18acec42d30-8402-44b9-b955-da392ad5b18a@@Content", "source": "d5532975-4459-416a-95ca-a7153192e9d9", "source_param": "d5532975-4459-416a-95ca-a7153192e9d9@@OutPut", "target": "cec42d30-8402-44b9-b955-da392ad5b18a", "target_param": "cec42d30-8402-44b9-b955-da392ad5b18a@@Content"}], "nodes": [{"id": "cec42d30-8402-44b9-b955-da392ad5b18a", "name": "Python代码", "sub_chain_base_info": null, "ui": "{\"id\":\"cec42d30-8402-44b9-b955-da392ad5b18a\",\"position\":{\"x\":624.7104294351652,\"y\":55.480497979519996},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyPythonWidget\",\"name\":\"Python代码\",\"desc\":\"可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖\",\"group\":\"WidgetGroupCodeTool\",\"oriWidgetKey\":\"WidgetKeyPythonWidget\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"preview\":false,\"define\":{\"id\":\"Content\",\"name\":\"输入数据\",\"desc\":\"需要使用python代码处理的数据，任意类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}},{\"data_class\":\"code\",\"category\":\"attribute\",\"preview\":false,\"define\":{\"id\":\"Code\",\"name\":\"python代码\",\"desc\":\"python代码，点击可查看或编辑代码\",\"default_value\":\"\\n# python解释器版本 3.11\\n\\ndef handler(data):\\n    \\\"\\\"\\\"\\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\\n   \\n    params:\\n    data (any): 参数名任意，任意类型\\n\\n    return:\\n    any: 返回任意类型\\n\\n    \\\"\\\"\\\"\\n    # TODO 数据处理\\n    # xxx\\n    result = data\\n\\n    return result\\n\\n\",\"required\":true,\"type\":\"TYPE_CODE_PYTHON\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"preview\":false,\"define\":{\"id\":\"OutPut\",\"desc\":\"python代码中handler函数返回的数据，任意类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}}]},\"values\":{\"Code\":\"\\n# python解释器版本 3.11\\n\\ndef handler(data):\\n    \\\"\\\"\\\"\\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\\n   \\n    params:\\n    data (any): 参数名任意，任意类型\\n\\n    return:\\n    any: 返回任意类型\\n\\n    \\\"\\\"\\\"\\n\\n    import requests\\n    import datetime\\n\\n    headers = {\\n        'Content-Type': 'application/json',\\n        'Access-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ2ZXJzaW9uIjoxLCJ1c2VyX3R5cGUiOjMsInNlc3Npb25faWQiOiJiNzI3ZDc5NzQzMzg0NmNmY2IzNGM0Mzk0OWI1YmM1YSIsImxhc3RfZXhwIjoxNzM0Mzk1OTkyLCJleHAiOjIwNDk3ODQ3OTEsInVzZXJfaWQiOiJ6aG9uZ2ppbnN1byIsInVzZXJfbmFtZSI6Ilx1NGUyZFx1OTFkMVx1NjI0MCIsInNvdXJjZSI6IjAifQ.kJLuKLNxwcLHwEZIzW1dtAifCWje9hR9XjR8TKxTflg'\\n    }\\n\\n    if 'headers' in data:\\n        headers.update(data['headers'])\\n\\n    body = {\\n        'timestamp': int(datetime.datetime.now().timestamp() * 1000),\\n    }\\n    body.update(data['body'])\\n\\n    response = requests.request(**{\\n        'method': 'POST',\\n        'url': data['url'],\\n        'headers': headers,\\n        'timeout': 60,\\n        'params': data['params'] if 'params' in data else {},\\n        'allow_redirects': True,\\n        'json': body,\\n    })\\n\\n    # result = response.content.decode('utf-8')\\n    try:\\n        result = response.json()  # requests 自带的方法可以直接解析 JSON\\n        print(result)\\n    except ValueError:\\n    # 如果返回的内容不是 JSON 格式，直接返回原始字符串\\n        result = response.content.decode('utf-8')\\n\\n    return result\\n\\n\"},\"name\":\"Python代码\",\"interactive\":true},\"width\":320,\"height\":180,\"selected\":true,\"positionAbsolute\":{\"x\":624.7104294351652,\"y\":55.480497979519996},\"dragging\":false}", "values": {"Code": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n\n    import requests\n    import datetime\n\n    headers = {\n        'Content-Type': 'application/json',\n        'Access-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ2ZXJzaW9uIjoxLCJ1c2VyX3R5cGUiOjMsInNlc3Npb25faWQiOiJiNzI3ZDc5NzQzMzg0NmNmY2IzNGM0Mzk0OWI1YmM1YSIsImxhc3RfZXhwIjoxNzM0Mzk1OTkyLCJleHAiOjIwNDk3ODQ3OTEsInVzZXJfaWQiOiJ6aG9uZ2ppbnN1byIsInVzZXJfbmFtZSI6Ilx1NGUyZFx1OTFkMVx1NjI0MCIsInNvdXJjZSI6IjAifQ.kJLuKLNxwcLHwEZIzW1dtAifCWje9hR9XjR8TKxTflg'\n    }\n\n    if 'headers' in data:\n        headers.update(data['headers'])\n\n    body = {\n        'timestamp': int(datetime.datetime.now().timestamp() * 1000),\n    }\n    body.update(data['body'])\n\n    response = requests.request(**{\n        'method': 'POST',\n        'url': data['url'],\n        'headers': headers,\n        'timeout': 60,\n        'params': data['params'] if 'params' in data else {},\n        'allow_redirects': True,\n        'json': body,\n    })\n\n    # result = response.content.decode('utf-8')\n    try:\n        result = response.json()  # requests 自带的方法可以直接解析 JSON\n        print(result)\n    except ValueError:\n    # 如果返回的内容不是 JSON 格式，直接返回原始字符串\n        result = response.content.decode('utf-8')\n\n    return result\n\n"}, "widget_detail": {"desc": "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖", "group": "WidgetGroupCodeTool", "id": "WidgetKeyPythonWidget", "name": "Python代码", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"category": "in-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "需要使用python代码处理的数据，任意类型", "id": "Content", "name": "输入数据", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}, {"category": "attribute", "data_class": "code", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "desc": "python代码，点击可查看或编辑代码", "id": "Code", "name": "python代码", "required": true, "type": "TYPE_CODE_PYTHON"}, "param_limits": null, "preview": false}, {"category": "out-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "python代码中handler函数返回的数据，任意类型", "id": "OutPut", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}]}, "widget_id": "WidgetKeyPythonWidget"}, {"id": "d5532975-4459-416a-95ca-a7153192e9d9", "name": "Json输入", "sub_chain_base_info": null, "ui": "{\"id\":\"d5532975-4459-416a-95ca-a7153192e9d9\",\"position\":{\"x\":122.25038763696841,\"y\":57.44906484649918},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyUpstreamInput\",\"name\":\"Json输入\",\"desc\":\"输入Json字符串并将其反序列化\",\"group\":\"WidgetGroupInput\",\"params\":[{\"data_class\":\"string\",\"category\":\"req-input\",\"preview\":false,\"define\":{\"id\":\"JsonInput\",\"name\":\"Json输入\",\"desc\":\"Json字符串输入\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"preview\":false,\"define\":{\"id\":\"OutPut\",\"desc\":\"把输入的Json反序列化为字符串、数字、列表或者字典等对应的类型\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}}]},\"values\":{},\"name\":\"Json输入\",\"interactive\":true},\"width\":320,\"height\":133,\"selected\":false,\"positionAbsolute\":{\"x\":122.25038763696841,\"y\":57.44906484649918},\"dragging\":false}", "values": {}, "widget_detail": {"desc": "输入Json字符串并将其反序列化", "group": "WidgetGroupInput", "id": "WidgetKeyUpstreamInput", "name": "Json输入", "params": [{"category": "req-input", "data_class": "string", "define": {"data_type": "DATA_TYPE_STRING", "desc": "Json字符串输入", "id": "JsonInput", "name": "Json输入", "type": "TYPE_TEXTAREA"}, "param_limits": null, "preview": false}, {"category": "out-port", "data_class": "string", "define": {"data_type": "DATA_TYPE_UNSPECIFIED", "desc": "把输入的Json反序列化为字符串、数字、列表或者字典等对应的类型", "id": "OutPut", "type": "TYPE_UNSPECIFIED"}, "param_limits": null, "preview": false}]}, "widget_id": "WidgetKeyUpstreamInput"}], "viewport": {"x": 18.415889290692803, "y": 131.79681972169513, "zoom": 0.8705505632961241}}}}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-5714cc67-681a-4988-8555-8015a6cf09e45714cc67-681a-4988-8555-8015a6cf09e4@@OutPut-d853cc67-6868-43ed-a925-9f765d9e162bd853cc67-6868-43ed-a925-9f765d9e162b@@Content", "source": "5714cc67-681a-4988-8555-8015a6cf09e4", "source_param": "5714cc67-681a-4988-8555-8015a6cf09e4@@OutPut", "target": "d853cc67-6868-43ed-a925-9f765d9e162b", "target_param": "d853cc67-6868-43ed-a925-9f765d9e162b@@Content"}, {"id": "reactflow__edge-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut-2017f2e5-a9f0-47dd-9051-47699f86d3522017f2e5-a9f0-47dd-9051-47699f86d352@@Input", "source": "4d34cde3-07de-4e30-9e34-92e2b748463d", "source_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut", "target": "2017f2e5-a9f0-47dd-9051-47699f86d352", "target_param": "2017f2e5-a9f0-47dd-9051-47699f86d352@@Input"}, {"id": "reactflow__edge-2017f2e5-a9f0-47dd-9051-47699f86d3522017f2e5-a9f0-47dd-9051-47699f86d352@@OutPutElse-6120eeb8-b556-4b15-a442-a26a43a123926120eeb8-b556-4b15-a442-a26a43a12392@@Content", "source": "2017f2e5-a9f0-47dd-9051-47699f86d352", "source_param": "2017f2e5-a9f0-47dd-9051-47699f86d352@@OutPutElse", "target": "6120eeb8-b556-4b15-a442-a26a43a12392", "target_param": "6120eeb8-b556-4b15-a442-a26a43a12392@@Content"}, {"id": "reactflow__edge-6120eeb8-b556-4b15-a442-a26a43a123926120eeb8-b556-4b15-a442-a26a43a12392@@OutPut-567902d5-15d3-4902-971c-eb0c4ffca94d567902d5-15d3-4902-971c-eb0c4ffca94d@@c1", "source": "6120eeb8-b556-4b15-a442-a26a43a12392", "source_param": "6120eeb8-b556-4b15-a442-a26a43a12392@@OutPut", "target": "567902d5-15d3-4902-971c-eb0c4ffca94d", "target_param": "567902d5-15d3-4902-971c-eb0c4ffca94d@@c1"}, {"id": "reactflow__edge-5714cc67-681a-4988-8555-8015a6cf09e45714cc67-681a-4988-8555-8015a6cf09e4@@OutPut-567902d5-15d3-4902-971c-eb0c4ffca94d567902d5-15d3-4902-971c-eb0c4ffca94d@@question", "source": "5714cc67-681a-4988-8555-8015a6cf09e4", "source_param": "5714cc67-681a-4988-8555-8015a6cf09e4@@OutPut", "target": "567902d5-15d3-4902-971c-eb0c4ffca94d", "target_param": "567902d5-15d3-4902-971c-eb0c4ffca94d@@question"}, {"id": "reactflow__edge-567902d5-15d3-4902-971c-eb0c4ffca94d567902d5-15d3-4902-971c-eb0c4ffca94d@@OutPut-d57013e9-f985-4249-abc6-223265f5ad40d57013e9-f985-4249-abc6-223265f5ad40@@Content", "source": "567902d5-15d3-4902-971c-eb0c4ffca94d", "source_param": "567902d5-15d3-4902-971c-eb0c4ffca94d@@OutPut", "target": "d57013e9-f985-4249-abc6-223265f5ad40", "target_param": "d57013e9-f985-4249-abc6-223265f5ad40@@Content"}, {"id": "reactflow__edge-d57013e9-f985-4249-abc6-223265f5ad40d57013e9-f985-4249-abc6-223265f5ad40@@OutPut-55c91ff2-64e5-4ec4-9601-292a835b1b7d55c91ff2-64e5-4ec4-9601-292a835b1b7d@@Text", "source": "d57013e9-f985-4249-abc6-223265f5ad40", "source_param": "d57013e9-f985-4249-abc6-223265f5ad40@@OutPut", "target": "55c91ff2-64e5-4ec4-9601-292a835b1b7d", "target_param": "55c91ff2-64e5-4ec4-9601-292a835b1b7d@@Text"}, {"id": "reactflow__edge-f126aa3b-5470-4460-ac32-ace543408735f126aa3b-5470-4460-ac32-ace543408735@@OutPut-723e2802-1a00-4e9a-a47f-ab4d7605ecca723e2802-1a00-4e9a-a47f-ab4d7605ecca@@format_code", "source": "f126aa3b-5470-4460-ac32-ace543408735", "source_param": "f126aa3b-5470-4460-ac32-ace543408735@@OutPut", "target": "723e2802-1a00-4e9a-a47f-ab4d7605ecca", "target_param": "723e2802-1a00-4e9a-a47f-ab4d7605ecca@@format_code"}, {"id": "reactflow__edge-723e2802-1a00-4e9a-a47f-ab4d7605ecca723e2802-1a00-4e9a-a47f-ab4d7605ecca@@OutPut-988848f6-1a95-49a1-ae06-847a90381b23988848f6-1a95-49a1-ae06-847a90381b23@@Content", "source": "723e2802-1a00-4e9a-a47f-ab4d7605ecca", "source_param": "723e2802-1a00-4e9a-a47f-ab4d7605ecca@@OutPut", "target": "988848f6-1a95-49a1-ae06-847a90381b23", "target_param": "988848f6-1a95-49a1-ae06-847a90381b23@@Content"}, {"id": "reactflow__edge-988848f6-1a95-49a1-ae06-847a90381b23988848f6-1a95-49a1-ae06-847a90381b23@@OutPut-0a18a88c-e84e-48ea-b4fc-4470db3b84870a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_1", "source": "988848f6-1a95-49a1-ae06-847a90381b23", "source_param": "988848f6-1a95-49a1-ae06-847a90381b23@@OutPut", "target": "0a18a88c-e84e-48ea-b4fc-4470db3b8487", "target_param": "0a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_1"}, {"id": "reactflow__edge-988848f6-1a95-49a1-ae06-847a90381b23988848f6-1a95-49a1-ae06-847a90381b23@@OutPut-c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@Text", "source": "988848f6-1a95-49a1-ae06-847a90381b23", "source_param": "988848f6-1a95-49a1-ae06-847a90381b23@@OutPut", "target": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24", "target_param": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@Text"}, {"id": "reactflow__edge-c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@OutPut-0a18a88c-e84e-48ea-b4fc-4470db3b84870a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_2", "source": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24", "source_param": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@OutPut", "target": "0a18a88c-e84e-48ea-b4fc-4470db3b8487", "target_param": "0a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_2"}, {"id": "reactflow__edge-c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@OutPut-500d6d6d-7a7d-49ac-9acc-f36c88152ef4500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@a_1", "source": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24", "source_param": "c4b2bf8d-de28-4bcb-afde-b45c5b1dcd24@@OutPut", "target": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4", "target_param": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@a_1"}, {"id": "reactflow__edge-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut-500d6d6d-7a7d-49ac-9acc-f36c88152ef4500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@code", "source": "4d34cde3-07de-4e30-9e34-92e2b748463d", "source_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut", "target": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4", "target_param": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@code"}, {"id": "reactflow__edge-500d6d6d-7a7d-49ac-9acc-f36c88152ef4500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@OutPut-2ab8f244-0f24-4373-8bcb-2c4cb40588392ab8f244-0f24-4373-8bcb-2c4cb4058839@@Content", "source": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4", "source_param": "500d6d6d-7a7d-49ac-9acc-f36c88152ef4@@OutPut", "target": "2ab8f244-0f24-4373-8bcb-2c4cb4058839", "target_param": "2ab8f244-0f24-4373-8bcb-2c4cb4058839@@Content"}, {"id": "reactflow__edge-a98f5704-aadd-436f-ae88-dd1fcdb30ed0a98f5704-aadd-436f-ae88-dd1fcdb30ed0@@OutPut-2533d9b2-8504-4bd5-a321-5ad7535f3fa42533d9b2-8504-4bd5-a321-5ad7535f3fa4@@Text", "source": "a98f5704-aadd-436f-ae88-dd1fcdb30ed0", "source_param": "a98f5704-aadd-436f-ae88-dd1fcdb30ed0@@OutPut", "target": "2533d9b2-8504-4bd5-a321-5ad7535f3fa4", "target_param": "2533d9b2-8504-4bd5-a321-5ad7535f3fa4@@Text"}, {"id": "reactflow__edge-2533d9b2-8504-4bd5-a321-5ad7535f3fa42533d9b2-8504-4bd5-a321-5ad7535f3fa4@@OutPut-0a18a88c-e84e-48ea-b4fc-4470db3b84870a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_3", "source": "2533d9b2-8504-4bd5-a321-5ad7535f3fa4", "source_param": "2533d9b2-8504-4bd5-a321-5ad7535f3fa4@@OutPut", "target": "0a18a88c-e84e-48ea-b4fc-4470db3b8487", "target_param": "0a18a88c-e84e-48ea-b4fc-4470db3b8487@@a_3"}, {"id": "reactflow__edge-2017f2e5-a9f0-47dd-9051-47699f86d3522017f2e5-a9f0-47dd-9051-47699f86d352@@OutPutIF-deebafe1-f7bd-4657-8d22-7707feace04bdeebafe1-f7bd-4657-8d22-7707feace04b@@Content", "source": "2017f2e5-a9f0-47dd-9051-47699f86d352", "source_param": "2017f2e5-a9f0-47dd-9051-47699f86d352@@OutPutIF", "target": "deebafe1-f7bd-4657-8d22-7707feace04b", "target_param": "deebafe1-f7bd-4657-8d22-7707feace04b@@Content"}, {"id": "reactflow__edge-deebafe1-f7bd-4657-8d22-7707feace04bdeebafe1-f7bd-4657-8d22-7707feace04b@@OutPut-723e2802-1a00-4e9a-a47f-ab4d7605ecca723e2802-1a00-4e9a-a47f-ab4d7605ecca@@code", "source": "deebafe1-f7bd-4657-8d22-7707feace04b", "source_param": "deebafe1-f7bd-4657-8d22-7707feace04b@@OutPut", "target": "723e2802-1a00-4e9a-a47f-ab4d7605ecca", "target_param": "723e2802-1a00-4e9a-a47f-ab4d7605ecca@@code"}, {"id": "reactflow__edge-deebafe1-f7bd-4657-8d22-7707feace04bdeebafe1-f7bd-4657-8d22-7707feace04b@@OutPut-9b7a3087-00e9-4216-946f-852911ad388a9b7a3087-00e9-4216-946f-852911ad388a@@Content", "source": "deebafe1-f7bd-4657-8d22-7707feace04b", "source_param": "deebafe1-f7bd-4657-8d22-7707feace04b@@OutPut", "target": "9b7a3087-00e9-4216-946f-852911ad388a", "target_param": "9b7a3087-00e9-4216-946f-852911ad388a@@Content"}, {"id": "reactflow__edge-0a18a88c-e84e-48ea-b4fc-4470db3b84870a18a88c-e84e-48ea-b4fc-4470db3b8487@@OutPut-31f4d73b-63f1-45db-81bd-1f2c05403fe331f4d73b-63f1-45db-81bd-1f2c05403fe3@@Input", "source": "0a18a88c-e84e-48ea-b4fc-4470db3b8487", "source_param": "0a18a88c-e84e-48ea-b4fc-4470db3b8487@@OutPut", "target": "31f4d73b-63f1-45db-81bd-1f2c05403fe3", "target_param": "31f4d73b-63f1-45db-81bd-1f2c05403fe3@@Input"}, {"id": "reactflow__edge-31f4d73b-63f1-45db-81bd-1f2c05403fe331f4d73b-63f1-45db-81bd-1f2c05403fe3@@OutPut-ab735bd0-6fc1-4e8f-a47e-72e9e81f74eaab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@Input", "source": "31f4d73b-63f1-45db-81bd-1f2c05403fe3", "source_param": "31f4d73b-63f1-45db-81bd-1f2c05403fe3@@OutPut", "target": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea", "target_param": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@Input"}, {"id": "reactflow__edge-ab735bd0-6fc1-4e8f-a47e-72e9e81f74eaab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@OutPutElse-8bf06d03-c289-42dc-b376-50ca881d64168bf06d03-c289-42dc-b376-50ca881d6416@@Content", "source": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea", "source_param": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@OutPutElse", "target": "8bf06d03-c289-42dc-b376-50ca881d6416", "target_param": "8bf06d03-c289-42dc-b376-50ca881d6416@@Content"}, {"id": "reactflow__edge-55c91ff2-64e5-4ec4-9601-292a835b1b7d55c91ff2-64e5-4ec4-9601-292a835b1b7d@@OutPut-39f811e7-e78c-4398-9ee1-dcc594deb82d39f811e7-e78c-4398-9ee1-dcc594deb82d@@a_2", "source": "55c91ff2-64e5-4ec4-9601-292a835b1b7d", "source_param": "55c91ff2-64e5-4ec4-9601-292a835b1b7d@@OutPut", "target": "39f811e7-e78c-4398-9ee1-dcc594deb82d", "target_param": "39f811e7-e78c-4398-9ee1-dcc594deb82d@@a_2"}, {"id": "reactflow__edge-55c91ff2-64e5-4ec4-9601-292a835b1b7d55c91ff2-64e5-4ec4-9601-292a835b1b7d@@OutPut-385fc6e4-2478-4ad2-8749-80d41c819d21385fc6e4-2478-4ad2-8749-80d41c819d21@@Text", "source": "55c91ff2-64e5-4ec4-9601-292a835b1b7d", "source_param": "55c91ff2-64e5-4ec4-9601-292a835b1b7d@@OutPut", "target": "385fc6e4-2478-4ad2-8749-80d41c819d21", "target_param": "385fc6e4-2478-4ad2-8749-80d41c819d21@@Text"}, {"id": "reactflow__edge-385fc6e4-2478-4ad2-8749-80d41c819d21385fc6e4-2478-4ad2-8749-80d41c819d21@@OutPut-39f811e7-e78c-4398-9ee1-dcc594deb82d39f811e7-e78c-4398-9ee1-dcc594deb82d@@a_1", "source": "385fc6e4-2478-4ad2-8749-80d41c819d21", "source_param": "385fc6e4-2478-4ad2-8749-80d41c819d21@@OutPut", "target": "39f811e7-e78c-4398-9ee1-dcc594deb82d", "target_param": "39f811e7-e78c-4398-9ee1-dcc594deb82d@@a_1"}, {"id": "reactflow__edge-39f811e7-e78c-4398-9ee1-dcc594deb82d39f811e7-e78c-4398-9ee1-dcc594deb82d@@OutPut-31f4d73b-63f1-45db-81bd-1f2c05403fe331f4d73b-63f1-45db-81bd-1f2c05403fe3@@Input", "source": "39f811e7-e78c-4398-9ee1-dcc594deb82d", "source_param": "39f811e7-e78c-4398-9ee1-dcc594deb82d@@OutPut", "target": "31f4d73b-63f1-45db-81bd-1f2c05403fe3", "target_param": "31f4d73b-63f1-45db-81bd-1f2c05403fe3@@Input"}, {"id": "reactflow__edge-ab735bd0-6fc1-4e8f-a47e-72e9e81f74eaab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@OutPutIF-89633fc9-fe33-4713-a7d5-84df7aa37ebb89633fc9-fe33-4713-a7d5-84df7aa37ebb@@Text", "source": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea", "source_param": "ab735bd0-6fc1-4e8f-a47e-72e9e81f74ea@@OutPutIF", "target": "89633fc9-fe33-4713-a7d5-84df7aa37ebb", "target_param": "89633fc9-fe33-4713-a7d5-84df7aa37ebb@@Text"}, {"id": "reactflow__edge-89633fc9-fe33-4713-a7d5-84df7aa37ebb89633fc9-fe33-4713-a7d5-84df7aa37ebb@@OutPut-1af3fafb-2a2e-4f5b-b490-ef752b4743c11af3fafb-2a2e-4f5b-b490-ef752b4743c1@@b_1", "source": "89633fc9-fe33-4713-a7d5-84df7aa37ebb", "source_param": "89633fc9-fe33-4713-a7d5-84df7aa37ebb@@OutPut", "target": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1", "target_param": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1@@b_1"}, {"id": "reactflow__edge-89633fc9-fe33-4713-a7d5-84df7aa37ebb89633fc9-fe33-4713-a7d5-84df7aa37ebb@@OutPut-8190674d-1ff5-4354-af51-617b7a6804ac8190674d-1ff5-4354-af51-617b7a6804ac@@z_1", "source": "89633fc9-fe33-4713-a7d5-84df7aa37ebb", "source_param": "89633fc9-fe33-4713-a7d5-84df7aa37ebb@@OutPut", "target": "8190674d-1ff5-4354-af51-617b7a6804ac", "target_param": "8190674d-1ff5-4354-af51-617b7a6804ac@@z_1"}, {"id": "reactflow__edge-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut-8190674d-1ff5-4354-af51-617b7a6804ac8190674d-1ff5-4354-af51-617b7a6804ac@@code", "source": "4d34cde3-07de-4e30-9e34-92e2b748463d", "source_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut", "target": "8190674d-1ff5-4354-af51-617b7a6804ac", "target_param": "8190674d-1ff5-4354-af51-617b7a6804ac@@code"}, {"id": "reactflow__edge-8190674d-1ff5-4354-af51-617b7a6804ac8190674d-1ff5-4354-af51-617b7a6804ac@@OutPut-18a68135-1870-4b88-be34-db8dbfb620ce18a68135-1870-4b88-be34-db8dbfb620ce@@Content", "source": "8190674d-1ff5-4354-af51-617b7a6804ac", "source_param": "8190674d-1ff5-4354-af51-617b7a6804ac@@OutPut", "target": "18a68135-1870-4b88-be34-db8dbfb620ce", "target_param": "18a68135-1870-4b88-be34-db8dbfb620ce@@Content"}, {"id": "reactflow__edge-39956471-733c-43f2-89a9-dece7d359dd539956471-733c-43f2-89a9-dece7d359dd5@@OutPut-1af3fafb-2a2e-4f5b-b490-ef752b4743c11af3fafb-2a2e-4f5b-b490-ef752b4743c1@@b_2", "source": "39956471-733c-43f2-89a9-dece7d359dd5", "source_param": "39956471-733c-43f2-89a9-dece7d359dd5@@OutPut", "target": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1", "target_param": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1@@b_2"}, {"id": "reactflow__edge-1af3fafb-2a2e-4f5b-b490-ef752b4743c11af3fafb-2a2e-4f5b-b490-ef752b4743c1@@OutPut-af3f9fd6-1579-40a4-911d-bcefc71f8002af3f9fd6-1579-40a4-911d-bcefc71f8002@@Input", "source": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1", "source_param": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1@@OutPut", "target": "af3f9fd6-1579-40a4-911d-bcefc71f8002", "target_param": "af3f9fd6-1579-40a4-911d-bcefc71f8002@@Input"}, {"id": "reactflow__edge-0a18a88c-e84e-48ea-b4fc-4470db3b84870a18a88c-e84e-48ea-b4fc-4470db3b8487@@OutPut-1af3fafb-2a2e-4f5b-b490-ef752b4743c11af3fafb-2a2e-4f5b-b490-ef752b4743c1@@a_123", "source": "0a18a88c-e84e-48ea-b4fc-4470db3b8487", "source_param": "0a18a88c-e84e-48ea-b4fc-4470db3b8487@@OutPut", "target": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1", "target_param": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1@@a_123"}, {"id": "reactflow__edge-8bf06d03-c289-42dc-b376-50ca881d64168bf06d03-c289-42dc-b376-50ca881d6416@@OutPut-56daca49-c131-4518-8360-217cdfd2588b56daca49-c131-4518-8360-217cdfd2588b@@b_1", "source": "8bf06d03-c289-42dc-b376-50ca881d6416", "source_param": "8bf06d03-c289-42dc-b376-50ca881d6416@@OutPut", "target": "56daca49-c131-4518-8360-217cdfd2588b", "target_param": "56daca49-c131-4518-8360-217cdfd2588b@@b_1"}, {"id": "reactflow__edge-8bf06d03-c289-42dc-b376-50ca881d64168bf06d03-c289-42dc-b376-50ca881d6416@@OutPut-2f06b87b-113d-4528-9d68-3d7ddecedc772f06b87b-113d-4528-9d68-3d7ddecedc77@@Text", "source": "8bf06d03-c289-42dc-b376-50ca881d6416", "source_param": "8bf06d03-c289-42dc-b376-50ca881d6416@@OutPut", "target": "2f06b87b-113d-4528-9d68-3d7ddecedc77", "target_param": "2f06b87b-113d-4528-9d68-3d7ddecedc77@@Text"}, {"id": "reactflow__edge-af3f9fd6-1579-40a4-911d-bcefc71f8002af3f9fd6-1579-40a4-911d-bcefc71f8002@@OutPut-efe053f2-af02-423d-9690-858c2030778befe053f2-af02-423d-9690-858c2030778b@@Input", "source": "af3f9fd6-1579-40a4-911d-bcefc71f8002", "source_param": "af3f9fd6-1579-40a4-911d-bcefc71f8002@@OutPut", "target": "efe053f2-af02-423d-9690-858c2030778b", "target_param": "efe053f2-af02-423d-9690-858c2030778b@@Input"}, {"id": "reactflow__edge-efe053f2-af02-423d-9690-858c2030778befe053f2-af02-423d-9690-858c2030778b@@OutPutElse-a2d33c8e-b4b7-4f52-9304-837d4b9e7569a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@Content", "source": "efe053f2-af02-423d-9690-858c2030778b", "source_param": "efe053f2-af02-423d-9690-858c2030778b@@OutPutElse", "target": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569", "target_param": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@Content"}, {"id": "reactflow__edge-a2d33c8e-b4b7-4f52-9304-837d4b9e7569a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@OutPut-43f8c1a7-9050-40a6-8ecb-7c8081824b1343f8c1a7-9050-40a6-8ecb-7c8081824b13@@Text", "source": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569", "source_param": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@OutPut", "target": "43f8c1a7-9050-40a6-8ecb-7c8081824b13", "target_param": "43f8c1a7-9050-40a6-8ecb-7c8081824b13@@Text"}, {"id": "reactflow__edge-a2d33c8e-b4b7-4f52-9304-837d4b9e7569a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@OutPut-655a62c9-5d71-4131-91fe-bc6ea1904123655a62c9-5d71-4131-91fe-bc6ea1904123@@c_1", "source": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569", "source_param": "a2d33c8e-b4b7-4f52-9304-837d4b9e7569@@OutPut", "target": "655a62c9-5d71-4131-91fe-bc6ea1904123", "target_param": "655a62c9-5d71-4131-91fe-bc6ea1904123@@c_1"}, {"id": "reactflow__edge-43f8c1a7-9050-40a6-8ecb-7c8081824b1343f8c1a7-9050-40a6-8ecb-7c8081824b13@@OutPut-655a62c9-5d71-4131-91fe-bc6ea1904123655a62c9-5d71-4131-91fe-bc6ea1904123@@c_2", "source": "43f8c1a7-9050-40a6-8ecb-7c8081824b13", "source_param": "43f8c1a7-9050-40a6-8ecb-7c8081824b13@@OutPut", "target": "655a62c9-5d71-4131-91fe-bc6ea1904123", "target_param": "655a62c9-5d71-4131-91fe-bc6ea1904123@@c_2"}, {"id": "reactflow__edge-655a62c9-5d71-4131-91fe-bc6ea1904123655a62c9-5d71-4131-91fe-bc6ea1904123@@OutPut-6c520d62-4786-4e41-9bcd-dbcc76f55c5f6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@Input", "source": "655a62c9-5d71-4131-91fe-bc6ea1904123", "source_param": "655a62c9-5d71-4131-91fe-bc6ea1904123@@OutPut", "target": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f", "target_param": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@Input"}, {"id": "reactflow__edge-2f06b87b-113d-4528-9d68-3d7ddecedc772f06b87b-113d-4528-9d68-3d7ddecedc77@@OutPut-56daca49-c131-4518-8360-217cdfd2588b56daca49-c131-4518-8360-217cdfd2588b@@a_2", "source": "2f06b87b-113d-4528-9d68-3d7ddecedc77", "source_param": "2f06b87b-113d-4528-9d68-3d7ddecedc77@@OutPut", "target": "56daca49-c131-4518-8360-217cdfd2588b", "target_param": "56daca49-c131-4518-8360-217cdfd2588b@@a_2"}, {"id": "reactflow__edge-56daca49-c131-4518-8360-217cdfd2588b56daca49-c131-4518-8360-217cdfd2588b@@OutPut-3318e9db-da3d-4b5d-9d71-35970b29e0073318e9db-da3d-4b5d-9d71-35970b29e007@@a_123", "source": "56daca49-c131-4518-8360-217cdfd2588b", "source_param": "56daca49-c131-4518-8360-217cdfd2588b@@OutPut", "target": "3318e9db-da3d-4b5d-9d71-35970b29e007", "target_param": "3318e9db-da3d-4b5d-9d71-35970b29e007@@a_123"}, {"id": "reactflow__edge-3318e9db-da3d-4b5d-9d71-35970b29e0073318e9db-da3d-4b5d-9d71-35970b29e007@@OutPut-af3f9fd6-1579-40a4-911d-bcefc71f8002af3f9fd6-1579-40a4-911d-bcefc71f8002@@Input", "source": "3318e9db-da3d-4b5d-9d71-35970b29e007", "source_param": "3318e9db-da3d-4b5d-9d71-35970b29e007@@OutPut", "target": "af3f9fd6-1579-40a4-911d-bcefc71f8002", "target_param": "af3f9fd6-1579-40a4-911d-bcefc71f8002@@Input"}, {"id": "reactflow__edge-efe053f2-af02-423d-9690-858c2030778befe053f2-af02-423d-9690-858c2030778b@@OutPutIF-918b6376-d6fb-4951-b84c-d69ac4e06193918b6376-d6fb-4951-b84c-d69ac4e06193@@Input", "source": "efe053f2-af02-423d-9690-858c2030778b", "source_param": "efe053f2-af02-423d-9690-858c2030778b@@OutPutIF", "target": "918b6376-d6fb-4951-b84c-d69ac4e06193", "target_param": "918b6376-d6fb-4951-b84c-d69ac4e06193@@Input"}, {"id": "reactflow__edge-2f06b87b-113d-4528-9d68-3d7ddecedc772f06b87b-113d-4528-9d68-3d7ddecedc77@@OutPut-3318e9db-da3d-4b5d-9d71-35970b29e0073318e9db-da3d-4b5d-9d71-35970b29e007@@b_2", "source": "2f06b87b-113d-4528-9d68-3d7ddecedc77", "source_param": "2f06b87b-113d-4528-9d68-3d7ddecedc77@@OutPut", "target": "3318e9db-da3d-4b5d-9d71-35970b29e007", "target_param": "3318e9db-da3d-4b5d-9d71-35970b29e007@@b_2"}, {"id": "reactflow__edge-918b6376-d6fb-4951-b84c-d69ac4e06193918b6376-d6fb-4951-b84c-d69ac4e06193@@OutPutElse-189cc2f5-51c9-4135-bace-9d9ee1800114189cc2f5-51c9-4135-bace-9d9ee1800114@@z_1", "source": "918b6376-d6fb-4951-b84c-d69ac4e06193", "source_param": "918b6376-d6fb-4951-b84c-d69ac4e06193@@OutPutElse", "target": "189cc2f5-51c9-4135-bace-9d9ee1800114", "target_param": "189cc2f5-51c9-4135-bace-9d9ee1800114@@z_1"}, {"id": "reactflow__edge-189cc2f5-51c9-4135-bace-9d9ee1800114189cc2f5-51c9-4135-bace-9d9ee1800114@@OutPut-fc332801-9356-4a79-9a0f-3d34d6c334bdfc332801-9356-4a79-9a0f-3d34d6c334bd@@Content", "source": "189cc2f5-51c9-4135-bace-9d9ee1800114", "source_param": "189cc2f5-51c9-4135-bace-9d9ee1800114@@OutPut", "target": "fc332801-9356-4a79-9a0f-3d34d6c334bd", "target_param": "fc332801-9356-4a79-9a0f-3d34d6c334bd@@Content"}, {"id": "reactflow__edge-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut-189cc2f5-51c9-4135-bace-9d9ee1800114189cc2f5-51c9-4135-bace-9d9ee1800114@@code", "source": "4d34cde3-07de-4e30-9e34-92e2b748463d", "source_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut", "target": "189cc2f5-51c9-4135-bace-9d9ee1800114", "target_param": "189cc2f5-51c9-4135-bace-9d9ee1800114@@code"}, {"id": "reactflow__edge-918b6376-d6fb-4951-b84c-d69ac4e06193918b6376-d6fb-4951-b84c-d69ac4e06193@@OutPutIF-1745bf80-bda2-4f98-89e9-fc6aafcab5f91745bf80-bda2-4f98-89e9-fc6aafcab5f9@@Content", "source": "918b6376-d6fb-4951-b84c-d69ac4e06193", "source_param": "918b6376-d6fb-4951-b84c-d69ac4e06193@@OutPutIF", "target": "1745bf80-bda2-4f98-89e9-fc6aafcab5f9", "target_param": "1745bf80-bda2-4f98-89e9-fc6aafcab5f9@@Content"}, {"id": "reactflow__edge-29ffeb47-a126-4afe-8df8-13b677fda54629ffeb47-a126-4afe-8df8-13b677fda546@@OutPut-fe7d4607-0440-4256-a57f-8aadc870056cfe7d4607-0440-4256-a57f-8aadc870056c@@Input", "source": "29ffeb47-a126-4afe-8df8-13b677fda546", "source_param": "29ffeb47-a126-4afe-8df8-13b677fda546@@OutPut", "target": "fe7d4607-0440-4256-a57f-8aadc870056c", "target_param": "fe7d4607-0440-4256-a57f-8aadc870056c@@Input"}, {"id": "reactflow__edge-fe7d4607-0440-4256-a57f-8aadc870056cfe7d4607-0440-4256-a57f-8aadc870056c@@OutPutElse-929a6f82-a1e0-4556-a700-e150f8dac19a929a6f82-a1e0-4556-a700-e150f8dac19a@@Text", "source": "fe7d4607-0440-4256-a57f-8aadc870056c", "source_param": "fe7d4607-0440-4256-a57f-8aadc870056c@@OutPutElse", "target": "929a6f82-a1e0-4556-a700-e150f8dac19a", "target_param": "929a6f82-a1e0-4556-a700-e150f8dac19a@@Text"}, {"id": "reactflow__edge-fe7d4607-0440-4256-a57f-8aadc870056cfe7d4607-0440-4256-a57f-8aadc870056c@@OutPutIF-cf9b9da8-af94-4e9d-a4eb-3d06012579a7cf9b9da8-af94-4e9d-a4eb-3d06012579a7@@Content", "source": "fe7d4607-0440-4256-a57f-8aadc870056c", "source_param": "fe7d4607-0440-4256-a57f-8aadc870056c@@OutPutIF", "target": "cf9b9da8-af94-4e9d-a4eb-3d06012579a7", "target_param": "cf9b9da8-af94-4e9d-a4eb-3d06012579a7@@Content"}, {"id": "reactflow__edge-cf9b9da8-af94-4e9d-a4eb-3d06012579a7cf9b9da8-af94-4e9d-a4eb-3d06012579a7@@OutPut-1c1aace9-b6a5-4ff7-9c38-915de03c88fd1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@Input", "source": "cf9b9da8-af94-4e9d-a4eb-3d06012579a7", "source_param": "cf9b9da8-af94-4e9d-a4eb-3d06012579a7@@OutPut", "target": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd", "target_param": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@Input"}, {"id": "reactflow__edge-929a6f82-a1e0-4556-a700-e150f8dac19a929a6f82-a1e0-4556-a700-e150f8dac19a@@OutPut-1c1aace9-b6a5-4ff7-9c38-915de03c88fd1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@Input", "source": "929a6f82-a1e0-4556-a700-e150f8dac19a", "source_param": "929a6f82-a1e0-4556-a700-e150f8dac19a@@OutPut", "target": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd", "target_param": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@Input"}, {"id": "reactflow__edge-1745bf80-bda2-4f98-89e9-fc6aafcab5f91745bf80-bda2-4f98-89e9-fc6aafcab5f9@@OutPut-f1ca9780-e59a-41cf-9c18-207effc6f582f1ca9780-e59a-41cf-9c18-207effc6f582@@Input", "source": "1745bf80-bda2-4f98-89e9-fc6aafcab5f9", "source_param": "1745bf80-bda2-4f98-89e9-fc6aafcab5f9@@OutPut", "target": "f1ca9780-e59a-41cf-9c18-207effc6f582", "target_param": "f1ca9780-e59a-41cf-9c18-207effc6f582@@Input"}, {"id": "reactflow__edge-1c1aace9-b6a5-4ff7-9c38-915de03c88fd1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@OutPut-f1ca9780-e59a-41cf-9c18-207effc6f582f1ca9780-e59a-41cf-9c18-207effc6f582@@Input", "source": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd", "source_param": "1c1aace9-b6a5-4ff7-9c38-915de03c88fd@@OutPut", "target": "f1ca9780-e59a-41cf-9c18-207effc6f582", "target_param": "f1ca9780-e59a-41cf-9c18-207effc6f582@@Input"}, {"id": "reactflow__edge-1af3fafb-2a2e-4f5b-b490-ef752b4743c11af3fafb-2a2e-4f5b-b490-ef752b4743c1@@OutPut-5bc1e3c1-0473-43df-89b1-bb86900ea0325bc1e3c1-0473-43df-89b1-bb86900ea032@@b_123", "source": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1", "source_param": "1af3fafb-2a2e-4f5b-b490-ef752b4743c1@@OutPut", "target": "5bc1e3c1-0473-43df-89b1-bb86900ea032", "target_param": "5bc1e3c1-0473-43df-89b1-bb86900ea032@@b_123"}, {"id": "reactflow__edge-f1ca9780-e59a-41cf-9c18-207effc6f582f1ca9780-e59a-41cf-9c18-207effc6f582@@OutPut-5bc1e3c1-0473-43df-89b1-bb86900ea0325bc1e3c1-0473-43df-89b1-bb86900ea032@@c_1", "source": "f1ca9780-e59a-41cf-9c18-207effc6f582", "source_param": "f1ca9780-e59a-41cf-9c18-207effc6f582@@OutPut", "target": "5bc1e3c1-0473-43df-89b1-bb86900ea032", "target_param": "5bc1e3c1-0473-43df-89b1-bb86900ea032@@c_1"}, {"id": "reactflow__edge-5bc1e3c1-0473-43df-89b1-bb86900ea0325bc1e3c1-0473-43df-89b1-bb86900ea032@@OutPut-6c520d62-4786-4e41-9bcd-dbcc76f55c5f6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@Input", "source": "5bc1e3c1-0473-43df-89b1-bb86900ea032", "source_param": "5bc1e3c1-0473-43df-89b1-bb86900ea032@@OutPut", "target": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f", "target_param": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@Input"}, {"id": "reactflow__edge-f1ca9780-e59a-41cf-9c18-207effc6f582f1ca9780-e59a-41cf-9c18-207effc6f582@@OutPut-6cc39b4e-4fd4-4ec1-9114-daa9eeafa3326cc39b4e-4fd4-4ec1-9114-daa9eeafa332@@Text", "source": "f1ca9780-e59a-41cf-9c18-207effc6f582", "source_param": "f1ca9780-e59a-41cf-9c18-207effc6f582@@OutPut", "target": "6cc39b4e-4fd4-4ec1-9114-daa9eeafa332", "target_param": "6cc39b4e-4fd4-4ec1-9114-daa9eeafa332@@Text"}, {"id": "reactflow__edge-6cc39b4e-4fd4-4ec1-9114-daa9eeafa3326cc39b4e-4fd4-4ec1-9114-daa9eeafa332@@OutPut-5bc1e3c1-0473-43df-89b1-bb86900ea0325bc1e3c1-0473-43df-89b1-bb86900ea032@@c_2", "source": "6cc39b4e-4fd4-4ec1-9114-daa9eeafa332", "source_param": "6cc39b4e-4fd4-4ec1-9114-daa9eeafa332@@OutPut", "target": "5bc1e3c1-0473-43df-89b1-bb86900ea032", "target_param": "5bc1e3c1-0473-43df-89b1-bb86900ea032@@c_2"}, {"id": "reactflow__edge-6c520d62-4786-4e41-9bcd-dbcc76f55c5f6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@OutPut-0a48ee9a-9c3e-4bf5-b535-6fb8244680750a48ee9a-9c3e-4bf5-b535-6fb824468075@@Input", "source": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f", "source_param": "6c520d62-4786-4e41-9bcd-dbcc76f55c5f@@OutPut", "target": "0a48ee9a-9c3e-4bf5-b535-6fb824468075", "target_param": "0a48ee9a-9c3e-4bf5-b535-6fb824468075@@Input"}, {"id": "reactflow__edge-0a48ee9a-9c3e-4bf5-b535-6fb8244680750a48ee9a-9c3e-4bf5-b535-6fb824468075@@OutPutIF-2024505a-f464-437b-802d-1c9403657c682024505a-f464-437b-802d-1c9403657c68@@Content", "source": "0a48ee9a-9c3e-4bf5-b535-6fb824468075", "source_param": "0a48ee9a-9c3e-4bf5-b535-6fb824468075@@OutPutIF", "target": "2024505a-f464-437b-802d-1c9403657c68", "target_param": "2024505a-f464-437b-802d-1c9403657c68@@Content"}, {"id": "reactflow__edge-2024505a-f464-437b-802d-1c9403657c682024505a-f464-437b-802d-1c9403657c68@@OutPut-e77d6a3f-2012-494e-b683-7324d08851fce77d6a3f-2012-494e-b683-7324d08851fc@@Text", "source": "2024505a-f464-437b-802d-1c9403657c68", "source_param": "2024505a-f464-437b-802d-1c9403657c68@@OutPut", "target": "e77d6a3f-2012-494e-b683-7324d08851fc", "target_param": "e77d6a3f-2012-494e-b683-7324d08851fc@@Text"}, {"id": "reactflow__edge-e77d6a3f-2012-494e-b683-7324d08851fce77d6a3f-2012-494e-b683-7324d08851fc@@OutPut-b7b157e6-e0d5-41e0-8461-099b00d6e1eab7b157e6-e0d5-41e0-8461-099b00d6e1ea@@d_1", "source": "e77d6a3f-2012-494e-b683-7324d08851fc", "source_param": "e77d6a3f-2012-494e-b683-7324d08851fc@@OutPut", "target": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea", "target_param": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea@@d_1"}, {"id": "reactflow__edge-0a48ee9a-9c3e-4bf5-b535-6fb8244680750a48ee9a-9c3e-4bf5-b535-6fb824468075@@OutPutElse-b279a017-a83d-4bc9-9aa3-dfff533d2bbdb279a017-a83d-4bc9-9aa3-dfff533d2bbd@@Content", "source": "0a48ee9a-9c3e-4bf5-b535-6fb824468075", "source_param": "0a48ee9a-9c3e-4bf5-b535-6fb824468075@@OutPutElse", "target": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd", "target_param": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd@@Content"}, {"id": "reactflow__edge-b279a017-a83d-4bc9-9aa3-dfff533d2bbdb279a017-a83d-4bc9-9aa3-dfff533d2bbd@@OutPut-f9fbe596-df19-455e-aa20-b3accbeeef16f9fbe596-df19-455e-aa20-b3accbeeef16@@d_2", "source": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd", "source_param": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd@@OutPut", "target": "f9fbe596-df19-455e-aa20-b3accbeeef16", "target_param": "f9fbe596-df19-455e-aa20-b3ac<PERSON><PERSON>ef16@@d_2"}, {"id": "reactflow__edge-b279a017-a83d-4bc9-9aa3-dfff533d2bbdb279a017-a83d-4bc9-9aa3-dfff533d2bbd@@OutPut-37e5c39d-2ea5-48de-ade7-a025fc37d18937e5c39d-2ea5-48de-ade7-a025fc37d189@@Text", "source": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd", "source_param": "b279a017-a83d-4bc9-9aa3-dfff533d2bbd@@OutPut", "target": "37e5c39d-2ea5-48de-ade7-a025fc37d189", "target_param": "37e5c39d-2ea5-48de-ade7-a025fc37d189@@Text"}, {"id": "reactflow__edge-37e5c39d-2ea5-48de-ade7-a025fc37d18937e5c39d-2ea5-48de-ade7-a025fc37d189@@OutPut-f9fbe596-df19-455e-aa20-b3accbeeef16f9fbe596-df19-455e-aa20-b3accbeeef16@@d_1", "source": "37e5c39d-2ea5-48de-ade7-a025fc37d189", "source_param": "37e5c39d-2ea5-48de-ade7-a025fc37d189@@OutPut", "target": "f9fbe596-df19-455e-aa20-b3accbeeef16", "target_param": "f9fbe596-df19-455e-aa20-b3ac<PERSON><PERSON>ef16@@d_1"}, {"id": "reactflow__edge-f9fbe596-df19-455e-aa20-b3accbeeef16f9fbe596-df19-455e-aa20-b3acc<PERSON>ef16@@OutPut-87862929-3660-4417-b95e-808b84355c5c87862929-3660-4417-b95e-808b84355c5c@@Input", "source": "f9fbe596-df19-455e-aa20-b3accbeeef16", "source_param": "f9fbe596-df19-455e-aa20-b3acc<PERSON>ef16@@OutPut", "target": "87862929-3660-4417-b95e-808b84355c5c", "target_param": "87862929-3660-4417-b95e-808b84355c5c@@Input"}, {"id": "reactflow__edge-e77d6a3f-2012-494e-b683-7324d08851fce77d6a3f-2012-494e-b683-7324d08851fc@@OutPut-6d8409e4-d5ca-407e-85b2-c96bfc168c0b6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@z_1", "source": "e77d6a3f-2012-494e-b683-7324d08851fc", "source_param": "e77d6a3f-2012-494e-b683-7324d08851fc@@OutPut", "target": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b", "target_param": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@z_1"}, {"id": "reactflow__edge-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut-6d8409e4-d5ca-407e-85b2-c96bfc168c0b6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@code", "source": "4d34cde3-07de-4e30-9e34-92e2b748463d", "source_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@OutPut", "target": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b", "target_param": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@code"}, {"id": "reactflow__edge-6d8409e4-d5ca-407e-85b2-c96bfc168c0b6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@OutPut-e4dfb59a-d042-4525-bc53-a3ac1c9681bee4dfb59a-d042-4525-bc53-a3ac1c9681be@@Content", "source": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b", "source_param": "6d8409e4-d5ca-407e-85b2-c96bfc168c0b@@OutPut", "target": "e4dfb59a-d042-4525-bc53-a3ac1c9681be", "target_param": "e4dfb59a-d042-4525-bc53-a3ac1c9681be@@Content"}, {"id": "reactflow__edge-7165762f-4246-4d90-99c0-7a6f677277f27165762f-4246-4d90-99c0-7a6f677277f2@@OutPut-c05a83ab-0552-4fe9-a61a-f81b52434a1cc05a83ab-0552-4fe9-a61a-f81b52434a1c@@Input", "source": "7165762f-4246-4d90-99c0-7a6f677277f2", "source_param": "7165762f-4246-4d90-99c0-7a6f677277f2@@OutPut", "target": "c05a83ab-0552-4fe9-a61a-f81b52434a1c", "target_param": "c05a83ab-0552-4fe9-a61a-f81b52434a1c@@Input"}, {"id": "reactflow__edge-c05a83ab-0552-4fe9-a61a-f81b52434a1cc05a83ab-0552-4fe9-a61a-f81b52434a1c@@OutPutIF-8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe@@Content", "source": "c05a83ab-0552-4fe9-a61a-f81b52434a1c", "source_param": "c05a83ab-0552-4fe9-a61a-f81b52434a1c@@OutPutIF", "target": "8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe", "target_param": "8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe@@Content"}, {"id": "reactflow__edge-c05a83ab-0552-4fe9-a61a-f81b52434a1cc05a83ab-0552-4fe9-a61a-f81b52434a1c@@OutPutElse-f191a5ec-ea45-4b7f-a137-2255217f151cf191a5ec-ea45-4b7f-a137-2255217f151c@@Text", "source": "c05a83ab-0552-4fe9-a61a-f81b52434a1c", "source_param": "c05a83ab-0552-4fe9-a61a-f81b52434a1c@@OutPutElse", "target": "f191a5ec-ea45-4b7f-a137-2255217f151c", "target_param": "f191a5ec-ea45-4b7f-a137-2255217f151c@@Text"}, {"id": "reactflow__edge-8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe@@OutPut-c4aacc98-865d-4b40-bba8-48889742ba08c4aacc98-865d-4b40-bba8-48889742ba08@@Content", "source": "8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe", "source_param": "8ec03d78-36d7-4bcc-9c00-a2a5b71fc4fe@@OutPut", "target": "c4aacc98-865d-4b40-bba8-48889742ba08", "target_param": "c4aacc98-865d-4b40-bba8-48889742ba08@@Content"}, {"id": "reactflow__edge-f191a5ec-ea45-4b7f-a137-2255217f151cf191a5ec-ea45-4b7f-a137-2255217f151c@@OutPut-fec62a62-9414-4322-a12a-536d9039287cfec62a62-9414-4322-a12a-536d9039287c@@Input", "source": "f191a5ec-ea45-4b7f-a137-2255217f151c", "source_param": "f191a5ec-ea45-4b7f-a137-2255217f151c@@OutPut", "target": "fec62a62-9414-4322-a12a-536d9039287c", "target_param": "fec62a62-9414-4322-a12a-536d9039287c@@Input"}, {"id": "reactflow__edge-fec62a62-9414-4322-a12a-536d9039287cfec62a62-9414-4322-a12a-536d9039287c@@OutPut-b7b157e6-e0d5-41e0-8461-099b00d6e1eab7b157e6-e0d5-41e0-8461-099b00d6e1ea@@d_2", "source": "fec62a62-9414-4322-a12a-536d9039287c", "source_param": "fec62a62-9414-4322-a12a-536d9039287c@@OutPut", "target": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea", "target_param": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea@@d_2"}, {"id": "reactflow__edge-5bc1e3c1-0473-43df-89b1-bb86900ea0325bc1e3c1-0473-43df-89b1-bb86900ea032@@OutPut-b7b157e6-e0d5-41e0-8461-099b00d6e1eab7b157e6-e0d5-41e0-8461-099b00d6e1ea@@c_123", "source": "5bc1e3c1-0473-43df-89b1-bb86900ea032", "source_param": "5bc1e3c1-0473-43df-89b1-bb86900ea032@@OutPut", "target": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea", "target_param": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea@@c_123"}, {"id": "reactflow__edge-b7b157e6-e0d5-41e0-8461-099b00d6e1eab7b157e6-e0d5-41e0-8461-099b00d6e1ea@@OutPut-87862929-3660-4417-b95e-808b84355c5c87862929-3660-4417-b95e-808b84355c5c@@Input", "source": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea", "source_param": "b7b157e6-e0d5-41e0-8461-099b00d6e1ea@@OutPut", "target": "87862929-3660-4417-b95e-808b84355c5c", "target_param": "87862929-3660-4417-b95e-808b84355c5c@@Input"}, {"id": "reactflow__edge-c4aacc98-865d-4b40-bba8-48889742ba08c4aacc98-865d-4b40-bba8-48889742ba08@@OutPut-8524d96b-bc3f-4621-bd54-714cb85ded5e8524d96b-bc3f-4621-bd54-714cb85ded5e@@Text", "source": "c4aacc98-865d-4b40-bba8-48889742ba08", "source_param": "c4aacc98-865d-4b40-bba8-48889742ba08@@OutPut", "target": "8524d96b-bc3f-4621-bd54-714cb85ded5e", "target_param": "8524d96b-bc3f-4621-bd54-714cb85ded5e@@Text"}, {"id": "reactflow__edge-8524d96b-bc3f-4621-bd54-714cb85ded5e8524d96b-bc3f-4621-bd54-714cb85ded5e@@OutPut-fec62a62-9414-4322-a12a-536d9039287cfec62a62-9414-4322-a12a-536d9039287c@@Input", "source": "8524d96b-bc3f-4621-bd54-714cb85ded5e", "source_param": "8524d96b-bc3f-4621-bd54-714cb85ded5e@@OutPut", "target": "fec62a62-9414-4322-a12a-536d9039287c", "target_param": "fec62a62-9414-4322-a12a-536d9039287c@@Input"}, {"id": "reactflow__edge-87862929-3660-4417-b95e-808b84355c5c87862929-3660-4417-b95e-808b84355c5c@@OutPut-b864e419-e3d3-4148-ba3d-d7ad3093fbb2b864e419-e3d3-4148-ba3d-d7ad3093fbb2@@Content", "source": "87862929-3660-4417-b95e-808b84355c5c", "source_param": "87862929-3660-4417-b95e-808b84355c5c@@OutPut", "target": "b864e419-e3d3-4148-ba3d-d7ad3093fbb2", "target_param": "b864e419-e3d3-4148-ba3d-d7ad3093fbb2@@Content"}, {"id": "reactflow__edge-b864e419-e3d3-4148-ba3d-d7ad3093fbb2b864e419-e3d3-4148-ba3d-d7ad3093fbb2@@OutPut-8749b1dd-29d3-4c8b-a933-67865d477d988749b1dd-29d3-4c8b-a933-67865d477d98@@Input", "source": "b864e419-e3d3-4148-ba3d-d7ad3093fbb2", "source_param": "b864e419-e3d3-4148-ba3d-d7ad3093fbb2@@OutPut", "target": "8749b1dd-29d3-4c8b-a933-67865d477d98", "target_param": "8749b1dd-29d3-4c8b-a933-67865d477d98@@Input"}, {"id": "reactflow__edge-d853cc67-6868-43ed-a925-9f765d9e162bd853cc67-6868-43ed-a925-9f765d9e162b@@OutPut-9da11bee-f748-4f7b-96d4-37f4e7a2c34d9da11bee-f748-4f7b-96d4-37f4e7a2c34d@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "d853cc67-6868-43ed-a925-9f765d9e162b", "source_param": "d853cc67-6868-43ed-a925-9f765d9e162b@@OutPut", "target": "9da11bee-f748-4f7b-96d4-37f4e7a2c34d", "target_param": "9da11bee-f748-4f7b-96d4-37f4e7a2c34d@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-9da11bee-f748-4f7b-96d4-37f4e7a2c34d9da11bee-f748-4f7b-96d4-37f4e7a2c34d@@OutPut-4d34cde3-07de-4e30-9e34-92e2b748463d4d34cde3-07de-4e30-9e34-92e2b748463d@@Content", "source": "9da11bee-f748-4f7b-96d4-37f4e7a2c34d", "source_param": "9da11bee-f748-4f7b-96d4-37f4e7a2c34d@@OutPut", "target": "4d34cde3-07de-4e30-9e34-92e2b748463d", "target_param": "4d34cde3-07de-4e30-9e34-92e2b748463d@@Content"}, {"id": "reactflow__edge-9b7a3087-00e9-4216-946f-852911ad388a9b7a3087-00e9-4216-946f-852911ad388a@@OutPut-d59f2c60-0221-4353-b5ab-72d14ff9d0b4d59f2c60-0221-4353-b5ab-72d14ff9d0b4@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "9b7a3087-00e9-4216-946f-852911ad388a", "source_param": "9b7a3087-00e9-4216-946f-852911ad388a@@OutPut", "target": "d59f2c60-0221-4353-b5ab-72d14ff9d0b4", "target_param": "d59f2c60-0221-4353-b5ab-72d14ff9d0b4@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-d59f2c60-0221-4353-b5ab-72d14ff9d0b4d59f2c60-0221-4353-b5ab-72d14ff9d0b4@@OutPut-f126aa3b-5470-4460-ac32-ace543408735f126aa3b-5470-4460-ac32-ace543408735@@Content", "source": "d59f2c60-0221-4353-b5ab-72d14ff9d0b4", "source_param": "d59f2c60-0221-4353-b5ab-72d14ff9d0b4@@OutPut", "target": "f126aa3b-5470-4460-ac32-ace543408735", "target_param": "f126aa3b-5470-4460-ac32-ace543408735@@Content"}, {"id": "reactflow__edge-2ab8f244-0f24-4373-8bcb-2c4cb40588392ab8f244-0f24-4373-8bcb-2c4cb4058839@@OutPut-1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "2ab8f244-0f24-4373-8bcb-2c4cb4058839", "source_param": "2ab8f244-0f24-4373-8bcb-2c4cb4058839@@OutPut", "target": "1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc", "target_param": "1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc@@OutPut-a98f5704-aadd-436f-ae88-dd1fcdb30ed0a98f5704-aadd-436f-ae88-dd1fcdb30ed0@@Content", "source": "1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc", "source_param": "1dc61d1a-8e64-4e21-bb39-c806b4a2c3dc@@OutPut", "target": "a98f5704-aadd-436f-ae88-dd1fcdb30ed0", "target_param": "a98f5704-aadd-436f-ae88-dd1fcdb30ed0@@Content"}, {"id": "reactflow__edge-18a68135-1870-4b88-be34-db8dbfb620ce18a68135-1870-4b88-be34-db8dbfb620ce@@OutPut-b44bf799-73e5-4cd1-8678-a78ef4f0d691b44bf799-73e5-4cd1-8678-a78ef4f0d691@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "18a68135-1870-4b88-be34-db8dbfb620ce", "source_param": "18a68135-1870-4b88-be34-db8dbfb620ce@@OutPut", "target": "b44bf799-73e5-4cd1-8678-a78ef4f0d691", "target_param": "b44bf799-73e5-4cd1-8678-a78ef4f0d691@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-b44bf799-73e5-4cd1-8678-a78ef4f0d691b44bf799-73e5-4cd1-8678-a78ef4f0d691@@OutPut-39956471-733c-43f2-89a9-dece7d359dd539956471-733c-43f2-89a9-dece7d359dd5@@Content", "source": "b44bf799-73e5-4cd1-8678-a78ef4f0d691", "source_param": "b44bf799-73e5-4cd1-8678-a78ef4f0d691@@OutPut", "target": "39956471-733c-43f2-89a9-dece7d359dd5", "target_param": "39956471-733c-43f2-89a9-dece7d359dd5@@Content"}, {"id": "reactflow__edge-fc332801-9356-4a79-9a0f-3d34d6c334bdfc332801-9356-4a79-9a0f-3d34d6c334bd@@OutPut-a078bf82-66c4-46af-ab9c-117a3bc3334fa078bf82-66c4-46af-ab9c-117a3bc3334f@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "fc332801-9356-4a79-9a0f-3d34d6c334bd", "source_param": "fc332801-9356-4a79-9a0f-3d34d6c334bd@@OutPut", "target": "a078bf82-66c4-46af-ab9c-117a3bc3334f", "target_param": "a078bf82-66c4-46af-ab9c-117a3bc3334f@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-a078bf82-66c4-46af-ab9c-117a3bc3334fa078bf82-66c4-46af-ab9c-117a3bc3334f@@OutPut-29ffeb47-a126-4afe-8df8-13b677fda54629ffeb47-a126-4afe-8df8-13b677fda546@@Content", "source": "a078bf82-66c4-46af-ab9c-117a3bc3334f", "source_param": "a078bf82-66c4-46af-ab9c-117a3bc3334f@@OutPut", "target": "29ffeb47-a126-4afe-8df8-13b677fda546", "target_param": "29ffeb47-a126-4afe-8df8-13b677fda546@@Content"}, {"id": "reactflow__edge-e4dfb59a-d042-4525-bc53-a3ac1c9681bee4dfb59a-d042-4525-bc53-a3ac1c9681be@@OutPut-0a742e03-4df7-4384-841f-a4d3798d38b00a742e03-4df7-4384-841f-a4d3798d38b0@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput", "source": "e4dfb59a-d042-4525-bc53-a3ac1c9681be", "source_param": "e4dfb59a-d042-4525-bc53-a3ac1c9681be@@OutPut", "target": "0a742e03-4df7-4384-841f-a4d3798d38b0", "target_param": "0a742e03-4df7-4384-841f-a4d3798d38b0@@d5532975-4459-416a-95ca-a7153192e9d9##JsonInput"}, {"id": "reactflow__edge-0a742e03-4df7-4384-841f-a4d3798d38b00a742e03-4df7-4384-841f-a4d3798d38b0@@OutPut-7165762f-4246-4d90-99c0-7a6f677277f27165762f-4246-4d90-99c0-7a6f677277f2@@Content", "source": "0a742e03-4df7-4384-841f-a4d3798d38b0", "source_param": "0a742e03-4df7-4384-841f-a4d3798d38b0@@OutPut", "target": "7165762f-4246-4d90-99c0-7a6f677277f2", "target_param": "7165762f-4246-4d90-99c0-7a6f677277f2@@Content"}], "viewport": {"x": -2700.1656510242474, "y": 388.13070997362536, "zoom": 0.15084400787657692}}, "created_type": "Applet-Chain", "usage_type": "Common", "create_time": 1739788140000, "enable_access": true, "problem_gen_info": null}]