package models

import "transwarp.io/aip/llmops-common/pb"

const (
	TableNameDocElements = "doc_elements"
)

type DocElement struct {
	Id          string                 `json:"element_id,omitempty" gorm:"column:id;primaryKey;type:varchar(191)"`
	Type        pb.DocElementType      `json:"type,omitempty" gorm:"column:ele_type"`
	Text        string                 `json:"text,omitempty" gorm:"column:text"`
	Metadata    *pb.DocElementMetadata `json:"metadata,omitempty" gorm:"column:metadata;serializer:json"`
	ParentIds   []string               `json:"parent_ids,omitempty" gorm:"column:parent_ids;serializer:json" description:"父元素id列表，自顶向底排序"`
	ParentTexts []string               `json:"parent_texts,omitempty" gorm:"column:parent_texts;serializer:json" description:"父元素内容列表，自顶向底排序"`
	Extra       map[string]string      `json:"extra,omitempty" gorm:"column:extra;serializer:json"`
}

func (*DocElement) TableName() string {
	return TableNameDocElements
}

func (d *DocElement) ToPb() *pb.DocElement {
	return &pb.DocElement{
		ElementId:   d.Id,
		Type:        d.Type,
		Text:        d.Text,
		Metadata:    d.Metadata,
		ParentIds:   d.ParentIds,
		ParentTexts: d.ParentTexts,
		Extra:       d.Extra,
	}
}

func FromDocElementPb(protoDocElement *pb.DocElement) *DocElement {
	return &DocElement{
		Id:          protoDocElement.ElementId,
		Type:        protoDocElement.Type,
		Text:        protoDocElement.Text,
		Metadata:    protoDocElement.Metadata,
		ParentIds:   protoDocElement.ParentIds,
		ParentTexts: protoDocElement.ParentTexts,
		Extra:       protoDocElement.Extra,
	}
}

func FromDocElementPbs(data []*pb.DocElement) []*DocElement {
	ret := make([]*DocElement, 0, len(data))
	for _, item := range data {
		ret = append(ret, FromDocElementPb(item))
	}
	return ret
}

func ToDocElementPbs(data []*DocElement) []*pb.DocElement {
	ret := make([]*pb.DocElement, 0, len(data))
	for _, item := range data {
		ret = append(ret, item.ToPb())
	}
	return ret
}
