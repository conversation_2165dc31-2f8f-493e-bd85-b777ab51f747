package api_tools

import (
	"context"
	"strings"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

// APIToolCollectionDO 工具集完整定义
type APIToolCollectionDO struct {
	BaseInfo            APIToolCollectionBaseDO                      `json:"base_info" description:"基础信息"`                             //基础信息
	Tools               []*APIToolDO                                 `json:"api_tools" description:"工具列表"`                             // 工具列表
	MetaType            APIToolMetaType                              `json:"meta_type" description:"工具原始文件类型：json/yaml"`               //原始数据类型
	MetaInfo            []byte                                       `json:"meta_info" description:"原始文件信息"`                           //原始信息
	Headers             map[string]string                            `json:"headers" description:"请求头"`                                //header
	BaseURL             string                                       `json:"base_url" description:"请求域名"`                              //请求域名
	Params              []*agent_definition.MCPParam                 `json:"params" description:"mcp参数"`                               //当类型是MCPserver
	AgentToolCollection *agent_definition.APIToolCollectionDescriber `json:"agent_tool_collection" description:"智能体需要的元信息（冗余），创建时可不传"` //智能体需要的元信息
	ProxyInfo           *agent_definition.ProxyInfo                  `json:"proxy"`
}

type APIToolReference struct {
	Type          ReferenceType `json:"type"`
	ReferenceMeta string        `json:"reference_meta"`
	ReferenceID   string        `json:"reference_key"`
}

type ReferenceType string

const (
	ReferenceTypeAgent = "chain_agent"
)

// APIToolCollectionBaseDO 工具集基础信息，列表页面展示
type APIToolCollectionBaseDO struct {
	ID                 string                      `json:"id" description:"数据库ID,创建时不传" json:"id,omitempty"`                                  //数据库ID
	Name               string                      `json:"name" description:"工具集名字" json:"name,omitempty"`                                    // 工具集名字
	Creator            string                      `json:"creator" description:"创建人,创建时不传" json:"creator,omitempty"`                          //创建人
	ProjectID          string                      `json:"project_id" description:"project_id,创建时不传" json:"project_id,omitempty"`             //project id
	Desc               string                      `json:"desc" description:"工具集描述" json:"desc,omitempty"`                                    // 工具集描述
	APIToolCnt         int64                       `json:"api_tool_cnt" description:"工具数量,创建时不传" json:"api_tool_cnt,omitempty"`               // 工具数量
	Released           *bool                       `json:"released" description:"是否发布" json:"released,omitempty"`                             //发布状态
	LastReleaseTimeSec int64                       `json:"last_release_time_sec" description:"发布时间" json:"last_release_time_sec,omitempty"`   //发布时间
	Type               APIToolType                 `json:"type" description:"类型，common/template/builtin,创建时不传" json:"type,omitempty"`         //发布状态
	ServerType         agent_definition.ServerType `json:"server_type" description:"服务器类型，rest/mcp/dynamic_mcp" json:"server_type,omitempty"` //插件类型
	References         []APIToolReference          `json:"references" description:"被引用的信息" json:"references,omitempty"`                       //被引用的信息
	CreateTimeSec      int64                       `json:"create_time_sec" description:"创建时间,创建时不传" json:"create_time_sec,omitempty"`         //创建时间
	UpdateTimeSec      int64                       `json:"update_time_sec" description:"更新时间,创建时不传" json:"update_time_sec,omitempty"`         //更新时间
	LogoUrl            string                      `json:"logo_url" json:"logo_url,omitempty"`
	McpType            agent_definition.McpType    `json:"mcp_type" description:"mcp类型, sse/streamableHttp" json:"mcp_type,omitempty"`
}

// APIToolDO 工具信息
type APIToolDO struct {
	ID          string                          `json:"id" description:"数据库ID,创建时不传"`                      //数据库ID
	Name        string                          `json:"name" description:"工具名字"`                           //工具名字
	ProjectID   string                          `json:"project_id" description:"project_id,创建时不传"`         //project id
	Alias       string                          `json:"alias" description:"工具别名"`                          //工具别名，api for human
	Desc        string                          `json:"desc" description:"工具描述"`                           //工具描述
	Method      string                          `json:"method" description:"请求类型"`                         //请求类型
	Path        string                          `json:"path" description:"请求路径"`                           //请求路径
	TestState   APIToolTestState                `json:"test_state" description:"测试状态:init/success/failed"` //测试状态
	ParamValues []agent_definition.APIToolParam `json:"param_values" description:"param参数值"`               //param参数值
}

type APIToolCollectionDemoDO struct {
	Name      string          `json:"name" description:"名字"`
	ProjectID string          `json:"project_id" description:"project_id,创建时不传"` //project id
	Desc      string          `json:"desc" description:"工具描述"`
	Type      APIToolMetaType `json:"type" description:"yaml/json"`
	Meta      []byte          `json:"meta" description:"yaml/json原始信息"`
}

// // APIToolHeader 工具header信息
// type APIToolHeader struct {
//	Key   string `json:"key" description:"header key"`     // header key
//	Value string `json:"value" description:"header value"` //header value
// }

// FIXME import cycle
// func (a *AgentTool) Type() agent_definition.ToolType {
// 	// TODO implement me
// 	panic("implement me")
// }

// func (a *AgentTool) Definition() agent_definition.Tool {
// 	// TODO implement me
// 	panic("implement me")
// }

type APIToolDemoInfo struct {
	Name string `json:"name" description:"名字"`
	APIToolMetaInfo
}

func (a *APIToolCollectionDO) InitForCreate(ctx context.Context) error {
	user, err := helper.GetUser(ctx)
	if err != nil {
		return err
	}
	projectID := helper.GetProjectID(ctx)
	a.BaseInfo.Type = APIToolTypeCommon
	a.BaseInfo.Creator = user
	a.BaseInfo.ProjectID = projectID
	for _, t := range a.Tools {
		if t.TestState == "" {
			t.TestState = APIToolTestStateInit
		}
		t.ProjectID = projectID
	}
	return nil
}

// InitForUpdate 一些属性无法被更新
func (a *APIToolCollectionDO) InitForUpdate(ctx context.Context) {
	a.BaseInfo.Creator = ""
	a.BaseInfo.ProjectID = ""
	a.BaseInfo.LastReleaseTimeSec = 0
	a.BaseInfo.Released = nil
	for _, t := range a.Tools {
		if t.ProjectID == "" {
			t.ProjectID = helper.GetProjectID(ctx)
		}
	}
}

func (a *APIToolCollectionDO) IsValid() error {
	if a.BaseInfo.Name == "" {
		return helper.ToolInvalidErr.Error("tool collection name is empty")
	}
	if a.BaseInfo.Type != APIToolTypeCommon && a.BaseInfo.Type != APIToolTypeTemplate {
		return helper.ToolInvalidErr.Error("tool collection type err,invalid type :%v", a.BaseInfo.Type)
	}
	if a.BaseInfo.ServerType != agent_definition.ServerTypeMCP && a.BaseInfo.ServerType != agent_definition.ServerTypeRest &&
		a.BaseInfo.ServerType != agent_definition.ServerTypeDynamicMCP {
		return helper.ToolInvalidErr.Error("tool collection type err,invalid serverType :%v", a.BaseInfo.ServerType)
	}
	if a.MetaType != APIToolMetaTypeJson && a.MetaType != APIToolMetaTypeYaml && a.BaseInfo.ServerType != agent_definition.ServerTypeMCP &&
		a.BaseInfo.ServerType != agent_definition.ServerTypeDynamicMCP {
		return helper.ToolInvalidErr.Error("tool collection meta type err,invalid type :%v", a.MetaType)
	}
	if a.BaseInfo.ServerType != agent_definition.ServerTypeMCP && a.BaseInfo.ServerType != agent_definition.ServerTypeDynamicMCP {
		for _, t := range a.Tools {
			if err := t.IsValid(); err != nil {
				return err
			}
		}
	}
	return nil
}

func (a *APIToolDO) IsValid() error {
	if a.Name == "" || a.Path == "" || a.Method == "" {
		return helper.ToolInvalidErr.Error("tool invalid :%v", a)
	}
	if a.TestState != APIToolTestStateInit && a.TestState != APIToolTestStateFailed &&
		a.TestState != APIToolTestStateSuccess {
		return helper.ToolInvalidErr.Error("tool test state invalid :%v", a)
	}
	return nil
}

// GetToolByPathAndMethod 通过path和method获取对应的tool，不存在则返回nil
func (a *APIToolCollectionDO) GetToolByPathAndMethod(path string, method string) *APIToolDO {
	for _, t := range a.Tools {
		if t.Path == path && strings.ToUpper(t.Method) == strings.ToUpper(method) {
			return t
		}
	}
	return nil
}

func (a *APIToolCollectionDO) GetToolByPath(path string) *APIToolDO {
	for _, t := range a.Tools {
		if t.Path == path {
			return t
		}
	}
	return nil
}

func (a *APIToolCollectionBaseDO) BuildReference(references []APIToolReference) {
	a.References = references
}

func (a *APIToolCollectionDO) BuildAgentParam() {
	tools := make([]agent_definition.APIToolDescriber, 0)
	for _, t := range a.Tools {
		tools = append(tools, agent_definition.APIToolDescriber{
			ID:                t.ID,
			BaseURL:           a.BaseURL,
			CollectionHeaders: a.Headers,
			Method:            t.Method,
			APIPath:           t.Path,
			Name:              t.Name,
			Desc:              t.Desc,
			Params:            t.ParamValues,
			CollectionName:    a.BaseInfo.Name,
			CollectionId:      a.BaseInfo.ID,
			ServerType:        a.BaseInfo.ServerType,
			McpType:           a.BaseInfo.McpType,
		})
	}
	a.AgentToolCollection = &agent_definition.APIToolCollectionDescriber{
		ID:         a.BaseInfo.ID,
		Name:       a.BaseInfo.Name,
		Desc:       a.BaseInfo.Desc,
		AgentTools: tools,
	}
}
