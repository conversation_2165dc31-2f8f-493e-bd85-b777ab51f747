package api_tools

import (
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

// APIToolTestParamInfo 工具集测试参数
type APIToolTestParamInfo struct {
	BaseUrl         string                       `json:"base_url" description:"调用地址"`
	Method          string                       `json:"method" description:"http method"`
	APIPath         string                       `json:"api_path" description:"api"`
	ServerType      agent_definition.ServerType  `json:"server_type" description:"服务器类型，rest/mcp/dynamic_mcp"` //插件类型,为空时默认rest
	McpType         agent_definition.McpType     `json:"mcp_type" description:"mcp类型, sse/streamableHttp"`
	Headers         map[string]string            `json:"headers" description:"全局headers"`
	Params          []APIToolCallParam           `json:"params" description:"参数列表"`
	ProxyInfo       *agent_definition.ProxyInfo  `json:"proxy" description:"代理配置"`
	MCPServerParams []*agent_definition.MCPParam `json:"mcp_server_params" description:"mcp server参数"`
}

type APIToolCallParam struct {
	Name      string                            `json:"name" description:"参数名"`
	ParamType agent_definition.APIToolParamType `json:"type" description:"类型query/path/header/body，前端调用需要传，服务端不需要"`
	Value     any                               `json:"value" description:"参数值"`
}

type APIToolCallCollectionParam struct {
	Name      string `json:"name" description:"参数名"`
	ParamType string `json:"type" description:"类型query/path/header/body，前端调用需要传，服务端不需要"`
	Value     any    `json:"value" description:"参数值"`
}

type APIToolMetaInfo struct {
	Type APIToolMetaType `json:"type" description:"yaml/json"`
	Meta []byte          `json:"meta" description:"yaml/json原始信息"`
}

type APIToolCallParamInfo struct {
	ToolCollectionID string                       `json:"toolCollectionID" description:"id"`
	BaseURL          string                       `json:"base_url" description:"base url"`
	APIPath          string                       `json:"api_path" description:"api"`
	Params           []APIToolCallParam           `json:"params" description:"参数列表"`
	CollectionParams []APIToolCallCollectionParam `json:"collection_params" description:"mcp插件参数列表"`
	McpType          agent_definition.McpType     `json:"mcp_type" description:"mcp类型, sse/streamableHttp"`
}
