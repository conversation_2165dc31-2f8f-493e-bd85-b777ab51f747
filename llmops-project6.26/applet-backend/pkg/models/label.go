package models

import mapset "github.com/deckarep/golang-set/v2"

const (
	LabelKind = "Applet-Chain"
)

type LabelGroups map[string][]string

// LabelGroupsDTO demo :"chain":{"场景":["AA","BB"]}
type LabelGroupsDTO map[string]map[string][]string

func (l LabelGroups) ToDTO() LabelGroupsDTO {
	return map[string]map[string][]string{
		LabelKind: l,
	}
}

func (l LabelGroupsDTO) ToDO() LabelGroups {
	return l[LabelKind]
}

func MergeLabels(labels []LabelGroups) LabelGroups {
	mSet := make(map[string]mapset.Set[string])
	res := make(map[string][]string)
	for _, l := range labels {
		for k, vs := range l {
			if _, ok := mSet[k]; !ok {
				mSet[k] = mapset.NewSet[string]()
			}
			s := mSet[k]
			for _, v := range vs {
				s.Add(v)
			}
		}
	}
	for k, s := range mSet {
		res[k] = s.ToSlice()
	}
	return res
}
