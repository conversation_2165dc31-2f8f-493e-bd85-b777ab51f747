package models

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"regexp"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type RoleType string

const (
	RoleTypeQuestion RoleType = "QUESTION"
	RoleTypeAnswer   RoleType = "ANSWER"
)

func DialogToPo(ctx context.Context, param *ChainRunReq, chainName, createdType string) *generated.Dialog {

	pbUserCtx, _ := helper.GetUserContext(ctx)

	return &generated.Dialog{
		AppID:       param.ChainID,
		AppName:     chainName,
		CreatedType: createdType,
		ProjectID:   pbUserCtx.ProjectId,
		ChatID:      param.ChatId,
		User:        pbUserCtx.UserId,
	}

}

func QuestionToPo(param map[string]interface{}, conversationId string) *generated.DialogMessage {

	content, err := json.Marshal(param)
	if err != nil {
		fmt.Println("Error :", err)
	}

	return &generated.DialogMessage{
		ChatID:  conversationId,
		Content: string(content),
		Role:    string(RoleTypeQuestion),
	}

}

func AnswerToPo(buffer *bytes.Buffer, conversationId string) *generated.DialogMessage {

	_, content := ExtractAnswer(buffer)

	return &generated.DialogMessage{
		ChatID:  conversationId,
		Content: content,
		Role:    string(RoleTypeAnswer),
	}

}

func ExtractAnswer(buffer *bytes.Buffer) (string, string) {

	var id, part string
	scanner := bufio.NewScanner(buffer)

	regexPattern := `data:\s*{\s*"response"\s*:\s*"(.*?)"\s*}`
	re := regexp.MustCompile(regexPattern)

	if scanner.Scan() {
		firstLine := scanner.Text()
		id = strings.TrimPrefix(firstLine, "id:")
	}

	for scanner.Scan() {
		line := scanner.Text()
		matches := re.FindAllStringSubmatch(line, -1)
		for _, match := range matches {
			if len(match) > 1 {
				part += matches[0][1]
			}
		}
	}

	if len(id) == 0 {
		id = uuid.New().String()
	}

	return id, part
}

func Message2DO(PO *generated.DialogMessage) *pb.Message {
	return &pb.Message{
		Id:         PO.ID,
		Content:    PO.Content,
		Role:       PO.Role,
		CreateTime: PO.CreateTime.Unix(),
	}
}

func Dialog2DO(dialog *generated.Dialog, question *generated.DialogMessage, answer *generated.DialogMessage) *pb.Dialog {
	return &pb.Dialog{
		Id:          dialog.ID,
		ChatId:      dialog.ChatID,
		AppId:       dialog.AppID,
		ProjectId:   dialog.ProjectID,
		User:        dialog.User,
		AppName:     dialog.AppName,
		CreatedType: dialog.CreatedType,
		CreateTime:  dialog.CreateTime.Unix(),
		UpdateTime:  dialog.UpdatedTime.Unix(),
		Messages: &pb.LatestMessage{
			Question: Message2DO(question),
			Answer:   Message2DO(answer),
		},
	}
}

func DialogAppToPo(ctx context.Context, chainId, chainName, createdType, url string) *generated.DialogApp {

	pbUserCtx, _ := helper.GetUserContext(ctx)

	return &generated.DialogApp{
		AppID:       chainId,
		AppName:     chainName,
		AppImage:    url,
		CreatedType: createdType,
		ProjectID:   pbUserCtx.ProjectId,
		User:        pbUserCtx.UserId,
	}

}

func DialogApp2DO(chain *generated.DialogApp) *pb.DialogApp {
	DO := &pb.DialogApp{
		Id:          chain.ID,
		AppId:       chain.AppID,
		AppName:     chain.AppName,
		AppImage:    chain.AppImage,
		CreatedType: chain.CreatedType,
		CreateTime:  chain.CreateTime.Unix(),
		UpdateTime:  chain.UpdatedTime.Unix(),
	}
	return DO
}
