package models

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type ArticleDO struct {
	ID          string           `json:"id"`
	ProjectID   string           `json:"project_id"`
	Name        string           `json:"name"`
	Abstract    string           `json:"abstract"`
	Title       string           `json:"title"`
	Content     string           `json:"content"`
	Creator     string           `json:"creator"`
	Status      string           `json:"status"`
	Labels      LabelGroups      `json:"labels"`
	IsSelected  int32            `json:"is_selected"`
	MetricsInfo ChainMetricsInfo `json:"metrics_info"` // 打点信息
	UpdatedTime int64            `json:"update_time"`
	CreatedTime int64            `json:"create_time"`
}

func (a ArticleDO) ToPO(ctx context.Context) (*generated.Article, error) {
	model := new(generated.Article)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(a, model); err != nil {
		return nil, err
	}
	// 拷贝结构体字段
	model.LabelInfo = helper.Struct2String(a.Labels)
	model.MetricsInfo = helper.Struct2String(a.MetricsInfo)
	// created、updatedTime由数据库自动控制
	return model, nil
}
func CvtArticlePOToDO(modelPO *generated.Article) (*ArticleDO, error) {
	modelDO := new(ArticleDO)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(modelPO, modelDO); err != nil {
		return nil, err
	}
	modelDO.Labels = *new(LabelGroups)
	if err := helper.String2Struct(modelPO.LabelInfo, &modelDO.Labels); err != nil {
		return nil, err
	}
	modelDO.MetricsInfo = *new(ChainMetricsInfo)
	if err := helper.String2Struct(modelPO.MetricsInfo, &modelDO.MetricsInfo); err != nil {
		return nil, err
	}
	// 设置时间类型
	modelDO.CreatedTime = helper.Time2UnixMilli(modelPO.CreatedTime)
	modelDO.UpdatedTime = helper.Time2UnixMilli(modelPO.UpdatedTime)
	return modelDO, nil
}
