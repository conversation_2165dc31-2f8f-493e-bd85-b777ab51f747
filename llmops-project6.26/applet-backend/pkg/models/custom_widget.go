package models

import (
	"context"
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type ParamType string

const (
	ParamTypeString = "string"
	ParamTypeInt64  = "int"
)

// 自定义算子状态
const (
	StartingStatus string = "Starting"
	StoppedStatus  string = "Stopped"
	StoppingStatus string = "Stopping"
	RunningStatus  string = "Running"
	FailedStatus   string = "Failed"
)

type CreateCustomWidgetReq struct {
	Start            bool                  `json:"start" description:"创建算子后是否立即启动"`
	CustomWidgetInfo *CustomWidgetBaseInfo `json:"custom_widget_info" description:"自定义算子具体信息"`
}
type UpdateCustomWidgetReq struct {
	ID               string                `json:"id" description:"自定义算子id"`
	Restart          bool                  `json:"restart" description:"部分参数需要重启才能生效"`
	CustomWidgetInfo *CustomWidgetBaseInfo `json:"custom_widget_info" description:"自定义算子具体信息"`
}

// CustomWidgetBaseInfo 自定义算子中允许用户修改的属性
type CustomWidgetBaseInfo struct {
	Name         string                 `json:"name" description:"算子名称"`
	Desc         string                 `json:"desc"  description:"算子描述信息"`
	Port         int32                  `json:"port" description:"使用的端口"`
	ParamInfo    []*FuncParam           `json:"param_info"  description:"算子入参信息描述"`
	ImageInfo    *CustomWidgetImageInfo `json:"image_info"  description:"自定义算子所使用的镜像信息"`
	LabelInfo    *LabelGroups           `json:"label_info"   description:"标签信息"`
	ResourceInfo *ResourceInfo          `json:"resource_info"   description:"算子资源限制信息"`
}

// CustomWidgetDO 自定义算子基本属性
type CustomWidgetDO struct {
	ID           string                 `json:"id"  description:"自定义算子id,唯一标识"`
	Status       string                 `json:"status"  description:"算子运行转态"`
	Name         string                 `json:"name" description:"算子名称"`
	Desc         string                 `json:"desc"  description:"算子描述信息"`
	Port         int32                  `json:"port" description:"使用的端口"`
	ParamInfo    []*FuncParam           `json:"param_info"  description:"算子入参信息描述"`
	ImageInfo    *CustomWidgetImageInfo `json:"image_info"  description:"自定义算子所使用的镜像信息"`
	LabelInfo    *LabelGroups           `json:"label_info"   description:"标签信息"`
	ResourceInfo *ResourceInfo          `json:"resource_info"   description:"算子资源限制信息"`
	// id、status及后续字段用户不可更改
	DeployInfo  *CustomWidgetDeployInfo `json:"deploy_info" description:"部署信息"`
	Creator     string                  `json:"creator" description:"创建者"`
	ProjectID   string                  `json:"project_id" description:"所属项目id"`
	CreatedTime int64                   `json:"created_time" description:"算子创建时间"`
	UpdatedTime int64                   `json:"updated_time" description:"算子最近一次跟新时间"`
}
type CustomWidgetDeployInfo struct {
	ServiceName string `json:"service_name"`
}
type CustomWidgetImageInfo struct {
	// ImageName string `json:"image_name"   description:"镜像名称"`
	ImageUrl string `json:"image_url"   description:"镜像的url地址"`
	// ImageID   string `json:"image_id"   description:"镜像在代码仓库中对应的id"`
}

type ResourceInfo struct {
	Request ResourceKind `json:"request"   description:"需要的最小资源数量"`
	Limit   ResourceKind `json:"limit"   description:"限制的最大资源数量"`
}

// ResourceKind 资源需求描述信息
type ResourceKind struct {
	Cpu    int64 `json:"cpu"   description:"cpu数量"`
	Gpu    int64 `json:"gpu"   description:"gpu数量"`
	Memory int64 `json:"memory"   description:"内存大小"`
}

type FuncParam struct {
	ParamName string `json:"param_name"`
	ParamType string `json:"param_type"`
	ParamDesc string `json:"param_desc"`
}

func (w CustomWidgetDO) Valid() error {
	if len(w.ParamInfo) == 0 {
		return helper.AppletChainCustomWidgetRegisterErr.Error("算子输入端点数量至少为一")
	}
	exists := make(map[string]bool)
	for _, p := range w.ParamInfo {
		if p.ParamName == "" {
			return helper.AppletChainCustomWidgetRegisterErr.Error("算子入参名称不能为空")
		}
		// p.ParamName此时必定存在
		if exists[p.ParamName] {
			return stderr.Internal.Error("exists the same param name")
		}
		exists[p.ParamName] = true
	}
	if w.Port <= 0 || w.Port >= 65535 {
		return stderr.Internal.Error("invalid port :%v", w.Port)
	}
	if w.Creator == "" {
		return stderr.Internal.Error("the field of creator cannot be empty")
	}
	if w.ProjectID == "" {
		return stderr.Internal.Error("the field of project_id cannot be empty")
	}
	if w.ImageInfo == nil || w.ImageInfo.ImageUrl == "" {
		return stderr.Internal.Error("the field of image_info.image_url cannot be empty")
	}
	return nil
}

func (w CustomWidgetDO) ToPO(ctx context.Context) (*generated.CustomWidget, error) {
	widgetPO := new(generated.CustomWidget)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(w, widgetPO); err != nil {
		return nil, err
	}
	// 拷贝结构体字段
	widgetPO.ImageInfo = helper.Struct2String(w.ImageInfo)
	widgetPO.LabelInfo = helper.Struct2String(w.LabelInfo)
	widgetPO.ResourceInfo = helper.Struct2String(w.ResourceInfo)
	widgetPO.ParamInfo = helper.Struct2String(w.ParamInfo)
	widgetPO.DeployInfo = helper.Struct2String(w.DeployInfo)
	// created、updatedTime由数据库自动控制
	return widgetPO, nil
}
func CvtCustomWidgetPOToDO(widgetPO *generated.CustomWidget) (*CustomWidgetDO, error) {
	widgetDO := new(CustomWidgetDO)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(widgetPO, widgetDO); err != nil {
		return nil, err
	}
	widgetDO.ParamInfo = make([]*FuncParam, 0)
	widgetDO.ImageInfo = new(CustomWidgetImageInfo)
	widgetDO.LabelInfo = new(LabelGroups)
	widgetDO.ResourceInfo = new(ResourceInfo)
	widgetDO.DeployInfo = new(CustomWidgetDeployInfo)
	// 给DO的结构体字段赋值
	if err := helper.String2Struct(widgetPO.ParamInfo, &(widgetDO.ParamInfo)); err != nil {
		return nil, err
	}
	if err := helper.String2Struct(widgetPO.ImageInfo, widgetDO.ImageInfo); err != nil {
		return nil, err
	}
	if err := helper.String2Struct(widgetPO.LabelInfo, widgetDO.LabelInfo); err != nil {
		return nil, err
	}
	if err := helper.String2Struct(widgetPO.ResourceInfo, widgetDO.ResourceInfo); err != nil {
		return nil, err
	}
	if err := helper.String2Struct(widgetPO.DeployInfo, widgetDO.DeployInfo); err != nil {
		return nil, err
	}

	// 设置时间类型
	widgetDO.CreatedTime = helper.Time2UnixMilli(widgetPO.CreatedTime)
	widgetDO.UpdatedTime = helper.Time2UnixMilli(widgetPO.UpdatedTime)
	return widgetDO, nil
}

// ToWidget 自定义算子
func (w CustomWidgetDO) ToWidget(ctx context.Context, istioGatewayAddr string, virtualSvcUrl string) (*widgets.Widget, error) {
	params := make([]widgets.WidgetParam, 0)
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Define: widgets.DynamicParam{
			Id:           "Name",
			Name:         "Name",
			Desc:         w.Desc,
			Type:         pb.DynamicParam_TYPE_INPUT,
			DataType:     pb.DynamicParam_DATA_TYPE_STRING,
			Disabled:     true,
			DefaultValue: w.Name,
		},
	})
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Define: widgets.DynamicParam{
			Id:           "Address",
			Name:         "调用地址",
			Desc:         "调用地址",
			Type:         pb.DynamicParam_TYPE_INPUT,
			DataType:     pb.DynamicParam_DATA_TYPE_STRING,
			DefaultValue: fmt.Sprintf("%s/%s/%v/", istioGatewayAddr, virtualSvcUrl, w.Port),
			Disabled:     true,
			Hidden:       true,
		},
	})
	for _, p := range w.ParamInfo {
		params = append(params, widgets.WidgetParam{
			DataClass:   widgets.DataClassString,
			Category:    widgets.ParamTypeNodeInPort,
			ParamLimits: widgets.SyncAnyLimits(),
			Define: widgets.DynamicParam{
				Id:       p.ParamName,
				Name:     p.ParamName,
				Desc:     p.ParamName,
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		})
	}
	params = append(params, widgets.WidgetParam{
		DataClass:   widgets.DataClassString,
		Category:    widgets.ParamTypeNodeOutPort,
		ParamLimits: widgets.AnyAnyLimits(),
		Define: widgets.DynamicParam{
			Id: "Output",
		},
	})

	baseDefine, err := widgets.WidgetFactoryImpl.GetWidgetDefine(widgets.WidgetKeyCustomWidget)
	if err != nil {
		return nil, err
	}
	baseDefine.Desc = w.Desc
	baseDefine.Params = params
	return baseDefine, nil
}

func (w CustomWidgetDO) toScriptConfig() *widgets.ScriptConfig {
	return &widgets.ScriptConfig{
		ScriptClassName: "promptTmpl",
		ScriptClassParamFunc: func(node *widgets.ChainNode) (string, error) {
			return "", nil
		},
	}
}
