package models

type DifyImportRequest struct {
	YamlContent string `json:"yaml_content" binding:"required"`
}

type DifyImportResponse struct {
	WorkflowURL string `json:"workflow_url"`
	AppID       string `json:"app_id"`
	AccessToken string `json:"access_token"`
}

// DifyImportRequestBody dify导入API的请求体
type DifyImportRequestBody struct {
	Mode        string `json:"mode"` // 固定为"yaml-content"
	YamlContent string `json:"yaml_content"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type DifyImportResponseBody struct {
	AppID string `json:"app_id"`
}

type DifyPublishRequestBody struct{}

type DifyPublishResponseBody struct{}

type DifySiteTokenRequestBody struct{}

type DifySiteTokenResponseBody struct {
	AccessToken string `json:"access_token"` // 访问token
}

type DifyCheckDependenciesResponseBody struct {
	LeakedDependencies []interface{} `json:"leaked_dependencies"`
}
