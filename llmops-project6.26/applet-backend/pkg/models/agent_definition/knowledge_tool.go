package agent_definition

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

const (
	KnowlHubParamQuery     = "query"
	KnowlHubParamQueryDesc = "查询文本"
)

type SimpleKbInfo struct {
	ID       string `json:"id"  description:"知识库ID"`
	IsPublic bool   `json:"is_public"  description:"是否来源于公共空间"`
}
type KnowlHubDescriber struct {
	ID              string              `json:"id"  description:"知识库ID"`
	Name            string              `json:"name"  description:"知识库名称"`
	Desc            string              `json:"desc"  description:"知识库描述"`
	IsPublic        bool                `json:"is_public"  description:"是否来源于公共空间"`
	EnableDocs      []*pb.Document      `json:"enable_docs"  description:"召回时启用的文档信息"`
	RecallParams    *pb.RecallParams    `json:"recall_params" description:"该知识库recall配置信息"`
	RerankParams    *pb.RerankParams    `json:"rerank_params"  description:"该知识库rerank配置信息，后端依靠kbs层级自动配置"`
	AutoConfigured  bool                `json:"auto_configured" description:"是否自动配置"`
	ContextNum      int32               `json:"context_num" description:"切片上下文数量"`
	DocEngineConfig *pb.DocEngineConfig `json:"doc_engine_config" description:"doc_engine类型知识库"`
}

func (a *KnowlHubDescriber) Type() ToolType {
	return ToolTypeKnowledgeHub
}

func (a *KnowlHubDescriber) Definition() Tool {
	return Tool{
		ID:           a.ID,
		Type:         a.Type(),
		NameForModel: a.Name,
		NameForHuman: a.Name,
		Description:  a.Desc,
		Parameters: triton.FunctionParameters{
			Type: triton.ParameterTypeObject,
			Properties: map[string]triton.ParameterProperty{
				KnowlHubParamQuery: triton.ParameterProperty{
					Type:        triton.ParameterTypeString,
					Description: KnowlHubParamQueryDesc,
				},
			},
		},
	}
}
