package agent_definition

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type ModelToolDescriber struct {
	pb.ModelService
}

// Type 当前工具类型，可用于判断为其匹配哪个执行器
func (m *ModelToolDescriber) Type() ToolType {
	return ToolTypeModelService
}

// Definition 当前工具在被大模型选择时，需要提供的信息，一般为工具的作用描述，参数信息等
func (m *ModelToolDescriber) Definition() Tool {
	if m.InvokeAsTool == nil {
		return Tool{}
	}
	invokeAsTool := m.InvokeAsTool
	properties, required := getParamDesc(invokeAsTool.InvokeParams)
	return Tool{
		ID:           m.Id,
		Type:         ToolTypeModelService,
		NameForModel: invokeAsTool.NameForModel,
		NameForHuman: invokeAsTool.NameForHuman,
		Description:  invokeAsTool.Desc,
		Parameters: triton.FunctionParameters{
			Type:       triton.ParameterTypeObject,
			Properties: properties,
			Required:   required,
		},
	}
}
func getParamDesc(invokeParams []*pb.InvokeParam) (map[string]triton.ParameterProperty, []string) {
	params := make(map[string]triton.ParameterProperty)
	required := make([]string, 0)
	for _, p := range invokeParams {
		params[p.Name] = triton.ParameterProperty{
			Type:        triton.ParameterType(p.Type),
			Description: p.Desc,
		}
		required = append(required, p.Name)
	}
	return params, required
}
