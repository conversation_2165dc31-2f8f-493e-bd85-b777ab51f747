// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

const TableNameLlmBasicConfig = "llm_basic_configs"

// LlmBasicConfig mapped from table <llm_basic_configs>
type LlmBasicConfig struct {
	ID                 string `gorm:"column:id;primaryKey" json:"id"`
	ProjectID          string `gorm:"column:project_id" json:"project_id"`
	LlmModelSvc        string `gorm:"column:llm_model_svc" json:"llm_model_svc"`
	EmbeddingSvc       string `gorm:"column:embedding_svc" json:"embedding_svc"`
	ImageGenSvc        string `gorm:"column:image_gen_svc" json:"image_gen_svc"`
	ReRankSvc          string `gorm:"column:re_rank_svc" json:"re_rank_svc"`
	ImageUnderstandSvc string `gorm:"column:image_understand_svc" json:"image_understand_svc"`
	OcrSvc             string `gorm:"column:ocr_svc" json:"ocr_svc"`
}

// TableName LlmBasicConfig's table name
func (*LlmBasicConfig) TableName() string {
	return TableNameLlmBasicConfig
}
