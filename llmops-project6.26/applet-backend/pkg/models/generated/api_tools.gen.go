// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAPITool = "api_tools"

// APITool mapped from table <api_tools>
type APITool struct {
	ID           string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                   // ID
	ProjectID    string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                   // 项目ID
	CollectionID string    `gorm:"column:collection_id;not null;comment:工具集ID" json:"collection_id"`                            // 工具集ID
	Name         string    `gorm:"column:name;not null;comment:工具名称,从yaml - path中解析出来的name" json:"name"`                        // 工具名称,从yaml - path中解析出来的name
	Alias_       string    `gorm:"column:alias;not null;comment:工具别名，name for human" json:"alias"`                              // 工具别名，name for human
	Desc         string    `gorm:"column:desc;not null;comment:工具描述" json:"desc"`                                               // 工具描述
	TestState    string    `gorm:"column:test_state;not null;default:init;comment: 测试状态:init/success/failed" json:"test_state"` //  测试状态:init/success/failed
	Method       string    `gorm:"column:method;not null;comment:请求方法GET/POST/xxx等" json:"method"`                              // 请求方法GET/POST/xxx等
	Path         string    `gorm:"column:path;not null;comment:请求路径" json:"path"`                                               // 请求路径
	Params       string    `gorm:"column:params;comment:参数，json array格式" json:"params"`                                         // 参数，json array格式
	CreateTime   time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`       // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"`     // 更新时间
}

// TableName APITool's table name
func (*APITool) TableName() string {
	return TableNameAPITool
}
