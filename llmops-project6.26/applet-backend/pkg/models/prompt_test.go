package models

import (
	"encoding/json"
	"fmt"
	"testing"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func TestListPrompt(t *testing.T) {
	var widgetParamsV2 WidgetParams
	widgetParamsV2 = map[string]*WidgetParam{
		"widgetID1": &WidgetParam{
			WidgetID: "widgetID1",
			Params: map[string]*WidgetParamValue{
				"paramID1": &WidgetParamValue{Value: "111"},
				"paramID2": {Value: "222"},
			},
		},
		"widgetID2": &WidgetParam{
			WidgetID: "widgetID2",
			Params: map[string]*WidgetParamValue{
				"paramID1": &WidgetParamValue{Value: "111"},
				"paramID2": {Value: 123},
			},
		},
	}
	bytes, err := json.Marshal(widgetParamsV2)
	fmt.Println(string(bytes))

	widgets.Init()
	prompt := &PromptTemplate{
		Name: "22",
	}
	widget, err := prompt.ToWidget()
	if err != nil {
		t.Fatal(err)
	}
	t.Log(widget)
	if err := widgets.WidgetFactoryImpl.RegisterOrUpdate(widget); err != nil {
		t.Fatal(err)
	}
	w, err := widgets.WidgetFactoryImpl.GetWidget("22")
	if w.Define().Name != "22" {
		t.Fatal(w.Define().Name)
	}

}
