package models

import (
	"transwarp.io/applied-ai/aiot/vision-std/database"
)

const (
	TableNameAppletService = "applet_services"
)

// AppletService 应用服务,单独保存发布时的配置信息。
type AppletService struct {
	ChainID    string `json:"chain_id,omitempty" gorm:"type:VARCHAR(100); primaryKey; comment:应用链id" description:"应用链id"`
	ServiceID  string `json:"service_id,omitempty" gorm:"type:VARCHAR(100); comment:应用服务id; not null" description:"应用服务id"`
	SnapshotID string `json:"snapshot_id,omitempty" gorm:"type:VARCHAR(100); comment:快照id; not null" description:"快照id"`
	ProjectID  string `json:"project_id,omitempty" gorm:"type:VARCHAR(100); not null;  comment:项目空间id" description:"项目空间id"`
	database.TimeMixin
}

func (*AppletService) TableName() string {
	return TableNameAppletService
}
