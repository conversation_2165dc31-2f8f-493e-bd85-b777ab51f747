package models

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"gorm.io/gorm"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/database"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

type ExternalAPPSource string

const (
	RegisterExternalAPP ExternalAPPSource = "register"
	DeployedExternalAPP ExternalAPPSource = "deployed"
)

type RegisterType string

const (
	RegisterTypeSrcUrl     RegisterType = "src_url"
	RegisterTypeIFrameCode RegisterType = "iframe_code"
)

// ExternalAPP 应用仓库 - 外部注册第三方应用(IFrame / 跳转反问链接)
type ExternalAPP struct {
	database.TimeMixin
	ID               string                        `json:"id" gorm:"type:VARCHAR(100);primaryKey; comment:外部应用ID" description:"后端自动生成uuid"`
	Name             string                        `json:"name" gorm:"type:VARCHAR(100); not null; comment:外部应用名称" description:"应用名称"`
	Desc             string                        `json:"desc" gorm:"type:VARCHAR(2048);" description:"应用描述"`
	RegisterType     RegisterType                  `json:"register_type" gorm:"type:VARCHAR(100);" description:"外部应用的引入方式[src_url|iframe_code],"`
	IFrameCode       string                        `json:"iframe_code" gorm:"type:VARCHAR(2048);" description:"通过iframe方式引入外部应用的具体代码"`
	SrcUrl           string                        `json:"src_url" gorm:"type:VARCHAR(4096); not null;" description:"外部应用的地址,可以用于新页面跳转/Iframe嵌入. 当Source为deployed时, 表示应用为通过istio部署所得, 故需要前端拼接当前浏览器地址作为前缀进行跳转访问"`
	ProjectID        string                        `json:"project_id" gorm:"type:VARCHAR(200); not null;" description:"注册的外部应用所在的项目空间ID, 后端通过query param填充"`
	Creator          string                        `json:"creator" gorm:"type:VARCHAR(200); not null;" description:"注册该外部应用的用户, 后端自动填充"`
	ImageUrl         string                        `json:"image_url"  gorm:"type:VARCHAR(400);" description:"封面图片地址, sfs路径"`
	Published        bool                          `json:"published"  gorm:"type:bool;" description:"该外部注册应用是否已发布(发布后的应用可以在应用体验中看到)"`
	Members          stdsrv.Members                `json:"members"  gorm:"type:text;serializer:json" description:"应用发布后可以看到该应用的成员列表"`
	Source           ExternalAPPSource             `json:"source"  gorm:"type:VARCHAR(128);" description:"自定义应用来源:[register=用户注册|deployed=自定义容器部署], 前端注册添加时,后端自动填充"`
	LabelInfo        LabelGroups                   `json:"label_info"  gorm:"type:text;serializer:json" description:"标签信息"`
	IFrameCfg        map[string]any                `json:"iframe_cfg"  gorm:"type:text;serializer:json" description:"用户IFrame自定义配置"`
	MlopsSvc         *serving.MLOpsServiceBaseInfo `json:"-"  gorm:"-:all;"` // 当服务为自定部署时,存储其关联的mlops service 定义
	PermissionAction string                        `json:"permission_action,omitempty" gorm:"type:VARCHAR(256);"`
	PermissionCfg    *common.PermissionCfg         `json:"permission_cfg,omitempty" gorm:"type:text;serializer:json"`
}

func (e *ExternalAPP) GetID() (string, error) {
	return e.ID, nil
}

func (e *ExternalAPP) GetCreator() (string, error) {
	return e.Creator, nil
}

func (e *ExternalAPP) GetPermissionCfg() (*common.PermissionCfg, error) {
	return e.PermissionCfg, nil
}

func (e *ExternalAPP) SetPermissionCfg(cfg *common.PermissionCfg) error {
	e.PermissionCfg = cfg
	return nil
}

func (a *ExternalAPP) GetPermissionAction() (string, error) {
	return a.PermissionAction, nil
}
func (a *ExternalAPP) SetPermissionAction(action string) error {
	a.PermissionAction = action
	return nil
}

// BeforeSave 钩子函数，在保存之前将 Label 转换为 JSON
func (e *ExternalAPP) BeforeSave(tx *gorm.DB) (err error) {
	if e.CreatedAt.IsZero() {
		e.CreatedAt = time.Now()
	}
	e.UpdatedAt = time.Now()
	return
}

// AfterFind 钩子函数，在查询之后将 JSON 解析为结构体
func (e *ExternalAPP) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (e *ExternalAPP) CreatedType() AppletType {
	if e.MlopsSvc != nil {
		return AppletTypeCustomDeployed
	}
	return AppletTypeExternalRegister
}

func (e *ExternalAPP) ToAppletChainBaseDO() *AppletChainBaseDO {
	return &AppletChainBaseDO{
		ID:               e.ID,
		Name:             e.Name,
		Creator:          e.Creator,
		Desc:             e.Desc,
		Labels:           e.LabelInfo,
		LastDebugState:   ChainDebugStateSuccess,
		ContainsSubChain: false,
		ProjectID:        e.ProjectID,
		UpdateTime:       e.UpdatedAt.UnixMilli(),
		CreateTime:       e.CreatedAt.UnixMilli(),
		AssetType:        pb.AssetType_ASSET_SHARED.String(),
		CreatedType:      e.CreatedType(),
		ExperimentInfo:   e.GetExperimentInfo(),
		AssistantInfo:    nil,
		SourceInfo:       nil,
		MetricsInfo:      &ChainMetricsInfo{},
		SpaceInfo:        &pb.SpaceInfo{},
		UsageType:        AppletUsageTypeCommon,
		PermissionAction: e.PermissionAction,
		PermissionCfg:    e.PermissionCfg,
		ExtraInfo: ExtraInfo{
			Published:   e.Published,
			ServiceInfo: e.GetAppServiceInfo(),
		},
	}
}

func (e *ExternalAPP) ToAppExperimentService() *AppExperimentService {
	return &AppExperimentService{
		ID:             e.ID,
		ProjectId:      e.ProjectID,
		ExperimentInfo: e.GetExperimentInfo(),
		AppServiceInfo: e.GetAppServiceInfo(),
		ChainDetail:    nil,
		CreatedType:    e.CreatedType(),
		UsageType:      AppletUsageTypeCommon,
		CreateTime:     e.CreatedAt.UnixMilli(),
		ProblemGenInfo: nil,
	}
}

func (e *ExternalAPP) GetExperimentInfo() *ExperimentInfo {
	return &ExperimentInfo{
		ExamplesInfo: ExamplesInfo{
			ImageUrl:           e.ImageUrl,
			Introduction:       e.Desc,
			MultimodalExamples: []*MultimodalExample{},
			Members:            e.Members,
			PermissionAction:   e.PermissionAction,
			PermissionCfg:      e.PermissionCfg,
		},
		Name:         e.Name,
		LabelInfo:    e.LabelInfo,
		RegisterType: e.RegisterType,
		SrcUrl:       e.SrcUrl,
		IFrameCode:   e.IFrameCode,
	}
}

func (e *ExternalAPP) GetAppServiceInfo() *AppServiceInfo {
	if e.MlopsSvc == nil {
		stdlog.Warnf("can not GetAppServiceInfo from a external app that not deployed by serving")
		return &AppServiceInfo{
			HealthInfo: &health.ChainHealth{ServiceHealth: health.ServiceHealth{Healthy: true}},
		}
	}
	svc := e.MlopsSvc
	return &AppServiceInfo{
		ID:         svc.Id,
		Name:       svc.Name,
		VirtualUrl: svc.VirtualSvcUrl,
		Status:     svc.State,
		StateIno:   GetRunningStateInfo(),
		HealthInfo: &health.ChainHealth{
			ServiceHealth: health.ServiceHealth{
				ID:      svc.Id,
				Name:    svc.Name,
				Healthy: svc.State == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE,
				Detail:  svc.State.String(),
			},
			Timestamp:    time.Now().UnixMilli(),
			Dependencies: nil,
		},
	}
}

func (e *ExternalAPP) SubmitPermission(ctx context.Context) error {
	return helper.SubmitAndSetPermission(ctx, e, cas.ObjType_AppletChain)
}
