package dao

import (
	"context"
	"fmt"
	"gorm.io/gen"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type DialogDAO struct {
	BaseDAO
}

type DialogParam struct {
	ChatId    *string
	AppId     *string
	ProjectID *string
	User      *string
	AppName   *string
	StartTime *int64
	EndTime   *int64
}

func (c DialogParam) ToQueryConditions() []gen.Condition {
	cods := make([]gen.Condition, 0)
	q := query.Dialog
	if c.ChatId != nil {
		cods = append(cods, q.ChatID.Eq(*c.ChatId))
	}
	if c.AppId != nil {
		cods = append(cods, q.AppID.Eq(*c.AppId))
	}
	if c.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*c.ProjectID))
	}
	if c.User != nil {
		cods = append(cods, q.User.Eq(*c.User))
	}
	if c.AppName != nil {
		cods = append(cods, q.AppName.Eq(*c.AppName))
	}

	if c.StartTime != nil {
		cods = append(cods, q.CreateTime.Gt(time.Unix(*c.StartTime, 0)))
	}

	if c.EndTime != nil {
		cods = append(cods, q.CreateTime.Lt(time.Unix(*c.EndTime, 0)))
	}
	return cods
}

// 对话 Meta

func (c DialogDAO) GetByChatId(ctx context.Context, chatId string) (*generated.Dialog, error) {
	q := c.getQueryOrDefault().Dialog
	return q.WithContext(ctx).Where(q.ChatID.Eq(chatId)).First()
}

func (c DialogDAO) GetDiaLog(ctx context.Context, param *DialogParam) (*generated.Dialog, error) {
	q := c.getQueryOrDefault().Dialog
	return q.WithContext(ctx).Where(param.ToQueryConditions()...).First()
}
func (c DialogDAO) Count(ctx context.Context, conversationId string) (int64, error) {
	q := c.getQueryOrDefault().Dialog
	return q.WithContext(ctx).Where(q.ChatID.Eq(conversationId)).Count()
}

func (c DialogDAO) Create(ctx context.Context, dialog *generated.Dialog) error {
	q := c.getQueryOrDefault().Dialog
	return q.WithContext(ctx).Create(dialog)
}

func (c DialogDAO) Update(ctx context.Context, dialog *generated.Dialog) error {
	q := c.getQueryOrDefault().Dialog
	_, err := q.WithContext(ctx).Updates(dialog)
	return err
}

func (c DialogDAO) UpdateColumns(ctx context.Context, id string, qId, aId int64) error {
	q := c.getQueryOrDefault().Dialog
	_, err := q.WithContext(ctx).Where(q.ChatID.Eq(id)).UpdateColumns(
		map[string]interface{}{q.LatestQuestionID.ColumnName().String(): qId,
			q.LatestAnswerID.ColumnName().String(): aId})
	return err
}

func (c DialogDAO) BatchCreate(ctx context.Context, messages []*generated.Dialog) error {
	q := c.getQueryOrDefault().Dialog
	return q.WithContext(ctx).CreateInBatches(messages, 2)
}

func (c DialogDAO) Delete(ctx context.Context, conversationId string) (string, error) {
	q := c.getQueryOrDefault().Dialog
	_, err := q.WithContext(ctx).Where(q.ChatID.Eq(conversationId)).Delete()
	return conversationId, err

}

// 具体对话信息相关

type MessageParam struct {
	ChatId  *string
	Content *string
	Role    *string
}

func (c MessageParam) ToQueryConditions() []gen.Condition {
	cods := make([]gen.Condition, 0)
	q := query.DialogMessage
	if c.ChatId != nil {
		cods = append(cods, q.ChatID.Eq(*c.ChatId))
	}
	if c.Role != nil {
		cods = append(cods, q.Role.Eq(*c.Role))
	}
	if c.Content != nil {
		cods = append(cods, q.Content.Like(fmt.Sprintf("%%%s%%", *c.Content)))
	}

	return cods
}

func (c DialogDAO) CreateMessage(ctx context.Context, message *generated.DialogMessage) error {
	q := c.getQueryOrDefault().DialogMessage
	return q.WithContext(ctx).Create(message)
}

func (c DialogDAO) BatchCreateMessage(ctx context.Context, messages []*generated.DialogMessage) error {
	q := c.getQueryOrDefault().DialogMessage
	return q.WithContext(ctx).CreateInBatches(messages, 2)
}

func (c DialogDAO) GetMessageById(ctx context.Context, id int64) (*generated.DialogMessage, error) {
	q := c.getQueryOrDefault().DialogMessage
	return q.WithContext(ctx).Where(q.ID.Eq(id)).First()
}

func (c DialogDAO) RevokeMessage(ctx context.Context, id int64) {
	q := c.getQueryOrDefault().DialogMessage
	_, _ = q.WithContext(ctx).Where(q.ID.Eq(id)).Delete()
}

func (c DialogDAO) CountMessages(ctx context.Context, param *MessageParam) (int64, error) {
	q := c.getQueryOrDefault().DialogMessage
	return q.WithContext(ctx).Where(param.ToQueryConditions()...).Count()
}

func (c DialogDAO) CollectionMessages(ctx context.Context, param *MessageParam, page *pb.PageReq) ([]*generated.DialogMessage, error) {
	q := c.getQueryOrDefault().DialogMessage
	cond := q.WithContext(ctx).Where(param.ToQueryConditions()...)

	if page != nil {
		sortBy := page.SortBy
		name, find := q.GetFieldByName(sortBy)
		if !find {
			name = q.CreateTime
		}
		if page.IsDesc {
			cond = cond.Order(name.Desc()).Order(q.ID.Desc())
		} else {
			cond = cond.Order(name.Asc()).Order(q.ID.Asc())
		}

		if page.PageSize > 0 && page.Page > 0 {
			cond.Limit(int(page.PageSize)).Offset(int((page.Page - 1) * page.PageSize))
		}
	} else {
		cond.Order(q.ID.Asc())
	}

	return cond.Find()

}

func (c DialogDAO) DeleteMessages(ctx context.Context, param *MessageParam) (*string, error) {
	q := c.getQueryOrDefault().DialogMessage
	_, err := q.WithContext(ctx).Where(param.ToQueryConditions()...).Delete()

	return param.ChatId, err
}

func (c DialogDAO) GetDialogs(ctx context.Context, param *DialogParam, req *pb.DialogReq) ([]*generated.Dialog, int64, error) {
	q := c.getQueryOrDefault().Dialog
	cond := q.WithContext(ctx).Where(param.ToQueryConditions()...)

	if len(req.Content) != 0 {

		m1 := c.getQueryOrDefault().DialogMessage.As("m1")
		cond = cond.LeftJoin(m1, q.LatestQuestionID.EqCol(m1.ID), m1.Content.Like(fmt.Sprintf("%%%s%%", req.Content)))

		m2 := c.getQueryOrDefault().DialogMessage.As("m2")
		cond = cond.LeftJoin(m2, q.LatestAnswerID.EqCol(m2.ID), m2.Content.Like(fmt.Sprintf("%%%s%%", req.Content)))

		cond = cond.Where(q.Where(m1.ID.IsNotNull()).Or(m2.ID.IsNotNull()))

	}

	count, err := cond.Count()
	if err != nil {
		return nil, count, err
	}

	if req.PageReq != nil {
		sortBy := req.PageReq.SortBy
		if len(sortBy) == 0 {
			sortBy = q.CreateTime.ColumnName().String()
		}
		name, find := q.GetFieldByName(sortBy)
		if !find {
			name = q.CreateTime
		}
		if req.PageReq.IsDesc {
			cond = cond.Order(name.Desc()).Order(q.ID.Desc())
		} else {
			cond = cond.Order(name.Asc()).Order(q.ID.Asc())
		}

		if req.PageReq.PageSize > 0 && req.PageReq.Page > 0 {
			cond = cond.Limit(int(req.PageReq.PageSize)).Offset(int((req.PageReq.Page - 1) * req.PageReq.PageSize))
		}
	} else {
		cond = cond.Order(q.ID.Desc())
	}

	res, err := cond.Find()
	return res, count, err
}

func (c DialogDAO) GetDialogApps(ctx context.Context, user, projectId string) ([]*generated.DialogApp, int64, error) {
	q := c.getQueryOrDefault().DialogApp
	cond := q.WithContext(ctx).Where(q.User.Eq(user)).Where(q.ProjectID.Eq(projectId))

	size, err := cond.Count()
	if err != nil {
		return nil, size, err
	}

	if size > 0 {
		chains, err := cond.Order(q.CreateTime.Desc()).Find()
		if err != nil {
			return nil, 0, err
		}
		return chains, size, nil
	}

	return nil, 0, err
}

func (c DialogDAO) GetDialogApp(ctx context.Context, po *generated.DialogApp) (*generated.DialogApp, error) {
	q := c.getQueryOrDefault().DialogApp
	return q.WithContext(ctx).
		Where(q.AppID.Eq(po.AppID)).Where(q.ProjectID.Eq(po.ProjectID)).Where(q.User.Eq(po.User)).First()
}

// AddDialogApp Upsert
func (c DialogDAO) AddDialogApp(ctx context.Context, po *generated.DialogApp) (int64, error) {
	q := c.getQueryOrDefault().DialogApp
	err := q.WithContext(ctx).Save(po)
	if err != nil {
		return 0, err
	}
	return po.ID, nil
}

func (c DialogDAO) DeleteDialogApp(ctx context.Context, po *generated.DialogApp) (int64, error) {

	q := c.getQueryOrDefault().DialogApp
	_, err := q.WithContext(ctx).
		Where(q.AppID.Eq(po.AppID)).Where(q.ProjectID.Eq(po.ProjectID)).Where(q.User.Eq(po.User)).Delete()

	if err != nil {
		return 0, err
	}

	return po.ID, nil
}

func (c DialogDAO) GetTotalDialog(ctx context.Context, appId string) (int64, error) {
	q := c.getQueryOrDefault().Dialog
	if len(appId) != 0 {
		return q.WithContext(ctx).Where(q.AppID.Eq(appId)).Count()
	}
	return q.WithContext(ctx).Count()
}
