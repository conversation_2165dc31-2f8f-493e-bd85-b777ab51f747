package dao

import (
	"errors"
	"os"
	"os/exec"
	"strings"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"

	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func set83DevDBConfig() {
	conf.Config = &conf.AppConfig{
		Database: "mysql",
		Mysql: conf2.MysqlConfig{
			// before test
			// docker run --name mysqltmp -d --net host -e MYSQL_ROOT_PASSWORD=123 ***********/aip/deps/mysql-8.0:dev
			// after test
			// docker rm -f mysqltmp
			Username: "root",
			Password: "Warp!CV@2022#",
			Host:     "*************",
			Port:     "31398",
			// Password:       "123",
			// Host:           "localhost",
			// Port:           "3306",
			DBName:         "test_app_default",
			NotPrintSql:    false,
			NotCreateTable: false,
		},
	}
}
func setLocalDBConfig() {
	println("start mysql container")
	// 尝试删除已存在容器
	stop := exec.Command("docker", strings.Split("rm -f mysqltmpforapplet", " ")...)
	stop.Stdout = os.Stdout
	stop.Stderr = os.Stdout
	_ = stop.Run()

	// 启动本地测试mysql
	start := exec.Command("docker",
		strings.Split("run --name mysqltmpforapplet "+
				"-d --net host "+
				"-e MYSQL_ROOT_PASSWORD=123 "+
				"-e MYSQL_ROOT_HOST=% "+
				"-v /dev/shm:/var/lib/mysql "+
				"***********/aip/deps/mysql-8.0:dev", " ")...)
	start.Stdout = os.Stdout
	start.Stderr = os.Stdout
	err := start.Run()
	if err != nil {
		panic(err)
	}
	conf.Config = &conf.AppConfig{
		Database: "mysql",
		Mysql: conf2.MysqlConfig{
			// before test
			// docker run --name mysqltmp -d --net host -e MYSQL_ROOT_PASSWORD=123 ***********/aip/deps/mysql-8.0:dev
			// after test
			// docker rm -f mysqltmp
			Username: "root",
			Password: "123",
			Host:     "localhost",
			Port:     "3306",
			// Password:       "123",
			// Host:           "localhost",
			// Port:           "3306",
			DBName:         "test_app_default",
			NotPrintSql:    false,
			NotCreateTable: false,
		},
	}
	begin := time.Now()
	maxWait := time.Second * 30
	_, err = ConnectMySQL(conf.Config.Mysql)
	for err != nil {
		if time.Now().After(begin.Add(maxWait)) {
			panic("timeout for waiting mysql container ready")
		}
		println("waiting mysql container ready")
		time.Sleep(3 * time.Second)
		_, err = ConnectMySQL(conf.Config.Mysql)
	}
}

func TestInitDB(t *testing.T) {
	setLocalDBConfig()
	db = MustInitDB()
	app := &models.ExternalAPP{
		ID:       "123",
		ImageUrl: "12333",
		SrcUrl:   "32111",
		Desc:     "aabb",
		LabelInfo: models.LabelGroups{
			"labelA": {"a1", "a2"},
		},
		Members: stdsrv.Members{
			{
				Id:       "123",
				UserType: "123",
			},
		},
		IFrameCfg: map[string]any{
			"k1": 1.0,
			"k2": "1",
			"k3": true,
		},
	}
	if e := db.Save(app).Error; e != nil {
		t.Fatal(e)
	}
	got := new(models.ExternalAPP)
	got.ID = "123"

	t.Logf("want:\n" + toolkit.SprintPrettyJson(app))
	if err := db.Find(&got).Error; err != nil {
		t.Fatal(err)
	}
	t.Logf("got:\n" + toolkit.SprintPrettyJson(app))

	if diffs := utils.Diff(got, app); diffs != nil {
		t.Fatalf("diff between got and want: %s", strings.Join(diffs, "\n"))
	}

	if err := db.Delete(got).Error; err != nil {
		t.Fatal(err)
	}

	app, err := NewExternalAPPDB().GetExternalAPP("nonexist")
	if err == nil {
		t.Fatal("err should not be none")
	}

	is := gorm.IsRecordNotFoundError(err)
	if is {
		t.Fatal("unexpected result of IsRecordNotFoundError")
	} else {
		t.Logf("err %s is not jinzhu/gorm.RecordNotFoundError", err.Error())
	}
	is = errors.Is(gorm2.ErrRecordNotFound, err)
	if !is {
		t.Fatal("unexpected result of errors.Is(gorm2.ErrRecordNotFound,err)")
	} else {
		t.Logf("err %s is gorm.io/gorm.ErrRecordNotFound", err.Error())
	}
}
