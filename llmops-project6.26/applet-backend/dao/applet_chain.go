package dao

import (
	"context"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/go-faster/errors"
	"github.com/influxdata/kapacitor/uuid"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type AppletChainDAO struct {
	BaseDAO
}

type AppletChainParam struct {
	ID               *string // 应用链ID
	Name             *string // 应用链名字
	Creator          *string // 创建人
	Type             *int32  // 类型，0未知，1模板，2用户创建
	State            *int32  // 类型，0下线，1上线中，2上线
	Label            *string // 标签，map结构
	Chain            *string // 算子&依赖关系编排
	Desc             *string // 应用链描述
	DeployInfo       *string // 部署信息
	DebugInfo        *string // 调试信息
	ContainsSubChain *int32  //是否包含子链，0不包含，1包含
	ProjectID        *string // 项目ID
	LastDebugState   *int32  // 最后一次调试状态
	SourceInfo       *string // 来源信息
	MetricsInfo      *string // 打点信息，访问计数等
	UpdateTime       *int64  // 打点信息，访问计数等
}

type ChainQueryParam struct {
	FilterEmptyChain *bool //过滤无编排信息的应用链
}

func (a AppletChainDAO) GetByID(ctx context.Context, ID string) (*generated.AppletChain, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	return chainQuery.WithContext(ctx).Where(query.AppletChain.ID.Eq(ID)).First()
}

func (a AppletChainDAO) GetProjIds(ctx context.Context) ([]string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	appChains, err := chainQuery.WithContext(ctx).Distinct(query.AppletChain.ProjectID).Find()
	if err != nil {
		return nil, err
	}
	projIds := make([]string, 0)
	for _, appChain := range appChains {
		projIds = append(projIds, appChain.ProjectID)
	}
	return projIds, nil
}

func (a AppletChainDAO) BatchDeleteChain(ctx context.Context, IDs []string) error {
	chainQuery := a.getQueryOrDefault().AppletChain
	res, err := chainQuery.WithContext(ctx).Where(query.AppletChain.ID.In(IDs...)).Delete()
	if err != nil {
		stdlog.Errorf("delete chain by id err :%v", err)
		return stderr.Wrap(err, "delete chain by id err")
	}
	if res.Error != nil {
		stdlog.Errorf("delete chain by id err :%v", res.Error)
		return stderr.Wrap(res.Error, "delete chain by id err")
	}
	return nil
}

func (a AppletChainDAO) DeleteChain(ctx context.Context, ID string) error {
	chainQuery := a.getQueryOrDefault().AppletChain
	res, err := chainQuery.WithContext(ctx).Where(query.AppletChain.ID.Eq(ID)).Delete()
	if err != nil {
		stdlog.Errorf("delete chain by id err :%v", err)
		return err
	}
	if res.Error != nil {
		stdlog.Errorf("delete chain by id err :%v", res.Error)
		return res.Error
	}
	if res.RowsAffected != 1 {
		stdlog.Errorf("delete chain by id err , affect rows :%v", res.RowsAffected)
		return errors.Errorf("delete chain by id err , affect rows :%v", res.RowsAffected)
	}
	return nil
}

func (a AppletChainDAO) SaveChain(ctx context.Context, appletChain *generated.AppletChain) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	if appletChain.ID == "" {
		appletChain.ID = uuid.New().String()
	}
	err := chainQuery.WithContext(ctx).Create(appletChain)
	if err != nil {
		stdlog.Errorf("save chain basic info err :%v", err)
		return "", err
	}
	return appletChain.ID, nil
}

func (a AppletChainDAO) SaveBasicInfo(ctx context.Context, appletChain *generated.AppletChain) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	if appletChain.ID == "" {
		appletChain.ID = uuid.New().String()
	}
	appletChainCopy := *appletChain
	appletChainCopy.Chain = ""
	err := chainQuery.WithContext(ctx).Create(&appletChainCopy)
	if err != nil {
		stdlog.Errorf("save chain basic info err :%v", err)
		return "", err
	}
	return appletChain.ID, nil
}

func (a AppletChainDAO) UpsertBasicInfo(ctx context.Context, appletChain *generated.AppletChain) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	if appletChain.ID == "" {
		appletChain.ID = uuid.New().String()
	}
	err := chainQuery.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				UpdateAll: true,
			}).
		Omit(chainQuery.Chain).
		Create(appletChain)
	if err != nil {
		stdlog.Errorf("save chain basic info err :%v", err)
		return "", err
	}
	return appletChain.ID, nil
}

func (a AppletChainDAO) UpdateBasicInfo(ctx context.Context, chainID string, model *generated.AppletChain) (string, error) {
	model.ChainUpdatedTime = time.Now()
	chainQuery := a.getQueryOrDefault().AppletChain
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).Updates(model)
	if err != nil {
		stdlog.Errorf("update chain basic info err :%v", err)
		return "", err
	}
	return chainID, nil
}

func (a AppletChainDAO) UpdateByGenerated(ctx context.Context, chainID string, model *generated.AppletChain) error {
	chainQuery := a.getQueryOrDefault().AppletChain
	model.ID = ""
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).Updates(model)
	if err != nil {
		stdlog.Errorf("update chain :%v", err)
		return err
	}
	return nil
}

// UpdateBasicInfoV2 根据入参是否为指针，决定是否更新该项
func (a AppletChainDAO) UpdateBasicInfoV2(ctx context.Context, chainID string, model AppletChainParam) error {
	chainQuery := a.getQueryOrDefault().AppletChain
	updateMap := make(map[string]interface{})
	if model.ID != nil {
		updateMap["id"] = ptr.ToString(model.ID)
	}
	if model.Name != nil {
		updateMap["name"] = ptr.ToString(model.Name)
	}
	if model.Type != nil {
		updateMap["type"] = ptr.ToInt32(model.Type)
	}
	if model.Label != nil {
		updateMap["label"] = ptr.ToString(model.Label)
	}
	if model.Desc != nil {
		updateMap["desc"] = ptr.ToString(model.Desc)
	}
	if model.ProjectID != nil {
		updateMap["project_id"] = ptr.ToString(model.ProjectID)
	}
	if model.Chain != nil {
		updateMap["chain"] = ptr.ToString(model.Chain)
	}
	if model.DeployInfo != nil {
		updateMap["deploy_info"] = ptr.ToString(model.DeployInfo)
	}
	if model.DebugInfo != nil {
		updateMap["debug_info"] = ptr.ToString(model.DeployInfo)
	}
	if model.ContainsSubChain != nil {
		updateMap["contains_sub_chain"] = ptr.ToInt32(model.ContainsSubChain)
	}
	if model.State != nil {
		updateMap["state"] = ptr.ToInt32(model.State)
	}
	if model.LastDebugState != nil {
		updateMap["last_debug_state"] = ptr.ToInt32(model.LastDebugState)
	}
	if model.SourceInfo != nil {
		updateMap["source_info"] = ptr.ToString(model.SourceInfo)
	}
	if model.MetricsInfo != nil {
		updateMap["metrics_info"] = ptr.ToString(model.MetricsInfo)
	}
	if model.UpdateTime != nil {
		updateMap["chain_update_time"] = time.UnixMilli(ptr.ToInt64(model.UpdateTime))
	}
	_, err := chainQuery.Where(chainQuery.ID.Eq(chainID)).Updates(updateMap)
	if err != nil {
		stdlog.Errorf("update chain basic info err :%v", err)
		return err
	}
	return nil
}

func (a AppletChainDAO) UpdateState(ctx context.Context, chainID string, state int32) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).
		Updates(map[string]interface{}{"state": state})
	if err != nil {
		stdlog.Errorf("update chain state err :%v", err)
		return "", err
	}
	return chainID, nil
}

func (a AppletChainDAO) UpdateDeployInfo(ctx context.Context, chainID string, deploy string) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).
		Updates(map[string]interface{}{"deploy_info": deploy})
	if err != nil {
		stdlog.Errorf("update chain state err :%v", err)
		return "", err
	}
	return chainID, nil
}

// UpdateChain 不更新0值
func (a AppletChainDAO) UpdateChain(ctx context.Context, chainID string, model *generated.AppletChain) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).Updates(model)
	if err != nil {
		stdlog.Errorf("update chain info err :%v", err)
		return "", err
	}
	return chainID, nil
}

func (a AppletChainDAO) UpdateChainDetailInfo(ctx context.Context, chainID string, chainDetail string) (string, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	chainQuery.ID.ColumnName().String()
	_, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.Eq(chainID)).Updates(generated.AppletChain{
		Chain: chainDetail,
	})
	if err != nil {
		stdlog.Errorf("update chain detail info err :%v", err)
		return "", err
	}
	return chainID, nil
}

func (a AppletChainDAO) ListByIDs(ctx context.Context, IDs []string) ([]*generated.AppletChain, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	chains, err := chainQuery.WithContext(ctx).Where(chainQuery.ID.In(IDs...)).Find()
	if err != nil {
		return nil, err
	}
	return chains, nil
}
func (a AppletChainDAO) ListByName(ctx context.Context, chainName string) ([]*generated.AppletChain, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	chains, err := chainQuery.WithContext(ctx).Where(chainQuery.Name.Eq(chainName)).Find()
	if err != nil {
		return nil, err
	}
	return chains, nil
}

// Query 通用查询
func (a AppletChainDAO) Query(ctx context.Context, chain *generated.AppletChain) ([]*generated.AppletChain, error) {
	var excludeFields []field.Expr
	cods, err := a.BuildCondition(chain)
	if err != nil {
		return nil, err
	}
	return a.ConditionalQuery(ctx, cods, excludeFields)
}

// SimpleInfoQuery 不查询应用链编排和智能体配置信息
// param 查询条件，可为nil
func (a AppletChainDAO) SimpleInfoQuery(ctx context.Context, chain *generated.AppletChain, param *ChainQueryParam) ([]*generated.AppletChain, error) {
	appletChain := a.getQueryOrDefault().AppletChain
	excludeFields := []field.Expr{appletChain.Chain, appletChain.AssistantInfo}
	cods, err := a.BuildCondition(chain)
	if err != nil {
		return nil, err
	}
	if param != nil {
		if ptr.ToBool(param.FilterEmptyChain) {
			cods = append(cods, a.getQueryOrDefault().AppletChain.Chain.Neq(""))
		}
	}
	return a.ConditionalQuery(ctx, cods, excludeFields)
}

func (a AppletChainDAO) BuildCondition(chain *generated.AppletChain) ([]gen.Condition, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	cods := make([]gen.Condition, 0)
	if chain != nil {
		if chain.ID != "" {
			cods = append(cods, chainQuery.ID.Eq(chain.ID))
		}
		if chain.Name != "" {
			cods = append(cods, chainQuery.Name.Eq(chain.Name))
		}
		if chain.CreatedType != "" {
			cods = append(cods, chainQuery.CreatedType.Eq(chain.CreatedType))
		}
		if chain.ProjectID != "" {
			cods = append(cods, chainQuery.ProjectID.Eq(chain.ProjectID))
		}
		if chain.Creator != "" {
			cods = append(cods, chainQuery.Creator.Eq(chain.Creator))
		}
	}
	return cods, nil
}

func (a AppletChainDAO) ConditionalQuery(ctx context.Context, cods []gen.Condition,
	excludeFields []field.Expr) ([]*generated.AppletChain, error) {
	chainQuery := a.getQueryOrDefault().AppletChain
	chains, err := chainQuery.WithContext(ctx).Omit(excludeFields...).Where(cods...).Find()
	if err != nil {
		return nil, err
	}
	return chains, nil

}
