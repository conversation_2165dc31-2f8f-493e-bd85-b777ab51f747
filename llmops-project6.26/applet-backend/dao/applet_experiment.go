package dao

import (
	"context"

	"gorm.io/gen"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type AppletExperimentDAO struct {
	BaseDAO
}

// ExperimentQueryParam 应用体验常用查询条件
type ExperimentQueryParam struct {
	ChainID   *string `json:"chain_id" description:"相关的应用链id"`
	Name      *string `json:"name"  description:"体验名称"`
	Creator   *string `json:"creator" description:"创建者"`
	ProjectID *string `json:"project_id" description:"算子所属项目,默认为当前项目"`
	Status    *string `json:"status"  description:"应用体验是否已发布"`
	CreatedBy *string `json:"created_by" description:"创建途径"`
}

func (a AppletExperimentDAO) Create(ctx context.Context, experiment *generated.AppletExperiment) (string, error) {
	query := a.getQueryOrDefault().AppletExperiment
	if err := query.WithContext(ctx).Create(experiment); err != nil {
		return "", err
	}
	return experiment.ChainID, nil
}
func (a AppletExperimentDAO) GetByID(ctx context.Context, chainID string) (*generated.AppletExperiment, error) {
	query := a.getQueryOrDefault().AppletExperiment
	return query.WithContext(ctx).Where(query.ChainID.Eq(chainID)).First()
}
func (a AppletExperimentDAO) DeleteByID(ctx context.Context, chainID string) (string, error) {
	query := a.getQueryOrDefault().AppletExperiment
	if _, err := query.WithContext(ctx).Where(query.ChainID.Eq(chainID)).Delete(); err != nil {
		return "", err
	}
	return chainID, nil
}
func (a AppletExperimentDAO) List(ctx context.Context,
	queryParam *ExperimentQueryParam) ([]*generated.AppletExperiment, error) {
	query := a.getQueryOrDefault().AppletExperiment
	cods := make([]gen.Condition, 0)
	if queryParam.ChainID != nil {
		cods = append(cods, query.ChainID.Eq(*queryParam.ChainID))
	}
	if queryParam.Name != nil {
		cods = append(cods, query.Name.Eq(*queryParam.Name))
	}

	if queryParam.Creator != nil {
		cods = append(cods, query.Creator.Eq(*queryParam.Creator))
	}
	if queryParam.ProjectID != nil {
		cods = append(cods, query.ProjectID.Eq(*queryParam.ProjectID))
	}
	if queryParam.Status != nil {
		cods = append(cods, query.Status.Eq(*queryParam.Status))
	}
	if queryParam.CreatedBy != nil {
		cods = append(cods, query.CreatedBy.Eq(*queryParam.CreatedBy))
	}
	res, err := query.WithContext(ctx).Where(cods...).Find()
	return res, err
}

// Update 更新体验，不更新0值
func (a AppletExperimentDAO) Update(ctx context.Context, chainID string, experiment *generated.AppletExperiment) error {
	query := a.getQueryOrDefault().AppletExperiment
	_, err := query.WithContext(ctx).Where(query.ChainID.Eq(chainID)).Updates(experiment)
	if err != nil {
		return err
	}
	return nil
}
