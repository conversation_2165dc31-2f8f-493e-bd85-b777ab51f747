package dao

import (
	"context"
	"fmt"
	"testing"
	"time"

	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

// TestFindOrphanedChainDebugHistories 验证查询孤立调试记录的功能
func TestFindOrphanedChainDebugHistories(t *testing.T) {
	// 执行查询
	SetDBConfig()
	start := time.Now()
	db := MustInitDBForTest()
	query.SetDefault(db)
	ChainDebugHistoryDAOImpl := NewChainDebugHistoryDAO(query.Q)
	elapsed := time.Since(start)
	fmt.Printf("创建连接耗时：%v\n", elapsed)

	orphanHis, err := ChainDebugHistoryDAOImpl.ListOrphanDebugs(context.Background())

	start2 := time.Now()
	elapsed2 := time.Since(start2)
	fmt.Printf("查询耗时：%v\n", elapsed2)

	if err != nil {
		t.Fatal("查询失败：", err)
	}

	for _, r := range orphanHis {
		fmt.Println(r)
	}
}

func (c ChainDebugDAO) ListOrphanDebugs(ctx context.Context) ([]*generated.ChainDebugHistory, error) {
	q := c.getQueryOrDefault()
	ac := q.AppletChain       // 对应 applet_chain 表
	dh := q.ChainDebugHistory // 对应 chain_debug_histories 表

	// LEFT JOIN applet_chain ON dh.chain_id = ac.id
	// WHERE ac.id IS NULL  → 找到所有没有对应 applet_chain 的 dh 记录
	histories, err := dh.
		WithContext(ctx).
		LeftJoin(ac, dh.ChainID.EqCol(ac.ID)).
		Where(ac.ID.IsNull()).
		Find()
	if err != nil {
		return nil, err
	}
	return histories, nil
}

func TestCleanOrphanDebugsOlderThan(t *testing.T) {
	// 执行查询
	SetDBConfig()
	start := time.Now()
	db := MustInitDBForTest()
	query.SetDefault(db)
	ChainDebugHistoryDAOImpl := NewChainDebugHistoryDAO(query.Q)
	elapsed := time.Since(start)
	fmt.Printf("创建连接耗时：%v\n", elapsed)

	err := ChainDebugHistoryDAOImpl.CleanOrphanDebugsOlderThan(context.Background(), 24)

	start2 := time.Now()
	elapsed2 := time.Since(start2)
	fmt.Printf("删除耗时：%v\n", elapsed2)

	if err != nil {
		t.Fatal("删除失败：", err)
	}
}
