package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/go-faster/errors"
	"github.com/influxdata/kapacitor/uuid"
	"gorm.io/gen"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type ChainDebugDAO struct {
	BaseDAO
}

type ChainLastDebugState struct {
	ChainID    string    `json:"chain_id"`
	LastState  int32     `json:"last_state"`
	CreateTime time.Time `json:"create_time"`
}

type ChainDebugParam struct {
	// 这里是chatId，作为表的primary key
	ID *string
	// 应用链ID
	ChainID *string
	// 应用链快照
	ChainSnapshot *string
	// 应用链状态
	State *int32
	// 应用链状态，可以填多个，用于筛选
	States *[]int32
	// 创建人
	Creator *string
	// 项目ID
	ProjectID *string
	// 调试详情
	DebugMessage *string
	// 调试记录名称
	DebugName *string
	// 时间范围开始
	FromTime *time.Time
	// 时间范围结束
	ToTime *time.Time
	// 页长
	PageSize *int
	// 页码
	Page *int
}

func (c ChainDebugDAO) GetByID(ctx context.Context, ID string) (*generated.ChainDebugHistory, error) {
	q := c.getQueryOrDefault().ChainDebugHistory
	return q.WithContext(ctx).Where(q.ID.Eq(ID)).First()
}

func (c ChainDebugDAO) DeleteByID(ctx context.Context, ID string) error {
	q := c.getQueryOrDefault().ChainDebugHistory
	res, err := q.WithContext(ctx).Where(q.ID.Eq(ID)).Delete()
	if err != nil {
		stdlog.Errorf("delete chainDebugHistory by id err :%v", err)
		return err
	}
	if res.Error != nil {
		stdlog.Errorf("delete chainDebugHistory by id err :%v", res.Error)
		return res.Error
	}
	if res.RowsAffected != 1 {
		stdlog.Errorf("delete chainDebugHistory by id err , affect rows :%v", res.RowsAffected)
		return errors.Errorf("delete chainDebugHistory by id err , affect rows :%v", res.RowsAffected)
	}
	return nil
}

func (c ChainDebugDAO) DeleteByChainID(ctx context.Context, chainID string) error {
	if chainID == "" {
		return nil
	}

	q := c.getQueryOrDefault().ChainDebugHistory
	res, err := q.WithContext(ctx).Where(q.ChainID.Eq(chainID)).Delete()

	// 优先检查Delete()调用本身的错误
	if err != nil {
		stdlog.Errorf("删除链调试历史失败: %v", err)
		return stderr.Wrap(err, "删除链调试历史失败，chainID: %v", chainID)
	}

	// 再检查GORM操作结果中的错误
	if res.Error != nil {
		stdlog.Errorf("GORM删除链调试历史失败: %v", res.Error)
		return stderr.Wrap(res.Error, "GORM删除链调试历史失败，chainID: %v", chainID)
	}

	if res.RowsAffected == 0 {
		stdlog.Warnf("未找到匹配的调试历史记录，chainID: %v", chainID)
		return nil
	}

	return nil
}

func (c ChainDebugDAO) BatchDelete(ctx context.Context, chainIDs []string) error {
	if len(chainIDs) == 0 {
		return nil
	}

	q := c.getQueryOrDefault().ChainDebugHistory
	res, err := q.WithContext(ctx).Where(q.ChainID.In(chainIDs...)).Delete()

	// 优先检查Delete()调用本身的错误
	if err != nil {
		stdlog.Errorf("批量删除链调试历史失败: %v", err)
		return stderr.Wrap(err, "批量删除链调试历史失败，chainIDs: %v", chainIDs)
	}

	// 再检查GORM操作结果中的错误
	if res.Error != nil {
		stdlog.Errorf("GORM批量删除链调试历史失败: %v", res.Error)
		return stderr.Wrap(res.Error, "GORM批量删除链调试历史失败，chainIDs: %v", chainIDs)
	}

	if res.RowsAffected == 0 {
		stdlog.Warnf("未找到匹配的调试历史记录，chainIDs: %v", chainIDs)
		return nil
	}

	return nil
}

// 删除 {hours} 前创建的，且 chain_id 是 applet_chain 表中不存在的，ChainDebughistories
func (c ChainDebugDAO) CleanOrphanDebugsOlderThan(ctx context.Context, hours int) error {
	cutoff := time.Now().Add(-time.Duration(hours) * time.Hour)

	q := c.getQueryOrDefault()
	dh := q.ChainDebugHistory // 对应 chain_debug_histories
	ac := q.AppletChain       // 对应 applet_chain

	batchSize := 1000
	for {
		// 1) 分批用 LEFT JOIN + IS NULL + 时间过滤 拿出要删除的 IDs
		var ids []string
		err := dh.
			WithContext(ctx).
			Select(dh.ID).                         // 只取 ID 列
			LeftJoin(ac, dh.ChainID.EqCol(ac.ID)). // LEFT JOIN applet_chain ac ON dh.chain_id = ac.id
			Where(ac.ID.IsNull()).                 // ac.id IS NULL → orphan
			Where(dh.CreateTime.Lte(cutoff)).      // 且 create_time <= cutoff
			Limit(batchSize).                      // 分批
			Pluck(dh.ID, &ids)                     // 把 DH.ID 的结果收集到 ids 切片
		if err != nil {
			return fmt.Errorf("查询需清理的 orphan Debug IDs 失败: %w", err)
		}
		if len(ids) == 0 {
			break
		}

		// 2) 分批删除
		res, err := dh.
			WithContext(ctx).
			Where(dh.ID.In(ids...)). // DELETE WHERE id IN (ids...)
			Delete()
		if err != nil {
			return fmt.Errorf("删除 orphan 调试记录失败: %w", err)
		}
		if res.Error != nil {
			return fmt.Errorf("GORM 删除 orphan 调试记录失败: %w", res.Error)
		}
		if res.RowsAffected < int64(batchSize) {
			break
		}
	}

	return nil
}

func (c ChainDebugDAO) List(ctx context.Context, model ChainDebugParam) ([]*generated.ChainDebugHistory, int64, error) {
	q := c.getQueryOrDefault().ChainDebugHistory
	cods := make([]gen.Condition, 0)
	if model.ID != nil {
		cods = append(cods, q.ID.Eq(*model.ID))
	}
	if model.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*model.ProjectID))
	}
	if model.States != nil && len(*model.States) > 0 {
		cods = append(cods, q.State.In(*model.States...))
	}
	if model.ChainID != nil {
		cods = append(cods, q.ChainID.Eq(*model.ChainID))
	}
	if model.Creator != nil {
		cods = append(cods, q.Creator.Eq(*model.Creator))
	}
	if model.DebugName != nil && *model.DebugName != "" {
		cods = append(cods, q.DebugName.Like("%"+*model.DebugName+"%"))
	}
	if model.FromTime != nil {
		cods = append(cods, q.CreateTime.Gte(*model.FromTime))
	}
	if model.ToTime != nil {
		cods = append(cods, q.CreateTime.Lte(*model.ToTime))
	}

	// 筛选掉 DebugMessage，DebugName，ChainSnapshotID 为空的记录（不符合规范）
	cods = append(cods,
		q.DebugMessage.IsNotNull(),
		q.DebugMessage.Neq(""),
		q.DebugName.IsNotNull(),
		q.DebugName.Neq(""),
		q.ChainSnapshotID.IsNotNull(),
		q.ChainSnapshotID.Neq(0),
	)

	// 过滤掉状态为 Running 和 Init 的调试记录
	cods = append(cods, q.State.NotIn(models.ChainDebugStateRunning.Code, models.ChainDebugStateInit.Code))

	query := q.WithContext(ctx).Where(cods...)

	query = query.Order(q.CreateTime.Desc())

	total, err := query.Count()

	if err != nil {
		return nil, 0, err
	}

	// 分页逻辑
	if model.PageSize != nil && model.Page != nil {
		pageSize := *model.PageSize
		page := *model.Page
		if page < 1 {
			page = 1
		}
		query = query.Limit(pageSize).Offset((page - 1) * pageSize)
	}

	historyRecords, err := query.Find()

	if err != nil {
		return nil, 0, err
	}

	return historyRecords, total, nil
}

func (c ChainDebugDAO) UpdateByID(ctx context.Context, ID string, model ChainDebugParam) error {
	q := c.getQueryOrDefault().ChainDebugHistory
	updateMap := make(map[string]interface{})
	if model.ProjectID != nil {
		updateMap["project_id"] = *model.ProjectID
	}
	if model.State != nil {
		updateMap["state"] = *model.State
	}
	if model.ChainID != nil {
		updateMap["chain_id"] = *model.ChainID
	}
	if model.Creator != nil {
		updateMap["creator"] = *model.Creator
	}
	if model.ChainSnapshot != nil {
		updateMap["chain_snapshot"] = *model.ChainSnapshot
	}
	if model.DebugMessage != nil {
		updateMap["debug_message"] = *model.DebugMessage
	}

	_, err := q.Where(q.ID.Eq(ID)).Updates(updateMap)
	return err
}

func (c ChainDebugDAO) Save(ctx context.Context, model *generated.ChainDebugHistory) (string, error) {
	ID := uuid.New().String()
	model.ID = ID
	if err := c.getQueryOrDefault().ChainDebugHistory.WithContext(ctx).Save(model); err != nil {
		return "", err
	}
	return ID, nil
}

func (c ChainDebugDAO) ListLastDebugState(ctx context.Context) ([]*ChainLastDebugState, error) {
	type ChainDebugState struct {
		ChainID    string    `json:"chain_id"`
		State      int32     `json:"state"`
		CreateTime time.Time `json:"create_time"`
	}
	scanRes := make([]*ChainDebugState, 0)
	res := make([]*ChainLastDebugState, 0)
	ch := c.getQueryOrDefault().ChainDebugHistory
	err := ch.Where(ch.ProjectID.Eq(helper.GetProjectID(ctx))).Order(ch.CreateTime.Desc()).Scan(&scanRes)
	if err != nil {
		return nil, err
	}
	m := make(map[string]*ChainLastDebugState)
	for _, r := range scanRes {
		if _, ok := m[r.ChainID]; !ok {
			m[r.ChainID] = &ChainLastDebugState{
				ChainID:    r.ChainID,
				LastState:  r.State,
				CreateTime: r.CreateTime,
			}
		}
	}
	for _, v := range m {
		res = append(res, v)
	}
	return res, nil
}

func (c ChainDebugDAO) GetLastDebugStateByChainID(ctx context.Context, chainID string) (int32, error) {
	ch := c.getQueryOrDefault().ChainDebugHistory
	records, err := ch.Where(ch.ChainID.Eq(chainID)).Order(ch.CreateTime.Desc()).Find()
	if err != nil {
		return 0, err
	}
	if len(records) == 0 {
		return 0, nil
	}
	return records[0].State, nil
}
