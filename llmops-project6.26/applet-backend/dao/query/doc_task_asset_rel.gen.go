// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newDocTaskAssetRel(db *gorm.DB, opts ...gen.DOOption) docTaskAssetRel {
	_docTaskAssetRel := docTaskAssetRel{}

	_docTaskAssetRel.docTaskAssetRelDo.UseDB(db, opts...)
	_docTaskAssetRel.docTaskAssetRelDo.UseModel(&models.DocTaskAssetRel{})

	tableName := _docTaskAssetRel.docTaskAssetRelDo.TableName()
	_docTaskAssetRel.ALL = field.NewAsterisk(tableName)
	_docTaskAssetRel.TaskID = field.NewString(tableName, "task_id")
	_docTaskAssetRel.AssetID = field.NewString(tableName, "asset_id")
	_docTaskAssetRel.KbID = field.NewString(tableName, "kb_id")
	_docTaskAssetRel.DocShortID = field.NewInt64(tableName, "doc_short_id")
	_docTaskAssetRel.Document = docTaskAssetRelBelongsToDocument{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Document", "models.Document"),
	}

	_docTaskAssetRel.fillFieldMap()

	return _docTaskAssetRel
}

type docTaskAssetRel struct {
	docTaskAssetRelDo

	ALL        field.Asterisk
	TaskID     field.String
	AssetID    field.String
	KbID       field.String
	DocShortID field.Int64
	Document   docTaskAssetRelBelongsToDocument

	fieldMap map[string]field.Expr
}

func (d docTaskAssetRel) Table(newTableName string) *docTaskAssetRel {
	d.docTaskAssetRelDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d docTaskAssetRel) As(alias string) *docTaskAssetRel {
	d.docTaskAssetRelDo.DO = *(d.docTaskAssetRelDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *docTaskAssetRel) updateTableName(table string) *docTaskAssetRel {
	d.ALL = field.NewAsterisk(table)
	d.TaskID = field.NewString(table, "task_id")
	d.AssetID = field.NewString(table, "asset_id")
	d.KbID = field.NewString(table, "kb_id")
	d.DocShortID = field.NewInt64(table, "doc_short_id")

	d.fillFieldMap()

	return d
}

func (d *docTaskAssetRel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *docTaskAssetRel) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 5)
	d.fieldMap["task_id"] = d.TaskID
	d.fieldMap["asset_id"] = d.AssetID
	d.fieldMap["kb_id"] = d.KbID
	d.fieldMap["doc_short_id"] = d.DocShortID

}

func (d docTaskAssetRel) clone(db *gorm.DB) docTaskAssetRel {
	d.docTaskAssetRelDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d docTaskAssetRel) replaceDB(db *gorm.DB) docTaskAssetRel {
	d.docTaskAssetRelDo.ReplaceDB(db)
	return d
}

type docTaskAssetRelBelongsToDocument struct {
	db *gorm.DB

	field.RelationField
}

func (a docTaskAssetRelBelongsToDocument) Where(conds ...field.Expr) *docTaskAssetRelBelongsToDocument {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a docTaskAssetRelBelongsToDocument) WithContext(ctx context.Context) *docTaskAssetRelBelongsToDocument {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a docTaskAssetRelBelongsToDocument) Session(session *gorm.Session) *docTaskAssetRelBelongsToDocument {
	a.db = a.db.Session(session)
	return &a
}

func (a docTaskAssetRelBelongsToDocument) Model(m *models.DocTaskAssetRel) *docTaskAssetRelBelongsToDocumentTx {
	return &docTaskAssetRelBelongsToDocumentTx{a.db.Model(m).Association(a.Name())}
}

type docTaskAssetRelBelongsToDocumentTx struct{ tx *gorm.Association }

func (a docTaskAssetRelBelongsToDocumentTx) Find() (result *models.Document, err error) {
	return result, a.tx.Find(&result)
}

func (a docTaskAssetRelBelongsToDocumentTx) Append(values ...*models.Document) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a docTaskAssetRelBelongsToDocumentTx) Replace(values ...*models.Document) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a docTaskAssetRelBelongsToDocumentTx) Delete(values ...*models.Document) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a docTaskAssetRelBelongsToDocumentTx) Clear() error {
	return a.tx.Clear()
}

func (a docTaskAssetRelBelongsToDocumentTx) Count() int64 {
	return a.tx.Count()
}

type docTaskAssetRelDo struct{ gen.DO }

type IDocTaskAssetRelDo interface {
	gen.SubQuery
	Debug() IDocTaskAssetRelDo
	WithContext(ctx context.Context) IDocTaskAssetRelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDocTaskAssetRelDo
	WriteDB() IDocTaskAssetRelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDocTaskAssetRelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDocTaskAssetRelDo
	Not(conds ...gen.Condition) IDocTaskAssetRelDo
	Or(conds ...gen.Condition) IDocTaskAssetRelDo
	Select(conds ...field.Expr) IDocTaskAssetRelDo
	Where(conds ...gen.Condition) IDocTaskAssetRelDo
	Order(conds ...field.Expr) IDocTaskAssetRelDo
	Distinct(cols ...field.Expr) IDocTaskAssetRelDo
	Omit(cols ...field.Expr) IDocTaskAssetRelDo
	Join(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo
	Group(cols ...field.Expr) IDocTaskAssetRelDo
	Having(conds ...gen.Condition) IDocTaskAssetRelDo
	Limit(limit int) IDocTaskAssetRelDo
	Offset(offset int) IDocTaskAssetRelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDocTaskAssetRelDo
	Unscoped() IDocTaskAssetRelDo
	Create(values ...*models.DocTaskAssetRel) error
	CreateInBatches(values []*models.DocTaskAssetRel, batchSize int) error
	Save(values ...*models.DocTaskAssetRel) error
	First() (*models.DocTaskAssetRel, error)
	Take() (*models.DocTaskAssetRel, error)
	Last() (*models.DocTaskAssetRel, error)
	Find() ([]*models.DocTaskAssetRel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocTaskAssetRel, err error)
	FindInBatches(result *[]*models.DocTaskAssetRel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.DocTaskAssetRel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDocTaskAssetRelDo
	Assign(attrs ...field.AssignExpr) IDocTaskAssetRelDo
	Joins(fields ...field.RelationField) IDocTaskAssetRelDo
	Preload(fields ...field.RelationField) IDocTaskAssetRelDo
	FirstOrInit() (*models.DocTaskAssetRel, error)
	FirstOrCreate() (*models.DocTaskAssetRel, error)
	FindByPage(offset int, limit int) (result []*models.DocTaskAssetRel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDocTaskAssetRelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d docTaskAssetRelDo) Debug() IDocTaskAssetRelDo {
	return d.withDO(d.DO.Debug())
}

func (d docTaskAssetRelDo) WithContext(ctx context.Context) IDocTaskAssetRelDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d docTaskAssetRelDo) ReadDB() IDocTaskAssetRelDo {
	return d.Clauses(dbresolver.Read)
}

func (d docTaskAssetRelDo) WriteDB() IDocTaskAssetRelDo {
	return d.Clauses(dbresolver.Write)
}

func (d docTaskAssetRelDo) Session(config *gorm.Session) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Session(config))
}

func (d docTaskAssetRelDo) Clauses(conds ...clause.Expression) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d docTaskAssetRelDo) Returning(value interface{}, columns ...string) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d docTaskAssetRelDo) Not(conds ...gen.Condition) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d docTaskAssetRelDo) Or(conds ...gen.Condition) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d docTaskAssetRelDo) Select(conds ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d docTaskAssetRelDo) Where(conds ...gen.Condition) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d docTaskAssetRelDo) Order(conds ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d docTaskAssetRelDo) Distinct(cols ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d docTaskAssetRelDo) Omit(cols ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d docTaskAssetRelDo) Join(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d docTaskAssetRelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d docTaskAssetRelDo) RightJoin(table schema.Tabler, on ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d docTaskAssetRelDo) Group(cols ...field.Expr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d docTaskAssetRelDo) Having(conds ...gen.Condition) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d docTaskAssetRelDo) Limit(limit int) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d docTaskAssetRelDo) Offset(offset int) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d docTaskAssetRelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d docTaskAssetRelDo) Unscoped() IDocTaskAssetRelDo {
	return d.withDO(d.DO.Unscoped())
}

func (d docTaskAssetRelDo) Create(values ...*models.DocTaskAssetRel) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d docTaskAssetRelDo) CreateInBatches(values []*models.DocTaskAssetRel, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d docTaskAssetRelDo) Save(values ...*models.DocTaskAssetRel) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d docTaskAssetRelDo) First() (*models.DocTaskAssetRel, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTaskAssetRel), nil
	}
}

func (d docTaskAssetRelDo) Take() (*models.DocTaskAssetRel, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTaskAssetRel), nil
	}
}

func (d docTaskAssetRelDo) Last() (*models.DocTaskAssetRel, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTaskAssetRel), nil
	}
}

func (d docTaskAssetRelDo) Find() ([]*models.DocTaskAssetRel, error) {
	result, err := d.DO.Find()
	return result.([]*models.DocTaskAssetRel), err
}

func (d docTaskAssetRelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocTaskAssetRel, err error) {
	buf := make([]*models.DocTaskAssetRel, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d docTaskAssetRelDo) FindInBatches(result *[]*models.DocTaskAssetRel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d docTaskAssetRelDo) Attrs(attrs ...field.AssignExpr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d docTaskAssetRelDo) Assign(attrs ...field.AssignExpr) IDocTaskAssetRelDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d docTaskAssetRelDo) Joins(fields ...field.RelationField) IDocTaskAssetRelDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d docTaskAssetRelDo) Preload(fields ...field.RelationField) IDocTaskAssetRelDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d docTaskAssetRelDo) FirstOrInit() (*models.DocTaskAssetRel, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTaskAssetRel), nil
	}
}

func (d docTaskAssetRelDo) FirstOrCreate() (*models.DocTaskAssetRel, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTaskAssetRel), nil
	}
}

func (d docTaskAssetRelDo) FindByPage(offset int, limit int) (result []*models.DocTaskAssetRel, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d docTaskAssetRelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d docTaskAssetRelDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d docTaskAssetRelDo) Delete(models ...*models.DocTaskAssetRel) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *docTaskAssetRelDo) withDO(do gen.Dao) *docTaskAssetRelDo {
	d.DO = *do.(*gen.DO)
	return d
}
