// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newSafetyConfig(db *gorm.DB, opts ...gen.DOOption) safetyConfig {
	_safetyConfig := safetyConfig{}

	_safetyConfig.safetyConfigDo.UseDB(db, opts...)
	_safetyConfig.safetyConfigDo.UseModel(&generated.SafetyConfig{})

	tableName := _safetyConfig.safetyConfigDo.TableName()
	_safetyConfig.ALL = field.NewAsterisk(tableName)
	_safetyConfig.ID = field.NewString(tableName, "id")
	_safetyConfig.ProjectID = field.NewString(tableName, "project_id")
	_safetyConfig.InputGuardrails = field.NewString(tableName, "input_guardrails")
	_safetyConfig.OutputGuardrails = field.NewString(tableName, "output_guardrails")

	_safetyConfig.fillFieldMap()

	return _safetyConfig
}

type safetyConfig struct {
	safetyConfigDo

	ALL              field.Asterisk
	ID               field.String
	ProjectID        field.String
	InputGuardrails  field.String
	OutputGuardrails field.String

	fieldMap map[string]field.Expr
}

func (s safetyConfig) Table(newTableName string) *safetyConfig {
	s.safetyConfigDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s safetyConfig) As(alias string) *safetyConfig {
	s.safetyConfigDo.DO = *(s.safetyConfigDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *safetyConfig) updateTableName(table string) *safetyConfig {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewString(table, "id")
	s.ProjectID = field.NewString(table, "project_id")
	s.InputGuardrails = field.NewString(table, "input_guardrails")
	s.OutputGuardrails = field.NewString(table, "output_guardrails")

	s.fillFieldMap()

	return s
}

func (s *safetyConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *safetyConfig) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 4)
	s.fieldMap["id"] = s.ID
	s.fieldMap["project_id"] = s.ProjectID
	s.fieldMap["input_guardrails"] = s.InputGuardrails
	s.fieldMap["output_guardrails"] = s.OutputGuardrails
}

func (s safetyConfig) clone(db *gorm.DB) safetyConfig {
	s.safetyConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s safetyConfig) replaceDB(db *gorm.DB) safetyConfig {
	s.safetyConfigDo.ReplaceDB(db)
	return s
}

type safetyConfigDo struct{ gen.DO }

type ISafetyConfigDo interface {
	gen.SubQuery
	Debug() ISafetyConfigDo
	WithContext(ctx context.Context) ISafetyConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISafetyConfigDo
	WriteDB() ISafetyConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISafetyConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISafetyConfigDo
	Not(conds ...gen.Condition) ISafetyConfigDo
	Or(conds ...gen.Condition) ISafetyConfigDo
	Select(conds ...field.Expr) ISafetyConfigDo
	Where(conds ...gen.Condition) ISafetyConfigDo
	Order(conds ...field.Expr) ISafetyConfigDo
	Distinct(cols ...field.Expr) ISafetyConfigDo
	Omit(cols ...field.Expr) ISafetyConfigDo
	Join(table schema.Tabler, on ...field.Expr) ISafetyConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISafetyConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISafetyConfigDo
	Group(cols ...field.Expr) ISafetyConfigDo
	Having(conds ...gen.Condition) ISafetyConfigDo
	Limit(limit int) ISafetyConfigDo
	Offset(offset int) ISafetyConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISafetyConfigDo
	Unscoped() ISafetyConfigDo
	Create(values ...*generated.SafetyConfig) error
	CreateInBatches(values []*generated.SafetyConfig, batchSize int) error
	Save(values ...*generated.SafetyConfig) error
	First() (*generated.SafetyConfig, error)
	Take() (*generated.SafetyConfig, error)
	Last() (*generated.SafetyConfig, error)
	Find() ([]*generated.SafetyConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.SafetyConfig, err error)
	FindInBatches(result *[]*generated.SafetyConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.SafetyConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISafetyConfigDo
	Assign(attrs ...field.AssignExpr) ISafetyConfigDo
	Joins(fields ...field.RelationField) ISafetyConfigDo
	Preload(fields ...field.RelationField) ISafetyConfigDo
	FirstOrInit() (*generated.SafetyConfig, error)
	FirstOrCreate() (*generated.SafetyConfig, error)
	FindByPage(offset int, limit int) (result []*generated.SafetyConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISafetyConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s safetyConfigDo) Debug() ISafetyConfigDo {
	return s.withDO(s.DO.Debug())
}

func (s safetyConfigDo) WithContext(ctx context.Context) ISafetyConfigDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s safetyConfigDo) ReadDB() ISafetyConfigDo {
	return s.Clauses(dbresolver.Read)
}

func (s safetyConfigDo) WriteDB() ISafetyConfigDo {
	return s.Clauses(dbresolver.Write)
}

func (s safetyConfigDo) Session(config *gorm.Session) ISafetyConfigDo {
	return s.withDO(s.DO.Session(config))
}

func (s safetyConfigDo) Clauses(conds ...clause.Expression) ISafetyConfigDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s safetyConfigDo) Returning(value interface{}, columns ...string) ISafetyConfigDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s safetyConfigDo) Not(conds ...gen.Condition) ISafetyConfigDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s safetyConfigDo) Or(conds ...gen.Condition) ISafetyConfigDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s safetyConfigDo) Select(conds ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s safetyConfigDo) Where(conds ...gen.Condition) ISafetyConfigDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s safetyConfigDo) Order(conds ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s safetyConfigDo) Distinct(cols ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s safetyConfigDo) Omit(cols ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s safetyConfigDo) Join(table schema.Tabler, on ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s safetyConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s safetyConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s safetyConfigDo) Group(cols ...field.Expr) ISafetyConfigDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s safetyConfigDo) Having(conds ...gen.Condition) ISafetyConfigDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s safetyConfigDo) Limit(limit int) ISafetyConfigDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s safetyConfigDo) Offset(offset int) ISafetyConfigDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s safetyConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISafetyConfigDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s safetyConfigDo) Unscoped() ISafetyConfigDo {
	return s.withDO(s.DO.Unscoped())
}

func (s safetyConfigDo) Create(values ...*generated.SafetyConfig) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s safetyConfigDo) CreateInBatches(values []*generated.SafetyConfig, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s safetyConfigDo) Save(values ...*generated.SafetyConfig) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s safetyConfigDo) First() (*generated.SafetyConfig, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.SafetyConfig), nil
	}
}

func (s safetyConfigDo) Take() (*generated.SafetyConfig, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.SafetyConfig), nil
	}
}

func (s safetyConfigDo) Last() (*generated.SafetyConfig, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.SafetyConfig), nil
	}
}

func (s safetyConfigDo) Find() ([]*generated.SafetyConfig, error) {
	result, err := s.DO.Find()
	return result.([]*generated.SafetyConfig), err
}

func (s safetyConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.SafetyConfig, err error) {
	buf := make([]*generated.SafetyConfig, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s safetyConfigDo) FindInBatches(result *[]*generated.SafetyConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s safetyConfigDo) Attrs(attrs ...field.AssignExpr) ISafetyConfigDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s safetyConfigDo) Assign(attrs ...field.AssignExpr) ISafetyConfigDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s safetyConfigDo) Joins(fields ...field.RelationField) ISafetyConfigDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s safetyConfigDo) Preload(fields ...field.RelationField) ISafetyConfigDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s safetyConfigDo) FirstOrInit() (*generated.SafetyConfig, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.SafetyConfig), nil
	}
}

func (s safetyConfigDo) FirstOrCreate() (*generated.SafetyConfig, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.SafetyConfig), nil
	}
}

func (s safetyConfigDo) FindByPage(offset int, limit int) (result []*generated.SafetyConfig, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s safetyConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s safetyConfigDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s safetyConfigDo) Delete(models ...*generated.SafetyConfig) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *safetyConfigDo) withDO(do gen.Dao) *safetyConfigDo {
	s.DO = *do.(*gen.DO)
	return s
}
