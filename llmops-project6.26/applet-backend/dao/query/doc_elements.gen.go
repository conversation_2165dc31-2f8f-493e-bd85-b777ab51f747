// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newDocElement(db *gorm.DB, opts ...gen.DOOption) docElement {
	_docElement := docElement{}

	_docElement.docElementDo.UseDB(db, opts...)
	_docElement.docElementDo.UseModel(&models.DocElement{})

	tableName := _docElement.docElementDo.TableName()
	_docElement.ALL = field.NewAsterisk(tableName)
	_docElement.Id = field.NewString(tableName, "id")
	_docElement.Type = field.NewInt32(tableName, "ele_type")
	_docElement.Text = field.NewString(tableName, "text")
	_docElement.Metadata = field.NewField(tableName, "metadata")
	_docElement.ParentIds = field.NewField(tableName, "parent_ids")
	_docElement.ParentTexts = field.NewField(tableName, "parent_texts")
	_docElement.Extra = field.NewField(tableName, "extra")

	_docElement.fillFieldMap()

	return _docElement
}

type docElement struct {
	docElementDo

	ALL         field.Asterisk
	Id          field.String
	Type        field.Int32
	Text        field.String
	Metadata    field.Field
	ParentIds   field.Field
	ParentTexts field.Field
	Extra       field.Field

	fieldMap map[string]field.Expr
}

func (d docElement) Table(newTableName string) *docElement {
	d.docElementDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d docElement) As(alias string) *docElement {
	d.docElementDo.DO = *(d.docElementDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *docElement) updateTableName(table string) *docElement {
	d.ALL = field.NewAsterisk(table)
	d.Id = field.NewString(table, "id")
	d.Type = field.NewInt32(table, "ele_type")
	d.Text = field.NewString(table, "text")
	d.Metadata = field.NewField(table, "metadata")
	d.ParentIds = field.NewField(table, "parent_ids")
	d.ParentTexts = field.NewField(table, "parent_texts")
	d.Extra = field.NewField(table, "extra")

	d.fillFieldMap()

	return d
}

func (d *docElement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *docElement) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 7)
	d.fieldMap["id"] = d.Id
	d.fieldMap["ele_type"] = d.Type
	d.fieldMap["text"] = d.Text
	d.fieldMap["metadata"] = d.Metadata
	d.fieldMap["parent_ids"] = d.ParentIds
	d.fieldMap["parent_texts"] = d.ParentTexts
	d.fieldMap["extra"] = d.Extra
}

func (d docElement) clone(db *gorm.DB) docElement {
	d.docElementDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d docElement) replaceDB(db *gorm.DB) docElement {
	d.docElementDo.ReplaceDB(db)
	return d
}

type docElementDo struct{ gen.DO }

type IDocElementDo interface {
	gen.SubQuery
	Debug() IDocElementDo
	WithContext(ctx context.Context) IDocElementDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDocElementDo
	WriteDB() IDocElementDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDocElementDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDocElementDo
	Not(conds ...gen.Condition) IDocElementDo
	Or(conds ...gen.Condition) IDocElementDo
	Select(conds ...field.Expr) IDocElementDo
	Where(conds ...gen.Condition) IDocElementDo
	Order(conds ...field.Expr) IDocElementDo
	Distinct(cols ...field.Expr) IDocElementDo
	Omit(cols ...field.Expr) IDocElementDo
	Join(table schema.Tabler, on ...field.Expr) IDocElementDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDocElementDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDocElementDo
	Group(cols ...field.Expr) IDocElementDo
	Having(conds ...gen.Condition) IDocElementDo
	Limit(limit int) IDocElementDo
	Offset(offset int) IDocElementDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDocElementDo
	Unscoped() IDocElementDo
	Create(values ...*models.DocElement) error
	CreateInBatches(values []*models.DocElement, batchSize int) error
	Save(values ...*models.DocElement) error
	First() (*models.DocElement, error)
	Take() (*models.DocElement, error)
	Last() (*models.DocElement, error)
	Find() ([]*models.DocElement, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocElement, err error)
	FindInBatches(result *[]*models.DocElement, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.DocElement) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDocElementDo
	Assign(attrs ...field.AssignExpr) IDocElementDo
	Joins(fields ...field.RelationField) IDocElementDo
	Preload(fields ...field.RelationField) IDocElementDo
	FirstOrInit() (*models.DocElement, error)
	FirstOrCreate() (*models.DocElement, error)
	FindByPage(offset int, limit int) (result []*models.DocElement, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDocElementDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d docElementDo) Debug() IDocElementDo {
	return d.withDO(d.DO.Debug())
}

func (d docElementDo) WithContext(ctx context.Context) IDocElementDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d docElementDo) ReadDB() IDocElementDo {
	return d.Clauses(dbresolver.Read)
}

func (d docElementDo) WriteDB() IDocElementDo {
	return d.Clauses(dbresolver.Write)
}

func (d docElementDo) Session(config *gorm.Session) IDocElementDo {
	return d.withDO(d.DO.Session(config))
}

func (d docElementDo) Clauses(conds ...clause.Expression) IDocElementDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d docElementDo) Returning(value interface{}, columns ...string) IDocElementDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d docElementDo) Not(conds ...gen.Condition) IDocElementDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d docElementDo) Or(conds ...gen.Condition) IDocElementDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d docElementDo) Select(conds ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d docElementDo) Where(conds ...gen.Condition) IDocElementDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d docElementDo) Order(conds ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d docElementDo) Distinct(cols ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d docElementDo) Omit(cols ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d docElementDo) Join(table schema.Tabler, on ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d docElementDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d docElementDo) RightJoin(table schema.Tabler, on ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d docElementDo) Group(cols ...field.Expr) IDocElementDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d docElementDo) Having(conds ...gen.Condition) IDocElementDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d docElementDo) Limit(limit int) IDocElementDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d docElementDo) Offset(offset int) IDocElementDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d docElementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDocElementDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d docElementDo) Unscoped() IDocElementDo {
	return d.withDO(d.DO.Unscoped())
}

func (d docElementDo) Create(values ...*models.DocElement) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d docElementDo) CreateInBatches(values []*models.DocElement, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d docElementDo) Save(values ...*models.DocElement) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d docElementDo) First() (*models.DocElement, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocElement), nil
	}
}

func (d docElementDo) Take() (*models.DocElement, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocElement), nil
	}
}

func (d docElementDo) Last() (*models.DocElement, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocElement), nil
	}
}

func (d docElementDo) Find() ([]*models.DocElement, error) {
	result, err := d.DO.Find()
	return result.([]*models.DocElement), err
}

func (d docElementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocElement, err error) {
	buf := make([]*models.DocElement, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d docElementDo) FindInBatches(result *[]*models.DocElement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d docElementDo) Attrs(attrs ...field.AssignExpr) IDocElementDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d docElementDo) Assign(attrs ...field.AssignExpr) IDocElementDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d docElementDo) Joins(fields ...field.RelationField) IDocElementDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d docElementDo) Preload(fields ...field.RelationField) IDocElementDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d docElementDo) FirstOrInit() (*models.DocElement, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocElement), nil
	}
}

func (d docElementDo) FirstOrCreate() (*models.DocElement, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocElement), nil
	}
}

func (d docElementDo) FindByPage(offset int, limit int) (result []*models.DocElement, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d docElementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d docElementDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d docElementDo) Delete(models ...*models.DocElement) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *docElementDo) withDO(do gen.Dao) *docElementDo {
	d.DO = *do.(*gen.DO)
	return d
}
