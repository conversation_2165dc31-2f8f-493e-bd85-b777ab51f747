// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newDialogApp(db *gorm.DB, opts ...gen.DOOption) dialogApp {
	_dialogApp := dialogApp{}

	_dialogApp.dialogAppDo.UseDB(db, opts...)
	_dialogApp.dialogAppDo.UseModel(&generated.DialogApp{})

	tableName := _dialogApp.dialogAppDo.TableName()
	_dialogApp.ALL = field.NewAsterisk(tableName)
	_dialogApp.ID = field.NewInt64(tableName, "id")
	_dialogApp.AppID = field.NewString(tableName, "app_id")
	_dialogApp.AppName = field.NewString(tableName, "app_name")
	_dialogApp.AppImage = field.NewString(tableName, "app_image")
	_dialogApp.ProjectID = field.NewString(tableName, "project_id")
	_dialogApp.User = field.NewString(tableName, "user")
	_dialogApp.CreateTime = field.NewTime(tableName, "create_time")
	_dialogApp.CreatedType = field.NewString(tableName, "created_type")
	_dialogApp.UpdatedTime = field.NewTime(tableName, "updated_time")

	_dialogApp.fillFieldMap()

	return _dialogApp
}

type dialogApp struct {
	dialogAppDo

	ALL         field.Asterisk
	ID          field.Int64
	AppID       field.String
	AppName     field.String
	AppImage    field.String
	ProjectID   field.String
	User        field.String
	CreateTime  field.Time   // 创建时间
	CreatedType field.String // 应用类型
	UpdatedTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (d dialogApp) Table(newTableName string) *dialogApp {
	d.dialogAppDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dialogApp) As(alias string) *dialogApp {
	d.dialogAppDo.DO = *(d.dialogAppDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dialogApp) updateTableName(table string) *dialogApp {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.AppID = field.NewString(table, "app_id")
	d.AppName = field.NewString(table, "app_name")
	d.AppImage = field.NewString(table, "app_image")
	d.ProjectID = field.NewString(table, "project_id")
	d.User = field.NewString(table, "user")
	d.CreateTime = field.NewTime(table, "create_time")
	d.CreatedType = field.NewString(table, "created_type")
	d.UpdatedTime = field.NewTime(table, "updated_time")

	d.fillFieldMap()

	return d
}

func (d *dialogApp) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dialogApp) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 9)
	d.fieldMap["id"] = d.ID
	d.fieldMap["app_id"] = d.AppID
	d.fieldMap["app_name"] = d.AppName
	d.fieldMap["app_image"] = d.AppImage
	d.fieldMap["project_id"] = d.ProjectID
	d.fieldMap["user"] = d.User
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["created_type"] = d.CreatedType
	d.fieldMap["updated_time"] = d.UpdatedTime
}

func (d dialogApp) clone(db *gorm.DB) dialogApp {
	d.dialogAppDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dialogApp) replaceDB(db *gorm.DB) dialogApp {
	d.dialogAppDo.ReplaceDB(db)
	return d
}

type dialogAppDo struct{ gen.DO }

type IDialogAppDo interface {
	gen.SubQuery
	Debug() IDialogAppDo
	WithContext(ctx context.Context) IDialogAppDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDialogAppDo
	WriteDB() IDialogAppDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDialogAppDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDialogAppDo
	Not(conds ...gen.Condition) IDialogAppDo
	Or(conds ...gen.Condition) IDialogAppDo
	Select(conds ...field.Expr) IDialogAppDo
	Where(conds ...gen.Condition) IDialogAppDo
	Order(conds ...field.Expr) IDialogAppDo
	Distinct(cols ...field.Expr) IDialogAppDo
	Omit(cols ...field.Expr) IDialogAppDo
	Join(table schema.Tabler, on ...field.Expr) IDialogAppDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDialogAppDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDialogAppDo
	Group(cols ...field.Expr) IDialogAppDo
	Having(conds ...gen.Condition) IDialogAppDo
	Limit(limit int) IDialogAppDo
	Offset(offset int) IDialogAppDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogAppDo
	Unscoped() IDialogAppDo
	Create(values ...*generated.DialogApp) error
	CreateInBatches(values []*generated.DialogApp, batchSize int) error
	Save(values ...*generated.DialogApp) error
	First() (*generated.DialogApp, error)
	Take() (*generated.DialogApp, error)
	Last() (*generated.DialogApp, error)
	Find() ([]*generated.DialogApp, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.DialogApp, err error)
	FindInBatches(result *[]*generated.DialogApp, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.DialogApp) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDialogAppDo
	Assign(attrs ...field.AssignExpr) IDialogAppDo
	Joins(fields ...field.RelationField) IDialogAppDo
	Preload(fields ...field.RelationField) IDialogAppDo
	FirstOrInit() (*generated.DialogApp, error)
	FirstOrCreate() (*generated.DialogApp, error)
	FindByPage(offset int, limit int) (result []*generated.DialogApp, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDialogAppDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d dialogAppDo) Debug() IDialogAppDo {
	return d.withDO(d.DO.Debug())
}

func (d dialogAppDo) WithContext(ctx context.Context) IDialogAppDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d dialogAppDo) ReadDB() IDialogAppDo {
	return d.Clauses(dbresolver.Read)
}

func (d dialogAppDo) WriteDB() IDialogAppDo {
	return d.Clauses(dbresolver.Write)
}

func (d dialogAppDo) Session(config *gorm.Session) IDialogAppDo {
	return d.withDO(d.DO.Session(config))
}

func (d dialogAppDo) Clauses(conds ...clause.Expression) IDialogAppDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d dialogAppDo) Returning(value interface{}, columns ...string) IDialogAppDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d dialogAppDo) Not(conds ...gen.Condition) IDialogAppDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d dialogAppDo) Or(conds ...gen.Condition) IDialogAppDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d dialogAppDo) Select(conds ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d dialogAppDo) Where(conds ...gen.Condition) IDialogAppDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d dialogAppDo) Order(conds ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d dialogAppDo) Distinct(cols ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d dialogAppDo) Omit(cols ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d dialogAppDo) Join(table schema.Tabler, on ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d dialogAppDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d dialogAppDo) RightJoin(table schema.Tabler, on ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d dialogAppDo) Group(cols ...field.Expr) IDialogAppDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d dialogAppDo) Having(conds ...gen.Condition) IDialogAppDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d dialogAppDo) Limit(limit int) IDialogAppDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d dialogAppDo) Offset(offset int) IDialogAppDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d dialogAppDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogAppDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d dialogAppDo) Unscoped() IDialogAppDo {
	return d.withDO(d.DO.Unscoped())
}

func (d dialogAppDo) Create(values ...*generated.DialogApp) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d dialogAppDo) CreateInBatches(values []*generated.DialogApp, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d dialogAppDo) Save(values ...*generated.DialogApp) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d dialogAppDo) First() (*generated.DialogApp, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogApp), nil
	}
}

func (d dialogAppDo) Take() (*generated.DialogApp, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogApp), nil
	}
}

func (d dialogAppDo) Last() (*generated.DialogApp, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogApp), nil
	}
}

func (d dialogAppDo) Find() ([]*generated.DialogApp, error) {
	result, err := d.DO.Find()
	return result.([]*generated.DialogApp), err
}

func (d dialogAppDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.DialogApp, err error) {
	buf := make([]*generated.DialogApp, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d dialogAppDo) FindInBatches(result *[]*generated.DialogApp, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d dialogAppDo) Attrs(attrs ...field.AssignExpr) IDialogAppDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d dialogAppDo) Assign(attrs ...field.AssignExpr) IDialogAppDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d dialogAppDo) Joins(fields ...field.RelationField) IDialogAppDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d dialogAppDo) Preload(fields ...field.RelationField) IDialogAppDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d dialogAppDo) FirstOrInit() (*generated.DialogApp, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogApp), nil
	}
}

func (d dialogAppDo) FirstOrCreate() (*generated.DialogApp, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogApp), nil
	}
}

func (d dialogAppDo) FindByPage(offset int, limit int) (result []*generated.DialogApp, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d dialogAppDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d dialogAppDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d dialogAppDo) Delete(models ...*generated.DialogApp) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *dialogAppDo) withDO(do gen.Dao) *dialogAppDo {
	d.DO = *do.(*gen.DO)
	return d
}
