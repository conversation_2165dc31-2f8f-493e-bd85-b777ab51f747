// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newCustomWidget(db *gorm.DB, opts ...gen.DOOption) customWidget {
	_customWidget := customWidget{}

	_customWidget.customWidgetDo.UseDB(db, opts...)
	_customWidget.customWidgetDo.UseModel(&generated.CustomWidget{})

	tableName := _customWidget.customWidgetDo.TableName()
	_customWidget.ALL = field.NewAsterisk(tableName)
	_customWidget.ID = field.NewString(tableName, "id")
	_customWidget.Name = field.NewString(tableName, "name")
	_customWidget.Desc = field.NewString(tableName, "desc")
	_customWidget.Status = field.NewString(tableName, "status")
	_customWidget.Port = field.NewInt32(tableName, "port")
	_customWidget.ParamInfo = field.NewString(tableName, "param_info")
	_customWidget.ImageInfo = field.NewString(tableName, "image_info")
	_customWidget.LabelInfo = field.NewString(tableName, "label_info")
	_customWidget.ResourceInfo = field.NewString(tableName, "resource_info")
	_customWidget.DeployInfo = field.NewString(tableName, "deploy_info")
	_customWidget.Creator = field.NewString(tableName, "creator")
	_customWidget.ProjectID = field.NewString(tableName, "project_id")
	_customWidget.CreatedTime = field.NewTime(tableName, "created_time")
	_customWidget.UpdatedTime = field.NewTime(tableName, "updated_time")

	_customWidget.fillFieldMap()

	return _customWidget
}

type customWidget struct {
	customWidgetDo

	ALL          field.Asterisk
	ID           field.String // 自定义算子唯一ID
	Name         field.String // 算子名称
	Desc         field.String // 算子描述信息
	Status       field.String // 算子运行状态
	Port         field.Int32  // 端口
	ParamInfo    field.String // 参数列表
	ImageInfo    field.String // 自定义算子镜像信息，map结构
	LabelInfo    field.String // 标签，map结构
	ResourceInfo field.String // 资源配置信息，map结构
	DeployInfo   field.String // 部署信息
	Creator      field.String // 创建人
	ProjectID    field.String // 项目ID
	CreatedTime  field.Time   // 创建时间
	UpdatedTime  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (c customWidget) Table(newTableName string) *customWidget {
	c.customWidgetDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c customWidget) As(alias string) *customWidget {
	c.customWidgetDo.DO = *(c.customWidgetDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *customWidget) updateTableName(table string) *customWidget {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.Name = field.NewString(table, "name")
	c.Desc = field.NewString(table, "desc")
	c.Status = field.NewString(table, "status")
	c.Port = field.NewInt32(table, "port")
	c.ParamInfo = field.NewString(table, "param_info")
	c.ImageInfo = field.NewString(table, "image_info")
	c.LabelInfo = field.NewString(table, "label_info")
	c.ResourceInfo = field.NewString(table, "resource_info")
	c.DeployInfo = field.NewString(table, "deploy_info")
	c.Creator = field.NewString(table, "creator")
	c.ProjectID = field.NewString(table, "project_id")
	c.CreatedTime = field.NewTime(table, "created_time")
	c.UpdatedTime = field.NewTime(table, "updated_time")

	c.fillFieldMap()

	return c
}

func (c *customWidget) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *customWidget) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 14)
	c.fieldMap["id"] = c.ID
	c.fieldMap["name"] = c.Name
	c.fieldMap["desc"] = c.Desc
	c.fieldMap["status"] = c.Status
	c.fieldMap["port"] = c.Port
	c.fieldMap["param_info"] = c.ParamInfo
	c.fieldMap["image_info"] = c.ImageInfo
	c.fieldMap["label_info"] = c.LabelInfo
	c.fieldMap["resource_info"] = c.ResourceInfo
	c.fieldMap["deploy_info"] = c.DeployInfo
	c.fieldMap["creator"] = c.Creator
	c.fieldMap["project_id"] = c.ProjectID
	c.fieldMap["created_time"] = c.CreatedTime
	c.fieldMap["updated_time"] = c.UpdatedTime
}

func (c customWidget) clone(db *gorm.DB) customWidget {
	c.customWidgetDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c customWidget) replaceDB(db *gorm.DB) customWidget {
	c.customWidgetDo.ReplaceDB(db)
	return c
}

type customWidgetDo struct{ gen.DO }

type ICustomWidgetDo interface {
	gen.SubQuery
	Debug() ICustomWidgetDo
	WithContext(ctx context.Context) ICustomWidgetDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICustomWidgetDo
	WriteDB() ICustomWidgetDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICustomWidgetDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICustomWidgetDo
	Not(conds ...gen.Condition) ICustomWidgetDo
	Or(conds ...gen.Condition) ICustomWidgetDo
	Select(conds ...field.Expr) ICustomWidgetDo
	Where(conds ...gen.Condition) ICustomWidgetDo
	Order(conds ...field.Expr) ICustomWidgetDo
	Distinct(cols ...field.Expr) ICustomWidgetDo
	Omit(cols ...field.Expr) ICustomWidgetDo
	Join(table schema.Tabler, on ...field.Expr) ICustomWidgetDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICustomWidgetDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICustomWidgetDo
	Group(cols ...field.Expr) ICustomWidgetDo
	Having(conds ...gen.Condition) ICustomWidgetDo
	Limit(limit int) ICustomWidgetDo
	Offset(offset int) ICustomWidgetDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICustomWidgetDo
	Unscoped() ICustomWidgetDo
	Create(values ...*generated.CustomWidget) error
	CreateInBatches(values []*generated.CustomWidget, batchSize int) error
	Save(values ...*generated.CustomWidget) error
	First() (*generated.CustomWidget, error)
	Take() (*generated.CustomWidget, error)
	Last() (*generated.CustomWidget, error)
	Find() ([]*generated.CustomWidget, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.CustomWidget, err error)
	FindInBatches(result *[]*generated.CustomWidget, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.CustomWidget) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICustomWidgetDo
	Assign(attrs ...field.AssignExpr) ICustomWidgetDo
	Joins(fields ...field.RelationField) ICustomWidgetDo
	Preload(fields ...field.RelationField) ICustomWidgetDo
	FirstOrInit() (*generated.CustomWidget, error)
	FirstOrCreate() (*generated.CustomWidget, error)
	FindByPage(offset int, limit int) (result []*generated.CustomWidget, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICustomWidgetDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c customWidgetDo) Debug() ICustomWidgetDo {
	return c.withDO(c.DO.Debug())
}

func (c customWidgetDo) WithContext(ctx context.Context) ICustomWidgetDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c customWidgetDo) ReadDB() ICustomWidgetDo {
	return c.Clauses(dbresolver.Read)
}

func (c customWidgetDo) WriteDB() ICustomWidgetDo {
	return c.Clauses(dbresolver.Write)
}

func (c customWidgetDo) Session(config *gorm.Session) ICustomWidgetDo {
	return c.withDO(c.DO.Session(config))
}

func (c customWidgetDo) Clauses(conds ...clause.Expression) ICustomWidgetDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c customWidgetDo) Returning(value interface{}, columns ...string) ICustomWidgetDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c customWidgetDo) Not(conds ...gen.Condition) ICustomWidgetDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c customWidgetDo) Or(conds ...gen.Condition) ICustomWidgetDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c customWidgetDo) Select(conds ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c customWidgetDo) Where(conds ...gen.Condition) ICustomWidgetDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c customWidgetDo) Order(conds ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c customWidgetDo) Distinct(cols ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c customWidgetDo) Omit(cols ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c customWidgetDo) Join(table schema.Tabler, on ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c customWidgetDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c customWidgetDo) RightJoin(table schema.Tabler, on ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c customWidgetDo) Group(cols ...field.Expr) ICustomWidgetDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c customWidgetDo) Having(conds ...gen.Condition) ICustomWidgetDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c customWidgetDo) Limit(limit int) ICustomWidgetDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c customWidgetDo) Offset(offset int) ICustomWidgetDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c customWidgetDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICustomWidgetDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c customWidgetDo) Unscoped() ICustomWidgetDo {
	return c.withDO(c.DO.Unscoped())
}

func (c customWidgetDo) Create(values ...*generated.CustomWidget) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c customWidgetDo) CreateInBatches(values []*generated.CustomWidget, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c customWidgetDo) Save(values ...*generated.CustomWidget) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c customWidgetDo) First() (*generated.CustomWidget, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.CustomWidget), nil
	}
}

func (c customWidgetDo) Take() (*generated.CustomWidget, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.CustomWidget), nil
	}
}

func (c customWidgetDo) Last() (*generated.CustomWidget, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.CustomWidget), nil
	}
}

func (c customWidgetDo) Find() ([]*generated.CustomWidget, error) {
	result, err := c.DO.Find()
	return result.([]*generated.CustomWidget), err
}

func (c customWidgetDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.CustomWidget, err error) {
	buf := make([]*generated.CustomWidget, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c customWidgetDo) FindInBatches(result *[]*generated.CustomWidget, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c customWidgetDo) Attrs(attrs ...field.AssignExpr) ICustomWidgetDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c customWidgetDo) Assign(attrs ...field.AssignExpr) ICustomWidgetDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c customWidgetDo) Joins(fields ...field.RelationField) ICustomWidgetDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c customWidgetDo) Preload(fields ...field.RelationField) ICustomWidgetDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c customWidgetDo) FirstOrInit() (*generated.CustomWidget, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.CustomWidget), nil
	}
}

func (c customWidgetDo) FirstOrCreate() (*generated.CustomWidget, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.CustomWidget), nil
	}
}

func (c customWidgetDo) FindByPage(offset int, limit int) (result []*generated.CustomWidget, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c customWidgetDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c customWidgetDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c customWidgetDo) Delete(models ...*generated.CustomWidget) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *customWidgetDo) withDO(do gen.Dao) *customWidgetDo {
	c.DO = *do.(*gen.DO)
	return c
}
