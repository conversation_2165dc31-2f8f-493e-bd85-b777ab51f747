// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAppletExperiment(db *gorm.DB, opts ...gen.DOOption) appletExperiment {
	_appletExperiment := appletExperiment{}

	_appletExperiment.appletExperimentDo.UseDB(db, opts...)
	_appletExperiment.appletExperimentDo.UseModel(&generated.AppletExperiment{})

	tableName := _appletExperiment.appletExperimentDo.TableName()
	_appletExperiment.ALL = field.NewAsterisk(tableName)
	_appletExperiment.ChainID = field.NewString(tableName, "chain_id")
	_appletExperiment.Name = field.NewString(tableName, "name")
	_appletExperiment.ImageURL = field.NewString(tableName, "image_url")
	_appletExperiment.Introduction = field.NewString(tableName, "introduction")
	_appletExperiment.Examples = field.NewString(tableName, "examples")
	_appletExperiment.LabelInfo = field.NewString(tableName, "label_info")
	_appletExperiment.Creator = field.NewString(tableName, "creator")
	_appletExperiment.ProjectID = field.NewString(tableName, "project_id")
	_appletExperiment.Status = field.NewString(tableName, "status")
	_appletExperiment.ServiceInfo = field.NewString(tableName, "service_info")
	_appletExperiment.CreatedBy = field.NewString(tableName, "created_by")
	_appletExperiment.CreatedTime = field.NewTime(tableName, "created_time")
	_appletExperiment.UpdatedTime = field.NewTime(tableName, "updated_time")

	_appletExperiment.fillFieldMap()

	return _appletExperiment
}

type appletExperiment struct {
	appletExperimentDo

	ALL          field.Asterisk
	ChainID      field.String // 相关的应用链ID
	Name         field.String // 应用体验名称
	ImageURL     field.String // 封面的图片地址
	Introduction field.String // 开场白，应用介绍
	Examples     field.String // 常用问题示例
	LabelInfo    field.String // 标签，map结构
	Creator      field.String // 创建人
	ProjectID    field.String // 项目ID
	Status       field.String // '应用体验是否发布'
	ServiceInfo  field.String // 应用体验部署后的服务信息
	CreatedBy    field.String // '创建途径是智能体还是应用链'
	CreatedTime  field.Time   // 创建时间
	UpdatedTime  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a appletExperiment) Table(newTableName string) *appletExperiment {
	a.appletExperimentDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appletExperiment) As(alias string) *appletExperiment {
	a.appletExperimentDo.DO = *(a.appletExperimentDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appletExperiment) updateTableName(table string) *appletExperiment {
	a.ALL = field.NewAsterisk(table)
	a.ChainID = field.NewString(table, "chain_id")
	a.Name = field.NewString(table, "name")
	a.ImageURL = field.NewString(table, "image_url")
	a.Introduction = field.NewString(table, "introduction")
	a.Examples = field.NewString(table, "examples")
	a.LabelInfo = field.NewString(table, "label_info")
	a.Creator = field.NewString(table, "creator")
	a.ProjectID = field.NewString(table, "project_id")
	a.Status = field.NewString(table, "status")
	a.ServiceInfo = field.NewString(table, "service_info")
	a.CreatedBy = field.NewString(table, "created_by")
	a.CreatedTime = field.NewTime(table, "created_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")

	a.fillFieldMap()

	return a
}

func (a *appletExperiment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appletExperiment) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 13)
	a.fieldMap["chain_id"] = a.ChainID
	a.fieldMap["name"] = a.Name
	a.fieldMap["image_url"] = a.ImageURL
	a.fieldMap["introduction"] = a.Introduction
	a.fieldMap["examples"] = a.Examples
	a.fieldMap["label_info"] = a.LabelInfo
	a.fieldMap["creator"] = a.Creator
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["status"] = a.Status
	a.fieldMap["service_info"] = a.ServiceInfo
	a.fieldMap["created_by"] = a.CreatedBy
	a.fieldMap["created_time"] = a.CreatedTime
	a.fieldMap["updated_time"] = a.UpdatedTime
}

func (a appletExperiment) clone(db *gorm.DB) appletExperiment {
	a.appletExperimentDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appletExperiment) replaceDB(db *gorm.DB) appletExperiment {
	a.appletExperimentDo.ReplaceDB(db)
	return a
}

type appletExperimentDo struct{ gen.DO }

type IAppletExperimentDo interface {
	gen.SubQuery
	Debug() IAppletExperimentDo
	WithContext(ctx context.Context) IAppletExperimentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppletExperimentDo
	WriteDB() IAppletExperimentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppletExperimentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppletExperimentDo
	Not(conds ...gen.Condition) IAppletExperimentDo
	Or(conds ...gen.Condition) IAppletExperimentDo
	Select(conds ...field.Expr) IAppletExperimentDo
	Where(conds ...gen.Condition) IAppletExperimentDo
	Order(conds ...field.Expr) IAppletExperimentDo
	Distinct(cols ...field.Expr) IAppletExperimentDo
	Omit(cols ...field.Expr) IAppletExperimentDo
	Join(table schema.Tabler, on ...field.Expr) IAppletExperimentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppletExperimentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppletExperimentDo
	Group(cols ...field.Expr) IAppletExperimentDo
	Having(conds ...gen.Condition) IAppletExperimentDo
	Limit(limit int) IAppletExperimentDo
	Offset(offset int) IAppletExperimentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletExperimentDo
	Unscoped() IAppletExperimentDo
	Create(values ...*generated.AppletExperiment) error
	CreateInBatches(values []*generated.AppletExperiment, batchSize int) error
	Save(values ...*generated.AppletExperiment) error
	First() (*generated.AppletExperiment, error)
	Take() (*generated.AppletExperiment, error)
	Last() (*generated.AppletExperiment, error)
	Find() ([]*generated.AppletExperiment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletExperiment, err error)
	FindInBatches(result *[]*generated.AppletExperiment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.AppletExperiment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppletExperimentDo
	Assign(attrs ...field.AssignExpr) IAppletExperimentDo
	Joins(fields ...field.RelationField) IAppletExperimentDo
	Preload(fields ...field.RelationField) IAppletExperimentDo
	FirstOrInit() (*generated.AppletExperiment, error)
	FirstOrCreate() (*generated.AppletExperiment, error)
	FindByPage(offset int, limit int) (result []*generated.AppletExperiment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppletExperimentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appletExperimentDo) Debug() IAppletExperimentDo {
	return a.withDO(a.DO.Debug())
}

func (a appletExperimentDo) WithContext(ctx context.Context) IAppletExperimentDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appletExperimentDo) ReadDB() IAppletExperimentDo {
	return a.Clauses(dbresolver.Read)
}

func (a appletExperimentDo) WriteDB() IAppletExperimentDo {
	return a.Clauses(dbresolver.Write)
}

func (a appletExperimentDo) Session(config *gorm.Session) IAppletExperimentDo {
	return a.withDO(a.DO.Session(config))
}

func (a appletExperimentDo) Clauses(conds ...clause.Expression) IAppletExperimentDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appletExperimentDo) Returning(value interface{}, columns ...string) IAppletExperimentDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appletExperimentDo) Not(conds ...gen.Condition) IAppletExperimentDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appletExperimentDo) Or(conds ...gen.Condition) IAppletExperimentDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appletExperimentDo) Select(conds ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appletExperimentDo) Where(conds ...gen.Condition) IAppletExperimentDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appletExperimentDo) Order(conds ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appletExperimentDo) Distinct(cols ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appletExperimentDo) Omit(cols ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appletExperimentDo) Join(table schema.Tabler, on ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appletExperimentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appletExperimentDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appletExperimentDo) Group(cols ...field.Expr) IAppletExperimentDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appletExperimentDo) Having(conds ...gen.Condition) IAppletExperimentDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appletExperimentDo) Limit(limit int) IAppletExperimentDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appletExperimentDo) Offset(offset int) IAppletExperimentDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appletExperimentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletExperimentDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appletExperimentDo) Unscoped() IAppletExperimentDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appletExperimentDo) Create(values ...*generated.AppletExperiment) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appletExperimentDo) CreateInBatches(values []*generated.AppletExperiment, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appletExperimentDo) Save(values ...*generated.AppletExperiment) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appletExperimentDo) First() (*generated.AppletExperiment, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletExperiment), nil
	}
}

func (a appletExperimentDo) Take() (*generated.AppletExperiment, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletExperiment), nil
	}
}

func (a appletExperimentDo) Last() (*generated.AppletExperiment, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletExperiment), nil
	}
}

func (a appletExperimentDo) Find() ([]*generated.AppletExperiment, error) {
	result, err := a.DO.Find()
	return result.([]*generated.AppletExperiment), err
}

func (a appletExperimentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletExperiment, err error) {
	buf := make([]*generated.AppletExperiment, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appletExperimentDo) FindInBatches(result *[]*generated.AppletExperiment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appletExperimentDo) Attrs(attrs ...field.AssignExpr) IAppletExperimentDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appletExperimentDo) Assign(attrs ...field.AssignExpr) IAppletExperimentDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appletExperimentDo) Joins(fields ...field.RelationField) IAppletExperimentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appletExperimentDo) Preload(fields ...field.RelationField) IAppletExperimentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appletExperimentDo) FirstOrInit() (*generated.AppletExperiment, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletExperiment), nil
	}
}

func (a appletExperimentDo) FirstOrCreate() (*generated.AppletExperiment, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletExperiment), nil
	}
}

func (a appletExperimentDo) FindByPage(offset int, limit int) (result []*generated.AppletExperiment, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appletExperimentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appletExperimentDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appletExperimentDo) Delete(models ...*generated.AppletExperiment) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appletExperimentDo) withDO(do gen.Dao) *appletExperimentDo {
	a.DO = *do.(*gen.DO)
	return a
}
