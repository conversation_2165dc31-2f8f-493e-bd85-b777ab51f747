// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newChainSnapshot(db *gorm.DB, opts ...gen.DOOption) chainSnapshot {
	_chainSnapshot := chainSnapshot{}

	_chainSnapshot.chainSnapshotDo.UseDB(db, opts...)
	_chainSnapshot.chainSnapshotDo.UseModel(&generated.ChainSnapshot{})

	tableName := _chainSnapshot.chainSnapshotDo.TableName()
	_chainSnapshot.ALL = field.NewAsterisk(tableName)
	_chainSnapshot.ID = field.NewInt64(tableName, "id")
	_chainSnapshot.ChainDetail = field.NewString(tableName, "chain_detail")
	_chainSnapshot.Base = field.NewString(tableName, "base")
	_chainSnapshot.ChainID = field.NewString(tableName, "chain_id")

	_chainSnapshot.fillFieldMap()

	return _chainSnapshot
}

type chainSnapshot struct {
	chainSnapshotDo

	ALL         field.Asterisk
	ID          field.Int64  // ID，和ChainDebugHistory的ChainSnapshotId关联
	ChainDetail field.String // 应用链快照
	Base        field.String // 用于service version info 的 base 信息
	ChainID     field.String // 应用链id

	fieldMap map[string]field.Expr
}

func (c chainSnapshot) Table(newTableName string) *chainSnapshot {
	c.chainSnapshotDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chainSnapshot) As(alias string) *chainSnapshot {
	c.chainSnapshotDo.DO = *(c.chainSnapshotDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chainSnapshot) updateTableName(table string) *chainSnapshot {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.ChainDetail = field.NewString(table, "chain_detail")
	c.Base = field.NewString(table, "base")
	c.ChainID = field.NewString(table, "chain_id")

	c.fillFieldMap()

	return c
}

func (c *chainSnapshot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chainSnapshot) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 4)
	c.fieldMap["id"] = c.ID
	c.fieldMap["chain_detail"] = c.ChainDetail
	c.fieldMap["base"] = c.Base
	c.fieldMap["chain_id"] = c.ChainID
}

func (c chainSnapshot) clone(db *gorm.DB) chainSnapshot {
	c.chainSnapshotDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c chainSnapshot) replaceDB(db *gorm.DB) chainSnapshot {
	c.chainSnapshotDo.ReplaceDB(db)
	return c
}

type chainSnapshotDo struct{ gen.DO }

type IChainSnapshotDo interface {
	gen.SubQuery
	Debug() IChainSnapshotDo
	WithContext(ctx context.Context) IChainSnapshotDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IChainSnapshotDo
	WriteDB() IChainSnapshotDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IChainSnapshotDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IChainSnapshotDo
	Not(conds ...gen.Condition) IChainSnapshotDo
	Or(conds ...gen.Condition) IChainSnapshotDo
	Select(conds ...field.Expr) IChainSnapshotDo
	Where(conds ...gen.Condition) IChainSnapshotDo
	Order(conds ...field.Expr) IChainSnapshotDo
	Distinct(cols ...field.Expr) IChainSnapshotDo
	Omit(cols ...field.Expr) IChainSnapshotDo
	Join(table schema.Tabler, on ...field.Expr) IChainSnapshotDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IChainSnapshotDo
	RightJoin(table schema.Tabler, on ...field.Expr) IChainSnapshotDo
	Group(cols ...field.Expr) IChainSnapshotDo
	Having(conds ...gen.Condition) IChainSnapshotDo
	Limit(limit int) IChainSnapshotDo
	Offset(offset int) IChainSnapshotDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IChainSnapshotDo
	Unscoped() IChainSnapshotDo
	Create(values ...*generated.ChainSnapshot) error
	CreateInBatches(values []*generated.ChainSnapshot, batchSize int) error
	Save(values ...*generated.ChainSnapshot) error
	First() (*generated.ChainSnapshot, error)
	Take() (*generated.ChainSnapshot, error)
	Last() (*generated.ChainSnapshot, error)
	Find() ([]*generated.ChainSnapshot, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainSnapshot, err error)
	FindInBatches(result *[]*generated.ChainSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.ChainSnapshot) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IChainSnapshotDo
	Assign(attrs ...field.AssignExpr) IChainSnapshotDo
	Joins(fields ...field.RelationField) IChainSnapshotDo
	Preload(fields ...field.RelationField) IChainSnapshotDo
	FirstOrInit() (*generated.ChainSnapshot, error)
	FirstOrCreate() (*generated.ChainSnapshot, error)
	FindByPage(offset int, limit int) (result []*generated.ChainSnapshot, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IChainSnapshotDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c chainSnapshotDo) Debug() IChainSnapshotDo {
	return c.withDO(c.DO.Debug())
}

func (c chainSnapshotDo) WithContext(ctx context.Context) IChainSnapshotDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chainSnapshotDo) ReadDB() IChainSnapshotDo {
	return c.Clauses(dbresolver.Read)
}

func (c chainSnapshotDo) WriteDB() IChainSnapshotDo {
	return c.Clauses(dbresolver.Write)
}

func (c chainSnapshotDo) Session(config *gorm.Session) IChainSnapshotDo {
	return c.withDO(c.DO.Session(config))
}

func (c chainSnapshotDo) Clauses(conds ...clause.Expression) IChainSnapshotDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chainSnapshotDo) Returning(value interface{}, columns ...string) IChainSnapshotDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chainSnapshotDo) Not(conds ...gen.Condition) IChainSnapshotDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chainSnapshotDo) Or(conds ...gen.Condition) IChainSnapshotDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chainSnapshotDo) Select(conds ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chainSnapshotDo) Where(conds ...gen.Condition) IChainSnapshotDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chainSnapshotDo) Order(conds ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chainSnapshotDo) Distinct(cols ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chainSnapshotDo) Omit(cols ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chainSnapshotDo) Join(table schema.Tabler, on ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chainSnapshotDo) LeftJoin(table schema.Tabler, on ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chainSnapshotDo) RightJoin(table schema.Tabler, on ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chainSnapshotDo) Group(cols ...field.Expr) IChainSnapshotDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chainSnapshotDo) Having(conds ...gen.Condition) IChainSnapshotDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chainSnapshotDo) Limit(limit int) IChainSnapshotDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chainSnapshotDo) Offset(offset int) IChainSnapshotDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chainSnapshotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IChainSnapshotDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chainSnapshotDo) Unscoped() IChainSnapshotDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chainSnapshotDo) Create(values ...*generated.ChainSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chainSnapshotDo) CreateInBatches(values []*generated.ChainSnapshot, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chainSnapshotDo) Save(values ...*generated.ChainSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chainSnapshotDo) First() (*generated.ChainSnapshot, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainSnapshot), nil
	}
}

func (c chainSnapshotDo) Take() (*generated.ChainSnapshot, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainSnapshot), nil
	}
}

func (c chainSnapshotDo) Last() (*generated.ChainSnapshot, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainSnapshot), nil
	}
}

func (c chainSnapshotDo) Find() ([]*generated.ChainSnapshot, error) {
	result, err := c.DO.Find()
	return result.([]*generated.ChainSnapshot), err
}

func (c chainSnapshotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainSnapshot, err error) {
	buf := make([]*generated.ChainSnapshot, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chainSnapshotDo) FindInBatches(result *[]*generated.ChainSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chainSnapshotDo) Attrs(attrs ...field.AssignExpr) IChainSnapshotDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chainSnapshotDo) Assign(attrs ...field.AssignExpr) IChainSnapshotDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chainSnapshotDo) Joins(fields ...field.RelationField) IChainSnapshotDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chainSnapshotDo) Preload(fields ...field.RelationField) IChainSnapshotDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chainSnapshotDo) FirstOrInit() (*generated.ChainSnapshot, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainSnapshot), nil
	}
}

func (c chainSnapshotDo) FirstOrCreate() (*generated.ChainSnapshot, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainSnapshot), nil
	}
}

func (c chainSnapshotDo) FindByPage(offset int, limit int) (result []*generated.ChainSnapshot, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chainSnapshotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chainSnapshotDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chainSnapshotDo) Delete(models ...*generated.ChainSnapshot) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chainSnapshotDo) withDO(do gen.Dao) *chainSnapshotDo {
	c.DO = *do.(*gen.DO)
	return c
}
