// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newDocTask(db *gorm.DB, opts ...gen.DOOption) docTask {
	_docTask := docTask{}

	_docTask.docTaskDo.UseDB(db, opts...)
	_docTask.docTaskDo.UseModel(&models.DocTask{})

	tableName := _docTask.docTaskDo.TableName()
	_docTask.ALL = field.NewAsterisk(tableName)
	_docTask.DocumentId = field.NewString(tableName, "doc_id")
	_docTask.KnowledgeBaseId = field.NewString(tableName, "kb_id")
	_docTask.DocProcessingConfig = field.NewField(tableName, "dpc")
	_docTask.DocumentProcessingProgress = field.NewField(tableName, "prog")

	_docTask.fillFieldMap()

	return _docTask
}

type docTask struct {
	docTaskDo

	ALL                        field.Asterisk
	DocumentId                 field.String
	KnowledgeBaseId            field.String
	DocProcessingConfig        field.Field
	DocumentProcessingProgress field.Field

	fieldMap map[string]field.Expr
}

func (d docTask) Table(newTableName string) *docTask {
	d.docTaskDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d docTask) As(alias string) *docTask {
	d.docTaskDo.DO = *(d.docTaskDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *docTask) updateTableName(table string) *docTask {
	d.ALL = field.NewAsterisk(table)
	d.DocumentId = field.NewString(table, "doc_id")
	d.KnowledgeBaseId = field.NewString(table, "kb_id")
	d.DocProcessingConfig = field.NewField(table, "dpc")
	d.DocumentProcessingProgress = field.NewField(table, "prog")

	d.fillFieldMap()

	return d
}

func (d *docTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *docTask) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 4)
	d.fieldMap["doc_id"] = d.DocumentId
	d.fieldMap["kb_id"] = d.KnowledgeBaseId
	d.fieldMap["dpc"] = d.DocProcessingConfig
	d.fieldMap["prog"] = d.DocumentProcessingProgress
}

func (d docTask) clone(db *gorm.DB) docTask {
	d.docTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d docTask) replaceDB(db *gorm.DB) docTask {
	d.docTaskDo.ReplaceDB(db)
	return d
}

type docTaskDo struct{ gen.DO }

type IDocTaskDo interface {
	gen.SubQuery
	Debug() IDocTaskDo
	WithContext(ctx context.Context) IDocTaskDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDocTaskDo
	WriteDB() IDocTaskDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDocTaskDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDocTaskDo
	Not(conds ...gen.Condition) IDocTaskDo
	Or(conds ...gen.Condition) IDocTaskDo
	Select(conds ...field.Expr) IDocTaskDo
	Where(conds ...gen.Condition) IDocTaskDo
	Order(conds ...field.Expr) IDocTaskDo
	Distinct(cols ...field.Expr) IDocTaskDo
	Omit(cols ...field.Expr) IDocTaskDo
	Join(table schema.Tabler, on ...field.Expr) IDocTaskDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDocTaskDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDocTaskDo
	Group(cols ...field.Expr) IDocTaskDo
	Having(conds ...gen.Condition) IDocTaskDo
	Limit(limit int) IDocTaskDo
	Offset(offset int) IDocTaskDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDocTaskDo
	Unscoped() IDocTaskDo
	Create(values ...*models.DocTask) error
	CreateInBatches(values []*models.DocTask, batchSize int) error
	Save(values ...*models.DocTask) error
	First() (*models.DocTask, error)
	Take() (*models.DocTask, error)
	Last() (*models.DocTask, error)
	Find() ([]*models.DocTask, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocTask, err error)
	FindInBatches(result *[]*models.DocTask, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.DocTask) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDocTaskDo
	Assign(attrs ...field.AssignExpr) IDocTaskDo
	Joins(fields ...field.RelationField) IDocTaskDo
	Preload(fields ...field.RelationField) IDocTaskDo
	FirstOrInit() (*models.DocTask, error)
	FirstOrCreate() (*models.DocTask, error)
	FindByPage(offset int, limit int) (result []*models.DocTask, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDocTaskDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d docTaskDo) Debug() IDocTaskDo {
	return d.withDO(d.DO.Debug())
}

func (d docTaskDo) WithContext(ctx context.Context) IDocTaskDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d docTaskDo) ReadDB() IDocTaskDo {
	return d.Clauses(dbresolver.Read)
}

func (d docTaskDo) WriteDB() IDocTaskDo {
	return d.Clauses(dbresolver.Write)
}

func (d docTaskDo) Session(config *gorm.Session) IDocTaskDo {
	return d.withDO(d.DO.Session(config))
}

func (d docTaskDo) Clauses(conds ...clause.Expression) IDocTaskDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d docTaskDo) Returning(value interface{}, columns ...string) IDocTaskDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d docTaskDo) Not(conds ...gen.Condition) IDocTaskDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d docTaskDo) Or(conds ...gen.Condition) IDocTaskDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d docTaskDo) Select(conds ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d docTaskDo) Where(conds ...gen.Condition) IDocTaskDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d docTaskDo) Order(conds ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d docTaskDo) Distinct(cols ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d docTaskDo) Omit(cols ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d docTaskDo) Join(table schema.Tabler, on ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d docTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d docTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d docTaskDo) Group(cols ...field.Expr) IDocTaskDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d docTaskDo) Having(conds ...gen.Condition) IDocTaskDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d docTaskDo) Limit(limit int) IDocTaskDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d docTaskDo) Offset(offset int) IDocTaskDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d docTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDocTaskDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d docTaskDo) Unscoped() IDocTaskDo {
	return d.withDO(d.DO.Unscoped())
}

func (d docTaskDo) Create(values ...*models.DocTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d docTaskDo) CreateInBatches(values []*models.DocTask, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d docTaskDo) Save(values ...*models.DocTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d docTaskDo) First() (*models.DocTask, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTask), nil
	}
}

func (d docTaskDo) Take() (*models.DocTask, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTask), nil
	}
}

func (d docTaskDo) Last() (*models.DocTask, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTask), nil
	}
}

func (d docTaskDo) Find() ([]*models.DocTask, error) {
	result, err := d.DO.Find()
	return result.([]*models.DocTask), err
}

func (d docTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DocTask, err error) {
	buf := make([]*models.DocTask, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d docTaskDo) FindInBatches(result *[]*models.DocTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d docTaskDo) Attrs(attrs ...field.AssignExpr) IDocTaskDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d docTaskDo) Assign(attrs ...field.AssignExpr) IDocTaskDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d docTaskDo) Joins(fields ...field.RelationField) IDocTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d docTaskDo) Preload(fields ...field.RelationField) IDocTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d docTaskDo) FirstOrInit() (*models.DocTask, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTask), nil
	}
}

func (d docTaskDo) FirstOrCreate() (*models.DocTask, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.DocTask), nil
	}
}

func (d docTaskDo) FindByPage(offset int, limit int) (result []*models.DocTask, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d docTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d docTaskDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d docTaskDo) Delete(models ...*models.DocTask) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *docTaskDo) withDO(do gen.Dao) *docTaskDo {
	d.DO = *do.(*gen.DO)
	return d
}
