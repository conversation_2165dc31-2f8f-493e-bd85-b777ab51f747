package dao

import (
	"context"

	"github.com/influxdata/kapacitor/uuid"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type AppletLabelDAO struct {
	BaseDAO
}

func (a AppletLabelDAO) ListLabel(ctx context.Context) ([]*generated.AppletLabel, error) {
	labelQuery := a.getQueryOrDefault().AppletLabel
	res, err := labelQuery.WithContext(ctx).Find()
	if err != nil {
		stdlog.Errorf("query chain by id err :%v", err)
		return nil, err
	}
	return res, nil
}

func (a AppletLabelDAO) BatchUpsertLabel(ctx context.Context, labels []*generated.AppletLabel) error {
	labelQuery := a.getQueryOrDefault().AppletLabel
	for _, l := range labels {
		if l.ID == "" {
			l.ID = uuid.New().String()
		}
	}
	err := labelQuery.WithContext(ctx).Clauses(
		clause.OnConflict{
			DoNothing: true,
		}).
		CreateInBatches(labels, 10)
	if err != nil {
		stdlog.Errorf("batch save label err :%v", err)
		return err
	}
	return nil
}
