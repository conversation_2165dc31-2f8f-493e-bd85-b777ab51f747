package dao

import (
	"context"

	"gorm.io/gen"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type APIToolCollectionDemoDAO struct {
	BaseDAO
}

type ToolDemoQueryParam struct {
	ProjectID *string
}

func (t *ToolDemoQueryParam) ToQueryConditions() []gen.Condition {
	q := query.APIToolCollectionDemo
	cods := make([]gen.Condition, 0)
	if t.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*t.ProjectID))
	}
	return cods
}

func (a APIToolCollectionDemoDAO) ListByParam(ctx context.Context, param *ToolDemoQueryParam) ([]*generated.APIToolCollectionDemo, error) {
	q := a.getQueryOrDefault().APIToolCollectionDemo
	return q.WithContext(ctx).Where(param.ToQueryConditions()...).Find()
}

// Upsert 存在则更新
func (a APIToolCollectionDemoDAO) Upsert(ctx context.Context, model *generated.APIToolCollectionDemo) error {
	q := a.getQueryOrDefault().APIToolCollectionDemo
	if err := q.WithContext(ctx).Save(model); err != nil {
		return err
	}
	return nil
}
