package dao

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gen"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type APIToolCollectionDAO struct {
	BaseDAO
}

type APICollectionQueryParam struct {
	Name          *string
	Type          *string
	ProjectID     *string
	IDs           []string
	ReleasedState *string
}

func (a APICollectionQueryParam) ToQueryConditions() []gen.Condition {
	cods := make([]gen.Condition, 0)
	q := query.APIToolCollection
	if a.Name != nil {
		cods = append(cods, q.Name.Eq(*a.Name))
	}
	if a.Type != nil {
		cods = append(cods, q.Type.Eq(*a.Type))
	}
	if a.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*a.ProjectID))
	}
	if len(a.IDs) != 0 {
		cods = append(cods, q.ID.In(a.IDs...))
	}
	if a.ReleasedState != nil {
		cods = append(cods, q.ReleasedState.Eq(*a.ReleasedState))
	}
	return cods
}

func (a APIToolCollectionDAO) Create(ctx context.Context, model *generated.APIToolCollection) (string, error) {
	q := a.getQueryOrDefault().APIToolCollection
	ID := uuid.New().String()
	model.ID = ID
	if err := q.WithContext(ctx).Create(model); err != nil {
		return "", err
	}
	return ID, nil
}

func (a APIToolCollectionDAO) UpdateByID(ctx context.Context, ID string, model *generated.APIToolCollection) error {
	q := a.getQueryOrDefault().APIToolCollection
	if _, err := q.WithContext(ctx).Where(q.ID.Eq(ID)).Updates(model); err != nil {
		return err
	}
	return nil
}

func (a APIToolCollectionDAO) GetByID(ctx context.Context, ID string) (*generated.APIToolCollection, error) {
	q := a.getQueryOrDefault().APIToolCollection
	return q.WithContext(ctx).Where(q.ID.Eq(ID)).First()
}

func (a APIToolCollectionDAO) ListByParam(ctx context.Context, param *APICollectionQueryParam) ([]*generated.APIToolCollection, error) {
	q := a.getQueryOrDefault().APIToolCollection
	return q.WithContext(ctx).Where(param.ToQueryConditions()...).Find()
}

func (a APIToolCollectionDAO) BatchDelete(ctx context.Context, param *APICollectionQueryParam) error {
	q := a.getQueryOrDefault().APIToolCollection
	if _, err := q.WithContext(ctx).Where(param.ToQueryConditions()...).Delete(); err != nil {
		return err
	}
	return nil
}
