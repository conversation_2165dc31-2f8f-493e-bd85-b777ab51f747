package dao

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type ChainDebugLogDAO struct {
	db *gorm.DB
}

func NewChainDebugLogDAO() *ChainDebugLogDAO {
	return &ChainDebugLogDAO{db: MustInitDB()}
}

func GetChainDebugLogDAO() *ChainDebugLogDAO {
	return NewChainDebugLogDAO()
}

func (r *ChainDebugLogDAO) SaveDebugLog(ctx context.Context, log *models.ChainDebugLog) error {
	return r.db.Create(log).Error
}

func (r *ChainDebugLogDAO) UpsertDebugLog(ctx context.Context, log *models.ChainDebugLog) error {
	return db.Clauses(clause.OnConflict{
		UpdateAll: true, // 更新所有字段
	}).Create(log).Error
}

func (r *ChainDebugLogDAO) ListDebugLog(ctx context.Context, chatID, uuNodeID string) ([]*models.ChainDebugLog, error) {
	logs := make([]*models.ChainDebugLog, 0)
	if err := r.db.Where(&models.ChainDebugLog{ChatID: chatID, NodeID: models.CastUUNodeID(uuNodeID)}).Find(&logs).Error; err != nil {
		return nil, err
	}
	return logs, nil
}
