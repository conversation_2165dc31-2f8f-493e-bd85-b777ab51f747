"""add tool conversation variables idx

Revision ID: 8ae9bc661daa
Revises: 9fafbd60eca1
Create Date: 2024-01-15 14:22:03.597692

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '8ae9bc661daa'
down_revision = '9fafbd60eca1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_conversation_variables', schema=None) as batch_op:
        batch_op.create_index('conversation_id_idx', ['conversation_id'], unique=False)
        batch_op.create_index('user_id_idx', ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_conversation_variables', schema=None) as batch_op:
        batch_op.drop_index('user_id_idx')
        batch_op.drop_index('conversation_id_idx')

    # ### end Alembic commands ###
