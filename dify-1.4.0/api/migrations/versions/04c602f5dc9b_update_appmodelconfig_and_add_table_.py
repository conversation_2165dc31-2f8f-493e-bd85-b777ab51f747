"""update AppModelConfig and add table TracingAppConfig

Revision ID: 04c602f5dc9b
Revises: 4e99a8df00ff
Create Date: 2024-06-12 07:49:07.666510

"""
import sqlalchemy as sa
from alembic import op

import models.types

# revision identifiers, used by Alembic.
revision = '04c602f5dc9b'
down_revision = '4ff534e1eb11'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tracing_app_configs',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('tracing_provider', sa.String(length=255), nullable=True),
    sa.Column('tracing_config', sa.J<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tracing_app_config_pkey')
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ##
    op.drop_table('tracing_app_configs')

    # ### end Alembic commands ###
