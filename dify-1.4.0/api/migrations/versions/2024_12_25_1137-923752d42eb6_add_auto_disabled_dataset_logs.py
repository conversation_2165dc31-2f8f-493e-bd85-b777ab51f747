"""add_auto_disabled_dataset_logs

Revision ID: 923752d42eb6
Revises: e19037032219
Create Date: 2024-12-25 11:37:55.467101

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '923752d42eb6'
down_revision = 'e19037032219'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_auto_disable_logs',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('dataset_id', models.types.StringUUID(), nullable=False),
    sa.Column('document_id', models.types.StringUUID(), nullable=False),
    sa.Column('notified', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_auto_disable_log_pkey')
    )
    with op.batch_alter_table('dataset_auto_disable_logs', schema=None) as batch_op:
        batch_op.create_index('dataset_auto_disable_log_created_atx', ['created_at'], unique=False)
        batch_op.create_index('dataset_auto_disable_log_dataset_idx', ['dataset_id'], unique=False)
        batch_op.create_index('dataset_auto_disable_log_tenant_idx', ['tenant_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('dataset_auto_disable_logs', schema=None) as batch_op:
        batch_op.drop_index('dataset_auto_disable_log_tenant_idx')
        batch_op.drop_index('dataset_auto_disable_log_dataset_idx')
        batch_op.drop_index('dataset_auto_disable_log_created_atx')

    op.drop_table('dataset_auto_disable_logs')
    # ### end Alembic commands ###
