"""init

Revision ID: 64b051264f32
Revises:
Create Date: 2023-05-13 14:26:59.085018

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '64b051264f32'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')

    op.create_table('account_integrates',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('provider', sa.String(length=16), nullable=False),
    sa.Column('open_id', sa.String(length=255), nullable=False),
    sa.Column('encrypted_token', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='account_integrate_pkey'),
    sa.UniqueConstraint('account_id', 'provider', name='unique_account_provider'),
    sa.UniqueConstraint('provider', 'open_id', name='unique_provider_open_id')
    )
    op.create_table('accounts',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('password', sa.String(length=255), nullable=True),
    sa.Column('password_salt', sa.String(length=255), nullable=True),
    sa.Column('avatar', sa.String(length=255), nullable=True),
    sa.Column('interface_language', sa.String(length=255), nullable=True),
    sa.Column('interface_theme', sa.String(length=255), nullable=True),
    sa.Column('timezone', sa.String(length=255), nullable=True),
    sa.Column('last_login_at', sa.DateTime(), nullable=True),
    sa.Column('last_login_ip', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=16), server_default=sa.text("'active'::character varying"), nullable=False),
    sa.Column('initialized_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='account_pkey')
    )
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.create_index('account_email_idx', ['email'], unique=False)

    op.create_table('api_requests',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('api_token_id', postgresql.UUID(), nullable=False),
    sa.Column('path', sa.String(length=255), nullable=False),
    sa.Column('request', sa.Text(), nullable=True),
    sa.Column('response', sa.Text(), nullable=True),
    sa.Column('ip', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='api_request_pkey')
    )
    with op.batch_alter_table('api_requests', schema=None) as batch_op:
        batch_op.create_index('api_request_token_idx', ['tenant_id', 'api_token_id'], unique=False)

    op.create_table('api_tokens',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=True),
    sa.Column('dataset_id', postgresql.UUID(), nullable=True),
    sa.Column('type', sa.String(length=16), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='api_token_pkey')
    )
    with op.batch_alter_table('api_tokens', schema=None) as batch_op:
        batch_op.create_index('api_token_app_id_type_idx', ['app_id', 'type'], unique=False)
        batch_op.create_index('api_token_token_idx', ['token', 'type'], unique=False)

    op.create_table('app_dataset_joins',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='app_dataset_join_pkey')
    )
    with op.batch_alter_table('app_dataset_joins', schema=None) as batch_op:
        batch_op.create_index('app_dataset_join_app_dataset_idx', ['dataset_id', 'app_id'], unique=False)

    op.create_table('app_model_configs',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('provider', sa.String(length=255), nullable=False),
    sa.Column('model_id', sa.String(length=255), nullable=False),
    sa.Column('configs', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('opening_statement', sa.Text(), nullable=True),
    sa.Column('suggested_questions', sa.Text(), nullable=True),
    sa.Column('suggested_questions_after_answer', sa.Text(), nullable=True),
    sa.Column('more_like_this', sa.Text(), nullable=True),
    sa.Column('model', sa.Text(), nullable=True),
    sa.Column('user_input_form', sa.Text(), nullable=True),
    sa.Column('pre_prompt', sa.Text(), nullable=True),
    sa.Column('agent_mode', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='app_model_config_pkey')
    )
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.create_index('app_app_id_idx', ['app_id'], unique=False)

    op.create_table('apps',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('mode', sa.String(length=255), nullable=False),
    sa.Column('icon', sa.String(length=255), nullable=True),
    sa.Column('icon_background', sa.String(length=255), nullable=True),
    sa.Column('app_model_config_id', postgresql.UUID(), nullable=True),
    sa.Column('status', sa.String(length=255), server_default=sa.text("'normal'::character varying"), nullable=False),
    sa.Column('enable_site', sa.Boolean(), nullable=False),
    sa.Column('enable_api', sa.Boolean(), nullable=False),
    sa.Column('api_rpm', sa.Integer(), nullable=False),
    sa.Column('api_rph', sa.Integer(), nullable=False),
    sa.Column('is_demo', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('is_public', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='app_pkey')
    )
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.create_index('app_tenant_id_idx', ['tenant_id'], unique=False)

    op.execute('CREATE SEQUENCE task_id_sequence;')
    op.execute('CREATE SEQUENCE taskset_id_sequence;')

    op.create_table('celery_taskmeta',
    sa.Column('id', sa.Integer(), nullable=False,
              server_default=sa.text('nextval(\'task_id_sequence\')')),
    sa.Column('task_id', sa.String(length=155), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('result', sa.PickleType(), nullable=True),
    sa.Column('date_done', sa.DateTime(), nullable=True),
    sa.Column('traceback', sa.Text(), nullable=True),
    sa.Column('name', sa.String(length=155), nullable=True),
    sa.Column('args', sa.LargeBinary(), nullable=True),
    sa.Column('kwargs', sa.LargeBinary(), nullable=True),
    sa.Column('worker', sa.String(length=155), nullable=True),
    sa.Column('retries', sa.Integer(), nullable=True),
    sa.Column('queue', sa.String(length=155), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('task_id')
    )
    op.create_table('celery_tasksetmeta',
    sa.Column('id', sa.Integer(), nullable=False,
              server_default=sa.text('nextval(\'taskset_id_sequence\')')),
    sa.Column('taskset_id', sa.String(length=155), nullable=True),
    sa.Column('result', sa.PickleType(), nullable=True),
    sa.Column('date_done', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('taskset_id')
    )
    op.create_table('conversations',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('app_model_config_id', postgresql.UUID(), nullable=False),
    sa.Column('model_provider', sa.String(length=255), nullable=False),
    sa.Column('override_model_configs', sa.Text(), nullable=True),
    sa.Column('model_id', sa.String(length=255), nullable=False),
    sa.Column('mode', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('inputs', sa.JSON(), nullable=True),
    sa.Column('introduction', sa.Text(), nullable=True),
    sa.Column('system_instruction', sa.Text(), nullable=True),
    sa.Column('system_instruction_tokens', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('from_source', sa.String(length=255), nullable=False),
    sa.Column('from_end_user_id', postgresql.UUID(), nullable=True),
    sa.Column('from_account_id', postgresql.UUID(), nullable=True),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('read_account_id', postgresql.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='conversation_pkey')
    )
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.create_index('conversation_app_from_user_idx', ['app_id', 'from_source', 'from_end_user_id'], unique=False)

    op.create_table('dataset_keyword_tables',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('keyword_table', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_keyword_table_pkey'),
    sa.UniqueConstraint('dataset_id')
    )
    with op.batch_alter_table('dataset_keyword_tables', schema=None) as batch_op:
        batch_op.create_index('dataset_keyword_table_dataset_id_idx', ['dataset_id'], unique=False)

    op.create_table('dataset_process_rules',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('mode', sa.String(length=255), server_default=sa.text("'automatic'::character varying"), nullable=False),
    sa.Column('rules', sa.Text(), nullable=True),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_process_rule_pkey')
    )
    with op.batch_alter_table('dataset_process_rules', schema=None) as batch_op:
        batch_op.create_index('dataset_process_rule_dataset_id_idx', ['dataset_id'], unique=False)

    op.create_table('dataset_queries',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('source', sa.String(length=255), nullable=False),
    sa.Column('source_app_id', postgresql.UUID(), nullable=True),
    sa.Column('created_by_role', sa.String(), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_query_pkey')
    )
    with op.batch_alter_table('dataset_queries', schema=None) as batch_op:
        batch_op.create_index('dataset_query_dataset_id_idx', ['dataset_id'], unique=False)

    op.create_table('datasets',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('provider', sa.String(length=255), server_default=sa.text("'vendor'::character varying"), nullable=False),
    sa.Column('permission', sa.String(length=255), server_default=sa.text("'only_me'::character varying"), nullable=False),
    sa.Column('data_source_type', sa.String(length=255), nullable=True),
    sa.Column('indexing_technique', sa.String(length=255), nullable=True),
    sa.Column('index_struct', sa.Text(), nullable=True),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_by', postgresql.UUID(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_pkey')
    )
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.create_index('dataset_tenant_idx', ['tenant_id'], unique=False)

    op.create_table('dify_setups',
    sa.Column('version', sa.String(length=255), nullable=False),
    sa.Column('setup_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('version', name='dify_setup_pkey')
    )
    op.create_table('document_segments',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('document_id', postgresql.UUID(), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('word_count', sa.Integer(), nullable=False),
    sa.Column('tokens', sa.Integer(), nullable=False),
    sa.Column('keywords', sa.JSON(), nullable=True),
    sa.Column('index_node_id', sa.String(length=255), nullable=True),
    sa.Column('index_node_hash', sa.String(length=255), nullable=True),
    sa.Column('hit_count', sa.Integer(), nullable=False),
    sa.Column('enabled', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('disabled_at', sa.DateTime(), nullable=True),
    sa.Column('disabled_by', postgresql.UUID(), nullable=True),
    sa.Column('status', sa.String(length=255), server_default=sa.text("'waiting'::character varying"), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('indexing_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('stopped_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='document_segment_pkey')
    )
    with op.batch_alter_table('document_segments', schema=None) as batch_op:
        batch_op.create_index('document_segment_dataset_id_idx', ['dataset_id'], unique=False)
        batch_op.create_index('document_segment_dataset_node_idx', ['dataset_id', 'index_node_id'], unique=False)
        batch_op.create_index('document_segment_document_id_idx', ['document_id'], unique=False)
        batch_op.create_index('document_segment_tenant_dataset_idx', ['dataset_id', 'tenant_id'], unique=False)
        batch_op.create_index('document_segment_tenant_document_idx', ['document_id', 'tenant_id'], unique=False)

    op.create_table('documents',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('dataset_id', postgresql.UUID(), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('data_source_type', sa.String(length=255), nullable=False),
    sa.Column('data_source_info', sa.Text(), nullable=True),
    sa.Column('dataset_process_rule_id', postgresql.UUID(), nullable=True),
    sa.Column('batch', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('created_from', sa.String(length=255), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_api_request_id', postgresql.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('processing_started_at', sa.DateTime(), nullable=True),
    sa.Column('file_id', sa.Text(), nullable=True),
    sa.Column('word_count', sa.Integer(), nullable=True),
    sa.Column('parsing_completed_at', sa.DateTime(), nullable=True),
    sa.Column('cleaning_completed_at', sa.DateTime(), nullable=True),
    sa.Column('splitting_completed_at', sa.DateTime(), nullable=True),
    sa.Column('tokens', sa.Integer(), nullable=True),
    sa.Column('indexing_latency', sa.Float(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('is_paused', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('paused_by', postgresql.UUID(), nullable=True),
    sa.Column('paused_at', sa.DateTime(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('stopped_at', sa.DateTime(), nullable=True),
    sa.Column('indexing_status', sa.String(length=255), server_default=sa.text("'waiting'::character varying"), nullable=False),
    sa.Column('enabled', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('disabled_at', sa.DateTime(), nullable=True),
    sa.Column('disabled_by', postgresql.UUID(), nullable=True),
    sa.Column('archived', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('archived_reason', sa.String(length=255), nullable=True),
    sa.Column('archived_by', postgresql.UUID(), nullable=True),
    sa.Column('archived_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('doc_type', sa.String(length=40), nullable=True),
    sa.Column('doc_metadata', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='document_pkey')
    )
    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.create_index('document_dataset_id_idx', ['dataset_id'], unique=False)
        batch_op.create_index('document_is_paused_idx', ['is_paused'], unique=False)

    op.create_table('embeddings',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('hash', sa.String(length=64), nullable=False),
    sa.Column('embedding', sa.LargeBinary(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='embedding_pkey'),
    sa.UniqueConstraint('hash', name='embedding_hash_idx')
    )
    op.create_table('end_users',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=True),
    sa.Column('type', sa.String(length=255), nullable=False),
    sa.Column('external_user_id', sa.String(length=255), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('is_anonymous', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='end_user_pkey')
    )
    with op.batch_alter_table('end_users', schema=None) as batch_op:
        batch_op.create_index('end_user_session_id_idx', ['session_id', 'type'], unique=False)
        batch_op.create_index('end_user_tenant_session_id_idx', ['tenant_id', 'session_id', 'type'], unique=False)

    op.create_table('installed_apps',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('app_owner_tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('is_pinned', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='installed_app_pkey'),
    sa.UniqueConstraint('tenant_id', 'app_id', name='unique_tenant_app')
    )
    with op.batch_alter_table('installed_apps', schema=None) as batch_op:
        batch_op.create_index('installed_app_app_id_idx', ['app_id'], unique=False)
        batch_op.create_index('installed_app_tenant_id_idx', ['tenant_id'], unique=False)

    op.create_table('invitation_codes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('batch', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=32), nullable=False),
    sa.Column('status', sa.String(length=16), server_default=sa.text("'unused'::character varying"), nullable=False),
    sa.Column('used_at', sa.DateTime(), nullable=True),
    sa.Column('used_by_tenant_id', postgresql.UUID(), nullable=True),
    sa.Column('used_by_account_id', postgresql.UUID(), nullable=True),
    sa.Column('deprecated_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='invitation_code_pkey')
    )
    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.create_index('invitation_codes_batch_idx', ['batch'], unique=False)
        batch_op.create_index('invitation_codes_code_idx', ['code', 'status'], unique=False)

    op.create_table('message_agent_thoughts',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('message_id', postgresql.UUID(), nullable=False),
    sa.Column('message_chain_id', postgresql.UUID(), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('thought', sa.Text(), nullable=True),
    sa.Column('tool', sa.Text(), nullable=True),
    sa.Column('tool_input', sa.Text(), nullable=True),
    sa.Column('observation', sa.Text(), nullable=True),
    sa.Column('tool_process_data', sa.Text(), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('message_token', sa.Integer(), nullable=True),
    sa.Column('message_unit_price', sa.Numeric(), nullable=True),
    sa.Column('answer', sa.Text(), nullable=True),
    sa.Column('answer_token', sa.Integer(), nullable=True),
    sa.Column('answer_unit_price', sa.Numeric(), nullable=True),
    sa.Column('tokens', sa.Integer(), nullable=True),
    sa.Column('total_price', sa.Numeric(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('latency', sa.Float(), nullable=True),
    sa.Column('created_by_role', sa.String(), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='message_agent_thought_pkey')
    )
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.create_index('message_agent_thought_message_chain_id_idx', ['message_chain_id'], unique=False)
        batch_op.create_index('message_agent_thought_message_id_idx', ['message_id'], unique=False)

    op.create_table('message_chains',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('message_id', postgresql.UUID(), nullable=False),
    sa.Column('type', sa.String(length=255), nullable=False),
    sa.Column('input', sa.Text(), nullable=True),
    sa.Column('output', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='message_chain_pkey')
    )
    with op.batch_alter_table('message_chains', schema=None) as batch_op:
        batch_op.create_index('message_chain_message_id_idx', ['message_id'], unique=False)

    op.create_table('message_feedbacks',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('conversation_id', postgresql.UUID(), nullable=False),
    sa.Column('message_id', postgresql.UUID(), nullable=False),
    sa.Column('rating', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('from_source', sa.String(length=255), nullable=False),
    sa.Column('from_end_user_id', postgresql.UUID(), nullable=True),
    sa.Column('from_account_id', postgresql.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='message_feedback_pkey')
    )
    with op.batch_alter_table('message_feedbacks', schema=None) as batch_op:
        batch_op.create_index('message_feedback_app_idx', ['app_id'], unique=False)
        batch_op.create_index('message_feedback_conversation_idx', ['conversation_id', 'from_source', 'rating'], unique=False)
        batch_op.create_index('message_feedback_message_idx', ['message_id', 'from_source'], unique=False)

    op.create_table('operation_logs',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('action', sa.String(length=255), nullable=False),
    sa.Column('content', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('created_ip', sa.String(length=255), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='operation_log_pkey')
    )
    with op.batch_alter_table('operation_logs', schema=None) as batch_op:
        batch_op.create_index('operation_log_account_action_idx', ['tenant_id', 'account_id', 'action'], unique=False)

    op.create_table('pinned_conversations',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('conversation_id', postgresql.UUID(), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pinned_conversation_pkey')
    )
    with op.batch_alter_table('pinned_conversations', schema=None) as batch_op:
        batch_op.create_index('pinned_conversation_conversation_idx', ['app_id', 'conversation_id', 'created_by'], unique=False)

    op.create_table('providers',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('provider_name', sa.String(length=40), nullable=False),
    sa.Column('provider_type', sa.String(length=40), nullable=False, server_default=sa.text("'custom'::character varying")),
    sa.Column('encrypted_config', sa.Text(), nullable=True),
    sa.Column('is_valid', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.Column('quota_type', sa.String(length=40), nullable=True, server_default=sa.text("''::character varying")),
    sa.Column('quota_limit', sa.Integer(), nullable=True),
    sa.Column('quota_used', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='provider_pkey'),
    sa.UniqueConstraint('tenant_id', 'provider_name', 'provider_type', 'quota_type', name='unique_provider_name_type_quota')
    )
    with op.batch_alter_table('providers', schema=None) as batch_op:
        batch_op.create_index('provider_tenant_id_provider_idx', ['tenant_id', 'provider_name'], unique=False)

    op.create_table('recommended_apps',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('description', sa.JSON(), nullable=False),
    sa.Column('copyright', sa.String(length=255), nullable=False),
    sa.Column('privacy_policy', sa.String(length=255), nullable=False),
    sa.Column('category', sa.String(length=255), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False),
    sa.Column('is_listed', sa.Boolean(), nullable=False),
    sa.Column('install_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='recommended_app_pkey')
    )
    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.create_index('recommended_app_app_id_idx', ['app_id'], unique=False)
        batch_op.create_index('recommended_app_is_listed_idx', ['is_listed'], unique=False)

    op.create_table('saved_messages',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('message_id', postgresql.UUID(), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='saved_message_pkey')
    )
    with op.batch_alter_table('saved_messages', schema=None) as batch_op:
        batch_op.create_index('saved_message_message_idx', ['app_id', 'message_id', 'created_by'], unique=False)

    op.create_table('sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('data', sa.LargeBinary(), nullable=True),
    sa.Column('expiry', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('session_id')
    )
    op.create_table('sites',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('icon', sa.String(length=255), nullable=True),
    sa.Column('icon_background', sa.String(length=255), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('default_language', sa.String(length=255), nullable=False),
    sa.Column('copyright', sa.String(length=255), nullable=True),
    sa.Column('privacy_policy', sa.String(length=255), nullable=True),
    sa.Column('customize_domain', sa.String(length=255), nullable=True),
    sa.Column('customize_token_strategy', sa.String(length=255), nullable=False),
    sa.Column('prompt_public', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('status', sa.String(length=255), server_default=sa.text("'normal'::character varying"), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('code', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id', name='site_pkey')
    )
    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.create_index('site_app_id_idx', ['app_id'], unique=False)
        batch_op.create_index('site_code_idx', ['code', 'status'], unique=False)

    op.create_table('tenant_account_joins',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('role', sa.String(length=16), server_default='normal', nullable=False),
    sa.Column('invited_by', postgresql.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tenant_account_join_pkey'),
    sa.UniqueConstraint('tenant_id', 'account_id', name='unique_tenant_account_join')
    )
    with op.batch_alter_table('tenant_account_joins', schema=None) as batch_op:
        batch_op.create_index('tenant_account_join_account_id_idx', ['account_id'], unique=False)
        batch_op.create_index('tenant_account_join_tenant_id_idx', ['tenant_id'], unique=False)

    op.create_table('tenants',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('encrypt_public_key', sa.Text(), nullable=True),
    sa.Column('plan', sa.String(length=255), server_default=sa.text("'basic'::character varying"), nullable=False),
    sa.Column('status', sa.String(length=255), server_default=sa.text("'normal'::character varying"), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tenant_pkey')
    )
    op.create_table('upload_files',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('storage_type', sa.String(length=255), nullable=False),
    sa.Column('key', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('size', sa.Integer(), nullable=False),
    sa.Column('extension', sa.String(length=255), nullable=False),
    sa.Column('mime_type', sa.String(length=255), nullable=True),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('used', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('used_by', postgresql.UUID(), nullable=True),
    sa.Column('used_at', sa.DateTime(), nullable=True),
    sa.Column('hash', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id', name='upload_file_pkey')
    )
    with op.batch_alter_table('upload_files', schema=None) as batch_op:
        batch_op.create_index('upload_file_tenant_idx', ['tenant_id'], unique=False)

    op.create_table('message_annotations',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('conversation_id', postgresql.UUID(), nullable=False),
    sa.Column('message_id', postgresql.UUID(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='message_annotation_pkey')
    )
    with op.batch_alter_table('message_annotations', schema=None) as batch_op:
        batch_op.create_index('message_annotation_app_idx', ['app_id'], unique=False)
        batch_op.create_index('message_annotation_conversation_idx', ['conversation_id'], unique=False)
        batch_op.create_index('message_annotation_message_idx', ['message_id'], unique=False)

    op.create_table('messages',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('model_provider', sa.String(length=255), nullable=False),
    sa.Column('model_id', sa.String(length=255), nullable=False),
    sa.Column('override_model_configs', sa.Text(), nullable=True),
    sa.Column('conversation_id', postgresql.UUID(), nullable=False),
    sa.Column('inputs', sa.JSON(), nullable=True),
    sa.Column('query', sa.Text(), nullable=False),
    sa.Column('message', sa.JSON(), nullable=False),
    sa.Column('message_tokens', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('message_unit_price', sa.Numeric(precision=10, scale=4), nullable=False),
    sa.Column('answer', sa.Text(), nullable=False),
    sa.Column('answer_tokens', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('answer_unit_price', sa.Numeric(precision=10, scale=4), nullable=False),
    sa.Column('provider_response_latency', sa.Float(), server_default=sa.text('0'), nullable=False),
    sa.Column('total_price', sa.Numeric(precision=10, scale=7), nullable=True),
    sa.Column('currency', sa.String(length=255), nullable=False),
    sa.Column('from_source', sa.String(length=255), nullable=False),
    sa.Column('from_end_user_id', postgresql.UUID(), nullable=True),
    sa.Column('from_account_id', postgresql.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('agent_based', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='message_pkey')
    )
    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.create_index('message_account_idx', ['app_id', 'from_source', 'from_account_id'], unique=False)
        batch_op.create_index('message_app_id_idx', ['app_id', 'created_at'], unique=False)
        batch_op.create_index('message_conversation_id_idx', ['conversation_id'], unique=False)
        batch_op.create_index('message_end_user_idx', ['app_id', 'from_source', 'from_end_user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.drop_index('message_end_user_idx')
        batch_op.drop_index('message_conversation_id_idx')
        batch_op.drop_index('message_app_id_idx')
        batch_op.drop_index('message_account_idx')

    op.drop_table('messages')
    with op.batch_alter_table('message_annotations', schema=None) as batch_op:
        batch_op.drop_index('message_annotation_message_idx')
        batch_op.drop_index('message_annotation_conversation_idx')
        batch_op.drop_index('message_annotation_app_idx')

    op.drop_table('message_annotations')
    with op.batch_alter_table('upload_files', schema=None) as batch_op:
        batch_op.drop_index('upload_file_tenant_idx')

    op.drop_table('upload_files')
    op.drop_table('tenants')
    with op.batch_alter_table('tenant_account_joins', schema=None) as batch_op:
        batch_op.drop_index('tenant_account_join_tenant_id_idx')
        batch_op.drop_index('tenant_account_join_account_id_idx')

    op.drop_table('tenant_account_joins')
    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.drop_index('site_code_idx')
        batch_op.drop_index('site_app_id_idx')

    op.drop_table('sites')
    op.drop_table('sessions')
    with op.batch_alter_table('saved_messages', schema=None) as batch_op:
        batch_op.drop_index('saved_message_message_idx')

    op.drop_table('saved_messages')
    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.drop_index('recommended_app_is_listed_idx')
        batch_op.drop_index('recommended_app_app_id_idx')

    op.drop_table('recommended_apps')
    with op.batch_alter_table('providers', schema=None) as batch_op:
        batch_op.drop_index('provider_tenant_id_provider_idx')

    op.drop_table('providers')
    with op.batch_alter_table('pinned_conversations', schema=None) as batch_op:
        batch_op.drop_index('pinned_conversation_conversation_idx')

    op.drop_table('pinned_conversations')
    with op.batch_alter_table('operation_logs', schema=None) as batch_op:
        batch_op.drop_index('operation_log_account_action_idx')

    op.drop_table('operation_logs')
    with op.batch_alter_table('message_feedbacks', schema=None) as batch_op:
        batch_op.drop_index('message_feedback_message_idx')
        batch_op.drop_index('message_feedback_conversation_idx')
        batch_op.drop_index('message_feedback_app_idx')

    op.drop_table('message_feedbacks')
    with op.batch_alter_table('message_chains', schema=None) as batch_op:
        batch_op.drop_index('message_chain_message_id_idx')

    op.drop_table('message_chains')
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.drop_index('message_agent_thought_message_id_idx')
        batch_op.drop_index('message_agent_thought_message_chain_id_idx')

    op.drop_table('message_agent_thoughts')
    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.drop_index('invitation_codes_code_idx')
        batch_op.drop_index('invitation_codes_batch_idx')

    op.drop_table('invitation_codes')
    with op.batch_alter_table('installed_apps', schema=None) as batch_op:
        batch_op.drop_index('installed_app_tenant_id_idx')
        batch_op.drop_index('installed_app_app_id_idx')

    op.drop_table('installed_apps')
    with op.batch_alter_table('end_users', schema=None) as batch_op:
        batch_op.drop_index('end_user_tenant_session_id_idx')
        batch_op.drop_index('end_user_session_id_idx')

    op.drop_table('end_users')
    op.drop_table('embeddings')
    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.drop_index('document_is_paused_idx')
        batch_op.drop_index('document_dataset_id_idx')

    op.drop_table('documents')
    with op.batch_alter_table('document_segments', schema=None) as batch_op:
        batch_op.drop_index('document_segment_tenant_document_idx')
        batch_op.drop_index('document_segment_tenant_dataset_idx')
        batch_op.drop_index('document_segment_document_id_idx')
        batch_op.drop_index('document_segment_dataset_node_idx')
        batch_op.drop_index('document_segment_dataset_id_idx')

    op.drop_table('document_segments')
    op.drop_table('dify_setups')
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.drop_index('dataset_tenant_idx')

    op.drop_table('datasets')
    with op.batch_alter_table('dataset_queries', schema=None) as batch_op:
        batch_op.drop_index('dataset_query_dataset_id_idx')

    op.drop_table('dataset_queries')
    with op.batch_alter_table('dataset_process_rules', schema=None) as batch_op:
        batch_op.drop_index('dataset_process_rule_dataset_id_idx')

    op.drop_table('dataset_process_rules')
    with op.batch_alter_table('dataset_keyword_tables', schema=None) as batch_op:
        batch_op.drop_index('dataset_keyword_table_dataset_id_idx')

    op.drop_table('dataset_keyword_tables')
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.drop_index('conversation_app_from_user_idx')

    op.drop_table('conversations')
    op.drop_table('celery_tasksetmeta')
    op.drop_table('celery_taskmeta')

    op.execute('DROP SEQUENCE taskset_id_sequence;')
    op.execute('DROP SEQUENCE task_id_sequence;')
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.drop_index('app_tenant_id_idx')

    op.drop_table('apps')
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.drop_index('app_app_id_idx')

    op.drop_table('app_model_configs')
    with op.batch_alter_table('app_dataset_joins', schema=None) as batch_op:
        batch_op.drop_index('app_dataset_join_app_dataset_idx')

    op.drop_table('app_dataset_joins')
    with op.batch_alter_table('api_tokens', schema=None) as batch_op:
        batch_op.drop_index('api_token_token_idx')
        batch_op.drop_index('api_token_app_id_type_idx')

    op.drop_table('api_tokens')
    with op.batch_alter_table('api_requests', schema=None) as batch_op:
        batch_op.drop_index('api_request_token_idx')

    op.drop_table('api_requests')
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.drop_index('account_email_idx')

    op.drop_table('accounts')
    op.drop_table('account_integrates')

    op.execute('DROP EXTENSION IF EXISTS "uuid-ossp";')
    # ### end Alembic commands ###
