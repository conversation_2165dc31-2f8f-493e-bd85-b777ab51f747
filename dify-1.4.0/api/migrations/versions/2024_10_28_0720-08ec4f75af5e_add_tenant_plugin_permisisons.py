"""add_tenant_plugin_permisisons

Revision ID: 08ec4f75af5e
Revises: ddcc8bbef391
Create Date: 2024-10-28 07:20:39.711124

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '08ec4f75af5e'
down_revision = 'ddcc8bbef391'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('account_plugin_permissions',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('install_permission', sa.String(length=16), server_default='everyone', nullable=False),
    sa.Column('debug_permission', sa.String(length=16), server_default='noone', nullable=False),
    sa.PrimaryKeyConstraint('id', name='account_plugin_permission_pkey'),
    sa.UniqueConstraint('tenant_id', name='unique_tenant_plugin')
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('account_plugin_permissions')
    # ### end Alembic commands ###
