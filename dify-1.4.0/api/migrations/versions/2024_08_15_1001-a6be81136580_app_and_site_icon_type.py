"""app and site icon type

Revision ID: a6be81136580
Revises: 8782057ff0dc
Create Date: 2024-08-15 10:01:24.697888

"""
import sqlalchemy as sa
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = 'a6be81136580'
down_revision = '8782057ff0dc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.add_column(sa.Column('icon_type', sa.String(length=255), nullable=True))

    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.add_column(sa.Column('icon_type', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.drop_column('icon_type')

    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.drop_column('icon_type')

    # ### end Alembic commands ###
