"""add tool meta

Revision ID: c3311b089690
Revises: e2eacc9a1b63
Create Date: 2024-03-28 11:50:45.364875

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'c3311b089690'
down_revision = 'e2eacc9a1b63'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tool_meta_str', sa.Text(), server_default=sa.text("'{}'::text"), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.drop_column('tool_meta_str')

    # ### end Alembic commands ###
