"""add_rate_limit_logs

Revision ID: f051706725cc
Revises: 923752d42eb6
Create Date: 2025-01-14 06:17:35.536388

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f051706725cc'
down_revision = 'ee79d9b1c156'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rate_limit_logs',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('subscription_plan', sa.String(length=255), nullable=False),
    sa.Column('operation', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='rate_limit_log_pkey')
    )
    with op.batch_alter_table('rate_limit_logs', schema=None) as batch_op:
        batch_op.create_index('rate_limit_log_operation_idx', ['operation'], unique=False)
        batch_op.create_index('rate_limit_log_tenant_idx', ['tenant_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('rate_limit_logs', schema=None) as batch_op:
        batch_op.drop_index('rate_limit_log_tenant_idx')
        batch_op.drop_index('rate_limit_log_operation_idx')

    op.drop_table('rate_limit_logs')
    # ### end Alembic commands ###
