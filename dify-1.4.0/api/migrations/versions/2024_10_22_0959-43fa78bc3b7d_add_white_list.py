"""add_white_list

Revision ID: 43fa78bc3b7d
Revises: 0251a1c768cc
Create Date: 2024-10-22 09:59:23.713716

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '43fa78bc3b7d'
down_revision = '0251a1c768cc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('whitelists',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=True),
    sa.Column('category', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='whitelists_pkey')
    )
    with op.batch_alter_table('whitelists', schema=None) as batch_op:
        batch_op.create_index('whitelists_tenant_idx', ['tenant_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('whitelists', schema=None) as batch_op:
        batch_op.drop_index('whitelists_tenant_idx')

    op.drop_table('whitelists')
    # ### end Alembic commands ###
