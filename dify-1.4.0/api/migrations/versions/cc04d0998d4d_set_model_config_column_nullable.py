"""set model config column nullable

Revision ID: cc04d0998d4d
Revises: b289e2408ee2
Create Date: 2024-02-27 03:47:47.376325

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cc04d0998d4d'
down_revision = 'b289e2408ee2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.alter_column('provider',
                              existing_type=sa.VARCHAR(length=255),
                              nullable=True)
        batch_op.alter_column('model_id',
                              existing_type=sa.VARCHAR(length=255),
                              nullable=True)
        batch_op.alter_column('configs',
                              existing_type=postgresql.JSON(astext_type=sa.Text()),
                              nullable=True)

    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.alter_column('api_rpm',
                              existing_type=sa.Integer(),
                              server_default='0',
                              nullable=False)

        batch_op.alter_column('api_rph',
                              existing_type=sa.Integer(),
                              server_default='0',
                              nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.alter_column('api_rpm',
                              existing_type=sa.Integer(),
                              server_default=None,
                              nullable=False)

        batch_op.alter_column('api_rph',
                              existing_type=sa.Integer(),
                              server_default=None,
                              nullable=False)

    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.alter_column('configs',
                              existing_type=postgresql.JSON(astext_type=sa.Text()),
                              nullable=False)
        batch_op.alter_column('model_id',
                              existing_type=sa.VARCHAR(length=255),
                              nullable=False)
        batch_op.alter_column('provider',
                              existing_type=sa.VARCHAR(length=255),
                              nullable=False)

    # ### end Alembic commands ###
