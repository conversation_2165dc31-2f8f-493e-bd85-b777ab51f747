"""add tool_invoke_model_log

Revision ID: c71211c8f604
Revises: f25003750af4
Create Date: 2024-01-09 11:42:50.664797

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c71211c8f604'
down_revision = 'f25003750af4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tool_model_invokes',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('user_id', postgresql.UUID(), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('provider', sa.String(length=40), nullable=False),
    sa.Column('tool_type', sa.String(length=40), nullable=False),
    sa.Column('tool_name', sa.String(length=40), nullable=False),
    sa.Column('tool_id', postgresql.UUID(), nullable=False),
    sa.Column('model_parameters', sa.Text(), nullable=False),
    sa.Column('prompt_messages', sa.Text(), nullable=False),
    sa.Column('model_response', sa.Text(), nullable=False),
    sa.Column('prompt_tokens', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('answer_tokens', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('answer_unit_price', sa.Numeric(precision=10, scale=4), nullable=False),
    sa.Column('answer_price_unit', sa.Numeric(precision=10, scale=7), server_default=sa.text('0.001'), nullable=False),
    sa.Column('provider_response_latency', sa.Float(), server_default=sa.text('0'), nullable=False),
    sa.Column('total_price', sa.Numeric(precision=10, scale=7), nullable=True),
    sa.Column('currency', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_model_invoke_pkey')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tool_model_invokes')
    # ### end Alembic commands ###
