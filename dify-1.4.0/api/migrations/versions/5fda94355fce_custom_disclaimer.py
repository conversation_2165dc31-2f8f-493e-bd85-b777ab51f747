"""Custom Disclaimer

Revision ID: 5fda94355fce
Revises: 47cc7df8c4f3
Create Date: 2024-05-10 20:04:45.806549

"""
import sqlalchemy as sa
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = '5fda94355fce'
down_revision = '47cc7df8c4f3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.add_column(sa.Column('custom_disclaimer', sa.String(length=255), nullable=True))

    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.add_column(sa.Column('custom_disclaimer', sa.String(length=255), nullable=True))

    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('custom_disclaimer', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.drop_column('custom_disclaimer')

    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.drop_column('custom_disclaimer')

    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.drop_column('custom_disclaimer')

    # ### end Alembic commands ###
