"""add_dataset_collection_binding

Revision ID: 6e2cfb077b04
Revises: 77e83833755c
Create Date: 2023-09-13 22:16:48.027810

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6e2cfb077b04'
down_revision = '77e83833755c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dataset_collection_bindings',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('provider_name', sa.String(length=40), nullable=False),
    sa.Column('model_name', sa.String(length=40), nullable=False),
    sa.Column('collection_name', sa.String(length=64), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='dataset_collection_bindings_pkey')
    )
    with op.batch_alter_table('dataset_collection_bindings', schema=None) as batch_op:
        batch_op.create_index('provider_model_name_idx', ['provider_name', 'model_name'], unique=False)

    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.add_column(sa.Column('collection_binding_id', postgresql.UUID(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.drop_column('collection_binding_id')

    with op.batch_alter_table('dataset_collection_bindings', schema=None) as batch_op:
        batch_op.drop_index('provider_model_name_idx')

    op.drop_table('dataset_collection_bindings')
    # ### end Alembic commands ###
