# OpenAI API Key
OPENAI_API_KEY=

# Azure OpenAI API Base Endpoint & API Key
AZURE_OPENAI_API_BASE=
AZURE_OPENAI_API_KEY=

# Anthropic API Key
ANTHROPIC_API_KEY=

# Replicate API Key
REPLICATE_API_KEY=

# Hugging Face API Key
HUGGINGFACE_API_KEY=
HUGGINGFACE_TEXT_GEN_ENDPOINT_URL=
HUGGINGFACE_TEXT2TEXT_GEN_ENDPOINT_URL=
HUGGINGFACE_EMBEDDINGS_ENDPOINT_URL=

# Minimax Credentials
MINIMAX_API_KEY=
MINIMAX_GROUP_ID=

# Spark Credentials
SPARK_APP_ID=
SPARK_API_KEY=
SPARK_API_SECRET=

# Tongyi Credentials
TONGYI_DASHSCOPE_API_KEY=

# Wenxin Credentials
WENXIN_API_KEY=
WENXIN_SECRET_KEY=

# ZhipuAI Credentials
ZHIPUAI_API_KEY=

# Baichuan Credentials
BAICHUAN_API_KEY=
BAICHUAN_SECRET_KEY=

# ChatGLM Credentials
CHATGLM_API_BASE=

# Xinference Credentials
XINFERENCE_SERVER_URL=
XINFERENCE_GENERATION_MODEL_UID=
XINFERENCE_CHAT_MODEL_UID=
XINFERENCE_EMBEDDINGS_MODEL_UID=
XINFERENCE_RERANK_MODEL_UID=

# OpenLLM Credentials
OPENLLM_SERVER_URL=

# LocalAI Credentials
LOCALAI_SERVER_URL=

# Cohere Credentials
COHERE_API_KEY=

# Jina Credentials
JINA_API_KEY=

# Ollama Credentials
OLLAMA_BASE_URL=

# Together API Key
TOGETHER_API_KEY=

# Mock Switch
MOCK_SWITCH=false

# CODE EXECUTION CONFIGURATION
CODE_EXECUTION_ENDPOINT=
CODE_EXECUTION_API_KEY=

# Volcengine MaaS Credentials
VOLC_API_KEY=
VOLC_SECRET_KEY=
VOLC_MODEL_ENDPOINT_ID=
VOLC_EMBEDDING_ENDPOINT_ID=

# 360 AI Credentials
ZHINAO_API_KEY=

# Plugin configuration
PLUGIN_DAEMON_KEY=
PLUGIN_DAEMON_URL=

# Marketplace configuration
MARKETPLACE_API_URL=
# VESSL AI Credentials
VESSL_AI_MODEL_NAME=
VESSL_AI_API_KEY=
VESSL_AI_ENDPOINT_URL=

# GPUStack Credentials
GPUSTACK_SERVER_URL=
GPUSTACK_API_KEY=

# Gitee AI Credentials
GITEE_AI_API_KEY=

# xAI Credentials
XAI_API_KEY=
XAI_API_BASE=
