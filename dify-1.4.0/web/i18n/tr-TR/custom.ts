const translation = {
  custom: '<PERSON><PERSON><PERSON>ştirme',
  upgradeTip: {
    prefix: 'Markanızı özelleştirmek için planınızı yükseltin',
    suffix: '.',
    des: 'Markanızı özelleştirmek için planınızı yükseltin',
    title: '<PERSON>ınızı yükseltin',
  },
  webapp: {
    title: 'WebApp markasını özelleştir',
    removeBrand: 'Powered by Dify\'i kaldır',
    changeLogo: 'Powered by Brand Resmini <PERSON>ti<PERSON>',
    changeLogoTip: 'SVG veya PNG formatında, en az 40x40px boyutunda',
  },
  app: {
    title: 'Uygulama başlığı markasını özelleştir',
    changeLogoTip: 'SVG veya PNG formatında, en az 80x80px boyutunda',
  },
  upload: 'Yükle',
  uploading: 'Yükleniyor',
  uploadedFail: 'Resim yükleme başarısız oldu, lütfen tekrar yükleyin.',
  change: '<PERSON><PERSON><PERSON>ti<PERSON>',
  apply: 'Uygula',
  restore: 'Varsay<PERSON>lan Ayarları Geri Yükle',
  customize: {
    contactUs: ' bizimle iletişime geçin ',
    prefix: 'Uygulama içindeki marka logosunu özelleştirmek için, lütfen',
    suffix: 'Enterprise sürümüne yükseltin.',
  },
}

export default translation
