const translation = {
  knowledge: '<PERSON><PERSON><PERSON> thức',
  documentCount: ' tài liệu',
  wordCount: ' nghìn từ',
  appCount: ' ứng dụng liên kết',
  createDataset: 'Tạo bộ kiến thức',
  createDatasetIntro: 'Nhập dữ liệu văn bản của bạn hoặc cập nhật dữ liệu theo thời gian thực qua Webhook để tăng cường ngữ cảnh cho LLM.',
  deleteDatasetConfirmTitle: 'Xóa bộ kiến thức này?',
  deleteDatasetConfirmContent:
    'Việc xóa bộ kiến thức là không thể hoàn tác. Người dùng sẽ không còn truy cập được vào bộ kiến thức của bạn, và tất cả cấu hình cùng nhật ký lời nhắc sẽ bị xóa vĩnh viễn.',
  datasetUsedByApp: 'Bộ kiến thức này đang được sử dụng bởi một số ứng dụng. Các ứng dụng sẽ không thể sử dụng bộ kiến thức này nữa, và tất cả cấu hình lời nhắc cùng nhật ký sẽ bị xóa vĩnh viễn.',
  datasetDeleted: 'Bộ kiến thức đã được xóa',
  datasetDeleteFailed: 'Xóa bộ kiến thức không thành công',
  didYouKnow: 'Bạn có biết?',
  intro1: 'Bộ kiến thức có thể được tích hợp vào ứng dụng Dify ',
  intro2: 'như một ngữ cảnh',
  intro3: ',',
  intro4: 'hoặc ',
  intro5: 'có thể được tạo',
  intro6: ' dưới dạng một plugin chỉ mục ChatGPT độc lập để xuất bản',
  unavailable: 'Không khả dụng',
  unavailableTip: 'Mô hình nhúng không khả dụng, cần cấu hình mô hình nhúng mặc định',
  datasets: 'BỘ KIẾN THỨC',
  datasetsApi: 'API',
  retrieval: {
    semantic_search: {
      title: 'Tìm kiếm Vector',
      description: 'Tạo các nhúng truy vấn và tìm kiếm đoạn văn bản tương tự nhất với biểu diễn vector của nó.',
    },
    full_text_search: {
      title: 'Tìm kiếm Toàn văn bản',
      description: 'Lập chỉ mục cho tất cả các thuật ngữ trong tài liệu, cho phép người dùng tìm kiếm bất kỳ thuật ngữ nào và truy xuất đoạn văn bản liên quan chứa các thuật ngữ đó.',
    },
    hybrid_search: {
      title: 'Tìm kiếm Kết hợp',
      description: 'Thực hiện tìm kiếm toàn văn bản và tìm kiếm vector đồng thời, sắp xếp lại để chọn kết quả phù hợp nhất với truy vấn của người dùng. Yêu cầu cấu hình API mô hình Rerank.',
      recommend: 'Đề xuất',
    },
    invertedIndex: {
      title: 'Chỉ mục Ngược',
      description: 'Chỉ mục Ngược là một cấu trúc được sử dụng cho việc truy xuất hiệu quả. Nó được tổ chức theo thuật ngữ, mỗi thuật ngữ trỏ đến tài liệu hoặc trang web chứa nó.',
    },
    change: 'Thay đổi',
    changeRetrievalMethod: 'Thay đổi phương pháp truy xuất',
  },
  docsFailedNotice: 'tài liệu không được lập chỉ mục',
  retry: 'Thử lại',
  indexingTechnique: {
    high_quality: 'CHẤT LƯỢNG',
    economy: 'TIẾT KIỆM',
  },
  indexingMethod: {
    semantic_search: 'VECTOR',
    full_text_search: 'VĂN BẢN ĐẦY ĐỦ',
    hybrid_search: 'KẾT HỢP',
    invertedIndex: 'ĐẢO NGƯỢC',
  },
  mixtureHighQualityAndEconomicTip: 'Mô hình xếp hạng lại là cần thiết cho sự kết hợp của các cơ sở kiến thức chất lượng cao và tiết kiệm.',
  inconsistentEmbeddingModelTip: 'Mô hình xếp hạng lại là cần thiết nếu các mô hình nhúng của các cơ sở kiến thức được chọn không nhất quán.',
  retrievalSettings: 'Cài đặt truy xuất',
  rerankSettings: 'Cài đặt xếp hạng lại',
  weightedScore: {
    title: 'Điểm số có trọng số',
    description: 'Bằng cách điều chỉnh trọng số được gán, chiến lược xếp hạng lại này xác định liệu ưu tiên khớp ngữ nghĩa hay từ khóa.',
    semanticFirst: 'Ngữ nghĩa trước',
    keywordFirst: 'Từ khóa trước',
    customized: 'Tùy chỉnh',
    semantic: 'Ngữ nghĩa',
    keyword: 'Từ khóa',
  },
  nTo1RetrievalLegacy: 'Truy xuất N-đến-1 sẽ chính thức bị loại bỏ từ tháng 9. Khuyến nghị sử dụng truy xuất đa đường dẫn mới nhất để có kết quả tốt hơn.',
  nTo1RetrievalLegacyLink: 'Tìm hiểu thêm',
  nTo1RetrievalLegacyLinkText: 'Truy xuất N-đến-1 sẽ chính thức bị loại bỏ vào tháng 9.',
  defaultRetrievalTip: 'Truy xuất nhiều đường dẫn được sử dụng theo mặc định. Kiến thức được lấy từ nhiều cơ sở kiến thức và sau đó được xếp hạng lại.',
  editExternalAPIConfirmWarningContent: {
    front: 'API Kiến thức Bên ngoài này được liên kết với',
    end: 'kiến thức bên ngoài, và sửa đổi này sẽ được áp dụng cho tất cả chúng. Bạn có chắc chắn muốn lưu thay đổi này không?',
  },
  editExternalAPIFormWarning: {
    front: 'API bên ngoài này được liên kết với',
    end: 'Kiến thức bên ngoài',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      end: '?',
      front: 'Xóa',
    },
    content: {
      front: 'API Kiến thức Bên ngoài này được liên kết với',
      end: 'kiến thức bên ngoài. Xóa API này sẽ làm mất hiệu lực tất cả chúng. Bạn có chắc chắn muốn xóa API này không?',
    },
    noConnectionContent: 'Bạn có chắc chắn xóa API này không?',
  },
  selectExternalKnowledgeAPI: {
    placeholder: 'Chọn một API kiến thức bên ngoài',
  },
  connectDatasetIntro: {
    content: {
      end: '. Sau đó tìm ID kiến thức tương ứng và điền vào biểu mẫu bên trái. Nếu tất cả thông tin là chính xác, nó sẽ tự động chuyển đến bài kiểm tra truy xuất trong cơ sở kiến thức sau khi nhấp vào nút kết nối.',
      front: 'Để kết nối với cơ sở kiến thức bên ngoài, trước tiên bạn cần tạo API bên ngoài. Vui lòng đọc kỹ và tham khảo',
      link: 'Tìm hiểu cách tạo API bên ngoài',
    },
    learnMore: 'Tìm hiểu thêm',
    title: 'Cách kết nối với cơ sở kiến thức bên ngoài',
  },
  connectHelper: {
    helper3: '. Chúng tôi thực sự khuyên bạn nên',
    helper4: 'Đọc tài liệu trợ giúp',
    helper1: 'Kết nối với cơ sở kiến thức bên ngoài thông qua API và ID cơ sở kiến thức. Hiện tại,',
    helper2: 'Chỉ hỗ trợ chức năng truy xuất',
    helper5: 'cẩn thận trước khi sử dụng tính năng này.',
  },
  externalKnowledgeForm: {
    cancel: 'Hủy',
    connect: 'Kết nối',
  },
  externalAPIForm: {
    encrypted: {
      end: 'Công nghệ.',
      front: 'Mã thông báo API của bạn sẽ được mã hóa và lưu trữ bằng cách sử dụng',
    },
    apiKey: 'Khóa API',
    endpoint: 'Điểm cuối API',
    edit: 'Biên tập',
    cancel: 'Hủy',
    name: 'Tên',
    save: 'Cứu',
  },
  learnHowToWriteGoodKnowledgeDescription: 'Tìm hiểu cách viết mô tả kiến thức tốt',
  noExternalKnowledge: 'Chưa có API Kiến thức Bên ngoài, hãy nhấp vào đây để tạo',
  connectDataset: 'Kết nối với cơ sở kiến thức bên ngoài',
  externalTag: 'Ngoài',
  externalAPIPanelTitle: 'API kiến thức bên ngoài',
  editExternalAPITooltipTitle: 'KIẾN THỨC LIÊN KẾT',
  externalKnowledgeNamePlaceholder: 'Vui lòng nhập tên của cơ sở kiến thức',
  createExternalAPI: 'Thêm API kiến thức bên ngoài',
  externalKnowledgeDescription: 'Mô tả kiến thức',
  externalAPIPanelDocumentation: 'Tìm hiểu cách tạo API Kiến thức Bên ngoài',
  allExternalTip: 'Khi chỉ sử dụng kiến thức bên ngoài, người dùng có thể chọn có bật mô hình Xếp hạng lại hay không. Nếu không được bật, các đoạn được truy xuất sẽ được sắp xếp dựa trên điểm số. Khi các chiến lược truy xuất của các cơ sở kiến thức khác nhau không nhất quán, nó sẽ không chính xác.',
  editExternalAPIFormTitle: 'Chỉnh sửa API Kiến thức Bên ngoài',
  externalKnowledgeId: 'ID kiến thức bên ngoài',
  mixtureInternalAndExternalTip: 'Mô hình Rerank là cần thiết cho sự kết hợp giữa kiến thức bên trong và bên ngoài.',
  externalAPI: 'API bên ngoài',
  externalKnowledgeDescriptionPlaceholder: 'Mô tả nội dung trong Cơ sở Kiến thức này (tùy chọn)',
  externalKnowledgeName: 'Tên kiến thức bên ngoài',
  externalKnowledgeIdPlaceholder: 'Vui lòng nhập ID kiến thức',
  createNewExternalAPI: 'Tạo API Kiến thức Bên ngoài mới',
  externalAPIPanelDescription: 'API kiến thức bên ngoài được sử dụng để kết nối với cơ sở kiến thức bên ngoài Dify và truy xuất kiến thức từ cơ sở kiến thức đó.',
  chunkingMode: {
    general: 'Tổng quát',
    parentChild: 'Cha mẹ-con cái',
  },
  parentMode: {
    paragraph: 'Đoạn',
    fullDoc: 'Tài liệu đầy đủ',
  },
  batchAction: {
    disable: 'Vô hiệu hóa',
    enable: 'Kích hoạt',
    delete: 'Xóa',
    selected: 'Chọn',
    archive: 'Lưu trữ',
    cancel: 'Hủy',
  },
  localDocs: 'Tài liệu địa phương',
  enable: 'Kích hoạt',
  preprocessDocument: '{{số}} Tiền xử lý tài liệu',
  documentsDisabled: '{{num}} tài liệu bị vô hiệu hóa - không hoạt động trong hơn 30 ngày',
  allKnowledge: 'Tất cả kiến thức',
  allKnowledgeDescription: 'Chọn để hiển thị tất cả kiến thức trong không gian làm việc này. Chỉ Chủ sở hữu không gian làm việc mới có thể quản lý tất cả kiến thức.',
  metadata: {
    createMetadata: {
      name: 'Tên',
      namePlaceholder: 'Thêm tên siêu dữ liệu',
      type: 'Loại',
      title: 'Siêu dữ liệu mới',
      back: 'Quay lại',
    },
    checkName: {
      invalid: 'Tên siêu dữ liệu chỉ có thể chứa chữ cái thường, số và dấu gạch dưới, và phải bắt đầu bằng một chữ cái thường.',
      empty: 'Tên siêu dữ liệu không được để trống',
    },
    batchEditMetadata: {
      applyToAllSelectDocumentTip: 'Tự động tạo tất cả các siêu dữ liệu đã chỉnh sửa và mới cho tất cả các tài liệu được chọn, nếu không, việc chỉnh sửa siêu dữ liệu sẽ chỉ áp dụng cho các tài liệu có nó.',
      multipleValue: 'Nhiều giá trị',
      editDocumentsNum: 'Chỉnh sửa {{num}} tài liệu',
      applyToAllSelectDocument: 'Áp dụng cho tất cả các tài liệu đã chọn',
      editMetadata: 'Chỉnh sửa siêu dữ liệu',
    },
    selectMetadata: {
      manageAction: 'Quản lý',
      search: 'Tìm kiếm siêu dữ liệu',
      newAction: 'Siêu dữ liệu mới',
    },
    datasetMetadata: {
      disabled: 'Tắt',
      rename: 'Đổi tên',
      namePlaceholder: 'Tên siêu dữ liệu',
      builtIn: 'Tích hợp sẵn',
      deleteTitle: 'Xác nhận để xóa',
      name: 'Tên',
      values: '{{num}} Giá trị',
      description: 'Bạn có thể quản lý tất cả metadata trong kiến thức này ở đây. Những thay đổi sẽ được đồng bộ hóa đến mọi tài liệu.',
      deleteContent: 'Bạn có chắc chắn muốn xóa siêu dữ liệu "{{name}}" không?',
      builtInDescription: 'Siêu dữ liệu được tích hợp sẵn sẽ tự động được trích xuất và tạo ra. Nó phải được bật trước khi sử dụng và không thể chỉnh sửa.',
      addMetaData: 'Thêm siêu dữ liệu',
    },
    documentMetadata: {
      documentInformation: 'Thông tin tài liệu',
      technicalParameters: 'Các Thông Số Kỹ Thuật',
      metadataToolTip: 'Dữ liệu siêu thông tin đóng vai trò là một bộ lọc quan trọng giúp nâng cao độ chính xác và tính liên quan của việc truy xuất thông tin. Bạn có thể chỉnh sửa và thêm dữ liệu siêu thông tin cho tài liệu này ở đây.',
      startLabeling: 'Bắt đầu gán nhãn',
    },
    addMetadata: 'Thêm siêu dữ liệu',
    chooseTime: 'Chọn một thời gian...',
    metadata: 'Siêu dữ liệu',
  },
  embeddingModelNotAvailable: 'Mô hình nhúng không khả dụng.',
}

export default translation
