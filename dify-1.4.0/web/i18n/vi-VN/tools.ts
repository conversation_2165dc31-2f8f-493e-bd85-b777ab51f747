const translation = {
  title: 'Công cụ',
  createCustomTool: 'Tạo công cụ tùy chỉnh',
  type: {
    all: 'Tất cả',
    builtIn: 'Tích hợp sẵn',
    custom: 'Tùy chỉnh',
    workflow: 'Quy trình làm việc',
  },
  contribute: {
    line1: 'Tôi quan tâm đến việc ',
    line2: 'đóng góp công cụ cho Dify.',
    viewGuide: 'Xem hướng dẫn',
  },
  author: 'Tác giả',
  auth: {
    unauthorized: 'Chưa xác thực',
    authorized: 'Đã xác thực',
    setup: 'Thiết lập xác thực để sử dụng',
    setupModalTitle: 'Thiết lập xác thực',
    setupModalTitleDescription: 'Sau khi cấu hình thông tin đăng nhập, tất cả thành viên trong không gian làm việc có thể sử dụng công cụ này khi triển khai ứng dụng.',
  },
  includeToolNum: '<PERSON>o gồm {{num}} công cụ',
  addTool: 'Thêm công cụ',
  createTool: {
    title: 'Tạo công cụ tùy chỉnh',
    editAction: 'Cấu hình',
    editTitle: 'Chỉnh sửa công cụ tùy chỉnh',
    name: 'Tên',
    toolNamePlaceHolder: 'Nhập tên công cụ',
    schema: 'Schema',
    schemaPlaceHolder: 'Nhập schema OpenAPI của bạn vào đây',
    viewSchemaSpec: 'Xem chi tiết OpenAPI-Swagger',
    importFromUrl: 'Nhập từ URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Vui lòng nhập URL hợp lệ',
    examples: 'Ví dụ',
    exampleOptions: {
      json: 'Thời tiết (JSON)',
      yaml: 'Cửa hàng thú cưng (YAML)',
      blankTemplate: 'Mẫu trống',
    },
    availableTools: {
      title: 'Công cụ hiện có',
      name: 'Tên',
      description: 'Mô tả',
      method: 'Phương thức',
      path: 'Đường dẫn',
      action: 'Hành động',
      test: 'Kiểm tra',
    },
    authMethod: {
      title: 'Phương thức xác thực',
      type: 'Loại xác thực',
      keyTooltip: 'Khóa tiêu đề HTTP, bạn có thể để trống nếu không biết hoặc đặt một giá trị tùy chỉnh',
      types: {
        none: 'Không',
        api_key: 'Khóa API',
        apiKeyPlaceholder: 'Tên tiêu đề HTTP cho Khóa API',
        apiValuePlaceholder: 'Nhập Khóa API',
      },
      key: 'Khóa',
      value: 'Giá trị',
    },
    authHeaderPrefix: {
      title: 'Loại xác thực',
      types: {
        basic: 'Cơ bản',
        bearer: 'Bearer',
        custom: 'Tùy chỉnh',
      },
    },
    privacyPolicy: 'Chính sách bảo mật',
    privacyPolicyPlaceholder: 'Vui lòng nhập chính sách bảo mật',
    customDisclaimer: 'Tuyên bố từ chối trách nhiệm tùy chỉnh',
    customDisclaimerPlaceholder: 'Vui lòng nhập tuyên bố từ chối trách nhiệm tùy chỉnh',
    deleteToolConfirmTitle: 'Xóa công cụ này?',
    deleteToolConfirmContent: 'Xóa công cụ là không thể hoàn tác. Người dùng sẽ không thể truy cập lại công cụ của bạn.',
    toolInput: {
      label: 'Tags',
      methodParameter: 'Thông số',
      name: 'Tên',
      descriptionPlaceholder: 'Mô tả ý nghĩa của tham số',
      methodSetting: 'Khung cảnh',
      title: 'Công cụ nhập liệu',
      methodSettingTip: 'Người dùng điền vào cấu hình công cụ',
      required: 'Bắt buộc',
      method: 'Phương pháp',
      methodParameterTip: 'LLM lấp đầy trong quá trình suy luận',
      description: 'Sự miêu tả',
      labelPlaceholder: 'Chọn thẻ (tùy chọn)',
    },
    nameForToolCallTip: 'Chỉ hỗ trợ số, chữ cái và dấu gạch dưới.',
    nameForToolCall: 'Công cụ gọi tên',
    nameForToolCallPlaceHolder: 'Được sử dụng để nhận dạng máy, chẳng hạn như getCurrentWeather, list_pets',
    descriptionPlaceholder: 'Mô tả ngắn gọn về mục đích của công cụ, ví dụ: lấy nhiệt độ cho một vị trí cụ thể.',
    description: 'Sự miêu tả',
    confirmTitle: 'Xác nhận để lưu ?',
    confirmTip: 'Các ứng dụng sử dụng công cụ này sẽ bị ảnh hưởng',
  },
  test: {
    title: 'Kiểm tra',
    parametersValue: 'Tham số & Giá trị',
    parameters: 'Tham số',
    value: 'Giá trị',
    testResult: 'Kết quả kiểm tra',
    testResultPlaceholder: 'Kết quả kiểm tra sẽ hiển thị ở đây',
  },
  thought: {
    using: 'Đang sử dụng',
    used: 'Đã sử dụng',
    requestTitle: 'Yêu cầu đến',
    responseTitle: 'Phản hồi từ',
  },
  setBuiltInTools: {
    info: 'Thông tin',
    setting: 'Cài đặt',
    toolDescription: 'Mô tả công cụ',
    parameters: 'Tham số',
    string: 'chuỗi',
    number: 'số',
    required: 'Bắt buộc',
    infoAndSetting: 'Thông tin & Cài đặt',
    file: 'tệp',
  },
  noCustomTool: {
    title: 'Chưa có công cụ tùy chỉnh!',
    content: 'Thêm và quản lý các công cụ tùy chỉnh của bạn ở đây để xây dựng ứng dụng AI.',
    createTool: 'Tạo công cụ',
  },
  noSearchRes: {
    title: 'Xin lỗi, không có kết quả!',
    content: 'Chúng tôi không tìm thấy công cụ nào phù hợp với tìm kiếm của bạn.',
    reset: 'Đặt lại tìm kiếm',
  },
  builtInPromptTitle: 'Lời nhắc',
  toolRemoved: 'Công cụ đã bị xóa',
  notAuthorized: 'Công cụ chưa được xác thực',
  howToGet: 'Cách nhận',
  addToolModal: {
    category: 'loại',
    manageInTools: 'Quản lý trong Công cụ',
    type: 'kiểu',
    add: 'thêm',
    added: 'Thêm',
    emptyTip: 'Đi tới "Quy trình làm việc -> Xuất bản dưới dạng công cụ"',
    emptyTitle: 'Không có sẵn công cụ quy trình làm việc',
    emptyTitleCustom: 'Không có công cụ tùy chỉnh nào có sẵn',
    emptyTipCustom: 'Tạo công cụ tùy chỉnh',
  },
  toolNameUsageTip: 'Tên cuộc gọi công cụ để lý luận và nhắc nhở tổng đài viên',
  customToolTip: 'Tìm hiểu thêm về các công cụ tùy chỉnh Dify',
  openInStudio: 'Mở trong Studio',
  noTools: 'Không tìm thấy công cụ',
  copyToolName: 'Sao chép tên',
}

export default translation
