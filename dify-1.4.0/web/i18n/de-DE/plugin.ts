const translation = {
  category: {
    extensions: 'Erweiterungen',
    bundles: 'B<PERSON>nde<PERSON>',
    agents: 'Agenten-Strategien',
    models: 'Modelle',
    all: 'Alle',
    tools: 'Werkzeuge',
  },
  categorySingle: {
    extension: 'Erweiterung',
    agent: 'Agenten-Strategie',
    bundle: 'Bünde<PERSON>',
    model: 'Modell',
    tool: 'Werkzeug',
  },
  list: {
    source: {
      marketplace: 'Installation aus dem Marketplace',
      github: 'Installation von GitHub',
      local: 'Installation aus lokaler Paketdatei',
    },
    notFound: 'Keine Plugins gefunden',
    noInstalled: 'Keine Plugins installiert',
  },
  source: {
    github: 'GitHub (Englisch)',
    marketplace: 'Marktplatz',
    local: 'Lokale Paketdatei',
  },
  detailPanel: {
    categoryTip: {
      local: 'Lokales Plugin',
      github: 'Installiert von Github',
      marketplace: 'Installiert aus dem Marketplace',
      debugging: 'Debuggen-Plugin',
    },
    operation: {
      remove: 'Entfernen',
      detail: 'Einzelheiten',
      install: 'Installieren',
      info: 'Plugin-Informationen',
      checkUpdate: 'Update prüfen',
      update: 'Aktualisieren',
      viewDetail: 'Im Detail sehen',
    },
    toolSelector: {
      paramsTip1: 'Steuert LLM-Inferenzparameter.',
      settings: 'BENUTZEREINSTELLUNGEN',
      uninstalledLink: 'In Plugins verwalten',
      descriptionLabel: 'Beschreibung des Werkzeugs',
      empty: 'Klicken Sie auf die Schaltfläche "+", um Werkzeuge hinzuzufügen. Sie können mehrere Werkzeuge hinzufügen.',
      title: 'Werkzeug "Hinzufügen"',
      paramsTip2: 'Wenn "Automatisch" ausgeschaltet ist, wird der Standardwert verwendet.',
      unsupportedContent: 'Die installierte Plug-in-Version bietet diese Aktion nicht.',
      unsupportedTitle: 'Nicht unterstützte Aktion',
      descriptionPlaceholder: 'Kurze Beschreibung des Zwecks des Werkzeugs, z. B. um die Temperatur für einen bestimmten Ort zu ermitteln.',
      auto: 'Automatisch',
      params: 'KONFIGURATION DER ARGUMENTATION',
      unsupportedContent2: 'Klicken Sie hier, um die Version zu wechseln.',
      placeholder: 'Wählen Sie ein Werkzeug aus...',
      uninstalledTitle: 'Tool nicht installiert',
      toolLabel: 'Werkzeug',
      uninstalledContent: 'Dieses Plugin wird aus dem lokalen/GitHub-Repository installiert. Bitte nach der Installation verwenden.',
    },
    strategyNum: '{{num}} {{Strategie}} IINKLUSIVE',
    configureApp: 'App konfigurieren',
    endpointDeleteContent: 'Möchten Sie {{name}} entfernen?',
    endpointsEmpty: 'Klicken Sie auf die Schaltfläche "+", um einen Endpunkt hinzuzufügen',
    disabled: 'Arbeitsunfähig',
    endpointsDocLink: 'Dokument anzeigen',
    endpointDisableTip: 'Endpunkt deaktivieren',
    endpoints: 'Endpunkte',
    actionNum: '{{num}} {{Aktion}} IINKLUSIVE',
    endpointModalTitle: 'Endpunkt einrichten',
    endpointModalDesc: 'Nach der Konfiguration können die Funktionen, die das Plugin über API-Endpunkte bereitstellt, verwendet werden.',
    configureTool: 'Werkzeug konfigurieren',
    endpointsTip: 'Dieses Plugin bietet bestimmte Funktionen über Endpunkte, und Sie können mehrere Endpunktsätze für den aktuellen Arbeitsbereich konfigurieren.',
    modelNum: '{{num}} ENTHALTENE MODELLE',
    configureModel: 'Modell konfigurieren',
    endpointDisableContent: 'Möchten Sie {{name}} deaktivieren?',
    endpointDeleteTip: 'Endpunkt entfernen',
    serviceOk: 'Service in Ordnung',
    switchVersion: 'Version wechseln',
  },
  debugInfo: {
    title: 'Debuggen',
    viewDocs: 'Dokumente anzeigen',
  },
  privilege: {
    everyone: 'Jeder',
    title: 'Plugin-Einstellungen',
    noone: 'Niemand',
    admins: 'Administratoren',
    whoCanDebug: 'Wer kann Plugins debuggen?',
    whoCanInstall: 'Wer kann Plugins installieren und verwalten?',
  },
  pluginInfoModal: {
    repository: 'Aufbewahrungsort',
    title: 'Plugin-Info',
    packageName: 'Paket',
    release: 'Loslassen',
  },
  action: {
    checkForUpdates: 'Nach Updates suchen',
    pluginInfo: 'Plugin-Info',
    usedInApps: 'Dieses Plugin wird in {{num}} Apps verwendet.',
    delete: 'Plugin entfernen',
    deleteContentRight: 'Plugin?',
    deleteContentLeft: 'Möchten Sie',
  },
  installModal: {
    labels: {
      repository: 'Aufbewahrungsort',
      package: 'Paket',
      version: 'Version',
    },
    installFailed: 'Installation fehlgeschlagen',
    installPlugin: 'Plugin installieren',
    uploadFailed: 'Upload fehlgeschlagen',
    install: 'Installieren',
    installComplete: 'Installation abgeschlossen',
    installing: 'Installation...',
    installedSuccessfullyDesc: 'Das Plugin wurde erfolgreich installiert.',
    installedSuccessfully: 'Installation erfolgreich',
    installFailedDesc: 'Die Installation des Plugins ist fehlgeschlagen.',
    pluginLoadError: 'Fehler beim Laden des Plugins',
    close: 'Schließen',
    pluginLoadErrorDesc: 'Dieses Plugin wird nicht installiert',
    cancel: 'Abbrechen',
    back: 'Zurück',
    uploadingPackage: 'Das Hochladen von {{packageName}}...',
    readyToInstallPackage: 'Über die Installation des folgenden Plugins',
    readyToInstallPackages: 'Über die Installation der folgenden {{num}} Plugins',
    fromTrustSource: 'Bitte stellen Sie sicher, dass Sie nur Plugins aus einer <trustSource>vertrauenswürdigen Quelle</trustSource> installieren.',
    readyToInstall: 'Über die Installation des folgenden Plugins',
    dropPluginToInstall: 'Legen Sie das Plugin-Paket hier ab, um es zu installieren',
    next: 'Nächster',
  },
  installFromGitHub: {
    selectPackagePlaceholder: 'Bitte wählen Sie ein Paket aus',
    gitHubRepo: 'GitHub-Repository',
    uploadFailed: 'Upload fehlgeschlagen',
    selectPackage: 'Paket auswählen',
    installFailed: 'Installation fehlgeschlagen',
    installNote: 'Bitte stellen Sie sicher, dass Sie nur Plugins aus einer vertrauenswürdigen Quelle installieren.',
    selectVersionPlaceholder: 'Bitte wählen Sie eine Version aus',
    updatePlugin: 'Update-Plugin von GitHub',
    installPlugin: 'Plugin von GitHub installieren',
    installedSuccessfully: 'Installation erfolgreich',
    selectVersion: 'Ausführung wählen',
  },
  upgrade: {
    usedInApps: 'Wird in {{num}} Apps verwendet',
    description: 'Über die Installation des folgenden Plugins',
    upgrading: 'Installation...',
    successfulTitle: 'Installation erfolgreich',
    upgrade: 'Installieren',
    title: 'Plugin installieren',
    close: 'Schließen',
  },
  error: {
    inValidGitHubUrl: 'Ungültige GitHub-URL. Bitte geben Sie eine gültige URL im Format ein: https://github.com/owner/repo',
    noReleasesFound: 'Keine Veröffentlichungen gefunden. Bitte überprüfen Sie das GitHub-Repository oder die Eingabe-URL.',
    fetchReleasesError: 'Freigaben können nicht abgerufen werden. Bitte versuchen Sie es später erneut.',
  },
  marketplace: {
    sortOption: {
      newlyReleased: 'Neu veröffentlicht',
      mostPopular: 'Beliebteste',
      firstReleased: 'Zuerst veröffentlicht',
      recentlyUpdated: 'Kürzlich aktualisiert',
    },
    viewMore: 'Mehr anzeigen',
    sortBy: 'Schwarze Stadt',
    discover: 'Entdecken',
    noPluginFound: 'Kein Plugin gefunden',
    difyMarketplace: 'Dify Marktplatz',
    moreFrom: 'Mehr aus dem Marketplace',
    pluginsResult: '{{num}} Ergebnisse',
    empower: 'Unterstützen Sie Ihre KI-Entwicklung',
    and: 'und',
    partnerTip: 'Von einem Dify-Partner verifiziert',
    verifiedTip: 'Von Dify überprüft',
  },
  task: {
    clearAll: 'Alle löschen',
    installingWithError: 'Installation von {{installingLength}} Plugins, {{successLength}} erfolgreich, {{errorLength}} fehlgeschlagen',
    installingWithSuccess: 'Installation von {{installingLength}} Plugins, {{successLength}} erfolgreich.',
    installedError: '{{errorLength}} Plugins konnten nicht installiert werden',
    installing: 'Installation von {{installingLength}} Plugins, 0 erledigt.',
    installError: '{{errorLength}} Plugins konnten nicht installiert werden, klicken Sie hier, um sie anzusehen',
  },
  allCategories: 'Alle Kategorien',
  install: '{{num}} Installationen',
  installAction: 'Installieren',
  submitPlugin: 'Plugin einreichen',
  from: 'Von',
  fromMarketplace: 'Aus dem Marketplace',
  search: 'Suchen',
  searchCategories: 'Kategorien durchsuchen',
  searchPlugins: 'Plugins suchen',
  endpointsEnabled: '{{num}} Gruppen von Endpunkten aktiviert',
  searchInMarketplace: 'Suche im Marketplace',
  searchTools: 'Suchwerkzeuge...',
  findMoreInMarketplace: 'Weitere Informationen finden Sie im Marketplace',
  installPlugin: 'Plugin installieren',
  installFrom: 'INSTALLIEREN VON',
  metadata: {
    title: 'Plugins',
  },
  difyVersionNotCompatible: 'Die aktuelle Dify-Version ist mit diesem Plugin nicht kompatibel, bitte aktualisieren Sie auf die erforderliche Mindestversion: {{minimalDifyVersion}}',
}

export default translation
