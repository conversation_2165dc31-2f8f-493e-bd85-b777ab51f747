const translation = {
  input: 'इनपुट',
  result: 'नतीजा',
  detail: 'विवरण',
  tracing: 'ट्रेसिंग',
  resultPanel: {
    status: 'स्थिति',
    time: 'समय की अवधि',
    tokens: 'कुल टोकन्स',
  },
  meta: {
    title: 'मेटाडाटा',
    status: 'स्थिति',
    version: 'वर्ज़न',
    executor: 'एक्ज़ीक्यूटर',
    startTime: 'शुरुआत की समय',
    time: 'समय की अवधि',
    tokens: 'कुल टोकन्स',
    steps: 'चालाने के चरण',
  },
  resultEmpty: {
    title: 'इस रन में सिर्फ JSON फार्मेट का नतीजा है,',
    tipLeft: 'लेखक ',
    link: 'विवरण पैनल',
    tipRight: ' देखें।',
  },
  actionLogs: 'क्रिया लॉग',
  circularInvocationTip: 'वर्तमान कार्यप्रवाह में उपकरणों/नोड्स का वृत्ताकार आह्वान है।',
}

export default translation
