const translation = {
  title: 'Ferramentas',
  createCustomTool: 'Criar Ferramenta Personalizada',
  type: {
    all: 'Todas',
    builtIn: 'Integradas',
    custom: 'Personalizadas',
    workflow: 'Fluxo de trabalho',
  },
  contribute: {
    line1: 'Estou interessado em ',
    line2: 'contribuir com ferramentas para o Dify.',
    viewGuide: 'Ver o guia',
  },
  author: 'Por',
  auth: {
    unauthorized: 'Para Autorizar',
    authorized: 'Autorizado',
    setup: 'Configurar autorização para usar',
    setupModalTitle: 'Configurar Autorização',
    setupModalTitleDescription: 'Após configurar as credenciais, todos os membros do espaço de trabalho podem usar essa ferramenta ao orquestrar aplicativos.',
  },
  includeToolNum: '{{num}} ferramentas incluídas',
  addTool: 'Adicionar Ferramenta',
  createTool: {
    title: 'Criar Ferramenta Personalizada',
    editAction: 'Configurar',
    editTitle: 'Editar Ferramenta Personalizada',
    name: 'Nome',
    toolNamePlaceHolder: 'Digite o nome da ferramenta',
    schema: 'Esquema',
    schemaPlaceHolder: 'Digite seu esquema OpenAPI aqui',
    viewSchemaSpec: 'Ver a Especificação OpenAPI-Swagger',
    importFromUrl: 'Importar de URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Digite uma URL válida',
    examples: 'Exemplos',
    exampleOptions: {
      json: 'Clima(JSON)',
      yaml: 'Pet Store(YAML)',
      blankTemplate: 'Modelo em Branco',
    },
    availableTools: {
      title: 'Ferramentas Disponíveis',
      name: 'Nome',
      description: 'Descrição',
      method: 'Método',
      path: 'Caminho',
      action: 'Ações',
      test: 'Testar',
    },
    authMethod: {
      title: 'Método de Autorização',
      type: 'Tipo de Autorização',
      keyTooltip: 'Chave do Cabeçalho HTTP, você pode deixar como "Authorization" se não tiver ideia do que é ou definir um valor personalizado',
      types: {
        none: 'Nenhum',
        api_key: 'Chave de API',
        apiKeyPlaceholder: 'Nome do cabeçalho HTTP para a Chave de API',
        apiValuePlaceholder: 'Digite a Chave de API',
      },
      key: 'Chave',
      value: 'Valor',
    },
    authHeaderPrefix: {
      title: 'Tipo de Autenticação',
      types: {
        basic: 'Básica',
        bearer: 'Bearer',
        custom: 'Personalizada',
      },
    },
    privacyPolicy: 'Política de Privacidade',
    privacyPolicyPlaceholder: 'Digite a política de privacidade',
    customDisclaimer: 'Aviso Personalizado',
    customDisclaimerPlaceholder: 'Digite o aviso personalizado',
    deleteToolConfirmTitle: 'Excluir esta ferramenta?',
    deleteToolConfirmContent: 'Excluir a ferramenta é irreversível. Os usuários não poderão mais acessar sua ferramenta.',
    toolInput: {
      label: 'Tags',
      methodSetting: 'Ambiente',
      methodParameterTip: 'Preenchimentos de LLM durante a inferência',
      methodSettingTip: 'O usuário preenche a configuração da ferramenta',
      methodParameter: 'Parâmetro',
      name: 'Nome',
      description: 'Descrição',
      method: 'Método',
      required: 'Necessário',
      title: 'Entrada de ferramenta',
      labelPlaceholder: 'Escolha tags(opcional)',
      descriptionPlaceholder: 'Descrição do significado do parâmetro',
    },
    description: 'Descrição',
    nameForToolCall: 'Nome da chamada da ferramenta',
    confirmTip: 'Os aplicativos que usam essa ferramenta serão afetados',
    confirmTitle: 'Confirme para salvar ?',
    nameForToolCallTip: 'Suporta apenas números, letras e sublinhados.',
    descriptionPlaceholder: 'Breve descrição da finalidade da ferramenta, por exemplo, obter a temperatura para um local específico.',
    nameForToolCallPlaceHolder: 'Usado para reconhecimento de máquina, como getCurrentWeather, list_pets',
  },
  test: {
    title: 'Testar',
    parametersValue: 'Parâmetros e Valor',
    parameters: 'Parâmetros',
    value: 'Valor',
    testResult: 'Resultados do Teste',
    testResultPlaceholder: 'O resultado do teste será exibido aqui',
  },
  thought: {
    using: 'Usando',
    used: 'Usado',
    requestTitle: 'Requisição para',
    responseTitle: 'Resposta de',
  },
  setBuiltInTools: {
    info: 'Informações',
    setting: 'Configuração',
    toolDescription: 'Descrição da Ferramenta',
    parameters: 'parâmetros',
    string: 'string',
    number: 'número',
    required: 'Obrigatório',
    infoAndSetting: 'Informações e Configurações',
    file: 'arquivo',
  },
  noCustomTool: {
    title: 'Nenhuma ferramenta personalizada!',
    content: 'Adicione e gerencie suas ferramentas personalizadas aqui para construir aplicativos de IA.',
    createTool: 'Criar Ferramenta',
  },
  noSearchRes: {
    title: 'Desculpe, sem resultados!',
    content: 'Não encontramos nenhuma ferramenta que corresponda à sua pesquisa.',
    reset: 'Redefinir Pesquisa',
  },
  builtInPromptTitle: 'Prompt',
  toolRemoved: 'Ferramenta removida',
  notAuthorized: 'Ferramenta não autorizada',
  howToGet: 'Como obter',
  addToolModal: {
    category: 'categoria',
    type: 'tipo',
    emptyTip: 'Vá para "Fluxo de trabalho - > Publicar como ferramenta"',
    add: 'adicionar',
    emptyTitle: 'Nenhuma ferramenta de fluxo de trabalho disponível',
    added: 'Adicionado',
    manageInTools: 'Gerenciar em Ferramentas',
    emptyTitleCustom: 'Nenhuma ferramenta personalizada disponível',
    emptyTipCustom: 'Criar uma ferramenta personalizada',
  },
  openInStudio: 'Abrir no Studio',
  customToolTip: 'Saiba mais sobre as ferramentas personalizadas da Dify',
  toolNameUsageTip: 'Nome da chamada da ferramenta para raciocínio e solicitação do agente',
  copyToolName: 'Nome da cópia',
  noTools: 'Nenhuma ferramenta encontrada',
}

export default translation
