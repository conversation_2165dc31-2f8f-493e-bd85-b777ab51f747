const translation = {
  category: {
    extensions: 'Extensões',
    all: 'Todo',
    bundles: 'Pacotes',
    models: 'Modelos',
    agents: 'Estratégias do agente',
    tools: 'Ferramentas',
  },
  categorySingle: {
    model: 'Modelo',
    bundle: 'Pacote',
    agent: 'Estratégia do agente',
    extension: 'Extensão',
    tool: 'Ferramenta',
  },
  list: {
    source: {
      marketplace: 'Instalar do Marketplace',
      github: 'Instalar do GitHub',
      local: 'Instalar a partir do arquivo de pacote local',
    },
    noInstalled: 'Nenhum plug-in instalado',
    notFound: 'Nenhum plugin encontrado',
  },
  source: {
    local: 'Arquivo de pacote local',
    github: 'GitHub',
    marketplace: 'Mercado',
  },
  detailPanel: {
    categoryTip: {
      debugging: 'Plugin de depuração',
      marketplace: 'Instalado do Marketplace',
      local: 'Plug-in local',
      github: 'Instalado a partir do Github',
    },
    operation: {
      checkUpdate: 'Verifique a atualização',
      install: 'Instalar',
      update: 'Atualização',
      info: 'Informações do plugin',
      detail: 'Detalhes',
      remove: 'Retirar',
      viewDetail: 'Ver detalhes',
    },
    toolSelector: {
      uninstalledLink: 'Gerenciar em plug-ins',
      unsupportedContent2: 'Clique para mudar de versão.',
      auto: 'Automático',
      title: 'Adicionar ferramenta',
      params: 'CONFIGURAÇÃO DE RACIOCÍNIO',
      toolLabel: 'Ferramenta',
      paramsTip1: 'Controla os parâmetros de inferência do LLM.',
      descriptionLabel: 'Descrição da ferramenta',
      uninstalledContent: 'Este plug-in é instalado a partir do repositório local/GitHub. Por favor, use após a instalação.',
      paramsTip2: 'Quando \'Automático\' está desativado, o valor padrão é usado.',
      placeholder: 'Selecione uma ferramenta...',
      empty: 'Clique no botão \'+\' para adicionar ferramentas. Você pode adicionar várias ferramentas.',
      settings: 'CONFIGURAÇÕES DO USUÁRIO',
      unsupportedContent: 'A versão do plug-in instalada não fornece essa ação.',
      descriptionPlaceholder: 'Breve descrição da finalidade da ferramenta, por exemplo, obter a temperatura para um local específico.',
      uninstalledTitle: 'Ferramenta não instalada',
      unsupportedTitle: 'Ação sem suporte',
    },
    serviceOk: 'Serviço OK',
    endpointsTip: 'Este plug-in fornece funcionalidades específicas por meio de endpoints e você pode configurar vários conjuntos de endpoints para o workspace atual.',
    strategyNum: '{{num}} {{estratégia}} INCLUSO',
    endpointDisableContent: 'Gostaria de desativar {{name}}?',
    endpointDeleteContent: 'Gostaria de remover {{name}}?',
    endpointsEmpty: 'Clique no botão \'+\' para adicionar um endpoint',
    configureModel: 'Configurar modelo',
    endpointModalDesc: 'Uma vez configurados, os recursos fornecidos pelo plug-in por meio de endpoints de API podem ser usados.',
    endpointDeleteTip: 'Remover endpoint',
    endpointDisableTip: 'Desativar ponto de extremidade',
    modelNum: '{{num}} MODELOS INCLUÍDOS',
    actionNum: '{{num}} {{ação}} INCLUSO',
    switchVersion: 'Versão do Switch',
    endpoints: 'Extremidade',
    disabled: 'Desactivado',
    configureApp: 'Configurar aplicativo',
    configureTool: 'Ferramenta de configuração',
    endpointsDocLink: 'Veja o documento',
    endpointModalTitle: 'Ponto de extremidade de configuração',
  },
  debugInfo: {
    title: 'Depuração',
    viewDocs: 'Ver documentos',
  },
  privilege: {
    whoCanInstall: 'Quem pode instalar e gerenciar plugins?',
    admins: 'Administradores',
    noone: 'Ninguém',
    whoCanDebug: 'Quem pode depurar plugins?',
    title: 'Preferências de plug-ins',
    everyone: 'Todos',
  },
  pluginInfoModal: {
    repository: 'Repositório',
    title: 'Informações do plugin',
    packageName: 'Pacote',
    release: 'Soltar',
  },
  action: {
    deleteContentLeft: 'Gostaria de remover',
    deleteContentRight: 'plugin?',
    delete: 'Remover plugin',
    pluginInfo: 'Informações do plugin',
    checkForUpdates: 'Verifique se há atualizações',
    usedInApps: 'Este plugin está sendo usado em aplicativos {{num}}.',
  },
  installModal: {
    labels: {
      version: 'Versão',
      repository: 'Repositório',
      package: 'Pacote',
    },
    installPlugin: 'Instale o plugin',
    close: 'Fechar',
    installedSuccessfullyDesc: 'O plugin foi instalado com sucesso.',
    next: 'Próximo',
    installFailedDesc: 'O plug-in foi instalado falhou.',
    installedSuccessfully: 'Instalação bem-sucedida',
    install: 'Instalar',
    installFailed: 'Falha na instalação',
    readyToInstallPackages: 'Prestes a instalar os seguintes plugins {{num}}',
    back: 'Voltar',
    installComplete: 'Instalação concluída',
    readyToInstallPackage: 'Prestes a instalar o seguinte plugin',
    cancel: 'Cancelar',
    fromTrustSource: 'Certifique-se de instalar apenas plug-ins de uma <trustSource>fonte confiável</trustSource>.',
    pluginLoadError: 'Erro de carregamento do plug-in',
    readyToInstall: 'Prestes a instalar o seguinte plugin',
    pluginLoadErrorDesc: 'Este plugin não será instalado',
    uploadFailed: 'Falha no upload',
    installing: 'Instalar...',
    uploadingPackage: 'Carregando {{packageName}} ...',
    dropPluginToInstall: 'Solte o pacote de plug-in aqui para instalar',
  },
  installFromGitHub: {
    selectVersionPlaceholder: 'Selecione uma versão',
    updatePlugin: 'Atualizar plugin do GitHub',
    installPlugin: 'Instale o plugin do GitHub',
    gitHubRepo: 'Repositório GitHub',
    installFailed: 'Falha na instalação',
    selectVersion: 'Selecione a versão',
    uploadFailed: 'Falha no upload',
    installedSuccessfully: 'Instalação bem-sucedida',
    installNote: 'Certifique-se de instalar apenas plug-ins de uma fonte confiável.',
    selectPackagePlaceholder: 'Selecione um pacote',
    selectPackage: 'Selecione o pacote',
  },
  upgrade: {
    title: 'Instale o plugin',
    successfulTitle: 'Instalação bem-sucedida',
    close: 'Fechar',
    upgrading: 'Instalar...',
    upgrade: 'Instalar',
    description: 'Prestes a instalar o seguinte plugin',
    usedInApps: 'Usado em aplicativos {{num}}',
  },
  error: {
    inValidGitHubUrl: 'URL do GitHub inválida. Insira um URL válido no formato: https://github.com/owner/repo',
    noReleasesFound: 'Nenhuma versão encontrada. Verifique o repositório GitHub ou a URL de entrada.',
    fetchReleasesError: 'Não é possível recuperar versões. Por favor, tente novamente mais tarde.',
  },
  marketplace: {
    sortOption: {
      mostPopular: 'Mais popular',
      firstReleased: 'Lançado pela primeira vez',
      recentlyUpdated: 'Atualizado recentemente',
      newlyReleased: 'Recém-lançado',
    },
    sortBy: 'Cidade negra',
    viewMore: 'Ver mais',
    and: 'e',
    pluginsResult: '{{num}} resultados',
    empower: 'Capacite seu desenvolvimento de IA',
    difyMarketplace: 'Mercado Dify',
    moreFrom: 'Mais do Marketplace',
    noPluginFound: 'Nenhum plugin encontrado',
    discover: 'Descobrir',
    verifiedTip: 'Verificado pelo Dify',
    partnerTip: 'Verificado por um parceiro da Dify',
  },
  task: {
    installedError: 'Falha na instalação dos plug-ins {{errorLength}}',
    installingWithSuccess: 'Instalando plugins {{installingLength}}, {{successLength}} sucesso.',
    installError: '{{errorLength}} plugins falha ao instalar, clique para ver',
    installingWithError: 'Instalando plug-ins {{installingLength}}, {{successLength}} sucesso, {{errorLength}} falhou',
    installing: 'Instalando plugins {{installingLength}}, 0 feito.',
    clearAll: 'Apagar tudo',
  },
  installAction: 'Instalar',
  endpointsEnabled: '{{num}} conjuntos de endpoints habilitados',
  submitPlugin: 'Enviar plugin',
  searchPlugins: 'Pesquisar plugins',
  searchInMarketplace: 'Pesquisar no Marketplace',
  installPlugin: 'Instale o plugin',
  from: 'De',
  searchTools: 'Ferramentas de pesquisa...',
  search: 'Procurar',
  fromMarketplace: 'Do Marketplace',
  allCategories: 'Todas as categorias',
  install: '{{num}} instala',
  searchCategories: 'Categorias de pesquisa',
  findMoreInMarketplace: 'Saiba mais no Marketplace',
  installFrom: 'INSTALAR DE',
  metadata: {
    title: 'Plugins',
  },
  difyVersionNotCompatible: 'A versão atual do Dify não é compatível com este plugin, por favor atualize para a versão mínima exigida: {{minimalDifyVersion}}',
}

export default translation
