const translation = {
  input: 'ENTRADA',
  result: 'RESULTADO',
  detail: 'DETALHE',
  tracing: 'RASTREIO',
  resultPanel: {
    status: 'STATUS',
    time: 'TEMPO DECORRIDO',
    tokens: 'TOTAL DE TOKENS',
  },
  meta: {
    title: 'METADADOS',
    status: 'Status',
    version: 'Vers<PERSON>',
    executor: 'Executor',
    startTime: 'Hora de Início',
    time: 'Tempo Decorrido',
    tokens: 'Total de Tokens',
    steps: 'Passos de Execução',
  },
  resultEmpty: {
    title: 'Esta execução apenas produz o formato JSON,',
    tipLeft: 'por favor vá para ',
    link: 'painel de detalhes',
    tipRight: ' veja.',
  },
  circularInvocationTip: 'Há uma invocação circular de ferramentas/nós no fluxo de trabalho atual.',
  actionLogs: 'Logs de ação',
}

export default translation
