const translation = {
  title: 'Prueba de recuperación',
  desc: 'Prueba del efecto de impacto del conocimiento basado en el texto de consulta proporcionado.',
  dateTimeFormat: 'MM/DD/YYYY hh:mm A',
  recents: 'Recientes',
  table: {
    header: {
      source: 'Fuente',
      text: 'Texto',
      time: 'Tiempo',
    },
  },
  input: {
    title: 'Texto fuente',
    placeholder: 'Por favor ingrese un texto, se recomienda una oración declarativa corta.',
    countWarning: 'Hasta 200 caracteres.',
    indexWarning: 'Solo conocimiento de alta calidad.',
    testing: '<PERSON><PERSON><PERSON>',
  },
  hit: {
    title: 'PÁRRAFOS DE RECUPERACIÓN',
    emptyTip: 'Los resultados de la prueba de recuperación se mostrarán aquí',
  },
  noRecentTip: 'No hay resultados de consulta recientes aquí',
  viewChart: 'Ver GRÁFICO VECTORIAL',
  viewDetail: 'Ver Detalle',
  settingTitle: 'Configuración de recuperación',
  open: 'Abrir',
  records: 'Archivo',
  chunkDetail: 'Detalle de fragmentos',
  keyword: 'Palabras clave',
  hitChunks: 'Golpea {{num}} fragmentos secundarios',
}

export default translation
