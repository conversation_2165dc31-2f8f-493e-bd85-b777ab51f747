const translation = {
  input: 'ENTRADA',
  result: 'RESULTADO',
  detail: 'DETALLE',
  tracing: 'TRAZADO',
  resultPanel: {
    status: 'ESTADO',
    time: 'TIEMPO TRANSCURRIDO',
    tokens: 'TOTAL DE TOKENS',
  },
  meta: {
    title: 'METADATOS',
    status: 'Estado',
    version: 'Versión',
    executor: 'Ejecutor',
    startTime: 'Hora de inicio',
    time: 'Tiempo transcurrido',
    tokens: 'Total de tokens',
    steps: 'Pasos de ejecución',
  },
  resultEmpty: {
    title: 'Esta ejecución solo produce formato JSON,',
    tipLeft: 'por favor ve al ',
    link: 'panel de detalle',
    tipRight: ' para verlo.',
  },
  actionLogs: 'Registros de acciones',
  circularInvocationTip: 'Hay una invocación circular de herramientas/nodos en el flujo de trabajo actual.',
}

export default translation
