const translation = {
  input: 'INPUT',
  result: 'RISULTATO',
  detail: 'DETTAGL<PERSON>',
  tracing: 'TRACCIAMENTO',
  resultPanel: {
    status: 'STATO',
    time: 'TEMPO TRASCORSO',
    tokens: 'TOKEN TOTALI',
  },
  meta: {
    title: 'METADATI',
    status: 'St<PERSON>',
    version: 'Versione',
    executor: 'Esecutore',
    startTime: 'Ora di Inizio',
    time: 'Tempo Trascorso',
    tokens: 'Token Totali',
    steps: 'Fasi Eseguite',
  },
  resultEmpty: {
    title: 'Questa esecuzione ha prodotto solo output in formato JSON,',
    tipLeft: 'per favore vai al ',
    link: 'pannello dei dettagli',
    tipRight: ' per visualizzarlo.',
  },
  circularInvocationTip: 'C\'è una chiamata circolare di strumenti/nodi nel flusso di lavoro corrente.',
  actionLogs: 'Registri delle azioni',
}

export default translation
