const translation = {
  currentPlan: '현재 요금제',
  upgradeBtn: {
    plain: '요금제 업그레이드',
    encourage: '지금 업그레이드',
    encourageShort: '업그레이드',
  },
  viewBilling: '청구 및 구독 관리',
  buyPermissionDeniedTip: '구독하려면 엔터프라이즈 관리자에게 문의하세요',
  plansCommon: {
    title: '당신에게 맞는 요금제를 선택하세요',
    yearlyTip: '연간 구독 시 2개월 무료!',
    mostPopular: '가장 인기 있는',
    planRange: {
      monthly: '월간',
      yearly: '연간',
    },
    month: '월',
    year: '년',
    save: '절약 ',
    free: '무료',
    currentPlan: '현재 요금제',
    contractSales: '영업에 문의하기',
    contractOwner: '팀 관리자에게 문의하기',
    startForFree: '무료로 시작하기',
    getStartedWith: '시작하기 ',
    contactSales: '영업에 문의하기',
    talkToSales: '영업과 상담하기',
    modelProviders: '모델 제공자',
    teamMembers: '팀 멤버',
    buildApps: '앱 만들기',
    vectorSpace: '벡터 공간',
    vectorSpaceBillingTooltip: '1MB당 약 120만 글자의 벡터화된 데이터를 저장할 수 있습니다 (OpenAI Embeddings을 기반으로 추정되며 모델에 따라 다릅니다).',
    vectorSpaceTooltip: '벡터 공간은 LLM이 데이터를 이해하는 데 필요한 장기 기억 시스템입니다.',
    documentProcessingPriority: '문서 처리 우선순위',
    documentProcessingPriorityTip: '더 높은 문서 처리 우선순위를 원하시면 요금제를 업그레이드하세요.',
    documentProcessingPriorityUpgrade: '더 높은 정확성과 빠른 속도로 데이터를 처리합니다.',
    priority: {
      'standard': '표준',
      'priority': '우선',
      'top-priority': '최우선',
    },
    logsHistory: '로그 기록',
    customTools: '사용자 정의 도구',
    unavailable: '사용 불가',
    days: '일',
    unlimited: '무제한',
    support: '지원',
    supportItems: {
      communityForums: '커뮤니티 포럼',
      emailSupport: '이메일 지원',
      priorityEmail: '우선 이메일 및 채팅 지원',
      logoChange: '로고 변경',
      SSOAuthentication: 'SSO 인증',
      personalizedSupport: '개별 지원',
      dedicatedAPISupport: '전용 API 지원',
      customIntegration: '사용자 정의 통합 및 지원',
      ragAPIRequest: 'RAG API 요청',
      agentMode: '에이전트 모드',
      workflow: '워크플로우',
      llmLoadingBalancing: 'LLM 로드 밸런싱',
      bulkUpload: '문서 대량 업로드',
      llmLoadingBalancingTooltip: '모델에 여러 API 키를 추가하여 API 속도 제한을 효과적으로 우회할 수 있습니다.',
    },
    comingSoon: '곧 출시 예정',
    member: '멤버',
    memberAfter: '멤버',
    messageRequest: {
      title: '메시지 크레딧',
      tooltip: 'GPT 제외 다양한 요금제에서의 메시지 호출 쿼터 (gpt4 제외). 제한을 초과하는 메시지는 OpenAI API 키를 사용합니다.',
      titlePerMonth: '{{count,number}} 메시지/월',
    },
    annotatedResponse: {
      title: '주석 응답 쿼터',
      tooltip: '수동으로 편집 및 응답 주석 달기로 앱의 사용자 정의 가능한 고품질 질의응답 기능을 제공합니다 (채팅 앱에만 해당).',
    },
    ragAPIRequestTooltip: 'Dify의 지식베이스 처리 기능을 호출하는 API 호출 수를 나타냅니다.',
    receiptInfo: '팀 소유자 및 팀 관리자만 구독 및 청구 정보를 볼 수 있습니다',
    annotationQuota: 'Annotation Quota(주석 할당량)',
    documentsUploadQuota: '문서 업로드 할당량',
    freeTrialTipPrefix: '가입하고 받으세요',
    comparePlanAndFeatures: '계획 및 기능 비교',
    documents: '{{count,number}} 지식 문서',
    apiRateLimit: 'API 요금 한도',
    cloud: '클라우드 서비스',
    unlimitedApiRate: 'API 호출 속도 제한 없음',
    freeTrialTip: '200회의 OpenAI 호출에 대한 무료 체험.',
    annualBilling: '연간 청구',
    getStarted: '시작하기',
    apiRateLimitUnit: '{{count,number}}/일',
    freeTrialTipSuffix: '신용카드 없음',
    teamWorkspace: '{{count,number}} 팀 작업 공간',
    self: '자체 호스팅',
    teamMember_other: '{{count,number}} 팀원',
    teamMember_one: '{{count,number}} 팀원',
    priceTip: '작업 공간당/',
    apiRateLimitTooltip: 'Dify API를 통한 모든 요청에는 API 요금 한도가 적용되며, 여기에는 텍스트 생성, 채팅 대화, 워크플로 실행 및 문서 처리가 포함됩니다.',
    documentsRequestQuota: '{{count,number}}/분 지식 요청 비율 제한',
    documentsTooltip: '지식 데이터 소스에서 가져올 수 있는 문서 수에 대한 쿼터.',
    documentsRequestQuotaTooltip: '지식 기반 내에서 작업 공간이 분당 수행할 수 있는 총 작업 수를 지정합니다. 여기에는 데이터 세트 생성, 삭제, 업데이트, 문서 업로드, 수정, 보관 및 지식 기반 쿼리가 포함됩니다. 이 지표는 지식 기반 요청의 성능을 평가하는 데 사용됩니다. 예를 들어, 샌드박스 사용자가 1분 이내에 10회의 연속 히트 테스트를 수행하면, 해당 작업 공간은 다음 1분 동안 데이터 세트 생성, 삭제, 업데이트 및 문서 업로드 또는 수정과 같은 작업을 수행하는 것이 일시적으로 제한됩니다.',
  },
  plans: {
    sandbox: {
      name: '샌드박스',
      description: 'GPT 무료 체험 200회',
      includesTitle: '포함된 항목:',
      for: '핵심 기능 무료 체험',
    },
    professional: {
      name: '프로페셔널',
      description: '개인 및 소규모 팀을 위해 더 많은 파워를 저렴한 가격에 제공합니다.',
      includesTitle: '무료 플랜에 추가로 포함된 항목:',
      for: '독립 개발자/소규모 팀을 위한',
    },
    team: {
      name: '팀',
      description: '제한 없이 협업하고 최고의 성능을 누리세요.',
      includesTitle: '프로페셔널 플랜에 추가로 포함된 항목:',
      for: '중간 규모 팀을 위한',
    },
    enterprise: {
      name: '엔터프라이즈',
      description: '대규모 미션 크리티컬 시스템을 위한 완전한 기능과 지원을 제공합니다.',
      includesTitle: '팀 플랜에 추가로 포함된 항목:',
      features: {
        2: '독점 기업 기능',
        1: '상업적 라이선스 승인',
        3: '다중 작업 공간 및 기업 관리',
        4: 'SSO',
        5: 'Dify 파트너에 의해 협상된 SLA',
        6: '고급 보안 및 제어',
        0: '기업급 확장 가능한 배포 솔루션',
        7: '디피 공식 업데이트 및 유지 관리',
        8: '전문 기술 지원',
      },
      price: '맞춤형',
      btnText: '판매 문의하기',
      for: '대규모 팀을 위해',
      priceTip: '연간 청구 전용',
    },
    community: {
      features: {
        0: '모든 핵심 기능이 공개 저장소에 릴리스됨',
        2: 'Dify 오픈 소스 라이선스를 준수합니다.',
        1: '단일 작업 공간',
      },
      btnText: '커뮤니티 시작하기',
      description: '개인 사용자, 소규모 팀 또는 비상업적 프로젝트를 위한',
      name: '커뮤니티',
      price: '무료',
      includesTitle: '무료 기능:',
      for: '개인 사용자, 소규모 팀 또는 비상업적 프로젝트를 위한',
    },
    premium: {
      features: {
        1: '단일 작업 공간',
        2: '웹앱 로고 및 브랜딩 맞춤화',
        3: '우선 이메일 및 채팅 지원',
        0: '다양한 클라우드 제공업체에 의한 자율 관리 신뢰성',
      },
      btnText: '프리미엄 받기',
      priceTip: '클라우드 마켓플레이스를 기반으로',
      name: '프리미엄',
      description: '중규모 조직 및 팀을 위한',
      comingSoon: '마이크로소프트 애저 및 구글 클라우드 지원 곧 제공됩니다.',
      price: '확장 가능',
      for: '중규모 조직 및 팀을 위한',
      includesTitle: '커뮤니티의 모든 것, 여기에 추가로:',
    },
  },
  vectorSpace: {
    fullTip: '벡터 공간이 가득 찼습니다.',
    fullSolution: '더 많은 공간을 얻으려면 요금제를 업그레이드하세요.',
  },
  apps: {
    fullTipLine1: '더 많은 앱을 생성하려면,',
    fullTipLine2: '요금제를 업그레이드하세요.',
    contactUs: '문의하기',
    fullTip1: '업그레이드하여 더 많은 앱을 만들기',
    fullTip2: '계획 한도에 도달했습니다.',
    fullTip2des: '비활성 애플리케이션을 정리하여 사용량을 줄이거나 저희에게 문의하는 것이 좋습니다.',
    fullTip1des: '이 계획에서 앱을 구축할 수 있는 한계에 도달했습니다.',
  },
  annotatedResponse: {
    fullTipLine1: '더 많은 대화를 주석 처리하려면,',
    fullTipLine2: '요금제를 업그레이드하세요.',
    quotaTitle: '주석 응답 쿼터',
  },
  usagePage: {
    vectorSpace: '지식 데이터 저장소',
    annotationQuota: '주석 할당량',
    teamMembers: '팀원들',
    buildApps: '앱 만들기',
    documentsUploadQuota: '문서 업로드 한도',
    vectorSpaceTooltip: '고품질 색인 모드를 사용하는 문서는 지식 데이터 저장소 자원을 소모합니다. 지식 데이터 저장소가 한도에 도달하면 새 문서를 업로드할 수 없습니다.',
  },
  teamMembers: '팀원들',
}

export default translation
