const translation = {
  apiServer: 'API Strežnik',
  apiKey: 'API Ključ',
  status: 'Status',
  disabled: 'Onemogoč<PERSON>',
  ok: 'V uporabi',
  copy: '<PERSON><PERSON><PERSON>',
  copied: '<PERSON><PERSON><PERSON>',
  regenerate: '<PERSON><PERSON>ira<PERSON>',
  play: 'Predvajaj',
  pause: 'Premor',
  playing: 'Predvajanje',
  loading: 'Nalaganje',
  merMaid: {
    rerender: 'Ponovno izrisi',
  },
  never: '<PERSON><PERSON>',
  apiKeyModal: {
    apiSecretKey: 'API Skrivni ključ',
    apiSecretKeyTips: 'Da bi preprečili zlorabo API-ja, zaščitite svoj API ključ. Izogibajte se uporabi v navadnem besedilu v sprednji kodi. :)',
    createNewSecretKey: 'Ustvari nov skrivni ključ',
    secretKey: 'Skrivni ključ',
    created: 'USTVARJENO',
    lastUsed: 'ZADNJA UPORABA',
    generateTips: 'Hranite ta ključ na varnem in dostopnem mestu.',
  },
  actionMsg: {
    deleteConfirmTitle: 'Izbrisati ta skrivni ključ?',
    deleteConfirmTips: 'To dejanje ni mogoče razveljaviti.',
    ok: 'V redu',
  },
  completionMode: {
    title: 'API za dokončanje aplikacije',
    info: 'Za visokokakovostno generiranje besedil, kot so članki, povzetki in prevodi, uporabite API za dokončanje sporočil z vnosom uporabnika. Generiranje besedil temelji na parametrih modela in predlogah pozivov, določenih v Dify Prompt Engineering.',
    createCompletionApi: 'Ustvari sporočilo o dokončanju',
    createCompletionApiTip: 'Ustvari sporočilo o dokončanju za podporo načinu vprašanj in odgovorov.',
    inputsTips: '(Neobvezno) Navedite vnosna polja uporabnikov kot ključ-vrednost pare, ki ustrezajo spremenljivkam v Prompt Eng. Ključ je ime spremenljivke, vrednost pa vrednost parametra. Če je vrsta polja Izberi, mora biti posredovana vrednost ena izmed vnaprej določenih možnosti.',
    queryTips: 'Vsebina besedila vnosa uporabnika.',
    blocking: 'Vrsta blokiranja, čakanje na dokončanje izvajanja in vračanje rezultatov. (Zahteve se lahko prekinejo, če je postopek dolg)',
    streaming: 'streaming povratki. Implementacija povratkov pretakanja na podlagi SSE (Server-Sent Events).',
    messageFeedbackApi: 'Povratne informacije o sporočilih (všeč)',
    messageFeedbackApiTip: 'Ocenite prejeta sporočila v imenu končnih uporabnikov z všečki ali nevšečki. Ti podatki so vidni na strani Dnevniki in opombe ter se uporabljajo za nadaljnje fino prilagajanje modela.',
    messageIDTip: 'ID sporočila',
    ratingTip: 'všeč ali nevšeč, null je preklic',
    parametersApi: 'Pridobite informacije o parametrih aplikacije',
    parametersApiTip: 'Pridobite konfigurirane vhodne parametre, vključno z imeni spremenljivk, imeni polj, vrstami in privzetimi vrednostmi. Običajno se uporablja za prikaz teh polj v obrazcu ali izpolnjevanje privzetih vrednosti po nalaganju odjemalca.',
  },
  chatMode: {
    title: 'API za klepet aplikacije',
    info: 'Za vsestranske pogovorne aplikacije, ki uporabljajo obliko vprašanj in odgovorov, pokličite API za klepetna sporočila, da začnete dialog. Ohranite tekoče pogovore tako, da prenesete vrnjeni conversation_id. Parametri odgovorov in predloge so odvisni od nastavitev Dify Prompt Eng.',
    createChatApi: 'Ustvari klepetno sporočilo',
    createChatApiTip: 'Ustvari novo pogovorno sporočilo ali nadaljuj obstoječi pogovor.',
    inputsTips: '(Neobvezno) Navedite vnosna polja uporabnikov kot ključ-vrednost pare, ki ustrezajo spremenljivkam v Prompt Eng. Ključ je ime spremenljivke, vrednost pa vrednost parametra. Če je vrsta polja Izberi, mora biti posredovana vrednost ena izmed vnaprej določenih možnosti.',
    queryTips: 'Vsebina vnosa/uporabniškega vprašanja',
    blocking: 'Vrsta blokiranja, čakanje na dokončanje izvajanja in vračanje rezultatov. (Zahteve se lahko prekinejo, če je postopek dolg)',
    streaming: 'streaming povratki. Implementacija povratkov pretakanja na podlagi SSE (Server-Sent Events).',
    conversationIdTip: '(Neobvezno) ID pogovora: pustite prazno za prvi pogovor; prenesite conversation_id iz konteksta, da nadaljujete dialog.',
    messageFeedbackApi: 'Povratne informacije končnih uporabnikov o sporočilu, všeč',
    messageFeedbackApiTip: 'Ocenite prejeta sporočila v imenu končnih uporabnikov z všečki ali nevšečki. Ti podatki so vidni na strani Dnevniki in opombe ter se uporabljajo za nadaljnje fino prilagajanje modela.',
    messageIDTip: 'ID sporočila',
    ratingTip: 'všeč ali nevšeč, null je preklic',
    chatMsgHistoryApi: 'Pridobi zgodovino klepetnih sporočil',
    chatMsgHistoryApiTip: 'Prva stran vrne najnovejše `limit` zapise, ki so v obratnem vrstnem redu.',
    chatMsgHistoryConversationIdTip: 'ID pogovora',
    chatMsgHistoryFirstId: 'ID prvega klepeta na trenutni strani. Privzeto ni.',
    chatMsgHistoryLimit: 'Koliko klepetov je vrnjenih na eno zahtevo',
    conversationsListApi: 'Pridobi seznam pogovorov',
    conversationsListApiTip: 'Pridobi seznam sej trenutnega uporabnika. Privzeto je vrnjenih zadnjih 20 sej.',
    conversationsListFirstIdTip: 'ID zadnjega zapisa na trenutni strani, privzeto ni.',
    conversationsListLimitTip: 'Koliko klepetov je vrnjenih na eno zahtevo',
    conversationRenamingApi: 'Preimenovanje pogovora',
    conversationRenamingApiTip: 'Preimenujte pogovore; ime je prikazano v večsejnih odjemalskih vmesnikih.',
    conversationRenamingNameTip: 'Novo ime',
    parametersApi: 'Pridobite informacije o parametrih aplikacije',
    parametersApiTip: 'Pridobite konfigurirane vhodne parametre, vključno z imeni spremenljivk, imeni polj, vrstami in privzetimi vrednostmi. Običajno se uporablja za prikaz teh polj v obrazcu ali izpolnjevanje privzetih vrednosti po nalaganju odjemalca.',
  },
  develop: {
    requestBody: 'Telo zahteve',
    pathParams: 'Parametri poti',
    query: 'Poizvedba',
    toc: 'Vsebino',
  },
}

export default translation
