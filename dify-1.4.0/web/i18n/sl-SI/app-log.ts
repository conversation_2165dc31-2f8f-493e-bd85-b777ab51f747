const translation = {
  title: 'Dnevnik<PERSON>',
  description: 'Dnevniki beležijo stanje delovanja aplikacije, vključno z vnosi uporabnikov in odgovori umetne inteligence.',
  dateTimeFormat: 'DD.MM.YYYY hh:mm A',
  table: {
    header: {
      updatedTime: 'Čas posodobitve',
      time: 'Čas ustvarjanja',
      endUser: 'Končni uporabnik ali račun',
      input: 'Vnos',
      output: 'Izhod',
      summary: 'Naslov',
      messageCount: '<PERSON>tevilo sporočil',
      userRate: 'Ocena uporabnika',
      adminRate: 'Ocena operaterja',
      startTime: 'ZAČETNI ČAS',
      status: 'STATUS',
      runtime: 'ČAS DELOVANJA',
      tokens: 'ŽETONI',
      user: 'Končni uporabnik ali račun',
      version: 'VERZIJA',
    },
    pagination: {
      previous: 'Prejš<PERSON>',
      next: 'Naslednja',
    },
    empty: {
      noChat: 'Še ni pogovora',
      noOutput: 'Ni izhoda',
      element: {
        title: 'Je kdo tam?',
        content: 'Opazujte in označite interakcije med končnimi uporabniki in aplikacijami umetne inteligence, da stalno izboljšujete natančnost AI. Lahko <shareLink>delite</shareLink> ali <testLink>preizkusite</testLink> spletno aplikacijo sami, nato pa se vrnete na to stran.',
      },
    },
  },
  detail: {
    time: 'Čas',
    conversationId: 'ID pogovora',
    promptTemplate: 'Predloga PROMPT-a',
    promptTemplateBeforeChat: 'Predloga PROMPT-a pred pogovorom · Kot sistemsko sporočilo',
    annotationTip: 'Izboljšave, ki jih je označil {{user}}',
    timeConsuming: 'Porabljen čas',
    second: 's',
    tokenCost: 'Porabljeni žetoni',
    loading: 'nalaganje',
    operation: {
      like: 'všeč',
      dislike: 'ni všeč',
      addAnnotation: 'Dodaj izboljšavo',
      editAnnotation: 'Uredi izboljšavo',
      annotationPlaceholder: 'Vnesite pričakovan odgovor, ki ga želite, da AI odgovori, kar se lahko uporabi za izboljšanje modela in kakovosti generiranja besedil v prihodnje.',
    },
    variables: 'Spremenljivke',
    uploadImages: 'Naložene slike',
    modelParams: 'Parametri modela',
  },
  filter: {
    period: {
      today: 'Danes',
      last7days: 'Zadnjih 7 dni',
      last4weeks: 'Zadnje 4 tedne',
      last3months: 'Zadnji 3 meseci',
      last12months: 'Zadnjih 12 mesecev',
      monthToDate: 'Mesec do danes',
      quarterToDate: 'Četrtletje do danes',
      yearToDate: 'Leto do danes',
      allTime: 'Vse obdobje',
    },
    annotation: {
      all: 'Vse',
      annotated: 'Označene izboljšave ({{count}} elementov)',
      not_annotated: 'Neoznačene',
    },
    sortBy: 'Razvrsti po:',
    descending: 'padajoče',
    ascending: 'naraščajoče',
  },
  workflowTitle: 'Dnevniki poteka dela',
  workflowSubtitle: 'Dnevnik beleži delovanje avtomatizacije.',
  runDetail: {
    title: 'Dnevnik pogovora',
    workflowTitle: 'Podrobnosti dnevnika',
    fileListDetail: 'Podrobnosti',
    fileListLabel: 'Podrobnosti o datoteki',
  },
  promptLog: 'Dnevnik PROMPT-ov',
  agentLog: 'Dnevnik pomočnika',
  viewLog: 'Ogled dnevnika',
  agentLogDetail: {
    agentMode: 'Način pomočnika',
    toolUsed: 'Uporabljeno orodje',
    iterations: 'Iteracije',
    iteration: 'Iteracija',
    finalProcessing: 'Končna obdelava',
  },
}

export default translation
