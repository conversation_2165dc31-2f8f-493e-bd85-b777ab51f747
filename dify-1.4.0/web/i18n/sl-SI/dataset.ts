const translation = {
  knowledge: '<PERSON>nan<PERSON>',
  externalTag: 'Zunanje',
  externalAPI: 'Zunanji API',
  externalAPIPanelTitle: 'Zunanji API za znanje',
  externalKnowledgeId: 'ID zunanjega znanja',
  externalKnowledgeName: 'I<PERSON> zunan<PERSON> znan<PERSON>',
  externalKnowledgeDescription: 'Opis znanja',
  externalKnowledgeIdPlaceholder: 'Prosimo, vnesite ID znanja',
  externalKnowledgeNamePlaceholder: 'Prosimo, vnesite ime baze znanja',
  externalKnowledgeDescriptionPlaceholder: 'Opišite, kaj je v tej bazi znanja (neobvezno)',
  learnHowToWriteGoodKnowledgeDescription: 'Nau<PERSON>ite se, kako napisati dober opis znanja',
  externalAPIPanelDescription: 'Zunanji API za znanje se uporablja za povezovanje z bazo znanja izven Dify in pridobivanje znanja iz te baze.',
  externalAPIPanelDocumentation: '<PERSON><PERSON><PERSON><PERSON> se, kako ustvariti zunanji API za znanje',
  documentCount: ' dokumentov',
  wordCount: ' tiso<PERSON> besed',
  appCount: ' povezanih aplikacij',
  createDataset: 'Ustvari znanje',
  createNewExternalAPI: 'Ustvari nov zunanji API za znanje',
  noExternalKnowledge: 'Zunanjega API-ja za znanje še ni, kliknite tukaj za ustvarjanje',
  createExternalAPI: 'Dodaj zunanji API za znanje',
  editExternalAPIFormTitle: 'Uredi zunanji API za znanje',
  editExternalAPITooltipTitle: 'POVEZANO ZNANJE',
  editExternalAPIConfirmWarningContent: {
    front: 'Ta zunanji API za znanje je povezan z',
    end: 'zunanjim znanjem, in ta sprememba bo vplivala na vse njih. Ali ste prepričani, da želite shraniti to spremembo?',
  },
  editExternalAPIFormWarning: {
    front: 'Ta zunanji API je povezan z',
    end: 'zunanjim znanjem',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      front: 'Izbriši',
      end: '?',
    },
    content: {
      front: 'Ta zunanji API za znanje je povezan z',
      end: 'zunanjim znanjem. Brisanje tega API-ja bo onemogočilo vse povezane baze znanja. Ali ste prepričani, da želite izbrisati ta API?',
    },
    noConnectionContent: 'Ali ste prepričani, da želite izbrisati ta API?',
  },
  selectExternalKnowledgeAPI: {
    placeholder: 'Izberite zunanji API za znanje',
  },
  connectDataset: 'Povežite se z zunanjo bazo znanja',
  connectDatasetIntro: {
    title: 'Kako se povezati z zunanjo bazo znanja',
    content: {
      front: 'Za povezavo z zunanjo bazo znanja morate najprej ustvariti zunanji API. Prosimo, natančno preberite in se sklicujte na',
      link: 'Naučite se, kako ustvariti zunanji API',
      end: '. Nato poiščite ustrezni ID znanja in ga vnesite v obrazec na levi. Če so vse informacije pravilne, boste po kliku na gumb za povezavo samodejno preusmerjeni na testiranje pridobivanja v bazi znanja.',
    },
    learnMore: 'Izvedite več',
  },
  connectHelper: {
    helper1: 'Povežite se z zunanjimi bazami znanja preko API-ja in ID-ja baze znanja. Trenutno je ',
    helper2: 'podprta le funkcionalnost pridobivanja',
    helper3: '. Močno priporočamo, da ',
    helper4: 'natančno preberete dokumentacijo za pomoč',
    helper5: ' pred uporabo te funkcije.',
  },
  createDatasetIntro: 'Uvozite lastne podatke v besedilni obliki ali v realnem času pišite podatke prek Webhook-a za izboljšanje konteksta LLM.',
  deleteDatasetConfirmTitle: 'Izbrisati to znanje?',
  deleteDatasetConfirmContent:
    'Brisanje znanja je nepovratno. Uporabniki do vašega znanja ne bodo več imeli dostopa, vse nastavitve pozivov in dnevniki bodo trajno izbrisani.',
  datasetUsedByApp: 'Znanje se uporablja v nekaterih aplikacijah. Aplikacije ne bodo več mogle uporabljati tega znanja, vse nastavitve pozivov in dnevniki bodo trajno izbrisani.',
  datasetDeleted: 'Znanje izbrisano',
  datasetDeleteFailed: 'Brisanje znanja ni uspelo',
  didYouKnow: 'Ali ste vedeli?',
  intro1: 'Znanje je mogoče integrirati v aplikacijo Dify ',
  intro2: 'kot kontekst',
  intro3: ',',
  intro4: 'ali pa ',
  intro5: 'se lahko ustvari',
  intro6: ' kot samostojni vtičnik ChatGPT za objavo',
  unavailable: 'Ni na voljo',
  unavailableTip: 'Vdelani model ni na voljo, potrebno je konfigurirati privzeti vdelani model',
  datasets: 'ZNANJE',
  datasetsApi: 'API DOSTOP',
  externalKnowledgeForm: {
    connect: 'Poveži',
    cancel: 'Prekliči',
  },
  externalAPIForm: {
    name: 'Ime',
    endpoint: 'API Končna točka',
    apiKey: 'API ključ',
    save: 'Shrani',
    cancel: 'Prekliči',
    edit: 'Uredi',
    encrypted: {
      front: 'Vaš API žeton bo šifriran in shranjen z uporabo',
      end: 'tehnologije.',
    },
  },
  retrieval: {
    semantic_search: {
      title: 'Vektorsko iskanje',
      description: 'Ustvari vdelke poizvedbe in poišči odstavke besedila, ki so najbolj podobni njegovi vektorski predstavitvi.',
    },
    full_text_search: {
      title: 'Iskanje celotnega besedila',
      description: 'Indeksirajte vse izraze v dokumentu, kar uporabnikom omogoča iskanje katerega koli izraza in pridobitev ustreznega odstavka besedila, ki ga vsebuje.',
    },
    hybrid_search: {
      title: 'Hibridno iskanje',
      description: 'Istočasno izvede iskanje celotnega besedila in vektorsko iskanje ter ponovno razvrsti zadetke, da izbere najboljše ujemanje za uporabnikovo poizvedbo. Uporabniki lahko določijo uteži ali konfigurirajo model za ponovno razvrščanje.',
      recommend: 'Priporočamo',
    },
    invertedIndex: {
      title: 'Inverzni indeks',
      description: 'Inverzni indeks je struktura, ki se uporablja za učinkovito pridobivanje. Organizirano po izrazih, vsak izraz kaže na dokumente ali spletne strani, ki ga vsebujejo.',
    },
    change: 'Spremeni',
    changeRetrievalMethod: 'Spremeni metodo pridobivanja',
  },
  docsFailedNotice: 'dokumentov ni bilo mogoče indeksirati',
  retry: 'Poskusi znova',
  indexingTechnique: {
    high_quality: 'HQ',
    economy: 'ECO',
  },
  indexingMethod: {
    semantic_search: 'VEKTORSKO',
    full_text_search: 'CELOTNO BESEDILO',
    hybrid_search: 'HIBRIDNO',
    invertedIndex: 'INVERZNO',
  },
  defaultRetrievalTip: 'Privzeto se uporablja večpotno pridobivanje. Znanje se pridobiva iz več baz znanja in nato ponovno razvrsti.',
  mixtureHighQualityAndEconomicTip: 'Model za ponovno razvrščanje je potreben za mešanico baz znanja visoke kakovosti in varčnih baz.',
  inconsistentEmbeddingModelTip: 'Model za ponovno razvrščanje je potreben, če so vdelani modeli izbranih baz znanja neenotni.',
  mixtureInternalAndExternalTip: 'Model za ponovno razvrščanje je potreben za mešanico notranjega in zunanjega znanja.',
  allExternalTip: 'Pri uporabi samo zunanjega znanja lahko uporabnik izbere, ali želi omogočiti model za ponovno razvrščanje. Če ni omogočen, bodo pridobljeni deli razvrščeni glede na ocene. Če so strategije pridobivanja različnih baz znanja neenotne, bo to netočno.',
  retrievalSettings: 'Nastavitve pridobivanja',
  rerankSettings: 'Nastavitve za ponovno razvrščanje',
  weightedScore: {
    title: 'Utežena ocena',
    description: 'Z nastavljanjem dodeljenih uteži ta strategija za ponovno razvrščanje določa, ali naj se daje prednost semantičnemu ali ključnemu ujemanju.',
    semanticFirst: 'Semantično najprej',
    keywordFirst: 'Ključne besede najprej',
    customized: 'Prilagojeno',
    semantic: 'Semantično',
    keyword: 'Ključna beseda',
  },
  nTo1RetrievalLegacy: 'N-to-1 pridobivanje bo uradno ukinjeno septembra. Priporočamo uporabo najnovejšega večpotnega pridobivanja za boljše rezultate.',
  nTo1RetrievalLegacyLink: 'Izvedite več',
  nTo1RetrievalLegacyLinkText: 'N-to-1 pridobivanje bo uradno ukinjeno septembra.',
  chunkingMode: {
    general: 'Splošno',
    parentChild: 'Starš-otrok',
  },
  parentMode: {
    fullDoc: 'Celoten dokument',
    paragraph: 'Odstavek',
  },
  batchAction: {
    cancel: 'Odpovedati',
    selected: 'Izbrane',
    enable: 'Omogočiti',
    disable: 'Onesposobiti',
    archive: 'Arhiv',
    delete: 'Izbrisati',
  },
  localDocs: 'Lokalni dokumenti',
  documentsDisabled: '{{num}} dokumenti onemogočeni - neaktivni več kot 30 dni',
  preprocessDocument: '{{num}} Predobdelava dokumentov',
  enable: 'Omogočiti',
  allKnowledge: 'Vse znanje',
  allKnowledgeDescription: 'Izberite, če želite prikazati vse znanje v tem delovnem prostoru. Samo lastnik delovnega prostora lahko upravlja vse znanje.',
  metadata: {
    createMetadata: {
      name: 'Ime',
      type: 'Tip',
      namePlaceholder: 'Dodajte ime metapodatkov',
      back: 'Nazaj',
      title: 'Nova metapodatki',
    },
    checkName: {
      empty: 'Ime metapodatkov ne more biti prazno',
      invalid: 'Ime metapodatkov lahko vsebuje samo male črke, številke in podčrtaje ter se mora začeti z malo črko.',
    },
    batchEditMetadata: {
      editMetadata: 'Uredi metapodatke',
      applyToAllSelectDocument: 'Uporabi za vse izbrane dokumente',
      multipleValue: 'Več vrednosti',
      applyToAllSelectDocumentTip: 'Samodejno ustvarite vse zgoraj omenjene urejene in nove metapodatke za vsa izbrana dokumenta, sicer bo urejanje metapodatkov veljalo le za dokumente, ki jih imajo.',
      editDocumentsNum: 'Urejanje {{num}} dokumentov',
    },
    selectMetadata: {
      search: 'Išči metapodatke',
      newAction: 'Nova metapodatki',
      manageAction: 'Upravljati',
    },
    datasetMetadata: {
      rename: 'Preimenuj',
      namePlaceholder: 'Ime metapodatkov',
      deleteTitle: 'Potrdite, da želite izbrisati',
      builtIn: 'Vgrajeno',
      deleteContent: 'Ali ste prepričani, da želite izbrisati metadata "{{name}}"',
      builtInDescription: 'Vgrajeni metapodatki so samodejno izvlečeni in ustvarjeni. Morajo biti omogočeni pred uporabo in jih ni mogoče urejati.',
      values: '{{num}} Vrednosti',
      addMetaData: 'Dodaj metapodatke',
      description: 'Vse metapodatke lahko upravljate tukaj v tej bazi znanja. Spremembe bodo usklajene z vsakim dokumentom.',
      disabled: 'Onemogočeno',
      name: 'Ime',
    },
    documentMetadata: {
      startLabeling: 'Začni označevanje',
      technicalParameters: 'Tehnični parametri',
      metadataToolTip: 'Metapodatki služijo kot pomemben filter, ki izboljšuje natančnost in pomembnost iskanja informacij. Tukaj lahko spremenite in dodate metapodatke za ta dokument.',
      documentInformation: 'Informacije o dokumentu',
    },
    metadata: 'Meta podatki',
    chooseTime: 'Izberi čas...',
    addMetadata: 'Dodaj metapodatke',
  },
  embeddingModelNotAvailable: 'Model za zajemanje ni na voljo.',
}

export default translation
