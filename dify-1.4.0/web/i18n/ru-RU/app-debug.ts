const translation = {
  pageTitle: {
    line1: 'PROMPT',
    line2: 'Engineering',
  },
  orchestrate: 'Оркестрация',
  promptMode: {
    simple: 'Переключиться в экспертный режим для редактирования всего ПРОМПТА',
    advanced: 'Экспертный режим',
    switchBack: 'Переключиться обратно',
    advancedWarning: {
      title: 'Вы переключились в экспертный режим, и после изменения ПРОМПТА вы НЕ СМОЖЕТЕ вернуться в базовый режим.',
      description: 'В экспертном режиме вы можете редактировать весь ПРОМПТ.',
      learnMore: 'Узнать больше',
      ok: 'ОК',
    },
    operation: {
      addMessage: 'Добавить сообщение',
    },
    contextMissing: 'Отсутствует компонент контекста, эффективность промпта может быть невысокой.',
  },
  operation: {
    applyConfig: 'Опубликовать',
    resetConfig: 'Сбросить',
    debugConfig: 'Отладка',
    addFeature: 'Добавить функцию',
    automatic: 'Сгенерировать',
    stopResponding: 'Остановить ответ',
    agree: 'лайк',
    disagree: 'дизлайк',
    cancelAgree: 'Отменить лайк',
    cancelDisagree: 'Отменить дизлайк',
    userAction: 'Пользователь ',
  },
  notSetAPIKey: {
    title: 'Ключ поставщика LLM не установлен',
    trailFinished: 'Пробный период закончен',
    description: 'Ключ поставщика LLM не установлен, его необходимо установить перед отладкой.',
    settingBtn: 'Перейти к настройкам',
  },
  trailUseGPT4Info: {
    title: 'В настоящее время не поддерживается gpt-4',
    description: 'Чтобы использовать gpt-4, пожалуйста, установите API ключ.',
  },
  feature: {
    groupChat: {
      title: 'Улучшение чата',
      description: 'Добавление настроек предварительного разговора для приложений может улучшить пользовательский опыт.',
    },
    groupExperience: {
      title: 'Улучшение опыта',
    },
    conversationOpener: {
      title: 'Начальное сообщение',
      description: 'В чат-приложении первое предложение, которое ИИ активно говорит пользователю, обычно используется в качестве приветствия.',
    },
    suggestedQuestionsAfterAnswer: {
      title: 'Последующие вопросы',
      description: 'Настройка предложения следующих вопросов может улучшить чат для пользователей.',
      resDes: '3 предложения для следующего вопроса пользователя.',
      tryToAsk: 'Попробуйте спросить',
    },
    moreLikeThis: {
      title: 'Больше похожего',
      description: 'Сгенерируйте несколько текстов одновременно, а затем отредактируйте и продолжайте генерировать',
      generateNumTip: 'Количество генерируемых каждый раз',
      tip: 'Использование этой функции приведет к дополнительным расходам токенов',
    },
    speechToText: {
      title: 'Преобразование речи в текст',
      description: 'После включения вы можете использовать голосовой ввод.',
      resDes: 'Голосовой ввод включен',
    },
    textToSpeech: {
      title: 'Преобразование текста в речь',
      description: 'После включения текст можно преобразовать в речь.',
      resDes: 'Преобразование текста в аудио включено',
    },
    citation: {
      title: 'Цитаты и ссылки',
      description: 'После включения отображается исходный документ и атрибутированная часть сгенерированного контента.',
      resDes: 'Цитаты и ссылки включены',
    },
    annotation: {
      title: 'Ответ аннотации',
      description: 'Вы можете вручную добавить высококачественный ответ в кэш для приоритетного сопоставления с похожими вопросами пользователей.',
      resDes: 'Ответ аннотации включен',
      scoreThreshold: {
        title: 'Порог оценки',
        description: 'Используется для установки порога сходства для ответа аннотации.',
        easyMatch: 'Простое совпадение',
        accurateMatch: 'Точное совпадение',
      },
      matchVariable: {
        title: 'Переменная соответствия',
        choosePlaceholder: 'Выберите переменную соответствия',
      },
      cacheManagement: 'Аннотации',
      cached: 'Аннотировано',
      remove: 'Удалить',
      removeConfirm: 'Удалить эту аннотацию?',
      add: 'Добавить аннотацию',
      edit: 'Редактировать аннотацию',
    },
    dataSet: {
      title: 'Контекст',
      noData: 'Вы можете импортировать знания в качестве контекста',
      words: 'Слова',
      textBlocks: 'Текстовые блоки',
      selectTitle: 'Выберите справочные знания',
      selected: 'Знания выбраны',
      noDataSet: 'Знания не найдены',
      toCreate: 'Перейти к созданию',
      notSupportSelectMulti: 'В настоящее время поддерживаются только одни знания',
      queryVariable: {
        title: 'Переменная запроса',
        tip: 'Эта переменная будет использоваться в качестве входных данных запроса для поиска контекста, получая информацию о контексте, связанную с вводом этой переменной.',
        choosePlaceholder: 'Выберите переменную запроса',
        noVar: 'Нет переменных',
        noVarTip: 'пожалуйста, создайте переменную в разделе Переменные',
        unableToQueryDataSet: 'Невозможно запросить знания',
        unableToQueryDataSetTip: 'Не удалось успешно запросить знания, пожалуйста, выберите переменную запроса контекста в разделе контекста.',
        ok: 'ОК',
        contextVarNotEmpty: 'переменная запроса контекста не может быть пустой',
        deleteContextVarTitle: 'Удалить переменную "{{varName}}"?',
        deleteContextVarTip: 'Эта переменная была установлена в качестве переменной запроса контекста, и ее удаление повлияет на нормальное использование знаний. Если вам все еще нужно удалить ее, пожалуйста, выберите ее заново в разделе контекста.',
      },
    },
    tools: {
      title: 'Инструменты',
      tips: 'Инструменты предоставляют стандартный метод вызова API, принимая пользовательский ввод или переменные в качестве параметров запроса для запроса внешних данных в качестве контекста.',
      toolsInUse: '{{count}} инструментов используется',
      modal: {
        title: 'Инструмент',
        toolType: {
          title: 'Тип инструмента',
          placeholder: 'Пожалуйста, выберите тип инструмента',
        },
        name: {
          title: 'Имя',
          placeholder: 'Пожалуйста, введите имя',
        },
        variableName: {
          title: 'Имя переменной',
          placeholder: 'Пожалуйста, введите имя переменной',
        },
      },
    },
    conversationHistory: {
      title: 'История разговоров',
      description: 'Установить префиксы имен для ролей разговора',
      tip: 'История разговоров не включена, пожалуйста, добавьте <histories> в промпт выше.',
      learnMore: 'Узнать больше',
      editModal: {
        title: 'Редактировать имена ролей разговора',
        userPrefix: 'Префикс пользователя',
        assistantPrefix: 'Префикс помощника',
      },
    },
    toolbox: {
      title: 'НАБОР ИНСТРУМЕНТОВ',
    },
    moderation: {
      title: 'Модерация контента',
      description: 'Обеспечьте безопасность выходных данных модели, используя API модерации или поддерживая список чувствительных слов.',
      allEnabled: 'ВХОДНОЙ/ВЫХОДНОЙ контент включен',
      inputEnabled: 'ВХОДНОЙ контент включен',
      outputEnabled: 'ВЫХОДНОЙ контент включен',
      modal: {
        title: 'Настройки модерации контента',
        provider: {
          title: 'Поставщик',
          openai: 'Модерация OpenAI',
          openaiTip: {
            prefix: 'Для модерации OpenAI требуется ключ API OpenAI, настроенный в ',
            suffix: '.',
          },
          keywords: 'Ключевые слова',
        },
        keywords: {
          tip: 'По одному на строку, разделенные разрывами строк. До 100 символов на строку.',
          placeholder: 'По одному на строку, разделенные разрывами строк',
          line: 'Строка',
        },
        content: {
          input: 'Модерировать ВХОДНОЙ контент',
          output: 'Модерировать ВЫХОДНОЙ контент',
          preset: 'Предустановленные ответы',
          placeholder: 'Здесь содержимое предустановленных ответов',
          condition: 'Модерация ВХОДНОГО и ВЫХОДНОГО контента включена хотя бы одна',
          fromApi: 'Предустановленные ответы возвращаются API',
          errorMessage: 'Предустановленные ответы не могут быть пустыми',
          supportMarkdown: 'Markdown поддерживается',
        },
        openaiNotConfig: {
          before: 'Для модерации OpenAI требуется ключ API OpenAI, настроенный в',
          after: '',
        },
      },
    },
  },
  generate: {
    title: 'Генератор промпта',
    description: 'Генератор промпта использует настроенную модель для оптимизации промпта для повышения качества и улучшения структуры. Пожалуйста, напишите четкие и подробные инструкции.',
    tryIt: 'Попробуйте',
    instruction: 'Инструкции',
    instructionPlaceHolder: 'Напишите четкие и конкретные инструкции.',
    generate: 'Сгенерировать',
    resTitle: 'Сгенерированный промпт',
    noDataLine1: 'Опишите свой случай использования слева,',
    noDataLine2: 'предварительный просмотр оркестрации будет показан здесь.',
    apply: 'Применить',
    loading: 'Оркестрация приложения для вас...',
    overwriteTitle: 'Перезаписать существующую конфигурацию?',
    overwriteMessage: 'Применение этого промпта перезапишет существующую конфигурацию.',
    template: {
      pythonDebugger: {
        name: 'Отладчик Python',
        instruction: 'Бот, который может генерировать и отлаживать ваш код на основе ваших инструкций',
      },
      translation: {
        name: 'Переводчик',
        instruction: 'Переводчик, который может переводить на несколько языков',
      },
      professionalAnalyst: {
        name: 'Профессиональный аналитик',
        instruction: 'Извлекайте информацию, выявляйте риски и извлекайте ключевую информацию из длинных отчетов в одну записку',
      },
      excelFormulaExpert: {
        name: 'Эксперт по формулам Excel',
        instruction: 'Чат-бот, который может помочь начинающим пользователям понять, использовать и создавать формулы Excel на основе инструкций пользователя',
      },
      travelPlanning: {
        name: 'Планировщик путешествий',
        instruction: 'Помощник по планированию путешествий - это интеллектуальный инструмент, разработанный, чтобы помочь пользователям без труда планировать свои поездки',
      },
      SQLSorcerer: {
        name: 'SQL-ассистент',
        instruction: 'Преобразуйте повседневный язык в SQL-запросы',
      },
      GitGud: {
        name: 'Git gud',
        instruction: 'Генерируйте соответствующие команды Git на основе описанных пользователем действий по управлению версиями',
      },
      meetingTakeaways: {
        name: 'Итоги совещания',
        instruction: 'Извлекайте из совещаний краткие резюме, включая темы обсуждения, ключевые выводы и элементы действий',
      },
      writingsPolisher: {
        name: 'Редактор',
        instruction: 'Используйте LLM, чтобы улучшить свои письменные работы',
      },
    },
  },
  resetConfig: {
    title: 'Подтвердить сброс?',
    message:
      'Сброс отменяет изменения, восстанавливая последнюю опубликованную конфигурацию.',
  },
  errorMessage: {
    nameOfKeyRequired: 'имя ключа: {{key}} обязательно',
    valueOfVarRequired: 'значение {{key}} не может быть пустым',
    queryRequired: 'Требуется текст запроса.',
    waitForResponse:
      'Пожалуйста, дождитесь завершения ответа на предыдущее сообщение.',
    waitForBatchResponse:
      'Пожалуйста, дождитесь завершения ответа на пакетное задание.',
    notSelectModel: 'Пожалуйста, выберите модель',
    waitForImgUpload: 'Пожалуйста, дождитесь загрузки изображения',
  },
  chatSubTitle: 'Инструкции',
  completionSubTitle: 'Префикс Промпта',
  promptTip:
    'Промпт направляют ответы ИИ с помощью инструкций и ограничений. Вставьте переменные, такие как {{input}}. Этот Промпт не будет видна пользователям.',
  formattingChangedTitle: 'Форматирование изменено',
  formattingChangedText:
    'Изменение форматирования приведет к сбросу области отладки, вы уверены?',
  variableTitle: 'Переменные',
  variableTip:
    'Пользователи заполняют переменные в форме, автоматически заменяя переменные в промпте.',
  notSetVar: 'Переменные позволяют пользователям вводить промпты или вступительные замечания при заполнении форм. Вы можете попробовать ввести "{{input}}" в промптах.',
  autoAddVar: 'В предварительной промпте упоминаются неопределенные переменные, хотите ли вы добавить их в форму пользовательского ввода?',
  variableTable: {
    key: 'Ключ переменной',
    name: 'Имя поля пользовательского ввода',
    optional: 'Необязательно',
    type: 'Тип ввода',
    action: 'Действия',
    typeString: 'Строка',
    typeSelect: 'Выбор',
  },
  varKeyError: {
    canNoBeEmpty: '{{key}} обязательно',
    tooLong: '{{key}} слишком длинное. Не может быть длиннее 30 символов',
    notValid: '{{key}} недействительно. Может содержать только буквы, цифры и подчеркивания',
    notStartWithNumber: '{{key}} не может начинаться с цифры',
    keyAlreadyExists: '{{key}} уже существует',
  },
  otherError: {
    promptNoBeEmpty: 'Промпт не может быть пустой',
    historyNoBeEmpty: 'История разговоров должна быть установлена в промпте',
    queryNoBeEmpty: 'Запрос должен быть установлен в промпте',
  },
  variableConfig: {
    'addModalTitle': 'Добавить поле ввода',
    'editModalTitle': 'Редактировать поле ввода',
    'description': 'Настройка для переменной {{varName}}',
    'fieldType': 'Тип поля',
    'string': 'Короткий текст',
    'text-input': 'Короткий текст',
    'paragraph': 'Абзац',
    'select': 'Выбор',
    'number': 'Число',
    'notSet': 'Не задано, попробуйте ввести {{input}} в префикс промпта',
    'stringTitle': 'Параметры текстового поля формы',
    'maxLength': 'Максимальная длина',
    'options': 'Варианты',
    'addOption': 'Добавить вариант',
    'apiBasedVar': 'Переменная на основе API',
    'varName': 'Имя переменной',
    'labelName': 'Имя метки',
    'inputPlaceholder': 'Пожалуйста, введите',
    'content': 'Содержимое',
    'required': 'Обязательно',
    'errorMsg': {
      labelNameRequired: 'Имя метки обязательно',
      varNameCanBeRepeat: 'Имя переменной не может повторяться',
      atLeastOneOption: 'Требуется хотя бы один вариант',
      optionRepeat: 'Есть повторяющиеся варианты',
    },
  },
  vision: {
    name: 'Зрение',
    description: 'Включение зрения позволит модели принимать изображения и отвечать на вопросы о них.',
    settings: 'Настройки',
    visionSettings: {
      title: 'Настройки зрения',
      resolution: 'Разрешение',
      resolutionTooltip: `Низкое разрешение позволит модели получать версию изображения с низким разрешением 512 x 512 и представлять изображение с бюджетом 65 токенов. Это позволяет API возвращать ответы быстрее и потреблять меньше входных токенов для случаев использования, не требующих высокой детализации.
      \n
      Высокое разрешение сначала позволит модели увидеть изображение с низким разрешением, а затем создаст детальные фрагменты входных изображений в виде квадратов 512 пикселей на основе размера входного изображения. Каждый из детальных фрагментов использует вдвое больший бюджет токенов, в общей сложности 129 токенов.`,
      high: 'Высокое',
      low: 'Низкое',
      uploadMethod: 'Метод загрузки',
      both: 'Оба',
      localUpload: 'Локальная загрузка',
      url: 'URL',
      uploadLimit: 'Лимит загрузки',
    },
  },
  voice: {
    name: 'Голос',
    defaultDisplay: 'Голос по умолчанию',
    description: 'Настройки преобразования текста в речь',
    settings: 'Настройки',
    voiceSettings: {
      title: 'Настройки голоса',
      language: 'Язык',
      resolutionTooltip: 'Язык, поддерживаемый преобразованием текста в речь.',
      voice: 'Голос',
      autoPlay: 'Автовоспроизведение',
      autoPlayEnabled: 'Включить',
      autoPlayDisabled: 'Выключить',
    },
  },
  openingStatement: {
    title: 'Начальное сообщение',
    add: 'Добавить',
    writeOpener: 'Написать начальное сообщение',
    placeholder: 'Напишите здесь свое начальное сообщение, вы можете использовать переменные, попробуйте ввести {{variable}}.',
    openingQuestion: 'Начальные вопросы',
    noDataPlaceHolder:
      'Начало разговора с пользователем может помочь ИИ установить более тесную связь с ним в диалоговых приложениях.',
    varTip: 'Вы можете использовать переменные, попробуйте ввести {{variable}}',
    tooShort: 'Для генерации вступительного замечания к разговору требуется не менее 20 слов начального промпта.',
    notIncludeKey: 'Начальный промпт не включает переменную: {{key}}. Пожалуйста, добавьте её в начальную промпт.',
  },
  modelConfig: {
    model: 'Модель',
    setTone: 'Установить тон ответов',
    title: 'Модель и параметры',
    modeType: {
      chat: 'Чат',
      completion: 'Завершение',
    },
  },
  inputs: {
    title: 'Отладка и предварительный просмотр',
    noPrompt: 'Попробуйте написать промпт во входных данных предварительного промпта',
    userInputField: 'Поле пользовательского ввода',
    noVar: 'Заполните значение переменной, которое будет автоматически заменяться в промпте каждый раз при запуске нового сеанса.',
    chatVarTip:
      'Заполните значение переменной, которое будет автоматически заменяться в промпте каждый раз при запуске нового сеанса',
    completionVarTip:
      'Заполните значение переменной, которое будет автоматически заменяться в промпте каждый раз при отправке вопроса.',
    previewTitle: 'Предварительный просмотр промпта',
    queryTitle: 'Содержимое запроса',
    queryPlaceholder: 'Пожалуйста, введите текст запроса.',
    run: 'ЗАПУСТИТЬ',
  },
  result: 'Выходной текст',
  datasetConfig: {
    settingTitle: 'Настройки поиска',
    knowledgeTip: 'Нажмите кнопку "+", чтобы добавить знания',
    retrieveOneWay: {
      title: 'Поиск N-к-1',
      description: 'На основе намерения пользователя и описаний знаний агент автономно выбирает наилучшие знания для запроса. Лучше всего подходит для приложений с различными, ограниченными знаниями.',
    },
    retrieveMultiWay: {
      title: 'Многопутный поиск',
      description: 'На основе намерения пользователя выполняет запросы по всем знаниям, извлекает соответствующий текст из нескольких источников и выбирает наилучшие результаты, соответствующие запросу пользователя, после повторного ранжирования.',
    },
    rerankModelRequired: 'Требуется rerank-модель ',
    params: 'Параметры',
    top_k: 'Top K',
    top_kTip: 'Используется для фильтрации фрагментов, наиболее похожих на вопросы пользователей. Система также будет динамически корректировать значение Top K в зависимости от max_tokens выбранной модели.',
    score_threshold: 'Порог оценки',
    score_thresholdTip: 'Используется для установки порога сходства для фильтрации фрагментов.',
    retrieveChangeTip: 'Изменение режима индексации и режима поиска может повлиять на приложения, связанные с этими знаниями.',
  },
  debugAsSingleModel: 'Отладка как одной модели',
  debugAsMultipleModel: 'Отладка как нескольких моделей',
  duplicateModel: 'Дублировать',
  publishAs: 'Опубликовать как',
  assistantType: {
    name: 'Тип помощника',
    chatAssistant: {
      name: 'Базовый помощник',
      description: 'Создайте помощника на основе чата, используя большую языковую модель',
    },
    agentAssistant: {
      name: 'Агент-помощник',
      description: 'Создайте интеллектуального агента, который может автономно выбирать инструменты для выполнения задач',
    },
  },
  agent: {
    agentMode: 'Режим агента',
    agentModeDes: 'Установите тип режима вывода для агента',
    agentModeType: {
      ReACT: 'ReAct',
      functionCall: 'Вызов функции',
    },
    setting: {
      name: 'Настройки агента',
      description: 'Настройки агента-помощника позволяют установить режим агента и расширенные функции, такие как встроенные промпты, доступные только в типе агента.',
      maximumIterations: {
        name: 'Максимальное количество итераций',
        description: 'Ограничьте количество итераций, которые может выполнить агент-помощник',
      },
    },
    buildInPrompt: 'Встроенный промпт',
    firstPrompt: 'Первый промпт',
    nextIteration: 'Следующая итерация',
    promptPlaceholder: 'Напишите здесь свой первый промпт',
    tools: {
      name: 'Инструменты',
      description: 'Использование инструментов может расширить возможности LLM, такие как поиск в Интернете или выполнение научных расчетов',
      enabled: 'Включено',
    },
  },
}

export default translation
