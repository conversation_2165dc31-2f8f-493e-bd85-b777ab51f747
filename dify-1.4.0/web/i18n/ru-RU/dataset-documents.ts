const translation = {
  list: {
    title: 'Документы',
    desc: 'Здесь отображаются все файлы базы знаний, и вся база знаний может быть связана с цитатами Dify или проиндексирована с помощью чата.',
    addFile: 'Добавить файл',
    addPages: 'Добавить страницы',
    addUrl: 'Добавить URL',
    table: {
      header: {
        fileName: 'НАЗВАНИЕ ФАЙЛА',
        words: 'СЛОВА',
        hitCount: 'КОЛИЧЕСТВО ОБРАЩЕНИЙ',
        uploadTime: 'ВРЕМЯ ЗАГРУЗКИ',
        status: 'СТАТУС',
        action: 'ДЕЙСТВИЕ',
        chunkingMode: 'РЕЖИМ ДРОБЛЕНИЯ',
      },
      rename: 'Переименовать',
      name: 'Название',
    },
    action: {
      uploadFile: 'Загрузить новый файл',
      settings: 'Настройки сегментации',
      addButton: 'Добавить фрагмент',
      add: 'Добавить фрагмент',
      batchAdd: 'Пакетное добавление',
      archive: 'Архивировать',
      unarchive: 'Разархивировать',
      delete: 'Удалить',
      enableWarning: 'Архивный файл не может быть включен',
      sync: 'Синхронизировать',
    },
    index: {
      enable: 'Включить',
      disable: 'Отключить',
      all: 'Все',
      enableTip: 'Файл может быть проиндексирован',
      disableTip: 'Файл не может быть проиндексирован',
    },
    status: {
      queuing: 'В очереди',
      indexing: 'Индексация',
      paused: 'Приостановлено',
      error: 'Ошибка',
      available: 'Доступно',
      enabled: 'Включено',
      disabled: 'Отключено',
      archived: 'Архивировано',
    },
    empty: {
      title: 'Пока нет документов',
      upload: {
        tip: 'Вы можете загружать файлы, синхронизировать с веб-сайта или из веб-приложений, таких как Notion, GitHub и т. д.',
      },
      sync: {
        tip: 'Dify будет периодически загружать файлы из вашего Notion и завершать обработку.',
      },
    },
    delete: {
      title: 'Вы уверены, что хотите удалить?',
      content: 'Если вам нужно будет возобновить обработку позже, вы продолжите с того места, где остановились',
    },
    batchModal: {
      title: 'Пакетное добавление фрагментов',
      csvUploadTitle: 'Перетащите сюда свой CSV-файл или ',
      browse: 'обзор',
      tip: 'CSV-файл должен соответствовать следующей структуре:',
      question: 'вопрос',
      answer: 'ответ',
      contentTitle: 'содержимое фрагмента',
      content: 'содержимое',
      template: 'Скачать шаблон здесь',
      cancel: 'Отмена',
      run: 'Запустить пакет',
      runError: 'Ошибка запуска пакета',
      processing: 'В процессе пакетной обработки',
      completed: 'Импорт завершен',
      error: 'Ошибка импорта',
      ok: 'ОК',
    },
    learnMore: 'Подробнее',
  },
  metadata: {
    title: 'Метаданные',
    desc: 'Маркировка метаданных для документов позволяет ИИ своевременно получать к ним доступ и раскрывать источник ссылок для пользователей.',
    dateTimeFormat: 'D MMMM YYYY, HH:mm',
    docTypeSelectTitle: 'Пожалуйста, выберите тип документа',
    docTypeChangeTitle: 'Изменить тип документа',
    docTypeSelectWarning:
      'Если тип документа будет изменен, заполненные сейчас метаданные больше не будут сохранены',
    firstMetaAction: 'Поехали',
    placeholder: {
      add: 'Добавить ',
      select: 'Выбрать ',
    },
    source: {
      upload_file: 'Загрузить файл',
      notion: 'Синхронизировать из Notion',
      github: 'Синхронизировать из Github',
    },
    type: {
      book: 'Книга',
      webPage: 'Веб-страница',
      paper: 'Статья',
      socialMediaPost: 'Пост в социальных сетях',
      personalDocument: 'Личный документ',
      businessDocument: 'Деловой документ',
      IMChat: 'Чат в мессенджере',
      wikipediaEntry: 'Статья в Википедии',
      notion: 'Синхронизировать из Notion',
      github: 'Синхронизировать из Github',
      technicalParameters: 'Технические параметры',
    },
    field: {
      processRule: {
        processDoc: 'Обработка документа',
        segmentRule: 'Правило фрагментации',
        segmentLength: 'Длина фрагментов',
        processClean: 'Очистка текста',
      },
      book: {
        title: 'Название',
        language: 'Язык',
        author: 'Автор',
        publisher: 'Издатель',
        publicationDate: 'Дата публикации',
        ISBN: 'ISBN',
        category: 'Категория',
      },
      webPage: {
        title: 'Название',
        url: 'URL',
        language: 'Язык',
        authorPublisher: 'Автор/Издатель',
        publishDate: 'Дата публикации',
        topicKeywords: 'Темы/Ключевые слова',
        description: 'Описание',
      },
      paper: {
        title: 'Название',
        language: 'Язык',
        author: 'Автор',
        publishDate: 'Дата публикации',
        journalConferenceName: 'Название журнала/конференции',
        volumeIssuePage: 'Том/Выпуск/Страница',
        DOI: 'DOI',
        topicsKeywords: 'Темы/Ключевые слова',
        abstract: 'Аннотация',
      },
      socialMediaPost: {
        platform: 'Платформа',
        authorUsername: 'Автор/Имя пользователя',
        publishDate: 'Дата публикации',
        postURL: 'URL поста',
        topicsTags: 'Темы/Теги',
      },
      personalDocument: {
        title: 'Название',
        author: 'Автор',
        creationDate: 'Дата создания',
        lastModifiedDate: 'Дата последнего изменения',
        documentType: 'Тип документа',
        tagsCategory: 'Теги/Категория',
      },
      businessDocument: {
        title: 'Название',
        author: 'Автор',
        creationDate: 'Дата создания',
        lastModifiedDate: 'Дата последнего изменения',
        documentType: 'Тип документа',
        departmentTeam: 'Отдел/Команда',
      },
      IMChat: {
        chatPlatform: 'Платформа чата',
        chatPartiesGroupName: 'Участники чата/Название группы',
        participants: 'Участники',
        startDate: 'Дата начала',
        endDate: 'Дата окончания',
        topicsKeywords: 'Темы/Ключевые слова',
        fileType: 'Тип файла',
      },
      wikipediaEntry: {
        title: 'Название',
        language: 'Язык',
        webpageURL: 'URL веб-страницы',
        editorContributor: 'Редактор/Автор',
        lastEditDate: 'Дата последнего редактирования',
        summaryIntroduction: 'Краткое содержание/Введение',
      },
      notion: {
        title: 'Название',
        language: 'Язык',
        author: 'Автор',
        createdTime: 'Время создания',
        lastModifiedTime: 'Время последнего изменения',
        url: 'URL',
        tag: 'Тег',
        description: 'Описание',
      },
      github: {
        repoName: 'Название репозитория',
        repoDesc: 'Описание репозитория',
        repoOwner: 'Владелец репозитория',
        fileName: 'Название файла',
        filePath: 'Путь к файлу',
        programmingLang: 'Язык программирования',
        url: 'URL',
        license: 'Лицензия',
        lastCommitTime: 'Время последнего коммита',
        lastCommitAuthor: 'Автор последнего коммита',
      },
      originInfo: {
        originalFilename: 'Исходное имя файла',
        originalFileSize: 'Исходный размер файла',
        uploadDate: 'Дата загрузки',
        lastUpdateDate: 'Дата последнего обновления',
        source: 'Источник',
      },
      technicalParameters: {
        segmentSpecification: 'Спецификация фрагментов',
        segmentLength: 'Длина фрагментов',
        avgParagraphLength: 'Средняя длина абзаца',
        paragraphs: 'Абзацы',
        hitCount: 'Количество обращений',
        embeddingTime: 'Время встраивания',
        embeddedSpend: 'Потрачено на встраивание',
      },
    },
    languageMap: {
      zh: 'Китайский',
      en: 'Английский',
      es: 'Испанский',
      fr: 'Французский',
      de: 'Немецкий',
      ja: 'Японский',
      ko: 'Корейский',
      ru: 'Русский',
      ar: 'Арабский',
      pt: 'Португальский',
      it: 'Итальянский',
      nl: 'Голландский',
      pl: 'Польский',
      sv: 'Шведский',
      tr: 'Турецкий',
      he: 'Иврит',
      hi: 'Хинди',
      da: 'Датский',
      fi: 'Финский',
      no: 'Норвежский',
      hu: 'Венгерский',
      el: 'Греческий',
      cs: 'Чешский',
      th: 'Тайский',
      id: 'Индонезийский',
    },
    categoryMap: {
      book: {
        fiction: 'Художественная литература',
        biography: 'Биография',
        history: 'История',
        science: 'Наука',
        technology: 'Технологии',
        education: 'Образование',
        philosophy: 'Философия',
        religion: 'Религия',
        socialSciences: 'Социальные науки',
        art: 'Искусство',
        travel: 'Путешествия',
        health: 'Здоровье',
        selfHelp: 'Самопомощь',
        businessEconomics: 'Бизнес/Экономика',
        cooking: 'Кулинария',
        childrenYoungAdults: 'Детская/Подростковая литература',
        comicsGraphicNovels: 'Комиксы/Графические романы',
        poetry: 'Поэзия',
        drama: 'Драматургия',
        other: 'Другое',
      },
      personalDoc: {
        notes: 'Заметки',
        blogDraft: 'Черновик блога',
        diary: 'Дневник',
        researchReport: 'Научный отчет',
        bookExcerpt: 'Отрывок из книги',
        schedule: 'Расписание',
        list: 'Список',
        projectOverview: 'Обзор проекта',
        photoCollection: 'Коллекция фотографий',
        creativeWriting: 'Творческое письмо',
        codeSnippet: 'Фрагмент кода',
        designDraft: 'Черновик дизайна',
        personalResume: 'Личное резюме',
        other: 'Другое',
      },
      businessDoc: {
        meetingMinutes: 'Протокол собрания',
        researchReport: 'Научный отчет',
        proposal: 'Предложение',
        employeeHandbook: 'Справочник сотрудника',
        trainingMaterials: 'Учебные материалы',
        requirementsDocument: 'Документ с требованиями',
        designDocument: 'Проектный документ',
        productSpecification: 'Спецификация продукта',
        financialReport: 'Финансовый отчет',
        marketAnalysis: 'Анализ рынка',
        projectPlan: 'План проекта',
        teamStructure: 'Структура команды',
        policiesProcedures: 'Политики и процедуры',
        contractsAgreements: 'Договоры и соглашения',
        emailCorrespondence: 'Переписка по электронной почте',
        other: 'Другое',
      },
    },
  },
  embedding: {
    processing: 'Расчет эмбеддингов...',
    paused: 'Расчет эмбеддингов приостановлен',
    completed: 'Встраивание завершено',
    error: 'Ошибка расчета эмбеддингов',
    docName: 'Предварительная обработка документа',
    mode: 'Правило сегментации',
    segmentLength: 'Длина фрагментов',
    textCleaning: 'Предварительная очистка текста',
    segments: 'Абзацы',
    highQuality: 'Режим высокого качества',
    economy: 'Экономичный режим',
    estimate: 'Оценочное потребление',
    stop: 'Остановить обработку',
    resume: 'Возобновить обработку',
    automatic: 'Автоматически',
    custom: 'Пользовательский',
    previewTip: 'Предварительный просмотр абзацев будет доступен после завершения расчета эмбеддингов',
    parentMaxTokens: 'Родитель',
    childMaxTokens: 'Ребёнок',
    hierarchical: 'Родитель-дочерний',
    pause: 'Пауза',
  },
  segment: {
    paragraphs: 'Абзацы',
    keywords: 'Ключевые слова',
    addKeyWord: 'Добавить ключевое слово',
    keywordError: 'Максимальная длина ключевого слова - 20',
    characters: 'символов',
    hitCount: 'Количество обращений',
    vectorHash: 'Векторный хэш: ',
    questionPlaceholder: 'добавьте вопрос здесь',
    questionEmpty: 'Вопрос не может быть пустым',
    answerPlaceholder: 'добавьте ответ здесь',
    answerEmpty: 'Ответ не может быть пустым',
    contentPlaceholder: 'добавьте содержимое здесь',
    contentEmpty: 'Содержимое не может быть пустым',
    newTextSegment: 'Новый текстовый сегмент',
    newQaSegment: 'Новый сегмент вопрос-ответ',
    delete: 'Удалить этот фрагмент?',
    chunks_other: 'КУСКИ',
    searchResults_one: 'РЕЗУЛЬТАТ',
    parentChunk: 'Родительский блок',
    characters_other: 'письмена',
    edited: 'ОТРЕДАКТИРОВАНЫ',
    regenerationSuccessMessage: 'Вы можете закрыть это окно.',
    searchResults_other: 'РЕЗУЛЬТАТЫ',
    regeneratingTitle: 'Регенерация дочерних блоков',
    parentChunks_one: 'РОДИТЕЛЬСКИЙ БЛОК',
    childChunk: 'Чайлд-Чанк',
    editedAt: 'Отредактировано в',
    editChildChunk: 'Редактирование дочернего фрагмента',
    parentChunks_other: 'РОДИТЕЛЬСКИЕ БЛОКИ',
    regenerationSuccessTitle: 'Регенерация завершена',
    childChunks_one: 'ДОЧЕРНИЙ ЧАНК',
    newChunk: 'Новый чанк',
    addAnother: 'Добавить еще один',
    clearFilter: 'Очистить фильтр',
    addChunk: 'Добавить чанк',
    editParentChunk: 'Редактирование родительского блока',
    chunkDetail: 'Деталь Чанка',
    regenerationConfirmMessage: 'При повторном создании дочерних блоков текущие дочерние блоки будут перезаписаны, включая отредактированные и вновь добавленные блоки. Регенерацию нельзя отменить.',
    collapseChunks: 'Сворачивание кусков',
    regenerationConfirmTitle: 'Вы хотите регенерировать дочерние куски?',
    searchResults_zero: 'РЕЗУЛЬТАТ',
    childChunks_other: 'ДЕТСКИЕ КУСОЧКИ',
    childChunkAdded: 'Добавлен 1 дочерний чанк',
    editChunk: 'Редактировать фрагмент',
    empty: 'Чанк не найден',
    chunks_one: 'ЛОМОТЬ',
    regeneratingMessage: 'Это может занять некоторое время, пожалуйста, подождите...',
    chunkAdded: 'Добавлен 1 блок',
    chunk: 'Ломоть',
    expandChunks: 'Развернуть чанки',
    characters_one: 'характер',
    addChildChunk: 'Добавить дочерний чанк',
    newChildChunk: 'Новый дочерний чанк',
  },
}

export default translation
