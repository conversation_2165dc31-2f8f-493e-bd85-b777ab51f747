const translation = {
  input: 'ВВОД',
  result: 'РЕЗУЛЬТАТ',
  detail: 'ДЕТАЛИ',
  tracing: 'ТРАССИРОВКА',
  resultPanel: {
    status: 'СТАТУС',
    time: 'ПРОШЕДШЕЕ ВРЕМЯ',
    tokens: 'ВСЕГО ТОКЕНОВ',
  },
  meta: {
    title: 'МЕТАДАННЫЕ',
    status: 'Статус',
    version: 'Версия',
    executor: 'Исполнитель',
    startTime: 'Время начала',
    time: 'Прошедшее время',
    tokens: 'Всего токенов',
    steps: 'Шаги выполнения',
  },
  resultEmpty: {
    title: 'Этот запуск выводит только формат JSON,',
    tipLeft: 'пожалуйста, перейдите на ',
    link: 'панель деталей',
    tipRight: ' чтобы просмотреть его.',
  },
  circularInvocationTip: 'В текущем рабочем процессе существует циклический вызов инструментов/узлов.',
  actionLogs: 'Журналы действий',
}

export default translation
