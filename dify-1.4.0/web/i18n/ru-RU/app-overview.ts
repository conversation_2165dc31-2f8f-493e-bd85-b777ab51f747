const translation = {
  welcome: {
    firstStepTip: 'Чтобы начать,',
    enterKeyTip: 'введите свой ключ API OpenAI ниже',
    getKeyTip: 'Получите свой ключ API на панели инструментов OpenAI',
    placeholder: 'Ваш ключ API OpenAI (например, sk-xxxx)',
  },
  apiKeyInfo: {
    cloud: {
      trial: {
        title: 'Вы используете пробную квоту {{providerName}}.',
        description: 'Пробная квота предоставляется для тестирования. Прежде чем пробная квота будет исчерпана, пожалуйста, настройте своего собственного поставщика модели или приобретите дополнительную квоту.',
      },
      exhausted: {
        title: 'Ваша пробная квота была исчерпана, пожалуйста, настройте свой APIKey.',
        description: 'Вы исчерпали свою пробную квоту. Пожалуйста, настройте своего собственного поставщика модели или приобретите дополнительную квоту.',
      },
    },
    selfHost: {
      title: {
        row1: 'Чтобы начать,',
        row2: 'сначала настройте своего поставщика модели.',
      },
    },
    callTimes: 'Количество вызовов',
    usedToken: 'Использованные токены',
    setAPIBtn: 'Перейти к настройке поставщика модели',
    tryCloud: 'Или попробуйте облачную версию Dify с бесплатной квотой',
  },
  overview: {
    title: 'Обзор',
    appInfo: {
      explanation: 'Готовое к использованию веб-приложение ИИ',
      accessibleAddress: 'Публичный URL',
      preview: 'Предварительный просмотр',
      regenerate: 'Перегенерировать',
      regenerateNotice: 'Вы хотите перегенерировать публичный URL?',
      preUseReminder: 'Пожалуйста, включите веб-приложение перед продолжением.',
      settings: {
        entry: 'Настройки',
        title: 'Настройки веб-приложения',
        webName: 'Название веб-приложения',
        webDesc: 'Описание веб-приложения',
        webDescTip: 'Этот текст будет отображаться на стороне клиента, предоставляя базовые инструкции по использованию приложения',
        webDescPlaceholder: 'Введите описание веб-приложения',
        language: 'Язык',
        workflow: {
          title: 'Рабочий процесс',
          subTitle: 'Подробности рабочего процесса',
          show: 'Показать',
          hide: 'Скрыть',
          showDesc: 'Показать или скрыть подробности рабочего процесса в веб-приложении',
        },
        chatColorTheme: 'Цветовая тема чата',
        chatColorThemeDesc: 'Установите цветовую тему чат-бота',
        chatColorThemeInverted: 'Инвертированные цвета',
        invalidHexMessage: 'Неверное HEX-значение',
        invalidPrivacyPolicy: 'Недопустимая ссылка на политику конфиденциальности. Пожалуйста, используйте действительную ссылку, начинающуюся с http или https',
        sso: {
          label: 'SSO аутентификация',
          title: 'WebApp SSO',
          description: 'Все пользователи должны войти в систему с помощью SSO перед использованием WebApp',
          tooltip: 'Обратитесь к администратору, чтобы включить WebApp SSO',
        },
        more: {
          entry: 'Показать больше настроек',
          copyright: 'Авторские права',
          copyRightPlaceholder: 'Введите имя автора или организации',
          privacyPolicy: 'Политика конфиденциальности',
          privacyPolicyPlaceholder: 'Введите ссылку на политику конфиденциальности',
          privacyPolicyTip: 'Помогает посетителям понять, какие данные собирает приложение, см. <privacyPolicyLink>Политику конфиденциальности</privacyPolicyLink> Dify.',
          customDisclaimer: 'Пользовательский отказ от ответственности',
          customDisclaimerPlaceholder: 'Введите текст пользовательского отказа от ответственности',
          customDisclaimerTip: 'Текст пользовательского отказа от ответственности будет отображаться на стороне клиента, предоставляя дополнительную информацию о приложении',
          copyrightTooltip: 'Пожалуйста, перейдите на тарифный план Professional или выше',
          copyrightTip: 'Отображение информации об авторских правах в веб-приложении',
        },
        modalTip: 'Настройки веб-приложения на стороне клиента.',
      },
      embedded: {
        entry: 'Встраивание',
        title: 'Встроить на веб-сайт',
        explanation: 'Выберите способ встраивания чат-приложения на свой веб-сайт',
        iframe: 'Чтобы добавить чат-приложение в любое место на вашем веб-сайте, добавьте этот iframe в свой HTML-код.',
        scripts: 'Чтобы добавить чат-приложение в правый нижний угол вашего веб-сайта, добавьте этот код в свой HTML.',
        chromePlugin: 'Установите расширение Dify Chatbot для Chrome',
        copied: 'Скопировано',
        copy: 'Копировать',
      },
      qrcode: {
        title: 'QR-код ссылки',
        scan: 'Сканировать, чтобы поделиться',
        download: 'Скачать QR-код',
      },
      customize: {
        way: 'способ',
        entry: 'Настроить',
        title: 'Настроить веб-приложение ИИ',
        explanation: 'Вы можете настроить внешний интерфейс веб-приложения в соответствии со своими потребностями.',
        way1: {
          name: 'Создайте форк клиентского кода, измените его и разверните на Vercel (рекомендуется)',
          step1: 'Создайте форк клиентского кода и измените его',
          step1Tip: 'Нажмите здесь, чтобы создать форк исходного кода в своей учетной записи GitHub и изменить код',
          step1Operation: 'Dify-WebClient',
          step2: 'Развернуть на Vercel',
          step2Tip: 'Нажмите здесь, чтобы импортировать репозиторий в Vercel и развернуть',
          step2Operation: 'Импортировать репозиторий',
          step3: 'Настроить переменные среды',
          step3Tip: 'Добавьте следующие переменные среды в Vercel',
        },
        way2: {
          name: 'Напишите клиентский код для вызова API и разверните его на сервере',
          operation: 'Документация',
        },
      },
      launch: 'Баркас',
    },
    apiInfo: {
      title: 'API серверной части',
      explanation: 'Легко интегрируется в ваше приложение',
      accessibleAddress: 'Конечная точка API сервиса',
      doc: 'Справочник по API',
    },
    status: {
      running: 'В работе',
      disable: 'Отключено',
    },
  },
  analysis: {
    title: 'Анализ',
    ms: 'мс',
    tokenPS: 'Токен/с',
    totalMessages: {
      title: 'Всего сообщений',
      explanation: 'Ежедневное количество взаимодействий с ИИ.',
    },
    totalConversations: {
      title: 'Всего чатов',
      explanation: 'Ежедневное количество чатов с LLM; проектирование/отладка не учитываются.',
    },
    activeUsers: {
      title: 'Активные пользователи',
      explanation: 'Уникальные пользователи, участвующие в вопросах и ответах с LLM; проектирование/отладка не учитываются.',
    },
    tokenUsage: {
      title: 'Использование токенов',
      explanation: 'Отражает ежедневное использование токенов языковой модели для приложения, полезно для целей контроля затрат.',
      consumed: 'Потрачено',
    },
    avgSessionInteractions: {
      title: 'Среднее количество взаимодействий за сеанс',
      explanation: 'Количество непрерывных взаимодействий пользователя с LLM; для приложений на основе чатов.',
    },
    avgUserInteractions: {
      title: 'Среднее количество взаимодействий пользователя',
      explanation: 'Отражает ежедневную частоту использования пользователями. Эта метрика отражает активность пользователей.',
    },
    userSatisfactionRate: {
      title: 'Уровень удовлетворенности пользователей',
      explanation: 'Количество лайков на 1000 сообщений. Это указывает на долю ответов, которыми пользователи довольны.',
    },
    avgResponseTime: {
      title: 'Среднее время ответа',
      explanation: 'Время (мс) для обработки/ответа LLM; для текстовых приложений.',
    },
    tps: {
      title: 'Скорость вывода токенов',
      explanation: 'Измерьте производительность LLM. Подсчитайте скорость вывода токенов LLM от начала запроса до завершения вывода.',
    },
  },
}

export default translation
