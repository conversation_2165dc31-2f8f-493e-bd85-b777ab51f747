const translation = {
  title: 'Testowanie odzyskiwania',
  desc: 'Przetestuj efekt uderzenia wiedzy na podstawie podanego tekstu zapytania.',
  dateTimeFormat: 'MM/DD/YYYY hh:mm A',
  recents: 'Ostatnie',
  table: {
    header: {
      source: 'Źródło',
      text: 'Tekst',
      time: '<PERSON><PERSON>',
    },
  },
  input: {
    title: 'Tekst źródłowy',
    placeholder: '<PERSON><PERSON>ę wpisać tekst, zaleca się krótkie zdanie deklaratywne.',
    countWarning: 'Do 200 znaków.',
    indexWarning: 'Tylko wiedza wysokiej jakości.',
    testing: 'Testowanie',
  },
  hit: {
    title: 'AKAPITY ODZYSKIWANIA',
    emptyTip: 'Wyniki testowania odzyskiwania będą tu pokazane',
  },
  noRecentTip: 'Brak ostatnich wyników zapytań tutaj',
  viewChart: '<PERSON>ob<PERSON>z WYKRES WEKTOROWY',
  settingTitle: 'Ustawienie pobierania',
  viewDetail: '<PERSON><PERSON>ż szczegóły',
  keyword: '<PERSON><PERSON>owa kluczowe',
  hitChunks: 'Trafienie w {{num}} fragmentów podrzędnych',
  open: 'Otwierać',
  records: 'Rekordy',
  chunkDetail: 'Szczegóły kawałka',
}

export default translation
