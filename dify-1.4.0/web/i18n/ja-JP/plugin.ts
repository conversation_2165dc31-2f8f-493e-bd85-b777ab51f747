const translation = {
  category: {
    extensions: '拡張機能',
    all: 'すべて',
    tools: '道具',
    bundles: 'バンドル',
    agents: 'エージェント戦略',
    models: 'モデル',
  },
  categorySingle: {
    agent: 'エージェント戦略',
    model: 'モデル',
    bundle: 'バンドル',
    tool: '道具',
    extension: '拡張',
  },
  list: {
    source: {
      local: 'ローカルパッケージファイルからインストール',
      github: 'GitHubからインストールする',
      marketplace: 'マーケットプレイスからインストール',
    },
    noInstalled: 'プラグインはインストールされていません',
    notFound: 'プラグインが見つかりません',
  },
  source: {
    github: 'GitHub',
    local: 'ローカルパッケージファイル',
    marketplace: 'マーケットプレイス',
  },
  detailPanel: {
    categoryTip: {
      marketplace: 'マーケットプレイスからインストールされました',
      local: 'ローカルプラグイン',
      debugging: 'デバッグプラグイン',
      github: 'Githubからインストールしました',
    },
    operation: {
      info: 'プラグイン情報',
      install: 'インストール',
      viewDetail: '詳細を見る',
      checkUpdate: '更新を確認する',
      update: '更新',
      detail: '詳細',
      remove: '削除',
    },
    toolSelector: {
      descriptionPlaceholder: 'ツールの目的の簡単な説明、例えば、特定の場所の温度を取得すること。',
      paramsTip2: '「自動」がオフのとき、デフォルト値が使用されます。',
      settings: 'ユーザー設定',
      unsupportedContent2: 'バージョンを切り替えるにはクリックしてください。',
      unsupportedContent: 'インストールされたプラグインのバージョンは、このアクションを提供していません。',
      title: 'ツールを追加',
      uninstalledContent: 'このプラグインはローカル/GitHubリポジトリからインストールされます。インストール後にご利用ください。',
      descriptionLabel: 'ツールの説明',
      auto: '自動',
      params: '推論設定',
      uninstalledLink: 'プラグインを管理する',
      placeholder: 'ツールを選択...',
      uninstalledTitle: 'ツールがインストールされていません',
      empty: 'ツールを追加するには「+」ボタンをクリックしてください。複数のツールを追加できます。',
      paramsTip1: 'LLM推論パラメータを制御します。',
      toolLabel: '道具',
      unsupportedTitle: 'サポートされていないアクション',
    },
    endpointDisableTip: 'エンドポイントを無効にする',
    endpointModalDesc: '設定が完了すると、APIエンドポイントを介してプラグインが提供する機能を使用できます。',
    endpointDisableContent: '{{name}}を無効にしますか？',
    endpointModalTitle: 'エンドポイントを設定する',
    endpointDeleteTip: 'エンドポイントを削除',
    modelNum: '{{num}} モデルが含まれています',
    serviceOk: 'サービスは正常です',
    disabled: 'サービスは無効化されています',
    endpoints: 'エンドポイント',
    endpointsTip: 'このプラグインはエンドポイントを介して特定の機能を提供し、現在のワークスペースのために複数のエンドポイントセットを構成できます。',
    configureModel: 'モデルを設定する',
    configureTool: 'ツールを設定する',
    endpointsEmpty: 'エンドポイントを追加するには、\'+\'ボタンをクリックしてください',
    strategyNum: '{{num}} {{strategy}} が含まれています',
    configureApp: 'アプリを設定する',
    endpointDeleteContent: '{{name}}を削除しますか？',
    actionNum: '{{num}} {{action}} が含まれています',
    endpointsDocLink: 'ドキュメントを表示する',
    switchVersion: 'バージョンの切り替え',
  },
  debugInfo: {
    title: 'デバッグ',
    viewDocs: 'ドキュメントを見る',
  },
  privilege: {
    admins: '管理者',
    noone: '誰もいない',
    whoCanInstall: '誰がプラグインをインストールして管理できますか？',
    whoCanDebug: '誰がプラグインのデバッグを行うことができますか？',
    everyone: 'みんな',
    title: 'プラグインの設定',
  },
  pluginInfoModal: {
    packageName: 'パッケージ',
    release: 'リリース',
    title: 'プラグイン情報',
    repository: 'リポジトリ',
  },
  action: {
    deleteContentRight: 'プラグイン？',
    usedInApps: 'このプラグインは{{num}}のアプリで使用されています。',
    delete: 'プラグインを削除する',
    pluginInfo: 'プラグイン情報',
    deleteContentLeft: '削除しますか',
    checkForUpdates: '更新を確認する',
  },
  installModal: {
    labels: {
      version: 'バージョン',
      package: 'パッケージ',
      repository: 'リポジトリ',
    },
    cancel: 'キャンセル',
    installing: 'インストール中...',
    installedSuccessfully: 'インストールに成功しました',
    installFailedDesc: 'プラグインのインストールに失敗しました。',
    fromTrustSource: '信頼できるソースからのみプラグインをインストールするようにしてください。',
    installedSuccessfullyDesc: 'プラグインは正常にインストールされました。',
    installFailed: 'インストールに失敗しました',
    readyToInstallPackage: '次のプラグインをインストールしようとしています',
    uploadFailed: 'アップロードに失敗しました',
    pluginLoadErrorDesc: 'このプラグインはインストールされません',
    installComplete: 'インストール完了',
    next: '次',
    readyToInstall: '次のプラグインをインストールしようとしています',
    pluginLoadError: 'プラグインの読み込みエラー',
    readyToInstallPackages: '次の{{num}}プラグインをインストールしようとしています',
    close: '閉じる',
    install: 'インストール',
    dropPluginToInstall: 'プラグインパッケージをここにドロップしてインストールします',
    installPlugin: 'プラグインをインストールする',
    back: '戻る',
    uploadingPackage: '{{packageName}}をアップロード中...',
  },
  installFromGitHub: {
    installedSuccessfully: 'インストールに成功しました',
    installNote: '信頼できるソースからのみプラグインをインストールするようにしてください。',
    updatePlugin: 'GitHubからプラグインを更新する',
    selectPackage: 'パッケージを選択',
    installFailed: 'インストールに失敗しました',
    selectPackagePlaceholder: 'パッケージを選択してください',
    gitHubRepo: 'GitHubリポジトリ',
    selectVersionPlaceholder: 'バージョンを選択してください',
    uploadFailed: 'アップロードに失敗しました',
    selectVersion: 'バージョンを選択',
    installPlugin: 'GitHubからプラグインをインストールする',
  },
  upgrade: {
    title: 'プラグインをインストールする',
    close: '閉じる',
    upgrading: 'インストール中...',
    description: '次のプラグインをインストールしようとしています',
    successfulTitle: 'インストールに成功しました',
    usedInApps: '{{num}}のアプリで使用されています',
    upgrade: 'インストール',
  },
  error: {
    fetchReleasesError: 'リリースを取得できません。後でもう一度お試しください。',
    inValidGitHubUrl: '無効なGitHub URLです。有効なURLを次の形式で入力してください: https://github.com/owner/repo',
    noReleasesFound: 'リリースは見つかりません。GitHubリポジトリまたは入力URLを確認してください。',
  },
  marketplace: {
    empower: 'AI開発をサポートする',
    discover: '探索',
    and: 'と',
    difyMarketplace: 'Difyマーケットプレイス',
    moreFrom: 'マーケットプレイスからのさらなる情報',
    noPluginFound: 'プラグインが見つかりません',
    pluginsResult: '{{num}} 件の結果',
    sortBy: '並べ替え',
    sortOption: {
      mostPopular: '人気順',
      recentlyUpdated: '最近更新順',
      newlyReleased: '新着順',
      firstReleased: 'リリース順',
    },
    viewMore: 'もっと見る',
    verifiedTip: 'このプラグインはDifyによって認証されています',
    partnerTip: 'このプラグインはDifyのパートナーによって認証されています',
  },
  task: {
    installError: '{{errorLength}} プラグインのインストールに失敗しました。表示するにはクリックしてください。',
    installingWithSuccess: '{{installingLength}}個のプラグインをインストール中、{{successLength}}個成功しました。',
    clearAll: 'すべてクリア',
    installedError: '{{errorLength}} プラグインのインストールに失敗しました',
    installingWithError: '{{installingLength}}個のプラグインをインストール中、{{successLength}}件成功、{{errorLength}}件失敗',
    installing: '{{installingLength}}個のプラグインをインストール中、0個完了。',
  },
  from: 'インストール元',
  install: '{{num}} インストール',
  installAction: 'インストール',
  installFrom: 'インストール元',
  searchPlugins: '検索プラグイン',
  search: '検索',
  endpointsEnabled: '{{num}} セットのエンドポイントが有効になりました',
  findMoreInMarketplace: 'マーケットプレイスでさらに見つけてください',
  fromMarketplace: 'マーケットプレイスから',
  searchCategories: '検索カテゴリ',
  allCategories: 'すべてのカテゴリ',
  searchTools: '検索ツール...',
  installPlugin: 'プラグインをインストールする',
  searchInMarketplace: 'マーケットプレイスで検索',
  submitPlugin: 'プラグインを提出する',
  difyVersionNotCompatible: '現在のDifyバージョンはこのプラグインと互換性がありません。最小バージョンは{{minimalDifyVersion}}です。',
  metadata: {
    title: 'プラグイン',
  },
}

export default translation
