import {
  type QueryKey,
  useQueryClient,
} from '@tanstack/react-query'

export const useInvalid = (key: Query<PERSON><PERSON>) => {
  const queryClient = useQueryClient()
  return () => {
    queryClient.invalidateQueries(
      {
        queryKey: key,
      },
    )
  }
}

export const useReset = (key: Query<PERSON>ey) => {
  const queryClient = useQueryClient()
  return () => {
    queryClient.resetQueries(
      {
        queryKey: key,
      },
    )
  }
}
