.creationInfo {
  padding-top: 42px;
}
.creationInfo .title {
  @apply mb-2;
  font-weight: 500;
  font-size: 20px;
  line-height: 30px;
  color: #101828;
}
.creationInfo .content {
  margin-bottom: 44px;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #667085;
}
.creationInfo .label {
  @apply mb-2;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #101828;
}
.datasetName {
  padding: 8px 12px;
  background: #F9FAFB;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #101828;
  word-break: break-all;
}

.dividerLine {
  margin: 24px 0;
  height: 1px;
  background-color: #eaecf0;
}

.sideTip {
  @apply flex flex-col items-center shrink-0 ;
  padding-top: 108px;
  width: 524px;
  border-left: 0.5px solid #F2F4F7;
}
.tipCard {
  @apply flex flex-col items-start p-6;
  width: 320px;
  background-color: #F9FAFB;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  border-radius: 12px;
}
.tipCard .icon {
  width: 32px;
  height: 32px;
  border: 1px solid #EAECF0;
  border-radius: 6px;
  background: center no-repeat url(../assets/book-open-01.svg);
  background-size: 16px;
}
.tipCard .title {
  margin: 12px 0;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #344054;
}
.tipCard .content {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #344054;
}
