import type { CrawlResultItem } from '@/models/datasets'

const result: CrawlResultItem[] = [
  {
    title: 'Start the frontend Docker container separately',
    markdown: 'Markdown 1',
    description: 'Description 1',
    source_url: 'https://example.com/1',
  },
  {
    title: 'Advanced Tool Integration',
    markdown: 'Markdown 2',
    description: 'Description 2',
    source_url: 'https://example.com/2',
  },
  {
    title: 'Local Source Code Start | English | Dify',
    markdown: 'Markdown 3',
    description: 'Description 3',
    source_url: 'https://example.com/3',
  },
]

export default result
