# 貢献ガイド

Difyに貢献しようとお考えですか？素晴らしいですね。私たちは、あなたがどのような貢献をしてくださるのか、とても楽しみにしています。スタートアップとして限られた人員と資金の中で、LLMアプリケーションの構築と管理のための最も直感的なワークフローを設計するという大きな目標を持っています。コミュニティからのあらゆる支援が、本当に重要な意味を持ちます。

私たちは迅速に開発を進める必要がありますが、同時に貢献者の皆様にとってスムーズな経験を提供したいと考えています。このガイドは、コードベースと私たちの貢献者との協働方法を理解していただき、すぐに楽しい開発に取り掛かれるようにすることを目的としています。

このガイドは、Dify自体と同様に、常に進化し続けています。実際のプロジェクトの進行状況と多少のずれが生じる場合もございますが、ご理解いただけますと幸いです。改善のためのフィードバックも歓迎いたします。

ライセンスについては、[ライセンスと貢献者同意書](./LICENSE)をご一読ください。また、コミュニティは[行動規範](https://github.com/langgenius/.github/blob/main/CODE_OF_CONDUCT.md)に従っています。

## 始める前に

取り組むべき課題をお探しですか？[初心者向けの課題](https://github.com/langgenius/dify/issues?q=is%3Aissue%20state%3Aopen%20label%3A%22good%20first%20issue%22)から選んで始めてみましょう！

新しいモデルランタイムやツールを追加したいですか？[プラグインリポジトリ](https://github.com/langgenius/dify-plugins)でPRを作成し、あなたの成果を見せてください。

既存のモデルランタイムやツールの更新、バグ修正をしたいですか？[公式プラグインリポジトリ](https://github.com/langgenius/dify-official-plugins)で作業を進めてください。

参加して、貢献して、一緒に素晴らしいものを作りましょう！💡✨

PRの説明には、既存のイシューへのリンクを含めるか、新しいイシューを作成することを忘れないでください。

### バグ報告

> [!IMPORTANT]
> バグ報告時には、以下の情報を必ず含めてください：

- 明確で分かりやすいタイトル
- エラーメッセージを含む詳細なバグの説明
- バグの再現手順
- 期待される動作
- バックエンドの問題の場合は**ログ**（docker-composeのログで確認可能）が非常に重要です
- 該当する場合はスクリーンショットや動画

優先順位の付け方：

  | 問題の種類                                                    | 優先度    |
  | ------------------------------------------------------------ | --------- |
  | コア機能のバグ（クラウドサービス、ログイン不可、アプリケーション不具合、セキュリティ脆弱性） | 最重要    |
  | 重要度の低いバグ、パフォーマンス改善                         | 中程度    |
  | 軽微な修正（タイプミス、分かりにくいが動作するUI）           | 低       |

### 機能リクエスト

> [!NOTE]
> 機能リクエスト時には、以下の情報を必ず含めてください：

- 明確で分かりやすいタイトル
- 機能の詳細な説明
- 使用事例
- その他の文脈や画面のスクリーンショット

優先順位の付け方：

  | 機能の種類                                                    | 優先度    |
  | ------------------------------------------------------------ | --------- |
  | チームメンバーによって高優先度とラベル付けされた機能         | 高       |
  | [コミュニティフィードボード](https://github.com/langgenius/dify/discussions/categories/feedbacks)での人気の機能リクエスト | 中程度    |
  | 非コア機能と軽微な改善                                       | 低       |
  | 価値はあるが緊急性の低いもの                                 | 将来対応  |

## PRの提出

### プルリクエストのプロセス

1. リポジトリをフォークする
2. PRを作成する前に、変更内容についてイシューで議論する
3. 変更用の新しいブランチを作成する
4. 変更に応じたテストを追加する
5. 既存のテストをパスすることを確認する
6. PRの説明文にイシューをリンクする（`fixes #<issue_number>`）
7. マージ完了！

### プロジェクトのセットアップ

#### フロントエンド

フロントエンドサービスのセットアップについては、`web/README.md`の[ガイド](https://github.com/langgenius/dify/blob/main/web/README.md)を参照してください。このドキュメントには、フロントエンド環境を適切にセットアップするための詳細な手順が記載されています。

#### バックエンド

バックエンドサービスのセットアップについては、`api/README.md`の[手順](https://github.com/langgenius/dify/blob/main/api/README.md)を参照してください。このドキュメントには、バックエンドを正しく動作させるためのステップバイステップのガイドが含まれています。

#### その他の注意点

セットアップを進める前に、以下の重要な情報が含まれているため、このドキュメントを注意深く確認することをお勧めします：
- 前提条件と依存関係
- インストール手順
- 設定の詳細
- 一般的なトラブルシューティングのヒント

セットアップ中に問題が発生した場合は、お気軽にお問い合わせください。

## サポートを受ける

貢献中に行き詰まったり、緊急の質問がある場合は、関連するGitHubイシューで質問するか、[Discord](https://discord.gg/8Tpq4AcN9c)で気軽にチャットしてください。

