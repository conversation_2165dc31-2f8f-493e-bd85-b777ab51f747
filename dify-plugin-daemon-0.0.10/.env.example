SERVER_PORT=5002
SERVER_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
GIN_MODE=release
PLATFORM=local

DIFY_INNER_API_KEY="QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1"
DIFY_INNER_API_URL=http://127.0.0.1:5001

PLUGIN_REMOTE_INSTALLING_ENABLED=true
PLUGIN_REMOTE_INSTALLING_HOST=127.0.0.1
PLUGIN_REMOTE_INSTALLING_PORT=5003

# s3 credentials
S3_USE_AWS_MANAGED_IAM=true
S3_ENDPOINT=
S3_USE_PATH_STYLE=true
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
AWS_REGION=

# tencent cos credentials
TENCENT_COS_SECRET_KEY=
TENCENT_COS_SECRET_ID=
TENCENT_COS_REGION=

# aliyun oss credentials
ALIYUN_OSS_REGION=
ALIYUN_OSS_ENDPOINT=
ALIYUN_OSS_ACCESS_KEY_ID=
ALIYUN_OSS_ACCESS_KEY_SECRET=
ALIYUN_OSS_AUTH_VERSION=v4
ALIYUN_OSS_PATH=

# azure blob storage credentials
AZURE_BLOB_STORAGE_CONTAINER_NAME=
AZURE_BLOB_STORAGE_CONNECTION_STRING=

# services storage
PLUGIN_STORAGE_TYPE=local
PLUGIN_STORAGE_OSS_BUCKET=
PLUGIN_STORAGE_LOCAL_ROOT=./storage

# where the plugin finally installed
PLUGIN_INSTALLED_PATH=plugin

# where the plugin finally running and working
PLUGIN_WORKING_PATH=cwd

# persistence storage
PERSISTENCE_STORAGE_PATH=persistence
PERSISTENCE_STORAGE_MAX_SIZE=104857600

# plugin webhook
PLUGIN_WEBHOOK_ENABLED=true

# routine pool
ROUTINE_POOL_SIZE=1024

# redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=difyai123456
REDIS_DB=0

DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=dify_plugin
# Specifies the SSL mode for the database connection. 
# Possible values include 'disable', 'require', 'verify-ca', and 'verify-full'. 
# 'disable' means SSL is not used for the connection.
DB_SSL_MODE=disable
# database connection pool settings
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=30
DB_CONN_MAX_LIFETIME=3600

DIFY_INVOCATION_CONNECTION_IDLE_TIMEOUT=120

MAX_PLUGIN_PACKAGE_SIZE=52428800

# dify serverless connector
DIFY_PLUGIN_SERVERLESS_CONNECTOR_URL=http://127.0.0.1:5004
DIFY_PLUGIN_SERVERLESS_CONNECTOR_API_KEY=HeRFb6yrzAy5vUSlJWK2lUl36mpkaRycv4witbQpucXacgXg7G9a8gVL

# python interpreter, if you are using local runtime, you should set this path to your python interpreter path
# otherwise, it should be /usr/bin/python3
# PYTHON_INTERPRETER_PATH=/usr/bin/python3

# uv path, if you are using local runtime, you should set this path to your local uv path
# otherwise, it will use `from uv._find_uv import find_uv_bin; print(find_uv_bin())`
# UV_PATH=

# python environment init timeout, if the python environment init process is not finished within this time, it will be killed
PYTHON_ENV_INIT_TIMEOUT=120

# pprof enabled, for debugging
PPROF_ENABLED=false

# FORCE_VERIFYING_SIGNATURE, for security, you should set this to true, pls be sure you know what you are doing
# if want to install plugin without verifying signature, set this to false
FORCE_VERIFYING_SIGNATURE=true

# Enable or disable third-party signature verification for plugins
# Set to "true" to allow verification using additional public keys specified in THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS
THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED=false
# A comma-separated list of file paths to public keys in addition to the official public key for signature verification
THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS=

# proxy settings, example: HTTP_PROXY=http://host.docker.internal:7890
HTTP_PROXY=
HTTPS_PROXY=

# plugin stdio buffer size
PLUGIN_STDIO_BUFFER_SIZE=1024
PLUGIN_STDIO_MAX_BUFFER_SIZE=5242880

# dify backwards invocation write timeout in milliseconds
DIFY_BACKWARDS_INVOCATION_WRITE_TIMEOUT=5000
# dify backwards invocation read timeout in milliseconds
DIFY_BACKWARDS_INVOCATION_READ_TIMEOUT=240000
