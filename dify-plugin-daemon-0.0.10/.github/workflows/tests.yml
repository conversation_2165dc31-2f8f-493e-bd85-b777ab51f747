name: Run Tests

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

concurrency:
  group: test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
  

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: -e REDIS_PASSWORD=difyai123456

      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: difyai123456
          POSTGRES_DB: difyai
        ports:
          - 5432:5432

      mysql:
        image: mysql:8.0
        env:
          MYSQL_DATABASE: difyai
          MYSQL_ROOT_PASSWORD: difyai123456
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping --silent"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=30

    steps:
      - uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'

      # setup uv and get uv path to env
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "UV_PATH=$(which uv)" >> $GITHUB_ENV

      - name: Setup License
        run: go run cmd/license/generate/main.go

      - name: Install dependencies
        run: go mod download

      - name: Run tests
        run: go test -v -timeout 1m ./...
