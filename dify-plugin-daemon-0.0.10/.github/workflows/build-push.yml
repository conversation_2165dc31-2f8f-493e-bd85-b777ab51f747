name: <PERSON><PERSON> and <PERSON>ush Daemon

on:
  push:
    branches:
      - "main"
      - "deploy/dev"
  pull_request:
    branches:
      - "main"
  release:
    types: [published]

concurrency:
  group: build-push-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  DOCKERHUB_USER: ${{ secrets.DOCKERHUB_USER }}
  DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
  DIFY_DAEMON_IMAGE_NAME: ${{ vars.DIFY_DAEMON_IMAGE_NAME || 'langgenius/dify-plugin-daemon' }}

jobs:
  matrix_prepare:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set matrix
        id: set-matrix
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            # Skip arm64 builds on PR
            echo "matrix={\"include\":[{\"service_name\":\"build-serverless-daemon-amd64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/amd64\",\"scope\":\"serverless\"},{\"service_name\":\"build-local-daemon-amd64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/amd64\",\"scope\":\"local\"}]}" >> $GITHUB_OUTPUT
          else
            # Include all builds for other events
            echo "matrix={\"include\":[{\"service_name\":\"build-serverless-daemon-amd64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/amd64\",\"scope\":\"serverless\"},{\"service_name\":\"build-serverless-daemon-arm64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/arm64\",\"scope\":\"serverless\"},{\"service_name\":\"build-local-daemon-amd64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/amd64\",\"scope\":\"local\"},{\"service_name\":\"build-local-daemon-arm64\",\"image_name_env\":\"DIFY_DAEMON_IMAGE_NAME\",\"platform\":\"linux/arm64\",\"scope\":\"local\"}]}" >> $GITHUB_OUTPUT
          fi

  build:
    needs: matrix_prepare
    runs-on: ${{ matrix.platform == 'linux/arm64' && 'arm64_runner' || 'ubuntu-latest' }}
    if: github.repository == 'langgenius/dify-plugin-daemon'
    strategy:
      matrix: ${{ fromJson(needs.matrix_prepare.outputs.matrix) }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
          echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        if: github.event_name != 'pull_request'
        with:
          username: ${{ env.DOCKERHUB_USER }}
          password: ${{ env.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DIFY_DAEMON_IMAGE_NAME }}
          tags: |
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=ref,event=branch
            type=sha,enable=true,priority=100,prefix=,suffix=,format=long
            type=raw,value=${{ github.ref_name }},enable=${{ startsWith(github.ref, 'refs/tags/') }}

      - name: Run Build Docker Image
        run: docker build --build-arg PLATFORM=${{ matrix.scope }} --build-arg VERSION=${{ github.sha }} -t dify-plugin-daemon -f ./docker/${{ matrix.scope }}.dockerfile .

      - name: Tag Docker Images
        if: github.event_name != 'pull_request'
        run: for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n');
          do
          docker tag dify-plugin-daemon "$tag-${{ matrix.scope }}-${{ env.PLATFORM_PAIR }}";
          done
      - name: Push Docker Image
        if: github.event_name != 'pull_request'
        run: for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n');
          do
          docker push $tag-${{ matrix.scope }}-${{ env.PLATFORM_PAIR }};
          done

  create-manifest:
    if: github.event_name != 'pull_request'
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        scope: [serverless, local]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKERHUB_USER }}
          password: ${{ env.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ vars.DIFY_DAEMON_IMAGE_NAME || 'langgenius/dify-plugin-daemon' }}
          tags: |
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=ref,event=branch
            type=sha,enable=true,priority=100,prefix=,suffix=,format=long
            type=raw,value=${{ github.ref_name }},enable=${{ startsWith(github.ref, 'refs/tags/') }}

      - name: Build Universal Docker Images
        run: for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n');
          do
          docker manifest create $tag-${{ matrix.scope }} $tag-${{ matrix.scope }}-linux-amd64 $tag-${{ matrix.scope }}-linux-arm64;
          docker manifest push $tag-${{ matrix.scope }};
          done
