identity:
  name: {{ .PluginName }}
  author: {{ .Author }}
  label:
    en_US: {{ .PluginName | SnakeToCamel }}
description:
  en_US: {{ .PluginName | SnakeToCamel }}
parameters:
  - name: model
    type: model-selector
    scope: tool-call&llm
    required: true
    label:
      en_US: Model
      zh_Hans: 模型
      pt_BR: Model
  - name: tools
    type: array[tools]
    required: true
    label:
      en_US: Tools list
      zh_Hans: 工具列表
      pt_BR: Tools list
extra:
  python:
    source: strategies/{{ .PluginName }}.py
