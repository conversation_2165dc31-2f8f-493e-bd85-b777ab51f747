identity:
  name: {{ .PluginName }}
  author: {{ .Author }}
  label:
    en_US: {{ .PluginName }}
    zh_Hans: {{ .PluginName }}
    pt_BR: {{ .PluginName }}
description:
  human:
    en_US: {{ .PluginDescription }}
    zh_Hans: {{ .PluginDescription }}
    pt_BR: {{ .PluginDescription }}
  llm: {{ .PluginDescription }}
parameters:
  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
      pt_BR: Query string
    human_description:
      en_US: {{ .PluginDescription }}
      zh_Hans: {{ .PluginDescription }}
      pt_BR: {{ .PluginDescription }}
    llm_description: {{ .PluginDescription }}
    form: llm
extra:
  python:
    source: tools/{{ .PluginName }}.py
