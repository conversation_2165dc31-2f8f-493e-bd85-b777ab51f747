provider: {{ .PluginName }}
label:
  en_US: {{ .PluginName | SnakeToCamel }}
description:
  en_US: Models provided by {{ .PluginName }}.
  zh_Hans: {{ .PluginName | SnakeToCamel }} 提供的模型。
icon_small:
  en_US: icon_s_en.svg
icon_large:
  en_US: icon_l_en.svg
background: "#E5E7EB"
help:
  title:
    en_US: Get your API Key from {{ .PluginName }}
    zh_Hans: 从 {{ .PluginName | SnakeToCamel }} 获取 API Key
  url:
    en_US: https://__put_your_url_here__/account/api-keys
supported_model_types:
{{- range .SupportedModelTypes }}
  - {{ . }}
{{- end }}
configurate_methods:
  - predefined-model
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
  credential_form_schemas:
    - variable: openai_api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
provider_credential_schema:
  credential_form_schemas:
    - variable: openai_api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
models:
{{- if HasSubstring "llm" .SupportedModelTypes }}
  llm:
    predefined:
      - "models/llm/*.yaml"
{{- end }}
{{- if HasSubstring "text_embedding" .SupportedModelTypes }}
  text_embedding:
    predefined:
      - "models/text_embedding/*.yaml"
{{- end }}
{{- if HasSubstring "tts" .SupportedModelTypes }}
  tts:
    predefined:
      - "models/tts/*.yaml"
{{- end }}
{{- if HasSubstring "speech2text" .SupportedModelTypes }}
  speech2text:
    predefined:
      - "models/speech2text/*.yaml"
{{- end }}
{{- if HasSubstring "moderation" .SupportedModelTypes }}
  moderation:
    predefined:
      - "models/moderation/*.yaml"
{{- end }}
extra:
  python:
    provider_source: provider/openai.py
    model_sources:
{{- if HasSubstring "llm" .SupportedModelTypes }}
      - "models/llm/llm.py"
{{- end }}
{{- if HasSubstring "text-embedding" .SupportedModelTypes }}
      - "models/text_embedding/text_embedding.py"
{{- end }}
{{- if HasSubstring "speech2text" .SupportedModelTypes }}
      - "models/speech2text/speech2text.py"
{{- end }}
{{- if HasSubstring "moderation" .SupportedModelTypes }}
      - "models/moderation/moderation.py"
{{- end }}
{{- if HasSubstring "tts" .SupportedModelTypes }}
      - "models/tts/tts.py"
{{- end }}
