import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
import time
from typing import Tuple

import requests

from config import conf

def send_email(receivers: list, subject: str, text: str, cc: list = [], retry_count: int = 2) -> Tuple[str, str]:
    try:
        # 从配置中读取 SMTP 信息
        smtp_server = conf().get("email_smtp_server")
        # 确保为int类型
        smtp_port = int(conf().get("email_smtp_port"))
        email_address = conf().get("email_address")
        email_password = conf().get("email_password")
        if not smtp_server:
            logging.warning("[EMAIL] SMTP server not found in configuration")
            return "", "SMTP server not found in configuration"
        if not smtp_port:
            logging.warning("[EMAIL] SMTP port not found in configuration")
            return "", "SMTP port not found in configuration"
        if not email_address:
            logging.warning("[EMAIL] Email address not found in configuration")
            return "", "Email address not found in configuration"
        if not email_password:
            logging.warning("[EMAIL] Email password not found in configuration")
            return "", "Email password not found in configuration"

        # 创建邮件消息
        msg = MIMEMultipart()
        msg['From'] = f"LLMOps智能助手<{email_address}>"
        msg['To'] = ", ".join(receivers)
        msg['Subject'] = subject
        if cc:
            msg['Cc'] = ", ".join(cc)

        # Attach the email body text
        msg.attach(MIMEText(text, 'plain'))
        all_recipients = receivers + cc
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.login(email_address, email_password)
            server.sendmail(email_address, all_recipients, msg.as_string())
        if cc:
            success_message = f"Send email to {receivers} with cc {cc} success"
        else:
            success_message = f"Send email to {receivers} success"
        logging.info(success_message)
        return success_message, ""
    except ValueError:
        return "", "SMTP server port must be an integer"
    except Exception as e:
        error_info = f"send email failed: {e}"
        logging.error(f"[SEND_EMAIL] {error_info}")
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[SEND_EMAIL] retrying send email to {receivers}, remaining retry count: {retry_count}")
            return send_email(receivers, subject, text, cc, retry_count - 1)
        return "", error_info

def send_email_by_proxy(receivers: list, subject: str, text: str, retry_count: int = 2) -> Tuple[str, str]:
    """
    配置的http代理,不支持smtp协议,转发到外部api发送邮件
    """
    try:
        header = {
            "Authorization": conf().get("authorization", "")
        }
        send_eamil_proxy_url = "https://api.hanfangyuan.cn/v1/send_emails"
        payload = {
            "receivers": receivers,
            "subject": subject,
            "text": text
        }
        response = requests.post(send_eamil_proxy_url, json=payload, headers=header)
        response.raise_for_status()
        result = response.json()
        err = result.get("error")
        if err:
            logging.error(f"[SEND_EMAIL_PROXY] {err}")
            return err
        return result.get("result"), ""
    except Exception as e:
        error_info = f"send email failed: {e}"
        logging.error(f"[SEND_EMAIL_PROXY] {error_info}")
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[SEND_EMAIL_PROXY] retrying send email to {receivers}, remaining retry count: {retry_count}")
            return send_email_by_proxy(receivers, subject, text, retry_count - 1)
        return "", error_info
