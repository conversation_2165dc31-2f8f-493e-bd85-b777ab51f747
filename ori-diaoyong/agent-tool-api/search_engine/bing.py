import logging
import time
from typing import <PERSON><PERSON>, List, Dict, Any
import concurrent.futures

import requests

from config import conf
from parse_url import parse_url_with_bs

MAX_PAGE = 5 # 搜索结果返回的最大网页数量
SINGE_PAGE_CHARACTER_LIMIT = 6000 # 单个网页字符限制
TOTAL_CHARACTER_LIMIT = 20000 # 总字符限制
INTERNET_SEARCH_CITATION_TYPE = "internet_search"

def bing_search(query: str, parse: bool, api_key: str = "", retry_count: int = 2) -> Tuple[str, str]:
    try:
        # 优先从参数获取api_key，否则从配置获取
        api_key = api_key or conf().get('bing_api_key')
        if not api_key:
            logging.warning("[BING] Bing API key not found in configuration")
            return "", "Bing API key not found in configuration"
        
        mkt = 'zh-CN'
        max_page = MAX_PAGE
        bing_search_url = "https://api.bing.microsoft.com/v7.0/search"
        headers = { 
            'Ocp-Apim-Subscription-Key': api_key
        }
        params = {
            'q': query,
            'mkt': mkt,
            'count': max_page,
            "responseFilter": "Webpages"
        }
        return _fetch_search_results(bing_search_url, headers, params, parse), ""
    except Exception as e:
        error_info = f"Error search {query} with bing: {e}"
        logging.error(error_info)
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"Retrying search {query} with bing, Remaining retry count: {retry_count}")
            return bing_search(query, parse, retry_count - 1)
        return "", error_info

def _fetch_and_parse_url(webpage, parse):
    name = webpage['name']
    url = webpage['url']
    snippet = webpage['snippet']
    if not parse:
        return {
            "citation_type": INTERNET_SEARCH_CITATION_TYPE,
            "content": snippet,
            "internet_search_details": {
                "title": name,
                "url": url,
                "snippet": snippet
            }
        }
    # {
    #     "id": "https://api.bing.microsoft.com/api/v7/#WebPages.0",
    #     "name": "国际新闻_央视网(cctv.com)",
    #     "url": "https://news.cctv.com/world/",
    #     "isFamilyFriendly": true,
    #     "displayUrl": "https://news.cctv.com/world",
    #     "snippet": "央视网(cctv.com)新闻频道国际新闻提供最及时，最权威，最全面的国际大事，焦点新闻 。 新闻 国内 国际 经济 社会 法治 图片 文娱 科技 生活 军事 快看 更多 原创 直播 人物 央博 教育 专题 健康 联 播+ 热解读 央视快评 新闻+ 8点见 快看 综 合 ...",
    #     "dateLastCrawled": "2024-05-19T20:13:00.0000000Z",
    #     "cachedPageUrl": "http://cncc.bingj.com/cache.aspx?q=%E4%BB%8A%E6%97%A5%E5%9B%BD%E9%99%85%E6%96%B0%E9%97%BB&d=4627651569414170&mkt=zh-CN&setlang=zh-CN&w=LB4rG6ET2IY4acT_ZneSO2obnefkpfgm",
    #     "language": "zh_chs",
    #     "isNavigational": false,
    #     "noCache": false
    # }
    logging.info(f"parsing url: {url}")
    content, err = parse_url_with_bs(url, retry_count=0, timeout=10)
    if err:
        logging.warning(f"Error parsing url: {url}, error: {err}, use snippet instead")
        content = snippet
    return {
        "citation_type": INTERNET_SEARCH_CITATION_TYPE,
        "content": content[:SINGE_PAGE_CHARACTER_LIMIT],
        "internet_search_details": {
            "title": name,
            "url": url,
            "snippet": snippet
        }
    }

def _fetch_search_results(bing_search_url, headers, params, parse) -> List[Dict[str, Any]]:
    # {
    #     "_type": "SearchResponse",
    #     "queryContext": {
    #         "originalQuery": "今日国际新闻"
    #     },
    #     "webPages": {
    #         "webSearchUrl": "https: //www.bing.com/search?q=%E4%BB%8A%E6%97%A5%E5%9B%BD%E9%99%85%E6%96%B0%E9%97%BB",
    #         "totalEstimatedMatches": 1240000,
    #         "value": [
    #             {
    #                 "id": "https://api.bing.microsoft.com/api/v7/#WebPages.0",
    #                 "name": "国际新闻_央视网(cctv.com)",
    #                 "url": "https://news.cctv.com/world/",
    #                 "isFamilyFriendly": true,
    #                 "displayUrl": "https://news.cctv.com/world",
    #                 "snippet": "央视网(cctv.com)新闻频道国际新闻提供最及时，最权威，最全面的国际大事，焦点新闻 。 新闻 国内 国际 经济 社会 法治 图片 文娱 科技 生活 军事 快看 更多 原创 直播 人物 央博 教育 专题 健康 联 播+ 热解读 央视快评 新闻+ 8点见 快看 综 合 ...",
    #                 "dateLastCrawled": "2024-05-19T20:13:00.0000000Z",
    #                 "cachedPageUrl": "http://cncc.bingj.com/cache.aspx?q=%E4%BB%8A%E6%97%A5%E5%9B%BD%E9%99%85%E6%96%B0%E9%97%BB&d=4627651569414170&mkt=zh-CN&setlang=zh-CN&w=LB4rG6ET2IY4acT_ZneSO2obnefkpfgm",
    #                 "language": "zh_chs",
    #                 "isNavigational": false,
    #                 "noCache": false
    #             }...
    #         ],
    #         "someResultsRemoved": true
    #     },
    #     "rankingResponse": {}
    # }
    results = []
    response = requests.get(bing_search_url, headers=headers, params=params)
    response.raise_for_status()
    search_results = response.json()
    webpages = search_results['webPages']['value']
    # 使用ThreadPoolExecutor进行并行处理
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(_fetch_and_parse_url, webpage, parse) for webpage in webpages]
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    filtered_result = []
    total_character_num = 0
    for result in results:
        if total_character_num > TOTAL_CHARACTER_LIMIT:
            break
        total_character_num += len(result.get("content", ""))
        filtered_result.append(result)

    return filtered_result
