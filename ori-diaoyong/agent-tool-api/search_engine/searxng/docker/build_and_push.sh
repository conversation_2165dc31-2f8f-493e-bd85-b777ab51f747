#!/bin/bash

# 设置变量
IMAGE_NAME="searxng"
IMAGE_TAG="dev"

# 切换到Dockerfile所在目录
cd "$(dirname "$0")"

# 构建Docker镜像
echo "开始构建Docker镜像: ${IMAGE_NAME}:${IMAGE_TAG}"
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} -f Dockerfile .
docker tag ${IMAGE_NAME}:${IMAGE_TAG} harbor.transwarp.io/aip/searxng:dev

# 推送到私有镜像仓库
echo "开始推送Docker镜像: ${IMAGE_NAME}:${IMAGE_TAG} 到私有镜像仓库"

sudo docker push harbor.transwarp.io/aip/searxng:dev
