from typing import Tuple
import requests
from bs4 import BeautifulSoup
import logging
import time

from config import conf

CHARACTER_LIMIT = 10000

tushare_headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'Cookie': conf().get('tushare_cookie'),
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"'
}

def tushare_search(query: str, retry_count: int = 2, timeout: int = 20) -> Tuple[str, str]:
    """
    Search for news on Tushare using the given query.
    Args:
        query (str): The query to search for.
        retry_count (int): Number of retries if request fails.
        timeout (int): Request timeout in seconds.
        
    Returns:
        Tuple[str, str]: (content, error_message)
    """
    try:
        url = f'https://tushare.pro/news'
        params = {'s': query}
        response = requests.get(
            url,
            headers=tushare_headers,
            params=params,
            timeout=timeout
        )
        response.raise_for_status()
        # Clean up the HTML content
        soup = BeautifulSoup(response.text, 'html.parser')
        body = soup.find('body')
        if body:
            for script in body.find_all('script'):
                script.decompose()
            for style in body.find_all('style'):
                style.decompose()
            content = body.get_text(strip=True)
            content = content[:CHARACTER_LIMIT]
            return content, ""
        else:
            return "", "No body tag found in the HTML"
    except Exception as e:
        error_info = f"Error searching Tushare with query '{query}': {e}"
        logging.error(error_info)
        
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"Retrying Tushare search... Remaining retry count: {retry_count}")
            return tushare_search(query, retry_count - 1, timeout=timeout)
        
        return "", error_info
