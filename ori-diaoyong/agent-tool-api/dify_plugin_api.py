"""
Dify插件API接口
提供RESTful API用于插件管理和工具调用
"""

import os
import tempfile
import logging
from typing import Dict, Any
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename

from config import conf
from utils.response import make_success_response, make_error_response
from dify_plugin import DifyPluginManager
from dify_plugin.models import DifyApiConfig


logger = logging.getLogger(__name__)


class DifyPluginAPI:
    """Dify插件API类"""
    
    def __init__(self, app: Flask):
        self.app = app
        self.config = conf()
        
        # 初始化Dify配置
        self.dify_config = DifyApiConfig.from_config(self.config)
        
        # 初始化插件管理器
        storage_dir = self.config.get('dify_plugin_storage_dir', '/tmp/dify_plugins')
        self.plugin_manager = DifyPluginManager(self.dify_config, storage_dir)
        
        # 注册API路由
        self._register_routes()
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.route('/v1/dify-plugins/upload', methods=['POST'])
        def upload_plugin():
            """上传并安装.difypkg插件文件"""
            try:
                # 检查文件
                if 'file' not in request.files:
                    return make_error_response("没有上传文件")
                
                file = request.files['file']
                if file.filename == '':
                    return make_error_response("文件名为空")
                
                if not file.filename.endswith('.difypkg'):
                    return make_error_response("文件格式不正确，必须是.difypkg文件")
                
                # 安全处理文件名
                filename = secure_filename(file.filename)
                
                # 保存临时文件
                temp_dir = tempfile.mkdtemp()
                temp_path = os.path.join(temp_dir, filename)
                file.save(temp_path)
                
                try:
                    # 安装插件
                    result = self.plugin_manager.upload_and_install_plugin(temp_path)
                    
                    if result['success']:
                        return make_success_response(result)
                    else:
                        return make_error_response(result['message'])
                        
                finally:
                    # 清理临时文件
                    try:
                        os.remove(temp_path)
                        os.rmdir(temp_dir)
                    except:
                        pass
                        
            except Exception as e:
                logger.error(f"上传插件失败: {e}")
                return make_error_response(f"上传插件失败: {str(e)}")
        



        @self.app.route('/v1/dify-plugins/installed', methods=['GET'])
        def get_all_installed_plugins():
            """获取所有已安装插件信息及授权状态"""
            try:
                result = self.plugin_manager.get_all_installed_plugins_with_auth_status()

                if result['success']:
                    return make_success_response(result['data'])
                else:
                    return make_error_response(result['message'])

            except Exception as e:
                logger.error(f"获取已安装插件信息失败: {e}")
                return make_error_response(f"获取已安装插件信息失败: {str(e)}")




def init_dify_plugin_api(app: Flask) -> DifyPluginAPI:
    """初始化Dify插件API"""
    return DifyPluginAPI(app)