import json
from typing import Any, Union

import requests
from httpx import post

from dify_tools.tool import ToolInvokeMessage, Tool


class ExecuteCodeTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        """
        invoke tools
        """
        err = ""
        api_key = tool_parameters.get('X-RapidAPI-Key')
        if not api_key:
            err = "X-RapidAPI-Key is required"
            return self.create_text_message(err), err

        source_code = tool_parameters.get('source_code')
        if not source_code:
            err = "source_code is required"
            return self.create_text_message(err), err

        language_id = tool_parameters.get('language_id')
        if not language_id:
            err = "language_id is required"
            return self.create_text_message(err), err

        try:
            url = "https://judge0-ce.p.rapidapi.com/submissions"
            querystring = {"base64_encoded": "false", "fields": "*"}

            headers = {
                "Content-Type": "application/json",
                "X-RapidAPI-Key": api_key,
                "X-RapidAPI-Host": "judge0-ce.p.rapidapi.com"
            }

            payload = {
                "language_id": language_id,
                "source_code": source_code,
                "stdin": tool_parameters.get('stdin', ''),
                "expected_output": tool_parameters.get('expected_output', ''),
                "additional_files": tool_parameters.get('additional_files', ''),
            }

            response = post(url, data=json.dumps(payload), headers=headers, params=querystring)

            if response.status_code != 201:
                err = response.text
                return self.create_text_message(err), err

            token = response.json()['token']

            url = f"https://judge0-ce.p.rapidapi.com/submissions/{token}"
            headers = {
                "X-RapidAPI-Key": api_key
            }
            
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                result = response.json()
                return self.create_text_message(text=f"stdout: {result.get('stdout', '')}\n"
                                 f"stderr: {result.get('stderr', '')}\n"
                                 f"compile_output: {result.get('compile_output', '')}\n"
                                 f"message: {result.get('message', '')}\n"), err
            else:
                err = f"Error retrieving submission details: {response.text}"
                return self.create_text_message(err), err
                
        except Exception as e:
            err = f"Error executing code: {str(e)}"
            return self.create_text_message(err), err

judgece_execute_code_tool = ExecuteCodeTool()