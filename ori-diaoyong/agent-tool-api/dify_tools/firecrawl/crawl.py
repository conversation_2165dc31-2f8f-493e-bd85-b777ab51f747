from typing import Any

from firecrawl import Fire<PERSON>rawlApp
from dify_tools.tool import Tool
from config import conf

class CrawlTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        err = ""
        firecrawl_api_key = tool_parameters.get('api_key') or conf().get('firecrawl_api_key', '')
        if not firecrawl_api_key:
            err = '无效的 firecrawl api key 配置'
            return self.create_text_message(err), err
        
        url = tool_parameters.get('url', '').strip()
        if not url:
            err = '无效的 URL'
            return self.create_text_message(err), err
        
        try:
            app = FirecrawlApp(api_key=firecrawl_api_key)
            crawl_limit = tool_parameters.get('crawl_limit', 3)

            options = {
                'crawlerOptions': {
                    'excludes': tool_parameters.get('excludes', '').split(',') if tool_parameters.get('excludes') else [],
                    'includes': tool_parameters.get('includes', '').split(',') if tool_parameters.get('includes') else [],
                    'limit': crawl_limit
                },
                'pageOptions': {
                    'onlyMainContent': tool_parameters.get('onlyMainContent', False)
                }
            }

            crawl_result = app.crawl_url(
                url=tool_parameters['url'], 
                params=options,
                wait_until_done=True, 
            )
            
            crawl_output = "**Crawl Result**\n\n"
            for result in crawl_result:
                crawl_output += f"**- Title:** {result.get('metadata', {}).get('title', '')}\n"
                crawl_output += f"**- Description:** {result.get('metadata', {}).get('description', '')}\n"
                crawl_output += f"**- URL:** {result.get('metadata', {}).get('ogUrl', '')}\n\n"
                crawl_output += f"**- Web Content:**\n{result.get('markdown', '')}\n\n"
                crawl_output += "---\n\n"
            
            return self.create_text_message(crawl_output), err
        except Exception as e:
            err = f"爬取过程中发生错误: {str(e)}"
            return self.create_text_message(err), err

fire_crawl_tool = CrawlTool()