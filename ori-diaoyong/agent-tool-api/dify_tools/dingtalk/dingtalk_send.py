import logging
import time
from typing import <PERSON><PERSON>
from config import conf

import requests

def send_dingtalk_group_message(text: str, hook_key: str="", retry_count: int = 2) -> Tuple[str, str]:
    # 从配置中读取 hook_key
    hook_key = hook_key or conf().get("dingtalk_hook_key")
    if not hook_key:
        logging.warning("[DINGTALK_GROUP_BOT] DINGTALK hook key not found in configuration")
        return "", "dingtalk hook key not found in configuration"
    url = f'https://oapi.dingtalk.com/robot/send?access_token={hook_key}'
    payload = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        err_msg = result.get("errmsg")
        if err_msg != "ok":
            raise Exception(err_msg)
        success_message = f"Send Dingtalk group message success"
        logging.info(f"[DINGTALK_GROUP_BOT] {success_message}")
        return success_message, ""
    except Exception as e:
        error_info = f"send Dingtalk group message failed: {e}"
        logging.error(f"[DINGTALK_GROUP_BOT] {error_info}")
        if retry_count > 0:
            logging.info(f"[DINGTALK_GROUP_BOT] retrying send message, remaining retry count: {retry_count}")
            time.sleep(1)
            return send_dingtalk_group_message(text, retry_count - 1)
        return "", error_info