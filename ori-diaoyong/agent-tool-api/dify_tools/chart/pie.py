import io
from typing import Any

import matplotlib.pyplot as plt

from dify_tools.tool import Tool

plt.rcParams['font.sans-serif'] = ['SimHei']

class PieChartTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        err = ""
        data = tool_parameters.get('data', '')
        if not data:
            err = 'Please input data'
            return self.create_text_message(err), err
            
        try:
            data = data.split(';')
            categories = tool_parameters.get('categories', None) or None

            # if all data is int, convert to int
            if all([i.isdigit() for i in data]):
                data = [int(i) for i in data]
            else:
                data = [float(i) for i in data]

            flg, ax = plt.subplots()

            if categories:
                categories = categories.split(';')
                if len(categories) != len(data):
                    err = 'Categories length does not match data length'
                    return self.create_text_message(err), err

            if categories:
                ax.pie(data, labels=categories)
            else:
                ax.pie(data)

            buf = io.BytesIO()
            flg.savefig(buf, format='png')
            buf.seek(0)
            plt.close(flg)

            return (self.create_text_message('the pie chart is saved as an image. ') + 
                   self.create_file_message(blob=buf.read(), ext='.png')), err
        except Exception as e:
            err = f"Error creating pie chart: {str(e)}"
            return self.create_text_message(err), err

pie_chart_tool = PieChartTool()
