import smtplib
import ssl
import logging
import json
import re
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from email.header import Header
from typing import Tuple
import time
import os
import socket

import requests

from config import conf

def dify_send_email(send_to: str, subject: str, content: str, retry_count: int = 2) -> <PERSON><PERSON>[str, str]:
    """
    直观且实用的邮件发送功能
    返回    Tuple[str, str]: (success_message, error_message)
    """
    
    try:
        email_regex = re.compile(r"^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$")

        receivers_email = []
        
        if isinstance(send_to, str):
            send_to = send_to.strip()
            # 尝试解析JSON格式
            if send_to.startswith('[') and send_to.endswith(']'):
                try:
                    receivers_email = json.loads(send_to)
                except json.JSONDecodeError:
                    return "", "收件人JSON格式错误"
            else:
                # 单个邮箱或逗号分隔的多个邮箱
                receivers_email = [email.strip() for email in send_to.split(',') if email.strip()]
        else:
            return "", "收件人参数格式错误"
        
        # 验证邮箱格式和必填参数
        for receiver in receivers_email:
            if not email_regex.match(receiver):
                return "", f"无效的收件人邮箱: {receiver}"
        if not receivers_email:
            return "", "收件人邮箱账号不能为空"
        if not subject:
            return "", "邮件主题不能为空"
        if not content:
            return "", "邮件内容不能为空"
        
        # 读取SMTP配置
        smtp_server = conf().get("email_smtp_server")
        smtp_port = conf().get("email_smtp_port")
        email_account = conf().get("email_address")
        email_password = conf().get("email_password")
        smtp_use_tls = conf().get("email_smtp_use_tls", True)
        
        if not all([smtp_server, email_account, email_password]):
            return "", "SMTP邮件配置不完整"
        
        try:
            smtp_port = int(smtp_port)
        except (ValueError, TypeError):
            smtp_port = 587
        
        if not email_regex.match(email_account):
            return "", f"发送者邮箱配置错误: {email_account}"
        
        # 构建邮件消息
        msg = MIMEMultipart()
        msg['Subject'] = subject
        
        sender_name = "LLMOps智能助手"
        msg['From'] = formataddr((str(Header(sender_name, 'utf-8')), email_account))
        
        msg['To'] = ", ".join(receivers_email)
        msg.attach(MIMEText(content, 'plain', 'utf-8'))

        http_proxy = os.getenv('http_proxy') or os.getenv('HTTP_PROXY')
        print("HTTP Proxy:", http_proxy)


        # 发送邮件
        smtp = None
        try:
            # 连接方式ssl或者STARTTLS
            if smtp_port == 465:
                smtp = smtplib.SMTP_SSL(smtp_server, smtp_port)
            else:
                smtp = smtplib.SMTP(smtp_server, smtp_port)
                if smtp_use_tls:
                    smtp.starttls()

            if email_account and email_password:
                smtp.login(email_account, email_password)
            smtp.sendmail(email_account, receivers_email, msg.as_string())
            
            success_message = f"邮件发送成功 | 收件人: {', '.join(receivers_email)} | 主题: {subject}"
            logging.info(f"[DIFY_SIMPLE_EMAIL] {success_message}")
            
            return success_message, ""
            
        finally:
            if smtp:
                try:
                    smtp.quit()
                except:
                    pass
        
    except Exception as e:
        error_info = f"邮件发送失败: {str(e)}"
        logging.error(f"[DIFY_SIMPLE_EMAIL] {error_info}")

        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[DIFY_SIMPLE_EMAIL] 重试发送，剩余次数: {retry_count}")
            return dify_send_email(send_to, subject, content, retry_count - 1)
        
        return "", error_info


def send_email_by_proxy(send_to: str, subject: str, content: str, retry_count: int = 2) -> Tuple[str, str]:
    """
    # 配置的http代理,不支持smtp协议,转发到外部api发送邮件
    """
    try:
        send_eamil_proxy_url = "http://************:5000/v1/send_emails"
        payload = {
            "receivers": send_to,
            "subject": subject,
            "text": content
        }
        try:
            proxies = {
                'http': None,
                'https': None
            }   
            logging.info(f"[SEND_EMAIL_PROXY] 尝试直连访问 {send_eamil_proxy_url}")
            response = requests.post(send_eamil_proxy_url, json=payload, timeout=30, proxies=proxies)
        except Exception as direct_error:
            logging.warning(f"[SEND_EMAIL_PROXY] 直连失败: {direct_error}")
            
            # 方法2: 使用代理
            try:
                proxies = {
                    'http': "*************:3128",
                    'https': "*************:3128"
                }
                logging.info(f"[SEND_EMAIL_PROXY] 尝试使用代理 *************:3128 访问 {send_eamil_proxy_url}")
                response = requests.post(send_eamil_proxy_url, json=payload, timeout=30, proxies=proxies)
                logging.info(f"[SEND_EMAIL_PROXY] 代理连接成功")
                
            except Exception as proxy_error:
                logging.error(f"[SEND_EMAIL_PROXY] 代理连接也失败: {proxy_error}")
                # 两种方式都失败，抛出最后的错误
                raise proxy_error
        logging.info(f"[SEND_EMAIL_PROXY] 响应状态码: {response.status_code}")           

        response.raise_for_status()
        result = response.json()
        err = result.get("error")
        if err:
            logging.error(f"[SEND_EMAIL_PROXY] {err}")
            return err
        return result.get("result"), ""
    except Exception as e:
        error_info = f"send email failed: {e}"
        logging.error(f"[SEND_EMAIL_PROXY] {error_info}")
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[SEND_EMAIL_PROXY] retrying send email to {send_to}, remaining retry count: {retry_count}")
            return send_email_by_proxy(send_to, subject, content, retry_count - 1)
        return "", error_info


def send_email_by_dify(send_to: str, subject: str, content: str, retry_count: int = 2) -> Tuple[str, str]:
    try:
        if not send_to:
            return "", "收件人邮箱账号不能为空"
        recipients = [email.strip() for email in send_to.split(',') if email.strip()]
        if not recipients:
            return "", "收件人邮箱账号不能为空"
        if not subject:
            return "", "邮件主题不能为空"
        if not content:
            return "", "邮件内容不能为空"
        
        email_regex = re.compile(r"^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$")
        for recipient in recipients:
            if not email_regex.match(recipient):
                return "", f"无效的收件人邮箱: {recipient}"
        
        dify_api_url = "http://************:5002/plugin/f24e8fa4-b522-48b2-a5ff-fe639b38d089/dispatch/tool/invoke"
        headers = {
            'Content-Type': 'application/json',
            'X-Api-Key': 'lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi',
            'X-Plugin-ID': 'langgenius/email',
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI'
        }
        
        success_messages = []
        failed_recipients = []
        print("调用api前")
        # 有几个收件人就调用几次api
        for recipient in recipients:
            try:
                payload = {
                    "data": {
                        "provider": "email",
                        "tool": "send_mail",
                        "credentials": {
                            "smtp_server": "smtp.qq.com",
                            "smtp_port": "587",
                            "email_account": "<EMAIL>",
                            "email_password": "tvisizfdwuzhcfee",
                            "encrypt_method": "TLS",
                            "nsender_address": "<EMAIL>"
                        },
                        "tool_parameters": {
                            "send_to": recipient,
                            "subject": subject,
                            "email_content": content
                        }
                    }
                }
                
                logging.info(f"[DIFY_EMAIL] 发送邮件到 {recipient}")
                # try:
                #     proxies = {
                #         'http': None,
                #         'https': None
                #     }
                #     print("直连的时候---------")
                #     response = requests.post(dify_api_url, json=payload, headers=headers, timeout=30, proxies=proxies)
                # except Exception as direct_error:
                #     print("代理的时候---------")
                #     logging.warning(f"[DIFY_EMAIL] 直连失败: {direct_error}")
                try:
                    proxies = {
                        'http': "*************:3128",
                        'https': "*************:3128"
                    }
                    logging.info(f"[DIFY_EMAIL] 尝试使用代理发送到 {recipient}")
                    response = requests.post(dify_api_url, json=payload, headers=headers, timeout=30, proxies=proxies)
                except Exception as proxy_error:
                    logging.error(f"[DIFY_EMAIL] 代理连接也失败: {proxy_error}")
                    failed_recipients.append(f"{recipient}: {str(proxy_error)}")
                    continue

                logging.info(f"[DIFY_EMAIL] 响应状态码: {response.status_code}, 响应内容: {response.text[:200]}...")
                if response.status_code == 200:
                    try:
                        if not response.text.strip():
                            error_msg = "API返回空响应"
                            failed_recipients.append(f"{recipient}: {error_msg}")
                            logging.error(f"[DIFY_EMAIL] {recipient}: {error_msg}")
                            continue
                        response_text = response.text.strip()
                        if response_text.startswith('data: '):
                            json_lines = []
                            for line in response_text.split('\n'):
                                if line.startswith('data: '):
                                    json_data = line[6:]
                                    if json_data.strip():
                                        json_lines.append(json_data)
                            
                            if json_lines:
                                result = json.loads(json_lines[-1])
                            else:
                                raise ValueError("SSE响应中没有找到有效的JSON数据")
                        else:
                            result = response.json()
                        
                        logging.info(f"[DIFY_EMAIL] API响应结果: {result}")
                        
                        # 检查响应结果
                        if result.get('code') == 200 or result.get('success') is True or 'success' in str(result).lower():
                            success_messages.append(f"发送成功到 {recipient}")
                            logging.info(f"[DIFY_EMAIL] 邮件发送成功到 {recipient}")
                        else:
                            error_msg = result.get('message') or result.get('msg') or result.get('error') or '未知错误'
                            failed_recipients.append(f"{recipient}: {error_msg}")
                            logging.error(f"[DIFY_EMAIL] API返回错误 {recipient}: {error_msg}")
                    
                    except (ValueError, json.JSONDecodeError) as json_error:
                        response_text = response.text.strip()
                        if 'success' in response_text.lower() and ('code":0' in response_text or 'message":"success"' in response_text):
                            success_messages.append(f"发送成功到 {recipient}")
                            logging.info(f"[DIFY_EMAIL] 邮件发送成功到 {recipient} (从SSE响应推断)")
                        else:
                            error_msg = f"JSON解析失败: {str(json_error)}, 响应内容: {response.text[:200]}"
                            failed_recipients.append(f"{recipient}: {error_msg}")
                            logging.error(f"[DIFY_EMAIL] {recipient}: {error_msg}")
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    failed_recipients.append(f"{recipient}: {error_msg}")
                    logging.error(f"[DIFY_EMAIL] HTTP错误 {recipient}: {error_msg}")
                    
            except Exception as e:
                error_msg = f"请求异常: {str(e)}"
                failed_recipients.append(f"{recipient}: {error_msg}")
                logging.error(f"[DIFY_EMAIL] 发送到 {recipient} 异常: {error_msg}")

        if success_messages and not failed_recipients:
            success_msg = f"邮件发送成功 | 收件人: {', '.join(recipients)} | 主题: {subject}"
            return success_msg, ""
        elif success_messages and failed_recipients:
            success_msg = f"部分邮件发送成功: {'; '.join(success_messages)}"
            error_msg = f"部分邮件发送失败: {'; '.join(failed_recipients)}"
            return success_msg, error_msg
        else:
            error_msg = f"邮件发送失败: {'; '.join(failed_recipients)}"
            return "", error_msg
            
    except Exception as e:
        error_info = f"Dify邮件发送异常: {str(e)}"
        logging.error(f"[DIFY_EMAIL] {error_info}")
        
        if retry_count > 0:
            time.sleep(1)
            logging.info(f"[DIFY_EMAIL] 重试发送，剩余次数: {retry_count}")
            return send_email_by_dify(send_to, subject, content, retry_count - 1)
        
        return "", error_info
