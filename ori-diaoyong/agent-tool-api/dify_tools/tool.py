import os
from typing import Union, Any
from uuid import uuid4

from config import conf 

SFS_ROOT = conf().get("sfs_root")
SFS_BASE_URL = conf().get("sfs_base_url")

class ToolInvokeMessage():
    class MessageType():
        TEXT = "text"
        IMAGE = "image"
        LINK = "link"
        BLOB = "blob"
        IMAGE_LINK = "image_link"
        FILE_VAR = "file_var"
    
    type: MessageType = MessageType.TEXT
    message: Union[str, bytes] = None
    meta: dict[str, Any] = None
    save_as: str = ''
    
    def __str__(self):
        return self.message

    def __init__ (self, type: MessageType, message: Union[str, bytes], save_as: str = ''):
        self.type = type
        self.message = message
        self.save_as = save_as

    
class Tool():
    def create_text_message(self, text: str, save_as: str = '') -> str:
        return text

    def create_file_message(self, blob: bytes, ext: str) -> str:
        random_id = str(uuid4())
        file_name = random_id + ext
        with open(os.path.join(SFS_ROOT, file_name), "wb") as f:
            f.write(blob)
        sfs_url = os.path.join(SFS_BASE_URL, file_name)
        markdown_link = f"[{file_name}]({sfs_url})"
        if ext == ".png" or ext == ".jpg" or ext == ".jpeg" or ext == ".bmp" or ext == ".ico":
            markdown_link = "!" + markdown_link
        return markdown_link
