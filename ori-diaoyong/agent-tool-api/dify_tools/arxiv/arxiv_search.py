from typing import Any, Optional

import arxiv
from dify_tools.tool import <PERSON><PERSON>, ToolInvokeMessage


class ArxivAPIWrapper():
    arxiv_search = arxiv.Search  #: :meta private:
    arxiv_exceptions = (
        arxiv.ArxivError,
        arxiv.UnexpectedEmptyPageError,
        arxiv.HTTPError,
    )  # :meta private:
    top_k_results: int = 5
    ARXIV_MAX_QUERY_LENGTH = 300
    load_max_docs: int = 100
    load_all_available_meta: bool = False
    doc_content_chars_max: Optional[int] = 4000

    def run(self, query: str) -> str:
        """
        Performs an arxiv search and A single string
        with the publish date, title, authors, and summary
        for each article separated by two newlines.

        If an error occurs or no documents found, error text
        is returned instead. Wrapper for
        https://lukasschwab.me/arxiv.py/index.html#Search

        Args:
            query: a plaintext search query
        """  # noqa: E501
        try:
            results = self.arxiv_search(  # type: ignore
                query[: self.ARXIV_MAX_QUERY_LENGTH], max_results=self.top_k_results
            ).results()
        except self.arxiv_exceptions as ex:
            return f"Arxiv exception: {ex}"
        docs = [
            f"Published: {result.updated.date()}\n"
            f"Title: {result.title}\n"
            f"Authors: <AUTHORS>
            f"Summary: {result.summary}"
            for result in results
        ]
        if docs:
            return "\n\n".join(docs)[: self.doc_content_chars_max]
        else:
            return "No good Arxiv Result was found"
 
class ArxivSearchTool(Tool):
    """
    A tool for searching articles on Arxiv.
    """
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        err = ""
        query = tool_parameters.get('query', '')

        if not query:
            err = 'Please input query'
            return self.create_text_message(err), err
        
        arxiv = ArxivAPIWrapper()
        try:
            response = arxiv.run(query)
            if response.startswith("Arxiv exception:"):
                err = response
                return self.create_text_message(err), err
            if response == "No good Arxiv Result was found":
                err = response
                return self.create_text_message(err), err
            return self.create_text_message(response), err
        except Exception as e:
            err = f"Error occurred while searching arxiv: {str(e)}"
            return self.create_text_message(err), err

arxiv_search_tool = ArxivSearchTool()