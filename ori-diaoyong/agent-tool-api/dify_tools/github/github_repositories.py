import json
from datetime import datetime
import logging
from typing import Any
from urllib.parse import quote
from config import conf

import requests

from dify_tools.tool import Tool


class GihubRepositoriesTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        """
            invoke tools
        """
        err = ""
        query = tool_parameters.get('query', '')
        if not query:
            err = 'Please input query'
            return self.create_text_message(err), err

        access_token = tool_parameters.get('access_token') or tool_parameters.get('api_key') or conf().get('github_api_key')
        if not access_token:
            err = "Github API Access Token not found in configuration"
            logging.warning("[GITHUB] " + err)
            return self.create_text_message(err), err

        api_version = tool_parameters.get('api_version', '2022-11-28')
        top_n = tool_parameters.get('top_n', 5)
        try:
            headers = {
                "Content-Type": "application/vnd.github+json",
                "Authorization": f"Bearer {access_token}",
                "X-GitHub-Api-Version": api_version
            }
            s = requests.session()
            api_domain = 'https://api.github.com'
            response = s.request(method='GET', headers=headers,
                                 url=f"{api_domain}/search/repositories?"
                                     f"q={quote(query)}&sort=stars&per_page={top_n}&order=desc")
            response_data = response.json()
            if response.status_code == 200 and isinstance(response_data.get('items'), list):
                contents = list()
                if len(response_data.get('items')) > 0:
                    for item in response_data.get('items'):
                        content = dict()
                        updated_at_object = datetime.strptime(item['updated_at'], "%Y-%m-%dT%H:%M:%SZ")
                        content['owner'] = item['owner']['login']
                        content['name'] = item['name']
                        content['description'] = item.get('description')[:100] if item.get('description') else ''
                        content['url'] = item['html_url']
                        content['star'] = item['watchers']
                        content['forks'] = item['forks']
                        content['updated'] = updated_at_object.strftime("%Y-%m-%d")
                        contents.append(content)
                    s.close()
                    return self.create_text_message(json.dumps(contents, ensure_ascii=False)), ""
                else:
                    err = f'No items related to {query} were found.'
                    return self.create_text_message(err), err
            else:
                err = (response.json()).get('message')
                return self.create_text_message(err), err
        except Exception as e:
            err = f"Github API Key and Api Version is invalid. {e}"
            return self.create_text_message(err), err

github_repositories_tool = GihubRepositoriesTool()
