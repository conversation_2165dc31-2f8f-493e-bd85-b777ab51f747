import json
import logging
from typing import Any, Union

import requests

from dify_tools.tool import ToolInvokeMessage, Tool
from config import conf  # 导入配置

class GaodeWeatherTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> tuple[str, str]:
        """
            invoke tools
        """
        err = ""
        city = tool_parameters.get('city')
        if not city:
            err = 'Please tell me your city'
            return self.create_text_message(err), err

        # 优先从参数获取api_key，否则从配置获取
        api_key = tool_parameters.get('api_key') or conf().get('gaode_api_key')
        if not api_key:
            err = "Gaode API key is not found in configuration or request"
            logging.warning("[GAODE_WEATHER] " + err)
            return self.create_text_message(err), err

        try:
            s = requests.session()
            api_domain = 'https://restapi.amap.com/v3'
            city_response = s.request(method='GET', headers={"Content-Type": "application/json; charset=utf-8"},
                                      url="{url}/config/district?keywords={keywords}"
                                          "&subdistrict=0&extensions=base&key={apikey}"
                                          "".format(url=api_domain, keywords=city,
                                                    apikey=api_key))
            City_data = city_response.json()
            print(City_data)
            if city_response.status_code == 200 and City_data.get('info') == 'OK':
                if len(City_data.get('districts')) > 0:
                    CityCode = City_data['districts'][0]['adcode']
                    weatherInfo_response = s.request(method='GET',
                                                     url="{url}/weather/weatherInfo?city={citycode}&extensions=all&key={apikey}&output=json"
                                                         "".format(url=api_domain, citycode=CityCode,
                                                                   apikey=api_key))
                    weatherInfo_data = weatherInfo_response.json()
                    if weatherInfo_response.status_code == 200 and weatherInfo_data.get('info') == 'OK':
                        contents = list()
                        if len(weatherInfo_data.get('forecasts')) > 0:
                            for item in weatherInfo_data['forecasts'][0]['casts']:
                                content = dict()
                                content['date'] = item.get('date')
                                content['week'] = item.get('week')
                                content['dayweather'] = item.get('dayweather')
                                content['daytemp_float'] = item.get('daytemp_float')
                                content['daywind'] = item.get('daywind')
                                content['nightweather'] = item.get('nightweather')
                                content['nighttemp_float'] = item.get('nighttemp_float')
                                contents.append(content)
                            s.close()
                            return self.create_text_message(json.dumps(contents, ensure_ascii=False)), ""
            s.close()
            err = f'No weather information for {city} was found.'
            return self.create_text_message(err), err
        except Exception as e:
            err = f"Gaode API Key and Api Version is invalid. {e}"
            return self.create_text_message(err), err

gaode_weather_tool = GaodeWeatherTool()
