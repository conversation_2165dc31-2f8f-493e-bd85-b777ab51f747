version: "3"

services:
  flask-api:
    image: harbor.transwarp.io/aip/agent-tool-api:dev # 公司镜像地址
    # image: flask-api
    pull_policy: always
    container_name: flask-api
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "6000:80"    # 端口映射，宿主机的某个端口映射到容器的80端口
    # volumes:
    #   - .:/api       # 将当前目录挂载到容器的/api目录下，更新当前目录代码时，容器中的代码也会更新，不用重新构建镜像
    #   - /mnt/207jfs/tool:/sfs/tool # 挂载sfs共享目录
    environment:
      WORKER_NUM: 2  # 可以通过环境变量来设置gunicorn的进程数，默认为1
      # 公司代理地址
      http_proxy: http://*************:3128
      https_proxy: http://*************:3128
      # no_proxy: localhost,127.0.0.1
    restart: always
