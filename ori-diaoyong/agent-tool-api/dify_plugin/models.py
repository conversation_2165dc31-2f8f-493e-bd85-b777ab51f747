"""
Dify插件适配层数据模型
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum


class PluginStatus(Enum):
    """插件状态"""
    UPLOADING = "uploading"
    INSTALLING = "installing"
    INSTALLED = "installed"
    FAILED = "failed"
    UNINSTALLED = "uninstalled"


class AuthStatus(Enum):
    """授权状态"""
    NOT_REQUIRED = "not_required"
    REQUIRED = "required"
    AUTHORIZED = "authorized"
    INVALID = "invalid"


@dataclass
class DifyApiConfig:
    """Dify API配置"""
    base_url: str
    api_key: str
    auth_token: str
    tenant_id: str


@dataclass
class PluginInfo:
    """插件信息"""
    plugin_id: str
    name: str
    version: str
    author: str
    description: str
    unique_identifier: str
    status: PluginStatus
    auth_status: AuthStatus = AuthStatus.NOT_REQUIRED
    install_time: Optional[datetime] = None
    permissions: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginUploadResult:
    """插件上传结果"""
    success: bool
    message: str
    plugin_info: Optional[PluginInfo] = None


@dataclass
class DifyApiConfig:
    """Dify API配置"""
    base_url: str
    api_key: str
    auth_token: str
    tenant_id: str = "f24e8fa4-b522-48b2-a5ff-fe639b38d089"
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'DifyApiConfig':
        """从配置文件创建"""
        return cls(
            base_url=config.get('dify_base_url', 'http://************'),
            api_key=config.get('dify_api_key', 'lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi'),
            auth_token=config.get('dify_auth_token', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI'),
            tenant_id=config.get('dify_tenant_id', 'f24e8fa4-b522-48b2-a5ff-fe639b38d089')
        )