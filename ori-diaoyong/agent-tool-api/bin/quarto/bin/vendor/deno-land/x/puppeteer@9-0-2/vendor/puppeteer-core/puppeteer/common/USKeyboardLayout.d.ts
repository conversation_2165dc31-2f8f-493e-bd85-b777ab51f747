/**
 * Copyright 2017 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @internal
 */
export interface KeyDefinition {
  keyCode?: number;
  shiftKeyCode?: number;
  key?: string;
  shiftKey?: string;
  code?: string;
  text?: string;
  shiftText?: string;
  location?: number;
}
/**
 * All the valid keys that can be passed to functions that take user input, such
 * as {@link Keyboard.press | keyboard.press }
 *
 * @public
 */
export declare type KeyInput =
  | "0"
  | "1"
  | "2"
  | "3"
  | "4"
  | "5"
  | "6"
  | "7"
  | "8"
  | "9"
  | "Power"
  | "Eject"
  | "Abort"
  | "Help"
  | "Backspace"
  | "Tab"
  | "Numpad5"
  | "NumpadEnter"
  | "Enter"
  | "\r"
  | "\n"
  | "ShiftLeft"
  | "ShiftRight"
  | "ControlLeft"
  | "ControlRight"
  | "AltLeft"
  | "AltRight"
  | "Pause"
  | "CapsLock"
  | "Escape"
  | "Convert"
  | "NonConvert"
  | "Space"
  | "Numpad9"
  | "PageUp"
  | "Numpad3"
  | "PageDown"
  | "End"
  | "Numpad1"
  | "Home"
  | "Numpad7"
  | "ArrowLeft"
  | "Numpad4"
  | "Numpad8"
  | "ArrowUp"
  | "ArrowRight"
  | "Numpad6"
  | "Numpad2"
  | "ArrowDown"
  | "Select"
  | "Open"
  | "PrintScreen"
  | "Insert"
  | "Numpad0"
  | "Delete"
  | "NumpadDecimal"
  | "Digit0"
  | "Digit1"
  | "Digit2"
  | "Digit3"
  | "Digit4"
  | "Digit5"
  | "Digit6"
  | "Digit7"
  | "Digit8"
  | "Digit9"
  | "KeyA"
  | "KeyB"
  | "KeyC"
  | "KeyD"
  | "KeyE"
  | "KeyF"
  | "KeyG"
  | "KeyH"
  | "KeyI"
  | "KeyJ"
  | "KeyK"
  | "KeyL"
  | "KeyM"
  | "KeyN"
  | "KeyO"
  | "KeyP"
  | "KeyQ"
  | "KeyR"
  | "KeyS"
  | "KeyT"
  | "KeyU"
  | "KeyV"
  | "KeyW"
  | "KeyX"
  | "KeyY"
  | "KeyZ"
  | "MetaLeft"
  | "MetaRight"
  | "ContextMenu"
  | "NumpadMultiply"
  | "NumpadAdd"
  | "NumpadSubtract"
  | "NumpadDivide"
  | "F1"
  | "F2"
  | "F3"
  | "F4"
  | "F5"
  | "F6"
  | "F7"
  | "F8"
  | "F9"
  | "F10"
  | "F11"
  | "F12"
  | "F13"
  | "F14"
  | "F15"
  | "F16"
  | "F17"
  | "F18"
  | "F19"
  | "F20"
  | "F21"
  | "F22"
  | "F23"
  | "F24"
  | "NumLock"
  | "ScrollLock"
  | "AudioVolumeMute"
  | "AudioVolumeDown"
  | "AudioVolumeUp"
  | "MediaTrackNext"
  | "MediaTrackPrevious"
  | "MediaStop"
  | "MediaPlayPause"
  | "Semicolon"
  | "Equal"
  | "NumpadEqual"
  | "Comma"
  | "Minus"
  | "Period"
  | "Slash"
  | "Backquote"
  | "BracketLeft"
  | "Backslash"
  | "BracketRight"
  | "Quote"
  | "AltGraph"
  | "Props"
  | "Cancel"
  | "Clear"
  | "Shift"
  | "Control"
  | "Alt"
  | "Accept"
  | "ModeChange"
  | " "
  | "Print"
  | "Execute"
  | "\u0000"
  | "a"
  | "b"
  | "c"
  | "d"
  | "e"
  | "f"
  | "g"
  | "h"
  | "i"
  | "j"
  | "k"
  | "l"
  | "m"
  | "n"
  | "o"
  | "p"
  | "q"
  | "r"
  | "s"
  | "t"
  | "u"
  | "v"
  | "w"
  | "x"
  | "y"
  | "z"
  | "Meta"
  | "*"
  | "+"
  | "-"
  | "/"
  | ";"
  | "="
  | ","
  | "."
  | "`"
  | "["
  | "\\"
  | "]"
  | "'"
  | "Attn"
  | "CrSel"
  | "ExSel"
  | "EraseEof"
  | "Play"
  | "ZoomOut"
  | ")"
  | "!"
  | "@"
  | "#"
  | "$"
  | "%"
  | "^"
  | "&"
  | "("
  | "A"
  | "B"
  | "C"
  | "D"
  | "E"
  | "F"
  | "G"
  | "H"
  | "I"
  | "J"
  | "K"
  | "L"
  | "M"
  | "N"
  | "O"
  | "P"
  | "Q"
  | "R"
  | "S"
  | "T"
  | "U"
  | "V"
  | "W"
  | "X"
  | "Y"
  | "Z"
  | ":"
  | "<"
  | "_"
  | ">"
  | "?"
  | "~"
  | "{"
  | "|"
  | "}"
  | '"'
  | "SoftLeft"
  | "SoftRight"
  | "Camera"
  | "Call"
  | "EndCall"
  | "VolumeDown"
  | "VolumeUp";
/**
 * @internal
 */
export declare const keyDefinitions: Readonly<Record<KeyInput, KeyDefinition>>;
