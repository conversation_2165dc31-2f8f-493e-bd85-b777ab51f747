import { u as util } from "./chunk-dac557ba.js";
import "./chunk-0c2d1322.js";
var _extend = util._extend;
var callbackify = util.callbackify;
var debuglog = util.debuglog;
var deprecate = util.deprecate;
var format = util.format;
var inherits = util.inherits;
var inspect = util.inspect;
var isArray = util.isArray;
var isBoolean = util.isBoolean;
var isBuffer = util.isBuffer;
var isDate = util.isDate;
var isError = util.isError;
var isFunction = util.isFunction;
var isNull = util.isNull;
var isNullOrUndefined = util.isNullOrUndefined;
var isNumber = util.isNumber;
var isObject = util.isObject;
var isPrimitive = util.isPrimitive;
var isRegExp = util.isRegExp;
var isString = util.isString;
var isSymbol = util.isSymbol;
var isUndefined = util.isUndefined;
var log = util.log;
var promisify = util.promisify;
export default util;
export { _extend, callbackify, debuglog, deprecate, format, inherits, inspect, isArray, isBoolean, isBuffer, isDate, isError, isFunction, isNull, isNullOrUndefined, isNumber, isObject, isPrimitive, isRegExp, isString, isSymbol, isUndefined, log, promisify };