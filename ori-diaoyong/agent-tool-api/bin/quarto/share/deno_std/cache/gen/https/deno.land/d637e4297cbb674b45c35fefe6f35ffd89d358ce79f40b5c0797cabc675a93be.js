// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { validateBinaryLike } from "./_util.ts";
/**
 * {@linkcode encodeBase58} and {@linkcode decodeBase58} for
 * [base58](https://en.wikipedia.org/wiki/Binary-to-text_encoding#Base58) encoding.
 *
 * This module is browser compatible.
 *
 * @module
 */ // deno-fmt-ignore
const mapBase58 = {
  "1": 0,
  "2": 1,
  "3": 2,
  "4": 3,
  "5": 4,
  "6": 5,
  "7": 6,
  "8": 7,
  "9": 8,
  A: 9,
  B: 10,
  C: 11,
  D: 12,
  E: 13,
  F: 14,
  G: 15,
  H: 16,
  J: 17,
  K: 18,
  L: 19,
  M: 20,
  N: 21,
  P: 22,
  Q: 23,
  R: 24,
  S: 25,
  T: 26,
  U: 27,
  V: 28,
  W: 29,
  X: 30,
  Y: 31,
  Z: 32,
  a: 33,
  b: 34,
  c: 35,
  d: 36,
  e: 37,
  f: 38,
  g: 39,
  h: 40,
  i: 41,
  j: 42,
  k: 43,
  m: 44,
  n: 45,
  o: 46,
  p: 47,
  q: 48,
  r: 49,
  s: 50,
  t: 51,
  u: 52,
  v: 53,
  w: 54,
  x: 55,
  y: 56,
  z: 57
};
const base58alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz".split("");
/**
 * @deprecated (will be removed in 0.210.0) Use a `encodeBase58` instead.
 *
 * Encodes a given Uint8Array, ArrayBuffer or string into draft-mspotny-base58-03 RFC base58 representation:
 * https://tools.ietf.org/id/draft-msporny-base58-01.html#rfc.section.1
 *
 * @param data
 *
 * @returns Encoded value
 */ export const encode = encodeBase58;
/**
 * @deprecated (will be removed in 0.210.0) Use a `decodeBase58` instead.
 *
 * Decodes a given b58 string according to draft-mspotny-base58-03 RFC base58 representation:
 * https://tools.ietf.org/id/draft-msporny-base58-01.html#rfc.section.1
 *
 * @param b58
 *
 * @returns Decoded value
 */ export const decode = decodeBase58;
/**
 * Encodes a given Uint8Array, ArrayBuffer or string into draft-mspotny-base58-03 RFC base58 representation:
 * https://tools.ietf.org/id/draft-msporny-base58-01.html#rfc.section.1
 */ export function encodeBase58(data) {
  const uint8tData = validateBinaryLike(data);
  let length = 0;
  let zeroes = 0;
  // Counting leading zeroes
  let index = 0;
  while(uint8tData[index] === 0){
    zeroes++;
    index++;
  }
  const notZeroUint8Data = uint8tData.slice(index);
  const size = Math.round(uint8tData.length * 138 / 100 + 1);
  const b58Encoding = [];
  notZeroUint8Data.forEach((byte)=>{
    let i = 0;
    let carry = byte;
    for(let reverse_iterator = size - 1; (carry > 0 || i < length) && reverse_iterator !== -1; reverse_iterator--, i++){
      carry += (b58Encoding[reverse_iterator] || 0) * 256;
      b58Encoding[reverse_iterator] = Math.round(carry % 58);
      carry = Math.floor(carry / 58);
    }
    length = i;
  });
  const strResult = Array.from({
    length: b58Encoding.length + zeroes
  });
  if (zeroes > 0) {
    strResult.fill("1", 0, zeroes);
  }
  b58Encoding.forEach((byteValue)=>strResult.push(base58alphabet[byteValue]));
  return strResult.join("");
}
/**
 * Decodes a given b58 string according to draft-mspotny-base58-03 RFC base58 representation:
 * https://tools.ietf.org/id/draft-msporny-base58-01.html#rfc.section.1
 */ export function decodeBase58(b58) {
  const splitInput = b58.trim().split("");
  let length = 0;
  let ones = 0;
  // Counting leading ones
  let index = 0;
  while(splitInput[index] === "1"){
    ones++;
    index++;
  }
  const notZeroData = splitInput.slice(index);
  const size = Math.round(b58.length * 733 / 1000 + 1);
  const output = [];
  notZeroData.forEach((char, idx)=>{
    let carry = mapBase58[char];
    let i = 0;
    if (carry === undefined) {
      throw new Error(`Invalid base58 char at index ${idx} with value ${char}`);
    }
    for(let reverse_iterator = size - 1; (carry > 0 || i < length) && reverse_iterator !== -1; reverse_iterator--, i++){
      carry += 58 * (output[reverse_iterator] || 0);
      output[reverse_iterator] = Math.round(carry % 256);
      carry = Math.floor(carry / 256);
    }
    length = i;
  });
  const validOutput = output.filter((item)=>item !== undefined);
  if (ones > 0) {
    const onesResult = Array.from({
      length: ones
    }).fill(0, 0, ones);
    return new Uint8Array([
      ...onesResult,
      ...validOutput
    ]);
  }
  return new Uint8Array(validOutput);
}
//# sourceMappingURL=data:application/json;base64,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