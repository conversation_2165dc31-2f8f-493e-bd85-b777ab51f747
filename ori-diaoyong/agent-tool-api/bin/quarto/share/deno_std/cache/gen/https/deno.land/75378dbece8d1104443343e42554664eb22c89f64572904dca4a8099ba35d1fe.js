// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given transformer to all values in the given record and returns a
 * new record containing the resulting keys associated to the last value that
 * produced them.
 *
 * @example
 * ```ts
 * import { mapValues } from "https://deno.land/std@$STD_VERSION/collections/map_values.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const usersById = {
 *   "a5ec": { name: "<PERSON>sch<PERSON>" },
 *   "de4f": { name: "<PERSON>" },
 * };
 * const namesById = mapValues(usersById, (it) => it.name);
 *
 * assertEquals(
 *   namesById,
 *   {
 *     "a5ec": "Mischa",
 *     "de4f": "<PERSON>",
 *   },
 * );
 * ```
 */ export function mapValues(record, transformer) {
  const ret = {};
  const entries = Object.entries(record);
  for (const [key, value] of entries){
    const mappedValue = transformer(value);
    ret[key] = mappedValue;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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