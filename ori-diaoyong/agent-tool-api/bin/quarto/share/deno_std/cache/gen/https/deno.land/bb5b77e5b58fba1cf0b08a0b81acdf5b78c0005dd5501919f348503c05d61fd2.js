// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Builds all possible orders of all elements in the given array
 * Ignores equality of elements, meaning this will always return the same
 * number of permutations for a given length of input.
 *
 * @example
 * ```ts
 * import { permutations } from "https://deno.land/std@$STD_VERSION/collections/permutations.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [ 1, 2 ];
 * const windows = permutations(numbers);
 *
 * assertEquals(windows, [
 *   [ 1, 2 ],
 *   [ 2, 1 ],
 * ]);
 * ```
 */ export function permutations(inputArray) {
  const ret = [];
  const array = [
    ...inputArray
  ];
  const k = array.length;
  if (k === 0) {
    return ret;
  }
  // Heap's Algorithm
  const c = new Array(k).fill(0);
  ret.push([
    ...array
  ]);
  let i = 1;
  while(i < k){
    if (c[i] < i) {
      if (i % 2 === 0) {
        [array[0], array[i]] = [
          array[i],
          array[0]
        ];
      } else {
        [array[c[i]], array[i]] = [
          array[i],
          array[c[i]]
        ];
      }
      ret.push([
        ...array
      ]);
      c[i] += 1;
      i = 1;
    } else {
      c[i] = 0;
      i += 1;
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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