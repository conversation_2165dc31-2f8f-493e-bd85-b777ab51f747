// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a tuple of two records with the first one containing all entries of
 * the given record that match the given predicate and the second one containing
 * all that do not.
 *
 * @example
 * ```ts
 * import { partitionEntries } from "https://deno.land/std@$STD_VERSION/collections/partition_entries.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const menu = {
 *   "Salad": 11,
 *   "Soup": 8,
 *   "Pasta": 13,
 * } as const;
 * const myOptions = partitionEntries(
 *   menu,
 *   ([item, price]) => item !== "Pasta" && price < 10,
 * );
 *
 * assertEquals(
 *   myOptions,
 *   [
 *     { "Soup": 8 },
 *     { "Salad": 11, "Pasta": 13 },
 *   ],
 * );
 * ```
 */ export function partitionEntries(record, predicate) {
  const match = {};
  const rest = {};
  const entries = Object.entries(record);
  for (const [key, value] of entries){
    if (predicate([
      key,
      value
    ])) {
      match[key] = value;
    } else {
      rest[key] = value;
    }
  }
  return [
    match,
    rest
  ];
}
//# sourceMappingURL=data:application/json;base64,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