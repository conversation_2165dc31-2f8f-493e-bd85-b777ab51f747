// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { join } from "../path/join.ts";
import { toPathString } from "./_util.ts";
/**
 * Ensures that a directory is empty.
 * Deletes directory contents if the directory is not empty.
 * If the directory does not exist, it is created.
 * The directory itself is not deleted.
 * Requires the `--allow-read` and `--allow-write` flag.
 *
 * @example
 * ```ts
 * import { emptyDir } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * emptyDir("./foo"); // returns a promise
 * ```
 */ export async function emptyDir(dir) {
  try {
    const items = [];
    for await (const dirEntry of Deno.readDir(dir)){
      items.push(dirEntry);
    }
    await Promise.all(items.map((item)=>{
      if (item && item.name) {
        const filepath = join(toPathString(dir), item.name);
        return Deno.remove(filepath, {
          recursive: true
        });
      }
    }));
  } catch (err) {
    if (!(err instanceof Deno.errors.NotFound)) {
      throw err;
    }
    // if not exist. then create it
    await Deno.mkdir(dir, {
      recursive: true
    });
  }
}
/**
 * Ensures that a directory is empty.
 * Deletes directory contents if the directory is not empty.
 * If the directory does not exist, it is created.
 * The directory itself is not deleted.
 * Requires the `--allow-read` and `--allow-write` flag.
 *
 * @example
 * ```ts
 * import { emptyDirSync } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * emptyDirSync("./foo"); // void
 * ```
 */ export function emptyDirSync(dir) {
  try {
    const items = [
      ...Deno.readDirSync(dir)
    ];
    // If the directory exists, remove all entries inside it.
    while(items.length){
      const item = items.shift();
      if (item && item.name) {
        const filepath = join(toPathString(dir), item.name);
        Deno.removeSync(filepath, {
          recursive: true
        });
      }
    }
  } catch (err) {
    if (!(err instanceof Deno.errors.NotFound)) {
      throw err;
    }
    // if not exist. then create it
    Deno.mkdirSync(dir, {
      recursive: true
    });
  }
}
//# sourceMappingURL=data:application/json;base64,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