// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// Copyright (c) <PERSON>. MIT license
/**
 * Extracts
 * [front matter](https://daily-dev-tips.com/posts/what-exactly-is-frontmatter/)
 * from strings.
 *
 * {@linkcode createExtractor} and {@linkcode test} functions
 * to handle many forms of front matter.
 *
 * Adapted from
 * [jxson/front-matter](https://github.com/jxson/front-matter/blob/36f139ef797bd9e5196a9ede03ef481d7fbca18e/index.js).
 *
 * Supported formats:
 *
 * - [`YAML`](./front_matter/yaml.ts)
 * - [`TOML`](./front_matter/toml.ts)
 * - [`JSON`](./front_matter/json.ts)
 *
 * ### Basic usage
 *
 * example.md
 *
 * ```markdown
 * ---
 * module: front_matter
 * tags:
 *   - yaml
 *   - toml
 *   - json
 * ---
 *
 * deno is awesome
 * ```
 *
 * example.ts
 *
 * ```ts
 * import {
 *   extract,
 *   test,
 * } from "https://deno.land/std@$STD_VERSION/front_matter/any.ts";
 *
 * const str = await Deno.readTextFile("./example.md");
 *
 * if (test(str)) {
 *   console.log(extract(str));
 * } else {
 *   console.log("document doesn't contain front matter");
 * }
 * ```
 *
 * ```sh
 * $ deno run ./example.ts
 * {
 *   frontMatter: "module: front_matter\ntags:\n  - yaml\n  - toml\n  - json",
 *   body: "deno is awesome",
 *   attrs: { module: "front_matter", tags: [ "yaml", "toml", "json" ] }
 * }
 * ```
 *
 * The above example recognizes any of the supported formats, extracts metadata and
 * parses accordingly. Please note that in this case both the [YAML](#yaml) and
 * [TOML](#toml) parsers will be imported as dependencies.
 *
 * If you need only one specific format then you can import the file named
 * respectively from [here](./front_matter).
 *
 * ### Advanced usage
 *
 * ```ts
 * import {
 *   createExtractor,
 *   Format,
 *   Parser,
 *   test as _test,
 * } from "https://deno.land/std@$STD_VERSION/front_matter/mod.ts";
 * import { parse } from "https://deno.land/std@$STD_VERSION/toml/parse.ts";
 *
 * const extract = createExtractor({
 *   [Format.TOML]: parse as Parser,
 *   [Format.JSON]: JSON.parse as Parser,
 * });
 *
 * export function test(str: string): boolean {
 *   return _test(str, [Format.TOML, Format.JSON]);
 * }
 * ```
 *
 * In this setup `extract()` and `test()` will work with TOML and JSON and only.
 * This way the YAML parser is not loaded if not needed. You can cherry-pick which
 * combination of formats are you supporting based on your needs.
 *
 * ### Delimiters
 *
 * #### YAML
 *
 * ```markdown
 * ---
 * these: are
 * ---
 * ```
 *
 * ```markdown
 * ---yaml
 * all: recognized
 * ---
 * ```
 *
 * ```markdown
 * = yaml =
 * as: yaml
 * = yaml =
 * ```
 *
 * #### TOML
 *
 * ```markdown
 * ---toml
 * this = 'is'
 * ---
 * ```
 *
 * ```markdown
 * = toml =
 * parsed = 'as'
 * toml = 'data'
 * = toml =
 * ```
 *
 * ```markdown
 * +++
 * is = 'that'
 * not = 'cool?'
 * +++
 * ```
 *
 * #### JSON
 *
 * ```markdown
 * ---json
 * {
 *   "and": "this"
 * }
 * ---
 * ```
 *
 * ```markdown
 * {
 *   "is": "JSON"
 * }
 * ```
 *
 * @module
 */ export * from "./create_extractor.ts";
export * from "./test.ts";
export { Format } from "./_formats.ts";
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2Zyb250X21hdHRlci9tb2QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIENvcHlyaWdodCAoYykgSmFzb24gQ2FtcGJlbGwuIE1JVCBsaWNlbnNlXG5cbi8qKlxuICogRXh0cmFjdHNcbiAqIFtmcm9udCBtYXR0ZXJdKGh0dHBzOi8vZGFpbHktZGV2LXRpcHMuY29tL3Bvc3RzL3doYXQtZXhhY3RseS1pcy1mcm9udG1hdHRlci8pXG4gKiBmcm9tIHN0cmluZ3MuXG4gKlxuICoge0BsaW5rY29kZSBjcmVhdGVFeHRyYWN0b3J9IGFuZCB7QGxpbmtjb2RlIHRlc3R9IGZ1bmN0aW9uc1xuICogdG8gaGFuZGxlIG1hbnkgZm9ybXMgb2YgZnJvbnQgbWF0dGVyLlxuICpcbiAqIEFkYXB0ZWQgZnJvbVxuICogW2p4c29uL2Zyb250LW1hdHRlcl0oaHR0cHM6Ly9naXRodWIuY29tL2p4c29uL2Zyb250LW1hdHRlci9ibG9iLzM2ZjEzOWVmNzk3YmQ5ZTUxOTZhOWVkZTAzZWY0ODFkN2ZiY2ExOGUvaW5kZXguanMpLlxuICpcbiAqIFN1cHBvcnRlZCBmb3JtYXRzOlxuICpcbiAqIC0gW2BZQU1MYF0oLi9mcm9udF9tYXR0ZXIveWFtbC50cylcbiAqIC0gW2BUT01MYF0oLi9mcm9udF9tYXR0ZXIvdG9tbC50cylcbiAqIC0gW2BKU09OYF0oLi9mcm9udF9tYXR0ZXIvanNvbi50cylcbiAqXG4gKiAjIyMgQmFzaWMgdXNhZ2VcbiAqXG4gKiBleGFtcGxlLm1kXG4gKlxuICogYGBgbWFya2Rvd25cbiAqIC0tLVxuICogbW9kdWxlOiBmcm9udF9tYXR0ZXJcbiAqIHRhZ3M6XG4gKiAgIC0geWFtbFxuICogICAtIHRvbWxcbiAqICAgLSBqc29uXG4gKiAtLS1cbiAqXG4gKiBkZW5vIGlzIGF3ZXNvbWVcbiAqIGBgYFxuICpcbiAqIGV4YW1wbGUudHNcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHtcbiAqICAgZXh0cmFjdCxcbiAqICAgdGVzdCxcbiAqIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vZnJvbnRfbWF0dGVyL2FueS50c1wiO1xuICpcbiAqIGNvbnN0IHN0ciA9IGF3YWl0IERlbm8ucmVhZFRleHRGaWxlKFwiLi9leGFtcGxlLm1kXCIpO1xuICpcbiAqIGlmICh0ZXN0KHN0cikpIHtcbiAqICAgY29uc29sZS5sb2coZXh0cmFjdChzdHIpKTtcbiAqIH0gZWxzZSB7XG4gKiAgIGNvbnNvbGUubG9nKFwiZG9jdW1lbnQgZG9lc24ndCBjb250YWluIGZyb250IG1hdHRlclwiKTtcbiAqIH1cbiAqIGBgYFxuICpcbiAqIGBgYHNoXG4gKiAkIGRlbm8gcnVuIC4vZXhhbXBsZS50c1xuICoge1xuICogICBmcm9udE1hdHRlcjogXCJtb2R1bGU6IGZyb250X21hdHRlclxcbnRhZ3M6XFxuICAtIHlhbWxcXG4gIC0gdG9tbFxcbiAgLSBqc29uXCIsXG4gKiAgIGJvZHk6IFwiZGVubyBpcyBhd2Vzb21lXCIsXG4gKiAgIGF0dHJzOiB7IG1vZHVsZTogXCJmcm9udF9tYXR0ZXJcIiwgdGFnczogWyBcInlhbWxcIiwgXCJ0b21sXCIsIFwianNvblwiIF0gfVxuICogfVxuICogYGBgXG4gKlxuICogVGhlIGFib3ZlIGV4YW1wbGUgcmVjb2duaXplcyBhbnkgb2YgdGhlIHN1cHBvcnRlZCBmb3JtYXRzLCBleHRyYWN0cyBtZXRhZGF0YSBhbmRcbiAqIHBhcnNlcyBhY2NvcmRpbmdseS4gUGxlYXNlIG5vdGUgdGhhdCBpbiB0aGlzIGNhc2UgYm90aCB0aGUgW1lBTUxdKCN5YW1sKSBhbmRcbiAqIFtUT01MXSgjdG9tbCkgcGFyc2VycyB3aWxsIGJlIGltcG9ydGVkIGFzIGRlcGVuZGVuY2llcy5cbiAqXG4gKiBJZiB5b3UgbmVlZCBvbmx5IG9uZSBzcGVjaWZpYyBmb3JtYXQgdGhlbiB5b3UgY2FuIGltcG9ydCB0aGUgZmlsZSBuYW1lZFxuICogcmVzcGVjdGl2ZWx5IGZyb20gW2hlcmVdKC4vZnJvbnRfbWF0dGVyKS5cbiAqXG4gKiAjIyMgQWR2YW5jZWQgdXNhZ2VcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHtcbiAqICAgY3JlYXRlRXh0cmFjdG9yLFxuICogICBGb3JtYXQsXG4gKiAgIFBhcnNlcixcbiAqICAgdGVzdCBhcyBfdGVzdCxcbiAqIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vZnJvbnRfbWF0dGVyL21vZC50c1wiO1xuICogaW1wb3J0IHsgcGFyc2UgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi90b21sL3BhcnNlLnRzXCI7XG4gKlxuICogY29uc3QgZXh0cmFjdCA9IGNyZWF0ZUV4dHJhY3Rvcih7XG4gKiAgIFtGb3JtYXQuVE9NTF06IHBhcnNlIGFzIFBhcnNlcixcbiAqICAgW0Zvcm1hdC5KU09OXTogSlNPTi5wYXJzZSBhcyBQYXJzZXIsXG4gKiB9KTtcbiAqXG4gKiBleHBvcnQgZnVuY3Rpb24gdGVzdChzdHI6IHN0cmluZyk6IGJvb2xlYW4ge1xuICogICByZXR1cm4gX3Rlc3Qoc3RyLCBbRm9ybWF0LlRPTUwsIEZvcm1hdC5KU09OXSk7XG4gKiB9XG4gKiBgYGBcbiAqXG4gKiBJbiB0aGlzIHNldHVwIGBleHRyYWN0KClgIGFuZCBgdGVzdCgpYCB3aWxsIHdvcmsgd2l0aCBUT01MIGFuZCBKU09OIGFuZCBvbmx5LlxuICogVGhpcyB3YXkgdGhlIFlBTUwgcGFyc2VyIGlzIG5vdCBsb2FkZWQgaWYgbm90IG5lZWRlZC4gWW91IGNhbiBjaGVycnktcGljayB3aGljaFxuICogY29tYmluYXRpb24gb2YgZm9ybWF0cyBhcmUgeW91IHN1cHBvcnRpbmcgYmFzZWQgb24geW91ciBuZWVkcy5cbiAqXG4gKiAjIyMgRGVsaW1pdGVyc1xuICpcbiAqICMjIyMgWUFNTFxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiAtLS1cbiAqIHRoZXNlOiBhcmVcbiAqIC0tLVxuICogYGBgXG4gKlxuICogYGBgbWFya2Rvd25cbiAqIC0tLXlhbWxcbiAqIGFsbDogcmVjb2duaXplZFxuICogLS0tXG4gKiBgYGBcbiAqXG4gKiBgYGBtYXJrZG93blxuICogPSB5YW1sID1cbiAqIGFzOiB5YW1sXG4gKiA9IHlhbWwgPVxuICogYGBgXG4gKlxuICogIyMjIyBUT01MXG4gKlxuICogYGBgbWFya2Rvd25cbiAqIC0tLXRvbWxcbiAqIHRoaXMgPSAnaXMnXG4gKiAtLS1cbiAqIGBgYFxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiA9IHRvbWwgPVxuICogcGFyc2VkID0gJ2FzJ1xuICogdG9tbCA9ICdkYXRhJ1xuICogPSB0b21sID1cbiAqIGBgYFxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiArKytcbiAqIGlzID0gJ3RoYXQnXG4gKiBub3QgPSAnY29vbD8nXG4gKiArKytcbiAqIGBgYFxuICpcbiAqICMjIyMgSlNPTlxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiAtLS1qc29uXG4gKiB7XG4gKiAgIFwiYW5kXCI6IFwidGhpc1wiXG4gKiB9XG4gKiAtLS1cbiAqIGBgYFxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiB7XG4gKiAgIFwiaXNcIjogXCJKU09OXCJcbiAqIH1cbiAqIGBgYFxuICpcbiAqIEBtb2R1bGVcbiAqL1xuXG5leHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVfZXh0cmFjdG9yLnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90ZXN0LnRzXCI7XG5leHBvcnQgeyBGb3JtYXQgfSBmcm9tIFwiLi9fZm9ybWF0cy50c1wiO1xuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSw0Q0FBNEM7QUFFNUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBd0pDLEdBRUQsY0FBYyx3QkFBd0I7QUFDdEMsY0FBYyxZQUFZO0FBQzFCLFNBQVMsTUFBTSxRQUFRLGdCQUFnQiJ9