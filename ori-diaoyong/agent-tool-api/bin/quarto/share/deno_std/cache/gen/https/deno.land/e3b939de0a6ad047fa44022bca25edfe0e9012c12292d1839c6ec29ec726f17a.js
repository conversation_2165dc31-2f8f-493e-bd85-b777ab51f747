// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Contains the functions {@linkcode accepts}, {@linkcode acceptsEncodings}, and
 * {@linkcode acceptsLanguages} to provide content negotiation capabilities.
 *
 * @module
 */ import { preferredEncodings } from "./_negotiation/encoding.ts";
import { preferredLanguages } from "./_negotiation/language.ts";
import { preferredMediaTypes } from "./_negotiation/media_type.ts";
export function accepts(request, ...types) {
  const accept = request.headers.get("accept");
  return types.length ? accept ? preferredMediaTypes(accept, types)[0] : types[0] : accept ? preferredMediaTypes(accept) : [
    "*/*"
  ];
}
export function acceptsEncodings(request, ...encodings) {
  const acceptEncoding = request.headers.get("accept-encoding");
  return encodings.length ? acceptEncoding ? preferredEncodings(acceptEncoding, encodings)[0] : encodings[0] : acceptEncoding ? preferredEncodings(acceptEncoding) : [
    "*"
  ];
}
export function acceptsLanguages(request, ...langs) {
  const acceptLanguage = request.headers.get("accept-language");
  return langs.length ? acceptLanguage ? preferredLanguages(acceptLanguage, langs)[0] : langs[0] : acceptLanguage ? preferredLanguages(acceptLanguage) : [
    "*"
  ];
}
//# sourceMappingURL=data:application/json;base64,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