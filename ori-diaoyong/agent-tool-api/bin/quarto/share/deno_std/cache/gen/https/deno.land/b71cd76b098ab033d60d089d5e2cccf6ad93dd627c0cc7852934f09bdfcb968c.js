// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new array that drops all elements in the given collection until the
 * last element that does not match the given predicate
 *
 * @example
 * ```ts
 * import { dropLastWhile } from "https://deno.land/std@$STD_VERSION/collections/drop_last_while.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [22, 30, 44];
 *
 * const notFortyFour = dropLastWhile(numbers, (i) => i !== 44);
 *
 * assertEquals(
 *   notFortyFour,
 *   [22, 30],
 * );
 * ```
 */ export function dropLastWhile(array, predicate) {
  let offset = array.length;
  while(0 < offset && predicate(array[offset - 1]))offset--;
  return array.slice(0, offset);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2Ryb3BfbGFzdF93aGlsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG4vKipcbiAqIFJldHVybnMgYSBuZXcgYXJyYXkgdGhhdCBkcm9wcyBhbGwgZWxlbWVudHMgaW4gdGhlIGdpdmVuIGNvbGxlY3Rpb24gdW50aWwgdGhlXG4gKiBsYXN0IGVsZW1lbnQgdGhhdCBkb2VzIG5vdCBtYXRjaCB0aGUgZ2l2ZW4gcHJlZGljYXRlXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyBkcm9wTGFzdFdoaWxlIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vY29sbGVjdGlvbnMvZHJvcF9sYXN0X3doaWxlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IG51bWJlcnMgPSBbMjIsIDMwLCA0NF07XG4gKlxuICogY29uc3Qgbm90Rm9ydHlGb3VyID0gZHJvcExhc3RXaGlsZShudW1iZXJzLCAoaSkgPT4gaSAhPT0gNDQpO1xuICpcbiAqIGFzc2VydEVxdWFscyhcbiAqICAgbm90Rm9ydHlGb3VyLFxuICogICBbMjIsIDMwXSxcbiAqICk7XG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRyb3BMYXN0V2hpbGU8VD4oXG4gIGFycmF5OiByZWFkb25seSBUW10sXG4gIHByZWRpY2F0ZTogKGVsOiBUKSA9PiBib29sZWFuLFxuKTogVFtdIHtcbiAgbGV0IG9mZnNldCA9IGFycmF5Lmxlbmd0aDtcbiAgd2hpbGUgKDAgPCBvZmZzZXQgJiYgcHJlZGljYXRlKGFycmF5W29mZnNldCAtIDFdKSkgb2Zmc2V0LS07XG5cbiAgcmV0dXJuIGFycmF5LnNsaWNlKDAsIG9mZnNldCk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBa0JDLEdBQ0QsT0FBTyxTQUFTLGNBQ2QsS0FBbUIsRUFDbkIsU0FBNkI7RUFFN0IsSUFBSSxTQUFTLE1BQU0sTUFBTTtFQUN6QixNQUFPLElBQUksVUFBVSxVQUFVLEtBQUssQ0FBQyxTQUFTLEVBQUUsRUFBRztFQUVuRCxPQUFPLE1BQU0sS0FBSyxDQUFDLEdBQUc7QUFDeEIifQ==