// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// Copyright (c) 2014 Jameson Little. MIT License.
// This module is browser compatible.
import { validateBinaryLike } from "./_util.ts";
/**
 * {@linkcode encode} and {@linkcode decode} for
 * [base32](https://en.wikipedia.org/wiki/Base32) encoding.
 *
 * Modified from https://github.com/beatgammit/base64-js
 *
 * This module is browser compatible.
 *
 * @example
 * ```ts
 * import {
 *   decodeBase32,
 *   encodeBase32,
 * } from "https://deno.land/std@$STD_VERSION/encoding/base32.ts";
 *
 * const b32Repr = "RC2E6GA=";
 *
 * const binaryData = decodeBase32(b32Repr);
 * console.log(binaryData);
 * // => Uint8Array [ 136, 180, 79, 24 ]
 *
 * console.log(encodeBase32(binaryData));
 * // => RC2E6GA=
 * ```
 *
 * @module
 */ const lookup = [];
const revLookup = [];
// RFC4648 base32
const code = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
for(let i = 0, len = code.length; i < len; ++i){
  lookup[i] = code[i];
  revLookup[code.charCodeAt(i)] = i;
}
const placeHolderPadLookup = [
  0,
  1,
  ,
  2,
  3,
  ,
  4
];
function _getPadLen(placeHoldersLen) {
  const maybeLen = placeHolderPadLookup[placeHoldersLen];
  if (typeof maybeLen !== "number") {
    throw new Error("Invalid pad length");
  }
  return maybeLen;
}
function getLens(b32) {
  const len = b32.length;
  if (len % 8 > 0) {
    throw new Error("Invalid string. Length must be a multiple of 8");
  }
  let validLen = b32.indexOf("=");
  if (validLen === -1) validLen = len;
  const placeHoldersLen = validLen === len ? 0 : 8 - validLen % 8;
  return [
    validLen,
    placeHoldersLen
  ];
}
/**
 * Returns number of bytes encoded in the given RFC4648 base32 string input.
 * @param b32
 */ export function byteLength(b32) {
  const [validLen, placeHoldersLen] = getLens(b32);
  return _byteLength(validLen, placeHoldersLen);
}
function _byteLength(validLen, placeHoldersLen) {
  return (validLen + placeHoldersLen) * 5 / 8 - _getPadLen(placeHoldersLen);
}
/**
 * @deprecated (will be removed in 0.210.0) Use a `decodeBase32` instead.
 *
 * Decodes a given RFC4648 base32 encoded string.
 * @param b32
 */ export const decode = decodeBase32;
/**
 * Decodes a given RFC4648 base32 encoded string.
 * @param b32
 */ export function decodeBase32(b32) {
  let tmp;
  const [validLen, placeHoldersLen] = getLens(b32);
  const arr = new Uint8Array(_byteLength(validLen, placeHoldersLen));
  let curByte = 0;
  // if there are placeholders, only get up to the last complete 8 chars
  const len = placeHoldersLen > 0 ? validLen - 8 : validLen;
  let i;
  for(i = 0; i < len; i += 8){
    tmp = revLookup[b32.charCodeAt(i)] << 20 | revLookup[b32.charCodeAt(i + 1)] << 15 | revLookup[b32.charCodeAt(i + 2)] << 10 | revLookup[b32.charCodeAt(i + 3)] << 5 | revLookup[b32.charCodeAt(i + 4)];
    arr[curByte++] = tmp >> 17 & 0xff;
    arr[curByte++] = tmp >> 9 & 0xff;
    arr[curByte++] = tmp >> 1 & 0xff;
    tmp = (tmp & 1) << 15 | revLookup[b32.charCodeAt(i + 5)] << 10 | revLookup[b32.charCodeAt(i + 6)] << 5 | revLookup[b32.charCodeAt(i + 7)];
    arr[curByte++] = tmp >> 8 & 0xff;
    arr[curByte++] = tmp & 0xff;
  }
  if (placeHoldersLen === 1) {
    tmp = revLookup[b32.charCodeAt(i)] << 20 | revLookup[b32.charCodeAt(i + 1)] << 15 | revLookup[b32.charCodeAt(i + 2)] << 10 | revLookup[b32.charCodeAt(i + 3)] << 5 | revLookup[b32.charCodeAt(i + 4)];
    arr[curByte++] = tmp >> 17 & 0xff;
    arr[curByte++] = tmp >> 9 & 0xff;
    arr[curByte++] = tmp >> 1 & 0xff;
    tmp = (tmp & 1) << 7 | revLookup[b32.charCodeAt(i + 5)] << 2 | revLookup[b32.charCodeAt(i + 6)] >> 3;
    arr[curByte++] = tmp & 0xff;
  } else if (placeHoldersLen === 3) {
    tmp = revLookup[b32.charCodeAt(i)] << 19 | revLookup[b32.charCodeAt(i + 1)] << 14 | revLookup[b32.charCodeAt(i + 2)] << 9 | revLookup[b32.charCodeAt(i + 3)] << 4 | revLookup[b32.charCodeAt(i + 4)] >> 1;
    arr[curByte++] = tmp >> 16 & 0xff;
    arr[curByte++] = tmp >> 8 & 0xff;
    arr[curByte++] = tmp & 0xff;
  } else if (placeHoldersLen === 4) {
    tmp = revLookup[b32.charCodeAt(i)] << 11 | revLookup[b32.charCodeAt(i + 1)] << 6 | revLookup[b32.charCodeAt(i + 2)] << 1 | revLookup[b32.charCodeAt(i + 3)] >> 4;
    arr[curByte++] = tmp >> 8 & 0xff;
    arr[curByte++] = tmp & 0xff;
  } else if (placeHoldersLen === 6) {
    tmp = revLookup[b32.charCodeAt(i)] << 3 | revLookup[b32.charCodeAt(i + 1)] >> 2;
    arr[curByte++] = tmp & 0xff;
  }
  return arr;
}
function encodeChunk(uint8, start, end) {
  let tmp;
  const output = [];
  for(let i = start; i < end; i += 5){
    tmp = uint8[i] << 16 & 0xff0000 | uint8[i + 1] << 8 & 0xff00 | uint8[i + 2] & 0xff;
    output.push(lookup[tmp >> 19 & 0x1f]);
    output.push(lookup[tmp >> 14 & 0x1f]);
    output.push(lookup[tmp >> 9 & 0x1f]);
    output.push(lookup[tmp >> 4 & 0x1f]);
    tmp = (tmp & 0xf) << 16 | uint8[i + 3] << 8 & 0xff00 | uint8[i + 4] & 0xff;
    output.push(lookup[tmp >> 15 & 0x1f]);
    output.push(lookup[tmp >> 10 & 0x1f]);
    output.push(lookup[tmp >> 5 & 0x1f]);
    output.push(lookup[tmp & 0x1f]);
  }
  return output.join("");
}
/**
 * @deprecated (will be removed in 0.210.0) Use a `encodeBase32` instead.
 *
 * Encodes a given Uint8Array into RFC4648 base32 representation
 * @param uint8
 */ export const encode = encodeBase32;
/**
 * Encodes a given Uint8Array into RFC4648 base32 representation
 */ export function encodeBase32(data) {
  const uint8 = validateBinaryLike(data);
  let tmp;
  const len = uint8.length;
  const extraBytes = len % 5;
  const parts = [];
  const maxChunkLength = 16385; // must be multiple of 5
  const len2 = len - extraBytes;
  // go through the array every 5 bytes, we'll deal with trailing stuff later
  for(let i = 0; i < len2; i += maxChunkLength){
    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));
  }
  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 4) {
    tmp = (uint8[len2] & 0xff) << 16 | (uint8[len2 + 1] & 0xff) << 8 | uint8[len2 + 2] & 0xff;
    parts.push(lookup[tmp >> 19 & 0x1f]);
    parts.push(lookup[tmp >> 14 & 0x1f]);
    parts.push(lookup[tmp >> 9 & 0x1f]);
    parts.push(lookup[tmp >> 4 & 0x1f]);
    tmp = (tmp & 0xf) << 11 | uint8[len2 + 3] << 3;
    parts.push(lookup[tmp >> 10 & 0x1f]);
    parts.push(lookup[tmp >> 5 & 0x1f]);
    parts.push(lookup[tmp & 0x1f]);
    parts.push("=");
  } else if (extraBytes === 3) {
    tmp = (uint8[len2] & 0xff) << 17 | (uint8[len2 + 1] & 0xff) << 9 | (uint8[len2 + 2] & 0xff) << 1;
    parts.push(lookup[tmp >> 20 & 0x1f]);
    parts.push(lookup[tmp >> 15 & 0x1f]);
    parts.push(lookup[tmp >> 10 & 0x1f]);
    parts.push(lookup[tmp >> 5 & 0x1f]);
    parts.push(lookup[tmp & 0x1f]);
    parts.push("===");
  } else if (extraBytes === 2) {
    tmp = (uint8[len2] & 0xff) << 12 | (uint8[len2 + 1] & 0xff) << 4;
    parts.push(lookup[tmp >> 15 & 0x1f]);
    parts.push(lookup[tmp >> 10 & 0x1f]);
    parts.push(lookup[tmp >> 5 & 0x1f]);
    parts.push(lookup[tmp & 0x1f]);
    parts.push("====");
  } else if (extraBytes === 1) {
    tmp = (uint8[len2] & 0xff) << 2;
    parts.push(lookup[tmp >> 5 & 0x1f]);
    parts.push(lookup[tmp & 0x1f]);
    parts.push("======");
  }
  return parts.join("");
}
//# sourceMappingURL=data:application/json;base64,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