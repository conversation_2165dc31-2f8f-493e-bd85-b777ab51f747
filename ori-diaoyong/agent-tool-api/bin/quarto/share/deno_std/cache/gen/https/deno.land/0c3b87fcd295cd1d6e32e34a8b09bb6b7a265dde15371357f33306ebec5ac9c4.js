// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns all distinct elements in the given array, preserving order by first
 * occurrence.
 *
 * @example
 * ```ts
 * import { distinct } from "https://deno.land/std@$STD_VERSION/collections/distinct.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [3, 2, 5, 2, 5];
 * const distinctNumbers = distinct(numbers);
 *
 * assertEquals(distinctNumbers, [3, 2, 5]);
 * ```
 */ export function distinct(array) {
  const set = new Set(array);
  return Array.from(set);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2Rpc3RpbmN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogUmV0dXJucyBhbGwgZGlzdGluY3QgZWxlbWVudHMgaW4gdGhlIGdpdmVuIGFycmF5LCBwcmVzZXJ2aW5nIG9yZGVyIGJ5IGZpcnN0XG4gKiBvY2N1cnJlbmNlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgZGlzdGluY3QgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9jb2xsZWN0aW9ucy9kaXN0aW5jdC50c1wiO1xuICogaW1wb3J0IHsgYXNzZXJ0RXF1YWxzIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vYXNzZXJ0L2Fzc2VydF9lcXVhbHMudHNcIjtcbiAqXG4gKiBjb25zdCBudW1iZXJzID0gWzMsIDIsIDUsIDIsIDVdO1xuICogY29uc3QgZGlzdGluY3ROdW1iZXJzID0gZGlzdGluY3QobnVtYmVycyk7XG4gKlxuICogYXNzZXJ0RXF1YWxzKGRpc3RpbmN0TnVtYmVycywgWzMsIDIsIDVdKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gZGlzdGluY3Q8VD4oYXJyYXk6IEl0ZXJhYmxlPFQ+KTogVFtdIHtcbiAgY29uc3Qgc2V0ID0gbmV3IFNldChhcnJheSk7XG5cbiAgcmV0dXJuIEFycmF5LmZyb20oc2V0KTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDOzs7Ozs7Ozs7Ozs7OztDQWNDLEdBQ0QsT0FBTyxTQUFTLFNBQVksS0FBa0I7RUFDNUMsTUFBTSxNQUFNLElBQUksSUFBSTtFQUVwQixPQUFPLE1BQU0sSUFBSSxDQUFDO0FBQ3BCIn0=