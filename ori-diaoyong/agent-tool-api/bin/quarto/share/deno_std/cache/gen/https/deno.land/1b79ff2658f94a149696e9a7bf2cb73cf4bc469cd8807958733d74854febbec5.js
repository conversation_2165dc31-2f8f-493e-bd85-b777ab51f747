// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given selector to all elements in the given collection and
 * calculates the sum of the results.
 *
 * @example
 * ```ts
 * import { sumOf } from "https://deno.land/std@$STD_VERSION/collections/sum_of.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = [
 *   { name: "<PERSON>", age: 34 },
 *   { name: "<PERSON>", age: 42 },
 *   { name: "<PERSON>", age: 23 },
 * ];
 * const totalAge = sumOf(people, (i) => i.age);
 *
 * assertEquals(totalAge, 99);
 * ```
 */ export function sumOf(array, selector) {
  let sum = 0;
  for (const i of array){
    sum += selector(i);
  }
  return sum;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3N1bV9vZi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG4vKipcbiAqIEFwcGxpZXMgdGhlIGdpdmVuIHNlbGVjdG9yIHRvIGFsbCBlbGVtZW50cyBpbiB0aGUgZ2l2ZW4gY29sbGVjdGlvbiBhbmRcbiAqIGNhbGN1bGF0ZXMgdGhlIHN1bSBvZiB0aGUgcmVzdWx0cy5cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIGltcG9ydCB7IHN1bU9mIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vY29sbGVjdGlvbnMvc3VtX29mLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IHBlb3BsZSA9IFtcbiAqICAgeyBuYW1lOiBcIkFubmFcIiwgYWdlOiAzNCB9LFxuICogICB7IG5hbWU6IFwiS2ltXCIsIGFnZTogNDIgfSxcbiAqICAgeyBuYW1lOiBcIkpvaG5cIiwgYWdlOiAyMyB9LFxuICogXTtcbiAqIGNvbnN0IHRvdGFsQWdlID0gc3VtT2YocGVvcGxlLCAoaSkgPT4gaS5hZ2UpO1xuICpcbiAqIGFzc2VydEVxdWFscyh0b3RhbEFnZSwgOTkpO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdW1PZjxUPihcbiAgYXJyYXk6IEl0ZXJhYmxlPFQ+LFxuICBzZWxlY3RvcjogKGVsOiBUKSA9PiBudW1iZXIsXG4pOiBudW1iZXIge1xuICBsZXQgc3VtID0gMDtcblxuICBmb3IgKGNvbnN0IGkgb2YgYXJyYXkpIHtcbiAgICBzdW0gKz0gc2VsZWN0b3IoaSk7XG4gIH1cblxuICByZXR1cm4gc3VtO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckM7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQWtCQyxHQUNELE9BQU8sU0FBUyxNQUNkLEtBQWtCLEVBQ2xCLFFBQTJCO0VBRTNCLElBQUksTUFBTTtFQUVWLEtBQUssTUFBTSxLQUFLLE1BQU87SUFDckIsT0FBTyxTQUFTO0VBQ2xCO0VBRUEsT0FBTztBQUNUIn0=