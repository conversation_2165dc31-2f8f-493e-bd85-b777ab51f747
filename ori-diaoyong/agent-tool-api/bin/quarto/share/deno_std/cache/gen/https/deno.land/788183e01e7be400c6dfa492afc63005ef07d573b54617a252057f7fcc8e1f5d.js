// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Transforms the given array into a Record, extracting the key of each element
 * using the given selector. If the selector produces the same key for multiple
 * elements, the latest one will be used (overriding the ones before it).
 *
 * @example
 * ```ts
 * import { associateBy } from "https://deno.land/std@$STD_VERSION/collections/associate_by.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const users = [
 *   { id: "a2e", userName: "<PERSON>" },
 *   { id: "5f8", userName: "<PERSON>" },
 *   { id: "d2c", userName: "<PERSON>" },
 * ];
 * const usersById = associateBy(users, (it) => it.id);
 *
 * assertEquals(usersById, {
 *   "a2e": { id: "a2e", userName: "<PERSON>" },
 *   "5f8": { id: "5f8", userName: "<PERSON>" },
 *   "d2c": { id: "d2c", userName: "<PERSON>" },
 * });
 * ```
 */ export function associateBy(array, selector) {
  const ret = {};
  for (const element of array){
    const selectedValue = selector(element);
    ret[selectedValue] = element;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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