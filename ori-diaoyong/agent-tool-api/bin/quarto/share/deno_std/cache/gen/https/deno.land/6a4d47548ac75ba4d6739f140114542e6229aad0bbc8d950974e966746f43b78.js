// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
export function compareNumber(a, b) {
  if (isNaN(a) || isNaN(b)) {
    throw new Error("Comparison against non-numbers");
  }
  return a === b ? 0 : a < b ? -1 : 1;
}
export function checkIdentifier(v1, v2) {
  // NOT having a prerelease is > having one
  // But NOT having a build is < having one
  if (v1.length && !v2.length) {
    return -1;
  } else if (!v1.length && v2.length) {
    return 1;
  } else {
    return 0;
  }
}
export function compareIdentifier(v1, v2) {
  let i = 0;
  do {
    const a = v1[i];
    const b = v2[i];
    if (a === undefined && b === undefined) {
      // same length is equal
      return 0;
    } else if (b === undefined) {
      // longer > shorter
      return 1;
    } else if (a === undefined) {
      // shorter < longer
      return -1;
    } else if (typeof a === "string" && typeof b === "number") {
      // string > number
      return 1;
    } else if (typeof a === "number" && typeof b === "string") {
      // number < string
      return -1;
    } else if (a < b) {
      return -1;
    } else if (a > b) {
      return 1;
    } else {
      continue;
    }
  }while (++i)
  // It can't ever reach here, but typescript doesn't realize that so
  // add this line so the return type is inferred correctly.
  return 0;
}
// The actual regexps
const re = [];
const src = [];
let R = 0;
// The following Regular Expressions can be used for tokenizing,
// validating, and parsing SemVer version strings.
// ## Numeric Identifier
// A single `0`, or a non-zero digit followed by zero or more digits.
const NUMERICIDENTIFIER = R++;
src[NUMERICIDENTIFIER] = "0|[1-9]\\d*";
// ## Non-numeric Identifier
// Zero or more digits, followed by a letter or hyphen, and then zero or
// more letters, digits, or hyphens.
const NONNUMERICIDENTIFIER = R++;
src[NONNUMERICIDENTIFIER] = "\\d*[a-zA-Z-][a-zA-Z0-9-]*";
// ## Main Version
// Three dot-separated numeric identifiers.
const MAINVERSION = R++;
const nid = src[NUMERICIDENTIFIER];
src[MAINVERSION] = `(${nid})\\.(${nid})\\.(${nid})`;
// ## Pre-release Version Identifier
// A numeric identifier, or a non-numeric identifier.
const PRERELEASEIDENTIFIER = R++;
src[PRERELEASEIDENTIFIER] = "(?:" + src[NUMERICIDENTIFIER] + "|" + src[NONNUMERICIDENTIFIER] + ")";
// ## Pre-release Version
// Hyphen, followed by one or more dot-separated pre-release version
// identifiers.
const PRERELEASE = R++;
src[PRERELEASE] = "(?:-(" + src[PRERELEASEIDENTIFIER] + "(?:\\." + src[PRERELEASEIDENTIFIER] + ")*))";
// ## Build Metadata Identifier
// Any combination of digits, letters, or hyphens.
const BUILDIDENTIFIER = R++;
src[BUILDIDENTIFIER] = "[0-9A-Za-z-]+";
// ## Build Metadata
// Plus sign, followed by one or more period-separated build metadata
// identifiers.
const BUILD = R++;
src[BUILD] = "(?:\\+(" + src[BUILDIDENTIFIER] + "(?:\\." + src[BUILDIDENTIFIER] + ")*))";
// ## Full Version String
// A main version, followed optionally by a pre-release version and
// build metadata.
// Note that the only major, minor, patch, and pre-release sections of
// the version string are capturing groups.  The build metadata is not a
// capturing group, because it should not ever be used in version
// comparison.
const FULL = R++;
const FULLPLAIN = "v?" + src[MAINVERSION] + src[PRERELEASE] + "?" + src[BUILD] + "?";
src[FULL] = "^" + FULLPLAIN + "$";
const GTLT = R++;
src[GTLT] = "((?:<|>)?=?)";
// Something like "2.*" or "1.2.x".
// Note that "x.x" is a valid xRange identifier, meaning "any version"
// Only the first item is strictly required.
const XRANGEIDENTIFIER = R++;
src[XRANGEIDENTIFIER] = src[NUMERICIDENTIFIER] + "|x|X|\\*";
const XRANGEPLAIN = R++;
src[XRANGEPLAIN] = "[v=\\s]*(" + src[XRANGEIDENTIFIER] + ")" + "(?:\\.(" + src[XRANGEIDENTIFIER] + ")" + "(?:\\.(" + src[XRANGEIDENTIFIER] + ")" + "(?:" + src[PRERELEASE] + ")?" + src[BUILD] + "?" + ")?)?";
const XRANGE = R++;
src[XRANGE] = "^" + src[GTLT] + "\\s*" + src[XRANGEPLAIN] + "$";
// Tilde ranges.
// Meaning is "reasonably at or greater than"
const LONETILDE = R++;
src[LONETILDE] = "(?:~>?)";
const TILDE = R++;
src[TILDE] = "^" + src[LONETILDE] + src[XRANGEPLAIN] + "$";
// Caret ranges.
// Meaning is "at least and backwards compatible with"
const LONECARET = R++;
src[LONECARET] = "(?:\\^)";
const CARET = R++;
src[CARET] = "^" + src[LONECARET] + src[XRANGEPLAIN] + "$";
// A simple gt/lt/eq thing, or just "" to indicate "any version"
const COMPARATOR = R++;
src[COMPARATOR] = "^" + src[GTLT] + "\\s*(" + FULLPLAIN + ")$|^$";
// Something like `1.2.3 - 1.2.4`
const HYPHENRANGE = R++;
src[HYPHENRANGE] = "^\\s*(" + src[XRANGEPLAIN] + ")" + "\\s+-\\s+" + "(" + src[XRANGEPLAIN] + ")" + "\\s*$";
// Star ranges basically just allow anything at all.
const STAR = R++;
src[STAR] = "(<|>)?=?\\s*\\*";
// Compile to actual regexp objects.
// All are flag-free, unless they were created above with a flag.
for(let i = 0; i < R; i++){
  if (!re[i]) {
    re[i] = new RegExp(src[i]);
  }
}
/**
 * Returns true if the value is a valid SemVer number.
 *
 * Must be a number. Must not be NaN. Can be positive or negative infinity.
 * Can be between 0 and MAX_SAFE_INTEGER.
 * @param value The value to check
 * @returns True if its a valid semver number
 */ export function isValidNumber(value) {
  return typeof value === "number" && !Number.isNaN(value) && (!Number.isFinite(value) || 0 <= value && value <= Number.MAX_SAFE_INTEGER);
}
export const MAX_LENGTH = 256;
/**
 * Returns true if the value is a valid semver pre-release or build identifier.
 *
 * Must be a string. Must be between 1 and 256 characters long. Must match
 * the regular expression /[0-9A-Za-z-]+/.
 * @param value The value to check
 * @returns True if the value is a valid semver string.
 */ export function isValidString(value) {
  return typeof value === "string" && value.length > 0 && value.length <= MAX_LENGTH && !!value.match(/[0-9A-Za-z-]+/);
}
/**
 * Checks to see if the value is a valid Operator string.
 *
 * Adds a type assertion if true.
 * @param value The value to check
 * @returns True if the value is a valid Operator string otherwise false.
 */ export function isValidOperator(value) {
  if (typeof value !== "string") return false;
  switch(value){
    case "":
    case "=":
    case "==":
    case "===":
    case "!==":
    case "!=":
    case ">":
    case ">=":
    case "<":
    case "<=":
      return true;
    default:
      return false;
  }
}
export { CARET, COMPARATOR, FULL, HYPHENRANGE, NUMERICIDENTIFIER, re, src, STAR, TILDE, XRANGE };
//# sourceMappingURL=data:application/json;base64,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