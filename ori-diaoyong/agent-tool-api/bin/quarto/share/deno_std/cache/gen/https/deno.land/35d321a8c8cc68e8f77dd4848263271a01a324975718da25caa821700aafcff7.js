// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * If the given value is part of the given object it returns true, otherwise it
 * returns false. Doesn't work with non-primitive values: includesValue({x: {}},
 * {}) returns false.
 *
 * @example
 * ```ts
 * import { includesValue } from "https://deno.land/std@$STD_VERSION/collections/includes_value.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const input = {
 *   first: 33,
 *   second: 34,
 * };
 *
 * assertEquals(includesValue(input, 34), true);
 * ```
 */ export function includesValue(record, value) {
  for(const i in record){
    if (Object.hasOwn(record, i) && (record[i] === value || Number.isNaN(value) && Number.isNaN(record[i]))) {
      return true;
    }
  }
  return false;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2luY2x1ZGVzX3ZhbHVlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogSWYgdGhlIGdpdmVuIHZhbHVlIGlzIHBhcnQgb2YgdGhlIGdpdmVuIG9iamVjdCBpdCByZXR1cm5zIHRydWUsIG90aGVyd2lzZSBpdFxuICogcmV0dXJucyBmYWxzZS4gRG9lc24ndCB3b3JrIHdpdGggbm9uLXByaW1pdGl2ZSB2YWx1ZXM6IGluY2x1ZGVzVmFsdWUoe3g6IHt9fSxcbiAqIHt9KSByZXR1cm5zIGZhbHNlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgaW5jbHVkZXNWYWx1ZSB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL2luY2x1ZGVzX3ZhbHVlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IGlucHV0ID0ge1xuICogICBmaXJzdDogMzMsXG4gKiAgIHNlY29uZDogMzQsXG4gKiB9O1xuICpcbiAqIGFzc2VydEVxdWFscyhpbmNsdWRlc1ZhbHVlKGlucHV0LCAzNCksIHRydWUpO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmNsdWRlc1ZhbHVlPFQ+KFxuICByZWNvcmQ6IFJlYWRvbmx5PFJlY29yZDxzdHJpbmcsIFQ+PixcbiAgdmFsdWU6IFQsXG4pOiBib29sZWFuIHtcbiAgZm9yIChjb25zdCBpIGluIHJlY29yZCkge1xuICAgIGlmIChcbiAgICAgIE9iamVjdC5oYXNPd24ocmVjb3JkLCBpKSAmJlxuICAgICAgKHJlY29yZFtpXSA9PT0gdmFsdWUgfHwgTnVtYmVyLmlzTmFOKHZhbHVlKSAmJiBOdW1iZXIuaXNOYU4ocmVjb3JkW2ldKSlcbiAgICApIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDOzs7Ozs7Ozs7Ozs7Ozs7OztDQWlCQyxHQUNELE9BQU8sU0FBUyxjQUNkLE1BQW1DLEVBQ25DLEtBQVE7RUFFUixJQUFLLE1BQU0sS0FBSyxPQUFRO0lBQ3RCLElBQ0UsT0FBTyxNQUFNLENBQUMsUUFBUSxNQUN0QixDQUFDLE1BQU0sQ0FBQyxFQUFFLEtBQUssU0FBUyxPQUFPLEtBQUssQ0FBQyxVQUFVLE9BQU8sS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsR0FDdEU7TUFDQSxPQUFPO0lBQ1Q7RUFDRjtFQUVBLE9BQU87QUFDVCJ9