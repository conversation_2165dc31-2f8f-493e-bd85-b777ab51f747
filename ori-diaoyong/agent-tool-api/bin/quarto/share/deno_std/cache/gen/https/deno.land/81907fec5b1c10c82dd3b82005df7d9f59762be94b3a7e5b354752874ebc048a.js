// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { Type } from "../type.ts";
const YAML_DATE_REGEXP = new RegExp("^([0-9][0-9][0-9][0-9])" + // [1] year
"-([0-9][0-9])" + // [2] month
"-([0-9][0-9])$");
const YAML_TIMESTAMP_REGEXP = new RegExp("^([0-9][0-9][0-9][0-9])" + // [1] year
"-([0-9][0-9]?)" + // [2] month
"-([0-9][0-9]?)" + // [3] day
"(?:[Tt]|[ \\t]+)" + // ...
"([0-9][0-9]?)" + // [4] hour
":([0-9][0-9])" + // [5] minute
":([0-9][0-9])" + // [6] second
"(?:\\.([0-9]*))?" + // [7] fraction
"(?:[ \\t]*(Z|([-+])([0-9][0-9]?)" + // [8] tz [9] tz_sign [10] tz_hour
"(?::([0-9][0-9]))?))?$");
function resolveYamlTimestamp(data) {
  if (data === null) return false;
  if (YAML_DATE_REGEXP.exec(data) !== null) return true;
  if (YAML_TIMESTAMP_REGEXP.exec(data) !== null) return true;
  return false;
}
function constructYamlTimestamp(data) {
  let match = YAML_DATE_REGEXP.exec(data);
  if (match === null) match = YAML_TIMESTAMP_REGEXP.exec(data);
  if (match === null) throw new Error("Date resolve error");
  // match: [1] year [2] month [3] day
  const year = +match[1];
  const month = +match[2] - 1; // JS month starts with 0
  const day = +match[3];
  if (!match[4]) {
    // no hour
    return new Date(Date.UTC(year, month, day));
  }
  // match: [4] hour [5] minute [6] second [7] fraction
  const hour = +match[4];
  const minute = +match[5];
  const second = +match[6];
  let fraction = 0;
  if (match[7]) {
    let partFraction = match[7].slice(0, 3);
    while(partFraction.length < 3){
      // milli-seconds
      partFraction += "0";
    }
    fraction = +partFraction;
  }
  // match: [8] tz [9] tz_sign [10] tz_hour [11] tz_minute
  let delta = null;
  if (match[9]) {
    const tzHour = +match[10];
    const tzMinute = +(match[11] || 0);
    delta = (tzHour * 60 + tzMinute) * 60000; // delta in milli-seconds
    if (match[9] === "-") delta = -delta;
  }
  const date = new Date(Date.UTC(year, month, day, hour, minute, second, fraction));
  if (delta) date.setTime(date.getTime() - delta);
  return date;
}
function representYamlTimestamp(date) {
  return date.toISOString();
}
export const timestamp = new Type("tag:yaml.org,2002:timestamp", {
  construct: constructYamlTimestamp,
  instanceOf: Date,
  kind: "scalar",
  represent: representYamlTimestamp,
  resolve: resolveYamlTimestamp
});
//# sourceMappingURL=data:application/json;base64,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