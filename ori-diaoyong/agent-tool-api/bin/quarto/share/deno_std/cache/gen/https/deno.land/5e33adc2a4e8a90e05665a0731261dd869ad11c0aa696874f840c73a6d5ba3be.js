// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
export var Format;
(function(Format) {
  Format["YAML"] = "yaml";
  Format["TOML"] = "toml";
  Format["JSON"] = "json";
  Format["UNKNOWN"] = "unknown";
})(Format || (Format = {}));
const { isArray } = Array;
function getBeginToken(delimiter) {
  return isArray(delimiter) ? delimiter[0] : delimiter;
}
function getEndToken(delimiter) {
  return isArray(delimiter) ? delimiter[1] : delimiter;
}
function createRegExp(...dv) {
  const beginPattern = "(" + dv.map(getBeginToken).join("|") + ")";
  const pattern = "^(" + "\\ufeff?" + // Maybe byte order mark
  beginPattern + "$([\\s\\S]+?)" + "^(?:" + dv.map(getEndToken).join("|") + ")\\s*" + "$" + (globalThis?.Deno?.build?.os === "windows" ? "\\r?" : "") + "(?:\\n)?)";
  return [
    new RegExp("^" + beginPattern + "$", "im"),
    new RegExp(pattern, "im")
  ];
}
const [RX_RECOGNIZE_YAML, RX_YAML] = createRegExp([
  "---yaml",
  "---"
], "= yaml =", "---");
const [RX_RECOGNIZE_TOML, RX_TOML] = createRegExp([
  "---toml",
  "---"
], "\\+\\+\\+", "= toml =");
const [RX_RECOGNIZE_JSON, RX_JSON] = createRegExp([
  "---json",
  "---"
], "= json =");
export const MAP_FORMAT_TO_RECOGNIZER_RX = {
  yaml: RX_RECOGNIZE_YAML,
  toml: RX_RECOGNIZE_TOML,
  json: RX_RECOGNIZE_JSON
};
export const MAP_FORMAT_TO_EXTRACTOR_RX = {
  yaml: RX_YAML,
  toml: RX_TOML,
  json: RX_JSON
};
//# sourceMappingURL=data:application/json;base64,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