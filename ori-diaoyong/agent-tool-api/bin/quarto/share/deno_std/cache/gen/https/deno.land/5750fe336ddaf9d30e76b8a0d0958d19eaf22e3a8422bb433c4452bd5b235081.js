// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { comparatorFormat } from "./comparator_format.ts";
/**
 * Formats the range into a string
 * @example >=0.0.0 || <1.0.0
 * @param range The range to format
 * @returns A string representation of the range
 */ export function rangeFormat(range) {
  return range.ranges.map((c)=>c.map((c)=>comparatorFormat(c)).join(" ")).join("||");
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9yYW5nZV9mb3JtYXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbmltcG9ydCB0eXBlIHsgU2VtVmVyUmFuZ2UgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgY29tcGFyYXRvckZvcm1hdCB9IGZyb20gXCIuL2NvbXBhcmF0b3JfZm9ybWF0LnRzXCI7XG5cbi8qKlxuICogRm9ybWF0cyB0aGUgcmFuZ2UgaW50byBhIHN0cmluZ1xuICogQGV4YW1wbGUgPj0wLjAuMCB8fCA8MS4wLjBcbiAqIEBwYXJhbSByYW5nZSBUaGUgcmFuZ2UgdG8gZm9ybWF0XG4gKiBAcmV0dXJucyBBIHN0cmluZyByZXByZXNlbnRhdGlvbiBvZiB0aGUgcmFuZ2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJhbmdlRm9ybWF0KHJhbmdlOiBTZW1WZXJSYW5nZSkge1xuICByZXR1cm4gcmFuZ2UucmFuZ2VzLm1hcCgoYykgPT4gYy5tYXAoKGMpID0+IGNvbXBhcmF0b3JGb3JtYXQoYykpLmpvaW4oXCIgXCIpKVxuICAgIC5qb2luKFxuICAgICAgXCJ8fFwiLFxuICAgICk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBRTFFLFNBQVMsZ0JBQWdCLFFBQVEseUJBQXlCO0FBRTFEOzs7OztDQUtDLEdBQ0QsT0FBTyxTQUFTLFlBQVksS0FBa0I7RUFDNUMsT0FBTyxNQUFNLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFNLEVBQUUsR0FBRyxDQUFDLENBQUMsSUFBTSxpQkFBaUIsSUFBSSxJQUFJLENBQUMsTUFDbkUsSUFBSSxDQUNIO0FBRU4ifQ==