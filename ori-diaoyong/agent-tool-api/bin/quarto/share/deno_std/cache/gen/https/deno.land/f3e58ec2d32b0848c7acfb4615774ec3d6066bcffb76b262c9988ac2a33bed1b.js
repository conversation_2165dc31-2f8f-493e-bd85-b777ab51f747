// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/** Order */ export function sortBy(array, selector, options) {
  const len = array.length;
  const indexes = new Array(len);
  const selectors = new Array(len);
  const order = options?.order ?? "asc";
  for(let i = 0; i < len; i++){
    indexes[i] = i;
    const s = selector(array[i]);
    selectors[i] = Number.isNaN(s) ? null : s;
  }
  indexes.sort((ai, bi)=>{
    let a = selectors[ai];
    let b = selectors[bi];
    if (order === "desc") {
      [a, b] = [
        b,
        a
      ];
    }
    if (a === null) return 1;
    if (b === null) return -1;
    return a > b ? 1 : a < b ? -1 : 0;
  });
  for(let i = 0; i < len; i++){
    indexes[i] = array[indexes[i]];
  }
  return indexes;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3NvcnRfYnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuLyoqIE9yZGVyICovXG5leHBvcnQgdHlwZSBPcmRlciA9IFwiYXNjXCIgfCBcImRlc2NcIjtcblxuLyoqIE9wdGlvbnMgZm9yIHNvcnRCeSAqL1xuZXhwb3J0IHR5cGUgU29ydEJ5T3B0aW9ucyA9IHtcbiAgb3JkZXI6IE9yZGVyO1xufTtcblxuLyoqXG4gKiBSZXR1cm5zIGFsbCBlbGVtZW50cyBpbiB0aGUgZ2l2ZW4gY29sbGVjdGlvbiwgc29ydGVkIGJ5IHRoZWlyIHJlc3VsdCB1c2luZ1xuICogdGhlIGdpdmVuIHNlbGVjdG9yLiBUaGUgc2VsZWN0b3IgZnVuY3Rpb24gaXMgY2FsbGVkIG9ubHkgb25jZSBmb3IgZWFjaFxuICogZWxlbWVudC4gQXNjZW5kaW5nIG9yIGRlc2NlbmRpbmcgb3JkZXIgY2FuIGJlIHNwZWNpZmllZC5cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIGltcG9ydCB7IHNvcnRCeSB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL3NvcnRfYnkudHNcIjtcbiAqIGltcG9ydCB7IGFzc2VydEVxdWFscyB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2Fzc2VydC9hc3NlcnRfZXF1YWxzLnRzXCI7XG4gKlxuICogY29uc3QgcGVvcGxlID0gW1xuICogICB7IG5hbWU6IFwiQW5uYVwiLCBhZ2U6IDM0IH0sXG4gKiAgIHsgbmFtZTogXCJLaW1cIiwgYWdlOiA0MiB9LFxuICogICB7IG5hbWU6IFwiSm9oblwiLCBhZ2U6IDIzIH0sXG4gKiBdO1xuICogY29uc3Qgc29ydGVkQnlBZ2UgPSBzb3J0QnkocGVvcGxlLCAoaXQpID0+IGl0LmFnZSk7XG4gKlxuICogYXNzZXJ0RXF1YWxzKHNvcnRlZEJ5QWdlLCBbXG4gKiAgIHsgbmFtZTogXCJKb2huXCIsIGFnZTogMjMgfSxcbiAqICAgeyBuYW1lOiBcIkFubmFcIiwgYWdlOiAzNCB9LFxuICogICB7IG5hbWU6IFwiS2ltXCIsIGFnZTogNDIgfSxcbiAqIF0pO1xuICpcbiAqIGNvbnN0IHNvcnRlZEJ5QWdlRGVzYyA9IHNvcnRCeShwZW9wbGUsIChpdCkgPT4gaXQuYWdlLCB7IG9yZGVyOiBcImRlc2NcIiB9KTtcbiAqXG4gKiBhc3NlcnRFcXVhbHMoc29ydGVkQnlBZ2VEZXNjLCBbXG4gKiAgIHsgbmFtZTogXCJLaW1cIiwgYWdlOiA0MiB9LFxuICogICB7IG5hbWU6IFwiQW5uYVwiLCBhZ2U6IDM0IH0sXG4gKiAgIHsgbmFtZTogXCJKb2huXCIsIGFnZTogMjMgfSxcbiAqIF0pO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzb3J0Qnk8VD4oXG4gIGFycmF5OiByZWFkb25seSBUW10sXG4gIHNlbGVjdG9yOiAoZWw6IFQpID0+IG51bWJlcixcbiAgb3B0aW9ucz86IFNvcnRCeU9wdGlvbnMsXG4pOiBUW107XG5leHBvcnQgZnVuY3Rpb24gc29ydEJ5PFQ+KFxuICBhcnJheTogcmVhZG9ubHkgVFtdLFxuICBzZWxlY3RvcjogKGVsOiBUKSA9PiBzdHJpbmcsXG4gIG9wdGlvbnM/OiBTb3J0QnlPcHRpb25zLFxuKTogVFtdO1xuZXhwb3J0IGZ1bmN0aW9uIHNvcnRCeTxUPihcbiAgYXJyYXk6IHJlYWRvbmx5IFRbXSxcbiAgc2VsZWN0b3I6IChlbDogVCkgPT4gYmlnaW50LFxuICBvcHRpb25zPzogU29ydEJ5T3B0aW9ucyxcbik6IFRbXTtcbmV4cG9ydCBmdW5jdGlvbiBzb3J0Qnk8VD4oXG4gIGFycmF5OiByZWFkb25seSBUW10sXG4gIHNlbGVjdG9yOiAoZWw6IFQpID0+IERhdGUsXG4gIG9wdGlvbnM/OiBTb3J0QnlPcHRpb25zLFxuKTogVFtdO1xuZXhwb3J0IGZ1bmN0aW9uIHNvcnRCeTxUPihcbiAgYXJyYXk6IHJlYWRvbmx5IFRbXSxcbiAgc2VsZWN0b3I6XG4gICAgfCAoKGVsOiBUKSA9PiBudW1iZXIpXG4gICAgfCAoKGVsOiBUKSA9PiBzdHJpbmcpXG4gICAgfCAoKGVsOiBUKSA9PiBiaWdpbnQpXG4gICAgfCAoKGVsOiBUKSA9PiBEYXRlKSxcbiAgb3B0aW9ucz86IFNvcnRCeU9wdGlvbnMsXG4pOiBUW10ge1xuICBjb25zdCBsZW4gPSBhcnJheS5sZW5ndGg7XG4gIGNvbnN0IGluZGV4ZXMgPSBuZXcgQXJyYXk8bnVtYmVyPihsZW4pO1xuICBjb25zdCBzZWxlY3RvcnMgPSBuZXcgQXJyYXk8UmV0dXJuVHlwZTx0eXBlb2Ygc2VsZWN0b3I+IHwgbnVsbD4obGVuKTtcbiAgY29uc3Qgb3JkZXIgPSBvcHRpb25zPy5vcmRlciA/PyBcImFzY1wiO1xuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuOyBpKyspIHtcbiAgICBpbmRleGVzW2ldID0gaTtcbiAgICBjb25zdCBzID0gc2VsZWN0b3IoYXJyYXlbaV0pO1xuICAgIHNlbGVjdG9yc1tpXSA9IE51bWJlci5pc05hTihzKSA/IG51bGwgOiBzO1xuICB9XG5cbiAgaW5kZXhlcy5zb3J0KChhaSwgYmkpID0+IHtcbiAgICBsZXQgYSA9IHNlbGVjdG9yc1thaV07XG4gICAgbGV0IGIgPSBzZWxlY3RvcnNbYmldO1xuICAgIGlmIChvcmRlciA9PT0gXCJkZXNjXCIpIHtcbiAgICAgIFthLCBiXSA9IFtiLCBhXTtcbiAgICB9XG4gICAgaWYgKGEgPT09IG51bGwpIHJldHVybiAxO1xuICAgIGlmIChiID09PSBudWxsKSByZXR1cm4gLTE7XG4gICAgcmV0dXJuIGEgPiBiID8gMSA6IGEgPCBiID8gLTEgOiAwO1xuICB9KTtcblxuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgKGluZGV4ZXMgYXMgdW5rbm93biBhcyBUW10pW2ldID0gYXJyYXlbaW5kZXhlc1tpXV07XG4gIH1cblxuICByZXR1cm4gaW5kZXhlcyBhcyB1bmtub3duIGFzIFRbXTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDLFVBQVUsR0E0RFYsT0FBTyxTQUFTLE9BQ2QsS0FBbUIsRUFDbkIsUUFJcUIsRUFDckIsT0FBdUI7RUFFdkIsTUFBTSxNQUFNLE1BQU0sTUFBTTtFQUN4QixNQUFNLFVBQVUsSUFBSSxNQUFjO0VBQ2xDLE1BQU0sWUFBWSxJQUFJLE1BQTBDO0VBQ2hFLE1BQU0sUUFBUSxTQUFTLFNBQVM7RUFFaEMsSUFBSyxJQUFJLElBQUksR0FBRyxJQUFJLEtBQUssSUFBSztJQUM1QixPQUFPLENBQUMsRUFBRSxHQUFHO0lBQ2IsTUFBTSxJQUFJLFNBQVMsS0FBSyxDQUFDLEVBQUU7SUFDM0IsU0FBUyxDQUFDLEVBQUUsR0FBRyxPQUFPLEtBQUssQ0FBQyxLQUFLLE9BQU87RUFDMUM7RUFFQSxRQUFRLElBQUksQ0FBQyxDQUFDLElBQUk7SUFDaEIsSUFBSSxJQUFJLFNBQVMsQ0FBQyxHQUFHO0lBQ3JCLElBQUksSUFBSSxTQUFTLENBQUMsR0FBRztJQUNyQixJQUFJLFVBQVUsUUFBUTtNQUNwQixDQUFDLEdBQUcsRUFBRSxHQUFHO1FBQUM7UUFBRztPQUFFO0lBQ2pCO0lBQ0EsSUFBSSxNQUFNLE1BQU0sT0FBTztJQUN2QixJQUFJLE1BQU0sTUFBTSxPQUFPLENBQUM7SUFDeEIsT0FBTyxJQUFJLElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxJQUFJO0VBQ2xDO0VBRUEsSUFBSyxJQUFJLElBQUksR0FBRyxJQUFJLEtBQUssSUFBSztJQUMzQixPQUEwQixDQUFDLEVBQUUsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztFQUNwRDtFQUVBLE9BQU87QUFDVCJ9