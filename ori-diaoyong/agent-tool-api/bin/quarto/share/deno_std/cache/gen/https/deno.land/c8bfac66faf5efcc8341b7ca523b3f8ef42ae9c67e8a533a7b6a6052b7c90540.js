// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { filterInPlace } from "./_utils.ts";
/**
 * Returns all distinct elements that appear at least once in each of the given
 * arrays.
 *
 * @example
 * ```ts
 * import { intersect } from "https://deno.land/std@$STD_VERSION/collections/intersect.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const lisaInterests = ["Cooking", "Music", "Hiking"];
 * const kimInterests = ["Music", "Tennis", "Cooking"];
 * const commonInterests = intersect(lisaInterests, kimInterests);
 *
 * assertEquals(commonInterests, ["Cooking", "Music"]);
 * ```
 */ export function intersect(...arrays) {
  const [originalHead, ...tail] = arrays;
  const head = [
    ...new Set(originalHead)
  ];
  const tailSets = tail.map((it)=>new Set(it));
  for (const set of tailSets){
    filterInPlace(head, (it)=>set.has(it));
    if (head.length === 0) return head;
  }
  return head;
}
//# sourceMappingURL=data:application/json;base64,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