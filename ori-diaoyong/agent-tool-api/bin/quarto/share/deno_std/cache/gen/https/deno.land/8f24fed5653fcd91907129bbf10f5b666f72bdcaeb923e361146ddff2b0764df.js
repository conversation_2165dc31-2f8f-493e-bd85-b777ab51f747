// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { BinarySearchTree as BinarySearchTree_ } from "./unstable/binary_search_tree.ts";
/** @deprecated (will be removed after 0.206.0) import from `collections/unstable/binary_search_tree.ts instead */ export class BinarySearchTree extends BinarySearchTree_ {
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2JpbmFyeV9zZWFyY2hfdHJlZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5pbXBvcnQgeyBCaW5hcnlTZWFyY2hUcmVlIGFzIEJpbmFyeVNlYXJjaFRyZWVfIH0gZnJvbSBcIi4vdW5zdGFibGUvYmluYXJ5X3NlYXJjaF90cmVlLnRzXCI7XG5cbi8qKiBAZGVwcmVjYXRlZCAod2lsbCBiZSByZW1vdmVkIGFmdGVyIDAuMjA2LjApIGltcG9ydCBmcm9tIGBjb2xsZWN0aW9ucy91bnN0YWJsZS9iaW5hcnlfc2VhcmNoX3RyZWUudHMgaW5zdGVhZCAqL1xuZXhwb3J0IGNsYXNzIEJpbmFyeVNlYXJjaFRyZWU8VD4gZXh0ZW5kcyBCaW5hcnlTZWFyY2hUcmVlXzxUPiB7fVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsU0FBUyxvQkFBb0IsaUJBQWlCLFFBQVEsbUNBQW1DO0FBRXpGLGdIQUFnSCxHQUNoSCxPQUFPLE1BQU0seUJBQTRCO0FBQXNCIn0=