// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { concat } from "../bytes/concat.ts";
export async function toArrayBuffer(readableStream) {
  const reader = readableStream.getReader();
  const chunks = [];
  while(true){
    const { done, value } = await reader.read();
    if (done) {
      break;
    }
    chunks.push(value);
  }
  return concat(...chunks).buffer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3N0cmVhbXMvdG9fYXJyYXlfYnVmZmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbmltcG9ydCB7IGNvbmNhdCB9IGZyb20gXCIuLi9ieXRlcy9jb25jYXQudHNcIjtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHRvQXJyYXlCdWZmZXIoXG4gIHJlYWRhYmxlU3RyZWFtOiBSZWFkYWJsZVN0cmVhbTxVaW50OEFycmF5Pixcbik6IFByb21pc2U8QXJyYXlCdWZmZXI+IHtcbiAgY29uc3QgcmVhZGVyID0gcmVhZGFibGVTdHJlYW0uZ2V0UmVhZGVyKCk7XG4gIGNvbnN0IGNodW5rczogVWludDhBcnJheVtdID0gW107XG5cbiAgd2hpbGUgKHRydWUpIHtcbiAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuXG4gICAgaWYgKGRvbmUpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGNodW5rcy5wdXNoKHZhbHVlKTtcbiAgfVxuXG4gIHJldHVybiBjb25jYXQoLi4uY2h1bmtzKS5idWZmZXI7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQyxTQUFTLE1BQU0sUUFBUSxxQkFBcUI7QUFFNUMsT0FBTyxlQUFlLGNBQ3BCLGNBQTBDO0VBRTFDLE1BQU0sU0FBUyxlQUFlLFNBQVM7RUFDdkMsTUFBTSxTQUF1QixFQUFFO0VBRS9CLE1BQU8sS0FBTTtJQUNYLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLEdBQUcsTUFBTSxPQUFPLElBQUk7SUFFekMsSUFBSSxNQUFNO01BQ1I7SUFDRjtJQUVBLE9BQU8sSUFBSSxDQUFDO0VBQ2Q7RUFFQSxPQUFPLFVBQVUsUUFBUSxNQUFNO0FBQ2pDIn0=