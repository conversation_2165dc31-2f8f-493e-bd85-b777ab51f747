// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { isWindows } from "./_os.ts";
import { toNamespacedPath as posixToNamespacedPath } from "./posix/to_namespaced_path.ts";
import { toNamespacedPath as windowsToNamespacedPath } from "./windows/to_namespaced_path.ts";
/**
 * Resolves path to a namespace path
 * @param path to resolve to namespace
 */ export function toNamespacedPath(path) {
  return isWindows ? windowsToNamespacedPath(path) : posixToNamespacedPath(path);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3BhdGgvdG9fbmFtZXNwYWNlZF9wYXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbmltcG9ydCB7IGlzV2luZG93cyB9IGZyb20gXCIuL19vcy50c1wiO1xuaW1wb3J0IHsgdG9OYW1lc3BhY2VkUGF0aCBhcyBwb3NpeFRvTmFtZXNwYWNlZFBhdGggfSBmcm9tIFwiLi9wb3NpeC90b19uYW1lc3BhY2VkX3BhdGgudHNcIjtcbmltcG9ydCB7IHRvTmFtZXNwYWNlZFBhdGggYXMgd2luZG93c1RvTmFtZXNwYWNlZFBhdGggfSBmcm9tIFwiLi93aW5kb3dzL3RvX25hbWVzcGFjZWRfcGF0aC50c1wiO1xuXG4vKipcbiAqIFJlc29sdmVzIHBhdGggdG8gYSBuYW1lc3BhY2UgcGF0aFxuICogQHBhcmFtIHBhdGggdG8gcmVzb2x2ZSB0byBuYW1lc3BhY2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRvTmFtZXNwYWNlZFBhdGgocGF0aDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIGlzV2luZG93c1xuICAgID8gd2luZG93c1RvTmFtZXNwYWNlZFBhdGgocGF0aClcbiAgICA6IHBvc2l4VG9OYW1lc3BhY2VkUGF0aChwYXRoKTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDLFNBQVMsU0FBUyxRQUFRLFdBQVc7QUFDckMsU0FBUyxvQkFBb0IscUJBQXFCLFFBQVEsZ0NBQWdDO0FBQzFGLFNBQVMsb0JBQW9CLHVCQUF1QixRQUFRLGtDQUFrQztBQUU5Rjs7O0NBR0MsR0FDRCxPQUFPLFNBQVMsaUJBQWlCLElBQVk7RUFDM0MsT0FBTyxZQUNILHdCQUF3QixRQUN4QixzQkFBc0I7QUFDNUIifQ==