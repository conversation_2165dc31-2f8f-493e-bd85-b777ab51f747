// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns all elements in the given collection until the first element that
 * does not match the given predicate.
 *
 * @example
 * ```ts
 * import { takeWhile } from "https://deno.land/std@$STD_VERSION/collections/take_while.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const arr = [1, 2, 3, 4, 5, 6];
 *
 * assertEquals(
 *   takeWhile(arr, (i) => i !== 4),
 *   [1, 2, 3],
 * );
 * ```
 */ export function takeWhile(array, predicate) {
  let offset = 0;
  const length = array.length;
  while(length > offset && predicate(array[offset])){
    offset++;
  }
  return array.slice(0, offset);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3Rha2Vfd2hpbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuLyoqXG4gKiBSZXR1cm5zIGFsbCBlbGVtZW50cyBpbiB0aGUgZ2l2ZW4gY29sbGVjdGlvbiB1bnRpbCB0aGUgZmlyc3QgZWxlbWVudCB0aGF0XG4gKiBkb2VzIG5vdCBtYXRjaCB0aGUgZ2l2ZW4gcHJlZGljYXRlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgdGFrZVdoaWxlIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vY29sbGVjdGlvbnMvdGFrZV93aGlsZS50c1wiO1xuICogaW1wb3J0IHsgYXNzZXJ0RXF1YWxzIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vYXNzZXJ0L2Fzc2VydF9lcXVhbHMudHNcIjtcbiAqXG4gKiBjb25zdCBhcnIgPSBbMSwgMiwgMywgNCwgNSwgNl07XG4gKlxuICogYXNzZXJ0RXF1YWxzKFxuICogICB0YWtlV2hpbGUoYXJyLCAoaSkgPT4gaSAhPT0gNCksXG4gKiAgIFsxLCAyLCAzXSxcbiAqICk7XG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRha2VXaGlsZTxUPihcbiAgYXJyYXk6IHJlYWRvbmx5IFRbXSxcbiAgcHJlZGljYXRlOiAoZWw6IFQpID0+IGJvb2xlYW4sXG4pOiBUW10ge1xuICBsZXQgb2Zmc2V0ID0gMDtcbiAgY29uc3QgbGVuZ3RoID0gYXJyYXkubGVuZ3RoO1xuXG4gIHdoaWxlIChsZW5ndGggPiBvZmZzZXQgJiYgcHJlZGljYXRlKGFycmF5W29mZnNldF0pKSB7XG4gICAgb2Zmc2V0Kys7XG4gIH1cblxuICByZXR1cm4gYXJyYXkuc2xpY2UoMCwgb2Zmc2V0KTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDOzs7Ozs7Ozs7Ozs7Ozs7O0NBZ0JDLEdBQ0QsT0FBTyxTQUFTLFVBQ2QsS0FBbUIsRUFDbkIsU0FBNkI7RUFFN0IsSUFBSSxTQUFTO0VBQ2IsTUFBTSxTQUFTLE1BQU0sTUFBTTtFQUUzQixNQUFPLFNBQVMsVUFBVSxVQUFVLEtBQUssQ0FBQyxPQUFPLEVBQUc7SUFDbEQ7RUFDRjtFQUVBLE9BQU8sTUFBTSxLQUFLLENBQUMsR0FBRztBQUN4QiJ9