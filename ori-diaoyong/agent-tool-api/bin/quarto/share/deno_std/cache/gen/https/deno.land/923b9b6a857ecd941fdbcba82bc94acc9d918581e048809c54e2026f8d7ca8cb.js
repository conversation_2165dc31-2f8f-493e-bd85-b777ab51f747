// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { dirname } from "../path/dirname.ts";
import { resolve } from "../path/resolve.ts";
import { ensureDir, ensureDirSync } from "./ensure_dir.ts";
import { getFileInfoType, toPathString } from "./_util.ts";
const isWindows = Deno.build.os === "windows";
function resolveSymlinkTarget(target, linkName) {
  if (typeof target !== "string") return target; // URL is always absolute path
  if (typeof linkName === "string") {
    return resolve(dirname(linkName), target);
  } else {
    return new URL(target, linkName);
  }
}
/**
 * Ensures that the link exists, and points to a valid file.
 * If the directory structure does not exist, it is created.
 *
 * @param target the source file path
 * @param linkName the destination link path
 */ export async function ensureSymlink(target, linkName) {
  const targetRealPath = resolveSymlinkTarget(target, linkName);
  const srcStatInfo = await Deno.lstat(targetRealPath);
  const srcFilePathType = getFileInfoType(srcStatInfo);
  await ensureDir(dirname(toPathString(linkName)));
  const options = isWindows ? {
    type: srcFilePathType === "dir" ? "dir" : "file"
  } : undefined;
  try {
    await Deno.symlink(target, linkName, options);
  } catch (error) {
    if (!(error instanceof Deno.errors.AlreadyExists)) {
      throw error;
    }
  }
}
/**
 * Ensures that the link exists, and points to a valid file.
 * If the directory structure does not exist, it is created.
 *
 * @param target the source file path
 * @param linkName the destination link path
 */ export function ensureSymlinkSync(target, linkName) {
  const targetRealPath = resolveSymlinkTarget(target, linkName);
  const srcStatInfo = Deno.lstatSync(targetRealPath);
  const srcFilePathType = getFileInfoType(srcStatInfo);
  ensureDirSync(dirname(toPathString(linkName)));
  const options = isWindows ? {
    type: srcFilePathType === "dir" ? "dir" : "file"
  } : undefined;
  try {
    Deno.symlinkSync(target, linkName, options);
  } catch (error) {
    if (!(error instanceof Deno.errors.AlreadyExists)) {
      throw error;
    }
  }
}
//# sourceMappingURL=data:application/json;base64,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