// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new array that drops all elements in the given collection until the
 * first element that does not match the given predicate.
 *
 * @example
 * ```ts
 * import { dropWhile } from "https://deno.land/std@$STD_VERSION/collections/drop_while.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [3, 2, 5, 2, 5];
 * const dropWhileNumbers = dropWhile(numbers, (i) => i !== 2);
 *
 * assertEquals(dropWhileNumbers, [2, 5, 2, 5]);
 * ```
 */ export function dropWhile(array, predicate) {
  let offset = 0;
  const length = array.length;
  while(length > offset && predicate(array[offset])){
    offset++;
  }
  return array.slice(offset, length);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2Ryb3Bfd2hpbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuLyoqXG4gKiBSZXR1cm5zIGEgbmV3IGFycmF5IHRoYXQgZHJvcHMgYWxsIGVsZW1lbnRzIGluIHRoZSBnaXZlbiBjb2xsZWN0aW9uIHVudGlsIHRoZVxuICogZmlyc3QgZWxlbWVudCB0aGF0IGRvZXMgbm90IG1hdGNoIHRoZSBnaXZlbiBwcmVkaWNhdGUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyBkcm9wV2hpbGUgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9jb2xsZWN0aW9ucy9kcm9wX3doaWxlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IG51bWJlcnMgPSBbMywgMiwgNSwgMiwgNV07XG4gKiBjb25zdCBkcm9wV2hpbGVOdW1iZXJzID0gZHJvcFdoaWxlKG51bWJlcnMsIChpKSA9PiBpICE9PSAyKTtcbiAqXG4gKiBhc3NlcnRFcXVhbHMoZHJvcFdoaWxlTnVtYmVycywgWzIsIDUsIDIsIDVdKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gZHJvcFdoaWxlPFQ+KFxuICBhcnJheTogcmVhZG9ubHkgVFtdLFxuICBwcmVkaWNhdGU6IChlbDogVCkgPT4gYm9vbGVhbixcbik6IFRbXSB7XG4gIGxldCBvZmZzZXQgPSAwO1xuICBjb25zdCBsZW5ndGggPSBhcnJheS5sZW5ndGg7XG5cbiAgd2hpbGUgKGxlbmd0aCA+IG9mZnNldCAmJiBwcmVkaWNhdGUoYXJyYXlbb2Zmc2V0XSkpIHtcbiAgICBvZmZzZXQrKztcbiAgfVxuXG4gIHJldHVybiBhcnJheS5zbGljZShvZmZzZXQsIGxlbmd0aCk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7Ozs7Q0FjQyxHQUNELE9BQU8sU0FBUyxVQUNkLEtBQW1CLEVBQ25CLFNBQTZCO0VBRTdCLElBQUksU0FBUztFQUNiLE1BQU0sU0FBUyxNQUFNLE1BQU07RUFFM0IsTUFBTyxTQUFTLFVBQVUsVUFBVSxLQUFLLENBQUMsT0FBTyxFQUFHO0lBQ2xEO0VBQ0Y7RUFFQSxPQUFPLE1BQU0sS0FBSyxDQUFDLFFBQVE7QUFDN0IifQ==