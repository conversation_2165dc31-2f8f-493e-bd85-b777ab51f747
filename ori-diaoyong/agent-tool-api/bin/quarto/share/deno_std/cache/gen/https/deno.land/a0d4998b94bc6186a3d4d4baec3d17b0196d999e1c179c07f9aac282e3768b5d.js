// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { compare } from "./compare.ts";
/**
 * A reverse comparison of two versions. Same as compare but
 * `1` and `-1` are inverted.
 *
 * Sorts in descending order if passed to `Array.sort()`,
 */ export function rcompare(s0, s1) {
  return compare(s1, s0);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9yY29tcGFyZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHR5cGUgeyBTZW1WZXIgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgY29tcGFyZSB9IGZyb20gXCIuL2NvbXBhcmUudHNcIjtcblxuLyoqXG4gKiBBIHJldmVyc2UgY29tcGFyaXNvbiBvZiB0d28gdmVyc2lvbnMuIFNhbWUgYXMgY29tcGFyZSBidXRcbiAqIGAxYCBhbmQgYC0xYCBhcmUgaW52ZXJ0ZWQuXG4gKlxuICogU29ydHMgaW4gZGVzY2VuZGluZyBvcmRlciBpZiBwYXNzZWQgdG8gYEFycmF5LnNvcnQoKWAsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByY29tcGFyZShcbiAgczA6IFNlbVZlcixcbiAgczE6IFNlbVZlcixcbik6IDEgfCAwIHwgLTEge1xuICByZXR1cm4gY29tcGFyZShzMSwgczApO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUFTLE9BQU8sUUFBUSxlQUFlO0FBRXZDOzs7OztDQUtDLEdBQ0QsT0FBTyxTQUFTLFNBQ2QsRUFBVSxFQUNWLEVBQVU7RUFFVixPQUFPLFFBQVEsSUFBSTtBQUNyQiJ9