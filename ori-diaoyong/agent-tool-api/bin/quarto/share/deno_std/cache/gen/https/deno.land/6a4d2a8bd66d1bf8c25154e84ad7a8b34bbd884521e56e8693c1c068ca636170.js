// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { YAMLError } from "../_error.ts";
import { Mark } from "../_mark.ts";
import * as common from "../_utils.ts";
import { LoaderState } from "./loader_state.ts";
const { hasOwn } = Object;
const CONTEXT_FLOW_IN = 1;
const CONTEXT_FLOW_OUT = 2;
const CONTEXT_BLOCK_IN = 3;
const CONTEXT_BLOCK_OUT = 4;
const CHOMPING_CLIP = 1;
const CHOMPING_STRIP = 2;
const CHOMPING_KEEP = 3;
const PATTERN_NON_PRINTABLE = // deno-lint-ignore no-control-regex
/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/;
const PATTERN_NON_ASCII_LINE_BREAKS = /[\x85\u2028\u2029]/;
const PATTERN_FLOW_INDICATORS = /[,\[\]\{\}]/;
const PATTERN_TAG_HANDLE = /^(?:!|!!|![a-z\-]+!)$/i;
const PATTERN_TAG_URI = /^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;
function _class(obj) {
  return Object.prototype.toString.call(obj);
}
function isEOL(c) {
  return c === 0x0a || /* LF */ c === 0x0d /* CR */ ;
}
function isWhiteSpace(c) {
  return c === 0x09 || /* Tab */ c === 0x20 /* Space */ ;
}
function isWsOrEol(c) {
  return c === 0x09 /* Tab */  || c === 0x20 /* Space */  || c === 0x0a /* LF */  || c === 0x0d /* CR */ ;
}
function isFlowIndicator(c) {
  return c === 0x2c /* , */  || c === 0x5b /* [ */  || c === 0x5d /* ] */  || c === 0x7b /* { */  || c === 0x7d /* } */ ;
}
function fromHexCode(c) {
  if (0x30 <= /* 0 */ c && c <= 0x39 /* 9 */ ) {
    return c - 0x30;
  }
  const lc = c | 0x20;
  if (0x61 <= /* a */ lc && lc <= 0x66 /* f */ ) {
    return lc - 0x61 + 10;
  }
  return -1;
}
function escapedHexLen(c) {
  if (c === 0x78 /* x */ ) {
    return 2;
  }
  if (c === 0x75 /* u */ ) {
    return 4;
  }
  if (c === 0x55 /* U */ ) {
    return 8;
  }
  return 0;
}
function fromDecimalCode(c) {
  if (0x30 <= /* 0 */ c && c <= 0x39 /* 9 */ ) {
    return c - 0x30;
  }
  return -1;
}
function simpleEscapeSequence(c) {
  return c === 0x30 /* 0 */  ? "\x00" : c === 0x61 /* a */  ? "\x07" : c === 0x62 /* b */  ? "\x08" : c === 0x74 /* t */  ? "\x09" : c === 0x09 /* Tab */  ? "\x09" : c === 0x6e /* n */  ? "\x0A" : c === 0x76 /* v */  ? "\x0B" : c === 0x66 /* f */  ? "\x0C" : c === 0x72 /* r */  ? "\x0D" : c === 0x65 /* e */  ? "\x1B" : c === 0x20 /* Space */  ? " " : c === 0x22 /* " */  ? "\x22" : c === 0x2f /* / */  ? "/" : c === 0x5c /* \ */  ? "\x5C" : c === 0x4e /* N */  ? "\x85" : c === 0x5f /* _ */  ? "\xA0" : c === 0x4c /* L */  ? "\u2028" : c === 0x50 /* P */  ? "\u2029" : "";
}
function charFromCodepoint(c) {
  if (c <= 0xffff) {
    return String.fromCharCode(c);
  }
  // Encode UTF-16 surrogate pair
  // https://en.wikipedia.org/wiki/UTF-16#Code_points_U.2B010000_to_U.2B10FFFF
  return String.fromCharCode((c - 0x010000 >> 10) + 0xd800, (c - 0x010000 & 0x03ff) + 0xdc00);
}
const simpleEscapeCheck = Array.from({
  length: 256
}); // integer, for fast access
const simpleEscapeMap = Array.from({
  length: 256
});
for(let i = 0; i < 256; i++){
  simpleEscapeCheck[i] = simpleEscapeSequence(i) ? 1 : 0;
  simpleEscapeMap[i] = simpleEscapeSequence(i);
}
function generateError(state, message) {
  return new YAMLError(message, new Mark(state.filename, state.input, state.position, state.line, state.position - state.lineStart));
}
function throwError(state, message) {
  throw generateError(state, message);
}
function throwWarning(state, message) {
  if (state.onWarning) {
    state.onWarning.call(null, generateError(state, message));
  }
}
const directiveHandlers = {
  YAML (state, _name, ...args) {
    if (state.version !== null) {
      return throwError(state, "duplication of %YAML directive");
    }
    if (args.length !== 1) {
      return throwError(state, "YAML directive accepts exactly one argument");
    }
    const match = /^([0-9]+)\.([0-9]+)$/.exec(args[0]);
    if (match === null) {
      return throwError(state, "ill-formed argument of the YAML directive");
    }
    const major = parseInt(match[1], 10);
    const minor = parseInt(match[2], 10);
    if (major !== 1) {
      return throwError(state, "unacceptable YAML version of the document");
    }
    state.version = args[0];
    state.checkLineBreaks = minor < 2;
    if (minor !== 1 && minor !== 2) {
      return throwWarning(state, "unsupported YAML version of the document");
    }
  },
  TAG (state, _name, ...args) {
    if (args.length !== 2) {
      return throwError(state, "TAG directive accepts exactly two arguments");
    }
    const handle = args[0];
    const prefix = args[1];
    if (!PATTERN_TAG_HANDLE.test(handle)) {
      return throwError(state, "ill-formed tag handle (first argument) of the TAG directive");
    }
    if (state.tagMap && hasOwn(state.tagMap, handle)) {
      return throwError(state, `there is a previously declared suffix for "${handle}" tag handle`);
    }
    if (!PATTERN_TAG_URI.test(prefix)) {
      return throwError(state, "ill-formed tag prefix (second argument) of the TAG directive");
    }
    if (typeof state.tagMap === "undefined") {
      state.tagMap = Object.create(null);
    }
    state.tagMap[handle] = prefix;
  }
};
function captureSegment(state, start, end, checkJson) {
  let result;
  if (start < end) {
    result = state.input.slice(start, end);
    if (checkJson) {
      for(let position = 0, length = result.length; position < length; position++){
        const character = result.charCodeAt(position);
        if (!(character === 0x09 || 0x20 <= character && character <= 0x10ffff)) {
          return throwError(state, "expected valid JSON character");
        }
      }
    } else if (PATTERN_NON_PRINTABLE.test(result)) {
      return throwError(state, "the stream contains non-printable characters");
    }
    state.result += result;
  }
}
function mergeMappings(state, destination, source, overridableKeys) {
  if (!common.isObject(source)) {
    return throwError(state, "cannot merge mappings; the provided source object is unacceptable");
  }
  const keys = Object.keys(source);
  for(let i = 0, len = keys.length; i < len; i++){
    const key = keys[i];
    if (!hasOwn(destination, key)) {
      Object.defineProperty(destination, key, {
        value: source[key],
        writable: true,
        enumerable: true,
        configurable: true
      });
      overridableKeys[key] = true;
    }
  }
}
function storeMappingPair(state, result, overridableKeys, keyTag, keyNode, valueNode, startLine, startPos) {
  // The output is a plain object here, so keys can only be strings.
  // We need to convert keyNode to a string, but doing so can hang the process
  // (deeply nested arrays that explode exponentially using aliases).
  if (Array.isArray(keyNode)) {
    keyNode = Array.prototype.slice.call(keyNode);
    for(let index = 0, quantity = keyNode.length; index < quantity; index++){
      if (Array.isArray(keyNode[index])) {
        return throwError(state, "nested arrays are not supported inside keys");
      }
      if (typeof keyNode === "object" && _class(keyNode[index]) === "[object Object]") {
        keyNode[index] = "[object Object]";
      }
    }
  }
  // Avoid code execution in load() via toString property
  // (still use its own toString for arrays, timestamps,
  // and whatever user schema extensions happen to have @@toStringTag)
  if (typeof keyNode === "object" && _class(keyNode) === "[object Object]") {
    keyNode = "[object Object]";
  }
  keyNode = String(keyNode);
  if (result === null) {
    result = {};
  }
  if (keyTag === "tag:yaml.org,2002:merge") {
    if (Array.isArray(valueNode)) {
      for(let index = 0, quantity = valueNode.length; index < quantity; index++){
        mergeMappings(state, result, valueNode[index], overridableKeys);
      }
    } else {
      mergeMappings(state, result, valueNode, overridableKeys);
    }
  } else {
    if (!state.json && !hasOwn(overridableKeys, keyNode) && hasOwn(result, keyNode)) {
      state.line = startLine || state.line;
      state.position = startPos || state.position;
      return throwError(state, "duplicated mapping key");
    }
    Object.defineProperty(result, keyNode, {
      value: valueNode,
      writable: true,
      enumerable: true,
      configurable: true
    });
    delete overridableKeys[keyNode];
  }
  return result;
}
function readLineBreak(state) {
  const ch = state.input.charCodeAt(state.position);
  if (ch === 0x0a /* LF */ ) {
    state.position++;
  } else if (ch === 0x0d /* CR */ ) {
    state.position++;
    if (state.input.charCodeAt(state.position) === 0x0a /* LF */ ) {
      state.position++;
    }
  } else {
    return throwError(state, "a line break is expected");
  }
  state.line += 1;
  state.lineStart = state.position;
}
function skipSeparationSpace(state, allowComments, checkIndent) {
  let lineBreaks = 0, ch = state.input.charCodeAt(state.position);
  while(ch !== 0){
    while(isWhiteSpace(ch)){
      ch = state.input.charCodeAt(++state.position);
    }
    if (allowComments && ch === 0x23 /* # */ ) {
      do {
        ch = state.input.charCodeAt(++state.position);
      }while (ch !== 0x0a && /* LF */ ch !== 0x0d && /* CR */ ch !== 0)
    }
    if (isEOL(ch)) {
      readLineBreak(state);
      ch = state.input.charCodeAt(state.position);
      lineBreaks++;
      state.lineIndent = 0;
      while(ch === 0x20 /* Space */ ){
        state.lineIndent++;
        ch = state.input.charCodeAt(++state.position);
      }
    } else {
      break;
    }
  }
  if (checkIndent !== -1 && lineBreaks !== 0 && state.lineIndent < checkIndent) {
    throwWarning(state, "deficient indentation");
  }
  return lineBreaks;
}
function testDocumentSeparator(state) {
  let _position = state.position;
  let ch = state.input.charCodeAt(_position);
  // Condition state.position === state.lineStart is tested
  // in parent on each call, for efficiency. No needs to test here again.
  if ((ch === 0x2d || /* - */ ch === 0x2e) && ch === state.input.charCodeAt(_position + 1) && ch === state.input.charCodeAt(_position + 2)) {
    _position += 3;
    ch = state.input.charCodeAt(_position);
    if (ch === 0 || isWsOrEol(ch)) {
      return true;
    }
  }
  return false;
}
function writeFoldedLines(state, count) {
  if (count === 1) {
    state.result += " ";
  } else if (count > 1) {
    state.result += common.repeat("\n", count - 1);
  }
}
function readPlainScalar(state, nodeIndent, withinFlowCollection) {
  const kind = state.kind;
  const result = state.result;
  let ch = state.input.charCodeAt(state.position);
  if (isWsOrEol(ch) || isFlowIndicator(ch) || ch === 0x23 /* # */  || ch === 0x26 /* & */  || ch === 0x2a /* * */  || ch === 0x21 /* ! */  || ch === 0x7c /* | */  || ch === 0x3e /* > */  || ch === 0x27 /* ' */  || ch === 0x22 /* " */  || ch === 0x25 /* % */  || ch === 0x40 /* @ */  || ch === 0x60 /* ` */ ) {
    return false;
  }
  let following;
  if (ch === 0x3f || /* ? */ ch === 0x2d /* - */ ) {
    following = state.input.charCodeAt(state.position + 1);
    if (isWsOrEol(following) || withinFlowCollection && isFlowIndicator(following)) {
      return false;
    }
  }
  state.kind = "scalar";
  state.result = "";
  let captureEnd, captureStart = captureEnd = state.position;
  let hasPendingContent = false;
  let line = 0;
  while(ch !== 0){
    if (ch === 0x3a /* : */ ) {
      following = state.input.charCodeAt(state.position + 1);
      if (isWsOrEol(following) || withinFlowCollection && isFlowIndicator(following)) {
        break;
      }
    } else if (ch === 0x23 /* # */ ) {
      const preceding = state.input.charCodeAt(state.position - 1);
      if (isWsOrEol(preceding)) {
        break;
      }
    } else if (state.position === state.lineStart && testDocumentSeparator(state) || withinFlowCollection && isFlowIndicator(ch)) {
      break;
    } else if (isEOL(ch)) {
      line = state.line;
      const lineStart = state.lineStart;
      const lineIndent = state.lineIndent;
      skipSeparationSpace(state, false, -1);
      if (state.lineIndent >= nodeIndent) {
        hasPendingContent = true;
        ch = state.input.charCodeAt(state.position);
        continue;
      } else {
        state.position = captureEnd;
        state.line = line;
        state.lineStart = lineStart;
        state.lineIndent = lineIndent;
        break;
      }
    }
    if (hasPendingContent) {
      captureSegment(state, captureStart, captureEnd, false);
      writeFoldedLines(state, state.line - line);
      captureStart = captureEnd = state.position;
      hasPendingContent = false;
    }
    if (!isWhiteSpace(ch)) {
      captureEnd = state.position + 1;
    }
    ch = state.input.charCodeAt(++state.position);
  }
  captureSegment(state, captureStart, captureEnd, false);
  if (state.result) {
    return true;
  }
  state.kind = kind;
  state.result = result;
  return false;
}
function readSingleQuotedScalar(state, nodeIndent) {
  let ch, captureStart, captureEnd;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 0x27 /* ' */ ) {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  state.position++;
  captureStart = captureEnd = state.position;
  while((ch = state.input.charCodeAt(state.position)) !== 0){
    if (ch === 0x27 /* ' */ ) {
      captureSegment(state, captureStart, state.position, true);
      ch = state.input.charCodeAt(++state.position);
      if (ch === 0x27 /* ' */ ) {
        captureStart = state.position;
        state.position++;
        captureEnd = state.position;
      } else {
        return true;
      }
    } else if (isEOL(ch)) {
      captureSegment(state, captureStart, captureEnd, true);
      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));
      captureStart = captureEnd = state.position;
    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {
      return throwError(state, "unexpected end of the document within a single quoted scalar");
    } else {
      state.position++;
      captureEnd = state.position;
    }
  }
  return throwError(state, "unexpected end of the stream within a single quoted scalar");
}
function readDoubleQuotedScalar(state, nodeIndent) {
  let ch = state.input.charCodeAt(state.position);
  if (ch !== 0x22 /* " */ ) {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  state.position++;
  let captureEnd, captureStart = captureEnd = state.position;
  let tmp;
  while((ch = state.input.charCodeAt(state.position)) !== 0){
    if (ch === 0x22 /* " */ ) {
      captureSegment(state, captureStart, state.position, true);
      state.position++;
      return true;
    }
    if (ch === 0x5c /* \ */ ) {
      captureSegment(state, captureStart, state.position, true);
      ch = state.input.charCodeAt(++state.position);
      if (isEOL(ch)) {
        skipSeparationSpace(state, false, nodeIndent);
      // TODO(bartlomieju): rework to inline fn with no type cast?
      } else if (ch < 256 && simpleEscapeCheck[ch]) {
        state.result += simpleEscapeMap[ch];
        state.position++;
      } else if ((tmp = escapedHexLen(ch)) > 0) {
        let hexLength = tmp;
        let hexResult = 0;
        for(; hexLength > 0; hexLength--){
          ch = state.input.charCodeAt(++state.position);
          if ((tmp = fromHexCode(ch)) >= 0) {
            hexResult = (hexResult << 4) + tmp;
          } else {
            return throwError(state, "expected hexadecimal character");
          }
        }
        state.result += charFromCodepoint(hexResult);
        state.position++;
      } else {
        return throwError(state, "unknown escape sequence");
      }
      captureStart = captureEnd = state.position;
    } else if (isEOL(ch)) {
      captureSegment(state, captureStart, captureEnd, true);
      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));
      captureStart = captureEnd = state.position;
    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {
      return throwError(state, "unexpected end of the document within a double quoted scalar");
    } else {
      state.position++;
      captureEnd = state.position;
    }
  }
  return throwError(state, "unexpected end of the stream within a double quoted scalar");
}
function readFlowCollection(state, nodeIndent) {
  let ch = state.input.charCodeAt(state.position);
  let terminator;
  let isMapping = true;
  let result = {};
  if (ch === 0x5b /* [ */ ) {
    terminator = 0x5d; /* ] */ 
    isMapping = false;
    result = [];
  } else if (ch === 0x7b /* { */ ) {
    terminator = 0x7d; /* } */ 
  } else {
    return false;
  }
  if (state.anchor !== null && typeof state.anchor !== "undefined" && typeof state.anchorMap !== "undefined") {
    state.anchorMap[state.anchor] = result;
  }
  ch = state.input.charCodeAt(++state.position);
  const tag = state.tag, anchor = state.anchor;
  let readNext = true;
  let valueNode, keyNode, keyTag = keyNode = valueNode = null, isExplicitPair, isPair = isExplicitPair = false;
  let following = 0, line = 0;
  const overridableKeys = Object.create(null);
  while(ch !== 0){
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if (ch === terminator) {
      state.position++;
      state.tag = tag;
      state.anchor = anchor;
      state.kind = isMapping ? "mapping" : "sequence";
      state.result = result;
      return true;
    }
    if (!readNext) {
      return throwError(state, "missed comma between flow collection entries");
    }
    keyTag = keyNode = valueNode = null;
    isPair = isExplicitPair = false;
    if (ch === 0x3f /* ? */ ) {
      following = state.input.charCodeAt(state.position + 1);
      if (isWsOrEol(following)) {
        isPair = isExplicitPair = true;
        state.position++;
        skipSeparationSpace(state, true, nodeIndent);
      }
    }
    line = state.line;
    composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);
    keyTag = state.tag || null;
    keyNode = state.result;
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if ((isExplicitPair || state.line === line) && ch === 0x3a /* : */ ) {
      isPair = true;
      ch = state.input.charCodeAt(++state.position);
      skipSeparationSpace(state, true, nodeIndent);
      composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);
      valueNode = state.result;
    }
    if (isMapping) {
      storeMappingPair(state, result, overridableKeys, keyTag, keyNode, valueNode);
    } else if (isPair) {
      result.push(storeMappingPair(state, null, overridableKeys, keyTag, keyNode, valueNode));
    } else {
      result.push(keyNode);
    }
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if (ch === 0x2c /* , */ ) {
      readNext = true;
      ch = state.input.charCodeAt(++state.position);
    } else {
      readNext = false;
    }
  }
  return throwError(state, "unexpected end of the stream within a flow collection");
}
function readBlockScalar(state, nodeIndent) {
  let chomping = CHOMPING_CLIP, didReadContent = false, detectedIndent = false, textIndent = nodeIndent, emptyLines = 0, atMoreIndented = false;
  let ch = state.input.charCodeAt(state.position);
  let folding = false;
  if (ch === 0x7c /* | */ ) {
    folding = false;
  } else if (ch === 0x3e /* > */ ) {
    folding = true;
  } else {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  let tmp = 0;
  while(ch !== 0){
    ch = state.input.charCodeAt(++state.position);
    if (ch === 0x2b || /* + */ ch === 0x2d /* - */ ) {
      if (CHOMPING_CLIP === chomping) {
        chomping = ch === 0x2b /* + */  ? CHOMPING_KEEP : CHOMPING_STRIP;
      } else {
        return throwError(state, "repeat of a chomping mode identifier");
      }
    } else if ((tmp = fromDecimalCode(ch)) >= 0) {
      if (tmp === 0) {
        return throwError(state, "bad explicit indentation width of a block scalar; it cannot be less than one");
      } else if (!detectedIndent) {
        textIndent = nodeIndent + tmp - 1;
        detectedIndent = true;
      } else {
        return throwError(state, "repeat of an indentation width identifier");
      }
    } else {
      break;
    }
  }
  if (isWhiteSpace(ch)) {
    do {
      ch = state.input.charCodeAt(++state.position);
    }while (isWhiteSpace(ch))
    if (ch === 0x23 /* # */ ) {
      do {
        ch = state.input.charCodeAt(++state.position);
      }while (!isEOL(ch) && ch !== 0)
    }
  }
  while(ch !== 0){
    readLineBreak(state);
    state.lineIndent = 0;
    ch = state.input.charCodeAt(state.position);
    while((!detectedIndent || state.lineIndent < textIndent) && ch === 0x20 /* Space */ ){
      state.lineIndent++;
      ch = state.input.charCodeAt(++state.position);
    }
    if (!detectedIndent && state.lineIndent > textIndent) {
      textIndent = state.lineIndent;
    }
    if (isEOL(ch)) {
      emptyLines++;
      continue;
    }
    // End of the scalar.
    if (state.lineIndent < textIndent) {
      // Perform the chomping.
      if (chomping === CHOMPING_KEEP) {
        state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
      } else if (chomping === CHOMPING_CLIP) {
        if (didReadContent) {
          // i.e. only if the scalar is not empty.
          state.result += "\n";
        }
      }
      break;
    }
    // Folded style: use fancy rules to handle line breaks.
    if (folding) {
      // Lines starting with white space characters (more-indented lines) are not folded.
      if (isWhiteSpace(ch)) {
        atMoreIndented = true;
        // except for the first content line (cf. Example 8.1)
        state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
      // End of more-indented block.
      } else if (atMoreIndented) {
        atMoreIndented = false;
        state.result += common.repeat("\n", emptyLines + 1);
      // Just one line break - perceive as the same line.
      } else if (emptyLines === 0) {
        if (didReadContent) {
          // i.e. only if we have already read some scalar content.
          state.result += " ";
        }
      // Several line breaks - perceive as different lines.
      } else {
        state.result += common.repeat("\n", emptyLines);
      }
    // Literal style: just add exact number of line breaks between content lines.
    } else {
      // Keep all line breaks except the header line break.
      state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
    }
    didReadContent = true;
    detectedIndent = true;
    emptyLines = 0;
    const captureStart = state.position;
    while(!isEOL(ch) && ch !== 0){
      ch = state.input.charCodeAt(++state.position);
    }
    captureSegment(state, captureStart, state.position, false);
  }
  return true;
}
function readBlockSequence(state, nodeIndent) {
  let line, following, detected = false, ch;
  const tag = state.tag, anchor = state.anchor, result = [];
  if (state.anchor !== null && typeof state.anchor !== "undefined" && typeof state.anchorMap !== "undefined") {
    state.anchorMap[state.anchor] = result;
  }
  ch = state.input.charCodeAt(state.position);
  while(ch !== 0){
    if (ch !== 0x2d /* - */ ) {
      break;
    }
    following = state.input.charCodeAt(state.position + 1);
    if (!isWsOrEol(following)) {
      break;
    }
    detected = true;
    state.position++;
    if (skipSeparationSpace(state, true, -1)) {
      if (state.lineIndent <= nodeIndent) {
        result.push(null);
        ch = state.input.charCodeAt(state.position);
        continue;
      }
    }
    line = state.line;
    composeNode(state, nodeIndent, CONTEXT_BLOCK_IN, false, true);
    result.push(state.result);
    skipSeparationSpace(state, true, -1);
    ch = state.input.charCodeAt(state.position);
    if ((state.line === line || state.lineIndent > nodeIndent) && ch !== 0) {
      return throwError(state, "bad indentation of a sequence entry");
    } else if (state.lineIndent < nodeIndent) {
      break;
    }
  }
  if (detected) {
    state.tag = tag;
    state.anchor = anchor;
    state.kind = "sequence";
    state.result = result;
    return true;
  }
  return false;
}
function readBlockMapping(state, nodeIndent, flowIndent) {
  const tag = state.tag, anchor = state.anchor, result = {}, overridableKeys = Object.create(null);
  let following, allowCompact = false, line, pos, keyTag = null, keyNode = null, valueNode = null, atExplicitKey = false, detected = false, ch;
  if (state.anchor !== null && typeof state.anchor !== "undefined" && typeof state.anchorMap !== "undefined") {
    state.anchorMap[state.anchor] = result;
  }
  ch = state.input.charCodeAt(state.position);
  while(ch !== 0){
    following = state.input.charCodeAt(state.position + 1);
    line = state.line; // Save the current line.
    pos = state.position;
    //
    // Explicit notation case. There are two separate blocks:
    // first for the key (denoted by "?") and second for the value (denoted by ":")
    //
    if ((ch === 0x3f || /* ? */ ch === 0x3a) && /* : */ isWsOrEol(following)) {
      if (ch === 0x3f /* ? */ ) {
        if (atExplicitKey) {
          storeMappingPair(state, result, overridableKeys, keyTag, keyNode, null);
          keyTag = keyNode = valueNode = null;
        }
        detected = true;
        atExplicitKey = true;
        allowCompact = true;
      } else if (atExplicitKey) {
        // i.e. 0x3A/* : */ === character after the explicit key.
        atExplicitKey = false;
        allowCompact = true;
      } else {
        return throwError(state, "incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line");
      }
      state.position += 1;
      ch = following;
    //
    // Implicit notation case. Flow-style node as the key first, then ":", and the value.
    //
    } else if (composeNode(state, flowIndent, CONTEXT_FLOW_OUT, false, true)) {
      if (state.line === line) {
        ch = state.input.charCodeAt(state.position);
        while(isWhiteSpace(ch)){
          ch = state.input.charCodeAt(++state.position);
        }
        if (ch === 0x3a /* : */ ) {
          ch = state.input.charCodeAt(++state.position);
          if (!isWsOrEol(ch)) {
            return throwError(state, "a whitespace character is expected after the key-value separator within a block mapping");
          }
          if (atExplicitKey) {
            storeMappingPair(state, result, overridableKeys, keyTag, keyNode, null);
            keyTag = keyNode = valueNode = null;
          }
          detected = true;
          atExplicitKey = false;
          allowCompact = false;
          keyTag = state.tag;
          keyNode = state.result;
        } else if (detected) {
          return throwError(state, "can not read an implicit mapping pair; a colon is missed");
        } else {
          state.tag = tag;
          state.anchor = anchor;
          return true; // Keep the result of `composeNode`.
        }
      } else if (detected) {
        return throwError(state, "can not read a block mapping entry; a multiline key may not be an implicit key");
      } else {
        state.tag = tag;
        state.anchor = anchor;
        return true; // Keep the result of `composeNode`.
      }
    } else {
      break; // Reading is done. Go to the epilogue.
    }
    //
    // Common reading code for both explicit and implicit notations.
    //
    if (state.line === line || state.lineIndent > nodeIndent) {
      if (composeNode(state, nodeIndent, CONTEXT_BLOCK_OUT, true, allowCompact)) {
        if (atExplicitKey) {
          keyNode = state.result;
        } else {
          valueNode = state.result;
        }
      }
      if (!atExplicitKey) {
        storeMappingPair(state, result, overridableKeys, keyTag, keyNode, valueNode, line, pos);
        keyTag = keyNode = valueNode = null;
      }
      skipSeparationSpace(state, true, -1);
      ch = state.input.charCodeAt(state.position);
    }
    if (state.lineIndent > nodeIndent && ch !== 0) {
      return throwError(state, "bad indentation of a mapping entry");
    } else if (state.lineIndent < nodeIndent) {
      break;
    }
  }
  //
  // Epilogue.
  //
  // Special case: last mapping's node contains only the key in explicit notation.
  if (atExplicitKey) {
    storeMappingPair(state, result, overridableKeys, keyTag, keyNode, null);
  }
  // Expose the resulting mapping.
  if (detected) {
    state.tag = tag;
    state.anchor = anchor;
    state.kind = "mapping";
    state.result = result;
  }
  return detected;
}
function readTagProperty(state) {
  let position, isVerbatim = false, isNamed = false, tagHandle = "", tagName, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 0x21 /* ! */ ) return false;
  if (state.tag !== null) {
    return throwError(state, "duplication of a tag property");
  }
  ch = state.input.charCodeAt(++state.position);
  if (ch === 0x3c /* < */ ) {
    isVerbatim = true;
    ch = state.input.charCodeAt(++state.position);
  } else if (ch === 0x21 /* ! */ ) {
    isNamed = true;
    tagHandle = "!!";
    ch = state.input.charCodeAt(++state.position);
  } else {
    tagHandle = "!";
  }
  position = state.position;
  if (isVerbatim) {
    do {
      ch = state.input.charCodeAt(++state.position);
    }while (ch !== 0 && ch !== 0x3e /* > */ )
    if (state.position < state.length) {
      tagName = state.input.slice(position, state.position);
      ch = state.input.charCodeAt(++state.position);
    } else {
      return throwError(state, "unexpected end of the stream within a verbatim tag");
    }
  } else {
    while(ch !== 0 && !isWsOrEol(ch)){
      if (ch === 0x21 /* ! */ ) {
        if (!isNamed) {
          tagHandle = state.input.slice(position - 1, state.position + 1);
          if (!PATTERN_TAG_HANDLE.test(tagHandle)) {
            return throwError(state, "named tag handle cannot contain such characters");
          }
          isNamed = true;
          position = state.position + 1;
        } else {
          return throwError(state, "tag suffix cannot contain exclamation marks");
        }
      }
      ch = state.input.charCodeAt(++state.position);
    }
    tagName = state.input.slice(position, state.position);
    if (PATTERN_FLOW_INDICATORS.test(tagName)) {
      return throwError(state, "tag suffix cannot contain flow indicator characters");
    }
  }
  if (tagName && !PATTERN_TAG_URI.test(tagName)) {
    return throwError(state, `tag name cannot contain such characters: ${tagName}`);
  }
  if (isVerbatim) {
    state.tag = tagName;
  } else if (typeof state.tagMap !== "undefined" && hasOwn(state.tagMap, tagHandle)) {
    state.tag = state.tagMap[tagHandle] + tagName;
  } else if (tagHandle === "!") {
    state.tag = `!${tagName}`;
  } else if (tagHandle === "!!") {
    state.tag = `tag:yaml.org,2002:${tagName}`;
  } else {
    return throwError(state, `undeclared tag handle "${tagHandle}"`);
  }
  return true;
}
function readAnchorProperty(state) {
  let ch = state.input.charCodeAt(state.position);
  if (ch !== 0x26 /* & */ ) return false;
  if (state.anchor !== null) {
    return throwError(state, "duplication of an anchor property");
  }
  ch = state.input.charCodeAt(++state.position);
  const position = state.position;
  while(ch !== 0 && !isWsOrEol(ch) && !isFlowIndicator(ch)){
    ch = state.input.charCodeAt(++state.position);
  }
  if (state.position === position) {
    return throwError(state, "name of an anchor node must contain at least one character");
  }
  state.anchor = state.input.slice(position, state.position);
  return true;
}
function readAlias(state) {
  let ch = state.input.charCodeAt(state.position);
  if (ch !== 0x2a /* * */ ) return false;
  ch = state.input.charCodeAt(++state.position);
  const _position = state.position;
  while(ch !== 0 && !isWsOrEol(ch) && !isFlowIndicator(ch)){
    ch = state.input.charCodeAt(++state.position);
  }
  if (state.position === _position) {
    return throwError(state, "name of an alias node must contain at least one character");
  }
  const alias = state.input.slice(_position, state.position);
  if (typeof state.anchorMap !== "undefined" && !hasOwn(state.anchorMap, alias)) {
    return throwError(state, `unidentified alias "${alias}"`);
  }
  if (typeof state.anchorMap !== "undefined") {
    state.result = state.anchorMap[alias];
  }
  skipSeparationSpace(state, true, -1);
  return true;
}
function composeNode(state, parentIndent, nodeContext, allowToSeek, allowCompact) {
  let allowBlockScalars, allowBlockCollections, indentStatus = 1, atNewLine = false, hasContent = false, type, flowIndent, blockIndent;
  if (state.listener && state.listener !== null) {
    state.listener("open", state);
  }
  state.tag = null;
  state.anchor = null;
  state.kind = null;
  state.result = null;
  const allowBlockStyles = allowBlockScalars = allowBlockCollections = CONTEXT_BLOCK_OUT === nodeContext || CONTEXT_BLOCK_IN === nodeContext;
  if (allowToSeek) {
    if (skipSeparationSpace(state, true, -1)) {
      atNewLine = true;
      if (state.lineIndent > parentIndent) {
        indentStatus = 1;
      } else if (state.lineIndent === parentIndent) {
        indentStatus = 0;
      } else if (state.lineIndent < parentIndent) {
        indentStatus = -1;
      }
    }
  }
  if (indentStatus === 1) {
    while(readTagProperty(state) || readAnchorProperty(state)){
      if (skipSeparationSpace(state, true, -1)) {
        atNewLine = true;
        allowBlockCollections = allowBlockStyles;
        if (state.lineIndent > parentIndent) {
          indentStatus = 1;
        } else if (state.lineIndent === parentIndent) {
          indentStatus = 0;
        } else if (state.lineIndent < parentIndent) {
          indentStatus = -1;
        }
      } else {
        allowBlockCollections = false;
      }
    }
  }
  if (allowBlockCollections) {
    allowBlockCollections = atNewLine || allowCompact;
  }
  if (indentStatus === 1 || CONTEXT_BLOCK_OUT === nodeContext) {
    const cond = CONTEXT_FLOW_IN === nodeContext || CONTEXT_FLOW_OUT === nodeContext;
    flowIndent = cond ? parentIndent : parentIndent + 1;
    blockIndent = state.position - state.lineStart;
    if (indentStatus === 1) {
      if (allowBlockCollections && (readBlockSequence(state, blockIndent) || readBlockMapping(state, blockIndent, flowIndent)) || readFlowCollection(state, flowIndent)) {
        hasContent = true;
      } else {
        if (allowBlockScalars && readBlockScalar(state, flowIndent) || readSingleQuotedScalar(state, flowIndent) || readDoubleQuotedScalar(state, flowIndent)) {
          hasContent = true;
        } else if (readAlias(state)) {
          hasContent = true;
          if (state.tag !== null || state.anchor !== null) {
            return throwError(state, "alias node should not have Any properties");
          }
        } else if (readPlainScalar(state, flowIndent, CONTEXT_FLOW_IN === nodeContext)) {
          hasContent = true;
          if (state.tag === null) {
            state.tag = "?";
          }
        }
        if (state.anchor !== null && typeof state.anchorMap !== "undefined") {
          state.anchorMap[state.anchor] = state.result;
        }
      }
    } else if (indentStatus === 0) {
      // Special case: block sequences are allowed to have same indentation level as the parent.
      // http://www.yaml.org/spec/1.2/spec.html#id2799784
      hasContent = allowBlockCollections && readBlockSequence(state, blockIndent);
    }
  }
  if (state.tag !== null && state.tag !== "!") {
    if (state.tag === "?") {
      for(let typeIndex = 0, typeQuantity = state.implicitTypes.length; typeIndex < typeQuantity; typeIndex++){
        type = state.implicitTypes[typeIndex];
        // Implicit resolving is not allowed for non-scalar types, and '?'
        // non-specific tag is only assigned to plain scalars. So, it isn't
        // needed to check for 'kind' conformity.
        if (type.resolve(state.result)) {
          // `state.result` updated in resolver if matched
          state.result = type.construct(state.result);
          state.tag = type.tag;
          if (state.anchor !== null && typeof state.anchorMap !== "undefined") {
            state.anchorMap[state.anchor] = state.result;
          }
          break;
        }
      }
    } else if (hasOwn(state.typeMap[state.kind || "fallback"], state.tag)) {
      type = state.typeMap[state.kind || "fallback"][state.tag];
      if (state.result !== null && type.kind !== state.kind) {
        return throwError(state, `unacceptable node kind for !<${state.tag}> tag; it should be "${type.kind}", not "${state.kind}"`);
      }
      if (!type.resolve(state.result)) {
        // `state.result` updated in resolver if matched
        return throwError(state, `cannot resolve a node with !<${state.tag}> explicit tag`);
      } else {
        state.result = type.construct(state.result);
        if (state.anchor !== null && typeof state.anchorMap !== "undefined") {
          state.anchorMap[state.anchor] = state.result;
        }
      }
    } else {
      return throwError(state, `unknown tag !<${state.tag}>`);
    }
  }
  if (state.listener && state.listener !== null) {
    state.listener("close", state);
  }
  return state.tag !== null || state.anchor !== null || hasContent;
}
function readDocument(state) {
  const documentStart = state.position;
  let position, directiveName, directiveArgs, hasDirectives = false, ch;
  state.version = null;
  state.checkLineBreaks = state.legacy;
  state.tagMap = Object.create(null);
  state.anchorMap = Object.create(null);
  while((ch = state.input.charCodeAt(state.position)) !== 0){
    skipSeparationSpace(state, true, -1);
    ch = state.input.charCodeAt(state.position);
    if (state.lineIndent > 0 || ch !== 0x25 /* % */ ) {
      break;
    }
    hasDirectives = true;
    ch = state.input.charCodeAt(++state.position);
    position = state.position;
    while(ch !== 0 && !isWsOrEol(ch)){
      ch = state.input.charCodeAt(++state.position);
    }
    directiveName = state.input.slice(position, state.position);
    directiveArgs = [];
    if (directiveName.length < 1) {
      return throwError(state, "directive name must not be less than one character in length");
    }
    while(ch !== 0){
      while(isWhiteSpace(ch)){
        ch = state.input.charCodeAt(++state.position);
      }
      if (ch === 0x23 /* # */ ) {
        do {
          ch = state.input.charCodeAt(++state.position);
        }while (ch !== 0 && !isEOL(ch))
        break;
      }
      if (isEOL(ch)) break;
      position = state.position;
      while(ch !== 0 && !isWsOrEol(ch)){
        ch = state.input.charCodeAt(++state.position);
      }
      directiveArgs.push(state.input.slice(position, state.position));
    }
    if (ch !== 0) readLineBreak(state);
    if (hasOwn(directiveHandlers, directiveName)) {
      directiveHandlers[directiveName](state, directiveName, ...directiveArgs);
    } else {
      throwWarning(state, `unknown document directive "${directiveName}"`);
    }
  }
  skipSeparationSpace(state, true, -1);
  if (state.lineIndent === 0 && state.input.charCodeAt(state.position) === 0x2d /* - */  && state.input.charCodeAt(state.position + 1) === 0x2d /* - */  && state.input.charCodeAt(state.position + 2) === 0x2d /* - */ ) {
    state.position += 3;
    skipSeparationSpace(state, true, -1);
  } else if (hasDirectives) {
    return throwError(state, "directives end mark is expected");
  }
  composeNode(state, state.lineIndent - 1, CONTEXT_BLOCK_OUT, false, true);
  skipSeparationSpace(state, true, -1);
  if (state.checkLineBreaks && PATTERN_NON_ASCII_LINE_BREAKS.test(state.input.slice(documentStart, state.position))) {
    throwWarning(state, "non-ASCII line breaks are interpreted as content");
  }
  state.documents.push(state.result);
  if (state.position === state.lineStart && testDocumentSeparator(state)) {
    if (state.input.charCodeAt(state.position) === 0x2e /* . */ ) {
      state.position += 3;
      skipSeparationSpace(state, true, -1);
    }
    return;
  }
  if (state.position < state.length - 1) {
    return throwError(state, "end of the stream or a document separator is expected");
  }
}
function loadDocuments(input, options) {
  input = String(input);
  options = options || {};
  if (input.length !== 0) {
    // Add tailing `\n` if not exists
    if (input.charCodeAt(input.length - 1) !== 0x0a /* LF */  && input.charCodeAt(input.length - 1) !== 0x0d /* CR */ ) {
      input += "\n";
    }
    // Strip BOM
    if (input.charCodeAt(0) === 0xfeff) {
      input = input.slice(1);
    }
  }
  const state = new LoaderState(input, options);
  // Use 0 as string terminator. That significantly simplifies bounds check.
  state.input += "\0";
  while(state.input.charCodeAt(state.position) === 0x20 /* Space */ ){
    state.lineIndent += 1;
    state.position += 1;
  }
  while(state.position < state.length - 1){
    readDocument(state);
  }
  return state.documents;
}
function isCbFunction(fn) {
  return typeof fn === "function";
}
export function loadAll(input, iteratorOrOption, options) {
  if (!isCbFunction(iteratorOrOption)) {
    return loadDocuments(input, iteratorOrOption);
  }
  const documents = loadDocuments(input, options);
  const iterator = iteratorOrOption;
  for(let index = 0, length = documents.length; index < length; index++){
    iterator(documents[index]);
  }
  return void 0;
}
export function load(input, options) {
  const documents = loadDocuments(input, options);
  if (documents.length === 0) {
    return null;
  }
  if (documents.length === 1) {
    return documents[0];
  }
  throw new YAMLError("expected a single document in the stream, but found more");
}
//# sourceMappingURL=data:application/json;base64,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