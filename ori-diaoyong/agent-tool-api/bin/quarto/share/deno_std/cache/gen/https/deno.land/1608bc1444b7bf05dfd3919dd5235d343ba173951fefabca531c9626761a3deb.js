// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { MAP_FORMAT_TO_EXTRACTOR_RX } from "./_formats.ts";
/**
 * Tests if a string has valid front matter. Supports YAML, TOML and JSON.
 *
 * @param str String to test.
 * @param formats A list of formats to test for. Defaults to all supported formats.
 *
 * ```ts
 * import { test, Format } from "https://deno.land/std@$STD_VERSION/front_matter/mod.ts";
 * import { assert } from "https://deno.land/std@$STD_VERSION/assert/assert.ts";
 *
 * assert(test("---\ntitle: Three dashes marks the spot\n---\n"));
 * assert(test("---toml\ntitle = 'Three dashes followed by format marks the spot'\n---\n"));
 * assert(test("---json\n{\"title\": \"Three dashes followed by format marks the spot\"}\n---\n"));
 *
 * assert(!test("---json\n{\"title\": \"Three dashes followed by format marks the spot\"}\n---\n", [Format.YAML]));
 * ```
 */ export function test(str, formats) {
  if (!formats) {
    formats = Object.keys(MAP_FORMAT_TO_EXTRACTOR_RX);
  }
  for (const format of formats){
    if (format === "unknown") {
      throw new TypeError("Unable to test for unknown front matter format");
    }
    const match = MAP_FORMAT_TO_EXTRACTOR_RX[format].exec(str);
    if (match?.index === 0) {
      return true;
    }
  }
  return false;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2Zyb250X21hdHRlci90ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG5cbmltcG9ydCB7IE1BUF9GT1JNQVRfVE9fRVhUUkFDVE9SX1JYIH0gZnJvbSBcIi4vX2Zvcm1hdHMudHNcIjtcblxudHlwZSBGb3JtYXQgPSBcInlhbWxcIiB8IFwidG9tbFwiIHwgXCJqc29uXCIgfCBcInVua25vd25cIjtcblxuLyoqXG4gKiBUZXN0cyBpZiBhIHN0cmluZyBoYXMgdmFsaWQgZnJvbnQgbWF0dGVyLiBTdXBwb3J0cyBZQU1MLCBUT01MIGFuZCBKU09OLlxuICpcbiAqIEBwYXJhbSBzdHIgU3RyaW5nIHRvIHRlc3QuXG4gKiBAcGFyYW0gZm9ybWF0cyBBIGxpc3Qgb2YgZm9ybWF0cyB0byB0ZXN0IGZvci4gRGVmYXVsdHMgdG8gYWxsIHN1cHBvcnRlZCBmb3JtYXRzLlxuICpcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyB0ZXN0LCBGb3JtYXQgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9mcm9udF9tYXR0ZXIvbW9kLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnQgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0LnRzXCI7XG4gKlxuICogYXNzZXJ0KHRlc3QoXCItLS1cXG50aXRsZTogVGhyZWUgZGFzaGVzIG1hcmtzIHRoZSBzcG90XFxuLS0tXFxuXCIpKTtcbiAqIGFzc2VydCh0ZXN0KFwiLS0tdG9tbFxcbnRpdGxlID0gJ1RocmVlIGRhc2hlcyBmb2xsb3dlZCBieSBmb3JtYXQgbWFya3MgdGhlIHNwb3QnXFxuLS0tXFxuXCIpKTtcbiAqIGFzc2VydCh0ZXN0KFwiLS0tanNvblxcbntcXFwidGl0bGVcXFwiOiBcXFwiVGhyZWUgZGFzaGVzIGZvbGxvd2VkIGJ5IGZvcm1hdCBtYXJrcyB0aGUgc3BvdFxcXCJ9XFxuLS0tXFxuXCIpKTtcbiAqXG4gKiBhc3NlcnQoIXRlc3QoXCItLS1qc29uXFxue1xcXCJ0aXRsZVxcXCI6IFxcXCJUaHJlZSBkYXNoZXMgZm9sbG93ZWQgYnkgZm9ybWF0IG1hcmtzIHRoZSBzcG90XFxcIn1cXG4tLS1cXG5cIiwgW0Zvcm1hdC5ZQU1MXSkpO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXN0KFxuICBzdHI6IHN0cmluZyxcbiAgZm9ybWF0cz86IChcInlhbWxcIiB8IFwidG9tbFwiIHwgXCJqc29uXCIgfCBcInVua25vd25cIilbXSxcbik6IGJvb2xlYW4ge1xuICBpZiAoIWZvcm1hdHMpIHtcbiAgICBmb3JtYXRzID0gT2JqZWN0LmtleXMoTUFQX0ZPUk1BVF9UT19FWFRSQUNUT1JfUlgpIGFzIEZvcm1hdFtdO1xuICB9XG5cbiAgZm9yIChjb25zdCBmb3JtYXQgb2YgZm9ybWF0cykge1xuICAgIGlmIChmb3JtYXQgPT09IFwidW5rbm93blwiKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiVW5hYmxlIHRvIHRlc3QgZm9yIHVua25vd24gZnJvbnQgbWF0dGVyIGZvcm1hdFwiKTtcbiAgICB9XG5cbiAgICBjb25zdCBtYXRjaCA9IE1BUF9GT1JNQVRfVE9fRVhUUkFDVE9SX1JYW2Zvcm1hdF0uZXhlYyhzdHIpO1xuICAgIGlmIChtYXRjaD8uaW5kZXggPT09IDApIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFFMUUsU0FBUywwQkFBMEIsUUFBUSxnQkFBZ0I7QUFJM0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FnQkMsR0FDRCxPQUFPLFNBQVMsS0FDZCxHQUFXLEVBQ1gsT0FBa0Q7RUFFbEQsSUFBSSxDQUFDLFNBQVM7SUFDWixVQUFVLE9BQU8sSUFBSSxDQUFDO0VBQ3hCO0VBRUEsS0FBSyxNQUFNLFVBQVUsUUFBUztJQUM1QixJQUFJLFdBQVcsV0FBVztNQUN4QixNQUFNLElBQUksVUFBVTtJQUN0QjtJQUVBLE1BQU0sUUFBUSwwQkFBMEIsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDO0lBQ3RELElBQUksT0FBTyxVQUFVLEdBQUc7TUFDdEIsT0FBTztJQUNUO0VBQ0Y7RUFFQSxPQUFPO0FBQ1QifQ==