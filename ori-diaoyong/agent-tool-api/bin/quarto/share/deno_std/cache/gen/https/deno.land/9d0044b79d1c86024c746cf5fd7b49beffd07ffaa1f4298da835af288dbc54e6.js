// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * {@linkcode sprintf} and {@linkcode printf} for printing formatted strings to
 * stdout.
 *
 * This implementation is inspired by POSIX and Golang but does not port
 * implementation code.
 *
 * sprintf converts and formats a variable number of arguments as is specified
 * by a `format string`. In it's basic form, a format string may just be a
 * literal. In case arguments are meant to be formatted, a `directive` is
 * contained in the format string, preceded by a '%' character:
 *
 *     %<verb>
 *
 * E.g. the verb `s` indicates the directive should be replaced by the string
 * representation of the argument in the corresponding position of the argument
 * list. E.g.:
 *
 *     Hello %s!
 *
 * applied to the arguments "World" yields "Hello World!".
 *
 * The meaning of the format string is modelled after [POSIX][1] format strings
 * as well as well as [Golang format strings][2]. Both contain elements specific
 * to the respective programming language that don't apply to JavaScript, so
 * they can not be fully supported. Furthermore we implement some functionality
 * that is specific to JS.
 *
 * ## Verbs
 *
 * The following verbs are supported:
 *
 * | Verb  | Meaning                                                        |
 * | ----- | -------------------------------------------------------------- |
 * | `%`   | print a literal percent                                        |
 * | `t`   | evaluate arg as boolean, print `true` or `false`               |
 * | `b`   | eval as number, print binary                                   |
 * | `c`   | eval as number, print character corresponding to the codePoint |
 * | `o`   | eval as number, print octal                                    |
 * | `x X` | print as hex (ff FF), treat string as list of bytes            |
 * | `e E` | print number in scientific/exponent format 1.123123e+01        |
 * | `f F` | print number as float with decimal point and no exponent       |
 * | `g G` | use %e %E or %f %F depending on size of argument               |
 * | `s`   | interpolate string                                             |
 * | `T`   | type of arg, as returned by `typeof`                           |
 * | `v`   | value of argument in 'default' format (see below)              |
 * | `j`   | argument as formatted by `JSON.stringify`                      |
 * | `i`   | argument as formatted by `Deno.inspect`                        |
 * | `I`   | argument as formatted by `Deno.inspect` in compact format      |
 *
 * ## Width and Precision
 *
 * Verbs may be modified by providing them with width and precision, either or
 * both may be omitted:
 *
 *     %9f    width 9, default precision
 *     %.9f   default width, precision 9
 *     %8.9f  width 8, precision 9
 *     %8.f   width 9, precision 0
 *
 * In general, 'width' describes the minimum length of the output, while
 * 'precision' limits the output.
 *
 * | verb      | precision                                                       |
 * | --------- | --------------------------------------------------------------- |
 * | `t`       | n/a                                                             |
 * | `b c o`   | n/a                                                             |
 * | `x X`     | n/a for number, strings are truncated to p bytes(!)             |
 * | `e E f F` | number of places after decimal, default 6                       |
 * | `g G`     | set maximum number of digits                                    |
 * | `s`       | truncate input                                                  |
 * | `T`       | truncate                                                        |
 * | `v`       | truncate, or depth if used with # see "'default' format", below |
 * | `j`       | n/a                                                             |
 *
 * Numerical values for width and precision can be substituted for the `*` char,
 * in which case the values are obtained from the next args, e.g.:
 *
 *     sprintf("%*.*f", 9, 8, 456.0)
 *
 * is equivalent to:
 *
 *     sprintf("%9.8f", 456.0)
 *
 * ## Flags
 *
 * The effects of the verb may be further influenced by using flags to modify
 * the directive:
 *
 * | Flag  | Verb      | Meaning                                                                    |
 * | ----- | --------- | -------------------------------------------------------------------------- |
 * | `+`   | numeric   | always print sign                                                          |
 * | `-`   | all       | pad to the right (left justify)                                            |
 * | `#`   |           | alternate format                                                           |
 * | `#`   | `b o x X` | prefix with `0b 0 0x`                                                      |
 * | `#`   | `g G`     | don't remove trailing zeros                                                |
 * | `#`   | `v`       | use output of `inspect` instead of `toString`                              |
 * | `' '` |           | space character                                                            |
 * | `' '` | `x X`     | leave spaces between bytes when printing string                            |
 * | `' '` | `d`       | insert space for missing `+` sign character                                |
 * | `0`   | all       | pad with zero, `-` takes precedence, sign is appended in front of padding  |
 * | `<`   | all       | format elements of the passed array according to the directive (extension) |
 *
 * ## 'default' format
 *
 * The default format used by `%v` is the result of calling `toString()` on the
 * relevant argument. If the `#` flags is used, the result of calling `inspect()`
 * is interpolated. In this case, the precision, if set is passed to `inspect()`
 * as the 'depth' config parameter.
 *
 * ## Positional arguments
 *
 * Arguments do not need to be consumed in the order they are provided and may
 * be consumed more than once. E.g.:
 *
 *     sprintf("%[2]s %[1]s", "World", "Hello")
 *
 * returns "Hello World". The presence of a positional indicator resets the arg
 * counter allowing args to be reused:
 *
 *     sprintf("dec[%d]=%d hex[%[1]d]=%x oct[%[1]d]=%#o %s", 1, 255, "Third")
 *
 * returns `dec[1]=255 hex[1]=0xff oct[1]=0377 Third`
 *
 * Width and precision my also use positionals:
 *
 *     "%[2]*.[1]*d", 1, 2
 *
 * This follows the golang conventions and not POSIX.
 *
 * ## Errors
 *
 * The following errors are handled:
 *
 * Incorrect verb:
 *
 *     S("%h", "") %!(BAD VERB 'h')
 *
 * Too few arguments:
 *
 *     S("%d") %!(MISSING 'd')"
 *
 * [1]: https://pubs.opengroup.org/onlinepubs/009695399/functions/fprintf.html
 * [2]: https://golang.org/pkg/fmt/
 *
 * @module
 */ var State;
(function(State) {
  State[State["PASSTHROUGH"] = 0] = "PASSTHROUGH";
  State[State["PERCENT"] = 1] = "PERCENT";
  State[State["POSITIONAL"] = 2] = "POSITIONAL";
  State[State["PRECISION"] = 3] = "PRECISION";
  State[State["WIDTH"] = 4] = "WIDTH";
})(State || (State = {}));
var WorP;
(function(WorP) {
  WorP[WorP["WIDTH"] = 0] = "WIDTH";
  WorP[WorP["PRECISION"] = 1] = "PRECISION";
})(WorP || (WorP = {}));
class Flags {
  plus;
  dash;
  sharp;
  space;
  zero;
  lessthan;
  width = -1;
  precision = -1;
}
const min = Math.min;
const UNICODE_REPLACEMENT_CHARACTER = "\ufffd";
const DEFAULT_PRECISION = 6;
const FLOAT_REGEXP = /(-?)(\d)\.?(\d*)e([+-])(\d+)/;
var F;
(function(F) {
  F[F["sign"] = 1] = "sign";
  F[F["mantissa"] = 2] = "mantissa";
  F[F["fractional"] = 3] = "fractional";
  F[F["esign"] = 4] = "esign";
  F[F["exponent"] = 5] = "exponent";
})(F || (F = {}));
class Printf {
  format;
  args;
  i;
  state = State.PASSTHROUGH;
  verb = "";
  buf = "";
  argNum = 0;
  flags = new Flags();
  haveSeen;
  // barf, store precision and width errors for later processing ...
  tmpError;
  constructor(format, ...args){
    this.format = format;
    this.args = args;
    this.haveSeen = Array.from({
      length: args.length
    });
    this.i = 0;
  }
  doPrintf() {
    for(; this.i < this.format.length; ++this.i){
      const c = this.format[this.i];
      switch(this.state){
        case State.PASSTHROUGH:
          if (c === "%") {
            this.state = State.PERCENT;
          } else {
            this.buf += c;
          }
          break;
        case State.PERCENT:
          if (c === "%") {
            this.buf += c;
            this.state = State.PASSTHROUGH;
          } else {
            this.handleFormat();
          }
          break;
        default:
          throw Error("Should be unreachable, certainly a bug in the lib.");
      }
    }
    // check for unhandled args
    let extras = false;
    let err = "%!(EXTRA";
    for(let i = 0; i !== this.haveSeen.length; ++i){
      if (!this.haveSeen[i]) {
        extras = true;
        err += ` '${Deno.inspect(this.args[i])}'`;
      }
    }
    err += ")";
    if (extras) {
      this.buf += err;
    }
    return this.buf;
  }
  // %[<positional>]<flag>...<verb>
  handleFormat() {
    this.flags = new Flags();
    const flags = this.flags;
    for(; this.i < this.format.length; ++this.i){
      const c = this.format[this.i];
      switch(this.state){
        case State.PERCENT:
          switch(c){
            case "[":
              this.handlePositional();
              this.state = State.POSITIONAL;
              break;
            case "+":
              flags.plus = true;
              break;
            case "<":
              flags.lessthan = true;
              break;
            case "-":
              flags.dash = true;
              flags.zero = false; // only left pad zeros, dash takes precedence
              break;
            case "#":
              flags.sharp = true;
              break;
            case " ":
              flags.space = true;
              break;
            case "0":
              // only left pad zeros, dash takes precedence
              flags.zero = !flags.dash;
              break;
            default:
              if ("1" <= c && c <= "9" || c === "." || c === "*") {
                if (c === ".") {
                  this.flags.precision = 0;
                  this.state = State.PRECISION;
                  this.i++;
                } else {
                  this.state = State.WIDTH;
                }
                this.handleWidthAndPrecision(flags);
              } else {
                this.handleVerb();
                return; // always end in verb
              }
          } // switch c
          break;
        case State.POSITIONAL:
          // TODO(bartlomieju): either a verb or * only verb for now
          if (c === "*") {
            const worp = this.flags.precision === -1 ? WorP.WIDTH : WorP.PRECISION;
            this.handleWidthOrPrecisionRef(worp);
            this.state = State.PERCENT;
            break;
          } else {
            this.handleVerb();
            return; // always end in verb
          }
        default:
          throw new Error(`Should not be here ${this.state}, library bug!`);
      } // switch state
    }
  }
  /**
   * Handle width or precision
   * @param wOrP
   */ handleWidthOrPrecisionRef(wOrP) {
    if (this.argNum >= this.args.length) {
      // handle Positional should have already taken care of it...
      return;
    }
    const arg = this.args[this.argNum];
    this.haveSeen[this.argNum] = true;
    if (typeof arg === "number") {
      switch(wOrP){
        case WorP.WIDTH:
          this.flags.width = arg;
          break;
        default:
          this.flags.precision = arg;
      }
    } else {
      const tmp = wOrP === WorP.WIDTH ? "WIDTH" : "PREC";
      this.tmpError = `%!(BAD ${tmp} '${this.args[this.argNum]}')`;
    }
    this.argNum++;
  }
  /**
   * Handle width and precision
   * @param flags
   */ handleWidthAndPrecision(flags) {
    const fmt = this.format;
    for(; this.i !== this.format.length; ++this.i){
      const c = fmt[this.i];
      switch(this.state){
        case State.WIDTH:
          switch(c){
            case ".":
              // initialize precision, %9.f -> precision=0
              this.flags.precision = 0;
              this.state = State.PRECISION;
              break;
            case "*":
              this.handleWidthOrPrecisionRef(WorP.WIDTH);
              break;
            default:
              {
                const val = parseInt(c);
                // most likely parseInt does something stupid that makes
                // it unusable for this scenario ...
                // if we encounter a non (number|*|.) we're done with prec & wid
                if (isNaN(val)) {
                  this.i--;
                  this.state = State.PERCENT;
                  return;
                }
                flags.width = flags.width === -1 ? 0 : flags.width;
                flags.width *= 10;
                flags.width += val;
              }
          } // switch c
          break;
        case State.PRECISION:
          {
            if (c === "*") {
              this.handleWidthOrPrecisionRef(WorP.PRECISION);
              break;
            }
            const val = parseInt(c);
            if (isNaN(val)) {
              // one too far, rewind
              this.i--;
              this.state = State.PERCENT;
              return;
            }
            flags.precision *= 10;
            flags.precision += val;
            break;
          }
        default:
          throw new Error("can't be here. bug.");
      } // switch state
    }
  }
  /** Handle positional */ handlePositional() {
    if (this.format[this.i] !== "[") {
      // sanity only
      throw new Error("Can't happen? Bug.");
    }
    let positional = 0;
    const format = this.format;
    this.i++;
    let err = false;
    for(; this.i !== this.format.length; ++this.i){
      if (format[this.i] === "]") {
        break;
      }
      positional *= 10;
      const val = parseInt(format[this.i]);
      if (isNaN(val)) {
        //throw new Error(
        //  `invalid character in positional: ${format}[${format[this.i]}]`
        //);
        this.tmpError = "%!(BAD INDEX)";
        err = true;
      }
      positional += val;
    }
    if (positional - 1 >= this.args.length) {
      this.tmpError = "%!(BAD INDEX)";
      err = true;
    }
    this.argNum = err ? this.argNum : positional - 1;
  }
  /** Handle less than */ handleLessThan() {
    // deno-lint-ignore no-explicit-any
    const arg = this.args[this.argNum];
    if ((arg || {}).constructor.name !== "Array") {
      throw new Error(`arg ${arg} is not an array. Todo better error handling`);
    }
    let str = "[ ";
    for(let i = 0; i !== arg.length; ++i){
      if (i !== 0) str += ", ";
      str += this._handleVerb(arg[i]);
    }
    return str + " ]";
  }
  /** Handle verb */ handleVerb() {
    const verb = this.format[this.i];
    this.verb = verb;
    if (this.tmpError) {
      this.buf += this.tmpError;
      this.tmpError = undefined;
      if (this.argNum < this.haveSeen.length) {
        this.haveSeen[this.argNum] = true; // keep track of used args
      }
    } else if (this.args.length <= this.argNum) {
      this.buf += `%!(MISSING '${verb}')`;
    } else {
      const arg = this.args[this.argNum]; // check out of range
      this.haveSeen[this.argNum] = true; // keep track of used args
      if (this.flags.lessthan) {
        this.buf += this.handleLessThan();
      } else {
        this.buf += this._handleVerb(arg);
      }
    }
    this.argNum++; // if there is a further positional, it will reset.
    this.state = State.PASSTHROUGH;
  }
  // deno-lint-ignore no-explicit-any
  _handleVerb(arg) {
    switch(this.verb){
      case "t":
        return this.pad(arg.toString());
      case "b":
        return this.fmtNumber(arg, 2);
      case "c":
        return this.fmtNumberCodePoint(arg);
      case "d":
        return this.fmtNumber(arg, 10);
      case "o":
        return this.fmtNumber(arg, 8);
      case "x":
        return this.fmtHex(arg);
      case "X":
        return this.fmtHex(arg, true);
      case "e":
        return this.fmtFloatE(arg);
      case "E":
        return this.fmtFloatE(arg, true);
      case "f":
      case "F":
        return this.fmtFloatF(arg);
      case "g":
        return this.fmtFloatG(arg);
      case "G":
        return this.fmtFloatG(arg, true);
      case "s":
        return this.fmtString(arg);
      case "T":
        return this.fmtString(typeof arg);
      case "v":
        return this.fmtV(arg);
      case "j":
        return this.fmtJ(arg);
      case "i":
        return this.fmtI(arg, false);
      case "I":
        return this.fmtI(arg, true);
      default:
        return `%!(BAD VERB '${this.verb}')`;
    }
  }
  /**
   * Pad a string
   * @param s text to pad
   */ pad(s) {
    const padding = this.flags.zero ? "0" : " ";
    if (this.flags.dash) {
      return s.padEnd(this.flags.width, padding);
    }
    return s.padStart(this.flags.width, padding);
  }
  /**
   * Pad a number
   * @param nStr
   * @param neg
   */ padNum(nStr, neg) {
    let sign;
    if (neg) {
      sign = "-";
    } else if (this.flags.plus || this.flags.space) {
      sign = this.flags.plus ? "+" : " ";
    } else {
      sign = "";
    }
    const zero = this.flags.zero;
    if (!zero) {
      // sign comes in front of padding when padding w/ zero,
      // in from of value if padding with spaces.
      nStr = sign + nStr;
    }
    const pad = zero ? "0" : " ";
    const len = zero ? this.flags.width - sign.length : this.flags.width;
    if (this.flags.dash) {
      nStr = nStr.padEnd(len, pad);
    } else {
      nStr = nStr.padStart(len, pad);
    }
    if (zero) {
      // see above
      nStr = sign + nStr;
    }
    return nStr;
  }
  /**
   * Format a number
   * @param n
   * @param radix
   * @param upcase
   */ fmtNumber(n, radix, upcase = false) {
    let num = Math.abs(n).toString(radix);
    const prec = this.flags.precision;
    if (prec !== -1) {
      this.flags.zero = false;
      num = n === 0 && prec === 0 ? "" : num;
      while(num.length < prec){
        num = "0" + num;
      }
    }
    let prefix = "";
    if (this.flags.sharp) {
      switch(radix){
        case 2:
          prefix += "0b";
          break;
        case 8:
          // don't annotate octal 0 with 0...
          prefix += num.startsWith("0") ? "" : "0";
          break;
        case 16:
          prefix += "0x";
          break;
        default:
          throw new Error("cannot handle base: " + radix);
      }
    }
    // don't add prefix in front of value truncated by precision=0, val=0
    num = num.length === 0 ? num : prefix + num;
    if (upcase) {
      num = num.toUpperCase();
    }
    return this.padNum(num, n < 0);
  }
  /**
   * Format number with code points
   * @param n
   */ fmtNumberCodePoint(n) {
    let s = "";
    try {
      s = String.fromCodePoint(n);
    } catch  {
      s = UNICODE_REPLACEMENT_CHARACTER;
    }
    return this.pad(s);
  }
  /**
   * Format special float
   * @param n
   */ fmtFloatSpecial(n) {
    // formatting of NaN and Inf are pants-on-head
    // stupid and more or less arbitrary.
    if (isNaN(n)) {
      this.flags.zero = false;
      return this.padNum("NaN", false);
    }
    if (n === Number.POSITIVE_INFINITY) {
      this.flags.zero = false;
      this.flags.plus = true;
      return this.padNum("Inf", false);
    }
    if (n === Number.NEGATIVE_INFINITY) {
      this.flags.zero = false;
      return this.padNum("Inf", true);
    }
    return "";
  }
  /**
   * Round fraction to precision
   * @param fractional
   * @param precision
   * @returns tuple of fractional and round
   */ roundFractionToPrecision(fractional, precision) {
    let round = false;
    if (fractional.length > precision) {
      fractional = "1" + fractional; // prepend a 1 in case of leading 0
      let tmp = parseInt(fractional.slice(0, precision + 2)) / 10;
      tmp = Math.round(tmp);
      fractional = Math.floor(tmp).toString();
      round = fractional[0] === "2";
      fractional = fractional.slice(1); // remove extra 1
    } else {
      while(fractional.length < precision){
        fractional += "0";
      }
    }
    return [
      fractional,
      round
    ];
  }
  /**
   * Format float E
   * @param n
   * @param upcase
   */ fmtFloatE(n, upcase = false) {
    const special = this.fmtFloatSpecial(n);
    if (special !== "") {
      return special;
    }
    const m = n.toExponential().match(FLOAT_REGEXP);
    if (!m) {
      throw Error("can't happen, bug");
    }
    let fractional = m[F.fractional];
    const precision = this.flags.precision !== -1 ? this.flags.precision : DEFAULT_PRECISION;
    let rounding = false;
    [fractional, rounding] = this.roundFractionToPrecision(fractional, precision);
    let e = m[F.exponent];
    let esign = m[F.esign];
    // scientific notation output with exponent padded to minlen 2
    let mantissa = parseInt(m[F.mantissa]);
    if (rounding) {
      mantissa += 1;
      if (10 <= mantissa) {
        mantissa = 1;
        const r = parseInt(esign + e) + 1;
        e = r.toString();
        esign = r < 0 ? "-" : "+";
      }
    }
    e = e.length === 1 ? "0" + e : e;
    const val = `${mantissa}.${fractional}${upcase ? "E" : "e"}${esign}${e}`;
    return this.padNum(val, n < 0);
  }
  /**
   * Format float F
   * @param n
   */ fmtFloatF(n) {
    const special = this.fmtFloatSpecial(n);
    if (special !== "") {
      return special;
    }
    // stupid helper that turns a number into a (potentially)
    // VERY long string.
    function expandNumber(n) {
      if (Number.isSafeInteger(n)) {
        return n.toString() + ".";
      }
      const t = n.toExponential().split("e");
      let m = t[0].replace(".", "");
      const e = parseInt(t[1]);
      if (e < 0) {
        let nStr = "0.";
        for(let i = 0; i !== Math.abs(e) - 1; ++i){
          nStr += "0";
        }
        return nStr += m;
      } else {
        const splIdx = e + 1;
        while(m.length < splIdx){
          m += "0";
        }
        return m.slice(0, splIdx) + "." + m.slice(splIdx);
      }
    }
    // avoiding sign makes padding easier
    const val = expandNumber(Math.abs(n));
    const arr = val.split(".");
    let dig = arr[0];
    let fractional = arr[1];
    const precision = this.flags.precision !== -1 ? this.flags.precision : DEFAULT_PRECISION;
    let round = false;
    [fractional, round] = this.roundFractionToPrecision(fractional, precision);
    if (round) {
      dig = (parseInt(dig) + 1).toString();
    }
    return this.padNum(`${dig}.${fractional}`, n < 0);
  }
  /**
   * Format float G
   * @param n
   * @param upcase
   */ fmtFloatG(n, upcase = false) {
    const special = this.fmtFloatSpecial(n);
    if (special !== "") {
      return special;
    }
    // The double argument representing a floating-point number shall be
    // converted in the style f or e (or in the style F or E in
    // the case of a G conversion specifier), depending on the
    // value converted and the precision. Let P equal the
    // precision if non-zero, 6 if the precision is omitted, or 1
    // if the precision is zero. Then, if a conversion with style E would
    // have an exponent of X:
    //     - If P > X>=-4, the conversion shall be with style f (or F )
    //     and precision P -( X+1).
    //     - Otherwise, the conversion shall be with style e (or E )
    //     and precision P -1.
    // Finally, unless the '#' flag is used, any trailing zeros shall be
    // removed from the fractional portion of the result and the
    // decimal-point character shall be removed if there is no
    // fractional portion remaining.
    // A double argument representing an infinity or NaN shall be
    // converted in the style of an f or F conversion specifier.
    // https://pubs.opengroup.org/onlinepubs/9699919799/functions/fprintf.html
    let P = this.flags.precision !== -1 ? this.flags.precision : DEFAULT_PRECISION;
    P = P === 0 ? 1 : P;
    const m = n.toExponential().match(FLOAT_REGEXP);
    if (!m) {
      throw Error("can't happen");
    }
    const X = parseInt(m[F.exponent]) * (m[F.esign] === "-" ? -1 : 1);
    let nStr = "";
    if (P > X && X >= -4) {
      this.flags.precision = P - (X + 1);
      nStr = this.fmtFloatF(n);
      if (!this.flags.sharp) {
        nStr = nStr.replace(/\.?0*$/, "");
      }
    } else {
      this.flags.precision = P - 1;
      nStr = this.fmtFloatE(n);
      if (!this.flags.sharp) {
        nStr = nStr.replace(/\.?0*e/, upcase ? "E" : "e");
      }
    }
    return nStr;
  }
  /**
   * Format string
   * @param s
   */ fmtString(s) {
    if (this.flags.precision !== -1) {
      s = s.slice(0, this.flags.precision);
    }
    return this.pad(s);
  }
  /**
   * Format hex
   * @param val
   * @param upper
   */ fmtHex(val, upper = false) {
    // allow others types ?
    switch(typeof val){
      case "number":
        return this.fmtNumber(val, 16, upper);
      case "string":
        {
          const sharp = this.flags.sharp && val.length !== 0;
          let hex = sharp ? "0x" : "";
          const prec = this.flags.precision;
          const end = prec !== -1 ? min(prec, val.length) : val.length;
          for(let i = 0; i !== end; ++i){
            if (i !== 0 && this.flags.space) {
              hex += sharp ? " 0x" : " ";
            }
            // TODO(bartlomieju): for now only taking into account the
            // lower half of the codePoint, ie. as if a string
            // is a list of 8bit values instead of UCS2 runes
            const c = (val.charCodeAt(i) & 0xff).toString(16);
            hex += c.length === 1 ? `0${c}` : c;
          }
          if (upper) {
            hex = hex.toUpperCase();
          }
          return this.pad(hex);
        }
      default:
        throw new Error("currently only number and string are implemented for hex");
    }
  }
  /**
   * Format value
   * @param val
   */ fmtV(val) {
    if (this.flags.sharp) {
      const options = this.flags.precision !== -1 ? {
        depth: this.flags.precision
      } : {};
      return this.pad(Deno.inspect(val, options));
    } else {
      const p = this.flags.precision;
      return p === -1 ? val.toString() : val.toString().slice(0, p);
    }
  }
  /**
   * Format JSON
   * @param val
   */ fmtJ(val) {
    return JSON.stringify(val);
  }
  /**
   * Format inspect
   * @param val
   * @param compact Whether or not the output should be compact.
   */ fmtI(val, compact) {
    return Deno.inspect(val, {
      colors: !Deno?.noColor,
      compact,
      depth: Infinity,
      iterableLimit: Infinity
    });
  }
}
/**
 * Converts and format a variable number of `args` as is specified by `format`.
 * `sprintf` returns the formatted string.
 *
 * @param format
 * @param args
 */ export function sprintf(format, ...args) {
  const printf = new Printf(format, ...args);
  return printf.doPrintf();
}
/**
 * Converts and format a variable number of `args` as is specified by `format`.
 * `printf` writes the formatted string to standard output.
 * @param format
 * @param args
 */ export function printf(format, ...args) {
  const s = sprintf(format, ...args);
  Deno.stdout.writeSync(new TextEncoder().encode(s));
}
//# sourceMappingURL=data:application/json;base64,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