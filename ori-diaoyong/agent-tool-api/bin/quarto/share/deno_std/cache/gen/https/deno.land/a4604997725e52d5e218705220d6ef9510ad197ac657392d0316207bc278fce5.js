// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns an element if and only if that element is the only one matching the
 * given condition. Returns `undefined` otherwise.
 *
 * @example
 * ```ts
 * import { findSingle } from "https://deno.land/std@$STD_VERSION/collections/find_single.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const bookings = [
 *   { month: "January", active: false },
 *   { month: "March", active: false },
 *   { month: "June", active: true },
 * ];
 * const activeBooking = findSingle(bookings, (it) => it.active);
 * const inactiveBooking = findSingle(bookings, (it) => !it.active);
 *
 * assertEquals(activeBooking, { month: "June", active: true });
 * assertEquals(inactiveBooking, undefined); // there are two applicable items
 * ```
 */ export function findSingle(array, predicate) {
  let match = undefined;
  let found = false;
  for (const element of array){
    if (predicate(element)) {
      if (found) {
        return undefined;
      }
      found = true;
      match = element;
    }
  }
  return match;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2ZpbmRfc2luZ2xlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogUmV0dXJucyBhbiBlbGVtZW50IGlmIGFuZCBvbmx5IGlmIHRoYXQgZWxlbWVudCBpcyB0aGUgb25seSBvbmUgbWF0Y2hpbmcgdGhlXG4gKiBnaXZlbiBjb25kaXRpb24uIFJldHVybnMgYHVuZGVmaW5lZGAgb3RoZXJ3aXNlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgZmluZFNpbmdsZSB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL2ZpbmRfc2luZ2xlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IGJvb2tpbmdzID0gW1xuICogICB7IG1vbnRoOiBcIkphbnVhcnlcIiwgYWN0aXZlOiBmYWxzZSB9LFxuICogICB7IG1vbnRoOiBcIk1hcmNoXCIsIGFjdGl2ZTogZmFsc2UgfSxcbiAqICAgeyBtb250aDogXCJKdW5lXCIsIGFjdGl2ZTogdHJ1ZSB9LFxuICogXTtcbiAqIGNvbnN0IGFjdGl2ZUJvb2tpbmcgPSBmaW5kU2luZ2xlKGJvb2tpbmdzLCAoaXQpID0+IGl0LmFjdGl2ZSk7XG4gKiBjb25zdCBpbmFjdGl2ZUJvb2tpbmcgPSBmaW5kU2luZ2xlKGJvb2tpbmdzLCAoaXQpID0+ICFpdC5hY3RpdmUpO1xuICpcbiAqIGFzc2VydEVxdWFscyhhY3RpdmVCb29raW5nLCB7IG1vbnRoOiBcIkp1bmVcIiwgYWN0aXZlOiB0cnVlIH0pO1xuICogYXNzZXJ0RXF1YWxzKGluYWN0aXZlQm9va2luZywgdW5kZWZpbmVkKTsgLy8gdGhlcmUgYXJlIHR3byBhcHBsaWNhYmxlIGl0ZW1zXG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZpbmRTaW5nbGU8VD4oXG4gIGFycmF5OiBJdGVyYWJsZTxUPixcbiAgcHJlZGljYXRlOiAoZWw6IFQpID0+IGJvb2xlYW4sXG4pOiBUIHwgdW5kZWZpbmVkIHtcbiAgbGV0IG1hdGNoOiBUIHwgdW5kZWZpbmVkID0gdW5kZWZpbmVkO1xuICBsZXQgZm91bmQgPSBmYWxzZTtcbiAgZm9yIChjb25zdCBlbGVtZW50IG9mIGFycmF5KSB7XG4gICAgaWYgKHByZWRpY2F0ZShlbGVtZW50KSkge1xuICAgICAgaWYgKGZvdW5kKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgICB9XG4gICAgICBmb3VuZCA9IHRydWU7XG4gICAgICBtYXRjaCA9IGVsZW1lbnQ7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG1hdGNoO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0JDLEdBQ0QsT0FBTyxTQUFTLFdBQ2QsS0FBa0IsRUFDbEIsU0FBNkI7RUFFN0IsSUFBSSxRQUF1QjtFQUMzQixJQUFJLFFBQVE7RUFDWixLQUFLLE1BQU0sV0FBVyxNQUFPO0lBQzNCLElBQUksVUFBVSxVQUFVO01BQ3RCLElBQUksT0FBTztRQUNULE9BQU87TUFDVDtNQUNBLFFBQVE7TUFDUixRQUFRO0lBQ1Y7RUFDRjtFQUVBLE9BQU87QUFDVCJ9