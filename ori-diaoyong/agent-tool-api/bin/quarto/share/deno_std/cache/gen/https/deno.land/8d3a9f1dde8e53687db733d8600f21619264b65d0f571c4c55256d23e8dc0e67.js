// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * @deprecated (will be removed after 0.205.0) Use ReadableStream and WritableStream instead of Reader and Writer.
 *
 * Functions for encoding binary data in array buffers.
 *
 * @module
 */ const rawTypeSizes = {
  int8: 1,
  uint8: 1,
  int16: 2,
  uint16: 2,
  int32: 4,
  uint32: 4,
  int64: 8,
  uint64: 8,
  float32: 4,
  float64: 8
};
/**
 * @deprecated (will be removed after 0.205.0)
 * Number of bytes required to store `dataType`.
 */ export function sizeof(dataType) {
  return rawTypeSizes[dataType];
}
/**
 * @deprecated (will be removed after 0.205.0) Use ReadableStreamBYOBReader to read exact number of bytes.
 *
 * Reads the exact number of bytes from `r` required to fill `b`.
 *
 * Throws `Deno.errors.UnexpectedEof` if `n` bytes cannot be read. */ export async function readExact(r, b) {
  let totalRead = 0;
  do {
    const tmp = new Uint8Array(b.length - totalRead);
    const nRead = await r.read(tmp);
    if (nRead === null) throw new Deno.errors.UnexpectedEof();
    b.set(tmp, totalRead);
    totalRead += nRead;
  }while (totalRead < b.length)
}
/**
 * @deprecated (will be removed after 0.205.0) Use ReadableStreamBYOBReader to read exact number of bytes.
 *
 * Reads exactly `n` bytes from `r`.
 *
 * Resolves it in a `Uint8Array`, or throws `Deno.errors.UnexpectedEof` if `n` bytes cannot be read. */ export async function getNBytes(r, n) {
  const scratch = new Uint8Array(n);
  await readExact(r, scratch);
  return scratch;
}
/**
 * @deprecated (will be removed after 0.205.0) Use DataView instead.
 *
 * Decodes a number from `b`. If `o.bytes` is shorter than `sizeof(o.dataType)`, returns `null`.
 *
 * `o.dataType` defaults to `"int32"`. */ export function varnum(b, o = {}) {
  o.dataType = o.dataType ?? "int32";
  const littleEndian = (o.endian ?? "big") === "little" ? true : false;
  if (b.length < sizeof(o.dataType)) return null;
  const view = new DataView(b.buffer);
  switch(o.dataType){
    case "int8":
      return view.getInt8(b.byteOffset);
    case "uint8":
      return view.getUint8(b.byteOffset);
    case "int16":
      return view.getInt16(b.byteOffset, littleEndian);
    case "uint16":
      return view.getUint16(b.byteOffset, littleEndian);
    case "int32":
      return view.getInt32(b.byteOffset, littleEndian);
    case "uint32":
      return view.getUint32(b.byteOffset, littleEndian);
    case "float32":
      return view.getFloat32(b.byteOffset, littleEndian);
    case "float64":
      return view.getFloat64(b.byteOffset, littleEndian);
  }
}
/**
 * @deprecated (will be removed after 0.205.0) use DataView instead.
 *
 * Decodes a bigint from `b`. If `o.bytes` is shorter than `sizeof(o.dataType)`, returns `null`.
 *
 * `o.dataType` defaults to `"int64"`. */ export function varbig(b, o = {}) {
  o.dataType = o.dataType ?? "int64";
  const littleEndian = (o.endian ?? "big") === "little" ? true : false;
  if (b.length < sizeof(o.dataType)) return null;
  const view = new DataView(b.buffer);
  switch(o.dataType){
    case "int8":
      return BigInt(view.getInt8(b.byteOffset));
    case "uint8":
      return BigInt(view.getUint8(b.byteOffset));
    case "int16":
      return BigInt(view.getInt16(b.byteOffset, littleEndian));
    case "uint16":
      return BigInt(view.getUint16(b.byteOffset, littleEndian));
    case "int32":
      return BigInt(view.getInt32(b.byteOffset, littleEndian));
    case "uint32":
      return BigInt(view.getUint32(b.byteOffset, littleEndian));
    case "int64":
      return view.getBigInt64(b.byteOffset, littleEndian);
    case "uint64":
      return view.getBigUint64(b.byteOffset, littleEndian);
  }
}
/**
 * @deprecated (will be removed after 0.205.0) Use DataView instead.
 *
 * Encodes number `x` into `b`. Returns the number of bytes used, or `0` if `b` is shorter than `sizeof(o.dataType)`.
 *
 * `o.dataType` defaults to `"int32"`. */ export function putVarnum(b, x, o = {}) {
  o.dataType = o.dataType ?? "int32";
  const littleEndian = (o.endian ?? "big") === "little" ? true : false;
  if (b.length < sizeof(o.dataType)) return 0;
  const view = new DataView(b.buffer);
  switch(o.dataType){
    case "int8":
      view.setInt8(b.byteOffset, x);
      break;
    case "uint8":
      view.setUint8(b.byteOffset, x);
      break;
    case "int16":
      view.setInt16(b.byteOffset, x, littleEndian);
      break;
    case "uint16":
      view.setUint16(b.byteOffset, x, littleEndian);
      break;
    case "int32":
      view.setInt32(b.byteOffset, x, littleEndian);
      break;
    case "uint32":
      view.setUint32(b.byteOffset, x, littleEndian);
      break;
    case "float32":
      view.setFloat32(b.byteOffset, x, littleEndian);
      break;
    case "float64":
      view.setFloat64(b.byteOffset, x, littleEndian);
      break;
  }
  return sizeof(o.dataType);
}
/**
 * @deprecated (will be removed after 0.205.0) Use DataView instead.
 *
 * Encodes bigint `x` into `b`. Returns the number of bytes used, or `0` if `b` is shorter than `sizeof(o.dataType)`.
 *
 * `o.dataType` defaults to `"int64"`. */ export function putVarbig(b, x, o = {}) {
  o.dataType = o.dataType ?? "int64";
  const littleEndian = (o.endian ?? "big") === "little" ? true : false;
  if (b.length < sizeof(o.dataType)) return 0;
  const view = new DataView(b.buffer);
  switch(o.dataType){
    case "int8":
      view.setInt8(b.byteOffset, Number(x));
      break;
    case "uint8":
      view.setUint8(b.byteOffset, Number(x));
      break;
    case "int16":
      view.setInt16(b.byteOffset, Number(x), littleEndian);
      break;
    case "uint16":
      view.setUint16(b.byteOffset, Number(x), littleEndian);
      break;
    case "int32":
      view.setInt32(b.byteOffset, Number(x), littleEndian);
      break;
    case "uint32":
      view.setUint32(b.byteOffset, Number(x), littleEndian);
      break;
    case "int64":
      view.setBigInt64(b.byteOffset, x, littleEndian);
      break;
    case "uint64":
      view.setBigUint64(b.byteOffset, x, littleEndian);
      break;
  }
  return sizeof(o.dataType);
}
/**
 * @deprecated (will be removed after 0.205.0) Use ReadableStreamBYOBReader and DataView instead.
 *
 * Decodes a number from `r`, consuming `sizeof(o.dataType)` bytes. If less than `sizeof(o.dataType)` bytes were read, throws `Deno.errors.unexpectedEof`.
 *
 * `o.dataType` defaults to `"int32"`. */ export async function readVarnum(r, o = {}) {
  o.dataType = o.dataType ?? "int32";
  const scratch = await getNBytes(r, sizeof(o.dataType));
  return varnum(scratch, o);
}
/**
 * @deprecated (will be removed after 0.205.0) Use ReadableStreamBYOBReader and DataView instead.
 *
 * Decodes a bigint from `r`, consuming `sizeof(o.dataType)` bytes. If less than `sizeof(o.dataType)` bytes were read, throws `Deno.errors.unexpectedEof`.
 *
 * `o.dataType` defaults to `"int64"`. */ export async function readVarbig(r, o = {}) {
  o.dataType = o.dataType ?? "int64";
  const scratch = await getNBytes(r, sizeof(o.dataType));
  return varbig(scratch, o);
}
/**
 * @deprecated (will be removed after 0.205.0) Use WritableStream and DataView instead.
 *
 * Encodes and writes `x` to `w`. Resolves to the number of bytes written.
 *
 * `o.dataType` defaults to `"int32"`. */ export function writeVarnum(w, x, o = {}) {
  o.dataType = o.dataType ?? "int32";
  const scratch = new Uint8Array(sizeof(o.dataType));
  putVarnum(scratch, x, o);
  return w.write(scratch);
}
/**
 * @deprecated (will be removed after 0.205.0) Use WritableStream and DataView instead.
 *
 * Encodes and writes `x` to `w`. Resolves to the number of bytes written.
 *
 * `o.dataType` defaults to `"int64"`. */ export function writeVarbig(w, x, o = {}) {
  o.dataType = o.dataType ?? "int64";
  const scratch = new Uint8Array(sizeof(o.dataType));
  putVarbig(scratch, x, o);
  return w.write(scratch);
}
/**
 * @deprecated (will be removed after 0.205.0) Use DataView instead.
 *
 * Encodes `x` into a new `Uint8Array`.
 *
 * `o.dataType` defaults to `"int32"` */ export function varnumBytes(x, o = {}) {
  o.dataType = o.dataType ?? "int32";
  const b = new Uint8Array(sizeof(o.dataType));
  putVarnum(b, x, o);
  return b;
}
/**
 * @deprecated (will be removed after 0.205.0) Use DataView instead.
 *
 * Encodes `x` into a new `Uint8Array`.
 *
 * `o.dataType` defaults to `"int64"` */ export function varbigBytes(x, o = {}) {
  o.dataType = o.dataType ?? "int64";
  const b = new Uint8Array(sizeof(o.dataType));
  putVarbig(b, x, o);
  return b;
}
//# sourceMappingURL=data:application/json;base64,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