// Originally ported from Go:
// https://github.com/golang/go/blob/go1.12.5/src/encoding/csv/
// Copyright 2011 The Go Authors. All rights reserved. BSD license.
// https://github.com/golang/go/blob/master/LICENSE
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { assert } from "../assert/assert.ts";
export const defaultReadOptions = {
  separator: ",",
  trimLeadingSpace: false
};
export async function parseRecord(line, reader, opt, startLine, lineIndex = startLine) {
  // line starting with comment character is ignored
  if (opt.comment && line[0] === opt.comment) {
    return [];
  }
  assert(opt.separator !== undefined);
  let fullLine = line;
  let quoteError = null;
  const quote = '"';
  const quoteLen = quote.length;
  const separatorLen = opt.separator.length;
  let recordBuffer = "";
  const fieldIndexes = [];
  parseField: for(;;){
    if (opt.trimLeadingSpace) {
      line = line.trimStart();
    }
    if (line.length === 0 || !line.startsWith(quote)) {
      // Non-quoted string field
      const i = line.indexOf(opt.separator);
      let field = line;
      if (i >= 0) {
        field = field.substring(0, i);
      }
      // Check to make sure a quote does not appear in field.
      if (!opt.lazyQuotes) {
        const j = field.indexOf(quote);
        if (j >= 0) {
          const col = runeCount(fullLine.slice(0, fullLine.length - line.slice(j).length));
          quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_BARE_QUOTE);
          break parseField;
        }
      }
      recordBuffer += field;
      fieldIndexes.push(recordBuffer.length);
      if (i >= 0) {
        line = line.substring(i + separatorLen);
        continue parseField;
      }
      break parseField;
    } else {
      // Quoted string field
      line = line.substring(quoteLen);
      for(;;){
        const i = line.indexOf(quote);
        if (i >= 0) {
          // Hit next quote.
          recordBuffer += line.substring(0, i);
          line = line.substring(i + quoteLen);
          if (line.startsWith(quote)) {
            // `""` sequence (append quote).
            recordBuffer += quote;
            line = line.substring(quoteLen);
          } else if (line.startsWith(opt.separator)) {
            // `","` sequence (end of field).
            line = line.substring(separatorLen);
            fieldIndexes.push(recordBuffer.length);
            continue parseField;
          } else if (0 === line.length) {
            // `"\n` sequence (end of line).
            fieldIndexes.push(recordBuffer.length);
            break parseField;
          } else if (opt.lazyQuotes) {
            // `"` sequence (bare quote).
            recordBuffer += quote;
          } else {
            // `"*` sequence (invalid non-escaped quote).
            const col = runeCount(fullLine.slice(0, fullLine.length - line.length - quoteLen));
            quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
            break parseField;
          }
        } else if (line.length > 0 || !reader.isEOF()) {
          // Hit end of line (copy all data so far).
          recordBuffer += line;
          const r = await reader.readLine();
          lineIndex++;
          line = r ?? ""; // This is a workaround for making this module behave similarly to the encoding/csv/reader.go.
          fullLine = line;
          if (r === null) {
            // Abrupt end of file (EOF or error).
            if (!opt.lazyQuotes) {
              const col = runeCount(fullLine);
              quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
              break parseField;
            }
            fieldIndexes.push(recordBuffer.length);
            break parseField;
          }
          recordBuffer += "\n"; // preserve line feed (This is because TextProtoReader removes it.)
        } else {
          // Abrupt end of file (EOF on error).
          if (!opt.lazyQuotes) {
            const col = runeCount(fullLine);
            quoteError = new ParseError(startLine + 1, lineIndex, col, ERR_QUOTE);
            break parseField;
          }
          fieldIndexes.push(recordBuffer.length);
          break parseField;
        }
      }
    }
  }
  if (quoteError) {
    throw quoteError;
  }
  const result = [];
  let preIdx = 0;
  for (const i of fieldIndexes){
    result.push(recordBuffer.slice(preIdx, i));
    preIdx = i;
  }
  return result;
}
function runeCount(s) {
  // Array.from considers the surrogate pair.
  return Array.from(s).length;
}
/**
 * A ParseError is returned for parsing errors.
 * Line numbers are 1-indexed and columns are 0-indexed.
 */ export class ParseError extends SyntaxError {
  /** Line where the record starts*/ startLine;
  /** Line where the error occurred */ line;
  /** Column (rune index) where the error occurred */ column;
  constructor(start, line, column, message){
    super();
    this.startLine = start;
    this.column = column;
    this.line = line;
    if (message === ERR_FIELD_COUNT) {
      this.message = `record on line ${line}: ${message}`;
    } else if (start !== line) {
      this.message = `record on line ${start}; parse error on line ${line}, column ${column}: ${message}`;
    } else {
      this.message = `parse error on line ${line}, column ${column}: ${message}`;
    }
  }
}
export const ERR_BARE_QUOTE = 'bare " in non-quoted-field';
export const ERR_QUOTE = 'extraneous or missing " in quoted-field';
export const ERR_INVALID_DELIM = "Invalid Delimiter";
export const ERR_FIELD_COUNT = "wrong number of fields";
export function convertRowToObject(row, headers, index) {
  if (row.length !== headers.length) {
    throw new Error(`Error number of fields line: ${index}\nNumber of fields found: ${headers.length}\nExpected number of fields: ${row.length}`);
  }
  const out = {};
  for(let i = 0; i < row.length; i++){
    out[headers[i]] = row[i];
  }
  return out;
}
//# sourceMappingURL=data:application/json;base64,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