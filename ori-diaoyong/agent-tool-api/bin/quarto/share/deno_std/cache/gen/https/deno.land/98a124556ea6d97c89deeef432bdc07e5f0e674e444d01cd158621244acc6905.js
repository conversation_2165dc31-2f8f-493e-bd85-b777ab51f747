// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { readInt } from "./read_int.ts";
const MAX_SAFE_INTEGER = BigInt(Number.MAX_SAFE_INTEGER);
/**
 * Read big endian 64bit long from <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @param buf
 *
 * @deprecated (will be removed after 1.0.0) Use Web Streams instead.
 */ export async function readLong(buf) {
  const high = await readInt(buf);
  if (high === null) return null;
  const low = await readInt(buf);
  if (low === null) throw new Deno.errors.UnexpectedEof();
  const big = BigInt(high) << 32n | BigInt(low);
  // We probably should provide a similar API that returns BigInt values.
  if (big > MAX_SAFE_INTEGER) {
    throw new RangeError("Long value too big to be represented as a JavaScript number.");
  }
  return Number(big);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2lvL3JlYWRfbG9uZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuXG5pbXBvcnQgeyB0eXBlIEJ1ZlJlYWRlciB9IGZyb20gXCIuL2J1Zl9yZWFkZXIudHNcIjtcbmltcG9ydCB7IHJlYWRJbnQgfSBmcm9tIFwiLi9yZWFkX2ludC50c1wiO1xuXG5jb25zdCBNQVhfU0FGRV9JTlRFR0VSID0gQmlnSW50KE51bWJlci5NQVhfU0FGRV9JTlRFR0VSKTtcblxuLyoqXG4gKiBSZWFkIGJpZyBlbmRpYW4gNjRiaXQgbG9uZyBmcm9tIEJ1ZlJlYWRlclxuICogQHBhcmFtIGJ1ZlxuICpcbiAqIEBkZXByZWNhdGVkICh3aWxsIGJlIHJlbW92ZWQgYWZ0ZXIgMS4wLjApIFVzZSBXZWIgU3RyZWFtcyBpbnN0ZWFkLlxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVhZExvbmcoYnVmOiBCdWZSZWFkZXIpOiBQcm9taXNlPG51bWJlciB8IG51bGw+IHtcbiAgY29uc3QgaGlnaCA9IGF3YWl0IHJlYWRJbnQoYnVmKTtcbiAgaWYgKGhpZ2ggPT09IG51bGwpIHJldHVybiBudWxsO1xuICBjb25zdCBsb3cgPSBhd2FpdCByZWFkSW50KGJ1Zik7XG4gIGlmIChsb3cgPT09IG51bGwpIHRocm93IG5ldyBEZW5vLmVycm9ycy5VbmV4cGVjdGVkRW9mKCk7XG4gIGNvbnN0IGJpZyA9IChCaWdJbnQoaGlnaCkgPDwgMzJuKSB8IEJpZ0ludChsb3cpO1xuICAvLyBXZSBwcm9iYWJseSBzaG91bGQgcHJvdmlkZSBhIHNpbWlsYXIgQVBJIHRoYXQgcmV0dXJucyBCaWdJbnQgdmFsdWVzLlxuICBpZiAoYmlnID4gTUFYX1NBRkVfSU5URUdFUikge1xuICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFxuICAgICAgXCJMb25nIHZhbHVlIHRvbyBiaWcgdG8gYmUgcmVwcmVzZW50ZWQgYXMgYSBKYXZhU2NyaXB0IG51bWJlci5cIixcbiAgICApO1xuICB9XG4gIHJldHVybiBOdW1iZXIoYmlnKTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFHMUUsU0FBUyxPQUFPLFFBQVEsZ0JBQWdCO0FBRXhDLE1BQU0sbUJBQW1CLE9BQU8sT0FBTyxnQkFBZ0I7QUFFdkQ7Ozs7O0NBS0MsR0FDRCxPQUFPLGVBQWUsU0FBUyxHQUFjO0VBQzNDLE1BQU0sT0FBTyxNQUFNLFFBQVE7RUFDM0IsSUFBSSxTQUFTLE1BQU0sT0FBTztFQUMxQixNQUFNLE1BQU0sTUFBTSxRQUFRO0VBQzFCLElBQUksUUFBUSxNQUFNLE1BQU0sSUFBSSxLQUFLLE1BQU0sQ0FBQyxhQUFhO0VBQ3JELE1BQU0sTUFBTSxBQUFDLE9BQU8sU0FBUyxHQUFHLEdBQUksT0FBTztFQUMzQyx1RUFBdUU7RUFDdkUsSUFBSSxNQUFNLGtCQUFrQjtJQUMxQixNQUFNLElBQUksV0FDUjtFQUVKO0VBQ0EsT0FBTyxPQUFPO0FBQ2hCIn0=