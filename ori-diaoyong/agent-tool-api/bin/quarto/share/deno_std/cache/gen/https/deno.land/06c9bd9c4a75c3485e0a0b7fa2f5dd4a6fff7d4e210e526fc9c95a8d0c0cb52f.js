// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { assertPath } from "./assert_path.ts";
export function assertArgs(from, to) {
  assertPath(from);
  assertPath(to);
  if (from === to) return "";
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3BhdGgvX2NvbW1vbi9yZWxhdGl2ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5pbXBvcnQgeyBhc3NlcnRQYXRoIH0gZnJvbSBcIi4vYXNzZXJ0X3BhdGgudHNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGFzc2VydEFyZ3MoZnJvbTogc3RyaW5nLCB0bzogc3RyaW5nKSB7XG4gIGFzc2VydFBhdGgoZnJvbSk7XG4gIGFzc2VydFBhdGgodG8pO1xuICBpZiAoZnJvbSA9PT0gdG8pIHJldHVybiBcIlwiO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckMsU0FBUyxVQUFVLFFBQVEsbUJBQW1CO0FBRTlDLE9BQU8sU0FBUyxXQUFXLElBQVksRUFBRSxFQUFVO0VBQ2pELFdBQVc7RUFDWCxXQUFXO0VBQ1gsSUFBSSxTQUFTLElBQUksT0FBTztBQUMxQiJ9