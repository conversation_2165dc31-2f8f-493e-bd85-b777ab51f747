// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { globToRegExp, isGlob, joinGlobs } from "../path/glob.ts";
import { isAbsolute } from "../path/is_absolute.ts";
import { resolve } from "../path/resolve.ts";
import { SEP_PATTERN } from "../path/separator.ts";
import { walk, walkSync } from "./walk.ts";
import { assert } from "../assert/assert.ts";
import { createWalkEntry, createWalkEntrySync, toPathString } from "./_util.ts";
const isWindows = Deno.build.os === "windows";
function split(path) {
  const s = SEP_PATTERN.source;
  const segments = path.replace(new RegExp(`^${s}|${s}$`, "g"), "").split(SEP_PATTERN);
  const isAbsolute_ = isAbsolute(path);
  return {
    segments,
    isAbsolute: isAbsolute_,
    hasTrailingSep: !!path.match(new RegExp(`${s}$`)),
    winRoot: isWindows && isAbsolute_ ? segments.shift() : undefined
  };
}
function throwUnlessNotFound(error) {
  if (!(error instanceof Deno.errors.NotFound)) {
    throw error;
  }
}
function comparePath(a, b) {
  if (a.path < b.path) return -1;
  if (a.path > b.path) return 1;
  return 0;
}
/**
 * Expand the glob string from the specified `root` directory and yield each
 * result as a `WalkEntry` object.
 *
 * See [`globToRegExp()`](../path/glob.ts#globToRegExp) for details on supported
 * syntax.
 *
 * @example
 * ```ts
 * import { expandGlob } from "https://deno.land/std@$STD_VERSION/fs/expand_glob.ts";
 * for await (const file of expandGlob("**\/*.ts")) {
 *   console.log(file);
 * }
 * ```
 */ export async function* expandGlob(glob, { root = Deno.cwd(), exclude = [], includeDirs = true, extended = true, globstar = true, caseInsensitive, followSymlinks } = {}) {
  const globOptions = {
    extended,
    globstar,
    caseInsensitive
  };
  const absRoot = resolve(root);
  const resolveFromRoot = (path)=>resolve(absRoot, path);
  const excludePatterns = exclude.map(resolveFromRoot).map((s)=>globToRegExp(s, globOptions));
  const shouldInclude = (path)=>!excludePatterns.some((p)=>!!path.match(p));
  const { segments, isAbsolute: isGlobAbsolute, hasTrailingSep, winRoot } = split(toPathString(glob));
  let fixedRoot = isGlobAbsolute ? winRoot !== undefined ? winRoot : "/" : absRoot;
  while(segments.length > 0 && !isGlob(segments[0])){
    const seg = segments.shift();
    assert(seg !== undefined);
    fixedRoot = joinGlobs([
      fixedRoot,
      seg
    ], globOptions);
  }
  let fixedRootInfo;
  try {
    fixedRootInfo = await createWalkEntry(fixedRoot);
  } catch (error) {
    return throwUnlessNotFound(error);
  }
  async function* advanceMatch(walkInfo, globSegment) {
    if (!walkInfo.isDirectory) {
      return;
    } else if (globSegment === "..") {
      const parentPath = joinGlobs([
        walkInfo.path,
        ".."
      ], globOptions);
      try {
        if (shouldInclude(parentPath)) {
          return yield await createWalkEntry(parentPath);
        }
      } catch (error) {
        throwUnlessNotFound(error);
      }
      return;
    } else if (globSegment === "**") {
      return yield* walk(walkInfo.path, {
        skip: excludePatterns,
        maxDepth: globstar ? Infinity : 1,
        followSymlinks
      });
    }
    const globPattern = globToRegExp(globSegment, globOptions);
    for await (const walkEntry of walk(walkInfo.path, {
      maxDepth: 1,
      skip: excludePatterns,
      followSymlinks
    })){
      if (walkEntry.path !== walkInfo.path && walkEntry.name.match(globPattern)) {
        yield walkEntry;
      }
    }
  }
  let currentMatches = [
    fixedRootInfo
  ];
  for (const segment of segments){
    // Advancing the list of current matches may introduce duplicates, so we
    // pass everything through this Map.
    const nextMatchMap = new Map();
    await Promise.all(currentMatches.map(async (currentMatch)=>{
      for await (const nextMatch of advanceMatch(currentMatch, segment)){
        nextMatchMap.set(nextMatch.path, nextMatch);
      }
    }));
    currentMatches = [
      ...nextMatchMap.values()
    ].sort(comparePath);
  }
  if (hasTrailingSep) {
    currentMatches = currentMatches.filter((entry)=>entry.isDirectory);
  }
  if (!includeDirs) {
    currentMatches = currentMatches.filter((entry)=>!entry.isDirectory);
  }
  yield* currentMatches;
}
/**
 * Synchronous version of `expandGlob()`.
 *
 * @example
 * ```ts
 * import { expandGlobSync } from "https://deno.land/std@$STD_VERSION/fs/expand_glob.ts";
 * for (const file of expandGlobSync("**\/*.ts")) {
 *   console.log(file);
 * }
 * ```
 */ export function* expandGlobSync(glob, { root = Deno.cwd(), exclude = [], includeDirs = true, extended = true, globstar = true, caseInsensitive, followSymlinks } = {}) {
  const globOptions = {
    extended,
    globstar,
    caseInsensitive
  };
  const absRoot = resolve(root);
  const resolveFromRoot = (path)=>resolve(absRoot, path);
  const excludePatterns = exclude.map(resolveFromRoot).map((s)=>globToRegExp(s, globOptions));
  const shouldInclude = (path)=>!excludePatterns.some((p)=>!!path.match(p));
  const { segments, isAbsolute: isGlobAbsolute, hasTrailingSep, winRoot } = split(toPathString(glob));
  let fixedRoot = isGlobAbsolute ? winRoot !== undefined ? winRoot : "/" : absRoot;
  while(segments.length > 0 && !isGlob(segments[0])){
    const seg = segments.shift();
    assert(seg !== undefined);
    fixedRoot = joinGlobs([
      fixedRoot,
      seg
    ], globOptions);
  }
  let fixedRootInfo;
  try {
    fixedRootInfo = createWalkEntrySync(fixedRoot);
  } catch (error) {
    return throwUnlessNotFound(error);
  }
  function* advanceMatch(walkInfo, globSegment) {
    if (!walkInfo.isDirectory) {
      return;
    } else if (globSegment === "..") {
      const parentPath = joinGlobs([
        walkInfo.path,
        ".."
      ], globOptions);
      try {
        if (shouldInclude(parentPath)) {
          return yield createWalkEntrySync(parentPath);
        }
      } catch (error) {
        throwUnlessNotFound(error);
      }
      return;
    } else if (globSegment === "**") {
      return yield* walkSync(walkInfo.path, {
        skip: excludePatterns,
        maxDepth: globstar ? Infinity : 1,
        followSymlinks
      });
    }
    const globPattern = globToRegExp(globSegment, globOptions);
    for (const walkEntry of walkSync(walkInfo.path, {
      maxDepth: 1,
      skip: excludePatterns,
      followSymlinks
    })){
      if (walkEntry.path !== walkInfo.path && walkEntry.name.match(globPattern)) {
        yield walkEntry;
      }
    }
  }
  let currentMatches = [
    fixedRootInfo
  ];
  for (const segment of segments){
    // Advancing the list of current matches may introduce duplicates, so we
    // pass everything through this Map.
    const nextMatchMap = new Map();
    for (const currentMatch of currentMatches){
      for (const nextMatch of advanceMatch(currentMatch, segment)){
        nextMatchMap.set(nextMatch.path, nextMatch);
      }
    }
    currentMatches = [
      ...nextMatchMap.values()
    ].sort(comparePath);
  }
  if (hasTrailingSep) {
    currentMatches = currentMatches.filter((entry)=>entry.isDirectory);
  }
  if (!includeDirs) {
    currentMatches = currentMatches.filter((entry)=>!entry.isDirectory);
  }
  yield* currentMatches;
}
//# sourceMappingURL=data:application/json;base64,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