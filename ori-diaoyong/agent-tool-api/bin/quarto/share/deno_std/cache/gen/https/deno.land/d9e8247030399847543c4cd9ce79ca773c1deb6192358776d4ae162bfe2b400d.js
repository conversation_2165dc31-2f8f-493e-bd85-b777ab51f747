// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { gte } from "./gte.ts";
import { lte } from "./lte.ts";
/**
 * Test to see if the version satisfies the range.
 * @param version The version to test
 * @param range The range to check
 * @returns true if the version is in the range
 */ export function testRange(version, range) {
  for (const r of range.ranges){
    if (r.every((c)=>gte(version, c.min) && lte(version, c.max))) {
      return true;
    }
  }
  return false;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci90ZXN0X3JhbmdlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG5pbXBvcnQgdHlwZSB7IFNlbVZlciwgU2VtVmVyUmFuZ2UgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgZ3RlIH0gZnJvbSBcIi4vZ3RlLnRzXCI7XG5pbXBvcnQgeyBsdGUgfSBmcm9tIFwiLi9sdGUudHNcIjtcblxuLyoqXG4gKiBUZXN0IHRvIHNlZSBpZiB0aGUgdmVyc2lvbiBzYXRpc2ZpZXMgdGhlIHJhbmdlLlxuICogQHBhcmFtIHZlcnNpb24gVGhlIHZlcnNpb24gdG8gdGVzdFxuICogQHBhcmFtIHJhbmdlIFRoZSByYW5nZSB0byBjaGVja1xuICogQHJldHVybnMgdHJ1ZSBpZiB0aGUgdmVyc2lvbiBpcyBpbiB0aGUgcmFuZ2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRlc3RSYW5nZSh2ZXJzaW9uOiBTZW1WZXIsIHJhbmdlOiBTZW1WZXJSYW5nZSk6IGJvb2xlYW4ge1xuICBmb3IgKGNvbnN0IHIgb2YgcmFuZ2UucmFuZ2VzKSB7XG4gICAgaWYgKHIuZXZlcnkoKGMpID0+IGd0ZSh2ZXJzaW9uLCBjLm1pbikgJiYgbHRlKHZlcnNpb24sIGMubWF4KSkpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBRTFFLFNBQVMsR0FBRyxRQUFRLFdBQVc7QUFDL0IsU0FBUyxHQUFHLFFBQVEsV0FBVztBQUUvQjs7Ozs7Q0FLQyxHQUNELE9BQU8sU0FBUyxVQUFVLE9BQWUsRUFBRSxLQUFrQjtFQUMzRCxLQUFLLE1BQU0sS0FBSyxNQUFNLE1BQU0sQ0FBRTtJQUM1QixJQUFJLEVBQUUsS0FBSyxDQUFDLENBQUMsSUFBTSxJQUFJLFNBQVMsRUFBRSxHQUFHLEtBQUssSUFBSSxTQUFTLEVBQUUsR0FBRyxJQUFJO01BQzlELE9BQU87SUFDVDtFQUNGO0VBQ0EsT0FBTztBQUNUIn0=