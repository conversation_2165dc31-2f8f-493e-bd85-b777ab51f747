// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { isSamePath, isSubdir } from "./_util.ts";
const EXISTS_ERROR = new Deno.errors.AlreadyExists("dest already exists.");
export class SubdirectoryMoveError extends Error {
  constructor(src, dest){
    super(`Cannot move '${src}' to a subdirectory of itself, '${dest}'.`);
  }
}
/**
 * Moves a file or directory.
 *
 * @example
 * ```ts
 * import { move } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * move("./foo", "./bar"); // returns a promise
 * ```
 */ export async function move(src, dest, { overwrite = false } = {}) {
  const srcStat = await Deno.stat(src);
  if (srcStat.isDirectory && (isSubdir(src, dest) || isSamePath(src, dest))) {
    throw new SubdirectoryMoveError(src, dest);
  }
  if (overwrite) {
    if (isSamePath(src, dest)) return;
    try {
      await Deno.remove(dest, {
        recursive: true
      });
    } catch (error) {
      if (!(error instanceof Deno.errors.NotFound)) {
        throw error;
      }
    }
  } else {
    try {
      await Deno.lstat(dest);
      return Promise.reject(EXISTS_ERROR);
    } catch  {
    // Do nothing...
    }
  }
  await Deno.rename(src, dest);
}
/**
 * Moves a file or directory synchronously.
 * @example
 * ```ts
 * import { moveSync } from "https://deno.land/std@$STD_VERSION/fs/mod.ts";
 *
 * moveSync("./foo", "./bar"); // void
 * ```
 */ export function moveSync(src, dest, { overwrite = false } = {}) {
  const srcStat = Deno.statSync(src);
  if (srcStat.isDirectory && (isSubdir(src, dest) || isSamePath(src, dest))) {
    throw new SubdirectoryMoveError(src, dest);
  }
  if (overwrite) {
    if (isSamePath(src, dest)) return;
    try {
      Deno.removeSync(dest, {
        recursive: true
      });
    } catch (error) {
      if (!(error instanceof Deno.errors.NotFound)) {
        throw error;
      }
    }
  } else {
    try {
      Deno.lstatSync(dest);
      throw EXISTS_ERROR;
    } catch (error) {
      if (error === EXISTS_ERROR) {
        throw error;
      }
    }
  }
  Deno.renameSync(src, dest);
}
//# sourceMappingURL=data:application/json;base64,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