// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { isSemVer } from "./is_semver.ts";
import { isValidOperator } from "./_shared.ts";
import { ALL, NONE } from "./constants.ts";
/**
 * Does a deep check on the value to see if it is a valid SemVerComparator object.
 *
 * Objects with extra fields are still considered valid if they have at
 * least the correct fields.
 *
 * Adds a type assertion if true.
 * @param value The value to check if its a SemVerComparator
 * @returns True if the object is a SemVerComparator otherwise false
 */ export function isSemVerComparator(value) {
  if (value === null || value === undefined) return false;
  if (value === NONE) return true;
  if (value === ALL) return true;
  if (Array.isArray(value)) return false;
  if (typeof value !== "object") return false;
  const { operator, semver, min, max } = value;
  return isValidOperator(operator) && isSemVer(semver) && isSemVer(min) && isSemVer(max);
}
//# sourceMappingURL=data:application/json;base64,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