// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns the first element that is the largest value of the given function or
 * undefined if there are no elements.
 *
 * @example
 * ```ts
 * import { maxBy } from "https://deno.land/std@$STD_VERSION/collections/max_by.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = [
 *   { name: "<PERSON>", age: 34 },
 *   { name: "<PERSON>", age: 42 },
 *   { name: "<PERSON>", age: 23 },
 * ];
 *
 * const personWithMaxAge = maxBy(people, (i) => i.age);
 *
 * assertEquals(personWithMaxAge, { name: "<PERSON>", age: 42 });
 * ```
 */ export function maxBy(array, selector) {
  let max = undefined;
  let maxValue = undefined;
  for (const current of array){
    const currentValue = selector(current);
    if (maxValue === undefined || currentValue > maxValue) {
      max = current;
      maxValue = currentValue;
    }
  }
  return max;
}
//# sourceMappingURL=data:application/json;base64,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