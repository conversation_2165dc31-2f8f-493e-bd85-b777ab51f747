// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns the first element having the largest value according to the provided
 * comparator or undefined if there are no elements.
 *
 * The comparator is expected to work exactly like one passed to `Array.sort`,
 * which means that `comparator(a, b)` should return a negative number if `a < b`,
 * a positive number if `a > b` and `0` if `a === b`.
 *
 * @example
 * ```ts
 * import { maxWith } from "https://deno.land/std@$STD_VERSION/collections/max_with.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
 * const largestName = maxWith(people, (a, b) => a.length - b.length);
 *
 * assertEquals(largestName, "Arthur");
 * ```
 */ export function maxWith(array, comparator) {
  let max = undefined;
  let isFirst = true;
  for (const current of array){
    if (isFirst || comparator(current, max) > 0) {
      max = current;
      isFirst = false;
    }
  }
  return max;
}
//# sourceMappingURL=data:application/json;base64,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