// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
// Bare keys may only contain ASCII letters,
// ASCII digits, underscores, and dashes (A-Za-z0-9_-).
function joinKeys(keys) {
  // Dotted keys are a sequence of bare or quoted keys joined with a dot.
  // This allows for grouping similar properties together:
  return keys.map((str)=>{
    return str.length === 0 || str.match(/[^A-Za-z0-9_-]/) ? JSON.stringify(str) : str;
  }).join(".");
}
var ArrayType;
(function(ArrayType) {
  ArrayType[ArrayType["ONLY_PRIMITIVE"] = 0] = "ONLY_PRIMITIVE";
  ArrayType[ArrayType["ONLY_OBJECT_EXCLUDING_ARRAY"] = 1] = "ONLY_OBJECT_EXCLUDING_ARRAY";
  ArrayType[ArrayType["MIXED"] = 2] = "MIXED";
})(ArrayType || (ArrayType = {}));
class Dumper {
  maxPad = 0;
  srcObject;
  output = [];
  #arrayTypeCache = new Map();
  constructor(srcObjc){
    this.srcObject = srcObjc;
  }
  dump(fmtOptions = {}) {
    // deno-lint-ignore no-explicit-any
    this.output = this.#printObject(this.srcObject);
    this.output = this.#format(fmtOptions);
    return this.output;
  }
  #printObject(obj, keys = []) {
    const out = [];
    const props = Object.keys(obj);
    const inlineProps = [];
    const multilineProps = [];
    for (const prop of props){
      if (this.#isSimplySerializable(obj[prop])) {
        inlineProps.push(prop);
      } else {
        multilineProps.push(prop);
      }
    }
    const sortedProps = inlineProps.concat(multilineProps);
    for(let i = 0; i < sortedProps.length; i++){
      const prop = sortedProps[i];
      const value = obj[prop];
      if (value instanceof Date) {
        out.push(this.#dateDeclaration([
          prop
        ], value));
      } else if (typeof value === "string" || value instanceof RegExp) {
        out.push(this.#strDeclaration([
          prop
        ], value.toString()));
      } else if (typeof value === "number") {
        out.push(this.#numberDeclaration([
          prop
        ], value));
      } else if (typeof value === "boolean") {
        out.push(this.#boolDeclaration([
          prop
        ], value));
      } else if (value instanceof Array) {
        const arrayType = this.#getTypeOfArray(value);
        if (arrayType === ArrayType.ONLY_PRIMITIVE) {
          out.push(this.#arrayDeclaration([
            prop
          ], value));
        } else if (arrayType === ArrayType.ONLY_OBJECT_EXCLUDING_ARRAY) {
          // array of objects
          for(let i = 0; i < value.length; i++){
            out.push("");
            out.push(this.#headerGroup([
              ...keys,
              prop
            ]));
            out.push(...this.#printObject(value[i], [
              ...keys,
              prop
            ]));
          }
        } else {
          // this is a complex array, use the inline format.
          const str = value.map((x)=>this.#printAsInlineValue(x)).join(",");
          out.push(`${this.#declaration([
            prop
          ])}[${str}]`);
        }
      } else if (typeof value === "object") {
        out.push("");
        out.push(this.#header([
          ...keys,
          prop
        ]));
        if (value) {
          const toParse = value;
          out.push(...this.#printObject(toParse, [
            ...keys,
            prop
          ]));
        }
      // out.push(...this._parse(value, `${path}${prop}.`));
      }
    }
    out.push("");
    return out;
  }
  #isPrimitive(value) {
    return value instanceof Date || value instanceof RegExp || [
      "string",
      "number",
      "boolean"
    ].includes(typeof value);
  }
  #getTypeOfArray(arr) {
    if (this.#arrayTypeCache.has(arr)) {
      return this.#arrayTypeCache.get(arr);
    }
    const type = this.#doGetTypeOfArray(arr);
    this.#arrayTypeCache.set(arr, type);
    return type;
  }
  #doGetTypeOfArray(arr) {
    if (!arr.length) {
      // any type should be fine
      return ArrayType.ONLY_PRIMITIVE;
    }
    const onlyPrimitive = this.#isPrimitive(arr[0]);
    if (arr[0] instanceof Array) {
      return ArrayType.MIXED;
    }
    for(let i = 1; i < arr.length; i++){
      if (onlyPrimitive !== this.#isPrimitive(arr[i]) || arr[i] instanceof Array) {
        return ArrayType.MIXED;
      }
    }
    return onlyPrimitive ? ArrayType.ONLY_PRIMITIVE : ArrayType.ONLY_OBJECT_EXCLUDING_ARRAY;
  }
  #printAsInlineValue(value) {
    if (value instanceof Date) {
      return `"${this.#printDate(value)}"`;
    } else if (typeof value === "string" || value instanceof RegExp) {
      return JSON.stringify(value.toString());
    } else if (typeof value === "number") {
      return value;
    } else if (typeof value === "boolean") {
      return value.toString();
    } else if (value instanceof Array) {
      const str = value.map((x)=>this.#printAsInlineValue(x)).join(",");
      return `[${str}]`;
    } else if (typeof value === "object") {
      if (!value) {
        throw new Error("should never reach");
      }
      const str = Object.keys(value).map((key)=>{
        return `${joinKeys([
          key
        ])} = ${// deno-lint-ignore no-explicit-any
        this.#printAsInlineValue(value[key])}`;
      }).join(",");
      return `{${str}}`;
    }
    throw new Error("should never reach");
  }
  #isSimplySerializable(value) {
    return typeof value === "string" || typeof value === "number" || typeof value === "boolean" || value instanceof RegExp || value instanceof Date || value instanceof Array && this.#getTypeOfArray(value) !== ArrayType.ONLY_OBJECT_EXCLUDING_ARRAY;
  }
  #header(keys) {
    return `[${joinKeys(keys)}]`;
  }
  #headerGroup(keys) {
    return `[[${joinKeys(keys)}]]`;
  }
  #declaration(keys) {
    const title = joinKeys(keys);
    if (title.length > this.maxPad) {
      this.maxPad = title.length;
    }
    return `${title} = `;
  }
  #arrayDeclaration(keys, value) {
    return `${this.#declaration(keys)}${JSON.stringify(value)}`;
  }
  #strDeclaration(keys, value) {
    return `${this.#declaration(keys)}${JSON.stringify(value)}`;
  }
  #numberDeclaration(keys, value) {
    switch(value){
      case Infinity:
        return `${this.#declaration(keys)}inf`;
      case -Infinity:
        return `${this.#declaration(keys)}-inf`;
      default:
        return `${this.#declaration(keys)}${value}`;
    }
  }
  #boolDeclaration(keys, value) {
    return `${this.#declaration(keys)}${value}`;
  }
  #printDate(value) {
    function dtPad(v, lPad = 2) {
      return v.padStart(lPad, "0");
    }
    const m = dtPad((value.getUTCMonth() + 1).toString());
    const d = dtPad(value.getUTCDate().toString());
    const h = dtPad(value.getUTCHours().toString());
    const min = dtPad(value.getUTCMinutes().toString());
    const s = dtPad(value.getUTCSeconds().toString());
    const ms = dtPad(value.getUTCMilliseconds().toString(), 3);
    // formatted date
    const fData = `${value.getUTCFullYear()}-${m}-${d}T${h}:${min}:${s}.${ms}`;
    return fData;
  }
  #dateDeclaration(keys, value) {
    return `${this.#declaration(keys)}${this.#printDate(value)}`;
  }
  #format(options = {}) {
    const { keyAlignment = false } = options;
    const rDeclaration = /^(\".*\"|[^=]*)\s=/;
    const out = [];
    for(let i = 0; i < this.output.length; i++){
      const l = this.output[i];
      // we keep empty entry for array of objects
      if (l[0] === "[" && l[1] !== "[") {
        // non-empty object with only subobjects as properties
        if (this.output[i + 1] === "" && this.output[i + 2]?.slice(0, l.length) === l.slice(0, -1) + ".") {
          i += 1;
          continue;
        }
        out.push(l);
      } else {
        if (keyAlignment) {
          const m = rDeclaration.exec(l);
          if (m) {
            out.push(l.replace(m[1], m[1].padEnd(this.maxPad)));
          } else {
            out.push(l);
          }
        } else {
          out.push(l);
        }
      }
    }
    // Cleaning multiple spaces
    const cleanedOutput = [];
    for(let i = 0; i < out.length; i++){
      const l = out[i];
      if (!(l === "" && out[i + 1] === "")) {
        cleanedOutput.push(l);
      }
    }
    return cleanedOutput;
  }
}
/**
 * Stringify dumps source object into TOML string and returns it.
 * @param srcObj
 * @param [fmtOptions] format options
 * @param [fmtOptions.keyAlignment] whether to algin key
 */ export function stringify(srcObj, fmtOptions) {
  return new Dumper(srcObj).dump(fmtOptions).join("\n");
}
//# sourceMappingURL=data:application/json;base64,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