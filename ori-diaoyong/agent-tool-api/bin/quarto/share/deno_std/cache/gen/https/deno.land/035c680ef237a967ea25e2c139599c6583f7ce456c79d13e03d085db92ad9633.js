// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns a new record with all entries of the given record except the ones
 * that have a value that does not match the given predicate.
 *
 * @example
 * ```ts
 * import { filterValues } from "https://deno.land/std@$STD_VERSION/collections/filter_values.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = {
 *   "<PERSON>": 37,
 *   "<PERSON>": 7,
 *   "<PERSON>": 23,
 * };
 * const adults = filterValues(people, (it) => it >= 18);
 *
 * assertEquals(
 *   adults,
 *   {
 *     "<PERSON>": 37,
 *     "<PERSON>": 23,
 *   },
 * );
 * ```
 */ export function filterValues(record, predicate) {
  const ret = {};
  const entries = Object.entries(record);
  for (const [key, value] of entries){
    if (predicate(value)) {
      ret[key] = value;
    }
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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