// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { randomInteger } from "./_utils.ts";
/**
 * Returns a random element from the given array
 *
 * @example
 * ```ts
 * import { sample } from "https://deno.land/std@$STD_VERSION/collections/sample.ts";
 * import { assert } from "https://deno.land/std@$STD_VERSION/assert/assert.ts";
 *
 * const numbers = [1, 2, 3, 4];
 * const random = sample(numbers);
 *
 * assert(numbers.includes(random as number));
 * ```
 */ export function sample(array) {
  const length = array.length;
  return length ? array[randomInteger(0, length - 1)] : undefined;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3NhbXBsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuLy8gVGhpcyBtb2R1bGUgaXMgYnJvd3NlciBjb21wYXRpYmxlLlxuXG5pbXBvcnQgeyByYW5kb21JbnRlZ2VyIH0gZnJvbSBcIi4vX3V0aWxzLnRzXCI7XG5cbi8qKlxuICogUmV0dXJucyBhIHJhbmRvbSBlbGVtZW50IGZyb20gdGhlIGdpdmVuIGFycmF5XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyBzYW1wbGUgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9jb2xsZWN0aW9ucy9zYW1wbGUudHNcIjtcbiAqIGltcG9ydCB7IGFzc2VydCB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2Fzc2VydC9hc3NlcnQudHNcIjtcbiAqXG4gKiBjb25zdCBudW1iZXJzID0gWzEsIDIsIDMsIDRdO1xuICogY29uc3QgcmFuZG9tID0gc2FtcGxlKG51bWJlcnMpO1xuICpcbiAqIGFzc2VydChudW1iZXJzLmluY2x1ZGVzKHJhbmRvbSBhcyBudW1iZXIpKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gc2FtcGxlPFQ+KGFycmF5OiByZWFkb25seSBUW10pOiBUIHwgdW5kZWZpbmVkIHtcbiAgY29uc3QgbGVuZ3RoID0gYXJyYXkubGVuZ3RoO1xuICByZXR1cm4gbGVuZ3RoID8gYXJyYXlbcmFuZG9tSW50ZWdlcigwLCBsZW5ndGggLSAxKV0gOiB1bmRlZmluZWQ7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQyxTQUFTLGFBQWEsUUFBUSxjQUFjO0FBRTVDOzs7Ozs7Ozs7Ozs7O0NBYUMsR0FDRCxPQUFPLFNBQVMsT0FBVSxLQUFtQjtFQUMzQyxNQUFNLFNBQVMsTUFBTSxNQUFNO0VBQzNCLE9BQU8sU0FBUyxLQUFLLENBQUMsY0FBYyxHQUFHLFNBQVMsR0FBRyxHQUFHO0FBQ3hEIn0=