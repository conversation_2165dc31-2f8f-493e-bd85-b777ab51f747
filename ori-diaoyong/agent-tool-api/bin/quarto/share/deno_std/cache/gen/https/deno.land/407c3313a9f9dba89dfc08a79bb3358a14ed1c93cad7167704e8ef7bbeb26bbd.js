// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { PartialReadError } from "../io/buf_reader.ts";
export var FileTypes;
(function(FileTypes) {
  FileTypes[FileTypes["file"] = 0] = "file";
  FileTypes[FileTypes["link"] = 1] = "link";
  FileTypes[FileTypes["symlink"] = 2] = "symlink";
  FileTypes[FileTypes["character-device"] = 3] = "character-device";
  FileTypes[FileTypes["block-device"] = 4] = "block-device";
  FileTypes[FileTypes["directory"] = 5] = "directory";
  FileTypes[FileTypes["fifo"] = 6] = "fifo";
  FileTypes[FileTypes["contiguous-file"] = 7] = "contiguous-file";
})(FileTypes || (FileTypes = {}));
export const HEADER_LENGTH = 512;
/*
struct posix_header {           // byte offset
  char name[100];               //   0
  char mode[8];                 // 100
  char uid[8];                  // 108
  char gid[8];                  // 116
  char size[12];                // 124
  char mtime[12];               // 136
  char chksum[8];               // 148
  char typeflag;                // 156
  char linkname[100];           // 157
  char magic[6];                // 257
  char version[2];              // 263
  char uname[32];               // 265
  char gname[32];               // 297
  char devmajor[8];             // 329
  char devminor[8];             // 337
  char prefix[155];             // 345
                                // 500
};
*/ export const ustarStructure = [
  {
    field: "fileName",
    length: 100
  },
  {
    field: "fileMode",
    length: 8
  },
  {
    field: "uid",
    length: 8
  },
  {
    field: "gid",
    length: 8
  },
  {
    field: "fileSize",
    length: 12
  },
  {
    field: "mtime",
    length: 12
  },
  {
    field: "checksum",
    length: 8
  },
  {
    field: "type",
    length: 1
  },
  {
    field: "linkName",
    length: 100
  },
  {
    field: "ustar",
    length: 8
  },
  {
    field: "owner",
    length: 32
  },
  {
    field: "group",
    length: 32
  },
  {
    field: "majorNumber",
    length: 8
  },
  {
    field: "minorNumber",
    length: 8
  },
  {
    field: "fileNamePrefix",
    length: 155
  },
  {
    field: "padding",
    length: 12
  }
];
export async function readBlock(reader, p) {
  let bytesRead = 0;
  while(bytesRead < p.length){
    const rr = await reader.read(p.subarray(bytesRead));
    if (rr === null) {
      if (bytesRead === 0) {
        return null;
      } else {
        throw new PartialReadError();
      }
    }
    bytesRead += rr;
  }
  return bytesRead;
}
//# sourceMappingURL=data:application/json;base64,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