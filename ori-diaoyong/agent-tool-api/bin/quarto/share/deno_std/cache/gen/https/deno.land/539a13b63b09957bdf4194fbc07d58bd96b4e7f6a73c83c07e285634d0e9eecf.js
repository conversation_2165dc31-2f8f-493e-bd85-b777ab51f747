// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Generators and validators for UUIDs for versions v1, v3, v4 and v5.
 *
 * Consider using the web platform
 * [`crypto.randomUUID`](https://developer.mozilla.org/en-US/docs/Web/API/Crypto/randomUUID)
 * for v4 UUIDs instead.
 *
 * Based on https://github.com/kelektiv/node-uuid -> https://www.ietf.org/rfc/rfc4122.txt
 *
 * Support for RFC4122 version 1, 3, 4, and 5 UUIDs
 *
 * This module is browser compatible.
 *
 * @module
 */ export * from "./constants.ts";
import * as v1 from "./v1.ts";
import * as v3 from "./v3.ts";
import * as v4 from "./v4.ts";
import * as v5 from "./v5.ts";
export const NIL_UUID = "00000000-0000-0000-0000-000000000000";
/**
 * Check if the passed UUID is the nil UUID.
 *
 * ```js
 * import { isNil } from "https://deno.land/std@$STD_VERSION/uuid/mod.ts";
 *
 * isNil("00000000-0000-0000-0000-000000000000") // true
 * isNil(crypto.randomUUID()) // false
 * ```
 */ export function isNil(id) {
  return id === NIL_UUID;
}
/**
 * Test a string to see if it is a valid UUID.
 *
 * ```js
 * import { validate } from "https://deno.land/std@$STD_VERSION/uuid/mod.ts"
 *
 * validate("not a UUID") // false
 * validate("6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b") // true
 * ```
 */ export function validate(uuid) {
  return /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i.test(uuid);
}
/**
 * Detect RFC version of a UUID.
 *
 * ```js
 * import { version } from "https://deno.land/std@$STD_VERSION/uuid/mod.ts"
 *
 * version("d9428888-122b-11e1-b85c-61cd3cbb3210") // 1
 * version("109156be-c4fb-41ea-b1b4-efe1671c5836") // 4
 * ```
 */ export function version(uuid) {
  if (!validate(uuid)) {
    throw new TypeError("Invalid UUID");
  }
  return parseInt(uuid[14], 16);
}
export { v1, v3, v4, v5 };
//# sourceMappingURL=data:application/json;base64,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