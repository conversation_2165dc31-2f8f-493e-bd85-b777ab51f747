// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Builds a new Record using the given array as keys and choosing a value for
 * each key using the given selector. If any of two pairs would have the same
 * value the latest on will be used (overriding the ones before it).
 *
 * @example
 * ```ts
 * import { associateWith } from "https://deno.land/std@$STD_VERSION/collections/associate_with.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const names = ["<PERSON>", "<PERSON>", "<PERSON>"];
 * const namesToLength = associateWith(names, (it) => it.length);
 *
 * assertEquals(namesToLength, {
 *   "Kim": 3,
 *   "<PERSON>": 4,
 *   "<PERSON>": 8,
 * });
 * ```
 */ export function associateWith(array, selector) {
  const ret = {};
  for (const element of array){
    const selectedValue = selector(element);
    ret[element] = selectedValue;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2Fzc29jaWF0ZV93aXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogQnVpbGRzIGEgbmV3IFJlY29yZCB1c2luZyB0aGUgZ2l2ZW4gYXJyYXkgYXMga2V5cyBhbmQgY2hvb3NpbmcgYSB2YWx1ZSBmb3JcbiAqIGVhY2gga2V5IHVzaW5nIHRoZSBnaXZlbiBzZWxlY3Rvci4gSWYgYW55IG9mIHR3byBwYWlycyB3b3VsZCBoYXZlIHRoZSBzYW1lXG4gKiB2YWx1ZSB0aGUgbGF0ZXN0IG9uIHdpbGwgYmUgdXNlZCAob3ZlcnJpZGluZyB0aGUgb25lcyBiZWZvcmUgaXQpLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgYXNzb2NpYXRlV2l0aCB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL2Fzc29jaWF0ZV93aXRoLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IG5hbWVzID0gW1wiS2ltXCIsIFwiTGFyYVwiLCBcIkpvbmF0aGFuXCJdO1xuICogY29uc3QgbmFtZXNUb0xlbmd0aCA9IGFzc29jaWF0ZVdpdGgobmFtZXMsIChpdCkgPT4gaXQubGVuZ3RoKTtcbiAqXG4gKiBhc3NlcnRFcXVhbHMobmFtZXNUb0xlbmd0aCwge1xuICogICBcIktpbVwiOiAzLFxuICogICBcIkxhcmFcIjogNCxcbiAqICAgXCJKb25hdGhhblwiOiA4LFxuICogfSk7XG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGFzc29jaWF0ZVdpdGg8VD4oXG4gIGFycmF5OiBJdGVyYWJsZTxzdHJpbmc+LFxuICBzZWxlY3RvcjogKGtleTogc3RyaW5nKSA9PiBULFxuKTogUmVjb3JkPHN0cmluZywgVD4ge1xuICBjb25zdCByZXQ6IFJlY29yZDxzdHJpbmcsIFQ+ID0ge307XG5cbiAgZm9yIChjb25zdCBlbGVtZW50IG9mIGFycmF5KSB7XG4gICAgY29uc3Qgc2VsZWN0ZWRWYWx1ZSA9IHNlbGVjdG9yKGVsZW1lbnQpO1xuXG4gICAgcmV0W2VsZW1lbnRdID0gc2VsZWN0ZWRWYWx1ZTtcbiAgfVxuXG4gIHJldHVybiByZXQ7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQW1CQyxHQUNELE9BQU8sU0FBUyxjQUNkLEtBQXVCLEVBQ3ZCLFFBQTRCO0VBRTVCLE1BQU0sTUFBeUIsQ0FBQztFQUVoQyxLQUFLLE1BQU0sV0FBVyxNQUFPO0lBQzNCLE1BQU0sZ0JBQWdCLFNBQVM7SUFFL0IsR0FBRyxDQUFDLFFBQVEsR0FBRztFQUNqQjtFQUVBLE9BQU87QUFDVCJ9