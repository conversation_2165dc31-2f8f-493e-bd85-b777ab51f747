// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { bytesToUuid, uuidToBytes } from "./_common.ts";
import { concat } from "../bytes/concat.ts";
import { assert } from "../assert/assert.ts";
const UUID_RE = /^[0-9a-f]{8}-[0-9a-f]{4}-[5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
/**
 * Validate that the passed UUID is an RFC4122 v5 UUID.
 *
 * @example
 * ```ts
 * import { generate as generateV5, validate } from "https://deno.land/std@$STD_VERSION/uuid/v5.ts";
 *
 * validate(await generateV5("6ba7b811-9dad-11d1-80b4-00c04fd430c8", new Uint8Array())); // true
 * validate(crypto.randomUUID()); // false
 * validate("this-is-not-a-uuid"); // false
 * ```
 */ export function validate(id) {
  return UUID_RE.test(id);
}
/**
 * Generate a RFC4122 v5 UUID (SHA-1 namespace).
 *
 * @example
 * ```js
 * import { generate } from "https://deno.land/std@$STD_VERSION/uuid/v5.ts";
 *
 * const NAMESPACE_URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
 *
 * const uuid = await generate(NAMESPACE_URL, new TextEncoder().encode("python.org"));
 * uuid === "7af94e2b-4dd9-50f0-9c9a-8a48519bdef0" // true
 * ```
 *
 * @param namespace The namespace to use, encoded as a UUID.
 * @param data The data to hash to calculate the SHA-1 digest for the UUID.
 */ export async function generate(namespace, data) {
  // TODO(lucacasonato): validate that `namespace` is a valid UUID.
  const space = uuidToBytes(namespace);
  assert(space.length === 16, "namespace must be a valid UUID");
  const toHash = concat(new Uint8Array(space), data);
  const buffer = await crypto.subtle.digest("sha-1", toHash);
  const bytes = new Uint8Array(buffer);
  bytes[6] = bytes[6] & 0x0f | 0x50;
  bytes[8] = bytes[8] & 0x3f | 0x80;
  return bytesToUuid(bytes);
}
//# sourceMappingURL=data:application/json;base64,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