// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { BytesList } from "../bytes/bytes_list.ts";
/** Generate longest proper prefix which is also suffix array. */ function createLPS(pat) {
  const lps = new Uint8Array(pat.length);
  lps[0] = 0;
  let prefixEnd = 0;
  let i = 1;
  while(i < lps.length){
    if (pat[i] === pat[prefixEnd]) {
      prefixEnd++;
      lps[i] = prefixEnd;
      i++;
    } else if (prefixEnd === 0) {
      lps[i] = 0;
      i++;
    } else {
      prefixEnd = lps[prefixEnd - 1];
    }
  }
  return lps;
}
/**
 * Read delimited bytes from a Reader.
 *
 * @deprecated (will be removed after 1.0.0) Use Web Streams instead.
 */ export async function* readDelim(reader, delim) {
  // Avoid unicode problems
  const delimLen = delim.length;
  const delimLPS = createLPS(delim);
  const chunks = new BytesList();
  const bufSize = Math.max(1024, delimLen + 1);
  // Modified KMP
  let inspectIndex = 0;
  let matchIndex = 0;
  while(true){
    const inspectArr = new Uint8Array(bufSize);
    const result = await reader.read(inspectArr);
    if (result === null) {
      // Yield last chunk.
      yield chunks.concat();
      return;
    } else if (result < 0) {
      // Discard all remaining and silently fail.
      return;
    }
    chunks.add(inspectArr, 0, result);
    let localIndex = 0;
    while(inspectIndex < chunks.size()){
      if (inspectArr[localIndex] === delim[matchIndex]) {
        inspectIndex++;
        localIndex++;
        matchIndex++;
        if (matchIndex === delimLen) {
          // Full match
          const matchEnd = inspectIndex - delimLen;
          const readyBytes = chunks.slice(0, matchEnd);
          yield readyBytes;
          // Reset match, different from KMP.
          chunks.shift(inspectIndex);
          inspectIndex = 0;
          matchIndex = 0;
        }
      } else {
        if (matchIndex === 0) {
          inspectIndex++;
          localIndex++;
        } else {
          matchIndex = delimLPS[matchIndex - 1];
        }
      }
    }
  }
}
//# sourceMappingURL=data:application/json;base64,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