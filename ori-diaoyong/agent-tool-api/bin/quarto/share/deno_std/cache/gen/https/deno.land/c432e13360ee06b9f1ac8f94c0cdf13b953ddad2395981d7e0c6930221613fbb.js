// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// Copyright 2020 <PERSON>. All rights reserved. MIT license.
/**
 * Functions for encoding typed integers in array buffers.
 *
 * @module
 */ // This implementation is a port of https://deno.land/x/varint@v2.0.0 by @keithamus
// This module is browser compatible.
export const MaxUInt64 = 18446744073709551615n;
export const MaxVarIntLen64 = 10;
export const MaxVarIntLen32 = 5;
const MSB = 0x80;
const REST = 0x7f;
const SHIFT = 7;
const MSBN = 0x80n;
const SHIFTN = 7n;
/**
 * Given a `buf`, starting at `offset` (default: 0), begin decoding bytes as
 * VarInt encoded bytes, for a maximum of 10 bytes (offset + 10). The returned
 * tuple is of the decoded varint 32-bit number, and the new offset with which
 * to continue decoding other data.
 *
 * If a `bigint` in return is undesired, the `decode32` function will return a
 * `number`, but this should only be used in cases where the varint is
 * _assured_ to be 32-bits. If in doubt, use `decode()`.
 *
 * To know how many bytes the VarInt took to encode, simply negate `offset`
 * from the returned new `offset`.
 */ export function decode(buf, offset = 0) {
  for(let i = offset, len = Math.min(buf.length, offset + MaxVarIntLen64), shift = 0, decoded = 0n; i < len; i += 1, shift += SHIFT){
    const byte = buf[i];
    decoded += BigInt((byte & REST) * Math.pow(2, shift));
    if (!(byte & MSB) && decoded > MaxUInt64) {
      throw new RangeError("overflow varint");
    }
    if (!(byte & MSB)) return [
      decoded,
      i + 1
    ];
  }
  throw new RangeError("malformed or overflow varint");
}
/**
 * Given a `buf`, starting at `offset` (default: 0), begin decoding bytes as
 * VarInt encoded bytes, for a maximum of 5 bytes (offset + 5). The returned
 * tuple is of the decoded varint 32-bit number, and the new offset with which
 * to continue decoding other data.
 *
 * VarInts are _not 32-bit by default_ so this should only be used in cases
 * where the varint is _assured_ to be 32-bits. If in doubt, use `decode()`.
 *
 * To know how many bytes the VarInt took to encode, simply negate `offset`
 * from the returned new `offset`.
 */ export function decode32(buf, offset = 0) {
  for(let i = offset, len = Math.min(buf.length, offset + MaxVarIntLen32), shift = 0, decoded = 0; i <= len; i += 1, shift += SHIFT){
    const byte = buf[i];
    decoded += (byte & REST) * Math.pow(2, shift);
    if (!(byte & MSB)) return [
      decoded,
      i + 1
    ];
  }
  throw new RangeError("malformed or overflow varint");
}
/**
 * Takes unsigned number `num` and converts it into a VarInt encoded
 * `Uint8Array`, returning a tuple consisting of a `Uint8Array` slice of the
 * encoded VarInt, and an offset where the VarInt encoded bytes end within the
 * `Uint8Array`.
 *
 * If `buf` is not given then a Uint8Array will be created.
 * `offset` defaults to `0`.
 *
 * If passed `buf` then that will be written into, starting at `offset`. The
 * resulting returned `Uint8Array` will be a slice of `buf`. The resulting
 * returned number is effectively `offset + bytesWritten`.
 */ export function encode(num, buf = new Uint8Array(MaxVarIntLen64), offset = 0) {
  num = BigInt(num);
  if (num < 0n) throw new RangeError("signed input given");
  for(let i = offset, len = Math.min(buf.length, MaxVarIntLen64); i <= len; i += 1){
    if (num < MSBN) {
      buf[i] = Number(num);
      i += 1;
      return [
        buf.slice(offset, i),
        i
      ];
    }
    buf[i] = Number(num & 0xFFn | MSBN);
    num >>= SHIFTN;
  }
  throw new RangeError(`${num} overflows uint64`);
}
//# sourceMappingURL=data:application/json;base64,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