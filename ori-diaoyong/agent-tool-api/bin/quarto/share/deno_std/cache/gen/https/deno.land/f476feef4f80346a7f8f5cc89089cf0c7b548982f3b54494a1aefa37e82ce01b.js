// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { ANY } from "./constants.ts";
function formatNumber(value) {
  if (value === Number.POSITIVE_INFINITY) {
    return "∞";
  } else if (value === Number.NEGATIVE_INFINITY) {
    return "⧞";
  } else {
    return value.toFixed(0);
  }
}
/**
 * Format a SemVer object into a string.
 *
 * If any number is NaN then NaN will be printed.
 *
 * If any number is positive or negative infinity then '∞' or '⧞' will be printed instead.
 *
 * @param semver The semantic version to format
 * @returns The string representation of a semantic version.
 */ export function format(semver, style = "full") {
  if (semver === ANY) {
    return "*";
  }
  const major = formatNumber(semver.major);
  const minor = formatNumber(semver.minor);
  const patch = formatNumber(semver.patch);
  const pre = semver.prerelease.join(".");
  const build = semver.build.join(".");
  const primary = `${major}.${minor}.${patch}`;
  const release = [
    primary,
    pre
  ].filter((v)=>v).join("-");
  const full = [
    release,
    build
  ].filter((v)=>v).join("+");
  switch(style){
    case "full":
      return full;
    case "release":
      return release;
    case "primary":
      return primary;
    case "build":
      return build;
    case "pre":
      return pre;
    case "patch":
      return patch;
    case "minor":
      return minor;
    case "major":
      return major;
  }
}
//# sourceMappingURL=data:application/json;base64,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