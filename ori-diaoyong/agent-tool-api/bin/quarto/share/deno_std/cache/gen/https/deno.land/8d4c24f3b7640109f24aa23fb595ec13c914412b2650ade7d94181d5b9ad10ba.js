// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { bytesToUuid } from "./_common.ts";
const UUID_RE = /^[0-9a-f]{8}-[0-9a-f]{4}-1[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
/**
 * Validates the UUID v1.
 * @param id UUID value.
 */ export function validate(id) {
  return UUID_RE.test(id);
}
let _nodeId;
let _clockseq;
let _lastMSecs = 0;
let _lastNSecs = 0;
/**
 * Generates a RFC4122 v1 UUID (time-based).
 * @param options Can use RFC time sequence values as overwrites.
 * @param buf Can allow the UUID to be written in byte-form starting at the offset.
 * @param offset Index to start writing on the UUID bytes in buffer.
 */ export function generate(options, buf, offset) {
  let i = buf && offset || 0;
  const b = buf ?? [];
  options ??= {};
  let { node = _nodeId, clockseq = _clockseq } = options;
  if (node === undefined || clockseq === undefined) {
    // deno-lint-ignore no-explicit-any
    const seedBytes = options.random ?? options.rng ?? crypto.getRandomValues(new Uint8Array(16));
    if (node === undefined) {
      node = _nodeId = [
        seedBytes[0] | 0x01,
        seedBytes[1],
        seedBytes[2],
        seedBytes[3],
        seedBytes[4],
        seedBytes[5]
      ];
    }
    if (clockseq === undefined) {
      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;
    }
  }
  let { msecs = new Date().getTime(), nsecs = _lastNSecs + 1 } = options;
  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000;
  if (dt < 0 && options.clockseq === undefined) {
    clockseq = clockseq + 1 & 0x3fff;
  }
  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {
    nsecs = 0;
  }
  if (nsecs > 10000) {
    throw new Error("Can't create more than 10M uuids/sec");
  }
  _lastMSecs = msecs;
  _lastNSecs = nsecs;
  _clockseq = clockseq;
  msecs += 12219292800000;
  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;
  b[i++] = tl >>> 24 & 0xff;
  b[i++] = tl >>> 16 & 0xff;
  b[i++] = tl >>> 8 & 0xff;
  b[i++] = tl & 0xff;
  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;
  b[i++] = tmh >>> 8 & 0xff;
  b[i++] = tmh & 0xff;
  b[i++] = tmh >>> 24 & 0xf | 0x10;
  b[i++] = tmh >>> 16 & 0xff;
  b[i++] = clockseq >>> 8 | 0x80;
  b[i++] = clockseq & 0xff;
  for(let n = 0; n < 6; ++n){
    b[i + n] = node[n];
  }
  return buf ?? bytesToUuid(b);
}
//# sourceMappingURL=data:application/json;base64,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