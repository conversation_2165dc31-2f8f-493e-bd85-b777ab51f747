// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns all distinct elements that appear in any of the given arrays
 *
 * @example
 * ```ts
 * import { union } from "https://deno.land/std@$STD_VERSION/collections/union.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const soupIngredients = ["Pepper", "Carrots", "Leek"];
 * const saladIngredients = ["Carrots", "Radicchio", "Pepper"];
 * const shoppingList = union(soupIngredients, saladIngredients);
 *
 * assertEquals(shoppingList, ["Pepper", "Carrots", "Leek", "Radicchio"]);
 * ```
 */ export function union(...arrays) {
  const set = new Set();
  for (const array of arrays){
    for (const element of array){
      set.add(element);
    }
  }
  return Array.from(set);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3VuaW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogUmV0dXJucyBhbGwgZGlzdGluY3QgZWxlbWVudHMgdGhhdCBhcHBlYXIgaW4gYW55IG9mIHRoZSBnaXZlbiBhcnJheXNcbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIGltcG9ydCB7IHVuaW9uIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vY29sbGVjdGlvbnMvdW5pb24udHNcIjtcbiAqIGltcG9ydCB7IGFzc2VydEVxdWFscyB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2Fzc2VydC9hc3NlcnRfZXF1YWxzLnRzXCI7XG4gKlxuICogY29uc3Qgc291cEluZ3JlZGllbnRzID0gW1wiUGVwcGVyXCIsIFwiQ2Fycm90c1wiLCBcIkxlZWtcIl07XG4gKiBjb25zdCBzYWxhZEluZ3JlZGllbnRzID0gW1wiQ2Fycm90c1wiLCBcIlJhZGljY2hpb1wiLCBcIlBlcHBlclwiXTtcbiAqIGNvbnN0IHNob3BwaW5nTGlzdCA9IHVuaW9uKHNvdXBJbmdyZWRpZW50cywgc2FsYWRJbmdyZWRpZW50cyk7XG4gKlxuICogYXNzZXJ0RXF1YWxzKHNob3BwaW5nTGlzdCwgW1wiUGVwcGVyXCIsIFwiQ2Fycm90c1wiLCBcIkxlZWtcIiwgXCJSYWRpY2NoaW9cIl0pO1xuICogYGBgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1bmlvbjxUPiguLi5hcnJheXM6IEl0ZXJhYmxlPFQ+W10pOiBUW10ge1xuICBjb25zdCBzZXQgPSBuZXcgU2V0PFQ+KCk7XG5cbiAgZm9yIChjb25zdCBhcnJheSBvZiBhcnJheXMpIHtcbiAgICBmb3IgKGNvbnN0IGVsZW1lbnQgb2YgYXJyYXkpIHtcbiAgICAgIHNldC5hZGQoZWxlbWVudCk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIEFycmF5LmZyb20oc2V0KTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDOzs7Ozs7Ozs7Ozs7OztDQWNDLEdBQ0QsT0FBTyxTQUFTLE1BQVMsR0FBRyxNQUFxQjtFQUMvQyxNQUFNLE1BQU0sSUFBSTtFQUVoQixLQUFLLE1BQU0sU0FBUyxPQUFRO0lBQzFCLEtBQUssTUFBTSxXQUFXLE1BQU87TUFDM0IsSUFBSSxHQUFHLENBQUM7SUFDVjtFQUNGO0VBRUEsT0FBTyxNQUFNLElBQUksQ0FBQztBQUNwQiJ9