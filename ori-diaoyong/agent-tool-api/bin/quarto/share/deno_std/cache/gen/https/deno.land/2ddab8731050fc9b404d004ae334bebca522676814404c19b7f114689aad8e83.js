// Ported from js-yaml v3.13.1:
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { Type } from "../type.ts";
// [ 64, 65, 66 ] -> [ padding, CR, LF ]
const BASE64_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";
function resolveYamlBinary(data) {
  if (data === null) return false;
  let code;
  let bitlen = 0;
  const max = data.length;
  const map = BASE64_MAP;
  // Convert one by one.
  for(let idx = 0; idx < max; idx++){
    code = map.indexOf(data.charAt(idx));
    // Skip CR/LF
    if (code > 64) continue;
    // Fail on illegal characters
    if (code < 0) return false;
    bitlen += 6;
  }
  // If there are any bits left, source was corrupted
  return bitlen % 8 === 0;
}
function constructYamlBinary(data) {
  // remove CR/LF & padding to simplify scan
  const input = data.replace(/[\r\n=]/g, "");
  const max = input.length;
  const map = BASE64_MAP;
  // Collect by 6*4 bits (3 bytes)
  const result = [];
  let bits = 0;
  for(let idx = 0; idx < max; idx++){
    if (idx % 4 === 0 && idx) {
      result.push(bits >> 16 & 0xff);
      result.push(bits >> 8 & 0xff);
      result.push(bits & 0xff);
    }
    bits = bits << 6 | map.indexOf(input.charAt(idx));
  }
  // Dump tail
  const tailbits = max % 4 * 6;
  if (tailbits === 0) {
    result.push(bits >> 16 & 0xff);
    result.push(bits >> 8 & 0xff);
    result.push(bits & 0xff);
  } else if (tailbits === 18) {
    result.push(bits >> 10 & 0xff);
    result.push(bits >> 2 & 0xff);
  } else if (tailbits === 12) {
    result.push(bits >> 4 & 0xff);
  }
  return new Uint8Array(result);
}
function representYamlBinary(object) {
  const max = object.length;
  const map = BASE64_MAP;
  // Convert every three bytes to 4 ASCII characters.
  let result = "";
  let bits = 0;
  for(let idx = 0; idx < max; idx++){
    if (idx % 3 === 0 && idx) {
      result += map[bits >> 18 & 0x3f];
      result += map[bits >> 12 & 0x3f];
      result += map[bits >> 6 & 0x3f];
      result += map[bits & 0x3f];
    }
    bits = (bits << 8) + object[idx];
  }
  // Dump tail
  const tail = max % 3;
  if (tail === 0) {
    result += map[bits >> 18 & 0x3f];
    result += map[bits >> 12 & 0x3f];
    result += map[bits >> 6 & 0x3f];
    result += map[bits & 0x3f];
  } else if (tail === 2) {
    result += map[bits >> 10 & 0x3f];
    result += map[bits >> 4 & 0x3f];
    result += map[bits << 2 & 0x3f];
    result += map[64];
  } else if (tail === 1) {
    result += map[bits >> 2 & 0x3f];
    result += map[bits << 4 & 0x3f];
    result += map[64];
    result += map[64];
  }
  return result;
}
function isBinary(obj) {
  return obj instanceof Uint8Array;
}
export const binary = new Type("tag:yaml.org,2002:binary", {
  construct: constructYamlBinary,
  kind: "scalar",
  predicate: isBinary,
  represent: representYamlBinary,
  resolve: resolveYamlBinary
});
//# sourceMappingURL=data:application/json;base64,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