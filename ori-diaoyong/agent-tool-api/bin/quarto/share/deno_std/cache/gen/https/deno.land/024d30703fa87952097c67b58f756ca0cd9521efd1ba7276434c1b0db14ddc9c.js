// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * Provides user-friendly {@linkcode serve} on top of Den<PERSON>'s native HTTP server
 * and other utilities for creating HTTP servers and clients.
 *
 * ## File Server
 *
 * A small program for serving local files over HTTP.
 *
 * ```sh
 * deno run --allow-net --allow-read https://deno.land/std/http/file_server.ts
 * > HTTP server listening on http://localhost:4507/
 * ```
 *
 * ## HTTP Status Code and Status Text
 *
 * Helper for processing status code and status text.
 *
 * ## HTTP errors
 *
 * Provides error classes for each HTTP error status code as well as utility
 * functions for handling HTTP errors in a structured way.
 *
 * ## Methods
 *
 * Provides helper functions and types to work with HTTP method strings safely.
 *
 * ## Negotiation
 *
 * A set of functions which can be used to negotiate content types, encodings and
 * languages when responding to requests.
 *
 * > Note: some libraries include accept charset functionality by analyzing the
 * > `Accept-Charset` header. This is a legacy header that
 * > [clients omit and servers should ignore](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Charset)
 * > therefore is not provided.
 *
 * ## Cookie maps
 *
 * An alternative to `cookie.ts` is `cookie_map.ts` which provides `CookieMap`,
 * `SecureCookieMap`, and `mergeHeaders` to manage request and response cookies
 * with the familiar `Map` interface.
 *
 * ## User agent handling
 *
 * The {@linkcode UserAgent} class provides user agent string parsing, allowing
 * a user agent flag to be semantically understood.
 *
 * For example to integrate the user agent provided in the header `User-Agent`
 * in an http request would look like this:
 *
 * ```ts
 * import { UserAgent } from "https://deno.land/std@$STD_VERSION/http/user_agent.ts";
 *
 * Deno.serve((req) => {
 *   const userAgent = new UserAgent(req.headers.get("user-agent") ?? "");
 *   return new Response(`Hello, ${userAgent.browser.name}
 *     on ${userAgent.os.name} ${userAgent.os.version}!`);
 * });
 * ```
 *
 * @module
 */ export * from "./cookie.ts";
export * from "./cookie_map.ts";
export * from "./etag.ts";
export * from "./http_errors.ts";
export * from "./http_status.ts";
export * from "./method.ts";
export * from "./negotiation.ts";
export * from "./server.ts";
export * from "./server_sent_event.ts";
export * from "./user_agent.ts";
//# sourceMappingURL=data:application/json;base64,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