// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Utilities for dealing with {@linkcode Date} objects.
 *
 * The following symbols from
 * [unicode LDML](http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table)
 * are supported:
 *
 * - `yyyy` - numeric year.
 * - `yy` - 2-digit year.
 * - `M` - numeric month.
 * - `MM` - 2-digit month.
 * - `d` - numeric day.
 * - `dd` - 2-digit day.
 *
 * - `H` - numeric hour (0-23 hours).
 * - `HH` - 2-digit hour (00-23 hours).
 * - `h` - numeric hour (1-12 hours).
 * - `hh` - 2-digit hour (01-12 hours).
 * - `m` - numeric minute.
 * - `mm` - 2-digit minute.
 * - `s` - numeric second.
 * - `ss` - 2-digit second.
 * - `S` - 1-digit fractionalSecond.
 * - `SS` - 2-digit fractionalSecond.
 * - `SSS` - 3-digit fractionalSecond.
 *
 * - `a` - dayPeriod, either `AM` or `PM`.
 *
 * - `'foo'` - quoted literal.
 * - `./-` - unquoted literal.
 *
 * This module is browser compatible.
 *
 * @module
 */ export * from "./constants.ts";
export * from "./day_of_year.ts";
export * from "./difference.ts";
export * from "./format.ts";
export * from "./is_leap.ts";
export * from "./parse.ts";
export * from "./to_imf.ts";
export * from "./week_of_year.ts";
//# sourceMappingURL=data:application/json;base64,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