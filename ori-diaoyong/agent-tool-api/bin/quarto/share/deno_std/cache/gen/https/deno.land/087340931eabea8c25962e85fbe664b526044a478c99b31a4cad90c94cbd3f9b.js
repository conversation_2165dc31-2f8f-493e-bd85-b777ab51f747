// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { concat } from "../bytes/concat.ts";
import { createLPS } from "./_common.ts";
/**
 * Divide a stream into chunks delimited by a given byte sequence.
 *
 * @example
 * Divide a CSV stream by commas, discarding the commas:
 * ```ts
 * import { DelimiterStream } from "https://deno.land/std@$STD_VERSION/streams/delimiter_stream.ts";
 * const res = await fetch("https://example.com/data.csv");
 * const parts = res.body!
 *   .pipeThrough(new DelimiterStream(new TextEncoder().encode(",")))
 *   .pipeThrough(new TextDecoderStream());
 * ```
 *
 * @example
 * Divide a stream after semi-colons, keeping the semi-colons in the output:
 * ```ts
 * import { DelimiterStream } from "https://deno.land/std@$STD_VERSION/streams/delimiter_stream.ts";
 * const res = await fetch("https://example.com/file.js");
 * const parts = res.body!
 *   .pipeThrough(
 *     new DelimiterStream(
 *       new TextEncoder().encode(";"),
 *       { disposition: "suffix" },
 *     )
 *   )
 *   .pipeThrough(new TextDecoderStream());
 * ```
 *
 * @param delimiter Delimiter byte sequence
 * @param options Options for the transform stream
 * @returns Transform stream
 */ export class DelimiterStream extends TransformStream {
  #bufs = [];
  #delimiter;
  #matchIndex = 0;
  #delimLPS;
  #disp;
  constructor(delimiter, options){
    super({
      transform: (chunk, controller)=>{
        this.#handle(chunk, controller);
      },
      flush: (controller)=>{
        controller.enqueue(concat(...this.#bufs));
      }
    });
    this.#delimiter = delimiter;
    this.#delimLPS = createLPS(delimiter);
    this.#disp = options?.disposition ?? "discard";
  }
  #handle(chunk, controller) {
    const bufs = this.#bufs;
    const length = chunk.byteLength;
    const disposition = this.#disp;
    const delimiter = this.#delimiter;
    const delimLen = delimiter.length;
    const lps = this.#delimLPS;
    let chunkStart = 0;
    let matchIndex = this.#matchIndex;
    let inspectIndex = 0;
    while(inspectIndex < length){
      if (chunk[inspectIndex] === delimiter[matchIndex]) {
        // Next byte matched our next delimiter byte
        inspectIndex++;
        matchIndex++;
        if (matchIndex === delimLen) {
          // Full match
          matchIndex = 0;
          const delimiterStartIndex = inspectIndex - delimLen;
          const delimitedChunkEnd = disposition === "suffix" ? inspectIndex : delimiterStartIndex;
          if (delimitedChunkEnd <= 0 && bufs.length === 0) {
            // Our chunk started with a delimiter and no previous chunks exist:
            // Enqueue an empty chunk.
            controller.enqueue(new Uint8Array());
            chunkStart = disposition === "prefix" ? 0 : inspectIndex;
          } else if (delimitedChunkEnd > 0 && bufs.length === 0) {
            // No previous chunks, slice from current chunk.
            controller.enqueue(chunk.subarray(chunkStart, delimitedChunkEnd));
            // Our chunk may have more than one delimiter; we must remember where
            // the next delimited chunk begins.
            chunkStart = disposition === "prefix" ? delimiterStartIndex : inspectIndex;
          } else if (delimitedChunkEnd === 0 && bufs.length > 0) {
            // Our chunk started with a delimiter, previous chunks are passed as
            // they are (with concatenation).
            if (bufs.length === 1) {
              // Concat not needed when a single buffer is passed.
              controller.enqueue(bufs[0]);
            } else {
              controller.enqueue(concat(...bufs));
            }
            // Drop all previous chunks.
            bufs.length = 0;
            if (disposition !== "prefix") {
              // suffix or discard: The next chunk starts where our inspection finished.
              // We should only ever end up here with a discard disposition as
              // for a suffix disposition this branch would mean that the previous
              // chunk ended with a full match but was not enqueued.
              chunkStart = inspectIndex;
            } else {
              chunkStart = 0;
            }
          } else if (delimitedChunkEnd < 0 && bufs.length > 0) {
            // Our chunk started by finishing a partial delimiter match.
            const lastIndex = bufs.length - 1;
            const last = bufs[lastIndex];
            const lastSliceIndex = last.byteLength + delimitedChunkEnd;
            const lastSliced = last.subarray(0, lastSliceIndex);
            if (lastIndex === 0) {
              controller.enqueue(lastSliced);
            } else {
              bufs[lastIndex] = lastSliced;
              controller.enqueue(concat(...bufs));
            }
            bufs.length = 0;
            if (disposition === "prefix") {
              // Must keep last bytes of last chunk.
              bufs.push(last.subarray(lastSliceIndex));
              chunkStart = 0;
            } else {
              chunkStart = inspectIndex;
            }
          } else if (delimitedChunkEnd > 0 && bufs.length > 0) {
            // Previous chunks and current chunk together form a delimited chunk.
            const chunkSliced = chunk.subarray(chunkStart, delimitedChunkEnd);
            const result = concat(...bufs, chunkSliced);
            bufs.length = 0;
            controller.enqueue(result);
            chunkStart = disposition === "prefix" ? delimitedChunkEnd : inspectIndex;
          } else {
            throw new Error("unreachable");
          }
        }
      } else if (matchIndex === 0) {
        // No match ongoing, keep going through the buffer.
        inspectIndex++;
      } else {
        // Ongoing match: Degrade to the previous possible match.
        // eg. If we're looking for 'AAB' and had matched 'AA' previously
        // but now got a new 'A', then we'll drop down to having matched
        // just 'A'. The while loop will turn around again and we'll rematch
        // to 'AA' and proceed onwards to try and match on 'B' again.
        matchIndex = lps[matchIndex - 1];
      }
    }
    // Save match index.
    this.#matchIndex = matchIndex;
    if (chunkStart === 0) {
      bufs.push(chunk);
    } else if (chunkStart < length) {
      // If we matched partially somewhere in the middle of our chunk
      // then the remnants should be pushed into buffers.
      bufs.push(chunk.subarray(chunkStart));
    }
  }
}
//# sourceMappingURL=data:application/json;base64,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