// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { format } from "./format.ts";
/**
 * Formats the comparator into a string
 * @example >=0.0.0
 * @param comparator
 * @returns A string representation of the comparator
 */ export function comparatorFormat(comparator) {
  const { semver, operator } = comparator;
  return `${operator}${format(semver)}`;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9jb21wYXJhdG9yX2Zvcm1hdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHR5cGUgeyBTZW1WZXJDb21wYXJhdG9yIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcbmltcG9ydCB7IGZvcm1hdCB9IGZyb20gXCIuL2Zvcm1hdC50c1wiO1xuXG4vKipcbiAqIEZvcm1hdHMgdGhlIGNvbXBhcmF0b3IgaW50byBhIHN0cmluZ1xuICogQGV4YW1wbGUgPj0wLjAuMFxuICogQHBhcmFtIGNvbXBhcmF0b3JcbiAqIEByZXR1cm5zIEEgc3RyaW5nIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBjb21wYXJhdG9yXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21wYXJhdG9yRm9ybWF0KGNvbXBhcmF0b3I6IFNlbVZlckNvbXBhcmF0b3IpIHtcbiAgY29uc3QgeyBzZW12ZXIsIG9wZXJhdG9yIH0gPSBjb21wYXJhdG9yO1xuICByZXR1cm4gYCR7b3BlcmF0b3J9JHtmb3JtYXQoc2VtdmVyKX1gO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUUxRSxTQUFTLE1BQU0sUUFBUSxjQUFjO0FBRXJDOzs7OztDQUtDLEdBQ0QsT0FBTyxTQUFTLGlCQUFpQixVQUE0QjtFQUMzRCxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxHQUFHO0VBQzdCLE9BQU8sQ0FBQyxFQUFFLFNBQVMsRUFBRSxPQUFPLFFBQVEsQ0FBQztBQUN2QyJ9