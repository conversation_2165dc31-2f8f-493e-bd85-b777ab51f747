// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { isSemVerComparator } from "./is_semver_comparator.ts";
/**
 * Does a deep check on the object to determine if its a valid range.
 *
 * Objects with extra fields are still considered valid if they have at
 * least the correct fields.
 *
 * Adds a type assertion if true.
 * @param value The value to check if its a valid SemVerRange
 * @returns True if its a valid SemVerRange otherwise false.
 */ export function isSemVerRange(value) {
  if (value === null || value === undefined) return false;
  if (Array.isArray(value)) return false;
  if (typeof value !== "object") return false;
  const { ranges } = value;
  return Array.isArray(ranges), ranges.every((r)=>Array.isArray(r) && r.every((c)=>isSemVerComparator(c)));
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9pc19zZW12ZXJfcmFuZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbmltcG9ydCB0eXBlIHsgU2VtVmVyUmFuZ2UgfSBmcm9tIFwiLi90eXBlcy50c1wiO1xuaW1wb3J0IHsgaXNTZW1WZXJDb21wYXJhdG9yIH0gZnJvbSBcIi4vaXNfc2VtdmVyX2NvbXBhcmF0b3IudHNcIjtcblxuLyoqXG4gKiBEb2VzIGEgZGVlcCBjaGVjayBvbiB0aGUgb2JqZWN0IHRvIGRldGVybWluZSBpZiBpdHMgYSB2YWxpZCByYW5nZS5cbiAqXG4gKiBPYmplY3RzIHdpdGggZXh0cmEgZmllbGRzIGFyZSBzdGlsbCBjb25zaWRlcmVkIHZhbGlkIGlmIHRoZXkgaGF2ZSBhdFxuICogbGVhc3QgdGhlIGNvcnJlY3QgZmllbGRzLlxuICpcbiAqIEFkZHMgYSB0eXBlIGFzc2VydGlvbiBpZiB0cnVlLlxuICogQHBhcmFtIHZhbHVlIFRoZSB2YWx1ZSB0byBjaGVjayBpZiBpdHMgYSB2YWxpZCBTZW1WZXJSYW5nZVxuICogQHJldHVybnMgVHJ1ZSBpZiBpdHMgYSB2YWxpZCBTZW1WZXJSYW5nZSBvdGhlcndpc2UgZmFsc2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1NlbVZlclJhbmdlKHZhbHVlOiB1bmtub3duKTogdmFsdWUgaXMgU2VtVmVyUmFuZ2Uge1xuICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuIGZhbHNlO1xuICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHJldHVybiBmYWxzZTtcbiAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIikgcmV0dXJuIGZhbHNlO1xuICBjb25zdCB7IHJhbmdlcyB9ID0gdmFsdWUgYXMgU2VtVmVyUmFuZ2U7XG4gIHJldHVybiAoXG4gICAgQXJyYXkuaXNBcnJheShyYW5nZXMpLFxuICAgICAgcmFuZ2VzLmV2ZXJ5KChyKSA9PlxuICAgICAgICBBcnJheS5pc0FycmF5KHIpICYmIHIuZXZlcnkoKGMpID0+IGlzU2VtVmVyQ29tcGFyYXRvcihjKSlcbiAgICAgIClcbiAgKTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFFMUUsU0FBUyxrQkFBa0IsUUFBUSw0QkFBNEI7QUFFL0Q7Ozs7Ozs7OztDQVNDLEdBQ0QsT0FBTyxTQUFTLGNBQWMsS0FBYztFQUMxQyxJQUFJLFVBQVUsUUFBUSxVQUFVLFdBQVcsT0FBTztFQUNsRCxJQUFJLE1BQU0sT0FBTyxDQUFDLFFBQVEsT0FBTztFQUNqQyxJQUFJLE9BQU8sVUFBVSxVQUFVLE9BQU87RUFDdEMsTUFBTSxFQUFFLE1BQU0sRUFBRSxHQUFHO0VBQ25CLE9BQ0UsTUFBTSxPQUFPLENBQUMsU0FDWixPQUFPLEtBQUssQ0FBQyxDQUFDLElBQ1osTUFBTSxPQUFPLENBQUMsTUFBTSxFQUFFLEtBQUssQ0FBQyxDQUFDLElBQU0sbUJBQW1CO0FBRzlEIn0=