// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { ANY, INVALID, MAX } from "./constants.ts";
/**
 * The maximum version that could match this comparator.
 *
 * If an invalid comparator is given such as <0.0.0 then
 * an out of range semver will be returned.
 * @returns the version, the MAX version or the next smallest patch version
 */ export function comparatorMax(semver, operator) {
  if (semver === ANY) {
    return MAX;
  }
  switch(operator){
    case "!=":
    case "!==":
    case ">":
    case ">=":
      return MAX;
    case "":
    case "=":
    case "==":
    case "===":
    case "<=":
      return semver;
    case "<":
      {
        const patch = semver.patch - 1;
        const minor = patch >= 0 ? semver.minor : semver.minor - 1;
        const major = minor >= 0 ? semver.major : semver.major - 1;
        // if you try to do <0.0.0 it will Give you -∞.∞.∞
        // which means no SemVer can compare successfully to it.
        if (major < 0) {
          return INVALID;
        } else {
          return {
            major,
            minor: minor >= 0 ? minor : Number.POSITIVE_INFINITY,
            patch: patch >= 0 ? patch : Number.POSITIVE_INFINITY,
            prerelease: [],
            build: []
          };
        }
      }
  }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9jb21wYXJhdG9yX21heC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHR5cGUgeyBPcGVyYXRvciwgU2VtVmVyIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcbmltcG9ydCB7IEFOWSwgSU5WQUxJRCwgTUFYIH0gZnJvbSBcIi4vY29uc3RhbnRzLnRzXCI7XG5cbi8qKlxuICogVGhlIG1heGltdW0gdmVyc2lvbiB0aGF0IGNvdWxkIG1hdGNoIHRoaXMgY29tcGFyYXRvci5cbiAqXG4gKiBJZiBhbiBpbnZhbGlkIGNvbXBhcmF0b3IgaXMgZ2l2ZW4gc3VjaCBhcyA8MC4wLjAgdGhlblxuICogYW4gb3V0IG9mIHJhbmdlIHNlbXZlciB3aWxsIGJlIHJldHVybmVkLlxuICogQHJldHVybnMgdGhlIHZlcnNpb24sIHRoZSBNQVggdmVyc2lvbiBvciB0aGUgbmV4dCBzbWFsbGVzdCBwYXRjaCB2ZXJzaW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21wYXJhdG9yTWF4KHNlbXZlcjogU2VtVmVyLCBvcGVyYXRvcjogT3BlcmF0b3IpOiBTZW1WZXIge1xuICBpZiAoc2VtdmVyID09PSBBTlkpIHtcbiAgICByZXR1cm4gTUFYO1xuICB9XG4gIHN3aXRjaCAob3BlcmF0b3IpIHtcbiAgICBjYXNlIFwiIT1cIjpcbiAgICBjYXNlIFwiIT09XCI6XG4gICAgY2FzZSBcIj5cIjpcbiAgICBjYXNlIFwiPj1cIjpcbiAgICAgIHJldHVybiBNQVg7XG4gICAgY2FzZSBcIlwiOlxuICAgIGNhc2UgXCI9XCI6XG4gICAgY2FzZSBcIj09XCI6XG4gICAgY2FzZSBcIj09PVwiOlxuICAgIGNhc2UgXCI8PVwiOlxuICAgICAgcmV0dXJuIHNlbXZlcjtcbiAgICBjYXNlIFwiPFwiOiB7XG4gICAgICBjb25zdCBwYXRjaCA9IHNlbXZlci5wYXRjaCAtIDE7XG4gICAgICBjb25zdCBtaW5vciA9IHBhdGNoID49IDAgPyBzZW12ZXIubWlub3IgOiBzZW12ZXIubWlub3IgLSAxO1xuICAgICAgY29uc3QgbWFqb3IgPSBtaW5vciA+PSAwID8gc2VtdmVyLm1ham9yIDogc2VtdmVyLm1ham9yIC0gMTtcbiAgICAgIC8vIGlmIHlvdSB0cnkgdG8gZG8gPDAuMC4wIGl0IHdpbGwgR2l2ZSB5b3UgLeKIni7iiJ4u4oieXG4gICAgICAvLyB3aGljaCBtZWFucyBubyBTZW1WZXIgY2FuIGNvbXBhcmUgc3VjY2Vzc2Z1bGx5IHRvIGl0LlxuICAgICAgaWYgKG1ham9yIDwgMCkge1xuICAgICAgICByZXR1cm4gSU5WQUxJRDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgbWFqb3IsXG4gICAgICAgICAgbWlub3I6IG1pbm9yID49IDAgPyBtaW5vciA6IE51bWJlci5QT1NJVElWRV9JTkZJTklUWSxcbiAgICAgICAgICBwYXRjaDogcGF0Y2ggPj0gMCA/IHBhdGNoIDogTnVtYmVyLlBPU0lUSVZFX0lORklOSVRZLFxuICAgICAgICAgIHByZXJlbGVhc2U6IFtdLFxuICAgICAgICAgIGJ1aWxkOiBbXSxcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFFMUUsU0FBUyxHQUFHLEVBQUUsT0FBTyxFQUFFLEdBQUcsUUFBUSxpQkFBaUI7QUFFbkQ7Ozs7OztDQU1DLEdBQ0QsT0FBTyxTQUFTLGNBQWMsTUFBYyxFQUFFLFFBQWtCO0VBQzlELElBQUksV0FBVyxLQUFLO0lBQ2xCLE9BQU87RUFDVDtFQUNBLE9BQVE7SUFDTixLQUFLO0lBQ0wsS0FBSztJQUNMLEtBQUs7SUFDTCxLQUFLO01BQ0gsT0FBTztJQUNULEtBQUs7SUFDTCxLQUFLO0lBQ0wsS0FBSztJQUNMLEtBQUs7SUFDTCxLQUFLO01BQ0gsT0FBTztJQUNULEtBQUs7TUFBSztRQUNSLE1BQU0sUUFBUSxPQUFPLEtBQUssR0FBRztRQUM3QixNQUFNLFFBQVEsU0FBUyxJQUFJLE9BQU8sS0FBSyxHQUFHLE9BQU8sS0FBSyxHQUFHO1FBQ3pELE1BQU0sUUFBUSxTQUFTLElBQUksT0FBTyxLQUFLLEdBQUcsT0FBTyxLQUFLLEdBQUc7UUFDekQsa0RBQWtEO1FBQ2xELHdEQUF3RDtRQUN4RCxJQUFJLFFBQVEsR0FBRztVQUNiLE9BQU87UUFDVCxPQUFPO1VBQ0wsT0FBTztZQUNMO1lBQ0EsT0FBTyxTQUFTLElBQUksUUFBUSxPQUFPLGlCQUFpQjtZQUNwRCxPQUFPLFNBQVMsSUFBSSxRQUFRLE9BQU8saUJBQWlCO1lBQ3BELFlBQVksRUFBRTtZQUNkLE9BQU8sRUFBRTtVQUNYO1FBQ0Y7TUFDRjtFQUNGO0FBQ0YifQ==