// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
export class BinarySearchNode {
  parent;
  value;
  left;
  right;
  constructor(parent, value){
    this.parent = parent;
    this.value = value;
    this.left = null;
    this.right = null;
  }
  static from(node) {
    const copy = new BinarySearchNode(node.parent, node.value);
    copy.left = node.left;
    copy.right = node.right;
    return copy;
  }
  directionFromParent() {
    return this.parent === null ? null : this === this.parent.left ? "left" : this === this.parent.right ? "right" : null;
  }
  findMinNode() {
    let minNode = this.left;
    while(minNode?.left)minNode = minNode.left;
    return minNode ?? this;
  }
  findMaxNode() {
    let maxNode = this.right;
    while(maxNode?.right)maxNode = maxNode.right;
    return maxNode ?? this;
  }
  findSuccessorNode() {
    if (this.right !== null) return this.right.findMinNode();
    let parent = this.parent;
    let direction = this.directionFromParent();
    while(parent && direction === "right"){
      direction = parent.directionFromParent();
      parent = parent.parent;
    }
    return parent;
  }
}
//# sourceMappingURL=data:application/json;base64,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