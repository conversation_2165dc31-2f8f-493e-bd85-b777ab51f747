// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { dump } from "./_dumper/dumper.ts";
/**
 * Serializes `object` as a YAML document.
 *
 * You can disable exceptions by setting the skipInvalid option to true.
 */ export function stringify(obj, options) {
  return dump(obj, options);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3lhbWwvc3RyaW5naWZ5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFBvcnRlZCBmcm9tIGpzLXlhbWwgdjMuMTMuMTpcbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9ub2RlY2EvanMteWFtbC9jb21taXQvNjY1YWFkZGE0MjM0OWRjYWU4NjlmMTIwNDBkOWIxMGVmMThkMTJkYVxuLy8gQ29weXJpZ2h0IDIwMTEtMjAxNSBieSBWaXRhbHkgUHV6cmluLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbmltcG9ydCB7IGR1bXAgfSBmcm9tIFwiLi9fZHVtcGVyL2R1bXBlci50c1wiO1xuaW1wb3J0IHR5cGUgeyBEdW1wZXJTdGF0ZU9wdGlvbnMgfSBmcm9tIFwiLi9fZHVtcGVyL2R1bXBlcl9zdGF0ZS50c1wiO1xuXG5leHBvcnQgdHlwZSBEdW1wT3B0aW9ucyA9IER1bXBlclN0YXRlT3B0aW9ucztcblxuLyoqXG4gKiBTZXJpYWxpemVzIGBvYmplY3RgIGFzIGEgWUFNTCBkb2N1bWVudC5cbiAqXG4gKiBZb3UgY2FuIGRpc2FibGUgZXhjZXB0aW9ucyBieSBzZXR0aW5nIHRoZSBza2lwSW52YWxpZCBvcHRpb24gdG8gdHJ1ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ2lmeShcbiAgb2JqOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPixcbiAgb3B0aW9ucz86IER1bXBPcHRpb25zLFxuKTogc3RyaW5nIHtcbiAgcmV0dXJuIGR1bXAob2JqLCBvcHRpb25zKTtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwrQkFBK0I7QUFDL0Isb0ZBQW9GO0FBQ3BGLDBFQUEwRTtBQUMxRSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDLFNBQVMsSUFBSSxRQUFRLHNCQUFzQjtBQUszQzs7OztDQUlDLEdBQ0QsT0FBTyxTQUFTLFVBQ2QsR0FBNEIsRUFDNUIsT0FBcUI7RUFFckIsT0FBTyxLQUFLLEtBQUs7QUFDbkIifQ==