// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Calls the given reducer on each element of the given collection, passing its
 * result as the accumulator to the next respective call, starting with the
 * given initialValue. Returns all intermediate accumulator results.
 *
 * @example
 * ```ts
 * import { runningReduce } from "https://deno.land/std@$STD_VERSION/collections/running_reduce.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [1, 2, 3, 4, 5];
 * const sumSteps = runningReduce(numbers, (sum, current) => sum + current, 0);
 *
 * assertEquals(sumSteps, [1, 3, 6, 10, 15]);
 * ```
 */ export function runningReduce(array, reducer, initialValue) {
  let currentResult = initialValue;
  return array.map((el, currentIndex)=>currentResult = reducer(currentResult, el, currentIndex));
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3J1bm5pbmdfcmVkdWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogQ2FsbHMgdGhlIGdpdmVuIHJlZHVjZXIgb24gZWFjaCBlbGVtZW50IG9mIHRoZSBnaXZlbiBjb2xsZWN0aW9uLCBwYXNzaW5nIGl0c1xuICogcmVzdWx0IGFzIHRoZSBhY2N1bXVsYXRvciB0byB0aGUgbmV4dCByZXNwZWN0aXZlIGNhbGwsIHN0YXJ0aW5nIHdpdGggdGhlXG4gKiBnaXZlbiBpbml0aWFsVmFsdWUuIFJldHVybnMgYWxsIGludGVybWVkaWF0ZSBhY2N1bXVsYXRvciByZXN1bHRzLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgcnVubmluZ1JlZHVjZSB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL3J1bm5pbmdfcmVkdWNlLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IG51bWJlcnMgPSBbMSwgMiwgMywgNCwgNV07XG4gKiBjb25zdCBzdW1TdGVwcyA9IHJ1bm5pbmdSZWR1Y2UobnVtYmVycywgKHN1bSwgY3VycmVudCkgPT4gc3VtICsgY3VycmVudCwgMCk7XG4gKlxuICogYXNzZXJ0RXF1YWxzKHN1bVN0ZXBzLCBbMSwgMywgNiwgMTAsIDE1XSk7XG4gKiBgYGBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJ1bm5pbmdSZWR1Y2U8VCwgTz4oXG4gIGFycmF5OiByZWFkb25seSBUW10sXG4gIHJlZHVjZXI6IChhY2N1bXVsYXRvcjogTywgY3VycmVudDogVCwgY3VycmVudEluZGV4OiBudW1iZXIpID0+IE8sXG4gIGluaXRpYWxWYWx1ZTogTyxcbik6IE9bXSB7XG4gIGxldCBjdXJyZW50UmVzdWx0ID0gaW5pdGlhbFZhbHVlO1xuICByZXR1cm4gYXJyYXkubWFwKChlbCwgY3VycmVudEluZGV4KSA9PlxuICAgIGN1cnJlbnRSZXN1bHQgPSByZWR1Y2VyKGN1cnJlbnRSZXN1bHQsIGVsLCBjdXJyZW50SW5kZXgpXG4gICk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FDRCxPQUFPLFNBQVMsY0FDZCxLQUFtQixFQUNuQixPQUFnRSxFQUNoRSxZQUFlO0VBRWYsSUFBSSxnQkFBZ0I7RUFDcEIsT0FBTyxNQUFNLEdBQUcsQ0FBQyxDQUFDLElBQUksZUFDcEIsZ0JBQWdCLFFBQVEsZUFBZSxJQUFJO0FBRS9DIn0=