// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { eq } from "./eq.ts";
/** Returns difference between two versions by the release type, or
 * `undefined` if the versions are the same. */ export function difference(s0, s1) {
  if (eq(s0, s1)) {
    return undefined;
  } else {
    let prefix = "";
    let defaultResult = undefined;
    if (s0 && s1) {
      if (s0.prerelease.length || s1.prerelease.length) {
        prefix = "pre";
        defaultResult = "prerelease";
      }
      for(const key in s0){
        if (key === "major" || key === "minor" || key === "patch") {
          if (s0[key] !== s1[key]) {
            return prefix + key;
          }
        }
      }
    }
    return defaultResult; // may be undefined
  }
}
//# sourceMappingURL=data:application/json;base64,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