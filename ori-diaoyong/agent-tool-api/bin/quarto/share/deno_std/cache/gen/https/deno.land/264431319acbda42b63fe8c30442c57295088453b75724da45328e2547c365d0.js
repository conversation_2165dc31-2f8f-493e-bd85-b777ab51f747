// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
import { <PERSON>uff<PERSON> } from "../io/buffer.ts";
import { writeAll } from "./write_all.ts";
/**
 * @deprecated (will be removed after 1.0.0) Use ReadableStreamDefaultReader directly.
 *
 * Create a `Reader` from a `ReadableStreamDefaultReader`.
 *
 * @example
 * ```ts
 * import { copy } from "https://deno.land/std@$STD_VERSION/streams/copy.ts";
 * import { readerFromStreamReader } from "https://deno.land/std@$STD_VERSION/streams/reader_from_stream_reader.ts";
 *
 * const res = await fetch("https://deno.land");
 * const file = await Deno.open("./deno.land.html", { create: true, write: true });
 *
 * const reader = readerFromStreamReader(res.body!.getReader());
 * await copy(reader, file);
 * file.close();
 * ```
 */ export function readerFromStreamReader(streamReader) {
  const buffer = new Buffer();
  return {
    async read (p) {
      if (buffer.empty()) {
        const res = await streamReader.read();
        if (res.done) {
          return null; // EOF
        }
        await writeAll(buffer, res.value);
      }
      return buffer.read(p);
    }
  };
}
//# sourceMappingURL=data:application/json;base64,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