// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns an array excluding all given values.
 *
 * @example
 * ```ts
 * import { withoutAll } from "https://deno.land/std@$STD_VERSION/collections/without_all.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const withoutList = withoutAll([2, 1, 2, 3], [1, 2]);
 *
 * assertEquals(withoutList, [3]);
 * ```
 */ export function withoutAll(array, values) {
  const toExclude = new Set(values);
  return array.filter((it)=>!toExclude.has(it));
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL3dpdGhvdXRfYWxsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogUmV0dXJucyBhbiBhcnJheSBleGNsdWRpbmcgYWxsIGdpdmVuIHZhbHVlcy5cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIGltcG9ydCB7IHdpdGhvdXRBbGwgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9jb2xsZWN0aW9ucy93aXRob3V0X2FsbC50c1wiO1xuICogaW1wb3J0IHsgYXNzZXJ0RXF1YWxzIH0gZnJvbSBcImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAkU1REX1ZFUlNJT04vYXNzZXJ0L2Fzc2VydF9lcXVhbHMudHNcIjtcbiAqXG4gKiBjb25zdCB3aXRob3V0TGlzdCA9IHdpdGhvdXRBbGwoWzIsIDEsIDIsIDNdLCBbMSwgMl0pO1xuICpcbiAqIGFzc2VydEVxdWFscyh3aXRob3V0TGlzdCwgWzNdKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gd2l0aG91dEFsbDxUPihhcnJheTogcmVhZG9ubHkgVFtdLCB2YWx1ZXM6IHJlYWRvbmx5IFRbXSk6IFRbXSB7XG4gIGNvbnN0IHRvRXhjbHVkZSA9IG5ldyBTZXQodmFsdWVzKTtcbiAgcmV0dXJuIGFycmF5LmZpbHRlcigoaXQpID0+ICF0b0V4Y2x1ZGUuaGFzKGl0KSk7XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFO0FBQzFFLHFDQUFxQztBQUVyQzs7Ozs7Ozs7Ozs7O0NBWUMsR0FDRCxPQUFPLFNBQVMsV0FBYyxLQUFtQixFQUFFLE1BQW9CO0VBQ3JFLE1BQU0sWUFBWSxJQUFJLElBQUk7RUFDMUIsT0FBTyxNQUFNLE1BQU0sQ0FBQyxDQUFDLEtBQU8sQ0FBQyxVQUFVLEdBQUcsQ0FBQztBQUM3QyJ9