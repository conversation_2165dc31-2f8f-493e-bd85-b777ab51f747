// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
function pre(prerelease, identifier) {
  let values = [
    ...prerelease
  ];
  // In reality this will either be 0, 1 or 2 entries.
  let i = values.length;
  while(--i >= 0){
    if (typeof values[i] === "number") {
      // deno-fmt-ignore
      values[i]++;
      i = -2;
    }
  }
  if (i === -1) {
    // didn't increment anything
    values.push(0);
  }
  if (identifier) {
    // 1.2.0-beta.1 bumps to 1.2.0-beta.2,
    // 1.2.0-beta.foobar or 1.2.0-beta bumps to 1.2.0-beta.0
    if (values[0] === identifier) {
      if (isNaN(values[1])) {
        values = [
          identifier,
          0
        ];
      }
    } else {
      values = [
        identifier,
        0
      ];
    }
  }
  return values;
}
function parseBuild(build, metadata) {
  return metadata === undefined ? build : metadata.split(".").filter((m)=>m);
}
/**
 * Returns the new version resulting from an increment by release type.
 *
 * `premajor`, `preminor` and `prepatch` will bump the version up to the next version,
 * based on the type, and will also add prerelease metadata.
 *
 * If called from a non-prerelease version, the `prerelease` will work the same as
 * `prepatch`. The patch version is incremented and then is made into a prerelease. If
 * the input version is already a prerelease it will simply increment the prerelease
 * metadata.
 *
 * If a prerelease identifier is specified without a number then a number will be added.
 * For example `pre` will result in `pre.0`. If the existing version already has a
 * prerelease with a number and its the same prerelease identifier then the number
 * will be incremented. If the identifier differs from the new identifier then the new
 * identifier is applied and the number is reset to `0`.
 *
 * If the input version has build metadata it will be preserved on the resulting version
 * unless a new build parameter is specified. Specifying `""` will unset existing build
 * metadata.
 * @param version The version to increment
 * @param release The type of increment to perform
 * @param prerelease The pre-release metadata of the new version
 * @param build The build metadata of the new version
 * @returns
 */ export function increment(version, release, prerelease, build) {
  let result;
  switch(release){
    case "premajor":
      result = {
        major: version.major + 1,
        minor: 0,
        patch: 0,
        prerelease: pre(version.prerelease, prerelease),
        build: parseBuild(version.build, build)
      };
      break;
    case "preminor":
      result = {
        major: version.major,
        minor: version.minor + 1,
        patch: 0,
        prerelease: pre(version.prerelease, prerelease),
        build: parseBuild(version.build, build)
      };
      break;
    case "prepatch":
      result = {
        major: version.major,
        minor: version.minor,
        patch: version.patch + 1,
        prerelease: pre(version.prerelease, prerelease),
        build: parseBuild(version.build, build)
      };
      break;
    // If the input is a non-prerelease version, this acts the same as
    // prepatch.
    case "prerelease":
      if (version.prerelease.length === 0) {
        result = {
          major: version.major,
          minor: version.minor,
          patch: version.patch + 1,
          prerelease: pre(version.prerelease, prerelease),
          build: parseBuild(version.build, build)
        };
        break;
      } else {
        result = {
          major: version.major,
          minor: version.minor,
          patch: version.patch,
          prerelease: pre(version.prerelease, prerelease),
          build: parseBuild(version.build, build)
        };
        break;
      }
    case "major":
      // If this is a pre-major version, bump up to the same major version.
      // Otherwise increment major.
      // 1.0.0-5 bumps to 1.0.0
      // 1.1.0 bumps to 2.0.0
      if (version.minor !== 0 || version.patch !== 0 || version.prerelease.length === 0) {
        result = {
          major: version.major + 1,
          minor: 0,
          patch: 0,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      } else {
        result = {
          major: version.major,
          minor: 0,
          patch: 0,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      }
    case "minor":
      // If this is a pre-minor version, bump up to the same minor version.
      // Otherwise increment minor.
      // 1.2.0-5 bumps to 1.2.0
      // 1.2.1 bumps to 1.3.0
      if (version.patch !== 0 || version.prerelease.length === 0) {
        result = {
          major: version.major,
          minor: version.minor + 1,
          patch: 0,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      } else {
        result = {
          major: version.major,
          minor: version.minor,
          patch: 0,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      }
    case "patch":
      // If this is not a pre-release version, it will increment the patch.
      // If it is a pre-release it will bump up to the same patch version.
      // 1.2.0-5 patches to 1.2.0
      // 1.2.0 patches to 1.2.1
      if (version.prerelease.length === 0) {
        result = {
          major: version.major,
          minor: version.minor,
          patch: version.patch + 1,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      } else {
        result = {
          major: version.major,
          minor: version.minor,
          patch: version.patch,
          prerelease: [],
          build: parseBuild(version.build, build)
        };
        break;
      }
    // 1.0.0 "pre" would become 1.0.0-0
    // 1.0.0-0 would become 1.0.0-1
    // 1.0.0-beta.0 would be come 1.0.0-beta.1
    // switching the pre identifier resets the number to 0
    case "pre":
      result = {
        major: version.major,
        minor: version.minor,
        patch: version.patch,
        prerelease: pre(version.prerelease, prerelease),
        build: parseBuild(version.build, build)
      };
      break;
    default:
      throw new Error(`invalid increment argument: ${release}`);
  }
  return result;
}
//# sourceMappingURL=data:application/json;base64,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