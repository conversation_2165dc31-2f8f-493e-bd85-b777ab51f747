// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
export function _format(sep, pathObject) {
  const dir = pathObject.dir || pathObject.root;
  const base = pathObject.base || (pathObject.name || "") + (pathObject.ext || "");
  if (!dir) return base;
  if (base === sep) return dir;
  if (dir === pathObject.root) return dir + base;
  return dir + sep + base;
}
export function assertArg(pathObject) {
  if (pathObject === null || typeof pathObject !== "object") {
    throw new TypeError(`The "pathObject" argument must be of type Object. Received type ${typeof pathObject}`);
  }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3BhdGgvX2NvbW1vbi9mb3JtYXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTgtMjAyMyB0aGUgRGVubyBhdXRob3JzLiBBbGwgcmlnaHRzIHJlc2VydmVkLiBNSVQgbGljZW5zZS5cbi8vIFRoaXMgbW9kdWxlIGlzIGJyb3dzZXIgY29tcGF0aWJsZS5cblxuaW1wb3J0IHR5cGUgeyBGb3JtYXRJbnB1dFBhdGhPYmplY3QgfSBmcm9tIFwiLi4vX2ludGVyZmFjZS50c1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gX2Zvcm1hdChcbiAgc2VwOiBzdHJpbmcsXG4gIHBhdGhPYmplY3Q6IEZvcm1hdElucHV0UGF0aE9iamVjdCxcbik6IHN0cmluZyB7XG4gIGNvbnN0IGRpcjogc3RyaW5nIHwgdW5kZWZpbmVkID0gcGF0aE9iamVjdC5kaXIgfHwgcGF0aE9iamVjdC5yb290O1xuICBjb25zdCBiYXNlOiBzdHJpbmcgPSBwYXRoT2JqZWN0LmJhc2UgfHxcbiAgICAocGF0aE9iamVjdC5uYW1lIHx8IFwiXCIpICsgKHBhdGhPYmplY3QuZXh0IHx8IFwiXCIpO1xuICBpZiAoIWRpcikgcmV0dXJuIGJhc2U7XG4gIGlmIChiYXNlID09PSBzZXApIHJldHVybiBkaXI7XG4gIGlmIChkaXIgPT09IHBhdGhPYmplY3Qucm9vdCkgcmV0dXJuIGRpciArIGJhc2U7XG4gIHJldHVybiBkaXIgKyBzZXAgKyBiYXNlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gYXNzZXJ0QXJnKHBhdGhPYmplY3Q6IEZvcm1hdElucHV0UGF0aE9iamVjdCkge1xuICBpZiAocGF0aE9iamVjdCA9PT0gbnVsbCB8fCB0eXBlb2YgcGF0aE9iamVjdCAhPT0gXCJvYmplY3RcIikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICBgVGhlIFwicGF0aE9iamVjdFwiIGFyZ3VtZW50IG11c3QgYmUgb2YgdHlwZSBPYmplY3QuIFJlY2VpdmVkIHR5cGUgJHt0eXBlb2YgcGF0aE9iamVjdH1gLFxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBSXJDLE9BQU8sU0FBUyxRQUNkLEdBQVcsRUFDWCxVQUFpQztFQUVqQyxNQUFNLE1BQTBCLFdBQVcsR0FBRyxJQUFJLFdBQVcsSUFBSTtFQUNqRSxNQUFNLE9BQWUsV0FBVyxJQUFJLElBQ2xDLENBQUMsV0FBVyxJQUFJLElBQUksRUFBRSxJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksRUFBRTtFQUNqRCxJQUFJLENBQUMsS0FBSyxPQUFPO0VBQ2pCLElBQUksU0FBUyxLQUFLLE9BQU87RUFDekIsSUFBSSxRQUFRLFdBQVcsSUFBSSxFQUFFLE9BQU8sTUFBTTtFQUMxQyxPQUFPLE1BQU0sTUFBTTtBQUNyQjtBQUVBLE9BQU8sU0FBUyxVQUFVLFVBQWlDO0VBQ3pELElBQUksZUFBZSxRQUFRLE9BQU8sZUFBZSxVQUFVO0lBQ3pELE1BQU0sSUFBSSxVQUNSLENBQUMsZ0VBQWdFLEVBQUUsT0FBTyxXQUFXLENBQUM7RUFFMUY7QUFDRiJ9