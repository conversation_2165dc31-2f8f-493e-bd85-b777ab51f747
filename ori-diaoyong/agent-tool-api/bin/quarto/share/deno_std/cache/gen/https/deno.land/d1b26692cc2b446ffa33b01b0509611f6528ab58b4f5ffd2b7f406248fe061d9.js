// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Returns the first element having the smallest value according to the provided
 * comparator or undefined if there are no elements
 *
 * @example
 * ```ts
 * import { minWith } from "https://deno.land/std@$STD_VERSION/collections/min_with.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const people = ["<PERSON>", "<PERSON>", "<PERSON>"];
 * const smallestName = minWith(people, (a, b) => a.length - b.length);
 *
 * assertEquals(smallestName, "Kim");
 * ```
 */ export function minWith(array, comparator) {
  let min = undefined;
  let isFirst = true;
  for (const current of array){
    if (isFirst || comparator(current, min) < 0) {
      min = current;
      isFirst = false;
    }
  }
  return min;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL21pbl93aXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogUmV0dXJucyB0aGUgZmlyc3QgZWxlbWVudCBoYXZpbmcgdGhlIHNtYWxsZXN0IHZhbHVlIGFjY29yZGluZyB0byB0aGUgcHJvdmlkZWRcbiAqIGNvbXBhcmF0b3Igb3IgdW5kZWZpbmVkIGlmIHRoZXJlIGFyZSBubyBlbGVtZW50c1xuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgbWluV2l0aCB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL21pbl93aXRoLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IHBlb3BsZSA9IFtcIktpbVwiLCBcIkFubmFcIiwgXCJKb2huXCJdO1xuICogY29uc3Qgc21hbGxlc3ROYW1lID0gbWluV2l0aChwZW9wbGUsIChhLCBiKSA9PiBhLmxlbmd0aCAtIGIubGVuZ3RoKTtcbiAqXG4gKiBhc3NlcnRFcXVhbHMoc21hbGxlc3ROYW1lLCBcIktpbVwiKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gbWluV2l0aDxUPihcbiAgYXJyYXk6IEl0ZXJhYmxlPFQ+LFxuICBjb21wYXJhdG9yOiAoYTogVCwgYjogVCkgPT4gbnVtYmVyLFxuKTogVCB8IHVuZGVmaW5lZCB7XG4gIGxldCBtaW46IFQgfCB1bmRlZmluZWQgPSB1bmRlZmluZWQ7XG4gIGxldCBpc0ZpcnN0ID0gdHJ1ZTtcblxuICBmb3IgKGNvbnN0IGN1cnJlbnQgb2YgYXJyYXkpIHtcbiAgICBpZiAoaXNGaXJzdCB8fCBjb21wYXJhdG9yKGN1cnJlbnQsIDxUPiBtaW4pIDwgMCkge1xuICAgICAgbWluID0gY3VycmVudDtcbiAgICAgIGlzRmlyc3QgPSBmYWxzZTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSxxQ0FBcUM7QUFFckM7Ozs7Ozs7Ozs7Ozs7O0NBY0MsR0FDRCxPQUFPLFNBQVMsUUFDZCxLQUFrQixFQUNsQixVQUFrQztFQUVsQyxJQUFJLE1BQXFCO0VBQ3pCLElBQUksVUFBVTtFQUVkLEtBQUssTUFBTSxXQUFXLE1BQU87SUFDM0IsSUFBSSxXQUFXLFdBQVcsU0FBYSxPQUFPLEdBQUc7TUFDL0MsTUFBTTtNQUNOLFVBQVU7SUFDWjtFQUNGO0VBRUEsT0FBTztBQUNUIn0=