// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Builds N-tuples of elements from the given N arrays with matching indices,
 * stopping when the smallest array's end is reached.
 *
 * @template T the type of the tuples produced by this function.
 * @example
 * ```ts
 * import { zip } from "https://deno.land/std@$STD_VERSION/collections/zip.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const numbers = [1, 2, 3, 4];
 * const letters = ["a", "b", "c", "d"];
 * const pairs = zip(numbers, letters);
 *
 * assertEquals(
 *   pairs,
 *   [
 *     [1, "a"],
 *     [2, "b"],
 *     [3, "c"],
 *     [4, "d"],
 *   ],
 * );
 * ```
 */ import { minOf } from "./min_of.ts";
export function zip(...arrays) {
  const minLength = minOf(arrays, (it)=>it.length) ?? 0;
  const ret = new Array(minLength);
  for(let i = 0; i < minLength; i += 1){
    const arr = arrays.map((it)=>it[i]);
    ret[i] = arr;
  }
  return ret;
}
//# sourceMappingURL=data:application/json;base64,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