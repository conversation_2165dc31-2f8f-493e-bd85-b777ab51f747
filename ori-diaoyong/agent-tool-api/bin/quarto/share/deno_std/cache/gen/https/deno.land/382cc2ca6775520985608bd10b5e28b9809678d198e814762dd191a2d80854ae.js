// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
/**
 * Utilities for working with the
 * [Streams API](https://developer.mozilla.org/en-US/docs/Web/API/Streams_API).
 *
 * Includes buffering and conversion.
 *
 * @module
 */ export * from "./buffer.ts";
export * from "./byte_slice_stream.ts";
export * from "./copy.ts";
export * from "./delimiter_stream.ts";
export * from "./early_zip_readable_streams.ts";
export * from "./iterate_reader.ts";
export * from "./limited_bytes_transform_stream.ts";
export * from "./limited_transform_stream.ts";
export * from "./merge_readable_streams.ts";
export * from "./read_all.ts";
export * from "./readable_stream_from_reader.ts";
export * from "./reader_from_iterable.ts";
export * from "./reader_from_stream_reader.ts";
export * from "./text_delimiter_stream.ts";
export * from "./text_line_stream.ts";
export * from "./to_array_buffer.ts";
export * from "./to_blob.ts";
export * from "./to_json.ts";
export * from "./to_text.ts";
export * from "./to_transform_stream.ts";
export * from "./writable_stream_from_writer.ts";
export * from "./write_all.ts";
export * from "./writer_from_stream_writer.ts";
export * from "./zip_readable_streams.ts";
//# sourceMappingURL=data:application/json;base64,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