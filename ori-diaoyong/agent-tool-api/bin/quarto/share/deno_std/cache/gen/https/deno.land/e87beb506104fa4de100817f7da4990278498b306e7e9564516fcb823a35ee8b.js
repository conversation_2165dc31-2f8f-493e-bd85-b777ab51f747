// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { ANY, INVALID } from "./constants.ts";
import { isValidNumber, isValidString } from "./_shared.ts";
/**
 * Checks to see if value is a valid SemVer object. It does a check
 * into each field including prerelease and build.
 *
 * Some invalid SemVer sentinels can still return true such as ANY and INVALID.
 * An object which has the same value as a sentinel but isn't reference equal
 * will still fail.
 *
 * Objects which are valid SemVer objects but have _extra_ fields are still
 * considered SemVer objects and this will return true.
 *
 * A type assertion is added to the value.
 * @param value The value to check to see if its a valid SemVer object
 * @returns True if value is a valid SemVer otherwise false
 */ export function isSemVer(value) {
  if (value === null || value === undefined) return false;
  if (Array.isArray(value)) return false;
  if (typeof value !== "object") return false;
  if (value === INVALID) return true;
  if (value === ANY) return true;
  const { major, minor, patch, build, prerelease } = value;
  const result = typeof major === "number" && isValidNumber(major) && typeof minor === "number" && isValidNumber(minor) && typeof patch === "number" && isValidNumber(patch) && Array.isArray(prerelease) && Array.isArray(build) && prerelease.every((v)=>typeof v === "string" || typeof v === "number") && prerelease.filter((v)=>typeof v === "string").every((v)=>isValidString(v)) && prerelease.filter((v)=>typeof v === "number").every((v)=>isValidNumber(v)) && build.every((v)=>typeof v === "string" && isValidString(v));
  return result;
}
//# sourceMappingURL=data:application/json;base64,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