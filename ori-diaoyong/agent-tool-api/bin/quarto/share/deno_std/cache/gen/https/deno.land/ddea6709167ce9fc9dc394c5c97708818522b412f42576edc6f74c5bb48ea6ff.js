// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.
/**
 * Applies the given selector to elements in the given array until a value is
 * produced that is neither `null` nor `undefined` and returns that value.
 * Returns `undefined` if no such value is produced.
 *
 * @example
 * ```ts
 * import { firstNotNullishOf } from "https://deno.land/std@$STD_VERSION/collections/first_not_nullish_of.ts";
 * import { assertEquals } from "https://deno.land/std@$STD_VERSION/assert/assert_equals.ts";
 *
 * const tables = [
 *   { number: 11, order: null },
 *   { number: 12, order: "Soup" },
 *   { number: 13, order: "Salad" },
 * ];
 * const nextOrder = firstNotNullishOf(tables, (it) => it.order);
 *
 * assertEquals(nextOrder, "Soup");
 * ```
 */ export function firstNotNullishOf(array, selector) {
  for (const current of array){
    const selected = selector(current);
    if (selected !== null && selected !== undefined) {
      return selected;
    }
  }
  return undefined;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL2NvbGxlY3Rpb25zL2ZpcnN0X25vdF9udWxsaXNoX29mLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDE4LTIwMjMgdGhlIERlbm8gYXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4gTUlUIGxpY2Vuc2UuXG4vLyBUaGlzIG1vZHVsZSBpcyBicm93c2VyIGNvbXBhdGlibGUuXG5cbi8qKlxuICogQXBwbGllcyB0aGUgZ2l2ZW4gc2VsZWN0b3IgdG8gZWxlbWVudHMgaW4gdGhlIGdpdmVuIGFycmF5IHVudGlsIGEgdmFsdWUgaXNcbiAqIHByb2R1Y2VkIHRoYXQgaXMgbmVpdGhlciBgbnVsbGAgbm9yIGB1bmRlZmluZWRgIGFuZCByZXR1cm5zIHRoYXQgdmFsdWUuXG4gKiBSZXR1cm5zIGB1bmRlZmluZWRgIGlmIG5vIHN1Y2ggdmFsdWUgaXMgcHJvZHVjZWQuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyBmaXJzdE5vdE51bGxpc2hPZiB9IGZyb20gXCJodHRwczovL2Rlbm8ubGFuZC9zdGRAJFNURF9WRVJTSU9OL2NvbGxlY3Rpb25zL2ZpcnN0X25vdF9udWxsaXNoX29mLnRzXCI7XG4gKiBpbXBvcnQgeyBhc3NlcnRFcXVhbHMgfSBmcm9tIFwiaHR0cHM6Ly9kZW5vLmxhbmQvc3RkQCRTVERfVkVSU0lPTi9hc3NlcnQvYXNzZXJ0X2VxdWFscy50c1wiO1xuICpcbiAqIGNvbnN0IHRhYmxlcyA9IFtcbiAqICAgeyBudW1iZXI6IDExLCBvcmRlcjogbnVsbCB9LFxuICogICB7IG51bWJlcjogMTIsIG9yZGVyOiBcIlNvdXBcIiB9LFxuICogICB7IG51bWJlcjogMTMsIG9yZGVyOiBcIlNhbGFkXCIgfSxcbiAqIF07XG4gKiBjb25zdCBuZXh0T3JkZXIgPSBmaXJzdE5vdE51bGxpc2hPZih0YWJsZXMsIChpdCkgPT4gaXQub3JkZXIpO1xuICpcbiAqIGFzc2VydEVxdWFscyhuZXh0T3JkZXIsIFwiU291cFwiKTtcbiAqIGBgYFxuICovXG5leHBvcnQgZnVuY3Rpb24gZmlyc3ROb3ROdWxsaXNoT2Y8VCwgTz4oXG4gIGFycmF5OiBJdGVyYWJsZTxUPixcbiAgc2VsZWN0b3I6IChpdGVtOiBUKSA9PiBPIHwgdW5kZWZpbmVkIHwgbnVsbCxcbik6IE5vbk51bGxhYmxlPE8+IHwgdW5kZWZpbmVkIHtcbiAgZm9yIChjb25zdCBjdXJyZW50IG9mIGFycmF5KSB7XG4gICAgY29uc3Qgc2VsZWN0ZWQgPSBzZWxlY3RvcihjdXJyZW50KTtcblxuICAgIGlmIChzZWxlY3RlZCAhPT0gbnVsbCAmJiBzZWxlY3RlZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gc2VsZWN0ZWQgYXMgTm9uTnVsbGFibGU8Tz47XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHVuZGVmaW5lZDtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUscUNBQXFDO0FBRXJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBbUJDLEdBQ0QsT0FBTyxTQUFTLGtCQUNkLEtBQWtCLEVBQ2xCLFFBQTJDO0VBRTNDLEtBQUssTUFBTSxXQUFXLE1BQU87SUFDM0IsTUFBTSxXQUFXLFNBQVM7SUFFMUIsSUFBSSxhQUFhLFFBQVEsYUFBYSxXQUFXO01BQy9DLE9BQU87SUFDVDtFQUNGO0VBRUEsT0FBTztBQUNUIn0=