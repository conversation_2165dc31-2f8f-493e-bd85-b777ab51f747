// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
import { compare } from "./compare.ts";
/** Returns `true` if they're logically equivalent, even if they're not the exact
 * same version object. */ export function eq(s0, s1) {
  return compare(s0, s1) === 0;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImh0dHBzOi8vZGVuby5sYW5kL3N0ZEAwLjIwNC4wL3NlbXZlci9lcS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAxOC0yMDIzIHRoZSBEZW5vIGF1dGhvcnMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHsgY29tcGFyZSB9IGZyb20gXCIuL2NvbXBhcmUudHNcIjtcbmltcG9ydCB0eXBlIHsgU2VtVmVyIH0gZnJvbSBcIi4vdHlwZXMudHNcIjtcblxuLyoqIFJldHVybnMgYHRydWVgIGlmIHRoZXkncmUgbG9naWNhbGx5IGVxdWl2YWxlbnQsIGV2ZW4gaWYgdGhleSdyZSBub3QgdGhlIGV4YWN0XG4gKiBzYW1lIHZlcnNpb24gb2JqZWN0LiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVxKHMwOiBTZW1WZXIsIHMxOiBTZW1WZXIpOiBib29sZWFuIHtcbiAgcmV0dXJuIGNvbXBhcmUoczAsIHMxKSA9PT0gMDtcbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7QUFDMUUsU0FBUyxPQUFPLFFBQVEsZUFBZTtBQUd2Qzt3QkFDd0IsR0FDeEIsT0FBTyxTQUFTLEdBQUcsRUFBVSxFQUFFLEVBQVU7RUFDdkMsT0FBTyxRQUFRLElBQUksUUFBUTtBQUM3QiJ9