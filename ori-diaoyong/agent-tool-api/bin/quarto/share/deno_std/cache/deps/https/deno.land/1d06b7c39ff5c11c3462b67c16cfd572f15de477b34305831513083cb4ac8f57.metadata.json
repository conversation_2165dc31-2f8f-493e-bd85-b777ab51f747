{"headers": {"x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "age": "656263", "x-frame-options": "DENY", "date": "Wed, 24 Apr 2024 07:24:08 GMT", "via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "access-control-allow-origin": "*", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "x-content-type-options": "nosniff", "x-amz-version-id": "PyJMK6F6BMRZYBrwiP2xwWKzPlXmz1Ql", "cross-origin-opener-policy": "same-origin", "x-cache": "Hit from cloudfront", "x-amz-cf-id": "3O2qG4220LfyyR1WVSx1BSnbYRSEtlb5fK6hsEC8KgbaevtN6tpt_A==", "cache-control": "public, max-age=31536000, immutable", "etag": "\"59b779dafbaf4ca6cdcb1d6098300f4d\"", "server": "deno/gcp-us-east4", "content-length": "842", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=168", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/yaml/_type/mod.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 812670201}}