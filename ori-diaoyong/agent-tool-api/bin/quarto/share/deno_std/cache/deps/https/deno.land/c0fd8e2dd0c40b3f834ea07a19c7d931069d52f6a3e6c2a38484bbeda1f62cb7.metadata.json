{"headers": {"cross-origin-opener-policy": "same-origin", "cross-origin-embedder-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "age": "657311", "x-amz-cf-id": "4JsJMeO31o8SCiIqUTkjmGQvIbJeNdNgLIJAJN3gpegCk0PyDVYnrg==", "accept-ranges": "bytes", "x-amz-cf-pop": "IAD61-P2", "date": "Wed, 24 Apr 2024 07:06:38 GMT", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-version-id": "nBsxZMy_ZHqMXIB3SpAKfRPk0myLenDE", "x-cache": "Hit from cloudfront", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=16", "x-frame-options": "DENY", "content-length": "1373", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cache-control": "public, max-age=31536000, immutable", "x-content-type-options": "nosniff", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "etag": "\"aa18084f20fa793ccd91d14628bbc810\""}, "url": "https://deno.land/std@0.204.0/bytes/copy.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 471818289}}