{"headers": {"age": "639986", "cache-control": "public, max-age=31536000, immutable", "cross-origin-embedder-policy": "same-origin", "accept-ranges": "bytes", "access-control-allow-origin": "*", "cross-origin-opener-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "content-length": "1637", "cross-origin-resource-policy": "same-origin", "date": "Wed, 24 Apr 2024 11:55:23 GMT", "server-timing": "fetchSource;dur=132", "vary": "Accept-Encoding, Origin", "etag": "\"519fdd7a52ccd546120d38580b4699dc\"", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "x-amz-cf-id": "zCQJn-drzxeo8LjpGd-2UjGK4kyvaycTcufkjc758zIcXX6EFP1xJw==", "x-amz-cf-pop": "IAD61-P2", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "DENY", "via": "http/2 edgeproxy-h", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "Tulha65anzZzjjqULV3runD8_dfKl2aT", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "server": "deno/gcp-us-east4", "x-amz-replication-status": "COMPLETED", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/log/levels.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 817053300}}