{"headers": {"cross-origin-resource-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "x-amz-cf-id": "ZjtPOnOFJbuxTX8TAy-zWVxYGQqBTpdUcu8QrwBATMc8gOQFI53c0A==", "server-timing": "fetchSource;dur=12", "content-length": "1547", "content-type": "application/typescript; charset=utf-8", "x-cache": "Hit from cloudfront", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "date": "Wed, 24 Apr 2024 06:53:25 GMT", "etag": "\"925b4ad3e318d2d8dabd24859d23066d\"", "cross-origin-embedder-policy": "same-origin", "accept-ranges": "bytes", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "access-control-allow-origin": "*", "x-frame-options": "DENY", "vary": "Accept-Encoding, Origin", "cache-control": "public, max-age=31536000, immutable", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "4NJlOABNKxIR1anyGlc6N2fRIUSu74vy", "x-content-type-options": "nosniff", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-opener-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "age": "658104"}, "url": "https://deno.land/std@0.204.0/async/deferred.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 637217674}}