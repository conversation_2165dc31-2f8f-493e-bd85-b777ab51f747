{"headers": {"cross-origin-opener-policy": "same-origin", "etag": "\"e9fcdc8b2473391e14bcb3537ebf816b\"", "via": "http/2 edgeproxy-h", "x-amz-cf-id": "0vYnNaPLDvyW1obj3_mHolwmsELznzYFVXi978dhLCb6_5TiqNzu0w==", "content-length": "2495", "server": "deno/gcp-us-east4", "x-amz-server-side-encryption": "AES256", "date": "Wed, 24 Apr 2024 06:29:58 GMT", "x-amz-version-id": "3gL2YFuNZnkWnJJQovFbFiZHyW8ZqUXd", "age": "659512", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-cf-pop": "IAD61-P2", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "accept-ranges": "bytes", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "vary": "Accept-Encoding, Origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-cache": "Hit from cloudfront", "cross-origin-resource-policy": "same-origin", "server-timing": "fetchSource;dur=12", "x-content-type-options": "nosniff", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "cross-origin-embedder-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "cache-control": "public, max-age=31536000, immutable"}, "url": "https://deno.land/std@0.204.0/fs/_util.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 986801031}}