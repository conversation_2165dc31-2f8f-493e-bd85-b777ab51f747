{"headers": {"x-frame-options": "DENY", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "accept-ranges": "bytes", "etag": "\"b719cbef8005ce88dd4d8cd9b55e5cfd\"", "content-length": "498", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "SYP6MHb9Xr1bGh5MSe5t-5jCKrC-MCjl0jl8ENz1LSztzr2DPheleg==", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "x-amz-version-id": "w7KUlpRDONOi5kcKYF88JpjI1McPYvWG", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-cache": "Hit from cloudfront", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "cross-origin-opener-policy": "same-origin", "server": "deno/gcp-us-east4", "vary": "Accept-Encoding, Origin", "referrer-policy": "strict-origin-when-cross-origin", "x-content-type-options": "nosniff", "age": "659195", "x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "server-timing": "fetchSource;dur=238"}, "url": "https://deno.land/std@0.204.0/path/dirname.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 978400721}}