{"headers": {"x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "x-frame-options": "DENY", "x-amz-cf-id": "eJH6ceOyqBmSoXX1McPsv4scIuAU9yn5ekZ8OECQJGiBTWbAgbAIPA==", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "date": "Wed, 24 Apr 2024 06:36:38 GMT", "x-amz-version-id": "0.ihpXqY4Mh9lidwTZmJZBLT_xrIZRzi", "x-amz-cf-pop": "IAD61-P2", "server": "deno/gcp-us-east4", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "content-type": "application/typescript; charset=utf-8", "x-content-type-options": "nosniff", "etag": "\"eb37210a2c8bb730b8076e203a31a8fe\"", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "age": "659111", "server-timing": "fetchSource;dur=312", "accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "content-length": "1413", "cache-control": "public, max-age=31536000, immutable", "access-control-allow-origin": "*", "cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "cross-origin-opener-policy": "same-origin", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "referrer-policy": "strict-origin-when-cross-origin"}, "url": "https://deno.land/std@0.204.0/path/posix/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 977826488}}