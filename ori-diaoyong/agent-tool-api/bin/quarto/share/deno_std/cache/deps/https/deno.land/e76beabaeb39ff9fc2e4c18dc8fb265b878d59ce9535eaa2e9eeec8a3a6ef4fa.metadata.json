{"headers": {"server-timing": "fetchSource;dur=84", "etag": "\"42abd8a628ecac7b2bd27a747b549c7b\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "accept-ranges": "bytes", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "x-amz-server-side-encryption": "AES256", "date": "Wed, 24 Apr 2024 06:53:29 GMT", "content-length": "6958", "cache-control": "public, max-age=31536000, immutable", "cross-origin-opener-policy": "same-origin", "cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-cf-id": "qYLQbYWegGl7bfeLoKrwPEIyyrL6b_xtFfBtFYO4RiFRL1kSintIEg==", "x-amz-cf-pop": "IAD61-P2", "server": "deno/gcp-us-east4", "x-amz-replication-status": "COMPLETED", "age": "658100", "referrer-policy": "strict-origin-when-cross-origin", "access-control-allow-origin": "*", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-cache": "Hit from cloudfront", "x-amz-version-id": "ycG3ue2wwGj6vctjugNrWudx2A7T6Abu", "x-content-type-options": "nosniff", "cross-origin-resource-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/io/buf_writer.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 27955970}}