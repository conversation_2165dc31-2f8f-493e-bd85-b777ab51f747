{"headers": {"server-timing": "fetchSource;dur=22", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "access-control-allow-origin": "*", "x-amz-cf-id": "P2KQg-xvx_Gte_hC4KhN1YFfNbKbzIP_KbTSz4CeHz4o4pSKCTtljg==", "x-amz-version-id": "PHjbqREI3I1PxVjOo6NiIEllH3MZxXdC", "accept-ranges": "bytes", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "age": "659983", "x-amz-cf-pop": "IAD61-P2", "vary": "Accept-Encoding, Origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-cache": "Hit from cloudfront", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "etag": "\"4845feb1852930a2db1359976efe35b6\"", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "server": "deno/gcp-us-east4", "cross-origin-opener-policy": "same-origin", "content-length": "3245", "cross-origin-resource-policy": "same-origin", "via": "http/2 edgeproxy-h", "x-content-type-options": "nosniff", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cache-control": "public, max-age=31536000, immutable"}, "url": "https://deno.land/std@0.204.0/yaml/_type/binary.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 951397943}}