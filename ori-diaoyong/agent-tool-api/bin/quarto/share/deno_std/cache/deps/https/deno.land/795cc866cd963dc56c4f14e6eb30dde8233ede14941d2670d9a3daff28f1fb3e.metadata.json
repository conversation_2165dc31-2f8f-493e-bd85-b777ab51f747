{"headers": {"x-amz-version-id": "hc7Tf74WHDDON_xwwHGg_zAKnzMasOhP", "etag": "\"877c6d46b087f79c9fa63087e8c1198d\"", "x-frame-options": "DENY", "x-cache": "Hit from cloudfront", "server-timing": "fetchSource;dur=170", "x-content-type-options": "nosniff", "x-amz-cf-id": "0B1C-YhjCVLio_yrMyaVykzAGbGxJ5UFm3hsQWPGDu5T4MM7OVcq6A==", "content-length": "3158", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED", "accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "age": "659983", "cross-origin-opener-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "cross-origin-embedder-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "server": "deno/gcp-us-east4", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "vary": "Accept-Encoding, Origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/yaml/schema.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 813481568}}