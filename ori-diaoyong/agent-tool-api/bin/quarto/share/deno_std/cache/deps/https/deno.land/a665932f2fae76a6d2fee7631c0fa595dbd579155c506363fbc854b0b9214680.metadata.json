{"headers": {"age": "659197", "accept-ranges": "bytes", "x-frame-options": "DENY", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-resource-policy": "same-origin", "x-amz-cf-id": "VfTqm_Ulgt_nWcHc-eWjKBe5wQnGlnEpXhdmGYoOCKeCeE_eY6Cdhg==", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "x-cache": "Hit from cloudfront", "cross-origin-embedder-policy": "same-origin", "server-timing": "fetchSource;dur=28", "content-type": "application/typescript; charset=utf-8", "x-amz-cf-pop": "IAD61-P2", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "cache-control": "public, max-age=31536000, immutable", "x-amz-version-id": "WXhwu7.SLuCzzG8fyTrQQlMO89_lxbgP", "etag": "\"91ebd4a95be5bbaa3936f127bd135321\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "server": "deno/gcp-us-east4", "x-amz-server-side-encryption": "AES256", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "content-length": "306", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "cross-origin-opener-policy": "same-origin", "access-control-allow-origin": "*"}, "url": "https://deno.land/std@0.204.0/path/_common/assert_path.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 765614383}}