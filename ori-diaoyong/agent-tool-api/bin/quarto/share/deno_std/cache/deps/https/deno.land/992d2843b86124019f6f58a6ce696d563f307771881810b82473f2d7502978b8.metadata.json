{"headers": {"referrer-policy": "strict-origin-when-cross-origin", "date": "Wed, 24 Apr 2024 07:12:20 GMT", "content-type": "application/javascript", "cross-origin-resource-policy": "same-origin", "access-control-allow-origin": "*", "etag": "W/\"8110a6cb8296bef463688b9f0269113f\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "server": "deno/gcp-us-east4", "x-amz-version-id": "w1XyswxlGLywujqmZvT4WAYgUQjOods0", "x-amz-cf-pop": "IAD61-P2", "age": "656971", "server-timing": "fetchSource;dur=46", "cross-origin-opener-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cache-control": "public, max-age=31536000, immutable", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "x-amz-server-side-encryption": "AES256", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "vary": "Accept-Encoding, Origin", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "x-cache": "Hit from cloudfront", "x-amz-cf-id": "WLa3jYYQk2rJsOBwqiusq-FUQyNv6sAoAWXbOCwEPFlOoJWl3P5rgQ==", "via": "http/2 edgeproxy-h"}, "url": "https://deno.land/std@0.204.0/crypto/_wasm/lib/deno_std_wasm_crypto.generated.mjs", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 713654418}}