{"headers": {"x-amz-server-side-encryption": "AES256", "x-amz-version-id": "eK_dRp5A_d_ErvvOjYnVza9OBj3grGeW", "x-amz-replication-status": "COMPLETED", "content-type": "application/typescript; charset=utf-8", "content-length": "1613", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "x-cache": "Hit from cloudfront", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "server-timing": "fetchSource;dur=126", "etag": "\"6d3362c47d47bef5bfc6e5891869f5ef\"", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "a87cmVxxwYVNTIYM0PU8OOtp7N4Byn1RpaFa4o8B7Kl2q8rZkvIREQ==", "cache-control": "public, max-age=31536000, immutable", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "accept-ranges": "bytes", "date": "Wed, 01 May 2024 21:41:49 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "server": "deno/gcp-us-east4", "vary": "Accept-Encoding, Origin", "x-amz-cf-pop": "IAD61-P2", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "via": "http/2 edgeproxy-h"}, "url": "https://deno.land/std@0.204.0/front_matter/_formats.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 819582269}}