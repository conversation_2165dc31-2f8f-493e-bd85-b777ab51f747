{"headers": {"cross-origin-embedder-policy": "same-origin", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "etag": "\"5de2f26a266ef4b5e62f52f002e311ee\"", "content-length": "1292", "vary": "Accept-Encoding, Origin", "x-amz-cf-id": "BQFDHImNnQ1V3hF742Zj4HUr88POtmKUQB5hGKl72yA1FbRShPfU_Q==", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "server": "deno/gcp-us-east4", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-resource-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "OyWxrqPoRYSm8.Vo8ymixdLm9IshAyRR", "x-cache": "Hit from cloudfront", "cache-control": "public, max-age=31536000, immutable", "referrer-policy": "strict-origin-when-cross-origin", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "via": "http/2 edgeproxy-h", "access-control-allow-origin": "*", "server-timing": "fetchSource;dur=38", "x-content-type-options": "nosniff", "x-amz-server-side-encryption": "AES256", "age": "659983"}, "url": "https://deno.land/std@0.204.0/yaml/_type/pairs.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 970844150}}