{"headers": {"vary": "Accept-Encoding, Origin", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "lRlIkf9tgEZf_4B8hhLM_O8XdgmnyNrGEuDDG1aXROA1Glfldbe3cA==", "x-amz-replication-status": "COMPLETED", "age": "658100", "x-amz-version-id": "adQj0dVOXjW.FlHn2bQ_hWyi8sRlQHP2", "accept-ranges": "bytes", "etag": "\"c2da24bff40c2510d65371cd2414291a\"", "access-control-allow-origin": "*", "server-timing": "fetchSource;dur=84", "date": "Wed, 24 Apr 2024 06:53:29 GMT", "referrer-policy": "strict-origin-when-cross-origin", "content-type": "application/typescript; charset=utf-8", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "via": "http/2 edgeproxy-h", "content-length": "584", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "cross-origin-embedder-policy": "same-origin", "server": "deno/gcp-us-east4", "cross-origin-resource-policy": "same-origin", "x-amz-server-side-encryption": "AES256", "x-frame-options": "DENY"}, "url": "https://deno.land/std@0.204.0/io/read_int.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 28439444}}