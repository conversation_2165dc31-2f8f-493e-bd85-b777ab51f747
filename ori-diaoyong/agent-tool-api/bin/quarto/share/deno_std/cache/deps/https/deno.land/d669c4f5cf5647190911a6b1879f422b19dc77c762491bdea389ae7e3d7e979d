// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.

import { isWindows } from "./_os.ts";
import { fromFileUrl as posixFromFileUrl } from "./posix/from_file_url.ts";
import { fromFileUrl as windowsFromFileUrl } from "./windows/from_file_url.ts";

/**
 * Converts a file URL to a path string.
 *
 * ```ts
 * import { fromFileUrl } from "https://deno.land/std@$STD_VERSION/path/from_file_url.ts";
 *
 * // posix
 * fromFileUrl("file:///home/<USER>"); // "/home/<USER>"
 *
 * // win32
 * fromFileUrl("file:///home/<USER>"); // "\\home\\foo"
 * fromFileUrl("file:///C:/Users/<USER>"); // "C:\\Users\\<USER>\\\\localhost\\home\\foo"
 * ```
 * @param url of a file URL
 */
export function fromFileUrl(url: string | URL): string {
  return isWindows ? windowsFromFileUrl(url) : posixFromFileUrl(url);
}
