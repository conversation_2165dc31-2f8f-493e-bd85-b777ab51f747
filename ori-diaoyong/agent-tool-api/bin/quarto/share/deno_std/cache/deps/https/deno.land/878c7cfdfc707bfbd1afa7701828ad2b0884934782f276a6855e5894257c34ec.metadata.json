{"headers": {"cross-origin-embedder-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "server": "deno/gcp-us-east4", "date": "Wed, 01 May 2024 21:41:49 GMT", "vary": "Accept-Encoding, Origin", "content-type": "application/typescript; charset=utf-8", "content-length": "3151", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "x-amz-cf-pop": "IAD61-P2", "accept-ranges": "bytes", "cross-origin-opener-policy": "same-origin", "x-amz-cf-id": "_hMdTjSJ66CnKNy4QLFpJdVB5MXZ_-nyuuqPIWYiKXmbIlBHYDP6MA==", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "cross-origin-resource-policy": "same-origin", "server-timing": "fetchSource;dur=96", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-amz-version-id": "AlUu8qcpRASl9woIkWxgwHVRueFyXyKp", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cache-control": "public, max-age=31536000, immutable", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "access-control-allow-origin": "*", "etag": "\"1f82dea94c142dffc27e12da9fe663a3\""}, "url": "https://deno.land/std@0.204.0/front_matter/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 144777486}}