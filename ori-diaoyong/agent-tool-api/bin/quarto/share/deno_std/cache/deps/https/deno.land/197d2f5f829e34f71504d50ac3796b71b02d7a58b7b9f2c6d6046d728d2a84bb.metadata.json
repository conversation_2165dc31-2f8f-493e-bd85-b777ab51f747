{"headers": {"etag": "\"2179535ee2389a8d7ca3aab71644a79a\"", "referrer-policy": "strict-origin-when-cross-origin", "age": "659196", "via": "http/2 edgeproxy-h", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "x-amz-version-id": "X7e.spcSzITZVsJC.Ai9LJVDrbjAjOEd", "server": "deno/gcp-us-east4", "x-amz-cf-id": "J9oQ2yOO42z7OoHUJsyxNMwBkEJ3m1gUTZiuor_-dY52z9PbZIGRiw==", "cross-origin-opener-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "content-length": "682", "content-type": "application/typescript; charset=utf-8", "server-timing": "fetchSource;dur=14", "accept-ranges": "bytes", "x-frame-options": "DENY", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "x-amz-replication-status": "COMPLETED", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin", "cross-origin-embedder-policy": "same-origin", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/path/posix/common.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 961783087}}