{"headers": {"cache-control": "public, max-age=31536000, immutable", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-version-id": "x5viJE78u_DrkNqf7VC8U_xArKFMpsQP", "accept-ranges": "bytes", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "x-amz-server-side-encryption": "AES256", "age": "659981", "etag": "\"dd235ebe69a889efdc15e21e761335d6\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cross-origin-opener-policy": "same-origin", "x-frame-options": "DENY", "server": "deno/gcp-us-east4", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-embedder-policy": "same-origin", "server-timing": "fetchSource;dur=242", "content-length": "1673", "cross-origin-resource-policy": "same-origin", "vary": "Accept-Encoding, Origin", "x-amz-cf-id": "wfBXBX20cUq3PdzjmAoUKmqFLld1CIYwHgF8ekNwAIBsnH3an_KoMg==", "x-amz-cf-pop": "IAD61-P2", "content-type": "application/typescript; charset=utf-8", "x-content-type-options": "nosniff", "x-cache": "Hit from cloudfront"}, "url": "https://deno.land/std@0.204.0/yaml/parse.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 980155121}}