{"headers": {"x-amz-version-id": "NEtiW.hmr7JJDKqMj8xc3gtUTkqtLSdn", "x-cache": "Hit from cloudfront", "age": "659197", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "server": "deno/gcp-us-east4", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "access-control-allow-origin": "*", "accept-ranges": "bytes", "x-amz-cf-pop": "IAD61-P2", "cross-origin-embedder-policy": "same-origin", "x-frame-options": "DENY", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cache-control": "public, max-age=31536000, immutable", "content-length": "214", "cross-origin-opener-policy": "same-origin", "x-amz-cf-id": "2kJhYVIQTNjGsnEnMl1W-saKgDSezxFbXeWfr-nWDZlNszI2-7NHQw==", "x-amz-replication-status": "COMPLETED", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "etag": "\"f42ce02a5dad40ee1131a9b6c28e7e48\"", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "content-type": "application/typescript; charset=utf-8", "server-timing": "fetchSource;dur=60", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/assert/assertion_error.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 495584008}}