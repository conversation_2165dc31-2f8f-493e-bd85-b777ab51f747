{"headers": {"content-length": "677", "etag": "\"a2769edc12acd7b4b03cf26bcc2669c0\"", "x-amz-cf-pop": "IAD61-P2", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "access-control-allow-origin": "*", "x-amz-replication-status": "COMPLETED", "age": "659195", "x-amz-version-id": "3c8um7PnPcpLGppJ9Em1MVzumXN0X3Ej", "server-timing": "fetchSource;dur=298", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "x-amz-server-side-encryption": "AES256", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "cache-control": "public, max-age=31536000, immutable", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-cf-id": "htdXia1T1BQsYE6J8cbisQoTxp6644Vjs90_YS7-wtA7x6hU9xHbUQ==", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/path/basename.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 978537487}}