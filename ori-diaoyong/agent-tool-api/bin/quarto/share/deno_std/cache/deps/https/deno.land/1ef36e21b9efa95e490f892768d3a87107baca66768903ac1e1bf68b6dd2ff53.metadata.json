{"headers": {"vary": "Accept-Encoding, Origin", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "via": "http/2 edgeproxy-h", "cross-origin-embedder-policy": "same-origin", "age": "659983", "x-amz-replication-status": "COMPLETED", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-amz-server-side-encryption": "AES256", "etag": "\"f495228d688dd204a3eb63769aba9806\"", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-version-id": "********************************", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "server-timing": "fetchSource;dur=26", "x-amz-cf-id": "BdW3nJfP_9vOlBMC172yaVwK5xa6sqvCTvlw2TEwD8qKxhq8rHdN8A==", "x-cache": "Hit from cloudfront", "cache-control": "public, max-age=31536000, immutable", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "cross-origin-resource-policy": "same-origin", "content-length": "4419", "cross-origin-opener-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "accept-ranges": "bytes", "server": "deno/gcp-us-east4"}, "url": "https://deno.land/std@0.204.0/yaml/_type/int.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 950429502}}