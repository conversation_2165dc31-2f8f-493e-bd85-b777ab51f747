{"headers": {"x-amz-server-side-encryption": "AES256", "content-type": "application/typescript; charset=utf-8", "date": "Wed, 01 May 2024 21:41:49 GMT", "etag": "\"da7be405462617edc568c17dd8e5f5be\"", "server": "deno/gcp-us-east4", "content-length": "1117", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "IrDDcW8w6Xz6Vptmhos4w77GPZrTC415ymJ8LAb6Cux1D78-al8AZA==", "accept-ranges": "bytes", "cache-control": "public, max-age=31536000, immutable", "cross-origin-resource-policy": "same-origin", "via": "http/2 edgeproxy-h", "x-amz-version-id": "CB4CT5s4xA2D0nRxuT9NTfWu0H7zR60x", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "DENY", "cross-origin-opener-policy": "same-origin", "cross-origin-embedder-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "access-control-allow-origin": "*", "x-amz-cf-pop": "IAD61-P2", "server-timing": "fetchSource;dur=22", "x-amz-replication-status": "COMPLETED", "vary": "Accept-Encoding, Origin", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff"}, "url": "https://deno.land/std@0.204.0/collections/map_values.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 588125961}}