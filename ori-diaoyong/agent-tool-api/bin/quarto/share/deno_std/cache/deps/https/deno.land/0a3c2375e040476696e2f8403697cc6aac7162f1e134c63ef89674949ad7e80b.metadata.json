{"headers": {"cross-origin-embedder-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=40", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "accept-ranges": "bytes", "server": "deno/gcp-us-east4", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "x-frame-options": "DENY", "etag": "\"84c6b169a1a30cdfff4d4264f01b7ade\"", "x-amz-cf-pop": "IAD61-P2", "access-control-allow-origin": "*", "age": "659983", "x-amz-cf-id": "GttLvxoPLIDm3prdEFra7UpbrDNg1InmUrFZ3YxdzvKdFkEWumoQRg==", "x-amz-replication-status": "COMPLETED", "via": "http/2 edgeproxy-h", "x-amz-version-id": "7t3ZAU535H6GKbzphwkdU7r4mJTvm55T", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "cross-origin-resource-policy": "same-origin", "vary": "Accept-Encoding, Origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-type": "application/typescript; charset=utf-8", "x-amz-server-side-encryption": "AES256", "content-length": "1067", "cache-control": "public, max-age=31536000, immutable", "cross-origin-opener-policy": "same-origin"}, "url": "https://deno.land/std@0.204.0/yaml/_type/nil.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 971017514}}