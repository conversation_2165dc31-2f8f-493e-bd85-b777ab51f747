{"headers": {"date": "Wed, 01 May 2024 21:41:49 GMT", "x-content-type-options": "nosniff", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "yS6RFkKDleLPMdG4Xg3BVdo5ob90xuMBAm_dPrXDXBnyyZv6PPPmmw==", "x-amz-version-id": "JTBWMu__ll7ftJmk3DLGm9sG2mTgDdLX", "cross-origin-resource-policy": "same-origin", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "server-timing": "fetchSource;dur=172", "x-frame-options": "DENY", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-cache": "Miss from cloudfront", "cross-origin-embedder-policy": "same-origin", "etag": "\"fcf86c2e24885c556bd15ce2f8dafadd\"", "content-length": "1185", "content-type": "application/typescript; charset=utf-8", "x-amz-server-side-encryption": "AES256", "cache-control": "public, max-age=31536000, immutable", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload"}, "url": "https://deno.land/std@0.204.0/collections/max_with.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 570631944}}