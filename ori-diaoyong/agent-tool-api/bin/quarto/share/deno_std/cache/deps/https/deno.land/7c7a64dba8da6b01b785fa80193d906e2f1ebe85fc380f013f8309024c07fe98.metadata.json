{"headers": {"cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "access-control-allow-origin": "*", "x-amz-cf-id": "on9HsWdI-AKpIukn1_GmFxVaojVkYZ8mS0nMwgBQt50_JEc19HLqGw==", "x-cache": "Hit from cloudfront", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-version-id": "i3jOGhfJ8E8kakIjUJ83.h2cfYGePFSp", "date": "Wed, 24 Apr 2024 06:53:24 GMT", "server-timing": "fetchSource;dur=26", "vary": "Accept-Encoding, Origin", "cache-control": "public, max-age=31536000, immutable", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "age": "658105", "referrer-policy": "strict-origin-when-cross-origin", "server": "deno/gcp-us-east4", "x-frame-options": "DENY", "via": "http/2 edgeproxy-h", "etag": "\"ee4dbec3fecea79b4f38ebc22c6f6b91\"", "x-content-type-options": "nosniff", "content-length": "22641", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "cross-origin-embedder-policy": "same-origin", "x-amz-replication-status": "COMPLETED", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "content-type": "application/typescript; charset=utf-8", "x-amz-server-side-encryption": "AES256"}, "url": "https://deno.land/std@0.204.0/flags/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 91740958}}