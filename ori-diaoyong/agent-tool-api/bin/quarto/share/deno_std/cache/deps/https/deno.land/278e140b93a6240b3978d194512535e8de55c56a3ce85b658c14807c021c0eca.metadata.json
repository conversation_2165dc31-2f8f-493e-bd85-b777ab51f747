{"headers": {"via": "http/2 edgeproxy-h", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "date": "Wed, 24 Apr 2024 06:36:38 GMT", "cross-origin-resource-policy": "same-origin", "age": "659111", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "access-control-allow-origin": "*", "referrer-policy": "strict-origin-when-cross-origin", "server": "deno/gcp-us-east4", "etag": "\"623d0f3f9b8b7e9e6421b6da609b35df\"", "x-amz-cf-id": "2WNZgTVnQtiBie1sfJiieXJOvKQOg_TzQVvV3GiFLDjyhEDvpaDHmA==", "server-timing": "fetchSource;dur=314", "x-amz-version-id": "oDTvyb7U2QGNNvPRM88x48_x7FmShHcn", "x-content-type-options": "nosniff", "x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "cross-origin-opener-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "accept-ranges": "bytes", "cross-origin-embedder-policy": "same-origin", "x-frame-options": "DENY", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "cache-control": "public, max-age=31536000, immutable", "vary": "Accept-Encoding, Origin", "content-length": "2105"}, "url": "https://deno.land/std@0.204.0/fs/empty_dir.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 977699571}}