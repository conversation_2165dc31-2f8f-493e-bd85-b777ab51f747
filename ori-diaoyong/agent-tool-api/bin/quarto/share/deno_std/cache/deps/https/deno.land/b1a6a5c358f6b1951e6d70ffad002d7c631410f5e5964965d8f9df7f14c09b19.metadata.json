{"headers": {"cross-origin-opener-policy": "same-origin", "date": "Wed, 01 May 2024 21:41:50 GMT", "via": "http/2 edgeproxy-h", "content-length": "5746", "cross-origin-resource-policy": "same-origin", "server-timing": "fetchSource;dur=26", "x-amz-cf-id": "5rdz8zjxmuq3rcMu1EU85rCzH9a1KLT3sv6dBsXTBLBVtaEC740zaw==", "cross-origin-embedder-policy": "same-origin", "vary": "Accept-Encoding, Origin", "x-amz-cf-pop": "IAD61-P2", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "x-cache": "Miss from cloudfront", "x-amz-replication-status": "COMPLETED", "accept-ranges": "bytes", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-server-side-encryption": "AES256", "server": "deno/gcp-us-east4", "etag": "\"34370dfa4714a1dcaa8d290f8e0634fe\"", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "referrer-policy": "strict-origin-when-cross-origin", "content-type": "application/typescript; charset=utf-8", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-content-type-options": "nosniff", "x-amz-version-id": "gphDtKZltfV3r0QdPwU4Cm.QsDxmyqr.", "x-frame-options": "DENY"}, "url": "https://deno.land/std@0.204.0/collections/unstable/binary_heap.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 456492316}}