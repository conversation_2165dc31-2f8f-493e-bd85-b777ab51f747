{"headers": {"access-control-allow-origin": "*", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=8", "x-amz-cf-id": "tt5mS76Rxdxtffuvmkn5GIObHKj-I6B2ESisqRDS4VQqQU6zr39NJA==", "x-amz-version-id": "z3UbcdL1OzgQvQlK_FPvQRi8XeCCv6iB", "x-amz-replication-status": "COMPLETED", "etag": "\"18d0ee82b1b28320a1cfdbd7b49c6a5f\"", "content-length": "176", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cross-origin-resource-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "via": "http/2 edgeproxy-h", "date": "Wed, 24 Apr 2024 06:53:25 GMT", "x-cache": "Hit from cloudfront", "vary": "Accept-Encoding, Origin", "cache-control": "public, max-age=31536000, immutable", "x-amz-server-side-encryption": "AES256", "age": "658105", "accept-ranges": "bytes", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-cf-pop": "IAD61-P2", "x-content-type-options": "nosniff", "server": "deno/gcp-us-east4", "cross-origin-embedder-policy": "same-origin", "x-frame-options": "DENY"}, "url": "https://deno.land/std@0.204.0/path/windows/separator.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 951727567}}