{"headers": {"x-amz-server-side-encryption": "AES256", "etag": "\"7d99e3cd0ac5c2f7426ce774d31621f8\"", "x-amz-cf-pop": "IAD61-P2", "vary": "Accept-Encoding, Origin", "x-content-type-options": "nosniff", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-cf-id": "w1Gf3SQ5ybH2rUeystPUBXj00vJBgb6VdtP2rEwcuyc65U3xTDqzBQ==", "date": "Wed, 24 Apr 2024 07:12:16 GMT", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "server": "deno/gcp-us-east4", "x-frame-options": "DENY", "content-length": "1043", "server-timing": "fetchSource;dur=46", "age": "656974", "cache-control": "public, max-age=31536000, immutable", "access-control-allow-origin": "*", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "7.qe11rbtH76pvnOhMXm0VQBNuMNZcwI", "x-cache": "Hit from cloudfront", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "accept-ranges": "bytes"}, "url": "https://deno.land/std@0.204.0/crypto/_fnv/fnv64.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 714070015}}