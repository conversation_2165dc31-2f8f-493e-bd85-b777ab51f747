{"headers": {"accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "cross-origin-embedder-policy": "same-origin", "etag": "\"4eca2f5cca4a809e8c99bae0836431f6\"", "content-length": "1035", "x-frame-options": "DENY", "date": "Wed, 24 Apr 2024 07:24:07 GMT", "cross-origin-opener-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cache-control": "public, max-age=31536000, immutable", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=12", "x-amz-cf-id": "dlLJkYj0EBYvNi8qkKWjdiJYaR2LTpn6y4F9QTJGWQxC_SmhG88Cpw==", "content-type": "application/typescript; charset=utf-8", "x-amz-cf-pop": "IAD61-P2", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "server": "deno/gcp-us-east4", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "access-control-allow-origin": "*", "x-amz-replication-status": "COMPLETED", "age": "656262", "x-amz-server-side-encryption": "AES256", "vary": "Accept-Encoding, Origin", "x-amz-version-id": "4cqLWHOQYh7E5r2iyv78fNbwjtLnVtsz", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "via": "http/2 edgeproxy-h"}, "url": "https://deno.land/std@0.204.0/yaml/schema/extended.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 504145403}}