{"headers": {"etag": "\"d5944fd9196c882fd78fb26ef59eea5d\"", "server-timing": "fetchSource;dur=46", "via": "http/2 edgeproxy-h", "age": "659983", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "x-frame-options": "DENY", "content-length": "46834", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "cross-origin-resource-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-amz-cf-id": "2zruZiGgMjEna5tzAX58IZ6GKf8_QSEKYQQsFeYWeuWEFNfEmdc1hA==", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "6VuqXOn9WCRUxXWH_lX02yk.eyxKLITQ", "content-type": "application/typescript; charset=utf-8", "x-cache": "Hit from cloudfront", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "server": "deno/gcp-us-east4", "cross-origin-embedder-policy": "same-origin", "x-content-type-options": "nosniff"}, "url": "https://deno.land/std@0.204.0/yaml/_loader/loader.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 226161389}}