{"headers": {"strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-pop": "IAD61-P2", "content-type": "application/typescript; charset=utf-8", "x-cache": "Hit from cloudfront", "server-timing": "fetchSource;dur=12", "vary": "Accept-Encoding, Origin", "cross-origin-embedder-policy": "same-origin", "date": "Wed, 24 Apr 2024 18:12:32 GMT", "x-amz-cf-id": "yyjbkh1ESe0NpKhxmmoCvMwb96aeuJpMwVWE6PC6Is2HCmviYwgI6w==", "x-amz-replication-status": "COMPLETED", "cross-origin-resource-policy": "same-origin", "x-amz-version-id": "vVWn_Tyys5bKyfoJ_lfQEX2JTcZX.psB", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "via": "http/2 edgeproxy-h", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "age": "617357", "content-length": "282", "etag": "\"0bb2b54a242ce40861be321092ba03b9\"", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "DENY", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "server": "deno/gcp-us-east4"}, "url": "https://deno.land/std@0.204.0/semver/neq.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 450902982}}