{"headers": {"access-control-allow-origin": "*", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "x-frame-options": "DENY", "cross-origin-resource-policy": "same-origin", "x-content-type-options": "nosniff", "x-amz-cf-pop": "IAD61-P2", "cross-origin-opener-policy": "same-origin", "content-length": "1321", "via": "http/2 edgeproxy-h", "x-amz-version-id": "JY9watjttjtLNZVC.cvoRmyTod60XHX0", "server": "deno/gcp-us-east4", "accept-ranges": "bytes", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-type": "application/typescript; charset=utf-8", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "x-amz-replication-status": "COMPLETED", "cache-control": "public, max-age=31536000, immutable", "date": "Wed, 01 May 2024 21:41:49 GMT", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "referrer-policy": "strict-origin-when-cross-origin", "server-timing": "fetchSource;dur=12", "cross-origin-embedder-policy": "same-origin", "x-amz-cf-id": "EfpX0GUcyAxxcDB83dXxaWx95y4et2jqFpMahUIVasNNKGEch6FG8w==", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/datetime/format.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 430336570}}