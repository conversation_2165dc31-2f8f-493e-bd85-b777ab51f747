{"headers": {"content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "date": "Wed, 01 May 2024 21:41:49 GMT", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "lej7eUhLHiAAAtLX8cT0EIE2B-583uEz_ra_YSJv6tlbMPU4Cwao-w==", "cache-control": "public, max-age=31536000, immutable", "accept-ranges": "bytes", "x-amz-replication-status": "COMPLETED", "etag": "\"b5ac6a9039e9c0f546e7a410596f6749\"", "content-length": "1448", "cross-origin-embedder-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "server": "deno/gcp-us-east4", "x-amz-version-id": "BcTrbYR7s4v7_qa5v9C0wqsJ844aLqy3", "x-content-type-options": "nosniff", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "access-control-allow-origin": "*", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=350", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "cross-origin-resource-policy": "same-origin", "x-cache": "Miss from cloudfront", "vary": "Accept-Encoding, Origin", "content-type": "application/typescript; charset=utf-8", "x-amz-server-side-encryption": "AES256", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT"}, "url": "https://deno.land/std@0.204.0/front_matter/test.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 979044956}}