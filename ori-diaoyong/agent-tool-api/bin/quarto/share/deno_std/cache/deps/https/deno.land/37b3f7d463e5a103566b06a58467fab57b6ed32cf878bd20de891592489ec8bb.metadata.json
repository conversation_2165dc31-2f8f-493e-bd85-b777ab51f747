{"headers": {"x-amz-replication-status": "COMPLETED", "content-length": "626", "age": "659982", "x-frame-options": "DENY", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "cache-control": "public, max-age=31536000, immutable", "via": "http/2 edgeproxy-h", "server-timing": "fetchSource;dur=14", "x-amz-cf-id": "lkABa8bax3Syb_q3SboQHI3Yu_cU8dFmUw150eGd1LCFYUDb9of_nw==", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "cross-origin-resource-policy": "same-origin", "access-control-allow-origin": "*", "x-content-type-options": "nosniff", "etag": "\"d35a3cf029aa6222157a8b5819f374a9\"", "cross-origin-embedder-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-amz-cf-pop": "IAD61-P2", "x-amz-version-id": "TuuVdM2dbAnuwfVd_wKS7A5_d51X_udM", "server": "deno/gcp-us-east4", "cross-origin-opener-policy": "same-origin", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "content-type": "application/typescript; charset=utf-8", "referrer-policy": "strict-origin-when-cross-origin", "accept-ranges": "bytes", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/yaml/schema/json.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 548401352}}