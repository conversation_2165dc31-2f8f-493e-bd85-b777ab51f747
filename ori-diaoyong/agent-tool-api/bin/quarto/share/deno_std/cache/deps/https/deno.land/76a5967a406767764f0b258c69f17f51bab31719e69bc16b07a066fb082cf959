// Ported from js-yaml v3.13.1:
// https://github.com/nodeca/js-yaml/commit/665aadda42349dcae869f12040d9b10ef18d12da
// Copyright 2011-2015 by <PERSON><PERSON>. All rights reserved. MIT license.
// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.
// This module is browser compatible.

export { core as CORE_SCHEMA } from "./core.ts";
export { def as DEFAULT_SCHEMA } from "./default.ts";
export { extended as EXTENDED_SCHEMA } from "./extended.ts";
export { failsafe as FAILSAFE_SCHEMA } from "./failsafe.ts";
export { json as JSON_SCHEMA } from "./json.ts";
