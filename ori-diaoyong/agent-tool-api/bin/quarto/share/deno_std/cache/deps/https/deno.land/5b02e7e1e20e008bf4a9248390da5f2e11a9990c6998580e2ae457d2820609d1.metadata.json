{"headers": {"age": "656974", "cross-origin-resource-policy": "same-origin", "etag": "\"22fb1ab6eca370cb5f3d853310b742f7\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "BUiBPRXA1ApVAeeXfp1hDaA8JYImJ3efnZgrS_w6ONJDO96hgkxWvQ==", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "x-amz-server-side-encryption": "AES256", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "content-type": "application/typescript; charset=utf-8", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "x-content-type-options": "nosniff", "server-timing": "fetchSource;dur=12", "cross-origin-opener-policy": "same-origin", "server": "deno/gcp-us-east4", "referrer-policy": "strict-origin-when-cross-origin", "x-cache": "Hit from cloudfront", "accept-ranges": "bytes", "content-length": "825", "x-amz-cf-pop": "IAD61-P2", "x-amz-version-id": "yfEn8CuX24XV3zgD1d6Y7IE4jSmdr31F", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "date": "Wed, 24 Apr 2024 07:12:16 GMT", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "vary": "Accept-Encoding, Origin"}, "url": "https://deno.land/std@0.204.0/crypto/_fnv/fnv32.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 716136128}}