{"headers": {"x-amz-replication-status": "COMPLETED", "x-amz-version-id": "pHMgqeciuBx49Jv12yBhMwaxRMStAFuz", "x-amz-server-side-encryption": "AES256", "x-content-type-options": "nosniff", "etag": "\"860d1097b9ba833199716b962b5ba8b0\"", "vary": "Accept-Encoding, Origin", "x-amz-cf-pop": "IAD61-P2", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-cf-id": "sha4ZhCI2t5JMBMHXEBO356wNA_6skA87lJ4w9RxaJAVgVlAgL77nQ==", "referrer-policy": "strict-origin-when-cross-origin", "accept-ranges": "bytes", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "x-cache": "Hit from cloudfront", "cache-control": "public, max-age=31536000, immutable", "content-length": "491", "age": "656263", "server": "deno/gcp-us-east4", "server-timing": "fetchSource;dur=56", "cross-origin-opener-policy": "same-origin", "via": "http/2 edgeproxy-h", "date": "Wed, 24 Apr 2024 07:24:08 GMT", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "access-control-allow-origin": "*", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin"}, "url": "https://deno.land/std@0.204.0/yaml/_type/map.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 957066808}}