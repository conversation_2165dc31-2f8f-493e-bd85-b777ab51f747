{"headers": {"content-type": "application/typescript; charset=utf-8", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "x-content-type-options": "nosniff", "server": "deno/gcp-us-east4", "etag": "\"6d764d8009989b20eb977915722bdfbf\"", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-replication-status": "COMPLETED", "access-control-allow-origin": "*", "x-amz-cf-id": "vdB_PoqhhLT7Na_iCLKo67EVZZz0XUgF-BpQt9eZvlp5Jc6b30G1dQ==", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "age": "659982", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "referrer-policy": "strict-origin-when-cross-origin", "vary": "Accept-Encoding, Origin", "cross-origin-opener-policy": "same-origin", "cache-control": "public, max-age=31536000, immutable", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "server-timing": "fetchSource;dur=12", "x-amz-version-id": "CJlMsME9rNX6V_5uRunN4EWmiJSgjjFf", "content-length": "520", "cross-origin-resource-policy": "same-origin", "accept-ranges": "bytes", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox"}, "url": "https://deno.land/std@0.204.0/yaml/schema/core.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 961559309}}