{"headers": {"x-amz-cf-id": "yIyqR1_u63lSWlzF37-oc1fWiIgiZ0tkMo8zw0V5EdjWDOgGn8o6Ug==", "x-amz-server-side-encryption": "AES256", "cache-control": "public, max-age=31536000, immutable", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "via": "http/2 edgeproxy-h", "cross-origin-embedder-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server-timing": "fetchSource;dur=10", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-pop": "IAD61-P2", "x-cache": "Hit from cloudfront", "x-amz-version-id": "vfI4jfZFf1tQydjoIODUuF4BRRXm0AcG", "cross-origin-opener-policy": "same-origin", "content-length": "731", "accept-ranges": "bytes", "age": "659196", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "cross-origin-resource-policy": "same-origin", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "etag": "\"a1fc434da36cfb21ad077a6f26a25aea\"", "x-frame-options": "DENY", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED", "vary": "Accept-Encoding, Origin", "x-content-type-options": "nosniff", "server": "deno/gcp-us-east4", "access-control-allow-origin": "*"}, "url": "https://deno.land/std@0.204.0/path/posix/join.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 505275305}}