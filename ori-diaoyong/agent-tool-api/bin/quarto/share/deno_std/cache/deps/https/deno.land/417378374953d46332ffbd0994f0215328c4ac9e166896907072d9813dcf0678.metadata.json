{"headers": {"x-amz-version-id": "SSGrxRpcVDujtuZJ0lO9KdYb94hYszYg", "etag": "\"b9840f524e30e016b7c6ffaaebfcf925\"", "x-amz-cf-pop": "IAD61-P2", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:01 GMT", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "via": "http/2 edgeproxy-h", "x-amz-replication-status": "COMPLETED", "x-cache": "Hit from cloudfront", "access-control-allow-origin": "*", "x-amz-cf-id": "YaGkYF4CtKxZ0c9a64SQmsF6e6NF44krx7JtVjy_2P-BEkHUM5pV1Q==", "cross-origin-embedder-policy": "same-origin", "date": "Wed, 24 Apr 2024 06:53:25 GMT", "x-content-type-options": "nosniff", "cache-control": "public, max-age=31536000, immutable", "referrer-policy": "strict-origin-when-cross-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "content-length": "3680", "x-frame-options": "DENY", "server-timing": "fetchSource;dur=18", "cross-origin-resource-policy": "same-origin", "age": "658104", "vary": "Accept-Encoding, Origin", "x-amz-server-side-encryption": "AES256", "server": "deno/gcp-us-east4", "accept-ranges": "bytes", "content-type": "application/typescript; charset=utf-8"}, "url": "https://deno.land/std@0.204.0/async/pool.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 436952390}}