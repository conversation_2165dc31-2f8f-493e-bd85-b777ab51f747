{"headers": {"etag": "\"173add4d9fd2a7210df2bce11e482b9f\"", "referrer-policy": "strict-origin-when-cross-origin", "via": "http/2 edgeproxy-h", "accept-ranges": "bytes", "x-frame-options": "DENY", "cache-control": "public, max-age=31536000, immutable", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "server": "deno/gcp-us-east4", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "server-timing": "fetchSource;dur=22", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-version-id": "w9FtJi5MfKsZJauOb8vKEGulW_y5aPgQ", "access-control-allow-origin": "*", "cross-origin-opener-policy": "same-origin", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "date": "Wed, 24 Apr 2024 07:24:08 GMT", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "age": "656263", "x-amz-cf-pop": "IAD61-P2", "vary": "Accept-Encoding, Origin", "x-amz-cf-id": "orKVjcs9mEApFENz0atK-pJs7fOokeh1Z7YaSH-jy0B8QXTmpek93Q==", "content-length": "1275"}, "url": "https://deno.land/std@0.204.0/yaml/_type/function.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 950825262}}