{"headers": {"x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "x-amz-cf-id": "q_7ShzIJWagEnfx-z2C9dKfiaSUz_a96sG8l5ZoDlzGNYQ79Rp5Iwg==", "x-amz-server-side-encryption": "AES256", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "referrer-policy": "strict-origin-when-cross-origin", "cache-control": "public, max-age=31536000, immutable", "access-control-allow-origin": "*", "x-content-type-options": "nosniff", "cross-origin-resource-policy": "same-origin", "x-amz-version-id": "KkMvW5RIb6WTfzs4Xc3uUIy8LySYk5Ko", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server-timing": "fetchSource;dur=78", "content-type": "application/typescript; charset=utf-8", "date": "Wed, 01 May 2024 21:41:49 GMT", "accept-ranges": "bytes", "server": "deno/gcp-us-east4", "cross-origin-opener-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "content-length": "10168", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "x-amz-replication-status": "COMPLETED", "x-cache": "Miss from cloudfront", "etag": "\"4c51b01f22e8616b5f6c557aeed8adcd\""}, "url": "https://deno.land/std@0.204.0/encoding/binary.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 118515696}}