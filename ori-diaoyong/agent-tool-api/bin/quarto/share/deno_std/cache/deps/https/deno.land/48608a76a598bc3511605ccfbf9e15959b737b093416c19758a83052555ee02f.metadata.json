{"headers": {"x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "x-amz-replication-status": "COMPLETED", "x-amz-version-id": "M2bkQA6fZDnEwtD6AyBcEpFQ5vyy57jr", "x-content-type-options": "nosniff", "referrer-policy": "strict-origin-when-cross-origin", "x-amz-cf-id": "4Oe4v4ryee0WSFqPq5OZgZKl1nvJEpSVACT2lFW8vvoOl8NLSPhGqA==", "cross-origin-opener-policy": "same-origin", "last-modified": "Thu, 12 Oct 2023 22:56:03 GMT", "date": "Wed, 24 Apr 2024 06:35:14 GMT", "etag": "\"60f625d038892d9eb0d9597f8105f402\"", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "age": "659196", "accept-ranges": "bytes", "x-frame-options": "DENY", "cross-origin-resource-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "server": "deno/gcp-us-east4", "content-type": "application/typescript; charset=utf-8", "cross-origin-embedder-policy": "same-origin", "content-length": "3978", "server-timing": "fetchSource;dur=10", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2"}, "url": "https://deno.land/std@0.204.0/path/windows/relative.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 505143068}}