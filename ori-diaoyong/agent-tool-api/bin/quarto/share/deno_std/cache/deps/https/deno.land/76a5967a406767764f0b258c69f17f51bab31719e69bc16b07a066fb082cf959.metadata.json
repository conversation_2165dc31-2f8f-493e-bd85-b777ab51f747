{"headers": {"referrer-policy": "strict-origin-when-cross-origin", "x-content-type-options": "nosniff", "vary": "Accept-Encoding, Origin", "x-cache": "Hit from cloudfront", "x-amz-cf-id": "nH82P9Epb-RWRmzGarE5yG1hjuXzHIhMSNv2mvBBA5_wB6GzO9nntA==", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-opener-policy": "same-origin", "accept-ranges": "bytes", "content-length": "580", "cross-origin-resource-policy": "same-origin", "access-control-allow-origin": "*", "content-type": "application/typescript; charset=utf-8", "cache-control": "public, max-age=31536000, immutable", "cross-origin-embedder-policy": "same-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "server": "deno/gcp-us-east4", "date": "Wed, 24 Apr 2024 06:22:08 GMT", "age": "659981", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-amz-replication-status": "COMPLETED", "server-timing": "fetchSource;dur=238", "x-frame-options": "DENY", "via": "http/2 edgeproxy-h", "x-amz-cf-pop": "IAD61-P2", "x-amz-server-side-encryption": "AES256", "x-amz-version-id": "u.b7deobsU50bUyOkTFPBE_DHKt0dAhZ"}, "url": "https://deno.land/std@0.204.0/yaml/schema/mod.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 978789388}}