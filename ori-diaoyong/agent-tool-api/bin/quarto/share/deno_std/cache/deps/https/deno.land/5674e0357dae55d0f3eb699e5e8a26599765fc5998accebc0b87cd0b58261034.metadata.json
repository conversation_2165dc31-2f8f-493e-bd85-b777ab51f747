{"headers": {"access-control-allow-origin": "*", "last-modified": "Thu, 12 Oct 2023 22:56:04 GMT", "cross-origin-resource-policy": "same-origin", "cross-origin-opener-policy": "same-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "vary": "Accept-Encoding, Origin", "server-timing": "fetchSource;dur=82", "x-amz-cf-pop": "IAD61-P2", "accept-ranges": "bytes", "date": "Wed, 01 May 2024 07:28:06 GMT", "age": "51224", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-replication-status": "COMPLETED", "x-content-type-options": "nosniff", "content-type": "application/typescript; charset=utf-8", "x-cache": "Hit from cloudfront", "referrer-policy": "strict-origin-when-cross-origin", "server": "deno/gcp-us-east4", "via": "http/2 edgeproxy-h", "x-amz-version-id": "t42MLK7pxQ11tzIWhFFfRZiH9pjewO8e", "x-amz-cf-id": "XfagCC1IIuaqheuqVCxwnswBSknCOH-RB0SQM_96jpuhf4Vq_03VWA==", "content-length": "2074", "x-frame-options": "DENY", "cross-origin-embedder-policy": "same-origin", "etag": "\"600363d31cc0026b38beda12737dcfc7\"", "x-amz-server-side-encryption": "AES256", "cache-control": "public, max-age=31536000, immutable"}, "url": "https://deno.land/std@0.204.0/streams/reader_from_iterable.ts", "now": {"secs_since_epoch": 1714599709, "nanos_since_epoch": 146907903}}