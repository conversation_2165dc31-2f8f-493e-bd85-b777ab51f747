{"headers": {"strict-transport-security": "max-age=63072000; includeSubDomains; preload", "via": "http/2 edgeproxy-h", "cross-origin-embedder-policy": "same-origin", "server-timing": "fetchSource;dur=64", "cross-origin-opener-policy": "same-origin", "etag": "\"bc46a4522f7648ebf49267e188435c89\"", "date": "Wed, 24 Apr 2024 06:29:58 GMT", "access-control-allow-origin": "*", "cache-control": "public, max-age=31536000, immutable", "cross-origin-resource-policy": "same-origin", "x-amz-cf-pop": "IAD61-P2", "age": "659513", "server": "deno/gcp-us-east4", "x-amz-cf-id": "JC-uJ1--NpjYuvdfjemT4DaB73lY5bJI5Txo5t1B4NthLxo9W0TP1g==", "accept-ranges": "bytes", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "content-length": "1477", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront", "referrer-policy": "strict-origin-when-cross-origin", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "x-amz-version-id": "********************************", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "vary": "Accept-Encoding, Origin", "content-type": "application/typescript; charset=utf-8", "x-amz-replication-status": "COMPLETED"}, "url": "https://deno.land/std@0.204.0/crypto/_fnv/util.ts", "now": {"secs_since_epoch": 1714599710, "nanos_since_epoch": 951878622}}