{"headers": {"cross-origin-opener-policy": "same-origin", "x-amz-version-id": "Wsp0MXveFA_h.jL_yoOw6OA3B90M8okl", "access-control-allow-origin": "*", "content-length": "3591", "last-modified": "Thu, 12 Oct 2023 22:56:02 GMT", "x-amz-replication-status": "COMPLETED", "cross-origin-resource-policy": "same-origin", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "cross-origin-embedder-policy": "same-origin", "age": "657256", "content-security-policy": "default-src 'none'; style-src 'unsafe-inline'; sandbox", "etag": "\"********************************\"", "server": "deno/gcp-us-east4", "via": "http/2 edgeproxy-h", "x-amz-cf-id": "Lri5w8f0lf25Hyr8c6O29YFNjCpZL1sRm11PkUnqgo9rEwJhewXCNQ==", "x-amz-cf-pop": "IAD61-P2", "accept-ranges": "bytes", "x-frame-options": "DENY", "cache-control": "public, max-age=31536000, immutable", "server-timing": "fetchSource;dur=28", "x-content-type-options": "nosniff", "date": "Wed, 24 Apr 2024 07:07:33 GMT", "content-type": "application/typescript; charset=utf-8", "vary": "Accept-Encoding, Origin", "x-amz-server-side-encryption": "AES256", "x-cache": "Hit from cloudfront"}, "url": "https://deno.land/std@0.204.0/encoding/hex.ts", "now": {"secs_since_epoch": 1714599708, "nanos_since_epoch": 142420129}}