/*-- scss:functions --*/
// Our website navbar implementation will shift the body down
// to accomodate the navbar, but the height is variable. As a result
// we compute the height using JS, so it is perfect. This can lead to
// a content jump when the js executes, so place a padding there at render
// time to minimize this.
@function navbar-default-offset($theme) {
  $offsets: (
    darkly: 82px,
    flatly: 82px,
    litera: 67px,
    lumen: 68px,
    lux: 105px,
    materia: 96px,
    pulse: 89px,
    quartz: 82px,
    sandstone: 63px,
    simplex: 80px,
    sketchy: 68px,
    slate: 66px,
    zephyr: 76px,
  );

  $val: null;
  @if ($theme != null) {
    $val: quarto-map.get($offsets, $theme);
  }

  @if ($val != null) {
    @return $val;
  } @else {
    @return 64px;
  }
}

/*-- scss:defaults --*/
$content-padding-top: 14px !default;
$sidebar-glass-bg: #66666666 !default;
$sidebar-anim-duration: 0.15s !default;

$navbar-toggle-position: left !default;

$navbar-toggler-order: if($navbar-toggle-position == "left", 1, 4) !default;
$navbar-title-order: if($navbar-toggle-position == "left", 2, 1) !default;
$navbar-search-order: if($navbar-toggle-position == "left", 4, 3) !default;
$navbar-tools-order: if($navbar-toggle-position == "left", 3, 2) !default;
$navbar-menu-order: if($navbar-toggle-position == "left", 20, 20) !default;

/*-- scss:mixins --*/

/*-- scss:rules --*/

.quarto-container {
  min-height: calc(100vh - 132px);
}

body.hypothesis-enabled {
  #quarto-header {
    margin-right: 16px;
  }
}

footer.footer .nav-footer,
#quarto-header > nav {
  padding-left: 1em;
  padding-right: 1em;
}

footer.footer div.nav-footer p:first-child {
  margin-top: 0;
}

footer.footer div.nav-footer p:last-child {
  margin-bottom: 0;
}

// content padding
#quarto-content > * {
  padding-top: $content-padding-top;
}

#quarto-content > #quarto-sidebar-glass {
  padding-top: 0px;
}

@include media-breakpoint-down(lg) {
  #quarto-content > * {
    padding-top: 0;
  }

  #quarto-content .subtitle {
    padding-top: $content-padding-top;
  }

  #quarto-content section:first-of-type h2:first-of-type {
    margin-top: 1rem;
  }
}

// headroom
.headroom-target,
header.headroom {
  will-change: transform;
  transition: position 200ms linear;
  transition: all 200ms linear;
}

header.headroom--pinned {
  transform: translateY(0%);
}

header.headroom--unpinned {
  transform: translateY(-100%);
}

.navbar-container {
  width: 100%;
}

.navbar-brand {
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar-brand-container {
  max-width: calc(100% - 115px);
  min-width: 0;
  display: flex;
  align-items: center;

  @include media-breakpoint-up(lg) {
    margin-right: 1em;
  }
}

.navbar-brand.navbar-brand-logo {
  margin-right: 4px;
  display: inline-flex;
}

.navbar-toggler {
  flex-basis: content;
  flex-shrink: 0;
}

.navbar {
  .navbar-brand-container {
    order: $navbar-title-order;
  }

  .navbar-toggler {
    order: $navbar-toggler-order;
  }

  .navbar-container > .navbar-nav {
    order: $navbar-menu-order;
  }

  .navbar-container > .navbar-brand-container {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .navbar-collapse {
    order: $navbar-menu-order;
  }

  #quarto-search {
    order: $navbar-search-order;
    margin-left: auto;
  }
}

@include media-breakpoint-down(med) {
  .navbar {
    @if $navbar-toggle-position == "left" {
      .navbar-toggler {
        margin-right: 0.5em;
      }
    } @else {
      #quarto-search {
        margin-right: 0.5em;
      }

      .quarto-navbar-tools .dropdown-menu {
        left: 0;
      }
    }
  }
}

.navbar-logo {
  max-height: 24px;
  width: auto;
  padding-right: 4px;
}

nav .nav-item:not(.compact) {
  padding-top: 1px;
}

nav .nav-link i,
nav .dropdown-item i {
  padding-right: 1px;
}

.navbar-expand-lg .navbar-nav .nav-link {
  padding-left: 0.6rem;
  padding-right: 0.6rem;
}

nav .nav-item.compact .nav-link {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 1.1rem;
}

.navbar {
  .quarto-navbar-tools {
    order: $navbar-tools-order;
    div.dropdown {
      display: inline-block;
    }
    .quarto-navigation-tool {
      color: $navbar-fg;
    }
    .quarto-navigation-tool:hover {
      color: $navbar-hl;
    }
  }
}

.navbar-nav .dropdown-menu {
  min-width: 220px;
  font-size: 0.9rem;
}

.navbar {
  .navbar-nav {
    .nav-link.dropdown-toggle::after {
      opacity: 0.75;
      vertical-align: 0.175em;
    }
  }

  ul.dropdown-menu {
    padding-top: 0;
    padding-bottom: 0;
  }
  .dropdown-header {
    text-transform: uppercase;
    font-size: 0.8rem;
    padding: 0 0.5rem;
  }

  .dropdown-item {
    padding: 0.4rem 0.5rem;
    & > i.bi {
      margin-left: 0.1rem;
      margin-right: 0.25em;
    }
  }
}

.sidebar #quarto-search {
  margin-top: -1px;
  svg.aa-SubmitIcon {
    width: 16px;
    height: 16px;
  }
}

.sidebar-navigation a {
  color: inherit;
}

.sidebar-title {
  margin-top: 0.25rem;
  padding-bottom: 0.5rem;
  font-size: 1.3rem;
  line-height: 1.6rem;
  visibility: visible;
}

.sidebar-title > a {
  font-size: inherit;
  text-decoration: none;
}

.sidebar-title .sidebar-tools-main {
  margin-top: -6px;
}

@include media-breakpoint-down(lg) {
  #quarto-sidebar {
    div.sidebar-header {
      padding-top: 0.2em;
    }
  }
}

.sidebar-header-stacked .sidebar-title {
  margin-top: 0.6rem;
}

.sidebar-logo {
  max-width: 90%;
  padding-bottom: 0.5rem;
}

.sidebar-logo-link {
  text-decoration: none;
}

.sidebar-navigation li a {
  text-decoration: none;
}

// Sidebar tools
.sidebar-navigation .quarto-navigation-tool {
  opacity: 0.7;
  font-size: 0.875rem;
}

#quarto-sidebar > nav > .sidebar-tools-main {
  margin-left: 14px;
}

.sidebar-tools-main {
  display: inline-flex;
  margin-left: 0px;
  order: 2;
}

.sidebar-tools-main:not(.tools-wide) {
  vertical-align: middle;
}

.sidebar-navigation .quarto-navigation-tool.dropdown-toggle::after {
  display: none;
}

// Sidebar navigation items
$sidebar-items-gap-spacing: 0.2em;
$sidebar-section-indent: 0.5em;
$sidebar-section-bottom-margin: 0.2em;

.sidebar.sidebar-navigation > * {
  padding-top: 1em;
}

.sidebar-item {
  margin-bottom: $sidebar-items-gap-spacing;
  line-height: 1rem;
  margin-top: 0.4rem;
}

.sidebar-section {
  padding-left: $sidebar-section-indent;
  padding-bottom: $sidebar-section-bottom-margin;
}

.sidebar-item .sidebar-item-container {
  // Positions the link and dongle
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.sidebar-item-toggle:hover {
  cursor: pointer;
}

.sidebar-item .sidebar-item-toggle .bi {
  // The dongle for opening and closing sections
  font-size: 0.7rem;
  text-align: center;
}

.sidebar-item .sidebar-item-toggle .bi-chevron-right::before {
  transition: transform 200ms ease;
}

.sidebar-item
  .sidebar-item-toggle[aria-expanded="false"]
  .bi-chevron-right::before {
  transform: none;
}

.sidebar-item
  .sidebar-item-toggle[aria-expanded="true"]
  .bi-chevron-right::before {
  transform: rotate(90deg);
}

.sidebar-item-text {
  width: 100%;
}

.sidebar-navigation .sidebar-divider {
  margin-left: 0;
  margin-right: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

// Toggle the top secondary navigation bar
@include media-breakpoint-down(lg) {
  .quarto-secondary-nav {
    display: block;

    button.quarto-search-button {
      padding-right: 0em;
      padding-left: 2em;
    }

    button.quarto-btn-toggle {
      margin-left: -0.75rem;
      margin-right: 0.15rem;
    }

    nav.quarto-title-breadcrumbs {
      display: none;
    }

    nav.quarto-page-breadcrumbs {
      display: flex;
      align-items: center;
      padding-right: 1em;
      margin-left: -0.25em;

      a {
        text-decoration: none;
      }

      ol.breadcrumb {
        margin-bottom: 0;
      }
    }
  }
}

@include media-breakpoint-up(lg) {
  .quarto-secondary-nav {
    display: none;
  }
}

.quarto-title-breadcrumbs .breadcrumb {
  margin-bottom: 0.5em;
  font-size: 0.9rem;

  li:last-of-type {
    a {
      color: $text-muted;
    }
  }
}

$sidebar-hl: if(
  $sidebar-hl,
  sidebar-hl,
  theme-contrast($nav-link-color, $sidebar-bg)
);
$sidebar-color: rgba($sidebar-fg, 1) !default;
$sidebar-hover-color: rgba($sidebar-hl, 0.8) !default;
$sidebar-active-color: $sidebar-hl !default;
$sidebar-disabled-color: rgba($sidebar-fg, 0.75) !default;

.quarto-secondary-nav .quarto-btn-toggle {
  color: $sidebar-color;
}

.quarto-secondary-nav[aria-expanded="false"]
  .quarto-btn-toggle
  .bi-chevron-right::before {
  transform: none;
}

.quarto-secondary-nav[aria-expanded="true"]
  .quarto-btn-toggle
  .bi-chevron-right::before {
  transform: rotate(90deg);
}

.quarto-secondary-nav .quarto-btn-toggle .bi-chevron-right::before {
  transition: transform 200ms ease;
}

.quarto-secondary-nav {
  cursor: pointer;
}

.no-decor {
  text-decoration: none;
}

.quarto-secondary-nav-title {
  margin-top: 0.3em;
  color: $sidebar-color;
  padding-top: 4px;
}

.quarto-secondary-nav nav.quarto-page-breadcrumbs {
  color: $sidebar-color;
  a {
    color: $sidebar-color;
  }
  a:hover {
    color: $sidebar-hover-color;
  }
  .breadcrumb-item::before {
    color: theme-dim($sidebar-color, 20%);
  }
}

.breadcrumb-item {
  line-height: 1.2rem;
}

div.sidebar-item-container {
  color: $sidebar-color;
  &:hover,
  &:focus {
    color: $sidebar-hover-color;
  }

  &.disabled {
    color: $sidebar-disabled-color;
  }

  .active,
  .show > .nav-link,
  .sidebar-link > code {
    color: $sidebar-active-color;
  }
}

div.sidebar.sidebar-navigation.rollup.quarto-sidebar-toggle-contents,
nav.sidebar.sidebar-navigation:not(.rollup) {
  @if $sidebar-bg {
    background-color: $sidebar-bg;
  } @else {
    background-color: $body-bg;
  }
}

@if $sidebar-border {
  .sidebar.sidebar-navigation:not(.rollup) {
    border-right: 1px solid $table-border-color !important;
  }
}

@include media-breakpoint-down(lg) {
  .sidebar-navigation .sidebar-item a,
  .nav-page .nav-page-text,
  .sidebar-navigation {
    font-size: $sidebar-font-size-collapse;
  }

  .sidebar-navigation ul.sidebar-section.depth1 .sidebar-section-item {
    font-size: $sidebar-font-size-section-collapse;
  }

  .sidebar-logo {
    display: none;
  }

  .sidebar.sidebar-navigation {
    position: static;
    border-bottom: 1px solid $table-border-color;
  }

  .sidebar.sidebar-navigation.collapsing {
    position: fixed;
    z-index: 1000;
  }

  .sidebar.sidebar-navigation.show {
    position: fixed;
    z-index: 1000;
  }

  .sidebar.sidebar-navigation {
    min-height: 100%;
  }

  nav.quarto-secondary-nav {
    @if $sidebar-bg {
      background-color: $sidebar-bg;
    } @else {
      background-color: $body-bg;
    }
    border-bottom: 1px solid $table-border-color;
  }

  .quarto-banner nav.quarto-secondary-nav {
    @if variable-exists(navbar-bg) {
      background-color: $navbar-bg;
    }

    @if (variable-exists(navbar-fg)) {
      color: $navbar-fg;
    }
    border-top: 1px solid $table-border-color;
  }

  .sidebar .sidebar-footer {
    visibility: visible;
    padding-top: 1rem;
    position: inherit;
  }

  .sidebar-tools-collapse {
    display: block;
  }
}

#quarto-sidebar {
  transition: width $sidebar-anim-duration ease-in;
  > * {
    padding-right: 1em;
  }
}

@include media-breakpoint-down(lg) {
  #quarto-sidebar .sidebar-menu-container {
    white-space: nowrap;
    min-width: 225px;
  }

  #quarto-sidebar.show {
    transition: width $sidebar-anim-duration ease-out;
  }
}

@include media-breakpoint-up(lg) {
  #quarto-sidebar {
    display: flex;
    flex-direction: column;
  }
  .nav-page .nav-page-text,
  .sidebar-navigation .sidebar-section .sidebar-item {
    font-size: $sidebar-font-size-section;
  }

  .sidebar-navigation .sidebar-item {
    font-size: $sidebar-font-size;
  }

  .sidebar.sidebar-navigation {
    display: block;
    position: sticky;
  }

  .sidebar-search {
    width: 100%;
  }

  .sidebar .sidebar-footer {
    visibility: visible;
  }
}

@include media-breakpoint-down(lg) {
  #quarto-sidebar-glass {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff00;
    transition: background-color $sidebar-anim-duration ease-in;
    z-index: -1;
  }

  #quarto-sidebar-glass.collapsing {
    z-index: $zindex-dropdown;
  }

  #quarto-sidebar-glass.show {
    transition: background-color $sidebar-anim-duration ease-out;
    background-color: $sidebar-glass-bg;
    z-index: $zindex-dropdown;
  }
}

.sidebar .sidebar-footer {
  padding: 0.5rem 1rem;
  align-self: flex-end;
  color: $text-muted;
  width: 100%;
}

.quarto-page-breadcrumbs .breadcrumb-item + .breadcrumb-item,
.quarto-page-breadcrumbs .breadcrumb-item {
  padding-right: 0.33em;
  padding-left: 0;
}

.quarto-page-breadcrumbs .breadcrumb-item::before {
  padding-right: 0.33em;
}

.quarto-sidebar-footer {
  font-size: 0.875em;
}

.sidebar-section .bi-chevron-right {
  vertical-align: middle;
}

.sidebar-section .bi-chevron-right::before {
  font-size: 0.9em;
}

.notransition {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}

// This is used to suppress the focus borders on Chrome when the user is simply
// clicking with the mouse vs using the keyboard to move focus.
.btn:focus:not(:focus-visible) {
  box-shadow: none;
}

.page-navigation {
  display: flex;
  justify-content: space-between;
}

.nav-page {
  padding-bottom: 0.75em;
}

.nav-page .bi {
  font-size: 1.8rem;
  vertical-align: middle;
}

.nav-page .nav-page-text {
  padding-left: 0.25em;
  padding-right: 0.25em;
}

.nav-page a {
  color: $text-muted;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.nav-page a:hover {
  color: $link-hover-color;
}

.nav-footer .toc-actions {
  a,
  a:hover {
    text-decoration: none;
  }

  ul {
    display: flex;
    list-style: none;

    :first-child {
      margin-left: auto;
    }
    :last-child {
      margin-right: auto;
    }

    li {
      padding-right: 1.5em;

      i.bi {
        padding-right: 0.4em;
      }
    }

    li:last-of-type {
      padding-right: 0;
    }
  }

  padding-bottom: 0.5em;
  padding-top: 0.5em;
}

// border weight
// border style
.nav-footer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: baseline;
  text-align: center;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  @if variable-exists(footer-bg) {
    background-color: $footer-bg;
  }
}

// Immediate set the top offset if a fixed top header is present
// This prevents a 'flash / jerk' when the page loads
body.nav-fixed {
  padding-top: navbar-default-offset($theme-name);
}

body .nav-footer {
  @if variable-exists(footer-border) and $footer-border {
    @if variable-exists(footer-border-color) {
      border-top: 1px solid $footer-border-color;
    } @else {
      border-top: 1px solid $table-border-color;
    }
  }
}

.nav-footer-contents {
  color: $text-muted;
  margin-top: 0.25rem;
}

.nav-footer {
  min-height: 3.5em;
  color: $footer-fg;
  a {
    @if variable-exists(footer-fg) {
      color: $footer-fg;
    }
  }
}

@if variable-exists(footer-left-font-size) {
  .nav-footer .nav-footer-left {
    font-size: $footer-left-font-size;
  }
}

@if variable-exists(footer-center-font-size) {
  .nav-footer .nav-footer-center {
    font-size: $footer-center-font-size;
  }
}

@if variable-exists(footer-right-font-size) {
  .nav-footer .nav-footer-right {
    font-size: $footer-right-font-size;
  }
}

.nav-footer-left .footer-items,
.nav-footer-center .footer-items,
.nav-footer-right .footer-items {
  display: inline-flex;
  padding-top: 0.3em;
  padding-bottom: 0.3em;
  margin-bottom: 0em;
}

.nav-footer-left .footer-items .nav-link,
.nav-footer-center .footer-items .nav-link,
.nav-footer-right .footer-items .nav-link {
  padding-left: 0.6em;
  padding-right: 0.6em;
}
.nav-footer-left {
  flex: 1 1 0px;
  text-align: left;
}
.nav-footer-right {
  flex: 1 1 0px;
  text-align: right;
}

.nav-footer-center {
  flex: 1 1 0px;
  min-height: 3em;
  text-align: center;
  .footer-items {
    justify-content: center;
  }
}

@include media-breakpoint-down(md) {
  .nav-footer-center {
    margin-top: 3em;
  }
}

.navbar .quarto-reader-toggle.reader .quarto-reader-toggle-btn {
  background-color: $navbar-fg;
  border-radius: 3px;
}

@include media-breakpoint-down(lg) {
  .quarto-reader-toggle {
    display: none;
  }
}

.quarto-reader-toggle.reader.quarto-navigation-tool .quarto-reader-toggle-btn {
  background-color: $sidebar-fg;
  border-radius: 3px;
}

.quarto-reader-toggle .quarto-reader-toggle-btn {
  display: inline-flex;
  padding-left: 0.2em;
  padding-right: 0.2em;
  margin-left: -0.2em;
  margin-right: -0.2em;
  text-align: center;
}

.navbar .quarto-reader-toggle:not(.reader) .bi::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGBA($navbar-fg)}" class="bi bi-body-text" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M0 .5A.5.5 0 0 1 .5 0h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 0 .5Zm0 2A.5.5 0 0 1 .5 2h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm9 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-9 2A.5.5 0 0 1 .5 4h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm5 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm-12 2A.5.5 0 0 1 .5 6h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Zm8 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-8 2A.5.5 0 0 1 .5 8h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm-7 2a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1h-8a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5Z"/></svg>');
}

.navbar .quarto-reader-toggle.reader .bi::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGBA($navbar-bg)}" class="bi bi-body-text" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M0 .5A.5.5 0 0 1 .5 0h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 0 .5Zm0 2A.5.5 0 0 1 .5 2h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm9 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-9 2A.5.5 0 0 1 .5 4h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm5 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm-12 2A.5.5 0 0 1 .5 6h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Zm8 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-8 2A.5.5 0 0 1 .5 8h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm-7 2a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1h-8a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5Z"/></svg>');
}

.sidebar-navigation .quarto-reader-toggle:not(.reader) .bi::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGBA($sidebar-fg)}" class="bi bi-body-text" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M0 .5A.5.5 0 0 1 .5 0h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 0 .5Zm0 2A.5.5 0 0 1 .5 2h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm9 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-9 2A.5.5 0 0 1 .5 4h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm5 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm-12 2A.5.5 0 0 1 .5 6h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Zm8 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-8 2A.5.5 0 0 1 .5 8h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm-7 2a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1h-8a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5Z"/></svg>');
}

.sidebar-navigation .quarto-reader-toggle.reader .bi::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#{colorToRGBA($sidebar-bg)}" class="bi bi-body-text" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M0 .5A.5.5 0 0 1 .5 0h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 0 .5Zm0 2A.5.5 0 0 1 .5 2h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm9 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-9 2A.5.5 0 0 1 .5 4h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm5 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Zm-12 2A.5.5 0 0 1 .5 6h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Zm8 0a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm-8 2A.5.5 0 0 1 .5 8h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm7 0a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5Zm-7 2a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1h-8a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5Z"/></svg>');
}

#quarto-back-to-top {
  display: none;
  position: fixed;
  bottom: 50px;
  background-color: $body-bg;
  border-radius: $border-radius;
  box-shadow: 0 0.2rem 0.5rem $text-muted, 0 0 0.05rem $text-muted;
  color: $text-muted;
  text-decoration: none;
  font-size: 0.9em;
  text-align: center;
  left: 50%;
  padding: 0.4rem 0.8rem;
  transform: translate(-50%, 0);
}
