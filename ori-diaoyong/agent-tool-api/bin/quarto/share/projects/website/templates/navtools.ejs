<% 
// If there are more than 2 icons appearing in the tools menu,
// wrap it to the next line
let toolCount = tools? tools.length : 0;
if (darkToggle) {
  toolCount++;
} 
if (readerToggle) {
  toolCount++;
}
if (search && search === "overlay") {
  toolCount++;
}
let dropDownAlignClz = '';
if (align && align === 'end') {
  dropDownAlignClz = ' dropdown-menu-end';
}
const isWide = toolCount > 2; %>

<% toolCount = 0 %>
<% tools = tools || [] %>
<div class="<%- className %><%- isWide ? ' tools-wide' : ''%><%- align === 'end' ? ' tools-end' : '' %>">
<% tools.forEach(tool => {  %>

  <% if (tool.menu) { %>
    <% toolDropdownId = 'quarto-navigation-tool-dropdown-' + toolCount %>
    <% toolCount = toolCount + 1 %>
    <div class="dropdown">
      <a href="<%- tool.href %>" title="<%- tool.text %>" id="<%- toolDropdownId %>" class="quarto-navigation-tool dropdown-toggle px-1" data-bs-toggle="dropdown" aria-expanded="false" aria-label="<%- tool['aria-label'] || tool.text %>"<%= tool.target ? ` target="${tool.target}"` : "" %>><i class="bi bi-<%- tool.icon %>"></i></a>
      <ul class="dropdown-menu<%- dropDownAlignClz %>" aria-labelledby="<%- toolDropdownId %>">
        <% tool.menu.forEach(item => { %> 
          <li>
            <a class="dropdown-item <%- className %>-item" href="<%- item.href %>"<%= item.target ? ` target="${item.target}"` : "" %>>
            <% if (item.icon) { %> 
              <i class="bi bi-<%- item.icon %> pe-1"></i>
            <% } %>
            <%- item.text %>
            </a>
          </li>
        <% }) %>     
      </ul>
    </div>
  <% } else { %>
    <a href="<%- tool.href %>" <%- tool.rel ? `rel="${tool.rel}"` : "" %> title="<%- tool.text %>" class="quarto-navigation-tool px-1" aria-label="<%- tool['aria-label'] || tool.text %>"<%= tool.target ? ` target="${tool.target}"` : "" %>><i class="bi bi-<%- tool.icon %>"></i></a>
  <% } %>
<% }) %>

<% if (darkToggle) { %>  
  <% partial('navdarktoggle.ejs', { classes: 'quarto-navigation-tool  px-1', language: language }) %>
<% } %>
<% if (readerToggle) { %>
  <% partial('navreadertoggle.ejs', { classes: 'quarto-navigation-tool px-1', language: language }) %>
<% } %>    
<% if (search && search === "overlay") { %>
    <% partial('navsearch.ejs', { classes: 'quarto-navigation-tool px-1', language: language }) %>
<% } %>
</div>