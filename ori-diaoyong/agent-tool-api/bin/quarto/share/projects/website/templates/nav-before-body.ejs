<%
const navbarToolsClass = 'quarto-navbar-tools';
const navbarTocLeft = nav['toc-location'] === "left" || nav['toc-location'] === "left-body";
const navbarTocRight = nav['toc-location'] === "right" || nav['toc-location'] === "right-body";
%>


<% /* Container that holds search results (when using the non-floating style.) */ %>
<div id="quarto-search-results"></div>

  
  <% /* The header will hold the navbar (if present) and/or a container for the collapsed sidebar */ %>
  <% if (nav.navbar || nav.sidebar) { %>  
  <header id="quarto-header" class="headroom fixed-top">

  <% if (nav.navbar) { %>  
    <nav class="navbar navbar-expand<%- nav.navbar["collapse-below"] %> <%- nav.navbar.background === "none" || nav.navbar.background === "body" ? "border-bottom" : "" %>" data-bs-theme="dark">
      <div class="navbar-container container-fluid">
      <% partial('navbrand.ejs', {
        navbar: nav.navbar
      }) %>
      <% if (nav.navbar.left || nav.navbar.right) { %>  
        <% if (nav.navbar.collapse) { %>
          <% if (nav.navbar.search) { %>
            <% partial('navsearch.ejs',  { classes: '', language: nav.language }) %>
          <% } %>
          <% partial('navtoggle.ejs', { language: nav.language }) %>
          <% partial('navcollapse.ejs') %>
            <% partial('navitems.ejs', { align: 'start', items: nav.navbar.left}) %>
            <% partial('navitems.ejs', { align: 'end', items: nav.navbar.right }) %>
          </div> <!-- /navcollapse -->
          <% partial('navtools.ejs', { align: 'none', tools: nav.navbar.tools, darkToggle: nav.navbar.darkToggle, readerToggle: nav.navbar.readerToggle, search: false, className: navbarToolsClass, language: nav.language }) %>
        <% } else { %>
          <% partial('navitems.ejs', { align: 'start', items: nav.navbar.left}) %>
          <% partial('navitems.ejs', { align: 'end', items: nav.navbar.right}) %>
          <% partial('navtools.ejs', { align: 'none', tools: nav.navbar.tools, darkToggle: nav.navbar.darkToggle, readerToggle: nav.navbar.readerToggle, search: false, className: navbarToolsClass, language: nav.language }) %>
          <% if (nav.navbar.search) { %>
            <% partial('navsearch.ejs',  { classes: '', language: nav.language }) %>
          <% } %>
        <% } %> 
      <% } else { %>
        <% partial('navtools.ejs', { align: 'end', tools: nav.navbar.tools, darkToggle: nav.navbar.darkToggle, readerToggle: nav.navbar.readerToggle, search: false, className: navbarToolsClass, language: nav.language }) %>
        <% if (nav.navbar.search) { %>
          <% partial('navsearch.ejs',  { classes: '', language: nav.language }) %>
        <% } %>
      <% } %>

      </div> <!-- /container-fluid -->
    </nav>
<% } %>

<% if (nav.sidebar && nav.layout) { %>
  <nav class="quarto-secondary-nav">
    <div class="container-fluid d-flex">
      <button type="button" class="quarto-btn-toggle btn"
      data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" 
      aria-controls="quarto-sidebar" aria-expanded="false" aria-label="<%- nav.language['toggle-sidebar'] %>"
      onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">
        <i class="bi bi-layout-text-sidebar-reverse"></i>
      </button>
      <% if (nav.showBreadCrumbs) { %>
        <h1 class="quarto-secondary-nav-title no-breadcrumbs"></h1>
        <a class="flex-grow-1" role="button" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" 
        aria-controls="quarto-sidebar" aria-expanded="false" aria-label="<%- nav.language['toggle-sidebar'] %>"
        onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
        </a>
      <% } else { %>
        <a class="flex-grow-1 no-decor" role="button" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" 
        aria-controls="quarto-sidebar" aria-expanded="false" aria-label="<%- nav.language['toggle-sidebar'] %>"
        onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
          <h1 class="quarto-secondary-nav-title"></h1>
        </a>     
      <% }  %>
      <% if (nav.sidebar && nav.sidebar.search) { %>
      <button type="button" class="btn quarto-search-button" aria-label="<%- nav.language['search'] %>" onclick="window.quartoOpenSearch();">
        <i class="bi bi-search"></i>
      </button>
      <% } %>
    </div>
  </nav>
<% } %>

</header>
<% } %> 

<% if (nav.layout) { %>

<!-- content -->
<% 
let gridClasses = "";
if (nav.layout === "article" || nav.layout === "full") {
  gridClasses = " page-columns page-rows-contents";
} 
%>
<div id="quarto-content" class="quarto-container<%- gridClasses %> page-layout-<%- nav.layout %><%- nav.navbar ? ' page-navbar': ''%>">

<!-- sidebar -->
<% if (nav.sidebar || navbarTocLeft) { %>
  <% partial('sidebar.ejs', { sidebar: nav.sidebar, sidebarStyle: nav.sidebarStyle, navbar: !!nav.navbar, toc: navbarTocLeft, language: nav.language }) %>
<% } %>

<!-- margin-sidebar -->
<% if (nav.layout === "article" || nav.layout === "full") { %>
    <div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
      <% if (nav.hasToc && navbarTocRight) { %>
        <div id="quarto-toc-target"></div>
      <% } %>
    </div>
<% } %>

<!-- main -->
<% if (nav.layout !== "custom") { %>
<main class="content" id="quarto-document-content">
<% } %>

<% } %>




