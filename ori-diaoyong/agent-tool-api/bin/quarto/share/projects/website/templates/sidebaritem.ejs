<% if (item.href && item.text && !item.contents) { %>
<li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="<%- item.href %>" class="sidebar-item-text sidebar-link<%- item.active ? " active" : "" %>"<%- item.target ? ` target="${item.target}"` : "" %>><% partial('navicon.ejs', { item }) %> <span class="menu-text"><%= item.text %></span></a>
  </div>
</li>
<% } else if (item.contents && item.text) { %>  
  <%
    // the section ID will ultimately be used in a selector, so the 
    // '.' means query selector will match an abbreviated ID with a class matching
    // what follows the dot. 
    sectionId = item.sectionId ? item.sectionId.replace(".", "") : undefined ; 
  %> 
  <% isCollapsed = collapse <= depth  && !item.expanded %>

  <li class="sidebar-item sidebar-item-section">
    <% if (item.contents.length > 0) { %>
      <div class="sidebar-item-container"> 
          <% if (item.href) { %>
            <a href="<%- item.href %>" class="sidebar-item-text sidebar-link<%- item.active ? " active" : "" %>"<%- item.target ? ` target="${item.target}"` : "" %>><% partial('navicon.ejs', { item }) %> <span class="menu-text"><%= item.text %></span></a>
          <% } else { %> 
            <a class="sidebar-item-text sidebar-link text-start<%- isCollapsed ? " collapsed" : "" %>" data-bs-toggle="collapse" data-bs-target="#<%- sectionId %>" aria-expanded="<%- isCollapsed ? "false" : "true" %>"><% partial('navicon.ejs', { item }) %> <span class="menu-text"><%= item.text %></span></a>
          <% }  %>      
          <a class="sidebar-item-toggle text-start<%- isCollapsed ?  " collapsed" : "" %>" data-bs-toggle="collapse" data-bs-target="#<%- sectionId %>" aria-expanded="<%- isCollapsed ? "false" : "true" %>" aria-label="<%- language['toggle-section'] %>">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="<%- sectionId %>" class="collapse list-unstyled sidebar-section depth<%-depth%> <%- isCollapsed ? "" : "show" %>">  
        <% item.contents.forEach(subItem => { %>
          <% partial('sidebaritem.ejs', { item: subItem, depth: depth + 1, collapse: collapse, borderColor: borderColor, language: language }) %>
        <% }) %>
      </ul>
    <% } else { %>
      <span class="sidebar-item-text sidebar-link text-start"><% partial('navicon.ejs', { item }) %> <span class="menu-text"><%= item.text %></span></span>
    <% } %>

  </li>
<% } else if (item.text && item.text.match(/^\-+$/)) { %>
<li class="px-0"><hr class="sidebar-divider hi <%- borderColor %>"></li>
<% } else if (item.text) { %>
  <li class="sidebar-item">
    <% partial('navicon.ejs', { item }) %> <span class="menu-text"><%= item.text %></span>
  </li>
<% } %>
