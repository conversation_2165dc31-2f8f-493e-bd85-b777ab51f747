<nav id="quarto-sidebar" class="sidebar collapse collapse-horizontal quarto-sidebar-collapse-item sidebar-navigation <%- sidebarStyle || (toc ? "floating" : "") %> overflow-auto">

  <% if (sidebar) { %>
  <% 
    let borderColor = sidebar['border-color'] ? "border-" + sidebar['border-color'] : "";

    let alignCss = ""
    if (sidebar.alignment === "center") {
      alignCss = "text-center";
    } else if (sidebar.alignment === "right") {
      alignCss = "text-end";
    } else {
      alignCss = "text-left";
    }

    // Whether there are tools that should be displayed to the user
    let needsTools = !sidebar || !!(sidebar.tools.length > 0 || (sidebar.search && sidebar.search === 'overlay') || sidebar.darkToggle || sidebar.readerToggle);
    // Where the tools should be added (depending upon what elements are present, the tools)
    // may be displayed in different locations, so this will determine where to display the tools
    // "logo", "title", "search", "fallthrough"

    let toolsLocation;
    // Under the title if that will be displayed
    if (sidebar.title && !navbar) {
      toolsLocation = "title";
    } else if (sidebar.logo) {
      toolsLocation = "logo";
    } else if (sidebar.search && sidebar.search !== "overlay") {
      toolsLocation = "search";
    } else {
      toolsLocation = "fallthrough";
    }
  %>


    
  <% if (sidebar.logo || (sidebar.title && !navbar)) { %>
    <div class="pt-lg-2 mt-2 <%= alignCss %> sidebar-header<%= sidebar.logo && sidebar.title ? ' sidebar-header-stacked' : '' %>">
    <% if (sidebar.logo) { %>
      <a href="/index.html" class="sidebar-logo-link">
      <img src="<%- sidebar.logo %>" alt="" class="sidebar-logo py-0 d-lg-inline d-none"/>
      </a>
    <% } %>
    <% if (needsTools && toolsLocation === "logo") { %>
      <% partial('navtools.ejs', { align: 'start', tools: sidebar.tools, className: 'sidebar-tools-main', darkToggle: sidebar.darkToggle, search: sidebar.search, readerToggle: sidebar.readerToggle, language: language })%>
    <% } %> 
    
    <% if (!navbar) { %>
    <% if (sidebar.title) { %>
    <div class="sidebar-title mb-0 py-0">
      <% if (!navbar) { %>
      <a href="/">
      <%- sidebar.title %>
      </a> 
      <% } %>  
      <% if (needsTools && toolsLocation === "title") { %>
        <% partial('navtools.ejs', { align: 'start', tools: sidebar.tools, className: 'sidebar-tools-main', darkToggle: sidebar.darkToggle, search: sidebar.search, readerToggle: sidebar.readerToggle, language: language })%>
      <% } %>  
    </div>
    <% } %>
    <% } %>
      </div>
    <% } %>

    <% if (sidebar.search) { %>
        <% if (sidebar.search !== "overlay") { %>
        <div class="<%- sidebar.search === "overlay" ? "d-flex " : "" %>mt-2 flex-shrink-0 align-items-center">
        <div class="sidebar-search">
        <% partial('navsearch.ejs',  { classes: '', language: language }) %>
        </div>
        </div>
        <% } %>
        <% if (needsTools && toolsLocation === "search") { %>
        <% partial('navtools.ejs', { align: 'start', tools: sidebar.tools, className: 'sidebar-tools-collapse', darkToggle: sidebar.darkToggle, search: sidebar.search, readerToggle: sidebar.readerToggle, language: language })%>
        <% } %>  
    <% } %>
    <% if (needsTools && toolsLocation === "fallthrough") { %>
        <% partial('navtools.ejs', { align: 'start', tools: sidebar.tools, className: 'sidebar-tools-main', darkToggle: sidebar.darkToggle, search: sidebar.search, readerToggle: sidebar.readerToggle, language: language })%>
    <% } %>

    <% if (sidebar.contents.length > 0) { %> 
    <div class="sidebar-menu-container"> 
    <ul class="list-unstyled mt-1">
    <% sidebar.contents.forEach(item => { %>
        <% partial('sidebaritem.ejs', { item, depth: 1, collapse: sidebar['collapse-level'], borderColor: borderColor, language: language }) %>
    <% }) %>
    </ul>
    </div>
    <% } %>
  <% } %>
   
  <% if (toc) { %>
    <div id="quarto-toc-target"></div>
  <% } %>

</nav>
<div id="quarto-sidebar-glass" class="quarto-sidebar-collapse-item" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" ></div>