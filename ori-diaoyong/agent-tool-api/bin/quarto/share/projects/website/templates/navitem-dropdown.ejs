

<% if (item.href) { %>
  <li>
    <a class="dropdown-item" href="<%- item.href %>"<%= item.rel ? ` rel="${item.rel}"` : "" %><%= item.target ? ` target="${item.target}"` : "" %>><% partial('navicon.ejs', { item }) %> <span class="dropdown-text"><%- item.text %></span></a>
  </li>  
<% } else if (item.menu) { %>
  <li class="dropdown">
    <a class="dropdown-toggle" href="#" id="<%- item.id %>" role="button" data-bs-toggle="dropdown" aria-expanded="false"<%= item.rel ? ` rel="${item.rel}"` : "" %><%= item.target ? ` target="${item.target}"` : "" %>>
      <% partial('navicon.ejs', { item }) %> <%- item.text %>
    </a>
    <ul class="dropdown-menu" aria-labelledby="<%- item.id %>">    
      <% for(const menuItem of item.menu) { %>
        <% partial('navitem-dropdown.ejs', { item: menuItem }) %>
      <% } %>
    </ul>
  </li>
<% } else if (item.text) { %>
  <% if (item.text.match(/^\-+$/)) { %>
    <li><hr class="dropdown-divider"></li>
  <% } else { %>
    <li class="dropdown-header"><% partial('navicon.ejs', { item }) %> <%- item.text %></li>
  <% } %>
<% } %>

