- name: toc
  alias: table-of-contents
  tags:
    formats: ["!man", "!$docbook-all", "!$jats-all"]
  schema: boolean
  description:
    short: "Include an automatically generated table of contents"
    long: |
      Include an automatically generated table of contents (or, in
      the case of `latex`, `context`, `docx`, `odt`,
      `opendocument`, `rst`, or `ms`, an instruction to create
      one) in the output document. This option has no effect
      if `standalone` is `false`.

      Note that if you are producing a PDF via `ms`, the table
      of contents will appear at the beginning of the
      document, before the title.  If you would prefer it to
      be at the end of the document, use the option
      `pdf-engine-opt: --no-toc-relocation`.

- name: toc-depth
  tags:
    formats: ["!man", "!$docbook-all", "!$jats-all", "!beamer"]
  schema: number
  description: |
    Specify the number of section levels to include in the table of contents.
    The default is 3

- name: toc-location
  schema:
    enum: ["body", "left", "right", "left-body", "right-body"]
  default: "right"
  tags:
    formats: [$html-doc]
  description:
    short: |
      Location for table of contents (`body`, `left`, `right` (default), `left-body`, `right-body`).
    long: |
      Location for table of contents:

      - `body`: Show the Table of Contents in the center body of the document. 
      - `left`: Show the Table of Contents in left margin of the document.
      - `right`(default): Show the Table of Contents in right margin of the document.
      - `left-body`: Show two Tables of Contents in both the center body and the left margin of the document.
      - `right-body`: Show two Tables of Contents in both the center body and the right margin of the document.
- name: toc-title
  schema: string
  tags:
    formats: [$epub-all, $odt-all, $office-all, $pdf-all, $html-doc, revealjs]
  description: The title used for the table of contents.

- name: toc-expand
  schema:
    anyOf:
      - number
      - boolean
  default: 1
  tags:
    formats: [$html-doc]
  description: |
    Specifies the depth of items in the table of contents that should be displayed as expanded in HTML output. Use `true` to expand all or `false` to collapse all.

- name: lof
  schema: boolean
  default: false
  tags:
    formats: [$pdf-all]
  description: Print a list of figures in the document.

- name: lot
  schema: boolean
  default: false
  tags:
    formats: [$pdf-all]
  description: Print a list of tables in the document.
