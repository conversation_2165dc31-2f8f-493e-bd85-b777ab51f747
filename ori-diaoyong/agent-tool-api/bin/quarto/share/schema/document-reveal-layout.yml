- name: auto-stretch
  tags:
    formats: [revealjs]
  schema: boolean
  default: true
  description: "For slides with a single top-level image, automatically stretch it to fill the slide."

- name: width
  tags:
    formats: [revealjs]
  schema:
    anyOf: [number, string]
  default: 1050
  description:
    short: "The 'normal' width of the presentation"
    long: |
      The "normal" width of the presentation, aspect ratio will
      be preserved when the presentation is scaled to fit different
      resolutions. Can be specified using percentage units.

- name: height
  tags:
    formats: [revealjs]
  schema:
    anyOf: [number, string]
  default: 700
  description:
    short: "The 'normal' height of the presentation"
    long: |
      The "normal" height of the presentation, aspect ratio will
      be preserved when the presentation is scaled to fit different
      resolutions. Can be specified using percentage units.

- name: margin
  tags:
    formats: [revealjs, typst]
  schema:
    anyOf:
      - number
      - object:
          closed: true
          properties:
            x:
              string:
                description: "Horizontal margin (e.g. 5cm)"
            y:
              string:
                description: "Vertical margin (e.g. 5cm)"
            top:
              string:
                description: "Top margin (e.g. 5cm)"
            bottom:
              string:
                description: "Bottom margin (e.g. 5cm)"
            left:
              string:
                description: "Left margin (e.g. 5cm)"
            right:
              string:
                description: "Right margin (e.g. 5cm)"
  default: 0.1
  description: |
    For `revealjs`, the factor of the display size that should remain empty around the content (e.g. 0.1).

    For `typst`, a dictionary with the fields defined in the Typst documentation:
    `x`, `y`, `top`, `bottom`, `left`, `right` (margins are specified in `cm` units,
    e.g. `5cm`).

- name: min-scale
  tags:
    formats: [revealjs]
  schema: number
  default: 0.2
  description: "Bounds for smallest possible scale to apply to content"

- name: max-scale
  tags:
    formats: [revealjs]
  schema: number
  default: 2.0
  description: "Bounds for largest possible scale to apply to content"

- name: center
  tags:
    formats: [revealjs]
  schema: boolean
  default: false
  description: "Vertical centering of slides"

- name: disable-layout
  tags:
    formats: [revealjs]
  schema: boolean
  default: false
  description: |
    Disables the default reveal.js slide layout (scaling and centering)

- name: code-block-height
  tags:
    formats: [revealjs]
  schema: string
  default: "500px"
  description: |
    Sets the maximum height for source code blocks that appear in the presentation.
