- name: documentclass
  schema:
    string:
      completions:
        - scrartcl
        - scrbook
        - scrreprt
        - scrlttr2
        - article
        - book
        - report
        - memoir
  tags:
    formats: [$pdf-all]
  default: scrartcl
  description: The document class.

- name: classoption
  schema:
    maybeArrayOf: string
  tags:
    formats: [$html-files, $pdf-all]
  description:
    short: Options for the document class,
    long: |
      For LaTeX/PDF output, the options set for the document
      class.

      For HTML output using KaTeX, you can render display
      math equations flush left using `classoption: fleqn`

- name: pagestyle
  schema:
    string:
      completions:
        - plain
        - empty
        - headings
  tags:
    formats: [$pdf-all]
  default: plain
  description: Control the `\pagestyle{}` for the document.

- name: papersize
  schema: string
  tags:
    formats: [$pdf-all, typst]
  description: |
    The paper size for the document.

- name: layout
  schema:
    maybeArrayOf: string
  tags:
    formats: [context]
  description:
    short: The options for margins and text layout for this document.
    long: |
      The options for margins and text layout for this document.

      See [ConTeXt Layout](https://wiki.contextgarden.net/Layout) for additional information.

- name: page-layout
  schema:
    enum: [article, full, custom]
  default: article
  tags:
    formats: [$html-doc]
  description: The page layout to use for this document (`article`, `full`, or `custom`)

- name: page-width
  tags:
    formats: [docx, $odt-all]
  schema: number
  description:
    short: |
      Target page width for output (used to compute columns widths for `layout` divs)
    long: |
      Target page width for output (used to compute columns widths for `layout` divs).
      Defaults to 6.5 inches, which corresponds to default letter page settings in 
      docx and odt.

- name: grid
  schema:
    object:
      closed: true
      properties:
        content-mode:
          enum: ["auto", "standard", "full", "slim"]
          description: "Defines whether to use the standard, slim, or full content grid or to automatically select the most appropriate content grid."
        sidebar-width:
          string:
            description: "The base width of the sidebar (left) column in an HTML page."
        margin-width:
          string:
            description: "The base width of the margin (right) column in an HTML page."
        body-width:
          string:
            description: "The base width of the body (center) column in an HTML page."
        gutter-width:
          string:
            description: "The width of the gutter that appears between columns in an HTML page."
  description:
    short: "Properties of the grid system used to layout Quarto HTML pages."

- name: appendix-style
  schema:
    anyOf:
      - enum: ["default", "plain", "none"]
  default: "default"
  tags:
    formats: [$html-doc]
  description:
    short: The layout of the appendix for this document (`none`, `plain`, or `default`)
    long: |
      The layout of the appendix for this document (`none`, `plain`, or `default`).

      To completely disable any styling of the appendix, choose the appendix style `none`. For minimal styling, choose `plain.`

- name: appendix-cite-as
  schema:
    anyOf:
      - boolean
      - maybeArrayOf:
          enum: ["display", "bibtex"]
  tags:
    formats: [$html-doc]
  description:
    short: Controls the formats which are provided in the citation section of the appendix (`false`, `display`, or `bibtex`).
    long: |
      Controls the formats which are provided in the citation section of the appendix.

      Use `false` to disable the display of the 'cite as' appendix. Pass one or more of `display` or `bibtex` to enable that
      format in 'cite as' appendix.

- name: title-block-style
  schema:
    anyOf:
      - enum: ["default", "plain", "manuscript", "none"]
  default: "default"
  tags:
    formats: [$html-doc]
  description:
    short: The layout of the title block for this document (`none`, `plain`, or `default`).
    long: |
      The layout of the title block for this document (`none`, `plain`, or `default`).

      To completely disable any styling of the title block, choose the style `none`. For minimal styling, choose `plain.`

- name: title-block-banner
  schema:
    anyOf:
      - string
      - boolean
  tags:
    formats: [$html-doc]
  description:
    short: Apply a banner style treatment to the title block.
    long: |
      Applies a banner style treatment for the title block. You may specify one of the following values:

      `true`
      : Will enable the banner style display and automatically select a background color based upon the theme.

      `<css color value>`
      : If you provide a CSS color value, the banner will be enabled and the background color set to the provided CSS color.

      `<path>`
      : If you provide the path to a file, the banner will be enabled and the background image will be set to the file path.

      See `title-block-banner-color` if you'd like to control the color of the title block banner text.

- name: title-block-banner-color
  schema: string
  tags:
    formats: [$html-doc]
  description:
    short: Sets the color of text elements in a banner style title block.
    long: |
      Sets the color of text elements in a banner style title block. Use one of the following values:

      `body` | `body-bg`
      : Will set the text color to the body text color or body background color, respectively.

      `<css color value>`
      : If you provide a CSS color value, the text color will be set to the provided CSS color.

- name: title-block-categories
  schema: boolean
  default: true
  tags:
    formats: [$html-doc]
  description:
    short: Enables or disables the display of categories in the title block.

- name: max-width
  schema: string
  tags:
    formats: [$html-files]
  description: Adds a css `max-width` to the body Element.

- name: margin-left
  schema: string
  tags:
    formats: [$html-files, context, $pdf-all]
  description:
    short: Sets the left margin of the document.
    long: |
      For HTML output, sets the `margin-left` property on the Body element.

      For LaTeX output, sets the left margin if `geometry` is not 
      used (otherwise `geometry` overrides this value)

      For ConTeXt output, sets the left margin if `layout` is not used, 
      otherwise `layout` overrides these.

      For `wkhtmltopdf` sets the left page margin.

- name: margin-right
  schema: string
  tags:
    formats: [$html-files, context, $pdf-all]
  description:
    short: Sets the right margin of the document.
    long: |
      For HTML output, sets the `margin-right` property on the Body element.

      For LaTeX output, sets the right margin if `geometry` is not 
      used (otherwise `geometry` overrides this value)

      For ConTeXt output, sets the right margin if `layout` is not used, 
      otherwise `layout` overrides these.

      For `wkhtmltopdf` sets the right page margin.

- name: margin-top
  schema: string
  tags:
    formats: [$html-files, context, $pdf-all]
  description:
    short: Sets the top margin of the document.
    long: |
      For HTML output, sets the `margin-top` property on the Body element.

      For LaTeX output, sets the top margin if `geometry` is not 
      used (otherwise `geometry` overrides this value)

      For ConTeXt output, sets the top margin if `layout` is not used, 
      otherwise `layout` overrides these.

      For `wkhtmltopdf` sets the top page margin.

- name: margin-bottom
  schema: string
  tags:
    formats: [$html-files, context, $pdf-all]
  description:
    short: Sets the bottom margin of the document.
    long: |
      For HTML output, sets the `margin-bottom` property on the Body element.

      For LaTeX output, sets the bottom margin if `geometry` is not 
      used (otherwise `geometry` overrides this value)

      For ConTeXt output, sets the bottom margin if `layout` is not used, 
      otherwise `layout` overrides these.

      For `wkhtmltopdf` sets the bottom page margin.

- name: geometry
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: Options for the geometry package.
    long: |
      Options for the [geometry](https://ctan.org/pkg/geometry) package. For example:

      ```yaml
      geometry:
        - top=30mm
        - left=20mm
        - heightrounded
      ```

- name: hyperrefoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description:
    short: Additional non-color options for the hyperref package.
    long: |
      Options for the [hyperref](https://ctan.org/pkg/hyperref) package. For example:

      ```yaml
      hyperrefoptions:
        - linktoc=all
        - pdfwindowui
        - pdfpagemode=FullScreen      
      ```

      To customize link colors, please see the [Quarto PDF reference](https://quarto.org/docs/reference/formats/pdf.html#colors).

- name: indent
  schema:
    anyOf:
      - boolean
      - string
  tags:
    formats: [$pdf-all, ms]
  default: false
  description:
    short: Whether to use document class settings for indentation.
    long: |
      Whether to use document class settings for indentation. If the document 
      class settings are not used, the default LaTeX template removes indentation 
      and adds space between paragraphs

      For groff (`ms`) documents, the paragraph indent, for example, `2m`.

- name: block-headings
  schema: boolean
  tags:
    formats: [$pdf-all]
  description:
    short: Make `\paragraph` and `\subparagraph` free-standing rather than run-in.
    long: |
      Make `\paragraph` and `\subparagraph` (fourth- and
      fifth-level headings, or fifth- and sixth-level with book
      classes) free-standing rather than run-in; requires further
      formatting to distinguish from `\subsubsection` (third- or
      fourth-level headings). Instead of using this option,
      [KOMA-Script](https://ctan.org/pkg/koma-script) can adjust headings 
      more extensively:

      ```yaml
      header-includes: |
        \RedeclareSectionCommand[
          beforeskip=-10pt plus -2pt minus -1pt,
          afterskip=1sp plus -1sp minus 1sp,
          font=\normalfont\itshape]{paragraph}
        \RedeclareSectionCommand[
          beforeskip=-10pt plus -2pt minus -1pt,
          afterskip=1sp plus -1sp minus 1sp,
          font=\normalfont\scshape,
          indent=0pt]{subparagraph}
      ```
