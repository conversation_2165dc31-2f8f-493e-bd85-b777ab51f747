- name: lightbox
  schema:
    anyOf:
      - boolean
      - enum: ["auto"]
      - object:
          closed: true
          properties:
            match:
              schema:
                enum: ["auto"]
              description:
                short: Set this to `auto` if you'd like any image to be given lightbox treatment.
                long: |
                  Set this to `auto` if you'd like any image to be given lightbox treatment. If you omit this, only images with the class `lightbox` will be given the lightbox treatment.
            effect:
              schema:
                enum: ["fade", "zoom", "none"]
              description: The effect that should be used when opening and closing the lightbox. One of `fade`, `zoom`, `none`. Defaults to `zoom`.
            desc-position:
              schema:
                enum: ["top", "bottom", "left", "right"]
              description: The position of the title and description when displaying a lightbox. One of `top`, `bottom`, `left`, `right`. Defaults to `bottom`.
            loop:
              boolean:
                description: Whether galleries should 'loop' to first image in the gallery if the user continues past the last image of the gallery. Boolean that defaults to `true`.
            css-class:
              string:
                description: A class name to apply to the lightbox to allow css targeting. This will replace the lightbox class with your custom class name.
  tags:
    formats: [$html-doc]
  description: Enable or disable lightbox treatment for images in this document.
