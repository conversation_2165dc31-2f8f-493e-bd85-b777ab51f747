aliases:
  epub-all: [epub, epub2, epub3]
  pdf-all: [latex, pdf, beamer]
  markdown-all: [markdown, gfm, commonmark, commonmark_x, markua, md]
  office-all: [docx, pptx]
  docbook-all: [docbook, docbook4, docbook5]
  odt-all: [odt, opendocument]
  html-doc: [html, html4, html5]
  html-pres: [slidy, slideous, s5, revealjs, dzslides]
  pres-all: [pptx, beamer, $html-pres]
  html-files: [$html-doc, $html-pres, dashboard]
  html-all: [$html-files, $epub-all]
  asciidoc-all: [asciidoc, asciidoctor]
  jats-all: [jats, jats_archiving, jats_articleauthoring, jats_publishing]

  # pandoc-all was created from
  # $ quarto pandoc --list-output-formats
  pandoc-all: [
      asciidoc,
      asciidoctor,
      beamer,
      biblatex,
      bibtex,
      chunkedhtml,
      commonmark,
      commonmark_x,
      context,
      csljson,
      docbook,
      docbook4,
      docbook5,
      docx,
      dokuwiki,
      dzslides,
      epub,
      epub2,
      epub3,
      fb2,
      gfm,
      haddock,
      html,
      html4,
      html5,
      icml,
      ipynb,
      jats,
      jats_archiving,
      jats_articleauthoring,
      jats_publishing,
      jira,
      json,
      latex,
      man,
      markdown,
      markdown_github,
      markdown_mmd,
      markdown_phpextra,
      markdown_strict,
      markua,
      mediawiki,
      ms,
      muse,
      native,
      odt,
      opendocument,
      opml,
      org,
      pdf,
      plain,
      pptx,
      revealjs,
      rst,
      rtf,
      s5,
      slideous,
      slidy,
      tei,
      texinfo,
      textile,
      typst,
      xwiki,
      zimwiki,

      # alias for 'commonmark'
      md,

      # synthesized formats
      dashboard,
    ]
