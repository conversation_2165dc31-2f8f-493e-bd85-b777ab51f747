- name: latex-auto-mk
  tags:
    formats: [pdf, beamer]
  schema: boolean
  default: true
  description:
    short: "Use Quarto's built-in PDF rendering wrapper"
    long: |
      Use Quarto's built-in PDF rendering wrapper (includes support 
      for automatically installing missing LaTeX packages)

- name: latex-auto-install
  tags:
    formats: [pdf, beamer]
  schema: boolean
  default: true
  description: "Enable/disable automatic LaTeX package installation"

- name: latex-min-runs
  tags:
    formats: [pdf, beamer]
  schema: number
  default: 1
  description: "Minimum number of compilation passes."

- name: latex-max-runs
  tags:
    formats: [pdf, beamer]
  schema: number
  default: 10
  description: "Maximum number of compilation passes."

- name: latex-clean
  tags:
    formats: [pdf, beamer]
  schema: boolean
  default: true
  description: "Clean intermediates after compilation."

- name: latex-makeindex
  tags:
    formats: [pdf, beamer]
  schema: string
  default: makeindex
  description: "Program to use for `makeindex`."

- name: latex-makeindex-opts
  tags:
    formats: [pdf, beamer]
  schema:
    arrayOf: string
  description: "Array of command line options for `makeindex`."

- name: latex-tlmgr-opts
  tags:
    formats: [pdf, beamer]
  schema:
    arrayOf: string
  description: "Array of command line options for `tlmgr`."

- name: latex-output-dir
  tags:
    formats: [pdf, beamer]
  schema: string
  description: "Output directory for intermediates and PDF."

- name: latex-tinytex
  tags:
    formats: [pdf, beamer]
  schema: boolean
  description: "Set to `false` to prevent an installation of TinyTex from being used to compile PDF documents."

- name: latex-input-paths
  tags:
    formats: [pdf, beamer]
  schema:
    arrayOf: string
  description: "Array of paths LaTeX should search for inputs."
