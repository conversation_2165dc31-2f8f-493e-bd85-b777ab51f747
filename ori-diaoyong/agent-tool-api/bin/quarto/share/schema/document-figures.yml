- name: fig-width
  schema: number
  description:
    short: "Default width for figures generated by Matplotlib or R graphics"
    long: |
      Default width for figures generated by Matplotlib or R graphics.

      Note that with the Jupyter engine, this option has no effect when
      provided at the cell level; it can only be provided with
      document or project metadata.

- name: fig-height
  schema: number
  description:
    short: "Default height for figures generated by Matplotlib or R graphics"
    long: |
      Default height for figures generated by Matplotlib or R graphics.

      Note that with the Jupyter engine, this option has no effect when
      provided at the cell level; it can only be provided with
      document or project metadata.

- name: fig-format
  schema:
    enum: [retina, png, jpeg, svg, pdf]
  description: "Default format for figures generated by Matplotlib or R graphics (`retina`, `png`, `jpeg`, `svg`, or `pdf`)"

- name: fig-dpi
  schema: number
  description:
    short: "Default DPI for figures generated by Matplotlib or R graphics"
    long: |
      Default DPI for figures generated by Matplotlib or R graphics.

      Note that with the Jupyter engine, this option has no effect when
      provided at the cell level; it can only be provided with
      document or project metadata.

- name: fig-asp
  tags:
    engine: knitr
  schema: number
  description:
    short: |
      The aspect ratio of the plot, i.e., the ratio of height/width.
    long: |
      The aspect ratio of the plot, i.e., the ratio of height/width. When `fig-asp` is specified,
      the height of a plot (the option `fig-height`) is calculated from `fig-width * fig-asp`.

      The `fig-asp` option is only available within the knitr engine.

- name: fig-responsive
  tags:
    formats: [$html-all]
  schema: boolean
  default: true
  description: Whether to make images in this document responsive.
