- name: about
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - enum: [jolla, trestles, solana, marquee, broadside]
      - ref: website-about
  description:
    short: Specifies that the page is an 'about' page and which template to use when laying out the page.
    # FIXME: How do we link to other schema descriptions in this style of documentation?
    long: |
      Specifies that the page is an 'about' page and which template to use when laying out the page.

      The allowed values are either:

      - one of the possible template values (`jolla`, `trestles`, `solana`, `marquee`, or `broadside`))
      - an object describing the 'about' page in more detail. See [About Pages](https://quarto.org/docs/websites/website-about.html) for more.
