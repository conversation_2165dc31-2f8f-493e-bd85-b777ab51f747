- id: date
  anyOf:
    - string
    - object:
        properties:
          value: string
          format: string
        required: [value]

- id: math-methods
  enum:
    values: [plain, webtex, gladtex, mathml, mathjax, katex]

- id: pandoc-format-request-headers
  arrayOf:
    arrayOf:
      schema: string
      length: 2

- id: pandoc-format-output-file
  anyOf:
    - path
    - enum:
        values: [null]
        hidden: true

- id: pandoc-format-filters
  arrayOf:
    anyOf:
      - path
      - object:
          properties:
            type: string
            path: path
          required: [path]
      - object:
          properties:
            type: string
            path: path
            at:
              enum:
                [
                  pre-ast,
                  post-ast,
                  pre-quarto,
                  post-quarto,
                  pre-render,
                  post-render,
                ]
          required: [path, at]
      - record:
          type:
            enum: [citeproc]

- id: pandoc-shortcodes
  arrayOf: path

- id: page-column
  enum:
    [
      body,
      body-outset,
      body-outset-left,
      body-outset-right,
      page,
      page-left,
      page-right,
      page-inset-left,
      page-inset-right,
      screen,
      screen-left,
      screen-right,
      screen-inset,
      screen-inset-shaded,
      screen-inset-left,
      screen-inset-right,
      margin,
    ]

- id: contents-auto
  object:
    properties:
      auto:
        anyOf:
          - boolean
          - maybeArrayOf: string
        description:
          short: Automatically generate sidebar contents.
          long: |
            Automatically generate sidebar contents. Pass `true` to include all documents
            in the site, a directory name to include only documents in that directory, 
            or a glob (or list of globs) to include documents based on a pattern. 

            Subdirectories will create sections (use an `index.qmd` in the directory to
            provide its title). Order will be alphabetical unless a numeric `order` field
            is provided in document metadata.

- id: navigation-item
  anyOf:
    - path
    - ref: navigation-item-object

- id: navigation-item-object
  object:
    closed: true
    properties:
      aria-label:
        string:
          description: "Accessible label for the item."
      file:
        hidden: true
        string:
          description: |
            Alias for href
      href:
        string:
          description: |
            Link to file contained with the project or external URL
      icon:
        string:
          description:
            short: Name of bootstrap icon (e.g. `github`, `twitter`, `share`)
            long: |
              Name of bootstrap icon (e.g. `github`, `twitter`, `share`)
              See <https://icons.getbootstrap.com/> for a list of available icons
      id:
        # "core identification"
        # this field is only used in typescript
        schema: string
        hidden: true
      menu:
        arrayOf:
          schema:
            ref: navigation-item
      text:
        string:
          description: |
            Text to display for item (defaults to the
            document title if not provided)
      url:
        hidden: true
        string:
          description: |
            Alias for href
      rel:
        string:
          description: |
            Value for rel attribute. Multiple space-separated values are permitted.
            See <https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/rel>
            for a details.
      target:
        string:
          description: |
            Value for target attribute.
            See <https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#attr-target>
            for details.

- id: giscus-themes
  enum:
    values:
      [
        light,
        light_high_contrast,
        light_protanopia,
        light_tritanopia,
        dark,
        dark_high_contrast,
        dark_protanopia,
        dark_tritanopia,
        dark_dimmed,
        transparent_dark,
        cobalt,
        purple_dark,
        noborder_light,
        noborder_dark,
        noborder_gray,
        preferred_color_scheme,
      ]

- id: comments
  anyOf:
    - enum: [false]
    - object:
        closed: true
        properties:
          utterances:
            object:
              closed: true
              properties:
                repo:
                  string:
                    description: The Github repo that will be used to store comments.
                label:
                  string:
                    description: The label that will be assigned to issues created by Utterances.
                theme:
                  string:
                    description:
                      short: The Github theme that should be used for Utterances.
                      long: |
                        The Github theme that should be used for Utterances
                        (`github-light`, `github-dark`, `github-dark-orange`,
                        `icy-dark`, `dark-blue`, `photon-dark`, `body-light`,
                        or `gruvbox-dark`)
                    completions:
                      - github-light
                      - github-dark
                      - github-dark-orange
                      - icy-dark
                      - dark-blue
                      - photon-dark
                      - body-light
                      - gruvbox-dark
                issue-term:
                  string:
                    description:
                      short: How posts should be mapped to Github issues
                      long: |
                        How posts should be mapped to Github issues
                        (`pathname`, `url`, `title` or `og:title`)
                    completions:
                      - pathname
                      - url
                      - title
                      - og:title
              required: [repo]
          giscus:
            object:
              closed: true
              properties:
                repo:
                  string:
                    description:
                      short: The Github repo that will be used to store comments.
                      long: |
                        The Github repo that will be used to store comments.

                        In order to work correctly, the repo must be public, with the giscus app installed, and 
                        the discussions feature must be enabled.
                repo-id:
                  string:
                    description:
                      short: The Github repository identifier.
                      long: |
                        The Github repository identifier.

                        You can quickly find this by using the configuration tool at [https://giscus.app](https://giscus.app).
                        If this is not provided, Quarto will attempt to discover it at render time.
                category:
                  string:
                    description:
                      short: The discussion category where new discussions will be created.
                      long: |
                        The discussion category where new discussions will be created. It is recommended 
                        to use a category with the **Announcements** type so that new discussions 
                        can only be created by maintainers and giscus.
                category-id:
                  string:
                    description:
                      short: The Github category identifier.
                      long: |
                        The Github category identifier.

                        You can quickly find this by using the configuration tool at [https://giscus.app](https://giscus.app).
                        If this is not provided, Quarto will attempt to discover it at render time.
                mapping:
                  anyOf:
                    - enum: [pathname, url, title, og:title]
                    - string
                  description:
                    short: The mapping between the page and the embedded discussion.
                    long: |
                      The mapping between the page and the embedded discussion. 

                      - `pathname`: The discussion title contains the page path
                      - `url`: The discussion title contains the page url
                      - `title`: The discussion title contains the page title
                      - `og:title`: The discussion title contains the `og:title` metadata value
                      - any other string or number: Any other strings will be passed through verbatim and a discussion title
                      containing that value will be used. Numbers will be treated
                      as a discussion number and automatic discussion creation is not supported.
                reactions-enabled:
                  boolean:
                    description: Display reactions for the discussion's main post before the comments.
                loading:
                  enum: [lazy]
                  description: "Specify `loading: lazy` to defer loading comments until the user scrolls near the comments container."
                input-position:
                  enum: [top, bottom]
                  description: Place the comment input box above or below the comments.
                theme:
                  anyOf:
                    - string
                    - ref: giscus-themes
                    - object:
                        closed: true
                        properties:
                          light:
                            anyOf:
                              - string
                              - ref: giscus-themes
                            description: The light theme name.
                          dark:
                            anyOf:
                              - string
                              - ref: giscus-themes
                            description: The dark theme name.

                  description:
                    short: The giscus theme to use when displaying comments.
                    long: |
                      The giscus theme to use when displaying comments. Light and dark themes are supported. If a single theme is provided by name, it will be used as light and dark theme. To use different themes, use `light` and `dark` key: 

                      ```yaml
                      website:
                        comments:
                          giscus:
                            light: light # giscus theme used for light website theme
                            dark: dark_dimmed # giscus theme used for dark website theme
                      ```
                language:
                  string:
                    description: The language that should be used when displaying the commenting interface.
              required: [repo]
          hypothesis:
            anyOf:
              - boolean
              - object:
                  closed: true
                  properties:
                    client-url:
                      string:
                        description: Override the default hypothesis client url with a custom client url.
                    openSidebar:
                      boolean:
                        default: false
                        description: Controls whether the sidebar opens automatically on startup.
                    showHighlights:
                      anyOf:
                        - boolean
                        - enum: ["always", "whenSidebarOpen", "never"]
                      default: "always"
                      description: Controls whether the in-document highlights are shown by default (`always`, `whenSidebarOpen` or `never`)
                    theme:
                      enum: ["classic", "clean"]
                      default: classic
                      description: Controls the overall look of the sidebar (`classic` or `clean`)
                    enableExperimentalNewNoteButton:
                      boolean:
                        default: false
                        description: |
                          Controls whether the experimental New Note button 
                          should be shown in the notes tab in the sidebar.
                    usernameUrl:
                      schema: string
                      description: |
                        Specify a URL to direct a user to, 
                        in a new tab. when they click on the annotation author 
                        link in the header of an annotation.
                    services:
                      arrayOf:
                        object:
                          properties:
                            apiUrl:
                              string:
                                description: The base URL of the service API.
                            authority:
                              string:
                                description: The domain name which the annotation service is associated with.
                            grantToken:
                              string:
                                description: An OAuth 2 grant token which the client can send to the service in order to get an access token for making authenticated requests to the service.
                            allowLeavingGroups:
                              boolean:
                                default: true
                                description: A flag indicating whether users should be able to leave groups of which they are a member.
                            enableShareLinks:
                              boolean:
                                default: true
                                description: A flag indicating whether annotation cards should show links that take the user to see an annotation in context.
                            groups:
                              anyOf:
                                - enum: ["$rpc:requestGroups"]
                                - arrayOf: string
                              description: An array of Group IDs or the literal string `$rpc:requestGroups`
                            icon:
                              string:
                                description: The URL to an image for the annotation service. This image will appear to the left of the name of the currently selected group.
                          required: [apiUrl, authority, grantToken]
                        description: |
                          Alternative annotation services which the client should 
                          connect to instead of connecting to the public Hypothesis 
                          service at hypothes.is.
                    branding:
                      object:
                        properties:
                          accentColor:
                            string:
                              description: Secondary color for elements of the commenting UI.
                          appBackgroundColor:
                            string:
                              description: The main background color of the commenting UI.
                          ctaBackgroundColor:
                            string:
                              description: The background color for call to action buttons.
                          selectionFontFamily:
                            string:
                              description: The font family for selection text in the annotation card.
                          annotationFontFamily:
                            string:
                              description: The font family for the actual annotation value that the user writes about the page or selection.
                        description: Settings to adjust the commenting sidebar's look and feel.
                    externalContainerSelector:
                      string:
                        description: A CSS selector specifying the containing element into which the sidebar iframe will be placed.
                    focus:
                      object:
                        properties:
                          user:
                            object:
                              properties:
                                username:
                                  string:
                                    description: The username of the user to focus on.
                                userid:
                                  string:
                                    description: The userid of the user to focus on.
                                displayName:
                                  string:
                                    description: The display name of the user to focus on.
                        required: [user]
                      description: Defines a focused filter set for the available annotations on a page.
                    requestConfigFromFrame:
                      object:
                        properties:
                          origin:
                            string:
                              description: Host url and port number of receiving iframe
                          ancestorLevel:
                            number:
                              description: Number of nested iframes deep the client is relative from the receiving iframe.
                    assetRoot:
                      string:
                        description: The root URL from which assets are loaded.
                    sidebarAppUrl:
                      string:
                        description: The URL for the sidebar application which displays annotations.
                        default: "https://hypothes.is/app.html"

- id: social-metadata
  object:
    closed: true
    properties:
      title:
        string:
          description:
            short: "The title of the page"
            long: |
              The title of the page. Note that by default Quarto will automatically 
              use the title metadata from the page. Specify this field if you’d like 
              to override the title for this provider.
      description:
        string:
          description:
            short: "A short description of the content."
            long: |
              A short description of the content. Note that by default Quarto will
              automatically  use the description metadata from the page. Specify this
              field if you’d like to override the description for this provider.
      image:
        path:
          description:
            short: "The path to a preview image for the content."
            long: |
              The path to a preview image for the content. By default, Quarto will use
              the `image` value from the format metadata. If you provide an 
              image, you may also optionally provide an `image-width` and `image-height`.
      image-alt:
        path:
          description:
            short: "The alt text for the preview image."
            long: |
              The alt text for the preview image. By default, Quarto will use
              the `image-alt` value from the format metadata. If you provide an 
              image, you may also optionally provide an `image-width` and `image-height`.
      image-width:
        number:
          description: "Image width (pixels)"
      image-height:
        number:
          description: "Image height (pixels)"

- id: page-footer-region
  anyOf:
    - string
    - arrayOf:
        ref: navigation-item

- id: sidebar-contents
  anyOf:
    - string
    - ref: contents-auto
    - arrayOf:
        anyOf:
          - ref: navigation-item
          - path
          - object:
              closed: true
              properties:
                section:
                  anyOf:
                    - string
                    - null
                contents:
                  ref: sidebar-contents
          - ref: contents-auto

- id: project-preview
  object:
    closed: true
    properties:
      port:
        number:
          description: Port to listen on (defaults to random value between 3000 and 8000)
      host:
        string:
          description: Hostname to bind to (defaults to 127.0.0.1)
      serve:
        description: Use an exernal application to preview the project.
        schema:
          ref: project-serve
      browser:
        boolean:
          description: Open a web browser to view the preview (defaults to true)
      watch-inputs:
        boolean:
          description: Re-render input files when they change (defaults to true)
      navigate:
        boolean:
          description: Navigate the browser automatically when outputs are updated (defaults to true)
      timeout:
        number:
          description: Time (in seconds) after which to exit if there are no active clients

- id: project-serve
  object:
    closed: true
    properties:
      cmd:
        string:
          description: |
            Serve project preview using the specified command.
            Interpolate the `--port` into the command using `{port}`.
      args:
        string:
          description: Additional command line arguments for preview command.
      env:
        object:
          description: Environment variables to set for preview command.
      ready:
        string:
          description: Regular expression for detecting when the server is ready.
    required: [cmd, ready]

- id: publish
  description: Sites published from project
  schema:
    object:
      closed: true
      properties:
        netlify:
          arrayOf:
            ref: publish-record
      description: "Sites published to Netlify"

- id: publish-record
  object:
    closed: true
    properties:
      id:
        string:
          description: "Unique identifier for site"
      url:
        string:
          description: "Published URL for site"

- id: twitter-card-config
  object:
    super:
      resolveRef: social-metadata
    closed: true
    properties:
      card-style:
        enum: [summary, summary_large_image]
        description:
          short: "Card style"
          long: |
            Card style (`summary` or `summary_large_image`).

            If this is not provided, the best style will automatically
            selected based upon other metadata. You can learn more about Twitter Card
            styles [here](https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards).
      creator:
        string:
          description: "`@username` of the content creator (must be a quoted string)"
      site:
        string:
          description: "`@username` of the website (must be a quoted string)"

- id: open-graph-config
  object:
    super:
      resolveRef: social-metadata
    closed: true
    properties:
      locale:
        string:
          description: "Locale of open graph metadata"
      site-name:
        string:
          description:
            short: "Name that should be displayed for the overall site"
            long: |
              Name that should be displayed for the overall site. If not explicitly 
              provided in the `open-graph` metadata, Quarto will use the website or
              book `title` by default.

- id: page-footer
  object:
    properties:
      left:
        ref: page-footer-region
        description: "Footer left content"
      right:
        ref: page-footer-region
        description: "Footer right content"
      center:
        ref: page-footer-region
        description: "Footer center content"
      border:
        anyOf:
          - boolean
          - string
        description: "Footer border (`true`, `false`, or a border color)"
      background:
        schema: string
        description: "Footer background color"
      foreground:
        schema: string
        description: "Footer foreground color"
    closed: true

- id: base-website
  object:
    closed: true
    properties:
      title:
        string:
          description: "Website title"
      description:
        string:
          description: "Website description"
      favicon:
        string:
          description: "The path to the favicon for this website"
      site-url:
        string:
          description: "Base URL for published website"
      site-path:
        string:
          description: |
            Path to site (defaults to `/`). Not required if you specify `site-url`.
      repo-url:
        string:
          description: "Base URL for website source code repository"
      repo-link-target:
        string:
          description: "The value of the target attribute for repo links"
      repo-link-rel:
        string:
          description: "The value of the rel attribute for repo links"
      repo-subdir:
        string:
          description: "Subdirectory of repository containing website"
      repo-branch:
        string:
          description: "Branch of website source code (defaults to `main`)"
      issue-url:
        string:
          description: "URL to use for the 'report an issue' repository action."
      repo-actions:
        maybeArrayOf:
          enum: [none, edit, source, issue]
          description:
            short: "Links to source repository actions"
            long: "Links to source repository actions (`none` or one or more of `edit`, `source`, `issue`)"
      reader-mode:
        boolean:
          description: |
            Displays a 'reader-mode' tool which allows users to hide the sidebar and table of contents when viewing a page.
      google-analytics:
        anyOf:
          - string
          - object:
              properties:
                tracking-id:
                  schema: string
                  description: "The Google tracking Id or measurement Id of this website."
                storage:
                  enum: [cookies, none]
                  description:
                    short: "Storage options for Google Analytics data"
                    long: |
                      Storage option for Google Analytics data using on of these two values:

                      `cookies`: Use cookies to store unique user and session identification (default).

                      `none`: Do not use cookies to store unique user and session identification.

                      For more about choosing storage options see [Storage](https://quarto.org/docs/websites/website-tools.html#storage).
                anonymize-ip:
                  schema: boolean
                  description:
                    short: "Anonymize the user ip address."
                    long: |
                      Anonymize the user ip address. For more about this feature, see 
                      [IP Anonymization (or IP masking) in Google Analytics](https://support.google.com/analytics/answer/2763052?hl=en).
                version:
                  enum: [3, 4]
                  description:
                    short: "The version number of Google Analytics to use."
                    long: |
                      The version number of Google Analytics to use. 

                      - `3`: Use analytics.js
                      - `4`: use gtag. 

                      This is automatically detected based upon the `tracking-id`, but you may specify it.
        description: "Enable Google Analytics for this website"
      cookie-consent:
        anyOf:
          - enum: [express, implied]
          - boolean
          - object:
              properties:
                type:
                  enum: [implied, express]
                  description:
                    short: "The type of consent that should be requested"
                    long: |
                      The type of consent that should be requested, using one of these two values:

                      - `implied` (default): This will notify the user that the site uses cookies and permit them to change preferences, but not block cookies unless the user changes their preferences.

                      - `express`: This will block cookies until the user expressly agrees to allow them (or continue blocking them if the user doesn’t agree).
                style:
                  enum: [simple, headline, interstitial, standalone]
                  description:
                    short: "The style of the consent banner that is displayed"
                    long: |
                      The style of the consent banner that is displayed:

                      - `simple` (default): A simple dialog in the lower right corner of the website.

                      - `headline`: A full width banner across the top of the website.

                      - `interstitial`: An semi-transparent overlay of the entire website.

                      - `standalone`: An opaque overlay of the entire website.
                palette:
                  enum: [light, dark]
                  description: "Whether to use a dark or light appearance for the consent banner (`light` or `dark`)."
                policy-url:
                  schema: string
                  description: "The url to the website’s cookie or privacy policy."
                language:
                  schema: string
                  description:
                    short: "The language to be used when diplaying the cookie consent prompt (defaults to document language)."
                    long: |
                      The language to be used when diplaying the cookie consent prompt specified using an IETF language tag.

                      If not specified, the document language will be used.
                prefs-text:
                  schema: string
                  description:
                    short: "The text to display for the cookie preferences link in the website footer."
        description:
          short: "Request cookie consent before enabling scripts that set cookies"
          long: |
            Quarto includes the ability to request cookie consent before enabling scripts that set cookies, using [Cookie Consent](https://www.cookieconsent.com/).

            The user’s cookie preferences will automatically control Google Analytics (if enabled) and can be used to control custom scripts you add as well. For more information see [Custom Scripts and Cookie Consent](https://quarto.org/docs/websites/website-tools.html#custom-scripts-and-cookie-consent).
      search:
        anyOf:
          - boolean
          - object:
              properties:
                location:
                  enum: [navbar, sidebar]
                  description: "Location for search widget (`navbar` or `sidebar`)"
                type:
                  enum: [overlay, textbox]
                  description: "Type of search UI (`overlay` or `textbox`)"
                limit:
                  schema: number
                  description: "Number of matches to display (defaults to 20)"
                collapse-after:
                  schema: number
                  description: "Matches after which to collapse additional results"
                copy-button:
                  schema: boolean
                  description: "Provide button for copying search link"
                keyboard-shortcut:
                  maybeArrayOf:
                    string:
                      description: "One or more keys that will act as a shortcut to launch search (single characters)"
                show-item-context:
                  schema:
                    anyOf:
                      - enum: ["tree", "parent", "root"]
                      - boolean
                  description: "Whether to include search result parents when displaying items in search results (when possible)."
                algolia:
                  object:
                    properties:
                      index-name:
                        schema: string
                        description: "The name of the index to use when performing a search"
                      application-id:
                        schema: string
                        description: "The unique ID used by Algolia to identify your application"
                      search-only-api-key:
                        schema: string
                        description: "The Search-Only API key to use to connect to Algolia"
                      analytics-events:
                        boolean:
                          description: "Enable tracking of Algolia analytics events"
                      show-logo:
                        boolean:
                          description: "Enable the display of the Algolia logo in the search results footer."
                      index-fields:
                        object:
                          properties:
                            href:
                              schema: string
                              description: "Field that contains the URL of index entries"
                            title:
                              schema: string
                              description: "Field that contains the title of index entries"
                            text:
                              schema: string
                              description: "Field that contains the text of index entries"
                            section:
                              schema: string
                              description: "Field that contains the section of index entries"
                          closed: true
                      params:
                        object:
                          description: "Additional parameters to pass when executing a search"
                    closed: true
                  description: "Use external Algolia search index"
              closed: true
        description: "Provide full text search for website"

      navbar:
        anyOf:
          - boolean
          - object:
              properties:
                title:
                  anyOf:
                    - string
                    - boolean
                  description: "The navbar title. Uses the project title if none is specified."
                logo:
                  path:
                    description: "Path to a logo image that will be displayed to the left of the title."
                logo-alt:
                  string:
                    description: "Alternate text for the logo image."
                logo-href:
                  string:
                    description: "Target href from navbar logo / title. By default, the logo and title link to the root page of the site (/index.html)."
                background:
                  anyOf:
                    - enum:
                        [
                          "primary",
                          "secondary",
                          "success",
                          "danger",
                          "warning",
                          "info",
                          "light",
                          "dark",
                        ]
                    - string
                  description: "The navbar's background color (named or hex color)."
                foreground:
                  anyOf:
                    - enum:
                        [
                          "primary",
                          "secondary",
                          "success",
                          "danger",
                          "warning",
                          "info",
                          "light",
                          "dark",
                        ]
                    - string
                  description: "The navbar's foreground color (named or hex color)."
                search:
                  boolean:
                    description: "Include a search box in the navbar."
                pinned:
                  boolean:
                    description: "Always show the navbar (keeping it pinned)."
                    default: false
                collapse:
                  boolean:
                    description: "Collapse the navbar into a menu when the display becomes narrow."
                    default: true
                collapse-below:
                  enum: [sm, md, lg, xl, xxl]
                  description: "The responsive breakpoint below which the navbar will collapse into a menu (`sm`, `md`, `lg` (default), `xl`, `xxl`)."
                  default: "lg"
                left:
                  arrayOf:
                    ref: navigation-item
                  description: "List of items for the left side of the navbar."
                right:
                  arrayOf:
                    ref: navigation-item
                  description: "List of items for the right side of the navbar."
                toggle-position:
                  schema:
                    enum: [left, right]
                  description: "The position of the collapsed navbar toggle when in responsive mode"
                  default: "left"

        description: "Top navigation options"

      sidebar:
        anyOf:
          - boolean
          - maybeArrayOf:
              object:
                properties:
                  id:
                    string:
                      description: "The identifier for this sidebar."
                  title:
                    anyOf:
                      - string
                      - boolean
                    description: "The sidebar title. Uses the project title if none is specified."
                  logo:
                    path:
                      description: "Path to a logo image that will be displayed in the sidebar."
                  search:
                    boolean:
                      description: "Include a search control in the sidebar."
                  tools:
                    arrayOf:
                      ref: navigation-item-object
                    description: "List of sidebar tools"
                  contents:
                    ref: sidebar-contents
                    description: "List of items for the sidebar"
                  style:
                    enum: ["docked", "floating"]
                    description: "The style of sidebar (`docked` or `floating`)."
                    default: "floating"
                  background:
                    anyOf:
                      - enum:
                          [
                            "primary",
                            "secondary",
                            "success",
                            "danger",
                            "warning",
                            "info",
                            "light",
                            "dark",
                          ]
                      - string
                    description: "The sidebar's background color (named or hex color)."
                  foreground:
                    anyOf:
                      - enum:
                          [
                            "primary",
                            "secondary",
                            "success",
                            "danger",
                            "warning",
                            "info",
                            "light",
                            "dark",
                          ]
                      - string
                    description: "The sidebar's foreground color (named or hex color)."
                  border:
                    boolean:
                      description: "Whether to show a border on the sidebar (defaults to true for 'docked' sidebars)"
                  alignment:
                    enum: ["left", "right", "center"]
                    description: "Alignment of the items within the sidebar (`left`, `right`, or `center`)"
                  collapse-level:
                    number:
                      description: "The depth at which the sidebar contents should be collapsed by default."
                      default: 2
                  pinned:
                    boolean:
                      description: "When collapsed, pin the collapsed sidebar to the top of the page."
                  header:
                    maybeArrayOf: string
                    description: "Markdown to place above sidebar content (text or file path)"
                  footer:
                    maybeArrayOf: string
                    description: "Markdown to place below sidebar content (text or file path)"
        description: "Side navigation options"
      body-header:
        string:
          description: "Markdown to insert at the beginning of each page’s body (below the title and author block)."
      body-footer:
        string:
          description: "Markdown to insert below each page’s body."
      margin-header:
        maybeArrayOf: string
        description: "Markdown to place above margin content (text or file path)"
      margin-footer:
        maybeArrayOf: string
        description: "Markdown to place below margin content (text or file path)"
      page-navigation:
        boolean:
          description: "Provide next and previous article links in footer"
      back-to-top-navigation:
        boolean:
          description: "Provide a 'back to top' navigation button"
      bread-crumbs:
        boolean:
          description: "Whether to show navigation breadcrumbs for pages more than 1 level deep"
      page-footer:
        anyOf:
          - string
          - ref: page-footer
        description: "Shared page footer"
      image:
        path:
          description: |
            Default site thumbnail image for `twitter` /`open-graph`
      image-alt:
        path:
          description: |
            Default site thumbnail image alt text for `twitter` /`open-graph`
      comments:
        schema:
          ref: comments

      open-graph:
        anyOf:
          - boolean
          - ref: open-graph-config
        description: "Publish open graph metadata"
      twitter-card:
        anyOf:
          - boolean
          - ref: twitter-card-config
        description: "Publish twitter card metadata"
      other-links:
        schema:
          ref: other-links
        tags:
          formats: [$html-doc]
        description: "A list of other links to appear below the TOC."
      code-links:
        schema:
          anyOf:
            - boolean
            - ref: code-links-schema
        tags:
          formats: [$html-doc]
        description: "A list of code links to appear with this document."

- id: book-schema
  schema:
    object:
      closed: true
      super:
        resolveRef: base-website
      properties:
        title:
          string:
            description: "Book title"
        subtitle:
          string:
            description: "Book subtitle"
        author:
          maybeArrayOf:
            anyOf: [string, object]
            description: "Author or authors of the book"
        date:
          string:
            description: "Book publication date"
        date-format:
          string:
            description: "Format string for dates in the book"
        abstract:
          string:
            description: "Book abstract"
        description:
          string:
            description: "Description metadata for HTML version of book"
        chapters:
          schema:
            ref: chapter-list
          description: "Book part and chapter files"
          hidden: true
        appendices:
          schema:
            ref: chapter-list
          description: "Book appendix files"
          hidden: true
        references:
          path:
            description: "Book references file"
        output-file:
          path:
            description: "Base name for single-file output (e.g. PDF, ePub)"
        cover-image:
          path:
            description: "Cover image (used in HTML and ePub formats)"
        cover-image-alt:
          string:
            description: "Alternative text for cover image (used in HTML format)"
        sharing:
          maybeArrayOf:
            enum: [twitter, facebook, linkedin]
            description: |
              Sharing buttons to include on navbar or sidebar
              (one or more of `twitter`, `facebook`, `linkedin`)
        downloads:
          maybeArrayOf:
            enum: [pdf, epub, docx]
            description: |
              Download buttons for other formats to include on navbar or sidebar
              (one or more of `pdf`, `epub`, and `docx`)
        tools:
          arrayOf:
            schema:
              ref: navigation-item
            description: "Custom tools for navbar or sidebar"
        doi:
          string:
            tags:
              formats: [$html-doc]
            description: The Digital Object Identifier for this book.

- id: chapter-item
  anyOf:
    - ref: navigation-item
    - object:
        properties:
          part:
            string:
              description: "Part title or path to input file"
          chapters:
            arrayOf:
              ref: navigation-item
            description: "Path to chapter input file"
        required: [part]

- id: chapter-list
  arrayOf:
    ref: chapter-item

- id: other-links
  arrayOf:
    object:
      properties:
        text:
          string:
            description: "The text for the link."
        href:
          string:
            description: "The href for the link."
        icon:
          string:
            description: "The bootstrap icon name for the link."
        rel:
          string:
            description: "The rel attribute value for the link."
        target:
          string:
            description: "The target attribute value for the link."
      required: [text, href]

- id: crossref-labels-schema
  string:
    completions:
      - alpha
      - arabic
      - roman

- id: epub-contributor
  anyOf:
    - string
    - maybeArrayOf:
        object:
          closed: true
          properties:
            role:
              string:
                description:
                  short: The role of this creator or contributor.
                  long: |
                    The role of this creator or contributor using 
                    [MARC relators](https://loc.gov/marc/relators/relaterm.html). Human readable
                    translations to commonly used relators (e.g. 'author', 'editor') will 
                    attempt to be automatically translated.
            file-as:
              string:
                description: An alternate version of the creator or contributor text used for alphabatizing.
            text:
              string:
                description: The text describing the creator or contributor (for example, creator name).

- id: format-language
  object:
    properties:
      toc-title-document: string
      toc-title-website: string
      related-formats-title: string
      related-notebooks-title: string
      callout-tip-title: string
      callout-note-title: string
      callout-warning-title: string
      callout-important-title: string
      callout-caution-title: string
      section-title-abstract: string
      section-title-footnotes: string
      section-title-appendices: string
      code-summary: string
      code-tools-menu-caption: string
      code-tools-show-all-code: string
      code-tools-hide-all-code: string
      code-tools-view-source: string
      code-tools-source-code: string
      search-no-results-text: string
      copy-button-tooltip: string
      copy-button-tooltip-success: string
      repo-action-links-edit: string
      repo-action-links-source: string
      repo-action-links-issue: string
      search-matching-documents-text: string
      search-copy-link-title: string
      search-hide-matches-text: string
      search-more-match-text: string
      search-more-matches-text: string
      search-clear-button-title: string
      search-text-placeholder: string
      search-detached-cancel-button-title: string
      search-submit-button-title: string
      crossref-fig-title: string
      crossref-tbl-title: string
      crossref-lst-title: string
      crossref-thm-title: string
      crossref-lem-title: string
      crossref-cor-title: string
      crossref-prp-title: string
      crossref-cnj-title: string
      crossref-def-title: string
      crossref-exm-title: string
      crossref-exr-title: string
      crossref-fig-prefix: string
      crossref-tbl-prefix: string
      crossref-lst-prefix: string
      crossref-ch-prefix: string
      crossref-apx-prefix: string
      crossref-sec-prefix: string
      crossref-eq-prefix: string
      crossref-thm-prefix: string
      crossref-lem-prefix: string
      crossref-cor-prefix: string
      crossref-prp-prefix: string
      crossref-cnj-prefix: string
      crossref-def-prefix: string
      crossref-exm-prefix: string
      crossref-exr-prefix: string
      crossref-lof-title: string
      crossref-lot-title: string
      crossref-lol-title: string
    errorDescription: "be a format language description object"

- id: website-about
  object:
    closed: true
    properties:
      id:
        string:
          description:
            short: "The target id for the about page."
            long: |
              The target id of this about page. When the about page is rendered, it will 
              place read the contents of a `div` with this id into the about template that you 
              have selected (and replace the contents with the rendered about content).

              If no such `div` is defined on the page, a `div` with this id will be created 
              and appended to the end of the page.
      template:
        anyOf:
          - enum: [jolla, trestles, solana, marquee, broadside]
          - path
        description:
          short: "The template to use to layout this about page."
          long: |
            The template to use to layout this about page. Choose from:

            - `jolla`
            - `trestles`
            - `solana`
            - `marquee`
            - `broadside`
      image:
        path:
          description:
            short: "The path to the main image on the about page."
            long: |
              The path to the main image on the about page. If not specified, 
              the `image` provided for the document itself will be used.
      image-alt:
        path:
          description: "The alt text for the main image on the about page."
      image-title:
        path:
          description: "The title for the main image on the about page."
      image-width:
        string:
          description:
            short: "A valid CSS width for the about page image."
            long: |
              A valid CSS width for the about page image.
      image-shape:
        enum: [rectangle, round, rounded]
        description:
          short: "The shape of the image on the about page."
          long: |
            The shape of the image on the about page.

            - `rectangle`
            - `round`
            - `rounded`
      links:
        arrayOf:
          ref: navigation-item

- id: website-listing
  object:
    closed: true
    properties:
      id:
        string:
          description:
            short: "The id of this listing."
            long: |
              The id of this listing. When the listing is rendered, it will 
              place the contents into a `div` with this id. If no such `div` is defined on the 
              page, a `div` with this id will be created and appended to the end of the page.

              If no `id` is provided for a listing, Quarto will synthesize one when rendering the page.
      type:
        enum: [default, table, grid, custom]
        description:
          short: "The type of listing to create."
          long: |
            The type of listing to create. Choose one of:

            - `default`: A blog style list of items
            - `table`: A table of items
            - `grid`: A grid of item cards
            - `custom`: A custom template, provided by the `template` field
      contents:
        maybeArrayOf:
          anyOf:
            - string
            - ref: website-listing-contents-object
        description: "The files or path globs of Quarto documents or YAML files that should be included in the listing."
      sort:
        anyOf:
          - boolean
          - maybeArrayOf: string
        description:
          short: "Sort items in the listing by these fields."
          long: |
            Sort items in the listing by these fields. The sort key is made up of a 
            field name followed by a direction `asc` or `desc`.

            For example:
            `date asc`

            Use `sort:false` to use the unsorted original order of items.
      max-items:
        number:
          description: The maximum number of items to include in this listing.
      page-size:
        number:
          description: The number of items to display on a page.
      sort-ui:
        anyOf:
          - boolean
          - arrayOf: string
        description:
          short: "Shows or hides the sorting control for the listing."
          long: |
            Shows or hides the sorting control for the listing. To control the 
            fields that will be displayed in the sorting control, provide a list
            of field names.
      filter-ui:
        anyOf:
          - boolean
          - arrayOf: string
        description:
          short: "Shows or hides the filtering control for the listing."
          long: |
            Shows or hides the filtering control for the listing. To control the 
            fields that will be used to filter the listing, provide a list
            of field names. By default all fields of the listing will be used
            when filtering.
      categories:
        anyOf:
          - boolean
          - enum: [numbered, unnumbered, cloud]
        description:
          short: "Display item categories from this listing in the margin of the page."
          long: |
            Display item categories from this listing in the margin of the page.

              - `numbered`: Category list with number of items
              - `unnumbered`: Category list
              - `cloud`: Word cloud style categories

      feed:
        anyOf:
          - boolean
          - object:
              closed: true
              properties:
                items:
                  number:
                    description: |
                      The number of items to include in your feed. Defaults to 20.
                type:
                  enum: [full, partial, metadata]
                  description:
                    short: Whether to include full or partial content in the feed.
                    long: |
                      Whether to include full or partial content in the feed.

                      - `full` (default): Include the complete content of the document in the feed.
                      - `partial`: Include only the first paragraph of the document in the feed.
                      - `metadata`: Use only the title, description, and other document metadata in the feed.
                title:
                  string:
                    description:
                      short: The title for this feed.
                      long: |
                        The title for this feed. Defaults to the site title provided the Quarto project.
                image:
                  path:
                    description:
                      short: The path to an image for this feed.
                      long: |
                        The path to an image for this feed. If not specified, the image for the page the listing 
                        appears on will be used, otherwise an image will be used if specified for the site 
                        in the Quarto project.
                description:
                  string:
                    description:
                      short: The description of this feed.
                      long: |
                        The description of this feed. If not specified, the description for the page the 
                        listing appears on will be used, otherwise the description 
                        of the site will be used if specified in the Quarto project.
                language:
                  string:
                    description:
                      short: The language of the feed.
                      long: |
                        The language of the feed. Omitted if not specified. 
                        See [https://www.rssboard.org/rss-language-codes](https://www.rssboard.org/rss-language-codes)
                        for a list of valid language codes.
                categories:
                  maybeArrayOf:
                    string:
                      description: A list of categories for which to create separate RSS feeds containing only posts with that category
                xml-stylesheet:
                  path:
                    description: The path to an XML stylesheet (XSL file) used to style the RSS feed.
        description: Enables an RSS feed for the listing.
      date-format:
        string:
          description:
            short: "The date format to use when displaying dates (e.g. d-M-yyy)."
            long: |
              The date format to use when displaying dates (e.g. d-M-yyy). 
              Learn more about supported date formatting values [here](https://deno.land/std@0.125.0/datetime).
      max-description-length:
        number:
          description:
            short: "The maximum length (in characters) of the description displayed in the listing."
            long: |
              The maximum length (in characters) of the description displayed in the listing.
              Defaults to 175.
      image-placeholder:
        string:
          description: "The default image to use if an item in the listing doesn't have an image."
      image-align:
        enum: [left, right]
        description: In `default` type listings, whether to place the image on the right or left side of the post content (`left` or `right`).
      image-height:
        string:
          description:
            short: "The height of the image being displayed."
            long: |
              The height of the image being displayed (a CSS height string).

              The width is automatically determined and the image will fill the rectangle without scaling (cropped to fill).
      grid-columns:
        number:
          description:
            short: "In `grid` type listings, the number of columns in the grid display."
            long: |
              In grid type listings, the number of columns in the grid display.
              Defaults to 3.
      grid-item-border:
        boolean:
          description:
            short: "In `grid` type listings, whether to display a border around the item card."
            long: |
              In grid type listings, whether to display a border around the item card. Defaults to `true`.
      grid-item-align:
        enum: [left, right, center]
        description:
          short: "In `grid` type listings, the alignment of the content within the card."
          long: |
            In grid type listings, the alignment of the content within the card (`left` (default), `right`, or `center`).
      table-striped:
        boolean:
          description:
            short: "In `table` type listings, display the table rows with alternating background colors."
            long: |
              In table type listings, display the table rows with alternating background colors.
              Defaults to `false`.
      table-hover:
        boolean:
          description:
            short: "In `table` type listings, highlight rows of the table when the user hovers the mouse over them."
            long: |
              In table type listings, highlight rows of the table when the user hovers the mouse over them.
              Defaults to false.
      template:
        path:
          description:
            short: "The path to a custom listing template."
            long: |
              The path to a custom listing template.
      template-params:
        schema: object
        description: "Parameters that are passed to the custom template."
      fields:
        arrayOf: string
        description:
          short: "The list of fields to include in this listing"
          long: |
            The list of fields to include in this listing.
      field-display-names:
        object:
          description:
            short: "A mapping of display names for listing fields."
            long: |
              A mapping that provides display names for specific fields. For example, to display the title column as ‘Report’ in a table listing you would write:

              ```yaml
              listing:
                field-display-names:
                title: "Report"
              ```
      field-types:
        object:
          description:
            short: "Provides the date type for the field of a listing item."
            long: |
              Provides the date type for the field of a listing item. Unknown fields are treated
              as strings unless a type is provided. Valid types are `date`, `number`.
      field-links:
        arrayOf: string
        description:
          short: "This list of fields to display as links in a table listing."
          long: |
            The list of fields to display as hyperlinks to the source document 
            when the listing type is a table. By default, only the `title` or 
            `filename` is displayed as a link.
      field-required:
        arrayOf: string
        description:
          short: "Fields that items in this listing must have populated."
          long: |
            Fields that items in this listing must have populated.
            If a listing is rendered and one more items in this listing 
            is missing a required field, an error will occur and the render will.
      include:
        maybeArrayOf: object
        description: "Items with matching field values will be included in the listing."
      exclude:
        maybeArrayOf: object
        description: "Items with matching field values will be excluded from the listing."

- id: website-listing-contents-object
  object:
    properties:
      author:
        maybeArrayOf: string
      date: string
      title: string
      subtitle: string

- id: csl-date
  anyOf:
    - string
    - maybeArrayOf: number
    - object:
        properties:
          year:
            number:
              description: The year
          month:
            number:
              description: The month
          day:
            number:
              description: The day

- id: csl-person
  anyOf:
    - maybeArrayOf: string
    - maybeArrayOf:
        object:
          properties:
            family-name:
              string:
                description: The family name.
            given-name:
              string:
                description: The given name.

- id: csl-number
  anyOf:
    - number
    - string

- id: csl-item-shared
  object:
    properties:
      abstract-url:
        string:
          description: A url to the abstract for this item.
      accessed:
        ref: csl-date
        description: Date the item has been accessed.
      annote:
        string:
          description:
            short: Short markup, decoration, or annotation to the item (e.g., to indicate items included in a review).
            long: |
              Short markup, decoration, or annotation to the item (e.g., to indicate items included in a review);

              For descriptive text (e.g., in an annotated bibliography), use `note` instead
      archive:
        string:
          description: Archive storing the item
      archive-collection:
        string:
          description: Collection the item is part of within an archive.
      archive_collection:
        schema: string
        hidden: true
      archive-location:
        string:
          description: Storage location within an archive (e.g. a box and folder number).
      archive_location:
        schema: string
        hidden: true
      archive-place:
        string:
          description: Geographic location of the archive.
      authority:
        string:
          description: Issuing or judicial authority (e.g. "USPTO" for a patent, "Fairfax Circuit Court" for a legal case).
      available-date:
        ref: csl-date
        description:
          short: Date the item was initially available
          long: |
            Date the item was initially available (e.g. the online publication date of a journal 
            article before its formal publication date; the date a treaty was made available for signing).
      call-number:
        string:
          description: Call number (to locate the item in a library).
      chair:
        ref: csl-person
        description: The person leading the session containing a presentation (e.g. the organizer of the `container-title` of a `speech`).
      chapter-number:
        ref: csl-number
        description: Chapter number (e.g. chapter number in a book; track number on an album).
      citation-key:
        string:
          description:
            short: Identifier of the item in the input data file (analogous to BiTeX entrykey).
            long: |
              Identifier of the item in the input data file (analogous to BiTeX entrykey);

              Use this variable to facilitate conversion between word-processor and plain-text writing systems;
              For an identifer intended as formatted output label for a citation 
              (e.g. “Ferr78”), use `citation-label` instead
      citation-label:
        string:
          description:
            short: Label identifying the item in in-text citations of label styles (e.g. "Ferr78").
            long: |
              Label identifying the item in in-text citations of label styles (e.g. "Ferr78");

              May be assigned by the CSL processor based on item metadata; For the identifier of the item 
              in the input data file, use `citation-key` instead
      citation-number:
        schema:
          ref: csl-number
          description: Index (starting at 1) of the cited reference in the bibliography (generated by the CSL processor).
        hidden: true
      collection-editor:
        ref: csl-person
        description: Editor of the collection holding the item (e.g. the series editor for a book).
      collection-number:
        ref: csl-number
        description: Number identifying the collection holding the item (e.g. the series number for a book)
      collection-title:
        string:
          description: Title of the collection holding the item (e.g. the series title for a book; the lecture series title for a presentation).
      compiler:
        ref: csl-person
        description: Person compiling or selecting material for an item from the works of various persons or bodies (e.g. for an anthology).
      composer:
        ref: csl-person
        description: Composer (e.g. of a musical score).
      container-author:
        ref: csl-person
        description: Author of the container holding the item (e.g. the book author for a book chapter).
      container-title:
        string:
          description:
            short: Title of the container holding the item.
            long: |
              Title of the container holding the item (e.g. the book title for a book chapter, 
              the journal title for a journal article; the album title for a recording; 
              the session title for multi-part presentation at a conference)
      container-title-short:
        string:
          description: Short/abbreviated form of container-title;
        hidden: true
      contributor:
        ref: csl-person
        description: A minor contributor to the item; typically cited using “with” before the name when listed in a bibliography.
      curator:
        ref: csl-person
        description: Curator of an exhibit or collection (e.g. in a museum).
      dimensions:
        string:
          description: Physical (e.g. size) or temporal (e.g. running time) dimensions of the item.
      director:
        ref: csl-person
        description: Director (e.g. of a film).
      division:
        string:
          description: Minor subdivision of a court with a `jurisdiction` for a legal item
      DOI:
        schema: string
        hidden: true
      edition:
        ref: csl-number
        description: (Container) edition holding the item (e.g. "3" when citing a chapter in the third edition of a book).
      editor:
        ref: csl-person
        description: The editor of the item.
      editorial-director:
        ref: csl-person
        description: Managing editor ("Directeur de la Publication" in French).
      editor-translator:
        ref: csl-person
        description:
          short: Combined editor and translator of a work.
          long: |
            Combined editor and translator of a work.

            The citation processory must be automatically generate if editor and translator variables 
            are identical; May also be provided directly in item data.
      event:
        schema: string
        hidden: true
      event-date:
        ref: csl-date
        description: Date the event related to an item took place.
      event-title:
        string:
          description: Name of the event related to the item (e.g. the conference name when citing a conference paper; the meeting where presentation was made).
      event-place:
        string:
          description: Geographic location of the event related to the item (e.g. "Amsterdam, The Netherlands").
      executive-producer:
        ref: csl-person
        description: Executive producer of the item (e.g. of a television series).
      first-reference-note-number:
        schema:
          ref: csl-number
        description:
          short: Number of a preceding note containing the first reference to the item.
          long: |
            Number of a preceding note containing the first reference to the item

            Assigned by the CSL processor; Empty in non-note-based styles or when the item hasn't 
            been cited in any preceding notes in a document
        hidden: true
      fulltext-url:
        string:
          description: A url to the full text for this item.
      genre:
        string:
          description:
            short: Type, class, or subtype of the item
            long: |
              Type, class, or subtype of the item (e.g. "Doctoral dissertation" for a PhD thesis; "NIH Publication" for an NIH technical report);

              Do not use for topical descriptions or categories (e.g. "adventure" for an adventure movie)
      guest:
        ref: csl-person
        description: Guest (e.g. on a TV show or podcast).
      host:
        ref: csl-person
        description: Host of the item (e.g. of a TV show or podcast).
      id:
        anyOf:
          - string
          - number
        description: A value which uniquely identifies this item.
      illustrator:
        ref: csl-person
        description: Illustrator (e.g. of a children’s book or graphic novel).
      interviewer:
        ref: csl-person
        description: Interviewer (e.g. of an interview).
      isbn:
        string:
          description: International Standard Book Number (e.g. "978-3-8474-1017-1").
      ISBN:
        schema: string
        hidden: true
      issn:
        string:
          description: International Standard Serial Number.
      ISSN:
        schema: string
        hidden: true
      issue:
        ref: csl-number
        description:
          short: Issue number of the item or container holding the item
          long: |
            Issue number of the item or container holding the item (e.g. "5" when citing a 
            journal article from journal volume 2, issue 5);

            Use `volume-title` for the title of the issue, if any.
      issued:
        ref: csl-date
        description: Date the item was issued/published.
      jurisdiction:
        string:
          description: Geographic scope of relevance (e.g. "US" for a US patent; the court hearing a legal case).
      keyword:
        string:
          description: Keyword(s) or tag(s) attached to the item.
      language:
        string:
          description:
            short: The language of the item (used only for citation of the item).
            long: |
              The language of the item (used only for citation of the item).

              Should be entered as an ISO 639-1 two-letter language code (e.g. "en", "zh"), 
              optionally with a two-letter locale code (e.g. "de-DE", "de-AT").

              This does not change the language of the item, instead it documents 
              what language the item uses (which may be used in citing the item).
      license:
        string:
          description:
            short: The license information applicable to an item.
            long: |
              The license information applicable to an item (e.g. the license an article 
              or software is released under; the copyright information for an item; 
              the classification status of a document)
      locator:
        ref: csl-number
        description:
          short: A cite-specific pinpointer within the item.
          long: |
            A cite-specific pinpointer within the item (e.g. a page number within a book, 
            or a volume in a multi-volume work).

            Must be accompanied in the input data by a label indicating the locator type 
            (see the Locators term list).

      medium:
        string:
          description: Description of the item’s format or medium (e.g. "CD", "DVD", "Album", etc.)
      narrator:
        ref: csl-person
        description: Narrator (e.g. of an audio book).
      note:
        string:
          description: Descriptive text or notes about an item (e.g. in an annotated bibliography).
      number:
        ref: csl-number
        description: Number identifying the item (e.g. a report number).
      number-of-pages:
        ref: csl-number
        description: Total number of pages of the cited item.
      number-of-volumes:
        ref: csl-number
        description: Total number of volumes, used when citing multi-volume books and such.
      organizer:
        ref: csl-person
        description: Organizer of an event (e.g. organizer of a workshop or conference).
      original-author:
        ref: csl-person
        description:
          short: The original creator of a work.
          long: |
            The original creator of a work (e.g. the form of the author name 
            listed on the original version of a book; the historical author of a work; 
            the original songwriter or performer for a musical piece; the original 
            developer or programmer for a piece of software; the original author of an 
            adapted work such as a book adapted into a screenplay)
      original-date:
        ref: csl-date
        description: Issue date of the original version.
      original-publisher:
        string:
          description: Original publisher, for items that have been republished by a different publisher.
      original-publisher-place:
        string:
          description: Geographic location of the original publisher (e.g. "London, UK").
      original-title:
        string:
          description: Title of the original version (e.g. "Война и мир", the untranslated Russian title of "War and Peace").
      page:
        ref: csl-number
        description: Range of pages the item (e.g. a journal article) covers in a container (e.g. a journal issue).
      page-first:
        ref: csl-number
        description: First page of the range of pages the item (e.g. a journal article) covers in a container (e.g. a journal issue).
      page-last:
        ref: csl-number
        description: Last page of the range of pages the item (e.g. a journal article) covers in a container (e.g. a journal issue).
      part-number:
        ref: csl-number
        description:
          short: Number of the specific part of the item being cited (e.g. part 2 of a journal article).
          long: |
            Number of the specific part of the item being cited (e.g. part 2 of a journal article).

            Use `part-title` for the title of the part, if any.
      part-title:
        string:
          description: Title of the specific part of an item being cited.
      pdf-url:
        string:
          description: A url to the pdf for this item.
      performer:
        ref: csl-person
        description: Performer of an item (e.g. an actor appearing in a film; a muscian performing a piece of music).
      pmcid:
        string:
          description: PubMed Central reference number.
      PMCID:
        schema: string
        hidden: true
      pmid:
        string:
          description: PubMed reference number.
      PMID:
        schema: string
        hidden: true
      printing-number:
        ref: csl-number
        description: Printing number of the item or container holding the item.
      producer:
        ref: csl-person
        description: Producer (e.g. of a television or radio broadcast).
      public-url:
        string:
          description: A public url for this item.
      publisher:
        string:
          description: The publisher of the item.
      publisher-place:
        string:
          description: The geographic location of the publisher.
      recipient:
        ref: csl-person
        description: Recipient (e.g. of a letter).
      reviewed-author:
        ref: csl-person
        description: Author of the item reviewed by the current item.
      reviewed-genre:
        string:
          description: Type of the item being reviewed by the current item (e.g. book, film).
      reviewed-title:
        string:
          description: Title of the item reviewed by the current item.
      scale:
        string:
          description: Scale of e.g. a map or model.
      script-writer:
        ref: csl-person
        description: Writer of a script or screenplay (e.g. of a film).
      section:
        ref: csl-number
        description: Section of the item or container holding the item (e.g. "§2.0.1" for a law; "politics" for a newspaper article).
      series-creator:
        ref: csl-person
        description: Creator of a series (e.g. of a television series).
      source:
        string:
          description: Source from whence the item originates (e.g. a library catalog or database).
      status:
        string:
          description: Publication status of the item (e.g. "forthcoming"; "in press"; "advance online publication"; "retracted")
      submitted:
        ref: csl-date
        description: Date the item (e.g. a manuscript) was submitted for publication.
      supplement-number:
        ref: csl-number
        description: Supplement number of the item or container holding the item (e.g. for secondary legal items that are regularly updated between editions).
      title-short:
        string:
          description: Short/abbreviated form of`title`.
        hidden: true
      translator:
        ref: csl-person
        description: Translator
      type:
        enum:
          [
            "article",
            "article-journal",
            "article-magazine",
            "article-newspaper",
            "bill",
            "book",
            "broadcast",
            "chapter",
            "classic",
            "collection",
            "dataset",
            "document",
            "entry",
            "entry-dictionary",
            "entry-encyclopedia",
            "event",
            "figure",
            "graphic",
            "hearing",
            "interview",
            "legal_case",
            "legislation",
            "manuscript",
            "map",
            "motion_picture",
            "musical_score",
            "pamphlet",
            "paper-conference",
            "patent",
            "performance",
            "periodical",
            "personal_communication",
            "post",
            "post-weblog",
            "regulation",
            "report",
            "review",
            "review-book",
            "software",
            "song",
            "speech",
            "standard",
            "thesis",
            "treaty",
            "webpage",
          ]
        description: The [type](https://docs.citationstyles.org/en/stable/specification.html#appendix-iii-types) of the item.
      url:
        string:
          description: Uniform Resource Locator (e.g. "https://aem.asm.org/cgi/content/full/74/9/2766")
      URL:
        schema: string
        hidden: true
      version:
        ref: csl-number
        description: Version of the item (e.g. "2.0.9" for a software program).
      volume:
        ref: csl-number
        description:
          short: Volume number of the item (e.g. “2” when citing volume 2 of a book) or the container holding the item.
          long: |
            Volume number of the item (e.g. "2" when citing volume 2 of a book) or the container holding the 
            item (e.g. "2" when citing a chapter from volume 2 of a book).

            Use `volume-title` for the title of the volume, if any.
      volume-title:
        string:
          description:
            short: Title of the volume of the item or container holding the item.
            long: |
              Title of the volume of the item or container holding the item.

              Also use for titles of periodical special issues, special sections, and the like.
      year-suffix:
        string:
          description: Disambiguating year suffix in author-date styles (e.g. "a" in "Doe, 1999a").

- id: csl-item
  object:
    super:
      resolveRef: csl-item-shared
    closed: true
    properties:
      abstract:
        string:
          description: Abstract of the item (e.g. the abstract of a journal article)
      author:
        ref: csl-person
        description: The author(s) of the item.
      doi:
        string:
          description: Digital Object Identifier (e.g. "10.1128/AEM.02591-07")
      references:
        string:
          description:
            short: Resources related to the procedural history of a legal case or legislation.
            long: |
              Resources related to the procedural history of a legal case or legislation;

              Can also be used to refer to the procedural history of other items (e.g. 
              "Conference canceled" for a presentation accepted as a conference that was subsequently 
              canceled; details of a retraction or correction notice)
      title:
        string:
          description: The primary title of the item.

- id: citation-item
  object:
    super:
      resolveRef: csl-item
    closed: true
    properties:
      article-id:
        maybeArrayOf:
          anyOf:
            - string
            - object:
                properties:
                  type:
                    string:
                      description: The type of identifier
                  value:
                    string:
                      description: The value for the identifier
        description: The unique identifier for this article.
      elocation-id:
        string:
          description: Bibliographic identifier for a document that does not have traditional printed page numbers.
      eissn:
        string:
          description: Electronic International Standard Serial Number.
      pissn:
        string:
          description: Print International Standard Serial Number.
      art-access-id:
        string:
          description: Generic article accession identifier.
      publisher-location:
        string:
          description: The location of the publisher of this item.
      subject:
        string:
          description: The name of a subject or topic describing the article.
      categories:
        maybeArrayOf:
          string:
            description: A list of subjects or topics describing the article.
      container-id:
        maybeArrayOf:
          anyOf:
            - string
            - object:
                properties:
                  type:
                    string:
                      description: The type of identifier (e.g. `nlm-ta` or `pmc`).
                  value:
                    string:
                      description: The value for the identifier
        description:
          short: External identifier of a publication or journal.
          long: |
            External identifier, typically assigned to a journal by 
            a publisher, archive, or library to provide a unique identifier for 
            the journal or publication.
      jats-type:
        string:
          description: The type used for the JATS `article` tag.

- id: smart-include
  anyOf:
    - record:
        text:
          string:
            description: Textual content to add to includes
    - record:
        file:
          string:
            description: Name of file with content to add to includes

- id: semver
  string:
    # from https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string
    pattern: "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$"
  description: Version number according to Semantic Versioning

- id: quarto-date
  anyOf:
    - string
    - object:
        closed: true
        properties:
          format: string
          value: string
        required: [value]

- id: project-profile
  schema:
    object:
      closed: true
      properties:
        default:
          maybeArrayOf: string
          description: |
            Default profile to apply if QUARTO_PROFILE is not defined.
        group:
          maybeArrayOf:
            arrayOf: string
          description: |
            Define a profile group for which at least one profile is always active.
  description: Specify a default profile and profile groups

- id: bad-parse-schema
  schema:
    object:
      propertyNames:
        string:
          pattern: "^[^\\s]+$"

- id: quarto-dev-schema
  schema:
    object:
      properties:
        _quarto:
          hidden: true
          object:
            properties:
              trace-filters: string
              tests: object

- id: notebook-view-schema
  schema:
    object:
      properties:
        notebook:
          string:
            description: "The path to the locally referenced notebook."
        title:
          description: "The title of the notebook when viewed."
          anyOf:
            - string
            - boolean
        url:
          string:
            description: "The url to use when viewing this notebook."
        download-url:
          string:
            description: "The url to use when downloading the notebook from the preview"
      required: [notebook]

- id: code-links-schema
  schema:
    anyOf:
      - boolean
      - maybeArrayOf:
          anyOf:
            - object:
                properties:
                  icon:
                    string:
                      description: The bootstrap icon for this code link.
                  text:
                    string:
                      description: The text for this code link.
                  href:
                    string:
                      description: The href for this code link.
                  rel:
                    string:
                      description: The rel used in the `a` tag for this code link.
                  target:
                    string:
                      description: The target used in the `a` tag for this code link.
            - enum: ["repo", "binder", "devcontainer"]

- id: manuscript-schema
  schema:
    object:
      closed: true
      properties:
        "article":
          path:
            description: "The input document that will serve as the root document for this manuscript"
        "code-links":
          schema:
            ref: code-links-schema
          description: "Code links to display for this manuscript."
        "manuscript-url":
          string:
            description: "The deployed url for this manuscript"
        "meca-bundle":
          anyOf:
            - boolean
            - string
          description: "Whether to generate a MECA bundle for this manuscript"
        "notebooks":
          arrayOf:
            anyOf:
              - string
              - ref: notebook-view-schema
        "resources":
          maybeArrayOf:
            schema: path
            description: "Additional file resources to be copied to output directory"
        "environment":
          maybeArrayOf:
            schema: path
            description: "Files that specify the execution environment (e.g. renv.lock, requirements.text, etc...)"
