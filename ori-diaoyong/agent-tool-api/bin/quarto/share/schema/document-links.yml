- name: link-external-icon
  tags:
    formats: [$html-doc, revealjs]
  schema: boolean
  description: "Show a special icon next to links that leave the current site."

- name: link-external-newwindow
  tags:
    formats: [$html-doc, revealjs]
  schema: boolean
  description: "Open external links in a new browser window or tab (rather than navigating the current tab)."

- name: link-external-filter
  tags:
    formats: [$html-doc, revealjs]
  schema: string
  description:
    short: "A regular expression that can be used to determine whether a link is an internal link."
    long: |
      A regular expression that can be used to determine whether a link is an internal link. For example, 
      the following will treat links that start with http://www.quarto.org as internal links (and others
      will be considered external):

      ```
      ^(?:http:|https:)\/\/www\.quarto\.org\/custom
      ```

- name: format-links
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - boolean
      - maybeArrayOf:
          anyOf:
            - string
            - object:
                properties:
                  text:
                    string:
                      description: "The title for the link."
                  href:
                    string:
                      description: "The href for the link."
                  icon:
                    string:
                      description: "The icon for the link."
                required: [text, href]
            - object:
                properties:
                  format:
                    string:
                      description: "The format that this link represents."
                  text:
                    string:
                      description: "The title for this link."
                  icon:
                    string:
                      description: "The icon for this link."
                required: [text, format]

  description:
    short: "Controls whether links to other rendered formats are displayed in HTML output."
    long: |
      Controls whether links to other rendered formats are displayed in HTML output.

      Pass `false` to disable the display of format lengths or pass a list of format names for which you'd
      like links to be shown.

- name: notebook-links
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - boolean
      - enum: [inline, global]
  description:
    short: "Controls the display of links to notebooks that provided embedded content or are created from documents."
    long: |
      Controls the display of links to notebooks that provided embedded content or are created from documents.

      Specify `false` to disable linking to source Notebooks. Specify `inline` to show links to source notebooks beneath the content they provide. 
      Specify `global` to show a set of global links to source notebooks.

- name: other-links
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - enum: [false]
      - ref: other-links
  description: "A list of links that should be displayed below the table of contents in an `Other Links` section."

- name: code-links
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - enum: [false]
      - ref: code-links-schema
  description: "A list of links that should be displayed below the table of contents in an `Code Links` section."

- name: notebook-subarticles
  tags:
    formats: [$jats-all]
  schema: boolean
  description:
    short: "Controls whether referenced notebooks are embedded in JATS output as subarticles."
    long: |
      Controls the display of links to notebooks that provided embedded content or are created from documents.

      Defaults to `true` - specify `false` to disable embedding Notebook as subarticles with the JATS output.

- name: notebook-view
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - boolean
      - maybeArrayOf:
          anyOf:
            - string
            - ref: notebook-view-schema
  description: "Configures the HTML viewer for notebooks that provide embedded content."

- name: notebook-view-style
  tags:
    formats: [$html-doc]
  schema:
    enum: [document, notebook]
  hidden: true
  description: "The style of document to render. Setting this to `notebook` will create additional notebook style affordances."

- name: notebook-preview-options
  tags:
    formats: [$html-doc]
  schema:
    object:
      properties:
        back:
          boolean:
            description: "Whether to show a back button in the notebook preview."
  description: "Options for controlling the display and behavior of Notebook previews."

- name: canonical-url
  tags:
    formats: [$html-doc]
  schema:
    anyOf:
      - boolean
      - string
  description:
    short: "Include a canonical link tag in website pages"
    long: |
      Include a canonical link tag in website pages. You may pass either `true` to 
      automatically generate a canonical link, or pass a canonical url that you'd like
      to have placed in the `href` attribute of the tag.

      Canonical links can only be generated for websites with a known `site-url`.
