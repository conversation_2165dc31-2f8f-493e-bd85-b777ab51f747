- name: from
  alias: reader
  schema: string
  default: markdown
  description:
    short: "Format to read from"
    long: |
      Format to read from. Extensions can be individually enabled or disabled by appending +EXTENSION or -EXTENSION to the format name (e.g. markdown+emoji).

- name: output-file
  schema:
    ref: pandoc-format-output-file
  default: "Input filename with output extension (e.g. .pdf, .html, etc.)"
  description: "Output file to write to"

- name: output-ext
  schema: string
  description: |
    Extension to use for generated output file

- name: template
  disabled: [$office-all, ipynb]
  schema: path
  description: |
    Use the specified file as a custom template for the generated document.

- name: template-partials
  disabled: [$office-all, ipynb]
  schema:
    maybeArrayOf: path
  description: |
    Include the specified files as partials accessible to the template for the generated content.

- name: standalone
  schema: boolean
  default: true
  description: |
    Produce output with an appropriate header and footer (e.g. a standalone HTML, LaTeX, TEI, or RTF file, not a fragment)

- name: embed-resources
  tags:
    formats: [$html-files]
  schema: boolean
  default: false
  description:
    short: "Produce a standalone HTML file with no external dependencies"
    long: |
      Produce a standalone HTML file with no external dependencies, using
      `data:` URIs to incorporate the contents of linked scripts, stylesheets,
      images, and videos. The resulting file should be "self-contained," in the
      sense that it needs no external files and no net access to be displayed
      properly by a browser. This option works only with HTML output formats,
      including `html4`, `html5`, `html+lhs`, `html5+lhs`, `s5`, `slidy`,
      `slideous`, `dzslides`, and `revealjs`. Scripts, images, and stylesheets at
      absolute URLs will be downloaded; those at relative URLs will be sought
      relative to the working directory (if the first source
      file is local) or relative to the base URL (if the first source
      file is remote).  Elements with the attribute
      `data-external="1"` will be left alone; the documents they
      link to will not be incorporated in the document.
      Limitation: resources that are loaded dynamically through
      JavaScript cannot be incorporated; as a result, some
      advanced features (e.g.  zoom or speaker notes) may not work
      in an offline "self-contained" `reveal.js` slide show.

- name: self-contained
  tags:
    formats: [$html-files]
  schema: boolean
  default: false
  hidden: true
  description:
    short: "Produce a standalone HTML file with no external dependencies"
    long: |
      Produce a standalone HTML file with no external dependencies. Note that
      this option has been deprecated in favor of `embed-resources`.

- name: self-contained-math
  tags:
    formats: [$html-files]
  schema: boolean
  default: false
  description:
    short: "Embed math libraries (e.g. MathJax) within `self-contained` output."
    long: |
      Embed math libraries (e.g. MathJax) within `self-contained` output.
      Note that math libraries are not embedded by default because they are 
       quite large and often time consuming to download.

- name: filters
  schema:
    ref: pandoc-format-filters
  description: |
    Specify executables or Lua scripts to be used as a filter transforming
    the pandoc AST after the input is parsed and before the output is written.

- name: shortcodes
  schema:
    ref: pandoc-shortcodes
  description: |
    Specify Lua scripts that implement shortcode handlers

- name: keep-md
  schema: boolean
  default: false
  tags:
    contexts: [document-execute]
  description: "Keep the markdown file generated by executing code"

- name: keep-ipynb
  schema: boolean
  default: false
  tags:
    contexts: [document-execute]
  description: "Keep the notebook file generated from executing code."

- name: ipynb-filters
  schema:
    arrayOf: "string"
  tags:
    contexts: [document-execute]
  description: "Filters to pre-process ipynb files before rendering to markdown"

- name: ipynb-shell-interactivity
  schema:
    enum: [null, all, last, last_expr, none, last_expr_or_assign]
  tags:
    contexts: [document-execute]
    engine: jupyter
  description: |
    Specify which nodes should be run interactively (displaying output from expressions)

- name: plotly-connected
  schema: boolean
  default: false
  tags:
    contexts: [document-execute]
    engine: jupyter
  description: |
    If true, use the "notebook_connected" plotly renderer, which downloads
    its dependencies from a CDN and requires an internet connection to view.

- name: keep-typ
  tags:
    formats: [typst]
  schema: boolean
  default: false
  description: "Keep the intermediate typst file used during render."

- name: keep-tex
  tags:
    formats: [pdf, beamer]
  schema: boolean
  default: false
  description: "Keep the intermediate tex file used during render."

- name: extract-media
  schema: path
  description:
    short: |
      Extract images and other media contained in or linked from the source document to the
      path DIR.
    long: |
      Extract images and other media contained in or linked from the source document to the
      path DIR, creating it if necessary, and adjust the images references in the document
      so they point to the extracted files. Media are downloaded, read from the file
      system, or extracted from a binary container (e.g. docx), as needed. The original
      file paths are used if they are relative paths not containing ... Otherwise filenames
      are constructed from the SHA1 hash of the contents.

- name: resource-path
  schema:
    arrayOf: path
  default: ["."]
  description: |
    List of paths to search for images and other resources.

- name: default-image-extension
  schema: string
  description:
    short: |
      Specify a default extension to use when image paths/URLs have no extension.
    long: |
      Specify a default extension to use when image paths/URLs have no
      extension.  This allows you to use the same source for formats that
      require different kinds of images.  Currently this option only affects
      the Markdown and LaTeX readers.

- name: abbreviations
  schema: string
  description:
    short: |
      Specifies a custom abbreviations file, with abbreviations one to a line.
    long: |
      Specifies a custom abbreviations file, with abbreviations one to a line.
      This list is used when reading Markdown input: strings found in this list
      will be followed by a nonbreaking space, and the period will not produce sentence-ending space in formats like LaTeX.  The strings may not contain
      spaces.

- name: dpi
  schema: number
  default: 96
  description:
    short: |
      Specify the default dpi (dots per inch) value for conversion from pixels to inch/
      centimeters and vice versa.
    long: |
      Specify the default dpi (dots per inch) value for conversion from pixels to inch/
      centimeters and vice versa. (Technically, the correct term would be ppi: pixels per
      inch.) The default is `96`. When images contain information about dpi internally, the
      encoded value is used instead of the default specified by this option.

- name: html-table-processing
  schema:
    enum: [none]
  description: If `none`, do not process tables in HTML input.
