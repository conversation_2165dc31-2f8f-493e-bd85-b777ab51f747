- name: bibliography
  schema:
    maybeArrayOf: path
  description: |
    Document bibliography (BibTeX or CSL). May be a single file or a list of files

- name: csl
  schema: path
  description: Citation Style Language file to use for formatting references.

- name: citations-hover
  schema: boolean
  tags:
    formats: [$html-files]
  default: true
  description: Enables a hover popup for citation that shows the reference information.

- name: citation-location
  schema:
    enum: [document, margin]
  tags:
    formats: [$html-doc]
  default: document
  description: Where citation information should be displayed (`document` or `margin`)

- name: cite-method
  tags:
    formats: [$pdf-all]
  schema:
    enum: [citeproc, natbib, biblatex]
  default: citeproc
  description: |
    Method used to format citations (`citeproc`, `natbib`, or `biblatex`).

- name: citeproc
  schema: boolean
  default: true
  description:
    short: "Turn on built-in citation processing"
    long: |
      Turn on built-in citation processing. To use this feature, you will need
      to have a document containing citations and a source of bibliographic data: 
      either an external bibliography file or a list of `references` in the 
      document's YAML metadata. You can optionally also include a `csl` 
      citation style file.

- name: biblatexoptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description: A list of options for BibLaTeX.

- name: natbiboptions
  schema:
    maybeArrayOf: string
  tags:
    formats: [$pdf-all]
  description: One or more options to provide for `natbib` when generating a bibliography.

- name: biblio-style
  schema: string
  tags:
    formats: [$pdf-all]
  description: The bibliography style to use (e.g. `\bibliographystyle{dinat}`) when using `natbib` or `biblatex`.

- name: bibliographystyle
  schema: string
  tags:
    formats: [typst]
  description: 'The bibliography style to use (e.g. `#set bibliography(style: "apa")`) when using typst built-in citation system (e.g when not `citeproc: true`).'

- name: biblio-title
  schema: string
  tags:
    formats: [$pdf-all]
  description: The bibliography title to use when using `natbib` or `biblatex`.

- name: biblio-config
  schema: boolean
  tags:
    formats: [$pdf-all]
  description: Controls whether to output bibliography configuration for `natbib` or `biblatex` when cite method is not `citeproc`.

- name: citation-abbreviations
  schema: path
  description:
    short: "JSON file containing abbreviations of journals that should be used in formatted bibliographies."
    long: |
      JSON file containing abbreviations of journals that should be
      used in formatted bibliographies when `form="short"` is
      specified. The format of the file can be illustrated with an
      example:

      ```json
      { "default": {
          "container-title": {
            "Lloyd's Law Reports": "Lloyd's Rep",
            "Estates Gazette": "EG",
            "Scots Law Times": "SLT"
          }
        }
      }
      ```
- name: link-citations
  schema: boolean
  tags:
    formats: [$pdf-all, docx]
  description: If true, citations will be hyperlinked to the corresponding bibliography entries (for author-date and numerical styles only). Defaults to false.

- name: link-bibliography
  schema: boolean
  tags:
    formats: [$pdf-all, docx]
  description:
    short: If true, DOIs, PMCIDs, PMID, and URLs in bibliographies will be rendered as hyperlinks.
    long: |
      If true, DOIs, PMCIDs, PMID, and URLs in bibliographies will be rendered as hyperlinks. (If an entry contains a DOI, PMCID, PMID, or URL, but none of 
      these fields are rendered by the style, then the title, or in the absence of a title the whole entry, will be hyperlinked.) Defaults to true.

- name: notes-after-punctuation
  schema: boolean
  tags:
    formats: [$pdf-all, docx]
  description:
    short: Places footnote references or superscripted numerical citations after following punctuation.
    long: |
      If true (the default for note styles), Quarto (via Pandoc) will put footnote references or superscripted numerical citations after 
      following punctuation. For example, if the source contains `blah blah [@jones99]`., the result will look like `blah blah.[^1]`, with 
      the note moved after the period and the space collapsed. 

      If false, the space will still be collapsed, but the footnote will not be moved after the punctuation. The option may also be used 
      in numerical styles that use superscripts for citation numbers (but for these styles the default is not to move the citation).
