- name: tbl-cap
  schema:
    maybeArrayOf: string
  description: Table caption

- name: tbl-subcap
  schema:
    anyOf:
      - enum: [true]
      - maybeArrayOf: string
  description: Table subcaptions

- name: tbl-colwidths
  tags:
    contexts: [document-tables]
    engine: [knitr, jupyter]
    formats: [$pdf-all, $html-all]
  schema:
    anyOf:
      - boolean
      - enum: [auto]
      - arrayOf: number
  description:
    short: "Apply explicit table column widths"
    long: |
      Apply explicit table column widths for markdown grid tables and pipe
      tables that are more than `columns` characters wide (72 by default). 

      Some formats (e.g. HTML) do an excellent job automatically sizing
      table columns and so don't benefit much from column width specifications.
      Other formats (e.g. LaTeX) require table column sizes in order to 
      correctly flow longer cell content (this is a major reason why tables 
      > 72 columns wide are assigned explicit widths by Pandoc).

      This can be specified as:

      - `auto`: Apply markdown table column widths except when there is a
        hyperlink in the table (which tends to throw off automatic
        calculation of column widths based on the markdown text width of cells).
        (`auto` is the default for HTML output formats)

      - `true`: Always apply markdown table widths (`true` is the default
        for all non-HTML formats)

      - `false`: Never apply markdown table widths.

      - An array of numbers (e.g. `[40, 30, 30]`): Array of explicit width percentages.

- name: html-table-processing
  schema:
    enum: [none]
  description: If `none`, do not process raw HTML table in cell output and leave it as-is
