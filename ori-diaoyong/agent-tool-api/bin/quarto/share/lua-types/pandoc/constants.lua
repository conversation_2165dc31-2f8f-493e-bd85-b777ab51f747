---@meta

--[[
Author name is mentioned in the text.  
]]
pandoc.AuthorInText = 'AuthorInText'

--[[
Author name is suppressed.
]]
pandoc.SuppressAuthor = 'SuppressAuthor'

--[[
Default citation style is used.
]]
pandoc.NormalCitation = 'NormalCitation'

--[[
Table cells aligned left.
]]
pandoc.AlignLeft = 'AlignLeft'

--[[
Table cells right-aligned.
]]
pandoc.AlignRight = 'AlignRight'

--[[
Table cell content is centered.
]]
pandoc.AlignCenter = 'AlignCenter'

--[[
Table cells are alignment is unaltered.
]]
pandoc.AlignDefault = 'AlignDefault'

--[[
Default list number delimiters are used.
]]
pandoc.DefaultDelim = 'DefaultDelim'

--[[
List numbers are delimited by a period.
]]
pandoc.Period = 'Period'

--[[
List numbers are delimited by a single parenthesis.
]]
pandoc.OneParen = 'OneParen'

--[[
List numbers are delimited by a double parentheses.
]]
pandoc.TwoParens = 'TwoParens'

--[[
List are numbered in the default style
]]
pandoc.DefaultStyle = 'DefaultStyle'

--[[
List items are numbered as examples.
]]
pandoc.Example = 'Example'

--[[
List are numbered using decimal integers.
]]
pandoc.Decimal = 'Decimal'

--[[
List are numbered using lower-case roman numerals.
]]
pandoc.LowerRoman = 'LowerRoman'

--[[
List are numbered using upper-case roman numerals
]]
pandoc.UpperRoman = 'UpperRoman'

--[[
List are numbered using lower-case alphabetic characters.
]]
pandoc.LowerAlpha = 'LowerAlpha'

--[[
List are numbered using upper-case alphabetic characters.
]]
pandoc.UpperAlpha = 'UpperAlpha'
