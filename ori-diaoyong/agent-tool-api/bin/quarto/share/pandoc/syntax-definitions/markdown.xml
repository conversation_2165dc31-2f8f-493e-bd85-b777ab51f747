<?xml version="1.0" encoding="UTF-8"?>
<!--
    Kate syntax highlight filter for Markdown/MultiMarkdown documents.

    Copyright 2008 Darrin <PERSON>. http://www.dyeager.org/
    Dual-Licensed under both GPL and BSD licenses.

    Extended 2009 <PERSON><PERSON><PERSON>. http://github.com/claes/kate-markdown/
    Extended 2019 <PERSON><PERSON><PERSON>. <<EMAIL>> (changes under MIT license).
-->
<!--
    The [^\s]{1} is in the regex to avoid interfering with bullet list which
    starts "* item". Thus emphasis/strong MUST be formed like *words here*
    with the asterisks next to the beginning of the first word.

    Match space or newline, followed by "*", followed by one non-space,
    followed by anything non-asterisk, followed by "*", followed by
    space, end-of-sentence punctuation, or the end of the line.

    TODO: We can be more specific and highlight text combinations in bold,
          italic and strikethrough.
          Example: **bold text and _italic and bold text_**
                   __bold and ~~strikeout and bold~~__
-->
<!DOCTYPE language
[
<!-- NOTE: To correctly detect bold, italic or strike out text, use minimal="true" in RegExpr rules -->
<!ENTITY contentregex_ast "(?:(?:[^\*\s\\]|\\.)(?:[^\\]|\\.)*)?(?:[^\*\s\\]|\\\S)">
<!ENTITY contentregex_und "(?:(?:[^_\s\\]|\\.)(?:[^\\]|\\.)*)?(?:[^_\s\\]|\\\S)">
<!-- strong + emphasis text -->
<!ENTITY strongemphasisregex_ast "\*{3,5}&contentregex_ast;\*{3}">
<!ENTITY strongemphasisregex_und "\b_{3,5}&contentregex_und;_{3}">
<!ENTITY strongemphasisregex_ast_und "\*{2,5}(?:(?:_|_{3}|_{5})&contentregex_und;_+|(?:_{2}|_{4})&contentregex_und;_)\*{2}">
<!ENTITY strongemphasisregex_ast_und2 "\*_{2,5}&contentregex_und;_{2,5}\*(?!\*)">
<!ENTITY strongemphasisregex_und_ast "\b_{2,5}(?:(?:\*|\*{3}|\*{5})&contentregex_ast;\*+|(?:\*{2}|\*{4})&contentregex_ast;\*)_{2}">
<!ENTITY strongemphasisregex_und_ast2 "\b_(?:\*{2,5}&contentregex_ast;\*{2,5}_{0,4}|_{0,4}\*{2,5}&contentregex_ast;\*{2,5})_\b">
<!-- strong text -->
<!ENTITY strongregex_ast "\*{2}(?:&contentregex_ast;\*{2}|\*{1,3}&contentregex_ast;\*{2}(?!\*))">
<!ENTITY strongregex_und "\b_{2}(?:&contentregex_und;_{2}|_{1,3}&contentregex_und;_{2}(?!_))">
<!-- emphasis text -->
<!ENTITY emphasisregex_ast "\*(?:&contentregex_ast;\*|\*{1,4}&contentregex_ast;\*(?!\*))">
<!ENTITY emphasisregex_und "\b_(?:&contentregex_und;_+|_{1,4}&contentregex_und;_)\b">
<!-- links.
     Keep in sync with reStructuredText (rest) -->
<!ENTITY startlink "(?:https?|ftp)\://">
<!ENTITY link "&startlink;[^&quot;&gt;\s]+">
<!-- link in normal text.
     Keep in sync with reStructuredText’s (rest) StandaloneHyperlink attribute -->
<!ENTITY implicitlink "\b&startlink;[^&quot;&gt;\s`\)]*[^\s!&quot;&apos;`\(\)\*,\.:;&lt;&gt;\?~\]\}\\](?=[[:punct:]]*(?:[\s\)]|$))">
<!-- references: [name], [name][id], [name][id] "title", [name](https://example.com) -->
<!ENTITY refchar "(?:\\.|[^\]\\])">
<!ENTITY refname "(?:\\.|[^\s\[\]\\])&refchar;*">
<!ENTITY reflinkid "\s*(?:&refname;)?">
<!ENTITY reflinkidregex "(?:\s?\[&reflinkid;\]|\((?:\\.|[^\(\)\\])*\))">
<!ENTITY reflinkregex "\[(?:(?=\s*&refname;\])|\s+(?=\]))">
<!-- target ref.: [id]: https://example.com "Title" -->
<!ENTITY reflinktargetregex '\[\s*(?:&refname;|\s)\]\:\s+(?=\S)'>
<!-- footnote: [^name] -->
<!ENTITY footnoteregex "\[\^&refchar;+\]">
<!-- image: ![title](https://example.com), ![title][id] -->
<!ENTITY startinlineimage "\!\[(?:\\.|[^\[\]\\])*\]\(">
<!ENTITY endinlineimage "(?:\\.|[^\(\)\\])*\)">
<!ENTITY inlineimageregex "&startinlineimage;(?=&endinlineimage;)">
<!ENTITY refimageregex "\!\[(?:\\.|[^\[\]\\])*\]\[&reflinkid;\]">
<!-- autolink & email: <https://example.com>, <<EMAIL>>, <mailto:<EMAIL>> -->
<!ENTITY autolinkregex '&lt;&link;&gt;'>
<!ENTITY email "[\-\.\w]+\@[\-a-z0-9]+(?:\.[\-a-z0-9]+)*\.[a-z]+">
<!ENTITY mailtolinkregex "&lt;(?:mailto:)?(?=&email;&gt;)">
<!-- horizontal rule: avoid conflicts with strong-emphasis text, lists and code blocks -->
<!ENTITY rulerregex "\s*(?:(?:\*\s*){3,}|(?:_\s*){3,}|(?:\-\s*){3,})\s*$">
<!-- two spaces at end of line generates linebreak -->
<!ENTITY linebreakregex "  $">
<!-- strikethrough text, pandoc style -->
<!ENTITY strikeoutregex "[~]{2}[^~](?:.*[^~])?[~]{2}">
<!-- start of fenced code block -->
<!ENTITY fcode "(`{3,}|~{3,})">
<!-- end of line & empty line -->
<!ENTITY end "\s*$">
<!ENTITY emptyline "^\s*$">
<!-- indented code block -->
<!ENTITY indentedcodeblock "(?:\s{4}|\t).*$">
<!-- bullet of list -->
<!ENTITY listbullet "[\*\+\-]">
<!-- metadata key & name of html tag -->
<!ENTITY metadatakey "[a-zA-Z0-9][\w\-\s]*\:">
<!ENTITY htmlname "[A-Za-z_\:][\w\.\:\-]*">
<!-- comment -->
<!ENTITY startcomment "&lt;!--">
<!ENTITY endcomment "--&gt;">
<!ENTITY checkbox "\[[ x]\](?=\s)">

<!-- html stuff -->
	<!ENTITY name          "[A-Za-z_:][\w.:_-]*">
	<!ENTITY attributeName "[A-Za-z_:*#\(\[][\)\]\w.:_-]*">
	<!ENTITY entref        "&amp;(?:#[0-9]+|#[xX][0-9A-Fa-f]+|&name;);">

]>

<language name="Markdown" version="27" kateversion="5.79" section="Markup" extensions="*.md;*.mmd;*.markdown" mimetype="text/markdown" priority="15" author="Darrin Yeager, Claes Holmerson" license="GPL,BSD">
  <highlighting>
    <contexts>
      <!-- Start of the Markdown document: find metadata or code block -->
      <context name="Start Document" attribute="Normal Text" lineEndContext="Normal Text" lineEmptyContext="Normal Text" fallthroughContext="Normal Text">
        <!-- Some titles: (Title|Author|Date|Copyright|Revision|CSS|LaTeX\ XSLT|Categories|Tags|BaseName|Excerpt) -->
        <!-- Metadata: Markdown format -->
        <RegExpr attribute="Metadata" context="metadata" String="^\-{3}&end;" column="0" beginRegion="metadata"/>
        <!-- Metadata: MultiMarkdown format -->
        <RegExpr attribute="Metadata Title" context="metadata-mm-value" String="^&metadatakey;(?=\s|$)" column="0"/>
        <!-- Code Block -->
        <RegExpr attribute="Code" context="find-code-block-start" String="^&indentedcodeblock;" column="0"/>
      </context>
      <context name="metadata" attribute="Metadata" lineEndContext="#stay">
        <StringDetect attribute="Metadata" context="#pop!Normal Text" String="---" column="0" endRegion="metadata"/>
        <StringDetect attribute="Metadata" context="#pop!Normal Text" String="..." column="0" endRegion="metadata"/>
        <RegExpr attribute="Metadata Title" String="^&metadatakey;(?=\s|$)" column="0"/>
      </context>
      <context name="metadata-mm-value" attribute="Metadata" lineEndContext="#pop!metadata-mm"/>
      <context name="metadata-mm" attribute="Metadata" lineEndContext="#stay" lineEmptyContext="#pop!Normal Text" fallthroughContext="#pop!Normal Text">
        <!-- Find Metadata key or value -->
        <RegExpr attribute="Metadata Title" context="#pop!metadata-mm-value" String="^&metadatakey;(?=\s|$)" column="0"/>
        <RegExpr attribute="Metadata" context="#pop!metadata-mm-value" String="^(?:\s{4}|\t)" column="0"/>
      </context>
      <context name="find-code-block-start" attribute="Normal Text" lineEndContext="#stay" lineEmptyContext="#stay" fallthroughContext="#pop!Normal Text">
        <IncludeRules context="find-code-block"/>
      </context>

      <!-- Normal Text Document -->
      <context name="Normal Text" attribute="Normal Text" lineEndContext="#stay" lineEmptyContext="find-code-block">
        <!-- Header -->
        <DetectChar context="find-header" char="#" column="0" lookAhead="true"/>
        <RegExpr attribute="Header H1" String="(?:\=\s*){2,}$" firstNonSpace="true"/>
        <!-- Highlight code block after line only with spaces or comment -->
        <RegExpr attribute="Normal Text" context="find-code-block" String="&emptyline;" column="0"/>
        <StringDetect attribute="Comment" context="find-code-block" String="&startcomment;" column="0" lookAhead="true"/>
        <!-- Horizontal rules, Blockquotes and Lists.
             NOTE: The indentation of the lists is captured. -->
        <RegExpr attribute="Horizontal Rule" context="find-code-block" String="^&rulerregex;" column="0"/>
        <DetectChar attribute="Blockquote" context="blockquote" char="&gt;" firstNonSpace="true"/>
        <RegExpr attribute="List" context="list" String="^(\s*)&listbullet;(\s+)" column="0"/>
        <RegExpr attribute="Number List" context="numlist" String="^(\s*)\d\.(\s+)" column="0"/>
        <RegExpr attribute="Number List" context="numlist2" String="^(\s*)\d\d+\.(\s+)" column="0"/>
        <!-- Find Strong, Emphasis and Strikethrough Text -->
        <DetectChar context="find-strong-normal" char="*" lookAhead="true"/>
        <DetectChar context="find-emphasis-normal" char="_" lookAhead="true"/>
        <RegExpr attribute="Strikethrough Text" minimal="true" String="&strikeoutregex;"/>
        <!-- Common -->
        <IncludeRules context="inc"/>
        <RegExpr attribute="Normal Text: Link" String="&implicitlink;"/>
      </context>
      <!-- Find indented code blocks. These are only allowed after an empty line or on the first line -->
      <context name="find-code-block" attribute="Normal Text" lineEndContext="#stay" lineEmptyContext="#stay" fallthroughContext="#pop">
        <RegExpr attribute="Code" String="^&indentedcodeblock;" column="0"/>
        <RegExpr attribute="Normal Text" String="&end;" />
        <RegExpr attribute="Comment" context="comment" String="^\s*&startcomment;|\s*&startcomment;(?=.*&endcomment;)" beginRegion="comment"/>
      </context>

      <context name="find-header" attribute="Normal Text" lineEndContext="#pop">
        <RegExpr attribute="Header H1" context="#pop!close-H2-region" String="^#\s" column="0" endRegion="H1" beginRegion="H1" lookAhead="true"/>
        <RegExpr attribute="Header H2" context="#pop!close-H3-region" String="^##\s" column="0" endRegion="H2" beginRegion="H2" lookAhead="true"/>
        <RegExpr attribute="Header H3" context="#pop!close-H4-region" String="^###\s" column="0" endRegion="H3" beginRegion="H3" lookAhead="true"/>
        <RegExpr attribute="Header H4" context="#pop!close-H5-region" String="^####\s" column="0" endRegion="H4" beginRegion="H4" lookAhead="true"/>
        <RegExpr attribute="Header H5" context="#pop!close-H6-region" String="^#####\s" column="0" endRegion="H5" beginRegion="H5" lookAhead="true"/>
        <RegExpr attribute="Header H6" context="#pop" String="^######\s.*" column="0" endRegion="H6" beginRegion="H6"/>
        <DetectChar attribute="Normal Text" context="#pop" char="#"/>
      </context>
      <!-- BUG: 441278 sub-headers should be closed when their parent header is closed  (e.g. in h1 h2 h3 h1, h1-h3 should all be closed at the 2nd h1) -->
      <!-- TODO: Port to a less hacky version (maybe a new attribute for declaring multiple endRegions)  -->
      <context name="close-H2-region" attribute="Header H2" lineEndContext="#pop!close-H3-region" fallthroughContext="#pop!close-H3-region">
        <DetectChar attribute="Header H2" context="#pop!close-H3-region" char="#" lookAhead="true" endRegion="H2"/>
      </context>
      <context name="close-H3-region" attribute="Header H3" lineEndContext="#pop!close-H4-region" fallthroughContext="#pop!close-H4-region">
        <DetectChar attribute="Header H3" context="#pop!close-H4-region" char="#" lookAhead="true" endRegion="H3"/>
      </context>
      <context name="close-H4-region" attribute="Header H4" lineEndContext="#pop!close-H5-region" fallthroughContext="#pop!close-H5-region">
        <DetectChar attribute="Header H4" context="#pop!close-H5-region" char="#" lookAhead="true" endRegion="H4"/>
      </context>
      <context name="close-H5-region" attribute="Header H5" lineEndContext="#pop!close-H6-region" fallthroughContext="#pop!close-H6-region">
        <DetectChar attribute="Header H5" context="#pop!close-H6-region" char="#" lookAhead="true" endRegion="H5"/>
      </context>
      <context name="close-H6-region" attribute="Header H6" lineEndContext="#pop" fallthroughContext="#pop">
        <DetectChar attribute="Header H6" context="#pop!close-sentinel-region" char="#" lookAhead="true" endRegion="H6"/>
      </context>
      <!-- This sentinel does not close any actual region, it's just here so that the proper attribute/highlighting is applied and -->
      <!--to ensure that H6 headers won't be set as the "primary" region that was closed just because it was closed last -->
      <context name="close-sentinel-region" attribute="Normal Text" lineEndContext="#pop" fallthroughContext="#pop">
        <RegExpr attribute="Header H1" context="#pop" String="^#\s.*" column="0"/>
        <RegExpr attribute="Header H2" context="#pop" String="^##\s.*" column="0"/>
        <RegExpr attribute="Header H3" context="#pop" String="^###\s.*" column="0"/>
        <RegExpr attribute="Header H4" context="#pop" String="^####\s.*" column="0"/>
        <RegExpr attribute="Header H5" context="#pop" String="^#####\s.*" column="0"/>
      </context>


      <context name="find-strong-normal" attribute="Normal Text" lineEndContext="#pop">
        <RegExpr attribute="Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast_und;|&strongemphasisregex_ast_und2;"/>
        <RegExpr attribute="Strong Text" context="#pop" minimal="true" String="&strongregex_ast;"/>
        <RegExpr attribute="Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast;"/>
        <RegExpr attribute="Emphasis Text" context="#pop" minimal="true" String="&emphasisregex_ast;"/>
        <DetectChar attribute="Normal Text" context="#pop" char="*"/>
      </context>
      <context name="find-emphasis-normal" attribute="Normal Text" lineEndContext="#pop">
        <RegExpr attribute="Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_und_ast;|&strongemphasisregex_und_ast2;"/>
        <RegExpr attribute="Strong Text" context="#pop" minimal="true" String="&strongregex_und;"/>
        <RegExpr attribute="Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_und;"/>
        <RegExpr attribute="Emphasis Text" context="#pop" minimal="true" String="&emphasisregex_und;"/>
        <DetectChar attribute="Normal Text" context="#pop" char="_"/>
      </context>

      <!-- Blockquote -->
      <context name="blockquote" attribute="Blockquote: Normal Text" lineEndContext="#stay" lineEmptyContext="#pop">
        <IncludeRules context="default-blockquote-1"/>
        <!-- Find code block -->
        <RegExpr attribute="Normal Text" context="#pop!find-code-block" String="&emptyline;" column="0"/>
        <StringDetect attribute="Comment" context="#pop!find-code-block" String="&startcomment;" column="0" lookAhead="true"/>
        <IncludeRules context="default-blockquote-2"/>
      </context>
      <!-- Blockquote within a list -->
      <context name="blockquote-list" attribute="Blockquote: Normal Text" lineEndContext="#stay" lineEmptyContext="#pop">
        <IncludeRules context="default-blockquote-1"/>
        <!-- Don't find code block -->
        <RegExpr attribute="Normal Text" context="#pop" String="&emptyline;" column="0"/>
        <StringDetect attribute="Comment" context="#pop!comment" String="&startcomment;" firstNonSpace="true"/>
        <IncludeRules context="default-blockquote-2"/>
      </context>

      <context name="default-blockquote-1" attribute="Blockquote: Normal Text" lineEndContext="#stay">
        <DetectChar attribute="Blockquote" char="&gt;" firstNonSpace="true"/>
        <!-- End with header, horizontal rule or list/numlist -->
        <RegExpr context="#pop" String="^(?:\s*(?:&listbullet;|[\d]+\.)\s|#{1,6}\s|&rulerregex;)" column="0" lookAhead="true"/>
      </context>
      <context name="default-blockquote-2" attribute="Blockquote: Normal Text" lineEndContext="#stay">
        <!-- Strong, emphasis, strong-emphasis and strikethrough text -->
        <AnyChar context="find-strong-emphasis-blockquote" String="*_" lookAhead="true"/>
        <RegExpr attribute="Blockquote: Strikethrough Text" minimal="true" String="&strikeoutregex;"/>
        <!-- Common -->
        <IncludeRules context="inc"/>
        <RegExpr attribute="Blockquote: Link" String="&implicitlink;"/>
      </context>
      <context name="find-strong-emphasis-blockquote" attribute="Blockquote: Normal Text" lineEndContext="#pop">
        <RegExpr attribute="Blockquote: Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast_und;|&strongemphasisregex_und_ast;|&strongemphasisregex_ast_und2;|&strongemphasisregex_und_ast2;"/>
        <RegExpr attribute="Blockquote: Strong Text" context="#pop" minimal="true" String="&strongregex_ast;|&strongregex_und;"/>
        <RegExpr attribute="Blockquote: Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast;|&strongemphasisregex_und;"/>
        <RegExpr attribute="Blockquote: Emphasis Text" context="#pop" minimal="true" String="&emphasisregex_ast;|&emphasisregex_und;"/>
        <AnyChar attribute="Blockquote: Normal Text" context="#pop" String="*_"/>
      </context>

      <!-- List and Numbered List -->
      <!-- NOTE: The indentation of the lists is captured. New paragraphs, code blocks, blockquotes or
           horizontal rules must have an indentation equal to or greater than the list, to be part of it. -->
      <!-- * list -->
      <context name="list" attribute="List: Normal Text" lineEndContext="#stay" fallthroughContext="content-list">
        <!-- Find indented code blocks, blockquotes and horizontal rules -->
        <RegExpr attribute="Code" String="^%1%2\s&indentedcodeblock;" column="0" dynamic="true"/>
        <RegExpr attribute="Blockquote" context="blockquote-list" String="^%1%2\s+&gt;" column="0" dynamic="true"/>
        <RegExpr attribute="Horizontal Rule" String="^%1%2\s+&rulerregex;" column="0" dynamic="true"/>
        <RegExpr String="&emptyline;" column="0"/>
        <!-- Text with the same indentation captured corresponds to the item list -->
        <RegExpr context="content-list" String="^%1%2\s" column="0" lookAhead="true" dynamic="true"/>
        <!-- Finish when the text has a lower indentation than the list -->
        <RegExpr context="#pop" String="^\s*\S" column="0" lookAhead="true"/>
        <!-- Highlight checkbox at the start of the item (task list) -->
        <RegExpr attribute="List: Checkbox" context="content-list" String="\s*&checkbox;"/>
      </context>
      <!-- 1. numlist (one digit) -->
      <context name="numlist" attribute="List: Normal Text" lineEndContext="#stay" fallthroughContext="content-list">
        <!-- Find indented code blocks, blockquotes and horizontal rules -->
        <RegExpr attribute="Code" String="^%1%2\s{2}&indentedcodeblock;" column="0" dynamic="true"/>
        <RegExpr attribute="Blockquote" context="blockquote-list" String="^%1%2\s{2,}&gt;" column="0" dynamic="true"/>
        <RegExpr attribute="Horizontal Rule" String="^%1%2\s{2,}&rulerregex;" column="0" dynamic="true"/>
        <RegExpr String="&emptyline;" column="0"/>
        <!-- Text with the same indentation captured -->
        <RegExpr context="content-list" String="^%1%2\s{2}" column="0" lookAhead="true" dynamic="true"/>
        <!-- Finish when the text has a lower indentation than the list -->
        <RegExpr context="#pop" String="^\s*\S" column="0" lookAhead="true"/>
      </context>
      <!-- 10. numlist (two or more digits) -->
      <context name="numlist2" attribute="List: Normal Text" lineEndContext="#stay" fallthroughContext="content-list">
        <!-- Find indented code blocks, blockquotes and horizontal rules -->
        <RegExpr attribute="Code" String="^%1%2\s{3}&indentedcodeblock;" column="0" dynamic="true"/>
        <RegExpr attribute="Blockquote" context="blockquote-list" String="^%1%2\s{3,}&gt;" column="0" dynamic="true"/>
        <RegExpr attribute="Horizontal Rule" String="^%1%2\s{3,}&rulerregex;" column="0" dynamic="true"/>
        <RegExpr String="&emptyline;" column="0"/>
        <!-- Text with the same indentation captured -->
        <RegExpr context="content-list" String="^%1%2\s{3}" column="0" lookAhead="true" dynamic="true"/>
        <!-- Finish when the text has a lower indentation than the list -->
        <RegExpr context="#pop" String="^\s*\S" column="0" lookAhead="true"/>
      </context>

      <!-- NOTE: Empty lines, blockquotes and horizontal rules send to contexts "list", "numlist" or "numlist2" (#pop),
           to check the indentation of the text and determine if the content of the list ends. -->
      <context name="content-list" attribute="List: Normal Text" lineEndContext="#stay" lineEmptyContext="#pop">
        <RegExpr context="#pop" String="&emptyline;" column="0"/>
        <!-- Blockquote and horzontal rule (check indentation) -->
        <RegExpr context="#pop" String="^\s*(?:&gt;|&rulerregex;)" column="0" lookAhead="true"/>
        <!-- End with header or new list/numlist -->
        <RegExpr context="#pop#pop" String="^(?:\s*(?:&listbullet;|[\d]+\.)\s|#{1,6}\s)" column="0" lookAhead="true"/>
        <!-- Strong, emphasis, strong-emphasis and strikethrough text -->
        <AnyChar context="find-strong-emphasis-list" String="*_" lookAhead="true"/>
        <RegExpr attribute="List: Strikethrough Text" minimal="true" String="&strikeoutregex;"/>
        <!-- Common -->
        <IncludeRules context="inc"/>
        <RegExpr attribute="List: Link" String="&implicitlink;"/>
      </context>
      <context name="find-strong-emphasis-list" attribute="List: Normal Text" lineEndContext="#pop">
        <RegExpr attribute="List: Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast_und;|&strongemphasisregex_und_ast;|&strongemphasisregex_ast_und2;|&strongemphasisregex_und_ast2;"/>
        <RegExpr attribute="List: Strong Text" context="#pop" minimal="true" String="&strongregex_ast;|&strongregex_und;"/>
        <RegExpr attribute="List: Strong-Emphasis Text" context="#pop" minimal="true" String="&strongemphasisregex_ast;|&strongemphasisregex_und;"/>
        <RegExpr attribute="List: Emphasis Text" context="#pop" minimal="true" String="&emphasisregex_ast;|&emphasisregex_und;"/>
        <AnyChar attribute="List: Normal Text" context="#pop" String="*_"/>
      </context>

      <!-- Comments -->
      <context name="comment" attribute="Comment" lineEndContext="#stay">
        <StringDetect attribute="Comment" context="#pop" String="&endcomment;" endRegion="comment"/>
        <IncludeRules context="##Comments"/>
      </context>

      <!-- Fenced Code Blocks -->
      <context name="find-lang-fenced-code" attribute="Normal Text" lineEndContext="#pop">
        <!-- Apply syntax highlighting to fenced code blocks for some languages -->
        <RegExpr attribute="Fenced Code" context="#pop!code" String="&fcode;&end;" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!bash-code" String="&fcode;\s*(?:bash(?:rc|_profile|_login|_logout)?|shell|sh|profile|PKGBUILD|APKBUILD|ebuild|eclass|nix)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!zsh-code" String="&fcode;\s*(?:zsh)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!cpp-code" String="&fcode;\s*(?:[ch]pp|[ch]\+\+|[ch]xx|h?cc|hh|cuh?|ino|pde|moc)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!csharp-code" String="&fcode;\s*(?:cs|csharp|c\#)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!cmake-code" String="&fcode;\s*(?:cmake|CMakeLists(?:\.txt)?)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!css-code" String="&fcode;\s*css&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!c-code" String="&fcode;\s*[ch]&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!doxygen-code" String="&fcode;\s*doxygen&end;" insensitive="true" beginRegion="code-block"/> <!-- Block comment of Doxygen -->
        <RegExpr attribute="Fenced Code" context="#pop!email-code" String="&fcode;\s*(?:email|emlx?|mbo?x)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!go-code" String="&fcode;\s*go(?:lang)?&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!hamlet-code" String="&fcode;\s*[wxs]?hamlet&end;" insensitive="true" beginRegion="code-block"/> <!-- Included in the Haskell definition -->
        <RegExpr attribute="Fenced Code" context="#pop!haskell-code" String="&fcode;\s*(?:haskell|c?hs|hs\-boot)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!html-code" String="&fcode;\s*(?:[sx]?html?|inc|tmpl|tpl)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!java-code" String="&fcode;\s*(?:java|bsh)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!javascript-code" String="&fcode;\s*(?:javascript|m?js|es6|kwinscript|julius)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!jsx-code" String="&fcode;\s*(?:jsx|tsx|(?:java|type)script\-react)&end;" insensitive="true" beginRegion="code-block"/> <!-- Included in the HTML definition. Also apply for TSX. -->
        <RegExpr attribute="Fenced Code" context="#pop!json-code" String="&fcode;\s*(?:json5?|gltf)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!yaml-code" String="&fcode;\s*(?:ya?ml)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!matlab-code" String="&fcode;\s*matlab&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!markdown-code" String="&fcode;\s*(?:markdown|m?md)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!mustache-code" String="&fcode;\s*(?:handlebars|hbs|mustache|mst|ractive|hogan|hulk)&end;" insensitive="true" beginRegion="code-block"/> <!-- Included in the HTML definition -->
        <RegExpr attribute="Fenced Code" context="#pop!perl-code" String="&fcode;\s*(?:perl|p[lm]|pod|psgi|vcl)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!php-code" String="&fcode;\s*(?:php[3457t]?|wml|phtml?|aw|ctp)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!python-code" String="&fcode;\s*(?:python[23]?|py[23w]?|[rc]py|sconstruct|gypi?)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!qml-code" String="&fcode;\s*qml(?:types)?&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!r-code" String="&fcode;\s*(?:r|rprofile|rscript)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!raku-code" String="&fcode;\s*(?:raku(?:mod|doc|test)?|perl6|p[lm]?6|pod6|nqp)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!rest-code" String="&fcode;\s*(?:rst|rest|restructuredtext)&end;" insensitive="true" beginRegion="code-block"/> <!-- Included in the CMake definition -->
        <RegExpr attribute="Fenced Code" context="#pop!ruby-code" String="&fcode;\s*(?:ruby|rbx?|rjs|rake|f?cgi|gemspec|irbrc|ru|prawn|Appraisals|(?:Rake|Cap|Chef|Gem|Guard|Hobo|Vagrant||Rant|Berks|Thor|Puppet)file|rxml|(?:xml|js)\.erb)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!rust-code" String="&fcode;\s*(?:rust|rs)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!mysql-code" String="&fcode;\s*(?:mysql|sql|ddl)&end;" insensitive="true" beginRegion="code-block"/> <!-- Included in the PHP definition -->
        <RegExpr attribute="Fenced Code" context="#pop!nim-code" String="&fcode;\s*(?:nims?)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!typescript-code" String="&fcode;\s*(?:typescript|ts)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!xml-code" String="&fcode;\s*(?:xml|xsd|xspf|tld|jsp|c?pt|dtml|rss|opml|svg|daml|rdf|ui|kcfg|qrc|wsdl|scxml|xbel|dae|sch|brd|docbook)&end;" insensitive="true" beginRegion="code-block"/>
        <RegExpr attribute="Fenced Code" context="#pop!code" String="&fcode;.*$" beginRegion="code-block"/>
      </context>
      <context name="code" attribute="Code" lineEndContext="#stay"> <!-- Unknown language -->
        <RegExpr attribute="Fenced Code" context="#pop" String="%1[~`]*(?=&end;)" firstNonSpace="true" dynamic="true" endRegion="code-block"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="bash-code" fallthroughContext="Command##Bash">
        <IncludeRules context="code"/>
        <IncludeRules context="##Bash" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="zsh-code" fallthroughContext="Command##Zsh">
        <IncludeRules context="code"/>
        <IncludeRules context="##Zsh" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="cmake-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##CMake" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="c-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##C" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="cpp-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##C++" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="csharp-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##C#" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="css-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##CSS" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="doxygen-code">
        <IncludeRules context="code"/>
        <IncludeRules context="BlockComment##Doxygen" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="email-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Email" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="go-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Go" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="hamlet-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Hamlet" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="haskell-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Haskell" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="html-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##HTML" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="java-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Java" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="javascript-code">
        <IncludeRules context="code"/>
        <IncludeRules context="Normal##JavaScript" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="jsx-code">
        <IncludeRules context="code"/>
        <IncludeRules context="Normal##JavaScript React (JSX)" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="json-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##JSON" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="yaml-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##YAML" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" lineEmptyContext="find-code-block" name="markdown-code">
        <IncludeRules context="code"/>
        <IncludeRules context="Normal Text"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="matlab-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Matlab" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="mustache-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Mustache/Handlebars (HTML)" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="perl-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Perl" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="php-code">
        <IncludeRules context="code"/>
        <IncludeRules context="phpsource##PHP/PHP" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="python-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Python" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="qml-code">
        <IncludeRules context="code"/>
        <IncludeRules context="Normal##QML" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="r-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##R Script" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="raku-code">
        <IncludeRules context="code"/>
        <IncludeRules context="base##Raku" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="rest-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##reStructuredText" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="ruby-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Ruby" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="rust-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Rust" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="mysql-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##SQL (MySQL)" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="nim-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##Nim" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="typescript-code">
        <IncludeRules context="code"/>
        <IncludeRules context="Normal##TypeScript" includeAttrib="true"/>
      </context>
      <context attribute="Normal Text" lineEndContext="#stay" name="xml-code">
        <IncludeRules context="code"/>
        <IncludeRules context="##XML" includeAttrib="true"/>
      </context>

      <!-- Common -->
      <context name="inc" attribute="Normal Text" lineEndContext="#stay">
        <!-- Code -->
        <RegExpr attribute="Code" String="`[^`]+`(?!`)|`{2}[^`](?:.*?[^`])?`{2}(?!`)|`{3}[^`](?:.*?[^`])?`{3}(?!`)|`{4}[^`](?:.*?[^`])?`{4}(?!`)|`{5,}[^`](?:.*?[^`])?`{5,}"/>
        <!-- Find Fenced Code Block -->
        <RegExpr attribute="Fenced Code" context="find-lang-fenced-code" String="`{3,}(?=[^`]*$)|~{3,}(?=[^~]*$)" firstNonSpace="true" lookAhead="true"/>
        <!-- Comment -->
        <StringDetect attribute="Comment" context="comment" String="&startcomment;" beginRegion="comment"/>
        <!-- Links and References -->
        <RegExpr attribute="Reference-Link Target" context="reflinktarget" String="&reflinktargetregex;" firstNonSpace="true"/>
        <RegExpr attribute="Footnote" String="&footnoteregex;"/>
        <RegExpr attribute="Reference-Link" context="find-reflink" String="&reflinkregex;"/>
        <RegExpr attribute="Inline Image" context="inlineimage" String="&inlineimageregex;"/>
        <RegExpr attribute="Reference Image" String="&refimageregex;"/>
        <RegExpr attribute="Auto-Link" context="autolink" String="&autolinkregex;" lookAhead="true"/>
        <RegExpr attribute="Mailto-Link" context="mailtolink" String="&mailtolinkregex;"/>
        <!-- Line Break -->
        <RegExpr attribute="Line Break" minimal="true" String="&linebreakregex;"/>
        <!-- Backslash Escapes -->
        <Detect2Chars attribute="Backslash Escape" char="\" char1="\"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="`"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="*"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="_"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="{"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="}"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="["/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="]"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="("/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1=")"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="#"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="+"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="-"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="."/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="!"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="&lt;"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="&gt;"/>
        <Detect2Chars attribute="Backslash Escape" char="\" char1="&amp;"/>
        <!-- Inline HTML -->
        <RegExpr attribute="EntityRef" String="&amp;(?:#[0-9]+|#[xX][0-9A-Fa-f]+|&htmlname;);"/>
        <RegExpr context="find-html-block" String="&lt;/?&htmlname;(?:[\s&gt;]|/&gt;|$)" lookAhead="true"/>
      </context>
      <context name="find-html-block" attribute="Normal Text" lineEndContext="#pop" fallthroughContext="#pop">
        <IncludeRules context="FindElements"/>
      </context>

      <!-- Links and email: <https://example.com>, <<EMAIL>> -->
      <context name="autolink" attribute="Auto-Link" lineEndContext="#pop" fallthroughContext="#pop">
        <DetectChar attribute="Auto-Link" char="&lt;"/>
        <DetectChar attribute="Auto-Link" context="#pop" char="&gt;"/>
        <RegExpr attribute="Link" String="&startlink;[^&quot;&gt;\s]+"/>
      </context>
      <context name="mailtolink" attribute="Email" lineEndContext="#pop">
        <DetectChar attribute="Mailto-Link" context="#pop" char="&gt;"/>
      </context>

      <!-- References: [example][id], [example](http://example.com) -->
      <context name="find-reflink" attribute="Reference-Link Name" lineEndContext="#pop">
        <Detect2Chars attribute="Reference-Link Name" char="\" char1="]"/>
        <RegExpr attribute="Reference-Link" context="#pop!find-reflink-id" String="\](?=&reflinkidregex;)"/> <!-- Find id -->
        <DetectChar attribute="Reference-Link" context="#pop" char="]"/>
        <!-- Image as link or reference -->
        <RegExpr attribute="Inline Image" context="inlineimage" String="&startinlineimage;(?=&endinlineimage;&refchar;*\])"/>
        <RegExpr attribute="Reference Image" String="&refimageregex;(?=&refchar;*\])"/>
        <RegExpr attribute="Reference-Link Name" context="#stay" String="(?:\[(?:\\.|[^\[\]\\])*\](?:\\.|[^\[\]\\])*)+(?=\])" minimal="true"/>
      </context>
      <context name="find-reflink-id" attribute="Reference-Link" lineEndContext="#pop">
        <DetectSpaces />
        <!-- Ref.: [an example][id] "Optional title" -->
        <DetectChar attribute="Reference-Link" context="#pop!reflink-id" char="["/>
        <!-- Inline Ref.: [an example](http://example.com/ "Title") -->
        <DetectChar attribute="Reference-Link" context="#pop!reflink-inline-id" char="("/>
      </context>
      <context name="reflink-inline-id" attribute="Reference-Link" lineEndContext="#pop">
        <Detect2Chars attribute="Reference-Link" char="\" char1=")"/>
        <DetectChar attribute="Reference-Link" context="#pop" char=")"/>
        <RegExpr attribute="Reference-Link: Link" String="\b&startlink;(?:\\.|[^&quot;&gt;\s\)\\])+"/>
        <RegExpr attribute="Reference-Link: Email" String="\b&email;\b"/>
      </context>
      <context name="reflink-id" attribute="Reference-Link ID" lineEndContext="#pop">
        <Detect2Chars attribute="Reference-Link ID" char="\" char1="]"/>
        <DetectChar attribute="Reference-Link" context="#pop!reflink-title" char="]"/>
        <RegExpr attribute="Reference-Link: Link" String="\b&startlink;(?:\\.|[^&quot;&gt;\s\]\\])+"/>
      </context>
      <!-- [an example][id] "title" -->
      <context name="reflink-title" attribute="Normal Text" lineEndContext="#pop" fallthroughContext="#pop">
        <RegExpr attribute="Reference-Link" context="#pop" String="\s+&quot;(?:\\.|[^&quot;\\])*&quot;"/>
      </context>

      <!-- Target Ref.: [foo]: http://example.com/  "Optional Title Here" -->
      <context name="reflinktarget" attribute="Reference-Link Target" lineEndContext="#pop" fallthroughContext="#pop">
        <RegExpr attribute="Reference-Link Target: Link" context="#pop!reflinktarget-title" String="\s*&link;(?=\s|$)"/>
        <RegExpr attribute="Reference-Link Target" context="#pop!reflinktarget-link" String="\s*&lt;(?=&link;&gt;(?:\s|$))"/>
        <RegExpr attribute="Reference-Link Target" context="#pop!reflinktarget-title" String="\s*#[\w\.\:\-]+\b"/>
      </context>
      <context name="reflinktarget-link" attribute="Reference-Link Target" lineEndContext="#pop" fallthroughContext="#pop">
        <DetectChar attribute="Reference-Link Target" context="#pop!reflinktarget-title" char="&gt;"/>
        <RegExpr attribute="Reference-Link Target: Link" String="&link;"/>
      </context>
      <context name="reflinktarget-title" attribute="Reference-Link Target" lineEndContext="#pop" fallthroughContext="#pop">
        <DetectSpaces attribute="Reference-Link Target"/>
        <RegExpr attribute="Reference-Link Target" context="#pop" String="&quot;(?:\\.|[^&quot;\\])*&quot;|&apos;(?:\\.|[^&apos;\\])*&apos;|\((?:\\.|[^\)\\])*\)"/>
      </context>

      <!-- Image -->
      <context name="inlineimage" attribute="Inline Image" lineEndContext="#pop">
        <DetectChar attribute="Inline Image" context="#pop" char=")"/>
        <Detect2Chars attribute="Inline Image" char="\" char1=")"/>
        <RegExpr attribute="Inline Image: Link" String="\b&startlink;(?:\\.|[^&quot;&gt;\s\)\\])+"/>
      </context>

  <context name="FindHTML" attribute="Normal Text" lineEndContext="#stay">
    <DetectSpaces/>
    <DetectIdentifier/>
    <StringDetect attribute="Comment" context="Comment" String="&lt;!--" beginRegion="comment" />
    <StringDetect attribute="CDATA" context="CDATA" String="&lt;![CDATA[" beginRegion="cdata" />
    <RegExpr attribute="Doctype" context="Doctype" String="&lt;!DOCTYPE\s+" insensitive="true" beginRegion="doctype" />
    <IncludeRules context="FindElements" />
    <RegExpr attribute="Processing Instruction" context="PI" String="&lt;\?[\w:-]*" beginRegion="pi" />

    <!-- as long as kde gives DTDs the text/html mimetype--><IncludeRules context="FindDTDRules" />
    <IncludeRules context="FindEntityRefs" />
  </context>

  <context name="FindElements" attribute="Other Text" lineEndContext="#pop">
    <RegExpr attribute="Element Symbols" context="ElementTagName" String="&lt;(?=(&name;))" />
    <RegExpr attribute="Element Symbols" context="ElementTagNameClose" String="&lt;/(?=(&name;))" />
  </context>

  <context name="ElementTagName" attribute="Other Text" lineEndContext="#pop">
    <IncludeRules context="FindHTMLTags" />
    <IncludeRules context="FindSpecialHTMLTags" />
    <StringDetect attribute="Element" context="#pop!El Open" String="%1" dynamic="true" />
  </context>

  <context name="ElementTagNameClose" attribute="Other Text" lineEndContext="#pop">
    <IncludeRules context="FindHTMLTagsClose" />
    <StringDetect attribute="Element" context="#pop!El Close" String="%1" dynamic="true" />
  </context>

  <!-- This allows you to insert HTML tags in other syntax definitions -->
  <context name="FindSpecialHTMLTags" attribute="Normal Text" lineEndContext="#stay">
    <WordDetect attribute="Element" context="#pop!CSS" String="style" insensitive="true" beginRegion="style" />
    <WordDetect attribute="Element" context="#pop!JS" String="script" insensitive="true" beginRegion="script" />
  </context>

  <context name="FindHTMLTags" attribute="Normal Text" lineEndContext="#stay">
    <WordDetect attribute="Element" context="#pop!El Open" String="pre" insensitive="true" beginRegion="pre" />
    <WordDetect attribute="Element" context="#pop!El Open" String="div" insensitive="true" beginRegion="div" />
    <WordDetect attribute="Element" context="#pop!El Open" String="table" insensitive="true" beginRegion="table" />
    <WordDetect attribute="Element" context="#pop!El Open" String="ul" insensitive="true" beginRegion="ul" />
    <WordDetect attribute="Element" context="#pop!El Open" String="ol" insensitive="true" beginRegion="ol" />
    <WordDetect attribute="Element" context="#pop!El Open" String="dl" insensitive="true" beginRegion="dl" />
    <WordDetect attribute="Element" context="#pop!El Open" String="article" insensitive="true" beginRegion="article" />
    <WordDetect attribute="Element" context="#pop!El Open" String="aside" insensitive="true" beginRegion="aside" />
    <WordDetect attribute="Element" context="#pop!El Open" String="details" insensitive="true" beginRegion="details" />
    <WordDetect attribute="Element" context="#pop!El Open" String="figure" insensitive="true" beginRegion="figure" />
    <WordDetect attribute="Element" context="#pop!El Open" String="footer" insensitive="true" beginRegion="footer" />
    <WordDetect attribute="Element" context="#pop!El Open" String="header" insensitive="true" beginRegion="header" />
    <WordDetect attribute="Element" context="#pop!El Open" String="main" insensitive="true" beginRegion="main" />
    <WordDetect attribute="Element" context="#pop!El Open" String="nav" insensitive="true" beginRegion="nav" />
    <WordDetect attribute="Element" context="#pop!El Open" String="section" insensitive="true" beginRegion="section" />
  </context>

  <context name="FindHTMLTagsClose" attribute="Normal Text" lineEndContext="#stay">
    <WordDetect attribute="Element" context="#pop!El Close" String="pre" insensitive="true" endRegion="pre" />
    <WordDetect attribute="Element" context="#pop!El Close" String="div" insensitive="true" endRegion="div" />
    <WordDetect attribute="Element" context="#pop!El Close" String="table" insensitive="true" endRegion="table" />
    <WordDetect attribute="Element" context="#pop!El Close" String="ul" insensitive="true" endRegion="ul" />
    <WordDetect attribute="Element" context="#pop!El Close" String="ol" insensitive="true" endRegion="ol" />
    <WordDetect attribute="Element" context="#pop!El Close" String="dl" insensitive="true" endRegion="dl" />
    <WordDetect attribute="Element" context="#pop!El Close" String="article" insensitive="true" endRegion="article" />
    <WordDetect attribute="Element" context="#pop!El Close" String="aside" insensitive="true" endRegion="aside" />
    <WordDetect attribute="Element" context="#pop!El Close" String="details" insensitive="true" endRegion="details" />
    <WordDetect attribute="Element" context="#pop!El Close" String="figure" insensitive="true" endRegion="figure" />
    <WordDetect attribute="Element" context="#pop!El Close" String="footer" insensitive="true" endRegion="footer" />
    <WordDetect attribute="Element" context="#pop!El Close" String="header" insensitive="true" endRegion="header" />
    <WordDetect attribute="Element" context="#pop!El Close" String="main" insensitive="true" endRegion="main" />
    <WordDetect attribute="Element" context="#pop!El Close" String="nav" insensitive="true" endRegion="nav" />
    <WordDetect attribute="Element" context="#pop!El Close" String="section" insensitive="true" endRegion="section" />
  </context>

  <context name="FindEntityRefs" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="EntityRef" context="#stay" String="&entref;" />
    <AnyChar attribute="Error" context="#stay" String="&amp;&lt;" />
  </context>

  <context name="FindPEntityRefs" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="EntityRef" context="#stay" String="&entref;" />
    <RegExpr attribute="PEntityRef" context="#stay" String="%&name;;" />
    <AnyChar attribute="Error" context="#stay" String="&amp;%" />
  </context>

  <context name="FindAttributes" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="Attribute" context="#stay" String="^&attributeName;|\s+&attributeName;" />
    <DetectChar attribute="Attribute Separator" context="Value" char="=" />
  </context>

  <context name="FindDTDRules" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="Doctype" context="Doctype Markupdecl" String="&lt;!(?:ELEMENT|ENTITY|ATTLIST|NOTATION)\b" />
  </context>


  <context name="Comment" attribute="Comment" lineEndContext="#stay">
    <DetectSpaces/>
    <StringDetect attribute="Comment" context="#pop" String="--&gt;" endRegion="comment" />
    <IncludeRules context="##Comments" />
    <DetectIdentifier/>
    <RegExpr attribute="Error" context="#stay" String="-(?:-(?!-&gt;))+" />
  </context>

  <context name="CDATA" attribute="Other Text" lineEndContext="#stay">
    <DetectSpaces/>
    <DetectIdentifier/>
    <StringDetect attribute="CDATA" context="#pop" String="]]&gt;" endRegion="cdata" />
    <StringDetect attribute="EntityRef" context="#stay" String="]]&amp;gt;" />
  </context>

  <context name="PI" attribute="Other Text" lineEndContext="#stay">
    <Detect2Chars attribute="Processing Instruction" context="#pop" char="?" char1="&gt;" endRegion="pi" />
  </context>

  <context name="Doctype" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Doctype" context="#pop" char="&gt;" endRegion="doctype" />
    <DetectChar attribute="Doctype" context="Doctype Internal Subset" char="[" beginRegion="int_subset" />
  </context>

  <context name="Doctype Internal Subset" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Doctype" context="#pop" char="]" endRegion="int_subset" />
    <IncludeRules context="FindDTDRules" />
    <StringDetect attribute="Comment" context="Comment" String="&lt;!--" beginRegion="comment" />
    <RegExpr attribute="Processing Instruction" context="PI" String="&lt;\?[\w:-]*" beginRegion="pi" />
    <IncludeRules context="FindPEntityRefs" />
  </context>

  <context name="Doctype Markupdecl" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Doctype" context="#pop" char="&gt;" />
    <DetectChar attribute="Value" context="Doctype Markupdecl DQ" char="&quot;" />
    <DetectChar attribute="Value" context="Doctype Markupdecl SQ" char="&apos;" />
  </context>

  <context name="Doctype Markupdecl DQ" attribute="Value" lineEndContext="#stay">
    <DetectChar attribute="Value" context="#pop" char="&quot;" />
    <IncludeRules context="FindPEntityRefs" />
  </context>

  <context name="Doctype Markupdecl SQ" attribute="Value" lineEndContext="#stay">
    <DetectChar attribute="Value" context="#pop" char="&apos;" />
    <IncludeRules context="FindPEntityRefs" />
  </context>

  <context name="El Open" attribute="Other Text" lineEndContext="#stay">
    <Detect2Chars attribute="Element Symbols" context="#pop" char="/" char1="&gt;" />
    <DetectChar attribute="Element Symbols" context="#pop" char="&gt;" />
    <IncludeRules context="FindAttributes" />
    <RegExpr attribute="Error" context="#stay" String="\S" />
  </context>

  <context name="El Close" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Element Symbols" context="#pop" char="&gt;" />
    <RegExpr attribute="Error" context="#stay" String="\S" />
  </context>

  <context name="CSS" attribute="Other Text" lineEndContext="#stay">
    <Detect2Chars attribute="Element Symbols" context="#pop" char="/" char1="&gt;" endRegion="style" />
    <DetectChar attribute="Element Symbols" context="CSS content" char="&gt;" />
    <IncludeRules context="FindAttributes" />
    <RegExpr attribute="Error" context="#stay" String="\S" />
  </context>

  <context name="CSS content" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="Element Symbols" context="CSS content Close" String="&lt;/(?=style\b)" insensitive="true" />
    <IncludeRules context="##CSS" includeAttrib="true"/>
  </context>
  <context name="CSS content Close" attribute="Other Text" lineEndContext="#stay">
    <DetectIdentifier attribute="Element" context="#pop#pop#pop!El Close" endRegion="style" />
  </context>

  <context name="JS" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="Attribute" context="Script-Type" String="(?:\s+|^)type(?=\=|\s|$)" insensitive="true"/>
    <DetectChar attribute="Element Symbols" context="JS content" char="&gt;" />
    <IncludeRules context="DefaultJS" />
  </context>
  <context name="DefaultJS" attribute="Other Text" lineEndContext="#stay">
    <Detect2Chars attribute="Element Symbols" context="#pop" char="/" char1="&gt;" endRegion="script" />
    <IncludeRules context="FindAttributes" />
    <RegExpr attribute="Error" context="#stay" String="\S" />
  </context>

  <context name="JS content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="Default JS content"/>
    <IncludeRules context="Normal##JavaScript" includeAttrib="true"/>
  </context>
  <context name="Default JS content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="FindScriptTagClose" />
    <RegExpr attribute="Comment" context="JS comment close" String="//(?=.*&lt;/script\b)" insensitive="true" />
  </context>

  <context name="FindScriptTagClose" attribute="Other Text" lineEndContext="#stay">
    <RegExpr attribute="Element Symbols" context="ScriptTagClose" String="&lt;/(?=script\b)" insensitive="true" />
  </context>
  <context name="ScriptTagClose" attribute="Other Text" lineEndContext="#stay">
    <DetectIdentifier attribute="Element" context="#pop#pop#pop!El Close" endRegion="script" />
  </context>

  <context name="JS comment close" attribute="Comment" lineEndContext="#pop">
    <RegExpr attribute="Element Symbols" context="#pop!ScriptTagClose" String="&lt;/(?=script\b)" insensitive="true" />
    <DetectSpaces />
    <IncludeRules context="##Comments" />
  </context>

  <context name="Value" attribute="Other Text" lineEndContext="#stay" fallthrough="true" fallthroughContext="Value NQ">
    <DetectChar attribute="Value" context="Value DQ" char="&quot;" />
    <DetectChar attribute="Value" context="Value SQ" char="&apos;" />
    <DetectSpaces />
  </context>

  <context name="Value NQ" attribute="Other Text" lineEndContext="#pop#pop" fallthrough="true" fallthroughContext="#pop#pop">
    <IncludeRules context="FindEntityRefs" />
    <RegExpr attribute="Value" context="#stay" String="/(?!&gt;)|[^/&gt;&lt;&quot;&apos;\s]" />
  </context>

  <context name="Value DQ" attribute="Value" lineEndContext="#stay">
    <DetectChar attribute="Value" context="#pop#pop" char="&quot;" />
    <IncludeRules context="FindEntityRefs" />
  </context>

  <context name="Value SQ" attribute="Value" lineEndContext="#stay">
    <DetectChar attribute="Value" context="#pop#pop" char="&apos;" />
    <IncludeRules context="FindEntityRefs" />
  </context>

  <!-- Read content from the "type" attribute to change the language to
       highlight in the <script> tag. The default language is JavaScript. -->

  <context name="Script-Type" attribute="Other Text" lineEndContext="#stay" fallthrough="true" fallthroughContext="#pop">
    <DetectSpaces />
    <DetectChar attribute="Attribute" context="#pop!Script-Type Value" char="=" />
  </context>
  <context name="Script-Type Value" attribute="Other Text" lineEndContext="#stay" fallthrough="true" fallthroughContext="#pop!Value">
    <DetectSpaces />
    <!-- TypeScript -->
    <StringDetect attribute="Value" context="#pop#pop!TypeScript" String="&quot;text/typescript&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!TypeScript" String="&apos;text/typescript&apos;"/>
    <!-- JSX (JavaScript React) -->
    <StringDetect attribute="Value" context="#pop#pop!JSX" String="&quot;text/jsx&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!JSX" String="&apos;text/jsx&apos;"/>
    <StringDetect attribute="Value" context="#pop#pop!JSX" String="&quot;text/babel&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!JSX" String="&apos;text/babel&apos;"/>
    <!-- MustacheJS / HandlebarsJS / RactiveJS -->
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&quot;x-tmpl-mustache&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&apos;x-tmpl-mustache&apos;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&quot;text/mustache&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&apos;text/mustache&apos;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&quot;text/x-mustache-template&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&apos;text/x-mustache-template&apos;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&quot;text/x-handlebars-template&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&apos;text/x-handlebars-template&apos;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&quot;text/ractive&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!MustacheJS" String="&apos;text/ractive&apos;"/>
    <!-- HTML templates -->
    <StringDetect attribute="Value" context="#pop#pop!Script HTML template" String="&quot;text/html&quot;"/>
    <StringDetect attribute="Value" context="#pop#pop!Script HTML template" String="&apos;text/html&apos;"/>
  </context>

  <context name="JSX" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Element Symbols" context="JSX content" char="&gt;" />
    <IncludeRules context="DefaultJS" />
  </context>
  <context name="JSX content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="Default JS content"/>
    <IncludeRules context="Normal##JavaScript React (JSX)" includeAttrib="true"/>
  </context>

  <context name="TypeScript" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Element Symbols" context="TypeScript content" char="&gt;" />
    <IncludeRules context="DefaultJS" />
  </context>
  <context name="TypeScript content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="Default JS content"/>
    <IncludeRules context="Normal##TypeScript" includeAttrib="true"/>
  </context>

  <context name="MustacheJS" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Element Symbols" context="MustacheJS content" char="&gt;" />
    <IncludeRules context="DefaultJS" />
  </context>
  <context name="MustacheJS content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="FindScriptTagClose" />
    <StringDetect attribute="Error" context="#stay" String="&lt;script&gt;" insensitive="true" />
    <RegExpr attribute="Error" context="#stay" String="&lt;script\b" insensitive="true" />
    <IncludeRules context="Base##Mustache/Handlebars (HTML)" includeAttrib="true"/>
  </context>

  <context name="Script HTML template" attribute="Other Text" lineEndContext="#stay">
    <DetectChar attribute="Element Symbols" context="Script HTML template content" char="&gt;" />
    <IncludeRules context="DefaultJS" />
  </context>
  <context name="Script HTML template content" attribute="Other Text" lineEndContext="#stay">
    <IncludeRules context="FindScriptTagClose" />
    <StringDetect attribute="Error" context="#stay" String="&lt;script&gt;" insensitive="true" />
    <RegExpr attribute="Error" context="#stay" String="&lt;script\b" insensitive="true" />
    <IncludeRules context="FindHTML" />
  </context>


    </contexts>
    <itemDatas>
      <itemData name="Normal Text" defStyleNum="dsNormal"/>
      <itemData name="Emphasis Text" defStyleNum="dsNormal" italic="true"/>
      <itemData name="Strong Text" defStyleNum="dsNormal" bold="true"/>
      <itemData name="Strong-Emphasis Text" defStyleNum="dsNormal" italic="true" bold="true"/>
      <itemData name="Strikethrough Text" defStyleNum="dsNormal" strikeOut="true"/>
      <itemData name="Normal Text: Link" defStyleNum="dsNormal" underline="true" spellChecking="false"/>
      <itemData name="Horizontal Rule" defStyleNum="dsNormal" bold="true" spellChecking="false"/>
      <itemData name="Line Break" defStyleNum="dsNormal" underline="true" color="#999999" spellChecking="false"/>
      <itemData name="Header H1" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Header H2" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Header H3" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Header H4" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Header H5" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Header H6" defStyleNum="dsFunction" bold="true"/>
      <itemData name="Blockquote" defStyleNum="dsAttribute" spellChecking="false"/>
      <itemData name="Blockquote: Normal Text" defStyleNum="dsAttribute"/>
      <itemData name="Blockquote: Emphasis Text" defStyleNum="dsAttribute" italic="true"/>
      <itemData name="Blockquote: Strong Text" defStyleNum="dsAttribute" bold="true"/>
      <itemData name="Blockquote: Strong-Emphasis Text" defStyleNum="dsAttribute" italic="true" bold="true"/>
      <itemData name="Blockquote: Strikethrough Text" defStyleNum="dsAttribute" strikeOut="true"/>
      <itemData name="Blockquote: Link" defStyleNum="dsAttribute" underline="true" spellChecking="false"/>
      <itemData name="List" defStyleNum="dsSpecialString" bold="1" spellChecking="false"/>
      <itemData name="Number List" defStyleNum="dsSpecialString" spellChecking="false"/>
      <itemData name="List: Normal Text" defStyleNum="dsNormal"/>
      <itemData name="List: Emphasis Text" defStyleNum="dsNormal" italic="true"/>
      <itemData name="List: Strong Text" defStyleNum="dsNormal" bold="true"/>
      <itemData name="List: Strong-Emphasis Text" defStyleNum="dsNormal" italic="true" bold="true"/>
      <itemData name="List: Strikethrough Text" defStyleNum="dsNormal" strikeOut="true"/>
      <itemData name="List: Link" defStyleNum="dsNormal" underline="true" spellChecking="false"/>
      <itemData name="List: Checkbox" defStyleNum="dsVariable" spellChecking="false"/>
      <itemData name="Comment" defStyleNum="dsComment"/>
      <itemData name="Code" defStyleNum="dsInformation"/>
      <itemData name="Fenced Code" defStyleNum="dsInformation" spellChecking="false"/>
      <itemData name="Auto-Link" defStyleNum="dsOthers" spellChecking="false"/>
      <itemData name="Link" defStyleNum="dsOthers" underline="true" spellChecking="false"/>
      <itemData name="Mailto-Link" defStyleNum="dsOthers" spellChecking="false"/>
      <itemData name="Email" defStyleNum="dsOthers" italic="true" underline="true" spellChecking="false"/>
      <itemData name="Footnote" defStyleNum="dsOthers" italic="true"/>
      <itemData name="Reference-Link" defStyleNum="dsComment" italic="false"/>
      <itemData name="Reference-Link Name" defStyleNum="dsOthers" underline="true" italic="false"/>
      <itemData name="Reference-Link ID" defStyleNum="dsComment" italic="false"/>
      <itemData name="Reference-Link: Link" defStyleNum="dsComment" underline="true" italic="false" spellChecking="false"/>
      <itemData name="Reference-Link: Email" defStyleNum="dsComment" italic="true" underline="true" spellChecking="false"/>
      <itemData name="Reference-Link Target" defStyleNum="dsOthers" italic="false" bold="false"/>
      <itemData name="Reference-Link Target: Link" defStyleNum="dsOthers" underline="true" spellChecking="false"/>
      <itemData name="Inline Image" defStyleNum="dsAlert" italic="false" bold="false"/>
      <itemData name="Reference Image" defStyleNum="dsAlert" italic="false" bold="false"/>
      <itemData name="Inline Image: Link" defStyleNum="dsAlert" italic="false" bold="false" underline="true"/>
      <itemData name="Metadata Title" defStyleNum="dsAnnotation"/>
      <itemData name="Metadata" defStyleNum="dsComment" italic="0"/>
      <itemData name="Backslash Escape" defStyleNum="dsSpecialChar" spellChecking="false"/>
      <itemData name="EntityRef" defStyleNum="dsDecVal" spellChecking="false"/>
    </itemDatas>
  </highlighting>
  <general>
    <keywords additionalDeliminator="`"/>
    <comments>
      <comment name="multiLine" start="&startcomment;" end="&endcomment;" region="comment"/>
    </comments>
  </general>
</language>
<!-- kate: replace-tabs on; indent-width 2; tab-width 2; -->
