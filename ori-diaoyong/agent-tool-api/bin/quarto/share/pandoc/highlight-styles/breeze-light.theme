{"metadata": {"copyright": ["SPDX-FileCopyrightText: 2016 Volker Krause <<EMAIL>>", "SPDX-FileCopyrightText: 2016 <PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SPDX-License-Identifier: MIT", "revision": 8, "name": "Breeze Light"}, "text-styles": {"Normal": {"text-color": "#1f1c1b", "selected-text-color": "#ffffff", "bold": false, "italic": false, "underline": false, "strike-through": false}, "Keyword": {"text-color": "#1f1c1b", "selected-text-color": "#ffffff", "bold": true}, "Function": {"text-color": "#644a9b", "selected-text-color": "#452886"}, "Variable": {"text-color": "#0057ae", "selected-text-color": "#00316e"}, "ControlFlow": {"text-color": "#1f1c1b", "selected-text-color": "#ffffff", "bold": true}, "Operator": {"text-color": "#ca60ca", "selected-text-color": "#a44ea4"}, "BuiltIn": {"text-color": "#644a9b", "selected-text-color": "#452886", "bold": true}, "Extension": {"text-color": "#0095ff", "selected-text-color": "#ffffff", "bold": true}, "Preprocessor": {"text-color": "#006e28", "selected-text-color": "#006e28"}, "Attribute": {"text-color": "#0057ae", "selected-text-color": "#00316e"}, "Char": {"text-color": "#924c9d", "selected-text-color": "#6c2477"}, "SpecialChar": {"text-color": "#3daee9", "selected-text-color": "#fcfcfc"}, "String": {"text-color": "#bf0303", "selected-text-color": "#9c0e0e"}, "VerbatimString": {"text-color": "#bf0303", "selected-text-color": "#9c0e0e"}, "SpecialString": {"text-color": "#ff5500", "selected-text-color": "#ff5500"}, "Import": {"text-color": "#ff5500", "selected-text-color": "#ff5500"}, "DataType": {"text-color": "#0057ae", "selected-text-color": "#00316e"}, "DecVal": {"text-color": "#b08000", "selected-text-color": "#805c00"}, "BaseN": {"text-color": "#b08000", "selected-text-color": "#805c00"}, "Float": {"text-color": "#b08000", "selected-text-color": "#805c00"}, "Constant": {"text-color": "#aa5500", "selected-text-color": "#5e2f00"}, "Comment": {"text-color": "#898887", "selected-text-color": "#5e5d5d"}, "Documentation": {"text-color": "#607880", "selected-text-color": "#46585e"}, "Annotation": {"text-color": "#ca60ca", "selected-text-color": "#a44ea4"}, "CommentVar": {"text-color": "#0095ff", "selected-text-color": "#ffffff"}, "RegionMarker": {"text-color": "#0057ae", "selected-text-color": "#00316e", "background-color": "#e0e9f8"}, "Information": {"text-color": "#b08000", "selected-text-color": "#805c00"}, "Warning": {"text-color": "#bf0303", "selected-text-color": "#9c0e0e"}, "Alert": {"text-color": "#bf0303", "selected-text-color": "#9c0e0e", "background-color": "#f7e6e6", "bold": true}, "Error": {"text-color": "#bf0303", "selected-text-color": "#9c0e0e", "underline": true}, "Others": {"text-color": "#006e28", "selected-text-color": "#006e28"}}, "editor-colors": {"BackgroundColor": "#ffffff", "CodeFolding": "#94caef", "BracketMatching": "#ffff00", "CurrentLine": "#f8f7f6", "IconBorder": "#f0f0f0", "IndentationLine": "#d2d2d2", "LineNumbers": "#a0a0a0", "CurrentLineNumber": "#1e1e1e", "MarkBookmark": "#0000ff", "MarkBreakpointActive": "#ff0000", "MarkBreakpointReached": "#ffff00", "MarkBreakpointDisabled": "#ff00ff", "MarkExecution": "#a0a0a4", "MarkWarning": "#00ff00", "MarkError": "#ff0000", "ModifiedLines": "#fdbc4b", "ReplaceHighlight": "#00ff00", "SavedLines": "#2ecc71", "SearchHighlight": "#ffff00", "TextSelection": "#94caef", "Separator": "#d5d5d5", "SpellChecking": "#bf0303", "TabMarker": "#d2d2d2", "TemplateBackground": "#d6d2d0", "TemplatePlaceholder": "#baf8ce", "TemplateFocusedPlaceholder": "#76da98", "TemplateReadOnlyPlaceholder": "#f6e6e6", "WordWrapMarker": "#ededed"}}