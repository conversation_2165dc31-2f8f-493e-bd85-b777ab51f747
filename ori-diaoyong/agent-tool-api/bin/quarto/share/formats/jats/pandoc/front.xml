$if(quarto-internal.jats-extended)$
$if(citation)$
<journal-meta>
$-- Formulate the journal-id
$if(citation.container-id)$
$for(citation.container-id)$
$if(citation.container-id.type)$
<journal-id journal-id-type="$citation.container-id.type$">$citation.container-id.value$</journal-id>
$else$
<journal-id>$citation.container-id.value$</journal-id>
$endif$
$endfor$
$else$
$-- Fallback: an empty journal-id in case none is available.
<journal-id></journal-id>
$endif$

$-- Formulate the journal title
<journal-title-group>
$if(citation.container-title)$
<journal-title>$citation.container-title$</journal-title>
$endif$
$if(citation.container-title-short)$
<abbrev-journal-title>$citation.container-title-short$</abbrev-journal-title>
$endif$
</journal-title-group>
$if(citation.issn)$
<issn>$citation.issn$</issn>
$endif$
$if(citation.pissn)$
<issn publication-format="print">$citation.pissn$</issn>
$endif$
$if(citation.eissn)$
<issn publication-format="electronic">$citation.eissn$</issn>
$endif$
$-- At least one issn element is required; use empty issn as fallback
$if(citation.issn)$
$elseif(citation.pissn)$
$elseif(citation.eissn)$
$else$
<issn></issn>
$endif$

<publisher>
<publisher-name>$citation.publisher$</publisher-name>
$if(citation.publisher-location)$
<publisher-loc>$citation.publisher-location$</publisher-loc>
$endif$
</publisher>
</journal-meta>
$endif$
$endif$


<article-meta>
$if(quarto-internal.jats-extended)$
$if(citation.article-id)$
$for(citation.article-id)$
$if(citation.article-id.type)$
<article-id pub-id-type="$citation.article-id.type$">$citation.article-id.value$</article-id>
$else$
<article-id pub-id-type="publisher-id">$citation.article-id.value$</article-id>
$endif$
$endfor$
$endif$
$if(citation.doi)$
<article-id pub-id-type="doi">$citation.doi$</article-id>
$endif$
$if(citation.pmid)$
<article-id pub-id-type="pmid">$citation.pmid$</article-id>
$endif$
$if(citation.pmcid)$
<article-id pub-id-type="pmcid">$citation.pmcid$</article-id>
$endif$
$if(citation.art-access-id)$
<article-id pub-id-type="art-access-id">$citation.art-access-id$</article-id>
$endif$

$if(citation.subject)$
<article-categories>
<subj-group>
<subject>$citation.subject$</subject>
$if(citation.categories)$
<subj-group subj-group-type="categories">
$for(citation.categories)$
<subject>$citation.categories$</subject>
$endfor$
</subj-group>
</subj-group>
$endif$
</article-categories>
$endif$
$endif$

$if(title)$
<title-group>
<article-title>$title$</article-title>
$if(subtitle)$
<subtitle>${subtitle}</subtitle>
$endif$
</title-group>
$endif$

${ authors.xml() }

$if(citation.issued)$<pub-date date-type="pub" publication-format="electronic" iso-8601-date="$citation.issued.iso-8601$">
$if(citation.issued.date-parts/first/first)$<year>$citation.issued.date-parts/first/first$</year>$endif$
$if(citation.issued.date-parts/first/rest)$<month>$citation.issued.date-parts/first/rest/first$</month>$endif$
$if(citation.issued.date-parts/first/rest/rest)$<day>$citation.issued.date-parts/first/rest/rest/first$</day>
$endif$</pub-date>$endif$

$if(citation.volume)$<volume>$citation.volume$</volume>$endif$
$if(citation.issue)$<issue>$citation.issue$</issue>$endif$
$if(citation.isbn)$<isbn>$citation.isbn$</isbn>$endif$
$if(citation.first-page)$<fpage>$citation.first-page$</fpage>$endif$
$if(citation.last-page)$<lpage>$citation.last-page$</lpage>$endif$
$if(citation.elocation-id)$<elocation-id>$citation.elocation-id$</elocation-id>$endif$
<history></history>

$if(quarto-internal.has-permissions)$
<permissions>
$for(copyright.statement)$
<copyright-statement>$copyright.statement$</copyright-statement>
$endfor$
$for(copyright.year)$
<copyright-year>$copyright.year$</copyright-year>
$endfor$
$for(copyright.holder)$
<copyright-holder>$copyright.holder$</copyright-holder>
$endfor$

$for(license)$
<license$if(it.type)$ license-type="${it.type}"$endif$$if(it.link)$ xlink:href="${it.link}"$endif$>
$if(it.url)$
<ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">${it.url}</ali:license_ref>
$endif$
$if(it.text)$<license-p>${it.text}</license-p>$endif$
</license>
$endfor$
</permissions>
$endif$

$if(abstract)$
<abstract>
$abstract$
</abstract>
$endif$
$if(tags)$
<kwd-group kwd-group-type="author">
$for(tags)$
<kwd>$tags$</kwd>
$endfor$
</kwd-group>
$endif$


$for(funding)$
<funding-group>
$for(funding.awards)$
<award-group>
$for(it.source)$
<funding-source$if(it.country)$ country="$it.country$"$endif$$if(it.type)$ source-type="$it.type$"$endif$>
$if(it.text)$
$it.text$
$elseif(it.institution)$
$it.institution:institution.xml()$
$endif$
</funding-source>
$endfor$

$if(it.id)$
<award-id>$it.id$</award-id>
$endif$

$for(it.recipient)$
<principal-award-recipient>
$if(it.name)$
$it:name.xml()$
$elseif(it.text)$
$it.text$
$elseif(it.institution)$
$it.institution:institution.xml()$
$endif$
</principal-award-recipient>
$endfor$

$for(it.investigator)$
<principal-investigator>
$if(it.name)$
$it:name.xml()$
$elseif(it.institution)$
$it.institution:institution.xml()$
$elseif(it.text)$
$it.text$
$endif$
</principal-investigator>
$endfor$
</award-group>
$endfor$
$if(funding.statement)$
<funding-statement>$funding.statement$</funding-statement>
$endif$
$if(funding.open-access)$
<open-access>$funding.open-access$</open-access>
$endif$
</funding-group>
$endfor$


</article-meta>

$if(quarto-internal.jats-extended)$
$if(notes)$
<notes>$notes$</notes>
$endif$
$endif$
