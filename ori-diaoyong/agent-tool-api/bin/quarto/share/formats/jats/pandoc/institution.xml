$-- wrap affiliation if it has a known institution identifier
$if(it.department)$
<institution content-type="dept">${it.department}</institution>
$endif$
<institution-wrap>
<institution>${it.name}</institution>
$if(it.isni)$
<institution-id institution-id-type="ISNI">${it.isni}</institution-id>
$endif$
$if(it.ringgold)$
<institution-id institution-id-type="Ringgold">${it.ringgold}</institution-id>
$endif$
$if(it.ror)$
<institution-id institution-id-type="ROR">${it.ror}</institution-id>
$endif$
$for(it.pid)$
<institution-id institution-id-type="${it.type}">${it.id}</institution-id>
$endfor$
</institution-wrap>