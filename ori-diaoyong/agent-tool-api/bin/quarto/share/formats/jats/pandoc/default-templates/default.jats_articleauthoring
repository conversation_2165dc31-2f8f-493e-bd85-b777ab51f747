<?xml version="1.0" encoding="utf-8" ?>
$if(xml-stylesheet)$
<?xml-stylesheet type="text/xsl" href="$xml-stylesheet$"?>
$endif$
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Article Authoring DTD v1.2 20190208//EN"
                  "JATS-articleauthoring1.dtd">
$if(article.type)$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="$article.type$">
$else$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="other">
$endif$
<front>
<article-meta>
$if(title)$
<title-group>
<article-title>$title$</article-title>
$if(subtitle)$
<subtitle>${subtitle}</subtitle>
$endif$
</title-group>
$endif$
$if(author)$
<contrib-group>
$for(author)$
<contrib contrib-type="author"$if(author.equal-contrib)$ equal-contrib="yes"$endif$$if(author.cor-id)$ corresp="yes"$endif$>
$if(author.orcid)$
<contrib-id contrib-id-type="orcid">$author.orcid$</contrib-id>
$endif$
$if(author.surname)$
<name>
<surname>$if(author.non-dropping-particle)$${author.non-dropping-particle} $endif$${author.surname}</surname>
<given-names>${author.given-names}$if(author.dropping-particle)$ ${author.dropping-particle}$endif$</given-names>
$if(author.prefix)$
<prefix>${author.suffix}</prefix>
$endif$
$if(author.suffix)$
<suffix>${author.suffix}</suffix>
$endif$
</name>
$elseif(author.name)$
<string-name>$author.name$</string-name>
$else$
<string-name>$author$</string-name>
$endif$
$for(author.affiliation)$
${ it:affiliations.jats() }
$endfor$
$if(author.email)$
<email>$author.email$</email>
$endif$
$if(author.cor-id)$
<xref ref-type="corresp" rid="cor-$author.cor-id$"><sup>*</sup></xref>
$endif$
</contrib>
$endfor$
</contrib-group>
$endif$
<permissions>
$for(copyright.statement)$
<copyright-statement>$copyright.statement$</copyright-statement>
$endfor$
$for(copyright.year)$
<copyright-year>$copyright.year$</copyright-year>
$endfor$
$for(copyright.holder)$
<copyright-holder>$copyright.holder$</copyright-holder>
$endfor$
$if(copyright.text)$
<license license-type="$copyright.type$" xlink:href="$copyright.link$">
<license-p>$copyright.text$</license-p>
</license>
$endif$
$for(license)$
<license$if(it.type)$ license-type="${it.type}"$endif$$if(it.link)$ xlink:href="${it.link}"$endif$>
<license-p>$if(it.text)$${it.text}$else$${it}$endif$</license-p>
</license>
$endfor$
</permissions>
<abstract>
$abstract$
</abstract>
$if(tags)$
<kwd-group kwd-group-type="author">
$for(tags)$
<kwd>$tags$</kwd>
$endfor$
</kwd-group>
$endif$
$if(article.funding-statement)$
<funding-group>
<funding-statement>$article.funding-statement$</funding-statement>
</funding-group>
$endif$
</article-meta>
</front>
<body>
$body$
</body>
<back>
$if(back)$
$back$
$endif$
</back>
</article>
