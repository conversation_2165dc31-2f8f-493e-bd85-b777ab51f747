!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.stickyThead=t():e.stickyThead=t()}(window,function(){return i={},o.m=n=[function(e,t,n){"use strict";function c(e){var t=e.getBoundingClientRect();return{top:t.top+window.pageYOffset,left:t.left+window.pageXOffset}}function u(e){if(e==window)return window.innerHeight;if(e==document)return Math.max(document.documentElement.clientHeight,document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight);var t=parseFloat(getComputedStyle(e,null).height.replace("px",""));return t||e.offsetHeight}function f(e){if(e==window)return window.innerWidth;if(e==document)return Math.max(document.documentElement.clientWidth,document.body.scrollWidth,document.documentElement.scrollWidth,document.body.offsetWidth,document.documentElement.offsetWidth);var t=parseFloat(getComputedStyle(e,null).width.replace("px",""));return t||e.offsetWidth}function p(e,t){for(var n in t)e.style[n]=t[n]}function g(e,t,n){var i=new CustomEvent(e,n?{}:{details:n});t.dispatchEvent(i)}n.r(t),n.d(t,"apply",function(){return i});var l={_storage:new WeakMap,put:function(e,t,n){this._storage.has(e)||this._storage.set(e,new Map),this._storage.get(e).set(t,n)},get:function(e,t){var n=this._storage.get(e);return n&&n.get(t)},remove:function(e,t){var n=this._storage.get(e);if(n){var i=n.delete(t);return 0===!n.size&&this._storage.delete(e),i}}};function i(e,n){var s="stickyThead",o=0,r={fixedOffset:0,leftOffset:0,marginTop:0,objDocument:document,objHead:document.head,objWindow:window,scrollableArea:window,cacheHeaderHeight:!1,zIndex:3};function i(i,t){var a=this;a.el=i,a.id=o++,a.$clonedHeader=null,a.$originalHeader=null,a.cachedHeaderHeight=null,a.isSticky=!1,a.hasBeenSticky=!1,a.leftOffset=null,a.topOffset=null,a.init=function(){a.setOptions(t),a.el.style.padding="0px",a.$originalHeader=a.el.querySelector("thead"),a.$clonedHeader=a.$originalHeader.cloneNode(!0),g("clonedHeader."+s,a.el,a.$clonedHeader),a.$clonedHeader.setAttribute("class","tableFloatingHeader"),p(a.$clonedHeader,{display:"none",opacity:0}),a.$originalHeader.setAttribute("class","tableFloatingHeaderOriginal"),a.$originalHeader.insertAdjacentElement("afterend",a.$clonedHeader);var e=document.createElement("style");e.setAttribute("type","text/css"),e.setAttribute("media","print"),e.innerHTML=".tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}",a.$printStyle=e,a.$head.appendChild(a.$printStyle),a.$clonedHeader.querySelectorAll("input, select").forEach(function(e){e.setAttribute("disabled",!0)}),a.updateWidth(),a.toggleHeaders(),a.bind()},a.destroy=function(){a.el&&a.el.removeEventListener("destroyed",a.teardown),a.teardown()},a.teardown=function(){a.isSticky&&p(a.$originalHeader,{position:"static"}),l.remove(a.el,s),a.unbind(),a.$clonedHeader.parentNode.removeChild(a.$clonedHeader),a.$originalHeader.classList.remove("tableFloatingHeaderOriginal"),p(a.$originalHeader,{visibility:"visible"}),a.$printStyle.parentNode.removeChild(a.$printStyle),a.el=null,a.$el=null},a.bind=function(){a.$scrollableArea.addEventListener("scroll",a.toggleHeaders),a.isWindowScrolling||(a.$window.addEventListener("scroll",a.setPositionValues),a.$window.addEventListener("resize",a.toggleHeaders)),a.$scrollableArea.addEventListener("resize",a.toggleHeaders),a.$scrollableArea.addEventListener("resize",a.updateWidth)},a.unbind=function(){a.$scrollableArea.removeEventListener("scroll",a.toggleHeaders),a.isWindowScrolling||(a.$window.removeEventListener("scroll",a.setPositionValues),a.$window.removeEventListener("resize",a.toggleHeaders)),a.$scrollableArea.removeEventListener("resize",a.updateWidth)},a.debounce=function(n,i){var o=null;return function(){var e=this,t=arguments;clearTimeout(o),o=setTimeout(function(){n.apply(e,t)},i)}},a.toggleHeaders=a.debounce(function(){if(a.el){var e,t,n,i=a.isWindowScrolling?isNaN(a.options.fixedOffset)?a.options.fixedOffset.offsetHeight:a.options.fixedOffset:c(a.$scrollableArea).top+(isNaN(a.options.fixedOffset)?0:a.options.fixedOffset),o=c(a.el),r=a.$scrollableArea.pageYOffset+i,l=a.$scrollableArea.pageXOffset,d=a.isWindowScrolling?r>o.top:i>o.top;d&&(t=a.options.cacheHeaderHeight?a.cachedHeaderHeight:u(a.$originalHeader),n=(a.isWindowScrolling?r:0)<o.top+u(a.el)-t-(a.isWindowScrolling?0:i)),d&&n?(e=o.left-l+a.options.leftOffset,p(a.$originalHeader,{position:"fixed",marginTop:a.options.marginTop+"px",top:0,left:e+"px",zIndex:a.options.zIndex}),a.leftOffset=e,a.topOffset=i,a.$clonedHeader.style.display="",a.isSticky||(a.isSticky=!0,a.updateWidth(),g("enabledStickiness."+s,a.el)),a.setPositionValues()):a.isSticky&&(a.$originalHeader.style.position="static",a.$clonedHeader.style.display="none",a.isSticky=!1,a.resetWidth(a.$clonedHeader.querySelectorAll("td,th"),a.$originalHeader.querySelectorAll("td,th")),g("disabledStickiness."+s,a.el))}},0),a.setPositionValues=a.debounce(function(){var e=a.$window.pageYOffset,t=a.$window.pageXOffset;!a.isSticky||e<0||e+u(a.$window)>u(a.$document)||t<0||t+f(a.$window)>f(a.$document)||p(a.$originalHeader,{top:a.topOffset-(a.isWindowScrolling?0:e)+"px",left:a.leftOffset-(a.isWindowScrolling?0:t)+"px"})},0),a.updateWidth=a.debounce(function(){if(a.isSticky){a.$originalHeaderCells||(a.$originalHeaderCells=a.$originalHeader.querySelectorAll("th,td")),a.$clonedHeaderCells||(a.$clonedHeaderCells=a.$clonedHeader.querySelectorAll("th,td"));var e=a.getWidth(a.$clonedHeaderCells);a.setWidth(e,a.$clonedHeaderCells,a.$originalHeaderCells),a.$originalHeader.style.width=f(a.$clonedHeader),a.options.cacheHeaderHeight&&(a.cachedHeaderHeight=u(a.$clonedHeader))}},0),a.getWidth=function(e){var d=[];return e.forEach(function(e,t){var n;if("border-box"===getComputedStyle(e).boxSizing){var i=e.getBoundingClientRect();n=i.width?i.width:i.right-i.left}else{if("collapse"===a.$originalHeader.querySelector("th").style.borderCollapse)if(window.getComputedStyle)n=parseFloat(window.getComputedStyle(e,null).width);else{var o=parseFloat(e.style.paddingLeft),r=parseFloat(e.style.paddingRight),l=parseFloat(e.style.borderWidth);n=e.offsetWidth-o-r-l}else n=f(e)}d[t]=n}),d},a.setWidth=function(i,e,o){e.forEach(function(e,t){var n=i[t];p(o[t],{minWidth:n+"px",maxWidth:n+"px"})})},a.resetWidth=function(e,n){e.forEach(function(e,t){p(n[t],{minWidth:i.style.minWidth,maxWidth:i.style.maxWidth})})},a.setOptions=function(e){var t,n;a.options=(t=e,n=r,Object.keys(t||{}).forEach(function(e){n[e]=t[e]}),n),a.$window=a.options.objWindow,a.$head=a.options.objHead,a.$document=a.options.objDocument,a.$scrollableArea=a.options.scrollableArea,a.isWindowScrolling=a.$scrollableArea===a.$window},a.updateOptions=function(e){a.setOptions(e),a.unbind(),a.bind(),a.updateWidth(),a.toggleHeaders()},a.el.addEventListener("destroyed",a.teardown.bind(a)),a.init()}return e.forEach(function(e){var t=l.get(e,s);t?"string"==typeof n?t[n].apply(t):t.updateOptions(n):"destroy"!==n&&l.put(e,s,new i(e,n))})}window.dataStore=l,"function"!=typeof window.CustomEvent&&(window.CustomEvent=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n})}],o.c=i,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)o.d(n,i,function(e){return t[e]}.bind(null,i));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=0);function o(e){if(i[e])return i[e].exports;var t=i[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}var n,i});
//# sourceMappingURL=stickythead.js.map