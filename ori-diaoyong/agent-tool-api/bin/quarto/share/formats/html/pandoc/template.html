<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="$lang$" xml:lang="$lang$"$if(dir)$ dir="$dir$"$endif$>

<head>

$metadata.html()$

<style>
$styles.html()$
</style>

<!-- htmldependencies:E3FAD763 -->
$for(header-includes)$
$header-includes$
$endfor$

$if(math)$
$if(mathjax)$
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
$endif$
  $math$

<script type="text/javascript">
const typesetMath = (el) => {
  if (window.MathJax) {
    // MathJax Typeset
    window.MathJax.typeset([el]);
  } else if (window.katex) {
    // KaTeX Render
    var mathElements = el.getElementsByClassName("math");
    var macros = [];
    for (var i = 0; i < mathElements.length; i++) {
      var texText = mathElements[i].firstChild;
      if (mathElements[i].tagName == "SPAN") {
        window.katex.render(texText.data, mathElements[i], {
          displayMode: mathElements[i].classList.contains('display'),
          throwOnError: false,
          macros: macros,
          fleqn: false
        });
      }
    }
  }
}
window.Quarto = {
  typesetMath
};
</script>
$endif$

$for(css)$
<link rel="stylesheet" href="$css$" />
$endfor$
</head>

<body>

$for(include-before)$
$include-before$
$endfor$

$if(title)$
$title-block.html()$
$elseif(subtitle)$
$title-block.html()$
$elseif(by-author)$
$title-block.html()$
$elseif(date)$
$title-block.html()$
$elseif(categories)$
$title-block.html()$
$elseif(date-modified)$
$title-block.html()$
$elseif(doi)$
$title-block.html()$
$elseif(abstract)$
$title-block.html()$
$elseif(keywords)$
$title-block.html()$
$endif$


$if(toc)$
$toc.html()$
$endif$

$body$

$for(include-after)$
$include-after$
$endfor$

</body>

</html>
