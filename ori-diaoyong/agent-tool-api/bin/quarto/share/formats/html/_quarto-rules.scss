// floating

.top-right {
  position: absolute;
  top: 1em;
  right: 1em;
}

// hidden

// https://github.com/quarto-dev/quarto-cli/issues/5403#issuecomment-1533791947
.visually-hidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: auto;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
}

.hidden {
  display: none !important;
}

.zindex-bottom {
  z-index: -1 !important;
}

// layout and figures

figure.figure {
  display: block;
}

.quarto-layout-panel {
  margin-bottom: 1em;
}

.quarto-layout-panel > figure {
  width: 100%;
}
.quarto-layout-panel > figure > figcaption,
.quarto-layout-panel > .panel-caption {
  margin-top: 10pt;
}

.quarto-layout-panel > .table-caption {
  margin-top: 0px;
}

.table-caption p {
  margin-bottom: 0.5em;
}

.quarto-layout-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.quarto-layout-valign-top {
  align-items: flex-start;
}
.quarto-layout-valign-bottom {
  align-items: flex-end;
}
.quarto-layout-valign-center {
  align-items: center;
}
.quarto-layout-cell {
  position: relative;
  margin-right: 20px;
}
.quarto-layout-cell:last-child {
  margin-right: 0;
}
.quarto-layout-cell figure,
.quarto-layout-cell > p {
  margin: 0.2em;
}
.quarto-layout-cell img {
  max-width: 100%;
}
.quarto-layout-cell .html-widget {
  width: 100% !important;
}
.quarto-layout-cell div figure p {
  margin: 0;
}
.quarto-layout-cell figure {
  display: block;
  margin-inline-start: 0;
  margin-inline-end: 0;
}
.quarto-layout-cell table {
  display: inline-table;
}
.quarto-layout-cell-subref figcaption,
figure .quarto-layout-row figure figcaption {
  text-align: center;
  font-style: italic;
}
.quarto-figure {
  position: relative;
  margin-bottom: 1em;
}

.quarto-figure > figure {
  width: 100%;
  margin-bottom: 0;
}
.quarto-figure-left > figure > p,
.quarto-figure-left > figure > div /* for mermaid and dot diagrams */ {
  text-align: left;
}
.quarto-figure-center > figure > p,
.quarto-figure-center > figure > div /* for mermaid and dot diagrams */ {
  text-align: center;
}
.quarto-figure-right > figure > p,
.quarto-figure-right > figure > div /* for mermaid and dot diagrams */ {
  text-align: right;
}

.quarto-figure > figure > div.cell-annotation,
.quarto-figure > figure > div code {
  text-align: left; /* override align center for code blocks */
}

figure > p:empty {
  display: none;
}
figure > p:first-child {
  margin-top: 0;
  margin-bottom: 0;
}

figure > figcaption.quarto-float-caption-bottom {
  margin-bottom: 0.5em;
}
figure > figcaption.quarto-float-caption-top {
  margin-top: 0.5em;
}

// anchor

// anchor js

div[id^="tbl-"] {
  position: relative;
}

.quarto-figure > .anchorjs-link {
  position: absolute;
  top: 0.6em;
  right: 0.5em;
}

div[id^="tbl-"] > .anchorjs-link {
  position: absolute;
  top: 0.7em;
  right: 0.3em;
}

/* workaround for anchorjs not hitting on generic :hover selector */
.quarto-figure:hover > .anchorjs-link,
div[id^="tbl-"]:hover > .anchorjs-link,
h2:hover > .anchorjs-link,
h3:hover > .anchorjs-link,
h4:hover > .anchorjs-link,
h5:hover > .anchorjs-link,
h6:hover > .anchorjs-link,
.reveal-anchorjs-link > .anchorjs-link {
  opacity: 1;
}

#title-block-header {
  margin-block-end: 1rem;
  position: relative;
  margin-top: -1px; // Chrome draws 1px white line between navbar and title block
}

#title-block-header .abstract {
  margin-block-start: 1rem;
}

#title-block-header .abstract .abstract-title {
  font-weight: 600;
}

#title-block-header a {
  text-decoration: none;
}

#title-block-header .author,
#title-block-header .date,
#title-block-header .doi {
  margin-block-end: 0.2rem;
}

#title-block-header .quarto-title-block > div {
  display: flex;
}

#title-block-header .quarto-title-block > div > h1 {
  flex-grow: 1;
}

#title-block-header .quarto-title-block > div > button {
  flex-shrink: 0;
  height: 2.25rem;
  margin-top: 0;
}

#title-block-header .quarto-title-block > div > button {
  @if mixin-exists(media-breakpoint-up) {
    @include media-breakpoint-up(lg) {
      margin-top: 5px;
    }
  }
}

// (Remove bottom margin from paragraphs in table headers)
tr.header > th > p:last-of-type {
  margin-bottom: 0px;
}

table,
table.table {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

caption,
.table-caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: center;
}

figure.quarto-float-tbl figcaption.quarto-float-caption-top {
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  text-align: center;
}

figure.quarto-float-tbl figcaption.quarto-float-caption-bottom {
  padding-top: 0.25rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

// utterances
.utterances {
  max-width: none;
  margin-left: -8px;
}

// iframe
iframe {
  margin-bottom: 1em;
}

// details
details {
  margin-bottom: 1em;
}
details[show] {
  margin-bottom: 0;
}

details > summary {
  @if variable-exists(text-muted) {
    color: $text-muted;
  }
}

details > summary > p:only-child {
  display: inline;
}

// codeCopy
pre.sourceCode,
code.sourceCode {
  position: relative;
}

// Inline code should wrap
// See https://github.com/quarto-dev/quarto-cli/issues/2649
p code:not(.sourceCode) {
  white-space: pre-wrap;
}

// default to scrolling <code> output rather than wrapping, since
// multi-column tabular output (very common for R & Python) is
// unreadable when wrapped.
code {
  white-space: pre;
}
@media print {
  code {
    white-space: pre-wrap;
  }
}
pre > code {
  display: block;
}

pre > code.sourceCode {
  white-space: $code-white-space;
}

pre > code.sourceCode > span > a:first-child::before {
  text-decoration: none;
}

pre.code-overflow-wrap > code.sourceCode {
  white-space: pre-wrap;
}

pre.code-overflow-scroll > code.sourceCode {
  white-space: pre;
}

// code linking (pkgdown style)
code a:any-link {
  color: inherit;
  text-decoration: none;
}
code a:hover {
  color: inherit;
  text-decoration: underline;
}

// task lists
ul.task-list {
  padding-left: 1em;
}

// tippy

[data-tippy-root] {
  display: inline-block;
}

.tippy-content .footnote-back {
  display: none;
}

.footnote-back {
  margin-left: 0.2em;
}

.tippy-content {
  overflow-x: auto;
}

// embedded source code
.quarto-embedded-source-code {
  display: none;
}

// unresolved crossrefs
.quarto-unresolved-ref {
  font-weight: 600;
}

// html cover image injection
.quarto-cover-image {
  max-width: 35%;
  float: right;
  margin-left: 30px;
}

// provide margin below jupyter widgets
.cell-output-display .widget-subarea {
  margin-bottom: 1em;
}

// fix for selectize inputs getting their contents clipped
// this also works for knitr sql cells (see github issue #3497)
.cell-output-display:not(.no-overflow-x),
.knitsql-table:not(.no-overflow-x) {
  overflow-x: auto;
}

.panel-input {
  margin-bottom: 1em;
}

.panel-input > div,
.panel-input > div > div {
  display: inline-block;
  vertical-align: top;
  padding-right: 12px;
}

.panel-input > p:last-child {
  margin-bottom: 0;
}

.layout-sidebar {
  margin-bottom: 1em;
}

.layout-sidebar .tab-content {
  border: none;
}

.tab-content > .page-columns.active {
  display: grid;
}

// default styling for .code-preview=".." iframes
div.sourceCode > iframe {
  width: 100%;
  height: 300px;

  // this negative-margin hack works around the rendering issue with
  // iframes and parent elements with rounded corners if the border
  // radius for (eg) div.sourceCode is changed, this is likely going
  // to need changing as well.
  @if variable-exists(code-preview-margin-bottom) {
    margin-bottom: $code-preview-margin-bottom;
  } @else {
    margin-bottom: -0.5em;
  }
  @if variable-exists(code-preview-border-color) {
    border: $code-preview-border-color;
  }
}

// link styling
a {
  text-underline-offset: 3px;
}

// ansi escaping
div.ansi-escaped-output {
  font-family: monospace;
  display: block;
}

/*!
*
* ansi colors from IPython notebook's
*
* we also add `bright-[color]-` synonyms for the `-[color]-intense` classes since
* that seems to be what ansi_up emits
*
*/
/* CSS font colors for translated ANSI escape sequences */
/* The color values are a mix of
   http://www.xcolors.net/dl/baskerville-ivorylight and
   http://www.xcolors.net/dl/euphrasia */
.ansi-black-fg {
  color: #3e424d;
}
.ansi-black-bg {
  background-color: #3e424d;
}
.ansi-black-intense-black,
.ansi-bright-black-fg {
  color: #282c36;
}
.ansi-black-intense-black,
.ansi-bright-black-bg {
  background-color: #282c36;
}
.ansi-red-fg {
  color: #e75c58;
}
.ansi-red-bg {
  background-color: #e75c58;
}
.ansi-red-intense-red,
.ansi-bright-red-fg {
  color: #b22b31;
}
.ansi-red-intense-red,
.ansi-bright-red-bg {
  background-color: #b22b31;
}
.ansi-green-fg {
  color: #00a250;
}
.ansi-green-bg {
  background-color: #00a250;
}
.ansi-green-intense-green,
.ansi-bright-green-fg {
  color: #007427;
}
.ansi-green-intense-green,
.ansi-bright-green-bg {
  background-color: #007427;
}
.ansi-yellow-fg {
  color: #ddb62b;
}
.ansi-yellow-bg {
  background-color: #ddb62b;
}
.ansi-yellow-intense-yellow,
.ansi-bright-yellow-fg {
  color: #b27d12;
}
.ansi-yellow-intense-yellow,
.ansi-bright-yellow-bg {
  background-color: #b27d12;
}
.ansi-blue-fg {
  color: #208ffb;
}
.ansi-blue-bg {
  background-color: #208ffb;
}
.ansi-blue-intense-blue,
.ansi-bright-blue-fg {
  color: #0065ca;
}
.ansi-blue-intense-blue,
.ansi-bright-blue-bg {
  background-color: #0065ca;
}
.ansi-magenta-fg {
  color: #d160c4;
}
.ansi-magenta-bg {
  background-color: #d160c4;
}
.ansi-magenta-intense-magenta,
.ansi-bright-magenta-fg {
  color: #a03196;
}
.ansi-magenta-intense-magenta,
.ansi-bright-magenta-bg {
  background-color: #a03196;
}
.ansi-cyan-fg {
  color: #60c6c8;
}
.ansi-cyan-bg {
  background-color: #60c6c8;
}
.ansi-cyan-intense-cyan,
.ansi-bright-cyan-fg {
  color: #258f8f;
}
.ansi-cyan-intense-cyan,
.ansi-bright-cyan-bg {
  background-color: #258f8f;
}
.ansi-white-fg {
  color: #c5c1b4;
}
.ansi-white-bg {
  background-color: #c5c1b4;
}
.ansi-white-intense-white,
.ansi-bright-white-fg {
  color: #a1a6b2;
}
.ansi-white-intense-white,
.ansi-bright-white-bg {
  background-color: #a1a6b2;
}
.ansi-default-inverse-fg {
  color: #ffffff;
}
.ansi-default-inverse-bg {
  background-color: #000000;
}
.ansi-bold {
  font-weight: bold;
}
.ansi-underline {
  text-decoration: underline;
}

:root {
  --quarto-body-bg: #{$body-bg};
  --quarto-body-color: #{$body-color};
  --quarto-text-muted: #{$text-muted};
  --quarto-border-color: #{$table-border-color};
  --quarto-border-width: #{$border-width};
  --quarto-border-radius: #{$border-radius};
}

/* rules to support GT table styling */
table.gt_table {
  color: var(--quarto-body-color);
  font-size: 1em;
  width: 100%; // to match other table styling
  background-color: transparent;
  border-top-width: inherit;
  border-bottom-width: inherit;
  border-color: var(--quarto-border-color);
}

table.gt_table th.gt_column_spanner_outer {
  color: var(--quarto-body-color);
  background-color: transparent;
  border-top-width: inherit;
  border-bottom-width: inherit;
  border-color: var(--quarto-border-color);
}

table.gt_table th.gt_col_heading {
  color: var(--quarto-body-color);
  font-weight: bold;
  background-color: transparent;
}

table.gt_table thead.gt_col_headings {
  border-bottom: 1px solid currentColor;
  border-top-width: inherit;
  border-top-color: var(--quarto-border-color);
}

table.gt_table thead.gt_col_headings:not(:first-child) {
  border-top-width: 1px;
  border-top-color: var(--quarto-border-color);
}

table.gt_table td.gt_row {
  border-bottom-width: 1px;
  border-bottom-color: var(--quarto-border-color);
  border-top-width: 0px;
}

table.gt_table tbody.gt_table_body {
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-bottom-color: var(--quarto-border-color);
  border-top-color: currentColor;
}

/* restore previous pandoc columns behavior 
   (too many reports of slide layout breaking)
   see https://github.com/jgm/pandoc/pull/8237
*/
div.columns {
  display: initial;
  gap: initial;
}
div.column {
  display: inline-block;
  overflow-x: initial;
  vertical-align: top;
  width: 50%;
}

// Code Annotation LayoutBoot
.code-annotation-tip-content {
  word-wrap: break-word;
}

.code-annotation-container-hidden {
  display: none !important;
}

dl.code-annotation-container-grid {
  display: grid;
  grid-template-columns: min-content auto;
  dt {
    grid-column: 1;
  }
  dd {
    grid-column: 2;
  }
}

pre.sourceCode.code-annotation-code {
  padding-right: 0;
}

code.sourceCode .code-annotation-anchor {
  z-index: 100;
  position: relative;
  float: right;
  background-color: transparent;
}

// Add a bit of margin to the right of a checkbox
// https://github.com/quarto-dev/quarto-cli/issues/6627
input[type="checkbox"] {
  margin-right: 0.5ch;
}

// Mermaid Theming
// if none come from theme, we need these
$body-color: #222 !default;
$body-bg: #fff !default;
$primary: #468 !default;
$secondary: #999 !default;
$font-family-sans-serif: sans-serif !default;

/* SCSS variables

   These are documented in quarto-cli/quarto-web:docs/authoring/_mermaid-theming.qmd

   Make sure to update the docs if you change these.
*/
$mermaid-bg-color: $body-bg !default;
$mermaid-edge-color: $secondary !default;
$mermaid-node-fg-color: $body-color !default;
$mermaid-fg-color: $body-color !default;
$mermaid-fg-color--lighter: lighten($body-color, 10%) !default;
$mermaid-fg-color--lightest: lighten($body-color, 20%) !default;
$mermaid-font-family: $font-family-sans-serif !default;
$mermaid-label-bg-color: $body-bg !default;
$mermaid-label-fg-color: $primary !default;
$mermaid-node-bg-color: rgba($primary, 0.1) !default;
$mermaid-node-fg-color: $primary !default;

/* CSS variables */
:root {
  --mermaid-bg-color: #{$mermaid-bg-color};
  --mermaid-edge-color: #{$mermaid-edge-color};
  --mermaid-node-fg-color: #{$mermaid-node-fg-color};
  --mermaid-fg-color: #{$mermaid-fg-color};
  --mermaid-fg-color--lighter: #{$mermaid-fg-color--lighter};
  --mermaid-fg-color--lightest: #{$mermaid-fg-color--lightest};
  --mermaid-font-family: #{$mermaid-font-family};
  --mermaid-label-bg-color: #{$mermaid-label-bg-color};
  --mermaid-label-fg-color: #{$mermaid-label-fg-color};
  --mermaid-node-bg-color: #{$mermaid-node-bg-color};
  --mermaid-node-fg-color: #{$mermaid-node-fg-color};
}

@media print {
  :root {
    font-size: 11pt;
  }
  #quarto-sidebar,
  #TOC,
  .nav-page {
    display: none;
  }
  .page-columns .content {
    grid-column-start: page-start;
  }
  .fixed-top {
    position: relative;
  }
  .panel-caption,
  .figure-caption,
  figcaption {
    color: #666;
  }
}
