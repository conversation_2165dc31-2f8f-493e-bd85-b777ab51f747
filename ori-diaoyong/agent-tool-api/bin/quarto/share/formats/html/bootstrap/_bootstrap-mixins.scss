@mixin body-secondary {
  @if variable-exists(body-secondary) {
    color: $body-secondary;
  } @else {
    color: theme-dim($body-color, 25%);
  }
}

@mixin page-columns {
  display: grid;
  gap: 0;
}

@mixin column-spanning-element {
  table {
    background: $body-bg;
  }
}

// GRID CASCADE
$grid-body-column-max: $grid-body-width !default;
$grid-body-column-min: quarto-math.min(500px, $grid-body-column-max) !default;

// Margin variables
$grid-page-gutter: $grid-column-gutter-width !default;
$grid-page-gutter-start: $grid-page-gutter !default;
$grid-page-gutter-end: $grid-page-gutter !default;

$grid-body-gutter: $grid-column-gutter-width !default;
$grid-body-gutter-start: $grid-body-gutter !default;
$grid-body-gutter-end: $grid-body-gutter !default;

/* FLOATING GRID */
$grid-page-gutter-float: 5fr !default;
$grid-float-sidebar-width: $grid-sidebar-width !default;
$grid-float-margin-width: $grid-margin-width !default;

/* Float Wide Default Grid */

// Margins
$grid-float-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-wide-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-float-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-wide-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-wide-margin-gutter: $grid-page-gutter-float !default;

// Sidebars
$grid-float-wide-sidebar-width: $grid-float-sidebar-width !default;
$grid-float-wide-sidebar-seg1: minmax(
  #{0.1 * $grid-float-wide-sidebar-width},
  #{0.2 * $grid-float-wide-sidebar-width}
) !default;
$grid-float-wide-sidebar-seg2: minmax(
  #{0.2 * $grid-float-wide-sidebar-width},
  #{0.6 * $grid-float-wide-sidebar-width}
) !default;
$grid-float-wide-sidebar-seg3: minmax(
  #{0.1 * $grid-float-wide-sidebar-width},
  #{0.2 * $grid-float-wide-sidebar-width}
) !default;

// Margins
$grid-float-wide-margin-width: $grid-float-margin-width !default;
$grid-float-wide-margin-seg3: minmax(
  #{0.1 * $grid-float-wide-margin-width},
  #{0.2 * $grid-float-wide-margin-width}
) !default;
$grid-float-wide-margin-seg2: minmax(
  #{0.2 * $grid-float-wide-margin-width},
  #{0.6 * $grid-float-wide-margin-width}
) !default;
$grid-float-wide-margin-seg1: minmax(
  #{0.1 * $grid-float-wide-margin-width},
  #{0.2 * $grid-float-wide-margin-width}
) !default;

// Body
$grid-float-wide-body-column-min: $grid-body-column-min !default;
$grid-float-wide-body-column-max: $grid-body-column-max !default;
$grid-float-wide-body: minmax(
  $grid-float-wide-body-column-min,
  calc(
    #{$grid-float-wide-body-column-max} - #{$grid-float-wide-page-gutter-start +
      $grid-float-wide-page-gutter-end}
  )
) !default;

/* Float Wide Slim Content Grid */

// Margins
$grid-float-wide-slim-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-wide-slim-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-wide-slim-body-gutter-start: $grid-body-gutter-start !default;
$grid-float-wide-slim-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-wide-slim-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-wide-slim-margin-gutter: $grid-page-gutter-float !default;

// Sidebars
$grid-float-wide-slim-sidebar-width: $grid-float-sidebar-width !default;
$grid-float-wide-slim-sidebar-seg1: 0.2 * $grid-float-wide-slim-sidebar-width !default;
$grid-float-wide-slim-sidebar-seg2: minmax(
  #{0.2 * $grid-float-wide-slim-sidebar-width},
  #{0.6 * $grid-float-wide-slim-sidebar-width}
) !default;
$grid-float-wide-slim-sidebar-seg3: 0.2 * $grid-float-wide-slim-sidebar-width !default;

// Margins
$grid-float-wide-slim-margin-width: $grid-float-margin-width !default;
$grid-float-wide-slim-margin-seg3: 0.2 * $grid-float-wide-slim-margin-width !default;
$grid-float-wide-slim-margin-seg2: minmax(
  #{0.2 * $grid-float-wide-slim-margin-width},
  #{0.6 * $grid-float-wide-slim-margin-width}
) !default;
$grid-float-wide-slim-margin-seg1: 0.2 * $grid-float-wide-slim-margin-width !default;

// Body
$grid-float-wide-slim-body-column-min: $grid-body-column-min - 50px !default;
$grid-float-wide-slim-body-column-max: $grid-body-column-max - 50px !default;
$grid-float-wide-slim-body: minmax(
  $grid-float-wide-slim-body-column-min,
  calc(
    #{$grid-float-wide-slim-body-column-max} - #{$grid-float-wide-slim-page-gutter-start +
      $grid-float-wide-slim-page-gutter-end}
  )
) !default;

/* Float Wide Full Grid */
// Margins
$grid-float-wide-full-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-wide-full-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-wide-full-body-gutter-start: $grid-body-gutter-start !default;
$grid-float-wide-full-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-wide-full-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-wide-full-margin-gutter: $grid-page-gutter-float !default;

// Sidebars
$grid-float-wide-full-sidebar-width: $grid-float-sidebar-width !default;
$grid-float-wide-full-sidebar-seg1: 0.2 * $grid-float-wide-full-sidebar-width !default;
$grid-float-wide-full-sidebar-seg2: minmax(
  #{0.2 * $grid-float-wide-full-sidebar-width},
  #{0.6 * $grid-float-wide-full-sidebar-width}
) !default;
$grid-float-wide-full-sidebar-seg3: 0.2 * $grid-float-wide-full-sidebar-width !default;

// Margins
$grid-float-wide-full-margin-width: $grid-float-margin-width !default;
$grid-float-wide-full-margin-seg3: 0.2 * $grid-float-wide-full-margin-width !default;
$grid-float-wide-full-margin-seg2: minmax(
  #{0.2 * $grid-float-wide-full-margin-width},
  #{0.6 * $grid-float-wide-full-margin-width}
) !default;
$grid-float-wide-full-margin-seg1: 0.2 * $grid-float-wide-full-margin-width !default;

// Body
$grid-float-wide-full-body-column-min: $grid-body-column-min !default;
$grid-float-wide-full-body-column-max: $grid-body-column-max !default;
$grid-float-wide-full-body: minmax(
  $grid-float-wide-full-body-column-min,
  calc(
    #{$grid-float-wide-full-body-column-max} - #{$grid-float-wide-full-page-gutter-start +
      $grid-float-wide-full-page-gutter-end}
  )
) !default;

/* Float Wide Listing Grid */
// Margins
$grid-float-wide-listing-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-wide-listing-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-wide-listing-body-gutter-start: $grid-body-gutter-start !default;
$grid-float-wide-listing-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-wide-listing-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-wide-listing-margin-gutter: $grid-page-gutter-float !default;

// Sidebars
$grid-float-wide-listing-sidebar-width: $grid-float-sidebar-width !default;
$grid-float-wide-listing-sidebar-seg1: minmax(
  #{0.1 * $grid-float-wide-listing-sidebar-width},
  #{0.2 * $grid-float-wide-listing-sidebar-width}
) !default;
$grid-float-wide-listing-sidebar-seg2: minmax(
  #{0.2 * $grid-float-wide-listing-sidebar-width},
  #{0.6 * $grid-float-wide-listing-sidebar-width}
) !default;
$grid-float-wide-listing-sidebar-seg3: minmax(
  #{0.1 * $grid-float-wide-listing-sidebar-width},
  #{0.2 * $grid-float-wide-listing-sidebar-width}
) !default;

// Margins
$grid-float-wide-listing-margin-width: $grid-float-margin-width !default;
$grid-float-wide-listing-margin-seg3: minmax(
  #{0.1 * $grid-float-wide-listing-margin-width},
  #{0.2 * $grid-float-wide-listing-margin-width}
) !default;
$grid-float-wide-listing-margin-seg2: minmax(
  #{0.2 * $grid-float-wide-listing-margin-width},
  #{0.6 * $grid-float-wide-listing-margin-width}
) !default;
$grid-float-wide-listing-margin-seg1: minmax(
  #{0.1 * $grid-float-wide-listing-margin-width},
  #{0.2 * $grid-float-wide-listing-margin-width}
) !default;

// Body
$grid-float-wide-listing-body-column-min: $grid-body-column-min !default;
$grid-float-wide-listing-body-column-max: $grid-body-column-max !default;
$grid-float-wide-listing-body: minmax(
  $grid-float-wide-listing-body-column-min,
  calc(
    #{$grid-float-wide-listing-body-column-max} - #{$grid-float-wide-listing-page-gutter-start +
      $grid-float-wide-listing-page-gutter-end}
  )
) !default;

/* Float Mid Default Grid */
// Margins
$grid-float-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-mid-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-float-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-mid-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-mid-margin-gutter: $grid-page-gutter-float !default;

// No sidebar, only margins
$grid-float-mid-margin-width: $grid-float-margin-width !default;
$grid-float-mid-margin-seg3: 0.2 * $grid-float-mid-margin-width !default;
$grid-float-mid-margin-seg2: minmax(
  #{0.3 * $grid-float-mid-margin-width},
  #{0.6 * $grid-float-mid-margin-width}
) !default;
$grid-float-mid-margin-seg1: 0.1 * $grid-float-mid-margin-width !default;

// Body
$grid-float-mid-body-column-min: $grid-body-column-min !default;
$grid-float-mid-body-column-max: $grid-body-column-max - 50px !default;
$grid-float-mid-body: minmax(
  $grid-float-mid-body-column-min,
  calc(
    #{$grid-float-mid-body-column-max} - #{$grid-float-mid-page-gutter-start + $grid-float-mid-page-gutter-end}
  )
) !default;

/* Float Mid Slim Content Grid */
// Margins
$grid-float-mid-slim-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-mid-slim-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-mid-slim-body-gutter-start: 2 *
  quarto-math.div($grid-body-gutter-start, 3) !default;
$grid-float-mid-slim-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-mid-slim-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-mid-slim-margin-gutter: 0.8 * $grid-page-gutter-float !default;

// No sidebar, only margins
$grid-float-mid-slim-margin-width: $grid-float-margin-width !default;
$grid-float-mid-slim-margin-seg3: 0.14 * $grid-float-mid-slim-margin-width !default;
$grid-float-mid-slim-margin-seg2: minmax(
  #{0.3 * $grid-float-mid-slim-margin-width},
  #{0.58 * $grid-float-mid-slim-margin-width}
) !default;
$grid-float-mid-slim-margin-seg1: 0.14 * $grid-float-mid-slim-margin-width !default;

// Body
$grid-float-mid-slim-body-column-min: $grid-body-column-min !default;
$grid-float-mid-slim-body-column-max: $grid-body-column-max - 50px !default;
$grid-float-mid-slim-body: minmax(
  $grid-float-mid-slim-body-column-min,
  calc(
    #{$grid-float-mid-slim-body-column-max} - #{$grid-float-mid-slim-page-gutter-start +
      $grid-float-mid-slim-page-gutter-end}
  )
) !default;

/* Float Mid Full Content Grid */
// Margins
$grid-float-mid-full-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-mid-full-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-mid-full-body-gutter-start: 2 *
  quarto-math.div($grid-body-gutter-start, 3) !default;
$grid-float-mid-full-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-mid-full-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-mid-full-margin-gutter: 0.8 * $grid-page-gutter-float !default;

// No sidebar or margins

// Body
$grid-float-mid-full-body-column-min: $grid-body-column-min !default;
$grid-float-mid-full-body-column-max: $grid-body-column-max !default;
$grid-float-mid-full-body: minmax(
  $grid-float-mid-full-body-column-min,
  calc(
    #{$grid-float-mid-full-body-column-max} - #{$grid-float-mid-full-page-gutter-start +
      $grid-float-mid-full-page-gutter-end}
  )
) !default;

/* Float Mid Listing Content Grid */
// Margins
$grid-float-mid-listing-page-gutter-start: $grid-page-gutter-start !default;
$grid-float-mid-listing-page-gutter-end: $grid-page-gutter-end !default;
$grid-float-mid-listing-body-gutter-start: 2 *
  quarto-math.div($grid-body-gutter-start, 3) !default;
$grid-float-mid-listing-body-gutter-end: $grid-body-gutter-end !default;
$grid-float-mid-listing-sidebar-gutter: $grid-page-gutter-float !default;
$grid-float-mid-listing-margin-gutter: 0.8 * $grid-page-gutter-float !default;

// No sidebar, only margins
$grid-float-mid-listing-margin-width: $grid-float-margin-width !default;
$grid-float-mid-listing-margin-seg3: 0.2 * $grid-float-mid-listing-margin-width !default;
$grid-float-mid-listing-margin-seg2: minmax(
  #{0.3 * $grid-float-mid-listing-margin-width},
  #{0.6 * $grid-float-mid-listing-margin-width}
) !default;
$grid-float-mid-listing-margin-seg1: 0.1 * $grid-float-mid-listing-margin-width !default;

// Body
$grid-float-mid-listing-body-column-min: $grid-body-column-min !default;
$grid-float-mid-listing-body-column-max: $grid-body-column-max - 50px !default;
$grid-float-mid-listing-body: minmax(
  $grid-float-mid-listing-body-column-min,
  calc(
    #{$grid-float-mid-listing-body-column-max} - #{$grid-float-mid-listing-page-gutter-start +
      $grid-float-mid-listing-page-gutter-end}
  )
) !default;

/* DOCKED GRID */
$grid-docked-sidebar-width: $grid-sidebar-width !default;
$grid-docked-margin-width: $grid-margin-width !default;
$grid-docked-body-width: $grid-body-column-max + 200px !default;

/* Docked Wide Default Grid */

// Margins
$grid-docked-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-wide-page-gutter-end: $grid-page-gutter-end !default;
$grid-docked-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-wide-margin-gutter: 5fr !default;

// Sidebars
$grid-docked-wide-sidebar-width: $grid-docked-sidebar-width !default;
$grid-docked-wide-sidebar-seg1: minmax(
  #{0.2 * $grid-docked-wide-sidebar-width},
  #{0.4 * $grid-docked-wide-sidebar-width}
) !default;
$grid-docked-wide-sidebar-seg2: 0.2 * $grid-docked-wide-sidebar-width !default;
$grid-docked-wide-sidebar-seg3: 0.2 * $grid-docked-wide-sidebar-width !default;

// Margins
$grid-docked-wide-margin-width: $grid-docked-margin-width !default;
$grid-docked-wide-margin-seg3: 0.2 * $grid-docked-wide-margin-width !default;
$grid-docked-wide-margin-seg2: minmax(
  #{0.2 * $grid-docked-wide-margin-width},
  #{0.4 * $grid-docked-wide-margin-width}
) !default;
$grid-docked-wide-margin-seg1: 0.2 * $grid-docked-wide-margin-width !default;

// Body
$grid-docked-wide-body-column-min: $grid-body-column-min !default;
$grid-docked-wide-body-column-max: $grid-docked-body-width !default;
$grid-docked-wide-body: minmax(
  $grid-docked-wide-body-column-min,
  calc(
    #{$grid-docked-wide-body-column-max} - #{$grid-docked-wide-page-gutter-start +
      $grid-docked-wide-page-gutter-end}
  )
) !default;

/* Docked Slim Content Grid */

// Margins
$grid-docked-wide-slim-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-wide-slim-page-gutter-end: $grid-page-gutter-end !default;
$grid-docked-wide-slim-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-wide-slim-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-wide-slim-margin-gutter: 5fr !default;

// Sidebars
$grid-docked-wide-slim-sidebar-width: $grid-docked-sidebar-width !default;
$grid-docked-wide-slim-sidebar-seg1: minmax(
  #{0.2 * $grid-docked-wide-slim-sidebar-width},
  #{0.4 * $grid-docked-wide-slim-sidebar-width}
) !default;
$grid-docked-wide-slim-sidebar-seg2: 0.2 * $grid-docked-wide-slim-sidebar-width !default;
$grid-docked-wide-slim-sidebar-seg3: 0.2 * $grid-docked-wide-slim-sidebar-width !default;

// Margins
$grid-docked-wide-slim-margin-width: $grid-docked-margin-width !default;
$grid-docked-wide-slim-margin-seg3: 0.2 * $grid-docked-wide-slim-margin-width !default;
$grid-docked-wide-slim-margin-seg2: minmax(
  #{0 * $grid-docked-wide-slim-margin-width},
  #{0.8 * $grid-docked-wide-slim-margin-width}
) !default;
$grid-docked-wide-slim-margin-seg1: 0.2 * $grid-docked-wide-slim-margin-width !default;

// Body
$grid-docked-wide-slim-body-column-min: $grid-body-column-min - 50px !default;
$grid-docked-wide-slim-body-column-max: $grid-body-column-max - 50px !default;
$grid-docked-wide-slim-body: minmax(
  $grid-docked-wide-slim-body-column-min,
  calc(
    #{$grid-docked-wide-slim-body-column-max} - #{$grid-docked-wide-slim-page-gutter-start +
      $grid-docked-wide-slim-page-gutter-end}
  )
) !default;

/* Docked Full Content Grid */

// Margins
$grid-docked-wide-full-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-wide-full-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-wide-full-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-wide-full-margin-gutter: 5fr !default;
$grid-docked-wide-full-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-docked-wide-full-sidebar-width: $grid-docked-sidebar-width !default;
$grid-docked-wide-full-sidebar-seg1: minmax(
  #{0.2 * $grid-docked-wide-full-sidebar-width},
  #{0.4 * $grid-docked-wide-full-sidebar-width}
) !default;
$grid-docked-wide-full-sidebar-seg2: 0.2 * $grid-docked-wide-full-sidebar-width !default;
$grid-docked-wide-full-sidebar-seg3: 0.2 * $grid-docked-wide-full-sidebar-width !default;

// No Margins

// Body
$grid-docked-wide-full-body-column-min: $grid-body-column-min !default;
$grid-docked-wide-full-body-column-max: $grid-docked-body-width !default;
$grid-docked-wide-full-body: minmax(
  $grid-docked-wide-full-body-column-min,
  calc(
    #{$grid-docked-wide-full-body-column-max} - #{$grid-docked-wide-full-page-gutter-start +
      $grid-docked-wide-full-page-gutter-end}
  )
) !default;

/* Docked Listing Grid */

// Margins
$grid-docked-wide-listing-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-wide-listing-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-wide-listing-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-wide-listing-margin-gutter: 5fr !default;
$grid-docked-wide-listing-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-docked-wide-listing-sidebar-width: $grid-docked-sidebar-width !default;
$grid-docked-wide-listing-sidebar-seg1: minmax(
  #{0.2 * $grid-docked-wide-listing-sidebar-width},
  #{0.4 * $grid-docked-wide-listing-sidebar-width}
) !default;
$grid-docked-wide-listing-sidebar-seg2: 0.2 *
  $grid-docked-wide-listing-sidebar-width !default;
$grid-docked-wide-listing-sidebar-seg3: 0.2 *
  $grid-docked-wide-listing-sidebar-width !default;

// Margins
$grid-docked-wide-listing-margin-width: $grid-docked-margin-width !default;
$grid-docked-wide-listing-margin-seg3: 0.2 *
  $grid-docked-wide-listing-margin-width !default;
$grid-docked-wide-listing-margin-seg2: minmax(
  #{0 * $grid-docked-wide-listing-margin-width},
  #{0.8 * $grid-docked-wide-listing-margin-width}
) !default;
$grid-docked-wide-listing-margin-seg1: 0.2 *
  $grid-docked-wide-listing-margin-width !default;

// Body
$grid-docked-wide-listing-body-column-min: $grid-body-column-min !default;
$grid-docked-wide-listing-body-column-max: $grid-docked-body-width !default;
$grid-docked-wide-listing-body: minmax(
  $grid-docked-wide-listing-body-column-min,
  calc(
    #{$grid-docked-wide-listing-body-column-max} - #{$grid-docked-wide-listing-page-gutter-start +
      $grid-docked-wide-listing-page-gutter-end}
  )
) !default;

/* Docked Mid Default Grid */
// Margins
$grid-docked-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-mid-margin-gutter: 5fr !default;
$grid-docked-mid-page-gutter-end: $grid-page-gutter-end !default;

// No sidebar, only margins
$grid-docked-mid-margin-width: $grid-docked-margin-width !default;
$grid-docked-mid-margin-seg3: 0.2 * $grid-docked-mid-margin-width !default;
$grid-docked-mid-margin-seg2: minmax(
  #{0.1 * $grid-docked-mid-margin-width},
  #{0.2 * $grid-docked-mid-margin-width}
) !default;
$grid-docked-mid-margin-seg1: 0.2 * $grid-docked-mid-margin-width !default;

// Body
$grid-docked-mid-body-column-min: $grid-body-column-min !default;
$grid-docked-mid-body-column-max: $grid-body-column-max - 50px !default;
$grid-docked-mid-body: minmax(
  $grid-docked-mid-body-column-min,
  calc(
    #{$grid-docked-mid-body-column-max} - #{$grid-docked-mid-page-gutter-start +
      $grid-docked-mid-page-gutter-end}
  )
) !default;

/* Docked Mid Slim Content Grid */
// Margins
$grid-docked-mid-slim-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-mid-slim-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-mid-slim-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-mid-slim-margin-gutter: 5fr !default;
$grid-docked-mid-slim-page-gutter-end: $grid-page-gutter-end !default;

// No sidebar, only margins
$grid-docked-mid-slim-margin-width: $grid-docked-margin-width !default;
$grid-docked-mid-slim-margin-seg3: 0.2 * $grid-docked-mid-slim-margin-width !default;
$grid-docked-mid-slim-margin-seg2: minmax(
  #{0.1 * $grid-docked-mid-slim-margin-width},
  #{0.2 * $grid-docked-mid-slim-margin-width}
) !default;
$grid-docked-mid-slim-margin-seg1: 0.2 * $grid-docked-mid-slim-margin-width !default;

// Body
$grid-docked-mid-slim-body-column-min: $grid-body-column-min !default;
$grid-docked-mid-slim-body-column-max: $grid-body-column-max - 50px !default;
$grid-docked-mid-slim-body: minmax(
  $grid-docked-mid-slim-body-column-min,
  calc(
    #{$grid-docked-mid-slim-body-column-max} - #{$grid-docked-mid-slim-page-gutter-start +
      $grid-docked-mid-slim-page-gutter-end}
  )
) !default;

/* Docked Mid Full Content Grid */
// Margins
$grid-docked-mid-full-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-mid-full-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-mid-full-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-mid-full-margin-gutter: 5fr !default;
$grid-docked-mid-full-page-gutter-end: $grid-page-gutter-end !default;

// No sidebar or margins

// Body
$grid-docked-mid-full-body-column-min: $grid-body-column-min !default;
$grid-docked-mid-full-body-column-max: $grid-docked-body-width !default;
$grid-docked-mid-full-body: minmax(
  $grid-docked-mid-full-body-column-min,
  calc(
    #{$grid-docked-mid-full-body-column-max} - #{$grid-docked-mid-full-page-gutter-start +
      $grid-docked-mid-full-page-gutter-end}
  )
) !default;

/* Float Mid Listing Content Grid */
// Margins
$grid-docked-mid-listing-page-gutter-start: $grid-page-gutter-start !default;
$grid-docked-mid-listing-body-gutter-start: $grid-body-gutter-start !default;
$grid-docked-mid-listing-body-gutter-end: $grid-body-gutter-end !default;
$grid-docked-mid-listing-margin-gutter: $grid-page-gutter-float !default;
$grid-docked-mid-listing-page-gutter-end: $grid-page-gutter-end !default;

// No sidebar, only margins
$grid-docked-mid-listing-margin-width: $grid-docked-margin-width !default;
$grid-docked-mid-listing-margin-seg3: 0.2 *
  $grid-docked-mid-listing-margin-width !default;
$grid-docked-mid-listing-margin-seg2: minmax(
  #{0.3 * $grid-docked-mid-listing-margin-width},
  #{0.6 * $grid-docked-mid-listing-margin-width}
) !default;
$grid-docked-mid-listing-margin-seg1: 0.1 *
  $grid-docked-mid-listing-margin-width !default;

// Body
$grid-docked-mid-listing-body-column-min: $grid-body-column-min !default;
$grid-docked-mid-listing-body-column-max: $grid-body-column-max - 50px !default;
$grid-docked-mid-listing-body: minmax(
  $grid-docked-mid-listing-body-column-min,
  calc(
    #{$grid-docked-mid-listing-body-column-max} - #{$grid-docked-mid-listing-page-gutter-start +
      $grid-docked-mid-listing-page-gutter-end}
  )
) !default;

/* DEFAULT (HTML PAGE, NOT IN WEBSITE) GRID */
$grid-default-sidebar-width: $grid-sidebar-width !default;
$grid-default-margin-width: $grid-margin-width !default;
$grid-default-body-width: $grid-body-column-max + 50px !default;

/* Default Wide Grid */
// Margins
$grid-default-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-wide-sidebar-gutter: 5fr !default;
$grid-default-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-wide-margin-gutter: 5fr !default;
$grid-default-wide-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-wide-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-wide-sidebar-seg1: 0.14 * $grid-default-wide-sidebar-width !default;
$grid-default-wide-sidebar-seg2: 0.14 * $grid-default-wide-sidebar-width !default;

// Margins
$grid-default-wide-margin-width: $grid-default-margin-width !default;
$grid-default-wide-margin-seg3: 0.14 * $grid-default-wide-margin-width !default;
$grid-default-wide-margin-seg2: minmax(
  #{0.3 * $grid-default-wide-margin-width},
  #{0.58 * $grid-default-wide-margin-width}
) !default;
$grid-default-wide-margin-seg1: 0.14 * $grid-default-wide-margin-width !default;

// Body
$grid-default-wide-body-column-min: $grid-body-column-min !default;
$grid-default-wide-body-column-max: $grid-default-body-width !default;
$grid-default-wide-body: minmax(
  $grid-default-wide-body-column-min,
  calc(
    #{$grid-default-wide-body-column-max} - #{$grid-default-wide-page-gutter-start +
      $grid-default-wide-page-gutter-end}
  )
) !default;

/* Default Mid Grid */
// Margins
$grid-default-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-mid-sidebar-gutter: 5fr !default;
$grid-default-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-mid-margin-gutter: 5fr !default;
$grid-default-mid-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-mid-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-mid-sidebar-seg1: 0.14 * $grid-default-mid-sidebar-width !default;
$grid-default-mid-sidebar-seg2: 0.14 * $grid-default-mid-sidebar-width !default;

// Margins
$grid-default-mid-margin-width: $grid-default-margin-width !default;
$grid-default-mid-margin-seg3: 0.14 * $grid-default-mid-margin-width !default;
$grid-default-mid-margin-seg2: minmax(
  #{0.3 * $grid-default-mid-margin-width},
  #{0.58 * $grid-default-mid-margin-width}
) !default;
$grid-default-mid-margin-seg1: 0.14 * $grid-default-mid-margin-width !default;

// Body
$grid-default-mid-body-column-min: $grid-body-column-min !default;
$grid-default-mid-body-column-max: $grid-default-body-width - 50px !default;
$grid-default-mid-body: minmax(
  $grid-default-mid-body-column-min,
  calc(
    #{$grid-default-mid-body-column-max} - #{$grid-default-mid-page-gutter-start +
      $grid-default-mid-page-gutter-end}
  )
) !default;

/* Full Content Wide Grid */
// Margins
$grid-default-full-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-full-wide-sidebar-gutter: 5fr !default;
$grid-default-full-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-full-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-full-wide-margin-gutter: 5fr !default;
$grid-default-full-wide-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-full-wide-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-full-wide-sidebar-seg1: 0.14 *
  $grid-default-full-wide-sidebar-width !default;
$grid-default-full-wide-sidebar-seg2: 0.14 *
  $grid-default-full-wide-sidebar-width !default;

// Margins
$grid-default-full-wide-margin-width: $grid-default-margin-width !default;
$grid-default-full-wide-margin-seg2: 0.14 * $grid-default-full-wide-margin-width !default;
$grid-default-full-wide-margin-seg1: 0.14 * $grid-default-full-wide-margin-width !default;

// Body
$grid-default-full-wide-body-column-min: $grid-body-column-min !default;
$grid-default-full-wide-body-column-max: $grid-default-body-width !default;
$grid-default-full-wide-body: minmax(
  $grid-default-full-wide-body-column-min,
  calc(
    #{$grid-default-full-wide-body-column-max} - #{$grid-default-full-wide-page-gutter-start +
      $grid-default-full-wide-page-gutter-end}
  )
) !default;

/* Full Content Mid Grid */
// Margins
$grid-default-full-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-full-mid-sidebar-gutter: 5fr !default;
$grid-default-full-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-full-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-full-mid-margin-gutter: 5fr !default;
$grid-default-full-mid-page-gutter-end: $grid-page-gutter-end !default;

// No margins or sidebars

// Body
$grid-default-full-mid-body-column-min: $grid-body-column-min !default;
$grid-default-full-mid-body-column-max: $grid-default-body-width - 50px !default;
$grid-default-full-mid-body: minmax(
  $grid-default-full-mid-body-column-min,
  calc(
    #{$grid-default-full-mid-body-column-max} - #{$grid-default-full-mid-page-gutter-start +
      $grid-default-full-mid-page-gutter-end}
  )
) !default;

/* Listing Wide Grid */
// Margins
$grid-default-listing-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-listing-wide-sidebar-gutter: 5fr !default;
$grid-default-listing-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-listing-wide-body-gutter-end: 2 * $grid-body-gutter-end !default;
$grid-default-listing-wide-margin-gutter: 1fr !default;
$grid-default-listing-wide-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-listing-wide-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-listing-wide-sidebar-seg1: minmax(
  #{0.2 * $grid-default-listing-wide-sidebar-width},
  #{0.4 * $grid-default-listing-wide-sidebar-width}
) !default;
$grid-default-listing-wide-sidebar-seg2: 0.2 *
  $grid-default-listing-wide-sidebar-width !default;
$grid-default-listing-wide-sidebar-seg3: 0.2 *
  $grid-default-listing-wide-sidebar-width !default;

// Margins
$grid-default-listing-wide-margin-width: $grid-default-margin-width !default;
$grid-default-listing-wide-sidebar-seg3: 0.2 *
  $grid-default-listing-wide-margin-width !default;
$grid-default-listing-wide-margin-seg2: minmax(
  #{0 * $grid-default-listing-wide-margin-width},
  #{1 * $grid-default-listing-wide-margin-width}
) !default;
$grid-default-listing-wide-margin-seg1: 0.2 *
  $grid-default-listing-wide-margin-width !default;

// Body
$grid-default-listing-wide-body-column-min: $grid-body-column-min !default;
$grid-default-listing-wide-body-column-max: $grid-default-body-width !default;
$grid-default-listing-wide-body: minmax(
  $grid-default-listing-wide-body-column-min,
  calc(
    #{$grid-default-listing-wide-body-column-max} - #{$grid-default-listing-wide-page-gutter-start +
      $grid-default-listing-wide-page-gutter-end}
  )
) !default;

/* Listing Mid Grid */
// Margins
$grid-default-listing-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-listing-mid-sidebar-gutter: 5fr !default;
$grid-default-listing-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-listing-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-listing-mid-margin-gutter: 5fr !default;
$grid-default-listing-mid-page-gutter-end: $grid-page-gutter-end !default;

// No margins or sidebars

// Body
$grid-default-listing-mid-body-column-min: $grid-body-column-min !default;
$grid-default-listing-mid-body-column-max: $grid-default-body-width + 400px !default;
$grid-default-listing-mid-body: minmax(
  $grid-default-listing-mid-body-column-min,
  calc(
    #{$grid-default-listing-mid-body-column-max} - #{$grid-default-listing-mid-page-gutter-start +
      $grid-default-listing-mid-page-gutter-end}
  )
) !default;

/* Slim Wide Grid */
// Margins
$grid-default-slim-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-slim-wide-sidebar-gutter: 5fr !default;
$grid-default-slim-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-slim-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-slim-wide-margin-gutter: 5fr !default;
$grid-default-slim-wide-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-slim-wide-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-slim-wide-sidebar-seg1: 0.14 *
  $grid-default-slim-wide-sidebar-width !default;
$grid-default-slim-wide-sidebar-seg2: 0.14 *
  $grid-default-slim-wide-sidebar-width !default;

// Margins
$grid-default-slim-wide-margin-width: $grid-default-margin-width !default;
$grid-default-slim-wide-sidebar-seg3: 0.2 * $grid-default-slim-wide-margin-width !default;
$grid-default-slim-wide-margin-seg2: minmax(
  #{0 * $grid-default-slim-wide-margin-width},
  #{0.8 * $grid-default-slim-wide-margin-width}
) !default;
$grid-default-slim-wide-margin-seg1: 0.2 * $grid-default-slim-wide-margin-width !default;

// Body
$grid-default-slim-wide-body-column-min: $grid-body-column-min !default;
$grid-default-slim-wide-body-column-max: $grid-default-body-width !default;
$grid-default-slim-wide-body: minmax(
  $grid-default-slim-wide-body-column-min,
  calc(
    #{$grid-default-slim-wide-body-column-max} - #{$grid-default-slim-wide-page-gutter-start +
      $grid-default-slim-wide-page-gutter-end}
  )
) !default;

/* Slim Mid Grid */
// Margins
$grid-default-slim-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-slim-mid-sidebar-gutter: 5fr !default;
$grid-default-slim-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-slim-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-slim-mid-margin-gutter: 5fr !default;
$grid-default-slim-mid-page-gutter-end: $grid-page-gutter-end !default;

// Margins
$grid-default-slim-mid-margin-width: $grid-default-margin-width !default;
$grid-default-slim-mid-margin-seg3: 0.14 * $grid-default-slim-mid-margin-width !default;
$grid-default-slim-mid-margin-seg2: minmax(
  #{0.3 * $grid-default-slim-mid-margin-width},
  #{0.58 * $grid-default-slim-mid-margin-width}
) !default;
$grid-default-slim-mid-margin-seg1: 0.14 * $grid-default-slim-mid-margin-width !default;

// Body
$grid-default-slim-mid-body-column-min: $grid-body-column-min !default;
$grid-default-slim-mid-body-column-max: $grid-default-body-width - 50px !default;
$grid-default-slim-mid-body: minmax(
  $grid-default-slim-mid-body-column-min,
  calc(
    #{$grid-default-slim-mid-body-column-max} - #{$grid-default-slim-mid-page-gutter-start +
      $grid-default-slim-mid-page-gutter-end}
  )
) !default;

/* TOC Wide Grid */
// Margins
$grid-default-toc-wide-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-toc-wide-sidebar-gutter: 5fr !default;
$grid-default-toc-wide-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-toc-wide-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-toc-wide-margin-gutter: 5fr !default;
$grid-default-toc-wide-page-gutter-end: $grid-page-gutter-end !default;

// Sidebars
$grid-default-toc-wide-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-toc-wide-sidebar-seg1: 0.14 * $grid-default-toc-wide-sidebar-width !default;
$grid-default-toc-wide-sidebar-seg2: minmax(
  #{0 * $grid-default-toc-wide-sidebar-width},
  #{0.7 * $grid-default-toc-wide-sidebar-width}
) !default;
$grid-default-toc-wide-sidebar-seg3: 0.14 * $grid-default-toc-wide-sidebar-width !default;

// Margins
$grid-default-toc-wide-margin-width: $grid-default-margin-width !default;
$grid-default-toc-wide-margin-seg3: 0.2 * $grid-default-toc-wide-margin-width !default;
$grid-default-toc-wide-margin-seg2: minmax(
  #{0 * $grid-default-toc-wide-margin-width},
  #{0.8 * $grid-default-toc-wide-margin-width}
) !default;
$grid-default-toc-wide-margin-seg1: 0.2 * $grid-default-toc-wide-margin-width !default;

// Body
$grid-default-toc-wide-body-column-min: $grid-body-column-min - 50px !default;
$grid-default-toc-wide-body-column-max: $grid-default-body-width - 50px !default;
$grid-default-toc-wide-body: minmax(
  $grid-default-toc-wide-body-column-min,
  calc(
    #{$grid-default-toc-wide-body-column-max} - #{$grid-default-toc-wide-page-gutter-start +
      $grid-default-toc-wide-page-gutter-end}
  )
) !default;

/* TOC Mid Grid */
// Margins
$grid-default-toc-mid-page-gutter-start: $grid-page-gutter-start !default;
$grid-default-toc-mid-sidebar-gutter: 5fr !default;
$grid-default-toc-mid-body-gutter-start: $grid-body-gutter-start !default;
$grid-default-toc-mid-body-gutter-end: $grid-body-gutter-end !default;
$grid-default-toc-mid-margin-gutter: 5fr !default;
$grid-default-toc-mid-page-gutter-end: $grid-page-gutter-end !default;

// Margins
$grid-default-toc-mid-sidebar-width: $grid-default-sidebar-width !default;
$grid-default-toc-mid-sidebar-seg1: 0.14 * $grid-default-toc-mid-sidebar-width !default;
$grid-default-toc-mid-sidebar-seg2: minmax(
  #{0 * $grid-default-toc-mid-sidebar-width},
  #{0.58 * $grid-default-toc-mid-sidebar-width}
) !default;
$grid-default-toc-mid-sidebar-seg3: 0.14 * $grid-default-toc-mid-sidebar-width !default;

// Body
$grid-default-toc-mid-body-column-min: $grid-body-column-min - 50px !default;
$grid-default-toc-mid-body-column-max: $grid-default-body-width - 50px !default;
$grid-default-toc-mid-body: minmax(
  $grid-default-toc-mid-body-column-min,
  calc(
    #{$grid-default-toc-mid-body-column-max} - #{$grid-default-toc-mid-page-gutter-start +
      $grid-default-toc-mid-page-gutter-end}
  )
) !default;

// Floating Grid Definitions
@mixin page-columns-float-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-wide-page-gutter-start
    [screen-start-inset] $grid-float-wide-sidebar-gutter
    [page-start] $grid-float-wide-sidebar-seg1
    [page-start-inset] $grid-float-wide-sidebar-seg2
    [body-start-outset] $grid-float-wide-sidebar-seg3
    [body-start] $grid-float-wide-body-gutter-start
    [body-content-start] $grid-float-wide-body
    [body-content-end] $grid-float-wide-body-gutter-end
    [body-end] $grid-float-wide-margin-seg3
    [body-end-outset] $grid-float-wide-margin-seg2
    [page-end-inset] $grid-float-wide-margin-seg1
    [page-end] $grid-float-wide-margin-gutter
    [screen-end-inset] $grid-float-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-slimcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-wide-slim-page-gutter-start
    [screen-start-inset] $grid-float-wide-slim-sidebar-gutter
    [page-start] $grid-float-wide-slim-sidebar-seg1
    [page-start-inset] $grid-float-wide-slim-sidebar-seg2
    [body-start-outset] $grid-float-wide-slim-sidebar-seg3
    [body-start] $grid-float-wide-slim-body-gutter-start
    [body-content-start] $grid-float-wide-slim-body
    [body-content-end] $grid-float-wide-slim-body-gutter-end
    [body-end] $grid-float-wide-slim-margin-seg3
    [body-end-outset] $grid-float-wide-slim-margin-seg2
    [page-end-inset] $grid-float-wide-slim-margin-seg1
    [page-end] $grid-float-wide-slim-margin-gutter
    [screen-end-inset] $grid-float-wide-slim-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-fullcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-wide-full-page-gutter-start
    [screen-start-inset] $grid-float-wide-full-sidebar-gutter
    [page-start] $grid-float-wide-full-sidebar-seg1
    [page-start-inset] $grid-float-wide-full-sidebar-seg2
    [body-start-outset] $grid-float-wide-full-sidebar-seg3
    [body-start] $grid-float-wide-full-body-gutter-start
    [body-content-start] $grid-float-wide-full-body
    [body-content-end] $grid-float-wide-full-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-float-wide-full-margin-gutter
    [screen-end-inset] $grid-float-wide-full-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-listing-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-wide-listing-page-gutter-start
    [screen-start-inset] $grid-float-wide-listing-sidebar-gutter
    [page-start] $grid-float-wide-listing-sidebar-seg1
    [page-start-inset] $grid-float-wide-listing-sidebar-seg2
    [body-start-outset] $grid-float-wide-listing-sidebar-seg3
    [body-start] $grid-float-wide-listing-body-gutter-start
    [body-content-start] $grid-float-wide-listing-body
    [body-content-end] $grid-float-wide-listing-body-gutter-end
    [body-end] $grid-float-wide-listing-margin-seg3
    [body-end-outset] $grid-float-wide-listing-margin-seg2
    [page-end-inset] $grid-float-wide-listing-margin-seg1
    [page-end] $grid-float-wide-listing-margin-gutter
    [screen-end-inset] $grid-float-wide-listing-page-gutter-end
    [screen-end];
}

// medium 976 down
@mixin page-columns-float-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-mid-page-gutter-start
    [screen-start-inset] $grid-float-mid-sidebar-gutter
    [page-start page-start-inset body-start-outset body-start] $grid-float-mid-body-gutter-start
    [body-content-start] $grid-float-mid-body
    [body-content-end] $grid-float-mid-body-gutter-end
    [body-end] $grid-float-mid-margin-seg3
    [body-end-outset] $grid-float-mid-margin-seg2
    [page-end-inset] $grid-float-mid-margin-seg1
    [page-end] $grid-float-mid-margin-gutter
    [screen-end-inset] $grid-float-mid-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-slimcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-mid-slim-page-gutter-start
    [screen-start-inset] $grid-float-mid-slim-sidebar-gutter
    [page-start page-start-inset body-start-outset body-start] $grid-float-mid-slim-body-gutter-start
    [body-content-start] $grid-float-mid-slim-body
    [body-content-end] $grid-float-mid-slim-body-gutter-end
    [body-end] $grid-float-mid-slim-margin-seg3
    [body-end-outset] $grid-float-mid-slim-margin-seg2
    [page-end-inset] $grid-float-mid-slim-margin-seg1
    [page-end] $grid-float-mid-slim-margin-gutter
    [screen-end-inset] $grid-float-mid-slim-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-fullcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-mid-full-page-gutter-start
    [screen-start-inset] $grid-float-mid-full-sidebar-gutter
    [page-start page-start-inset body-start-outset body-start] $grid-float-mid-full-body-gutter-start
    [body-content-start] $grid-float-mid-full-body
    [body-content-end]$grid-float-mid-full-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-float-mid-full-margin-gutter
    [screen-end-inset] $grid-float-mid-full-page-gutter-end
    [screen-end];
}

@mixin page-columns-float-listing-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-float-mid-listing-page-gutter-start
    [screen-start-inset] $grid-float-mid-listing-sidebar-gutter
    [page-start page-start-inset body-start-outset body-start] $grid-float-mid-listing-body-gutter-start
    [body-content-start] $grid-float-mid-listing-body
    [body-content-end] $grid-float-mid-listing-body-gutter-end
    [body-end] $grid-float-mid-listing-margin-seg3
    [body-end-outset] $grid-float-mid-listing-margin-seg2
    [page-end-inset] $grid-float-mid-listing-margin-seg1
    [page-end] $grid-float-mid-listing-margin-gutter
    [screen-end-inset] $grid-float-mid-listing-page-gutter-end
    [screen-end];
}

// Docked Grid Definitions
@mixin page-columns-docked-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-wide-page-gutter-start
    [screen-start-inset page-start] $grid-docked-wide-sidebar-seg1
    [page-start-inset] $grid-docked-wide-sidebar-seg2
    [body-start-outset] $grid-docked-wide-sidebar-seg3
    [body-start] $grid-docked-wide-body-gutter-start
    [body-content-start] $grid-docked-wide-body
    [body-content-end] $grid-docked-wide-body-gutter-end
    [body-end] $grid-docked-wide-margin-seg3
    [body-end-outset] $grid-docked-wide-margin-seg2
    [page-end-inset] $grid-docked-wide-margin-seg1
    [page-end] $grid-docked-wide-margin-gutter
    [screen-end-inset] $grid-docked-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-slimcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-wide-slim-page-gutter-start
    [screen-start-inset page-start] $grid-docked-wide-slim-sidebar-seg1
    [page-start-inset] $grid-docked-wide-slim-sidebar-seg2
    [body-start-outset] $grid-docked-wide-slim-sidebar-seg3
    [body-start] $grid-docked-wide-slim-body-gutter-start
    [body-content-start] $grid-docked-wide-slim-body
    [body-content-end] $grid-docked-wide-slim-body-gutter-end
    [body-end] $grid-docked-wide-slim-margin-seg3
    [body-end-outset] $grid-docked-wide-slim-margin-seg2
    [page-end-inset] $grid-docked-wide-slim-margin-seg1
    [page-end] $grid-docked-wide-slim-margin-gutter
    [screen-end-inset] $grid-docked-wide-slim-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-fullcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-wide-full-page-gutter-start
    [screen-start-inset page-start] $grid-docked-wide-full-sidebar-seg1
    [page-start-inset] $grid-docked-wide-full-sidebar-seg2
    [body-start-outset] $grid-docked-wide-full-sidebar-seg3
    [body-start] $grid-docked-wide-full-body-gutter-start
    [body-content-start] $grid-docked-wide-full-body
    [body-content-end] $grid-docked-wide-full-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-docked-wide-full-margin-gutter
    [screen-end-inset] $grid-docked-wide-full-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-listing-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-wide-listing-page-gutter-start
    [screen-start-inset page-start] $grid-docked-wide-listing-sidebar-seg1
    [page-start-inset] $grid-docked-wide-listing-sidebar-seg2
    [body-start-outset] $grid-docked-wide-listing-sidebar-seg3
    [body-start] $grid-docked-wide-listing-body-gutter-start
    [body-content-start] $grid-docked-wide-listing-body
    [body-content-end] $grid-docked-wide-listing-body-gutter-end
    [body-end] $grid-docked-wide-listing-margin-seg3
    [body-end-outset] $grid-docked-wide-listing-margin-seg2
    [page-end-inset] $grid-docked-wide-listing-margin-seg1
    [page-end] $grid-docked-wide-listing-margin-gutter
    [screen-end-inset] $grid-docked-wide-listing-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-mid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset body-start body-content-start] $grid-docked-mid-body
    [body-content-end] $grid-docked-mid-body-gutter-end
    [body-end] $grid-docked-mid-margin-seg3
    [body-end-outset] $grid-docked-mid-margin-seg2
    [page-end-inset] $grid-docked-mid-margin-seg1
    [page-end] $grid-docked-mid-margin-gutter
    [screen-end-inset] $grid-docked-mid-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-slimcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-mid-slim-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset body-start body-content-start] $grid-docked-mid-slim-body
    [body-content-end] $grid-docked-mid-slim-body-gutter-end
    [body-end] $grid-docked-mid-slim-margin-seg3
    [body-end-outset] $grid-docked-mid-slim-margin-seg2
    [page-end-inset] $grid-docked-mid-slim-margin-seg1
    [page-end] $grid-docked-mid-slim-margin-gutter
    [screen-end-inset] $grid-docked-mid-slim-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-fullcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-mid-full-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset body-start body-content-start] $grid-docked-mid-full-body
    [body-content-end] $grid-docked-mid-full-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-docked-mid-full-margin-gutter
    [screen-end-inset] $grid-docked-mid-full-page-gutter-end
    [screen-end];
}

@mixin page-columns-docked-listing-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-docked-mid-listing-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset body-start body-content-start] $grid-docked-mid-listing-body
    [body-content-end] $grid-docked-mid-listing-body-gutter-start
    [body-end] $grid-docked-mid-slim-margin-seg3
    [body-end-outset] $grid-docked-mid-slim-margin-seg2
    [page-end-inset] $grid-docked-mid-slim-margin-seg1
    [page-end] $grid-docked-mid-listing-margin-gutter
    [screen-end-inset] $grid-docked-mid-listing-page-gutter-end
    [screen-end];
}

// Plain Grid Definitions
@mixin page-columns-default-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-wide-page-gutter-start
    [screen-start-inset] $grid-default-wide-sidebar-gutter
    [page-start page-start-inset] $grid-default-wide-sidebar-seg1
    [body-start-outset] $grid-default-wide-sidebar-seg2
    [body-start] $grid-default-wide-body-gutter-start
    [body-content-start] $grid-default-wide-body
    [body-content-end] $grid-default-wide-body-gutter-end
    [body-end] $grid-default-wide-margin-seg3
    [body-end-outset] $grid-default-wide-margin-seg2
    [page-end-inset] $grid-default-wide-margin-seg1
    [page-end] $grid-default-wide-margin-gutter
    [screen-end-inset] $grid-default-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-default-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-mid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset] $grid-default-mid-sidebar-gutter
    [body-start] $grid-default-mid-body-gutter-start
    [body-content-start] $grid-default-mid-body
    [body-content-end] $grid-default-mid-body-gutter-end
    [body-end] $grid-default-mid-margin-seg3
    [body-end-outset] $grid-default-mid-margin-seg2
    [page-end-inset] $grid-default-mid-margin-seg1
    [page-end] $grid-default-mid-margin-gutter
    [screen-end-inset] $grid-default-mid-page-gutter-end
    [screen-end];
}

// Full content grid definitions
@mixin page-columns-fullcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-full-wide-page-gutter-start
    [screen-start-inset] $grid-default-full-wide-sidebar-gutter
    [page-start page-start-inset] $grid-default-full-wide-sidebar-seg1
    [body-start-outset] $grid-default-full-wide-sidebar-seg2
    [body-start] $grid-default-full-wide-body-gutter-start
    [body-content-start] $grid-default-full-wide-body
    [body-content-end] $grid-default-full-wide-body-gutter-end
    [body-end] $grid-default-full-wide-margin-seg2
    [body-end-outset] $grid-default-full-wide-margin-seg1
    [page-end-inset page-end] $grid-default-full-wide-margin-gutter
    [screen-end-inset] $grid-default-full-wide-page-gutter-end;
}

@mixin page-columns-fullcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-full-mid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset] $grid-default-full-mid-sidebar-gutter
    [body-start] $grid-default-full-mid-body-gutter-start
    [body-content-start] $grid-default-full-mid-body
    [body-content-end] $grid-default-full-mid-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-default-full-mid-margin-gutter
    [screen-end-inset] $grid-default-full-mid-page-gutter-end
    [screen-end];
}

// Listing content grid definitions
@mixin page-columns-listing-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-listing-wide-page-gutter-start
    [screen-start-inset page-start] $grid-default-listing-wide-sidebar-seg1
    [page-start-inset] $grid-default-listing-wide-sidebar-seg2
    [body-start-outset] $grid-default-listing-wide-sidebar-seg3
    [body-start] $grid-default-listing-wide-body-gutter-start
    [body-content-start] $grid-default-listing-wide-body
    [body-content-end] $grid-default-listing-wide-body-gutter-end
    [body-end] $grid-default-listing-wide-sidebar-seg3
    [body-end-outset] $grid-default-listing-wide-margin-seg2
    [page-end-inset] $grid-default-listing-wide-sidebar-seg1
    [page-end] $grid-default-listing-wide-margin-gutter
    [screen-end-inset] $grid-default-listing-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-listing-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-listing-mid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset] $grid-default-listing-mid-sidebar-gutter
    [body-start] $grid-default-listing-mid-body-gutter-start
    [body-content-start] $grid-default-listing-mid-body
    [body-content-end body-end body-end-outset page-end-inset page-end] $grid-default-listing-mid-margin-gutter
    [screen-end-inset] $grid-default-listing-mid-page-gutter-end
    [screen-end];
}

// Slim content grid definitions
@mixin page-columns-slimcontent-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-slim-wide-page-gutter-start
    [screen-start-inset] $grid-default-slim-wide-sidebar-gutter
    [page-start page-start-inset] $grid-default-slim-wide-sidebar-seg1
    [body-start-outset] $grid-default-slim-wide-sidebar-seg2
    [body-start] $grid-default-slim-wide-body-gutter-start
    [body-content-start] $grid-default-slim-wide-body
    [body-content-end] $grid-default-slim-wide-body-gutter-end
    [body-end] $grid-default-slim-wide-sidebar-seg3
    [body-end-outset] $grid-default-slim-wide-margin-seg2
    [page-end-inset] $grid-default-slim-wide-sidebar-seg1
    [page-end] $grid-default-slim-wide-margin-gutter
    [screen-end-inset] $grid-default-slim-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-slimcontent-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-slim-mid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset] $grid-default-slim-mid-sidebar-gutter
    [body-start] $grid-default-slim-mid-body-gutter-start
    [body-content-start] $grid-default-slim-mid-body
    [body-content-end] $grid-default-slim-mid-body-gutter-end
    [body-end] $grid-default-slim-mid-margin-seg3
    [body-end-outset] $grid-default-slim-mid-margin-seg2
    [page-end-inset] $grid-default-slim-mid-margin-seg1
    [page-end] $grid-default-slim-mid-margin-gutter
    [screen-end-inset] $grid-default-slim-mid-page-gutter-end
    [screen-end];
}

// TOC left grid
@mixin page-columns-tocleft-wide {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-toc-wide-page-gutter-start
    [screen-start-inset] $grid-default-toc-wide-sidebar-gutter
    [page-start] $grid-default-toc-wide-sidebar-seg1
    [page-start-inset] $grid-default-toc-wide-sidebar-seg2
    [body-start-outset] $grid-default-toc-wide-sidebar-seg3
    [body-start] $grid-default-toc-wide-body-gutter-start
    [body-content-start] $grid-default-toc-wide-body
    [body-content-end] $grid-default-toc-wide-body-gutter-end
    [body-end] $grid-default-toc-wide-margin-seg3
    [body-end-outset] $grid-default-toc-wide-margin-seg2
    [page-end-inset] $grid-default-toc-wide-margin-seg1
    [page-end] $grid-default-toc-wide-margin-gutter
    [screen-end-inset] $grid-default-toc-wide-page-gutter-end
    [screen-end];
}

@mixin page-columns-tocleft-mid {
  @include page-columns();
  grid-template-columns:
    [screen-start] $grid-default-toc-mid-page-gutter-start
    [screen-start-inset] $grid-default-toc-mid-sidebar-gutter
    [page-start] $grid-default-toc-mid-sidebar-seg1
    [page-start-inset] $grid-default-toc-mid-sidebar-seg2
    [body-start-outset] $grid-default-toc-mid-sidebar-seg3
    [body-start] $grid-default-toc-mid-body-gutter-start
    [body-content-start] $grid-default-toc-mid-body
    [body-content-end] $grid-default-toc-mid-body-gutter-end
    [body-end body-end-outset page-end-inset page-end] $grid-default-toc-mid-margin-gutter
    [screen-end-inset] $grid-default-toc-mid-page-gutter-end
    [screen-end];
}

// Small size responsive grid
// All grids share this fully collapsed mode which hides
// the sidebar and margin
@mixin grid-template-columns-narrow {
  grid-template-columns:
    [screen-start] $grid-page-gutter-start
    [screen-start-inset page-start page-start-inset body-start-outset body-start body-content-start] minmax(
      0px,
      1fr
    )
    [body-content-end body-end body-end-outset page-end-inset page-end screen-end-inset] $grid-page-gutter-end
    [screen-end];
}
