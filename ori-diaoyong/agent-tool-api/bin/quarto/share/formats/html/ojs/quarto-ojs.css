span.ojs-inline span div {
  display: inline-block;
}

/* add some breathing room between display outputs and text especially */
div.cell + section,
div.cell + h1,
div.cell + h2,
div.cell + h3,
div.cell + h4,
div.cell + h5,
div.cell + h6,
div.cell + p {
  margin-top: 1rem;
}

div[nodetype="declaration"]:not([data-output="all"])
  div.observablehq:not(observablehq--error) {
  display: none;
}

/* mimic bootstrap behavior which is always correct in observablehq outputs */
.observablehq a:not([href]):not([class]),
.observablehq a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

.observablehq .observablehq--inspect {
  --code-fallback: Consolas, monaco, monospace;
  font-family: var(--bs-font-monospace, var(--code-fallback));
  font-size: 0.8em;
}

.observablehq--field {
  margin-left: 1rem;
}

.observablehq--caret {
  margin-right: 2px;
  vertical-align: baseline;
}

.observablehq--collapsed,
.observablehq--expanded.observablehq--inspect a {
  cursor: pointer;
}

/* classes directly from observable's runtime */
.observablehq--key,
.observablehq--index {
  color: var(--quarto-hl-dt-color);
}

.observablehq--string {
  color: var(--quarto-hl-st-color);
}

.observablehq--bigint,
.observablehq--date,
.observablehq--number,
.observablehq--regexp,
.observablehq--symbol {
  color: var(--quarto-hl-dv-color);
}

.observablehq--null,
.observablehq--boolean,
.observablehq--undefined,
.observablehq--keyword {
  color: var(--quarto-hl-kw-color);
}

/* In addition, their import statements specifically come highlighted by hljs.
   (probably some legacy feature of theirs?) We handle those here as well. 

   Just to be on the safe side, we select on observable's 'md-pre'
   class as well, in case someone else uses hljs and wishes to put
   their own highlighting.

   TODO Note that to make our highlighting consistent, we're
   overriding the "im" class to present like a keyword. I should make
   sure this looks right everywhere, but I don't know how to test it
   comprehensively.
*/

code.javascript span.im {
  color: var(--quarto-hl-kw-color);
}

pre.observablehq--md-pre span.hljs-keyword {
  color: var(--quarto-hl-kw-color);
}

pre.observablehq--md-pre span.hljs-string {
  color: var(--quarto-hl-st-color);
}

pre.observablehq--md-pre .span.hljs-date,
pre.observablehq--md-pre .span.hljs-number,
pre.observablehq--md-pre .span.hljs-regexp,
pre.observablehq--md-pre .span.hljs-symbol {
  color: var(--quarto-hl-dv-color);
}

/* Other general niceties, but it's possible that we should do this on a page-by-page basis */

input {
  vertical-align: middle;
}

input[type="radio"],
input[type="checkbox"] {
  margin: 0px 0px 3px 0px;
}

.observable-in-a-box-waiting-for-module-import {
  visibility: hidden;
}

/* play nicely w/ sidebar layout */
.panel-sidebar .observablehq > form[class^="oi-"] {
  flex-wrap: wrap !important;
}

/* likely that this only makes sense with bootstrap. TODO check with charles */
.observablehq table {
  font-size: 0.9em !important;
}

.quarto-ojs-hide {
  display: none;
}

.quarto-ojs-error-pinpoint {
  border-bottom: 2px dotted #e51400;
  font-weight: 700;
  cursor: pointer;
}

code span.quarto-ojs-error-pinpoint {
  color: inherit;
}

.observablehq--error .observablehq--inspect {
  font-size: 0.875em !important;
}

.observablehq--error .callout {
  margin-bottom: 0;
  margin-top: 0;
}

/* this counteracts the negative margin which observablehq uses in their tables at runtime */
.quarto-ojs-table-fixup {
  padding-left: 14px;
}

div.observablehq input[type="date"] {
  border-style: solid;
  padding: 0.8em 0.5em 0.8em 0.5em;
  border-color: rgba(220, 220, 220, 0.3);
}
