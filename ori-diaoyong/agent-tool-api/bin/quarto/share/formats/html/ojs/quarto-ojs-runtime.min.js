var e={},t={};function i(e){return new Function("d","return {"+e.map((function(e,t){return JSON.stringify(e)+": d["+t+'] || ""'})).join(",")+"}")}function n(e){var t=Object.create(null),i=[];return e.forEach((function(e){for(var n in e)n in t||i.push(t[n]=n)})),i}function s(e,t){var i=e+"",n=i.length;return n<t?new Array(t-n+1).join(0)+i:i}function r(e){var t,i=e.getUTCHours(),n=e.getUTCMinutes(),r=e.getUTCSeconds(),a=e.getUTCMilliseconds();return isNaN(e)?"Invalid Date":((t=e.getUTCFullYear())<0?"-"+s(-t,6):t>9999?"+"+s(t,6):s(t,4))+"-"+s(e.getUTCMonth()+1,2)+"-"+s(e.getUTCDate(),2)+(a?"T"+s(i,2)+":"+s(n,2)+":"+s(r,2)+"."+s(a,3)+"Z":r?"T"+s(i,2)+":"+s(n,2)+":"+s(r,2)+"Z":n||i?"T"+s(i,2)+":"+s(n,2)+"Z":"")}function a(s){var a=new RegExp('["'+s+"\n\r]"),o=s.charCodeAt(0);function u(i,n){var s,r=[],a=i.length,u=0,c=0,p=a<=0,l=!1;function h(){if(p)return t;if(l)return l=!1,e;var n,s,r=u;if(34===i.charCodeAt(r)){for(;u++<a&&34!==i.charCodeAt(u)||34===i.charCodeAt(++u););return(n=u)>=a?p=!0:10===(s=i.charCodeAt(u++))?l=!0:13===s&&(l=!0,10===i.charCodeAt(u)&&++u),i.slice(r+1,n-1).replace(/""/g,'"')}for(;u<a;){if(10===(s=i.charCodeAt(n=u++)))l=!0;else if(13===s)l=!0,10===i.charCodeAt(u)&&++u;else if(s!==o)continue;return i.slice(r,n)}return p=!0,i.slice(r,a)}for(10===i.charCodeAt(a-1)&&--a,13===i.charCodeAt(a-1)&&--a;(s=h())!==t;){for(var d=[];s!==e&&s!==t;)d.push(s),s=h();n&&null==(d=n(d,c++))||r.push(d)}return r}function c(e,t){return e.map((function(e){return t.map((function(t){return l(e[t])})).join(s)}))}function p(e){return e.map(l).join(s)}function l(e){return null==e?"":e instanceof Date?r(e):a.test(e+="")?'"'+e.replace(/"/g,'""')+'"':e}return{parse:function(e,t){var n,s,r=u(e,(function(e,r){if(n)return n(e,r-1);s=e,n=t?function(e,t){var n=i(e);return function(i,s){return t(n(i),s,e)}}(e,t):i(e)}));return r.columns=s||[],r},parseRows:u,format:function(e,t){return null==t&&(t=n(e)),[t.map(l).join(s)].concat(c(e,t)).join("\n")},formatBody:function(e,t){return null==t&&(t=n(e)),c(e,t).join("\n")},formatRows:function(e){return e.map(p).join("\n")},formatRow:p,formatValue:l}}var o=a(","),u=o.parse,c=o.parseRows,p=a("\t"),l=p.parse,h=p.parseRows;function d(e){for(var t in e){var i,n,s=e[t].trim();if(s)if("true"===s)s=!0;else if("false"===s)s=!1;else if("NaN"===s)s=NaN;else if(isNaN(i=+s)){if(!(n=s.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/)))continue;f&&n[4]&&!n[7]&&(s=s.replace(/-/g,"/").replace(/T/," ")),s=new Date(s)}else s=i;else s=null;e[t]=s}return e}const f=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours();function m(e,t,i){return{resolve:(n=i)=>`${e}@${t}/${n}`}}const g=m("d3","7.8.5","dist/d3.min.js"),v=m("@observablehq/inputs","0.10.6","dist/inputs.min.js"),x=m("@observablehq/plot","0.6.11","dist/plot.umd.min.js"),y=m("@observablehq/graphviz","0.2.1","dist/graphviz.min.js"),b=m("@observablehq/highlight.js","2.0.0","highlight.min.js"),E=m("@observablehq/katex","0.11.1","dist/katex.min.js"),C=m("lodash","4.17.21","lodash.min.js"),D=m("htl","0.3.1","dist/htl.min.js"),A=m("jszip","3.10.1","dist/jszip.min.js"),w=m("marked","0.3.12","marked.min.js"),F=m("sql.js","1.8.0","dist/sql-wasm.js"),k=m("vega","5.22.1","build/vega.min.js"),S=m("vega-lite","5.6.0","build/vega-lite.min.js"),_=m("vega-lite-api","5.0.0","build/vega-lite-api.min.js"),B=m("apache-arrow","4.0.1","Arrow.es2015.min.js"),I=m("apache-arrow","9.0.0","+esm"),P=m("apache-arrow","11.0.0","+esm"),N=m("arquero","4.8.8","dist/arquero.min.js"),T=m("topojson-client","3.1.0","dist/topojson-client.min.js"),L=m("exceljs","4.3.0","dist/exceljs.min.js"),V=m("mermaid","9.2.2","dist/mermaid.min.js"),R=m("leaflet","1.9.3","dist/leaflet.js"),O=m("@duckdb/duckdb-wasm","1.24.0","+esm"),M=new Map,j=[],q=j.map,U=j.some,z=j.hasOwnProperty,W=/^((?:@[^/@]+\/)?[^/@]+)(?:@([^/]+))?(?:\/(.*))?$/,H=/^\d+\.\d+\.\d+(-[\w-.+]+)?$/,G=/(?:\.[^/]*|\/)$/;class RequireError extends Error{constructor(e){super(e)}}function Q(e){const t=W.exec(e);return t&&{name:t[1],version:t[2],path:t[3]}}function K(e="https://cdn.jsdelivr.net/npm/",t=["unpkg","jsdelivr","browser","main"]){if(!/\/$/.test(e))throw new Error("origin lacks trailing slash");function i(t){const i=`${e}${t.name}${t.version?`@${t.version}`:""}/package.json`;let n=M.get(i);return n||M.set(i,n=fetch(i).then((e=>{if(!e.ok)throw new RequireError("unable to load package.json");return e.redirected&&!M.has(e.url)&&M.set(e.url,n),e.json()}))),n}return async function(n,s){if(n.startsWith(e)&&(n=n.substring(e.length)),/^(\w+:)|\/\//i.test(n))return n;if(/^[.]{0,2}\//i.test(n))return new URL(n,null==s?location:s).href;if(!n.length||/^[\s._]/.test(n)||/\s$/.test(n))throw new RequireError("illegal name");const r=Q(n);if(!r)return`${e}${n}`;if(!r.version&&null!=s&&s.startsWith(e)){const t=await i(Q(s.substring(e.length)));r.version=t.dependencies&&t.dependencies[r.name]||t.peerDependencies&&t.peerDependencies[r.name]}if(r.path&&!G.test(r.path)&&(r.path+=".js"),r.path&&r.version&&H.test(r.version))return`${e}${r.name}@${r.version}/${r.path}`;const a=await i(r);return`${e}${a.name}@${a.version}/${r.path||function(e){for(const i of t){let t=e[i];if("string"==typeof t)return t.startsWith("./")&&(t=t.slice(2)),G.test(t)?t:`${t}.js`}}(a)||"index.js"}`}}RequireError.prototype.name=RequireError.name;var Y=Z(K());let J,X=0;function Z(e){const t=new Map,i=s(null);function n(e){if("string"!=typeof e)return e;let i=t.get(e);return i||t.set(e,i=new Promise(((t,i)=>{const n=document.createElement("script");n.onload=()=>{try{t(j.pop()(s(e)))}catch(e){i(new RequireError("invalid module"))}n.remove(),X--,0===X&&(window.define=J)},n.onerror=()=>{i(new RequireError("unable to load module")),n.remove(),X--,0===X&&(window.define=J)},n.async=!0,n.src=e,0===X&&(J=window.define,window.define=ne),X++,document.head.appendChild(n)}))),i}function s(t){return i=>Promise.resolve(e(i,t)).then(n)}function r(e){return arguments.length>1?Promise.all(q.call(arguments,i)).then(ee):i(e)}return r.alias=function(t){return Z(((i,n)=>i in t&&(n=null,"string"!=typeof(i=t[i]))?i:e(i,n)))},r.resolve=e,r}function ee(e){const t={};for(const i of e)for(const e in i)z.call(i,e)&&(null==i[e]?Object.defineProperty(t,e,{get:te(i,e)}):t[e]=i[e]);return t}function te(e,t){return()=>e[t]}function ie(e){return"exports"===(e+="")||"module"===e}function ne(e,t,i){const n=arguments.length;n<2?(i=e,t=[]):n<3&&(i=t,t="string"==typeof e?[]:e),j.push(U.call(t,ie)?e=>{const n={},s={exports:n};return Promise.all(q.call(t,(t=>"exports"===(t+="")?n:"module"===t?s:e(t)))).then((e=>(i.apply(null,e),s.exports)))}:e=>Promise.all(q.call(t,e)).then((e=>"function"==typeof i?i.apply(null,e):i)))}ne.amd={};const se="https://cdn.observableusercontent.com/npm/";let re,ae=Y;async function oe(e){const[t,i]=await Promise.all([e(F.resolve()),e.resolve(F.resolve("dist/"))]);return t({locateFile:e=>`${i}${e}`})}class ue{constructor(e){Object.defineProperties(this,{_db:{value:e}})}static async open(e){const[t,i]=await Promise.all([oe(ae),Promise.resolve(e).then(pe)]);return new ue(new t.Database(i))}async query(e,t){return await async function(e,t,i){const[n]=await e.exec(t,i);if(!n)return[];const{columns:s,values:r}=n,a=r.map((e=>function(e){const t={};for(const[i,n]of e)t[i]=n;return t}(e.map(((e,t)=>[s[t],e])))));return a.columns=s,a}(this._db,e,t)}async queryRow(e,t){return(await this.query(e,t))[0]||null}async explain(e,t){return le("pre",{className:"observablehq--inspect"},[he((await this.query(`EXPLAIN QUERY PLAN ${e}`,t)).map((e=>e.detail)).join("\n"))])}async describeTables({schema:e}={}){return this.query(`SELECT NULLIF(schema, 'main') AS schema, name FROM pragma_table_list() WHERE type = 'table'${null==e?"":" AND schema = ?"} AND name NOT LIKE 'sqlite_%' ORDER BY schema, name`,null==e?[]:[e])}async describeColumns({schema:e,table:t}={}){if(null==t)throw new Error("missing table");const i=await this.query(`SELECT name, type, "notnull" FROM pragma_table_info(?${null==e?"":", ?"}) ORDER BY cid`,null==e?[t]:[t,e]);if(!i.length)throw new Error(`table not found: ${t}`);return i.map((({name:e,type:t,notnull:i})=>({name:e,type:ce(t),databaseType:t,nullable:!i})))}async describe(e){const t=await(void 0===e?this.query("SELECT name FROM sqlite_master WHERE type = 'table'"):this.query("SELECT * FROM pragma_table_info(?)",[e]));if(!t.length)throw new Error("Not found");const{columns:i}=t;return le("table",{value:t},[le("thead",[le("tr",i.map((e=>le("th",[he(e)]))))]),le("tbody",t.map((e=>le("tr",i.map((t=>le("td",[he(e[t])])))))))])}async sql(){return this.query(...this.queryTag.apply(this,arguments))}queryTag(e,...t){return[e.join("?"),t]}}function ce(e){switch(e){case"NULL":return"null";case"INT":case"INTEGER":case"TINYINT":case"SMALLINT":case"MEDIUMINT":case"BIGINT":case"UNSIGNED BIG INT":case"INT2":case"INT8":return"integer";case"TEXT":case"CLOB":case"DATE":case"DATETIME":return"string";case"REAL":case"DOUBLE":case"DOUBLE PRECISION":case"FLOAT":case"NUMERIC":return"number";case"BLOB":return"buffer";default:return/^(?:(?:(?:VARYING|NATIVE) )?CHARACTER|(?:N|VAR|NVAR)CHAR)\(/.test(e)?"string":/^(?:DECIMAL|NUMERIC)\(/.test(e)?"number":"other"}}function pe(e){return"string"==typeof e?fetch(e).then(pe):e instanceof Response||e instanceof Blob?e.arrayBuffer().then(pe):e instanceof ArrayBuffer?new Uint8Array(e):e}function le(e,t,i){2===arguments.length&&(i=t,t=void 0);const n=document.createElement(e);if(void 0!==t)for(const e in t)n[e]=t[e];if(void 0!==i)for(const e of i)n.appendChild(e);return n}function he(e){return document.createTextNode(e)}function de(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function fe(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function me(e){let t,i,n;function s(e,n,s=0,r=e.length){if(s<r){if(0!==t(n,n))return r;do{const t=s+r>>>1;i(e[t],n)<0?s=t+1:r=t}while(s<r)}return s}return 2!==e.length?(t=de,i=(t,i)=>de(e(t),i),n=(t,i)=>e(t)-i):(t=e===de||e===fe?e:ge,i=e,n=e),{left:s,center:function(e,t,i=0,r=e.length){const a=s(e,t,i,r-1);return a>i&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,s=0,r=e.length){if(s<r){if(0!==t(n,n))return r;do{const t=s+r>>>1;i(e[t],n)<=0?s=t+1:r=t}while(s<r)}return s}}}function ge(){return 0}function ve(e,t=de){let i,n=!1;if(1===t.length){let s;for(const r of e){const e=t(r);(n?de(e,s)>0:0===de(e,e))&&(i=r,s=e,n=!0)}}else for(const s of e)(n?t(s,i)>0:0===t(s,s))&&(i=s,n=!0);return i}function xe(e){return e&&"function"==typeof e.toArrowBuffer}function ye(e){return e&&"function"==typeof e.getChild&&"function"==typeof e.toArray&&e.schema&&Array.isArray(e.schema.fields)}function be(e){return{name:e.name,type:Ee(e.type),nullable:e.nullable,databaseType:String(e.type)}}function Ee(e){switch(e.typeId){case 2:return"integer";case 3:case 7:return"number";case 4:case 15:return"buffer";case 5:return"string";case 6:return"boolean";case 8:case 9:case 10:return"date";case 12:case 16:return"array";case 13:case 14:return"object";default:return"other"}}async function Ce(){return await import(`${se}${P.resolve()}`)}Object.defineProperty(ue.prototype,"dialect",{value:"sqlite"}),me(de),me((function(e){return null===e?NaN:+e})).center;class De{constructor(e){Object.defineProperties(this,{_db:{value:e}})}async queryStream(e,t){const i=await this._db.connect();let n,s;try{if(t?.length>0){const s=await i.prepare(e);n=await s.send(...t)}else n=await i.send(e);if(s=await n.next(),s.done)throw new Error("missing first batch")}catch(e){throw await i.close(),e}return{schema:(r=s.value,r.schema.fields.map(be)),async*readRows(){try{for(;!s.done;)yield s.value.toArray(),s=await n.next()}finally{await i.close()}}};var r}async query(e,t){const i=await this.queryStream(e,t),n=[];for await(const e of i.readRows())for(const t of e)n.push(t);return n.schema=i.schema,n}async queryRow(e,t){const i=(await this.queryStream(e,t)).readRows();try{const{done:e,value:t}=await i.next();return e||!t.length?null:t[0]}finally{await i.return()}}async sql(e,...t){return await this.query(e.join("?"),t)}queryTag(e,...t){return[e.join("?"),t]}escape(e){return`"${e}"`}async describeTables(){return(await this.query("SHOW TABLES")).map((({name:e})=>({name:e})))}async describeColumns({table:e}={}){return(await this.query(`DESCRIBE ${this.escape(e)}`)).map((({column_name:e,column_type:t,null:i})=>({name:e,type:ke(t),nullable:"NO"!==i,databaseType:t})))}static async of(e={},t={}){const i=await async function(){void 0===re&&(re=async function(){const e=await import(`${se}${O.resolve()}`),t=await e.selectBundle({mvp:{mainModule:`${se}${O.resolve("dist/duckdb-mvp.wasm")}`,mainWorker:`${se}${O.resolve("dist/duckdb-browser-mvp.worker.js")}`},eh:{mainModule:`${se}${O.resolve("dist/duckdb-eh.wasm")}`,mainWorker:`${se}${O.resolve("dist/duckdb-browser-eh.worker.js")}`}}),i=new e.ConsoleLogger;return{module:e,bundle:t,logger:i}}());const{module:e,bundle:t,logger:i}=await re,n=await e.createWorker(t.mainWorker),s=new e.AsyncDuckDB(i,n);return await s.instantiate(t.mainModule),s}();return void 0===t.query?.castTimestampToDate&&(t={...t,query:{...t.query,castTimestampToDate:!0}}),void 0===t.query?.castBigIntToDouble&&(t={...t,query:{...t.query,castBigIntToDouble:!0}}),await i.open(t),await Promise.all(Object.entries(e).map((async([e,t])=>{if(t instanceof Ct)await Ae(i,e,t);else if(ye(t))await we(i,e,t);else if(Array.isArray(t))await Fe(i,e,t);else if(xe(t))await async function(e,t,i){const n=(await Ce()).tableFromIPC(i.toArrowBuffer());return await we(e,t,n)}(i,e,t);else if("data"in t){const{data:n,...s}=t;ye(n)?await we(i,e,n,s):await Fe(i,e,n,s)}else{if(!("file"in t))throw new Error(`invalid source: ${t}`);{const{file:n,...s}=t;await Ae(i,e,n,s)}}}))),new De(i)}}async function Ae(e,t,i,n){const s=await i.url();if(s.startsWith("blob:")){const t=await i.arrayBuffer();await e.registerFileBuffer(i.name,new Uint8Array(t))}else await e.registerFileURL(i.name,s,4);const r=await e.connect();try{switch(i.mimeType){case"text/csv":case"text/tab-separated-values":return await r.insertCSVFromPath(i.name,{name:t,schema:"main",...n}).catch((async e=>{if(e.toString().includes("Could not convert"))return await async function(e,t,i){const n=await e.prepare(`CREATE TABLE '${i}' AS SELECT * FROM read_csv_auto(?, ALL_VARCHAR=TRUE)`);return await n.send(t.name)}(r,i,t);throw e}));case"application/json":return await r.insertJSONFromPath(i.name,{name:t,schema:"main",...n});default:if(/\.arrow$/i.test(i.name)){const e=new Uint8Array(await i.arrayBuffer());return await r.insertArrowFromIPCStream(e,{name:t,schema:"main",...n})}if(/\.parquet$/i.test(i.name))return await r.query(`CREATE VIEW '${t}' AS SELECT * FROM parquet_scan('${i.name}')`);throw new Error(`unknown file type: ${i.mimeType}`)}}finally{await r.close()}}async function we(e,t,i,n){const s=await e.connect();try{await s.insertArrowTable(i,{name:t,schema:"main",...n})}finally{await s.close()}}async function Fe(e,t,i,n){const s=(await Ce()).tableFromJSON(i);return await we(e,t,s,n)}function ke(e){switch(e){case"BIGINT":case"HUGEINT":case"UBIGINT":return"bigint";case"DOUBLE":case"REAL":case"FLOAT":return"number";case"INTEGER":case"SMALLINT":case"TINYINT":case"USMALLINT":case"UINTEGER":case"UTINYINT":return"integer";case"BOOLEAN":return"boolean";case"DATE":case"TIMESTAMP":case"TIMESTAMP WITH TIME ZONE":return"date";case"VARCHAR":case"UUID":return"string";default:return/^DECIMAL\(/.test(e)?"integer":"other"}}Object.defineProperty(De.prototype,"dialect",{value:"duckdb"});function Se(e){return Array.isArray(e)&&(_e(e.schema)||Be(e.columns)||function(e){const t=Math.min(20,e.length);for(let i=0;i<t;++i){const t=e[i];if(null===t||"object"!=typeof t)return!1}return t>0&&function(e){for(const t in e)return!0;return!1}(e[0])}(e)||Ne(e)||Te(e))||Le(e)}function _e(e){return Array.isArray(e)&&e.every(Ie)}function Be(e){return Array.isArray(e)&&e.every((e=>"string"==typeof e))}function Ie(e){return e&&"string"==typeof e.name&&"string"==typeof e.type}function Pe(e){return Le(e)||Ne(e)||Te(e)}function Ne(e){const t=Math.min(20,e.length);if(!(t>0))return!1;let i,n=!1;for(let s=0;s<t;++s){const t=e[s];if(null==t)continue;const r=typeof t;if(void 0===i)switch(r){case"number":case"boolean":case"string":case"bigint":i=r;break;default:return!1}else if(r!==i)return!1;n=!0}return n}function Te(e){const t=Math.min(20,e.length);if(!(t>0))return!1;let i=!1;for(let n=0;n<t;++n){const t=e[n];if(null!=t){if(!(t instanceof Date))return!1;i=!0}}return i}function Le(e){return e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array}const Ve=Object.assign((async(e,t,i,n)=>{if(e=await Oe(await e,n),(s=e)&&("function"==typeof s.sql||"function"==typeof s.queryTag&&("function"==typeof s.query||"function"==typeof s.queryStream))&&("table"!==r||"function"==typeof s.describeColumns)&&s!==Ve)return qe(e,function(e,t){const i="function"==typeof t.escape?t.escape:e=>e,{select:n,from:s,filter:r,sort:a,slice:o}=e;if(!s.table)throw new Error("missing from table");if(n.columns&&0===n.columns.length)throw new Error("at least one column must be selected");const u=new Map(e.names?.map((({column:e,name:t})=>[e,t]))),c=n.columns?n.columns.map((e=>{const t=u.get(e);return t?`${i(e)} AS ${i(t)}`:i(e)})).join(", "):"*",p=[[`SELECT ${c} FROM ${$e(s.table,i)}`]];for(let e=0;e<r.length;++e)Ue(e?"\nAND ":"\nWHERE ",p),We(r[e],p,i);for(let e=0;e<a.length;++e)Ue(e?", ":"\nORDER BY ",p),ze(a[e],p,i);if("mssql"===t.dialect||"oracle"===t.dialect){if(null!==o.to||null!==o.from){if(!a.length){if(!n.columns)throw new Error("at least one column must be explicitly specified. Received '*'.");Ue("\nORDER BY ",p),ze({column:n.columns[0],direction:"ASC"},p,i)}Ue(`\nOFFSET ${o.from||0} ROWS`,p),Ue(`\nFETCH NEXT ${null!==o.to?o.to-(o.from||0):1e9} ROWS ONLY`,p)}}else null===o.to&&null===o.from||Ue("\nLIMIT "+(null!==o.to?o.to-(o.from||0):1e9),p),null!==o.from&&Ue(` OFFSET ${o.from}`,p);return p}(t,e),i);var s,r;if(Se(e))return function(e,t){const i=new Map,n=e,s=ct(e,t);e=s.source;let r=s.schema;if(t.derive){const n=[];t.derive.map((({name:s,value:r})=>{let a=[];pt(e,t).map(((e,t)=>{let i;try{i=r(e)}catch(e){a.push({index:t,error:e}),i=void 0}n[t]?n[t]={...n[t],[s]:i}:n.push({[s]:i})})),a.length&&i.set(s,a)}));const s=ct(n,t);e=e.map(((e,t)=>({...e,...s.source[t]}))),r=[...r,...s.schema]}for(const{type:i,operands:n}of t.filter){const[{value:t}]=n,s=n.slice(1).map((({value:e})=>e));switch(i){case"v":{const[i]=s,n=at(i);e=e.filter((e=>n(e[t])));break}case"nv":{const[i]=s,n=at(i);e=e.filter((e=>!n(e[t])));break}case"eq":{const[i]=s;if(i instanceof Date){const n=+i;e=e.filter((e=>+e[t]===n))}else e=e.filter((e=>e[t]===i));break}case"ne":{const[i]=s;e=e.filter((e=>e[t]!==i));break}case"c":{const[i]=s;e=e.filter((e=>"string"==typeof e[t]&&e[t].includes(i)));break}case"nc":{const[i]=s;e=e.filter((e=>"string"==typeof e[t]&&!e[t].includes(i)));break}case"in":{const i=new Set(s);e=e.filter((e=>i.has(e[t])));break}case"nin":{const i=new Set(s);e=e.filter((e=>!i.has(e[t])));break}case"n":e=e.filter((e=>null==e[t]));break;case"nn":e=e.filter((e=>null!=e[t]));break;case"lt":{const[i]=s;e=e.filter((e=>e[t]<i));break}case"lte":{const[i]=s;e=e.filter((e=>e[t]<=i));break}case"gt":{const[i]=s;e=e.filter((e=>e[t]>i));break}case"gte":{const[i]=s;e=e.filter((e=>e[t]>=i));break}default:throw new Error(`unknown filter type: ${i}`)}}for(const{column:i,direction:s}of function(e){if("function"!=typeof e[Symbol.iterator])throw new TypeError("values is not iterable");return Array.from(e).reverse()}(t.sort)){const t="desc"===s?Ke:Qe;e===n&&(e=e.slice()),e.sort(((e,n)=>t(e[i],n[i])))}let{from:a,to:o}=t.slice;a=null==a?0:Math.max(0,a),o=null==o?1/0:Math.max(0,o),(a>0||o<1/0)&&(e=e.slice(Math.max(0,a),Math.max(0,o)));let u=r.slice();if(t.select.columns){if(r){const e=new Map(r.map((e=>[e.name,e])));r=t.select.columns.map((t=>e.get(t)))}e=e.map((e=>Object.fromEntries(t.select.columns.map((t=>[t,e[t]])))))}if(t.names){const i=new Map(t.names.map((e=>[e.column,e])));r&&(r=r.map((e=>{const t=i.get(e.name);return{...e,...t?{name:t.name}:null}}))),u&&(u=u.map((e=>{const t=i.get(e.name);return{...e,...t?{name:t.name}:null}}))),e=pt(e,t)}e!==n&&r&&(e.schema=r);return e.fullSchema=u,e.errors=i,e}(e,t);if(!e)throw new Error("missing data source");throw new Error("invalid data source")}),{sql:(e,t,i)=>async function(){return qe(await Me(await e,i),arguments,t)}});function Re(e){const t=new WeakMap;return(i,n)=>{if(!i||"object"!=typeof i)throw new Error("invalid data source");let s=t.get(i);return(!s||Se(i)&&i.length!==s._numRows)&&(s=e(i,n),s._numRows=i.length,t.set(i,s)),s}}const Oe=Re((async(e,t)=>{if(e instanceof Ct){switch(e.mimeType){case"text/csv":return e.csv();case"text/tab-separated-values":return e.tsv();case"application/json":return e.json();case"application/x-sqlite3":return e.sqlite()}if(/\.(arrow|parquet)$/i.test(e.name))return je(e,t);throw new Error(`unsupported file type: ${e.mimeType}`)}return ye(e)||xe(e)?je(e,t):Se(e)&&Pe(e)?Array.from(e,(e=>({value:e}))):e})),Me=Re((async(e,t)=>{if(e instanceof Ct){switch(e.mimeType){case"text/csv":case"text/tab-separated-values":case"application/json":return je(e,t);case"application/x-sqlite3":return e.sqlite()}if(/\.(arrow|parquet)$/i.test(e.name))return je(e,t);throw new Error(`unsupported file type: ${e.mimeType}`)}return Se(e)?je(await async function(e,t){const i=await Ce();return Pe(e)?i.tableFromArrays({[t]:e}):i.tableFromJSON(e)}(e,t),t):ye(e)||xe(e)?je(e,t):e}));function je(e,t=(e instanceof Ct?function(e){return e.name.replace(/@\d+(?=\.|$)/,"").replace(/\.\w+$/,"")}(e):"__table")){return De.of({[t]:e})}async function qe(e,t,i){if(!e)throw new Error("missing data source");if("function"==typeof e.queryTag){const n=new AbortController,s={signal:n.signal};if(i.then((()=>n.abort("invalidated"))),"function"==typeof e.queryStream)return async function*(e){let t=performance.now();const i=await e,n=[];n.done=!1,n.error=null,n.schema=i.schema;try{for await(const e of i.readRows()){performance.now()-t>150&&n.length>0&&(yield n,t=performance.now());for(const t of e)n.push(t)}n.done=!0,yield n}catch(e){n.error=e,yield n}}(e.queryStream(...e.queryTag.apply(e,t),s));if("function"==typeof e.query)return e.query(...e.queryTag.apply(e,t),s)}if("function"==typeof e.sql)return e.sql.apply(e,t);throw new Error("source does not implement query, queryStream, or sql")}function $e(e,t){if("object"==typeof e){let i="";return null!=e.database&&(i+=t(e.database)+"."),null!=e.schema&&(i+=t(e.schema)+"."),i+=t(e.table),i}return t(e)}function Ue(e,t){const i=t[0];i[i.length-1]+=e}function ze({column:e,direction:t},i,n){Ue(`${n(e)} ${t.toUpperCase()}`,i)}function We({type:e,operands:t},i,n){if(t.length<1)throw new Error("Invalid operand length");if(1===t.length||"v"===e||"nv"===e)switch(He(t[0],i,n),e){case"n":case"nv":return void Ue(" IS NULL",i);case"nn":case"v":return void Ue(" IS NOT NULL",i);default:throw new Error("Invalid filter operation")}if(2!==t.length||["in","nin"].includes(e)){var s;switch(He(t[0],i,n),e){case"in":Ue(" IN (",i);break;case"nin":Ue(" NOT IN (",i);break;default:throw new Error("Invalid filter operation")}!function(e,t){let i=!0;for(const n of e)i?i=!1:Ue(",",t),t.push(n.value),t[0].push("")}(t.slice(1),i),Ue(")",i)}else{if(["c","nc"].includes(e)){switch(He(t[0],i,n),e){case"c":Ue(" LIKE ",i);break;case"nc":Ue(" NOT LIKE ",i)}return void He((s=t[1],{...s,value:`%${s.value}%`}),i,n)}switch(He(t[0],i,n),e){case"eq":Ue(" = ",i);break;case"ne":Ue(" <> ",i);break;case"gt":Ue(" > ",i);break;case"lt":Ue(" < ",i);break;case"gte":Ue(" >= ",i);break;case"lte":Ue(" <= ",i);break;default:throw new Error("Invalid filter operation")}He(t[1],i,n)}}function He(e,t,i){"column"===e.type?Ue(i(e.value),t):(t.push(e.value),t[0].push(""))}function Ge(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))}function Qe(e,t){return Ge(e,t)||(e<t?-1:e>t?1:0)}function Ke(e,t){return Ge(e,t)||(e>t?-1:e<t?1:0)}const Ye=e=>"number"==typeof e&&!Number.isNaN(e),Je=e=>Number.isInteger(e)&&!Number.isNaN(e),Xe=e=>"string"==typeof e,Ze=e=>"boolean"==typeof e,et=e=>"bigint"==typeof e,tt=e=>e instanceof Date&&!isNaN(e),it=e=>e instanceof ArrayBuffer,nt=e=>Array.isArray(e),st=e=>"object"==typeof e&&null!==e,rt=e=>null!=e;function at(e){switch(e){case"string":return Xe;case"bigint":return et;case"boolean":return Ze;case"number":return Ye;case"integer":return Je;case"date":return tt;case"buffer":return it;case"array":return nt;case"object":return st;default:return rt}}const ot=/^(([-+]\d{2})?\d{4}(-\d{2}(-\d{2}))|(\d{1,2})\/(\d{1,2})\/(\d{2,4}))([T ]\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/;function ut(e,t){switch(t){case"string":return"string"==typeof e||null==e?e:String(e);case"boolean":if("string"==typeof e){const t=e.trim().toLowerCase();return"true"===t||"false"!==t&&null}return"boolean"==typeof e||null==e?e:Boolean(e);case"bigint":return"bigint"==typeof e||null==e?e:Number.isInteger("string"!=typeof e||e.trim()?+e:NaN)?BigInt(e):void 0;case"integer":case"number":return"number"==typeof e?e:null==e||"string"==typeof e&&!e.trim()?NaN:Number(e);case"date":{if(e instanceof Date||null==e)return e;if("number"==typeof e)return new Date(e);const t=String(e).trim();return"string"!=typeof e||t?new Date(ot.test(t)?t:NaN):null}case"array":case"object":case"buffer":case"other":return e;default:throw new Error(`Unable to coerce to type: ${t}`)}}function ct(e,t){const i=e;let{schema:n,inferred:s}=function(e){const{columns:t}=e;let{schema:i}=e;return _e(i)?{schema:i,inferred:!1}:(i=dt(e,Be(t)?t:void 0),{schema:i,inferred:!0})}(e);const r=new Map(n.map((({name:e,type:t})=>[e,t])));if(t.types){for(const{name:e,type:s}of t.types){r.set(e,s),n===i.schema&&(n=n.slice());const t=n.findIndex((t=>t.name===e));t>-1&&(n[t]={...n[t],type:s})}e=e.map((e=>lt(e,r,n)))}else s&&(e=e.map((e=>lt(e,r,n))));return{source:e,schema:n}}function pt(e,t){if(!t.names)return e;const i=new Map(t.names.map((e=>[e.column,e])));return e.map((e=>Object.fromEntries(Object.keys(e).map((t=>[i.get(t)?.name??t,e[t]])))))}function lt(e,t,i){const n={};for(const s of i){const i=t.get(s.name),r=e[s.name];n[s.name]="raw"===i?r:ut(r,i)}return n}const ht=["boolean","integer","number","date","bigint","array","object","buffer"];function dt(e,t=function(e){const t=new Set;for(const i of e)if(i)for(const e in i)Object.prototype.hasOwnProperty.call(i,e)&&t.add(e);return Array.from(t)}(e)){const i=[],n=e.slice(0,100);for(const e of t){const t={boolean:0,integer:0,number:0,date:0,string:0,array:0,object:0,bigint:0,buffer:0,defined:0};for(const i of n){let n=i[e];if(null==n)continue;const s=typeof n;if("string"!==s)++t.defined,Array.isArray(n)?++t.array:n instanceof Date?++t.date:n instanceof ArrayBuffer?++t.buffer:"number"===s?(++t.number,Number.isInteger(n)&&++t.integer):s in t&&++t[s];else{if(n=n.trim(),!n)continue;++t.defined,++t.string,/^(true|false)$/i.test(n)?++t.boolean:n&&!isNaN(n)?(++t.number,Number.isInteger(+n)&&++t.integer):ot.test(n)&&++t.date}}const s=Math.max(1,.9*t.defined),r=ve(ht,(e=>t[e]>=s?t[e]:NaN))??(t.string>=s?"string":"other");i.push({name:e,type:r,inferred:r})}return i}class ft{constructor(e){Object.defineProperties(this,{_:{value:e},sheetNames:{value:e.worksheets.map((e=>e.name)),enumerable:!0}})}sheet(e,t){const i="number"==typeof e?this.sheetNames[e]:this.sheetNames.includes(e+="")?e:null;if(null==i)throw new Error(`Sheet not found: ${e}`);return function(e,{range:t,headers:i}={}){let[[n,s],[r,a]]=function(e=":",{columnCount:t,rowCount:i}){if(!(e+="").match(/^[A-Z]*\d*:[A-Z]*\d*$/))throw new Error("Malformed range specifier");const[[n=0,s=0],[r=t-1,a=i-1]]=e.split(":").map(xt);return[[n,s],[r,a]]}(t,e);const o=i?e._rows[s++]:null;let u=new Set(["#"]);for(let e=n;e<=r;e++){const t=o?mt(o.findCell(e+1)):null;let i=t&&t+""||vt(e);for(;u.has(i);)i+="_";u.add(i)}u=new Array(n).concat(Array.from(u));const c=new Array(a-s+1);for(let t=s;t<=a;t++){const i=c[t-s]=Object.create(null,{"#":{value:t+1}}),a=e.getRow(t+1);if(a.hasValues)for(let e=n;e<=r;e++){const t=mt(a.findCell(e+1));null!=t&&(i[u[e+1]]=t)}}return c.columns=u.filter((()=>!0)),c}(this._.getWorksheet(i),t)}}function mt(e){if(!e)return;const{value:t}=e;if(t&&"object"==typeof t&&!(t instanceof Date)){if(t.formula||t.sharedFormula)return t.result&&t.result.error?NaN:t.result;if(t.richText)return gt(t);if(t.text){let{text:e}=t;return e.richText&&(e=gt(e)),t.hyperlink&&t.hyperlink!==e?`${t.hyperlink} ${e}`:e}return t}return t}function gt(e){return e.richText.map((e=>e.text)).join("")}function vt(e){let t="";e++;do{t=String.fromCharCode(64+(e%26||26))+t}while(e=Math.floor((e-1)/26));return t}function xt(e){const[,t,i]=e.match(/^([A-Z]*)(\d*)$/);let n=0;if(t)for(let e=0;e<t.length;e++)n+=Math.pow(26,t.length-e-1)*(t.charCodeAt(e)-64);return[n?n-1:void 0,i?+i-1:void 0]}async function yt(e){const t=await fetch(await e.url());if(!t.ok)throw new Error(`Unable to load file: ${e.name}`);return t}async function bt(e,t,{array:i=!1,typed:n=!1}={}){const s=await e.text(),r="\t"===t?i?h:l:i?c:u;if("auto"===n&&!i){const e=r(s);return function(e,t){const i=new Map(t.map((({name:e,type:t})=>[e,t])));return Object.assign(e.map((e=>lt(e,i,t))),{schema:t})}(e,dt(e,e.columns))}return r(s,n&&d)}class Et{constructor(e,t){Object.defineProperty(this,"name",{value:e,enumerable:!0}),void 0!==t&&Object.defineProperty(this,"mimeType",{value:t+"",enumerable:!0})}async blob(){return(await yt(this)).blob()}async arrayBuffer(){return(await yt(this)).arrayBuffer()}async text(){return(await yt(this)).text()}async json(){return(await yt(this)).json()}async stream(){return(await yt(this)).body}async csv(e){return bt(this,",",e)}async tsv(e){return bt(this,"\t",e)}async image(e){const t=await this.url();return new Promise(((i,n)=>{const s=new Image;new URL(t,document.baseURI).origin!==new URL(location).origin&&(s.crossOrigin="anonymous"),Object.assign(s,e),s.onload=()=>i(s),s.onerror=()=>n(new Error(`Unable to load file: ${this.name}`)),s.src=t}))}async arrow({version:e=4}={}){switch(e){case 4:{const[e,t]=await Promise.all([ae(B.resolve()),yt(this)]);return e.Table.from(t)}case 9:{const[e,t]=await Promise.all([import(`${se}${I.resolve()}`),yt(this)]);return e.tableFromIPC(t)}case 11:{const[e,t]=await Promise.all([import(`${se}${P.resolve()}`),yt(this)]);return e.tableFromIPC(t)}default:throw new Error(`unsupported arrow version: ${e}`)}}async sqlite(){return ue.open(yt(this))}async zip(){const[e,t]=await Promise.all([ae(A.resolve()),this.arrayBuffer()]);return new wt(await e.loadAsync(t))}async xml(e="application/xml"){return(new DOMParser).parseFromString(await this.text(),e)}async html(){return this.xml("text/html")}async xlsx(){const[e,t]=await Promise.all([ae(L.resolve()),this.arrayBuffer()]);return new ft(await(new e.Workbook).xlsx.load(t))}}class Ct extends Et{constructor(e,t,i){super(t,i),Object.defineProperty(this,"_url",{value:e})}async url(){return await this._url+""}}function Dt(e){throw new Error(`File not found: ${e}`)}function At(e){return Object.assign((t=>{const i=e(t+="");if(null==i)throw new Error(`File not found: ${t}`);if("object"==typeof i&&"url"in i){const{url:e,mimeType:n}=i;return new Ct(e,t,n)}return new Ct(i,t)}),{prototype:Ct.prototype})}class wt{constructor(e){Object.defineProperty(this,"_",{value:e}),this.filenames=Object.keys(e.files).filter((t=>!e.files[t].dir))}file(e){const t=this._.file(e+="");if(!t||t.dir)throw new Error(`file not found: ${e}`);return new Ft(t)}}class Ft extends Et{constructor(e){super(e.name),Object.defineProperty(this,"_",{value:e}),Object.defineProperty(this,"_url",{writable:!0})}async url(){return this._url||(this._url=this.blob().then(URL.createObjectURL))}async blob(){return this._.async("blob")}async arrayBuffer(){return this._.async("arraybuffer")}async text(){return this._.async("text")}async json(){return JSON.parse(await this.text())}}var kt={math:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};var St=0;function _t(e){return new Bt("O-"+(null==e?"":e+"-")+ ++St)}function Bt(e){this.id=e,this.href=new URL(`#${e}`,location)+""}Bt.prototype.toString=function(){return"url("+this.href+")"};var It=Object.freeze({__proto__:null,canvas:function(e,t){var i=document.createElement("canvas");return i.width=e,i.height=t,i},context2d:function(e,t,i){null==i&&(i=devicePixelRatio);var n=document.createElement("canvas");n.width=e*i,n.height=t*i,n.style.width=e+"px";var s=n.getContext("2d");return s.scale(i,i),s},download:function(e,t="untitled",i="Save"){const n=document.createElement("a"),s=n.appendChild(document.createElement("button"));async function r(){await new Promise(requestAnimationFrame),URL.revokeObjectURL(n.href),n.removeAttribute("href"),s.textContent=i,s.disabled=!1}return s.textContent=i,n.download=t,n.onclick=async t=>{if(s.disabled=!0,n.href)return r();s.textContent="Saving…";try{const t=await("function"==typeof e?e():e);s.textContent="Download",n.href=URL.createObjectURL(t)}catch(e){s.textContent=i}if(t.eventPhase)return r();s.disabled=!1},n},element:function(e,t){var i,n=e+="",s=n.indexOf(":");s>=0&&"xmlns"!==(n=e.slice(0,s))&&(e=e.slice(s+1));var r=kt.hasOwnProperty(n)?document.createElementNS(kt[n],e):document.createElement(e);if(t)for(var a in t)s=(n=a).indexOf(":"),i=t[a],s>=0&&"xmlns"!==(n=a.slice(0,s))&&(a=a.slice(s+1)),kt.hasOwnProperty(n)?r.setAttributeNS(kt[n],a,i):r.setAttribute(a,i);return r},input:function(e){var t=document.createElement("input");return null!=e&&(t.type=e),t},range:function(e,t,i){1===arguments.length&&(t=e,e=null);var n=document.createElement("input");return n.min=e=null==e?0:+e,n.max=t=null==t?1:+t,n.step=null==i?"any":i=+i,n.type="range",n},select:function(e){var t=document.createElement("select");return Array.prototype.forEach.call(e,(function(e){var i=document.createElement("option");i.value=i.textContent=e,t.appendChild(i)})),t},svg:function(e,t){var i=document.createElementNS("http://www.w3.org/2000/svg","svg");return i.setAttribute("viewBox",[0,0,e,t]),i.setAttribute("width",e),i.setAttribute("height",t),i},text:function(e){return document.createTextNode(e)},uid:_t});var Pt=Object.freeze({__proto__:null,buffer:function(e){return new Promise((function(t,i){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=i,n.readAsArrayBuffer(e)}))},text:function(e){return new Promise((function(t,i){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=i,n.readAsText(e)}))},url:function(e){return new Promise((function(t,i){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=i,n.readAsDataURL(e)}))}});function Nt(){return this}function Tt(e,t){let i=!1;if("function"!=typeof t)throw new Error("dispose is not a function");return{[Symbol.iterator]:Nt,next:()=>i?{done:!0}:(i=!0,{done:!1,value:e}),return:()=>(i=!0,t(e),{done:!0}),throw:()=>({done:i=!0})}}function Lt(e){let t,i,n=!1;const s=e((function(e){i?(i(e),i=null):n=!0;return t=e}));if(null!=s&&"function"!=typeof s)throw new Error("function"==typeof s.then?"async initializers are not supported":"initializer returned something, but not a dispose function");return{[Symbol.iterator]:Nt,throw:()=>({done:!0}),return:()=>(null!=s&&s(),{done:!0}),next:function(){return{done:!1,value:n?(n=!1,Promise.resolve(t)):new Promise((e=>i=e))}}}}function Vt(e){switch(e.type){case"range":case"number":return e.valueAsNumber;case"date":return e.valueAsDate;case"checkbox":return e.checked;case"file":return e.multiple?e.files:e.files[0];case"select-multiple":return Array.from(e.selectedOptions,(e=>e.value));default:return e.value}}var Rt=Object.freeze({__proto__:null,disposable:Tt,filter:function*(e,t){for(var i,n=-1;!(i=e.next()).done;)t(i.value,++n)&&(yield i.value)},input:function(e){return Lt((function(t){var i=function(e){switch(e.type){case"button":case"submit":case"checkbox":return"click";case"file":return"change";default:return"input"}}(e),n=Vt(e);function s(){t(Vt(e))}return e.addEventListener(i,s),void 0!==n&&t(n),function(){e.removeEventListener(i,s)}}))},map:function*(e,t){for(var i,n=-1;!(i=e.next()).done;)yield t(i.value,++n)},observe:Lt,queue:function(e){let t;const i=[],n=e((function(e){i.push(e),t&&(t(i.shift()),t=null);return e}));if(null!=n&&"function"!=typeof n)throw new Error("function"==typeof n.then?"async initializers are not supported":"initializer returned something, but not a dispose function");return{[Symbol.iterator]:Nt,throw:()=>({done:!0}),return:()=>(null!=n&&n(),{done:!0}),next:function(){return{done:!1,value:i.length?Promise.resolve(i.shift()):new Promise((e=>t=e))}}}},range:function*(e,t,i){e=+e,t=+t,i=(s=arguments.length)<2?(t=e,e=0,1):s<3?1:+i;for(var n=-1,s=0|Math.max(0,Math.ceil((t-e)/i));++n<s;)yield e+n*i},valueAt:function(e,t){if(!(!isFinite(t=+t)||t<0||t!=t|0))for(var i,n=-1;!(i=e.next()).done;)if(++n===t)return i.value},worker:function(e){const t=URL.createObjectURL(new Blob([e],{type:"text/javascript"})),i=new Worker(t);return Tt(i,(()=>{i.terminate(),URL.revokeObjectURL(t)}))}});function Ot(e,t){return function(i){var n,s,r,a,o,u,c,p,l=i[0],h=[],d=null,f=-1;for(o=1,u=arguments.length;o<u;++o){if((n=arguments[o])instanceof Node)h[++f]=n,l+="\x3c!--o:"+f+"--\x3e";else if(Array.isArray(n)){for(c=0,p=n.length;c<p;++c)(s=n[c])instanceof Node?(null===d&&(h[++f]=d=document.createDocumentFragment(),l+="\x3c!--o:"+f+"--\x3e"),d.appendChild(s)):(d=null,l+=s);d=null}else l+=n;l+=i[o]}if(d=e(l),++f>0){for(r=new Array(f),a=document.createTreeWalker(d,NodeFilter.SHOW_COMMENT,null,!1);a.nextNode();)s=a.currentNode,/^o:/.test(s.nodeValue)&&(r[+s.nodeValue.slice(2)]=s);for(o=0;o<f;++o)(s=r[o])&&s.parentNode.replaceChild(h[o],s)}return 1===d.childNodes.length?d.removeChild(d.firstChild):11===d.nodeType?((s=t()).appendChild(d),s):d}}const Mt=Ot((function(e){var t=document.createElement("template");return t.innerHTML=e.trim(),document.importNode(t.content,!0)}),(function(){return document.createElement("span")}));function jt(e){let t;Object.defineProperties(this,{generator:{value:Lt((e=>{t=e}))},value:{get:()=>e,set:i=>t(e=i)}}),void 0!==e&&t(e)}function*qt(){for(;;)yield Date.now()}var $t=new Map;function Ut(e,t){var i;return(i=$t.get(e=+e))?i.then((()=>t)):(i=Date.now())>=e?Promise.resolve(t):function(e,t){var i=new Promise((function(i){$t.delete(t);var n=t-e;if(!(n>0))throw new Error("invalid time");if(n>2147483647)throw new Error("too long to wait");setTimeout(i,n)}));return $t.set(t,i),i}(i,e).then((()=>t))}var zt=Object.freeze({__proto__:null,delay:function(e,t){return new Promise((function(i){setTimeout((function(){i(t)}),e)}))},tick:function(e,t){return Ut(Math.ceil((Date.now()+1)/e)*e,t)},when:Ut});function Wt(e,t){if(/^(\w+:)|\/\//i.test(e))return e;if(/^[.]{0,2}\//i.test(e))return new URL(e,null==t?location:t).href;if(!e.length||/^[\s._]/.test(e)||/\s$/.test(e))throw new Error("illegal name");return"https://unpkg.com/"+e}const Ht=Ot((function(e){var t=document.createElementNS("http://www.w3.org/2000/svg","g");return t.innerHTML=e.trim(),t}),(function(){return document.createElementNS("http://www.w3.org/2000/svg","g")}));var Gt=String.raw;function Qt(e){return new Promise((function(t,i){var n=document.createElement("link");n.rel="stylesheet",n.href=e,n.onerror=i,n.onload=t,document.head.appendChild(n)}))}function Kt(){return Lt((function(e){var t=e(document.body.clientWidth);function i(){var i=document.body.clientWidth;i!==t&&e(t=i)}return window.addEventListener("resize",i),function(){window.removeEventListener("resize",i)}}))}const Yt=Object.assign(Object.defineProperties((function(e){const t=function(e){return null==e?ae:Z(e)}(e);var i;Object.defineProperties(this,(i={FileAttachment:()=>Dt,Mutable:()=>jt,now:qt,width:Kt,dot:()=>t(y.resolve()),htl:()=>t(D.resolve()),html:()=>Mt,md:()=>function(e){return e(w.resolve()).then((function(t){return Ot((function(i){var n=document.createElement("div");n.innerHTML=t(i,{langPrefix:""}).trim();var s=n.querySelectorAll("pre code[class]");return s.length>0&&e(b.resolve()).then((function(t){s.forEach((function(i){function n(){t.highlightBlock(i),i.parentNode.classList.add("observablehq--md-pre")}t.getLanguage(i.className)?n():e(b.resolve("async-languages/index.js")).then((n=>{if(n.has(i.className))return e(b.resolve("async-languages/"+n.get(i.className))).then((e=>{t.registerLanguage(i.className,e)}))})).then(n,n)}))})),n}),(function(){return document.createElement("div")}))}))}(t),svg:()=>Ht,tex:()=>function(e){return Promise.all([e(E.resolve()),e.resolve(E.resolve("dist/katex.min.css")).then(Qt)]).then((function(e){var t=e[0],i=n();function n(e){return function(){var i=document.createElement("div");return t.render(Gt.apply(String,arguments),i,e),i.removeChild(i.firstChild)}}return i.options=n,i.block=n({displayMode:!0}),i}))}(t),_:()=>t(C.resolve()),aq:()=>t.alias({"apache-arrow":B.resolve()})(N.resolve()),Arrow:()=>t(B.resolve()),d3:()=>t(g.resolve()),DuckDBClient:()=>De,Inputs:()=>t(v.resolve()).then((e=>({...e,file:e.fileOf(Et)}))),L:()=>async function(e){const t=await e(R.resolve());if(!t._style){const i=document.createElement("link");i.rel="stylesheet",i.href=await e.resolve(R.resolve("dist/leaflet.css")),t._style=document.head.appendChild(i)}return t}(t),mermaid:()=>async function(e){const t=await e(V.resolve());return t.initialize({securityLevel:"loose",theme:"neutral"}),function(){const e=document.createElement("div");return e.innerHTML=t.render(_t().id,String.raw.apply(String,arguments)),e.removeChild(e.firstChild)}}(t),Plot:()=>t(x.resolve()),__query:()=>Ve,require:()=>t,resolve:()=>Wt,SQLite:()=>oe(t),SQLiteDatabaseClient:()=>ue,topojson:()=>t(T.resolve()),vl:()=>async function(e){const[t,i,n]=await Promise.all([k,S,_].map((t=>e(t.resolve()))));return n.register(t,i)}(t),aapl:()=>new Ct("https://static.observableusercontent.com/files/3ccff97fd2d93da734e76829b2b066eafdaac6a1fafdec0faf6ebc443271cfc109d29e80dd217468fcb2aff1e6bffdc73f356cc48feb657f35378e6abbbb63b9").csv({typed:!0}),alphabet:()=>new Ct("https://static.observableusercontent.com/files/75d52e6c3130b1cae83cda89305e17b50f33e7420ef205587a135e8562bcfd22e483cf4fa2fb5df6dff66f9c5d19740be1cfaf47406286e2eb6574b49ffc685d").csv({typed:!0}),cars:()=>new Ct("https://static.observableusercontent.com/files/048ec3dfd528110c0665dfa363dd28bc516ffb7247231f3ab25005036717f5c4c232a5efc7bb74bc03037155cb72b1abe85a33d86eb9f1a336196030443be4f6").csv({typed:!0}),citywages:()=>new Ct("https://static.observableusercontent.com/files/39837ec5121fcc163131dbc2fe8c1a2e0b3423a5d1e96b5ce371e2ac2e20a290d78b71a4fb08b9fa6a0107776e17fb78af313b8ea70f4cc6648fad68ddf06f7a").csv({typed:!0}),diamonds:()=>new Ct("https://static.observableusercontent.com/files/87942b1f5d061a21fa4bb8f2162db44e3ef0f7391301f867ab5ba718b225a63091af20675f0bfe7f922db097b217b377135203a7eab34651e21a8d09f4e37252").csv({typed:!0}),flare:()=>new Ct("https://static.observableusercontent.com/files/a6b0d94a7f5828fd133765a934f4c9746d2010e2f342d335923991f31b14120de96b5cb4f160d509d8dc627f0107d7f5b5070d2516f01e4c862b5b4867533000").csv({typed:!0}),industries:()=>new Ct("https://static.observableusercontent.com/files/76f13741128340cc88798c0a0b7fa5a2df8370f57554000774ab8ee9ae785ffa2903010cad670d4939af3e9c17e5e18e7e05ed2b38b848ac2fc1a0066aa0005f").csv({typed:!0}),miserables:()=>new Ct("https://static.observableusercontent.com/files/31d904f6e21d42d4963ece9c8cc4fbd75efcbdc404bf511bc79906f0a1be68b5a01e935f65123670ed04e35ca8cae3c2b943f82bf8db49c5a67c85cbb58db052").json(),olympians:()=>new Ct("https://static.observableusercontent.com/files/31ca24545a0603dce099d10ee89ee5ae72d29fa55e8fc7c9ffb5ded87ac83060d80f1d9e21f4ae8eb04c1e8940b7287d179fe8060d887fb1f055f430e210007c").csv({typed:!0}),penguins:()=>new Ct("https://static.observableusercontent.com/files/715db1223e067f00500780077febc6cebbdd90c151d3d78317c802732252052ab0e367039872ab9c77d6ef99e5f55a0724b35ddc898a1c99cb14c31a379af80a").csv({typed:!0}),pizza:()=>new Ct("https://static.observableusercontent.com/files/c653108ab176088cacbb338eaf2344c4f5781681702bd6afb55697a3f91b511c6686ff469f3e3a27c75400001a2334dbd39a4499fe46b50a8b3c278b7d2f7fb5").csv({typed:!0}),weather:()=>new Ct("https://static.observableusercontent.com/files/693a46b22b33db0f042728700e0c73e836fa13d55446df89120682d55339c6db7cc9e574d3d73f24ecc9bc7eb9ac9a1e7e104a1ee52c00aab1e77eb102913c1f").csv({typed:!0}),DOM:It,Files:Pt,Generators:Rt,Promises:zt},Object.fromEntries(Object.entries(i).map(Jt))))}),{resolve:{get:()=>ae.resolve,enumerable:!0,configurable:!0},require:{get:()=>ae,set:function(e){ae=e},enumerable:!0,configurable:!0}}),{resolveFrom:K,requireFrom:Z});function Jt([e,t]){return[e,{value:t,writable:!0,enumerable:!0}]}class Xt{constructor(e){this._node=e,this._spans=[],this.normalizeCodeRange(),this.initializeEntryPoints()}normalizeCodeRange(){const e=this._node.querySelectorAll("code > span");for(const t of e)Array.from(t.childNodes).filter((e=>e.nodeType===e.TEXT_NODE)).forEach((e=>{const t=document.createElement("span");t.textContent=e.wholeText,e.replaceWith(t)}))}initializeEntryPoints(){const e=this._node.querySelectorAll("code > span");let t=[],i=this._node.parentElement.dataset.sourceOffset&&-Number(this._node.parentElement.dataset.sourceOffset)||0;for(const n of e){let e=Number(n.id.split("-").pop()),s=1;Array.from(n.childNodes).filter((e=>e.nodeType===e.ELEMENT_NODE&&"SPAN"===e.nodeName)).forEach((n=>{t.push({offset:i,line:e,column:s,node:n}),i+=n.textContent.length,s+=n.textContent.length})),i+=1}this._elementEntryPoints=t}locateEntry(e){let t;if(e!==1/0){for(let i=0;i<this._elementEntryPoints.length;++i){const n=this._elementEntryPoints[i];if(n.offset>e)return{entry:t,index:i-1};t=n}return e<t.offset+t.node.textContent.length?{entry:t,index:this._elementEntryPoints.length-1}:void 0}}offsetToLineColumn(e){let t=this.locateEntry(e);if(void 0===t){const t=this._elementEntryPoints,i=t[t.length-1];return{line:i.line,column:i.column+Math.min(i.node.textContent.length,e-i.offset)}}return{line:t.entry.line,column:t.entry.column+e-t.entry.offset}}*spanSelection(e,t){this.ensureExactSpan(e,t);const i=this.locateEntry(e),n=this.locateEntry(t);if(void 0===i)return;const s=i.index,r=n&&n.index||this._elementEntryPoints.length;for(let e=s;e<r;++e)void 0!==this._elementEntryPoints[e]&&(yield this._elementEntryPoints[e])}decorateSpan(e,t,i){for(const n of this.spanSelection(e,t))for(const e of i)n.node.classList.add(e)}clearSpan(e,t,i){for(const n of this.spanSelection(e,t))for(const e of i)n.node.classList.remove(e)}ensureExactSpan(e,t){const i=(e,t)=>{const i=document.createElement("span");for(const t of e.node.classList)i.classList.add(t);const n=e.node.textContent.slice(0,t-e.offset),s=e.node.textContent.slice(t-e.offset);e.node.textContent=n,i.textContent=s,e.node.after(i),this._elementEntryPoints.push({column:e.column+t-e.offset,line:e.line,node:i,offset:t}),this._elementEntryPoints.sort(((e,t)=>e.offset-t.offset))},n=this.locateEntry(e);void 0!==n&&void 0!==n.entry&&n.entry.offset!=e&&i(n.entry,e);const s=this.locateEntry(t);void 0!==s&&void 0!==n.entry&&s.entry.offset!==t&&i(s.entry,t)}clearSpan(e,t,i){this.ensureExactSpan(e,t);const n=this.locateEntry(e),s=this.locateEntry(t);if(void 0===n)return;const r=n.index,a=s&&s.index||this._elementEntryPoints.length;for(let e=r;e<a;++e)for(const t of i)this._elementEntryPoints[e].node.classList.remove(t)}}function Zt(e,t,i){i=i||{};var n=e.ownerDocument,s=n.defaultView.CustomEvent;"function"==typeof s?s=new s(t,{detail:i}):((s=n.createEvent("Event")).initEvent(t,!1,!1),s.detail=i),e.dispatchEvent(s)}function ei(e){return Array.isArray(e)||e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array}function ti(e){return e===(0|e)+""}function ii(e){const t=document.createElement("span");return t.className="observablehq--cellname",t.textContent=`${e} = `,t}const ni=Symbol.prototype.toString;function si(e){return ni.call(e)}const{getOwnPropertySymbols:ri,prototype:{hasOwnProperty:ai}}=Object,{toStringTag:oi}=Symbol,ui={},ci=ri;function pi(e,t){return ai.call(e,t)}function li(e){return e[oi]||e.constructor&&e.constructor.name||"Object"}function hi(e,t){try{const i=e[t];return i&&i.constructor,i}catch(e){return ui}}const di=[{symbol:"@@__IMMUTABLE_INDEXED__@@",name:"Indexed",modifier:!0},{symbol:"@@__IMMUTABLE_KEYED__@@",name:"Keyed",modifier:!0},{symbol:"@@__IMMUTABLE_LIST__@@",name:"List",arrayish:!0},{symbol:"@@__IMMUTABLE_MAP__@@",name:"Map"},{symbol:"@@__IMMUTABLE_ORDERED__@@",name:"Ordered",modifier:!0,prefix:!0},{symbol:"@@__IMMUTABLE_RECORD__@@",name:"Record"},{symbol:"@@__IMMUTABLE_SET__@@",name:"Set",arrayish:!0,setish:!0},{symbol:"@@__IMMUTABLE_STACK__@@",name:"Stack",arrayish:!0}];function fi(e){try{let t=di.filter((({symbol:t})=>!0===e[t]));if(!t.length)return;const i=t.find((e=>!e.modifier)),n="Map"===i.name&&t.find((e=>e.modifier&&e.prefix)),s=t.some((e=>e.arrayish)),r=t.some((e=>e.setish));return{name:`${n?n.name:""}${i.name}`,symbols:t,arrayish:s&&!r,setish:r}}catch(e){return null}}const{getPrototypeOf:mi,getOwnPropertyDescriptors:gi}=Object,vi=mi({});function xi(e,t,i,n){let s,r,a,o,u=ei(e);e instanceof Map?e instanceof e.constructor?(s=`Map(${e.size})`,r=yi):(s="Map()",r=wi):e instanceof Set?e instanceof e.constructor?(s=`Set(${e.size})`,r=bi):(s="Set()",r=wi):u?(s=`${e.constructor.name}(${e.length})`,r=Ci):(o=fi(e))?(s=`Immutable.${o.name}${"Record"===o.name?"":`(${e.size})`}`,u=o.arrayish,r=o.arrayish?Di:o.setish?Ei:Fi):n?(s=li(e),r=Ai):(s=li(e),r=wi);const c=document.createElement("span");c.className="observablehq--expanded",i&&c.appendChild(ii(i));const p=c.appendChild(document.createElement("a"));p.innerHTML="<svg width=8 height=8 class='observablehq--caret'>\n    <path d='M4 7L0 1h8z' fill='currentColor' />\n  </svg>",p.appendChild(document.createTextNode(`${s}${u?" [":" {"}`)),p.addEventListener("mouseup",(function(t){t.stopPropagation(),an(c,Pi(e,null,i,n))})),r=r(e);for(let e=0;!(a=r.next()).done&&e<20;++e)c.appendChild(a.value);if(!a.done){const e=c.appendChild(document.createElement("a"));e.className="observablehq--field",e.style.display="block",e.appendChild(document.createTextNode("  … more")),e.addEventListener("mouseup",(function(e){e.stopPropagation(),c.insertBefore(a.value,c.lastChild.previousSibling);for(let e=0;!(a=r.next()).done&&e<19;++e)c.insertBefore(a.value,c.lastChild.previousSibling);a.done&&c.removeChild(c.lastChild.previousSibling),Zt(c,"load")}))}return c.appendChild(document.createTextNode(u?"]":"}")),c}function*yi(e){for(const[t,i]of e)yield _i(t,i);yield*wi(e)}function*bi(e){for(const t of e)yield Bi(t);yield*wi(e)}function*Ei(e){for(const t of e)yield Bi(t)}function*Ci(e){for(let t=0,i=e.length;t<i;++t)t in e&&(yield Si(t,hi(e,t),"observablehq--index"));for(const t in e)!ti(t)&&pi(e,t)&&(yield Si(t,hi(e,t),"observablehq--key"));for(const t of ci(e))yield Si(si(t),hi(e,t),"observablehq--symbol")}function*Di(e){let t=0;for(const i=e.size;t<i;++t)yield Si(t,e.get(t),!0)}function*Ai(e){for(const t in gi(e))yield Si(t,hi(e,t),"observablehq--key");for(const t of ci(e))yield Si(si(t),hi(e,t),"observablehq--symbol");const t=mi(e);t&&t!==vi&&(yield ki(t))}function*wi(e){for(const t in e)pi(e,t)&&(yield Si(t,hi(e,t),"observablehq--key"));for(const t of ci(e))yield Si(si(t),hi(e,t),"observablehq--symbol");const t=mi(e);t&&t!==vi&&(yield ki(t))}function*Fi(e){for(const[t,i]of e)yield Si(t,i,"observablehq--key")}function ki(e){const t=document.createElement("div"),i=t.appendChild(document.createElement("span"));return t.className="observablehq--field",i.className="observablehq--prototype-key",i.textContent="  <prototype>",t.appendChild(document.createTextNode(": ")),t.appendChild(rn(e,void 0,void 0,void 0,!0)),t}function Si(e,t,i){const n=document.createElement("div"),s=n.appendChild(document.createElement("span"));return n.className="observablehq--field",s.className=i,s.textContent=`  ${e}`,n.appendChild(document.createTextNode(": ")),n.appendChild(rn(t)),n}function _i(e,t){const i=document.createElement("div");return i.className="observablehq--field",i.appendChild(document.createTextNode("  ")),i.appendChild(rn(e)),i.appendChild(document.createTextNode(" => ")),i.appendChild(rn(t)),i}function Bi(e){const t=document.createElement("div");return t.className="observablehq--field",t.appendChild(document.createTextNode("  ")),t.appendChild(rn(e)),t}function Ii(e){const t=window.getSelection();return"Range"===t.type&&(t.containsNode(e,!0)||t.anchorNode.isSelfOrDescendant(e)||t.focusNode.isSelfOrDescendant(e))}function Pi(e,t,i,n){let s,r,a,o,u=ei(e);if(e instanceof Map?e instanceof e.constructor?(s=`Map(${e.size})`,r=Ni):(s="Map()",r=Oi):e instanceof Set?e instanceof e.constructor?(s=`Set(${e.size})`,r=Ti):(s="Set()",r=Oi):u?(s=`${e.constructor.name}(${e.length})`,r=Ri):(o=fi(e))?(s=`Immutable.${o.name}${"Record"===o.name?"":`(${e.size})`}`,u=o.arrayish,r=o.arrayish?Vi:o.setish?Li:Mi):(s=li(e),r=Oi),t){const t=document.createElement("span");return t.className="observablehq--shallow",i&&t.appendChild(ii(i)),t.appendChild(document.createTextNode(s)),t.addEventListener("mouseup",(function(i){Ii(t)||(i.stopPropagation(),an(t,Pi(e)))})),t}const c=document.createElement("span");c.className="observablehq--collapsed",i&&c.appendChild(ii(i));const p=c.appendChild(document.createElement("a"));p.innerHTML="<svg width=8 height=8 class='observablehq--caret'>\n    <path d='M7 4L1 8V0z' fill='currentColor' />\n  </svg>",p.appendChild(document.createTextNode(`${s}${u?" [":" {"}`)),c.addEventListener("mouseup",(function(t){Ii(c)||(t.stopPropagation(),an(c,xi(e,0,i,n)))}),!0),r=r(e);for(let e=0;!(a=r.next()).done&&e<20;++e)e>0&&c.appendChild(document.createTextNode(", ")),c.appendChild(a.value);return a.done||c.appendChild(document.createTextNode(", …")),c.appendChild(document.createTextNode(u?"]":"}")),c}function*Ni(e){for(const[t,i]of e)yield $i(t,i);yield*Oi(e)}function*Ti(e){for(const t of e)yield rn(t,!0);yield*Oi(e)}function*Li(e){for(const t of e)yield rn(t,!0)}function*Vi(e){let t=-1,i=0;for(const n=e.size;i<n;++i)i>t+1&&(yield ji(i-t-1)),yield rn(e.get(i),!0),t=i;i>t+1&&(yield ji(i-t-1))}function*Ri(e){let t=-1,i=0;for(const n=e.length;i<n;++i)i in e&&(i>t+1&&(yield ji(i-t-1)),yield rn(hi(e,i),!0),t=i);i>t+1&&(yield ji(i-t-1));for(const t in e)!ti(t)&&pi(e,t)&&(yield qi(t,hi(e,t),"observablehq--key"));for(const t of ci(e))yield qi(si(t),hi(e,t),"observablehq--symbol")}function*Oi(e){for(const t in e)pi(e,t)&&(yield qi(t,hi(e,t),"observablehq--key"));for(const t of ci(e))yield qi(si(t),hi(e,t),"observablehq--symbol")}function*Mi(e){for(const[t,i]of e)yield qi(t,i,"observablehq--key")}function ji(e){const t=document.createElement("span");return t.className="observablehq--empty",t.textContent=1===e?"empty":`empty × ${e}`,t}function qi(e,t,i){const n=document.createDocumentFragment(),s=n.appendChild(document.createElement("span"));return s.className=i,s.textContent=e,n.appendChild(document.createTextNode(": ")),n.appendChild(rn(t,!0)),n}function $i(e,t){const i=document.createDocumentFragment();return i.appendChild(rn(e,!0)),i.appendChild(document.createTextNode(" => ")),i.appendChild(rn(t,!0)),i}function Ui(e,t){if(e instanceof Date||(e=new Date(+e)),isNaN(e))return"function"==typeof t?t(e):t;const i=e.getUTCHours(),n=e.getUTCMinutes(),s=e.getUTCSeconds(),r=e.getUTCMilliseconds();return`${a=e.getUTCFullYear(),a<0?`-${zi(-a,6)}`:a>9999?`+${zi(a,6)}`:zi(a,4)}-${zi(e.getUTCMonth()+1,2)}-${zi(e.getUTCDate(),2)}${i||n||s||r?`T${zi(i,2)}:${zi(n,2)}${s||r?`:${zi(s,2)}${r?`.${zi(r,3)}`:""}`:""}Z`:""}`;var a}function zi(e,t){return`${e}`.padStart(t,"0")}var Wi=Error.prototype.toString;var Hi=RegExp.prototype.toString;function Gi(e){return e.replace(/[\\`\x00-\x09\x0b-\x19]|\${/g,Qi)}function Qi(e){var t=e.charCodeAt(0);switch(t){case 8:return"\\b";case 9:return"\\t";case 11:return"\\v";case 12:return"\\f";case 13:return"\\r"}return t<16?"\\x0"+t.toString(16):t<32?"\\x"+t.toString(16):"\\"+e}function Ki(e,t){for(var i=0;t.exec(e);)++i;return i}var Yi=Function.prototype.toString,Ji={prefix:"async ƒ"},Xi={prefix:"async ƒ*"},Zi={prefix:"class"},en={prefix:"ƒ"},tn={prefix:"ƒ*"};function nn(e,t,i){var n=document.createElement("span");n.className="observablehq--function",i&&n.appendChild(ii(i));var s=n.appendChild(document.createElement("span"));return s.className="observablehq--keyword",s.textContent=e.prefix,n.appendChild(document.createTextNode(t)),n}const{prototype:{toString:sn}}=Object;function rn(e,t,i,n,s){let r=typeof e;switch(r){case"boolean":case"undefined":e+="";break;case"number":e=0===e&&1/e<0?"-0":e+"";break;case"bigint":e+="n";break;case"symbol":e=si(e);break;case"function":return function(e,t){var i,n,s=Yi.call(e);switch(e.constructor&&e.constructor.name){case"AsyncFunction":i=Ji;break;case"AsyncGeneratorFunction":i=Xi;break;case"GeneratorFunction":i=tn;break;default:i=/^class\b/.test(s)?Zi:en}return i===Zi?nn(i,"",t):(n=/^(?:async\s*)?(\w+)\s*=>/.exec(s))?nn(i,"("+n[1]+")",t):(n=/^(?:async\s*)?\(\s*(\w+(?:\s*,\s*\w+)*)?\s*\)/.exec(s))||(n=/^(?:async\s*)?function(?:\s*\*)?(?:\s*\w+)?\s*\(\s*(\w+(?:\s*,\s*\w+)*)?\s*\)/.exec(s))?nn(i,n[1]?"("+n[1].replace(/\s*,\s*/g,", ")+")":"()",t):nn(i,"(…)",t)}(e,n);case"string":return function(e,t,i,n){if(!1===t){if(Ki(e,/["\n]/g)<=Ki(e,/`|\${/g)){const t=document.createElement("span");n&&t.appendChild(ii(n));const i=t.appendChild(document.createElement("span"));return i.className="observablehq--string",i.textContent=JSON.stringify(e),t}const s=e.split("\n");if(s.length>20&&!i){const i=document.createElement("div");n&&i.appendChild(ii(n));const r=i.appendChild(document.createElement("span"));r.className="observablehq--string",r.textContent="`"+Gi(s.slice(0,20).join("\n"));const a=i.appendChild(document.createElement("span")),o=s.length-20;return a.textContent=`Show ${o} truncated line${o>1?"s":""}`,a.className="observablehq--string-expand",a.addEventListener("mouseup",(function(s){s.stopPropagation(),an(i,rn(e,t,!0,n))})),i}const r=document.createElement("span");n&&r.appendChild(ii(n));const a=r.appendChild(document.createElement("span"));return a.className="observablehq--string"+(i?" observablehq--expanded":""),a.textContent="`"+Gi(e)+"`",r}const s=document.createElement("span");n&&s.appendChild(ii(n));const r=s.appendChild(document.createElement("span"));return r.className="observablehq--string",r.textContent=JSON.stringify(e.length>100?`${e.slice(0,50)}…${e.slice(-49)}`:e),s}(e,t,i,n);default:if(null===e){r=null,e="null";break}if(e instanceof Date){r="date",e=Ui(e,"Invalid Date");break}if(e===ui){r="forbidden",e="[forbidden]";break}switch(sn.call(e)){case"[object RegExp]":r="regexp",e=function(e){return Hi.call(e)}(e);break;case"[object Error]":case"[object DOMException]":r="error",e=function(e){return e.stack||Wi.call(e)}(e);break;default:return(i?xi:Pi)(e,t,n,s)}}const a=document.createElement("span");n&&a.appendChild(ii(n));const o=a.appendChild(document.createElement("span"));return o.className=`observablehq--${r}`,o.textContent=e,a}function an(e,t){e.classList.contains("observablehq--inspect")&&t.classList.add("observablehq--inspect"),e.parentNode.replaceChild(t,e),Zt(t,"load")}const on=/\s+\(\d+:\d+\)$/m;class un{constructor(e){if(!e)throw new Error("invalid node");this._node=e,e.classList.add("observablehq")}pending(){const{_node:e}=this;e.classList.remove("observablehq--error"),e.classList.add("observablehq--running")}fulfilled(e,t){const{_node:i}=this;if((!function(e){return(e instanceof Element||e instanceof Text)&&e instanceof e.constructor}(e)||e.parentNode&&e.parentNode!==i)&&(e=rn(e,!1,i.firstChild&&i.firstChild.classList&&i.firstChild.classList.contains("observablehq--expanded"),t)).classList.add("observablehq--inspect"),i.classList.remove("observablehq--running","observablehq--error"),i.firstChild!==e)if(i.firstChild){for(;i.lastChild!==i.firstChild;)i.removeChild(i.lastChild);i.replaceChild(e,i.firstChild)}else i.appendChild(e);Zt(i,"update")}rejected(e,t){const{_node:i}=this;for(i.classList.remove("observablehq--running"),i.classList.add("observablehq--error");i.lastChild;)i.removeChild(i.lastChild);var n=document.createElement("div");n.className="observablehq--inspect",t&&n.appendChild(ii(t)),n.appendChild(document.createTextNode((e+"").replace(on,""))),i.appendChild(n),Zt(i,"error",{error:e})}}un.into=function(e){if("string"==typeof e&&null==(e=document.querySelector(e)))throw new Error("container not found");return function(){return new un(e.appendChild(document.createElement("div")))}};class cn extends Error{constructor(e,t){super(e),this.input=t}}function pn(e){return()=>e}function ln(e){return e}cn.prototype.name="RuntimeError";const hn=Array.prototype.map;function dn(){}const fn=Symbol("no-observer");function mn(e,t,i,n){var s;i||(i=fn),Object.defineProperties(this,{_observer:{value:i,writable:!0},_definition:{value:yn,writable:!0},_duplicate:{value:void 0,writable:!0},_duplicates:{value:void 0,writable:!0},_indegree:{value:NaN,writable:!0},_inputs:{value:[],writable:!0},_invalidate:{value:dn,writable:!0},_module:{value:t},_name:{value:null,writable:!0},_outputs:{value:new Set,writable:!0},_promise:{value:Promise.resolve(void 0),writable:!0},_reachable:{value:i!==fn,writable:!0},_rejector:{value:(s=this,e=>{if(e===bn)throw e;if(e===yn)throw new cn(`${s._name} is not defined`,s._name);if(e instanceof Error&&e.message)throw new cn(e.message,s._name);throw new cn(`${s._name} could not be resolved`,s._name)})},_shadow:{value:gn(t,n)},_type:{value:e},_value:{value:void 0,writable:!0},_version:{value:0,writable:!0}})}function gn(e,t){return t?.shadow?new Map(Object.entries(t.shadow).map((([t,i])=>[t,new mn(2,e).define([],i)]))):null}function vn(e){e._module._runtime._dirty.add(e),e._outputs.add(this)}function xn(e){e._module._runtime._dirty.add(e),e._outputs.delete(this)}function yn(){throw yn}function bn(){throw bn}function En(e){return()=>{throw new cn(`${e} is defined more than once`)}}function Cn(e,t,i){const n=this._module._scope,s=this._module._runtime;if(this._inputs.forEach(xn,this),t.forEach(vn,this),this._inputs=t,this._definition=i,this._value=void 0,i===dn?s._variables.delete(this):s._variables.add(this),e!==this._name||n.get(e)!==this){let t,r;if(this._name)if(this._outputs.size)n.delete(this._name),r=this._module._resolve(this._name),r._outputs=this._outputs,this._outputs=new Set,r._outputs.forEach((function(e){e._inputs[e._inputs.indexOf(this)]=r}),this),r._outputs.forEach(s._updates.add,s._updates),s._dirty.add(r).add(this),n.set(this._name,r);else if((r=n.get(this._name))===this)n.delete(this._name);else{if(3!==r._type)throw new Error;r._duplicates.delete(this),this._duplicate=void 0,1===r._duplicates.size&&(r=r._duplicates.keys().next().value,t=n.get(this._name),r._outputs=t._outputs,t._outputs=new Set,r._outputs.forEach((function(e){e._inputs[e._inputs.indexOf(t)]=r})),r._definition=r._duplicate,r._duplicate=void 0,s._dirty.add(t).add(r),s._updates.add(r),n.set(this._name,r))}if(this._outputs.size)throw new Error;e&&((r=n.get(e))?3===r._type?(this._definition=En(e),this._duplicate=i,r._duplicates.add(this)):2===r._type?(this._outputs=r._outputs,r._outputs=new Set,this._outputs.forEach((function(e){e._inputs[e._inputs.indexOf(r)]=this}),this),s._dirty.add(r).add(this),n.set(e,this)):(r._duplicate=r._definition,this._duplicate=i,t=new mn(3,this._module),t._name=e,t._definition=this._definition=r._definition=En(e),t._outputs=r._outputs,r._outputs=new Set,t._outputs.forEach((function(e){e._inputs[e._inputs.indexOf(r)]=t})),t._duplicates=new Set([this,r]),s._dirty.add(r).add(t),s._updates.add(r).add(t),n.set(e,t)):n.set(e,this)),this._name=e}return this._version>0&&++this._version,s._updates.add(this),s._compute(),this}Object.defineProperties(mn.prototype,{_pending:{value:function(){this._observer.pending&&this._observer.pending()},writable:!0,configurable:!0},_fulfilled:{value:function(e){this._observer.fulfilled&&this._observer.fulfilled(e,this._name)},writable:!0,configurable:!0},_rejected:{value:function(e){this._observer.rejected&&this._observer.rejected(e,this._name)},writable:!0,configurable:!0},_resolve:{value:function(e){return this._shadow?.get(e)??this._module._resolve(e)},writable:!0,configurable:!0},define:{value:function(e,t,i){switch(arguments.length){case 1:i=e,e=t=null;break;case 2:i=t,"string"==typeof e?t=null:(t=e,e=null)}return Cn.call(this,null==e?null:String(e),null==t?[]:hn.call(t,this._resolve,this),"function"==typeof i?i:pn(i))},writable:!0,configurable:!0},delete:{value:function(){return Cn.call(this,null,[],dn)},writable:!0,configurable:!0},import:{value:function(e,t,i){arguments.length<3&&(i=t,t=e);return Cn.call(this,String(t),[i._resolve(String(e))],ln)},writable:!0,configurable:!0}});const Dn=Symbol("variable"),An=Symbol("invalidation"),wn=Symbol("visibility");function Fn(e,t=[]){Object.defineProperties(this,{_runtime:{value:e},_scope:{value:new Map},_builtins:{value:new Map([["@variable",Dn],["invalidation",An],["visibility",wn],...t])},_source:{value:null,writable:!0}})}async function kn(e,t){await e._compute();try{return await t._promise}catch(i){if(i===bn)return kn(e,t);throw i}}function Sn(e){return e._name}Object.defineProperties(Fn.prototype,{_resolve:{value:function(e){let t,i=this._scope.get(e);if(!i)if(i=new mn(2,this),this._builtins.has(e))i.define(e,pn(this._builtins.get(e)));else if(this._runtime._builtin._scope.has(e))i.import(e,this._runtime._builtin);else{try{t=this._runtime._global(e)}catch(t){return i.define(e,function(e){return()=>{throw e}}(t))}void 0===t?this._scope.set(i._name=e,i):i.define(e,pn(t))}return i},writable:!0,configurable:!0},redefine:{value:function(e){const t=this._scope.get(e);if(!t)throw new cn(`${e} is not defined`);if(3===t._type)throw new cn(`${e} is defined more than once`);return t.define.apply(t,arguments)},writable:!0,configurable:!0},define:{value:function(){const e=new mn(1,this);return e.define.apply(e,arguments)},writable:!0,configurable:!0},derive:{value:function(e,t){const i=new Map,n=new Set,s=[];function r(e){let t=i.get(e);return t||(t=new Fn(e._runtime,e._builtins),t._source=e,i.set(e,t),s.push([t,e]),n.add(e),t)}const a=r(this);for(const i of e){const{alias:e,name:n}="object"==typeof i?i:{name:i};a.import(n,null==e?n:e,t)}for(const e of n)for(const[t,i]of e._scope)if(i._definition===ln){if(e===this&&a._scope.has(t))continue;const n=i._inputs[0]._module;n._source&&r(n)}for(const[e,t]of s)for(const[n,s]of t._scope){const t=e._scope.get(n);if(!t||2===t._type)if(s._definition===ln){const t=s._inputs[0],r=t._module;e.import(t._name,n,i.get(r)||r)}else e.define(n,s._inputs.map(Sn),s._definition)}return a},writable:!0,configurable:!0},import:{value:function(){const e=new mn(1,this);return e.import.apply(e,arguments)},writable:!0,configurable:!0},value:{value:async function(e){let t=this._scope.get(e);if(!t)throw new cn(`${e} is not defined`);if(t._observer!==fn)return kn(this._runtime,t);t=this.variable(!0).define([e],ln);try{return await kn(this._runtime,t)}finally{t.delete()}},writable:!0,configurable:!0},variable:{value:function(e,t){return new mn(1,this,e,t)},writable:!0,configurable:!0},builtin:{value:function(e,t){this._builtins.set(e,t)},writable:!0,configurable:!0}});const _n="function"==typeof requestAnimationFrame?requestAnimationFrame:"function"==typeof setImmediate?setImmediate:e=>setTimeout(e,0);function Bn(e=new Yt,t=Mn){const i=this.module();if(Object.defineProperties(this,{_dirty:{value:new Set},_updates:{value:new Set},_precomputes:{value:[],writable:!0},_computing:{value:null,writable:!0},_init:{value:null,writable:!0},_modules:{value:new Map},_variables:{value:new Set},_disposed:{value:!1,writable:!0},_builtin:{value:i},_global:{value:t}}),e)for(const t in e)new mn(2,i).define(t,[],e[t])}function In(e){const t=new Set(e._inputs);for(const i of t){if(i===e)return!0;i._inputs.forEach(t.add,t)}return!1}function Pn(e){++e._indegree}function Nn(e){--e._indegree}function Tn(e){return e._promise.catch(e._rejector)}function Ln(e){return new Promise((function(t){e._invalidate=t}))}function Vn(e,t){let i,n,s="function"==typeof IntersectionObserver&&t._observer&&t._observer._node,r=!s,a=dn,o=dn;return s&&(n=new IntersectionObserver((([e])=>(r=e.isIntersecting)&&(i=null,a()))),n.observe(s),e.then((()=>(n.disconnect(),n=null,o())))),function(e){return r?Promise.resolve(e):n?(i||(i=new Promise(((e,t)=>(a=e,o=t)))),i.then((()=>e))):Promise.reject()}}function Rn(e){e._invalidate(),e._invalidate=dn,e._pending();const t=e._value,i=++e._version;let n=null;const s=e._promise=(e._inputs.length?Promise.all(e._inputs.map(Tn)).then((function(s){if(e._version!==i)throw bn;for(let t=0,i=s.length;t<i;++t)switch(s[t]){case An:s[t]=n=Ln(e);break;case wn:n||(n=Ln(e)),s[t]=Vn(n,e);break;case Dn:s[t]=e}return e._definition.apply(t,s)})):new Promise((i=>i(e._definition.call(t))))).then((function(t){if(e._version!==i)throw bn;if(function(e){return e&&"function"==typeof e.next&&"function"==typeof e.return}(t))return(n||Ln(e)).then((s=t,function(){s.return()})),function(e,t,i){const n=e._module._runtime;let s;function r(e){return new Promise((e=>e(i.next(s)))).then((({done:t,value:i})=>t?void 0:Promise.resolve(i).then(e)))}function a(){const i=r((r=>{if(e._version!==t)throw bn;return s=r,o(r,i).then((()=>n._precompute(a))),e._fulfilled(r),r}));i.catch((n=>{n!==bn&&e._version===t&&(o(void 0,i),e._rejected(n))}))}function o(t,i){return e._value=t,e._promise=i,e._outputs.forEach(n._updates.add,n._updates),n._compute()}return r((i=>{if(e._version!==t)throw bn;return s=i,n._precompute(a),i}))}(e,i,t);var s;return t}));s.then((t=>{e._value=t,e._fulfilled(t)}),(t=>{t!==bn&&e._version===i&&(e._value=void 0,e._rejected(t))}))}function On(e,t){e._invalidate(),e._invalidate=dn,e._pending(),++e._version,e._indegree=NaN,(e._promise=Promise.reject(t)).catch(dn),e._value=void 0,e._rejected(t)}function Mn(e){return globalThis[e]}function jn(e){const t=document.createElement("template");return t.innerHTML=e,document.importNode(t.content,!0)}function qn(e){const t=document.createElementNS("http://www.w3.org/2000/svg","g");return t.innerHTML=e,t}Object.defineProperties(Bn.prototype,{_precompute:{value:function(e){this._precomputes.push(e),this._compute()},writable:!0,configurable:!0},_compute:{value:function(){return this._computing||(this._computing=this._computeSoon())},writable:!0,configurable:!0},_computeSoon:{value:function(){return new Promise(_n).then((()=>this._disposed?void 0:this._computeNow()))},writable:!0,configurable:!0},_computeNow:{value:async function(){let e,t,i=[],n=this._precomputes;if(n.length){this._precomputes=[];for(const e of n)e();await function(e=0){let t=Promise.resolve();for(let i=0;i<e;++i)t=t.then((()=>{}));return t}(3)}e=new Set(this._dirty),e.forEach((function(t){t._inputs.forEach(e.add,e);const i=function(e){if(e._observer!==fn)return!0;const t=new Set(e._outputs);for(const e of t){if(e._observer!==fn)return!0;e._outputs.forEach(t.add,t)}return!1}(t);i>t._reachable?this._updates.add(t):i<t._reachable&&t._invalidate(),t._reachable=i}),this),e=new Set(this._updates),e.forEach((function(t){t._reachable?(t._indegree=0,t._outputs.forEach(e.add,e)):(t._indegree=NaN,e.delete(t))})),this._computing=null,this._updates.clear(),this._dirty.clear(),e.forEach((function(e){e._outputs.forEach(Pn)}));do{for(e.forEach((function(e){0===e._indegree&&i.push(e)}));t=i.pop();)Rn(t),t._outputs.forEach(s),e.delete(t);e.forEach((function(t){In(t)&&(On(t,new cn("circular definition")),t._outputs.forEach(Nn),e.delete(t))}))}while(e.size);function s(e){0==--e._indegree&&i.push(e)}},writable:!0,configurable:!0},dispose:{value:function(){this._computing=Promise.resolve(),this._disposed=!0,this._variables.forEach((e=>{e._invalidate(),e._version=NaN}))},writable:!0,configurable:!0},module:{value:function(e,t=dn){let i;if(void 0===e)return(i=this._init)?(this._init=null,i):new Fn(this);if(i=this._modules.get(e),i)return i;this._init=i=new Fn(this),this._modules.set(e,i);try{e(this,t)}finally{this._init=null}return i},writable:!0,configurable:!0},fileAttachments:{value:At,writable:!0,configurable:!0},load:{value:function(e,t,i){if("function"==typeof t&&(i=t,t=null),"function"!=typeof i)throw new Error("invalid observer");null==t&&(t=new Yt);const{modules:n,id:s}=e,r=new Map,a=new Bn(t),o=u(s);function u(e){let t=r.get(e);return t||r.set(e,t=a.module()),t}for(const e of n){const t=u(e.id);let n=0;for(const s of e.variables)s.from?t.import(s.remote,s.name,u(s.from)):t===o?t.variable(i(s,n,e.variables)).define(s.name,s.inputs,s.value):t.define(s.name,s.inputs,s.value),++n}return a},writable:!0,configurable:!0}});const $n=Object.assign(ps(jn,(e=>{if(null===e.firstChild)return null;if(e.firstChild===e.lastChild)return e.removeChild(e.firstChild);const t=document.createElement("span");return t.appendChild(e),t})),{fragment:ps(jn,(e=>e))});Object.assign(ps(qn,(e=>null===e.firstChild?null:e.firstChild===e.lastChild?e.removeChild(e.firstChild):e)),{fragment:ps(qn,(e=>{const t=document.createDocumentFragment();for(;e.firstChild;)t.appendChild(e.firstChild);return t}))});const Un=60,zn=62,Wn=47,Hn=45,Gn=33,Qn=61,Kn=10,Yn=11,Jn=12,Xn=13,Zn=14,es=17,ts=22,is=23,ns=26,ss="http://www.w3.org/2000/svg",rs="http://www.w3.org/1999/xlink",as="http://www.w3.org/XML/1998/namespace",os="http://www.w3.org/2000/xmlns/",us=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),cs=new Map([["xlink:actuate",rs],["xlink:arcrole",rs],["xlink:href",rs],["xlink:role",rs],["xlink:show",rs],["xlink:title",rs],["xlink:type",rs],["xml:lang",as],["xml:space",as],["xmlns",os],["xmlns:xlink",os]]);function ps(e,t){return function({raw:i}){let n,s,r,a,o=1,u="",c=0;for(let e=0,t=arguments.length;e<t;++e){const t=i[e];if(e>0){const n=arguments[e];switch(o){case ns:if(null!=n){const e=`${n}`;if(gs(s))u+=e.replace(/[<]/g,ls);else{if(new RegExp(`</${s}[\\s>/]`,"i").test(u.slice(-s.length-2)+e))throw new Error("unsafe raw text");u+=e}}break;case 1:null==n||(n instanceof Node||"string"!=typeof n&&n[Symbol.iterator]||/(?:^|>)$/.test(i[e-1])&&/^(?:<|$)/.test(t)?(u+="\x3c!--::"+e+"--\x3e",c|=128):u+=`${n}`.replace(/[<&]/g,ls));break;case 9:{let s;if(o=Jn,/^[\s>]/.test(t)){if(null==n||!1===n){u=u.slice(0,r-i[e-1].length);break}if(!0===n||""==(s=`${n}`)){u+="''";break}if("style"===i[e-1].slice(r,a)&&fs(n)||"function"==typeof n){u+="::"+e,c|=1;break}}if(void 0===s&&(s=`${n}`),""===s)throw new Error("unsafe unquoted empty string");u+=s.replace(/^['"]|[\s>&]/g,ls);break}case Jn:u+=`${n}`.replace(/[\s>&]/g,ls);break;case Yn:u+=`${n}`.replace(/['&]/g,ls);break;case Kn:u+=`${n}`.replace(/["&]/g,ls);break;case 6:if(fs(n)){u+="::"+e+"=''",c|=1;break}throw new Error("invalid binding");case es:break;default:throw new Error("invalid binding")}}for(let e=0,i=t.length;e<i;++e){const i=t.charCodeAt(e);switch(o){case 1:i===Un&&(o=2);break;case 2:i===Gn?o=25:i===Wn?o=3:hs(i)?(n=e,s=void 0,o=4,--e):63===i?(o=5,--e):(o=1,--e);break;case 3:hs(i)?(o=4,--e):i===zn?o=1:(o=5,--e);break;case 4:ds(i)?(o=6,s=vs(t,n,e)):i===Wn?o=Zn:i===zn&&(s=vs(t,n,e),o=ms(s)?ns:1);break;case 6:ds(i)||(i===Wn||i===zn?(o=7,--e):i===Qn?(o=8,r=e+1,a=void 0):(o=8,--e,r=e+1,a=void 0));break;case 8:ds(i)||i===Wn||i===zn?(o=7,--e,a=e):i===Qn&&(o=9,a=e);break;case 7:ds(i)||(i===Wn?o=Zn:i===Qn?o=9:i===zn?o=ms(s)?ns:1:(o=8,--e,r=e+1,a=void 0));break;case 9:ds(i)||(34===i?o=Kn:39===i?o=Yn:i===zn?o=ms(s)?ns:1:(o=Jn,--e));break;case Kn:34===i&&(o=Xn);break;case Yn:39===i&&(o=Xn);break;case Jn:ds(i)?o=6:i===zn&&(o=ms(s)?ns:1);break;case Xn:ds(i)?o=6:i===Wn?o=Zn:i===zn?o=ms(s)?ns:1:(o=6,--e);break;case Zn:i===zn?o=1:(o=6,--e);break;case 5:i===zn&&(o=1);break;case 15:i===Hn?o=16:i===zn?o=1:(o=es,--e);break;case 16:i===Hn?o=is:i===zn?o=1:(o=es,--e);break;case es:i===Un?o=18:i===Hn&&(o=ts);break;case 18:i===Gn?o=19:i!==Un&&(o=es,--e);break;case 19:i===Hn?o=20:(o=es,--e);break;case 20:i===Hn?o=21:(o=is,--e);break;case 21:o=is,--e;break;case ts:i===Hn?o=is:(o=es,--e);break;case is:i===zn?o=1:i===Gn?o=24:i!==Hn&&(o=es,--e);break;case 24:i===Hn?o=ts:i===zn?o=1:(o=es,--e);break;case 25:i===Hn&&t.charCodeAt(e+1)===Hn?(o=15,++e):(o=5,--e);break;case ns:i===Un&&(o=27);break;case 27:i===Wn?o=28:(o=ns,--e);break;case 28:hs(i)?(n=e,o=29,--e):(o=ns,--e);break;case 29:ds(i)&&s===vs(t,n,e)?o=6:i===Wn&&s===vs(t,n,e)?o=Zn:i===zn&&s===vs(t,n,e)?o=1:hs(i)||(o=ns,--e);break;default:o=void 0}}u+=t}const p=e(u),l=document.createTreeWalker(p,c,null,!1),h=[];for(;l.nextNode();){const e=l.currentNode;switch(e.nodeType){case 1:{const t=e.attributes;for(let i=0,n=t.length;i<n;++i){const{name:s,value:r}=t[i];if(/^::/.test(s)){const t=arguments[+s.slice(2)];ys(e,s),--i,--n;for(const i in t){const n=t[i];null==n||!1===n||("function"==typeof n?e[i]=n:"style"===i&&fs(n)?bs(e[i],n):xs(e,i,!0===n?"":n))}}else if(/^::/.test(r)){const t=arguments[+r.slice(2)];ys(e,s),--i,--n,"function"==typeof t?e[s]=t:bs(e[s],t)}}break}case 8:if(/^::/.test(e.data)){const t=e.parentNode,i=arguments[+e.data.slice(2)];if(i instanceof Node)t.insertBefore(i,e);else if("string"!=typeof i&&i[Symbol.iterator])if(i instanceof NodeList||i instanceof HTMLCollection)for(let n=i.length-1,s=e;n>=0;--n)s=t.insertBefore(i[n],s);else for(const n of i)null!=n&&t.insertBefore(n instanceof Node?n:document.createTextNode(n),e);else t.insertBefore(document.createTextNode(i),e);h.push(e)}}}for(const e of h)e.parentNode.removeChild(e);return t(p)}}function ls(e){return`&#${e.charCodeAt(0).toString()};`}function hs(e){return 65<=e&&e<=90||97<=e&&e<=122}function ds(e){return 9===e||10===e||12===e||32===e||13===e}function fs(e){return e&&e.toString===Object.prototype.toString}function ms(e){return"script"===e||"style"===e||gs(e)}function gs(e){return"textarea"===e||"title"===e}function vs(e,t,i){return e.slice(t,i).toLowerCase()}function xs(e,t,i){e.namespaceURI===ss&&(t=t.toLowerCase(),t=us.get(t)||t,cs.has(t))?e.setAttributeNS(cs.get(t),t,i):e.setAttribute(t,i)}function ys(e,t){e.namespaceURI===ss&&(t=t.toLowerCase(),t=us.get(t)||t,cs.has(t))?e.removeAttributeNS(cs.get(t),t):e.removeAttribute(t)}function bs(e,t){for(const i in t){const n=t[i];i.startsWith("--")?e.setProperty(i,n):e[i]=n}}const Es={bubbles:!0};function Cs(e){e.preventDefault()}function Ds(e){return e}let As=0;function ws(e,t){if(e)return e=$n`<label>${e}`,void 0!==t&&(e.htmlFor=t.id="__ns__-"+ ++As),e}function Fs(e="≡",{label:t="",value:i,reduce:n,disabled:s,required:r=!1,width:a}={}){const o="string"==typeof e||e instanceof Node;o?(r||void 0!==i||(i=0),void 0===n&&(n=(e=0)=>e+1),s=new Set(s?[e]:[]),e=[[e,n]]):(r||void 0!==i||(i=null),s=new Set(!0===s?Array.from(e,(([e])=>e)):s||void 0));const u=$n`<form class=__ns__ onsubmit=${Cs}>`,c={width:(p=a,null==p?null:"number"==typeof p?`${p}px`:`${p}`)};var p;const l=Array.from(e,(([e,t=Ds])=>{if("function"!=typeof t)throw new TypeError("reduce is not a function");return $n`<button disabled=${s.has(e)} style=${c} onclick=${e=>{u.value=t(u.value),function({currentTarget:e}){(e.form||e).dispatchEvent(new Event("input",Es))}(e)}}>${e}`}));return(t=ws(t,o?l[0]:void 0))&&u.append(t),u.append(...l),u.value=i,u}const ks=_s((e=>{const t=Ss(e);return e=>null==e?"":"number"==typeof e?t(e):e instanceof Date?Ui(e,"Invalid Date"):`${e}`})),Ss=_s((e=>t=>0===t?"0":t.toLocaleString(e)));function _s(e){let t,i=_s;return(n="en")=>n===i?t:t=e(i=n)}ks(),Ss();class Bs extends un{constructor(e,t){super(e),this._cellAst=t}rejected(e){return super.rejected(e)}}const Is=new Set;let Ps={};class Ns extends Bs{constructor(e){super(e)}fulfilled(e,t){return Is.has(t)&&window.Shiny&&(void 0===window.Shiny.setInputValue?Ps[t]=e:window.Shiny.setInputValue(t,e)),super.fulfilled(e,t)}}const{Generators:Ts}=new Yt;class Ls{find(e){return document.querySelectorAll(".ojs-inputs-button")}init(e,t){const i=Fs(e.textContent);e.innerHTML="",e.appendChild(i);const n=Ts.input(e.firstChild);return async function(){await n.next().value;for(const e of n)t(await e)}(),{onSetValue:e=>{},dispose:()=>{n.return()}}}}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var Vs={exports:{}};!function(e){var t={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},i="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",n={5:i,"5module":i+" export import",6:i+" const class extends export import super"},s=/^in(stanceof)?$/,r="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",a="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",o=new RegExp("["+r+"]"),u=new RegExp("["+r+a+"]");r=a=null;var c=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],p=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function l(e,t){for(var i=65536,n=0;n<t.length;n+=2){if((i+=t[n])>e)return!1;if((i+=t[n+1])>=e)return!0}}function h(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&o.test(String.fromCharCode(e)):!1!==t&&l(e,c)))}function d(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&u.test(String.fromCharCode(e)):!1!==t&&(l(e,c)||l(e,p)))))}var f=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function m(e,t){return new f(e,{beforeExpr:!0,binop:t})}var g={beforeExpr:!0},v={startsExpr:!0},x={};function y(e,t){return void 0===t&&(t={}),t.keyword=e,x[e]=new f(e,t)}var b={num:new f("num",v),regexp:new f("regexp",v),string:new f("string",v),name:new f("name",v),eof:new f("eof"),bracketL:new f("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new f("]"),braceL:new f("{",{beforeExpr:!0,startsExpr:!0}),braceR:new f("}"),parenL:new f("(",{beforeExpr:!0,startsExpr:!0}),parenR:new f(")"),comma:new f(",",g),semi:new f(";",g),colon:new f(":",g),dot:new f("."),question:new f("?",g),questionDot:new f("?."),arrow:new f("=>",g),template:new f("template"),invalidTemplate:new f("invalidTemplate"),ellipsis:new f("...",g),backQuote:new f("`",v),dollarBraceL:new f("${",{beforeExpr:!0,startsExpr:!0}),eq:new f("=",{beforeExpr:!0,isAssign:!0}),assign:new f("_=",{beforeExpr:!0,isAssign:!0}),incDec:new f("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new f("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:m("||",1),logicalAND:m("&&",2),bitwiseOR:m("|",3),bitwiseXOR:m("^",4),bitwiseAND:m("&",5),equality:m("==/!=/===/!==",6),relational:m("</>/<=/>=",7),bitShift:m("<</>>/>>>",8),plusMin:new f("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:m("%",10),star:m("*",10),slash:m("/",10),starstar:new f("**",{beforeExpr:!0}),coalesce:m("??",1),_break:y("break"),_case:y("case",g),_catch:y("catch"),_continue:y("continue"),_debugger:y("debugger"),_default:y("default",g),_do:y("do",{isLoop:!0,beforeExpr:!0}),_else:y("else",g),_finally:y("finally"),_for:y("for",{isLoop:!0}),_function:y("function",v),_if:y("if"),_return:y("return",g),_switch:y("switch"),_throw:y("throw",g),_try:y("try"),_var:y("var"),_const:y("const"),_while:y("while",{isLoop:!0}),_with:y("with"),_new:y("new",{beforeExpr:!0,startsExpr:!0}),_this:y("this",v),_super:y("super",v),_class:y("class",v),_extends:y("extends",g),_export:y("export"),_import:y("import",v),_null:y("null",v),_true:y("true",v),_false:y("false",v),_in:y("in",{beforeExpr:!0,binop:7}),_instanceof:y("instanceof",{beforeExpr:!0,binop:7}),_typeof:y("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:y("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:y("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},E=/\r\n?|\n|\u2028|\u2029/,C=new RegExp(E.source,"g");function D(e,t){return 10===e||13===e||!t&&(8232===e||8233===e)}var A=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,w=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,F=Object.prototype,k=F.hasOwnProperty,S=F.toString;function _(e,t){return k.call(e,t)}var B=Array.isArray||function(e){return"[object Array]"===S.call(e)};function I(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}var P=function(e,t){this.line=e,this.column=t};P.prototype.offset=function(e){return new P(this.line,this.column+e)};var N=function(e,t,i){this.start=t,this.end=i,null!==e.sourceFile&&(this.source=e.sourceFile)};function T(e,t){for(var i=1,n=0;;){C.lastIndex=n;var s=C.exec(e);if(!(s&&s.index<t))return new P(i,t-n);++i,n=s.index+s[0].length}}var L={ecmaVersion:10,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1};function V(e){var t={};for(var i in L)t[i]=e&&_(e,i)?e[i]:L[i];if(t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),B(t.onToken)){var n=t.onToken;t.onToken=function(e){return n.push(e)}}return B(t.onComment)&&(t.onComment=R(t,t.onComment)),t}function R(e,t){return function(i,n,s,r,a,o){var u={type:i?"Block":"Line",value:n,start:s,end:r};e.locations&&(u.loc=new N(this,a,o)),e.ranges&&(u.range=[s,r]),t.push(u)}}var O=1,M=2,j=O|M,q=4,$=8,U=16,z=32,W=64,H=128;function G(e,t){return M|(e?q:0)|(t?$:0)}var Q=0,K=1,Y=2,J=3,X=4,Z=5,ee=function(e,i,s){this.options=e=V(e),this.sourceFile=e.sourceFile,this.keywords=I(n[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var r="";if(!0!==e.allowReserved){for(var a=e.ecmaVersion;!(r=t[a]);a--);"module"===e.sourceType&&(r+=" await")}this.reservedWords=I(r);var o=(r?r+" ":"")+t.strict;this.reservedWordsStrict=I(o),this.reservedWordsStrictBind=I(o+" "+t.strictBind),this.input=String(i),this.containsEsc=!1,s?(this.pos=s,this.lineStart=this.input.lastIndexOf("\n",s-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(E).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=b.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports={},0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(O),this.regexpState=null},te={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0}};ee.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},te.inFunction.get=function(){return(this.currentVarScope().flags&M)>0},te.inGenerator.get=function(){return(this.currentVarScope().flags&$)>0},te.inAsync.get=function(){return(this.currentVarScope().flags&q)>0},te.allowSuper.get=function(){return(this.currentThisScope().flags&W)>0},te.allowDirectSuper.get=function(){return(this.currentThisScope().flags&H)>0},te.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},ee.prototype.inNonArrowFunction=function(){return(this.currentThisScope().flags&M)>0},ee.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var i=this,n=0;n<e.length;n++)i=e[n](i);return i},ee.parse=function(e,t){return new this(t,e).parse()},ee.parseExpressionAt=function(e,t,i){var n=new this(i,e,t);return n.nextToken(),n.parseExpression()},ee.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(ee.prototype,te);var ie=ee.prototype,ne=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;function se(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}ie.strictDirective=function(e){for(;;){w.lastIndex=e,e+=w.exec(this.input)[0].length;var t=ne.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){w.lastIndex=e+t[0].length;var i=w.exec(this.input),n=i.index+i[0].length,s=this.input.charAt(n);return";"===s||"}"===s||E.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(s)||"!"===s&&"="===this.input.charAt(n+1))}e+=t[0].length,w.lastIndex=e,e+=w.exec(this.input)[0].length,";"===this.input[e]&&e++}},ie.eat=function(e){return this.type===e&&(this.next(),!0)},ie.isContextual=function(e){return this.type===b.name&&this.value===e&&!this.containsEsc},ie.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},ie.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},ie.canInsertSemicolon=function(){return this.type===b.eof||this.type===b.braceR||E.test(this.input.slice(this.lastTokEnd,this.start))},ie.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},ie.semicolon=function(){this.eat(b.semi)||this.insertSemicolon()||this.unexpected()},ie.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},ie.expect=function(e){this.eat(e)||this.unexpected()},ie.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")},ie.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var i=t?e.parenthesizedAssign:e.parenthesizedBind;i>-1&&this.raiseRecoverable(i,"Parenthesized pattern")}},ie.checkExpressionErrors=function(e,t){if(!e)return!1;var i=e.shorthandAssign,n=e.doubleProto;if(!t)return i>=0||n>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),n>=0&&this.raiseRecoverable(n,"Redefinition of __proto__ property")},ie.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},ie.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var re=ee.prototype;re.parseTopLevel=function(e){var t={};for(e.body||(e.body=[]);this.type!==b.eof;){var i=this.parseStatement(null,!0,t);e.body.push(i)}if(this.inModule)for(var n=0,s=Object.keys(this.undefinedExports);n<s.length;n+=1){var r=s[n];this.raiseRecoverable(this.undefinedExports[r].start,"Export '"+r+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var ae={kind:"loop"},oe={kind:"switch"};re.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;w.lastIndex=this.pos;var t=w.exec(this.input),i=this.pos+t[0].length,n=this.input.charCodeAt(i);if(91===n)return!0;if(e)return!1;if(123===n)return!0;if(h(n,!0)){for(var r=i+1;d(this.input.charCodeAt(r),!0);)++r;var a=this.input.slice(i,r);if(!s.test(a))return!0}return!1},re.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;w.lastIndex=this.pos;var e=w.exec(this.input),t=this.pos+e[0].length;return!(E.test(this.input.slice(this.pos,t))||"function"!==this.input.slice(t,t+8)||t+8!==this.input.length&&d(this.input.charAt(t+8)))},re.parseStatement=function(e,t,i){var n,s=this.type,r=this.startNode();switch(this.isLet(e)&&(s=b._var,n="let"),s){case b._break:case b._continue:return this.parseBreakContinueStatement(r,s.keyword);case b._debugger:return this.parseDebuggerStatement(r);case b._do:return this.parseDoStatement(r);case b._for:return this.parseForStatement(r);case b._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(r,!1,!e);case b._class:return e&&this.unexpected(),this.parseClass(r,!0);case b._if:return this.parseIfStatement(r);case b._return:return this.parseReturnStatement(r);case b._switch:return this.parseSwitchStatement(r);case b._throw:return this.parseThrowStatement(r);case b._try:return this.parseTryStatement(r);case b._const:case b._var:return n=n||this.value,e&&"var"!==n&&this.unexpected(),this.parseVarStatement(r,n);case b._while:return this.parseWhileStatement(r);case b._with:return this.parseWithStatement(r);case b.braceL:return this.parseBlock(!0,r);case b.semi:return this.parseEmptyStatement(r);case b._export:case b._import:if(this.options.ecmaVersion>10&&s===b._import){w.lastIndex=this.pos;var a=w.exec(this.input),o=this.pos+a[0].length,u=this.input.charCodeAt(o);if(40===u||46===u)return this.parseExpressionStatement(r,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===b._import?this.parseImport(r):this.parseExport(r,i);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(r,!0,!e);var c=this.value,p=this.parseExpression();return s===b.name&&"Identifier"===p.type&&this.eat(b.colon)?this.parseLabeledStatement(r,c,p,e):this.parseExpressionStatement(r,p)}},re.parseBreakContinueStatement=function(e,t){var i="break"===t;this.next(),this.eat(b.semi)||this.insertSemicolon()?e.label=null:this.type!==b.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var n=0;n<this.labels.length;++n){var s=this.labels[n];if(null==e.label||s.name===e.label.name){if(null!=s.kind&&(i||"loop"===s.kind))break;if(e.label&&i)break}}return n===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,i?"BreakStatement":"ContinueStatement")},re.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},re.parseDoStatement=function(e){return this.next(),this.labels.push(ae),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(b._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(b.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},re.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(ae),this.enterScope(0),this.expect(b.parenL),this.type===b.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var i=this.isLet();if(this.type===b._var||this.type===b._const||i){var n=this.startNode(),s=i?"let":this.value;return this.next(),this.parseVar(n,!0,s),this.finishNode(n,"VariableDeclaration"),(this.type===b._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===n.declarations.length?(this.options.ecmaVersion>=9&&(this.type===b._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,n)):(t>-1&&this.unexpected(t),this.parseFor(e,n))}var r=new se,a=this.parseExpression(!0,r);return this.type===b._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.options.ecmaVersion>=9&&(this.type===b._in?t>-1&&this.unexpected(t):e.await=t>-1),this.toAssignable(a,!1,r),this.checkLVal(a),this.parseForIn(e,a)):(this.checkExpressionErrors(r,!0),t>-1&&this.unexpected(t),this.parseFor(e,a))},re.parseFunctionStatement=function(e,t,i){return this.next(),this.parseFunction(e,ce|(i?0:pe),!1,t)},re.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(b._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},re.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(b.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},re.parseSwitchStatement=function(e){var t;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(b.braceL),this.labels.push(oe),this.enterScope(0);for(var i=!1;this.type!==b.braceR;)if(this.type===b._case||this.type===b._default){var n=this.type===b._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),n?t.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,t.test=null),this.expect(b.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},re.parseThrowStatement=function(e){return this.next(),E.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var ue=[];re.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===b._catch){var t=this.startNode();if(this.next(),this.eat(b.parenL)){t.param=this.parseBindingAtom();var i="Identifier"===t.param.type;this.enterScope(i?z:0),this.checkLVal(t.param,i?X:Y),this.expect(b.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(b._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},re.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},re.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(ae),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},re.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},re.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},re.parseLabeledStatement=function(e,t,i,n){for(var s=0,r=this.labels;s<r.length;s+=1)r[s].name===t&&this.raise(i.start,"Label '"+t+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===b._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var u=this.labels[o];if(u.statementStart!==e.start)break;u.statementStart=this.start,u.kind=a}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(n?-1===n.indexOf("label")?n+"label":n:"label"),this.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")},re.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},re.parseBlock=function(e,t,i){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(b.braceL),e&&this.enterScope(0);this.type!==b.braceR;){var n=this.parseStatement(null);t.body.push(n)}return i&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},re.parseFor=function(e,t){return e.init=t,this.expect(b.semi),e.test=this.type===b.semi?null:this.parseExpression(),this.expect(b.semi),e.update=this.type===b.parenR?null:this.parseExpression(),this.expect(b.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},re.parseForIn=function(e,t){var i=this.type===b._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!i||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)?this.raise(t.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"):"AssignmentPattern"===t.type&&this.raise(t.start,"Invalid left-hand side in for-loop"),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(b.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")},re.parseVar=function(e,t,i){for(e.declarations=[],e.kind=i;;){var n=this.startNode();if(this.parseVarId(n,i),this.eat(b.eq)?n.init=this.parseMaybeAssign(t):"const"!==i||this.type===b._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===n.id.type||t&&(this.type===b._in||this.isContextual("of"))?n.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(n,"VariableDeclarator")),!this.eat(b.comma))break}return e},re.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLVal(e.id,"var"===t?K:Y,!1)};var ce=1,pe=2,le=4;re.parseFunction=function(e,t,i,n){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!n)&&(this.type===b.star&&t&pe&&this.unexpected(),e.generator=this.eat(b.star)),this.options.ecmaVersion>=8&&(e.async=!!n),t&ce&&(e.id=t&le&&this.type!==b.name?null:this.parseIdent(),!e.id||t&pe||this.checkLVal(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?K:Y:J));var s=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(G(e.async,e.generator)),t&ce||(e.id=this.type===b.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,i,!1),this.yieldPos=s,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(e,t&ce?"FunctionDeclaration":"FunctionExpression")},re.parseFunctionParams=function(e){this.expect(b.parenL),e.params=this.parseBindingList(b.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},re.parseClass=function(e,t){this.next();var i=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var n=this.startNode(),s=!1;for(n.body=[],this.expect(b.braceL);this.type!==b.braceR;){var r=this.parseClassElement(null!==e.superClass);r&&(n.body.push(r),"MethodDefinition"===r.type&&"constructor"===r.kind&&(s&&this.raise(r.start,"Duplicate constructor in the same class"),s=!0))}return this.strict=i,this.next(),e.body=this.finishNode(n,"ClassBody"),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},re.parseClassElement=function(e){var t=this;if(this.eat(b.semi))return null;var i=this.startNode(),n=function(e,n){void 0===n&&(n=!1);var s=t.start,r=t.startLoc;return!(!t.eatContextual(e)||(t.type===b.parenL||n&&t.canInsertSemicolon())&&(i.key&&t.unexpected(),i.computed=!1,i.key=t.startNodeAt(s,r),i.key.name=e,t.finishNode(i.key,"Identifier"),1))};i.kind="method",i.static=n("static");var s=this.eat(b.star),r=!1;s||(this.options.ecmaVersion>=8&&n("async",!0)?(r=!0,s=this.options.ecmaVersion>=9&&this.eat(b.star)):n("get")?i.kind="get":n("set")&&(i.kind="set")),i.key||this.parsePropertyName(i);var a=i.key,o=!1;return i.computed||i.static||!("Identifier"===a.type&&"constructor"===a.name||"Literal"===a.type&&"constructor"===a.value)?i.static&&"Identifier"===a.type&&"prototype"===a.name&&this.raise(a.start,"Classes may not have a static property named prototype"):("method"!==i.kind&&this.raise(a.start,"Constructor can't have get/set modifier"),s&&this.raise(a.start,"Constructor can't be a generator"),r&&this.raise(a.start,"Constructor can't be an async method"),i.kind="constructor",o=e),this.parseClassMethod(i,s,r,o),"get"===i.kind&&0!==i.value.params.length&&this.raiseRecoverable(i.value.start,"getter should have no params"),"set"===i.kind&&1!==i.value.params.length&&this.raiseRecoverable(i.value.start,"setter should have exactly one param"),"set"===i.kind&&"RestElement"===i.value.params[0].type&&this.raiseRecoverable(i.value.params[0].start,"Setter cannot use rest params"),i},re.parseClassMethod=function(e,t,i,n){return e.value=this.parseMethod(t,i,n),this.finishNode(e,"MethodDefinition")},re.parseClassId=function(e,t){this.type===b.name?(e.id=this.parseIdent(),t&&this.checkLVal(e.id,Y,!1)):(!0===t&&this.unexpected(),e.id=null)},re.parseClassSuper=function(e){e.superClass=this.eat(b._extends)?this.parseExprSubscripts():null},re.parseExport=function(e,t){if(this.next(),this.eat(b.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseIdent(!0),this.checkExport(t,e.exported.name,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==b.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(b._default)){var i;if(this.checkExport(t,"default",this.lastTokStart),this.type===b._function||(i=this.isAsyncFunction())){var n=this.startNode();this.next(),i&&this.next(),e.declaration=this.parseFunction(n,ce|le,!1,i)}else if(this.type===b._class){var s=this.startNode();e.declaration=this.parseClass(s,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id.name,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==b.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var r=0,a=e.specifiers;r<a.length;r+=1){var o=a[r];this.checkUnreserved(o.local),this.checkLocalExport(o.local)}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},re.checkExport=function(e,t,i){e&&(_(e,t)&&this.raiseRecoverable(i,"Duplicate export '"+t+"'"),e[t]=!0)},re.checkPatternExport=function(e,t){var i=t.type;if("Identifier"===i)this.checkExport(e,t.name,t.start);else if("ObjectPattern"===i)for(var n=0,s=t.properties;n<s.length;n+=1){var r=s[n];this.checkPatternExport(e,r)}else if("ArrayPattern"===i)for(var a=0,o=t.elements;a<o.length;a+=1){var u=o[a];u&&this.checkPatternExport(e,u)}else"Property"===i?this.checkPatternExport(e,t.value):"AssignmentPattern"===i?this.checkPatternExport(e,t.left):"RestElement"===i?this.checkPatternExport(e,t.argument):"ParenthesizedExpression"===i&&this.checkPatternExport(e,t.expression)},re.checkVariableExport=function(e,t){if(e)for(var i=0,n=t;i<n.length;i+=1){var s=n[i];this.checkPatternExport(e,s.id)}},re.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},re.parseExportSpecifiers=function(e){var t=[],i=!0;for(this.expect(b.braceL);!this.eat(b.braceR);){if(i)i=!1;else if(this.expect(b.comma),this.afterTrailingComma(b.braceR))break;var n=this.startNode();n.local=this.parseIdent(!0),n.exported=this.eatContextual("as")?this.parseIdent(!0):n.local,this.checkExport(e,n.exported.name,n.exported.start),t.push(this.finishNode(n,"ExportSpecifier"))}return t},re.parseImport=function(e){return this.next(),this.type===b.string?(e.specifiers=ue,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===b.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},re.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===b.name){var i=this.startNode();if(i.local=this.parseIdent(),this.checkLVal(i.local,Y),e.push(this.finishNode(i,"ImportDefaultSpecifier")),!this.eat(b.comma))return e}if(this.type===b.star){var n=this.startNode();return this.next(),this.expectContextual("as"),n.local=this.parseIdent(),this.checkLVal(n.local,Y),e.push(this.finishNode(n,"ImportNamespaceSpecifier")),e}for(this.expect(b.braceL);!this.eat(b.braceR);){if(t)t=!1;else if(this.expect(b.comma),this.afterTrailingComma(b.braceR))break;var s=this.startNode();s.imported=this.parseIdent(!0),this.eatContextual("as")?s.local=this.parseIdent():(this.checkUnreserved(s.imported),s.local=s.imported),this.checkLVal(s.local,Y),e.push(this.finishNode(s,"ImportSpecifier"))}return e},re.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},re.isDirectiveCandidate=function(e){return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var he=ee.prototype;he.toAssignable=function(e,t,i){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];this.toAssignable(r,t),"RestElement"!==r.type||"ArrayPattern"!==r.argument.type&&"ObjectPattern"!==r.argument.type||this.raise(r.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,i);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return e},he.toAssignableList=function(e,t){for(var i=e.length,n=0;n<i;n++){var s=e[n];s&&this.toAssignable(s,t)}if(i){var r=e[i-1];6===this.options.ecmaVersion&&t&&r&&"RestElement"===r.type&&"Identifier"!==r.argument.type&&this.unexpected(r.argument.start)}return e},he.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},he.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==b.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},he.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case b.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(b.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case b.braceL:return this.parseObj(!0)}return this.parseIdent()},he.parseBindingList=function(e,t,i){for(var n=[],s=!0;!this.eat(e);)if(s?s=!1:this.expect(b.comma),t&&this.type===b.comma)n.push(null);else{if(i&&this.afterTrailingComma(e))break;if(this.type===b.ellipsis){var r=this.parseRestBinding();this.parseBindingListItem(r),n.push(r),this.type===b.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),n.push(a)}return n},he.parseBindingListItem=function(e){return e},he.parseMaybeDefault=function(e,t,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(b.eq))return i;var n=this.startNodeAt(e,t);return n.left=i,n.right=this.parseMaybeAssign(),this.finishNode(n,"AssignmentPattern")},he.checkLVal=function(e,t,i){switch(void 0===t&&(t=Q),e.type){case"Identifier":t===Y&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(t?"Binding ":"Assigning to ")+e.name+" in strict mode"),i&&(_(i,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),i[e.name]=!0),t!==Q&&t!==Z&&this.declareName(e.name,t,e.start);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":t&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ObjectPattern":for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];this.checkLVal(r,t,i)}break;case"Property":this.checkLVal(e.value,t,i);break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var u=o[a];u&&this.checkLVal(u,t,i)}break;case"AssignmentPattern":this.checkLVal(e.left,t,i);break;case"RestElement":this.checkLVal(e.argument,t,i);break;case"ParenthesizedExpression":this.checkLVal(e.expression,t,i);break;default:this.raise(e.start,(t?"Binding":"Assigning to")+" rvalue")}};var de=ee.prototype;de.checkPropClash=function(e,t,i){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var n,s=e.key;switch(s.type){case"Identifier":n=s.name;break;case"Literal":n=String(s.value);break;default:return}var r=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===n&&"init"===r&&(t.proto&&(i?i.doubleProto<0&&(i.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);else{var a=t[n="$"+n];a?("init"===r?this.strict&&a.init||a.get||a.set:a.init||a[r])&&this.raiseRecoverable(s.start,"Redefinition of property"):a=t[n]={init:!1,get:!1,set:!1},a[r]=!0}}},de.parseExpression=function(e,t){var i=this.start,n=this.startLoc,s=this.parseMaybeAssign(e,t);if(this.type===b.comma){var r=this.startNodeAt(i,n);for(r.expressions=[s];this.eat(b.comma);)r.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(r,"SequenceExpression")}return s},de.parseMaybeAssign=function(e,t,i){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var n=!1,s=-1,r=-1;t?(s=t.parenthesizedAssign,r=t.trailingComma,t.parenthesizedAssign=t.trailingComma=-1):(t=new se,n=!0);var a=this.start,o=this.startLoc;this.type!==b.parenL&&this.type!==b.name||(this.potentialArrowAt=this.start);var u=this.parseMaybeConditional(e,t);if(i&&(u=i.call(this,u,a,o)),this.type.isAssign){var c=this.startNodeAt(a,o);return c.operator=this.value,c.left=this.type===b.eq?this.toAssignable(u,!1,t):u,n||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=c.left.start&&(t.shorthandAssign=-1),this.checkLVal(u),this.next(),c.right=this.parseMaybeAssign(e),this.finishNode(c,"AssignmentExpression")}return n&&this.checkExpressionErrors(t,!0),s>-1&&(t.parenthesizedAssign=s),r>-1&&(t.trailingComma=r),u},de.parseMaybeConditional=function(e,t){var i=this.start,n=this.startLoc,s=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return s;if(this.eat(b.question)){var r=this.startNodeAt(i,n);return r.test=s,r.consequent=this.parseMaybeAssign(),this.expect(b.colon),r.alternate=this.parseMaybeAssign(e),this.finishNode(r,"ConditionalExpression")}return s},de.parseExprOps=function(e,t){var i=this.start,n=this.startLoc,s=this.parseMaybeUnary(t,!1);return this.checkExpressionErrors(t)||s.start===i&&"ArrowFunctionExpression"===s.type?s:this.parseExprOp(s,i,n,-1,e)},de.parseExprOp=function(e,t,i,n,s){var r=this.type.binop;if(null!=r&&(!s||this.type!==b._in)&&r>n){var a=this.type===b.logicalOR||this.type===b.logicalAND,o=this.type===b.coalesce;o&&(r=b.logicalAND.binop);var u=this.value;this.next();var c=this.start,p=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1),c,p,r,s),h=this.buildBinary(t,i,e,l,u,a||o);return(a&&this.type===b.coalesce||o&&(this.type===b.logicalOR||this.type===b.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(h,t,i,n,s)}return e},de.buildBinary=function(e,t,i,n,s,r){var a=this.startNodeAt(e,t);return a.left=i,a.operator=s,a.right=n,this.finishNode(a,r?"LogicalExpression":"BinaryExpression")},de.parseMaybeUnary=function(e,t){var i,n=this.start,s=this.startLoc;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction))i=this.parseAwait(),t=!0;else if(this.type.prefix){var r=this.startNode(),a=this.type===b.incDec;r.operator=this.value,r.prefix=!0,this.next(),r.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),a?this.checkLVal(r.argument):this.strict&&"delete"===r.operator&&"Identifier"===r.argument.type?this.raiseRecoverable(r.start,"Deleting local variable in strict mode"):t=!0,i=this.finishNode(r,a?"UpdateExpression":"UnaryExpression")}else{if(i=this.parseExprSubscripts(e),this.checkExpressionErrors(e))return i;for(;this.type.postfix&&!this.canInsertSemicolon();){var o=this.startNodeAt(n,s);o.operator=this.value,o.prefix=!1,o.argument=i,this.checkLVal(i),this.next(),i=this.finishNode(o,"UpdateExpression")}}return!t&&this.eat(b.starstar)?this.buildBinary(n,s,i,this.parseMaybeUnary(null,!1),"**",!1):i},de.parseExprSubscripts=function(e){var t=this.start,i=this.startLoc,n=this.parseExprAtom(e);if("ArrowFunctionExpression"===n.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return n;var s=this.parseSubscripts(n,t,i);return e&&"MemberExpression"===s.type&&(e.parenthesizedAssign>=s.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=s.start&&(e.parenthesizedBind=-1)),s},de.parseSubscripts=function(e,t,i,n){for(var s=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.potentialArrowAt===e.start,r=!1;;){var a=this.parseSubscript(e,t,i,n,s,r);if(a.optional&&(r=!0),a===e||"ArrowFunctionExpression"===a.type){if(r){var o=this.startNodeAt(t,i);o.expression=a,a=this.finishNode(o,"ChainExpression")}return a}e=a}},de.parseSubscript=function(e,t,i,n,s,r){var a=this.options.ecmaVersion>=11,o=a&&this.eat(b.questionDot);n&&o&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var u=this.eat(b.bracketL);if(u||o&&this.type!==b.parenL&&this.type!==b.backQuote||this.eat(b.dot)){var c=this.startNodeAt(t,i);c.object=e,c.property=u?this.parseExpression():this.parseIdent("never"!==this.options.allowReserved),c.computed=!!u,u&&this.expect(b.bracketR),a&&(c.optional=o),e=this.finishNode(c,"MemberExpression")}else if(!n&&this.eat(b.parenL)){var p=new se,l=this.yieldPos,h=this.awaitPos,d=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var f=this.parseExprList(b.parenR,this.options.ecmaVersion>=8,!1,p);if(s&&!o&&!this.canInsertSemicolon()&&this.eat(b.arrow))return this.checkPatternErrors(p,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=l,this.awaitPos=h,this.awaitIdentPos=d,this.parseArrowExpression(this.startNodeAt(t,i),f,!0);this.checkExpressionErrors(p,!0),this.yieldPos=l||this.yieldPos,this.awaitPos=h||this.awaitPos,this.awaitIdentPos=d||this.awaitIdentPos;var m=this.startNodeAt(t,i);m.callee=e,m.arguments=f,a&&(m.optional=o),e=this.finishNode(m,"CallExpression")}else if(this.type===b.backQuote){(o||r)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var g=this.startNodeAt(t,i);g.tag=e,g.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(g,"TaggedTemplateExpression")}return e},de.parseExprAtom=function(e){this.type===b.slash&&this.readRegexp();var t,i=this.potentialArrowAt===this.start;switch(this.type){case b._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),t=this.startNode(),this.next(),this.type!==b.parenL||this.allowDirectSuper||this.raise(t.start,"super() call outside constructor of a subclass"),this.type!==b.dot&&this.type!==b.bracketL&&this.type!==b.parenL&&this.unexpected(),this.finishNode(t,"Super");case b._this:return t=this.startNode(),this.next(),this.finishNode(t,"ThisExpression");case b.name:var n=this.start,s=this.startLoc,r=this.containsEsc,a=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!r&&"async"===a.name&&!this.canInsertSemicolon()&&this.eat(b._function))return this.parseFunction(this.startNodeAt(n,s),0,!1,!0);if(i&&!this.canInsertSemicolon()){if(this.eat(b.arrow))return this.parseArrowExpression(this.startNodeAt(n,s),[a],!1);if(this.options.ecmaVersion>=8&&"async"===a.name&&this.type===b.name&&!r)return a=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(b.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,s),[a],!0)}return a;case b.regexp:var o=this.value;return(t=this.parseLiteral(o.value)).regex={pattern:o.pattern,flags:o.flags},t;case b.num:case b.string:return this.parseLiteral(this.value);case b._null:case b._true:case b._false:return(t=this.startNode()).value=this.type===b._null?null:this.type===b._true,t.raw=this.type.keyword,this.next(),this.finishNode(t,"Literal");case b.parenL:var u=this.start,c=this.parseParenAndDistinguishExpression(i);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(e.parenthesizedAssign=u),e.parenthesizedBind<0&&(e.parenthesizedBind=u)),c;case b.bracketL:return t=this.startNode(),this.next(),t.elements=this.parseExprList(b.bracketR,!0,!0,e),this.finishNode(t,"ArrayExpression");case b.braceL:return this.parseObj(!1,e);case b._function:return t=this.startNode(),this.next(),this.parseFunction(t,0);case b._class:return this.parseClass(this.startNode(),!1);case b._new:return this.parseNew();case b.backQuote:return this.parseTemplate();case b._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},de.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case b.parenL:return this.parseDynamicImport(e);case b.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},de.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(b.parenR)){var t=this.start;this.eat(b.comma)&&this.eat(b.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},de.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"!==this.options.sourceType&&this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},de.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},de.parseParenExpression=function(){this.expect(b.parenL);var e=this.parseExpression();return this.expect(b.parenR),e},de.parseParenAndDistinguishExpression=function(e){var t,i=this.start,n=this.startLoc,s=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var r,a=this.start,o=this.startLoc,u=[],c=!0,p=!1,l=new se,h=this.yieldPos,d=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==b.parenR;){if(c?c=!1:this.expect(b.comma),s&&this.afterTrailingComma(b.parenR,!0)){p=!0;break}if(this.type===b.ellipsis){r=this.start,u.push(this.parseParenItem(this.parseRestBinding())),this.type===b.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}u.push(this.parseMaybeAssign(!1,l,this.parseParenItem))}var f=this.start,m=this.startLoc;if(this.expect(b.parenR),e&&!this.canInsertSemicolon()&&this.eat(b.arrow))return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=h,this.awaitPos=d,this.parseParenArrowList(i,n,u);u.length&&!p||this.unexpected(this.lastTokStart),r&&this.unexpected(r),this.checkExpressionErrors(l,!0),this.yieldPos=h||this.yieldPos,this.awaitPos=d||this.awaitPos,u.length>1?((t=this.startNodeAt(a,o)).expressions=u,this.finishNodeAt(t,"SequenceExpression",f,m)):t=u[0]}else t=this.parseParenExpression();if(this.options.preserveParens){var g=this.startNodeAt(i,n);return g.expression=t,this.finishNode(g,"ParenthesizedExpression")}return t},de.parseParenItem=function(e){return e},de.parseParenArrowList=function(e,t,i){return this.parseArrowExpression(this.startNodeAt(e,t),i)};var fe=[];de.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(b.dot)){e.meta=t;var i=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.inNonArrowFunction()||this.raiseRecoverable(e.start,"'new.target' can only be used in functions"),this.finishNode(e,"MetaProperty")}var n=this.start,s=this.startLoc,r=this.type===b._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),n,s,!0),r&&"ImportExpression"===e.callee.type&&this.raise(n,"Cannot use new with import()"),this.eat(b.parenL)?e.arguments=this.parseExprList(b.parenR,this.options.ecmaVersion>=8,!1):e.arguments=fe,this.finishNode(e,"NewExpression")},de.parseTemplateElement=function(e){var t=e.isTagged,i=this.startNode();return this.type===b.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value,cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),i.tail=this.type===b.backQuote,this.finishNode(i,"TemplateElement")},de.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var i=this.startNode();this.next(),i.expressions=[];var n=this.parseTemplateElement({isTagged:t});for(i.quasis=[n];!n.tail;)this.type===b.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(b.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(b.braceR),i.quasis.push(n=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(i,"TemplateLiteral")},de.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===b.name||this.type===b.num||this.type===b.string||this.type===b.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===b.star)&&!E.test(this.input.slice(this.lastTokEnd,this.start))},de.parseObj=function(e,t){var i=this.startNode(),n=!0,s={};for(i.properties=[],this.next();!this.eat(b.braceR);){if(n)n=!1;else if(this.expect(b.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(b.braceR))break;var r=this.parseProperty(e,t);e||this.checkPropClash(r,s,t),i.properties.push(r)}return this.finishNode(i,e?"ObjectPattern":"ObjectExpression")},de.parseProperty=function(e,t){var i,n,s,r,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(b.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===b.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(this.type===b.parenL&&t&&(t.parenthesizedAssign<0&&(t.parenthesizedAssign=this.start),t.parenthesizedBind<0&&(t.parenthesizedBind=this.start)),a.argument=this.parseMaybeAssign(!1,t),this.type===b.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(s=this.start,r=this.startLoc),e||(i=this.eat(b.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(a)?(n=!0,i=this.options.ecmaVersion>=9&&this.eat(b.star),this.parsePropertyName(a,t)):n=!1,this.parsePropertyValue(a,e,i,n,s,r,t,o),this.finishNode(a,"Property")},de.parsePropertyValue=function(e,t,i,n,s,r,a,o){if((i||n)&&this.type===b.colon&&this.unexpected(),this.eat(b.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===b.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(i,n);else if(t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===b.comma||this.type===b.braceR||this.type===b.eq)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((i||n)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=s),e.kind="init",t?e.value=this.parseMaybeDefault(s,r,e.key):this.type===b.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(s,r,e.key)):e.value=e.key,e.shorthand=!0):this.unexpected();else{(i||n)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var u="get"===e.kind?0:1;if(e.value.params.length!==u){var c=e.value.start;"get"===e.kind?this.raiseRecoverable(c,"getter should have no params"):this.raiseRecoverable(c,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},de.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(b.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(b.bracketR),e.key;e.computed=!1}return e.key=this.type===b.num||this.type===b.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},de.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},de.parseMethod=function(e,t,i){var n=this.startNode(),s=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(n),this.options.ecmaVersion>=6&&(n.generator=e),this.options.ecmaVersion>=8&&(n.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(G(t,n.generator)|W|(i?H:0)),this.expect(b.parenL),n.params=this.parseBindingList(b.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(n,!1,!0),this.yieldPos=s,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(n,"FunctionExpression")},de.parseArrowExpression=function(e,t,i){var n=this.yieldPos,s=this.awaitPos,r=this.awaitIdentPos;return this.enterScope(G(i,!1)|U),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1),this.yieldPos=n,this.awaitPos=s,this.awaitIdentPos=r,this.finishNode(e,"ArrowFunctionExpression")},de.parseFunctionBody=function(e,t,i){var n=t&&this.type!==b.braceL,s=this.strict,r=!1;if(n)e.body=this.parseMaybeAssign(),e.expression=!0,this.checkParams(e,!1);else{var a=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);s&&!a||(r=this.strictDirective(this.end))&&a&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var o=this.labels;this.labels=[],r&&(this.strict=!0),this.checkParams(e,!s&&!r&&!t&&!i&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLVal(e.id,Z),e.body=this.parseBlock(!1,void 0,r&&!s),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=o}this.exitScope()},de.isSimpleParamList=function(e){for(var t=0,i=e;t<i.length;t+=1)if("Identifier"!==i[t].type)return!1;return!0},de.checkParams=function(e,t){for(var i={},n=0,s=e.params;n<s.length;n+=1){var r=s[n];this.checkLVal(r,K,t?null:i)}},de.parseExprList=function(e,t,i,n){for(var s=[],r=!0;!this.eat(e);){if(r)r=!1;else if(this.expect(b.comma),t&&this.afterTrailingComma(e))break;var a=void 0;i&&this.type===b.comma?a=null:this.type===b.ellipsis?(a=this.parseSpread(n),n&&this.type===b.comma&&n.trailingComma<0&&(n.trailingComma=this.start)):a=this.parseMaybeAssign(!1,n),s.push(a)}return s},de.checkUnreserved=function(e){var t=e.start,i=e.end,n=e.name;this.inGenerator&&"yield"===n&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===n&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.keywords.test(n)&&this.raise(t,"Unexpected keyword '"+n+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,i).indexOf("\\")||(this.strict?this.reservedWordsStrict:this.reservedWords).test(n)&&(this.inAsync||"await"!==n||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+n+"' is reserved"))},de.parseIdent=function(e,t){var i=this.startNode();return this.type===b.name?i.name=this.value:this.type.keyword?(i.name=this.type.keyword,"class"!==i.name&&"function"!==i.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(i,"Identifier"),e||(this.checkUnreserved(i),"await"!==i.name||this.awaitIdentPos||(this.awaitIdentPos=i.start)),i},de.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===b.semi||this.canInsertSemicolon()||this.type!==b.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(b.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},de.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var e=this.startNode();return this.next(),e.argument=this.parseMaybeUnary(null,!1),this.finishNode(e,"AwaitExpression")};var me=ee.prototype;me.raise=function(e,t){var i=T(this.input,e);t+=" ("+i.line+":"+i.column+")";var n=new SyntaxError(t);throw n.pos=e,n.loc=i,n.raisedAt=this.pos,n},me.raiseRecoverable=me.raise,me.curPosition=function(){if(this.options.locations)return new P(this.curLine,this.pos-this.lineStart)};var ge=ee.prototype,ve=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[]};ge.enterScope=function(e){this.scopeStack.push(new ve(e))},ge.exitScope=function(){this.scopeStack.pop()},ge.treatFunctionsAsVarInScope=function(e){return e.flags&M||!this.inModule&&e.flags&O},ge.declareName=function(e,t,i){var n=!1;if(t===Y){var s=this.currentScope();n=s.lexical.indexOf(e)>-1||s.functions.indexOf(e)>-1||s.var.indexOf(e)>-1,s.lexical.push(e),this.inModule&&s.flags&O&&delete this.undefinedExports[e]}else if(t===X)this.currentScope().lexical.push(e);else if(t===J){var r=this.currentScope();n=this.treatFunctionsAsVar?r.lexical.indexOf(e)>-1:r.lexical.indexOf(e)>-1||r.var.indexOf(e)>-1,r.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(o.flags&z&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){n=!0;break}if(o.var.push(e),this.inModule&&o.flags&O&&delete this.undefinedExports[e],o.flags&j)break}n&&this.raiseRecoverable(i,"Identifier '"+e+"' has already been declared")},ge.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},ge.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},ge.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&j)return t}},ge.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&j&&!(t.flags&U))return t}};var xe=function(e,t,i){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new N(e,i)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},ye=ee.prototype;function be(e,t,i,n){return e.type=t,e.end=i,this.options.locations&&(e.loc.end=n),this.options.ranges&&(e.range[1]=i),e}ye.startNode=function(){return new xe(this,this.start,this.startLoc)},ye.startNodeAt=function(e,t){return new xe(this,e,t)},ye.finishNode=function(e,t){return be.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},ye.finishNodeAt=function(e,t,i,n){return be.call(this,e,t,i,n)};var Ee=function(e,t,i,n,s){this.token=e,this.isExpr=!!t,this.preserveSpace=!!i,this.override=n,this.generator=!!s},Ce={b_stat:new Ee("{",!1),b_expr:new Ee("{",!0),b_tmpl:new Ee("${",!1),p_stat:new Ee("(",!1),p_expr:new Ee("(",!0),q_tmpl:new Ee("`",!0,!0,(function(e){return e.tryReadTemplateToken()})),f_stat:new Ee("function",!1),f_expr:new Ee("function",!0),f_expr_gen:new Ee("function",!0,!1,null,!0),f_gen:new Ee("function",!1,!1,null,!0)},De=ee.prototype;De.initialContext=function(){return[Ce.b_stat]},De.braceIsBlock=function(e){var t=this.curContext();return t===Ce.f_expr||t===Ce.f_stat||(e!==b.colon||t!==Ce.b_stat&&t!==Ce.b_expr?e===b._return||e===b.name&&this.exprAllowed?E.test(this.input.slice(this.lastTokEnd,this.start)):e===b._else||e===b.semi||e===b.eof||e===b.parenR||e===b.arrow||(e===b.braceL?t===Ce.b_stat:e!==b._var&&e!==b._const&&e!==b.name&&!this.exprAllowed):!t.isExpr)},De.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},De.updateContext=function(e){var t,i=this.type;i.keyword&&e===b.dot?this.exprAllowed=!1:(t=i.updateContext)?t.call(this,e):this.exprAllowed=i.beforeExpr},b.parenR.updateContext=b.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===Ce.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},b.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?Ce.b_stat:Ce.b_expr),this.exprAllowed=!0},b.dollarBraceL.updateContext=function(){this.context.push(Ce.b_tmpl),this.exprAllowed=!0},b.parenL.updateContext=function(e){var t=e===b._if||e===b._for||e===b._with||e===b._while;this.context.push(t?Ce.p_stat:Ce.p_expr),this.exprAllowed=!0},b.incDec.updateContext=function(){},b._function.updateContext=b._class.updateContext=function(e){!e.beforeExpr||e===b.semi||e===b._else||e===b._return&&E.test(this.input.slice(this.lastTokEnd,this.start))||(e===b.colon||e===b.braceL)&&this.curContext()===Ce.b_stat?this.context.push(Ce.f_stat):this.context.push(Ce.f_expr),this.exprAllowed=!1},b.backQuote.updateContext=function(){this.curContext()===Ce.q_tmpl?this.context.pop():this.context.push(Ce.q_tmpl),this.exprAllowed=!1},b.star.updateContext=function(e){if(e===b._function){var t=this.context.length-1;this.context[t]===Ce.f_expr?this.context[t]=Ce.f_expr_gen:this.context[t]=Ce.f_gen}this.exprAllowed=!0},b.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==b.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var Ae="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",we=Ae+" Extended_Pictographic",Fe=we,ke={9:Ae,10:we,11:Fe},Se="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",_e="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",Be=_e+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Ie=Be+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Pe={9:_e,10:Be,11:Ie},Ne={};function Te(e){var t=Ne[e]={binary:I(ke[e]+" "+Se),nonBinary:{General_Category:I(Se),Script:I(Pe[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}Te(9),Te(10),Te(11);var Le=ee.prototype,Ve=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":""),this.unicodeProperties=Ne[e.options.ecmaVersion>=11?11:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function Re(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}function Oe(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function Me(e){return h(e,!0)||36===e||95===e}function je(e){return d(e,!0)||36===e||95===e||8204===e||8205===e}function qe(e){return e>=65&&e<=90||e>=97&&e<=122}function $e(e){return e>=0&&e<=1114111}function Ue(e){return 100===e||68===e||115===e||83===e||119===e||87===e}function ze(e){return qe(e)||95===e}function We(e){return ze(e)||He(e)}function He(e){return e>=48&&e<=57}function Ge(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function Qe(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function Ke(e){return e>=48&&e<=55}Ve.prototype.reset=function(e,t,i){var n=-1!==i.indexOf("u");this.start=0|e,this.source=t+"",this.flags=i,this.switchU=n&&this.parser.options.ecmaVersion>=6,this.switchN=n&&this.parser.options.ecmaVersion>=9},Ve.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},Ve.prototype.at=function(e,t){void 0===t&&(t=!1);var i=this.source,n=i.length;if(e>=n)return-1;var s=i.charCodeAt(e);if(!t&&!this.switchU||s<=55295||s>=57344||e+1>=n)return s;var r=i.charCodeAt(e+1);return r>=56320&&r<=57343?(s<<10)+r-56613888:s},Ve.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var i=this.source,n=i.length;if(e>=n)return n;var s,r=i.charCodeAt(e);return!t&&!this.switchU||r<=55295||r>=57344||e+1>=n||(s=i.charCodeAt(e+1))<56320||s>57343?e+1:e+2},Ve.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},Ve.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},Ve.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},Ve.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},Le.validateRegExpFlags=function(e){for(var t=e.validFlags,i=e.flags,n=0;n<i.length;n++){var s=i.charAt(n);-1===t.indexOf(s)&&this.raise(e.start,"Invalid regular expression flag"),i.indexOf(s,n+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},Le.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},Le.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,i=e.backReferenceNames;t<i.length;t+=1){var n=i[t];-1===e.groupNames.indexOf(n)&&e.raise("Invalid named capture referenced")}},Le.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},Le.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},Le.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},Le.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!i,!0}return e.pos=t,!1},Le.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},Le.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},Le.regexp_eatBracedQuantifier=function(e,t){var i=e.pos;if(e.eat(123)){var n=0,s=-1;if(this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue),e.eat(125)))return-1!==s&&s<n&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=i}return!1},Le.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},Le.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},Le.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},Le.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},Le.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},Le.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},Le.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!Oe(t)&&(e.lastIntValue=t,e.advance(),!0)},Le.regexp_eatPatternCharacters=function(e){for(var t=e.pos,i=0;-1!==(i=e.current())&&!Oe(i);)e.advance();return e.pos!==t},Le.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t||(e.advance(),0))},Le.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e))return-1!==e.groupNames.indexOf(e.lastStringValue)&&e.raise("Duplicate capture group name"),void e.groupNames.push(e.lastStringValue);e.raise("Invalid group")}},Le.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},Le.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=Re(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=Re(e.lastIntValue);return!0}return!1},Le.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,n=e.current(i);return e.advance(i),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(n=e.lastIntValue),Me(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},Le.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,n=e.current(i);return e.advance(i),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(n=e.lastIntValue),je(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},Le.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},Le.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var i=e.lastIntValue;if(e.switchU)return i>e.maxBackReference&&(e.maxBackReference=i),!0;if(i<=e.numCapturingParens)return!0;e.pos=t}return!1},Le.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},Le.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},Le.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},Le.regexp_eatZero=function(e){return 48===e.current()&&!He(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},Le.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},Le.regexp_eatControlLetter=function(e){var t=e.current();return!!qe(t)&&(e.lastIntValue=t%32,e.advance(),!0)},Le.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var i=e.pos,n=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var s=e.lastIntValue;if(n&&s>=55296&&s<=56319){var r=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(a>=56320&&a<=57343)return e.lastIntValue=1024*(s-55296)+(a-56320)+65536,!0}e.pos=r,e.lastIntValue=s}return!0}if(n&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&$e(e.lastIntValue))return!0;n&&e.raise("Invalid unicode escape"),e.pos=i}return!1},Le.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t||(e.lastIntValue=t,e.advance(),0))},Le.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1},Le.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(Ue(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(80===t||112===t)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1},Le.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var i=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,i,n),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,s),!0}return!1},Le.regexp_validateUnicodePropertyNameAndValue=function(e,t,i){_(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(i)||e.raise("Invalid property value")},Le.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},Le.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";ze(t=e.current());)e.lastStringValue+=Re(t),e.advance();return""!==e.lastStringValue},Le.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";We(t=e.current());)e.lastStringValue+=Re(t),e.advance();return""!==e.lastStringValue},Le.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},Le.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},Le.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var i=e.lastIntValue;!e.switchU||-1!==t&&-1!==i||e.raise("Invalid character class"),-1!==t&&-1!==i&&t>i&&e.raise("Range out of order in character class")}}},Le.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var i=e.current();(99===i||Ke(i))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var n=e.current();return 93!==n&&(e.lastIntValue=n,e.advance(),!0)},Le.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},Le.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!He(t)&&95!==t||(e.lastIntValue=t%32,e.advance(),0))},Le.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},Le.regexp_eatDecimalDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;He(i=e.current());)e.lastIntValue=10*e.lastIntValue+(i-48),e.advance();return e.pos!==t},Le.regexp_eatHexDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;Ge(i=e.current());)e.lastIntValue=16*e.lastIntValue+Qe(i),e.advance();return e.pos!==t},Le.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var i=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*i+e.lastIntValue:e.lastIntValue=8*t+i}else e.lastIntValue=t;return!0}return!1},Le.regexp_eatOctalDigit=function(e){var t=e.current();return Ke(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},Le.regexp_eatFixedHexDigits=function(e,t){var i=e.pos;e.lastIntValue=0;for(var n=0;n<t;++n){var s=e.current();if(!Ge(s))return e.pos=i,!1;e.lastIntValue=16*e.lastIntValue+Qe(s),e.advance()}return!0};var Ye=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new N(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},Je=ee.prototype;function Xe(e,t){return t?parseInt(e,8):parseFloat(e.replace(/_/g,""))}function Ze(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}function et(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}Je.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new Ye(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},Je.getToken=function(){return this.next(),new Ye(this)},"undefined"!=typeof Symbol&&(Je[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===b.eof,value:t}}}}),Je.curContext=function(){return this.context[this.context.length-1]},Je.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(b.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},Je.readToken=function(e){return h(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},Je.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);return e<=55295||e>=57344?e:(e<<10)+this.input.charCodeAt(this.pos+1)-56613888},Je.skipBlockComment=function(){var e,t=this.options.onComment&&this.curPosition(),i=this.pos,n=this.input.indexOf("*/",this.pos+=2);if(-1===n&&this.raise(this.pos-2,"Unterminated comment"),this.pos=n+2,this.options.locations)for(C.lastIndex=i;(e=C.exec(this.input))&&e.index<this.pos;)++this.curLine,this.lineStart=e.index+e[0].length;this.options.onComment&&this.options.onComment(!0,this.input.slice(i+2,n),i,this.pos,t,this.curPosition())},Je.skipLineComment=function(e){for(var t=this.pos,i=this.options.onComment&&this.curPosition(),n=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!D(n);)n=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,i,this.curPosition())},Je.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&A.test(String.fromCharCode(e))))break e;++this.pos}}},Je.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=e,this.value=t,this.updateContext(i)},Je.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(b.ellipsis)):(++this.pos,this.finishToken(b.dot))},Je.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(b.assign,2):this.finishOp(b.slash,1)},Je.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),i=1,n=42===e?b.star:b.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++i,n=b.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(b.assign,i+1):this.finishOp(n,i)},Je.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.options.ecmaVersion>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(b.assign,3):this.finishOp(124===e?b.logicalOR:b.logicalAND,2):61===t?this.finishOp(b.assign,2):this.finishOp(124===e?b.bitwiseOR:b.bitwiseAND,1)},Je.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(b.assign,2):this.finishOp(b.bitwiseXOR,1)},Je.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!E.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(b.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(b.assign,2):this.finishOp(b.plusMin,1)},Je.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),i=1;return t===e?(i=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+i)?this.finishOp(b.assign,i+1):this.finishOp(b.bitShift,i)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(i=2),this.finishOp(b.relational,i)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},Je.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(b.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(b.arrow)):this.finishOp(61===e?b.eq:b.prefix,1)},Je.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(b.questionDot,2)}if(63===t)return e>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(b.assign,3):this.finishOp(b.coalesce,2)}return this.finishOp(b.question,1)},Je.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(b.parenL);case 41:return++this.pos,this.finishToken(b.parenR);case 59:return++this.pos,this.finishToken(b.semi);case 44:return++this.pos,this.finishToken(b.comma);case 91:return++this.pos,this.finishToken(b.bracketL);case 93:return++this.pos,this.finishToken(b.bracketR);case 123:return++this.pos,this.finishToken(b.braceL);case 125:return++this.pos,this.finishToken(b.braceR);case 58:return++this.pos,this.finishToken(b.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(b.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(b.prefix,1)}this.raise(this.pos,"Unexpected character '"+et(e)+"'")},Je.finishOp=function(e,t){var i=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,i)},Je.readRegexp=function(){for(var e,t,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var n=this.input.charAt(this.pos);if(E.test(n)&&this.raise(i,"Unterminated regular expression"),e)e=!1;else{if("["===n)t=!0;else if("]"===n&&t)t=!1;else if("/"===n&&!t)break;e="\\"===n}++this.pos}var s=this.input.slice(i,this.pos);++this.pos;var r=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(r);var o=this.regexpState||(this.regexpState=new Ve(this));o.reset(i,s,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var u=null;try{u=new RegExp(s,a)}catch(e){}return this.finishToken(b.regexp,{pattern:s,flags:a,value:u})},Je.readInt=function(e,t,i){for(var n=this.options.ecmaVersion>=12&&void 0===t,s=i&&48===this.input.charCodeAt(this.pos),r=this.pos,a=0,o=0,u=0,c=null==t?1/0:t;u<c;++u,++this.pos){var p=this.input.charCodeAt(this.pos),l=void 0;if(n&&95===p)s&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===u&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=p;else{if((l=p>=97?p-97+10:p>=65?p-65+10:p>=48&&p<=57?p-48:1/0)>=e)break;o=p,a=a*e+l}}return n&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===r||null!=t&&this.pos-r!==t?null:a},Je.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var i=this.readInt(e);return null==i&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(i=Ze(this.input.slice(t,this.pos)),++this.pos):h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(b.num,i)},Je.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var i=this.pos-t>=2&&48===this.input.charCodeAt(t);i&&this.strict&&this.raise(t,"Invalid number");var n=this.input.charCodeAt(this.pos);if(!i&&!e&&this.options.ecmaVersion>=11&&110===n){var s=Ze(this.input.slice(t,this.pos));return++this.pos,h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(b.num,s)}i&&/[89]/.test(this.input.slice(t,this.pos))&&(i=!1),46!==n||i||(++this.pos,this.readInt(10),n=this.input.charCodeAt(this.pos)),69!==n&&101!==n||i||(43!==(n=this.input.charCodeAt(++this.pos))&&45!==n||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var r=Xe(this.input.slice(t,this.pos),i);return this.finishToken(b.num,r)},Je.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},Je.readString=function(e){for(var t="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var n=this.input.charCodeAt(this.pos);if(n===e)break;92===n?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(!1),i=this.pos):(D(n,this.options.ecmaVersion>=10)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(b.string,t)};var tt={};Je.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==tt)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},Je.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw tt;this.raise(e,t)},Je.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==b.template&&this.type!==b.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(b.template,e)):36===i?(this.pos+=2,this.finishToken(b.dollarBraceL)):(++this.pos,this.finishToken(b.backQuote));if(92===i)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(D(i)){switch(e+=this.input.slice(t,this.pos),++this.pos,i){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(i)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},Je.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(b.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},Je.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return et(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(e){var i=this.pos-1;return this.invalidStringToken(i,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var n=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],s=parseInt(n,8);return s>255&&(n=n.slice(0,-1),s=parseInt(n,8)),this.pos+=n.length-1,t=this.input.charCodeAt(this.pos),"0"===n&&56!==t&&57!==t||!this.strict&&!e||this.invalidStringToken(this.pos-1-n.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(s)}return D(t)?"":String.fromCharCode(t)}},Je.readHexChar=function(e){var t=this.pos,i=this.readInt(16,e);return null===i&&this.invalidStringToken(t,"Bad character escape sequence"),i},Je.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,n=this.options.ecmaVersion>=6;this.pos<this.input.length;){var s=this.fullCharCodeAtPos();if(d(s,n))this.pos+=s<=65535?1:2;else{if(92!==s)break;this.containsEsc=!0,e+=this.input.slice(i,this.pos);var r=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?h:d)(a,n)||this.invalidStringToken(r,"Invalid Unicode escape"),e+=et(a),i=this.pos}t=!1}return e+this.input.slice(i,this.pos)},Je.readWord=function(){var e=this.readWord1(),t=b.name;return this.keywords.test(e)&&(t=x[e]),this.finishToken(t,e)};var it="7.4.1";ee.acorn={Parser:ee,version:it,defaultOptions:L,Position:P,SourceLocation:N,getLineInfo:T,Node:xe,TokenType:f,tokTypes:b,keywordTypes:x,TokContext:Ee,tokContexts:Ce,isIdentifierChar:d,isIdentifierStart:h,Token:Ye,isNewLine:D,lineBreak:E,lineBreakG:C,nonASCIIwhitespace:A};var nt=new Set(["Array","ArrayBuffer","atob","AudioContext","Blob","Boolean","BigInt","btoa","clearInterval","clearTimeout","console","crypto","CustomEvent","DataView","Date","decodeURI","decodeURIComponent","devicePixelRatio","document","encodeURI","encodeURIComponent","Error","escape","eval","fetch","File","FileList","FileReader","Float32Array","Float64Array","Function","Headers","Image","ImageData","Infinity","Int16Array","Int32Array","Int8Array","Intl","isFinite","isNaN","JSON","Map","Math","NaN","Number","navigator","Object","parseFloat","parseInt","performance","Path2D","Promise","Proxy","RangeError","ReferenceError","Reflect","RegExp","cancelAnimationFrame","requestAnimationFrame","Set","setInterval","setTimeout","String","Symbol","SyntaxError","TextDecoder","TextEncoder","this","TypeError","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","undefined","unescape","URIError","URL","WeakMap","WeakSet","WebSocket","Worker","window"]);function st(e,t,i,n,s){i||(i=pt),function e(n,s,r){var a=r||n.type,o=t[a];i[a](n,s,e),o&&o(n,s)}(e,n,s)}function rt(e,t,i,n,s){var r=[];i||(i=pt),function e(n,s,a){var o=a||n.type,u=t[o],c=n!==r[r.length-1];c&&r.push(n),i[o](n,s,e),u&&u(n,s||r,r),c&&r.pop()}(e,n,s)}var at=Object.create||function(e){function t(){}return t.prototype=e,new t};function ot(e,t){var i=at(t||pt);for(var n in e)i[n]=e[n];return i}function ut(e,t,i){i(e,t)}function ct(e,t,i){}var pt={};pt.Program=pt.BlockStatement=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1)i(s[n],t,"Statement")},pt.Statement=ut,pt.EmptyStatement=ct,pt.ExpressionStatement=pt.ParenthesizedExpression=pt.ChainExpression=function(e,t,i){return i(e.expression,t,"Expression")},pt.IfStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Statement"),e.alternate&&i(e.alternate,t,"Statement")},pt.LabeledStatement=function(e,t,i){return i(e.body,t,"Statement")},pt.BreakStatement=pt.ContinueStatement=ct,pt.WithStatement=function(e,t,i){i(e.object,t,"Expression"),i(e.body,t,"Statement")},pt.SwitchStatement=function(e,t,i){i(e.discriminant,t,"Expression");for(var n=0,s=e.cases;n<s.length;n+=1){var r=s[n];r.test&&i(r.test,t,"Expression");for(var a=0,o=r.consequent;a<o.length;a+=1)i(o[a],t,"Statement")}},pt.SwitchCase=function(e,t,i){e.test&&i(e.test,t,"Expression");for(var n=0,s=e.consequent;n<s.length;n+=1)i(s[n],t,"Statement")},pt.ReturnStatement=pt.YieldExpression=pt.AwaitExpression=function(e,t,i){e.argument&&i(e.argument,t,"Expression")},pt.ThrowStatement=pt.SpreadElement=function(e,t,i){return i(e.argument,t,"Expression")},pt.TryStatement=function(e,t,i){i(e.block,t,"Statement"),e.handler&&i(e.handler,t),e.finalizer&&i(e.finalizer,t,"Statement")},pt.CatchClause=function(e,t,i){e.param&&i(e.param,t,"Pattern"),i(e.body,t,"Statement")},pt.WhileStatement=pt.DoWhileStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.body,t,"Statement")},pt.ForStatement=function(e,t,i){e.init&&i(e.init,t,"ForInit"),e.test&&i(e.test,t,"Expression"),e.update&&i(e.update,t,"Expression"),i(e.body,t,"Statement")},pt.ForInStatement=pt.ForOfStatement=function(e,t,i){i(e.left,t,"ForInit"),i(e.right,t,"Expression"),i(e.body,t,"Statement")},pt.ForInit=function(e,t,i){"VariableDeclaration"===e.type?i(e,t):i(e,t,"Expression")},pt.DebuggerStatement=ct,pt.FunctionDeclaration=function(e,t,i){return i(e,t,"Function")},pt.VariableDeclaration=function(e,t,i){for(var n=0,s=e.declarations;n<s.length;n+=1)i(s[n],t)},pt.VariableDeclarator=function(e,t,i){i(e.id,t,"Pattern"),e.init&&i(e.init,t,"Expression")},pt.Function=function(e,t,i){e.id&&i(e.id,t,"Pattern");for(var n=0,s=e.params;n<s.length;n+=1)i(s[n],t,"Pattern");i(e.body,t,e.expression?"Expression":"Statement")},pt.Pattern=function(e,t,i){"Identifier"===e.type?i(e,t,"VariablePattern"):"MemberExpression"===e.type?i(e,t,"MemberPattern"):i(e,t)},pt.VariablePattern=ct,pt.MemberPattern=ut,pt.RestElement=function(e,t,i){return i(e.argument,t,"Pattern")},pt.ArrayPattern=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Pattern")}},pt.ObjectPattern=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];"Property"===r.type?(r.computed&&i(r.key,t,"Expression"),i(r.value,t,"Pattern")):"RestElement"===r.type&&i(r.argument,t,"Pattern")}},pt.Expression=ut,pt.ThisExpression=pt.Super=pt.MetaProperty=ct,pt.ArrayExpression=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Expression")}},pt.ObjectExpression=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1)i(s[n],t)},pt.FunctionExpression=pt.ArrowFunctionExpression=pt.FunctionDeclaration,pt.SequenceExpression=function(e,t,i){for(var n=0,s=e.expressions;n<s.length;n+=1)i(s[n],t,"Expression")},pt.TemplateLiteral=function(e,t,i){for(var n=0,s=e.quasis;n<s.length;n+=1)i(s[n],t);for(var r=0,a=e.expressions;r<a.length;r+=1)i(a[r],t,"Expression")},pt.TemplateElement=ct,pt.UnaryExpression=pt.UpdateExpression=function(e,t,i){i(e.argument,t,"Expression")},pt.BinaryExpression=pt.LogicalExpression=function(e,t,i){i(e.left,t,"Expression"),i(e.right,t,"Expression")},pt.AssignmentExpression=pt.AssignmentPattern=function(e,t,i){i(e.left,t,"Pattern"),i(e.right,t,"Expression")},pt.ConditionalExpression=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Expression"),i(e.alternate,t,"Expression")},pt.NewExpression=pt.CallExpression=function(e,t,i){if(i(e.callee,t,"Expression"),e.arguments)for(var n=0,s=e.arguments;n<s.length;n+=1)i(s[n],t,"Expression")},pt.MemberExpression=function(e,t,i){i(e.object,t,"Expression"),e.computed&&i(e.property,t,"Expression")},pt.ExportNamedDeclaration=pt.ExportDefaultDeclaration=function(e,t,i){e.declaration&&i(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&i(e.source,t,"Expression")},pt.ExportAllDeclaration=function(e,t,i){e.exported&&i(e.exported,t),i(e.source,t,"Expression")},pt.ImportDeclaration=function(e,t,i){for(var n=0,s=e.specifiers;n<s.length;n+=1)i(s[n],t);i(e.source,t,"Expression")},pt.ImportExpression=function(e,t,i){i(e.source,t,"Expression")},pt.ImportSpecifier=pt.ImportDefaultSpecifier=pt.ImportNamespaceSpecifier=pt.Identifier=pt.Literal=ct,pt.TaggedTemplateExpression=function(e,t,i){i(e.tag,t,"Expression"),i(e.quasi,t,"Expression")},pt.ClassDeclaration=pt.ClassExpression=function(e,t,i){return i(e,t,"Class")},pt.Class=function(e,t,i){e.id&&i(e.id,t,"Pattern"),e.superClass&&i(e.superClass,t,"Expression"),i(e.body,t)},pt.ClassBody=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1)i(s[n],t)},pt.MethodDefinition=pt.Property=function(e,t,i){e.computed&&i(e.key,t,"Expression"),i(e.value,t,"Expression")};var lt=ot({Import(){},ViewExpression(e,t,i){i(e.id,t,"Identifier")},MutableExpression(e,t,i){i(e.id,t,"Identifier")}});function ht(e){return"FunctionExpression"===e.type||"FunctionDeclaration"===e.type||"ArrowFunctionExpression"===e.type||"Program"===e.type}function dt(e){return"BlockStatement"===e.type||"ForInStatement"===e.type||"ForOfStatement"===e.type||"ForStatement"===e.type||ht(e)}function ft(e){return"FunctionExpression"===e.type||"FunctionDeclaration"===e.type}function mt(e,t){const i={type:"Program",body:[e.body]},n=new Map,s=new Set(t),r=[];function a(e,t){const i=n.get(e);return!!i&&i.has(t)}function o(e,t){const i=n.get(e);i?i.add(t.name):n.set(e,new Set([t.name]))}function u(e){e.id&&o(e,e.id)}function c(e){e.params.forEach((t=>l(t,e))),e.id&&o(e,e.id)}function p(e){e.param&&l(e.param,e)}function l(e,t){switch(e.type){case"Identifier":o(t,e);break;case"ObjectPattern":e.properties.forEach((e=>l(e,t)));break;case"ArrayPattern":e.elements.forEach((e=>e&&l(e,t)));break;case"Property":l(e.value,t);break;case"RestElement":l(e.argument,t);break;case"AssignmentPattern":l(e.left,t);break;default:throw new Error("Unrecognized pattern type: "+e.type)}}function h(e){o(i,e.local)}function d(e,t){let i=e.name;if("undefined"!==i){for(let n=t.length-2;n>=0;--n){if("arguments"===i&&ft(t[n]))return;if(a(t[n],i))return;"ViewExpression"===t[n].type&&(i=`viewof ${(e=t[n]).id.name}`),"MutableExpression"===t[n].type&&(i=`mutable ${(e=t[n]).id.name}`)}if(!s.has(i)){if("arguments"===i)throw Object.assign(new SyntaxError("arguments is not allowed"),{node:e});r.push(e)}}}function f(e,t){switch(e.type){case"Identifier":case"VariablePattern":i(e,t);break;case"ArrayPattern":case"ObjectPattern":rt(e,{Identifier:i,VariablePattern:i},lt)}function i(e,i){for(const i of t)if(a(i,e.name))return;if("MutableExpression"!==i[i.length-2].type)throw Object.assign(new SyntaxError(`Assignment to constant variable ${e.name}`),{node:e})}}function m(e,t){f(e.argument,t)}function g(e,t){f(e.left,t)}return rt(i,{VariableDeclaration:(e,t)=>{let i=null;for(let n=t.length-1;n>=0&&null===i;--n)("var"===e.kind?ht(t[n]):dt(t[n]))&&(i=t[n]);e.declarations.forEach((e=>l(e.id,i)))},FunctionDeclaration:(e,t)=>{let i=null;for(let e=t.length-2;e>=0&&null===i;--e)ht(t[e])&&(i=t[e]);o(i,e.id),c(e)},Function:c,ClassDeclaration:(e,t)=>{let i=null;for(let e=t.length-2;e>=0&&null===i;e--)ht(t[e])&&(i=t[e]);o(i,e.id)},Class:u,CatchClause:p,ImportDefaultSpecifier:h,ImportSpecifier:h,ImportNamespaceSpecifier:h},lt),rt(i,{VariablePattern:d,Identifier:d},lt),rt(i,{AssignmentExpression:g,UpdateExpression:m,ForOfStatement:g,ForInStatement:g},lt),r}function gt(e,t){const i={type:"Program",body:[e.body]},n=new Map,{references:s}=e;return st(i,{CallExpression:e=>{const{callee:i,arguments:r}=e;if("Identifier"!==i.type||i.name!==t||s.indexOf(i)<0)return;if(1!==r.length||!("Literal"===r[0].type&&/^['"]/.test(r[0].raw)||"TemplateLiteral"===r[0].type&&0===r[0].expressions.length))throw Object.assign(new SyntaxError(`${t} requires a single literal string argument`),{node:e});const[a]=r,o="Literal"===a.type?a.value:a.quasis[0].value.cooked,u={start:a.start,end:a.end};n.has(o)?n.get(o).push(u):n.set(o,[u])}},lt),n}const vt=2,xt=4,yt=8,bt=Symbol("start"),Et=Symbol("modifier"),Ct=Symbol("function"),Dt=Symbol("name");function At(e,{globals:t}={}){const i=Ft.parse(e);return _t(i,e,t),Bt(i),i}function wt(e){let t,i=bt;try{for(const n of ee.tokenizer(e,{ecmaVersion:11})){switch(i){case bt:case Et:if(n.type===b.name){if(i===bt&&("viewof"===n.value||"mutable"===n.value||"async"===n.value)){i=Et;continue}i=Dt,t=n;continue}if(n.type===b._function||n.type===b._class){i=Ct;continue}break;case Dt:if(n.type===b.eq)return t.value;break;case Ct:if(n.type===b.star)continue;if(n.type===b.name&&n.end<e.length)return n.value}return}}catch(e){return}}class Ft extends ee{constructor(e,...t){super(Object.assign({ecmaVersion:12},e),...t)}enterScope(e){return e&vt&&++this.O_function,super.enterScope(e)}exitScope(){return this.currentScope().flags&vt&&--this.O_function,super.exitScope()}parseForIn(e,t){return 1===this.O_function&&e.await&&(this.O_async=!0),super.parseForIn(e,t)}parseAwait(){return 1===this.O_function&&(this.O_async=!0),super.parseAwait()}parseYield(e){return 1===this.O_function&&(this.O_generator=!0),super.parseYield(e)}parseImport(e){return this.next(),e.specifiers=this.parseImportSpecifiers(),this.type===b._with&&(this.next(),e.injections=this.parseImportSpecifiers()),this.expectContextual("from"),e.source=this.type===b.string?this.parseExprAtom():this.unexpected(),this.finishNode(e,"ImportDeclaration")}parseImportSpecifiers(){const e=[],t=new Set;let i=!0;for(this.expect(b.braceL);!this.eat(b.braceR);){if(i)i=!1;else if(this.expect(b.comma),this.afterTrailingComma(b.braceR))break;const n=this.startNode();n.view=this.eatContextual("viewof"),n.mutable=!n.view&&this.eatContextual("mutable"),n.imported=this.parseIdent(),this.checkUnreserved(n.imported),this.checkLocal(n.imported),this.eatContextual("as")?(n.local=this.parseIdent(),this.checkUnreserved(n.local),this.checkLocal(n.local)):n.local=n.imported,this.checkLVal(n.local,"let"),t.has(n.local.name)&&this.raise(n.local.start,`Identifier '${n.local.name}' has already been declared`),t.add(n.local.name),e.push(this.finishNode(n,"ImportSpecifier"))}return e}parseExprAtom(e){return this.parseMaybeKeywordExpression("viewof","ViewExpression")||this.parseMaybeKeywordExpression("mutable","MutableExpression")||super.parseExprAtom(e)}parseCell(e,t){const i=new Ft({},this.input,this.start);let n=i.getToken(),s=null,r=null;return this.O_function=0,this.O_async=!1,this.O_generator=!1,this.strict=!0,this.enterScope(vt|xt|yt),n.type===b._import&&i.getToken().type!==b.parenL?s=this.parseImport(this.startNode()):n.type!==b.eof&&n.type!==b.semi&&(n.type===b.name&&("viewof"!==n.value&&"mutable"!==n.value||(n=i.getToken(),n.type!==b.name&&i.unexpected()),n=i.getToken(),n.type===b.eq&&(r=this.parseMaybeKeywordExpression("viewof","ViewExpression")||this.parseMaybeKeywordExpression("mutable","MutableExpression")||this.parseIdent(),n=i.getToken(),this.expect(b.eq))),n.type===b.braceL?s=this.parseBlock():(s=this.parseExpression(),null!==r||"FunctionExpression"!==s.type&&"ClassExpression"!==s.type||(r=s.id))),this.semicolon(),t&&this.expect(b.eof),r&&this.checkLocal(r),e.id=r,e.async=this.O_async,e.generator=this.O_generator,e.body=s,this.exitScope(),this.finishNode(e,"Cell")}parseTopLevel(e){return this.parseCell(e,!0)}toAssignable(e,t,i){return"MutableExpression"===e.type?e:super.toAssignable(e,t,i)}checkLocal(e){const t=e.id||e;(nt.has(t.name)||"arguments"===t.name)&&this.raise(t.start,`Identifier '${t.name}' is reserved`)}checkUnreserved(e){return"viewof"!==e.name&&"mutable"!==e.name||this.raise(e.start,`Unexpected keyword '${e.name}'`),super.checkUnreserved(e)}checkLVal(e,t,i){return super.checkLVal("MutableExpression"===e.type?e.id:e,t,i)}unexpected(e){this.raise(null!=e?e:this.start,this.type===b.eof?"Unexpected end of input":"Unexpected token")}parseMaybeKeywordExpression(e,t){if(this.isContextual(e)){const e=this.startNode();return this.next(),e.id=this.parseIdent(),this.finishNode(e,t)}}}function kt(e,{globals:t}={}){const i=St.parse(e);for(const n of i.cells)_t(n,e,t),Bt(n,e);return i}class St extends Ft{parseTopLevel(e){for(e.cells||(e.cells=[]);this.type!==b.eof;){const t=this.parseCell(this.startNode());t.input=this.input,e.cells.push(t)}return this.next(),this.finishNode(e,"Program")}}function _t(e,t,i=nt){if(e.body&&"ImportDeclaration"!==e.body.type)try{e.references=mt(e,i)}catch(e){if(e.node){const i=T(t,e.node.start);e.message+=` (${i.line}:${i.column})`,e.pos=e.node.start,e.loc=i,delete e.node}throw e}return e}function Bt(e,t){if(e.body&&"ImportDeclaration"!==e.body.type)try{e.fileAttachments=gt(e,"FileAttachment"),e.databaseClients=gt(e,"DatabaseClient"),e.secrets=gt(e,"Secret")}catch(e){if(e.node){const i=T(t,e.node.start);e.message+=` (${i.line}:${i.column})`,e.pos=e.node.start,e.loc=i,delete e.node}throw e}else e.fileAttachments=new Map,e.databaseClients=new Map,e.secrets=new Map;return e}var It=Object.freeze({__proto__:null,parseCell:At,peekId:wt,CellParser:Ft,parseModule:kt,ModuleParser:St,walk:lt});function Pt(e,t,i,n,s){i||(i=Lt),function e(n,s,r){var a=r||n.type;i[a](n,s,e),r||t(n,s,a)}(e,n,s)}function Nt(e,t,i){i(e,t)}function Tt(e,t,i){}var Lt={};Lt.Program=Lt.BlockStatement=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1)i(s[n],t,"Statement")},Lt.Statement=Nt,Lt.EmptyStatement=Tt,Lt.ExpressionStatement=Lt.ParenthesizedExpression=Lt.ChainExpression=function(e,t,i){return i(e.expression,t,"Expression")},Lt.IfStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Statement"),e.alternate&&i(e.alternate,t,"Statement")},Lt.LabeledStatement=function(e,t,i){return i(e.body,t,"Statement")},Lt.BreakStatement=Lt.ContinueStatement=Tt,Lt.WithStatement=function(e,t,i){i(e.object,t,"Expression"),i(e.body,t,"Statement")},Lt.SwitchStatement=function(e,t,i){i(e.discriminant,t,"Expression");for(var n=0,s=e.cases;n<s.length;n+=1){var r=s[n];r.test&&i(r.test,t,"Expression");for(var a=0,o=r.consequent;a<o.length;a+=1)i(o[a],t,"Statement")}},Lt.SwitchCase=function(e,t,i){e.test&&i(e.test,t,"Expression");for(var n=0,s=e.consequent;n<s.length;n+=1)i(s[n],t,"Statement")},Lt.ReturnStatement=Lt.YieldExpression=Lt.AwaitExpression=function(e,t,i){e.argument&&i(e.argument,t,"Expression")},Lt.ThrowStatement=Lt.SpreadElement=function(e,t,i){return i(e.argument,t,"Expression")},Lt.TryStatement=function(e,t,i){i(e.block,t,"Statement"),e.handler&&i(e.handler,t),e.finalizer&&i(e.finalizer,t,"Statement")},Lt.CatchClause=function(e,t,i){e.param&&i(e.param,t,"Pattern"),i(e.body,t,"Statement")},Lt.WhileStatement=Lt.DoWhileStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.body,t,"Statement")},Lt.ForStatement=function(e,t,i){e.init&&i(e.init,t,"ForInit"),e.test&&i(e.test,t,"Expression"),e.update&&i(e.update,t,"Expression"),i(e.body,t,"Statement")},Lt.ForInStatement=Lt.ForOfStatement=function(e,t,i){i(e.left,t,"ForInit"),i(e.right,t,"Expression"),i(e.body,t,"Statement")},Lt.ForInit=function(e,t,i){"VariableDeclaration"===e.type?i(e,t):i(e,t,"Expression")},Lt.DebuggerStatement=Tt,Lt.FunctionDeclaration=function(e,t,i){return i(e,t,"Function")},Lt.VariableDeclaration=function(e,t,i){for(var n=0,s=e.declarations;n<s.length;n+=1)i(s[n],t)},Lt.VariableDeclarator=function(e,t,i){i(e.id,t,"Pattern"),e.init&&i(e.init,t,"Expression")},Lt.Function=function(e,t,i){e.id&&i(e.id,t,"Pattern");for(var n=0,s=e.params;n<s.length;n+=1)i(s[n],t,"Pattern");i(e.body,t,e.expression?"Expression":"Statement")},Lt.Pattern=function(e,t,i){"Identifier"===e.type?i(e,t,"VariablePattern"):"MemberExpression"===e.type?i(e,t,"MemberPattern"):i(e,t)},Lt.VariablePattern=Tt,Lt.MemberPattern=Nt,Lt.RestElement=function(e,t,i){return i(e.argument,t,"Pattern")},Lt.ArrayPattern=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Pattern")}},Lt.ObjectPattern=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];"Property"===r.type?(r.computed&&i(r.key,t,"Expression"),i(r.value,t,"Pattern")):"RestElement"===r.type&&i(r.argument,t,"Pattern")}},Lt.Expression=Nt,Lt.ThisExpression=Lt.Super=Lt.MetaProperty=Tt,Lt.ArrayExpression=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Expression")}},Lt.ObjectExpression=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1)i(s[n],t)},Lt.FunctionExpression=Lt.ArrowFunctionExpression=Lt.FunctionDeclaration,Lt.SequenceExpression=function(e,t,i){for(var n=0,s=e.expressions;n<s.length;n+=1)i(s[n],t,"Expression")},Lt.TemplateLiteral=function(e,t,i){for(var n=0,s=e.quasis;n<s.length;n+=1)i(s[n],t);for(var r=0,a=e.expressions;r<a.length;r+=1)i(a[r],t,"Expression")},Lt.TemplateElement=Tt,Lt.UnaryExpression=Lt.UpdateExpression=function(e,t,i){i(e.argument,t,"Expression")},Lt.BinaryExpression=Lt.LogicalExpression=function(e,t,i){i(e.left,t,"Expression"),i(e.right,t,"Expression")},Lt.AssignmentExpression=Lt.AssignmentPattern=function(e,t,i){i(e.left,t,"Pattern"),i(e.right,t,"Expression")},Lt.ConditionalExpression=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Expression"),i(e.alternate,t,"Expression")},Lt.NewExpression=Lt.CallExpression=function(e,t,i){if(i(e.callee,t,"Expression"),e.arguments)for(var n=0,s=e.arguments;n<s.length;n+=1)i(s[n],t,"Expression")},Lt.MemberExpression=function(e,t,i){i(e.object,t,"Expression"),e.computed&&i(e.property,t,"Expression")},Lt.ExportNamedDeclaration=Lt.ExportDefaultDeclaration=function(e,t,i){e.declaration&&i(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&i(e.source,t,"Expression")},Lt.ExportAllDeclaration=function(e,t,i){e.exported&&i(e.exported,t),i(e.source,t,"Expression")},Lt.ImportDeclaration=function(e,t,i){for(var n=0,s=e.specifiers;n<s.length;n+=1)i(s[n],t);i(e.source,t,"Expression")},Lt.ImportExpression=function(e,t,i){i(e.source,t,"Expression")},Lt.ImportSpecifier=Lt.ImportDefaultSpecifier=Lt.ImportNamespaceSpecifier=Lt.Identifier=Lt.Literal=Tt,Lt.TaggedTemplateExpression=function(e,t,i){i(e.tag,t,"Expression"),i(e.quasi,t,"Expression")},Lt.ClassDeclaration=Lt.ClassExpression=function(e,t,i){return i(e,t,"Class")},Lt.Class=function(e,t,i){e.id&&i(e.id,t,"Pattern"),e.superClass&&i(e.superClass,t,"Expression"),i(e.body,t)},Lt.ClassBody=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1)i(s[n],t)},Lt.MethodDefinition=Lt.Property=function(e,t,i){e.computed&&i(e.key,t,"Expression"),i(e.value,t,"Expression")};const Vt=e=>{let t,i=e;return(t=/\.js(\?|$)/i.exec(i))&&(i=i.slice(0,t.index)),(t=/^[0-9a-f]{16}$/i.test(i))&&(i=`d/${i}`),(t=/^https:\/\/(api\.|beta\.|)observablehq\.com\//i.exec(i))&&(i=i.slice(t[0].length)),i};function Rt(e){const t=[];if(e.body.specifiers)for(const i of e.body.specifiers)i.view?t.push({name:"viewof "+i.imported.name,alias:"viewof "+i.local.name}):i.mutable&&t.push({name:"mutable "+i.imported.name,alias:"mutable "+i.local.name}),t.push({name:i.imported.name,alias:i.local.name});const i=void 0!==e.body.injections,n=[];if(i)for(const t of e.body.injections)t.view?n.push({name:"viewof "+t.imported.name,alias:"viewof "+t.local.name}):t.mutable&&n.push({name:"mutable "+t.imported.name,alias:"mutable "+t.local.name}),n.push({name:t.imported.name,alias:t.local.name});const s=`import {${t.map((e=>`${e.name} as ${e.alias}`)).join(", ")}} ${i?`with {${n.map((e=>`${e.name} as ${e.alias}`)).join(", ")}} `:""}from "${e.body.source.value}"`;return{specifiers:t,hasInjections:i,injections:n,importString:s}}function Ot(e){let t=null;e.id&&e.id.name?t=e.id.name:e.id&&e.id.id&&e.id.id.name&&(t=e.id.id.name);let i=e.input.substring(e.body.start,e.body.end),n={},s=[],r=0;const a=Array.from(new Set((e.references||[]).map((e=>{if("ViewExpression"===e.type){if(void 0===n[e.id.name]){const t="$"+r++;n[e.id.name]=t,s.push(t)}return"viewof "+e.id.name}if("MutableExpression"===e.type){if(void 0===n[e.id.name]){const t="$"+r++;n[e.id.name]=t,s.push(t)}return"mutable "+e.id.name}return s.push(e.name),e.name})))),o=e=>{const t=[],i=new Set;for(const n of e)i.has(n)||(i.add(n),t.push(n));return t},u=[];let c={newStr:"",span:[e.body.start,e.body.start]};return Pt(e.body,(t=>{if("ViewExpression"===t.type||"MutableExpression"===t.type){t.start!==c.span[1]&&u.push({newStr:e.input.substring(c.span[1],t.start)});const i="MutableExpression"===t.type?".value":"",s={newStr:`${n[t.id.name]}${i}`,span:[t.start,t.end]};c=s,u.push(s)}}),lt),u.push({newStr:e.input.substring(c.span[1],e.body.end),span:[c.span[1],e.body.end]}),i=u.map((e=>e.newStr)).join(""),{cellName:t,references:o(s),bodyText:i,cellReferences:o(a)}}function Mt(e){if(e.body&&e.body.specifiers)return e.body.specifiers.map((e=>`${e.view?"viewof ":e.mutable?"mutable ":""}${e.local.name}`));if(e.id&&e.id.type&&e.id){if("ViewExpression"===e.id.type)return[`viewof ${e.id.id.name}`];if("MutableExpression"===e.id.type)return[`mutable ${e.id.id.name}`];if(e.id.name)return[e.id.name]}return[]}function jt(e){return e.references?e.references.map((e=>e.name?e.name:"ViewExpression"===e.type?`viewof ${e.id.name}`:"MutableExpression"===e.type?`mutable ${e.id.name}`:null)):e.body&&e.body.injections?e.body.injections.map((e=>`${e.view?"viewof ":e.mutable?"mutable ":""}${e.imported.name}`)):[]}function qt(e){const t=[];for(const i of e.cells){const e=Mt(i),n=jt(i);if(e&&e.length)for(const i of e)t.push([i,n]),i.startsWith("viewof ")&&t.push([i.substring("viewof ".length),[i]])}return new Map(t)}function $t(e,t){const i=qt(e),n=new Set,s=t.slice();for(;s.length;){const e=s.pop();if(n.add(e),!i.has(e))continue;const t=i.get(e);for(const e of t)n.has(e)||s.push(e)}return{cells:e.cells.filter((e=>Mt(e).filter((e=>n.has(e))).length))}}function Ut(e,t){const i=new Map;let n="",s=0;for(const{body:r}of e.cells){if("ImportDeclaration"!==r.type||i.has(r.source.value))continue;const e="define"+ ++s,a=r.specifiers.map((e=>`${e.view?"viewof ":e.mutable?"mutable ":""}${e.imported.name}`)),o=t(r.source.value,a);i.set(r.source.value,{defineName:e,fromPath:o}),n+=`import ${e} from "${o}";\n`}return n.length&&(n+="\n"),{importSrc:n,importMap:i}}function zt(e,t,i=!1){let n;if(i){const i=[];for(const n of e.cells)if(0!==n.fileAttachments.size)for(const e of n.fileAttachments.keys())i.push([e,t(e)]);i.length&&(n=`[${i.map((([e,t])=>`[${JSON.stringify(e)}, ${t}]`)).join(",")}]`)}else{const i=[];for(const n of e.cells)if(0!==n.fileAttachments.size)for(const e of n.fileAttachments.keys())i.push([e,t(e)]);i.length&&(n=JSON.stringify(i))}return n?`  const fileAttachments = new Map(${n});\n  main.builtin("FileAttachment", runtime.fileAttachments(name => fileAttachments.get(name)));`:""}function Wt(e,t,i){const{defineImportMarkdown:n,observeViewofValues:s,observeMutableValues:r}=i;let a=0;return e.cells.map((e=>{let i="";if("ImportDeclaration"===e.body.type){const{specifiers:s,hasInjections:r,injections:o,importString:u}=Rt(e);n&&(i+=`  main.variable(observer()).define(\n    null,\n    ["md"],\n    md => md\`~~~javascript\n${u}\n~~~\`\n  );\n`);const c="child"+ ++a;i+=`  const ${c} = runtime.module(${t.get(e.body.source.value).defineName})${r?`.derive(${JSON.stringify(o)}, main)`:""};\n${s.map((e=>`  main.import("${e.name}", "${e.alias}", ${c});`)).join("\n")}`}else{const{cellName:t,references:n,bodyText:a,cellReferences:o}=Ot(e),u=t?`"${t}"`:"",c=n.join(",");let p="";p="BlockStatement"!==e.body.type?`{return(\n${a}\n)}`:"\n"+a+"\n";const l=o.length?JSON.stringify(o)+", ":"";let h="";if(h=e.generator&&e.async?`async function*(${c})${p}`:e.async?`async function(${c})${p}`:e.generator?`function*(${c})${p}`:`function(${c})${p}`,e.id&&"ViewExpression"===e.id.type){const e=`"viewof ${t}"`;i+=`  main.variable(observer(${e})).define(${e}, ${l}${h});\n  main.variable(${s?`observer("${t}")`:"null"}).define("${t}", ["Generators", ${e}], (G, _) => G.input(_));`}else if(e.id&&"MutableExpression"===e.id.type){const e=`"initial ${t}"`,n=`"mutable ${t}"`;i+=`  main.define(${e}, ${l}${h});\n  main.variable(observer(${n})).define(${n}, ["Mutable", ${e}], (M, _) => new M(_));\n  main.variable(${r?`observer("${t}")`:"null"}).define("${t}", [${n}], _ => _.generator);`}else i+=`  main.variable(observer(${u})).define(${t?u+", ":""}${l}${h});`}return i})).join("\n")}function Ht(e,t={}){const{resolveImportPath:i,resolveFileAttachments:n,defineImportMarkdown:s,observeViewofValues:r,observeMutableValues:a,UNSAFE_allowJavascriptFileAttachments:o}=t,{importSrc:u,importMap:c}=Ut(e,i);return`${u}export default function define(runtime, observer) {\n  const main = runtime.module();\n${zt(e,n,o)}\n${Wt(e,c,{defineImportMarkdown:s,observeViewofValues:r,observeMutableValues:a})||""}\n  return main;\n}`}function Gt(e){return`https://api.observablehq.com/${Vt(e)}.js?v=3`}function Qt(e){return e}class Kt{constructor(e={}){const{resolveFileAttachments:t=Qt,resolveImportPath:i=Gt,defineImportMarkdown:n=!0,observeViewofValues:s=!0,observeMutableValues:r=!0,UNSAFE_allowJavascriptFileAttachments:a=!1}=e;this.resolveFileAttachments=t,this.resolveImportPath=i,this.defineImportMarkdown=n,this.observeViewofValues=s,this.observeMutableValues=r,this.UNSAFE_allowJavascriptFileAttachments=a}module(e,t={}){let i="string"==typeof e?kt(e):e;return t.treeShake&&(i=$t(i,t.treeShake)),Ht(i,{resolveImportPath:this.resolveImportPath,resolveFileAttachments:this.resolveFileAttachments,defineImportMarkdown:this.defineImportMarkdown,observeViewofValues:this.observeViewofValues,observeMutableValues:this.observeMutableValues,UNSAFE_allowJavascriptFileAttachments:this.UNSAFE_allowJavascriptFileAttachments})}notebook(e){return Ht({cells:e.nodes.map((({value:e})=>{const t=At(e);return t.input=e,t}))},{resolveImportPath:this.resolveImportPath,resolveFileAttachments:this.resolveFileAttachments,defineImportMarkdown:this.defineImportMarkdown,observeViewofValues:this.observeViewofValues,observeMutableValues:this.observeMutableValues})}}const Yt=Object.getPrototypeOf((async function(){})).constructor,Jt=Object.getPrototypeOf((function*(){})).constructor,Xt=Object.getPrototypeOf((async function*(){})).constructor;function Zt(e){const{cellName:t,references:i,bodyText:n,cellReferences:s}=Ot(e);let r,a;return r="BlockStatement"!==e.body.type?e.async?`return (async function(){ return (${n});})()`:`return (function(){ return (${n});})()`:n,a=e.generator&&e.async?new Xt(...i,r):e.async?new Yt(...i,r):e.generator?new Jt(...i,r):new Function(...i,r),{cellName:t,cellFunction:a,cellReferences:s}}function ei(e){const t=Vt(e);return import(`https://api.observablehq.com/${t}.js?v=3`).then((e=>e.default))}function ti(e){return e}class ii{constructor(e={}){const{module:t=null,observer:i=null,resolveImportPath:n=ei,resolveFileAttachments:s=ti,defineImportMarkdown:r=!0,observeViewofValues:a=!0,observeMutableValues:o=!0}=e;this.defaultModule=t,this.defaultObserver=i,this.resolveImportPath=n,this.resolveFileAttachments=s,this.defineImportMarkdown=r,this.observeViewofValues=a,this.observeMutableValues=o}async module(e,t,i){if(t=t||this.defaultModule,i=i||this.defaultObserver,!t)throw Error("No module provided.");if(!i)throw Error("No observer provided.");const n=kt(e),s=[];for(const r of n.cells)r.input=e,s.push(this.cell(r,t,i));return Promise.all(s)}async cell(e,t,i){if(t=t||this.defaultModule,i=i||this.defaultObserver,!t)throw Error("No module provided.");if(!i)throw Error("No observer provided.");let n;if("string"==typeof e?(n=At(e),n.input=e):n=e,"ImportDeclaration"===n.body.type){const e=n.body.source.value,s=n.body.specifiers.map((e=>`${e.view?"viewof ":e.mutable?"mutable ":""}${e.imported.name}`)),r=await this.resolveImportPath(e,s);let a,o;const{specifiers:u,hasInjections:c,injections:p,importString:l}=Rt(n),h=t._runtime.module(r);if(this.defineImportMarkdown&&(a=t.variable(i()).define(null,["md"],(e=>e`~~~javascript
  ${l}
  ~~~`))),c){const e=h.derive(p,t);o=u.map((({name:i,alias:n})=>t.import(i,n,e)))}else o=u.map((({name:e,alias:i})=>t.import(e,i,h)));return a?[a,...o]:o}{const{cellName:e,cellFunction:s,cellReferences:r}=Zt(n);if(n.id&&"ViewExpression"===n.id.type){const n=`viewof ${e}`;return[t.variable(i(n)).define(n,r,s.bind(this)),t.variable(this.observeViewofValues?i(e):null).define(e,["Generators",n],((e,t)=>e.input(t)))]}if(n.id&&"MutableExpression"===n.id.type){const n=`initial ${e}`,a=`mutable ${e}`;return[t.variable(null).define(n,r,s),t.variable(i(a)).define(a,["Mutable",n],((e,t)=>new e(t))),t.variable(this.observeMutableValues?i(e):null).define(e,[a],(e=>e.generator))]}return[t.variable(i(e)).define(e,r,s.bind(this))]}}}e.Compiler=Kt,e.Interpreter=ii,e.parser=It,e.treeShakeModule=$t,Object.defineProperty(e,"__esModule",{value:!0})}(Vs.exports);var Rs=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,357,0,62,13,1495,6,110,6,6,9,4759,9,787719,239],Os=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2637,96,16,1070,4050,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,46,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,482,44,11,6,17,0,322,29,19,43,1269,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4152,8,221,3,5761,15,7472,3104,541,1507,4938],Ms="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",js={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},qs="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",$s={5:qs,"5module":qs+" export import",6:qs+" const class extends export import super"},Us=/^in(stanceof)?$/,zs=new RegExp("["+Ms+"]"),Ws=new RegExp("["+Ms+"‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿]");function Hs(e,t){for(var i=65536,n=0;n<t.length;n+=2){if((i+=t[n])>e)return!1;if((i+=t[n+1])>=e)return!0}}function Gs(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&zs.test(String.fromCharCode(e)):!1!==t&&Hs(e,Os)))}function Qs(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&Ws.test(String.fromCharCode(e)):!1!==t&&(Hs(e,Os)||Hs(e,Rs)))))}var Ks=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function Ys(e,t){return new Ks(e,{beforeExpr:!0,binop:t})}var Js={beforeExpr:!0},Xs={startsExpr:!0},Zs={};function er(e,t){return void 0===t&&(t={}),t.keyword=e,Zs[e]=new Ks(e,t)}var tr={num:new Ks("num",Xs),regexp:new Ks("regexp",Xs),string:new Ks("string",Xs),name:new Ks("name",Xs),privateId:new Ks("privateId",Xs),eof:new Ks("eof"),bracketL:new Ks("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new Ks("]"),braceL:new Ks("{",{beforeExpr:!0,startsExpr:!0}),braceR:new Ks("}"),parenL:new Ks("(",{beforeExpr:!0,startsExpr:!0}),parenR:new Ks(")"),comma:new Ks(",",Js),semi:new Ks(";",Js),colon:new Ks(":",Js),dot:new Ks("."),question:new Ks("?",Js),questionDot:new Ks("?."),arrow:new Ks("=>",Js),template:new Ks("template"),invalidTemplate:new Ks("invalidTemplate"),ellipsis:new Ks("...",Js),backQuote:new Ks("`",Xs),dollarBraceL:new Ks("${",{beforeExpr:!0,startsExpr:!0}),eq:new Ks("=",{beforeExpr:!0,isAssign:!0}),assign:new Ks("_=",{beforeExpr:!0,isAssign:!0}),incDec:new Ks("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new Ks("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:Ys("||",1),logicalAND:Ys("&&",2),bitwiseOR:Ys("|",3),bitwiseXOR:Ys("^",4),bitwiseAND:Ys("&",5),equality:Ys("==/!=/===/!==",6),relational:Ys("</>/<=/>=",7),bitShift:Ys("<</>>/>>>",8),plusMin:new Ks("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:Ys("%",10),star:Ys("*",10),slash:Ys("/",10),starstar:new Ks("**",{beforeExpr:!0}),coalesce:Ys("??",1),_break:er("break"),_case:er("case",Js),_catch:er("catch"),_continue:er("continue"),_debugger:er("debugger"),_default:er("default",Js),_do:er("do",{isLoop:!0,beforeExpr:!0}),_else:er("else",Js),_finally:er("finally"),_for:er("for",{isLoop:!0}),_function:er("function",Xs),_if:er("if"),_return:er("return",Js),_switch:er("switch"),_throw:er("throw",Js),_try:er("try"),_var:er("var"),_const:er("const"),_while:er("while",{isLoop:!0}),_with:er("with"),_new:er("new",{beforeExpr:!0,startsExpr:!0}),_this:er("this",Xs),_super:er("super",Xs),_class:er("class",Xs),_extends:er("extends",Js),_export:er("export"),_import:er("import",Xs),_null:er("null",Xs),_true:er("true",Xs),_false:er("false",Xs),_in:er("in",{beforeExpr:!0,binop:7}),_instanceof:er("instanceof",{beforeExpr:!0,binop:7}),_typeof:er("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:er("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:er("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},ir=/\r\n?|\n|\u2028|\u2029/,nr=new RegExp(ir.source,"g");function sr(e){return 10===e||13===e||8232===e||8233===e}function rr(e,t,i){void 0===i&&(i=e.length);for(var n=t;n<i;n++){var s=e.charCodeAt(n);if(sr(s))return n<i-1&&13===s&&10===e.charCodeAt(n+1)?n+2:n+1}return-1}var ar=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,or=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,ur=Object.prototype,cr=ur.hasOwnProperty,pr=ur.toString,lr=Object.hasOwn||function(e,t){return cr.call(e,t)},hr=Array.isArray||function(e){return"[object Array]"===pr.call(e)};function dr(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function fr(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}var mr=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,gr=function(e,t){this.line=e,this.column=t};gr.prototype.offset=function(e){return new gr(this.line,this.column+e)};var vr=function(e,t,i){this.start=t,this.end=i,null!==e.sourceFile&&(this.source=e.sourceFile)};function xr(e,t){for(var i=1,n=0;;){var s=rr(e,n,t);if(s<0)return new gr(i,t-n);++i,n=s}}var yr={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},br=!1;function Er(e){var t={};for(var i in yr)t[i]=e&&lr(e,i)?e[i]:yr[i];if("latest"===t.ecmaVersion?t.ecmaVersion=1e8:null==t.ecmaVersion?(!br&&"object"==typeof console&&console.warn&&(br=!0,console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),null==e.allowHashBang&&(t.allowHashBang=t.ecmaVersion>=14),hr(t.onToken)){var n=t.onToken;t.onToken=function(e){return n.push(e)}}return hr(t.onComment)&&(t.onComment=function(e,t){return function(i,n,s,r,a,o){var u={type:i?"Block":"Line",value:n,start:s,end:r};e.locations&&(u.loc=new vr(this,a,o)),e.ranges&&(u.range=[s,r]),t.push(u)}}(t,t.onComment)),t}var Cr=256;function Dr(e,t){return 2|(e?4:0)|(t?8:0)}var Ar=function(e,t,i){this.options=e=Er(e),this.sourceFile=e.sourceFile,this.keywords=dr($s[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var n="";!0!==e.allowReserved&&(n=js[e.ecmaVersion>=6?6:5===e.ecmaVersion?5:3],"module"===e.sourceType&&(n+=" await")),this.reservedWords=dr(n);var s=(n?n+" ":"")+js.strict;this.reservedWordsStrict=dr(s),this.reservedWordsStrictBind=dr(s+" "+js.strictBind),this.input=String(t),this.containsEsc=!1,i?(this.pos=i,this.lineStart=this.input.lastIndexOf("\n",i-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(ir).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=tr.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null,this.privateNameStack=[]},wr={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};Ar.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},wr.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},wr.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},wr.inAsync.get=function(){return(4&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},wr.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&Cr)return!1;if(2&t.flags)return(4&t.flags)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},wr.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(64&t)>0||i||this.options.allowSuperOutsideMethod},wr.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},wr.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},wr.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(258&t)>0||i},wr.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&Cr)>0},Ar.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var i=this,n=0;n<e.length;n++)i=e[n](i);return i},Ar.parse=function(e,t){return new this(t,e).parse()},Ar.parseExpressionAt=function(e,t,i){var n=new this(i,e,t);return n.nextToken(),n.parseExpression()},Ar.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(Ar.prototype,wr);var Fr=Ar.prototype,kr=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;Fr.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){or.lastIndex=e,e+=or.exec(this.input)[0].length;var t=kr.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){or.lastIndex=e+t[0].length;var i=or.exec(this.input),n=i.index+i[0].length,s=this.input.charAt(n);return";"===s||"}"===s||ir.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(s)||"!"===s&&"="===this.input.charAt(n+1))}e+=t[0].length,or.lastIndex=e,e+=or.exec(this.input)[0].length,";"===this.input[e]&&e++}},Fr.eat=function(e){return this.type===e&&(this.next(),!0)},Fr.isContextual=function(e){return this.type===tr.name&&this.value===e&&!this.containsEsc},Fr.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},Fr.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},Fr.canInsertSemicolon=function(){return this.type===tr.eof||this.type===tr.braceR||ir.test(this.input.slice(this.lastTokEnd,this.start))},Fr.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},Fr.semicolon=function(){this.eat(tr.semi)||this.insertSemicolon()||this.unexpected()},Fr.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},Fr.expect=function(e){this.eat(e)||this.unexpected()},Fr.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var Sr=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};Fr.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var i=t?e.parenthesizedAssign:e.parenthesizedBind;i>-1&&this.raiseRecoverable(i,t?"Assigning to rvalue":"Parenthesized pattern")}},Fr.checkExpressionErrors=function(e,t){if(!e)return!1;var i=e.shorthandAssign,n=e.doubleProto;if(!t)return i>=0||n>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),n>=0&&this.raiseRecoverable(n,"Redefinition of __proto__ property")},Fr.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},Fr.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var _r=Ar.prototype;_r.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==tr.eof;){var i=this.parseStatement(null,!0,t);e.body.push(i)}if(this.inModule)for(var n=0,s=Object.keys(this.undefinedExports);n<s.length;n+=1){var r=s[n];this.raiseRecoverable(this.undefinedExports[r].start,"Export '"+r+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var Br={kind:"loop"},Ir={kind:"switch"};_r.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;or.lastIndex=this.pos;var t=or.exec(this.input),i=this.pos+t[0].length,n=this.input.charCodeAt(i);if(91===n||92===n||n>55295&&n<56320)return!0;if(e)return!1;if(123===n)return!0;if(Gs(n,!0)){for(var s=i+1;Qs(n=this.input.charCodeAt(s),!0);)++s;if(92===n||n>55295&&n<56320)return!0;var r=this.input.slice(i,s);if(!Us.test(r))return!0}return!1},_r.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;or.lastIndex=this.pos;var e,t=or.exec(this.input),i=this.pos+t[0].length;return!(ir.test(this.input.slice(this.pos,i))||"function"!==this.input.slice(i,i+8)||i+8!==this.input.length&&(Qs(e=this.input.charCodeAt(i+8))||e>55295&&e<56320))},_r.parseStatement=function(e,t,i){var n,s=this.type,r=this.startNode();switch(this.isLet(e)&&(s=tr._var,n="let"),s){case tr._break:case tr._continue:return this.parseBreakContinueStatement(r,s.keyword);case tr._debugger:return this.parseDebuggerStatement(r);case tr._do:return this.parseDoStatement(r);case tr._for:return this.parseForStatement(r);case tr._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(r,!1,!e);case tr._class:return e&&this.unexpected(),this.parseClass(r,!0);case tr._if:return this.parseIfStatement(r);case tr._return:return this.parseReturnStatement(r);case tr._switch:return this.parseSwitchStatement(r);case tr._throw:return this.parseThrowStatement(r);case tr._try:return this.parseTryStatement(r);case tr._const:case tr._var:return n=n||this.value,e&&"var"!==n&&this.unexpected(),this.parseVarStatement(r,n);case tr._while:return this.parseWhileStatement(r);case tr._with:return this.parseWithStatement(r);case tr.braceL:return this.parseBlock(!0,r);case tr.semi:return this.parseEmptyStatement(r);case tr._export:case tr._import:if(this.options.ecmaVersion>10&&s===tr._import){or.lastIndex=this.pos;var a=or.exec(this.input),o=this.pos+a[0].length,u=this.input.charCodeAt(o);if(40===u||46===u)return this.parseExpressionStatement(r,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===tr._import?this.parseImport(r):this.parseExport(r,i);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(r,!0,!e);var c=this.value,p=this.parseExpression();return s===tr.name&&"Identifier"===p.type&&this.eat(tr.colon)?this.parseLabeledStatement(r,c,p,e):this.parseExpressionStatement(r,p)}},_r.parseBreakContinueStatement=function(e,t){var i="break"===t;this.next(),this.eat(tr.semi)||this.insertSemicolon()?e.label=null:this.type!==tr.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var n=0;n<this.labels.length;++n){var s=this.labels[n];if(null==e.label||s.name===e.label.name){if(null!=s.kind&&(i||"loop"===s.kind))break;if(e.label&&i)break}}return n===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,i?"BreakStatement":"ContinueStatement")},_r.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},_r.parseDoStatement=function(e){return this.next(),this.labels.push(Br),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(tr._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(tr.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},_r.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(Br),this.enterScope(0),this.expect(tr.parenL),this.type===tr.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var i=this.isLet();if(this.type===tr._var||this.type===tr._const||i){var n=this.startNode(),s=i?"let":this.value;return this.next(),this.parseVar(n,!0,s),this.finishNode(n,"VariableDeclaration"),(this.type===tr._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===n.declarations.length?(this.options.ecmaVersion>=9&&(this.type===tr._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,n)):(t>-1&&this.unexpected(t),this.parseFor(e,n))}var r=this.isContextual("let"),a=!1,o=new Sr,u=this.parseExpression(!(t>-1)||"await",o);return this.type===tr._in||(a=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===tr._in?t>-1&&this.unexpected(t):e.await=t>-1),r&&a&&this.raise(u.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(u,!1,o),this.checkLValPattern(u),this.parseForIn(e,u)):(this.checkExpressionErrors(o,!0),t>-1&&this.unexpected(t),this.parseFor(e,u))},_r.parseFunctionStatement=function(e,t,i){return this.next(),this.parseFunction(e,Nr|(i?0:Tr),!1,t)},_r.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(tr._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},_r.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(tr.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},_r.parseSwitchStatement=function(e){var t;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(tr.braceL),this.labels.push(Ir),this.enterScope(0);for(var i=!1;this.type!==tr.braceR;)if(this.type===tr._case||this.type===tr._default){var n=this.type===tr._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),n?t.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,t.test=null),this.expect(tr.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},_r.parseThrowStatement=function(e){return this.next(),ir.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var Pr=[];_r.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===tr._catch){var t=this.startNode();if(this.next(),this.eat(tr.parenL)){t.param=this.parseBindingAtom();var i="Identifier"===t.param.type;this.enterScope(i?32:0),this.checkLValPattern(t.param,i?4:2),this.expect(tr.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(tr._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},_r.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},_r.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(Br),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},_r.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},_r.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},_r.parseLabeledStatement=function(e,t,i,n){for(var s=0,r=this.labels;s<r.length;s+=1){r[s].name===t&&this.raise(i.start,"Label '"+t+"' is already declared")}for(var a=this.type.isLoop?"loop":this.type===tr._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var u=this.labels[o];if(u.statementStart!==e.start)break;u.statementStart=this.start,u.kind=a}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(n?-1===n.indexOf("label")?n+"label":n:"label"),this.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")},_r.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},_r.parseBlock=function(e,t,i){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(tr.braceL),e&&this.enterScope(0);this.type!==tr.braceR;){var n=this.parseStatement(null);t.body.push(n)}return i&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},_r.parseFor=function(e,t){return e.init=t,this.expect(tr.semi),e.test=this.type===tr.semi?null:this.parseExpression(),this.expect(tr.semi),e.update=this.type===tr.parenR?null:this.parseExpression(),this.expect(tr.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},_r.parseForIn=function(e,t){var i=this.type===tr._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!i||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)&&this.raise(t.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(tr.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")},_r.parseVar=function(e,t,i){for(e.declarations=[],e.kind=i;;){var n=this.startNode();if(this.parseVarId(n,i),this.eat(tr.eq)?n.init=this.parseMaybeAssign(t):"const"!==i||this.type===tr._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===n.id.type||t&&(this.type===tr._in||this.isContextual("of"))?n.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(n,"VariableDeclarator")),!this.eat(tr.comma))break}return e},_r.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,"var"===t?1:2,!1)};var Nr=1,Tr=2;function Lr(e,t){var i=t.key.name,n=e[i],s="true";return"MethodDefinition"!==t.type||"get"!==t.kind&&"set"!==t.kind||(s=(t.static?"s":"i")+t.kind),"iget"===n&&"iset"===s||"iset"===n&&"iget"===s||"sget"===n&&"sset"===s||"sset"===n&&"sget"===s?(e[i]="true",!1):!!n||(e[i]=s,!1)}function Vr(e,t){var i=e.computed,n=e.key;return!i&&("Identifier"===n.type&&n.name===t||"Literal"===n.type&&n.value===t)}_r.parseFunction=function(e,t,i,n,s){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!n)&&(this.type===tr.star&&t&Tr&&this.unexpected(),e.generator=this.eat(tr.star)),this.options.ecmaVersion>=8&&(e.async=!!n),t&Nr&&(e.id=4&t&&this.type!==tr.name?null:this.parseIdent(),!e.id||t&Tr||this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var r=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(Dr(e.async,e.generator)),t&Nr||(e.id=this.type===tr.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,i,!1,s),this.yieldPos=r,this.awaitPos=a,this.awaitIdentPos=o,this.finishNode(e,t&Nr?"FunctionDeclaration":"FunctionExpression")},_r.parseFunctionParams=function(e){this.expect(tr.parenL),e.params=this.parseBindingList(tr.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},_r.parseClass=function(e,t){this.next();var i=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var n=this.enterClassBody(),s=this.startNode(),r=!1;for(s.body=[],this.expect(tr.braceL);this.type!==tr.braceR;){var a=this.parseClassElement(null!==e.superClass);a&&(s.body.push(a),"MethodDefinition"===a.type&&"constructor"===a.kind?(r&&this.raise(a.start,"Duplicate constructor in the same class"),r=!0):a.key&&"PrivateIdentifier"===a.key.type&&Lr(n,a)&&this.raiseRecoverable(a.key.start,"Identifier '#"+a.key.name+"' has already been declared"))}return this.strict=i,this.next(),e.body=this.finishNode(s,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},_r.parseClassElement=function(e){if(this.eat(tr.semi))return null;var t=this.options.ecmaVersion,i=this.startNode(),n="",s=!1,r=!1,a="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(tr.braceL))return this.parseClassStaticBlock(i),i;this.isClassElementNameStart()||this.type===tr.star?o=!0:n="static"}if(i.static=o,!n&&t>=8&&this.eatContextual("async")&&(!this.isClassElementNameStart()&&this.type!==tr.star||this.canInsertSemicolon()?n="async":r=!0),!n&&(t>=9||!r)&&this.eat(tr.star)&&(s=!0),!n&&!r&&!s){var u=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?a=u:n=u)}if(n?(i.computed=!1,i.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),i.key.name=n,this.finishNode(i.key,"Identifier")):this.parseClassElementName(i),t<13||this.type===tr.parenL||"method"!==a||s||r){var c=!i.static&&Vr(i,"constructor"),p=c&&e;c&&"method"!==a&&this.raise(i.key.start,"Constructor can't have get/set modifier"),i.kind=c?"constructor":a,this.parseClassMethod(i,s,r,p)}else this.parseClassField(i);return i},_r.isClassElementNameStart=function(){return this.type===tr.name||this.type===tr.privateId||this.type===tr.num||this.type===tr.string||this.type===tr.bracketL||this.type.keyword},_r.parseClassElementName=function(e){this.type===tr.privateId?("constructor"===this.value&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},_r.parseClassMethod=function(e,t,i,n){var s=e.key;"constructor"===e.kind?(t&&this.raise(s.start,"Constructor can't be a generator"),i&&this.raise(s.start,"Constructor can't be an async method")):e.static&&Vr(e,"prototype")&&this.raise(s.start,"Classes may not have a static property named prototype");var r=e.value=this.parseMethod(t,i,n);return"get"===e.kind&&0!==r.params.length&&this.raiseRecoverable(r.start,"getter should have no params"),"set"===e.kind&&1!==r.params.length&&this.raiseRecoverable(r.start,"setter should have exactly one param"),"set"===e.kind&&"RestElement"===r.params[0].type&&this.raiseRecoverable(r.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},_r.parseClassField=function(e){if(Vr(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&Vr(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(tr.eq)){var t=this.currentThisScope(),i=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=i}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},_r.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(320);this.type!==tr.braceR;){var i=this.parseStatement(null);e.body.push(i)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},_r.parseClassId=function(e,t){this.type===tr.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},_r.parseClassSuper=function(e){e.superClass=this.eat(tr._extends)?this.parseExprSubscripts(!1):null},_r.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},_r.exitClassBody=function(){for(var e=this.privateNameStack.pop(),t=e.declared,i=e.used,n=this.privateNameStack.length,s=0===n?null:this.privateNameStack[n-1],r=0;r<i.length;++r){var a=i[r];lr(t,a.name)||(s?s.used.push(a):this.raiseRecoverable(a.start,"Private field '#"+a.name+"' must be declared in an enclosing class"))}},_r.parseExport=function(e,t){if(this.next(),this.eat(tr.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==tr.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(tr._default)){var i;if(this.checkExport(t,"default",this.lastTokStart),this.type===tr._function||(i=this.isAsyncFunction())){var n=this.startNode();this.next(),i&&this.next(),e.declaration=this.parseFunction(n,4|Nr,!1,i)}else if(this.type===tr._class){var s=this.startNode();e.declaration=this.parseClass(s,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==tr.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var r=0,a=e.specifiers;r<a.length;r+=1){var o=a[r];this.checkUnreserved(o.local),this.checkLocalExport(o.local),"Literal"===o.local.type&&this.raise(o.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},_r.checkExport=function(e,t,i){e&&("string"!=typeof t&&(t="Identifier"===t.type?t.name:t.value),lr(e,t)&&this.raiseRecoverable(i,"Duplicate export '"+t+"'"),e[t]=!0)},_r.checkPatternExport=function(e,t){var i=t.type;if("Identifier"===i)this.checkExport(e,t,t.start);else if("ObjectPattern"===i)for(var n=0,s=t.properties;n<s.length;n+=1){var r=s[n];this.checkPatternExport(e,r)}else if("ArrayPattern"===i)for(var a=0,o=t.elements;a<o.length;a+=1){var u=o[a];u&&this.checkPatternExport(e,u)}else"Property"===i?this.checkPatternExport(e,t.value):"AssignmentPattern"===i?this.checkPatternExport(e,t.left):"RestElement"===i?this.checkPatternExport(e,t.argument):"ParenthesizedExpression"===i&&this.checkPatternExport(e,t.expression)},_r.checkVariableExport=function(e,t){if(e)for(var i=0,n=t;i<n.length;i+=1){var s=n[i];this.checkPatternExport(e,s.id)}},_r.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},_r.parseExportSpecifiers=function(e){var t=[],i=!0;for(this.expect(tr.braceL);!this.eat(tr.braceR);){if(i)i=!1;else if(this.expect(tr.comma),this.afterTrailingComma(tr.braceR))break;var n=this.startNode();n.local=this.parseModuleExportName(),n.exported=this.eatContextual("as")?this.parseModuleExportName():n.local,this.checkExport(e,n.exported,n.exported.start),t.push(this.finishNode(n,"ExportSpecifier"))}return t},_r.parseImport=function(e){return this.next(),this.type===tr.string?(e.specifiers=Pr,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===tr.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},_r.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===tr.name){var i=this.startNode();if(i.local=this.parseIdent(),this.checkLValSimple(i.local,2),e.push(this.finishNode(i,"ImportDefaultSpecifier")),!this.eat(tr.comma))return e}if(this.type===tr.star){var n=this.startNode();return this.next(),this.expectContextual("as"),n.local=this.parseIdent(),this.checkLValSimple(n.local,2),e.push(this.finishNode(n,"ImportNamespaceSpecifier")),e}for(this.expect(tr.braceL);!this.eat(tr.braceR);){if(t)t=!1;else if(this.expect(tr.comma),this.afterTrailingComma(tr.braceR))break;var s=this.startNode();s.imported=this.parseModuleExportName(),this.eatContextual("as")?s.local=this.parseIdent():(this.checkUnreserved(s.imported),s.local=s.imported),this.checkLValSimple(s.local,2),e.push(this.finishNode(s,"ImportSpecifier"))}return e},_r.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===tr.string){var e=this.parseLiteral(this.value);return mr.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},_r.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},_r.isDirectiveCandidate=function(e){return this.options.ecmaVersion>=5&&"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var Rr=Ar.prototype;Rr.toAssignable=function(e,t,i){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];this.toAssignable(r,t),"RestElement"!==r.type||"ArrayPattern"!==r.argument.type&&"ObjectPattern"!==r.argument.type||this.raise(r.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,i);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return e},Rr.toAssignableList=function(e,t){for(var i=e.length,n=0;n<i;n++){var s=e[n];s&&this.toAssignable(s,t)}if(i){var r=e[i-1];6===this.options.ecmaVersion&&t&&r&&"RestElement"===r.type&&"Identifier"!==r.argument.type&&this.unexpected(r.argument.start)}return e},Rr.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},Rr.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==tr.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},Rr.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case tr.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(tr.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case tr.braceL:return this.parseObj(!0)}return this.parseIdent()},Rr.parseBindingList=function(e,t,i){for(var n=[],s=!0;!this.eat(e);)if(s?s=!1:this.expect(tr.comma),t&&this.type===tr.comma)n.push(null);else{if(i&&this.afterTrailingComma(e))break;if(this.type===tr.ellipsis){var r=this.parseRestBinding();this.parseBindingListItem(r),n.push(r),this.type===tr.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),n.push(a)}return n},Rr.parseBindingListItem=function(e){return e},Rr.parseMaybeDefault=function(e,t,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(tr.eq))return i;var n=this.startNodeAt(e,t);return n.left=i,n.right=this.parseMaybeAssign(),this.finishNode(n,"AssignmentPattern")},Rr.checkLValSimple=function(e,t,i){void 0===t&&(t=0);var n=0!==t;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(n?"Binding ":"Assigning to ")+e.name+" in strict mode"),n&&(2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),i&&(lr(i,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),i[e.name]=!0),5!==t&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":n&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return n&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,i);default:this.raise(e.start,(n?"Binding":"Assigning to")+" rvalue")}},Rr.checkLValPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"ObjectPattern":for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];this.checkLValInnerPattern(r,t,i)}break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var u=o[a];u&&this.checkLValInnerPattern(u,t,i)}break;default:this.checkLValSimple(e,t,i)}},Rr.checkLValInnerPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"Property":this.checkLValInnerPattern(e.value,t,i);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,i);break;case"RestElement":this.checkLValPattern(e.argument,t,i);break;default:this.checkLValPattern(e,t,i)}};var Or=function(e,t,i,n,s){this.token=e,this.isExpr=!!t,this.preserveSpace=!!i,this.override=n,this.generator=!!s},Mr={b_stat:new Or("{",!1),b_expr:new Or("{",!0),b_tmpl:new Or("${",!1),p_stat:new Or("(",!1),p_expr:new Or("(",!0),q_tmpl:new Or("`",!0,!0,(function(e){return e.tryReadTemplateToken()})),f_stat:new Or("function",!1),f_expr:new Or("function",!0),f_expr_gen:new Or("function",!0,!1,null,!0),f_gen:new Or("function",!1,!1,null,!0)},jr=Ar.prototype;jr.initialContext=function(){return[Mr.b_stat]},jr.curContext=function(){return this.context[this.context.length-1]},jr.braceIsBlock=function(e){var t=this.curContext();return t===Mr.f_expr||t===Mr.f_stat||(e!==tr.colon||t!==Mr.b_stat&&t!==Mr.b_expr?e===tr._return||e===tr.name&&this.exprAllowed?ir.test(this.input.slice(this.lastTokEnd,this.start)):e===tr._else||e===tr.semi||e===tr.eof||e===tr.parenR||e===tr.arrow||(e===tr.braceL?t===Mr.b_stat:e!==tr._var&&e!==tr._const&&e!==tr.name&&!this.exprAllowed):!t.isExpr)},jr.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},jr.updateContext=function(e){var t,i=this.type;i.keyword&&e===tr.dot?this.exprAllowed=!1:(t=i.updateContext)?t.call(this,e):this.exprAllowed=i.beforeExpr},jr.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},tr.parenR.updateContext=tr.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===Mr.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},tr.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?Mr.b_stat:Mr.b_expr),this.exprAllowed=!0},tr.dollarBraceL.updateContext=function(){this.context.push(Mr.b_tmpl),this.exprAllowed=!0},tr.parenL.updateContext=function(e){var t=e===tr._if||e===tr._for||e===tr._with||e===tr._while;this.context.push(t?Mr.p_stat:Mr.p_expr),this.exprAllowed=!0},tr.incDec.updateContext=function(){},tr._function.updateContext=tr._class.updateContext=function(e){!e.beforeExpr||e===tr._else||e===tr.semi&&this.curContext()!==Mr.p_stat||e===tr._return&&ir.test(this.input.slice(this.lastTokEnd,this.start))||(e===tr.colon||e===tr.braceL)&&this.curContext()===Mr.b_stat?this.context.push(Mr.f_stat):this.context.push(Mr.f_expr),this.exprAllowed=!1},tr.backQuote.updateContext=function(){this.curContext()===Mr.q_tmpl?this.context.pop():this.context.push(Mr.q_tmpl),this.exprAllowed=!1},tr.star.updateContext=function(e){if(e===tr._function){var t=this.context.length-1;this.context[t]===Mr.f_expr?this.context[t]=Mr.f_expr_gen:this.context[t]=Mr.f_gen}this.exprAllowed=!0},tr.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==tr.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var qr=Ar.prototype;function $r(e){return"MemberExpression"===e.type&&"PrivateIdentifier"===e.property.type||"ChainExpression"===e.type&&$r(e.expression)}qr.checkPropClash=function(e,t,i){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var n,s=e.key;switch(s.type){case"Identifier":n=s.name;break;case"Literal":n=String(s.value);break;default:return}var r=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===n&&"init"===r&&(t.proto&&(i?i.doubleProto<0&&(i.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);else{var a=t[n="$"+n];if(a)("init"===r?this.strict&&a.init||a.get||a.set:a.init||a[r])&&this.raiseRecoverable(s.start,"Redefinition of property");else a=t[n]={init:!1,get:!1,set:!1};a[r]=!0}}},qr.parseExpression=function(e,t){var i=this.start,n=this.startLoc,s=this.parseMaybeAssign(e,t);if(this.type===tr.comma){var r=this.startNodeAt(i,n);for(r.expressions=[s];this.eat(tr.comma);)r.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(r,"SequenceExpression")}return s},qr.parseMaybeAssign=function(e,t,i){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var n=!1,s=-1,r=-1,a=-1;t?(s=t.parenthesizedAssign,r=t.trailingComma,a=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new Sr,n=!0);var o=this.start,u=this.startLoc;this.type!==tr.parenL&&this.type!==tr.name||(this.potentialArrowAt=this.start,this.potentialArrowInForAwait="await"===e);var c=this.parseMaybeConditional(e,t);if(i&&(c=i.call(this,c,o,u)),this.type.isAssign){var p=this.startNodeAt(o,u);return p.operator=this.value,this.type===tr.eq&&(c=this.toAssignable(c,!1,t)),n||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=c.start&&(t.shorthandAssign=-1),this.type===tr.eq?this.checkLValPattern(c):this.checkLValSimple(c),p.left=c,this.next(),p.right=this.parseMaybeAssign(e),a>-1&&(t.doubleProto=a),this.finishNode(p,"AssignmentExpression")}return n&&this.checkExpressionErrors(t,!0),s>-1&&(t.parenthesizedAssign=s),r>-1&&(t.trailingComma=r),c},qr.parseMaybeConditional=function(e,t){var i=this.start,n=this.startLoc,s=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return s;if(this.eat(tr.question)){var r=this.startNodeAt(i,n);return r.test=s,r.consequent=this.parseMaybeAssign(),this.expect(tr.colon),r.alternate=this.parseMaybeAssign(e),this.finishNode(r,"ConditionalExpression")}return s},qr.parseExprOps=function(e,t){var i=this.start,n=this.startLoc,s=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||s.start===i&&"ArrowFunctionExpression"===s.type?s:this.parseExprOp(s,i,n,-1,e)},qr.parseExprOp=function(e,t,i,n,s){var r=this.type.binop;if(null!=r&&(!s||this.type!==tr._in)&&r>n){var a=this.type===tr.logicalOR||this.type===tr.logicalAND,o=this.type===tr.coalesce;o&&(r=tr.logicalAND.binop);var u=this.value;this.next();var c=this.start,p=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,s),c,p,r,s),h=this.buildBinary(t,i,e,l,u,a||o);return(a&&this.type===tr.coalesce||o&&(this.type===tr.logicalOR||this.type===tr.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(h,t,i,n,s)}return e},qr.buildBinary=function(e,t,i,n,s,r){"PrivateIdentifier"===n.type&&this.raise(n.start,"Private identifier can only be left side of binary expression");var a=this.startNodeAt(e,t);return a.left=i,a.operator=s,a.right=n,this.finishNode(a,r?"LogicalExpression":"BinaryExpression")},qr.parseMaybeUnary=function(e,t,i,n){var s,r=this.start,a=this.startLoc;if(this.isContextual("await")&&this.canAwait)s=this.parseAwait(n),t=!0;else if(this.type.prefix){var o=this.startNode(),u=this.type===tr.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,u,n),this.checkExpressionErrors(e,!0),u?this.checkLValSimple(o.argument):this.strict&&"delete"===o.operator&&"Identifier"===o.argument.type?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):"delete"===o.operator&&$r(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,s=this.finishNode(o,u?"UpdateExpression":"UnaryExpression")}else if(t||this.type!==tr.privateId){if(s=this.parseExprSubscripts(e,n),this.checkExpressionErrors(e))return s;for(;this.type.postfix&&!this.canInsertSemicolon();){var c=this.startNodeAt(r,a);c.operator=this.value,c.prefix=!1,c.argument=s,this.checkLValSimple(s),this.next(),s=this.finishNode(c,"UpdateExpression")}}else(n||0===this.privateNameStack.length)&&this.unexpected(),s=this.parsePrivateIdent(),this.type!==tr._in&&this.unexpected();return i||!this.eat(tr.starstar)?s:t?void this.unexpected(this.lastTokStart):this.buildBinary(r,a,s,this.parseMaybeUnary(null,!1,!1,n),"**",!1)},qr.parseExprSubscripts=function(e,t){var i=this.start,n=this.startLoc,s=this.parseExprAtom(e,t);if("ArrowFunctionExpression"===s.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return s;var r=this.parseSubscripts(s,i,n,!1,t);return e&&"MemberExpression"===r.type&&(e.parenthesizedAssign>=r.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=r.start&&(e.parenthesizedBind=-1),e.trailingComma>=r.start&&(e.trailingComma=-1)),r},qr.parseSubscripts=function(e,t,i,n,s){for(var r=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.potentialArrowAt===e.start,a=!1;;){var o=this.parseSubscript(e,t,i,n,r,a,s);if(o.optional&&(a=!0),o===e||"ArrowFunctionExpression"===o.type){if(a){var u=this.startNodeAt(t,i);u.expression=o,o=this.finishNode(u,"ChainExpression")}return o}e=o}},qr.parseSubscript=function(e,t,i,n,s,r,a){var o=this.options.ecmaVersion>=11,u=o&&this.eat(tr.questionDot);n&&u&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var c=this.eat(tr.bracketL);if(c||u&&this.type!==tr.parenL&&this.type!==tr.backQuote||this.eat(tr.dot)){var p=this.startNodeAt(t,i);p.object=e,c?(p.property=this.parseExpression(),this.expect(tr.bracketR)):this.type===tr.privateId&&"Super"!==e.type?p.property=this.parsePrivateIdent():p.property=this.parseIdent("never"!==this.options.allowReserved),p.computed=!!c,o&&(p.optional=u),e=this.finishNode(p,"MemberExpression")}else if(!n&&this.eat(tr.parenL)){var l=new Sr,h=this.yieldPos,d=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var m=this.parseExprList(tr.parenR,this.options.ecmaVersion>=8,!1,l);if(s&&!u&&!this.canInsertSemicolon()&&this.eat(tr.arrow))return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=h,this.awaitPos=d,this.awaitIdentPos=f,this.parseArrowExpression(this.startNodeAt(t,i),m,!0,a);this.checkExpressionErrors(l,!0),this.yieldPos=h||this.yieldPos,this.awaitPos=d||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var g=this.startNodeAt(t,i);g.callee=e,g.arguments=m,o&&(g.optional=u),e=this.finishNode(g,"CallExpression")}else if(this.type===tr.backQuote){(u||r)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var v=this.startNodeAt(t,i);v.tag=e,v.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(v,"TaggedTemplateExpression")}return e},qr.parseExprAtom=function(e,t){this.type===tr.slash&&this.readRegexp();var i,n=this.potentialArrowAt===this.start;switch(this.type){case tr._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),i=this.startNode(),this.next(),this.type!==tr.parenL||this.allowDirectSuper||this.raise(i.start,"super() call outside constructor of a subclass"),this.type!==tr.dot&&this.type!==tr.bracketL&&this.type!==tr.parenL&&this.unexpected(),this.finishNode(i,"Super");case tr._this:return i=this.startNode(),this.next(),this.finishNode(i,"ThisExpression");case tr.name:var s=this.start,r=this.startLoc,a=this.containsEsc,o=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!a&&"async"===o.name&&!this.canInsertSemicolon()&&this.eat(tr._function))return this.overrideContext(Mr.f_expr),this.parseFunction(this.startNodeAt(s,r),0,!1,!0,t);if(n&&!this.canInsertSemicolon()){if(this.eat(tr.arrow))return this.parseArrowExpression(this.startNodeAt(s,r),[o],!1,t);if(this.options.ecmaVersion>=8&&"async"===o.name&&this.type===tr.name&&!a&&(!this.potentialArrowInForAwait||"of"!==this.value||this.containsEsc))return o=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(tr.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(s,r),[o],!0,t)}return o;case tr.regexp:var u=this.value;return(i=this.parseLiteral(u.value)).regex={pattern:u.pattern,flags:u.flags},i;case tr.num:case tr.string:return this.parseLiteral(this.value);case tr._null:case tr._true:case tr._false:return(i=this.startNode()).value=this.type===tr._null?null:this.type===tr._true,i.raw=this.type.keyword,this.next(),this.finishNode(i,"Literal");case tr.parenL:var c=this.start,p=this.parseParenAndDistinguishExpression(n,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(p)&&(e.parenthesizedAssign=c),e.parenthesizedBind<0&&(e.parenthesizedBind=c)),p;case tr.bracketL:return i=this.startNode(),this.next(),i.elements=this.parseExprList(tr.bracketR,!0,!0,e),this.finishNode(i,"ArrayExpression");case tr.braceL:return this.overrideContext(Mr.b_expr),this.parseObj(!1,e);case tr._function:return i=this.startNode(),this.next(),this.parseFunction(i,0);case tr._class:return this.parseClass(this.startNode(),!1);case tr._new:return this.parseNew();case tr.backQuote:return this.parseTemplate();case tr._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},qr.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case tr.parenL:return this.parseDynamicImport(e);case tr.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},qr.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(tr.parenR)){var t=this.start;this.eat(tr.comma)&&this.eat(tr.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},qr.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"===this.options.sourceType||this.options.allowImportExportEverywhere||this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},qr.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},qr.parseParenExpression=function(){this.expect(tr.parenL);var e=this.parseExpression();return this.expect(tr.parenR),e},qr.parseParenAndDistinguishExpression=function(e,t){var i,n=this.start,s=this.startLoc,r=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var a,o=this.start,u=this.startLoc,c=[],p=!0,l=!1,h=new Sr,d=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==tr.parenR;){if(p?p=!1:this.expect(tr.comma),r&&this.afterTrailingComma(tr.parenR,!0)){l=!0;break}if(this.type===tr.ellipsis){a=this.start,c.push(this.parseParenItem(this.parseRestBinding())),this.type===tr.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}c.push(this.parseMaybeAssign(!1,h,this.parseParenItem))}var m=this.lastTokEnd,g=this.lastTokEndLoc;if(this.expect(tr.parenR),e&&!this.canInsertSemicolon()&&this.eat(tr.arrow))return this.checkPatternErrors(h,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=d,this.awaitPos=f,this.parseParenArrowList(n,s,c,t);c.length&&!l||this.unexpected(this.lastTokStart),a&&this.unexpected(a),this.checkExpressionErrors(h,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=f||this.awaitPos,c.length>1?((i=this.startNodeAt(o,u)).expressions=c,this.finishNodeAt(i,"SequenceExpression",m,g)):i=c[0]}else i=this.parseParenExpression();if(this.options.preserveParens){var v=this.startNodeAt(n,s);return v.expression=i,this.finishNode(v,"ParenthesizedExpression")}return i},qr.parseParenItem=function(e){return e},qr.parseParenArrowList=function(e,t,i,n){return this.parseArrowExpression(this.startNodeAt(e,t),i,!1,n)};var Ur=[];qr.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(tr.dot)){e.meta=t;var i=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var n=this.start,s=this.startLoc,r=this.type===tr._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),n,s,!0,!1),r&&"ImportExpression"===e.callee.type&&this.raise(n,"Cannot use new with import()"),this.eat(tr.parenL)?e.arguments=this.parseExprList(tr.parenR,this.options.ecmaVersion>=8,!1):e.arguments=Ur,this.finishNode(e,"NewExpression")},qr.parseTemplateElement=function(e){var t=e.isTagged,i=this.startNode();return this.type===tr.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value,cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),i.tail=this.type===tr.backQuote,this.finishNode(i,"TemplateElement")},qr.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var i=this.startNode();this.next(),i.expressions=[];var n=this.parseTemplateElement({isTagged:t});for(i.quasis=[n];!n.tail;)this.type===tr.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(tr.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(tr.braceR),i.quasis.push(n=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(i,"TemplateLiteral")},qr.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===tr.name||this.type===tr.num||this.type===tr.string||this.type===tr.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===tr.star)&&!ir.test(this.input.slice(this.lastTokEnd,this.start))},qr.parseObj=function(e,t){var i=this.startNode(),n=!0,s={};for(i.properties=[],this.next();!this.eat(tr.braceR);){if(n)n=!1;else if(this.expect(tr.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(tr.braceR))break;var r=this.parseProperty(e,t);e||this.checkPropClash(r,s,t),i.properties.push(r)}return this.finishNode(i,e?"ObjectPattern":"ObjectExpression")},qr.parseProperty=function(e,t){var i,n,s,r,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(tr.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===tr.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(a.argument=this.parseMaybeAssign(!1,t),this.type===tr.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(s=this.start,r=this.startLoc),e||(i=this.eat(tr.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(a)?(n=!0,i=this.options.ecmaVersion>=9&&this.eat(tr.star),this.parsePropertyName(a,t)):n=!1,this.parsePropertyValue(a,e,i,n,s,r,t,o),this.finishNode(a,"Property")},qr.parsePropertyValue=function(e,t,i,n,s,r,a,o){if((i||n)&&this.type===tr.colon&&this.unexpected(),this.eat(tr.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===tr.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(i,n);else if(t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===tr.comma||this.type===tr.braceR||this.type===tr.eq)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((i||n)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=s),e.kind="init",t?e.value=this.parseMaybeDefault(s,r,this.copyNode(e.key)):this.type===tr.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(s,r,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected();else{(i||n)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var u="get"===e.kind?0:1;if(e.value.params.length!==u){var c=e.value.start;"get"===e.kind?this.raiseRecoverable(c,"getter should have no params"):this.raiseRecoverable(c,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},qr.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(tr.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(tr.bracketR),e.key;e.computed=!1}return e.key=this.type===tr.num||this.type===tr.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},qr.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},qr.parseMethod=function(e,t,i){var n=this.startNode(),s=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(n),this.options.ecmaVersion>=6&&(n.generator=e),this.options.ecmaVersion>=8&&(n.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|Dr(t,n.generator)|(i?128:0)),this.expect(tr.parenL),n.params=this.parseBindingList(tr.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(n,!1,!0,!1),this.yieldPos=s,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(n,"FunctionExpression")},qr.parseArrowExpression=function(e,t,i,n){var s=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.enterScope(16|Dr(i,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,n),this.yieldPos=s,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(e,"ArrowFunctionExpression")},qr.parseFunctionBody=function(e,t,i,n){var s=t&&this.type!==tr.braceL,r=this.strict,a=!1;if(s)e.body=this.parseMaybeAssign(n),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);r&&!o||(a=this.strictDirective(this.end))&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var u=this.labels;this.labels=[],a&&(this.strict=!0),this.checkParams(e,!r&&!a&&!t&&!i&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,5),e.body=this.parseBlock(!1,void 0,a&&!r),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=u}this.exitScope()},qr.isSimpleParamList=function(e){for(var t=0,i=e;t<i.length;t+=1){if("Identifier"!==i[t].type)return!1}return!0},qr.checkParams=function(e,t){for(var i=Object.create(null),n=0,s=e.params;n<s.length;n+=1){var r=s[n];this.checkLValInnerPattern(r,1,t?null:i)}},qr.parseExprList=function(e,t,i,n){for(var s=[],r=!0;!this.eat(e);){if(r)r=!1;else if(this.expect(tr.comma),t&&this.afterTrailingComma(e))break;var a=void 0;i&&this.type===tr.comma?a=null:this.type===tr.ellipsis?(a=this.parseSpread(n),n&&this.type===tr.comma&&n.trailingComma<0&&(n.trailingComma=this.start)):a=this.parseMaybeAssign(!1,n),s.push(a)}return s},qr.checkUnreserved=function(e){var t=e.start,i=e.end,n=e.name;(this.inGenerator&&"yield"===n&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===n&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&"arguments"===n&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),!this.inClassStaticBlock||"arguments"!==n&&"await"!==n||this.raise(t,"Cannot use "+n+" in class static initialization block"),this.keywords.test(n)&&this.raise(t,"Unexpected keyword '"+n+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,i).indexOf("\\"))||(this.strict?this.reservedWordsStrict:this.reservedWords).test(n)&&(this.inAsync||"await"!==n||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+n+"' is reserved"))},qr.parseIdent=function(e,t){var i=this.startNode();return this.type===tr.name?i.name=this.value:this.type.keyword?(i.name=this.type.keyword,"class"!==i.name&&"function"!==i.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(i,"Identifier"),e||(this.checkUnreserved(i),"await"!==i.name||this.awaitIdentPos||(this.awaitIdentPos=i.start)),i},qr.parsePrivateIdent=function(){var e=this.startNode();return this.type===tr.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),0===this.privateNameStack.length?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e),e},qr.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===tr.semi||this.canInsertSemicolon()||this.type!==tr.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(tr.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},qr.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var zr=Ar.prototype;zr.raise=function(e,t){var i=xr(this.input,e);t+=" ("+i.line+":"+i.column+")";var n=new SyntaxError(t);throw n.pos=e,n.loc=i,n.raisedAt=this.pos,n},zr.raiseRecoverable=zr.raise,zr.curPosition=function(){if(this.options.locations)return new gr(this.curLine,this.pos-this.lineStart)};var Wr=Ar.prototype,Hr=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};Wr.enterScope=function(e){this.scopeStack.push(new Hr(e))},Wr.exitScope=function(){this.scopeStack.pop()},Wr.treatFunctionsAsVarInScope=function(e){return 2&e.flags||!this.inModule&&1&e.flags},Wr.declareName=function(e,t,i){var n=!1;if(2===t){var s=this.currentScope();n=s.lexical.indexOf(e)>-1||s.functions.indexOf(e)>-1||s.var.indexOf(e)>-1,s.lexical.push(e),this.inModule&&1&s.flags&&delete this.undefinedExports[e]}else if(4===t){this.currentScope().lexical.push(e)}else if(3===t){var r=this.currentScope();n=this.treatFunctionsAsVar?r.lexical.indexOf(e)>-1:r.lexical.indexOf(e)>-1||r.var.indexOf(e)>-1,r.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(32&o.flags&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){n=!0;break}if(o.var.push(e),this.inModule&&1&o.flags&&delete this.undefinedExports[e],259&o.flags)break}n&&this.raiseRecoverable(i,"Identifier '"+e+"' has already been declared")},Wr.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},Wr.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},Wr.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags)return t}},Wr.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags&&!(16&t.flags))return t}};var Gr=function(e,t,i){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new vr(e,i)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},Qr=Ar.prototype;function Kr(e,t,i,n){return e.type=t,e.end=i,this.options.locations&&(e.loc.end=n),this.options.ranges&&(e.range[1]=i),e}Qr.startNode=function(){return new Gr(this,this.start,this.startLoc)},Qr.startNodeAt=function(e,t){return new Gr(this,e,t)},Qr.finishNode=function(e,t){return Kr.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},Qr.finishNodeAt=function(e,t,i,n){return Kr.call(this,e,t,i,n)},Qr.copyNode=function(e){var t=new Gr(this,e.start,this.startLoc);for(var i in e)t[i]=e[i];return t};var Yr="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",Jr=Yr+" Extended_Pictographic",Xr=Jr+" EBase EComp EMod EPres ExtPict",Zr={9:Yr,10:Jr,11:Jr,12:Xr,13:Xr},ea="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",ta="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",ia=ta+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",na=ia+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",sa=na+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",ra={9:ta,10:ia,11:na,12:sa,13:sa+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith"},aa={};function oa(e){var t=aa[e]={binary:dr(Zr[e]+" "+ea),nonBinary:{General_Category:dr(ea),Script:dr(ra[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var ua=0,ca=[9,10,11,12,13];ua<ca.length;ua+=1){oa(ca[ua])}var pa=Ar.prototype,la=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":""),this.unicodeProperties=aa[e.options.ecmaVersion>=13?13:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function ha(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function da(e){return e>=65&&e<=90||e>=97&&e<=122}function fa(e){return da(e)||95===e}function ma(e){return fa(e)||ga(e)}function ga(e){return e>=48&&e<=57}function va(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function xa(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function ya(e){return e>=48&&e<=55}la.prototype.reset=function(e,t,i){var n=-1!==i.indexOf("u");this.start=0|e,this.source=t+"",this.flags=i,this.switchU=n&&this.parser.options.ecmaVersion>=6,this.switchN=n&&this.parser.options.ecmaVersion>=9},la.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},la.prototype.at=function(e,t){void 0===t&&(t=!1);var i=this.source,n=i.length;if(e>=n)return-1;var s=i.charCodeAt(e);if(!t&&!this.switchU||s<=55295||s>=57344||e+1>=n)return s;var r=i.charCodeAt(e+1);return r>=56320&&r<=57343?(s<<10)+r-56613888:s},la.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var i=this.source,n=i.length;if(e>=n)return n;var s,r=i.charCodeAt(e);return!t&&!this.switchU||r<=55295||r>=57344||e+1>=n||(s=i.charCodeAt(e+1))<56320||s>57343?e+1:e+2},la.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},la.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},la.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},la.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},pa.validateRegExpFlags=function(e){for(var t=e.validFlags,i=e.flags,n=0;n<i.length;n++){var s=i.charAt(n);-1===t.indexOf(s)&&this.raise(e.start,"Invalid regular expression flag"),i.indexOf(s,n+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},pa.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},pa.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,i=e.backReferenceNames;t<i.length;t+=1){var n=i[t];-1===e.groupNames.indexOf(n)&&e.raise("Invalid named capture referenced")}},pa.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},pa.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},pa.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},pa.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!i,!0}return e.pos=t,!1},pa.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},pa.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},pa.regexp_eatBracedQuantifier=function(e,t){var i=e.pos;if(e.eat(123)){var n=0,s=-1;if(this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue),e.eat(125)))return-1!==s&&s<n&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=i}return!1},pa.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},pa.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},pa.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},pa.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},pa.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},pa.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},pa.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!ha(t)&&(e.lastIntValue=t,e.advance(),!0)},pa.regexp_eatPatternCharacters=function(e){for(var t=e.pos,i=0;-1!==(i=e.current())&&!ha(i);)e.advance();return e.pos!==t},pa.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t)&&(e.advance(),!0)},pa.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e))return-1!==e.groupNames.indexOf(e.lastStringValue)&&e.raise("Duplicate capture group name"),void e.groupNames.push(e.lastStringValue);e.raise("Invalid group")}},pa.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},pa.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=fr(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=fr(e.lastIntValue);return!0}return!1},pa.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,n=e.current(i);return e.advance(i),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(n=e.lastIntValue),function(e){return Gs(e,!0)||36===e||95===e}(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},pa.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,n=e.current(i);return e.advance(i),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(n=e.lastIntValue),function(e){return Qs(e,!0)||36===e||95===e||8204===e||8205===e}(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},pa.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},pa.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var i=e.lastIntValue;if(e.switchU)return i>e.maxBackReference&&(e.maxBackReference=i),!0;if(i<=e.numCapturingParens)return!0;e.pos=t}return!1},pa.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},pa.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},pa.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},pa.regexp_eatZero=function(e){return 48===e.current()&&!ga(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},pa.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},pa.regexp_eatControlLetter=function(e){var t=e.current();return!!da(t)&&(e.lastIntValue=t%32,e.advance(),!0)},pa.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var i=e.pos,n=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var s=e.lastIntValue;if(n&&s>=55296&&s<=56319){var r=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(a>=56320&&a<=57343)return e.lastIntValue=1024*(s-55296)+(a-56320)+65536,!0}e.pos=r,e.lastIntValue=s}return!0}if(n&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&function(e){return e>=0&&e<=1114111}(e.lastIntValue))return!0;n&&e.raise("Invalid unicode escape"),e.pos=i}return!1},pa.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t)&&(e.lastIntValue=t,e.advance(),!0)},pa.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1},pa.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(function(e){return 100===e||68===e||115===e||83===e||119===e||87===e}(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(80===t||112===t)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1},pa.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var i=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,i,n),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,s),!0}return!1},pa.regexp_validateUnicodePropertyNameAndValue=function(e,t,i){lr(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(i)||e.raise("Invalid property value")},pa.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},pa.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";fa(t=e.current());)e.lastStringValue+=fr(t),e.advance();return""!==e.lastStringValue},pa.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";ma(t=e.current());)e.lastStringValue+=fr(t),e.advance();return""!==e.lastStringValue},pa.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},pa.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},pa.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var i=e.lastIntValue;!e.switchU||-1!==t&&-1!==i||e.raise("Invalid character class"),-1!==t&&-1!==i&&t>i&&e.raise("Range out of order in character class")}}},pa.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var i=e.current();(99===i||ya(i))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var n=e.current();return 93!==n&&(e.lastIntValue=n,e.advance(),!0)},pa.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},pa.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!ga(t)&&95!==t)&&(e.lastIntValue=t%32,e.advance(),!0)},pa.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},pa.regexp_eatDecimalDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;ga(i=e.current());)e.lastIntValue=10*e.lastIntValue+(i-48),e.advance();return e.pos!==t},pa.regexp_eatHexDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;va(i=e.current());)e.lastIntValue=16*e.lastIntValue+xa(i),e.advance();return e.pos!==t},pa.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var i=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*i+e.lastIntValue:e.lastIntValue=8*t+i}else e.lastIntValue=t;return!0}return!1},pa.regexp_eatOctalDigit=function(e){var t=e.current();return ya(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},pa.regexp_eatFixedHexDigits=function(e,t){var i=e.pos;e.lastIntValue=0;for(var n=0;n<t;++n){var s=e.current();if(!va(s))return e.pos=i,!1;e.lastIntValue=16*e.lastIntValue+xa(s),e.advance()}return!0};var ba=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new vr(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},Ea=Ar.prototype;function Ca(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}Ea.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new ba(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},Ea.getToken=function(){return this.next(),new ba(this)},"undefined"!=typeof Symbol&&(Ea[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===tr.eof,value:t}}}}),Ea.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(tr.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},Ea.readToken=function(e){return Gs(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},Ea.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},Ea.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,i=this.input.indexOf("*/",this.pos+=2);if(-1===i&&this.raise(this.pos-2,"Unterminated comment"),this.pos=i+2,this.options.locations)for(var n=void 0,s=t;(n=rr(this.input,s,this.pos))>-1;)++this.curLine,s=this.lineStart=n;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,i),t,this.pos,e,this.curPosition())},Ea.skipLineComment=function(e){for(var t=this.pos,i=this.options.onComment&&this.curPosition(),n=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!sr(n);)n=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,i,this.curPosition())},Ea.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&ar.test(String.fromCharCode(e))))break e;++this.pos}}},Ea.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=e,this.value=t,this.updateContext(i)},Ea.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(tr.ellipsis)):(++this.pos,this.finishToken(tr.dot))},Ea.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(tr.assign,2):this.finishOp(tr.slash,1)},Ea.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),i=1,n=42===e?tr.star:tr.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++i,n=tr.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(tr.assign,i+1):this.finishOp(n,i)},Ea.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(tr.assign,3);return this.finishOp(124===e?tr.logicalOR:tr.logicalAND,2)}return 61===t?this.finishOp(tr.assign,2):this.finishOp(124===e?tr.bitwiseOR:tr.bitwiseAND,1)},Ea.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(tr.assign,2):this.finishOp(tr.bitwiseXOR,1)},Ea.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!ir.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(tr.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(tr.assign,2):this.finishOp(tr.plusMin,1)},Ea.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),i=1;return t===e?(i=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+i)?this.finishOp(tr.assign,i+1):this.finishOp(tr.bitShift,i)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(i=2),this.finishOp(tr.relational,i)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},Ea.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(tr.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(tr.arrow)):this.finishOp(61===e?tr.eq:tr.prefix,1)},Ea.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(tr.questionDot,2)}if(63===t){if(e>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(tr.assign,3);return this.finishOp(tr.coalesce,2)}}return this.finishOp(tr.question,1)},Ea.readToken_numberSign=function(){var e=35;if(this.options.ecmaVersion>=13&&(++this.pos,Gs(e=this.fullCharCodeAtPos(),!0)||92===e))return this.finishToken(tr.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+fr(e)+"'")},Ea.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(tr.parenL);case 41:return++this.pos,this.finishToken(tr.parenR);case 59:return++this.pos,this.finishToken(tr.semi);case 44:return++this.pos,this.finishToken(tr.comma);case 91:return++this.pos,this.finishToken(tr.bracketL);case 93:return++this.pos,this.finishToken(tr.bracketR);case 123:return++this.pos,this.finishToken(tr.braceL);case 125:return++this.pos,this.finishToken(tr.braceR);case 58:return++this.pos,this.finishToken(tr.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(tr.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(tr.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+fr(e)+"'")},Ea.finishOp=function(e,t){var i=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,i)},Ea.readRegexp=function(){for(var e,t,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var n=this.input.charAt(this.pos);if(ir.test(n)&&this.raise(i,"Unterminated regular expression"),e)e=!1;else{if("["===n)t=!0;else if("]"===n&&t)t=!1;else if("/"===n&&!t)break;e="\\"===n}++this.pos}var s=this.input.slice(i,this.pos);++this.pos;var r=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(r);var o=this.regexpState||(this.regexpState=new la(this));o.reset(i,s,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var u=null;try{u=new RegExp(s,a)}catch(e){}return this.finishToken(tr.regexp,{pattern:s,flags:a,value:u})},Ea.readInt=function(e,t,i){for(var n=this.options.ecmaVersion>=12&&void 0===t,s=i&&48===this.input.charCodeAt(this.pos),r=this.pos,a=0,o=0,u=0,c=null==t?1/0:t;u<c;++u,++this.pos){var p=this.input.charCodeAt(this.pos),l=void 0;if(n&&95===p)s&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===u&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=p;else{if((l=p>=97?p-97+10:p>=65?p-65+10:p>=48&&p<=57?p-48:1/0)>=e)break;o=p,a=a*e+l}}return n&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===r||null!=t&&this.pos-r!==t?null:a},Ea.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var i=this.readInt(e);return null==i&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(i=Ca(this.input.slice(t,this.pos)),++this.pos):Gs(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(tr.num,i)},Ea.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var i=this.pos-t>=2&&48===this.input.charCodeAt(t);i&&this.strict&&this.raise(t,"Invalid number");var n=this.input.charCodeAt(this.pos);if(!i&&!e&&this.options.ecmaVersion>=11&&110===n){var s=Ca(this.input.slice(t,this.pos));return++this.pos,Gs(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(tr.num,s)}i&&/[89]/.test(this.input.slice(t,this.pos))&&(i=!1),46!==n||i||(++this.pos,this.readInt(10),n=this.input.charCodeAt(this.pos)),69!==n&&101!==n||i||(43!==(n=this.input.charCodeAt(++this.pos))&&45!==n||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),Gs(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var r,a=(r=this.input.slice(t,this.pos),i?parseInt(r,8):parseFloat(r.replace(/_/g,"")));return this.finishToken(tr.num,a)},Ea.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},Ea.readString=function(e){for(var t="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var n=this.input.charCodeAt(this.pos);if(n===e)break;92===n?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(!1),i=this.pos):8232===n||8233===n?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(sr(n)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(tr.string,t)};var Da={};Ea.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==Da)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},Ea.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Da;this.raise(e,t)},Ea.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==tr.template&&this.type!==tr.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(tr.template,e)):36===i?(this.pos+=2,this.finishToken(tr.dollarBraceL)):(++this.pos,this.finishToken(tr.backQuote));if(92===i)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(sr(i)){switch(e+=this.input.slice(t,this.pos),++this.pos,i){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(i)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},Ea.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(tr.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},Ea.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return fr(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var i=this.pos-1;return this.invalidStringToken(i,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var n=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],s=parseInt(n,8);return s>255&&(n=n.slice(0,-1),s=parseInt(n,8)),this.pos+=n.length-1,t=this.input.charCodeAt(this.pos),"0"===n&&56!==t&&57!==t||!this.strict&&!e||this.invalidStringToken(this.pos-1-n.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(s)}return sr(t)?"":String.fromCharCode(t)}},Ea.readHexChar=function(e){var t=this.pos,i=this.readInt(16,e);return null===i&&this.invalidStringToken(t,"Bad character escape sequence"),i},Ea.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,n=this.options.ecmaVersion>=6;this.pos<this.input.length;){var s=this.fullCharCodeAtPos();if(Qs(s,n))this.pos+=s<=65535?1:2;else{if(92!==s)break;this.containsEsc=!0,e+=this.input.slice(i,this.pos);var r=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?Gs:Qs)(a,n)||this.invalidStringToken(r,"Invalid Unicode escape"),e+=fr(a),i=this.pos}t=!1}return e+this.input.slice(i,this.pos)},Ea.readWord=function(){var e=this.readWord1(),t=tr.name;return this.keywords.test(e)&&(t=Zs[e]),this.finishToken(t,e)};Ar.acorn={Parser:Ar,version:"8.8.1",defaultOptions:yr,Position:gr,SourceLocation:vr,getLineInfo:xr,Node:Gr,TokenType:Ks,tokTypes:tr,keywordTypes:Zs,TokContext:Or,tokContexts:Mr,isIdentifierChar:Qs,isIdentifierStart:Gs,Token:ba,isNewLine:sr,lineBreak:ir,lineBreakG:nr,nonASCIIwhitespace:ar};var Aa=new Set(["Array","ArrayBuffer","atob","AudioContext","Blob","Boolean","BigInt","btoa","clearInterval","clearTimeout","console","crypto","CustomEvent","DataView","Date","decodeURI","decodeURIComponent","devicePixelRatio","document","encodeURI","encodeURIComponent","Error","escape","eval","fetch","File","FileList","FileReader","Float32Array","Float64Array","Function","Headers","Image","ImageData","Infinity","Int16Array","Int32Array","Int8Array","Intl","isFinite","isNaN","JSON","Map","Math","NaN","Number","navigator","Object","parseFloat","parseInt","performance","Path2D","Promise","Proxy","RangeError","ReferenceError","Reflect","RegExp","cancelAnimationFrame","requestAnimationFrame","Set","setInterval","setTimeout","String","Symbol","SyntaxError","TextDecoder","TextEncoder","this","TypeError","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","undefined","unescape","URIError","URL","WeakMap","WeakSet","WebSocket","Worker","window"]);function wa(e,t,i,n,s){i||(i=Ba),function e(n,s,r){var a=r||n.type,o=t[a];i[a](n,s,e),o&&o(n,s)}(e,n,s)}function Fa(e,t,i,n,s){var r=[];i||(i=Ba),function e(n,s,a){var o=a||n.type,u=t[o],c=n!==r[r.length-1];c&&r.push(n),i[o](n,s,e),u&&u(n,s||r,r),c&&r.pop()}(e,n,s)}function ka(e,t){var i=Object.create(t||Ba);for(var n in e)i[n]=e[n];return i}function Sa(e,t,i){i(e,t)}function _a(e,t,i){}var Ba={};Ba.Program=Ba.BlockStatement=Ba.StaticBlock=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1){i(s[n],t,"Statement")}},Ba.Statement=Sa,Ba.EmptyStatement=_a,Ba.ExpressionStatement=Ba.ParenthesizedExpression=Ba.ChainExpression=function(e,t,i){return i(e.expression,t,"Expression")},Ba.IfStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Statement"),e.alternate&&i(e.alternate,t,"Statement")},Ba.LabeledStatement=function(e,t,i){return i(e.body,t,"Statement")},Ba.BreakStatement=Ba.ContinueStatement=_a,Ba.WithStatement=function(e,t,i){i(e.object,t,"Expression"),i(e.body,t,"Statement")},Ba.SwitchStatement=function(e,t,i){i(e.discriminant,t,"Expression");for(var n=0,s=e.cases;n<s.length;n+=1){var r=s[n];r.test&&i(r.test,t,"Expression");for(var a=0,o=r.consequent;a<o.length;a+=1){i(o[a],t,"Statement")}}},Ba.SwitchCase=function(e,t,i){e.test&&i(e.test,t,"Expression");for(var n=0,s=e.consequent;n<s.length;n+=1){i(s[n],t,"Statement")}},Ba.ReturnStatement=Ba.YieldExpression=Ba.AwaitExpression=function(e,t,i){e.argument&&i(e.argument,t,"Expression")},Ba.ThrowStatement=Ba.SpreadElement=function(e,t,i){return i(e.argument,t,"Expression")},Ba.TryStatement=function(e,t,i){i(e.block,t,"Statement"),e.handler&&i(e.handler,t),e.finalizer&&i(e.finalizer,t,"Statement")},Ba.CatchClause=function(e,t,i){e.param&&i(e.param,t,"Pattern"),i(e.body,t,"Statement")},Ba.WhileStatement=Ba.DoWhileStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.body,t,"Statement")},Ba.ForStatement=function(e,t,i){e.init&&i(e.init,t,"ForInit"),e.test&&i(e.test,t,"Expression"),e.update&&i(e.update,t,"Expression"),i(e.body,t,"Statement")},Ba.ForInStatement=Ba.ForOfStatement=function(e,t,i){i(e.left,t,"ForInit"),i(e.right,t,"Expression"),i(e.body,t,"Statement")},Ba.ForInit=function(e,t,i){"VariableDeclaration"===e.type?i(e,t):i(e,t,"Expression")},Ba.DebuggerStatement=_a,Ba.FunctionDeclaration=function(e,t,i){return i(e,t,"Function")},Ba.VariableDeclaration=function(e,t,i){for(var n=0,s=e.declarations;n<s.length;n+=1){i(s[n],t)}},Ba.VariableDeclarator=function(e,t,i){i(e.id,t,"Pattern"),e.init&&i(e.init,t,"Expression")},Ba.Function=function(e,t,i){e.id&&i(e.id,t,"Pattern");for(var n=0,s=e.params;n<s.length;n+=1){i(s[n],t,"Pattern")}i(e.body,t,e.expression?"Expression":"Statement")},Ba.Pattern=function(e,t,i){"Identifier"===e.type?i(e,t,"VariablePattern"):"MemberExpression"===e.type?i(e,t,"MemberPattern"):i(e,t)},Ba.VariablePattern=_a,Ba.MemberPattern=Sa,Ba.RestElement=function(e,t,i){return i(e.argument,t,"Pattern")},Ba.ArrayPattern=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Pattern")}},Ba.ObjectPattern=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1){var r=s[n];"Property"===r.type?(r.computed&&i(r.key,t,"Expression"),i(r.value,t,"Pattern")):"RestElement"===r.type&&i(r.argument,t,"Pattern")}},Ba.Expression=Sa,Ba.ThisExpression=Ba.Super=Ba.MetaProperty=_a,Ba.ArrayExpression=function(e,t,i){for(var n=0,s=e.elements;n<s.length;n+=1){var r=s[n];r&&i(r,t,"Expression")}},Ba.ObjectExpression=function(e,t,i){for(var n=0,s=e.properties;n<s.length;n+=1){i(s[n],t)}},Ba.FunctionExpression=Ba.ArrowFunctionExpression=Ba.FunctionDeclaration,Ba.SequenceExpression=function(e,t,i){for(var n=0,s=e.expressions;n<s.length;n+=1){i(s[n],t,"Expression")}},Ba.TemplateLiteral=function(e,t,i){for(var n=0,s=e.quasis;n<s.length;n+=1){i(s[n],t)}for(var r=0,a=e.expressions;r<a.length;r+=1){i(a[r],t,"Expression")}},Ba.TemplateElement=_a,Ba.UnaryExpression=Ba.UpdateExpression=function(e,t,i){i(e.argument,t,"Expression")},Ba.BinaryExpression=Ba.LogicalExpression=function(e,t,i){i(e.left,t,"Expression"),i(e.right,t,"Expression")},Ba.AssignmentExpression=Ba.AssignmentPattern=function(e,t,i){i(e.left,t,"Pattern"),i(e.right,t,"Expression")},Ba.ConditionalExpression=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Expression"),i(e.alternate,t,"Expression")},Ba.NewExpression=Ba.CallExpression=function(e,t,i){if(i(e.callee,t,"Expression"),e.arguments)for(var n=0,s=e.arguments;n<s.length;n+=1){i(s[n],t,"Expression")}},Ba.MemberExpression=function(e,t,i){i(e.object,t,"Expression"),e.computed&&i(e.property,t,"Expression")},Ba.ExportNamedDeclaration=Ba.ExportDefaultDeclaration=function(e,t,i){e.declaration&&i(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&i(e.source,t,"Expression")},Ba.ExportAllDeclaration=function(e,t,i){e.exported&&i(e.exported,t),i(e.source,t,"Expression")},Ba.ImportDeclaration=function(e,t,i){for(var n=0,s=e.specifiers;n<s.length;n+=1){i(s[n],t)}i(e.source,t,"Expression")},Ba.ImportExpression=function(e,t,i){i(e.source,t,"Expression")},Ba.ImportSpecifier=Ba.ImportDefaultSpecifier=Ba.ImportNamespaceSpecifier=Ba.Identifier=Ba.PrivateIdentifier=Ba.Literal=_a,Ba.TaggedTemplateExpression=function(e,t,i){i(e.tag,t,"Expression"),i(e.quasi,t,"Expression")},Ba.ClassDeclaration=Ba.ClassExpression=function(e,t,i){return i(e,t,"Class")},Ba.Class=function(e,t,i){e.id&&i(e.id,t,"Pattern"),e.superClass&&i(e.superClass,t,"Expression"),i(e.body,t)},Ba.ClassBody=function(e,t,i){for(var n=0,s=e.body;n<s.length;n+=1){i(s[n],t)}},Ba.MethodDefinition=Ba.PropertyDefinition=Ba.Property=function(e,t,i){e.computed&&i(e.key,t,"Expression"),e.value&&i(e.value,t,"Expression")};var Ia=ka({Import(){},ViewExpression(e,t,i){i(e.id,t,"Identifier")},MutableExpression(e,t,i){i(e.id,t,"Identifier")}});function Pa(e){return"FunctionExpression"===e.type||"FunctionDeclaration"===e.type||"ArrowFunctionExpression"===e.type||"Program"===e.type}function Na(e){return"BlockStatement"===e.type||"ForInStatement"===e.type||"ForOfStatement"===e.type||"ForStatement"===e.type||Pa(e)}function Ta(e){return"FunctionExpression"===e.type||"FunctionDeclaration"===e.type}function La(e,t){const i={type:"Program",body:[e.body]},n=new Map,{references:s}=e;return wa(i,{CallExpression:e=>{const{callee:i,arguments:r}=e;if("Identifier"!==i.type||i.name!==t||s.indexOf(i)<0)return;if(1!==r.length||!("Literal"===r[0].type&&/^['"]/.test(r[0].raw)||"TemplateLiteral"===r[0].type&&0===r[0].expressions.length))throw Object.assign(new SyntaxError(`${t} requires a single literal string argument`),{node:e});const[a]=r,o="Literal"===a.type?a.value:a.quasis[0].value.cooked,u={start:a.start,end:a.end};n.has(o)?n.get(o).push(u):n.set(o,[u])}},Ia),n}class Va extends Ar{constructor(e,...t){super(Object.assign({ecmaVersion:13},e),...t)}enterScope(e){return 2&e&&++this.O_function,super.enterScope(e)}exitScope(){return 2&this.currentScope().flags&&--this.O_function,super.exitScope()}parseForIn(e,t){return 1===this.O_function&&e.await&&(this.O_async=!0),super.parseForIn(e,t)}parseAwait(){return 1===this.O_function&&(this.O_async=!0),super.parseAwait()}parseYield(e){return 1===this.O_function&&(this.O_generator=!0),super.parseYield(e)}parseImport(e){return this.next(),e.specifiers=this.parseImportSpecifiers(),this.type===tr._with&&(this.next(),e.injections=this.parseImportSpecifiers()),this.expectContextual("from"),e.source=this.type===tr.string?this.parseExprAtom():this.unexpected(),this.finishNode(e,"ImportDeclaration")}parseImportSpecifiers(){const e=[],t=new Set;let i=!0;for(this.expect(tr.braceL);!this.eat(tr.braceR);){if(i)i=!1;else if(this.expect(tr.comma),this.afterTrailingComma(tr.braceR))break;const n=this.startNode();n.view=this.eatContextual("viewof"),n.mutable=!n.view&&this.eatContextual("mutable"),n.imported=this.parseIdent(),this.checkUnreserved(n.imported),this.checkLocal(n.imported),this.eatContextual("as")?(n.local=this.parseIdent(),this.checkUnreserved(n.local),this.checkLocal(n.local)):n.local=n.imported,this.checkLValSimple(n.local,"let"),t.has(n.local.name)&&this.raise(n.local.start,`Identifier '${n.local.name}' has already been declared`),t.add(n.local.name),e.push(this.finishNode(n,"ImportSpecifier"))}return e}parseExprAtom(e){return this.parseMaybeKeywordExpression("viewof","ViewExpression")||this.parseMaybeKeywordExpression("mutable","MutableExpression")||super.parseExprAtom(e)}startCell(){this.O_function=0,this.O_async=!1,this.O_generator=!1,this.strict=!0,this.enterScope(14)}finishCell(e,t,i){return i&&this.checkLocal(i),e.id=i,e.body=t,e.async=this.O_async,e.generator=this.O_generator,this.exitScope(),this.finishNode(e,"Cell")}parseCell(e,t){const i=new Va({},this.input,this.start);let n=i.getToken(),s=null,r=null;return this.startCell(),n.type===tr._import&&i.getToken().type!==tr.parenL?s=this.parseImport(this.startNode()):n.type!==tr.eof&&n.type!==tr.semi&&(n.type===tr.name&&("viewof"!==n.value&&"mutable"!==n.value||(n=i.getToken(),n.type!==tr.name&&i.unexpected()),n=i.getToken(),n.type===tr.eq&&(r=this.parseMaybeKeywordExpression("viewof","ViewExpression")||this.parseMaybeKeywordExpression("mutable","MutableExpression")||this.parseIdent(),n=i.getToken(),this.expect(tr.eq))),n.type===tr.braceL?s=this.parseBlock():(s=this.parseExpression(),null!==r||"FunctionExpression"!==s.type&&"ClassExpression"!==s.type||(r=s.id))),this.semicolon(),t&&this.expect(tr.eof),this.finishCell(e,s,r)}parseTopLevel(e){return this.parseCell(e,!0)}toAssignable(e,t,i){return"MutableExpression"===e.type?e:super.toAssignable(e,t,i)}checkLocal(e){const t=e.id||e;(Aa.has(t.name)||"arguments"===t.name)&&this.raise(t.start,`Identifier '${t.name}' is reserved`)}checkUnreserved(e){return"viewof"!==e.name&&"mutable"!==e.name||this.raise(e.start,`Unexpected keyword '${e.name}'`),super.checkUnreserved(e)}checkLValSimple(e,t,i){return super.checkLValSimple("MutableExpression"===e.type?e.id:e,t,i)}unexpected(e){this.raise(null!=e?e:this.start,this.type===tr.eof?"Unexpected end of input":"Unexpected token")}parseMaybeKeywordExpression(e,t){if(this.isContextual(e)){const e=this.startNode();return this.next(),e.id=this.parseIdent(),this.finishNode(e,t)}}}function Ra(){e:for(;this.pos<this.input.length;this.pos++)switch(this.input.charCodeAt(this.pos)){case 92:this.pos<this.input.length-1&&++this.pos;break;case 36:if(123===this.input.charCodeAt(this.pos+1)){if(this.pos===this.start&&this.type===tr.invalidTemplate)return this.pos+=2,this.finishToken(tr.dollarBraceL);break e}}return this.finishToken(tr.invalidTemplate,this.input.slice(this.start,this.pos))}function Oa(e,t,i=Aa){if(e.body)if("ImportDeclaration"===e.body.type)e.references=e.body.injections?e.body.injections.map((e=>e.imported)):[];else try{e.references=function(e,t){const i={type:"Program",body:[e.body]},n=new Map,s=new Set(t),r=[];function a(e,t){const i=n.get(e);return!!i&&i.has(t)}function o(e,t){const i=n.get(e);i?i.add(t.name):n.set(e,new Set([t.name]))}function u(e){e.params.forEach((t=>c(t,e))),e.id&&o(e,e.id)}function c(e,t){switch(e.type){case"Identifier":o(t,e);break;case"ObjectPattern":e.properties.forEach((e=>c(e,t)));break;case"ArrayPattern":e.elements.forEach((e=>e&&c(e,t)));break;case"Property":c(e.value,t);break;case"RestElement":c(e.argument,t);break;case"AssignmentPattern":c(e.left,t);break;default:throw new Error("Unrecognized pattern type: "+e.type)}}function p(e){o(i,e.local)}function l(e,t){let i=e.name;if("undefined"!==i){for(let n=t.length-2;n>=0;--n){if("arguments"===i&&Ta(t[n]))return;if(a(t[n],i))return;"ViewExpression"===t[n].type&&(i=`viewof ${(e=t[n]).id.name}`),"MutableExpression"===t[n].type&&(i=`mutable ${(e=t[n]).id.name}`)}if(!s.has(i)){if("arguments"===i)throw Object.assign(new SyntaxError("arguments is not allowed"),{node:e});r.push(e)}}}function h(e,t){if(e)switch(e.type){case"Identifier":case"VariablePattern":for(const i of t)if(a(i,e.name))return;if("MutableExpression"===t[t.length-2].type)return;throw Object.assign(new SyntaxError(`Assignment to constant variable ${e.name}`),{node:e});case"ArrayPattern":for(const i of e.elements)h(i,t);return;case"ObjectPattern":for(const i of e.properties)h(i,t);return;case"Property":return void h(e.value,t);case"RestElement":return void h(e.argument,t)}}function d(e,t){h(e.left,t)}return Fa(i,{VariableDeclaration:(e,t)=>{let i=null;for(let n=t.length-1;n>=0&&null===i;--n)("var"===e.kind?Pa(t[n]):Na(t[n]))&&(i=t[n]);e.declarations.forEach((e=>c(e.id,i)))},FunctionDeclaration:(e,t)=>{let i=null;for(let e=t.length-2;e>=0&&null===i;--e)Pa(t[e])&&(i=t[e]);o(i,e.id),u(e)},Function:u,ClassDeclaration:(e,t)=>{let i=null;for(let e=t.length-2;e>=0&&null===i;e--)Pa(t[e])&&(i=t[e]);o(i,e.id)},Class:function(e){e.id&&o(e,e.id)},CatchClause:function(e){e.param&&c(e.param,e)},ImportDefaultSpecifier:p,ImportSpecifier:p,ImportNamespaceSpecifier:p},Ia),Fa(i,{VariablePattern:l,Identifier:l},Ia),Fa(i,{AssignmentExpression:d,AssignmentPattern:d,UpdateExpression:function(e,t){h(e.argument,t)},ForOfStatement:d,ForInStatement:d},Ia),r}(e,i)}catch(e){if(e.node){const i=xr(t,e.node.start);e.message+=` (${i.line}:${i.column})`,e.pos=e.node.start,e.loc=i,delete e.node}throw e}else e.references=[];return e}function Ma(e,t){if(e.body&&"ImportDeclaration"!==e.body.type)try{e.fileAttachments=La(e,"FileAttachment"),e.databaseClients=La(e,"DatabaseClient"),e.secrets=La(e,"Secret"),e.notificationClients=La(e,"NotificationClient")}catch(e){if(e.node){const i=xr(t,e.node.start);e.message+=` (${i.line}:${i.column})`,e.pos=e.node.start,e.loc=i,delete e.node}throw e}else e.fileAttachments=new Map,e.databaseClients=new Map,e.secrets=new Map,e.notificationClients=new Map;return e}function ja(e,{globals:t}={}){const i=qa.parse(e);for(const n of i.cells)Oa(n,e,t),Ma(n,e);return i}new Or("`",!0,!0,(e=>Ra.call(e)));class qa extends Va{parseTopLevel(e){for(e.cells||(e.cells=[]);this.type!==tr.eof;){const t=this.parseCell(this.startNode());t.input=this.input,e.cells.push(t)}return this.next(),this.finishNode(e,"Program")}}function $a(e){return(new TextDecoder).decode(function(e){const t=atob(e);return Uint8Array.from(t,(e=>e.codePointAt(0)))}(e))}class Ua{pending(){}fulfilled(e,t){}rejected(e,t){}}function za(e,t){const i={},n={};return t.forEach((e=>{i[e]=new Promise(((t,i)=>{n[e]={resolve:t,reject:i}}))})),e.then((e=>{t.forEach((t=>{n[t].resolve(e[t])}))})).catch((e=>{t.forEach((t=>{n[t].reject(e)}))})),function(e,n){const s=e.module();return t.forEach((e=>{s.variable(n(e)).define(e,[],(()=>i[e]))})),s}}function Wa(e,t){return async(i,n)=>{const s=i.startsWith("/")||i.startsWith("."),r=i.match(/^https:\/\/(api\.|beta\.|)observablehq\.com\//i);if(!s||r)return async function(e){const t=(e=>{let t,i=e;return(t=/\.js(\?|$)/i.exec(i))&&(i=i.slice(0,t.index)),(t=/^[0-9a-f]{16}$/i.test(i))&&(i=`d/${i}`),(t=/^https:\/\/(api\.|beta\.|)observablehq\.com\//i.exec(i))&&(i=i.slice(t[0].length)),i})(e),i=`https://api.observablehq.com/${t}.js?v=3`;return(await import(i)).default}(i);let a,o,u;if(window._ojs.selfContained){i.endsWith(".ts")?i=i.replace(/\.ts$/,".js"):i.endsWith(".tsx")&&(i=i.replace(/\.tsx$/,".js"));const e=t.get(i);if(void 0===e)throw new Error(`missing local file ${i} in self-contained mode`);a=e,o=e;const n=e.match(/data:(.*);base64/)[1];switch(n){case"application/javascript":u="js";break;case"application/ojs-javascript":u="ojs";break;default:throw new Error(`unrecognized MIME type ${n}`)}}else{u=new URL(i,window.location).pathname.match(/\.(ojs|js|ts|tsx|qmd)$/)[1],i.startsWith("/")?(a=function(t){const{runtimeToRoot:i}=e;return i?`${i}/${t}`:t}(i),o=function(t){const{docToRoot:i}=e;return i?`${i}/${t}`:t}(i)):(a=function(t){const{runtimeToDoc:i}=e;return i?`${i}/${t}`:t}(i),o=i)}if("ts"===u||"tsx"===u)try{return za(import(window._ojs.selfContained?a:a.replace(/\.ts$/,".js").replace(/\.tsx$/,".js")),n)}catch(e){throw console.error(e),e}else{if("js"!==u){if("ojs"===u)return async function(e){const t=await fetch(e);return Ha(await t.text())}(o);if("qmd"===u){const e=`${o.slice(0,-4)}.html`,t=await fetch(e);return function(e){const t=(new DOMParser).parseFromString(e,"text/html"),i=[];for(const e of t.querySelectorAll('script[type="ojs-define"]'))i.push(e.text);const n=[];for(const e of t.querySelectorAll('script[type="ojs-module-contents"]'))for(const t of JSON.parse($a(e.text)).contents)n.push(t.source);return Ha(n.join("\n"),i)}(await t.text())}throw new Error(`internal error, unrecognized module type ${u}`)}try{return za(import(a),n)}catch(e){throw console.error(e),e}}}}function Ha(e,t=[]){return(i,n)=>{const s=i.module();window._ojs.ojsConnector.interpreter.module(e,s,(e=>new Ua));for(const e of t)for(const{name:t,value:i}of JSON.parse(e).contents)window._ojs.ojsConnector.define(t,s)(i);return s}}class Ga{constructor({paths:e,inspectorClass:t,library:i,allowPendingGlobals:n=!1}){this.library=i||new Yt,this.localResolverMap=new Map,this.pendingGlobals={},this.allowPendingGlobals=n,this.runtime=new Bn(this.library,(e=>this.global(e))),this.mainModule=this.runtime.module(),this.interpreter=new Vs.exports.Interpreter({module:this.mainModule,resolveImportPath:Wa(e,this.localResolverMap)}),this.inspectorClass=t||un,this.mainModuleHasImports=!1,this.mainModuleOutstandingImportCount=0,this.chunkPromises=[]}global(e){if(void 0!==window[e])return window[e];if(this.allowPendingGlobals){if(!this.pendingGlobals.hasOwnProperty(e)){const t={};t.promise=new Promise(((e,i)=>{t.resolve=e,t.reject=i})),this.pendingGlobals[e]=t}return this.pendingGlobals[e].promise}}killPendingGlobals(){this.allowPendingGlobals=!1;for(const[e,{reject:t}]of Object.entries(this.pendingGlobals))t(new cn(`${e} is not defined`))}setLocalResolver(e){for(const[t,i]of Object.entries(e))this.localResolverMap.set(t,i)}define(e,t){let i;t||(t=this.mainModule);const n=this.library.Generators.observe((e=>{i=e}));return t.variable().define(e,n),i}watch(e,t,i){i||(i=this.mainModule),i.variable({fulfilled:i=>t(i,e)}).define([e],(e=>e))}async value(e,t){t||(t=this.mainModule);return await t.value(e)}finishInterpreting(){return Promise.all(this.chunkPromises)}interpretWithRunner(e,t){try{const i=ja(e),n=Promise.all(i.cells.map(t));return this.chunkPromises.push(n),n}catch(e){return Promise.reject(e)}}waitOnImports(e,t){return"ImportDeclaration"!==e.body.type?t:(this.mainModuleHasImports=!0,this.mainModuleOutstandingImportCount++,t.then((e=>(this.mainModuleOutstandingImportCount--,0===this.mainModuleOutstandingImportCount&&this.clearImportModuleWait(),e))))}interpretQuiet(e){return this.interpretWithRunner(e,(t=>{const i=e.slice(t.start,t.end),n=this.interpreter.module(i,void 0,(e=>new Ua));return this.waitOnImports(t,n)}))}}
/*!
 * escape-html
 * Copyright(c) 2012-2013 TJ Holowaychuk
 * Copyright(c) 2015 Andreas Lubbe
 * Copyright(c) 2015 Tiancheng "Timothy" Gu
 * Copyright(c) 2022 RStudio, PBC
 * 
 * MIT Licensed
 *
 * Minimal changes to make ES6
 * 
 */var Qa=/["'&<>]/;function Ka(e){var t,i=""+e,n=Qa.exec(i);if(!n)return i;var s="",r=0,a=0;for(r=n.index;r<i.length;r++){switch(i.charCodeAt(r)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#39;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}a!==r&&(s+=i.substring(a,r)),a=r+1,s+=t}return a!==r?s+i.substring(a,r):s}function Ya(e,t,...i){const n=document.createElement(e);for(const[e,i]of Object.entries(t||{}))n.setAttribute(e,i);for(;i.length;){const e=i.shift();Array.isArray(e)?i.unshift(...e):e instanceof HTMLElement?n.appendChild(e):n.appendChild(document.createTextNode(Ka(e)))}return n}const Ja={a:"svg",animate:"svg",animateMotion:"svg",animateTransform:"svg",circle:"svg",clipPath:"svg",defs:"svg",desc:"svg",discard:"svg",ellipse:"svg",feBlend:"svg",feColorMatrix:"svg",feComponentTransfer:"svg",feComposite:"svg",feConvolveMatrix:"svg",feDiffuseLighting:"svg",feDisplacementMap:"svg",feDistantLight:"svg",feDropShadow:"svg",feFlood:"svg",feFuncA:"svg",feFuncB:"svg",feFuncG:"svg",feFuncR:"svg",feGaussianBlur:"svg",feImage:"svg",feMerge:"svg",feMergeNode:"svg",feMorphology:"svg",feOffset:"svg",fePointLight:"svg",feSpecularLighting:"svg",feSpotLight:"svg",feTile:"svg",feTurbulence:"svg",filter:"svg",foreignObject:"svg",g:"svg",image:"svg",line:"svg",linearGradient:"svg",marker:"svg",mask:"svg",metadata:"svg",mpath:"svg",path:"svg",pattern:"svg",polygon:"svg",polyline:"svg",radialGradient:"svg",rect:"svg",script:"svg",set:"svg",stop:"svg",style:"svg",svg:"svg",switch:"svg",symbol:"svg",text:"svg",textPath:"svg",title:"svg",tspan:"svg",use:"svg",view:"svg"},Xa={svg:{namespace:"http://www.w3.org/2000/svg",class:SVGElement}};function Za(e){const t=Ja[e];if(void 0===t)return Ya;const i=Xa[t];return function(e,t,...n){return function(e,t,i,...n){const s=document.createElementNS(e.namespace,t);for(const[e,t]of Object.entries(i||{}))s.setAttribute(e,t);for(;n.length;){const t=n.shift();Array.isArray(t)?n.unshift(...t):t instanceof HTMLElement||t instanceof e.class?s.appendChild(t):s.appendChild(document.createTextNode(Ka(t)))}return s}(i,e,t,...n)}}let eo;var to,io,no,so,ro,ao,oo,uo,co,po,lo,ho,fo,mo,go,vo,xo,yo,bo,Eo;io={code:{ES5Regex:{NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,NonAsciiIdentifierPart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/},ES6Regex:{NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},isDecimalDigit:function(e){return 48<=e&&e<=57},isHexDigit:function(e){return 48<=e&&e<=57||97<=e&&e<=102||65<=e&&e<=70},isOctalDigit:function(e){return e>=48&&e<=55},NON_ASCII_WHITESPACES:[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279],isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&io.code.NON_ASCII_WHITESPACES.indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},fromCodePoint:function(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(Math.floor((e-65536)/1024)+55296)+String.fromCharCode((e-65536)%1024+56320)},IDENTIFIER_START:new Array(128),IDENTIFIER_PART:new Array(128),isIdentifierStartES5:function(e){return e<128?io.code.IDENTIFIER_START[e]:io.code.ES5Regex.NonAsciiIdentifierStart.test(io.code.fromCodePoint(e))},isIdentifierPartES5:function(e){return e<128?io.code.IDENTIFIER_PART[e]:io.code.ES5Regex.NonAsciiIdentifierPart.test(io.code.fromCodePoint(e))},isIdentifierStartES6:function(e){return e<128?io.code.IDENTIFIER_START[e]:io.code.ES6Regex.NonAsciiIdentifierStart.test(io.code.fromCodePoint(e))},isIdentifierPartES6:function(e){return e<128?io.code.IDENTIFIER_PART[e]:io.code.ES6Regex.NonAsciiIdentifierPart.test(io.code.fromCodePoint(e))}}};for(var Co=0;Co<128;++Co)io.code.IDENTIFIER_START[Co]=Co>=97&&Co<=122||Co>=65&&Co<=90||36===Co||95===Co;for(Co=0;Co<128;++Co)io.code.IDENTIFIER_PART[Co]=Co>=97&&Co<=122||Co>=65&&Co<=90||Co>=48&&Co<=57||36===Co||95===Co;function Do(e){return zo.Statement.hasOwnProperty(e.type)}eo={Sequence:0,Yield:1,Assignment:1,Conditional:2,ArrowFunction:2,Coalesce:3,LogicalOR:4,LogicalAND:5,BitwiseOR:6,BitwiseXOR:7,BitwiseAND:8,Equality:9,Relational:10,BitwiseSHIFT:11,Additive:12,Multiplicative:13,Exponentiation:14,Await:15,Unary:15,Postfix:16,OptionalChaining:17,Call:18,New:19,TaggedTemplate:20,Member:21,Primary:22},to={"??":eo.Coalesce,"||":eo.LogicalOR,"&&":eo.LogicalAND,"|":eo.BitwiseOR,"^":eo.BitwiseXOR,"&":eo.BitwiseAND,"==":eo.Equality,"!=":eo.Equality,"===":eo.Equality,"!==":eo.Equality,is:eo.Equality,isnt:eo.Equality,"<":eo.Relational,">":eo.Relational,"<=":eo.Relational,">=":eo.Relational,in:eo.Relational,instanceof:eo.Relational,"<<":eo.BitwiseSHIFT,">>":eo.BitwiseSHIFT,">>>":eo.BitwiseSHIFT,"+":eo.Additive,"-":eo.Additive,"*":eo.Multiplicative,"%":eo.Multiplicative,"/":eo.Multiplicative,"**":eo.Exponentiation};var Ao=32,wo=33;function Fo(e,t){var i="";for(t|=0;t>0;t>>>=1,e+=e)1&t&&(i+=e);return i}function ko(e){var t=e.length;return t&&io.code.isLineTerminator(e.charCodeAt(t-1))}function So(e,t){var i;for(i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e}function _o(e,t){var i,n;function s(e){return"object"==typeof e&&e instanceof Object&&!(e instanceof RegExp)}for(i in t)t.hasOwnProperty(i)&&(s(n=t[i])?s(e[i])?_o(e[i],n):e[i]=_o({},n):e[i]=n);return e}function Bo(e,t){return 8232==(-2&e)?(t?"u":"\\u")+(8232===e?"2028":"2029"):10===e||13===e?(t?"":"\\")+(10===e?"n":"r"):String.fromCharCode(e)}function Io(e,t){var i;return 8===e?"\\b":12===e?"\\f":9===e?"\\t":(i=e.toString(16).toUpperCase(),ro||e>255?"\\u"+"0000".slice(i.length)+i:0!==e||io.code.isDecimalDigit(t)?11===e?"\\x0B":"\\x"+"00".slice(i.length)+i:"\\0")}function Po(e){if(92===e)return"\\\\";if(10===e)return"\\n";if(13===e)return"\\r";if(8232===e)return"\\u2028";if(8233===e)return"\\u2029";throw new Error("Incorrectly classified character")}function No(e){var t,i,n,s="";for(t=0,i=e.length;t<i;++t)n=e[t],s+=Array.isArray(n)?No(n):n;return s}function To(e){return Array.isArray(e)?No(e):e}function Lo(){return lo||" "}function Vo(e,t){var i,n,s,r;return 0===(i=To(e).toString()).length?[t]:0===(n=To(t).toString()).length?[e]:(s=i.charCodeAt(i.length-1),r=n.charCodeAt(0),(43===s||45===s)&&s===r||io.code.isIdentifierPartES5(s)&&io.code.isIdentifierPartES5(r)||47===s&&105===r?[e,Lo(),t]:io.code.isWhiteSpace(s)||io.code.isLineTerminator(s)||io.code.isWhiteSpace(r)||io.code.isLineTerminator(r)?[e,t]:[e,lo,t])}function Ro(e){return[no,e]}function Oo(e){var t;t=no,e(no+=so),no=t}function Mo(e,t){if("Line"===e.type){if(ko(e.value))return"//"+e.value;var i="//"+e.value;return Eo||(i+="\n"),i}return vo.format.indent.adjustMultilineComment&&/[\n\r]/.test(e.value)?function(e,t){var i,n,s,r,a,o,u,c;for(i=e.split(/\r\n|[\r\n]/),o=Number.MAX_VALUE,n=1,s=i.length;n<s;++n){for(r=i[n],a=0;a<r.length&&io.code.isWhiteSpace(r.charCodeAt(a));)++a;o>a&&(o=a)}for(void 0!==t?(u=no,"*"===i[1][o]&&(t+=" "),no=t):(1&o&&--o,u=no),n=1,s=i.length;n<s;++n)c=To(Ro(i[n].slice(o))),i[n]=yo?c.join(""):c;return no=u,i.join("\n")}("/*"+e.value+"*/",t):"/*"+e.value+"*/"}function jo(e,t){var i,n,s,r,a,o,u,c,p,l,h,d;if(e.leadingComments&&e.leadingComments.length>0){if(r=t,Eo){for(t=[],c=(s=e.leadingComments[0]).extendedRange,p=s.range,(d=((h=bo.substring(c[0],p[0])).match(/\n/g)||[]).length)>0?(t.push(Fo("\n",d)),t.push(Ro(Mo(s)))):(t.push(h),t.push(Mo(s))),l=p,i=1,n=e.leadingComments.length;i<n;i++)p=(s=e.leadingComments[i]).range,d=(bo.substring(l[1],p[0]).match(/\n/g)||[]).length,t.push(Fo("\n",d)),t.push(Ro(Mo(s))),l=p;d=(bo.substring(p[1],c[1]).match(/\n/g)||[]).length,t.push(Fo("\n",d))}else for(s=e.leadingComments[0],t=[],mo&&"Program"===e.type&&0===e.body.length&&t.push("\n"),t.push(Mo(s)),ko(To(t).toString())||t.push("\n"),i=1,n=e.leadingComments.length;i<n;++i)ko(To(u=[Mo(s=e.leadingComments[i])]).toString())||u.push("\n"),t.push(Ro(u));t.push(Ro(r))}if(e.trailingComments)if(Eo)c=(s=e.trailingComments[0]).extendedRange,p=s.range,(d=((h=bo.substring(c[0],p[0])).match(/\n/g)||[]).length)>0?(t.push(Fo("\n",d)),t.push(Ro(Mo(s)))):(t.push(h),t.push(Mo(s)));else for(a=!ko(To(t).toString()),o=Fo(" ",function(e){var t;for(t=e.length-1;t>=0&&!io.code.isLineTerminator(e.charCodeAt(t));--t);return e.length-1-t}(To([no,t,so]).toString())),i=0,n=e.trailingComments.length;i<n;++i)s=e.trailingComments[i],a?(t=0===i?[t,so]:[t,o]).push(Mo(s,o)):t=[t,Ro(Mo(s))],i===n-1||ko(To(t).toString())||(t=[t,"\n"]);return t}function qo(e,t,i){var n,s=0;for(n=e;n<t;n++)"\n"===bo[n]&&s++;for(n=1;n<s;n++)i.push(po)}function $o(e,t,i){return t<i?["(",e,")"]:e}function Uo(e){var t,i,n;for(t=1,i=(n=e.split(/\r\n|\n/)).length;t<i;t++)n[t]=po+no+n[t];return n}function zo(){}function Wo(e){return To(e.name)}function Ho(e,t){return e.async?"async"+(t?Lo():lo):""}function Go(e){return e.generator&&!vo.moz.starlessGenerator?"*"+lo:""}function Qo(e){var t=e.value,i="";return t.async&&(i+=Ho(t,!e.computed)),t.generator&&(i+=Go(t)?"*":""),i}function Ko(e){var t;if(t=new zo,Do(e))return t.generateStatement(e,1);if(function(e){return zo.Expression.hasOwnProperty(e.type)}(e))return t.generateExpression(e,eo.Sequence,7);throw new Error("Unknown node type: "+e.type)}zo.prototype.maybeBlock=function(e,t){var i,n,s=this;return n=!vo.comment||!e.leadingComments,"BlockStatement"===e.type&&n?[lo,this.generateStatement(e,t)]:"EmptyStatement"===e.type&&n?";":(Oo((function(){i=[po,Ro(s.generateStatement(e,t))]})),i)},zo.prototype.maybeBlockSuffix=function(e,t){var i=ko(To(t).toString());return"BlockStatement"!==e.type||vo.comment&&e.leadingComments||i?i?[t,no]:[t,po,no]:[t,lo]},zo.prototype.generatePattern=function(e,t,i){return"Identifier"===e.type?Wo(e):this.generateExpression(e,t,i)},zo.prototype.generateFunctionParams=function(e){var t,i,n,s;if(s=!1,"ArrowFunctionExpression"!==e.type||e.rest||e.defaults&&0!==e.defaults.length||1!==e.params.length||"Identifier"!==e.params[0].type){for((n="ArrowFunctionExpression"===e.type?[Ho(e,!1)]:[]).push("("),e.defaults&&(s=!0),t=0,i=e.params.length;t<i;++t)s&&e.defaults[t]?n.push(this.generateAssignment(e.params[t],e.defaults[t],"=",eo.Assignment,7)):n.push(this.generatePattern(e.params[t],eo.Assignment,7)),t+1<i&&n.push(","+lo);e.rest&&(e.params.length&&n.push(","+lo),n.push("..."),n.push(Wo(e.rest))),n.push(")")}else n=[Ho(e,!0),Wo(e.params[0])];return n},zo.prototype.generateFunctionBody=function(e){var t,i;return t=this.generateFunctionParams(e),"ArrowFunctionExpression"===e.type&&(t.push(lo),t.push("=>")),e.expression?(t.push(lo),"{"===(i=this.generateExpression(e.body,eo.Assignment,7)).toString().charAt(0)&&(i=["(",i,")"]),t.push(i)):t.push(this.maybeBlock(e.body,9)),t},zo.prototype.generateIterationForStatement=function(e,t,i){var n=["for"+(t.await?Lo()+"await":"")+lo+"("],s=this;return Oo((function(){"VariableDeclaration"===t.left.type?Oo((function(){n.push(t.left.kind+Lo()),n.push(s.generateStatement(t.left.declarations[0],0))})):n.push(s.generateExpression(t.left,eo.Call,7)),n=Vo(n,e),n=[Vo(n,s.generateExpression(t.right,eo.Assignment,7)),")"]})),n.push(this.maybeBlock(t.body,i)),n},zo.prototype.generatePropertyKey=function(e,t){var i=[];return t&&i.push("["),i.push(this.generateExpression(e,eo.Assignment,7)),t&&i.push("]"),i},zo.prototype.generateAssignment=function(e,t,i,n,s){return eo.Assignment<n&&(s|=1),$o([this.generateExpression(e,eo.Call,s),lo+i+lo,this.generateExpression(t,eo.Assignment,s)],eo.Assignment,n)},zo.prototype.semicolon=function(e){return!fo&&e&Ao?"":";"},zo.Statement={BlockStatement:function(e,t){var i,n,s=["{",po],r=this;return Oo((function(){var a,o,u,c;for(0===e.body.length&&Eo&&(i=e.range)[1]-i[0]>2&&("\n"===(n=bo.substring(i[0]+1,i[1]-1))[0]&&(s=["{"]),s.push(n)),c=1,8&t&&(c|=16),a=0,o=e.body.length;a<o;++a)Eo&&(0===a&&(e.body[0].leadingComments&&(i=e.body[0].leadingComments[0].extendedRange,"\n"===(n=bo.substring(i[0],i[1]))[0]&&(s=["{"])),e.body[0].leadingComments||qo(e.range[0],e.body[0].range[0],s)),a>0&&(e.body[a-1].trailingComments||e.body[a].leadingComments||qo(e.body[a-1].range[1],e.body[a].range[0],s))),a===o-1&&(c|=Ao),u=e.body[a].leadingComments&&Eo?r.generateStatement(e.body[a],c):Ro(r.generateStatement(e.body[a],c)),s.push(u),ko(To(u).toString())||Eo&&a<o-1&&e.body[a+1].leadingComments||s.push(po),Eo&&a===o-1&&(e.body[a].trailingComments||qo(e.body[a].range[1],e.range[1],s))})),s.push(Ro("}")),s},BreakStatement:function(e,t){return e.label?"break "+e.label.name+this.semicolon(t):"break"+this.semicolon(t)},ContinueStatement:function(e,t){return e.label?"continue "+e.label.name+this.semicolon(t):"continue"+this.semicolon(t)},ClassBody:function(e,t){var i=["{",po],n=this;return Oo((function(t){var s,r;for(s=0,r=e.body.length;s<r;++s)i.push(t),i.push(n.generateExpression(e.body[s],eo.Sequence,7)),s+1<r&&i.push(po)})),ko(To(i).toString())||i.push(po),i.push(no),i.push("}"),i},ClassDeclaration:function(e,t){var i,n;return i=["class"],e.id&&(i=Vo(i,this.generateExpression(e.id,eo.Sequence,7))),e.superClass&&(n=Vo("extends",this.generateExpression(e.superClass,eo.Unary,7)),i=Vo(i,n)),i.push(lo),i.push(this.generateStatement(e.body,wo)),i},DirectiveStatement:function(e,t){return vo.raw&&e.raw?e.raw+this.semicolon(t):function(e){var t,i,n,s;for(s="double"===uo?'"':"'",t=0,i=e.length;t<i;++t){if(39===(n=e.charCodeAt(t))){s='"';break}if(34===n){s="'";break}92===n&&++t}return s+e+s}(e.directive)+this.semicolon(t)},DoWhileStatement:function(e,t){var i=Vo("do",this.maybeBlock(e.body,1));return Vo(i=this.maybeBlockSuffix(e.body,i),["while"+lo+"(",this.generateExpression(e.test,eo.Sequence,7),")"+this.semicolon(t)])},CatchClause:function(e,t){var i,n=this;return Oo((function(){var t;e.param?(i=["catch"+lo+"(",n.generateExpression(e.param,eo.Sequence,7),")"],e.guard&&(t=n.generateExpression(e.guard,eo.Sequence,7),i.splice(2,0," if ",t))):i=["catch"]})),i.push(this.maybeBlock(e.body,1)),i},DebuggerStatement:function(e,t){return"debugger"+this.semicolon(t)},EmptyStatement:function(e,t){return";"},ExportDefaultDeclaration:function(e,t){var i,n=["export"];return i=t&Ao?wo:1,n=Vo(n,"default"),n=Do(e.declaration)?Vo(n,this.generateStatement(e.declaration,i)):Vo(n,this.generateExpression(e.declaration,eo.Assignment,7)+this.semicolon(t))},ExportNamedDeclaration:function(e,t){var i,n=["export"],s=this;return i=t&Ao?wo:1,e.declaration?Vo(n,this.generateStatement(e.declaration,i)):(e.specifiers&&(0===e.specifiers.length?n=Vo(n,"{"+lo+"}"):"ExportBatchSpecifier"===e.specifiers[0].type?n=Vo(n,this.generateExpression(e.specifiers[0],eo.Sequence,7)):(n=Vo(n,"{"),Oo((function(t){var i,r;for(n.push(po),i=0,r=e.specifiers.length;i<r;++i)n.push(t),n.push(s.generateExpression(e.specifiers[i],eo.Sequence,7)),i+1<r&&n.push(","+po)})),ko(To(n).toString())||n.push(po),n.push(no+"}")),e.source?n=Vo(n,["from"+lo,this.generateExpression(e.source,eo.Sequence,7),this.semicolon(t)]):n.push(this.semicolon(t))),n)},ExportAllDeclaration:function(e,t){return["export"+lo,"*"+lo,"from"+lo,this.generateExpression(e.source,eo.Sequence,7),this.semicolon(t)]},ExpressionStatement:function(e,t){var i,n;return 123===(n=To(i=[this.generateExpression(e.expression,eo.Sequence,7)]).toString()).charCodeAt(0)||function(e){var t;return"class"===e.slice(0,5)&&(123===(t=e.charCodeAt(5))||io.code.isWhiteSpace(t)||io.code.isLineTerminator(t))}(n)||function(e){var t;return"function"===e.slice(0,8)&&(40===(t=e.charCodeAt(8))||io.code.isWhiteSpace(t)||42===t||io.code.isLineTerminator(t))}(n)||function(e){var t,i,n;if("async"!==e.slice(0,5))return!1;if(!io.code.isWhiteSpace(e.charCodeAt(5)))return!1;for(i=6,n=e.length;i<n&&io.code.isWhiteSpace(e.charCodeAt(i));++i);return i!==n&&("function"===e.slice(i,i+8)&&(40===(t=e.charCodeAt(i+8))||io.code.isWhiteSpace(t)||42===t||io.code.isLineTerminator(t)))}(n)||go&&16&t&&"Literal"===e.expression.type&&"string"==typeof e.expression.value?i=["(",i,")"+this.semicolon(t)]:i.push(this.semicolon(t)),i},ImportDeclaration:function(e,t){var i,n,s=this;return 0===e.specifiers.length?["import",lo,this.generateExpression(e.source,eo.Sequence,7),this.semicolon(t)]:(i=["import"],n=0,"ImportDefaultSpecifier"===e.specifiers[n].type&&(i=Vo(i,[this.generateExpression(e.specifiers[n],eo.Sequence,7)]),++n),e.specifiers[n]&&(0!==n&&i.push(","),"ImportNamespaceSpecifier"===e.specifiers[n].type?i=Vo(i,[lo,this.generateExpression(e.specifiers[n],eo.Sequence,7)]):(i.push(lo+"{"),e.specifiers.length-n==1?(i.push(lo),i.push(this.generateExpression(e.specifiers[n],eo.Sequence,7)),i.push(lo+"}"+lo)):(Oo((function(t){var r,a;for(i.push(po),r=n,a=e.specifiers.length;r<a;++r)i.push(t),i.push(s.generateExpression(e.specifiers[r],eo.Sequence,7)),r+1<a&&i.push(","+po)})),ko(To(i).toString())||i.push(po),i.push(no+"}"+lo)))),i=Vo(i,["from"+lo,this.generateExpression(e.source,eo.Sequence,7),this.semicolon(t)]))},VariableDeclarator:function(e,t){var i=1&t?7:6;return e.init?[this.generateExpression(e.id,eo.Assignment,i),lo,"=",lo,this.generateExpression(e.init,eo.Assignment,i)]:this.generatePattern(e.id,eo.Assignment,i)},VariableDeclaration:function(e,t){var i,n,s,r,a,o=this;function u(){for(r=e.declarations[0],vo.comment&&r.leadingComments?(i.push("\n"),i.push(Ro(o.generateStatement(r,a)))):(i.push(Lo()),i.push(o.generateStatement(r,a))),n=1,s=e.declarations.length;n<s;++n)r=e.declarations[n],vo.comment&&r.leadingComments?(i.push(","+po),i.push(Ro(o.generateStatement(r,a)))):(i.push(","+lo),i.push(o.generateStatement(r,a)))}return i=[e.kind],a=1&t?1:0,e.declarations.length>1?Oo(u):u(),i.push(this.semicolon(t)),i},ThrowStatement:function(e,t){return[Vo("throw",this.generateExpression(e.argument,eo.Sequence,7)),this.semicolon(t)]},TryStatement:function(e,t){var i,n,s,r;if(i=["try",this.maybeBlock(e.block,1)],i=this.maybeBlockSuffix(e.block,i),e.handlers)for(n=0,s=e.handlers.length;n<s;++n)i=Vo(i,this.generateStatement(e.handlers[n],1)),(e.finalizer||n+1!==s)&&(i=this.maybeBlockSuffix(e.handlers[n].body,i));else{for(n=0,s=(r=e.guardedHandlers||[]).length;n<s;++n)i=Vo(i,this.generateStatement(r[n],1)),(e.finalizer||n+1!==s)&&(i=this.maybeBlockSuffix(r[n].body,i));if(e.handler)if(Array.isArray(e.handler))for(n=0,s=e.handler.length;n<s;++n)i=Vo(i,this.generateStatement(e.handler[n],1)),(e.finalizer||n+1!==s)&&(i=this.maybeBlockSuffix(e.handler[n].body,i));else i=Vo(i,this.generateStatement(e.handler,1)),e.finalizer&&(i=this.maybeBlockSuffix(e.handler.body,i))}return e.finalizer&&(i=Vo(i,["finally",this.maybeBlock(e.finalizer,1)])),i},SwitchStatement:function(e,t){var i,n,s,r,a,o=this;if(Oo((function(){i=["switch"+lo+"(",o.generateExpression(e.discriminant,eo.Sequence,7),")"+lo+"{"+po]})),e.cases)for(a=1,s=0,r=e.cases.length;s<r;++s)s===r-1&&(a|=Ao),n=Ro(this.generateStatement(e.cases[s],a)),i.push(n),ko(To(n).toString())||i.push(po);return i.push(Ro("}")),i},SwitchCase:function(e,t){var i,n,s,r,a,o=this;return Oo((function(){for(i=e.test?[Vo("case",o.generateExpression(e.test,eo.Sequence,7)),":"]:["default:"],s=0,(r=e.consequent.length)&&"BlockStatement"===e.consequent[0].type&&(n=o.maybeBlock(e.consequent[0],1),i.push(n),s=1),s===r||ko(To(i).toString())||i.push(po),a=1;s<r;++s)s===r-1&&t&Ao&&(a|=Ao),n=Ro(o.generateStatement(e.consequent[s],a)),i.push(n),s+1===r||ko(To(n).toString())||i.push(po)})),i},IfStatement:function(e,t){var i,n,s=this;return Oo((function(){i=["if"+lo+"(",s.generateExpression(e.test,eo.Sequence,7),")"]})),n=1,t&Ao&&(n|=Ao),e.alternate?(i.push(this.maybeBlock(e.consequent,1)),i=this.maybeBlockSuffix(e.consequent,i),i="IfStatement"===e.alternate.type?Vo(i,["else ",this.generateStatement(e.alternate,n)]):Vo(i,Vo("else",this.maybeBlock(e.alternate,n)))):i.push(this.maybeBlock(e.consequent,n)),i},ForStatement:function(e,t){var i,n=this;return Oo((function(){i=["for"+lo+"("],e.init?"VariableDeclaration"===e.init.type?i.push(n.generateStatement(e.init,0)):(i.push(n.generateExpression(e.init,eo.Sequence,6)),i.push(";")):i.push(";"),e.test?(i.push(lo),i.push(n.generateExpression(e.test,eo.Sequence,7)),i.push(";")):i.push(";"),e.update?(i.push(lo),i.push(n.generateExpression(e.update,eo.Sequence,7)),i.push(")")):i.push(")")})),i.push(this.maybeBlock(e.body,t&Ao?wo:1)),i},ForInStatement:function(e,t){return this.generateIterationForStatement("in",e,t&Ao?wo:1)},ForOfStatement:function(e,t){return this.generateIterationForStatement("of",e,t&Ao?wo:1)},LabeledStatement:function(e,t){return[e.label.name+":",this.maybeBlock(e.body,t&Ao?wo:1)]},Program:function(e,t){var i,n,s,r,a;for(r=e.body.length,i=[mo&&r>0?"\n":""],a=17,s=0;s<r;++s)mo||s!==r-1||(a|=Ao),Eo&&(0===s&&(e.body[0].leadingComments||qo(e.range[0],e.body[s].range[0],i)),s>0&&(e.body[s-1].trailingComments||e.body[s].leadingComments||qo(e.body[s-1].range[1],e.body[s].range[0],i))),n=Ro(this.generateStatement(e.body[s],a)),i.push(n),s+1<r&&!ko(To(n).toString())&&(Eo&&e.body[s+1].leadingComments||i.push(po)),Eo&&s===r-1&&(e.body[s].trailingComments||qo(e.body[s].range[1],e.range[1],i));return i},FunctionDeclaration:function(e,t){return[Ho(e,!0),"function",Go(e)||Lo(),e.id?Wo(e.id):"",this.generateFunctionBody(e)]},ReturnStatement:function(e,t){return e.argument?[Vo("return",this.generateExpression(e.argument,eo.Sequence,7)),this.semicolon(t)]:["return"+this.semicolon(t)]},WhileStatement:function(e,t){var i,n=this;return Oo((function(){i=["while"+lo+"(",n.generateExpression(e.test,eo.Sequence,7),")"]})),i.push(this.maybeBlock(e.body,t&Ao?wo:1)),i},WithStatement:function(e,t){var i,n=this;return Oo((function(){i=["with"+lo+"(",n.generateExpression(e.object,eo.Sequence,7),")"]})),i.push(this.maybeBlock(e.body,t&Ao?wo:1)),i}},So(zo.prototype,zo.Statement),zo.Expression={MutableExpression:function(e,t,i){return`mutable ${e.id.name}`},ViewExpression:function(e,t,i){return`viewof ${e.id.name}`},SequenceExpression:function(e,t,i){var n,s,r;for(eo.Sequence<t&&(i|=1),n=[],s=0,r=e.expressions.length;s<r;++s)n.push(this.generateExpression(e.expressions[s],eo.Assignment,i)),s+1<r&&n.push(","+lo);return $o(n,eo.Sequence,t)},AssignmentExpression:function(e,t,i){return this.generateAssignment(e.left,e.right,e.operator,t,i)},ArrowFunctionExpression:function(e,t,i){return $o(this.generateFunctionBody(e),eo.ArrowFunction,t)},ConditionalExpression:function(e,t,i){return eo.Conditional<t&&(i|=1),$o([this.generateExpression(e.test,eo.Coalesce,i),lo+"?"+lo,this.generateExpression(e.consequent,eo.Assignment,i),lo+":"+lo,this.generateExpression(e.alternate,eo.Assignment,i)],eo.Conditional,t)},LogicalExpression:function(e,t,i){return"??"===e.operator&&(i|=64),this.BinaryExpression(e,t,i)},BinaryExpression:function(e,t,i){var n,s,r,a,o,u;return a=to[e.operator],s="**"===e.operator?eo.Postfix:a,r="**"===e.operator?a:a+1,a<t&&(i|=1),n=47===(u=(o=this.generateExpression(e.left,s,i)).toString()).charCodeAt(u.length-1)&&io.code.isIdentifierPartES5(e.operator.charCodeAt(0))?[o,Lo(),e.operator]:Vo(o,e.operator),o=this.generateExpression(e.right,r,i),"/"===e.operator&&"/"===o.toString().charAt(0)||"<"===e.operator.slice(-1)&&"!--"===o.toString().slice(0,3)?(n.push(Lo()),n.push(o)):n=Vo(n,o),"in"!==e.operator||1&i?("||"===e.operator||"&&"===e.operator)&&64&i?["(",n,")"]:$o(n,a,t):["(",n,")"]},CallExpression:function(e,t,i){var n,s,r;for(n=[this.generateExpression(e.callee,eo.Call,3)],e.optional&&n.push("?."),n.push("("),s=0,r=e.arguments.length;s<r;++s)n.push(this.generateExpression(e.arguments[s],eo.Assignment,7)),s+1<r&&n.push(","+lo);return n.push(")"),2&i?$o(n,eo.Call,t):["(",n,")"]},ChainExpression:function(e,t,i){return eo.OptionalChaining<t&&(i|=2),$o(this.generateExpression(e.expression,eo.OptionalChaining,i),eo.OptionalChaining,t)},NewExpression:function(e,t,i){var n,s,r,a,o;if(s=e.arguments.length,o=4&i&&!ho&&0===s?5:1,n=Vo("new",this.generateExpression(e.callee,eo.New,o)),!(4&i)||ho||s>0){for(n.push("("),r=0,a=s;r<a;++r)n.push(this.generateExpression(e.arguments[r],eo.Assignment,7)),r+1<a&&n.push(","+lo);n.push(")")}return $o(n,eo.New,t)},MemberExpression:function(e,t,i){var n,s;return n=[this.generateExpression(e.object,eo.Call,2&i?3:1)],e.computed?(e.optional&&n.push("?."),n.push("["),n.push(this.generateExpression(e.property,eo.Sequence,2&i?7:5)),n.push("]")):(e.optional||"Literal"!==e.object.type||"number"!=typeof e.object.value||(s=To(n).toString()).indexOf(".")<0&&!/[eExX]/.test(s)&&io.code.isDecimalDigit(s.charCodeAt(s.length-1))&&!(s.length>=2&&48===s.charCodeAt(0))&&n.push(" "),n.push(e.optional?"?.":"."),n.push(Wo(e.property))),$o(n,eo.Member,t)},MetaProperty:function(e,t,i){var n;return(n=[]).push("string"==typeof e.meta?e.meta:Wo(e.meta)),n.push("."),n.push("string"==typeof e.property?e.property:Wo(e.property)),$o(n,eo.Member,t)},UnaryExpression:function(e,t,i){var n,s,r,a,o;return s=this.generateExpression(e.argument,eo.Unary,7),""===lo?n=Vo(e.operator,s):(n=[e.operator],e.operator.length>2?n=Vo(n,s):(o=(a=To(n).toString()).charCodeAt(a.length-1),r=s.toString().charCodeAt(0),(43===o||45===o)&&o===r||io.code.isIdentifierPartES5(o)&&io.code.isIdentifierPartES5(r)?(n.push(Lo()),n.push(s)):n.push(s))),$o(n,eo.Unary,t)},YieldExpression:function(e,t,i){var n;return n=e.delegate?"yield*":"yield",e.argument&&(n=Vo(n,this.generateExpression(e.argument,eo.Yield,7))),$o(n,eo.Yield,t)},AwaitExpression:function(e,t,i){return $o(Vo(e.all?"await*":"await",this.generateExpression(e.argument,eo.Await,7)),eo.Await,t)},UpdateExpression:function(e,t,i){return e.prefix?$o([e.operator,this.generateExpression(e.argument,eo.Unary,7)],eo.Unary,t):$o([this.generateExpression(e.argument,eo.Postfix,7),e.operator],eo.Postfix,t)},FunctionExpression:function(e,t,i){var n=[Ho(e,!0),"function"];return e.id?(n.push(Go(e)||Lo()),n.push(Wo(e.id))):n.push(Go(e)||lo),n.push(this.generateFunctionBody(e)),n},ArrayPattern:function(e,t,i){return this.ArrayExpression(e,t,i,!0)},ArrayExpression:function(e,t,i,n){var s,r,a=this;return e.elements.length?(r=!n&&e.elements.length>1,s=["[",r?po:""],Oo((function(t){var i,n;for(i=0,n=e.elements.length;i<n;++i)e.elements[i]?(s.push(r?t:""),s.push(a.generateExpression(e.elements[i],eo.Assignment,7))):(r&&s.push(t),i+1===n&&s.push(",")),i+1<n&&s.push(","+(r?po:lo))})),r&&!ko(To(s).toString())&&s.push(po),s.push(r?no:""),s.push("]"),s):"[]"},RestElement:function(e,t,i){return"..."+this.generatePattern(e.argument)},ClassExpression:function(e,t,i){var n,s;return n=["class"],e.id&&(n=Vo(n,this.generateExpression(e.id,eo.Sequence,7))),e.superClass&&(s=Vo("extends",this.generateExpression(e.superClass,eo.Unary,7)),n=Vo(n,s)),n.push(lo),n.push(this.generateStatement(e.body,wo)),n},MethodDefinition:function(e,t,i){var n,s;return n=e.static?["static"+lo]:[],s="get"===e.kind||"set"===e.kind?[Vo(e.kind,this.generatePropertyKey(e.key,e.computed)),this.generateFunctionBody(e.value)]:[Qo(e),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)],Vo(n,s)},Property:function(e,t,i){return"get"===e.kind||"set"===e.kind?[e.kind,Lo(),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)]:e.shorthand?"AssignmentPattern"===e.value.type?this.AssignmentPattern(e.value,eo.Sequence,7):this.generatePropertyKey(e.key,e.computed):e.method?[Qo(e),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)]:[this.generatePropertyKey(e.key,e.computed),":"+lo,this.generateExpression(e.value,eo.Assignment,7)]},ObjectExpression:function(e,t,i){var n,s,r,a,o=this;return e.properties.length?(n=e.properties.length>1,Oo((function(){r=o.generateExpression(e.properties[0],eo.Sequence,7)})),n||(a=To(r).toString(),/[\r\n]/g.test(a))?(Oo((function(t){var i,a;if(s=["{",po,t,r],n)for(s.push(","+po),i=1,a=e.properties.length;i<a;++i)s.push(t),s.push(o.generateExpression(e.properties[i],eo.Sequence,7)),i+1<a&&s.push(","+po)})),ko(To(s).toString())||s.push(po),s.push(no),s.push("}"),s):["{",lo,r,lo,"}"]):"{}"},AssignmentPattern:function(e,t,i){return this.generateAssignment(e.left,e.right,"=",t,i)},ObjectPattern:function(e,t,i){var n,s,r,a,o,u=this;if(!e.properties.length)return"{}";if(a=!1,1===e.properties.length)"Property"===(o=e.properties[0]).type&&"Identifier"!==o.value.type&&(a=!0);else for(s=0,r=e.properties.length;s<r;++s)if("Property"===(o=e.properties[s]).type&&!o.shorthand){a=!0;break}return n=["{",a?po:""],Oo((function(t){var i,s;for(i=0,s=e.properties.length;i<s;++i)n.push(a?t:""),n.push(u.generateExpression(e.properties[i],eo.Sequence,7)),i+1<s&&n.push(","+(a?po:lo))})),a&&!ko(To(n).toString())&&n.push(po),n.push(a?no:""),n.push("}"),n},ThisExpression:function(e,t,i){return"this"},Super:function(e,t,i){return"super"},Identifier:function(e,t,i){return Wo(e)},ImportDefaultSpecifier:function(e,t,i){return Wo(e.id||e.local)},ImportNamespaceSpecifier:function(e,t,i){var n=["*"],s=e.id||e.local;return s&&n.push(lo+"as"+Lo()+Wo(s)),n},ImportSpecifier:function(e,t,i){var n=e.imported,s=[n.name],r=e.local;return r&&r.name!==n.name&&s.push(Lo()+"as"+Lo()+Wo(r)),s},ExportSpecifier:function(e,t,i){var n=e.local,s=[n.name],r=e.exported;return r&&r.name!==n.name&&s.push(Lo()+"as"+Lo()+Wo(r)),s},Literal:function(e,t,i){var n;if(e.hasOwnProperty("raw")&&xo&&vo.raw)try{if("Literal"===(n=xo(e.raw).body[0].expression).type&&n.value===e.value)return e.raw}catch(e){}return e.regex?"/"+e.regex.pattern+"/"+e.regex.flags:"bigint"==typeof e.value?e.value.toString()+"n":e.bigint?e.bigint+"n":null===e.value?"null":"string"==typeof e.value?function(e){var t,i,n,s,r,a="",o=0,u=0;for(t=0,i=e.length;t<i;++t){if(39===(n=e.charCodeAt(t)))++o;else if(34===n)++u;else if(47===n&&ro)a+="\\";else{if(io.code.isLineTerminator(n)||92===n){a+=Po(n);continue}if(!io.code.isIdentifierPartES5(n)&&(ro&&n<32||!ro&&!co&&(n<32||n>126))){a+=Io(n,e.charCodeAt(t+1));continue}}a+=String.fromCharCode(n)}if(r=(s=!("double"===uo||"auto"===uo&&u<o))?"'":'"',!(s?o:u))return r+a+r;for(e=a,a=r,t=0,i=e.length;t<i;++t)(39===(n=e.charCodeAt(t))&&s||34===n&&!s)&&(a+="\\"),a+=String.fromCharCode(n);return a+r}(e.value):"number"==typeof e.value?function(e){var t,i,n,s,r;if(e!=e)throw new Error("Numeric literal whose value is NaN");if(e<0||0===e&&1/e<0)throw new Error("Numeric literal whose value is negative");if(e===1/0)return ro?"null":ao?"1e400":"1e+400";if(t=""+e,!ao||t.length<3)return t;for(i=t.indexOf("."),ro||48!==t.charCodeAt(0)||1!==i||(i=0,t=t.slice(1)),n=t,t=t.replace("e+","e"),s=0,(r=n.indexOf("e"))>0&&(s=+n.slice(r+1),n=n.slice(0,r)),i>=0&&(s-=n.length-i-1,n=+(n.slice(0,i)+n.slice(i+1))+""),r=0;48===n.charCodeAt(n.length+r-1);)--r;return 0!==r&&(s-=r,n=n.slice(0,r)),0!==s&&(n+="e"+s),(n.length<t.length||oo&&e>1e12&&Math.floor(e)===e&&(n="0x"+e.toString(16)).length<t.length)&&+n===e&&(t=n),t}(e.value):"boolean"==typeof e.value?e.value?"true":"false":function(e){var t,i,n,s,r,a,o,u;if(i=e.toString(),e.source){if(!(t=i.match(/\/([^/]*)$/)))return i;for(n=t[1],i="",o=!1,u=!1,s=0,r=e.source.length;s<r;++s)a=e.source.charCodeAt(s),u?(i+=Bo(a,u),u=!1):(o?93===a&&(o=!1):47===a?i+="\\":91===a&&(o=!0),i+=Bo(a,u),u=92===a);return"/"+i+"/"+n}return i}(e.value)},GeneratorExpression:function(e,t,i){return this.ComprehensionExpression(e,t,i)},ComprehensionExpression:function(e,t,i){var n,s,r,a,o=this;return n="GeneratorExpression"===e.type?["("]:["["],vo.moz.comprehensionExpressionStartsWithAssignment&&(a=this.generateExpression(e.body,eo.Assignment,7),n.push(a)),e.blocks&&Oo((function(){for(s=0,r=e.blocks.length;s<r;++s)a=o.generateExpression(e.blocks[s],eo.Sequence,7),s>0||vo.moz.comprehensionExpressionStartsWithAssignment?n=Vo(n,a):n.push(a)})),e.filter&&(n=Vo(n,"if"+lo),a=this.generateExpression(e.filter,eo.Sequence,7),n=Vo(n,["(",a,")"])),vo.moz.comprehensionExpressionStartsWithAssignment||(a=this.generateExpression(e.body,eo.Assignment,7),n=Vo(n,a)),n.push("GeneratorExpression"===e.type?")":"]"),n},ComprehensionBlock:function(e,t,i){var n;return n=Vo(n="VariableDeclaration"===e.left.type?[e.left.kind,Lo(),this.generateStatement(e.left.declarations[0],0)]:this.generateExpression(e.left,eo.Call,7),e.of?"of":"in"),n=Vo(n,this.generateExpression(e.right,eo.Sequence,7)),["for"+lo+"(",n,")"]},SpreadElement:function(e,t,i){return["...",this.generateExpression(e.argument,eo.Assignment,7)]},TaggedTemplateExpression:function(e,t,i){var n=3;return 2&i||(n=1),$o([this.generateExpression(e.tag,eo.Call,n),this.generateExpression(e.quasi,eo.Primary,4)],eo.TaggedTemplate,t)},TemplateElement:function(e,t,i){return e.value.raw},TemplateLiteral:function(e,t,i){var n,s,r;for(n=["`"],s=0,r=e.quasis.length;s<r;++s)n.push(this.generateExpression(e.quasis[s],eo.Primary,7)),s+1<r&&(n.push("${"+lo),n.push(this.generateExpression(e.expressions[s],eo.Sequence,7)),n.push(lo+"}"));return n.push("`"),n},ModuleSpecifier:function(e,t,i){return this.Literal(e,t,i)},ImportExpression:function(e,t,i){return $o(["import(",this.generateExpression(e.source,eo.Assignment,7),")"],eo.Call,t)}},So(zo.prototype,zo.Expression),zo.prototype.generateExpression=function(e,t,i){var n,s;return s=e.type||"Property",vo.verbatim&&e.hasOwnProperty(vo.verbatim)?function(e,t){var i;return To("string"==typeof(i=e[vo.verbatim])?$o(Uo(i),eo.Sequence,t):$o(Uo(i.content),null!=i.precedence?i.precedence:eo.Sequence,t))}(e,t):(n=this[s](e,t,i),vo.comment&&(n=jo(e,n)),To(n))},zo.prototype.generateStatement=function(e,t){var i,n;return i=this[e.type](e,t),vo.comment&&(i=jo(e,i)),n=To(i).toString(),"Program"!==e.type||mo||""!==po||"\n"!==n.charAt(n.length-1)||(i=yo?To(i).replaceRight(/\s+$/,""):n.replace(/\s+$/,"")),To(i)},eo=_o({},eo);const Yo=ka({Import(){},ViewExpression(e,t,i){i(e.id,t,"Identifier")},MutableExpression(e,t,i){i(e.id,t,"Identifier")},Cell(e,t,i){i(e.body,t)},Program(e,t,i){if(e.body)for(let n=0,s=e.body;n<s.length;n+=1){i(s[n],t,"Statement")}else{if(!e.cells)throw new Error(`OJS traversal: I don't know how to walk this node: ${e}`);for(let n=0,s=e.cells;n<s.length;n+=1){i(s[n],t)}}}});const Jo={};function Xo(e,t){let i;try{i=ja(e)}catch(t){if(!(t instanceof SyntaxError))throw t;return e}!function(e,t){wa(e,t,Yo)}(i,{CallExpression(e){!function(e,t){const i=function(e){if("CallExpression"!==e.type)return null;const t=e.callee;if("MemberExpression"!==t.type)return null;if("Identifier"!==t.property.type||"plot"!==t.property.name)return null;let i=e.arguments;for(e=t.object;;){if("Identifier"===e.type&&"Plot"===e.name)return i;if("CallExpression"!==e.type||"MemberExpression"!==e.callee.type)return null;e=e.callee.object}}(e);if(null===i)return!1;if(i.length>1)return!1;if(0===i.length&&i.push({type:"ObjectExpression",properties:[]}),"ObjectExpression"!==i[0].type)return!1;const n=i[0].properties;if(!n.every((e=>"Property"===e.type)))return!1;const s=(()=>{if(null!==document.getElementById(t))return t;{let e,i=1;do{if(e=`${t}-${i}`,i+=1,null!==document.getElementById(e)&&void 0===Jo[e])return Jo[e]=!0,e}while(null!==document.getElementById(e))}})();if(void 0===s)return!1;const r=e=>({type:"MemberExpression",object:{type:"MemberExpression",computed:!0,object:{type:"Identifier",name:"cards"},property:{type:"Literal",value:s}},property:{type:"Identifier",name:e}}),a=e=>({type:"Property",key:{type:"Identifier",name:e},value:r(e)});n.unshift(a("width")),n.unshift(a("height"))}(e,t)}});const n=i.cells.map((e=>{const t=function(e,t){var i={indent:null,base:null,parse:null,comment:!1,format:{indent:{style:"    ",base:0,adjustMultilineComment:!1},newline:"\n",space:" ",json:!1,renumber:!1,hexadecimal:!1,quotes:"single",escapeless:!1,compact:!1,parentheses:!0,semicolons:!0,safeConcatenation:!1,preserveBlankLines:!1},moz:{comprehensionExpressionStartsWithAssignment:!1,starlessGenerator:!1},sourceMap:null,sourceMapRoot:null,sourceMapWithCode:!1,directive:!1,raw:!0,verbatim:null,sourceCode:null};return null!=t?("string"==typeof t.indent&&(i.format.indent.style=t.indent),"number"==typeof t.base&&(i.format.indent.base=t.base),t=_o(i,t),so=t.format.indent.style,no="string"==typeof t.base?t.base:Fo(so,t.format.indent.base)):(so=(t=i).format.indent.style,no=Fo(so,t.format.indent.base)),ro=t.format.json,ao=t.format.renumber,oo=!ro&&t.format.hexadecimal,uo=ro?"double":t.format.quotes,co=t.format.escapeless,po=t.format.newline,lo=t.format.space,t.format.compact&&(po=lo=so=no=""),ho=t.format.parentheses,fo=t.format.semicolons,mo=t.format.safeConcatenation,go=t.directive,xo=ro?null:t.parse,yo=!1,bo=t.sourceCode,Eo=t.format.preserveBlankLines&&null!==bo,vo=t,Ko(e).toString()}(e.body);if(null===e.id)return t;if("Identifier"===e.id.type)return`${e.id.name} = ${t}`;if("ViewExpression"===e.id.type)return`viewof ${e.id.id.name} = ${t}`;if("MutableExpression"===e.id.type)return`mutable ${e.id.id.name} = ${t}`;throw new Error(`OJS: I don't know how to handle this cell id: ${e.id.type}`)})).join("\n");return n}function Zo(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}Zo.prototype.define=function(e,t){for(let i in e){let n=e[i].map((function(e){return e.toLowerCase()}));i=i.toLowerCase();for(let e=0;e<n.length;e++){const s=n[e];if("*"!==s[0]){if(!t&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+i+'".');this._types[s]=i}}if(t||!this._extensions[i]){const e=n[0];this._extensions[i]="*"!==e[0]?e:e.substr(1)}}},Zo.prototype.getType=function(e){let t=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),i=t.replace(/^.*\./,"").toLowerCase(),n=t.length<e.length;return(i.length<t.length-1||!n)&&this._types[i]||null},Zo.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null};var eu=new Zo({"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]},{"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]});function tu(e){const t=[];for(const e of document.querySelectorAll('script[type="ojs-module-contents"]'))for(const i of JSON.parse($a(e.text)).contents){let e=document.getElementById(i.cellName)||document.getElementById(`${i.cellName}-1`);e&&t.push(e)}t.forEach((t=>{t.innerHTML="";const i=e();t.appendChild(nu({heading:"Error",type:"error",message:i}))}))}const iu=(e,t)=>function(){return!!window.quartoDevhost&&(window.quartoDevhost.openInputFile(e,t,!0),!1)};function nu(e){const{type:t,heading:i,message:n,onclick:s}=e,r=document.createElement("div");r.classList.add(`callout-${t}`,"callout","callout-style-default","callout-captioned");const a=document.createElement("div");a.classList.add("callout-header","d-flex","align-content-center");const o=document.createElement("div");o.classList.add("callout-icon-container");const u=document.createElement("i");u.classList.add("callout-icon"),o.appendChild(u),a.appendChild(o);const c=document.createElement("div");c.classList.add("callout-caption-container","flex-fill"),"string"==typeof i?c.innerText=i:c.appendChild(i),a.appendChild(c),r.appendChild(a);const p=document.createElement("div");if(p.classList.add("callout-body-container","callout-body"),"string"==typeof n){const e=document.createElement("p");e.innerText=n,p.appendChild(e)}else p.append(n);return r.appendChild(p),s&&(r.onclick=s,r.style.cursor="pointer"),r}void 0===Object.fromEntries&&(Object.fromEntries=function(e){const t={};for(const[i,n]of e)t[i]=n;return t});const su="ojs-in-a-box-waiting-for-module-import";class ru extends Ga{constructor(e){super(e)}clearImportModuleWait(){const e=Array.from(document.querySelectorAll(`.${su}`));for(const t of e)t.classList.remove(su)}finishInterpreting(){return super.finishInterpreting().then((()=>{this.mainModuleHasImports&&this.clearImportModuleWait()}))}locatePreDiv(e,t){let i;for(const n of e.querySelectorAll("pre.sourceCode")){if(!(n.compareDocumentPosition(t)&t.DOCUMENT_POSITION_FOLLOWING))break;i=n}return i}findCellOutputDisplay(e){for(;e&&!e.classList.contains("cell-output-display");)e=e.parentElement;return e||null}setPreDivClasses(e,t){if(t){e.classList.add("numberSource");const t=e.parentElement;t.classList.contains("hidden")&&(e._hidden=!0,t.classList.remove("hidden"),"DETAILS"===t.parentElement.tagName&&(t.parentElement.classList.remove("hidden"),t.parentElement.setAttribute("open","open")))}else if(e.classList.remove("numberSource"),!0===e._hidden){const t=e.parentElement;t.classList.add("hidden"),"DETAILS"===t.parentElement.tagName&&t.parentElement.classList.add("hidden")}}clearErrorPinpoints(e,t){const i=this.locatePreDiv(e,t);if(void 0===i)return;this.setPreDivClasses(i,!1);let n=0;i.parentElement.dataset.sourceOffset&&(n=-Number(i.parentElement.dataset.sourceOffset));for(const e of i._decorator.spanSelection(n,1/0)){const{node:t}=e;t.classList.remove("quarto-ojs-error-pinpoint"),t.onclick=null}}decorateOjsDivWithErrorPinpoint(e,t,i,n,s){const r=this.findCellOutputDisplay(e);r&&(void 0===r._errorSpans&&(r._errorSpans=[]),r._errorSpans.push({start:t,end:i,line:n,column:s}))}decorateSource(e,t){if(!e)return;this.clearErrorPinpoints(e,t);const i=this.locatePreDiv(e,t);if(void 0===i)return;let n=i.parentElement;"DETAILS"===n.parentElement.tagName&&(n=n.parentElement);let s=n.nextElementSibling,r=!1;for(;null!==s&&s.classList.contains("cell-output-display");){for(const e of s._errorSpans||[]){for(const t of i._decorator.spanSelection(e.start,e.end)){const{node:i}=t;i.classList.add("quarto-ojs-error-pinpoint"),i.onclick=iu(e.line,e.column)}r=!0}s=s.nextElementSibling}this.setPreDivClasses(i,r)}clearError(e){const t=this.findCellOutputDisplay(e);t&&(t._errorSpans=[])}signalError(e,t,i){(t=>{let n;const s=t.querySelector(".observablehq--inspect");let[r,a]=s.textContent.split(": ");if("RuntimeError"===r)if(r="OJS Runtime Error",a.match(/^(.+) is not defined$/)){const[s,...o]=a.split(" "),u=document.createElement("p"),c=document.createElement("tt");c.innerText=s,u.appendChild(c),u.appendChild(document.createTextNode(" "+o.join(" "))),a=u;const p=this.locatePreDiv(e,t);if(void 0!==p){p.classList.add("numberSource");const e=i.references.find((e=>e.name===s));if(void 0!==e){const{line:i,column:s}=p._decorator.offsetToLineColumn(e.start),a=document.createElement("span"),o=document.createTextNode(`${r} (line ${i}, column ${s}) `);if(a.appendChild(o),window.quartoDevhost){const e=document.createElement("a");e.href="#",e.innerText="(source)",n=iu(i,s),a.appendChild(e)}r=a,this.decorateOjsDivWithErrorPinpoint(t,e.start,e.end,i,s)}}}else if(a.match(/^(.+) could not be resolved$/)||a.match(/^(.+) is defined more than once$/)){const[e,...t]=a.split(" "),i=document.createElement("p"),n=document.createElement("tt");n.innerText=e,i.appendChild(n),i.appendChild(document.createTextNode(" "+t.join(" "))),a=i}else{const e=document.createElement("p");e.appendChild(document.createTextNode(a)),a=e}else{r="OJS Error";const e=document.createElement("p");e.appendChild(document.createTextNode(s.textContent)),a=e}const o=nu({type:"important",heading:r,message:a,onclick:n});t.appendChild(o)})(t)}interpret(e,t,i){const n=this,s=(e,t)=>s=>{const r="function"==typeof i?i():i;e.appendChild(r),t.id&&"ViewExpression"===t.id.type&&!s.startsWith("viewof ")&&r.classList.add("quarto-ojs-hide");let a,o=e;for(;null!==o&&!o.classList.contains("cell");)o=o.parentElement,o&&o.classList.contains("cell-output-display")&&(a=o);const u=!o||"all"===o.dataset.output;return new MutationObserver((function(e){for(const i of e){const e=i.target;if(!u){const e=Array.from(i.target.childNodes);for(const t of e)t.classList.contains("observablehq--inspect")&&!t.parentNode.classList.contains("observablehq--error")&&"expression"!==t.parentNode.parentNode.dataset.nodetype&&t.classList.add("quarto-ojs-hide"),t.classList.contains("observablehq--inspect")&&!t.parentNode.classList.contains("observablehq--error")&&"expression"===t.parentNode.parentNode.dataset.nodetype&&t.classList.remove("quarto-ojs-hide")}e.classList.contains("observablehq--error")?(e.querySelector(".observablehq--inspect").style.display="none",0===e.querySelectorAll(".callout-important").length&&n.signalError(o,e,t)):(n.clearError(e),"expression"!==e.parentNode.dataset.nodetype&&!u&&Array.from(e.childNodes).every((e=>e.classList.contains("observablehq--inspect")))&&e.classList.add("quarto-ojs-hide")),n.decorateSource(o,e);for(const t of i.addedNodes){if("svg"===t.tagName&&Array.from(t.classList).some((e=>e.match(/plot-[0-9a-f]+/)))&&(t.style.background="none"),"FORM"===t.tagName&&Array.from(t.classList).some((e=>e.endsWith("table")&&e.startsWith("oi-")))&&(t.classList.add("quarto-ojs-table-fixup"),window._ojs.isDashboard)){const e=t.querySelector("table");e&&(e.style.width="100%"),t.style.maxHeight=null,t.style.margin="0 0 0 0",t.style.padding="0 0 0 0";let i=t.parentElement;for(;i&&!i.classList.contains("cell-output-display")&&!i.classList.contains("cell");)i=i.parentElement;null!==i&&t.clientHeight>i.clientHeight&&(t.style.maxHeight=`${i.clientHeight}px`)}const i=t.querySelectorAll("button");for(const e of Array.from(i))e.classList.add("btn"),e.classList.add("btn-quarto");const n=t.querySelectorAll("code.javascript");1===n.length&&(n[0].textContent.trim().startsWith("import")&&e.classList.add("quarto-ojs-hide"))}}if(a){Array.from(a.querySelectorAll("div.observablehq")).every((e=>e.classList.contains("quarto-ojs-hide")))?a.classList.add("quarto-ojs-hide"):a.classList.remove("quarto-ojs-hide")}})).observe(r,{childList:!0}),r.classList.add(su),new this.inspectorClass(r,t)};return this.interpretWithRunner(e,(i=>{const n="function"==typeof t?t():t,r=e.slice(i.start,i.end),a=this.interpreter.module(r,void 0,s(n,i));return this.waitOnImports(i,a)}))}}function au(){const e=window._ojs,t=void 0!==window.Shiny;if(window.navigator.userAgent.includes("QtWebEngine"))return void tu((()=>{const e=document.createElement("div");e.appendChild(document.createTextNode("This document uses OJS, which requires JavaScript features not present in this version of QtWebEngine. If you're using RStudio IDE, please upgrade to a "));const t=document.createElement("a");return t.appendChild(document.createTextNode("2022.12 build")),t.href="https://dailies.rstudio.com/rstudio/elsbeth-geranium/",e.appendChild(t),e.appendChild(document.createTextNode(".")),e}));if("file:"===window.location.protocol)return void tu((()=>{const e=document.createElement("div");return e.appendChild(document.createTextNode("This document uses OJS, which requires JavaScript features disabled when running in file://... URLs. In order for these features to work, run this document in a web server.")),e}));if(t){e.hasShiny=!0,function(){const e=Symbol("value"),t=Symbol("callback"),i=Symbol("instance");class n extends Shiny.InputBinding{constructor(e){super(),this.x=e}find(e){const t=this.x.find(e);return $(t)}getId(e){return this.x.getId?this.x.getId(e):super.getId(e)}initialize(n){const s=this.x.init(n,(i=>{n[e]=i,n[t]()}));n[i]=s}getValue(t){return t[e]}setValue(t,n){t[e]=n,t[i].onSetValue(n)}subscribe(e,i){e[t]=i}unsubscribe(e){e[i].dispose()}}class s extends Shiny.OutputBinding{find(e){return $(e).find(".observablehq-inspector")}getId(e){return e.id}renderValue(e,t){new un(e).fulfilled(t)}}void 0===window.Shiny?console.warn("Shiny runtime not found; Shiny features won't work."):(Shiny.inputBindings.register(new n(new Ls)),Shiny.outputBindings.register(new s),Shiny.addCustomMessageHandler("ojs-export",(({name:e})=>{window._ojs.ojsConnector.mainModule.redefine(e,window._ojs.ojsConnector.library.shinyOutput()(e)),Shiny.bindAll(document.body)})))}();const t=document.createElement("span");window._ojs.shinyElementRoot=t,document.body.appendChild(t)}const i=new Yt;function n(e){const t=Object.keys(e);return e[t[0]].map(((i,n)=>Object.fromEntries(t.map((t=>{const i=e[t][n];return[t,null===i?void 0:i]}))))).filter((e=>!Object.values(e).every((e=>void 0===e))))}t&&function(e){class t extends Shiny.OutputBinding{constructor(e,t){super(),this._name=e,this._change=t}find(e){return $(e).find("#"+this._name)}getId(e){return e.id}renderValue(e,t){this._change(t)}onValueError(e,t){const i=`Shiny error in ${e.id}`;console.groupCollapsed(`%c${i}`,"color:red"),console.log(`${t.message}`),console.log(`call: ${t.call}`),console.groupEnd(i)}}$(document).on("shiny:connected",(function(e){Object.entries(Ps).map((([e,t])=>{window.Shiny.setInputValue(e,t)})),Ps={}})),e.shinyInput=function(){return e=>{Is.add(e),window._ojs.ojsConnector.mainModule.value(e).then((t=>{window.Shiny&&window.Shiny.setInputValue?window.Shiny.setInputValue(e,t):Ps[e]=t}))}},e.shinyOutput=function(){return function(i){const n=document.createElement("div");return n.id=i,n.classList.add("ojs-variable-writer"),window._ojs.shinyElementRoot.appendChild(n),e.Generators.observe((e=>{Shiny.outputBindings.register(new t(i,e))}))}}}(i),i.transpose=()=>n;const s=document.querySelector("main")||document.querySelector("div.reveal")||document.querySelector("body");i.width=function(){return null===s?i.Generators.observe((e=>{e(void 0)})):i.Generators.observe((function(e){var t=e(s.clientWidth);function i(){var i=s.clientWidth;i!==t&&e(t=i)}return window.addEventListener("resize",i),function(){window.removeEventListener("resize",i)}}))},i.cards=function(){return null===s?i.Generators.observe((e=>{e(void 0)})):i.Generators.observe((function(e){let t;function i(){let i=!1;const n={};let s=0;const r=e=>{const r={card:e,width:e.clientWidth,height:e.clientHeight};n[s]=r,void 0!==t&&t[s].width===r.width&&t[s].height===r.height||(i=!0),s++,e.id&&(n[e.id]=r)};for(const e of document.querySelectorAll("div.card")){for(const t of e.querySelectorAll("div.cell-output-display"))r(t);for(const t of e.querySelectorAll("div.quarto-layout-cell"))r(t)}for(const e of document.querySelectorAll("div")){if(!e.id.startsWith("ojs-cell-"))continue;let s;if(e.parentElement.classList.contains("cell-output-display"))s=e.parentElement;else if(e.parentElement.classList.contains("quarto-layout-cell"))s=e.parentElement;else{if(!e.parentElement.parentElement.classList.contains("cell-output-display"))continue;s=e.parentElement.parentElement}const r={card:s,width:e.clientWidth,height:e.clientHeight};n[e.id]=r,void 0!==t&&t[e.id].width===r.width&&t[e.id].height===r.height||(i=!0),""!==e.parentElement.id&&(n[e.parentElement.id]=r)}i&&(t=n,e(n))}return i(),window.addEventListener("resize",i),function(){window.removeEventListener("resize",i)}}))},Array.from(document.querySelectorAll("span.co")).filter((e=>"//| echo: fenced"===e.textContent)).forEach((e=>{const t=e.parentElement,i=t.nextSibling;if(i){const e=i.nextSibling;if(e){const t=Number(e.id.split("-")[1]);e.style="counter-reset: source-line "+(t-1)}}const n=t.parentElement.parentElement.parentElement,s=Number(n.dataset.sourceOffset);n.dataset.sourceOffset=s-"//| echo: fenced\n".length,t.remove(),i.remove()}));const r=[...Array.from(document.querySelectorAll("div.quarto-layout-panel div[id]")),...Array.from(document.querySelectorAll("div.ojs-track-layout[id]"))];i.layoutWidth=function(){return i.Generators.observe((function(e){const t=Object.fromEntries(r.map((e=>[e.id,e.clientWidth])));function i(){let i=!1;for(const e of r){const n=e.clientWidth;n!==t[e.id]&&(t[e.id]=n,i=!0)}i&&e(t)}return e(t),window.addEventListener("resize",i),function(){window.removeEventListener("resize",i)}}))};let a={};function o(t){if(a[t])return a[t];let i;const n=window.location.href.split("#")[0].replace(/[^/]*$/,"");i=t.startsWith("/")?""===e.paths.docToRoot?`${n}.${t}`:`${n}${e.paths.docToRoot}${t}`:t.startsWith("http")?t:`${n}${t}`;return{url:i,mimeType:eu.getType(i)}}i.FileAttachment=()=>At(o);const u=new ru({paths:e.paths,inspectorClass:t?Ns:Bs,library:i,allowPendingGlobals:t});e.ojsConnector=u,t&&$(document).one("shiny:idle",(()=>{$(document).one("shiny:message",(()=>{setTimeout((()=>{u.killPendingGlobals()}),0)}))}));const c=new Map;const p=document.querySelectorAll("pre.sourceCode code.sourceCode");Array.from(p).map((e=>{e=e.parentElement;const t=new Xt(e);return e._decorator=t,t})).forEach((e=>{if(void 0===e._node.parentElement.dataset.syntaxErrorPosition)return;const t=Number(e._node.parentElement.dataset.syntaxErrorPosition);e.decorateSpan(t,t+1,["quarto-ojs-error-pinpoint"])}));const l={setLocalResolver(e){a=e,u.setLocalResolver(e)},finishInterpreting:()=>u.finishInterpreting(),async value(e){await this.finishInterpreting();return await u.value(e)},interpretLenient:(e,t,i)=>l.interpret(e,t,i).catch((()=>{})),interpret(e,t,i){let n;return u.interpret(e,(()=>{let e;if(n=document.getElementById(t),!n&&(e=function(e){c.has(e)||c.set(e,0);let t=c.get(e);return t++,c.set(e,t),`${e}-${t}`}(t),n=document.getElementById(e),!n))throw new Error("Ran out of quarto subfigures.");return n}),(()=>document.createElement(i?"span":"div"))).catch((e=>{let t=n;for(;null!==t&&!t.classList.contains("cell");)t=t.parentElement;const i=n.querySelector(".observablehq");if(!i)throw e;for(const e of i.querySelectorAll(".callout"))e.remove();const s=document.createElement("pre");s.innerText=e.stack;const r=nu({type:"important",heading:`${e.name}: ${e.message}`,message:s});return i.appendChild(r),u.clearError(i),u.clearErrorPinpoints(t,i),e}))},interpretQuiet:e=>u.interpretQuiet(e),interpretFromScriptTags(){for(const e of document.querySelectorAll("script[type='ojs-module-contents']"))for(const t of JSON.parse($a(e.text)).contents){let e=window._ojs.isDashboard?Xo(t.source,t.cellName):t.source;switch(t.methodName){case"interpret":this.interpret(e,t.cellName,t.inline);break;case"interpretLenient":this.interpretLenient(e,t.cellName,t.inline);break;case"interpretQuiet":this.interpretQuiet(e);break;default:throw new Error(`Don't know how to call method ${t.methodName}`)}}for(const e of document.querySelectorAll("script[type='ojs-define']"))for(const{name:t,value:i}of JSON.parse(e.text).contents)u.define(t)(i)}};return l}window._ojs={ojsConnector:void 0,paths:{},hasShiny:!1,isDashboard:document.body.classList.contains("quarto-dashboard"),shinyElementRoot:void 0},window._ojs.runtime=au(),window._ojs.jsx={createElement:(e,t,...i)=>"function"==typeof e?e({...t,children:i}):Za(e)(e,t,...i)};
