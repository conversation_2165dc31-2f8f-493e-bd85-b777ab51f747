// Tippy customizations

.tippy-box[data-theme~="quarto"] {
  background-color: $body-bg;
  border: solid $border-width $border-color;
  border-radius: $border-radius;
  color: $body-color;
  font-size: $font-size-sm;
}
.tippy-box[data-theme~="quarto"] > .tippy-backdrop {
  background-color: $body-bg;
}
.tippy-box[data-theme~="quarto"] > .tippy-arrow:after,
.tippy-box[data-theme~="quarto"] > .tippy-svg-arrow:after {
  content: "";
  position: absolute;
  z-index: -1;
}
.tippy-box[data-theme~="quarto"] > .tippy-arrow:after {
  border-color: transparent;
  border-style: solid;
}

.tippy-box[data-placement^="top"] > .tippy-arrow:before {
  bottom: -6px;
}
.tippy-box[data-placement^="bottom"] > .tippy-arrow:before {
  top: -6px;
}
.tippy-box[data-placement^="right"] > .tippy-arrow:before {
  left: -6px;
}
.tippy-box[data-placement^="left"] > .tippy-arrow:before {
  right: -6px;
}

.tippy-box[data-theme~="quarto"][data-placement^="top"] > .tippy-arrow:before {
  border-top-color: $body-bg;
}
.tippy-box[data-theme~="quarto"][data-placement^="top"] > .tippy-arrow:after {
  border-top-color: $border-color;
  border-width: 7px 7px 0;
  top: 17px;
  left: 1px;
}
.tippy-box[data-theme~="quarto"][data-placement^="top"]
  > .tippy-svg-arrow
  > svg {
  top: 16px;
}
.tippy-box[data-theme~="quarto"][data-placement^="top"]
  > .tippy-svg-arrow:after {
  top: 17px;
}
.tippy-box[data-theme~="quarto"][data-placement^="bottom"]
  > .tippy-arrow:before {
  border-bottom-color: $body-bg;
  bottom: 16px;
}
.tippy-box[data-theme~="quarto"][data-placement^="bottom"]
  > .tippy-arrow:after {
  border-bottom-color: $border-color;
  border-width: 0 7px 7px;
  bottom: 17px;
  left: 1px;
}
.tippy-box[data-theme~="quarto"][data-placement^="bottom"]
  > .tippy-svg-arrow
  > svg {
  bottom: 15px;
}
.tippy-box[data-theme~="quarto"][data-placement^="bottom"]
  > .tippy-svg-arrow:after {
  bottom: 17px;
}
.tippy-box[data-theme~="quarto"][data-placement^="left"] > .tippy-arrow:before {
  border-left-color: $body-bg;
}
.tippy-box[data-theme~="quarto"][data-placement^="left"] > .tippy-arrow:after {
  border-left-color: $border-color;
  border-width: 7px 0 7px 7px;
  left: 17px;
  top: 1px;
}
.tippy-box[data-theme~="quarto"][data-placement^="left"]
  > .tippy-svg-arrow
  > svg {
  left: 11px;
}
.tippy-box[data-theme~="quarto"][data-placement^="left"]
  > .tippy-svg-arrow:after {
  left: 12px;
}
.tippy-box[data-theme~="quarto"][data-placement^="right"]
  > .tippy-arrow:before {
  border-right-color: $body-bg;
  right: 16px;
}
.tippy-box[data-theme~="quarto"][data-placement^="right"] > .tippy-arrow:after {
  border-width: 7px 7px 7px 0;
  right: 17px;
  top: 1px;
  border-right-color: $border-color;
}
.tippy-box[data-theme~="quarto"][data-placement^="right"]
  > .tippy-svg-arrow
  > svg {
  right: 11px;
}
.tippy-box[data-theme~="quarto"][data-placement^="right"]
  > .tippy-svg-arrow:after {
  right: 12px;
}
.tippy-box[data-theme~="quarto"] > .tippy-svg-arrow {
  fill: $body-color;
}
.tippy-box[data-theme~="quarto"] > .tippy-svg-arrow:after {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA2czEuNzk2LS4wMTMgNC42Ny0zLjYxNUM1Ljg1MS45IDYuOTMuMDA2IDggMGMxLjA3LS4wMDYgMi4xNDguODg3IDMuMzQzIDIuMzg1QzE0LjIzMyA2LjAwNSAxNiA2IDE2IDZIMHoiIGZpbGw9InJnYmEoMCwgOCwgMTYsIDAuMikiLz48L3N2Zz4=);
  background-size: 16px 6px;
  width: 16px;
  height: 6px;
}
