<script src="https://giscus.app/client.js"
        data-repo="<%- giscus.repo %>"
        data-repo-id="<%- giscus['repo-id'] %>"
        data-category="<%- giscus.category %>"
        data-category-id="<%- giscus['category-id'] %>"
        data-mapping="<%- giscus.mapping %>"
        data-reactions-enabled="<%- giscus['reactions-enabled'] ? 1 : 0 %>"
        data-emit-metadata="0"
        data-input-position="<%- giscus['input-position'] %>"
        data-theme="<%- giscus.theme %>"
        data-lang="<%- giscus.language %>"
        crossorigin="anonymous"
        <%- giscus.loading ? `data-loading=${giscus.loading}` : '' %>
        async>
</script>
<input type="hidden" id="giscus-base-theme" value="<%- giscus.baseTheme %>">
<input type="hidden" id="giscus-alt-theme" value="<%- giscus.altTheme %>">