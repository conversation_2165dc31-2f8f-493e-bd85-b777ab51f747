<style type="text/css">

  .reveal div.sourceCode {
    margin: 0;
    overflow: auto;
  }

  .reveal div.hanging-indent {
    margin-left: 1em;
    text-indent: -1em;
  }

  .reveal .slide:not(.center) {
    height: 100%;
    <% if (scrollable) { %>overflow-y: auto;<% } %>
  }

  .reveal .slide.scrollable {
    overflow-y: auto;
  }

  .reveal .footnotes {
    height: 100%;
    overflow-y: auto;
  }

  .reveal .slide .absolute {
    position: absolute;
    display: block;
  }

  .reveal .footnotes ol {
    counter-reset: ol;
    list-style-type: none; 
    margin-left: 0;
  }

  .reveal .footnotes ol li:before {
    counter-increment: ol;
    content: counter(ol) ". "; 
  }

  .reveal .footnotes ol li > p:first-child {
    display: inline-block;
  }


  .reveal .slide ul,
  .reveal .slide ol {
    margin-bottom: 0.5em;
  }

  .reveal .slide ul li,
  .reveal .slide ol li {
    margin-top: 0.4em;
    margin-bottom: 0.2em;
  }

  .reveal .slide ul[role="tablist"] li {
    margin-bottom: 0;
  }

  .reveal .slide ul li > *:first-child,
  .reveal .slide ol li > *:first-child {
    margin-block-start: 0;
  }
  .reveal .slide ul li > *:last-child,
  .reveal .slide ol li > *:last-child {
    margin-block-end: 0;
  }

  .reveal .slide .columns:nth-child(3) {
    margin-block-start: 0.8em;
  }

  .reveal blockquote {
    box-shadow: none;
  }

  .reveal .tippy-content>* {
    margin-top: 0.2em;
    margin-bottom: 0.7em;
  }

  .reveal .tippy-content>*:last-child {
    margin-bottom: 0.2em;
  }

  .reveal .slide > img.stretch.quarto-figure-center,
  .reveal .slide > img.r-stretch.quarto-figure-center {
    display: block;
    margin-left: auto;
    margin-right: auto; 
  }

  .reveal .slide > img.stretch.quarto-figure-left,
  .reveal .slide > img.r-stretch.quarto-figure-left  {
    display: block;
    margin-left: 0;
    margin-right: auto; 
  }

  .reveal .slide > img.stretch.quarto-figure-right,
  .reveal .slide > img.r-stretch.quarto-figure-right  {
    display: block;
    margin-left: auto;
    margin-right: 0; 
  }

</style>