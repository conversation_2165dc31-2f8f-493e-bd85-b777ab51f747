/**
 * White theme for reveal.js. This is the opposite of the 'black' theme.
 *
 * By <PERSON><PERSON>, http://hakim.se
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------


// Include theme-specific fonts
@import url(./fonts/source-sans-pro/source-sans-pro.css);


// Override theme settings (see ../template/settings.scss)
$backgroundColor: #fff;

$mainColor: #222;
$headingColor: #222;

$mainFontSize: 42px;
$mainFont: 'Source Sans Pro', Helvetica, sans-serif;
$headingFont: 'Source Sans Pro', Helvetica, sans-serif;
$headingTextShadow: none;
$headingLetterSpacing: normal;
$headingTextTransform: uppercase;
$headingFontWeight: 600;
$linkColor: #2a76dd;
$linkColorHover: lighten( $linkColor, 15% );
$selectionBackgroundColor: lighten( $linkColor, 25% );

$heading1Size: 2.5em;
$heading2Size: 1.6em;
$heading3Size: 1.3em;
$heading4Size: 1.0em;

// Change text colors against dark slide backgrounds
@include dark-bg-text-color(#fff);


// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
