// Exposes theme's variables for easy re-use in CSS for plugin authors

:root {
  --r-background-color: #{$backgroundColor};
  --r-main-font: #{$mainFont};
  --r-main-font-size: #{$mainFontSize};
  --r-main-color: #{$mainColor};
  --r-block-margin: #{$blockMargin};
  --r-heading-margin: #{$headingMargin};
  --r-heading-font: #{$headingFont};
  --r-heading-color: #{$headingColor};
  --r-heading-line-height: #{$headingLineHeight};
  --r-heading-letter-spacing: #{$headingLetterSpacing};
  --r-heading-text-transform: #{$headingTextTransform};
  --r-heading-text-shadow: #{$headingTextShadow};
  --r-heading-font-weight: #{$headingFontWeight};
  --r-heading1-text-shadow: #{$heading1TextShadow};
  --r-heading1-size: #{$heading1Size};
  --r-heading2-size: #{$heading2Size};
  --r-heading3-size: #{$heading3Size};
  --r-heading4-size: #{$heading4Size};
  --r-code-font: #{$codeFont};
  --r-link-color: #{$linkColor};
  --r-link-color-dark: #{darken($linkColor , 15% )};
  --r-link-color-hover: #{$linkColorHover};
  --r-selection-background-color: #{$selectionBackgroundColor};
  --r-selection-color: #{$selectionColor};
}
