/**
 * Solarized Light theme for reveal.js.
 * Author: <PERSON><PERSON><PERSON>
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------



// Include theme-specific fonts
@import url(./fonts/league-gothic/league-gothic.css);
@import url(https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic);


/**
 * Solarized colors by <PERSON>
 */
html * {
	color-profile: sRGB;
	rendering-intent: auto;
}

// Solarized colors
$base03:    #002b36;
$base02:    #073642;
$base01:    #586e75;
$base00:    #657b83;
$base0:     #839496;
$base1:     #93a1a1;
$base2:     #eee8d5;
$base3:     #fdf6e3;
$yellow:    #b58900;
$orange:    #cb4b16;
$red:       #dc322f;
$magenta:   #d33682;
$violet:    #6c71c4;
$blue:      #268bd2;
$cyan:      #2aa198;
$green:     #859900;

// Override theme settings (see ../template/settings.scss)
$mainColor: $base00;
$headingColor: $base01;
$headingTextShadow: none;
$backgroundColor: $base3;
$linkColor: $blue;
$linkColorHover: lighten( $linkColor, 20% );
$selectionBackgroundColor: $magenta;

// Background generator
// @mixin bodyBackground() {
// 	@include radial-gradient( rgba($base3,1), rgba(lighten($base3, 20%),1) );
// }



// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
