// Base settings for all themes that can optionally be
// overridden by the super-theme

// Background of the presentation
$backgroundColor: #2b2b2b;

// Primary/body text
$mainFont: 'Lato', sans-serif;
$mainFontSize: 40px;
$mainColor: #eee;

// Vertical spacing between blocks of text
$blockMargin: 20px;

// Headings
$headingMargin: 0 0 $blockMargin 0;
$headingFont: 'League Gothic', Impact, sans-serif;
$headingColor: #eee;
$headingLineHeight: 1.2;
$headingLetterSpacing: normal;
$headingTextTransform: uppercase;
$headingTextShadow: none;
$headingFontWeight: normal;
$heading1TextShadow: $headingTextShadow;

$heading1Size: 3.77em;
$heading2Size: 2.11em;
$heading3Size: 1.55em;
$heading4Size: 1.00em;

$codeFont: monospace;

// Links and actions
$linkColor: #13DAEC;
$linkColorHover: lighten( $linkColor, 20% );

// Text selection
$selectionBackgroundColor: #FF5E99;
$selectionColor: #fff;

// Generates the presentation background, can be overridden
// to return a background image or gradient
@mixin bodyBackground() {
	background: $backgroundColor;
}
