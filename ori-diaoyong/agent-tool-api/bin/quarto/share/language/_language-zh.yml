toc-title-document: "目录"
toc-title-website: "该页面内容"

related-formats-title: "其他格式"
related-notebooks-title: "笔记本"
source-notebooks-prefix: "资源"
other-links-title: "其他链接"
code-links-title: "代码链接"
launch-dev-container-title: "启动 Dev Container"
launch-binder-title: "启动 Binder"

article-notebook-label: "文章笔记本"
notebook-preview-download: "下载笔记本"
notebook-preview-download-src: "下载源代码"
notebook-preview-back: "返回文章"
manuscript-meca-bundle: "MECA存档"

section-title-abstract: "摘要"
section-title-appendices: "附录"
section-title-footnotes: "脚注"
section-title-references: "参考"
section-title-reuse: "重用"
section-title-copyright: "版权"
section-title-citation: "引文"

appendix-attribution-bibtex: "BibTeX"
appendix-attribution-cite-as: "请引用这项工作："

title-block-author-single: "作者"
title-block-author-plural: "作者"
title-block-affiliation-single: "联系"
title-block-affiliation-plural: "隶属关系"
title-block-published: "发布日期"
title-block-modified: "修改的"
title-block-keywords: "关键词"

callout-tip-title: "提示"
callout-note-title: "注释"
callout-warning-title: "警告"
callout-important-title: "重要"
callout-caution-title: "注意"

code-summary: "代码"

code-line: "行"
code-lines: "行"

code-tools-menu-caption: "代码"
code-tools-show-all-code: "显示所有代码"
code-tools-hide-all-code: "隐藏所有代码"
code-tools-view-source: "查看源代码"
code-tools-source-code: "源代码"

copy-button-tooltip: "复制到剪贴板"
copy-button-tooltip-success: "已复制"

repo-action-links-edit: "编辑该页面"
repo-action-links-source: "查看代码"
repo-action-links-issue: "报告问题"

back-to-top: "回到顶部"

search-no-results-text: "没有结果"
search-matching-documents-text: "匹配的文档"
search-copy-link-title: "复制搜索链接"
search-hide-matches-text: "隐藏其它匹配结果"
search-more-match-text: "更多匹配结果"
search-more-matches-text: "更多匹配结果"
search-clear-button-title: "清除"
search-text-placeholder: ""
search-detached-cancel-button-title: "取消"
search-submit-button-title: "提交"
search-label: "搜索"

toggle-section: "切換部分"
toggle-sidebar: "切换侧边栏导航"
toggle-dark-mode: "切换深色模式"
toggle-reader-mode: "切换阅读器模式"
toggle-navigation: "切换导航"

crossref-fig-title: "图"
crossref-tbl-title: "表格"
crossref-lst-title: "列表"
crossref-thm-title: "定理"
crossref-lem-title: "引理"
crossref-cor-title: "推论"
crossref-prp-title: "命题"
crossref-cnj-title: "猜想"
crossref-def-title: "定义"
crossref-exm-title: "例子"
crossref-exr-title: "练习"
crossref-ch-prefix: "章节"
crossref-apx-prefix: "附录"
crossref-sec-prefix: "章节"
crossref-eq-prefix: "方程式"
crossref-lof-title: "插图目录"
crossref-lot-title: "列表目录"
crossref-lol-title: "列表目录"

environment-proof-title: "论证"
environment-remark-title: "评语"
environment-solution-title: "答案"

listing-page-order-by: "排序方式"
listing-page-order-by-default: "缺省"
listing-page-order-by-date-asc: "日期升序"
listing-page-order-by-date-desc: "日期降序"
listing-page-order-by-number-desc: "降序"
listing-page-order-by-number-asc: "升序"
listing-page-field-date: "日期"
listing-page-field-title: "标题"
listing-page-field-description: "描述"
listing-page-field-author: "作者"
listing-page-field-filename: "文件名"
listing-page-field-filemodified: "修改时间"
listing-page-field-subtitle: "副标题"
listing-page-field-readingtime: "阅读时间"
listing-page-field-categories: "类别"
listing-page-minutes-compact: "{0} 分钟"
listing-page-category-all: "全部"
listing-page-no-matches: "无匹配项"
listing-page-field-wordcount: "单词统计"
listing-page-words: "{0}个单词"
