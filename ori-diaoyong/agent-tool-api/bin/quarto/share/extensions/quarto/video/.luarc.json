{"Generator": ["Quarto", "This file provides type information for Lua completion and diagnostics.", "Quarto will automatically update this file to reflect the current path", "of your Quarto installation, and the file will also be added to .gitignore", "since it points to the absolute path of Quarto on the local system.", "Remove the 'Generator' key to manage this file's contents manually."], "Lua.runtime.version": "Lua 5.3", "Lua.workspace.checkThirdParty": false, "Lua.workspace.library": ["/Users/<USER>/projects/github/quarto/quarto-cli/src/resources/lua-types"], "Lua.runtime.plugin": "/Users/<USER>/projects/github/quarto/quarto-cli/src/resources/lua-plugin/plugin.lua", "Lua.completion.showWord": "Disable", "Lua.completion.keywordSnippet": "Both", "Lua.diagnostics.disable": ["lowercase-global", "trailing-space"]}