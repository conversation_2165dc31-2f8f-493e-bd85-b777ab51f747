import json
from flask import jsonify, Response

def make_success_response(data) -> dict:
    return jsonify({
        "code": 200,
        "msg": "success",
        "data": data,
        "success": True
    })

def make_citations_response(citations) -> dict:
    return jsonify({
        "code": 200,
        "msg": "success",
        "citations": citations,
        "success": True
    })

def make_error_response(message: str) -> tuple:
    return jsonify({
        "code": 500,
        "msg": message,
        "success": False
    }), 500

def make_pretty_response(data, status=200):
    return Response(json.dumps(data, ensure_ascii=False, indent=2), status=status, headers={'Content-Type': 'application/json;charset=utf-8'})
