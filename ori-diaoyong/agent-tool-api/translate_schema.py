#!/usr/bin/env python3
import json
import os
import time
from typing import Any, Dict, List
import requests
import urllib3
from pathlib import Path

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def parse_translation_response(response_text: str) -> str:
    """
    Parse the translation response from the API and extract the translated text.
    """
    try:
        response = json.loads(response_text)
        if "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]
        return ""
    except Exception as e:
        print(f"Error parsing response: {e}")
        return ""

def translate_text(text: str, retry_count: int = 3, delay: float = 2.0) -> str:
    """
    Translate Chinese text to English using the provided API.
    Includes retry mechanism and rate limiting.
    """
    url = "https://172.17.120.207:30745/gateway/mw/api/v1/mwh/svcmgr/remote-services/dd9536d8-15cb-4790-a3f3-73fee5560a9a/infer"
    
    headers = {
        'accept': 'application/json',
        'content-type': 'application/json',
        'cookie': 'LLM1ST=u7KRTNgNV-x9rHuxmRs-b3OLYTuC1N7Qw-Y=; PROJECT_ID=assets; debug=1; SOPHONID=d28210e24c80026618ed5b697e407f3e; lang=zh'
    }
    
    data = {
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的翻译助手。请将用户输入的中文文本翻译成英文。保持专业性和准确性。输出时仅保留翻译结果即可,不要有额外的内容"
            },
            {
                "role": "user",
                "content": f"请将以下中文翻译成英文：{text}"
            }
        ],
        "model": "atom",
        "stream": False
    }
    
    for attempt in range(retry_count):
        try:
            # Create a session with custom SSL settings
            session = requests.Session()
            session.verify = False
            session.trust_env = False
            
            response = session.post(
                f"{url}?seldon_mode=true",
                headers=headers,
                json=data
            )
            
            print(f"Response status code: {response.status_code}")
            print(f"Response text: {response.text}")
            
            if response.status_code == 429:  # Too Many Requests
                print(f"Rate limit hit, waiting {delay} seconds before retry {attempt + 1}/{retry_count}")
                time.sleep(delay)
                delay *= 2  # Exponential backoff
                continue
                
            return parse_translation_response(response.text)
        except Exception as e:
            print(f"Translation error (attempt {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(delay)
                delay *= 2
            else:
                return text

def should_translate(key: str, path: List[str]) -> bool:
    """
    Determine if a field should be translated based on its key and path in the JSON structure.
    """
    if key == "description":
        return True
    if key == "title" and len(path) >= 2 and path[-2] == "info":
        return True
    return False

def translate_json_value(value: Any, path: List[str] = None) -> Any:
    """
    Recursively traverse JSON structure and translate relevant fields.
    """
    if path is None:
        path = []

    if isinstance(value, dict):
        result = {}
        for k, v in value.items():
            new_path = path + [k]
            if should_translate(k, new_path):
                result[k] = translate_text(v)
            else:
                result[k] = translate_json_value(v, new_path)
        return result
    elif isinstance(value, list):
        return [translate_json_value(item, path) for item in value]
    else:
        return value

def process_schema_files():
    """
    Process all JSON files in the chinese-desc directory and save translations to english-desc.
    """
    chinese_dir = Path("openapi_schema/chinese-desc")
    english_dir = Path("openapi_schema/english-desc")

    # Create english-desc directory if it doesn't exist
    english_dir.mkdir(parents=True, exist_ok=True)

    # Process each JSON file
    for json_file in chinese_dir.glob("*.json"):
        print(f"Processing {json_file.name}...")
        
        # Read the Chinese schema
        with open(json_file, 'r', encoding='utf-8') as f:
            schema = json.load(f)

        # Translate the schema
        translated_schema = translate_json_value(schema)

        # Save the translated schema
        output_file = english_dir / json_file.name
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(translated_schema, f, ensure_ascii=False, indent=2)

        print(f"Translated {json_file.name} -> {output_file.name}")

if __name__ == "__main__":
    process_schema_files() 