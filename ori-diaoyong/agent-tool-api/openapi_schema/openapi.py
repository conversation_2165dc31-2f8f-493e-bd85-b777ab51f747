import os
import json
from config import conf
import logging

OPENAPI_EXTENSION_AVATAR_URL = "x-avatar-url"
OPENAPI_EXTENSION_USED_IN_PRODUCTION = "x-usedInProduction"

# 指定需要设置为true的title列表
USED_IN_PRODUCTION_TITLES = [
    "企业微信",
    "Enterprise WeChat",
    "URL内容解析",
    "URL content parsing",
    "文件读取器",
    "File reader",
    "图表生成",
    "Chart generation",
    "代码解释器",
    "code interpreter",
    "邮件发送",
    "Email sending",
    "必应搜索",
    "Bing Search",
]

def load_openapi_schemas(lang=None):
    """
    加载 OpenAPI schema 文件
    :param lang: 语言代码，'zh' 或 'en'，如果为 None 则返回中文的 schema
    :return: schema 列表
    """
    base_dir = 'openapi_schema'
    lang_dirs = {
        'zh': os.path.join(base_dir, 'chinese-desc'),
        'en': os.path.join(base_dir, 'english-desc')
    }
    
    if lang:
        if lang not in lang_dirs:
            raise ValueError(f"Unsupported language: {lang}")
        return load_schemas_from_dir(lang_dirs[lang])
    
    # 默认返回中文 schema
    return load_schemas_from_dir(lang_dirs['zh'])

def load_schemas_from_dir(dir_path):
    """
    从指定目录加载 schema 文件
    :param dir_path: schema 文件目录路径
    :param add_avatar_url: 是否添加头像URL
    :return: schema 列表
    """
    schemas = []
    if os.path.exists(dir_path):
        avatar_prefix = conf().get('avatar_prefix', 'sfs:///tool/avatars')
        for filename in os.listdir(dir_path):
            if filename.endswith('.json'):
                file_path = os.path.join(dir_path, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        schema = json.load(f)
                        avatar_filename = os.path.splitext(filename)[0] + '.jpg'
                        schema[OPENAPI_EXTENSION_AVATAR_URL] = os.path.join(avatar_prefix, avatar_filename)
                        title = schema.get('info', {}).get('title', '')
                        if title in USED_IN_PRODUCTION_TITLES:
                            schema[OPENAPI_EXTENSION_USED_IN_PRODUCTION] = True
                        else:
                            schema[OPENAPI_EXTENSION_USED_IN_PRODUCTION] = False
                        schemas.append(schema)
                except Exception as e:
                    logging.error(f"Error loading schema {filename}: {str(e)}")
    return schemas