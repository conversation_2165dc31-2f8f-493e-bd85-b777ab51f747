{"openapi": "3.0.0", "info": {"title": "文件读取器", "description": "从URL读取各种格式文件的内容，仅支持txt、md、csv、pdf、docx、xlsx、xls格式", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"read_file": {"post": {"description": "从指定URL下载文件并提取其文本内容, 仅支持txt、md、csv、pdf、docx、xlsx、xls格式。", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "文件的http协议URL地址"}, "name": {"type": "string", "description": "文件名，包含扩展名，用于确定文件类型, 如temp.txt, book.pdf"}}, "required": ["url", "name"]}}}}}}}}