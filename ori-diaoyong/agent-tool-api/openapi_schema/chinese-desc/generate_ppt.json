{"openapi": "3.1.0", "info": {"title": "PPT生成", "description": "供根据提的标题和内容生成一个PPT文件。", "version": "v1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"generate_ppt": {"post": {"description": "供根据提的标题和内容生成一个PPT文件。", "operationId": "generatePPT", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "description": "PPT文件的标题"}, "content": {"type": "string", "description": "表示每页PPT标题和内容的Markdown格式字符串。示例：## 第一页标题\n\n第一页内容 \n\n## 第二页标题\n\n第二页内容\n\n"}}, "required": ["title", "content"]}}}}}}}}