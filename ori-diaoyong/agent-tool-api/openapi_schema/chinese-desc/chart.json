{"openapi": "3.1.0", "info": {"title": "图表生成", "description": "图表生成是一个用于生成可视化图表的工具，你可以通过它来生成柱状图、折线图、饼图等各类图表", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/chart/"}], "paths": {"line": {"post": {"description": "生成一张包含输入数据的线性图表。", "operationId": "line", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "用于生成线性图表的数据，数据应为包含数字列表的字符串，\"1;2;3;4;5\""}, "x_axis": {"type": "string", "description": "线性图表的 x 轴，x 轴应为包含一系列文本的字符串，如 \"a;b;c;1;2\"，以便与数据匹配。"}}, "required": ["data"]}}}}}}, "bar": {"post": {"description": "使用输入数据生成柱状图", "operationId": "bar", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "用于生成柱状图的数据，数据应为包含数字列表的字符串，例如 \"1;2;3;4;5\""}, "x_axis": {"type": "string", "description": "条形图的 x 轴，x 轴应该是一个包含文本列表的字符串，如 \"a;b;c;1;2\"，以便与数据匹配。"}}, "required": ["data"]}}}}}}, "pie": {"post": {"description": "使用输入数据生成饼图", "operationId": "pie", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "用于生成饼图的数据，数据应为包含数字列表的字符串，如\"1;2;3;4;5\"。"}, "categories": {"type": "string", "description": "饼图的类别，类别应为包含文本列表的字符串，如 \"a;b;c;1;2\"，以便与数据匹配。"}}, "required": ["data"]}}}}}}}}