{"openapi": "3.1.0", "info": {"title": "PPT generation", "description": "To generate a PPT file based on the provided title and content.", "version": "v1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"generate_ppt": {"post": {"description": "To generate a PPT file based on the provided title and content.", "operationId": "generatePPT", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the PPT file"}, "content": {"type": "string", "description": "Markdown formatted string representing the title and content of each PPT page. Example: \n\n## Title of the First Page\n\nContent of the First Page \n\n## Title of the Second Page\n\nContent of the Second Page"}}, "required": ["title", "content"]}}}}}}}}