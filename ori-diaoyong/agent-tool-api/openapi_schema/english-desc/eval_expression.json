{"openapi": "3.1.0", "info": {"title": "Calculator", "description": "A tool for calculating mathematical expressions.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"eval_expression": {"post": {"description": "A tool for calculating mathematical expressions.", "operationId": "eval_expression", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"expression": {"type": "string", "description": "Mathematical expression to be calculated", "default": ""}}, "required": ["expression"]}}}}}}}}