{"openapi": "3.0.0", "info": {"title": "code interpreter", "description": "Run a piece of code and return the result.", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/code_interpreter/"}], "paths": {"python": {"post": {"description": "A tool for running Python code and obtaining the execution results. Please use the print() function in your code to output the results, otherwise the results will be empty.", "operationId": "python代码解释器", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "description": "Please enter valid Python code that uses the print() function to output the result of the execution, otherwise the result will be empty."}}, "required": ["code"]}}}}}}}}