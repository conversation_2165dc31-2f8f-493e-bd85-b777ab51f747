{"openapi": "3.0.0", "info": {"title": "<PERSON><PERSON> search", "description": "A tool for obtaining information using a search engine", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"jina_search": {"post": {"description": "A tool for obtaining information using a search engine", "operationId": "jina_search", "requestBody": {"description": "Search request", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The content that needs to be searched", "default": ""}}, "required": ["query"]}}}}}}}}