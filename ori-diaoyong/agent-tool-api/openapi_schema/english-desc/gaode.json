{"openapi": "3.1.0", "info": {"title": "<PERSON><PERSON>", "description": "AMap Open Platform Service Toolkit", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"gaode_weather": {"post": {"description": "The tool used when you want to inquire about the weather or weather-related questions.", "operationId": "gaode_weather", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"city": {"type": "string", "description": "The city name for weather inquiry. If there is no city information, you can ask for the city name. You need to extract the Chinese city name from the question."}}, "required": ["city"]}}}}}}}}