{"openapi": "3.0.0", "info": {"title": "Email sending", "description": "Tools for sending emails", "version": "1.0.0"}, "servers": [{"url": "http://autocv-agent-tool-api-service.{namespace}/v1/"}], "paths": {"send_emails": {"post": {"description": "A tool for sending emails requires a recipient list, email subject, and body content.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"receivers": {"type": "array", "items": {"type": "string", "format": "email"}, "description": "Email recipient list"}, "subject": {"type": "string", "description": "Email Subject"}, "text": {"type": "string", "description": "Email content"}, "cc": {"type": "array", "items": {"type": "string", "format": "email"}, "description": "CC (Carbon Copy) recipients list, an optional parameter."}}, "required": ["receivers", "subject", "text"]}}}}}}}}