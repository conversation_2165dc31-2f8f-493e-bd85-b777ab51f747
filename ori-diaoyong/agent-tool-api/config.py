import json
import os
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(filename)s:%(lineno)d]'
    )

    # 设置文件日志，日志文件名为 app.log，最大文件大小为 10MB，最多保留 5 个文件
    file_handler = RotatingFileHandler('flask-app.log', maxBytes=10*1024*1024, backupCount=5, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 设置控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 获取根日志记录器，添加文件日志和控制台日志
    logging.basicConfig(level=logging.DEBUG, handlers=[file_handler, console_handler])


setup_logging()

def load_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # 从环境变量加载配置,覆盖文件中的配置
    logging.info("[CONF] 从环境变量加载配置")
    logging.info(f"[CONF] 环境变量名称: {', '.join(os.environ.keys())}")
    for name, value in os.environ.items():
        name = name.lower()
        if name in config and value.strip():
            logging.info(f"[CONF] 通过环境变量覆盖配置: {name}={value}")
            config[name] = value
    
    return config

config = load_config()

def reload_conf():
    global config
    config = load_config()

def conf() -> dict:
    return config
