FROM harbor.transwarp.io/aip/base/python:3.11.8-slim-bookworm

# gunicorn 进程数量 http服务端口号
ENV WORKER_NUM=1 \
    PORT=80

COPY . /api

WORKDIR /api

# 更新apt为国内源，安装中文字体
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn\/debian-security/g' /etc/apt/sources.list.d/debian.sources && \
    apt update && apt install -y --no-install-recommends fontconfig && \
    cp SimHei.ttf /usr/share/fonts/SimHei.ttf && \
    fc-cache -fv

# 安装python 依赖
RUN pip install -v -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple --trusted-host mirrors.aliyun.com

CMD sh bin/boot.sh
