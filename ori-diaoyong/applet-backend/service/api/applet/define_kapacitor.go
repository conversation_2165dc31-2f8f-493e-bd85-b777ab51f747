package applet

//func (r *Resource) EnableKpctScript(request *restful.Request, response *restful.Response) {
//	ctx := context.Background()
//
//	scriptID, err := clients.KapacitorCli.RegisterTickScript(ctx, "")
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, scriptID)
//}
//
//func (r *Resource) DisableKpctScript(request *restful.Request, response *restful.Response) {
//	ctx := context.Background()
//	err := clients.KapacitorCli.DisableTickScript(ctx, "")
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, true)
//}
