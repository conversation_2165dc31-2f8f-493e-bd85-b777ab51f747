package applet

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func (r *Resource) ListDynamicDatasource(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	dsKey := request.QueryParameter("key")
	if dsKey == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("no datasource key"))
		return
	}
	svc, ok := applet.DynamicDatasourceMap[dsKey]
	if !ok {
		helper.ErrorResponse(response, stderr.Internal.Error("invalid datasource key :%v", dsKey))
		return
	}

	res, err := svc.List(request, ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}
