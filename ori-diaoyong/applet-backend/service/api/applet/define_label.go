package applet

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	ChainTypeParam = "chain_type"
)

// ListLabels 获取所有标签
func (r *Resource) ListLabels(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ChainType := models.ChainTypeUserCreate
	chainTypeStr := request.QueryParameter(ChainTypeParam)
	if chainTypeStr == models.ChainTypeTemplateStr {
		ChainType = models.ChainTypeTemplate
	}
	labels, err := applet.LabelManager.ListLabel(ctx, ChainType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, labels.ToDTO())
}

// SaveLabels 保存标签
func (r *Resource) SaveLabels(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	userID := auth.GetAuthContext(request).GetUsername()
	param := &models.LabelGroupsDTO{}
	request.ReadEntity(param)
	err := applet.LabelManager.BatchSaveLabelIgnoreDup(ctx, userID, param.ToDO())
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, true)
}
