package applet

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	AgentToolAPIURL = "http://localhost:8080" // agent-tool-api的地址
)

// getAgentToolAPIURL 获取agent-tool-api的URL
func getAgentToolAPIURL() string {
	// TODO: 从配置文件读取，这里先使用默认值
	return AgentToolAPIURL
}

// httpClient HTTP客户端
var httpClient = &http.Client{}

// callAgentToolAPI 调用agent-tool-api
func callAgentToolAPI(method, endpoint string, body io.Reader) (*http.Response, error) {
	url := AgentToolAPIURL + endpoint
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return httpClient.Do(req)
}

// ==================== Dify Plugin处理函数 ====================

// GetInstalledDifyPlugins 获取所有已安装插件信息及授权状态（并行优化版本）
func (r *Resource) GetInstalledDifyPlugins(request *restful.Request, response *restful.Response) {
	resp, err := callAgentToolAPI("GET", "/v1/dify-plugins/installed", nil)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("调用agent-tool-api失败: %v", err)))
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("读取响应失败: %v", err)))
		return
	}

	var result InstalledPluginsResponse
	if err := json.Unmarshal(body, &result); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("解析响应失败: %v", err)))
		return
	}

	helper.SuccessResponse(response, result.Data)
}

// UploadDifyPlugin 上传插件
func (r *Resource) UploadDifyPlugin(request *restful.Request, response *restful.Response) {
	// 解析multipart form
	err := request.Request.ParseMultipartForm(32 << 20) // 32 MB
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Error(fmt.Sprintf("解析表单失败: %v", err)))
		return
	}

	file, fileHeader, err := request.Request.FormFile("pkg")
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("pkg文件不能为空"))
		return
	}
	defer file.Close()

	// 验证文件扩展名
	filename := fileHeader.Filename
	if !strings.HasSuffix(strings.ToLower(filename), ".difypkg") {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("文件必须是.difypkg格式"))
		return
	}

	// 创建新的multipart form用于转发
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 创建文件字段
	fileWriter, err := writer.CreateFormFile("file", filename)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("创建文件字段失败: %v", err)))
		return
	}

	// 复制文件内容
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("复制文件内容失败: %v", err)))
		return
	}

	writer.Close()

	// 调用agent-tool-api
	agentToolAPIURL := getAgentToolAPIURL()
	uploadURL := fmt.Sprintf("%s/v1/dify-plugins/upload", agentToolAPIURL)

	req, err := http.NewRequest("POST", uploadURL, &buf)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("创建请求失败: %v", err)))
		return
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{Timeout: 300 * time.Second} // 5分钟超时
	resp, err := client.Do(req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("调用agent-tool-api失败: %v", err)))
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("读取响应失败: %v", err)))
		return
	}

	// 解析响应
	var result struct {
		Success bool        `json:"success"`
		Message string      `json:"msg"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error(fmt.Sprintf("解析响应失败: %v", err)))
		return
	}

	// 返回结果
	if result.Success {
		helper.SuccessResponse(response, result.Data)
	} else {
		helper.ErrorResponse(response, stderr.Internal.Error(result.Message))
	}
}
