package applet

import (
	"fmt"
	"net/http"

	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func (r *Resource) ApplicationService(root string) {
	projectIDQueryParam := r.QueryParameter("project_id", "项目ID").Required(true)
	r.Path(root).Consumes(restful.MIME_JSON, "text/event-stream").Produces(restful.MIME_JSON, "text/event-stream")
	// READ APIs
	tags := []string{"应用仓库-应用管理"}
	r.Route(r.GET("/applications").To(r.ListApplications).
		Doc("获取应用列表-所有形式").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.AppletChainBaseDO{}))

	r.Route(r.POST("/applications:create").To(r.CreateApplication).
		Doc("创建应用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.AppletChainDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.POST("/applications:update").To(r.UpdateApplication).
		Doc("更新应用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.AppletChainDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.GET("/applications/{id}/experiment").To(r.GetExperimentInfo).
		Doc("将获取应用的体验信息:如图片、开场白等").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "应用ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExperimentInfo{}))

	r.Route(r.PUT("/applications/{id}/experiment").To(r.UpdateChainExperiment).
		Doc("更新应用的体验信息:如图片、开场白等").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "应用ID")).
		Reads(models.ExperimentInfo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/applications:publish").To(r.PublishApplication).
		Doc("将已保存应用（应用助手以及应用链）使用默认配置发布到服务管理,以及重新上线").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/applications:publish-callback").To(r.PublishApplicationCallBack).
		Doc("将已保存应用（应用助手以及应用链）使用默认配置发布到服务管理,以及重新上线").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/applications:cancel").To(r.CancelApplication).
		Doc("将已发布的应用取消,传入应用链ID").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.GET("/applications/-/services").To(r.ListAppService).
		Doc("应用中心-服务体验列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.QueryParameter(QueryParameterContain, "文本检索")).
		Param(r.QueryParameter(QueryParameterOnlyRunning, "only_running=true,仅返回健康能运行的")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.AppExperimentService{}))

	r.Route(r.GET("/applications/-/services/health-info").To(r.ListServiceHealthInfo).
		Doc("获取所有部署服务的健康信息,较慢").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]*applet.ChainState{}))

	r.Route(r.GET("/applications/-/kb-services").To(r.ListKbAppService).
		Doc("应用中心-获取与知识库相关的服务").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.QueryParameter(helper.QueryParamMlopsStateSelector, "mlops_svc_state状态筛选,逗号分割. Creating,Pending,Available...")).
		Param(r.QueryParameter(ServiceType, fmt.Sprintf("服务类型,限定为%s或者%s",
			ServiceTypeChunking, ServiceTypeRecall)).Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.AppExperimentService{}))

	r.Route(r.GET("/applications/-/service-tools").To(r.ListAppServiceAsTool).
		Doc("应用中心-服务工具列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(ServiceType, fmt.Sprintf("服务类型,限定为%s、%s、%s",
			ServiceTypeChunking, ServiceTypeRecall, ServiceTypeQuestionClassify)).Required(false)).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*agent_definition.AppletServiceToolDescriber{}))

	r.Route(r.GET("/applications/{id}/service").To(r.GetAppService).
		Doc("应用中心-单个服务信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "应用ID")).
		Param(r.QueryParameter(QueryParameterNeedHealthInfo,
			"是否需要获取该服务的健康状态").DefaultValue(QueryParameterValueFalse)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppExperimentService{}))

	r.Route(r.GET("/applications/{id}/knowledge").To(r.GetRelatedApp).
		Doc("应用中心-查询使用了指定知识库的应用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "知识库id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.SimpleChainInfo{}))

	r.Route(r.GET("/applications/-/labels").To(r.ListAppServLabels).
		Doc("应用中心-获取服务体验列表的所有标签信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.LabelGroups{}))

	r.Route(r.POST("/applications/{id}/count").To(r.CountVisitInfo).
		Doc(fmt.Sprintf("统计应用的访问量等信息,type限定为:%s, %s, %s",
			applet.ChainMetricsTypeVisit, applet.ChainMetricsTypeClone, applet.ChainMetricsTypeExecute)).
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "应用ID")).
		Reads(models.UpdateVisitInfoReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/applications:ai-generate-chain").To(r.AIGenerateChain).
		Doc(fmt.Sprintf("ai生成应用链")).
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AIGenerateChainReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ChainInfo{}))
}

func (r *Resource) AssistantService(root string) {
	projectIDQueryParam := r.QueryParameter("project_id", "项目ID").Required(true)
	tags := []string{"应用仓库-助手管理(零代码构建)"}
	r.Path(root).Consumes(restful.MIME_JSON, "text/event-stream").Produces(restful.MIME_JSON, "text/event-stream")
	r.Route(r.POST("/assistants").To(r.CreateAssistant).
		Doc("创建新的智能助手").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Reads(models.AppletAssistant{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.PUT("/assistants/{id}").To(r.UpdateAssistant).
		Doc("更新智能助手定义（非发布）").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Reads(models.AppletAssistant{}).
		Param(r.PathParameter("id", "应用ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.GET("/assistants/{id}/chain").To(r.GetAssistantChain).
		Doc("获取创建应用智能体时所填信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "应用ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletAssistant{}))

	r.Route(r.POST("/assistants:debug").To(r.DebugAssistant).
		Doc("智能体调试测试").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AssistantDebugReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), struct{}{}))

	r.Route(r.POST("/assistants:nodes-info").To(r.GetAssistantNodesInfo).
		Doc("获取智能助手对应的节点信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AssistantDebugReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), struct{}{}))

	r.Route(r.PUT("/assistants/{id}/chats/{chat_id}/state").To(r.UpdateAssistantDebugState).
		Doc("更新智能体调试结果,此时需智能体已创建").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用ID")).
		Param(r.PathParameter("chat_id", "本次对话ID")).
		Reads(models.UpdateDebugStateReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))
}

var (
	externalAppIDPP = stdsrv.NewPathParam("id", "外部应用ID")
)

// ExternalAppService 管理注册的第三方应用服务的接口
func (r *Resource) ExternalAppService(root string) {
	r.ea = dao.NewExternalAPPDB()
	projectIDQueryParam := r.QueryParameter("project_id", "项目ID").Required(true)
	tags := []string{"应用仓库-外部自定义应用注册管理"}
	r.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	r.Route(r.GET("/external/apps").To(r.ListExternalApps).
		Doc("获取外部应用列表").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Writes(models.ExternalAPP{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExternalAPP{}))

	// r.Route(r.GET("/external/apps/{id}").To(r.GetExternalApp).
	// 	Doc("获取特定的外部应用定义").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
	// 	Writes(models.ExternalAPP{}).
	// 	Param(r.PathParameter("id", "应用ID")).
	// 	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExternalAPP{}))

	r.Route(r.POST("/external/apps").To(r.CreateExternalApp).
		Doc("创建一个外部应用").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Reads(models.ExternalAPP{}).
		Writes(models.ExternalAPP{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExternalAPP{}))

	r.Route(r.PUT("/external/apps/{id}").To(r.UpdateExternalApp).
		Doc("更新外部应用定义").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Reads(models.ExternalAPP{}).
		Param(externalAppIDPP.Param().Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExternalAPP{}))

	r.Route(r.DELETE("/external/apps/{id}").To(r.DeleteExternalApp).
		Doc("删除外部应用定义").Metadata(restfulspec.KeyOpenAPITags, tags).Param(projectIDQueryParam).
		Reads(models.ExternalAPP{}).
		Param(externalAppIDPP.Param().Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ExternalAPP{}))
}
