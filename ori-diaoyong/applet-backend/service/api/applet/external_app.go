package applet

import (
	"context"
	"path"
	"strconv"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/database"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	clients2 "transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func (r *Resource) CreateExternalApp(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "api.CreateExternalApp"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	var user, projectID string
	user, err = helper.GetUser(ctx)
	if err != nil {
		err = stderr.InvalidParam.Cause(err, "no user info")
		return
	}

	projectID = helper.GetProjectID(ctx)
	if projectID == "" {
		err = stderr.InvalidParam.Errorf("no project id")
		return
	}

	newApp := new(models.ExternalAPP)
	if err = request.ReadEntity(newApp); err != nil {
		err = stderr.Trace(err)
		return
	}

	// fill default values
	newApp.ID = toolkit.NewUUID()
	newApp.Creator = user
	newApp.Source = models.RegisterExternalAPP
	newApp.ProjectID = projectID
	if err = r.ea.CreateExternalAPP(ctx, newApp); err != nil {
		err = stderr.Trace(err)
		return
	}
	helper.SuccessResponse(response, newApp)
	return
}

func (r *Resource) UpdateExternalApp(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "UpdateExternalApp"))
		}
	}()
	var id string
	id, err = externalAppIDPP.GetRequiredValue(request)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	newApp := new(models.ExternalAPP)
	if err = request.ReadEntity(newApp); err != nil {
		err = stderr.Trace(err)
		return
	}
	oldApp, err := r.ea.GetExternalAPP(id)
	if err != nil {
		err = stderr.Trace(err)
		return
	}

	// keep some field static
	newApp.ID = id
	newApp.ProjectID = oldApp.ProjectID
	newApp.Creator = oldApp.Creator
	newApp.Source = oldApp.Source
	newApp.Published = oldApp.Published
	ctx := helper.GenNewCtx(request)
	if err = r.ea.UpdateExternalAPP(ctx, newApp); err != nil {
		err = stderr.Trace(err)
		return
	}
	helper.SuccessResponse(response, newApp)
	return
}

func (r *Resource) DeleteExternalApp(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "DeleteExternalApp"))
		}
	}()
	var id string
	id, err = externalAppIDPP.GetRequiredValue(request)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	err = r.ea.DeleteExternalAPP(id)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListExternalApps(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "ListExternalApps"))
		}
	}()

	ctx := helper.GenNewCtx(request)
	var projectID string

	projectID = helper.GetProjectID(ctx)
	if projectID == "" {
		err = stderr.InvalidParam.Errorf("no project id")
		return
	}

	// 从数据中获取用户自行注册的外部APP
	var ret []*models.ExternalAPP
	ret, err = r.ea.ListExternalAPPS(&models.ExternalAPP{ProjectID: projectID})
	if err != nil {
		err = stderr.Internal.Cause(err, "list all external apps")
		return
	}
	for _, ea := range ret {
		ea.Source = models.RegisterExternalAPP
	}
	filteredApps, err := helper.FilterAndSetPermission(ctx, ret, cas.ObjType_AppletChain)
	if err != nil {
		err = stderr.Internal.Cause(err, "list external apps with permission")
		return
	}

	// 从Serving获取用户基于平台部署的外部APP
	svcsList, err := clients2.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_CUSTOM},
	})
	if err != nil {
		stdlog.WithError(err).Errorf("error while listing custom services, only those registered will be returned")
		// 不要使 serving 的报错影响该接口返回
		err = nil
	}
	if svcsList != nil {
		stdlog.Debugf("got %d custom svcs while getting external apps from serving", len(svcsList.ServiceInfos))
		for _, svc := range svcsList.ServiceInfos {
			if svc.State != serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
				stdlog.Debugf("skip those custom service not available yet")
				continue
			}
			if len(svc.Apis) == 0 {
				stdlog.Debugf("skip those custom service without apis")
				continue
			}

			// FIXME 需要更加显性的约定,来表示自定义部署的应用
			// 当前默认所有具有至少一个 Apis 的自定义类型的部署, 均为自定义应用
			if len(svc.Apis) > 1 {
				stdlog.Warnf("custom service has multiple apis, using the fisrt one as the external app's source url")
			}
			filteredApps = append(filteredApps, &models.ExternalAPP{
				ID:               svc.Id,
				Name:             svc.Name,
				Desc:             svc.Desc,
				SrcUrl:           svc.VirtualSvcUrl,
				ImageUrl:         "",
				Source:           models.DeployedExternalAPP,
				PermissionAction: svc.PermissionAction,
				PermissionCfg:    svc.PermissionCfg,
			})
		}
	}

	helper.SuccessResponse(response, filteredApps)
}

// listExternalApps 获取所有 自定义部署 或 外部注册 的应用
func listExternalApps(ctx context.Context, withCustomDeployed bool) (apps []*models.ExternalAPP, err error) {
	rApps, err := listRegisteredExternalApps(ctx)
	if err != nil {
		err = stderr.Wrap(err, "list registered apps")
		return
	}
	apps = append(apps, rApps...)

	if !withCustomDeployed {
		return
	}
	dApps, err := listDeployedExternalApps(ctx)
	if err != nil {
		err = stderr.Wrap(err, "list deployed apps")
		return
	}
	apps = append(apps, dApps...)
	return
}

// listRegisteredExternalApps 获取所有 外部注册 的应用
func listRegisteredExternalApps(ctx context.Context) (apps []*models.ExternalAPP, err error) {
	eaDB := dao.GetExternalAPPDB()
	var projectID string

	// if projectID == "", get all
	projectID = helper.GetProjectID(ctx)

	// 从数据中获取用户自行注册的外部APP
	apps, err = eaDB.ListExternalAPPS(&models.ExternalAPP{ProjectID: projectID})
	if err != nil {
		err = stderr.Internal.Cause(err, "list all external apps")
		return
	}
	for _, app := range apps {
		app.Source = models.RegisterExternalAPP
	}
	return helper.FilterAndSetPermission(ctx, apps, cas.ObjType_AppletChain)
}

// listRegisteredExternalApps 获取所有 自定义部署 的应用
func listDeployedExternalApps(ctx context.Context) (apps []*models.ExternalAPP, err error) {
	// 从Serving获取用户基于平台部署的外部APP
	svcsList, err := clients2.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_CUSTOM},
	})
	if err != nil {
		stdlog.WithError(err).Errorf("error while listing custom services, only those registered will be returned")
		// 不要使 serving 的报错影响该接口返回
		err = nil
	}
	if svcsList != nil {
		stdlog.Debugf("got %d custom svcs while getting external apps from serving", len(svcsList.ServiceInfos))
		for _, svc := range svcsList.ServiceInfos {
			if svc.State != serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
				stdlog.Debugf("skip those custom service not available yet")
				continue
			}

			if svc.SourceMeta == nil || !svc.SourceMeta.IsApp {
				// 非APP类型的地自定义服务,跳过
				stdlog.Debugf("skip those services which are not custom apps")
				continue
			}

			if len(svc.Endpoints) == 0 {
				stdlog.Debugf("skip those custom service without endpoints")
				continue
			}

			if len(svc.Endpoints) > 1 {
				stdlog.Warnf("custom service has multiple endpoints, using the first one that contains API of type 'API_TYPE_WEB_APP' as the external app's source url")
			}

			apiPathForRedirect := ""
			portForRedirect := ""

		OuterLoop:
			for _, endpoint := range svc.Endpoints {
				for _, apiAttr := range endpoint.ApiAttrs {
					// 选择第一个类型为 “网页服务” 的 ApiPath，用于SrcUrl
					if apiAttr.ApiType == common.APIType_API_TYPE_WEB_APP {
						apiPathForRedirect = apiAttr.ApiPath
						portForRedirect = strconv.Itoa(int(endpoint.Port))
						break OuterLoop
					}
				}
			}

			if len(apiPathForRedirect) == 0 || len(portForRedirect) == 0 {
				stdlog.Debugf("skip those custom service without any endpoint that has API of type 'API_TYPE_WEB_APP'")
				continue
			}

			apps = append(apps, &models.ExternalAPP{
				TimeMixin: database.TimeMixin{
					CreatedAt: toolkit.ConvertUnixTimestamp(int64(svc.CreateTime)),
					UpdatedAt: toolkit.ConvertUnixTimestamp(int64(svc.UpdateTime)),
				},
				ID:   svc.Id,
				Name: svc.Name,
				Desc: svc.Desc,
				// SrcUrl:    path.Join(conf.Config.MLOps.ExternalIstioGatewayAddr, svc.VirtualSvcUrl, strconv.Itoa(int(api.Port)), apiPath),
				// SrcUrl:    path.Join(svc.VirtualSvcUrl, strconv.Itoa(int(api.Port)), apiPath),
				SrcUrl:    path.Join(svc.VirtualSvcUrl, portForRedirect, apiPathForRedirect),
				ProjectID: svc.ProjectId,
				Creator:   svc.Creator,
				ImageUrl:  "",
				Source:    models.DeployedExternalAPP,
				LabelInfo: func() models.LabelGroups {
					li := make(models.LabelGroups)
					for k, vs := range svc.Labels {
						li[k] = vs.Items
					}
					return li
				}(),
				IFrameCfg:        nil,
				MlopsSvc:         svc,
				PermissionCfg:    svc.PermissionCfg,
				PermissionAction: svc.PermissionAction,
			})
		}
	}
	return
}

// listExternalAppsAsExperis 以应用体验的形式, 获取所有 自定义部署 或 外部注册 的应用
func listExternalAppsAsExperis(ctx context.Context) ([]*models.AppExperimentService, error) {
	eas, err := listExternalApps(ctx, true)
	if err != nil {
		return nil, stderr.Wrap(err, "list external apps")
	}
	var apps []*models.AppExperimentService
	for _, ea := range eas {
		apps = append(apps, ea.ToAppExperimentService())
	}
	return apps, nil
}

// listExternalAppsAsApplications 以应用配置的形式, 获取所有 外部注册 的应用(不含自定义部署)
func listExternalAppsAsApplications(ctx context.Context) (apps []*models.AppletChainBaseDO, err error) {
	eas, err := listExternalApps(ctx, false)
	if err != nil {
		err = stderr.Errorf("list external apps")
		return
	}
	for _, ea := range eas {
		app := ea.ToAppletChainBaseDO()
		if app == nil {
			continue
		}
		apps = append(apps, app)
	}
	return apps, nil
}
