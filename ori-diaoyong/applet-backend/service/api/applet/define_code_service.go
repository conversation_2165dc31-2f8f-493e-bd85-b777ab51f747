package applet

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/code_service"
)

// GenerateCode 生成代码
func (r *Resource) GenerateCode(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	generateCodeRequest := &code_service.GenerateCodeRequest{}
	if err := stdsrv.ReadEntityMixWithProto(request, generateCodeRequest); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	if err := generateCodeRequest.Validate(); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	result, err := applet.CodeService.GenerateCode(ctx, generateCodeRequest)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, result)
}

// TestCode 测试代码
func (r *Resource) TestCode(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	testCodeRequest := &code_service.TestCodeRequest{}
	if err := request.ReadEntity(testCodeRequest); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	if err := testCodeRequest.Validate(); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	result, err := applet.CodeService.TestCode(ctx, testCodeRequest)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, result)
}
