package applet

import (
	"fmt"
	"net/http"
	"path"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/code_service"
	"transwarp.io/applied-ai/applet-backend/pkg/models/probe_questions"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func NewAppletAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.ChainService(p)
	r.WidgetService(p)
	r.ExperimentService(p)
	return r.WebService
}

func NewAPIToolAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.APIToolService(p)
	return r.WebService
}

func NewApplicationAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.ApplicationService(p)
	r.AssistantService(p)
	r.ExternalAppService(p)
	r.ChainTemplateService(p)
	return r.WebService
}

func NewGeneralLLMAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.GlobalLLMService(p)
	// 其他通用任务
	return r.WebService
}

func NewPortalInfoAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.PortalInfo(p)
	// 其他通用任务
	return r.WebService
}

type Resource struct {
	*restful.WebService
	ea *dao.ExternalAPPDB
}

func (r *Resource) GlobalLLMService(root string) {
	projectIDQueryParam := r.QueryParameter("project_id", "项目ID").Required(true)
	r.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	// READ APIs
	tags := []string{"应用仓库-全局服务"}
	r.Route(r.POST("/projects-llm/prompt_optimize").
		To(r.OptimizePrompt).Doc("提示词优化").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.PromptToOptimize{}).Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.OptimizedPrompt{}))

	r.Route(r.GET("/projects-llm").
		To(r.GetLLMModelByProjectID).Doc("获取模型信息").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.LLMBasicConfig{}))

	r.Route(r.PUT("/projects-llm").To(r.CreateOrUpdateLLMModel).
		Doc("创建或更新模型").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AllModelsForAgent{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.POST("/projects-llm/agent_create").
		To(r.CreateAgentByProjectID).Doc("创建Agent").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AutoCreatedAgentParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletAssistant{}))

	r.Route(r.POST("/project-llm/agent_create/one_more_example").
		To(r.CreateOneMoreExample).Doc("创建Agent的更多引导示例").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(models.AutoCreatedAgentParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), ""))

	r.Route(r.POST("/projects-llm/image_gen").
		To(r.ImageGen).Doc("生成图片").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).Reads(models.ImageGenParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ImageGenResult{}))

	r.Route(r.POST("/projects-llm/probe").
		To(r.Probe).Doc("生成追问问题").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(probe_questions.ProbeQuestionsParam{}).Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), probe_questions.ProbeQuestionsParam{}))
}

func (r *Resource) ChainService(root string) {
	r.Path(root).Consumes(restful.MIME_JSON, "text/event-stream").Produces(restful.MIME_JSON, "text/event-stream")
	// READ APIs
	tags := []string{"应用仓库-应用链"}
	r.Route(r.GET("/chains").To(r.ListChains).
		Doc("查询应用链列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.AppletChainBaseDO{}))

	// r.Route(r.GET("/chains/-/templates").To(r.ListChainTemplates).
	//	Doc("获取系统内置的应用链模板列表").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.Base{}))

	r.Route(r.GET("/chains/{id}").To(r.GetChain).
		Doc("获取指定应用链详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用链ID")).
		Param(r.QueryParameter(helper.QueryParamUpgrade, helper.UrlQueryValueTrue)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletChainDO{}))

	r.Route(r.GET("/chains/{id}/deploy-cfg").To(r.GetChainDeployCfg).
		Doc("获取指定应用链详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ChainDeployCfg{}))

	r.Route(r.DELETE("/chains/{id}").To(r.DeleteChain).
		Doc("删除指定应用链").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/chains:batch-delete").To(r.BatchDelete).
		Doc("批量删除应用链").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.BatchDeleteChainParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/chains").To(r.CreateChain).
		Doc("创建应用链应用链（基础必填信息）").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.CreatAppletChainParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.POST("/chains:generate-code").To(r.GenerateCode).
		Doc("生成应用链代码").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(code_service.GenerateCodeRequest{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), code_service.GenerateCodeResponse{}))

	r.Route(r.POST("/chains:test-code").To(r.TestCode).
		Doc("测试代码").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(code_service.TestCodeRequest{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), code_service.TestCodeResponse{}))

	r.Route(r.PATCH("/chains/{id}/base-info").To(r.UpdateChainBasic).
		Doc("更新应用链定义（基础信息）").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.AppletChainBaseDO{}).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.PATCH("/chains/{id}/node-info").To(r.UpdateChainDetail).
		Doc("更新应用链定义（算子编排数据）").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(widgets.Chain{}).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	// r.Route(r.PATCH("/chains/{id}/dialog-info").To(nil).
	//	Doc("更新应用链定义（算子编排数据）").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(widgets.Chain{}).
	//	Param(r.PathParameter("id", "应用链ID")).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.PATCH("/chains/{id}/debug-info").To(r.UpdateChainDebugInfo).
		Doc("更新应用链调试信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.ChainDebugInfo{}).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	// r.Route(r.POST("/chains/-/templates:fork").To(r.ForkChainFromTemplate).
	//	Doc("应用链模板保存为我的应用链").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(helper.ID{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	// // 分享为模板
	// r.Route(r.POST("/chains/{id}/fork-as-templates").To(r.SaveAsTemplate).
	//	Doc("应用链分享为模板").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(models.ForkChainToTemplateReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	// 复制到project
	r.Route(r.POST("/chains/clone").To(r.CopyToProject).
		Doc("复制到project").Metadata(restfulspec.KeyOpenAPITags, tags).
		Metadata("examine", "applet_share").
		Reads(models.ForkChainToProjectReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	r.Route(r.GET(fmt.Sprintf("/labels")).To(r.ListLabels).
		Doc("获取所有可用的标签").Metadata(restfulspec.KeyOpenAPITags, tags).
		Writes([]models.LabelGroupsDTO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.LabelGroupsDTO{}))

	r.Route(r.POST(fmt.Sprintf("/labels")).To(r.SaveLabels).
		Doc("保存标签").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.LabelGroupsDTO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))

	r.Route(r.GET("/chains/{id}/script").To(r.GenerateTickScript).
		Doc("获取指定应用链的脚本描述").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用链ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.GetTickScriptResp{}))

	// 运行（执行一次）
	r.Route(r.POST("/chains:run").To(r.RunChain).
		Doc("运行").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(models.ChainRunReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ChainRunRes{}))

	// 调用一次事件触发器
	r.Route(r.POST("/chains:trigger").To(r.TriggerChain).
		Doc("触发一次服务调用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(models.TriggerReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	// 调试 = 注册脚本 + 执行一次 + 删除脚本
	r.Route(r.POST("/chains:debug").To(r.DebugChain).
		Doc("debug = (注册脚本 + 执行一次 + 删除脚本)").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.ChainDebugReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ChainRunRes{}))

	// 列出应用链的调试历史的基本信息
	r.Route(r.GET("/chains/{id}/debug-histories").To(r.ListChainDebugHistory).
		Doc("获取某个链的调试记录").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppletID, "应用链ID")).
		Param(r.QueryParameter("debug_name", "名字")).
		Param(r.QueryParameter("from_time", "时间范围开始")).
		Param(r.QueryParameter("to_time", "时间范围结尾")).
		Param(r.QueryParameter("states", "调试状态: 可多选: success(成功), failed(失败), canceled(终止)")).
		Param(r.QueryParameter("page_size", "页长, 为空返回所有数据")).
		Param(r.QueryParameter("page", "页码, 默认1")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []applet.ChainDebugHistoryRsp{}))

	// 列出调试记录的基本信息
	r.Route(r.GET("/chains/{id}/debug-histories/{run_id}").To(r.GetDebugHistoryByChatID).
		Doc("获取某个链的某条调试记录详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppletID, "应用链ID")).
		Param(r.PathParameter(helper.PathParamAppletRunID, "对话ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), applet.ResponseInDebugMsg{}))

	// 列出调试记录的基本信息
	r.Route(r.DELETE("/chains/{id}/debug-histories/{run_id}").To(r.DeleteDebugHistoryByChatID).
		Doc("删除某个链的某条调试记录详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppletID, "应用链ID")).
		Param(r.PathParameter(helper.PathParamAppletRunID, "对话ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST(fmt.Sprintf(helper.BackendCallbackCancelFormat,
		helper.PathParamAppletRunID)).To(r.CancelChat).
		Doc("取消（cancel）对话后需要将debugMessage写入调试历史记录中").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(debug.OverallDebugMessage{}).
		Param(r.PathParameter(helper.PathParamAppletRunID, "对话ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	// 指定调试id，获取应用链快照
	r.Route(r.GET("/chains/{id}/snapshots/{snap_id}").To(r.GetChainSnapshot).
		Doc("获取某个链的某个快照定义").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppletID, "应用链ID")).
		Param(r.PathParameter(helper.PathParamAppletSnapID, "快照ID（对话ID）")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletChainDO{}))

	// 接口弃用，已经改用 SSE 推送消息，不需要监听 mqtt 了
	//// 日志
	//r.Route(r.GET("/chains/{id}/runs/{run_id}/logs").To(r.ConsumerLogs).
	//	Doc("查看日志").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(r.PathParameter(helper.PathParamAppletID, "应用ID")).
	//	Param(r.PathParameter(helper.PathParamAppletRunID, "调试、或运行id")).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []*debug.DebugMessage{}))

	r.Route(r.GET("/chains/{id}/runs/{run_id}/nodes/{node_id}/logs").To(r.ConsumerNodeLogs).
		Doc("查看日志").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppletID, "应用ID")).
		Param(r.PathParameter(helper.PathParamAppletRunID, "调试、或运行id")).
		Param(r.PathParameter(helper.PathParamAppletNodeID, "算子节点id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.ChainDebugLog{}))

	r.Route(r.GET("/chains/-/data-types").To(r.ListDataTypeDesc).
		Doc("查询传输类型具体信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[widgets.WidgetParamDataType]widgets.DataTypeDesc{}))

	r.Route(r.GET("/chains/-/mode-types").To(r.ListModeTypeDesc).
		Doc("查询传输方式具体信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[widgets.WidgetParamModeType]widgets.ModeTypeDesc{}))
	// 调试 = 注册脚本 + 执行一次 + 删除脚本
	// // 停止问答
	// r.Route(r.POST("/chains/{id}/runs/{run_id}:stop").To(r.GetChain).
	//	Doc("停止问答").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))
}

func (r *Resource) WidgetService(root string) {
	tags := []string{"应用仓库-应用链算子管理"}

	r.Route(r.GET(fmt.Sprintf("/widget-groups")).To(r.ListWidgetGroups).
		Doc("获取所有可用算子分组列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Writes([]models.WidgetGroup{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.WidgetGroup{}))

	r.Route(r.GET(fmt.Sprintf("/widget-groups/text")).To(r.ListWidgetGroupsAsText).
		Doc("获取所有可用算子分组列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Writes(models.WidgetGroupText{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.WidgetGroupText{}))

	r.Route(r.GET(fmt.Sprintf("/dynamic_widgets/{%s}",
		helper.PathParamDynamicWidgetType)).To(r.ListDynamicWidget).
		Doc("获取动态算子列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamDynamicWidgetType, "动态算子类型")).
		Reads(models.GetDynamicWidgetReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.DynamicWidgetDesc{}))
	// 动态算子
	r.Route(r.GET(fmt.Sprintf("/dynamic_widgets/{%s}/{%s}",
		helper.PathParamDynamicWidgetType, helper.PathParamDynamicWidgetKey)).To(r.GetDynamicWidget).
		Doc("获取动态算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamDynamicWidgetType, "动态算子类型")).
		Param(r.PathParameter(helper.PathParamDynamicWidgetKey, "动态算子key值")).
		Reads(models.GetDynamicWidgetDefineReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), widgets.Widget{}))

	// // deprecated 创建并启动自定义算子
	// r.Route(r.POST("/widgets/custom_widget/register").To(r.RegisterCustomWidget).
	//	Doc("注册自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(models.CreateCustomWidgetReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []helper.ID{}))

	r.Route(r.POST("/custom-widgets").To(r.CreateCustomWidget).
		Doc("创建自定义算子,并指定是否立即启动").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(models.CreateCustomWidgetReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.CustomWidgetDO{}))

	r.Route(r.PATCH("/custom-widgets").To(r.UpdateCustomWidget).
		Doc("更新指定id的自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(models.UpdateCustomWidgetReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.CustomWidgetDO{}))

	// 目前的操作都是在特定项目下，避免混淆
	// r.Route(r.GET("/custom-widgets").To(r.ListAllCustomWidgets).
	//	Doc("查询所有自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.CustomWidgetDO{}))

	r.Route(r.POST("/custom-widgets:search").To(r.ListCustomWidgets).
		Doc("查询当前项目下特定的自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(dao.CustomWidgetQueryParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.CustomWidgetDO{}))

	r.Route(r.DELETE("/custom-widgets").To(r.DeleteCustomWidget).
		Doc("删除指定的自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.CustomWidgetDO{}))

	r.Route(r.POST("/custom-widgets:start").To(r.StartCustomWidget).
		Doc("启动指定的自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.CustomWidgetDO{}))

	r.Route(r.POST("/custom-widgets:stop").To(r.StopCustomWidget).
		Doc("停止指定的自定义算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.CustomWidgetDO{}))

	r.Route(r.GET("/custom-widgets/labels").To(r.ListCustomWidgetLabels).
		Doc("查询当前项目下自定义算子的所有标签").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.LabelGroups{}))

	r.Route(r.POST("/test-widgets").To(r.UpsertTestWidget).
		Doc("创建或更新测试算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(widgets.Widget{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.DELETE("/test-widgets").To(r.CleanTestWidget).
		Doc("清空测试算子").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))
}

func (r *Resource) ExperimentService(root string) {
	tags := []string{"应用仓库-应用体验管理（deprecated）"}
	// // 应用体验相关 Deprecated
	// r.Route(r.POST("/applet-experiments").To(r.CreateExperiment).
	//	Doc("发布应用体验,如果已存在则更新").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(models.PublishExperimentReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))
	//
	// // Deprecated
	// r.Route(r.DELETE("/applet-experiments").To(r.CancelExperiment).
	//	Doc("取消发布指定的应用体验").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(helper.ID{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))

	r.Route(r.POST("/applet-experiments:create").To(r.CreateExperiment).
		Doc("创建应用体验").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(models.CreateExperimentReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))

	r.Route(r.POST("/applet-experiments:publish").To(r.PublishExperiment).
		Doc("将体验发布,对体验进行部署").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))

	r.Route(r.POST("/applet-experiments:cancel").To(r.CancelExperiment).
		Doc("取消体验发布,取消部署").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))

	r.Route(r.DELETE("/applet-experiments").To(r.DeleteExperiment).
		Doc("删除对应的体验").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))

	r.Route(r.GET("/applet-experiments/labels").To(r.ListExperimentLabels).
		Doc("查询当前项目应用体验的所有标签").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.LabelGroupsDTO{}))

	r.Route(r.POST("/applet-experiments:search").To(r.ListExperiment).
		Doc("获取当前项目下的应用体验").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Reads(dao.ExperimentQueryParam{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.AppletExperimentDO{}))

	// 统计应用链整体状态
	r.Route(r.GET("/stats").To(r.Stats).
		Doc("应用链状态").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ChainState{}))

	// 前端入参转换费应用链服务的入参
	r.Route(r.POST("/chains/params/transfer").To(r.GetChainExecuteParam).
		Doc("前端调试入参转换为应用链服务的入参").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(models.ChainDebugReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ChainRunRes{}))

	// 应用链服务的入参
	r.Route(r.GET("/chains/{id}/params").To(r.GetChainExecuteParamTemplate).
		Doc("前端调试入参转换为应用链服务的入参").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter("id", "应用ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ChainRunRes{}))

	// 动态下拉框参数
	r.Route(r.GET("/widgets/datasource").To(r.ListDynamicDatasource).
		Param(r.QueryParameter("key", "动态数据源类型")).
		Doc("动态datasource").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.DynamicDatasource{}))

	//// 动态算子
	//r.Route(r.POST("/dynamic_input_widgets/input_aggregation").To(r.GetDynamicInputWidget).
	//	Doc("输入聚合").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Reads(models.GetDynamicInputWidgetDefineReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), widgets.Widget{}))

	//// 更新调试状态
	//r.Route(r.PUT("/chains/{id}/chats/{chat_id}/state").To(r.UpdateChatDebugState).
	//	Doc("更新调试状态").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(r.PathParameter("id", "应用链id")).
	//	Param(r.PathParameter("chat_id", "本次对话ID")).
	//	Reads(models.UpdateDebugStateReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}))

	// 获取全局搜索数据
	r.Route(r.GET("/models/-/search-data").To(r.GetModelSearchData).
		Doc("全局搜索应用仓库的数据").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter("project_id", "项目ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.Group{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	// // 智能体相关
	// ws.Route(ws.GET("/agent_definition/prompt-examples").To(r.PromptExamples).
	//	Doc("获取提示词示例").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.PromptInfo{}))
	//
	// ws.Route(ws.GET("/agent_definition/question-examples").To(r.QuestionExamples).
	//	Doc("获取查询问题示例").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), []string{}))
	//
	// ws.Route(ws.POST("/agent_definition:debug").To(r.DebugAgent).
	//	Doc("智能体调试").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(models.AgentDebugReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), struct{}{}))
	//
	// ws.Route(ws.POST("/agent_definition:create").To(r.TODO).
	//	Doc("将调式好的智能体保存").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(models.AgentCreateReq{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))
	//
	// ws.Route(ws.POST("/agent_definition:publish").To(r.TODO).
	//	Doc("将保存的智能体发布-部署,== 体验的发布、部署").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(helper.ID{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AppletExperimentDO{}))
	//
	// ws.Route(ws.POST("/agent_definition/collection:used").To(r.TODO).
	//	Doc("查询工具集是否在智能体中被引用到").Metadata(restfulspec.KeyOpenAPITags, tags).
	//	Param(ws.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
	//	Reads(helper.BatchIDs{}).
	//	Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]bool{}))

	// return ws
}
