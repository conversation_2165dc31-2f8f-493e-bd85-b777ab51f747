package applet

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"

	"github.com/aws/smithy-go/ptr"
	"github.com/emicklei/go-restful/v3"
	_ "github.com/google/uuid"
	_ "github.com/manucorporat/sse"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	_ "transwarp.io/applied-ai/applet-backend/core/applet_log"
	"transwarp.io/applied-ai/applet-backend/core/global_llm"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/probe_questions"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func (r *Resource) GenerateTickScript(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	script, err := applet.ChainManager.GenerateTickScriptV2(ctx, chainID, nil)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, models.GetTickScriptResp{Script: script})
}

func (r *Resource) GetChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	chain, err := applet.ChainManager.GetChainByID(ctx, chainID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// TODO  upgradeChainDetail
	_, err = applet.ChainDeployManager.GetSimpleState(ctx, chainID)
	if err != nil {
		// 查询不到相关的部署信息
		chain.Base.Published = false
	} else {
		chain.Base.Published = true
	}
	// 打点
	AsyncUpdateChainMetrics(chainID, applet.ChainMetricsTypeVisit)
	helper.SuccessResponse(response, chain)
}

func (r *Resource) GetChainDeployCfg(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	cfg, err := applet.ChainManager.GetDeployCfg(ctx, chainID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, cfg)
}

// UpdateChainBasic 更新算子基础信息
func (r *Resource) UpdateChainBasic(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	chainBasic := &models.AppletChainBaseDO{}
	request.ReadEntity(chainBasic)
	err := applet.ChainManager.UpdateChainBasic(ctx, chainID, chainBasic)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: chainID})
}

// UpdateChainDetail 更新算子编排信息
func (r *Resource) UpdateChainDetail(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	chainDetail := &widgets.Chain{}
	if err := request.ReadEntity(chainDetail); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := applet.ChainManager.UpdateChainDetail(ctx, chainID, chainDetail)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: chainID})
}

// UpdateChainDebugInfo 更新应用链调试信息
func (r *Resource) UpdateChainDebugInfo(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	debugInfo := &models.ChainDebugInfo{}
	request.ReadEntity(debugInfo)
	err := applet.ChainManager.UpdateChainDebugInfo(ctx, chainID, debugInfo)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: chainID})
}

func (r *Resource) DeleteChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	qry := query.Q
	if err := qry.Transaction(func(tx *query.Query) error {
		// 同步删除链和其对应的调试历史
		err := applet.ChainManager.DeleteChain(ctx, chainID)
		if err != nil {
			helper.ErrorResponse(response, err)
			return err
		}
		err = applet.ChainDebugManager.DeleteDebugHistoriesByChainID(ctx, chainID)
		if err != nil {
			helper.ErrorResponse(response, err)
			return err
		}
		return nil
	}); err != nil {
		stdlog.Errorf("delete chain err :%v", err)
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, true)
}

func (r *Resource) BatchDelete(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	IDs := &models.BatchDeleteChainParam{}
	request.ReadEntity(IDs)
	qry := query.Q
	if err := qry.Transaction(func(tx *query.Query) error {
		// 同步 批量删除链 和 这些链对应的调试历史
		err := applet.ChainManager.BatchDeleteChain(ctx, IDs.ChainIDs)
		if err != nil {
			helper.ErrorResponse(response, err)
			return err
		}
		err = applet.ChainDebugManager.BatchDeleteChainDebugHistories(ctx, IDs.ChainIDs)
		if err != nil {
			helper.ErrorResponse(response, err)
			return err
		}
		return nil
	}); err != nil {
		stdlog.Errorf("batch delete chainn err :%v", err)
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, true)
}

func (r *Resource) ListChains(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectID := helper.GetProjectID(ctx)
	param := &applet.SearchChainParam{
		ProjectID: projectID,
	}
	filterInvalid := request.QueryParameter("filter-invalid")
	if strings.ToLower(filterInvalid) == "true" {
		param.FilterEmpty = ptr.Bool(true)
		param.FilterDebugFailed = ptr.Bool(true)
	}
	chains, err := applet.ChainManager.SearchForBaseDO(ctx, param)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, chains)
}

// CreateChain 创建应用链
func (r *Resource) CreateChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	userID := auth.GetAuthContext(request).GetUsername()
	param := &models.CreatAppletChainParam{}
	if err := request.ReadEntity(param); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := helper.GetProjectID(ctx)
	ID, err := applet.ChainManager.CreateChainBasic(ctx, &models.AppletChainDO{
		Base: models.AppletChainBaseDO{
			Name:    param.Name,
			Creator: userID,
			// Type:      models.ChainTypeUserCreate,
			Desc:      param.Desc,
			Labels:    param.Labels,
			ProjectID: projectID,
			AssetType: param.AssetType,
		},
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: ID})
}

// CopyToProject 复制到其他project
func (r *Resource) CopyToProject(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	param := &models.ForkChainToProjectReq{}
	if err := request.ReadEntity(param); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectId := helper.GetProjectID(ctx)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("invalid projectId id :%v", projectId))
		return
	}
	chain, err := applet.ChainManager.GetChainByID(ctx, param.OriChainID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 变更 basic 信息
	chain.Base.ID = ""
	chain.Base.Creator = auth.GetAuthContext(request).GetUsername()
	chain.Base.ProjectID = param.TargetProjectID
	chain.Base.Name = fmt.Sprintf("%v_%v", chain.Base.Name, time.Now().Unix())
	if param.TargetChainName != "" {
		chain.Base.Name = param.TargetChainName
	}
	chain.Base.Desc = param.TargetChainDesc
	chain.Base.LastDebugState = models.ChainDebugStateInit
	chain.Base.SourceInfo = &models.ChainSourceInfo{
		SourceChainID:   param.OriChainID,
		SourceProjectID: helper.GetProjectID(ctx),
	}
	if param.RemoveOriLabel {
		chain.Base.Labels = make(map[string][]string)
	}
	newLabels := models.MergeLabels([]models.LabelGroups{chain.Base.Labels, param.TargetLabels})
	chain.Base.Labels = newLabels

	// 克隆智能助手-临时文件知识库需要重新构建
	if chain.Base.CreatedType == models.AppletTypeAssistant && chain.Base.AssistantInfo != nil {
		chain.Base.AssistantInfo.LocalFiles = nil
	}

	// 拷贝示例文件到对应空间
	if err := updateExperimentInfo(ctx, chain.Base.ExperimentInfo, param.TargetProjectID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	// 重名校验
	if err := applet.ValidChainNameForCreate(ctx, chain.Base.Name, param.TargetProjectID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	newID, err := applet.ChainManager.CreateChain(ctx, chain)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 打点
	AsyncUpdateChainMetrics(param.OriChainID, applet.ChainMetricsTypeClone)
	helper.SuccessResponse(response, helper.ID{ID: newID})
}

func (r *Resource) GetModelSearchData(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	// first get available services
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{})
	if err != nil {
		helper.ErrorResponse(response, err)
	}

	// chains convert to map
	chainDOs, err := applet.ChainManager.SearchForSimpleChainDO(ctx, &applet.SearchChainParam{ProjectID: helper.GetProjectID(ctx)})
	if err != nil {
		helper.ErrorResponse(response, err)
	}

	chainMap := make(map[string]*models.AppletChainDO)
	for _, chain := range chainDOs {
		chainMap[chain.Base.ID] = chain
	}

	// collect
	items := make([]*pb.Item, 0)
	for _, service := range appExperiServices {
		chain := chainMap[service.ID]
		if _, ok := chainMap[service.ID]; ok {
			item := &pb.Item{
				Id:   service.ID,
				Name: chain.Base.Name,
				Desc: chain.Base.Desc,
				/*Labels:    chain.Base.Labels,*/
				ProjectId: service.ProjectId,
				Module:    pb.Module_mlops,
				RscType:   pb.RscType_CHAIN,
				// delete rsc_type, add create_type
				CreateType:   service.CreatedType,
				Creator:      chain.Base.Creator,
				UpdateTime:   chain.Base.UpdateTime,
				ReleaseCount: 0,
				AssetType:    chain.Base.AssetType,
			}
			items = append(items, item)
		}
	}

	assets := &pb.Group{
		Module:  pb.Module_mlops,
		RscType: pb.RscType_CHAIN,
		Items:   items,
	}

	helper.SuccessResponse(response, assets)
}

func (r *Resource) OptimizePrompt(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectID := helper.GetProjectID(ctx)
	var inputData models.PromptToOptimize
	if err := request.ReadEntity(&inputData); err != nil {
		stdlog.Errorf("failed to read entity: %v", err)
		helper.ErrorResponse(response, err)
		return
	}

	// 获取模型配置
	var l global_llm.LLModel
	modelConfig, err := l.GetModelByProjectID(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to get model by name: %v", err)
		return
	}
	model := modelConfig.ModelsForAgent.LLMModelSvc

	// 优化prompt
	optimizer, err := clients.NewPromptOptimizer(ctx, model)
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to create prompt optimizer: %v", err)
		return
	}
	optimizedPrompt, err := optimizer.PromptOptimize(ctx, "optimize", inputData.InputPrompt)
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to optimize prompt: %v", err)
		return
	}

	optimizedPrompt = cleanRedundantOuput.ReplaceAllString(optimizedPrompt, "")
	optimizedPrompt = TrimLeadingEscapes(optimizedPrompt)
	helper.SuccessResponse(response, models.OptimizedPrompt{OptimizedPrompt: optimizedPrompt})
}

var (
	cleanRedundantOuput = regexp.MustCompile(`优化后的输入:`) // 匹配特定文本
)

// TrimLeadingEscapes 仅去除开头的转义字符，保留其他位置的格式
func TrimLeadingEscapes(s string, cutset ...string) string {
	// 默认需要去除的符集
	defaultCutset := "\n\t\r"
	if len(cutset) > 0 {
		defaultCutset = cutset[0]
	}

	for i, r := range s {
		if !strings.ContainsRune(defaultCutset, r) {
			return s[i:] // 返回第一个非转义字符后的内容
		}
	}
	return s
}

func (r *Resource) GetLLMModelByProjectID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	model, err := GetModelByProjectIDWithContext(ctx)
	if err == global_llm.ErrProjModelNotConfigured {
		helper.SuccessResponse(response, models.LLMBasicConfig{})
		return
	}
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, model)
}

func (r *Resource) CreateOrUpdateLLMModel(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	var svc models.AllModelsForAgent

	// 构造model
	if err := stdsrv.ReadEntityMixWithProto(request, &svc); err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to read entity: %v", err)
		return
	}
	if svc == *new(models.AllModelsForAgent) {
		stdlog.Errorf("Invalid input")
		helper.ErrorResponse(response, stderr.InvalidParam.Error("Invalid json or empty data"))
		return
	}
	ProjectID := helper.GetProjectID(ctx)
	if ProjectID == "" {
		stdlog.Errorf("LLM config field name can not be empty")
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("project id  can not be empty"))
		return
	}
	model := models.LLMBasicConfig{
		ProjectID: ProjectID,
		ModelsForAgent: models.AllModelsForAgent{
			LLMModelSvc:        svc.LLMModelSvc,
			EmbeddingSvc:       svc.EmbeddingSvc,
			ImageGenSvc:        svc.ImageGenSvc,
			RerankSvc:          svc.RerankSvc,
			ImageUnderstandSvc: svc.ImageUnderstandSvc,
			OcrSvc:             svc.OcrSvc,
		},
	}

	// operate MySQL
	var LLMBasicConfigDAO global_llm.LLModel
	err := LLMBasicConfigDAO.CreateOrUpdateModel(ctx, model)
	if err != nil {
		stdlog.Errorf("failed to create model: %v", err)
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: ProjectID})
}

func (r *Resource) CreateAgentByProjectID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	var agentParam models.AutoCreatedAgentParam
	if err := request.ReadEntity(&agentParam); err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to read entity: %v", err)
		return
	}
	model, err := GetModelByProjectIDWithContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if model.ModelsForAgent.LLMModelSvc == nil {
		helper.ErrorResponse(response, global_llm.ErrProjLLMModelNotConfigured)
		return
	}
	agentCreationHandler, err := global_llm.GetCreateHandler(ctx, model.ModelsForAgent.LLMModelSvc, model.ModelsForAgent.EmbeddingSvc, model.ModelsForAgent.ImageGenSvc)
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to create grpc client: %v", err)
		return
	}
	createdAgent, err := agentCreationHandler.CreateAgent(ctx, agentParam.Purpose)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, createdAgent)
}

func (r *Resource) CreateOneMoreExample(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	var agentParam models.AutoCreatedAgentParam
	if err := request.ReadEntity(&agentParam); err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to read entity: %v", err)
		return
	}
	model, err := GetModelByProjectIDWithContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	exampleResp, err := global_llm.CreateOneMoreExample(ctx, model.ModelsForAgent.LLMModelSvc, agentParam.Purpose)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, exampleResp)
}

func (r *Resource) ImageGen(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	var imageGenParam models.ImageGenParam
	if err := request.ReadEntity(&imageGenParam); err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to read entity: %v", err)
		return
	}
	model, err := GetModelByProjectIDWithContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	imageGenResp, err := global_llm.ImageGenerate(ctx, model.ModelsForAgent.ImageGenSvc, imageGenParam.Prompt)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, imageGenResp)
}

func (r *Resource) Probe(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ctx, cancel := context.WithTimeout(ctx, time.Second*60)
	defer cancel()
	// 获取ProjectID，用于获取模型
	projectID := helper.GetProjectID(ctx)
	var probeQuestionsParam probe_questions.ProbeQuestionsParam
	if err := request.ReadEntity(&probeQuestionsParam); err != nil {
		stdlog.Errorf("failed to read entity: %v", err)
		helper.ErrorResponse(response, err)
		return
	}

	// 获取模型配置
	var l global_llm.LLModel
	// 根据模型ID获取模型配置
	modelConfig, err := l.GetModelByProjectID(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to get model by name: %v", err)
		return
	}
	model := modelConfig.ModelsForAgent.LLMModelSvc

	probe, err := clients.NewProbeQuestionsUtil(ctx, model)

	// TODO
	if err != nil {
		helper.ErrorResponse(response, err)
		stdlog.Errorf("failed to create prompt optimizer: %v", err)
		return
	}
	for i := 0; i < 3; i++ {
		probeQuestion, probeErr := probe.ProbeQuestions(ctx, probeQuestionsParam)
		if probeErr != nil {
			helper.ErrorResponse(response, probeErr)
			stdlog.Errorf("failed to create probe questions: %v", probeErr)
			return
		}
		probeQuestionsParam.UserHistoryQuestions = append(probeQuestionsParam.UserHistoryQuestions, probeQuestion)
		stdsrv.SSESendData(response.ResponseWriter, "", probeQuestion)
		// helper.SuccessResponse(response, probe_questions.ProbeQuestionsResult{ProbeQuestion: probeQuestion})
	}
}

func (r *Resource) ListDataTypeDesc(request *restful.Request, response *restful.Response) {
	res := widgets.GetParamsFactory().ListDataTypeDesc()
	helper.SuccessResponse(response, res)
}
func (r *Resource) ListModeTypeDesc(request *restful.Request, response *restful.Response) {
	res := widgets.GetParamsFactory().ListModeTypeDesc()
	helper.SuccessResponse(response, res)
}

func GetModelByProjectIDWithContext(ctx context.Context) (*models.LLMBasicConfig, error) {
	ProjectID := helper.GetProjectID(ctx)
	if ProjectID == "" {
		stdlog.Errorf("imageGenParam field name can not be empty")
		err := fmt.Errorf("empty Project ID")
		return nil, err
	}
	var LLMBasicConfigDAO global_llm.LLModel
	model, err := LLMBasicConfigDAO.GetModelByProjectID(ctx, ProjectID)
	if err != nil {
		stdlog.Errorf("failed to get model by ID: %v", err)
		return nil, err
	}
	return model, nil
}
func (r *Resource) TODO(request *restful.Request, response *restful.Response) {
}

type RespWriterFlusher interface {
	http.ResponseWriter
	http.Flusher
}

type FlushWriter struct {
	w RespWriterFlusher
}

func NewFlushWriter(w RespWriterFlusher) *FlushWriter {
	return &FlushWriter{w: w}
}

func (f *FlushWriter) Header() http.Header {
	return f.w.Header()
}

func (f *FlushWriter) Write(bytes []byte) (int, error) {
	nw, er := f.w.Write(bytes)
	if er != nil {
		return nw, er
	}
	f.w.Flush()
	return nw, er
}

func (f *FlushWriter) WriteHeader(statusCode int) {
	f.w.WriteHeader(statusCode)
	f.w.Flush()
}

func (r *Resource) Mock(request *restful.Request, response *restful.Response) {

}
func updateExperimentInfo(ctx context.Context, experimentInfo *models.ExperimentInfo, tProjectId string) error {
	if helper.GetProjectID(ctx) == tProjectId {
		return nil
	}
	if experimentInfo == nil || len(experimentInfo.MultimodalExamples) == 0 {
		return nil
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return err
	}
	tTenantId, err := stdsrv.GetProjectTenantUid(tProjectId, token)
	if err != nil {
		return stderr.Wrap(err, "get tenant id")
	}

	prefixFormat := regexp.MustCompile(`sfs:///tenants/([^/]+)/projs/([^/]+)`)
	for _, example := range experimentInfo.MultimodalExamples {
		if example.File == "" {
			continue
		}
		//  sfs:///tenants/a1/projs/b1/1.txt ->  sfs:///tenants/a1/projs/b2/1.txt
		temp := prefixFormat.ReplaceAllString(example.File, fmt.Sprintf("sfs:///tenants/%s/projs/%s", tTenantId, tProjectId))

		oriSfsUrl, err := stdfs.NewRelativeFilePath(example.File)
		if err != nil {
			return err
		}

		tarSfsUrl, err := stdfs.NewRelativeFilePath(temp)
		if err != nil {
			return err
		}
		err = utils.OverwriteFile(oriSfsUrl.ToAbsFilePath(), tarSfsUrl.ToAbsFilePath())
		if err != nil {
			return stderr.Wrap(err, "overwrite file")
		}
		example.File = tarSfsUrl.ToSFSFilePath()
	}
	return nil
}
