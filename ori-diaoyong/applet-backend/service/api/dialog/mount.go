package dialog

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"path"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

type Resource struct {
	*restful.WebService
}

func NewDialogAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.DialogService(p)
	return r.WebService
}

func (r *Resource) DialogService(root string) {

	r.Path(root).Consumes(restful.MIME_JSON, "text/event-stream").Produces(restful.MIME_JSON, "text/event-stream")
	// READ APIs
	tags := []string{"应用仓库-对话记录"}

	r.Route(r.GET("/applications/{app_id}/chats/{chat_id}").To(r.GetAppChat).
		Doc("根据app_id 和 chat_id获取单个对话内的历史记录").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppID, "应用id")).
		Param(r.PathParameter(helper.PathParamAppletChatID, "对话id")).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Param(r.QueryParameter("page", "页数")).
		Param(r.QueryParameter("page_size", "页大小")).
		Param(r.QueryParameter("is_desc", "排序是否降序")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), &pb.MessageRes{}))

	r.Route(r.GET("/applications/chats").To(r.GetAppChats).
		Doc("获取所有会话").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.PathParamAppID, "应用链id")).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Param(r.QueryParameter("page", "页数")).
		Param(r.QueryParameter("page_size", "页大小")).
		Param(r.QueryParameter("is_desc", "排序是否降序")).
		Param(r.QueryParameter("content", "搜索关键字(模糊匹配)")).
		Param(r.QueryParameter("start_time", "[开始时间]startTime").DataType("integer")).
		Param(r.QueryParameter("end_time", "[结束时间]endTime").DataType("integer")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DialogRes{}))

	r.Route(r.DELETE("/applications/{app_id}/chats/{chat_id}").To(r.DeleteAppChat).
		Doc("根据 chat_id 清空单条历史记录").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppID, "应用id")).
		Param(r.PathParameter(helper.PathParamAppletChatID, "对话id").Required(true)).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), new(string)))

	r.Route(r.GET("/applications").To(r.GetDialogApps).
		Doc("根据project_id获取所有应用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), &pb.DialogAppRes{}))

	r.Route(r.PUT("/applications/{app_id}").To(r.AddDialogApp).
		Doc("根据project_i添加应用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppID, "应用链id").Required(true)).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), new(int64)))

	r.Route(r.DELETE("/applications/{app_id}").To(r.DeleteDialogApp).
		Doc("根据project_id删除应用链").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(r.PathParameter(helper.PathParamAppID, "应用链id").Required(true)).
		Param(r.QueryParameter(helper.QueryParamProjectID, "项目id").Required(true)).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), new(int64)))

	r.Route(r.POST("/applications:downloads").To(r.DownloadDialogs).
		Doc("下载对话").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(pb.DownloadDialogReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DownloadDialogRes{}))

}
