package knowledge_base

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/emicklei/go-restful/v3"
	"github.com/google/uuid"
	"github.com/manucorporat/sse"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	pclents "transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func (r *Resource) ListPopularKnowledgeBases(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	resp, err := r.kbm.ListKnowledgeBases(ctx, &pb.ListKnowledgeBasesReq{UserContext: pbUserCtx})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	limit, err := helper.LimitQP.GetIntValueWithDefault(request, helper.DefaultLimits)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	sortBy := helper.SortByQP.GetValue(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	KnowledgeBaseInfos := resp.Result
	switch sortBy {
	case pb.SortBy_Sort_By_Clone_Times.String():
		sort.Slice(KnowledgeBaseInfos, func(i, j int) bool {
			return KnowledgeBaseInfos[i].KnowledgeBase.MetricsInfo.CloneTimes > KnowledgeBaseInfos[j].KnowledgeBase.MetricsInfo.CloneTimes
		})
	case pb.SortBy_Sort_By_Execute_Times.String():
		sort.Slice(KnowledgeBaseInfos, func(i, j int) bool {
			return KnowledgeBaseInfos[i].KnowledgeBase.MetricsInfo.ExecuteTimes > KnowledgeBaseInfos[j].KnowledgeBase.MetricsInfo.ExecuteTimes
		})
	default: // case pb.SortBy_Sort_By_Visit_Times.String():
		sort.Slice(KnowledgeBaseInfos, func(i, j int) bool {
			return KnowledgeBaseInfos[i].KnowledgeBase.MetricsInfo.VisitTimes > KnowledgeBaseInfos[j].KnowledgeBase.MetricsInfo.VisitTimes
		})
	}
	if len(KnowledgeBaseInfos) > limit {
		resp.Result = KnowledgeBaseInfos[:limit]
	}
	helper.SuccessResponse(response, resp.Result)
}

func (r *Resource) ListKnowledgeBases(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	cts := request.QueryParameter(helper.QueryParamContentTypeSelector)
	sts := request.QueryParameter(helper.QueryParamSourceTypeSelector)
	rts := request.QueryParameter(helper.QueryParamRegistryTypeSelector)
	ips := request.QueryParameter(helper.QueryParamIsPublishedSelector)
	cs := request.QueryParameter(helper.QueryParamCreatorSelector)
	ls := request.QueryParameter(helper.QueryParamLabelSelector)
	sceneTypeSelector := request.QueryParameter(helper.QueryParamSceneTypeSelector)
	isPublic := strings.ToLower(request.QueryParameter(helper.QueryParamIsPublic)) == "true"

	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req := &pb.ListKnowledgeBasesReq{
		UserContext: pbUserCtx,
		ListSelector: &pb.ListKnowledgeBasesSelector{
			ContentTypes:  helper.StringToEnumSlice[pb.KnowledgeBaseContentType](cts, pb.KnowledgeBaseContentType_value),
			SourceTypes:   helper.StringToEnumSlice[pb.KnowledgeBaseSourceType](sts, pb.KnowledgeBaseSourceType_value),
			RegistryTypes: helper.StringToEnumSlice[pb.KnowledgeBaseRegistryType](rts, pb.KnowledgeBaseRegistryType_value),
			SceneTypes:    helper.StringToEnumSlice[pb.KnowledgeBaseSceneType](sceneTypeSelector, pb.KnowledgeBaseSceneType_value),
			IsPublished:   helper.StringToBoolSlice(ips),
			Creators: func() []string {
				if cs != "" {
					return strings.Split(cs, ",")
				} else {
					return []string{}
				}
			}(),
			Labels: helper.String2MapValues(ls),
		},
		IsPublic: isPublic,
	}
	rsp, err := r.kbm.ListKnowledgeBases(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	req := &pb.GetKnowledgeBaseReq{
		UserContext: pbUserCtx,
		Id:          id,
	}
	rsp, err := r.kbm.GetKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	AsyncUpdateKBMetrics(id, knowledge_base.KbMetricsTypeVisit)
	helper.SuccessResponse(response, rsp.Result)
}

func (r *Resource) CreateKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.KnowledgeBase](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	rsp, err := r.kbm.CreateKnowledgeBase(ctx, &pb.CreateKnowledgeBaseReq{
		UserContext: pbUserCtx,
		Base:        req,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp.Result)
}

func (r *Resource) UpdateKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	kb, err := helper.ReadEntity[pb.KnowledgeBase](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kb.Id = id
	rsp, err := r.kbm.UpdateKnowledgeBase(ctx, &pb.UpdateKnowledgeBaseReq{
		UserContext: pbUserCtx,
		Base:        kb,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp.Result)
	return
}

func (r *Resource) DeleteKnowledgeBases(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	_, err = r.kbm.DeleteKnowledgeBases(ctx, &pb.DeleteKnowledgeBasesReq{
		UserContext: pbUserCtx,
		Ids:         []string{id},
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListConnectionDetails(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	dc, err := helper.ReadEntity[pb.DataConnection](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	rsp, err := r.kbm.ListConnectionTables(ctx, &pb.ListConnectionTablesReq{
		UserContext: pbUserCtx,
		Connection:  dc,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	var ret []*models.TableInfo

	if len(rsp.Tables) > 0 {
		for _, t := range rsp.Tables {
			ti := new(models.TableInfo)
			err = ti.FromPb(t)
			if err != nil {
				helper.ErrorResponse(response, err)
				return
			}
			ret = append(ret, ti)
		}
	} else {
		for _, desc := range rsp.EsIndices {
			ti := new(models.TableInfo)
			ti.ESDescription = desc
			ret = append(ret, ti)
		}
	}

	helper.SuccessResponse(response, ret)
}

func (r *Resource) ListDocuments(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	ls := request.QueryParameter(helper.QueryParamLabelSelector)
	name := request.QueryParameter("name")
	pageReq, err := getPageReq(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	rsp, err := r.kbm.ListDocumentsByPage(ctx, &pb.ListDocumentsByPageReq{
		UserContext:     pbUserCtx,
		KnowledgeBaseId: id,
		PageReq:         pageReq,
		Labels:          helper.String2MapValues(ls),
		Name:            name,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) DisableDocument(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	docId := request.QueryParameter(helper.QueryParamDocId)

	_, err = r.kbm.DisableDocument(ctx, &pb.DisableDocumentReq{
		UserContext:     pbUserCtx,
		KnowledgeBaseId: id,
		DocId:           docId,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListDocumentChunks(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	docId := request.PathParameter(helper.PathParamDocId)
	pageReq, err := getPageReq(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	var sts []pb.ChunkSourceType
	stsStr := request.QueryParameter(helper.QueryParamSourceTypeSelector)
	if stsStr != "" {
		for _, s := range strings.Split(stsStr, ",") {
			sts = append(sts, pb.ChunkSourceType(pb.ChunkSourceType_value[s]))
		}
	}

	rsp, err := r.kbm.ListDocumentChunks(ctx, &pb.ListDocumentChunksReq{
		UserContext:        pbUserCtx,
		KnowledgeBaseId:    id,
		DocId:              docId,
		PageReq:            pageReq,
		SourceTypeSelector: sts,
		SearchContent:      request.QueryParameter(helper.QueryParamSearchContent),
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) RetrieveKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.RetrieveKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	qKbId := request.QueryParameter(helper.QueryParamKnowledgeBaseID)
	if qKbId != "" {
		req.KnowledgeBaseId = qKbId
	}

	req.UserContext = pbUserCtx
	rsp, err := r.kbm.RetrieveKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	AsyncUpdateKBMetrics(req.KnowledgeBaseId, knowledge_base.KbMetricsTypeExecute)
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetDocumentTree(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)

	rsp, err := r.kbm.GetDocumentTree(ctx, &pb.GetDocumentTreeReq{
		UserContext:     pbUserCtx,
		KnowledgeBaseId: id,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp.Tree)
}

func (r *Resource) ListDocumentTrees(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	idsParam := request.QueryParameter(helper.QueryParamIds)
	ids := strings.Split(idsParam, ",")

	m := sync.Map{}
	call := func(id string) error {
		rsp, err := r.kbm.GetDocumentTree(ctx, &pb.GetDocumentTreeReq{
			UserContext:     pbUserCtx,
			KnowledgeBaseId: id,
		})
		if err != nil {
			stdlog.Warnf("ListDocumentTrees [%s]: %v", id, err)
		} else {
			m.Store(id, rsp.Tree)
		}
		return nil
	}
	err = stdsrv.SyncBatchCall(ids, call)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	ret := make([]*models.KnowledgeBaseTree, 0)
	m.Range(func(key, value interface{}) bool {
		ret = append(ret, &models.KnowledgeBaseTree{
			Id:   key.(string),
			Tree: value.(*pb.DocumentTree),
		})
		return true
	})
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Id < ret[j].Id
	})

	helper.SuccessResponse(response, ret)
}

func (r *Resource) SubmitFileToKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.SubmitFileToKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req.UserContext = pbUserCtx
	rsp, err := r.kbm.SubmitFileToKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp.Doc)
}

func (r *Resource) RemoveFileFromKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.RemoveFileFromKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.RemoveFileFromKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) UpdateKnowledgeBaseState(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.UpdateKnowledgeBaseStateReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	rsp, err := r.kbm.UpdateKnowledgeBaseState(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ListTkhRepos(request *restful.Request, response *restful.Response) {
	if !conf.Config.TKHConfig.Enabled {
		helper.ErrorResponse(response, knowledge_base.TkhNotEnabledErr)
		return
	}
	ctx := context.Background()
	req := clients.TKHClient.BaseApi.ListAllDocTreesUsingGET(ctx)
	res, _, err := req.Execute()
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "TKH api ListUserDocTreesUsingGET"))
		return
	}
	helper.SuccessResponse(response, res.Data)
}

func (r *Resource) CreateChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.CreateChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.CreateChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) CreateAugmentChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.CreateAugmentChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.CreateAugmentChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) DeleteChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.DeleteChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.DeleteChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) DeleteAugmentChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.DeleteAugmentChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.DeleteAugmentChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) UpdateChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.UpdateChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.UpdateChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) UpdateAugmentChunk(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.UpdateAugmentChunkReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.UpdateAugmentChunk(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) PreviewDocumentProcess(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.PreviewDocumentProcessReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.PreviewDocumentProcess(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) SyncSubmitFilesToKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.SyncSubmitFilesToKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	response.Header().Set("Access-Control-Allow-Origin", "*")
	response.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	response.Header().Set("Content-Type", "text/event-stream")
	response.Header().Set("Cache-Control", "no-cache")
	response.Header().Set("Connection", "keep-alive")
	response.Flush()

	ch, err := r.kbm.SyncSubmitFilesToKnowledgeBaseMain(ctx, req)
	if err != nil {
		helper.ErrorSSEResponse(response, err)
		return
	}
	for msg := range ch {
		bs, err := stdsrv.DefaultProtoJsonAccessor().Marshal(msg)
		if err != nil {
			stdlog.Errorf("proto json marshal err: %v", err)
		}
		err = sse.Encode(response, sse.Event{
			Id:    uuid.New().String(),
			Event: "message",
			Data:  string(bs),
		})
		if err != nil {
			stdlog.Errorf("encode msg err: %v", err)
		}
		response.Flush()
	}
	stdlog.Infof("SyncSubmitFilesToKnowledgeBase connection closed")
}

func (r *Resource) RetryDocumentProcess(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.RetryDocumentProcessReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	rsp, err := r.kbm.RetryDocumentProcess(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) RetrieveCrossKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.RetrieveCrossKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	istioMode := false
	istioModeStr := request.QueryParameter("istio_mode")
	istioMode, err = strconv.ParseBool(istioModeStr)
	if err != nil {
		istioMode = false
	}
	if istioMode {
		req.IstioMode = istioMode
	}
	rsp, err := r.kbm.RetrieveCrossKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) IsDocumentExistent(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.IsDocumentExistentReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	rsp, err := r.kbm.IsDocumentExistent(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) Stats(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	rsp, err := knowledge_base.GetKbsStats(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) StatsStore(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	// 默认返回存储开销前十的知识库
	rsp, err := r.kbm.CollectKnowledgeBaseStoreStats(helper.GetProjectID(ctx), 10, false)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ShareKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.ShareKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	baseId := request.PathParameter(helper.PathParamKnowledgeBaseID)
	if req.KnowledgeBaseId == "" {
		req.KnowledgeBaseId = baseId
	}
	if req.KnowledgeBaseId != baseId || req.KnowledgeBaseId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("knowledge base id error"))
		return
	}

	req.UserContext = pbUserCtx
	rsp, err := r.kbm.ShareKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) PublishKnowledgeBase(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, err)
		}
	}()
	kbsId := kbsIdPP.GetValue(request)
	if kbsId == "" {
		err = stderr.InvalidParam.Error("knowledge base id can not be empty")
		return
	}
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return
	}
	publishKbsReq := new(pb.PublishKnowledgeBaseReq)
	err = request.ReadEntity(publishKbsReq)
	if err != nil {
		err = stderr.Wrap(err, "failed to read param publish knowledge base rquest")
		return
	}
	publishKbsReq.Id = kbsId
	publishKbsReq.Ctx = pbUserCtx
	rsp, err := r.kbm.PublishKnowledgeBase(ctx, publishKbsReq)
	if err != nil {
		err = stderr.Wrap(err, "failed to publish knowledge base")
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) SubmitChunksToKnowledgeBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.SubmitChunksToKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
}

func (r *Resource) GetDefaultPrompts(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	var ret *models.KnowledgeEnhancePrompts
	if helper.IsChinese(ctx) {
		ret = &models.KnowledgeEnhancePrompts{
			QuestionPrompt:     pclents.DefaultInstructionTextQuestion,
			SummaryPrompt:      pclents.DefaultInstructionTextSummary,
			TableDescPrompt:    pclents.PromptTableDescription,
			TableSummaryPrompt: pclents.PromptTableSummary,
			ImageDescPrompt:    pclents.PromptImageDescription,
		}
	} else {
		ret = &models.KnowledgeEnhancePrompts{
			QuestionPrompt:     pclents.DefaultInstructionTextQuestionEn,
			SummaryPrompt:      pclents.DefaultInstructionTextSummaryEn,
			TableDescPrompt:    pclents.PromptTableDescriptionEn,
			TableSummaryPrompt: pclents.PromptTableSummaryEn,
			ImageDescPrompt:    pclents.PromptImageDescriptionEn,
		}
	}

	helper.SuccessResponse(response, ret)
}

func (r *Resource) TraceDocElements(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.TraceDocElementsReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.TraceDocElements(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) RefreshHealthz(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	err = r.kbm.RefreshHealthz(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, true)
}

func (r *Resource) GetHealthzState(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	st := r.kbm.GetHealthzState(ctx)

	helper.SuccessResponse(response, st)
}

func getPageReq(request *restful.Request) (*pb.PageReq, error) {
	page := request.QueryParameter("page")
	pageSize := request.QueryParameter("page_size")
	pageNum, err := strconv.Atoi(page)
	if err != nil {
		//return nil, err
		// 使用默认
		pageNum = 1
	}
	pageSizeNum, err := strconv.Atoi(pageSize)
	if err != nil {
		//return nil, err
		pageSizeNum = 20
	}
	sortBy := request.QueryParameter("sort_by")
	isDesc := strings.ToLower(request.QueryParameter("is_desc")) == "true"
	return &pb.PageReq{
		Page:     int32(pageNum),
		PageSize: int32(pageSizeNum),
		SortBy:   sortBy,
		IsDesc:   isDesc,
	}, nil
}

func (r *Resource) GetKnowledgeBaseSearchData(request *restful.Request, response *restful.Response) {
	rsp, err := r.kbm.ListKnowledgeBasesSearchData()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	items := make([]*pb.Item, 0)
	for _, kb := range rsp.Result {
		item := &pb.Item{
			Id:   kb.KnowledgeBase.Id,
			Name: kb.KnowledgeBase.Name,
			Desc: kb.KnowledgeBase.Description,
			/*Labels:    chain.Base.Labels,*/
			ProjectId: kb.KnowledgeBase.ProjectId,
			Module:    pb.Module_knowledge,
			RscType:   pb.RscType_KNOWLEDGE_BASE,
			// delete rsc_type, add create_type
			// CreateType:   kb.KnowledgeBase.CreationType,
			Creator:    kb.KnowledgeBase.CreateUser,
			UpdateTime: kb.KnowledgeBase.UpdateTimeMills,
		}
		items = append(items, item)
	}

	assets := &pb.Group{
		Module:  pb.Module_mlops,
		RscType: pb.RscType_CHAIN,
		Items:   items,
	}

	helper.SuccessResponse(response, assets)
}

func (r *Resource) RebuildIndex(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)

	err := r.kbm.RebuildIndex(ctx, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, true)
}

func (r *Resource) StoreTaskDocument(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.StoreTaskDocumentReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.StoreTaskDocument(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ReStoreDocument(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.ReStoreDocumentReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.ReStoreDocument(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetDocument(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamDocId)
	baseId := request.PathParameter(helper.PathParamKnowledgeBaseID)
	rsp, err := r.kbm.GetDocument(ctx, &pb.GetDocumentReq{
		UserContext:     pbUserCtx,
		KnowledgeBaseId: baseId,
		DocId:           id,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) BatchEnableDisableDocuments(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	req, err := helper.ReadEntity[pb.BatchEnableDisableDocumentsReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	req.KnowledgeBaseId = id
	rsp, err := r.kbm.BatchEnableDisableDocuments(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) BatchEnableDisableChunks(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	docId := request.PathParameter(helper.PathParamDocId)
	req, err := helper.ReadEntity[pb.BatchEnableDisableChunksReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx
	req.KnowledgeBaseId = id
	req.DocId = docId
	rsp, err := r.kbm.BatchEnableDisableChunks(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetDocumentByTaskAndAsset(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	taskID := request.PathParameter("task_id")
	assetID := request.PathParameter("asset_id")
	rsp, err := r.kbm.GetDocumentByTaskAndAsset(ctx, &pb.GetDocumentByTaskAndAssetReq{
		UserContext: pbUserCtx,
		TaskId:      taskID,
		AssetId:     assetID,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) DeleteDocumentsByTasks(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	taskIDs := request.PathParameter("task_ids")
	ids := strings.Split(taskIDs, ",")
	rsp, err := r.kbm.DeleteDocumentsByTasks(ctx, &pb.DeleteDocumentsByTasksReq{
		UserContext: pbUserCtx,
		TaskIds:     ids,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetDocumentAsset(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.QueryParameter(helper.QueryParamKnowledgeBaseID)
	projectID := request.QueryParameter(helper.QueryParameterProjectID)
	docID := request.QueryParameter(helper.QueryParamDocId)
	if docID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("doc_id is missing"))
		return
	}
	rsp, err := r.kbm.GetDocumentAssetDetail(ctx, projectID, kbid, docID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GetDocumentAssetPath(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.PathParameter(helper.PathParamKnowledgeBaseID)
	projectID := request.QueryParameter(helper.QueryParameterProjectID)
	docID := request.QueryParameter(helper.QueryParamDocId)
	if docID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("doc_id is missing"))
		return
	}
	rsp, err := r.kbm.GetDocumentAssetDetail(ctx, projectID, kbid, docID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) RetryStoreDocument(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req, err := helper.ReadEntity[pb.RetryStoreDocumentReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.UserContext = pbUserCtx

	rsp, err := r.kbm.RetryStoreDocument(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ExportDocumentAsset(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.QueryParameter(helper.QueryParamKnowledgeBaseID)
	projectID := request.QueryParameter(helper.QueryParameterProjectID)
	docID := request.QueryParameter(helper.QueryParamDocId)
	if docID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("doc_id is missing"))
		return
	}
	err = r.kbm.ExportDocumentAsset(ctx, response.ResponseWriter, projectID, kbid, docID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
}

func (r *Resource) ExportDocumentAssetPath(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.PathParameter(helper.PathParamKnowledgeBaseID)
	projectID := request.QueryParameter(helper.QueryParameterProjectID)
	docID := request.QueryParameter(helper.QueryParamDocId)
	if docID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("doc_id is missing"))
		return
	}
	err = r.kbm.ExportDocumentAsset(ctx, response.ResponseWriter, projectID, kbid, docID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
}

func (r *Resource) RetrieveKnowledgeBasePath(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req, err := helper.ReadEntity[pb.RetrieveKnowledgeBaseReq](request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	qKbId := request.PathParameter(helper.PathParamKnowledgeBaseID)
	if qKbId != "" {
		req.KnowledgeBaseId = qKbId
	}

	req.UserContext = pbUserCtx
	rsp, err := r.kbm.RetrieveKnowledgeBase(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	AsyncUpdateKBMetrics(req.KnowledgeBaseId, knowledge_base.KbMetricsTypeExecute)
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ExportDocumentQaPairs(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.QueryParameter(helper.QueryParamKnowledgeBaseID)
	err = r.kbm.ExportDocumentQaPairs(ctx, response.ResponseWriter, kbid)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
}

func (r *Resource) ExportDocumentByIds(request *restful.Request, response *restful.Response) {
	kbid := request.QueryParameter(helper.QueryParamKnowledgeBaseID)
	var docIds []string
	err := request.ReadEntity(&docIds)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	name := fmt.Sprintf("process_result_%s_%s.zip", time.Now().Format("20060102"),
		time.Now().Format("1504"))
	response.Header().Set("Content-Type", "application/zip")
	response.Header().Set("Content-Disposition",
		fmt.Sprintf("attachment; filename*=utf-8''%s", url.QueryEscape(name)))

	zos := zip.NewWriter(response.ResponseWriter)
	defer zos.Close()

	err = r.kbm.ExportDocumentByIds(docIds, kbid, zos)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
}
func (r *Resource) GetDocEngineTemplate(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	rsp, err := r.dem.GetDocEngineTemplate(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) QueryDocumentAssets(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	kbid := request.PathParameter(helper.PathParamKnowledgeBaseID)
	body := new(pb.MetaQueryConfig)
	err = request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body err: %v", err))
		return
	}
	rsp, err := r.kbm.QueryDocumentAssets(ctx, kbid, body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) CreatePrompt(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	body := make(map[string]any)
	err = request.ReadEntity(&body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body error."))
		return
	}
	err = r.dem.CreatePrompt(ctx, projectID, body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListPrompts(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	rsp, err := r.dem.ListPrompts(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) CreateGraph(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	body := bytes.Buffer{}
	_, err = io.Copy(&body, request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body error."))
		return
	}
	contentType := request.HeaderParameter("Content-Type")
	err = r.dem.CreateGraph(ctx, projectID, body, contentType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListGraphs(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	rsp, err := r.dem.ListGraphs(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) AddGraphFile(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	body := bytes.Buffer{}
	_, err = io.Copy(&body, request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body error."))
		return
	}
	contentType := request.HeaderParameter("Content-Type")
	err = r.dem.AddGraphFile(ctx, projectID, body, contentType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) CreateRegex(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	body := bytes.Buffer{}
	_, err = io.Copy(&body, request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body error."))
		return
	}
	contentType := request.HeaderParameter("Content-Type")
	err = r.dem.CreateRegex(ctx, projectID, body, contentType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListRegexes(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	rsp, err := r.dem.ListRegexes(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) AddRegexFile(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	body := bytes.Buffer{}
	_, err = io.Copy(&body, request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body error."))
		return
	}
	contentType := request.HeaderParameter("Content-Type")
	err = r.dem.AddRegexFile(ctx, projectID, body, contentType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) DownloadConfigFile(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	path := request.QueryParameter("path")
	if path == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("path is empty"))
		return
	}
	err = r.dem.DownloadConfigFile(ctx, projectID, path, response.ResponseWriter)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
}

func (r *Resource) ConfigFileContent(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	path := request.QueryParameter("path")
	if path == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("path is empty"))
		return
	}
	rsp, err := r.dem.ConfigFileContent(ctx, projectID, path)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) GlobalDependencyStatus(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectID := request.QueryParameter(helper.QueryParamProjectID)
	rsp, err := r.dem.GlobalDependencyStatus(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ListDocumentsFast(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	_, err := helper.GetUserContext(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	id := request.PathParameter(helper.PathParamKnowledgeBaseID)
	rsp, err := r.kbm.ListDocumentsFast(ctx, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}
