package knowledge_base

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/tkh-go"
)

func NewAPI(root string) *restful.WebService {
	return (&Resource{
		kbm: knowledge_base.GetKnowledgeBaseManager(),
		dem: knowledge_base.GetDocEngineManager(),
	}).WebService(root)
}

type Resource struct {
	kbm *knowledge_base.KnowledgeBaseManager
	dem *knowledge_base.DocEngineManager
}

var (
	kbsIdPP   = helper.NewPathParam("base_id", "知识库id")
	projectQP = helper.NewQueryParam("project_id", "项目id")
)

func (r *Resource) WebService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	// READ APIs
	tags := []string{"知识库"}

	ws.Route(ws.GET("/popular-kbs").To(r.ListPopularKnowledgeBases).
		Doc("获取最受欢迎的知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(helper.LimitQP.Param()).
		Param(helper.SortByQP.Param()).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*pb.KnowledgeBaseInfo{}))

	// API definitions
	ws.Route(ws.GET("/kbs").To(r.ListKnowledgeBases).
		Doc("获取知识库列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamIsPublic, "true|false, 是否查询公共空间；查询公共空间时，额外查询所有项目的公共知识库，否则只查询当前项目下的知识库")).
		Param(ws.QueryParameter(helper.QueryParamContentTypeSelector, "知识库类型筛选列表,逗号分隔. 0:文本知识库; 1:知识图谱; 2:多模态知识库")).
		Param(ws.QueryParameter(helper.QueryParamSourceTypeSelector, "知识库来源类型筛选列表,逗号分割. 0:新建知识库; 1:注册已有的知识库")).
		Param(ws.QueryParameter(helper.QueryParamRegistryTypeSelector, "知识库注册类型筛选列表,逗号分割. 0:非注册; 1:从数据连接注册; 2:从TKH注册")).
		Param(ws.QueryParameter(helper.QueryParamSceneTypeSelector, "知识库业务场景类型筛选列表,逗号分割. 0:问答场景; 1:安全场景; 99:其他场景")).
		Param(ws.QueryParameter(helper.QueryParamIsPublishedSelector, "知识库是否已发布筛选列表，逗号分隔. true：已发布；未发布. 列表长度不为1时查询全部状态")).
		Param(ws.QueryParameter(helper.QueryParamCreatorSelector, "知识库创建者筛选列表，逗号分隔. 列表长度为0时查询全部创建者")).
		Param(ws.QueryParameter(helper.QueryParamLabelSelector, "知识库标签筛选列表，形如labels=key:v1,v2;key2:v1,v2. 为空时查询全部标签")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ListKnowledgeBasesRsp{}))

	ws.Route(ws.GET("/kbs/{base_id}").To(r.GetKnowledgeBase).
		Doc("根据id获取知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.KnowledgeBaseInfo{}))

	ws.Route(ws.POST("/kbs").To(r.CreateKnowledgeBase).
		Doc("创建知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.KnowledgeBase{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.KnowledgeBase{}))

	ws.Route(ws.PUT("/kbs/{base_id}").To(r.UpdateKnowledgeBase).
		Doc("更新知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Reads(pb.KnowledgeBase{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.KnowledgeBase{}))

	ws.Route(ws.DELETE("/kbs/{base_id}").To(r.DeleteKnowledgeBases).
		Doc("删除知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.POST("/connection:detail").To(r.ListConnectionDetails).
		Doc("数据连接导入-获取数据连接的库表列表, 包含表描述信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.DataConnection{}).
		Writes([]models.TableInfo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.TableInfo{}))

	ws.Route(ws.GET("/kbs/{base_id}/docs").To(r.ListDocuments).
		Doc("获取知识库的文档列表，平铺形式").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库库id")).
		Param(ws.QueryParameter("page", "页数")).
		Param(ws.QueryParameter("page_size", "页大小")).
		Param(ws.QueryParameter("sort_by", "排序字段")).
		Param(ws.QueryParameter("is_desc", "排序是否降序")).
		Param(ws.QueryParameter("name", "文档名称")).
		Param(ws.QueryParameter(helper.QueryParamLabelSelector, "文件资产标签筛选列表，形如labels=key:v1,v2;key2:v1,v2. 为空时查询全部标签")).
		Writes(pb.ListDocumentsRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ListDocumentsRsp{}))

	ws.Route(ws.GET("/kbs/{base_id}/docs/{doc_id}").To(r.GetDocument).
		Doc("获取知识库的文档详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库库id")).
		Param(ws.PathParameter(helper.PathParamDocId, "文档id")).
		Writes(pb.GetDocumentRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentRsp{}))

	ws.Route(ws.PUT("/kbs/{base_id}/doc:disable").To(r.DisableDocument).
		Doc("启用/禁用 知识库文档").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter(helper.QueryParamDocId, "文档id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.GET("/kbs/{base_id}/docs/{doc_id}/chunks").To(r.ListDocumentChunks).
		Doc("获取知识库文档的分段内容列表，支持排序").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.PathParameter(helper.PathParamDocId, "文档id")).
		Param(ws.QueryParameter("page", "页数")).
		Param(ws.QueryParameter("page_size", "页大小")).
		Param(ws.QueryParameter("sort_by", "排序字段")).
		Param(ws.QueryParameter("is_desc", "排序是否降序")).
		Param(ws.QueryParameter(helper.QueryParamSearchContent, "搜索内容")).
		Param(ws.QueryParameter(helper.QueryParamSourceTypeSelector, "分段来源类型筛选器(逗号分隔), SOURCE_TYPE_GENERATED:自动生成的分段， SOURCE_TYPE_CREATED:手动创建的分段")).
		Writes(pb.ListDocumentChunksRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ListDocumentChunksRsp{}))

	ws.Route(ws.POST("/kbs:retrieve").To(r.RetrieveKnowledgeBase).
		Doc("检索知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter("knowledge_base_id", "知识库id(body和此处都指定时，此处优先)")).
		Reads(pb.RetrieveKnowledgeBaseReq{}).
		Writes(pb.RetrieveKnowledgeBaseRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.RetrieveKnowledgeBaseRsp{}))

	ws.Route(ws.POST("/kbs:retrieve/{base_id}").To(r.RetrieveKnowledgeBasePath).
		Doc("检索知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id(body和此处都指定时，此处优先)")).
		Reads(pb.RetrieveKnowledgeBaseReq{}).
		Writes(pb.RetrieveKnowledgeBaseRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.RetrieveKnowledgeBaseRsp{}))

	ws.Route(ws.GET("/kbs/{base_id}/doc-tree").To(r.GetDocumentTree).
		Doc("获取知识库的文档列表，文档树形式").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DocumentTree{}))

	ws.Route(ws.POST("/kbs:doc-trees").To(r.ListDocumentTrees).
		Doc("获取多个知识库的文档树").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamIds, "知识库id列表，逗号分隔")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []models.KnowledgeBaseTree{}))

	ws.Route(ws.POST("/docs:submit").To(r.SubmitFileToKnowledgeBase).
		Doc("新增文档到知识库(异步处理),允许自动创建知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.SubmitFileToKnowledgeBaseReq{}).
		Writes(pb.Document{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.Document{}))

	ws.Route(ws.POST("/docs:remove").To(r.RemoveFileFromKnowledgeBase).
		Doc("从知识库移除文档").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.RemoveFileFromKnowledgeBaseReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.PUT("/kbs/{base_id}/state").To(r.UpdateKnowledgeBaseState).
		Doc("更新知识库状态(可见、可检索)").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter("base_id", "知识库id")).
		Reads(pb.UpdateKnowledgeBaseStateReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.UpdateKnowledgeBaseStateRsp{}))

	ws.Route(ws.GET("/tkh/repos").To(r.ListTkhRepos).
		Doc("获取TKH所有文本类型的知识库列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []tkh.Repository{}))

	ws.Route(ws.POST("/chunks:add").To(r.CreateChunk).
		Doc("手动新增分段").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.CreateChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.CreateChunkRsp{}))

	ws.Route(ws.POST("/augment-chunks:add").To(r.CreateAugmentChunk).
		Doc("手动新增增强切片").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.CreateAugmentChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.CreateAugmentChunkRsp{}))

	ws.Route(ws.POST("/chunks:remove").To(r.DeleteChunk).
		Doc("删除指定分段").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.DeleteChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DeleteChunkRsp{}))

	ws.Route(ws.POST("/augment-chunks:remove").To(r.DeleteAugmentChunk).
		Doc("删除指定增强切片").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.DeleteChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DeleteChunkRsp{}))

	ws.Route(ws.POST("/chunks:update").To(r.UpdateChunk).
		Doc("编辑更新指定分段").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.UpdateChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.UpdateChunkRsp{}))

	ws.Route(ws.POST("/augment-chunks:update").To(r.UpdateAugmentChunk).
		Doc("编辑更新指定增强切片").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.UpdateAugmentChunkReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.UpdateAugmentChunkRsp{}))

	ws.Route(ws.POST("/doc-proc:preview").To(r.PreviewDocumentProcess).
		Doc("预览文档加工效果(分段预览)").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.PreviewDocumentProcessReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DocSvcLoadChunkRsp{}))

	ws.Route(ws.POST("/docs:sync-submit").To(r.SyncSubmitFilesToKnowledgeBase).
		Doc("新增文档到知识库(同步处理)，可指定文档处理配置或按照知识库默认值，可批量处理，处理中推送进度").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.SyncSubmitFilesToKnowledgeBaseReq{}).
		Writes(pb.SyncSubmitFilesToKnowledgeBaseRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.SyncSubmitFilesToKnowledgeBaseRsp{}))

	ws.Route(ws.POST("/docs:retry-submit").To(r.RetryDocumentProcess).
		Doc("重试文档处理流程, 异步处理").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.RetryDocumentProcessReq{}).
		Writes(pb.RetryDocumentProcessRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.RetryDocumentProcessRsp{}))

	ws.Route(ws.POST("/trace:elements").To(r.TraceDocElements).
		Doc("溯源文档元素").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.TraceDocElementsReq{}).
		Writes(pb.TraceDocElementsRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.TraceDocElementsRsp{}))

	ws.Route(ws.POST("/kbs:cross-retrieve").To(r.RetrieveCrossKnowledgeBase).
		Doc("跨知识库检索").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter("istio_mode", "是否需要经过istio留下审计记录").DataType("boolean").DefaultValue("false")).
		Reads(pb.RetrieveCrossKnowledgeBaseReq{}).
		Writes(pb.RetrieveCrossKnowledgeBaseRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.RetrieveCrossKnowledgeBaseRsp{}))

	ws.Route(ws.POST("/docs:is-existent").To(r.IsDocumentExistent).
		Doc("判断文档是否存在于知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.IsDocumentExistentReq{}).
		Writes(pb.IsDocumentExistentRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.IsDocumentExistentRsp{}))

	ws.Route(ws.GET("/stats").To(r.Stats).
		Doc("统计接口").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(pb.CollectKnowledgeBaseStatsRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.CollectKnowledgeBaseStatsRsp{}))

	ws.Route(ws.GET("/stats/storage").To(r.StatsStore).
		Doc("统计知识库的存储开销").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.CollectKnowledgeBaseStatsRsp{}))

	ws.Route(ws.PUT("/kbs/{base_id}/share").To(r.ShareKnowledgeBase).
		Doc("设置知识库是否共享到公共空间").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库库id")).
		Reads(pb.ShareKnowledgeBaseReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []pb.ShareKnowledgeBaseRsp{}))

	ws.Route(ws.POST("/kbs/{base_id}/publish").To(r.PublishKnowledgeBase).
		Doc("将知识库服务发布到服务列表").Metadata(restfulspec.KeyOpenAPITags, tags).Param(kbsIdPP.Param()).Param(projectQP.Param()).
		Reads(pb.PublishKnowledgeBaseReq{}).Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.PublishKnowledgeBaseRsp{}))

	ws.Route(ws.GET("/prompts").To(r.GetDefaultPrompts).
		Doc("获取知识增强的默认提示词").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.KnowledgeEnhancePrompts{}))

	ws.Route(ws.POST("/kbs/healthz:refresh").To(r.RefreshHealthz).
		Doc("刷新知识库的健康检测状态(立即执行,涉及全部知识库)").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), true))

	ws.Route(ws.GET("/kbs/healthz/state").To(r.GetHealthzState).
		Doc("获取知识库的健康检测运行状态").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), knowledge_base.HealthzState{}))

	ws.Route(ws.GET("/kbs/-/search-data").To(r.GetKnowledgeBaseSearchData).
		Doc("获取知识库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.Group{}))

	//重建索引
	ws.Route(ws.POST("/kbs/{base_id}/rebuild-index").To(r.RebuildIndex).
		Doc("重建知识库索引").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	// 2.0 新增
	ws.Route(ws.POST("/kbs:task-store").To(r.StoreTaskDocument).
		Doc("知识库任务入库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(pb.StoreTaskDocumentReq{}).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(pb.StoreTaskDocumentRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.StoreTaskDocumentRsp{}))

	ws.Route(ws.POST("/kbs:doc-restore").To(r.ReStoreDocument).
		Doc("文档重新入库").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(pb.ReStoreDocumentReq{}).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(pb.ReStoreDocumentRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ReStoreDocumentRsp{}))

	ws.Route(ws.POST("/kbs/{base_id}/docs/retrieval-status").To(r.BatchEnableDisableDocuments).
		Doc("启用禁用文档").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(pb.BatchEnableDisableDocumentsReq{}).
		Writes(pb.BatchEnableDisableDocumentsRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.BatchEnableDisableDocumentsRsp{}))

	ws.Route(ws.POST("/kbs/{base_id}/docs/{doc_id}/chunks/retrieval-status").To(r.BatchEnableDisableChunks).
		Doc("启用禁用切片").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.PathParameter(helper.PathParamDocId, "文档id")).
		Reads(pb.BatchEnableDisableChunksReq{}).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(pb.BatchEnableDisableChunksRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.BatchEnableDisableChunksRsp{}))

	ws.Route(ws.GET("/kbs/{base_id}/tasks/{task_id}/assets/{asset_id}").To(r.GetDocumentByTaskAndAsset).
		Doc("根据task和asset获取知识库文档信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.PathParameter("task_id", "任务id")).
		Param(ws.PathParameter("asset_id", "文件资产id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	ws.Route(ws.DELETE("/kbs/{base_id}/tasks/{task_ids}").To(r.DeleteDocumentsByTasks).
		Doc("根据task删除该任务的文档").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.PathParameter("task_ids", "任务id，逗号分割")).
		Writes(pb.DeleteDocumentsByTasksRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.DeleteDocumentsByTasksRsp{}))

	// 获取元数据接口
	ws.Route(ws.GET("/kbs/assets:meta").To(r.GetDocumentAsset).
		Doc("获取文档的文件资产信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter(helper.QueryParamDocId, "文档id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	ws.Route(ws.GET("/kbs/assets:meta/{base_id}").To(r.GetDocumentAssetPath).
		Doc("获取文档的文件资产信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter(helper.QueryParamDocId, "文档id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	ws.Route(ws.POST("/kbs:docs-retry").To(r.RetryStoreDocument).
		Doc("文档失败切片重试").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(pb.RetryStoreDocumentReq{}).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(pb.RetryStoreDocumentRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.RetryStoreDocumentRsp{}))

	// 下载文档原始文件
	ws.Route(ws.GET("/kbs/assets:export").To(r.ExportDocumentAsset).
		Doc("获取文档的文件资产").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter(helper.QueryParamDocId, "文档id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	ws.Route(ws.GET("/kbs/assets:export/{base_id}").To(r.ExportDocumentAssetPath).
		Doc("获取文档的文件资产").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Param(ws.QueryParameter(helper.QueryParamDocId, "文档id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	// 导出文档的qa对
	ws.Route(ws.GET("/kbs/qa:export").To(r.ExportDocumentQaPairs).
		Doc("导出知识库的qa对").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamKnowledgeBaseID, "知识库id")).
		Writes(pb.GetDocumentByTaskAndAssetRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	// 导出文档
	ws.Route(ws.POST("/kbs/docs:export").To(r.ExportDocumentByIds).
		Doc("导出知识库的文档").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter(helper.QueryParamKnowledgeBaseID, "知识库id")).
		Reads([]string{}, "文档id列表").
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.GetDocumentByTaskAndAssetRsp{}))

	// doc engine 新版获取配置接口
	ws.Route(ws.GET("/kbs/doc-engine/template").To(r.GetDocEngineTemplate).
		Doc("获取doc-engine的模板").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(map[string]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]any{}))

	// 底图转发
	ws.Route(ws.POST("/kbs/doc-engine/graphs").To(r.CreateGraph).
		Doc("新增底图").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Consumes("multipart/form-data").
		Reads(struct{}{}).
		Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.GET("/kbs/doc-engine/graphs").To(r.ListGraphs).
		Param(ws.QueryParameter("project_id", "项目id")).
		Doc("底图列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Writes([]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []any{}))

	ws.Route(ws.POST("/kbs/doc-engine/graphs/add-file").To(r.AddGraphFile).
		Doc("底图添加文件").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Consumes("multipart/form-data").
		Reads(struct{}{}).
		Writes(map[string]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]any{}))

	// prompt转发
	ws.Route(ws.POST("/kbs/doc-engine/prompts").To(r.CreatePrompt).
		Doc("创建prompts").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Reads(struct{}{}).
		Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.GET("/kbs/doc-engine/prompts").To(r.ListPrompts).
		Doc("prompts列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes([]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []any{}))

	// 正则转发
	ws.Route(ws.POST("/kbs/doc-engine/regexes").To(r.CreateRegex).
		Doc("新增正则").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Consumes("multipart/form-data").
		Reads(struct{}{}).
		Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.GET("/kbs/doc-engine/regexes").To(r.ListRegexes).
		Doc("正则列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes([]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []any{}))

	ws.Route(ws.POST("/kbs/doc-engine/regexes/add-file").To(r.AddRegexFile).
		Doc("正则添加文件").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Consumes("multipart/form-data").
		Reads(struct{}{}).
		Writes(map[string]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]any{}))

	ws.Route(ws.GET("/kbs/doc-engine/download-config-file").To(r.DownloadConfigFile).
		Doc("doc engine 配置下载").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter("path", "path")).
		Writes(map[string]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]any{}))

	ws.Route(ws.GET("/kbs/doc-engine/config-file-content").To(r.ConfigFileContent).
		Doc("doc engine 配置预览").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.QueryParameter("path", "path")).
		Writes("").
		Returns(http.StatusOK, http.StatusText(http.StatusOK), ""))

	ws.Route(ws.GET("/kbs/doc-engine/global-dependency-status").To(r.GlobalDependencyStatus).
		Doc("doc engine 全局配置").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Writes(map[string]any{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string]any{}))

	// 根据标签查询文件资产列表
	ws.Route(ws.POST("/kbs/assets:query/{base_id}").To(r.QueryDocumentAssets).
		Doc("根据标签查询获取资产列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库id")).
		Writes([]*models.Document{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.Document{}))

	ws.Route(ws.GET("/kbs/{base_id}/docs:fast").To(r.ListDocumentsFast).
		Doc("获取知识库的文档列表，平铺形式").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("project_id", "项目id")).
		Param(ws.PathParameter(helper.PathParamKnowledgeBaseID, "知识库库id")).
		Writes(pb.ListDocumentsRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ListDocumentsRsp{}))

	return ws

}
