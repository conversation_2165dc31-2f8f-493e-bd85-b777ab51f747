# Server API
该目录下主要为系统后端服务的提供的HTTP API的定义与实现代码
## 框架:  [go-restful](github.com/emicklei/go-restful)

## 目录说明
```
api
├── xxx  # xxx相关API
├── xxx  # xxx相关API
├── ...
```



## 示例代码:

```go
func NewVisionSceneAPI(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON, restful.MIME_OCTET).Produces(restful.MIME_JSON)
	metaK := restfulspec.KeyOpenAPITags
	metaV := []string{"场景模板"}

	ws.Route(ws.GET("/scenes").To(ListScenes).Metadata(metaK, metaV).
		Doc("获取场景模板列表").
		Returns(200, "OK", []models.SceneSummary{}))
	ws.Route(ws.POST("/scenes").To(CreateScene).Metadata(metaK, metaV).
		Doc("创建场景模板").
		Reads(CreateSceneReq{}).
		Returns(200, "OK", models.SceneSummary{}))
	// ...
}
```

## 接口风格规范

### [RESTful API 设计指南](http://www.ruanyifeng.com/blog/2014/05/restful_api.html)

### [谷歌API设计指南](https://www.bookstack.cn/read/API-design-guide/API-design-guide-01-%E7%AE%80%E4%BB%8B.md)