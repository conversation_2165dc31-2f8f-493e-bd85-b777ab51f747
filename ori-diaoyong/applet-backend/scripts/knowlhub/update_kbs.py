import argparse
import json
import requests

# 用于批量更新知识库
# 参数：
#   - base_url, 必填
#   - project, 必填，项目id
#   - tenant, 必填，租户id
#   - kbs, 选填，逗号分隔的知识库id列表， 为空时更新项目内的所有知识库
#   - vector, 选填，指定更新后的向量模型值，json文件路径
#   - rerank, 选填，指定更新后的重排序模型值，json文件路径

# 运行逻辑： 先调用知识库列表接口(GET /api/v1/knowlhub/kbs)获取空间内的所有知识库，然后根据知识库id筛选。 然后依次调用知识库更新接口(PUT /api/v1/knowlhub/kbs/{知识库id})

query_params = ""

def update_kb(base_url, kb, vector=None, rerank=None):
    if vector is None and rerank is None:
        return True
    url = f"{base_url}/api/v1/knowlhub/kbs/{kb['id']}?{query_params}"
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    
    if vector:
        with open(vector) as f:
            kb['vector_model'] = json.load(f)
    if rerank:
        with open(rerank) as f:
            m = json.load(f)
            kb['retrieval_config']
    
    response = requests.put(url, json=kb, headers=headers, verify=False)
    return response.status_code == 200

def rebuild_index(base_url, kb_id):
    url = f"{base_url}/api/v1/knowlhub/kbs/{kb_id}/rebuild-index"
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    response = requests.post(url, headers=headers, verify=False)
    return response.status_code == 200

def main():
    parser = argparse.ArgumentParser(description='Update knowledge bases')
    parser.add_argument("--token", help='authorization', default="Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
    parser.add_argument('--base-url',  help='Base URL', default="https://*************:32443/llm/llmops/tenants/guotaizhengquan-rb1/gateway/applet")
    parser.add_argument('--project',  help='Project ID', default="guotaijunanzq")
    parser.add_argument('--tenant',  help='Tenant ID', default="guotaizhengquan-rb1")
    parser.add_argument('--kbs', help='Comma separated KB IDs')
    parser.add_argument('--vector', help='Vector model JSON file path')
    parser.add_argument('--rerank', help='Rerank model JSON file path')
    parser.add_argument('--rebuild-index', action='store_true', default=False, help='Reload index after update')
    
    args = parser.parse_args()
    global query_params, token
    query_params = f"project_id={args.project}&tenantId={args.tenant}"
    token = args.token
    
    # Get all KBs in project
    headers = {'Authorization': token}
    response = requests.get(f"{args.base_url}/api/v1/knowlhub/kbs?{query_params}", headers=headers, verify=False)
    if response.status_code != 200:
        print("Failed to get KB list")
        return
    
    all_kbs = response.json()['result']
    all_kbs = [x["knowledge_base"] for x in all_kbs]
    target_kbs = []
    
    if args.kbs:
        kb_ids = args.kbs.split(',')
        target_kbs = [kb for kb in all_kbs if kb['id'] in kb_ids]
    else:
        target_kbs = all_kbs

    for kb in target_kbs:
        print(f"Updating KB {kb['id']}...")
        if update_kb(args.base_url, kb, args.vector, args.rerank):
            print("update Success")
        else:
            print("update Failed")
            continue
        if args.rebuild_index:
            ret = rebuild_index(args.base_url, kb["id"])
            print("rebuild index:", ret)
            
if __name__ == '__main__':
    main()