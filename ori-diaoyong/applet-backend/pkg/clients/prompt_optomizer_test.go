package clients

import (
	"context"
	"fmt"
	"testing"
)

const ()

func TestPromptOptimizer(t *testing.T) {
	q := "向{{.id}}的助手提问：莎士比亚的八大悲剧是哪些？哈姆雷特的故事是怎样的？"
	ctx := context.Background()
	opt, err := NewPromptOptimizer(ctx, TestModel)
	if err != nil {
		t.<PERSON>rror(err)
	}
	res, err := opt.PromptOptimize(ctx, "optimize", q)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(res)
	q = "在印象派画家中，{{.color}}通常被用来表现什么样子的氛围？请举例说明。"
	opt, err = NewPromptOptimizer(ctx, TestModel)
	if err != nil {
		t.<PERSON>rror(err)
	}
	res, err = opt.PromptOptimize(ctx, "optimize", q)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}
	fmt.Println(res)
	q = "在19世纪，事件{{.item}}在历史上有什么影响。"
	opt, err = NewPromptOptimizer(ctx, TestModel)
	if err != nil {
		t.Error(err)
	}
	res, err = opt.PromptOptimize(ctx, "optimize", q)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(res)
}
