package clients

import (
	"context"
	"fmt"
	"net/url"
	"path"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	MlopsRpcSize = 1024 * 1024 * 1024 // 1GB
)

type MLOpsConfig struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	// IstioGatewayAddr 可用于覆盖从服务段返回的istio ingress gateway地址
	// 当该地址为空时，则使用istio ingress gateway 返回中的 runtime info中的地址，可能为集群内部service name
	IstioGatewayAddr         string `yaml:"istio_gateway_addr"`
	ExternalIstioGatewayAddr string `yaml:"external_istio_gateway_addr"`
}
type MlOpsClient struct {
	c    MLOpsConfig
	conn *grpc.ClientConn
	Cli  serving.MLOpsServiceServiceClient
}

func NewMLOpsClient(mc MLOpsConfig) (c *MlOpsClient, err error) {
	conn, err := grpc.Dial(mc.Host+":"+mc.Port, grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(MlopsRpcSize)))
	if err != nil {
		return nil, stderr.Wrap(err, "can not connected to mlops")
	}
	stdlog.Infof("connected to mlops successfully")
	c = &MlOpsClient{
		c:    mc,
		conn: conn,
		Cli:  serving.NewMLOpsServiceServiceClient(conn),
	}
	stdlog.Infof("init mlops client success")
	return
}

// Close 关闭grpc conn, client 使用完毕请调用该方法释放tcp链接
func (m *MlOpsClient) Close() error {
	return m.conn.Close()
}

// GetRuntimeInfo 获取某个 mlops serving 纳管的服务的运行相关信息
func (m *MlOpsClient) GetRuntimeInfo(ctx context.Context, serviceID string) (*serving.MLOpsSvcRuntimeInfo, error) {
	// ctx = helper.WithGrpcCtx(ctx)
	return m.Cli.GetRuntimeInfo(ctx, &serving.ServiceID{Id: serviceID})
}

// GetHttpUrlOfService 返回被 mlops serving 纳管的服务的完整URL地址
// e.g.
// 对于容器内 http://0.0.0.0:1884/api/v1 的请求地址
// 经 istio-gateway代理后的完整路径为：
// - 格式：http://{istio-gateway-host}:{istio-gateway-port}/{virtual-url}/{custom-api-inner-port}/{custom-api-inner-path}
// - 具体示例：http://istio-ingressgateway.istio-system.svc:80/seldon/dev/service-1265bdd9-342d-4696-9dfc-709a165503c6/1884/api/v1
// 其中：
// - istio-gateway-host：istio ingress gateway本身的ip
// - istio-gateway-port：istio ingress gateway本身服务的端口
// - virtual-url： 用于标示某个具体被纳管的服务的路由前缀， 用户可自定义，默认拼接规则为：virtual url = seldon + namespace + service id
// - custom-api-inner-port：被纳管的服务的监听端口
// - custom-api-inner-path：被纳管的服务的API Path
func (m *MlOpsClient) GetHttpUrlOfService(ctx context.Context, svcID string, port int, apiPath string) (*url.URL, error) {
	ri, err := m.GetRuntimeInfo(ctx, svcID)
	if err != nil {
		return nil, stderr.Wrap(err, "get runtime info of svc %s", svcID)
	}

	hc := ri.HttpCallInfo
	if hc == nil {
		return nil, stderr.Internal.Errorf("the runtime info of svc %s is nil", svcID)
	}

	gwAddr := hc.GatewayAddress
	if addr := m.c.IstioGatewayAddr; addr != "" {
		stdlog.Debugf("over write istio gateway addr '%s' with '%s'", gwAddr, addr)
		gwAddr = addr
	}
	rawPath := path.Join(hc.VirtualUrl, fmt.Sprintf("%d", port), apiPath)
	u, err := url.Parse(fmt.Sprintf("%s/%s", gwAddr, strings.TrimPrefix(rawPath, "/")))
	if err != nil {
		return nil, stderr.Wrap(err, "parsing the raw path '%s' of mlops service %s", rawPath, svcID)
	}
	return u, nil
}

func (m *MlOpsClient) GetIstioAddr() string {
	return m.c.IstioGatewayAddr
}
