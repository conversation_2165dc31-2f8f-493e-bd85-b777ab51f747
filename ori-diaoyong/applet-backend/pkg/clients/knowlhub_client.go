package clients

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	GrpcMaxMessageMB int = 1024
	MB                   = 1024 * 1024
)

type KnowlHubClient struct {
	Host            string
	Port            string
	KnowlBaseClient pb.KnowledgeBaseManagerClient
}

func (c *KnowlHubClient) Init() error {
	creds := insecure.NewCredentials()
	conn, err := grpc.Dial(c.Host+":"+c.Port, grpc.WithTransportCredentials(creds), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(GrpcMaxMessageMB*MB)))
	if err != nil {
		return stderr.Wrap(err, "can not connect to knowlhub")
	}
	stdlog.Infof("connected to knowlhub successfully")
	c.Knowl<PERSON>ase<PERSON>lient = pb.NewKnowledgeBaseManagerClient(conn)
	return nil
}
