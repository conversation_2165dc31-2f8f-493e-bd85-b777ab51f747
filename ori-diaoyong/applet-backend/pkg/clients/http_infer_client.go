package clients

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"github.com/google/uuid"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	httpScheme         = "http"
	httpsScheme        = "https"
	defaultHttpPort    = "80"
	defaultHttpsPort   = "443"
	splitColon         = ":"
	IstioSvcName       = "istio-ingressgateway.llmops"
	IstioEnvName       = "ISTIO"
	defaultPID         = "pid"
	defaultTID         = "tid"
	generatedImagesDir = "/sfs/tenants/%s/projs/%s/generated_images/"
)

var (
	comHttpClient = NewBaseHttpCli(30 * time.Minute)                                 //设置client超时时间
	hcCache       = stdsrv.NewStdCache[*http.Client](30*time.Minute, 15*time.Minute) //设置缓存过期时间
)

func NewBaseHttpCli(timeOut time.Duration) *http.Client {
	client := &http.Client{
		Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
		Timeout:   timeOut,
	}
	return client
}

type CallConfig struct {
	Method       string
	FullInferUrl string
	Headers      map[string]string
	InferReq     ConcreteInferReq
}
type ConcreteInferReq interface {
	IsStream() bool
}
type ConcreteInferResp interface {
}

// HttpCallHelper 提供最基本的httpCall方法
type HttpCallHelper struct {
}

func (h *HttpCallHelper) StreamCall(ctx context.Context, config *CallConfig,
	eventParseHandler func(event stdsrv.SSEEvent) error) error {
	inferReq := config.InferReq
	if inferReq == nil || !inferReq.IsStream() {
		return stderr.Errorf("the inferReq is nil or the mode of infer is sync")
	}

	client, req, err := getClientAndReq(ctx, config)
	if err != nil {
		return err
	}

	if err = helper.HttpCallForStream(client, req, eventParseHandler); err != nil {
		return stderr.Wrap(err, "HttpCallForStream")
	}
	return nil
}

func (h *HttpCallHelper) SyncCall(ctx context.Context, config *CallConfig,
	stringParseHandler func(result string) error) error {
	inferReq := config.InferReq
	if inferReq == nil || inferReq.IsStream() {
		return stderr.Errorf("the inferReq is nil or the mode of infer is stream")
	}

	client, req, err := getClientAndReq(ctx, config)
	if err != nil {
		return err
	}

	if err = helper.HttpCallForSync(client, req, stringParseHandler); err != nil {
		return err
	}
	return nil
}

func getClientAndReq(ctx context.Context, config *CallConfig) (*http.Client, *http.Request, error) {
	req, err := helper.GetHttpReq(ctx, config.Method, config.FullInferUrl, config.InferReq, true)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "failed to do prepare HttpReq")
	}
	for headerK, headerV := range config.Headers {
		req.Header.Set(headerK, headerV)
	}
	return getHttpClient(ctx), req, nil
}

func getHttpClient(ctx context.Context) *http.Client {
	deadline, ok := ctx.Deadline()
	if ok {
		// ctx设置过期时间,从缓存中获取hc
		dur := time.Until(deadline)
		hc, found := hcCache.Get(dur.String())
		if !found {
			hc = NewBaseHttpCli(dur)
			hcCache.Set(dur.String(), hc)
		}
		return hc
	}
	return comHttpClient
}

// StreamInfer 基本的流式推理接口
func StreamInfer(ctx context.Context, model *pb.ModelService, inferReq ConcreteInferReq, dataHandler func(data string) error) error {
	if !inferReq.IsStream() {
		return stderr.Errorf("the mode of infer must be stream")
	}
	callConfig := GetCallConfig(model, inferReq)
	eventParseHandler := func(event stdsrv.SSEEvent) error {
		eventType := event.Event
		if eventType == stdsrv.SSEEvtError {
			return stderr.Errorf("received an event with error type, the event is: %s ", stdsrv.AnyToString(event))
		}
		if eventType != stdsrv.SSEEvtMessage {
			return stderr.Errorf("received an event with %s type, the event is: %s ", eventType, stdsrv.AnyToString(event))
		}

		data := stdsrv.AnyToString(event.Data)
		if strings.TrimSpace(data) == triton.DONEMessage {
			stdlog.Infof("received an event with DONE message ")
			return nil
		}
		return dataHandler(data)
	}

	return new(HttpCallHelper).StreamCall(ctx, callConfig, eventParseHandler)
}

// SyncInfer 基本的同步推理接口
// body: 响应体(code==200)
func SyncInfer(ctx context.Context, model *pb.ModelService, inferReq ConcreteInferReq, bodyHandler func(body string) error) error {
	if inferReq.IsStream() {
		return stderr.Errorf("the mode of infer must be sync")
	}
	callConfig := GetCallConfig(model, inferReq)
	return new(HttpCallHelper).SyncCall(ctx, callConfig, bodyHandler)
}

// syncInferHelper
// 对特殊的格式调用提供支持,以什么格式调用,则返回对应的响应格式,不支持根据渠道商自动转化调用格式
type syncInferHelper struct {
}

// OpenAiAudioTrans  使用openai格式进行音频翻译。需要通过表单传输数据,额外处理
func (s *syncInferHelper) openAiAudioTrans(ctx context.Context, model *pb.ModelService,
	req *triton.OpenAiAudioTransReq) (*triton.OpenAiAudioTransResp, error) {
	var err error
	var openaiResp *triton.OpenAiAudioTransResp
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, "failed to do openAiAudioTrans")
		}
	}()
	if len(req.TimestampGranularities) != 0 {
		req.ResponseFormat = triton.AudioResponseFormatTypeVerboseJson
	}

	// 1、准备表单数据
	buffer := new(bytes.Buffer)
	formWriter := multipart.NewWriter(buffer)
	part, err := formWriter.CreateFormFile("file", "test.mp3")
	if err != nil {
		return nil, err
	}
	if _, err = part.Write(req.File); err != nil {
		return nil, err
	}
	fields := make(map[string]string)
	fields["model"] = req.Model
	fields["language"] = req.Language
	fields["prompt"] = req.Prompt
	fields["response_format"] = string(req.ResponseFormat)
	if req.Temperature != 0.0 {
		fields["temperature"] = fmt.Sprintf("%.2f", req.Temperature)
	}
	for k, v := range fields {
		if v == "" {
			continue
		}
		err = formWriter.WriteField(k, v)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to WriteField")
		}
	}
	for _, t := range req.TimestampGranularities {
		err := formWriter.WriteField("timestamp_granularities[]", string(t))
		if err != nil {
			return nil, stderr.Wrap(err, "failed to WriteField")
		}
	}

	if err = formWriter.Close(); err != nil {
		return nil, stderr.Wrap(err, "failed to close formWriter")
	}

	// 2、构造httpRequest
	client := getHttpClient(ctx)
	callConfig := GetCallConfig(model, req)
	httpRequest, err := http.NewRequest(callConfig.Method, callConfig.FullInferUrl, buffer)
	if err != nil {
		return nil, err
	}
	for headerK, headerV := range callConfig.Headers {
		httpRequest.Header.Set(headerK, headerV)
	}

	//设置额外设置header
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	httpRequest.Header.Set(auth.TokenHeader, token)
	httpRequest.Header.Set(helper.ContentTypeHeader, formWriter.FormDataContentType())

	stringHandler := func(result string) error {
		openaiResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiAudioTransResp](result)
		if err != nil {
			return stderr.Wrap(err, "failed to Construct2OpenaiInferResp")
		}
		return nil
	}

	if err = helper.HttpCallForSync(client, httpRequest, stringHandler); err != nil {
		return nil, stderr.Wrap(err, "failed to do HttpCallForSync")
	}

	return openaiResp, nil
}

// GetCallConfig 获取模型调用时的http请求配置
func GetCallConfig(model *pb.ModelService, req ConcreteInferReq) *CallConfig {
	// 方便本地调试
	fullUrl := model.FullUrl
	if istioAddr := os.Getenv(IstioEnvName); istioAddr != "" {
		fullUrl = strings.ReplaceAll(fullUrl, IstioSvcName, istioAddr)
	}
	callConfig := &CallConfig{
		InferReq:     req,
		FullInferUrl: fullUrl,
		Method:       http.MethodPost,
	}
	return callConfig
}

// GetModelHost 返回http接口的host与port
func GetModelHost(model *pb.ModelService) (host, port string, err error) {
	callCfg := GetCallConfig(model, nil)
	url, err := url.Parse(callCfg.FullInferUrl)
	if err != nil {
		return
	}
	host = url.Host
	if strings.Contains(host, splitColon) {
		host = strings.Split(host, splitColon)[0]
	}
	port = url.Port()
	if port == "" {
		switch url.Scheme {
		case httpsScheme:
			port = defaultHttpsPort
		case httpScheme:
			port = defaultHttpPort
		default:
			port = defaultHttpPort
		}
	}
	return host, port, nil
}

// CheckModelConn 检查tcp连接是否可访问
func CheckModelConn(model *pb.ModelService) (bool, error) {
	host, port, err := GetModelHost(model)
	if err != nil {
		return false, err
	}
	isSucceed := utils.GetConnStatus(host+":"+port) == utils.ConnectSucceed
	if !isSucceed {
		return false, stderr.Error("failed to dail model_service[%s] by tcp://%s:%s", model.Id, host, port)
	}
	return isSucceed, nil
}

// CheckModelHealth 对模型发起一次调用,检查模型是否能正常工作。
// error！=nil时,代表模型可能存在问题
func CheckModelHealth(ctx context.Context, model *pb.ModelService) error {
	var inferErr error
	_, inferErr = CheckModelConn(model)
	if inferErr != nil {
		return stderr.Wrap(inferErr, "failed to conn the model")
	}

	switch model.Kind {
	case pb.ModelKind_MODEL_KIND_ML:
		_, inferErr = MlAll(ctx, model, &triton.StdMLReq{Data: [][]float32{{0, 0, 0}}})
		return inferErr
	case pb.ModelKind_MODEL_KIND_CV:
		//_, inferErr = CvAll(ctx, model, &triton.StdCVReq{ClientId: "c_id"})
		return nil
	}

	switch model.SubKind {
	case pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION: //文本生成
		handler := func(textContent string) error { return nil }
		req := &triton.OpenAiChatReq{MaxTokens: 10, Messages: []triton.MultimodalMessageItem{{Role: triton.OpenaiUserMsg, Content: "简单介绍自身"}}}
		inferErr = SyncChat(ctx, model, req, handler)

	case pb.ModelSubKind_MODEL_SUB_KIND_NLP_RERANKING: //重排模型
		_, inferErr = Rerank(ctx, model, &triton.RerankReq{Query: "a", Texts: []string{"a", "b"}})

	case pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR: //文本向量
		_, inferErr = Embedding(ctx, model, &triton.OpenAiTextVectorReq{Input: []string{"a", "b"}})

	case pb.ModelSubKind_MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE: //多模态，图像生成
		_, inferErr = ImageGen(ctx, model, &triton.OpenAiImageGenReq{Prompt: "生成一张图片", Size: "100x100"})

	case pb.ModelSubKind_MODEL_SUB_KIND_NLP_ENTITY_RECOGNITION: //实体识别
		_, inferErr = EntityRecognize(ctx, model, &triton.EntityRecognitionReq{Text: "上海有很多企业"})

	case pb.ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT:
		handler := func(textContent string) error { return nil }
		req := &triton.OpenAiChatReq{
			MaxTokens: 10,
			Messages: []triton.MultimodalMessageItem{
				{
					Role: triton.OpenaiUserMsg,
					Content: []*triton.MultimodalContentItem{
						{
							Type: "text",
							Text: "请简单介绍图片",
						},
						{
							Type: "image_url",
							ImageUrl: map[string]string{
								"detail": "high",
								//图片base64编码
								"url": "data:image/jpeg;base64,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",
							},
						},
					},
				},
			},
		}

		inferErr = SyncChat(ctx, model, req, handler)

	case pb.ModelSubKind_MODEL_SUB_KIND_SR_STT:
		fileBytes, _ := ioutil.ReadFile("./files/audio.wav")
		OpenAiAudioReq := &triton.OpenAiAudioTransReq{
			File:        fileBytes,
			Prompt:      "请简单介绍音频文件",
			Temperature: 0.8,
		}
		_, inferErr = AudioTrans(ctx, model, OpenAiAudioReq)
	default:
		inferErr = nil
	}
	return inferErr
}

// StreamChat
// 使用openai格式,对chat类型的模型进行调用(/chat/completions接口所支持的类型),
// 当前具体类型为文本生成、图像理解、视频理解
func StreamChat(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) error {
	_, err := StreamChatV2(ctx, model, inferReq, stringHandler)
	return err
}

// SyncChat StreamChat的同步版本
func SyncChat(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) error {
	_, err := SyncChatV2(ctx, model, inferReq, stringHandler)
	return err
}

func StreamChatForTaskMode(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) error {
	inferReq.Others.DisableThink = true
	_, err := StreamChatV2(ctx, model, inferReq, stringHandler)
	return err
}

// SyncChatForTaskMode
// TaskMode 使用大模型作为追问、示例生成、文本增强等任务需要去除思考内容
func SyncChatForTaskMode(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) error {
	inferReq.Others.DisableThink = true
	_, err := SyncChatV2(ctx, model, inferReq, stringHandler)
	return err
}

// StreamChatV2
// TotalResult 调用情况总结,token消耗等
func StreamChatV2(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) (*TotalResult, error) {
	// 流式,指明需要返回token信息
	inferReq.Stream = true
	inferReq.StreamOptions = &triton.StreamOptions{IncludeUsage: true}
	return chatCompletion(ctx, model, inferReq, stringHandler)
}
func SyncChatV2(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) (*TotalResult, error) {
	inferReq.Stream = false
	inferReq.StreamOptions = nil
	return chatCompletion(ctx, model, inferReq, stringHandler)
}

type TotalResult struct {
	LastRsp *triton.OpenAiChatResp
}

// 以下方法均支持 根据渠道商自动进行格式转换(openai/transwarp),
func chatCompletion(ctx context.Context, model *pb.ModelService, inferReq *triton.OpenAiChatReq,
	stringHandler func(textContent string) error) (*TotalResult, error) {

	tRes := new(TotalResult)

	// 对输出数据作一定处理
	innerHandler := []func(inferReq *triton.OpenAiChatReq, resp *triton.OpenAiChatResp) error{
		getUnifyThinkingHandler(),   // 思考方式统一为think标签,方便后续使用
		getDisableThinkingHandler(), // 部分场景,需要去除think标签
	}
	respHandler := func(resp ConcreteInferResp) error {
		castedResp, ok := resp.(*triton.OpenAiChatResp)
		if !ok {
			return stderr.Errorf("failed while cast inferResp to OpenAiChatResp")
		}

		for _, handler := range innerHandler {
			if err := handler(inferReq, castedResp); err != nil {
				return err
			}
		}

		tRes.LastRsp = castedResp
		str, err := castedResp.GetInferResult()
		if err != nil {
			return stderr.Wrap(err, "failed to do GetInferResult")
		}
		return stringHandler(str)
	}
	if err := genericInfer(ctx, model, inferReq, respHandler); err != nil {
		return nil, err
	}

	return tRes, nil
}

func getUnifyThinkingHandler() func(inferReq *triton.OpenAiChatReq, resp *triton.OpenAiChatResp) error {
	reasoningBuff := "" // 如果思考过程只有空串,则不添加think标签
	reasoningCount := 0 // 每一次的思考内容可能会分多次发送,只需要添加一次think标签
	return func(inferReq *triton.OpenAiChatReq, resp *triton.OpenAiChatResp) error {
		if len(resp.Choices) != 1 {
			return nil
		}
		choice := resp.Choices[0]
		switch inferReq.Stream {
		case true: // 流式调用
			msg := choice.Delta
			// 1、对于content与ReasoningContent都无数据时，不处理
			if msg.Content == "" && msg.ReasoningContent == "" {
				return nil
			}
			// 2、累加思考内容
			reasoningBuff = reasoningBuff + msg.ReasoningContent
			// 3、处于思考过程
			// 如果是第一次,则添加think标签。否则只是将ReasoningContent写入Content中
			if !helper.IsEmpty(reasoningBuff) {
				reasoningCount++
				if reasoningCount == 1 {
					msg.Content = fmt.Sprintf("<think>\n%s", reasoningBuff)
				} else {
					msg.Content = reasoningBuff
				}
				reasoningBuff = ""
				msg.ReasoningContent = ""
			} else {
				// 4、处于回答过程。
				// 如果之前不是思考过程,原样输出即可。否则输出Content时需要添加think结束标签
				msg.Content = msg.Content
				if reasoningCount != 0 {
					reasoningCount = 0
					reasoningBuff = ""
					msg.Content = fmt.Sprintf("\n</think>\n\n%s", msg.Content)
				}
			}
		case false: // 同步调用
			msg := choice.Message
			if msg.ReasoningContent != "" {
				// 先思考,再回答
				msg.Content = fmt.Sprintf("<think>\n%s\n</think>\n\n%s", msg.ReasoningContent, msg.Content)
				msg.ReasoningContent = ""
			}
		}
		return nil
	}
}

var (
	disableThinkRe = regexp.MustCompile(`<think>[\s\S]*?</think>`)
)

func getDisableThinkingHandler() func(inferReq *triton.OpenAiChatReq, resp *triton.OpenAiChatResp) error {
	isThinking := false      // 正在思考中
	curThinkingContent := "" // 缓存本次思考的内容
	return func(inferReq *triton.OpenAiChatReq, resp *triton.OpenAiChatResp) error {
		if !inferReq.Others.DisableThink {
			return nil // 未开启,不作处理。
		}
		if len(resp.Choices) == 0 {
			return nil
		}

		choice := resp.Choices[0]
		switch inferReq.Stream {
		case true: // 流式调用
			msg := choice.Delta
			switch isThinking {
			case true: // 1、当前输出是think内容的延续
				curThinkingContent = curThinkingContent + msg.Content
				if strings.Contains(curThinkingContent, "</think>") { // 1.1、结束思考,只输出think标签之外的内容
					isThinking = false
					curThinkingContent = ""
					msg.Content = strings.SplitN(msg.Content, "</think>", 2)[1]
				} else {
					msg.Content = "" // 1.2、未发现结束标签,仍在思考中,输出空串代替
				}
			case false: // 2、当前还未思考,或准备开始思考
				if strings.Contains(msg.Content, "<think>") { //	2.1 开始思考,只输出思考之前的内容
					isThinking = true
					curThinkingContent = strings.SplitN(msg.Content, "<think>", 2)[1]
					msg.Content = strings.SplitN(msg.Content, "<think>", 2)[0]
				} else {
					// 2.1 不涉及思考,原样输出即可
					//msg.Content = msg.Content
				}
			}
		case false: // 同步调用
			msg := choice.Message
			msg.Content = disableThinkRe.ReplaceAllString(msg.Content, "")
		}
		return nil
	}
}

func Embedding(ctx context.Context, model *pb.ModelService,
	req *triton.OpenAiTextVectorReq) (*triton.OpenAiTextVectorResp, error) {
	if req.EncodingFormat == "" {
		req.EncodingFormat = "float"
	}
	var inferResp *triton.OpenAiTextVectorResp
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

func ImageGen(ctx context.Context, model *pb.ModelService,
	req *triton.OpenAiImageGenReq) (*triton.OpenAiImageGenResp, error) {
	var inferResp *triton.OpenAiImageGenResp
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

// SimpleImageGen  根据提示词生成图片并保存到本地
// Prompt: "生成一张图片", Size: "100x100"
// absFilePath: 生成图片对pod而言的绝对路径
func SimpleImageGen(ctx context.Context, model *pb.ModelService, prompt, size string) (absFilePath string, err error) {
	modelName := DefaultModelName
	for _, p := range model.InferenceParams {
		if p.Id == modelField {
			modelName = p.DefaultValue
		}
	}
	req := &triton.OpenAiImageGenReq{N: 1, Model: modelName, Size: size, Prompt: prompt, ResponseFormat: triton.ImageGenResponseFormatB64Json}
	resp, err := ImageGen(ctx, model, req)
	if err != nil {
		return "", err
	}
	if len(resp.Data) != 1 || resp.Data[0].B64JSON == "" {
		return "", stderr.Errorf("unexpected resp, can not find base64 json for image")
	}
	pid, tid := defaultPID, defaultTID
	if projectID := helper.GetProjectID(ctx); projectID != "" {
		pid = projectID
	}
	if tenantID := helper.GetTenantID(ctx); tenantID != "" {
		tid = tenantID
	}
	return saveBase64Image(resp.Data[0].B64JSON, fmt.Sprintf(generatedImagesDir, tid, pid))
}

func AudioTrans(ctx context.Context, model *pb.ModelService,
	req *triton.OpenAiAudioTransReq) (*triton.OpenAiAudioTransResp, error) {
	var inferResp *triton.OpenAiAudioTransResp
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

// Rerank 以下方法暂时只存在transwarp渠道商
func Rerank(ctx context.Context, model *pb.ModelService, req *triton.RerankReq) (*triton.RerankRes, error) {
	var inferResp *triton.RerankRes
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

// LLMInfer 兼容triton部署的所有llm模型,原生响应格式
// func LLMInfer(ctx context.Context, model *pb.ModelService,
//		req *triton.StdLLMInferReq) (*triton.StdLLMInferResp, error) {
//		var inferResp *triton.StdLLMInferResp
//		respHandler := func(resp ConcreteInferResp) error {
//			return handleResp(resp, &inferResp)
//		}
//		if err := genericInfer(ctx, model, req, respHandler); err != nil {
//			return nil, err
//		}
//		return inferResp, nil
//	}
//

// EntityRecognize 实体识别
func EntityRecognize(ctx context.Context, model *pb.ModelService,
	req *triton.EntityRecognitionReq) (*triton.EntityRecognitionResp, error) {
	var inferResp *triton.EntityRecognitionResp
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

// TODO  StdCVResp中存在字段需要设置为any
//func CvAll(ctx context.Context, model *pb.ModelService,
//	req *triton.StdCVReq) (*triton.StdCVResp, error) {
//	var inferResp *triton.StdCVResp
//	respHandler := func(resp ConcreteInferResp) error {
//		return handleResp(resp, &inferResp)
//	}
//	if err := genericInfer(ctx, model, req, respHandler); err != nil {
//		return nil, err
//	}
//	return inferResp, nil
//}

func MlAll(ctx context.Context, model *pb.ModelService,
	req *triton.StdMLReq) (*triton.StdMLResp, error) {
	var inferResp *triton.StdMLResp
	respHandler := func(resp ConcreteInferResp) error {
		return handleResp(resp, &inferResp)
	}
	if err := genericInfer(ctx, model, req, respHandler); err != nil {
		return nil, err
	}
	return inferResp, nil
}

func handleResp[T any](resp ConcreteInferResp, target **T) error {
	castedResp, ok := resp.(*T)
	if !ok {
		return stderr.Errorf("failed while cast inferResp to %T", *target)
	}
	*target = castedResp
	return nil
}

// genericInfer 以openai格式进行调用
// 对远程模型,如果是其他格式,支持由openai格式自动向其他格式进行转化(transwarp/openai)
func genericInfer(ctx context.Context, model *pb.ModelService, req ConcreteInferReq,
	respHandler func(resp ConcreteInferResp) error) (generErr error) {
	defer func() {
		if generErr != nil {
			generErr = stderr.Wrap(generErr, "with model_svc_info[id=%s, name=%s, subKind=%s, fullUrl=%s]",
				model.Id, model.Name, model.SubKind.String(), model.FullUrl)
		}
	}()
	inferReq, err := getAdaptedInferReq(ctx, model, req)
	if err != nil {
		return stderr.Wrap(err, "failed to get adaptedInferReq")
	}
	bodyHandler := func(body string) error {
		// resolveOriInferResp 由adaptor具体进行结果解析
		resp, err := resolveOriInferResp(ctx, model, inferReq, body)
		if err != nil {
			return stderr.Wrap(err, "failed to resolve OriInferResp")
		}
		return respHandler(resp)
	}

	_, ok := inferReq.(*triton.OpenAiAudioTransReq)
	// openai格式的音频翻译需要额外处理
	if ok {
		OpenAiAudioTransReq, err := new(syncInferHelper).openAiAudioTrans(ctx, model, inferReq.(*triton.OpenAiAudioTransReq))
		if err != nil {
			return stderr.Wrap(err, "failed to do openAi AudioTrans")
		}
		return bodyHandler(stdsrv.AnyToString(OpenAiAudioTransReq))
	}

	if inferReq.IsStream() {
		return StreamInfer(ctx, model, inferReq, bodyHandler)
	}
	return SyncInfer(ctx, model, inferReq, bodyHandler)
}

func getAdaptedInferReq(ctx context.Context, model *pb.ModelService, oriInferReq ConcreteInferReq) (ConcreteInferReq, error) {
	if isRemoteModel(model) {
		// 将请求体转换为远程模型所支持的格式
		adaptor, err := getRemoteAdaptor(model)
		if err != nil {
			return nil, err
		}
		return adaptor.ConvertInferReq(ctx, model, oriInferReq)
	}
	return new(OpenaiAdaptor).ConvertInferReq(ctx, model, oriInferReq)
}

func resolveOriInferResp(ctx context.Context, model *pb.ModelService,
	oriInferReq ConcreteInferReq, oriInferResp string) (ConcreteInferResp, error) {
	if !isRemoteModel(model) {
		// TODO 优化
		if strings.Contains(model.FullUrl, "std/v1") {
			return new(TranswarpAdaptor).ResolveInferResp(ctx, model, oriInferReq, oriInferResp)
		} else {
			return new(OpenaiAdaptor).ResolveInferResp(ctx, model, oriInferReq, oriInferResp)
		}
	}
	adaptor, err := getRemoteAdaptor(model)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get adaptor for remote model")
	}
	return adaptor.ResolveInferResp(ctx, model, oriInferReq, oriInferResp)
}

func isRemoteModel(model *pb.ModelService) bool {
	return model.Type == pb.ModelServiceType_MODEL_SERVICE_TYPE_REMOTE
}

// isLocalDlieModel 本地 mwh部署
func isLocalDlieModel(model *pb.ModelService) bool {
	return model.Type == pb.ModelServiceType_MODEL_SERVICE_TYPE_LOCAL &&
		model.Schema == pb.ModelServiceSchema_MODEL_SERVICE_SCHEMA_DLIE
}

// isLocalSeldonModel 本地 mlops部署
func isLocalSeldonModel(model *pb.ModelService) bool {
	return model.Type == pb.ModelServiceType_MODEL_SERVICE_TYPE_LOCAL &&
		model.Schema == pb.ModelServiceSchema_MODEL_SERVICE_SCHEMA_SELDON
}

// Deprecated: 直接使用triton.OpenAiChatReq,使用LLMChatReq再进行cvt可能存在部分参数匹配不上
func Cvt2OpenaiChatReq(LLMChatReq *triton.LLMChatReq) *triton.OpenAiChatReq {
	if LLMChatReq.Query == "" && LLMChatReq.Prompt == "" {
		panic(stderr.Errorf("query and prompt is empty"))
	}
	openAiChatReq, err := LLMChatReq.Cvt2OpenaiChatReq()
	if err != nil {
		err = stderr.Wrap(err, "cvt llmChatReq to openaiChatReq")
		panic(err)
	}
	return openAiChatReq
}

// GetBaseChatReq
// 创建chatReq,并通过model设置默认参数
func GetBaseChatReq(model *pb.ModelService) (req *triton.OpenAiChatReq, err error) {
	req = &triton.OpenAiChatReq{}
	if model.InferenceParams == nil {
		return req, nil
	}
	for _, p := range model.InferenceParams {
		switch p.Id {
		case modelField:
			req.Model = p.DefaultValue
		case temperatureField:
			req.Temperature, err = helper.CastString2Float64(p.DefaultValue)
		case topPField:
			req.TopP, err = helper.CastString2Float64(p.DefaultValue)
		case frequencyPenaltyField, repetitionPenaltyField:
			req.FrequencyPenalty, err = helper.CastString2Float64(p.DefaultValue)
		case stopField, stopWordsField:
			req.Stop, err = helper.CastString2strs(p.DefaultValue)
		case maxLengthField, maxTokensField:
			req.MaxTokens, err = helper.CastString2Int(p.DefaultValue)
			//case topKField:
			//case noRepeatNgramSizeField:
		}
		if err != nil {
			return nil, stderr.Wrap(err, "invalid model infer params")
		}
	}
	return req, nil
}

// GetTotalChatReq 应用model中预先设置的参数创建请求,并将query、prompt填写到req中的具体位置,
func GetTotalChatReq(model *pb.ModelService, simpleReq *SimpleReq) (req *triton.OpenAiChatReq, err error) {
	req, err = GetBaseChatReq(model)
	if err != nil {
		return
	}

	if simpleReq.Others != nil {
		req.Others = *simpleReq.Others
	}

	query := simpleReq.Query
	prompt := simpleReq.Prompt
	if helper.IsEmpty(query) && helper.IsEmpty(prompt) {
		err = stderr.Errorf("query and prompt is empty str")
		return
	}

	// query与prompt每次由调用者指定
	req.Messages = nil
	if prompt != "" {
		req.Messages = append(req.Messages, triton.MultimodalMessageItem{Role: triton.OpenaiSystemMsg, Content: prompt})
	}
	if query != "" {
		req.Messages = append(req.Messages, triton.MultimodalMessageItem{Role: triton.OpenaiUserMsg, Content: query})
	}
	return
}

type SimpleReq struct {
	Query  string         `json:"query"`
	Prompt string         `json:"prompt"`
	Others *triton.Others `json:"others"`
}

//type LLMParams struct {
//	MaxLength         int      `json:"max_length,omitempty"`
//	NumBeams          int      `json:"num_beams,omitempty"`
//	EarlyStopping     bool     `json:"early_stopping,omitempty"`
//	DoSample          bool     `json:"do_sample,omitempty"`
//	Temperature       float64  `json:"temperature,omitempty"`
//	TopK              int      `json:"top_k,omitempty"`
//	TopP              float64  `json:"top_p,omitempty"`
//	RepetitionPenalty float64  `json:"repetition_penalty,omitempty"`
//	NoRepeatNgramSize int      `json:"no_repeat_ngram_size,omitempty"`
//	StopWords         []string `json:"stop_words,omitempty"`
//	GuidedJson        string   `json:"guided_json,omitempty"`
//	GuidedBackend     string   `json:"guided_backend,omitempty"`
//}

func GetBaseAudioReq(model *pb.ModelService) (req *triton.OpenAiAudioTransReq, err error) {
	req = &triton.OpenAiAudioTransReq{}
	if model.InferenceParams == nil {
		return req, nil
	}
	for _, p := range model.InferenceParams {
		switch p.Id {
		case modelField:
			req.Model = p.DefaultValue
		case languageField:
			req.Language = p.DefaultValue
		case promptField:
			req.Prompt = p.DefaultValue
		case responseFormatField:
			req.ResponseFormat = triton.AudioResponseFormatType(p.DefaultValue)
		case temperatureField:
			req.Temperature, err = helper.CastString2Float64(p.DefaultValue)
		case timestampGranularitiesField:
			strs, _ := helper.CastString2strs(p.DefaultValue)
			for _, str := range strs {
				req.TimestampGranularities = append(req.TimestampGranularities, triton.TimestampType(str))
			}
		}
		if err != nil {
			return nil, stderr.Wrap(err, "invalid model infer params")
		}
	}
	if len(req.TimestampGranularities) != 0 {
		req.ResponseFormat = triton.AudioResponseFormatTypeVerboseJson
	}
	return req, nil
}

// base64Str
// 格式1 data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4QB2RXhpZgAATU0AKgAAAAgAAwEx
// 格式2	/9j/4AAQSkZJRgABAQAAAQABAAD/4QB2RXhpZgAATU0AKgAAAAgAAwEx
func saveBase64Image(base64Str, dirPath string) (absFilePath string, err error) {
	if err = os.MkdirAll(dirPath, os.ModePerm); err != nil {
		return "", stderr.Wrap(err, "mkdir for dst file")
	}
	if idx := strings.Index(base64Str, "base64,"); idx != -1 {
		base64Str = base64Str[idx+len("base64,"):]
	}

	// 确定文件后缀
	rawData, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return "", err
	}
	mimeType := http.DetectContentType(rawData[:512])
	var ext string
	switch mimeType {
	case "image/jpg", "image/jpeg":
		ext = ".jpg"
	case "image/png":
		ext = ".png"
	case "image/gif":
		ext = ".gif"
	case "image/webp":
		ext = ".webp"
	default:
		ext = ".jpg"
	}
	absFilePath = filepath.Join(dirPath, uuid.New().String()+ext)
	if err = os.WriteFile(absFilePath, rawData, 0644); err != nil {
		return "", stderr.Wrap(err, "write file error")
	}
	return absFilePath, nil
}
