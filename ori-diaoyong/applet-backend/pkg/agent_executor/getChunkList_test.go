package agent_executor

import (
	"encoding/json"
	"fmt"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func TestGetChunkList(t *testing.T) {
	result1 := &pb.ChunkRetrieveResult{
		AboveChunks: []*pb.Chunk{
			{Id: "4745710e-5193-4c99-92ca-566c53a39c90", Content: "result1，上文1"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046500", Content: "result1，上文2"},
		},
		Chunk: &pb.Chunk{
			Id:      "5210a73f-7cb5-4c27-9ab8-d92e3a527e88",
			Content: "result1，正文",
		},
		LaterChunks: []*pb.Chunk{
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046499", Content: "result1，下文1"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046501", Content: "result1，下文2"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046502", Content: "result1，下文3"},
		},
		Score:             0.9003906,
		DocId:             "d96ea959-4e46-4817-868b-bbe737802b0a",
		DocName:           "中铁二局技术管理系统培训手册V1.3-区域指挥部-d96ea959.docx",
		KnowledgeBaseId:   "6fe3e3d1-9f23-4ac6-8b32-f975dd69df04",
		KnowledgeBaseName: "5.技术管理系统-常见问题与操作手册",
	}

	result2 := &pb.ChunkRetrieveResult{
		AboveChunks: []*pb.Chunk{
			{Id: "4745710e-5193-4c99-92ca-566c53a39c90", Content: "result2，上文1"},
		},
		Chunk: &pb.Chunk{
			Id:      "5210a73f-7cb5-4c27-9ab8-d92e3a527e88",
			Content: "result2，正文",
		},
		LaterChunks: []*pb.Chunk{
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046491", Content: "result2，下文1"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046492", Content: "result2，下文2"},
		},
		Score:             0.9003906,
		DocId:             "d96ea959-4e46-4817-868b-bbe737802b0a",
		DocName:           "中铁二局技术管理系统培训手册V1.3-区域指挥部-d96ea959.docx-2",
		KnowledgeBaseId:   "6fe3e3d1-9f23-4ac6-8b32-f975dd69df04-2",
		KnowledgeBaseName: "5.技术管理系统-常见问题与操作手册-2",
	}

	result3 := &pb.ChunkRetrieveResult{
		AboveChunks: []*pb.Chunk{
			{Id: "4745710e-5193-4c99-92ca-566c53a39c90", Content: "result3，上文1"},
			{Id: "4745710e-5193-4c99-92ca-566c53a39c91", Content: "result3，上文2"},
			{Id: "4745710e-5193-4c99-92ca-566c53a39c92", Content: "result3，上文3"},
		},
		Chunk: &pb.Chunk{
			Id:      "5210a73f-7cb5-4c27-9ab8-d92e3a527e88",
			Content: "result3，正文",
		},
		LaterChunks: []*pb.Chunk{
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046790", Content: "result3，下文1"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046791", Content: "result3，下文2"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046792", Content: "result3，下文3"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046793", Content: "result3，下文4"},
			{Id: "6af5365d-4c34-47a3-8160-ae2ad3046794", Content: "result3，下文5"},
		},
		Score:             0.9003906,
		DocId:             "d96ea959-4e46-4817-868b-bbe737802b0a",
		DocName:           "中铁二局技术管理系统培训手册V1.3-区域指挥部-d96ea959.docx-3",
		KnowledgeBaseId:   "6fe3e3d1-9f23-4ac6-8b32-f975dd69df04",
		KnowledgeBaseName: "5.技术管理系统-常见问题与操作手册-3",
	}

	resultList := make([]*pb.ChunkRetrieveResult, 0)
	resultList = append(resultList, result1)
	resultList = append(resultList, result2)
	resultList = append(resultList, result3)

	const query = "用户输入的检索词"

	chunks := make([]*pb.Chunk, 0)
	for _, r := range resultList {
		chunk := getChunk(r, query)
		chunks = append(chunks, chunk)
	}

	stdlog.Info(chunks)

	citations, err := TryCvt2Citations(chunks, CitationTypeTextKnowledge)
	if err != nil {
		stdlog.Error(err, "convert chunks to citations failed")
	}

	// Format citations into contents array
	contents := make([]string, 0, len(citations))
	for _, citation := range citations {
		knowledgeBaseName := citation.TextKnowledgeDetails.KnowledgeBaseName
		docName := citation.TextKnowledgeDetails.DocName
		content := fmt.Sprintf(
			"上文（Previous Context）:\n%s\n\n"+
				"内容（Main Content）:\n%s\n\n"+
				"下文（Following Context）:\n%s\n\n"+
				"知识库（Knowledge Base）: %s\n文档（Document）: %s",
			citation.TextKnowledgeDetails.AboveText,
			citation.Content,
			citation.TextKnowledgeDetails.LaterText,
			knowledgeBaseName,
			docName,
		)
		contents = append(contents, content)
	}

	// Convert contents to JSON string for observation
	jsonBytes, err := json.Marshal(contents)
	fmt.Println(string(jsonBytes))

	citationTexts := CvtCitations2CitationTexts(citations)
	fmt.Println(citationTexts)
}
