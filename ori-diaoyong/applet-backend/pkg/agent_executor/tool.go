package agent_executor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

func NewToolExecutor(cfg any) (agent_definition.ToolExecutor, error) {
	switch c := cfg.(type) {
	case AppletToolExecutorConfig: //  应用服务
		return NewAppletToolExecutor(c)
	case ModelToolExecutorConfig: // 模型服务
		return NewModelToolExecutor(c)
	case ApiToolExecutorConfig: // 工具集
		return NewApiToolExecutor(c)
	case KnowledgeToolExecutorConfig: // 知识库
		return NewKnowledgeToolExecutor(c)
	default:
		return nil, nil
	}
}

type ApiToolExecutorConfig struct {
}

func NewApiToolExecutor(cfg ApiToolExecutorConfig) (*ApiToolExecutor, error) {
	return &ApiToolExecutor{}, nil
}

type KnowledgeToolExecutorConfig struct {
	Host string
	Port string
}

func NewKnowledgeToolExecutor(cfg KnowledgeToolExecutorConfig) (*KnowlHubExecutor, error) {

	cli := &clients.KnowlHubClient{
		Host: cfg.Host,
		Port: cfg.Port,
	}
	err := cli.Init()
	if err != nil {
		return nil, err
	}

	return &KnowlHubExecutor{Cli: cli}, nil
}

type ModelToolExecutorConfig struct {
}

func NewModelToolExecutor(cfg ModelToolExecutorConfig) (*ModelToolExecutor, error) {
	return &ModelToolExecutor{}, nil
}

type AppletToolExecutorConfig struct {
	MLOps  clients.MLOpsConfig     `yaml:"mlops_config"`
	AppSvc clients.AppletSvcConfig `yaml:"app_svc_config"`
}

func NewAppletToolExecutor(config AppletToolExecutorConfig) (*AppletServiceExecutor, error) {
	ac, err := clients.NewAppletSvcClient(config.MLOps, config.AppSvc, nil)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return &AppletServiceExecutor{
		ac: ac,
	}, nil
}
