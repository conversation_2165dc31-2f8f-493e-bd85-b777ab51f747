package agent_executor

import (
	"context"
	"encoding/json"
	"fmt"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

const (
	ParamNameQuery         = "query"
	FieldKnowledgeBaseId   = "knowledge_base_id"
	FieldKnowledgeBaseName = "knowledge_base_name"
	FieldDocId             = "doc_id"
	FieldDocName           = "doc_name"
	FieldScore             = "score"
	FieldQuery             = "query"
	FieldAboveText         = "AboveChunks"
	FieldLaterText         = "LaterChunks"
)

type KnowlHubExecutor struct {
	Cli *clients.KnowlHubClient
}

// Type 当前执行器支持的工具类型，可用于判断能够执行什么工具
func (e *KnowlHubExecutor) Type() agent_definition.ToolType {
	return agent_definition.ToolTypeKnowledgeHub
}

// executeCommon 执行KnowlHub工具的通用逻辑，返回查询结果的Chunks
func (e *KnowlHubExecutor) executeCommon(ctx context.Context, tool agent_definition.ToolDescriber, input any) ([]*pb.Chunk, error) {
	kb, ok := tool.(*agent_definition.KnowlHubDescriber)
	if !ok {
		return nil, helper.ToolExecuteErr.Errorf("tool type is not KnowlHubDescriber")
	}
	// 模型输入参数
	paramValueMap, ok := input.(map[string]any)
	if !ok {
		return nil, helper.ToolExecuteErr.Errorf("input type is not map[string]any")
	}
	queryAny := paramValueMap[ParamNameQuery]
	if !ok {
		return nil, helper.ToolExecuteErr.Errorf("query is not in input")
	}

	query, ok := queryAny.(string)
	if !ok {
		return nil, helper.ToolExecuteErr.Errorf("query type is not string")
	}

	return e.Search(ctx, kb, query)
}

// CvtChunksToObservation 将知识库检索结果转换为Agent可观察的JSON字符串
// 只保留Chunk的内容字段，并序列化为JSON数组
func (e *KnowlHubExecutor) CvtChunksToObservation(chunks []*pb.Chunk) (string, error) {
	contents := make([]string, 0, len(chunks))
	for _, c := range chunks {
		knowledgeBaseName := c.Extra[FieldKnowledgeBaseName]
		docName := c.Extra[FieldDocName]
		contents = append(contents, fmt.Sprintf("内容: %s\n知识库: %s\n文档: %s", c.Content, knowledgeBaseName, docName))
	}

	bs, err := json.Marshal(contents)
	if err != nil {
		return "", stderr.Wrap(err, "marshal chunk contents failed")
	}
	return string(bs), nil
}

// Execute 使用给定的输入参数，执行对应的工具，返回JSON格式的内容字符串
// 对应Agent生成的Action的执行过程
func (e *KnowlHubExecutor) Execute(ctx context.Context, tool agent_definition.ToolDescriber, input any) (string, error) {
	chunks, err := e.executeCommon(ctx, tool, input)
	if err != nil {
		return "", err
	}

	return e.CvtChunksToObservation(chunks)
}

// ExecuteReturnCitations executes the tool and returns citations along with observation
func (e *KnowlHubExecutor) ExecuteReturnCitations(
	ctx context.Context,
	tool agent_definition.ToolDescriber,
	input any,
) (citations []*Citation, observation string, err error) {
	chunks, err := e.executeCommon(ctx, tool, input)
	if err != nil {
		return nil, "", stderr.Wrap(err, "execute knowl hub tool failed")
	}

	// Convert chunks to citations using TryCvt2Citations
	citations, err = TryCvt2Citations(chunks, CitationTypeTextKnowledge)
	if err != nil {
		return nil, "", stderr.Wrap(err, "convert chunks to citations failed")
	}

	// Format citations into contents array
	contents := make([]string, 0, len(citations))
	for _, citation := range citations {
		knowledgeBaseName := citation.TextKnowledgeDetails.KnowledgeBaseName
		docName := citation.TextKnowledgeDetails.DocName
		content := fmt.Sprintf(
			"上文（Previous Context）:\n%s\n\n"+
				"内容（Main Content）:\n%s\n\n"+
				"下文（Following Context）:\n%s\n\n"+
				"知识库（Knowledge Base）: %s\n文档（Document）: %s",
			citation.TextKnowledgeDetails.AboveText,
			citation.Content,
			citation.TextKnowledgeDetails.LaterText,
			knowledgeBaseName,
			docName,
		)
		contents = append(contents, content)
	}

	// Convert contents to JSON string for observation
	jsonBytes, err := json.Marshal(contents)
	if err != nil {
		return nil, "", stderr.Wrap(err, "marshal citations to json failed")
	}

	return citations, string(jsonBytes), nil
}

func (e *KnowlHubExecutor) Search(ctx context.Context, kb *agent_definition.KnowlHubDescriber, query string) ([]*pb.Chunk, error) {
	uc, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "get user context")
	}
	var docRange []string
	for _, v := range kb.EnableDocs {
		docRange = append(docRange, v.DocId)
	}
	strategy := pb.KnowledgeBaseRetrieveStrategy_VECTOR
	if kb.RecallParams != nil {
		strategy = kb.RecallParams.Strategy
	}
	if kb.DocEngineConfig != nil {
		strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
	}

	searchReq := &pb.RetrieveKnowledgeBaseReq{
		UserContext:     uc,
		KnowledgeBaseId: kb.ID,
		Query:           query,
		DocRange:        docRange,
		RetrievalConfig: &pb.RetrievalConfig{
			Strategy:        strategy,
			RecallParams:    kb.RecallParams,
			RerankParams:    kb.RerankParams,
			ChainService:    "",
			AutoConfigured:  false,
			ContextNum:      kb.ContextNum,
			DocEngineConfig: kb.DocEngineConfig,
		},
	}
	stdlog.Info("searchReq = ", searchReq)

	rsp, err := e.Cli.KnowlBaseClient.RetrieveKnowledgeBase(ctx, searchReq)
	if err != nil {
		stdlog.Info("failed to do kb_search, err = ", err)
		return nil, err
	}
	stdlog.Info("searchRsp = ", rsp)

	chunks := make([]*pb.Chunk, 0)
	for _, r := range rsp.Result {
		chunk := getChunk(r, query)
		chunks = append(chunks, chunk)
	}
	return chunks, nil
}

// grpc调用RetrieveCrossKnowledgeBase
func (e *KnowlHubExecutor) SearchCrossKb(ctx context.Context, kbs *agent_definition.KnowledgeBases, query string) ([]*pb.Chunk, error) {
	uc, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "get user context")
	}
	ranges := make([]*pb.RetrieveRange, 0)
	for _, kb := range kbs.KnowledgeBaseDesc {
		var docRange []string
		for _, v := range kb.EnableDocs {
			docRange = append(docRange, v.DocId)
		}
		strategy := pb.KnowledgeBaseRetrieveStrategy_VECTOR
		if kb.RecallParams != nil {
			strategy = kb.RecallParams.Strategy
		}
		if kb.DocEngineConfig != nil {
			strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		retrieveRange := &pb.RetrieveRange{
			KnowledgeBaseId: kb.ID,
			DocRange:        docRange,
			RetrievalConfig: &pb.RetrievalConfig{
				Strategy:        strategy,
				RecallParams:    kb.RecallParams,
				RerankParams:    kb.RerankParams,
				ChainService:    "",
				AutoConfigured:  false,
				ContextNum:      kb.ContextNum,
				DocEngineConfig: kb.DocEngineConfig,
			},
		}
		ranges = append(ranges, retrieveRange)
	}

	searchReq := &pb.RetrieveCrossKnowledgeBaseReq{
		UserContext:  uc,
		Query:        query,
		Ranges:       ranges,
		RerankParams: kbs.RerankParams,
	}
	stdlog.Info("searchReq = ", searchReq)

	rsp, err := e.Cli.KnowlBaseClient.RetrieveCrossKnowledgeBase(ctx, searchReq)
	if err != nil {
		stdlog.Info("failed to do kb_search_cross, err = ", err)
		return nil, err
	}
	stdlog.Info("searchRsp = ", rsp)

	chunks := make([]*pb.Chunk, 0)
	for _, r := range rsp.Result {
		chunk := getChunk(r, query)
		chunks = append(chunks, chunk)
	}
	return chunks, nil
}

func (e *KnowlHubExecutor) Insert(ctx context.Context, knowledgeBaseId, sfsFilePath string, chunks []*pb.Chunk) (string, error) {
	uc, err := helper.GetUserContext(ctx)
	if err != nil {
		return "", stderr.Wrap(err, "get user context")
	}
	req := &pb.SubmitChunksToKnowledgeBaseReq{
		UserContext:     uc,
		KnowledgeBaseId: knowledgeBaseId,
		FilePath:        sfsFilePath,
		Chunks:          chunks,
	}
	rsp, err := e.Cli.KnowlBaseClient.SubmitChunksToKnowledgeBase(ctx, req)
	if err != nil {
		return "", stderr.Wrap(err, "submit chunks of %s to knowledge base %s", sfsFilePath, knowledgeBaseId)
	}
	return rsp.String(), nil
}

// CheckHealth 检查某个工具的健康状态
func (e *KnowlHubExecutor) CheckHealth(tool agent_definition.ToolDescriber) (health.ServiceHealth, error) {
	return health.ServiceHealth{
		ID:      tool.Definition().ID,
		Name:    "",
		Healthy: true,
		Detail:  "",
	}, nil
}

func getChunk(r *pb.ChunkRetrieveResult, query string) *pb.Chunk {
	chunk := r.Chunk
	if chunk.Extra == nil {
		chunk.Extra = make(map[string]string)
	}
	chunk.Extra[FieldScore] = fmt.Sprintf("%f", r.Score)
	chunk.Extra[FieldDocId] = r.DocId
	chunk.Extra[FieldDocName] = r.DocName
	chunk.Extra[FieldKnowledgeBaseId] = r.KnowledgeBaseId
	chunk.Extra[FieldKnowledgeBaseName] = r.KnowledgeBaseName
	chunk.Extra[FieldQuery] = query

	aboveText := extractContextChunks(r.AboveChunks)
	chunk.Extra[FieldAboveText] = aboveText
	laterText := extractContextChunks(r.LaterChunks)
	chunk.Extra[FieldLaterText] = laterText

	return chunk
}

func extractContextChunks(chunks []*pb.Chunk) string {
	if len(chunks) == 0 {
		return ""
	}
	finalString := ""
	for _, chunk := range chunks {
		finalString += chunk.Content
		finalString += "; "
	}
	return finalString
	// todo, 看搜索的时候，能不能拿到 加工策略，从而拿到 ChunkOverlap 的长度，然后直接给上下文去重
}
