package agent_executor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

type ApiToolExecutor struct {
}

func (a *ApiToolExecutor) CheckHealth(tool agent_definition.ToolDescriber) (health.ServiceHealth, error) {
	return health.ServiceHealth{
		ID:      tool.Definition().ID,
		Name:    "",
		Healthy: true,
		Detail:  "",
	}, nil
}

func (a *ApiToolExecutor) Type() agent_definition.ToolType {
	return agent_definition.ToolTypeAPITool
}

func (a *ApiToolExecutor) Execute(ctx context.Context, tool agent_definition.ToolDescriber, input any) (string, error) {
	apiToolDescriber, ok := tool.(*agent_definition.APIToolDescriber)
	if !ok {
		return "", helper.ToolExecuteErr.Error("tool type is not APIToolDescriber")
	}
	// 模型输入参数
	paramValueMap, ok := input.(map[string]any)
	if !ok {
		return "", helper.ToolExecuteErr.Error("input type is not map[string]any")
	}
	// 工具定义参数
	toolParamMap := make(map[string]agent_definition.APIToolParam)
	for _, p := range apiToolDescriber.Params {
		toolParamMap[p.Name] = p
	}
	// 用户自定义参数键值对
	toolParamValueMap := apiToolDescriber.ToolParamValueMap
	// 请求路径
	apiPath, err := url.JoinPath(apiToolDescriber.BaseURL, apiToolDescriber.APIPath)
	if err != nil {
		return "", stderr.Wrap(err, "execute api tool err,join path :%v , %v error",
			apiToolDescriber.BaseURL, apiToolDescriber.APIPath)
	}
	// header
	header := apiToolDescriber.CollectionHeaders
	// query参数
	queryParam := make(map[string]string)
	// 请求体-object中的参数
	reqMap := make(map[string]any)
	// 请求体是array的情况用到的参数
	var arrBody any
	// 校验模型返回的参数是否正确
	for name, _ := range paramValueMap {
		_, ok := toolParamMap[name]
		if !ok {
			return "", helper.ToolExecuteErr.Error("execute api call error,undefined param :%v ", name)
		}
		// todo 校验模型返回的值的数据类型
	}
	// 校验用户自定义参数是否正确
	for name, _ := range toolParamValueMap {
		_, ok := toolParamMap[name]
		if !ok {
			return "", helper.ToolExecuteErr.Error("execute api call error,undefined param :%v ", name)
		}
		// todo 校验模型返回的值的数据类型
	}
	for paramName, p := range toolParamMap {
		var value any
		if p.DefaultValue != nil {
			value = p.DefaultValue
		}
		// 模型输入参数
		if r, ok := paramValueMap[p.Name]; ok {
			value = r
		}
		// 用户自定义参数
		if r, ok := toolParamValueMap[p.Name]; ok {
			value = r
		}
		if p.Required && value == nil {
			return "", helper.ToolExecuteErr.Error("required failed :%v no value", paramName)
		}
		if value != nil {
			switch p.ParamType {
			case agent_definition.APIToolParamTypeQuery:
				queryParam[paramName] = fmt.Sprintf("%v", value)
			case agent_definition.APIToolParamTypePath:
				apiPath = strings.Replace(apiPath, fmt.Sprintf("{%s}", p.Name), fmt.Sprintf("%v", value), -1)
			case agent_definition.APIToolParamTypeHeader:
				header[paramName] = fmt.Sprintf("%v", value)
			case agent_definition.APIToolParamTypeBody:
				reqMap[paramName] = value
			case agent_definition.APIToolParamTypeArray:
				arrBody = value
			}
		}
	}
	reqBody := ""
	// 请求体是数组
	if arrBody != nil {
		bytes, err := json.Marshal(arrBody)
		if err != nil {
			return "", err
		}
		reqBody = string(bytes)
	} else if len(reqMap) > 0 {
		// 请求体是object
		bytes, err := json.Marshal(reqMap)
		if err != nil {
			return "", err
		}
		reqBody = string(bytes)
	}
	if apiToolDescriber.ServerType == agent_definition.ServerTypeRest {
		stdlog.Infof("call api tool params : path :%v,query param :%v,header :%v,body :%v",
			apiPath, queryParam, header, reqBody)
		return HttpCallString(ctx, &httpParam{
			Method:     strings.ToUpper(apiToolDescriber.Method),
			Url:        apiPath,
			ReqBody:    reqBody,
			Header:     header,
			QueryParam: queryParam,
		})
	} else {
		// mcp连接参数
		collectionParams := make(map[string]agent_definition.MCPParam)
		for _, p := range apiToolDescriber.CollectionParams {
			collectionParams[p.Name] = p
		}
		collectionParamValueMap := apiToolDescriber.CollectionParamValueMap
		if collectionParamValueMap == nil {
			collectionParamValueMap = make(map[string]any, 0)
		}
		// map连接参数是否正确
		for name, _ := range collectionParamValueMap {
			_, ok := collectionParams[name]
			if !ok {
				return "", helper.ToolExecuteErr.Error("execute api call error,undefined param :%v ", name)
			}
			// todo 校验数据类型
		}
		for paramName, p := range collectionParams {
			var value any
			if p.DefaultValue != nil {
				value = p.DefaultValue
			}
			// 输入参数
			if r, ok := collectionParamValueMap[p.Name]; ok && p.Customize {
				value = r
			}
			collectionParamValueMap[paramName] = value
		}

		for paramName, value := range toolParamValueMap {
			paramValueMap[paramName] = value
		}
		stdlog.Infof("call mcp tool params : path :%v, query param :%v, collectionParams: %v", apiToolDescriber.APIPath, paramValueMap,
			collectionParamValueMap)
		return mcpCallString(ctx, &mcpParam{
			ServerType:       apiToolDescriber.ServerType,
			McpType:          apiToolDescriber.McpType,
			BaseUrl:          apiToolDescriber.BaseURL,
			ToolName:         apiToolDescriber.Name,
			Params:           paramValueMap,
			CollectionParams: collectionParamValueMap,
			Proxy:            &apiToolDescriber.Proxy,
		})
	}
}

func (a *ApiToolExecutor) ExecuteReturnCitations(
	ctx context.Context,
	tool agent_definition.ToolDescriber,
	input any,
) (citations []*Citation, observation string, err error) {
	result, err := a.Execute(ctx, tool, input)
	if err != nil {
		return nil, "", stderr.Wrap(err, "execute api tool failed")
	}
	var citationsResponse *CitationsResponse
	if err := json.Unmarshal([]byte(result), &citationsResponse); err != nil {
		// 如果无法解析为引用，直接把原始结果作为observation返回
		return nil, result, nil
	}
	if citationsResponse == nil || !citationsResponse.Validate() {
		// 如果引用信息不合法，直接把原始结果作为observation返回
		return nil, result, nil
	}
	contents := make([]string, 0, len(citationsResponse.Citations))
	for _, citation := range citationsResponse.Citations {
		var content string
		switch citation.CitationType {
		case CitationTypeTextKnowledge:
			content = fmt.Sprintf("内容: %s\n知识库: %s\n文档: %s", citation.Content, citation.TextKnowledgeDetails.KnowledgeBaseName, citation.TextKnowledgeDetails.DocName)
		case CitationTypeInternetSearch:
			content = fmt.Sprintf("内容: %s\n标题: %s\n链接: %s", citation.Content, citation.InternetSearchDetail.Title, citation.InternetSearchDetail.Url)
		}
		contents = append(contents, content)
	}
	jsonBytes, err := json.Marshal(contents)
	if err != nil {
		return nil, "", stderr.Wrap(err, "marshal citations to json failed")
	}
	return citationsResponse.Citations, string(jsonBytes), nil
}
