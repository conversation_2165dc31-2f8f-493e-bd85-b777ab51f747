package widgets

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

func Init() {
	InitWidgetGroups()
	initWidgetFactory()
}

type NodeMeta struct {
	ID           string
	NodeID       string
	SubChainID   string
	NodeName     string
	ChainName    string
	SubChainName string
	WidgetKey    string          //算子类别id
	Context      context.Context // ctx,生成tick-script时可能需要
}

func (n NodeMeta) ToBaseScript() *script.BaseScript {
	return &script.BaseScript{
		ID:           n.ID,
		NodeID:       n.NodeID,
		SubChainID:   n.SubChainID,
		NodeName:     n.NodeName,
		ChainName:    n.ChainName,
		SubChainName: n.SubChainName,
		WidgetKey:    n.WidgetKey,
	}
}
