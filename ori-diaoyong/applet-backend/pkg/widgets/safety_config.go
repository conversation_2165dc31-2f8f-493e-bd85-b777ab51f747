package widgets

import (
	"bufio"
	"context"
	"database/sql/driver"
	"fmt"
	"net/http"
	"os"
	"path"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/conf"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"

	"github.com/pkg/errors"
)

const (
	regExt    = ".reg"
	sfsPrefix = "sfs://"
)

// SafetyConfig 安全围栏配置
type SafetyConfig struct {
	ID               string            `json:"id"`
	ProjectID        string            `json:"project_id"`
	InputGuardrails  *InputGuardrails  `json:"input_guardrails"`
	OutputGuardrails *OutputGuardrails `json:"output_guardrails"`
}

type InputGuardrails struct {
	PromptInjectGuard   PromptInjectGuard   `json:"prompt_inject_guard"`  // 提示词注入防护
	SensitiveProtection SensitiveProtection `json:"sensitive_protection"` // 敏感词防护
}

type PromptInjectGuard struct {
	PromptInjectGuardEnabled      BitBool               `json:"prompt_inject_guard_enabled"`     // 提示词注入防护开关
	PresetRuleStrategy            InputPresetRule       `json:"preset_rule_strategy"`            // 预置规则策略
	IntelligentProtectionStrategy IntelligentProtection `json:"intelligent_protection_strategy"` // 智能防护策略
	Voiceover                     string                `json:"voiceover"`                       // 回复话术
}

type InputPresetRule struct {
	PresetRuleStrategyEnabled   BitBool     `json:"preset_rule_strategy_enabled"`  // 预置规则策略开关
	FileInfos                   []*FileInfo `json:"file_infos"`                    // 文件信息
	SafetyRestrictionsThreshold float32     `json:"safety_restrictions_threshold"` // 安全限制阈值
}

type IntelligentProtection struct {
	IntelligentProtectionStrategyEnabled BitBool          `json:"intelligent_protection_strategy_enabled"` // 智能防护策略开关
	KnowledgeHubInfo                     KnowledgeHubInfo `json:"knowledge_hub_info"`                      // 知识库信息
	SafetyRestrictionsThreshold          float32          `json:"safety_restrictions_threshold"`           // 安全限制阈值
}

type KnowledgeHubInfo struct {
	ID        string `json:"id"`
	ProjectID string `json:"project_id"`
}

type FileInfo struct {
	UploadFilePath    string   `json:"upload_file_path"`    // 上传文件路径
	UploadFileContent []string `json:"upload_file_content"` // 上传文件的内容
}

type SensitiveProtection struct {
	SensitiveProtectionEnabled BitBool     `json:"sensitive_protection_enabled"` // 敏感词防护开关
	FileInfos                  []*FileInfo `json:"file_infos"`                   // 文件信息
	Voiceover                  string      `json:"voiceover"`                    // 回复话术
	RegExpFileInfos            []*FileInfo `json:"reg_exp_file_infos"`           // 正则配置文件信息
	//ReqExpEnabled              BitBool     `json:"reg_exp_enabled"`              // 正则配置是否开启
}

type OutputGuardrails struct {
	SensitiveProtection SensitiveProtection `json:"sensitive_protection"` // 敏感词防护
}

type BitBool bool

// Value implements the driver.Valuer interface,
// and turns the BitBool into a bitfield (BIT(1)) for MySQL storage.
func (b BitBool) Value() (driver.Value, error) {
	return bool(b), nil
}

// Scan implements the sql.Scanner interface,
// and turns the bitfield incoming from MySQL into a BitBool
func (b *BitBool) Scan(src interface{}) error {
	v, ok := src.([]byte)
	if !ok {
		return errors.New("bad []byte type assertion")
	}
	*b = v[0] == 1
	return nil
}

// 构造函数
func NewSafetyConfig(projectId string) *SafetyConfig {
	return &SafetyConfig{
		ID:        toolkit.NewUUID(),
		ProjectID: projectId,
		InputGuardrails: &InputGuardrails{
			PromptInjectGuard: PromptInjectGuard{
				PromptInjectGuardEnabled: true,
				PresetRuleStrategy: InputPresetRule{
					PresetRuleStrategyEnabled:   true,
					SafetyRestrictionsThreshold: 0.5,
					FileInfos:                   nil,
				},
				IntelligentProtectionStrategy: IntelligentProtection{
					IntelligentProtectionStrategyEnabled: false,
					KnowledgeHubInfo: KnowledgeHubInfo{
						ID:        "",
						ProjectID: "",
					},
					SafetyRestrictionsThreshold: 0.7,
				},
				Voiceover: "您的最近输入触发了系统的安全限制。为了保护所有用户的安全，我们建议您检查并修改您的输入。",
			},
			SensitiveProtection: SensitiveProtection{
				SensitiveProtectionEnabled: true,
				Voiceover:                  "您的最近输入触发了系统的安全限制。为了保护所有用户的安全，我们建议您检查并修改您的输入。",
				FileInfos:                  nil,
			},
		},
		OutputGuardrails: &OutputGuardrails{
			SensitiveProtection: SensitiveProtection{
				SensitiveProtectionEnabled: true,
				Voiceover:                  "模型输出触发了系统的安全限制，非常抱歉！",
				FileInfos:                  nil,
			},
		},
	}
}

// UpdateGuardrailsServerConfig 使用httpclient调用外部python服务
func UpdateGuardrailsServerConfig(ctx context.Context, config *SafetyConfig) error {
	if err := SetFileContent(config); err != nil {
		return err
	}
	fullUlr := conf.Config.GuardrailsConfig.FullUpdateAddr
	HttpClient := clients2.HttpClient{Cli: clients2.NewBaseHttpCli(10 * time.Minute)}
	_, err := HttpClient.HttpCallString(ctx, &clients2.HttpParam{
		Method:  http.MethodPost,
		Url:     fullUlr,
		ReqBody: stdsrv.AnyToString(config),
	})
	if err != nil {
		return err
	}
	return nil
}

func SetFileContent(safetyConfig *SafetyConfig) error {
	var regFileInfos []*FileInfo
	var filteredFileInfos []*FileInfo
	// 获取输入护栏的正则敏感词
	regFileInfos = make([]*FileInfo, 0)
	filteredFileInfos = make([]*FileInfo, 0)
	inSenPro := safetyConfig.InputGuardrails.SensitiveProtection
	for _, file := range inSenPro.FileInfos {
		if path.Ext(file.UploadFilePath) == regExt {
			regFileInfos = append(regFileInfos, file)
		} else {
			filteredFileInfos = append(filteredFileInfos, file)
		}
	}
	for _, file := range inSenPro.RegExpFileInfos {
		if path.Ext(file.UploadFilePath) == regExt {
			regFileInfos = append(regFileInfos, file)
		} else {
			filteredFileInfos = append(filteredFileInfos, file)
		}
	}
	safetyConfig.InputGuardrails.SensitiveProtection.FileInfos = filteredFileInfos
	safetyConfig.InputGuardrails.SensitiveProtection.RegExpFileInfos = regFileInfos

	// 获取输出护栏的正则敏感词
	regFileInfos = make([]*FileInfo, 0)
	filteredFileInfos = make([]*FileInfo, 0)
	out := safetyConfig.OutputGuardrails.SensitiveProtection
	for _, file := range out.FileInfos {
		if path.Ext(file.UploadFilePath) == regExt {
			regFileInfos = append(regFileInfos, file)
		} else {
			filteredFileInfos = append(filteredFileInfos, file)
		}
	}
	for _, file := range out.RegExpFileInfos {
		if path.Ext(file.UploadFilePath) == regExt {
			regFileInfos = append(regFileInfos, file)
		} else {
			filteredFileInfos = append(filteredFileInfos, file)
		}
	}
	safetyConfig.OutputGuardrails.SensitiveProtection.FileInfos = filteredFileInfos
	safetyConfig.OutputGuardrails.SensitiveProtection.RegExpFileInfos = regFileInfos

	if err := fillConfigFileContent(safetyConfig); err != nil {
		return stderr.Wrap(err, "failed to do fillConfigFileContent")
	}
	return nil
}

func fillConfigFileContent(safetyConfig *SafetyConfig) error {
	//1、输入安全护栏
	// 敏感词
	if err := fillFileContent(safetyConfig.InputGuardrails.SensitiveProtection.FileInfos); err != nil {
		return err
	}
	// 敏感词正则
	if err := fillFileContent(safetyConfig.InputGuardrails.SensitiveProtection.RegExpFileInfos); err != nil {
		return err
	}
	// 提示词注入防护
	if err := fillFileContent(safetyConfig.InputGuardrails.PromptInjectGuard.PresetRuleStrategy.FileInfos); err != nil {
		return err
	}

	//2、输出安全护栏
	// 敏感词
	if err := fillFileContent(safetyConfig.OutputGuardrails.SensitiveProtection.FileInfos); err != nil {
		return err
	}
	// 敏感词正则
	if err := fillFileContent(safetyConfig.OutputGuardrails.SensitiveProtection.RegExpFileInfos); err != nil {
		return err
	}
	return nil
}

func fillFileContent(fileInfos []*FileInfo) error {
	// 读取文件内容
	for _, fileInfo := range fileInfos {
		fileContent, err := ReadFileContent(fileInfo.UploadFilePath)
		if err != nil {
			return err
		}
		fileInfo.UploadFileContent = fileContent
	}
	return nil
}

func ReadFileContent(sfsFilePath string) ([]string, error) {
	if sfsFilePath == "" {
		return []string{}, nil
	}
	absFilePath := ToAbsFilePath(sfsFilePath)
	var lines []string
	file, err := os.Open(absFilePath)
	if err != nil {
		return nil, fmt.Errorf("ReadFileContent: open file %s failed: %v", absFilePath, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("ReadFileContent: read file %s failed: %v", absFilePath, err)
	}
	return lines, nil
}
func ToAbsFilePath(sfsPath string) string {
	p := sfsPath
	p = strings.TrimPrefix(p, sfsPrefix)
	p = path.Join(conf.Config.StorageRoot, p)
	return p
}
