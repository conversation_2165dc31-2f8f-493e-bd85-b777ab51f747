package widgets

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeyCustomWidget 自定义算子
	WidgetKeyCustomWidget = "WidgetKeyCustomWidget"
)

type customWidget struct {
}

func (c customWidget) ScriptConfig() *ScriptConfig {
	return nil
}

// WidgetFuncCall 具体定义暂时由/dynamic_widget接口返回
// DynamicEndPoint  算子的输入、输出端点个数可变化,比如数据合并、问题分类等特殊算子
var WidgetFuncCall = &Widget{
	Id:              WidgetKeyCustomWidget,
	Name:            "自定义算子(已发布)",
	Desc:            "用于调用所发布的自定义算子",
	Group:           WidgetGroupAdvanced,
	DynamicEndPoint: true,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入",
				Desc: "输入",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出",
			},
		},
	},
}

func (c customWidget) Define() *Widget {
	return WidgetFuncCall
}

func (c customWidget) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	address, err := getNodeValueToString(nodeValue, "Address", false)
	if err != nil {
		return nil, err
	}
	// timeout, err := getNodeValueToTimeSecond(nodeValue, "Timeout", false)
	// if err != nil {
	//	return nil, err
	// }
	// params := make(map[string]interface{})
	// for k, v := range nodeValue {
	//	if strings.HasPrefix(k, "param_") {
	//		params[k] = v
	//	}
	// }
	// payloadBytes, err := json.Marshal(params)
	if err != nil {
		return nil, stderr.Wrap(err, "customWidget gen script err")
	}
	return script.HttpCall{
		Meta:    nodeMeta.ToBaseScript(),
		URL:     address,
		Timeout: 60,
		Method:  "POST",
	}, nil
}
