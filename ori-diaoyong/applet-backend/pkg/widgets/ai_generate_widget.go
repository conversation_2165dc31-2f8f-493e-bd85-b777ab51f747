package widgets

import "encoding/json"

type AIParamType = string

const (
	AIParamTypeUserInput = "user-input"
	AIParamTypeInput     = "in-port"
	AIParamTypeAttribute = "attribute"
	AIParamTypeOutPut    = "out-port"
)

type AIWidgetDesc struct {
	WidgetID     string              `json:"widget_id"`
	WidgetName   string              `json:"widget_name"`
	WidgetDesc   string              `json:"widget_desc"`
	WidgetParams []AIWidgetParamDesc `json:"widget_params"`
}

type AIWidgetParamDesc struct {
	ID          string `json:"id"`
	Type        string `json:"type"`
	Desc        string `json:"desc"`
	SampleValue string `json:"sample_value"`
}

func (a AIWidgetDesc) Marshal() (string, error) {
	bytes, err := json.MarshalIndent(a, "", "  ")
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func GenerateWidgetDesc() (string, error) {
	res := ""
	widgets := ListAIGenerateWidgets()
	for _, w := range widgets {
		str, err := w.AIDesc().Marshal()
		if err != nil {
			return "", err
		}
		res += "\n"
		res += str
	}
	return res, nil
}

func ListAIGenerateWidgets() []AIGenerateWidget {
	res := make([]AIGenerateWidget, 0)
	res = append(res, llm{}, pythonWidget{}, textInputs{})
	return res
}

type AIGenerateWidget interface {
	AIDesc() AIWidgetDesc
}
