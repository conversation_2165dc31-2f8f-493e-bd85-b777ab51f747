package widgets

import (
	"fmt"
	"net/url"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextParse = "WidgetKeyTextParse"
)

const (
	TextParseDesc          = "使用平台提供的解析引擎，将文件转成DocElement结构的json输出（DocElement规范详见使用文档）"
	TextParseInPortDesc    = "SFSFile: { name(string) uid(string) url(string) content([]byte, http请求时自动转为base64) }"
	ParamIDAdvancedOptions = "AdvancedOptions"

	ParamIDOcrModelService   = "OcrModelService"
	ParamIDImageModelService = "ImageModelService"

	OptionSave2MD         = "Save2MD"
	OptionFigure          = "Figure"
	OptionTable           = "Table"
	OptionChemicalFormula = "ChemicalFormula"
	OptionMathFormula     = "MathFormula"

	ParamIDSavePath          = "SavePath"
	ParamIDUseDefaultPath    = "UseDefaultSavePath"
	QueryParamForcePartition = "forcePartition"

	QueryParamSeparator = "separator"
	DefaultSeparator    = "\n"

	QueryParamStrategy = "strategy"
	StrategyAuto       = "auto"
	StrategyHiRes      = "hi_res"
	StrategyFast       = "fast"
	StrategyVlModel    = "vl_model"
	QueryParamJson     = "json"
	QueryParamTopK     = "topK"
)

var (
	textParseQuerys = []string{
		QueryParamSeparator,
		QueryParamStrategy,
		QueryParamJson,
		QueryParamTopK,
	}
)

// textParse 调用applet-doc的服务进行文档解析
// 默认返回纯文本,也可选择返回json结构数据
type textParse struct {
}

var widgetTextParse = &Widget{
	Id:    WidgetKeyTextParse,
	Name:  "标准文档解析",
	Desc:  TextParseDesc,
	Group: WidgetGroupProcessKnowledge,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile),
			Define: DynamicParam{
				Id:       "File",
				Name:     "文件",
				Desc:     BaseSyncLimits(DataTypeSFSFile).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamForcePartition,
				Name:         "强制解析",
				Desc:         "开启强制解析时,解析服务会忽略缓存,降低解析效率",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamStrategy,
				Name:         "解析策略",
				Desc:         "文件解析策略, 默认为自动策略",
				Type:         pb.DynamicParam_TYPE_SELECTOR,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Datasource:   fmt.Sprintf("%s@@快速模式,%s@@高精度模型策略,%s@@大模型策略", StrategyFast, StrategyHiRes, StrategyVlModel),
				DefaultValue: StrategyFast,
			},
		},

		// 解析模型配置 ocr模型
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDOcrModelService,
				Name:         "解析模型",
				Desc:         "使用OCR模型辅助文档解析",
				Type:         pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource:   GetOcrModelSvcConditionsStr(),
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildAnyPreconditionString(QueryParamStrategy, []string{StrategyVlModel, StrategyHiRes}),
			},
		},

		// 图像理解模型配置
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDImageModelService,
				Name:         "图像理解模型",
				Desc:         "使用图像理解模型辅助文档解析",
				Type:         pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource:   GetImage2TextModelSvcConditionsStr(),
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildAnyPreconditionString(QueryParamStrategy, []string{StrategyVlModel}),
			},
		},

		// 策略配置
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:   ParamIDAdvancedOptions,
				Name: "策略配置",
				Desc: "控制解析策略的具体行为",
				Type: pb.DynamicParam_TYPE_SELECTOR,
				Datasource: fmt.Sprintf("%s@@识别数学公式,%s@@识别化学公式,%s@@保留图片,%s@@保留表格",
					OptionMathFormula, OptionChemicalFormula, OptionFigure, OptionTable),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Multiple: true,
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDUseDefaultPath,
				Name:         "默认路径",
				Desc:         "默认将文件保存至各个租户的目录下，例如sfs:///tenants/dev-assets/projs/assets/",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "true",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDSavePath,
				Name:         "自定义路径",
				Desc:         "文本解析信息的的保存地址，例如sfs:///tenants/dev-assets/projs/assets/",
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Precondition: BuildBothPreconditionString(ParamIDUseDefaultPath, ParamValueFalse),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamJson,
				Name:         "以json格式返回",
				Desc:         "启用则返回文档元素列表(ElementsV2),不启用则返回纯文本(String)",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "true",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamSeparator,
				Name:         "文本拼接字符",
				Desc:         "拼接文本使用的字符，默认为'\\n'",
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				DefaultValue: DefaultSeparator,
				Precondition: BuildBothPreconditionString(QueryParamJson, ParamValueFalse),
			},
		},

		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeElementsV2),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "解析后的文本，根据配置输出不同类型，" + BaseSyncLimits(DataTypeString, DataTypeElementsV2).String(),
			},
		},
	},
}

func (f textParse) Define() *Widget {
	return widgetTextParse
}

func (f textParse) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	//设置url
	baseURL := conf.GetDocSvcAddr() + conf.Config.DocSvcConfig.LoadrawApi.Url
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}

	//设置queryParam
	q := u.Query()
	projectID, tenantID, token, err := getProjectID(&nodeMeta)
	if err != nil {
		return nil, err
	}
	parseStrategy := getStringValueWithDefault(nodeValue, QueryParamStrategy, StrategyFast)
	q.Set(helper.QueryParamTenantID, tenantID)
	q.Set(helper.QueryParamProjectID, projectID)
	q.Set(QueryParamStrategy, fmt.Sprint(parseStrategy))
	q.Set(QueryParamForcePartition, fmt.Sprint(getBoolValueWithDefault(nodeValue, QueryParamForcePartition, false))) // 关闭强制解析文件，使用解析缓存
	q.Set(QueryParamJson, fmt.Sprint(getBoolValueWithDefault(nodeValue, QueryParamJson, false)))
	q.Set(QueryParamSeparator, fmt.Sprint(getStringValueWithDefault(nodeValue, QueryParamSeparator, DefaultSeparator)))
	u.RawQuery = q.Encode()

	//设置bodyParam
	ocrModel, err := GetStringNodeValueTo[pb.ModelService](nodeValue, ParamIDOcrModelService)
	if err != nil {
		return nil, err
	}

	imageModel, err := GetStringNodeValueTo[pb.ModelService](nodeValue, ParamIDImageModelService)
	if err != nil {
		return nil, err
	}
	switch parseStrategy {
	case StrategyHiRes:
		if ocrModel.FullUrl == "" {
			return nil, stderr.Errorf("ocr model is nil for strategy %s", StrategyHiRes)
		}
	case StrategyVlModel:
		if ocrModel.FullUrl == "" || imageModel.FullUrl == "" {
			return nil, stderr.Errorf("ocr or image model is nil for strategy %s", StrategyVlModel)
		}
	}

	savePath := fmt.Sprintf("sfs:///tenants/%s/projs/%s/", tenantID, projectID)
	useDefault := getBoolValueWithDefault(nodeValue, ParamIDUseDefaultPath, false)
	if !useDefault {
		savePath = getStringValueWithDefault(nodeValue, ParamIDSavePath, savePath)
	}
	advancedOptionList := getStringListWithDefault(nodeValue, ParamIDAdvancedOptions, make([]string, 0))
	optionsMap := cvtSlice2Map(advancedOptionList)
	reqBody := &DocLoadRawReqMerge{
		SavePath: savePath,
		ExternalConfig: &ExternalConfig{
			Save2MD:         optionsMap[OptionSave2MD],
			Figure:          optionsMap[OptionFigure],
			Table:           optionsMap[OptionTable],
			MathFormula:     optionsMap[OptionMathFormula],
			ChemicalFormula: optionsMap[OptionChemicalFormula],
		},
		DocApiKey:     token,
		DocServiceUrl: ocrModel.FullUrl,
		ImageApiKey:   token,
		ImageModelUrl: imageModel.FullUrl,
	}

	return script.HttpCall{
		Meta:        nodeMeta.ToBaseScript(),
		URL:         u.String(),
		Header:      "",
		Method:      "POST",
		ServiceType: script.HTTPCallServiceTypeTextParse,
		Timeout:     60,
		Body:        script.MultilineString(stdsrv.AnyToString(reqBody)),
	}, nil
}
