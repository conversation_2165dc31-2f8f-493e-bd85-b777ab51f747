package engine

import (
	"fmt"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

type SFSFile struct {
	Name    string `json:"name"`
	Uid     string `json:"uid"`
	Url     string `json:"url"`
	HttpUrl string `json:"http_url,omitempty"`
	Content []byte `json:"content"`
}

func (s *SFSFile) GetSfsDownloadUrl() string {
	return fmt.Sprintf("[%s](<%s>)", s.Name, s.Url)
}
func GetExampleSFSFile() *SFSFile {
	return &SFSFile{Name: "name", Uid: "uid", Url: "sfs:///tenants/llmops-assets/projs/assets/a.text", Content: []byte("content")}
}
func GetExampleSFSFiles() []*SFSFile {
	return []*SFSFile{GetExampleSFSFile()}
}

type ElementsV2 struct {
	Duration float64          `json:"duration"` //耗时
	Elements []*pb.DocElement `json:"elements"`
}

type SplitResult struct {
	Texts []string `json:"texts"`
}

type VecInsertRes struct {
	Records   int    `json:"records,omitempty"`   // 插入向量数量
	Dimension int    `json:"dimension,omitempty"` // 插入向量维度
	BodySize  int    `json:"bodySize,omitempty"`  // 插入实体二进制数据总量
	Consumed  string `json:"consumed,omitempty"`  // 插入总耗时
}

type WidgetParamsGoto struct {
	MaxLoopRounds int64    `json:"max_loop_rounds"` // 最大循环次数
	TargetNodes   []string `json:"target_nodes"`    // 目标算子ids
}

type WidgetParamsConditionalSwitch struct {
	Condition       string   `json:"condition"`
	TargetNodesIf   []string `json:"target_nodes_if"`
	TargetNodesElse []string `json:"target_nodes_else"`
}

type WidgetParamsQuestionClassifier struct {
	Categories      []*Category                                  `json:"categories"  description:"具体分类信息"`
	EnableAppMode   bool                                         `json:"enable_app_mode" description:"使用应用服务进行问题分类" `
	EnableMutilMode bool                                         `json:"enable_mutil_mode" description:"支持将问题分类到多个类别"`
	CustomPrompt    string                                       `json:"custom_prompt" description:"模型服务提示词"`
	ModelService    *pb.ModelService                             `json:"model_service" description:"模型服务"`
	AppletService   *agent_definition.AppletServiceToolDescriber `json:"applet_service" description:"应用服务"`
}

func (w *WidgetParamsQuestionClassifier) Validate() error {
	if w.EnableAppMode && (w.AppletService == nil || w.AppletService.ID == "") {
		return stderr.Errorf("applet service is nil for app mode")
	}
	if !w.EnableAppMode && (w.ModelService == nil || w.ModelService.Id == "") {
		return stderr.Errorf("model service is nil for default mode")
	}
	if len(w.Categories) < 2 {
		return stderr.Errorf("the len of categories is less than 2")
	}

	defaultNums := 0
	exist := make(map[string]bool)
	for _, c := range w.Categories {
		if c.IsDefault {
			defaultNums++
		}
		if strings.HasPrefix(c.CategoryName, " ") || strings.HasSuffix(c.CategoryName, " ") {
			return stderr.Errorf("category name cannot contains space as prefix or subfix")
		}

		if exist[c.CategoryName] {
			return stderr.Errorf("category name[%s] is replicated", c.CategoryName)
		}
		exist[c.CategoryName] = true
	}
	if defaultNums != 1 {
		return stderr.Errorf("the nums of default categories must be 1")
	}
	return nil
}

// Category 每一个分类传递给多个下游节点
type Category struct {
	IsDefault    bool     `json:"is_default"`
	CategoryName string   `json:"category_name"`
	TargetNodes  []string `json:"target_nodes"`
}

type QuestionClassifyReq struct {
	Question      string   `json:"question" description:"待分类的问题"`
	CategoryNames []string `json:"category_names" description:"所有的类别名称"`
}

func (q *QuestionClassifyReq) Validate() error {
	if helper.IsEmpty(q.Question) {
		return stderr.Errorf("question is empty str")
	}
	if len(q.CategoryNames) == 0 {
		return stderr.Errorf("the len of category name is 0")
	}

	for _, name := range q.CategoryNames {
		if helper.IsEmpty(name) {
			return stderr.Errorf("exist category name with empty str")
		}
	}
	return nil
}

type QuestionClassifyResp struct {
	QuestionClassifyReq
	DecidedCategoryNames []string `json:"decided_category_names" description:"问题归属的类别"`
}

func (w *QuestionClassifyResp) Validate() error {
	if err := w.QuestionClassifyReq.Validate(); err != nil {
		return err
	}
	if len(w.DecidedCategoryNames) == 0 {
		return stderr.Errorf("the len of decided category name is 0")
	}
	categoryNamesMap := helper.CvtSlice2Map(w.CategoryNames, func(name string) string { return name })
	for _, name := range w.DecidedCategoryNames {
		if _, exist := categoryNamesMap[name]; !exist {
			return stderr.Errorf("the decided category name %s is not exist", name)
		}
	}
	return nil
}
