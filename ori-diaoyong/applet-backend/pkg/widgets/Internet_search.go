package widgets

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyInternetSearch       = "WidgetKeyInternetSearch"
	ParamIDInternetSearchInput    = "Input"
	ParamIDInternetSearchEngine   = "Engine"
	ParamIDInternetSearchParseUrl = "ParseUrl"
	ParamIDInternetSearchEnable   = "Enable"
	ParamIDInternetSearchOutput   = "Output"
	urlParamParseUrl              = "parse"

	// 搜索引擎路径常量
	PathSearXNGSearch = "/v1/searxng_search"
	PathBingSearch    = "/v1/bing_search"
)

// internetSearch 互联网搜索
type internetSearch struct {
}

var widgetInternetSearch = &Widget{
	Id:    WidgetKeyInternetSearch,
	Name:  "互联网搜索",
	Desc:  "使用搜索引擎搜索输入的文本",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDInternetSearchInput,
				Name: "文本",
				Desc: "需要进行联网搜索的文本内容，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDInternetSearchEnable,
				Name:     "是否启用",
				Desc:     "是否启用互联网检索算子",
				Type:     pb.DynamicParam_TYPE_SWITCH,
				DataType: pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDInternetSearchEngine,
				Name:         "搜索引擎",
				Desc:         "可选的搜索引擎，默认为必应",
				Type:         pb.DynamicParam_TYPE_SELECTOR,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				DefaultValue: string(script.HTTPCallServiceTypeBingSearch),
				Datasource:   fmt.Sprintf("%s@@必应搜索,%s@@SearXNG搜索", script.HTTPCallServiceTypeBingSearch, script.HTTPCallServiceTypeSearXNGSearch),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDInternetSearchParseUrl,
				Name:         "解析网页",
				Desc:         "是否解析搜索引擎获取的网页详细内容，默认不解析",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeInternetCitations),
			Define: DynamicParam{
				Id:   ParamIDInternetSearchOutput,
				Desc: `搜索后的内容，` + BaseSyncLimits(DataTypeInternetCitations).String(),
			},
		},
	},
}

func (i internetSearch) Define() *Widget {
	return widgetInternetSearch
}

func (i internetSearch) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	searchEngine, err := getNodeValueToString(nodeValue, ParamIDInternetSearchEngine, true)
	if err != nil {
		return nil, stderr.Wrap(err, "invalid search engine value")
	}
	needParseUrl, err := getNodeValueToBool(nodeValue, ParamIDInternetSearchParseUrl, true)
	if err != nil {
		return nil, stderr.Wrap(err, "invalid get node value needParseUrl to bool")
	}
	enable, err := getNodeValueToBool(nodeValue, ParamIDInternetSearchEnable, true)
	if err != nil {
		return nil, stderr.Wrap(err, "invalid get node value enable to bool")
	}

	engineType := script.HTTPCallServiceType(searchEngine)
	url, err := getSearchUrl(engineType, needParseUrl)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get search url")
	}

	return script.HttpCall{
		Meta:        nodeMeta.ToBaseScript(),
		URL:         url,
		Header:      "",
		Method:      "POST",
		Timeout:     60,
		Escape:      !enable,
		ServiceType: engineType,
	}, nil
}

// getSearchUrl 构建并返回搜索引擎的URL。
// 参数 engineType 指定搜索引擎类型
// 参数 needParseUrl 指示是否需要解析URL。
func getSearchUrl(engineType script.HTTPCallServiceType, needParseUrl bool) (string, error) {
	var path string

	switch engineType {
	case script.HTTPCallServiceTypeSearXNGSearch:
		path = PathSearXNGSearch
	case script.HTTPCallServiceTypeBingSearch:
		path = PathBingSearch
	default:
		return "", stderr.Errorf("不支持的搜索引擎类型: %s", engineType)
	}

	// 获取基础URL并替换命名空间占位符
	builtinToolBaseUrl := strings.ReplaceAll(conf.Config.APIToolConfig.BuiltinToolBaseUrl, conf.NamespacePlaceholder, k8s.CurrentNamespaceInCluster())

	// 解析基础URL
	baseU, err := url.Parse(builtinToolBaseUrl)
	if err != nil {
		return "", stderr.Errorf("解析基础URL失败: %v", err)
	}

	// 设置路径
	baseU.Path = path

	// 设置查询参数
	q := baseU.Query()
	q.Set(urlParamParseUrl, strconv.FormatBool(needParseUrl))
	baseU.RawQuery = q.Encode()

	// 返回构建好的URL字符串
	return baseU.String(), nil
}
