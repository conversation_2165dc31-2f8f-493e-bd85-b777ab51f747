package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextShow = "WidgetKeyTextShow"
)

type textShow struct {
}

var widgetTextShow = &Widget{
	Id:    WidgetKeyTextShow,
	Name:  "文本呈现",
	Desc:  "用于对上游输出的文本增加自定义前后缀",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "Input",
				Name: "文本输入",
				Desc: "文本输入," + BaseAnyLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "Prefix",
				Name:     "前缀",
				Desc:     "前缀",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "Suffix",
				Name:     "后缀",
				Desc:     "后缀",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出," + BaseAnyLimits(DataTypeString).String(),
			},
		},
	},
}

func (f textShow) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	prefix, err := getNodeValueToString(nodeValue, "Prefix", true)
	if err != nil {
		return nil, err
	}
	suffix, err := getNodeValueToString(nodeValue, "Suffix", true)
	if err != nil {
		return nil, err
	}
	return script.TextDisplay{
		Meta:   nodeMeta.ToBaseScript(),
		Prefix: script.MultilineString(prefix),
		Suffix: script.MultilineString(suffix),
	}, nil
}

func (f textShow) Define() *Widget {
	return widgetTextShow
}
