package widgets

import (
	"context"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyOutputGuardrail         = "WidgetKeyOutputGuardrail"
	OutputGuardrailParamIDInput      = "Input"
	OutputGuardrailParamIDStrategy   = "Strategy"
	OutputGuardrailParamIDStrategyID = "StrategyID"
)

type outputGuardrail struct {
}

var widgetOutputGuardrail = &Widget{
	Id:    WidgetKeyOutputGuardrail,
	Name:  "输出安全护栏",
	Desc:  "针对输出进行安全检测并配置干预话术",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:       OutputGuardrailParamIDInput,
				Name:     "待检测文本",
				Desc:     "待检测文本," + BaseAnyLimits(DataTypeString).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       OutputGuardrailParamIDStrategy,
				Name:     "输出安全策略",
				Desc:     "点击配置提示词注入、敏感词防护等安全策略详情",
				Type:     pb.DynamicParam_TYPE_GUARDRAIL_OUTPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       OutputGuardrailParamIDStrategyID,
				Name:     "配置策略id",
				Desc:     "配置策略id",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Hidden:   true,
			},
		},

		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "通过安全防护的文本，" + BaseAnyLimits(DataTypeString).String(),
			},
		},
	},
}

type WidgetParamsOutputGuardrail WidgetParamsInputGuardrail

func (f outputGuardrail) Define() *Widget {
	return widgetOutputGuardrail
}

func (f outputGuardrail) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	outputGuardrails, err := GetStringNodeValueTo[OutputGuardrails](nodeValue, OutputGuardrailParamIDStrategy)
	if err != nil {
		return nil, err
	}
	widgetParams := &WidgetParamsOutputGuardrail{
		SafetyConfig: SafetyConfig{
			ID:               nodeMeta.NodeID,
			ProjectID:        nodeMeta.NodeID,
			InputGuardrails:  &InputGuardrails{},
			OutputGuardrails: outputGuardrails},
	}
	switch conf.Config.IsSimpleMode {
	case true: // 轻量化
		widgetParams.Enable = false
	case false:
		widgetParams.Enable = bool(outputGuardrails.SensitiveProtection.SensitiveProtectionEnabled)
		if err := UpdateGuardrailsServerConfig(context.Background(), &widgetParams.SafetyConfig); err != nil {
			return nil, stderr.Wrap(err, "update guardrail service config")
		}
	}
	return script.OutputGuardrail{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(widgetParams)),
	}, nil
}
