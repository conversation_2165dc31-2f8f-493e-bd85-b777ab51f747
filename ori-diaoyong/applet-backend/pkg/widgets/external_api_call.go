package widgets

import (
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeyExternalAPICall 远程模型
	WidgetKeyExternalAPICall = "WidgetKeyExternalAPICall"
)

type externalAPICall struct {
}

var WidgetExternalAPICall = &Widget{
	Id:    WidgetKeyExternalAPICall,
	Name:  "远程模型调用",
	Desc:  "使用上同\"通用HTTP调用\"，为减少用户多次使用相同服务需要反复填写API地址与请求头，故使用远程模型预填充API地址与请求头",
	Group: WidgetGroupChainTool,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入",
				Desc: "输入",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出",
			},
		},
	},
}

func (e externalAPICall) Define() *Widget {
	return WidgetExternalAPICall
}

func (e externalAPICall) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	url, err := getNodeValueToString(nodeValue, "URL", false)
	if err != nil {
		return nil, err
	}
	header, err := getHeaderJSON(nodeValue, "Header", true)
	if err != nil {
		return nil, err
	}
	return script.HttpCall{
		Meta:    nodeMeta.ToBaseScript(),
		URL:     url,
		Header:  script.MultilineString(header),
		Method:  "POST",
		Timeout: 60,
	}, nil
}
