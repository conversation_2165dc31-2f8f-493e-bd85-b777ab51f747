package widgets

import (
	"fmt"
	"sync"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
)

// DataTypeDesc 传输类型简介
type DataTypeDesc struct {
	Type    WidgetParamDataType `json:"type"`    //数据类型id
	Desc    string              `json:"desc"`    // 简要描述
	Example any                 `json:"example"` //数据类型示例
}

// ModeTypeDesc 数据流转传输方式-流式/同步/any
type ModeTypeDesc struct {
	Type WidgetParamModeType `json:"type"` //流转类型id
	Name string              `json:"name"`
	Desc string              `json:"desc"` //简单描述
}

const (
	ModeTypeAny                  WidgetParamModeType = "Any"
	ModeTypeSync                 WidgetParamModeType = "Sync"
	ModeTypeStream               WidgetParamModeType = "Stream"
	DataTypeAny                  WidgetParamDataType = "Any"
	DataTypeString               WidgetParamDataType = "String"
	DataTypeJsonString           WidgetParamDataType = "JsonString"
	DataTypeHtmlString           WidgetParamDataType = "HtmlString"
	DataTypeSfsUrlString         WidgetParamDataType = "SfsUrlString"
	DataTypeHttpUrlString        WidgetParamDataType = "HttpUrlString"
	DataTypeStrings              WidgetParamDataType = "Strings"
	DataTypeChunk                WidgetParamDataType = "Chunk"
	DataTypeChunks               WidgetParamDataType = "Chunks"
	DataTypeElement              WidgetParamDataType = "Element"
	DataTypeElements             WidgetParamDataType = "Elements"
	DataTypeElementsV2           WidgetParamDataType = "ElementsV2"
	DataTypeQAItems              WidgetParamDataType = "QAItems"
	DataTypeSFSFile              WidgetParamDataType = "SFSFile"
	DataTypeSFSFiles             WidgetParamDataType = "SFSFiles"
	DataTypeVectorInsert         WidgetParamDataType = "VectorInsert"
	DataTypeLoadChunk            WidgetParamDataType = "LoadChunk"
	DataTypeInternetCitation     WidgetParamDataType = "InternetCitation"
	DataTypeInternetCitations    WidgetParamDataType = "InternetCitations"
	DataTypeGeneralMap           WidgetParamDataType = "map[string]any"
	DataTypeGeneralMaps          WidgetParamDataType = "[]map[string]any"
	DataTypeVecInsertRes         WidgetParamDataType = "VecInsertRes"
	DataTypeAudioTranResp        WidgetParamDataType = "AudioTranResp"
	DataTypeImageGenResp         WidgetParamDataType = "ImageGenResp"
	DataTypeQuestionClassifyReq  WidgetParamDataType = "QuestionClassifyReq"
	DataTypeQuestionClassifyResp WidgetParamDataType = "QuestionClassifyResp"
)

var paramsFactory *widgetParamFactory
var paramsFactorySO sync.Once

func GetParamsFactory() *widgetParamFactory {
	paramsFactorySO.Do(func() {
		paramsFactory = initWidgetParamFactory()
	})
	return paramsFactory
}

func newEmptyFactory() *widgetParamFactory {
	return &widgetParamFactory{
		modeTypes: make(map[WidgetParamModeType]*ModeTypeDesc),
		dataTypes: make(map[WidgetParamDataType]*DataTypeDesc),
	}
}

type widgetParamFactory struct {
	modeTypes map[WidgetParamModeType]*ModeTypeDesc
	dataTypes map[WidgetParamDataType]*DataTypeDesc
}

func (w *widgetParamFactory) ListModeTypeDesc() map[WidgetParamModeType]*ModeTypeDesc {
	return w.modeTypes
}
func (w *widgetParamFactory) GetModeTypeDesc(mType WidgetParamModeType) (*ModeTypeDesc, error) {
	res := w.modeTypes[mType]
	if res == nil {
		return nil, stderr.Errorf("fail to get mode type desc from factory with key %s", mType)
	}
	return res, nil
}

func (w *widgetParamFactory) registerModeTypes(modes ...*ModeTypeDesc) {
	for _, mode := range modes {
		mType := mode.Type
		if _, ok := w.modeTypes[mType]; ok {
			panic(stderr.Errorf("the type %s of mode is repeated", mType))
		}
		w.modeTypes[mType] = mode
	}
}

func (w *widgetParamFactory) ListDataTypeDesc() map[WidgetParamDataType]*DataTypeDesc {
	return w.dataTypes
}
func (w *widgetParamFactory) GetDataTypeDesc(dType WidgetParamDataType) (*DataTypeDesc, error) {
	res := w.dataTypes[dType]
	if res == nil {
		return nil, stderr.Errorf("fail to get data type desc from factory with key %s", dType)
	}
	return res, nil
}

func (w *widgetParamFactory) registerDataTypes(datas ...*DataTypeDesc) {
	for _, data := range datas {
		dType := data.Type
		if _, ok := w.dataTypes[dType]; ok {
			panic(stderr.Errorf("the type %s of data is repeated", data))
		}
		w.dataTypes[dType] = data
	}
}

// initWidgetParamFactory TODO 如何自动同步各算子params的定义与具体实现
func initWidgetParamFactory() *widgetParamFactory {
	factory := newEmptyFactory()
	// 传输方式
	factory.registerModeTypes(
		&ModeTypeDesc{
			Type: ModeTypeAny,
			Name: "任意传输方式",
			Desc: "可以是流式或同步传输方式中的任意一种。",
		},
		&ModeTypeDesc{
			Type: ModeTypeStream,
			Name: "流式传输方式",
			Desc: "算子节点每执行一次,可能会产生多次输出,输入同理。",
		},
		&ModeTypeDesc{
			Type: ModeTypeSync,
			Name: "同步传输方式",
			Desc: "算子节点每执行一次,只会产生一次输出,输入同理。",
		},
	)

	sfsExample := engine.GetExampleSFSFile()
	eleExample := &pb.DocElement{ElementId: "id", Type: 0, Text: "text"}
	chunkExample := &pb.Chunk{Id: "id", Content: "content", ElementIds: []string{"id1", "id2"}, SourceType: 0, ContentType: 0}
	internetCitationExample := &agent_executor.Citation{Content: "content", CitationType: agent_executor.CitationTypeInternetSearch, InternetSearchDetail: &agent_executor.InternetSearchDetails{Title: "title", Url: "url", Snippet: "snippet"}}
	// 数据类型
	factory.registerDataTypes(
		&DataTypeDesc{
			Type:    DataTypeAny,
			Desc:    "任意数据类型",
			Example: "{}、text、[{}]...",
		},
		&DataTypeDesc{
			Type:    DataTypeString,
			Desc:    "字符串类型",
			Example: "text",
		},
		&DataTypeDesc{
			Type:    DataTypeJsonString,
			Desc:    "满足json格式的字符串",
			Example: `{"key":"value"}, ["value1"], "text", ...`,
		},
		&DataTypeDesc{
			Type:    DataTypeHtmlString,
			Desc:    "满足html格式的字符串",
			Example: `<!DOCTYPE html><html><body><h1>Hello, World!</h1><img src="image.jpg" alt="Example Image" /></body></html>`,
		},
		&DataTypeDesc{
			Type:    DataTypeSfsUrlString,
			Desc:    "满足sfs地址格式的字符串",
			Example: "sfs:///tenants/llmops-assets/projs/assets/a.text",
		},
		&DataTypeDesc{
			Type:    DataTypeHttpUrlString,
			Desc:    "满足http接口格式的字符串",
			Example: "https://example.com/api/v1",
		},
		&DataTypeDesc{
			Type:    DataTypeStrings,
			Desc:    "字符串列表",
			Example: []string{"text1", "text2"},
		},
		&DataTypeDesc{
			Type:    DataTypeChunk,
			Desc:    fmt.Sprintf("%s类型,主要用于定义知识库检索的单个结果", DataTypeChunk),
			Example: chunkExample,
		},
		&DataTypeDesc{
			Type:    DataTypeChunks,
			Desc:    fmt.Sprintf("%s类型,主要用于存储知识库的所有检索结果", DataTypeChunks),
			Example: []*pb.Chunk{chunkExample},
		},
		&DataTypeDesc{
			Type:    DataTypeElement,
			Desc:    fmt.Sprintf("%s类型,主要用于定义文档解析的单个结果", DataTypeElement),
			Example: eleExample,
		},
		&DataTypeDesc{
			Type:    DataTypeElements,
			Desc:    fmt.Sprintf("%s类型,主要用于存储文档解析的所有结果", DataTypeElements),
			Example: []*pb.DocElement{eleExample},
		},
		&DataTypeDesc{
			Type:    DataTypeElementsV2,
			Desc:    fmt.Sprintf("%s类型,用于存储文档解析的所有结果及其他信息", DataTypeElementsV2),
			Example: &engine.ElementsV2{Duration: 0.8, Elements: []*pb.DocElement{eleExample}},
		},
		&DataTypeDesc{
			Type:    DataTypeQAItems,
			Desc:    fmt.Sprintf("%s类型,用于存储用户对话历史", DataTypeQAItems),
			Example: []*triton.QAItem{{Q: "Q1", A: "A1"}, {Q: "Q2", A: "A2"}},
		},
		&DataTypeDesc{
			Type:    DataTypeSFSFile,
			Desc:    fmt.Sprintf("%s类型,用于存储单个sfs文件的具体信息", DataTypeSFSFile),
			Example: sfsExample,
		},
		&DataTypeDesc{
			Type:    DataTypeSFSFiles,
			Desc:    fmt.Sprintf("%s类型,用于存储多个sfs文件的具体信息", DataTypeSFSFiles),
			Example: []*engine.SFSFile{sfsExample},
		},
		&DataTypeDesc{
			Type: DataTypeVectorInsert,
			Desc: fmt.Sprintf("%s类型,用于定义向量数据库相关算子的输入输出格式", DataTypeVectorInsert),
			Example: &NodeInPortVectorInsert{
				FileName:             "name",
				OpenAiTextVectorReq:  new(triton.OpenAiTextVectorReq),
				OpenAiTextVectorResp: new(triton.OpenAiTextVectorResp),
			},
		},
		&DataTypeDesc{
			Type: DataTypeLoadChunk,
			Desc: fmt.Sprintf("%s类型,用于定义知识库加工策略的最终输出格式", DataTypeLoadChunk),
			Example: &pb.DocSvcLoadChunkRsp{
				Chunks:   []*pb.Chunk{chunkExample},
				Elements: []*pb.DocElement{eleExample},
			},
		},
		&DataTypeDesc{
			Type:    DataTypeInternetCitation,
			Desc:    fmt.Sprintf("%s类型,用于定义联网检索的单个结果", DataTypeInternetCitation),
			Example: internetCitationExample,
		},
		&DataTypeDesc{
			Type:    DataTypeInternetCitations,
			Desc:    fmt.Sprintf("%s类型,用于存储联网检索的所有结果", DataTypeInternetCitations),
			Example: []*agent_executor.Citation{internetCitationExample},
		},
		&DataTypeDesc{
			Type:    DataTypeGeneralMap,
			Desc:    fmt.Sprintf("%s类型,value字段可为任意类型", DataTypeGeneralMap),
			Example: map[string]any{"k1": "v1", "k2": 2},
		},
		&DataTypeDesc{
			Type:    DataTypeGeneralMaps,
			Desc:    fmt.Sprintf("%s类型,表示map[string]any类型的列表, map的value字段可为任意类型", DataTypeGeneralMaps),
			Example: []map[string]any{{"k1": "v1", "k2": 2}, {"k3": "v3", "k4": 4}},
		},
		&DataTypeDesc{
			Type:    DataTypeVecInsertRes,
			Desc:    fmt.Sprintf("%s类型,向量库写入时的返回结果", DataTypeVecInsertRes),
			Example: &engine.VecInsertRes{Records: 10, Dimension: 1024, BodySize: 1024, Consumed: "0.1s"},
		},

		&DataTypeDesc{
			Type: DataTypeAudioTranResp,
			Desc: fmt.Sprintf("%s类型,音频文件识别结果,满足openai格式", DataTypeAudioTranResp),
			Example: &triton.OpenAiAudioTransResp{
				Text:     "text",
				Language: "language",
				Words:    []*triton.Word{{Word: "word", Start: 0, End: 0}},
				Segments: []*triton.Segment{{Text: "text", Start: 0, End: 0}}},
		},
		&DataTypeDesc{
			Type: DataTypeImageGenResp,
			Desc: fmt.Sprintf("%s类型,图像生成调用结果,满足openai格式", DataTypeImageGenResp),
			Example: &triton.OpenAiImageGenResp{
				Created: time.Now().UnixMilli(),
				Data: []triton.Image{
					{
						URL:           sfsExample.Url,
						B64JSON:       "{b64_json}",
						RevisedPrompt: "prompt",
					},
				},
			},
		},
		&DataTypeDesc{
			Type: DataTypeQuestionClassifyReq,
			Desc: fmt.Sprintf("%s类型,问题分类服务具体输入格式", DataTypeQuestionClassifyReq),
			Example: engine.QuestionClassifyReq{
				Question:      "待分类问题",
				CategoryNames: []string{"类别1", "类别2"},
			},
		},
		&DataTypeDesc{
			Type: DataTypeQuestionClassifyResp,
			Desc: fmt.Sprintf("%s类型,问题分类服务具体输出格式", DataTypeQuestionClassifyResp),
			Example: engine.QuestionClassifyResp{
				QuestionClassifyReq: engine.QuestionClassifyReq{
					Question:      "待分类问题",
					CategoryNames: []string{"类别1", "类别2"},
				},
				DecidedCategoryNames: []string{"类别1"},
			},
		},
	)
	return factory
}
