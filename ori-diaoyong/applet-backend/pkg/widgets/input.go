package widgets

import (
	"encoding/json"
	"strconv"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyFileInput      = "WidgetKeyFileInput"
	WidgetKeyFileRead       = "WidgetKeyFileRead"
	WidgetKeyFileParse      = "WidgetKeyFileParse"
	WidgetKeyBatchFileInput = "WidgetKeyBatchFileInput"
	WidgetKeyTextInput      = "WidgetKeyTextInput"
	ParamIDTextInput        = "TextInput"
	ParamIDFileInput        = "FileInput"

	ParamIDFileInputIsFileContentRead = "IsFileContentRead"
	ParamIDFileInputMaxFileSizeMB     = "MaxFileSizeMB"
	ParamIDFileInputAllowedExtensions = "AllowedExtensions"
	ParamIDFileInputMutilFileMode     = "EnableEnhanceMode"
	ParamIDFileInputUnpackMode        = "UnpackMode"
)

const (
	FileInputDesc        = "用于加载上传的单个文件, 输出SFSFile类型数据"
	FileInputOutPortDesc = `上传的文件内容, SFSFile: { "name": "xx", "uid": "xx", "url": "xx", "content": [23, 43]([]byte, http请求时自动转为base64) }`
)

type fileInputs struct {
}

var widgetFileInput = &Widget{
	Id:    WidgetKeyFileInput,
	Name:  "文件上传",
	Desc:  FileInputDesc,
	Group: WidgetGroupInput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassFile,
			Category:    ParamTypeReqInput,
			ParamLimits: BaseSyncLimits(DataTypeSFSFiles),
			Define: DynamicParam{
				Id:         ParamIDFileInput,
				Name:       "上传文件",
				Desc:       "上传文件," + BaseSyncLimits(DataTypeSFSFiles).String(),
				Required:   true,
				Datasource: "txt",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileInputIsFileContentRead,
				Name:         "读取文件内容",
				Desc:         "开启后会读取文件内容并向下游节点传递;当文件较大且不涉及远程服务时,可关闭此选项优化性能",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "true",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileInputMaxFileSizeMB,
				Name:         "最大文件大小(MB)",
				Desc:         "允许上传的最大文件大小，单位为MB",
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Type:         pb.DynamicParam_TYPE_NUMBER,
				Required:     true,
				DefaultValue: strconv.Itoa(script.FileInputDefaultMaxFileSizeMB),
				NumberRange: &pb.NumberRange{
					Min: script.FileInputMinMaxFileSizeMB,
					Max: script.FileInputMaxMaxFileSizeMB,
				},
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileInputAllowedExtensions,
				Name:         "允许的文件扩展名",
				Desc:         "允许上传的文件扩展名列表，每行只允许输入一个扩展名，可输入*、txt、pdf、docx等，当输入 * 或者未输入任何扩展名时表示允许上传所有格式的文件",
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Multiple:     true,
				DefaultValue: script.FileInputDefaultAllowedExtensions,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:   ParamIDFileInputMutilFileMode,
				Name: "多文件模式",
				Desc: "开启后支持上传多个文件,并且最终输出文件列表。" +
					"默认关闭,请在有需要的时候开启该模式。当前大部分算子对接的是单个文件,随意开启该模式可能导致错误。",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:   ParamIDFileInputUnpackMode,
				Name: "解压缩模式",
				Desc: "开启后自动对扩展名为tar、gz、zip的文件进行解压,并且最终输出文件列表。" +
					"默认关闭,请在有需要的时候开启该模式。当前大部分算子对接的是单个文件,随意开启该模式可能导致错误。",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile, DataTypeSFSFiles),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeSFSFile, DataTypeSFSFiles).String(),
			},
		},
	},
}

func (f fileInputs) Define() *Widget {
	return widgetFileInput
}

func (f fileInputs) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	isFileContentRead := getBoolValueWithDefault(nodeValue, ParamIDFileInputIsFileContentRead, true)
	maxFileSizeMB := getInt64ValueWithDefault(nodeValue, ParamIDFileInputMaxFileSizeMB, script.FileInputDefaultMaxFileSizeMB)
	allowedExtensions := getStringListWithDefault(nodeValue, ParamIDFileInputAllowedExtensions, script.FileInputDefaultAllowedExtensionsSlice)
	params := script.WidgetParamsFileInput{
		IsFileContentRead:   isFileContentRead,
		MaxFileSizeMB:       int(maxFileSizeMB),
		AllowedExtensions:   allowedExtensions,
		EnableUnpackMode:    getBoolValueWithDefault(nodeValue, ParamIDFileInputUnpackMode, false),
		EnableMutilFileMode: getBoolValueWithDefault(nodeValue, ParamIDFileInputMutilFileMode, false),
	}

	paramsBytes, err := json.Marshal(params)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to marshal WidgetParamsFileInput")
	}

	return script.FileInput{
		Meta:     nodeMeta.ToBaseScript(),
		NodeID:   nodeMeta.NodeID,
		InputKey: ParamIDFileInput,
		Params:   script.MultilineString(paramsBytes),
	}, nil
}

type batchFileInputs struct {
}

var widgetBatchFileInput = &Widget{
	Id:    WidgetKeyBatchFileInput,
	Name:  "批量文件输入",
	Desc:  "批量读取文件内容",
	Group: WidgetGroupInput,
	Params: []WidgetParam{
		{
			DataClass: DataClassFile,
			Category:  ParamTypeReqInput,
			Define: DynamicParam{
				Id:         ParamIDFileInput,
				Name:       "上传文件",
				Desc:       "上传文件",
				Multiple:   true,
				Maxlen:     10,
				Datasource: "txt",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (b batchFileInputs) Define() *Widget {
	return widgetBatchFileInput
}

func (b batchFileInputs) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.FileInput{
		Meta:     nodeMeta.ToBaseScript(),
		NodeID:   b.Define().Id,
		InputKey: ParamIDFileInput,
	}, nil
}

type textInputs struct {
}

var widgetTextInput = &Widget{
	Id:    WidgetKeyTextInput,
	Name:  "文本输入",
	Desc:  "用于将输入的文本原样输出",
	Group: WidgetGroupInput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeReqInput,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       ParamIDTextInput,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Name:     "文本输入",
				Desc:     "文本输入," + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (t textInputs) Define() *Widget {
	return widgetTextInput
}

func (t textInputs) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.TextInput{
		Meta:     nodeMeta.ToBaseScript(),
		InputKey: ParamIDTextInput,
	}, nil
}

func (t textInputs) AIDesc() AIWidgetDesc {
	params := []AIWidgetParamDesc{
		{
			ID:   ParamIDTextInput,
			Type: AIParamTypeUserInput,
			Desc: "接受用户输入",
		},
		{
			ID:   "OutPut",
			Type: AIParamTypeOutPut,
			Desc: "输出端点",
		},
	}
	return AIWidgetDesc{
		WidgetID:     t.Define().Id,
		WidgetName:   t.Define().Name,
		WidgetDesc:   "用来接收用户的输入文本，通常用于工作流的入口",
		WidgetParams: params,
	}
}
