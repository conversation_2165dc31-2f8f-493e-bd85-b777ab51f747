package widgets

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

type ChainNodeType uint16

const (
	ChainNodeTypeWidget   ChainNodeType = 0
	ChainNodeTypeSubChain ChainNodeType = 1
)

const (
	NodeInputInfoKey  = "NODE-INPUT-INFO"
	NodeOutputInfoKey = "NODE-OUTPUT-INFO"
)

// Chain  chainDetail
type Chain struct {
	Nodes    []ChainNode `json:"nodes"`          // 管线-点集
	Edges    []ChainEdge `json:"edges"`          // 管线-边集
	Viewport Viewport    `json:"viewport"`       // 画布视角
	Tips     []Tip       `json:"tips,omitempty"` // 便签
}

// SubChain 子链详情
type SubChain struct {
	ChainID  string      `json:"chain_id"` // 应用链ID
	Name     string      `json:"name"`     // 应用链Name
	Nodes    []ChainNode `json:"nodes"`    // 管线-点集
	Edges    []ChainEdge `json:"edges"`    // 管线-边集
	Viewport Viewport    `json:"viewport"` // 画布视角
}

type Viewport struct {
	X    float64 `json:"x"`
	Y    float64 `json:"y"`
	Zoom float64 `json:"zoom"`
}

// ChainEdge 表示应用链中的一条边， 必须从一个算子的输出参数（out-port）指向另一个算子的输入参数（in-port)
// 同时两端的数据类型必须相匹配

type ChainEdge struct {
	Id          string `json:"id"`
	SourceNode  string `json:"source"`
	SourceParam string `json:"source_param"`
	TargetNode  string `json:"target"`
	TargetParam string `json:"target_param"`
}

type ChainNode struct {
	Id               string                 `json:"id"`                  // 节点ID, 前端自动生成
	Name             string                 `json:"name"`                // 节点名称, 前端填写
	WidgetId         string                 `json:"widget_id"`           // 对应的算子ID
	WidgetDetail     *Widget                `json:"widget_detail"`       // 对应的算子详情
	UI               string                 `json:"ui"`                  // 前端存储的节点UI相关的信息，后端不负责解析
	Values           map[string]interface{} `json:"values"`              // 用户填写的参数配置
	SubChainBaseInfo *SubChainBaseInfo      `json:"sub_chain_base_info"` // 子链基础信息
}

type Tip struct {
	Id string `json:"id"`
	UI string `json:"ui"` // 前端存储的节点UI相关的信息，包含id type content等，后端不负责解析
}

// SubChainBaseInfo 子链基础信息
type SubChainBaseInfo struct {
	SubChainName     string `json:"sub_chain_name"`      // 子链展开后，子链所属原本的应用链的name
	SubChainWidgetID string `json:"sub_chain_widget_id"` // 子链所属算子ID
}

// ChainNodeLinkPoint 描述一个节点的连接点，包括节点信息和连接点信息
type ChainNodeLinkPoint struct {
	Node  ChainNode
	Param WidgetParam
}

// Widget 应用链的算子定义
type Widget struct {
	Id              string        `json:"id,omitempty"`                // 算子类别id==WidgetKey, 动态算子及普通算子均使用此字段
	Name            string        `json:"name,omitempty"`              // 算子中文名称
	Desc            string        `json:"desc,omitempty"`              // 算子描述
	Group           string        `json:"group,omitempty"`             // 算子分组
	Params          []WidgetParam `json:"params,omitempty"`            // 算子动态参数列表
	DynamicEndPoint bool          `json:"dynamic_end_point,omitempty"` // 算子的输入、输出端点个数可变化,比如数据合并、问题分类等特殊算子
	MdFullDesc      string        `json:"md_full_desc,omitempty"`      // 详细md描述文档
	MdSummaryDesc   string        `json:"md_summary_desc,omitempty"`   // 简要md描述文档

	//  string category = 5; // i.e. device or process
	//  string pipeline_type = 6;  // tick || media || applet
	//  string language = 7;      // en || ch
}

func (c ChainNode) GetValuesWithDefault() (map[string]interface{}, error) {
	widget, err := c.GetWidgetDetail()
	if err != nil {
		return nil, err
	}
	res := c.Values
	for _, p := range widget.Params {
		if p.Define.DefaultValue != "" {
			if _, ok := c.Values[p.Define.Id]; !ok {
				res[p.Define.Id] = p.Define.DefaultValue
			}
		}
	}
	return res, nil
}

func (c ChainNode) GetWidgetDetail() (*Widget, error) {
	widgetDetail := c.WidgetDetail
	// 兼容历史数据
	if widgetDetail == nil {
		stdlog.Warnf("widget detail is nil :%v", c.WidgetId)
		widgetInterface, err := WidgetFactoryImpl.GetWidget(c.WidgetId)
		if err != nil {
			return nil, err
		}
		widgetDetail = widgetInterface.Define()
	}
	if widgetDetail == nil {
		return nil, stderr.Internal.Error("no widget detail:%v", c.WidgetId)
	}
	return widgetDetail, nil
}

func GetNodeIDFromEdge(edge string) (string, error) {
	if len(strings.Split(edge, "@@")) != 2 {
		return "", stderr.Internal.Error("get node id from edge err,invalid edge :%v", edge)
	}
	return strings.Split(edge, "@@")[0], nil
}

func GetParamIDFromEdge(edge string) (string, error) {
	if len(strings.Split(edge, "@@")) != 2 {
		return "", stderr.Internal.Error("get param id from edge err,invalid edge :%v", edge)
	}
	return strings.Split(edge, "@@")[1], nil
}

func (c Chain) getNodeMap() map[string]ChainNode {
	res := make(map[string]ChainNode)
	for _, n := range c.Nodes {
		res[n.Id] = n
	}
	return res
}
func (c Chain) getEdgeSourceMap() map[string][]ChainEdge {
	res := make(map[string][]ChainEdge)
	for _, e := range c.Edges {
		if r, ok := res[e.SourceNode]; ok {
			res[e.SourceNode] = append(r, e)
		} else {
			res[e.SourceNode] = []ChainEdge{e}
		}
	}
	return res
}

// OnlyGotoSourceEdge 该节点所有的边均来自Goto算子
func (c Chain) OnlyGotoSourceEdge(nodeId string) (bool, error) {
	nodeMap := c.getNodeMap()
	if _, exist := nodeMap[nodeId]; !exist {
		return false, stderr.Errorf("can not find node with id[%s]", nodeId)
	}

	for _, edge := range c.Edges {
		targetNode := nodeMap[edge.TargetNode]
		sourceNode := nodeMap[edge.SourceNode]
		if targetNode.Id != nodeId {
			continue
		}
		if sourceNode.WidgetDetail != nil && sourceNode.WidgetDetail.Id != WidgetKeyGoTo {
			return false, nil
		}
	}
	return true, nil
}

// GetUserInPortNodes 获取用户输入节点
func (c Chain) GetUserInPortNodes() ([]ChainNode, error) {
	res := make([]ChainNode, 0)
	for _, n := range c.Nodes {
		widgetDetail, err := n.GetWidgetDetail()
		if err != nil {
			return nil, err
		}
		for _, p := range widgetDetail.Params {
			if p.Category == ParamTypeReqInput {
				res = append(res, n)
				break
			}
		}
	}
	return res, nil
}

// GetUserInPortNodeByID 获取用户输入节点
func (c Chain) GetUserInPortNodeByID(ID string) (*ChainNode, error) {
	for _, n := range c.Nodes {
		widgetDetail, err := n.GetWidgetDetail()
		if err != nil {
			return nil, err
		}
		for _, p := range widgetDetail.Params {
			if p.Category == ParamTypeReqInput {
				if n.Id == ID {
					return &n, nil
				}
			}
		}
	}
	return nil, stderr.Internal.Error("no user input node:%v ", ID)
}

// GetUserInPortNodeByWidgetKey 获取用户输入节点
func (c Chain) GetUserInPortNodeByWidgetKey(key string) (*ChainNode, error) {
	for _, n := range c.Nodes {
		widgetDetail, err := n.GetWidgetDetail()
		if err != nil {
			return nil, err
		}
		for _, p := range widgetDetail.Params {
			if p.Category == ParamTypeReqInput {
				if widgetDetail.Id == key {
					return &n, nil
				}
			}
		}
	}
	return nil, stderr.Internal.Error("no user input node:%v ", key)
}

// GetWidgetByNodeID 获取下一个节点及连接点
func (c Chain) GetWidgetByNodeID(ID string) (*Widget, error) {
	for _, n := range c.Nodes {
		if n.Id == ID {
			return n.GetWidgetDetail()
		}
	}
	return nil, stderr.Internal.Error("can not find node :%v", ID)
}

// GetNextNodes 获取下一个节点及连接点
func (c Chain) GetNextNodes(ID string) ([]*ChainNodeLinkPoint, error) {
	res := make([]*ChainNodeLinkPoint, 0)
	edgeMap := c.getEdgeSourceMap()
	nodeMap := c.getNodeMap()
	if edges, ok := edgeMap[ID]; ok {
		for _, e := range edges {
			node := nodeMap[e.TargetNode]
			paramID, err := GetParamIDFromEdge(e.TargetParam)
			if err != nil {
				return nil, err
			}
			w, err := node.GetWidgetDetail()
			if err != nil {
				return nil, err
			}
			param, ok := w.GetParamMap()[paramID]
			if !ok {
				return nil, stderr.Internal.Error("node :%v no param :%v", node, paramID)
			}
			res = append(res, &ChainNodeLinkPoint{
				Node:  nodeMap[e.TargetNode],
				Param: *param,
			})
		}
	}
	return res, nil
}

func (c Chain) ContainsSubChain() bool {
	for _, n := range c.Nodes {
		if n.WidgetDetail != nil && n.WidgetDetail.Id == WidgetKeySubChain {
			return true
		}
	}
	return false
}

func (c Chain) ContainsGOTONode() bool {
	for _, n := range c.Nodes {
		if n.WidgetDetail != nil && n.WidgetDetail.Id == WidgetKeyGoTo {
			return true
		}
	}
	return false
}

// GetLastNodes 获取输出节点,仅支持一个输出节点
func (c Chain) GetLastNodes() []ChainNode {
	res := make([]ChainNode, 0)
	edgeMap := c.getEdgeSourceMap()
	for _, n := range c.Nodes {
		if _, ok := edgeMap[n.Id]; !ok {
			res = append(res, n)
		}
	}
	return res
}

// ContainsCommonNode 除了输入输出节点之外还有其他节点
func (c Chain) ContainsCommonNode() (bool, error) {
	for _, n := range c.Nodes {
		widget, err := n.GetWidgetDetail()
		if err != nil {
			return false, err
		}
		if !widget.IsInputNode() && !widget.IsOutputNode() {
			return true, nil
		}
	}
	return false, nil
}

// GetLastNode 获取最后一个节点
func (c Chain) GetLastNode() (*ChainNode, error) {
	edgeMap := c.getEdgeSourceMap()
	for _, n := range c.Nodes {
		if _, ok := edgeMap[n.Id]; !ok {
			return &n, nil
		}
	}
	return nil, stderr.Internal.Error("can not found common node")
}

// IsLLMChain 是否是大模型对话链
func (c Chain) IsLLMChain() bool {
	_, err := c.GetUserInPortNodeByWidgetKey(WidgetKeyTextInput)
	if err != nil {
		return false
	}
	for _, node := range c.Nodes {
		// 对话模型或者远程模型
		if node.WidgetDetail.Id == WidgetKeyLLM || node.WidgetDetail.Id == WidgetKeyExternalAPICall ||
			node.WidgetDetail.Id == WidgetKeyAgent {
			return true
		}
	}
	return false
}

// GetLastCommonNode 获取最后一个有意义的节点(除了输入&输出)
func (c Chain) GetLastCommonNode() (*ChainNode, error) {
	edgeMap := c.getEdgeSourceMap()
	nodeMap := c.getNodeMap()
	for _, n := range c.Nodes {
		widget, err := n.GetWidgetDetail()
		if err != nil {
			return nil, err
		}
		if edges, ok := edgeMap[n.Id]; !ok {
			if !widget.IsInputNode() && !widget.IsOutputNode() {
				return &n, nil
			}
		} else {
			// 如果本节点的next节点是output节点，则此节点未last common node
			for _, e := range edges {
				w, err := nodeMap[e.TargetNode].GetWidgetDetail()
				if err != nil {
					return nil, err
				}
				if w.IsOutputNode() {
					return &n, nil
				}
			}
		}
	}
	return nil, stderr.Internal.Error("can not found common node")
}

func (c ChainNode) GetUserInPortParam() ([]WidgetParam, error) {
	res := make([]WidgetParam, 0)
	widgetDetail, err := c.GetWidgetDetail()
	if err != nil {
		return nil, err
	}
	for _, p := range widgetDetail.Params {
		if p.Category == ParamTypeReqInput {
			res = append(res, p)
		}
	}
	return res, err
}

// func (c ChainNode) GetUserInPortParamByID(ID string) (*WidgetParam, error) {
//	widgetDetail, err := c.GetWidgetDetail()
//	if err != nil {
//		return nil, err
//	}
//	for _, p := range widgetDetail.Params {
//		if p.Category == ParamTypeReqInput && p.Define.Id == ID {
//			return &p, nil
//		}
//	}
//	return nil, stderr.Internal.Error("node :%v no param :%v", c.WidgetId)
// }

func (c ChainEdge) GetSourceParam() (string, error) {
	splits := strings.Split(c.SourceParam, "@@")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid source param :%v", c.SourceParam)
	}
	return splits[1], nil
}

func (c ChainEdge) GetTargetParam() (string, error) {
	splits := strings.Split(c.TargetParam, "@@")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid target param :%v", c.TargetParam)
	}
	return splits[1], nil
}

func (w Widget) GetOutPortParam() []WidgetParam {
	res := make([]WidgetParam, 0)
	for _, p := range w.Params {
		if p.Category == ParamTypeNodeOutPort || p.Category == ParamTypeRspOutput {
			res = append(res, p)
		}
	}
	return res
}

func (w Widget) GetOutPortID() []string {
	res := make([]string, 0)
	for _, p := range w.Params {
		if p.Category == ParamTypeNodeOutPort || p.Category == ParamTypeRspOutput {
			res = append(res, p.Define.Id)
		}
	}
	return res
}

func (w Widget) GetParamMap() map[string]*WidgetParam {
	res := make(map[string]*WidgetParam)
	for _, p := range w.Params {
		p := p
		res[p.Define.Id] = &p
	}
	return res
}

func (w Widget) IsUserInputWidget() bool {
	for _, p := range w.Params {
		if p.Category == ParamTypeReqInput {
			return true
		}
	}
	return false
}

func (w Widget) GetUserInputParam() []WidgetParam {
	res := make([]WidgetParam, 0)
	for _, p := range w.Params {
		if p.Category == ParamTypeReqInput {
			res = append(res, p)
		}
	}
	return res
}

func (w Widget) IsInputNode() bool {
	for _, p := range w.Params {
		if p.Category == ParamTypeReqInput {
			return true
		}
	}
	return false
}

// IsOutputNode 区分output node 与 last node，output node没有输出参数，last node是应用链的最后一个算子
func (w Widget) IsOutputNode() bool {
	for _, p := range w.Params {
		if p.Category == ParamTypeRspOutput {
			return true
		}
	}
	return false
}

type WidgetParamCategory string
type WidgetParamDataClass string

// WidgetParam 算子参数，每个算子可能包含多个，算子参数会区分为不同类型
type WidgetParam struct {
	DataClass   WidgetParamDataClass `json:"data_class"`   // DataClass 算子参数对应的数据结构类型
	Category    WidgetParamCategory  `json:"category"`     // Category 算子参数类别
	Preview     bool                 `json:"preview"`      // 是否可以预览
	Define      DynamicParam         `json:"define"`       // Define 算子参数的定义
	ParamLimits *WidgetParamLimits   `json:"param_limits"` // 数据流-传输方式及传输类型限制,目前主要针对in-out和out-put类别
}

func (w *WidgetParam) IsInput() bool {
	return w.Category == ParamTypeNodeInPort || w.Category == ParamTypeReqInput
}

func (w *WidgetParam) IsOutPut() bool {
	return w.Category == ParamTypeNodeOutPort || w.Category == ParamTypeRspOutput
}

func (w *WidgetParam) IsAttr() bool {
	return w.Category == ParamTypeAttribute
}

const (
	ParamTypeUnspecified WidgetParamCategory = ""
	ParamTypeAttribute   WidgetParamCategory = "attribute"  // 算子自身属性
	ParamTypeNodeInPort  WidgetParamCategory = "in-port"    // 接受上游算子数据输入
	ParamTypeNodeOutPort WidgetParamCategory = "out-port"   // 数据输出到下游算子
	ParamTypeReqInput    WidgetParamCategory = "req-input"  // 界面上用户手动输入
	ParamTypeRspOutput   WidgetParamCategory = "rsp-output" // 界面上打印输出

	DataClassUnspecified WidgetParamDataClass = ""
	DataClassString      WidgetParamDataClass = "string"
	DataClassVector      WidgetParamDataClass = "vector"
	DataClassJson        WidgetParamDataClass = "json"
	DataClassSfsURL      WidgetParamDataClass = "sfs-url"
	DataClassS3URL       WidgetParamDataClass = "s3-url"
	DataClassFile        WidgetParamDataClass = "file"
	DataClassCode        WidgetParamDataClass = "code"
)

func WidgetParamsToMap(params []WidgetParam) map[string]*WidgetParam {
	res := make(map[string]*WidgetParam)
	for _, p := range params {
		p := p
		res[p.Define.Id] = &p
	}
	return res
}

type DynamicParam pb.DynamicParam

func (r *DynamicParam) MarshalJSON() ([]byte, error) {
	return json.Marshal(&struct {
		*pb.DynamicParam
		Type     string `json:"type"`      // 将原来的 int32 类型的枚举类型转换为对应的 string 形式
		DataType string `json:"data_type"` // 将原来的 int32 类型的枚举类型转换为对应的 string 形式
	}{
		DynamicParam: (*pb.DynamicParam)(r),
		Type:         r.Type.String(),
		DataType:     r.DataType.String(),
	})
}

func (r *DynamicParam) UnmarshalJSON(data []byte) error {
	p := &struct {
		*pb.DynamicParam
		Type     string `json:"type"`      // 将原来的 int32 类型的枚举类型转换为对应的 string 形式
		DataType string `json:"data_type"` // 将原来的 int32 类型的枚举类型转换为对应的 string 形式
	}{
		DynamicParam: (*pb.DynamicParam)(r),
	}
	if err := json.Unmarshal(data, p); err != nil {
		return err
	}
	r.Type = pb.DynamicParam_Type(pb.DynamicParam_Type_value[p.Type])
	r.DataType = pb.DynamicParam_DataType(pb.DynamicParam_DataType_value[p.DataType])
	return nil
}

// IsEmpty 空链
func (c Chain) IsEmpty() bool {
	if len(c.Nodes) == 0 && len(c.Edges) == 0 {
		return true
	}
	return false
}

func (c Chain) IsValid() error {
	if c.IsEmpty() {
		return nil
	}

	// 至少有一个输入节点
	inputNodes, err := c.GetUserInPortNodes()
	if err != nil {
		return helper.AppletChainInvalidErr.Errorf(err.Error())
	}
	if len(inputNodes) == 0 {
		return helper.AppletChainInvalidErr.Errorf("没有输入节点")
	}

	// 有且仅有一个输出节点
	outPutNodes := c.GetLastNodes()
	if len(outPutNodes) != 1 {
		return helper.AppletChainInvalidErr.Errorf("输出节点数量必须为1")
	}

	nodeMap := make(map[string]*Widget)
	targetEdgeMap := helper.CvtSlice2Map(c.Edges, func(edge ChainEdge) string { return edge.TargetParam })
	sourceEdgeMap := helper.CvtSlice2Map(c.Edges, func(edge ChainEdge) string { return edge.SourceParam })
	for _, n := range c.Nodes {
		widgetDetail := n.WidgetDetail
		if widgetDetail == nil {
			w, err := WidgetFactoryImpl.GetWidget(n.WidgetId)
			if err != nil {
				return helper.AppletChainInvalidErr.Errorf("节点[%s]已失效,%s", n.Name, err.Error())
			}
			widgetDetail = w.Define()
		}
		nodeMap[n.Id] = widgetDetail

		// 算子参数校验
		valuesMap := n.Values
		for _, param := range widgetDetail.Params {
			if !(param.Define.Precondition == "" && param.Define.Required) {
				continue
			}
			paramId := param.Define.Id
			switch param.Category {
			case ParamTypeAttribute: // 算子参数
				if _, exist := valuesMap[paramId]; !exist {
					return helper.AppletChainInvalidErr.Errorf("节点[%s]存在必填参数未设置", n.Name)
				}
			case ParamTypeNodeInPort: // 上游输入边是否存在
				if _, exist := targetEdgeMap[getEdgeParam(n.Id, paramId)]; !exist {
					return helper.AppletChainInvalidErr.Errorf("节点[%s]输入边不合法", n.Name)
				}
			case ParamTypeNodeOutPort: // 输出边是否存在
				if n.Id == outPutNodes[0].Id {
					continue
				}
				if _, exist := sourceEdgeMap[getEdgeParam(n.Id, paramId)]; !exist {
					return helper.AppletChainInvalidErr.Errorf("节点[%s]输出边不合法", n.Name)
				}
			case ParamTypeReqInput, ParamTypeRspOutput:
				continue
			}
		}
	}
	for _, n := range c.Edges {
		if w, ok := nodeMap[n.TargetNode]; !ok {
			return errors.Errorf("edge invalid ,node :%v not exists", n.TargetNode)
		} else {
			paramArr := strings.Split(n.TargetParam, "@@")
			if len(paramArr) != 2 {
				return errors.Errorf("param format err ,param :%v", n.TargetParam)
			}
			if _, ok := w.GetParamMap()[paramArr[1]]; !ok {
				return errors.Errorf("edge invalid ,param :%v not exists", n.TargetParam)
			}
		}
		if w, ok := nodeMap[n.SourceNode]; !ok {
			return errors.Errorf("edge invalid ,node :%v not exists", n.SourceNode)
		} else {
			paramArr := strings.Split(n.SourceParam, "@@")
			if len(paramArr) != 2 {
				return errors.Errorf("param format err ,param :%v", n.TargetParam)
			}
			if _, ok := w.GetParamMap()[paramArr[1]]; !ok {
				return errors.Errorf("edge invalid ,param :%v not exists", n.SourceParam)
			}
		}
	}

	return nil
}

func getEdgeParam(nodeId, paramId string) string {
	return fmt.Sprintf("%s@@%s", nodeId, paramId)
}

type NodeInputInfos struct {
	NodeInputs []*NodeInputInfo
}

type NodeOutputInfos struct {
	NodeOutputs []*NodeOutputInfo
}

type NodeInputInfo struct {
	PreNodeID             string //前置节点的ID
	PreNodeOutPutParam    string //前置节点的输出端点
	CurrentNodeInputParam string //当前节点的输入端点
	PreNodeVar            string // 前置节点script的变量名
}

type NodeOutputInfo struct {
	NextNodeID             string //后置节点ID
	NextNodeInputParam     string //后置节点的输入端点
	CurrentNodeOutputParam string //当前节点的输出端点
	NextNodeVar            string // 后置节点script的变量名
}

//type PreNodeInfo struct {
//	// 上一个节点的ID
//	PreNodeID string
//	// 上一个节点的输出端点
//	PreNodeOutput string
//	// 上一个节点连接到哪个参数
//	PreNodeConnect string
//	// 上一个节点对应的的script变量名
//	PreNodeScriptVar string
//}
//
//type NextNodeInfo struct {
//	// 下一个节点的ID
//	NextNodeID string
//	// 哪个输出端点连接到下一个节点
//	OutputIDForNextNode string
//	// 下一个节点的参数
//	NextNodeParam string
//	// 下一个节点对应的script变量名
//	NextNodeScriptVar string
//}

type WidgetParamLimits struct {
	Types []widgetParamType `json:"types"`
}

func (w *WidgetParamLimits) String() string {
	if len(w.Types) == 0 {
		panic(stderr.Errorf("the length of type is 0"))
	}
	sb := strings.Builder{}
	sb.WriteString("支持类型: ")
	for index, t := range w.Types {
		_, dataType, err := resolveParamType(t)
		if err != nil {
			panic(err)
		}
		dataTypeDesc, err := GetParamsFactory().GetDataTypeDesc(dataType)
		if err != nil {
			panic(err)
		}
		sb.WriteString(fmt.Sprintf("%+v: %s", t, toolkit.SprintPrettyJson(dataTypeDesc.Example)))
		if index != len(w.Types)-1 {
			sb.WriteString(", ")
		} else {
			sb.WriteString("。")
		}
	}
	return sb.String()
}

func EmptyLimits() *WidgetParamLimits {
	return &WidgetParamLimits{}
}

// baseLimits 单个传输方式与传输类型
func baseLimits(modeType WidgetParamModeType, dataTypes ...WidgetParamDataType) *WidgetParamLimits {
	limits := EmptyLimits()
	for _, dataType := range dataTypes {
		limits.AppendType(getParamType(modeType, dataType))
	}
	return limits
}

func SyncAnyLimits() *WidgetParamLimits {
	return BaseSyncLimits(DataTypeAny)
}

func AnyAnyLimits() *WidgetParamLimits {
	return BaseAnyLimits(DataTypeAny)
}

// BaseSyncLimits 传输方式为sync, 指定传输类型
func BaseSyncLimits(dataTypes ...WidgetParamDataType) *WidgetParamLimits {
	return baseLimits(ModeTypeSync, dataTypes...)
}
func BaseStreamLimits(dataTypes ...WidgetParamDataType) *WidgetParamLimits {
	return baseLimits(ModeTypeStream, dataTypes...)
}
func BaseAnyLimits(dataTypes ...WidgetParamDataType) *WidgetParamLimits {
	return baseLimits(ModeTypeAny, dataTypes...)
}

func (w *WidgetParamLimits) AppendType(types ...widgetParamType) *WidgetParamLimits {
	w.Types = append(w.Types, types...)
	return w
}

// widgetParamType 模式加数据类型 WidgetParamModeType-WidgetParamDataType
type widgetParamType string

type WidgetParamModeType string
type WidgetParamDataType string

func getParamType(modeType WidgetParamModeType, dataType WidgetParamDataType) widgetParamType {
	return widgetParamType(fmt.Sprintf("%s-%s", modeType, dataType))
}
func resolveParamType(t widgetParamType) (modeType WidgetParamModeType, dataType WidgetParamDataType, err error) {
	strs := strings.Split(string(t), "-")
	if len(strs) != 2 {
		return "", "", stderr.Errorf("the widgetParamType: %v is valid", t)
	}
	return WidgetParamModeType(strs[0]), WidgetParamDataType(strs[1]), nil
}

// CheckEdgeReq 检查算子连接是否合法
type CheckEdgeReq struct {
	SourceLimits WidgetParamLimits `json:"source_limits"` //输入节点参数限制
	TargetLimits WidgetParamLimits `json:"target_limits"` //输出节点参数限制
}
type CheckEdgeResp struct {
	Result bool  `json:"result"` //是否成功
	Error  error `json:"error"`  //失败原因
}

const (
	widgetKey     = "${widgetKey}"
	cateInfo      = "${cateInfo}"
	purposeInfo   = "${purposeInfo}"
	inFormat      = "${inFormat}"
	inExample     = "${inExample}"
	outFormat     = "${outFormat}"
	outExample    = "${outExample}"
	inModeType    = "${inModeType}"
	outModeType   = "${outModeType}"
	widgetName    = "${widgetName}"
	widgetTypeUrl = "${widgetTypeUrl}"
)

var (
	sumMdDesc = fmt.Sprintf(`
<!--%s-->
**算子类别**: %s
**算子用途**: %s
`, widgetKey, cateInfo, purposeInfo)

	fullMdDesc = fmt.Sprintf(`
<!--%s-->
**算子类别**: %s
**算子用途**: %s
**输入格式**: %s
<details>
    <summary>示例数据</summary>
    <pre>
%s
    </pre>
</details>

**输出格式**: %s
<details>
    <summary>示例数据</summary>
    <pre>
%s
    </pre>
</details>

**输入类型**: %s
**输出类型**: %s
**详细信息**: [%s-算子详情文档](%s)
`, widgetKey, cateInfo, purposeInfo, inFormat, inExample, outFormat, outExample, inModeType, outModeType, widgetName, widgetTypeUrl)
)

func genMdDesc(isSummary bool, widget *Widget) (string, error) {
	prefix := "    "
	groupDesc, err := getWidgetGroupDesc(widget.Group)
	if err != nil {
		return "", err
	}
	widgetDocUrl, err := getWidgetGroupDocUrl(widget.Group)
	if err != nil {
		return "", err
	}

	inFormatStr, inExampleStr, inMTypeStr, outFormatStr, outExampleStr, outMTypeStr, err := prepareMdStr(widget)
	if err != nil {
		return "", err
	}
	//替换对应字段
	replacer := strings.NewReplacer(
		widgetKey, widget.Id,
		cateInfo, groupDesc.Name,
		purposeInfo, widget.Desc,
		inFormat, inFormatStr,
		inExample, appendPrefix(inExampleStr, prefix),
		outFormat, outFormatStr,
		outExample, appendPrefix(outExampleStr, prefix),
		inModeType, inMTypeStr,
		outModeType, outMTypeStr,
		widgetName, widget.Name,
		widgetTypeUrl, widgetDocUrl,
	)
	ret := ""
	if isSummary {
		ret = replacer.Replace(sumMdDesc)
	} else {
		ret = replacer.Replace(fullMdDesc)
	}
	//stdlog.Infof(ret)
	return ret, nil
}

func prepareMdStr(widget *Widget) (inFormat, inExample, inMType, outFormat, outExample, outMType string, err2 error) {
	splitFlag := ", "
	paramsFac := GetParamsFactory()
	modeTypeUrl := getModeTypeDocUrl()
	dataTypeUrl := getDataTypeDocUrl()
	for _, p := range widget.Params {
		if !p.IsInput() && !p.IsOutPut() {
			continue
		}
		if p.ParamLimits == nil || len(p.ParamLimits.Types) == 0 {
			err2 = stderr.Errorf("input/output param limits is nil for widget[%s]", widget.Name)
			return
		}
		for _, t := range p.ParamLimits.Types {
			mType, dType, err := resolveParamType(t)
			if err != nil {
				err2 = err
				return
			}

			mDesc, err := paramsFac.GetModeTypeDesc(mType)
			if err != nil {
				err2 = err
				return
			}
			dDesc, err := paramsFac.GetDataTypeDesc(dType)
			if err != nil {
				err2 = err
				return
			}

			format := fmt.Sprintf("[%s](%s)%s", dType, dataTypeUrl, splitFlag)
			example := fmt.Sprintf("%s:\n%s\n\n", dType, toolkit.SprintPrettyJson(dDesc.Example))
			if p.IsInput() {
				inMType = fmt.Sprintf("[%s](%s)(%s)", mType, modeTypeUrl, mDesc.Name)
				inFormat = appendIfNotExist(inFormat, format)
				inExample = appendIfNotExist(inExample, example)
			} else {
				outMType = fmt.Sprintf("[%s](%s)(%s)", mType, modeTypeUrl, mDesc.Name)
				outFormat = appendIfNotExist(outFormat, format)
				outExample = appendIfNotExist(outExample, example)
			}
		}
	}
	inFormat = strings.TrimRight(inFormat, splitFlag)
	outFormat = strings.TrimRight(outFormat, splitFlag)

	inExample = strings.TrimRight(inExample, "\n")
	outExample = strings.TrimRight(outExample, "\n")
	return
}

func getModeTypeDocUrl() string {
	return "doc:///help_document/zh/index.html#ModeType"
}

func getDataTypeDocUrl() string {
	return "doc:///help_document/zh/index.html#DataType"
}

func getWidgetGroupDocUrl(groupKey string) (string, error) {
	return fmt.Sprintf("doc:///help_document/zh/index.html#%s", groupKey), nil
}

func getWidgetGroupDesc(groupKey string) (*WidgetGroupDesc, error) {
	groupDesc, ok := WidgetGroupDefines[groupKey]
	if !ok {
		return nil, stderr.Errorf("widget group [%s] is not exists", groupKey)
	}
	return groupDesc, nil
}

func appendIfNotExist(str, substr string) string {
	if strings.Contains(str, substr) {
		return str
	}
	return str + substr
}

func appendPrefix(str, prefix string) string {
	ret := ""
	strs := strings.Split(str, "\n")
	for index, s := range strs {
		ret = ret + prefix + s
		if index != len(strs)-1 {
			ret = ret + "\n"
		}
	}
	return ret
}
