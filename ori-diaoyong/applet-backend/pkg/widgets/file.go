package widgets

// 文件处理算子

//type fileParser struct {
//}
//
//var widgetFileParser = &Widget{
//	Id:    WidgetKeyFileParser,
//	Name:  "文件解析",
//	Desc:  "将pdf、word文件解析成text文本",
//	Group: WidgetGroupFile,
//	Params: []WidgetParam{
//		{
//			DataClass: DataClassString,
//			Category:  ParamTypeNodeInPort,
//			Define: DynamicParam{
//				Id:       "Text",
//				Name:     "文本",
//				Desc:     "文本",
//				Type:     pb.DynamicParam_TYPE_INPUT,
//				DataType: pb.DynamicParam_DATA_TYPE_STRING,
//			},
//		},
//		{
//			DataClass: DataClassString,
//			Category:  ParamTypeAttribute,
//			Define: DynamicParam{
//				Id:         "TextFormat",
//				Name:       "文件格式",
//				Desc:       "文件格式",
//				Type:       pb.DynamicParam_TYPE_SELECTOR,
//				Datasource: "pdf@@pdf,word@@word",
//				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
//			},
//		},
//		{
//			DataClass: DataClassString,
//			Category:  ParamTypeAttribute,
//			Define: DynamicParam{
//				Id:         "ParserType",
//				Name:       "解析方法",
//				Desc:       "文件格式",
//				Type:       pb.DynamicParam_TYPE_SELECTOR,
//				Datasource: "common_parser@@通用解析",
//				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
//			},
//		},
//		{
//			DataClass: DataClassString,
//			Category:  ParamTypeNodeOutPort,
//			Define: DynamicParam{
//				Id:       "OutPut",
//				Type:     pb.DynamicParam_TYPE_INPUT,
//				DataType: pb.DynamicParam_DATA_TYPE_STRING,
//			},
//		},
//	},
//}
//
//func (f fileParser) Define() *Widget {
//	return widgetFileParser
//}
//
//func (f fileParser) ScriptConfig() *ScriptConfig {
//	return &ScriptConfig{ScriptClassName: getScriptNameFromWidgetKey(f.Define().Id)}
//}
