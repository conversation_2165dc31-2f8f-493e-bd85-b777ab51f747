package widgets

const (
	// WidgetGroupInput 输入
	WidgetGroupInput = "WidgetGroupInput"
	// WidgetGroupProcessText 文本处理
	WidgetGroupProcessText = "WidgetGroupProcessText"
	// WidgetGroupAIModel 模型调用
	WidgetGroupAIModel = "WidgetGroupAIModel"
	// WidgetGroupProcessKnowledge 知识加工
	WidgetGroupProcessKnowledge = "WidgetGroupProcessKnowledge"
	// WidgetGroupVD 知识库
	WidgetGroupVD = "WidgetGroupVD"
	// WidgetGroupAdvanced 高级
	WidgetGroupAdvanced = "WidgetGroupAdvanced"
	// WidgetGroupControlFlow 控制流
	WidgetGroupControlFlow = "WidgetGroupControlFlow"
	// WidgetGroupChainTool 工具调用
	WidgetGroupChainTool = "WidgetGroupChainTool"
	// WidgetGroupCodeTool 胶水代码
	WidgetGroupCodeTool = "WidgetGroupCodeTool"
	// WidgetGroupOutput 输出
	WidgetGroupOutput = "WidgetGroupOutput"

	// WidgetGroupSubChain 应用链调用
	WidgetGroupSubChain = "WidgetGroupSubChain"
	// WidgetGroupTestWidgets 测试算子
	WidgetGroupTestWidgets = "WidgetGroupTestWidgets"
)

type SortFlag int

// 删除 TODO 注释并重新排序 SortFlag
const (
	SortFlagWidgetGroupInput SortFlag = iota
	SortFlagWidgetGroupProcessText
	SortFlagWidgetGroupAIModel
	SortFlagWidgetGroupProcessKnowledge
	SortFlagWidgetGroupVD
	SortFlagWidgetGroupAdvanced
	SortFlagWidgetGroupControlFlow
	SortFlagWidgetGroupChainTool
	SortFlagWidgetGroupCodeTool
	SortFlagWidgetGroupOutput
	SortFlagWidgetGroupSubChain
	SortFlagWidgetGroupTestWidgets
)

var WidgetGroupDefines map[string]*WidgetGroupDesc

type WidgetGroupDesc struct {
	ID       string
	Name     string
	Desc     string
	SortFlag SortFlag
}

func InitWidgetGroups() {
	WidgetGroupDefines = make(map[string]*WidgetGroupDesc)
	WidgetGroupDefines[WidgetGroupInput] = &WidgetGroupDesc{
		ID:       WidgetGroupInput,
		Name:     "输入",
		Desc:     "处理用户输入",
		SortFlag: SortFlagWidgetGroupInput,
	}
	WidgetGroupDefines[WidgetGroupProcessText] = &WidgetGroupDesc{
		ID:       WidgetGroupProcessText,
		Name:     "文本处理",
		Desc:     "对文本进行安全过滤、拼接等处理",
		SortFlag: SortFlagWidgetGroupProcessText,
	}
	WidgetGroupDefines[WidgetGroupAIModel] = &WidgetGroupDesc{
		ID:       WidgetGroupAIModel,
		Name:     "模型调用",
		Desc:     "调用文本、图片、语音等模型服务",
		SortFlag: SortFlagWidgetGroupAIModel,
	}
	WidgetGroupDefines[WidgetGroupProcessKnowledge] = &WidgetGroupDesc{
		ID:       WidgetGroupProcessKnowledge,
		Name:     "知识加工",
		Desc:     "对文档、图片等数据进行结构化处理",
		SortFlag: SortFlagWidgetGroupProcessKnowledge,
	}
	WidgetGroupDefines[WidgetGroupVD] = &WidgetGroupDesc{
		ID:       WidgetGroupVD,
		Name:     "知识库",
		Desc:     "知识库",
		SortFlag: SortFlagWidgetGroupVD,
	}
	WidgetGroupDefines[WidgetGroupAdvanced] = &WidgetGroupDesc{
		ID:       WidgetGroupAdvanced,
		Name:     "高级",
		Desc:     "提供智能体、自定义算子等功能",
		SortFlag: SortFlagWidgetGroupAdvanced,
	}
	WidgetGroupDefines[WidgetGroupControlFlow] = &WidgetGroupDesc{
		ID:       WidgetGroupControlFlow,
		Name:     "控制流",
		Desc:     "使用条件判断、循环跳转等对应用链的数据流进行控制",
		SortFlag: SortFlagWidgetGroupControlFlow,
	}
	WidgetGroupDefines[WidgetGroupChainTool] = &WidgetGroupDesc{
		ID:       WidgetGroupChainTool,
		Name:     "工具调用",
		Desc:     "调用HTTP、参数提取器、自定义工具等",
		SortFlag: SortFlagWidgetGroupChainTool,
	}
	WidgetGroupDefines[WidgetGroupCodeTool] = &WidgetGroupDesc{
		ID:       WidgetGroupCodeTool,
		Name:     "胶水代码",
		Desc:     "编写自定义代码以连接和整合不同的组件和服务",
		SortFlag: SortFlagWidgetGroupCodeTool,
	}
	WidgetGroupDefines[WidgetGroupOutput] = &WidgetGroupDesc{
		ID:       WidgetGroupOutput,
		Name:     "输出",
		Desc:     "输出处理后的数据",
		SortFlag: SortFlagWidgetGroupOutput,
	}
	WidgetGroupDefines[WidgetGroupSubChain] = &WidgetGroupDesc{
		ID:       WidgetGroupSubChain,
		Name:     "应用链调用",
		Desc:     "应用链调用",
		SortFlag: SortFlagWidgetGroupSubChain,
	}
	WidgetGroupDefines[WidgetGroupTestWidgets] = &WidgetGroupDesc{
		ID:       WidgetGroupTestWidgets,
		Name:     "测试算子",
		Desc:     "测试算子,用于查看算子定义是否符合预期",
		SortFlag: SortFlagWidgetGroupTestWidgets,
	}
}
