package widgets

import (
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

type IWidget interface {
	Define() *Widget
	// Script 用于生成script脚本
	Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error)
}

type ScriptConfig struct {
	// script脚本的 name
	ScriptClassName string
	// script脚本的 构造参数
	ScriptClassParamFunc func(node *ChainNode) (string, error)
	// 在算子中没有，但是需要输出给engine的额外参数
	ExtraInput map[string]interface{}
	// 在算子中有，但是最终不输出给engine的参数
	HideInput map[string]interface{}
	// 自定义算子到script的参数转换
	ParamTransferFunc func(node *ChainNode) (map[string]interface{}, error)
}
