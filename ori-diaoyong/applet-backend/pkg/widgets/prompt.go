package widgets

// import (
// 	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
// )

var FunPromptParamTransfer = func(node *ChainNode) (string, error) {
	widget, err := WidgetFactoryImpl.GetWidget(node.WidgetId)
	if err != nil {
		return "", err
	}
	return widget.Define().Desc, nil
}

const (
	WidgetKeyDynamicPrompt = "WidgetKeyDynamicPrompt"
)

// type dynamicPrompt struct {
// }

// var WidgetDynamicPrompt = &Widget{
// 	Id:           WidgetKeyDynamicPrompt,
// 	OriWidgetKey: WidgetKeyDynamicPrompt,
// 	Name:         "新建提示模板",
// 	Desc:         "用于基于新建的提示词模版进行提示词拼接",
// 	Group:        WidgetGroupProcessData,
// 	Dynamic:      true,
// }

// func (t dynamicPrompt) Define() *Widget {
// 	return WidgetDynamicPrompt
// }

// func (t dynamicPrompt) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
// 	templateStr, err := getNodeValueToString(nodeValue, "Template", true)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return script.Prompt{
// 		Meta:     nodeMeta.ToBaseScript(),
// 		Template: script.MultilineString(templateStr),
// 		RawInput: false,
// 	}, nil
// }
