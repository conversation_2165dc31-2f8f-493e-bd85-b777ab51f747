package widgets

import (
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyApiCall      = "WidgetKeyApiCall"
	WidgetKeyCodeInstance = "WidgetKeyCodeInstance"
	ParamIDCodeInstance   = "CodeInstance"
)

type apiCall struct {
}

var widgetApiCall = &Widget{
	Id:    WidgetKeyApiCall,
	Name:  "通用HTTP调用",
	Desc:  "可调用所有HTTP API服务，暂时只支持POST请求",
	Group: WidgetGroupChainTool,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "APIPath",
				Name:     "请求地址",
				Desc:     "url地址，例如 http://example.com/api/v1",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "Header",
				Name:     "请求头",
				Desc:     "请求头，例如 Content-Type = application/json 或 Authorization = Bearer xxx",
				Type:     pb.DynamicParam_TYPE_KVITEM,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Multiple: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       "RequestBody",
				Name:     "请求体",
				Desc:     "http的请求体，要求是字典、列表、数字、字符串等合法的http请求数据," + BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "http请求响应的数据，可能是字典、列表、数字、字符串等合法的http响应数据," + BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (f apiCall) Define() *Widget {
	return widgetApiCall
}

func (f apiCall) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	url, err := getNodeValueToString(nodeValue, "APIPath", false)
	if err != nil {
		return nil, err
	}
	header, err := getHeaderJSON(nodeValue, "Header", true)
	if err != nil {
		return nil, err
	}
	return script.HttpCall{
		Meta:    nodeMeta.ToBaseScript(),
		URL:     url,
		Header:  script.MultilineString(header),
		Method:  "POST",
		Timeout: 60,
	}, nil
}

type codeInstance struct {
}

var widgetCodeInstance = &Widget{
	Id:    WidgetKeyCodeInstance,
	Name:  "自定义算子(代码开发)",
	Desc:  "用于打开代码实例实时编辑调试，实际应用时建议将代码实例固化成自定义算子使用。",
	Group: WidgetGroupAdvanced,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       "Content",
				Name:     "输入数据",
				Desc:     BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDCodeInstance,
				Name:     "代码实例",
				Desc:     "代码实例,点击可查看或编辑代码",
				Type:     pb.DynamicParam_TYPE_CODE_INSTANCE_ID,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (c codeInstance) Define() *Widget {
	return widgetCodeInstance
}

type AppletWidgetInstance struct {
	ServiceAddr string `json:"serviceAddr"`
}

func (c codeInstance) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	instance, err := GetStringNodeValueTo[AppletWidgetInstance](nodeValue, ParamIDCodeInstance)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to parse value with key: %s", ParamIDCodeInstance)
	}

	if instance.ServiceAddr == "" {
		return nil, stderr.Wrap(err, "serviceAddr is empty str")
	}
	return script.HttpCall{
		Meta:    nodeMeta.ToBaseScript(),
		URL:     fmt.Sprintf("http://%s:80/", instance.ServiceAddr),
		Method:  "POST",
		Timeout: 60,
	}, nil
}
