package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyConditionJudge = "WidgetKeyConditionJudge"
)

const (
	OutPutKeyIF   = "OutPutIF"
	OutPutKeyElse = "OutPutElse"
)

type conditionJudge struct {
}

var widgetConditionJudge = &Widget{
	Id:    WidgetKeyConditionJudge,
	Name:  "条件判断",
	Desc:  "把数据流分叉成if分支与else分支，根据输入的条件决定数据流向的分支",
	Group: WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入数据",
				Desc: "待执行判断条件的数据," + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "Code",
				Name:     "判断条件",
				Desc:     DefaultConditionJudgeCode,
				Required: true,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_INPUT,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   OutPutKeyIF,
				Name: "If",
				Desc: "条件成立时的输出端点，原样输出输入的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   OutPutKeyElse,
				Name: "Else",
				Desc: "条件不成立时的输出端点，原样输出输入的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
	},
}

func (f conditionJudge) Define() *Widget {
	return widgetConditionJudge
}

func (f conditionJudge) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	widgetParam := new(engine.WidgetParamsConditionalSwitch)
	outPutInfos, err := getNodeValueToOutPut(nodeValue, false)
	if err != nil {
		return nil, err
	}
	condition, err := getNodeValueToString(nodeValue, "Code", false)
	if err != nil {
		return nil, err
	}
	widgetParam.Condition = condition
	for _, n := range outPutInfos.NodeOutputs {
		if n.CurrentNodeOutputParam == OutPutKeyIF {
			widgetParam.TargetNodesIf = append(widgetParam.TargetNodesIf, n.NextNodeID)
		} else if n.CurrentNodeOutputParam == OutPutKeyElse {
			widgetParam.TargetNodesElse = append(widgetParam.TargetNodesElse, n.NextNodeID)
		}
	}
	if len(widgetParam.TargetNodesIf) == 0 || len(widgetParam.TargetNodesElse) == 0 {
		return nil, stderr.Internal.Errorf("build condition judge script err, no next node info for if or else")
	}
	return script.ConditionalSwitch{
		Meta:   nodeMeta.ToBaseScript(),
		Params: stdsrv.AnyToString(widgetParam),
	}, nil
}

const DefaultConditionJudgeCode = `
使用input变量存储上游传入的数据
假设input存储的数据为
{
  "string": "string",
  "number": 123,
  "dict": { "k": "v" }
}
可以使用下面的语法表示判断条件, 更复杂的判断条件请参考Jsonnet语法
input.number == 123 && input.number > 100 || input.dict.k == "v"
`
