package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyImageAnalysis = "WidgetKeyImageAnalysis"
)

type imageAnalysis struct{}

var widgetKeyImageAnalysis = &Widget{
	Id:    WidgetKeyImageAnalysis,
	Name:  "图片理解",
	Desc:  "对上传的图片进行解析理解",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassFile,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile),
			Define: DynamicParam{
				Id:       "File",
				Name:     "图片",
				Desc:     BaseSyncLimits(DataTypeSFSFile).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDUseBase64,
				Name:         "Base64编码",
				Desc:         "是否使用base64将图片编码后再进行模型调用,远程及跨空间调用时需启用。",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "false",
				//Hidden: true,
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       "Question",
				Name:     "文本",
				Desc:     "文字描述,辅助图片内容分析理解," + BaseSyncLimits(DataTypeString).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "服务",
				Desc:       "图片理解服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetImageToTextModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "图片理解的文本内容," + BaseAnyLimits(DataTypeString).String(),
			},
		},
	},
}

func (i imageAnalysis) Define() *Widget {
	return widgetKeyImageAnalysis
}

func (i imageAnalysis) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	useBase64 := getBoolValueWithDefault(nodeValue, ParamIDUseBase64, true)
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}
	if params.SubKind != pb.ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT {
		return nil, stderr.Errorf("the sub kind of model is error")
	}

	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:      nodeMeta.ToBaseScript(),
		UseBase64: useBase64,
		Params:    script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}
