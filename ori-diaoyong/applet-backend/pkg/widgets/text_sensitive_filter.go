package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

// 文件处理算子

const (
	WidgetKeyTextSensitiveFilter = "WidgetKeyTextSensitiveFilter"
)

type textSensitiveFilter struct {
}

var widgetTextSensitiveFilter = &Widget{
	Id:    WidgetKeyTextSensitiveFilter,
	Name:  "敏感词过滤",
	Desc:  "用于过滤输入输出过程中的敏感词，当检测到敏感词出现时，会立即中断整个调用过程",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "检测文本",
				Desc:     "待进行敏感词检测的文本",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "sensitiveWords",
				Name:         "敏感词列表",
				Desc:         "需要过滤的敏感词列表，每行放置一个敏感词",
				Type:         pb.DynamicParam_TYPE_TEXTAREA,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: "敏感词A\n敏感词B\n敏感词C\n敏感词D",
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "displayText",
				Name:         "命中后回复",
				Desc:         "当检测到敏感词时，中断流程后返回的回复。",
				Type:         pb.DynamicParam_TYPE_TEXTAREA,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				DefaultValue: "很抱歉，我无法讨论这个特定的话题。如果你对其他问题有兴趣，比如科技、文化、教育或者日常生活中的任何事情，我都非常乐意帮助你。请告诉我你的其他需求吧！",
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (t textSensitiveFilter) Define() *Widget {
	return widgetTextSensitiveFilter
}

func (t textSensitiveFilter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	sensitiveWords, err := getNodeValueToString(nodeValue, "sensitiveWords", false)
	if err != nil {
		return nil, err
	}
	displayText, err := getNodeValueToString(nodeValue, "displayText", true)
	if err != nil {
		return nil, err
	}
	return script.TextSensitiveFilter{
		Meta:           nodeMeta.ToBaseScript(),
		SensitiveWords: sensitiveWords,
		DisplayText:    displayText,
	}, nil
}
