package widgets

import (
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyChunksOutput   = "WidgetKeyChunksOutput"
	ParamIDChunks           = "Chunks"
	ParamIDElements         = "Elements"
	ParamIDLoadChunkResp    = "LoadChunkResp"
	ParamIDEnableSplit      = "EnableSplit"
	ParamIDMaxSplitLen      = "MaxSplitLen"
	DefaultValueMaxSplitLen = 100
)

type chunksOutput struct {
}

var widgetChunksOutput = &Widget{
	Id:    WidgetKeyChunksOutput,
	Name:  "知识加工输出(自定义策略)",
	Desc:  "标记算子,代表链的最终输出为pb.DocSvcLoadChunkRsp",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:       ParamIDChunks,
				Name:     "chunks",
				Desc:     "chunks文本，" + BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeElements),
			Define: DynamicParam{
				Id:       ParamIDElements,
				Name:     "elements",
				Desc:     "elements文本，" + BaseSyncLimits(DataTypeElements).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		//{
		//	DataClass: DataClassJson,
		//	Category:  ParamTypeNodeInPort,
		//	Define: DynamicParam{
		//		Id:       ParamIDLoadChunkResp,
		//		Name:     "LoadChunkResp",
		//		Desc:     "要求上游输入数据为pb.DocSvcLoadChunkRsp",
		//		DataType: pb.DynamicParam_DATA_TYPE_STRING,
		//	},
		//},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnableSplit,
				Name:         "批次输出",
				Desc:         "开启后,将最后获得的LoadChunk数组分为多个批次输出",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueFalse,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDMaxSplitLen,
				Name:     "批次长度",
				Desc:     "每个批次输出时的数组长度",
				DataType: pb.DynamicParam_DATA_TYPE_INT,
				Type:     pb.DynamicParam_TYPE_NUMBER,
				NumberRange: &pb.NumberRange{
					Min:  100,
					Step: 100,
					Max:  10000,
				},
				Required:     true,
				DefaultValue: fmt.Sprintf("%d", DefaultValueMaxSplitLen),
				Precondition: BuildBothPreconditionString(ParamIDEnableSplit, ParamValueTrue),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeLoadChunk),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeLoadChunk).String(),
			},
		},
	},
}

func (c chunksOutput) Define() *Widget {
	return widgetChunksOutput
}

func (c chunksOutput) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	widgetParams := &WidgetParamsChunksOutPut{
		EnableSplit: getBoolValueWithDefault(nodeValue, ParamIDEnableSplit, false),
		MaxSplitLen: getIntValueWithDefault(nodeValue, ParamIDMaxSplitLen, DefaultValueMaxSplitLen),
	}
	return script.ChunksOutput{
		Meta:   nodeMeta.ToBaseScript(),
		Params: stdsrv.AnyToString(widgetParams),
	}, nil
}

const (
	WidgetKeyQuestionClassifierOutput = "WidgetKeyQuestionClassifierOutput"
	ParamIDQuestionClassifierResp     = "QuestionClassifierResp"
)

type questionClassifierOutput struct {
}

var widgetQuestionClassifierOutput = &Widget{
	Id:    WidgetKeyQuestionClassifierOutput,
	Name:  "问题分类输出(自定义策略)",
	Desc:  "标记算子,代表链的最终输出为QuestionClassifierResp",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeQuestionClassifyResp),
			Define: DynamicParam{
				Id:       "Input",
				Required: true,
				Name:     "输入",
				Desc:     "问题分类服务的具体格式," + BaseSyncLimits(DataTypeQuestionClassifyResp).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeQuestionClassifyResp),
			Define: DynamicParam{
				Required: true,
				Id:       "OutPut",
				Name:     "输出",
				Desc:     "问题分类服务的具体格式," + BaseSyncLimits(DataTypeQuestionClassifyResp).String(),
			},
		},
	},
}

func (q questionClassifierOutput) Define() *Widget {
	return widgetQuestionClassifierOutput
}

func (q questionClassifierOutput) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.QuestionClassifierOutPut{
		Meta: nodeMeta.ToBaseScript(),
	}, nil
}

type WidgetParamsChunksOutPut struct {
	EnableSplit bool
	MaxSplitLen int
}
