package widgets

import (
	"fmt"
	"net/url"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyParagraphAggregator = "WidgetKeyParagraphAggregator"
	QueryParamChunkStrategy      = "chunkStrategy"
	QueryParamChunkSize          = "chunkSize"
	QueryParamOverlap            = "overlap"
	QueryParamMerge              = "merge"
	QueryParamSeparators         = "separators"
	QueryParamType               = "type"
)

var (
	paragraphAggregateQuerys = []string{
		QueryParamChunkStrategy,
		QueryParamChunkSize,
		QueryParamOverlap,
		QueryParamMerge,
		QueryParamSeparators,
		QueryParamType,
	}
)

type paragraphAggregator struct {
}

var widgetParagraphAggregator = &Widget{
	Id:    WidgetKeyParagraphAggregator,
	Name:  "解析元素转切片",
	Desc:  "Elements转Chunks",
	Group: WidgetGroupProcessKnowledge,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeElementsV2),
			Define: DynamicParam{
				Id:   "Content",
				Name: "输入",
				Desc: `待聚合的元素，` + BaseSyncLimits(DataTypeElementsV2).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamChunkStrategy,
				Name:         "分段策略",
				Desc:         "分段成 []pb.Chunk 的策略，暂时只支持basic",
				Type:         pb.DynamicParam_TYPE_SELECTOR,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Disabled:     true,
				Datasource:   "basic@@basic",
				DefaultValue: "basic",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamChunkSize,
				Name:         "分段长度",
				Desc:         "分段成 []pb.Chunk 的长度，默认500",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamMerge,
				Name:         "是否合并",
				Desc:         "是否合并成‘分段长度’的指定的长度，并返回结果",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: "true",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamOverlap,
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           QueryParamType,
				Name:         "分段方式",
				Desc:         "分段成 []pb.Chunk 的方式，选character时不分优先级（默认），选recursive时越靠前的分隔符越优先使用，",
				Type:         pb.DynamicParam_TYPE_SELECTOR,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Datasource:   "CHARACTER@@character,RECURSIVE@@recursive",
				DefaultValue: "CHARACTER",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       QueryParamSeparators,
				Name:     "分割符",
				Desc:     "多选分割符，是否按照优先级分割取决于‘分段方式’",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: false,
				Multiple: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeLoadChunk),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "合并后的若干个文本段落，" + BaseSyncLimits(DataTypeLoadChunk).String(),
			},
		},
	},
}

func (t paragraphAggregator) Define() *Widget {
	return widgetParagraphAggregator
}

func (f paragraphAggregator) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	baseURL := conf.GetDocSvcAddr() + conf.Config.DocSvcConfig.ChunkApi.Url
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}

	q := u.Query()

	for _, param := range paragraphAggregateQuerys {
		var v interface{}
		if param == QueryParamMerge {
			v = getBoolValueWithDefault(nodeValue, param, false)
		} else if param == QueryParamChunkSize || param == QueryParamOverlap {
			v = getInt64ValueWithDefault(nodeValue, param, 0)
		} else if param == QueryParamSeparators {
			v = getStringListWithDefault(nodeValue, param, make([]string, 0))
			if newV, ok := v.([]string); ok {
				v = newV
				for _, value := range newV {
					q.Add(param, value)
				}
			} else {
				stdlog.Warnf("WARNING: Parameter %s is not a []string type", v)
			}
		} else {
			v = getStringValueWithDefault(nodeValue, param, "")
		}
		if v != nil && v != "" && param != QueryParamSeparators {
			q.Set(param, fmt.Sprint(v))
		}
	}

	u.RawQuery = q.Encode()

	return script.HttpCall{
		Meta:        nodeMeta.ToBaseScript(),
		URL:         u.String(),
		Header:      "",
		Method:      "POST",
		Timeout:     60,
		ServiceType: script.HTTPCallServiceTypeParagraphAggregate,
	}, nil
}
