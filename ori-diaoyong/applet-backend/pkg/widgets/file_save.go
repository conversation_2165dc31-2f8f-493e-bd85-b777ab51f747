package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyFileSave                 = "WidgetKeyFileSave"
	ParamIDFileSaveFilePath           = "FilePath"
	ParamIDFileSaveAllowOverwrite     = "AllowOverwrite"
	ParamIDFileSaveEnableDownloadMode = "EnableDownloadMode"
	ParamIDFileSaveAdvanced           = "AdvancedConfig"
)

type fileSave struct {
}

var widgetFileSave = &Widget{
	Id:    WidgetKeyFileSave,
	Name:  "文件保存",
	Desc:  "用于将最终结果保存为文件并存储在文件系统中",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile, DataTypeSFSFiles, DataTypeGeneralMap, DataTypeGeneralMaps),
			Define: DynamicParam{
				Id:   "ContentInput",
				Name: "文件",
				Desc: `待保存的文件，` + BaseSyncLimits(DataTypeSFSFile, DataTypeSFSFiles, DataTypeGeneralMap, DataTypeGeneralMaps).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileSaveAdvanced,
				Name:         "高级配置",
				Desc:         "是否开启高级配置，未开启时默认文件保存到空间根目录，允许覆盖已有文件",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueFalse,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileSaveFilePath,
				Name:         "文件目录",
				Desc:         "要保存的文件目录，目录不存在时会创建，例如testDir、testDir/temp",
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Type:         pb.DynamicParam_TYPE_INPUT,
				Precondition: BuildBothPreconditionString(ParamIDFileSaveAdvanced, ParamValueTrue),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileSaveAllowOverwrite,
				Name:         "允许覆盖",
				Desc:         "保存文件时是否允许覆盖已存在的文件，当设置为不允许覆盖时，文件已存在会报错",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueTrue,
				Precondition: BuildBothPreconditionString(ParamIDFileSaveAdvanced, ParamValueTrue),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDFileSaveEnableDownloadMode,
				Name:         "下载模式",
				Desc:         "将原始的json格式输出,转化为一个可供用户下载的文件地址",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueFalse,
				Precondition: BuildBothPreconditionString(ParamIDFileSaveAdvanced, ParamValueTrue),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFiles, DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "保存后的文件，" + BaseSyncLimits(DataTypeSFSFiles, DataTypeString).String(),
			},
		},
	},
}

func (f fileSave) Define() *Widget {
	return widgetFileSave
}

func (f fileSave) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	filePath, err := getNodeValueToString(nodeValue, ParamIDFileSaveFilePath, true)
	if err != nil {
		return nil, err
	}
	allowOverwrite, err := getNodeValueToBool(nodeValue, ParamIDFileSaveAllowOverwrite, true)
	if err != nil {
		return nil, err
	}

	return script.FileSave{
		Meta:               nodeMeta.ToBaseScript(),
		SaveDir:            filePath,
		DisallowOverwrite:  !allowOverwrite,
		EnableDownloadMode: getBoolValueWithDefault(nodeValue, ParamIDFileSaveEnableDownloadMode, false),
	}, nil
}
