package widgets

import (
	"encoding/json"
	"strconv"
	"strings"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

func getScriptNameFromWidgetKey(key string) string {
	if strings.HasPrefix(key, "WidgetKey") {
		return key[len("WidgetKey"):]
	}
	return key
}

func LLMParamToPath(ip string, port string, modelName string) {
	return
}

func getHeaderJSON(value map[string]interface{}, key string, ignoreEmpty bool) (string, error) {
	m, err := getNodeValueToMap(value, key, ignoreEmpty)
	if err != nil {
		return "", err
	}
	// m["Content-Type"] = "application/json"
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func getNodeValueToMap(value map[string]interface{}, key string, ignoreEmpty bool) (map[string]string, error) {
	res := make(map[string]string, 0)
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return res, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return res, nil
	}
	oriKV, ok := v.([]interface{})
	if !ok {
		return res, stderr.Internal.Error("invalid kv format :%v", v)
	}
	for _, kv := range oriKV {
		kvMap, ok := kv.(map[string]interface{})
		if !ok {
			return res, stderr.Internal.Error("invalid kv format :%v", v)
		}
		key := kvMap["key"]
		value := kvMap["value"]
		stringKey, ok := key.(string)
		if !ok {
			return res, stderr.Internal.Error("invalid kv format :%v", v)
		}
		stringValue, ok := value.(string)
		if !ok {
			return res, stderr.Internal.Error("invalid kv format :%v", v)
		}
		res[stringKey] = stringValue
	}
	return res, nil
}

func getNodeValueToStringList(value map[string]interface{}, key string, ignoreEmpty bool) ([]string, error) {
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return []string{}, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return []string{}, nil
	}
	list, ok := v.([]interface{})
	if !ok {
		return []string{}, stderr.Internal.Error("invalid string list :%v", v)
	}
	res := make([]string, 0)
	for _, s := range list {
		sv, ok := s.(string)
		if !ok {
			return []string{}, stderr.Internal.Error("invalid string :%v", s)
		}
		res = append(res, sv)
	}
	return res, nil
}

func getNodeValueToString(value map[string]interface{}, key string, ignoreEmpty bool) (string, error) {
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return "", stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return "", nil
	}
	res, ok := v.(string)
	if !ok {
		return "", stderr.Internal.Error("ScriptConfig,invalid value :%v for %v", v, key)
	}
	return res, nil
}

func GetStringNodeValueTo[T any](value map[string]interface{}, key string) (*T, error) {
	str, err := getNodeValueToString(value, key, true)
	if err != nil {
		return nil, err
	}
	res := new(T)
	if str == "" {
		return res, nil
	}
	if err = stdsrv.UnmarshalMixWithProto(str, res); err != nil {
		return nil, err
	}
	return res, nil
}

func getNodeValueToBool(value map[string]interface{}, key string, ignoreEmpty bool) (bool, error) {
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return false, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return false, nil
	}
	// 根据值的类型进行不同处理
	switch val := v.(type) {
	case string:
		// 字符串类型：尝试解析为布尔值，支持 "true"/"false"/"1"/"0" 等格式
		if res, err := strconv.ParseBool(val); err == nil {
			return res, nil
		}
		return false, stderr.Internal.Errorf("ScriptConfig,invalid string value :%v for %v", v, key)
	case bool:
		// 布尔类型：直接返回值
		return val, nil
	default:
		// 其他类型：返回错误
		return false, stderr.Internal.Errorf("ScriptConfig,invalid value :%v for %v", v, key)
	}
}

func getBoolValueWithDefault(value map[string]interface{}, key string, defaultValue bool) bool {
	res, err := getNodeValueToBool(value, key, false)
	if err != nil {
		return defaultValue
	}
	return res
}
func getInt64ValueWithDefault(value map[string]interface{}, key string, defaultValue int64) int64 {
	res, err := getNodeValueToInt64(value, key, false)
	if err != nil {
		return defaultValue
	}
	return res
}

func getInt32ValueWithDefault(value map[string]interface{}, key string, defaultValue int32) int32 {
	return int32(getInt64ValueWithDefault(value, key, int64(defaultValue)))
}

func getIntValueWithDefault(value map[string]interface{}, key string, defaultValue int) int {
	return int(getInt64ValueWithDefault(value, key, int64(defaultValue)))
}

func getFloat64ValueWithDefault(value map[string]interface{}, key string, defaultValue float64) float64 {
	res, err := getNodeValueToFloat64(value, key, false)
	if err != nil {
		return defaultValue
	}
	return res
}
func getFloat32ValueWithDefault(value map[string]interface{}, key string, defaultValue float32) float32 {
	return float32(getFloat64ValueWithDefault(value, key, float64(defaultValue)))
}

func isEmptyString(value map[string]interface{}, key string) bool {
	str := getStringValueWithDefault(value, key, "")
	return strings.TrimSpace(str) == ""
}
func getStringValueWithDefault(value map[string]interface{}, key string, defaultValue string) string {
	res, err := getNodeValueToString(value, key, false)
	if err != nil {
		return defaultValue
	}
	return res
}
func getStringListWithDefault(value map[string]interface{}, key string, defaultValue []string) []string {
	res, err := getNodeValueToStringList(value, key, false)
	if err != nil {
		return defaultValue
	}
	return res
}

func getProjectID(nodeMeta *NodeMeta) (projectID, tenantID, token string, err error) {
	ctx := nodeMeta.Context
	if ctx == nil {
		return "", "", "", stderr.Internal.Errorf("ctx is nil")
	}
	token, err = helper.GetToken(ctx)
	if err != nil {
		return "", "", "", err
	}
	return helper.GetProjectID(ctx), helper.GetTenantID(ctx), token, nil
}

func cvtStringList2Map(values []string) map[string]bool {
	res := make(map[string]bool)
	for _, v := range values {
		res[v] = true
	}
	return res
}

func cvtSlice2Map[T comparable](values []T) map[T]bool {
	res := make(map[T]bool)
	for _, v := range values {
		res[v] = true
	}
	return res
}

func getNodeValueToInputInfo(value map[string]interface{}, ignoreEmpty bool) (NodeInputInfos, error) {
	v, ok := value[NodeInputInfoKey]
	if !ok {
		if !ignoreEmpty {
			return NodeInputInfos{}, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", NodeInputInfoKey)
		}
		return NodeInputInfos{}, nil
	}
	res, ok := v.(NodeInputInfos)
	if !ok {
		return res, stderr.Internal.Error("ScriptConfig,invalid value :%v for %v", v, NodeInputInfoKey)
	}
	return res, nil
}

func getNodeValueToOutPut(value map[string]interface{}, ignoreEmpty bool) (NodeOutputInfos, error) {
	v, ok := value[NodeOutputInfoKey]
	if !ok {
		if !ignoreEmpty {
			return NodeOutputInfos{}, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", NodeOutputInfoKey)
		}
		return NodeOutputInfos{}, nil
	}
	res, ok := v.(NodeOutputInfos)
	if !ok {
		return res, stderr.Internal.Error("ScriptConfig,invalid value :%v for %v", v, NodeOutputInfoKey)
	}
	return res, nil
}

func getNodeValueToInt64(value map[string]interface{}, key string, ignoreEmpty bool) (int64, error) {
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return 0, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return 0, nil
	}
	chunkSizeF64, ok := v.(float64)
	if !ok {
		return 0, stderr.Internal.Error("invalid value :%v for %v", v, key)
	}
	res := int64(chunkSizeF64)
	return res, nil
}

func getNodeValueToFloat64(value map[string]interface{}, key string, ignoreEmpty bool) (float64, error) {
	v, ok := value[key]
	if !ok {
		if !ignoreEmpty {
			return 0, stderr.AppletChainParamMissedError.Error("ScriptConfig err ,no value for param : %v", key)
		}
		return 0, nil

	}
	res, ok := v.(float64)
	if !ok {
		return 0, stderr.Internal.Error("invalid value :%v for %v", v, key)
	}
	return res, nil
}

func getNodeValueToTimeSecond(value map[string]interface{}, key string, ignoreEmpty bool) (script.TimeoutSecond, error) {
	i64Value, err := getNodeValueToInt64(value, key, ignoreEmpty)
	if err != nil {
		return 0, err
	}
	return script.TimeoutSecond(i64Value), nil

}

type ModelServiceConditions struct {
	Kind                 string `json:"kind,omitempty"`       // 模型类型
	SubKind              string `json:"subKind,omitempty"`    // 模型子类型
	OnlyAvailable        bool   `json:"onlyAvailable"`        // 是否只显示运行中的服务
	ShowPublic           bool   `json:"showPublic"`           // 是否包含公共空间中的服务
	ShowRemote           bool   `json:"showRemote"`           // 是否包含远程模型服务
	IncludeMWService     bool   `json:"includeMWService"`     // 是否是包含模型仓库部署的服务
	IncludeSeldonService bool   `json:"includeSeldonService"` // 是否是包含Seldon部署的服务
	OmitCensored         bool   `json:"omitCensored"`         // 是否过滤开启了安全(敏感词)防护的服务
}

func GetLLMModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_NLP, pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION)
}
func GetOcrModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_CV, pb.ModelSubKind_MODEL_SUB_KIND_CV_OCR)
}

func GetImage2TextModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_MULTI, pb.ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT)
}

func GetVectorModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_NLP, pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR)
}

func GetRerankingModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_NLP, pb.ModelSubKind_MODEL_SUB_KIND_NLP_RERANKING)
}

func GetAudioToTextModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_SR, pb.ModelSubKind_MODEL_SUB_KIND_SR_STT)
}

func GetImageToTextModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_MULTI, pb.ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT)
}

func GetTextToImageModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_MULTI, pb.ModelSubKind_MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE)
}

func GetDlieModelSvcConditionsStr() string {
	return GetModelSvcConditionsStr(pb.ModelKind_MODEL_KIND_UNSPECIFIED, pb.ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED)
}

func GetModelSvcConditionsStr(kind pb.ModelKind, subKind pb.ModelSubKind) string {
	conditions := getFixedModelSvcConditions()
	// 如果kind和subKind是unspecified，则不设置，保持空字符串
	if kind != pb.ModelKind_MODEL_KIND_UNSPECIFIED {
		conditions.Kind = kind.String()
	}
	if subKind != pb.ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED {
		conditions.SubKind = subKind.String()
	}
	return stdsrv.AnyToString(conditions)
}

func getFixedModelSvcConditions() *ModelServiceConditions {
	return &ModelServiceConditions{
		OnlyAvailable:        true,
		ShowPublic:           true,
		ShowRemote:           true,
		IncludeMWService:     true,
		IncludeSeldonService: true,
		OmitCensored:         false,
	}
}
