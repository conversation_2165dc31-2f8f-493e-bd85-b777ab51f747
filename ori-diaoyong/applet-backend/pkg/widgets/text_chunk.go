package widgets

import (
	"fmt"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextChunk = "WidgetKeyTextChunk"
)

var (
	textChunkQuerys = []string{
		"strategy",
		"chunkSize",
		"overlap",
	}
)

type textChunk struct {
}

var widgetTextChunk = &Widget{
	Id:    WidgetKeyTextChunk,
	Name:  "文本分段",
	Desc:  "用于把文本解析后返回的elements结构进行分段，转换为知识库分段结构",
	Group: WidgetGroupProcessKnowledge,
	Params: []WidgetParam{
		{
			DataClass: DataClassJson,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:   "Bytes",
				Name: "文件字节流",
				Desc: "文件字节流",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         "strategy",
				Name:       "分段策略",
				Desc:       "分段策略，默认basic",
				Type:       pb.DynamicParam_TYPE_SELECTOR,
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   false,
				Datasource: "basic@@basic(默认分段),title@@title(根据title结构分段)",
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分段长度",
				Desc:         "分段长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "overlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (f textChunk) Define() *Widget {
	return widgetTextChunk
}

func (f textChunk) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	querys := make([]string, 0)
	for _, q := range textChunkQuerys {
		v, err := getNodeValueToString(nodeValue, q, true)
		if err != nil {
			return nil, err
		}
		if v != "" {
			querys = append(querys, fmt.Sprintf("%s=%s", q, v))
		}
	}
	url := conf.GetDocSvcAddr() + conf.Config.DocSvcConfig.ChunkApi.Url
	if len(querys) > 0 {
		url = fmt.Sprintf("%s?%s", url, strings.Join(querys, "&"))
	}
	return script.HttpCall{
		Meta:    nodeMeta.ToBaseScript(),
		URL:     url,
		Header:  "",
		Method:  "POST",
		Timeout: 60,
	}, nil
}
