package script

type BaseScript struct {
	// 唯一标识
	ID string `json:"id"`
	// 节点ID，相同的子链被拖入画布多次时，NodeID有可能相同
	NodeID string `json:"nodeID"`
	// 子链ID，子链中不同算子所属的subChainID相同，对应画布中应用链调用算子的nodeID
	SubChainID string `json:"subChainID"`
	// 节点名称
	NodeName string `json:"nodeName"`
	// 主链名称
	ChainName string `json:"chainName"`
	// 子链名称，子链中不同算子所属的SubChainName相同
	SubChainName string `json:"subChainName"`
	WidgetKey    string `json:"widgetKey"` // 算子类别id
}
