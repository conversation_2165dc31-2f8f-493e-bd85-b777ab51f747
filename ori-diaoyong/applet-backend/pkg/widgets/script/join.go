package script

import (
	"fmt"
	"strings"

	"github.com/influxdata/kapacitor/uuid"
)

type Join struct {
	Meta             *BaseScript   `json:"meta"`
	JoinElement      Val           `json:"joinElement" scriptType:"constructor"`
	As               Val           `json:"as"`
	Tolerance        TimeoutSecond `json:"tolerance"`
	CacheEmittedSets bool          `json:"cacheEmittedSets"`
}

func (f Join) ScriptName() string {
	return "join"
}

type NodeJoinParam struct {
	NodeID    string
	NodeAlias string
}

func GenJoinScript(nodes []*NodeJoinParam, nodeMeta *BaseScript, isCacheEnabled bool) (string, error) {
	if nodeMeta == nil {
		nodeMeta = &BaseScript{
			ID: fmt.Sprintf("join_%s", uuid.New().String()),
		}
	}
	if nodeMeta.ID == "" {
		nodeMeta.ID = fmt.Sprintf("join_%s", uuid.New().String())
	}
	JoinElementBuilder := strings.Builder{}
	AsBuilder := strings.Builder{}
	//builder.WriteString(firstNode.NodeID)
	//builder.WriteString(fmt.Sprintf("var_%s|join(", firstNode.NodeID))
	for i := 1; i < len(nodes); i++ {
		JoinElementBuilder.WriteString(fmt.Sprintf("var_%s", nodes[i].NodeID))
		if i != len(nodes)-1 {
			JoinElementBuilder.WriteString(",")
		}
	}

	//builder.WriteString(fmt.Sprintf(").as("))
	for i, _ := range nodes {
		AsBuilder.WriteString("'")
		AsBuilder.WriteString(nodes[i].NodeAlias)
		AsBuilder.WriteString("'")
		if i != len(nodes)-1 {
			AsBuilder.WriteString(",")
		}
	}
	join := Join{
		Meta:             nodeMeta,
		JoinElement:      Val(JoinElementBuilder.String()),
		As:               Val(AsBuilder.String()),
		Tolerance:        TimeoutSecond(60),
		CacheEmittedSets: isCacheEnabled,
	}
	script, err := GenScript(join)
	if err != nil {
		return "", err
	}
	firstNode := nodes[0]
	return fmt.Sprintf("var_%s|%s", firstNode.NodeID, script), nil
}
