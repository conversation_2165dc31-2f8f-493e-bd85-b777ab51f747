package script

type HttpCall struct {
	Meta        *BaseScript         `json:"meta"`
	URL         string              `json:"url"`
	Header      MultilineString     `json:"header"`
	Method      string              `json:"method"`
	Timeout     TimeoutSecond       `json:"timeout"`
	ServiceType HTTPCallServiceType `json:"serviceType"`
	Escape      bool                `json:"escape"`
	Body        MultilineString     `json:"body"`
}

func (l HttpCall) ScriptName() string {
	return "httpCall"
}

// HTTP调用算子，调用服务的类型
type HTTPCallServiceType string

const (
	// 调用文本分割服务
	HTTPCallServiceTypeTextSplit          HTTPCallServiceType = "TextSplit"
	HTTPCallServiceTypeBingSearch         HTTPCallServiceType = "BingSearch"
	HTTPCallServiceTypeSearXNGSearch      HTTPCallServiceType = "SearXNGSearch"
	HTTPCallServiceTypeTextParse          HTTPCallServiceType = "TextParse"
	HTTPCallServiceTypeParagraphAggregate HTTPCallServiceType = "ParagraphAggregate"
)
