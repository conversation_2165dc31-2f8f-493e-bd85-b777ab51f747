package models

import (
	"context"
	"fmt"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type TemplateGroupKey string

const (
	BasicQA                 TemplateGroupKey = "BasicQA"
	KnowledgeBaseQA         TemplateGroupKey = "KnowledgeBaseQA"
	DataBaseQA              TemplateGroupKey = "DataBaseQA"
	MultiModality           TemplateGroupKey = "MultiModality"
	ToolsCall               TemplateGroupKey = "ToolsCall"
	IntentRecognition       TemplateGroupKey = "IntentRecognition"
	CycleAndBranch          TemplateGroupKey = "CycleAndBranch"
	CustomDocumentParseTool TemplateGroupKey = "CustomDocumentParseTool"
	UserCustomTemplates     TemplateGroupKey = "UserCustomTemplates"
)

type TemplateGroupSortFlag int

const (
	SortFlagBasicQA TemplateGroupSortFlag = iota
	SortFlagKnowledgeBaseQA
	SortFlagDataBaseQA
	SortFlagMultiModality
	SortFlagToolsCall
	SortFlagIntentRecognition
	SortFlagCycleAndBranch
	SortFlagCustomDocumentParseTool
	SortFlagUserCustomTemplates
)

type ChainTemplate struct {
	ID               string           `json:"id"`
	Name             string           `json:"name"`
	Desc             string           `json:"desc"`
	MdFullDesc       string           `json:"md_full_desc"`
	TemplateGroupKey TemplateGroupKey `json:"template_group_key"`
	Template         *widgets.Chain   `json:"template"`
	CreatedTime      int64            `json:"created_time"`
	UpdatedTime      int64            `json:"updated_time"`
}
type TemplateGroupDesc struct {
	TemplateGroupKey TemplateGroupKey      `json:"template_group_key"`
	Name             string                `json:"name"`
	Desc             string                `json:"desc"`
	SortFlag         TemplateGroupSortFlag `json:"sort_flag"`
}
type TemplateGroup struct {
	*TemplateGroupDesc
	Templates []*ChainTemplate `json:"templates"`
}

func (c ChainTemplate) GetAITemplateDesc() AIChainTemplateDesc {
	return AIChainTemplateDesc{
		ID:   c.ID,
		Name: c.Name,
		Desc: c.MdFullDesc,
	}
}

func GetBuiltInGroupDesc(ctx context.Context) map[TemplateGroupKey]*TemplateGroupDesc {
	groups := make(map[TemplateGroupKey]*TemplateGroupDesc)
	groups[BasicQA] = &TemplateGroupDesc{
		TemplateGroupKey: BasicQA,
		Name:             "基础问答",
		Desc:             "基础问答",
		SortFlag:         SortFlagBasicQA,
	}
	groups[KnowledgeBaseQA] = &TemplateGroupDesc{
		TemplateGroupKey: KnowledgeBaseQA,
		Name:             "知识问答",
		Desc:             "知识问答",
		SortFlag:         SortFlagKnowledgeBaseQA,
	}
	groups[DataBaseQA] = &TemplateGroupDesc{
		TemplateGroupKey: DataBaseQA,
		Name:             "数据库问答",
		Desc:             "数据库问答",
		SortFlag:         SortFlagDataBaseQA,
	}
	groups[MultiModality] = &TemplateGroupDesc{
		TemplateGroupKey: MultiModality,
		Name:             "多模态",
		Desc:             "多模态",
		SortFlag:         SortFlagMultiModality,
	}
	groups[ToolsCall] = &TemplateGroupDesc{
		TemplateGroupKey: ToolsCall,
		Name:             "工具调用",
		Desc:             "工具调用",
		SortFlag:         SortFlagToolsCall,
	}
	groups[IntentRecognition] = &TemplateGroupDesc{
		TemplateGroupKey: IntentRecognition,
		Name:             "意图识别",
		Desc:             "意图识别",
		SortFlag:         SortFlagIntentRecognition,
	}
	groups[CycleAndBranch] = &TemplateGroupDesc{
		TemplateGroupKey: CycleAndBranch,
		Name:             "循环分支",
		Desc:             "循环分支",
		SortFlag:         SortFlagCycleAndBranch,
	}
	groups[CustomDocumentParseTool] = &TemplateGroupDesc{
		TemplateGroupKey: CustomDocumentParseTool,
		Name:             "自定义文本解析",
		Desc:             "自定义文本解析",
		SortFlag:         SortFlagCustomDocumentParseTool,
	}
	groups[UserCustomTemplates] = &TemplateGroupDesc{
		TemplateGroupKey: UserCustomTemplates,
		Name:             "用户自定义模板",
		Desc:             "用户自定义模板",
		SortFlag:         SortFlagUserCustomTemplates,
	}

	if !helper.IsChinese(ctx) {
		for _, v := range groups {
			groupKey := string(v.TemplateGroupKey)
			v.Name = groupKey
			v.Desc = groupKey
		}
	}
	return groups
}

const (
	templateId  = "${templateId}"
	cateInfo    = "${cateInfo}"
	purposeInfo = "${purposeInfo}"
)

var (
	zhMdDesc = fmt.Sprintf(
		`
<!--%s-->
**类别**: %s
**用途**: %s
`, templateId, cateInfo, purposeInfo)

	enMdDesc = fmt.Sprintf(
		`
<!--%s-->
**Category**: %s
**Purpose**: %s
`, templateId, cateInfo, purposeInfo)
)

func GenTemplateMdDesc(ctx context.Context, template *ChainTemplate) (string, error) {
	tgDesc := GetBuiltInGroupDesc(ctx)
	groupKey := template.TemplateGroupKey
	desc, ok := tgDesc[groupKey]
	if !ok {
		stderr.Errorf("can not find template group with key: %s", groupKey)
	}

	mdStr := zhMdDesc
	if !helper.IsChinese(ctx) {
		mdStr = enMdDesc
	}

	//替换对应字段
	replacer := strings.NewReplacer(
		templateId, template.ID,
		cateInfo, desc.Name,
		purposeInfo, template.Desc,
	)
	return replacer.Replace(mdStr), nil
}
