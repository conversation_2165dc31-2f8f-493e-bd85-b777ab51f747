package models

import (
	"context"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type CreateExperimentReq struct {
	ChainID      string       `json:"chain_id"  description:"相关的应用链ID"`
	Name         string       `json:"name"  description:"应用体验名称"`
	ImageUrl     string       `json:"image_url"  description:"封面的图片地址"`
	Introduction string       `json:"introduction"  description:"开场白，应用介绍"`
	Examples     []string     `json:"examples"  description:"常用问题示例"`
	LabelInfo    *LabelGroups `json:"label_info"   description:"标签信息"`
}

type AppletExperimentDO struct {
	ChainID      string          `json:"chain_id"  description:"相关的应用链ID"`
	Name         string          `json:"name"  description:"应用体验名称"`
	ImageUrl     string          `json:"image_url"  description:"封面的图片地址"`
	Introduction string          `json:"introduction"  description:"开场白，应用介绍"`
	Examples     []string        `json:"examples"  description:"常用问题示例"`
	LabelInfo    *LabelGroups    `json:"label_info"   description:"标签信息"`
	Status       string          `json:"status"  description:"应用体验是否发布"`
	ServiceInfo  *AppServiceInfo `json:"service_info"  description:"已发布的应用体验,所对应的服务信息"`
	CreatedBy    string          `json:"created_by"  description:"创建方式是属于应用链、智能体"`
	Creator      string          `json:"creator"  description:"创建人"`
	ProjectID    string          `json:"project_id"  description:"项目ID"`
	CreatedTime  int64           `json:"created_time"  description:"创建时间"`
	UpdatedTime  int64           `json:"updated_time"  description:"更新时间"`
}

func (a AppletExperimentDO) Valid() error {
	if a.ChainID == "" || a.Name == "" {
		return stderr.Internal.Error("the field of chain_id, name cannot be empty")
	}
	if a.Creator == "" {
		return stderr.Internal.Error("the field of creator cannot be empty")
	}
	if a.ProjectID == "" {
		return stderr.Internal.Error("the field of project_id cannot be empty")
	}
	return nil
}
func (a AppletExperimentDO) ToPO(ctx context.Context) (*generated.AppletExperiment, error) {
	experimentPO := new(generated.AppletExperiment)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(a, experimentPO); err != nil {
		return nil, err
	}
	// 拷贝结构体字段
	experimentPO.Examples = helper.Struct2String(a.Examples)
	experimentPO.LabelInfo = helper.Struct2String(a.LabelInfo)
	experimentPO.ServiceInfo = helper.Struct2String(a.ServiceInfo)
	// created、updatedTime由数据库自动控制
	return experimentPO, nil
}
func CvtExperimentPOToDO(experimentPO *generated.AppletExperiment) (*AppletExperimentDO, error) {
	experimentDO := new(AppletExperimentDO)
	// 拷贝同名、同类型的基本字段
	if err := helper.CopyTheSameFields(experimentPO, experimentDO); err != nil {
		return nil, err
	}
	// 给DO的结构体字段赋值
	experimentDO.Examples = make([]string, 0)
	if err := helper.String2Struct(experimentPO.Examples, &(experimentDO.Examples)); err != nil {
		return nil, err
	}
	experimentDO.LabelInfo = new(LabelGroups)
	if err := helper.String2Struct(experimentPO.LabelInfo, &(experimentDO.LabelInfo)); err != nil {
		return nil, err
	}
	experimentDO.ServiceInfo = new(AppServiceInfo)
	if err := helper.String2Struct(experimentPO.ServiceInfo, &(experimentDO.ServiceInfo)); err != nil {
		return nil, err
	}
	// 设置时间类型
	experimentDO.CreatedTime = helper.Time2UnixMilli(experimentPO.CreatedTime)
	experimentDO.UpdatedTime = helper.Time2UnixMilli(experimentPO.UpdatedTime)
	return experimentDO, nil
}
