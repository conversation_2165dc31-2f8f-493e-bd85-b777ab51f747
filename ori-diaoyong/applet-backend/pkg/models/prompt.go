package models

import (
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

type PromptTemplate struct {
	ID         int64    `json:"id"`
	Name       string   `json:"name"`
	Template   string   `json:"template"`
	Published  bool     `json:"published"`
	Source     string   `json:"source"`
	Parameters []string `json:"parameters"`
}

type WidgetInstance struct {
	widget *widgets.Widget
	script *widgets.ScriptConfig
}

// todo fixme
func NewWidgetInstance(widget *widgets.Widget, script *widgets.ScriptConfig, s script.Generator) *WidgetInstance {
	return &WidgetInstance{widget: widget, script: script}
}

func (w WidgetInstance) Define() *widgets.Widget {
	return w.widget
}

func (w WidgetInstance) ScriptConfig() *widgets.ScriptConfig {
	return w.script
}

// fixme
func (w WidgetInstance) Script(nodeMeta widgets.NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return nil, nil
}

// ToWidget 动态生成IWidget的实例
func (p PromptTemplate) ToWidget() (widgets.IWidget, error) {
	widget, err := p.ToWidgetModel()
	if err != nil {
		return nil, err
	}
	return NewWidgetInstance(widget, p.toScriptConfig(), nil), nil
}

// ToWidgetModel 提示词模板算子
func (p PromptTemplate) ToWidgetModel() (*widgets.Widget, error) {
	params := make([]widgets.WidgetParam, 0)
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Preview:   true,
		Define: widgets.DynamicParam{
			Id:           "Template",
			Name:         "模板",
			Disabled:     true,
			DefaultValue: p.Template,
		},
	})
	for _, param := range p.Parameters {
		params = append(params, widgets.WidgetParam{
			DataClass:   widgets.DataClassString,
			Category:    widgets.ParamTypeNodeInPort,
			ParamLimits: widgets.SyncAnyLimits(),
			Define: widgets.DynamicParam{
				Id:       param,
				Name:     param,
				Desc:     param,
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		})
	}
	params = append(params, widgets.WidgetParam{
		DataClass:   widgets.DataClassString,
		Category:    widgets.ParamTypeNodeOutPort,
		ParamLimits: widgets.BaseSyncLimits(widgets.DataTypeString),
		Define: widgets.DynamicParam{
			Id: "OutPut",
		},
	})

	baseDefine, err := widgets.WidgetFactoryImpl.GetWidgetDefine(widgets.WidgetKeySampleCubePrompt)
	if err != nil {
		return nil, err
	}
	baseDefine.Name = p.Name
	baseDefine.Desc = p.Template
	baseDefine.Params = params
	return baseDefine, nil
}

func (p PromptTemplate) toScriptConfig() *widgets.ScriptConfig {
	return &widgets.ScriptConfig{
		ScriptClassName:      "promptTmpl",
		ScriptClassParamFunc: widgets.FunPromptParamTransfer,
	}
}

func (p PromptTemplate) toScript() *script.Generator {
	return nil
}

func PromptsToDynamicWidgetDesc(promptTemplates []*PromptTemplate) []*DynamicWidgetDesc {
	res := make([]*DynamicWidgetDesc, 0)
	for _, p := range promptTemplates {
		res = append(res, p.ToDynamicWidgetDesc())
	}
	return res
}

func (p PromptTemplate) ToDynamicWidgetDesc() *DynamicWidgetDesc {
	return &DynamicWidgetDesc{
		Type: DynamicWidgetTypePrompt.Desc,
		Name: p.Name,
		Key:  fmt.Sprintf("%v", p.ID),
	}
}
