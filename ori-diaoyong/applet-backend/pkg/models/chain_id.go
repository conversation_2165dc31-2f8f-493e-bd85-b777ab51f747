package models

import (
	"fmt"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

// GenSubChainNodeID 同一个子链拖入画布中的时候，子链中算子的ID是一样的
// 为了保证ID的唯一性，需要拼接算子本身的ID
// 拼接规则为 subchain_${子链所属算子ID}_{子链中算子的原始ID}
func GenSubChainNodeID(parentID string, subID string) string {
	return fmt.Sprintf("subchain_%s_%s", parentID, subID)
}

func GetOriIDFromSubID(ID string) (string, error) {
	splits := strings.Split(ID, "_")
	if len(splits) != 3 {
		return "", stderr.Internal.Error("invalid id format :%v", ID)
	}
	return splits[2], nil
}

func GetParentIDFromSubID(ID string) (string, error) {
	splits := strings.Split(ID, "_")
	if len(splits) != 3 {
		return "", stderr.Internal.Error("invalid id format :%v", ID)
	}
	return splits[1], nil
}

// GenSubChainParamID 子链转换成一个算子的时候，子链中多个算子的参数会合并成一个算子的参数
// 为了保证生成的算子的参数不重复，需要拼接上原算子的ID
// case : Node1 - 【TextInput】, Node2 - 【TextInput】. 合并为 Node3 - 【Node1##TextInput,Node2##TextInput】
func GenSubChainParamID(oriNodeID string, oriParamID string) string {
	return fmt.Sprintf("%s##%s", oriNodeID, oriParamID)
}

// GetNodeIDFromEdgeParam edge中的param的格式为 nodeID@@paramID
func GetNodeIDFromEdgeParam(edgeParamID string) (string, error) {
	splits := strings.Split(edgeParamID, "@@")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid edge param :%v", edgeParamID)
	}
	return splits[0], nil
}

// GetParamIDFromEdgeParam edge中的param的格式为 nodeID@@paramID
func GetParamIDFromEdgeParam(edgeParamID string) (string, error) {
	splits := strings.Split(edgeParamID, "@@")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid edge param :%v", edgeParamID)
	}
	return splits[1], nil
}

// GetNodeIDFromChainRunParam 应用链运行时param的格式为 nodeID##paramID
func GetNodeIDFromChainRunParam(edgeParamID string) (string, error) {
	splits := strings.Split(edgeParamID, "##")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid edge param :%v", edgeParamID)
	}
	return splits[0], nil
}

// GetParamIDFromChainRunParam  应用链运行时param的格式为 nodeID##paramID
func GetParamIDFromChainRunParam(edgeParamID string) (string, error) {
	splits := strings.Split(edgeParamID, "##")
	if len(splits) != 2 {
		return "", stderr.Internal.Error("invalid edge param :%v", edgeParamID)
	}
	return splits[1], nil
}
