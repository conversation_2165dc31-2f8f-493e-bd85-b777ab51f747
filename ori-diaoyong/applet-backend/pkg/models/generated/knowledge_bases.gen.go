// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameKnowledgeBasis = "knowledge_bases"

// KnowledgeBasis mapped from table <knowledge_bases>
type KnowledgeBasis struct {
	ID                 string    `gorm:"column:id;primaryKey" json:"id"`
	Name               string    `gorm:"column:name" json:"name"`
	Description        string    `gorm:"column:description" json:"description"`
	ContentType        int32     `gorm:"column:content_type" json:"content_type"`
	SourceType         int32     `gorm:"column:source_type" json:"source_type"`
	RegistryType       int32     `gorm:"column:registry_type" json:"registry_type"`
	Icon               string    `gorm:"column:icon" json:"icon"`
	ConnectionRegistry string    `gorm:"column:connection_registry" json:"connection_registry"`
	TkhRegistry        string    `gorm:"column:tkh_registry" json:"tkh_registry"`
	Creator            string    `gorm:"column:creator" json:"creator"`
	CreateTimeMills    time.Time `gorm:"column:create_time_mills;default:CURRENT_TIMESTAMP" json:"create_time_mills"`
	UpdateTimeMills    time.Time `gorm:"column:update_time_mills;default:CURRENT_TIMESTAMP" json:"update_time_mills"`
	DisabledDocs       string    `gorm:"column:disabled_docs" json:"disabled_docs"`
	ProjectID          string    `gorm:"column:project_id" json:"project_id"`
	IsVisible          bool      `gorm:"column:is_visible" json:"is_visible"`
	IsRetrievable      bool      `gorm:"column:is_retrievable" json:"is_retrievable"`
	CreationType       int32     `gorm:"column:creation_type" json:"creation_type"`
}

// TableName KnowledgeBasis's table name
func (*KnowledgeBasis) TableName() string {
	return TableNameKnowledgeBasis
}
