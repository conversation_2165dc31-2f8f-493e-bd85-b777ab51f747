// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameArticle = "articles"

// Article mapped from table <articles>
type Article struct {
	ID          string    `gorm:"column:id;primaryKey;comment:文章ID" json:"id"`                                                                // 文章ID
	ProjectID   string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                                  // 项目ID
	Name        string    `gorm:"column:name;not null;comment:文章名称" json:"name"`                                                              // 文章名称
	Abstract    string    `gorm:"column:abstract;not null;comment:文章摘要" json:"abstract"`                                                      // 文章摘要
	Content     string    `gorm:"column:content;not null;comment:内容" json:"content"`                                                          // 内容
	LabelInfo   string    `gorm:"column:label_info;not null;default:{};comment:标签，map结构" json:"label_info"`                                   // 标签，map结构
	Creator     string    `gorm:"column:creator;not null;comment:创建者" json:"creator"`                                                         // 创建者
	Status      string    `gorm:"column:status;not null;default:Publishing;comment:文章状态,Publishing:发布中-待审核,Published:已发布-审核完毕" json:"status"` // 文章状态,Publishing:发布中-待审核,Published:已发布-审核完毕
	IsSelected  int32     `gorm:"column:is_selected;not null;default:1;comment:是否被选中为首页精选，0未知，1未选中，2选中为首页精选" json:"is_selected"`              // 是否被选中为首页精选，0未知，1未选中，2选中为首页精选
	MetricsInfo string    `gorm:"column:metrics_info;not null;default:{};comment:打点信息，访问次数等" json:"metrics_info"`                             // 打点信息，访问次数等
	CreatedTime time.Time `gorm:"column:created_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"`                    // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"`                    // 更新时间
}

// TableName Article's table name
func (*Article) TableName() string {
	return TableNameArticle
}
