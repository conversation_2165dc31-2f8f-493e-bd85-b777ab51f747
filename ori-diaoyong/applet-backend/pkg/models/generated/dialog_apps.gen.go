// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameDialogApp = "dialog_apps"

// DialogApp mapped from table <dialog_apps>
type DialogApp struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	AppID       string    `gorm:"column:app_id" json:"app_id"`
	AppName     string    `gorm:"column:app_name" json:"app_name"`
	AppImage    string    `gorm:"column:app_image" json:"app_image"`
	ProjectID   string    `gorm:"column:project_id" json:"project_id"`
	User        string    `gorm:"column:user" json:"user"`
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	CreatedType string    `gorm:"column:created_type;comment:应用类型" json:"created_type"`                                    // 应用类型
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName DialogApp's table name
func (*DialogApp) TableName() string {
	return TableNameDialogApp
}
