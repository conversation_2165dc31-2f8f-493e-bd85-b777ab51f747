// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

const TableNameSafetyConfig = "safety_configs"

// SafetyConfig mapped from table <safety_configs>
type SafetyConfig struct {
	ID               string `gorm:"column:id;primaryKey" json:"id"`
	ProjectID        string `gorm:"column:project_id" json:"project_id"`
	InputGuardrails  string `gorm:"column:input_guardrails" json:"input_guardrails"`
	OutputGuardrails string `gorm:"column:output_guardrails" json:"output_guardrails"`
}

// TableName SafetyConfig's table name
func (*SafetyConfig) TableName() string {
	return TableNameSafetyConfig
}
