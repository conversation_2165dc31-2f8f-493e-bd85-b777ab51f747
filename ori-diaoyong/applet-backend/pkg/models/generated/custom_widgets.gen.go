// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameCustomWidget = "custom_widgets"

// CustomWidget mapped from table <custom_widgets>
type CustomWidget struct {
	ID           string    `gorm:"column:id;primaryKey;comment:自定义算子唯一ID" json:"id"`                                        // 自定义算子唯一ID
	Name         string    `gorm:"column:name;not null;comment:算子名称" json:"name"`                                           // 算子名称
	Desc         string    `gorm:"column:desc;comment:算子描述信息" json:"desc"`                                                  // 算子描述信息
	Status       string    `gorm:"column:status;not null;default:Stopped;comment:算子运行状态" json:"status"`                     // 算子运行状态
	Port         int32     `gorm:"column:port;not null;comment:端口" json:"port"`                                             // 端口
	ParamInfo    string    `gorm:"column:param_info;not null;comment:参数列表" json:"param_info"`                               // 参数列表
	ImageInfo    string    `gorm:"column:image_info;comment:自定义算子镜像信息，map结构" json:"image_info"`                             // 自定义算子镜像信息，map结构
	LabelInfo    string    `gorm:"column:label_info;comment:标签，map结构" json:"label_info"`                                    // 标签，map结构
	ResourceInfo string    `gorm:"column:resource_info;comment:资源配置信息，map结构" json:"resource_info"`                          // 资源配置信息，map结构
	DeployInfo   string    `gorm:"column:deploy_info;not null;comment:部署信息" json:"deploy_info"`                             // 部署信息
	Creator      string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                      // 创建人
	ProjectID    string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                               // 项目ID
	CreatedTime  time.Time `gorm:"column:created_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"` // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName CustomWidget's table name
func (*CustomWidget) TableName() string {
	return TableNameCustomWidget
}
