// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameDialogMessage = "dialog_messages"

// DialogMessage mapped from table <dialog_messages>
type DialogMessage struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ChatID      string    `gorm:"column:chat_id;not null" json:"chat_id"`
	Content     string    `gorm:"column:content" json:"content"`
	Role        string    `gorm:"column:role;comment:记录的类型:(question, answer)" json:"role"`                                // 记录的类型:(question, answer)
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName DialogMessage's table name
func (*DialogMessage) TableName() string {
	return TableNameDialogMessage
}
