package models

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const (
	ApiPrefix = "https://*************:30443/gateway/applet"
	RunApi    = "api/v1/applet/chains:run"
)

func ConvertChainPO2ChainDO(model *generated.AppletChain, debugState int32) (*AppletChainDO, error) {
	baseModel, err := ConvertChainPO2BaseDO(model, debugState)
	if err != nil {
		return nil, err
	}
	if baseModel == nil {
		return nil, errors.Errorf("chain no base info")
	}
	chainDO := &AppletChainDO{
		Base: *baseModel,
	}
	chainDetail, err := ConvertChanDetailStr2DO(model.Chain)
	if err != nil {
		return nil, stderr.Wrap(err, "convert chain :%v err", model.ID)
	}
	if chainDetail != nil {
		chainDO.ChainDetail = chainDetail
	}
	return chainDO, nil
}

func ChainState2Int(t ChainDeployState) int32 {
	return int32(t)
}

func ChainInt2State(t int32) ChainDeployState {
	if t == int32(ChainStateOnline) {
		return ChainStateOnline
	}
	if t == int32(ChainStateProgressing) {
		return ChainStateProgressing
	}
	return ChainStateOffline
}

func ChainType2Int(t ChainType) int32 {
	return int32(t)
}

func ChainInt2Type(t int32) ChainType {
	switch t {
	case int32(ChainTypeTemplate):
		return ChainTypeTemplate
	case int32(ChainTypeUserCreate):
		return ChainTypeUserCreate
	default:
		return ChainTypeUnKnow
	}
}

func ConvertChainDebugStatePO2DO(state int32) (*ChainDebugState, error) {
	switch state {
	case ChainDebugStateInit.Code:
		return ChainDebugStateInit, nil
	case ChainDebugStateRunning.Code:
		return ChainDebugStateRunning, nil
	case ChainDebugStateFailed.Code:
		return ChainDebugStateFailed, nil
	case ChainDebugStateSuccess.Code:
		return ChainDebugStateSuccess, nil
	case ChainDebugStateCanceled.Code:
		return ChainDebugStateCanceled, nil
	}
	return nil, stderr.Error("invalid chain state :%v", state)
}

func ConvertChainPO2BaseDO(model *generated.AppletChain, debugState int32) (*AppletChainBaseDO, error) {
	label, err := ConvertLabelsStr2DO(model.Label)
	if err != nil {
		return nil, err
	}
	cstTL, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return nil, stderr.Wrap(err, "set timezone err")
	}
	var containsSubChain bool
	if model.ContainsSubChain == ChainContainsSubChain {
		containsSubChain = true
	}
	chainLastDebugState, err := ConvertChainDebugStatePO2DO(debugState)
	if err != nil {
		return nil, err
	}

	experimentInfo := new(ExperimentInfo)
	if err = helper.String2Struct(model.ExamplesInfo, &experimentInfo.ExamplesInfo); err != nil {
		return nil, err
	}
	experimentInfo.LabelInfo = label
	experimentInfo.Name = model.Name
	// 兼容老版本examples
	for _, str := range experimentInfo.HistoryExamples {
		experimentInfo.MultimodalExamples = append(experimentInfo.MultimodalExamples, &MultimodalExample{Text: str})
	}
	experimentInfo.HistoryExamples = nil
	if experimentInfo.PermissionCfg == nil {
		experimentInfo.PermissionAction = string(cas.Act_All)
		experimentInfo.PermissionCfg = &common.PermissionCfg{
			PermissionMode: string(cas.PermissionMode_Public),
			PublicType:     string(cas.PublicType_All),
		}
	}
	assistantInfo := new(AssistantInfo)
	if err = helper.String2Struct(model.AssistantInfo, assistantInfo); err != nil {
		return nil, err
	}

	sourceInfo := &ChainSourceInfo{}
	if err = helper.String2Struct(model.SourceInfo, sourceInfo); err != nil {
		return nil, stderr.Wrap(err, "source info :%v string to struct error", model.SourceInfo)
	}
	metricsInfo := ChainMetricsInfo{}
	if err = helper.String2Struct(model.MetricsInfo, &metricsInfo); err != nil {
		return nil, stderr.Wrap(err, "metrics info :%v string to struct error", model.SourceInfo)
	}
	spaceInfo := pb.SpaceInfo{IndustryLabels: make(map[string]bool), IsSelected: make(map[string]bool)}
	if err = helper.String2Struct(model.SpaceInfo, &spaceInfo); err != nil {
		return nil, stderr.Wrap(err, "spaceInfo :%v string to struct error", model.SourceInfo)
	}
	permissionCfg := new(common.PermissionCfg)
	if err = helper.String2Struct(model.PermissionCfg, permissionCfg); err != nil {
		return nil, stderr.Wrap(err, "permission cfg :%v string to struct error", model.SourceInfo)
	}

	if permissionCfg.PermissionMode == "" {
		permissionCfg.PermissionMode = string(cas.PermissionMode_Public)
	}
	if permissionCfg.PublicType == "" {
		permissionCfg.PublicType = string(cas.PublicType_All)
	}
	baseDO := &AppletChainBaseDO{
		ID:               model.ID,
		Name:             model.Name,
		Creator:          model.Creator,
		Desc:             model.Desc,
		Labels:           label,
		ContainsSubChain: containsSubChain,
		ProjectID:        model.ProjectID,
		UpdateTime:       model.ChainUpdatedTime.In(cstTL).UnixMilli(),
		CreateTime:       model.CreateTime.In(cstTL).UnixMilli(),
		LastDebugState:   chainLastDebugState,
		AssetType:        pb.AssetType_name[model.AssetType],
		CreatedType:      model.CreatedType,
		ExperimentInfo:   experimentInfo,
		AssistantInfo:    assistantInfo,
		SourceInfo:       sourceInfo,
		MetricsInfo:      &metricsInfo,
		SpaceInfo:        &spaceInfo,
		UsageType:        model.UsageType,
		PermissionAction: model.PermissionAction,
		PermissionCfg:    permissionCfg,
	}
	return baseDO, nil
}

func ConvertChanDetailStr2DO(chainDetail string) (*widgets.Chain, error) {
	if chainDetail == "" {
		return nil, nil
	}
	var chain *widgets.Chain
	if err := json.Unmarshal([]byte(chainDetail), &chain); err != nil {
		return chain, err
	}
	return chain, nil
}

func ConvertChanDetailDO2Str(model *widgets.Chain) (string, error) {
	if model == nil {
		return "", nil
	}
	bytes, err := json.Marshal(model)
	if err != nil {
		return "", err
	}
	return string(bytes), err
}

func ConvertStr2Time(timestampStr string) (*time.Time, error) {
	if timestampStr == "" {
		return nil, nil
	}

	// 将字符串转换为 int64
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid timestamp: %s", timestampStr)
	}

	//转换为 time.Time
	parsedTime := time.UnixMilli(timestamp)
	return &parsedTime, nil
}
