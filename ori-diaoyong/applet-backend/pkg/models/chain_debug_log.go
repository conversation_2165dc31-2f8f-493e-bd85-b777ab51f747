package models

import (
	"transwarp.io/applied-ai/aiot/vision-std/database"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

const (
	TableNameChainDebugLog = "chain_debug_logs"
)

// ChainDebugLog uniqueIndex:idx_chat_node_round
type ChainDebugLog struct {
	ChatID       string              `json:"chat_id" gorm:"type:VARCHAR(100); not null; uniqueIndex:idx_chat_node_round;comment:本次调试id,又称为run_id" description:"comment:本次调试id,又称为run_id"`
	NodeID       uuNodeID            `json:"node_id" gorm:"type:VARCHAR(191); not null; uniqueIndex:idx_chat_node_round;comment:对链展开后的节点进行唯一标识" description:"格式为算子节点id或子链节点id_算子节点id"`
	Round        int                 `json:"round" gorm:"type:INTEGER; not null; uniqueIndex:idx_chat_node_round;comment:本次调试算子具体轮次" description:"本次调试算子具体轮次"`
	DebugMessage *debug.DebugMessage `json:"debug_message" gorm:"type:MEDIUMTEXT; serializer:json;not null; comment:节点调试详情" description:"节点调试详情"`
	ChainID      string              `json:"chain_id,omitempty" gorm:"type:VARCHAR(100);comment:关联的应用链id" description:"关联的应用链id"`
	ProjectID    string              `json:"project_id,omitempty" gorm:"type:VARCHAR(100); not null;  comment:项目空间id" description:"项目空间id"`
	database.TimeMixin
}

func (*ChainDebugLog) TableName() string {
	return TableNameChainDebugLog
}

type uuNodeID string

func GetUUNodeID(subChainID, nodeID string) uuNodeID {
	if subChainID == "" {
		return uuNodeID(nodeID)
	}
	return uuNodeID(subChainID + "_" + nodeID)
}

func CastUUNodeID(uuNodeId string) uuNodeID {
	return uuNodeID(uuNodeId)
}
