package health

type ChainHealthResponse struct {
	Success bool         `json:"success"`
	Data    *ChainHealth `json:"data"`
	Error   string       `json:"error"` // sucess为false时，error为错误信息
}

type ChainHealth struct {
	ServiceHealth
	Timestamp    int64           `json:"timestamp"`
	Dependencies []ServiceHealth `json:"dependencies"`
}

type ServiceHealth struct {
	ID      string `json:"-"` // 工具的唯一标识，不同算子可能依赖同一个tool工具，ID用于应用链dependencies列表去重
	Name    string `json:"name"`
	Healthy bool   `json:"healthy"`
	Detail  string `json:"detail"`
}
