package models

import "transwarp.io/aip/llmops-common/pb"

type PromptToOptimize struct {
	InputPrompt string `json:"input_prompt"`
}

type OptimizedPrompt struct {
	OptimizedPrompt string `json:"optimized_prompt"`
}

type LLMBasicConfig struct {
	ProjectID      string            `json:"project_id" description:"项目id"`
	ModelsForAgent AllModelsForAgent `json:"models_for_agent" description:"Agent对应模型"`
}

type AllModelsForAgent struct {
	LLMModelSvc        *pb.ModelService `json:"llm_model_svc" description:"大模型配置信息"`
	EmbeddingSvc       *pb.ModelService `json:"embedding_model" description:"嵌入模型配置信息"`
	ImageGenSvc        *pb.ModelService `json:"image_gen_model" description:"图像生成模型配置信息"`
	RerankSvc          *pb.ModelService `json:"rerank_model" description:"rerank模型配置信息"`
	ImageUnderstandSvc *pb.ModelService `json:"image_understand_model" description:"图像理解模型配置"`
	OcrSvc             *pb.ModelService `json:"ocr_svc" description:"高精度解析模型配置"`
}

type AutoCreatedAgentParam struct {
	Purpose string `json:"destination" description:"目的"`
}

type ImageGenParam struct {
	Prompt string `json:"prompt" description:"提示"`
}

type ImageGenResult struct {
	ImageUrl string `json:"image" description:"生成的图片的url"`
}
