package agent_definition

import (
	"transwarp.io/aip/llmops-common/pb"
)

type ServiceTypeEnum string

const (
	ServiceTypeEnumApplet ServiceTypeEnum = "applet"
	ServiceTypeEnumModel  ServiceTypeEnum = "model"
)
type AgentMode string

const (
	AgentModeReAct           AgentMode = "react"
	AgentModeFunctionCalling AgentMode = "function_calling"
)

// LLMAgentConfig 用于给到LLM Agent算子的完整提示、模型、工具等相关信息的描述
type LLMAgentConfig struct {
	Prompt         string                        `json:"prompt" description:"提示词"`
	AgentMode      AgentMode                     `json:"agent_mode" description:"智能体模式"`
	LLMModelSvc    *pb.ModelService              `json:"llm_model_svc" description:"大模型配置信息,前端传入llm_model_svc_str字段"`
	RerankModelSvc *pb.ModelService              `json:"rerank_model_svc" description:"智能体溯源功能使用"`
	APICollections []*APIToolCollectionDescriber `json:"api_collections" description:"API类工具集"`
	KnowledgeBases AssistantKnowledgeBases       `json:"knowledge_bases" description:"知识库类工具"`
	SystemServices []SystemService               `json:"system_services" description:"系统服务类工具集"`
}

type SystemService struct {
	ServiceType   ServiceTypeEnum             `json:"service_type" description:"服务类型，限定为applet/model"`
	AppletService *AppletServiceToolDescriber `json:"applet_service" description:"应用服务类工具"`
	ModelService  *ModelToolDescriber         `json:"model_service" description:"模型服务类工具"`
}
type KnowledgeBases struct {
	MustUse           bool                 `json:"must_use,omitempty" description:"必须调用知识库,和前端交互"`
	RerankParams      *pb.RerankParams     `json:"rerank_params,omitempty" description:"知识库整体rerank配置信息"`
	KnowledgeBaseDesc []*KnowlHubDescriber `json:"knowledge_base_desc" description:"各知识库具体信息"`
}

type AssistantKnowledgeBases struct {
	KnowledgeBases
	StandardScoreThreshold    float32              `json:"standard_score_threshold" description:"标准问答阈值"`
	StandardKnowledgeBaseDesc []*KnowlHubDescriber `json:"standard_knowledge_base_desc" description:"标准问答知识库"`
}

func SetKbsRerankParams(kbs *KnowledgeBases) {
	for _, kb := range kbs.KnowledgeBaseDesc {
		kb.RerankParams = kbs.RerankParams
	}
}
