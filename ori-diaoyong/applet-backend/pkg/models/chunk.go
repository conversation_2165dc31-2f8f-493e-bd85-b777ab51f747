package models

import "transwarp.io/aip/llmops-common/pb"

const (
	TableNameChunks = "chunks"

	IndexFlagNot      int32 = 0
	IndexFlagVector   int32 = 1
	IndexFlagFulltext int32 = 2
	IndexFlagALL      int32 = 3
)

type Chunk struct {
	Id string `json:"id" gorm:"column:id;primaryKey;type:varchar(191)"`

	Content string `json:"indexed_content" gorm:"column:content" description:"原文内容"`

	ElementIDs []string `json:"element_ids" gorm:"column:element_ids;serializer:json" description:"chunk对应的文档元素id列表,仅原文类型的chunk需要该属性"`

	SourceType pb.ChunkSourceType `json:"source_type" gorm:"column:source_type" description:"chunk来源类型"`

	ContentType pb.OriginalContentType `json:"content_type" gorm:"column:content_type" description:"chunk原文内容类型"`

	DisableVectorIndexing bool `json:"disable_vector_indexing" gorm:"column:disable_vector_indexing" description:"是否禁用向量化索引"`

	DisableFullTextIndexing bool `json:"disable_full_text_indexing" gorm:"column:disable_full_text_indexing" description:"是否禁用全文索引"`

	DocumentId string `json:"document_id" gorm:"column:document_id;index:idx_document_order,priority:1;type:varchar(191);index:idx_document_flag,priority:1" description:"所属文档id"`

	KnowledgeBaseId string `json:"knowledge_base_id" gorm:"column:knowledge_base_id;type:varchar(191)" description:"所属知识库id"`

	AugmentedChunks []*pb.AugmentedChunk `json:"augmented_chunks" gorm:"column:augmented_chunks;serializer:json" description:"相关的增强chunks"`

	// 用于维护chunks在原文档中的顺序
	OrderId int `json:"-" gorm:"column:order_id;unique;autoIncrement;index:idx_document_order,priority:2,sort:asc"`

	Edited bool `json:"edited" gorm:"column:edited" description:"是否被修改编辑过"`

	RetrieveEnabled *bool `json:"retrieve_enabled" gorm:"column:retrieve_enable;default:1" description:"是否可召回"`

	QaPairs []*pb.QaPair `json:"qa_pairs" gorm:"column:qa_pairs;serializer:json" description:"问答对列表"`

	IndexFlag int32 `json:"index_flag" gorm:"column:index_flag;index:idx_document_flag,priority:2;default:0" description:"入库标记，0: 未入库, 1: 向量入库, 2: 全文入库, 3: 全部入库"`
}

func (*Chunk) TableName() string {
	return TableNameChunks
}

func (c *Chunk) ToPb() *pb.Chunk {
	return &pb.Chunk{
		Id:                      c.Id,
		Content:                 c.Content,
		ElementIds:              c.ElementIDs,
		SourceType:              c.SourceType,
		ContentType:             c.ContentType,
		DisableVectorIndexing:   c.DisableVectorIndexing,
		DisableFullTextIndexing: c.DisableFullTextIndexing,
		AugmentedChunks:         c.AugmentedChunks,
		Edited:                  c.Edited,
		RetrieveEnabled:         *c.RetrieveEnabled,
		QaPairs:                 c.QaPairs,
		IndexFlag:               c.IndexFlag,
	}
}

func FromChunkPb(chunk *pb.Chunk, kbId, docId string) *Chunk {
	return &Chunk{
		Id:                      chunk.Id,
		Content:                 chunk.Content,
		ElementIDs:              chunk.ElementIds,
		SourceType:              chunk.SourceType,
		ContentType:             chunk.ContentType,
		DisableVectorIndexing:   chunk.DisableVectorIndexing,
		DisableFullTextIndexing: chunk.DisableFullTextIndexing,
		AugmentedChunks:         chunk.AugmentedChunks,
		Edited:                  chunk.Edited,
		RetrieveEnabled:         &chunk.RetrieveEnabled,
		KnowledgeBaseId:         kbId,
		DocumentId:              docId,
		QaPairs:                 chunk.QaPairs,
		IndexFlag:               chunk.IndexFlag,
	}
}

func FromChunkPbs(chunk []*pb.Chunk, kbId, docId string) []*Chunk {
	ret := make([]*Chunk, 0, len(chunk))
	for _, c := range chunk {
		ret = append(ret, FromChunkPb(c, kbId, docId))
	}
	return ret
}

func ToChunkPbs(chunks []*Chunk) []*pb.Chunk {
	ret := make([]*pb.Chunk, 0, len(chunks))
	for _, c := range chunks {
		ret = append(ret, c.ToPb())
	}
	return ret
}

type ChunkForIndexing struct {
	// chunk id
	Id string
	// 原文chunk id， 用于确认增强chunk所属的原文；原文类型chunk的OriId等于自己的Id
	OriId string
	// 文档 id
	DocId string
	// 知识库 id
	KBId string
	// 用于构建索引的文本
	Text string
	// 是否禁用向量化索引
	DisableVectorIndexing bool
	// 是否禁用全文索引
	DisableFullTextIndexing bool
	// 检索的优先级
	Priority int
	// 短id
	ShortId string
	// 类型
	Type string
	// 额外信息
	Extra string
}

func (ci *ChunkForIndexing) FromChunk(c *Chunk) *ChunkForIndexing {
	ci.Id = c.Id
	ci.OriId = c.Id
	ci.DocId = c.DocumentId
	ci.KBId = c.KnowledgeBaseId
	ci.Text = c.Content
	ci.DisableVectorIndexing = c.DisableVectorIndexing
	ci.DisableFullTextIndexing = c.DisableFullTextIndexing
	ci.Type = c.ContentType.String()
	return ci
}

func (ci *ChunkForIndexing) FromAugChunk(c *Chunk, ac *pb.AugmentedChunk) *ChunkForIndexing {
	ci.Id = ac.Id
	ci.OriId = c.Id
	ci.DocId = c.DocumentId
	ci.KBId = c.KnowledgeBaseId
	ci.Text = ac.Content
	ci.DisableVectorIndexing = ac.DisableVectorIndexing
	ci.DisableFullTextIndexing = ac.DisableFullTextIndexing
	ci.Type = ac.AugmentedType.String()
	return ci
}
