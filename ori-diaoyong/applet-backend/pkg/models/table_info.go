package models

import (
	"encoding/json"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/hippo-go/v1"
)

type TableInfo struct {
	Name          string                  `json:"name"`
	Rows          int                     `json:"rows"`
	Description   *hippo.TableDescription `json:"description"`
	ESDescription string                  `json:"es_description"`
}

func (i *TableInfo) FromPb(data *pb.TableInfo) error {
	i.Name = data.Name
	i.Rows = int(data.Rows)
	desc := new(hippo.TableDescription)
	if err := json.Unmarshal([]byte(data.Description), desc); err != nil {
		return err
	}
	i.Description = desc
	return nil
}
