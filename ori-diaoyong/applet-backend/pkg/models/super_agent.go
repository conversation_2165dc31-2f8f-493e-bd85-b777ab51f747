package models

import (
	"encoding/json"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type AIGenerateChainReq struct {
	Query       string        `json:"query"`
	ChatHistory []ChatHistory `json:"chat_history"`
}

type ChatHistory struct {
	Question string `json:"Q"`
	Answer   string `json:"A"`
}

func GetChatHistoryDesc(history []ChatHistory) (string, error) {
	if len(history) == 0 {
		return "[]", nil
	}
	bytes, err := json.MarshalIndent(history, "", "  ")
	if err != nil {
		return "", err
	}
	return string(bytes), err
}

type AIChainTemplateDesc struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Desc string `json:"description"`
}

type ChainInfo struct {
	ChainDetail *widgets.Chain `json:"chain_detail"`
}

type AIChainDetail struct {
	Nodes []AIChainNode `json:"nodes"`
	Edges []AIChainEdge `json:"edges"`
}

type AIChainNode struct {
	ID       int64                  `json:"id"`
	WidgetID string                 `json:"widget_id"`
	Params   map[string]interface{} `json:"params"`
}

type AIChainEdge struct {
	SourceNodeID int64  `json:"source_node_id"`
	SourceWidget string `json:"source_widget"`
	SourceParam  string `json:"source_param"`
	TargetNodeID int64  `json:"target_node_id"`
	TargetWidget string `json:"target_widget"`
	TargetParam  string `json:"target_param"`
}

func GetAITemplateDesc(temps []AIChainTemplateDesc) (string, error) {
	res := ""
	for _, t := range temps {
		res += "\n"
		bytes, err := json.MarshalIndent(t, "", "  ")
		if err != nil {
			return "", err
		}
		res += string(bytes)
	}
	return res, nil
}
