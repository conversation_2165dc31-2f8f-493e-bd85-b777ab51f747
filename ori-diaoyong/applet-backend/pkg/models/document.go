package models

import (
	"fmt"
	"strings"
	"time"

	"transwarp.io/aip/llmops-common/pb"
)

const (
	TableNameDocuments = "documents"
	// 文档元数据字段常量
	// 文档元数据内置字段常量
	MetaFieldAssetID        = "资产ID"
	MetaFieldAssetName      = "资产名称"
	MetaFieldFileName       = "文件名称"
	MetaFieldFileType       = "文件类型"
	MetaFieldFileSize       = "文件大小"
	MetaFieldFileDepartment = "部门"
	MetaFieldWordCount      = "字数"
	MetaFieldDescription    = "描述"
	MetaFieldSource         = "来源"
	MetaFieldTags           = "标签"
	MetaFieldCatalog        = "编目"
	MetaFieldCreator        = "创建者"
	MetaFieldCreationDate   = "创建日期"
	MetaFieldEnabled        = "启用"
	MetaFieldRelatedFiles   = "关联文件"
	MetaFieldUpdateType     = "更新类型"
)

type Document struct {
	// 文档id
	Id string `json:"id" gorm:"column:id;type:varchar(191);uniqueIndex" description:"文档id"`

	// 文档名称 (Linux 下通常为255, getconf NAME_MAX /mnt/disk1 可得)
	Name string `json:"name" gorm:"column:name;type:varchar(500)" description:"文档名称"`

	// 文档路径(Linux 下通常为4096, getconf PATH_MAX /mnt/disk1 可得)
	FilePath string `json:"file_path" gorm:"column:file_path;type:varchar(8192)" description:"文档路径"`

	// 文档大小
	FileSizeBytes int32 `json:"file_size_bytes" gorm:"column:file_size_bytes" description:"文档大小"`

	// 文档格式
	FileFormat string `json:"file_format" gorm:"column:file_format;type:varchar(200)" description:"文档格式"`

	// 上传时间
	UploadTime time.Time `json:"upload_time" gorm:"column:upload_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" description:"上传时间"`

	// 字符数
	NumChars int32 `json:"num_chars" gorm:"column:num_chars" description:"字符数"`

	// 文件MD5
	FileMD5 string `json:"file_md5" gorm:"column:file_md5" description:"文件MD5"`

	// 知识库ID
	KnowledgeBaseId string `json:"knowledge_base_id" gorm:"column:knowledge_base_id;type:varchar(191)" description:"知识库ID"`

	ProjectId string `gorm:"column:project_id;type:varchar(191)" json:"projectId"`

	TableConfig *pb.TableConfig `json:"table_config" gorm:"column:table_config;serializer:json" description:"表格配置"`

	DocumentFileSource pb.DocumentFileSource `json:"document_file_source" gorm:"column:document_file_source" description:"文件来源"`

	CorpusConfig *pb.CorpusConfig `json:"corpus_config" gorm:"column:corpus_config;serializer:json" description:"表格配置"`

	DocProcessingConfig *pb.DocProcessingConfig `json:"doc_processing_config" gorm:"column:doc_processing_config;serializer:json" description:"文件处理配置"`

	// 2.0 新增
	IndexConfig *pb.IndexConfig `json:"index_config" gorm:"column:index_config;serializer:json" description:"入库索引配置"`

	Stage pb.DocumentTaskStage `json:"stage" gorm:"column:stage" description:"文档入库状态"`

	DbStatus pb.DocumentDbStatus `json:"db_status" gorm:"column:db_status" description:"文档chunk写入状态"`

	ProcessTask *pb.ProcessTask `json:"process_task" gorm:"column:process_task;serializer:json" description:"加工任务相关信息"`

	FileAsset *pb.FileAsset `json:"file_asset" gorm:"column:file_asset;serializer:json" description:"文件资产信息"`

	AssetRel *pb.KnowledgeAssetRel `json:"asset_rel" gorm:"column:asset_rel;serializer:json" description:"表格文件信息"`

	ShortId int64 `json:"short_id" gorm:"column:short_id;autoIncrement;primaryKey" description:"使用int id进行文档的过滤"`

	RetrieveEnabled bool `json:"retrieve_enabled" gorm:"column:retrieve_enabled;default:1" description:"是否可召回"`

	AbstractChunk *pb.Chunk `json:"abstract_chunk" gorm:"column:abstract_chunk;serializer:json" description:"文档摘要的切片"`

	NumChunks int64 `json:"num_chunks" gorm:"column:num_chunks;default:0" description:"切片数量"`

	NumSuccessChunks int64 `json:"num_success_chunks" gorm:"column:num_success_chunks;default:0" description:"成功切片数量"`
}

func (*Document) TableName() string {
	return TableNameDocuments
}

func (doc *Document) ToPb() *pb.Document {
	return &pb.Document{
		DocId:               doc.Id,
		DocName:             doc.Name,
		FilePath:            doc.FilePath,
		FileSizeBytes:       doc.FileSizeBytes,
		FileFormat:          doc.FileFormat,
		UploadTimeMills:     doc.UploadTime.UnixMilli(),
		NumChars:            doc.NumChars,
		FileMd5:             doc.FileMD5,
		KnowledgeBaseId:     doc.KnowledgeBaseId,
		TableConfig:         doc.TableConfig,
		DocumentFileSource:  doc.DocumentFileSource,
		CorpusConfig:        doc.CorpusConfig,
		DocProcessingConfig: doc.DocProcessingConfig,
		IndexConfig:         doc.IndexConfig,
		Stage:               doc.Stage,
		DbStatus:            doc.DbStatus,
		ProcessTask:         doc.ProcessTask,
		FileAsset:           doc.FileAsset,
		ShortId:             doc.ShortId,
		RetrieveEnabled:     doc.RetrieveEnabled,
		AbstractChunk:       doc.AbstractChunk,
		AssetRel:            doc.AssetRel,
		NumChunks:           doc.NumChunks,
		NumSuccessChunks:    doc.NumSuccessChunks,
	}
}

func FromDocumentPb(protoDoc *pb.Document) *Document {
	return &Document{
		Id:                  protoDoc.DocId,
		Name:                protoDoc.DocName,
		FilePath:            protoDoc.FilePath,
		FileSizeBytes:       protoDoc.FileSizeBytes,
		FileFormat:          protoDoc.FileFormat,
		UploadTime:          time.UnixMilli(protoDoc.UploadTimeMills),
		NumChars:            protoDoc.NumChars,
		FileMD5:             protoDoc.FileMd5,
		KnowledgeBaseId:     protoDoc.KnowledgeBaseId,
		TableConfig:         protoDoc.TableConfig,
		DocumentFileSource:  protoDoc.DocumentFileSource,
		CorpusConfig:        protoDoc.CorpusConfig,
		DocProcessingConfig: protoDoc.DocProcessingConfig,
		IndexConfig:         protoDoc.IndexConfig,
		Stage:               protoDoc.Stage,
		DbStatus:            protoDoc.DbStatus,
		ProcessTask:         protoDoc.ProcessTask,
		FileAsset:           protoDoc.FileAsset,
		ShortId:             protoDoc.ShortId,
		RetrieveEnabled:     protoDoc.RetrieveEnabled,
		AbstractChunk:       protoDoc.AbstractChunk,
		AssetRel:            protoDoc.AssetRel,
		NumChunks:           protoDoc.NumChunks,
		NumSuccessChunks:    protoDoc.NumSuccessChunks,
	}
}

type DocumentSubmitInfo struct {
	FilePath            string
	DocumentFileSource  pb.DocumentFileSource
	TableConfig         *pb.TableConfig
	CorpusConfig        *pb.CorpusConfig
	DocProcessingConfig *pb.DocProcessingConfig
}

func (doc *Document) GetMeta(column string, indexFlag bool) any {
	if doc == nil {
		return ""
	}
	// 需要结合indexConfig返回
	if indexFlag {
		if doc.IndexConfig != nil || len(doc.IndexConfig.ScalarIndexConfigs) == 0 {
			// 如果没开启标量索引则忽略此字段
			return nil
		}
		for _, ic := range doc.IndexConfig.ScalarIndexConfigs {
			if ic.OriColumn == MetaFieldTags {
				if !ic.Enabled {
					if strings.HasPrefix(column, "label.") {
						return nil
					}
				}
			} else if ic.OriColumn == column {
				if !ic.Enabled {
					return nil
				}
			}
		}
	}
	// 判断是不是label.开头
	if strings.HasPrefix(column, "label.") {
		// 如果是label.开头，则返回label.字段
		return doc.FileAsset.Labels[strings.TrimPrefix(column, "label.")]
	}
	switch column {
	case MetaFieldAssetID:
		return doc.FileAsset.Id
	case MetaFieldAssetName:
		return doc.FileAsset.Name
	case MetaFieldFileName:
		return doc.FileAsset.FileName
	case MetaFieldFileType:
		return doc.FileAsset.FileType
	case MetaFieldFileDepartment:
		return doc.FileAsset.Department
	case MetaFieldFileSize:
		return doc.FileSizeBytes
	case MetaFieldWordCount:
		return doc.NumChars
	case MetaFieldDescription:
		return doc.FileAsset.Description
	case MetaFieldSource:
		return doc.FileAsset.Sources
	case MetaFieldTags:
		return doc.FileAsset.Labels
	case MetaFieldCatalog:
		return doc.FileAsset.Catalog
	case MetaFieldCreator:
		return doc.FileAsset.Creator
	case MetaFieldCreationDate:
		return doc.FileAsset.CreateAt
	case MetaFieldEnabled:
		return doc.RetrieveEnabled
	case MetaFieldRelatedFiles:
		return nil
	case MetaFieldUpdateType:
		return nil
	default:
		return nil
	}
}

// GetMetaString 将GetMeta的返回值转为string
func (doc *Document) GetMetaString(column string, indexFlag bool) string {
	value := doc.GetMeta(column, indexFlag)
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case float64:
		return fmt.Sprintf("%f", v)
	case bool:
		return fmt.Sprintf("%v", v)
	case []string:
		return strings.Join(v, ",")
	case time.Time:
		return v.Format(time.RFC3339)
	case map[string]string:
		var parts []string
		for key, val := range v {
			parts = append(parts, fmt.Sprintf("%s:%s", key, val))
		}
		return strings.Join(parts, ",")
	default:
		return fmt.Sprintf("%v", v)
	}
}

// docuemnt task file_asset rel

const (
	TableNameDocumentRel = "doc_task_asset_rel" // 包含文档、任务、资产关系及标签
)

type DocTaskAssetRel struct {
	TaskID     string `json:"task_id" gorm:"column:task_id;type:varchar(191);index:idx_task_asset" description:"加工任务id"`
	AssetID    string `json:"asset_id" gorm:"column:asset_id;type:varchar(191);index:idx_task_asset" description:"资产ID"`
	KbID       string `json:"kb_id" gorm:"column:kb_id;type:varchar(191)" description:"知识库ID"`
	DocShortID int64  `json:"doc_short_id" gorm:"column:doc_short_id" description:"知识库主键"`

	Document *Document `json:"document" gorm:"foreignKey:DocShortID;references:ShortId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

func (*DocTaskAssetRel) TableName() string {
	return TableNameDocumentRel
}
