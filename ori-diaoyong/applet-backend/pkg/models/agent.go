package models

import (
	"fmt"
	"path/filepath"
	"time"

	"github.com/robfig/cron/v3"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
)

const (
	PublishedStatus = "Published"
	CanceledStatus  = "Canceled"
)

type ExperimentInfo struct {
	ExamplesInfo // 对应数据库的examples_info字段。
	// 共用applet-chain的name和labelInfo
	Name      string      `json:"name"  description:"名称"`
	LabelInfo LabelGroups `json:"label_info"   description:"标签信息"`
	// 外部应用的引入信息
	RegisterType RegisterType `json:"register_type" description:"外部引用的引入方式[src_url|iframe_code],"`
	SrcUrl       string       `json:"src_url"  description:"当为外部应用时, 该字段存放外部应用的完整跳转链接"`
	IFrameCode   string       `json:"iframe_code"  description:"通过iframe方式引入外部应用的具体代码"`
}

type MultimodalExample struct {
	Text string `json:"text,omitempty"  description:"文本内容"`
	File string `json:"file,omitempty"  description:"文件地址"`
}

func (example *MultimodalExample) Cvt2SvcCallReq() (map[string]any, error) {
	ret := make(map[string]any)
	if example.Text != "" {
		ret[widgets.ParamIDTextInput] = example.Text
	}
	if example.File != "" {
		ret[widgets.ParamIDFileInput] = []*engine.SFSFile{{
			Name: filepath.Base(example.File),
			Uid:  example.File,
			Url:  example.File,
		}}
	}
	return ret, nil
}

type ExamplesInfo struct {
	ImageUrl           string                `json:"image_url"  description:"封面图片地址"`
	Introduction       string                `json:"introduction"  description:"开场白介绍"`
	HistoryExamples    []string              `json:"examples,omitempty"  description:"兼容历史数据"`
	MultimodalExamples []*MultimodalExample  `json:"multimodal_examples"  description:"常用问题示例"`
	Members            stdsrv.Members        `json:"members"  description:"可见成员列表"`
	Triggers           []*Trigger            `json:"triggers" description:"触发器配置"`
	PermissionAction   string                `json:"permission_action,omitempty" yaml:"permission_action"`
	PermissionCfg      *common.PermissionCfg `json:"permission_cfg,omitempty" yaml:"permission_cfg"`
}

// QueryInfo 为 *triton.LLMChatReq 简化版， 字段名保持一致
type QueryInfo struct {
	Query   string           `json:"query"  description:"当前询问的问题"`
	File    string           `json:"file"  description:"当前对话所传入的文件地址"`
	History []*triton.QAItem `json:"history"  description:"历史问答对"`
}

type AssistantDebugReq struct {
	Assistant *AppletAssistant `json:"assistant" description:"待调试应用助手的完整定义"`
	QueryInfo *QueryInfo       `json:"query_info"  description:"本次对话输入信息"`
}

// AppExperimentService 应用中心-服务列表信息及其关联的体验设置
type AppExperimentService struct {
	ID             string          `json:"id" description:"应用id"`
	ProjectId      string          `json:"project_id" description:"项目id"`
	ExperimentInfo *ExperimentInfo `json:"experiment_info" description:"应用体验信息,封面图片、示例问题等"`
	AppServiceInfo *AppServiceInfo `json:"service_info" description:"关系服务的健康状态，以及一些必要信息"`
	ChainDetail    *widgets.Chain  `json:"chain_detail" description:"算子编排信息"`
	CreatedType    string          `json:"created_type" description:"创建方式类型Applet-Chain、Applet-Assistant"`
	UsageType      string          `json:"usage_type" description:"用途LLM/Common"`
	CreateTime     int64           `json:"create_time" description:"创建时间"`
	ProblemGenInfo *ProblemGenInfo `json:"problem_gen_info" description:"用于生成问题的配置"`
}

type AppServiceInfo struct {
	ID         string                `json:"id"  description:"服务id"`
	Name       string                `json:"name"  description:"服务名称"`
	VirtualUrl string                `json:"virtual_url"  description:"服务虚拟地址, istio gateway 转发时匹配的路由前缀"`
	Status     serving.MLOpsSvcState `json:"status"  description:"服务上下线状态"`
	StateIno   *StateInfo            `json:"state_info" description:"服务状态信息"`
	HealthInfo *health.ChainHealth   `json:"health_info"  description:"服务健康信息,对运行中的服务才有意义"`
}

// AppletAssistant 应用仓库 - AI助手（零代码）
type AppletAssistant struct {
	ID             string          `json:"id" description:"上传文件时产生的知识库id,没有上传文件时请生成uuid"`
	ExperimentInfo *ExperimentInfo `json:"experiment_info" description:"体验时的基本信息"`
	LastDebugState string          `json:"last_debug_state" description:"必填 init,running,success,failed,canceled"`
	//AgentConfig        *agent_definition.LLMAgentConfig `json:"agent_config" description:"agent算子相关配置"`
	//LocalFiles         []*LocalFile                     `json:"local_files" description:"上传的本地文件信息"`
	//InputGuardrails    widgets.InputGuardrails          `json:"input_guardrails" description:"输入安全护栏配置"`
	//OutputGuardrails   widgets.OutputGuardrails         `json:"output_guardrails" description:"输出安全护栏配置"`
	//ProblemGenInfo     *ProblemGenInfo                  `json:"problem_gen_info" description:"用于生成问题的配置"`
	//InternetSearchInfo InternetSearchInfo               `json:"internet_search_info" description:"互联网检索相关配置"`
	AssistantInfo
	PermissionAction string                `json:"permission_action,omitempty" yaml:"permission_action"`
	PermissionCfg    *common.PermissionCfg `json:"permission_cfg,omitempty" yaml:"permission_cfg"`
}

type InternetSearchInfo struct {
	Enable   bool `json:"enable" description:"是否启用互联网检索"`
	ParseUrl bool `json:"parse_url" description:"是否开启网页解析"`
}
type ProblemGenInfo struct {
	Enable          bool   `json:"enable" description:"是否开启自动追问"`
	UseCustomPrompt bool   `json:"use_custom_prompt" description:"是否使用自定义提示词"`
	CustomPrompt    string `json:"custom_prompt,omitempty" description:"自定义提示词"`
}

type LocalFileInfo struct {
	KnowledgeBaseId string       `json:"knowledge_base_id" description:"上传本地文件所对应的知识库id"`
	LocalFiles      []*LocalFile `json:"local_files" description:"上传的本地文件信息"`
}

// LocalFile 用户上传的本地知识文件
type LocalFile struct {
	Name       string `json:"name" description:"文件名 e.g. test.pdf" `
	Path       string `json:"path" description:"文件路径（SFS格式）e.g. sfs:///temp/test.pdf" `
	Size       int    `json:"size" description:"文件大小" `
	DocumentID string `json:"document_id" description:"文件上传到知识库中后对应的文档id"`
}

type TriggerType string

const (
	TimeBased  TriggerType = "TimeBased"
	EventBased TriggerType = "EventBased"
)

type CronEvent struct {
	ScheduledTime time.Time
	TriggerInfo   *TriggerInfo
}

type EntryInfo struct {
	TimeZone TimeZone
	EntryId  cron.EntryID
}

type TriggerInfo struct {
	Trigger
	ChainId   string
	ServiceId string
}
type Trigger struct {
	Id                string            `json:"id"  description:"触发器id,对触发器进行唯一区分"`
	Name              string            `json:"name" description:"触发器名称"`
	TimeZone          TimeZone          `json:"time_zone" description:"时区,格式限定为UTC、Asia/Shanghai这样的格式。"`
	TriggerType       TriggerType       `json:"trigger_type" description:"触发器类型,TimeBased,EventBased"`
	CurlUrl           string            `json:"curl_url" description:"事件触发的具体curl_url,EventBased类型使用"`
	CronExpression    string            `json:"cron_expression" description:"cron表达式,示例0 18 * * 5,TimeBased类型使用"`
	MultimodalExample MultimodalExample `json:"multimodal_example"  description:"输入示例"`
}

type AssistantInfo struct {
	AgentConfig        *agent_definition.LLMAgentConfig `json:"agent_config" description:"agent算子相关配置"`
	LocalFiles         []*LocalFile                     `json:"local_files" description:"上传的本地文件信息"`
	InputGuardrails    widgets.InputGuardrails          `json:"input_guardrails" description:"输入安全护栏配置"`
	OutputGuardrails   widgets.OutputGuardrails         `json:"output_guardrails" description:"输出安全护栏配置"`
	ProblemGenInfo     *ProblemGenInfo                  `json:"problem_gen_info" description:"用于生成问题的配置"`
	InternetSearchInfo InternetSearchInfo               `json:"internet_search_info" description:"互联网检索相关配置"`
}

// Application 应用开发列表
type Application struct {
	ID             string           `json:"id" description:"id"`
	Name           string           `json:"name" description:"名称"`
	Creator        string           `json:"creator" description:"创建者名称"`
	Desc           string           `json:"desc" description:"描述"`
	Labels         *LabelGroups     `json:"labels" description:"标签信息"`
	LastDebugState *ChainDebugState `json:"debug_state" description:"上次调试状态"`
	ProjectID      string           `json:"projectID" description:"项目id"`
	UpdateTime     int64            `json:"update_time" description:"最近更新时间"`
	Published      bool             `json:"published" description:"是否发布"`
	CreatedType    string           `json:"created_type" description:"创建方式类型 Applet-Chain、Applet-Assistant"`
	ServiceInfo    *AppServiceInfo  `json:"service_info" description:"相关联的服务信息"`
	SourceInfo     *ChainSourceInfo `json:"source_info"`  // 来源信息
	MetricsInfo    ChainMetricsInfo `json:"metrics_info"` // 打点信息
}

type Member struct {
	Id       string `json:"id"  description:"用户或用户组名称"`
	UserType string `json:"user_type"  description:"类型"`
}

type TriggerReq struct {
	ChainId   string `json:"chain_id"`
	TriggerId string `json:"trigger_id"`
}

// TimeZone 表示一个合法的时区名称
type TimeZone string

// Validate 检查该时区是否有效
func (tz TimeZone) Validate() error {
	if _, err := time.LoadLocation(string(tz)); err != nil {
		return fmt.Errorf("invalid timezone: %s", tz)
	}
	return nil
}

// Location 获取对应的 *time.Location
func (tz TimeZone) Location() (*time.Location, error) {
	loc, err := time.LoadLocation(string(tz))
	if err != nil {
		return nil, fmt.Errorf("failed to load location for timezone %s: %w", tz, err)
	}
	return loc, nil
}
