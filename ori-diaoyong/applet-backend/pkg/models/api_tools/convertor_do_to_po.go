package api_tools

import (
	"github.com/aws/smithy-go/ptr"
	"time"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func APIToolCollectionReleasedStateDO2PO(released bool) string {
	if released {
		return APIToolCollectionStateReleased
	}
	return APIToolCollectionStateUnReleased
}

func (a *APIToolDO) ToPO(CollectionID string) (*generated.APITool, error) {
	paramStr, err := helper.InterfaceToString(a.ParamValues)
	if err != nil {
		return nil, err
	}
	tool := &generated.APITool{
		ID:           a.ID,
		Name:         a.Name,
		Alias_:       a.Alias,
		Desc:         a.Desc,
		TestState:    string(a.TestState),
		CollectionID: CollectionID,
		Method:       a.Method,
		Path:         a.Path,
		Params:       paramStr,
		ProjectID:    a.ProjectID,
	}
	return tool, nil
}

func ToolBatchDOToPO(DOs []*APIToolDO, collectionID string) ([]*generated.APITool, error) {
	res := make([]*generated.APITool, 0)
	for _, DO := range DOs {
		if PO, err := DO.ToPO(collectionID); err != nil {
			return nil, err
		} else {
			res = append(res, PO)
		}
	}
	return res, nil
}

func (a *APIToolCollectionDemoDO) ToPO() (*generated.APIToolCollectionDemo, error) {
	res := &generated.APIToolCollectionDemo{
		Name:      a.Name,
		MetaType:  string(a.Type),
		ProjectID: a.ProjectID,
		MetaInfo:  a.Meta,
		Desc:      a.Desc,
	}
	return res, nil
}

func (a *APIToolCollectionDO) ToPO() (*generated.APIToolCollection, error) {
	headers, err := helper.InterfaceToString(a.Headers)
	if err != nil {
		return nil, err
	}
	res := &generated.APIToolCollection{
		Name:       a.BaseInfo.Name,
		Creator:    a.BaseInfo.Creator,
		Desc:       a.BaseInfo.Desc,
		ServerType: string(a.BaseInfo.ServerType),
		McpParams:  helper.MarshalInterface(a.Params),
		McpType:    string(a.BaseInfo.McpType),
		MetaType:   string(a.MetaType),
		Type:       string(a.BaseInfo.Type),
		ProjectID:  a.BaseInfo.ProjectID,
		MetaInfo:   a.MetaInfo,
		Headers:    headers,
		BaseURL:    a.BaseURL,
		LogoURL:    a.BaseInfo.LogoUrl,
		ProxyInfo:  helper.MarshalInterface(a.ProxyInfo),
	}
	if a.BaseInfo.Released != nil {
		res.ReleasedState = APIToolCollectionReleasedStateDO2PO(*a.BaseInfo.Released)
	}
	if a.BaseInfo.LastReleaseTimeSec != 0 {
		res.LastPublishTime = ptr.Time(time.Unix(a.BaseInfo.LastReleaseTimeSec, 0))
	}
	return res, nil
}
