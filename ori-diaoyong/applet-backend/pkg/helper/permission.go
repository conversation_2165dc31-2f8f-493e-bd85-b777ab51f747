package helper

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"

	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func PermissionCfg2Policy(cfg *common.PermissionCfg, projectId string) []*cas.PutObjectReq {
	var putObjectReq []*cas.PutObjectReq
	for _, cfg := range cfg.CustomPolicies {
		objReq := &cas.PutObjectReq{
			SubType:   cas.SubType(cfg.SubType),
			Username:  cfg.Username,
			GroupId:   uint64(cfg.GroupId),
			Act:       cas.Act(cfg.Action),
			ProjectId: projectId,
		}
		putObjectReq = append(putObjectReq, objReq)
	}
	return putObjectReq
}

type ResourcePermission struct {
	WithAllPermission bool
	Id2Action         map[string]cas.Act
}

// 获得当前用户对于资产的权限
func GetUserAssetPermissionMap(ctx context.Context, objType cas.ObjType) (*ResourcePermission, error) {
	projectId := GetProjectID(ctx)
	username, err := GetUser(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get user info")
	}
	rbacApi := cas.NewRBACApi("", "")
	params := []cas.ListParam{
		cas.WithProjectId(projectId),
		cas.WithObjType(objType),
	}
	policies, err := rbacApi.ListObject(ctx, username, params...)
	if err != nil {
		return nil, stderr.Wrap(err, "获取当前用户%s下的资产权限列表失败！", username)
	}

	userAssetPerMap := make(map[string]cas.Act)
	withAllPermission := false
	if policies.AccessType == cas.AccessType_Unrestricted {
		withAllPermission = true
	} else {
		withAllPermission = false
		policiesList := policies.Objects
		for _, policy := range policiesList {
			assetObjId := policy.ObjId
			userAssetPerMap[assetObjId] = policy.Act
		}
	}
	//userAssetPerMap := map[string]cas.Act{"unrestricted": cas.Act_All}
	return &ResourcePermission{
		WithAllPermission: withAllPermission,
		Id2Action:         userAssetPerMap,
	}, nil
}

// PermissionConfigurable 需要进行权限过滤的数据结构
type PermissionConfigurable interface {
	GetID() (string, error)
	GetCreator() (string, error)
	GetPermissionCfg() (*common.PermissionCfg, error)
	SetPermissionCfg(cfg *common.PermissionCfg) error
	GetPermissionAction() (string, error)
	SetPermissionAction(action string) error
}

// FilterAndSetPermission 根据权限过滤,并设置当前用户对于资产的访问权限
// objs: 实现PermissionConfigurable接口的对象切片,方法中会修改权限相关的字段
// assetType: 资源类型（如 cas.ObjType_AppletChain）
// []T： 根据用户权限过滤后剩余的资产对象切片
func FilterAndSetPermission[T PermissionConfigurable](ctx context.Context, objs []T, assetType cas.ObjType) ([]T, error) {
	permission, err := GetUserAssetPermissionMap(ctx, assetType)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get user asset permission map")
	}
	// 全量权限，全部赋权并返回
	if permission.WithAllPermission {
		for _, obj := range objs {
			if err := obj.SetPermissionAction(string(cas.Act_All)); err != nil {
				return nil, err
			}
			cfg, err := obj.GetPermissionCfg()
			if err != nil {
				return nil, err
			}
			if cfg == nil || (cfg.PermissionMode == "" && cfg.PublicType == "") {
				if err := obj.SetPermissionAction(string(cas.Act_All)); err != nil {
					return nil, err
				}
				if err := obj.SetPermissionCfg(&common.PermissionCfg{
					PermissionMode: string(cas.PermissionMode_Public),
					PublicType:     string(cas.PublicType_All),
				}); err != nil {
					return nil, err
				}
			}
		}
		return objs, nil
	}

	currentUser, err := GetUser(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "fail to get current user")
	}
	var filtered []T
	for _, obj := range objs {
		cfg, err := obj.GetPermissionCfg()
		if err != nil {
			return nil, err
		}
		// 旧数据
		if cfg == nil || (cfg.PermissionMode == "" && cfg.PublicType == "") {
			if err := obj.SetPermissionAction(string(cas.Act_All)); err != nil {
				return nil, err
			}
			if err := obj.SetPermissionCfg(&common.PermissionCfg{
				PermissionMode: string(cas.PermissionMode_Public),
				PublicType:     string(cas.PublicType_All),
			}); err != nil {
				return nil, err
			}
			filtered = append(filtered, obj)
			continue
		}
		// 创建者
		creator, err := obj.GetCreator()
		if err != nil {
			return nil, err
		}
		if creator == currentUser {
			if err := obj.SetPermissionAction(string(cas.Act_All)); err != nil {
				return nil, err
			}
			filtered = append(filtered, obj)
			continue
		}
		// 公开数据
		if cfg.PermissionMode == string(cas.PermissionMode_Public) {
			if cfg.PublicType == string(cas.PublicType_All) {
				if err := obj.SetPermissionAction(string(cas.Act_All)); err != nil {
					return nil, err
				}
			} else if cfg.PublicType == string(cas.PublicType_Readonly) {
				if err := obj.SetPermissionAction(string(cas.Act_ReadOnly)); err != nil {
					return nil, err
				}
			}
			filtered = append(filtered, obj)
			continue
		}
		// 自定义权限数据
		id, err := obj.GetID()
		if err != nil {
			return nil, err
		}
		if action, ok := permission.Id2Action[id]; ok {
			if err := obj.SetPermissionAction(string(action)); err != nil {
				return nil, err
			}
			filtered = append(filtered, obj)
		}
	}
	return filtered, nil
}

// SubmitAndSetPermission 根据需要,将权限提交到cas(自定义权限)。 并设置当前用户对于资产的访问权限
// objs: 需实现 PermissionConfigurable 接口的对象切片
// assetType: 资源类型（如 cas.ObjType_AppletChain）
func SubmitAndSetPermission[T PermissionConfigurable](ctx context.Context, obj T, assetType cas.ObjType) error {
	objId, err := obj.GetID()
	if err != nil {
		return stderr.Wrap(err, "get obj id")
	}
	permissionCfg, err := obj.GetPermissionCfg()
	if err != nil {
		return stderr.Wrap(err, "get permission cfg ")
	}

	if permissionCfg == nil || (permissionCfg.PermissionMode == "" && permissionCfg.PublicType == "") {
		obj.SetPermissionCfg(&common.PermissionCfg{
			PermissionMode: string(cas.PermissionMode_Public),
			PublicType:     string(cas.PublicType_All),
		})
		obj.SetPermissionAction(string(cas.Act_All))
		stdlog.Infof("%s 权限配置为空,设置权限配置为公开", objId)
		return nil
	}
	if permissionCfg.PermissionMode == string(cas.PermissionMode_Public) {
		switch permissionCfg.PublicType {
		case string(cas.PublicType_All):
			obj.SetPermissionAction(string(cas.Act_All))
		case string(cas.PublicType_Readonly):
			obj.SetPermissionAction(string(cas.Act_ReadOnly))
		}
		stdlog.Infof("%s 权限为公开，设置权限配置为公开", objId)
		return nil
	}
	policy := PermissionCfg2Policy(permissionCfg, GetProjectID(ctx))
	casCli := cas.NewRBACApi("", "")
	_, err = casCli.PutObject(ctx, objId, cas.ObjType_AppletChain, policy)
	if err != nil {
		return stderr.Wrap(err, "set permission for chain %s", objId)
	}
	stdlog.Infof("提交 %s 的权限配置成功", objId)
	return nil

}
