package helper

import (
	"context"
	"github.com/apache/arrow/go/v12/arrow"
	"github.com/apache/arrow/go/v12/arrow/memory"
	"github.com/apache/arrow/go/v12/parquet"
	"github.com/apache/arrow/go/v12/parquet/file"
	"github.com/apache/arrow/go/v12/parquet/pqarrow"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func ParseParquetSchema(path string) (*arrow.Schema, error) {
	mem := memory.NewCheckedAllocator(memory.DefaultAllocator)
	rdr, err := file.OpenParquetFile(path, false, file.WithReadProps(parquet.NewReaderProperties(mem)))
	if err != nil {
		return nil, stderr.Wrap(err, "open parquet file %s", path)
	}
	defer rdr.Close()
	arrowRdr, err := pqarrow.NewFileReader(rdr, pqarrow.ArrowReadProperties{BatchSize: 10, Parallel: true}, mem)
	if err != nil {
		return nil, stderr.Wrap(err, "get file reader for parquet file %s", path)
	}
	return arrowRdr.Schema()
}

func ReadParquetFileAsMap(path string, memoryMap bool) (dataMaps []map[string]string, err error) {
	mem := memory.NewCheckedAllocator(memory.DefaultAllocator)
	rdr, err := file.OpenParquetFile(path, memoryMap, file.WithReadProps(parquet.NewReaderProperties(mem)))
	if err != nil {
		return nil, stderr.Wrap(err, "open parquet file %s", path)
	}
	arrowRdr, err := pqarrow.NewFileReader(rdr, pqarrow.ArrowReadProperties{BatchSize: 10, Parallel: true}, mem)
	if err != nil {
		return nil, stderr.Wrap(err, "get file reader for parquet file %s", path)
	}
	recordReader, err := arrowRdr.GetRecordReader(context.Background(), nil, nil)
	defer recordReader.Release()
	for recordReader.Next() {
		rec := recordReader.Record()
		numRows := rec.NumRows()
		numCols := rec.NumCols()
		for row := 0; row < int(numRows); row++ {
			rowMap := make(map[string]string, 0)
			for c := 0; c < int(numCols); c++ {
				colName := rec.ColumnName(c)
				colData := rec.Column(c)
				rowMap[colName] = colData.ValueStr(row)
			}
			dataMaps = append(dataMaps, rowMap)
		}
	}
	return dataMaps, nil
}
