package helper

// 用户自定义方法类型
// 使用方式为 /edge/api/v1/resources/{id}:{method}
type CustomHttpMethod = string

const (
	BatchCreate CustomHttpMethod = ":batchCreate" // 批量创建资源
)

// BatchIDs 用于批量操作(删除,启动,停止)指定类型的资源
type BatchIDs struct {
	IDs []string `json:"ids"` // 待批量操作的资源ID列表
}

type EmptyRsp struct{}

type ID struct {
	ID string `json:"id"`
}

type HttpStringResp struct {
	RespBody string `json:"resp_body"`
}

type IDs struct {
	IDs []string `json:"ids"`
}

type ChainRunRes struct {
}
