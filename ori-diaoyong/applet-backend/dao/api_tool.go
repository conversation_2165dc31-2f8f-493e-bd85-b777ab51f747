package dao

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gen"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type APIToolQueryParam struct {
	Name          *string
	CollectionID  *string
	ProjectID     *string
	IDs           []string
	CollectionIDs []string
}

func (a APIToolQueryParam) ToQueryConditions() []gen.Condition {
	cods := make([]gen.Condition, 0)
	q := query.APITool
	if a.Name != nil {
		cods = append(cods, q.Name.Eq(*a.Name))
	}
	if a.CollectionID != nil {
		cods = append(cods, q.CollectionID.Eq(*a.CollectionID))
	}
	if a.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*a.ProjectID))
	}
	if len(a.IDs) != 0 {
		cods = append(cods, q.ID.In(a.IDs...))
	}
	if len(a.CollectionIDs) != 0 {
		cods = append(cods, q.CollectionID.In(a.CollectionIDs...))
	}
	return cods
}

type APIToolDAO struct {
	BaseDAO
}

func (a APIToolDAO) Create(ctx context.Context, model *generated.APITool) error {
	q := a.getQueryOrDefault().APITool
	ID := uuid.New().String()
	model.ID = ID
	return q.WithContext(ctx).Create(model)
}

func (a APIToolDAO) BatchCreate(ctx context.Context, models []*generated.APITool) error {
	q := a.getQueryOrDefault().APITool
	for _, m := range models {
		ID := uuid.New().String()
		m.ID = ID
	}
	return q.WithContext(ctx).CreateInBatches(models, 10)
}

func (a APIToolDAO) UpdateByID(ctx context.Context, ID string, model *generated.APITool) error {
	q := a.getQueryOrDefault().APITool
	if _, err := q.WithContext(ctx).Where(q.ID.Eq(ID)).Updates(model); err != nil {
		return err
	}
	return nil
}

func (a APIToolDAO) ListByParam(ctx context.Context, param *APIToolQueryParam) ([]*generated.APITool, error) {
	q := a.getQueryOrDefault().APITool
	return q.WithContext(ctx).Where(param.ToQueryConditions()...).Find()
}

func (a APIToolDAO) BatchDelete(ctx context.Context, param *APIToolQueryParam) error {
	q := a.getQueryOrDefault().APITool
	if _, err := q.WithContext(ctx).Where(param.ToQueryConditions()...).Delete(); err != nil {
		return err
	}
	return nil
}

type ToolCnt struct {
	CollectionID string `json:"collection_id"`
	Cnt          int32  `json:"cnt"`
}

func (a APIToolDAO) CntByCollectionID(ctx context.Context, param *APIToolQueryParam) (map[string]int32, error) {
	res := make(map[string]int32)
	q := a.getQueryOrDefault().APITool
	cods := make([]gen.Condition, 0)
	if param.Name != nil {
		cods = append(cods, q.Name.Eq(*param.Name))
	}
	if param.CollectionID != nil {
		cods = append(cods, q.CollectionID.Eq(*param.CollectionID))
	}
	if param.ProjectID != nil {
		cods = append(cods, q.ProjectID.Eq(*param.ProjectID))
	}
	toolCnts := make([]*ToolCnt, 0)
	if err := q.WithContext(ctx).Where(cods...).Select(q.CollectionID, q.ID.Count().As("cnt")).Group(q.CollectionID).Scan(&toolCnts); err != nil {
		return nil, err
	}
	for _, t := range toolCnts {
		res[t.CollectionID] = t.Cnt
	}
	return res, nil
}

func (a APIToolDAO) BatchUpsert(ctx context.Context, models []*generated.APITool) error {
	q := a.getQueryOrDefault().APITool
	return q.CreateInBatches(models, 10)
}
