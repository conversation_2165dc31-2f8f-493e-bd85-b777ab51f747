package dao

import (
	"context"

	"github.com/influxdata/kapacitor/uuid"
	"gorm.io/gen"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type CustomWidgetDAO struct {
	BaseDAO
}

// CustomWidgetQueryParam 自定义算子常用查询条件
type CustomWidgetQueryParam struct {
	ID        *string `json:"id" description:"算子唯一id"`
	Name      *string `json:"name"  description:"算子名称"`
	Status    *string `json:"status"  description:"算子运行状态"`
	Creator   *string `json:"creator" description:"创建者"`
	ProjectID *string `json:"project_id" description:"算子所属项目,默认为当前项目"`
}

func (d CustomWidgetDAO) Create(ctx context.Context, customWidget *generated.CustomWidget) (string, error) {
	query := d.getQueryOrDefault().CustomWidget
	if customWidget.ID == "" {
		customWidget.ID = uuid.New().String()
	}
	if err := query.WithContext(ctx).Create(customWidget); err != nil {
		return "", err
	}
	return customWidget.ID, nil
}

func (d CustomWidgetDAO) DeleteByID(ctx context.Context, id string) error {
	query := d.getQueryOrDefault().CustomWidget
	if _, err := query.WithContext(ctx).Where(query.ID.Eq(id)).Delete(); err != nil {
		return err
	}
	return nil
}

func (d CustomWidgetDAO) ListAll(ctx context.Context) ([]*generated.CustomWidget, error) {
	query := d.getQueryOrDefault().CustomWidget
	res, err := query.WithContext(ctx).Find()
	return res, err
}

func (d CustomWidgetDAO) List(ctx context.Context, queryParam *CustomWidgetQueryParam) ([]*generated.CustomWidget, error) {
	query := d.getQueryOrDefault().CustomWidget
	cods := make([]gen.Condition, 0)
	if queryParam.ID != nil {
		cods = append(cods, query.ID.Eq(*queryParam.ID))
	}
	if queryParam.Name != nil {
		cods = append(cods, query.Name.Eq(*queryParam.Name))
	}
	if queryParam.Status != nil {
		cods = append(cods, query.Status.Eq(*queryParam.Status))
	}
	if queryParam.Creator != nil {
		cods = append(cods, query.Creator.Eq(*queryParam.Creator))
	}
	if queryParam.ProjectID != nil {
		cods = append(cods, query.ProjectID.Eq(*queryParam.ProjectID))
	}
	res, err := query.WithContext(ctx).Where(cods...).Find()
	return res, err
}

func (d CustomWidgetDAO) GetByID(ctx context.Context, ID string) (*generated.CustomWidget, error) {
	query := d.getQueryOrDefault().CustomWidget
	res, err := query.WithContext(ctx).Where(query.ID.Eq(ID)).Find()
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, stderr.Internal.Error("no custom widget :%v", ID)
	}
	return res[0], err
}

// Update 更新算子，不更新0值
func (d CustomWidgetDAO) Update(ctx context.Context, ID string, widget *generated.CustomWidget) error {
	query := d.getQueryOrDefault().CustomWidget
	_, err := query.WithContext(ctx).Where(query.ID.Eq(ID)).Updates(widget)
	if err != nil {
		return err
	}
	return nil
}
