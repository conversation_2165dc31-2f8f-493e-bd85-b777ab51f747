package dao

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type AppletServiceDAO struct {
	db *gorm.DB
}

func newAppletServiceDAO() *AppletServiceDAO {
	return &AppletServiceDAO{db: MustInitDB()}
}

func getAppletServiceDAO() *AppletServiceDAO {
	return newAppletServiceDAO()
}

func (dao *AppletServiceDAO) SaveAppletService(ctx context.Context, svc *models.AppletService) error {
	if svc.ProjectID == "" {
		svc.ProjectID = helper.GetProjectID(ctx)
	}
	return dao.db.Create(svc).Error
}

func (dao *AppletServiceDAO) UpsertAppletService(ctx context.Context, svc *models.AppletService) error {
	if svc.ProjectID == "" {
		svc.ProjectID = helper.GetProjectID(ctx)
	}
	return db.Clauses(clause.OnConflict{
		UpdateAll: true, // 更新所有字段
	}).Create(svc).Error
}

func (dao *AppletServiceDAO) GetAppletService(ctx context.Context, chainId string) (*models.AppletService, error) {
	svc := new(models.AppletService)
	if err := dao.db.Where(&models.AppletService{ChainID: chainId}).Find(svc).Error; err != nil {
		return nil, err
	}
	return svc, nil
}

func (dao *AppletServiceDAO) ListAppletService(ctx context.Context) ([]*models.AppletService, error) {
	svcs := make([]*models.AppletService, 0)
	if err := dao.db.Where(&models.AppletService{ProjectID: helper.GetProjectID(ctx)}).Find(&svcs).Error; err != nil {
		return nil, err
	}
	return svcs, nil
}

func (dao *AppletServiceDAO) ListAllAppletService(ctx context.Context) ([]*models.AppletService, error) {
	svcs := make([]*models.AppletService, 0)
	if err := dao.db.Find(&svcs).Error; err != nil {
		return nil, err
	}
	return svcs, nil
}
