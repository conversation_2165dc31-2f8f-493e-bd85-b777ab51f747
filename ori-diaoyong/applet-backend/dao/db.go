package dao

import (
	"database/sql"
	"fmt"
	"log"
	"reflect"
	"sync"
	"time"

	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	stdconf "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

var (
	AppletChainDAOImpl        *AppletChainDAO
	AppletExperimentDAOImpl   *AppletExperimentDAO
	AppletLabelDAOImpl        *AppletLabelDAO
	CustomWidgetDAOImpl       *CustomWidgetDAO
	ChainDebugHistoryImpl     *ChainDebugDAO
	APIToolCollectionImpl     *APIToolCollectionDAO
	APIToolImpl               *APIToolDAO
	APIToolCollectionDemoImpl *APIToolCollectionDemoDAO
	GuardrailsImpl            *GuardrailsDAO
	LLMBasicConfigDAOImpl     *LLMBasicConfigDAO
	DialogDAOImpl             *DialogDAO
	ChainDebugLogDAOImpl      *ChainDebugLogDAO
	ChainSnapshotDAOImpl      *ChainSnapshotDAO
	AppletServiceDAOImpl      *AppletServiceDAO
)

var (
	ModelsToAutoMigrate = []any{
		AppletChain{},
		AppletLabel{},
		CustomWidget{},
		ChainDebugHistory{},
		ChainSnapshot{},
		models.ChainDebugLog{},
		AppletExperiment{},
		APIToolCollection{},
		APITool{},
		APIToolCollectionDemo{},
		models.KnowledgeBase{},
		models.Document{},
		models.Chunk{},
		models.DocElement{},
		models.DocTask{},
		SafetyConfig{},
		LLMBasicConfig{},
		Dialog{},
		DialogMessage{},
		DialogApp{},
		models.ExternalAPP{},
		ChainTemplate{},
		models.AppletService{},
		models.DocTaskAssetRel{},
	}
)

var (
	db     *gorm.DB
	dbOnce sync.Once
)

func Init() {
	// init here

	// Set Default DB for global Query
	db = MustInitDB()
	query.SetDefault(db)
	InitDaoImpl()
}

// NewAppletChainDAO 需要用到事务的时候，需要手动传入query
func NewAppletChainDAO(query *query.Query) *AppletChainDAO {
	return &AppletChainDAO{BaseDAO{query: query}}
}

func NewAppletLabelDAO(query *query.Query) *AppletLabelDAO {
	return &AppletLabelDAO{BaseDAO{query: query}}
}

func NewCustomWidgetDAO(query *query.Query) *CustomWidgetDAO {
	return &CustomWidgetDAO{BaseDAO{query: query}}
}

func NewChainDebugHistoryDAO(query *query.Query) *ChainDebugDAO {
	return &ChainDebugDAO{BaseDAO{query: query}}
}

func NewChainSnapshotDAO(query *query.Query) *ChainSnapshotDAO {
	return &ChainSnapshotDAO{BaseDAO{query: query}}
}

func NewAPICollectionDAO(query *query.Query) *APIToolCollectionDAO {
	return &APIToolCollectionDAO{BaseDAO{query: query}}
}

func NewAPIToolDAO(query *query.Query) *APIToolDAO {
	return &APIToolDAO{BaseDAO{query: query}}
}

func NewLLMBasicConfigDAO(query *query.Query) *LLMBasicConfigDAO {
	return &LLMBasicConfigDAO{BaseDAO{query: query}}
}

func NewDialogAO(query *query.Query) *DialogDAO {
	return &DialogDAO{BaseDAO{query: query}}
}

func InitDaoImpl() {
	AppletChainDAOImpl = &AppletChainDAO{}
	AppletExperimentDAOImpl = &AppletExperimentDAO{}
	AppletLabelDAOImpl = &AppletLabelDAO{}
	CustomWidgetDAOImpl = &CustomWidgetDAO{}
	ChainDebugHistoryImpl = &ChainDebugDAO{}
	APIToolCollectionImpl = &APIToolCollectionDAO{}
	APIToolImpl = &APIToolDAO{}
	APIToolCollectionDemoImpl = &APIToolCollectionDemoDAO{}
	GuardrailsImpl = &GuardrailsDAO{}
	LLMBasicConfigDAOImpl = &LLMBasicConfigDAO{}
	DialogDAOImpl = &DialogDAO{}
	ChainDebugLogDAOImpl = GetChainDebugLogDAO()
	ChainSnapshotDAOImpl = &ChainSnapshotDAO{}
	AppletServiceDAOImpl = getAppletServiceDAO()

}

// InitQuery 初始化DAO查询接口
func InitQuery() (Q *query.Query) {
	mdb := MustInitDB()
	Q = query.Use(mdb)
	return Q
}

// MustInitDB 根据配置初始化数据库实例, 请注意如果初始化失败将会直接导致panic
func MustInitDB() *gorm.DB {
	dbOnce.Do(func() {
		var err error
		switch conf.Config.Database {
		case conf.DatabaseMysql:
			stdlog.Debug("use mysql as database")
			db, err = ConnectMySQL(conf.Config.Mysql)
		case conf.DatabaseSqlite:
			stdlog.Debug("use sqlite as database")
			db, err = ConnectSqlite(conf.Config.Sqlite)
		default:
			panic(any("unsupported database type"))
		}
		if err != nil {
			log.Panicf("failed to init db: %s", err.Error())
		}

		// 如果是1.x -> 2.x升级，需要手动修改document表结构
		if conf.Config.KnowlhubConfig.DocumentPkUpdate {
			// 执行SQL语句修改documents表结构，删除索引
			if err := db.Exec("ALTER TABLE documents DROP INDEX idx_documents_id").Error; err != nil {
				stdlog.WithError(err).Error("无法删除documents表的索引idx_documents_id")
				// 这里不需要panic，因为可能是表结构已经更新过了或索引不存在
				stdlog.Warn("documents表索引idx_documents_id删除失败，可能表结构已经更新过或索引不存在")
			} else {
				stdlog.Info("成功删除documents表的索引idx_documents_id")
			}
			// 执行SQL语句修改document表结构，删除主键
			if err := db.Exec("ALTER TABLE documents DROP PRIMARY KEY").Error; err != nil {
				stdlog.WithError(err).Error("无法删除document表的主键")
				// 这里不需要panic，因为可能是表结构已经更新过了
				stdlog.Warn("document表主键删除失败，可能表结构已经更新过")
			} else {
				stdlog.Info("成功删除document表的主键")
			}
		}

		// 只在第一次进行自动建表操作
		for _, m := range ModelsToAutoMigrate {
			err := db.AutoMigrate(m)
			if err != nil {
				stdlog.WithError(err).Errorf("failed to auto migrate for %s", reflect.TypeOf(m))
				panic(err)
			}
		}
	})
	return db
}

func ConnectMySQL(c stdconf.MysqlConfig) (*gorm.DB, error) {
	// 参考 https://github.com/go-sql-driver/mysql#dsn-data-source-name 获取详情
	// 先不用dbname连接，创建好db后再使用dbname连接
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local",
		c.Username, c.Password, c.Host, c.Port)
	d, err := sql.Open("mysql", dsn)
	if err != nil {
		stdlog.WithError(err).Error("failed to connect to mysql")
		return nil, err
	}
	sql := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARSET utf8mb4", c.DBName)
	_, err = d.Exec(sql)
	if err != nil {
		stdlog.WithError(err).Error("failed to create db.")
		return nil, err
	}
	_ = d.Close()
	dsn = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.Username, c.Password, c.Host, c.Port, c.DBName)
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                                   logger.Default.LogMode(logger.Info),
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		stdlog.WithError(err).Error("failed to connect to mysql")
		return nil, err
	}

	if !c.NotPrintSql {
		db = db.Debug()
	}

	sqlDB, err := db.DB()
	if err != nil {
		stdlog.WithError(err).Error("failed to get sqlDB for ConnPool setup")
		return nil, err
	}
	sqlDB.SetMaxIdleConns(c.MaxIdle)
	sqlDB.SetMaxOpenConns(c.MaxConn)
	return db, nil
}

func ConnectSqlite(c stdconf.SqliteConfig) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(c.File), &gorm.Config{})
	if err != nil {
		stdlog.WithError(err).Error("failed to connect to sqlite")
		return nil, err
	}
	return db, nil
}

// AppletChain 仅用于生成表结构
type AppletChain struct {
	Id               string    `gorm:"type:VARCHAR(50);primaryKey; comment:应用链ID"`
	Name             string    `gorm:"type:VARCHAR(256); not null; collate:utf8_bin; uniqueIndex:unique_name_project;comment:应用链名字"`
	Creator          string    `gorm:"type:VARCHAR(256); not null; comment:创建人"`
	LastDebugState   int32     `gorm:"type:int;not null; default:0;comment:最新一次调试状态，0未调试，1调试中，2成功，3失败"`
	Label            string    `gorm:"type:TEXT; comment:标签，map结构"`
	Chain            string    `gorm:"type:MEDIUMTEXT; comment:算子&依赖关系编排"`
	Desc             string    `gorm:"type:VARCHAR(1024); not null;comment:应用链描述"`
	DebugInfo        string    `gorm:"type:TEXT; not null;comment:调试信息"`
	ContainsSubChain int32     `gorm:"type:int;not null; comment:是否包含子链，2不包含，1包含"`
	ProjectID        string    `gorm:"type:VARCHAR(256); not null;uniqueIndex:unique_name_project; comment:项目ID"`
	CreateTime       time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime      time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:数据库记录更新时间"`
	ChainUpdatedTime time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_chain_updated_time;comment:应用链更新时间"`
	AssetType        int32     `gorm:"type:int; comment:资产类型[0共享资产，1内嵌资产]"`
	UsageType        string    `gorm:"type:VARCHAR(256); not null; default:'Common';comment:用途，LLM/Common"`
	CreatedType      string    `gorm:"type:VARCHAR(256); not null; default:'Applet-Chain';comment:创建类型"`
	SourceInfo       string    `gorm:"type:VARCHAR(256); not null; default:'';comment:来源信息"`
	MetricsInfo      string    `gorm:"type:VARCHAR(256); not null; default:'';comment:打点信息，访问次数等"`
	AssistantInfo    string    `gorm:"type:MEDIUMTEXT; comment:智能体相关信息"`
	ExamplesInfo     string    `gorm:"type:TEXT; comment:图片地址、开场白、引导示例"`
	SpaceInfo        string    `gorm:"type:VARCHAR(256); default:'{}';comment:机构空间所属行业及首页精选等相关信息"`
	PermissionAction string    `gorm:"type:VARCHAR(256); default:'';comment:权限操作"`
	PermissionCfg    string    `gorm:"type:TEXT;comment:权限配置"`
}

// AppletLabel 仅用于生成表结构
type AppletLabel struct {
	Id          string    `gorm:"type:VARCHAR(50);primaryKey; comment:应用链ID"`
	GroupName   string    `gorm:"type:VARCHAR(256); not null; uniqueIndex:unique_name_group_name;comment:标签组名字"`
	Name        string    `gorm:"type:VARCHAR(256); not null; uniqueIndex:unique_name_group_name;comment:标签名字"`
	Creator     string    `gorm:"type:VARCHAR(256); not null; comment:创建人"`
	ProjectID   string    `gorm:"type:VARCHAR(256); not null; comment:项目ID"`
	CreateTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// CustomWidget 仅用于生成表结构,自定义算子
type CustomWidget struct {
	Id           string    `gorm:"type:VARCHAR(50);primaryKey; comment:自定义算子唯一ID"`
	Name         string    `gorm:"type:VARCHAR(256); not null; uniqueIndex:unique_name;comment:算子名称"`
	Desc         string    `gorm:"type:text; comment:算子描述信息"`
	Status       string    `gorm:"type:VARCHAR(256); default:'Stopped'; not null; comment:算子运行状态"`
	Port         uint32    `gorm:"type:int;not null; not null;comment:端口"`
	ParamInfo    string    `gorm:"type:text; not null; comment:参数列表"`
	ImageInfo    string    `gorm:"type:TEXT; comment:自定义算子镜像信息，map结构"`
	LabelInfo    string    `gorm:"type:TEXT; comment:标签，map结构"`
	ResourceInfo string    `gorm:"type:TEXT; comment:资源配置信息，map结构"`
	DeployInfo   string    `gorm:"type:text; not null; comment:部署信息"`
	Creator      string    `gorm:"type:VARCHAR(256); not null; comment:创建人"`
	ProjectID    string    `gorm:"type:VARCHAR(256); not null; uniqueIndex:unique_name;comment:项目ID"`
	CreatedTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime  time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// ChainDebugHistory 调试记录 仅用于生成表结构
type ChainDebugHistory struct {
	Id              string    `gorm:"type:VARCHAR(50);primaryKey; comment:ID"`
	ChainId         string    `gorm:"type:VARCHAR(50);index:index_chain_id;comment:应用链ID"`
	ChainSnapshotId int64     `gorm:"type:bigint; comment:应用链快照id"`
	ChainSnapshot   string    `gorm:"type:MEDIUMTEXT; comment:应用链快照详情"`
	State           int32     `gorm:"type:int;not null; default:0;comment:调试状态，0未调试，1调试中，2成功，3失败，4取消"`
	Creator         string    `gorm:"type:VARCHAR(256); not null; comment:创建人"`
	ProjectID       string    `gorm:"type:VARCHAR(256); not null;comment:项目ID"`
	CreateTime      time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime     time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
	DebugMessage    string    `gorm:"type:LONGTEXT; comment:EventStream 中 type 为'debug'的数据"`
	DebugName       string    `gorm:"type:MEDIUMTEXT; comment:用户输入（textInput或fileName）作为单条调试记录的名称"`
}

type ChainSnapshot struct {
	Id          int64  `gorm:"type:bigint;primaryKey; comment:ID，和ChainDebugHistory的ChainSnapshotId关联"`
	ChainDetail string `gorm:"type:MEDIUMTEXT; comment:应用链快照"`
	Base        string `gorm:"type:MEDIUMTEXT; comment:用于service version info 的 base 信息"`
	ChainId     string `gorm:"type:VARCHAR(256); comment:应用链id"`
}

// AppletExperiment 由应用链发布的应用体验，用于供他人试用
type AppletExperiment struct {
	ChainId      string    `gorm:"type:VARCHAR(50);primaryKey;comment:相关的应用链ID"`
	Name         string    `gorm:"type:VARCHAR(256); not null; comment:应用体验名称"`
	ImageUrl     string    `gorm:"type:VARCHAR(256); not null; comment:封面的图片地址"`
	Introduction string    `gorm:"type:VARCHAR(256); not null; comment:开场白，应用介绍"`
	Examples     string    `gorm:"type:VARCHAR(256); not null; comment:常用问题示例"`
	LabelInfo    string    `gorm:"type:VARCHAR(256); not null; default:'{}'; comment:标签，map结构"`
	Creator      string    `gorm:"type:VARCHAR(256); not null; comment:创建人"`
	ProjectID    string    `gorm:"type:VARCHAR(256); not null;comment:项目ID"`
	Status       string    `gorm:"type:VARCHAR(256); not null; default:'Published'; comment:'应用体验是否发布'"`
	ServiceInfo  string    `gorm:"type:VARCHAR(256); not null;comment:应用体验部署后的服务信息"`
	CreatedBy    string    `gorm:"type:VARCHAR(256); not null; default:'applet-chain'; comment:'创建途径是智能体还是应用链'"`
	CreatedTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime  time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// APIToolCollection api工具集
type APIToolCollection struct {
	Id              string    `gorm:"type:VARCHAR(50);primaryKey; comment:ID"`
	ProjectID       string    `gorm:"type:VARCHAR(256); not null; default :'';uniqueIndex:unique_name;comment:项目ID"`
	Name            string    `gorm:"type:VARCHAR(256); not null;default :''; uniqueIndex:unique_name;comment:工具集名称"`
	Creator         string    `gorm:"type:VARCHAR(256); not null;default :''; comment:创建人"`
	Desc            string    `gorm:"type:VARCHAR(1024); not null;default :''; comment:工具集描述"`
	ReleasedState   string    `gorm:"type:VARCHAR(50); not null; default : 'un_released';comment:是否发布，released，un_released"`
	MetaType        string    `gorm:"type:VARCHAR(256); not null;default :''; comment:元信息类型 json/yaml"`
	Type            string    `gorm:"type:VARCHAR(256); not null;default :'common''; comment:工具集类型 common/template"`
	MetaInfo        []byte    `gorm:"type:text; comment:元信息详情"`
	Headers         string    `gorm:"type:VARCHAR(2048); not null;default :''; comment: 请求头，map类型"`
	BaseUrl         string    `gorm:"type:VARCHAR(256); not null;default :''; comment: 请求地址"`
	LastPublishTime time.Time `gorm:"type:TIMESTAMP;null;comment:最后一次发布时间"`
	LogoUrl         string    `gorm:"type:VARCHAR(256); not null;default :''; comment: 工具集logo"`
	ProxyInfo       string    `gorm:"type:VARCHAR(256); not null;default :''; comment: 代理配置"`
	CreateTime      time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime     time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
	ServerType      string    `gorm:"type:VARCHAR(256); not null;default :'rest''; comment:工具集类型 rest/mcp/dynamic_mcp"`
	MCPParams       string    `gorm:"type:VARCHAR(2048); not null;default :''; comment: mcp参数，json结构"`
	McpType         string    `gorm:"type:VARCHAR(256); not null;default :'''; comment:mcp类型 sse/streamableHttp"`
}

// APITool 工具
type APITool struct {
	Id           string    `gorm:"type:VARCHAR(50);primaryKey; comment:ID"`
	ProjectID    string    `gorm:"type:VARCHAR(256); not null;default :''; comment:项目ID"`
	CollectionId string    `gorm:"type:VARCHAR(256); not null;default :'';index:index_collection_id; comment:工具集ID"`
	Name         string    `gorm:"type:VARCHAR(256); not null;default :''; comment:工具名称,从yaml - path中解析出来的name"`
	Alias        string    `gorm:"type:VARCHAR(256); not null;default :''; comment:工具别名，name for human"`
	Desc         string    `gorm:"type:VARCHAR(1024); not null;default ''; comment:工具描述"`
	TestState    string    `gorm:"type:VARCHAR(50); not null;default :'init'; comment: 测试状态:init/success/failed"`
	Method       string    `gorm:"type:VARCHAR(50); not null;default :''; comment:请求方法GET/POST/xxx等"`
	Path         string    `gorm:"type:VARCHAR(256); not null;default :''; comment:请求路径"`
	Params       string    `gorm:"type:text; comment:参数，json array格式"`
	CreateTime   time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime  time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// APIToolCollectionDemo api工具集示例
type APIToolCollectionDemo struct {
	Id          string    `gorm:"type:VARCHAR(50);primaryKey; comment:ID"`
	ProjectID   string    `gorm:"type:VARCHAR(256); not null; comment:项目ID"`
	Name        string    `gorm:"type:VARCHAR(256); not null;default ''; uniqueIndex:unique_name;comment:工具集示例名称"`
	Desc        string    `gorm:"type:VARCHAR(1024); not null;default ''; comment:工具集示例描述"`
	MetaType    string    `gorm:"type:VARCHAR(256); not null; default '';comment:元信息类型 json/yaml"`
	MetaInfo    []byte    `gorm:"type:text; comment:元信息详情"`
	CreateTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// SafetyConfig 安全围栏配置
type SafetyConfig struct {
	ID               string                    `gorm:"column:id;type:varchar(50);primaryKey" json:"id"`
	ProjectID        string                    `gorm:"column:project_id;type:varchar(36);index;unique" json:"project_id"`
	InputGuardrails  *widgets.InputGuardrails  `gorm:"column:input_guardrails;type:json" json:"input_guardrails"`
	OutputGuardrails *widgets.OutputGuardrails `gorm:"column:output_guardrails;type:json" json:"output_guardrails"`
}

// LLMBasicConfig 大语言模型配置
type LLMBasicConfig struct {
	ID                 string `gorm:"column:id;type:varchar(50);primaryKey" json:"id"`
	ProjectID          string `gorm:"column:project_id;type:varchar(36);index;unique" json:"project_id"`
	LLMModelSvc        string `gorm:"column:llm_model_svc;type:MEDIUMTEXT" json:"llm_model_svc"`
	EmbeddingSvc       string `gorm:"column:embedding_svc;type:MEDIUMTEXT" json:"embedding_svc"`
	ImageGenSvc        string `gorm:"column:image_gen_svc;type:MEDIUMTEXT" json:"image_gen_svc"`
	ReRankSvc          string `gorm:"column:re_rank_svc;type:MEDIUMTEXT" json:"re_rank_svc"`
	ImageUnderstandSvc string `gorm:"column:image_understand_svc" json:"image_understand_svc"`
	OcrSvc             string `gorm:"column:ocr_svc" json:"ocr_svc"`
}

// Dialog 大语言模型对话
type Dialog struct {
	ID               int64     `gorm:"column:id;unique;autoIncrement;" json:"id"`
	AppId            string    `gorm:"column:app_id;type:varchar(256);" json:"app_id"`
	AppName          string    `gorm:"column:app_name;type:varchar(256);" json:"app_name"`
	CreatedType      string    `gorm:"column:created_type;type:varchar(256);comment:应用类型" json:"created_type"`
	ProjectID        string    `gorm:"column:project_id;type:varchar(36);" json:"project_id"`
	ChatId           string    `gorm:"column:chat_id;not null;type:varchar(50);uniqueIndex:unique_name_project" json:"chat_id"`
	User             string    `gorm:"column:user;type:varchar(36)" json:"user"`
	LatestQuestionId int64     `gorm:"column:latest_question_id;type:bigint;comment:会话最新的提问id" json:"latest_question_id"`
	LatestAnswerId   int64     `gorm:"column:latest_answer_id;type:bigint;comment:会话最新的回答id" json:"latest_answer_id"`
	CreateTime       time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间" json:"create_time"`
	UpdatedTime      time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// DialogMessage 大语言模型历史记录
type DialogMessage struct {
	ID          int64     `gorm:"column:id;unique;autoIncrement;" json:"id"`
	ChatId      string    `gorm:"column:chat_id;not null;type:varchar(50);index;" json:"chat_id"`
	Content     string    `gorm:"column:content;type:LONGTEXT;" json:"content"`
	Role        string    `gorm:"column:role;type:varchar(50);comment:记录的类型:(question, answer)" json:"role"`
	CreateTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

type DialogApp struct {
	ID          int64     `gorm:"column:id;unique;autoIncrement;" json:"id"`
	AppId       string    `gorm:"column:app_id;type:varchar(256);uniqueIndex:unique_user_project_app" json:"app_id"`
	AppName     string    `gorm:"column:app_name;type:varchar(256);" json:"app_name"`
	AppImage    string    `gorm:"column:app_image;type:varchar(256);" json:"app_image"`
	CreatedType string    `gorm:"column:created_type;type:varchar(256);comment:应用类型" json:"created_type"`
	ProjectID   string    `gorm:"column:project_id;type:varchar(36);uniqueIndex:unique_user_project_app" json:"project_id"`
	User        string    `gorm:"column:user;type:varchar(36);uniqueIndex:unique_user_project_app" json:"user"`
	CreateTime  time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

// ChainTemplate 应用链模板
type ChainTemplate struct {
	Id       string `gorm:"type:VARCHAR(50);primaryKey;comment:模板ID"`
	Name     string `gorm:"type:VARCHAR(256);not null;collate:utf8_bin;uniqueIndex:unique_name_project;comment:模板名称"`
	Desc     string `gorm:"type:VARCHAR(1024); not null;comment:模板描述"`
	Template string `gorm:"type:MEDIUMTEXT; comment:模板详情(算子编排信息)"`
	// ProjectID string `gorm:"type:VARCHAR(256); not null;uniqueIndex:unique_name_project; comment:项目ID"`
	CreatedTime time.Time `gorm:"type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间"`
	UpdatedTime time.Time `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间"`
}

func (LLMBasicConfig) TableName() string {
	return "llm_basic_configs"
}
