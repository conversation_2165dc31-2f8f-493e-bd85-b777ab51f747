package dao

import (
	"fmt"

	"log"
	"testing"
	"time"

	"gorm.io/gorm"
	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

func TestListLog(t *testing.T) {
	SetDBConfig()
	start := time.Now()
	ChainDebugLogDAOImpl := &ChainDebugLogDAO{db: MustInitDBForTest().Debug()}
	elapsed := time.Since(start)
	fmt.Printf("创建连接耗时：%v\n", elapsed)

	start2 := time.Now()
	logs, err := ChainDebugLogDAOImpl.ListDebugLog(nil, "2d5a797e-8f19-4615-bb9b-8274b83cf004", "7b8ac9b7-1ac6-4406-b2ca-0c49dde7d770_dd9536d8-15cb-4790-a3f3-73fee5560a9a")
	elapsed2 := time.Since(start2)
	fmt.Printf("查询耗时：%v\n", elapsed2)

	if err != nil {
		t.Fatal("查询失败：", err)
	}
	fmt.Println(logs)
}

func SetDBConfig() {
	conf.Config = &conf.AppConfig{
		Database: "mysql",
		Mysql: conf2.MysqlConfig{
			Username:       "root",
			Password:       "Warp!CV@2022#",
			Host:           "**************",
			Port:           "31346",
			DBName:         "applet_backend",
			NotPrintSql:    false,
			NotCreateTable: false,
		},
	}
}

// 仅用于测试，删除判断Documents的逻辑；为了速度只Migrate指定的模型
func MustInitDBForTest() *gorm.DB {
	dbOnce.Do(func() {
		var err error
		switch conf.Config.Database {
		case conf.DatabaseMysql:
			stdlog.Debug("use mysql as database")
			db, err = ConnectMySQL(conf.Config.Mysql)
		case conf.DatabaseSqlite:
			stdlog.Debug("use sqlite as database")
			db, err = ConnectSqlite(conf.Config.Sqlite)
		default:
			panic(any("unsupported database type"))
		}
		if err != nil {
			log.Panicf("failed to init db: %s", err.Error())
		}

		err = db.AutoMigrate(models.ChainDebugLog{})
		if err != nil {
			stdlog.WithError(err).Errorf("failed to auto migrate")
			panic(err)
		}
	})
	return db
}
