// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newChainTemplate(db *gorm.DB, opts ...gen.DOOption) chainTemplate {
	_chainTemplate := chainTemplate{}

	_chainTemplate.chainTemplateDo.UseDB(db, opts...)
	_chainTemplate.chainTemplateDo.UseModel(&generated.ChainTemplate{})

	tableName := _chainTemplate.chainTemplateDo.TableName()
	_chainTemplate.ALL = field.NewAsterisk(tableName)
	_chainTemplate.ID = field.NewString(tableName, "id")
	_chainTemplate.Name = field.NewString(tableName, "name")
	_chainTemplate.Desc = field.NewString(tableName, "desc")
	_chainTemplate.Template = field.NewString(tableName, "template")
	_chainTemplate.CreatedTime = field.NewTime(tableName, "created_time")
	_chainTemplate.UpdatedTime = field.NewTime(tableName, "updated_time")

	_chainTemplate.fillFieldMap()

	return _chainTemplate
}

type chainTemplate struct {
	chainTemplateDo

	ALL         field.Asterisk
	ID          field.String // 模板ID
	Name        field.String // 模板名称
	Desc        field.String // 模板描述
	Template    field.String // 模板详情(算子编排信息)
	CreatedTime field.Time   // 创建时间
	UpdatedTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (c chainTemplate) Table(newTableName string) *chainTemplate {
	c.chainTemplateDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chainTemplate) As(alias string) *chainTemplate {
	c.chainTemplateDo.DO = *(c.chainTemplateDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chainTemplate) updateTableName(table string) *chainTemplate {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.Name = field.NewString(table, "name")
	c.Desc = field.NewString(table, "desc")
	c.Template = field.NewString(table, "template")
	c.CreatedTime = field.NewTime(table, "created_time")
	c.UpdatedTime = field.NewTime(table, "updated_time")

	c.fillFieldMap()

	return c
}

func (c *chainTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chainTemplate) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 6)
	c.fieldMap["id"] = c.ID
	c.fieldMap["name"] = c.Name
	c.fieldMap["desc"] = c.Desc
	c.fieldMap["template"] = c.Template
	c.fieldMap["created_time"] = c.CreatedTime
	c.fieldMap["updated_time"] = c.UpdatedTime
}

func (c chainTemplate) clone(db *gorm.DB) chainTemplate {
	c.chainTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c chainTemplate) replaceDB(db *gorm.DB) chainTemplate {
	c.chainTemplateDo.ReplaceDB(db)
	return c
}

type chainTemplateDo struct{ gen.DO }

type IChainTemplateDo interface {
	gen.SubQuery
	Debug() IChainTemplateDo
	WithContext(ctx context.Context) IChainTemplateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IChainTemplateDo
	WriteDB() IChainTemplateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IChainTemplateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IChainTemplateDo
	Not(conds ...gen.Condition) IChainTemplateDo
	Or(conds ...gen.Condition) IChainTemplateDo
	Select(conds ...field.Expr) IChainTemplateDo
	Where(conds ...gen.Condition) IChainTemplateDo
	Order(conds ...field.Expr) IChainTemplateDo
	Distinct(cols ...field.Expr) IChainTemplateDo
	Omit(cols ...field.Expr) IChainTemplateDo
	Join(table schema.Tabler, on ...field.Expr) IChainTemplateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IChainTemplateDo
	RightJoin(table schema.Tabler, on ...field.Expr) IChainTemplateDo
	Group(cols ...field.Expr) IChainTemplateDo
	Having(conds ...gen.Condition) IChainTemplateDo
	Limit(limit int) IChainTemplateDo
	Offset(offset int) IChainTemplateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IChainTemplateDo
	Unscoped() IChainTemplateDo
	Create(values ...*generated.ChainTemplate) error
	CreateInBatches(values []*generated.ChainTemplate, batchSize int) error
	Save(values ...*generated.ChainTemplate) error
	First() (*generated.ChainTemplate, error)
	Take() (*generated.ChainTemplate, error)
	Last() (*generated.ChainTemplate, error)
	Find() ([]*generated.ChainTemplate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainTemplate, err error)
	FindInBatches(result *[]*generated.ChainTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.ChainTemplate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IChainTemplateDo
	Assign(attrs ...field.AssignExpr) IChainTemplateDo
	Joins(fields ...field.RelationField) IChainTemplateDo
	Preload(fields ...field.RelationField) IChainTemplateDo
	FirstOrInit() (*generated.ChainTemplate, error)
	FirstOrCreate() (*generated.ChainTemplate, error)
	FindByPage(offset int, limit int) (result []*generated.ChainTemplate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IChainTemplateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c chainTemplateDo) Debug() IChainTemplateDo {
	return c.withDO(c.DO.Debug())
}

func (c chainTemplateDo) WithContext(ctx context.Context) IChainTemplateDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chainTemplateDo) ReadDB() IChainTemplateDo {
	return c.Clauses(dbresolver.Read)
}

func (c chainTemplateDo) WriteDB() IChainTemplateDo {
	return c.Clauses(dbresolver.Write)
}

func (c chainTemplateDo) Session(config *gorm.Session) IChainTemplateDo {
	return c.withDO(c.DO.Session(config))
}

func (c chainTemplateDo) Clauses(conds ...clause.Expression) IChainTemplateDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chainTemplateDo) Returning(value interface{}, columns ...string) IChainTemplateDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chainTemplateDo) Not(conds ...gen.Condition) IChainTemplateDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chainTemplateDo) Or(conds ...gen.Condition) IChainTemplateDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chainTemplateDo) Select(conds ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chainTemplateDo) Where(conds ...gen.Condition) IChainTemplateDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chainTemplateDo) Order(conds ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chainTemplateDo) Distinct(cols ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chainTemplateDo) Omit(cols ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chainTemplateDo) Join(table schema.Tabler, on ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chainTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chainTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chainTemplateDo) Group(cols ...field.Expr) IChainTemplateDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chainTemplateDo) Having(conds ...gen.Condition) IChainTemplateDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chainTemplateDo) Limit(limit int) IChainTemplateDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chainTemplateDo) Offset(offset int) IChainTemplateDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chainTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IChainTemplateDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chainTemplateDo) Unscoped() IChainTemplateDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chainTemplateDo) Create(values ...*generated.ChainTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chainTemplateDo) CreateInBatches(values []*generated.ChainTemplate, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chainTemplateDo) Save(values ...*generated.ChainTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chainTemplateDo) First() (*generated.ChainTemplate, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainTemplate), nil
	}
}

func (c chainTemplateDo) Take() (*generated.ChainTemplate, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainTemplate), nil
	}
}

func (c chainTemplateDo) Last() (*generated.ChainTemplate, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainTemplate), nil
	}
}

func (c chainTemplateDo) Find() ([]*generated.ChainTemplate, error) {
	result, err := c.DO.Find()
	return result.([]*generated.ChainTemplate), err
}

func (c chainTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainTemplate, err error) {
	buf := make([]*generated.ChainTemplate, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chainTemplateDo) FindInBatches(result *[]*generated.ChainTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chainTemplateDo) Attrs(attrs ...field.AssignExpr) IChainTemplateDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chainTemplateDo) Assign(attrs ...field.AssignExpr) IChainTemplateDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chainTemplateDo) Joins(fields ...field.RelationField) IChainTemplateDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chainTemplateDo) Preload(fields ...field.RelationField) IChainTemplateDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chainTemplateDo) FirstOrInit() (*generated.ChainTemplate, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainTemplate), nil
	}
}

func (c chainTemplateDo) FirstOrCreate() (*generated.ChainTemplate, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainTemplate), nil
	}
}

func (c chainTemplateDo) FindByPage(offset int, limit int) (result []*generated.ChainTemplate, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chainTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chainTemplateDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chainTemplateDo) Delete(models ...*generated.ChainTemplate) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chainTemplateDo) withDO(do gen.Dao) *chainTemplateDo {
	c.DO = *do.(*gen.DO)
	return c
}
