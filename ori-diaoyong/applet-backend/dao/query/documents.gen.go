// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newDocument(db *gorm.DB, opts ...gen.DOOption) document {
	_document := document{}

	_document.documentDo.UseDB(db, opts...)
	_document.documentDo.UseModel(&models.Document{})

	tableName := _document.documentDo.TableName()
	_document.ALL = field.NewAsterisk(tableName)
	_document.Id = field.NewString(tableName, "id")
	_document.Name = field.NewString(tableName, "name")
	_document.FilePath = field.NewString(tableName, "file_path")
	_document.FileSizeBytes = field.NewInt32(tableName, "file_size_bytes")
	_document.FileFormat = field.NewString(tableName, "file_format")
	_document.UploadTime = field.NewTime(tableName, "upload_time_mills")
	_document.NumChars = field.NewInt32(tableName, "num_chars")
	_document.FileMD5 = field.NewString(tableName, "file_md5")
	_document.KnowledgeBaseId = field.NewString(tableName, "knowledge_base_id")
	_document.ProjectId = field.NewString(tableName, "project_id")
	_document.TableConfig = field.NewField(tableName, "table_config")
	_document.DocumentFileSource = field.NewInt32(tableName, "document_file_source")
	_document.CorpusConfig = field.NewField(tableName, "corpus_config")
	_document.DocProcessingConfig = field.NewField(tableName, "doc_processing_config")
	_document.IndexConfig = field.NewField(tableName, "index_config")
	_document.Stage = field.NewInt32(tableName, "stage")
	_document.DbStatus = field.NewInt32(tableName, "db_status")
	_document.ProcessTask = field.NewField(tableName, "process_task")
	_document.FileAsset = field.NewField(tableName, "file_asset")
	_document.AssetRel = field.NewField(tableName, "asset_rel")
	_document.ShortId = field.NewInt64(tableName, "short_id")
	_document.RetrieveEnabled = field.NewBool(tableName, "retrieve_enabled")
	_document.AbstractChunk = field.NewField(tableName, "abstract_chunk")
	_document.NumChunks = field.NewInt64(tableName, "num_chunks")
	_document.NumSuccessChunks = field.NewInt64(tableName, "num_success_chunks")

	_document.fillFieldMap()

	return _document
}

type document struct {
	documentDo

	ALL                 field.Asterisk
	Id                  field.String
	Name                field.String
	FilePath            field.String
	FileSizeBytes       field.Int32
	FileFormat          field.String
	UploadTime          field.Time
	NumChars            field.Int32
	FileMD5             field.String
	KnowledgeBaseId     field.String
	ProjectId           field.String
	TableConfig         field.Field
	DocumentFileSource  field.Int32
	CorpusConfig        field.Field
	DocProcessingConfig field.Field
	IndexConfig         field.Field
	Stage               field.Int32
	DbStatus            field.Int32
	ProcessTask         field.Field
	FileAsset           field.Field
	AssetRel            field.Field
	ShortId             field.Int64
	RetrieveEnabled     field.Bool
	AbstractChunk       field.Field
	NumChunks           field.Int64
	NumSuccessChunks    field.Int64

	fieldMap map[string]field.Expr
}

func (d document) Table(newTableName string) *document {
	d.documentDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d document) As(alias string) *document {
	d.documentDo.DO = *(d.documentDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *document) updateTableName(table string) *document {
	d.ALL = field.NewAsterisk(table)
	d.Id = field.NewString(table, "id")
	d.Name = field.NewString(table, "name")
	d.FilePath = field.NewString(table, "file_path")
	d.FileSizeBytes = field.NewInt32(table, "file_size_bytes")
	d.FileFormat = field.NewString(table, "file_format")
	d.UploadTime = field.NewTime(table, "upload_time_mills")
	d.NumChars = field.NewInt32(table, "num_chars")
	d.FileMD5 = field.NewString(table, "file_md5")
	d.KnowledgeBaseId = field.NewString(table, "knowledge_base_id")
	d.ProjectId = field.NewString(table, "project_id")
	d.TableConfig = field.NewField(table, "table_config")
	d.DocumentFileSource = field.NewInt32(table, "document_file_source")
	d.CorpusConfig = field.NewField(table, "corpus_config")
	d.DocProcessingConfig = field.NewField(table, "doc_processing_config")
	d.IndexConfig = field.NewField(table, "index_config")
	d.Stage = field.NewInt32(table, "stage")
	d.DbStatus = field.NewInt32(table, "db_status")
	d.ProcessTask = field.NewField(table, "process_task")
	d.FileAsset = field.NewField(table, "file_asset")
	d.AssetRel = field.NewField(table, "asset_rel")
	d.ShortId = field.NewInt64(table, "short_id")
	d.RetrieveEnabled = field.NewBool(table, "retrieve_enabled")
	d.AbstractChunk = field.NewField(table, "abstract_chunk")
	d.NumChunks = field.NewInt64(table, "num_chunks")
	d.NumSuccessChunks = field.NewInt64(table, "num_success_chunks")

	d.fillFieldMap()

	return d
}

func (d *document) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *document) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 25)
	d.fieldMap["id"] = d.Id
	d.fieldMap["name"] = d.Name
	d.fieldMap["file_path"] = d.FilePath
	d.fieldMap["file_size_bytes"] = d.FileSizeBytes
	d.fieldMap["file_format"] = d.FileFormat
	d.fieldMap["upload_time_mills"] = d.UploadTime
	d.fieldMap["num_chars"] = d.NumChars
	d.fieldMap["file_md5"] = d.FileMD5
	d.fieldMap["knowledge_base_id"] = d.KnowledgeBaseId
	d.fieldMap["project_id"] = d.ProjectId
	d.fieldMap["table_config"] = d.TableConfig
	d.fieldMap["document_file_source"] = d.DocumentFileSource
	d.fieldMap["corpus_config"] = d.CorpusConfig
	d.fieldMap["doc_processing_config"] = d.DocProcessingConfig
	d.fieldMap["index_config"] = d.IndexConfig
	d.fieldMap["stage"] = d.Stage
	d.fieldMap["db_status"] = d.DbStatus
	d.fieldMap["process_task"] = d.ProcessTask
	d.fieldMap["file_asset"] = d.FileAsset
	d.fieldMap["asset_rel"] = d.AssetRel
	d.fieldMap["short_id"] = d.ShortId
	d.fieldMap["retrieve_enabled"] = d.RetrieveEnabled
	d.fieldMap["abstract_chunk"] = d.AbstractChunk
	d.fieldMap["num_chunks"] = d.NumChunks
	d.fieldMap["num_success_chunks"] = d.NumSuccessChunks
}

func (d document) clone(db *gorm.DB) document {
	d.documentDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d document) replaceDB(db *gorm.DB) document {
	d.documentDo.ReplaceDB(db)
	return d
}

type documentDo struct{ gen.DO }

type IDocumentDo interface {
	gen.SubQuery
	Debug() IDocumentDo
	WithContext(ctx context.Context) IDocumentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDocumentDo
	WriteDB() IDocumentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDocumentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDocumentDo
	Not(conds ...gen.Condition) IDocumentDo
	Or(conds ...gen.Condition) IDocumentDo
	Select(conds ...field.Expr) IDocumentDo
	Where(conds ...gen.Condition) IDocumentDo
	Order(conds ...field.Expr) IDocumentDo
	Distinct(cols ...field.Expr) IDocumentDo
	Omit(cols ...field.Expr) IDocumentDo
	Join(table schema.Tabler, on ...field.Expr) IDocumentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDocumentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDocumentDo
	Group(cols ...field.Expr) IDocumentDo
	Having(conds ...gen.Condition) IDocumentDo
	Limit(limit int) IDocumentDo
	Offset(offset int) IDocumentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDocumentDo
	Unscoped() IDocumentDo
	Create(values ...*models.Document) error
	CreateInBatches(values []*models.Document, batchSize int) error
	Save(values ...*models.Document) error
	First() (*models.Document, error)
	Take() (*models.Document, error)
	Last() (*models.Document, error)
	Find() ([]*models.Document, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Document, err error)
	FindInBatches(result *[]*models.Document, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Document) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDocumentDo
	Assign(attrs ...field.AssignExpr) IDocumentDo
	Joins(fields ...field.RelationField) IDocumentDo
	Preload(fields ...field.RelationField) IDocumentDo
	FirstOrInit() (*models.Document, error)
	FirstOrCreate() (*models.Document, error)
	FindByPage(offset int, limit int) (result []*models.Document, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDocumentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d documentDo) Debug() IDocumentDo {
	return d.withDO(d.DO.Debug())
}

func (d documentDo) WithContext(ctx context.Context) IDocumentDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d documentDo) ReadDB() IDocumentDo {
	return d.Clauses(dbresolver.Read)
}

func (d documentDo) WriteDB() IDocumentDo {
	return d.Clauses(dbresolver.Write)
}

func (d documentDo) Session(config *gorm.Session) IDocumentDo {
	return d.withDO(d.DO.Session(config))
}

func (d documentDo) Clauses(conds ...clause.Expression) IDocumentDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d documentDo) Returning(value interface{}, columns ...string) IDocumentDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d documentDo) Not(conds ...gen.Condition) IDocumentDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d documentDo) Or(conds ...gen.Condition) IDocumentDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d documentDo) Select(conds ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d documentDo) Where(conds ...gen.Condition) IDocumentDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d documentDo) Order(conds ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d documentDo) Distinct(cols ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d documentDo) Omit(cols ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d documentDo) Join(table schema.Tabler, on ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d documentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d documentDo) RightJoin(table schema.Tabler, on ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d documentDo) Group(cols ...field.Expr) IDocumentDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d documentDo) Having(conds ...gen.Condition) IDocumentDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d documentDo) Limit(limit int) IDocumentDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d documentDo) Offset(offset int) IDocumentDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d documentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDocumentDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d documentDo) Unscoped() IDocumentDo {
	return d.withDO(d.DO.Unscoped())
}

func (d documentDo) Create(values ...*models.Document) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d documentDo) CreateInBatches(values []*models.Document, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d documentDo) Save(values ...*models.Document) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d documentDo) First() (*models.Document, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Document), nil
	}
}

func (d documentDo) Take() (*models.Document, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Document), nil
	}
}

func (d documentDo) Last() (*models.Document, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Document), nil
	}
}

func (d documentDo) Find() ([]*models.Document, error) {
	result, err := d.DO.Find()
	return result.([]*models.Document), err
}

func (d documentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Document, err error) {
	buf := make([]*models.Document, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d documentDo) FindInBatches(result *[]*models.Document, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d documentDo) Attrs(attrs ...field.AssignExpr) IDocumentDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d documentDo) Assign(attrs ...field.AssignExpr) IDocumentDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d documentDo) Joins(fields ...field.RelationField) IDocumentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d documentDo) Preload(fields ...field.RelationField) IDocumentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d documentDo) FirstOrInit() (*models.Document, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Document), nil
	}
}

func (d documentDo) FirstOrCreate() (*models.Document, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Document), nil
	}
}

func (d documentDo) FindByPage(offset int, limit int) (result []*models.Document, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d documentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d documentDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d documentDo) Delete(models ...*models.Document) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *documentDo) withDO(do gen.Dao) *documentDo {
	d.DO = *do.(*gen.DO)
	return d
}
