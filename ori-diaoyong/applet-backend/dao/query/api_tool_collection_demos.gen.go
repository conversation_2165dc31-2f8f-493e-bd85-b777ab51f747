// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAPIToolCollectionDemo(db *gorm.DB, opts ...gen.DOOption) aPIToolCollectionDemo {
	_aPIToolCollectionDemo := aPIToolCollectionDemo{}

	_aPIToolCollectionDemo.aPIToolCollectionDemoDo.UseDB(db, opts...)
	_aPIToolCollectionDemo.aPIToolCollectionDemoDo.UseModel(&generated.APIToolCollectionDemo{})

	tableName := _aPIToolCollectionDemo.aPIToolCollectionDemoDo.TableName()
	_aPIToolCollectionDemo.ALL = field.NewAsterisk(tableName)
	_aPIToolCollectionDemo.ID = field.NewString(tableName, "id")
	_aPIToolCollectionDemo.ProjectID = field.NewString(tableName, "project_id")
	_aPIToolCollectionDemo.Name = field.NewString(tableName, "name")
	_aPIToolCollectionDemo.Desc = field.NewString(tableName, "desc")
	_aPIToolCollectionDemo.MetaType = field.NewString(tableName, "meta_type")
	_aPIToolCollectionDemo.MetaInfo = field.NewBytes(tableName, "meta_info")
	_aPIToolCollectionDemo.CreateTime = field.NewTime(tableName, "create_time")
	_aPIToolCollectionDemo.UpdatedTime = field.NewTime(tableName, "updated_time")

	_aPIToolCollectionDemo.fillFieldMap()

	return _aPIToolCollectionDemo
}

type aPIToolCollectionDemo struct {
	aPIToolCollectionDemoDo

	ALL         field.Asterisk
	ID          field.String // ID
	ProjectID   field.String // 项目ID
	Name        field.String // 工具集示例名称
	Desc        field.String // 工具集示例描述
	MetaType    field.String // 元信息类型 json/yaml
	MetaInfo    field.Bytes  // 元信息详情
	CreateTime  field.Time   // 创建时间
	UpdatedTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a aPIToolCollectionDemo) Table(newTableName string) *aPIToolCollectionDemo {
	a.aPIToolCollectionDemoDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aPIToolCollectionDemo) As(alias string) *aPIToolCollectionDemo {
	a.aPIToolCollectionDemoDo.DO = *(a.aPIToolCollectionDemoDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aPIToolCollectionDemo) updateTableName(table string) *aPIToolCollectionDemo {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.ProjectID = field.NewString(table, "project_id")
	a.Name = field.NewString(table, "name")
	a.Desc = field.NewString(table, "desc")
	a.MetaType = field.NewString(table, "meta_type")
	a.MetaInfo = field.NewBytes(table, "meta_info")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")

	a.fillFieldMap()

	return a
}

func (a *aPIToolCollectionDemo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aPIToolCollectionDemo) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["name"] = a.Name
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["meta_type"] = a.MetaType
	a.fieldMap["meta_info"] = a.MetaInfo
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["updated_time"] = a.UpdatedTime
}

func (a aPIToolCollectionDemo) clone(db *gorm.DB) aPIToolCollectionDemo {
	a.aPIToolCollectionDemoDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aPIToolCollectionDemo) replaceDB(db *gorm.DB) aPIToolCollectionDemo {
	a.aPIToolCollectionDemoDo.ReplaceDB(db)
	return a
}

type aPIToolCollectionDemoDo struct{ gen.DO }

type IAPIToolCollectionDemoDo interface {
	gen.SubQuery
	Debug() IAPIToolCollectionDemoDo
	WithContext(ctx context.Context) IAPIToolCollectionDemoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAPIToolCollectionDemoDo
	WriteDB() IAPIToolCollectionDemoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAPIToolCollectionDemoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAPIToolCollectionDemoDo
	Not(conds ...gen.Condition) IAPIToolCollectionDemoDo
	Or(conds ...gen.Condition) IAPIToolCollectionDemoDo
	Select(conds ...field.Expr) IAPIToolCollectionDemoDo
	Where(conds ...gen.Condition) IAPIToolCollectionDemoDo
	Order(conds ...field.Expr) IAPIToolCollectionDemoDo
	Distinct(cols ...field.Expr) IAPIToolCollectionDemoDo
	Omit(cols ...field.Expr) IAPIToolCollectionDemoDo
	Join(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo
	Group(cols ...field.Expr) IAPIToolCollectionDemoDo
	Having(conds ...gen.Condition) IAPIToolCollectionDemoDo
	Limit(limit int) IAPIToolCollectionDemoDo
	Offset(offset int) IAPIToolCollectionDemoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolCollectionDemoDo
	Unscoped() IAPIToolCollectionDemoDo
	Create(values ...*generated.APIToolCollectionDemo) error
	CreateInBatches(values []*generated.APIToolCollectionDemo, batchSize int) error
	Save(values ...*generated.APIToolCollectionDemo) error
	First() (*generated.APIToolCollectionDemo, error)
	Take() (*generated.APIToolCollectionDemo, error)
	Last() (*generated.APIToolCollectionDemo, error)
	Find() ([]*generated.APIToolCollectionDemo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APIToolCollectionDemo, err error)
	FindInBatches(result *[]*generated.APIToolCollectionDemo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.APIToolCollectionDemo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAPIToolCollectionDemoDo
	Assign(attrs ...field.AssignExpr) IAPIToolCollectionDemoDo
	Joins(fields ...field.RelationField) IAPIToolCollectionDemoDo
	Preload(fields ...field.RelationField) IAPIToolCollectionDemoDo
	FirstOrInit() (*generated.APIToolCollectionDemo, error)
	FirstOrCreate() (*generated.APIToolCollectionDemo, error)
	FindByPage(offset int, limit int) (result []*generated.APIToolCollectionDemo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAPIToolCollectionDemoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aPIToolCollectionDemoDo) Debug() IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Debug())
}

func (a aPIToolCollectionDemoDo) WithContext(ctx context.Context) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aPIToolCollectionDemoDo) ReadDB() IAPIToolCollectionDemoDo {
	return a.Clauses(dbresolver.Read)
}

func (a aPIToolCollectionDemoDo) WriteDB() IAPIToolCollectionDemoDo {
	return a.Clauses(dbresolver.Write)
}

func (a aPIToolCollectionDemoDo) Session(config *gorm.Session) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Session(config))
}

func (a aPIToolCollectionDemoDo) Clauses(conds ...clause.Expression) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aPIToolCollectionDemoDo) Returning(value interface{}, columns ...string) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aPIToolCollectionDemoDo) Not(conds ...gen.Condition) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aPIToolCollectionDemoDo) Or(conds ...gen.Condition) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aPIToolCollectionDemoDo) Select(conds ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aPIToolCollectionDemoDo) Where(conds ...gen.Condition) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aPIToolCollectionDemoDo) Order(conds ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aPIToolCollectionDemoDo) Distinct(cols ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aPIToolCollectionDemoDo) Omit(cols ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aPIToolCollectionDemoDo) Join(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aPIToolCollectionDemoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aPIToolCollectionDemoDo) RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aPIToolCollectionDemoDo) Group(cols ...field.Expr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aPIToolCollectionDemoDo) Having(conds ...gen.Condition) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aPIToolCollectionDemoDo) Limit(limit int) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aPIToolCollectionDemoDo) Offset(offset int) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aPIToolCollectionDemoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aPIToolCollectionDemoDo) Unscoped() IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aPIToolCollectionDemoDo) Create(values ...*generated.APIToolCollectionDemo) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aPIToolCollectionDemoDo) CreateInBatches(values []*generated.APIToolCollectionDemo, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aPIToolCollectionDemoDo) Save(values ...*generated.APIToolCollectionDemo) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aPIToolCollectionDemoDo) First() (*generated.APIToolCollectionDemo, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollectionDemo), nil
	}
}

func (a aPIToolCollectionDemoDo) Take() (*generated.APIToolCollectionDemo, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollectionDemo), nil
	}
}

func (a aPIToolCollectionDemoDo) Last() (*generated.APIToolCollectionDemo, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollectionDemo), nil
	}
}

func (a aPIToolCollectionDemoDo) Find() ([]*generated.APIToolCollectionDemo, error) {
	result, err := a.DO.Find()
	return result.([]*generated.APIToolCollectionDemo), err
}

func (a aPIToolCollectionDemoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APIToolCollectionDemo, err error) {
	buf := make([]*generated.APIToolCollectionDemo, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aPIToolCollectionDemoDo) FindInBatches(result *[]*generated.APIToolCollectionDemo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aPIToolCollectionDemoDo) Attrs(attrs ...field.AssignExpr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aPIToolCollectionDemoDo) Assign(attrs ...field.AssignExpr) IAPIToolCollectionDemoDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aPIToolCollectionDemoDo) Joins(fields ...field.RelationField) IAPIToolCollectionDemoDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aPIToolCollectionDemoDo) Preload(fields ...field.RelationField) IAPIToolCollectionDemoDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aPIToolCollectionDemoDo) FirstOrInit() (*generated.APIToolCollectionDemo, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollectionDemo), nil
	}
}

func (a aPIToolCollectionDemoDo) FirstOrCreate() (*generated.APIToolCollectionDemo, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollectionDemo), nil
	}
}

func (a aPIToolCollectionDemoDo) FindByPage(offset int, limit int) (result []*generated.APIToolCollectionDemo, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aPIToolCollectionDemoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aPIToolCollectionDemoDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aPIToolCollectionDemoDo) Delete(models ...*generated.APIToolCollectionDemo) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aPIToolCollectionDemoDo) withDO(do gen.Dao) *aPIToolCollectionDemoDo {
	a.DO = *do.(*gen.DO)
	return a
}
