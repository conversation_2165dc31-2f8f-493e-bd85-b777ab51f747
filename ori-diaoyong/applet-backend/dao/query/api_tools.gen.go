// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAPITool(db *gorm.DB, opts ...gen.DOOption) aPITool {
	_aPITool := aPITool{}

	_aPITool.aPIToolDo.UseDB(db, opts...)
	_aPITool.aPIToolDo.UseModel(&generated.APITool{})

	tableName := _aPITool.aPIToolDo.TableName()
	_aPITool.ALL = field.NewAsterisk(tableName)
	_aPITool.ID = field.NewString(tableName, "id")
	_aPITool.ProjectID = field.NewString(tableName, "project_id")
	_aPITool.CollectionID = field.NewString(tableName, "collection_id")
	_aPITool.Name = field.NewString(tableName, "name")
	_aPITool.Alias_ = field.NewString(tableName, "alias")
	_aPITool.Desc = field.NewString(tableName, "desc")
	_aPITool.TestState = field.NewString(tableName, "test_state")
	_aPITool.Method = field.NewString(tableName, "method")
	_aPITool.Path = field.NewString(tableName, "path")
	_aPITool.Params = field.NewString(tableName, "params")
	_aPITool.CreateTime = field.NewTime(tableName, "create_time")
	_aPITool.UpdatedTime = field.NewTime(tableName, "updated_time")

	_aPITool.fillFieldMap()

	return _aPITool
}

type aPITool struct {
	aPIToolDo

	ALL          field.Asterisk
	ID           field.String // ID
	ProjectID    field.String // 项目ID
	CollectionID field.String // 工具集ID
	Name         field.String // 工具名称,从yaml - path中解析出来的name
	Alias_       field.String // 工具别名，name for human
	Desc         field.String // 工具描述
	TestState    field.String //  测试状态:init/success/failed
	Method       field.String // 请求方法GET/POST/xxx等
	Path         field.String // 请求路径
	Params       field.String // 参数，json array格式
	CreateTime   field.Time   // 创建时间
	UpdatedTime  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a aPITool) Table(newTableName string) *aPITool {
	a.aPIToolDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aPITool) As(alias string) *aPITool {
	a.aPIToolDo.DO = *(a.aPIToolDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aPITool) updateTableName(table string) *aPITool {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.ProjectID = field.NewString(table, "project_id")
	a.CollectionID = field.NewString(table, "collection_id")
	a.Name = field.NewString(table, "name")
	a.Alias_ = field.NewString(table, "alias")
	a.Desc = field.NewString(table, "desc")
	a.TestState = field.NewString(table, "test_state")
	a.Method = field.NewString(table, "method")
	a.Path = field.NewString(table, "path")
	a.Params = field.NewString(table, "params")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")

	a.fillFieldMap()

	return a
}

func (a *aPITool) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aPITool) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["collection_id"] = a.CollectionID
	a.fieldMap["name"] = a.Name
	a.fieldMap["alias"] = a.Alias_
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["test_state"] = a.TestState
	a.fieldMap["method"] = a.Method
	a.fieldMap["path"] = a.Path
	a.fieldMap["params"] = a.Params
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["updated_time"] = a.UpdatedTime
}

func (a aPITool) clone(db *gorm.DB) aPITool {
	a.aPIToolDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aPITool) replaceDB(db *gorm.DB) aPITool {
	a.aPIToolDo.ReplaceDB(db)
	return a
}

type aPIToolDo struct{ gen.DO }

type IAPIToolDo interface {
	gen.SubQuery
	Debug() IAPIToolDo
	WithContext(ctx context.Context) IAPIToolDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAPIToolDo
	WriteDB() IAPIToolDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAPIToolDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAPIToolDo
	Not(conds ...gen.Condition) IAPIToolDo
	Or(conds ...gen.Condition) IAPIToolDo
	Select(conds ...field.Expr) IAPIToolDo
	Where(conds ...gen.Condition) IAPIToolDo
	Order(conds ...field.Expr) IAPIToolDo
	Distinct(cols ...field.Expr) IAPIToolDo
	Omit(cols ...field.Expr) IAPIToolDo
	Join(table schema.Tabler, on ...field.Expr) IAPIToolDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolDo
	Group(cols ...field.Expr) IAPIToolDo
	Having(conds ...gen.Condition) IAPIToolDo
	Limit(limit int) IAPIToolDo
	Offset(offset int) IAPIToolDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolDo
	Unscoped() IAPIToolDo
	Create(values ...*generated.APITool) error
	CreateInBatches(values []*generated.APITool, batchSize int) error
	Save(values ...*generated.APITool) error
	First() (*generated.APITool, error)
	Take() (*generated.APITool, error)
	Last() (*generated.APITool, error)
	Find() ([]*generated.APITool, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APITool, err error)
	FindInBatches(result *[]*generated.APITool, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.APITool) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAPIToolDo
	Assign(attrs ...field.AssignExpr) IAPIToolDo
	Joins(fields ...field.RelationField) IAPIToolDo
	Preload(fields ...field.RelationField) IAPIToolDo
	FirstOrInit() (*generated.APITool, error)
	FirstOrCreate() (*generated.APITool, error)
	FindByPage(offset int, limit int) (result []*generated.APITool, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAPIToolDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aPIToolDo) Debug() IAPIToolDo {
	return a.withDO(a.DO.Debug())
}

func (a aPIToolDo) WithContext(ctx context.Context) IAPIToolDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aPIToolDo) ReadDB() IAPIToolDo {
	return a.Clauses(dbresolver.Read)
}

func (a aPIToolDo) WriteDB() IAPIToolDo {
	return a.Clauses(dbresolver.Write)
}

func (a aPIToolDo) Session(config *gorm.Session) IAPIToolDo {
	return a.withDO(a.DO.Session(config))
}

func (a aPIToolDo) Clauses(conds ...clause.Expression) IAPIToolDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aPIToolDo) Returning(value interface{}, columns ...string) IAPIToolDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aPIToolDo) Not(conds ...gen.Condition) IAPIToolDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aPIToolDo) Or(conds ...gen.Condition) IAPIToolDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aPIToolDo) Select(conds ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aPIToolDo) Where(conds ...gen.Condition) IAPIToolDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aPIToolDo) Order(conds ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aPIToolDo) Distinct(cols ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aPIToolDo) Omit(cols ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aPIToolDo) Join(table schema.Tabler, on ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aPIToolDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aPIToolDo) RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aPIToolDo) Group(cols ...field.Expr) IAPIToolDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aPIToolDo) Having(conds ...gen.Condition) IAPIToolDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aPIToolDo) Limit(limit int) IAPIToolDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aPIToolDo) Offset(offset int) IAPIToolDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aPIToolDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aPIToolDo) Unscoped() IAPIToolDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aPIToolDo) Create(values ...*generated.APITool) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aPIToolDo) CreateInBatches(values []*generated.APITool, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aPIToolDo) Save(values ...*generated.APITool) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aPIToolDo) First() (*generated.APITool, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APITool), nil
	}
}

func (a aPIToolDo) Take() (*generated.APITool, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APITool), nil
	}
}

func (a aPIToolDo) Last() (*generated.APITool, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APITool), nil
	}
}

func (a aPIToolDo) Find() ([]*generated.APITool, error) {
	result, err := a.DO.Find()
	return result.([]*generated.APITool), err
}

func (a aPIToolDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APITool, err error) {
	buf := make([]*generated.APITool, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aPIToolDo) FindInBatches(result *[]*generated.APITool, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aPIToolDo) Attrs(attrs ...field.AssignExpr) IAPIToolDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aPIToolDo) Assign(attrs ...field.AssignExpr) IAPIToolDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aPIToolDo) Joins(fields ...field.RelationField) IAPIToolDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aPIToolDo) Preload(fields ...field.RelationField) IAPIToolDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aPIToolDo) FirstOrInit() (*generated.APITool, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APITool), nil
	}
}

func (a aPIToolDo) FirstOrCreate() (*generated.APITool, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APITool), nil
	}
}

func (a aPIToolDo) FindByPage(offset int, limit int) (result []*generated.APITool, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aPIToolDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aPIToolDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aPIToolDo) Delete(models ...*generated.APITool) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aPIToolDo) withDO(do gen.Dao) *aPIToolDo {
	a.DO = *do.(*gen.DO)
	return a
}
