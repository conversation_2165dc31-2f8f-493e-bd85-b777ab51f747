// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newDialog(db *gorm.DB, opts ...gen.DOOption) dialog {
	_dialog := dialog{}

	_dialog.dialogDo.UseDB(db, opts...)
	_dialog.dialogDo.UseModel(&generated.Dialog{})

	tableName := _dialog.dialogDo.TableName()
	_dialog.ALL = field.NewAsterisk(tableName)
	_dialog.ID = field.NewInt64(tableName, "id")
	_dialog.AppID = field.NewString(tableName, "app_id")
	_dialog.AppName = field.NewString(tableName, "app_name")
	_dialog.ProjectID = field.NewString(tableName, "project_id")
	_dialog.ChatID = field.NewString(tableName, "chat_id")
	_dialog.User = field.NewString(tableName, "user")
	_dialog.LatestQuestionID = field.NewInt64(tableName, "latest_question_id")
	_dialog.LatestAnswerID = field.NewInt64(tableName, "latest_answer_id")
	_dialog.CreateTime = field.NewTime(tableName, "create_time")
	_dialog.UpdatedTime = field.NewTime(tableName, "updated_time")
	_dialog.CreatedType = field.NewString(tableName, "created_type")

	_dialog.fillFieldMap()

	return _dialog
}

type dialog struct {
	dialogDo

	ALL              field.Asterisk
	ID               field.Int64
	AppID            field.String
	AppName          field.String
	ProjectID        field.String
	ChatID           field.String
	User             field.String
	LatestQuestionID field.Int64  // 会话最新的提问id
	LatestAnswerID   field.Int64  // 会话最新的回答id
	CreateTime       field.Time   // 创建时间
	UpdatedTime      field.Time   // 更新时间
	CreatedType      field.String // 应用类型

	fieldMap map[string]field.Expr
}

func (d dialog) Table(newTableName string) *dialog {
	d.dialogDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dialog) As(alias string) *dialog {
	d.dialogDo.DO = *(d.dialogDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dialog) updateTableName(table string) *dialog {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.AppID = field.NewString(table, "app_id")
	d.AppName = field.NewString(table, "app_name")
	d.ProjectID = field.NewString(table, "project_id")
	d.ChatID = field.NewString(table, "chat_id")
	d.User = field.NewString(table, "user")
	d.LatestQuestionID = field.NewInt64(table, "latest_question_id")
	d.LatestAnswerID = field.NewInt64(table, "latest_answer_id")
	d.CreateTime = field.NewTime(table, "create_time")
	d.UpdatedTime = field.NewTime(table, "updated_time")
	d.CreatedType = field.NewString(table, "created_type")

	d.fillFieldMap()

	return d
}

func (d *dialog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dialog) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 11)
	d.fieldMap["id"] = d.ID
	d.fieldMap["app_id"] = d.AppID
	d.fieldMap["app_name"] = d.AppName
	d.fieldMap["project_id"] = d.ProjectID
	d.fieldMap["chat_id"] = d.ChatID
	d.fieldMap["user"] = d.User
	d.fieldMap["latest_question_id"] = d.LatestQuestionID
	d.fieldMap["latest_answer_id"] = d.LatestAnswerID
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["updated_time"] = d.UpdatedTime
	d.fieldMap["created_type"] = d.CreatedType
}

func (d dialog) clone(db *gorm.DB) dialog {
	d.dialogDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dialog) replaceDB(db *gorm.DB) dialog {
	d.dialogDo.ReplaceDB(db)
	return d
}

type dialogDo struct{ gen.DO }

type IDialogDo interface {
	gen.SubQuery
	Debug() IDialogDo
	WithContext(ctx context.Context) IDialogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDialogDo
	WriteDB() IDialogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDialogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDialogDo
	Not(conds ...gen.Condition) IDialogDo
	Or(conds ...gen.Condition) IDialogDo
	Select(conds ...field.Expr) IDialogDo
	Where(conds ...gen.Condition) IDialogDo
	Order(conds ...field.Expr) IDialogDo
	Distinct(cols ...field.Expr) IDialogDo
	Omit(cols ...field.Expr) IDialogDo
	Join(table schema.Tabler, on ...field.Expr) IDialogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDialogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDialogDo
	Group(cols ...field.Expr) IDialogDo
	Having(conds ...gen.Condition) IDialogDo
	Limit(limit int) IDialogDo
	Offset(offset int) IDialogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogDo
	Unscoped() IDialogDo
	Create(values ...*generated.Dialog) error
	CreateInBatches(values []*generated.Dialog, batchSize int) error
	Save(values ...*generated.Dialog) error
	First() (*generated.Dialog, error)
	Take() (*generated.Dialog, error)
	Last() (*generated.Dialog, error)
	Find() ([]*generated.Dialog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.Dialog, err error)
	FindInBatches(result *[]*generated.Dialog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.Dialog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDialogDo
	Assign(attrs ...field.AssignExpr) IDialogDo
	Joins(fields ...field.RelationField) IDialogDo
	Preload(fields ...field.RelationField) IDialogDo
	FirstOrInit() (*generated.Dialog, error)
	FirstOrCreate() (*generated.Dialog, error)
	FindByPage(offset int, limit int) (result []*generated.Dialog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDialogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d dialogDo) Debug() IDialogDo {
	return d.withDO(d.DO.Debug())
}

func (d dialogDo) WithContext(ctx context.Context) IDialogDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d dialogDo) ReadDB() IDialogDo {
	return d.Clauses(dbresolver.Read)
}

func (d dialogDo) WriteDB() IDialogDo {
	return d.Clauses(dbresolver.Write)
}

func (d dialogDo) Session(config *gorm.Session) IDialogDo {
	return d.withDO(d.DO.Session(config))
}

func (d dialogDo) Clauses(conds ...clause.Expression) IDialogDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d dialogDo) Returning(value interface{}, columns ...string) IDialogDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d dialogDo) Not(conds ...gen.Condition) IDialogDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d dialogDo) Or(conds ...gen.Condition) IDialogDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d dialogDo) Select(conds ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d dialogDo) Where(conds ...gen.Condition) IDialogDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d dialogDo) Order(conds ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d dialogDo) Distinct(cols ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d dialogDo) Omit(cols ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d dialogDo) Join(table schema.Tabler, on ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d dialogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDialogDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d dialogDo) RightJoin(table schema.Tabler, on ...field.Expr) IDialogDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d dialogDo) Group(cols ...field.Expr) IDialogDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d dialogDo) Having(conds ...gen.Condition) IDialogDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d dialogDo) Limit(limit int) IDialogDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d dialogDo) Offset(offset int) IDialogDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d dialogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d dialogDo) Unscoped() IDialogDo {
	return d.withDO(d.DO.Unscoped())
}

func (d dialogDo) Create(values ...*generated.Dialog) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d dialogDo) CreateInBatches(values []*generated.Dialog, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d dialogDo) Save(values ...*generated.Dialog) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d dialogDo) First() (*generated.Dialog, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.Dialog), nil
	}
}

func (d dialogDo) Take() (*generated.Dialog, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.Dialog), nil
	}
}

func (d dialogDo) Last() (*generated.Dialog, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.Dialog), nil
	}
}

func (d dialogDo) Find() ([]*generated.Dialog, error) {
	result, err := d.DO.Find()
	return result.([]*generated.Dialog), err
}

func (d dialogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.Dialog, err error) {
	buf := make([]*generated.Dialog, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d dialogDo) FindInBatches(result *[]*generated.Dialog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d dialogDo) Attrs(attrs ...field.AssignExpr) IDialogDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d dialogDo) Assign(attrs ...field.AssignExpr) IDialogDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d dialogDo) Joins(fields ...field.RelationField) IDialogDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d dialogDo) Preload(fields ...field.RelationField) IDialogDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d dialogDo) FirstOrInit() (*generated.Dialog, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.Dialog), nil
	}
}

func (d dialogDo) FirstOrCreate() (*generated.Dialog, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.Dialog), nil
	}
}

func (d dialogDo) FindByPage(offset int, limit int) (result []*generated.Dialog, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d dialogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d dialogDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d dialogDo) Delete(models ...*generated.Dialog) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *dialogDo) withDO(do gen.Dao) *dialogDo {
	d.DO = *do.(*gen.DO)
	return d
}
