package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type ChainSnapshotDAO struct {
	BaseDAO
}

func (c ChainSnapshotDAO) Save(ctx context.Context, model *generated.ChainSnapshot) (int64, error) {
	// 使用自动生成的 Save 方法插入记录
	if err := c.getQueryOrDefault().ChainSnapshot.WithContext(ctx).Save(model); err != nil {
		return 0, err
	}

	// 插入后，GORM 会自动填充 model.Id
	return model.ID, nil
}

func (c ChainSnapshotDAO) GetByID(ctx context.Context, ID int64) (*generated.ChainSnapshot, error) {
	q := c.getQueryOrDefault().ChainSnapshot
	return q.WithContext(ctx).Where(q.ID.Eq(ID)).First()
}
func (c ChainSnapshotDAO) GetAsChainDO(ctx context.Context, snapshotId string) (*models.AppletChainDO, error) {
	intValue, err := helper.CastString2Int64(snapshotId)
	if err != nil {
		return nil, err
	}
	po, err := c.GetByID(ctx, intValue)
	if err != nil {
		return nil, err
	}
	return cvt2DO(po)
}

func (c ChainSnapshotDAO) listByIDs(ctx context.Context, snapshotIds []string) ([]*generated.ChainSnapshot, error) {
	q := c.getQueryOrDefault().ChainSnapshot
	ids, err := helper.CastStrSlice2Int64(snapshotIds)
	if err != nil {
		return nil, err
	}
	return q.WithContext(ctx).Where(q.ID.In(ids...)).Find()
}

func (c ChainSnapshotDAO) ListAsChainDO(ctx context.Context, snapshotIds []string) ([]*models.AppletChainDO, error) {
	pos, err := c.listByIDs(ctx, snapshotIds)
	if err != nil {
		return nil, err
	}
	return cvt2DOs(pos)
}

// GetTriggersAsMap
// map[string][]*models.Trigger   snapshotId --> Triggers
func (c ChainSnapshotDAO) GetTriggersAsMap(ctx context.Context, snapshotIds []string) (map[string][]*models.Trigger, error) {
	pos, err := c.listByIDs(ctx, snapshotIds)
	if err != nil {
		return nil, err
	}
	return extraTriggers(pos)
}

func extraTriggers(snapshots []*generated.ChainSnapshot) (map[string][]*models.Trigger, error) {
	ret := make(map[string][]*models.Trigger)
	for _, s := range snapshots {
		chainDO, err := cvt2DO(s)
		if err != nil {
			return nil, err
		}
		base := chainDO.Base
		if base.ID == "" || base.ExperimentInfo == nil {
			continue
		}
		sid := fmt.Sprintf("%d", s.ID)
		for _, t := range base.ExperimentInfo.Triggers {
			ret[sid] = append(ret[sid], t)
		}
	}
	return ret, nil
}

func cvt2DOs(pos []*generated.ChainSnapshot) ([]*models.AppletChainDO, error) {
	ret := make([]*models.AppletChainDO, 0)
	for _, po := range pos {
		r, err := cvt2DO(po)
		if err != nil {
			return nil, err
		}
		ret = append(ret, r)
	}
	return ret, nil
}

func cvt2DO(PO *generated.ChainSnapshot) (*models.AppletChainDO, error) {
	ret := models.AppletChainDO{ChainDetail: new(widgets.Chain)}
	if err := json.Unmarshal([]byte(PO.Base), &ret.Base); err != nil {
		return nil, err
	}
	if err := json.Unmarshal([]byte(PO.ChainDetail), ret.ChainDetail); err != nil {
		return nil, err
	}
	return &ret, nil
}

func cvt2PO(svcId string, chainDO *models.AppletChainDO) (*generated.ChainSnapshot, error) {
	return &generated.ChainSnapshot{
		ChainDetail: stdsrv.AnyToString(chainDO.ChainDetail),
		Base:        stdsrv.AnyToString(chainDO.Base),
		ChainID:     chainDO.Base.ID,
	}, nil
}
