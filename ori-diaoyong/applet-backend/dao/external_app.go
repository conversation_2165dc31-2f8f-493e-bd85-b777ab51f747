package dao

import (
	"context"
	"gorm.io/gorm"

	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type ExternalAPPDB struct {
	db *gorm.DB
}

var (
	ea *ExternalAPPDB
)

func NewExternalAPPDB() *ExternalAPPDB {
	return &ExternalAPPDB{db: MustInitDB()}
}

func GetExternalAPPDB() *ExternalAPPDB {
	if ea == nil {
		ea = NewExternalAPPDB()
	}
	return ea
}

func (r *ExternalAPPDB) CreateExternalAPP(ctx context.Context, app *models.ExternalAPP) error {
	err := r.db.Create(app).Error
	if err != nil {
		return err
	}
	return app.SubmitPermission(ctx)
}

func (r *ExternalAPPDB) GetExternalAPP(id string) (*models.ExternalAPP, error) {
	var app models.ExternalAPP
	if err := r.db.Where("id = ?", id).First(&app).Error; err != nil {
		return nil, err
	}
	return &app, nil
}

func (r *ExternalAPPDB) ListExternalAPPS(filter *models.ExternalAPP) ([]*models.ExternalAPP, error) {
	apps := make([]*models.ExternalAPP, 0)
	rdb := r.db
	if filter != nil {
		rdb = rdb.Where(filter)
	}
	if err := rdb.Find(&apps).Error; err != nil {
		return nil, err
	}
	return apps, nil
}

func (r *ExternalAPPDB) UpdateExternalAPP(ctx context.Context, app *models.ExternalAPP) error {
	err := r.db.Save(app).Error
	if err != nil {
		return err
	}
	return app.SubmitPermission(ctx)
}

func (r *ExternalAPPDB) DeleteExternalAPP(id string) error {
	return r.db.Where("id = ?", id).Delete(&models.ExternalAPP{}).Error
}
