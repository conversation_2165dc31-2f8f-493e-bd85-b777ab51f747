package dao

import (
	"context"
	"fmt"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type LLMBasicConfigDAO struct {
	BaseDAO
}
type LLMBasicConfigQueryParam struct {
	ID        *string `json:"id" description:"模型唯一id"`
	ProjectID *string `json:"project_id" description:"项目id"`
}

func (l LLMBasicConfigDAO) ListAll(ctx context.Context) ([]*generated.LlmBasicConfig, error) {
	q := l.getQueryOrDefault().LlmBasicConfig
	res, err := q.WithContext(ctx).Find()
	if err != nil {
		stdlog.Errorf("list all config failed: %v", err)
	}
	return res, err
}

// GetByProjectID 根据ProjectID获取配置
func (l LLMBasicConfigDAO) GetByProjectID(ctx context.Context, ProjectId string) (*generated.LlmBasicConfig, error) {
	q := l.getQueryOrDefault().LlmBasicConfig
	res, err := q.WithContext(ctx).Where(q.ProjectID.Eq(ProjectId)).Find()
	if err != nil {
		stdlog.Errorf("get config by ProjectId failed: %v", err)
		return nil, err
	}
	if len(res) == 0 {
		err = fmt.Errorf("no such config with ProjectId %s", ProjectId)
		stdlog.Errorf("get config by ProjectId failed: %v", err)
		return nil, nil
	}
	return res[0], nil
}

// CreateOrUpdateBySpec 幂等地更新或创建配置，如果ProjectID已存在则更新，否则创建
func (l LLMBasicConfigDAO) CreateOrUpdateBySpec(ctx context.Context, customConfig *generated.LlmBasicConfig) error {
	q := l.getQueryOrDefault().LlmBasicConfig
	customConfig.ID = customConfig.ProjectID
	err := q.WithContext(ctx).Clauses(clause.OnConflict{
		UpdateAll: true,
	}).Create(customConfig)
	if err != nil {
		stdlog.Errorf("create or update config by ID failed: %v", err)
		return err
	}
	return nil
}
