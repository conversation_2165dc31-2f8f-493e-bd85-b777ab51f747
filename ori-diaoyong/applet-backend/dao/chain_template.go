package dao

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type ChainTemplateDAO struct {
	db *gorm.DB
}

func NewChainTemplateDAO() *ChainTemplateDAO {
	return &ChainTemplateDAO{db: MustInitDB()}
}

func GetChainTemplateDAO() *ChainTemplateDAO {
	return NewChainTemplateDAO()
}

// UpsertChainTemplate 创建或更新
func (r *ChainTemplateDAO) UpsertChainTemplate(ctx context.Context, entity *models.ChainTemplate) error {
	return db.Clauses(clause.OnConflict{
		UpdateAll: true, // 更新所有字段
	}).Create(entity).Error
}

// GetChainTemplate 根据id查询
func (r *ChainTemplateDAO) GetChainTemplate(ctx context.Context, entityId string) (*models.ChainTemplate, error) {
	res := new(models.ChainTemplate)
	if err := r.db.Where(&models.ChainTemplate{ID: entityId}).Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// ListChainTemplate 根据entity查询
func (r *ChainTemplateDAO) ListChainTemplate(ctx context.Context, entity *models.ChainTemplate) ([]*models.ChainTemplate, error) {
	res := make([]*models.ChainTemplate, 0)
	if err := r.db.Where(entity).Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (r *ChainTemplateDAO) ListAll(ctx context.Context) ([]*models.ChainTemplate, error) {
	res := make([]*models.ChainTemplate, 0)
	if err := r.db.Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (r *ChainTemplateDAO) DeleteChainTemplate(ctx context.Context, entityId string) error {
	res := make([]*models.ChainTemplate, 0)
	if err := r.db.Where(&models.ChainTemplate{ID: entityId}).Delete(&res).Error; err != nil {
		return err
	}
	return nil
}
