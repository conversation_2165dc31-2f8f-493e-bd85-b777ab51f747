package clients

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/aws/smithy-go/ptr"
	kpct "github.com/influxdata/kapacitor/client/v1"
	"net/http"
	"net/url"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type Kpct struct {
	httpCli      *clients.HttpClient
	KapacitorCli *kpct.Client
	host         string
}

// RegisterTickScript 注册&开启kpct引擎
func (k *Kpct) RegisterTickScript(ctx context.Context, taskID string, script string) (string, error) {
	// start to create task
	task, err := k.KapacitorCli.CreateTask(kpct.CreateTaskOptions{
		ID:         taskID,
		TICKscript: script,
		Status:     kpct.Enabled,
		Type:       kpct.StreamTask,
		DBRPs:      []kpct.DBRP{{conf.Config.Kapacitor.DBName, "autogen"}},
	})
	if err != nil {
		return "", err
	}
	return task.ID, nil
}

// DisableTickScript 停止kpct引擎
func (k *Kpct) DisableTickScript(ctx context.Context, taskID string) error {
	return k.KapacitorCli.DeleteTask(k.KapacitorCli.TaskLink(taskID))
}

// ValidTickScript 校验tick是否合理可用,主要考虑engine是否移除了某一类算子
func (k *Kpct) ValidTickScript(ctx context.Context, script string) error {
	taskId, err := k.RegisterTickScript(ctx, toolkit.NewUUID(), script)
	if err != nil {
		return stderr.Wrap(err, "register tick script failed")
	}
	return k.DisableTickScript(ctx, taskId)
}

// Execute 执行
func (k *Kpct) Execute(ctx context.Context, queryParams url.Values, APIPath *string, param *models.ChainDebugParam, evtHandler stdsrv.SSEEventHandler) error {
	api := conf.Config.Kapacitor.APIPath
	port := conf.Config.Kapacitor.ExecuteAPIPort
	prefix := fmt.Sprintf("http://%s", k.host)
	reqUrl := fmt.Sprintf("%s:%s%s", prefix, port, api)
	if APIPath != nil && *APIPath != "" {
		reqUrl = fmt.Sprintf("%s:%s%s/%s", prefix, port, api, ptr.ToString(APIPath))
	}
	reqUrl = fmt.Sprintf("%s?%s", reqUrl, queryParams.Encode())
	body, err := k.getReqBodyFromWidget(param.RunParam)
	if err != nil {
		return stderr.Wrap(err, "get req body from widget err,param :%v", param)
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqUrl, bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	tk, err := helper.GetToken(ctx)
	if err != nil {
		return stderr.Wrap(err, "get user token")
	}
	req.Header.Set(auth.TokenHeader, tk)
	stdlog.Infof("execute kapacitor task,url :%v,param :%v", reqUrl, string(body))
	resp, err := k.httpCli.Cli.Do(req)
	if err != nil {
		return stderr.Wrap(err, "kapacitor task execute err")
	}
	defer resp.Body.Close()
	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		return fmt.Errorf("illegal response: [%d] -> %s", resp.StatusCode, resp.Status)
	}
	param.EventHandler = evtHandler
	// 使用 SSEReadEvent 进行流式处理
	if err = stdsrv.SSEReadEvent(ctx, bufio.NewReader(resp.Body), evtHandler); err != nil {
		return fmt.Errorf("error processing SSE events: %w", err)
	}

	return nil
}

func (k *Kpct) getReqBodyFromWidget(params models.WidgetParams) ([]byte, error) {
	if len(params) == 0 {
		return nil, stderr.Internal.Error("invalid param :%v", params)
	}
	paramMap := params.ToChainExecuteParam()
	data, err := json.Marshal(paramMap)
	if err != nil {
		return nil, stderr.Wrap(err, "getReqBodyFromWidget : marshal query value :%v err", paramMap)
	}
	return data, nil
}
