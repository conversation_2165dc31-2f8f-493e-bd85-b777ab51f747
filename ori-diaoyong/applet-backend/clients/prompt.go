package clients

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	APIGetTemplate            = "scenes/-/templates"
	PromptKeyApplicationChain = "application_chain"
)

type PromptClient struct {
	httpCli *clients.HttpClient
	cfg     *conf.PromptClientCfg
}

func (p PromptClient) ListChainPrompts(ctx context.Context) ([]*models.PromptTemplate, error) {
	res := make([]*models.PromptTemplate, 0)
	baseURL := "api/prompt/"
	url := fmt.Sprintf("http://%s:%s/%s%s", p.cfg.Host, p.cfg.Port, baseURL, APIGetTemplate)
	stdlog.Infof("base url :%v", url)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return res, nil
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return res, err
	}
	// req.Header.Set("Authorization", p.cfg.Auth)
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/json")

	q := req.URL.Query()
	// q.Add("published", "true")
	project := helper.GetProjectID(ctx)
	// todo 删除这段逻辑
	if project == "" {
		project = "9bb6bece-6d01-4e2c-a265-399200268720"
	}

	if project != "" {
		q.Add("project_id", project)
	}
	req.URL.RawQuery = q.Encode()

	resp, err := p.httpCli.Cli.Do(req)
	if err != nil {
		return res, stderr.Wrap(err, "request url :%v", url)
	}
	defer resp.Body.Close()
	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		return nil, fmt.Errorf("illegal response: [%d] -> %s", resp.StatusCode, resp.Status)
	}
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body err")
	}
	allPrompts := make([]*models.PromptTemplate, 0)
	if err := json.Unmarshal(data, &allPrompts); err != nil {
		return nil, stderr.Wrap(err, "unmarshal response body err")
	}
	for _, p := range allPrompts {
		if p.Published || p.Source == PromptKeyApplicationChain {
			res = append(res, p)
		}
	}
	return res, nil
}
