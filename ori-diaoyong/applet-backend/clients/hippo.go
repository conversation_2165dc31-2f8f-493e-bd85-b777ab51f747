package clients

import (
	"context"
	"fmt"
	"sync"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/hippo-go/v1"
)

var (
	hippoCliStore       = make(map[string]*hippo.HippoClient)
	getProjHippoCliLock sync.Mutex
)

// initHippoCli 初始化全局HippoCli
// func initHippoCli() (err error) {
// 	cfg := conf.Config.HippoConfig
// 	dbCfg := &hippo.VectorDBConfig{
// 		ClientID:        "llmops-kb",
// 		Address:         fmt.Sprintf("%s:%s", conf.Config.HippoConfig.Address, conf.Config.HippoConfig.Port),
// 		UserName:        cfg.UserName,
// 		UserPassword:    cfg.Password,
// 		Database:        cfg.Database,
// 		Table:           "",
// 		Partition:       "",
// 		MetricType:      cfg.DefaultMetricType,
// 		IndexType:       cfg.DefaultIndexType,
// 		AutoCreateTable: false,
// 	}

// 	ctx := context.Background()
// 	cli, err := NewHippoCli(ctx, dbCfg, true)
// 	if err == nil {
// 		HippoCli = cli
// 	}
// 	return err
// }

func GetProjHippoCli(ctx context.Context, projectId string) (*hippo.HippoClient, error) {
	getProjHippoCliLock.Lock()
	defer getProjHippoCliLock.Unlock()

	if cli, ok := hippoCliStore[projectId]; ok {
		return cli, nil
	}

	// new client
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	addr, err := helper.GetProjHippoAddr(projectId, token)
	if err != nil {
		return nil, err
	}

	cfg := conf.Config.HippoConfig
	dbCfg := &hippo.VectorDBConfig{
		ClientID:        "llmops-kb",
		Address:         fmt.Sprintf("%s:%s", addr, conf.Config.HippoConfig.Port),
		UserName:        cfg.UserName,
		UserPassword:    cfg.Password,
		Database:        cfg.Database,
		Table:           "",
		Partition:       "",
		MetricType:      cfg.DefaultMetricType,
		IndexType:       cfg.DefaultIndexType,
		AutoCreateTable: false,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	cli, err := NewHippoCli(ctx, dbCfg, true)
	if err == nil {
		hippoCliStore[projectId] = cli
	}
	return cli, err
}

func GetDataConnectionHippoCli(ctx context.Context, projectId string, conn *pb.DataConnection) (*hippo.HippoClient, error) {
	getProjHippoCliLock.Lock()
	if conn == nil || conn.Id == "" {
		getProjHippoCliLock.Unlock()
		return GetProjHippoCli(ctx, projectId)
	}

	cfg := conf.Config.HippoConfig
	dbCfg := &hippo.VectorDBConfig{
		ClientID:        "llmops-kb",
		Address:         fmt.Sprintf("%s:%s", conn.Address, conn.Port),
		UserName:        conn.Username,
		UserPassword:    conn.Password,
		Database:        conn.Database,
		Table:           "",
		Partition:       "",
		MetricType:      cfg.DefaultMetricType,
		IndexType:       cfg.DefaultIndexType,
		AutoCreateTable: false,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	cli, err := NewHippoCli(ctx, dbCfg, true)
	getProjHippoCliLock.Unlock()
	return cli, err
}

func NewHippoCli(ctx context.Context, cfg *hippo.VectorDBConfig, autoCreateDB bool) (*hippo.HippoClient, error) {
	cli, err := hippo.NewHippoDBCli(ctx, *cfg)
	if err != nil {
		return nil, err
	}

	listRsp, err := cli.ListDatabases(ctx)
	if err != nil {
		return nil, err
	}
	allDBs := map[string]struct{}{}
	for _, v := range listRsp.Databases {
		allDBs[v.Name] = struct{}{}
	}
	if _, ok := allDBs[cfg.Database]; !ok {
		if !autoCreateDB {
			return nil, fmt.Errorf("NewHippoCli: database %s not exsits, conn: %v", cfg.Database, cfg)
		}
		stdlog.Infof("NewHippoCli: database %s not exsits, creating", cfg.Database)
		createRsp, err := cli.CreateDatabase(ctx, cfg.Database)
		if err != nil {
			return nil, err
		}
		stdlog.Infof("NewHippoCli: create db rsp %v", createRsp)
	}
	return cli, nil
}
