package clients

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	ContentTypeJSON = "application/json"
)

// CSMClient 代码空间调用-客户端
type CSMClient struct {
	httpCli *clients.HttpClient
	cfg     *conf.CSMClientCfg
}

func (c CSMClient) httpRpcCall(ctx context.Context, method string, fullUrl string, body io.Reader) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", ContentTypeJSON)
	resp, err := c.httpCli.Cli.Do(req)
	if err != nil {
		return nil, stderr.Wrap(err, "request url :%v", fullUrl)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("illegal response: [%d] -> %s", resp.StatusCode, resp.Status)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body err")
	}
	return data, nil
}
