package clients

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"io"
	"net/http"
	"strconv"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

type CVATClient struct {
	DataConnClient pb.DataConnectionManagerClient
	httpCli        *clients.HttpClient
}

const (
	DATA_CONNECTION_PATH = "/api/data/connections"
)

type DataConnectionList struct {
	Result []*pb.DataConnection `json:"result"`
}

func (c *CVATClient) initClient() error {
	cvat := conf.Config.CVATConfig
	grpcConfig := conf.Config.GrpcConfig
	creds := insecure.NewCredentials()
	conn, err := grpc.Dial(cvat.Host+":"+cvat.GrpcPort, grpc.WithTransportCredentials(creds), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(grpcConfig.GetMaxMessageMB())))
	if err != nil {
		return stderr.Wrap(err, "can not connected to cvat")
	}
	stdlog.Infof("connected to cvat successfully")
	c.DataConnClient = pb.NewDataConnectionManagerClient(conn)
	c.httpCli = HttpCli
	return nil
}

func (c *CVATClient) ListDataConnections(ctx context.Context, type_selector pb.ConnectionType, project_id string, tenant_id string) (*DataConnectionList, error) {
	var dataConnectionList DataConnectionList

	urlStr := fmt.Sprintf("http://%s:%s%s", conf.Config.CVATConfig.Host, conf.Config.CVATConfig.HttpPort, DATA_CONNECTION_PATH)

	token, err := helper.GetToken(ctx)
	if err != nil {
		stdlog.Errorln("Unable to get the token")
		return &dataConnectionList, err
	}

	HeaderMap := map[string]string{
		"Authorization": token,
		"Content-Type":  ContentTypeJSON,
	}

	QueryMap := map[string]string{
		"project_id":      project_id,
		"tenant_id":       tenant_id,
		"type_selector":   strconv.Itoa(int(type_selector)),
		"status_selector": strconv.Itoa(1),
	}

	param := &clients.HttpParam{
		Method:     http.MethodGet,
		Url:        urlStr,
		Header:     HeaderMap,
		QueryParam: QueryMap,
	}

	resInString, err := HttpCli.HttpCallString(ctx, param)

	if err != nil {
		return nil, stderr.Wrap(err, "HttpCallString 报错")
	}

	err = stdsrv.UnmarshalMixWithProto(resInString, &dataConnectionList)

	if err != nil {
		stdlog.Errorln("unable to Unmarshal response string into self-defined struct 'Reponse'.")
		return nil, err
	}

	return &dataConnectionList, nil
}

func (c *CVATClient) HttpRpcCall(ctx context.Context, method string, fullUrl string, body io.Reader) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", ContentTypeJSON)
	resp, err := c.httpCli.Cli.Do(req)
	if err != nil {
		return nil, stderr.Wrap(err, "request url :%v", fullUrl)
	}
	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("illegal response: [%d] -> %s, response: %s", resp.StatusCode, resp.Status, string(data))
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body err")
	}
	return data, nil
}

func (c *CVATClient) HttpForwardGet(ctx context.Context, fullUrl string, w http.ResponseWriter) error {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fullUrl, nil)
	token, err := helper.GetToken(ctx)
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", ContentTypeJSON)
	resp, err := c.httpCli.Cli.Do(req)
	if err != nil {
		return stderr.Wrap(err, "request url :%v", fullUrl)
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("illegal response: [%d] -> %s", resp.StatusCode, resp.Status)
	}
	defer resp.Body.Close()
	// 设置响应头
	// 复制内容类型
	contentType := resp.Header.Get("Content-Type")
	if contentType != "" {
		w.Header().Set("Content-Type", contentType)
	} else {
		// 默认设为octet-stream
		w.Header().Set("Content-Type", "application/octet-stream")
	}

	// 复制内容长度
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		w.Header().Set("Content-Length", contentLength)
	}

	// 设置文件名，优先从源响应获取，否则使用默认名
	filename := getFilenameFromHeader(resp)
	if filename != "" {
		w.Header().Set("Content-Disposition", "attachment; filename=\""+filename+"\"")
	}

	// 复制其他有用的头信息
	for _, header := range []string{"Content-Encoding", "Cache-Control", "ETag"} {
		if value := resp.Header.Get(header); value != "" {
			w.Header().Set(header, value)
		}
	}

	// 转发文件内容
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		// 注意：此时已经开始发送响应，无法再发送错误状态
		stdlog.WithError(err).Errorf("传输文件时出错: %v", err)
	}
	return nil
}

func getFilenameFromHeader(resp *http.Response) string {
	// 尝试从Content-Disposition头中提取
	if cd := resp.Header.Get("Content-Disposition"); cd != "" {
		if filename := extractFilenameFromContentDisposition(cd); filename != "" {
			return filename
		}
	}

	// 可以添加更多的提取逻辑，例如从URL路径中提取
	// ...

	return "exported_file" // 默认文件名
}

func extractFilenameFromContentDisposition(cd string) string {
	// 非常简化的实现，仅用于示例
	// 实际项目可能需要正则表达式和处理引号等
	const filenamePrefix = "filename="
	if idx := strings.Index(cd, filenamePrefix); idx >= 0 {
		filename := cd[idx+len(filenamePrefix):]
		// 移除引号
		filename = strings.Trim(filename, "\"")
		return filename
	}
	return ""
}
