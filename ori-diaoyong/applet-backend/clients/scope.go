package clients

import (
	"context"
	"fmt"

	"github.com/elastic/go-elasticsearch/v6"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

var (
	scopeCliStore          = make(map[string]*elasticsearch.Client)
	getProjScopeCliLockMap = stdsync.NewLockMap()
)

func GetDataConnectionScopeCli(ctx context.Context, projectId string, conn *pb.DataConnection) (*elasticsearch.Client, error) {
	l := getProjScopeCliLockMap.Get(projectId)
	l.Lock()
	if conn == nil || conn.Id == "" {
		l.Unlock()
		return GetProjScopeCli(ctx, projectId)
	}

	addr := fmt.Sprintf("http://%s:%s", conn.Address, conn.Port)

	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{
			addr,
		},
		Username: conn.Username,
		Password: conn.Password,
	})
	if err != nil {
		return nil, err
	}
	stdlog.Infof("addr: %s, es version: %s", addr, elasticsearch.Version)
	stdlog.Info(es.Info())
	l.Unlock()
	return es, nil
}

func GetProjScopeCli(ctx context.Context, projectId string) (*elasticsearch.Client, error) {
	l := getProjScopeCliLockMap.Get(projectId)
	l.Lock()
	defer l.Unlock()
	if cli, ok := scopeCliStore[projectId]; ok {
		return cli, nil
	}

	// new client
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	addr, err := helper.GetProjHippoAddr(projectId, token)
	if err != nil {
		return nil, err
	}
	addr = fmt.Sprintf("http://%s:%s", addr, conf.Config.ScopeConfig.Port)

	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{
			addr,
		},
		Username: conf.Config.ScopeConfig.UserName,
		Password: conf.Config.ScopeConfig.Password,
	})
	if err != nil {
		return nil, err
	}
	stdlog.Infof("project: %s, es version: %s", projectId, elasticsearch.Version)
	stdlog.Info(es.Info())
	scopeCliStore[projectId] = es
	return es, nil
}
