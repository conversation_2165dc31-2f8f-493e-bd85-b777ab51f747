package clients

import (
	"context"
	"github.com/spf13/viper"
	"testing"
	"transwarp.io/applied-ai/applet-backend/conf"
)

func TestListPrompt(t *testing.T) {
	ctx := context.Background()
	viper.AddConfigPath("../etc/")
	viper.SetConfigName("app")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		t.<PERSON>al(err)
	}
	if err := viper.Unmarshal(&conf.Config, conf.DecConfig); err != nil {
		t.Fatal(err)
	}
	initHttpCli()
	initPromptClient()
	res, err := PromptCli.ListChainPrompts(ctx)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}
