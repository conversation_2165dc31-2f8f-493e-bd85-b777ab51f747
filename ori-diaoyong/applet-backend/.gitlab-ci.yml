image: harbor.transwarp.io/aip/base/golang-gitlab-builder:1.23-ubuntu24.04

stages:
  - build
  - deploy
  - trigger
# 具体说明见http://gitlab.transwarp.io/applied-ai/aiot/common-ci项目的CI文件
include:
  #  编译构建, 根据Dockerfile.x86_64和Dockerfile。arm_64分别构建golang项目的镜像，并推送到harbor。非branch、master、dev的功能分支不会推送镜像。可配置参数（gitlab项目的环境变量）：
  #  BEFORE_SCRIPT_COMMAND 期望额外在before_script job执行的命令
  - project: applied-ai/aiot/common-ci
    file: go-build.yml
    # 部署CI，可配置参数（gitlab项目的环境变量）：
    # AUTOCV_SSH_USER 部署主机的用户
    # AUTOCV_SSH_ADDR 部署主机的地址
    # DEPLOY_NAME     pod的部署名称
  - project: applied-ai/aiot/common-ci
    file: deploy.yml


# 触发applet-engine的ci过程
trigger-applet-engine:
  stage: trigger
  only:
    - dev
    - tags
    - master
    - /^branch-.*$/
    - /^llm-.*$/
  script:
    - "curl -X POST --fail -F token=${TRIGGER_TOKEN} -F ref=${CI_COMMIT_REF_NAME} http://gitlab.transwarp.io/api/v4/projects/16048/trigger/pipeline"
  tags:
    - k8s