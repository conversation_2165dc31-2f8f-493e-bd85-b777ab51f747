package scripts

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

var (
	hc  = new(http.Client)
	ctx = helper.NewContextWithBaseInfo(context.Background(), "llmops-yx", "zjs-test",
		"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ")
)

// TestGenTemplateFile 读取固定空间应用链生成整个模板文件
func TestGenTemplateFile(t *testing.T) {
	listSimpleUrl := "http://**************:30816/api/v1/app/applications?project_id=zjs-test&tenantId=llmops-yx"
	detailUrl := "http://**************:30816/api/v1/applet/chains/{}?project_id=zjs-test&tenantId=llmops-yx"
	//scriptlUrl := "http://**************:30816/api/v1/applet/chains/{}/script?project_id=assets&tenantId=dev-assets"
	req, _ := helper.GetHttpReq(ctx, http.MethodGet, listSimpleUrl, nil, true)
	helper.HttpCallForSync(hc, req, func(result string) error {
		simples := make([]*models.AppletChainBaseDO, 0)
		json.Unmarshal([]byte(result), &simples)
		for _, s := range simples {
			url := strings.ReplaceAll(detailUrl, "{}", s.ID)
			req, _ := helper.GetHttpReq(ctx, http.MethodGet, url, nil, true)
			helper.HttpCallForSync(hc, req, func(result string) error {
				rs := new(models.AppletChainDO)
				json.Unmarshal([]byte(result), &rs)
				chainTemplate := new(models.ChainTemplate)
				chainTemplate.ID = rs.Base.Name + ".json"
				chainTemplate.Name = rs.Base.Name
				chainTemplate.Desc = rs.Base.Desc
				chainTemplate.Template = rs.ChainDetail
				// TOOD能正常生成tick
				// 将对象转换为格式化的 JSON
				prettyJSON, _ := json.MarshalIndent(chainTemplate, "", "  ")
				// 将 JSON 写入文件
				os.WriteFile("files/"+chainTemplate.ID, prettyJSON, 0644)
				return nil
			})
		}
		return nil
	})
	stdlog.Info("success")
}

// TestRegisterTemplate 依据模板文件生成应用链到固定空间
func TestRegisterTemplate(t *testing.T) {
	postUrl := "http://172.17.120.207:31057/api/v1/applet/chains?project_id=zjs-test&tenantId=llmops-yx"
	patchUrl := "http://172.17.120.207:31057/api/v1/applet/chains/{}/node-info?project_id=zjs-test&tenantId=llmops-yx"
	srcPath := "../en/"
	files, err := os.ReadDir(srcPath)
	if err != nil {
		stdlog.Info(err)
	}
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		path := srcPath + f.Name()
		ct := new(models.ChainTemplate)
		if err := helper.ReadFileToStruct(path, ct); err != nil {
			stdlog.Info(err)
		}
		if ct.ID != "" {
			params := &models.CreatAppletChainParam{
				Name: ct.Name,
				Desc: ct.Desc,
			}
			req, _ := helper.GetHttpReq(ctx, http.MethodPost, postUrl, params, true)
			helper.HttpCallForSync(hc, req, func(result string) error {
				ID := new(helper.ID)
				json.Unmarshal([]byte(result), ID)
				url := strings.ReplaceAll(patchUrl, "{}", ID.ID)
				req, _ := helper.GetHttpReq(ctx, http.MethodPatch, url, ct.Template, true)
				err := helper.HttpCallForSync(hc, req, func(result string) error {
					stdlog.Info(result)
					return nil
				})
				if err != nil {
					stdlog.Info(f.Name(), err)
				}
				return nil
			})
		}
	}
	stdlog.Info("successfully")
}
func TestUpgradeChainDetail(t *testing.T) {
	widgets.Init()
	factory := widgets.WidgetFactoryImpl
	files, err := os.ReadDir("../")
	if err != nil {
		stdlog.Info(err)
	}
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		path := "../" + f.Name()
		ct := new(models.ChainTemplate)
		if err := helper.ReadFileToStruct(path, ct); err != nil {
			stdlog.Info(err)
		}
		if ct.ID == "" {
			continue
		}

		// 更新算子版本
		if err := upgradeChainDetail(factory, ct.Template); err != nil {
			stdlog.Info(err)
		}

		// TOOD能正常生成tick
		// 将对象转换为格式化的 JSON
		prettyJSON, _ := json.MarshalIndent(ct, "", "  ")
		// 将 JSON 写入文件
		os.WriteFile("../"+f.Name(), prettyJSON, 0644)
	}
}

// upgradeChainDetail
// 根据widgetKey,对应用链中算子定义进行部分更新
func upgradeChainDetail(widgetFactory widgets.IWidgetFactory, chainDetail *widgets.Chain) error {
	if chainDetail == nil {
		return stderr.Errorf("chain detail is nil")
	}
	// nodes存储的不是指针,需要注意一下
	for index, _ := range chainDetail.Nodes {
		nodePtr := &chainDetail.Nodes[index]
		uiMap := make(map[string]any)
		json.Unmarshal([]byte(nodePtr.UI), &uiMap)
		delete(uiMap, "data")
		nodePtr.UI = stdsrv.AnyToString(uiMap)

		widgetDetail := nodePtr.WidgetDetail
		if nodePtr.WidgetDetail == nil {
			return stderr.Errorf("widget detail is nil")
		}
		widgetKey := widgetDetail.Id
		standWidget, err := widgetFactory.GetWidget(widgetKey)
		if err != nil {
			return err
		}
		standWidgetDetail := standWidget.Define()

		if !standWidgetDetail.DynamicEndPoint {
			nodePtr.WidgetDetail = standWidgetDetail
			continue
		}

		widgetDetail.Name = standWidgetDetail.Name
		widgetDetail.Desc = standWidgetDetail.Desc
		widgetDetail.Group = standWidgetDetail.Group
		widgetDetail.DynamicEndPoint = standWidgetDetail.DynamicEndPoint

		for index, _ := range widgetDetail.Params {
			p := &widgetDetail.Params[index]
			if p.ParamLimits == nil {
				continue
			}
			if p.Category == widgets.ParamTypeNodeInPort || p.Category == widgets.ParamTypeNodeOutPort {
				p.ParamLimits = widgets.AnyAnyLimits()
			}
		}
	}
	return nil
}

func TestTranslateTemplates(t *testing.T) {
	start := time.Now()
	modelServices, ctx, err := GetModelAndCtx()
	if err != nil {
		stdlog.Info("get model field")
		return
	}
	var model *pb.ModelService
	for _, m := range modelServices.Services {
		if m.Name == "qwen2-72b-instruct-on-llm11" {
			model = m
			break
		}
	}
	dirPath := "../zh/"
	destPath := "../en/"
	files, err := os.ReadDir(dirPath)
	if err != nil {
		stdlog.Info(err)
	}
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		path := dirPath + f.Name()
		ct := new(models.ChainTemplate)
		if err := helper.ReadFileToStruct(path, ct); err != nil {
			stdlog.Info(err)
		}
		if ct.ID == "" {
			continue
		}

		tasks := make([]func() error, 0)
		tasks = append(tasks, CreateTranslateTask(ctx, model, &ct.Name, true))
		tasks = append(tasks, CreateTranslateTask(ctx, model, &ct.Desc, false))
		for index, _ := range ct.Template.Nodes {
			nodePtr := &ct.Template.Nodes[index]
			tasks = append(tasks, CreateTranslateTask(ctx, model, &nodePtr.Name, true))

			widgetDetail := nodePtr.WidgetDetail
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widgetDetail.Name, true))
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widgetDetail.Desc, false))

			for index, _ := range widgetDetail.Params {
				paramPtr := &widgetDetail.Params[index]
				if paramPtr.Category == widgets.ParamTypeNodeOutPort && paramPtr.Define.Name == "" {
					paramPtr.Define.Name = "输出"
				}
				tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.Define.Name, true))
				tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.Define.Desc, false))
			}
		}

		err := helper.SyncRunTasks(tasks)
		if err != nil {
			stdlog.Info(err)
		}
		// 将对象转换为格式化的 JSON
		prettyJSON, _ := json.MarshalIndent(ct, "", "  ")
		// 将 JSON 写入文件
		os.WriteFile(destPath+f.Name(), prettyJSON, 0644)
	}

	elapsed := time.Since(start)
	stdlog.Infof("cost %v to do translate", elapsed)
}

func GetModelAndCtx() (*pb.ReadModelServiceRsp, context.Context, error) {
	ctx := helper.NewContextWithBaseInfo(context.Background(), "llmops-assets-997", "assets",
		"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ")
	callConfig := &clients.CallConfig{
		Method: http.MethodGet,
		FullInferUrl: "https://172.17.120.207:30745/llm/dev/tenants/dev-assets/gateway/mw/api/v1/mwh/svcmgr/services?" +
			//FullInferUrl: "https://**************:30443/llm/llmops/tenants/llmops-assets/gateway/mw/api/v1/mwh/svcmgr/services?" +
			"with_remote_service=true&only_running=true&with_asset=false&is_mw_mode=true" +
			"&is_seldon_mode=true&project_id=assets&tenantId=dev-assets",
		Headers:  map[string]string{"Cookie": "SOPHONID=1ec99d3c2ccfa531fe5eb99ebe0bfcae"},
		InferReq: &triton.RerankReq{},
	}
	modelServices := new(pb.ReadModelServiceRsp)
	err := new(clients.HttpCallHelper).SyncCall(ctx, callConfig, func(result string) error {
		return stdsrv.UnmarshalMixWithProto(result, modelServices)
	})
	return modelServices, ctx, err
}

func CreateTranslateTask(ctx context.Context, model *pb.ModelService, text *string, capitalizeFirChar bool) func() error {
	return func() error {
		str := *text
		if strings.TrimSpace(str) == "" {
			return nil
		}

		additionalRule := ""
		if capitalizeFirChar {
			additionalRule = "6. 正在翻译名称，确保输出的英文每个单词首字母都是大写"
		}

		req := &triton.OpenAiChatReq{
			Temperature: 0.01,
			Messages: []triton.MultimodalMessageItem{
				{
					Role: "user",
					Content: fmt.Sprintf(`
您是一个翻译专家，擅长将用户给定的文档翻译成英文版本。请按以下规则处理:
1. 完整保留原始文档格式（如Markdown的#标题/*列表/链接/代码块等）
2. 仅翻译自然语言文本,保留数字、URL、专有名词等
3. 输入文本可能是包含"输入、输出"这样的中文字符串,将其翻译为对应的英文
4. 输入文本可能存在中英文混合的情况,将其中的中文部分翻译为英文
5. 输出时仅保留翻译结果即可,不要有额外的解释
%s
请对以下文本进行详细翻译:
%s
`, additionalRule, str),
				},
			},
		}
		return clients.SyncChat(ctx, model, req, func(textContent string) error {
			*text = textContent
			return nil
		})
	}
}
