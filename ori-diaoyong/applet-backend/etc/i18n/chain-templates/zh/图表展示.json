{"id": "图表展示.json", "name": "图表展示", "desc": "展示了目前构建对话应用时，平台组件支持的各种富文本的Markdown语法插件，支持构建丰富样式的应用。", "template_group_key": "ToolsCall", "template": {"nodes": [{"id": "d66502c5-826a-4c17-917e-5f667710d206", "name": "文件上传", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "文件上传", "desc": "用于加载上传的单个文件, 输出SFSFile类型数据", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "上传文件", "desc": "上传文件,支持类型: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]。", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "读取文件内容", "desc": "是否读取文件内容，开启后会读取文件内容，向下游节点传递文件字节流；否则只会传递文件路径", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "最大文件大小(MB)", "desc": "允许上传的最大文件大小，单位为MB", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "允许的文件扩展名", "desc": "允许上传的文件扩展名列表，每行只允许输入一个扩展名，可输入*、txt、pdf、docx等，当输入 * 或者未输入任何扩展名时表示允许上传所有格式的文件", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":441,\"id\":\"d66502c5-826a-4c17-917e-5f667710d206\",\"position\":{\"x\":191,\"y\":118},\"positionAbsolute\":{\"x\":191,\"y\":118},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["jpg", "png"], "IsFileContentRead": true, "MaxFileSizeMB": 20}, "sub_chain_base_info": null}, {"id": "912cf276-9a08-4f90-b269-4d4a9cf0b583", "name": "Python代码", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python代码", "desc": "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用python代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python代码", "desc": "python代码，点击可查看或编辑代码", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "python代码中handler函数返回的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"912cf276-9a08-4f90-b269-4d4a9cf0b583\",\"position\":{\"x\":714,\"y\":257},\"positionAbsolute\":{\"x\":714,\"y\":257},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\nimport mimetypes\n\ndef get_mime_type(file_path):\n    mime_type, _ = mimetypes.guess_type(file_path)\n    return mime_type\n\nimport base64\ndef is_image_mime(mime_type):\n    if mime_type:\n        return mime_type.startswith(\"image/\")\n    return False\n\n\n# 说明：被以下符号包裹的文本内容，将以代码块形式展示\n# 1. ```\n# ```\n# Hello World!\n# ```\n#\n# 2. ~~~\n# ~~~\n# Hello World!\n# ~~~\n#\n# 3.由`换行符号 + 四个空格`开始， 由`三个空格结束`， 中间内容带`四个空格`前缀\n# \"\\n    开始\\n    Hello World \\n     Hello World \\n      Hello World \\n   结束\"\n#\n# 4. HTML标签：由` <pre><code> `开始，由` </code></pre> `结束\n# <pre>\n# <code>\n#     Hello World\n# <\\code>\n# <\\pre>\n\ndef handler(data):\n    fname=data['name'] # 上传文件的名称\n    furl=data['url'] # 上传文件的内部sfs路径地址\n    fcontent=data['content'] # 上传文件的内容（base64编码格式）\n    mime_type = get_mime_type(data['url'])\n    if not is_image_mime(mime_type):\n        return \"请上传图片以查看完整示例效果\"\n\n    return \"\"\"\n输出样式示例：\n\n\"\"\" + f\"\"\"\n\n# 1. 附件下载\n一般需要为 sfs:///tenants/`<tid>`/projs/`<pid>`/<filepath> 格式的地址\ne.g.  \n  下载链接： [Link：{fname}]({furl})\n\n\"\"\" + \"\"\"\n\n# 2. HTML 标签\n## 2.1 IFrame 嵌入\n<iframe src=\"https://www.transwarp.cn/\" width=\"100%\" height=\"200px\"></iframe>\n\n## 2.2 表格嵌入\n<table><tbody><thead><th></th><th>董事会秘书</th><th>证券事务代表</th></thead><tr><td>姓名</td><td>张三</td><td>李四,王五</td></tr><tr><td>联系地址</td><td>中国自由贸易试验区</td><td>中国自由贸易试验区</td></tr><tr><td>电话</td><td>0000-1234567</td><td>0000-1234567</td></tr><tr><td>传真</td><td>0000-1234567</td><td>0000-1234567</td></tr><tr><td>电子信箱</td><td><EMAIL></td><td><EMAIL>; wang.wu@jonhon. cn</td></tr></tbody></table>\n\n\"\"\" + \"\"\"\n\n# 3. Mermaid 格式\n\n## 3.1 流程图\n```mermaid\nflowchart LR\nA[Hard] -->|Text| B(Round)\nB --> C{Decision}\nC -->|One| D[Result 1]\nC -->|Two| E[Result 2]\n```\n\n## 3.2 实体关系图\n```mermaid\nerDiagram\n          CUSTOMER }|..|{ DELIVERY-ADDRESS : has\n          CUSTOMER ||--o{ ORDER : places\n          CUSTOMER ||--o{ INVOICE : \"liable for\"\n          DELIVERY-ADDRESS ||--o{ ORDER : receives\n          INVOICE ||--|{ ORDER : covers\n          ORDER ||--|{ ORDER-ITEM : includes\n          PRODUCT-CATEGORY ||--|{ PRODUCT : contains\n          PRODUCT ||--o{ ORDER-ITEM : \"ordered in\"\n```\n\n## 3.3 饼图\n```mermaid\npie showData\n    title Key elements in Product X\n    \"Calcium\" : 42.96\n    \"Potassium\" : 50.05\n    \"Magnesium\" : 10.01\n    \"Iron\" :  5\n```\n## 3.4 网络拓扑图\n```relation-graph\n{\n    \"data\": {\n        \"nodes\": [\n            {\n                \"text\": \"采集管理中心\",\n                \"id\": \"centre\",\n                \"nodeShape\": 0,\n                \"width\": 120,\n                \"height\": 120,\n                \"offset_y\": -20,\n                \"color\": \"#34A0CE\"\n            },\n            {\n                \"text\": \"《数据收集服务》<br> 2355条/分钟\",\n                \"id\": \"data\",\n                \"nodeShape\": 1,\n                \"width\": 230,\n                \"height\": 50,\n                \"fixed\": true,\n                \"x\": -330,\n                \"y\": -200\n            },\n            {\n                \"text\": \"《代理池服务》<br> 今日剩余：32020个\",\n                \"id\": \"proxy\",\n                \"nodeShape\": 1,\n                \"width\": 230,\n                \"height\": 50,\n                \"fixed\": true,\n                \"x\": -330,\n                \"y\": -120\n            },\n            {\n                \"text\": \"《采集任务分发服务》<br> 112个/分钟\",\n                \"id\": \"task\",\n                \"nodeShape\": 1,\n                \"width\": 230,\n                \"height\": 50,\n                \"fixed\": true,\n                \"x\": -330,\n                \"y\": -40,\n                \"color\": \"#E6A23C\"\n            },\n            {\n                \"text\": \"程序调度服务\",\n                \"id\": \"cron\",\n                \"nodeShape\": 1,\n                \"width\": 130,\n                \"height\": 40,\n                \"color\": \"#34A0CE\"\n            },\n            {\n                \"text\": \"ExeSvr-01@10.0.0.201 | 最后响应：3秒前\",\n                \"id\": \"exe-01\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-02@10.0.0.202 | 最后响应：5秒前\",\n                \"id\": \"exe-02\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-03@10.0.0.203 | 最后响应：2秒前\",\n                \"id\": \"exe-03\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-04@10.0.0.204 | 最后响应：4分钟前\",\n                \"id\": \"exe-04\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35,\n                \"color\": \"#34A0CE\"\n            },\n            {\n                \"text\": \"ExeSvr-05@10.0.0.205 | 最后响应：2分钟前\",\n                \"id\": \"exe-05\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35,\n                \"color\": \"#34A0CE\"\n            },\n            {\n                \"text\": \"ExeSvr-06@10.0.0.206 | 最后响应：3秒前\",\n                \"id\": \"exe-06\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-07@10.0.0.207 | 最后响应：3秒前\",\n                \"id\": \"exe-07\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-docker-01@10.0.0.211 | 最后响应：17天前\",\n                \"id\": \"exe-08\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35,\n                \"color\": \"#F56C6C\"\n            },\n            {\n                \"text\": \"ExeSvr-docker-02@10.0.0.211 | 最后响应：3秒前\",\n                \"id\": \"exe-09\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            },\n            {\n                \"text\": \"ExeSvr-docker-03@10.0.0.211 | 最后响应：3秒前\",\n                \"id\": \"exe-10\",\n                \"nodeShape\": 1,\n                \"width\": 400,\n                \"height\": 35\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"data\",\n                \"to\": \"centre\",\n                \"text\": null,\n                \"isHideArrow\": true,\n                \"lineShape\": 4\n            },\n            {\n                \"from\": \"proxy\",\n                \"to\": \"centre\",\n                \"text\": null,\n                \"isHideArrow\": true,\n                \"lineShape\": 4\n            },\n            {\n                \"from\": \"task\",\n                \"to\": \"centre\",\n                \"text\": null,\n                \"isHideArrow\": true,\n                \"lineShape\": 4\n            },\n            {\n                \"from\": \"centre\",\n                \"to\": \"cron\",\n                \"text\": null,\n                \"isHideArrow\": true\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-01\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-02\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-03\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-04\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-05\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-06\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-07\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-08\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-09\",\n                \"text\": null\n            },\n            {\n                \"from\": \"cron\",\n                \"to\": \"exe-10\",\n                \"text\": null\n            }\n        ]\n    },\n    \"options\": {\n        \"layout\": {\n            \"layoutName\": \"tree\",\n            \"max_per_height\": \"60\",\n            \"levelDistance\": \"200,400,200\",\n            \"centerOffset_x\": 0,\n            \"centerOffset_y\": 0,\n            \"from\": \"left\"\n        },\n        \"defaultNodeBorderWidth\": 0,\n        \"defaultNodeShape\": 1,\n        \"useAnimationWhenRefresh\": false,\n        \"defaultJunctionPoint\": \"lr\",\n        \"defaultLineShape\": 2\n    }\n}\n```\n\n## 3.5 集团图谱\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"N1\",\n        \"nodes\": [\n            {\n                \"id\": \"N1\",\n                \"text\": \"刘玉国\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N2\",\n                \"text\": \"毛华\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N3\",\n                \"text\": \"张颖\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N4\",\n                \"text\": \"田鑫\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N5\",\n                \"text\": \"Applefield Global Limited\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N6\",\n                \"text\": \"陈宇\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N7\",\n                \"text\": \"马化腾\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N8\",\n                \"text\": \"丁国云\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N9\",\n                \"text\": \"唐毅斌\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N10\",\n                \"text\": \"腾创控股有限公司\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N11\",\n                \"text\": \"张福茂\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N12\",\n                \"text\": \"奚丹\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N13\",\n                \"text\": \"胡建龙\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N14\",\n                \"text\": \"中霸集团有限公司\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N15\",\n                \"text\": \"马化腾\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N16\",\n                \"text\": \"廖卫怡\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N17\",\n                \"text\": \"向宇东\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"ctrler\"\n                }\n            },\n            {\n                \"id\": \"N18\",\n                \"text\": \"浙江腾趣网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N19\",\n                \"text\": \"杭州恒信信息系统工程有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N20\",\n                \"text\": \"成都信仰投资有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N21\",\n                \"text\": \"林芝文娱本源科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N22\",\n                \"text\": \"大连市世纪鲲鹏科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N23\",\n                \"text\": \"贵安新区腾讯数码有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N24\",\n                \"text\": \"霍尔果斯晓腾影业文化传播有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N25\",\n                \"text\": \"鹰王（深圳）科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N26\",\n                \"text\": \"深圳市腾讯信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N27\",\n                \"text\": \"上海腾闻网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N28\",\n                \"text\": \"横琴红土创新创业投资合伙企业（有限合伙）\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N29\",\n                \"text\": \"深圳市世纪天游科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N30\",\n                \"text\": \"深圳辰薰贸易有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N31\",\n                \"text\": \"武汉市腾讯教育科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N32\",\n                \"text\": \"深圳市腾讯网域计算机网络有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N33\",\n                \"text\": \"深圳市腾讯商业管理有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N34\",\n                \"text\": \"上海腾讯信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N35\",\n                \"text\": \"海南周天娱乐有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N36\",\n                \"text\": \"成都美奥广告有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N37\",\n                \"text\": \"深圳市企鹅金融科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N38\",\n                \"text\": \"浙江腾越网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N39\",\n                \"text\": \"珠海横琴腾南网络信息科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N40\",\n                \"text\": \"霍尔果斯腾影影视发行有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N41\",\n                \"text\": \"上海腾富金融信息服务有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N42\",\n                \"text\": \"杭州腾讯魔乐软件有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N43\",\n                \"text\": \"财付通支付科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N44\",\n                \"text\": \"华骏科技（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N45\",\n                \"text\": \"清远市腾讯数码有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N46\",\n                \"text\": \"武汉市世纪冲鸣科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N47\",\n                \"text\": \"腾讯影业文化传播有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N48\",\n                \"text\": \"深圳市文娱华章科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N49\",\n                \"text\": \"腾安基金销售（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N50\",\n                \"text\": \"湖南腾湘科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N51\",\n                \"text\": \"广州腾讯科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N52\",\n                \"text\": \"深圳市腾讯创业基地发展有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N53\",\n                \"text\": \"南京网典科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N54\",\n                \"text\": \"深圳市腾讯教育科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N55\",\n                \"text\": \"桐庐平顶之上文化创意有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N56\",\n                \"text\": \"深圳市腾南网络信息科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N57\",\n                \"text\": \"重庆市瑞德铭科技发展有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N58\",\n                \"text\": \"北京腾讯影业有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N59\",\n                \"text\": \"深圳市文娱华彩科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N60\",\n                \"text\": \"深圳市世纪彩讯科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N61\",\n                \"text\": \"海南腾讯互动娱乐有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N62\",\n                \"text\": \"北京弘泰永盛资本管理有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N63\",\n                \"text\": \"深圳市世纪腾华信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N64\",\n                \"text\": \"深圳市智税链科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N65\",\n                \"text\": \"河南腾河网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N66\",\n                \"text\": \"深圳市世纪凯旋科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N67\",\n                \"text\": \"上海腾讯企鹅影视文化传播有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N68\",\n                \"text\": \"深圳市腾讯动漫有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N69\",\n                \"text\": \"仪征腾讯数码有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N70\",\n                \"text\": \"深圳瓶子科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N71\",\n                \"text\": \"北京游侠网信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N72\",\n                \"text\": \"河北雄安新区腾讯计算机系统有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N73\",\n                \"text\": \"南京腾讯数码有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N74\",\n                \"text\": \"腾讯科技（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N75\",\n                \"text\": \"海南天天众猜科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N76\",\n                \"text\": \"北京驿码神通信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N77\",\n                \"text\": \"深圳市天美互动娱乐有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N78\",\n                \"text\": \"成都友巢互动网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N79\",\n                \"text\": \"张家界（北京驿码神通）信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N80\",\n                \"text\": \"腾讯科技（上海）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N81\",\n                \"text\": \"腾讯云科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N82\",\n                \"text\": \"深圳市腾讯魔方科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N83\",\n                \"text\": \"重庆腾讯信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N84\",\n                \"text\": \"深圳市腾讯文化传媒有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N85\",\n                \"text\": \"北京英克必成科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N86\",\n                \"text\": \"成都恒宇装饰有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N87\",\n                \"text\": \"腾讯科技(成都)有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N88\",\n                \"text\": \"合肥市腾讯信息科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N89\",\n                \"text\": \"腾创（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N90\",\n                \"text\": \"上海挚信新经济一期股权投资合伙企业（有限合伙）\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N91\",\n                \"text\": \"宝石岭（天津）科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N92\",\n                \"text\": \"深圳市宏大酒店用品有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N93\",\n                \"text\": \"北京倍嘉时代科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N94\",\n                \"text\": \"碁震（上海）云计算科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N95\",\n                \"text\": \"腾讯云计算（北京）有限责任公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N96\",\n                \"text\": \"深圳市腾讯网络信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N97\",\n                \"text\": \"广东腾南网络信息科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N98\",\n                \"text\": \"腾创控股（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N99\",\n                \"text\": \"浙江腾讯影业有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N100\",\n                \"text\": \"腾讯医疗健康（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N101\",\n                \"text\": \"腾讯大地通途（北京）科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N102\",\n                \"text\": \"广州菠萝蜜网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N103\",\n                \"text\": \"腾讯征信有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N104\",\n                \"text\": \"辽宁腾辽科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N105\",\n                \"text\": \"深圳市腾讯计算机系统有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N106\",\n                \"text\": \"重庆市腾讯计算机系统有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N107\",\n                \"text\": \"烟台帝思普网络科技有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N108\",\n                \"text\": \"北京有个节目文化传播有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N109\",\n                \"text\": \"腾创未来（深圳）有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N110\",\n                \"text\": \"北京市掌中星天下信息技术有限公司\",\n                \"color\": \"#4ea2f0\",\n                \"borderColor\": \"#6cc0ff\",\n                \"data\": {\n                    \"nodetype\": \"ent\"\n                }\n            },\n            {\n                \"id\": \"N111\",\n                \"text\": \"集团名称：腾创未来（深圳）有限公司\",\n                \"color\": \"#ec6941\",\n                \"borderColor\": \"#ff875e\",\n                \"data\": {\n                    \"nodetype\": \"main\"\n                }\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"N8\",\n                \"to\": \"N36\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N98\",\n                \"text\": \"执行董事;总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N42\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N1\",\n                \"to\": \"N91\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N103\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N11\",\n                \"to\": \"N102\",\n                \"text\": \"60%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N87\",\n                \"text\": \"董事长兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N30\",\n                \"text\": \"总经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N87\",\n                \"text\": \"77.2727%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N80\",\n                \"to\": \"N73\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N17\",\n                \"to\": \"N62\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N3\",\n                \"to\": \"N76\",\n                \"text\": \"经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N17\",\n                \"to\": \"N86\",\n                \"text\": \"60%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N63\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N74\",\n                \"text\": \"董事长;总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N31\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N1\",\n                \"to\": \"N93\",\n                \"text\": \"执行董事;经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N57\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N30\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N53\",\n                \"to\": \"N94\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N85\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N66\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N107\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N67\",\n                \"to\": \"N35\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N47\",\n                \"to\": \"N40\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N95\",\n                \"text\": \"80%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N107\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N80\",\n                \"to\": \"N69\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N33\",\n                \"to\": \"N28\",\n                \"text\": \"99%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N32\",\n                \"text\": \"29%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N71\",\n                \"text\": \"65.79%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N54\",\n                \"to\": \"N31\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N59\",\n                \"to\": \"N48\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N23\",\n                \"text\": \"77.2727%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N38\",\n                \"to\": \"N18\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N22\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N88\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N54\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N110\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N103\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N17\",\n                \"to\": \"N78\",\n                \"text\": \"董事兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N89\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N73\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N85\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N3\",\n                \"to\": \"N62\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N6\",\n                \"to\": \"N61\",\n                \"text\": \"执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N78\",\n                \"text\": \"董事长%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N8\",\n                \"to\": \"N20\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N33\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N3\",\n                \"to\": \"N51\",\n                \"text\": \"执行董事兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N54\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N34\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N20\",\n                \"text\": \"执行董事兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N43\",\n                \"text\": \"董事长%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N61\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N28\",\n                \"text\": \"51.05570085%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N87\",\n                \"to\": \"N23\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N10\",\n                \"to\": \"N98\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N77\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N2\",\n                \"to\": \"N19\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N83\",\n                \"to\": \"N57\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N75\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N83\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N37\",\n                \"text\": \"29%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N97\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N26\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N2\",\n                \"to\": \"N42\",\n                \"text\": \"总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N51\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N72\",\n                \"text\": \"90%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N52\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N99\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N52\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N77\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N61\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N93\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N53\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N43\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N11\",\n                \"to\": \"N71\",\n                \"text\": \"经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N80\",\n                \"text\": \"董事长兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N89\",\n                \"text\": \"总经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N92\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N44\",\n                \"text\": \"执行董事;总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N60\",\n                \"text\": \"53.742843%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N11\",\n                \"to\": \"N102\",\n                \"text\": \"执行董事兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N108\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N46\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N75\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N74\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N43\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N37\",\n                \"text\": \"执行董事;总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N27\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N35\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N38\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N59\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N26\",\n                \"text\": \"总经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N37\",\n                \"text\": \"71%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N68\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N78\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N32\",\n                \"text\": \"71%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N76\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N67\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N24\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N67\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N20\",\n                \"to\": \"N36\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N21\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N46\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N20\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N108\",\n                \"to\": \"N55\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N76\",\n                \"to\": \"N79\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N110\",\n                \"to\": \"N42\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N63\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N22\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N32\",\n                \"to\": \"N70\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N96\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N109\",\n                \"text\": \"总经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N40\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N36\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N10\",\n                \"to\": \"N109\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N55\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N94\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N82\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N52\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N105\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N93\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N97\",\n                \"to\": \"N56\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N32\",\n                \"to\": \"N49\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N50\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N100\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N33\",\n                \"to\": \"N90\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N106\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N81\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N33\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N99\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N26\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N69\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N59\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N13\",\n                \"to\": \"N19\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N47\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N5\",\n                \"to\": \"N44\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N58\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N81\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N6\",\n                \"to\": \"N25\",\n                \"text\": \"总经理;执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N67\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N33\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N45\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N88\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N100\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N47\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N47\",\n                \"to\": \"N24\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N16\",\n                \"to\": \"N30\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N59\",\n                \"to\": \"N21\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N101\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N101\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N51\",\n                \"to\": \"N45\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N97\",\n                \"to\": \"N39\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N1\",\n                \"to\": \"N91\",\n                \"text\": \"执行董事;经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N83\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N76\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N103\",\n                \"text\": \"执行董事%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N64\",\n                \"text\": \"51.571415%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N48\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N92\",\n                \"text\": \"执行董事;总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N85\",\n                \"to\": \"N95\",\n                \"text\": \"20%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N47\",\n                \"to\": \"N58\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N106\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N29\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N51\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N68\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N96\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N29\",\n                \"to\": \"N75\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N32\",\n                \"to\": \"N41\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"N110\",\n                \"text\": \"54.2857%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N17\",\n                \"to\": \"N86\",\n                \"text\": \"执行董事兼总经理%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N6\",\n                \"to\": \"N25\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N84\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N80\",\n                \"to\": \"N34\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"N29\",\n                \"text\": \"50%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N103\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N53\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N33\",\n                \"to\": \"N64\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N84\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N14\",\n                \"to\": \"N80\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N108\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N104\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N47\",\n                \"text\": \"95%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N65\",\n                \"text\": \"51%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N74\",\n                \"to\": \"N82\",\n                \"text\": \"100%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N66\",\n                \"to\": \"N43\",\n                \"text\": \"5%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N105\",\n                \"to\": \"N60\",\n                \"text\": \"99%\",\n                \"fontColor\": \"#ed724d\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N105\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N86\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N30\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N74\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N66\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N25\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N78\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N98\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N62\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N29\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N109\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N92\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N44\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N89\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N87\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N20\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N80\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N102\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N19\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            },\n            {\n                \"from\": \"N111\",\n                \"to\": \"N91\",\n                \"text\": \"null%\",\n                \"fontColor\": \"#d2c0a5\"\n            }\n        ]\n    },\n    \"options\": {\n        \"layouts\": [\n            {\n                \"label\": \"Center\",\n                \"layoutName\": \"center\",\n                \"layoutClassName\": \"seeks-layout-center\"\n            }\n        ]\n    }\n}\n```\n\n## 3.6 力学布局\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"a\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"b\"\n            },\n            {\n                \"id\": \"b1\",\n                \"text\": \"b1\"\n            },\n            {\n                \"id\": \"b1-1\",\n                \"text\": \"b1-1\"\n            },\n            {\n                \"id\": \"b1-2\",\n                \"text\": \"b1-2\"\n            },\n            {\n                \"id\": \"b1-3\",\n                \"text\": \"b1-3\"\n            },\n            {\n                \"id\": \"b1-4\",\n                \"text\": \"b1-4\"\n            },\n            {\n                \"id\": \"b1-5\",\n                \"text\": \"b1-5\"\n            },\n            {\n                \"id\": \"b1-6\",\n                \"text\": \"b1-6\"\n            },\n            {\n                \"id\": \"b2\",\n                \"text\": \"b2\"\n            },\n            {\n                \"id\": \"b2-1\",\n                \"text\": \"b2-1\"\n            },\n            {\n                \"id\": \"b2-2\",\n                \"text\": \"b2-2\"\n            },\n            {\n                \"id\": \"b2-3\",\n                \"text\": \"b2-3\"\n            },\n            {\n                \"id\": \"b2-4\",\n                \"text\": \"b2-4\"\n            },\n            {\n                \"id\": \"b3\",\n                \"text\": \"b3\"\n            },\n            {\n                \"id\": \"b3-1\",\n                \"text\": \"b3-1\"\n            },\n            {\n                \"id\": \"b3-2\",\n                \"text\": \"b3-2\"\n            },\n            {\n                \"id\": \"b3-3\",\n                \"text\": \"b3-3\"\n            },\n            {\n                \"id\": \"b3-4\",\n                \"text\": \"b3-4\"\n            },\n            {\n                \"id\": \"b3-5\",\n                \"text\": \"b3-5\"\n            },\n            {\n                \"id\": \"b3-6\",\n                \"text\": \"b3-6\"\n            },\n            {\n                \"id\": \"b3-7\",\n                \"text\": \"b3-7\"\n            },\n            {\n                \"id\": \"b4\",\n                \"text\": \"b4\"\n            },\n            {\n                \"id\": \"b4-1\",\n                \"text\": \"b4-1\"\n            },\n            {\n                \"id\": \"b4-2\",\n                \"text\": \"b4-2\"\n            },\n            {\n                \"id\": \"b4-3\",\n                \"text\": \"b4-3\"\n            },\n            {\n                \"id\": \"b4-4\",\n                \"text\": \"b4-4\"\n            },\n            {\n                \"id\": \"b4-5\",\n                \"text\": \"b4-5\"\n            },\n            {\n                \"id\": \"b4-6\",\n                \"text\": \"b4-6\"\n            },\n            {\n                \"id\": \"b4-7\",\n                \"text\": \"b4-7\"\n            },\n            {\n                \"id\": \"b4-8\",\n                \"text\": \"b4-8\"\n            },\n            {\n                \"id\": \"b4-9\",\n                \"text\": \"b4-9\"\n            },\n            {\n                \"id\": \"b5\",\n                \"text\": \"b5\"\n            },\n            {\n                \"id\": \"b5-1\",\n                \"text\": \"b5-1\"\n            },\n            {\n                \"id\": \"b5-2\",\n                \"text\": \"b5-2\"\n            },\n            {\n                \"id\": \"b5-3\",\n                \"text\": \"b5-3\"\n            },\n            {\n                \"id\": \"b5-4\",\n                \"text\": \"b5-4\"\n            },\n            {\n                \"id\": \"b6\",\n                \"text\": \"b6\"\n            },\n            {\n                \"id\": \"b6-1\",\n                \"text\": \"b6-1\"\n            },\n            {\n                \"id\": \"b6-2\",\n                \"text\": \"b6-2\"\n            },\n            {\n                \"id\": \"b6-3\",\n                \"text\": \"b6-3\"\n            },\n            {\n                \"id\": \"b6-4\",\n                \"text\": \"b6-4\"\n            },\n            {\n                \"id\": \"b6-5\",\n                \"text\": \"b6-5\"\n            },\n            {\n                \"id\": \"c\",\n                \"text\": \"c\"\n            },\n            {\n                \"id\": \"c1\",\n                \"text\": \"c1\"\n            },\n            {\n                \"id\": \"c1-1\",\n                \"text\": \"c1-1\"\n            },\n            {\n                \"id\": \"c1-2\",\n                \"text\": \"c1-2\"\n            },\n            {\n                \"id\": \"c1-3\",\n                \"text\": \"c1-3\"\n            },\n            {\n                \"id\": \"c1-4\",\n                \"text\": \"c1-4\"\n            },\n            {\n                \"id\": \"c1-5\",\n                \"text\": \"c1-5\"\n            },\n            {\n                \"id\": \"c1-6\",\n                \"text\": \"c1-6\"\n            },\n            {\n                \"id\": \"c1-7\",\n                \"text\": \"c1-7\"\n            },\n            {\n                \"id\": \"c2\",\n                \"text\": \"c2\"\n            },\n            {\n                \"id\": \"c2-1\",\n                \"text\": \"c2-1\"\n            },\n            {\n                \"id\": \"c2-2\",\n                \"text\": \"c2-2\"\n            },\n            {\n                \"id\": \"c3\",\n                \"text\": \"c3\"\n            },\n            {\n                \"id\": \"c3-1\",\n                \"text\": \"c3-1\"\n            },\n            {\n                \"id\": \"c3-2\",\n                \"text\": \"c3-2\"\n            },\n            {\n                \"id\": \"c3-3\",\n                \"text\": \"c3-3\"\n            },\n            {\n                \"id\": \"d\",\n                \"text\": \"d\"\n            },\n            {\n                \"id\": \"d1\",\n                \"text\": \"d1\"\n            },\n            {\n                \"id\": \"d1-1\",\n                \"text\": \"d1-1\"\n            },\n            {\n                \"id\": \"d1-2\",\n                \"text\": \"d1-2\"\n            },\n            {\n                \"id\": \"d1-3\",\n                \"text\": \"d1-3\"\n            },\n            {\n                \"id\": \"d1-4\",\n                \"text\": \"d1-4\"\n            },\n            {\n                \"id\": \"d1-5\",\n                \"text\": \"d1-5\"\n            },\n            {\n                \"id\": \"d1-6\",\n                \"text\": \"d1-6\"\n            },\n            {\n                \"id\": \"d1-7\",\n                \"text\": \"d1-7\"\n            },\n            {\n                \"id\": \"d1-8\",\n                \"text\": \"d1-8\"\n            },\n            {\n                \"id\": \"d2\",\n                \"text\": \"d2\"\n            },\n            {\n                \"id\": \"d2-1\",\n                \"text\": \"d2-1\"\n            },\n            {\n                \"id\": \"d2-2\",\n                \"text\": \"d2-2\"\n            },\n            {\n                \"id\": \"d3\",\n                \"text\": \"d3\"\n            },\n            {\n                \"id\": \"d3-1\",\n                \"text\": \"d3-1\"\n            },\n            {\n                \"id\": \"d3-2\",\n                \"text\": \"d3-2\"\n            },\n            {\n                \"id\": \"d3-3\",\n                \"text\": \"d3-3\"\n            },\n            {\n                \"id\": \"d3-4\",\n                \"text\": \"d3-4\"\n            },\n            {\n                \"id\": \"d3-5\",\n                \"text\": \"d3-5\"\n            },\n            {\n                \"id\": \"d4\",\n                \"text\": \"d4\"\n            },\n            {\n                \"id\": \"d4-1\",\n                \"text\": \"d4-1\"\n            },\n            {\n                \"id\": \"d4-2\",\n                \"text\": \"d4-2\"\n            },\n            {\n                \"id\": \"d4-3\",\n                \"text\": \"d4-3\"\n            },\n            {\n                \"id\": \"d4-4\",\n                \"text\": \"d4-4\"\n            },\n            {\n                \"id\": \"d4-5\",\n                \"text\": \"d4-5\"\n            },\n            {\n                \"id\": \"d4-6\",\n                \"text\": \"d4-6\"\n            },\n            {\n                \"id\": \"e\",\n                \"text\": \"e\"\n            },\n            {\n                \"id\": \"e1\",\n                \"text\": \"e1\"\n            },\n            {\n                \"id\": \"e1-1\",\n                \"text\": \"e1-1\"\n            },\n            {\n                \"id\": \"e1-2\",\n                \"text\": \"e1-2\"\n            },\n            {\n                \"id\": \"e1-3\",\n                \"text\": \"e1-3\"\n            },\n            {\n                \"id\": \"e1-4\",\n                \"text\": \"e1-4\"\n            },\n            {\n                \"id\": \"e1-5\",\n                \"text\": \"e1-5\"\n            },\n            {\n                \"id\": \"e1-6\",\n                \"text\": \"e1-6\"\n            },\n            {\n                \"id\": \"e2\",\n                \"text\": \"e2\"\n            },\n            {\n                \"id\": \"e2-1\",\n                \"text\": \"e2-1\"\n            },\n            {\n                \"id\": \"e2-2\",\n                \"text\": \"e2-2\"\n            },\n            {\n                \"id\": \"e2-3\",\n                \"text\": \"e2-3\"\n            },\n            {\n                \"id\": \"e2-4\",\n                \"text\": \"e2-4\"\n            },\n            {\n                \"id\": \"e2-5\",\n                \"text\": \"e2-5\"\n            },\n            {\n                \"id\": \"e2-6\",\n                \"text\": \"e2-6\"\n            },\n            {\n                \"id\": \"e2-7\",\n                \"text\": \"e2-7\"\n            },\n            {\n                \"id\": \"e2-8\",\n                \"text\": \"e2-8\"\n            },\n            {\n                \"id\": \"e2-9\",\n                \"text\": \"e2-9\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b1\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-1\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-2\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-3\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-4\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-5\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-6\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b2\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-1\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-2\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-3\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-4\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b3\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-1\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-2\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-3\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-4\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-5\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-6\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-7\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b4\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-1\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-2\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-3\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-4\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-5\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-6\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-7\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-8\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-9\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b5\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-1\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-2\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-3\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-4\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b6\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-1\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-2\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-3\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-4\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-5\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c1\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-1\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-2\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-3\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-4\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-5\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-6\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-7\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c2\"\n            },\n            {\n                \"from\": \"c2\",\n                \"to\": \"c2-1\"\n            },\n            {\n                \"from\": \"c2\",\n                \"to\": \"c2-2\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c3\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-1\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-2\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-3\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"d\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d1\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-1\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-2\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-3\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-4\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-5\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-6\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-7\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-8\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d2\"\n            },\n            {\n                \"from\": \"d2\",\n                \"to\": \"d2-1\"\n            },\n            {\n                \"from\": \"d2\",\n                \"to\": \"d2-2\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d3\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-1\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-2\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-3\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-4\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-5\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d4\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-1\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-2\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-3\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-4\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-5\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-6\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"e\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"e1\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-1\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-2\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-3\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-4\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-5\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-6\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"e2\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-1\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-2\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-3\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-4\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-5\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-6\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-7\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-8\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-9\"\n            }\n        ]\n    },\n    \"options\": {\n        \"debug\": false,\n        \"defaultNodeBorderWidth\": 0,\n        \"allowSwitchLineShape\": true,\n        \"allowSwitchJunctionPoint\": true,\n        \"defaultLineShape\": 1,\n        \"layouts\": [\n            {\n                \"label\": \"自动布局\",\n                \"layoutName\": \"force\",\n                \"layoutClassName\": \"seeks-layout-force\"\n            }\n        ],\n        \"defaultJunctionPoint\": \"border\"\n    }\n}\n```\n\n## 3.7 中心布局\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"a\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"b\"\n            },\n            {\n                \"id\": \"b1\",\n                \"text\": \"b1\"\n            },\n            {\n                \"id\": \"b1-1\",\n                \"text\": \"b1-1\"\n            },\n            {\n                \"id\": \"b1-2\",\n                \"text\": \"b1-2\"\n            },\n            {\n                \"id\": \"b1-3\",\n                \"text\": \"b1-3\"\n            },\n            {\n                \"id\": \"b1-4\",\n                \"text\": \"b1-4\"\n            },\n            {\n                \"id\": \"b1-5\",\n                \"text\": \"b1-5\"\n            },\n            {\n                \"id\": \"b1-6\",\n                \"text\": \"b1-6\"\n            },\n            {\n                \"id\": \"b2\",\n                \"text\": \"b2\"\n            },\n            {\n                \"id\": \"b2-1\",\n                \"text\": \"b2-1\"\n            },\n            {\n                \"id\": \"b2-2\",\n                \"text\": \"b2-2\"\n            },\n            {\n                \"id\": \"b2-3\",\n                \"text\": \"b2-3\"\n            },\n            {\n                \"id\": \"b2-4\",\n                \"text\": \"b2-4\"\n            },\n            {\n                \"id\": \"b3\",\n                \"text\": \"b3\"\n            },\n            {\n                \"id\": \"b3-1\",\n                \"text\": \"b3-1\"\n            },\n            {\n                \"id\": \"b3-2\",\n                \"text\": \"b3-2\"\n            },\n            {\n                \"id\": \"b3-3\",\n                \"text\": \"b3-3\"\n            },\n            {\n                \"id\": \"b3-4\",\n                \"text\": \"b3-4\"\n            },\n            {\n                \"id\": \"b3-5\",\n                \"text\": \"b3-5\"\n            },\n            {\n                \"id\": \"b3-6\",\n                \"text\": \"b3-6\"\n            },\n            {\n                \"id\": \"b3-7\",\n                \"text\": \"b3-7\"\n            },\n            {\n                \"id\": \"b4\",\n                \"text\": \"b4\"\n            },\n            {\n                \"id\": \"b4-1\",\n                \"text\": \"b4-1\"\n            },\n            {\n                \"id\": \"b4-2\",\n                \"text\": \"b4-2\"\n            },\n            {\n                \"id\": \"b4-3\",\n                \"text\": \"b4-3\"\n            },\n            {\n                \"id\": \"b4-4\",\n                \"text\": \"b4-4\"\n            },\n            {\n                \"id\": \"b4-5\",\n                \"text\": \"b4-5\"\n            },\n            {\n                \"id\": \"b4-6\",\n                \"text\": \"b4-6\"\n            },\n            {\n                \"id\": \"b4-7\",\n                \"text\": \"b4-7\"\n            },\n            {\n                \"id\": \"b4-8\",\n                \"text\": \"b4-8\"\n            },\n            {\n                \"id\": \"b4-9\",\n                \"text\": \"b4-9\"\n            },\n            {\n                \"id\": \"b5\",\n                \"text\": \"b5\"\n            },\n            {\n                \"id\": \"b5-1\",\n                \"text\": \"b5-1\"\n            },\n            {\n                \"id\": \"b5-2\",\n                \"text\": \"b5-2\"\n            },\n            {\n                \"id\": \"b5-3\",\n                \"text\": \"b5-3\"\n            },\n            {\n                \"id\": \"b5-4\",\n                \"text\": \"b5-4\"\n            },\n            {\n                \"id\": \"b6\",\n                \"text\": \"b6\"\n            },\n            {\n                \"id\": \"b6-1\",\n                \"text\": \"b6-1\"\n            },\n            {\n                \"id\": \"b6-2\",\n                \"text\": \"b6-2\"\n            },\n            {\n                \"id\": \"b6-3\",\n                \"text\": \"b6-3\"\n            },\n            {\n                \"id\": \"b6-4\",\n                \"text\": \"b6-4\"\n            },\n            {\n                \"id\": \"b6-5\",\n                \"text\": \"b6-5\"\n            },\n            {\n                \"id\": \"c\",\n                \"text\": \"c\"\n            },\n            {\n                \"id\": \"c1\",\n                \"text\": \"c1\"\n            },\n            {\n                \"id\": \"c1-1\",\n                \"text\": \"c1-1\"\n            },\n            {\n                \"id\": \"c1-2\",\n                \"text\": \"c1-2\"\n            },\n            {\n                \"id\": \"c1-3\",\n                \"text\": \"c1-3\"\n            },\n            {\n                \"id\": \"c1-4\",\n                \"text\": \"c1-4\"\n            },\n            {\n                \"id\": \"c1-5\",\n                \"text\": \"c1-5\"\n            },\n            {\n                \"id\": \"c1-6\",\n                \"text\": \"c1-6\"\n            },\n            {\n                \"id\": \"c1-7\",\n                \"text\": \"c1-7\"\n            },\n            {\n                \"id\": \"c2\",\n                \"text\": \"c2\"\n            },\n            {\n                \"id\": \"c2-1\",\n                \"text\": \"c2-1\"\n            },\n            {\n                \"id\": \"c2-2\",\n                \"text\": \"c2-2\"\n            },\n            {\n                \"id\": \"c3\",\n                \"text\": \"c3\"\n            },\n            {\n                \"id\": \"c3-1\",\n                \"text\": \"c3-1\"\n            },\n            {\n                \"id\": \"c3-2\",\n                \"text\": \"c3-2\"\n            },\n            {\n                \"id\": \"c3-3\",\n                \"text\": \"c3-3\"\n            },\n            {\n                \"id\": \"d\",\n                \"text\": \"d\"\n            },\n            {\n                \"id\": \"d1\",\n                \"text\": \"d1\"\n            },\n            {\n                \"id\": \"d1-1\",\n                \"text\": \"d1-1\"\n            },\n            {\n                \"id\": \"d1-2\",\n                \"text\": \"d1-2\"\n            },\n            {\n                \"id\": \"d1-3\",\n                \"text\": \"d1-3\"\n            },\n            {\n                \"id\": \"d1-4\",\n                \"text\": \"d1-4\"\n            },\n            {\n                \"id\": \"d1-5\",\n                \"text\": \"d1-5\"\n            },\n            {\n                \"id\": \"d1-6\",\n                \"text\": \"d1-6\"\n            },\n            {\n                \"id\": \"d1-7\",\n                \"text\": \"d1-7\"\n            },\n            {\n                \"id\": \"d1-8\",\n                \"text\": \"d1-8\"\n            },\n            {\n                \"id\": \"d2\",\n                \"text\": \"d2\"\n            },\n            {\n                \"id\": \"d2-1\",\n                \"text\": \"d2-1\"\n            },\n            {\n                \"id\": \"d2-2\",\n                \"text\": \"d2-2\"\n            },\n            {\n                \"id\": \"d3\",\n                \"text\": \"d3\"\n            },\n            {\n                \"id\": \"d3-1\",\n                \"text\": \"d3-1\"\n            },\n            {\n                \"id\": \"d3-2\",\n                \"text\": \"d3-2\"\n            },\n            {\n                \"id\": \"d3-3\",\n                \"text\": \"d3-3\"\n            },\n            {\n                \"id\": \"d3-4\",\n                \"text\": \"d3-4\"\n            },\n            {\n                \"id\": \"d3-5\",\n                \"text\": \"d3-5\"\n            },\n            {\n                \"id\": \"d4\",\n                \"text\": \"d4\"\n            },\n            {\n                \"id\": \"d4-1\",\n                \"text\": \"d4-1\"\n            },\n            {\n                \"id\": \"d4-2\",\n                \"text\": \"d4-2\"\n            },\n            {\n                \"id\": \"d4-3\",\n                \"text\": \"d4-3\"\n            },\n            {\n                \"id\": \"d4-4\",\n                \"text\": \"d4-4\"\n            },\n            {\n                \"id\": \"d4-5\",\n                \"text\": \"d4-5\"\n            },\n            {\n                \"id\": \"d4-6\",\n                \"text\": \"d4-6\"\n            },\n            {\n                \"id\": \"e\",\n                \"text\": \"e\"\n            },\n            {\n                \"id\": \"e1\",\n                \"text\": \"e1\"\n            },\n            {\n                \"id\": \"e1-1\",\n                \"text\": \"e1-1\"\n            },\n            {\n                \"id\": \"e1-2\",\n                \"text\": \"e1-2\"\n            },\n            {\n                \"id\": \"e1-3\",\n                \"text\": \"e1-3\"\n            },\n            {\n                \"id\": \"e1-4\",\n                \"text\": \"e1-4\"\n            },\n            {\n                \"id\": \"e1-5\",\n                \"text\": \"e1-5\"\n            },\n            {\n                \"id\": \"e1-6\",\n                \"text\": \"e1-6\"\n            },\n            {\n                \"id\": \"e2\",\n                \"text\": \"e2\"\n            },\n            {\n                \"id\": \"e2-1\",\n                \"text\": \"e2-1\"\n            },\n            {\n                \"id\": \"e2-2\",\n                \"text\": \"e2-2\"\n            },\n            {\n                \"id\": \"e2-3\",\n                \"text\": \"e2-3\"\n            },\n            {\n                \"id\": \"e2-4\",\n                \"text\": \"e2-4\"\n            },\n            {\n                \"id\": \"e2-5\",\n                \"text\": \"e2-5\"\n            },\n            {\n                \"id\": \"e2-6\",\n                \"text\": \"e2-6\"\n            },\n            {\n                \"id\": \"e2-7\",\n                \"text\": \"e2-7\"\n            },\n            {\n                \"id\": \"e2-8\",\n                \"text\": \"e2-8\"\n            },\n            {\n                \"id\": \"e2-9\",\n                \"text\": \"e2-9\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b1\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-1\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-2\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-3\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-4\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-5\"\n            },\n            {\n                \"from\": \"b1\",\n                \"to\": \"b1-6\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b2\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-1\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-2\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-3\"\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-4\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b3\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-1\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-2\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-3\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-4\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-5\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-6\"\n            },\n            {\n                \"from\": \"b3\",\n                \"to\": \"b3-7\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b4\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-1\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-2\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-3\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-4\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-5\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-6\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-7\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-8\"\n            },\n            {\n                \"from\": \"b4\",\n                \"to\": \"b4-9\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b5\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-1\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-2\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-3\"\n            },\n            {\n                \"from\": \"b5\",\n                \"to\": \"b5-4\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b6\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-1\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-2\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-3\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-4\"\n            },\n            {\n                \"from\": \"b6\",\n                \"to\": \"b6-5\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c1\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-1\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-2\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-3\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-4\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-5\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-6\"\n            },\n            {\n                \"from\": \"c1\",\n                \"to\": \"c1-7\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c2\"\n            },\n            {\n                \"from\": \"c2\",\n                \"to\": \"c2-1\"\n            },\n            {\n                \"from\": \"c2\",\n                \"to\": \"c2-2\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c3\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-1\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-2\"\n            },\n            {\n                \"from\": \"c3\",\n                \"to\": \"c3-3\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"d\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d1\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-1\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-2\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-3\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-4\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-5\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-6\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-7\"\n            },\n            {\n                \"from\": \"d1\",\n                \"to\": \"d1-8\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d2\"\n            },\n            {\n                \"from\": \"d2\",\n                \"to\": \"d2-1\"\n            },\n            {\n                \"from\": \"d2\",\n                \"to\": \"d2-2\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d3\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-1\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-2\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-3\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-4\"\n            },\n            {\n                \"from\": \"d3\",\n                \"to\": \"d3-5\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"d4\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-1\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-2\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-3\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-4\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-5\"\n            },\n            {\n                \"from\": \"d4\",\n                \"to\": \"d4-6\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"e\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"e1\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-1\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-2\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-3\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-4\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-5\"\n            },\n            {\n                \"from\": \"e1\",\n                \"to\": \"e1-6\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"e2\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-1\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-2\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-3\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-4\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-5\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-6\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-7\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-8\"\n            },\n            {\n                \"from\": \"e2\",\n                \"to\": \"e2-9\"\n            }\n        ]\n    },\n    \"options\": {\n        \"debug\": false,\n        \"allowSwitchLineShape\": true,\n        \"allowSwitchJunctionPoint\": true,\n        \"defaultNodeBorderWidth\": 0,\n        \"zoomToFitWhenRefresh\": false,\n        \"defaultNodeWidth\": 40,\n        \"defaultNodeHeight\": 40,\n        \"defaultLineColor\": \"rgba(0, 186, 189, 1)\",\n        \"defaultNodeColor\": \"rgba(0, 206, 209, 1)\",\n        \"layout\": {\n            \"label\": \"Center\",\n            \"layoutName\": \"center\",\n            \"layoutClassName\": \"seeks-layout-center\",\n            \"distance_coefficient\": 1\n        }\n    }\n}\n```\n\n## 3.8 双向树\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"root\",\n        \"nodes\": [\n            {\n                \"id\": \"root\",\n                \"text\": \"Node\"\n            },\n            {\n                \"id\": \"N2\",\n                \"text\": \"New Node N2\"\n            },\n            {\n                \"id\": \"N3\",\n                \"text\": \"New Node N3\"\n            },\n            {\n                \"id\": \"N4\",\n                \"text\": \"New Node N4\"\n            },\n            {\n                \"id\": \"N5\",\n                \"text\": \"New Node N5\"\n            },\n            {\n                \"id\": \"N6\",\n                \"text\": \"New Node N6\"\n            },\n            {\n                \"id\": \"N7\",\n                \"text\": \"New Node N7\"\n            },\n            {\n                \"id\": \"N8\",\n                \"text\": \"New Node N8\"\n            },\n            {\n                \"id\": \"N9\",\n                \"text\": \"New Node N9\"\n            },\n            {\n                \"id\": \"N10\",\n                \"text\": \"New Node N10\"\n            },\n            {\n                \"id\": \"N11\",\n                \"text\": \"New Node N11\"\n            },\n            {\n                \"id\": \"N12\",\n                \"text\": \"New Node N12\"\n            },\n            {\n                \"id\": \"N13\",\n                \"text\": \"New Node N13\"\n            },\n            {\n                \"id\": \"N14\",\n                \"text\": \"New Node N14\"\n            },\n            {\n                \"id\": \"N15\",\n                \"text\": \"New Node N15\"\n            },\n            {\n                \"id\": \"N16\",\n                \"text\": \"New Node N16\"\n            },\n            {\n                \"id\": \"N17\",\n                \"text\": \"New Node N17\"\n            },\n            {\n                \"id\": \"N18\",\n                \"text\": \"New Node N18\"\n            },\n            {\n                \"id\": \"N19\",\n                \"text\": \"New Node N19\"\n            },\n            {\n                \"id\": \"N20\",\n                \"text\": \"New Node N20\"\n            },\n            {\n                \"id\": \"N21\",\n                \"text\": \"New Node N21\"\n            },\n            {\n                \"id\": \"N22\",\n                \"text\": \"New Node N22\"\n            },\n            {\n                \"id\": \"N23\",\n                \"text\": \"New Node N23\"\n            },\n            {\n                \"id\": \"N24\",\n                \"text\": \"New Node N24\"\n            },\n            {\n                \"id\": \"N25\",\n                \"text\": \"New Node N25\"\n            },\n            {\n                \"id\": \"N26\",\n                \"text\": \"New Node N26\"\n            },\n            {\n                \"id\": \"N27\",\n                \"text\": \"New N27\"\n            },\n            {\n                \"id\": \"N28\",\n                \"text\": \"New N28\"\n            },\n            {\n                \"id\": \"N29\",\n                \"text\": \"New N29\"\n            },\n            {\n                \"id\": \"N30\",\n                \"text\": \"New N30\"\n            },\n            {\n                \"id\": \"N31\",\n                \"text\": \"New N31\"\n            },\n            {\n                \"id\": \"N32\",\n                \"text\": \"New N32\"\n            },\n            {\n                \"id\": \"N33\",\n                \"text\": \"New N33\"\n            },\n            {\n                \"id\": \"N34\",\n                \"text\": \"New N34\"\n            },\n            {\n                \"id\": \"N35\",\n                \"text\": \"New N35\"\n            },\n            {\n                \"id\": \"N36\",\n                \"text\": \"New N36\"\n            },\n            {\n                \"id\": \"N37\",\n                \"text\": \"New N37\"\n            },\n            {\n                \"id\": \"N38\",\n                \"text\": \"New N38\"\n            },\n            {\n                \"id\": \"N39\",\n                \"text\": \"New N39\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"N3\",\n                \"to\": \"N2\",\n                \"text\": \"New Line 1\"\n            },\n            {\n                \"from\": \"N2\",\n                \"to\": \"root\",\n                \"text\": \"New Line 1\"\n            },\n            {\n                \"from\": \"root\",\n                \"to\": \"N4\",\n                \"text\": \"New Line 2\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N5\",\n                \"text\": \"New Line 3\"\n            },\n            {\n                \"from\": \"N6\",\n                \"to\": \"N7\",\n                \"text\": \"New Line 2\"\n            },\n            {\n                \"from\": \"N7\",\n                \"to\": \"root\",\n                \"text\": \"New Line 3\"\n            },\n            {\n                \"from\": \"N8\",\n                \"to\": \"N9\",\n                \"text\": \"New Line 4\"\n            },\n            {\n                \"from\": \"N9\",\n                \"to\": \"root\",\n                \"text\": \"New Line 5\"\n            },\n            {\n                \"from\": \"N10\",\n                \"to\": \"N11\",\n                \"text\": \"New Line 6\"\n            },\n            {\n                \"from\": \"N11\",\n                \"to\": \"root\",\n                \"text\": \"New Line 7\"\n            },\n            {\n                \"from\": \"N13\",\n                \"to\": \"N12\",\n                \"text\": \"New Line 8\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"root\",\n                \"text\": \"New Line 9\"\n            },\n            {\n                \"from\": \"N18\",\n                \"to\": \"N17\",\n                \"text\": \"New Line 10\"\n            },\n            {\n                \"from\": \"N17\",\n                \"to\": \"N14\",\n                \"text\": \"New Line 11\"\n            },\n            {\n                \"from\": \"N15\",\n                \"to\": \"N14\",\n                \"text\": \"New Line 12\"\n            },\n            {\n                \"from\": \"N16\",\n                \"to\": \"N15\",\n                \"text\": \"New Line 13\"\n            },\n            {\n                \"from\": \"N12\",\n                \"to\": \"N14\",\n                \"text\": \"New Line 14\"\n            },\n            {\n                \"from\": \"N20\",\n                \"to\": \"N19\",\n                \"text\": \"New Line 15\"\n            },\n            {\n                \"from\": \"N21\",\n                \"to\": \"N19\",\n                \"text\": \"New Line 16\"\n            },\n            {\n                \"from\": \"N19\",\n                \"to\": \"N15\",\n                \"text\": \"New Line 17\"\n            },\n            {\n                \"from\": \"N26\",\n                \"to\": \"N22\",\n                \"text\": \"New Line 18\"\n            },\n            {\n                \"from\": \"N24\",\n                \"to\": \"N25\",\n                \"text\": \"New Line 19\"\n            },\n            {\n                \"from\": \"N24\",\n                \"to\": \"N22\",\n                \"text\": \"New Line 20\"\n            },\n            {\n                \"from\": \"N22\",\n                \"to\": \"N23\",\n                \"text\": \"New Line 21\"\n            },\n            {\n                \"from\": \"N23\",\n                \"to\": \"N14\",\n                \"text\": \"New Line 22\"\n            },\n            {\n                \"from\": \"root\",\n                \"to\": \"N30\",\n                \"text\": \"New Line 1\"\n            },\n            {\n                \"from\": \"root\",\n                \"to\": \"N27\",\n                \"text\": \"New Line 2\"\n            },\n            {\n                \"from\": \"N30\",\n                \"to\": \"N33\",\n                \"text\": \"New Line 3\"\n            },\n            {\n                \"from\": \"N30\",\n                \"to\": \"N29\",\n                \"text\": \"New Line 4\"\n            },\n            {\n                \"from\": \"N27\",\n                \"to\": \"N28\",\n                \"text\": \"New Line 5\"\n            },\n            {\n                \"from\": \"N27\",\n                \"to\": \"N31\",\n                \"text\": \"New Line 6\"\n            },\n            {\n                \"from\": \"N27\",\n                \"to\": \"N32\",\n                \"text\": \"New Line 7\"\n            },\n            {\n                \"from\": \"N4\",\n                \"to\": \"N34\",\n                \"text\": \"New Line 8\"\n            },\n            {\n                \"from\": \"N28\",\n                \"to\": \"N35\",\n                \"text\": \"New Line 9\"\n            },\n            {\n                \"from\": \"N28\",\n                \"to\": \"N36\",\n                \"text\": \"New Line 12\"\n            },\n            {\n                \"from\": \"N28\",\n                \"to\": \"N37\",\n                \"text\": \"New Line 13\"\n            },\n            {\n                \"from\": \"N36\",\n                \"to\": \"N39\",\n                \"text\": \"New Line 14\"\n            },\n            {\n                \"from\": \"N36\",\n                \"to\": \"N38\",\n                \"text\": \"New Line 15\"\n            }\n        ]\n    },\n    \"options\": {\n        \"debug\": false,\n        \"layout\": {\n            \"layoutName\": \"tree\",\n            \"from\": \"left\",\n            \"max_per_width\": 300,\n            \"min_per_height\": 40\n        },\n        \"defaultNodeShape\": 1,\n        \"defaultNodeWidth\": 100,\n        \"defaultLineShape\": 4,\n        \"defaultJunctionPoint\": \"lr\",\n        \"defaultNodeBorderWidth\": 0,\n        \"defaultLineColor\": \"rgba(0, 186, 189, 1)\",\n        \"defaultNodeColor\": \"rgba(0, 206, 209, 1)\",\n        \"defaultLineTextOffset_x\": -8,\n        \"defaultLineTextOffset_y\": -1\n    }\n}\n```\n\n## 3.9 多节点样式\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"Border color\",\n                \"borderColor\": \"yellow\"\n            },\n            {\n                \"id\": \"a1\",\n                \"text\": \"No border\",\n                \"borderWidth\": -1,\n                \"color\": \"#ff8c00\"\n            },\n            {\n                \"id\": \"a2\",\n                \"text\": \"Plain\",\n                \"borderWidth\": 3,\n                \"color\": \"transparent\",\n                \"borderColor\": \"#ff8c00\",\n                \"fontColor\": \"#ff8c00\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"Font color\",\n                \"color\": \"#43a2f1\",\n                \"fontColor\": \"#ffd700\"\n            },\n            {\n                \"id\": \"a1-1\",\n                \"html\": \"<span>Text Node</span>\"\n            },\n            {\n                \"id\": \"d\",\n                \"text\": \"Node Size\",\n                \"width\": 150,\n                \"height\": 150,\n                \"color\": \"#ff8c00\",\n                \"borderWidth\": 5,\n                \"borderColor\": \"#ffd700\",\n                \"fontColor\": \"#ffffff\"\n            },\n            {\n                \"id\": \"e\",\n                \"text\": \"Rectangular node\",\n                \"nodeShape\": 1\n            },\n            {\n                \"id\": \"f\",\n                \"text\": \"Rectangular\",\n                \"borderWidth\": 1,\n                \"nodeShape\": 1,\n                \"width\": 300,\n                \"height\": 60\n            },\n            {\n                \"id\": \"f1\",\n                \"text\": \"Fixed\",\n                \"fixed\": true,\n                \"x\": 60,\n                \"y\": 60\n            },\n            {\n                \"id\": \"g\",\n                \"text\": \"Css Flash\",\n                \"styleClass\": \"my-node-flash-style\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"a1\"\n            },\n            {\n                \"from\": \"a1\",\n                \"to\": \"a1-1\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"a2\"\n            },\n            {\n                \"from\": \"a1\",\n                \"to\": \"a1-4\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"f1\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"d\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"f\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"g\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"e\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"e\"\n            }\n        ]\n    },\n    \"options\": {}\n}\n```\n\n## 3.10 多种关系样式\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"A\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"B\"\n            },\n            {\n                \"id\": \"c\",\n                \"text\": \"C\"\n            },\n            {\n                \"id\": \"x\",\n                \"text\": \"X\"\n            },\n            {\n                \"id\": \"d\",\n                \"text\": \"D\",\n                \"nodeShape\": 1,\n                \"width\": 150,\n                \"height\": 100\n            },\n            {\n                \"id\": \"e\",\n                \"text\": \"E\",\n                \"width\": 150,\n                \"height\": 150\n            },\n            {\n                \"id\": \"f\",\n                \"text\": \"F\",\n                \"nodeShape\": 1\n            },\n            {\n                \"id\": \"g\",\n                \"text\": \"G\",\n                \"borderWidth\": 1,\n                \"nodeShape\": 1,\n                \"width\": 300,\n                \"height\": 60\n            },\n            {\n                \"id\": \"h\",\n                \"text\": \"H\",\n                \"fixed\": true,\n                \"x\": 20,\n                \"y\": 20\n            },\n            {\n                \"id\": \"i\",\n                \"text\": \"I\"\n            },\n            {\n                \"id\": \"k1\",\n                \"text\": \"K-1\"\n            },\n            {\n                \"id\": \"k2\",\n                \"text\": \"K-2\"\n            },\n            {\n                \"id\": \"k3\",\n                \"text\": \"K-3\"\n            },\n            {\n                \"id\": \"k4\",\n                \"text\": \"K-4\"\n            },\n            {\n                \"id\": \"m1\",\n                \"text\": \"M-1\"\n            },\n            {\n                \"id\": \"m2\",\n                \"text\": \"M-2\"\n            },\n            {\n                \"id\": \"m3\",\n                \"text\": \"M-3\"\n            },\n            {\n                \"id\": \"m4\",\n                \"text\": \"M-4\"\n            },\n            {\n                \"id\": \"p1\",\n                \"text\": \"P-1\"\n            },\n            {\n                \"id\": \"p2\",\n                \"text\": \"P-2\"\n            },\n            {\n                \"id\": \"p3\",\n                \"text\": \"P-3\"\n            },\n            {\n                \"id\": \"p4\",\n                \"text\": \"P-4\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\",\n                \"text\": \"Text Color\",\n                \"color\": \"#c71585\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\",\n                \"text\": \"Multiple Relations 1\",\n                \"color\": \"rgba(30, 144, 255, 1)\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\",\n                \"text\": \"Multiple Relations 2\",\n                \"color\": \"rgba(255, 140, 0, 1)\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"a\",\n                \"text\": \"Multiple Relations 3\",\n                \"color\": \"#90ee90\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"d\",\n                \"text\": \"Thick Line\",\n                \"lineWidth\": 3\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"e\",\n                \"text\": \"Hide Arrow\",\n                \"showEndArrow\": false\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"e\",\n                \"text\": \"Same Start and End\",\n                \"color\": \"red\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"f\",\n                \"text\": \"Line Style 5\",\n                \"lineShape\": 5\n            },\n            {\n                \"from\": \"f\",\n                \"to\": \"g\",\n                \"text\": \"Line Style 3\",\n                \"lineShape\": 3\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"h\",\n                \"text\": \"Different Color for Line and Text\",\n                \"color\": \"rgba(30, 144, 255, 1)\",\n                \"fontColor\": \"rgba(255, 140, 0, 1)\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"i\",\n                \"text\": \"Polyline\",\n                \"lineShape\": 4\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"h\",\n                \"text\": \"Line Style 1\",\n                \"lineShape\": 6\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"k1\",\n                \"text\": \"Style 2\",\n                \"lineShape\": 2,\n                \"lineWidth\": 2,\n                \"color\": \"#ff8c00\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"k2\",\n                \"text\": \"Style 2\",\n                \"lineShape\": 2,\n                \"lineWidth\": 3,\n                \"color\": \"#ff8c00\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"k3\",\n                \"text\": \"Style 2\",\n                \"lineShape\": 2,\n                \"lineWidth\": 4,\n                \"color\": \"#ff8c00\"\n            },\n            {\n                \"from\": \"d\",\n                \"to\": \"k4\",\n                \"text\": \"Style 2\",\n                \"lineShape\": 2,\n                \"lineWidth\": 5,\n                \"color\": \"#ff8c00\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"m1\",\n                \"text\": \"Style 3\",\n                \"lineShape\": 3,\n                \"lineWidth\": 6,\n                \"color\": \"#00ced1\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"m2\",\n                \"text\": \"Style 3\",\n                \"lineShape\": 3,\n                \"lineWidth\": 7,\n                \"color\": \"#00ced1\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"m3\",\n                \"text\": \"Style 3\",\n                \"lineShape\": 3,\n                \"lineWidth\": 8,\n                \"color\": \"#00ced1\"\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"m4\",\n                \"text\": \"Style 3\",\n                \"lineShape\": 3,\n                \"lineWidth\": 9,\n                \"color\": \"#00ced1\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"p1\",\n                \"text\": \"Style 5\",\n                \"lineShape\": 5,\n                \"lineWidth\": 10,\n                \"color\": \"#ffd700\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"p2\",\n                \"text\": \"Style 5\",\n                \"lineShape\": 5,\n                \"lineWidth\": 11,\n                \"color\": \"#ffd700\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"p3\",\n                \"text\": \"Style 5\",\n                \"lineShape\": 5,\n                \"lineWidth\": 12,\n                \"color\": \"#ffd700\"\n            },\n            {\n                \"from\": \"e\",\n                \"to\": \"p4\",\n                \"text\": \"This line has a very long text that can follow along the line\",\n                \"useTextPath\": true,\n                \"lineShape\": 5,\n                \"color\": \"#ffd700\"\n            },\n            {\n                \"from\": \"i\",\n                \"to\": \"x\",\n                \"text\": \"Link2\",\n                \"color\": \"rgba(255, 120, 0, 1)\",\n                \"lineWidth\": 1,\n                \"data\": {}\n            },\n            {\n                \"from\": \"x\",\n                \"to\": \"i\",\n                \"text\": \"Link3\",\n                \"color\": \"rgba(0, 206, 209, 1)\",\n                \"showStartArrow\": true,\n                \"showEndArrow\": false,\n                \"lineWidth\": 1,\n                \"lineShape\": 1,\n                \"data\": {}\n            },\n            {\n                \"from\": \"x\",\n                \"to\": \"i\",\n                \"text\": \"Link3\",\n                \"color\": \"rgba(144, 240, 144, 0.5)\",\n                \"showEndArrow\": false,\n                \"lineWidth\": 5,\n                \"lineShape\": 3,\n                \"data\": {}\n            }\n        ]\n    },\n    \"options\": {}\n}\n```\n\n## 3.11 tree布局-动画关系\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"a\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"b\"\n            },\n            {\n                \"id\": \"b1\",\n                \"text\": \"b1\"\n            },\n            {\n                \"id\": \"b2\",\n                \"text\": \"b2\"\n            },\n            {\n                \"id\": \"b2-1\",\n                \"text\": \"b2-1\"\n            },\n            {\n                \"id\": \"b2-2\",\n                \"text\": \"b2-2\"\n            },\n            {\n                \"id\": \"c\",\n                \"text\": \"c\"\n            },\n            {\n                \"id\": \"c1\",\n                \"text\": \"c1\"\n            },\n            {\n                \"id\": \"c2\",\n                \"text\": \"c2\"\n            },\n            {\n                \"id\": \"c3\",\n                \"text\": \"c3\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\",\n                \"text\": \"虚线1\",\n                \"dashType\": 1\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b1\",\n                \"text\": \"虚线2\",\n                \"dashType\": 2\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"b2\",\n                \"text\": \"虚线3\",\n                \"dashType\": 3\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-1\",\n                \"text\": \"虚线4\",\n                \"dashType\": 4\n            },\n            {\n                \"from\": \"b2\",\n                \"to\": \"b2-2\",\n                \"text\": \"正常线条\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\",\n                \"text\": \"线条动画1\",\n                \"width\": 10,\n                \"animation\": 1\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c1\",\n                \"text\": \"线条动画2\",\n                \"width\": 10,\n                \"animation\": 2\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c2\",\n                \"text\": \"线条动画3\",\n                \"width\": 10,\n                \"animation\": 3\n            },\n            {\n                \"from\": \"c\",\n                \"to\": \"c3\",\n                \"text\": \"线条动画4\",\n                \"width\": 10,\n                \"animation\": 4\n            }\n        ]\n    },\n    \"options\": {\n        \"layout\": {\n            \"layoutName\": \"tree\",\n            \"from\": \"left\",\n            \"min_per_width\": 310,\n            \"min_per_height\": 70\n        }\n    }\n}\n```\n\n## 3.12 简单实例\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"2\",\n        \"nodes\": [\n            {\n                \"id\": \"1\",\n                \"text\": \"节点-1\",\n                \"myicon\": \"el-icon-star-on\"\n            },\n            {\n                \"id\": \"2\",\n                \"text\": \"节点-2\",\n                \"myicon\": \"el-icon-setting\",\n                \"width\": 100,\n                \"height\": 100\n            },\n            {\n                \"id\": \"3\",\n                \"text\": \"节点-3\",\n                \"myicon\": \"el-icon-setting\"\n            },\n            {\n                \"id\": \"4\",\n                \"text\": \"节点-4\",\n                \"myicon\": \"el-icon-star-on\"\n            },\n            {\n                \"id\": \"6\",\n                \"text\": \"节点-6\",\n                \"myicon\": \"el-icon-setting\"\n            },\n            {\n                \"id\": \"7\",\n                \"text\": \"节点-7\",\n                \"myicon\": \"el-icon-setting\"\n            },\n            {\n                \"id\": \"8\",\n                \"text\": \"节点-8\",\n                \"myicon\": \"el-icon-star-on\"\n            },\n            {\n                \"id\": \"9\",\n                \"text\": \"节点-9\",\n                \"myicon\": \"el-icon-headset\"\n            },\n            {\n                \"id\": \"71\",\n                \"text\": \"节点-71\",\n                \"myicon\": \"el-icon-headset\"\n            },\n            {\n                \"id\": \"72\",\n                \"text\": \"节点-72\",\n                \"myicon\": \"el-icon-s-tools\"\n            },\n            {\n                \"id\": \"73\",\n                \"text\": \"节点-73\",\n                \"myicon\": \"el-icon-star-on\"\n            },\n            {\n                \"id\": \"81\",\n                \"text\": \"节点-81\",\n                \"myicon\": \"el-icon-s-promotion\"\n            },\n            {\n                \"id\": \"82\",\n                \"text\": \"节点-82\",\n                \"myicon\": \"el-icon-s-promotion\"\n            },\n            {\n                \"id\": \"83\",\n                \"text\": \"节点-83\",\n                \"myicon\": \"el-icon-star-on\"\n            },\n            {\n                \"id\": \"84\",\n                \"text\": \"节点-84\",\n                \"myicon\": \"el-icon-s-promotion\"\n            },\n            {\n                \"id\": \"85\",\n                \"text\": \"节点-85\",\n                \"myicon\": \"el-icon-sunny\"\n            },\n            {\n                \"id\": \"91\",\n                \"text\": \"节点-91\",\n                \"myicon\": \"el-icon-sunny\"\n            },\n            {\n                \"id\": \"92\",\n                \"text\": \"节点-82\",\n                \"myicon\": \"el-icon-sunny\"\n            },\n            {\n                \"id\": \"5\",\n                \"text\": \"节点-5\",\n                \"myicon\": \"el-icon-sunny\"\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"7\",\n                \"to\": \"71\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"7\",\n                \"to\": \"72\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"7\",\n                \"to\": \"73\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"81\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"82\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"83\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"84\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"85\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"9\",\n                \"to\": \"91\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"9\",\n                \"to\": \"92\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"1\",\n                \"to\": \"2\",\n                \"text\": \"投资\"\n            },\n            {\n                \"from\": \"3\",\n                \"to\": \"1\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"4\",\n                \"to\": \"2\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"6\",\n                \"to\": \"2\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"7\",\n                \"to\": \"2\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"8\",\n                \"to\": \"2\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"9\",\n                \"to\": \"2\",\n                \"text\": \"高管\"\n            },\n            {\n                \"from\": \"1\",\n                \"to\": \"5\",\n                \"text\": \"投资\"\n            }\n        ]\n    },\n    \"options\": {\n        \"debug\": false,\n        \"defaultJunctionPoint\": \"border\",\n        \"defaultNodeColor\": \"#519633\",\n        \"defaultLineColor\": \"#c8d300\",\n        \"defaultFocusRootNode\": false,\n        \"defaultLineTextOffset_y\": -3,\n        \"layout\": {\n            \"layoutName\": \"center\"\n        }\n    }\n}\n```\n\n## 3.13 产品销售\n```relation-graph\n{\n    \"data\": {\n        \"nodes\": [\n            {\n                \"id\": \"0 0 2 0 0 0 0 3\",\n                \"text\": \"Vins et alcools Chevalier\",\n                \"x\": 764.5931625469138,\n                \"y\": 90.27418906432246\n            },\n            {\n                \"id\": \"0 0 0 0 0 0 0 8\",\n                \"text\": \"Buchanan\",\n                \"x\": 1110,\n                \"y\": 394.5\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 0 55\",\n                \"text\": \"Vins et alcools Chevalier\",\n                \"x\": 510,\n                \"y\": 394.50000000000006\n            }\n        ],\n        \"lines\": [\n            {\n                \"id\": \"0 0 2 0 0 0 0 3 0 0 0 0 0 0 0 8 8 0 5 0 13 31 38 35 35 30 39 38 33 38 31 34 32 35 31 38 34 37 36 38\",\n                \"from\": \"0 0 2 0 0 0 0 3\",\n                \"to\": \"0 0 0 0 0 0 0 8\",\n                \"text\": \"sold_by\",\n                \"color\": \"#9ea5af\",\n                \"lineWidth\": 1\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 0 55 0 0 2 0 0 0 0 3 8 0 7 0 13 31 38 35 35 30 39 38 33 35 36 32 32 31 36 31 32 30 33 32\",\n                \"from\": \"0 0 3 0 0 0 0 55\",\n                \"to\": \"0 0 2 0 0 0 0 3\",\n                \"text\": \"buy\",\n                \"color\": \"#9ea5af\",\n                \"lineWidth\": 1\n            }\n        ]\n    },\n    \"options\": {\n        \"backgroundColor\": \"#eef0f7\"\n    }\n}\n```\n\n## 3.14 数据关系图\n```relation-graph\n{\n    \"data\": {\n        \"rootId\": \"a\",\n        \"nodes\": [\n            {\n                \"id\": \"a\",\n                \"text\": \"A\",\n                \"borderColor\": \"yellow\"\n            },\n            {\n                \"id\": \"b\",\n                \"text\": \"B\",\n                \"color\": \"#43a2f1\",\n                \"fontColor\": \"yellow\"\n            },\n            {\n                \"id\": \"c\",\n                \"text\": \"C\",\n                \"nodeShape\": 1,\n                \"width\": 80,\n                \"height\": 60\n            },\n            {\n                \"id\": \"e\",\n                \"text\": \"E\",\n                \"nodeShape\": 0,\n                \"width\": 150,\n                \"height\": 150\n            }\n        ],\n        \"lines\": [\n            {\n                \"from\": \"a\",\n                \"to\": \"b\",\n                \"text\": \"line 1\",\n                \"color\": \"#43a2f1\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"c\",\n                \"text\": \"line 2\"\n            },\n            {\n                \"from\": \"a\",\n                \"to\": \"e\",\n                \"text\": \"line 3\"\n            },\n            {\n                \"from\": \"b\",\n                \"to\": \"e\",\n                \"color\": \"#67C23A\"\n            }\n        ]\n    },\n    \"options\": {}\n}\n```\n\n## 3.15 保险条款\n```relation-graph\n{\n    \"data\": {\n        \"nodes\": [\n            {\n                \"id\": \"0 0 3 0 0 0 0 0\",\n                \"text\": \"保险期间\",\n                \"x\": 862.0248985156727,\n                \"y\": 452.5186738867545\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 0 0\",\n                \"text\": \"安邦一年团体定期寿险\",\n                \"x\": 1499.8100492512156,\n                \"y\": 245.81634752880672\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 0 2\",\n                \"text\": \"投保范围\",\n                \"x\": 1860,\n                \"y\": 413.5\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 0 3\",\n                \"text\": \"责任免除\",\n                \"x\": 1341.7627457812105,\n                \"y\": 1126.792387221365\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 0 1\",\n                \"text\": \"保险责任\",\n                \"x\": 923.7718505538105,\n                \"y\": 823.9910817518792\n            },\n            {\n                \"id\": \"0 0 0 0 0 0 0 33\",\n                \"text\": \"安邦人寿保险股份有限公司\",\n                \"x\": 932.4426669730889,\n                \"y\": -70.69302131574867\n            },\n            {\n                \"id\": \"0 0 2 0 0 0 0 22\",\n                \"text\": \"人寿保险-定期寿险\",\n                \"x\": 1341.7627457812105,\n                \"y\": -299.7923872213653\n            },\n            {\n                \"id\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"健康保险-非个人税收优惠型健康保险-疾病保险-防癌保险\",\n                \"x\": -757.29226193334,\n                \"y\": 343.17835684551056\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 18 f6\",\n                \"text\": \"君龙附加好安心防癌疾病保险\",\n                \"x\": 603.7888223362356,\n                \"y\": 202.93253180648563\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 42\",\n                \"text\": \"天安人寿康护防癌疾病保险\",\n                \"x\": 395.5332551066628,\n                \"y\": 866.3677246169981\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 48\",\n                \"text\": \"天安人寿附加天福防癌疾病保险\",\n                \"x\": -369.0439775175826,\n                \"y\": 735.2236658282169\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d1\",\n                \"text\": \"和谐健康之道定期防癌疾病保险\",\n                \"x\": -677.6331970275463,\n                \"y\": -44.572119683967095\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2\",\n                \"text\": \"和谐健康之道终身防癌疾病保险\",\n                \"x\": 270.99314088847774,\n                \"y\": -258.1280777943866\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 dd 28\",\n                \"text\": \"保险期间\",\n                \"x\": 818.2099352788887,\n                \"y\": -302.2602613521889\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 dd 29\",\n                \"text\": \"保险责任\",\n                \"x\": 902.5944221999744,\n                \"y\": -544.9253843563093\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 dd 2a\",\n                \"text\": \"投保范围\",\n                \"x\": -356.46566569746756,\n                \"y\": -206.08627479916558\n            },\n            {\n                \"id\": \"0 0 3 0 0 0 dd 2b\",\n                \"text\": \"责任免除\",\n                \"x\": -256.7512768757618,\n                \"y\": -571.8505762907878\n            },\n            {\n                \"id\": \"0 0 0 0 0 0 0 24\",\n                \"text\": \"和谐健康保险股份有限公司\",\n                \"x\": 581.7742146866593,\n                \"y\": -844.3040242927982\n            }\n        ],\n        \"lines\": [\n            {\n                \"id\": \"0 0 5 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 0 0\",\n                \"to\": \"0 0 3 0 0 0 0 0\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 0 0 0 0 3 0 0 0 0 2 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 0 0\",\n                \"to\": \"0 0 3 0 0 0 0 2\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 0 0 0 0 3 0 0 0 0 3 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 0 0\",\n                \"to\": \"0 0 3 0 0 0 0 3\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 0 0 0 0 3 0 0 0 0 1 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 0 0\",\n                \"to\": \"0 0 3 0 0 0 0 1\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 0 0 0 0 0 33 0 0 5 0 0 0 0 0 0 0 6\",\n                \"from\": \"0 0 0 0 0 0 0 33\",\n                \"to\": \"0 0 5 0 0 0 0 0\",\n                \"text\": \"sale\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 0 0 0 0 2 0 0 0 0 22 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 0 0\",\n                \"to\": \"0 0 2 0 0 0 0 22\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 18 f6 0 0 2 0 0 0 0 5 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 18 f6\",\n                \"to\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 42 0 0 2 0 0 0 0 5 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 19 42\",\n                \"to\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 48 0 0 2 0 0 0 0 5 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 19 48\",\n                \"to\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d1 0 0 2 0 0 0 0 5 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 19 d1\",\n                \"to\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2 0 0 2 0 0 0 0 5 0 0 8\",\n                \"from\": \"0 0 5 0 0 0 19 d2\",\n                \"to\": \"0 0 2 0 0 0 0 5\",\n                \"text\": \"belong\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2 0 0 3 0 0 0 dd 28 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 19 d2\",\n                \"to\": \"0 0 3 0 0 0 dd 28\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2 0 0 3 0 0 0 dd 29 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 19 d2\",\n                \"to\": \"0 0 3 0 0 0 dd 29\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2 0 0 3 0 0 0 dd 2a 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 19 d2\",\n                \"to\": \"0 0 3 0 0 0 dd 2a\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 5 0 0 0 19 d2 0 0 3 0 0 0 dd 2b 0 0 a\",\n                \"from\": \"0 0 5 0 0 0 19 d2\",\n                \"to\": \"0 0 3 0 0 0 dd 2b\",\n                \"text\": \"include\"\n            },\n            {\n                \"id\": \"0 0 0 0 0 0 0 24 0 0 5 0 0 0 19 d2 0 0 6\",\n                \"from\": \"0 0 0 0 0 0 0 24\",\n                \"to\": \"0 0 5 0 0 0 19 d2\",\n                \"text\": \"sale\"\n            }\n        ]\n    },\n    \"options\": {\n        \"backgroundColor\": \"#eef0f7\"\n    }\n}\n```\n\n## 3.16 其它类型\n请参见: \n  [Github: mermaid/README.md](https://github.com/mermaid-js/mermaid/blob/develop/README.zh-CN.md)\n  [mermaid使用指南](https://mermaid.js.org/intro/)\n\n\"\"\" + \"\"\"\n\n# 4. 脑图展示\n当输出的内容中包含Markdown语法的标题时， 可以点击对话框右下角对应Icon进行展示\n\n![img](data:image/png;base64,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)\n\n## 4.1 二级标题1\n### 4.1.1 三级标题1\n#### 四级标题1\n#### 四级标题2\n## 4.2 二级标题2\n### 4.2.1 三级标题2\n\n\"\"\" + \"\"\"\n\n# 5. Markdown 原生表格展示\n| Syntax      | Description | Test Text     |\n| :---        |    :----:   |          ---: |\n| Header      | Title       | Here's this   |\n| Paragraph   | Text        | And more      |\n\n\"\"\" + f\"\"\"\n\n# 6. 附件为图片嵌入\n\n## 6.1 Markdown语法\n### 1. base64格式\n![img](data:{mime_type};base64,{data['content']})\n### 2. sfs url格式\n![img]({data['url']})\n\n## 6.2 HTML 标签（可控制图像大小）\n### 1. base64 \n<img src=\"data:{mime_type};base64,{data['content']}\"  style=\"width:100px; heigh:100px\" />\n\n### 2. sfs url格式\n<img src=\"{data['url']}\" style=\"width:200px;heigh:200px\" />\n\"\"\" +  \"\"\" \n\n# 7 Charts 语法\n图表配置规范\n* 必须要指定代码块的language 为 charts\n* 图表配置，可以参考示例 https://echarts.apache.org/examples/zh/index.html\n* 目前只支持完全符合 JSON 规范的内容，不要书写函数、判断条件等\n* 图表大小：宽度100%， 高度 400px。如果复制的配置里包含图表大小的，请修改为合理范围\n\n## 7.1 折柱混合图示例\n~~~charts\n{\n  xAxis: {\n    type: 'category',\n    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n  },\n  yAxis: {\n    type: 'value'\n  },\n  series: [\n    {\n      data: [150, 230, 224, 218, 135, 147, 260],\n      type: 'line'\n    },\n     {\n      data: [130, 270, 294, 208, 145, 187, 210],\n      type: 'bar'\n    }\n  ]\n}\n~~~\n\n## 7.2 玫瑰饼图示例\n~~~charts\n{\n  legend: {\n    top: 'bottom'\n  },\n  toolbox: {\n    show: true,\n    feature: {\n      saveAsImage: { show: true }\n    }\n  },\n  series: [\n    {\n      name: 'Nightingale Chart',\n      type: 'pie',\n      radius: [20, 100],\n      center: ['50%', '50%'],\n      roseType: 'area',\n      itemStyle: {\n        borderRadius: 8\n      },\n      data: [\n        { value: 40, name: 'rose 1' },\n        { value: 38, name: 'rose 2' },\n        { value: 32, name: 'rose 3' },\n        { value: 30, name: 'rose 4' },\n        { value: 28, name: 'rose 5' },\n        { value: 26, name: 'rose 6' },\n        { value: 22, name: 'rose 7' },\n        { value: 18, name: 'rose 8' }\n      ]\n    }\n  ]\n}\n~~~\n\n## 7.3 雷达图示例\n~~~charts\n{\n  backgroundColor: '#161627',\n  title: {\n    text: 'AQI - Radar',\n    left: 'center',\n    textStyle: {\n      color: '#eee'\n    }\n  },\n  legend: {\n    bottom: 5,\n    data: ['Beijing', 'Shanghai', 'Guangzhou'],\n    itemGap: 20,\n    textStyle: {\n      color: '#fff',\n      fontSize: 14\n    },\n    selectedMode: 'single'\n  },\n  radar: {\n    indicator: [\n      { name: 'AQI', max: 300 },\n      { name: 'PM2.5', max: 250 },\n      { name: 'PM10', max: 300 },\n      { name: 'CO', max: 5 },\n      { name: 'NO2', max: 200 },\n      { name: 'SO2', max: 100 }\n    ],\n    shape: 'circle',\n    splitNumber: 5,\n    axisName: {\n      color: 'rgb(238, 197, 102)'\n    },\n    splitLine: {\n      lineStyle: {\n        color: [\n          'rgba(238, 197, 102, 0.1)',\n          'rgba(238, 197, 102, 0.2)',\n          'rgba(238, 197, 102, 0.4)',\n          'rgba(238, 197, 102, 0.6)',\n          'rgba(238, 197, 102, 0.8)',\n          'rgba(238, 197, 102, 1)'\n        ]\n      }\n    },\n    splitArea: {\n      show: false\n    },\n    axisLine: {\n      lineStyle: {\n        color: 'rgba(238, 197, 102, 0.5)'\n      }\n    }\n  },\n  series: [\n    {\n      name: 'Beijing',\n      type: 'radar',\n      lineStyle: {\n  width: 1,\n  opacity: 0.5\n},\n      data: [\n  [55, 9, 56, 0.46, 18, 6, 1],\n  [25, 11, 21, 0.65, 34, 9, 2],\n  [56, 7, 63, 0.3, 14, 5, 3],\n  [33, 7, 29, 0.33, 16, 6, 4],\n  [42, 24, 44, 0.76, 40, 16, 5],\n  [82, 58, 90, 1.77, 68, 33, 6],\n  [74, 49, 77, 1.46, 48, 27, 7],\n  [78, 55, 80, 1.29, 59, 29, 8],\n  [267, 216, 280, 4.8, 108, 64, 9],\n  [185, 127, 216, 2.52, 61, 27, 10],\n  [39, 19, 38, 0.57, 31, 15, 11],\n  [41, 11, 40, 0.43, 21, 7, 12],\n  [64, 38, 74, 1.04, 46, 22, 13],\n  [108, 79, 120, 1.7, 75, 41, 14],\n  [108, 63, 116, 1.48, 44, 26, 15],\n  [33, 6, 29, 0.34, 13, 5, 16],\n  [94, 66, 110, 1.54, 62, 31, 17],\n  [186, 142, 192, 3.88, 93, 79, 18],\n  [57, 31, 54, 0.96, 32, 14, 19],\n  [22, 8, 17, 0.48, 23, 10, 20],\n  [39, 15, 36, 0.61, 29, 13, 21],\n  [94, 69, 114, 2.08, 73, 39, 22],\n  [99, 73, 110, 2.43, 76, 48, 23],\n  [31, 12, 30, 0.5, 32, 16, 24],\n  [42, 27, 43, 1, 53, 22, 25],\n  [154, 117, 157, 3.05, 92, 58, 26],\n  [234, 185, 230, 4.09, 123, 69, 27],\n  [160, 120, 186, 2.77, 91, 50, 28],\n  [134, 96, 165, 2.76, 83, 41, 29],\n  [52, 24, 60, 1.03, 50, 21, 30],\n  [46, 5, 49, 0.28, 10, 6, 31]\n],\n      symbol: 'none',\n      itemStyle: {\n        color: '#F9713C'\n      },\n      areaStyle: {\n        opacity: 0.1\n      }\n    },\n    {\n      name: 'Shanghai',\n      type: 'radar',\n      lineStyle: {\n  width: 1,\n  opacity: 0.5\n},\n      data: [\n  [91, 45, 125, 0.82, 34, 23, 1],\n  [65, 27, 78, 0.86, 45, 29, 2],\n  [83, 60, 84, 1.09, 73, 27, 3],\n  [109, 81, 121, 1.28, 68, 51, 4],\n  [106, 77, 114, 1.07, 55, 51, 5],\n  [109, 81, 121, 1.28, 68, 51, 6],\n  [106, 77, 114, 1.07, 55, 51, 7],\n  [89, 65, 78, 0.86, 51, 26, 8],\n  [53, 33, 47, 0.64, 50, 17, 9],\n  [80, 55, 80, 1.01, 75, 24, 10],\n  [117, 81, 124, 1.03, 45, 24, 11],\n  [99, 71, 142, 1.1, 62, 42, 12],\n  [95, 69, 130, 1.28, 74, 50, 13],\n  [116, 87, 131, 1.47, 84, 40, 14],\n  [108, 80, 121, 1.3, 85, 37, 15],\n  [134, 83, 167, 1.16, 57, 43, 16],\n  [79, 43, 107, 1.05, 59, 37, 17],\n  [71, 46, 89, 0.86, 64, 25, 18],\n  [97, 71, 113, 1.17, 88, 31, 19],\n  [84, 57, 91, 0.85, 55, 31, 20],\n  [87, 63, 101, 0.9, 56, 41, 21],\n  [104, 77, 119, 1.09, 73, 48, 22],\n  [87, 62, 100, 1, 72, 28, 23],\n  [168, 128, 172, 1.49, 97, 56, 24],\n  [65, 45, 51, 0.74, 39, 17, 25],\n  [39, 24, 38, 0.61, 47, 17, 26],\n  [39, 24, 39, 0.59, 50, 19, 27],\n  [93, 68, 96, 1.05, 79, 29, 28],\n  [188, 143, 197, 1.66, 99, 51, 29],\n  [174, 131, 174, 1.55, 108, 50, 30],\n  [187, 143, 201, 1.39, 89, 53, 31]\n],\n      symbol: 'none',\n      itemStyle: {\n        color: '#B3E4A1'\n      },\n      areaStyle: {\n        opacity: 0.05\n      }\n    },\n    {\n      name: 'Guangzhou',\n      type: 'radar',\n      lineStyle: {\n  width: 1,\n  opacity: 0.5\n},\n      data: [\n  [26, 37, 27, 1.163, 27, 13, 1],\n  [85, 62, 71, 1.195, 60, 8, 2],\n  [78, 38, 74, 1.363, 37, 7, 3],\n  [21, 21, 36, 0.634, 40, 9, 4],\n  [41, 42, 46, 0.915, 81, 13, 5],\n  [56, 52, 69, 1.067, 92, 16, 6],\n  [64, 30, 28, 0.924, 51, 2, 7],\n  [55, 48, 74, 1.236, 75, 26, 8],\n  [76, 85, 113, 1.237, 114, 27, 9],\n  [91, 81, 104, 1.041, 56, 40, 10],\n  [84, 39, 60, 0.964, 25, 11, 11],\n  [64, 51, 101, 0.862, 58, 23, 12],\n  [70, 69, 120, 1.198, 65, 36, 13],\n  [77, 105, 178, 2.549, 64, 16, 14],\n  [109, 68, 87, 0.996, 74, 29, 15],\n  [73, 68, 97, 0.905, 51, 34, 16],\n  [54, 27, 47, 0.592, 53, 12, 17],\n  [51, 61, 97, 0.811, 65, 19, 18],\n  [91, 71, 121, 1.374, 43, 18, 19],\n  [73, 102, 182, 2.787, 44, 19, 20],\n  [73, 50, 76, 0.717, 31, 20, 21],\n  [84, 94, 140, 2.238, 68, 18, 22],\n  [93, 77, 104, 1.165, 53, 7, 23],\n  [99, 130, 227, 3.97, 55, 15, 24],\n  [146, 84, 139, 1.094, 40, 17, 25],\n  [113, 108, 137, 1.481, 48, 15, 26],\n  [81, 48, 62, 1.619, 26, 3, 27],\n  [56, 48, 68, 1.336, 37, 9, 28],\n  [82, 92, 174, 3.29, 0, 13, 29],\n  [106, 116, 188, 3.628, 101, 16, 30],\n  [118, 50, 0, 1.383, 76, 11, 31]\n],\n      symbol: 'none',\n      itemStyle: {\n        color: 'rgb(238, 197, 102)'\n      },\n      areaStyle: {\n        opacity: 0.05\n      }\n    }\n  ]\n}\n~~~\n\"\"\""}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-d66502c5-826a-4c17-917e-5f667710d206d66502c5-826a-4c17-917e-5f667710d206@@OutPut-912cf276-9a08-4f90-b269-4d4a9cf0b583912cf276-9a08-4f90-b269-4d4a9cf0b583@@Content", "source": "d66502c5-826a-4c17-917e-5f667710d206", "source_param": "d66502c5-826a-4c17-917e-5f667710d206@@OutPut", "target": "912cf276-9a08-4f90-b269-4d4a9cf0b583", "target_param": "912cf276-9a08-4f90-b269-4d4a9cf0b583@@Content"}], "viewport": {"x": 0, "y": 0, "zoom": 1}}, "created_time": 0, "updated_time": 0}