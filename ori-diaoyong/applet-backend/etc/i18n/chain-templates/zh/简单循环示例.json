{"id": "简单循环示例.json", "name": "简单循环示例", "desc": "在应用链内部通过循环跳转+条件判断算子，实现循环逻辑。", "template_group_key": "CycleAndBranch", "template": {"nodes": [{"id": "63487cfd-4c23-4033-bd5a-15aa22416415", "name": "文本输入-输入数值", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "文本输入", "desc": "用于将输入的文本原样输出", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "文本输入", "desc": "文本输入,支持类型: Sync-String: \"text\"。", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"63487cfd-4c23-4033-bd5a-15aa22416415\",\"position\":{\"x\":122.12275750260858,\"y\":210.4526550925716},\"positionAbsolute\":{\"x\":122.12275750260858,\"y\":210.4526550925716},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "c8baface-e33d-4071-8d71-982536676948", "name": "Python-自增循环", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python代码", "desc": "可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用python代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python代码", "desc": "python代码，点击可查看或编辑代码", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "python代码中handler函数返回的数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"c8baface-e33d-4071-8d71-982536676948\",\"position\":{\"x\":552.8463971599524,\"y\":125.69279431990475},\"positionAbsolute\":{\"x\":552.8463971599524,\"y\":125.69279431990475},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n\n    return str(int(data) + 1)\n\n"}, "sub_chain_base_info": null}, {"id": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "name": "条件判断-大于10", "widget_id": "WidgetKeyConditionJudge", "widget_detail": {"id": "WidgetKeyConditionJudge", "name": "条件判断", "desc": "把数据流分叉成if分支与else分支，根据输入的条件决定数据流向的分支", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyConditionJudge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入数据", "desc": "待执行判断条件的数据,支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "条件成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "判断条件", "desc": "\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\n可以使用下面的语法表示判断条件, 更复杂的判断条件请参考Jsonnet语法\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"\n", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "条件不成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":236,\"id\":\"038d0694-52fb-4ac8-85b1-acadf0e9f5de\",\"position\":{\"x\":1023.2885419478077,\"y\":109.3583271542933},\"positionAbsolute\":{\"x\":1023.2885419478077,\"y\":109.3583271542933},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "std.parseInt(input) > 4"}, "sub_chain_base_info": null}, {"id": "71000714-ce7c-400c-a5d6-bd42b3196bce", "name": "循环跳转", "widget_id": "WidgetKeyGoTo", "widget_detail": {"id": "WidgetKeyGoTo", "name": "循环跳转", "desc": "用于改变数据的流向，把数据传输到之前的算子实现循环", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyGoTo", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入数据", "desc": "待改变流向的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "maxLoopRounds", "name": "最大轮次数", "desc": "最大循环次数，要求是正整数", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":208,\"id\":\"71000714-ce7c-400c-a5d6-bd42b3196bce\",\"position\":{\"x\":656.0459751708086,\"y\":474.0213824860109},\"positionAbsolute\":{\"x\":656.0459751708086,\"y\":474.0213824860109},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"maxLoopRounds": 5}, "sub_chain_base_info": null}, {"id": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1", "name": "文本呈现", "widget_id": "WidgetKeyTextShow", "widget_detail": {"id": "WidgetKeyTextShow", "name": "文本呈现", "desc": "用于对上游输出的文本增加自定义前后缀", "group": "WidgetGroupOutput", "oriWidgetKey": "WidgetKeyTextShow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "文本输入", "desc": "文本输入,支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Prefix", "name": "前缀", "desc": "前缀", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Suffix", "name": "后缀", "desc": "后缀", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "输出,支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"9ba4aa6d-1a9a-4091-bcb8-b297a99418b1\",\"position\":{\"x\":1621.697363895553,\"y\":193.9490332643993},\"positionAbsolute\":{\"x\":1621.697363895553,\"y\":193.9490332643993},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Prefix": "循环结果：\n```json\n", "Suffix": "\n```"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-63487cfd-4c23-4033-bd5a-15aa2241641563487cfd-4c23-4033-bd5a-15aa22416415@@OutPut-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@Content", "source": "63487cfd-4c23-4033-bd5a-15aa22416415", "source_param": "63487cfd-4c23-4033-bd5a-15aa22416415@@OutPut", "target": "c8baface-e33d-4071-8d71-982536676948", "target_param": "c8baface-e33d-4071-8d71-982536676948@@Content"}, {"id": "reactflow__edge-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@OutPut-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@Input", "source": "c8baface-e33d-4071-8d71-982536676948", "source_param": "c8baface-e33d-4071-8d71-982536676948@@OutPut", "target": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "target_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@Input"}, {"id": "reactflow__edge-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutElse-71000714-ce7c-400c-a5d6-bd42b3196bce71000714-ce7c-400c-a5d6-bd42b3196bce@@Input", "source": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "source_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutElse", "target": "71000714-ce7c-400c-a5d6-bd42b3196bce", "target_param": "71000714-ce7c-400c-a5d6-bd42b3196bce@@Input"}, {"id": "reactflow__edge-71000714-ce7c-400c-a5d6-bd42b3196bce71000714-ce7c-400c-a5d6-bd42b3196bce@@OutPut-c8baface-e33d-4071-8d71-982536676948c8baface-e33d-4071-8d71-982536676948@@Content", "source": "71000714-ce7c-400c-a5d6-bd42b3196bce", "source_param": "71000714-ce7c-400c-a5d6-bd42b3196bce@@OutPut", "target": "c8baface-e33d-4071-8d71-982536676948", "target_param": "c8baface-e33d-4071-8d71-982536676948@@Content"}, {"id": "reactflow__edge-038d0694-52fb-4ac8-85b1-acadf0e9f5de038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutIF-9ba4aa6d-1a9a-4091-bcb8-b297a99418b19ba4aa6d-1a9a-4091-bcb8-b297a99418b1@@Input", "source": "038d0694-52fb-4ac8-85b1-acadf0e9f5de", "source_param": "038d0694-52fb-4ac8-85b1-acadf0e9f5de@@OutPutIF", "target": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1", "target_param": "9ba4aa6d-1a9a-4091-bcb8-b297a99418b1@@Input"}], "viewport": {"x": -42.694602975749035, "y": 71.031247713129, "zoom": 0.7937005035983871}}, "created_time": 0, "updated_time": 0}