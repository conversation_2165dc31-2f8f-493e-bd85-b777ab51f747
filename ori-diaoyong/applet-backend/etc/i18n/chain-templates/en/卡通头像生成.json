{"id": "卡通头像生成.json", "name": "Cartoon Avatar Generation", "desc": "Input the job position, generate a cartoon avatar for this position.", "template_group_key": "MultiModality", "template": {"nodes": [{"id": "8146e8a4-039b-4609-b6bf-dfe29f4bd8fd", "name": "Text Presentation", "widget_id": "WidgetKeyTextShow", "widget_detail": {"id": "WidgetKeyTextShow", "name": "Text Presentation", "desc": "Used to add custom prefixes and suffixes to the upstream output text", "group": "WidgetGroupOutput", "oriWidgetKey": "WidgetKeyTextShow", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text Input", "desc": "Text input, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Prefix", "name": "Prefix", "desc": "Prefix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Suffix", "name": "Suffix", "desc": "Suffix", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Output, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"8146e8a4-039b-4609-b6bf-dfe29f4bd8fd\",\"position\":{\"x\":494.9510673613065,\"y\":-35.00079050208896},\"positionAbsolute\":{\"x\":494.9510673613065,\"y\":-35.00079050208896},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Prefix": "![pic](", "Suffix": ")"}, "sub_chain_base_info": null}, {"id": "34bce451-17f7-4303-8394-c3400faf52dd", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"height\":133,\"id\":\"34bce451-17f7-4303-8394-c3400faf52dd\",\"position\":{\"x\":-516.9794873741322,\"y\":0.1546242173031977},\"positionAbsolute\":{\"x\":-516.9794873741322,\"y\":0.1546242173031977},\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "b5c89e42-6e78-44d0-87ad-461607731972", "name": "Text-to-Image", "widget_id": "WidgetKeyTextToImage", "widget_detail": {"id": "WidgetKeyTextToImage", "name": "Text-to-Image", "desc": "Generate corresponding images based on the analysis of text content", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyTextToImage", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Service", "datasource": "{\"kind\":\"<PERSON><PERSON>EL_KIND_MULTI\",\"subKind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Generated image URL, supported type: Sync-SfsUrlString: \"sfs:///tenants/llmops-assets/projs/assets/a.text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-SfsUrlString"]}}]}, "ui": "{\"dragging\":false,\"height\":214,\"id\":\"b5c89e42-6e78-44d0-87ad-461607731972\",\"position\":{\"x\":-5.665824702102327,\"y\":-75.87160550944182},\"positionAbsolute\":{\"x\":-5.665824702102327,\"y\":-75.87160550944182},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"f1310b23-f979-4188-a1b4-c7a2c9a1e59a\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"model_name\":\"文生图\",\"release_name\":\"v1\",\"release_version\":\"v1\",\"model_id\":\"MWH-MODEL-csmrjpvo3t2rqlgqsdc0\",\"release_id\":\"MWH-MODEL-RELEASE-csmrnifo3t2rqlgqsdl0\",\"inference_params\":[],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-f1310b23-f979-4188-a1b4-c7a2c9a1e59a\",\"name\":\"Golden-文生图\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-f1310b23-f979-4188-a1b4-c7a2c9a1e59a/8011/openai/v1/images/generations?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"文生图\",\"name_for_human\":\"图像生成模型:文生图\",\"desc\":\"按照描述生成图像\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"对图片的描述\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1731060993000\",\"reference_model\":{\"id\":\"MWH-MODEL-csmrjpvo3t2rqlgqsdc0\",\"name\":\"文生图\",\"domain\":{\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"\",\"user_id\":\"xiang.zhang\",\"thumbnail\":\"sfs://tenants/llmops-assets/projs/assets/avatar/b3ff1f27-e881-403b-9f45-da494d4e69bc_w.jpg\",\"is_public\":false,\"create_time_ms\":\"1731049959568\",\"update_time_ms\":\"1731057066086\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":1,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-csmrnifo3t2rqlgqsdl0\",\"name\":\"v1\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-csmrjpvo3t2rqlgqsdc0/releases/MWH-MODEL-RELEASE-csmrnifo3t2rqlgqsdl0/model-files\",\"model_id\":\"MWH-MODEL-csmrjpvo3t2rqlgqsdc0\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"17821271814\",\"create_time_ms\":\"1731050441751\",\"update_time_ms\":\"1731050441838\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":null,\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"xiang.zhang\",\"training_template\":null}],\"usage_count\":{\"deploys_count\":0,\"views_count\":67,\"downloads_count\":1,\"invokes_count\":0,\"trainings_count\":0,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-csmrjpvo3t2rqlgqsdc0\"},\"disk_usage\":17821293000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-csmrnifo3t2rqlgqsdl0\",\"name\":\"v1\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-csmrjpvo3t2rqlgqsdc0/releases/MWH-MODEL-RELEASE-csmrnifo3t2rqlgqsdl0/model-files\",\"model_id\":\"MWH-MODEL-csmrjpvo3t2rqlgqsdc0\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"17821292985\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1731060898917\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"9defb61a-15eb-4296-a0cf-083e1a3cf272\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\",\"number_range\":null,\"default_value\":\"kolors\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"xiang.zhang\",\"training_template\":null},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1731062324000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"文生图\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v1\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"v1\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_MULTI\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_MULTI\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Golden-文生图\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#029be6\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"f1310b23-f979-4188-a1b4-c7a2c9a1e59a\"}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-b5c89e42-6e78-44d0-87ad-461607731972b5c89e42-6e78-44d0-87ad-461607731972@@OutPut-8146e8a4-039b-4609-b6bf-dfe29f4bd8fd8146e8a4-039b-4609-b6bf-dfe29f4bd8fd@@Input", "source": "b5c89e42-6e78-44d0-87ad-461607731972", "source_param": "b5c89e42-6e78-44d0-87ad-461607731972@@OutPut", "target": "8146e8a4-039b-4609-b6bf-dfe29f4bd8fd", "target_param": "8146e8a4-039b-4609-b6bf-dfe29f4bd8fd@@Input"}, {"id": "reactflow__edge-34bce451-17f7-4303-8394-c3400faf52dd34bce451-17f7-4303-8394-c3400faf52dd@@OutPut-b5c89e42-6e78-44d0-87ad-461607731972b5c89e42-6e78-44d0-87ad-461607731972@@Text", "source": "34bce451-17f7-4303-8394-c3400faf52dd", "source_param": "34bce451-17f7-4303-8394-c3400faf52dd@@OutPut", "target": "b5c89e42-6e78-44d0-87ad-461607731972", "target_param": "b5c89e42-6e78-44d0-87ad-461607731972@@Text"}], "viewport": {"x": 501.8007548295608, "y": 225.89627481053833, "zoom": 0.6708211124411306}}, "created_time": 0, "updated_time": 0}