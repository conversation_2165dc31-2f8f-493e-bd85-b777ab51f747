package knowledge_base

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

// InnerHandler 用于处理内部知识库的入库与检索
// 可以根据配置切换不同的向量和全文引擎
type InnerHandler struct {
	*HandlerBase
	vector    VectorHandler
	fulltext  FullTextHandler
	docEngine *DocEngineHandler
}

func (h *InnerHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.DocumentInfo, 0, len(docs))

	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE ||
		h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		docEngineDocs, err := h.docEngine.ListDocuments(ctx)
		if err != nil {
			return nil, err
		}
		for _, doc := range docEngineDocs.Result {
			ret = append(ret, doc)
		}
	} else {
		for _, doc := range docs {
			strategyOrigin := new(pb.StrategyOrigin)
			if doc.DocProcessingConfig != nil {
				strategyOrigin = &doc.DocProcessingConfig.DocProcessingStrategyOrigin
			}
			di := &pb.DocumentInfo{
				Doc:            doc.ToPb(),
				Prog:           GetDocImportTaskManager().Get(doc.Id).DocumentProcessingProgress,
				StrategyOrigin: *strategyOrigin,
			}
			if doc.Stage == pb.DocumentTaskStage_WAITING_EXAMINING || doc.Stage == pb.DocumentTaskStage_WAITING {
				di.Prog.Stage = doc.Stage
			}
			di.NumChunks = doc.NumChunks
			di.NumSuccessChunks = doc.NumSuccessChunks
			ret = append(ret, di)
		}
	}
	return &pb.ListDocumentsRsp{Result: ret}, nil
}

func (h *InnerHandler) CountDocuments(ctx context.Context) (int32, error) {
	return GetDocumentStore().CountByKnowledgeBase(h.KnowledgeBase.Id)
}

func (h *InnerHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	// 平铺展开树即可
	//    [dir]知识库名称
	//           |
	// [file]doc1, [file]doc2, ...

	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE || h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.GetDocumentTree(ctx)
	} else {
		root := new(pb.DocumentTree)
		root.Node = &pb.DocumentNode{
			Category: pb.DocumentNodeCategory_DIR,
			Id:       h.KnowledgeBase.Id,
			Name:     h.KnowledgeBase.Name,
		}

		docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
		if err != nil {
			return nil, err
		}
		for _, doc := range docs {
			if doc.Stage == pb.DocumentTaskStage_INDEXING_DONE {
				subTree := &pb.DocumentTree{
					Node: &pb.DocumentNode{
						Category: pb.DocumentNodeCategory_FILE,
						Id:       doc.Id,
						Name:     doc.Name,
					},
				}
				root.Children = append(root.Children, subTree)
			}
		}
		if len(docs) == 0 {
			subTree := &pb.DocumentTree{
				Node: &pb.DocumentNode{
					Category: pb.DocumentNodeCategory_FILE,
					Id:       "全量文本",
					Name:     "全量文本",
				},
			}
			root.Children = append(root.Children, subTree)
		}
		return &pb.GetDocumentTreeRsp{Tree: root}, nil
	}
}

func (h *InnerHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	ret := make([]*pb.ChunkInfo, 0)
	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.ListDocumentChunks(ctx, docId, pageReq)
	} else {
		chunks, err := GetChunkStore(h.KnowledgeBase.ProjectId).LoadByDocument(docId)
		if err != nil {
			return nil, err
		}
		for _, chunk := range chunks {
			ret = append(ret, &pb.ChunkInfo{
				Chunk: chunk.ToPb(),
			})
		}
		rsp := &pb.ListDocumentChunksRsp{
			Result: ret,
			Total:  int32(len(ret)),
		}
		return rsp, nil
	}
}

func (h *InnerHandler) ListDocumentChunksByReq(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE ||
		h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.ListDocumentChunksByReq(ctx, req)
	} else {
		rsp, err := GetChunkStore(h.KnowledgeBase.ProjectId).LoadByRequest(req)
		if err != nil {
			return nil, err
		}
		return rsp, nil
	}
}

func (h *InnerHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented") // TODO: Implement
}

func (h *InnerHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (rsp *pb.RetrieveKnowledgeBaseRsp, err error) {
	if req.RetrievalConfig == nil {
		req.RetrievalConfig = h.KnowledgeBase.RetrievalConfig
	}
	if req.RetrievalConfig == nil {
		return nil, stderr.BadRequest.Error("retrieval config is nil")
	}
	if helper.VersionCheck(h.KnowledgeBase.Version, "2.0.0") >= 0 {
		if req.RetrievalConfig.Strategy != pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE {
			req, err = h.beforeRecall(req)
			if err != nil {
				return nil, stderr.Internal.Cause(err, "change short ids error.")
			}
			if len(req.DocRange) == 0 {
				// 现在经过过滤为空只可能是文档都被过滤掉了，返回空结果
				return &pb.RetrieveKnowledgeBaseRsp{Request: req}, nil
			}
		}
	}
	switch req.RetrievalConfig.Strategy {
	case pb.KnowledgeBaseRetrieveStrategy_VECTOR:
		rsp, err = h.vector.Recall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT:
		rsp, err = h.fulltext.Recall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_MIXED:
		rsp, err = h.MixedRecall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE:
		rsp, err = h.docEngine.Recall(ctx, req)
	default:
		err = stderr.BadRequest.Error("unsupported retrieval strategy")
	}

	if err != nil {
		return nil, err
	}

	// set kb name and doc name
	if req.RetrievalConfig.Strategy == pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE {
		kbName := h.KnowledgeBase.Name
		for _, item := range rsp.Result {
			item.KnowledgeBaseName = kbName
		}
	} else {
		rsp.Result, err = fillRetrieveResults(h.KnowledgeBase.ProjectId, rsp.Result)
		if err != nil {
			return nil, err
		}

		// set kb name and doc name
		docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
		if err != nil {
			stdlog.Errorf("InnerHandler Recall: GetDocumentStore.LoadByKnowledgeBase: %v", err)
		}
		docsMp := make(map[string]*models.Document, len(docs))
		for _, doc := range docs {
			docsMp[doc.Id] = doc
		}
		kbName := h.KnowledgeBase.Name
		for _, item := range rsp.Result {
			item.KnowledgeBaseName = kbName
			item.DocName = docsMp[item.DocId].Name
			item.FileAsset = docsMp[item.DocId].FileAsset
			// 根据indexConfig拼接返回内容
			if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_TABLE {
				h.mergeTableContent(docsMp[item.DocId], item)
			} else {
				h.mergeContent(docsMp[item.DocId], item)
			}
		}

	}

	return rsp, nil

}

func (h *InnerHandler) MixedRecall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (rsp *pb.RetrieveKnowledgeBaseRsp, err error) {
	rsp1, err := h.vector.Recall(ctx, req)
	if err != nil {
		return nil, err
	}
	rsp2, err := h.fulltext.Recall(ctx, req)
	if err != nil {
		return nil, err
	}

	// mix
	mixed := mixRetrieveResults(append(rsp1.Result, rsp2.Result...))

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            mixed,
		VectorIndexHits:   rsp1.VectorIndexHits,
		FullTextIndexHits: rsp2.FullTextIndexHits,
	}, nil
}

func (h *InnerHandler) mergeTableContent(doc *models.Document, res *pb.ChunkRetrieveResult) {
	indexConfig := doc.IndexConfig
	// 解析chunk的content
	kv := make(map[string]string)
	err := stdsrv.Unmarshal(([]byte)(res.Chunk.Content), &kv)
	if err != nil {
		stdlog.WithError(err).Errorf("unmarshal chunk [%s] error.", res.Chunk.Id)
	}
	res.MergedContent = make(map[string]string)
	for _, vec := range indexConfig.VectorIndexConfigs {
		if vec.Returned {
			if vec.OriColumn == question {
				for i, ac := range res.Chunk.QaPairs {
					res.MergedContent[fmt.Sprintf("question_%d", i+1)] = ac.Question.Content
				}
			} else if vec.OriColumn == answer {
				for i, ac := range res.Chunk.QaPairs {
					res.MergedContent[fmt.Sprintf("answer_%d", i+1)] = ac.Answer.Content
				}
			} else {
				if v, ok := kv[vec.OriColumn]; ok {
					res.MergedContent[vec.OriColumn] = v
				}
			}
		}
	}
}

func (h *InnerHandler) mergeContent(doc *models.Document, res *pb.ChunkRetrieveResult) {
	indexConfig := doc.IndexConfig
	res.MergedContent = make(map[string]string)
	for _, scalar := range indexConfig.ScalarIndexConfigs {
		if scalar.Returned {
			res.MergedContent[scalar.OriColumn] = doc.GetMetaString(scalar.OriColumn, false)
		}
	}
	for _, vec := range indexConfig.VectorIndexConfigs {
		if vec.Returned {
			switch vec.OriColumn {
			case docChunk:
				res.MergedContent[docChunk] = res.Chunk.Content
			case docSummary:
				if doc.AbstractChunk != nil {
					res.MergedContent[docSummary] = doc.AbstractChunk.Content
				}
			case imageSummary:
				for _, ac := range res.Chunk.AugmentedChunks {
					if ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_SUMMARY {
						res.MergedContent[imageSummary] = ac.Content
					}
				}
			case tableSummary:
				for _, ac := range res.Chunk.AugmentedChunks {
					if ac.AugmentedType == pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY {
						res.MergedContent[tableSummary] = ac.Content
					}
				}
			case question:
				for i, ac := range res.Chunk.QaPairs {
					res.MergedContent[fmt.Sprintf("question_%d", i+1)] = ac.Question.Content
				}
			case answer:
				for i, ac := range res.Chunk.QaPairs {
					res.MergedContent[fmt.Sprintf("answer_%d", i+1)] = ac.Answer.Content
				}
			}
		}
	}
}

func (h *InnerHandler) beforeRecall(req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseReq, error) {
	// 使用短id进行查询
	var err error
	var docs []*models.Document
	if len(req.DocRange) == 0 {
		// 如果为空查询该知识库所有文档
		docs, err = GetDocumentStore().LoadByKnowledgeBase(req.KnowledgeBaseId)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "get documents error before recall.")
		}
	} else {
		docs, err = GetDocumentStore().LoadBatch(req.DocRange)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "get documents error before recall.")
		}
	}
	// 过滤掉禁用的文档
	tmp := make([]*models.Document, 0)
	for _, doc := range docs {
		if doc.RetrieveEnabled {
			tmp = append(tmp, doc)
		}
	}
	docs = tmp
	// 根据元数据查询信息进行过滤
	docs = parseMetaQuery(docs, req.MetaQuery, true)
	newIds := make([]string, 0, len(docs))
	for _, doc := range docs {
		// 用十六进制str进一步压缩str长度
		newIds = append(newIds, strconv.FormatInt(doc.ShortId, 16))
	}
	req.DocRange = newIds

	return req, nil
}

// parseMetaQuery 根据元数据过滤doc
func parseMetaQuery(docs []*models.Document, req *pb.MetaQueryConfig, indexFlag bool) []*models.Document {
	if req == nil || req.Condition == nil {
		return docs
	}
	res := make([]*models.Document, 0)
	for _, doc := range docs {
		if parseCondition(doc, req.Condition, indexFlag) {
			res = append(res, doc)
		}
	}
	return res
}

func parseCondition(doc *models.Document, condition *pb.ExprCondition, indexFlag bool) bool {
	switch condition.BoolOp {
	case pb.BoolOperation_AND:
		res := true
		for _, expr := range condition.Expr {
			if expr.Condition == nil {
				res = res && calExpr(doc.GetMetaString(expr.Field, indexFlag), expr.Value, expr.Op)
				if !res {
					break
				}
			} else {
				res = res && parseCondition(doc, expr.Condition, indexFlag)
			}
		}
		return res
	case pb.BoolOperation_OR:
		res := false
		for _, expr := range condition.Expr {
			if expr.Condition == nil {
				res = res || calExpr(doc.GetMetaString(expr.Field, indexFlag), expr.Value, expr.Op)
				if res {
					break
				}
			} else {
				res = res || parseCondition(doc, expr.Condition, indexFlag)
			}
		}
		return res
	default:
		return false
	}
}

func calExpr(metaValue, value string, op pb.ExprOperation) bool {
	switch op {
	case pb.ExprOperation_EQ:
		return metaValue == value
	case pb.ExprOperation_NEQ:
		return metaValue != value
	default:
		// FIXME 其他操作符先不实现
		return false
	}
}

func (h *InnerHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if err := h.fulltext.DeleteChunksById(ctx, chunkIds); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksById(ctx, chunkIds); err != nil {
		stdlog.Error(err)
	}

	return nil
}

func (h *InnerHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if err := h.fulltext.DeleteChunksByOriId(ctx, oriChunkIds); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksByOriId(ctx, oriChunkIds); err != nil {
		stdlog.Error(err)
	}

	return nil
}

// DeleteChunksByDocId 删除文档相关的索引，还包括数据库中的数据
func (h *InnerHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	// 如果ctx有传参数，则不需要删除db中的数据
	if v := ctx.Value("remain_db"); v == nil {
		if err := GetChunkStore(h.KnowledgeBase.ProjectId).DeleteByDocument(docId); err != nil {
			return err
		}
		if err := GetDocElementStore().DeleteByDocument(docId); err != nil {
			return err
		}
	}

	// TODO return error比打印日志更合理点？
	if err := h.fulltext.DeleteChunksByDocId(ctx, docId); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksByDocId(ctx, docId); err != nil {
		stdlog.Error(err)
	}
	return nil
}

func (h *InnerHandler) Drop(ctx context.Context) error {
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE || h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.Drop(ctx)
	} else {
		err := h.vector.Drop(ctx)
		if err != nil {
			return err
		}
		return h.fulltext.Drop(ctx)
	}
}

// fillRetrieveResults 根据id从ChunkStore读取对应内容并填充
// 并过滤掉禁用的chunk
func fillRetrieveResults(projectID string, data []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	ids := make([]string, 0, len(data))
	for _, item := range data {
		ids = append(ids, item.Chunk.Id)
	}
	chunks, err := GetChunkStore(projectID).LoadBatch(ids)
	if err != nil {
		return nil, err
	}
	chunksMp := make(map[string]*models.Chunk, len(chunks))
	for _, chunk := range chunks {
		chunksMp[chunk.Id] = chunk
	}

	ret := make([]*pb.ChunkRetrieveResult, 0, len(data))
	for _, item := range data {
		chunk, ok := chunksMp[item.Chunk.Id]
		if !ok {
			stdlog.Errorf("fillRetrieveResults: chunk %s not found", item.Chunk.Id)
			continue
		}
		if !*chunk.RetrieveEnabled {
			// 忽略禁用的chunk
			continue
		}
		item.Chunk = chunk.ToPb()
		item.DocId = chunk.DocumentId
		item.KnowledgeBaseId = chunk.KnowledgeBaseId
		ret = append(ret, item)
	}
	return ret, nil
}

func mixRetrieveResults(data []*pb.ChunkRetrieveResult) []*pb.ChunkRetrieveResult {
	// chunk id -> weighted score
	mp := map[string]float32{}

	for _, item := range data {
		id := item.Chunk.Id
		if exScore, ok := mp[id]; ok {
			mp[id] = exScore*0.5 + item.Score*0.5
		} else {
			mp[id] = item.Score
		}
	}

	merged := make([]*pb.ChunkRetrieveResult, 0, len(mp))
	for id, score := range mp {
		merged = append(merged, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{
				Id: id,
			},
			Score: score,
		})
	}
	return merged
}

func (h *InnerHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE || h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		err := h.docEngine.RemoveFileFromKnowledgeBase(ctx, req)
		if err != nil {
			stdlog.Error("InnerHandler DocEngine.RemoveFileFromKnowledgeBase:", err)
		}
		return err
	} else {
		doc, err := GetDocumentStore().Load(req.DocId)
		if err != nil {
			return err
		}
		if err := GetDocumentStore().Delete(ctx, req.DocId); err != nil {
			return err
		}
		// 回调cvat删除
		body, err := json.Marshal([]*pb.Document{doc.ToPb()})
		if err != nil {
			return err
		}
		_, err = clients.CVATCli.HttpRpcCall(ctx, http.MethodDelete, fmt.Sprintf("http://%s:%s%s", conf.Config.PromptCfg.Host, conf.Config.PromptCfg.Port, deletetaskFiles), bytes.NewBuffer(body))
		if err != nil {
			return err
		}
		if err := h.vector.RemoveFileFromKnowledgeBase(ctx, req); err != nil {
			stdlog.Error("InnerHandler hippo.RemoveFileFromKnowledgeBase:", err)
		}
		if err := h.fulltext.RemoveFileFromKnowledgeBase(ctx, req); err != nil {
			stdlog.Error("InnerHandler scope.RemoveFileFromKnowledgeBase:", err)
		}
		return nil
	}
}

func (h *InnerHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) (err error) {

	if err = h.vector.SubmitChunks(ctx, chunks); err != nil {
		return err
	}

	if err = h.fulltext.SubmitChunks(context.WithValue(ctx, AlreadySubmitVector, "true"), chunks); err != nil {
		return err
	}
	return nil
}

func (h *InnerHandler) RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error {
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_DOCENGINE || h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		err := h.docEngine.RemoveFilesFromKnowledgeBase(ctx, docIds)
		if err != nil {
			stdlog.Error("InnerHandler DocEngine.RemoveFileFromKnowledgeBase:", err)
		}
		return err
	} else {
		if err := GetDocumentStore().DeleteBatch(ctx, docIds); err != nil {
			return err
		}
		if err := h.vector.RemoveFilesFromKnowledgeBase(ctx, docIds); err != nil {
			stdlog.Error("InnerHandler hippo.RemoveFileFromKnowledgeBase:", err)
		}
		if err := h.fulltext.RemoveFilesFromKnowledgeBase(ctx, docIds); err != nil {
			stdlog.Error("InnerHandler scope.RemoveFileFromKnowledgeBase:", err)
		}
		return nil
	}
}

func NewInnerHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	handler := &InnerHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase: kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
				pb.KnowledgeBaseRetrieveStrategy_MIXED,
				pb.KnowledgeBaseRetrieveStrategy_VECTOR,
				pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT,
				pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE,
			},
		},
	}
	if kb.DatabaseConfig == nil || kb.DatabaseConfig.UseDefault {
		kb.DatabaseConfig = &pb.DatabaseConfig{
			VectorDb: &pb.DataConnection{
				Id:   "",
				Type: pb.ConnectionType(pb.ConnectionType_value[conf.Config.KnowlhubConfig.VectorEngine]),
			},
			FulltextDb: &pb.DataConnection{
				Id:   "",
				Type: pb.ConnectionType(pb.ConnectionType_value[conf.Config.KnowlhubConfig.FullTextEngine]),
			},
		}
	}
	vectorEngine, err := GetVectorConstructor(kb.DatabaseConfig.VectorDb.Type.String())
	if err != nil {
		return nil, err
	}
	fulltextEngine, err := GetFullTextConstructor(kb.DatabaseConfig.FulltextDb.Type.String())
	if err != nil {
		return nil, err
	}
	vector, err := vectorEngine(ctx, kb)
	if err != nil {
		return nil, stderr.Wrap(err, "new inner vector handler")
	}
	fulltext, err := fulltextEngine(ctx, kb)
	if err != nil {
		return nil, stderr.Wrap(err, "new inner fulltext handler")
	}

	handler.vector = vector
	handler.fulltext = fulltext
	if kb.ContentType == pb.KnowledgeBaseContentType_DOCENGINE {
		docEngine, err := NewDocEngineHandler(ctx, kb)
		if err != nil {
			return nil, stderr.Wrap(err, "new inner doc engine handler")
		}
		handler.docEngine = docEngine.(*DocEngineHandler)
	}
	return handler, nil
}
