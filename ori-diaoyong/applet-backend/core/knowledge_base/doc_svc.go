package knowledge_base

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	pkgcli "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

// 文档算子服务client

type DocHandler interface {
	Load(context.Context, *DocLoadReq) (*DocLoadRsp, error)
	Split(context.Context, *DocSplitReq) (*DocSplitRsp, error)
	LoadSplit(context.Context, *DocLoadSplitReq) (*DocLoadSplitRsp, error)
	LoadChunks(context.Context, *DocLoadSplitReq) (*pb.DocSvcLoadChunkRsp, error)
	HandleTableChunks(ctx context.Context, data *pb.DocSvcLoadChunkRsp) error
	LoadTable(context.Context, *TableContentLoadReq) (TableContentLoadRsp, error)
	Healthz(context.Context) (ok bool, msg string)
}

type DocLoadReq struct {
	Filepath     string `json:"filepath,omitempty"`     // 需要'sfs://'开头的文件地址
	NeedElements bool   `json:"needElements,omitempty"` // 默认为false，如果需要返回elements的话设置为true
	Strategy     string `json:"strategy,omitempty"`     // 仅在识别pdf和图片时生效， 默认为auto， 可选值auto | hi_res | fast | ocr_only
}

type DocElement struct {
	Type      pb.DocElementType `json:"type,omitempty"`
	ElementID string            `json:"element_id,omitempty"`
	Text      string            `json:"text,omitempty"`
	Metadata  struct {
		Coordinates *pb.Coordinates `json:"coordinates,omitempty"`
		TextAsHtml  string          `json:"text_as_html,omitempty"`
		Languages   []string        `json:"languages,omitempty"`
		PageNumber  int32           `json:"page_number,omitempty"`
		ParentIds   []string        `json:"parent_ids,omitempty"`
		ParentTexts []string        `json:"parent_texts,omitempty"`
	} `json:"metadata,omitempty"`
}

type DocLoadRsp struct {
	Elements []DocElement `json:"elements,omitempty"`
	Duration float64      `json:"duration,omitempty"`
	Filepath string       `json:"filepath,omitempty"`
	Text     string       `json:"text,omitempty"`
}

type DocSplitReq struct {
	Text         string   `json:"text,omitempty"`         // 需要切割的文本
	Type         string   `json:"type,omitempty"`         // 切分方式，具体的值看下面的表格
	ChunkSize    int      `json:"chunkSize,omitempty"`    // 分段大小，默认1000
	ChunkOverlap int      `json:"chunkOverlap,omitempty"` // 重叠大小，默认200
	Separator    string   `json:"separator,omitempty"`    // 切分字符，默认"\n", 仅对character生效
	SplitHeaders []string `json:"splitHeaders,omitempty"` // markdown和html方式需要
	Language     string   `json:"language,omitempty"`     // code类型时需要
}

type DocSplitRsp struct {
	Texts    []string `json:"texts,omitempty"`
	Duration float64  `json:"duration,omitempty"`
}

type DocExternalConfig struct {
	Figure          bool `json:"figure,omitempty"`           // 是否保留图片
	Table           bool `json:"table,omitempty"`            // 是否保留表格
	MathFormula     bool `json:"math_formula,omitempty"`     // 识别数学公式
	ChemicalFormula bool `json:"chemical_formula,omitempty"` // 识别化学公式
	Save2md         bool `json:"save2md"`                    // 保存md
	Save2docx       bool `json:"save2docx"`                  // 保存docx
	Save2excel      bool `json:"save2excel"`                 // 保存excel
}

type DocLoadSplitReq struct {
	Filepath       string             `json:"filepath,omitempty"`     // 需要'sfs://'开头的文件地址
	Strategy       string             `json:"strategy,omitempty"`     // 仅在识别pdf和图片时生效， 默认为auto， 可选值auto | hi_res | fast | ocr_only
	Type           string             `json:"type,omitempty"`         // 切分方式，具体的值看下面的表格
	ChunkSize      int                `json:"chunkSize,omitempty"`    // 分段大小，默认1000
	ChunkOverlap   int                `json:"chunkOverlap,omitempty"` // 重叠大小，默认200
	Separator      string             `json:"separator,omitempty"`    // 切分字符，默认"\n", 仅对character生效
	Separators     []string           `json:"separators,omitempty"`   // 切分字符列表
	SplitHeaders   []string           `json:"splitHeaders,omitempty"`
	Language       string             `json:"language,omitempty"` // code类型时需要
	TopK           int                `json:"topK,omitempty"`     // 仅解析前TopK页，用于预览，目前仅适用于pdf
	Merge          bool               `json:"merge"`              // 是否开启分段合并
	ExternalConfig *DocExternalConfig `json:"external_config,omitempty"`
	SavePath       string             `json:"savepath,omitempty"` // 保存（图片）路径
	DocServiceUrl  string             `json:"docServiceUrl"`      // gpu文档解析服务地址
	ImageModelUrl  string             `json:"imageModelUrl"`      // 图像理解模型地址
	DocApiKey      string             `json:"docApiKey"`          // apikey，目前使用user token
	ImageApiKey    string             `json:"imageApiKey"`
	ForcePartition bool               `json:"forcePartition"`
}

type DocLoadSplitRsp struct {
	Texts    []string `json:"texts,omitempty"`
	Duration float64  `json:"duration,omitempty"`
	Filepath string   `json:"filepath,omitempty"`
}

type DocLoadChunkRsp struct {
	Chunks   []*pb.Chunk   `json:"chunks,omitempty"`
	Elements []*DocElement `json:"elements,omitempty"`
}

type TableContentLoadReq struct {
	Filepath        string `json:"filepath,omitempty"`  // 需要'sfs://'开头的文件地址
	SheetName       string `json:"sheetName,omitempty"` // excel的sheet表名，csv文件不需要
	HeaderRow       int32  `json:"headerRow,omitempty"`
	DataStartingRow int32  `json:"dataRow,omitempty"` // excel表格数据开始的行号
}

type TableContentLoadRsp = map[string][]string

// // TODO
// type DocChunksRsp struct {
// 	Chunks   []*models.Chunk      `json:"chunks,omitempty"`
// 	Elements []*models.DocElement `json:"elements,omitempty"`
// }

type docSvcHandler struct {
	conf.DocSvcConfig
}

var docSvc DocHandler
var docSvcOnce sync.Once

func GetDocHandler() DocHandler {
	docSvcOnce.Do(func() {
		docSvc = &docSvcHandler{
			conf.Config.DocSvcConfig,
		}
	})
	return docSvc
}

func (h *docSvcHandler) Load(ctx context.Context, req *DocLoadReq) (*DocLoadRsp, error) {
	panic("not implemented") // TODO: Implement
}

func (h *docSvcHandler) Split(ctx context.Context, req *DocSplitReq) (*DocSplitRsp, error) {
	panic("not implemented") // TODO: Implement
}

func (h *docSvcHandler) LoadSplit(ctx context.Context, req *DocLoadSplitReq) (*DocLoadSplitRsp, error) {
	bs, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	api := h.LoadSplitApi
	param := &pkgcli.HttpParam{
		Method:  api.Method,
		Url:     conf.GetDocSvcAddr() + api.Url,
		ReqBody: string(bs),
	}
	stdlog.Debugf("load split %s, ReqBody: %s", req.Filepath, string(bs))
	rsp := new(DocLoadSplitRsp)
	err = clients.HttpCli.HttpCall(ctx, param, rsp)
	if err != nil {
		return nil, stderr.Wrap(err, "http call")
	}

	return rsp, err
}

func (h *docSvcHandler) LoadChunks(ctx context.Context, req *DocLoadSplitReq) (*pb.DocSvcLoadChunkRsp, error) {
	bs, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	api := h.LoadChunkApi
	projectId := helper.GetProjectID(ctx)
	tenantId := helper.GetTenantID(ctx)
	param := &pkgcli.HttpParam{
		Method:  api.Method,
		Url:     fmt.Sprintf("%s%s?project_id=%s&tenant_id=%s", conf.GetDocSvcAddr(), api.Url, projectId, tenantId),
		ReqBody: string(bs),
	}
	stdlog.Debugf("load chunks %s, ReqBody: %s", req.Filepath, string(bs))
	rsp := new(pb.DocSvcLoadChunkRsp)
	body, err := clients.HttpCli.HttpCallString(ctx, param)
	if err != nil {
		return nil, stderr.Wrap(err, "http call")
	}
	pj := stdsrv.DefaultProtoJsonAccessor()
	if err = pj.Unmarshal([]byte(body), rsp); err != nil {
		return nil, stderr.Wrap(err, "protojson unmarshal")
	}

	return rsp, err
}

func (h *docSvcHandler) LoadTable(ctx context.Context, req *TableContentLoadReq) (TableContentLoadRsp, error) {
	bs, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	api := h.LoadTableApi
	param := &pkgcli.HttpParam{
		Method:  api.Method,
		Url:     conf.GetDocSvcAddr() + api.Url,
		ReqBody: string(bs),
	}
	body, err := clients.HttpCli.HttpCallString(ctx, param)
	if err != nil {
		return nil, stderr.Wrap(err, "http call")
	}
	rsp := new(TableContentLoadRsp)
	err = json.Unmarshal([]byte(body), rsp)
	if err != nil {
		return nil, stderr.Wrap(err, "unmarshal")
	}
	return *rsp, nil
}

// HandleTableChunks 将表格类型的chunk content设置为其doc element的TextAsHtml, 并取消原文的向量索引
func (h *docSvcHandler) HandleTableChunks(ctx context.Context, rsp *pb.DocSvcLoadChunkRsp) error {
	if rsp == nil || len(rsp.Chunks) == 0 {
		return nil
	}

	eleMp := make(map[string]*pb.DocElement)
	for _, e := range rsp.Elements {
		eleMp[e.ElementId] = e
	}
	for _, c := range rsp.Chunks {
		if c.ContentType == pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE {
			if len(c.ElementIds) != 1 {
				stdlog.Warnf("length of table chunk elementIds is not 1, chunkId:%s, element_ids:[%v]", c.Id, c.ElementIds)
				continue
			}
			if e, ok := eleMp[c.ElementIds[0]]; !ok {
				stdlog.Errorf("table chunk element not found, chunkId:%s", c.Id)
				continue
			} else {
				c.Content = e.Metadata.TextAsHtml
				c.DisableVectorIndexing = true
				c.DisableFullTextIndexing = false
			}
		}
	}

	return nil
}

func (h *docSvcHandler) Healthz(ctx context.Context) (ok bool, msg string) {
	api := h.HealthzApi
	param := &pkgcli.HttpParam{
		Method: api.Method,
		Url:    conf.GetDocSvcAddr() + api.Url,
	}
	url, err := url.Parse(param.Url)
	if err != nil {
		return false, err.Error()
	}
	req, err := http.NewRequestWithContext(ctx, param.Method, url.String(), nil)
	if err != nil {
		return false, err.Error()
	}
	rsp, err := clients.HttpCli.Cli.Do(req)
	if err != nil {
		return false, err.Error()
	}
	if rsp.StatusCode >= 200 && rsp.StatusCode < 300 {
		return true, ""
	}
	defer rsp.Body.Close()
	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return false, err.Error()
	}
	return false, string(body)
}

func DefualtDocLoadSplitReq() *DocLoadSplitReq {
	return &DocLoadSplitReq{
		Strategy:     conf.Config.DocSvcConfig.DefaultStrategy,
		Type:         conf.Config.DocSvcConfig.DefaultSplitType,
		ChunkSize:    conf.Config.DocSvcConfig.DefaultChunkSize,
		ChunkOverlap: conf.Config.DocSvcConfig.DefaultChunkOverlap,
	}
}

func NewDocLoadSplitReq(ctx context.Context, dpc *pb.DocProcessingConfig, filePath string, headPages int) (*DocLoadSplitReq, error) {
	loadCfg := dpc.DocLoadConfig
	splitCfg := dpc.DocSplitConfig
	req := &DocLoadSplitReq{
		Filepath:       filePath,
		Strategy:       conf.Config.DocSvcConfig.DefaultStrategy,
		Type:           pb.DocSplitStrategyType_name[int32(splitCfg.SplitStrategyType)],
		ChunkSize:      int(splitCfg.ChunkSize),
		ChunkOverlap:   int(splitCfg.ChunkOverlap),
		Separators:     splitCfg.Separators,
		TopK:           headPages,
		Merge:          !splitCfg.DisableChunkMerging,
		ForcePartition: true,
		ExternalConfig: &DocExternalConfig{
			Save2md:    true,
			Save2docx:  true,
			Save2excel: true,
			Figure:     true,
			Table:      true,
		},
	}
	if loadCfg != nil {
		req.Strategy = strings.ToLower(loadCfg.StrategyType.String())
		if loadCfg.StrategyType == pb.DocLoadStrategyType_HI_RES || loadCfg.StrategyType == pb.DocLoadStrategyType_VL_MODEL {
			if loadCfg.DocModel == nil {
				return nil, stderr.Internal.Errorf("doc model must be set when strategy is hi_res or vl_model")
			}
			req.ExternalConfig.Figure = loadCfg.RemainFigure
			req.ExternalConfig.Table = loadCfg.RemainTable
			req.ExternalConfig.MathFormula = loadCfg.RecogMathFormula
			req.ExternalConfig.ChemicalFormula = loadCfg.RecogChemicalFormula
			token, err := helper.GetToken(ctx)
			if err != nil {
				stdlog.WithError(err).Errorf("Parse user token with context error.")
				token = conf.Config.Token
			}
			req.DocApiKey = token
			req.ImageApiKey = token
			req.DocServiceUrl = loadCfg.DocModel.FullUrl

			if loadCfg.StrategyType == pb.DocLoadStrategyType_VL_MODEL {
				// 需要图像理解模型
				if loadCfg.ImageModel == nil {
					return nil, stderr.Internal.Errorf("image model must be set when strategy is vl_model")
				}
				req.ImageModelUrl = loadCfg.ImageModel.FullUrl
			}
		}
	} else {
		if dpc.AutoConfigured {
			// 智能策略，判断是否有配置默认的字符识别模型，如果配置，则使用hi_res，否则使用fast
			ms, err := DefaultOcrModelSvc(helper.GetProjectID(ctx))
			if err != nil {
				// 使用fast策略
				req.Strategy = "fast"
			} else {
				req.Strategy = "hi_res"
				token, err := helper.GetToken(ctx)
				if err != nil {
					stdlog.WithError(err).Errorf("Parse user token with context error.")
					token = conf.Config.Token
				}
				req.DocServiceUrl = ms.FullUrl
				req.DocApiKey = token
				req.ImageApiKey = token
			}
		}
	}
	return req, nil
}
