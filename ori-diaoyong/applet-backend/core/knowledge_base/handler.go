package knowledge_base

import (
	"context"
	"fmt"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"

	"transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type StoreHandler interface {
	ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error)
	GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error)
	ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error)
	ListDocumentChunksByReq(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error)
	CountDocuments(ctx context.Context) (int32, error)

	AsyncSubmitFileToKnowledgeBase(filePath string)
	Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error)

	RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error
	SupportedRetrieveStrategies() []pb.KnowledgeBaseRetrieveStrategy

	SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error
	DeleteChunksById(ctx context.Context, chunkIds []string) error
	DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error
	DeleteChunksByDocId(ctx context.Context, docId string) error

	// Drop drop all data of a knowledge base
	Drop(ctx context.Context) error
	RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error
}

type VectorHandler interface {
	StoreHandler
	VectorName() string
	Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error)
}

type FullTextHandler interface {
	StoreHandler
	FullTextName() string
	Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error)
}

type HandlerBase struct {
	KnowledgeBase      *models.KnowledgeBase
	RetrieveStrategies []pb.KnowledgeBaseRetrieveStrategy
}

func (h *HandlerBase) SupportedRetrieveStrategies() []pb.KnowledgeBaseRetrieveStrategy {
	return h.RetrieveStrategies
}

func (h *HandlerBase) Embedding(ctx context.Context, content any) ([]float32, error) {
	ret, err := h.BatchEmbedding(ctx, []any{content})
	if err != nil {
		return nil, err
	}
	return ret[0], nil
}

func (h *HandlerBase) BatchEmbedding(ctx context.Context, contents []any) ([][]float32, error) {
	var vectorModel *pb.ModelService
	kb := h.KnowledgeBase
	store, err := GetStoreType(kb)
	if err != nil {
		return nil, err
	}
	switch store {
	case InnerStore:
		vectorModel = kb.VectorModel
	case ExternalHippoStore:
		vectorModel = kb.ConnectionRegistry.VectorModel
	default:
		return nil, fmt.Errorf("Embedding: unsupported store type, store:[%v]", store)
	}

	switch kb.ContentType {
	case pb.KnowledgeBaseContentType_TEXT, pb.KnowledgeBaseContentType_TABLE:
		return GetEmbeddingManager().BatchEmbedding(ctx, vectorModel, contents)
	default:
		return nil, fmt.Errorf("Embedding: unsupported content type, type:[%v]", kb.ContentType)
	}
}

func (h *HandlerBase) ListDocumentChunksByReq(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	panic("not implemented")
}

func GetStoreType(kb *models.KnowledgeBase) (ret KnowledgeBaseStoreType, err error) {
	ret = -1
	if kb.SourceType == pb.KnowledgeBaseSourceType_FROM_SCRATCH {
		ret = InnerStore
	} else if kb.RegistryType == pb.KnowledgeBaseRegistryType_FROM_TKH {
		ret = TkhStore
	} else if kb.RegistryType == pb.KnowledgeBaseRegistryType_FROM_DATA_CONNECTION {
		conn := kb.ConnectionRegistry
		if conn != nil {
			switch conn.DataConnectionType {
			case pb.ConnectionType_HIPPO:
				ret = ExternalHippoStore
			case pb.ConnectionType_SCOPE:
				ret = ExternalScopeStore
			}
		}
	}

	if ret == -1 {
		err = fmt.Errorf("kb has no supported store handler, kb:[%v]", kb)
	}
	return
}

func NewHandler(ctx context.Context, kb *models.KnowledgeBase) (ret StoreHandler, err error) {
	store, err := GetStoreType(kb)
	if err != nil {
		return nil, err
	}
	if f, ok := StoreHandlerMap[store]; ok {
		return f(ctx, kb)
	} else {
		return nil, fmt.Errorf("StoreHandlerMap has no supported func, type:[%v]", store)
	}
}

func NewHandlerById(ctx context.Context, kbId string) (ret StoreHandler, err error) {
	repo := dao.InitQuery().KnowledgeBase
	kb, err := repo.Where(repo.Id.Eq(kbId)).Take()
	if err != nil {
		return nil, err
	}
	return NewHandler(ctx, kb)
}

func EmbeddingTexts(ctx context.Context, vectorModel *pb.ModelService, data []any) ([][]float32, error) {
	if len(data) == 0 {
		return [][]float32{}, nil
	}
	texts, err := utils.CvtAny2TSlice[string](data)
	if err != nil {
		return nil, stderr.Wrap(err, "EmbeddingTexts")
	}
	OpenAiTextVectorReq := &triton.OpenAiTextVectorReq{
		Input: texts,
	}
	// 添加重试
	retryCount, retryInterval := conf.Config.KnowlhubConfig.EmbeddingRetryCount, conf.Config.KnowlhubConfig.EmbeddingRetryInterval
	var lastErr error
	for retryCount > 0 {
		retryCount--
		OpenAiTextVectorResp, err := clients.Embedding(ctx, vectorModel, OpenAiTextVectorReq)
		if err != nil {
			stdlog.WithError(err).Errorf("call embedding model error %d times, waiting %v seconds", conf.Config.KnowlhubConfig.EmbeddingRetryCount+1-retryCount, retryInterval.Seconds())
			lastErr = err
			time.Sleep(retryInterval)
			retryInterval *= 2
			continue
		}
		if OpenAiTextVectorResp == nil || len(OpenAiTextVectorResp.Data) != len(data) {
			stdlog.Errorf("EmbeddingTexts: OpenAiTextVectorResp is nil or len(OpenAiTextVectorResp.Data) != len(data)")
			stdlog.Errorf("call embedding model error %d times, waiting %v seconds", conf.Config.KnowlhubConfig.EmbeddingRetryCount+1-retryCount, retryInterval.Seconds())
			lastErr = fmt.Errorf("EmbeddingTexts: OpenAiTextVectorResp is nil or len(OpenAiTextVectorResp.Data) != len(data)")
			time.Sleep(retryInterval)
			retryInterval *= 2
			continue
		}
		// 将二维 float64 切片转换为二维 float32 切片
		embeddingRespFp32 := make([][]float32, len(OpenAiTextVectorResp.Data))
		for index, value := range OpenAiTextVectorResp.Data {
			embeddingRespFp32[index] = make([]float32, len(value.Embedding))
		}
		for i, value := range OpenAiTextVectorResp.Data {
			for j, embedding := range value.Embedding {
				embeddingRespFp32[i][j] = float32(embedding)
			}
		}
		return embeddingRespFp32, nil
	}
	// 超过重试次数还是失败
	return nil, stderr.Wrap(lastErr, "EmbeddingTexts failed")
}

// BatchifySlice 需要确保同一个chunk的增强切片分在同一组
func BatchifySlice(chunks []*models.ChunkForIndexing, batchSize int) [][]*models.ChunkForIndexing {
	res := make([][]*models.ChunkForIndexing, 0)
	if len(chunks) == 0 {
		return res
	}

	// 按OriId分组
	groupsByOriId := make(map[string][]*models.ChunkForIndexing)
	for _, chunk := range chunks {
		oriId := chunk.OriId
		groupsByOriId[oriId] = append(groupsByOriId[oriId], chunk)
	}

	// 创建批次
	currentBatch := make([]*models.ChunkForIndexing, 0)
	for _, group := range groupsByOriId {
		// 如果当前批次加上这个完整组不超过batchSize，添加到当前批次
		if len(currentBatch)+len(group) <= batchSize {
			currentBatch = append(currentBatch, group...)
		} else {
			// 如果当前批次已有内容，先保存它
			if len(currentBatch) > 0 {
				res = append(res, currentBatch)
			}

			// 创建新批次，包含整个当前组
			// 注意：这个组可能自身就超过batchSize
			currentBatch = make([]*models.ChunkForIndexing, 0)
			currentBatch = append(currentBatch, group...)
		}
	}

	// 添加最后一个批次（如果有）
	if len(currentBatch) > 0 {
		res = append(res, currentBatch)
	}

	return res
}
