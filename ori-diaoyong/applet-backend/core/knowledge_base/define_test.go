package knowledge_base

import (
	"context"
	"sort"
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func TestUpdateDisabledDocs(t *testing.T) {
	tests := []struct {
		disabledDocs []string
		docId        string
		disabled     bool
		expected     []string
	}{
		{
			disabledDocs: []string{"doc1", "doc2", "doc3"},
			docId:        "doc2",
			disabled:     true,
			expected:     []string{"doc1", "doc2", "doc3"},
		},
		{
			disabledDocs: []string{"doc1", "doc2", "doc3"},
			docId:        "doc4",
			disabled:     true,
			expected:     []string{"doc1", "doc2", "doc3", "doc4"},
		},
		{
			disabledDocs: []string{"doc1", "doc2", "doc3"},
			docId:        "doc2",
			disabled:     false,
			expected:     []string{"doc1", "doc3"},
		},
		{
			disabledDocs: []string{},
			docId:        "doc1",
			disabled:     true,
			expected:     []string{"doc1"},
		},
	}

	for _, test := range tests {
		result := updateDisabledDocs(test.disabledDocs, test.docId, test.disabled)
		if !sort.StringsAreSorted(result) {
			t.Errorf("Unsorted result: %v", result)
		}
		if !equal(result, test.expected) {
			t.Errorf("Expected %v, but got %v", test.expected, result)
		}
	}
}

func equal(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func TestUpdateMetricsInfo(t *testing.T) {
	// eb4e2409-c54f-4542-926c-67cda52390ee
	// e9f3e320-919a-440d-b82e-02e65f8503fd
	ctx := context.Background()
	conf.TestInit()
	dao.Init()
	Init()
	widgets.Init()
	clients.TestInit()

	kbm := GetKnowledgeBaseManager()
	err := kbm.UpdateKnowledgeMetricsInfo(ctx, "03f79a6b-cefc-4366-8405-55b59266edc8", KbMetricsTypeVisit)
	if err != nil {
		stdlog.Info("error")
	} else {
		stdlog.Info("ok")
	}
}
