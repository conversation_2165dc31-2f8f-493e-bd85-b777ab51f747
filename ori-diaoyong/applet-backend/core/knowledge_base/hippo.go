package knowledge_base

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/hippo-go/v1"
)

var (
	checkTableLockMp   = stdsync.NewLockMap()
	newHippoCliTimeout = 10 * time.Second
)

type HippoHandler struct {
	*HandlerBase
	initOnce        sync.Once
	Cli             *hippo.HippoClient
	Table           string
	TextField       string
	TitleField      string
	VecField        string
	IdField         string
	OriginalIdField string
	PriorityField   string
	ShortIdField    string
	IsExternal      bool
}

// hippo handler for self-managed kb
func NewInternalHippoHandler(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error) {
	h := &HippoHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase: kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
				pb.KnowledgeBaseRetrieveStrategy_VECTOR,
			},
		},
		Cli:             nil, // lazy init
		TextField:       "",
		TitleField:      InnerTitleField,
		VecField:        InnerVecField,
		IdField:         InnerIdField,
		OriginalIdField: InnerOriIdField,
		PriorityField:   InnerPriorityField,
		ShortIdField:    InnerShortIdField,
		Table:           getInnerHippoTable(kb),
		IsExternal:      false,
	}

	return h, nil
}

// hippo handler for handler data connection
func NewExternalHippoHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	if kb.ConnectionRegistry == nil {
		return nil, fmt.Errorf("kb.ConnectionRegistry is nil")
	}

	ctx, cancel := context.WithTimeout(ctx, newHippoCliTimeout)
	defer cancel()

	connReg := kb.ConnectionRegistry
	rsp, err := clients.CVATCli.DataConnClient.GetDataConnection(ctx, &pb.GetConnectionReq{
		Id: connReg.Id,
		UserContext: &pb.UserContext{
			ProjectId: kb.ProjectId,
		},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "data conn cli: GetDataConnection")
	}
	conn := rsp.Result

	cfg := &hippo.VectorDBConfig{
		ClientID:        "llmops-kb",
		Address:         fmt.Sprintf("%s:%s", conn.Address, conn.Port),
		UserName:        conn.Username,
		UserPassword:    conn.Password,
		Database:        conn.Database,
		Table:           connReg.Table,
		Dimension:       int64(connReg.VectorDim),
		MetricType:      conf.Config.HippoConfig.DefaultMetricType,
		AutoCreateTable: false,
		IndexName:       connReg.VectorIndexName,
	}
	cli, err := clients.NewHippoCli(ctx, cfg, false)
	if err != nil {
		return nil, stderr.Wrap(err, "NewHippoCli")
	}

	h := &HippoHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase: kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
				pb.KnowledgeBaseRetrieveStrategy_VECTOR,
			},
		},
		Cli:        cli,
		TextField:  connReg.TextField,
		TitleField: connReg.DocField,
		VecField:   connReg.VectorField,
		IdField:    connReg.IdField,
		Table:      getExternalHippoTable(kb),
		IsExternal: true,
	}
	if h.IdField == "" {
		h.IdField = hippo.VectorDBFieldID
	}
	return h, nil
}

func (h *HippoHandler) initStore(ctx context.Context) (err error) {
	if h.IsExternal {
		return nil
	}

	h.initOnce.Do(func() {
		if h.Cli != nil {
			return
		}
		if h.Cli, err = clients.GetDataConnectionHippoCli(ctx, h.KnowledgeBase.ProjectId, h.KnowledgeBase.DatabaseConfig.VectorDb); err != nil {
			return
		}
		if err = h.checkTable(ctx); err != nil {
			return
		}
	})

	return
}

func (h *HippoHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "getDocumentRows")
	}

	ret := make([]*pb.DocumentInfo, 0, len(docRowsMap))
	for doc, numRows := range docRowsMap {
		ret = append(ret, &pb.DocumentInfo{
			Doc:       h.newDoc(doc),
			NumChunks: int64(numRows),
		})
	}
	return &pb.ListDocumentsRsp{Result: ret}, nil
}

func (h *HippoHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	// 平铺展开树即可
	//    [dir]知识库名称
	//           |
	// [file]doc1, [file]doc2, ...

	root := new(pb.DocumentTree)
	root.Node = &pb.DocumentNode{
		Category: pb.DocumentNodeCategory_DIR,
		Id:       h.KnowledgeBase.Id,
		Name:     h.KnowledgeBase.Name,
	}

	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "HippoHandler.getDocumentRows")
	}
	for doc := range docRowsMap {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       doc,
				Name:     doc,
			},
		}
		root.Children = append(root.Children, subTree)
	}
	if len(docRowsMap) == 0 {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       "全量文本",
				Name:     "全量文本",
			},
		}
		root.Children = append(root.Children, subTree)
	}
	return &pb.GetDocumentTreeRsp{Tree: root}, nil
}

func (h *HippoHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	ids, err := h.ListIdsByDoc(ctx, docId)
	if err != nil {
		return nil, stderr.Wrap(err, "ListDocumentChunks:ListIdByDoc")
	}
	total := len(ids)
	offset, limit := helper.ParsePageReq(pageReq, total, conf.Config.HippoConfig.MaxChunkPageSize)
	retIdRange := ids[offset : offset+limit]

	chunInfos, err := h.listChunkByIds(ctx, retIdRange)
	if err != nil {
		return nil, stderr.Wrap(err, "ListDocumentChunks")
	}
	if pageReq.IsDesc {
		utils.ReverseSlice(chunInfos)
	}
	return &pb.ListDocumentChunksRsp{
		Total:    int32(total),
		PageNum:  pageReq.Page,
		PageSize: pageReq.PageSize,
		Result:   chunInfos,
	}, nil
}

func (h *HippoHandler) ListIdsByDoc(ctx context.Context, docId string) ([]string, error) {
	rsp, err := h.Cli.QueryIds(ctx, &hippo.SearchParams{
		Table:        h.Table,
		OutputFields: []string{h.IdField},
		Expr:         fmt.Sprintf("%s == '%s'", h.TitleField, docId),
	})
	if err != nil {
		return nil, err
	}
	if len(rsp.FieldsData) < 1 {
		return []string{}, nil
	}
	data := rsp.FieldsData[0].FieldValues
	ret := make([]string, 0, len(data))
	for _, v := range data {
		ret = append(ret, strconv.FormatInt(v, 10))
	}
	return ret, nil
}

func (h *HippoHandler) Drop(ctx context.Context) error {
	if h.IsExternal {
		return ErrCouldNotUpdateExternalKB
	}
	if err := h.initStore(ctx); err != nil {
		return err
	}
	return h.Cli.DeleteTable(ctx, h.Table)
}

// InnerRecall 仅召回chunk id
// 串行的方法
func (h *HippoHandler) innerRecall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	if params == nil {
		return nil, fmt.Errorf("recall params is nil")
	}
	vec, err := h.Embedding(ctx, req.Query)
	if err != nil {
		return nil, err
	}
	var expr string

	mp := make(map[string]float32)
	hits := make(map[string]*pb.Hit)
	chunks := make([]*pb.ChunkRetrieveResult, 0)

	cRange := req.DocRange

	// 需要根据索引顺序依次查询，并且再下一次查询中剔除已经返回的文档
	for r := 0; r < maxRecallCount; r++ {
		stdlog.Infof("begin to recall round [%d] with kb [%s]", r+1, req.KnowledgeBaseId)
		if len(cRange) > 0 {
			expr = fmt.Sprintf("%s in %s and %s == '%d'", h.TitleField, h.rangeStr(cRange), h.PriorityField, r)
		} else {
			// 说明再前几轮都已经返回了
			break
		}
		search, err := h.Cli.Search(ctx, &hippo.SearchParams{
			OutputFields: []string{h.IdField, h.OriginalIdField, h.ShortIdField},
			TopK:         int(params.TopK),
			Vectors:      [][]float32{vec},
			Table:        h.Table,
			Expr:         expr,
			Normalize:    true,
			Params:       map[string]any{},
		})
		if err != nil {
			return nil, stderr.Wrap(err, "hippo cli search")
		}

		item := search.Results[0]

		var ids, oriIds, shortIds *hippo.FieldColumnData
		for i, fcd := range item.Fields {
			if fcd.FieldName == h.IdField {
				ids = item.Fields[i]
			} else if fcd.FieldName == h.OriginalIdField {
				oriIds = item.Fields[i]
			} else if fcd.FieldName == h.ShortIdField {
				shortIds = item.Fields[i]
			}
		}
		if ids == nil || oriIds == nil || shortIds == nil {
			return nil, fmt.Errorf("hippo search result fields not found")
		}
		searchedDocs := make(map[string]struct{})
		for i := 0; i < len(item.Scores); i++ {
			id := ids.Data[i].(string)
			oriId := oriIds.Data[i].(string)
			shortId := shortIds.Data[i].(string)
			if exScore, ok := mp[oriId]; !ok || item.Scores[i] > exScore {
				mp[oriId] = item.Scores[i]
			}
			hits[id] = &pb.Hit{
				Hit:   true,
				Score: item.Scores[i],
				Round: int32(r),
			}
			searchedDocs[shortId] = struct{}{}
		}
		// 从docRange中剔除已经返回过的文档
		newRange := make([]string, 0)
		for _, id := range cRange {
			if _, ok := searchedDocs[id]; !ok {
				newRange = append(newRange, id)
			}
		}
		cRange = newRange
	}

	for oriId, maxScore := range mp {
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{Id: oriId},
			Score: maxScore,
		})
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks, VectorIndexHits: hits}, nil
}

// InnerRecallV1 仅召回chunk id
// 用于召回2.0版本之前的知识库
func (h *HippoHandler) innerRecallV1(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	if params == nil {
		return nil, fmt.Errorf("recall params is nil")
	}
	vec, err := h.Embedding(ctx, req.Query)
	if err != nil {
		return nil, err
	}
	var expr string
	if len(req.DocRange) > 0 {
		expr = fmt.Sprintf("%s in %s", h.TitleField, h.rangeStr(req.DocRange))
	}

	search, err := h.Cli.Search(ctx, &hippo.SearchParams{
		OutputFields: []string{h.IdField, h.OriginalIdField},
		TopK:         int(params.TopK),
		Vectors:      [][]float32{vec},
		Table:        h.Table,
		Expr:         expr,
		Normalize:    true,
		Params:       map[string]any{},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "hippo cli search")
	}

	item := search.Results[0]
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(search.Results))

	var ids, oriIds *hippo.FieldColumnData
	for i, fcd := range item.Fields {
		if fcd.FieldName == h.IdField {
			ids = item.Fields[i]
		} else if fcd.FieldName == h.OriginalIdField {
			oriIds = item.Fields[i]
		}
	}
	if ids == nil || oriIds == nil {
		return nil, fmt.Errorf("hippo search result fields not found")
	}

	mp := make(map[string]float32)
	hits := make(map[string]*pb.Hit)

	for i := 0; i < len(item.Scores); i++ {
		id := ids.Data[i].(string)
		oriId := oriIds.Data[i].(string)
		if exScore, ok := mp[oriId]; !ok || item.Scores[i] > exScore {
			mp[oriId] = item.Scores[i]
		}
		hits[id] = &pb.Hit{
			Hit:   true,
			Score: item.Scores[i],
		}
	}

	for oriId, maxScore := range mp {
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{Id: oriId},
			Score: maxScore,
		})
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks, VectorIndexHits: hits}, nil
}

// innerRecallParallel 并行的查询
func (h *HippoHandler) innerRecallParallel(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	if params == nil {
		return nil, fmt.Errorf("recall params is nil")
	}
	vec, err := h.Embedding(ctx, req.Query)
	if err != nil {
		return nil, err
	}

	roundMp := make(map[int]map[string]*pb.Hit)
	roundHits := make(map[int]map[string]*pb.Hit)
	roundSearchedDocs := make(map[int]map[string]struct{})
	chunks := make([]*pb.ChunkRetrieveResult, 0)
	lock := sync.Mutex{}

	cRange := req.DocRange
	wg := sync.WaitGroup{}
	realMax := maxRecallCount
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_TABLE {
		realMax = 1
	}
	wg.Add(realMax)
	errs := make([]error, 0)

	// 需要根据索引顺序并行查询，最后汇总剔除在高优先级中已经存在的文档
	for r := 0; r < realMax; r++ {
		go func(r int) {
			mp := make(map[string]*pb.Hit)
			hits := make(map[string]*pb.Hit)
			defer wg.Done()
			var expr string
			stdlog.Infof("begin to recall round [%d] with kb [%s]", r+1, req.KnowledgeBaseId)
			if len(cRange) > 0 {
				expr = fmt.Sprintf("%s in %s and %s == '%d'", h.ShortIdField, h.rangeStr(cRange), h.PriorityField, r)
			} else {
				expr = fmt.Sprintf("%s == '%d'", h.PriorityField, r)
			}
			search, err := h.Cli.Search(ctx, &hippo.SearchParams{
				OutputFields: []string{h.IdField, h.OriginalIdField, h.ShortIdField},
				TopK:         int(params.TopK),
				Vectors:      [][]float32{vec},
				Table:        h.Table,
				Expr:         expr,
				Normalize:    true,
				Params:       map[string]any{},
			})
			if err != nil {
				errs = append(errs, stderr.Wrap(err, "hippo cli search with round [%d]", r))
				return
			}

			item := search.Results[0]

			var ids, oriIds, shortIds *hippo.FieldColumnData
			for i, fcd := range item.Fields {
				if fcd.FieldName == h.IdField {
					ids = item.Fields[i]
				} else if fcd.FieldName == h.OriginalIdField {
					oriIds = item.Fields[i]
				} else if fcd.FieldName == h.ShortIdField {
					shortIds = item.Fields[i]
				}
			}
			if ids == nil || oriIds == nil || shortIds == nil {
				errs = append(errs, stderr.Wrap(err, "hippo search result fields not found with round [%d]", r))
				return
			}
			searchedDocs := make(map[string]struct{})
			for i := 0; i < len(item.Scores); i++ {
				id := ids.Data[i].(string)
				oriId := oriIds.Data[i].(string)
				shortId := shortIds.Data[i].(string)
				hit := &pb.Hit{
					Hit:     true,
					Score:   item.Scores[i],
					Round:   int32(r),
					ShortId: shortId,
				}
				if exScore, ok := mp[oriId]; !ok || item.Scores[i] > exScore.Score {
					mp[oriId] = hit
				}
				hits[id] = hit
				searchedDocs[shortId] = struct{}{}
			}
			lock.Lock()
			roundMp[r] = mp
			roundHits[r] = hits
			roundSearchedDocs[r] = searchedDocs
			lock.Unlock()
		}(r)
	}
	wg.Wait()
	// 汇总后处理结果
	if len(errs) > 0 {
		return nil, stderr.Internal.Errorf("hippo search failed with %+v", errs)
	}
	hits := make(map[string]*pb.Hit)
	allSearchedDocs := make(map[string]struct{})
	for r := 0; r < realMax; r++ {
		cMp := roundMp[r]
		for k, v := range cMp {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				chunks = append(chunks, &pb.ChunkRetrieveResult{
					Chunk: &pb.Chunk{Id: k},
					Score: v.Score,
				})
			}
		}
		cHits := roundHits[r]
		for k, v := range cHits {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				hits[k] = v
			}
		}
		cSearchedDocs := roundSearchedDocs[r]
		for k := range cSearchedDocs {
			allSearchedDocs[k] = struct{}{}
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks, VectorIndexHits: hits}, nil
}

func (h *HippoHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if !h.IsExternal {
		// 兼容2.0之前的知识库
		if helper.VersionCheck(h.KnowledgeBase.Version, "2.0.0") < 0 {
			return h.innerRecallV1(ctx, req)
		}
		return h.innerRecallParallel(ctx, req)
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	if params == nil {
		return nil, fmt.Errorf("recall params is nil")
	}
	vec, err := h.Embedding(ctx, req.Query)
	if err != nil {
		return nil, err
	}

	var expr string
	if len(req.DocRange) > 0 {
		expr = fmt.Sprintf("%s in %s", h.TitleField, h.rangeStr(req.DocRange))
	}

	search, err := h.Cli.Search(ctx, &hippo.SearchParams{
		OutputFields: []string{h.TextField},
		TopK:         int(params.TopK),
		Vectors:      [][]float32{vec},
		Table:        h.Table,
		Expr:         expr,
		Normalize:    true,
		Params:       map[string]any{},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "hippo cli search")
	}

	item := search.Results[0]
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(search.Results))
	texts := item.Fields[0]
	if texts.FieldName != h.TextField {
		return nil, fmt.Errorf("unexcepted field name of search result. %v", item)
	}
	for i := 0; i < len(item.Scores); i++ {
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{Content: texts.Data[i].(string)},
			Score: item.Scores[i],
		})
	}
	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{Request: req, Result: chunks}, nil
}

func (h *HippoHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	startTime := time.Now()

	// 过滤掉不需要向量化索引的chunks
	chunks = utils.FilterSlice(chunks, func(i int) bool { return !chunks[i].DisableVectorIndexing })

	batches := utils.BatchifySlice(chunks, conf.Config.KnowlhubConfig.VectorIndexingBatchSize)
	cnt := 0
	errs := make(map[int]string)
	var embddingCosts, hippoCosts time.Duration
	for i, batch := range batches {
		sz := len(batch)
		tp := time.Now()
		texts := make([]string, sz)
		for i, item := range batch {
			texts[i] = item.Text
		}
		vecs, err := h.BatchEmbedding(ctx, utils.CvcT2AnySlice(texts))
		if err != nil {
			return err
		}
		embddingCosts += time.Since(tp)

		tp = time.Now()
		idColData := make([]string, 0, sz)
		oriIdColData := make([]string, 0, sz)
		docIdColData := make([]string, 0, sz)
		vecColData := make([][]float32, 0, sz)
		priorityData := make([]int32, 0, sz)
		shortIdData := make([]string, 0, sz)
		for i := 0; i < sz; i++ {
			cnt += 1
			idColData = append(idColData, batch[i].Id)
			oriIdColData = append(oriIdColData, batch[i].OriId)
			docIdColData = append(docIdColData, batch[i].DocId)
			vecColData = append(vecColData, vecs[i])
			priorityData = append(priorityData, int32(batch[i].Priority))
			shortIdData = append(shortIdData, batch[i].ShortId)
		}
		idCol := hippo.NewColumnData(h.IdField, idColData)
		oriIdCol := hippo.NewColumnData(h.OriginalIdField, oriIdColData)
		docIdCol := hippo.NewColumnData(h.TitleField, docIdColData)
		vecCol := hippo.NewColumnData(h.VecField, vecColData)
		priorityCol := hippo.NewColumnData(h.PriorityField, priorityData)
		shortIdCol := hippo.NewColumnData(h.ShortIdField, shortIdData)

		_, err = h.Cli.Insert(ctx, h.Table, idCol, oriIdCol, docIdCol, priorityCol, shortIdCol, vecCol)
		if err != nil {
			errs[i] = err.Error()
			continue
		}
		hippoCosts += time.Since(tp)

		if (i+1)%50 == 0 { // print time costs debug info every 50 batches
			stdlog.Infof("HippoHandler.SubmitChunks [at batch %d] time consumption(avg over recent 50 batches): embedding: %s, hippo: %s", i+1, embddingCosts/50, hippoCosts/50)
			embddingCosts, hippoCosts = 0, 0
		}

		// 修改chunk的flag
		idsMap := make(map[string]struct{})
		ids := make([]string, 0)
		for _, chunk := range batch {
			if _, ok := idsMap[chunk.OriId]; !ok {
				idsMap[chunk.OriId] = struct{}{}
				ids = append(ids, chunk.OriId)
			}
		}
		err = GetChunkStore(h.KnowledgeBase.ProjectId).BatchFlagChunks(ids, models.IndexFlagALL)
		if err != nil {
			errs[i] = err.Error()
		}
	}
	stdlog.Infof("SubmitChunks[hippo], total costs: %v seconds, inserted %d chunks", time.Since(startTime).Seconds(), cnt)
	if len(errs) > 0 {
		return stderr.Errorf("hybase cli: SubmitChunks: %+v", errs)
	}
	return nil
}

func (h *HippoHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented")
}

func (h *HippoHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	_, err := h.Cli.DeleteByQuery(ctx, h.Table, fmt.Sprintf("%s in ['%s']", h.TitleField, req.DocId))
	return err
}

func (h *HippoHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	if len(chunkIds) > 0 {
		_, err := h.Cli.DeleteByQuery(ctx, h.Table, fmt.Sprintf("%s in ['%s']", h.IdField, h.rangeStr(chunkIds)))
		if err != nil {
			return stderr.Wrap(err, "DeleteChunksById[hippo] ids:%v", chunkIds)
		}
	}

	return nil
}

func (h *HippoHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	if len(oriChunkIds) > 0 {
		_, err := h.Cli.DeleteByQuery(ctx, h.Table, fmt.Sprintf("%s in ['%s']", h.OriginalIdField, h.rangeStr(oriChunkIds)))
		if err != nil {
			return stderr.Wrap(err, "DeleteChunksByOriId[hippo] ori_ids:%v", oriChunkIds)
		}
	}

	return nil
}

func (h *HippoHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	_, err := h.Cli.DeleteByQuery(ctx, h.Table, fmt.Sprintf("%s = '%s'", h.TitleField, docId))
	if err != nil {
		return stderr.Wrap(err, "DeleteChunksByDocId[hippo] doc_id:%v", docId)
	}
	return nil
}

func (h *HippoHandler) RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	strIds := make([]string, 0)
	for _, id := range docIds {
		strIds = append(strIds, fmt.Sprintf("'%s'", id))
	}
	_, err := h.Cli.DeleteByQuery(ctx, h.Table, fmt.Sprintf("%s in [%s]", h.TitleField, strings.Join(strIds, ",")))
	return err
}

func (h *HippoHandler) CountDocuments(ctx context.Context) (int32, error) {
	if h.IsExternal {
		docRowsMap, err := h.getDocumentRows(ctx)
		if err != nil {
			return 0, err
		}
		return int32(len(docRowsMap)), nil
	}
	return GetDocumentStore().CountByKnowledgeBase(h.KnowledgeBase.Id)
}

func (h *HippoHandler) checkTable(ctx context.Context) (err error) {
	lock := checkTableLockMp.Get(h.KnowledgeBase.Id)
	lock.Lock()
	defer lock.Unlock()

	hasTable, err := h.Cli.HasTable(ctx, h.Cli.GetCfg().Database, h.Table)
	if err != nil {
		return stderr.Wrap(err, "hippo cli: HasTable")
	}
	if !hasTable {
		err = h.createTable(ctx)
		if err != nil {
			return
		}
		err = h.Cli.PrepareTable(ctx, h.Table)
		if err != nil {
			return stderr.Wrap(err, "hippo cli: prepare table")
		}
	}
	return nil
}

func getInnerHippoTable(kb *models.KnowledgeBase) string {
	return fmt.Sprintf("llm-%s", kb.Id)
}

func getExternalHippoTable(kb *models.KnowledgeBase) string {
	return kb.ConnectionRegistry.Table
}

// return map[string]int, key: 文档名称, value: 行数(chunk数量或段落数)
func (h *HippoHandler) getDocumentRows(ctx context.Context) (mp map[string]int, err error) {
	rsp, err := h.Cli.Query(ctx, &hippo.SearchParams{
		Table:        h.Table,
		OutputFields: []string{h.titleField()},
	})
	if err != nil {
		return
	}

	mp = make(map[string]int)

	for _, v := range rsp.FieldsData[0].FieldValues {
		if doc, ok := v.(string); ok {
			mp[doc] += 1
		} else {
			err = fmt.Errorf("unexpected type of FieldValue in hippo.HippoQueryRsp")
			return
		}
	}
	return mp, nil
}

func (h *HippoHandler) newDoc(doc string) *pb.Document {
	return &pb.Document{
		DocId:   doc,
		DocName: doc,
	}
}

func (h *HippoHandler) listChunkByIds(ctx context.Context, ids []string) (ret []*pb.ChunkInfo, err error) {
	var idsStr string
	for i, v := range ids {
		if i != 0 {
			idsStr += ", "
		}
		idsStr += fmt.Sprintf("%s", v)
	}

	displayFields := h.getDisplayFields()
	params := &hippo.SearchParams{
		Table:        h.Table,
		OutputFields: []string{h.textField()},
		Expr:         fmt.Sprintf("%s in [%s]", h.idField(), idsStr),
	}
	if len(displayFields) > 0 {
		params.OutputFields = append(params.OutputFields, displayFields...)
	}
	rsp, err := h.Cli.Query(ctx, params)
	if err != nil {
		return nil, stderr.Wrap(err, "hippo cli: Query")
	}

	total := len(ids)
	ret = make([]*pb.ChunkInfo, total)
	for i := 0; i < total; i++ {
		ret[i] = new(pb.ChunkInfo)

		ret[i].Chunk = &pb.Chunk{
			Id: ids[i],
		}
	}

	for _, field := range rsp.FieldsData {
		if field.FieldName == h.textField() {
			for i, v := range field.FieldValues {
				vs := v.(string)
				ret[i].Chunk.Content = vs
			}
		} else {
			for i, v := range field.FieldValues {
				ret[i].DisplayInfos = append(ret[i].DisplayInfos, &pb.DisplayInfo{
					Name:  field.FieldName,
					Value: fmt.Sprintf("%v", v),
				})
			}
		}
	}

	return ret, nil
}

// func (h *HippoHandler) listChunkByDoc(ctx context.Context, doc string) (ret []*pb.ChunkInfo, err error) {

// 	fields := h.getDisplayFields()
// 	fields = append(fields, h.textField())
// 	rsp, err := clients.HippoCli.ListByDoc(ctx, h.Table, doc, fields)
// 	if err != nil {
// 		return nil, stderr.Wrap(err, "HippoCli ListByDoc")
// 	}

// 	total := len(ids)
// 	ret = make([]*pb.ChunkInfo, total)
// 	for i := 0; i < total; i++ {
// 		ret[i] = new(pb.ChunkInfo)
// 		ret[i].ChunkId = strconv.FormatInt(ids[i], 10)
// 	}

// 	for _, field := range rsp.FieldsData {
// 		if field.FieldName == h.textField() {
// 			for i, v := range field.FieldValues {
// 				ret[i].Content = v.(string)
// 			}
// 		} else {
// 			for i, v := range field.FieldValues {
// 				ret[i].DisplayInfos = append(ret[i].DisplayInfos, &pb.DisplayInfo{
// 					Name:  field.FieldName,
// 					Value: fmt.Sprintf("%v", v),
// 				})
// 			}
// 		}
// 	}

// 	return ret, nil
// }

func (h *HippoHandler) getDisplayFields() []string {
	return []string{}
}

func (h *HippoHandler) textField() string {
	return h.TextField
}

func (h *HippoHandler) titleField() string {
	return h.TitleField
}

func (h *HippoHandler) vecField() string {
	return h.VecField
}

func (h *HippoHandler) idField() string {
	return h.IdField
}

func (h *HippoHandler) rangeStr(values []string) string {
	var s string
	for i, v := range values {
		if i != 0 {
			s += ","
		}
		s += fmt.Sprintf("'%s'", v)
	}
	return "[" + s + "]"
}

func (h *HippoHandler) createTable(ctx context.Context) error {
	cfg := h.Cli.GetCfg()
	vecModel := h.KnowledgeBase.VectorModel
	dim, err := GetVecModelDim(vecModel)
	if err != nil {
		return err
	}
	req := &hippo.CreateTableReq{
		Settings: hippo.TableSettings{
			NumberOfShards:   1,
			NumberOfReplicas: 1,
		},
		Schema:    innerTableSchema(dim),
		TableName: h.Table,
		Database:  cfg.Database,
	}
	err = h.Cli.CreateTable(ctx, req)
	if err != nil {
		return stderr.Internal.Cause(err, "hippo cli: CreateTable")
	}
	if _, err := h.Cli.CreateIndex(ctx, cfg.Database, h.Table, innerEmbeddingIndex()); err != nil {
		return stderr.Wrap(err, "hippo cli: CreateIndex")
	}
	return nil
}

func (h *HippoHandler) VectorName() string {
	return pb.ConnectionType_HIPPO.String()
}

func (h *HippoHandler) Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error) {
	return NewInternalHippoHandler
}
