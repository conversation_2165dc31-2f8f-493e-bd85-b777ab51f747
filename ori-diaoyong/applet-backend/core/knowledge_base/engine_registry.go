package knowledge_base

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type EngineRegistry struct {
	vectorStores   map[string]func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error)
	fulltextStores map[string]func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error)
}

var (
	engineRegistry *EngineRegistry
)

func initEngineRegistry() {
	engineRegistry = &EngineRegistry{
		vectorStores:   make(map[string]func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error)),
		fulltextStores: make(map[string]func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error)),
	}
	// 注册现有的handler
	RegisterVector(&HippoHandler{})
	RegisterVector(&HybaseVectorHandler{})
	RegisterFullText(&ScopeHand<PERSON>{})
	RegisterFullText(&HybaseFullTextHandler{})
}

func GetVectorConstructor(name string) (func(ctx context.Context, kb *models.KnowledgeBase) (VectorHandler, error), error) {
	if c, ok := engineRegistry.vectorStores[name]; ok {
		return c, nil
	} else {
		return nil, stderr.Internal.Errorf("vector engine %s not found", name)
	}
}

func GetFullTextConstructor(name string) (func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error), error) {
	if c, ok := engineRegistry.fulltextStores[name]; ok {
		return c, nil
	} else {
		return nil, stderr.Internal.Errorf("fulltext engine %s not found", name)
	}
}

func RegisterVector(handler VectorHandler) {
	engineRegistry.vectorStores[handler.VectorName()] = handler.Constructor()
}

func RegisterFullText(handler FullTextHandler) {
	engineRegistry.fulltextStores[handler.FullTextName()] = handler.Constructor()
}
