package knowledge_base

import (
	"context"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type LinkedDocumentBatch struct {
	ch     chan string
	done   chan struct{}
	result map[string]*LinkedDocument
}

type LinkedDocument struct {
	Doc          *models.Document
	LinkedChunks map[string]*LinkedChunk
}

type LinkedChunk struct {
	Chunk *models.Chunk
	Above *LinkedChunk
	Later *LinkedChunk
}

const (
	BatchSize = 10
)

func NewLinkedDocumentBatch() *LinkedDocumentBatch {
	return &LinkedDocumentBatch{
		ch:     make(chan string, BatchSize),
		done:   make(chan struct{}),
		result: make(map[string]*LinkedDocument),
	}
}

func (b *LinkedDocumentBatch) Start() {
	go func() {
		wg := sync.WaitGroup{}
		for id := range b.ch {
			wg.Add(1)
			go func(id string) {
				doc, err := GetLinkedDocument(id)
				if err != nil {
					stdlog.WithError(err).<PERSON>rrorf("error getting linked document %s", id)
				} else {
					b.result[id] = doc
				}
				wg.Done()
			}(id)
		}
		wg.Wait()
		close(b.done)
	}()
}

func (b *LinkedDocumentBatch) Done() <-chan struct{} {
	return b.done
}

func (b *LinkedDocumentBatch) Submit(docID string) {
	b.ch <- docID
}

func (b *LinkedDocumentBatch) End() {
	close(b.ch)
}

func (b *LinkedDocumentBatch) Close() {
	close(b.done)
	close(b.ch)
}

func (b *LinkedDocumentBatch) Result() map[string]*LinkedDocument {
	return b.result
}

func (b *LinkedDocumentBatch) Process(chunkRetrieves []*pb.ChunkRetrieveResult, contextNum int32) []*pb.ChunkRetrieveResult {
	if contextNum <= 0 {
		return chunkRetrieves
	}
	timeout, cancel := context.WithTimeout(context.Background(), time.Minute*10)
	defer cancel()
	select {
	case <-timeout.Done():
		stdlog.Errorf("waiting for get linked document timeout.")
	case <-b.Done():
		// 获取结束
		links := b.Result()
		for _, chunkRetrieve := range chunkRetrieves {
			linkedDoc, ok := links[chunkRetrieve.DocId]
			if !ok {
				stdlog.Warnf("linked doc [%s] not found", chunkRetrieve.DocId)
				continue
			}
			// 如果是外部知识库检索，是没有chunk id的
			if chunkRetrieve.Chunk == nil {
				continue
			}
			c := linkedDoc.LinkedChunks[chunkRetrieve.Chunk.Id]
			for i := 0; i < int(contextNum); i++ {
				if c == nil {
					break
				}
				if c.Above != nil {
					chunkRetrieve.AboveChunks = append([]*pb.Chunk{c.Above.Chunk.ToPb()}, chunkRetrieve.AboveChunks...)
				}
				c = c.Above
			}
			c = linkedDoc.LinkedChunks[chunkRetrieve.Chunk.Id]
			for i := 0; i < int(contextNum); i++ {
				if c == nil {
					break
				}
				if c.Later != nil {
					chunkRetrieve.LaterChunks = append(chunkRetrieve.LaterChunks, c.Later.Chunk.ToPb())
				}
				c = c.Later
			}
		}
	}
	return chunkRetrieves
}

// GetLinkedDocument 获取document，构建链表
func GetLinkedDocument(docID string) (*LinkedDocument, error) {
	doc, err := GetDocumentStore().Load(docID)
	if err != nil {
		return nil, err
	}
	chunks, err := GetChunkStore(doc.ProjectId).LoadByDocument(docID)
	if err != nil {
		return nil, err
	}
	cacheChunks := make(map[string]*LinkedChunk)
	for i := 0; i < len(chunks); i++ {
		cacheChunk := &LinkedChunk{
			Chunk: chunks[i],
		}
		cacheChunks[chunks[i].Id] = cacheChunk
		if i > 0 {
			cacheChunk.Above = cacheChunks[chunks[i-1].Id]
			cacheChunks[chunks[i-1].Id].Later = cacheChunk
		}
	}
	return &LinkedDocument{
		Doc:          doc,
		LinkedChunks: cacheChunks,
	}, nil
}
