package knowledge_base

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"github.com/elastic/go-elasticsearch/v6"
	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	TermsTitle = "terms_title"
)

var (
	checkIndexLockMp = stdsync.NewLockMap()
)

type ScopeHandler struct {
	*HandlerBase
	initOnce      sync.Once
	Cli           *stdes.Client
	IndexName     string
	TextField     string // 文本内容字段名
	TitleField    string // 文档标题字段名
	IdField       string
	OriIdField    string
	PriorityField string
	ShortIdField  string
	IsExternal    bool
}

func (h *ScopeHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "ScopeHandler.getDocumentRows")
	}
	result := make([]*pb.DocumentInfo, 0, len(docRowsMap))
	for doc, count := range docRowsMap {
		result = append(result, &pb.DocumentInfo{
			Doc: &pb.Document{
				DocId:   doc,
				DocName: doc,
			},
			NumChunks: int64(count),
		})
	}
	return &pb.ListDocumentsRsp{Result: result}, nil
}

func (h *ScopeHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	root := new(pb.DocumentTree)
	root.Node = &pb.DocumentNode{
		Category: pb.DocumentNodeCategory_DIR,
		Id:       h.KnowledgeBase.Id,
		Name:     h.KnowledgeBase.Name,
	}

	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "ScopeHandler.getDocumentRows")
	}
	for doc, _ := range docRowsMap {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       doc,
				Name:     doc,
			},
		}
		root.Children = append(root.Children, subTree)
	}
	if len(docRowsMap) == 0 {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       "全量文本",
				Name:     "全量文本",
			},
		}
		root.Children = append(root.Children, subTree)
	}
	return &pb.GetDocumentTreeRsp{Tree: root}, nil
}

func (h *ScopeHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				h.TitleField: docId,
			},
		},
	}
	rsp := new(stdes.RecallResp)
	err := h.Cli.Search(ctx, h.IndexName, query, rsp)
	if err != nil {
		return nil, err
	}

	var chunks []*pb.ChunkInfo
	for _, hit := range rsp.Hits.Hits {
		item := hit.Source
		text := item[h.TextField].(string)
		chunk := pb.Chunk{
			Id:      hit.ID,
			Content: text,
		}
		displays := []*pb.DisplayInfo{}
		for k, v := range item {
			if k == h.TextField {
				continue
			}
			displays = append(displays, &pb.DisplayInfo{Name: k, Value: fmt.Sprintf("%v", v)})
		}
		chunks = append(chunks, &pb.ChunkInfo{
			Chunk:        &chunk,
			DisplayInfos: displays,
		})
	}
	return &pb.ListDocumentChunksRsp{
		Result: chunks,
	}, nil

}

func (h *ScopeHandler) checkIndex(ctx context.Context) error {
	lock := checkIndexLockMp.Get(h.KnowledgeBase.Id)
	lock.Lock()
	defer lock.Unlock()
	ex, err := h.Cli.IndexExists(ctx, h.IndexName)
	if err != nil {
		return err
	}
	if !ex {
		err = h.Cli.CreateIndex(ctx, h.IndexName, DefaultMapping)
		if err != nil {
			return err
		}
	}
	return nil
}

func (h *ScopeHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	err := h.initStore(ctx)
	if err != nil {
		return err
	}
	startTime := time.Now()
	// create index if not exists

	// 过滤掉不需要全文索引的chunks
	chunks = utils.FilterSlice(chunks, func(i int) bool { return !chunks[i].DisableFullTextIndexing })
	cnt := 0
	errs := make(map[int]string)
	batches := utils.BatchifySlice(chunks, conf.Config.KnowlhubConfig.FullTextIndexingBatchSize)
	for i, batch := range batches {
		bulkOps := make([]*stdes.BulkOperation, 0, len(batch))
		for _, chunk := range batch {
			cnt += 1
			op := &stdes.BulkOperation{
				Action: stdes.BulkOpIndex,
				Source: stdes.BulkOpSource{},
				Data:   makeIndexBody(chunk),
			}
			bulkOps = append(bulkOps, op)
		}

		err = h.Cli.Bulk(ctx, h.IndexName, bulkOps)
		if err != nil {
			errs[i] = err.Error()
			continue
		}
		// 修改chunk的flag
		idsMap := make(map[string]struct{})
		ids := make([]string, 0)
		for _, chunk := range batch {
			if _, ok := idsMap[chunk.OriId]; !ok {
				idsMap[chunk.OriId] = struct{}{}
				ids = append(ids, chunk.OriId)
			}
		}
		err = GetChunkStore(h.KnowledgeBase.ProjectId).BatchFlagChunks(ids, models.IndexFlagALL)
		if err != nil {
			errs[i] = err.Error()
		}
	}
	stdlog.Infof("SubmitChunks[scope], total costs: %v seconds, inserted %d chunks", time.Since(startTime).Seconds(), cnt)
	return nil
}

func makeIndexBody(c *models.ChunkForIndexing) map[string]any {
	ret := make(map[string]any)
	ret[InnerIdField] = c.Id
	ret[InnerTextField] = c.Text
	ret[InnerOriIdField] = c.OriId
	ret[InnerTitleField] = c.DocId
	ret[InnerPriorityField] = c.Priority
	ret[InnerShortIdField] = c.ShortId
	return ret
}

func (h *ScopeHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented")
}

func (h *ScopeHandler) Drop(ctx context.Context) error {
	if h.IsExternal {
		return ErrCouldNotUpdateExternalKB
	}
	if err := h.initStore(ctx); err != nil {
		return err
	}

	return h.Cli.DeleteIndex(ctx, h.IndexName)
}

func (h *ScopeHandler) innerRecallV1(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams

	mustConds := []map[string]any{}
	// text match
	mustConds = append(mustConds, map[string]any{
		"match": map[string]interface{}{
			h.TextField: req.Query,
		}})

	// docId in range
	if len(req.DocRange) > 0 {
		termsCond := map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): req.DocRange,
			},
		}
		mustConds = append(mustConds, termsCond)
	}

	query := map[string]interface{}{
		"size":      params.TopK,
		"min_score": params.ScoreThreshold,
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustConds,
			},
		},
	}

	if bs, err := json.Marshal(query); err == nil {
		stdlog.Infof("scope search query:\n%s", string(bs))
	}

	rsp := new(stdes.RecallResp)
	err := h.Cli.Search(ctx, h.IndexName, query, rsp)
	if err != nil {
		return nil, err
	}

	if rsp.Hits == nil {
		return nil, stderr.Internal.Error("ScopeHandler.Recall: rsp.Hits is nil")
	}

	// normalize hits score
	normalizeSearchHits(rsp.Hits.Hits)

	// construct results
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(rsp.Hits.Hits))
	hitsMap := make(map[string]*pb.Hit)
	if h.IsExternal {
		for _, hit := range rsp.Hits.Hits {
			hitsMap[hit.ID] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{
					Id:      hit.ID,
					Content: hit.Source[h.TextField].(string),
				},
				Score:           hit.Score,
				DocId:           hit.Source[h.TitleField].(string),
				KnowledgeBaseId: h.KnowledgeBase.Id,
			})
		}
	} else {
		// oriId -> maxScore
		mp := map[string]float32{}

		for _, hit := range rsp.Hits.Hits {
			id := hit.Source[h.IdField].(string)
			oriId := hit.Source[h.OriIdField].(string)
			hitsMap[id] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			if exScore, ok := mp[oriId]; ok && exScore > hit.Score {
				continue
			} else {
				mp[oriId] = hit.Score
			}
		}
		for oriId, maxScore := range mp {
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{Id: oriId},
				Score: maxScore,
			})
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            chunks,
		FullTextIndexHits: hitsMap,
	}, nil
}

func (h *ScopeHandler) innerRecall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	cRange := req.DocRange

	chunks := make([]*pb.ChunkRetrieveResult, 0)
	hitsMap := make(map[string]*pb.Hit)
	// oriId -> maxScore
	mp := map[string]float32{}

	for r := 0; r < maxRecallCount; r++ {
		mustConds := []map[string]any{}
		filterConds := []map[string]any{}
		// text match
		mustConds = append(mustConds, map[string]any{
			"match": map[string]interface{}{
				h.TextField: req.Query,
			}})
		filterConds = append(filterConds, map[string]any{
			"terms": map[string]any{
				termsKey(h.PriorityField): r,
			},
		})
		// docId in range
		if len(cRange) > 0 {
			filterCond := map[string]any{
				"terms": map[string]any{
					termsKey(h.ShortIdField): req.DocRange,
				},
			}
			filterConds = append(filterConds, filterCond)
		} else {
			// 说明之前的批次已经全部返回所有文档了
			break
		}

		query := map[string]interface{}{
			"size":      params.TopK,
			"min_score": params.ScoreThreshold,
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must":   mustConds,
					"filter": filterConds,
				},
			},
		}
		if bs, err := json.Marshal(query); err == nil {
			stdlog.Infof("scope search query:\n%s", string(bs))
		}

		rsp := new(stdes.RecallResp)
		err := h.Cli.Search(ctx, h.IndexName, query, rsp)
		if err != nil {
			return nil, err
		}

		if rsp.Hits == nil {
			return nil, stderr.Internal.Error("ScopeHandler.Recall: rsp.Hits is nil")
		}

		// normalize hits score
		normalizeSearchHits(rsp.Hits.Hits)
		searchedDocs := make(map[string]struct{})
		for _, hit := range rsp.Hits.Hits {
			id := hit.Source[h.IdField].(string)
			oriId := hit.Source[h.OriIdField].(string)
			shortId := hit.Source[h.ShortIdField].(string)
			hitsMap[id] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
				Round: int32(r),
			}
			searchedDocs[shortId] = struct{}{}
			if exScore, ok := mp[oriId]; ok && exScore > hit.Score {
				continue
			} else {
				mp[oriId] = hit.Score
			}
		}
		// 从docRange中剔除已经返回过的文档
		newRange := make([]string, 0)
		for _, id := range cRange {
			if _, ok := searchedDocs[id]; !ok {
				newRange = append(newRange, id)
			}
		}
		cRange = newRange
	}
	for oriId, maxScore := range mp {
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{Id: oriId},
			Score: maxScore,
		})
	}
	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            chunks,
		FullTextIndexHits: hitsMap,
	}, nil
}

// innerRecallParallel 并行查询
func (h *ScopeHandler) innerRecallParallel(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams
	cRange := req.DocRange
	wg := sync.WaitGroup{}
	realMax := maxRecallCount
	if h.KnowledgeBase.ContentType == pb.KnowledgeBaseContentType_TABLE {
		realMax = 1
	}
	wg.Add(realMax)

	chunks := make([]*pb.ChunkRetrieveResult, 0)
	roundHits := make(map[int]map[string]*pb.Hit)
	roundMp := make(map[int]map[string]*pb.Hit)
	roundSearchedDocs := make(map[int]map[string]struct{})
	lock := sync.Mutex{}
	errs := make([]error, 0)

	for r := 0; r < realMax; r++ {
		go func(r int) {
			defer wg.Done()
			mustConds := []map[string]any{}
			filterConds := []map[string]any{}
			// text match
			mustConds = append(mustConds, map[string]any{
				"match": map[string]interface{}{
					h.TextField: req.Query,
				}})
			filterConds = append(filterConds, map[string]any{
				"term": map[string]any{
					termsKey(h.PriorityField): r,
				},
			})
			// docId in range
			if len(cRange) > 0 {
				filterCond := map[string]any{
					"terms": map[string]any{
						termsKey(h.ShortIdField): req.DocRange,
					},
				}
				filterConds = append(filterConds, filterCond)
			}

			query := map[string]interface{}{
				"size":      params.TopK,
				"min_score": params.ScoreThreshold,
				"query": map[string]interface{}{
					"bool": map[string]interface{}{
						"must":   mustConds,
						"filter": filterConds,
					},
				},
			}
			if bs, err := json.Marshal(query); err == nil {
				stdlog.Infof("scope search query:\n%s", string(bs))
			}

			rsp := new(stdes.RecallResp)
			err := h.Cli.Search(ctx, h.IndexName, query, rsp)
			if err != nil {
				errs = append(errs, stderr.Wrap(err, "ScopeHandler.RecallParallel error"))
				return
			}

			if rsp.Hits == nil {
				errs = append(errs, stderr.Internal.Errorf("ScopeHandler.RecallParallel: rsp.Hits is nil"))
				return
			}

			hitsMap := make(map[string]*pb.Hit)
			// oriId -> maxScore
			mp := make(map[string]*pb.Hit)

			// normalize hits score
			normalizeSearchHits(rsp.Hits.Hits)
			searchedDocs := make(map[string]struct{})
			for _, hit := range rsp.Hits.Hits {
				id := hit.Source[h.IdField].(string)
				oriId := hit.Source[h.OriIdField].(string)
				shortId := hit.Source[h.ShortIdField].(string)
				ph := &pb.Hit{
					Hit:     true,
					Score:   hit.Score,
					ShortId: shortId,
					Round:   int32(r),
				}
				hitsMap[id] = ph
				searchedDocs[shortId] = struct{}{}
				if exScore, ok := mp[oriId]; ok && exScore.Score > hit.Score {
					continue
				} else {
					mp[oriId] = ph
				}
			}
			lock.Lock()
			roundMp[r] = mp
			roundHits[r] = hitsMap
			roundSearchedDocs[r] = searchedDocs
			lock.Unlock()
		}(r)
	}

	wg.Wait()
	// 汇总后处理结果
	if len(errs) > 0 {
		return nil, stderr.Internal.Errorf("ScopeHandler.RecallParallel errors: %+v", errs)
	}
	hits := make(map[string]*pb.Hit)
	allSearchedDocs := make(map[string]struct{})
	for r := 0; r < realMax; r++ {
		cMp := roundMp[r]
		for k, v := range cMp {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				chunks = append(chunks, &pb.ChunkRetrieveResult{
					Chunk: &pb.Chunk{Id: k},
					Score: v.Score,
				})
			}
		}
		cHits := roundHits[r]
		for k, v := range cHits {
			if _, ok := allSearchedDocs[v.ShortId]; !ok {
				hits[k] = v
			}
		}
		cSearchedDocs := roundSearchedDocs[r]
		for k, _ := range cSearchedDocs {
			allSearchedDocs[k] = struct{}{}
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            chunks,
		FullTextIndexHits: hits,
	}, nil
}

// Recall 从全文索引召回query相关的文档
// 外部库的text字段即为Chunk内容；内部库召回以Chunk Id为准(text可能仅为索引内容，非召回内容)，后续需从Chunk Store查询取回Chunk
func (h *ScopeHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if !h.IsExternal {
		if helper.VersionCheck(h.KnowledgeBase.Version, "2.0.0") < 0 {
			return h.innerRecallV1(ctx, req)
		}
		return h.innerRecallParallel(ctx, req)
	}
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams

	mustConds := []map[string]any{}
	// text match
	mustConds = append(mustConds, map[string]any{
		"match": map[string]interface{}{
			h.TextField: req.Query,
		}})

	// docId in range
	if len(req.DocRange) > 0 {
		termsCond := map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): req.DocRange,
			},
		}
		mustConds = append(mustConds, termsCond)
	}

	query := map[string]interface{}{
		"size":      params.TopK,
		"min_score": params.ScoreThreshold,
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustConds,
			},
		},
	}

	if bs, err := json.Marshal(query); err == nil {
		stdlog.Infof("scope search query:\n%s", string(bs))
	}

	rsp := new(stdes.RecallResp)
	err := h.Cli.Search(ctx, h.IndexName, query, rsp)
	if err != nil {
		return nil, err
	}

	if rsp.Hits == nil {
		return nil, stderr.Internal.Error("ScopeHandler.Recall: rsp.Hits is nil")
	}

	// normalize hits score
	normalizeSearchHits(rsp.Hits.Hits)

	// construct results
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(rsp.Hits.Hits))
	hitsMap := make(map[string]*pb.Hit)
	if h.IsExternal {
		for _, hit := range rsp.Hits.Hits {
			hitsMap[hit.ID] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{
					Id:      hit.ID,
					Content: hit.Source[h.TextField].(string),
				},
				Score:           hit.Score,
				DocId:           hit.Source[h.TitleField].(string),
				KnowledgeBaseId: h.KnowledgeBase.Id,
			})
		}
	} else {
		// oriId -> maxScore
		mp := map[string]float32{}

		for _, hit := range rsp.Hits.Hits {
			id := hit.Source[h.IdField].(string)
			oriId := hit.Source[h.OriIdField].(string)
			hitsMap[id] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			if exScore, ok := mp[oriId]; ok && exScore > hit.Score {
				continue
			} else {
				mp[oriId] = hit.Score
			}
		}
		for oriId, maxScore := range mp {
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{Id: oriId},
				Score: maxScore,
			})
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            chunks,
		FullTextIndexHits: hitsMap,
	}, nil
}

func normalizeSearchHits(hits []*stdes.SearchHit) {
	for _, hit := range hits {
		hit.Score = utils.Sigmoid(hit.Score)
	}
}

func (h *ScopeHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"term": map[string]any{
				termsKey(h.TitleField): req.DocId,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	return err
}

func (h *ScopeHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.IdField): chunkIds,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}

	return nil
}

func (h *ScopeHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.OriIdField): oriChunkIds,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}
	return nil
}

func (h *ScopeHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): []string{docId},
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}
	return nil
}

func (h *ScopeHandler) RemoveFilesFromKnowledgeBase(ctx context.Context, docIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): docIds,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}
	return nil
}

func (h *ScopeHandler) CountDocuments(ctx context.Context) (int32, error) {
	if h.IsExternal {
		docRowsMap, err := h.getDocumentRows(ctx)
		if err != nil {
			return 0, err
		}
		return int32(len(docRowsMap)), nil
	}
	return GetDocumentStore().CountByKnowledgeBase(h.KnowledgeBase.Id)
}

func (h *ScopeHandler) getDocumentRows(ctx context.Context) (mp map[string]int, err error) {

	terms, err := h.Cli.Terms(ctx, h.IndexName, h.TitleField)
	if err != nil {
		return nil, err
	}
	var buckets []*stdes.AggregationBucket
	if agg, ok := terms.Aggregations[TermsTitle]; ok {
		buckets = agg.Buckets
	} else {
		return nil, stderr.Internal.Error("ScopeHandler.ListDocuments: no aggregation found")
	}

	ret := make(map[string]int)
	for _, b := range buckets {
		ret[b.Key] = b.DocCount
	}
	return ret, nil
}

func (h *ScopeHandler) initStore(ctx context.Context) (err error) {
	if h.IsExternal {
		return nil
	}

	h.initOnce.Do(func() {
		if h.Cli != nil {
			return
		}
		var cli *elasticsearch.Client
		if cli, err = clients.GetDataConnectionScopeCli(ctx, h.KnowledgeBase.ProjectId, h.KnowledgeBase.DatabaseConfig.FulltextDb); err != nil {
			return
		}
		h.Cli = stdes.NewESClient(cli)

		if err = h.checkIndex(ctx); err != nil {
			return
		}
	})
	return
}

func getInnerScopeIndex(kb *models.KnowledgeBase) string {
	return fmt.Sprintf("llm-%s", kb.Id)
}

func NewInternalScopeHandler(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	h := &ScopeHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase:      kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT},
		},
		Cli:           nil, // lazy init
		IndexName:     getInnerScopeIndex(kb),
		TextField:     InnerTextField,
		TitleField:    InnerTitleField,
		IdField:       InnerIdField,
		OriIdField:    InnerOriIdField,
		PriorityField: InnerPriorityField,
		ShortIdField:  InnerShortIdField,
		IsExternal:    false,
	}
	return h, nil
}
func NewExternalScopeHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	if kb.ConnectionRegistry == nil {
		return nil, fmt.Errorf("NewScopeHandler: kb.ConnectionRegistry is nil")
	}
	connReg := kb.ConnectionRegistry
	getConnRsp, err := clients.CVATCli.DataConnClient.GetDataConnection(context.Background(), &pb.GetConnectionReq{
		Id: connReg.Id,
	})
	if err != nil {
		return nil, err
	}
	conn := getConnRsp.Result

	cli, err := Conn2ESCli(conn)
	if err != nil {
		return nil, err
	}

	return &ScopeHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase:      kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT},
		},
		Cli:        stdes.NewESClient(cli),
		IndexName:  connReg.Table,
		TextField:  connReg.TextField,
		TitleField: connReg.DocField,
		IsExternal: true,
	}, nil
}

func termsKey(key string) string {
	// return fmt.Sprintf("%s.keyword", key)
	// 创建mapping时已经为keyword类型的field，不再需要指定 xxx.keyword
	return key
}

func (h *ScopeHandler) FullTextName() string {
	return pb.ConnectionType_SCOPE.String()
}

func (h *ScopeHandler) Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	return NewInternalScopeHandler
}
