package knowledge_base

import (
	"context"
	"github.com/google/uuid"
	"gorm.io/gen"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	maxWorkerNum  = 20
	deleteTimeout = time.Minute * 10

	imageSummary string = "image_digest"
	tableSummary string = "table_digest"
	question     string = "qa_question"
	answer       string = "qa_answer"
	docSummary   string = "doc_digest"
	docChunk     string = "chunk"
	recoverLock  string = "kbs:doc:import:task:recover"
)

var (
	ditm           *DocImportTaskManager
	indexConfigMap = map[pb.AugmentedChunkType]string{
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_SUMMARY:     imageSummary,
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY:     tableSummary,
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION: imageSummary,
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION: tableSummary,
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION:          question,
		pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_ANSWER:            answer,
	}
)

// DocImportTaskManager 用于2.0版本的向量化，入库任务的控制
type DocImportTaskManager struct {
	// 管道通知需要处理的文档
	docChan chan *DocWithKb
	q       *query.Query
	tasks   sync.Map // docId -> *models.DocTask
}

func (m *DocImportTaskManager) Create(task *models.DocTask) error {
	docId := task.DocumentProcessingProgress.Document.DocId
	if actual, loaded := m.tasks.LoadOrStore(docId, task); loaded {
		if actualTask, ok := actual.(*models.DocTask); ok {
			if !actualTask.DocumentProcessingProgress.Finished {
				return stderr.BadRequest.Errorf("already exists running doc task ")
			}
		}
		m.tasks.Store(docId, task)
	}
	return nil
}

func (m *DocImportTaskManager) Start(task *models.DocTask) {
	// 此处的start仅仅只是记录任务开始，并无实际逻辑
	doc := task.DocumentProcessingProgress.Document
	m.Store(doc.DocId)
}

func (m *DocImportTaskManager) Submit(task *models.DocTask) error {
	if err := m.Create(task); err != nil {
		return err
	}
	m.Start(task)
	return nil
}

func (m *DocImportTaskManager) Cancel(docId string) {
	t := m.Get(docId)
	if t.Cancel != nil {
		t.Cancel()
	}
}

func (m *DocImportTaskManager) Get(docId string) *models.DocTask {
	t, ok := m.tasks.Load(docId)
	if ok {
		return t.(*models.DocTask)
	}
	repo := m.q.DocTask
	task, err := repo.Where(repo.DocumentId.Eq(docId)).Take()
	if err == nil {
		return task
	}

	task = &models.DocTask{
		DocumentId: docId,
		DocumentProcessingProgress: &pb.DocumentProcessingProgress{
			Percentage:   0,
			Finished:     true,
			ErrorMessage: ErrDocTaskNotFound.Error(),
			Stage:        pb.DocumentTaskStage_WAITING_EXAMINING,
		},
	}
	return task
}

func (m *DocImportTaskManager) ListByKnowledgeBase(kbId string) []*models.DocTask {
	//TODO implement me
	panic("implement me")
}

func (m *DocImportTaskManager) Store(docId string) {
	task := m.Get(docId)
	repo := m.q.DocTask
	if err := repo.Save(task); err != nil {
		stdlog.Errorf("DocImportTaskManager.Store failed: %v", err)
	}
}

func (m *DocImportTaskManager) SubmitRebuildIndex(task *models.DocTask) error {
	//TODO implement me
	panic("implement me")
}

func initDocImportTaskManager() {
	ditm = &DocImportTaskManager{
		docChan: make(chan *DocWithKb),
		q:       dao.InitQuery(),
	}
	ditm.startWorker()
	// 重启停止的任务标记为失败
	ditm.checkTasks()
}

func GetDocImportTaskManager() *DocImportTaskManager {
	return ditm
}

// ProcessStoreReq 任务的入库，会有多个文件
func (m *DocImportTaskManager) ProcessStoreReq(ctx context.Context, req *pb.StoreTaskDocumentReq) error {
	// 获取知识库
	kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return stderr.Internal.Cause(err, "store task document error when get knowledge base [%s]", req.KnowledgeBaseId)
	}
	docs, err := GetDocumentStore().LoadByKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return stderr.Internal.Cause(err, "get docs by knowledge [%s] error.", req.KnowledgeBaseId)
	}
	repo := m.q.Document
	toProcess := make([]*models.Document, 0)
	// 过滤属于taskid的文档
	for _, doc := range docs {
		if doc.ProcessTask != nil && doc.ProcessTask.Id == req.TaskId {
			toProcess = append(toProcess, doc)
		}
	}
	wg := sync.WaitGroup{}
	wg.Add(len(toProcess))
	for _, d := range toProcess {
		go func(doc *models.Document) {
			defer wg.Done()
			var err error
			// 等待文档chunk写入完成
			maxCount := 20
			interval := time.Second * 5
			for maxCount > 0 {
				maxCount--
				doc, err = GetDocumentStore().Load(doc.Id)
				if err != nil {
					stdlog.WithError(err).Errorf("get document [%s] error.", doc.Id)
				} else {
					if doc.DbStatus == pb.DocumentDbStatus_WRITTEN {
						break
					}
				}
				stdlog.Infof("chunk is writting, waiting for %vs.", interval.Seconds())
				time.Sleep(interval)
				interval = interval * 2
			}
			// 任务入库
			task := NewDocTask(ctx, kb.Id, nil, doc.ToPb())
			err = m.Submit(task)
			if err != nil {
				stdlog.WithError(err).Errorf("submit document [%s] error.", doc.Id)
				return
			}
			if maxCount == 0 {
				stdlog.Errorf("chunk is still writing after 300s, some errors may occured")
				m.failDoc(doc, stderr.Errorf("chunk is still writing after 300s, some errors may occured"))
				return
			}
			stdlog.Infof("begin to write task [%s] documents to vector db", req.TaskId)
			// 修改document状态为嵌入中, 更新写入indexconfig
			doc.IndexConfig = req.IndexConfig
			doc.Stage = pb.DocumentTaskStage_INDEXING
			_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
			if err != nil {
				stdlog.WithError(err).Errorf("update document [%s] stage to indexing error.", doc.Id)
			}
			task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING
			m.Store(doc.Id)
			m.docChan <- &DocWithKb{
				task: task,
				doc:  doc,
				kb:   kb,
			}
		}(d)
	}
	wg.Wait()
	return nil
}

// ProcessReStoreReq 处理文档的重新入库，需要删除所有该文档的入库数据，重新根据索引配置入库
func (m *DocImportTaskManager) ProcessReStoreReq(ctx context.Context, req *pb.ReStoreDocumentReq) error {
	// 获取知识库
	kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return stderr.Internal.Cause(err, "store task document error when get knowledge base [%s]", req.KnowledgeBaseId)
	}
	doc, err := GetDocumentStore().Load(req.DocId)
	if err != nil {
		return stderr.Internal.Cause(err, "get document by docId [%s] error.", req.DocId)
	}
	// 删除该文档已有的入库数据
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return stderr.Internal.Cause(err, "new handler for [%s] error.", doc.Id)
	}
	ctx, cancel := context.WithTimeout(ctx, deleteTimeout)
	defer cancel()
	ctx = context.WithValue(ctx, "remain_db", "true")
	err = handler.DeleteChunksByDocId(ctx, req.DocId)
	if err != nil {
		return stderr.Internal.Cause(err, "delete chunks by docId [%s] error.", req.DocId)
	}
	// 任务入库
	task := NewDocTask(ctx, kb.Id, nil, doc.ToPb())
	err = m.Submit(task)
	if err != nil {
		return stderr.Internal.Cause(err, "submit document [%s] error.", doc.Id)
	}
	// 开启事务
	tran := m.q.Begin()
	// 修改document状态为嵌入中, 更新写入indexconfig
	doc.IndexConfig = req.IndexConfig
	doc.Stage = pb.DocumentTaskStage_INDEXING
	repo := tran.Document
	_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
	if err != nil {
		stdlog.WithError(err).Errorf("update document [%s] stage to indexing error.", doc.Id)
		tran.Rollback()
	}
	cRepo := tran.Chunk.Table(GetChunkStore(kb.ProjectId).GetTableName())
	_, err = cRepo.Where(cRepo.DocumentId.Eq(doc.Id)).UpdateSimple(cRepo.IndexFlag.Value(0))
	if err != nil {
		stdlog.WithError(err).Errorf("update [%s] chunks index flag error.", doc.Id)
		tran.Rollback()
	}
	tran.Commit()
	task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING
	m.Store(doc.Id)
	// 重新入库
	m.docChan <- &DocWithKb{
		task: task,
		doc:  doc,
		kb:   kb,
	}
	return nil
}

func (m *DocImportTaskManager) RetryDocumentChunks(ctx context.Context, req *pb.RetryStoreDocumentReq) error {
	// 获取知识库
	kb, err := GetKnowledgeBaseManager().takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return stderr.Internal.Cause(err, "store task document error when get knowledge base [%s]", req.KnowledgeBaseId)
	}
	docs, err := GetDocumentStore().LoadBatch(req.DocIds)
	if err != nil {
		return stderr.Internal.Cause(err, "get document by docIds %+v error.", req.DocIds)
	}
	for _, doc := range docs {
		// 任务入库
		task := NewDocTask(ctx, kb.Id, nil, doc.ToPb())
		err = m.Submit(task)
		if err != nil {
			return stderr.Internal.Cause(err, "submit document [%s] error.", doc.Id)
		}
		// 修改document状态为嵌入中, 更新写入indexconfig
		doc.Stage = pb.DocumentTaskStage_INDEXING
		repo := m.q.Document
		_, err = repo.Where(repo.Id.Eq(doc.Id)).Updates(doc)
		if err != nil {
			stdlog.WithError(err).Errorf("update document [%s] stage to indexing error.", doc.Id)
		}
		task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING
		m.Store(doc.Id)
		// 重新入库
		m.docChan <- &DocWithKb{
			task: task,
			doc:  doc,
			kb:   kb,
		}
	}
	return nil
}

func (m *DocImportTaskManager) startWorker() {
	// 启动work协程，控制并发处理入向量库的文档数量
	for i := 0; i < maxWorkerNum; i++ {
		go func() {
			for doc := range m.docChan {
				stdlog.Infof("begin to write vector db with doc [%s]", doc.doc.Id)
				// 查询doc的所有chunk
				cs := GetChunkStore(doc.kb.ProjectId)
				chunks, err := cs.LoadByCondition(func(q *query.Query) []gen.Condition {
					return []gen.Condition{
						q.Chunk.Table(cs.GetTableName()).DocumentId.Eq(doc.doc.Id),
						q.Chunk.Table(cs.GetTableName()).IndexFlag.In(models.IndexFlagNot, models.IndexFlagVector, models.IndexFlagFulltext),
					}
				})
				if err != nil {
					stdlog.WithError(err).Errorf("get chunks by doc [%s] error.", doc.doc.Id)
					m.failDoc(doc.doc, stderr.Wrap(err, "get chunks by doc [%s] error.", doc.doc.Id))
					continue
				}
				// 根据kb和索引设置调用handler入库
				handler, err := NewHandler(context.Background(), doc.kb)
				if err != nil {
					stdlog.WithError(err).Errorf("get handler with kb [%s] error.", doc.kb.Id)
					m.failDoc(doc.doc, stderr.Wrap(err, "get handler with kb [%s] error.", doc.kb.Id))
					continue
				}
				// 如果是表格文档，还需要处理表格文档的增强数据
				if doc.kb.ContentType == pb.KnowledgeBaseContentType_TABLE {
					chunks = m.processTableAugmentChunks(chunks, doc.doc)
				}
				// 现在超时由单独的batch控制
				idxChunks := makeChunksForIndexingWithIndexConfig(doc.doc, chunks, doc.kb.VectorModel)
				err = handler.SubmitChunks(doc.task.Ctx, idxChunks)
				if err != nil {
					stdlog.WithError(err).Errorf("submit chunks by doc [%s] error.", doc.doc.Id)
					m.failDoc(doc.doc, stderr.Wrap(err, "submit chunks by doc [%s] error.", doc.doc.Id))
					continue
				}
				// 判断一下是否所有chunk都是已入库
				chunks, err = cs.LoadByCondition(func(q *query.Query) []gen.Condition {
					return []gen.Condition{
						q.Chunk.Table(cs.GetTableName()).DocumentId.Eq(doc.doc.Id),
						q.Chunk.Table(cs.GetTableName()).IndexFlag.In(models.IndexFlagNot, models.IndexFlagVector, models.IndexFlagFulltext),
					}
				})
				if err != nil {
					// 忽略错误
					stdlog.WithError(err).Errorf("get chunks by doc [%s] error.", doc.doc.Id)
				}
				if len(chunks) > 0 {
					// 有没成功的切片，原因未知，忽略，不返回失败
					stdlog.Warnf("some chunks are still not indexed")
					//m.failDoc(doc.doc, stderr.Errorf("some chunks are still not indexed"))
				}
				// 修改document中成功的数量
				docRepo := m.q.Document
				_, err = docRepo.Where(docRepo.Id.Eq(doc.doc.Id)).UpdateSimple(docRepo.NumSuccessChunks.SetCol(docRepo.NumChunks.Sub(int64(len(chunks)))))
				if err != nil {
					stdlog.WithError(err).Warnf("update document [%s] success num error.", doc.doc.Id)
				}
				// 成功
				m.succeedDoc(doc.doc)
			}
		}()
	}
}

func (m *DocImportTaskManager) processTableAugmentChunks(chunks []*models.Chunk, doc *models.Document) []*models.Chunk {
	indexConfig := doc.IndexConfig
	for _, chunk := range chunks {
		// 解析chunk的content
		kv := make(map[string]string)
		err := stdsrv.Unmarshal(([]byte)(chunk.Content), &kv)
		if err != nil {
			stdlog.WithError(err).Errorf("unmarshal chunk [%s] error.", chunk.Id)
			continue
		}
		for _, vecConfig := range indexConfig.VectorIndexConfigs {
			if c, ok := kv[vecConfig.OriColumn]; vecConfig.Enabled && ok {
				// 需要新创建一个增强切片
				chunk.AugmentedChunks = append(chunk.AugmentedChunks, &pb.AugmentedChunk{
					Id:            uuid.NewString(),
					Content:       c,
					SourceType:    pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
					AugmentedType: pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD,
				})
			}
		}
	}
	return chunks
}

func (m *DocImportTaskManager) failDoc(doc *models.Document, e error) {
	repo := m.q.Document
	_, err := repo.Where(repo.Id.Eq(doc.Id)).UpdateSimple(repo.Stage.Value(int32(pb.DocumentTaskStage_INDEXING_FAILED)))
	if err != nil {
		stdlog.WithError(err).Errorf("update doc [%s] stage to failed error.", doc.Id)
	}
	task := m.Get(doc.Id)
	task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING_FAILED
	task.DocumentProcessingProgress.Finished = true
	task.DocumentProcessingProgress.ErrorMessage = e.Error()
	m.Store(doc.Id)
}

func (m *DocImportTaskManager) succeedDoc(doc *models.Document) {
	repo := m.q.Document
	_, err := repo.Where(repo.Id.Eq(doc.Id)).UpdateSimple(repo.Stage.Value(int32(pb.DocumentTaskStage_INDEXING_DONE)))
	if err != nil {
		stdlog.WithError(err).Errorf("update doc [%s] stage to failed error.", doc.Id)
	}
	task := m.Get(doc.Id)
	task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING_DONE
	task.DocumentProcessingProgress.Finished = true
	m.Store(doc.Id)
}

func (m *DocImportTaskManager) checkTasks() {
	// 防止多实例重复启动
	lock, err := clients.RedisCli.Lock(recoverLock, time.Minute*5)
	if err != nil {
		stdlog.WithError(err).Errorf("redis lock failed. skip recover.")
		return
	}
	defer clients.RedisCli.Release(lock)
	// 运行中的任务标记为失败
	repo := m.q.DocTask
	docIds := make([]string, 0)
	tasks, err := repo.Find()
	for _, task := range tasks {
		if task.DocumentProcessingProgress == nil {
			continue
		}
		if task.DocumentProcessingProgress.Finished {
			continue
		}
		task.DocumentProcessingProgress.Finished = true
		task.DocumentProcessingProgress.Stage = pb.DocumentTaskStage_INDEXING_FAILED
		task.DocumentProcessingProgress.ErrorMessage = "server restart when task is running, please retry"
		if err := repo.Save(task); err != nil {
			stdlog.Errorf("DocImportTaskManager.Store failed: %v", err)
		} else {
			docIds = append(docIds, task.DocumentId)
		}
	}
	docRepo := m.q.Document
	_, err = docRepo.Where(docRepo.Id.In(docIds...)).UpdateSimple(docRepo.Stage.Value(int32(pb.DocumentTaskStage_INDEXING_FAILED)))
	if err != nil {
		stdlog.WithError(err).Errorf("update doc repo %+v failed.", docIds)
	}
}

type DocWithKb struct {
	task *models.DocTask
	doc  *models.Document
	kb   *models.KnowledgeBase
}
