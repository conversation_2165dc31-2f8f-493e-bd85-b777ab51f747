package knowledge_base

import (
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type DocElementStore interface {
	Load(id string) (*models.DocElement, error)
	LoadBatch(ids []string) ([]*models.DocElement, error)
	LoadByChunks(chunks []*models.Chunk) ([][]*models.DocElement, error)
	Store(*models.DocElement) error
	StoreBatch([]*models.DocElement) error
	Delete(id string) error
	DeleteBatch(ids []string) error
	DeleteByDocument(docId string) error
}

var des DocElementStore
var deso sync.Once

func GetDocElementStore() DocElementStore {
	deso.Do(func() {
		des = &docElementStore{
			Q: dao.InitQuery(),
		}
	})
	return des
}

type docElementStore struct {
	Q *query.Query
}

func (s *docElementStore) LoadBatch(ids []string) ([]*models.DocElement, error) {
	repo := s.Q.DocElement
	return repo.Where(repo.Id.In(ids...)).Find()
}

func (s *docElementStore) Load(id string) (*models.DocElement, error) {
	repo := s.Q.DocElement
	return repo.Where(repo.Id.Eq(id)).Take()
}

func (s *docElementStore) Store(c *models.DocElement) error {
	repo := s.Q.DocElement
	return repo.Create(c)
}

func (s *docElementStore) StoreBatch(data []*models.DocElement) error {
	repo := s.Q.DocElement
	return repo.CreateInBatches(data, 100)
}

func (s *docElementStore) Delete(id string) error {
	repo := s.Q.DocElement
	_, err := repo.Where(repo.Id.Eq(id)).Delete()
	return err
}
func (s *docElementStore) DeleteBatch(ids []string) error {
	repo := s.Q.DocElement
	_, err := repo.Where(repo.Id.In(ids...)).Delete()
	return err
}

func (s *docElementStore) LoadByChunks(chunks []*models.Chunk) ([][]*models.DocElement, error) {
	ids := s.fetchIdsByChunks(chunks)
	elements, err := s.LoadBatch(ids)
	if err != nil {
		return nil, err
	}

	eleMap := make(map[string]*models.DocElement, len(elements))
	for _, ele := range elements {
		eleMap[ele.Id] = ele
	}

	ret := make([][]*models.DocElement, len(chunks))
	for i, chunk := range chunks {
		ret[i] = make([]*models.DocElement, len(chunk.ElementIDs))
		for j, eid := range chunk.ElementIDs {
			ret[i][j] = eleMap[eid]
		}
	}
	return ret, nil
}

func (s *docElementStore) DeleteByDocument(docId string) (err error) {
	dr := s.Q.Document
	doc, err := dr.Where(dr.Id.Eq(docId)).First()
	if err != nil {
		return
	}
	cr := s.Q.Chunk.Table(GetChunkStore(doc.ProjectId).GetTableName())
	chunks, err := cr.Select(cr.ElementIDs).Where(cr.DocumentId.Eq(docId)).Find()
	if err != nil {
		return
	}
	ids := s.fetchIdsByChunks(chunks)
	return s.DeleteBatch(ids)
}

func (s *docElementStore) fetchIdsByChunks(chunks []*models.Chunk) []string {
	set := map[string]struct{}{}
	for _, chunk := range chunks {
		for _, eid := range chunk.ElementIDs {
			set[eid] = struct{}{}
		}
	}
	return utils.Set2Slice(set)
}
