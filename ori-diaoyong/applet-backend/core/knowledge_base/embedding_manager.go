package knowledge_base

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
)

var (
	embeddingManager *EmbeddingManager
	embmgrOnce       sync.Once
)

const (
	queueSize   = 100
	idleTimeout = 30 * time.Second
)

func GetEmbeddingManager() *EmbeddingManager {
	embmgrOnce.Do(func() {
		embeddingManager = &EmbeddingManager{
			pool: NewDynamicPool(
				conf.Config.KnowlhubConfig.EmbeddingModelMinConcurrency,
				conf.Config.KnowlhubConfig.EmbeddingModelMaxConcurrency,
				queueSize,
				idleTimeout,
			),
		}
	})
	return embeddingManager
}

// EmbeddingManager 用于全局管理队emb模型的调用
type EmbeddingManager struct {
	pool *DynamicPool // 姑且先设置一个全局的，后续根据不同emb模型开不同的协程池
}

// BatchEmbedding 会将emb任务提交到协程池，并同步等待结果返回
func (m *EmbeddingManager) BatchEmbedding(ctx context.Context, vectorModel *pb.ModelService, data []any) ([][]float32, error) {
	if len(data) == 0 {
		return [][]float32{}, nil
	}
	texts, err := utils.CvtAny2TSlice[string](data)
	if err != nil {
		return nil, stderr.Wrap(err, "EmbeddingTexts")
	}
	OpenAiTextVectorReq := &triton.OpenAiTextVectorReq{
		Input: texts,
	}
	f := func() ([][]float32, error) {
		// 添加重试
		retryCount, retryInterval := conf.Config.KnowlhubConfig.EmbeddingRetryCount, conf.Config.KnowlhubConfig.EmbeddingRetryInterval
		var lastErr error
		for retryCount > 0 {
			retryCount--
			// 添加单次emb的超时
			ctxTimeout, cancel := context.WithTimeout(ctx, conf.Config.KnowlhubConfig.EmbeddingTimeout)
			OpenAiTextVectorResp, err := clients.Embedding(ctxTimeout, vectorModel, OpenAiTextVectorReq)
			if err != nil {
				stdlog.WithError(err).Errorf("call embedding model error %d times, waiting %v seconds", conf.Config.KnowlhubConfig.EmbeddingRetryCount+1-retryCount, retryInterval.Seconds())
				lastErr = err
				cancel()
				time.Sleep(retryInterval)
				retryInterval *= 2
				continue
			}
			if OpenAiTextVectorResp == nil || len(OpenAiTextVectorResp.Data) != len(data) {
				stdlog.Errorf("EmbeddingTexts: OpenAiTextVectorResp is nil or len(OpenAiTextVectorResp.Data) != len(data)")
				stdlog.Errorf("call embedding model error %d times, waiting %v seconds", conf.Config.KnowlhubConfig.EmbeddingRetryCount+1-retryCount, retryInterval.Seconds())
				lastErr = fmt.Errorf("EmbeddingTexts: OpenAiTextVectorResp is nil or len(OpenAiTextVectorResp.Data) != len(data)")
				cancel()
				time.Sleep(retryInterval)
				retryInterval *= 2
				continue
			}
			// 将二维 float64 切片转换为二维 float32 切片
			embeddingRespFp32 := make([][]float32, len(OpenAiTextVectorResp.Data))
			for index, value := range OpenAiTextVectorResp.Data {
				embeddingRespFp32[index] = make([]float32, len(value.Embedding))
			}
			for i, value := range OpenAiTextVectorResp.Data {
				for j, embedding := range value.Embedding {
					embeddingRespFp32[i][j] = float32(embedding)
				}
			}
			cancel()
			return embeddingRespFp32, nil
		}
		// 超过重试次数还是失败
		return nil, stderr.Wrap(lastErr, "EmbeddingTexts failed")
	}
	result := m.pool.Submit(f)
	select {
	case r := <-result:
		if r.Err != nil {
			return nil, stderr.Wrap(r.Err, "EmbeddingTexts")
		}
		return r.Vectors, nil
	}
}

type Result struct {
	Vectors [][]float32
	Err     error
}

// Task 表示要执行的任务
type Task struct {
	Execute func() ([][]float32, error) // 执行函数返回任意类型的结果
	Result  chan *Result                // 用于接收结果的通道
	timeout *time.Duration              // 超时
}

// DynamicPool 是动态大小的协程池
type DynamicPool struct {
	tasks        chan *Task     // 任务队列
	wg           sync.WaitGroup // 用于等待所有工作协程完成
	mutex        sync.Mutex     // 保护并发访问
	workerCount  int            // 当前工作协程数量
	minWorkers   int            // 最小工作协程数
	maxWorkers   int            // 最大工作协程数
	idleTimeout  time.Duration  // 空闲工作协程的超时时间
	ctx          context.Context
	cancel       context.CancelFunc
	autoScale    bool // 是否启用自动伸缩
	pendingTasks int  // 等待中的任务数量
}

// NewDynamicPool 创建一个新的动态协程池
func NewDynamicPool(minWorkers, maxWorkers int, queueSize int, idleTimeout time.Duration) *DynamicPool {
	if minWorkers <= 0 {
		minWorkers = 1
	}
	if maxWorkers < minWorkers {
		maxWorkers = minWorkers
	}
	if queueSize <= 0 {
		queueSize = 100
	}

	ctx, cancel := context.WithCancel(context.Background())

	pool := &DynamicPool{
		tasks:       make(chan *Task, queueSize),
		minWorkers:  minWorkers,
		maxWorkers:  maxWorkers,
		idleTimeout: idleTimeout,
		ctx:         ctx,
		cancel:      cancel,
		autoScale:   true,
	}

	// 启动最小数量的工作协程
	for i := 0; i < minWorkers; i++ {
		pool.addWorker()
	}

	// 启动自动扩缩容管理器
	go pool.scaleManager()

	return pool
}

// Submit 提交任务并获取结果的方法
func (p *DynamicPool) Submit(execute func() ([][]float32, error)) <-chan *Result {
	resultChan := make(chan *Result, 1) // 缓冲为1，避免阻塞
	task := &Task{
		Execute: execute,
		Result:  resultChan,
	}

	// 提交任务到池中
	select {
	case p.tasks <- task:
		// 检查是否需要添加新的工作协程
		p.mutex.Lock()
		p.pendingTasks++
		p.mutex.Unlock()
		// 检查是否需要扩容
		p.tryScaleUp()
	case <-p.ctx.Done():
		close(resultChan)
		return resultChan
	}

	return resultChan
}

func (p *DynamicPool) SubmitWithTimeout(execute func() ([][]float32, error), timeout *time.Duration) <-chan *Result {
	resultChan := make(chan *Result, 1) // 缓冲为1，避免阻塞
	task := &Task{
		Execute: execute,
		Result:  resultChan,
		timeout: timeout,
	}

	// 提交任务到池中
	select {
	case p.tasks <- task:
		// 检查是否需要添加新的工作协程
		p.mutex.Lock()
		p.pendingTasks++
		if p.pendingTasks > p.workerCount && p.workerCount < p.maxWorkers {
			p.addWorker()
		}
		p.mutex.Unlock()
	case <-p.ctx.Done():
		close(resultChan)
		return resultChan
	}

	// 检查是否需要扩容
	p.tryScaleUp()

	return resultChan
}

// 添加一个工作协程
func (p *DynamicPool) addWorker() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.workerCount >= p.maxWorkers {
		return
	}

	p.workerCount++
	p.wg.Add(1)

	go func() {
		defer p.wg.Done()
		defer func() {
			p.mutex.Lock()
			p.workerCount--
			p.mutex.Unlock()
		}()

		for {
			// 等待任务或超时
			select {
			case <-p.ctx.Done():
				return // 池被关闭
			case task, ok := <-p.tasks:
				if !ok {
					return // 任务队列已关闭
				}
				// 执行任务，捕获返回值
				func() {
					defer func() {
						if r := recover(); r != nil {
							// 如果发生panic，发送nil或错误信息
							if task.Result != nil {
								var err error
								switch x := r.(type) {
								case string:
									err = errors.New("panic: " + x)
								case error:
									err = fmt.Errorf("panic: %w", x)
								default:
									err = fmt.Errorf("panic: %v", r)
								}
								task.Result <- &Result{Vectors: nil, Err: err}
								close(task.Result)
							}
						}
					}()
					// 执行任务并获取结果
					result, err := task.Execute()
					// 将结果发送到结果通道
					if task.Result != nil {
						task.Result <- &Result{Vectors: result, Err: err}
						close(task.Result)
					}
				}()
				p.mutex.Lock()
				p.pendingTasks--
				p.mutex.Unlock()

			case <-time.After(p.idleTimeout):
				// 判断是否可以安全退出
				p.mutex.Lock()
				canExit := p.workerCount > p.minWorkers
				p.mutex.Unlock()

				if canExit {
					return // 工作协程数量超过最小值，可以退出
				}
			}
		}
	}()
}

// 尝试扩容
func (p *DynamicPool) tryScaleUp() {
	if !p.autoScale {
		return
	}

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 如果等待任务数量大于工作协程数量，并且工作协程数量小于最大值，则增加工作协程
	if p.pendingTasks > p.workerCount && p.workerCount < p.maxWorkers {
		// 释放锁，然后添加工作协程
		p.mutex.Unlock()
		p.addWorker()
		p.mutex.Lock()
	}
}

// 自动扩缩容管理器
func (p *DynamicPool) scaleManager() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.adjustWorkerCount()
		}
	}
}

// 调整工作协程数量
func (p *DynamicPool) adjustWorkerCount() {
	if !p.autoScale {
		return
	}

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 这里可以根据更复杂的策略调整工作协程数量
	// 例如：任务队列长度、CPU使用率、内存使用等

	// 简单策略：如果任务少，减少工作协程；如果任务多，增加工作协程
	pendingRatio := float64(p.pendingTasks) / float64(p.workerCount)

	if pendingRatio < 0.2 && p.workerCount > p.minWorkers {
		// 任务较少，可以减少工作协程
		// 注意：我们不需要实际删除工作协程，只需要更新计数
		// 工作协程会自行在空闲超时后退出
	} else if pendingRatio > 0.8 && p.workerCount < p.maxWorkers {
		// 任务较多，增加工作协程
		p.mutex.Unlock()
		p.addWorker()
		p.mutex.Lock()
	}
}

// Stop 优雅关闭协程池
func (p *DynamicPool) Stop() {
	p.cancel() // 通知所有工作协程退出
	close(p.tasks)
	p.wg.Wait() // 等待所有工作协程完成
}

// SetAutoScale 设置是否启用自动伸缩
func (p *DynamicPool) SetAutoScale(enable bool) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.autoScale = enable
}

// Resize 手动调整协程池大小
func (p *DynamicPool) Resize(minWorkers, maxWorkers int) {
	if minWorkers <= 0 || maxWorkers < minWorkers {
		return
	}

	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.minWorkers = minWorkers
	p.maxWorkers = maxWorkers

	// 如果当前工作协程数小于新的最小值，添加更多工作协程
	for p.workerCount < p.minWorkers {
		p.mutex.Unlock()
		p.addWorker()
		p.mutex.Lock()
	}
}

// Stats 返回协程池的当前状态
func (p *DynamicPool) Stats() (workers, pendingTasks int) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	return p.workerCount, p.pendingTasks
}
