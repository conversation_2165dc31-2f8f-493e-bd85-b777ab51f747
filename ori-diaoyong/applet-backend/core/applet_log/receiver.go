package applet_log

//
//import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"strings"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
//	"transwarp.io/applied-ai/aiot/vision-std/transport/mqtt"
//	"transwarp.io/applied-ai/applet-backend/clients"
//	"transwarp.io/applied-ai/applet-backend/dao"
//	"transwarp.io/applied-ai/applet-backend/pkg/models"
//	"transwarp.io/applied-ai/applet-engine/pkg/debug"
//)
//
//// GetDebugWildcardTopic 获取调试日志通配主题
//func GetDebugWildcardTopic() string {
//	return fmt.Sprintf("app_debug/#")
//}
//
//func GetChatIDFromTopic(topic string) (string, error) {
//	splits := strings.Split(topic, "/")
//	if len(splits) < 2 {
//		return "", stderr.Internal.Error("invalid topic :%v", topic)
//	}
//	return splits[1], nil
//}
//
//func innerHandler(msg *mqtt.Msg) {
//	// script_id、task_id 、chat_id 、run_id 等价
//	chatID, err := GetChatIDFromTopic(msg.Topic)
//	if err != nil {
//		stdlog.Errorf("receive msg with valid topic: %s, %v", msg.Topic, err)
//		return
//	}
//
//	debugMessage := new(debug.DebugMessage)
//	if err := json.Unmarshal(msg.Payload, debugMessage); err != nil {
//		stdlog.Errorf("failed to Unmarshal with err :%v", err)
//		return
//	}
//
//	if debugMessage.IsEndMessage() {
//		UpsertMessageAsDebugLog(context.Background(), chatID, debugMessage)
//	}
//
//	debugMessage.Input = nil
//	debugMessage.Output = nil
//	debugMessage.Log = nil
//	debugMessage.Children = nil
//
//	logManager := ManagerFactory.Get(chatID)
//	if logManager != nil {
//		logManager.ReceiveLog(debugMessage)
//	}
//}
//
//func Subscribe(chatID string) error {
//	ManagerFactory.Register(chatID)
//	return nil
//}
//func UnSubscribe(chatID string) error {
//	ManagerFactory.Remove(chatID)
//	return nil
//}
//
//func SubscribeAll() error {
//	topic := GetDebugWildcardTopic()
//	if err := clients.MqttCli.Sub(innerHandler, topic); err != nil {
//		stdlog.WithError(err).Errorf("failed to sub topic %s", topic)
//		return err
//	}
//	return nil
//}
//
//func UpsertMessageAsDebugLog(ctx context.Context, chatID string, debugMessage *debug.DebugMessage) {
//	log := &models.ChainDebugLog{
//		ChatID:       chatID,
//		NodeID:       models.GetUUNodeID(debugMessage.Meta.SubChainID, debugMessage.Meta.NodeID),
//		Round:        debugMessage.Round,
//		DebugMessage: debugMessage,
//		ChainID:      "", // 待补充
//		ProjectID:    "", // 待补充
//	}
//	if err := dao.ChainDebugLogDAOImpl.UpsertDebugLog(ctx, log); err != nil {
//		stderr.Errorf("failed to save message: %+v as debug log with err :%v", debugMessage, err)
//	}
//}
