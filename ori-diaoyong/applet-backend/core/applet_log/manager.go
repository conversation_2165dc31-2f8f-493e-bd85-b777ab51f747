package applet_log

//
//import (
//	"context"
//	"github.com/google/uuid"
//	"sync"
//	"time"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
//	"transwarp.io/applied-ai/applet-backend/pkg/helper"
//	"transwarp.io/applied-ai/applet-engine/pkg/debug"
//)
//
//const (
//	ManagerDestroyWaitingTime = 1 * time.Minute
//)
//
//type Message = debug.DebugMessage
//
//type ConsumerID string
//
//type LogManager struct {
//	key         string
//	lock        sync.RWMutex
//	logConsumer map[ConsumerID]chan *Message
//	// 用于监控是否不再有消息传入
//	msgWatcher   chan uint8
//	cacheChannel chan *Message
//}
//
//func NewManager(key string) *LogManager {
//	m := &LogManager{key: key}
//	m.lock = sync.RWMutex{}
//	m.msgWatcher = make(chan uint8)
//	m.logConsumer = make(map[ConsumerID]chan *Message)
//	m.cacheChannel = make(chan *Message, 10000)
//	// 一段时间内收不到消息，则销毁manager
//	helper.GoFunWithRecover(func() {
//		for {
//			select {
//			case _, ok := <-m.msgWatcher:
//				if !ok {
//					stdlog.Infof("msg watcher closed")
//					return
//				}
//			case <-time.After(ManagerDestroyWaitingTime):
//				// 销毁
//				stdlog.Infof("log manager :%v destroyed", key)
//				m.Clear()
//				return
//			}
//		}
//	})
//	return m
//}
//
//func (m *LogManager) RegisterConsumer(consumerKey ConsumerID) {
//	msgChan := make(chan *Message, 1000)
//	m.lock.Lock()
//	defer m.lock.Unlock()
//	stdlog.Infof("log consumer :%v registered", consumerKey)
//	m.logConsumer[consumerKey] = msgChan
//}
//
//func (m *LogManager) ReceiveLog(msg *Message) {
//	m.msgWatcher <- 0
//	if len(m.logConsumer) == 0 {
//		stdlog.Debugf("cache msg:%v", msg)
//		m.cacheChannel <- msg
//	} else {
//		for _, c := range m.logConsumer {
//			c <- msg
//		}
//	}
//}
//
//func (m *LogManager) RegisterAndConsume(ctx context.Context, fun func(msg *Message) error) error {
//	consumerID := ConsumerID(uuid.New().String())
//	m.RegisterConsumer(consumerID)
//	return m.ConsumeLog(ctx, consumerID, fun)
//}
//
//func (m *LogManager) ConsumeLog(ctx context.Context, consumerKey ConsumerID, fun func(msg *Message) error) error {
//	ctx, ctxCancel := context.WithCancel(ctx)
//	channel, err := m.getChannel(consumerKey)
//	if err != nil {
//		return err
//	}
//	stdlog.Infof("consumer :%v begin to consume log ", consumerKey)
//	for {
//		select {
//		case <-ctx.Done():
//			stdlog.Infof("close consumer :%v", consumerKey)
//			m.CloseConsumer(consumerKey)
//			return nil
//			// 先从缓存读
//		case msg, ok := <-m.cacheChannel:
//			if !ok {
//				stdlog.Infof("cacheChannel closed")
//				return nil
//			}
//			if msg.IsChainEndMessage() {
//				ctxCancel()
//			}
//			stdlog.Debugf("consumer msg from cache :%v", msg)
//			if err := fun(msg); err != nil {
//				stdlog.Errorf("err happened while handling msg, :%v", err)
//			}
//		case msg, ok := <-channel:
//			if !ok {
//				stdlog.Infof("channel closed")
//				return nil
//			}
//			if msg.IsChainEndMessage() {
//				ctxCancel()
//			}
//			if err := fun(msg); err != nil {
//				stdlog.Errorf("err happened while handling msg, :%v", err)
//			}
//		}
//	}
//}
//
//func (m *LogManager) getChannel(consumerID ConsumerID) (chan *Message, error) {
//	m.lock.RLock()
//	defer m.lock.RUnlock()
//	channel, ok := m.logConsumer[consumerID]
//	if !ok {
//		return nil, stderr.Internal.Error("no consumer :%v", consumerID)
//	}
//	return channel, nil
//}
//
//func (m *LogManager) CloseConsumer(consumerID ConsumerID) {
//	m.lock.Lock()
//	defer m.lock.Unlock()
//	if c, ok := m.logConsumer[consumerID]; ok {
//		close(c)
//		delete(m.logConsumer, consumerID)
//	}
//}
//
//func (m *LogManager) Clear() {
//	m.lock.Lock()
//	defer m.lock.Unlock()
//	for key, c := range m.logConsumer {
//		close(c)
//		delete(m.logConsumer, key)
//	}
//	close(m.cacheChannel)
//	ManagerFactory.Remove(m.key)
//}
