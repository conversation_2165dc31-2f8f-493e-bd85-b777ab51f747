package super_agent

import (
	"testing"
)

func TestCallAgent(t *testing.T) {
	//ctx := context.Background()
	//defaultToken := "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
	//
	//ctx = helper.SetToken(ctx, defaultToken)
	////conf.TestInit()
	////
	////// init widgets
	////widgets.Init()
	////
	////// // init clients
	////clients.Init()
	////
	////// // init dao
	////dao.Init()
	////
	////// init core manager
	////if err := core.Init(ctx); err != nil {
	////	panic(err)
	////}
	//widgets.Init()
	//agent := SuperAgent{}
	//res, err := agent.GenerateChain(ctx, "帮我实现一个在wiki中搜索星环科技相关信息，并让大模型生成总结的应用链", nil)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//bytes, _ := json.Marshal(res)
	//fmt.Println(string(bytes))

}
