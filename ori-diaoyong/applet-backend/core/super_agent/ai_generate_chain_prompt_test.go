package super_agent

import (
	"encoding/json"
	"fmt"
	"testing"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func TestGetPrompt(t *testing.T) {
	res, _ := GetAIGenChainPrompt([]models.ChatHistory{})
	fmt.Println(res)
}

func TestAIChainToAppletChain(t *testing.T) {
	widgets.Init()
	str := `{
	"nodes": [{
			"id": 1,
			"widget_id": "WidgetKeyPythonWidget",
			"params": {
				"Code": "import wikipedia\\n\\ndef handler(data):\\n    \"\"\"\\n    使用维基百科搜索星环科技相关信息\\n    \"\"\"\\n    search_query = \"星环科技\"\\n    try:\\n        # 搜索星环科技相关页面\\n        search_results = wikipedia.search(search_query)\\n        if search_results:\\n            # 获取第一个结果的摘要\\n            summary = wikipedia.summary(search_results[0], sentences=5)\\n            return summary\\n        else:\\n            return \\\"未找到星环科技相关信息\\\"\\n    except Exception as e:\\n        return f\\\"发生错误: {e}\\\""
			}
		},
		{
			"id": 2,
			"widget_id": "WidgetKeyLLMModel",
			"params": {
				"SystemPrompt": "你是一个信息总结助手，我会提供一段关于星环科技的内容，请你对其进行简洁明了的总结。",
				"ModelServer": "your-model-server-endpoint"
			}
		}
	],
	"edges": [{
		"source_node_id": 1,
		"source_widget": "WidgetKeyPythonWidget",
		"source_param": "OutPut",
		"target_node_id": 2,
		"target_widget": "WidgetKeyLLMModel",
		"target_param": "Text"
	}]
}`
	aiChain := &models.AIChainDetail{}
	if err := json.Unmarshal([]byte(str), aiChain); err != nil {
		t.Fatal(err)
	}
	res, err := AIChainToAppletChain(aiChain)
	if err != nil {
		t.Fatal(err)
	}
	bytes, _ := json.Marshal(res)
	fmt.Println(string(bytes))
}

//func TestGetRecommendTemplate(t *testing.T) {
//	ctx := context.Background()
//	// init config
//	conf.TestInit()
//
//	// init widgets
//	widgets.Init()
//
//	// // init clients
//	clients.Init()
//
//	// // init dao
//	dao.Init()
//
//	// init core manager
//	if err := core.Init(ctx); err != nil {
//		panic(err)
//	}
//	res, err := GetAIRecommendChainPrompt(ctx)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(res)
//}
