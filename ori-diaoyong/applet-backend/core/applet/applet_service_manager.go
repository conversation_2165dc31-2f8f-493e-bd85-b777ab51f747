package applet

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

// InitAppletService  隔离应用链和应用服务发布时保存的配置后,并兼容历史数据
func InitAppletService() error {
	go func() {
		ticker := time.NewTicker(conf.Config.AppSvcConfig.InitInterval)
		defer ticker.Stop()

		if err := initAppletService(); err != nil {
			stdlog.Error(err)
		} else {
			return
		}
		for range ticker.C {
			if err := initAppletService(); err != nil {
				stdlog.Error(err)
			} else {
				return
			}
		}

	}()
	return nil
}

// InitAppletService  隔离应用链和应用服务发布时保存的配置后,并兼容历史数据
func initAppletService() error {
	ctx := conf.GetDefaultContext()
	// 1、查询mlops,获取所有的已部署应用
	allSvcMap, err := ChainDeployManager.ListSimpleStateAsMap(ctx)
	if err != nil {
		return stderr.Wrap(err, "init applet service")
	}

	// 2、查询本地,获取部署时已保存的应用配置
	appletSvcs, err := AppServiceManger.ListAppSvcAsChainDO(ctx)
	if err != nil {
		return stderr.Wrap(err, "init applet service")
	}
	localSvcMap := helper.CvtSlice2Map(appletSvcs, func(svc *models.AppletChainDO) string { return svc.Base.ID })

	// 3、之前部署的历史数据,在第二步查不到,需要补充一条记录
	for chainId, svc := range allSvcMap {
		if _, exist := localSvcMap[chainId]; !exist {
			chainD0, err := ChainManager.GetChainByID(ctx, chainId)
			if err != nil {
				continue
			}
			snapshotId, err := InsertSnapshot(ctx, chainD0)
			if err != nil {
				continue
			}
			if err := AppServiceManger.UpsertAppSvc(
				ctx, &models.AppletService{
					ChainID:    chainId,
					ServiceID:  svc.ServiceID,
					SnapshotID: snapshotId,
					ProjectID:  chainD0.Base.ProjectID}); err != nil {
				continue
			}
		}
	}
	return nil
}

type AppServiceMgr struct {
}

func (a *AppServiceMgr) ListAppSvcAsChainDO(ctx context.Context) ([]*models.AppletChainDO, error) {
	appletSvcs, err := dao.AppletServiceDAOImpl.ListAppletService(ctx)
	if err != nil {
		return nil, err
	}
	snapshotIds := make([]string, 0)
	for _, as := range appletSvcs {
		snapshotIds = append(snapshotIds, as.SnapshotID)
	}
	return dao.ChainSnapshotDAOImpl.ListAsChainDO(ctx, snapshotIds)
}

func (a *AppServiceMgr) GetAppSvcAsChainDO(ctx context.Context, chainId string) (*models.AppletChainDO, error) {
	appletSvc, err := dao.AppletServiceDAOImpl.GetAppletService(ctx, chainId)
	if err != nil {
		return nil, err
	}
	return dao.ChainSnapshotDAOImpl.GetAsChainDO(ctx, appletSvc.SnapshotID)
}

func (a *AppServiceMgr) UpsertAppSvc(ctx context.Context, appSvc *models.AppletService) error {
	return dao.AppletServiceDAOImpl.UpsertAppletService(ctx, appSvc)
}
