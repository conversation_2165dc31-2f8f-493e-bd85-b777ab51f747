package applet

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"os"
	"path/filepath"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

// ValidChainNameForUpdate 更新时检查名称是否重复
func ValidChainNameForUpdate(ctx context.Context, chainID string, chainName string, targetProjectID string) error {
	chains, err := dao.AppletChainDAOImpl.Query(ctx, &generated.AppletChain{
		Name:      chainName,
		ProjectID: targetProjectID,
	})
	if err != nil {
		return stderr.Wrap(err, "get chain by id err")
	}
	for _, c := range chains {
		// 这里还需要加上projectID == c.ProjectID的原因是：假如projectID为空，会查询所有project下的chain
		if c.ID != chainID && targetProjectID == c.ProjectID {
			return stderr.AppletNameDuplicatedError.Error("%v", chainName)
		}
	}
	return nil
}

// ValidChainNameForCreate 创建时检查名称重复
func ValidChainNameForCreate(ctx context.Context, chainName string, projectID string) error {
	chains, err := dao.AppletChainDAOImpl.Query(ctx, &generated.AppletChain{
		Name:      chainName,
		ProjectID: projectID,
	})
	if err != nil {
		return stderr.Wrap(err, "failed to get chain by name and project id ")
	}
	if len(chains) != 0 {
		return stderr.AppletNameDuplicatedError.Error("%v", chainName)
	}
	return nil
}

func ValidAuthAndState(ctx context.Context, chainID string,
	validStates []models.ChainDeployState) error {
	_, err := dao.AppletChainDAOImpl.GetByID(ctx, chainID)
	if err != nil {
		return stderr.Wrap(err, "get chain by id err")
	}
	return nil
}

// ValidChainDetailForUpdate 校验应用链编排信息及生成的tick是否合理
func ValidChainDetailForUpdate(ctx context.Context, chainDetail *widgets.Chain) error {
	if err := chainDetail.IsValid(); err != nil {
		return stderr.Wrap(err, "chain detail is invalid")
	}
	uuid := toolkit.NewUUID()
	chainDO := &models.AppletChainDO{
		Base:        models.AppletChainBaseDO{ID: uuid, Name: uuid},
		ChainDetail: chainDetail,
	}
	tickScript, err := GenerateTickWithChain(ctx, chainDO, ptr.String(uuid))
	if err != nil {
		return err
	}
	if err := clients.KapacitorCli.ValidTickScript(ctx, tickScript); err != nil {
		return err
	}
	return nil
}

func WriteByteToFile(path string, data []byte) error {
	if os.Getenv("LOCAL_DEBUG_MODLE") == "true" {
		return nil
	}
	// 提取目录路径
	dir := filepath.Dir(path)

	// 创建目录（如果不存在的话）
	err := os.MkdirAll(dir, os.ModePerm) // 0755 表示目录权限
	if err != nil {
		return err
	}
	err = os.WriteFile(path, data, os.ModePerm)
	if err != nil {
		return err
	}
	return nil
}
