package applet

import (
	"context"
	"encoding/json"
	"io"
	"strconv"

	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/sse"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/core/applet_log"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

type IChainDebug interface {
	DebugSucceed(ctx context.Context, chainID string, chainDebugID string) error
	DebugFailed(ctx context.Context, chainID string, chainDebugID string) error
	DebugCanceled(ctx context.Context, chainID string, chainDebugID string) error
	DebugRunning(ctx context.Context, chainID string, chainDebugID string) error
	// UpdateDebugState 更新最新的调试状态，此接口不对外直接暴露
	updateChainDebugState(ctx context.Context, chainID string, chainDebugID string, chainDebugState *models.ChainDebugState) error
	// Debug 调试应用链
	Debug(ctx context.Context, param *models.ChainDebugParam) error
	// Save 保存调试历史
	Save(ctx context.Context, eventDataStr string, chainParam *models.ChainParam) (string, error)
	// List 列出指定应用链的调试历史(基本信息)
	ListChainDebugHistory(ctx context.Context, param *dao.ChainDebugParam) (*ChainDebugHistoryRsp, error)
	GetDebugHistoryByChatID(ctx context.Context, chatID string) (*ResponseInDebugMsg, error)
	DeleteDebugHistoryByChatID(ctx context.Context, chatID string) error
	// DeleteDebugHistoriesByChainID 删除指定 ChainID 对应的所有调试历史
	DeleteDebugHistoriesByChainID(ctx context.Context, chainID string) error
	// BatchDeleteChainDebugHistories 批量删除
	BatchDeleteChainDebugHistories(ctx context.Context, chainIDs []string) error
	// CleanOrphanChainDebugHistories 删除没有主的调试历史
	CleanOrphanChainDebugHistories(ctx context.Context) error
	GetChainSnapshot(ctx context.Context, chatID string) (*models.AppletChainDO, error)
	CancelChat(ctx context.Context, chatID string, message *debug.OverallDebugMessage) error
}

//type ChainDebugParam struct {
//	chainID         string
//	chainName       string
//	ChainDetail     *widgets.Chain
//	runID           string
//	RunParam        models.WidgetParams
//	RespWriter      io.Writer
//	finishChan      chan struct{}
//	ExperienceModel bool
//}

//func (c ChainDebugParam) Done() <-chan struct{} {
//	return c.finishChan
//}
//
//func (c ChainDebugParam) finishTask() {
//	c.finishChan <- struct{}{}
//}

func NewChainDebugParam(chainID string, chainName string, chainDetail *widgets.Chain, runID string, experienceModel bool, param models.WidgetParams, rspWriter io.Writer) *models.ChainDebugParam {
	return &models.ChainDebugParam{
		ChainID:         chainID,
		ChainName:       chainName,
		ChainDetail:     chainDetail,
		RunID:           runID,
		RunParam:        param,
		RespWriter:      rspWriter,
		FinishChan:      make(chan struct{}),
		ExperienceModel: experienceModel,
	}
}

type ChainDebug struct {
}

func (c ChainDebug) Debug(ctx context.Context, param *models.ChainDebugParam) error {
	APIPath := param.RunID
	// 更新状态为运行中
	if !param.ExperienceModel {
		if err := c.DebugRunning(ctx, param.ChainID, param.RunID); err != nil {
			return err
		}
	}
	chainDO := &models.AppletChainDO{
		Base:        models.AppletChainBaseDO{ID: param.ChainID, Name: param.ChainName},
		ChainDetail: param.ChainDetail,
	}
	script, err := GenerateTickWithChain(ctx, chainDO, ptr.String(APIPath))
	if err != nil {
		return err
	}
	stdlog.Infof("gen tick script success: \n%v", script)
	id, err := clients.KapacitorCli.RegisterTickScript(ctx, param.RunID, script)
	if err != nil {
		return err
	}
	stdlog.Infof("register tick script success,id :%v", id)

	openTrace := false
	if param.ChainDetail.ContainsSubChain() {
		openTrace = true
	}
	// 加入debug参数
	queryParams := helper.GetEngineExecQueryParams(ctx, true, openTrace)

	err = clients.KapacitorCli.Execute(ctx, queryParams, ptr.String(APIPath), param, getEventHandler(ctx, param))
	// 调用成功或者失败都需要清理script
	defer helper.GoFunWithRecover(func() {
		stdlog.Infof("execute finished,closing tick script...")
		if err := clients.KapacitorCli.DisableTickScript(ctx, id); err != nil {
			stdlog.Errorf("execute finished,close tick script failed")
		} else {
			stdlog.Infof("execute finished,tick script closed")
		}
	})
	if err != nil {
		return err
	}
	param.FinishTask()
	return nil
}

func getEventHandler(ctx context.Context, param *models.ChainDebugParam) func(event stdsrv.SSEEvent) error {
	chatId := param.RunID

	// 启动协程监听上下文取消
	helper.GoFunWithRecover(func() {
		for {
			select {
			case <-ctx.Done():
				if !param.ExperienceModel {
					stdlog.Infof("chat :%v debug canceled", chatId)
					// 手动构造取消事件
					cancelEvent := stdsrv.SSEEvent{Event: stdsrv.SSEEvtCancel, Data: "debug canceled by user"}
					// 调用事件处理器处理这个事件
					_ = param.EventHandler(cancelEvent)
				}
				return
			case <-param.Done():
				stdlog.Infof("chat :%v finished", chatId)
				return
			}
		}
	})
	// 钩子函数，分情况处理Event，并把所有 Event 写入输出流
	return func(event stdsrv.SSEEvent) error {
		var err error
		switch event.Event {
		case stdsrv.SSEEvtLog:
			logMsg := new(debug.DebugMessage)
			if err := helper.String2Struct(stdsrv.AnyToString(event.Data), logMsg); err != nil {
				return stderr.Wrap(err, "invalid event data")
			}

			if logMsg.IsEndMessage() {
				applet_log.UpsertMessageAsDebugLog(ctx, chatId, logMsg)
			}
			// 循环可能导致日志消息太多，由前端去查询
			logMsg.Log, logMsg.Input, logMsg.Output = "", "", ""
			event.Data = logMsg
		case stdsrv.SSEEvtDebug:
			err = dao.ChainDebugHistoryImpl.UpdateByID(ctx, chatId,
				dao.ChainDebugParam{DebugMessage: ptr.String(stdsrv.AnyToString(event.Data))})
		case stdsrv.SSEEvtClose:
			err = ChainDebugManager.DebugSucceed(ctx, param.ChainID, chatId)
		case stdsrv.SSEEvtError:
			err = ChainDebugManager.DebugFailed(ctx, param.ChainID, chatId)
		case stdsrv.SSEEvtCancel:
			// 使用新的上下文避免原上下文已取消
			newCtx := context.Background()
			err = ChainDebugManager.DebugCanceled(newCtx, param.ChainID, chatId)
		}
		if err != nil {
			return stderr.Wrap(err, "处理%s event 时发生了错误", event.Event)
		}

		if err := sse.Encode(param.RespWriter, event); err != nil {
			return stderr.Wrap(err, "encode msg err")
		}
		return nil
	}
}

func (c ChainDebug) Save(ctx context.Context, eventDataStr string, chainParam *models.ChainParam) (string, error) {
	user, err := helper.GetUser(ctx)
	if err != nil {
		return "", err
	}
	var ID string
	qry := query.Q
	if err := qry.Transaction(func(tx *query.Query) error {

		// 先插入一条记录到 chain_snapshot 表，然后将id存在 chain_debug_histories 的 chain_snapshot_id 字段
		chainSnapshotModel := &generated.ChainSnapshot{
			ChainID:     chainParam.ChainID,
			ChainDetail: stdsrv.AnyToString(chainParam.ChainDetail),
		}
		chainSnapshotID, err := dao.NewChainSnapshotDAO(tx).Save(ctx, chainSnapshotModel)
		if err != nil {
			return err
		}

		debugHistoryModel := &generated.ChainDebugHistory{
			ChainID:         chainParam.ChainID,
			State:           models.ChainDebugStateInit.Code,
			Creator:         user,
			ProjectID:       helper.GetProjectID(ctx),
			DebugMessage:    eventDataStr,
			ChainSnapshotID: chainSnapshotID,
			DebugName:       chainParam.DebugName,
		}
		ID, err = dao.NewChainDebugHistoryDAO(tx).Save(ctx, debugHistoryModel)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		stdlog.Errorf("update chain debug state err :%v", err)
		return "", err
	}

	return ID, nil
}

func (c ChainDebug) DebugSucceed(ctx context.Context, chainID string, chainDebugID string) error {
	return c.updateChainDebugState(ctx, chainID, chainDebugID, models.ChainDebugStateSuccess)
}

func (c ChainDebug) DebugFailed(ctx context.Context, chainID string, chainDebugID string) error {
	return c.updateChainDebugState(ctx, chainID, chainDebugID, models.ChainDebugStateFailed)
}

func (c ChainDebug) DebugRunning(ctx context.Context, chainID string, chainDebugID string) error {
	return c.updateChainDebugState(ctx, chainID, chainDebugID, models.ChainDebugStateRunning)
}

func (c ChainDebug) DebugCanceled(ctx context.Context, chainID string, chainDebugID string) error {
	return c.updateChainDebugState(ctx, chainID, chainDebugID, models.ChainDebugStateCanceled)
}

func (c ChainDebug) updateChainDebugState(ctx context.Context, chainID string, chainDebugID string, chainDebugState *models.ChainDebugState) error {
	if chainDebugState == nil {
		return stderr.Internal.Error("chain debug state is nil")
	}
	// 状态校验
	chain, err := ChainManager.GetChainByID(ctx, chainID)
	// 如果链不存在，说明用户没用保存，只更新 chainDebugHistory 的状态
	if err != nil {
		if errUpdatingHis := dao.ChainDebugHistoryImpl.UpdateByID(ctx, chainDebugID,
			dao.ChainDebugParam{
				State: ptr.Int32(chainDebugState.Code),
			}); errUpdatingHis != nil {
			return errUpdatingHis
		}
		return nil
	}

	oriState := chain.Base.LastDebugState
	if oriState.Code == chainDebugState.Code {
		return nil
	}
	stateMachine := ChainStateTransfer{
		SourceState: *oriState,
		TargetState: *chainDebugState,
	}
	if err := stateMachine.IsValid(); err != nil {
		stdlog.Errorf("invalid state :%v ", stateMachine)
		return err
	}
	qry := query.Q
	if err := qry.Transaction(func(tx *query.Query) error {

		// 同步更新applet-chain调试转态
		updateModel := &generated.AppletChain{LastDebugState: chainDebugState.Code}
		_, err = dao.NewAppletChainDAO(tx).UpdateChain(ctx, chainID, updateModel)
		if err != nil {
			return err
		}

		if err := dao.NewChainDebugHistoryDAO(tx).UpdateByID(ctx, chainDebugID, dao.ChainDebugParam{
			State: ptr.Int32(chainDebugState.Code),
		}); err != nil {
			return err
		}
		return nil
	}); err != nil {
		stdlog.Errorf("update chain debug state err :%v", err)
		return err
	}

	return nil
}

type ChainStateTransfer struct {
	SourceState models.ChainDebugState
	TargetState models.ChainDebugState
}

// validStateMap 应用链调试状态机
var validStateMap map[int32][]int32

func InitChainDebugStateMachine() {
	validStateMap = make(map[int32][]int32, 0)
	// running状态可以由init、failed、success,canceled转为
	validStateMap[models.ChainDebugStateRunning.Code] = []int32{
		models.ChainDebugStateInit.Code, models.ChainDebugStateFailed.Code,
		models.ChainDebugStateSuccess.Code, models.ChainDebugStateCanceled.Code,
	}
	validStateMap[models.ChainDebugStateFailed.Code] = []int32{
		models.ChainDebugStateRunning.Code,
	}
	validStateMap[models.ChainDebugStateSuccess.Code] = []int32{
		models.ChainDebugStateRunning.Code,
	}
	validStateMap[models.ChainDebugStateCanceled.Code] = []int32{
		models.ChainDebugStateRunning.Code,
		models.ChainDebugStateInit.Code,
	}

}

// IsValid 应用链状态是否合法
func (c ChainStateTransfer) IsValid() error {
	validStates, ok := validStateMap[c.TargetState.Code]
	if !ok {
		return helper.AppletChainDebugStateErr.Error("state :%v can't transfer to :%v", c.SourceState, c.TargetState)
	}
	for _, s := range validStates {
		if s == c.SourceState.Code {
			return nil
		}
	}
	return helper.AppletChainDebugStateErr.Error("state :%v can't transfer to :%v", c.SourceState, c.TargetState)
}

type ChainDebugHistory struct {
	ChatId          string `json:"chat_id"`           //run_id，调试id
	DebugName       string `json:"name"`              //调试名称
	DebugTime       int64  `json:"debug_time"`        //调试时间
	DebugState      string `json:"debug_state"`       //调试状态
	ChainSnapshotId int64  `json:"chain_snapshot_id"` //chain_snapshot主键，用于复现应用链
}

type ChainDebugHistoryRsp struct {
	Total       int                  `json:"total"`       // 总记录数
	HistoryList []*ChainDebugHistory `json:"historyList"` // 数据列表
}

func (c ChainDebug) ListChainDebugHistory(ctx context.Context, param *dao.ChainDebugParam) (*ChainDebugHistoryRsp, error) {
	// 筛选出符合条件的 ChainDebugHistory，并按时间顺序排序，写入[]ChainDebugHistoryDesc
	historyRecords, count, err := dao.ChainDebugHistoryImpl.List(ctx, *param)
	if err != nil {
		return nil, stderr.Wrap(err, "无法获取调试历史记录")
	}

	historyList := make([]*ChainDebugHistory, 0)

	for _, historyRecord := range historyRecords {
		debugState, err := models.ConvertChainDebugStatePO2DO(historyRecord.State)
		if err != nil {
			return nil, err
		}
		historyEntity := &ChainDebugHistory{
			ChatId:          historyRecord.ID,
			DebugName:       historyRecord.DebugName,
			DebugTime:       historyRecord.CreateTime.UnixMilli(),
			DebugState:      debugState.State,
			ChainSnapshotId: historyRecord.ChainSnapshotID,
		}
		historyList = append(historyList, historyEntity)
	}

	chainDebugHistoryRsp := &ChainDebugHistoryRsp{
		Total:       int(count),
		HistoryList: historyList,
	}

	return chainDebugHistoryRsp, nil
}

type ResponseInDebugMsg struct {
	debug.OverallDebugMessage
	ChainSnapshotID int64  `json:"chain_snapshot_id"`
	Creator         string `json:"creator"`
	DebugName       string `json:"debug_name"`
}

// ChainDebugHistory.DebugMessage 中有一个 Response 字段，需要反序列化读取 ChainDebugHistory.DebugMessage 后提取 Response
type DebugMessage struct {
	Response ResponseInDebugMsg `json:"response"`
}

func (c ChainDebug) GetDebugHistoryByChatID(ctx context.Context, chatID string) (*ResponseInDebugMsg, error) {
	chainDebugHistory, err := dao.ChainDebugHistoryImpl.GetByID(ctx, chatID)
	if err != nil {
		return nil, stderr.Wrap(err, "无法获取调试历史记录")
	}

	var root DebugMessage
	if chainDebugHistory.DebugMessage == "" {
		// r.GET("/chains/{id}/history" 筛选过了，前端拿到的历史记录列表都是有 “debug_message” 字段的记录，按理说不会进入到这里
		return nil, stderr.Wrap(err, "debugMessage 为空，无法获取调试历史记录")
	}

	if err := json.Unmarshal([]byte(chainDebugHistory.DebugMessage), &root); err != nil {
		return nil, stderr.Wrap(err, "调试历史记录 的 DebugMessage反序列化失败")
	}

	root.Response.ChainSnapshotID = chainDebugHistory.ChainSnapshotID
	root.Response.Creator = chainDebugHistory.Creator
	root.Response.DebugName = chainDebugHistory.DebugName

	return &root.Response, nil
}

func (c ChainDebug) DeleteDebugHistoryByChatID(ctx context.Context, chatID string) error {
	if err := dao.ChainDebugHistoryImpl.DeleteByID(ctx, chatID); err != nil {
		return stderr.Wrap(err, "无法获取调试历史记录")
	}
	return nil
}

func (c ChainDebug) DeleteDebugHistoriesByChainID(ctx context.Context, chainID string) error {
	if err := dao.ChainDebugHistoryImpl.DeleteByChainID(ctx, chainID); err != nil {
		return stderr.Wrap(err, "删除链调试历史失败，chainID: %v", chainID)
	}
	return nil
}

func (c ChainDebug) BatchDeleteChainDebugHistories(ctx context.Context, chainIDs []string) error {
	if len(chainIDs) == 0 {
		return nil
	}

	if err := dao.ChainDebugHistoryImpl.BatchDelete(ctx, chainIDs); err != nil {
		return stderr.Wrap(err, "批量删除链调试历史失败，chainIDs: %v", chainIDs)
	}

	return nil
}

func (c ChainDebug) CleanOrphanChainDebugHistories(ctx context.Context) error {
	if err := dao.ChainDebugHistoryImpl.CleanOrphanDebugsOlderThan(ctx, 24); err != nil {
		stdlog.WithError(err).Error("clean orphan debugs failed")
		return err
	}

	return nil
}

func (c ChainDebug) GetChainSnapshot(ctx context.Context, chatID string) (*models.AppletChainDO, error) {
	qry := query.Q
	chatIDint64, err := strconv.ParseInt(chatID, 10, 64)
	if err != nil {
		return nil, stderr.Wrap(err, "chatID 从 string 到 int64 转换失败")
	}

	chainSnapshot, err := dao.NewChainSnapshotDAO(qry).GetByID(ctx, chatIDint64)
	if err != nil {
		return nil, stderr.Wrap(err, "DAO获取 chainSnapshot 失败")
	}

	retBase := &models.AppletChainBaseDO{}
	if chainSnapshot.Base != "" {
		if err := json.Unmarshal([]byte(chainSnapshot.Base), &retBase); err != nil {
			return nil, stderr.Wrap(err, "反序列化 chainSnapshot.Base 部分失败")
		}
	}

	retChainDetail := &widgets.Chain{}
	if err := json.Unmarshal([]byte(chainSnapshot.ChainDetail), &retChainDetail); err != nil {
		return nil, stderr.Wrap(err, "反序列化 chainSnapshot.ChainDetail 部分失败")
	}

	chainDetail := &models.AppletChainDO{
		Base:        *retBase,
		ChainDetail: retChainDetail,
	}

	return chainDetail, nil
}

func (c ChainDebug) CancelChat(ctx context.Context, chatID string, message *debug.OverallDebugMessage) error {
	var response stdsrv.Response
	response.Response = &message

	rspBytes, err := json.Marshal(response)
	if err != nil {
		return err
	}

	rspStr := string(rspBytes)
	rspStrPtr := ptr.String(rspStr)

	if err := dao.ChainDebugHistoryImpl.UpdateByID(ctx, chatID, dao.ChainDebugParam{
		DebugMessage: rspStrPtr,
	}); err != nil {
		return err
	}

	return nil
}
