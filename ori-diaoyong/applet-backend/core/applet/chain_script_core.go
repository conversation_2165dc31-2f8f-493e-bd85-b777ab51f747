package applet

import (
	"fmt"
	"math"
	"sort"
	"strings"

	"github.com/aws/smithy-go/ptr"
	"github.com/influxdata/kapacitor/tick"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

func (s ScriptGenerator) GenScript() (string, error) {
	lastNodes, err := s.nodes.getOutputNode()
	if err != nil {
		stdlog.Errorf("gen script : get output node err :%v", err)
		return "", err
	}
	scriptLineMap := make(map[string]ScriptLine)
	for _, lastNode := range lastNodes {
		if err := s.Gen(lastNode, 1, scriptLineMap); err != nil {
			stdlog.Errorf("gen script err :%v", err)
			return "", err
		}
	}

	scriptLines := make([]ScriptLine, 0)
	for _, v := range scriptLineMap {
		scriptLines = append(scriptLines, v)
	}
	// 加入第一个节点
	port := conf.Config.Kapacitor.APIPort
	httpNode := script.ListenHttp{
		Path:     conf.Config.Kapacitor.APIPath,
		Port:     int64(port),
		Timeout:  300,
		NoPretty: false,
	}
	if s.APIPath != nil && *s.APIPath != "" {
		httpNode.Path = fmt.Sprintf("%s/%s", conf.Config.Kapacitor.APIPath, ptr.ToString(s.APIPath))
	}
	httpNodeScript, err := script.GenScript(httpNode)
	if err != nil {
		return "", err
	}
	scriptLines = append(scriptLines, ScriptLine{
		Str: fmt.Sprintf("var var_http_input=stream|%s", httpNodeScript),
		Idx: math.MaxInt64,
	})
	// 排序
	sort.Slice(scriptLines, func(i, j int) bool {
		if scriptLines[i].Idx >= scriptLines[j].Idx {
			return true
		}
		return false
	})
	// 拼接script
	tickBuilder := strings.Builder{}
	for _, s := range scriptLines {
		tickBuilder.WriteString(s.Str)
		tickBuilder.WriteString("\n")
	}
	oriStr := tickBuilder.String()
	tickScript, err := tick.Format(oriStr)
	if err != nil {
		return "", stderr.Wrap(err, "构建应用链脚本失败")
	}
	return tickScript, nil
}

// ScriptLine 用户生成最终脚本的中间结构
type ScriptLine struct {
	Str string
	// 用于排序
	Idx int64
}

func getVarParam(ID string) string {
	var res string
	if strings.HasPrefix(ID, "subchain") {
		res = fmt.Sprintf("subchain_%s_%s", helper.GetUUIDTime(ID[9:17]), helper.GetUUIDTime(ID[46:54]))
	} else {
		res = helper.GetUUIDTime(ID)
	}
	return res
}

func genNodeScriptForOutPut(node widgets.IWidget, meta widgets.NodeMeta, values map[string]interface{}) (string, error) {
	scriptGenParam, err := node.Script(meta, values)
	if err != nil {
		return "", stderr.Wrap(err, "generate script failed for node with id: %s, name: %s", node.Define().Id, node.Define().Name)
	}
	scriptStr, err := script.GenScript(scriptGenParam)
	if err != nil {
		return "", stderr.Wrap(err, "gen script err")
	}
	return scriptStr, nil
}

func (s ScriptGenerator) Gen(node *ScriptNode, currIdx int64, scriptLineMap map[string]ScriptLine) error {
	if node.NodeInfo.WidgetDetail == nil {
		return stderr.Internal.Error("gen node err,invalid chain , widget :%v no detail", node.NodeInfo.WidgetId)
	}
	builder := strings.Builder{}
	scriptLine, ok := scriptLineMap[node.NodeID]
	// 已经访问过，更新索引
	if ok {
		if scriptLine.Idx > currIdx {
			currIdx = scriptLine.Idx
		}
		scriptLineMap[node.NodeID] = ScriptLine{
			Str: scriptLine.Str,
			Idx: currIdx,
		}
	} else {
		realValues, err := node.NodeInfo.GetValuesWithDefault()
		if err != nil {
			return err
		}
		// 上游节点信息
		if err := node.setConnectNodeInfoToNodeValue(realValues); err != nil {
			return stderr.Wrap(err, "ser pre node info to node value err")
		}
		nodeMeta := widgets.NodeMeta{
			ID:        node.NodeID,
			NodeID:    node.NodeID,
			NodeName:  node.NodeInfo.Name,
			WidgetKey: node.WidgetDefine.Define().Id,
			ChainName: s.chainName,
			Context:   s.Context,
		}
		if node.SubChainBaseInfo != nil {
			// 若含有子链，nodeID格式为 subchain_${子链所属算子ID}_{子链中算子的原始ID}
			subChainParentNodeID, err := models.GetParentIDFromSubID(node.NodeID)
			if err != nil {
				return err
			}
			subChainOriNodeID, err := models.GetOriIDFromSubID(node.NodeID)
			if err != nil {
				return err
			}
			nodeMeta.NodeID = subChainOriNodeID
			nodeMeta.SubChainName = node.SubChainBaseInfo.SubChainName
			nodeMeta.SubChainID = subChainParentNodeID
		}
		preNodes := node.PreNodes
		scriptStr, err := genNodeScriptForOutPut(node.WidgetDefine, nodeMeta, realValues)
		if err != nil {
			return stderr.Wrap(err, "gen script err")
		}
		if len(preNodes) == 0 {
			// 输入节点
			builder.WriteString(fmt.Sprintf("var var_%s=", getVarParam(node.NodeID)))
			builder.WriteString(fmt.Sprintf("var_http_input|%s", scriptStr))
		} else if len(preNodes) > 1 && node.NodeInfo.WidgetDetail.Id != widgets.WidgetKeyUnion {
			// 聚合输入
			builder.WriteString(fmt.Sprintf("var var_%s = ", getVarParam(node.NodeID)))
			joinParams := make([]*script.NodeJoinParam, 0)
			for _, inputInfo := range node.InputInfos {
				joinParams = append(joinParams, &script.NodeJoinParam{
					NodeID:    inputInfo.PreNodeVar,
					NodeAlias: inputInfo.CurrentNodeInputParam,
				})
			}
			var nodeMeta *script.BaseScript
			if preNodes[0].SubChainBaseInfo != nil {
				nodeMeta = &script.BaseScript{
					SubChainID:   preNodes[0].SubChainBaseInfo.SubChainWidgetID,
					SubChainName: preNodes[0].SubChainBaseInfo.SubChainName,
				}
			}
			var isCacheEnabled bool
			if node.NodeInfo.WidgetDetail.Id == widgets.WidgetKeyInputAggregation {
				isCacheEnabled = widgets.GetCacheEnabled(realValues)
			}
			scStr, err := script.GenJoinScript(joinParams, nodeMeta, isCacheEnabled)
			if err != nil {
				return err
			}
			builder.WriteString(scStr)
			builder.WriteString(fmt.Sprintf("|%s", scriptStr))

		} else {
			preNode := preNodes[0]
			builder.WriteString(fmt.Sprintf("var var_%s=", getVarParam(node.NodeID)))
			builder.WriteString(fmt.Sprintf("var_%s|%s", getVarParam(preNode.NodeID), scriptStr))
		}

		// 最后一个节点
		if len(node.NextNodes) == 0 {
			builder.WriteString("|httpRsp()")
		}
		scriptLineMap[node.NodeID] = ScriptLine{
			Str: builder.String(),
			Idx: currIdx,
		}
	}
	idx := currIdx + 1
	for _, n := range node.PreNodes {
		if err := s.Gen(n, idx, scriptLineMap); err != nil {
			return err
		}
	}
	return nil
}
