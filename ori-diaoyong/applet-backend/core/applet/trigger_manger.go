package applet

import (
	"context"
	"fmt"
	"github.com/robfig/cron/v3"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdctn"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	RedisKeyTriggerTopic      = "APPLET_BACKEND_Trigger_Topic"
	RedisKeyTriggerLockPrefix = "APPLET_BACKEND_Trigger_Lock_Prefix"
)

var (
	defaultCtx             context.Context
	triggerManagerInstance *TriggerManager
)

// TriggerManager 负责状态管理和调度逻辑
type TriggerManager struct {
	// 传递调度事件
	eventChan chan *models.CronEvent

	// 记录每个触发器对应的任务信息  trigger_id -> {time_zone,entry_id}
	entryMap *stdctn.ConcurrentMap[string, *models.EntryInfo]

	// 不同时区使用不同实例进行定时调度  time_zone -> *cron.Cron
	cronExecutors *stdctn.ConcurrentMap[models.TimeZone, *cron.Cron]
}

func InitTriggerManger() error {
	defaultCtx = conf.GetDefaultContext()
	tm, err := newTriggerManager()
	if err != nil {
		return stderr.Errorf("failed to initialize TriggerManager: %v", err)
	}
	triggerManagerInstance = tm
	return nil
}
func GetTriggerManager() *TriggerManager {
	return triggerManagerInstance
}

func newTriggerManager() (*TriggerManager, error) {
	tm := &TriggerManager{
		eventChan:     make(chan *models.CronEvent, 100),
		entryMap:      stdctn.NewConcurrentMap[string, *models.EntryInfo](),
		cronExecutors: stdctn.NewConcurrentMap[models.TimeZone, *cron.Cron](),
	}

	tm.initTriggers()
	// 监听Redis并更新对应的triggers
	go tm.subAndUpdateTrigger()
	go tm.start()
	return tm, nil
}

// initTriggers 从数据库加载初始触发器并进行注册

func (tm *TriggerManager) initTriggers() {
	triggerInfos, err := tm.loadAllTriggers()
	if err != nil {
		stdlog.Errorf("fail to load all triggers while init triggers")
		return
	}
	for _, trigger := range triggerInfos {
		if trigger.TriggerType != models.TimeBased {
			continue
		}
		if err := tm.applyTrigger(trigger); err != nil {
			stdlog.Errorf("fail to apply trigger with [%s]", stdsrv.AnyToString(trigger))
			return
		}
	}
}

// applyTrigger 应用触发器,如果该触发器对应的任务已存在,则删除旧任务并重新注册
func (tm *TriggerManager) applyTrigger(trigger *models.TriggerInfo) error {
	if trigger.TriggerType != models.TimeBased {
		stderr.Errorf("only %s trigger support apply for cron", models.TimeBased)
	}

	// 1、去除旧任务
	if entryInfo, ok := tm.entryMap.Get(trigger.Id); ok {
		oldCron, err := tm.getCron(entryInfo.TimeZone)
		if err != nil {
			return err
		}
		oldCron.Remove(entryInfo.EntryId)
	}

	// 2、注册新任务
	curCron, err := tm.getCron(trigger.TimeZone)
	if err != nil {
		return err
	}
	schedule, err := cron.ParseStandard(trigger.CronExpression)
	if err != nil {
		return stderr.Wrap(err, "failed to parse cron: %s", trigger.CronExpression)
	}
	var entryID cron.EntryID
	job := cron.FuncJob(func() {
		entry := curCron.Entry(entryID)
		tm.eventChan <- &models.CronEvent{
			TriggerInfo:   trigger,
			ScheduledTime: entry.Prev,
		}
	})
	entryID = curCron.Schedule(schedule, job)

	// 3、记录trigger与任务之间的关系
	tm.entryMap.Set(trigger.Id, &models.EntryInfo{TimeZone: trigger.TimeZone, EntryId: entryID})
	return nil
}

func (tm *TriggerManager) getCron(timeZone models.TimeZone) (*cron.Cron, error) {
	ret, ok := tm.cronExecutors.Get(timeZone)
	if !ok {
		loc, err := timeZone.Location()
		if err != nil {
			return nil, stderr.Errorf("failed to load location for timezone %s: %w", timeZone, err)
		}
		ret = cron.New(
			cron.WithLocation(loc),
			cron.WithChain(cron.Recover(cron.DefaultLogger)),
		)
		ret.Start()
		tm.cronExecutors.Set(timeZone, ret)
	}
	return ret, nil
}

// UpdateTriggersById 更新关于某个chainId的触发器,通过发布订阅机制,更新所有pod上相关的定时任务
func (tm *TriggerManager) UpdateTriggersById(chainId string) error {
	return clients.GetRedisRDB().Publish(defaultCtx, RedisKeyTriggerTopic, chainId).Err()
}

// UpdateTriggersById 更新关于某个chainId的触发器,通过发布订阅机制,更新所有pod上相关的定时任务

// updateTriggers 监听redis消息,以便更新相关的触发器
func (tm *TriggerManager) subAndUpdateTrigger() {
	pubsub := clients.GetRedisRDB().Subscribe(defaultCtx, RedisKeyTriggerTopic)
	for {
		msg, err := pubsub.ReceiveMessage(defaultCtx)
		if err != nil {
			stdlog.Errorf("Redis pubsub error: %v", err)
			continue
		}
		chainId := msg.Payload
		tm.updateTriggersById(chainId)
	}
}

func (tm *TriggerManager) updateTriggersById(chainId string) error {
	newTriggers, err := tm.loadTriggersById(chainId)
	if err != nil {
		return stderr.Wrap(err, "failed to load triggers for chain id %s", chainId)
	}
	for _, trigger := range newTriggers {
		if trigger.TriggerType != models.TimeBased {
			continue
		}
		if err := tm.applyTrigger(trigger); err != nil {
			return stderr.Wrap(err, "apply trigger")
		}
	}
	return nil
}

// loadAllTriggers 读取数据库中的所有triggers
func (tm *TriggerManager) loadAllTriggers() ([]*models.TriggerInfo, error) {
	svcs, err := dao.AppletServiceDAOImpl.ListAllAppletService(defaultCtx)
	if err != nil {
		return nil, err
	}
	return constructTriggerInfos(svcs)
}

func (tm *TriggerManager) loadTriggersById(chainId string) ([]*models.TriggerInfo, error) {
	svc, err := dao.AppletServiceDAOImpl.GetAppletService(defaultCtx, chainId)
	if err != nil {
		return nil, err
	}
	return constructTriggerInfos([]*models.AppletService{svc})
}

func constructTriggerInfos(svcs []*models.AppletService) ([]*models.TriggerInfo, error) {
	temp := make(map[string]*models.AppletService)
	snapshotIDs := make([]string, 0)
	for _, svc := range svcs {
		temp[svc.SnapshotID] = svc
		snapshotIDs = append(snapshotIDs, svc.SnapshotID)
	}
	triggersMap, err := dao.ChainSnapshotDAOImpl.GetTriggersAsMap(defaultCtx, snapshotIDs)
	if err != nil {
		return nil, err
	}
	ret := make([]*models.TriggerInfo, 0)
	for snapshotId, triggers := range triggersMap {
		svc, exist := temp[snapshotId]
		if !exist {
			return nil, err
		}
		for _, t := range triggers {
			ret = append(ret, &models.TriggerInfo{
				Trigger:   *t,
				ChainId:   svc.ChainID,
				ServiceId: svc.ServiceID,
			})
		}
	}
	return ret, nil
}

// ManualTrigger 手动触发
func (tm *TriggerManager) ManualTrigger(triggerReq *models.TriggerReq) error {
	triggers, err := tm.loadTriggersById(triggerReq.ChainId)
	if err != nil {
		return err
	}
	for _, t := range triggers {
		if t.Trigger.Id == triggerReq.TriggerId {
			if t.TriggerType != models.EventBased {
				return stderr.Errorf("only %s trigger support manual execution", models.EventBased)
			}
			tm.eventChan <- &models.CronEvent{
				TriggerInfo:   t,
				ScheduledTime: time.Now(),
			}
			return nil
		}
	}
	return stderr.Errorf("can not find trigger with chain_id[%s] and trigger_id[%s]", triggerReq.ChainId, triggerReq.TriggerId)
}

// start 消费event,去调用触发器所指定的服务
func (tm *TriggerManager) start() {
	tm.cronExecutors.Range(func(tz models.TimeZone, cron *cron.Cron) {
		cron.Start() // 启用定时任务,开始往channel中推送事件
	})
	for event := range tm.eventChan {
		triggerInfo := event.TriggerInfo
		redisKey := fmt.Sprintf("%s_%s_%s_%d", RedisKeyTriggerLockPrefix, triggerInfo.ChainId, triggerInfo.Id, event.ScheduledTime.UnixNano())
		ok, err := clients.GetRedisCli().SetNx(redisKey, "0", 0)
		if err != nil || !ok {
			continue
		}
		go func() {
			req, err := triggerInfo.MultimodalExample.Cvt2SvcCallReq()
			if err != nil {
				stdlog.Infof("failed to get call req for cron event[%v] with error[%v]", event, err)
				return
			}
			resp, err := clients.AppletSvcCli.CallAppletSvc(defaultCtx, triggerInfo.ServiceId, req)
			stdlog.Infof("trigger applet svc for cron event[%v], with resp[%v] and error[%v]", event, resp, err)
		}()
	}
}
