package applet

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"sort"
	"strings"
	"testing"
	"time"

	"gopkg.in/yaml.v3"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const English = "英文"

// 应用链国际化策略：
// 遍历所有算子，提取所有 widgetGroup，widget，params 的中文描述，写入 etc/WidgetGroupsInfoZh.yaml，
// 将 etc/WidgetGroupsInfoZh.yaml 翻译出一份英文的yaml（可以用模型，也可以自己翻译）
// 读取英文的yaml，动态地将英文的描述映射到 []*models.WidgetGroup 中，返回给前端
// 例子可以参考 applet/widget.go 中的 ListWidgetGroups()
func TestWidgetGroupExtract(t *testing.T) {
	widgets.Init()
	widgetMap := make(map[string][]*widgets.Widget)
	widgets.WidgetFactoryImpl.Range(func(w widgets.IWidget) {
		if ws, ok := widgetMap[w.Define().Group]; ok {
			widgetMap[w.Define().Group] = append(ws, w.Define())
		} else {
			widgetMap[w.Define().Group] = []*widgets.Widget{w.Define()}
		}
	})

	rawWgs := WidgetGroupsMapInYaml{}
	enWgs := WidgetGroupsMapInYaml{}

	// 以 WidgetGroupId 为 key，WidgetGroupInYaml（Widgets字段保存组内所有算子信息）为value，生成 widgetGroupsMapInYaml
	// 用 yaml.Marshal(widgetGroupsMapInYaml) 序列化，写入 etc/WidgetGroupsInfoZh.yaml
	for groupKey, ws := range widgetMap {
		// ws排序
		sort.Slice(ws, func(i, j int) bool {
			if ws[i].Id < ws[j].Id {
				return true
			}
			return false
		})

		wsToBeTranslated := []WidgetInYaml{}
		for _, w := range ws {
			widgetParams := []ParamInYaml{}
			for _, p := range w.Params {
				widgetParams = append(widgetParams, ParamInYaml{
					Name:       p.Define.Name,
					Desc:       p.Define.Desc,
					DataSource: p.Define.Datasource,
				})
			}

			wsToBeTranslated = append(wsToBeTranslated, WidgetInYaml{
				Name:   w.Name,
				Desc:   w.Desc,
				Params: widgetParams,
			})
		}

		desc, ok := widgets.WidgetGroupDefines[groupKey]
		if !ok {
			return
		}

		wg := WidgetGroupInYaml{
			Name:    desc.Name,
			Desc:    desc.Desc,
			Widgets: wsToBeTranslated,
		}
		rawWgs[desc.ID] = wg
		enWgs[desc.ID] = wg.TranslateEn()
	}

	saveAsYaml("../../etc/WidgetGroupsInfoZh.yaml", rawWgs)
	saveAsYaml("../../etc/WidgetGroupsInfoEn.yaml", enWgs)
}

// 当 capitalizedEveryFirstChar 为 true 时，"，注意每个单词的首字母要大写"会被 fmt.Sprintf 拼接进提示词
var capitalizedEveryFirstChar bool

func (wg WidgetGroupInYaml) TranslateEn() (ewg WidgetGroupInYaml) {
	ewg = WidgetGroupInYaml{}
	capitalizedEveryFirstChar = true
	ewg.Name = MustTransEnglish(wg.Name)
	capitalizedEveryFirstChar = false
	ewg.Desc = MustTransEnglish(wg.Desc)
	ewg.Widgets = make([]WidgetInYaml, len(wg.Widgets))
	for i, w := range wg.Widgets {
		ewg.Widgets[i] = w.TranslateEn()
	}
	return
}

func (w WidgetInYaml) TranslateEn() (ew WidgetInYaml) {
	ew = WidgetInYaml{}
	capitalizedEveryFirstChar = true
	ew.Name = MustTransEnglish(w.Name)
	capitalizedEveryFirstChar = false
	ew.Desc = MustTransEnglish(w.Desc)
	ew.Params = make([]ParamInYaml, len(w.Params))
	for i, p := range w.Params {
		ew.Params[i] = ParamInYaml{
			Name:       MustTransEnglish(p.Name),
			Desc:       MustTransEnglish(p.Desc),
			DataSource: MustTransEnglish(p.DataSource),
		}
	}
	return ew
}

func MustTransEnglish(text string) string {
	if strings.TrimSpace(text) == "" {
		stdlog.Warnf("skip empty string translating")
		return ""
	}
	res, err := Translate(text, English)
	if err != nil {
		stdlog.WithError(err).Errorf("tans %s", text)
		panic(err)
	}
	fmt.Printf("原文:%s \n译文:%s\n", text, res)
	return res
}

func saveAsYaml(filePath string, content any) error {
	yamlData, err := yaml.Marshal(content)
	if err != nil {
		fmt.Println("Error marshalling to YAML:", err)
		return err
	}

	// 打开文件（不存在则创建，存在则覆盖）
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create or open file: %w", err)
	}
	defer file.Close()

	// 写入内容
	_, err = file.Write(yamlData)
	if err != nil {
		return fmt.Errorf("failed to write content: %w", err)
	}

	fmt.Printf("YAML data successfully written to %s\n", filePath)
	return nil
}

// TranslationRequest represents the request payload for the translation API
type TranslationRequest struct {
	Messages []Message `json:"messages"`
	Model    string    `json:"model"`
	Stream   bool      `json:"stream"`
}

// Message represents a single message in the request payload
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// TranslationResponse represents the response payload from the translation API
type TranslationResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int      `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice represents a single choice in the response payload
type Choice struct {
	Index        int         `json:"index"`
	Message      Message     `json:"message"`
	Logprobs     interface{} `json:"logprobs"`
	FinishReason string      `json:"finish_reason"`
	StopReason   interface{} `json:"stop_reason"`
}

// Translate function sends a translation request to the API and returns the translated text
func Translate(text, targetLanguage string) (string, error) {
	// Define the API endpoint
	url := "http://172.17.120.11:8011/openai/v1/chat/completions"

	// Create the request payload
	request := TranslationRequest{
		Messages: []Message{
			{
				Role:    "system",
				Content: "你是一个很有用的翻译助手, 能够快速根据需求完成用户给定的翻译任务, 输出时仅保留翻译结果即可,不要有额外的解释.",
			},
			{
				Role: "user",
				//Content: fmt.Sprintf("请将以下文本翻译成%s: %s", targetLanguage, text),
				Content: fmt.Sprintf(
					"请将《》中的文本翻译成%s%s：《%s》，注意输出不要带有《》符号",
					targetLanguage,
					func() string {
						if capitalizedEveryFirstChar && targetLanguage == English {
							return "，注意每个单词的首字母要大写"
						}
						return ""
					}(),
					text,
				),
			},
		},
		Model:  "atom",
		Stream: false,
	}

	// Marshal the request payload to JSON
	requestJSON, err := json.Marshal(request)
	if err != nil {
		return "", err
	}

	// Create a new HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return "", err
	}

	//theUrl, err := findProxy(req)
	// Set the request headers
	req.Header.Set("Content-Type", "application/json")

	// Send the HTTP request
	//client := &http.Client{}

	// 设置自定义 HTTP 客户端
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: nil,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// Unmarshal the response body to a struct
	var response TranslationResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}

	// Extract the translated text from the response
	if len(response.Choices) > 0 {
		res := response.Choices[0].Message.Content
		return res, nil
	}

	return "", fmt.Errorf("no translation found in the response")
}

func test() {
	// Example usage of the Translate function
	text := "Hello, how are you?"
	targetLanguage := "中文"

	translatedText, err := Translate(text, targetLanguage)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Translated Text: %s\n", translatedText)
}

func testExample(t *testing.T) {
	// Example usage of the Translate function
	text := "分段成 []pb.Chunk 的策略，暂时只支持basic"
	targetLanguage := English

	//isWidgetName = true
	translatedText, err := Translate(text, targetLanguage)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Translated Text: %s\n", translatedText)
}
