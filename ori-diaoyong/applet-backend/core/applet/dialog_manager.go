package applet

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"github.com/go-faster/errors"
	"github.com/gocarina/gocsv"
	"io"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type Dialog interface {
	// BatchCreate 同时创建answer 和 question
	BatchCreate(ctx context.Context, param *models.ChainRunReq, buffer *bytes.Buffer) error
	// GetAppChat  获取单个对话的所有记录
	GetAppChat(ctx context.Context, param *pb.MessageReq) (*pb.MessageRes, error)
	// DeleteSingleDialog 删除会话
	DeleteSingleDialog(ctx context.Context, param *pb.MessageReq) (*string, error)
	// GetAppChats 获取最新数据的列表
	GetAppChats(ctx context.Context, dialogReq *pb.DialogReq) (*pb.DialogRes, error)
	// GetDialogApps 获取所有应用
	GetDialogApps(ctx context.Context) (*pb.DialogAppRes, error)
	// SaveDialogApp 添加对话里单个应用
	SaveDialogApp(ctx context.Context, chainId string) (*generated.DialogApp, error)
	// DeleteDialogApp 删除对话里单个应用
	DeleteDialogApp(ctx context.Context, id string) (int64, error)
	// DownloadDialogs 下载会话内的所有内容
	DownloadDialogs(ctx context.Context, req *pb.DownloadDialogReq) (*pb.DownloadDialogRes, error)
	// GetDialogByChatId 获取单个对话信息
	GetDialogByChatId(ctx context.Context, appId, chatId string) (*generated.Dialog, error)
}

const (
	DateFormat = "2006-01-02"
	ZIP        = ".zip"
	CSV        = ".csv"
)

type DialogImpl struct {
}

type MessagesAllCsv struct {
	Answer   string `csv:"Answer"`
	Question string `csv:"Question"`
}

type MessagesAnswerCsv struct {
	Answer string `csv:"Answer"`
}

type MessagesQuestionCsv struct {
	Question string `csv:"Question"`
}

func reqConvertDialog(ctx context.Context, req *pb.DialogReq) *dao.DialogParam {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil
	}
	param := &dao.DialogParam{}

	param.ProjectID = &pbUserCtx.ProjectId

	param.User = &pbUserCtx.UserId

	if len(req.AppId) != 0 {
		param.AppId = &req.AppId
	}

	if req.StartTime > 0 {
		param.StartTime = &req.StartTime
	}
	if req.EndTime > 0 {
		param.EndTime = &req.EndTime
	}

	return param

}

func (d DialogImpl) Valid(ctx context.Context, chatId string) error {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return err
	}

	dialog, err := dao.DialogDAOImpl.GetByChatId(ctx, chatId)
	if err != nil {
		return err
	} else if dialog != nil {
		if dialog.User != pbUserCtx.UserId || dialog.ProjectID != pbUserCtx.ProjectId {
			return errors.Errorf(fmt.Sprintf("user and project_id is not satisfied,"+
				"db_user:%s,db_project:%s,cur_user:%s, cur_project_id:%s", dialog.User, dialog.ProjectID, pbUserCtx.UserId, pbUserCtx.ProjectId))
		}
	}
	return nil
}

func (d DialogImpl) BatchCreate(ctx context.Context, param *models.ChainRunReq, buffer *bytes.Buffer) error {

	chain, err := dao.AppletChainDAOImpl.GetByID(ctx, param.ChainID)
	if err != nil {
		return err
	}

	po := models.DialogToPo(ctx, param, chain.Name, chain.CreatedType)

	// create dialog
	size, err := dao.DialogDAOImpl.Count(ctx, param.ChatId)
	if size == 0 {
		err := dao.DialogDAOImpl.Create(ctx, po)
		if err != nil {
			return err
		}
	} else if err := d.Valid(ctx, param.ChatId); err != nil {
		return err
	}

	if err := query.Q.Transaction(func(tx *query.Query) error {
		question := models.QuestionToPo(param.Params, po.ChatID)
		answer := models.AnswerToPo(buffer, po.ChatID)
		err = dao.DialogDAOImpl.BatchCreateMessage(ctx, []*generated.DialogMessage{question, answer})
		if err != nil {
			dao.DialogDAOImpl.RevokeMessage(ctx, answer.ID)
			dao.DialogDAOImpl.RevokeMessage(ctx, question.ID)
			return errors.Errorf(fmt.Sprintf("cannot add message,err: %s", err.Error()))
		}

		dialog, err := dao.DialogDAOImpl.GetByChatId(ctx, param.ChatId)
		if err != nil {
			return err
		}

		err = dao.DialogDAOImpl.UpdateColumns(ctx, dialog.ChatID, question.ID, answer.ID)
		if err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}

	return nil

}

func (d DialogImpl) DeleteSingleDialog(ctx context.Context, req *pb.MessageReq) (*string, error) {
	if err := d.Valid(ctx, req.ChatId); err != nil {
		return nil, err
	}

	param := reqConvertMessage(req)
	_, err := dao.DialogDAOImpl.Delete(ctx, req.ChatId)
	if err != nil {
		return nil, err
	}
	return dao.DialogDAOImpl.DeleteMessages(ctx, param)
}

func (d DialogImpl) GetAppChat(ctx context.Context, req *pb.MessageReq) (*pb.MessageRes, error) {

	res := &pb.MessageRes{
		ChatId:   req.ChatId,
		Messages: make([]*pb.Message, 0),
	}

	if err := d.Valid(ctx, req.ChatId); err != nil {
		return nil, err
	}

	param := reqConvertMessage(req)

	size, err := dao.DialogDAOImpl.CountMessages(ctx, param)
	if err != nil {
		return res, err
	}

	res.Size = size

	if size > 0 {
		messages, err := dao.DialogDAOImpl.CollectionMessages(ctx, param, req.PageReq)
		if err != nil {
			return res, err
		}
		for _, message := range messages {
			res.Messages = append(res.Messages, models.Message2DO(message))
		}
	}

	return res, nil
}

func (d DialogImpl) GetDialogApps(ctx context.Context) (*pb.DialogAppRes, error) {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, err
	}

	res := &pb.DialogAppRes{
		Apps: make([]*pb.DialogApp, 0),
	}

	chains, size, err := dao.DialogDAOImpl.GetDialogApps(ctx, pbUserCtx.UserId, pbUserCtx.ProjectId)

	res.Size = size
	res.ProjectId = pbUserCtx.ProjectId
	res.User = pbUserCtx.UserId

	for _, chain := range chains {

		//重新按照 app_id 查询并保存一份，保证数据的一致性
		app, err := d.SaveDialogApp(ctx, chain.AppID)
		if err != nil {
			//如果应用链已经删除，那么使用删除前最后一次的数据
			app = chain
		}
		app.CreateTime = chain.CreateTime
		app.UpdatedTime = chain.UpdatedTime
		res.Apps = append(res.Apps, models.DialogApp2DO(app))
	}

	return res, nil
}

func (d DialogImpl) SaveDialogApp(ctx context.Context, chainId string) (*generated.DialogApp, error) {

	chain, err := dao.AppletChainDAOImpl.GetByID(ctx, chainId)
	if err != nil {
		return nil, err
	}

	experimentInfo := new(models.ExperimentInfo)
	if err = helper.String2Struct(chain.ExamplesInfo, &experimentInfo.ExamplesInfo); err != nil {
		return nil, err
	}
	po := models.DialogAppToPo(ctx, chainId, chain.Name, chain.CreatedType, experimentInfo.ImageUrl)

	_, err = dao.DialogDAOImpl.AddDialogApp(ctx, po)
	if err != nil {
		return nil, err
	}

	return dao.DialogDAOImpl.GetDialogApp(ctx, po)
}

func (d DialogImpl) DeleteDialogApp(ctx context.Context, id string) (int64, error) {

	po := models.DialogAppToPo(ctx, id, "", "", "")
	return dao.DialogDAOImpl.DeleteDialogApp(ctx, po)
}

func (d DialogImpl) GetDialogByChatId(ctx context.Context, appId, chatId string) (*generated.Dialog, error) {
	param := reqConvertDialog(ctx, &pb.DialogReq{
		AppId:  appId,
		ChatId: chatId,
	})
	return dao.DialogDAOImpl.GetDiaLog(ctx, param)
}

func reqConvertMessage(req *pb.MessageReq) *dao.MessageParam {
	param := &dao.MessageParam{}

	if len(req.ChatId) != 0 {
		param.ChatId = &req.ChatId
	}

	if len(req.Content) != 0 {
		param.Content = &req.Content
	}

	if len(req.Role) != 0 {
		param.Role = &req.Role
	}

	return param

}
func (d DialogImpl) GetAppChats(ctx context.Context, dialogReq *pb.DialogReq) (*pb.DialogRes, error) {
	res := &pb.DialogRes{
		Dialogs: make([]*pb.Dialog, 0),
	}

	param := reqConvertDialog(ctx, dialogReq)
	dialogs, size, err := dao.DialogDAOImpl.GetDialogs(ctx, param, dialogReq)
	if err != nil {
		return nil, err
	}

	res.Size = size

	if size > 0 {
		for _, dialog := range dialogs {
			question, err := dao.DialogDAOImpl.GetMessageById(ctx, dialog.LatestQuestionID)
			if err != nil {
				return nil, err
			}

			answer, err := dao.DialogDAOImpl.GetMessageById(ctx, dialog.LatestAnswerID)
			if err != nil {
				return nil, err
			}
			res.Dialogs = append(res.Dialogs, models.Dialog2DO(dialog, question, answer))
		}

	}

	total, err := dao.DialogDAOImpl.GetTotalDialog(ctx, dialogReq.AppId)
	if err != nil {
		return nil, err
	}
	res.Total = total

	return res, nil
}

func (d DialogImpl) DownloadDialogs(ctx context.Context, req *pb.DownloadDialogReq) (*pb.DownloadDialogRes, error) {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, err
	}

	//create tmp file
	tmpDir := fmt.Sprintf("./%d", time.Now().Unix())
	err = os.Mkdir(tmpDir, 0755)
	if err != nil {
		return nil, err
	}
	defer os.RemoveAll(tmpDir)

	filePrefix := strings.Join([]string{pbUserCtx.UserId, pbUserCtx.ProjectId, time.Now().Format(DateFormat)}, "_")
	files := make([]string, 0)
	for _, chatId := range req.ChatIds {

		param := reqConvertMessage(&pb.MessageReq{ChatId: chatId})

		if err := d.Valid(ctx, *param.ChatId); err != nil {
			return nil, err
		}

		dialog, err := dao.DialogDAOImpl.GetByChatId(ctx, chatId)
		if err != nil {
			return nil, err
		}

		messages, err := dao.DialogDAOImpl.CollectionMessages(ctx, param, nil)
		if err != nil {
			return nil, err
		}

		suffix := strings.Join([]string{filePrefix, dialog.AppName, chatId}, "_") + CSV

		filename := path.Join(tmpDir, suffix)

		messageCsv := geneMessage(messages, req.Type)

		err = writeToFile(filename, messageCsv)
		if err != nil {
			return nil, err
		}

		files = append(files, filename)
	}

	//zip
	zipFile, err := os.OpenFile(filePrefix+ZIP, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return nil, err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	for _, file := range files {
		src, err := os.Open(file)
		if err != nil {
			return nil, err
		}
		defer src.Close()

		zipEntry, err := zipWriter.Create(filepath.Base(file))
		if err != nil {
			return nil, err
		}

		_, err = io.Copy(zipEntry, src)
		if err != nil {
			return nil, err
		}
	}

	err = zipWriter.Close()
	if err != nil {
		return nil, err
	}

	res := &pb.DownloadDialogRes{}
	data, err := ioutil.ReadFile(filePrefix + ZIP)
	res.Data = data
	res.FileName = filePrefix + ZIP
	err = os.Remove(res.FileName)
	if err != nil {
		return nil, err
	}

	return res, nil

}

func geneMessage(messages []*generated.DialogMessage, dType pb.DOWNLOAD_TYPE) interface{} {
	switch dType {
	case pb.DOWNLOAD_TYPE_DOWNLOAD_TYPE_ALL:
		{
			csv := make([]*MessagesAllCsv, 0)
			for i := 0; i < len(messages); i += 2 {
				if i >= len(messages) {
					break
				}
				//fixme resolve random issue
				m1 := messages[i]
				m2 := messages[i+1]
				m := &MessagesAllCsv{}

				if m1.Role == string(models.RoleTypeAnswer) {
					m.Answer = m1.Content
				} else if m1.Role == string(models.RoleTypeQuestion) {
					m.Question = m1.Content
				}
				if m2.Role == string(models.RoleTypeAnswer) {
					m.Answer = m2.Content
				} else if m2.Role == string(models.RoleTypeQuestion) {
					m.Question = m2.Content
				}

				csv = append(csv, m)
			}
			return csv
		}
	case pb.DOWNLOAD_TYPE_DOWNLOAD_TYPE_ANSWER:
		{
			csv := make([]*MessagesAnswerCsv, 0)
			for _, message := range messages {
				if message.Role == string(models.RoleTypeAnswer) {
					csv = append(csv, &MessagesAnswerCsv{Answer: message.Content})
				}
			}
			return csv
		}
	case pb.DOWNLOAD_TYPE_DOWNLOAD_TYPE_QUESTION:
		{
			csv := make([]*MessagesQuestionCsv, 0)
			for _, message := range messages {
				if message.Role == string(models.RoleTypeQuestion) {
					csv = append(csv, &MessagesQuestionCsv{Question: message.Content})
				}
			}
			return csv
		}
	default:
		return nil
	}

}

func writeToFile(filename string, records interface{}) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// add utf-8 bom
	file.WriteString("\xEF\xBB\xBF")

	if err := gocsv.MarshalFile(records, file); err != nil {
		return err
	}

	return nil
}
