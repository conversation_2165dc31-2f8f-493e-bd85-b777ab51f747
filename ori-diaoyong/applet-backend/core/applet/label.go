package applet

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type Label interface {
	// ListLabel 查询应用链相关的标签
	ListLabel(ctx context.Context, chainType models.ChainType) (models.LabelGroups, error)
	// BatchSaveLabelIgnoreDup 批量保存标签(忽略重复)-labels表
	BatchSaveLabelIgnoreDup(ctx context.Context, creator string, labels models.LabelGroups) error
	// ListCustomWidgetLabel 查询自定义算子相关的标签
	ListCustomWidgetLabel(ctx context.Context, projectID string) (*models.LabelGroups, error)
	ListExperimentLabel(ctx context.Context, projectID string) (*models.LabelGroups, error)
}

type LabelImpl struct {
}

func (l LabelImpl) ListLabel(ctx context.Context, chainType models.ChainType) (models.LabelGroups, error) {
	projectID := helper.GetProjectID(ctx)
	var chains []*models.AppletChainBaseDO
	var err error
	// 不再存在模板链的概念了
	chains, err = ChainManager.SearchForBaseDO(ctx, &SearchChainParam{
		ProjectID: projectID,
	})
	if err != nil {
		return nil, err
	}
	labels := make([]models.LabelGroups, 0)
	for _, c := range chains {
		labels = append(labels, c.Labels)
	}
	res := models.MergeLabels(labels)
	return res, nil

}

func (l LabelImpl) BatchSaveLabelIgnoreDup(ctx context.Context, creator string,
	labels models.LabelGroups) error {
	labelPOs := models.ConvertLabelDO2PO(labels, creator)
	return dao.AppletLabelDAOImpl.BatchUpsertLabel(ctx, labelPOs)
}
func (l LabelImpl) ListCustomWidgetLabel(ctx context.Context, projectID string) (*models.LabelGroups, error) {
	widgets, err := WidgetManager.ListCustomWidgets(ctx, &dao.CustomWidgetQueryParam{
		ProjectID: ptr.String(projectID),
	})
	if err != nil {
		return nil, err
	}
	labels := make([]models.LabelGroups, 0)
	for _, c := range widgets {
		labels = append(labels, *c.LabelInfo)
	}
	res := models.MergeLabels(labels)
	return &res, nil
}

func (l LabelImpl) ListExperimentLabel(ctx context.Context, projectID string) (*models.LabelGroups, error) {
	widgets, err := ExperimentManager.ListExperiment(ctx, &dao.ExperimentQueryParam{
		ProjectID: ptr.String(projectID),
	})
	if err != nil {
		return nil, err
	}
	labels := make([]models.LabelGroups, 0)
	for _, c := range widgets {
		labels = append(labels, *c.LabelInfo)
	}
	res := models.MergeLabels(labels)
	return &res, nil
}
