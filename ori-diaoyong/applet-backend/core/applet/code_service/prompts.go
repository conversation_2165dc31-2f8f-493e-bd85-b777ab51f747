package code_service

import (
	"fmt"

	// "transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models/code_service"
)

const GENERATE_PYTHON_CODE_PROMPT_TEMPLATE = `
# Role
You are an expert programmer. Generate code based on the following instructions.

# Instructions

## Functional Requirements
%s

## Implementation Requirements
Write the code in python.
Please ensure that you meet the following requirements:
1. Define a function named 'handler'.
2. The 'handler' function must have and can only have one input parameter. You may modify the name of the input parameter of the 'handler' function.
3. The handler function is the entry point that will be executed.
4. Python libraries need to be imported before they can be used.
5. You may need to generate code based on existing code and the input value.

# Existing Code
The content within the <existing-code></existing-code> XML tags is existing code. Use it when you are requested to modify existing code.
<existing-code>
%s
</existing-code>

# Handler Function Input Value
The content of type %s within the <input-value></input-value> XML tags is the input value of handler function. You may need to generate code based on it.
<input-value>
%s
</input-value>

# Generated Code Format
- Provide ONLY the code without any additional explanations, comments, or markdown formatting.
- DO NOT use markdown code blocks ` +
	"(``` or ``` python). Return the raw code directly." +
	`
- The code should start immediately after this instruction, without any preceding newlines or spaces.       
- The code should be complete, functional, and follow best practices for python.

# Example Generated Code
import json
def handler(data: str) -> any:
    result = json.loads(data)
    return result

# Generated Code
`

const GENERATE_GO_TEMPLATE_CODE_PROMPT_TEMPLATE = `
You are a professional Go Template developer. Your task is to generate Go Template code based primarily on the user's requirement.

## User Requirement:
%s

## Existing Code (optional reference):
The content within the <existing-code></existing-code> XML tags is existing code. Use it when you are requested to modify existing code.
<existing-code>
%s
</existing-code>

## Input Example (for test data, optional):
The content of type %s within the <input-value></input-value> XML tags is the input value of Go-Template. You may need to generate code based on it.
<input-value>
%s
</input-value>

Instructions:
- Focus on the user requirement as the primary source of truth.
- If the existing code or input data does not align with the requirement, you may ignore them.
- Output valid, well-structured Go Template code.
- Do NOT include any explanation — only output the final template.

Generate the Go Template code accordingly.
`

const GENERATE_JSONNET_CODE_PROMPT_TEMPLATE = `
You are an expert in Jsonnet templating. Your task is to generate valid Jsonnet code based on the user's requirement.

## User Requirement:
%s

## Existing Code (optional reference):
The content within the <existing-code></existing-code> XML tags is existing code. Use it when you are requested to modify existing code.
<existing-code>
%s
</existing-code>

## Input Example (JSON input bound to variable 'input', optional):
The content of type %s within the <input-value></input-value> XML tags is the input value of Jsonnet. You may need to generate code based on it.
<input-value>
%s
</input-value>

Instructions:
- Assume the variable 'input' is already defined as: local input = <JSON>;
- Do NOT include 'local input = ...' in your output — it is already handled by the system.
- Your output must be a valid Jsonnet object, array, or expression that uses the existing 'input' variable.
- The generated code will be appended directly after the input declaration and evaluated using github.com/google/go-jsonnet v0.20.0.
- If the existing code or input does not match the requirement, you may ignore them.
- Only output Jsonnet code. No explanation, no comments, and no markdown formatting.

Generate the Jsonnet code accordingly.
`

func getGenerateCodePrompt(instruction string, existingCode string, input code_service.InputPayload, language string) (string, error) {
	// 将原始 Inputs 内容作为字符串打印到 prompt 中（最终输出）
	inputStr := string(input.Inputs)

	// 限制长度（防止截断导致 json 无效，可以保守一点，比如截在 3000 字节以内）
	maxLen := conf.Config.CodeService.MaxInputLen
	if len(inputStr) > maxLen {
		inputStr = inputStr[:maxLen] + "..."
	}

	var prompt_template string
	switch language {
	case code_service.CodeLanguagePython:
		prompt_template = GENERATE_PYTHON_CODE_PROMPT_TEMPLATE
	case code_service.CodeLanguageJsonnet:
		prompt_template = GENERATE_JSONNET_CODE_PROMPT_TEMPLATE
	case code_service.CodeLanguageGoTemplate:
		prompt_template = GENERATE_GO_TEMPLATE_CODE_PROMPT_TEMPLATE
	default:
		prompt_template = ""
	}

	return fmt.Sprintf(prompt_template, instruction, existingCode, input.Type, inputStr), nil
}
