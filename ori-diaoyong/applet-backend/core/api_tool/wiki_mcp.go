package api_tool

import (
	"fmt"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
)

const WikiToolAPI = "/api/v1/tool/collections/builtin-tool-wiki-dynamic-mcp/dynamic-mcp"

func newWikiMcpServer() *api_tools.APIToolCollectionDO {
	currentNamespace := k8s.CurrentNamespaceInCluster()
	wikiMcpUrl := fmt.Sprintf("http://%s.%s%s%s", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, WikiToolAPI)
	wikiCollection := &api_tools.APIToolCollectionDO{
		BaseInfo: api_tools.APIToolCollectionBaseDO{
			ID:                 "builtin-tool-wiki-dynamic-mcp",
			Name:               "Wiki搜索mcp",
			Creator:            "",
			ProjectID:          "",
			Desc:               "Wiki搜索",
			APIToolCnt:         2,
			Released:           ptr.Bool(true),
			LastReleaseTimeSec: 1749091632,
			Type:               api_tools.APIToolTypeBuiltin,
			ServerType:         agent_definition.ServerTypeMCP,
			McpType:            agent_definition.McpTypeStreamableHttp,
			CreateTimeSec:      1749091631,
			UpdateTimeSec:      1749091632,
			LogoUrl:            "",
		},
		Tools: []*api_tools.APIToolDO{
			{
				ID:        "builtin-wiki-search-tool",
				Name:      "Wiki关键字搜索",
				ProjectID: "",
				Alias:     "http_request",
				Desc:      "根据关键字搜索Wiki排名前几的文档",
				Method:    "GET",
				Path:      "https://172.16.20.30/rest/api/content/search",
				TestState: api_tools.APIToolTestStateInit,
				ParamValues: []agent_definition.APIToolParam{
					{
						Name:           "keyword",
						Desc:           "该参数代表用户想要搜索的关键字",
						ParamValueType: "string",
						Required:       true,
						DefaultValue:   "sophon",
						ModelIgnore:    false,
					},
					{
						Name:           "limit",
						Desc:           "该参数代表返回结果的数量",
						ParamValueType: "int",
						Required:       true,
						DefaultValue:   3,
						ModelIgnore:    false,
					},
				},
			},
			{
				ID:        "builtin-wiki-document-tool",
				Name:      "Wiki提取文档",
				ProjectID: "",
				Alias:     "http_request",
				Desc:      "根据文档id提取文档详情",
				Method:    "GET",
				Path:      "https://172.16.20.30/rest/api/content",
				TestState: api_tools.APIToolTestStateInit,
				ParamValues: []agent_definition.APIToolParam{
					{
						Name:           "doc_id",
						Desc:           "文档id",
						ParamValueType: "string",
						Required:       true,
						DefaultValue:   "",
						ModelIgnore:    false,
					},
				},
			},
		},
		MetaType: api_tools.APIToolMetaTypeJson,
		MetaInfo: []byte{},
		Headers:  map[string]string{"Accept": "application/json", "Authorization": "Basic bGFueWluZy5ndW86R2x5OTI5MSQ="},
		BaseURL:  wikiMcpUrl,
	}

	return wikiCollection
}
