module transwarp.io/applied-ai/doc-engine-go

replace transwarp.io/applied-ai/aiot/vision-std => ../vision-std

replace transwarp.io/aip/llmops-common => ../llmops-common

require (
	github.com/go-http-utils/headers v0.0.0-20181008091004-fed159eddc2a
	transwarp.io/applied-ai/aiot/vision-std v0.0.0
)

require (
	github.com/juju/errors v0.0.0-20181118221551-089d3ea4e4d5 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	golang.org/x/sys v0.22.0 // indirect
	gopkg.in/check.v1 v1.0.0-20190902080502-41f04d3bba15 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
)

go 1.20
