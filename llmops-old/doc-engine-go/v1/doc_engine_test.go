package doc_engine

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"testing"
)

var c *DocEngineClient
var ctx = context.Background()

func init() {

	mc, err := NewDocEngineCli(context.Background(), DocEngineConfig{
		Address: "**************:6632",
	})
	if err != nil {
		panic(err)
	}
	c = mc
}

func TestKnowledgeBase(t *testing.T) {
	filePath := "req.json"

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// 读取文件内容
	body, err := ioutil.ReadAll(file)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	// 解析JSON数组
	var createKBReq CreateKnowledgeBaseReq
	if err := json.Unmarshal(body, &createKBReq); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}
	t.Run("Create", func(t *testing.T) {
		client := c
		ctx := context.TODO()
		rsp, err := client.CreateKnowledgeBase(ctx, &createKBReq)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Create KnowledgeBase: %+v", rsp.Result)
	})

	t.Run("List", func(t *testing.T) {
		client := c
		ctx := context.TODO()
		rsp, err := client.ListKnowledgeBase(ctx, createKBReq.ProjectId, createKBReq.TenantId)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("List KnowledgeBase: %+v", rsp.Result)
	})

	t.Run("Get", func(t *testing.T) {
		client := c
		ctx := context.TODO()
		rsp, err := client.GetKnowledgeBase(ctx, createKBReq.Name)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Get KnowledgeBase: %+v", rsp.Result)
	})

	t.Run("Update", func(t *testing.T) {
		client := c
		ctx := context.TODO()
		rsp, err := client.GetKnowledgeBase(ctx, createKBReq.Name)
		if err != nil {
			t.Fatal(err)
		}
		rsp.Result.Group.EntitySchemaConf.Name = "test1_3"
		req := &UpdateKnowledgeBaseReq{
			IndexBuildConf: IndexBuildConfReq{
				IndexTypeArr: DefaultIndexType(),
			},
			EntitySchemaConf: rsp.Result.Group.EntitySchemaConf,
		}
		updateRsp, err := client.UpdateKnowledgeBase(ctx, createKBReq.Name, req)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Update KnowledgeBase req: %+v", req)
		t.Logf("Update KnowledgeBase: %+v", updateRsp.Result)

		rsp, err = client.GetKnowledgeBase(ctx, createKBReq.Name)

		t.Logf("Get KnowledgeBase: %+v", rsp.Result)

	})

	t.Run("Upload & CreateIndex & GetDocs & GetSlices & retrieve", func(t *testing.T) {
		client := c
		ctx := context.TODO()
		rsp, err := client.UploadFiles(ctx, createKBReq.Name, []string{"非上市公司-中闽恒实供应链有限公司.docx"})
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Upload files: %+v", rsp[0])

		indexReq := &DocReq{
			DocIds: []string{rsp[0].FurDocInfos[0].FurDocId},
		}
		index, err := client.CreateIndex(ctx, createKBReq.Name, indexReq)
		t.Logf("Create index: %+v", index.Result)

		docs, err := client.GetDocs(ctx, createKBReq.Name)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Get docs: %+v", docs)

		req := &RetrieveChunksReq{
			GroupName: createKBReq.Name,
			DocId:     rsp[0].FurDocInfos[0].FurDocId,
			PageSize:  2,
			PageNum:   5,
			Keyword:   "",
		}
		chunks, err := client.RetrieveChunks(ctx, req)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Get slices: %+v", chunks)

		singleReq := &RetrieveSingleKnowledgeReq{
			DocNames:  []string{rsp[0].FurDocInfos[0].FurDocId},
			Query:     "法定代表人是谁",
			GroupName: createKBReq.Name,
			Type:      GraphPyCode,
		}
		res, err := client.RetrieveSingleKnowledge(ctx, singleReq)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Retrieve Single Knowledge: %+v", res.Result)

		multiReq := &RetrieveMultiKnowledgeReq{
			Ranges: []RetrieveDoc{{
				GroupName: createKBReq.Name,
				DocNames:  []string{rsp[0].FurDocInfos[0].FurDocId},
			}},
			Query:    "法定代表人是谁",
			ConfType: GraphPyCode,
		}
		res, err = client.RetrieveMultiKnowledge(ctx, multiReq)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("Retrieve Multi Knowledge: %+v", res.Result)
	})

}
