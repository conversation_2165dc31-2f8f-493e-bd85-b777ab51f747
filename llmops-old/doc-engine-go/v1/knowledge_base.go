package doc_engine

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type EntityConfig struct {
	EntityType             string `json:"entity_type"`               // 必填，实体类型
	EntityValueType        string `json:"entity_value_type"`         //必填，实体值类型
	EntityValueTemplate    string `json:"entity_value_template"`     //必填，实体值模板
	EntityValueSourceType  string `json:"entity_value_source_type"`  //必填，实体值来源类型
	EntityValueProcessType string `json:"entity_value_process_type"` //必填，实体值处理类型
	EntityTypeGroup        string `json:"entity_type_group"`         //必填，实体类型分组
}

type EntitySchemaConf struct {
	Name   string         `json:"name"`   //必填，schema名字
	Config []EntityConfig `json:"config"` //必填，schema配置
}

type IndexBuildConfReq struct {
	IndexTypeArr []IndexType `json:"index_type_arr"` //必填，索引类型
}

type IndexType string

const (
	Hippo       IndexType = "HIPPO"
	Scope       IndexType = "SCOPE"
	GraphLlm    IndexType = "GRAPH_LLM"
	GraphScript IndexType = "GRAPH_SCRIPT"
)

type CreateKnowledgeBaseReq struct {
	Name             string            `json:"name"`               //必填，知识库名字
	ProjectId        string            `json:"project_id"`         //必填，项目ID
	TenantId         string            `json:"tenant_id"`          //必填，空间ID
	IndexBuildConf   IndexBuildConfReq `json:"index_build_conf"`   //必填，索引类型配置
	EntitySchemaConf EntitySchemaConf  `json:"entity_schema_conf"` //必填，schema配置
}

type CreateKnowledgeBaseRsp struct {
	Code   int           `json:"code"`
	Result KnowledgeBase `json:"result"`
}

type KnowledgeBase struct {
	Name              string            `json:"name"`
	ProjectId         string            `json:"project_id"`
	TenantId          string            `json:"tenant_id"`
	CreateTimeMills   int64             `json:"create_time_mills"`
	UpdateTimeMills   int64             `json:"update_time_mills"`
	IndexBuildConfRsp IndexBuildConfRsp `json:"index_build_conf"`
	EntitySchemaConf  EntitySchemaConf  `json:"entity_schema_conf"`
}

type IndexBuildConfRsp struct {
	DocIds              []string            `json:"doc_ids"`
	IndexTypeArr        []IndexType         `json:"index_type_arr"`
	EntitySchemaName    string              `json:"entity_schema_name"`
	Rebuild             bool                `json:"rebuild"`
	WriteXml            bool                `json:"write_xml"`
	DocVersion          bool                `json:"doc_version"`
	WriteFurImg         bool                `json:"write_fur_img"`
	GroupName           string              `json:"group_name"`
	GroupRebuild        bool                `json:"group_rebuild"`
	FileParserConf      FileParserConf      `json:"file_parser_conf"`
	GraphIndexBuildConf GraphIndexBuildConf `json:"graph_index_build_conf"`
	ThreadNum           int64               `json:"thread_num"`
}

type FileParserConf struct {
	ParseImage bool   `json:"parse_image"`
	TargetDir  string `json:"target_dir"`
}

type GraphIndexBuildConf struct {
	ExtractGraph       bool   `json:"extract_graph"`
	CorrelateBackGraph bool   `json:"correlate_back_graph"`
	DumpGraph          bool   `json:"dump_graph"`
	EntitySchemaName   string `json:"entity_schema_name"`
}

type ListKnowledgeBaseRsp struct {
	Code   int32                     `json:"code"`
	Result []ListKnowledgeBaseResult `json:"result"`
}

type ListKnowledgeBaseResult struct {
	KnowledgeBase KnowledgeBase `json:"group"`
}

type GetKnowledgeBaseRsp struct {
	Code   int                    `json:"code"`
	Result GetKnowledgeBaseResult `json:"result"`
}

type GetKnowledgeBaseResult struct {
	Group                       KnowledgeBase `json:"group"`
	DocsNum                     int32         `json:"num_docs"`
	SupportedRetrieveStrategies []string      `json:"supported_retrieve_strategies"`
}

type UpdateKnowledgeBaseReq struct {
	IndexBuildConf   IndexBuildConfReq `json:"index_Build_Conf"`   //必填，索引类型配置
	EntitySchemaConf EntitySchemaConf  `json:"entity_schema_conf"` //必填，schema配置
}

type KnowledgeBaseInfo struct {
	GroupName   string     `json:"groupName"`
	SchemaName  string     `json:"schemaName"`
	FurDocInfos []DocInfos `json:"furDocInfos"`
}

type DocInfos struct {
	OrgFileName  string `json:"orgFileName"`
	FurDocId     string `json:"furDocId"`
	OrgFileSize  int    `json:"orgFileSize"`
	IndexState   string `json:"indexState"`
	FurFileState string `json:"furFileState"`
	OrgFileTime  string `json:"orgFileTime"`
	FurFileTime  string `json:"furFileTime"`
}

type StandardRsp struct {
	Code   int    `json:"code"`
	Result string `json:"result"`
}

type RetrieveChunksReq struct {
	GroupName string `json:"group_name"`
	DocId     string `json:"doc_id"`
	PageNum   int    `json:"page_num"`
	PageSize  int    `json:"page_size"`
	Keyword   string `json:keyword`
}

type RetrieveChunksRsp struct {
	Total    int32  `json:"total"`
	PageNum  int    `json:"pageNum"`
	PageSize int    `json:"pageSize"`
	Nodes    []Node `json:"nodes"`
}

type Node struct {
	Id      int    `json:"id"`
	Content string `json:"content"`
}

type QueryType string

const (
	HippoQuery       QueryType = "HIPPO"
	ScopeQuery       QueryType = "SCOPE"
	ScopeHippoRerank QueryType = "SCOPE_HIPPO_RERANK"
	GraphEntity      QueryType = "GRAPH_ENTITY"
	GraphSqlOpcode   QueryType = "GRAPH_SQL_OPCODE"
	GraphMixed       QueryType = "GRAPH_MIXED"
	GraphPyCode      QueryType = "GRAPH_PY_CODE"
	WholeDoc         QueryType = "WHOLE_DOC"
)

type RetrieveSingleKnowledgeReq struct {
	DocNames  []string  `json:"doc_names"`
	Query     string    `json:"query"`
	GroupName string    `json:"group_name"`
	Type      QueryType `json:"type"`
}

type RetrieveKnowledgeRsp struct {
	Code   int     `json:"Code"`
	Result []Chunk `json:"Result"`
}

type Chunk struct {
	Node      Node   `json:"node"`
	GroupName string `json:"group_name"`
	DocName   string `json:"doc_name"`
	DocId     string `json:"doc_id"`
}

type RetrieveMultiKnowledgeReq struct {
	Ranges   []RetrieveDoc `json:"ranges"`
	Query    string        `json:"query"`
	ConfType QueryType     `json:"confType"`
}

type RetrieveDoc struct {
	DocNames  []string `json:"doc_names"`
	GroupName string   `json:"group_name"`
}

type DocReq struct {
	DocIds []string `json:"docIds"`
}

type DocEngineConfig struct {
	Address string // 必填，数据库地址
}

func (c *DocEngineConfig) ValidAndSetDefault() error {
	if c.Address == "" {
		return stderr.InvalidParam.Error("address is necessary")
	}
	return nil
}
