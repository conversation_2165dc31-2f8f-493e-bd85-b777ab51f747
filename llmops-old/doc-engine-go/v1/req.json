{"name": "test_knowledge_base_1", "project_id": "test_doc_engine", "tenant_id": "llmops", "index_Build_Conf": {"index_type_arr": ["HIPPO", "SCOPE"]}, "entity_schema_conf": {"name": "test_schema", "config": [{"entity_type": "合同标的类型", "entity_value_type": "STRING", "entity_value_template": "合同标的类型是多选的，从下面的列表中选择：包含星环软件产品，包含一体机，包含维保服务，包含外采服务，包含人天服务等。", "entity_value_source_type": "EXTERNAL", "entity_value_process_type": "EXACT_CONTAINS", "entity_type_group": "EXTERNAL"}, {"entity_type": "客户类型", "entity_value_type": "STRING", "entity_value_template": "可能的取值：非交警客户/交警-总队/交警-大支队/交警-小支队", "entity_value_source_type": "EXTERNAL", "entity_value_process_type": "EXACT_CONTAINS", "entity_type_group": "EXTERNAL"}, {"entity_type": "维保服务类型", "entity_value_type": "STRING", "entity_value_template": "可能的取值：订阅服务/标准服务/专业服务/高级服务", "entity_value_source_type": "EXTERNAL", "entity_value_process_type": "EXACT_MATCH", "entity_type_group": "EXTERNAL"}, {"entity_type": "含税金额", "entity_value_type": "DOUBLE", "entity_value_template": "引用的段落或原文", "entity_value_source_type": "ENTITY_NODE", "entity_value_process_type": "SEMANTIC_COMPARE", "entity_type_group": "Entity"}, {"entity_type": "合同类型", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "ENTITY_NODE", "entity_value_process_type": "EXACT_MATCH", "entity_type_group": "Entity"}, {"entity_type": "签约方信息", "entity_value_type": "STRING", "entity_value_template": "签约双方的名称，地址，开户银行，账号，户名等信息。", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同主体与基本信息类"}, {"entity_type": "付款", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同行为与履行类"}, {"entity_type": "验收", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同行为与履行类"}, {"entity_type": "服务", "entity_value_type": "STRING", "entity_value_template": "合同中包含服务的部分如：人天服务，维护服务，维保服务，质保服务，培训服务等所有服务的内容。", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同行为与履行类"}, {"entity_type": "产品", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同行为与履行类"}, {"entity_type": "权利义务", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同权利与义务类"}, {"entity_type": "责任", "entity_value_type": "STRING", "entity_value_template": "一般情况指的是违约责任", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同权利与义务类"}, {"entity_type": "保密", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同保障与约束类"}, {"entity_type": "不可抗力", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同保障与约束类"}, {"entity_type": "反贿赂", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同相关法律与权益类"}, {"entity_type": "知识产权", "entity_value_type": "STRING", "entity_value_template": "合同中描述甲方向乙方购买星环软件产品，涉及到的产品知识产权归属约定。", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同相关法律与权益类"}, {"entity_type": "源代码", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同相关法律与权益类"}, {"entity_type": "转分包", "entity_value_type": "STRING", "entity_value_template": "合同中包含转包/分包/转让合同义务/转移合同义务的内容。", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同变更与衍生类"}, {"entity_type": "争议", "entity_value_type": "STRING", "entity_value_template": "", "entity_value_source_type": "TAG_NODE", "entity_value_process_type": "NONE", "entity_type_group": "合同变更与衍生类"}]}}