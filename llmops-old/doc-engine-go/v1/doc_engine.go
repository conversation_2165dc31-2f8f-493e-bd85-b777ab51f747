package doc_engine

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-http-utils/headers"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	docEngineRestfulHttpSchema   = "http"
	docEngineRestfulHttpBasePath = "v1/"
	applicationJson              = "application/json"
)

type DocEngineClient struct {
	host    string
	baseUrl string

	hc     http.Client
	router *DocEngineRouter
}

func DefaultIndexType() []IndexType {
	return []IndexType{Hippo, Scope}
}

func NewDocEngineCli(ctx context.Context, cfg DocEngineConfig) (*DocEngineClient, error) {
	if err := cfg.ValidAndSetDefault(); err != nil {
		return nil, stderr.Wrap(err, "invalid config")
	}
	br := baseRouteBuilder{
		Schema:   docEngineRestfulHttpSchema,
		Host:     cfg.Address,
		BasePath: docEngineRestfulHttpBasePath,
	}
	router := NewDocEngineRouter(br)
	deCli := &DocEngineClient{
		host:    cfg.Address,
		baseUrl: "",
		hc:      http.Client{},
		router:  &router,
	}

	return deCli, nil
}

func doRequest[T any](c *DocEngineClient, ctx context.Context, route Route, req any, rsp T) (T, error) {
	err := c.doRequest(ctx, route, req, rsp)
	return rsp, err
}

func (c *DocEngineClient) doRequest(ctx context.Context, route Route, req any, rsp any) (err error) {
	// wrap error
	pc, _, _, _ := runtime.Caller(1) // 0 表示当前函数调用栈帧
	caller := runtime.FuncForPC(pc).Name()
	if caller != "" {
		parts := strings.Split(caller, ".")
		caller = parts[len(parts)-1]
	}
	stdlog.Infof("[Doc engine:%s]%s %s", caller, route.Method(), route.Url())
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, caller)
		}
	}()

	// parse request to io.Reader
	if route.Method() == "" || route.Url() == "" {
		return stderr.InvalidParam.Error("method and url cannot be empty")
	}
	var bodyR io.Reader
	if req != nil {
		if route.headers[headers.ContentType] == applicationJson {
			bodyBs, err := json.Marshal(req)
			if err != nil {
				return stderr.Wrap(err, "do doc-engine request: marshal request: %+v", req)
			}
			bodyR = bytes.NewReader(bodyBs)
			stdlog.Debug(string(bodyBs))
		} else {
			bodyR = bytes.NewReader(req.(*bytes.Buffer).Bytes())
		}
	}
	// build request
	httpReq, err := http.NewRequest(route.Method(), route.Url(), bodyR)
	if err != nil {
		return stderr.Internal.Cause(err, "new http request")
	}
	httpReq.WithContext(ctx)
	for k, v := range route.headers {
		httpReq.Header.Add(k, v)
	}

	// do request and handle response or error
	httpRsp, err := c.hc.Do(httpReq)
	if err != nil {
		return stderr.Wrap(err, "do doc-engine request: %s %s", route.Method(), route.Url())
	}
	defer func() { _ = httpRsp.Body.Close() }()
	content, err := io.ReadAll(httpRsp.Body)
	stdlog.Infof("rsp: %s", string(content))
	if httpRsp.StatusCode != http.StatusOK {
		return stderr.Internal.Error(httpRsp.Status + string(content))
	}
	if rsp == nil {
		return stderr.InvalidParam.Error("must provided a response receiver pointer")
	}

	if route.unmarshaler != nil {
		if err = route.unmarshaler(content, rsp); err != nil {
			return stderr.Unmarshal.Cause(err, "unmatched between giving rsp receiver and rsp content")
		}
	} else {
		if err = json.Unmarshal(content, rsp); err != nil {
			return stderr.Unmarshal.Cause(err, "unmatched between giving rsp receiver and rsp content")
		}
	}

	return nil
}

func (c *DocEngineClient) requestUrl(ctx context.Context, method, url string, body io.Reader) string {

	// request
	req, err := http.NewRequest(method, url, body)
	req.Header.Add(headers.ContentType, applicationJson)

	if err != nil {
		println(err)
	}

	// response
	response, err := http.DefaultClient.Do(req)
	if err != nil {
		println(err)
	}
	defer response.Body.Close()

	// read response
	res, err := io.ReadAll(response.Body)
	if err != nil {
		println(err)
	}
	return string(res)
}

type Unmarshaler func([]byte, any) error

type Route struct {
	method      string
	url         string
	headers     map[string]string
	unmarshaler Unmarshaler
}

func (r *Route) Method() string {
	return r.method
}
func (r *Route) Url() string {
	return r.url
}

type baseRouteBuilder struct {
	Schema   string
	Host     string
	BasePath string
}

func (b baseRouteBuilder) NewRoute(method string, route string, qps ...string) Route {
	kvs := make(map[string][]string, 0)
	for i := 0; i < len(qps); i += 2 {
		kvs[qps[i]] = append(kvs[qps[i]], qps[i+1])
	}

	query := url.Values(kvs).Encode()
	if query == "" {
		return Route{
			method:  method,
			url:     fmt.Sprintf("%s://%s/%s", b.Schema, b.Host, path.Join(b.BasePath, route)),
			headers: map[string]string{headers.ContentType: applicationJson},
		}
	} else {
		return Route{
			method: method,
			url:    fmt.Sprintf("%s://%s/%s?%s", b.Schema, b.Host, path.Join(b.BasePath, route), query),
		}
	}

}

type DocEngineRouter struct {
	KnowledgeBase KnowledgeBaseRouter
	Query         QueryBaseRouter
}

type KnowledgeBaseRouter struct {
	baseRouteBuilder
}

type QueryBaseRouter struct {
	baseRouteBuilder
}

func NewDocEngineRouter(b baseRouteBuilder) DocEngineRouter {
	return DocEngineRouter{
		KnowledgeBase: KnowledgeBaseRouter{baseRouteBuilder: b},
		Query:         QueryBaseRouter{baseRouteBuilder: b},
	}
}

func (k *KnowledgeBaseRouter) Create() Route {
	return k.NewRoute(http.MethodPost, "groups")
}

func (k *KnowledgeBaseRouter) List(projectId string, tenantId string) Route {
	return k.NewRoute(http.MethodGet, "groups", "project_id", projectId, "tenant_id", tenantId)
}

func (k *KnowledgeBaseRouter) Get(knowledgeName string) Route {
	return k.NewRoute(http.MethodGet, "groups/"+knowledgeName)
}

func (k *KnowledgeBaseRouter) Update(knowledgeName string) Route {
	return k.NewRoute(http.MethodPut, "groups/"+knowledgeName)
}

func (k *KnowledgeBaseRouter) Delete(knowledgeName string) Route {
	return k.NewRoute(http.MethodDelete, "groups/"+knowledgeName)
}

func (k *KnowledgeBaseRouter) Upload(knowledgeName string) Route {
	return k.NewRoute(http.MethodPost, "groups/"+knowledgeName+"/upload-docs")
}

func (k *KnowledgeBaseRouter) DeleteDocs(knowledgeName string) Route {
	return k.NewRoute(http.MethodPost, "groups/"+knowledgeName+"/docs:delete")
}

func (k *KnowledgeBaseRouter) CreateIndex(knowledgeName string) Route {
	return k.NewRoute(http.MethodPost, "groups/"+knowledgeName+"/build-index")
}

func (k *KnowledgeBaseRouter) GetDocs(knowledgeName string) Route {
	return k.NewRoute(http.MethodGet, "groups/"+knowledgeName+"/docs")
}

func (k *KnowledgeBaseRouter) RetrieveChunks(knowledgeName string, docId string, pageNum int, pageSize int, keyword string) Route {
	if keyword == "" {
		return k.NewRoute(http.MethodGet, "groups/"+knowledgeName+"/docs/"+docId+"/nodes",
			"page_num", fmt.Sprintf("%d", pageNum), "page_size", fmt.Sprintf("%d", pageSize))
	} else {
		return k.NewRoute(http.MethodGet, "groups/"+knowledgeName+"/docs/"+docId+"/nodes",
			"page_num", fmt.Sprintf("%d", pageNum), "page_size", fmt.Sprintf("%d", pageSize), "keyword", keyword)
	}
}

func (c *DocEngineClient) CreateKnowledgeBase(ctx context.Context, req *CreateKnowledgeBaseReq) (*CreateKnowledgeBaseRsp, error) {
	route := c.router.KnowledgeBase.Create()

	rsp := new(CreateKnowledgeBaseRsp)
	err := c.doRequest(ctx, route, req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	stdlog.Infof("create knowledge base for doc engine: %s", req.Name)
	return rsp, nil
}

func (c *DocEngineClient) ListKnowledgeBase(ctx context.Context, projectId string, tenantId string) (*ListKnowledgeBaseRsp, error) {
	rsp := new(ListKnowledgeBaseRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.List(projectId, tenantId), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) GetKnowledgeBase(ctx context.Context, knowledgeName string) (*GetKnowledgeBaseRsp, error) {
	rsp := new(GetKnowledgeBaseRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.Get(knowledgeName), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) UpdateKnowledgeBase(ctx context.Context, knowledgeName string, req *UpdateKnowledgeBaseReq) (*StandardRsp, error) {
	rsp := new(StandardRsp)
	stdlog.Infof("doc engine entity schema: %s", req)
	err := c.doRequest(ctx, c.router.KnowledgeBase.Update(knowledgeName), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) DeleteKnowledgeBase(ctx context.Context, knowledgeName string) (*StandardRsp, error) {
	rsp := new(StandardRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.Delete(knowledgeName), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) UploadFiles(ctx context.Context, knowledgeName string, filePaths []string) ([]*KnowledgeBaseInfo, error) {
	// 创建表单文件
	body := &bytes.Buffer{}
	multipartWriter := multipart.NewWriter(body)

	for _, f := range filePaths {
		// open file
		stdlog.Infof("file is %s", f)
		file, err := os.Open(f)
		//stdlog.Infof("content is %s", file)
		if err != nil {
			println(err)
		}
		defer file.Close()

		// 创建文件字段并写入文件
		part, err := multipartWriter.CreateFormFile("files", filepath.Base(f))
		if err != nil {
			return nil, fmt.Errorf("failed to create form file: %w", err)
		}

		// 将文件内容写入到表单文件字段
		_, err = io.Copy(part, file)
		if err != nil {
			return nil, fmt.Errorf("failed to copy file content: %w", err)
		}
	}

	// 结束写入multipart表单
	err := multipartWriter.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close writer: %w", err)
	}

	// request
	route := c.router.KnowledgeBase.Upload(knowledgeName)
	route.headers[headers.ContentType] = multipartWriter.FormDataContentType()
	var rsp []*KnowledgeBaseInfo
	stdlog.Infof("body: %s", body)
	err = c.doRequest(ctx, route, body, &rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) DeleteDocs(ctx context.Context, knowledgeName string, req *DocReq) (*StandardRsp, error) {
	// request
	rsp := new(StandardRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.DeleteDocs(knowledgeName), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) CreateIndex(ctx context.Context, knowledgeName string, req *DocReq) (*StandardRsp, error) {
	// request
	rsp := new(StandardRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.CreateIndex(knowledgeName), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) GetDocs(ctx context.Context, knowledgeName string) ([]*KnowledgeBaseInfo, error) {
	rsp := make([]*KnowledgeBaseInfo, 0, 1)
	err := c.doRequest(ctx, c.router.KnowledgeBase.GetDocs(knowledgeName), nil, &rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) RetrieveChunks(ctx context.Context, req *RetrieveChunksReq) (*RetrieveChunksRsp, error) {
	rsp := new(RetrieveChunksRsp)
	err := c.doRequest(ctx, c.router.KnowledgeBase.RetrieveChunks(req.GroupName, req.DocId, req.PageNum, req.PageSize, req.Keyword), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (q *QueryBaseRouter) RetrieveSingleKnowledge() Route {
	return q.NewRoute(http.MethodPost, "query")
}

func (q *QueryBaseRouter) RetrieveMultiKnowledge() Route {
	return q.NewRoute(http.MethodPost, "query:cross")
}

func (c *DocEngineClient) RetrieveSingleKnowledge(ctx context.Context, req *RetrieveSingleKnowledgeReq) (*RetrieveKnowledgeRsp, error) {
	rsp := new(RetrieveKnowledgeRsp)
	err := c.doRequest(ctx, c.router.Query.RetrieveSingleKnowledge(), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func (c *DocEngineClient) RetrieveMultiKnowledge(ctx context.Context, req *RetrieveMultiKnowledgeReq) (*RetrieveKnowledgeRsp, error) {
	rsp := new(RetrieveKnowledgeRsp)
	err := c.doRequest(ctx, c.router.Query.RetrieveMultiKnowledge(), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}
