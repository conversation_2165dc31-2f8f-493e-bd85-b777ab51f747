package stdsrv

import (
	"bufio"
	"bytes"
	"context"
	"io"
	"net/http"
	"runtime"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/sse"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type SSEEvt = string
type SSEEvent = sse.Event

const (
	SSEEvtClose       SSEEvt = "close"
	SSEEvtError       SSEEvt = "error"
	SSEEvtMessage     SSEEvt = "message"
	SSEEvtTrace       SSEEvt = "trace"
	SSEEvtReplace     SSEEvt = "replace"
	SSEEvtEmpty       SSEEvt = ""
	SSEEvtThought     SSEEvt = "thought"
	SSEEvtAction      SSEEvt = "action"
	SSEEvtObservation SSEEvt = "observation"
	SSEEvtDebug       SSEEvt = "debug"
	SSEEvtLog         SSEEvt = "log"
	SSEEvtCitation    SSEEvt = "citation"
	SSEEvtCancel      SSEEvt = "cancel"

	errorPrefix = "[error] "
)

type Response struct {
	Response any `json:"response"`
}

type ChainBlockingResponse struct {
	Id       string `json:"id,omitempty"`
	Response any    `json:"response,omitempty"`
	Debug    any    `json:"debug,omitempty"`
	Error    error  `json:"error,omitempty"`
}

// SSESendData 发送一个sse事件，事件类型为SSEEvtMessage，事件ID为id，事件数据为data
func SSESendData(w http.ResponseWriter, id string, data any) error {
	return WriteSSE(w, sse.Event{
		Event: SSEEvtMessage,
		Id:    id,
		Data:  data,
	})
}

// SSESendDataWithoutEvent 发送一个sse事件，事件类型为SSEEvtEmpty，事件ID为id，事件数据为data
func SSESendDataWithoutEvent(w http.ResponseWriter, id string, data any) error {
	return WriteSSE(w, sse.Event{
		Event: SSEEvtEmpty,
		Id:    id,
		Data:  data,
	})
}
func SSESendDataWithEventName(w http.ResponseWriter, id string, eventName SSEEvt, data any) error {
	return WriteSSE(w, sse.Event{
		Event: eventName,
		Id:    id,
		Data:  data,
	})
}

func SSESendError(w http.ResponseWriter, id string, err error) error {
	return WriteSSE(w, sse.Event{
		Event: SSEEvtError,
		Id:    id,
		Data:  errorPrefix + err.Error(), // FIXME 使用 event 进行区分
	})
}

func SendClose(w http.ResponseWriter, id string) error {
	return WriteSSE(w, sse.Event{
		Event: SSEEvtClose,
		Id:    id,
		Data:  "",
	})
}

// SSESendReplace 使用标准文字回复替换未通过安全检测的回复
//
// 参数:
//
//	stdRspStr string: 标准文字回复，最终以 Response{Response: stdRspStr} 结构的形式返回
func SSESendReplace(w http.ResponseWriter, id string, stdRspStr string) error {
	// 使用WriteSSE函数发送一个类型为SSEEvtReplace的SSE事件。
	// 事件ID为id，事件数据为stdRspStr。
	return WriteSSE(w, sse.Event{
		Event: SSEEvtReplace,
		Id:    id,
		Data:  Response{Response: stdRspStr},
	})
}

func SetSSEHeader(hd http.Header) {
	hd.Set("Content-Type", "text/event-stream")
	hd.Set("Cache-Control", "no-cache")
	hd.Set("Connection", "keep-alive")
}

func EncodeAndFlush(w http.ResponseWriter, e SSEEvent) error {
	err := sse.Encode(w, e)
	if err != nil {
		return stderr.Wrap(err, "encode server send event: %+v", e)
	}
	fls, ok := w.(http.Flusher)
	if !ok {
		return stderr.Error("ResponseWriter %T not implemented http.Flusher", w)
	}
	defer func() {
		if r := recover(); r != nil {
			trace := make([]byte, 4096)
			n := runtime.Stack(trace, false)
			// 打印堆栈信息, 包含错误信息、发生时间和堆栈跟踪
			stdlog.Errorf("panic occurred:\n"+
				"error: %v\n"+
				"time: %v\n"+
				"stack trace:\n%s",
				r,
				time.Now(),
				string(trace[:n]))
		}
	}()
	fls.Flush()
	return nil
}

type CurrentWriter struct {
	sync.Mutex
	http.ResponseWriter
}

func GetCurrentWriter(w http.ResponseWriter) *CurrentWriter {
	return &CurrentWriter{
		ResponseWriter: w,
	}
}
func WriteSSE(w http.ResponseWriter, e SSEEvent) error {
	if cw, ok := w.(*CurrentWriter); ok {
		cw.Lock()
		defer cw.Unlock()
		w = cw.ResponseWriter
	}
	SetSSEHeader(w.Header())
	w.WriteHeader(200)
	if err := EncodeAndFlush(w, e); err != nil {
		return stderr.Wrap(err, "encode and flush sse")
	}
	return nil
}

const MB = 1024 * 1024
const MAX_SSE_DATA_SIZE = 2048 * MB
const DELIMITER = '\n'

// SSEReadData 从http.Response.Body读取一个SSE message，message以两个'\n'为分隔符分割
func SSEReadData(reader *bufio.Reader) ([]byte, error) {
	var sseData []byte
	for {
		// 读取第一个'\n'前的字节块
		chunk, readErr := reader.ReadBytes(DELIMITER)
		// 如果读取错误且不是EOF，则返回错误
		if readErr != nil && readErr != io.EOF {
			return nil, stderr.Wrap(readErr, "reader read bytes")
		}
		hasBackslashR := false
		// 如果读取到的数据以 \r\n 结尾，移除 \r
		if len(chunk) > 1 && chunk[len(chunk)-2] == '\r' && chunk[len(chunk)-1] == '\n' {
			chunk = chunk[:len(chunk)-2]
			chunk = append(chunk, '\n')
			sseData = append(sseData, chunk...)
			hasBackslashR = true
		} else {
			// 将读取到的字节块追加到sseData中
			sseData = append(sseData, chunk...)
		}
		if hasBackslashR {
			// 判断下一个字节是否是 \r\n
			nextByte, peekErr := reader.Peek(2) // Peek 2 字节
			if peekErr != nil && peekErr != io.EOF {
				return nil, stderr.Wrap(peekErr, "reader peek")
			}

			// 如果下一个字节是 \r\n，说明已经读取到了完整的 message 字节块
			if len(nextByte) == 2 && nextByte[0] == '\r' && nextByte[1] == '\n' {
				sseData = append(sseData, '\n') // 添加\n
				reader.Discard(2)               // 跳过 \r\n
				return sseData, nil
			}
		}
		// 判断下一个字节是否是'\n'
		nextByte, peekErr := reader.Peek(1)
		if peekErr != nil && peekErr != io.EOF {
			return nil, stderr.Wrap(peekErr, "reader peek")
		}
		// 如果下一个字节也是'\n'，说明已经读取到了一个完整的message字节块，返回该字节块
		if len(nextByte) > 0 && nextByte[0] == DELIMITER {
			sseData = append(sseData, nextByte...)
			reader.ReadByte() // Peek方法并不会读取下一个字节，所以需要把'\n'读掉
			return sseData, nil
		}
		// 如果数据读取完毕，则返回sseData和EOF
		if readErr == io.EOF {
			return sseData, io.EOF
		}
		// 如果数据大小超过最大限制，则返回错误，防止无限循环读取
		if len(sseData) > MAX_SSE_DATA_SIZE {
			return nil, stderr.Error("sse data size is %d MB, which exceeds the max limit %d MB", len(sseData)/MB, MAX_SSE_DATA_SIZE/MB) // 如果sseData的大小超过最大限制，则返回错误
		}
	}
}

type SSEEventHandler func(event SSEEvent) error

// SSEReadEvent 从给定的 Reader 中流式读取SSE数据，并使用传入的Handler进行处理
// 一旦处理过程中返回错误，则读取处理流程将立即返回该错误
func SSEReadEvent(ctx context.Context, reader *bufio.Reader, evtHandler SSEEventHandler) error {
	if evtHandler == nil {
		return stderr.InvalidParam.Error("evt handler can not be nil")
	}
	for {
		select {
		case <-ctx.Done():
			return stderr.Internal.Error("context done")
		default:
		}

		data, err := SSEReadData(reader)
		if err != nil {
			if err == io.EOF {
				return nil
			}
			return stderr.Wrap(err, "read sse data")
		}

		evts, err := SSEDecodeData(data)
		if err != nil {
			return stderr.Wrap(err, "decode data as sse: %s", string(data))
		}
		for _, evt := range evts {
			if err = evtHandler(evt); err != nil {
				return err
			}
		}
	}
}

// SSEDecodeData 把字节流解码为sse Event数组
func SSEDecodeData(data []byte) ([]sse.Event, error) {
	events, err := sse.Decode(bytes.NewBuffer(data))
	if err != nil {
		return nil, stderr.Wrap(err, "decode")
	}
	return events, nil
}
