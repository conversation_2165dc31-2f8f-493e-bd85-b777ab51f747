package stdsrv

import (
	"fmt"
	"testing"
	"time"
)

func TestSyncBatchCallWithMaxConcurrency(t *testing.T) {
	total := 200
	ids := make([]string, total)
	for i := 0; i < total; i++ {
		ids[i] = fmt.Sprintf("%d", i)
	}
	cost := time.Second
	call := func(id string) error {
		t.Logf("handling id %s", id)
		time.Sleep(cost)
		return nil
	}
	maxConcurrency := 10

	begin := time.Now()
	rsp := SyncBatchCallWithMaxConcurrency(ids, maxConcurrency, call)
	end := time.Now()
	t.Logf("the cost duration want=%s, got=%s, rsp=%+v", time.Duration(int(cost)*len(ids)/maxConcurrency).String(), end.Sub(begin).String(), rsp)
}
