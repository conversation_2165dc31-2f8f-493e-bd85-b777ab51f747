package stdsrv

import (
	"reflect"
	"testing"
)

func TestBoomOriginReferer(t *testing.T) {
	tests := []struct {
		name           string
		rawURL         string
		want           TOriginReferer
		wantUrl        string
		wantGatewayUrl string
	}{
		{
			name:   "",
			rawURL: "https://**************:30745/llm/dev/project/assets/mlops/service?desc=true&order_by=update_time&name=asda#list",
			want: TOriginReferer{
				Schema:            "https",
				Host:              "**************:30745",
				TopPrefix:         "",
				SystemNS:          "dev",
				ProjectID:         "assets",
				Suffix:            "mlops/service",
				Raw:               "https://**************:30745/llm/dev/project/assets/mlops/service?desc=true&order_by=update_time&name=asda#list",
				IsNoneProj:        false,
				NoneProjectIDType: "",
			},
			wantUrl:        "https://**************:30745/llm/dev",
			wantGatewayUrl: "https://**************:30745/llm/dev/gateway",
		}, {
			name:   "",
			rawURL: "http://llmops.wuya-ai.com/llm/llmops/project/market/detail",
			want: TOriginReferer{
				Schema:            "http",
				Host:              `llmops.wuya-ai.com`,
				TopPrefix:         "",
				SystemNS:          "llmops",
				ProjectID:         "assets",
				Suffix:            "detail",
				Raw:               "http://llmops.wuya-ai.com/llm/llmops/project/market/detail",
				IsNoneProj:        true,
				NoneProjectIDType: NoneProjectIDMarket,
			},
			wantUrl:        "http://llmops.wuya-ai.com/llm/llmops",
			wantGatewayUrl: "http://llmops.wuya-ai.com/llm/llmops/gateway",
		}, {
			name:   "",
			rawURL: "https://**************:30745/fake/prefix/llm/dev/project/assets/sample/catalogs/assets/detail/49",
			want: TOriginReferer{
				Schema:    "https",
				Host:      "**************:30745",
				TopPrefix: "fake/prefix",
				SystemNS:  "dev",
				ProjectID: "assets",
				Suffix:    "sample/catalogs/assets/detail/49",
				Raw:       "https://**************:30745/fake/prefix/llm/dev/project/assets/sample/catalogs/assets/detail/49",
			},
			wantUrl:        "https://**************:30745/fake/prefix/llm/dev",
			wantGatewayUrl: "https://**************:30745/fake/prefix/llm/dev/gateway",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, ok := BoomTOriginReferer(tt.rawURL)
			if !ok {
				t.Errorf("failed to BoomTOriginReferer() : %v", tt.rawURL)
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BoomTOriginReferer() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(got.BaseUrl(), tt.wantUrl) {
				t.Errorf("BoomTOriginReferer() = %v, want %v", got.BaseUrl(), tt.wantUrl)
			}

			if !reflect.DeepEqual(got.BaseGatewayUrl(), tt.wantGatewayUrl) {
				t.Errorf("BoomTOriginReferer() = %v, want %v", got.BaseGatewayUrl(), tt.wantGatewayUrl)
			}

			pid, ok := ExtractProjectIDFromReferer(tt.rawURL)
			if !ok {
				t.Errorf("failed to ExtractProjectIDFromReferer() : %v", tt.rawURL)
			}
			if pid != tt.want.ProjectID {
				t.Errorf("ExtractProjectIDFromReferer() = %v, want %v", pid, tt.want.ProjectID)
			}

		})
	}
}
