package stdsrv

import (
	"fmt"
	"net/http"
	"net/url"
	"path"
	"regexp"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// NoneProjectIDType 定义了 特殊 "项目ID" 的类型
// 即路由中本该是项目ID的部分,实际是些特殊的菜单.
type NoneProjectIDType string

const (
	TranswarpCustomHeaderReferer                   = "T-Origin-Referer" // 前端NGINX注入的完整的浏览器上访问的地址
	SpecialProjectIDAssets                         = "assets"           // 公共空间
	NoneProjectIDMarket          NoneProjectIDType = "market"           // 首页, 模型/应用/知识市场的页面, 等同于公共空间
	NoneProjectIDCenter          NoneProjectIDType = "manage-center"    // 全局管理中心, 等同于公共空间
	NoneProjectIDNotification    NoneProjectIDType = "notifications"    // 我的消息(审批管理/
)

// noneProjIDsMapping 定义了 一些特殊 project id 的映射关系
var noneProjIDsMapping = map[string]string{
	string(NoneProjectIDMarket):       SpecialProjectIDAssets,
	string(NoneProjectIDCenter):       SpecialProjectIDAssets,
	string(NoneProjectIDNotification): SpecialProjectIDAssets,
}

var (
	// /project/：匹配字符串 /project/。
	// ([^/]+)：匹配 project 后面的 project_id，这个 ID 可以是任何不包含 / 的字符。
	// /：匹配紧跟在 project_id 后的 /。
	originReferRegex   = regexp.MustCompile(`/project/([^/]+)/`) // 从 URL Refer 中提取 project id 的 regex
	originReferPattern = regexp.MustCompile(`^(?:https?://[^/]+)?(?:/(?P<top_prefix>(?:[^/]+/)*[^/]+))?/llm/(?P<system_ns>[^/]+)/project/(?P<project_id>[^/]+)/(?P<suffix>.+)$`)
)

func GetRefererFromRequest(r *http.Request) (t TOriginReferer, ok bool) {
	if r == nil {
		stdlog.Warnf("can not get refer from nil request")
		return t, false
	}
	referer := r.Header.Get(TranswarpCustomHeaderReferer)
	if referer == "" {
		stdlog.Warnf("header not found or is empty: '%s'", TranswarpCustomHeaderReferer)
		return t, false
	}
	return BoomTOriginReferer(referer)
}

// ExtractProjectIDFromReferer 从浏览器 Origin Referer 中尝试提取 project id
func ExtractProjectIDFromReferer(url string) (string, bool) {
	to, ok := BoomTOriginReferer(url)
	if !ok {
		return "", false
	}
	return to.ProjectID, true
}

// TOriginReferer 表示前端当前浏览器访问的路由信息
// e.g.
// https://llmops.wuya-ai.com/llm/llmops/project/assets/detail
// https://**************:30745/llm/dev/project/assets/sample/catalogs/assets/detail/49
// https://**************:30745/llm/dev/project/assets/mlops/service?desc=true&order_by=update_time&name=asda#list
type TOriginReferer struct {
	Schema            string            // Schema 完整 http / https 访问地址中的协议部分
	Host              string            // Host 完整 http / https 访问地址中的地址部分
	TopPrefix         string            // TopPrefix 在 /llm 固定前缀之前的 可选的额外的前缀(可选)
	SystemNS          string            // SystemNS  在 /llm 固定前缀之后的 系统所在K8S_NS
	ProjectID         string            // ProjectID 在 /llm/$system_ns/project/ 之后的 project_id
	Suffix            string            // Suffix    在 /llm/$system_ns/project/$project_id/ 之后的 剩余部分
	Raw               string            // Raw       原始的 URL
	IsNoneProj        bool              // IsNoneProj 是否非项目ID
	NoneProjectIDType NoneProjectIDType // NoneProjectIDType 特殊项目的类型
}

func (t TOriginReferer) BaseUrl() string {
	return fmt.Sprintf("%s://%s/", t.Schema, t.Host) + path.Join(t.TopPrefix, "llm", t.SystemNS)
}

func (t TOriginReferer) BaseGatewayUrl() string {
	return t.BaseUrl() + "/gateway"
}
func (t TOriginReferer) BaseIstioGatewayUrl() string {
	return t.BaseGatewayUrl() + "/isgw"
}

func BoomTOriginReferer(rawURL string) (TOriginReferer, bool) {
	// 解析 URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		stdlog.WithError(err).Warnf("invalid t origin referer url: %s", rawURL)
		return TOriginReferer{Raw: rawURL}, false
	}

	// 去掉查询参数和 Fragment
	path := parsedURL.Path

	// 执行正则匹配
	matches := originReferPattern.FindStringSubmatch(path)
	if matches == nil {
		stdlog.Warnf("t origin referer pattern not matched regex: %s", rawURL)
		return TOriginReferer{Raw: rawURL}, false
	}

	// 获取命名分组索引
	groupNames := originReferPattern.SubexpNames()
	info := TOriginReferer{
		Schema: parsedURL.Scheme,
		Host:   parsedURL.Host,
		Raw:    rawURL,
	}

	for i, name := range groupNames {
		if i > 0 && name != "" {
			switch name {
			// 以下魔法值请与 originReferPattern 中一一对应
			case "top_prefix":
				info.TopPrefix = matches[i]
			case "system_ns":
				info.SystemNS = matches[i]
			case "project_id":
				if projID, ok := noneProjIDsMapping[matches[i]]; ok {
					info.ProjectID = projID
					info.NoneProjectIDType = NoneProjectIDType(matches[i])
					info.IsNoneProj = true
				} else {
					info.ProjectID = matches[i]
				}
			case "suffix":
				info.Suffix = matches[i]
			}
		}
	}

	// 处理 suffix 为空的情况
	info.Suffix = strings.TrimPrefix(info.Suffix, "/")
	return info, true
}
