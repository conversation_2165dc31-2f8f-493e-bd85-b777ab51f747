package stdsrv

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	"github.com/patrickmn/go-cache"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdctn"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	CasEndpointEnv     = "CAS_ENDPOINT_ENV"
	DefaultCasEndpoint = "http://autocv-cas-service:80"
	GetProjectApi      = "/api/v1/projmgr/projects"
	GetTenantApi       = "/api/v1/tenants"

	// ugly impl, but ...
	innerToken = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4NzYwMjgyNjQsImlhdCI6MTcyMjQyODI2NCwiaXNzIjoiIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInN1YiI6IiIsInVzZXJuYW1lIjoidGhpbmdlciJ9.U4PVADKz2vt0FkP4XduTyxGe4cmLkWHkCmPe6MArnVIpGBNaI3My9mr63EGxbWWVNypuTJnHXB9H28EJ6tSIAg"

	ProjectIDCookieKey = "PROJECT_ID"
)

var (
	customCasEndpoint = "" // 用户可自行手动指定cas(项目管理服务的) 的链接地址
	customCasToken    = "" // 用户可自行手动指定cas(项目管理服务的) 的访问鉴权Token

)

var (
	httpClient *http.Client
	c          = cache.New(5*time.Second, 10*time.Minute)
)

func init() {
	httpClient = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: time.Second * 5,
	}
}

// SetCasConfig 设置默认调用 cas 服务时所用的 cas 服务地址以及认证token
func SetCasConfig(endpoint, token string) {
	if endpoint != "" {
		customCasEndpoint = endpoint
	}
	if token != "" {
		customCasToken = token
	}
}

// GetProject 获取指定ID的项目， 项目结构体以Map形式返回 // TODO 转为结构返回
func GetProject(projectId, token string) (ret map[string]any, err error) {
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, "get project of '%s'", projectId)
		}
	}()
	if projectId == "" {
		err = stderr.InvalidParam.Error("project id is necessary")
		return
	}
	if token == "" {
		if customCasToken != "" {
			token = customCasToken
		} else {
			token = innerToken
		}
	}
	key := "proj:" + projectId
	if v, ok := c.Get(key); ok {
		return v.(map[string]any), nil
	}

	ret, err = getProject(projectId, token)

	if err == nil {
		c.Set(key, ret, cache.DefaultExpiration)
	}
	return
}

// GetProjectWithoutCache 不使用缓存获取指定ID的项目， 项目结构体以Map形式返回 // TODO 转为结构返回
func GetProjectWithoutCache(projectId, token string) (ret map[string]any, err error) {
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, "get project of '%s'", projectId)
		}
	}()
	if projectId == "" {
		err = stderr.InvalidParam.Error("project id is necessary")
		return
	}
	if token == "" {
		if customCasToken != "" {
			token = customCasToken
		} else {
			token = innerToken
		}
	}
	key := "proj:" + projectId

	ret, err = getProject(projectId, token)

	if err == nil {
		c.Set(key, ret, cache.DefaultExpiration)
	}
	return
}

func getProject(projectId, token string) (ret map[string]any, err error) {
	addr := getCasEndpointAddr()

	url := fmt.Sprintf("%s%s/%s", addr, GetProjectApi, projectId)
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return
	}
	request.Header.Set("Authorization", token)
	response, err := httpClient.Do(request)
	if err != nil {
		return
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return
	}

	if response.StatusCode != http.StatusOK {
		err = fmt.Errorf("status: %s; data: %s", response.Status, string(data))
		return
	}

	err = Unmarshal(data, &ret)
	if err != nil {
		return
	}
	return
}

func GetProjectTenantUid(projectId, token string) (tenantUid string, err error) {
	proj, err := GetProject(projectId, token)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	tenantUid, err = GetTenantUidFromProject(proj)
	if err != nil {
		err = stderr.Trace(err)
	}
	return
}

func GetTenantUidFromProject(proj map[string]any) (tenantUid string, err error) {
	tu, ok := proj["tenant_uid"]
	if !ok {
		return "", stderr.Internal.Error("'tenant_uid' not fount in project fields: %+v", stdctn.Keys(proj))
	}
	tenantUid, ok = tu.(string)
	if !ok {
		return "", stderr.Internal.Error("unknown type of tenant_uid %T, expecting string", tu)
	}
	return
}

func GetProjectTenant(projectId, token string) (tenant *pb.Tenant, err error) {
	tenantUid, err := GetProjectTenantUid(projectId, token)
	if err != nil {
		return
	}
	tenant, err = GetTenant(tenantUid, token)
	return
}

func GetTenant(tenantUid, token string) (tenant *pb.Tenant, err error) {
	key := "tenant:" + tenantUid
	if v, ok := c.Get(key); ok {
		return v.(*pb.Tenant), nil
	}
	tenant, err = getTenant(tenantUid, token)
	if err == nil {
		c.Set(key, tenant, cache.DefaultExpiration)
	}
	return
}

func GetTenantWithoutCache(tenantUid, token string) (tenant *pb.Tenant, err error) {
	key := "tenant:" + tenantUid
	tenant, err = getTenant(tenantUid, token)
	if err == nil {
		c.Set(key, tenant, cache.DefaultExpiration)
	}
	return
}

func getTenant(tenantUid, token string) (tenant *pb.Tenant, err error) {
	addr := getCasEndpointAddr()

	url := fmt.Sprintf("%s%s/%s", addr, GetTenantApi, tenantUid)
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return
	}
	request.Header.Set("Authorization", token)
	response, err := httpClient.Do(request)
	if err != nil {
		return
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return
	}
	tenant = new(pb.Tenant)
	err = Unmarshal(data, tenant)
	return
}

// GetTenantHippo 获取hippo实例的状态
func GetTenantHippo(tenantUid, token string) (ok bool, msg string) {
	addr := getCasEndpointAddr()
	url := fmt.Sprintf("%s%s/%s/hippo", addr, GetTenantApi, tenantUid)
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		msg = stderr.Wrap(err, "failed to create request").Error()
		return
	}

	request.Header.Set("Authorization", token)
	request.Header.Set("content-type", "application/json")
	response, err := httpClient.Do(request)
	if err != nil {
		msg = stderr.Wrap(err, "failed to send request").Error()
		return
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		msg = stderr.Wrap(err, "failed to read response").Error()
		return
	}
	type Instance struct {
		Id     string `json:"id"`
		Name   string `json:"name"`
		Status string `json:"status"`
	}
	instance := new(Instance)
	err = Unmarshal(data, instance)
	if err != nil {
		msg = stderr.Wrap(err, "failed to unmarshal response").Error()
		return
	}
	if strings.ToLower(instance.Status) == "running" {
		return true, ""
	} else {
		msg = fmt.Sprintf("hippo instance %s is %s", instance.Name, instance.Status)
	}
	return
}

// Project 为项目本身的信息 // TODO 转换为结构体定义
type Project map[string]any

// ProjectInfo 为通过HTTP请求查询到的LLMOps项目空间的基本信息
// * 要求请求中必须携带 ProjectIDQueryParam 定义的请求参数, 即 project_id
type ProjectInfo struct {
	ProjectID string         // ProjectID 为项目的唯一标识
	TenantID  string         // TenantID 为项目空间中任务负载所用的资源池关联的租户ID（等价与K8S Namespace）
	Tenant    *pb.Tenant     // Tenant 为项目空间关联租户的详细信息
	Project   map[string]any // Project 为项目本身的信息 // TODO 转换为结构体定义
}

func (p *ProjectInfo) ProjectRoot(createIfNotExist bool) (stdfs.RelativeFilePath, error) {
	return stdfs.NewProjectLocation(p.TenantID, p.ProjectID, createIfNotExist)
}

func getProjIDFromHeader(r *restful.Request) (string, bool) {
	referer := r.HeaderParameter(TranswarpCustomHeaderReferer)
	if referer != "" {
		return ExtractProjectIDFromReferer(referer)
	}
	return "", false
}

// GetProjectInfoFromRequest 直接尝试基于默认的配置，从请求中获取project_id，并关联查询相关的项目&租户信息
func GetProjectInfoFromRequest(r *restful.Request) (ret *ProjectInfo, err error) {
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, "get project info from request")
		}
	}()

	if r == nil {
		err = stderr.InvalidParam.Errorf("request can not be nil")
		return
	}
	var projID string
	// try get proj id from http headers
	if pid, ok := getProjIDFromHeader(r); ok {
		projID = pid
	}

	if projID == "" {
		projID = ProjectIDQueryParam.GetValue(r)
	}
	if projID == "" {
		err = stderr.InvalidParam.Errorf("project id not fount in header(T-Origin-Referer) or query param(project_id)")
		return
	}

	token := innerToken
	if cct := customCasToken; cct != "" {
		stdlog.Debugf("use costom cas token: %s", cct)
		token = cct
	}

	proj, err := GetProject(projID, token)
	if err != nil {
		return
	}
	tid, err := GetTenantUidFromProject(proj)
	if err != nil {
		return
	}
	tenant, err := GetTenant(tid, token)
	if err != nil {
		return
	}
	return &ProjectInfo{
		ProjectID: projID,
		TenantID:  tid,
		Tenant:    tenant,
		Project:   proj,
	}, nil
}

func getCasEndpointAddr() string {
	if os.Getenv(CasEndpointEnv) != "" {
		return os.Getenv(CasEndpointEnv)
	}
	if customCasEndpoint != "" {
		return customCasEndpoint
	}
	return DefaultCasEndpoint
}

func NewProjectCookie(projID string) *http.Cookie {
	return &http.Cookie{
		Name:     ProjectIDCookieKey,
		Value:    projID,
		Path:     "/",
		Expires:  time.Now().Add(24 * time.Hour),
		MaxAge:   int(24 * time.Hour.Seconds()),
		Secure:   true,
		HttpOnly: true,
		SameSite: http.SameSiteNoneMode,
	}
}
