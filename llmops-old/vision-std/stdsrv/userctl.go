package stdsrv

import (
	"net/http"
	"os"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	UserTypeUser        = "user"
	UserTypeUserGroup   = "user_group"
	HeaderAuthorization = "Authorization"
	GroupsUrl           = "http://autocv-cas-service/api/v1/usermgr/groups"
	ENVCAS              = "CAS"
)

type Members []Member

type Member struct {
	Id       string `json:"id"  description:"用户或用户组名称"`
	UserType string `json:"user_type"  description:"类型"`
}

type Groups []Group
type Group struct {
	Gid              int64     `json:"gid"`
	Name             string    `json:"name"`
	PlatformRoleName string    `json:"platform_role_name"`
	PlatformRoleId   int64     `json:"platform_role_id"`
	Description      string    `json:"description"`
	UserNames        []string  `json:"user_names"`
	CreateUser       string    `json:"create_user"`
	CreateTime       time.Time `json:"create_time"`
}

// BelongTo 判断用户id是否属于member, gMap组别信息
func BelongTo(userId string, members []Member, gMap map[string]Group) bool {
	if len(members) == 0 {
		return true
	}
	for _, m := range members {
		// 通过用户id匹配
		if m.UserType == UserTypeUser && m.Id == userId {
			return true
		}

		// 通过用户组匹配
		if m.UserType == UserTypeUserGroup {
			group, ok := gMap[m.Id]
			if !ok {
				continue
			}
			for _, cName := range group.UserNames {
				if cName == userId {
					return true
				}
			}
		}
	}
	return false
}

// GetGroupsMap 组名 -->> 用户组具体信息
func GetGroupsMap(token string) (map[string]Group, error) {
	fullUrl := GroupsUrl
	cas := os.Getenv(ENVCAS)
	if cas != "" {
		fullUrl = strings.ReplaceAll(fullUrl, "autocv-cas-service", cas)
	}

	callConf := &CallConfig{
		Method:   http.MethodGet,
		FullUrl:  fullUrl,
		Headers:  map[string]string{HeaderAuthorization: token},
		InferReq: nil,
	}
	groups, err := httpSyncCallV2[Groups](new(http.Client), callConf)
	if err != nil {
		return nil, stderr.Wrap(err, "fail to get groups map")
	}
	gMap := make(map[string]Group)
	for _, g := range *groups {
		gMap[g.Name] = g
	}
	return gMap, nil
}
