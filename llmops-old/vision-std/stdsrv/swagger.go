package stdsrv

import (
	"net/http"
	"unicode"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// 获取swagger API definition的RouteFunction， 可以避免文档的数据模型显示无关的pb字段。
// 用法：api.Route(api.GET("/swagger.json").To(GetSwagger))
func GetSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices: restful.RegisteredWebServices(),
		SchemaFormatHandler: func(typeName string) string {
			return typeName
		},
		DefinitionNameHandler: func(s string) string {
			if len(s) > 0 && unicode.IsUpper(rune(s[0])) {
				return s
			}
			return ""
		},
	}
	swagger := restfulspec.BuildSwagger(config)
	swagger.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Description: "API Docs for SOPHON Vision",
			Title:       "Vision API DOCS",
			Version:     "1.0.0",
		},
	}
	swagger.Schemes = []string{"http", "https"}
	if err := response.WriteHeaderAndEntity(http.StatusOK, swagger); err != nil {
		stdlog.WithError(err).Error("write response")
	}
}
