package stdsrv

import (
	"errors"
	"net/http"

	"github.com/emicklei/go-restful/v3"
)

type Language string

const (
	// curl 'https://172.17.120.207:30745/llm/dev/tenants/dev-assets/gateway/applet/api/v1/portal-info/app/stats?project_id=assets&tenantId=dev-assets' \
	// -H 'accept: application/json' \
	// -H 'accept-language: zh,en-US;q=0.9,en;q=0.8,zh-CN;q=0.7,eo;q=0.6' \
	// -H 'cookie: PROJECT_ID=assets; grafana_session=2cc2dc8754a5880e2073c04d916a905e; SOPHONID=1863bd08c448770340c71a7323ed3c2a; debug=1; lang=en' \
	// -H 'sophonlocale: zh' \
	// --insecure
	languageCookieKey          = "lang"         // cookie 中带的语言, 界面上调整后即时生效, 优先使用该值
	languageHeaderKey          = "sophonlocale" // Header中传输的选择的语言, 有一定滞后性, 页面刷新后才会生效
	LanguageChinese   Language = "zh"
	LanguageEnglish   Language = "en"
	defaultLanguage            = LanguageChinese
)

func (l Language) String() string {
	return string(l)
}
func (l Language) IsChinese() bool {
	return l == "" || l == LanguageChinese
}
func (l Language) IsEnglish() bool {
	return l == LanguageEnglish
}

func UserEnglishLanguage(r *restful.Request) bool {
	return GetLanguage(r).IsEnglish()
}

func GetLanguage(r *restful.Request) Language {
	l, ok := GetLanguageFromCookie(r)
	if ok {
		return l
	}

	l, ok = GetLanguageFromHeader(r)
	if ok {
		return l
	}

	return defaultLanguage
}
func GetLanguageFromCookie(r *restful.Request) (Language, bool) {
	if r == nil || r.Request == nil {
		return defaultLanguage, false
	}
	c, err := r.Request.Cookie(languageCookieKey)
	if err != nil && errors.Is(err, http.ErrNoCookie) {
		return defaultLanguage, false
	}
	if err != nil {
		// never shouldn't happen
		return defaultLanguage, false
	}
	return Language(c.Value), true
}

func GetLanguageFromHeader(r *restful.Request) (Language, bool) {
	if r == nil || r.Request == nil {
		return defaultLanguage, false
	}
	v := r.HeaderParameter(languageHeaderKey)
	if v == "" {
		return defaultLanguage, false
	}
	return Language(v), true
}
