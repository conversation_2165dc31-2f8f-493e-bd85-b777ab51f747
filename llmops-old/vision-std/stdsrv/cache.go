package stdsrv

import (
	"github.com/patrickmn/go-cache"
	"time"
)

type stdCache[T any] struct {
	cache        *cache.Cache
	defaultExpir time.Duration // 默认过期时间
}

func NewStdCache[T any](defaultExpir, cleanupInterval time.Duration) *stdCache[T] {
	return &stdCache[T]{
		cache:        cache.New(defaultExpir, cleanupInterval),
		defaultExpir: defaultExpir,
	}
}

func (s *stdCache[T]) Get(key string) (T, bool) {
	var zero T
	value, found := s.cache.Get(key)
	if !found {
		return zero, false
	}
	valueT, ok := value.(T)
	if !ok {
		return zero, false
	}
	s.Set(key, valueT) // 使用后重新续期
	return valueT, true
}

func (s *stdCache[T]) Set(key string, value T) {
	s.cache.Set(key, value, s.defaultExpir)
}

//	func (s *stdCache[T]) SetWithExpir(key string, value T, expiration time.Duration) {
//		s.cache.Set(key, value, expiration)
//	}
func (s *stdCache[T]) Delete(key string) {
	s.cache.Delete(key)
}
