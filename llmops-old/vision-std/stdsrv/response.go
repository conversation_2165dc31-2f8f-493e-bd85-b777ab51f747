package stdsrv

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

var (
	EmptyRsp = struct{}{}
)

// WriteFileResponse 将文件写入到Http的响应体附件中, 触发浏览器的下载事件
func WriteFileResponse(response *restful.Response, fileName string, src io.Reader) error {
	WriteFileHeader(response, fileName)
	if _, err := io.Copy(response.ResponseWriter, src); err != nil {
		return err
	}
	return nil
}

// NonStreamWriteFileResponse 将文件写入到Http的响应体附件中, 触发浏览器的下载事件
func NonStreamWriteFileResponse(response *restful.Response, fileName string, data []byte) error {
	WriteFileHeader(response, fileName)
	WriteFileLengthHeader(response, len(data))
	if _, err := response.ResponseWriter.Write(data); err != nil {
		return err
	}
	return nil
}

// WriteFileHeader 设置返回附件文件的响应头
func WriteFileHeader(response *restful.Response, fileName string) {
	response.Header().Add("Content-Type", "application/octet-stream")
	response.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename=%s`, url.QueryEscape(fileName)))
	response.Header().Add("File-Name", url.QueryEscape(fileName))
}

// WriteFileLengthHeader 设置返回附件文件的长度
func WriteFileLengthHeader(response *restful.Response, length int) {
	response.Header().Add("Content-Length", strconv.Itoa(length))
}

func SuccessResponse(response *restful.Response, value interface{}) {
	err := response.WriteHeaderAndEntity(http.StatusOK, value)
	if err != nil {
		stdlog.WithError(err).Errorf("restful: error while writing header and entity")
	}
}

func ErrorResponse(response *restful.Response, e error) {
	httpCode, ge := BuildErrorResponse(e)
	if ge != nil {
		stdlog.WithError(e).Errorf("API Call Error")
	}
	response.WriteError(httpCode, ge)
}

func BuildErrorResponse(e error) (int, *stderr.GatewayError) {
	se := stderr.Unwrap(e)
	return stderr.GetCode(se.Code).HttpCode, &stderr.GatewayError{
		Tag:      se.Tag,
		Code:     se.Code,
		Redirect: se.Redirect,
		Msg:      se.Message(),
		Detail:   se.Message() + "\n" + se.Stack(),
	}
}

func HandleErr(response *restful.Response, err error) {
	if err != nil {
		ErrorResponse(response, err)
		return
	}
}

func ReadEntityMixWithProto(req *restful.Request, structPtr any) error {
	bytes, err := io.ReadAll(req.Request.Body)
	if err != nil {
		return err
	}
	return UnmarshalMixWithProto(string(bytes), structPtr)
}

func SuccessResponseMixWithProto(resp *restful.Response, structPtr any) {
	var bytes []byte
	var err error
	if marshaler, ok := structPtr.(StdMarshaler); ok {
		bytes, err = marshaler.StdMarshal()
	} else {
		bytes, err = MarshalMixWithProto(structPtr)
	}
	if err != nil {
		ErrorResponse(resp, err)
	} else {
		resp.Header().Set(restful.HEADER_ContentType, restful.MIME_JSON)
		resp.WriteHeader(http.StatusOK)
		_, err := resp.Write(bytes)
		if err != nil {
			stdlog.Info(err)
		}
	}
}
