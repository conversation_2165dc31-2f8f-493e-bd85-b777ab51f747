package stdsrv

import (
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// AsyncBatchCall 使用给定的处理函数处理传入的所有ID, 若在其中发生错误,则仅打印日志
// 函数返回时, 所有调用协程并未完成
func AsyncBatchCall(ids []string, call func(string) error) {
	for _, id := range ids {
		go func(id string) {
			err := call(id)
			if err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%s'", id)
			}
		}(id)
	}
}

// SyncBatchCall 使用给定的处理函数处理传入的所有ID, 若在其中发生错误,则直接返回
// 当所有调用协程完成后,该函数才会返回
func SyncBatchCall(ids []string, call func(string) error) error {
	wg := sync.WaitGroup{}
	wg.Add(len(ids))
	errs := make([]error, 0, len(ids))
	for _, id := range ids {
		go func(id string) {
			defer wg.Done()
			if err := call(id); err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%s'", id)
				errs = append(errs, err)
			}
		}(id)
	}
	// 等待所有调用协程执行完毕
	wg.Wait()
	return stderr.JoinErrors(errs...)
}

func SyncBatchCallGeneric[T any](data []T, call func(T) error) error {
	return SyncBatchCallGenericWithIdx(data, func(i int, item T) error {
		return call(item)
	})
}

func SyncBatchCallGenericWithIdx[T any](data []T, call func(int, T) error) error {
	wg := sync.WaitGroup{}
	wg.Add(len(data))
	errs := make([]error, 0, len(data))
	for i, item := range data {
		go func(i int,item T) {
			defer wg.Done()
			if err := call(i,item); err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%v'", item)
				errs = append(errs, err)
			}
		}(i, item)
	}
	// 等待所有调用协程执行完毕
	wg.Wait()
	return stderr.JoinErrors(errs...)
}

// SyncBatchCallWithMaxConcurrency 使用给定的处理函数处理传入的所有ID
// 过程中最多同时有给定的 maxConcurrency 个协程在同时运行
// 当所有调用协程完成后,该函数才会返回
func SyncBatchCallWithMaxConcurrency(ids []string, maxConcurrency int, call func(string) error) (rsp BatchOptRsp) {
	wg := sync.WaitGroup{}
	wg.Add(len(ids))
	rsp.Total = len(ids)
	rsp.BeginAt = time.Now()
	pool := make(chan struct{}, maxConcurrency)
	for i := 0; i < maxConcurrency; i++ {
		pool <- struct{}{}
	}
	errMsgs := make([]string, 0)
	for _, id := range ids {
		_, ok := <-pool // apply a new available goroutine
		if !ok {
			stdlog.Warnf("goroutine pool closed")
			break
		}
		go func(id string) {
			defer func() {
				pool <- struct{}{} // release a goroutine back to pool
				wg.Done()
			}()

			if err := call(id); err != nil {
				stdlog.WithError(err).Errorf("failed to do call with id '%s'", id)
				errMsgs = append(errMsgs, stderr.Unwrap(err).Message())
			}
		}(id)
	}
	// 等待所有调用协程执行完毕
	wg.Wait()
	rsp.EndAt = time.Now()
	rsp.TimeCost = rsp.EndAt.Sub(rsp.BeginAt)
	rsp.Failure = len(errMsgs)
	rsp.Success = rsp.Total - rsp.Failure
	rsp.ErrMsgs = errMsgs
	close(pool)
	return
}

type BatchOptRsp struct {
	Total    int           `json:"total,omitempty"`
	Success  int           `json:"success,omitempty"`
	Failure  int           `json:"failure,omitempty"`
	ErrMsgs  []string      `json:"err_msgs,omitempty"`
	BeginAt  time.Time     `json:"begin_at"`
	EndAt    time.Time     `json:"end_at"`
	TimeCost time.Duration `json:"time_cost,omitempty"`
}
