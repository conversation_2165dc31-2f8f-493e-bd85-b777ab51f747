package stdsrv

import (
	"bufio"
	"bytes"
	"context"
	"net/http"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func call(url string, headers map[string]string, body string) (*http.Response, error) {
	request, err := http.NewRequest("POST", url, bytes.NewReader([]byte(body)))
	if err != nil {
		return nil, stderr.Wrap(err, "new request")
	}
	request.Header.Add("Content-Type", "application/json")
	for k, v := range headers {
		request.Header.Add(k, v)
	}
	client := &http.Client{}
	return client.Do(request)
}
func NNDelimiter() (*http.Response, error) {
	var body = `
		{
		"messages": [
			{
				"content": "你好",
				"role": "user"
			}
		],
		"model": "atom",
		"stream": true,
		"temperature": 0.01
		}`
	return call("http://172.17.120.11:8011/openai/v1/chat/completions", nil, body)
}
func RNRNDelimiter() (*http.Response, error) {
	var body = `
	{
	"messages": [
		{
			"content": "你好",
			"role": "user"
		}
	],
	"model": "deepseek",
	"stream": true,
	"temperature": 0.01
	}`
	return call("https://esop.gtja.com/dsllm/v1/chat/completions", map[string]string{"X-Sys-Id": "aicredit"}, body)
}
func TestSSEReadEvent(t *testing.T) {
	nn, err := NNDelimiter()
	if err != nil {
		t.Log(err)
		t.Fail()
		return
	}
	defer nn.Body.Close()
	reader := bufio.NewReader(nn.Body)
	err = SSEReadEvent(context.Background(), reader, func(event SSEEvent) error {
		// stdlog.Infof("event: %v", event)
		return nil
	})
	if err != nil {
		t.Log(err)
		t.Fail()
		return
	}
	stdlog.Infof("###rnrn####")
	rnrn, err := RNRNDelimiter()
	if err != nil {
		t.Log(err)
		t.Fail()
		return
	}
	defer rnrn.Body.Close()
	reader = bufio.NewReader(rnrn.Body)
	err = SSEReadEvent(context.Background(), reader, func(event SSEEvent) error {
		stdlog.Infof("event: %v", event)
		return nil
	})
	if err != nil {
		t.Log(err)
		t.Fail()
		return
	}
}
