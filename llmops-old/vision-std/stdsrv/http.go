package stdsrv

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type CallConfig struct {
	Method   string
	FullUrl  string
	Headers  map[string]string
	InferReq any
}

func httpSyncCall(client *http.Client, callConfig *CallConfig) ([]byte, error) {
	ctx := context.Background()
	req, err := http.NewRequestWithContext(ctx, callConfig.Method, callConfig.FullUrl,
		bytes.NewBufferString(AnyToString(callConfig.InferReq)))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	for k, v := range callConfig.Headers {
		req.Header.Set(k, v)
	}
	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, stderr.Internal.Error("http code not 200, resp:[%v]", resp)
	}
	return body, nil
}

func httpSyncCallV2[T any](client *http.Client, callConfig *CallConfig) (*T, error) {
	body, err := httpSyncCall(client, callConfig)
	if err != nil {
		return nil, err
	}
	resp := new(T)
	if err := Unmarshal(body, resp); err != nil {
		stderr.Wrap(err, "fail to do Unmarshal, the body is %s", string(body))
	}
	return resp, nil
}
