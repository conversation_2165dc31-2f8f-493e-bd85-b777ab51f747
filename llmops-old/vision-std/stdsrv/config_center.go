package stdsrv

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	GetConfigDefinesApi   = "/api/v1/custom/config/defines"
	GetPutConfigValuesApi = "/api/v1/custom/config/values"
)

type ConfigCenterOptions func(c *ConfigCenter)

func WithToken(token string) ConfigCenterOptions {
	return func(c *ConfigCenter) {
		c.token = token
	}
}
func WithEndpoint(endpoint string) ConfigCenterOptions {
	return func(c *ConfigCenter) {
		c.endpoint = endpoint
	}
}
func WithTimeout(timeout time.Duration) ConfigCenterOptions {
	return func(c *ConfigCenter) {
		c.timeout = timeout
	}
}

type ConfigCenter struct {
	endpoint string
	token    string
	client   *http.Client
	timeout  time.Duration
}

func NewConfigCenter(opts ...ConfigCenterOptions) *ConfigCenter {
	endpoint := getCasEndpointAddr()
	cc := &ConfigCenter{
		endpoint: endpoint,
		token:    innerToken,
		client:   httpClient,
		timeout:  30 * time.Second,
	}
	for _, opt := range opts {
		opt(cc)
	}
	return cc
}

func (cc *ConfigCenter) setRequest(ctx context.Context, method string, api string, body io.Reader) (*http.Request, error) {
	ru, err := url.Parse(cc.endpoint)
	if err != nil {
		return nil, stderr.Wrap(err, "illegal endpoint address %s", cc.endpoint)
	}
	u := url.URL{
		Scheme: ru.Scheme,
		Host:   ru.Host,
		Path:   api,
	}
	request, err := http.NewRequestWithContext(ctx, method, u.String(), body)
	if err != nil {
		return nil, stderr.Wrap(err, "when constructing request")
	}
	request.Header.Set("Authorization", cc.token)
	request.Header.Set("Content-Type", "application/json")
	return request, nil
}

func (cc *ConfigCenter) GetConfigDefines(ctx context.Context) ([]map[string]any, error) {
	nctx, cancel := context.WithTimeout(ctx, cc.timeout)
	defer cancel()
	request, err := cc.setRequest(nctx, http.MethodGet, GetConfigDefinesApi, nil)
	if err != nil {
		return nil, stderr.Wrap(err, "set get config defines request")
	}
	rsp, err := cc.client.Do(request)
	if err != nil {
		return nil, stderr.Wrap(err, "do get config defines request")
	}
	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read get config defines response")
	}
	if rsp.StatusCode != 200 {
		return nil, stderr.Internal.Errorf("failed to get config defines %s", data)
	}
	// cas那边定义的返回结构是[]*pb.DynamicParam 并且其中enum编成了string，不好解析
	defines := make([]map[string]any, 0)
	err = Unmarshal(data, &defines)
	if err != nil {
		return nil, stderr.Wrap(err, "unmarshal config defines %s", data)
	}
	return defines, nil
}

type customConfig struct {
	Id       int                    `json:"id,omitempty" yaml:"id"`
	CreateAt time.Time              `json:"create_at,omitempty" yaml:"create_at"`
	UpdateAt time.Time              `json:"update_at,omitempty" yaml:"update_at"`
	DeleteAt time.Time              `json:"delete_at,omitempty" yaml:"delete_at"`
	System   bool                   `json:"system,omitempty" yaml:"system"`
	Value    map[string]interface{} `json:"value,omitempty" yaml:"value"`
	UpdateBy string                 `json:"update_by,omitempty" yaml:"update_by"`
}

type customLogo struct {
	Id       int       `json:"id,omitempty" yaml:"id"`
	CreateAt time.Time `json:"create_at,omitempty" yaml:"create_at"`
	UpdateAt time.Time `json:"update_at,omitempty" yaml:"update_at"`
	DeleteAt time.Time `json:"delete_at,omitempty" yaml:"delete_at"`
	System   bool      `json:"system,omitempty" yaml:"system"`
	Default  int       `json:"default,omitempty" yaml:"default"`
	Path     string    `json:"path,omitempty" yaml:"path"`
	UpdateBy string    `json:"update_by,omitempty" yaml:"update_by"`
}

type GetConfigValuesRsp struct {
	Logo   customLogo   `json:"logo,omitempty" yaml:"logo"`
	Config customConfig `json:"config,omitempty" yaml:"config"`
}

func (cc *ConfigCenter) GetConfigsValues(ctx context.Context) (*GetConfigValuesRsp, error) {
	nctx, cancel := context.WithTimeout(ctx, cc.timeout)
	defer cancel()
	request, err := cc.setRequest(nctx, http.MethodGet, GetPutConfigValuesApi, nil)
	if err != nil {
		return nil, stderr.Wrap(err, "set get config values request")
	}
	rsp, err := cc.client.Do(request)
	if err != nil {
		return nil, stderr.Wrap(err, "do get config values request")
	}
	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read get config values response")
	}
	if rsp.StatusCode != 200 {
		return nil, stderr.Internal.Errorf("failed to get config values %s", data)
	}
	configValues := new(GetConfigValuesRsp)
	err = Unmarshal(data, configValues)
	if err != nil {
		return nil, stderr.Wrap(err, "unmarshal config values %s", data)
	}
	return configValues, nil
}

type UpdateConfigValuesReq struct {
	customConfig
}

func (cc *ConfigCenter) PutConfigValues(ctx context.Context, req *UpdateConfigValuesReq) error {
	nctx, cancel := context.WithTimeout(ctx, cc.timeout)
	defer cancel()
	payload, err := Marshal(req)
	if err != nil {
		return stderr.Wrap(err, "marshal request payload %v", req)
	}
	request, err := cc.setRequest(nctx, http.MethodPut, GetPutConfigValuesApi, bytes.NewReader(payload))
	if err != nil {
		return stderr.Wrap(err, "set put config values request")
	}
	rsp, err := cc.client.Do(request)
	if err != nil {
		return stderr.Wrap(err, "put config values request")
	}
	if rsp.StatusCode != 200 {
		data, _ := io.ReadAll(rsp.Body)
		return stderr.Internal.Errorf("failed to put config values %s", data)
	}
	return nil
}
