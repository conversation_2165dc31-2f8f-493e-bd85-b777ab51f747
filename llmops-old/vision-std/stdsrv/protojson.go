package stdsrv

import (
	"github.com/emicklei/go-restful/v3"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"io"
	"reflect"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

var (
	protoJsonAccessor = &ProtoJsonAccessor{
		MarshalOptions: protojson.MarshalOptions{
			Multiline:       true,
			Indent:          "  ",
			AllowPartial:    true,
			UseProtoNames:   true,
			UseEnumNumbers:  false,
			EmitUnpopulated: true,
		},
		UnmarshalOptions: protojson.UnmarshalOptions{
			AllowPartial:   true,
			DiscardUnknown: true,
		},
	}
)

const (
	JsonTag             = "json"
	ActionTypeMarshal   = "Marshal"
	ActionTypeUnmarshal = "Unmarshal"
)

type ProtoJsonAccessor struct {
	protojson.MarshalOptions
	protojson.UnmarshalOptions
}

func (p *ProtoJsonAccessor) Read(req *restful.Request, v interface{}) error {
	if req == nil || req.Request == nil || req.Request.Body == nil {
		return stderr.InvalidParam.Error("body in request is nil")
	}
	bs, err := io.ReadAll(req.Request.Body)
	if err != nil {
		return stderr.Internal.Cause(err, "read request body")
	}
	if msg, ok := v.(proto.Message); ok {
		if err := p.Unmarshal(bs, msg); err != nil {
			return stderr.Wrap(err, "unmarshal value with protojson when parsing request")
		}
		return nil
	}

	if err := Unmarshal(bs, v); err != nil {
		return stderr.Wrap(err, "unmarshal value with built-in json when parsing request")
	}
	return nil
}

func (p *ProtoJsonAccessor) Write(resp *restful.Response, status int, v interface{}) error {

	// write marshalls the value to JSON and set the Content-Type Header.
	if v == nil {
		resp.WriteHeader(status)
		// do not write a nil representation
		return nil
	}

	var bs []byte
	var err error
	if msg, ok := v.(proto.Message); ok {
		bs, err = p.Marshal(msg)
		if err != nil {
			return stderr.Wrap(err, "marshal value with protojson")
		}
	} else {
		bs, err = Marshal(v)
		if err != nil {
			return stderr.Wrap(err, "marshal value with buit-in json")
		}
	}

	resp.Header().Set(restful.HEADER_ContentType, restful.MIME_JSON)
	resp.WriteHeader(status)
	_, err = resp.Write(bs)
	return err
}

// DefaultProtoJsonAccessor
// 获取默认配置的protoJsonAccessor
// 用于替代原框架默认的 built-in json解析器， 优先使用 protojson 解析 proto.Message
// restful.RegisterEntityAccessor(restful.MIME_JSON, protoJsonAccessor)
func DefaultProtoJsonAccessor() *ProtoJsonAccessor {
	return protoJsonAccessor
}

// UnmarshalMixWithProto
// structOrJsonStr 混合了pb结构的结构体对象、指针、或者相应的jsonStr
// structPtr 传入结构体的指针,或者切片的指针,存储解析结果。不能为nil
// 结构体内部支持切片、结构体、指针,以及嵌套等
func UnmarshalMixWithProto(structOrJsonStr any, structPtr any) error {
	if IsNilPointer(structOrJsonStr) || IsNilPointer(structPtr) {
		return stderr.Error("exist param that is nil pointer")
	}

	bytes := AnyToBytes(structOrJsonStr)
	// 1、内部混合proto  可能由于json结构体不匹配或pb枚举值导致error,暂时不处理,
	if err := Unmarshal(bytes, structPtr); err != nil {
		stdlog.Debug(err)
	}
	var values interface{}
	// 2、备份一份到map中,
	if isSlicePointer(structPtr) { // *[]
		// 2.1 切片的指针
		structPtr = getSliceFromSlicePointer(structPtr)
		values = make([]map[string]interface{}, 0)
	} else {
		// 2.2 结构体的指针
		values = make(map[string]interface{})
	}
	if err := Unmarshal(bytes, &values); err != nil {
		return err
	}
	// 3、protojson.Unmarshal 对proto结构体进一步处理
	errs := make([]error, 0)
	handlerProtoField(ActionTypeUnmarshal, structPtr, values, &errs)
	return stderr.JoinErrors(errs...)
}

// MarshalMixWithProto
// structOrPtr 需要进行序列化的结构体对象、或相应的指针，切片、或相应的指针
func MarshalMixWithProto(structOrPtr any) ([]byte, error) {
	if IsNilPointer(structOrPtr) {
		return nil, stderr.Errorf("the param is nil pointer")
	}
	var structPtr any
	var values any
	if isSlice(structOrPtr) { // []
		structPtr = structOrPtr
		values = make([]map[string]interface{}, 0)
	} else if isSlicePointer(structOrPtr) { //  *[]
		structPtr = getSliceFromSlicePointer(structOrPtr)
		values = make([]map[string]interface{}, 0)
	} else {
		structPtr = GetPtrOfStruct(structOrPtr)
		values = make(map[string]interface{})
	}
	bytes := AnyToBytes(structPtr)
	if err := Unmarshal(bytes, &values); err != nil {
		return nil, err
	}
	errs := make([]error, 0)
	handlerProtoField(ActionTypeMarshal, structPtr, values, &errs)
	if err := stderr.JoinErrors(errs...); err != nil {
		return nil, err
	}
	return Marshal(values)
}

// handlerProtoField 处理proto字段
// structPtr 指针、map、或切片
func handlerProtoField(actionType string, structPtr any, values any, errsPtr *[]error) {
	if isProtoMessage(structPtr) {
		valuesMap, ok := values.(map[string]interface{})
		if !ok {
			return
		}
		switch actionType {
		case ActionTypeUnmarshal:
			// pb结构体,存在枚举值需要额外解析
			bytes := AnyToBytes(valuesMap)
			if err := DefaultProtoJsonAccessor().Unmarshal(bytes, structPtr.(proto.Message)); err != nil {
				*errsPtr = append(*errsPtr, stderr.Wrap(err, "failed to do proto.unmarshal"))
			}
			return
		case ActionTypeMarshal:
			bytes, err := DefaultProtoJsonAccessor().Marshal(structPtr.(proto.Message))
			if err != nil {
				*errsPtr = append(*errsPtr, stderr.Wrap(err, "failed to do proto.marshal"))
			}
			if err := Unmarshal(bytes, &valuesMap); err != nil {
				*errsPtr = append(*errsPtr, stderr.Wrap(err, "failed to do unmarshal"))
			}
			return
		}
	}

	// 1、特殊情况:切片嵌套切片,最外层为切片
	structRefV := reflect.ValueOf(structPtr)
	if structRefV.Kind() == reflect.Slice {
		fieldPtr := structRefV.Interface()
		nextV, ok := values.([]interface{})
		if !ok {
			return
		}
		// 遍历切片
		sliceRefV := reflect.ValueOf(fieldPtr)
		for j := 0; j < sliceRefV.Len(); j++ {
			// 获取切片的第j个元素的值
			elem := sliceRefV.Index(j)
			handlerProtoField(actionType, getPtrOfValue(elem), nextV[j], errsPtr)
		}
	} else if structRefV.Kind() == reflect.Map {
		// 1.1、特殊情况 map[string]interface
		cMap, ok := values.(map[string]interface{})
		if !ok {
			return
		}
		keys := structRefV.MapKeys()
		for _, key := range keys {
			strKey := key.String()
			value := structRefV.MapIndex(key)
			// 基本数据类型,跳过
			if isBasicType(value) {
				continue
			}
			handlerProtoField(actionType, getPtrOfValue(value), cMap[strKey], errsPtr)
		}
	} else {
		// 2、一般情况,内部存在字段、属性
		valuesMap, ok := values.(map[string]interface{})
		if !ok {
			return
		}
		refT := reflect.TypeOf(structPtr).Elem()
		refV := reflect.ValueOf(structPtr).Elem()
		for i := 0; i < refT.NumField(); i++ {
			cFiledT := refT.Field(i)
			cFiledV := refV.Field(i)
			// 获取结构体字段JSON标签值中的name
			cFieldName := getJsonTagName(cFiledT)
			if cFieldName == "" {
				// 2.1、如果没有配jsonTag,并且为匿名字段(继承的结构体),需要特殊处理
				if cFiledT.Anonymous {
					// 对匿名字段递归处理
					handlerProtoField(actionType, getPtrOfValue(cFiledV), valuesMap, errsPtr)
					continue
				}
				// 2.2、 如果没有配jsonTag,并且为普通字段,使用字段名替换jsonTag
				cFieldName = cFiledT.Name
			}

			if isExistValidKey(valuesMap, cFieldName) {
				// 根据成员类型进行处理
				switch cFiledV.Kind() {
				case reflect.Ptr:
					// 指针
					handlerProtoField(actionType, getPtrOfValue(cFiledV), valuesMap[cFieldName], errsPtr)
				case reflect.Struct:
					// 结构体
					handlerProtoField(actionType, getPtrOfValue(cFiledV), valuesMap[cFieldName], errsPtr)
				case reflect.Slice:
					// 切片
					handlerProtoField(actionType, getPtrOfValue(cFiledV), valuesMap[cFieldName], errsPtr)
				case reflect.Map:
					// map
					handlerProtoField(actionType, getPtrOfValue(cFiledV), valuesMap[cFieldName], errsPtr)
				}
			}
		}
	}
}

func isProtoMessage(v interface{}) bool {
	if _, ok := v.(proto.Message); ok {
		return true
	}
	return false
}
func isExistValidKey(valuesMap map[string]interface{}, key string) bool {
	if _, ok := valuesMap[key]; ok {
		if valuesMap[key] != nil && string(AnyToBytes(valuesMap[key])) != "" {
			return true
		}
		return false
	}
	return false
}
func getPtrOfValue(value reflect.Value) any {
	switch value.Kind() {
	case reflect.Ptr:
		// 某些情况下可能为nil
		if value.IsNil() {
			newValue := reflect.New(value.Type().Elem())
			value.Set(newValue)
		}
		return value.Interface()
	case reflect.Slice:
		return value.Interface()
	case reflect.Map:
		return value.Interface()
	default:
		if !value.CanAddr() {
            // 对于不可寻址的值，创建新的可寻址值
            newValue := reflect.New(value.Type())
            newValue.Elem().Set(value)
            return newValue.Interface()
        }
		return value.Addr().Interface()
	}
}

func isSlicePointer(v any) bool {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr {
		return false
	}
	elemType := rv.Elem().Type()
	return elemType.Kind() == reflect.Slice
}

func isMapPointer(v any) bool {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr {
		return false
	}
	elemType := rv.Elem().Type()
	return elemType.Kind() == reflect.Map
}

func isSlice(v any) bool {
	rv := reflect.ValueOf(v)
	return rv.Kind() == reflect.Slice
}
func isMap(v any) bool {
	rv := reflect.ValueOf(v)
	return rv.Kind() == reflect.Map
}
func getSliceFromSlicePointer(structPtr any) any {
	refV := reflect.ValueOf(structPtr)
	if refV.Kind() != reflect.Ptr {
		return nil
	}
	elem := refV.Elem()
	if elem.Kind() != reflect.Slice {
		return nil
	}
	return elem.Interface()
}

// AnyToBytes
// 传入结构体对象、指针、或者string
func AnyToBytes(structOrStr any) []byte {
	var err error
	bytes := make([]byte, 0)
	if IsNilPointer(structOrStr) {
		return bytes
	}
	switch structOrStr.(type) {
	case string:
		bytes = []byte(structOrStr.(string))
	default:
		bytes, err = Marshal(structOrStr)
		if err != nil {
			err = stderr.Wrap(err, "failed to do AnyToBytes")
			panic(err)
		}
	}
	return bytes
}
func AnyToString(structOrStr any) string {
	return string(AnyToBytes(structOrStr))
}

func StringToAny(str string, anyPtr any) error {
	return Unmarshal([]byte(str), anyPtr)
}

func GetPtrOfStruct(structOrPtr any) any {
	refV := reflect.ValueOf(structOrPtr)
	if refV.Kind() != reflect.Ptr && refV.Kind() != reflect.Slice {
		newStructPtr := reflect.New(reflect.TypeOf(structOrPtr))
		newStructPtr.Elem().Set(refV)
		return newStructPtr.Interface()
	} else {
		return structOrPtr
	}
}

func IsNilPointer(v interface{}) bool {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr && val.IsNil() {
		return true
	}
	return false
}

func isBasicType(value reflect.Value) bool {
	switch value.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64,
		reflect.Bool,
		reflect.String:
		return true
	default:
		return false
	}
}

// getJsonTag 获取完整的jsonTag
func getJsonTag(sf reflect.StructField) string {
	return sf.Tag.Get(JsonTag)
}

// getJsonTagName jsonTag中的名称
func getJsonTagName(sf reflect.StructField) string {
	tag := getJsonTag(sf)
	return strings.Split(tag, ",")[0]
}
