package stdsrv

import (
	"strconv"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type (
	Param        = string
	ParamDesc    = string
	ParamType    = string
	ParamDefault = string
)

// 以下定义了部分HTTP请求解析过程中涉及的一些常见错误
var (
	ErrMissingPathParam     = stderr.NewCode(stderr.Base, "路径参数缺失", "Missing path parameter")
	ErrMissingQueryParam    = stderr.NewCode(stderr.Base, "必要请求参数缺失", "Missing required query parameter")
	ErrMissingRequiredField = stderr.NewCode(stderr.Base, "结构中必填字段缺失", "Missing required field in struct")
	ErrReadRequestBodyError = stderr.NewCode(stderr.Base, "请求体解析失败", "Request body parsing failed")
	ErrServiceConfigError   = stderr.NewCode(stderr.Base, "服务配置错误", "The service is misconfigured")
)

var (
	ProjectIDQueryParam = NewQueryParam("project_id", "用户当前接口关联的项目的ID")
	TenantIDQueryParam  = NewQueryParam("tenant_id", "用户当前接口关联的项目所属的租户ID(非必须")
)

const (
	ParamTypeString   ParamType = "string"
	ParamTypeBool     ParamType = "boolean"
	ParamTypeInt      ParamType = "integer"
	ParamTypeFloat    ParamType = "float"
	ParamTypeFile     ParamType = "file"
	QueryParamItemSep           = "," // 请求参数中， 多个项（数组Array或映射Map）之间的分隔符
	QueryParamKVSep             = "=" // 请求参数中， 单个Map的键值对之间的分符号

	DefaultPageNum  = 1
	DefaultPageSize = 10
)

type ParamDescriptor struct {
	Name        string
	Desc        string
	IsPathParam bool
}

func NewQueryParam(name, desc string) *ParamDescriptor {
	return &ParamDescriptor{
		Name:        name,
		Desc:        desc,
		IsPathParam: false,
	}
}
func NewPathParam(name, desc string) *ParamDescriptor {
	return &ParamDescriptor{
		Name:        name,
		Desc:        desc,
		IsPathParam: true,
	}
}

func (p *ParamDescriptor) GetName() string {
	return p.Name
}

func (p *ParamDescriptor) Param() *restful.Parameter {
	if p.IsPathParam {
		return restful.PathParameter(p.Name, p.Desc)
	}
	return restful.QueryParameter(p.Name, p.Desc)
}

func (p *ParamDescriptor) GetValue(req *restful.Request) string {
	if p.IsPathParam {
		return req.PathParameter(p.Name)
	}
	return req.QueryParameter(p.Name)
}

// GetNonEmptyValue 获取非空值,空值报错
func (p *ParamDescriptor) GetNonEmptyValue(req *restful.Request) (string, error) {
	param := req.QueryParameter(p.Name)
	if param == "" {
		return "", stderr.Internal.Error("failed to get non empty param %s", p.Name)
	}
	return param, nil
}

// GetRequiredValue 获取对应的参数，并判断是否为空
func (p *ParamDescriptor) GetRequiredValue(req *restful.Request) (string, error) {
	if p.IsPathParam {
		v := req.PathParameter(p.Name)
		if v == "" {
			return "", ErrMissingPathParam.Error(p.Name)
		}
		return v, nil
	}
	v := req.QueryParameter(p.Name)
	if v == "" {
		return "", ErrMissingQueryParam.Error(p.Name)
	}
	return v, nil
}

func (p *ParamDescriptor) GetIntValue(req *restful.Request) (int, error) {
	return p.GetIntValueWithDefault(req, 0)
}
func (p *ParamDescriptor) GetBoolValue(req *restful.Request) (bool, error) {
	v := ""
	if p.IsPathParam {
		v = req.PathParameter(p.Name)
	} else {
		v = req.QueryParameter(p.Name)
	}
	stdlog.Debugf("%s original value is %s", p.Name, v)
	if v == "" {
		return false, nil
	}
	bv, err := strconv.ParseBool(v)
	if err != nil {
		return false, stderr.InvalidParam.Cause(err, "invalid param %s=%s", p.Name, v)
	}
	return bv, nil
}
func (p *ParamDescriptor) GetIntValueWithDefault(req *restful.Request, defV int) (int, error) {
	v := ""
	if p.IsPathParam {
		v = req.PathParameter(p.Name)
	} else {
		v = req.QueryParameter(p.Name)
	}
	if v == "" {
		return defV, nil
	}
	iv, err := strconv.Atoi(v)
	if err != nil {
		return 0, stderr.InvalidParam.Cause(err, "invalid param %s : %s", p.Name, v)
	}
	return iv, nil
}

func (p *ParamDescriptor) GetValueAsArr(req *restful.Request) []string {
	vs := strings.Split(p.GetValue(req), QueryParamItemSep)
	for i, v := range vs {
		v = strings.TrimSpace(v)
		if v == "" {
			vs = append(vs[:i], vs[i+1:]...)
		}
	}
	return vs
}

func (p *ParamDescriptor) GetValueAsMap(req *restful.Request) map[string]string {
	items := p.GetValueAsArr(req)
	kvs := make(map[string]string, 0)
	for _, item := range items {
		kv := strings.Split(item, QueryParamKVSep)
		if len(kv) != 2 {
			stdlog.Warnf("invalid map key value pair item in query param: %s", item)
			continue
		}
		kvs[kv[0]] = kv[1]
	}
	return kvs
}
func (p *ParamDescriptor) GetTimeSecondsWithDefault(req *restful.Request, seconds int) (int, error) {
	timeStr := req.QueryParameter(p.Name)
	if timeStr == "" {
		return seconds, nil
	}
	dur, err := time.ParseDuration(timeStr)
	if err != nil {
		return -1, err
	}
	return int(dur.Seconds()), nil
}
