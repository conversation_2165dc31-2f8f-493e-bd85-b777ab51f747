package stdsrv

import (
	"net/http"
	"testing"

	"github.com/emicklei/go-restful/v3"
)

func TestUserEnglishLanguage(t *testing.T) {
	// -H 'Accept: application/json' \
	// -H 'Accept-Language: zh,en-US;q=0.9,en;q=0.8,zh-CN;q=0.7,eo;q=0.6' \
	// -H 'Cookie: PROJECT_ID=assets; grafana_session=2cc2dc8754a5880e2073c04d916a905e; SOPHONID=1863bd08c448770340c71a7323ed3c2a; debug=1; lang=en' \
	// -H 'Sophonlocale: zh' \
	r := &restful.Request{Request: &http.Request{Header: map[string][]string{
		"Cookie":       {"PROJECT_ID=assets", "grafana_session=2cc2dc8754a5880e2073c04d916a905e", "SOPHONID=1863bd08c448770340c71a7323ed3c2a", "debug=1", "lang=en"},
		"Sophonlocale": {"zh"},
		"Accept":       {"application/json"},
	}}}
	if got := UserEnglishLanguage(r); got != true {
		t.Errorf("UserEnglishLanguage() = %v, want %v", got, true)
	}

	if got := GetLanguage(r); got != LanguageEnglish {
		t.Fatalf("GetLanguage() = %v, want %v", got, LanguageEnglish)
	}
	if got, ok := GetLanguageFromCookie(r); got != LanguageEnglish || ok != true {
		t.Fatalf("GetLanguageFromCookie() = %v,%v, want %v,%v", got, ok, LanguageEnglish, true)
	}

	if got, ok := GetLanguageFromHeader(r); got != LanguageChinese || ok != true {
		t.Fatalf("GetLanguageFromHeader() = %v,%v, want %v,%v", got, ok, LanguageChinese, true)
	}
}
