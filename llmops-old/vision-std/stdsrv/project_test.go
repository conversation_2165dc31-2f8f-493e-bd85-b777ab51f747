package stdsrv

import (
	"net/http"
	"os"
	"testing"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

func TestGetProjectTenant(t *testing.T) {
	os.Setenv(CasEndpointEnv, "http://**************:31763")
	projectId := "llmops-default-9989"
	token := "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjAwNzQ3MTcsImlhdCI6MTcxOTgxNTUxNywiaXNzIjoidHJhbnN3YXJwIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInN1YiI6ImxsbW9wcyIsInVzZXJuYW1lIjoidGhpbmdlciJ9.havUTDeK97ajI7SGQyLQpPKDn612qQolXrFvWYs012FaHajEOreVf7Pj2b32ByAn_-UoY1iI2A28B5NoPM8saw"

	tenant, err := GetProjectTenant(projectId, token)
	if err != nil {
		t.Errorf("GetProjectTenantUid returned error: %v", err)
	}

	t.Log(tenant)
}

func TestEnsureTenantHippo(t *testing.T) {
	os.Setenv(CasEndpointEnv, "http://**************:31763")
	token := "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjAwNzQ3MTcsImlhdCI6MTcxOTgxNTUxNywiaXNzIjoidHJhbnN3YXJwIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInN1YiI6ImxsbW9wcyIsInVzZXJuYW1lIjoidGhpbmdlciJ9.havUTDeK97ajI7SGQyLQpPKDn612qQolXrFvWYs012FaHajEOreVf7Pj2b32ByAn_-UoY1iI2A28B5NoPM8saw"

	ok, msg := GetTenantHippo("llmops-default-9989", token)
	t.Log(ok, msg)
}

func TestGetProjectInfoFromRequest(t *testing.T) {
	type args struct {
		r *restful.Request
	}
	r := &restful.Request{}
	r.Request, _ = http.NewRequest("GET", "www.baidu.com?project_id=default", nil)
	SetCasConfig("https://**************:30745/llm/dev/gateway/cas", "")
	t.Run("", func(t *testing.T) {
		gotRet, err := GetProjectInfoFromRequest(r)

		t.Logf("ret=%s", toolkit.SprintPrettyJson(gotRet))
		t.Logf("err=%+v", err)
	})
	// == RUN   TestGetProjectInfoFromRequest/#00
	// project_test.go:44: ret={
	// 	"ProjectID": "default",
	// 			"TenantID": "llmops-default-9989",
	// 			"Tenant": {
	// 		"tenant_uid": "llmops-default-9989",
	// 				"tenant_quotas": {
	// 			"quota_name": "llmops-default-9989-default",
	// 					"hard": {
	// 				"limits_cpu": "725",
	// 						"limits_memory": "731.12Gi",
	// 						"pods": "1k",
	// 						"bandwidth": "4Gi",
	// 						"gpu": "300",
	// 						"gpu_memory": "162Gi",
	// 						"file_storage": "187Gi"
	// 			},
	// 			"used": {
	// 				"limits_cpu": "34200m",
	// 						"limits_memory": "86.77Gi",
	// 						"pods": "7",
	// 						"bandwidth": "0Gi",
	// 						"gpu": "0",
	// 						"gpu_memory": "0Gi",
	// 						"knowl": "2Gi",
	// 						"file_storage": "0Gi"
	// 			}
	// 		},
	// 		"creator": "admin",
	// 				"hippo_service_name": "autocv-hippo-service"
	// 	},
	// 	"Project": {
	// 		"createUser": "",
	// 				"create_time": "2024-05-22T02:32:05.937Z",
	// 				"create_user": "thinger",
	// 				"description": "您可以在本项目内，查看Sophon平台官方预置的训练数据/大语言模型/提示工程/应用链等默认数据，作为最佳实践案例。同时，您还可以通过克隆功能将内置数据应用到您自己的开发项目中。",
	// 				"disabled": false,
	// 				"examine": 0,
	// 				"industry": "交易所",
	// 				"labels": null,
	// 				"logo": "",
	// 				"member_count": 8,
	// 				"name": "快速开始_演示demo",
	// 				"project_id": "default",
	// 				"resource_quota": {
	// 			"bandwidth": "",
	// 					"egress_bandwidth": "",
	// 					"file_storage": "",
	// 					"gpu": "",
	// 					"gpu_memory": "",
	// 					"ingress_bandwidth": "",
	// 					"knowl": "",
	// 					"knowledge_base_storage": "",
	// 					"limits_cpu": "",
	// 					"limits_memory": "",
	// 					"pods": "",
	// 					"requests_cpu": "",
	// 					"requests_memory": "",
	// 					"requests_storage": ""
	// 		},
	// 		"tenant_uid": "llmops-default-9989"
	// 	}
	// }
	// project_test.go:45: err=<nil>
	// --- PASS: TestGetProjectInfoFromRequest/#00 (0.15s)
}
