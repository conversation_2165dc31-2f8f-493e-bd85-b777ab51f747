package stdsrv

import (
	"context"
	"testing"
)

var ctx context.Context
var configCenter *ConfigCenter

func init() {
	ctx = context.Background()
	configCenter = NewConfigCenter(WithEndpoint("http://**************:31364"), WithToken("Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4NzYwMjgyNjQsImlhdCI6MTcyMjQyODI2NCwiaXNzIjoiIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInN1YiI6IiIsInVzZXJuYW1lIjoidGhpbmdlciJ9.U4PVADKz2vt0FkP4XduTyxGe4cmLkWHkCmPe6MArnVIpGBNaI3My9mr63EGxbWWVNypuTJnHXB9H28EJ6tSIAg"))

}

func TestGetConfigDefines(t *testing.T) {
	configDefines, err := configCenter.GetConfigDefines(ctx)
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Log(configDefines)
}
func TestGetConfigValues(t *testing.T) {
	configValues, err := configCenter.GetConfigsValues(ctx)
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Log(configValues)
}

func TestPutConfigValues(t *testing.T) {
	configValues, err := configCenter.GetConfigsValues(ctx)
	if err != nil {
		t.Fatal(err)
		return
	}
	vs := configValues.Config
	t.Logf(" get values : %v", vs.Value)
	err = configCenter.PutConfigValues(ctx, &UpdateConfigValuesReq{
		customConfig: customConfig{
			Id:     vs.Id,
			System: vs.System,
			Value: map[string]interface{}{
				"tab_title": "LLMOps 测试配置中心接口",
			},
		},
	})
	if err != nil {
		t.Fatal(err)
		return
	}
	configValuesAfterUpdate, err := configCenter.GetConfigsValues(ctx)
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf(" get values : %v", configValuesAfterUpdate.Config.Value)
}
