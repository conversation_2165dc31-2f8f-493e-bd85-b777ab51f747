# THINGER-STD (standard library, protocol definitions)

## health pkg使用

1. main.go文件中直接 import 即可

```
import  (
    _ "transwarp.io/applied-ai/aiot/vision-std/health"
)
```

2. 修改deployment或statefulset, 添加liveness、readiness探针, 如下

```
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 80
            scheme: HTTP
          initialDelaySeconds: 20
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 2

        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 80
            scheme: HTTP
          initialDelaySeconds: 20
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 2
```

控制 liveness 和 readiness 检查的参数：
initialDelaySeconds：容器启动后第一次执行探测是需要等待多少秒。
periodSeconds：执行探测的频率。默认是 10 秒，最小 1 秒。
timeoutSeconds：探测超时时间。默认 1 秒，最小 1 秒。
successThreshold：探测失败后，最少连续探测成功多少次才被认定为成功。默认是 1。对于 liveness 必须是 1。最小值是 1。
failureThreshold：探测成功后，最少连续探测失败多少次才被认定为失败。默认是 3。最小值是 1。

HTTP probe 中可以给 httpGet 设置其他配置项：
host：连接的主机名，默认连接到 pod 的 IP。你可能想在 http header 中设置 “Host” 而不是使用 IP。
scheme：连接使用的 schema，默认 HTTP。
path: 访问的 HTTP server 的 path。
httpHeaders：自定义请求的 header。HTTP 运行重复的 header。
port：访问的容器的端口名字或者端口号。端口号必须介于 1 和 65535 之间
