package stdmsg

//
//var CurrMessageID int64 = 0
//
//func NextMessageID() int64 {
//	return atomic.AddInt64(&CurrMessageID, 1)
//}
//
//func type2MsgType(payload interface{}) MessageType {
//	switch payload.(type) {
//	case *stdhub.ResourceEvent:
//		return MessageType_ResourceEvent
//	case *ResourceReq:
//		return MessageType_ResourceReq
//	case *ResourceRsp:
//		return MessageType_ResourceRsp
//	case *ResourceTsReq:
//		return MessageType_ResourceTsReq
//	case *ResourceTsRsp:
//		return MessageType_ResourceTsRsp
//	default:
//		return MessageType_None
//	}
//}
//
//func NewMessage(sender string, payload interface{}) (*Message, error) {
//	return NewMessageWithID(sender, payload, NextMessageID())
//}
//
//func NewMessageWithID(sender string, payload interface{}, messageId int64) (*Message, error) {
//	data, err := json.<PERSON>(payload)
//	if err != nil {
//		return nil, err
//	}
//
//	return &Message{
//		Id:      messageId,
//		Type:    type2MsgType(payload),
//		Sender:  sender,
//		Payload: data,
//	}, nil
//}
//
//func UnmarshalPayload(msg *Message) (result interface{}, err error) {
//	switch msg.Type {
//	case MessageType_ResourceEvent:
//		result = new(stdhub.ResourceEvent)
//	case MessageType_ResourceReq:
//		result = new(ResourceReq)
//	case MessageType_ResourceRsp:
//		result = new(ResourceRsp)
//	case MessageType_ResourceTsReq:
//		result = new(ResourceTsReq)
//	case MessageType_ResourceTsRsp:
//		result = new(ResourceTsRsp)
//	default:
//		return nil, stderr.IllegalDataFormat.Error("unexpected message type: %s", msg.Type)
//	}
//
//	if err := json.Unmarshal(msg.Payload, result); err != nil {
//		return nil, stderr.Unmarshal.Cause(err, "fail to parse msg: %s", string(msg.Payload))
//	}
//
//	return result, nil
//}
