package stdmsg

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
)

func TestJsonBytes(t *testing.T) {

	owner := "test"
	res := stdhub.ResourceBuilder.NewEdge(owner, stdhub.GetEdgeNodeInfo(owner, "localhost"))
	msg, _ := NewMessage(owner, res)

	// PAYLOAD
	{
		t.Logf("json marshal size: %d", len(msg.Payload))

	}

	// JSON
	{
		data, _ := json.Marshal(msg)
		t.Logf("json marshal size: %d", len(data))

	}

	// PROTO
	{
		data, _ := proto.Marshal(msg)
		t.Logf("proto marshal size: %d", len(data))
	}

}