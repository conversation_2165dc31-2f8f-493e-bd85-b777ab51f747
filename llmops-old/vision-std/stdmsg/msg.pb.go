// Code generated by protoc-gen-go. DO NOT EDIT.
// source: msg.proto

package stdmsg

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MessageType int32

const (
	MessageType_None          MessageType = 0
	MessageType_ResourceEvent MessageType = 1
	MessageType_ResourceReq   MessageType = 2
	MessageType_ResourceRsp   MessageType = 3
	MessageType_ResourceTsReq MessageType = 4
	MessageType_ResourceTsRsp MessageType = 5
)

var MessageType_name = map[int32]string{
	0: "None",
	1: "ResourceEvent",
	2: "ResourceReq",
	3: "ResourceRsp",
	4: "ResourceTsReq",
	5: "ResourceTsRsp",
}
var MessageType_value = map[string]int32{
	"None":          0,
	"ResourceEvent": 1,
	"ResourceReq":   2,
	"ResourceRsp":   3,
	"ResourceTsReq": 4,
	"ResourceTsRsp": 5,
}

func (x MessageType) String() string {
	return proto.EnumName(MessageType_name, int32(x))
}
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_msg_0a207c0be2a9ab83, []int{0}
}

type Message struct {
	Id                   int64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 MessageType `protobuf:"varint,2,opt,name=type,proto3,enum=stdmsg.MessageType" json:"type,omitempty"`
	Sender               string      `protobuf:"bytes,3,opt,name=sender,proto3" json:"sender,omitempty"`
	Payload              []byte      `protobuf:"bytes,4,opt,name=payload,proto3" json:"payload,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_msg_0a207c0be2a9ab83, []int{0}
}
func (m *Message) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message.Unmarshal(m, b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message.Marshal(b, m, deterministic)
}
func (dst *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(dst, src)
}
func (m *Message) XXX_Size() int {
	return xxx_messageInfo_Message.Size(m)
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

func (m *Message) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Message) GetType() MessageType {
	if m != nil {
		return m.Type
	}
	return MessageType_None
}

func (m *Message) GetSender() string {
	if m != nil {
		return m.Sender
	}
	return ""
}

func (m *Message) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func init() {
	proto.RegisterType((*Message)(nil), "stdmsg.Message")
	proto.RegisterEnum("stdmsg.MessageType", MessageType_name, MessageType_value)
}

func init() { proto.RegisterFile("msg.proto", fileDescriptor_msg_0a207c0be2a9ab83) }

var fileDescriptor_msg_0a207c0be2a9ab83 = []byte{
	// 202 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x8f, 0xc1, 0x4a, 0xc4, 0x30,
	0x10, 0x86, 0x4d, 0x1a, 0xbb, 0xee, 0xac, 0xae, 0x71, 0x04, 0xc9, 0x31, 0x78, 0x31, 0x78, 0xe8,
	0x41, 0x9f, 0xc1, 0xa3, 0x1e, 0xc2, 0xbe, 0x40, 0x35, 0x43, 0x59, 0xb0, 0x4d, 0xcc, 0x44, 0xa1,
	0x6f, 0x2f, 0xd6, 0x16, 0xaa, 0xc7, 0xff, 0xe3, 0xfb, 0x06, 0x06, 0xb6, 0x3d, 0x77, 0x4d, 0xca,
	0xb1, 0x44, 0xac, 0xb9, 0x84, 0x9e, 0xbb, 0xdb, 0x02, 0x9b, 0x67, 0x62, 0x6e, 0x3b, 0xc2, 0x3d,
	0xc8, 0x63, 0x30, 0xc2, 0x0a, 0x57, 0x79, 0x79, 0x0c, 0x78, 0x07, 0xaa, 0x8c, 0x89, 0x8c, 0xb4,
	0xc2, 0xed, 0x1f, 0xae, 0x9b, 0xdf, 0xa2, 0x99, 0xf5, 0xc3, 0x98, 0xc8, 0x4f, 0x02, 0xde, 0x40,
	0xcd, 0x34, 0x04, 0xca, 0xa6, 0xb2, 0xc2, 0x6d, 0xfd, 0xbc, 0xd0, 0xc0, 0x26, 0xb5, 0xe3, 0x7b,
	0x6c, 0x83, 0x51, 0x56, 0xb8, 0x73, 0xbf, 0xcc, 0xfb, 0x0c, 0xbb, 0xd5, 0x19, 0x3c, 0x03, 0xf5,
	0x12, 0x07, 0xd2, 0x27, 0x78, 0x05, 0x17, 0x9e, 0x38, 0x7e, 0xe6, 0x37, 0x7a, 0xfa, 0xa2, 0xa1,
	0x68, 0x81, 0x97, 0xb0, 0x5b, 0x90, 0xa7, 0x0f, 0x2d, 0xff, 0x00, 0x4e, 0xba, 0x5a, 0x47, 0x07,
	0xfe, 0x71, 0xd4, 0x3f, 0xc4, 0x49, 0x9f, 0xbe, 0xd6, 0xd3, 0xe3, 0x8f, 0xdf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x2b, 0x9d, 0xfb, 0x4b, 0x05, 0x01, 0x00, 0x00,
}
