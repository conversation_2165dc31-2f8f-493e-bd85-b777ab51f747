package stderr

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
)

var tag = "std"

func SetContext(id string) {
	tag = id
}

// only for returning error to frontend, as they support { "code":"...", "msg":"..." }
type GatewayError struct {
	Code     int32  `json:"code"`
	Msg      string `json:"msg"`
	Detail   string `json:"detail"`
	Tag      string `json:"tag"`
	Redirect string `json:"redirect"` // 请求失败时前端重定向地址
}

func (e *GatewayError) Error() string {
	r, _ := json.Marshal(e)
	return string(r)
}

type StdErr struct {
	Tag      string `json:"tag"`
	Msg      string `json:"message"`
	Redirect string `json:"redirect"` // 请求失败时前端重定向地址
	Code     int32  `json:"err_code"`
	// deprecated 使用 Stack() 代替
	StackMsg string `json:"stack"` // 最底层错误发生时的调用堆栈信息

	stack []string // 不同层级的错误信息， 越底层的错误信息越靠前
}

// Error returns json of this error
func (e *StdErr) Error() string {
	// 当msg包括html标签时, json.Marshal会对<、>、&等特殊字符进行转义
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(e)
	return bf.String()
}

// Message returns brief error message
func (e *StdErr) Message() string {
	if e.Msg == "" {
		return GetCode(e.Code).Msg
	}
	return GetCode(e.Code).Msg + " : " + e.Msg
}

// Stack returns a formatted StackMsg trace of the goroutine that first error belongs to.
func (e *StdErr) Stack() string {
	return e.StackMsg
}

// JoinErrors 将给定的多个错误信息结合起来, 并以默认的错误码返回
// 若所有错误均为nil的话则返回nil
func JoinErrors(errs ...error) error {
	return JoinErrorsWithCode(Internal, errs...)
}

// JoinErrorsWithCode 将给定的多个错误信息结合起来, 并以指定的错误码返回
// 若所有错误均为nil的话则返回nil
func JoinErrorsWithCode(code *Code, errs ...error) error {
	if len(errs) == 0 {
		return nil
	}
	if len(errs) == 1 {
		return errs[0]
	}

	errMsg := ""
	for i, err := range errs {
		if err == nil {
			continue
		}
		errMsg += fmt.Sprintf("Error[%d]: %s\n", i, Unwrap(err).Message())
	}
	if errMsg == "" {
		return nil
	}
	return code.Error(errMsg)
}

func (e *StdErr) PrintStack() {
	print(e.StackMsg)
}

const ErrStackPrefix = "At:\n"

// Errorf 可触发编译器语法校验,推荐使用
func (code *Code) Errorf(format string, args ...interface{}) error {
	return code.Error(format, args...)
}

// Deprecated: 使用 Errorf 代替, 以触发IDE语法校验
// Error returns a StdErr with giving Code and messages.
func (code *Code) Error(format string, args ...interface{}) error {
	e := &StdErr{
		Tag:  tag,
		Code: code.Code,
		Msg:  fmt.Sprintf(format, args...),
	}
	return e.addStack()
}

// RedirectWithError returns a StdErr with giving redirect url, Code and messages.
func (code *Code) RedirectWithError(redirect string, format string, a ...interface{}) error {
	return &StdErr{
		Tag:      tag,
		Code:     code.Code,
		Redirect: redirect,
		Msg:      fmt.Sprintf(format, a...),
		StackMsg: string(debug.Stack()),
	}
}

// Cause returns an error with new Code and additional message.
// Code will be set to internal if giving err is nil or is not *StdErr.
// Code will be override if giving err is *StdErr.
func (code *Code) Cause(err error, format string, a ...interface{}) error {
	// avoid panic
	if err == nil {
		return code.Error(format, a...)
	}

	se := Unwrap(err)
	if se.Code == Unknown.Code {
		se.Code = code.Code
	}
	se = se.addStack()
	return se.addMsg(format, a...)
}

// RedirectWithCause returns an error with new redirect url, Code and additional message.
// Code will be set to internal if giving err is nil or is not *StdErr.
// Code will be override if giving err is *StdErr.
func (code *Code) RedirectWithCause(err error, redirect string, format string, a ...interface{}) error {
	// avoid panic
	if err == nil {
		return code.RedirectWithError(redirect, format, a...)
	}

	se := Unwrap(err)
	if se.Code == Unknown.Code {
		se.Code = code.Code
	}
	se.Redirect = redirect
	return se.addMsg(format, a...)
}

func getStack() string {
	// 获取当前 getStack 函数所在路径：
	// e.g. transwarp.io/applied-ai/aiot/vision-std/stderr.getStack
	pc, _, _, _ := runtime.Caller(0)
	cur := runtime.FuncForPC(pc).Name()
	ps := strings.Split(cur, ".")
	errPkg := "transwarp.io/applied-ai/aiot/vision-std/stderr"
	if len(ps) == 2 {
		errPkg = ps[0]
	}

	skip := 0
	var funcName, fileName string // 函数名、文件名
	var lineNo int                // 所在行
	for skip = 0; skip < 10; skip += 1 {
		pc, fileName, lineNo, _ = runtime.Caller(skip)
		funcName = runtime.FuncForPC(pc).Name()
		if strings.Contains(funcName, errPkg) {
			continue
		}
		break
	}
	return fmt.Sprintf("\t%s[%s:%d]\n", funcName, filepath.Base(fileName), lineNo)
}

// Wrap returns a *StdErr with additional message.
func Wrap(err error, format string, a ...interface{}) error {
	if err == nil {
		return Internal.Error(format, a...)
	}
	return Unwrap(err).addMsg(format, a...)
}

// Trace 仅添加当前调用者的堆栈信息
func Trace(err error) error {
	return Unwrap(err).addStack()
}

// Deprecated: 使用 Errorf 代替, 以触发IDE语法校验
// Error 直接使用默认的 Internal Code 返回错误信息
func Error(format string, a ...interface{}) error {
	return Errorf(format, a...)
}

// Errorf 直接使用默认的 Internal Code 返回错误信息
func Errorf(format string, a ...interface{}) error {
	return Internal.Errorf(format, a...)
}

// addMsg will append additional description to err msg.
func (e *StdErr) addMsg(format string, a ...interface{}) *StdErr {
	if format == "" {
		return e
	}
	e.Msg = fmt.Sprintf(format, a...) + " : " + e.Msg
	return e
}

// addStack 将当前错误的调用者（即错误发生或者包装的位置）的信息加入错误堆栈
func (e *StdErr) addStack() *StdErr {
	e.stack = append(e.stack, getStack())
	sm := []byte(ErrStackPrefix)
	for i := len(e.stack) - 1; i >= 0; i-- {
		sm = append(sm, []byte(e.stack[i])...)
	}
	e.StackMsg = string(sm)
	return e
}

func Unwrap(err error) *StdErr {
	if err == nil {
		return nil
	}
	var e *StdErr
	if errors.As(err, &e) {
		return e
	}
	return Internal.Error(err.Error()).(*StdErr)
}
