package stderr

import (
	"fmt"
	"net/http"
)

type Code struct {
	Code     int32
	HttpCode int
	Msg      string
	Msg_EN   string
}

// func (c *Code) Error() string {

// }

// BaseCode为每个模块对应的code的起始值，为6位以上的整数
// 每个具体的error code 最终为： BaseCode*10^3 + SubCode
type BaseCode int

const (
	Base     BaseCode = 1
	Demgr    BaseCode = 100
	Plmgr    BaseCode = 200
	Faas     BaseCode = 300
	Hub      BaseCode = 400
	Micro    BaseCode = 500
	Alertmgr BaseCode = 600
	Boot     BaseCode = 700
	Appmgr   BaseCode = 800
	Status   BaseCode = 900
	Datamgr  BaseCode = 1000
	Scene    BaseCode = 1100
	Deploy   BaseCode = 1200
	License  BaseCode = 1400

	IllegalDataFormatErrCode = 810
	InvalidParamErrCode      = 820
	IPWhiteList              = 4030
)

var (
	AllCodes = make(map[int32]*Code)

	/* General Errors */
	TooManyRequests          = newCode(300, "您的操作过于频繁，请稍后再试", "Too Many Requests")
	BadRequest               = newCode(400, "错误的请求参数", "Bad Request")
	Unauthenticated          = newCode(401, "认证失败", "Authenticated failed")
	Unauthorized             = newCode(403, "授权失败", "Authorized failed")
	Unauthorized_IPWhiteList = newCode(IPWhiteList, "请使用配置白名单内的IP访问", "Please use an IP address from the configured whitelist to access")
	NotFound                 = newCode(404, "您访问的资源不存在", "Not Found")

	Internal          = newCode(500, "服务内部错误", "Internal Message Error")
	Unsupported       = newCode(600, "操作不支持", "Unsupported")
	NotAllowed        = newCode(610, "操作不允许", "Not Allowed")
	Database          = newCode(700, "数据库操作失败", "Database Operation Failure")
	IllegalDataFormat = newCode(IllegalDataFormatErrCode, "数据格式不正确", "Illegal Data Format")
	InvalidParam      = newCode(InvalidParamErrCode, "参数不正确", "Invalid Parameter")
	Marshal           = newCode(830, "序列化错误", "Serialization Failure")
	Unmarshal         = newCode(840, "反序列化错误", "Deserialization Failure")
	Unknown           = newCode(999, "未定义的错误", "Unknown Failure")

	/* Device Manager */
	/* Device Errors: 100,000 - 100,999 */
	DeviceNotFound            = newCode(100401, "设备不存在", "Device Not Found")
	ProductNotFound           = newCode(100402, "设备产品不存在", "Product Not Found")
	DeviceRpcUnbindFailure    = newCode(100601, "设备解绑规则应用失败", "Device Unbind Pipeline Instance Failure")
	DeviceRpcWriteDataFailure = newCode(100602, "设备数据写入失败", "Device Write Data Failure")
	DeviceReadPropFailure     = newCode(100603, "设备读取属性失败", "Device Read Property Failure")
	DeviceWritePropFailure    = newCode(100604, "设备更新属性失败", "Device Write Property Failure")
	DeviceSubEventFailure     = newCode(100605, "设备监听事件失败", "Device Subscribe Event Failure")
	DeviceCallMethodFailure   = newCode(100606, "设备调用服务失败", "Device Call Method Failure")
	DeviceQueryFailure        = newCode(100701, "设备查询失败", "Device Query Failure")
	DeviceSaveFailure         = newCode(100702, "设备保存失败", "Device Save Failure")
	DeviceRemoveFailure       = newCode(100703, "设备删除失败", "Device Remove Failure")
	DeviceIllegalDataFormat   = newCode(100810, "设备数据格式不正确", "Device Illegal Data Format")
	DeviceInvalidParam        = newCode(100820, "设备参数不正确", "Device Invalid Parameter")
	DeviceAlreadyExist        = newCode(100901, "设备已存在", "Device Already Exist")
	DeviceInfoResolveFailure  = newCode(100902, "设备信息解析失败", "Device Info Resolve Failure")
	DeviceOperationFailure    = newCode(100903, "设备操作失败", "Device Operation Failure")
	DeviceNotConnectedError   = newCode(100904, "设备未连接", "Device Not Connected")

	/* Dashboard Errors: 101,000 - 101,999 */
	DashboardNotFound       = newCode(101401, "面板不存在", "Dashboard Not Found")
	DashboardInvalidParam   = newCode(101810, "面板参数不正确", "Dashboard Invalid Parameter")
	DashboardAlreadyExist   = newCode(101901, "面板已存在", "Dashboard Already Exist")
	DashboardQueryFailure   = newCode(101701, "面板查询失败", "Dashboard Query Failure")
	DashboardSaveFailure    = newCode(101702, "面板保存失败", "Dashboard Save Failure")
	DashboardRemoveFailure  = newCode(101703, "面板删除失败", "Dashboard RemoveRelease Failure")
	DashboardPanelNotFound  = newCode(101402, "仪表不存在", "Panel Not Found")
	DashboardInvalidDisplay = newCode(101403, "数据展示异常", "Dashboard Data Display Wrongly")

	/* Pipeline Manager */
	/* Pipeline Errors: 200,000 - 200,999 */
	PipelineNotFound     = newCode(200401, "规则定义不存在", "Pipeline Not Found")
	PipelineAlreadyExist = newCode(200901, "规则定义已存在", "Pipeline Already Exist")
	PipelineQueryFailure = newCode(200701, "规则定义查询失败", "Pipeline Query Failure")

	/* Pipeline Instance Errors: 201,000 - 201,999 */
	PipelineInstanceNotFound        = newCode(201401, "规则实例不存在", "Pipeline Instance Not Found")
	PipelineInstanceAlreadyExist    = newCode(201901, "规则实例已存在", "Pipeline Instance Already Exist")
	PipelineInstanceQueryFailure    = newCode(201701, "规则实例查询失败", "Pipeline Instance Query Failure")
	PipelineInstanceRemoveFailure   = newCode(201703, "规则实例删除失败", "Pipeline Instance Remove Failure")
	PipelineInstanceRunFailure      = newCode(201704, "规则实例启动失败", "Pipeline Instance Run Failure")
	PipelineInstanceStopFailure     = newCode(201705, "规则实例停止失败", "Pipeline Instance Stop Failure")
	PipelineContainCyclic           = newCode(201706, "规则定义中不能包含有向环", "The pipeline must be a directed acyclic graph")
	PipelineContainMultiCC          = newCode(201707, "规则最多仅能包含一个连通分量", "A Pipeline can contain at most one connected component")
	PipelineNodeTypeIllegal         = newCode(201708, "规则算子节点类型非法", "The pipeline node type illegal")
	PipelineNodeMissingRequiredProp = newCode(201709, "规则算子节点缺少必填属性", "The pipeline node is missing a required property")
	PipelineNodeNotFound            = newCode(201710, "规则算子节点不存在", "The pipeline node not found")
	PipelineNotAvailable            = newCode(201711, "规则不可用", "The pipeline is not available")

	/* Boot Errors: 300,000 - 300,999 */

	/* Function Errors: 301,000 - 301,999 */
	FunctionAlreadyExist  = newCode(301901, "函数已存在", "Function Already Exist")
	FunctionInvalidFormat = newCode(301902, "无效的函数格式", "Invalid Function Format")

	/* Micro Service */
	/* Micro Service : 500,000 - 500,999 */
	ServiceBadRequest       = newCode(500001, "请求无效", "Bad Request")
	ServiceUnauthorized     = newCode(500002, "用户未授权", "Unauthorized")
	ServiceForbidden        = newCode(500003, "被禁止的操作", "Forbidden")
	ServiceNotFound         = newCode(500004, "服务不存在或未启动", "Service Not Found")
	ServiceMethodNotAllowed = newCode(500005, "方法不允许", "Method Not Allowed")
	ServiceTimeout          = newCode(500006, "方法调用超时", "Method Timeout")
	ServiceConflict         = newCode(500007, "请求存在冲突", "Request Conflict")
	ServiceInternal         = newCode(500008, "服务内部错误", "Internal Server Error")

	/* Alert Manager */
	/* Alert Errors: 600,000 - 600999 */
	BatchDeleteFailure = newCode(600001, "批量删除告警消息失败", "Batch Delete Alert Messages Failure")

	/* k8s Errors: 700,000 - 700999 */
	ObjectNoDifference = newCode(700000, "k8s对象之间没有区别", "Obeject No Difference")

	/* App  Errors: 800,000 - 800,999 */
	AppNotFound           = newCode(800401, "服务类型不存在", "App Not Found")
	AppAlreadyExist       = newCode(800601, "服务已存在", "App Already Exist")
	AppHaveVersionIsInUse = newCode(800606, "服务有版本正在运行", "App Have Some Versions Is In Use")
	AppRemoveFailure      = newCode(800603, "服务删除失败", "App Remove Failure")

	/* App Release Errors: 801,000 - 801,999 */
	AppReleaseNotFound      = newCode(801401, "服务版本不存在", "AppRelease Not Found")
	AppReleaseIsInUse       = newCode(801606, "服务版本正在运行", "AppRelease Is Use")
	AppReleaseRemoveFailure = newCode(801603, "服务版本删除失败", "AppRelease Remove Failure")
	AppReleaseAlreadyExist  = newCode(801604, "服务版本已存在", "AppRelease Already Exist")
	/* Resource Errors: 400,000 - 400,999 */
	ResourceNotFound         = newCode(400401, "资源不存在", "Resource Not Found")
	ResourceValueEmpty       = newCode(400402, "资源对象为空", "Resource Value Empty")
	ResourceQueryFailure     = newCode(400701, "资源查询失败", "Resource Query Failure")
	ResourceSaveFailure      = newCode(400702, "资源保存失败", "Resource Save Failure")
	ResourceRemoveFailure    = newCode(400703, "资源删除失败", "Resource Remove Failure")
	ResourceUnmarshalFailure = newCode(400830, "资源解析失败", "Resource Unmarshal Failure")
	ResourceAlreadyExist     = newCode(400901, "资源已存在", "Resource Already Exist")
	ResourceOutOfDate        = newCode(400902, "资源已过期", "Resource Out of Date")
	ResourceSelfMigration    = newCode(400903, "资源迁移源与目标不能相同", "Resource Migration Source Target Must Be Different")
	ResourceUnsupported      = newCode(400904, "资源类型不支持", "Resource Type Unsupported")
	ResourceDeleted          = newCode(400905, "资源已删除", "Resource Deleted")

	/* Data Manager Errors: 1,000,000 - 1,000,999 */
	DirectoryAlreadyExist   = newCode(1000001, "同名目录已存在", "Directory Already Exists")
	InvalidExpireTimeFormat = newCode(1000002, "过期时间格式错误", "Invalid Expiration Time Format")
	TaskExecuteFailed       = newCode(1000003, "数据治理任务执行失败", "Failed to Execute task")
	TaskNameConflict        = newCode(1000004, "存在同类型同名任务", "task of the Same Type and Name Exists")
	SuperTaskAlreadyExist   = newCode(1000005, "父文件夹已设置同类型任务", "The Same Type task Created on the Parent Folder")
	SubTaskAlreadyExist     = newCode(1000006, "子文件夹已设置", "The Same Type task Created on the Child Folder")
	TaskSchemaFoundFailed   = newCode(1000007, "从数据中心检索任务元数据失败", "Failed to Find task Schema from Hub")
	TaskAlreadyRunning      = newCode(1000008, "任务已处于运行状态", "task Already Running")
	TaskAlreadyStopped      = newCode(1000009, "任务已处于中止状态", "task Already Stopped")
	DirectoryNotFound       = newCode(1000010, "目录不存在", "Directory Not Found")
	InvalidCompressionFile  = newCode(1000011, "无效的压缩文件", "Invalid Compression File")
	PathIsNotDir            = newCode(1000012, "任务路径仅支持文件夹", "Task Path Only Supports Directories")
	InvalidFileName         = newCode(1000013, "文件名包含非法字符", "The file name contains illegal characters")

	// Scene Manager Errors: 1,100,000 - 1,100,999 */
	SceneAPIError           = newCode(110001, "场景管理请求失败", "Scene Manager Request Failure")
	ScenePkgExportError     = newCode(110002, "场景包导出失败", "Scene Package Export Failure")
	ScenePkgImportError     = newCode(110003, "场景包导入失败", "Scene Package Import Failure")
	SceneDistributeError    = newCode(110004, "场景下发失败", "Scene Distribution Failure")
	SceneReleaseCheckError  = newCode(110006, "场景版本校验失败", "Scene Release Verify Failure")
	SceneInstanceStartError = newCode(110005, "场景实例启动失败", "Scene Instance Start Failure")

	// System Errors: 1,200,000 - 1,200,999
	ImageNotFoundError = newCode(120001, "Docker镜像不存在", "Docker Image Not Found")
	// Multi Media GatewayAPI Errors
	MMGatewayAPIError = newCode(130001, "多媒体网关请求失败", "Multimedia Gateway Request Failure")

	LicenseValidError           = newCode(int32(License*1e3+1), "许可证授权验证失败", "License Cert Valid Failure")
	LicenseRscExceedError       = newCode(int32(License*1e3+2), "资源使用超出许可证书范围", "Resource usage exceeds the license permitted scope")
	LicenseNotAllowedError      = newCode(int32(License*1e3+3), "操作不在许可证书范围内", "The operation is not within the license permitted scope")
	LicenseHDNotMatchError      = newCode(int32(License*1e3+4), "硬件信息与许可证授权范围不一致", "The hardware information is inconsistent with the scope of the authorization")
	LicenseNotActivatedError    = newCode(int32(License*1e3+5), "产品许可证授权尚未激活", "The product license has not been activated")
	LicenseUnsupportedAuthError = newCode(int32(License*1e3+6), "不支持的许可证授权类型", "Unsupported license authorization types")

	// Applet Cube Errors: 150000 - 159999 */
	AppletNameDuplicatedError    = newCode(150000, "应用链名称重复", "Duplicate applet chain name")
	AppletChainParamMissedError  = newCode(150001, "应用链必填参数缺失", "Miss applet chain param")
	AppletChainParamInvalidError = newCode(150002, "应用链参数错误", "Invalid applet chain param")

	// Points System Errors: 160000 - 169999 */
	PointsSystemNotEnoughPoints = newCode(160001, "积分不足，剩余积分", "not enough points for the project, Remaining points")

	// CAS Errors: 170000 - 179999 */
	RuleNameExistsErr = newCode(170001, "预警规则名称已存在", "Rule name already exists")

	// Knowlhub Errors: 180000 - 189999 */
	KnowlBaseNotFound    = newCode(180001, "知识库不存在", "Knowledge base not found")
	KnowlBaseDocNotFound = newCode(180002, "文档不存在", "Document not found")
	KnowlChunkNotFound   = newCode(180003, "分段不存在", "Chunk not found")
)

func newCode(code int32, msg string, msgEn string) *Code {
	if oc, ok := AllCodes[code]; ok {
		panic(fmt.Sprintf("Duplicated error code (code:'%d',msg:'%s') with (code:'%d',msg:'%s')!!!", code, msg, oc.Code, oc.Msg))
	}
	httpCode := http.StatusInternalServerError
	switch code {
	// 保留特定错误码原本的http status code: 400, 401 , 403
	case http.StatusBadRequest, http.StatusUnauthorized, http.StatusForbidden:
		httpCode = int(code)
	case IllegalDataFormatErrCode, InvalidParamErrCode:
		httpCode = http.StatusBadRequest
	case IPWhiteList:
		httpCode = 403
	}
	result := &Code{code, httpCode, msg, msgEn}
	AllCodes[code] = result
	return result
}

func GetCode(code int32) *Code {
	if c, ok := AllCodes[code]; ok {
		return c
	} else {
		return Unknown
	}
}

func NewCode(base BaseCode, msg, msgEn string) *Code {
	next := int32(base)*1e3 + 1
	for k := range AllCodes {
		if k/1000 != int32(base) {
			continue
		}

		if k >= next {
			next = k + 1
		}
	}
	return newCode(next, msg, msgEn)
}
