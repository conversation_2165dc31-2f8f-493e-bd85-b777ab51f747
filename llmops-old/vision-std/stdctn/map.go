package stdctn

import (
	"golang.org/x/exp/constraints"
	"sort"
	"sync"
)

type ConcurrentMap[K comparable, V any] struct {
	sync.RWMutex
	m             map[K]V
	hookBeforeSet func(key K, old V, new V)
}

func NewConcurrentMap[K comparable, V any]() *ConcurrentMap[K, V] {
	return &ConcurrentMap[K, V]{
		m: make(map[K]V),
	}
}

func (m *ConcurrentMap[K, V]) Get(key K) (V, bool) {
	m.RLock()
	defer m.RUnlock()
	v, ok := m.m[key]
	return v, ok
}

func (m *ConcurrentMap[K, V]) Set(key K, value V) {
	m.Lock()
	defer m.Unlock()
	if m.hookBeforeSet != nil {
		m.hookBeforeSet(key, m.m[key], value)
	}
	m.m[key] = value
}

func (m *ConcurrentMap[K, V]) Delete(key K) {
	m.Lock()
	defer m.Unlock()
	delete(m.m, key)
}

func (m *ConcurrentMap[K, V]) BeforeSet(fn func(key K, old V, new V)) {
	m.hookBeforeSet = fn
}

func Keys[KT constraints.Ordered, VT any](mp map[KT]VT) []KT {
	ks := make([]KT, 0, len(mp))
	for k := range mp {
		ks = append(ks, k)
	}
	sort.Slice(ks, func(i, j int) bool {
		return ks[i] < ks[j]
	})
	return ks
}

func Values[KT constraints.Ordered, VT any](mp map[KT]VT) []VT {
	ks := Keys(mp)
	vs := make([]VT, len(ks))
	for i, k := range ks {
		vs[i] = mp[k]
	}
	return vs
}
