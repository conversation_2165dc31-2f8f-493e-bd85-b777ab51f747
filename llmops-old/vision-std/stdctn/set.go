package stdctn

// Set 为基于 go map 与 泛型机制实现的一个简单的 Set 的封装
// PS: 线程不安全
type Set[T comparable] map[T]struct{}

func NewSet[T comparable]() Set[T] {
	return make(Set[T], 0)
}

func (s Set[T]) Add(t T) {
	s[t] = struct{}{}
}

func (s Set[T]) Exist(t T) bool {
	_, ok := s[t]
	return ok
}

func (s Set[T]) Delete(t T) {
	delete(s, t)
}

func (s Set[T]) Count() int {
	return len(s)
}

func (s Set[T]) ToArray() []T {
	ret := make([]T, 0, len(s))
	for t := range s {
		ret = append(ret, t)
	}
	return ret
}
