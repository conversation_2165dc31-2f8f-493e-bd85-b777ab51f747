package srs

import (
	"reflect"
	"testing"
)

func TestGetDivision(t *testing.T) {
	type args struct {
	}
	tests := []struct {
		name  string
		code  string
		want  Division
		want1 bool
	}{
		{code: "110000", want: Division{Province: "北京市", Prefecture: "", Country: ""}, want1: true},
		{code: "110101", want: Division{Province: "北京市", Prefecture: "", Country: "东城区"}, want1: true},
		{code: "13", want: Division{Province: "河北省", Prefecture: "", Country: ""}, want1: true},
		{code: "1301", want: Division{Province: "河北省", Prefecture: "石家庄市", Country: ""}, want1: true},
		{code: "130102", want: Division{Province: "河北省", Prefecture: "石家庄市", Country: "长安区"}, want1: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetDivision(tt.code)
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("GetDivision() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.<PERSON><PERSON><PERSON>("GetDivision() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
