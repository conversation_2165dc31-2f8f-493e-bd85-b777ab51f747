package srs

import (
	gb2260 "github.com/cn/GB2260.go"
)

type DivisionCode string

var gb = gb2260.NewGB2260("")

const (
	SupportCivilCodeMaxLen = 6 // 最大可自动解析的CiviCode长度
	CivilCodeLen           = 8 // 国标中中心编码长度（GB28181 附录D）
)

// GetDivision 根据给定的行政区划代码，自动解析出其对应的行政区划名称（依据国标GB2260）
// 最多支持6位的行政区划代码（不足自动补0，超出自动截断）
func GetDivision(code string) (Division, bool) {
	if l := len(code); l != 6 {
		if l < 6 {
			for ; l < 6; l++ {
				code += "0"
			}
		} else {
			code = code[:6]
		}
	}
	res := Division{}
	dv := gb.Get(code)
	if dv == nil {
		return res, false
	}
	for _, s := range dv.Stack() {
		switch {
		case s.IsProvince():
			res.Province = s.Province().Name
		case s.IsPrefecture():
			res.Prefecture = s.Prefecture().Name
		case s.IsCountry():
			res.Country = s.Country().Name
		}
	}
	return res, true
}

type Division struct {
	Province   string // 省级行政区划
	Prefecture string // 市级行政区划
	Country    string // 区县级行政区划
}

func (d *Division) String() string {
	res := ""
	if d.Province != "" {
		res += d.Province
	}
	if d.Prefecture != "" {
		res += d.Prefecture
	}
	if d.Country != "" {
		res += d.Country
	}
	return res
}
