package srs

import (
	"net/http"
	"reflect"
	"testing"
)

func Test_client_ListGB28181Channels(t *testing.T) {
	type fields struct {
		cli       http.Client
		urlPrefix string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				cli:       http.Client{},
				urlPrefix: "http://*************:31448/api/v1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &client{
				cli:       tt.fields.cli,
				urlPrefix: tt.fields.urlPrefix,
			}
			got, err := c.ListGB28181Channels()
			if (err != nil) != tt.wantErr {
				t.Errorf("ListGB28181Channels() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("got %d channels", len(got))
			t.Logf("%+v", got)
		})
	}
}

func TestNewGB28181Url(t *testing.T) {
	type args struct {
		host       string
		port       int
		sessionID  string
		deviceID   string
		autoInvite bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				host:       "*************",
				port:       30214,
				sessionID:  "34020000001320000249",
				deviceID:   "34020000001320000255",
				autoInvite: false,
			},
			want: "rtmp://*************:30214/live/34020000001320000249@34020000001320000255",
		},
		{
			name: "",
			args: args{
				host:       "*************",
				port:       30214,
				sessionID:  "34020000001320000249",
				deviceID:   "34020000001320000255",
				autoInvite: true,
			},
			want: "rtmp://*************:30214/live/34020000001320000249@34020000001320000255?url=Z2IyODE4MTovLzM0MDIwMDAwMDAxMzIwMDAwMjQ5QDM0MDIwMDAwMDAxMzIwMDAwMjU1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewGB28181Url(tt.args.host, tt.args.port, tt.args.sessionID, tt.args.deviceID, tt.args.autoInvite); got != tt.want {
				t.Errorf("NewGB28181Url() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGB28181Item_AsMap(t1 *testing.T) {

	tests := []struct {
		name string
		item GB28181Item
		want map[string]string
	}{
		{
			name: "",
			item: GB28181Item{
				DeviceID:     "64010000001330000001",
				Name:         "Camera1",
				Manufacturer: "Manufacturer1",
				Model:        "Model1",
				Owner:        "Owner1",
				CivilCode:    "CivilCode1",
				Block:        "Block1",
				Address:      "Address1",
				Parental:     "1",
				ParentID:     "64010000001110000001",
				SafetyWay:    "0",
				RegisterWay:  "1",
				CertNum:      "CertNum1",
				Certifiable:  "0",
				ErrCode:      "400",
				EndTime:      "2010-11-11T19:46:17",
				Secrecy:      "0",
				IPAddress:    "************",
				Port:         "5060",
				Password:     "Password1",
				Status:       "Status1",
				Longitude:    "171.3",
				Latitude:     "34.2",
			},
			want: map[string]string{
				"DeviceID":     "64010000001330000001",
				"Name":         "Camera1",
				"Manufacturer": "Manufacturer1",
				"Model":        "Model1",
				"Owner":        "Owner1",
				"CivilCode":    "CivilCode1",
				"Block":        "Block1",
				"Address":      "Address1",
				"Parental":     "1",
				"ParentID":     "64010000001110000001",
				"SafetyWay":    "0",
				"RegisterWay":  "1",
				"CertNum":      "CertNum1",
				"Certifiable":  "0",
				"ErrCode":      "400",
				"EndTime":      "2010-11-11T19:46:17",
				"Secrecy":      "0",
				"IPAddress":    "************",
				"Port":         "5060",
				"Password":     "Password1",
				"Status":       "Status1",
				"Longitude":    "171.3",
				"Latitude":     "34.2",
			},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			if got := tt.item.AsMap(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("AsMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParse28181Item(t *testing.T) {
	type args struct {
		xmlStr string
	}
	tests := []struct {
		name    string
		args    args
		want    *GB28181Item
		wantErr bool
	}{
		{
			name: "",
			args: args{
				xmlStr: `
		<Item>
            <DeviceID>64010000001330000001</DeviceID>
            <Name>Camera1</Name>
            <Manufacturer>Manufacturer1</Manufacturer>
            <Model>Model1</Model>
            <Owner>Owner1</Owner>
            <CivilCode>CivilCode1</CivilCode>
            <Block>Block1</Block>
            <Address>Address1</Address>
            <Parental>1</Parental>
            <ParentID>64010000001110000001</ParentID>
            <SafetyWay>0</SafetyWay>
            <RegisterWay>1</RegisterWay>
            <CertNum>CertNum1</CertNum>
            <Certifiable>0</Certifiable>
            <ErrCode>400</ErrCode>
            <EndTime>2010-11-11T19:46:17</EndTime>
            <Secrecy>0</Secrecy>
            <IPAddress>************</IPAddress>
            <Port>5060</Port>
            <Password>Password1</Password>
            <Status>Status1</Status>
            <Longitude>171.3</Longitude>
            <Latitude>34.2</Latitude>
        </Item>`,
			},
			want: &GB28181Item{
				DeviceID:     "64010000001330000001",
				Name:         "Camera1",
				Manufacturer: "Manufacturer1",
				Model:        "Model1",
				Owner:        "Owner1",
				CivilCode:    "CivilCode1",
				Block:        "Block1",
				Address:      "Address1",
				Parental:     "1",
				ParentID:     "64010000001110000001",
				SafetyWay:    "0",
				RegisterWay:  "1",
				CertNum:      "CertNum1",
				Certifiable:  "0",
				ErrCode:      "400",
				EndTime:      "2010-11-11T19:46:17",
				Secrecy:      "0",
				IPAddress:    "************",
				Port:         "5060",
				Password:     "Password1",
				Status:       "Status1",
				Longitude:    "171.3",
				Latitude:     "34.2",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Parse28181Item(tt.args.xmlStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("Parse28181Item() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Parse28181Item() got = %v, want %v", got, tt.want)
			}
		})
	}
}
