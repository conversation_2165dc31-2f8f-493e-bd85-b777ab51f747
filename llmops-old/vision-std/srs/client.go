package srs

import (
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"github.com/fatih/structs"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DeviceStatusOn    = "ON"
	DeviceStatusOff   = "OFF"
	DeviceIsParent    = "1"
	DeviceIsNotParent = "0"
)

type Stream struct {
	Id      string `json:"id"`
	Name    string `json:"name"`
	Vhost   string `json:"vhost"`
	App     string `json:"app"`
	Publish struct {
		Active bool   `json:"active"`
		Cid    string `json:"cid"`
	} `json:"publish"`
}

type ListStreamsRsp struct {
	Code    int      `json:"code"`
	Server  int      `json:"server"`
	Streams []Stream `json:"streams"`
}

type KickoffRsp struct {
	Code int `json:"code"`
}

// Gb28181Device 为 multimedia-gateway (SRS) 中定义的GB28181设备的结构
type Gb28181Device struct {
	DeviceId           string `json:"device_id"`
	DeviceStatus       string `json:"device_status"`
	DeviceName         string `json:"device_name"`
	DeviceParental     string `json:"device_parental"`
	DeviceManufacturer string `json:"device_manufacturer"`
	DeviceRaw          string `json:"device_raw"`
	InviteStatus       string `json:"invite_status"`
	InviteTime         int64  `json:"invite_time"`
}

func (d *Gb28181Device) RawItem() (*GB28181Item, error) {
	return Parse28181Item(d.DeviceRaw)
}

// GB28181Item 为 GB28181 规范中定义的设备信息查询返回的数据结构
// 具体可参见 J.9.5 Messagesip:源设备编码@源域名或IP地址端口SIP/2.0
type GB28181Item struct {
	DeviceID        string `xml:"DeviceID"`
	Name            string `xml:"Name"`
	Manufacturer    string `xml:"Manufacturer"`
	Model           string `xml:"Model"`
	Owner           string `xml:"Owner"`
	CivilCode       string `xml:"CivilCode"`
	Block           string `xml:"Block"`
	Address         string `xml:"Address"`
	Parental        string `xml:"Parental"`
	ParentID        string `xml:"ParentID"`
	BusinessGroupID string `xml:"BusinessGroupID"`
	SafetyWay       string `xml:"SafetyWay"`
	RegisterWay     string `xml:"RegisterWay"`
	CertNum         string `xml:"CertNum"`
	Certifiable     string `xml:"Certifiable"`
	ErrCode         string `xml:"ErrCode"`
	EndTime         string `xml:"EndTime"`
	Secrecy         string `xml:"Secrecy"`
	IPAddress       string `xml:"IPAddress"`
	Port            string `xml:"Port"`
	Password        string `xml:"Password"`
	Status          string `xml:"Status"`
	Longitude       string `xml:"Longitude"`
	Latitude        string `xml:"Latitude"`
}

// AsMap 将该结构体的所有字段与对应值转换为 map[string]string 并返回
func (t *GB28181Item) AsMap() map[string]string {
	si := structs.Map(t)
	res := make(map[string]string, len(si))
	for s, i := range si {
		if str, ok := i.(string); ok {
			res[s] = str
		}
	}
	return res
}

// Parse28181Item GB28181 摄像头目录信息的XML中将各个字段解析为结构体
// 具体XML格式如下：
// <Item>
//    <DeviceID>64010000001330000001</DeviceID>
//    <Name>Camera1</Name>
//    <Manufacturer>Manufacturer1</Manufacturer>
//    <Model>Model1</Model>
//    <Owner>Owner1</Owner>
//    <CivilCode>CivilCode1</CivilCode>
//    <Block>Block1</Block>
//    <Address>Address1</Address>
//    <Parental>1</Parental>
//    <ParentID>64010000001110000001</ParentID>
//    <SafetyWay>0</SafetyWay>
//    <RegisterWay>1</RegisterWay>
//    <CertNum>CertNum1</CertNum>
//    <Certifiable>0</Certifiable>
//    <ErrCode>400</ErrCode>
//    <EndTime>2010-11-11T19:46:17</EndTime>
//    <Secrecy>0</Secrecy>
//    <IPAddress>************</IPAddress>
//    <Port>5060</Port>
//    <Password>Password1</Password>
//    <Status>Status1</Status>
//    <Longitude>171.3</Longitude>
//    <Latitude>34.2</Latitude>
//</Item>
func Parse28181Item(xmlStr string) (*GB28181Item, error) {
	res := new(GB28181Item)
	err := xml.Unmarshal([]byte(xmlStr), res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

type Gb28181Session struct {
	Devices      []*Gb28181Device `json:"devices"`
	Id           string           `json:"id"`
	DeviceSumnum int              `json:"device_sumnum"`
}

type Gb28181Channel struct {
	Id          string `json:"id"`
	Ip          string `json:"ip"`
	RtmpPort    int    `json:"rtmp_port"`
	App         string `json:"app"`
	Stream      string `json:"stream"`
	RtmpUrl     string `json:"rtmp_url"`
	Ssrc        int    `json:"ssrc"`
	RtpPort     int    `json:"rtp_port"`
	PortMode    string `json:"port_mode"`
	RtpPeerPort int    `json:"rtp_peer_port"`
	RtpPeerIp   string `json:"rtp_peer_ip"`
	RecvTime    int    `json:"recv_time"`
	RecvTimeStr string `json:"recv_time_str"`

	GwName      string `json:"gb_gateway"`    // channel 所在的 gbmmgw 的服务地址
	GwClusterIp string `json:"gw_cluster_ip"` // channel 所在的 gbmmgw 的服务地址(集群IP)
}

type ListSessionRsp struct {
	Code int         `json:"code"`
	Data SessionData `json:"data"`
}

type SessionData struct {
	Sessions []*Gb28181Session `json:"sessions"`
}

type ListChannelRsp struct {
	Code int         `json:"code"`
	Data ChannelData `json:"data"`
}

type ChannelData struct {
	Channels []*Gb28181Channel `json:"channels"`
}

type SnapshotRsp struct {
	Code  int    `json:"code"`
	Image string `json:"image"`
}

type RecordRsp struct {
	Code int `json:"code"`
}

// MMGatewayAPI 多媒体网关的API
type MMGatewayAPI interface {
	// ListStreams 获取当前多媒体网关中的所有视频流信息
	ListStreams() ([]Stream, error)
	// KickOffStream 停止并删除多媒体网关中具有指定通道的流
	KickOffStream(channelID string) error
	// ListGB28181Sessions 获取注册到多媒体网关的所有GB28181会话
	ListGB28181Sessions() ([]*Gb28181Session, error)
	// ListGB28181Channels 获取推流到多媒体网关的所有GB28181通道
	ListGB28181Channels() ([]*Gb28181Channel, error)
	// GetGB28181Channel 获取指定的GB28181通道的信息
	GetGB28181Channel(channelID string) (*Gb28181Channel, error)
	// DoSnapshot 获取指定的流地址的当前截图,并以jpeg/base64的形式返回
	DoSnapshot(streamUrl string) (string, error)
	// StartRecord 开始录制指定多媒体网关地址的视频流
	StartRecord(streamID, streamUrl string) error
	// StopRecord 停止录制指定多媒体网关地址的视频流
	StopRecord(streamID, streamUrl string) error
}

type RecordAction string

const (
	APIScheme              = "http"
	APIBasePath            = "/api/v1"
	MinTimeout             = time.Second * 3
	SnapshotJpegDataPrefix = "data:image/jpg;base64," // 多媒体网关截图数据前缀

	StartRecording RecordAction = "enable"  // 开始录制
	StopRecording  RecordAction = "disable" // 停止录制
)

func NewMMGatewayAPI(host string, port int, timeout time.Duration) MMGatewayAPI {
	if timeout < MinTimeout {
		timeout = MinTimeout
	}
	return &client{
		cli: http.Client{
			Timeout: timeout,
		},
		urlPrefix: fmt.Sprintf("%s://%s:%d/%s", APIScheme, host, port, strings.TrimPrefix(APIBasePath, "/")),
	}
}

// client 为多媒体网关的客户端封装
type client struct {
	cli       http.Client
	urlPrefix string
}

func (c *client) ListStreams() ([]Stream, error) {
	url := c.newURL("/streams")
	rsp, err := c.cli.Get(url)
	if err != nil {
		return nil, err
	}
	buf, _ := ioutil.ReadAll(rsp.Body)
	streams := new(ListStreamsRsp)
	if err := json.Unmarshal(buf, streams); err != nil {
		return nil, fmt.Errorf("failed to unmarshal srsStreams, error:%+v", err)
	}
	if streams.Code != 0 {
		return nil, fmt.Errorf("failed to get streams")
	}
	return streams.Streams, nil
}

func (c *client) KickOffStream(cid string) error {
	url := c.newURL(fmt.Sprintf("/clients/%s", cid))
	req, err := http.NewRequest(http.MethodDelete, url, nil)
	if err != nil {
		return err
	}
	rsp, err := c.cli.Do(req)
	if err != nil {
		return err
	}
	buf, _ := ioutil.ReadAll(rsp.Body)
	res := new(KickoffRsp)
	if err := json.Unmarshal(buf, &res); err != nil {
		return stderr.Unmarshal.Cause(err, "failed to unmarshal kickoff response '%s'", buf)
	}
	if res.Code != 0 {
		return stderr.Internal.Error("failed to kickoff the stream: '%s', code is unexpected '%d'", cid, res.Code)
	}
	stdlog.Infof("success to kickoff the stream: '%s'", cid)
	return nil
}

// ListGB28181Sessions 获取流媒体服务器中已经注册Session列表
// 接口响应结果格式通常如下所示：
// {
//  "code": 0,
//  "data": {
//    "sessions": [
//      {
//        "id": "34020000001320000249",
//        "device_sumnum": 1,
//        "devices": [
//          {
//            "device_id": "34020000001320000255",
//            "device_status": "OFF",
//            "device_name": "IPdome",
//            "device_parental": "0",
//            "device_manufacturer": "Hikvision",
//            "is_directory": false,
//            "invite_status": "Unknow",
//            "invite_time": 0
//          }
//        ]
//      },
//      {
//        "id": "34020000001320000255",
//        "device_sumnum": 1,
//        "devices": [
//          {
//            "device_id": "34020000001320000255",
//            "device_status": "ON",
//            "device_name": "IPdome",
//            "device_parental": "0",
//            "device_manufacturer": "Hikvision",
//            "is_directory": false,
//            "invite_status": "InviteOk",
//            "invite_time": 1663432722
//          }
//        ]
//      }
//    ]
//  }
//}
func (c *client) ListGB28181Sessions() ([]*Gb28181Session, error) {
	url := c.newURL("gb28181?action=sip_query_session")
	rsp, err := c.cli.Get(url)
	if err != nil {
		return nil, err
	}
	body, _ := ioutil.ReadAll(rsp.Body)
	res := new(ListSessionRsp)
	if err := json.Unmarshal(body, res); err != nil {
		return nil, err
	}
	if res.Code != 0 {
		return nil, fmt.Errorf("failed to list gb28181 sessions, code=%d", res.Code)
	}
	return res.Data.Sessions, nil
}

func (c *client) GetGB28181Channel(channelID string) (*Gb28181Channel, error) {
	// do http req
	url := c.newURL(fmt.Sprintf("gb28181?action=query_channel&id=%s", channelID))
	resp, err := c.cli.Get(url)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get channels using url '%s'", url)
	}

	chs, err := parseChannels(resp)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to parse channels")
	}
	if len(chs) < 1 {
		return nil, stderr.MMGatewayAPIError.Error("gb28181 chanel '%s' not found", channelID)
	}
	if chs[0].RtpPeerIp == "" {
		return nil, fmt.Errorf("the rtpPeerIP of channel '%s' is empty", channelID)
	}
	return chs[0], nil
}

func (c *client) ListGB28181Channels() ([]*Gb28181Channel, error) {
	// do http req
	url := c.newURL("gb28181?action=query_channel")
	resp, err := c.cli.Get(url)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get channels using url '%s'", url)
	}
	return parseChannels(resp)
}

func parseChannels(resp *http.Response) ([]*Gb28181Channel, error) {
	res := new(ListChannelRsp)
	body, _ := ioutil.ReadAll(resp.Body)
	if err := json.Unmarshal(body, res); err != nil {
		return nil, stderr.Unmarshal.Cause(err, "failed to unmarshal list channel response '%s'", body)
	}
	if res.Code != 0 {
		return nil, stderr.MMGatewayAPIError.Error("failed to get gb28181 channel, code=%d", res.Code)
	}
	return res.Data.Channels, nil
}

func (c *client) StartRecord(streamID, streamUrl string) error {
	return c.optRecord(StartRecording, streamID, streamUrl)
}

func (c *client) StopRecord(streamID, streamUrl string) error {
	return c.optRecord(StopRecording, streamID, streamUrl)
}

func (c *client) DoSnapshot(streamUrl string) (string, error) {
	url := c.newURL(fmt.Sprintf("snapshots?url=%s", streamUrl))
	rsp, err := c.cli.Get(url)
	if err != nil {
		return "", stderr.MMGatewayAPIError.Cause(err, "failed to do snapshot")
	}
	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return "", stderr.MMGatewayAPIError.Cause(err, "failed to read snapshot response body")
	}
	res := new(SnapshotRsp)
	if err := json.Unmarshal(body, res); err != nil {
		return "", stderr.MMGatewayAPIError.Cause(err, "failed to unmarshal rsp body '%s'", body)
	}
	if res.Code != 0 {
		return "", stderr.MMGatewayAPIError.Error("failed to snapshot, errCode:%d", res.Code)
	}
	return SnapshotJpegDataPrefix + res.Image, nil
}

func (c *client) optRecord(act RecordAction, streamID, streamUrl string) error {
	url := c.newURL(fmt.Sprintf("/dvr?vhost=__defaultVhost__&action=%s&stream=%s&url=%s", act, streamID, streamUrl))
	rsp, err := c.cli.Get(url)
	if err != nil {
		return stderr.MMGatewayAPIError.Cause(err, "failed to do http request with url '%s'", url)
	}

	body, _ := ioutil.ReadAll(rsp.Body)
	res := new(RecordRsp)
	if err := json.Unmarshal(body, res); err != nil {
		return stderr.MMGatewayAPIError.Cause(err, "failed to unmarshal record response body '%s'", body)
	}
	if res.Code != 0 {
		return stderr.MMGatewayAPIError.Error("failed to %s device record, code=%d", act, res.Code)
	}
	stdlog.Infof("success to %s device record by url:%s", act, url)
	return nil
}

func (c *client) newURL(path string) string {
	return strings.TrimSuffix(c.urlPrefix, "/") + "/" + strings.TrimPrefix(path, "/")
}

const GB28181Prefix = "gb28181://"

// NewGB28181Url 返回支持自动发送Invite请求的的GB28181 RTMP 视频播放地址
// e.g. rtmp://*************:1935/preview/34020000002000000001@34020000001320000255
func NewGB28181Url(host string, port int, sessionID, deviceID string, autoInvite bool) string {
	gbUrl := fmt.Sprintf("rtmp://%s:%d/gb28181/%s", host, port, deviceID)
	if autoInvite {
		// 开启自动Invite时，需要在url中告知SRS具体的 sessionID channelID
		gbUrl = fmt.Sprintf("%s?url=%s", gbUrl, base64.URLEncoding.EncodeToString([]byte(GB28181Prefix+sessionID+"@"+deviceID)))
	}
	return gbUrl
}
