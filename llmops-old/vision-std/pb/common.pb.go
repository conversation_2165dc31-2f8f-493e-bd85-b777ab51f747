// Code generated by protoc-gen-go. DO NOT EDIT.
// source: common.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 系统消息, 用于日志中的关键信息的识别, 存储与展示
type SysMessage struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Level                string   `protobuf:"bytes,2,opt,name=level,proto3" json:"level,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Module               string   `protobuf:"bytes,4,opt,name=module,proto3" json:"module,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SysMessage) Reset()         { *m = SysMessage{} }
func (m *SysMessage) String() string { return proto.CompactTextString(m) }
func (*SysMessage) ProtoMessage()    {}
func (*SysMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{0}
}

func (m *SysMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SysMessage.Unmarshal(m, b)
}
func (m *SysMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SysMessage.Marshal(b, m, deterministic)
}
func (m *SysMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SysMessage.Merge(m, src)
}
func (m *SysMessage) XXX_Size() int {
	return xxx_messageInfo_SysMessage.Size(m)
}
func (m *SysMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_SysMessage.DiscardUnknown(m)
}

var xxx_messageInfo_SysMessage proto.InternalMessageInfo

func (m *SysMessage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SysMessage) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *SysMessage) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SysMessage) GetModule() string {
	if m != nil {
		return m.Module
	}
	return ""
}

// 系统右下角弹窗信息
type Notification struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Level                string   `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	Detail               string   `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	Time                 int64    `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Notification) Reset()         { *m = Notification{} }
func (m *Notification) String() string { return proto.CompactTextString(m) }
func (*Notification) ProtoMessage()    {}
func (*Notification) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{1}
}

func (m *Notification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Notification.Unmarshal(m, b)
}
func (m *Notification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Notification.Marshal(b, m, deterministic)
}
func (m *Notification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Notification.Merge(m, src)
}
func (m *Notification) XXX_Size() int {
	return xxx_messageInfo_Notification.Size(m)
}
func (m *Notification) XXX_DiscardUnknown() {
	xxx_messageInfo_Notification.DiscardUnknown(m)
}

var xxx_messageInfo_Notification proto.InternalMessageInfo

func (m *Notification) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Notification) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Notification) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *Notification) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *Notification) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *Notification) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func init() {
	proto.RegisterType((*SysMessage)(nil), "pb.SysMessage")
	proto.RegisterType((*Notification)(nil), "pb.Notification")
}

func init() { proto.RegisterFile("common.proto", fileDescriptor_555bd8c177793206) }

var fileDescriptor_555bd8c177793206 = []byte{
	// 191 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0xd0, 0xbf, 0x6a, 0xc3, 0x30,
	0x10, 0xc7, 0x71, 0x24, 0xff, 0x29, 0x3d, 0x4c, 0x07, 0x51, 0x8a, 0x46, 0xe3, 0xc9, 0x53, 0x97,
	0x3e, 0x47, 0x3b, 0xb8, 0x4f, 0x60, 0x5b, 0xd7, 0x72, 0x20, 0xe9, 0x4c, 0x7c, 0x09, 0xe4, 0x2d,
	0xf2, 0xc8, 0x21, 0x92, 0x4d, 0x20, 0x64, 0xd3, 0xf7, 0xa7, 0xe1, 0x23, 0x04, 0xcd, 0xcc, 0x21,
	0x70, 0xfc, 0x5c, 0x0e, 0x2c, 0x6c, 0xf4, 0x32, 0x75, 0x0e, 0xe0, 0xf7, 0xbc, 0x7e, 0xe3, 0xba,
	0x8e, 0xff, 0x68, 0xde, 0x40, 0x93, 0xb3, 0xaa, 0x55, 0xfd, 0xeb, 0xa0, 0xc9, 0x99, 0x77, 0xa8,
	0x3c, 0x9e, 0xd0, 0x5b, 0x9d, 0xa6, 0x1c, 0xc6, 0xc2, 0xcb, 0xcc, 0x51, 0x30, 0x8a, 0x2d, 0xd2,
	0xbe, 0xa7, 0xf9, 0x80, 0x3a, 0xb0, 0x3b, 0x7a, 0xb4, 0x65, 0xba, 0xd8, 0xaa, 0xbb, 0x28, 0x68,
	0x7e, 0x58, 0xe8, 0x8f, 0xe6, 0x51, 0x88, 0xe3, 0x33, 0x48, 0x48, 0x3c, 0xee, 0x50, 0x8a, 0x3b,
	0x5f, 0x3c, 0xf0, 0x21, 0xbf, 0x77, 0x53, 0xf6, 0xbc, 0xf1, 0x0e, 0x65, 0x24, 0x6f, 0xab, 0xcc,
	0xe7, 0x32, 0x06, 0x4a, 0xa1, 0x80, 0xb6, 0x6e, 0x55, 0x5f, 0x0c, 0xe9, 0x3c, 0xd5, 0xe9, 0x0f,
	0xbe, 0xae, 0x01, 0x00, 0x00, 0xff, 0xff, 0x57, 0x37, 0x82, 0x63, 0x13, 0x01, 0x00, 0x00,
}
