syntax = "proto3";

package pb;

// 系统消息, 用于日志中的关键信息的识别, 存储与展示
message SysMessage  {
    string id        =1; // 系统消息ID, 也即发生时间戳(ns)
    string level     =2; // 系统消息级别
    string content   =3; // 系统消息内容
    string module    =4; // 系统消息所属模块, 可能为系统服务或场景应用/模型/函数的实例
}

// 系统右下角弹窗信息
message Notification {
    string id        = 1;
    string title     = 2;
    string level     = 3; // info | warn | error
    string message   = 4; // summary
    string detail    = 5; // detail
    int64  time      = 6; // ms
}

//protoc -I=$GOPATH/src:. --go_out=.  *.proto
