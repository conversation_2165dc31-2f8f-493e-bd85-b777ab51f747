package pb

import (
	"fmt"
	"strings"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/go-connections/nat"
	"github.com/docker/go-units"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"
	corev1 "k8s.io/api/core/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

// WorkerState 对应 k8s pod state
type WorkerState = string

type BootNamespace = string
type BootServiceName = string

var BootNamespaces = []BootNamespace{string(stdhub.RscTypeModelDeployment), string(stdhub.RscTypeSceneInstance)}

const (
	// Namespace.ServiceId
	BootServiceNameTemplate = "%s-%s"

	WorkerCreated    WorkerState = "created"    // created A container that has been created (e.g. with docker create) but not started
	WorkerRunning    WorkerState = "running"    // running A currently running container
	WorkerNotReady   WorkerState = "notready"   // Pod 中存在容器未处于运行状态
	WorkerPaused     WorkerState = "paused"     // paused A container whose processes have been paused
	WorkerRestarting WorkerState = "restarting" // restarting A container that is in the process of being restarted
	WorkerRemoving   WorkerState = "removing"   //
	WorkerExited     WorkerState = "exited"     // exited A container that ran and completed ("stopped" in other contexts, although a created container is technically also "stopped")
	WorkerDead       WorkerState = "dead"       // dead A container that the daemon tried and failed to stop (usually due to a busy device or resource used by the container)
	WorkerSuccess    WorkerState = "successful"
	WorkerFailure    WorkerState = "failure"
)

// service type 字段,对应k8s Kind 如 Job, Deployment等
type ServiceType string

const (
	K8sJob         ServiceType = "Job"
	K8sStatefulSet ServiceType = "StatefulSet"
	K8sDeployment  ServiceType = "Deployment"
	// 适用于docker
	None ServiceType = "None"
)

// BootService is a server running on some containers.
type BootService struct {
	// Name 须符合 k8s 资源命名规范
	// a DNS-1035 label must consist of lower case alphanumeric characters or '-',
	// start with an alphabetic character, and end with an alphanumeric character
	// e.g. 'my-name', or 'abc-123', regex used for validation is '[a-z]([-a-z0-9]*[a-z0-9])?'
	Name string `json:"name,omitempty"`
	// Namespace
	Namespace string `json:"namespace,omitempty"`
	// TargetNodes 规定了资源调度的节点范围
	TargetNodes []string `json:"target_nodes"`
	// ReplicaMutex 副本间是否互斥， 可以用于避免同节点部署调度多个Pod
	ReplicaMutex bool `json:"replica_mutex"`
	// Replica 为预期的目标节点数量（目前每个节点上最多一个Pod）
	Replica            int32         `json:"replica,omitempty"`
	Config             *BootConfig   `json:"config,omitempty"`
	InitContainer      *BootConfig   `json:"init_container,omitempty"`
	RefKey             stdhub.RscKey `json:"ref_key"`
	SideCars           []*BootConfig `json:"side_cars"`
	Type               ServiceType   `json:"type"`
	ServiceAccountName string        `json:"service_account_name"`
}

// FormatNameAsK8SRsc 将常见的符号替换为k8s合法的形式
// failed to start scene instance task, cause服务内部错误 :
// failed to create or update boot service, rollback it :
// failed to create or update k8s service :
//
//	 Service "task-ins-c9bospam0ud-danger_regio" is invalid: [
//	   metadata.name: Invalid value: "task-ins-c9bospam0ud-danger_regio":
//	  	    a DNS-1035 label must consist of lower case alphanumeric characters or '-', start with an alphabetic character, and end with an alphanumeric character (e.g. 'my-name', or 'abc-123', regex used for validation is '[a-z]([-a-z0-9]*[a-z0-9])?'),
//	   metadata.labels: Invalid value: "/thinger/local-test-edge/scene_instance/SCENE_INSTANCE-c9bospam0udkbbp8rpn0":
//	  		must be no more than 63 characters,
//	   metadata.labels: Invalid value: "/thinger/local-test-edge/scene_instance/SCENE_INSTANCE-c9bospam0udkbbp8rpn0":
//	  		a valid label must be an empty string or consist of alphanumeric characters, '-', '_' or '.', and must start and end with an alphanumeric character (e.g. 'MyValue', or 'my_value', or '12345', regex used for validation is '(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])?'),
//	   spec.selector: Invalid value: "/thinger/local-test-edge/scene_instance/SCENE_INSTANCE-c9bospam0udkbbp8rpn0":
//			must be no more than 63 characters,
//	   spec.selector: Invalid value: "/thinger/local-test-edge/scene_instance/SCENE_INSTANCE-c9bospam0udkbbp8rpn0":
//	  		a valid label must be an empty string or consist of alphanumeric characters, '-', '_' or '.', and must start and end with an alphanumeric character (e.g. 'MyValue', or 'my_value', or '12345', regex used for validation is '(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])?')]
func FormatNameAsK8SRsc(id string) string {
	// to lower case
	id = strings.ToLower(id)
	// replace all char to '-'
	id = strings.NewReplacer(".", "-", "_", "-").Replace(id)
	return id
}

// Event 对应k8s pod event
// e.g.
//
//	src=&EventSource{Component:,Host:,},
//	name=model-deployment-c9r664am0udp1736mllg-65ccb8f88-bnb4l,
//	reason=FailedScheduling,
//	msg=0/1 nodes are available: 1 Insufficient cpu.
type Event struct {
	Source  string `json:"source"`
	Reason  string `json:"reason"`
	Message string `json:"message"`
	Name    string `json:"name"` // who's event
}

func (e Event) String() string {
	// 可以精简Event String内容
	// Deployment 创建的典型name格式为 ${dp-id}-${rand-rs-id}-${rand-pod-id}
	// e.g. model-deployment-c9r664am0udp1736mllg-65ccb8f88-bnb4l， 因此可仅保留后面 pod id 部分。
	idx := strings.LastIndex(e.Name, "-")
	name := e.Name
	if idx > 0 {
		name = name[idx+1:]
	}
	return fmt.Sprintf("name=%s, reason=%s, msg=%s", name, e.Reason, e.Message)
}

type BasicStatusInfo struct {
	Expect int32    `json:"expect"` // 期望个数
	Ready  int32    `json:"ready"`  // 实际个数
	Events []*Event `json:"events"` // 事件
}

func (s BasicStatusInfo) EventsString() string {
	evts := make([]string, 0)
	for _, evt := range s.Events {
		if evt == nil {
			continue
		}
		evts = append(evts, evt.String())
	}
	return strings.Join(evts, ";")
}

func (s *BasicStatusInfo) AddEvt(source, reason, message, name string) {
	s.Events = append(s.Events, &Event{
		Source:  source,
		Reason:  reason,
		Message: message,
		Name:    name,
	})
}

// 代表一个副本管理器(statefulset/deployment)的状态
type ServiceStats struct {
	Name        string          `json:"name"`
	RefKey      *stdhub.RscKey  `json:"ref_key"` // boot service 相关资源的ID
	StatusInfo  BasicStatusInfo `json:"status_info"`
	WorkerStats []*WorkerStats  `json:"worker_stats"` // 各pod metric
}

// 代表一个pod的状态
type WorkerStats struct {
	Name     string `json:"name"` // FUNC.function1-1， name即id
	NodeName string `json:"node_name"`
	// status info
	State WorkerState `json:"state"`

	Message string `json:"message"`

	// statistics info
	Stats *ContainerStats `json:"stats"`
}

// CONTAINER ID  NAME                 CPU %  MEM USAGE / LIMIT     MEM %   NET I/O             BLOCK I/O
// 200ea3d9426e  thinger-chronograf   0.00%  5.059MiB / 7.767GiB   0.06%   17.1kB / 1.85kB     62.7MB / 172kB
type ContainerStats struct {
	CpuPercent float64 `json:"cpu_percent"`
	MemPercent float64 `json:"mem_percent"`
	MemUsage   int64   `json:"mem_usage"`
	MemLimit   int64   `json:"mem_limit"`
	NetRecv    int64   `json:"net_recv"`
	NetSent    int64   `json:"net_sent"`
	BlockRead  int64   `json:"block_read"`
	BlockWrite int64   `json:"block_write"`
	PidsNum    int64   `json:"pids_num"`
}

type BootConfig struct {
	Image               string          `json:"image"`
	PullOnExist         bool            `json:"pull_on_exist"`
	ImageRepoBasicToken string          `json:"image_repo_basic_token"` // 如果有，后端根据配置自动填充
	Network             string          `json:"network"`
	Privileged          bool            `json:"privileged"`
	Resources           *ResourceConfig `json:"resources"`
	Restart             *RestartConfig  `json:"restart"`
	LogConfig           *LogConfig      `json:"log_config"`
	Cmd                 []string        `json:"cmd"`
	Args                []string        `json:"args"`
	// ExposedPorts 控制将容器内的端口随机映射到一个可用的 NodePort 上.
	// Network == host 情况下，Pod将直接使用 host network，并将端口直接通过所在节点主机网络暴露出来；
	ExposedPorts  []string `json:"exposed_ports"`   // 当前服务占用的端口   ["12345/udp", "12346", "123457/tcp"]
	ExposedAsHost bool     `json:"exposed_as_host"` // Pod暴露的端口是否以host模式进行暴露
	// Ports 在 K8S 部署情况下，将容器内端口（冒号右侧）映射到指定的 NodePort (冒号左侧)
	Ports   []string          `json:"ports"` // 端口映射 ["12345:8080"]
	Devices []string          `json:"devices"`
	Envs    map[string]string `json:"envs"`
	Volumes map[string]string `json:"volumes"` // 主机到容器的映射
	Labels  map[string]string `json:"labels"`

	// k8s 相关配置
	ImagePullSecrets []string             `json:"image_pull_secrets"` // 用于 Pod Pull 镜像时,与Registry的认证凭证名称（Secret Name），依赖于集群内已经存在的 docker-registry secret, 详细可参考https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
	ExtVolumes       []corev1.Volume      `json:"ext_volumes"`        // 额外的Volume配置 e.g. PVC
	ExtVolumeMounts  []corev1.VolumeMount `json:"ext_volume_mounts"`  // 额外的容器VolumeMount配置
	NodeAffinity     *corev1.NodeAffinity `json:"node_affinity,omitempty" yaml:"node_affinity"`
}
type ResourceConfig struct {
	Gpu    *GPU    `json:"gpu"`
	Cpu    *CPU    `json:"cpu"`
	Memory *Memory `json:"memory"`
}

// Memory 最大可用内存限制
type Memory struct {
	Limit string `json:"limit"`
	Swap  string `json:"swap"`
}

// GPU 是否分配GPU
type GPU struct {
	Enable bool `json:"enable,omitempty"`
}

// CPU 核数
type CPU struct {
	Cpus float32 `json:"cpus"`
}

type RestartConfig struct {
	MaxRetry int32                  `protobuf:"varint,1,opt,name=max_retry,json=maxRetry,proto3" json:"max_retry,omitempty"`
	Policy   RestartConfig_Policy   `protobuf:"varint,2,opt,name=policy,proto3,enum=RestartConfig_Policy" json:"policy,omitempty"`
	Backoff  *RestartConfig_Backoff `protobuf:"bytes,3,opt,name=backoff,proto3" json:"backoff,omitempty"`
}
type RestartConfig_Policy = int32

const (
	RestartConfig_No        RestartConfig_Policy = 0
	RestartConfig_Always    RestartConfig_Policy = 1
	RestartConfig_OnFailure RestartConfig_Policy = 2
)

type RestartConfig_Backoff struct {
	Min    string  `protobuf:"bytes,1,opt,name=min,proto3" json:"min,omitempty"`
	Max    string  `protobuf:"bytes,2,opt,name=max,proto3" json:"max,omitempty"`
	Factor float64 `protobuf:"fixed64,3,opt,name=factor,proto3" json:"factor,omitempty"`
}

type LogConfig struct {
	DriverType string            `json:"driver_type"`
	Config     map[string]string `json:"config"`
}

// DefaultFluentdLogConfig will return a LogConfig of BootConfig with following content :
//
//	driver: "fluentd"
//	options:
//	  tag: "{{.Name}}"
//	  fluentd-address: "localhost:30224"
func DefaultFluentdLogConfig() *LogConfig {
	return FluentdLogConfig("localhost:30224")
}

func FluentdLogConfig(addr string) *LogConfig {
	return &LogConfig{
		DriverType: "fluentd",
		Config: map[string]string{
			"tag":             "{{.Name}}",
			"fluentd-address": addr,
		},
	}
}

// DefaultJsonFileLogConfig will return a LogConfig of BootConfig with following content :
//
//	driver: "json-file"
//	options:
//	  max-size: "1024m"
func DefaultJsonFileLogConfig() *LogConfig {
	return &LogConfig{
		DriverType: "json-file",
		Config: map[string]string{
			"max-size": "1024m",
		},
	}
}

// DefaultRestartConfig will return a restart config which means boot will restart the service
// forever at the following interval:
// 1s*2^0, 1s*2^1, 1s*2^2, ..., 5m, 5m
func DefaultRestartConfig() *RestartConfig {
	return &RestartConfig{
		MaxRetry: 5,
		Policy:   RestartConfig_Always,
		Backoff: &RestartConfig_Backoff{
			Min:    "1s",
			Max:    "5m",
			Factor: 2,
		},
	}
}

type (
	Runtime   = string
	NetworkId = string
	// NetworkName 为BootService支持的网络模式
	NetworkName = string

	// CtnLabelKey defined specific label keys for those containers started as BootService.
	CtnLabelKey = string
)

const (
	RuntimeNvidia Runtime = "nvidia"
	RuntimeRunc   Runtime = "runc"

	NetworkHost   NetworkName = "host"        // 使用宿主机网络
	NetworkBridge NetworkName = "vision-node" // default, 使用平台服务所在的桥接网络

	DefaultCPU                = 1.5
	DefaultReplica            = 1
	DefaultMemLimit           = "300m"
	DefaultMemSwap            = "500m"
	DefaultMaxRetry           = 10
	DefaultMinRetryInterval   = "5s"
	DefaultMaxRetryInterval   = "2m"
	DefaultIntervalMultiplier = 2.0

	ServiceName CtnLabelKey = "thinger.service"
	NameSpace   CtnLabelKey = "thinger.service-ns"
	MD5Checksum CtnLabelKey = "thinger.service-md5"
)

var (
	DefaultRestart = &RestartConfig{
		MaxRetry: 0, // maximum retry count cannot be used with restart policy 'always'
		Policy:   RestartConfig_Always,
		Backoff: &RestartConfig_Backoff{
			Min:    DefaultMinRetryInterval,
			Max:    DefaultMaxRetryInterval,
			Factor: DefaultIntervalMultiplier,
		},
	}

	DefaultResource = &ResourceConfig{
		Cpu:    &CPU{Cpus: DefaultCPU},
		Gpu:    &GPU{Enable: false},
		Memory: &Memory{Limit: DefaultMemLimit, Swap: DefaultMemSwap},
	}

	DefaultLogConfig = DefaultFluentdLogConfig()
)

type ContainerConfigs struct {
	Config        *container.Config
	HostConfig    *container.HostConfig
	NetworkConfig *network.NetworkingConfig
	Platform      *v1.Platform
}

// DockerCtnConfigs 返回 BootService对应的 container configs
func (s *BootService) DockerCtnConfigs() (*ContainerConfigs, error) {
	cfg := s.Config
	exposedPorts, portBindings, err := nat.ParsePortSpecs(cfg.Ports)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to parse port specs: %+v", cfg.Ports)
	}

	if s.Replica < 1 {
		return nil, stderr.Internal.Error("the replica of %s must greater than 0", s.Name)
	}

	if s.Replica > 1 && len(portBindings) > 0 {
		return nil, stderr.Internal.Error("not allowd to use hostPort when replica > 1")
	}

	labels := s.specificLabels()
	for k, v := range cfg.Labels {
		labels[k] = v
	}

	config := &container.Config{
		Labels:       labels,
		Image:        cfg.Image,
		ExposedPorts: exposedPorts,
		Cmd:          cfg.Cmd,
		Env:          utils.AppendEnv(cfg.Envs, false),
	}

	mem, err := units.RAMInBytes(cfg.Resources.Memory.Limit)
	if err != nil {
		return nil, err
	}
	memSwap, err := units.RAMInBytes(cfg.Resources.Memory.Swap)
	if err != nil {
		return nil, err
	}

	binds := make([]string, 0)
	for src, target := range cfg.Volumes {
		binds = append(binds, fmt.Sprintf("%s:%s", src, target))
	}

	devices := make([]container.DeviceMapping, 0)
	for _, dvc := range cfg.Devices {
		devices = append(devices, container.DeviceMapping{
			PathOnHost:        dvc,
			PathInContainer:   dvc,
			CgroupPermissions: "rmw",
		})
	}

	// set init true to avoid cannot stop container immediately.
	// https://unix.stackexchange.com/questions/457649/unable-to-kill-process-with-pid-1-in-docker-container
	init := true
	hostConfig := &container.HostConfig{
		Binds:        binds,
		PortBindings: portBindings,
		//RestartPolicy: container.RestartPolicy{
		//	Name:              restartPolicy[cfg.Restart.Policy],
		//	MaximumRetryCount: int(cfg.Restart.MaxRetry),
		//},
		Resources: container.Resources{
			NanoCPUs:   int64(cfg.Resources.Cpu.Cpus * 1e9),
			Memory:     mem,
			MemorySwap: memSwap,
			Devices:    devices,
		},
		LogConfig: container.LogConfig{
			Type:   cfg.LogConfig.DriverType,
			Config: cfg.LogConfig.Config,
		},
		Init:       &init,
		Privileged: cfg.Privileged,
	}

	if cfg.Resources.Gpu.Enable {
		hostConfig.Runtime = RuntimeNvidia
	} else {
		hostConfig.Runtime = RuntimeRunc
	}

	if cfg.Network == "" {
		cfg.Network = NetworkBridge
	}
	aliases := []string{s.Name}
	if cfg.Network == NetworkHost {
		// Network-scoped alias is supported only for containers in user defined networks
		aliases = nil
	}
	networkConfig := &network.NetworkingConfig{
		EndpointsConfig: map[string]*network.EndpointSettings{
			cfg.Network: {
				Aliases:   aliases,
				NetworkID: NetworkBridge,
			},
		},
	}
	return &ContainerConfigs{
		Config:        config,
		HostConfig:    hostConfig,
		NetworkConfig: networkConfig,
		Platform:      nil,
	}, nil
}

// specificLabels will return the specific labels of a boot service
// which including ServiceName, ServiceNameSpace and the md5 checksum
// of the config.
func (s *BootService) specificLabels() map[string]string {
	return map[CtnLabelKey]string{
		ServiceName: s.Name,
		NameSpace:   s.Namespace,
		MD5Checksum: toolkit.MD5(s.Config),
	}
}

func (s *BootService) Validate() error {
	if s == nil {
		return stderr.InvalidParam.Error("empty boot service")
	}
	if s.Name == "" {
		return stderr.InvalidParam.Error("empty service name")
	}
	if s.Namespace == "" {
		return stderr.InvalidParam.Error("empty service name namespace")
	}
	if err := s.RefKey.Valid(); err != nil {
		return stderr.InvalidParam.Cause(err, "empty service reference key")
	}
	if s.Config == nil {
		return stderr.InvalidParam.Error("empty boot config")
	}
	if s.Config.Image == "" {
		return stderr.InvalidParam.Error("empty service image")
	}
	return nil
}

func (s *BootService) ApplyDefault() {
	if s.Replica == 0 {
		s.Replica = DefaultReplica
		stdlog.Debugf("use default replica %d", s.Replica)
	}

	cfg := s.Config
	if cfg.Network == "" {
		cfg.Network = NetworkBridge
		stdlog.Debugf("use default network %s", cfg.Network)
	}

	if cfg.LogConfig == nil {
		cfg.LogConfig = DefaultLogConfig
		stdlog.Debugf("use default log config %+v", cfg.LogConfig)
	}

	if cfg.Restart == nil {
		cfg.Restart = DefaultRestart
		stdlog.Debugf("use default restart policy %+v", cfg.Restart)
	} else {
		// MaximumRetryCount is only work when the restart policy is on-failure
		if cfg.Restart.Policy == RestartConfig_OnFailure && cfg.Restart.MaxRetry == 0 {
			cfg.Restart.MaxRetry = DefaultMaxRetry
		}
		if cfg.Restart.Policy == RestartConfig_Always {
			cfg.Restart.MaxRetry = 0
		}
		if cfg.Restart.Backoff == nil {
			cfg.Restart.Backoff = DefaultRestart.Backoff
			stdlog.Debugf("use default back off %+v", cfg.Restart.Backoff)
		}
	}
	if cfg.Resources == nil {
		cfg.Resources = DefaultResource
		stdlog.Debugf("use default resource %+v", cfg.Resources)
	} else {
		// it is painful
		if cfg.Resources.Cpu == nil {
			cfg.Resources.Cpu = DefaultResource.Cpu
			stdlog.Debugf("use default resource cpu %+v", cfg.Resources.Cpu)
		}
		if cfg.Resources.Gpu == nil {
			cfg.Resources.Gpu = DefaultResource.Gpu
			stdlog.Debugf("use default resource gpu %+v", cfg.Resources.Gpu)
		}
		if cfg.Resources.Memory == nil {
			cfg.Resources.Memory = DefaultResource.Memory
			stdlog.Debugf("use default resource memory %+v", cfg.Resources.Memory)
		}
	}
}

func NewExposedUDPPort(port int) string {
	return fmt.Sprintf("%d/udp", port)
}
