package pb

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
)

func TestBootServiceDockerCtnConfigs(t *testing.T) {
	info := &BootService{
		Name:        "test_ctn",
		Namespace:   "FUNC",
		Replica:     2,
		Config: &BootConfig{
			Image:      "172.16.1.41:5000/modelserver/vehicle:20190809",
			Network:    "thinger-edge",
			Privileged: false,
			Resources: &ResourceConfig{
				Gpu: &GPU{
					Enable: true,
				},
				Cpu: &CPU{
					Cpus: 0,
				},
				Memory: &Memory{
					Limit: "0B",
					Swap:  "0B",
				},
			},
			Restart: &RestartConfig{
				MaxRetry: 0,
				Policy:   RestartConfig_Always,
				Backoff:  &RestartConfig_Backoff{Min: "1s", Max: "5m", Factor: 2.0},
			},
			LogConfig: &LogConfig{
				DriverType: "json-file",
				Config:     map[string]string{},
			},
			Cmd: []string{
				"/bin/bash",
				"-c",
				"python /workspace/demos/server_start.py /workspace/demos/Config.ini /workspace/demos/register.ini",
			},
			Ports:   []string{},
			Devices: []string{},
			Envs: map[string]string{
				"K8SServiceInnerPort": "8080",
				//"PATH":                       "/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
				"CUDA_VERSION":               "10.0.130",
				"CUDA_PKG_VERSION":           "10-0=10.0.130-1",
				"LD_LIBRARY_PATH":            "/usr/local/cuda/extras/CUPTI/lib64:/usr/local/nvidia/lib:/usr/local/nvidia/lib64",
				"NVIDIA_VISIBLE_DEVICES":     "all",
				"NVIDIA_DRIVER_CAPABILITIES": "compute,utility",
				"NVIDIA_REQUIRE_CUDA":        "cuda>=10.0 brand=tesla,driver>=384,driver<385 brand=tesla,driver>=410,driver<411",
				"LANG":                       "C.UTF-8",
			},
			Volumes: map[string]string{},
			Labels: map[string]string{
				"api_specs":          "[{\"http_path\":\"/api/vehicle_detection\",\"http_method\":\"POST\",\"req_template\":\"{\\\"client_id\\\":\\\"default\\\",\\\"params\\\":[{\\\"type\\\":\\\"jpeg\\\",\\\"data\\\":\\\"%%image%%\\\"}]}\",\"rsp_meta\":\"{\\\"positions\\\": [{\\\"id\\\": \\\"vehicle\\\", \\\"name\\\": \\\"车辆框\\\", \\\"path\\\": \\\"/positions/vehicle\\\"}]}\"},{\"http_path\":\"/api/vehicle_cmt\",\"http_method\":\"POST\",\"req_template\":\"{\\\"client_id\\\":\\\"default\\\",\\\"params\\\":[{\\\"type\\\":\\\"jpeg\\\",\\\"data\\\":\\\"%%image%%\\\"}]}\",\"rsp_meta\":\"{\\\"attributes\\\": [{\\\"id\\\":\\\"color\\\",\\\"name\\\": \\\"颜色\\\",\\\"path\\\": \\\"/attributes/color\\\",\\\"type\\\": \\\"string\\\"},{\\\"id\\\": \\\"make\\\", \\\"name\\\": \\\"制造商\\\",\\\"path\\\": \\\"/attributes/make\\\",\\\"type\\\": \\\"string\\\"}, {\\\"id\\\":\\\"type\\\",\\\"name\\\":\\\"车型\\\",\\\"path\\\": \\\"/attributes/type\\\",\\\"type\\\":\\\"string\\\"}]}\"},{\"http_path\":\"/api/plate_detection\",\"http_method\":\"POST\",\"req_template\":\"{\\\"client_id\\\":\\\"default\\\",\\\"params\\\":[{\\\"type\\\":\\\"jpeg\\\",\\\"data\\\":\\\"%%image%%\\\"}]}\",\"rsp_meta\":\"{\\\"positions\\\": [{\\\"id\\\": \\\"license\\\",\\\"name\\\": \\\"车牌框\\\",\\\"path\\\": \\\"/positions/license\\\"}]}\"},{\"http_path\":\"/api/plate_recognition\",\"http_method\":\"POST\",\"req_template\":\"{\\\"client_id\\\":\\\"default\\\",\\\"params\\\":[{\\\"type\\\":\\\"jpeg\\\",\\\"data\\\":\\\"%%image%%\\\"}]}\",\"rsp_meta\":\"{\\\"attributes\\\": [{\\\"id\\\": \\\"ocr\\\",\\\"name\\\": \\\"字符识别\\\",\\\"path\\\": \\\"/attributes/ocr\\\",\\\"type\\\": \\\"string\\\"}]}\"}]",
				"maintainer":         "NVIDIA CORPORATION <<EMAIL>>",
				"thinger.service":    "MODEL.APP-bme92fcm1hde86l9t0m0",
				"thinger.service-ns": "MODEL",
			},
		},
	}

	_, err := info.DockerCtnConfigs()
	if err != nil {
		t.Fatal(err)
	}
}

func TestDefaultFluentdLogConfig(t *testing.T) {

	a := &BootConfig{
		Image:     "of",
		Network:   "ok",
		Resources: DefaultResource,
		Restart:   DefaultRestart,
	}

	bs, _ := json.Marshal(a)
	fmt.Println(string(bs))
	b := base64.StdEncoding.EncodeToString(bs)
	fmt.Println(b)

	x, _ := base64.StdEncoding.DecodeString(b)
	fmt.Println(string(x))

	js := "{\\\"network\\\":\\\"thinger-edge\\\",\\\"resources\\\":{\\\"gpu\\\":{},\\\"cpu\\\":{\\\"cpus\\\":41},\\\"memory\\\":{}},\\\"restart\\\":{\\\"policy\\\":1},\\\"envs\\\":{\\\"PORT\\\":\\\"8080\\\"}}"
	c := &BootConfig{}
	s, err := strconv.Unquote(js)
	if err != nil {
		fmt.Println(err)
		return
	}
	if err := json.Unmarshal([]byte(s), c); err != nil {
		fmt.Println(err)
		return
	}
}
