package stdbatch

import (
	"reflect"
	"testing"
)

func TestBatchTask_GetBatchCreationRecords(t *testing.T) {

	testSuccessRes := SingleOperationResult{
		Index:   0,
		Message: SuccessMessage,
		Row:     []string{"a", "b"},
		Res:     Success,
	}

	testFailedRes := SingleOperationResult{
		Index:   1,
		Message: CanceledMessage,
		Row:     []string{"c", "d"},
		Res:     Failed,
	}
	testTask := BatchTask{
		state:       Done,
		creationRes: []SingleOperationResult{testSuccessRes, testFailedRes},
	}
	tests := []struct {
		name    string
		filter  string
		want    []SingleOperationResult
		wantErr bool
	}{
		{
			name:    "test get success creation records",
			filter:  Success,
			want:    []SingleOperationResult{testSuccessRes},
			wantErr: false,
		}, {
			name:    "test get failed creation records",
			filter:  Failed,
			want:    []SingleOperationResult{testFailedRes},
			wantErr: false,
		}, {
			name:    "test get all creation records",
			filter:  "",
			want:    []SingleOperationResult{testSuccessRes, testFailedRes},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := testTask.GetBatchCreationResults(tt.filter)
			if (err != nil) != tt.wantErr {
				t.Errorf("batchCreationTask.GetBatchCreationResults() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("batchCreationTask.GetBatchCreationResults() = %v, want %v", got, tt.want)
			}
		})
	}
}
