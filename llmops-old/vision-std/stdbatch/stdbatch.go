package stdbatch

import (
	"context"
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

type (
	BatchTaskState       string   // 批量操作任务的整体状态
	SingleOperationState = string // 批量操作任务中单个操作结果
)

const (
	BatchOperationMinCount = 1
	BatchOperationMaxCount = 1000

	Created    BatchTaskState = "Created"    // 文件已上传
	Invalid    BatchTaskState = "Invalid"    // 校验未通过
	Ready      BatchTaskState = "Ready"      // 校验通过, 可以开始执行
	InProgress BatchTaskState = "InProgress" // 执行中
	Done       BatchTaskState = "Done"       // 执行已完成
	Canceled   BatchTaskState = "Canceled"   // 执行过程中取消
	Stopped    BatchTaskState = "Stopped"    // 任务结束(无法再进行任何相关操作)

	Success SingleOperationState = "success" // 创建成功
	Failed  SingleOperationState = "failed"  // 创建失败

	CreationResCol  = "操作结果"  // 获取创建记录时添加的创建结果字段
	SuccessMessage  = "成功"    // 设备添加成功显示的消息
	CanceledMessage = "任务已取消" //  任务取消后显示的消息
)

// BatchTaskValidity is the validity of uploaded csv file to do batch creation.
type BatchTaskValidity struct {
	TaskID string         `json:"task_id"`
	Valid  bool           `json:"valid"`   // 批量任务是否合法
	Total  int            `json:"total"`   // 批量任务所含子项数量
	ErrMsg string         `json:"err_msg"` // 任务非法原因详情
	Header [][]string     `json:"header"`  // 任务资源元信息(列名+描述)
	State  BatchTaskState `json:"state"`
}

type BatchTaskSummary struct {
	Total   int      `json:"total"`
	Success int      `json:"success"`
	Failure int      `json:"failure"`
	ErrMsgs []string `json:"err_msgs"`
}

// SingleOperationResult is the result of each creation in task.
type SingleOperationResult struct {
	Index   int                  `json:"index"`   // 当前设备序号
	Message string               `json:"message"` // 创建结果消息
	Row     []string             `json:"row"`     // 文件中对应的原始数据
	Res     SingleOperationState `json:"res"`     // 创建结果状态 success / failed
}

type Executor interface {
	Total() int
	Header() [][]string
	Fields(idx int) []string                               // Fields 获取第 i 个资源的字段信息
	Exec(idx int) error                                    // Exec 操作第 i 个资源的方法
	SetConfig(cfg any) error                               // SetConfig 更新当前批量任务内容的函数
	HandleResult(taskID string, res SingleOperationResult) // HandleResult 任务操作结果的处理函数
}

type BatchTask struct {
	exec Executor

	id          string                  // 任务ID
	state       BatchTaskState          // 当前任务状态
	ctx         context.Context         // 创建操作时共用的context
	cancelF     context.CancelFunc      // 用于取消所有正在执行的操作
	creationRes []SingleOperationResult // 创建结果列表
	done        chan BatchTaskState     // 所有设备创建完成的信号
}

func NewBatchTask(exec Executor) *BatchTask {
	ctx, cancelF := context.WithCancel(context.Background())
	return &BatchTask{
		id:          toolkit.NewUUID(),
		state:       Created,
		ctx:         ctx,
		cancelF:     cancelF,
		creationRes: nil,
		exec:        exec,
		done:        make(chan BatchTaskState, 1),
	}
}

func (t *BatchTask) SetExecutor(executor Executor) {
	t.exec = executor
	t.creationRes = make([]SingleOperationResult, executor.Total())
}

func (t *BatchTask) ID() string {
	if t.id == "" {
		t.id = toolkit.NewUUID()
	}
	return t.id
}

// SetTaskConfig 更新批量任务的配置
func (t *BatchTask) SetTaskConfig(cfg any) BatchTaskValidity {
	errMsg := ""
	if t.state != Created && t.state != Ready {
		errMsg = fmt.Sprintf("batch task only can be set in state [%s, %s]", Created, Ready)
	} else {
		err := t.exec.SetConfig(cfg)
		if err != nil {
			errMsg = err.Error()
		}
	}
	validity := BatchTaskValidity{
		TaskID: t.id,
	}
	if errMsg != "" {
		t.state = Invalid
		validity.ErrMsg = errMsg
		validity.State = t.state
	} else {
		t.state = Ready
		validity.Valid = true
		validity.State = t.state
		validity.Total = t.exec.Total()
		validity.Header = t.exec.Header()
	}
	t.creationRes = make([]SingleOperationResult, t.exec.Total())
	return validity
}

func (t *BatchTask) GetBatchCreationSummary() (*BatchTaskSummary, error) {
	if t.state != Canceled && t.state != Done && t.state != Stopped {
		return nil, stderr.NotAllowed.Error("batch task is not in a stopped state: %s", t.state)
	}

	summary := &BatchTaskSummary{
		Total:   t.exec.Total(),
		Success: 0,
		Failure: 0,
		ErrMsgs: make([]string, 0),
	}
	for _, res := range t.creationRes {
		if res.Res == Success {
			summary.Success++
		} else {
			summary.Failure++
			summary.ErrMsgs = append(summary.ErrMsgs, fmt.Sprintf("index=%d msg=%s", res.Index, res.Message))
		}
	}
	return summary, nil
}

// GetBatchCreationResults will return creation records by filter.
// It will return an error if task state is done or canceled
func (t *BatchTask) GetBatchCreationResults(filter string) ([]SingleOperationResult, error) {
	if t.state != Done && t.state != Canceled {
		return nil, stderr.NotAllowed.Error("批量创建操作未完成, 无法获取创建记录")
	}
	// return all results
	if filter == "" {
		return t.creationRes, nil
	}

	results := make([]SingleOperationResult, 0)
	for _, res := range t.creationRes {
		if res.Res == filter {
			results = append(results, res)
		}
	}
	return results, nil
}

func (t *BatchTask) GetBatchCreationCSVRecords(filter string) ([][]string, error) {
	results, err := t.GetBatchCreationResults(filter)
	if err != nil {
		return nil, err
	}
	records := make([][]string, 0, len(results)+2)
	// 1. write header, insert specific cols to provide creation result
	for _, row := range t.exec.Header() {
		records = append(records, append([]string{CreationResCol}, row...))
	}

	// 2. write records, insert creation result into each records
	for _, result := range results {
		records = append(records, append([]string{result.Message}, result.Row...))
	}
	return records, nil
}

func (t *BatchTask) SetState(state BatchTaskState) error {
	if state != InProgress && state != Canceled {
		return stderr.InvalidParam.Error("更新任务状态失败, 目标状态范围为[%s, %s]", InProgress, Canceled)
	}

	switch state {
	case InProgress:
		if t.state != Ready {
			return stderr.InvalidParam.Error("启动任务失败, 无法从'%s'状态启动", t.state)
		}
		t.Start()
	case Canceled:
		if t.state != InProgress {
			return stderr.InvalidParam.Error("取消任务失败, 无法取消%s'状态的任务", t.state)
		}
		t.Cancel()
	}
	return nil
}

// Start will start this batch creation task.
// It will handle resources successively.
// Each creation result will be sent to frontend through websocket after it succeed or failed.
func (t *BatchTask) Start() {
	if t.state != Ready {
		stdlog.Errorf("could not start batch creation task from '%s' state", t.state)
		return
	}

	stdlog.Debugf("start batch creation task")
	t.state = InProgress

	go func() {
		for i := 0; i < t.exec.Total(); i++ {
			// task has been canceled, skip executing remained operations
			if t.state == Canceled {
				for j := i; j < t.exec.Total(); j++ {
					t.OperateFailed(j, stderr.Internal.Error(CanceledMessage))
				}
				return
			}
			err := t.exec.Exec(i)
			if err != nil {
				t.OperateFailed(i, err)
				continue
			}
			t.OperateSucceed(i)
		}

		// batch operations complete
		if t.state != Canceled { // in case task has been canceled while executing last operation
			t.Done()
		}
	}()
}

// Wait will block until task has completed or canceled
func (t *BatchTask) Wait() {
	<-t.done
}

// Cancel will cancel all remained operation immediately
func (t *BatchTask) Cancel() {
	stdlog.Debugf("cancel batch creation task")
	if t.state != InProgress {
		return
	}
	t.state = Canceled
	t.done <- t.state
	close(t.done)
	t.cancelF()
}

// Done will change task's state from InProgress to Done
func (t *BatchTask) Done() {
	t.state = Done
	t.done <- t.state
	close(t.done)
}

// OperateFailed will send the failed creation info of specified device to frontend
func (t *BatchTask) OperateFailed(idx int, err error) {
	errMsg := stderr.Unwrap(err).Message()
	stdlog.Debugf("failed to execute operation(index:%d), cause: %s", idx, errMsg)
	res := SingleOperationResult{
		Index:   idx,
		Res:     Failed,
		Message: errMsg,
		Row:     t.exec.Fields(idx),
	}
	t.creationRes[idx] = res
	t.exec.HandleResult(t.id, res)
}

// OperateSucceed will send the success creation info of specified device to frontend
func (t *BatchTask) OperateSucceed(idx int) {
	stdlog.Debugf("success to execute operation(index:%d)", idx)
	res := SingleOperationResult{
		Index:   idx,
		Res:     Success,
		Message: SuccessMessage,
		Row:     t.exec.Fields(idx),
	}
	t.creationRes[idx] = res
	t.exec.HandleResult(t.id, res)
}
