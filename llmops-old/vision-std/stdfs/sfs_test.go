package stdfs

import "testing"

func TestNewRelativeFilePath(t *testing.T) {
	tests := []struct {
		name    string
		path    string
		want    RelativeFilePath
		wantErr bool
	}{
		{path: "sfs:///tenants/dev-assets/projs/assets/temp/test.txt", want: "tenants/dev-assets/projs/assets/temp/test.txt", wantErr: false},
		{path: "s3://127.0.0.1:9000/tenants/dev-assets/projs/assets/temp/test.txt", want: "tenants/dev-assets/projs/assets/temp/test.txt", wantErr: false},
		{path: "/sfs/tenants/dev-assets/projs/assets/temp/test.txt", want: "tenants/dev-assets/projs/assets/temp/test.txt", wantErr: false},
		{path: "tenants/dev-assets/projs/assets/temp/test.txt", want: "tenants/dev-assets/projs/assets/temp/test.txt", wantErr: false},
		{path: "temp/test.txt", want: "", wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewRelativeFilePath(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewRelativeFilePath() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("NewRelativeFilePath() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToTenantFilePath(t *testing.T) {
	tests := []struct {
		name    string
		path    string
		want    string
		wantErr bool
	}{
		{path: "tenants/dev-assets/projs/assets/cvat/dataset-text/741/433", want: "projs/assets/cvat/dataset-text/741/433", wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sfsPath, err := NewRelativeFilePath(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewRelativeFilePath() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got := sfsPath.ToTenantFilePath()
			if got != tt.want {
				t.Errorf("NewRelativeFilePath() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsPathInTenantRoot(t *testing.T) {
	tests := []struct {
		name string
		path string
		want bool
	}{
		{path: "/tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "tenants/dev/temp/abc.txt", want: true},
		{path: "/temp/abc.txt", want: false},
		{path: "/sfs/temp/abc.txt", want: false},
	}
	for _, tt := range tests {
		t.Run(tt.path, func(t *testing.T) {
			if got := IsPathInTenantRoot(tt.path); got != tt.want {
				t.Errorf("IsPathInTenantRoot() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestIsPathInProjRoot(t *testing.T) {
	tests := []struct {
		name string
		path string
		want bool
	}{
		{path: "/tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "/tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "/tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "tenants/dev/projs/dev-default/temp/abc.txt", want: true},
		{path: "/tenants/dev/temp/abc.txt", want: false},
		{path: "/temp/abc.txt", want: false},
		{path: "/sfs/temp/abc.txt", want: false},
	}
	for _, tt := range tests {
		t.Run(tt.path, func(t *testing.T) {
			if got := IsPathInProjRoot(tt.path); got != tt.want {
				t.Errorf("IsPathInProjRoot() = %v, want %v", got, tt.want)
			}
		})
	}
}
