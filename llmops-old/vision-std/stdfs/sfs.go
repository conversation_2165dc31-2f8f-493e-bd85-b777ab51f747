package stdfs

import (
	"encoding/base64"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// 详细存储规范&设计方案请参见：
// https://wiki.transwarp.io/pages/viewpage.action?pageId=108659981
//
// 示例目录结构(以系统服务部署在dev namespace 为例)：
// * 系统服务容器内SFS文件目录结构：
// /sfs
// └── tenants
//    ├── dev-assets
//    │         └── projs
//    │             └── assets
//    │                 ├── csm
//    │                 ├── mwh
//    │                 └── temp
//    │                 	└── testfile.txt
//    └── dev-default
//        └── projs
//            └── default
//                ├── csm
//                ├── mwh
//                └── temp
//
// 以将公共空间（assets）项目空间租户内的 sfs-volume 挂载至任务负载（服务部署、代码实例等）容器内 /mnt/data 为例subpath="", mountPath="/mnt/data") 可见的目录结构：
// /mnt/data
// ├── csm
// ├── mwh
// └── temp
//    └── testfile.txt

// RelativeFilePath 为sfs文件存储中描述文件地址的类型
// 对应的为文件相对于sfs的相对路径
// 实际对应的文件绝对路径为： /sfs/${RelativeFilePath}
// 在租户隔离的文件存储管理下，一般有前缀格式为 /tenants/{tenant_id}/projs/{proj_id}/
type RelativeFilePath string

type FSMode string

const (
	SophonFS FSMode = "sfs" // 通过原datamgr 现sophon-fs接口上传的文件地址
	OssGw    FSMode = "s3"  // S3对象存储服务提供的地址
	LocalFS  FSMode = ""    // 普通文件路径

	LocalRoot = "/sfs" // 强制统一的容器内文件存储根路径（绝对路径）
)

var (
	tenantRootRe = regexp.MustCompile(`^/*tenants/[^/]+/`)
	projRootRe   = regexp.MustCompile(`^/*tenants/[^/]+/projs/[^/]+/?`)
)

// NewRelativeFilePath 尝试解析path并返回对应的RelativeFilePath,可以用与在SFS体系下进行格式转换
// 支持以下格式：
// e.g.
// -----------------------------------------------------------------------------
//
//	FSMode 		|   ExamplePath                                            |
//
// -----------------------------------------------------------------------------
//
//	SophonFS		|   sfs:///temp/test.txt                                   |
//	OssGw          |   s3://{ip}:{port}/temp/test.txt                         |
//	LocalFS        |   /sfs/tenants/{tenant_id}/projs/{proj_id}/              |
//	相对路径        |   tenants/{tenant_id}/projs/{proj_id}/                   |
//
// -----------------------------------------------------------------------------
//
//	其他错误示例     |   /tenants/{tenant_id}/projs/{proj_id}/                  |
//	其他错误示例     |   sfs/tenants/{tenant_id}/projs/{proj_id}/               |
//	其他错误示例     |   tenants/{tenant_id}/projs/{proj_id}/                   |
//
// -----------------------------------------------------------------------------
func NewRelativeFilePath(path string) (RelativeFilePath, error) {
	p, mode, err := parsePath(path)
	if err != nil {
		return "", err
	}

	// 本地文件路径不能是相对路径
	if mode == LocalFS {
		if filepath.IsAbs(p) {
			p = strings.TrimPrefix(p, LocalRoot)
		}
	}

	// 验证是否租户隔离的路径前缀
	if !projRootRe.MatchString(p) {
		return "", stderr.InvalidParam.Error("invalid path %s, the format of SFSFilePath should be '/tenants/{tenant_id}/projs/{proj_id}/...'", p)
	}

	return RelativeFilePath(strings.TrimPrefix(p, string(filepath.Separator))), nil
}

// NewProjectLocation 返回一个项目在容器内的文件存储根目录或指定的子目录路径
//
// 参数:
//   - tenantID: 租户ID，不能为空
//   - projID: 项目ID，不能为空
//   - createIfNotExist: 如果为true，则在路径不存在时创建目录
//   - subDirs: 可选的子目录路径，将被追加到项目根目录后面
//
// 返回:
//   - RelativeFilePath: 相对于容器根目录的文件路径，格式为 "tenants/<tenantID>/projs/<projID>[/subDirs...]"
//   - error: 如果tenantID或projID为空，或目录创建失败时返回错误
//
// 示例:
//
//	// 获取项目根目录
//	path1, err := NewProjectLocation("tenant1", "proj1", true)
//	// 结果: tenants/tenant1/projs/proj1
//
//	// 获取项目下的临时目录
//	path2, err := NewProjectLocation("tenant1", "proj1", true, "temp", "123456")
//	// 结果: tenants/tenant1/projs/proj1/temp/123456
func NewProjectLocation(tenantID, projID string, createIfNotExist bool, subDirs ...string) (RelativeFilePath, error) {
	if projID == "" || tenantID == "" {
		return "", stderr.InvalidParam.Error("the id of project and tenant both are required")
	}

	parts := []string{"tenants", tenantID, "projs", projID}
	if len(subDirs) > 0 {
		parts = append(parts, subDirs...)
	}

	rp := RelativeFilePath(filepath.Join(parts...))
	if createIfNotExist {
		if err := rp.CreateIfNotExist(); err != nil {
			return "", stderr.Wrap(err, "init project sfs root: %s", rp.ToAbsFilePath())
		}
	}

	return rp, nil
}

// GetLocalPath 尝试将一个带前缀的文件地址URL转为其在本地的文件地址，若无法完成转换则返回错误
// e.g.
//
//	sfs:///temp/test.txt
//	s3://ip:port/temp/test.txt
func GetLocalPath(path string) (string, error) {
	p, mode, err := parsePath(path)
	if err != nil {
		return "", err
	}

	return getAbsPath(p, mode), nil
}

func parsePath(path string) (p string, mode FSMode, err error) {
	u, err := url.Parse(path)
	if err != nil {
		return "", "", stderr.InvalidParam.Cause(err, "url parse")
	}
	switch FSMode(u.Scheme) {
	case SophonFS:
		mode = SophonFS
	case OssGw:
		mode = OssGw
	case LocalFS:
		mode = LocalFS
	default:
		return "", "", stderr.InvalidParam.Error("unsupported fs %s", u.Scheme)
	}
	p = u.Path
	return
}
func getAbsPath(path string, mode FSMode) string {
	if mode == LocalFS && filepath.IsAbs(path) {
		return path
	}
	// 默认其他情况都是相对于/sfs的路径
	return filepath.Join(LocalRoot, path)
}

func SFSRoot() string {
	return LocalRoot
}

func GetSFSLocalPath(sfsPath string) (ret string, err error) {
	return GetLocalPath(sfsPath)
}

func (fp RelativeFilePath) String() string {
	return string(fp)
}

// ToSFSFilePath 返回sfs:///格式的路径
// e.g. sfs:////tenants/{tenant_id}/projs/{proj_id}/temp/testfile.txt
func (fp RelativeFilePath) ToSFSFilePath() string {
	fpath := filepath.Clean(string(fp))
	return fmt.Sprintf("%s:///%s", SophonFS, strings.TrimPrefix(fpath, "/"))
}

// ToTenantFilePath 返回租户空间内的文件路径（去除租户隔离的路径前缀，基于挂载点的相对路径）
// e.g. /{mount_path}/projs/{proj_id}/temp/testfile.txt
// 其中mount_path为其在租户空间内，容器内的挂载点, 需自行拼接
func (fp RelativeFilePath) ToTenantFilePath() string {
	return trimTenantsPathPrefix(string(fp))
}

func (fp RelativeFilePath) ToProjFilePath() string {
	return trimProjsPathPrefix(string(fp))
}

// ToAbsFilePath 返回绝对路径(系统服务容器内访问时的本地文件路径）
// e.g. /{LocalRoot}/tenants/{tenant_id}/projs/{proj_id}/temp/testfile.txt
// 其中LocalRoot为sfs根目录在容器内的挂载点，这里推荐为 /sfs
func (fp RelativeFilePath) ToAbsFilePath() string {
	return filepath.Join(LocalRoot, string(fp))
}

func (fp RelativeFilePath) CreateIfNotExist() error {
	abs := fp.ToAbsFilePath()
	if abs == "" {
		return stderr.Internal.Error("can not get the abs file path of %s", fp)
	}
	if err := os.MkdirAll(abs, os.ModePerm); err != nil {
		return stderr.Wrap(err, "mkdir all %s", abs)
	}
	return nil
}

func (fp RelativeFilePath) Dir() string {
	return filepath.Dir(string(fp))
}

func trimTenantsPathPrefix(path string) string {
	if !tenantRootRe.MatchString(path) {
		stdlog.Errorf("trimTenantsPathPrefix: path prefix not matched, path: %s", path)
		return path
	}
	return tenantRootRe.ReplaceAllString(path, "")
}

func trimProjsPathPrefix(path string) string {
	if !projRootRe.MatchString(path) {
		stdlog.Errorf("trimProjsPathPrefix: path prefix not matched, path: %s", path)
		return path
	}
	return projRootRe.ReplaceAllString(path, "")
}

// IsPathInTenantRoot 返回给定的路径是否包含 tenant root 的部分
// IsPathInProjRoot 返回给定的路径是否包含 project root 的部分 (/tenants/<tid>/projs/<pid>)
// e.g.
// 包含:
// - /tenants/dev/projs/dev-default/temp/abc.txt
// - /tenants/dev/projs/dev-default/temp/abc.txt
// - /tenants/dev/projs/dev-default/temp/abc.txt
// - /tenants/dev/temp/abc.txt
// 不含:
// - /temp/abc.txt
// - /sfs/temp/abc.txt
func IsPathInTenantRoot(path string) bool {
	return tenantRootRe.Match([]byte(path))
}

// IsPathInProjRoot 返回给定的路径是否包含 project root 的部分 (/tenants/<tid>/projs/<pid>)
// e.g.
// 包含:
// - /sfs/tenants/dev/projs/dev-default/temp/abc.txt
// - data/store/tenants/dev/projs/dev-default/temp/abc.txt
// - temp/tenants/dev/projs/dev-default/temp/abc.txt
// 不含:
// - /temp/abc.txt
// - /sfs/temp/abc.txt
// - /sfs/tenants/dev/temp/abc.txt
func IsPathInProjRoot(path string) bool {
	return projRootRe.Match([]byte(path))
}

type SFSFileID string
type SFSFilePath string

func (s SFSFilePath) ToSFSFileID() SFSFileID {
	base64Path := base64.RawURLEncoding.EncodeToString([]byte(s))
	return SFSFileID(base64Path)
}
func (s SFSFilePath) IsValid() bool {
	return strings.HasPrefix(string(s), "sfs://")
}

const (
	SFSFreeDownloadQueryParam = "f"
	SFSFreeDownloadBaseAPI    = "/file:download"
	SFSFreeDownloadFullAPI    = "/api/v1/datamgr" + SFSFreeDownloadBaseAPI
	SFSFreeDownloadGatewayAPI = "/datamgr" + SFSFreeDownloadFullAPI
)

func (s SFSFilePath) ToHttpUrl() string {
	ns := k8s.CurrentNamespaceInCluster()
	return s.ToHttpUrlV2(ns)
}

// ToHttpUrlV2
// systemNamespace 系统服务所处的ns
func (s SFSFilePath) ToHttpUrlV2(systemNamespace string) string {
	fileId := s.ToSFSFileID()
	u := url.URL{
		Scheme: "http",
		Host:   fmt.Sprintf("autocv-gateway-service.%s:80", systemNamespace),
		Path:   SFSFreeDownloadGatewayAPI,
	}
	q := u.Query()
	q.Add(SFSFreeDownloadQueryParam, string(fileId))
	u.RawQuery = q.Encode()
	return u.String()
}

func (s SFSFileID) ToSFSFilePath() (SFSFilePath, error) {
	base64Path := string(s)
	filePath, err := base64.RawURLEncoding.DecodeString(base64Path)
	if err != nil {
		return "", stderr.Wrap(err, "decode base64 file name %s", base64Path)
	}
	return SFSFilePath(filePath), nil
}
