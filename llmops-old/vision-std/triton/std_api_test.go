package triton

import (
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

func Test_isStdApi(t *testing.T) {
	type args struct {
		ins  []*pb.ModelInput
		outs []*pb.ModelOutput
		exp  StdModelIO
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "",
			args: args{
				ins: []*pb.ModelInput{
					{Name: StdCVInputData},
					{Name: StdCVInputMeta},
				},
				outs: []*pb.ModelOutput{
					{Name: StdCVOutputRes},
				},
				exp: StdCVModelIO,
			},
			want: true,
		}, {
			name: "",
			args: args{
				ins: []*pb.ModelInput{
					{Name: StdCVInputData},
					{Name: StdCVInputMeta},
				},
				outs: []*pb.ModelOutput{
					{Name: StdCVOutputRes},
				},
				exp: StdTextGenModelIO,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := matchStdModelIO(tt.args.ins, tt.args.outs, tt.args.exp); got != tt.want {
				t.Errorf("matchStdModelIO() = %v, want %v", got, tt.want)
			}
		})
	}
}
