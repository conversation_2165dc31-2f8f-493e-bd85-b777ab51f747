package triton

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"strings"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"

	"github.com/google/uuid"
	"google.golang.org/grpc/credentials/insecure"

	"google.golang.org/grpc/metadata"

	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

const (
	DLIEScheme       = "dlie"
	SeldonScheme     = "seldon"
	AuthorizationKey = "Authorization"
)

type Client interface {
	GetGrpcConfig() GrpcConfig
	IsServerLive(opts ...grpc.CallOption) (bool, error)
	IsServerLiveWithCtx(ctx context.Context, opts ...grpc.CallOption) (bool, error)
	IsServerReady(opts ...grpc.CallOption) (bool, error)
	IsServerReadyWithCtx(ctx context.Context, opts ...grpc.CallOption) (bool, error)
	LoadModel(modelName string, opts ...grpc.CallOption) error
	LoadEnsembleModel(modelName string, opts ...grpc.CallOption) error
	UnloadModel(modelName string, opts ...grpc.CallOption) error
	UnloadEnsembleModel(modelName string, opts ...grpc.CallOption) error
	ListModels(ready bool, opts ...grpc.CallOption) ([]*pb.RepositoryIndexResponse_ModelIndex, error)
	ListModelsWithRepo(repo string, ready bool, opts ...grpc.CallOption) ([]*pb.RepositoryIndexResponse_ModelIndex, error)

	// 推理相关

	// ModelInfer 用于进行标准的CV模型推理
	ModelInfer(modelName string, req *pb.InferReq, opts ...grpc.CallOption) (map[string]interface{}, error)
	// SyncModelInfer 用于通用的同步推理模式， 仅支持以 BYTES 格式返回响应， 响应结果Map中：Output name -> Output binary data
	// 注： 响应binary data中，已经去除了前四个字节的长度数据
	SyncModelInfer(modelName string, req SyncInferRequestor, opts ...grpc.CallOption) (map[string][]byte, error)
	// StreamModelInfer 基于 Python Backend Decoupled 模式 + Triton Stream 功能实现了 LLM 的流式返回的能力
	// 每次请求会新建一个 Stream 链接，完成请求发送后立即关闭Send. 然后循环接受 Stream 的响应数据，直到接收到 EOF 或者链接断开为止；
	// 每次接收到的数据，均会使用 StreamInferRequestor 中提供的对应的 Output 的 Handler 进行处理.
	StreamModelInfer(ctx context.Context, modelName string, req StreamInferRequestor, opts ...grpc.CallOption) (err error)
	// RawModelInfer 为底层 Triton Server 原生的推理请求
	RawModelInfer(req *pb.ModelInferRequest, opts ...grpc.CallOption) (*pb.ModelInferResponse, error)

	GetStreamInferCli(ctx context.Context, opts ...grpc.CallOption) (pb.GRPCInferenceService_ModelStreamInferClient, error)
	IsModelReady(modelName string, opts ...grpc.CallOption) (bool, error)
	LoadModelOnSpecificAccelerator(modelName string, rawConfig string, accType conf.AcceleratorType, accIndexs []int, instanceNum int, opts ...grpc.CallOption) error
	// Disconnect 停止triton client的连接，取消所有正在进行中的请求与协程。避免协程泄露
	Disconnect()

	// ModelMeta 返回指定DLIE的元信息
	// 注：modelName 必须为完整的路径，即包含sub model的部分
	ModelMeta(modelName string, opts ...grpc.CallOption) (*pb.ModelMetadataResponse, error)
	// ModelStats 返回指定DLIE模型的统计信息
	// 注：modelName 必须为完整的路径，即包含sub model的部分
	ModelStats(modelName string, opts ...grpc.CallOption) (*pb.ModelStatistics, error)
	MultiModelStats(modelNames ...string) (map[string]*pb.ModelStatistics, error)
	// WatchModelStats 按照给定的时间间隔，自动采集DLIE模型的统计信息，并传入返回的Channel
	// 注：modelName 必须为完整的路径，即包含sub model的部分
	WatchModelStats(ctx context.Context, modelName string, interval time.Duration, opts ...grpc.CallOption) <-chan *pb.ModelStatistics
	WatchMultiModelStats(ctx context.Context, modelNames []string, interval time.Duration, opts ...grpc.CallOption) <-chan map[string]*pb.ModelStatistics
	// HandleModelStats 按照给定的时间间隔，自动采集并处理DLIE模型的统计信息
	// 注：modelName 必须为完整的路径，即包含sub model的部分
	HandleModelStats(ctx context.Context, modelName string, interval time.Duration, handler func(stats *pb.ModelStatistics), opts ...grpc.CallOption)
	HandleMultiModelStats(ctx context.Context, modelNames []string, interval time.Duration, handler func(stats map[string]*pb.ModelStatistics), opts ...grpc.CallOption)
}
type GrpcConfig struct {
	// Node 为Triton Inference Server所在服务器节点的主机名
	Node string `json:"node" yaml:"node"`
	// Host 为Triton Inference Server所在的服务器IP地址/域名
	Host string `json:"host" yaml:"host"`
	// Port 为 GRPCInferenceService 服务监听的端口号， 默认为 8001
	Port int `json:"port" yaml:"port"`
	// BaseTimeout 首次建立连接以及大多数基础服务接口调用的超时时间
	BaseTimeout time.Duration `json:"base_timeout" yaml:"base_timeout"`
	// LoadTimeout 为加载模型时的超时时间，耗时相对较久
	LoadTimeout time.Duration `json:"load_timeout" yaml:"load_timeout"`
	// InferTimeout 为模型推理的超时时间
	InferTimeout time.Duration     `json:"infer_timeout" yaml:"infer_timeout"`
	DialOps      []grpc.DialOption `json:"dial_ops" yaml:"dial_ops"`
}

// KeyString 返回该Grpc连接对应的Key, 可用于相同配置的多client之间的复用
func (c *GrpcConfig) KeyString() string {
	return fmt.Sprintf("_dlie_%s_%s_%d_%s_%s", c.Node, c.Host, c.Port, c.BaseTimeout.String(), c.LoadTimeout.String())
}

type grpcCli struct {
	GrpcConfig
	baseCtx     context.Context
	baseCancel  context.CancelFunc
	cli         pb.GRPCInferenceServiceClient
	conn        *grpc.ClientConn
	lastStats   *pb.ModelStatisticsResponse
	lastStatsTs time.Time
	header      metadata.MD
	mutex       sync.Mutex
}
type VersionChoice struct {
	Latest   *pb.ModelVersionPolicy_Latest   `json:"latest"`
	All      *pb.ModelVersionPolicy_All      `json:"all"`
	Specific *pb.ModelVersionPolicy_Specific `json:"specific"`
}
type ModelWarmupInput struct {
	DataType      pb.DataType `json:"data_type,omitempty"`
	Dims          []int64     `json:"dims,omitempty"`
	ZeroData      bool        `json:"zero_data,omitempty"`
	RandomData    bool        `json:"random_data,omitempty"`
	InputDataFile string      `json:"input_data_file,omitempty"`
}

type ModelWarmup struct {
	Name      string                       `json:"name,omitempty"`
	BatchSize uint32                       `json:"batch_size,omitempty"`
	Inputs    map[string]*ModelWarmupInput `json:"inputs,omitempty"`
}

// CustomConfig 原triton proto中定义的ModelConfig无法序列化的字段在这里展开
type CustomConfig struct {
	Name                   string                        `json:"name,omitempty"`
	Platform               string                        `json:"platform,omitempty"`
	Backend                string                        `json:"backend,omitempty"`
	Input                  []*pb.ModelInput              `json:"input,omitempty"`
	Output                 []*pb.ModelOutput             `json:"output,omitempty"`
	MaxBatchSize           int32                         `json:"max_batch_size,omitempty"`
	BatchInput             []*pb.BatchInput              `json:"batch_input,omitempty"`
	BatchOutput            []*pb.BatchOutput             `json:"batch_output,omitempty"`
	Optimization           *pb.ModelOptimizationPolicy   `json:"optimization,omitempty"`
	DefaultModelFilename   string                        `json:"default_model_filename,omitempty"`
	CcModelFilenames       map[string]string             `json:"cc_model_filenames,omitempty"`
	MetricTags             map[string]string             `json:"metric_tags,omitempty"`
	Parameters             map[string]*pb.ModelParameter `json:"parameters,omitempty"`
	ModelOperations        *pb.ModelOperations           `json:"model_operations,omitempty"`
	ModelTransactionPolicy *pb.ModelTransactionPolicy    `json:"model_transaction_policy,omitempty"`
	ModelRepositoryAgents  *pb.ModelRepositoryAgents     `json:"model_repository_agents,omitempty"`
	ResponseCache          *pb.ModelResponseCache        `json:"response_cache,omitempty"`
	InstanceGroup          []*pb.ModelInstanceGroup      `json:"instance_group,omitempty"`
	// oneof 类型
	ModelWarmup []*ModelWarmup `json:"model_warmup,omitempty"`
	// oneof 类型
	DynamicBatching    *pb.ModelDynamicBatching  `json:"dynamic_batching,omitempty" `
	EnsembleScheduling *pb.ModelEnsembling       `json:"ensemble_scheduling,omitempty"`
	SequenceBatching   *pb.ModelSequenceBatching `json:"sequence_batching,omitempty"`
	// oneof 类型
	VersionPolicy *VersionChoice `json:"version_policy,omitempty"`
}

// PlatformType Triton 模型文件对应的配置描述中的platform
// 通常用于确定运行该模型的 backend
// TODO 待补充完整
type PlatformType = string

// BackendType 运行模型所需要的backend
type BackendType = string

var (
	supportedPlatform = map[PlatformType]struct{}{
		PlatformEnsemble:     {},
		PlatformPython:       {},
		PlatformTRT:          {},
		PlatformTFGraph:      {},
		PlatformTFSavedModel: {},
		PlatformOnnx:         {},
		PlatformTorch:        {},
	}
	supportedBackend = map[BackendType]struct{}{
		BackendOnnx:        {},
		BackendPython:      {},
		BackendDali:        {},
		BackendFil:         {},
		BackendOpenvino:    {},
		BackendPytorch:     {},
		BackendRepeat:      {},
		BackendSquare:      {},
		BackendTensorflow1: {},
		BackendTensorflow2: {},
		BackendTensorrt:    {},
		BackendHwascend:    {},
		BackendBitmain:     {},
		BackendCambricon:   {},
	}
)

// IsSupportedBackend 判断给定的backend类型是否支持
func IsSupportedBackend(backend BackendType) bool {
	_, ok := supportedBackend[backend]
	return ok
}

// IsSupportedPlatform 判断给定的platform类型是否支持
func IsSupportedPlatform(platform PlatformType) bool {
	_, ok := supportedPlatform[platform]
	return ok
}

const (
	PlatformEnsemble     PlatformType = "ensemble"
	PlatformPython       PlatformType = "python"
	PlatformTRT          PlatformType = "tensorrt_plan"
	PlatformTFGraph      PlatformType = "tensorflow_graphdef"
	PlatformTFSavedModel PlatformType = "tensorflow_savedmodel"
	PlatformOnnx         PlatformType = "onnxruntime_onnx"
	PlatformTorch        PlatformType = "pytorch_libtorch"

	BackendOnnx        BackendType = "onnxruntime"
	BackendPython      BackendType = "python"
	BackendDali        BackendType = "dali"
	BackendFil         BackendType = "fil"
	BackendOpenvino    BackendType = "openvino"
	BackendPytorch     BackendType = "pytorch"
	BackendRepeat      BackendType = "repeat"
	BackendSquare      BackendType = "square"
	BackendTensorflow1 BackendType = "tensorflow1"
	BackendTensorflow2 BackendType = "tensorflow2"
	BackendTensorrt    BackendType = "tensorrt"
	BackendHwascend    BackendType = "hwascend"
	BackendBitmain     BackendType = "bitmain"
	BackendCambricon   BackendType = "cambricon"
	SeldonDpHeaderKey              = "seldon"
	NamespaceHeaderKey             = "namespace"
	QueryIdHeaderKey               = "query-id" //https://jira.transwarp.io/browse/AIP-101578?filter=-1
)
const (
	MinBaseTimeout      = time.Second * 1
	MaxBaseTimeout      = time.Minute * 10
	DefaultBaseTimeout  = time.Second * 5
	MinLoadTimeout      = time.Second * 10
	MaxLoadTimeout      = time.Minute * 10
	DefaultLoadTimeout  = time.Second * 120
	MinInferTimeout     = time.Second * 2
	MaxInferTimeout     = time.Minute * 20
	DefaultInferTimeout = time.Minute * 10
	DefaultServerPort   = 8001

	// EnsembleModelSep is the separator between ensemble model and sub model
	EnsembleModelSep = ":"

	EnsembleInferInputDataType    = "BYTES"        // 发送推理请求Input的数据格式（https://github.com/triton-inference-server/server/blob/main/docs/model_configuration.md#datatypes）
	EnsembleInferInputNameData    = "IMAGE_BINARY" // 存放推理请求的具体数据的input name
	EnsembleInferInputNameMeta    = "IMAGE_DESC"   // 存放推理请求的Meta信息的input name
	EnsembleInferOutputNameResult = "RESULT"       // 推理请求返回结果所在的output name

	statsCacheDur = time.Second * 3 // 全局统计信息的缓存超时时间
	ParameterKey  = "config"

	defaultMaxMsgSize = 64 << 20
)

var (
	EnsembleInferMetaShape   = []int64{1}
	EnsembleInferBinaryShape = []int64{-1}

	grpcDialOps = []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallSendMsgSize(defaultMaxMsgSize),
			grpc.MaxCallRecvMsgSize(defaultMaxMsgSize),
		),
	}
)

func NewTritonGrpcClient(cfg GrpcConfig) (Client, error) {
	if cfg.BaseTimeout < MinBaseTimeout || cfg.BaseTimeout > MaxBaseTimeout {
		stdlog.Warnf("invalid base timeout %s, replace it with default %s", cfg.BaseTimeout.String(), DefaultBaseTimeout.String())
		cfg.BaseTimeout = DefaultBaseTimeout
	}
	if cfg.LoadTimeout < MinLoadTimeout || cfg.LoadTimeout > MaxLoadTimeout {
		stdlog.Warnf("invalid load timeout %s, replace it with default %s", cfg.LoadTimeout.String(), DefaultLoadTimeout.String())
		cfg.LoadTimeout = DefaultLoadTimeout
	}
	if cfg.InferTimeout < MinInferTimeout || cfg.InferTimeout > MaxInferTimeout {
		stdlog.Warnf("invalid infer timeout %s, replace it with default %s", cfg.InferTimeout.String(), DefaultInferTimeout.String())
		cfg.InferTimeout = DefaultInferTimeout
	}
	if cfg.Host == "" {
		return nil, stderr.Internal.Error("'host' is necessary while connecting to the triton server")
	}
	if cfg.Port <= 0 || cfg.Port >= 65535 {
		stdlog.Warnf("invalid server port %d, replace it with default %d", cfg.Port, DefaultServerPort)
		cfg.Port = DefaultServerPort
	}

	// Connect to gRPC server
	baseCtx, baseCancel := context.WithCancel(context.Background())
	dailCtx, dailCancel := context.WithTimeout(baseCtx, cfg.BaseTimeout)
	target := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)

	var err error
	var conn *grpc.ClientConn
	if len(cfg.DialOps) == 0 {
		conn, err = grpc.DialContext(dailCtx, target, grpcDialOps...)
	} else {
		conn, err = grpc.DialContext(dailCtx, target, cfg.DialOps...)
	}

	dailCancel()
	if err != nil {
		baseCancel()
		return nil, stderr.InvalidParam.Cause(err, "couldn't connect to triton server endpoint %s", target)
	}

	// Create client from gRPC server connection
	return &grpcCli{
		GrpcConfig: cfg,
		baseCtx:    baseCtx,
		baseCancel: baseCancel,
		header:     make(metadata.MD),
		conn:       conn,
		cli:        pb.NewGRPCInferenceServiceClient(conn),
	}, nil
}

// NewClientWithSeldonFullUrl
// fullUrl: 服务的完整url e.g.
// seldon seldon://{istio-gateway}/{namespace}/{seldon-deployment-name}
// dlie dlie://{dp-id}:port
func NewClientWithSeldonFullUrl(ctx context.Context, fullUrl string) (Client, error) {
	cfg := GrpcConfig{
		BaseTimeout:  DefaultBaseTimeout,
		LoadTimeout:  DefaultLoadTimeout,
		InferTimeout: DefaultInferTimeout,
	}
	u, err := url.Parse(fullUrl)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to parse full url")
	}
	// Connect to gRPC server
	baseCtx, baseCancel := context.WithCancel(ctx)
	md := make(metadata.MD)
	authorization := ctx.Value(AuthorizationKey)
	if authorization != nil {
		md.Append(AuthorizationKey, authorization.(string))
	}
	if u.Scheme == SeldonScheme {
		nsAndSdep := strings.SplitN(strings.TrimPrefix(u.Path, "/"), "/", 2)
		md.Append(NamespaceHeaderKey, nsAndSdep[0])
		md.Append(SeldonDpHeaderKey, nsAndSdep[1])
		stdlog.Infof("grpc call metadata %v", md)
	}
	dailCtx, dailCancel := context.WithTimeout(baseCtx, cfg.BaseTimeout)
	conn, err := grpc.DialContext(dailCtx, u.Host, grpcDialOps...)
	dailCancel()
	if err != nil {
		baseCancel()
		return nil, stderr.InvalidParam.Cause(err, "couldn't connect to triton server endpoint %s", fullUrl)
	}
	// Create client from gRPC server connection
	return &grpcCli{
		GrpcConfig: cfg,
		baseCtx:    baseCtx,
		baseCancel: baseCancel,
		conn:       conn,
		cli:        pb.NewGRPCInferenceServiceClient(conn),
		header:     md,
	}, nil
}
func (c *grpcCli) GetGrpcConfig() GrpcConfig {
	return c.GrpcConfig
}

func (c *grpcCli) Disconnect() {
	// 取消所有进行中的请求与协程
	c.baseCancel()
	if err := c.conn.Close(); err != nil {
		stdlog.WithError(err).Errorf("failed to close grpc conn of triton grpc client")
	}
	return
}

func (c *grpcCli) IsServerReady(opts ...grpc.CallOption) (bool, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()
	return c.IsServerReadyWithCtx(ctx)
}

func (c *grpcCli) IsServerLive(opts ...grpc.CallOption) (bool, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()
	return c.IsServerLiveWithCtx(ctx, opts...)
}

func (c *grpcCli) IsServerReadyWithCtx(ctx context.Context, opts ...grpc.CallOption) (bool, error) {
	for k, v := range c.header {
		ctx = metadata.AppendToOutgoingContext(ctx, k, v[0])
	}
	rsp, err := c.cli.ServerReady(ctx, &pb.ServerReadyRequest{})
	if err != nil {
		return false, stderr.ServiceInternal.Cause(err, "couldn't get server health")
	}
	return rsp.Ready, nil
}

func (c *grpcCli) IsServerLiveWithCtx(ctx context.Context, opts ...grpc.CallOption) (bool, error) {
	for k, v := range c.header {
		ctx = metadata.AppendToOutgoingContext(ctx, k, v[0])
	}
	rsp, err := c.cli.ServerLive(ctx, &pb.ServerLiveRequest{}, opts...)
	if err != nil {
		return false, stderr.ServiceInternal.Cause(err, "couldn't get server health")
	}
	return rsp.Live, nil
}

// LoadModel 将通知Triton Inference Server加载指定的模型, 对于如下所示的Repo结构：
//
//	/models/
//	|-- classifier
//	|   |-- densenet_onnx
//	|   |   |-- 1
//	|   |   |   |-- model.onnx
//	|   |   |   `-- onnx.proto
//	|   |   |-- config.pbtxt
//	|   |   `-- densenet_labels.txt
//	|   |-- dev_ensemble
//	|   |   |-- 2
//	|   |   |   `-- dummy
//	|   |   `-- config.pbtxt
//	|   |-- postprocess
//	|   |   |-- 1
//	|   |   |   |-- densenet_labels.txt
//	|   |   |   |-- model.py
//	|   |   |   `-- triton_python_backend_utils.py
//	|   |   `-- config.pbtxt
//	|   `-- preprocess
//	|       |-- 1
//	|       |   |-- model.py
//	|       |   `-- triton_python_backend_utils.py
//	|       `-- config.pbtxt
//	|-- densenet_onnx
//	|   |-- 1
//	|   |   `-- model.onnx
//	|   |-- config.pbtxt
//	|   `-- densenet_labels.txt
//	`-- inception_graphdef
//	    |-- 1
//	    |   `-- model.graphdef
//	    |-- config.pbtxt
//	    `-- inception_labels.txt
//
// 其中 classifier 为 DLIE 模型, 对应Model Warehouse 中某个具体的 Model Release
// 如需加载整个DLIE模型，需要其顶层名称后加字符':', e.g. "classifier:"
// 如需加载DLIE模型某个子模型，需要使用'.'对模型路径进行拼接, e.g. "classifier.densenet_onnx"
// 模型Load成功后，其状态将转换为 Ready;
func (c *grpcCli) LoadModel(modelName string, opts ...grpc.CallOption) (err error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.LoadTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	// load a model
	// 加载模型成功后，对应的State 变为Ready, 且Version变为非空
	_, err = c.cli.RepositoryModelLoad(ctx, &pb.RepositoryModelLoadRequest{
		ModelName: modelName,
	})
	if err != nil {
		return stderr.ServiceInternal.Cause(err, "failed to load model '%s'", modelName)
	}
	return nil
}

func (c *grpcCli) LoadEnsembleModel(modelName string, opts ...grpc.CallOption) (err error) {
	return c.LoadModel(strings.TrimSuffix(modelName, EnsembleModelSep) + EnsembleModelSep)
}

// LoadModelOnSpecificAccelerator 将模型加载在该client所在节点指定的加速卡上,并启动相应数量的实例
func (c *grpcCli) LoadModelOnSpecificAccelerator(modelName, rawConfig string, accType conf.AcceleratorType, accIndexs []int, instanceNum int, opts ...grpc.CallOption) error {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.LoadTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	// parse raw model.pbtxt config
	mc := new(pb.ModelConfig)
	if err := proto.UnmarshalText(rawConfig, mc); err != nil {
		return stderr.Internal.Cause(err, "failed to unmarshal following text to ModelConfig: %s", rawConfig)
	}

	if mc.Platform == PlatformEnsemble {
		// Poll failed for model directory 'model-xxx/release-xxx/predict': instance group should not be specified for ensemble
		return c.LoadModel(modelName)
	}

	cpuIndexes := make([]int32, 0)
	gpuIndexes := make([]int32, 0)
	for _, accIndex := range accIndexs {
		if accIndex == conf.CPUAcceleratorIndex {
			cpuIndexes = append(cpuIndexes, int32(accIndex))
		} else {
			gpuIndexes = append(gpuIndexes, int32(accIndex))
		}
	}
	instanceGroup := make([]*pb.ModelInstanceGroup, 0)
	if len(cpuIndexes) != 0 {
		cInstanceGroup, err := genModelInstanceGroup(conf.AcceleratorCPU, int32(instanceNum), cpuIndexes)
		if err != nil {
			return err
		}
		instanceGroup = append(instanceGroup, cInstanceGroup)

	}
	if len(gpuIndexes) != 0 {
		gInstanceGroup, err := genModelInstanceGroup(accType, int32(instanceNum), gpuIndexes)
		if err != nil {
			return err
		}
		instanceGroup = append(instanceGroup, gInstanceGroup)
	}

	config, _ := json.Marshal(mc)
	stdlog.Debug("### original model config ###", string(config))
	custom := &CustomConfig{
		Name:                   mc.Name,
		Platform:               mc.Platform,
		Backend:                mc.Backend,
		Input:                  mc.Input,
		Output:                 mc.Output,
		MaxBatchSize:           mc.MaxBatchSize,
		BatchInput:             mc.BatchInput,
		BatchOutput:            mc.BatchOutput,
		Optimization:           mc.Optimization,
		DefaultModelFilename:   mc.DefaultModelFilename,
		CcModelFilenames:       mc.CcModelFilenames,
		MetricTags:             mc.MetricTags,
		Parameters:             mc.Parameters,
		ModelOperations:        mc.ModelOperations,
		ModelTransactionPolicy: mc.ModelTransactionPolicy,
		ModelRepositoryAgents:  mc.ModelRepositoryAgents,
		ResponseCache:          mc.ResponseCache,
		InstanceGroup:          instanceGroup,
	}
	// 做proto 中 oneof类型的转换
	switch schedulingChoice := mc.SchedulingChoice.(type) {
	case *pb.ModelConfig_DynamicBatching:
		custom.DynamicBatching = schedulingChoice.DynamicBatching
	case *pb.ModelConfig_EnsembleScheduling:
		custom.EnsembleScheduling = schedulingChoice.EnsembleScheduling
	case *pb.ModelConfig_SequenceBatching:
		custom.SequenceBatching = schedulingChoice.SequenceBatching
	default:
		custom.DynamicBatching = nil
		custom.SequenceBatching = nil
		custom.EnsembleScheduling = nil
	}
	if mc.VersionPolicy != nil {
		custom.VersionPolicy = new(VersionChoice)
		switch versionPolicy := mc.VersionPolicy.PolicyChoice.(type) {
		case *pb.ModelVersionPolicy_Latest_:
			custom.VersionPolicy.Latest = versionPolicy.Latest
		case *pb.ModelVersionPolicy_All_:
			custom.VersionPolicy.All = versionPolicy.All
		case *pb.ModelVersionPolicy_Specific_:
			custom.VersionPolicy.Specific = versionPolicy.Specific
		default:
			custom.VersionPolicy = nil
		}
	}
	custom.ModelWarmup = make([]*ModelWarmup, len(mc.ModelWarmup))
	for i, warmup := range mc.ModelWarmup {
		custom.ModelWarmup[i] = &ModelWarmup{
			Name:      warmup.Name,
			BatchSize: warmup.BatchSize,
		}
		if warmup.Inputs == nil {
			continue
		}
		inputs := make(map[string]*ModelWarmupInput, 0)
		for k, v := range warmup.Inputs {
			inputs[k] = &ModelWarmupInput{
				DataType: v.DataType,
				Dims:     v.Dims,
			}
			switch inputDataType := v.InputDataType.(type) {
			case *pb.ModelWarmup_Input_ZeroData:
				inputs[k].ZeroData = inputDataType.ZeroData
			case *pb.ModelWarmup_Input_RandomData:
				inputs[k].RandomData = inputDataType.RandomData
			case *pb.ModelWarmup_Input_InputDataFile:
				inputs[k].InputDataFile = inputDataType.InputDataFile
			}
		}
		custom.ModelWarmup[i].Inputs = inputs
	}
	modelConfigBytes, err := json.Marshal(custom)
	if err != nil {
		err = stderr.Internal.Error("can not encode model config when deploy model on specific accelerator")
		return err
	}
	stdlog.Debug("### changed model config to  ###\n", string(modelConfigBytes))
	// https://github.com/triton-inference-server/server/blob/main/docs/protocol/extension_model_repository.md
	// the load API accepts the following parameters: "config", "file"
	parameters := map[string]*pb.ModelRepositoryParameter{
		ParameterKey: {
			ParameterChoice: &pb.ModelRepositoryParameter_StringParam{StringParam: string(modelConfigBytes)},
		},
	}
	loadModelRequest := &pb.RepositoryModelLoadRequest{
		ModelName:  modelName,
		Parameters: parameters,
	}
	_, err = c.cli.RepositoryModelLoad(ctx, loadModelRequest)
	if err != nil {
		return err
	}
	return nil
}

// genModelInstanceGroup组装模型运行实例配置
func genModelInstanceGroup(accType conf.AcceleratorType, instanceNum int32, accIndexs []int32, opts ...grpc.CallOption) (*pb.ModelInstanceGroup, error) {
	if len(accIndexs) == 0 {
		return nil, nil
	}
	var kind pb.ModelInstanceGroup_Kind
	switch accType {
	case conf.AcceleratorCPU:
		kind = pb.ModelInstanceGroup_KIND_CPU
		// 归并CPU上启动的实例数量，一般不会出现，因为一个节点只有一个CPU
		instanceNum = instanceNum * int32(len(accIndexs))
		accIndexs = nil
	case conf.AcceleratorGPU, conf.AcceleratorJetson:
		kind = pb.ModelInstanceGroup_KIND_GPU
	case conf.AcceleratorAtlas, conf.AcceleratorMLU:
		kind = pb.ModelInstanceGroup_KIND_MODEL
	default:
		return nil, stderr.InvalidParam.Error("unsupported accelerator type '%s'", accType)
	}
	return &pb.ModelInstanceGroup{
		Kind:  kind,
		Count: instanceNum,
		Gpus:  accIndexs,
	}, nil
}
func (c *grpcCli) ListModels(ready bool, opts ...grpc.CallOption) ([]*pb.RepositoryIndexResponse_ModelIndex, error) {
	return c.ListModelsWithRepo("", ready)
}

func (c *grpcCli) UnloadModel(modelName string, opts ...grpc.CallOption) (err error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.LoadTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	// unload the specified model
	_, err = c.cli.RepositoryModelUnload(ctx, &pb.RepositoryModelUnloadRequest{
		ModelName: modelName,
	})
	if err != nil {
		return stderr.ServiceInternal.Cause(err, "failed to unload model '%s'", modelName)
	}
	return nil
}

func (c *grpcCli) UnloadEnsembleModel(modelName string, opts ...grpc.CallOption) error {
	return c.UnloadModel(strings.TrimSuffix(modelName, EnsembleModelSep) + EnsembleModelSep)
}

func (c *grpcCli) IsModelReady(modelName string, opts ...grpc.CallOption) (bool, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	rsp, err := c.cli.ModelReady(ctx, &pb.ModelReadyRequest{
		Name:    modelName,
		Version: "",
	})
	if err != nil {
		return false, stderr.ServiceInternal.Cause(err, "failed to get model ready rsp of '%s'", modelName)
	}
	return rsp.Ready, nil
}

func (c *grpcCli) ModelInfer(modelName string, req *pb.InferReq, opts ...grpc.CallOption) (map[string]interface{}, error) {
	outs, err := c.SyncModelInfer(modelName, (*StdCVReq)(req))
	if err != nil {
		return nil, stderr.Wrap(err, "sync model infer of %s", modelName)
	}

	// get result
	var resStr []byte
	for name, out := range outs {
		if name != EnsembleInferOutputNameResult {
			continue
		}
		resStr = out
	}
	if len(resStr) == 0 {
		return nil, stderr.Internal.Error("empty infer result")
	}

	res := new(map[string]interface{})
	if err := json.Unmarshal(resStr, res); err != nil {
		return nil, err
	}
	// set model_info
	modelInfo := make(map[string]string)
	modelInfo["model_name"] = modelName
	(*res)["model_info"] = modelInfo

	return *res, nil
}

func (c *grpcCli) SyncModelInfer(modelName string, req SyncInferRequestor, opts ...grpc.CallOption) (map[string][]byte, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.InferTimeout)
	requestId := uuid.New().String()
	header := metadata.MD{QueryIdHeaderKey: []string{requestId}}
	ctx = metadata.NewOutgoingContext(ctx, header)
	defer cancel()
	ins, err := req.Inputs()
	if err != nil {
		return nil, stderr.Wrap(err, "get inputs of sync infer request")
	}
	rsp, err := c.cli.ModelInfer(ctx, &pb.ModelInferRequest{
		Id:         requestId,
		ModelName:  modelName,
		Parameters: nil,
		Inputs:     ins,
		Outputs:    req.Outputs(),
	})
	if err != nil {
		return nil, err
	}

	// we should get the output content from raw contents: case https://github.com/triton-inference-server/server/issues/2582
	pb.SetOutputs(rsp)

	// get result
	res := make(map[string][]byte)
	for _, output := range rsp.Outputs {
		if output.Contents == nil {
			continue
		}
		bs := output.Contents.BytesContents
		if len(bs) == 0 {
			continue
		}
		res[output.Name] = bs[0] // 暂时不支持Batch, 故理论上应有  N === len(output.Contents.BytesContents) === 1
		if len(bs[0]) == 0 {
			stdlog.Warnf("empty content of infer output %s", output.Name)
		}
	}
	// 检查是否所有预期的输出都返回了
	for _, outName := range req.OutputNames() {
		if _, ok := res[outName]; !ok {
			return nil, stderr.Internal.Error("expected output named %s not found", outName)
		}
	}
	return res, nil
}

// StreamModelInfer 基于 DLIE & Python Decouple 模式的的模型流式推理封装
func (c *grpcCli) StreamModelInfer(ctx context.Context, modelName string, req StreamInferRequestor, opts ...grpc.CallOption) (err error) {
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, "decoupled infer")
		}
	}()
	requestId := uuid.New().String()
	ctx = metadata.AppendToOutgoingContext(ctx, QueryIdHeaderKey, requestId)
	ic, err := c.cli.ModelStreamInfer(ctx)
	if err != nil {
		err = stderr.Wrap(err, "new stream infer")
		return
	}

	stdlog.Infof("[req-id=%s]send decoupled infer request", requestId)
	ins, err := req.Inputs()
	if err != nil {
		return stderr.Wrap(err, "preparing input tensors")
	}
	err = ic.Send(&pb.ModelInferRequest{
		Id:         requestId,
		ModelName:  modelName,
		Parameters: nil,
		Inputs:     ins,
		Outputs:    req.Outputs(),
	})
	if err != nil {
		return stderr.Wrap(err, "send infer req")
	}
	if err = ic.CloseSend(); err != nil { // only send once
		return stderr.Wrap(err, "close send")
	}

	var msg *pb.ModelStreamInferResponse
	for {
		select {
		case <-ctx.Done():
			return stderr.Internal.Error("context has done, interrupt inferring")
		default:
			// context is not done
		}
		msg, err = ic.Recv()
		if err != nil {
			if err == io.EOF {
				stdlog.Infof("stream complete")
				return nil
			}
			return stderr.Wrap(err, "recv stream infer response")
		}
		if msg.ErrorMessage != "" {
			return stderr.Internal.Error("msg error %s", msg.ErrorMessage)
		}
		outNames := req.OutputNames()
		outs := msg.InferResponse.Outputs
		rawOuts := msg.InferResponse.RawOutputContents
		if len(outs) == 0 || len(outs) != len(rawOuts) {
			return stderr.Internal.Error("outputs in response should not be empty and should have same size with raw output contents")
		}
		if len(outs) != len(outNames) || len(rawOuts) != len(outNames) {
			return stderr.Internal.Error("expect %d output and raw output content, but %d was returned", len(outNames), len(outs))
		}
		stdlog.Infof("[req-id=%s]recv message with id %s", requestId, msg.InferResponse.Id)
		for i, rawOut := range rawOuts {
			handler := req.StreamOutputHandler(outs[i].Name)
			if handler == nil {
				return stderr.InvalidParam.Error("stream handler of output %s has not specified", outs[i].Name)
			}
			if len(rawOut) < 4 {
				err = stderr.Internal.Error("there are at least four bytes in raw output to identify its subsequent byte length")
				return
			}
			// 当实际数据为空时，直接跳过，不调用handler
			if len(rawOut) == 4 {
				continue
			}
			content := rawOut[4:]
			if err = handler(content); err != nil {
				stdlog.WithError(err).Errorf("handle raw output content: %s", content)
				return nil
			}
		}

	}
}

// RawModelInfer 直接使用triton提供的grpc接口进行推理 for mwh
func (c *grpcCli) RawModelInfer(req *pb.ModelInferRequest, opts ...grpc.CallOption) (*pb.ModelInferResponse, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.InferTimeout)
	var requestId string
	if req.Id != "" {
		requestId = req.Id
	} else {
		requestId = uuid.New().String()
	}
	header := metadata.MD{QueryIdHeaderKey: []string{requestId}}
	ctx = metadata.NewOutgoingContext(ctx, header)
	defer cancel()
	rsp, err := c.cli.ModelInfer(ctx, req)
	if err != nil {
		return nil, err
	}

	// we should get the output content from raw contents: case https://github.com/triton-inference-server/server/issues/2582
	pb.SetOutputs(rsp)
	return rsp, nil
}

func (c *grpcCli) ModelMeta(name string, opts ...grpc.CallOption) (*pb.ModelMetadataResponse, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	rsp, err := c.cli.ModelMetadata(ctx, &pb.ModelMetadataRequest{
		Name: name,
	})
	if err != nil {
		return nil, stderr.ServiceInternal.Cause(err, "failed to list the models")
	}
	return rsp, nil
}

func (c *grpcCli) ListModelsWithRepo(repo string, ready bool, opts ...grpc.CallOption) ([]*pb.RepositoryIndexResponse_ModelIndex, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	rsp, err := c.cli.RepositoryIndex(ctx, &pb.RepositoryIndexRequest{
		RepositoryName: repo,
		Ready:          ready,
	})
	if err != nil {
		return nil, stderr.ServiceInternal.Cause(err, "failed to list the models")
	}
	return rsp.Models, nil
}

func (c *grpcCli) ModelStats(modelName string, opts ...grpc.CallOption) (*pb.ModelStatistics, error) {
	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	rsp, err := c.cli.ModelStatistics(ctx, &pb.ModelStatisticsRequest{
		Name: modelName,
	})
	if err != nil || len(rsp.ModelStats) == 0 {
		return nil, stderr.ServiceInternal.Cause(err, "fail to get model current status")
	}
	// TODO 支持多版本
	// we only care about the first stats, cause our ensemble model usually contains only one version.
	return rsp.ModelStats[0], nil
}

func (c *grpcCli) MultiModelStats(modelNames ...string) (map[string]*pb.ModelStatistics, error) {
	if len(modelNames) == 0 {
		return nil, nil
	}
	nameSet := make(map[string]struct{})
	for _, modelName := range modelNames {
		nameSet[modelName] = struct{}{}
	}

	ctx, cancel := context.WithTimeout(c.baseCtx, c.BaseTimeout)
	ctx = metadata.NewOutgoingContext(ctx, c.header)
	defer cancel()

	if c.lastStats == nil || c.lastStatsTs.Add(statsCacheDur).Before(time.Now()) {
		// 缓存为空或已过期， 重新获取所有模型的统计信息
		rsp, err := c.cli.ModelStatistics(ctx, &pb.ModelStatisticsRequest{})
		if err != nil {
			return nil, stderr.ServiceInternal.Cause(err, "fail to get model stats")
		}
		c.lastStats = rsp
		c.lastStatsTs = time.Now()
	}

	res := make(map[string]*pb.ModelStatistics, 0)
	for _, s := range c.lastStats.ModelStats {
		if _, ok := nameSet[s.Name]; ok {
			res[s.Name] = s
		}
	}

	if len(res) != len(nameSet) {
		stdlog.Warnf("model stats may not an exact match with giving models, got %d names, but found %d stats", len(nameSet), len(res))
		for name := range nameSet {
			if _, ok := res[name]; !ok {
				stdlog.Warnf("stats of dlie model '%s' not found", name)
			}
		}
	}
	return res, nil
}

func (c *grpcCli) WatchModelStats(ctx context.Context, modelName string, interval time.Duration, opts ...grpc.CallOption) <-chan *pb.ModelStatistics {
	ch := make(chan *pb.ModelStatistics, 10)
	ticker := time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-ctx.Done():
				stdlog.Infof("stop watching service stats, cause it has been stopped")
				close(ch)
				ticker.Stop()
				return
			case <-c.baseCtx.Done():
				stdlog.Infof("stop watching service stats, cause client has been closed")
				close(ch)
				ticker.Stop()
				return
			case <-ticker.C:
				s, err := c.ModelStats(modelName)
				if err != nil {
					stdlog.WithError(err).Errorf("failed to get the stats of dlie model '%s'", modelName)
					continue
				}
				ch <- s
			}
		}
	}()
	return ch
}

func (c *grpcCli) WatchMultiModelStats(ctx context.Context, modelNames []string, interval time.Duration, opts ...grpc.CallOption) <-chan map[string]*pb.ModelStatistics {
	ch := make(chan map[string]*pb.ModelStatistics, 10)
	ticker := time.NewTicker(interval)
	stdlog.Infof("begin to watch model stats of '%+v' with interval '%s'", modelNames, interval.String())
	go func() {
		for {
			select {
			case <-ctx.Done():
				stdlog.Infof("stop watching service stats, cause it has been stopped")
				close(ch)
				ticker.Stop()
				return
			case <-c.baseCtx.Done():
				stdlog.Infof("stop watching service stats, cause client has been closed")
				close(ch)
				ticker.Stop()
				return
			case <-ticker.C:
				s, err := c.MultiModelStats(modelNames...)
				if err != nil {
					stdlog.WithError(err).Errorf("failed to get the stats of dlie models '%+v'", modelNames)
					continue
				}
				ch <- s
			}
		}
	}()
	return ch
}

func (c *grpcCli) HandleModelStats(ctx context.Context, modelName string, interval time.Duration, handler func(stats *pb.ModelStatistics), opts ...grpc.CallOption) {
	for stats := range c.WatchModelStats(ctx, modelName, interval) {
		handler(stats)
	}
}

func (c *grpcCli) HandleMultiModelStats(ctx context.Context, modelNames []string, interval time.Duration, handler func(stats map[string]*pb.ModelStatistics), opts ...grpc.CallOption) {
	stdlog.Infof("begin to handle multi model stats of '%+v' with interval '%s'", modelNames, interval.String())
	for stats := range c.WatchMultiModelStats(ctx, modelNames, interval) {
		handler(stats)
	}
}

func (c *grpcCli) GetStreamInferCli(ctx context.Context, opts ...grpc.CallOption) (pb.GRPCInferenceService_ModelStreamInferClient, error) {
	for k, v := range c.header {
		ctx = metadata.AppendToOutgoingContext(ctx, k, v[0])
	}
	return c.cli.ModelStreamInfer(ctx, opts...)
}

// CallWithHeader
// input like ("seldon", "service-23cabd03-5001-436f-b3d2-87408ee79f12", "namespace", "llm")
func CallWithHeader(kv ...string) grpc.HeaderCallOption {
	md := metadata.Pairs(kv...)
	return grpc.HeaderCallOption{HeaderAddr: &md}
}

// getClientHeaderWithPair 带锁获取一个 key=value键值对header的副本
func (c *grpcCli) getClientHeaderWithPair(key, value string) metadata.MD {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.header.Set(key, value)
	header := utils.DeepCopy(c.header)
	return header
}
