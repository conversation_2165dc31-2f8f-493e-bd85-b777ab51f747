package triton

import (
	"encoding/json"
	"reflect"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

const (
	DefaultSideCarPort       = "8011"
	QueryParamInnerModelName = "InnerModelName"
	DefaultInnerModelName    = "atom"
	DefaultInnerModelVersion = "1"
	ModelStateReady          = "READY"
	DONEMessage              = "[DONE]"
)

// TODO 优化mwh-infergw和 extraapi
var genericInferReq2InferUrl = map[string]string{
	//std
	getFullClassName(&EntityRecognitionReq{}): "/std/v1/entity/recognition",
	getFullClassName(&LLMChatReq{}):           "/std/v1/chat/completions",
	getFullClassName(&LLMChatStopReq{}):       "/std/v1/chat/completions:stop",
	getFullClassName(&StdAudioTransReq{}):     "/std/v1/audio/transcriptions",
	getFullClassName(&StdCVReq{}):             "/std/v1/cv/all",
	getFullClassName(&StdImageGenReq{}):       "/std/v1/images/generations",
	getFullClassName(&StdMLReq{}):             "/std/v1/ml/all",
	getFullClassName(&Text2VecReqV2{}):        "/std/v1/embeddings",
	getFullClassName(&Any2VecReq{}):           "/std/v1/embeddings", //兼容Text2VecReqV2
	getFullClassName(&RerankReq{}):            "/std/v1/rerank",

	// 兼容大模型推理接口,但各模型最好使用独立的接口
	getFullClassName(&StdLLMInferReq{}): "/std/v1/llm-infer/all",

	//openai
	getFullClassName(&OpenAiChatReq{}):       "/openai/v1/chat/completions",
	getFullClassName(&OpenAiCompletionReq{}): "/openai/v1/completions",
	getFullClassName(&OpenAiImageGenReq{}):   "/openai/v1/images/generations",
	getFullClassName(&OpenAiTextVectorReq{}): "/openai/v1/embeddings",
	getFullClassName(&OpenAiAny2VectorReq{}): "/openai/v1/embeddings", //兼容OpenAiTextVectorReq
	getFullClassName(&OpenAiAudioTransReq{}): "/openai/v1/audio/transcriptions",
}

func getFullClassName(inferReq GenericInferReq) string {
	t := reflect.TypeOf(inferReq)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.PkgPath() + "." + t.Name()
}

func GetInferUrlByReq(inferReq GenericInferReq) (string, error) {
	fullClassName := getFullClassName(inferReq)
	if inferUrl, ok := genericInferReq2InferUrl[fullClassName]; ok {
		return inferUrl, nil
	}
	return "", stderr.Error("can not find inferUrl by this inferReq")
}

// GetInferUrlByReqV2 初始化时使用
func GetInferUrlByReqV2(inferReq GenericInferReq) string {
	str, err := GetInferUrlByReq(inferReq)
	if err != nil {
		panic(err)
	}
	return str
}

// GenericInferReq 兼容std格式与openai格式的推理请求接口
type GenericInferReq interface {

	//IsStream 本次推理是否为流式调用,可根据子类的stream字段去确定
	IsStream() bool

	// GetModelInferRequest 构造triton格式的请求张量
	// innerModelName 对于模型pod而言，所使用的模型名称一般为atom
	GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error)

	// Parse2InferResp
	// 将推理结果由统一的streamInfo格式转为所期望的格式,以供http传输
	// 如果为std实现方式，解析为标准格式-StdInferResp
	// 如果为openai实现方式，则根据需要进行解析
	Parse2InferResp(streamInfo *StreamInfo) (interface{}, error)
}

// Deprecated
// StdInferResp 本地模型调用，统一的返回结构
type StdInferResp struct {
	Result    interface{} `json:"result"  description:"模型推理产生的结果,由具体模型决定"`
	ModelInfo *ModelInfo  `json:"model_info"  description:"模型自身简要信息"`
}
type ModelInfo struct {
	ModelName    string `json:"model_name"  description:"模型名称"`
	ModelVersion string `json:"model_version"  description:"模型版本"`
}

func GetStdInferResp(result interface{}, inferResp *pb.ModelInferResponse) interface{} {
	return result
}

func Construct2StdInferResp[T any](str string) (*T, error) {
	return Construct2OpenaiInferResp[T](str)
}

func Construct2OpenaiInferResp[T any](str string) (*T, error) {
	openaiInferResp := new(T)
	if err := json.Unmarshal([]byte(str), openaiInferResp); err != nil {
		return nil, stderr.Wrap(err, "failed to call json.Unmarshal, the str is %s", str)
	}
	return openaiInferResp, nil
}

type StdImageGenReq struct {
	StdLLMInferReq
}

func (s *StdImageGenReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	return s.StdLLMInferReq.GetModelInferRequest(innerModelName)
}
func (s *StdImageGenReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	return s.StdLLMInferReq.Parse2InferResp(info)
}
func (s *StdImageGenReq) IsStream() bool {
	return false
}

type StdAudioTransReq struct {
	StdLLMInferReq
}

func (s *StdAudioTransReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	s.Stream = false
	s.Stop = false
	return s.StdLLMInferReq.GetModelInferRequest(innerModelName)
}
func (s *StdAudioTransReq) IsStream() bool {
	return false
}
