package triton

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"
)

func Test_streamTextFilter(t *testing.T) {
	basicConfig := FilterBasicConfig{
		WindowSize: DefaultWindowSize,
		StepSize:   DefaultStepSize,
	}
	securityConfigId := "146fc6cf-4227-494d-851c-d91d4e604819" // 敏感词："敏感词"
	securityCensorUrl := "http://**************:8008"
	securityServiceConfig := FilterSecurityServiceConfig{
		ConfigId: securityConfigId,
		Address:  securityCensorUrl,
		Timeout:  DefaultSecurityCensorTimeout,
		Ctx:      context.Background(),
	}
	fmt.Printf("basicConfig: %+v\n securityServiceConfig: %+v\n", basicConfig, securityServiceConfig)
	// 也可使用NewStreamTextFilterWithCustomFilter创建过滤器，传入指定的过滤函数，不使用过滤服务
	textFilter, err := NewStreamTextFilter(basicConfig, securityServiceConfig)
	if err != nil {
		t.Fatal(err)
	}
	passTextHandler := func(text string) error {
		fmt.Println("pass text:", text)
		return nil
	}
	textFilter.SetPassTextHandler(passTextHandler)

	stopTextHandler := func(response *CheckSecurityResponse) error {
		fmt.Println("stop text:", response)
		return nil
	}
	textFilter.SetStopTextHandler(stopTextHandler)

	sensitiveStreamText := []string{"这是一个", "包含XXX", "的测试文本", "a", "b", "a", "b", "a", "b", "a", "b", "a", "b", "吃", "饭", "和", "你好", "hello", "敏感词"}
	for _, text := range sensitiveStreamText {
		err := textFilter.Append(text)
		if err != nil && !errors.Is(err, ErrFoundRiskWord) {
			t.Fatal(err)
		}
		if errors.Is(err, ErrFoundRiskWord) {
			fmt.Println("found risk word stop append")
			break
		}
		time.Sleep(100 * time.Millisecond)
	}
	// 标识流式文本结束，并阻塞等待剩余文本处理完成
	checkSecurityResponse, err := textFilter.Done()
	// 不是发现敏感词错误，则抛出错误
	if err != nil && !errors.Is(err, ErrFoundRiskWord) {
		t.Fatal(err)
	}
	if errors.Is(err, ErrFoundRiskWord) {
		fmt.Println("found risk word when filtering text")
	}
	// 判断是否有安全风险，无风险不处理
	if checkSecurityResponse == nil {
		t.Fatal("check security response is nil")
	}
	if !checkSecurityResponse.Risk {
		fmt.Println("全部处理完成，未发现风险文本")
	} else {
		fmt.Println("发现风险文本，请检查")
	}
}
