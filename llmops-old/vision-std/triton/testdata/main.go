package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

var c triton.Client

func init() {
	// you can use cmd like "kubectl port-forward --address 0.0.0.0 pod/autocv-dlie-b69r4 18001:8001" to expose dlie server port
	cli, err := triton.NewTritonGrpcClient(triton.GrpcConfig{
		Host: "*************",
		// req.SetInstruction("为这个句子生成表示以用于检索相关文章：")
		// Port: 31343, // bge
		Port: 31405,
		// Host:         "**************",
		// Port:         8001,
		BaseTimeout:  time.Second * 10,
		LoadTimeout:  0,
		InferTimeout: time.Minute * 3,
	})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect to trition grpc server, tests will be skipped")
		return
	}
	c = cli
}

func main() {
	bs, err := os.ReadFile("/home/<USER>/work/gopath/src/transwarp.io/aiot/vision-std/triton/testdata/刑法.txt")
	if err != nil {
		panic(err)
	}
	ctx := &triton.Text2VecReqV2{
		Texts: strings.Split(string(bs), "\n　　第"),
	}
	// ctx.SetInstruction("为这个句子生成表示以用于检索相关文章：")
	for _, text := range ctx.Texts {
		println(len(text), text)
	}
	ctxRet, err := c.SyncModelInfer("atom", ctx)
	if err != nil {
		panic(err)
	}
	println("context infer done !")
	ctxRes, err := ctx.ParseResult(ctxRet)
	if err != nil {
		panic(err)
	}
	s := bufio.NewScanner(os.Stdin)

	for s.Scan() {
		input := s.Text()
		req := &triton.Text2VecReqV2{Texts: []string{input}}
		// req.SetInstruction("为这个句子生成表示以用于检索相关文章：")
		reqRet, err := c.SyncModelInfer("atom", req)
		if err != nil {
			panic(err)
		}
		reqRes, err := ctx.ParseResult(reqRet)
		if err != nil {
			panic(err)
		}

		for i, q := range req.Texts {
			ans := ""
			maxDis := 0.0
			for j, a := range ctx.Texts {
				dis := triton.CosineSimilarity(reqRes.Results[i], ctxRes.Results[j])
				fmt.Printf("similarity between req[%d],ctx[%d] is %.4f\n", i, j, dis)
				// heap.Push(hp, [2]any{dis, a})
				if dis > maxDis {
					maxDis = dis
					ans = a
				}
			}
			fmt.Printf("best match: [%d]\nQ:%s, \nA:%s\n", i, q, ans)
		}

	}
}

type IntHeap struct {
	scores []float64
	texts  []string
}

func (h *IntHeap) Len() int           { return len(h.scores) }
func (h *IntHeap) Less(i, j int) bool { return h.scores[i] < h.scores[j] }
func (h *IntHeap) Swap(i, j int)      { h.scores[i], h.scores[j] = h.scores[j], h.scores[i] }
func (h *IntHeap) Push(x any) {
	v := x.([2]any)
	h.scores = append(h.scores, v[0].(float64))
	h.texts = append(h.texts, v[1].(string))
}

func (h *IntHeap) Pop() any {
	n := len(h.texts)
	x := h.texts[n-1]
	h.texts = h.texts[:n-1]
	h.scores = h.scores[:n-1]
	return x
}
