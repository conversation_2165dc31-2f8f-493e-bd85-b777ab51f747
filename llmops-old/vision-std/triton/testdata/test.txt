Edge版本发布-镜像打包流程：
1.cd /thinger-deploy/scripts
2.bash save-images.sh
3.根据需求选择需要的模块
4.拷入U盘
    a.查看U盘的设备路径 sudo fdisk -l
    b.若U盘格式为
    c.将U盘mount到指定目录 sudo mount /dev/sda /mnt/usb
    d.将打包好的镜像包复制到上述目录
5.上传百度云盘


docker-compose安装过程：
a.获取安装包：
    i.在线下载：sudo curl-L "https://github.com/docker/compose/releases/download/1.25.5/docker-compose-$(uname-s)-$(uname-m)" -o /usr/local/bin/docker-compose
    ii.离线包：docker-compose@v1.25.3
b.修改权限:sudo chmod +x /usr/local/bin/docker-compose
c.创建连接：sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose


docker 内存泄漏问题排查解决方案：
a.故障表现：
    i.内核版本: 3.10.0-957.1.3.el7.x86_64
    ii.无法创建/启动新的容器，删除部分已有容器后，可创建近似数量的新容器；
    iii.主机buff/cache内存缓慢增长，且无法通过手动清理恢复
    iv.通过cat /sys/fs/cgroup/memory/docker/memory.kmem.usage_in_bytes查看docker占用的kernel memory, 停止所有容器或docker服务后也不会释放
b.可能原因：
    i.内核存在kmem使用的相关BUG:
    ii.docker 的release都默认开启使用kmem
    iii.docker在容器调度的过程中，不断触发相关BUG导致kmem泄漏
c.修复方法:
    i.替换docker binary文件
        1.获取docker二进制文件位置： which docker
        2.停止docker服务：sudo systemctl stop docker
        3.下载禁用了kmem的docker二进制文件： docker-binary-17.12.tar 或者172.16.1.99/gold/docker-ce:17.12 镜像中 /root/docker目录下；
        4.使用2中解压出的二进制文件，替换1中获取到的对应路径下的文件；可通过docker version（应为：17.12.1-ce）确定是否替换成功
        5.重启主机以释放泄漏的kernel memory
d.相关讨论：
    i.https://tencentcloudcontainerteam.github.io/2018/12/29/cgroup-leaking/
    ii.https://github.com/opencontainers/runc/pull/1921
    iii.https://github.com/moby/moby/issues/29638


docker 网络问题，开启 iptables
对于Sophon Edge r21.04 LTS 及其之前的版本，微服务之前通过bridge网络进行通信，需要启用docker的iptables功能。
但对于部分情况下， Edge可能部署在TOS环境上，而TOS默认禁用掉了docker的iptables， 从而导致Edge服务间的网络可能出现异常。具体解决方案如下：
1.验证是否禁用了iptables; 若输出为空，则可以认为未禁用
    systemctl status docker|grep "iptables=false"
2.若如下图所示，docker的iptables已被禁用，则需要修改docker.service的相关配置
3.获取docker.service的配置文件路径，并进行编辑 systemctl status docker|grep loaded
4.通常TOS对于iptables的禁用定义在 ${DOCKER_OPTS} 环境变量中，而该环境变量通常位于 /etc/sysconfig/docker，因此需要对其进行编辑。
5.配置环境变量 ${DOCKER_OPTS}， vim /etc/sysconfig/docker, 删除红色框线内的部分并保存。
6.重启docker服务。 注意：请确保重启docker不会影响其它运行在当前服务器上的服务
    $ sudo systemctl daemon-reload
    $ sudo systemctl restart docker
7.执行iptables -P FORWARD ACCEPT


Multimedia core dump常见原因：
a.RTSP源(TCP模式) + src crop 或 resize
    i.相关讨论：https://forums.developer.nvidia.com/t/nvvideoconvert-crashes-on-rtsp-input-src-crop-xw-h-pipeline/81317/32
    ii.解决方案：
        1.Jetson平台替换:libgstnvvideoconvert.zip
        2.X86平台替换：libgstnvvideoconvert .zip
        3.暂时解决方案：
            a.切换RTSP源为UDP模式
            b.切换为软解码


多媒体pipeline启动失败常见问题排查：
问题1：not-negotiated
    报错信息.transform could not transform video/x-raw(memory:NVMM) ...in anything we support
    错误原因：使用nvidia runtime启动pipeline时，可能decodebin会自动选择硬件加速的解码方式，但是该方式解码出的格式 “video/x-raw(memory:NVMM)” 后不支持使用 videoconvert算子
    解决方案：
        a.使用runc的docker runtime启动对应容器，可通过 docker run --runtime=runc 或 在docker-compose.yml中添加 runtime: runc的配置
        b.将 "videoconvert" 算子替换为 "nvvideoconvert"
问题2：b.not-linked
    报错信息：task task has an error, ERROR: gst-stream-error-quark: Internal data stream error.
    错误原因：当解码组件或自动解码器选择硬件加速解码后,其数据格式为 video/x-raw(NVMM), 故经帧率控制算子（videorate + capsfilter video/x-raw,framerate）时, videorate sink的x-raw(NVMM)无法与下游算子(capsfilter video/x-raw,framerate )进一步协商，从而导致无法连接
    解决方案：
        a.将帧率控制算子中的 video/x-raw限制去除
        b.根据具体上下游算子需要的capability进行灵活设置


处理区域算子失效(get object rect fail or position invalid, using full frame)
规则最终结果中可能包含了所绘制区域外部的信息，即设置处理区域时失败。日志中可见get object rect fail or position invalid, using full frame 字样。
出现该现象的原因一般为，所设处理区域的范围，超出了帧画面的范围。导致无法对原始帧进行裁剪，因此向后转发的为未裁剪的原始帧，从而导致了非预期结果的出现。
解决方法：尝试把区域稍微画小一点， 尽量不要贴着视频画面的边界。


前端渲染相关属性字段介绍（Faas或算子中设置的Meta字段）
前端主要通过在video上绘制一层canvas实现Overlay:
SEI Data数据结构：
注： SEI数据结构为 SeiData[]。
interface SeiData {
  data: string; //JSONString,结构见下方描述
  pts: number // Presentation Time Stamp 显示时间戳，这个时间戳用来告诉程序该在什么时候显示这一帧的数据。
  dts: number // Decoding Time Stamp 解码时间戳，这个时间戳的意义在于告诉程序该在什么时候解码这一帧的数据。
}


SeiData中的data转成的JSON结构：
注：其中的data可能为 IData 或 IData[]。
推理结果数据结构
interface IData {
  // 渲染框线画线方式：默认不传为多边形连结，其他自定义连结方式需要传入唯一position_type标识符，提供点线连结方式。
  position_type?: string; 
  // 点坐标集合
  position: number[][]; 
  // 框线的属性信息，文字描述
  attributes: IDetectionObjectAttr[]; 
  // 展示的字体颜色 例: #00000 或 red，默认值：#DED921
  color: string; 
  // 字体大小 默认值：12，画布resize时会自适应大小
  font_size: number;
  // 字体 默认值："sophone-font"
  font_family: string;
  // 框线的宽度，画框粗细值 默认值：2
  line_width: number; 
  // 整个SEI单元是否需要进行渲染，默认值: true
  shown: boolean; 
  // 框线是否显示 默认值：true
  position_shown: boolean; 
  // 角类型，两线相交处理方式,bevel:创建尖角;miter:创建斜角; round: 创建圆角; 默认值: bevel
  line_join: "bevel" | "miter" | "round"; 
  // 整个SEI支持多层嵌套，color, font_size, font_family, line_width, shown, position_shown, line_join属性若无指定会继承上级
  [index: string]: IData[] | IData 
}


IFrame使用问题:
前端报错信息：Refused to display '........' in a frame because it set 'X-Frame-Options' to 'deny'
解决方法：修改前端镜像中的Nginx配置 docker exec -ti thinger-frontend bash，修改nginx配置文件/etc/nginx/conf.d/default.conf ， 删除其中的 add_header X-Frame-Options DENY; 配置后，重启nginx服务即可nginx -s reload
注意：该方法仅能临时解决该问题，在容器重启后将恢复原始配置。如需要长期修复该问题，请联系研发同学提供支持。


在Faas中使用pip安装指定包(PS: r20.09及以后版本已经支持配置requirements.txt文件)
以引用基础镜像中不存在'pandas'为例，在handler函数前添加以下代码：
import sys
import subprocess
def install(package):
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-U', '--no-cache-dir', '-i', 'http://mirrors.aliyun.com/pypi/simple/', '--trusted-host','mirrors.aliyun.com',package])
install('pandas')
import pandas


mvcc database space exceeded 问题解决方案
该错误为系统资源数据库thigner-rdb的数据库文件超限，需要进行碎片整理以压缩空间，可参考如下流程：
# 1. 获取当前etcd数据的修订版本(revision)
rev=$(docker exec -e ETCDCTL_API=3 thinger-rdb etcdctl endpoint status --write-out=json | grep -E -o '"revision":[0-9]*' | grep -E -o '[0-9]*')
# 2. 整合压缩旧版本数据
docker exec -e ETCDCTL_API=3 thinger-rdb etcdctl compact "$rev"
# 3. 执行碎片整理
docker exec -e ETCDCTL_API=3 thinger-rdb etcdctl defrag --command-timeout=90s
# 4. 解除告警
docker exec -e ETCDCTL_API=3 thinger-rdb etcdctl alarm disarm