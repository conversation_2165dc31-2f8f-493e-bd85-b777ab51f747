package pb

import (
	"reflect"
	"testing"
)

func TestReadModelConfig(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name    string
		args    args
		want    *ModelConfig
		wantErr bool
	}{
		{
			name: "test unmarshal text to model config",
			args: args{
				path: "test/config.pbtxt",
			},
			want: &ModelConfig{
				Name:         "dev_ensemble",
				Platform:     "ensemble",
				Backend:      "",
				MaxBatchSize: 0,
				Input: []*ModelInput{{
					Name:     "IMAGE_BINARY",
					DataType: DataType_TYPE_STRING,
					Dims:     []int64{1},
				}, {
					Name:     "IMAGE_DESC",
					DataType: DataType_TYPE_STRING,
					Dims:     []int64{1},
				}},
				Output:                 []*ModelOutput{{
					Name:     "RESULT",
					DataType: DataType_TYPE_STRING,
					Dims:     []int64{1},
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ReadModelConfigFile(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReadModelConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReadModelConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}
