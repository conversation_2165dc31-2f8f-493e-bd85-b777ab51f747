// Copyright (c) 2020, NVIDIA CORPORATION. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
//  * Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//  * Neither the name of NVIDIA CORPORATION nor the names of its
//    contributors may be used to endorse or promote products derived
//    from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS ``AS IS'' AND ANY
// EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
// CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
// EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
// PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
// PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
// OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: pb/grpc_service.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// @@
// @@.. cpp:var:: message ServerLiveRequest
// @@
// @@   Request message for ServerLive.
// @@
type ServerLiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ServerLiveRequest) Reset() {
	*x = ServerLiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerLiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerLiveRequest) ProtoMessage() {}

func (x *ServerLiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerLiveRequest.ProtoReflect.Descriptor instead.
func (*ServerLiveRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{0}
}

// @@
// @@.. cpp:var:: message ServerLiveResponse
// @@
// @@   Response message for ServerLive.
// @@
type ServerLiveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: bool live
	// @@
	// @@     True if the inference server is live, false it not live.
	// @@
	Live bool `protobuf:"varint,1,opt,name=live,proto3" json:"live,omitempty"`
}

func (x *ServerLiveResponse) Reset() {
	*x = ServerLiveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerLiveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerLiveResponse) ProtoMessage() {}

func (x *ServerLiveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerLiveResponse.ProtoReflect.Descriptor instead.
func (*ServerLiveResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{1}
}

func (x *ServerLiveResponse) GetLive() bool {
	if x != nil {
		return x.Live
	}
	return false
}

// @@
// @@.. cpp:var:: message ServerReadyRequest
// @@
// @@   Request message for ServerReady.
// @@
type ServerReadyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ServerReadyRequest) Reset() {
	*x = ServerReadyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerReadyRequest) ProtoMessage() {}

func (x *ServerReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerReadyRequest.ProtoReflect.Descriptor instead.
func (*ServerReadyRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{2}
}

// @@
// @@.. cpp:var:: message ServerReadyResponse
// @@
// @@   Response message for ServerReady.
// @@
type ServerReadyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: bool ready
	// @@
	// @@     True if the inference server is ready, false it not ready.
	// @@
	Ready bool `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
}

func (x *ServerReadyResponse) Reset() {
	*x = ServerReadyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerReadyResponse) ProtoMessage() {}

func (x *ServerReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerReadyResponse.ProtoReflect.Descriptor instead.
func (*ServerReadyResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{3}
}

func (x *ServerReadyResponse) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

// @@
// @@.. cpp:var:: message ModelReadyRequest
// @@
// @@   Request message for ModelReady.
// @@
type ModelReadyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model to check for readiness.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string version
	// @@
	// @@     The version of the model to check for readiness. If not given the
	// @@     server will choose a version based on the model and internal policy.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelReadyRequest) Reset() {
	*x = ModelReadyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReadyRequest) ProtoMessage() {}

func (x *ModelReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReadyRequest.ProtoReflect.Descriptor instead.
func (*ModelReadyRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{4}
}

func (x *ModelReadyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelReadyRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// @@
// @@.. cpp:var:: message ModelReadyResponse
// @@
// @@   Response message for ModelReady.
// @@
type ModelReadyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: bool ready
	// @@
	// @@     True if the model is ready, false it not ready.
	// @@
	Ready bool `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
}

func (x *ModelReadyResponse) Reset() {
	*x = ModelReadyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReadyResponse) ProtoMessage() {}

func (x *ModelReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReadyResponse.ProtoReflect.Descriptor instead.
func (*ModelReadyResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{5}
}

func (x *ModelReadyResponse) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

// @@
// @@.. cpp:var:: message ServerMetadataRequest
// @@
// @@   Request message for ServerMetadata.
// @@
type ServerMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ServerMetadataRequest) Reset() {
	*x = ServerMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerMetadataRequest) ProtoMessage() {}

func (x *ServerMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerMetadataRequest.ProtoReflect.Descriptor instead.
func (*ServerMetadataRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{6}
}

// @@
// @@.. cpp:var:: message ServerMetadataResponse
// @@
// @@   Response message for ServerMetadata.
// @@
type ServerMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The server name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@
	// @@  .. cpp:var:: string version
	// @@
	// @@     The server version.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// @@
	// @@  .. cpp:var:: string extensions (repeated)
	// @@
	// @@     The extensions supported by the server.
	// @@
	Extensions []string `protobuf:"bytes,3,rep,name=extensions,proto3" json:"extensions,omitempty"`
}

func (x *ServerMetadataResponse) Reset() {
	*x = ServerMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerMetadataResponse) ProtoMessage() {}

func (x *ServerMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerMetadataResponse.ProtoReflect.Descriptor instead.
func (*ServerMetadataResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{7}
}

func (x *ServerMetadataResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServerMetadataResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServerMetadataResponse) GetExtensions() []string {
	if x != nil {
		return x.Extensions
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelMetadataRequest
// @@
// @@   Request message for ModelMetadata.
// @@
type ModelMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string version
	// @@
	// @@     The version of the model to check for readiness. If not
	// @@     given the server will choose a version based on the
	// @@     model and internal policy.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelMetadataRequest) Reset() {
	*x = ModelMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetadataRequest) ProtoMessage() {}

func (x *ModelMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetadataRequest.ProtoReflect.Descriptor instead.
func (*ModelMetadataRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{8}
}

func (x *ModelMetadataRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelMetadataRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// @@
// @@.. cpp:var:: message ModelMetadataResponse
// @@
// @@   Response message for ModelMetadata.
// @@
type ModelMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The model name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@
	// @@  .. cpp:var:: string versions (repeated)
	// @@
	// @@     The versions of the model.
	// @@
	Versions []string `protobuf:"bytes,2,rep,name=versions,proto3" json:"versions,omitempty"`
	// @@
	// @@  .. cpp:var:: string platform
	// @@
	// @@     The model's platform.
	// @@
	Platform string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`
	// @@
	// @@  .. cpp:var:: TensorMetadata inputs (repeated)
	// @@
	// @@     The model's inputs.
	// @@
	Inputs []*ModelMetadataResponse_TensorMetadata `protobuf:"bytes,4,rep,name=inputs,proto3" json:"inputs,omitempty"`
	// @@
	// @@  .. cpp:var:: TensorMetadata outputs (repeated)
	// @@
	// @@     The model's outputs.
	// @@
	Outputs []*ModelMetadataResponse_TensorMetadata `protobuf:"bytes,5,rep,name=outputs,proto3" json:"outputs,omitempty"`
}

func (x *ModelMetadataResponse) Reset() {
	*x = ModelMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetadataResponse) ProtoMessage() {}

func (x *ModelMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetadataResponse.ProtoReflect.Descriptor instead.
func (*ModelMetadataResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{9}
}

func (x *ModelMetadataResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelMetadataResponse) GetVersions() []string {
	if x != nil {
		return x.Versions
	}
	return nil
}

func (x *ModelMetadataResponse) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ModelMetadataResponse) GetInputs() []*ModelMetadataResponse_TensorMetadata {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *ModelMetadataResponse) GetOutputs() []*ModelMetadataResponse_TensorMetadata {
	if x != nil {
		return x.Outputs
	}
	return nil
}

// @@
// @@.. cpp:var:: message InferParameter
// @@
// @@   An inference parameter value.
// @@
type InferParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: oneof parameter_choice
	// @@
	// @@     The parameter value can be a string, an int64 or
	// @@     a boolean
	// @@
	//
	// Types that are assignable to ParameterChoice:
	//
	//	*InferParameter_BoolParam
	//	*InferParameter_Int64Param
	//	*InferParameter_StringParam
	ParameterChoice isInferParameter_ParameterChoice `protobuf_oneof:"parameter_choice"`
}

func (x *InferParameter) Reset() {
	*x = InferParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferParameter) ProtoMessage() {}

func (x *InferParameter) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferParameter.ProtoReflect.Descriptor instead.
func (*InferParameter) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{10}
}

func (m *InferParameter) GetParameterChoice() isInferParameter_ParameterChoice {
	if m != nil {
		return m.ParameterChoice
	}
	return nil
}

func (x *InferParameter) GetBoolParam() bool {
	if x, ok := x.GetParameterChoice().(*InferParameter_BoolParam); ok {
		return x.BoolParam
	}
	return false
}

func (x *InferParameter) GetInt64Param() int64 {
	if x, ok := x.GetParameterChoice().(*InferParameter_Int64Param); ok {
		return x.Int64Param
	}
	return 0
}

func (x *InferParameter) GetStringParam() string {
	if x, ok := x.GetParameterChoice().(*InferParameter_StringParam); ok {
		return x.StringParam
	}
	return ""
}

type isInferParameter_ParameterChoice interface {
	isInferParameter_ParameterChoice()
}

type InferParameter_BoolParam struct {
	// @@    .. cpp:var:: bool bool_param
	// @@
	// @@       A boolean parameter value.
	// @@
	BoolParam bool `protobuf:"varint,1,opt,name=bool_param,json=boolParam,proto3,oneof"`
}

type InferParameter_Int64Param struct {
	// @@    .. cpp:var:: int64 int64_param
	// @@
	// @@       An int64 parameter value.
	// @@
	Int64Param int64 `protobuf:"varint,2,opt,name=int64_param,json=int64Param,proto3,oneof"`
}

type InferParameter_StringParam struct {
	// @@    .. cpp:var:: string string_param
	// @@
	// @@       A string parameter value.
	// @@
	StringParam string `protobuf:"bytes,3,opt,name=string_param,json=stringParam,proto3,oneof"`
}

func (*InferParameter_BoolParam) isInferParameter_ParameterChoice() {}

func (*InferParameter_Int64Param) isInferParameter_ParameterChoice() {}

func (*InferParameter_StringParam) isInferParameter_ParameterChoice() {}

// @@
// @@.. cpp:var:: message InferTensorContents
// @@
// @@   The data contained in a tensor represented by the repeated type
// @@   that matches the tensor's data type. Protobuf oneof is not used
// @@   because oneofs cannot contain repeated fields.
// @@
type InferTensorContents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: bool bool_contents (repeated)
	// @@
	// @@     Representation for BOOL data type. The size must match what is
	// @@     expected by the tensor's shape. The contents must be the flattened,
	// @@     one-dimensional, row-major order of the tensor elements.
	// @@
	BoolContents []bool `protobuf:"varint,1,rep,packed,name=bool_contents,json=boolContents,proto3" json:"bool_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: int32 int_contents (repeated)
	// @@
	// @@     Representation for INT8, INT16, and INT32 data types. The size
	// @@     must match what is expected by the tensor's shape. The contents
	// @@     must be the flattened, one-dimensional, row-major order of the
	// @@     tensor elements.
	// @@
	IntContents []int32 `protobuf:"varint,2,rep,packed,name=int_contents,json=intContents,proto3" json:"int_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: int64 int64_contents (repeated)
	// @@
	// @@     Representation for INT64 data types. The size must match what
	// @@     is expected by the tensor's shape. The contents must be the
	// @@     flattened, one-dimensional, row-major order of the tensor elements.
	// @@
	Int64Contents []int64 `protobuf:"varint,3,rep,packed,name=int64_contents,json=int64Contents,proto3" json:"int64_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: uint32 uint_contents (repeated)
	// @@
	// @@     Representation for UINT8, UINT16, and UINT32 data types. The size
	// @@     must match what is expected by the tensor's shape. The contents
	// @@     must be the flattened, one-dimensional, row-major order of the
	// @@     tensor elements.
	// @@
	UintContents []uint32 `protobuf:"varint,4,rep,packed,name=uint_contents,json=uintContents,proto3" json:"uint_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: uint64 uint64_contents (repeated)
	// @@
	// @@     Representation for UINT64 data types. The size must match what
	// @@     is expected by the tensor's shape. The contents must be the
	// @@     flattened, one-dimensional, row-major order of the tensor elements.
	// @@
	Uint64Contents []uint64 `protobuf:"varint,5,rep,packed,name=uint64_contents,json=uint64Contents,proto3" json:"uint64_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: float fp32_contents (repeated)
	// @@
	// @@     Representation for FP32 data type. The size must match what is
	// @@     expected by the tensor's shape. The contents must be the flattened,
	// @@     one-dimensional, row-major order of the tensor elements.
	// @@
	Fp32Contents []float32 `protobuf:"fixed32,6,rep,packed,name=fp32_contents,json=fp32Contents,proto3" json:"fp32_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: double fp64_contents (repeated)
	// @@
	// @@     Representation for FP64 data type. The size must match what is
	// @@     expected by the tensor's shape. The contents must be the flattened,
	// @@     one-dimensional, row-major order of the tensor elements.
	// @@
	Fp64Contents []float64 `protobuf:"fixed64,7,rep,packed,name=fp64_contents,json=fp64Contents,proto3" json:"fp64_contents,omitempty"`
	// @@
	// @@  .. cpp:var:: bytes bytes_contents (repeated)
	// @@
	// @@     Representation for BYTES data type. The size must match what is
	// @@     expected by the tensor's shape. The contents must be the flattened,
	// @@     one-dimensional, row-major order of the tensor elements.
	// @@
	BytesContents [][]byte `protobuf:"bytes,8,rep,name=bytes_contents,json=bytesContents,proto3" json:"bytes_contents,omitempty"`
}

func (x *InferTensorContents) Reset() {
	*x = InferTensorContents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferTensorContents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferTensorContents) ProtoMessage() {}

func (x *InferTensorContents) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferTensorContents.ProtoReflect.Descriptor instead.
func (*InferTensorContents) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{11}
}

func (x *InferTensorContents) GetBoolContents() []bool {
	if x != nil {
		return x.BoolContents
	}
	return nil
}

func (x *InferTensorContents) GetIntContents() []int32 {
	if x != nil {
		return x.IntContents
	}
	return nil
}

func (x *InferTensorContents) GetInt64Contents() []int64 {
	if x != nil {
		return x.Int64Contents
	}
	return nil
}

func (x *InferTensorContents) GetUintContents() []uint32 {
	if x != nil {
		return x.UintContents
	}
	return nil
}

func (x *InferTensorContents) GetUint64Contents() []uint64 {
	if x != nil {
		return x.Uint64Contents
	}
	return nil
}

func (x *InferTensorContents) GetFp32Contents() []float32 {
	if x != nil {
		return x.Fp32Contents
	}
	return nil
}

func (x *InferTensorContents) GetFp64Contents() []float64 {
	if x != nil {
		return x.Fp64Contents
	}
	return nil
}

func (x *InferTensorContents) GetBytesContents() [][]byte {
	if x != nil {
		return x.BytesContents
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelInferRequest
// @@
// @@   Request message for ModelInfer.
// @@
type ModelInferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string model_name
	// @@
	// @@     The name of the model to use for inferencing.
	// @@
	ModelName string `protobuf:"bytes,1,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// @@  .. cpp:var:: string model_version
	// @@
	// @@     The version of the model to use for inference. If not
	// @@     given the latest/most-recent version of the model is used.
	// @@
	ModelVersion string `protobuf:"bytes,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// @@  .. cpp:var:: string id
	// @@
	// @@     Optional identifier for the request. If specified will be
	// @@     returned in the response.
	// @@
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// @@  .. cpp:var:: map<string,InferParameter> parameters
	// @@
	// @@     Optional inference parameters.
	// @@
	Parameters map[string]*InferParameter `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@
	// @@  .. cpp:var:: InferInputTensor inputs (repeated)
	// @@
	// @@     The input tensors for the inference.
	// @@
	Inputs []*ModelInferRequest_InferInputTensor `protobuf:"bytes,5,rep,name=inputs,proto3" json:"inputs,omitempty"`
	// @@
	// @@  .. cpp:var:: InferRequestedOutputTensor outputs (repeated)
	// @@
	// @@     The requested output tensors for the inference. Optional, if not
	// @@     specified all outputs specified in the model config will be
	// @@     returned.
	// @@
	Outputs []*ModelInferRequest_InferRequestedOutputTensor `protobuf:"bytes,6,rep,name=outputs,proto3" json:"outputs,omitempty"`
	// @@
	// @@  .. cpp:var:: bytes raw_input_contents
	// @@
	// @@     The data contained in an input tensor can be represented in
	// @@     "raw" bytes form or in the repeated type that matches the
	// @@     tensor's data type. Using the "raw" bytes form will
	// @@     typically allow higher performance due to the way protobuf
	// @@     allocation and reuse interacts with GRPC. For example, see
	// @@     https://github.com/grpc/grpc/issues/23231.
	// @@
	// @@     To use the raw representation 'raw_input_contents' must be
	// @@     initialized with data for each tensor in the same order as
	// @@     'inputs'. For each tensor, the size of this content must
	// @@     match what is expected by the tensor's shape and data
	// @@     type. The raw data must be the flattened, one-dimensional,
	// @@     row-major order of the tensor elements without any stride
	// @@     or padding between the elements. Note that the FP16 data
	// @@     type must be represented as raw content as there is no
	// @@     specific data type for a 16-bit float type.
	// @@
	// @@     If this field is specified then InferInputTensor::contents
	// @@     must not be specified for any input tensor.
	// @@
	RawInputContents [][]byte `protobuf:"bytes,7,rep,name=raw_input_contents,json=rawInputContents,proto3" json:"raw_input_contents,omitempty"`
}

func (x *ModelInferRequest) Reset() {
	*x = ModelInferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferRequest) ProtoMessage() {}

func (x *ModelInferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferRequest.ProtoReflect.Descriptor instead.
func (*ModelInferRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{12}
}

func (x *ModelInferRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelInferRequest) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *ModelInferRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModelInferRequest) GetParameters() map[string]*InferParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelInferRequest) GetInputs() []*ModelInferRequest_InferInputTensor {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *ModelInferRequest) GetOutputs() []*ModelInferRequest_InferRequestedOutputTensor {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *ModelInferRequest) GetRawInputContents() [][]byte {
	if x != nil {
		return x.RawInputContents
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelInferResponse
// @@
// @@   Response message for ModelInfer.
// @@
type ModelInferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string model_name
	// @@
	// @@     The name of the model used for inference.
	// @@
	ModelName string `protobuf:"bytes,1,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// @@  .. cpp:var:: string model_version
	// @@
	// @@     The version of the model used for inference.
	// @@
	ModelVersion string `protobuf:"bytes,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// @@  .. cpp:var:: string id
	// @@
	// @@     The id of the inference request if one was specified.
	// @@
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// @@  .. cpp:var:: map<string,InferParameter> parameters
	// @@
	// @@     Optional inference response parameters.
	// @@
	Parameters map[string]*InferParameter `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@
	// @@  .. cpp:var:: InferOutputTensor outputs (repeated)
	// @@
	// @@     The output tensors holding inference results.
	// @@
	Outputs []*ModelInferResponse_InferOutputTensor `protobuf:"bytes,5,rep,name=outputs,proto3" json:"outputs,omitempty"`
	// @@
	// @@  .. cpp:var:: bytes raw_output_contents
	// @@
	// @@     The data contained in an output tensor can be represented in
	// @@     "raw" bytes form or in the repeated type that matches the
	// @@     tensor's data type. Using the "raw" bytes form will
	// @@     typically allow higher performance due to the way protobuf
	// @@     allocation and reuse interacts with GRPC. For example, see
	// @@     https://github.com/grpc/grpc/issues/23231.
	// @@
	// @@     To use the raw representation 'raw_output_contents' must be
	// @@     initialized with data for each tensor in the same order as
	// @@     'outputs'. For each tensor, the size of this content must
	// @@     match what is expected by the tensor's shape and data
	// @@     type. The raw data must be the flattened, one-dimensional,
	// @@     row-major order of the tensor elements without any stride
	// @@     or padding between the elements. Note that the FP16 data
	// @@     type must be represented as raw content as there is no
	// @@     specific data type for a 16-bit float type.
	// @@
	// @@     If this field is specified then InferOutputTensor::contents
	// @@     must not be specified for any output tensor.
	// @@
	RawOutputContents [][]byte `protobuf:"bytes,6,rep,name=raw_output_contents,json=rawOutputContents,proto3" json:"raw_output_contents,omitempty"`
}

func (x *ModelInferResponse) Reset() {
	*x = ModelInferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferResponse) ProtoMessage() {}

func (x *ModelInferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferResponse.ProtoReflect.Descriptor instead.
func (*ModelInferResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{13}
}

func (x *ModelInferResponse) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelInferResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *ModelInferResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModelInferResponse) GetParameters() map[string]*InferParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelInferResponse) GetOutputs() []*ModelInferResponse_InferOutputTensor {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *ModelInferResponse) GetRawOutputContents() [][]byte {
	if x != nil {
		return x.RawOutputContents
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelStreamInferResponse
// @@
// @@   Response message for ModelStreamInfer.
// @@
type ModelStreamInferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string error_message
	// @@
	// @@     The message describing the error. The empty message
	// @@     indicates the inference was successful without errors.
	// @@
	ErrorMessage string `protobuf:"bytes,1,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// @@
	// @@  .. cpp:var:: ModelInferResponse infer_response
	// @@
	// @@     Holds the results of the request.
	// @@
	InferResponse *ModelInferResponse `protobuf:"bytes,2,opt,name=infer_response,json=inferResponse,proto3" json:"infer_response,omitempty"`
}

func (x *ModelStreamInferResponse) Reset() {
	*x = ModelStreamInferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStreamInferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStreamInferResponse) ProtoMessage() {}

func (x *ModelStreamInferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStreamInferResponse.ProtoReflect.Descriptor instead.
func (*ModelStreamInferResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{14}
}

func (x *ModelStreamInferResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ModelStreamInferResponse) GetInferResponse() *ModelInferResponse {
	if x != nil {
		return x.InferResponse
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelConfigRequest
// @@
// @@   Request message for ModelConfig.
// @@
type ModelConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string version
	// @@
	// @@     The version of the model. If not given the model version
	// @@     is selected automatically based on the version policy.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelConfigRequest) Reset() {
	*x = ModelConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfigRequest) ProtoMessage() {}

func (x *ModelConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfigRequest.ProtoReflect.Descriptor instead.
func (*ModelConfigRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{15}
}

func (x *ModelConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelConfigRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// @@
// @@.. cpp:var:: message ModelConfigResponse
// @@
// @@   Response message for ModelConfig.
// @@
type ModelConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: ModelConfig config
	// @@
	// @@     The model configuration.
	// @@
	Config *ModelConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *ModelConfigResponse) Reset() {
	*x = ModelConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfigResponse) ProtoMessage() {}

func (x *ModelConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfigResponse.ProtoReflect.Descriptor instead.
func (*ModelConfigResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{16}
}

func (x *ModelConfigResponse) GetConfig() *ModelConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelStatisticsRequest
// @@
// @@   Request message for ModelStatistics.
// @@
type ModelStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model. If not given returns statistics for
	// @@     all models.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string version
	// @@
	// @@     The version of the model. If not given returns statistics for
	// @@     all model versions.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelStatisticsRequest) Reset() {
	*x = ModelStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStatisticsRequest) ProtoMessage() {}

func (x *ModelStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStatisticsRequest.ProtoReflect.Descriptor instead.
func (*ModelStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{17}
}

func (x *ModelStatisticsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelStatisticsRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// @@
// @@.. cpp:var:: message StatisticDuration
// @@
// @@   Statistic recording a cumulative duration metric.
// @@
type StatisticDuration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: uint64 count
	// @@
	// @@     Cumulative number of times this metric occurred.
	// @@
	Count uint64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// @@  .. cpp:var:: uint64 total_time_ns
	// @@
	// @@     Total collected duration of this metric in nanoseconds.
	// @@
	Ns uint64 `protobuf:"varint,2,opt,name=ns,proto3" json:"ns,omitempty"`
}

func (x *StatisticDuration) Reset() {
	*x = StatisticDuration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticDuration) ProtoMessage() {}

func (x *StatisticDuration) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticDuration.ProtoReflect.Descriptor instead.
func (*StatisticDuration) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{18}
}

func (x *StatisticDuration) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *StatisticDuration) GetNs() uint64 {
	if x != nil {
		return x.Ns
	}
	return 0
}

// @@
// @@.. cpp:var:: message InferStatistics
// @@
// @@   Inference statistics.
// @@
type InferStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: StatisticDuration success
	// @@
	// @@     Cumulative count and duration for successful inference
	// @@     request. The "success" count and cumulative duration includes
	// @@     cache hits.
	// @@
	Success *StatisticDuration `protobuf:"bytes,1,opt,name=success,proto3" json:"success,omitempty"`
	// @@  .. cpp:var:: StatisticDuration fail
	// @@
	// @@     Cumulative count and duration for failed inference
	// @@     request.
	// @@
	Fail *StatisticDuration `protobuf:"bytes,2,opt,name=fail,proto3" json:"fail,omitempty"`
	// @@  .. cpp:var:: StatisticDuration queue
	// @@
	// @@     The count and cumulative duration that inference requests wait in
	// @@     scheduling or other queues. The "queue" count and cumulative
	// @@     duration includes cache hits.
	// @@
	Queue *StatisticDuration `protobuf:"bytes,3,opt,name=queue,proto3" json:"queue,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_input
	// @@
	// @@     The count and cumulative duration to prepare input tensor data as
	// @@     required by the model framework / backend. For example, this duration
	// @@     should include the time to copy input tensor data to the GPU.
	// @@     The "compute_input" count and cumulative duration do not account for
	// @@     requests that were a cache hit. See the "cache_hit" field for more
	// @@     info.
	// @@
	ComputeInput *StatisticDuration `protobuf:"bytes,4,opt,name=compute_input,json=computeInput,proto3" json:"compute_input,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_infer
	// @@
	// @@     The count and cumulative duration to execute the model.
	// @@     The "compute_infer" count and cumulative duration do not account for
	// @@     requests that were a cache hit. See the "cache_hit" field for more
	// @@     info.
	// @@
	ComputeInfer *StatisticDuration `protobuf:"bytes,5,opt,name=compute_infer,json=computeInfer,proto3" json:"compute_infer,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_output
	// @@
	// @@     The count and cumulative duration to extract output tensor data
	// @@     produced by the model framework / backend. For example, this duration
	// @@     should include the time to copy output tensor data from the GPU.
	// @@     The "compute_output" count and cumulative duration do not account for
	// @@     requests that were a cache hit. See the "cache_hit" field for more
	// @@     info.
	// @@
	ComputeOutput *StatisticDuration `protobuf:"bytes,6,opt,name=compute_output,json=computeOutput,proto3" json:"compute_output,omitempty"`
	// @@  .. cpp:var:: StatisticDuration cache_hit
	// @@
	// @@     The count of response cache hits and cumulative duration to lookup
	// @@     and extract output tensor data from the Response Cache on a cache
	// @@     hit. For example, this duration should include the time to copy
	// @@     output tensor data from the Response Cache to the response object.
	// @@     On cache hits, triton does not need to go to the model/backend
	// @@     for the output tensor data, so the "compute_input", "compute_infer",
	// @@     and "compute_output" fields are not updated. Assuming the response
	// @@     cache is enabled for a given model, a cache hit occurs for a
	// @@     request to that model when the request metadata (model name,
	// @@     model version, model inputs) hashes to an existing entry in the
	// @@     cache. On a cache miss, the request hash and response output tensor
	// @@     data is added to the cache. See response cache docs for more info:
	// @@     https://github.com/triton-inference-server/server/blob/main/docs/response_cache.md
	// @@
	CacheHit *StatisticDuration `protobuf:"bytes,7,opt,name=cache_hit,json=cacheHit,proto3" json:"cache_hit,omitempty"`
	// @@  .. cpp:var:: StatisticDuration cache_miss
	// @@
	// @@     The count of response cache misses and cumulative duration to lookup
	// @@     and insert output tensor data from the computed response to the cache.
	// @@     For example, this duration should include the time to copy
	// @@     output tensor data from the response object to the Response Cache.
	// @@     Assuming the response cache is enabled for a given model, a cache
	// @@     miss occurs for a request to that model when the request metadata
	// @@     does NOT hash to an existing entry in the cache. See the response
	// @@     cache docs for more info:
	// @@     https://github.com/triton-inference-server/server/blob/main/docs/response_cache.md
	// @@
	CacheMiss *StatisticDuration `protobuf:"bytes,8,opt,name=cache_miss,json=cacheMiss,proto3" json:"cache_miss,omitempty"`
}

func (x *InferStatistics) Reset() {
	*x = InferStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferStatistics) ProtoMessage() {}

func (x *InferStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferStatistics.ProtoReflect.Descriptor instead.
func (*InferStatistics) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{19}
}

func (x *InferStatistics) GetSuccess() *StatisticDuration {
	if x != nil {
		return x.Success
	}
	return nil
}

func (x *InferStatistics) GetFail() *StatisticDuration {
	if x != nil {
		return x.Fail
	}
	return nil
}

func (x *InferStatistics) GetQueue() *StatisticDuration {
	if x != nil {
		return x.Queue
	}
	return nil
}

func (x *InferStatistics) GetComputeInput() *StatisticDuration {
	if x != nil {
		return x.ComputeInput
	}
	return nil
}

func (x *InferStatistics) GetComputeInfer() *StatisticDuration {
	if x != nil {
		return x.ComputeInfer
	}
	return nil
}

func (x *InferStatistics) GetComputeOutput() *StatisticDuration {
	if x != nil {
		return x.ComputeOutput
	}
	return nil
}

func (x *InferStatistics) GetCacheHit() *StatisticDuration {
	if x != nil {
		return x.CacheHit
	}
	return nil
}

func (x *InferStatistics) GetCacheMiss() *StatisticDuration {
	if x != nil {
		return x.CacheMiss
	}
	return nil
}

// @@
// @@.. cpp:var:: message InferBatchStatistics
// @@
// @@   Inference batch statistics.
// @@
type InferBatchStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: uint64 batch_size
	// @@
	// @@     The size of the batch.
	// @@
	BatchSize uint64 `protobuf:"varint,1,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_input
	// @@
	// @@     The count and cumulative duration to prepare input tensor data as
	// @@     required by the model framework / backend with the given batch size.
	// @@     For example, this duration should include the time to copy input
	// @@     tensor data to the GPU.
	// @@
	ComputeInput *StatisticDuration `protobuf:"bytes,2,opt,name=compute_input,json=computeInput,proto3" json:"compute_input,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_infer
	// @@
	// @@     The count and cumulative duration to execute the model with the given
	// @@     batch size.
	// @@
	ComputeInfer *StatisticDuration `protobuf:"bytes,3,opt,name=compute_infer,json=computeInfer,proto3" json:"compute_infer,omitempty"`
	// @@  .. cpp:var:: StatisticDuration compute_output
	// @@
	// @@     The count and cumulative duration to extract output tensor data
	// @@     produced by the model framework / backend with the given batch size.
	// @@     For example, this duration should include the time to copy output
	// @@     tensor data from the GPU.
	// @@
	ComputeOutput *StatisticDuration `protobuf:"bytes,4,opt,name=compute_output,json=computeOutput,proto3" json:"compute_output,omitempty"`
}

func (x *InferBatchStatistics) Reset() {
	*x = InferBatchStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferBatchStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferBatchStatistics) ProtoMessage() {}

func (x *InferBatchStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferBatchStatistics.ProtoReflect.Descriptor instead.
func (*InferBatchStatistics) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{20}
}

func (x *InferBatchStatistics) GetBatchSize() uint64 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

func (x *InferBatchStatistics) GetComputeInput() *StatisticDuration {
	if x != nil {
		return x.ComputeInput
	}
	return nil
}

func (x *InferBatchStatistics) GetComputeInfer() *StatisticDuration {
	if x != nil {
		return x.ComputeInfer
	}
	return nil
}

func (x *InferBatchStatistics) GetComputeOutput() *StatisticDuration {
	if x != nil {
		return x.ComputeOutput
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelStatistics
// @@
// @@   Statistics for a specific model and version.
// @@
type ModelStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model. If not given returns statistics for all
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string version
	// @@
	// @@     The version of the model.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// @@  .. cpp:var:: uint64 last_inference
	// @@
	// @@     The timestamp of the last inference request made for this model,
	// @@     as milliseconds since the epoch.
	// @@
	LastInference uint64 `protobuf:"varint,3,opt,name=last_inference,json=lastInference,proto3" json:"last_inference,omitempty"`
	// @@  .. cpp:var:: uint64 last_inference
	// @@
	// @@     The cumulative count of successful inference requests made for this
	// @@     model. Each inference in a batched request is counted as an
	// @@     individual inference. For example, if a client sends a single
	// @@     inference request with batch size 64, "inference_count" will be
	// @@     incremented by 64. Similarly, if a clients sends 64 individual
	// @@     requests each with batch size 1, "inference_count" will be
	// @@     incremented by 64. The "inference_count" value DOES NOT include
	// @@     cache hits.
	// @@
	InferenceCount uint64 `protobuf:"varint,4,opt,name=inference_count,json=inferenceCount,proto3" json:"inference_count,omitempty"`
	// @@  .. cpp:var:: uint64 last_inference
	// @@
	// @@     The cumulative count of the number of successful inference executions
	// @@     performed for the model. When dynamic batching is enabled, a single
	// @@     model execution can perform inferencing for more than one inference
	// @@     request. For example, if a clients sends 64 individual requests each
	// @@     with batch size 1 and the dynamic batcher batches them into a single
	// @@     large batch for model execution then "execution_count" will be
	// @@     incremented by 1. If, on the other hand, the dynamic batcher is not
	// @@     enabled for that each of the 64 individual requests is executed
	// @@     independently, then "execution_count" will be incremented by 64.
	// @@     The "execution_count" value DOES NOT include cache hits.
	// @@
	ExecutionCount uint64 `protobuf:"varint,5,opt,name=execution_count,json=executionCount,proto3" json:"execution_count,omitempty"`
	// @@  .. cpp:var:: InferStatistics inference_stats
	// @@
	// @@     The aggregate statistics for the model/version.
	// @@
	InferenceStats *InferStatistics `protobuf:"bytes,6,opt,name=inference_stats,json=inferenceStats,proto3" json:"inference_stats,omitempty"`
	// @@  .. cpp:var:: InferBatchStatistics batch_stats (repeated)
	// @@
	// @@     The aggregate statistics for each different batch size that is
	// @@     executed in the model. The batch statistics indicate how many actual
	// @@     model executions were performed and show differences due to different
	// @@     batch size (for example, larger batches typically take longer to
	// @@     compute).
	// @@
	BatchStats []*InferBatchStatistics `protobuf:"bytes,7,rep,name=batch_stats,json=batchStats,proto3" json:"batch_stats,omitempty"`
}

func (x *ModelStatistics) Reset() {
	*x = ModelStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStatistics) ProtoMessage() {}

func (x *ModelStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStatistics.ProtoReflect.Descriptor instead.
func (*ModelStatistics) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{21}
}

func (x *ModelStatistics) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelStatistics) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModelStatistics) GetLastInference() uint64 {
	if x != nil {
		return x.LastInference
	}
	return 0
}

func (x *ModelStatistics) GetInferenceCount() uint64 {
	if x != nil {
		return x.InferenceCount
	}
	return 0
}

func (x *ModelStatistics) GetExecutionCount() uint64 {
	if x != nil {
		return x.ExecutionCount
	}
	return 0
}

func (x *ModelStatistics) GetInferenceStats() *InferStatistics {
	if x != nil {
		return x.InferenceStats
	}
	return nil
}

func (x *ModelStatistics) GetBatchStats() []*InferBatchStatistics {
	if x != nil {
		return x.BatchStats
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelStatisticsResponse
// @@
// @@   Response message for ModelStatistics.
// @@
type ModelStatisticsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: ModelStatistics model_stats (repeated)
	// @@
	// @@     Statistics for each requested model.
	// @@
	ModelStats []*ModelStatistics `protobuf:"bytes,1,rep,name=model_stats,json=modelStats,proto3" json:"model_stats,omitempty"`
}

func (x *ModelStatisticsResponse) Reset() {
	*x = ModelStatisticsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStatisticsResponse) ProtoMessage() {}

func (x *ModelStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStatisticsResponse.ProtoReflect.Descriptor instead.
func (*ModelStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{22}
}

func (x *ModelStatisticsResponse) GetModelStats() []*ModelStatistics {
	if x != nil {
		return x.ModelStats
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelRepositoryParameter
// @@
// @@   An model repository parameter value.
// @@
type ModelRepositoryParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: oneof parameter_choice
	// @@
	// @@     The parameter value can be a string, an int64 or
	// @@     a boolean
	// @@
	//
	// Types that are assignable to ParameterChoice:
	//
	//	*ModelRepositoryParameter_BoolParam
	//	*ModelRepositoryParameter_Int64Param
	//	*ModelRepositoryParameter_StringParam
	ParameterChoice isModelRepositoryParameter_ParameterChoice `protobuf_oneof:"parameter_choice"`
}

func (x *ModelRepositoryParameter) Reset() {
	*x = ModelRepositoryParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRepositoryParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRepositoryParameter) ProtoMessage() {}

func (x *ModelRepositoryParameter) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRepositoryParameter.ProtoReflect.Descriptor instead.
func (*ModelRepositoryParameter) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{23}
}

func (m *ModelRepositoryParameter) GetParameterChoice() isModelRepositoryParameter_ParameterChoice {
	if m != nil {
		return m.ParameterChoice
	}
	return nil
}

func (x *ModelRepositoryParameter) GetBoolParam() bool {
	if x, ok := x.GetParameterChoice().(*ModelRepositoryParameter_BoolParam); ok {
		return x.BoolParam
	}
	return false
}

func (x *ModelRepositoryParameter) GetInt64Param() int64 {
	if x, ok := x.GetParameterChoice().(*ModelRepositoryParameter_Int64Param); ok {
		return x.Int64Param
	}
	return 0
}

func (x *ModelRepositoryParameter) GetStringParam() string {
	if x, ok := x.GetParameterChoice().(*ModelRepositoryParameter_StringParam); ok {
		return x.StringParam
	}
	return ""
}

type isModelRepositoryParameter_ParameterChoice interface {
	isModelRepositoryParameter_ParameterChoice()
}

type ModelRepositoryParameter_BoolParam struct {
	// @@    .. cpp:var:: bool bool_param
	// @@
	// @@       A boolean parameter value.
	// @@
	BoolParam bool `protobuf:"varint,1,opt,name=bool_param,json=boolParam,proto3,oneof"`
}

type ModelRepositoryParameter_Int64Param struct {
	// @@    .. cpp:var:: int64 int64_param
	// @@
	// @@       An int64 parameter value.
	// @@
	Int64Param int64 `protobuf:"varint,2,opt,name=int64_param,json=int64Param,proto3,oneof"`
}

type ModelRepositoryParameter_StringParam struct {
	// @@    .. cpp:var:: string string_param
	// @@
	// @@       A string parameter value.
	// @@
	StringParam string `protobuf:"bytes,3,opt,name=string_param,json=stringParam,proto3,oneof"`
}

func (*ModelRepositoryParameter_BoolParam) isModelRepositoryParameter_ParameterChoice() {}

func (*ModelRepositoryParameter_Int64Param) isModelRepositoryParameter_ParameterChoice() {}

func (*ModelRepositoryParameter_StringParam) isModelRepositoryParameter_ParameterChoice() {}

// @@
// @@.. cpp:var:: message RepositoryIndexRequest
// @@
// @@   Request message for RepositoryIndex.
// @@
type RepositoryIndexRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string repository_name
	// @@
	// @@     The name of the repository. If empty the index is returned
	// @@     for all repositories.
	// @@
	RepositoryName string `protobuf:"bytes,1,opt,name=repository_name,json=repositoryName,proto3" json:"repository_name,omitempty"`
	// @@  .. cpp:var:: bool ready
	// @@
	// @@     If true returned only models currently ready for inferencing.
	// @@
	Ready bool `protobuf:"varint,2,opt,name=ready,proto3" json:"ready,omitempty"`
}

func (x *RepositoryIndexRequest) Reset() {
	*x = RepositoryIndexRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryIndexRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryIndexRequest) ProtoMessage() {}

func (x *RepositoryIndexRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryIndexRequest.ProtoReflect.Descriptor instead.
func (*RepositoryIndexRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{24}
}

func (x *RepositoryIndexRequest) GetRepositoryName() string {
	if x != nil {
		return x.RepositoryName
	}
	return ""
}

func (x *RepositoryIndexRequest) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

// @@
// @@.. cpp:var:: message RepositoryIndexResponse
// @@
// @@   Response message for RepositoryIndex.
// @@
type RepositoryIndexResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: ModelIndex models (repeated)
	// @@
	// @@     An index entry for each model.
	// @@
	Models []*RepositoryIndexResponse_ModelIndex `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *RepositoryIndexResponse) Reset() {
	*x = RepositoryIndexResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryIndexResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryIndexResponse) ProtoMessage() {}

func (x *RepositoryIndexResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryIndexResponse.ProtoReflect.Descriptor instead.
func (*RepositoryIndexResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{25}
}

func (x *RepositoryIndexResponse) GetModels() []*RepositoryIndexResponse_ModelIndex {
	if x != nil {
		return x.Models
	}
	return nil
}

// @@
// @@.. cpp:var:: message RepositoryModelLoadRequest
// @@
// @@   Request message for RepositoryModelLoad.
// @@
type RepositoryModelLoadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string repository_name
	// @@
	// @@     The name of the repository to load from. If empty the model
	// @@     is loaded from any repository.
	// @@
	RepositoryName string `protobuf:"bytes,1,opt,name=repository_name,json=repositoryName,proto3" json:"repository_name,omitempty"`
	// @@  .. cpp:var:: string repository_name
	// @@
	// @@     The name of the model to load, or reload.
	// @@
	ModelName string `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// @@  .. cpp:var:: map<string,ModelRepositoryParameter> parameters
	// @@
	// @@     Optional model repository request parameters.
	// @@
	Parameters map[string]*ModelRepositoryParameter `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RepositoryModelLoadRequest) Reset() {
	*x = RepositoryModelLoadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryModelLoadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryModelLoadRequest) ProtoMessage() {}

func (x *RepositoryModelLoadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryModelLoadRequest.ProtoReflect.Descriptor instead.
func (*RepositoryModelLoadRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{26}
}

func (x *RepositoryModelLoadRequest) GetRepositoryName() string {
	if x != nil {
		return x.RepositoryName
	}
	return ""
}

func (x *RepositoryModelLoadRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *RepositoryModelLoadRequest) GetParameters() map[string]*ModelRepositoryParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

// @@
// @@.. cpp:var:: message RepositoryModelLoadResponse
// @@
// @@   Response message for RepositoryModelLoad.
// @@
type RepositoryModelLoadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RepositoryModelLoadResponse) Reset() {
	*x = RepositoryModelLoadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryModelLoadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryModelLoadResponse) ProtoMessage() {}

func (x *RepositoryModelLoadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryModelLoadResponse.ProtoReflect.Descriptor instead.
func (*RepositoryModelLoadResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{27}
}

// @@
// @@.. cpp:var:: message RepositoryModelUnloadRequest
// @@
// @@   Request message for RepositoryModelUnload.
// @@
type RepositoryModelUnloadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string repository_name
	// @@
	// @@     The name of the repository from which the model was originally
	// @@     loaded. If empty the repository is not considered.
	// @@
	RepositoryName string `protobuf:"bytes,1,opt,name=repository_name,json=repositoryName,proto3" json:"repository_name,omitempty"`
	// @@  .. cpp:var:: string repository_name
	// @@
	// @@     The name of the model to unload.
	// @@
	ModelName string `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// @@  .. cpp:var:: map<string,ModelRepositoryParameter> parameters
	// @@
	// @@     Optional model repository request parameters.
	// @@
	Parameters map[string]*ModelRepositoryParameter `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RepositoryModelUnloadRequest) Reset() {
	*x = RepositoryModelUnloadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryModelUnloadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryModelUnloadRequest) ProtoMessage() {}

func (x *RepositoryModelUnloadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryModelUnloadRequest.ProtoReflect.Descriptor instead.
func (*RepositoryModelUnloadRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{28}
}

func (x *RepositoryModelUnloadRequest) GetRepositoryName() string {
	if x != nil {
		return x.RepositoryName
	}
	return ""
}

func (x *RepositoryModelUnloadRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *RepositoryModelUnloadRequest) GetParameters() map[string]*ModelRepositoryParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

// @@
// @@.. cpp:var:: message RepositoryModelUnloadResponse
// @@
// @@   Response message for RepositoryModelUnload.
// @@
type RepositoryModelUnloadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RepositoryModelUnloadResponse) Reset() {
	*x = RepositoryModelUnloadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryModelUnloadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryModelUnloadResponse) ProtoMessage() {}

func (x *RepositoryModelUnloadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryModelUnloadResponse.ProtoReflect.Descriptor instead.
func (*RepositoryModelUnloadResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{29}
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryStatusRequest
// @@
// @@   Request message for SystemSharedMemoryStatus.
// @@
type SystemSharedMemoryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the region to get status for. If empty the
	// @@     status is returned for all registered regions.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SystemSharedMemoryStatusRequest) Reset() {
	*x = SystemSharedMemoryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryStatusRequest) ProtoMessage() {}

func (x *SystemSharedMemoryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryStatusRequest.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryStatusRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{30}
}

func (x *SystemSharedMemoryStatusRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryStatusResponse
// @@
// @@   Response message for SystemSharedMemoryStatus.
// @@
type SystemSharedMemoryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: map<string,RegionStatus> regions
	// @@
	// @@     Status for each of the registered regions, indexed by
	// @@     region name.
	// @@
	Regions map[string]*SystemSharedMemoryStatusResponse_RegionStatus `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SystemSharedMemoryStatusResponse) Reset() {
	*x = SystemSharedMemoryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryStatusResponse) ProtoMessage() {}

func (x *SystemSharedMemoryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryStatusResponse.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryStatusResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{31}
}

func (x *SystemSharedMemoryStatusResponse) GetRegions() map[string]*SystemSharedMemoryStatusResponse_RegionStatus {
	if x != nil {
		return x.Regions
	}
	return nil
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryRegisterRequest
// @@
// @@   Request message for SystemSharedMemoryRegister.
// @@
type SystemSharedMemoryRegisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the region to register.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string shared_memory_key
	// @@
	// @@     The key of the underlying memory object that contains the
	// @@     shared memory region.
	// @@
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// @@  .. cpp:var:: uint64 offset
	// @@
	// @@     Offset, in bytes, within the underlying memory object to
	// @@     the start of the shared memory region.
	// @@
	Offset uint64 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	// @@  .. cpp:var:: uint64 byte_size
	// @@
	// @@     Size of the shared memory region, in bytes.
	// @@
	ByteSize uint64 `protobuf:"varint,4,opt,name=byte_size,json=byteSize,proto3" json:"byte_size,omitempty"`
}

func (x *SystemSharedMemoryRegisterRequest) Reset() {
	*x = SystemSharedMemoryRegisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryRegisterRequest) ProtoMessage() {}

func (x *SystemSharedMemoryRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryRegisterRequest.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryRegisterRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{32}
}

func (x *SystemSharedMemoryRegisterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemSharedMemoryRegisterRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SystemSharedMemoryRegisterRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SystemSharedMemoryRegisterRequest) GetByteSize() uint64 {
	if x != nil {
		return x.ByteSize
	}
	return 0
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryRegisterResponse
// @@
// @@   Response message for SystemSharedMemoryRegister.
// @@
type SystemSharedMemoryRegisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SystemSharedMemoryRegisterResponse) Reset() {
	*x = SystemSharedMemoryRegisterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryRegisterResponse) ProtoMessage() {}

func (x *SystemSharedMemoryRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryRegisterResponse.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryRegisterResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{33}
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryUnregisterRequest
// @@
// @@   Request message for SystemSharedMemoryUnregister.
// @@
type SystemSharedMemoryUnregisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the system region to unregister. If empty
	// @@     all system shared-memory regions are unregistered.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SystemSharedMemoryUnregisterRequest) Reset() {
	*x = SystemSharedMemoryUnregisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryUnregisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryUnregisterRequest) ProtoMessage() {}

func (x *SystemSharedMemoryUnregisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryUnregisterRequest.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryUnregisterRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{34}
}

func (x *SystemSharedMemoryUnregisterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// @@
// @@.. cpp:var:: message SystemSharedMemoryUnregisterResponse
// @@
// @@   Response message for SystemSharedMemoryUnregister.
// @@
type SystemSharedMemoryUnregisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SystemSharedMemoryUnregisterResponse) Reset() {
	*x = SystemSharedMemoryUnregisterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryUnregisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryUnregisterResponse) ProtoMessage() {}

func (x *SystemSharedMemoryUnregisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryUnregisterResponse.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryUnregisterResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{35}
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryStatusRequest
// @@
// @@   Request message for CudaSharedMemoryStatus.
// @@
type CudaSharedMemoryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the region to get status for. If empty the
	// @@     status is returned for all registered regions.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CudaSharedMemoryStatusRequest) Reset() {
	*x = CudaSharedMemoryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryStatusRequest) ProtoMessage() {}

func (x *CudaSharedMemoryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryStatusRequest.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryStatusRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{36}
}

func (x *CudaSharedMemoryStatusRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryStatusResponse
// @@
// @@   Response message for CudaSharedMemoryStatus.
// @@
type CudaSharedMemoryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: map<string,RegionStatus> regions
	// @@
	// @@     Status for each of the registered regions, indexed by
	// @@     region name.
	// @@
	Regions map[string]*CudaSharedMemoryStatusResponse_RegionStatus `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CudaSharedMemoryStatusResponse) Reset() {
	*x = CudaSharedMemoryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryStatusResponse) ProtoMessage() {}

func (x *CudaSharedMemoryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryStatusResponse.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryStatusResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{37}
}

func (x *CudaSharedMemoryStatusResponse) GetRegions() map[string]*CudaSharedMemoryStatusResponse_RegionStatus {
	if x != nil {
		return x.Regions
	}
	return nil
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryRegisterRequest
// @@
// @@   Request message for CudaSharedMemoryRegister.
// @@
type CudaSharedMemoryRegisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the region to register.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: bytes raw_handle
	// @@
	// @@     The raw serialized cudaIPC handle.
	// @@
	RawHandle []byte `protobuf:"bytes,2,opt,name=raw_handle,json=rawHandle,proto3" json:"raw_handle,omitempty"`
	// @@  .. cpp:var:: int64 device_id
	// @@
	// @@     The GPU device ID on which the cudaIPC handle was created.
	// @@
	DeviceId int64 `protobuf:"varint,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// @@  .. cpp:var:: uint64 byte_size
	// @@
	// @@     Size of the shared memory block, in bytes.
	// @@
	ByteSize uint64 `protobuf:"varint,4,opt,name=byte_size,json=byteSize,proto3" json:"byte_size,omitempty"`
}

func (x *CudaSharedMemoryRegisterRequest) Reset() {
	*x = CudaSharedMemoryRegisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryRegisterRequest) ProtoMessage() {}

func (x *CudaSharedMemoryRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryRegisterRequest.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryRegisterRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{38}
}

func (x *CudaSharedMemoryRegisterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CudaSharedMemoryRegisterRequest) GetRawHandle() []byte {
	if x != nil {
		return x.RawHandle
	}
	return nil
}

func (x *CudaSharedMemoryRegisterRequest) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *CudaSharedMemoryRegisterRequest) GetByteSize() uint64 {
	if x != nil {
		return x.ByteSize
	}
	return 0
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryRegisterResponse
// @@
// @@   Response message for CudaSharedMemoryRegister.
// @@
type CudaSharedMemoryRegisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CudaSharedMemoryRegisterResponse) Reset() {
	*x = CudaSharedMemoryRegisterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryRegisterResponse) ProtoMessage() {}

func (x *CudaSharedMemoryRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryRegisterResponse.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryRegisterResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{39}
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryUnregisterRequest
// @@
// @@   Request message for CudaSharedMemoryUnregister.
// @@
type CudaSharedMemoryUnregisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the cuda region to unregister. If empty
	// @@     all cuda shared-memory regions are unregistered.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CudaSharedMemoryUnregisterRequest) Reset() {
	*x = CudaSharedMemoryUnregisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryUnregisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryUnregisterRequest) ProtoMessage() {}

func (x *CudaSharedMemoryUnregisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryUnregisterRequest.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryUnregisterRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{40}
}

func (x *CudaSharedMemoryUnregisterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// @@
// @@.. cpp:var:: message CudaSharedMemoryUnregisterResponse
// @@
// @@   Response message for CudaSharedMemoryUnregister.
// @@
type CudaSharedMemoryUnregisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CudaSharedMemoryUnregisterResponse) Reset() {
	*x = CudaSharedMemoryUnregisterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryUnregisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryUnregisterResponse) ProtoMessage() {}

func (x *CudaSharedMemoryUnregisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryUnregisterResponse.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryUnregisterResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{41}
}

// @@
// @@.. cpp:var:: message TraceSettingRequest
// @@
// @@   Request message for TraceSetting.
// @@
type TraceSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: map<string,SettingValue> settings
	// @@
	// @@     The new setting values to be updated,
	// @@     settings that are not specified will remain unchanged.
	// @@
	Settings map[string]*TraceSettingRequest_SettingValue `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@
	// @@  .. cpp:var:: string model_name
	// @@
	// @@     The name of the model to apply the new trace settings.
	// @@     If not given, the new settings will be applied globally.
	// @@
	ModelName string `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *TraceSettingRequest) Reset() {
	*x = TraceSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceSettingRequest) ProtoMessage() {}

func (x *TraceSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceSettingRequest.ProtoReflect.Descriptor instead.
func (*TraceSettingRequest) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{42}
}

func (x *TraceSettingRequest) GetSettings() map[string]*TraceSettingRequest_SettingValue {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *TraceSettingRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// @@
// @@.. cpp:var:: message TraceSettingResponse
// @@
// @@   Response message for TraceSetting.
// @@
type TraceSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: map<string,SettingValue> settings
	// @@
	// @@     The current trace settings, including any changes specified
	// @@     by TraceSettingRequest.
	// @@
	Settings map[string]*TraceSettingResponse_SettingValue `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TraceSettingResponse) Reset() {
	*x = TraceSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceSettingResponse) ProtoMessage() {}

func (x *TraceSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceSettingResponse.ProtoReflect.Descriptor instead.
func (*TraceSettingResponse) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{43}
}

func (x *TraceSettingResponse) GetSettings() map[string]*TraceSettingResponse_SettingValue {
	if x != nil {
		return x.Settings
	}
	return nil
}

// @@
// @@  .. cpp:var:: message TensorMetadata
// @@
// @@     Metadata for a tensor.
// @@
type ModelMetadataResponse_TensorMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The tensor name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@
	// @@    .. cpp:var:: string datatype
	// @@
	// @@       The tensor data type.
	// @@
	Datatype string `protobuf:"bytes,2,opt,name=datatype,proto3" json:"datatype,omitempty"`
	// @@
	// @@    .. cpp:var:: int64 shape (repeated)
	// @@
	// @@       The tensor shape. A variable-size dimension is represented
	// @@       by a -1 value.
	// @@
	Shape []int64 `protobuf:"varint,3,rep,packed,name=shape,proto3" json:"shape,omitempty"`
}

func (x *ModelMetadataResponse_TensorMetadata) Reset() {
	*x = ModelMetadataResponse_TensorMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMetadataResponse_TensorMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetadataResponse_TensorMetadata) ProtoMessage() {}

func (x *ModelMetadataResponse_TensorMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetadataResponse_TensorMetadata.ProtoReflect.Descriptor instead.
func (*ModelMetadataResponse_TensorMetadata) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ModelMetadataResponse_TensorMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelMetadataResponse_TensorMetadata) GetDatatype() string {
	if x != nil {
		return x.Datatype
	}
	return ""
}

func (x *ModelMetadataResponse_TensorMetadata) GetShape() []int64 {
	if x != nil {
		return x.Shape
	}
	return nil
}

// @@
// @@  .. cpp:var:: message InferInputTensor
// @@
// @@     An input tensor for an inference request.
// @@
type ModelInferRequest_InferInputTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The tensor name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@
	// @@    .. cpp:var:: string datatype
	// @@
	// @@       The tensor data type.
	// @@
	Datatype string `protobuf:"bytes,2,opt,name=datatype,proto3" json:"datatype,omitempty"`
	// @@
	// @@    .. cpp:var:: int64 shape (repeated)
	// @@
	// @@       The tensor shape.
	// @@
	Shape []int64 `protobuf:"varint,3,rep,packed,name=shape,proto3" json:"shape,omitempty"`
	// @@    .. cpp:var:: map<string,InferParameter> parameters
	// @@
	// @@       Optional inference input tensor parameters.
	// @@
	Parameters map[string]*InferParameter `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@    .. cpp:var:: InferTensorContents contents
	// @@
	// @@       The tensor contents using a data-type format. This field
	// @@       must not be specified if tensor contents are being specified
	// @@       in ModelInferRequest.raw_input_contents.
	// @@
	Contents *InferTensorContents `protobuf:"bytes,5,opt,name=contents,proto3" json:"contents,omitempty"`
}

func (x *ModelInferRequest_InferInputTensor) Reset() {
	*x = ModelInferRequest_InferInputTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferRequest_InferInputTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferRequest_InferInputTensor) ProtoMessage() {}

func (x *ModelInferRequest_InferInputTensor) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferRequest_InferInputTensor.ProtoReflect.Descriptor instead.
func (*ModelInferRequest_InferInputTensor) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ModelInferRequest_InferInputTensor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelInferRequest_InferInputTensor) GetDatatype() string {
	if x != nil {
		return x.Datatype
	}
	return ""
}

func (x *ModelInferRequest_InferInputTensor) GetShape() []int64 {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *ModelInferRequest_InferInputTensor) GetParameters() map[string]*InferParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelInferRequest_InferInputTensor) GetContents() *InferTensorContents {
	if x != nil {
		return x.Contents
	}
	return nil
}

// @@
// @@  .. cpp:var:: message InferRequestedOutputTensor
// @@
// @@     An output tensor requested for an inference request.
// @@
type ModelInferRequest_InferRequestedOutputTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The tensor name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: map<string,InferParameter> parameters
	// @@
	// @@       Optional requested output tensor parameters.
	// @@
	Parameters map[string]*InferParameter `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelInferRequest_InferRequestedOutputTensor) Reset() {
	*x = ModelInferRequest_InferRequestedOutputTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferRequest_InferRequestedOutputTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferRequest_InferRequestedOutputTensor) ProtoMessage() {}

func (x *ModelInferRequest_InferRequestedOutputTensor) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferRequest_InferRequestedOutputTensor.ProtoReflect.Descriptor instead.
func (*ModelInferRequest_InferRequestedOutputTensor) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{12, 1}
}

func (x *ModelInferRequest_InferRequestedOutputTensor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelInferRequest_InferRequestedOutputTensor) GetParameters() map[string]*InferParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

// @@
// @@  .. cpp:var:: message InferOutputTensor
// @@
// @@     An output tensor returned for an inference request.
// @@
type ModelInferResponse_InferOutputTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The tensor name.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@
	// @@    .. cpp:var:: string datatype
	// @@
	// @@       The tensor data type.
	// @@
	Datatype string `protobuf:"bytes,2,opt,name=datatype,proto3" json:"datatype,omitempty"`
	// @@
	// @@    .. cpp:var:: int64 shape (repeated)
	// @@
	// @@       The tensor shape.
	// @@
	Shape []int64 `protobuf:"varint,3,rep,packed,name=shape,proto3" json:"shape,omitempty"`
	// @@    .. cpp:var:: map<string,InferParameter> parameters
	// @@
	// @@       Optional output tensor parameters.
	// @@
	Parameters map[string]*InferParameter `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@    .. cpp:var:: InferTensorContents contents
	// @@
	// @@       The tensor contents using a data-type format. This field
	// @@       must not be specified if tensor contents are being specified
	// @@       in ModelInferResponse.raw_output_contents.
	// @@
	Contents *InferTensorContents `protobuf:"bytes,5,opt,name=contents,proto3" json:"contents,omitempty"`
}

func (x *ModelInferResponse_InferOutputTensor) Reset() {
	*x = ModelInferResponse_InferOutputTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferResponse_InferOutputTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferResponse_InferOutputTensor) ProtoMessage() {}

func (x *ModelInferResponse_InferOutputTensor) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferResponse_InferOutputTensor.ProtoReflect.Descriptor instead.
func (*ModelInferResponse_InferOutputTensor) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ModelInferResponse_InferOutputTensor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelInferResponse_InferOutputTensor) GetDatatype() string {
	if x != nil {
		return x.Datatype
	}
	return ""
}

func (x *ModelInferResponse_InferOutputTensor) GetShape() []int64 {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *ModelInferResponse_InferOutputTensor) GetParameters() map[string]*InferParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelInferResponse_InferOutputTensor) GetContents() *InferTensorContents {
	if x != nil {
		return x.Contents
	}
	return nil
}

// @@
// @@  .. cpp:var:: message ModelIndex
// @@
// @@     Index entry for a model.
// @@
type RepositoryIndexResponse_ModelIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The name of the model.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: string version
	// @@
	// @@       The version of the model.
	// @@
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// @@
	// @@    .. cpp:var:: string state
	// @@
	// @@       The state of the model.
	// @@
	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	// @@
	// @@    .. cpp:var:: string reason
	// @@
	// @@       The reason, if any, that the model is in the given state.
	// @@
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *RepositoryIndexResponse_ModelIndex) Reset() {
	*x = RepositoryIndexResponse_ModelIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepositoryIndexResponse_ModelIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepositoryIndexResponse_ModelIndex) ProtoMessage() {}

func (x *RepositoryIndexResponse_ModelIndex) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepositoryIndexResponse_ModelIndex.ProtoReflect.Descriptor instead.
func (*RepositoryIndexResponse_ModelIndex) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{25, 0}
}

func (x *RepositoryIndexResponse_ModelIndex) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RepositoryIndexResponse_ModelIndex) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *RepositoryIndexResponse_ModelIndex) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *RepositoryIndexResponse_ModelIndex) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// @@
// @@  .. cpp:var:: message RegionStatus
// @@
// @@     Status for a shared memory region.
// @@
type SystemSharedMemoryStatusResponse_RegionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The name for the shared memory region.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: string shared_memory_key
	// @@
	// @@       The key of the underlying memory object that contains the
	// @@       shared memory region.
	// @@
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// @@    .. cpp:var:: uint64 offset
	// @@
	// @@       Offset, in bytes, within the underlying memory object to
	// @@       the start of the shared memory region.
	// @@
	Offset uint64 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	// @@    .. cpp:var:: uint64 byte_size
	// @@
	// @@       Size of the shared memory region, in bytes.
	// @@
	ByteSize uint64 `protobuf:"varint,4,opt,name=byte_size,json=byteSize,proto3" json:"byte_size,omitempty"`
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) Reset() {
	*x = SystemSharedMemoryStatusResponse_RegionStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSharedMemoryStatusResponse_RegionStatus) ProtoMessage() {}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSharedMemoryStatusResponse_RegionStatus.ProtoReflect.Descriptor instead.
func (*SystemSharedMemoryStatusResponse_RegionStatus) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{31, 0}
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SystemSharedMemoryStatusResponse_RegionStatus) GetByteSize() uint64 {
	if x != nil {
		return x.ByteSize
	}
	return 0
}

// @@
// @@  .. cpp:var:: message RegionStatus
// @@
// @@     Status for a shared memory region.
// @@
type CudaSharedMemoryStatusResponse_RegionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string name
	// @@
	// @@       The name for the shared memory region.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: uin64 device_id
	// @@
	// @@       The GPU device ID where the cudaIPC handle was created.
	// @@
	DeviceId uint64 `protobuf:"varint,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// @@    .. cpp:var:: uint64 byte_size
	// @@
	// @@       Size of the shared memory region, in bytes.
	// @@
	ByteSize uint64 `protobuf:"varint,3,opt,name=byte_size,json=byteSize,proto3" json:"byte_size,omitempty"`
}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) Reset() {
	*x = CudaSharedMemoryStatusResponse_RegionStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CudaSharedMemoryStatusResponse_RegionStatus) ProtoMessage() {}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CudaSharedMemoryStatusResponse_RegionStatus.ProtoReflect.Descriptor instead.
func (*CudaSharedMemoryStatusResponse_RegionStatus) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{37, 0}
}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) GetDeviceId() uint64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *CudaSharedMemoryStatusResponse_RegionStatus) GetByteSize() uint64 {
	if x != nil {
		return x.ByteSize
	}
	return 0
}

// @@
// @@  .. cpp:var:: message SettingValue
// @@
// @@     The values to be associated with a trace setting.
// @@     If no value is provided, the setting will be clear and
// @@     the global setting value will be used.
// @@
type TraceSettingRequest_SettingValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string value (repeated)
	// @@
	// @@       The value.
	// @@
	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *TraceSettingRequest_SettingValue) Reset() {
	*x = TraceSettingRequest_SettingValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceSettingRequest_SettingValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceSettingRequest_SettingValue) ProtoMessage() {}

func (x *TraceSettingRequest_SettingValue) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceSettingRequest_SettingValue.ProtoReflect.Descriptor instead.
func (*TraceSettingRequest_SettingValue) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{42, 0}
}

func (x *TraceSettingRequest_SettingValue) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// @@
// @@  .. cpp:var:: message SettingValue
// @@
// @@     The values to be associated with a trace setting.
// @@
type TraceSettingResponse_SettingValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@    .. cpp:var:: string value (repeated)
	// @@
	// @@       The value.
	// @@
	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *TraceSettingResponse_SettingValue) Reset() {
	*x = TraceSettingResponse_SettingValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_grpc_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceSettingResponse_SettingValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceSettingResponse_SettingValue) ProtoMessage() {}

func (x *TraceSettingResponse_SettingValue) ProtoReflect() protoreflect.Message {
	mi := &file_pb_grpc_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceSettingResponse_SettingValue.ProtoReflect.Descriptor instead.
func (*TraceSettingResponse_SettingValue) Descriptor() ([]byte, []int) {
	return file_pb_grpc_service_proto_rawDescGZIP(), []int{43, 0}
}

func (x *TraceSettingResponse_SettingValue) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_pb_grpc_service_proto protoreflect.FileDescriptor

var file_pb_grpc_service_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x62, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x1a, 0x15, 0x70, 0x62, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x28,
	0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x6c, 0x69, 0x76, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2b,
	0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x22, 0x41, 0x0a, 0x11, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2a,
	0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x66, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x44, 0x0a, 0x14, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xcf, 0x02, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x47, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x12, 0x49, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x1a, 0x56, 0x0a, 0x0e, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x73, 0x68,
	0x61, 0x70, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6f,
	0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x42,
	0x12, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x22, 0xc3, 0x02, 0x0a, 0x13, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x54, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62,
	0x6f, 0x6f, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x08, 0x52, 0x0c, 0x62, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x69,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x0c, 0x75, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x70, 0x33, 0x32,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x02, 0x52,
	0x0c, 0x66, 0x70, 0x33, 0x32, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x70, 0x36, 0x34, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x01, 0x52, 0x0c, 0x66, 0x70, 0x36, 0x34, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x9d, 0x08, 0x0a, 0x11, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x45, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x51, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x72,
	0x61, 0x77, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x10, 0x72, 0x61, 0x77, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xcd, 0x02, 0x0a, 0x10, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x68, 0x61, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a,
	0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xf3, 0x01, 0x0a, 0x1a, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x67, 0x0a, 0x0a,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdf, 0x05, 0x0a, 0x12, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x11, 0x72, 0x61, 0x77,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xd0,
	0x02, 0x0a, 0x11, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x85, 0x01, 0x0a, 0x18,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a,
	0x0e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x42, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e,
	0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x46,
	0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x6e,
	0x73, 0x22, 0xf2, 0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a,
	0x04, 0x66, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x66, 0x61, 0x69, 0x6c, 0x12,
	0x32, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x39,
	0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x68, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x63, 0x61, 0x63, 0x68, 0x65, 0x48, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x22, 0x80, 0x02, 0x0a, 0x14, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x41,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x22, 0xbf, 0x02, 0x0a, 0x0f, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x0e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52,
	0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x56, 0x0a, 0x17, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x18, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x12, 0x1f, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x42, 0x12, 0x0a, 0x10, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x22, 0x57, 0x0a,
	0x16, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x22, 0xca, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x45, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x68, 0x0a, 0x0a, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x22, 0x9f, 0x02, 0x0a, 0x1a, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f,
	0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x0a, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x1a, 0x62, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1d, 0x0a, 0x1b, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa3, 0x02, 0x0a, 0x1c, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x62, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1f, 0x0a, 0x1d, 0x52, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x35, 0x0a, 0x1f, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xd7, 0x02, 0x0a, 0x20, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x69, 0x0a, 0x0c, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x79,
	0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x74, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7e, 0x0a, 0x21,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x24, 0x0a, 0x22,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x39, 0x0a, 0x23, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x26, 0x0a,
	0x24, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x0a, 0x1d, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc4, 0x02, 0x0a, 0x1e, 0x43,
	0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a,
	0x07, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0x5c, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x72, 0x0a,
	0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x4c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x8e, 0x01, 0x0a, 0x1f, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61, 0x77,
	0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x72,
	0x61, 0x77, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x22, 0x0a, 0x20, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0x0a, 0x21, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x24, 0x0a, 0x22, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a,
	0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x24, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x68, 0x0a, 0x0d,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf2, 0x01, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x49, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54, 0x72,
	0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x24, 0x0a, 0x0c, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x1a, 0x69, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xe7, 0x0e, 0x0a, 0x14,
	0x47, 0x52, 0x50, 0x43, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x76, 0x65, 0x12, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4e, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79,
	0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4b, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12,
	0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57,
	0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x20, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a,
	0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x10, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x12, 0x1c,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x69,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x4e, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x21, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x66, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x25, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x15, 0x52, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x27, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x18, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2a, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x1a,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x1c, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a,
	0x16, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x29, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75,
	0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75,
	0x0a, 0x18, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x1a, 0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x43, 0x75, 0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x75,
	0x64, 0x61, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x6e,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x51, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x36, 0x5a, 0x34, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61,
	0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x2d, 0x61, 0x69,
	0x2f, 0x61, 0x69, 0x6f, 0x74, 0x2f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2d, 0x73, 0x74, 0x64,
	0x2f, 0x74, 0x72, 0x69, 0x74, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_grpc_service_proto_rawDescOnce sync.Once
	file_pb_grpc_service_proto_rawDescData = file_pb_grpc_service_proto_rawDesc
)

func file_pb_grpc_service_proto_rawDescGZIP() []byte {
	file_pb_grpc_service_proto_rawDescOnce.Do(func() {
		file_pb_grpc_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_grpc_service_proto_rawDescData)
	})
	return file_pb_grpc_service_proto_rawDescData
}

var file_pb_grpc_service_proto_msgTypes = make([]protoimpl.MessageInfo, 64)
var file_pb_grpc_service_proto_goTypes = []interface{}{
	(*ServerLiveRequest)(nil),                            // 0: inference.ServerLiveRequest
	(*ServerLiveResponse)(nil),                           // 1: inference.ServerLiveResponse
	(*ServerReadyRequest)(nil),                           // 2: inference.ServerReadyRequest
	(*ServerReadyResponse)(nil),                          // 3: inference.ServerReadyResponse
	(*ModelReadyRequest)(nil),                            // 4: inference.ModelReadyRequest
	(*ModelReadyResponse)(nil),                           // 5: inference.ModelReadyResponse
	(*ServerMetadataRequest)(nil),                        // 6: inference.ServerMetadataRequest
	(*ServerMetadataResponse)(nil),                       // 7: inference.ServerMetadataResponse
	(*ModelMetadataRequest)(nil),                         // 8: inference.ModelMetadataRequest
	(*ModelMetadataResponse)(nil),                        // 9: inference.ModelMetadataResponse
	(*InferParameter)(nil),                               // 10: inference.InferParameter
	(*InferTensorContents)(nil),                          // 11: inference.InferTensorContents
	(*ModelInferRequest)(nil),                            // 12: inference.ModelInferRequest
	(*ModelInferResponse)(nil),                           // 13: inference.ModelInferResponse
	(*ModelStreamInferResponse)(nil),                     // 14: inference.ModelStreamInferResponse
	(*ModelConfigRequest)(nil),                           // 15: inference.ModelConfigRequest
	(*ModelConfigResponse)(nil),                          // 16: inference.ModelConfigResponse
	(*ModelStatisticsRequest)(nil),                       // 17: inference.ModelStatisticsRequest
	(*StatisticDuration)(nil),                            // 18: inference.StatisticDuration
	(*InferStatistics)(nil),                              // 19: inference.InferStatistics
	(*InferBatchStatistics)(nil),                         // 20: inference.InferBatchStatistics
	(*ModelStatistics)(nil),                              // 21: inference.ModelStatistics
	(*ModelStatisticsResponse)(nil),                      // 22: inference.ModelStatisticsResponse
	(*ModelRepositoryParameter)(nil),                     // 23: inference.ModelRepositoryParameter
	(*RepositoryIndexRequest)(nil),                       // 24: inference.RepositoryIndexRequest
	(*RepositoryIndexResponse)(nil),                      // 25: inference.RepositoryIndexResponse
	(*RepositoryModelLoadRequest)(nil),                   // 26: inference.RepositoryModelLoadRequest
	(*RepositoryModelLoadResponse)(nil),                  // 27: inference.RepositoryModelLoadResponse
	(*RepositoryModelUnloadRequest)(nil),                 // 28: inference.RepositoryModelUnloadRequest
	(*RepositoryModelUnloadResponse)(nil),                // 29: inference.RepositoryModelUnloadResponse
	(*SystemSharedMemoryStatusRequest)(nil),              // 30: inference.SystemSharedMemoryStatusRequest
	(*SystemSharedMemoryStatusResponse)(nil),             // 31: inference.SystemSharedMemoryStatusResponse
	(*SystemSharedMemoryRegisterRequest)(nil),            // 32: inference.SystemSharedMemoryRegisterRequest
	(*SystemSharedMemoryRegisterResponse)(nil),           // 33: inference.SystemSharedMemoryRegisterResponse
	(*SystemSharedMemoryUnregisterRequest)(nil),          // 34: inference.SystemSharedMemoryUnregisterRequest
	(*SystemSharedMemoryUnregisterResponse)(nil),         // 35: inference.SystemSharedMemoryUnregisterResponse
	(*CudaSharedMemoryStatusRequest)(nil),                // 36: inference.CudaSharedMemoryStatusRequest
	(*CudaSharedMemoryStatusResponse)(nil),               // 37: inference.CudaSharedMemoryStatusResponse
	(*CudaSharedMemoryRegisterRequest)(nil),              // 38: inference.CudaSharedMemoryRegisterRequest
	(*CudaSharedMemoryRegisterResponse)(nil),             // 39: inference.CudaSharedMemoryRegisterResponse
	(*CudaSharedMemoryUnregisterRequest)(nil),            // 40: inference.CudaSharedMemoryUnregisterRequest
	(*CudaSharedMemoryUnregisterResponse)(nil),           // 41: inference.CudaSharedMemoryUnregisterResponse
	(*TraceSettingRequest)(nil),                          // 42: inference.TraceSettingRequest
	(*TraceSettingResponse)(nil),                         // 43: inference.TraceSettingResponse
	(*ModelMetadataResponse_TensorMetadata)(nil),         // 44: inference.ModelMetadataResponse.TensorMetadata
	(*ModelInferRequest_InferInputTensor)(nil),           // 45: inference.ModelInferRequest.InferInputTensor
	(*ModelInferRequest_InferRequestedOutputTensor)(nil), // 46: inference.ModelInferRequest.InferRequestedOutputTensor
	nil, // 47: inference.ModelInferRequest.ParametersEntry
	nil, // 48: inference.ModelInferRequest.InferInputTensor.ParametersEntry
	nil, // 49: inference.ModelInferRequest.InferRequestedOutputTensor.ParametersEntry
	(*ModelInferResponse_InferOutputTensor)(nil), // 50: inference.ModelInferResponse.InferOutputTensor
	nil, // 51: inference.ModelInferResponse.ParametersEntry
	nil, // 52: inference.ModelInferResponse.InferOutputTensor.ParametersEntry
	(*RepositoryIndexResponse_ModelIndex)(nil), // 53: inference.RepositoryIndexResponse.ModelIndex
	nil, // 54: inference.RepositoryModelLoadRequest.ParametersEntry
	nil, // 55: inference.RepositoryModelUnloadRequest.ParametersEntry
	(*SystemSharedMemoryStatusResponse_RegionStatus)(nil), // 56: inference.SystemSharedMemoryStatusResponse.RegionStatus
	nil, // 57: inference.SystemSharedMemoryStatusResponse.RegionsEntry
	(*CudaSharedMemoryStatusResponse_RegionStatus)(nil), // 58: inference.CudaSharedMemoryStatusResponse.RegionStatus
	nil,                                      // 59: inference.CudaSharedMemoryStatusResponse.RegionsEntry
	(*TraceSettingRequest_SettingValue)(nil), // 60: inference.TraceSettingRequest.SettingValue
	nil,                                      // 61: inference.TraceSettingRequest.SettingsEntry
	(*TraceSettingResponse_SettingValue)(nil), // 62: inference.TraceSettingResponse.SettingValue
	nil,                 // 63: inference.TraceSettingResponse.SettingsEntry
	(*ModelConfig)(nil), // 64: inference.ModelConfig
}
var file_pb_grpc_service_proto_depIdxs = []int32{
	44, // 0: inference.ModelMetadataResponse.inputs:type_name -> inference.ModelMetadataResponse.TensorMetadata
	44, // 1: inference.ModelMetadataResponse.outputs:type_name -> inference.ModelMetadataResponse.TensorMetadata
	47, // 2: inference.ModelInferRequest.parameters:type_name -> inference.ModelInferRequest.ParametersEntry
	45, // 3: inference.ModelInferRequest.inputs:type_name -> inference.ModelInferRequest.InferInputTensor
	46, // 4: inference.ModelInferRequest.outputs:type_name -> inference.ModelInferRequest.InferRequestedOutputTensor
	51, // 5: inference.ModelInferResponse.parameters:type_name -> inference.ModelInferResponse.ParametersEntry
	50, // 6: inference.ModelInferResponse.outputs:type_name -> inference.ModelInferResponse.InferOutputTensor
	13, // 7: inference.ModelStreamInferResponse.infer_response:type_name -> inference.ModelInferResponse
	64, // 8: inference.ModelConfigResponse.config:type_name -> inference.ModelConfig
	18, // 9: inference.InferStatistics.success:type_name -> inference.StatisticDuration
	18, // 10: inference.InferStatistics.fail:type_name -> inference.StatisticDuration
	18, // 11: inference.InferStatistics.queue:type_name -> inference.StatisticDuration
	18, // 12: inference.InferStatistics.compute_input:type_name -> inference.StatisticDuration
	18, // 13: inference.InferStatistics.compute_infer:type_name -> inference.StatisticDuration
	18, // 14: inference.InferStatistics.compute_output:type_name -> inference.StatisticDuration
	18, // 15: inference.InferStatistics.cache_hit:type_name -> inference.StatisticDuration
	18, // 16: inference.InferStatistics.cache_miss:type_name -> inference.StatisticDuration
	18, // 17: inference.InferBatchStatistics.compute_input:type_name -> inference.StatisticDuration
	18, // 18: inference.InferBatchStatistics.compute_infer:type_name -> inference.StatisticDuration
	18, // 19: inference.InferBatchStatistics.compute_output:type_name -> inference.StatisticDuration
	19, // 20: inference.ModelStatistics.inference_stats:type_name -> inference.InferStatistics
	20, // 21: inference.ModelStatistics.batch_stats:type_name -> inference.InferBatchStatistics
	21, // 22: inference.ModelStatisticsResponse.model_stats:type_name -> inference.ModelStatistics
	53, // 23: inference.RepositoryIndexResponse.models:type_name -> inference.RepositoryIndexResponse.ModelIndex
	54, // 24: inference.RepositoryModelLoadRequest.parameters:type_name -> inference.RepositoryModelLoadRequest.ParametersEntry
	55, // 25: inference.RepositoryModelUnloadRequest.parameters:type_name -> inference.RepositoryModelUnloadRequest.ParametersEntry
	57, // 26: inference.SystemSharedMemoryStatusResponse.regions:type_name -> inference.SystemSharedMemoryStatusResponse.RegionsEntry
	59, // 27: inference.CudaSharedMemoryStatusResponse.regions:type_name -> inference.CudaSharedMemoryStatusResponse.RegionsEntry
	61, // 28: inference.TraceSettingRequest.settings:type_name -> inference.TraceSettingRequest.SettingsEntry
	63, // 29: inference.TraceSettingResponse.settings:type_name -> inference.TraceSettingResponse.SettingsEntry
	48, // 30: inference.ModelInferRequest.InferInputTensor.parameters:type_name -> inference.ModelInferRequest.InferInputTensor.ParametersEntry
	11, // 31: inference.ModelInferRequest.InferInputTensor.contents:type_name -> inference.InferTensorContents
	49, // 32: inference.ModelInferRequest.InferRequestedOutputTensor.parameters:type_name -> inference.ModelInferRequest.InferRequestedOutputTensor.ParametersEntry
	10, // 33: inference.ModelInferRequest.ParametersEntry.value:type_name -> inference.InferParameter
	10, // 34: inference.ModelInferRequest.InferInputTensor.ParametersEntry.value:type_name -> inference.InferParameter
	10, // 35: inference.ModelInferRequest.InferRequestedOutputTensor.ParametersEntry.value:type_name -> inference.InferParameter
	52, // 36: inference.ModelInferResponse.InferOutputTensor.parameters:type_name -> inference.ModelInferResponse.InferOutputTensor.ParametersEntry
	11, // 37: inference.ModelInferResponse.InferOutputTensor.contents:type_name -> inference.InferTensorContents
	10, // 38: inference.ModelInferResponse.ParametersEntry.value:type_name -> inference.InferParameter
	10, // 39: inference.ModelInferResponse.InferOutputTensor.ParametersEntry.value:type_name -> inference.InferParameter
	23, // 40: inference.RepositoryModelLoadRequest.ParametersEntry.value:type_name -> inference.ModelRepositoryParameter
	23, // 41: inference.RepositoryModelUnloadRequest.ParametersEntry.value:type_name -> inference.ModelRepositoryParameter
	56, // 42: inference.SystemSharedMemoryStatusResponse.RegionsEntry.value:type_name -> inference.SystemSharedMemoryStatusResponse.RegionStatus
	58, // 43: inference.CudaSharedMemoryStatusResponse.RegionsEntry.value:type_name -> inference.CudaSharedMemoryStatusResponse.RegionStatus
	60, // 44: inference.TraceSettingRequest.SettingsEntry.value:type_name -> inference.TraceSettingRequest.SettingValue
	62, // 45: inference.TraceSettingResponse.SettingsEntry.value:type_name -> inference.TraceSettingResponse.SettingValue
	0,  // 46: inference.GRPCInferenceService.ServerLive:input_type -> inference.ServerLiveRequest
	2,  // 47: inference.GRPCInferenceService.ServerReady:input_type -> inference.ServerReadyRequest
	4,  // 48: inference.GRPCInferenceService.ModelReady:input_type -> inference.ModelReadyRequest
	6,  // 49: inference.GRPCInferenceService.ServerMetadata:input_type -> inference.ServerMetadataRequest
	8,  // 50: inference.GRPCInferenceService.ModelMetadata:input_type -> inference.ModelMetadataRequest
	12, // 51: inference.GRPCInferenceService.ModelInfer:input_type -> inference.ModelInferRequest
	12, // 52: inference.GRPCInferenceService.ModelStreamInfer:input_type -> inference.ModelInferRequest
	15, // 53: inference.GRPCInferenceService.ModelConfig:input_type -> inference.ModelConfigRequest
	17, // 54: inference.GRPCInferenceService.ModelStatistics:input_type -> inference.ModelStatisticsRequest
	24, // 55: inference.GRPCInferenceService.RepositoryIndex:input_type -> inference.RepositoryIndexRequest
	26, // 56: inference.GRPCInferenceService.RepositoryModelLoad:input_type -> inference.RepositoryModelLoadRequest
	28, // 57: inference.GRPCInferenceService.RepositoryModelUnload:input_type -> inference.RepositoryModelUnloadRequest
	30, // 58: inference.GRPCInferenceService.SystemSharedMemoryStatus:input_type -> inference.SystemSharedMemoryStatusRequest
	32, // 59: inference.GRPCInferenceService.SystemSharedMemoryRegister:input_type -> inference.SystemSharedMemoryRegisterRequest
	34, // 60: inference.GRPCInferenceService.SystemSharedMemoryUnregister:input_type -> inference.SystemSharedMemoryUnregisterRequest
	36, // 61: inference.GRPCInferenceService.CudaSharedMemoryStatus:input_type -> inference.CudaSharedMemoryStatusRequest
	38, // 62: inference.GRPCInferenceService.CudaSharedMemoryRegister:input_type -> inference.CudaSharedMemoryRegisterRequest
	40, // 63: inference.GRPCInferenceService.CudaSharedMemoryUnregister:input_type -> inference.CudaSharedMemoryUnregisterRequest
	42, // 64: inference.GRPCInferenceService.TraceSetting:input_type -> inference.TraceSettingRequest
	1,  // 65: inference.GRPCInferenceService.ServerLive:output_type -> inference.ServerLiveResponse
	3,  // 66: inference.GRPCInferenceService.ServerReady:output_type -> inference.ServerReadyResponse
	5,  // 67: inference.GRPCInferenceService.ModelReady:output_type -> inference.ModelReadyResponse
	7,  // 68: inference.GRPCInferenceService.ServerMetadata:output_type -> inference.ServerMetadataResponse
	9,  // 69: inference.GRPCInferenceService.ModelMetadata:output_type -> inference.ModelMetadataResponse
	13, // 70: inference.GRPCInferenceService.ModelInfer:output_type -> inference.ModelInferResponse
	14, // 71: inference.GRPCInferenceService.ModelStreamInfer:output_type -> inference.ModelStreamInferResponse
	16, // 72: inference.GRPCInferenceService.ModelConfig:output_type -> inference.ModelConfigResponse
	22, // 73: inference.GRPCInferenceService.ModelStatistics:output_type -> inference.ModelStatisticsResponse
	25, // 74: inference.GRPCInferenceService.RepositoryIndex:output_type -> inference.RepositoryIndexResponse
	27, // 75: inference.GRPCInferenceService.RepositoryModelLoad:output_type -> inference.RepositoryModelLoadResponse
	29, // 76: inference.GRPCInferenceService.RepositoryModelUnload:output_type -> inference.RepositoryModelUnloadResponse
	31, // 77: inference.GRPCInferenceService.SystemSharedMemoryStatus:output_type -> inference.SystemSharedMemoryStatusResponse
	33, // 78: inference.GRPCInferenceService.SystemSharedMemoryRegister:output_type -> inference.SystemSharedMemoryRegisterResponse
	35, // 79: inference.GRPCInferenceService.SystemSharedMemoryUnregister:output_type -> inference.SystemSharedMemoryUnregisterResponse
	37, // 80: inference.GRPCInferenceService.CudaSharedMemoryStatus:output_type -> inference.CudaSharedMemoryStatusResponse
	39, // 81: inference.GRPCInferenceService.CudaSharedMemoryRegister:output_type -> inference.CudaSharedMemoryRegisterResponse
	41, // 82: inference.GRPCInferenceService.CudaSharedMemoryUnregister:output_type -> inference.CudaSharedMemoryUnregisterResponse
	43, // 83: inference.GRPCInferenceService.TraceSetting:output_type -> inference.TraceSettingResponse
	65, // [65:84] is the sub-list for method output_type
	46, // [46:65] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_pb_grpc_service_proto_init() }
func file_pb_grpc_service_proto_init() {
	if File_pb_grpc_service_proto != nil {
		return
	}
	file_pb_model_config_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_grpc_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerLiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerLiveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerReadyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerReadyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReadyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReadyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferTensorContents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStreamInferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticDuration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferBatchStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStatisticsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRepositoryParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryIndexRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryIndexResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryModelLoadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryModelLoadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryModelUnloadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryModelUnloadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryRegisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryRegisterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryUnregisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryUnregisterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryRegisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryRegisterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryUnregisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryUnregisterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMetadataResponse_TensorMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferRequest_InferInputTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferRequest_InferRequestedOutputTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferResponse_InferOutputTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepositoryIndexResponse_ModelIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemSharedMemoryStatusResponse_RegionStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CudaSharedMemoryStatusResponse_RegionStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceSettingRequest_SettingValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_grpc_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceSettingResponse_SettingValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pb_grpc_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*InferParameter_BoolParam)(nil),
		(*InferParameter_Int64Param)(nil),
		(*InferParameter_StringParam)(nil),
	}
	file_pb_grpc_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*ModelRepositoryParameter_BoolParam)(nil),
		(*ModelRepositoryParameter_Int64Param)(nil),
		(*ModelRepositoryParameter_StringParam)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_grpc_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   64,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_grpc_service_proto_goTypes,
		DependencyIndexes: file_pb_grpc_service_proto_depIdxs,
		MessageInfos:      file_pb_grpc_service_proto_msgTypes,
	}.Build()
	File_pb_grpc_service_proto = out.File
	file_pb_grpc_service_proto_rawDesc = nil
	file_pb_grpc_service_proto_goTypes = nil
	file_pb_grpc_service_proto_depIdxs = nil
}
