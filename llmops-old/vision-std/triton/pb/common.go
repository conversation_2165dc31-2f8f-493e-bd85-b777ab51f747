package pb

import (
	"io"
	"io/ioutil"
	"os"

	"github.com/golang/protobuf/proto"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func NewByteTensor(bs ...[]byte) *InferTensorContents {
	return &InferTensorContents{
		BytesContents: bs,
	}
}

// SetOutputs 将推理结果中的RawOutputContents设置到对应的OutputTensor.Contents 中,以便于后续处理。
// 具体原因可参考：// https://github.com/triton-inference-server/server/issues/2582
func SetOutputs(rsp *ModelInferResponse) {
	if rsp == nil {
		return
	}
	if rsp.RawOutputContents == nil || len(rsp.RawOutputContents) != len(rsp.Outputs) {
		stdlog.Errorf("mismatch size between RawOutputContents and Outputs in ModelInferResponse")
		return
	}
	for i, o := range rsp.Outputs {
		if o.Contents != nil {
			// this contents has already been set, skip it
			continue
		}
		raw := rsp.RawOutputContents[i]
		if o.Datatype == "BYTES" && len(raw) >= 4 {
			// 参考官方给出的 python client 代码实现
			// https://github.com/triton-inference-server/client/blob/main/src/python/library/tritonclient/grpc/__init__.py
			//  if datatype == 'BYTES':
			//      # String results contain a 4-byte string length
			//      # followed by the actual string characters. Hence,
			//      # need to decode the raw bytes to convert into
			//      # array elements.
			//      np_array = deserialize_bytes_tensor(
			//          self._result.raw_output_contents[index])
			raw = raw[4:]
		}
		o.Contents = NewByteTensor(raw)
	}
	return
}

func ReadModelConfigFile(path string) (*ModelConfig, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to read model config from '%s'", path)
	}
	defer f.Close()
	return ReadModelConfig(f)
}

func ReadModelConfig(reader io.Reader) (*ModelConfig, error) {
	bs, err := ioutil.ReadAll(reader)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to read data from reader")
	}
	mc := new(ModelConfig)
	if err := proto.UnmarshalText(string(bs), mc); err != nil {
		return nil, stderr.Internal.Cause(err, "failed to unmarshal following text to ModelConfig: %s", string(bs))
	}
	return mc, nil
}
