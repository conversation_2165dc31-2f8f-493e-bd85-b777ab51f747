// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: pb/dlie.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InferReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id 标识发起推理请求的客户端
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// params 为本次推理中所有的待推理数据
	Params []*InferReq_Param `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"`
	// context 为推理过程中可能用到的上下文信息
	Context *InferReq_Context `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
	// extra 为调用时Client端自行传输的额外配置
	Extra map[string]string `protobuf:"bytes,4,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *InferReq) Reset() {
	*x = InferReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferReq) ProtoMessage() {}

func (x *InferReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferReq.ProtoReflect.Descriptor instead.
func (*InferReq) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{0}
}

func (x *InferReq) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *InferReq) GetParams() []*InferReq_Param {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *InferReq) GetContext() *InferReq_Context {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *InferReq) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

type ExternalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InferParams  map[string]string          `protobuf:"bytes,1,rep,name=infer_params,json=inferParams,proto3" json:"infer_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeviceLabels map[string]string          `protobuf:"bytes,2,rep,name=device_labels,json=deviceLabels,proto3" json:"device_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Roi          []*ExternalConfig_Position `protobuf:"bytes,3,rep,name=roi,proto3" json:"roi,omitempty"`
}

func (x *ExternalConfig) Reset() {
	*x = ExternalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalConfig) ProtoMessage() {}

func (x *ExternalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalConfig.ProtoReflect.Descriptor instead.
func (*ExternalConfig) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{1}
}

func (x *ExternalConfig) GetInferParams() map[string]string {
	if x != nil {
		return x.InferParams
	}
	return nil
}

func (x *ExternalConfig) GetDeviceLabels() map[string]string {
	if x != nil {
		return x.DeviceLabels
	}
	return nil
}

func (x *ExternalConfig) GetRoi() []*ExternalConfig_Position {
	if x != nil {
		return x.Roi
	}
	return nil
}

// FIXME
// protobuf 部分语法不支持，因此该结构和模型推理规范中的结构不完全一致
// e.g. position 应为 [][]int 结构
type InferRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id 标识发起推理请求的客户端,与请求中的client_id对应
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// results 为本次推理请求中所有推理数据的结果
	Results []*InferRsp_Result `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	// model_info 模型信息，版本/时间等
	ModelInfo map[string]string `protobuf:"bytes,3,rep,name=model_info,json=modelInfo,proto3" json:"model_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *InferRsp) Reset() {
	*x = InferRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp) ProtoMessage() {}

func (x *InferRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp.ProtoReflect.Descriptor instead.
func (*InferRsp) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2}
}

func (x *InferRsp) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *InferRsp) GetResults() []*InferRsp_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *InferRsp) GetModelInfo() map[string]string {
	if x != nil {
		return x.ModelInfo
	}
	return nil
}

// Param 为推理请求中一个具体的待推理数据（一张图片）相关的描述信息
type InferReq_Param struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 为标识推理数据的ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// type 为标识该条推理数据的数据格式，e.g. raw, jpg/jpeg, png
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// data 为该条推理数据的payload（发送给Triton Ensemble Model时该字段必须为空）
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// size 为该条推理数据的payload的具体大小(字节数)
	Size uint64 `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	// external_config 推理某条数据时，可能需要的一些额外的配置信息
	ExternalConfig *ExternalConfig `protobuf:"bytes,5,opt,name=external_config,json=externalConfig,proto3" json:"external_config,omitempty"`
}

func (x *InferReq_Param) Reset() {
	*x = InferReq_Param{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferReq_Param) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferReq_Param) ProtoMessage() {}

func (x *InferReq_Param) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferReq_Param.ProtoReflect.Descriptor instead.
func (*InferReq_Param) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{0, 0}
}

func (x *InferReq_Param) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InferReq_Param) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InferReq_Param) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *InferReq_Param) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *InferReq_Param) GetExternalConfig() *ExternalConfig {
	if x != nil {
		return x.ExternalConfig
	}
	return nil
}

// Context 为本次推理过程中可能会使用到的上下文信息
type InferReq_Context struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// instance_labels 为发出该推理请求的SophonEdge应用实例包含的标签信息
	InstanceLabels map[string]string `protobuf:"bytes,1,rep,name=instance_labels,json=instanceLabels,proto3" json:"instance_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pipeline_labels 为发出该推理请求的SophonEdge应用规则包含的标签信息
	PipelineLabels map[string]string `protobuf:"bytes,2,rep,name=pipeline_labels,json=pipelineLabels,proto3" json:"pipeline_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *InferReq_Context) Reset() {
	*x = InferReq_Context{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferReq_Context) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferReq_Context) ProtoMessage() {}

func (x *InferReq_Context) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferReq_Context.ProtoReflect.Descriptor instead.
func (*InferReq_Context) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{0, 1}
}

func (x *InferReq_Context) GetInstanceLabels() map[string]string {
	if x != nil {
		return x.InstanceLabels
	}
	return nil
}

func (x *InferReq_Context) GetPipelineLabels() map[string]string {
	if x != nil {
		return x.PipelineLabels
	}
	return nil
}

type ExternalConfig_Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position []*ExternalConfig_Position_Point `protobuf:"bytes,1,rep,name=position,proto3" json:"position,omitempty"`
}

func (x *ExternalConfig_Position) Reset() {
	*x = ExternalConfig_Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalConfig_Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalConfig_Position) ProtoMessage() {}

func (x *ExternalConfig_Position) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalConfig_Position.ProtoReflect.Descriptor instead.
func (*ExternalConfig_Position) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ExternalConfig_Position) GetPosition() []*ExternalConfig_Position_Point {
	if x != nil {
		return x.Position
	}
	return nil
}

type ExternalConfig_Position_Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
	Y uint32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *ExternalConfig_Position_Point) Reset() {
	*x = ExternalConfig_Position_Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalConfig_Position_Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalConfig_Position_Point) ProtoMessage() {}

func (x *ExternalConfig_Position_Point) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalConfig_Position_Point.ProtoReflect.Descriptor instead.
func (*ExternalConfig_Position_Point) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *ExternalConfig_Position_Point) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *ExternalConfig_Position_Point) GetY() uint32 {
	if x != nil {
		return x.Y
	}
	return 0
}

// Result 为返回内容的结构体
type InferRsp_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 标识对应的请求中的 InferReq.Param.id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// objects 为对应推理数据的推理结果，可能包含多个检测目标
	Objects []*InferRsp_Result_Object `protobuf:"bytes,2,rep,name=objects,proto3" json:"objects,omitempty"`
}

func (x *InferRsp_Result) Reset() {
	*x = InferRsp_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp_Result) ProtoMessage() {}

func (x *InferRsp_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp_Result.ProtoReflect.Descriptor instead.
func (*InferRsp_Result) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2, 0}
}

func (x *InferRsp_Result) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InferRsp_Result) GetObjects() []*InferRsp_Result_Object {
	if x != nil {
		return x.Objects
	}
	return nil
}

// Object 表示单个检测目标
type InferRsp_Result_Object struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// position_type 目标的位置类型（point  , cnpose18 , alphapose17 , openpose18 , mask不指定时默认为 point ，渲染时将点连成线）
	PositionType string `protobuf:"bytes,1,opt,name=position_type,json=positionType,proto3" json:"position_type,omitempty"`
	// position 目标框位置
	Position []*InferRsp_Result_Object_Point `protobuf:"bytes,2,rep,name=position,proto3" json:"position,omitempty"`
	// attributes 目标的属性列表
	Attributes []*InferRsp_Result_Object_Attr `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty"`
	// objects 目标中包含的子目标列表
	Objects []*InferRsp_Result_Object `protobuf:"bytes,4,rep,name=objects,proto3" json:"objects,omitempty"`
	// 单通道png图片的base64编码，每个点的像素对应标签的id，如果是实例分割就是0,1
	Mask string `protobuf:"bytes,5,opt,name=mask,proto3" json:"mask,omitempty"`
	// 标签与id的dict; 例如 "mask_label": {"cat":1, "dog":2 ...}
	MaskLabel string `protobuf:"bytes,6,opt,name=mask_label,json=maskLabel,proto3" json:"mask_label,omitempty"`
}

func (x *InferRsp_Result_Object) Reset() {
	*x = InferRsp_Result_Object{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp_Result_Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp_Result_Object) ProtoMessage() {}

func (x *InferRsp_Result_Object) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp_Result_Object.ProtoReflect.Descriptor instead.
func (*InferRsp_Result_Object) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *InferRsp_Result_Object) GetPositionType() string {
	if x != nil {
		return x.PositionType
	}
	return ""
}

func (x *InferRsp_Result_Object) GetPosition() []*InferRsp_Result_Object_Point {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *InferRsp_Result_Object) GetAttributes() []*InferRsp_Result_Object_Attr {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *InferRsp_Result_Object) GetObjects() []*InferRsp_Result_Object {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *InferRsp_Result_Object) GetMask() string {
	if x != nil {
		return x.Mask
	}
	return ""
}

func (x *InferRsp_Result_Object) GetMaskLabel() string {
	if x != nil {
		return x.MaskLabel
	}
	return ""
}

// Attr 为目标的属性
type InferRsp_Result_Object_Attr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Desc  string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *InferRsp_Result_Object_Attr) Reset() {
	*x = InferRsp_Result_Object_Attr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp_Result_Object_Attr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp_Result_Object_Attr) ProtoMessage() {}

func (x *InferRsp_Result_Object_Attr) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp_Result_Object_Attr.ProtoReflect.Descriptor instead.
func (*InferRsp_Result_Object_Attr) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2, 0, 0, 0}
}

func (x *InferRsp_Result_Object_Attr) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *InferRsp_Result_Object_Attr) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *InferRsp_Result_Object_Attr) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// Point 为目标位置的一个点位坐标
type InferRsp_Result_Object_Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
	Y uint32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *InferRsp_Result_Object_Point) Reset() {
	*x = InferRsp_Result_Object_Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp_Result_Object_Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp_Result_Object_Point) ProtoMessage() {}

func (x *InferRsp_Result_Object_Point) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp_Result_Object_Point.ProtoReflect.Descriptor instead.
func (*InferRsp_Result_Object_Point) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2, 0, 0, 1}
}

func (x *InferRsp_Result_Object_Point) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *InferRsp_Result_Object_Point) GetY() uint32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type InferRsp_Result_Object_Stats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型内部的置信度指标
	Score []float32 `protobuf:"fixed32,1,rep,packed,name=score,proto3" json:"score,omitempty"`
	// 模型推理延迟(非必须）
	CostMs uint32 `protobuf:"varint,2,opt,name=cost_ms,json=costMs,proto3" json:"cost_ms,omitempty"`
}

func (x *InferRsp_Result_Object_Stats) Reset() {
	*x = InferRsp_Result_Object_Stats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_dlie_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InferRsp_Result_Object_Stats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InferRsp_Result_Object_Stats) ProtoMessage() {}

func (x *InferRsp_Result_Object_Stats) ProtoReflect() protoreflect.Message {
	mi := &file_pb_dlie_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InferRsp_Result_Object_Stats.ProtoReflect.Descriptor instead.
func (*InferRsp_Result_Object_Stats) Descriptor() ([]byte, []int) {
	return file_pb_dlie_proto_rawDescGZIP(), []int{2, 0, 0, 2}
}

func (x *InferRsp_Result_Object_Stats) GetScore() []float32 {
	if x != nil {
		return x.Score
	}
	return nil
}

func (x *InferRsp_Result_Object_Stats) GetCostMs() uint32 {
	if x != nil {
		return x.CostMs
	}
	return 0
}

var File_pb_dlie_proto protoreflect.FileDescriptor

var file_pb_dlie_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x70, 0x62, 0x2f, 0x64, 0x6c, 0x69, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x09, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xe1, 0x05, 0x0a, 0x08, 0x49,
	0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x34,
	0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x1a, 0x97, 0x01, 0x0a, 0x05, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xc3,
	0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x58, 0x0a, 0x0f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x58, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x41,
	0x0a, 0x13, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x41, 0x0a, 0x13, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdf,
	0x03, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x50, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x34, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x1a, 0x75, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x23, 0x0a, 0x05, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01,
	0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x79, 0x1a,
	0x3e, 0x0a, 0x10, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x3f, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x84, 0x06, 0x0a, 0x08, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x12, 0x41, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0xa3, 0x04, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b,
	0x0a, 0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x1a, 0xcb, 0x03, 0x0a, 0x06,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52,
	0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x46, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73,
	0x6b, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x73, 0x6b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x1a, 0x42, 0x0a, 0x04, 0x41, 0x74, 0x74, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x1a, 0x23, 0x0a, 0x05,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01,
	0x79, 0x1a, 0x36, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x63, 0x6f, 0x73, 0x74, 0x4d, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x36, 0x5a, 0x34, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x2d,
	0x61, 0x69, 0x2f, 0x61, 0x69, 0x6f, 0x74, 0x2f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2d, 0x73,
	0x74, 0x64, 0x2f, 0x74, 0x72, 0x69, 0x74, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_dlie_proto_rawDescOnce sync.Once
	file_pb_dlie_proto_rawDescData = file_pb_dlie_proto_rawDesc
)

func file_pb_dlie_proto_rawDescGZIP() []byte {
	file_pb_dlie_proto_rawDescOnce.Do(func() {
		file_pb_dlie_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_dlie_proto_rawDescData)
	})
	return file_pb_dlie_proto_rawDescData
}

var file_pb_dlie_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_pb_dlie_proto_goTypes = []interface{}{
	(*InferReq)(nil),                      // 0: inference.InferReq
	(*ExternalConfig)(nil),                // 1: inference.ExternalConfig
	(*InferRsp)(nil),                      // 2: inference.InferRsp
	(*InferReq_Param)(nil),                // 3: inference.InferReq.Param
	(*InferReq_Context)(nil),              // 4: inference.InferReq.Context
	nil,                                   // 5: inference.InferReq.ExtraEntry
	nil,                                   // 6: inference.InferReq.Context.InstanceLabelsEntry
	nil,                                   // 7: inference.InferReq.Context.PipelineLabelsEntry
	(*ExternalConfig_Position)(nil),       // 8: inference.ExternalConfig.Position
	nil,                                   // 9: inference.ExternalConfig.InferParamsEntry
	nil,                                   // 10: inference.ExternalConfig.DeviceLabelsEntry
	(*ExternalConfig_Position_Point)(nil), // 11: inference.ExternalConfig.Position.Point
	(*InferRsp_Result)(nil),               // 12: inference.InferRsp.Result
	nil,                                   // 13: inference.InferRsp.ModelInfoEntry
	(*InferRsp_Result_Object)(nil),        // 14: inference.InferRsp.Result.Object
	(*InferRsp_Result_Object_Attr)(nil),   // 15: inference.InferRsp.Result.Object.Attr
	(*InferRsp_Result_Object_Point)(nil),  // 16: inference.InferRsp.Result.Object.Point
	(*InferRsp_Result_Object_Stats)(nil),  // 17: inference.InferRsp.Result.Object.Stats
}
var file_pb_dlie_proto_depIdxs = []int32{
	3,  // 0: inference.InferReq.params:type_name -> inference.InferReq.Param
	4,  // 1: inference.InferReq.context:type_name -> inference.InferReq.Context
	5,  // 2: inference.InferReq.extra:type_name -> inference.InferReq.ExtraEntry
	9,  // 3: inference.ExternalConfig.infer_params:type_name -> inference.ExternalConfig.InferParamsEntry
	10, // 4: inference.ExternalConfig.device_labels:type_name -> inference.ExternalConfig.DeviceLabelsEntry
	8,  // 5: inference.ExternalConfig.roi:type_name -> inference.ExternalConfig.Position
	12, // 6: inference.InferRsp.results:type_name -> inference.InferRsp.Result
	13, // 7: inference.InferRsp.model_info:type_name -> inference.InferRsp.ModelInfoEntry
	1,  // 8: inference.InferReq.Param.external_config:type_name -> inference.ExternalConfig
	6,  // 9: inference.InferReq.Context.instance_labels:type_name -> inference.InferReq.Context.InstanceLabelsEntry
	7,  // 10: inference.InferReq.Context.pipeline_labels:type_name -> inference.InferReq.Context.PipelineLabelsEntry
	11, // 11: inference.ExternalConfig.Position.position:type_name -> inference.ExternalConfig.Position.Point
	14, // 12: inference.InferRsp.Result.objects:type_name -> inference.InferRsp.Result.Object
	16, // 13: inference.InferRsp.Result.Object.position:type_name -> inference.InferRsp.Result.Object.Point
	15, // 14: inference.InferRsp.Result.Object.attributes:type_name -> inference.InferRsp.Result.Object.Attr
	14, // 15: inference.InferRsp.Result.Object.objects:type_name -> inference.InferRsp.Result.Object
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_pb_dlie_proto_init() }
func file_pb_dlie_proto_init() {
	if File_pb_dlie_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_dlie_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferReq_Param); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferReq_Context); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalConfig_Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalConfig_Position_Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp_Result_Object); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp_Result_Object_Attr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp_Result_Object_Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_dlie_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InferRsp_Result_Object_Stats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_dlie_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_dlie_proto_goTypes,
		DependencyIndexes: file_pb_dlie_proto_depIdxs,
		MessageInfos:      file_pb_dlie_proto_msgTypes,
	}.Build()
	File_pb_dlie_proto = out.File
	file_pb_dlie_proto_rawDesc = nil
	file_pb_dlie_proto_goTypes = nil
	file_pb_dlie_proto_depIdxs = nil
}
