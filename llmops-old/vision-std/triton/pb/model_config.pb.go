// Copyright (c) 2018-2020, NVIDIA CORPORATION. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
//  * Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//  * Neither the name of NVIDIA CORPORATION nor the names of its
//    contributors may be used to endorse or promote products derived
//    from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS ``AS IS'' AND ANY
// EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
// CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
// EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
// PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
// PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
// OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// Copyright (c) 2018, TensorFlow Authors. All rights reserved.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: pb/model_config.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// @@
// @@.. cpp:enum:: DataType
// @@
// @@   Data types supported for input and output tensors.
// @@
type DataType int32

const (
	// @@  .. cpp:enumerator:: DataType::INVALID = 0
	DataType_TYPE_INVALID DataType = 0
	// @@  .. cpp:enumerator:: DataType::BOOL = 1
	DataType_TYPE_BOOL DataType = 1
	// @@  .. cpp:enumerator:: DataType::UINT8 = 2
	DataType_TYPE_UINT8 DataType = 2
	// @@  .. cpp:enumerator:: DataType::UINT16 = 3
	DataType_TYPE_UINT16 DataType = 3
	// @@  .. cpp:enumerator:: DataType::UINT32 = 4
	DataType_TYPE_UINT32 DataType = 4
	// @@  .. cpp:enumerator:: DataType::UINT64 = 5
	DataType_TYPE_UINT64 DataType = 5
	// @@  .. cpp:enumerator:: DataType::INT8 = 6
	DataType_TYPE_INT8 DataType = 6
	// @@  .. cpp:enumerator:: DataType::INT16 = 7
	DataType_TYPE_INT16 DataType = 7
	// @@  .. cpp:enumerator:: DataType::INT32 = 8
	DataType_TYPE_INT32 DataType = 8
	// @@  .. cpp:enumerator:: DataType::INT64 = 9
	DataType_TYPE_INT64 DataType = 9
	// @@  .. cpp:enumerator:: DataType::FP16 = 10
	DataType_TYPE_FP16 DataType = 10
	// @@  .. cpp:enumerator:: DataType::FP32 = 11
	DataType_TYPE_FP32 DataType = 11
	// @@  .. cpp:enumerator:: DataType::FP64 = 12
	DataType_TYPE_FP64 DataType = 12
	// @@  .. cpp:enumerator:: DataType::STRING = 13
	DataType_TYPE_STRING DataType = 13
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0:  "TYPE_INVALID",
		1:  "TYPE_BOOL",
		2:  "TYPE_UINT8",
		3:  "TYPE_UINT16",
		4:  "TYPE_UINT32",
		5:  "TYPE_UINT64",
		6:  "TYPE_INT8",
		7:  "TYPE_INT16",
		8:  "TYPE_INT32",
		9:  "TYPE_INT64",
		10: "TYPE_FP16",
		11: "TYPE_FP32",
		12: "TYPE_FP64",
		13: "TYPE_STRING",
	}
	DataType_value = map[string]int32{
		"TYPE_INVALID": 0,
		"TYPE_BOOL":    1,
		"TYPE_UINT8":   2,
		"TYPE_UINT16":  3,
		"TYPE_UINT32":  4,
		"TYPE_UINT64":  5,
		"TYPE_INT8":    6,
		"TYPE_INT16":   7,
		"TYPE_INT32":   8,
		"TYPE_INT64":   9,
		"TYPE_FP16":    10,
		"TYPE_FP32":    11,
		"TYPE_FP64":    12,
		"TYPE_STRING":  13,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{0}
}

// @@
// @@  .. cpp:enum:: Kind
// @@
// @@     Kind of this instance group.
// @@
type ModelInstanceGroup_Kind int32

const (
	// @@    .. cpp:enumerator:: Kind::KIND_AUTO = 0
	// @@
	// @@       This instance group represents instances that can run on either
	// @@       CPU or GPU. If all GPUs listed in 'gpus' are available then
	// @@       instances will be created on GPU(s), otherwise instances will
	// @@       be created on CPU.
	// @@
	ModelInstanceGroup_KIND_AUTO ModelInstanceGroup_Kind = 0
	// @@    .. cpp:enumerator:: Kind::KIND_GPU = 1
	// @@
	// @@       This instance group represents instances that must run on the
	// @@       GPU.
	// @@
	ModelInstanceGroup_KIND_GPU ModelInstanceGroup_Kind = 1
	// @@    .. cpp:enumerator:: Kind::KIND_CPU = 2
	// @@
	// @@       This instance group represents instances that must run on the
	// @@       CPU.
	// @@
	ModelInstanceGroup_KIND_CPU ModelInstanceGroup_Kind = 2
	// @@    .. cpp:enumerator:: Kind::KIND_MODEL = 3
	// @@
	// @@       This instance group represents instances that should run on the
	// @@       CPU and/or GPU(s) as specified by the model or backend itself.
	// @@       The inference server will not override the model/backend
	// @@       settings.
	// @@
	ModelInstanceGroup_KIND_MODEL ModelInstanceGroup_Kind = 3
)

// Enum value maps for ModelInstanceGroup_Kind.
var (
	ModelInstanceGroup_Kind_name = map[int32]string{
		0: "KIND_AUTO",
		1: "KIND_GPU",
		2: "KIND_CPU",
		3: "KIND_MODEL",
	}
	ModelInstanceGroup_Kind_value = map[string]int32{
		"KIND_AUTO":  0,
		"KIND_GPU":   1,
		"KIND_CPU":   2,
		"KIND_MODEL": 3,
	}
)

func (x ModelInstanceGroup_Kind) Enum() *ModelInstanceGroup_Kind {
	p := new(ModelInstanceGroup_Kind)
	*p = x
	return p
}

func (x ModelInstanceGroup_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelInstanceGroup_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[1].Descriptor()
}

func (ModelInstanceGroup_Kind) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[1]
}

func (x ModelInstanceGroup_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelInstanceGroup_Kind.Descriptor instead.
func (ModelInstanceGroup_Kind) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{1, 0}
}

// @@
// @@  .. cpp:enum:: SecondaryDeviceKind
// @@
// @@     The kind of the secondary device.
// @@
type ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind int32

const (
	// @@    .. cpp:enumerator:: SecondaryDeviceKind::KIND_NVDLA = 0
	// @@
	// @@       An NVDLA core. http://nvdla.org
	// @@       Currently KIND_NVDLA is only supported by the TensorRT backend.
	// @@
	ModelInstanceGroup_SecondaryDevice_KIND_NVDLA ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind = 0
)

// Enum value maps for ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind.
var (
	ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind_name = map[int32]string{
		0: "KIND_NVDLA",
	}
	ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind_value = map[string]int32{
		"KIND_NVDLA": 0,
	}
)

func (x ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) Enum() *ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind {
	p := new(ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind)
	*p = x
	return p
}

func (x ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[2].Descriptor()
}

func (ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[2]
}

func (x ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind.Descriptor instead.
func (ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{1, 0, 0}
}

// @@
// @@  .. cpp:enum:: Format
// @@
// @@     The format for the input.
// @@
type ModelInput_Format int32

const (
	// @@    .. cpp:enumerator:: Format::FORMAT_NONE = 0
	// @@
	// @@       The input has no specific format. This is the default.
	// @@
	ModelInput_FORMAT_NONE ModelInput_Format = 0
	// @@    .. cpp:enumerator:: Format::FORMAT_NHWC = 1
	// @@
	// @@       HWC image format. Tensors with this format require 3 dimensions
	// @@       if the model does not support batching (max_batch_size = 0) or 4
	// @@       dimensions if the model does support batching (max_batch_size
	// @@       >= 1). In either case the 'dims' below should only specify the
	// @@       3 non-batch dimensions (i.e. HWC or CHW).
	// @@
	ModelInput_FORMAT_NHWC ModelInput_Format = 1
	// @@    .. cpp:enumerator:: Format::FORMAT_NCHW = 2
	// @@
	// @@       CHW image format. Tensors with this format require 3 dimensions
	// @@       if the model does not support batching (max_batch_size = 0) or 4
	// @@       dimensions if the model does support batching (max_batch_size
	// @@       >= 1). In either case the 'dims' below should only specify the
	// @@       3 non-batch dimensions (i.e. HWC or CHW).
	// @@
	ModelInput_FORMAT_NCHW ModelInput_Format = 2
)

// Enum value maps for ModelInput_Format.
var (
	ModelInput_Format_name = map[int32]string{
		0: "FORMAT_NONE",
		1: "FORMAT_NHWC",
		2: "FORMAT_NCHW",
	}
	ModelInput_Format_value = map[string]int32{
		"FORMAT_NONE": 0,
		"FORMAT_NHWC": 1,
		"FORMAT_NCHW": 2,
	}
)

func (x ModelInput_Format) Enum() *ModelInput_Format {
	p := new(ModelInput_Format)
	*p = x
	return p
}

func (x ModelInput_Format) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelInput_Format) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[3].Descriptor()
}

func (ModelInput_Format) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[3]
}

func (x ModelInput_Format) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelInput_Format.Descriptor instead.
func (ModelInput_Format) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{3, 0}
}

// @@
// @@    .. cpp:enum:: Kind
// @@
// @@       The kind of the batch input.
// @@
type BatchInput_Kind int32

const (
	// @@      .. cpp:enumerator:: Kind::BATCH_ELEMENT_COUNT = 0
	// @@
	// @@         The element count of the 'source_input' will be added as
	// @@         input with shape [1].
	// @@
	BatchInput_BATCH_ELEMENT_COUNT BatchInput_Kind = 0
	// @@      .. cpp:enumerator:: Kind::BATCH_ACCUMULATED_ELEMENT_COUNT = 1
	// @@
	// @@         The accumulated element count of the 'source_input' will be
	// @@         added as input with shape [1]. For example, if there is a
	// @@         batch of two request, each with 2 elements, an input of value
	// @@         2 will be added to the first request, and an input of value
	// @@         4 will be added to the second request.
	// @@
	BatchInput_BATCH_ACCUMULATED_ELEMENT_COUNT BatchInput_Kind = 1
	// @@      .. cpp:enumerator::
	// @@         Kind::BATCH_ACCUMULATED_ELEMENT_COUNT_WITH_ZERO = 2
	// @@
	// @@         The accumulated element count of the 'source_input' will be
	// @@         added as input with shape [1], except for the first request
	// @@         in the batch. For the first request in the batch, the input
	// @@         will have shape [2] where the first element is value 0.
	// @@
	BatchInput_BATCH_ACCUMULATED_ELEMENT_COUNT_WITH_ZERO BatchInput_Kind = 2
	// @@      .. cpp:enumerator:: Kind::BATCH_MAX_ELEMENT_COUNT_AS_SHAPE = 3
	// @@
	// @@         Among the requests in the batch, the max element count of the
	// @@         'source_input' will be added as input with shape
	// @@         [max_element_count] for the first request in the batch.
	// @@         For other requests, such input will be with shape [0].
	// @@         The data of the tensor will be uninitialized.
	// @@
	BatchInput_BATCH_MAX_ELEMENT_COUNT_AS_SHAPE BatchInput_Kind = 3
	// @@      .. cpp:enumerator:: Kind::BATCH_ITEM_SHAPE = 4
	// @@
	// @@         Among the requests in the batch, the shape of the
	// @@         'source_input' will be added as input with shape
	// @@         [batch_size, len(input_dim)]. For example, if one
	// @@         batch-2 input with shape [3, 1] and batch-1 input
	// @@         with shape [2, 2] are batched, the batch input will
	// @@         have shape [3, 2] and value [ [3, 1], [3, 1], [2, 2]].
	// @@
	BatchInput_BATCH_ITEM_SHAPE BatchInput_Kind = 4
	// @@      .. cpp:enumerator:: Kind::BATCH_ITEM_SHAPE_FLATTEN = 5
	// @@
	// @@         Among the requests in the batch, the shape of the
	// @@         'source_input' will be added as input with single dimensional
	// @@         shape [batch_size * len(input_dim)]. For example, if one
	// @@         batch-2 input with shape [3, 1] and batch-1 input
	// @@         with shape [2, 2] are batched, the batch input will
	// @@         have shape [6] and value [3, 1, 3, 1, 2, 2].
	// @@
	BatchInput_BATCH_ITEM_SHAPE_FLATTEN BatchInput_Kind = 5
)

// Enum value maps for BatchInput_Kind.
var (
	BatchInput_Kind_name = map[int32]string{
		0: "BATCH_ELEMENT_COUNT",
		1: "BATCH_ACCUMULATED_ELEMENT_COUNT",
		2: "BATCH_ACCUMULATED_ELEMENT_COUNT_WITH_ZERO",
		3: "BATCH_MAX_ELEMENT_COUNT_AS_SHAPE",
		4: "BATCH_ITEM_SHAPE",
		5: "BATCH_ITEM_SHAPE_FLATTEN",
	}
	BatchInput_Kind_value = map[string]int32{
		"BATCH_ELEMENT_COUNT":                       0,
		"BATCH_ACCUMULATED_ELEMENT_COUNT":           1,
		"BATCH_ACCUMULATED_ELEMENT_COUNT_WITH_ZERO": 2,
		"BATCH_MAX_ELEMENT_COUNT_AS_SHAPE":          3,
		"BATCH_ITEM_SHAPE":                          4,
		"BATCH_ITEM_SHAPE_FLATTEN":                  5,
	}
)

func (x BatchInput_Kind) Enum() *BatchInput_Kind {
	p := new(BatchInput_Kind)
	*p = x
	return p
}

func (x BatchInput_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchInput_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[4].Descriptor()
}

func (BatchInput_Kind) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[4]
}

func (x BatchInput_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchInput_Kind.Descriptor instead.
func (BatchInput_Kind) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{5, 0}
}

// @@
// @@  .. cpp:enum:: Kind
// @@
// @@     The kind of the batch output.
// @@
type BatchOutput_Kind int32

const (
	// @@    .. cpp:enumerator:: Kind::BATCH_SCATTER_WITH_INPUT_SHAPE = 0
	// @@
	// @@       The output should be scattered according to the shape of
	// @@       'source_input'. The dynamic dimension of the output will
	// @@       be set to the value of the same dimension in the input.
	// @@
	BatchOutput_BATCH_SCATTER_WITH_INPUT_SHAPE BatchOutput_Kind = 0
)

// Enum value maps for BatchOutput_Kind.
var (
	BatchOutput_Kind_name = map[int32]string{
		0: "BATCH_SCATTER_WITH_INPUT_SHAPE",
	}
	BatchOutput_Kind_value = map[string]int32{
		"BATCH_SCATTER_WITH_INPUT_SHAPE": 0,
	}
)

func (x BatchOutput_Kind) Enum() *BatchOutput_Kind {
	p := new(BatchOutput_Kind)
	*p = x
	return p
}

func (x BatchOutput_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchOutput_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[5].Descriptor()
}

func (BatchOutput_Kind) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[5]
}

func (x BatchOutput_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchOutput_Kind.Descriptor instead.
func (BatchOutput_Kind) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{6, 0}
}

// @@
// @@  .. cpp:enum:: ModelPriority
// @@
// @@     Model priorities. A model will be given scheduling and execution
// @@     preference over models at lower priorities. Current model
// @@     priorities only work for TensorRT models.
// @@
type ModelOptimizationPolicy_ModelPriority int32

const (
	// @@    .. cpp:enumerator:: ModelPriority::PRIORITY_DEFAULT = 0
	// @@
	// @@       The default model priority.
	// @@
	ModelOptimizationPolicy_PRIORITY_DEFAULT ModelOptimizationPolicy_ModelPriority = 0
	// @@    .. cpp:enumerator:: ModelPriority::PRIORITY_MAX = 1
	// @@
	// @@       The maximum model priority.
	// @@
	ModelOptimizationPolicy_PRIORITY_MAX ModelOptimizationPolicy_ModelPriority = 1
	// @@    .. cpp:enumerator:: ModelPriority::PRIORITY_MIN = 2
	// @@
	// @@       The minimum model priority.
	// @@
	ModelOptimizationPolicy_PRIORITY_MIN ModelOptimizationPolicy_ModelPriority = 2
)

// Enum value maps for ModelOptimizationPolicy_ModelPriority.
var (
	ModelOptimizationPolicy_ModelPriority_name = map[int32]string{
		0: "PRIORITY_DEFAULT",
		1: "PRIORITY_MAX",
		2: "PRIORITY_MIN",
	}
	ModelOptimizationPolicy_ModelPriority_value = map[string]int32{
		"PRIORITY_DEFAULT": 0,
		"PRIORITY_MAX":     1,
		"PRIORITY_MIN":     2,
	}
)

func (x ModelOptimizationPolicy_ModelPriority) Enum() *ModelOptimizationPolicy_ModelPriority {
	p := new(ModelOptimizationPolicy_ModelPriority)
	*p = x
	return p
}

func (x ModelOptimizationPolicy_ModelPriority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelOptimizationPolicy_ModelPriority) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[6].Descriptor()
}

func (ModelOptimizationPolicy_ModelPriority) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[6]
}

func (x ModelOptimizationPolicy_ModelPriority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelOptimizationPolicy_ModelPriority.Descriptor instead.
func (ModelOptimizationPolicy_ModelPriority) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 0}
}

// @@
// @@  .. cpp:enum:: TimeoutAction
// @@
// @@     The action applied to timed-out requests.
// @@
type ModelQueuePolicy_TimeoutAction int32

const (
	// @@    .. cpp:enumerator:: Action::REJECT = 0
	// @@
	// @@       Reject the request and return error message accordingly.
	// @@
	ModelQueuePolicy_REJECT ModelQueuePolicy_TimeoutAction = 0
	// @@    .. cpp:enumerator:: Action::DELAY = 1
	// @@
	// @@       Delay the request until all other requests at the same
	// @@       (or higher) priority levels that have not reached their timeouts
	// @@       are processed. A delayed request will eventually be processed,
	// @@       but may be delayed indefinitely due to newly arriving requests.
	// @@
	ModelQueuePolicy_DELAY ModelQueuePolicy_TimeoutAction = 1
)

// Enum value maps for ModelQueuePolicy_TimeoutAction.
var (
	ModelQueuePolicy_TimeoutAction_name = map[int32]string{
		0: "REJECT",
		1: "DELAY",
	}
	ModelQueuePolicy_TimeoutAction_value = map[string]int32{
		"REJECT": 0,
		"DELAY":  1,
	}
)

func (x ModelQueuePolicy_TimeoutAction) Enum() *ModelQueuePolicy_TimeoutAction {
	p := new(ModelQueuePolicy_TimeoutAction)
	*p = x
	return p
}

func (x ModelQueuePolicy_TimeoutAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelQueuePolicy_TimeoutAction) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[7].Descriptor()
}

func (ModelQueuePolicy_TimeoutAction) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[7]
}

func (x ModelQueuePolicy_TimeoutAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelQueuePolicy_TimeoutAction.Descriptor instead.
func (ModelQueuePolicy_TimeoutAction) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{9, 0}
}

// @@
// @@    .. cpp:enum:: Kind
// @@
// @@       The kind of the control.
// @@
type ModelSequenceBatching_Control_Kind int32

const (
	// @@      .. cpp:enumerator:: Kind::CONTROL_SEQUENCE_START = 0
	// @@
	// @@         A new sequence is/is-not starting. If true a sequence is
	// @@         starting, if false a sequence is continuing. Must
	// @@         specify either int32_false_true, fp32_false_true or
	// @@         bool_false_true for this control. This control is optional.
	// @@
	ModelSequenceBatching_Control_CONTROL_SEQUENCE_START ModelSequenceBatching_Control_Kind = 0
	// @@      .. cpp:enumerator:: Kind::CONTROL_SEQUENCE_READY = 1
	// @@
	// @@         A sequence is/is-not ready for inference. If true the
	// @@         input tensor data is valid and should be used. If false
	// @@         the input tensor data is invalid and inferencing should
	// @@         be "skipped". Must specify either int32_false_true,
	// @@         fp32_false_true or bool_false_true for this control. This
	// @@         control is optional.
	// @@
	ModelSequenceBatching_Control_CONTROL_SEQUENCE_READY ModelSequenceBatching_Control_Kind = 1
	// @@      .. cpp:enumerator:: Kind::CONTROL_SEQUENCE_END = 2
	// @@
	// @@         A sequence is/is-not ending. If true a sequence is
	// @@         ending, if false a sequence is continuing. Must specify
	// @@         either int32_false_true, fp32_false_true or bool_false_true
	// @@         for this control. This control is optional.
	// @@
	ModelSequenceBatching_Control_CONTROL_SEQUENCE_END ModelSequenceBatching_Control_Kind = 2
	// @@      .. cpp:enumerator:: Kind::CONTROL_SEQUENCE_CORRID = 3
	// @@
	// @@         The correlation ID of the sequence. The correlation ID
	// @@         is an uint64_t value that is communicated in whole or
	// @@         in part by the tensor. The tensor's datatype must be
	// @@         specified by data_type and must be TYPE_UINT64, TYPE_INT64,
	// @@         TYPE_UINT32 or TYPE_INT32. If a 32-bit datatype is specified
	// @@         the correlation ID will be truncated to the low-order 32
	// @@         bits. This control is optional.
	// @@
	ModelSequenceBatching_Control_CONTROL_SEQUENCE_CORRID ModelSequenceBatching_Control_Kind = 3
)

// Enum value maps for ModelSequenceBatching_Control_Kind.
var (
	ModelSequenceBatching_Control_Kind_name = map[int32]string{
		0: "CONTROL_SEQUENCE_START",
		1: "CONTROL_SEQUENCE_READY",
		2: "CONTROL_SEQUENCE_END",
		3: "CONTROL_SEQUENCE_CORRID",
	}
	ModelSequenceBatching_Control_Kind_value = map[string]int32{
		"CONTROL_SEQUENCE_START":  0,
		"CONTROL_SEQUENCE_READY":  1,
		"CONTROL_SEQUENCE_END":    2,
		"CONTROL_SEQUENCE_CORRID": 3,
	}
)

func (x ModelSequenceBatching_Control_Kind) Enum() *ModelSequenceBatching_Control_Kind {
	p := new(ModelSequenceBatching_Control_Kind)
	*p = x
	return p
}

func (x ModelSequenceBatching_Control_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelSequenceBatching_Control_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_model_config_proto_enumTypes[8].Descriptor()
}

func (ModelSequenceBatching_Control_Kind) Type() protoreflect.EnumType {
	return &file_pb_model_config_proto_enumTypes[8]
}

func (x ModelSequenceBatching_Control_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelSequenceBatching_Control_Kind.Descriptor instead.
func (ModelSequenceBatching_Control_Kind) EnumDescriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 0, 0}
}

// @@
// @@  .. cpp:var:: message ModelRateLimiter
// @@
// @@     The specifications required by the rate limiter to properly
// @@     schedule the inference requests across the different models
// @@     and their instances.
// @@
type ModelRateLimiter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: Resource resources (repeated)
	// @@
	// @@     The resources required to execute the request on a model instance.
	// @@     Resources are just names with a corresponding count. The execution
	// @@     of the instance will be blocked until the specificied resources are
	// @@     available. By default an instance uses no rate-limiter resources.
	// @@
	Resources []*ModelRateLimiter_Resource `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	// @@  .. cpp:var:: uint32 priority
	// @@
	// @@     The optional weighting value to be used for prioritizing across
	// @@     instances. An instance with priority 2 will be given 1/2 the
	// @@     number of scheduling chances as an instance_group with priority
	// @@     1. The default priority is 1. The priority of value 0 will be
	// @@     treated as priority 1.
	// @@
	Priority uint32 `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *ModelRateLimiter) Reset() {
	*x = ModelRateLimiter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRateLimiter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRateLimiter) ProtoMessage() {}

func (x *ModelRateLimiter) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRateLimiter.ProtoReflect.Descriptor instead.
func (*ModelRateLimiter) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{0}
}

func (x *ModelRateLimiter) GetResources() []*ModelRateLimiter_Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *ModelRateLimiter) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

// @@
// @@.. cpp:var:: message ModelInstanceGroup
// @@
// @@   A group of one or more instances of a model and resources made
// @@   available for those instances.
// @@
type ModelInstanceGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     Optional name of this group of instances. If not specified the
	// @@     name will be formed as <model name>_<group number>. The name of
	// @@     individual instances will be further formed by a unique instance
	// @@     number and GPU index:
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: Kind kind
	// @@
	// @@     The kind of this instance group. Default is KIND_AUTO. If
	// @@     KIND_AUTO or KIND_GPU then both 'count' and 'gpu' are valid and
	// @@     may be specified. If KIND_CPU or KIND_MODEL only 'count' is valid
	// @@     and 'gpu' cannot be specified.
	// @@
	Kind ModelInstanceGroup_Kind `protobuf:"varint,4,opt,name=kind,proto3,enum=inference.ModelInstanceGroup_Kind" json:"kind,omitempty"`
	// @@  .. cpp:var:: int32 count
	// @@
	// @@     For a group assigned to GPU, the number of instances created for
	// @@     each GPU listed in 'gpus'. For a group assigned to CPU the number
	// @@     of instances created. Default is 1.
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	// @@  .. cpp:var:: ModelRateLimiter rate_limiter
	// @@
	// @@     The rate limiter specific settings to be associated with this
	// @@     instance group. Optional, if not specified no rate limiting
	// @@     will be applied to this instance group.
	// @@
	RateLimiter *ModelRateLimiter `protobuf:"bytes,6,opt,name=rate_limiter,json=rateLimiter,proto3" json:"rate_limiter,omitempty"`
	// @@  .. cpp:var:: int32 gpus (repeated)
	// @@
	// @@     GPU(s) where instances should be available. For each GPU listed,
	// @@     'count' instances of the model will be available. Setting 'gpus'
	// @@     to empty (or not specifying at all) is eqivalent to listing all
	// @@     available GPUs.
	// @@
	Gpus []int32 `protobuf:"varint,3,rep,packed,name=gpus,proto3" json:"gpus,omitempty"`
	// @@  .. cpp:var:: SecondaryDevice secondary_devices (repeated)
	// @@
	// @@     Secondary devices that are required by instances specified by this
	// @@     instance group. Optional.
	// @@
	SecondaryDevices []*ModelInstanceGroup_SecondaryDevice `protobuf:"bytes,8,rep,name=secondary_devices,json=secondaryDevices,proto3" json:"secondary_devices,omitempty"`
	// @@  .. cpp:var:: string profile (repeated)
	// @@
	// @@     For TensorRT models containing multiple optimization profile, this
	// @@     parameter specifies a set of optimization profiles available to this
	// @@     instance group. The inference server will choose the optimal profile
	// @@     based on the shapes of the input tensors. This field should lie
	// @@     between 0 and <TotalNumberOfOptimizationProfilesInPlanModel> - 1
	// @@     and be specified only for TensorRT backend, otherwise an error will
	// @@     be generated. If not specified, the server will select the first
	// @@     optimization profile by default.
	// @@
	Profile []string `protobuf:"bytes,5,rep,name=profile,proto3" json:"profile,omitempty"`
	// @@  .. cpp:var:: bool passive
	// @@
	// @@     Whether the instances within this instance group will be accepting
	// @@     inference requests from the scheduler. If true, the instances will
	// @@     not be added to the scheduler. Default value is false.
	// @@
	Passive bool `protobuf:"varint,7,opt,name=passive,proto3" json:"passive,omitempty"`
	// @@  .. cpp:var:: string host_policy
	// @@
	// @@     The host policy name that the instance to be associated with.
	// @@     The default value is set to reflect the device kind of the instance,
	// @@     for instance, KIND_CPU is "cpu", KIND_MODEL is "model" and
	// @@     KIND_GPU is "gpu_<gpu_id>".
	// @@
	HostPolicy string `protobuf:"bytes,9,opt,name=host_policy,json=hostPolicy,proto3" json:"host_policy,omitempty"`
}

func (x *ModelInstanceGroup) Reset() {
	*x = ModelInstanceGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInstanceGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInstanceGroup) ProtoMessage() {}

func (x *ModelInstanceGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInstanceGroup.ProtoReflect.Descriptor instead.
func (*ModelInstanceGroup) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{1}
}

func (x *ModelInstanceGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelInstanceGroup) GetKind() ModelInstanceGroup_Kind {
	if x != nil {
		return x.Kind
	}
	return ModelInstanceGroup_KIND_AUTO
}

func (x *ModelInstanceGroup) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ModelInstanceGroup) GetRateLimiter() *ModelRateLimiter {
	if x != nil {
		return x.RateLimiter
	}
	return nil
}

func (x *ModelInstanceGroup) GetGpus() []int32 {
	if x != nil {
		return x.Gpus
	}
	return nil
}

func (x *ModelInstanceGroup) GetSecondaryDevices() []*ModelInstanceGroup_SecondaryDevice {
	if x != nil {
		return x.SecondaryDevices
	}
	return nil
}

func (x *ModelInstanceGroup) GetProfile() []string {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *ModelInstanceGroup) GetPassive() bool {
	if x != nil {
		return x.Passive
	}
	return false
}

func (x *ModelInstanceGroup) GetHostPolicy() string {
	if x != nil {
		return x.HostPolicy
	}
	return ""
}

// @@
// @@.. cpp:var:: message ModelTensorReshape
// @@
// @@   Reshape specification for input and output tensors.
// @@
type ModelTensorReshape struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: int64 shape (repeated)
	// @@
	// @@     The shape to use for reshaping.
	// @@
	Shape []int64 `protobuf:"varint,1,rep,packed,name=shape,proto3" json:"shape,omitempty"`
}

func (x *ModelTensorReshape) Reset() {
	*x = ModelTensorReshape{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelTensorReshape) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelTensorReshape) ProtoMessage() {}

func (x *ModelTensorReshape) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelTensorReshape.ProtoReflect.Descriptor instead.
func (*ModelTensorReshape) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{2}
}

func (x *ModelTensorReshape) GetShape() []int64 {
	if x != nil {
		return x.Shape
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelInput
// @@
// @@   An input required by the model.
// @@
type ModelInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the input.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: DataType data_type
	// @@
	// @@     The data-type of the input.
	// @@
	DataType DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@  .. cpp:var:: Format format
	// @@
	// @@     The format of the input. Optional.
	// @@
	Format ModelInput_Format `protobuf:"varint,3,opt,name=format,proto3,enum=inference.ModelInput_Format" json:"format,omitempty"`
	// @@  .. cpp:var:: int64 dims (repeated)
	// @@
	// @@     The dimensions/shape of the input tensor that must be provided
	// @@     when invoking the inference API for this model.
	// @@
	Dims []int64 `protobuf:"varint,4,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// @@  .. cpp:var:: ModelTensorReshape reshape
	// @@
	// @@     The shape expected for this input by the backend. The input will
	// @@     be reshaped to this before being presented to the backend. The
	// @@     reshape must have the same number of elements as the input shape
	// @@     specified by 'dims'. Optional.
	// @@
	Reshape *ModelTensorReshape `protobuf:"bytes,5,opt,name=reshape,proto3" json:"reshape,omitempty"`
	// @@  .. cpp:var:: bool is_shape_tensor
	// @@
	// @@     Whether or not the input is a shape tensor to the model. This field
	// @@     is currently supported only for the TensorRT model. An error will be
	// @@     generated if this specification does not comply with underlying
	// @@     model.
	// @@
	IsShapeTensor bool `protobuf:"varint,6,opt,name=is_shape_tensor,json=isShapeTensor,proto3" json:"is_shape_tensor,omitempty"`
	// @@  .. cpp:var:: bool allow_ragged_batch
	// @@
	// @@     Whether or not the input is allowed to be "ragged" in a dynamically
	// @@     created batch. Default is false indicating that two requests will
	// @@     only be batched if this tensor has the same shape in both requests.
	// @@     True indicates that two requests can be batched even if this tensor
	// @@     has a different shape in each request.
	// @@
	AllowRaggedBatch bool `protobuf:"varint,7,opt,name=allow_ragged_batch,json=allowRaggedBatch,proto3" json:"allow_ragged_batch,omitempty"`
	// @@  .. cpp:var:: bool optional
	// @@
	// @@     Whether or not the input is optional for the model execution.
	// @@     If true, the input is not required in the inference request.
	// @@     Default value is false.
	// @@
	Optional bool `protobuf:"varint,8,opt,name=optional,proto3" json:"optional,omitempty"`
}

func (x *ModelInput) Reset() {
	*x = ModelInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInput) ProtoMessage() {}

func (x *ModelInput) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInput.ProtoReflect.Descriptor instead.
func (*ModelInput) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{3}
}

func (x *ModelInput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelInput) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *ModelInput) GetFormat() ModelInput_Format {
	if x != nil {
		return x.Format
	}
	return ModelInput_FORMAT_NONE
}

func (x *ModelInput) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (x *ModelInput) GetReshape() *ModelTensorReshape {
	if x != nil {
		return x.Reshape
	}
	return nil
}

func (x *ModelInput) GetIsShapeTensor() bool {
	if x != nil {
		return x.IsShapeTensor
	}
	return false
}

func (x *ModelInput) GetAllowRaggedBatch() bool {
	if x != nil {
		return x.AllowRaggedBatch
	}
	return false
}

func (x *ModelInput) GetOptional() bool {
	if x != nil {
		return x.Optional
	}
	return false
}

// @@
// @@.. cpp:var:: message ModelOutput
// @@
// @@   An output produced by the model.
// @@
type ModelOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the output.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: DataType data_type
	// @@
	// @@     The data-type of the output.
	// @@
	DataType DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@  .. cpp:var:: int64 dims (repeated)
	// @@
	// @@     The dimensions/shape of the output tensor.
	// @@
	Dims []int64 `protobuf:"varint,3,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// @@  .. cpp:var:: ModelTensorReshape reshape
	// @@
	// @@     The shape produced for this output by the backend. The output will
	// @@     be reshaped from this to the shape specifed in 'dims' before being
	// @@     returned in the inference response. The reshape must have the same
	// @@     number of elements as the output shape specified by 'dims'. Optional.
	// @@
	Reshape *ModelTensorReshape `protobuf:"bytes,5,opt,name=reshape,proto3" json:"reshape,omitempty"`
	// @@  .. cpp:var:: string label_filename
	// @@
	// @@     The label file associated with this output. Should be specified only
	// @@     for outputs that represent classifications. Optional.
	// @@
	LabelFilename string `protobuf:"bytes,4,opt,name=label_filename,json=labelFilename,proto3" json:"label_filename,omitempty"`
	// @@  .. cpp:var:: bool is_shape_tensor
	// @@
	// @@     Whether or not the output is a shape tensor to the model. This field
	// @@     is currently supported only for the TensorRT model. An error will be
	// @@     generated if this specification does not comply with underlying
	// @@     model.
	// @@
	IsShapeTensor bool `protobuf:"varint,6,opt,name=is_shape_tensor,json=isShapeTensor,proto3" json:"is_shape_tensor,omitempty"`
}

func (x *ModelOutput) Reset() {
	*x = ModelOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOutput) ProtoMessage() {}

func (x *ModelOutput) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOutput.ProtoReflect.Descriptor instead.
func (*ModelOutput) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{4}
}

func (x *ModelOutput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelOutput) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *ModelOutput) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (x *ModelOutput) GetReshape() *ModelTensorReshape {
	if x != nil {
		return x.Reshape
	}
	return nil
}

func (x *ModelOutput) GetLabelFilename() string {
	if x != nil {
		return x.LabelFilename
	}
	return ""
}

func (x *ModelOutput) GetIsShapeTensor() bool {
	if x != nil {
		return x.IsShapeTensor
	}
	return false
}

// @@  .. cpp:var:: message BatchInput
// @@
// @@     A batch input is an additional input that must be added by
// @@     the backend based on all the requests in a batch.
// @@
type BatchInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: Kind kind
	// @@
	// @@       The kind of this batch input.
	// @@
	Kind BatchInput_Kind `protobuf:"varint,1,opt,name=kind,proto3,enum=inference.BatchInput_Kind" json:"kind,omitempty"`
	// @@    .. cpp:var:: string target_name (repeated)
	// @@
	// @@       The name of the model inputs that the backend will create
	// @@       for this batch input.
	// @@
	TargetName []string `protobuf:"bytes,2,rep,name=target_name,json=targetName,proto3" json:"target_name,omitempty"`
	// @@    .. cpp:var:: DataType data_type
	// @@
	// @@       The input's datatype. The data type can be TYPE_INT32 or
	// @@       TYPE_FP32.
	// @@
	DataType DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@    .. cpp:var:: string source_input (repeated)
	// @@
	// @@       The backend derives the value for each batch input from one or
	// @@       more other inputs. 'source_input' gives the names of those
	// @@       inputs.
	// @@
	SourceInput []string `protobuf:"bytes,4,rep,name=source_input,json=sourceInput,proto3" json:"source_input,omitempty"`
}

func (x *BatchInput) Reset() {
	*x = BatchInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInput) ProtoMessage() {}

func (x *BatchInput) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInput.ProtoReflect.Descriptor instead.
func (*BatchInput) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{5}
}

func (x *BatchInput) GetKind() BatchInput_Kind {
	if x != nil {
		return x.Kind
	}
	return BatchInput_BATCH_ELEMENT_COUNT
}

func (x *BatchInput) GetTargetName() []string {
	if x != nil {
		return x.TargetName
	}
	return nil
}

func (x *BatchInput) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *BatchInput) GetSourceInput() []string {
	if x != nil {
		return x.SourceInput
	}
	return nil
}

// @@.. cpp:var:: message BatchOutput
// @@
// @@   A batch output is an output produced by the model that must be handled
// @@   differently by the backend based on all the requests in a batch.
// @@
type BatchOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string target_name (repeated)
	// @@
	// @@     The name of the outputs to be produced by this batch output
	// @@     specification.
	// @@
	TargetName []string `protobuf:"bytes,1,rep,name=target_name,json=targetName,proto3" json:"target_name,omitempty"`
	// @@  .. cpp:var:: Kind kind
	// @@
	// @@     The kind of this batch output.
	// @@
	Kind BatchOutput_Kind `protobuf:"varint,2,opt,name=kind,proto3,enum=inference.BatchOutput_Kind" json:"kind,omitempty"`
	// @@  .. cpp:var:: string source_input (repeated)
	// @@
	// @@     The backend derives each batch output from one or more inputs.
	// @@     'source_input' gives the names of those inputs.
	// @@
	SourceInput []string `protobuf:"bytes,3,rep,name=source_input,json=sourceInput,proto3" json:"source_input,omitempty"`
}

func (x *BatchOutput) Reset() {
	*x = BatchOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchOutput) ProtoMessage() {}

func (x *BatchOutput) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchOutput.ProtoReflect.Descriptor instead.
func (*BatchOutput) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{6}
}

func (x *BatchOutput) GetTargetName() []string {
	if x != nil {
		return x.TargetName
	}
	return nil
}

func (x *BatchOutput) GetKind() BatchOutput_Kind {
	if x != nil {
		return x.Kind
	}
	return BatchOutput_BATCH_SCATTER_WITH_INPUT_SHAPE
}

func (x *BatchOutput) GetSourceInput() []string {
	if x != nil {
		return x.SourceInput
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelVersionPolicy
// @@
// @@   Policy indicating which versions of a model should be made
// @@   available by the inference server.
// @@
type ModelVersionPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: oneof policy_choice
	// @@
	// @@     Each model must implement only a single version policy. The
	// @@     default policy is 'Latest'.
	// @@
	//
	// Types that are assignable to PolicyChoice:
	//
	//	*ModelVersionPolicy_Latest_
	//	*ModelVersionPolicy_All_
	//	*ModelVersionPolicy_Specific_
	PolicyChoice isModelVersionPolicy_PolicyChoice `protobuf_oneof:"policy_choice"`
}

func (x *ModelVersionPolicy) Reset() {
	*x = ModelVersionPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelVersionPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelVersionPolicy) ProtoMessage() {}

func (x *ModelVersionPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelVersionPolicy.ProtoReflect.Descriptor instead.
func (*ModelVersionPolicy) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{7}
}

func (m *ModelVersionPolicy) GetPolicyChoice() isModelVersionPolicy_PolicyChoice {
	if m != nil {
		return m.PolicyChoice
	}
	return nil
}

func (x *ModelVersionPolicy) GetLatest() *ModelVersionPolicy_Latest {
	if x, ok := x.GetPolicyChoice().(*ModelVersionPolicy_Latest_); ok {
		return x.Latest
	}
	return nil
}

func (x *ModelVersionPolicy) GetAll() *ModelVersionPolicy_All {
	if x, ok := x.GetPolicyChoice().(*ModelVersionPolicy_All_); ok {
		return x.All
	}
	return nil
}

func (x *ModelVersionPolicy) GetSpecific() *ModelVersionPolicy_Specific {
	if x, ok := x.GetPolicyChoice().(*ModelVersionPolicy_Specific_); ok {
		return x.Specific
	}
	return nil
}

type isModelVersionPolicy_PolicyChoice interface {
	isModelVersionPolicy_PolicyChoice()
}

type ModelVersionPolicy_Latest_ struct {
	// @@    .. cpp:var:: Latest latest
	// @@
	// @@       Serve only latest version(s) of the model.
	// @@
	Latest *ModelVersionPolicy_Latest `protobuf:"bytes,1,opt,name=latest,proto3,oneof"`
}

type ModelVersionPolicy_All_ struct {
	// @@    .. cpp:var:: All all
	// @@
	// @@       Serve all versions of the model.
	// @@
	All *ModelVersionPolicy_All `protobuf:"bytes,2,opt,name=all,proto3,oneof"`
}

type ModelVersionPolicy_Specific_ struct {
	// @@    .. cpp:var:: Specific specific
	// @@
	// @@       Serve only specific version(s) of the model.
	// @@
	Specific *ModelVersionPolicy_Specific `protobuf:"bytes,3,opt,name=specific,proto3,oneof"`
}

func (*ModelVersionPolicy_Latest_) isModelVersionPolicy_PolicyChoice() {}

func (*ModelVersionPolicy_All_) isModelVersionPolicy_PolicyChoice() {}

func (*ModelVersionPolicy_Specific_) isModelVersionPolicy_PolicyChoice() {}

// @@
// @@.. cpp:var:: message ModelOptimizationPolicy
// @@
// @@   Optimization settings for a model. These settings control if/how a
// @@   model is optimized and prioritized by the backend framework when
// @@   it is loaded.
// @@
type ModelOptimizationPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: Graph graph
	// @@
	// @@     The graph optimization setting for the model. Optional.
	// @@
	Graph *ModelOptimizationPolicy_Graph `protobuf:"bytes,1,opt,name=graph,proto3" json:"graph,omitempty"`
	// @@  .. cpp:var:: ModelPriority priority
	// @@
	// @@     The priority setting for the model. Optional.
	// @@
	Priority ModelOptimizationPolicy_ModelPriority `protobuf:"varint,2,opt,name=priority,proto3,enum=inference.ModelOptimizationPolicy_ModelPriority" json:"priority,omitempty"`
	// @@  .. cpp:var:: Cuda cuda
	// @@
	// @@     CUDA-specific optimization settings. Optional.
	// @@
	Cuda *ModelOptimizationPolicy_Cuda `protobuf:"bytes,3,opt,name=cuda,proto3" json:"cuda,omitempty"`
	// @@  .. cpp:var:: ExecutionAccelerators execution_accelerators
	// @@
	// @@     The accelerators used for the model. Optional.
	// @@
	ExecutionAccelerators *ModelOptimizationPolicy_ExecutionAccelerators `protobuf:"bytes,4,opt,name=execution_accelerators,json=executionAccelerators,proto3" json:"execution_accelerators,omitempty"`
	// @@  .. cpp:var:: PinnedMemoryBuffer input_pinned_memory
	// @@
	// @@     Use pinned memory buffer when the data transfer for inputs
	// @@     is between GPU memory and non-pinned system memory.
	// @@     Default is true.
	// @@
	InputPinnedMemory *ModelOptimizationPolicy_PinnedMemoryBuffer `protobuf:"bytes,5,opt,name=input_pinned_memory,json=inputPinnedMemory,proto3" json:"input_pinned_memory,omitempty"`
	// @@  .. cpp:var:: PinnedMemoryBuffer output_pinned_memory
	// @@
	// @@     Use pinned memory buffer when the data transfer for outputs
	// @@     is between GPU memory and non-pinned system memory.
	// @@     Default is true.
	// @@
	OutputPinnedMemory *ModelOptimizationPolicy_PinnedMemoryBuffer `protobuf:"bytes,6,opt,name=output_pinned_memory,json=outputPinnedMemory,proto3" json:"output_pinned_memory,omitempty"`
	// @@  .. cpp:var:: uint32 gather_kernel_buffer_threshold
	// @@
	// @@     The backend may use a gather kernel to gather input data if the
	// @@     device has direct access to the source buffer and the destination
	// @@     buffer. In such case, the gather kernel will be used only if the
	// @@     number of buffers to be gathered is greater or equal to
	// @@     the specifed value. If 0, the gather kernel will be disabled.
	// @@     Default value is 0.
	// @@     Currently only recognized by TensorRT backend.
	// @@
	GatherKernelBufferThreshold uint32 `protobuf:"varint,7,opt,name=gather_kernel_buffer_threshold,json=gatherKernelBufferThreshold,proto3" json:"gather_kernel_buffer_threshold,omitempty"`
	// @@  .. cpp:var:: bool eager_batching
	// @@
	// @@     Start preparing the next batch before the model instance is ready
	// @@     for the next inference. This option can be used to overlap the
	// @@     batch preparation with model execution, with the trade-off that
	// @@     the next batch might be smaller than what it could have been.
	// @@     Default value is false.
	// @@     Currently only recognized by TensorRT backend.
	// @@
	EagerBatching bool `protobuf:"varint,8,opt,name=eager_batching,json=eagerBatching,proto3" json:"eager_batching,omitempty"`
}

func (x *ModelOptimizationPolicy) Reset() {
	*x = ModelOptimizationPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy) ProtoMessage() {}

func (x *ModelOptimizationPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8}
}

func (x *ModelOptimizationPolicy) GetGraph() *ModelOptimizationPolicy_Graph {
	if x != nil {
		return x.Graph
	}
	return nil
}

func (x *ModelOptimizationPolicy) GetPriority() ModelOptimizationPolicy_ModelPriority {
	if x != nil {
		return x.Priority
	}
	return ModelOptimizationPolicy_PRIORITY_DEFAULT
}

func (x *ModelOptimizationPolicy) GetCuda() *ModelOptimizationPolicy_Cuda {
	if x != nil {
		return x.Cuda
	}
	return nil
}

func (x *ModelOptimizationPolicy) GetExecutionAccelerators() *ModelOptimizationPolicy_ExecutionAccelerators {
	if x != nil {
		return x.ExecutionAccelerators
	}
	return nil
}

func (x *ModelOptimizationPolicy) GetInputPinnedMemory() *ModelOptimizationPolicy_PinnedMemoryBuffer {
	if x != nil {
		return x.InputPinnedMemory
	}
	return nil
}

func (x *ModelOptimizationPolicy) GetOutputPinnedMemory() *ModelOptimizationPolicy_PinnedMemoryBuffer {
	if x != nil {
		return x.OutputPinnedMemory
	}
	return nil
}

func (x *ModelOptimizationPolicy) GetGatherKernelBufferThreshold() uint32 {
	if x != nil {
		return x.GatherKernelBufferThreshold
	}
	return 0
}

func (x *ModelOptimizationPolicy) GetEagerBatching() bool {
	if x != nil {
		return x.EagerBatching
	}
	return false
}

// @@
// @@.. cpp:var:: message ModelQueuePolicy
// @@
// @@   Queue policy for inference requests.
// @@
type ModelQueuePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: TimeoutAction timeout_action
	// @@
	// @@     The action applied to timed-out request.
	// @@     The default action is REJECT.
	// @@
	TimeoutAction ModelQueuePolicy_TimeoutAction `protobuf:"varint,1,opt,name=timeout_action,json=timeoutAction,proto3,enum=inference.ModelQueuePolicy_TimeoutAction" json:"timeout_action,omitempty"`
	// @@
	// @@  .. cpp:var:: uint64 default_timeout_microseconds
	// @@
	// @@     The default timeout for every request, in microseconds.
	// @@     The default value is 0 which indicates that no timeout is set.
	// @@
	DefaultTimeoutMicroseconds uint64 `protobuf:"varint,2,opt,name=default_timeout_microseconds,json=defaultTimeoutMicroseconds,proto3" json:"default_timeout_microseconds,omitempty"`
	// @@
	// @@  .. cpp:var:: bool allow_timeout_override
	// @@
	// @@     Whether individual request can override the default timeout value.
	// @@     When true, individual requests can set a timeout that is less than
	// @@     the default timeout value but may not increase the timeout.
	// @@     The default value is false.
	// @@
	AllowTimeoutOverride bool `protobuf:"varint,3,opt,name=allow_timeout_override,json=allowTimeoutOverride,proto3" json:"allow_timeout_override,omitempty"`
	// @@
	// @@  .. cpp:var:: uint32 max_queue_size
	// @@
	// @@     The maximum queue size for holding requests. A request will be
	// @@     rejected immediately if it can't be enqueued because the queue is
	// @@     full. The default value is 0 which indicates that no maximum
	// @@     queue size is enforced.
	// @@
	MaxQueueSize uint32 `protobuf:"varint,4,opt,name=max_queue_size,json=maxQueueSize,proto3" json:"max_queue_size,omitempty"`
}

func (x *ModelQueuePolicy) Reset() {
	*x = ModelQueuePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelQueuePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelQueuePolicy) ProtoMessage() {}

func (x *ModelQueuePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelQueuePolicy.ProtoReflect.Descriptor instead.
func (*ModelQueuePolicy) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{9}
}

func (x *ModelQueuePolicy) GetTimeoutAction() ModelQueuePolicy_TimeoutAction {
	if x != nil {
		return x.TimeoutAction
	}
	return ModelQueuePolicy_REJECT
}

func (x *ModelQueuePolicy) GetDefaultTimeoutMicroseconds() uint64 {
	if x != nil {
		return x.DefaultTimeoutMicroseconds
	}
	return 0
}

func (x *ModelQueuePolicy) GetAllowTimeoutOverride() bool {
	if x != nil {
		return x.AllowTimeoutOverride
	}
	return false
}

func (x *ModelQueuePolicy) GetMaxQueueSize() uint32 {
	if x != nil {
		return x.MaxQueueSize
	}
	return 0
}

// @@
// @@.. cpp:var:: message ModelDynamicBatching
// @@
// @@   Dynamic batching configuration. These settings control how dynamic
// @@   batching operates for the model.
// @@
type ModelDynamicBatching struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: int32 preferred_batch_size (repeated)
	// @@
	// @@     Preferred batch sizes for dynamic batching. If a batch of one of
	// @@     these sizes can be formed it will be executed immediately.  If
	// @@     not specified a preferred batch size will be chosen automatically
	// @@     based on model and GPU characteristics.
	// @@
	PreferredBatchSize []int32 `protobuf:"varint,1,rep,packed,name=preferred_batch_size,json=preferredBatchSize,proto3" json:"preferred_batch_size,omitempty"`
	// @@  .. cpp:var:: uint64 max_queue_delay_microseconds
	// @@
	// @@     The maximum time, in microseconds, a request will be delayed in
	// @@     the scheduling queue to wait for additional requests for
	// @@     batching. Default is 0.
	// @@
	MaxQueueDelayMicroseconds uint64 `protobuf:"varint,2,opt,name=max_queue_delay_microseconds,json=maxQueueDelayMicroseconds,proto3" json:"max_queue_delay_microseconds,omitempty"`
	// @@  .. cpp:var:: bool preserve_ordering
	// @@
	// @@     Should the dynamic batcher preserve the ordering of responses to
	// @@     match the order of requests received by the scheduler. Default is
	// @@     false. If true, the responses will be returned in the same order as
	// @@     the order of requests sent to the scheduler. If false, the responses
	// @@     may be returned in arbitrary order. This option is specifically
	// @@     needed when a sequence of related inference requests (i.e. inference
	// @@     requests with the same correlation ID) are sent to the dynamic
	// @@     batcher to ensure that the sequence responses are in the correct
	// @@     order.
	// @@
	PreserveOrdering bool `protobuf:"varint,3,opt,name=preserve_ordering,json=preserveOrdering,proto3" json:"preserve_ordering,omitempty"`
	// @@  .. cpp:var:: uint32 priority_levels
	// @@
	// @@     The number of priority levels to be enabled for the model,
	// @@     the priority level starts from 1 and 1 is the highest priority.
	// @@     Requests are handled in priority order with all priority 1 requests
	// @@     processed before priority 2, all priority 2 requests processed before
	// @@     priority 3, etc. Requests with the same priority level will be
	// @@     handled in the order that they are received.
	// @@
	PriorityLevels uint32 `protobuf:"varint,4,opt,name=priority_levels,json=priorityLevels,proto3" json:"priority_levels,omitempty"`
	// @@  .. cpp:var:: uint32 default_priority_level
	// @@
	// @@     The priority level used for requests that don't specify their
	// @@     priority. The value must be in the range [ 1, 'priority_levels' ].
	// @@
	DefaultPriorityLevel uint32 `protobuf:"varint,5,opt,name=default_priority_level,json=defaultPriorityLevel,proto3" json:"default_priority_level,omitempty"`
	// @@  .. cpp:var:: ModelQueuePolicy default_queue_policy
	// @@
	// @@     The default queue policy used for requests that don't require
	// @@     priority handling and requests that specify priority levels where
	// @@     there is no specific policy given. If not specified, a policy with
	// @@     default field values will be used.
	// @@
	DefaultQueuePolicy *ModelQueuePolicy `protobuf:"bytes,6,opt,name=default_queue_policy,json=defaultQueuePolicy,proto3" json:"default_queue_policy,omitempty"`
	// @@  .. cpp:var:: map<uint32, ModelQueuePolicy> priority_queue_policy
	// @@
	// @@     Specify the queue policy for the priority level. The default queue
	// @@     policy will be used if a priority level doesn't specify a queue
	// @@     policy.
	// @@
	PriorityQueuePolicy map[uint32]*ModelQueuePolicy `protobuf:"bytes,7,rep,name=priority_queue_policy,json=priorityQueuePolicy,proto3" json:"priority_queue_policy,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelDynamicBatching) Reset() {
	*x = ModelDynamicBatching{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDynamicBatching) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDynamicBatching) ProtoMessage() {}

func (x *ModelDynamicBatching) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDynamicBatching.ProtoReflect.Descriptor instead.
func (*ModelDynamicBatching) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{10}
}

func (x *ModelDynamicBatching) GetPreferredBatchSize() []int32 {
	if x != nil {
		return x.PreferredBatchSize
	}
	return nil
}

func (x *ModelDynamicBatching) GetMaxQueueDelayMicroseconds() uint64 {
	if x != nil {
		return x.MaxQueueDelayMicroseconds
	}
	return 0
}

func (x *ModelDynamicBatching) GetPreserveOrdering() bool {
	if x != nil {
		return x.PreserveOrdering
	}
	return false
}

func (x *ModelDynamicBatching) GetPriorityLevels() uint32 {
	if x != nil {
		return x.PriorityLevels
	}
	return 0
}

func (x *ModelDynamicBatching) GetDefaultPriorityLevel() uint32 {
	if x != nil {
		return x.DefaultPriorityLevel
	}
	return 0
}

func (x *ModelDynamicBatching) GetDefaultQueuePolicy() *ModelQueuePolicy {
	if x != nil {
		return x.DefaultQueuePolicy
	}
	return nil
}

func (x *ModelDynamicBatching) GetPriorityQueuePolicy() map[uint32]*ModelQueuePolicy {
	if x != nil {
		return x.PriorityQueuePolicy
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelSequenceBatching
// @@
// @@   Sequence batching configuration. These settings control how sequence
// @@   batching operates for the model.
// @@
type ModelSequenceBatching struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: oneof strategy_choice
	// @@
	// @@     The strategy used by the sequence batcher. Default strategy
	// @@     is 'direct'.
	// @@
	//
	// Types that are assignable to StrategyChoice:
	//
	//	*ModelSequenceBatching_Direct
	//	*ModelSequenceBatching_Oldest
	StrategyChoice isModelSequenceBatching_StrategyChoice `protobuf_oneof:"strategy_choice"`
	// @@  .. cpp:var:: uint64 max_sequence_idle_microseconds
	// @@
	// @@     The maximum time, in microseconds, that a sequence is allowed to
	// @@     be idle before it is aborted. The inference server considers a
	// @@     sequence idle when it does not have any inference request queued
	// @@     for the sequence. If this limit is exceeded, the inference server
	// @@     will free the sequence slot allocated by the sequence and make it
	// @@     available for another sequence. If not specified (or specified as
	// @@     zero) a default value of 1000000 (1 second) is used.
	// @@
	MaxSequenceIdleMicroseconds uint64 `protobuf:"varint,1,opt,name=max_sequence_idle_microseconds,json=maxSequenceIdleMicroseconds,proto3" json:"max_sequence_idle_microseconds,omitempty"`
	// @@  .. cpp:var:: ControlInput control_input (repeated)
	// @@
	// @@     The model input(s) that the server should use to communicate
	// @@     sequence start, stop, ready and similar control values to the
	// @@     model.
	// @@
	ControlInput []*ModelSequenceBatching_ControlInput `protobuf:"bytes,2,rep,name=control_input,json=controlInput,proto3" json:"control_input,omitempty"`
	// @@  .. cpp:var:: State state (repeated)
	// @@
	// @@     The optional state that can be stored in Triton for performing
	// @@     inference requests on a sequence. Each sequence holds an implicit
	// @@     state local to itself. The output state tensor provided by the
	// @@     model in 'output_name' field of the current inference request will
	// @@     be transferred as an input tensor named 'input_name' in the next
	// @@     request of the same sequence. The input state of the first request
	// @@     in the sequence contains garbage data.
	// @@
	State []*ModelSequenceBatching_State `protobuf:"bytes,5,rep,name=state,proto3" json:"state,omitempty"`
}

func (x *ModelSequenceBatching) Reset() {
	*x = ModelSequenceBatching{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching) ProtoMessage() {}

func (x *ModelSequenceBatching) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11}
}

func (m *ModelSequenceBatching) GetStrategyChoice() isModelSequenceBatching_StrategyChoice {
	if m != nil {
		return m.StrategyChoice
	}
	return nil
}

func (x *ModelSequenceBatching) GetDirect() *ModelSequenceBatching_StrategyDirect {
	if x, ok := x.GetStrategyChoice().(*ModelSequenceBatching_Direct); ok {
		return x.Direct
	}
	return nil
}

func (x *ModelSequenceBatching) GetOldest() *ModelSequenceBatching_StrategyOldest {
	if x, ok := x.GetStrategyChoice().(*ModelSequenceBatching_Oldest); ok {
		return x.Oldest
	}
	return nil
}

func (x *ModelSequenceBatching) GetMaxSequenceIdleMicroseconds() uint64 {
	if x != nil {
		return x.MaxSequenceIdleMicroseconds
	}
	return 0
}

func (x *ModelSequenceBatching) GetControlInput() []*ModelSequenceBatching_ControlInput {
	if x != nil {
		return x.ControlInput
	}
	return nil
}

func (x *ModelSequenceBatching) GetState() []*ModelSequenceBatching_State {
	if x != nil {
		return x.State
	}
	return nil
}

type isModelSequenceBatching_StrategyChoice interface {
	isModelSequenceBatching_StrategyChoice()
}

type ModelSequenceBatching_Direct struct {
	// @@    .. cpp:var:: StrategyDirect direct
	// @@
	// @@       StrategyDirect scheduling strategy.
	// @@
	Direct *ModelSequenceBatching_StrategyDirect `protobuf:"bytes,3,opt,name=direct,proto3,oneof"`
}

type ModelSequenceBatching_Oldest struct {
	// @@    .. cpp:var:: StrategyOldest oldest
	// @@
	// @@       StrategyOldest scheduling strategy.
	// @@
	Oldest *ModelSequenceBatching_StrategyOldest `protobuf:"bytes,4,opt,name=oldest,proto3,oneof"`
}

func (*ModelSequenceBatching_Direct) isModelSequenceBatching_StrategyChoice() {}

func (*ModelSequenceBatching_Oldest) isModelSequenceBatching_StrategyChoice() {}

// @@
// @@.. cpp:var:: message ModelEnsembling
// @@
// @@   Model ensembling configuration. These settings specify the models that
// @@   compose the ensemble and how data flows between the models.
// @@
type ModelEnsembling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: Step step (repeated)
	// @@
	// @@     The models and the input / output mappings used within the ensemble.
	// @@
	Step []*ModelEnsembling_Step `protobuf:"bytes,1,rep,name=step,proto3" json:"step,omitempty"`
}

func (x *ModelEnsembling) Reset() {
	*x = ModelEnsembling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelEnsembling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelEnsembling) ProtoMessage() {}

func (x *ModelEnsembling) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelEnsembling.ProtoReflect.Descriptor instead.
func (*ModelEnsembling) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{12}
}

func (x *ModelEnsembling) GetStep() []*ModelEnsembling_Step {
	if x != nil {
		return x.Step
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelParameter
// @@
// @@   A model parameter.
// @@
type ModelParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string string_value
	// @@
	// @@     The string value of the parameter.
	// @@
	StringValue string `protobuf:"bytes,1,opt,name=string_value,json=stringValue,proto3" json:"string_value,omitempty"`
}

func (x *ModelParameter) Reset() {
	*x = ModelParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelParameter) ProtoMessage() {}

func (x *ModelParameter) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelParameter.ProtoReflect.Descriptor instead.
func (*ModelParameter) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{13}
}

func (x *ModelParameter) GetStringValue() string {
	if x != nil {
		return x.StringValue
	}
	return ""
}

// @@
// @@.. cpp:var:: message ModelWarmup
// @@
// @@   Settings used to construct the request sample for model warmup.
// @@
type ModelWarmup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the request sample.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: uint32 batch_size
	// @@
	// @@     The batch size of the inference request. This must be >= 1. For
	// @@     models that don't support batching, batch_size must be 1. If
	// @@     batch_size > 1, the 'inputs' specified below will be duplicated to
	// @@     match the batch size requested.
	// @@
	BatchSize uint32 `protobuf:"varint,2,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
	// @@  .. cpp:var:: map<string, Input> inputs
	// @@
	// @@     The warmup meta data associated with every model input, including
	// @@     control tensors.
	// @@
	Inputs map[string]*ModelWarmup_Input `protobuf:"bytes,3,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelWarmup) Reset() {
	*x = ModelWarmup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelWarmup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelWarmup) ProtoMessage() {}

func (x *ModelWarmup) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelWarmup.ProtoReflect.Descriptor instead.
func (*ModelWarmup) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{14}
}

func (x *ModelWarmup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelWarmup) GetBatchSize() uint32 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

func (x *ModelWarmup) GetInputs() map[string]*ModelWarmup_Input {
	if x != nil {
		return x.Inputs
	}
	return nil
}

// @@
// @@ .. cpp:var:: message ModelOperations
// @@
// @@    The metadata of libraries providing custom operations for this model.
// @@
type ModelOperations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string op_library_filename (repeated)
	// @@
	// @@     Optional paths of the libraries providing custom operations for
	// @@     this model. Valid only for ONNX models.
	// @@
	OpLibraryFilename []string `protobuf:"bytes,1,rep,name=op_library_filename,json=opLibraryFilename,proto3" json:"op_library_filename,omitempty"`
}

func (x *ModelOperations) Reset() {
	*x = ModelOperations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOperations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOperations) ProtoMessage() {}

func (x *ModelOperations) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOperations.ProtoReflect.Descriptor instead.
func (*ModelOperations) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{15}
}

func (x *ModelOperations) GetOpLibraryFilename() []string {
	if x != nil {
		return x.OpLibraryFilename
	}
	return nil
}

// @@
// @@ .. cpp:var:: message ModelTransactionPolicy
// @@
// @@    The specification that describes the nature of transactions
// @@    to be expected from the model.
// @@
type ModelTransactionPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: bool decoupled
	// @@
	// @@     Indicates whether responses generated by the model are decoupled with
	// @@     the requests issued to it, which means the number of responses
	// @@     generated by model may differ from number of requests issued, and
	// @@     that the responses may be out of order relative to the order of
	// @@     requests. The default is false, which means the model will generate
	// @@     exactly one response for each request.
	// @@
	Decoupled bool `protobuf:"varint,1,opt,name=decoupled,proto3" json:"decoupled,omitempty"`
}

func (x *ModelTransactionPolicy) Reset() {
	*x = ModelTransactionPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelTransactionPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelTransactionPolicy) ProtoMessage() {}

func (x *ModelTransactionPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelTransactionPolicy.ProtoReflect.Descriptor instead.
func (*ModelTransactionPolicy) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{16}
}

func (x *ModelTransactionPolicy) GetDecoupled() bool {
	if x != nil {
		return x.Decoupled
	}
	return false
}

// @@
// @@.. cpp:var:: message ModelRepositoryAgents
// @@
// @@   The repository agents for the model.
// @@
type ModelRepositoryAgents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp:var:: Agent agents (repeated)
	// @@
	// @@     The ordered list of agents for the model. These agents will be
	// @@     invoked in order to respond to repository actions occuring for the
	// @@     model.
	// @@
	Agents []*ModelRepositoryAgents_Agent `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
}

func (x *ModelRepositoryAgents) Reset() {
	*x = ModelRepositoryAgents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRepositoryAgents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRepositoryAgents) ProtoMessage() {}

func (x *ModelRepositoryAgents) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRepositoryAgents.ProtoReflect.Descriptor instead.
func (*ModelRepositoryAgents) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{17}
}

func (x *ModelRepositoryAgents) GetAgents() []*ModelRepositoryAgents_Agent {
	if x != nil {
		return x.Agents
	}
	return nil
}

// @@
// @@.. cpp:var:: message ModelResponseCache
// @@
// @@   The response cache setting for the model.
// @@
type ModelResponseCache struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@
	// @@  .. cpp::var:: bool enable
	// @@
	// @@     Whether or not to use response cache for the model. If True, the
	// @@     responses from the model are cached and when identical request
	// @@     is encountered, instead of going through the model execution,
	// @@     the response from the cache is utilized. By default, response
	// @@     cache is disabled for the models.
	// @@
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *ModelResponseCache) Reset() {
	*x = ModelResponseCache{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelResponseCache) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelResponseCache) ProtoMessage() {}

func (x *ModelResponseCache) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelResponseCache.ProtoReflect.Descriptor instead.
func (*ModelResponseCache) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{18}
}

func (x *ModelResponseCache) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

// @@
// @@.. cpp:var:: message ModelConfig
// @@
// @@   A model configuration.
// @@
type ModelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the model.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: string platform
	// @@
	// @@     The framework for the model. Possible values are
	// @@     "tensorrt_plan", "tensorflow_graphdef",
	// @@     "tensorflow_savedmodel", "onnxruntime_onnx",
	// @@     "pytorch_libtorch".
	// @@
	Platform string `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`
	// @@  .. cpp:var:: string backend
	// @@
	// @@     The backend used by the model.
	// @@
	Backend string `protobuf:"bytes,17,opt,name=backend,proto3" json:"backend,omitempty"`
	// @@  .. cpp:var:: ModelVersionPolicy version_policy
	// @@
	// @@     Policy indicating which version(s) of the model will be served.
	// @@
	VersionPolicy *ModelVersionPolicy `protobuf:"bytes,3,opt,name=version_policy,json=versionPolicy,proto3" json:"version_policy,omitempty"`
	// @@  .. cpp:var:: int32 max_batch_size
	// @@
	// @@     Maximum batch size allowed for inference. This can only decrease
	// @@     what is allowed by the model itself. A max_batch_size value of 0
	// @@     indicates that batching is not allowed for the model and the
	// @@     dimension/shape of the input and output tensors must exactly
	// @@     match what is specified in the input and output configuration. A
	// @@     max_batch_size value > 0 indicates that batching is allowed and
	// @@     so the model expects the input tensors to have an additional
	// @@     initial dimension for the batching that is not specified in the
	// @@     input (for example, if the model supports batched inputs of
	// @@     2-dimensional tensors then the model configuration will specify
	// @@     the input shape as [ X, Y ] but the model will expect the actual
	// @@     input tensors to have shape [ N, X, Y ]). For max_batch_size > 0
	// @@     returned outputs will also have an additional initial dimension
	// @@     for the batch.
	// @@
	MaxBatchSize int32 `protobuf:"varint,4,opt,name=max_batch_size,json=maxBatchSize,proto3" json:"max_batch_size,omitempty"`
	// @@  .. cpp:var:: ModelInput input (repeated)
	// @@
	// @@     The inputs request by the model.
	// @@
	Input []*ModelInput `protobuf:"bytes,5,rep,name=input,proto3" json:"input,omitempty"`
	// @@  .. cpp:var:: ModelOutput output (repeated)
	// @@
	// @@     The outputs produced by the model.
	// @@
	Output []*ModelOutput `protobuf:"bytes,6,rep,name=output,proto3" json:"output,omitempty"`
	// @@  .. cpp:var:: BatchInput batch_input (repeated)
	// @@
	// @@     The model input(s) that the server should use to communicate
	// @@     batch related values to the model.
	// @@
	BatchInput []*BatchInput `protobuf:"bytes,20,rep,name=batch_input,json=batchInput,proto3" json:"batch_input,omitempty"`
	// @@  .. cpp:var:: BatchOutput batch_output (repeated)
	// @@
	// @@     The outputs produced by the model that requires special handling
	// @@     by the model backend.
	// @@
	BatchOutput []*BatchOutput `protobuf:"bytes,21,rep,name=batch_output,json=batchOutput,proto3" json:"batch_output,omitempty"`
	// @@  .. cpp:var:: ModelOptimizationPolicy optimization
	// @@
	// @@     Optimization configuration for the model. If not specified
	// @@     then default optimization policy is used.
	// @@
	Optimization *ModelOptimizationPolicy `protobuf:"bytes,12,opt,name=optimization,proto3" json:"optimization,omitempty"`
	// @@  .. cpp:var:: oneof scheduling_choice
	// @@
	// @@     The scheduling policy for the model. If not specified the
	// @@     default scheduling policy is used for the model. The default
	// @@     policy is to execute each inference request independently.
	// @@
	//
	// Types that are assignable to SchedulingChoice:
	//
	//	*ModelConfig_DynamicBatching
	//	*ModelConfig_SequenceBatching
	//	*ModelConfig_EnsembleScheduling
	SchedulingChoice isModelConfig_SchedulingChoice `protobuf_oneof:"scheduling_choice"`
	// @@  .. cpp:var:: ModelInstanceGroup instance_group (repeated)
	// @@
	// @@     Instances of this model. If not specified, one instance
	// @@     of the model will be instantiated on each available GPU.
	// @@
	InstanceGroup []*ModelInstanceGroup `protobuf:"bytes,7,rep,name=instance_group,json=instanceGroup,proto3" json:"instance_group,omitempty"`
	// @@  .. cpp:var:: string default_model_filename
	// @@
	// @@     Optional filename of the model file to use if a
	// @@     compute-capability specific model is not specified in
	// @@     :cpp:var:`cc_model_filenames`. If not specified the default name
	// @@     is 'model.graphdef', 'model.savedmodel', 'model.plan' or
	// @@     'model.pt' depending on the model type.
	// @@
	DefaultModelFilename string `protobuf:"bytes,8,opt,name=default_model_filename,json=defaultModelFilename,proto3" json:"default_model_filename,omitempty"`
	// @@  .. cpp:var:: map<string,string> cc_model_filenames
	// @@
	// @@     Optional map from CUDA compute capability to the filename of
	// @@     the model that supports that compute capability. The filename
	// @@     refers to a file within the model version directory.
	// @@
	CcModelFilenames map[string]string `protobuf:"bytes,9,rep,name=cc_model_filenames,json=ccModelFilenames,proto3" json:"cc_model_filenames,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@  .. cpp:var:: map<string,string> metric_tags
	// @@
	// @@     Optional metric tags. User-specific key-value pairs for metrics
	// @@     reported for this model. These tags are applied to the metrics
	// @@     reported on the HTTP metrics port.
	// @@
	MetricTags map[string]string `protobuf:"bytes,10,rep,name=metric_tags,json=metricTags,proto3" json:"metric_tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@  .. cpp:var:: map<string,ModelParameter> parameters
	// @@
	// @@     Optional model parameters. User-specified parameter values.
	// @@
	Parameters map[string]*ModelParameter `protobuf:"bytes,14,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@  .. cpp:var:: ModelWarmup model_warmup (repeated)
	// @@
	// @@     Warmup setting of this model. If specified, all instances
	// @@     will be run with the request samples in sequence before
	// @@     serving the model.
	// @@     This field can only be specified if the model is not an ensemble
	// @@     model.
	// @@
	ModelWarmup []*ModelWarmup `protobuf:"bytes,16,rep,name=model_warmup,json=modelWarmup,proto3" json:"model_warmup,omitempty"`
	// @@  .. cpp:var:: ModelOperations model_operations
	// @@
	// @@     Optional metadata of the libraries providing custom operations for
	// @@     this model.
	// @@
	ModelOperations *ModelOperations `protobuf:"bytes,18,opt,name=model_operations,json=modelOperations,proto3" json:"model_operations,omitempty"`
	// @@  .. cpp:var:: ModelTransactionPolicy model_transaction_policy
	// @@
	// @@     Optional specification that describes the nature of transactions
	// @@     to be expected from the model.
	// @@
	ModelTransactionPolicy *ModelTransactionPolicy `protobuf:"bytes,19,opt,name=model_transaction_policy,json=modelTransactionPolicy,proto3" json:"model_transaction_policy,omitempty"`
	// @@  .. cpp:var:: ModelRepositoryAgents model_repository_agents
	// @@
	// @@     Optional specification of the agent(s) that should be invoked
	// @@     with repository actions are performed for this model.
	// @@
	ModelRepositoryAgents *ModelRepositoryAgents `protobuf:"bytes,23,opt,name=model_repository_agents,json=modelRepositoryAgents,proto3" json:"model_repository_agents,omitempty"`
	// @@  .. cpp:var:: ModelResponseCache response_cache
	// @@
	// @@     Optional setting for utilizing the response cache for this
	// @@     model.
	// @@
	ResponseCache *ModelResponseCache `protobuf:"bytes,24,opt,name=response_cache,json=responseCache,proto3" json:"response_cache,omitempty"`
}

func (x *ModelConfig) Reset() {
	*x = ModelConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelConfig) ProtoMessage() {}

func (x *ModelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelConfig.ProtoReflect.Descriptor instead.
func (*ModelConfig) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{19}
}

func (x *ModelConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelConfig) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ModelConfig) GetBackend() string {
	if x != nil {
		return x.Backend
	}
	return ""
}

func (x *ModelConfig) GetVersionPolicy() *ModelVersionPolicy {
	if x != nil {
		return x.VersionPolicy
	}
	return nil
}

func (x *ModelConfig) GetMaxBatchSize() int32 {
	if x != nil {
		return x.MaxBatchSize
	}
	return 0
}

func (x *ModelConfig) GetInput() []*ModelInput {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *ModelConfig) GetOutput() []*ModelOutput {
	if x != nil {
		return x.Output
	}
	return nil
}

func (x *ModelConfig) GetBatchInput() []*BatchInput {
	if x != nil {
		return x.BatchInput
	}
	return nil
}

func (x *ModelConfig) GetBatchOutput() []*BatchOutput {
	if x != nil {
		return x.BatchOutput
	}
	return nil
}

func (x *ModelConfig) GetOptimization() *ModelOptimizationPolicy {
	if x != nil {
		return x.Optimization
	}
	return nil
}

func (m *ModelConfig) GetSchedulingChoice() isModelConfig_SchedulingChoice {
	if m != nil {
		return m.SchedulingChoice
	}
	return nil
}

func (x *ModelConfig) GetDynamicBatching() *ModelDynamicBatching {
	if x, ok := x.GetSchedulingChoice().(*ModelConfig_DynamicBatching); ok {
		return x.DynamicBatching
	}
	return nil
}

func (x *ModelConfig) GetSequenceBatching() *ModelSequenceBatching {
	if x, ok := x.GetSchedulingChoice().(*ModelConfig_SequenceBatching); ok {
		return x.SequenceBatching
	}
	return nil
}

func (x *ModelConfig) GetEnsembleScheduling() *ModelEnsembling {
	if x, ok := x.GetSchedulingChoice().(*ModelConfig_EnsembleScheduling); ok {
		return x.EnsembleScheduling
	}
	return nil
}

func (x *ModelConfig) GetInstanceGroup() []*ModelInstanceGroup {
	if x != nil {
		return x.InstanceGroup
	}
	return nil
}

func (x *ModelConfig) GetDefaultModelFilename() string {
	if x != nil {
		return x.DefaultModelFilename
	}
	return ""
}

func (x *ModelConfig) GetCcModelFilenames() map[string]string {
	if x != nil {
		return x.CcModelFilenames
	}
	return nil
}

func (x *ModelConfig) GetMetricTags() map[string]string {
	if x != nil {
		return x.MetricTags
	}
	return nil
}

func (x *ModelConfig) GetParameters() map[string]*ModelParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ModelConfig) GetModelWarmup() []*ModelWarmup {
	if x != nil {
		return x.ModelWarmup
	}
	return nil
}

func (x *ModelConfig) GetModelOperations() *ModelOperations {
	if x != nil {
		return x.ModelOperations
	}
	return nil
}

func (x *ModelConfig) GetModelTransactionPolicy() *ModelTransactionPolicy {
	if x != nil {
		return x.ModelTransactionPolicy
	}
	return nil
}

func (x *ModelConfig) GetModelRepositoryAgents() *ModelRepositoryAgents {
	if x != nil {
		return x.ModelRepositoryAgents
	}
	return nil
}

func (x *ModelConfig) GetResponseCache() *ModelResponseCache {
	if x != nil {
		return x.ResponseCache
	}
	return nil
}

type isModelConfig_SchedulingChoice interface {
	isModelConfig_SchedulingChoice()
}

type ModelConfig_DynamicBatching struct {
	// @@    .. cpp:var:: ModelDynamicBatching dynamic_batching
	// @@
	// @@       If specified, enables the dynamic-batching scheduling
	// @@       policy. With dynamic-batching the scheduler may group
	// @@       together independent requests into a single batch to
	// @@       improve inference throughput.
	// @@
	DynamicBatching *ModelDynamicBatching `protobuf:"bytes,11,opt,name=dynamic_batching,json=dynamicBatching,proto3,oneof"`
}

type ModelConfig_SequenceBatching struct {
	// @@    .. cpp:var:: ModelSequenceBatching sequence_batching
	// @@
	// @@       If specified, enables the sequence-batching scheduling
	// @@       policy. With sequence-batching, inference requests
	// @@       with the same correlation ID are routed to the same
	// @@       model instance. Multiple sequences of inference requests
	// @@       may be batched together into a single batch to
	// @@       improve inference throughput.
	// @@
	SequenceBatching *ModelSequenceBatching `protobuf:"bytes,13,opt,name=sequence_batching,json=sequenceBatching,proto3,oneof"`
}

type ModelConfig_EnsembleScheduling struct {
	// @@    .. cpp:var:: ModelEnsembling ensemble_scheduling
	// @@
	// @@       If specified, enables the model-ensembling scheduling
	// @@       policy. With model-ensembling, inference requests
	// @@       will be processed according to the specification, such as an
	// @@       execution sequence of models. The input specified in this model
	// @@       config will be the input for the ensemble, and the output
	// @@       specified will be the output of the ensemble.
	// @@
	EnsembleScheduling *ModelEnsembling `protobuf:"bytes,15,opt,name=ensemble_scheduling,json=ensembleScheduling,proto3,oneof"`
}

func (*ModelConfig_DynamicBatching) isModelConfig_SchedulingChoice() {}

func (*ModelConfig_SequenceBatching) isModelConfig_SchedulingChoice() {}

func (*ModelConfig_EnsembleScheduling) isModelConfig_SchedulingChoice() {}

// @@  .. cpp:var:: message Resource
// @@
// @@     The resource property.
// @@
type ModelRateLimiter_Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string name
	// @@
	// @@     The name associated with the resource.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@  .. cpp:var:: bool global
	// @@
	// @@     Whether or not the resource is global. If true then the resource
	// @@     is assumed to be shared among the devices otherwise specified
	// @@     count of the resource is assumed for each device associated
	// @@     with the instance.
	// @@
	Global bool `protobuf:"varint,2,opt,name=global,proto3" json:"global,omitempty"`
	// @@  .. cpp:var:: uint32 count
	// @@
	// @@     The number of resources required for the execution of the model
	// @@     instance.
	// @@
	Count uint32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ModelRateLimiter_Resource) Reset() {
	*x = ModelRateLimiter_Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRateLimiter_Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRateLimiter_Resource) ProtoMessage() {}

func (x *ModelRateLimiter_Resource) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRateLimiter_Resource.ProtoReflect.Descriptor instead.
func (*ModelRateLimiter_Resource) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ModelRateLimiter_Resource) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelRateLimiter_Resource) GetGlobal() bool {
	if x != nil {
		return x.Global
	}
	return false
}

func (x *ModelRateLimiter_Resource) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// @@
// @@  .. cpp:var:: message SecondaryDevice
// @@
// @@     A secondary device required for a model instance.
// @@
type ModelInstanceGroup_SecondaryDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: SecondaryDeviceKind kind
	// @@
	// @@     The secondary device kind.
	// @@
	Kind ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind `protobuf:"varint,1,opt,name=kind,proto3,enum=inference.ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind" json:"kind,omitempty"`
	// @@  .. cpp:var:: int64 device_id
	// @@
	// @@     Identifier for the secondary device.
	// @@
	DeviceId int64 `protobuf:"varint,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *ModelInstanceGroup_SecondaryDevice) Reset() {
	*x = ModelInstanceGroup_SecondaryDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInstanceGroup_SecondaryDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInstanceGroup_SecondaryDevice) ProtoMessage() {}

func (x *ModelInstanceGroup_SecondaryDevice) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInstanceGroup_SecondaryDevice.ProtoReflect.Descriptor instead.
func (*ModelInstanceGroup_SecondaryDevice) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ModelInstanceGroup_SecondaryDevice) GetKind() ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind {
	if x != nil {
		return x.Kind
	}
	return ModelInstanceGroup_SecondaryDevice_KIND_NVDLA
}

func (x *ModelInstanceGroup_SecondaryDevice) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

// @@  .. cpp:var:: message Latest
// @@
// @@     Serve only the latest version(s) of a model. This is
// @@     the default policy.
// @@
type ModelVersionPolicy_Latest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: uint32 num_versions
	// @@
	// @@       Serve only the 'num_versions' highest-numbered versions. T
	// @@       The default value of 'num_versions' is 1, indicating that by
	// @@       default only the single highest-number version of a
	// @@       model will be served.
	// @@
	NumVersions uint32 `protobuf:"varint,1,opt,name=num_versions,json=numVersions,proto3" json:"num_versions,omitempty"`
}

func (x *ModelVersionPolicy_Latest) Reset() {
	*x = ModelVersionPolicy_Latest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelVersionPolicy_Latest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelVersionPolicy_Latest) ProtoMessage() {}

func (x *ModelVersionPolicy_Latest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelVersionPolicy_Latest.ProtoReflect.Descriptor instead.
func (*ModelVersionPolicy_Latest) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ModelVersionPolicy_Latest) GetNumVersions() uint32 {
	if x != nil {
		return x.NumVersions
	}
	return 0
}

// @@  .. cpp:var:: message All
// @@
// @@     Serve all versions of the model.
// @@
type ModelVersionPolicy_All struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModelVersionPolicy_All) Reset() {
	*x = ModelVersionPolicy_All{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelVersionPolicy_All) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelVersionPolicy_All) ProtoMessage() {}

func (x *ModelVersionPolicy_All) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelVersionPolicy_All.ProtoReflect.Descriptor instead.
func (*ModelVersionPolicy_All) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{7, 1}
}

// @@  .. cpp:var:: message Specific
// @@
// @@     Serve only specific versions of the model.
// @@
type ModelVersionPolicy_Specific struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: int64 versions (repeated)
	// @@
	// @@       The specific versions of the model that will be served.
	// @@
	Versions []int64 `protobuf:"varint,1,rep,packed,name=versions,proto3" json:"versions,omitempty"`
}

func (x *ModelVersionPolicy_Specific) Reset() {
	*x = ModelVersionPolicy_Specific{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelVersionPolicy_Specific) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelVersionPolicy_Specific) ProtoMessage() {}

func (x *ModelVersionPolicy_Specific) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelVersionPolicy_Specific.ProtoReflect.Descriptor instead.
func (*ModelVersionPolicy_Specific) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{7, 2}
}

func (x *ModelVersionPolicy_Specific) GetVersions() []int64 {
	if x != nil {
		return x.Versions
	}
	return nil
}

// @@
// @@  .. cpp:var:: message Graph
// @@
// @@     Enable generic graph optimization of the model. If not specified
// @@     the framework's default level of optimization is used. Supports
// @@     TensorFlow graphdef and savedmodel and Onnx models. For TensorFlow
// @@     causes XLA to be enabled/disabled for the model. For Onnx defaults
// @@     to enabling all optimizations, -1 enables only basic optimizations,
// @@     +1 enables only basic and extended optimizations.
// @@
type ModelOptimizationPolicy_Graph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: int32 level
	// @@
	// @@       The optimization level. Defaults to 0 (zero) if not specified.
	// @@
	// @@         - -1: Disabled
	// @@         -  0: Framework default
	// @@         -  1+: Enable optimization level (greater values indicate
	// @@            higher optimization levels)
	// @@
	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *ModelOptimizationPolicy_Graph) Reset() {
	*x = ModelOptimizationPolicy_Graph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_Graph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_Graph) ProtoMessage() {}

func (x *ModelOptimizationPolicy_Graph) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_Graph.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_Graph) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ModelOptimizationPolicy_Graph) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// @@
// @@  .. cpp:var:: message Cuda
// @@
// @@     CUDA-specific optimization settings.
// @@
type ModelOptimizationPolicy_Cuda struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: bool graphs
	// @@
	// @@       Use CUDA graphs API to capture model operations and execute
	// @@       them more efficiently. Default value is false.
	// @@       Currently only recognized by TensorRT backend.
	// @@
	Graphs bool `protobuf:"varint,1,opt,name=graphs,proto3" json:"graphs,omitempty"`
	// @@    .. cpp:var:: bool busy_wait_events
	// @@
	// @@       Use busy-waiting to synchronize CUDA events to achieve minimum
	// @@       latency from event complete to host thread to be notified, with
	// @@       the cost of high CPU load. Default value is false.
	// @@       Currently only recognized by TensorRT backend.
	// @@
	BusyWaitEvents bool `protobuf:"varint,2,opt,name=busy_wait_events,json=busyWaitEvents,proto3" json:"busy_wait_events,omitempty"`
	// @@    .. cpp:var:: GraphSpec graph_spec (repeated)
	// @@
	// @@       Specification of the CUDA graph to be captured. If not specified
	// @@       and 'graphs' is true, the default CUDA graphs will be captured
	// @@       based on model settings.
	// @@       Currently only recognized by TensorRT backend.
	// @@
	GraphSpec []*ModelOptimizationPolicy_Cuda_GraphSpec `protobuf:"bytes,3,rep,name=graph_spec,json=graphSpec,proto3" json:"graph_spec,omitempty"`
	// @@    .. cpp:var:: bool output_copy_stream
	// @@
	// @@       Uses a CUDA stream separate from the inference stream to copy the
	// @@       output to host. However, be aware that setting this option to
	// @@       true will lead to an increase in the memory consumption of the
	// @@       model as Triton will allocate twice as much GPU memory for its
	// @@       I/O tensor buffers. Default value is false.
	// @@       Currently only recognized by TensorRT backend.
	// @@
	OutputCopyStream bool `protobuf:"varint,4,opt,name=output_copy_stream,json=outputCopyStream,proto3" json:"output_copy_stream,omitempty"`
}

func (x *ModelOptimizationPolicy_Cuda) Reset() {
	*x = ModelOptimizationPolicy_Cuda{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_Cuda) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_Cuda) ProtoMessage() {}

func (x *ModelOptimizationPolicy_Cuda) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_Cuda.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_Cuda) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ModelOptimizationPolicy_Cuda) GetGraphs() bool {
	if x != nil {
		return x.Graphs
	}
	return false
}

func (x *ModelOptimizationPolicy_Cuda) GetBusyWaitEvents() bool {
	if x != nil {
		return x.BusyWaitEvents
	}
	return false
}

func (x *ModelOptimizationPolicy_Cuda) GetGraphSpec() []*ModelOptimizationPolicy_Cuda_GraphSpec {
	if x != nil {
		return x.GraphSpec
	}
	return nil
}

func (x *ModelOptimizationPolicy_Cuda) GetOutputCopyStream() bool {
	if x != nil {
		return x.OutputCopyStream
	}
	return false
}

// @@
// @@  .. cpp:var:: message ExecutionAccelerators
// @@
// @@     Specify the preferred execution accelerators to be used to execute
// @@     the model. Currently only recognized by ONNX Runtime backend and
// @@     TensorFlow backend.
// @@
// @@     For ONNX Runtime backend, it will deploy the model with the execution
// @@     accelerators by priority, the priority is determined based on the
// @@     order that they are set, i.e. the provider at the front has highest
// @@     priority. Overall, the priority will be in the following order:
// @@         <gpu_execution_accelerator> (if instance is on GPU)
// @@         CUDA Execution Provider     (if instance is on GPU)
// @@         <cpu_execution_accelerator>
// @@         Default CPU Execution Provider
// @@
type ModelOptimizationPolicy_ExecutionAccelerators struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: Accelerator gpu_execution_accelerator (repeated)
	// @@
	// @@       The preferred execution provider to be used if the model instance
	// @@       is deployed on GPU.
	// @@
	// @@       For ONNX Runtime backend, possible value is "tensorrt" as name,
	// @@       and no parameters are required.
	// @@
	// @@       For TensorFlow backend, possible values are "tensorrt",
	// @@       "auto_mixed_precision", "gpu_io".
	// @@
	// @@       For "tensorrt", the following parameters can be specified:
	// @@         "precision_mode": The precision used for optimization.
	// @@         Allowed values are "FP32" and "FP16". Default value is "FP32".
	// @@
	// @@         "max_cached_engines": The maximum number of cached TensorRT
	// @@         engines in dynamic TensorRT ops. Default value is 100.
	// @@
	// @@         "minimum_segment_size": The smallest model subgraph that will
	// @@         be considered for optimization by TensorRT. Default value is 3.
	// @@
	// @@         "max_workspace_size_bytes": The maximum GPU memory the model
	// @@         can use temporarily during execution. Default value is 1GB.
	// @@
	// @@       For "auto_mixed_precision", no parameters are required. If set,
	// @@       the model will try to use FP16 for better performance.
	// @@       This optimization can not be set with "tensorrt".
	// @@
	// @@       For "gpu_io", no parameters are required. If set, the model will
	// @@       be executed using TensorFlow Callable API to set input and output
	// @@       tensors in GPU memory if possible, which can reduce data transfer
	// @@       overhead if the model is used in ensemble. However, the Callable
	// @@       object will be created on model creation and it will request all
	// @@       outputs for every model execution, which may impact the
	// @@       performance if a request does not require all outputs. This
	// @@       optimization will only take affect if the model instance is
	// @@       created with KIND_GPU.
	// @@
	GpuExecutionAccelerator []*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator `protobuf:"bytes,1,rep,name=gpu_execution_accelerator,json=gpuExecutionAccelerator,proto3" json:"gpu_execution_accelerator,omitempty"`
	// @@    .. cpp:var:: Accelerator cpu_execution_accelerator (repeated)
	// @@
	// @@       The preferred execution provider to be used if the model instance
	// @@       is deployed on CPU.
	// @@
	// @@       For ONNX Runtime backend, possible value is "openvino" as name,
	// @@       and no parameters are required.
	// @@
	CpuExecutionAccelerator []*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator `protobuf:"bytes,2,rep,name=cpu_execution_accelerator,json=cpuExecutionAccelerator,proto3" json:"cpu_execution_accelerator,omitempty"`
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators) Reset() {
	*x = ModelOptimizationPolicy_ExecutionAccelerators{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_ExecutionAccelerators) ProtoMessage() {}

func (x *ModelOptimizationPolicy_ExecutionAccelerators) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_ExecutionAccelerators.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_ExecutionAccelerators) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 2}
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators) GetGpuExecutionAccelerator() []*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator {
	if x != nil {
		return x.GpuExecutionAccelerator
	}
	return nil
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators) GetCpuExecutionAccelerator() []*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator {
	if x != nil {
		return x.CpuExecutionAccelerator
	}
	return nil
}

// @@
// @@  .. cpp:var:: message PinnedMemoryBuffer
// @@
// @@     Specify whether to use a pinned memory buffer when transferring data
// @@     between non-pinned system memory and GPU memory. Using a pinned
// @@     memory buffer for system from/to GPU transfers will typically provide
// @@     increased performance. For example, in the common use case where the
// @@     request provides inputs and delivers outputs via non-pinned system
// @@     memory, if the model instance accepts GPU IOs, the inputs will be
// @@     processed by two copies: from non-pinned system memory to pinned
// @@     memory, and from pinned memory to GPU memory. Similarly, pinned
// @@     memory will be used for delivering the outputs.
// @@
type ModelOptimizationPolicy_PinnedMemoryBuffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: bool enable
	// @@
	// @@       Use pinned memory buffer. Default is true.
	// @@
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *ModelOptimizationPolicy_PinnedMemoryBuffer) Reset() {
	*x = ModelOptimizationPolicy_PinnedMemoryBuffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_PinnedMemoryBuffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_PinnedMemoryBuffer) ProtoMessage() {}

func (x *ModelOptimizationPolicy_PinnedMemoryBuffer) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_PinnedMemoryBuffer.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_PinnedMemoryBuffer) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 3}
}

func (x *ModelOptimizationPolicy_PinnedMemoryBuffer) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

// @@    .. cpp:var:: message GraphSpec
// @@
// @@       Specification of the CUDA graph to be captured.
// @@
type ModelOptimizationPolicy_Cuda_GraphSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@      .. cpp:var:: int32 batch_size
	// @@
	// @@         The batch size of the CUDA graph. If 'max_batch_size' is 0,
	// @@         'batch_size' must be set to 0. Otherwise, 'batch_size' must
	// @@         be set to value between 1 and 'max_batch_size'.
	// @@
	BatchSize int32 `protobuf:"varint,1,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
	// @@      .. cpp:var:: map<string, Shape> input
	// @@
	// @@         The specification of the inputs. 'Shape' is the shape of the
	// @@         input without batching dimension.
	// @@
	Input map[string]*ModelOptimizationPolicy_Cuda_GraphSpec_Shape `protobuf:"bytes,2,rep,name=input,proto3" json:"input,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@      .. cpp:var:: LowerBound graph_lower_bound
	// @@
	// @@         Specify the lower bound of the CUDA graph. Optional.
	// @@         If specified, the graph can be used for input shapes and
	// @@         batch sizes that are in closed interval between the lower
	// @@         bound specification and graph specification. For dynamic
	// @@         shape model, this allows CUDA graphs to be launched
	// @@         frequently without capturing all possible shape combinations.
	// @@         However, using graph for shape combinations different from
	// @@         the one used for capturing introduces uninitialized data for
	// @@         execution and it may distort the inference result if
	// @@         the model is sensitive to uninitialized data.
	// @@
	GraphLowerBound *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound `protobuf:"bytes,3,opt,name=graph_lower_bound,json=graphLowerBound,proto3" json:"graph_lower_bound,omitempty"`
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) Reset() {
	*x = ModelOptimizationPolicy_Cuda_GraphSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_Cuda_GraphSpec) ProtoMessage() {}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_Cuda_GraphSpec.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_Cuda_GraphSpec) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 1, 0}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) GetBatchSize() int32 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) GetInput() map[string]*ModelOptimizationPolicy_Cuda_GraphSpec_Shape {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec) GetGraphLowerBound() *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound {
	if x != nil {
		return x.GraphLowerBound
	}
	return nil
}

// @@      .. cpp:var:: message Dims
// @@
// @@         Specification of tensor dimension.
// @@
type ModelOptimizationPolicy_Cuda_GraphSpec_Shape struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@        .. cpp:var:: int64 dim (repeated)
	// @@
	// @@           The dimension.
	// @@
	Dim []int64 `protobuf:"varint,1,rep,packed,name=dim,proto3" json:"dim,omitempty"`
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_Shape) Reset() {
	*x = ModelOptimizationPolicy_Cuda_GraphSpec_Shape{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_Shape) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_Cuda_GraphSpec_Shape) ProtoMessage() {}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_Shape) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_Cuda_GraphSpec_Shape.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_Cuda_GraphSpec_Shape) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 1, 0, 0}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_Shape) GetDim() []int64 {
	if x != nil {
		return x.Dim
	}
	return nil
}

type ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@      .. cpp:var:: int32 batch_size
	// @@
	// @@         The batch size of the CUDA graph. If 'max_batch_size' is 0,
	// @@         'batch_size' must be set to 0. Otherwise, 'batch_size' must
	// @@         be set to value between 1 and 'max_batch_size'.
	// @@
	BatchSize int32 `protobuf:"varint,1,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
	// @@      .. cpp:var:: map<string, Shape> input
	// @@
	// @@         The specification of the inputs. 'Shape' is the shape of
	// @@         the input without batching dimension.
	// @@
	Input map[string]*ModelOptimizationPolicy_Cuda_GraphSpec_Shape `protobuf:"bytes,2,rep,name=input,proto3" json:"input,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) Reset() {
	*x = ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) ProtoMessage() {}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 1, 0, 1}
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) GetBatchSize() int32 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

func (x *ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound) GetInput() map[string]*ModelOptimizationPolicy_Cuda_GraphSpec_Shape {
	if x != nil {
		return x.Input
	}
	return nil
}

// @@
// @@  .. cpp:var:: message Accelerator
// @@
// @@     Specify the accelerator to be used to execute the model.
// @@     Accelerator with the same name may accept different parameters
// @@     depending on the backends.
// @@
type ModelOptimizationPolicy_ExecutionAccelerators_Accelerator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: string name
	// @@
	// @@       The name of the execution accelerator.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: map<string, string> parameters
	// @@
	// @@       Additional paremeters used to configure the accelerator.
	// @@
	Parameters map[string]string `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) Reset() {
	*x = ModelOptimizationPolicy_ExecutionAccelerators_Accelerator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) ProtoMessage() {}

func (x *ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelOptimizationPolicy_ExecutionAccelerators_Accelerator.ProtoReflect.Descriptor instead.
func (*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{8, 2, 0}
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelOptimizationPolicy_ExecutionAccelerators_Accelerator) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

// @@  .. cpp:var:: message Control
// @@
// @@     A control is a signal that the sequence batcher uses to
// @@     communicate with a backend.
// @@
type ModelSequenceBatching_Control struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: Kind kind
	// @@
	// @@       The kind of this control.
	// @@
	Kind ModelSequenceBatching_Control_Kind `protobuf:"varint,1,opt,name=kind,proto3,enum=inference.ModelSequenceBatching_Control_Kind" json:"kind,omitempty"`
	// @@    .. cpp:var:: int32 int32_false_true (repeated)
	// @@
	// @@       The control's true and false setting is indicated by setting
	// @@       a value in an int32 tensor. The tensor must be a
	// @@       1-dimensional tensor with size equal to the batch size of
	// @@       the request. 'int32_false_true' must have two entries: the
	// @@       first the false value and the second the true value.
	// @@
	Int32FalseTrue []int32 `protobuf:"varint,2,rep,packed,name=int32_false_true,json=int32FalseTrue,proto3" json:"int32_false_true,omitempty"`
	// @@    .. cpp:var:: float fp32_false_true (repeated)
	// @@
	// @@       The control's true and false setting is indicated by setting
	// @@       a value in a fp32 tensor. The tensor must be a
	// @@       1-dimensional tensor with size equal to the batch size of
	// @@       the request. 'fp32_false_true' must have two entries: the
	// @@       first the false value and the second the true value.
	// @@
	Fp32FalseTrue []float32 `protobuf:"fixed32,3,rep,packed,name=fp32_false_true,json=fp32FalseTrue,proto3" json:"fp32_false_true,omitempty"`
	// @@    .. cpp:var:: bool bool_false_true (repeated)
	// @@
	// @@       The control's true and false setting is indicated by setting
	// @@       a value in a bool tensor. The tensor must be a
	// @@       1-dimensional tensor with size equal to the batch size of
	// @@       the request. 'bool_false_true' must have two entries: the
	// @@       first the false value and the second the true value.
	// @@
	BoolFalseTrue []bool `protobuf:"varint,5,rep,packed,name=bool_false_true,json=boolFalseTrue,proto3" json:"bool_false_true,omitempty"`
	// @@    .. cpp:var:: DataType data_type
	// @@
	// @@       The control's datatype.
	// @@
	DataType DataType `protobuf:"varint,4,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
}

func (x *ModelSequenceBatching_Control) Reset() {
	*x = ModelSequenceBatching_Control{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_Control) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_Control) ProtoMessage() {}

func (x *ModelSequenceBatching_Control) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_Control.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_Control) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ModelSequenceBatching_Control) GetKind() ModelSequenceBatching_Control_Kind {
	if x != nil {
		return x.Kind
	}
	return ModelSequenceBatching_Control_CONTROL_SEQUENCE_START
}

func (x *ModelSequenceBatching_Control) GetInt32FalseTrue() []int32 {
	if x != nil {
		return x.Int32FalseTrue
	}
	return nil
}

func (x *ModelSequenceBatching_Control) GetFp32FalseTrue() []float32 {
	if x != nil {
		return x.Fp32FalseTrue
	}
	return nil
}

func (x *ModelSequenceBatching_Control) GetBoolFalseTrue() []bool {
	if x != nil {
		return x.BoolFalseTrue
	}
	return nil
}

func (x *ModelSequenceBatching_Control) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

// @@  .. cpp:var:: message ControlInput
// @@
// @@     The sequence control values to communicate by a model input.
// @@
type ModelSequenceBatching_ControlInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: string name
	// @@
	// @@       The name of the model input.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: Control control (repeated)
	// @@
	// @@       The control value(s) that should be communicated to the
	// @@       model using this model input.
	// @@
	Control []*ModelSequenceBatching_Control `protobuf:"bytes,2,rep,name=control,proto3" json:"control,omitempty"`
}

func (x *ModelSequenceBatching_ControlInput) Reset() {
	*x = ModelSequenceBatching_ControlInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_ControlInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_ControlInput) ProtoMessage() {}

func (x *ModelSequenceBatching_ControlInput) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_ControlInput.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_ControlInput) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 1}
}

func (x *ModelSequenceBatching_ControlInput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelSequenceBatching_ControlInput) GetControl() []*ModelSequenceBatching_Control {
	if x != nil {
		return x.Control
	}
	return nil
}

// @@
// @@  .. cpp:var:: message InitialState
// @@
// @@     Settings used to initialize data for implicit state.
// @@
type ModelSequenceBatching_InitialState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@      .. cpp:var:: DataType data_type
	// @@
	// @@         The data-type of the state.
	// @@
	DataType DataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@      .. cpp:var:: int64 dims (repeated)
	// @@
	// @@         The shape of the state tensor, not including the batch dimension.
	// @@
	Dims []int64 `protobuf:"varint,2,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// @@      .. cpp:var:: oneof state_data
	// @@
	// @@         Specify how the initial state data is generated.
	// @@
	//
	// Types that are assignable to StateData:
	//
	//	*ModelSequenceBatching_InitialState_ZeroData
	//	*ModelSequenceBatching_InitialState_DataFile
	StateData isModelSequenceBatching_InitialState_StateData `protobuf_oneof:"state_data"`
	// @@  .. cpp:var:: string name
	// @@
	// @@     The name of the state initialization.
	// @@
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ModelSequenceBatching_InitialState) Reset() {
	*x = ModelSequenceBatching_InitialState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_InitialState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_InitialState) ProtoMessage() {}

func (x *ModelSequenceBatching_InitialState) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_InitialState.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_InitialState) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 2}
}

func (x *ModelSequenceBatching_InitialState) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *ModelSequenceBatching_InitialState) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (m *ModelSequenceBatching_InitialState) GetStateData() isModelSequenceBatching_InitialState_StateData {
	if m != nil {
		return m.StateData
	}
	return nil
}

func (x *ModelSequenceBatching_InitialState) GetZeroData() bool {
	if x, ok := x.GetStateData().(*ModelSequenceBatching_InitialState_ZeroData); ok {
		return x.ZeroData
	}
	return false
}

func (x *ModelSequenceBatching_InitialState) GetDataFile() string {
	if x, ok := x.GetStateData().(*ModelSequenceBatching_InitialState_DataFile); ok {
		return x.DataFile
	}
	return ""
}

func (x *ModelSequenceBatching_InitialState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type isModelSequenceBatching_InitialState_StateData interface {
	isModelSequenceBatching_InitialState_StateData()
}

type ModelSequenceBatching_InitialState_ZeroData struct {
	// @@
	// @@      .. cpp:var:: bool zero_data
	// @@
	// @@         The identifier for using zeros as initial state data.
	// @@         Note that the value of 'zero_data' will not be checked,
	// @@         instead, zero data will be used as long as the field is set.
	// @@
	ZeroData bool `protobuf:"varint,3,opt,name=zero_data,json=zeroData,proto3,oneof"`
}

type ModelSequenceBatching_InitialState_DataFile struct {
	// @@      .. cpp:var:: string data_file
	// @@
	// @@         The file whose content will be used as the initial data for
	// @@         the state in row-major order. The file must be provided in
	// @@         sub-directory 'initial_state' under the model directory.
	// @@
	DataFile string `protobuf:"bytes,4,opt,name=data_file,json=dataFile,proto3,oneof"`
}

func (*ModelSequenceBatching_InitialState_ZeroData) isModelSequenceBatching_InitialState_StateData() {
}

func (*ModelSequenceBatching_InitialState_DataFile) isModelSequenceBatching_InitialState_StateData() {
}

// @@  .. cpp:var:: message State
// @@
// @@     An input / output pair of tensors that carry state for the sequence.
// @@
type ModelSequenceBatching_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: string input_name
	// @@
	// @@       The name of the model state input.
	// @@
	InputName string `protobuf:"bytes,1,opt,name=input_name,json=inputName,proto3" json:"input_name,omitempty"`
	// @@    .. cpp:var:: string output_name
	// @@
	// @@       The name of the model state output.
	// @@
	OutputName string `protobuf:"bytes,2,opt,name=output_name,json=outputName,proto3" json:"output_name,omitempty"`
	// @@    .. cpp:var:: DataType data_type
	// @@
	// @@       The data-type of the state.
	// @@
	DataType DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@    .. cpp:var:: int64 dim (repeated)
	// @@
	// @@       The dimension.
	// @@
	Dims []int64 `protobuf:"varint,4,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// @@  .. cpp:var:: InitialState initial_state (repeated)
	// @@
	// @@     The optional field to specify the initial state for the model.
	// @@
	InitialState []*ModelSequenceBatching_InitialState `protobuf:"bytes,5,rep,name=initial_state,json=initialState,proto3" json:"initial_state,omitempty"`
}

func (x *ModelSequenceBatching_State) Reset() {
	*x = ModelSequenceBatching_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_State) ProtoMessage() {}

func (x *ModelSequenceBatching_State) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_State.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_State) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 3}
}

func (x *ModelSequenceBatching_State) GetInputName() string {
	if x != nil {
		return x.InputName
	}
	return ""
}

func (x *ModelSequenceBatching_State) GetOutputName() string {
	if x != nil {
		return x.OutputName
	}
	return ""
}

func (x *ModelSequenceBatching_State) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *ModelSequenceBatching_State) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (x *ModelSequenceBatching_State) GetInitialState() []*ModelSequenceBatching_InitialState {
	if x != nil {
		return x.InitialState
	}
	return nil
}

// @@  .. cpp:var:: message StrategyDirect
// @@
// @@     The sequence batcher uses a specific, unique batch
// @@     slot for each sequence. All inference requests in a
// @@     sequence are directed to the same batch slot in the same
// @@     model instance over the lifetime of the sequence. This
// @@     is the default strategy.
// @@
type ModelSequenceBatching_StrategyDirect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: uint64 max_queue_delay_microseconds
	// @@
	// @@       The maximum time, in microseconds, a candidate request
	// @@       will be delayed in the sequence batch scheduling queue to
	// @@       wait for additional requests for batching. Default is 0.
	// @@
	MaxQueueDelayMicroseconds uint64 `protobuf:"varint,1,opt,name=max_queue_delay_microseconds,json=maxQueueDelayMicroseconds,proto3" json:"max_queue_delay_microseconds,omitempty"`
	// @@    .. cpp:var:: float minimum_slot_utilization
	// @@
	// @@       The minimum slot utilization that must be satisfied to
	// @@       execute the batch before 'max_queue_delay_microseconds' expires.
	// @@       For example, a value of 0.5 indicates that the batch should be
	// @@       executed as soon as 50% or more of the slots are ready even if
	// @@       the 'max_queue_delay_microseconds' timeout has not expired.
	// @@       The default is 0.0, indicating that a batch will be executed
	// @@       before 'max_queue_delay_microseconds' timeout expires if at least
	// @@       one batch slot is ready. 'max_queue_delay_microseconds' will be
	// @@       ignored unless minimum_slot_utilization is set to a non-zero
	// @@       value.
	// @@
	MinimumSlotUtilization float32 `protobuf:"fixed32,2,opt,name=minimum_slot_utilization,json=minimumSlotUtilization,proto3" json:"minimum_slot_utilization,omitempty"`
}

func (x *ModelSequenceBatching_StrategyDirect) Reset() {
	*x = ModelSequenceBatching_StrategyDirect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_StrategyDirect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_StrategyDirect) ProtoMessage() {}

func (x *ModelSequenceBatching_StrategyDirect) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_StrategyDirect.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_StrategyDirect) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 4}
}

func (x *ModelSequenceBatching_StrategyDirect) GetMaxQueueDelayMicroseconds() uint64 {
	if x != nil {
		return x.MaxQueueDelayMicroseconds
	}
	return 0
}

func (x *ModelSequenceBatching_StrategyDirect) GetMinimumSlotUtilization() float32 {
	if x != nil {
		return x.MinimumSlotUtilization
	}
	return 0
}

// @@  .. cpp:var:: message StrategyOldest
// @@
// @@     The sequence batcher maintains up to 'max_candidate_sequences'
// @@     candidate sequences. 'max_candidate_sequences' can be greater
// @@     than the model's 'max_batch_size'. For inferencing the batcher
// @@     chooses from the candidate sequences up to 'max_batch_size'
// @@     inference requests. Requests are chosen in an oldest-first
// @@     manner across all candidate sequences. A given sequence is
// @@     not guaranteed to be assigned to the same batch slot for
// @@     all inference requests of that sequence.
// @@
type ModelSequenceBatching_StrategyOldest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: int32 max_candidate_sequences
	// @@
	// @@       Maximum number of candidate sequences that the batcher
	// @@       maintains. Excess seqences are kept in an ordered backlog
	// @@       and become candidates when existing candidate sequences
	// @@       complete.
	// @@
	MaxCandidateSequences int32 `protobuf:"varint,1,opt,name=max_candidate_sequences,json=maxCandidateSequences,proto3" json:"max_candidate_sequences,omitempty"`
	// @@    .. cpp:var:: int32 preferred_batch_size (repeated)
	// @@
	// @@       Preferred batch sizes for dynamic batching of candidate
	// @@       sequences. If a batch of one of these sizes can be formed
	// @@       it will be executed immediately. If not specified a
	// @@       preferred batch size will be chosen automatically
	// @@       based on model and GPU characteristics.
	// @@
	PreferredBatchSize []int32 `protobuf:"varint,2,rep,packed,name=preferred_batch_size,json=preferredBatchSize,proto3" json:"preferred_batch_size,omitempty"`
	// @@    .. cpp:var:: uint64 max_queue_delay_microseconds
	// @@
	// @@       The maximum time, in microseconds, a candidate request
	// @@       will be delayed in the dynamic batch scheduling queue to
	// @@       wait for additional requests for batching. Default is 0.
	// @@
	MaxQueueDelayMicroseconds uint64 `protobuf:"varint,3,opt,name=max_queue_delay_microseconds,json=maxQueueDelayMicroseconds,proto3" json:"max_queue_delay_microseconds,omitempty"`
}

func (x *ModelSequenceBatching_StrategyOldest) Reset() {
	*x = ModelSequenceBatching_StrategyOldest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSequenceBatching_StrategyOldest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSequenceBatching_StrategyOldest) ProtoMessage() {}

func (x *ModelSequenceBatching_StrategyOldest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSequenceBatching_StrategyOldest.ProtoReflect.Descriptor instead.
func (*ModelSequenceBatching_StrategyOldest) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{11, 5}
}

func (x *ModelSequenceBatching_StrategyOldest) GetMaxCandidateSequences() int32 {
	if x != nil {
		return x.MaxCandidateSequences
	}
	return 0
}

func (x *ModelSequenceBatching_StrategyOldest) GetPreferredBatchSize() []int32 {
	if x != nil {
		return x.PreferredBatchSize
	}
	return nil
}

func (x *ModelSequenceBatching_StrategyOldest) GetMaxQueueDelayMicroseconds() uint64 {
	if x != nil {
		return x.MaxQueueDelayMicroseconds
	}
	return 0
}

// @@  .. cpp:var:: message Step
// @@
// @@     Each step specifies a model included in the ensemble,
// @@     maps ensemble tensor names to the model input tensors,
// @@     and maps model output tensors to ensemble tensor names
// @@
type ModelEnsembling_Step struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@  .. cpp:var:: string model_name
	// @@
	// @@     The name of the model to execute for this step of the ensemble.
	// @@
	ModelName string `protobuf:"bytes,1,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// @@  .. cpp:var:: int64 model_version
	// @@
	// @@     The version of the model to use for inference. If -1
	// @@     the latest/most-recent version of the model is used.
	// @@
	ModelVersion int64 `protobuf:"varint,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// @@  .. cpp:var:: map<string,string> input_map
	// @@
	// @@     Map from name of an input tensor on this step's model to ensemble
	// @@     tensor name. The ensemble tensor must have the same data type and
	// @@     shape as the model input. Each model input must be assigned to
	// @@     one ensemble tensor, but the same ensemble tensor can be assigned
	// @@     to multiple model inputs.
	// @@
	InputMap map[string]string `protobuf:"bytes,3,rep,name=input_map,json=inputMap,proto3" json:"input_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @@  .. cpp:var:: map<string,string> output_map
	// @@
	// @@     Map from name of an output tensor on this step's model to ensemble
	// @@     tensor name. The data type and shape of the ensemble tensor will
	// @@     be inferred from the model output. It is optional to assign all
	// @@     model outputs to ensemble tensors. One ensemble tensor name
	// @@     can appear in an output map only once.
	// @@
	OutputMap map[string]string `protobuf:"bytes,4,rep,name=output_map,json=outputMap,proto3" json:"output_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelEnsembling_Step) Reset() {
	*x = ModelEnsembling_Step{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelEnsembling_Step) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelEnsembling_Step) ProtoMessage() {}

func (x *ModelEnsembling_Step) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelEnsembling_Step.ProtoReflect.Descriptor instead.
func (*ModelEnsembling_Step) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ModelEnsembling_Step) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelEnsembling_Step) GetModelVersion() int64 {
	if x != nil {
		return x.ModelVersion
	}
	return 0
}

func (x *ModelEnsembling_Step) GetInputMap() map[string]string {
	if x != nil {
		return x.InputMap
	}
	return nil
}

func (x *ModelEnsembling_Step) GetOutputMap() map[string]string {
	if x != nil {
		return x.OutputMap
	}
	return nil
}

// @@
// @@  .. cpp:var:: message Input
// @@
// @@     Meta data associated with an input.
// @@
type ModelWarmup_Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: DataType data_type
	// @@
	// @@       The data-type of the input.
	// @@
	DataType DataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=inference.DataType" json:"data_type,omitempty"`
	// @@    .. cpp:var:: int64 dims (repeated)
	// @@
	// @@       The shape of the input tensor, not including the batch dimension.
	// @@
	Dims []int64 `protobuf:"varint,2,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// @@    .. cpp:var:: oneof input_data_type
	// @@
	// @@       Specify how the input data is generated. If the input has STRING
	// @@       data type and 'random_data' is set, the data generation will fall
	// @@       back to 'zero_data'.
	// @@
	//
	// Types that are assignable to InputDataType:
	//
	//	*ModelWarmup_Input_ZeroData
	//	*ModelWarmup_Input_RandomData
	//	*ModelWarmup_Input_InputDataFile
	InputDataType isModelWarmup_Input_InputDataType `protobuf_oneof:"input_data_type"`
}

func (x *ModelWarmup_Input) Reset() {
	*x = ModelWarmup_Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelWarmup_Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelWarmup_Input) ProtoMessage() {}

func (x *ModelWarmup_Input) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelWarmup_Input.ProtoReflect.Descriptor instead.
func (*ModelWarmup_Input) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ModelWarmup_Input) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_TYPE_INVALID
}

func (x *ModelWarmup_Input) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (m *ModelWarmup_Input) GetInputDataType() isModelWarmup_Input_InputDataType {
	if m != nil {
		return m.InputDataType
	}
	return nil
}

func (x *ModelWarmup_Input) GetZeroData() bool {
	if x, ok := x.GetInputDataType().(*ModelWarmup_Input_ZeroData); ok {
		return x.ZeroData
	}
	return false
}

func (x *ModelWarmup_Input) GetRandomData() bool {
	if x, ok := x.GetInputDataType().(*ModelWarmup_Input_RandomData); ok {
		return x.RandomData
	}
	return false
}

func (x *ModelWarmup_Input) GetInputDataFile() string {
	if x, ok := x.GetInputDataType().(*ModelWarmup_Input_InputDataFile); ok {
		return x.InputDataFile
	}
	return ""
}

type isModelWarmup_Input_InputDataType interface {
	isModelWarmup_Input_InputDataType()
}

type ModelWarmup_Input_ZeroData struct {
	// @@
	// @@    .. cpp:var:: bool zero_data
	// @@
	// @@       The identifier for using zeros as input data. Note that the
	// @@       value of 'zero_data' will not be checked, instead, zero data
	// @@       will be used as long as the field is set.
	// @@
	ZeroData bool `protobuf:"varint,3,opt,name=zero_data,json=zeroData,proto3,oneof"`
}

type ModelWarmup_Input_RandomData struct {
	// @@
	// @@    .. cpp:var:: bool random_data
	// @@
	// @@       The identifier for using random data as input data. Note that
	// @@       the value of 'random_data' will not be checked, instead,
	// @@       random data will be used as long as the field is set.
	// @@
	RandomData bool `protobuf:"varint,4,opt,name=random_data,json=randomData,proto3,oneof"`
}

type ModelWarmup_Input_InputDataFile struct {
	// @@    .. cpp:var:: string input_data_file
	// @@
	// @@       The file whose content will be used as raw input data in
	// @@       row-major order. The file must be provided in a sub-directory
	// @@       'warmup' under the model directory.
	// @@
	InputDataFile string `protobuf:"bytes,5,opt,name=input_data_file,json=inputDataFile,proto3,oneof"`
}

func (*ModelWarmup_Input_ZeroData) isModelWarmup_Input_InputDataType() {}

func (*ModelWarmup_Input_RandomData) isModelWarmup_Input_InputDataType() {}

func (*ModelWarmup_Input_InputDataFile) isModelWarmup_Input_InputDataType() {}

// @@
// @@  .. cpp:var:: message Agent
// @@
// @@     A repository agent that should be invoked for the specified
// @@     repository actions for this model.
// @@
type ModelRepositoryAgents_Agent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @@    .. cpp:var:: string name
	// @@
	// @@       The name of the agent.
	// @@
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// @@    .. cpp:var:: map<string, string> parameters
	// @@
	// @@       The parameters for the agent.
	// @@
	Parameters map[string]string `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelRepositoryAgents_Agent) Reset() {
	*x = ModelRepositoryAgents_Agent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_model_config_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRepositoryAgents_Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRepositoryAgents_Agent) ProtoMessage() {}

func (x *ModelRepositoryAgents_Agent) ProtoReflect() protoreflect.Message {
	mi := &file_pb_model_config_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRepositoryAgents_Agent.ProtoReflect.Descriptor instead.
func (*ModelRepositoryAgents_Agent) Descriptor() ([]byte, []int) {
	return file_pb_model_config_proto_rawDescGZIP(), []int{17, 0}
}

func (x *ModelRepositoryAgents_Agent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelRepositoryAgents_Agent) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

var File_pb_model_config_proto protoreflect.FileDescriptor

var file_pb_model_config_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x62, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x1a, 0x4c, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xed, 0x04, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x36, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x4b, 0x69,
	0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x0c, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65,
	0x72, 0x52, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x67, 0x70, 0x75, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x67, 0x70,
	0x75, 0x73, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x73, 0x73,
	0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x61, 0x73, 0x73, 0x69,
	0x76, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x1a, 0xac, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x25, 0x0a, 0x13, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x0a, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x56, 0x44, 0x4c, 0x41,
	0x10, 0x00, 0x22, 0x41, 0x0a, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x0d, 0x0a, 0x09, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x47, 0x50, 0x55, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x49, 0x4e, 0x44, 0x5f,
	0x43, 0x50, 0x55, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x10, 0x03, 0x22, 0x2a, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x68, 0x61, 0x70, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70,
	0x65, 0x22, 0x84, 0x03, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x2e, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x69, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73,
	0x12, 0x37, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x53, 0x68, 0x61, 0x70, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x61, 0x67, 0x67, 0x65,
	0x64, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x67, 0x67, 0x65, 0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x06, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x5f, 0x4e, 0x48, 0x57, 0x43, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x4d, 0x41,
	0x54, 0x5f, 0x4e, 0x43, 0x48, 0x57, 0x10, 0x02, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x09,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69,
	0x6d, 0x73, 0x12, 0x37, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x68, 0x61,
	0x70, 0x65, 0x52, 0x07, 0x72, 0x65, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x5f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x22, 0x82, 0x03, 0x0a, 0x0a, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x2e, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x22,
	0xcd, 0x01, 0x0a, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x00, 0x12, 0x23, 0x0a, 0x1f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x4d,
	0x55, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x41, 0x43, 0x43, 0x55, 0x4d, 0x55, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x45, 0x4c, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x5a,
	0x45, 0x52, 0x4f, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d,
	0x41, 0x58, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x41, 0x53, 0x5f, 0x53, 0x48, 0x41, 0x50, 0x45, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x42,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x48, 0x41, 0x50, 0x45, 0x10,
	0x04, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x53, 0x48, 0x41, 0x50, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x54, 0x54, 0x45, 0x4e, 0x10, 0x05, 0x22,
	0xae, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2f, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x2e, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x22, 0x2a, 0x0a, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x22, 0x0a, 0x1e,
	0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x41, 0x54, 0x54, 0x45, 0x52, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x53, 0x48, 0x41, 0x50, 0x45, 0x10, 0x00,
	0x22, 0xbe, 0x02, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3e, 0x0a, 0x06, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x06, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x03, 0x61, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x2e, 0x41, 0x6c, 0x6c, 0x48, 0x00, 0x52, 0x03, 0x61, 0x6c, 0x6c, 0x12, 0x44,
	0x0a, 0x08, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x48, 0x00, 0x52, 0x08, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x1a, 0x2b, 0x0a, 0x06, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x05, 0x0a, 0x03, 0x41, 0x6c, 0x6c, 0x1a, 0x26, 0x0a, 0x08, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x42, 0x0f, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x22, 0xe6, 0x10, 0x0a, 0x17, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3e, 0x0a,
	0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x4c, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x04, 0x63,
	0x75, 0x64, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x43, 0x75,
	0x64, 0x61, 0x52, 0x04, 0x63, 0x75, 0x64, 0x61, 0x12, 0x6f, 0x0a, 0x16, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x15, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x65, 0x0a, 0x13, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x5f, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x50, 0x69, 0x6e, 0x6e, 0x65,
	0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x52, 0x11, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x12, 0x67, 0x0a, 0x14, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x69, 0x6e, 0x6e, 0x65,
	0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x2e, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x42,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x52, 0x12, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x69, 0x6e,
	0x6e, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x1e, 0x67, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x62, 0x75, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x1b, 0x67, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x42,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x65, 0x61, 0x67, 0x65, 0x72, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x1a, 0x1d, 0x0a, 0x05, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x1a, 0xc1, 0x06, 0x0a, 0x04, 0x43, 0x75, 0x64, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x79, 0x5f, 0x77, 0x61,
	0x69, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x62, 0x75, 0x73, 0x79, 0x57, 0x61, 0x69, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x50, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x53, 0x70, 0x65, 0x63, 0x52, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x63, 0x6f, 0x70, 0x79,
	0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x1a,
	0xf6, 0x04, 0x0a, 0x09, 0x47, 0x72, 0x61, 0x70, 0x68, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x52, 0x0a, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e,
	0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x12, 0x68, 0x0a, 0x11, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x62, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e,
	0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x4c,
	0x6f, 0x77, 0x65, 0x72, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x0f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x4c, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x1a, 0x19, 0x0a, 0x05, 0x53, 0x68,
	0x61, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x03, 0x64, 0x69, 0x6d, 0x1a, 0xfd, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x42,
	0x6f, 0x75, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x5d, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x47, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x6f, 0x75, 0x6e, 0x64,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x1a, 0x71, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x4d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x53, 0x70, 0x65, 0x63, 0x2e, 0x53, 0x68, 0x61, 0x70, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x71, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x43, 0x75, 0x64, 0x61, 0x2e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x53, 0x68, 0x61, 0x70, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xf6, 0x03, 0x0a, 0x15, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x67, 0x70, 0x75, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x17, 0x67, 0x70,
	0x75, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x63, 0x70, 0x75, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x17, 0x63, 0x70, 0x75, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0xd6, 0x01, 0x0a, 0x0b, 0x41, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x74, 0x0a, 0x0a,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x54, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63,
	0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x2c, 0x0a, 0x12, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0x49, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x14, 0x0a, 0x10, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x46,
	0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x49, 0x4f,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x02, 0x22, 0xaa, 0x02, 0x0a, 0x10, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x50, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x40, 0x0a, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x1a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78,
	0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0x26, 0x0a, 0x0d, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x44, 0x45, 0x4c, 0x41, 0x59, 0x10, 0x01, 0x22, 0xb7, 0x04, 0x0a, 0x14, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x12,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x6d, 0x61, 0x78, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19, 0x6d, 0x61, 0x78, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x4d, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x6c,
	0x0a, 0x15, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x63, 0x0a, 0x18,
	0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xd8, 0x0c, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x49, 0x0a, 0x06, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x49, 0x0a, 0x06, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x6f, 0x6c, 0x64, 0x65, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x1e, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x6c, 0x65, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x1b, 0x6d, 0x61, 0x78, 0x53, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x6c, 0x65, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x1a, 0xef, 0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x41, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4b, 0x69, 0x6e,
	0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x74, 0x72, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x54, 0x72, 0x75,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x70, 0x33, 0x32, 0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f,
	0x74, 0x72, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x66, 0x70, 0x33, 0x32,
	0x46, 0x61, 0x6c, 0x73, 0x65, 0x54, 0x72, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x6f, 0x6f,
	0x6c, 0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x74, 0x72, 0x75, 0x65, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x08, 0x52, 0x0d, 0x62, 0x6f, 0x6f, 0x6c, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x54, 0x72, 0x75,
	0x65, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x75, 0x0a, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x16, 0x43,
	0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x53, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x4e, 0x54, 0x52,
	0x4f, 0x4c, 0x5f, 0x53, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x53,
	0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x53, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x49, 0x44, 0x10, 0x03, 0x1a, 0x66, 0x0a, 0x0c, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x1a, 0xb4, 0x01, 0x0a, 0x0c, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x09, 0x7a, 0x65, 0x72,
	0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08,
	0x7a, 0x65, 0x72, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xe1, 0x01, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x1a, 0x8b, 0x01,
	0x0a, 0x0e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x12, 0x3f, 0x0a, 0x1c, 0x6d, 0x61, 0x78, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x64, 0x65,
	0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19, 0x6d, 0x61, 0x78, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x44, 0x65, 0x6c, 0x61, 0x79, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x12, 0x38, 0x0a, 0x18, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x16, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x53, 0x6c, 0x6f, 0x74,
	0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xbb, 0x01, 0x0a, 0x0e,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x12, 0x36,
	0x0a, 0x17, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x15, 0x6d, 0x61, 0x78, 0x43, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x6d, 0x61, 0x78, 0x5f,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19,
	0x6d, 0x61, 0x78, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x4d, 0x69, 0x63,
	0x72, 0x6f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x22, 0xa9, 0x03, 0x0a,
	0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x69, 0x6e, 0x67,
	0x12, 0x33, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x52,
	0x04, 0x73, 0x74, 0x65, 0x70, 0x1a, 0xe0, 0x02, 0x0a, 0x04, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x4d,
	0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x74, 0x65, 0x70, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a,
	0x0d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x33, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa4, 0x03,
	0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x3a, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x1a, 0xcc, 0x01, 0x0a,
	0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x09,
	0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x08, 0x7a, 0x65, 0x72, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0b, 0x72,
	0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x0a, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28,
	0x0a, 0x0f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x57, 0x0a, 0x0b, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72,
	0x6d, 0x75, 0x70, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x41, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x70, 0x5f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x70, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x46,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x63, 0x6f, 0x75, 0x70, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x65, 0x63, 0x6f, 0x75, 0x70, 0x6c, 0x65, 0x64, 0x22,
	0x8c, 0x02, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xb2, 0x01, 0x0a, 0x05, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x1a,
	0x3d, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2c,
	0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xa4, 0x0d, 0x0a,
	0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x44, 0x0a, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x24, 0x0a, 0x0e,
	0x6d, 0x61, 0x78, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x2e, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12,
	0x36, 0x0a, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x14,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x0a, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x39, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0c, 0x6f, 0x70,
	0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x10, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x4f, 0x0a, 0x11, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x10, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x13, 0x65, 0x6e, 0x73,
	0x65, 0x6d, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x69,
	0x6e, 0x67, 0x48, 0x00, 0x52, 0x12, 0x65, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x34,
	0x0a, 0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x12, 0x63, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10,
	0x63, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x47, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x61, 0x67, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x39, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x77, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x52,
	0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x12, 0x45, 0x0a, 0x10,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x5b, 0x0a, 0x18, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x16, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x58, 0x0a, 0x17, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x15, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x1a, 0x43, 0x0a, 0x15, 0x43, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x13,
	0x0a, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x2a, 0xeb, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x0c, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4c, 0x10,
	0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x38, 0x10,
	0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x31, 0x36,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x33,
	0x32, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54,
	0x36, 0x34, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x38, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x31,
	0x36, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x33,
	0x32, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x36,
	0x34, 0x10, 0x09, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x50, 0x31, 0x36,
	0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x50, 0x33, 0x32, 0x10,
	0x0b, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x50, 0x36, 0x34, 0x10, 0x0c,
	0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10,
	0x0d, 0x42, 0x36, 0x5a, 0x34, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69,
	0x6f, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x2d, 0x61, 0x69, 0x2f, 0x61, 0x69, 0x6f,
	0x74, 0x2f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2d, 0x73, 0x74, 0x64, 0x2f, 0x74, 0x72, 0x69,
	0x74, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_model_config_proto_rawDescOnce sync.Once
	file_pb_model_config_proto_rawDescData = file_pb_model_config_proto_rawDesc
)

func file_pb_model_config_proto_rawDescGZIP() []byte {
	file_pb_model_config_proto_rawDescOnce.Do(func() {
		file_pb_model_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_model_config_proto_rawDescData)
	})
	return file_pb_model_config_proto_rawDescData
}

var file_pb_model_config_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_pb_model_config_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_pb_model_config_proto_goTypes = []interface{}{
	(DataType)(0),                // 0: inference.DataType
	(ModelInstanceGroup_Kind)(0), // 1: inference.ModelInstanceGroup.Kind
	(ModelInstanceGroup_SecondaryDevice_SecondaryDeviceKind)(0), // 2: inference.ModelInstanceGroup.SecondaryDevice.SecondaryDeviceKind
	(ModelInput_Format)(0),                                    // 3: inference.ModelInput.Format
	(BatchInput_Kind)(0),                                      // 4: inference.BatchInput.Kind
	(BatchOutput_Kind)(0),                                     // 5: inference.BatchOutput.Kind
	(ModelOptimizationPolicy_ModelPriority)(0),                // 6: inference.ModelOptimizationPolicy.ModelPriority
	(ModelQueuePolicy_TimeoutAction)(0),                       // 7: inference.ModelQueuePolicy.TimeoutAction
	(ModelSequenceBatching_Control_Kind)(0),                   // 8: inference.ModelSequenceBatching.Control.Kind
	(*ModelRateLimiter)(nil),                                  // 9: inference.ModelRateLimiter
	(*ModelInstanceGroup)(nil),                                // 10: inference.ModelInstanceGroup
	(*ModelTensorReshape)(nil),                                // 11: inference.ModelTensorReshape
	(*ModelInput)(nil),                                        // 12: inference.ModelInput
	(*ModelOutput)(nil),                                       // 13: inference.ModelOutput
	(*BatchInput)(nil),                                        // 14: inference.BatchInput
	(*BatchOutput)(nil),                                       // 15: inference.BatchOutput
	(*ModelVersionPolicy)(nil),                                // 16: inference.ModelVersionPolicy
	(*ModelOptimizationPolicy)(nil),                           // 17: inference.ModelOptimizationPolicy
	(*ModelQueuePolicy)(nil),                                  // 18: inference.ModelQueuePolicy
	(*ModelDynamicBatching)(nil),                              // 19: inference.ModelDynamicBatching
	(*ModelSequenceBatching)(nil),                             // 20: inference.ModelSequenceBatching
	(*ModelEnsembling)(nil),                                   // 21: inference.ModelEnsembling
	(*ModelParameter)(nil),                                    // 22: inference.ModelParameter
	(*ModelWarmup)(nil),                                       // 23: inference.ModelWarmup
	(*ModelOperations)(nil),                                   // 24: inference.ModelOperations
	(*ModelTransactionPolicy)(nil),                            // 25: inference.ModelTransactionPolicy
	(*ModelRepositoryAgents)(nil),                             // 26: inference.ModelRepositoryAgents
	(*ModelResponseCache)(nil),                                // 27: inference.ModelResponseCache
	(*ModelConfig)(nil),                                       // 28: inference.ModelConfig
	(*ModelRateLimiter_Resource)(nil),                         // 29: inference.ModelRateLimiter.Resource
	(*ModelInstanceGroup_SecondaryDevice)(nil),                // 30: inference.ModelInstanceGroup.SecondaryDevice
	(*ModelVersionPolicy_Latest)(nil),                         // 31: inference.ModelVersionPolicy.Latest
	(*ModelVersionPolicy_All)(nil),                            // 32: inference.ModelVersionPolicy.All
	(*ModelVersionPolicy_Specific)(nil),                       // 33: inference.ModelVersionPolicy.Specific
	(*ModelOptimizationPolicy_Graph)(nil),                     // 34: inference.ModelOptimizationPolicy.Graph
	(*ModelOptimizationPolicy_Cuda)(nil),                      // 35: inference.ModelOptimizationPolicy.Cuda
	(*ModelOptimizationPolicy_ExecutionAccelerators)(nil),     // 36: inference.ModelOptimizationPolicy.ExecutionAccelerators
	(*ModelOptimizationPolicy_PinnedMemoryBuffer)(nil),        // 37: inference.ModelOptimizationPolicy.PinnedMemoryBuffer
	(*ModelOptimizationPolicy_Cuda_GraphSpec)(nil),            // 38: inference.ModelOptimizationPolicy.Cuda.GraphSpec
	(*ModelOptimizationPolicy_Cuda_GraphSpec_Shape)(nil),      // 39: inference.ModelOptimizationPolicy.Cuda.GraphSpec.Shape
	(*ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound)(nil), // 40: inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound
	nil, // 41: inference.ModelOptimizationPolicy.Cuda.GraphSpec.InputEntry
	nil, // 42: inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound.InputEntry
	(*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator)(nil), // 43: inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator
	nil,                                   // 44: inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator.ParametersEntry
	nil,                                   // 45: inference.ModelDynamicBatching.PriorityQueuePolicyEntry
	(*ModelSequenceBatching_Control)(nil), // 46: inference.ModelSequenceBatching.Control
	(*ModelSequenceBatching_ControlInput)(nil),   // 47: inference.ModelSequenceBatching.ControlInput
	(*ModelSequenceBatching_InitialState)(nil),   // 48: inference.ModelSequenceBatching.InitialState
	(*ModelSequenceBatching_State)(nil),          // 49: inference.ModelSequenceBatching.State
	(*ModelSequenceBatching_StrategyDirect)(nil), // 50: inference.ModelSequenceBatching.StrategyDirect
	(*ModelSequenceBatching_StrategyOldest)(nil), // 51: inference.ModelSequenceBatching.StrategyOldest
	(*ModelEnsembling_Step)(nil),                 // 52: inference.ModelEnsembling.Step
	nil,                                          // 53: inference.ModelEnsembling.Step.InputMapEntry
	nil,                                          // 54: inference.ModelEnsembling.Step.OutputMapEntry
	(*ModelWarmup_Input)(nil),                    // 55: inference.ModelWarmup.Input
	nil,                                          // 56: inference.ModelWarmup.InputsEntry
	(*ModelRepositoryAgents_Agent)(nil),          // 57: inference.ModelRepositoryAgents.Agent
	nil,                                          // 58: inference.ModelRepositoryAgents.Agent.ParametersEntry
	nil,                                          // 59: inference.ModelConfig.CcModelFilenamesEntry
	nil,                                          // 60: inference.ModelConfig.MetricTagsEntry
	nil,                                          // 61: inference.ModelConfig.ParametersEntry
}
var file_pb_model_config_proto_depIdxs = []int32{
	29, // 0: inference.ModelRateLimiter.resources:type_name -> inference.ModelRateLimiter.Resource
	1,  // 1: inference.ModelInstanceGroup.kind:type_name -> inference.ModelInstanceGroup.Kind
	9,  // 2: inference.ModelInstanceGroup.rate_limiter:type_name -> inference.ModelRateLimiter
	30, // 3: inference.ModelInstanceGroup.secondary_devices:type_name -> inference.ModelInstanceGroup.SecondaryDevice
	0,  // 4: inference.ModelInput.data_type:type_name -> inference.DataType
	3,  // 5: inference.ModelInput.format:type_name -> inference.ModelInput.Format
	11, // 6: inference.ModelInput.reshape:type_name -> inference.ModelTensorReshape
	0,  // 7: inference.ModelOutput.data_type:type_name -> inference.DataType
	11, // 8: inference.ModelOutput.reshape:type_name -> inference.ModelTensorReshape
	4,  // 9: inference.BatchInput.kind:type_name -> inference.BatchInput.Kind
	0,  // 10: inference.BatchInput.data_type:type_name -> inference.DataType
	5,  // 11: inference.BatchOutput.kind:type_name -> inference.BatchOutput.Kind
	31, // 12: inference.ModelVersionPolicy.latest:type_name -> inference.ModelVersionPolicy.Latest
	32, // 13: inference.ModelVersionPolicy.all:type_name -> inference.ModelVersionPolicy.All
	33, // 14: inference.ModelVersionPolicy.specific:type_name -> inference.ModelVersionPolicy.Specific
	34, // 15: inference.ModelOptimizationPolicy.graph:type_name -> inference.ModelOptimizationPolicy.Graph
	6,  // 16: inference.ModelOptimizationPolicy.priority:type_name -> inference.ModelOptimizationPolicy.ModelPriority
	35, // 17: inference.ModelOptimizationPolicy.cuda:type_name -> inference.ModelOptimizationPolicy.Cuda
	36, // 18: inference.ModelOptimizationPolicy.execution_accelerators:type_name -> inference.ModelOptimizationPolicy.ExecutionAccelerators
	37, // 19: inference.ModelOptimizationPolicy.input_pinned_memory:type_name -> inference.ModelOptimizationPolicy.PinnedMemoryBuffer
	37, // 20: inference.ModelOptimizationPolicy.output_pinned_memory:type_name -> inference.ModelOptimizationPolicy.PinnedMemoryBuffer
	7,  // 21: inference.ModelQueuePolicy.timeout_action:type_name -> inference.ModelQueuePolicy.TimeoutAction
	18, // 22: inference.ModelDynamicBatching.default_queue_policy:type_name -> inference.ModelQueuePolicy
	45, // 23: inference.ModelDynamicBatching.priority_queue_policy:type_name -> inference.ModelDynamicBatching.PriorityQueuePolicyEntry
	50, // 24: inference.ModelSequenceBatching.direct:type_name -> inference.ModelSequenceBatching.StrategyDirect
	51, // 25: inference.ModelSequenceBatching.oldest:type_name -> inference.ModelSequenceBatching.StrategyOldest
	47, // 26: inference.ModelSequenceBatching.control_input:type_name -> inference.ModelSequenceBatching.ControlInput
	49, // 27: inference.ModelSequenceBatching.state:type_name -> inference.ModelSequenceBatching.State
	52, // 28: inference.ModelEnsembling.step:type_name -> inference.ModelEnsembling.Step
	56, // 29: inference.ModelWarmup.inputs:type_name -> inference.ModelWarmup.InputsEntry
	57, // 30: inference.ModelRepositoryAgents.agents:type_name -> inference.ModelRepositoryAgents.Agent
	16, // 31: inference.ModelConfig.version_policy:type_name -> inference.ModelVersionPolicy
	12, // 32: inference.ModelConfig.input:type_name -> inference.ModelInput
	13, // 33: inference.ModelConfig.output:type_name -> inference.ModelOutput
	14, // 34: inference.ModelConfig.batch_input:type_name -> inference.BatchInput
	15, // 35: inference.ModelConfig.batch_output:type_name -> inference.BatchOutput
	17, // 36: inference.ModelConfig.optimization:type_name -> inference.ModelOptimizationPolicy
	19, // 37: inference.ModelConfig.dynamic_batching:type_name -> inference.ModelDynamicBatching
	20, // 38: inference.ModelConfig.sequence_batching:type_name -> inference.ModelSequenceBatching
	21, // 39: inference.ModelConfig.ensemble_scheduling:type_name -> inference.ModelEnsembling
	10, // 40: inference.ModelConfig.instance_group:type_name -> inference.ModelInstanceGroup
	59, // 41: inference.ModelConfig.cc_model_filenames:type_name -> inference.ModelConfig.CcModelFilenamesEntry
	60, // 42: inference.ModelConfig.metric_tags:type_name -> inference.ModelConfig.MetricTagsEntry
	61, // 43: inference.ModelConfig.parameters:type_name -> inference.ModelConfig.ParametersEntry
	23, // 44: inference.ModelConfig.model_warmup:type_name -> inference.ModelWarmup
	24, // 45: inference.ModelConfig.model_operations:type_name -> inference.ModelOperations
	25, // 46: inference.ModelConfig.model_transaction_policy:type_name -> inference.ModelTransactionPolicy
	26, // 47: inference.ModelConfig.model_repository_agents:type_name -> inference.ModelRepositoryAgents
	27, // 48: inference.ModelConfig.response_cache:type_name -> inference.ModelResponseCache
	2,  // 49: inference.ModelInstanceGroup.SecondaryDevice.kind:type_name -> inference.ModelInstanceGroup.SecondaryDevice.SecondaryDeviceKind
	38, // 50: inference.ModelOptimizationPolicy.Cuda.graph_spec:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec
	43, // 51: inference.ModelOptimizationPolicy.ExecutionAccelerators.gpu_execution_accelerator:type_name -> inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator
	43, // 52: inference.ModelOptimizationPolicy.ExecutionAccelerators.cpu_execution_accelerator:type_name -> inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator
	41, // 53: inference.ModelOptimizationPolicy.Cuda.GraphSpec.input:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec.InputEntry
	40, // 54: inference.ModelOptimizationPolicy.Cuda.GraphSpec.graph_lower_bound:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound
	42, // 55: inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound.input:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound.InputEntry
	39, // 56: inference.ModelOptimizationPolicy.Cuda.GraphSpec.InputEntry.value:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec.Shape
	39, // 57: inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound.InputEntry.value:type_name -> inference.ModelOptimizationPolicy.Cuda.GraphSpec.Shape
	44, // 58: inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator.parameters:type_name -> inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator.ParametersEntry
	18, // 59: inference.ModelDynamicBatching.PriorityQueuePolicyEntry.value:type_name -> inference.ModelQueuePolicy
	8,  // 60: inference.ModelSequenceBatching.Control.kind:type_name -> inference.ModelSequenceBatching.Control.Kind
	0,  // 61: inference.ModelSequenceBatching.Control.data_type:type_name -> inference.DataType
	46, // 62: inference.ModelSequenceBatching.ControlInput.control:type_name -> inference.ModelSequenceBatching.Control
	0,  // 63: inference.ModelSequenceBatching.InitialState.data_type:type_name -> inference.DataType
	0,  // 64: inference.ModelSequenceBatching.State.data_type:type_name -> inference.DataType
	48, // 65: inference.ModelSequenceBatching.State.initial_state:type_name -> inference.ModelSequenceBatching.InitialState
	53, // 66: inference.ModelEnsembling.Step.input_map:type_name -> inference.ModelEnsembling.Step.InputMapEntry
	54, // 67: inference.ModelEnsembling.Step.output_map:type_name -> inference.ModelEnsembling.Step.OutputMapEntry
	0,  // 68: inference.ModelWarmup.Input.data_type:type_name -> inference.DataType
	55, // 69: inference.ModelWarmup.InputsEntry.value:type_name -> inference.ModelWarmup.Input
	58, // 70: inference.ModelRepositoryAgents.Agent.parameters:type_name -> inference.ModelRepositoryAgents.Agent.ParametersEntry
	22, // 71: inference.ModelConfig.ParametersEntry.value:type_name -> inference.ModelParameter
	72, // [72:72] is the sub-list for method output_type
	72, // [72:72] is the sub-list for method input_type
	72, // [72:72] is the sub-list for extension type_name
	72, // [72:72] is the sub-list for extension extendee
	0,  // [0:72] is the sub-list for field type_name
}

func init() { file_pb_model_config_proto_init() }
func file_pb_model_config_proto_init() {
	if File_pb_model_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_model_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRateLimiter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInstanceGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelTensorReshape); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelVersionPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelQueuePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDynamicBatching); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelEnsembling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelWarmup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOperations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelTransactionPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRepositoryAgents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelResponseCache); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRateLimiter_Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInstanceGroup_SecondaryDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelVersionPolicy_Latest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelVersionPolicy_All); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelVersionPolicy_Specific); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_Graph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_Cuda); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_ExecutionAccelerators); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_PinnedMemoryBuffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_Cuda_GraphSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_Cuda_GraphSpec_Shape); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_Cuda_GraphSpec_LowerBound); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelOptimizationPolicy_ExecutionAccelerators_Accelerator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_Control); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_ControlInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_InitialState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_StrategyDirect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSequenceBatching_StrategyOldest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelEnsembling_Step); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelWarmup_Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_model_config_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRepositoryAgents_Agent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pb_model_config_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*ModelVersionPolicy_Latest_)(nil),
		(*ModelVersionPolicy_All_)(nil),
		(*ModelVersionPolicy_Specific_)(nil),
	}
	file_pb_model_config_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*ModelSequenceBatching_Direct)(nil),
		(*ModelSequenceBatching_Oldest)(nil),
	}
	file_pb_model_config_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*ModelConfig_DynamicBatching)(nil),
		(*ModelConfig_SequenceBatching)(nil),
		(*ModelConfig_EnsembleScheduling)(nil),
	}
	file_pb_model_config_proto_msgTypes[39].OneofWrappers = []interface{}{
		(*ModelSequenceBatching_InitialState_ZeroData)(nil),
		(*ModelSequenceBatching_InitialState_DataFile)(nil),
	}
	file_pb_model_config_proto_msgTypes[46].OneofWrappers = []interface{}{
		(*ModelWarmup_Input_ZeroData)(nil),
		(*ModelWarmup_Input_RandomData)(nil),
		(*ModelWarmup_Input_InputDataFile)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_model_config_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_model_config_proto_goTypes,
		DependencyIndexes: file_pb_model_config_proto_depIdxs,
		EnumInfos:         file_pb_model_config_proto_enumTypes,
		MessageInfos:      file_pb_model_config_proto_msgTypes,
	}.Build()
	File_pb_model_config_proto = out.File
	file_pb_model_config_proto_rawDesc = nil
	file_pb_model_config_proto_goTypes = nil
	file_pb_model_config_proto_depIdxs = nil
}
