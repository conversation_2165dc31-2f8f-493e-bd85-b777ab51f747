syntax = "proto3";

package inference;

// option go_package = "transwarp.io/applied-ai/aiot/vision-std/triton/pb;inference";
option go_package = "transwarp.io/applied-ai/aiot/vision-std/triton/pb;pb";

message InferReq {
  // Param 为推理请求中一个具体的待推理数据（一张图片）相关的描述信息
  message Param {
    // id 为标识推理数据的ID
    string id = 1;
    // type 为标识该条推理数据的数据格式，e.g. raw, jpg/jpeg, png
    string type = 2;
    // data 为该条推理数据的payload（发送给Triton Ensemble Model时该字段必须为空）
    string data = 3;
    // size 为该条推理数据的payload的具体大小(字节数)
    uint64 size = 4;
    // external_config 推理某条数据时，可能需要的一些额外的配置信息
    ExternalConfig external_config = 5;
  }

  // Context 为本次推理过程中可能会使用到的上下文信息
  message Context {
    // instance_labels 为发出该推理请求的SophonEdge应用实例包含的标签信息
    map<string, string> instance_labels = 1;
    // pipeline_labels 为发出该推理请求的SophonEdge应用规则包含的标签信息
    map<string, string> pipeline_labels = 2;
  }

  // client_id 标识发起推理请求的客户端
  string client_id = 1;
  // params 为本次推理中所有的待推理数据
  repeated Param params = 2;
  // context 为推理过程中可能用到的上下文信息
  Context context = 3;
  // extra 为调用时Client端自行传输的额外配置
  map<string, string> extra = 4;
}

message ExternalConfig {
  message Position{
    message Point {
      uint32 x = 1;
      uint32 y = 2;
    }
    repeated Point position = 1;
  }
  map<string, string> infer_params = 1;
  map<string, string> device_labels = 2;
  repeated Position roi = 3;
}

// FIXME
// protobuf 部分语法不支持，因此该结构和模型推理规范中的结构不完全一致
// e.g. position 应为 [][]int 结构
message InferRsp {
  // Result 为返回内容的结构体
  message Result {
    // Object 表示单个检测目标
    message Object {
      // Attr 为目标的属性
      message Attr {
        string key = 1;
        string value = 2;
        string desc = 3;
      }
      // Point 为目标位置的一个点位坐标
      message Point {
        uint32 x = 1;
        uint32 y = 2;
      }

      message Stats {
        // 模型内部的置信度指标
        repeated float score = 1;
        // 模型推理延迟(非必须）
        uint32 cost_ms = 2;
      }

      // position_type 目标的位置类型（point  , cnpose18 , alphapose17 , openpose18 , mask不指定时默认为 point ，渲染时将点连成线）
      string position_type = 1;
      // position 目标框位置
      repeated Point position = 2;
      // attributes 目标的属性列表
      repeated Attr attributes = 3;
      // objects 目标中包含的子目标列表
      repeated Object objects = 4;
      // 单通道png图片的base64编码，每个点的像素对应标签的id，如果是实例分割就是0,1
      string mask = 5;
      // 标签与id的dict; 例如 "mask_label": {"cat":1, "dog":2 ...}
      string mask_label = 6;
    }

    // id 标识对应的请求中的 InferReq.Param.id
    string id = 1;
    // objects 为对应推理数据的推理结果，可能包含多个检测目标
    repeated Object objects = 2;
  }

  // client_id 标识发起推理请求的客户端,与请求中的client_id对应
  string client_id = 1;
  // results 为本次推理请求中所有推理数据的结果
  repeated Result results = 2;
  // model_info 模型信息，版本/时间等
  map<string, string> model_info = 3;
}