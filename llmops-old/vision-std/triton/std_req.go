package triton

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"path/filepath"
	"sort"
	"strconv"
	"strings"

	"github.com/sashabaranov/go-openai"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"github.com/fatih/structs"
	"github.com/juju/errors"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

var (
	EmptyInferRequestErr = stderr.BadRequest.Error("infer request is empty")
	Base64DecodeErr      = stderr.BadRequest.Error("illegal base64 data, please verify it")
)

const (
	imageSizeSeparator                                   = "x"
	ImageGenResponseFormatUrl     ImageGenResponseFormat = "url"
	ImageGenResponseFormatB64Json ImageGenResponseFormat = "b64_json"
	ImageGenResponseFormatB64Md   ImageGenResponseFormat = "b64_md"
)

// SyncInferRequestor 为 DLIE 推理引擎能够处理的通用推理结构
type SyncInferRequestor interface {
	// Inputs 返回将当前请求体转换为 DLIE Input 后的结构
	Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error)
	// InputNames 按顺序返回各个输入参数的名称
	InputNames() []string
	// Outputs 返回当前请求的输出格式
	Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor
	// OutputNames 按顺序返回各个输出参数的名称
	OutputNames() []string
}

// StreamInferRequestor 为 DLIE 推理引擎能够处理的通用推理结构, 同时支持Decoupled流式返回模式
type StreamInferRequestor interface {
	SyncInferRequestor
	// StreamOutputHandler 返回指定Output输出的流式返回内容的处理器
	StreamOutputHandler(name string) StreamOutputHandler
}

type StreamOutputHandler func(raw []byte) error

// QAItem 每个 QAItem 表示用户与大模型的一次交互记录，亦即一轮问答
type QAItem struct {
	Q string `json:"Q,omitempty" description:"用户问题"` // Q 为用户问题
	A string `json:"A,omitempty" description:"模型响应"` // A 为模型响应
}
type LLMParams struct {
	MaxLength         int      `json:"max_length,omitempty"`
	NumBeams          int      `json:"num_beams,omitempty"`
	EarlyStopping     bool     `json:"early_stopping,omitempty"`
	DoSample          bool     `json:"do_sample,omitempty"`
	Temperature       float64  `json:"temperature,omitempty"`
	TopK              int      `json:"top_k,omitempty"`
	TopP              float64  `json:"top_p,omitempty"`
	RepetitionPenalty float64  `json:"repetition_penalty,omitempty"`
	NoRepeatNgramSize int      `json:"no_repeat_ngram_size,omitempty"`
	StopWords         []string `json:"stop_words,omitempty"`
	GuidedJson        string   `json:"guided_json,omitempty"`
	GuidedBackend     string   `json:"guided_backend,omitempty"`
}

// deprecated
// 该方法转换为Map后Key为大写，与dlie侧不匹配
func (l *LLMParams) Cvt2Map() map[string]any {
	return structs.Map(l)
}

func (l *LLMParams) AsMap() map[string]any {
	return cvtToMap(l)
}

type ImageGenResponseFormat string
type ImageGenParams struct {
	Width          int                    `json:"width,omitempty"`
	Height         int                    `json:"height,omitempty"`
	ResponseFormat ImageGenResponseFormat `json:"response_format,omitempty"`
}

func (params *ImageGenParams) AsMap() map[string]any {
	return cvtToMap(params)
}

// 将结构体转为对应的map
func cvtToMap(structParams any) map[string]any {
	data := make(map[string]any)
	err := json.Unmarshal(stdsrv.AnyToBytes(structParams), &data)
	if err != nil {
		stdlog.Infof("failed while unmarshal params to map, the err is %s", err.Error())
	}
	return data
}

// LLMChatResp  专用于对话,text字段为生成的文本,
// 大模型返回可能为opeani-json格式或纯文本,text目前统一为生成的文本内容
// TODO 后续可以将对话模型的返回统一为openai格式
type LLMChatResp struct {
	RequestId    string `json:"request_id" yaml:"request_id" description:"每一次流式请求的唯一id。"`
	Index        int    `json:"index" yaml:"index" description:"返回消息的序列号。"`
	Text         string `json:"text" yaml:"text" description:"生成的具体文本。"`
	Created      int64  `json:"created" yaml:"created" description:"文本生成的时间"`
	Model        string `json:"model" yaml:"model" description:"模型名称"`
	FinishReason string `json:"finish_reason" yaml:"finish_reason" description:"停止原因"`
	Usage        *Usage `json:"usage,omitempty" description:"完成请求的token使用情况统计信息,流式请求携带在最后的消息"`
}

// StdLLMInferResp  兜底,text字段为大模型的原生返回
type StdLLMInferResp struct {
	LLMChatResp
}

// LLMChatReq 用于进行大模型对话的，支持流式返回的推理请求。 --不局限文本生成,同时支持图像理解,图像生成等。
// 非文本生成类型的大模型推理-推荐使用LLMInferReq
type LLMChatReq struct {
	Query    string                  `json:"query" yaml:"query" description:"需要询问的具体问题。"`
	Stream   bool                    `json:"stream" yaml:"stream" description:"是否为流式返回，由请求路径覆盖。"` // FIXME 当前 key 必须显示存在，否则默认为 stream 模式
	Prompt   string                  `json:"prompt,omitempty" yaml:"prompt" description:"系统提示词"`
	History  []QAItem                `json:"history,omitempty" yaml:"history" description:"对话的历史"`
	Params   map[string]any          `json:"params,omitempty" yaml:"params" description:"模型推理的控制参数。"`
	Handler  StreamOutputHandler     `json:"-"`
	Stop     bool                    `json:"stop" yaml:"stop" description:"停止流式推理"`
	File     string                  `json:"file,omitempty" yaml:"file" description:"多模态支持,传递图片的地址或者b64编码"`
	Messages []MultimodalMessageItem `json:"messages,omitempty" yaml:"messages" description:"多模态时-图像、视频理解,传递原始的messages信息"`
	Tools    []Tool                  `json:"tools,omitempty"  description:"模型可能调用的工具列表"`
	Mode     string                  `json:"mode" description:"模型对话场景模式。目前支持聊天(默认模式)、工具调用、代码执行, chat|tool|ci"`
}

func (r *LLMChatReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	bs, err := r.AsBytesTensor()
	if err != nil {
		return nil, stderr.Wrap(err, "convert llm chat req to bytes tensor")
	}
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdTextGenInputJson,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents: &pb.InferTensorContents{
				BytesContents: [][]byte{bs},
			},
		},
	}, nil
}

func (r *LLMChatReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdTextGenOutputRes,
			Parameters: nil,
		},
	}
}

func (r *LLMChatReq) InputNames() []string {
	return StdTextGenModelIO.InputNames()
}

func (r *LLMChatReq) OutputNames() []string {
	return StdTextGenModelIO.OutputNames()
}

func (r *LLMChatReq) StreamOutputHandler(name string) StreamOutputHandler {
	if name != StdTextGenOutputRes {
		return nil
	}
	return r.Handler
}

func (r *LLMChatReq) AsBytesTensor() ([]byte, error) {
	bs, err := json.Marshal(r)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "marshal LLMChatReq")
	}
	return bs, nil
}

func (r *LLMChatReq) IsStream() bool {
	return r.Stream
}
func (r *LLMChatReq) GetModelInferRequest(modelName string) (*pb.ModelInferRequest, error) {
	inputs, err := r.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := r.Outputs()
	inferReq := &pb.ModelInferRequest{
		ModelName: modelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	// 调用文本生成模型时给一个默认的request_id
	inferReq.Id = toolkit.NewUUID()
	return inferReq, nil
}
func (r *LLMChatReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}

	content := string(streamInfo.ModelInferResponse.RawOutputContents[0][4:])
	LLMChatResp := &LLMChatResp{
		RequestId:    streamInfo.RequestId,
		Created:      streamInfo.Created,
		Index:        streamInfo.Index,
		Text:         content,
		Model:        streamInfo.Model,
		FinishReason: streamInfo.FinishReason,
	}

	// 部分对话模型返回为openai-json格式,将其统一为std格式
	var openAiChatResp OpenAiChatResp
	err := json.Unmarshal([]byte(content), &openAiChatResp)
	if err == nil && openAiChatResp.Id != "" {
		LLMChatResp.Text, _ = openAiChatResp.GetInferResult()
		LLMChatResp.Usage = openAiChatResp.Usage
	}
	return GetStdInferResp(LLMChatResp, streamInfo.ModelInferResponse), nil
}

type StdLLMInferReq struct {
	LLMChatReq
}

func (r *StdLLMInferReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	StdLLMInferResp := StdLLMInferResp{
		LLMChatResp: LLMChatResp{
			RequestId:    streamInfo.RequestId,
			Created:      streamInfo.Created,
			Index:        streamInfo.Index,
			Text:         string(streamInfo.ModelInferResponse.RawOutputContents[0][4:]),
			Model:        streamInfo.Model,
			FinishReason: streamInfo.FinishReason,
		},
	}
	return GetStdInferResp(StdLLMInferResp, streamInfo.ModelInferResponse), nil
}

// Text2VecReqV1 进行文本->向量转换的模型请求结构
// 具体接口定义参见： https://sophon-edge.yuque.com/ta73lw/knqsgi/zd4pd1my43arsruz
type Text2VecReqV1 struct {
	Text string `json:"text"`
}

const (
	text2VecReqTmpl = `
{
    "client_id": "example",
    "params": [
        {
            "id": "0",
            "type": "text",
            "data": "TEXT_BINARY"
        }
    ]
}
`
)

func (t *Text2VecReqV1) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdTextVecV1InputData,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents:   NewByteTensor([]byte(t.Text)),
		},
		{
			Name:       StdTextVecV1InputMeta,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents:   NewByteTensor([]byte(text2VecReqTmpl)),
		},
	}, nil
}

func (t *Text2VecReqV1) InputNames() []string {
	return StdTextVecV1ModelIO.InputNames()
}

func (t *Text2VecReqV1) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdTextVecV1OutputRes,
			Parameters: nil,
		},
	}
}

const (
	TextVecResV1EmbeddingAttrKey = "embedding"
)

type TextVecResV1 struct {
	Results []struct {
		Id      string `json:"id"`
		Objects []struct {
			Attributes []struct {
				Key   string      `json:"key"`
				Value [][]float64 `json:"value"`
				Desc  string      `json:"desc"`
			} `json:"attributes"`
		} `json:"objects"`
	} `json:"results"`
	ClientId string `json:"client_id"`
}

// Embedding 从解析后的Text2Vec模型尝试输出取出向量部分
// 原始响应结构如下：https://sophon-edge.yuque.com/ta73lw/knqsgi/zd4pd1my43arsruz
//
//	{
//			"results": [{
//				"id": "0",
//				"objects": [{
//					"attributes": [{
//						"key": "embedding",
//						"value": [[0.5285001993179321, -0.7273613810539246, -0.4462415277957916, 0.3598140478134155, -0.7095134854316711, -1.7718604803085327, 0.48747876286506653, -0.5953181982040405, 0.7755603790283203, 0.6005987524986267, 0.8139524459838867, 0.8995569348335266, 1.2732950448989868, 0.6679684519767761, 0.9282382130622864, -1.0356298685073853, -1.0475850105285645, 0.8531246185302734, 1.2429537773132324, 1.459985375404358, 1.270519733428955, -0.7670188546180725, -0.8901241421699524, -0.5177432298660278, 1.2493730783462524, 0.11637143045663834, 0.7895699143409729, 0.36032819747924805, -1.066792368888855, 0.9917766451835632, -0.017925992608070374, -0.04924764856696129, 0.3487912118434906, -0.5112259387969971, -0.6250610947608948, 0.2315147966146469, -0.22048719227313995, -1.0883173942565918, 0.43461108207702637, 0.07609682530164719, -0.5990744233131409, -0.8958541750907898, -0.4022250175476074, 0.2982507050037384, 0.9841839671134949, 0.004953280091285706, 0.8342933058738708, -0.28572797775268555, 0.7106085419654846, 1.6486802101135254, 1.1540800333023071, 0.0459417998790741, -0.05952484905719757, -1.9543513059616089, 0.9695197939872742, 1.1256496906280518, 0.431153267621994, -0.4257218539714813, -0.3839162588119507, 0.7352533340454102, -0.6446767449378967, 2.319606065750122, -0.724761962890625, -0.5476768016815186, 0.37085258960723877, 1.034885048866272, 1.165757179260254, -0.8767469525337219, 2.026888847351074, 0.63639897108078, -1.534868836402893, -0.49154672026634216, -0.7613852620124817, 0.22223179042339325, -0.2981337308883667, 0.155668243765831, -0.6544671058654785, 0.3945681154727936, -1.2688376903533936, 0.6283382773399353, 0.14465714991092682, -0.5707878470420837, -0.3689112663269043, 0.18472063541412354, -1.5340856313705444, -0.2915376126766205, 0.13591699302196503, 4.156050205230713, 0.31347405910491943, 0.9669917225837708, 0.8715352416038513, 0.6852489113807678, 0.110443115234375, -0.6874659657478333, -0.6551730632781982, -0.4816608726978302, 0.20531213283538818, -1.5436404943466187, 0.09180478006601334, -0.8981683850288391, -0.4532967507839203, -1.155592679977417, -2.023864984512329, 0.3330349028110504, -0.17657727003097534, -0.037099383771419525, -0.23599942028522491, -0.37058284878730774, -0.8389982581138611, -0.787161648273468, 0.7528899312019348, -0.6204962134361267, 0.7188552021980286, -0.7191982865333557, -0.17834873497486115, -0.771325409412384, 0.17233037948608398, -0.3712550103664398, -0.329590380191803, 0.3725448548793793, -0.617321789264679, 0.5631818771362305, -1.47652006149292, -1.38982355594635, -1.4043898582458496, -0.033491101115942, -0.8394644856452942, -0.9688600897789001, -0.12521468102931976, 2.0700643062591553, -1.7325598001480103, -0.9567976593971252, 2.189945936203003, -0.6003260612487793, -0.1991514414548874, -0.060625817626714706, -0.7528206706047058, 0.5156545639038086, 0.3631744086742401, 0.3522169291973114, 0.4146151840686798, 0.1003221794962883, -0.2259383201599121, -0.5074396133422852, 1.6195088624954224, -0.4877007007598877, -0.6292282938957214, 0.29244187474250793, 0.4642668664455414, -0.47920188307762146, 1.703025460243225, 0.032870303839445114, -0.2759498655796051, -0.1300826519727707, -0.6069130897521973, -0.12485333532094955, -0.20150144398212433, 0.6637776494026184, -0.09189432859420776, -0.22800670564174652, 0.11199575662612915, 0.33590593934059143, 0.32289764285087585, -1.4020143747329712, -0.8382411003112793, 0.2306525558233261, -0.5634946227073669, 0.1612275093793869, -0.30070754885673523, 0.4819064438343048, -0.5723018050193787, 0.4306652843952179, -1.015118956565857, -0.5602767467498779, 0.3141269087791443, -2.0528695583343506, 1.042777180671692, 2.784600019454956, -1.3497753143310547, 0.5402353405952454, 1.2556114196777344, -0.5295568108558655, 0.7947365641593933, -0.7196241021156311, 0.3685777187347412, 0.90518718957901, 0.4619567394256592, -1.5303796529769897, 0.7054495811462402, -2.0908877849578857, -0.15186114609241486, 2.253613233566284, 0.5797201991081238, 0.5711320042610168, 1.4955202341079712, 1.6303043365478516, 0.481632798910141, -0.48832058906555176, -0.7054636478424072, 0.23978547751903534, -1.0208637714385986, -0.4515920579433441, 0.07468723505735397, 0.2586256265640259, 0.4772489368915558, -0.779233992099762, 0.16037116944789886, 0.6950774192810059, -0.8258693814277649, -1.7748140096664429, -0.43043097853660583, 0.20387984812259674, 0.5442628860473633, -1.082978367805481, 0.47486868500709534, -0.8157667517662048, -1.0676575899124146, 0.6705837249755859, 1.3325695991516113, -0.254722535610199, -0.35229143500328064, 0.9058434367179871, 0.7686598300933838, -0.12084092944860458, 0.0008723139762878418, -1.012232780456543, 1.0405383110046387, -0.3528505265712738, -1.5068902969360352, 1.3595819473266602, 0.6693801879882812, 1.442671775817871, -0.1683942824602127, 0.8425602316856384, -0.910945475101471, -0.491133451461792, -0.5953230857849121, 0.5600703358650208, -0.6197749376296997, -0.38033226132392883, 0.35250309109687805, 0.45649442076683044, -0.680995523929596, -0.49517622590065, -0.7721822261810303, 0.9924405217170715, -1.2712475061416626, -0.3247798681259155, -0.7890481352806091, 0.9867713451385498, -1.0227802991867065, 0.4072890281677246, 0.372262567281723, 0.6943483352661133, -0.06314101070165634, -0.07260486483573914, 2.0836238861083984, -1.6559438705444336, 0.12975183129310608, -0.4536239206790924, 0.8013317584991455, -1.2823792695999146, 0.19235533475875854, -1.2164891958236694, 0.2960822284221649, 0.19494712352752686, -0.5234422087669373, -0.51617032289505, 0.6658400893211365, -0.18139465153217316, -1.1112439632415771, 1.1368625164031982, 0.3642386496067047, -0.1191219612956047, 0.8537285327911377, 0.917834460735321, -0.3407599627971649, 0.8837018609046936, -0.3410532474517822, 1.366093635559082, -0.4957796633243561, 0.2405751496553421, -0.8212541937828064, -0.37255167961120605, 0.6343222260475159, -0.3317805528640747, -0.20569400489330292, 0.6517935395240784, 2.1510801315307617, -0.17265868186950684, -1.632886528968811, 0.5413036346435547, -0.2503267228603363, -1.55863618850708, 0.2816963493824005, 0.8514987826347351, 0.8517961502075195, 0.7111541628837585, 0.2640244662761688, -0.011597844772040844, -0.9561758637428284, 0.988893985748291, -0.2713630795478821, 0.8099920153617859, 0.3866833746433258, -0.34801602363586426, -0.5452585220336914, 0.2010873556137085, 0.21652932465076447, -0.7120130062103271, -0.4812423288822174, -1.5240801572799683, -0.18106581270694733, 0.733232319355011, 0.7909124493598938, -0.7770483493804932, -0.17425136268138885, 0.8750037550926208, -0.15476027131080627, 0.18291465938091278, 0.9143490195274353, 0.07924848794937134, -0.49112698435783386, 0.28404372930526733, -0.4341779053211212, 0.5697740912437439, -1.4071394205093384, -0.25002557039260864, -0.41064703464508057, 0.4040400981903076, -0.49840912222862244, -0.3087315857410431, -0.3729243278503418, 0.624763548374176, 0.5300337672233582, 0.5452325344085693, -0.6961455941200256, 1.008677363395691, -0.17168720066547394, 0.7149747014045715, 0.23602668941020966, 0.28717896342277527, -0.31177735328674316, 0.32049477100372314, -0.3660466969013214, 0.9130802750587463, 0.24034295976161957, 0.10733464360237122, -0.8661623001098633, -0.4914283752441406, -0.18248401582241058, 0.8164842128753662, 0.9294295907020569, 1.381874442100525, 0.30319640040397644, 0.6378857493400574, -0.1647651642560959, -0.402361661195755, 0.40501710772514343, -0.5437416434288025, 0.6762968897819519, -0.46552374958992004, 0.17805908620357513, -0.6946520209312439, -0.7233500480651855, -0.2519841194152832, 0.15398885309696198, -1.2281975746154785, 0.7593476176261902, 0.5250221490859985, 0.10359618812799454, 0.7776554226875305, 1.0544644594192505, -0.5467925071716309, -1.4922312498092651, -0.8189840912818909, 1.3697401285171509, -0.5177150368690491, -0.046481240540742874, -0.5714977383613586, 0.04931476339697838, -0.988694965839386, 1.7276655435562134, -0.804149329662323, -0.9308530688285828, -1.1170680522918701, -0.3452291786670685, -0.2721814811229706, 0.15529930591583252, 1.4259732961654663, -0.6541655659675598, 0.9006984829902649, 0.18865804374217987, -0.24244536459445953, 0.17441312968730927, -0.08817630261182785, 0.6220415830612183, -0.39549264311790466, -1.475662112236023, -0.31249359250068665, 0.19940857589244843, -0.04539940133690834, 0.43682798743247986, 0.5646281242370605, -0.15502257645130157, -0.5468311309814453, 0.45407605171203613, -0.2324337512254715, 1.965269684791565, 0.09688753634691238, 0.684147298336029, 0.13423071801662445, -0.6946647763252258, 0.7944545149803162, 1.079653263092041, -0.5646992921829224, 0.7046656608581543, -0.49616286158561707, 0.5039008259773254, -0.7491242289543152, 0.36903777718544006, -1.235129952430725, 0.6498039364814758, 1.2964997291564941, -0.9013687968254089, 0.4365571439266205, -1.07833731174469, -0.10690715163946152, -0.2887507677078247, 0.9395596981048584, 0.4185391664505005, 0.2115468531847, 0.48798665404319763, 0.6806324124336243, 0.28761374950408936, -1.4003572463989258, -0.05896259844303131, -1.7147026062011719, -0.2509181797504425, -1.3480663299560547, 0.17043529450893402, -0.3365565836429596, 0.34026309847831726, -0.8948073387145996, 1.554242491722107, 0.24717597663402557, 0.07080427557229996, -0.9155099391937256, 0.8993615508079529, -0.7929982542991638, 1.5624003410339355, -0.1859644651412964, 0.1141868606209755, -0.24029101431369781, 2.035109281539917, -0.6101357340812683, -0.2773928940296173, 0.2711600065231323, 0.01998826675117016, 0.2179046869277954, 0.6184737086296082, -0.21695096790790558, 0.40541377663612366, 0.06284666806459427, -0.9743329882621765, 0.062212858349084854, -1.24907386302948, 1.1875261068344116, 0.42595458030700684, 0.1265854686498642, -0.5586119294166565, -0.6526820063591003, 0.11520817130804062, 0.583057701587677, 0.972013533115387, 0.5244501233100891, -0.09558331221342087, 0.5907735228538513, -0.31639719009399414, -1.3498616218566895, 0.162143275141716, -0.18451911211013794, 0.022714264690876007, 0.9451773166656494, 0.2084171026945114, -0.15853360295295715, 0.297380656003952, -1.2961033582687378, -0.7305091023445129, -0.8422712683677673, -0.6679098010063171, -1.1711050271987915, -0.43113335967063904, 0.1466621309518814, 0.1777898073196411, 0.5439555644989014, -1.110124945640564, -1.5412521362304688, -0.15301300585269928, 1.5046335458755493, -1.3774900436401367, -0.7511263489723206, -1.3276780843734741, 0.3080991208553314, 0.7436478734016418, -0.04247435927391052, 0.8512213826179504, 0.3239411413669586, 0.3073631525039673, -0.29251304268836975, 0.9571163654327393, -0.7644398808479309, -0.686973512172699, 1.6991668939590454, -0.6906474232673645, -0.2454291433095932, 0.938891589641571, 0.7927324771881104, -0.4763772487640381, -1.0396472215652466, 0.5401610732078552, -1.0101876258850098, -1.5877448320388794, 0.2421327829360962, 0.05603219196200371, -0.0757891908288002, -0.16825269162654877, -0.4401058256626129, 1.5267977714538574, -0.5579589009284973, -0.7456934452056885, 1.6108318567276, -1.062447428703308, 1.0049322843551636, 0.060056641697883606, -0.4057047367095947, -1.0575271844863892, -0.03986595943570137, 0.36050495505332947, -0.17938382923603058, -0.07155030965805054, 0.2696041762828827, -0.15747322142124176, 0.4597974717617035, -0.6192237734794617, 0.01742885448038578, -1.1433871984481812, -0.19287967681884766, -0.40804967284202576, 0.1866607517004013, 0.022798290476202965, 0.34534355998039246, -0.048152435570955276, 0.2023361474275589, 0.5073695778846741, 1.8275686502456665, -0.37551966309547424, -1.39521062374115, 0.06989514827728271, -0.6978632807731628, 0.1652769297361374, 1.859399676322937, 0.6644532084465027, -0.12103968858718872, 0.6894158720970154, -0.5763468146324158, -0.1506471186876297, 0.06134213134646416, -0.5866823792457581, 0.07524073123931885, -0.7784401774406433, 0.021847687661647797, -0.27422893047332764, -0.8200066089630127, -0.6439036130905151, -0.28460612893104553, 0.19895704090595245, -0.11377250403165817, -0.5809637308120728, 0.0643940269947052, -0.5274035334587097, -1.7018183469772339, -0.242425799369812, -1.316430687904358, 0.21705661714076996, 0.8266026377677917, 0.7880830764770508, 1.4137088060379028, -1.6674827337265015, -1.0871098041534424, -0.6271979212760925, -0.4407266676425934, -0.6856024265289307, -0.4503408372402191, 0.06481026858091354, 0.587063729763031, -0.6085699200630188, -0.3815641701221466, 0.08938687294721603, 0.4067518413066864, 0.6057306528091431, -0.44822707772254944, 0.685355007648468, -1.546348214149475, -0.4563714563846588, 0.097179114818573, 1.4464043378829956, 0.3596717119216919, -0.4752492904663086, -1.4433549642562866, 0.43948599696159363, 0.11037091165781021, 0.37453070282936096, 0.49139639735221863, -0.34439072012901306, -0.20218390226364136, 1.0748976469039917, -0.6970967650413513, 0.34563159942626953, -1.0522326231002808, -0.1384488195180893, -0.30494916439056396, 0.23528222739696503, -0.7763698697090149, 1.4759832620620728, 1.2168185710906982, -0.6153880953788757, 1.4651960134506226, 0.3603106439113617, -1.0678609609603882, -0.6705817580223083, -0.5100206732749939, 0.6071131825447083, 0.19580549001693726, 0.7923248410224915, -0.8264820575714111, 0.17125070095062256, 0.34637191891670227, 0.6388802528381348, -0.16286858916282654, -0.5455763936042786, 0.07388968765735626, -0.3059309422969818, 1.1102603673934937, -0.013944864273071289, -0.4466886520385742, 0.009048178791999817, -0.7090756297111511, 0.8444259166717529, -0.0218745619058609, -0.35130295157432556, -0.5650234818458557, -0.8027330040931702, -1.5866856575012207, -0.8982923626899719, -0.935351550579071, -0.6770564913749695, 1.5353412628173828, 1.8960100412368774, 0.27872416377067566, -0.5865054130554199, -1.8277779817581177, 1.2315629720687866, -1.6788283586502075, -0.14132511615753174, -0.5501165986061096, -1.1873246431350708, 1.2974005937576294, 0.8629787564277649, 0.840116560459137, 0.01987367868423462, -0.24251510202884674, 0.1467362344264984, 0.2781693637371063, 0.19127368927001953, -0.5697237253189087, -0.983629047870636, 1.6878643035888672, 0.18323862552642822, -0.4003768265247345, 1.6355557441711426, -1.0887792110443115, 2.236579179763794, 1.036534070968628, -1.64923095703125, -0.025561926886439323, 1.0442110300064087, 0.2416985034942627, -1.0550605058670044, 0.17500130832195282, -0.20967130362987518, 0.180723175406456, 0.5327208638191223, -1.64886474609375, 1.62503182888031, -0.6854413151741028, 1.9703179597854614, 0.7978911399841309, 1.430916428565979, 0.911973237991333, 1.4897804260253906, -0.27159202098846436, -0.867384135723114, 0.4207148551940918, 0.3622962236404419, -0.5917940735816956, -1.1247729063034058, 1.2616839408874512, -0.5169437527656555, -0.38747796416282654, 1.4591201543807983, 0.3819262683391571, -0.26590967178344727, -0.2896243929862976, 0.6190296411514282, -1.3001928329467773, -0.045712705701589584, 1.039343237876892, 0.8748355507850647, 0.24167151749134064, 0.6354755163192749, -0.2598133981227875, -1.0211378335952759, 0.27946510910987854, 0.588737964630127, -0.41502857208251953, 1.597670078277588, -0.09825825691223145, -1.2076921463012695, -0.5933377742767334, -0.18182693421840668, 0.7250874042510986, -1.0186249017715454, 0.17046327888965607, -2.0862815380096436, 0.3082544505596161, 0.7366229891777039, -0.7256958484649658, -0.6928157806396484, 0.42336902022361755, 0.04387772083282471, -0.7063133120536804, 0.006874998565763235, 0.340750128030777, -2.001206874847412, 0.54960036277771, 1.2527189254760742, -0.2651110589504242, 0.195814847946167, 0.3296318054199219, 0.3732609748840332, -0.7860357165336609, 0.5143132209777832, 0.27999070286750793, 0.1363614797592163, -0.31552714109420776, -0.40595555305480957, -1.1573446989059448, -0.6288546919822693, -0.17547011375427246, 0.6430069804191589, -1.4575177431106567, -0.5062896013259888, -1.7502427101135254, 0.7776861786842346, -1.4264732599258423, 1.077106237411499, -0.6781086325645447, 0.3669218122959137, -0.9109361171722412, -1.4615668058395386, 0.7564812302589417, 0.7142331600189209, -0.6019671559333801, 0.2782696783542633, -0.41117432713508606, -0.2958911061286926, -0.3167078197002411, -1.119008183479309, -2.2627453804016113, -1.6654812097549438, -1.0886932611465454, 0.28560611605644226, -0.16416119039058685, 0.42641353607177734, -1.0655189752578735, 0.6010186076164246, 1.583992838859558, -0.1229739561676979, -1.3836981058120728, 0.5971036553382874, -1.0512957572937012, 0.7341670393943787, -0.26870831847190857, -0.6914999485015869, -1.250913143157959, 1.4111932516098022, -0.14708752930164337, 0.5104699730873108, 0.9144991040229797, -0.04589417204260826, 0.4678542912006378, -0.06315136700868607, -1.2347149848937988, 0.6734030842781067, 0.411521315574646, 1.1863778829574585, -0.03848632797598839, -1.2730242013931274, 0.04829123616218567, 0.2014586478471756, 1.0079797506332397, 0.8671284317970276, -0.13076063990592957, 0.7403525710105896, -0.3122771084308624, 0.5153037905693054, 0.5487352013587952, -0.32500192523002625, 0.039363935589790344, -0.6262890696525574, -1.1118971109390259, -1.194471836090088, -0.21308313310146332, -0.8800352215766907, -1.3560820817947388, -0.2898869216442108, -0.21322578191757202, 0.2518044412136078, 0.08151216059923172, -0.8984625339508057, -1.469180941581726, -0.6432720422744751, -0.7665889263153076, 1.7974649667739868, -0.2841842472553253, -0.06807199865579605, -0.6039387583732605, -0.182212695479393, -0.3431760370731354, -1.2048417329788208, -0.39394664764404297, 0.2160194367170334, -0.7277863621711731, 0.5204240083694458, 0.9279343485832214, 0.8801761269569397, 0.01709972880780697, -0.023623863235116005, -1.3766521215438843, -0.33367297053337097, -1.0328198671340942, 1.9783538579940796, -0.4102628231048584, -1.065270185470581, -0.2800596058368683, -0.8612205386161804, -0.7119610905647278, 0.9259204268455505, 0.20044200122356415, -0.8240429759025574, -1.0501655340194702, -1.359224796295166, 1.1457661390304565, -0.7546861171722412, -0.3177742660045624, -1.3563767671585083, 0.48879626393318176, -1.1159106492996216, 0.39603567123413086, -1.521723747253418, -0.5512602925300598, 0.18320046365261078, -1.3996524810791016, 1.014180302619934, -0.21119189262390137, -0.1371680647134781, -0.59837406873703, 0.030487896874547005, 0.013099377043545246, 0.6781429648399353, 1.404487133026123, -0.7847540974617004, 0.6040973663330078, -0.5721331238746643, -0.5630781650543213, -0.1760088950395584, -0.504830539226532, 0.5593438148498535, 1.0494232177734375, -1.074318528175354, 1.006066918373108, -0.3137177526950836, -0.20482875406742096, 0.20961958169937134, -1.0245882272720337, 0.2741549611091614, 0.40826499462127686, 0.026422055438160896, 0.8075829148292542, 1.5450416803359985, 0.8794903755187988, -0.5139287114143372, -0.27725133299827576, -1.0134738683700562, 0.25183355808258057, -0.49464085698127747, 0.12597721815109253, -0.47507524490356445, -1.0327755212783813, 0.45414260029792786, 0.7531917691230774, 0.47853314876556396, 0.38257336616516113, 0.11797314882278442, 0.13356582820415497, 0.16285373270511627, -0.27263519167900085, -0.34088513255119324, -0.9844867587089539, -0.3788945972919464, 0.10481300950050354, 1.7926993370056152, -1.38690185546875, 0.11912083625793457, 0.8576938509941101, 0.18579645454883575, 0.6739234924316406, 0.9798490405082703, 0.6888459324836731, 0.5217723846435547, 0.6341851949691772, 0.15505743026733398, 1.8393745422363281, -0.5226299166679382, 0.09479662030935287, -0.0095054404810071, 0.7855024933815002, 0.2903209626674652, 0.2947450280189514, 0.5081595778465271, -0.3389556407928467, -0.8229750990867615, 0.7961613535881042, 0.2905220687389374, 0.16719166934490204, -1.4870926141738892, 0.6528409123420715, 2.1559700965881348, -0.994198739528656, 0.37900590896606445, 0.500220000743866, 0.3309114873409271, 0.001097107888199389, 0.4603026211261749, 1.10047447681427, 0.7197306752204895, -0.08931281417608261, -0.5808047652244568, 0.016197791323065758, -0.26333746314048767, 0.49817147850990295, -0.3271157741546631, 0.46460220217704773, 0.7629596590995789, 0.7902854084968567, 0.2061237245798111, -0.6664662957191467, 0.32888275384902954, 0.02000473253428936, -0.34056249260902405, 0.026072651147842407, 0.6102632880210876, 0.03237559273838997, 1.095798134803772, 0.32947301864624023, 1.4262791872024536, 1.2918016910552979, 0.24165590107440948, -0.5980998873710632, 0.9234838485717773, -0.8504526019096375, -0.027688562870025635, -0.6040768027305603, -0.027882235124707222, 0.24793808162212372, -0.7758379578590393, -0.3793236315250397, 0.05105399712920189, -0.232573464512825, 0.40828731656074524, 0.8943560719490051, 0.40278613567352295, 0.08655527979135513, -0.6700553297996521, -0.3044755458831787, 0.02372286282479763, -0.17746873199939728, -1.309634804725647, 0.6191542744636536, 0.13959790766239166, -0.054442763328552246, -0.3392096757888794, -1.154854416847229, 0.5011261701583862, -1.039063811302185, -0.6271315217018127, 0.27150893211364746, 1.4607839584350586, -0.1265193074941635, -0.04180234298110008, 0.5442460775375366, 0.7631320357322693, 0.7046535015106201, -0.08582413196563721, -0.22206760942935944, -0.05768187716603279, 0.9972125887870789, -0.8939781785011292, 0.28384163975715637, 0.917063295841217, -1.4213987588882446, 1.9249075651168823, -0.43095481395721436]],
//						"desc": "text embedding"
//					}]
//				}]
//			}],
//			"client_id": "example"
//		}
//
// 若存在向量信息，则返回 vec, true
// 否则返回 nil, false
func (r *TextVecResV1) Embedding() (vecs [][]float64, ok bool) {
	if r == nil || len(r.Results) == 0 {
		return
	}
	res := r.Results[0]
	if len(res.Objects) == 0 {
		return
	}
	obj := res.Objects[0]
	if len(obj.Attributes) == 0 {
		return
	}
	for _, attr := range obj.Attributes {
		if attr.Key != TextVecResV1EmbeddingAttrKey {
			continue
		}
		if len(attr.Value) == 0 {
			return
		}
		return attr.Value, true
	}
	return
}

func (t *Text2VecReqV1) OutputNames() []string {
	return StdTextVecV1ModelIO.OutputNames()
}

func (t *Text2VecReqV1) ParseResult(res map[string][]byte) (*TextVecResV1, error) {
	ret := new(TextVecResV1)
	if res == nil {
		return nil, stderr.Internal.Error("parse text2vec result from nil")
	}
	bs, ok := res[StdTextVecV1OutputRes]
	if !ok {
		return nil, stderr.Internal.Error("expected output %s not found", StdTextVecV1OutputRes)
	}
	if err := json.Unmarshal(bs, ret); err != nil {
		return nil, stderr.Wrap(err, "unmarshal output date to %T", ret)
	}
	return ret, nil
}

// Text2VecReqV2 进行文本->向量转换的模型请求结构
// 具体接口定义参见： TODO
type Text2VecReqV2 struct {
	Texts []string `json:"texts"  description:"需要进行向量化的多段文本。"`
}

func (t *Text2VecReqV2) SetInstruction(instruction string) {
	for i := range t.Texts {
		t.Texts[i] = instruction + t.Texts[i]
	}
}

func (t *Text2VecReqV2) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	bs, _ := json.Marshal(t)
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdTextVecV2InputData,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents:   NewByteTensor(bs),
		},
	}, nil
}

func (t *Text2VecReqV2) InputNames() []string {
	return StdTextVecV2ModelIO.InputNames()
}

func (t *Text2VecReqV2) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdTextVecV2OutputRes,
			Parameters: nil,
		},
	}
}

func (t *Text2VecReqV2) OutputNames() []string {
	return StdTextVecV2ModelIO.OutputNames()
}

func (t *Text2VecReqV2) ParseResult(res map[string][]byte) (*TextVecResV2, error) {
	ret := new(TextVecResV2)
	if res == nil {
		return nil, stderr.Internal.Error("parse text2vec result from nil")
	}
	bs, ok := res[StdTextVecV2OutputRes]
	if !ok {
		return nil, stderr.Internal.Error("expected output %s not found", StdTextVecV1OutputRes)
	}
	if err := json.Unmarshal(bs, ret); err != nil {
		return nil, stderr.Wrap(err, "unmarshal output date to %T", ret)
	}
	return ret, nil
}

func (t *Text2VecReqV2) GetModelInferRequest(modelName string) (*pb.ModelInferRequest, error) {
	inputs, err := t.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := t.Outputs()
	inferReq := &pb.ModelInferRequest{
		ModelName: modelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}
func (t *Text2VecReqV2) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := streamInfo.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	textVecResV2 := &TextVecResV2{}
	if err := json.Unmarshal(bytes, textVecResV2); err != nil {
		return nil, err
	}
	return GetStdInferResp(textVecResV2, inferResp), nil
	//return GetStdInferResp(textVecResV2, inferResp), nil
}
func (t *Text2VecReqV2) IsStream() bool {
	return false
}

type TextVecResV2 struct {
	Results [][]float64 `json:"results" description:"各段文本向量化后的结果，具体长度由模型决定。"`
}

type TextVecResV2F32 struct {
	Results [][]float32 `json:"results" description:"各段文本向量化后的结果，具体长度由模型决定。"`
}

func (t *TextVecResV2F32) ParseResult(res map[string][]byte) error {
	if res == nil {
		return stderr.Internal.Error("parse text2vec result from nil")
	}
	bs, ok := res[StdTextVecV2OutputRes]
	if !ok {
		return stderr.Internal.Error("expected output %s not found", StdTextVecV1OutputRes)
	}
	if err := json.Unmarshal(bs, t); err != nil {
		return err
	}
	return nil
}

type GeneralInferReq map[string][]byte

func (g *GeneralInferReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	if g == nil {
		return nil, stderr.Internal.Error("general infer req is nil")
	}
	inputs := make([]*pb.ModelInferRequest_InferInputTensor, 0)
	// 与InputNames()保持一致顺序
	for _, name := range g.InputNames() {
		inputs = append(inputs, &pb.ModelInferRequest_InferInputTensor{
			Name:     name,
			Datatype: "BYTES",
			Shape:    []int64{1},
			Contents: NewByteTensor((*g)[name]),
		})
	}
	return inputs, nil
}

func (g *GeneralInferReq) InputNames() []string {
	if g == nil {
		return nil
	}
	var names []string
	for name := range *g {
		names = append(names, name)
	}
	// 固定顺序
	sort.Strings(names)
	return names
}

func (g *GeneralInferReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return nil
}

func (g *GeneralInferReq) OutputNames() []string {
	return nil
}

// StdCVResp CV模型推理结果，
type StdCVResp pb.InferRsp

// StdCVReq 用于常用的CV模型推理，包括目标检测、分类、分割等
type StdCVReq pb.InferReq

func (s *StdCVReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	meta, data, err := s.Tensor()
	if err != nil {
		return nil, stderr.Wrap(err, "get cv request tensor")
	}
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdCVInputData,
			Datatype:   EnsembleInferInputDataType,
			Shape:      []int64{int64(len(data.BytesContents))},
			Parameters: nil,
			Contents:   data,
		},
		{
			Name:       StdCVInputMeta,
			Datatype:   EnsembleInferInputDataType,
			Shape:      EnsembleInferMetaShape,
			Parameters: nil,
			Contents:   meta,
		},
	}, nil
}

func (s *StdCVReq) InputNames() []string {
	return StdCVModelIO.InputNames()
}

func (s *StdCVReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdCVOutputRes,
			Parameters: nil,
		},
	}
}

func (s *StdCVReq) OutputNames() []string {
	return StdCVModelIO.OutputNames()
}

// Tensor 将推理请求转换为Ensemble模型支持的Tensor格式
func (s *StdCVReq) Tensor() (meta, data *pb.InferTensorContents, err error) {
	if s == nil || len(s.Params) == 0 {
		return nil, nil, EmptyInferRequestErr
	}

	// export data from every infer param
	raws := make([][]byte, len(s.Params))
	for i, param := range s.Params {
		raws[i], err = base64.StdEncoding.DecodeString(param.Data)
		if err != nil {
			return nil, nil, Base64DecodeErr
		}
		param.Data = ""
	}

	data = NewByteTensor(raws...)
	metaJson, _ := json.Marshal(s)
	meta = NewByteTensor(metaJson)
	return
}
func (s *StdCVReq) IsStream() bool {
	return false
}
func (s StdCVReq) GetModelInferRequest(modelName string) (*pb.ModelInferRequest, error) {
	inputs, err := s.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := s.Outputs()
	inferReq := &pb.ModelInferRequest{
		ModelName: modelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}

func (s StdCVReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := streamInfo.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	// 依据CV模型的标准响应进行数据解析。
	// TODO StdCVResp中存在value字段需要设置为any类型,
	var js interface{}
	if err := json.Unmarshal(bytes, &js); err != nil {
		return nil, stderr.Wrap(err, "failed to do Unmarshal, the str is %s", string(bytes))
	}
	return GetStdInferResp(js, inferResp), nil
}

func NewByteTensor(bs ...[]byte) *pb.InferTensorContents {
	return &pb.InferTensorContents{
		BytesContents: bs,
	}
}

func CosineSimilarity(a []float64, b []float64) float64 {
	count := 0
	alen := len(a)
	blen := len(b)
	if alen > blen {
		count = alen
	} else {
		count = blen
	}
	sumA := 0.0
	s1 := 0.0
	s2 := 0.0
	for k := 0; k < count; k++ {
		if k >= alen {
			s2 += math.Pow(b[k], 2)
			continue
		}
		if k >= blen {
			s1 += math.Pow(a[k], 2)
			continue
		}
		sumA += a[k] * b[k]
		s1 += math.Pow(a[k], 2)
		s2 += math.Pow(b[k], 2)
	}
	if s1 == 0 || s2 == 0 {
		return 0.0
	}
	return sumA / (math.Sqrt(s1) * math.Sqrt(s2))
}

// EntityRecognitionReq  文本类型，实体识别推理请求
type EntityRecognitionReq struct {
	Text string `json:"text" description:"输入文本"`
}

func (r *EntityRecognitionReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       "TEXT",
			Datatype:   "BYTES",
			Shape:      []int64{1, 1},
			Parameters: nil,
			Contents: &pb.InferTensorContents{
				BytesContents: [][]byte{[]byte(r.Text)},
			},
		},
	}, nil
}
func (r *EntityRecognitionReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       "entity_indexs",
			Parameters: nil,
		},
	}
}

func (r *EntityRecognitionReq) IsStream() bool {
	return false
}
func (r *EntityRecognitionReq) GetModelInferRequest(modelName string) (*pb.ModelInferRequest, error) {
	inputs, err := r.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := r.Outputs()

	inferReq := &pb.ModelInferRequest{
		ModelName: modelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}
func (r *EntityRecognitionReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := streamInfo.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	entityRecognitionResp := &EntityRecognitionResp{}
	if err := json.Unmarshal(bytes, entityRecognitionResp); err != nil {
		return nil, err
	}
	return GetStdInferResp(entityRecognitionResp, inferResp), nil
}

// EntityRecognitionResp  文本类型，实体识别推理结果
type EntityRecognitionResp struct {
	Entity          [][]int        `json:"entity"  description:"识别出的多个实体对象，在文本中的起始与结束地址"`
	Label           []string       `json:"label" description:"各实体的标签名"`
	EntityStatistic map[string]int `json:"entity_statistic" description:"各标签出现次数统计"`
}

// OpenAiChatReq openai请求体、响应体--对话，文本生成
type OpenAiChatReq struct {
	Messages         []MultimodalMessageItem `json:"messages" description:"到目前为止，构成对话的消息列表"`
	Model            string                  `json:"model" description:"要使用的模型的ID"`
	Stream           bool                    `json:"stream"  description:"响应方式，默认为false，采用json返回。如果设置为true，采用流式消息响应，流由data:[DONE]消息终止"`
	StreamOptions    *StreamOptions          `json:"stream_options,omitempty"  description:"流式请求控制参数"`
	FrequencyPenalty float64                 `json:"frequency_penalty,omitempty"  description:"频率惩罚，介于-2.0和2.0之间的数字"`
	LogitBias        map[string]any          `json:"logit_bias,omitempty"  description:"逻辑偏置，修改指定标记出现在完成中的可能性"`
	MaxTokens        int                     `json:"max_tokens,omitempty"  description:"生成完整聊天的最大令牌数"`
	N                int                     `json:"n,omitempty"  description:"要为每条输入消息生成多少个聊天完成选项，默认值为1"`
	PresencePenalty  float64                 `json:"presence_penalty,omitempty"  description:"存在惩罚，介于-2.0和2.0之间的数字。"`
	ResponseFormat   *ChatResponseFormat     `json:"response_format,omitempty"  description:"用于指导模型进行json输出时所遵循的格式"`
	Seed             int                     `json:"seed,omitempty"  description:"种子，此功能处于测试阶段"`
	Stop             []string                `json:"stop,omitempty" description:"最多4个序列，API将停止生成更多令牌"`
	Temperature      float64                 `json:"temperature,omitempty"  description:"采样温度，介于0和2之间。较高的（如0.8）将使输出更加随机，而较低的值（如0.2）将使其更具集中性和确定性"`
	TopP             float64                 `json:"top_p,omitempty"  description:"温度采样的替代方法，称为核采样，其中模型考虑具有top_p概率质量的标记的结果。不建议同时更改temperature和top_p"`
	Tools            []Tool                  `json:"tools,omitempty"  description:"模型可能调用的工具列表"`
	ToolChoice       any                     `json:"tool_choice,omitempty"  description:"控制模型调用的函数（如果有）"`
	User             string                  `json:"user,omitempty"  description:"代表最终用户的唯一标识符，"`
	FunctionCall     any                     `json:"function_call,omitempty"  description:"已弃用，由tool_choice取代"`
	Functions        []FunctionDefinition    `json:"functions,omitempty"  description:"已弃用，由tools取代"`
	Others           Others                  `json:"-"`
}

type Others struct {
	DisableThink bool //去除思考内容
}

type StreamOptions struct {
	IncludeUsage bool `json:"include_usage,omitempty"  description:"是否返回流式的token统计"`
}

type ChatResponseFormat struct {
	Type       string          `json:"type,omitempty"`
	JsonSchema *JsonSchemaDesc `json:"json_schema,omitempty"`
}

type JsonSchemaDesc struct {
	Name        string  `json:"name,omitempty"`
	Description string  `json:"description,omitempty"`
	Strict      bool    `json:"strict,omitempty"`
	Schema      *Schema `json:"schema,omitempty"`
}

type Schema struct {
	Type                 string      `json:"type,omitempty"`
	Properties           interface{} `json:"properties,omitempty"`
	Required             []string    `json:"required,omitempty"`
	AdditionalProperties bool        `json:"additionalProperties,omitempty"`
}

func (o *OpenAiChatReq) IsStream() bool {
	return o.Stream
}
func (o *OpenAiChatReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	LLMChatReq, err := o.Cvt2LLMChatReq()
	if err != nil {
		return nil, err
	}
	return LLMChatReq.GetModelInferRequest(innerModelName)
}
func (o *OpenAiChatReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}

	var openAiChatResp OpenAiChatResp
	content := string(info.ModelInferResponse.RawOutputContents[0][4:])

	// json格式返回 strings.TrimSpace(os.Getenv("RESPONSE_JSON")) == "1"
	err := json.Unmarshal([]byte(content), &openAiChatResp)
	if err == nil && openAiChatResp.Id != "" {
		return openAiChatResp, nil
	}

	// 原始格式返回
	openAiChatResp = OpenAiChatResp{
		Id:      info.RequestId,
		Created: info.Created,
		Model:   o.Model,
		Choices: []*Choice{{Index: 0, FinishReason: info.FinishReason}},
	}
	if info.IsStream {
		openAiChatResp.Object = RespObjectChunkChat
		openAiChatResp.Choices[0].Delta = &MessageItem{Role: RoleAssistant, Content: content}
	} else {
		openAiChatResp.Object = RespObjectChat
		openAiChatResp.Choices[0].Message = &MessageItem{Role: RoleAssistant, Content: content}
	}
	return openAiChatResp, nil
}

func (o *OpenAiChatReq) Cvt2LLMChatReq() (*LLMChatReq, error) {
	req := &LLMChatReq{
		Stream: o.Stream,
	}
	if len(o.Messages) == 0 {
		return nil, errors.New("the length of messages can not be 0")
	}
	// 1、区分多模态和正常的对话 准备query、history等参数
	switch o.Messages[0].Content.(type) {
	case string:
		err := o.PrepareQuery(req)
		if err != nil {
			return nil, err
		}
	default:
		err := o.PrepareQueryMultimodal(req)
		if err != nil {
			return nil, err
		}
	}
	// 2、设置推理参数
	params := new(LLMParams)

	// 2.2 重新覆盖
	params.Temperature = o.Temperature
	params.MaxLength = o.MaxTokens
	params.TopP = o.TopP
	params.RepetitionPenalty = o.FrequencyPenalty
	params.NumBeams = o.N
	params.StopWords = o.Stop
	rf := o.ResponseFormat
	if rf != nil {
		if rf.JsonSchema == nil || rf.JsonSchema.Schema == nil {
			return nil, stderr.Error("must set the field of schema ")
		}
		params.GuidedJson = stdsrv.AnyToString(rf.JsonSchema.Schema)
	}

	if o.Temperature != 1.0 || o.TopP != 1.0 {
		params.DoSample = true
	}
	if o.Stop != nil {
		params.EarlyStopping = true
	}
	req.Params = params.AsMap()

	return req, nil
}

var CommonImageFileExts = []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".webp", ".ico"}
var CommonVideoFileExts = []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".webm", ".mpeg", ".mpg", ".3gp"}

type FileType = string

const (
	ImageFile  FileType = "image"
	VideoFile  FileType = "video"
	NoneFile   FileType = "none"
	OthersFile FileType = "others"
)

func fileType(fileName string) FileType {
	if fileName == "" {
		return NoneFile
	}
	if isImageFile(fileName) {
		return ImageFile
	}
	if isVideoFile(fileName) {
		return NoneFile
	}
	return OthersFile
}
func isImageFile(fileName string) bool {
	ext := filepath.Ext(fileName)
	for _, e := range CommonImageFileExts {
		if e == ext {
			return true
		}
	}
	return false
}

func isVideoFile(fileName string) bool {
	ext := filepath.Ext(fileName)
	for _, e := range CommonVideoFileExts {
		if e == ext {
			return true
		}
	}
	return false
}

// constructImageMessage 构造图像理解的openai请求message，仅支持图片 sfs协议的url
func constructImageMessage(query string, imageFileUrl string) MultimodalMessageItem {
	sfsFilePath := stdfs.SFSFilePath(imageFileUrl)
	httpUrl := imageFileUrl
	if sfsFilePath.IsValid() {
		httpUrl = sfsFilePath.ToHttpUrl()
	}
	imageContentItems := []MultimodalContentItem{
		{
			Type: "text",
			Text: query,
		},
		{
			Type: "image_url",
			ImageUrl: map[string]string{
				"url": httpUrl,
			},
		},
	}
	return MultimodalMessageItem{
		Role:    "user",
		Content: imageContentItems,
	}
}

// constructVideoMessage constructs a video message with the given query and video file URL.
func constructVideoMessage(query string, videoFileUrl string) MultimodalMessageItem {
	sfsFilePath := stdfs.SFSFilePath(videoFileUrl)
	httpUrl := videoFileUrl
	if sfsFilePath.IsValid() {
		httpUrl = sfsFilePath.ToHttpUrl()
	}
	videoContentItems := []MultimodalContentItem{
		{
			Type: "text",
			Text: query,
		},
		{
			Type:      "video",
			Video:     httpUrl,
			Fps:       0.1,
			MaxPixels: 151200,
		},
	}
	return MultimodalMessageItem{
		Role:    "user",
		Content: videoContentItems,
	}
}
func (r *LLMChatReq) Cvt2OpenaiChatReq() (*OpenAiChatReq, error) {
	paramsMap := new(LLMParams)
	if r.Params != nil {
		json.Unmarshal(stdsrv.AnyToBytes(r.Params), paramsMap)
	}

	var messages []MultimodalMessageItem
	if r.Prompt != "" {
		messages = append(messages, MultimodalMessageItem{Role: OpenaiSystemMsg, Content: r.Prompt})
	}

	for _, item := range r.History {
		question := item.Q
		answer := item.A
		messages = append(messages, MultimodalMessageItem{Role: OpenaiUserMsg, Content: question})
		messages = append(messages, MultimodalMessageItem{Role: OpenaiAssistantMsg, Content: answer})
	}
	switch fileType(r.File) {
	case ImageFile:
		messages = append(messages, constructImageMessage(r.Query, r.File))
	case VideoFile:
		messages = append(messages, constructVideoMessage(r.Query, r.File))
	default:
		//兼容之前的代码，对于其他文件不做处理
		if r.Query != "" {
			messages = append(messages, MultimodalMessageItem{Role: OpenaiUserMsg, Content: r.Query})
		}
	}
	if len(messages) == 0 {
		return nil, stderr.Errorf("the len of msg is 0")
	}

	var chatResponseFormat *ChatResponseFormat
	if paramsMap.GuidedJson != "" {
		Schema := new(Schema)
		if err := json.Unmarshal([]byte(paramsMap.GuidedJson), Schema); err != nil {
			return nil, stderr.Wrap(err, "failed to do json.Unmarshal")
		}
		chatResponseFormat = &ChatResponseFormat{
			Type: "json_schema",
			JsonSchema: &JsonSchemaDesc{
				Name:        "json_schema",
				Description: "json_schema",
				Strict:      false,
				Schema:      Schema,
			},
		}
	}

	openaiChatReq := &OpenAiChatReq{
		Messages:         messages,
		Model:            DefaultInnerModelName,
		Stream:           r.Stream,
		N:                paramsMap.NumBeams,
		FrequencyPenalty: paramsMap.RepetitionPenalty,
		MaxTokens:        paramsMap.MaxLength,
		Temperature:      paramsMap.Temperature,
		TopP:             paramsMap.TopP,
		Stop:             paramsMap.StopWords,
		ResponseFormat:   chatResponseFormat,
	}
	return openaiChatReq, nil
}

// PrepareQuery 正在对话,设置params之外的字段
func (o *OpenAiChatReq) PrepareQuery(req *LLMChatReq) error {
	// 构建历史回答
	messages := o.Messages
	prevQ := ""
	for _, msg := range messages {
		stringContent, ok := msg.Content.(string)
		if !ok {
			return errors.New("message.Content should be string")
		}
		switch msg.Role {
		case OpenaiSystemMsg:
			if req.Prompt != "" {
				stdlog.Warnf("system prompt '%s' will be override by '%s'", req.Prompt, msg.Content)
			}
			req.Prompt = stringContent
		case OpenaiUserMsg:
			// 记录前面问答论次中的用户问题
			prevQ = stringContent
		case OpenaiAssistantMsg:
			// 模型回答, 找到匹配的用户问题并消耗
			req.History = append(req.History, QAItem{
				Q: prevQ,
				A: stringContent,
			})
			prevQ = ""
		default:
			stdlog.Warnf("unsupported message type: %s with content %s", msg.Role, msg.Content)
		}
	}
	req.Query = prevQ
	if req.Query == "" && req.Prompt == "" {
		stderr.Errorf("the query and prompt is nil")
	}
	return nil
}

// PrepareQueryMultimodal
// messages下的content字段
//
//	正常对话时为string
//	多模态时如下,可传图像url或base64编码
//
// "messages": [
//
//	{
//	  "role": "user",
//	  "content": [
//	    {
//	      "type": "text",
//	      "text": "What’s in this image?"
//	    },
//	    {
//	      "type": "image_url",
//	      "image_url": {
//	        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
//	        "detail": "high"
//	      }
//	    },
//	    {
//	      "type": "image_url",
//	      "image_url": {
//	        "url": f"data:image/jpeg;base64,{base64_image}"
//	      }
//	    }
//	  ]
//	}
//
// ]
// PrepareQueryMultimodal
// 多模态对话,设置params之外的字段,暂时仅考虑最简单的情况。
func (o *OpenAiChatReq) PrepareQueryMultimodal(req *LLMChatReq) error {

	// 多模态消息 兼容最初单个文件
	for _, msg := range o.Messages {
		contentItems := make([]*MultimodalContentItem, 0)
		if err := json.Unmarshal(stdsrv.AnyToBytes(msg.Content), &contentItems); err != nil {
			return stderr.Errorf("failed to get MultimodalContentItems")
		}
		for _, contentItem := range contentItems {
			if contentItem.Type == "text" {
				req.Query = contentItem.Text
			}
			if contentItem.Type == "image_url" && contentItem.ImageUrl != nil {
				req.File = contentItem.ImageUrl["url"]
			}
		}
	}

	// 原始消息,content字段兼容各种格式
	req.Messages = o.Messages
	return nil
}

type OpenAiChatResp struct {
	Id                string    `json:"id" description:"聊天完成的唯一标识符。每个区块都具有相同的ID。"`
	Choices           []*Choice `json:"choices" description:"聊天完成选项列表"`
	Created           int64     `json:"created" description:"创建聊天完成时的时间戳"`
	Model             string    `json:"model" description:"用于聊天完成的模型"`
	Object            string    `json:"object" description:"代表返回的对象类型，为chat.completion.chunk，或者chat.completion。代表流式和非流式"`
	SystemFingerprint string    `json:"system_fingerprint,omitempty" description:"此指纹表示运行模型时使用的后端配置,非流式响应有效"`
	Usage             *Usage    `json:"usage,omitempty" description:"完成请求的token使用情况统计信息,流式请求携带在最后的消息"`
}

const (
	RespObjectChunkChat = "chat.completion.chunk"
	RespObjectChat      = "chat.completion"

	RoleAssistant = "assistant"
)

func (o *OpenAiChatResp) GetInferResult() (string, error) {
	res := ""
	for _, c := range o.Choices {
		switch o.Object {
		case RespObjectChunkChat:
			if c.Delta != nil {
				res = res + c.Delta.Content
			}
		case RespObjectChat:
			if c.Message != nil {
				res = res + c.Message.Content
			}
		}
	}
	return res, nil
}
func (o *OpenAiChatResp) GetReasoningContent() (string, error) {
	res := ""
	for _, c := range o.Choices {
		switch o.Object {
		case RespObjectChunkChat:
			if c.Delta != nil {
				res = res + c.Delta.ReasoningContent
			}
		case RespObjectChat:
			if c.Message != nil {
				res = res + c.Message.ReasoningContent
			}
		}
	}
	return res, nil
}

// OpenaiMsgRole OpenAI 对于Messages中的角色的定义
// 参考：https://platform.openai.com/docs/api-reference/chat/create#chat-create-messages
type OpenaiMsgRole = string

const (
	OpenaiSystemMsg    OpenaiMsgRole = "system"    // 对应系统提示词
	OpenaiUserMsg      OpenaiMsgRole = "user"      // 用户输入
	OpenaiAssistantMsg OpenaiMsgRole = "assistant" // 模型返回
	OpenaiToolMsg      OpenaiMsgRole = "tool"      // 工具调用信息
	OpenaiFunctionMsg  OpenaiMsgRole = "function"  // deprecated
)

// MultimodalMessageItem openai-chatCompletion多模态支持-图像理解，content结构发生变化
type MultimodalMessageItem struct {
	Role             OpenaiMsgRole `json:"role" description:"此消息作者的角色。"`
	Content          interface{}   `json:"content" description:"消息的内容。"`
	ReasoningContent string        `json:"reasoning_content,omitempty" yaml:"reasoning_content" description:"推理内容"`
	ToolCalls        []ToolCall    `json:"tool_calls,omitempty" description:"模型生成的工具调用"`
	FunctionCall     *FunctionCall `json:"functionCall,omitempty" description:"已弃用，并替换为tool_calls"`
}

// 多模态时,消息的content字段可为string类型或如下结构
//	  "content": [
//	    {
//	      "type": "text",
//	      "text": "What’s in this image?"
//	    },
//	    {
//	      "type": "image_url",
//	      "image_url": {
//	        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
//	        "detail": "high"
//	      }
//	    }]

type MultimodalContentItem struct {
	Type     string            `json:"type,omitempty" description:"消息的类型,openai目前仅支持text与image_url"`
	Text     string            `json:"text,omitempty" description:"type为text时消息的具体内容"`
	ImageUrl map[string]string `json:"image_url,omitempty" description:"type为image_url时消息的具体内容"`

	// hf 格式
	Image         string      `json:"image,omitempty" description:"type为image时,存储单张图片"`
	Video         interface{} `json:"video,omitempty" description:"type为video时,存储视频文件或多张图片"`
	Fps           float64     `json:"fps,omitempty" description:"fps"`
	MinPixels     int         `json:"min_pixels,omitempty" description:"最小像素个数"`
	MaxPixels     int         `json:"max_pixels,omitempty" description:"最大像素个数"`
	ResizedWidth  int         `json:"resized_width,omitempty" description:"图片宽度"`
	ResizedHeight int         `json:"resized_height,omitempty" description:"图片高度"`
}

type MessageItem struct {
	Role             OpenaiMsgRole `json:"role" description:"此消息作者的角色。"`
	Content          string        `json:"content" description:"消息的内容。"`
	ReasoningContent string        `json:"reasoning_content,omitempty" yaml:"reasoning_content" description:"推理内容"`
	ToolCalls        []ToolCall    `json:"tool_calls,omitempty" description:"模型生成的工具调用"`
	FunctionCall     *FunctionCall `json:"functionCall,omitempty" description:"已弃用，并替换为tool_calls"`
}

type Tool struct {
	Type     string             `json:"type" description:"The type of the tool. Currently, only function is supported."`
	Function FunctionDefinition `json:"function"`
}
type ToolCall struct {
	Index    *int          `json:"index,omitempty"`
	Id       string        `json:"id,omitempty"`
	Type     string        `json:"type"`
	Function *FunctionCall `json:"function"`
}

/*
	{
	    "name": "get_current_weather",
	    "description": "Get the current weather in a given location",
	    "parameters": {
	        "type": "object",
	        "properties": {
	            "location": {
	                "type": "string",
	                "description": "The city and state, e.g. San Francisco, CA",
	            },
	            "unit": {"type": "string"},
	        },
	        "required": ["location"],
	    },
	}
*/
type FunctionDefinition struct {
	Name        string             `json:"name"`
	Description string             `json:"description,omitempty"`
	Parameters  FunctionParameters `json:"parameters"`
}

type ParameterType string

const (
	ParameterTypeString  ParameterType = "string"
	ParameterTypeNumber  ParameterType = "number"
	ParameterTypeInteger ParameterType = "integer"
	ParameterTypeBoolean ParameterType = "boolean"
	ParameterTypeObject  ParameterType = "object"
	ParameterTypeArray   ParameterType = "array"
)

type FunctionParameters struct {
	Type       ParameterType                `json:"type" `
	Properties map[string]ParameterProperty `json:"properties"`
	Required   []string                     `json:"required"`
}

type ParameterProperty struct {
	Type        ParameterType `json:"type"`
	Description string        `json:"description,omitempty"`
}

type FunctionCall struct {
	Name      string `json:"name,omitempty"`
	Arguments string `json:"arguments,omitempty"`
}

type Choice struct {
	Index        int          `json:"index" description:"选项列表中选项的索引。"`
	Message      *MessageItem `json:"message,omitempty" description:"模型生成的聊天完成消息。非流式时使用"`
	Delta        *MessageItem `json:"delta,omitempty" description:"流式响应时使用的聊天完成增量。具体结构和message相同"`
	FinishReason string       `json:"finish_reason" description:"模型停止生成令牌的原因。" `
}
type Usage = openai.Usage
type OpenAiTextVectorReq struct {
	Input          []string `json:"input" description:"输入的文本。"`
	Model          string   `json:"model" description:"要使用的模型的ID。"`
	EncodingFormat string   `json:"encoding_format" description:"返回的向量化数据格式，默认为float"`
	User           string   `json:"user" description:"代表最终用户的唯一标识符"`
}

func (o *OpenAiTextVectorReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	text2VecReqV2 := &Text2VecReqV2{
		Texts: o.Input,
	}
	return text2VecReqV2.GetModelInferRequest(innerModelName)
}

func (o *OpenAiTextVectorReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := info.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	textVecResV2 := &TextVecResV2{}
	if err := json.Unmarshal(bytes, textVecResV2); err != nil {
		return nil, err
	}
	var data []Embedding
	for i, _ := range textVecResV2.Results {
		embedding := Embedding{
			Object:    "embedding",
			Index:     i,
			Embedding: textVecResV2.Results[i],
		}
		data = append(data, embedding)
	}
	openAiTextVectorResp := &OpenAiTextVectorResp{
		Object: "list",
		Data:   data,
		Model:  o.Model,
	}
	return openAiTextVectorResp, nil
}
func (o *OpenAiTextVectorReq) IsStream() bool {
	return false
}

type OpenAiAny2VectorReq struct {
	Input          any    `json:"input" description:"输入的文本。"`
	Model          string `json:"model" description:"要使用的模型的ID。"`
	EncodingFormat string `json:"encoding_format" description:"返回的向量化数据格式，默认为float"`
	User           string `json:"user" description:"代表最终用户的唯一标识符"`
}
type OpenAiAny2VectorResp = OpenAiTextVectorResp

func (o *OpenAiAny2VectorReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	req := &Any2VecReq{
		Texts: o.Input,
	}
	return req.GetModelInferRequest(innerModelName)
}

func (o *OpenAiAny2VectorReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := info.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	textVecResV2 := &Any2VecResp{}
	if err := json.Unmarshal(bytes, textVecResV2); err != nil {
		return nil, err
	}
	var data []Embedding
	for i, _ := range textVecResV2.Results {
		embedding := Embedding{
			Object:    "embedding",
			Index:     i,
			Embedding: textVecResV2.Results[i],
		}
		data = append(data, embedding)
	}
	openAiTextVectorResp := &OpenAiTextVectorResp{
		Object: "list",
		Data:   data,
		Model:  o.Model,
	}
	return openAiTextVectorResp, nil
}
func (o *OpenAiAny2VectorReq) IsStream() bool {
	return false
}

func (o *OpenAiTextVectorReq) Cvt2StdTextVectorReq() (*Text2VecReqV2, error) {
	text2VecReqV2 := &Text2VecReqV2{
		Texts: o.Input,
	}
	return text2VecReqV2, nil
}

type OpenAiTextVectorResp struct {
	Object string      `json:"object" description:"返回的对象类型，固定为list，代表有多个具体对象"`
	Data   []Embedding `json:"data" description:"各段文本对应的响应数据"`
	Model  string      `json:"model" description:"使用的模型ID"`
	Usage  *Usage      `json:"usage,omitempty" description:"完成请求的使用情况统计信息。"`
}

func (o *OpenAiTextVectorResp) GetFloat64Slice() [][]float64 {
	res := make([][]float64, len(o.Data))
	for i, data := range o.Data {
		res[i] = data.Embedding
	}
	return res
}

type Embedding struct {
	Index     int       `json:"index" description:"响应对象的索引，和请求中的文本对应。"`
	Embedding []float64 `json:"embedding" description:"请求文本对应的向量化数据，是浮点数的列表，长度取决于使用的模型"`
	Object    string    `json:"object" description:"对象类型，始终为embedding"`
}

type TimestampType string
type AudioResponseFormatType string

const (
	TimestampTypeWord                  TimestampType           = "word"
	TimestampTypeSegment               TimestampType           = "segment"
	AudioResponseFormatTypeText        AudioResponseFormatType = "text"
	AudioResponseFormatTypeJson        AudioResponseFormatType = "json"
	AudioResponseFormatTypeVerboseJson AudioResponseFormatType = "verbose_json"
)

// OpenAiAudioTransReq openai请求体、响应体--语音转文本
type OpenAiAudioTransReq struct {
	File                   []byte                  `json:"file" description:"要转录的音频文件对象（不是文件名）。支持flac、mp3、mp4、mpeg等格式"`
	Model                  string                  `json:"model,omitempty" description:"模型ID,目前仅whisper-1可用"`
	Language               string                  `json:"language" description:"输入音频的语言，以ISO-639-1格式提供输入语言将提高准确性和延迟。"`
	Prompt                 string                  `json:"prompt" description:"一个可选提示文本，用于指导模型的样式或继续上一个音频片段。提示应与音频语言匹配。"`
	ResponseFormat         AudioResponseFormatType `json:"response_format,omitempty" description:"脚本输出的格式，默认为还支持text，还支持json、verbose_json等"`
	Temperature            float64                 `json:"temperature" description:"采样温度，介于0和1之间。较高的值将使输出更加随机，而较低的值将使其更具集中性和确定性。"`
	TimestampGranularities []TimestampType         `json:"timestamp_granularities,,omitempty" description:"时间戳信息,支持word、segment中的一个或两个"`
}

func (o *OpenAiAudioTransReq) IsStream() bool {
	return false
}
func (o *OpenAiAudioTransReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	StdAudioTransReq, err := o.Cvt2StdAudioTransReq()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do Cvt2StdAudioTransReq")
	}
	return StdAudioTransReq.GetModelInferRequest(innerModelName)
}

func (o *OpenAiAudioTransReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	text := string(info.ModelInferResponse.RawOutputContents[0][4:])
	openAiAudioTransResp := &OpenAiAudioTransResp{
		Text: text,
	}
	return openAiAudioTransResp, nil
}

func (o *OpenAiAudioTransReq) Cvt2StdAudioTransReq() (*StdAudioTransReq, error) {
	// 设置推理参数
	params := make(map[string]interface{})
	if o.Language != "" {
		params["language"] = o.Language
	}
	if o.ResponseFormat != "" {
		params["response_format"] = o.ResponseFormat
	}
	if o.Temperature != 0.0 {
		params["temperature"] = o.Temperature
	}
	// 将文件内容转为Base64编码
	base64Encoded := base64.StdEncoding.EncodeToString(o.File)
	StdLLMInferReq := StdLLMInferReq{
		LLMChatReq: LLMChatReq{
			Stream: false,
			Stop:   false,
			Prompt: o.Prompt,
			Params: params,
			File:   base64Encoded,
		},
	}
	return &StdAudioTransReq{StdLLMInferReq}, nil
}

type Word struct {
	Word  string  `json:"word"`
	Start float64 `json:"start"`
	End   float64 `json:"end"`
}

type Segment struct {
	Id                int64   `json:"id"`
	Seek              int64   `json:"seek"`
	Start             float64 `json:"start"`
	End               float64 `json:"end"`
	Text              string  `json:"text"`
	Tokens            []int64 `json:"tokens"`
	Temperature       float64 `json:"temperature"`
	AvgLogprob        float64 `json:"avg_logprob"`
	CompressionRation float64 `json:"compression_ration"`
	NoSpeechProb      float64 `json:"no_speech_prob"`
}
type OpenAiAudioTransResp struct {
	Task     string     `json:"task,omitempty"`
	Language string     `json:"language,omitempty" `
	Duration float64    `json:"duration,omitempty"`
	Text     string     `json:"text" description:"由音频转录生成的文本"`
	Words    []*Word    `json:"words,omitempty"`
	Segments []*Segment `json:"segments,omitempty"`
}

// ImageList openai请求体、响应体--图像相关的返回结构
type ImageList struct {
	Created int64   `json:"created,omitempty" description:"图像生成完成的时间"`
	Data    []Image `json:"data,omitempty" description:"具体的各图像数据"`
}
type Image struct {
	URL           string `json:"url,omitempty" description:"生成的图像的URL"`
	B64JSON       string `json:"b64_json,omitempty" description:"生成的图像的base64编码的JSON"`
	RevisedPrompt string `json:"revised_prompt,omitempty" description:"用于生成图像的提示"`
}

// OpenAiImageGenReq openai请求体、响应体--图像生成
type OpenAiImageGenReq struct {
	Prompt         string                 `json:"prompt" description:"提示词，必填，所需图像的文本描述。"`
	Model          string                 `json:"model,omitempty" description:"用于图像生成的模型，默认为 dall-e-2"`
	N              int                    `json:"n,omitempty" description:"要生成的图像数。必须介于1和10之间。"`
	Quality        string                 `json:"quality,omitempty" description:"生成的图像的质量。"`
	ResponseFormat ImageGenResponseFormat `json:"response_format,omitempty" description:"返回生成的图像的格式，必须是url或b64_json之一，默认为url"`
	Size           string                 `json:"size,omitempty" description:"生成的图像的大小，支持256x256、512x512、1024x1024，默认为1024x1024"`
	Style          string                 `json:"style,omitempty" description:"生成的图像的样式，必须是vivid或natural之一"`
	User           string                 `json:"user,omitempty" description:"代表最终用户的唯一标识符"`
}

func (o *OpenAiImageGenReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	StdImageGenReq, err := o.Cvt2StdImageGenReq()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do Cvt2StdImageGenReq")
	}
	return StdImageGenReq.GetModelInferRequest(innerModelName)
}

// 要求格式固定为AxB,并且为16的倍数
func parseWidthAndHeight(size string) (width int, height int, err error) {
	strs := strings.Split(size, imageSizeSeparator)
	if len(strs) != 2 {
		err = errors.New(fmt.Sprintf("the format of %s is error, must be AxB", size))
		return
	}
	widthStr := strs[0]
	heightStr := strs[1]
	if width, err = strconv.Atoi(widthStr); err != nil {
		return
	}

	if height, err = strconv.Atoi(heightStr); err != nil {
		return
	}
	// 将width height 提升为16的倍数
	width = (width + 15) >> 4 << 4
	height = (height + 15) >> 4 << 4
	stdlog.Infof("change width, height to %d, %d", width, height)
	return
}

// ParseInferResp 将推理结果由streamInfo转为所期望的格式
// 如果为std实现方式，解析为标准格式-StdInferResp
// 如果为openai实现方式，则根据需要进行解析
func (o *OpenAiImageGenReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := info.ModelInferResponse
	bytes := inferResp.RawOutputContents[0][4:]
	var image Image
	if err := json.Unmarshal(bytes, &image); err != nil {
		return nil, err
	}
	image.RevisedPrompt = o.Prompt
	openAiImageGenResp := &OpenAiImageGenResp{
		Created: info.Created,
		Data: []Image{
			image,
		},
	}
	return openAiImageGenResp, nil
}
func (o *OpenAiImageGenReq) IsStream() bool {
	return false
}
func (o *OpenAiImageGenReq) Cvt2StdImageGenReq() (*StdImageGenReq, error) {
	// 设置图像生成的参数
	imageGenParams := new(ImageGenParams)
	if o.Size != "" {
		width, height, err := parseWidthAndHeight(o.Size)
		if err != nil {
			return nil, err
		}
		imageGenParams.Width = width
		imageGenParams.Height = height
	}
	// 生成图片的格式,限定为url/b64_json,优先支持为url
	switch o.ResponseFormat {
	case ImageGenResponseFormatB64Json:
		imageGenParams.ResponseFormat = ImageGenResponseFormatB64Json
	default:
		imageGenParams.ResponseFormat = ImageGenResponseFormatUrl
	}

	StdImageGenReq := &StdImageGenReq{
		StdLLMInferReq{
			LLMChatReq: LLMChatReq{
				Query:  o.Prompt,
				Stream: false,
				Stop:   false,
				Params: imageGenParams.AsMap(),
			},
		},
	}
	return StdImageGenReq, nil
}

type OpenAiImageGenResp ImageList

// OpenAiImageEditReq openai请求体、响应体--图像编辑
type OpenAiImageEditReq struct {
	Image          string `json:"image" description:"必填，要编辑的图像。必须是有效的PNG文件，小于4MB，并且正方形。"`
	Prompt         string `json:"prompt" description:"所需图像的文本描述。最大长度为1000个字符。"`
	Mask           string `json:"mask,omitempty" description:"一个附加图像，必须是有效的PNG文件，小于4MB，并且尺寸与image相同。"`
	Model          string `json:"model,omitempty" description:"用于图像生成的模型。目前仅dall-e-2支持。"`
	N              int    `json:"n,omitempty" description:"要生成的图像数。必须介于1和10之间。"`
	Size           string `json:"size,omitempty" description:"生成的图像的大小。默认为1024x1024"`
	ResponseFormat string `json:"response_format,omitempty" description:"返回生成的图像的格式。必须是url或b64_json之一。"`
	User           string `json:"user,omitempty" description:"代表最终用户的唯一标识符"`
}

func (*OpenAiImageEditReq) IsStream() bool {
	return false
}

type OpenAiImageEditResp ImageList

// OpenAiImageVariReq openai请求体、响应体--图像变体
type OpenAiImageVariReq struct {
	Image          string `json:"image" description:"用作变体基础的图像。必须是有效的PNG文件，小于4MB，并且正方形。"`
	Model          string `json:"Model,omitempty" description:"用于图像生成的模型。目前仅dall-e-2支持。"`
	N              int    `json:"n,omitempty" description:"要生成的图像数，必须介于1和10之间。"`
	ResponseFormat string `json:"response_format,omitempty" description:"返回生成的图像的格式。必须是url或b64_json之一。默认为url"`
	Size           string `json:"size,omitempty" description:"生成的图像的大小。默认为1024x1024"`
	User           string `json:"user,omitempty" description:"代表最终用户的唯一标识符"`
}

func (*OpenAiImageVariReq) IsStream() bool {
	return false
}

type OpenAiImageVariResp ImageList

// BaiduChatReq 百度千帆请求体、响应---对话模型请求体
type BaiduChatReq struct {
	Messages     []MessageItem `json:"messages"  description:"到目前为止，构成对话的消息列表"`
	Stream       bool          `json:"stream"  description:"响应方式，默认为false。如果设置为true，采用流式消息响应，流由data:[DONE]消息终止"`
	Temperature  float64       `json:"temperature" description:"采样温度，介于0和1之间。较高的值将使输出更加随机，而较低的值将使其更具集中性和确定性。"`
	TopP         float64       `json:"top_p" description:"温度采样的替代方法，称为核采样，其中模型考虑具有top_p概率质量的标记的结果。"`
	PenaltyScore float64       `json:"penalty_score" description:"惩罚因子"`
	UserId       string        `json:"user_id" description:"代表最终用户的唯一标识符"`
}

// BaiduChatResp 百度千帆对话模型，响应体
type BaiduChatResp struct {
	Id               string `json:"id" description:"聊天完成的唯一标识符。每个区块都具有相同的ID。"`
	Object           string `json:"object" description:"响应的对象类型"`
	Created          int64  `json:"created" description:"创建聊天完成时的时间戳"`
	SentenceId       int    `json:"sentence_id"  description:"文本片段的id,用于流式返回时进行区分"`
	IsEnd            bool   `json:"is_end"  description:"是否为结束消息，流式返回时进行区分"`
	IsTruncated      bool   `json:"is_truncated" description:"文本是否被截断"`
	Result           string `json:"result" description:"模型生成的文本"`
	NeedClearHistory bool   `json:"need_clear_history" description:"表示用户输入是否存在安全，是否关闭当前会话，清理历史会话信息。"`
	BanRound         int    `json:"ban_round" description:"当need_clear_history为true时，此字段会告知第几轮对话有敏感信息"`
	Usage            *Usage `json:"usage"  description:"完成请求的使用情况统计信息。"`
}

type RerankReq struct {
	Query string   `json:"query" description:"查询文本"`
	Texts []string `json:"texts" description:"待重排序的文本列表"`
}

// Inputs 返回将当前请求体转换为 DLIE Input 后的结构
func (r *RerankReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	bs, _ := json.Marshal(r)
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdRerankInputData,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents:   NewByteTensor(bs),
		},
	}, nil
}

// InputNames 按顺序返回各个输入参数的名称
func (r *RerankReq) InputNames() []string {
	return StdRerankModelIO.InputNames()
}

// Outputs 返回当前请求的输出格式
func (r *RerankReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdRerankOutputRes,
			Parameters: nil,
		},
	}
}

// OutputNames 按顺序返回各个输出参数的名称
func (r *RerankReq) OutputNames() []string {
	return StdRerankModelIO.OutputNames()
}

func (r *RerankReq) IsStream() bool {
	return false
}
func (r *RerankReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	inputs, err := r.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := r.Outputs()
	inferReq := &pb.ModelInferRequest{
		ModelName: innerModelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}
func (r *RerankReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	resp := new(RerankRes)
	if err := json.Unmarshal(info.ModelInferResponse.RawOutputContents[0][4:], resp); err != nil {
		return nil, err
	}
	return GetStdInferResp(resp, info.ModelInferResponse), nil
}

type RerankRes struct {
	Query  string    `json:"query"`
	Texts  []string  `json:"texts"`
	Scores []float32 `json:"scores"`
}

func (r *RerankRes) ParseResult(res map[string][]byte) error {
	if res == nil {
		return stderr.Internal.Error("parse rerank result from nil")
	}
	bs, ok := res[StdRerankOutputRes]
	if !ok {
		return stderr.Internal.Error("expected output %s not found", StdRerankOutputRes)
	}
	if err := json.Unmarshal(bs, r); err != nil {
		return err
	}
	return nil
}

// Any2VecReq  兼容Text2VecReqV2
type Any2VecReq struct {
	Texts any `json:"texts"  description:"需要进行向量化的输入"`
}
type Any2VecResp = TextVecResV2

func (r *Any2VecReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	bs, err := json.Marshal(r)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do marshal")
	}
	inputs := []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       StdTextVecV2InputData,
			Datatype:   "BYTES",
			Shape:      []int64{1},
			Parameters: nil,
			Contents:   NewByteTensor(bs),
		},
	}
	outputs := []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name:       StdTextVecV2OutputRes,
			Parameters: nil,
		},
	}
	inferReq := &pb.ModelInferRequest{
		ModelName: innerModelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}
func (r *Any2VecReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	resp := new(Any2VecResp)
	if err := json.Unmarshal(info.ModelInferResponse.RawOutputContents[0][4:], resp); err != nil {
		return nil, err
	}
	return resp, nil
}
func (r *Any2VecReq) IsStream() bool {
	return false
}
