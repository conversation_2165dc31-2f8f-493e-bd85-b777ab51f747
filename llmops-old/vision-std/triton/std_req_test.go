package triton

import (
	"encoding/json"
	"fmt"
	"testing"
)

func Test_jsonifyFunctionDefinition(t *testing.T) {
	jsonStr := "{\n	\"name\": \"get_current_weather\",\n	\"description\": \"Get the current weather in a given location\",\n	\"parameters\": {\n		\"type\": \"object\",\n		\"properties\": {\n			\"location\": {\n				\"type\": \"string\",\n				\"description\": \"The city and state, e.g. San Francisco, CA\"\n			},\n			\"unit\": {\"type\": \"string\"}\n		},\n		\"required\": [\"location\"]\n	}\n}"
	var def FunctionDefinition
	if err := json.Unmarshal([]byte(jsonStr), &def); err != nil {
		t.Fatal(err)
	}
	fmt.Printf("unserialized FunctionDefinition: %s\n", def)

	if str, err := json.<PERSON>(def); err != nil {
		t.<PERSON>al(err)
	} else {
		fmt.Printf("serialized FunctionDefinition: %s\n", str)
	}
}
func Test_StdChatReq2OpenaiChatReq(t *testing.T) {
	imageChat := &LLMChatReq{
		Query:  "图中是什么",
		Stream: false,
		Prompt: "你是一个图像理解大师",
		Stop:   false,
		File:   "sfs://tmp-path/tmp-file/tmp.jpg",
	}
	openaiImageChatReq, err := imageChat.Cvt2OpenaiChatReq()
	if err != nil {
		t.Fatal(err)
		return
	}
	r, _ := json.MarshalIndent(openaiImageChatReq, "", "  ")
	// expected result:
	// {
	// 	"messages": [
	// 	  {
	// 		"role": "system",
	// 		"content": "你是一个图像理解大师"
	// 	  },
	// 	  {
	// 		"role": "user",
	// 		"content": [
	// 		  {
	// 			"type": "text",
	// 			"text": "图中是什么"
	// 		  },
	// 		  {
	// 			"type": "image_url",
	// 			"image_url": {
	// 			  "url": "sfs://tmp-path/tmp-file/tmp.jpg"
	// 			}
	// 		  }
	// 		]
	// 	  }
	// 	],
	// 	"model": "atom",
	// 	"stream": false
	//   }
	t.Logf("%+v", string(r))
}
