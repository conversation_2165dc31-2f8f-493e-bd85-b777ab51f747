package triton

import (
	"bytes"
	"context"
	"encoding/binary"
	"github.com/pkg/errors"
	"io"
	"runtime/debug"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

// StdCommonHandler
// 统一推理请求处理,兼容流式与非流式,同时将结果及推理中发生的错误写到channel中
// isStream 该字段需要和inferReq中的stream字段保持一致
// inferReq 进行模型推理的标准请求张量
func StdCommonHandler(ctx context.Context, tritonClient Client, isStream bool,
	inferReq *pb.ModelInferRequest, rspChan chan *StreamInfo, errChan chan error) {
	var err error = nil
	defer func() {
		if r := recover(); r != nil {
			stdlog.Infof("Recovered from panic: %v\n", r)
			stdlog.Infof("Stack trace:\n%s", debug.Stack())
			err = stderr.Wrap(err, "Internal Server Error, Panic Occurred")
		}
		if err != nil && err != io.EOF {
			err = stderr.Wrap(err, "failed to call StdCommonHandler")
			stdlog.Error(err)
			errChan <- err

			stdlog.Info("error happend, send stopInferReq to triton server")
			SendStopStreamInfer(context.Background(), tritonClient, &LLMChatStopReq{RequestId: inferReq.Id}, inferReq.ModelName)
		}
		close(rspChan)
		close(errChan)
	}()

	for _, input := range inferReq.Inputs {
		contents := input.Contents
		if contents == nil {
			continue
		}
		for index, bytes := range contents.BytesContents {
			contentStr := string(bytes)
			if !isStream && len(contentStr) > 50 {
				contentStr = contentStr[:50] + "..."
			}
			stdlog.Infof("the content of inputs[%s][%d] is %s ", input.Name, index, contentStr)
		}
	}

	// 获取流式的triton客户端
	stdlog.Info("start initialize triton.streamCli")
	streamCli, err := tritonClient.GetStreamInferCli(ctx)
	if err != nil {
		err = stderr.Wrap(err, "failed to get triton.streamCli")
		return
	}

	stdlog.Info("start send pb.ModelInferRequest to triton server")
	err = SendInferRequest(streamCli, inferReq)
	if err != nil {
		return
	}

	// 准备基本的流式信息,兼容非流式
	streamInfo := StreamInfo{
		RequestId: inferReq.Id,
		Created:   time.Now().Unix(),
		Model:     inferReq.ModelName,
		IsStream:  isStream,
		// 后面字段每次请求可能不同
		Index:              -1,
		FinishReason:       "stop",
		ModelInferResponse: nil,
	}
	ticker := time.NewTicker(100 * time.Millisecond) // 每隔100毫秒检查一次
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			// 请求取消,发送stop消息。主要是文本生成类型模型
			stdlog.Info("the request is cancelled, send stopInferReq to triton server")
			SendStopStreamInfer(context.Background(), tritonClient, &LLMChatStopReq{RequestId: inferReq.Id}, inferReq.ModelName)
			return
		case <-ticker.C:
			streamInfo.Index++
			var msg *pb.ModelStreamInferResponse
			// 模型如果是流式返回,则需要多次调用Recv方法获取结果
			msg, err = streamCli.Recv()
			if err != nil {
				if err == io.EOF {
					stdlog.Info("receive end message,handle stream inferResponse successfully")
					return
				} else {
					err = stderr.Wrap(err, "failed to receive stream infer response,"+
						" please check the inferReq is correct")
					return
				}
			}
			if msg == nil || msg.InferResponse == nil {
				err = errors.New("the stream inferResponse received is nil")
				return
			}
			if msg.ErrorMessage != "" {
				err = errors.New(msg.ErrorMessage)
				return
			}

			stdlog.Info("construct StreamInfo for current inferResponse")
			// 拷贝流式基本信息并设置相关数据
			cpStreamInfo := streamInfo
			if isStream {
				// 流式返回时,中间消息finish字段为空
				cpStreamInfo.FinishReason = ""
			}

			cpStreamInfo.ModelInferResponse = msg.InferResponse
			rspChan <- &cpStreamInfo
		}
	}
}
func SendStopStreamInfer(ctx context.Context, tritonClient Client, LLMChatStopRep *LLMChatStopReq, modelName string) error {
	stopInferReq, err := LLMChatStopRep.GetModelInferRequest(modelName)
	if err != nil {
		return err
	}
	streamClient, err := tritonClient.GetStreamInferCli(ctx)
	if err != nil {
		return err
	}
	err = SendInferRequest(streamClient, stopInferReq)
	if err != nil {
		return err
	}
	return nil
}

type StreamInfo struct {
	IsStream           bool                   `json:"is_stream" yaml:"is_stream" description:"是否为流式"`
	Model              string                 `json:"model" yaml:"model" description:"模型名称"`
	RequestId          string                 `json:"request_id" yaml:"request_id" description:"每一次流式请求的唯一id。"`
	Created            int64                  `json:"created" yaml:"created" description:"文本生成的时间"`
	Index              int                    `json:"index" yaml:"index" description:"返回消息的序列号。"`
	FinishReason       string                 `json:"finish_reason" yaml:"finish_reason" description:"停止原因"`
	ModelInferResponse *pb.ModelInferResponse `json:"model_infer_response" yaml:"model_infer_response" description:"triton原生响应结构"`
}

func SendInferRequest(streamCli pb.GRPCInferenceService_ModelStreamInferClient, inferReq *pb.ModelInferRequest) error {
	err := streamCli.Send(inferReq)
	if err != nil {
		stdlog.Info(err, "failed to send model request")
		return err
	}
	err = streamCli.CloseSend()
	if err != nil {
		stdlog.Info(err, "failed to close model stream")
		return err
	}
	return nil
}

// StdMLReq 机器学习-二维表,请求体结构。 兼容机器学习下的二分类、多分类
type StdMLReq struct {
	Data [][]float32 `json:"data"`
}

// StdMLResp 机器学习-二维表,响应体结构。兼容机器学习下的二分类、多分类
type StdMLResp [][]float32

func (r *StdMLReq) Inputs() ([]*pb.ModelInferRequest_InferInputTensor, error) {
	flatten := make([]float32, 0)
	for _, dim := range r.Data {
		for _, e := range dim {
			flatten = append(flatten, e)
		}
	}
	shape := []int32{int32(len(r.Data)), int32(len(r.Data[0]))}
	return []*pb.ModelInferRequest_InferInputTensor{
		{
			Name:       "input__0",
			Datatype:   "FP32",
			Shape:      int32Array2Int64Array(shape),
			Parameters: nil,
			Contents: &pb.InferTensorContents{
				Fp32Contents: flatten,
			},
		},
	}, nil
}
func int32Array2Int64Array(int32Array []int32) []int64 {
	int64Array := make([]int64, 0)
	for _, elem := range int32Array {
		int64Array = append(int64Array, int64(elem))
	}
	return int64Array
}
func (r *StdMLReq) Outputs() []*pb.ModelInferRequest_InferRequestedOutputTensor {
	return []*pb.ModelInferRequest_InferRequestedOutputTensor{
		{
			Name: "output__0",
		},
	}
}
func (r *StdMLReq) GetModelInferRequest(modelName string) (*pb.ModelInferRequest, error) {
	inputs, err := r.Inputs()
	if err != nil {
		return nil, err
	}
	outputs := r.Outputs()
	inferReq := &pb.ModelInferRequest{
		ModelName: modelName,
		Inputs:    inputs,
		Outputs:   outputs,
	}
	return inferReq, nil
}
func (r *StdMLReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo == nil || streamInfo.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	inferResp := streamInfo.ModelInferResponse
	stdMLResp := StdMLResp{}
	bytes := inferResp.RawOutputContents[0]
	output := inferResp.Outputs[0]
	cols := int64(0)
	rows := int64(0)
	// FIXME 现在只能处理两维
	if len(output.Shape) == 1 {
		rows = 1
		cols = output.Shape[0]
	} else {
		rows = output.Shape[0]
		cols = output.Shape[1]
	}
	// triton返回为bytes 排成了一行，这里还原成数组
	elemNum := 0
	for i := int64(0); i < rows; i++ {
		fp32Row := make([]float32, 0)
		for j := int64(0); j < cols; j++ {
			fp32Row = append(fp32Row, readFloat32(bytes[elemNum*4:elemNum*4+4]))
			elemNum++
		}
		stdMLResp = append(stdMLResp, fp32Row)
	}
	return GetStdInferResp(&stdMLResp, inferResp), nil
}
func (r *StdMLReq) IsStream() bool {
	return false
}

func readFloat32(fourBytes []byte) float32 {
	buf := bytes.NewBuffer(fourBytes)
	var retval float32
	_ = binary.Read(buf, binary.LittleEndian, &retval)
	return retval
}

// LLMChatStopReq 停止流式推理
type LLMChatStopReq struct {
	RequestId string `json:"request_id"  description:"需要停止的流式推理的请求id"`
}

func (l *LLMChatStopReq) IsStream() bool {
	return false
}
func (l *LLMChatStopReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	stopRep := &LLMChatReq{Stop: true, Stream: false}
	pbInferReq, err := stopRep.GetModelInferRequest(innerModelName)
	if err != nil {
		return nil, err
	}
	// stop请求，必须指定具体的request_id
	if l.RequestId == "" {
		return nil, errors.New("must special request_id to stop stream infer")
	}
	pbInferReq.Id = l.RequestId
	return pbInferReq, nil
}

func (l *LLMChatStopReq) Parse2InferResp(streamInfo *StreamInfo) (interface{}, error) {
	if streamInfo != nil {
		return nil, errors.New("the streamInfo should be null for LLMChatStopReq")
	}
	stopResp := &LLMChatStopResp{RequestId: l.RequestId, Stopped: true}
	return GetStdInferResp(stopResp, streamInfo.ModelInferResponse), nil
}

func (l *LLMChatStopReq) Construct2InferResp(result string) (interface{}, error) {
	return Construct2StdInferResp[LLMChatStopResp](result)
}

type LLMChatStopResp struct {
	RequestId string `json:"request_id"  description:"需要停止的流式推理的请求id"`
	Stopped   bool   `json:"stopped"  description:"是否成功停止该请求"`
}
