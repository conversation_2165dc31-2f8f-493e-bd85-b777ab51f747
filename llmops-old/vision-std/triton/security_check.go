package triton

import (
	"bufio"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type GuardType = string

const (
	CheckInputRiskAPI                      = "/detect_input_risk"
	CheckOutputRiskAPI                     = "/detect_output_risk"
	CheckInputSensitiveWordsAPI            = "/detect_sen_word_input"
	CheckOutputSensitiveWordsAPI           = "/detect_sen_word_output"
	SentenceQPKey                          = "sentence"
	ConfigIdQPKey                          = "config_id"
	PiLev                        GuardType = "PI_LEV"
	PiSmart                      GuardType = "PI_SMART"
	SenWord                      GuardType = "SEN_WORD"
	UnSpecific                   GuardType = "UNSPECIFIC"
)

type SecurityCensor struct {
	Address string
	Timeout time.Duration
}

type CheckSecurityRequest struct {
	Sentence string `json:"sentence"`
	ConfigId string `json:"config_id"`
}
type CheckSecurityResponse struct {
	Risk      bool      `json:"risk"`
	Response  string    `json:"response"`
	GuardType GuardType `json:"guard_type"`
}

func NewSecurityCensor(address string, timeout time.Duration) *SecurityCensor {
	return &SecurityCensor{
		Address: address,
		Timeout: timeout,
	}
}

func (s *SecurityCensor) CheckInputRisk(ctx context.Context, req *CheckSecurityRequest) (*CheckSecurityResponse, error) {
	return s.checkSecurity(ctx, CheckInputRiskAPI, req)
}

func (s *SecurityCensor) CheckOutputRisk(ctx context.Context, req *CheckSecurityRequest) (*CheckSecurityResponse, error) {
	return s.checkSecurity(ctx, CheckOutputRiskAPI, req)
}

func (s *SecurityCensor) CheckOutputSensitiveWords(ctx context.Context, req *CheckSecurityRequest) (*CheckSecurityResponse, error) {
	return s.checkSecurity(ctx, CheckOutputSensitiveWordsAPI, req)
}

func (s *SecurityCensor) CheckInputSensitiveWords(ctx context.Context, req *CheckSecurityRequest) (*CheckSecurityResponse, error) {
	return s.checkSecurity(ctx, CheckInputSensitiveWordsAPI, req)
}

func (s *SecurityCensor) checkSecurity(ctx context.Context, api string, req *CheckSecurityRequest) (*CheckSecurityResponse, error) {
	if req == nil || req.Sentence == "" {
		return &CheckSecurityResponse{Risk: false, Response: "request is empty", GuardType: UnSpecific}, nil
	}
	nctx, cancel := context.WithTimeout(ctx, s.Timeout)
	defer cancel()
	fullUrl := s.Address + api + appendQueryParams(map[string]string{
		SentenceQPKey: req.Sentence,
		ConfigIdQPKey: req.ConfigId,
	})
	c := http.Client{}
	hr, err := http.NewRequestWithContext(nctx, "GET", fullUrl, nil)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to new request")
	}
	rsp, err := c.Do(hr)
	if err != nil {
		return nil, stderr.Wrap(err, "failed get response from security censor")
	}
	rspReader := bufio.NewReader(rsp.Body)
	rspBytes, err := io.ReadAll(rspReader)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to read response")
	}
	if rsp.StatusCode != 200 {
		return nil, stderr.Internal.Error("failed to get response form security censor %s", string(rspBytes))
	}
	checkRsp := new(CheckSecurityResponse)
	err = json.Unmarshal(rspBytes, checkRsp)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal check security response")
	}
	return checkRsp, nil
}

func appendQueryParams(kVPairs map[string]string) string {
	queryParams := url.Values{}
	for k, v := range kVPairs {
		queryParams[k] = []string{v}
	}
	return "?" + queryParams.Encode()
}
