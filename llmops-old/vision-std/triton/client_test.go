package triton

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"google.golang.org/grpc"
	"k8s.io/apimachinery/pkg/util/rand"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

var c Client

func init() {
	// you can use cmd like "kubectl port-forward --address 0.0.0.0 pod/autocv-dlie-b69r4 18001:8001" to expose dlie server port
	cli, err := NewTritonGrpcClient(GrpcConfig{
		Host: "*************",
		Port: 32141,
		// Host:         "**************",
		// Port:         8001,
		BaseTimeout:  time.Second * 10,
		LoadTimeout:  0,
		InferTimeout: time.Second * 30,
	})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect to trition grpc server, tests will be skipped")
		return
	}
	c = cli
}

func Test_grpcCli_LoadModel(t *testing.T) {
	if c == nil {
		return
	}

	type args struct {
		modelName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "TestModelService load ensemble model",
			args:    args{modelName: "classifier:"},
			wantErr: false,
		},
		{
			name:    "TestModelService load ensemble model",
			args:    args{modelName: "demo.simple_int8"},
			wantErr: false,
		},
		{
			name:    "TestModelService load common model",
			args:    args{modelName: "densenet_onnx"},
			wantErr: false,
		},
		{
			name:    "TestModelService load invalid model",
			args:    args{modelName: "simple.int8"},
			wantErr: false,
		},
		{
			name:    "TestModelService load python backend model",
			args:    args{modelName: "test_py_model"},
			wantErr: false,
		},
		{
			name:    "TestModelService load s3 model:yolo",
			args:    args{modelName: "model-c812hpb11801pnp7l2a0.release-c812hrb11801pnp7l2ag.yolo:"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := c.LoadModel(tt.args.modelName); (err != nil) != tt.wantErr {
				t.Errorf("LoadModel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_grpcCli_ModelMeta(t *testing.T) {
	if c == nil {
		return
	}

	type args struct {
		modelName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "TestModelService s3 model meta",
			args:    args{modelName: "model-c9tq3n7cqfkj63sis67g.release-c9tq3nncqfkj63sis680.predict"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if rsp, err := c.ModelMeta(tt.args.modelName); (err != nil) != tt.wantErr {
				t.Errorf("ModelMeta() error = %v, wantErr %v", err, tt.wantErr)
				return
			} else {
				t.Logf("got model meta = %v", rsp)
			}
		})
	}
}
func Test_grpcCli_ListModels(t *testing.T) {
	if c == nil {
		return
	}

	t.Run("test list models", func(t *testing.T) {
		models, err := c.ListModels(false)
		if err != nil {
			t.Errorf("failed to ListModels() error = %v", err)
		} else {
			t.Logf("got %d models", len(models))
			for _, model := range models {
				t.Logf("%+v", model)
			}
		}
	})
}
func Test_grpcCli_ModelInfer(t *testing.T) {
	if c == nil {
		return
	}

	type args struct {
		modelName string
		req       *pb.InferReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "TestModelService Infer Jpeg",
			args: args{
				modelName: "model-cbm92acl9optoi63jivg.release-cbm92acl9optoi63jj00.dev_ensemble",
				req: &pb.InferReq{
					ClientId: "1",
					Params: []*pb.InferReq_Param{
						{
							Id:   "1",
							Type: "jpg",
							// base64 string of a jpeg image witch contains a cat
							Data: "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",
							Size: 2788,
							ExternalConfig: &pb.ExternalConfig{
								InferParams:  map[string]string{"param1": "value1"},
								DeviceLabels: map[string]string{"deviceLabel1": "value1"},
							},
						}, {
							Id:   "1",
							Type: "jpg",
							// base64 string of a jpeg image witch contains a cat
							Data: "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",
							Size: 2788,
							ExternalConfig: &pb.ExternalConfig{
								InferParams:  map[string]string{"param1": "value1"},
								DeviceLabels: map[string]string{"deviceLabel1": "value1"},
							},
						},
					},
					Context: &pb.InferReq_Context{
						InstanceLabels: map[string]string{"testK": "testV"},
						PipelineLabels: map[string]string{"testK": "testV"},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.ModelInfer(tt.args.modelName, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ModelInfer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("ModelInfer() got = %v", got)
		})
	}
}

func Test_grpcCli_ModelStats(t *testing.T) {
	if c == nil {
		return
	}
	type args struct {
		modelName string
	}
	testCase := struct {
		name    string
		args    args
		wantErr bool
	}{
		name:    "TestModelService get model current status",
		args:    args{modelName: "simple_int8"},
		wantErr: false,
	}

	t.Run("test model current status", func(t *testing.T) {
		rsp, err := c.ModelStats(testCase.args.modelName)
		if err != nil {
			t.Errorf("failed to get model current status, %v", err)
		} else {
			t.Logf("model current status %v", rsp)
		}

	})
}

func Test_grpcCli_WatchModelStats(t *testing.T) {
	testCase := struct {
		modelName string
		version   string
		interval  time.Duration
	}{
		modelName: "model-c9tq3n7cqfkj63sis67g.release-c9tq3nncqfkj63sis680.predict",
		version:   "1",
		interval:  time.Second * 3,
	}
	t.Run("test watch model status", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		go func() {
			ch := c.WatchModelStats(ctx, testCase.modelName, testCase.interval)
			for status := range ch {
				t.Log(status)
				t.Logf("cumulative duration %s", time.Duration(status.InferenceStats.Success.Ns).String())
			}
		}()
		time.Sleep(time.Minute)
		cancel()
	})
}

func TestGrpcCli_WatchMultiModelStats(t *testing.T) {
	modelNames := []string{
		"inception_graphdef",
		"simple",
		"simple_dyna_sequence",
	}
	ctx, cancel := context.WithCancel(context.Background())
	ch := c.WatchMultiModelStats(ctx, modelNames, time.Second*3)
	for {
		select {
		case status := <-ch:
			if status == nil {
				continue
			} else {
				t.Logf("success watch multi model stats %v", status)
				cancel()
				return
			}
		}
	}
}

func Test_grpcCli_HandleMultiModelStats(t *testing.T) {
	type fields struct {
		GrpcConfig  GrpcConfig
		baseCtx     context.Context
		baseCancel  context.CancelFunc
		cli         pb.GRPCInferenceServiceClient
		conn        *grpc.ClientConn
		lastStats   *pb.ModelStatisticsResponse
		lastStatsTs time.Time
	}
	type args struct {
		ctx        context.Context
		modelNames []string
		interval   time.Duration
		handler    func(stats map[string]*pb.ModelStatistics)
	}
	ctx, _ := context.WithTimeout(context.Background(), time.Minute)
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx:        ctx,
				modelNames: []string{"model-ca843isl9opkp0gaqre0.release-ca843jcl9opkp0gaqreg.predict"},
				interval:   time.Second * 3,
				handler: func(stats map[string]*pb.ModelStatistics) {
					for name, s := range stats {
						fmt.Printf("\nname=%s, ststas=%+v", name, s)
					}
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c.HandleMultiModelStats(tt.args.ctx, tt.args.modelNames, tt.args.interval, tt.args.handler)
		})
	}

}

func TestGrpcConfig_KeyString(t *testing.T) {
	type fields struct {
		Node        string
		Host        string
		Port        int
		BaseTimeout time.Duration
		LoadTimeout time.Duration
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "",
			fields: fields{
				Node:        "node01",
				Host:        "localhost",
				Port:        1010,
				BaseTimeout: 0,
				LoadTimeout: time.Second,
			},
			want: "_dlie_node01_localhost_1010_0s_1s",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &GrpcConfig{
				Node:        tt.fields.Node,
				Host:        tt.fields.Host,
				Port:        tt.fields.Port,
				BaseTimeout: tt.fields.BaseTimeout,
				LoadTimeout: tt.fields.LoadTimeout,
			}
			if got := c.KeyString(); got != tt.want {
				t.Errorf("KeyString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_grpcCli_LoadModelOnSpecificAccelerator(t *testing.T) {
	type args struct {
		modelName   string
		rawConfig   string
		accType     conf.AcceleratorType
		accIndexs   []int
		instanceNum int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test load gpu 0 on hp3",
			args: args{
				modelName:   "model-cehs6v20sqhd155l77vg.release-cehs6v20sqhd155l7800.vehicle_detection_model",
				rawConfig:   "name: \"vehicle_detection_model\"\nplatform: \"onnxruntime_onnx\"\nmax_batch_size:0\n\ninput [\n  {\n    name: \"image\"\n    data_type: TYPE_FP32\n    dims: [ -1, 3, 640, 640]\n  },\n  {\n    name: \"scale_factor\"\n    data_type: TYPE_FP32\n    dims: [ -1, 2]\n  }\n]\noutput [\n  {\n    name: \"multiclass_nms3_0.tmp_0\"\n    data_type: TYPE_FP32\n    dims: [-1, 6]\n  },\n  {\n    name: \"multiclass_nms3_0.tmp_2\"\n    data_type: TYPE_INT32\n    dims: [1]\n  }\n]\n\ninstance_group [\n  {\n    count: 1\n    kind: KIND_GPU\n  }\n]\n\n#model_warmup [\n#  {\n#    name: \"warmup\"\n#    batch_size: 1\n#    inputs {\n#      key: \"images\"\n#      value: {                                                                      \n#            data_type: TYPE_FP32                                                      \n#            dims: [3, 640, 640]                                                  \n#            zero_data: true                                                           \n#      }\n#    }\n#  }\n#]\n\n\n#dynamic_batching {\n#    preferred_batch_size: [ 2,4, 6,8 ]\n#  }\n",
				accType:     conf.AcceleratorGPU,
				accIndexs:   []int{0},
				instanceNum: 3,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := c.LoadModelOnSpecificAccelerator(tt.args.modelName, tt.args.rawConfig, tt.args.accType, tt.args.accIndexs, tt.args.instanceNum); (err != nil) != tt.wantErr {
				t.Errorf("LoadModelOnSpecificAccelerator() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_grpcCli_StreamChat(t *testing.T) {
	req := &LLMChatReq{
		Query:   "你是一个做题家，请根据我给你的题目：小明每天早上花费10分钟时间走到学校，如果小明家距离学校2公里，那么他每分钟走多少米？、，给出它的答案：这是一个关于速度、路程、时间的数学问题。我们可以通过公式：速度＝路程÷时间 来解决。\\n因为小明每天早上走2公里，所以他的路程为2千米。而他每天早上要花费10分钟时间走到学校，因此他的时间为10分钟，即600秒。\\n所以小明每分钟走的距离为 2公里 / 600秒 = 0.0033公里/秒 或 3.3米/秒。\\n答案：小明每分钟走3.3米。你是一个做题家，请根据我给你的题目：小明每天早上花费10分钟时间走到学校，如果小明家距离学校2公里，那么他每分钟走多少米？、，给出它的答案：这是一个关于速度、路程、时间的数学问题。我们可以通过公式：速度＝路程÷时间 来解决。\\n因为小明每天早上走2公里，所以他的路程为2千米。而他每天早上要花费10分钟时间走到学校，因此他的时间为10分钟，即600秒。\\n所以小明每分钟走的距离为 2公里 / 600秒 = 0.0033公里/秒 或 3.3米/秒。\\n答案：小明每分钟走3.3米。你是一个做题家，请根据我给你的题目：小明每天早上花费10分钟时间走到学校，如果小明家距离学校2公里，那么他每分钟走多少米？、，给出它的答案：这是一个关于速度、路程、时间的数学问题。我们可以通过公式：速度＝路程÷时间 来解决。\\n因为小明每天早上走2公里，所以他的路程为2千米。而他每天早上要花费10分钟时间走到学校，因此他的时间为10分钟，即600秒。\\n所以小明每分钟走的距离为 2公里 / 600秒 = 0.0033公里/秒 或 3.3米/秒。\\n答案：小明每分钟走3.3米。",
		Stream:  false,
		History: make([]QAItem, 0),
		Handler: func(raw []byte) error {
			t.Logf("%s", raw)
			return nil
		},
	}
	err := c.StreamModelInfer(context.Background(), "atom", req)
	if err != nil {
		t.Fatal(err)
	}

}

func Test_grpcCli_Text2Vec(t *testing.T) {
	req := &Text2VecReqV1{
		Text: "星环信息科技（上海）股份有限公司（简称：星环科技，股票代码：688031.SH） [48] ，成立于2013年，是一家企业级大数据基础软件开发商，围绕数据的集成、存储、治理、建模、分析、挖掘和流通等数据全生命周期提供基础软件及服务。 [45]",
	}
	// if err := c.UnloadModel("TranswarpVectorPulse"); err != nil {
	// 	t.Fatal(err)
	// }
	// if err := c.LoadModel("TranswarpVectorPulse"); err != nil {
	// 	t.Fatal(err)
	// }
	res, err := c.SyncModelInfer("TranswarpVectorPulse", req)
	if err != nil {
		t.Fatal(err)
	}
	// t.Logf("%+v", res)
	for out, bs := range res {
		t.Logf("%s: %s", out, bs)
	}

	ret, err := req.ParseResult(res)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%+v", ret)

	emb, ok := ret.Embedding()
	if !ok {
		t.Fatal("no embedding info found in result")
	}
	t.Logf("%+v", emb)
	t.Logf("%d", len(emb))
	t.Logf("%d", len(emb[0]))

	return
}

func Test_grpcCli_Text2VecV2(t *testing.T) {
	ctx := &Text2VecReqV2{
		Texts: []string{
			"2022年10月18日，“国产大数据基础软件第一股”星环信息科技（上海）股份有限公司在科创板上市",
			"星环信息科技（上海）股份有限公司（简称：星环科技，股票代码：688031.SH） [48] ，成立于2013年，是一家企业级大数据基础软件开发商，围绕数据的集成、存储、治理、建模、分析、挖掘和流通等数据全生命周期提供基础软件及服务。 [45]",
			"2022年9月30日，星环信息科技（上海）股份有限公司在科创板开启申购。",
			"星环科技致力于打造企业级大数据基础软件，围绕数据的集成、存储、治理、建模、分析、挖掘和流通等数据全生命周期提供基础软件与服务，构建明日数据世界。公司以上海为总部，以北京、南京、广州、新加坡为区域总部，在郑州、成都、重庆、济南设有支持中心，同时在深圳、西安等地设有办事机构，并在加拿大设有海外分支机构。 [11]",
			"经过多年自主研发，星环科技建立了多个产品系列：一站式大数据基础平台TDH、分布式分析型数据库ArgoDB及交易型数据库KunDB、基于容器的智能数据云平台TDC、大数据开发工具TDS、智能分析工具Sophon和超融合大数据一体机TxData Appliance 等，并拥有多项专利技术。公司产品已经在二十多个行业应用落地，星环科技已完成E轮融资",
			"经营范围编辑:围绕数据集成、存储、治理、建模、分析、挖掘和服务等数据处理全生命周期，星环科技研发了一系列软件产品，包括大数据基础平台TDH、分布式关系型数据库ArgoDB和KunDB、数据开发与智能分析工具TDS和Sophon、大数据云平台TDC及配套的软硬一体机及相关技术服务，并拥有近百件海内外专利授权、近300件软著。 [9]",
		},
	}
	req := &Text2VecReqV2{
		Texts: []string{
			"星环什么时候上市？",
			"星环的主要经营范围有哪些？",
		},
	}
	ctxRet, err := c.SyncModelInfer("TranswarpVectorPulse", ctx)
	if err != nil {
		t.Fatal(err)
	}
	ctxRes, err := ctx.ParseResult(ctxRet)
	if err != nil {
		t.Fatal(err)
	}

	reqRet, err := c.SyncModelInfer("TranswarpVectorPulse", req)
	if err != nil {
		t.Fatal(err)
	}
	reqRes, err := ctx.ParseResult(reqRet)
	if err != nil {
		t.Fatal(err)
	}

	for i, q := range req.Texts {
		ans := ""
		maxDis := 0.0
		for j, a := range ctx.Texts {
			dis := CosineSimilarity(reqRes.Results[i], ctxRes.Results[j])
			t.Logf("similarity between req[%d],ctx[%d] is %.4f", i, j, dis)
			if dis > maxDis {
				maxDis = dis
				ans = a
			}
		}
		t.Logf("best match: [%d]\nQ:%s, \nA:%s", i, q, ans)
	}

	return
}

func Test_grpcCli_Text2VecV2_FromFile(t *testing.T) {
	bs, err := os.ReadFile("./testdata/test.txt")
	if err != nil {
		t.Fatal(err)
	}
	ctx := &Text2VecReqV2{
		Texts: strings.Split(string(bs), "\n\n\n"),
	}
	req := &Text2VecReqV2{
		Texts: []string{
			"介绍下Sophon Edge的打包流程",
			"Edge服务间的网络可能出现异常，应该如何进行排查",
			"前端 SeiData 的数据结构格式是怎样的？",
		},
	}
	ctxRet, err := c.SyncModelInfer("atom", ctx)
	if err != nil {
		t.Fatal(err)
	}
	ctxRes, err := ctx.ParseResult(ctxRet)
	if err != nil {
		t.Fatal(err)
	}

	reqRet, err := c.SyncModelInfer("atom", req)
	if err != nil {
		t.Fatal(err)
	}
	reqRes, err := ctx.ParseResult(reqRet)
	if err != nil {
		t.Fatal(err)
	}

	a := struct {
		CtxTexts         []string
		CtxFeatures      [][]float64
		QuestionTexts    []string
		QuestionFeatures [][]float64
	}{
		CtxTexts:         ctx.Texts,
		CtxFeatures:      ctxRes.Results,
		QuestionTexts:    req.Texts,
		QuestionFeatures: reqRes.Results,
	}
	bs, err = json.Marshal(a)
	if err != nil {
		t.Fatal(err)
	}
	if err = os.WriteFile("./test-res.json", bs, os.ModePerm); err != nil {
		t.Fatal(err)
	}

	for i, q := range req.Texts {
		ans := ""
		maxDis := 0.0
		for j, a := range ctx.Texts {
			dis := CosineSimilarity(reqRes.Results[i], ctxRes.Results[j])
			t.Logf("similarity between req[%d],ctx[%d] is %.4f", i, j, dis)
			if dis > maxDis {
				maxDis = dis
				ans = a
			}
		}
		t.Logf("best match: [%d]\nQ:%s, \nA:%s", i, q, ans)
	}

	return
}

// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:1-8         	       2	1158472136 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:2-8         	       3	 615729499 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:5-8         	       1	1053206417 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:10-8        	       1	1210944070 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:20-8        	       1	3134888337 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:50-8        	       1	5222439056 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:50,batchszie:100-8       	       1	4742405000 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:1-8        	       1	1066433661 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:2-8        	       2	 560765548 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:5-8        	       2	2185193514 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:10-8       	       1	2778148173 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:20-8       	       1	2824040963 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:50-8       	       1	5456690539 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:100,batchszie:100-8      	       1	10014023350 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:1-8        	       1	2799769078 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:2-8        	       2	 777310208 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:5-8        	       3	 592682055 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:10-8       	       3	 768690911 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:20-8       	       1	3539667224 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:50-8       	       1	3618406729 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:200,batchszie:100-8      	       1	2729333839 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:1-8        	       2	 948386515 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:2-8        	       1	1451700394 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:5-8        	       1	3537069379 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:10-8       	       1	2402243303 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:20-8       	       1	1989701572 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:50-8       	       1	1626660203 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:500,batchszie:100-8      	       1	7198596916 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:1-8       	       2	 746464102 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:2-8       	       3	 417763045 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:5-8       	       1	2443454017 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:10-8      	       1	2334826246 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:20-8      	       1	1327223036 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:50-8      	       1	1777541139 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:1000,batchszie:100-8     	       1	4410101148 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:1
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:1-8       	       3	1589741246 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:2
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:2-8       	       2	 606087578 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:5
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:5-8       	       4	 664230320 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:10
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:10-8      	       4	 451042818 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:20
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:20-8      	       1	1667243284 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:50
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:50-8      	       1	4596701082 ns/op
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:100
// BenchmarkGrpcCli_Text2VecV2/contentlen:2000,batchszie:100-8     	       1	5845699497 ns/op
func BenchmarkGrpcCli_Text2VecV2(b *testing.B) {
	contentLens := []int{50, 100, 200, 500, 1000, 2000}
	batchSize := []int{1, 2, 5, 10, 20, 50, 100}
	for _, cl := range contentLens {
		for _, bs := range batchSize {
			req := &Text2VecReqV2{Texts: make([]string, bs)}
			for i := range req.Texts {
				req.Texts[i] = rand.String(cl)
			}
			b.Run(fmt.Sprintf("contentlen:%d,batchszie:%d", cl, bs), func(b *testing.B) {
				for j := 0; j < b.N; j++ {
					rsp, err := c.SyncModelInfer("TranswarpVectorPulse", req)
					if err != nil {
						b.Fatal(err)
					}
					res, err := req.ParseResult(rsp)
					if len(res.Results) != len(req.Texts) {
						b.Fatal(res, req)
					}
				}
			})
		}
	}
}
