package triton

import (
	"context"
	"errors"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DefaultWindowSize            = 10
	DefaultStepSize              = 5
	DefaultSecurityCensorTimeout = time.Second * 10
)

var (
	ErrFilterTextFailed = errors.New("filter text failed")
	ErrPassTextFailed   = errors.New("pass text failed")
	ErrStopTextFailed   = errors.New("stop text failed")
	ErrFoundRiskWord    = errors.New("found risk word")
	ErrAlreadyDone      = errors.New("filter already done")
)

// FilterBasicConfig 定义了stream 文本过滤器的基本配置，包括滑动窗口长度和滑动步长。
type FilterBasicConfig struct {
	WindowSize int // 待过滤文本滑动窗口的长度
	StepSize   int // 滑动步长
}

// FilterSecurityServiceConfig 安全过滤服务配置，包括配置ID、服务地址、超时时间和Context。
type FilterSecurityServiceConfig struct {
	// ConfigId 配置ID
	// 用于标识特定的配置，以便在多个配置中区分。
	ConfigId string
	// Address 服务地址
	// 指定安全过滤服务的访问地址，用于建立与服务的连接。
	Address string
	// Timeout 超时设置
	// 定义了与安全过滤服务通信的超时时间，以避免长时间等待。
	Timeout time.Duration
	// Ctx 上下文信息
	// 提供了执行操作所需的上下文信息，如取消信号、超时等。
	Ctx context.Context
}

type passTextFunc func(text string) error

type stopTextFunc func(response *CheckSecurityResponse) error

type filterTextFunc func(text string) (*CheckSecurityResponse, error)

// StreamTextFilter 用于流式文本流过滤的结构，创建的StreamTextFilter结构体实例只能处理一次流式文本，当需要处理新的流式文本时需要创建新的实例
type StreamTextFilter struct {
	// filterTextHandler 文本过滤函数，对文本进行具体的过滤操作
	filterTextHandler filterTextFunc
	// passTextHandler 文本通过后的回调函数
	passTextHandler passTextFunc
	// stopTextHandler 文本被拦截后的回调函数
	stopTextHandler stopTextFunc
	// textBuffer 用于缓存需要过滤的流式文本
	textBuffer []rune
	// leftWindowIndex 表示滑动窗口的左边界索引。
	leftWindowIndex int
	// basicConfig 文本过滤器的基本配置，包括滑动窗口长度和滑动步长
	basicConfig FilterBasicConfig
	// securityServiceConfig 安全过滤服务配置，包括配置ID、服务地址、超时时间和Context
	securityServiceConfig FilterSecurityServiceConfig
	// done 表示过滤过程是否已完成。
	done bool
	// filterResult 用于存储过滤的结果。
	filterResult *CheckSecurityResponse
	// err 用于记录过滤过程中可能出现的错误。
	err error
	// wg 用于等待过滤相关的goroutine完成。
	wg sync.WaitGroup
}

// NewStreamTextFilter 创建一个流式文本过滤器。
// 通过基本配置和安全服务配置来初始化过滤函数
//
// basicConfig: 提供窗口大小和步长大小等基本配置，用于控制过滤器的操作策略。
// securityServiceConfig: 提供安全服务的配置，包括配置ID、地址、超时时间和上下文等，用于建立安全审查的连接和参数。
func NewStreamTextFilter(basicConfig FilterBasicConfig, securityServiceConfig FilterSecurityServiceConfig) (*StreamTextFilter, error) {
	if basicConfig.WindowSize <= 0 {
		return nil, stderr.Error("window size must be greater than 0")
	}
	if basicConfig.StepSize <= 0 {
		return nil, stderr.Error("step size must be greater than 0")
	}
	if securityServiceConfig.ConfigId == "" {
		return nil, stderr.Error("security config id cannot be empty")
	}
	if securityServiceConfig.Address == "" {
		return nil, stderr.Error("security address cannot be empty")
	}
	if securityServiceConfig.Timeout <= 0 {
		return nil, stderr.Error("security timeout must be greater than 0")
	}
	if securityServiceConfig.Ctx == nil {
		return nil, stderr.Error("security context cannot be nil")
	}

	securityCensor := NewSecurityCensor(securityServiceConfig.Address, securityServiceConfig.Timeout)
	filterHandler := func(text string) (*CheckSecurityResponse, error) {
		return securityCensor.CheckOutputSensitiveWords(securityServiceConfig.Ctx, &CheckSecurityRequest{
			ConfigId: securityServiceConfig.ConfigId,
			Sentence: text,
		})
	}
	return &StreamTextFilter{
		filterTextHandler:     filterHandler,
		basicConfig:           basicConfig,
		securityServiceConfig: securityServiceConfig,
	}, nil
}

// NewStreamTextFilterWithCustomFilter 创建文本过滤器时指定自定义过滤函数。
// 该函数通过提供的基础配置和自定义文本处理函数，初始化并返回一个 StreamTextFilter 实例。
//
// 参数:
//
//	basicConfig: 过滤器的基础配置，包括窗口大小和步长大小等参数。
//	filterTextHandler: 自定义的文本过滤处理函数，用于对流中的文本进行过滤。
//
// 返回值:
//
//	*StreamTextFilter: 初始化成功的流文本过滤器实例。
//	error: 如果初始化失败，则返回相应的错误信息。
func NewStreamTextFilterWithCustomFilter(basicConfig FilterBasicConfig, filterTextHandler filterTextFunc) (*StreamTextFilter, error) {
	if basicConfig.WindowSize <= 0 {
		return nil, stderr.Error("window size must be greater than 0")
	}
	if basicConfig.StepSize <= 0 {
		return nil, stderr.Error("step size must be greater than 0")
	}
	if filterTextHandler == nil {
		return nil, stderr.Error("filter text handler cannot be nil")
	}
	return &StreamTextFilter{
		filterTextHandler: filterTextHandler,
		basicConfig:       basicConfig,
	}, nil
}

// SetPassTextHandler 设置文本通过过滤的回调函数
func (f *StreamTextFilter) SetPassTextHandler(handler passTextFunc) error {
	if handler == nil {
		return errors.New("pass text handler cannot be nil")
	}
	f.passTextHandler = handler
	return nil
}

// SetStopTextHandler 设置文本被拦截后的回调函数
func (f *StreamTextFilter) SetStopTextHandler(handler stopTextFunc) error {
	if handler == nil {
		return errors.New("stop text handler cannot be nil")
	}
	f.stopTextHandler = handler
	return nil
}

// SetFilterTextHandler 设置自定义的文本过滤函数
func (f *StreamTextFilter) SetFilterTextHandler(handler filterTextFunc) error {
	if handler == nil {
		return errors.New("filter handler cannot be nil")
	}
	f.filterTextHandler = handler
	return nil
}

// SetBasicConfig 设置基础配置：滑动窗口大小和滑动步长
func (f *StreamTextFilter) SetBasicConfig(config FilterBasicConfig) error {
	if config.WindowSize <= 0 {
		return stderr.Error("window size must be greater than 0")
	}
	if config.StepSize <= 0 {
		return stderr.Error("step size must be greater than 0")
	}
	f.basicConfig = config
	return nil
}

// Append 追加需要处理的流式文本
//
//	开启一个goroutine来异步处理文本，根据配置对文本进行过滤，并根据过滤结果调用相应的处理函数
//	当文本被拦截或处理遇到错误时会停止处理，否则会持续处理直到调用Done方法，处理完成
func (f *StreamTextFilter) Append(text string) error {
	if f.done {
		return stderr.Error("stream text filter is already done")
	}
	if f.err != nil {
		return f.err
	}
	if f.textBuffer == nil {
		f.textBuffer = []rune{}
		f.startProcessing()
	}
	f.textBuffer = append(f.textBuffer, []rune(text)...)
	return nil
}

// startProcessing 启动文本处理流程。
// 开启一个goroutine来异步处理文本，根据配置对文本进行过滤，并根据过滤结果调用相应的处理函数，当文本被拦截或处理遇到错误时会停止处理，否则会持续处理直到调用Done方法，处理完成
func (f *StreamTextFilter) startProcessing() {
	f.wg.Add(1)
	go func() {
		defer f.wg.Done()
		var err error
		for {
			if f.done {
				remainingText := string(f.textBuffer[f.leftWindowIndex:])
				f.filterResult, err = f.filterTextHandler(remainingText)
				if err != nil {
					f.err = ErrFilterTextFailed
					break
				}
				if f.filterResult == nil {
					f.err = stderr.Error("filter result is nil")
					break
				}
				if !f.filterResult.Risk && f.passTextHandler != nil {
					if err = f.passTextHandler(remainingText); err != nil {
						f.err = ErrPassTextFailed
						break
					}
				}
				if f.filterResult.Risk {
					stdlog.Debugf("Risk text is \"%s\", %+v\n", remainingText, f.filterResult)
					f.err = ErrFoundRiskWord
					if f.stopTextHandler == nil {
						break
					}
					if err = f.stopTextHandler(f.filterResult); err != nil {
						f.err = ErrStopTextFailed
						break
					}
					break // 检测到敏感词，立即停止循环发送消息
				}
				break
			}
			if len(f.textBuffer) <= f.basicConfig.WindowSize {
				time.Sleep(100 * time.Millisecond)
				continue
			}
			if len(f.textBuffer)-(f.leftWindowIndex+f.basicConfig.WindowSize) < f.basicConfig.StepSize {
				time.Sleep(100 * time.Millisecond)
				continue
			}
			windowText := string(f.textBuffer[f.leftWindowIndex : f.leftWindowIndex+f.basicConfig.WindowSize])
			f.filterResult, err = f.filterTextHandler(windowText)
			if err != nil {
				f.err = ErrFilterTextFailed
				break
			}
			if f.filterResult == nil {
				f.err = stderr.Error("filter result is nil")
				break
			}
			outWindowText := string(f.textBuffer[f.leftWindowIndex : f.leftWindowIndex+f.basicConfig.StepSize])
			if !f.filterResult.Risk && f.passTextHandler != nil {
				if err = f.passTextHandler(outWindowText); err != nil {
					f.err = ErrPassTextFailed
					break
				}
			}
			if f.filterResult.Risk {
				stdlog.Debugf("Risk text is \"%s\", %+v\n", windowText, f.filterResult)
				f.err = ErrFoundRiskWord
				if f.stopTextHandler == nil {
					break
				}
				if err = f.stopTextHandler(f.filterResult); err != nil {
					f.err = ErrStopTextFailed
					break
				}
				break // 检测到敏感词，立即停止循环发送消息
			}
			f.leftWindowIndex += f.basicConfig.StepSize
		}
	}()
}

// Done 标识流式文本输入完毕，调用该方法后，Append()函数不再接收文本。同时该方法会阻塞，等待流式文本过滤任务完成
// 返回最终的过滤结果及可能出现的错误。
//
// 返回值:
// 	- *CheckSecurityResponse: 过滤操作的结果
// 	- error: 如果在过滤过程中发生错误，则返回该错误；否则返回nil。
func (f *StreamTextFilter) Done() (*CheckSecurityResponse, error) {
	// 将done标志设置为true，表示过滤操作已完成。
	f.done = true
	// 等待文本过滤线程执行完成
	f.wg.Wait()
	// 返回最终的过滤结果和可能出现的错误。
	return f.filterResult, f.err
}
