package triton

import (
	"encoding/json"
	"errors"
	"transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

// OpenAiCompletionReq represents a request structure for completion API.
type OpenAiCompletionReq struct {
	Model            string   `json:"model"`
	Stream           bool     `json:"stream"`
	Prompt           string   `json:"prompt,omitempty"`
	Suffix           string   `json:"suffix,omitempty"`
	MaxTokens        int      `json:"max_tokens,omitempty"`
	Temperature      float64  `json:"temperature,omitempty"`
	TopP             float64  `json:"top_p,omitempty"`
	N                int      `json:"n,omitempty"`
	LogProbs         int      `json:"logprobs,omitempty"`
	Echo             bool     `json:"echo,omitempty"`
	Stop             []string `json:"stop,omitempty"`
	PresencePenalty  float64  `json:"presence_penalty,omitempty"`
	FrequencyPenalty float64  `json:"frequency_penalty,omitempty"`
	BestOf           int      `json:"best_of,omitempty"`
	// LogitBias is must be a token id string (specified by their token ID in the tokenizer), not a word string.
	// incorrect: `"logit_bias":{"You": 6}`, correct: `"logit_bias":{"1639": 6}`
	// refs: https://platform.openai.com/docs/api-reference/completions/create#completions/create-logit_bias
	LogitBias map[string]int `json:"logit_bias,omitempty"`
	User      string         `json:"user,omitempty"`
}

func (o *OpenAiCompletionReq) GetModelInferRequest(innerModelName string) (*pb.ModelInferRequest, error) {
	LLMChatReq, err := o.Cvt2LLMChatReq()
	if err != nil {
		return nil, err
	}
	return LLMChatReq.GetModelInferRequest(innerModelName)
}

// Cvt2LLMChatReq  openai与triton的参数映射,可能需要扩展
func (o *OpenAiCompletionReq) Cvt2LLMChatReq() (*LLMChatReq, error) {
	// 设置推理参数
	params := LLMParams{}
	params.Temperature = o.Temperature
	params.MaxLength = o.MaxTokens
	params.TopP = o.TopP
	params.RepetitionPenalty = o.FrequencyPenalty
	params.NumBeams = o.N
	params.StopWords = o.Stop
	if o.Temperature != 1.0 || o.TopP != 1.0 {
		params.DoSample = true
	}
	if o.Stop != nil {
		params.EarlyStopping = true
	}
	// 转为Std结构
	LLMChatReq := &LLMChatReq{
		Query:  o.Prompt,
		Stream: o.Stream,
		Params: params.AsMap(),
	}
	return LLMChatReq, nil
}
func (o *OpenAiCompletionReq) Parse2InferResp(info *StreamInfo) (interface{}, error) {
	if info == nil || info.ModelInferResponse == nil {
		return nil, errors.New("inferResp cannot be nil")
	}
	content := string(info.ModelInferResponse.RawOutputContents[0][4:])

	// 部分对话模型返回为openai-json格式-(/chat/completion),将其统一为OpenAiCompletionResp格式
	var openAiChatResp OpenAiChatResp
	err := json.Unmarshal([]byte(content), &openAiChatResp)
	if err == nil && openAiChatResp.Id != "" {
		content, _ = openAiChatResp.GetInferResult()
	}

	openAiCompletionResp := &OpenAiCompletionResp{
		ID:      info.RequestId,
		Object:  "text_completion",
		Created: info.Created,
		Model:   o.Model,
		Choices: []CompletionChoice{
			{
				Text:         content,
				Index:        0,
				FinishReason: info.FinishReason,
			},
		},
	}
	return openAiCompletionResp, nil
}

func (o *OpenAiCompletionReq) IsStream() bool {
	return o.Stream
}

// CompletionChoice represents one of possible completions.
type CompletionChoice struct {
	Text         string         `json:"text"`
	Index        int            `json:"index"`
	FinishReason string         `json:"finish_reason"`
	LogProbs     *LogprobResult `json:"logprobs,omitempty""`
}

// LogprobResult represents logprob result of Choice.
type LogprobResult struct {
	Tokens        []string             `json:"tokens"`
	TokenLogprobs []float32            `json:"token_logprobs"`
	TopLogprobs   []map[string]float32 `json:"top_logprobs"`
	TextOffset    []int                `json:"text_offset"`
}

// OpenAiCompletionResp represents a response structure for completion API.
type OpenAiCompletionResp struct {
	ID      string             `json:"id"`
	Object  string             `json:"object"`
	Created int64              `json:"created"`
	Model   string             `json:"model"`
	Choices []CompletionChoice `json:"choices"`
	Usage   *Usage             `json:"usage,omitempty"`
}

func (o *OpenAiCompletionResp) GetInferResult() (string, error) {
	res := ""
	for _, c := range o.Choices {
		res = res + c.Text
	}
	return res, nil
}
