package triton

import "transwarp.io/applied-ai/aiot/vision-std/triton/pb"

type StdModelIOType string

const (
	StdIOTypeCV    StdModelIOType = "CV"
	StdCVInputData                = "IMAGE_BINARY"
	StdCVInputMeta                = "IMAGE_DESC"
	StdCVOutputRes                = "RESULT"

	StdIOTypeNLP    StdModelIOType = "NLP"
	StdNLPInputText                = "TEXT"
	StdNLPOutputRes                = "RESULT"

	StdIOTypeTextGen    StdModelIOType = "TEXT-GEN"
	StdTextGenInputJson                = "JSON"
	StdTextGenOutputRes                = "RESULT"

	StdIOTypeTextVecV1    StdModelIOType = "TEXT-VEC"
	StdTextVecV1InputData                = "TEXT_BINARY"
	StdTextVecV1InputMeta                = "TEXT_DESC"
	StdTextVecV1OutputRes                = "RESULT"

	StdIOTypeTextVecV2    StdModelIOType = "TEXT-VEC-V2"
	StdTextVecV2InputData                = "TEXTS"
	StdTextVecV2OutputRes                = "RESULT"

	StdIOTypeRerank    StdModelIOType = "RERANK"
	StdRerankInputData                = "TEXTS"
	StdRerankOutputRes                = "RESULT"
)

var (
	// StdCVModelIO 通用计算机视觉检测模型的标准 input/output 名称
	StdCVModelIO = NewStdModelIO(
		StdIOTypeCV,
		[]string{StdCVInputData, StdCVInputMeta},
		[]string{StdCVOutputRes},
	)

	// StdNLPModelIO 通用自然语言模型的标准 input/output 名称
	StdNLPModelIO = NewStdModelIO(
		StdIOTypeNLP,
		[]string{StdNLPInputText},
		[]string{StdNLPOutputRes},
	)
	// StdTextGenModelIO 通用文本生成的标准 input/output 名称
	StdTextGenModelIO = NewStdModelIO(
		StdIOTypeTextGen,
		[]string{StdTextGenInputJson},
		[]string{StdTextGenOutputRes},
	)
	// StdTextVecV1ModelIO 通用文本向量的标准 input/output 名称
	StdTextVecV1ModelIO = NewStdModelIO(
		StdIOTypeTextVecV1,
		[]string{StdTextVecV1InputData, StdTextVecV1InputMeta},
		[]string{StdTextVecV1OutputRes},
	)

	// StdTextVecV2ModelIO 通用文本向量的标准 input/output 名称
	StdTextVecV2ModelIO = NewStdModelIO(
		StdIOTypeTextVecV2,
		[]string{StdTextVecV2InputData},
		[]string{StdTextVecV2OutputRes},
	)

	// StdRerankModelIO 通用重排序的标准 input/output 名称
	StdRerankModelIO = NewStdModelIO(
		StdIOTypeRerank,
		[]string{StdRerankInputData},
		[]string{StdRerankOutputRes},
	)

	StdModelIOs = []StdModelIO{
		StdCVModelIO,
		StdNLPModelIO,
		StdTextGenModelIO,
		StdTextVecV1ModelIO,
		StdTextVecV2ModelIO,
	}
)

// StdModelIO 定义了标准的 DLIE 模型的输入输出名称
type StdModelIO struct {
	typ  StdModelIOType
	ins  []string
	outs []string
}

func NewStdModelIO(typ StdModelIOType, ins, outs []string) StdModelIO {
	return StdModelIO{
		typ:  typ,
		ins:  ins,
		outs: outs,
	}
}

func (n *StdModelIO) Type() StdModelIOType {
	return n.typ
}

func (n *StdModelIO) InputNames() []string {
	return n.ins
}

func (n *StdModelIO) OutputNames() []string {
	return n.outs
}

func (n *StdModelIO) InputNameSet() map[string]struct{} {
	ins := make(map[string]struct{}, len(n.ins))
	for _, name := range n.ins {
		ins[name] = struct{}{}
	}
	return ins
}

func (n *StdModelIO) OutputNameSet() map[string]struct{} {
	outs := make(map[string]struct{}, len(n.outs))
	for _, name := range n.outs {
		outs[name] = struct{}{}
	}
	return outs
}

// IsStdCVApi 返回给定的模型配置是否为标准的 Transwarp DLIE CV 接口格式
// 输入类型需符合规范 https://sophon-edge.yuque.com/ta73lw/knqsgi/wi3xk3#qiX5T
// 即：
//
//	# 输入张量信息
//	input [
//	  {
//		# 以二进制格式存储的图像数据
//		name: "IMAGE_BINARY"
//		data_type: TYPE_STRING
//		dims: [ 1 ]
//	  },
//	  {
//		# 以 JSON 格式存储的图像描述数据
//		name: "IMAGE_DESC"
//		data_type: TYPE_STRING
//		dims: [ 1 ]
//	  }
//	]
//
//	# 输出张量信息
//	output [
//	  {
//		# 以 JSON 格式存储的推理结果
//		name: "RESULT"
//		data_type: TYPE_STRING
//		dims: [ 1 ]
//	  }
//	]
func IsStdCVApi(ins []*pb.ModelInput, outs []*pb.ModelOutput) bool {
	return matchStdModelIO(ins, outs, StdCVModelIO)
}

func matchStdModelIO(ins []*pb.ModelInput, outs []*pb.ModelOutput, exp StdModelIO) bool {
	expIns := exp.InputNameSet()
	expOuts := exp.OutputNameSet()
	if len(ins) != len(expIns) || len(outs) != len(expOuts) {
		// 输入或输出数量不匹配
		return false
	}
	for _, in := range ins {
		if _, ok := expIns[in.Name]; !ok {
			// unexpected input name
			return false
		} else {
			// 每个输入仅能出现一次
			delete(expIns, in.Name)
		}
	}
	for _, out := range outs {
		if _, ok := expOuts[out.Name]; !ok {
			// unexpected input name
			return false
		} else {
			// 每个输出仅能出现一次
			delete(expIns, out.Name)
		}
	}
	return true
}

// IsStdModelIO 判断给定的模型输入输出是否和标准模型接口匹配，
// 若存在匹配，则返回匹配的标准接口类型 与 true
// 若不存在，则返回 nil 与 false
func IsStdModelIO(ins []*pb.ModelInput, outs []*pb.ModelOutput) (*StdModelIO, bool) {
	for _, exp := range StdModelIOs {
		if matchStdModelIO(ins, outs, exp) {
			return &exp, true
		}
	}
	return nil, false
}
