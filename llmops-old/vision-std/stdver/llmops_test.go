package stdver

import (
	"os"
	"testing"
)

func TestGetCurtSemVer(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		{name: "dev", want: "0.0.0-dev"},
		{name: "v0.0.0-dev", want: "0.0.0-dev"},
		{name: "0.0.0-dev", want: "0.0.0-dev"},
		{name: "llm-1.4.1-zz", want: "1.4.1-zz"},
		{name: "v1.4.1-pf", want: "1.4.1-pf"},
		{name: "llm-1.4.1", want: "1.4.1"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := os.Setenv(LLMOpsVersionEnvKey, tt.name); err != nil {
				panic(err)
			}
			if got := GetCurtSemVer(); got != tt.want {
				t.<PERSON>("GetCurtSemVer() = %v, want %v", got, tt.want)
			}
		})
	}
}
