package stdver

import (
	"os"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	// LLMOpsVersionEnvKey llmops 的系统服务可通过该环境变量声明当前版本号(一般Dockerfile中通过分支名称传入)
	// e.g.
	// ARG CI_COMMIT_REF_NAME
	// ENV LLMOPS_VERSION=${CI_COMMIT_REF_NAME}
	// 常见取值如下:
	// - dev 		// 表示开发分支, 对应 semver 为 v0.0.0-dev
	// - llm-1.3.5	// 表示正式分支, 对应 semver 为 v1.3.5
	LLMOpsVersionEnvKey = "LLMOPS_VERSION" // 可以通过{{ .Chart.Version }}进行渲染,不带v前缀

	BranchNameDev           = "dev"
	BranchNameReleasePrefix = "llm-"

	SemVerDev = "0.0.0-dev"
)

func GetCurtSemVer() string {
	ver := os.Getenv(LLMOpsVersionEnvKey)
	if ver == "" {
		stdlog.Warnf("semver not specified by env '%s', it defaults to the dev semver", LLMOpsVersionEnvKey)
		ver = SemVerDev
	}
	if strings.HasPrefix(ver, "v") {
		ver = strings.TrimPrefix(ver, "v")
	}
	if strings.HasPrefix(ver, BranchNameReleasePrefix) {
		ver = strings.TrimPrefix(ver, BranchNameReleasePrefix)
	}
	if ver == BranchNameDev {
		ver = SemVerDev
	}
	stdlog.Infof("got ver is '%s'", ver)
	return ver
}
func IsDev() bool {
	return GetCurtSemVer() == SemVerDev
}
func IsRelease() bool {
	return !IsDev()
}
