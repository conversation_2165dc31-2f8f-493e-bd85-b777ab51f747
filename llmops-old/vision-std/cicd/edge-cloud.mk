include common.mk

CLOUD_COMPONENT := $(subst thinger-,thingercloud-,${COMPONENT})

DEPLOY_SERVER ?= root@172.16.251.55
DOCKER_COMPOSE_ROOT ?= /thinger-deploy/thinger-edge-stack

CLOUD_DEPLOY_SERVER ?= root@172.16.251.93
CLOUD_DOCKER_COMPOSE_ROOT ?= /thinger-deploy/thinger-cloud-stack

image-x86:: ## Build and push X86 image
	$(call image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},amd64,${BUILD_VERSION},Dockerfile.cloud.x86_64,${NO_PUSH})

image-arm:: ## Build and push ARM64 image
	$(call image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},arm64,${BUILD_VERSION},Dockerfile.cloud.arm_64,${NO_PUSH})

image-x86-release:: ## Build and push X86 release image
	$(call image-release,${RELEASE_IMAGE_PREFIX}/${CLOUD_COMPONENT},amd64,${RELEASE_VERSION},Dockerfile.cloud.x86_64,${NO_PUSH})

image-arm-release:: ## Build and push ARM64 release image
	$(call image-release,${RELEASE_IMAGE_PREFIX}/arm64-${CLOUD_COMPONENT},arm64,${RELEASE_VERSION},Dockerfile.cloud.arm_64,${NO_PUSH})

deploy::
	$(call deploy,${CLOUD_COMPONENT},${CLOUD_DEPLOY_SERVER},${CLOUD_DOCKER_COMPOSE_ROOT})

build-image-x86:: ## Build and push X86 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},amd64,${BUILD_VERSION},Dockerfile.cloud.x86_64,${NO_PUSH})

build-image-arm:: ## Build and push ARM64 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},arm64,${BUILD_VERSION},Dockerfile.cloud.arm_64,${NO_PUSH})

build-image-x86-release:: ## Build and push X86 release image
	$(call build-image-release,${RELEASE_IMAGE_PREFIX}/${CLOUD_COMPONENT},amd64,${RELEASE_VERSION},Dockerfile.cloud.x86_64,${NO_PUSH})

build-image-arm-release:: ## Build and push ARM64 release image
	$(call build-image-release,${RELEASE_IMAGE_PREFIX}/arm64-${CLOUD_COMPONENT},arm64,${RELEASE_VERSION},Dockerfile.cloud.arm_64,${NO_PUSH})
