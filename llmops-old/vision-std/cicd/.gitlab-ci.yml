image: ***********/aip/base/go1.13.8-builder:sophon-0.0
before_script:
  - source /etc/profile
  - export STD_PATH="../thinger-std"
  - BRANCH_NAME="master"
  - echo ${CI_COMMIT_REF_NAME}
  - if [[ ${CI_COMMIT_REF_NAME} =~ ^branch-[0-9]+\.[0-9]+ ]];then BRANCH_NAME=$(echo ${CI_COMMIT_REF_NAME} | grep -oP "^branch-\d+\.\d+"); fi
  - if [[ ${CI_COMMIT_REF_NAME} =~ ^sophon-[0-9]+\.[0-9]+ ]];then BRANCH_NAME=$(echo ${CI_COMMIT_REF_NAME/sophon/branch} | grep -oP "^branch-\d+\.\d+"); fi
  - git clone -b ${BRANCH_NAME} http://gitlab-ci-token:${CI_JOB_TOKEN}@***********:10080/applied-ai/aiot/thinger-std.git ${STD_PATH}
  - echo ${BRANCH_NAME}
  - cp -f $STD_PATH/cicd/common.mk .
  - cp -f $STD_PATH/cicd/{{mk-type}}.mk ./Makefile
  - shopt -s expand_aliases
  - alias "killdocker=ps aux | grep docker | grep -v grep | awk '{print \$2}' | xargs kill -9 || true"
  - alias "startdocker=(startdocker.sh &) ; sleep 10"

stages:
  - test
  - build
  - deploy

unit_tests:
  stage: test
  script:
    - make test
  tags:
    - k8s

coverage:
  stage: test
  only:
    - master
    - /^branch-\d+\.\d+$/
  script:
    - make coverage
    - make analyze
  artifacts:
    paths:
      - build/coverage.html
    expire_in: 30 days
  tags:
    - k8s
  allow_failure: true

go_build:
  stage: build
  except:
    - master
    - /^branch-\d+\.\d+$/
    - tags
  script:
    - make build-x86
  artifacts:
    paths:
      - dist/
  tags:
    - k8s

image_build_x86:
  stage: build
  only:
    - master
    - /^branch-\d+\.\d+$/
  script:
    - startdocker
    - make build-x86
    - make image-x86
    - killdocker
  artifacts:
    paths:
      - dist/
  tags:
    - k8s

image_build_arm:
  stage: build
  only:
    - master
    - /^branch-\d+\.\d+$/
  script:
    - startdocker
    - make build-arm
    - make image-arm
    - killdocker
  artifacts:
    paths:
      - dist/
  tags:
    - k8s

release_build_x86:
  stage: build
  only:
    - tags
  script:
    - startdocker
    - make build-x86
    - make image-x86-release
    - killdocker
  artifacts:
    paths:
      - dist/
  tags:
    - k8s

release_build_arm:
  stage: build
  only:
    - tags
  script:
    - startdocker
    - make build-arm
    - make image-arm-release
    - killdocker
  artifacts:
    paths:
      - dist/
  tags:
    - k8s

image_deploy:
  stage: deploy
  only:
    - master
    - tags
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - make deploy
  tags:
    - k8s
