include common.mk

CLOUD_COMPONENT := $(subst vision-,visionhub-,${COMPONENT})

DEPLOY_SERVER ?= bykj@172.16.251.75
DOCKER_COMPOSE_ROOT ?= /vision-deploy/node-stack

CLOUD_DEPLOY_SERVER ?= bykj@172.16.251.76
CLOUD_DOCKER_COMPOSE_ROOT ?= /vision-deploy/hub-stack

deploy::
	$(call deploy,${CLOUD_COMPONENT},${CLOUD_DEPLOY_SERVER},${CLOUD_DOCKER_COMPOSE_ROOT})

build-image-x86:: ## Build and push X86 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},amd64,${BUILD_VERSION},Dockerfile.cloud.x86_64,${NO_PUSH})

build-image-arm:: ## Build and push ARM64 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${CLOUD_COMPONENT},arm64,${BUILD_VERSION},Dockerfile.cloud.arm_64,${NO_PUSH})
