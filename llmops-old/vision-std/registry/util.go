package registry

import (
	"errors"
	"net"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type RegisterParam struct {
	ETCDConf    ETCDConfig
	ServiceName string
	ServicePort int
}

func Register(param RegisterParam) error {
	cli, err := NewRegistryClientByETCD(&param.ETCDConf)
	if err != nil {
		return err
	}
	netIP, err := ExternalIP()
	if err != nil {
		stdlog.Error(err)
		return err
	}
	ip := netIP.String()

	svcInfo := NewServiceInfo(ip, param.ServicePort, false, map[string]string{})
	return cli.Register(param.ServiceName, svcInfo)
}

func ExternalIP() (net.IP, error) {
	ifaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}
	for _, iface := range ifaces {
		if iface.Flags&net.FlagUp == 0 {
			continue // interface down
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue // loopback interface
		}
		addrs, err := iface.Addrs()
		if err != nil {
			return nil, err
		}
		for _, addr := range addrs {
			ip := getIpFromAddr(addr)
			if ip == nil {
				continue
			}
			return ip, nil
		}
	}
	return nil, errors.New("connected to the network?")
}

func getIpFromAddr(addr net.Addr) net.IP {
	var ip net.IP
	switch v := addr.(type) {
	case *net.IPNet:
		ip = v.IP
	case *net.IPAddr:
		ip = v.IP
	}
	if ip == nil || ip.IsLoopback() {
		return nil
	}
	ip = ip.To4()
	if ip == nil {
		return nil // not an ipv4 address
	}

	return ip
}
