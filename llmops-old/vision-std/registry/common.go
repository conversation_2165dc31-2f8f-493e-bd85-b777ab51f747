package registry

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"go.etcd.io/etcd/api/v3/v3rpc/rpctypes"
	clientv3 "go.etcd.io/etcd/client/v3"
	"log"
	"os"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const SophonServiceId = "SOPHON_SERVICE_ID"
const KeyPrefix = "/sophon-registry"

type RegistryClient struct {
	Endpoints   []string
	DialTimeout time.Duration
}

type ServiceInfo struct {
	ServiceName string            `json:"serviceName"`
	Id          string            `json:"id"`
	Secure      bool              `json:"secure"`
	Address     string            `json:"address"`
	Port        int               `json:"port"`
	License     string            `json:"license"`
	ConfigInfo  map[string]string `json:"configInfo"`
}

type ETCDConfig struct {
	Endpoints   string
	DialTimeout int
}

func NewServiceInfo(ip string, port int, secure bool, configInfo map[string]string) *ServiceInfo {
	randomId := strings.Replace(uuid.New().String(), "-", "", -1)
	if len(randomId) > 10 {
		randomId = randomId[0:10]
	}

	serviceInfo := &ServiceInfo{
		Id:         randomId,
		Address:    ip,
		Port:       port,
		Secure:     secure,
		ConfigInfo: configInfo,
	}
	return serviceInfo
}

func NewRegistryClientByETCD(etcdCfg *ETCDConfig) (*RegistryClient, error) {
	endpoints := etcdCfg.Endpoints
	dialTimeout := etcdCfg.DialTimeout
	registryClient := &RegistryClient{
		Endpoints:   strings.Split(endpoints, ","),
		DialTimeout: time.Duration(dialTimeout) * time.Second,
	}
	return registryClient, nil
}

func (r *RegistryClient) Register(name string, info *ServiceInfo) error {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   r.Endpoints,
		DialTimeout: r.DialTimeout,
	})

	if err != nil {
		log.Println("connect to etcd failed, err:", err)
		return err
	}
	defer cli.Close()

	kv := clientv3.NewKV(cli)
	lease := clientv3.NewLease(cli)
	var curLeaseId clientv3.LeaseID = 0

	info.Id = fmt.Sprintf("%s-%s", name, info.Id)
	info.ServiceName = name

	serviceId := os.Getenv(SophonServiceId)
	if serviceId == "" {
		return stderr.Internal.Error("no SOPHON_SERVICE_ID")
	}
	key := fmt.Sprintf("%s/%s/%s/%s", KeyPrefix, serviceId, name, info.Id)

	serviceInfoJson, err := json.Marshal(info)
	if err != nil {
		return err
	}

	for {
		if curLeaseId == 0 {
			leaseResp, err := lease.Grant(context.TODO(), 5)
			if err != nil {
				panic(err)
			}

			if _, err := kv.Put(context.TODO(), key, string(serviceInfoJson), clientv3.WithLease(leaseResp.ID)); err != nil {
				panic(err)
			}
			curLeaseId = leaseResp.ID
		} else {
			// 续约租约，如果租约已经过期将curLeaseId复位到0重新走创建租约的逻辑
			if _, err := lease.KeepAliveOnce(context.TODO(), curLeaseId); err == rpctypes.ErrLeaseNotFound {
				curLeaseId = 0
				continue
			}
		}
		time.Sleep(time.Duration(1) * time.Second)
	}

	return nil
}
