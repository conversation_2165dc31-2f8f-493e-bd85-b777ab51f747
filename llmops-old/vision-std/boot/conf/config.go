package conf

import "time"

type EngineType = string

const (
	EngineDocker EngineType = "docker"
	EngineK8S    EngineType = "k8s"
)

type ManagerConfig struct {
	EngineType string `yaml:"engine_type"` // docker or k8s

	//docker config
	DockerApiVersion string `yaml:"docker_api_version"`
	RegistryTokens   string `yaml:"registry_tokens"`
	RegistryTimeout  string `yaml:"registry_timeout"`

	// k8s config
	// InCluster 指示当前进程是否运行在 k8s 集群环境中，本地测试时为false, 生产模式下为True
	InCluster bool `yaml:"in_cluster"`
	// KubeconfigPath 为k8s配置路径，用于使用配置文件初始化K8S Client
	KubeconfigPath string `yaml:"kubeconfig_path"`
	// ImagePullSecret 为调度时，默认附带的镜像下载凭证，需在集群中已存在。为空时，不附带默认镜像下载凭证。
	ImagePullSecret string        `yaml:"image_pull_secret"`
	APITimeout      time.Duration `yaml:"api_timeout"`
}
