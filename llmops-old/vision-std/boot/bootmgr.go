package boot

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/boot/conf"
	"transwarp.io/applied-ai/aiot/vision-std/boot/docker"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/sys"
)

// Manager 为 Edge 提供了统一的容器化调度接口
type Manager interface {
	// SyncServices 将系统中启动的服务调整为传入的列表， 启动未启动的服务，停止非预期的服务
	SyncServices(expects []*pb.BootService) error

	// RunService 启动一个 Edge 边缘服务
	RunService(svc *pb.BootService) error

	// DestroyService 停止并清理一个 Edge 边缘服务
	DestroyService(name string) error

	// Stats 返回此服务及其各个副本的当前资源占用统计信息
	Stats(serviceType pb.ServiceType, name string) *pb.ServiceStats

	// WatchStats 依据给定的间隔输出 BootService 的运行资源占用统计信息
	WatchStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration) <-chan *pb.ServiceStats

	// HandleStats 依据给定的间隔输出 BootService 的运行资源占用统计信息
	HandleStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration, handler func(stats *pb.ServiceStats))

	// NodeStats 返回所有节点的资源统计信息
	NodeStats() []*sys.NodeStats

	// NodesInfo 返回所有节点的资源信息
	NodesInfo() ([]*sys.NodeInfo, error)
}

func NewBootManager(cfg conf.ManagerConfig) (Manager, error) {
	switch cfg.EngineType {
	case conf.EngineDocker:
		return docker.NewBootManager(cfg)
	case conf.EngineK8S, "":
		return k8s.NewBootManager(cfg)
	default:
		return nil, stderr.InvalidParam.Error("invalid boot engine config '%s'", cfg.EngineType)
	}
}
