package k8s

import (
	"context"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	apicore "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	apimeta "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	lstapps "k8s.io/client-go/listers/apps/v1"
	lstbatch "k8s.io/client-go/listers/batch/v1"
	lstcore "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/client-go/util/homedir"
	"k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"k8s.io/metrics/pkg/client/clientset/versioned"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

type ResourceKind = string

const (
	defaultNS     = "default"
	namespaceFile = "/var/run/secrets/kubernetes.io/serviceaccount/namespace"

	defaultMetricSyncInterval = 1 * time.Minute // metric server 指标收集间隔默认即为60s.

	RscKindPod         ResourceKind = "Pod"
	RscKindService     ResourceKind = "Service"
	RscKindStatefulSet ResourceKind = "StatefulSet"
	RscKindDeployment  ResourceKind = "Deployment"
	RscKindJob         ResourceKind = "Job"
)

var (
	ClientNotStartedErr = stderr.NotAllowed.Error("client not started yet")
)

// NewClientFromConfPath 返回给定配置文件对应的k8s集群的client
func NewClientFromConfPath(path string) (*kubernetes.Clientset, error) {
	c, err := FileClusterConfig(path)
	if err != nil {
		return nil, err
	}
	return newClient(c)
}

// NewMetricClientFromConfPath 返回给定配置文件对应的k8s集群的metrics client
func NewMetricClientFromConfPath(path string) (*versioned.Clientset, error) {
	c, err := FileClusterConfig(path)
	if err != nil {
		return nil, err
	}
	return newMetricClient(c)
}

// NewClientInCluster 返回当前进程所在Pod所在的集群Client.
func NewClientInCluster() (*kubernetes.Clientset, error) {
	c, err := InClusterConfig()
	if err != nil {
		return nil, err
	}
	return newClient(c)
}

// NewMetricClientInCluster 返回当前进程所在Pod所在的集群的 Metrics Client.
func NewMetricClientInCluster() (*versioned.Clientset, error) {
	c, err := InClusterConfig()
	if err != nil {
		return nil, err
	}
	return newMetricClient(c)
}

// InClusterConfig 返回当前进程所在Pod所在的集群的配置，若当前进程未运行在K8S Pod中，则返回error
func InClusterConfig() (cfg *rest.Config, err error) {
	// creates the in-cluster config
	cfg, err = rest.InClusterConfig()
	if err != nil {
		err = stderr.Internal.Cause(err, "failed to get the client's in cluster config")
		return
	}
	cfg.QPS = 1000
	cfg.Burst = 1500
	return
}

// FileClusterConfig 返回指定路径中获取的集群的配置; 若path为空,则尝试从默认路径下进行读取
func FileClusterConfig(path string) (cfg *rest.Config, err error) {
	if path == "" {
		// try to replace config path with the default one in homedir
		home := homedir.HomeDir()
		if home == "" {
			return nil, stderr.InvalidParam.Error("cannot found the kube config from either specified path '%s' or default home dir.", path)
		}

		path = filepath.Join(home, ".kube", "config")
		stdlog.Warnf("path has not specify while creating k8s client, replace it with default '%s'", path)
	}

	// use the current context in kubeconfig
	cfg, err = clientcmd.BuildConfigFromFlags("", path)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get the client's config from '%s'", path)
	}
	return
}

func newClient(c *rest.Config) (*kubernetes.Clientset, error) {
	// creates the cli
	cli, err := kubernetes.NewForConfig(c)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to new client from in cluster config")
	}
	return cli, nil
}

func newMetricClient(c *rest.Config) (*versioned.Clientset, error) {
	// creates the cli
	cli, err := versioned.NewForConfig(c)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to new client from in cluster config")
	}
	return cli, nil
}

// IsRunningInCluster 返回当前进程是否运行在K8S Pod中。
func IsRunningInCluster() (bool, error) {
	// creates the in-cluster config
	_, err := rest.InClusterConfig()
	if err == nil {
		return true, nil
	}
	if err == rest.ErrNotInCluster {
		return false, nil
	}
	return false, err
}

// CurrentNamespaceInCluster 返回当前进程所在的Pod所在的Namespace.
// 若未在集群中运行，则返回默认的namespace.
func CurrentNamespaceInCluster() string {
	f, err := os.Open(namespaceFile)
	if err != nil {
		stdlog.WithError(err).Warnf("failed to open namespace file '%s' in cluster", namespaceFile)
		return defaultNS
	}
	defer func() { _ = f.Close() }()

	bs, err := ioutil.ReadAll(f)
	if err != nil {
		stdlog.WithError(err).Warnf("failed to read the namespace from file '%s'", namespaceFile)
		return defaultNS
	}

	return string(bs)
}

type Config struct {
	InCluster          bool          `yaml:"in_cluster" json:"in_cluster"`
	ConfigPath         string        `yaml:"config_path" json:"config_path"`
	Timeout            time.Duration `yaml:"timeout" json:"timeout"`
	MetricSyncInterval time.Duration `yaml:"metric_sync_interval" json:"metric_sync_interval"`
}
type Client struct {
	started    bool
	lister     RscLister             // 查询
	mc         *versioned.Clientset  // 指标
	c          *kubernetes.Clientset // 增删改
	stopCh     chan struct{}
	apiTimeout time.Duration

	metricCache metricCache
}

func NewClient(cfg Config) (*Client, error) {
	if cfg.Timeout <= time.Second || cfg.Timeout > time.Minute {
		cfg.Timeout = time.Second * 5
	}
	if cfg.MetricSyncInterval < 15*time.Second {
		cfg.MetricSyncInterval = defaultMetricSyncInterval
	}

	var cErr, mcErr error
	var c *kubernetes.Clientset
	var mc *versioned.Clientset
	if cfg.InCluster {
		c, cErr = NewClientInCluster()
		mc, mcErr = NewMetricClientInCluster()
	} else {
		c, cErr = NewClientFromConfPath(cfg.ConfigPath)
		mc, mcErr = NewMetricClientFromConfPath(cfg.ConfigPath)
	}
	if cErr != nil || mcErr != nil {
		return nil, stderr.JoinErrors(cErr, mcErr)
	}
	return &Client{
		mc:         mc,
		c:          c,
		apiTimeout: cfg.Timeout,
		metricCache: metricCache{
			ns:           CurrentNamespaceInCluster(),
			syncInterval: cfg.MetricSyncInterval,
		},
	}, nil
}

func (m *Client) Start() error {
	if m.stopCh != nil {
		m.Stop()
	}

	// 创建后续需要用到的 Informer / Lister 对象
	sif := informers.NewSharedInformerFactoryWithOptions(m.c, 5*time.Minute)
	cif := sif.Core().V1()
	aif := sif.Apps().V1()
	bif := sif.Batch().V1()
	m.lister = RscLister{
		DeploymentLister:  aif.Deployments().Lister(),
		DaemonSetLister:   aif.DaemonSets().Lister(),
		ReplicaSetLister:  aif.ReplicaSets().Lister(),
		StatefulSetLister: aif.StatefulSets().Lister(),

		ConfigMapLister: cif.ConfigMaps().Lister(),
		EndpointsLister: cif.Endpoints().Lister(),
		EventLister:     cif.Events().Lister(),
		NamespaceLister: cif.Namespaces().Lister(),
		NodeLister:      cif.Nodes().Lister(),
		PodLister:       cif.Pods().Lister(),
		ServiceLister:   cif.Services().Lister(),

		JobLister: bif.Jobs().Lister(),
	}

	// 启动所有 Informer / Lister, 并等待所有对象完成数据同步
	m.stopCh = make(chan struct{})
	sif.Start(m.stopCh)
	stdlog.Infof("waiting for k8s informer cache sync...")
	res := sif.WaitForCacheSync(m.stopCh)
	stdlog.Infof("k8s informer cache sync done")
	for rt, ok := range res {
		if !ok {
			return stderr.Internal.Error("failed to wait for cache sync of type '%s'", rt.String())
		}
	}

	go m.syncMetricsLoop()
	m.started = true
	return nil
}

func (m *Client) Stop() {
	m.started = false
	if m == nil {
		return
	}
	if m.stopCh == nil {
		return
	}
	close(m.stopCh)
	m.stopCh = nil
	return
}

func (m *Client) GetLister() *RscLister {
	return &m.lister
}

/*
 	该部分为K8S Node / Pod metrics 相关接口封装
	主要基于 MetricsV1beta1Interface 结构进行
*/

// GetPodMetrics 从缓存中返回指定Pod的指标信息（由于Server端采集频率默认为60s, 因此Client端无需重复获取）
// https://github.com/kubernetes-sigs/metrics-server/blob/master/FAQ.md#how-often-metrics-are-scraped
// 由于 metrics client 初始化时，其QPS默认值为5， 因此当启动的实例或者资源太多时，每个资源单独进行指标采集，很容易达到QPS=5的限制；
// 而若通过调大QPS的方式来进行避免，可能会对 api server 造成较大的压力；
// 因此这里采用定期获取所有Pod的Metric信息，并缓存到内存，后续查询时直接从内存中进行返回。
func (m *Client) GetPodMetrics(ns, name string) (*v1beta1.PodMetrics, error) {
	if m.metricCache.ns != ns {
		stdlog.Debugf("unexpected namespace '%s' of pod metric cache", ns)
		return m.getPodMetricsFromAPI(ns, name)
	}
	pm, ok := m.metricCache.getPoMetrics(name)
	if !ok {
		return nil, stderr.NotFound.Error("pod metric of '%s' not found in cache", name)
	}
	return pm, nil
}

func (m *Client) ListNodeMetrics() ([]v1beta1.NodeMetrics, error) {
	nms := m.metricCache.listNodeMetrics()
	if len(nms) == 0 {
		return nil, stderr.Internal.Error("node metrics not found in cache")
	}
	return nms, nil
}

func (m *Client) listPodMetricsFromAPI(ns string) (*v1beta1.PodMetricsList, error) {
	ctx, _ := m.newCallCtx()
	pm, err := m.mc.MetricsV1beta1().PodMetricses(ns).List(ctx, apimeta.ListOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list pod metrics")
	}
	return pm, nil
}

func (m *Client) getPodMetricsFromAPI(ns, name string) (*v1beta1.PodMetrics, error) {
	ctx, _ := m.newCallCtx()
	pm, err := m.mc.MetricsV1beta1().PodMetricses(ns).Get(ctx, name, apimeta.GetOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get pod metrics")
	}
	return pm, nil
}

func (m *Client) listNodeMetricsFromAPI() (*v1beta1.NodeMetricsList, error) {
	ctx, _ := m.newCallCtx()
	nms, err := m.mc.MetricsV1beta1().NodeMetricses().List(ctx, apimeta.ListOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes metrics")
	}
	return nms, nil
}

// syncMetricsLoop 定时从Metric Server获取所有Pod&Node Metrics数据，并缓存在内存中
// 同步频率暂时
func (m *Client) syncMetricsLoop() {
	stdlog.Infof("begin to sync metrics loop of namespace '%s' with a '%s' interval", m.metricCache.ns, m.metricCache.syncInterval.String())
	// 立即进行首次更新
	m.updateMetricsCache()
	ticker := time.NewTicker(m.metricCache.syncInterval)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			m.updateMetricsCache()
		case <-m.stopCh:
			stdlog.Infof("sync metrics loop exited, cause stop channel closed")
		}
	}
}

// updateMetricsCache 同步 node&pod metrics 到缓存
func (m *Client) updateMetricsCache() {
	stdlog.Infof("begin to update metrics cache of namespace '%s'", m.metricCache.ns)
	nl, err := m.listNodeMetricsFromAPI()
	if err != nil {
		stdlog.WithError(err).Errorf("failed to list node metrics while updating cache")
		m.metricCache.clearNodeMetrics()
	} else {
		m.metricCache.setNodeMetrics(nl.Items)
	}

	pl, err := m.listPodMetricsFromAPI(m.metricCache.ns)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to list pods metrics while updating cache")
		m.metricCache.clearPoMetrics()
	} else {
		m.metricCache.setPoMetrics(pl.Items)
	}
	stdlog.Infof("metrics cache updating completed")
}

/*
	 	该部分为K8S 常用资源的查询接口封装
		主要基于 Informer / Lister 结构进行
*/
func (m *Client) GetPod(ns, name string) (*apicore.Pod, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	po, err := m.lister.Pods(ns).Get(name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get pod")
	}
	return po.DeepCopy(), nil
}

func (m *Client) GetPodWithEvents(ns, name string) (*apicore.Pod, []*apicore.Event, error) {
	if !m.started {
		return nil, nil, ClientNotStartedErr
	}
	po, err := m.GetPod(ns, name)
	if err != nil {
		return nil, nil, err
	}

	el, err := m.GetPodEvents(ns, name)
	if err != nil {
		return nil, nil, err
	}

	return po, el, nil
}

func (m *Client) GetPodEvents(ns, name string) ([]*apicore.Event, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	el, err := m.ListEvents(ns, name, RscKindPod)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get the event of pod %s", name)
	}
	return el, nil
}

func (m *Client) GetStatefulSetEvents(ns, name string) ([]*apicore.Event, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	el, err := m.ListEvents(ns, name, RscKindStatefulSet)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get the event of stateful set %s", name)
	}
	return el, nil
}

func (m *Client) GetDeploymentEvents(ns, name string) ([]*apicore.Event, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	el, err := m.ListEvents(ns, name, RscKindDeployment)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get the event of deployment %s", name)
	}
	return el, nil
}

func (m *Client) GetJobEvents(ns, name string) ([]*apicore.Event, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	el, err := m.ListEvents(ns, name, RscKindJob)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get the event of job %s", name)
	}
	return el, nil
}

func (m *Client) ListEvents(ns, name, kind string) ([]*apicore.Event, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	s := labels.Set{}
	els, err := m.lister.Events(ns).List(s.AsSelector())
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list events of 'ns=%s,kind=%s,name=%s'", ns, kind, name)
	}
	desiredEvents := make([]*apicore.Event, 0)
	for _, el := range els {
		if el.InvolvedObject.Name == name && el.InvolvedObject.Kind == kind && el.InvolvedObject.Namespace == ns {
			desiredEvents = append(desiredEvents, el)
		}
	}
	return desiredEvents, nil
}

func (m *Client) GetNode(name string) (*apicore.Node, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	n, err := m.lister.NodeLister.Get(name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get node '%s'", name)
	}
	return n, nil
}

func (m *Client) ListNodes() ([]*apicore.Node, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	ns, err := m.lister.NodeLister.List(labels.Everything())
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes")
	}
	return ns, nil
}

func (m *Client) ListNodesWithLabels(lbs map[string]string) ([]*apicore.Node, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	ns, err := m.lister.NodeLister.List(labels.SelectorFromSet(lbs))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes")
	}
	return ns, nil
}

func (m *Client) ListServices(ns string, labelSelector string) ([]*apicore.Service, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	selector, err := labels.Parse(labelSelector)
	if err != nil {
		return nil, stderr.InvalidParam.Cause(err, "invalid label selector %s", labelSelector)
	}
	ss, err := m.lister.Services(ns).List(selector)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list services by label selector %s", labelSelector)
	}
	return ss, nil
}

func (m *Client) ListServicesInCurtNS(labelSelector string) ([]*apicore.Service, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	return m.ListServices(CurrentNamespaceInCluster(), labelSelector)
}

func (m *Client) ListPodsByLabels(ns string, lbs map[string]string) ([]*apicore.Pod, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	ps, err := m.lister.Pods(ns).List(labels.SelectorFromSet(lbs))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list pods by labels '%s'", lbs)
	}
	return ps, nil
}

func (m *Client) ListPodsByDaemonSet(ns, dsName string) ([]*apicore.Pod, error) {
	if !m.started {
		return nil, ClientNotStartedErr
	}
	ds, err := m.lister.DaemonSets(ns).Get(dsName)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get k8s daemon set by name : %s", dsName)
	}
	return m.ListPodsByLabels(ns, ds.Spec.Selector.MatchLabels)
}

/*
该部分为K8S API Server 部分直接操作的接口封装
主要用于对资源进行 增删改 操作
同时也包含一部分直接通过 API Server 查询的接口，可能性能较差，同时给API Server造成比较大的性能压力，不建议使用
*/
func (m *Client) UpdatePod(pod *apicore.Pod) (*apicore.Pod, error) {
	ctx, _ := m.newCallCtx()
	po, err := m.c.CoreV1().Pods(pod.Namespace).Update(ctx, pod, apimeta.UpdateOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to update pod")
	}
	return po, nil
}

// TODO 创建service deployment statefulset 代码重复较多，抽取公共方法
func (m *Client) CreateOrUpdateService(ns string, svc *apicore.Service) error {
	ctx, _ := m.newCallCtx()
	return m.CreateOrUpdateServiceWithCtx(ctx, ns, svc)
}

func (m *Client) CreateOrUpdateServiceWithCtx(ctx context.Context, ns string, svc *apicore.Service) error {
	cfgMD5 := ""
	if svc.Annotations != nil {
		v, ok := svc.Annotations[AnnotationConfigMD5]
		if ok {
			cfgMD5 = v
		}
	}
	// 检查service是否已存在
	sif := m.c.CoreV1().Services(ns)
	// 注： 无论对应Service是否存在，该接口均会返回一个非 nil Service
	existSvc, err := sif.Get(ctx, svc.Name, apimeta.GetOptions{})
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of service '%s'", svc.Name)
	}
	// svc 已存在
	if existSvc != nil && existSvc.Name != "" {
		if existSvc.Annotations[AnnotationConfigMD5] == cfgMD5 {
			// 配置未改变
			stdlog.Infof("k8s service already existed and the config hasn't changed, skip creating service")
			return nil
		}
		// 配置改变，删除已存在service
		stdlog.Infof("delete existing previous service firstly")
		if err := sif.Delete(ctx, svc.Name, apimeta.DeleteOptions{}); err != nil {
			return stderr.Wrap(err, "failed to del existing previous service")
		}
	}

	_, err = sif.Create(ctx, svc, apimeta.CreateOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create k8s service")
	}
	return nil
}
func (m *Client) CreateOrUpdateDeployment(ns string, dp *appsv1.Deployment) error {
	ctx, _ := m.newCallCtx()
	return m.CreateOrUpdateDeploymentWithCtx(ctx, ns, dp)
}

func (m *Client) CreateOrUpdateDeploymentWithCtx(ctx context.Context, ns string, dp *appsv1.Deployment) error {
	cfgMD5 := toolkit.MD5(dp.Spec)
	// 检查deployment是否已存在
	dif := m.c.AppsV1().Deployments(ns)
	// 注： 无论对应Deployment是否存在，该接口均会返回一个非 nil Deployment
	existDp, err := dif.Get(ctx, dp.Name, apimeta.GetOptions{})
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of deployment '%s'", dp.Name)
	}
	// dp 已存在
	if existDp != nil && existDp.Name != "" {
		if existDp.Annotations[AnnotationConfigMD5] == cfgMD5 {
			// 配置未改变
			stdlog.Infof("k8s service already existed and the config hasn't changed, skip creating service")
			return nil
		}
		// 配置改变，删除已存在service
		stdlog.Infof("delete existing previous service firstly")
		if err := dif.Delete(ctx, dp.Name, apimeta.DeleteOptions{}); err != nil {
			return stderr.Wrap(err, "failed to del existing previous service")
		}
	}
	// set config md5 annotation
	if dp.Annotations == nil {
		dp.Annotations = make(map[string]string)
	}
	dp.Annotations[AnnotationConfigMD5] = cfgMD5

	_, err = dif.Create(ctx, dp, apimeta.CreateOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create k8s service")
	}
	return nil
}
func (m *Client) CreateOrUpdateStatefulSet(ns string, dp *appsv1.StatefulSet) error {
	ctx, _ := m.newCallCtx()
	return m.CreateOrUpdateStatefulSetWithCtx(ctx, ns, dp)
}

func (m *Client) CreateOrUpdateStatefulSetWithCtx(ctx context.Context, ns string, dp *appsv1.StatefulSet) error {
	cfgMD5 := ""
	if dp.Annotations != nil {
		v, ok := dp.Annotations[AnnotationConfigMD5]
		if ok {
			cfgMD5 = v
		}
	}
	// 检查deployment是否已存在
	sif := m.c.AppsV1().StatefulSets(ns)
	// 注： 无论对应Deployment是否存在，该接口均会返回一个非 nil Deployment
	existDp, err := sif.Get(ctx, dp.Name, apimeta.GetOptions{})
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of deployment '%s'", dp.Name)
	}
	// dp 已存在
	if existDp != nil && existDp.Name != "" {
		if existDp.Annotations[AnnotationConfigMD5] == cfgMD5 {
			// 配置未改变
			stdlog.Infof("k8s service already existed and the config hasn't changed, skip creating service")
			return nil
		}
		// 配置改变，删除已存在service
		stdlog.Infof("delete existing previous service firstly")
		if err := sif.Delete(ctx, dp.Name, apimeta.DeleteOptions{}); err != nil {
			return stderr.Wrap(err, "failed to del existing previous service")
		}
	}
	_, err = sif.Create(ctx, dp, apimeta.CreateOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create k8s service")
	}
	return nil
}
func (m *Client) DeleteStatefulSet(ns, name string) error {
	ctx, _ := m.newCallCtx()
	err := m.c.AppsV1().StatefulSets(ns).Delete(ctx, name, apimeta.DeleteOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to delete stateful set %s", name)
	}
	return nil
}

func (m *Client) DeleteService(ns, name string) error {
	stdlog.Infof("delete the k8s service of %s", name)
	ctx, _ := m.newCallCtx()
	err := m.c.CoreV1().Services(ns).Delete(ctx, name, apimeta.DeleteOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create k8s service")
	}
	stdlog.Infof("succeed to delete the k8s service of %s", name)
	return nil
}

func (m *Client) DeleteDeployment(ns, name string) error {
	stdlog.Infof("delete the k8s deployment of %s", name)
	ctx, _ := m.newCallCtx()
	err := m.c.AppsV1().Deployments(ns).Delete(ctx, name, apimeta.DeleteOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create k8s service")
	}
	stdlog.Infof("succeed to delete the k8s service of %s", name)
	return nil
}

// ExecCmdInPodContainer 在pod container执行命令，只能在pod使用
func (m *Client) ExecCmdInPodContainer(containerName string, podName string, ns string, command []string) error {
	config, err := rest.InClusterConfig()
	if err != nil {
		return stderr.Wrap(err, "failed to get k8s restful client config in cluster")
	}
	execRequest := m.c.CoreV1().RESTClient().Post().Resource("pods").Name(podName).Namespace(ns).SubResource("exec")
	option := &apicore.PodExecOptions{
		Stdin:     false,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
		Container: containerName,
		Command:   command,
	}
	execRequest.VersionedParams(option, scheme.ParameterCodec)
	_, err = remotecommand.NewSPDYExecutor(config, "POST", execRequest.URL())
	if err != nil {
		return stderr.Wrap(err, "failed to exec command in pod container")
	}
	// TODO 通过Stream返回执行过程输出到指定输出设备
	return nil
}

// deprecated
func (m *Client) GetPodFromAPI(ns, name string) (*apicore.Pod, error) {
	ctx, _ := m.newCallCtx()
	po, err := m.c.CoreV1().Pods(ns).Get(ctx, name, apimeta.GetOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get pod")
	}
	return po, nil
}

func (m *Client) ListPodsByLabelsFromAPI(ns string, labelSelector string) ([]apicore.Pod, error) {
	ctx, _ := m.newCallCtx()
	ps, err := m.c.CoreV1().Pods(ns).List(ctx, apimeta.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list pods by labels '%s'", labelSelector)
	}
	return ps.Items, nil
}

// deprecated
func (m *Client) GetNodeFromAPI(name string) (*apicore.Node, error) {
	ctx, _ := m.newCallCtx()
	n, err := m.c.CoreV1().Nodes().Get(ctx, name, apimeta.GetOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get node '%s'", name)
	}
	return n, nil
}

// deprecated
func (m *Client) ListServicesFromAPI(ns string, labelSelector string) (*apicore.ServiceList, error) {
	ctx, _ := m.newCallCtx()
	ss, err := m.c.CoreV1().Services(ns).List(ctx, apimeta.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list services by label selector %s", labelSelector)
	}
	return ss, nil
}

// deprecated
func (m *Client) ListPodsByDaemonSetFromAPI(ns, dsName string) (*apicore.PodList, error) {
	ctx, _ := m.newCallCtx()
	ds, err := m.c.AppsV1().DaemonSets(ns).Get(ctx, dsName, apimeta.GetOptions{})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get k8s daemon set by name : %s", dsName)
	}
	ps, err := m.c.CoreV1().Pods(ns).List(ctx, apimeta.ListOptions{
		LabelSelector: labels.FormatLabels(ds.Spec.Selector.MatchLabels),
	})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes")
	}
	return ps, nil
}

// deprecated
func (m *Client) ListEventsFromAPI(ns, name, kind string) (*apicore.EventList, error) {
	ctx, _ := m.newCallCtx()
	el, err := m.c.CoreV1().Events(ns).List(ctx, apimeta.ListOptions{
		TypeMeta: apimeta.TypeMeta{},
		FieldSelector: labels.Set{
			"involvedObject.kind":      kind,
			"involvedObject.name":      name,
			"involvedObject.namespace": ns,
		}.String(),
	})
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list events of 'ns=%s,kind=%s,name=%s'", ns, kind, name)
	}
	return el, nil
}

func (m *Client) newCallCtx() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), m.apiTimeout)
}

// lister 包含了当前Client封装的接口所用到的或者可能后续会用到的 Lister对象
// 如需额外的List类型，可前往 "k8s.io/client-go/listers/" 下查找对应的资源接口；
// Lister 主要用于同步并实时监听集群资源的改动，并缓存至内存，以加速常用资源的查询以及减轻 K8S API Server 的压力；
type RscLister struct {
	// app listers
	// "k8s.io/client-go/listers/apps/v1"
	lstapps.DeploymentLister
	lstapps.DaemonSetLister
	lstapps.ReplicaSetLister
	lstapps.StatefulSetLister

	// core v1 listers
	// "k8s.io/client-go/listers/core/v1"
	lstcore.ConfigMapLister
	lstcore.EndpointsLister
	lstcore.EventLister
	lstcore.NamespaceLister
	lstcore.NodeLister
	lstcore.PodLister
	lstcore.ServiceLister

	// batch v1 listers
	// "k8s.io/client-go/listers/batch/v1"
	lstbatch.JobLister
}
