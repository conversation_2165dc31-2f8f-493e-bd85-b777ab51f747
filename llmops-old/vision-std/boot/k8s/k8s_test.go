package k8s

import (
	"context"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/rest"
	"k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"testing"
	"time"
)

func TestNewMetricClientFromConfPath(t *testing.T) {
	tests := []struct {
		name string
		path string
	}{
		{
			name: "user default kube config",
			path: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mc, err := NewMetricClientFromConfPath(tt.path)
			if err != nil {
				t.Fatal(err)
			}

			// Test get nodes metrics
			ns, err := mc.MetricsV1beta1().NodeMetricses().List(context.Background(), v1.ListOptions{})
			if err != nil {
				t.Fatal(err)
			}

			for _, evt := range ns.Items {
				t.Logf("%+v", evt)
			}

			// Test get pods metrics
			pos, err := mc.MetricsV1beta1().PodMetricses(defaultNS).List(context.Background(), v1.ListOptions{})
			if err != nil {
				t.Fatal(err)
			}

			for _, evt := range pos.Items {
				t.Logf("%+v", evt)
			}
		})
	}
}

var testPodName = "autocv-testbed-686f679fb9-sf6vm"

func Test_myClient(t *testing.T) {
	cli, err := NewClient(Config{
		Timeout:    time.Minute,
		InCluster:  false,
		ConfigPath: "/etc/rancher/k3s/k3s.yaml",
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Run("test get pod metrics", func(t *testing.T) {
		p, err := cli.GetPod("default", testPodName)
		if err != nil {
			t.Fatal(err)
			return
		}
		pm, err := cli.GetPodMetrics("default", testPodName)
		if err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("%+v", pm)

		ws := podMetric2WorkerStats(p.Status, pm)
		t.Logf("%+v", ws)
	})

	t.Run("test get pod events", func(t *testing.T) {
		po, el, err := cli.GetPodWithEvents(defaultNS, testPodName)
		if err != nil {
			t.Fatal(err)
		}
		t.Logf("%+v", po)
		t.Logf("%+v", el)
	})

	t.Run("test list node metrics", func(t *testing.T) {
		nms, err := cli.ListNodeMetrics()
		if err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("%+v", nms)

	})

	t.Run("test list nodes ", func(t *testing.T) {
		ns, err := cli.ListNodes()
		if err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("%+v", ns)
	})

	t.Run("test get node stats ", func(t *testing.T) {
		bc := &BootController{mc: cli}
		ns, err := bc.NodeStats()
		if err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("%+v", ns)
	})

	t.Run("test get pods by daemon set ", func(t *testing.T) {
		pods, err := cli.ListPodsByDaemonSet("default", "autocv-registry-ds")
		if err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("%+v", pods)
	})

	t.Run("test create or update service ", func(t *testing.T) {
		svc := &corev1.Service{
			TypeMeta: v1.TypeMeta{
				Kind:       RscKindService,
				APIVersion: AppV1,
			},
			ObjectMeta: v1.ObjectMeta{
				Name:            "test-service",
				Namespace:       "default",
				Labels:          map[string]string{"testkey": "testvalue"},
				Annotations:     map[string]string{"testkey": "testvalue"},
				OwnerReferences: nil,
				Finalizers:      nil,
				ClusterName:     "",
				ManagedFields:   nil,
			},
			Spec: corev1.ServiceSpec{
				Ports: []corev1.ServicePort{{
					Name:       "test",
					Protocol:   "TCP",
					Port:       8080,
					TargetPort: intstr.FromInt(80),
				}},
				Selector: map[string]string{"testkey": "testvalue"},
			},
		}
		t.Logf("creating first service")
		if err := cli.CreateOrUpdateService("default", svc); err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("updating exsited service with no change")
		if err := cli.CreateOrUpdateService("default", svc); err != nil {
			t.Fatal(err)
			return
		}

		t.Logf("updating exsited service with spec change")
		svc.Spec.Selector["newKey"] = "newValue"
		if err := cli.CreateOrUpdateService("default", svc); err != nil {
			t.Fatal(err)
			return
		}
		t.Logf("success")
	})

}

func TestParseSelector(t *testing.T) {
	s, err := labels.Parse("")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("empty selector : %s", s.String())
	t.Logf("nothing selector : %s", labels.Nothing().String())
	t.Logf("everything selector : %s", labels.Everything().String())
}

func TestWatchPodMetrics(t *testing.T) {
//		confContent := `apiVersion: v1
//clusters:
//- cluster:
//    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJkakNDQVIyZ0F3SUJBZ0lCQURBS0JnZ3Foa2pPUFFRREFqQWpNU0V3SHdZRFZRUUREQmhyTTNNdGMyVnkKZG1WeUxXTmhRREUyTXpReU56RXhNRFV3SGhjTk1qRXhNREUxTURReE1UUTFXaGNOTXpFeE1ERXpNRFF4TVRRMQpXakFqTVNFd0h3WURWUVFEREJock0zTXRjMlZ5ZG1WeUxXTmhRREUyTXpReU56RXhNRFV3V1RBVEJnY3Foa2pPClBRSUJCZ2dxaGtqT1BRTUJCd05DQUFUbkJBZXArSnZrS0RoWHBBSWdNRW0yNXovL2xnMUVld1RnY2IrRWlhZlMKQUZwSkppYTBGa3RhZmNjMkttSVJxZXQ0WC9kZkxoUFdJaWZmd3pjd1BSdzFvMEl3UURBT0JnTlZIUThCQWY4RQpCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVXpqYm9RQ3N4dFRJWCtjNWdiSVVtCkJZYUMwbEV3Q2dZSUtvWkl6ajBFQXdJRFJ3QXdSQUlnS0UzSXMwVnc2VzNmekIrOGtvL0FvcWdWVE5CZkcwY0UKY2VJTnA2cExHcDRDSUJ3OWQyeHVZclNScU9RSDBlU2lsaXlkcTlYaTNlaXpvcERjRDRsQ0gzbUIKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
//    server: https://*************:6443
//  name: default
//contexts:
//- context:
//    cluster: default
//    user: default
//  name: default
//current-context: default
//kind: Config
//preferences: {}
//users:
//- name: default
//  user:
//    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJrRENDQVRlZ0F3SUJBZ0lJZWMrQmpKZk9WR293Q2dZSUtvWkl6ajBFQXdJd0l6RWhNQjhHQTFVRUF3d1kKYXpOekxXTnNhV1Z1ZEMxallVQXhOak0wTWpjeE1UQTFNQjRYRFRJeE1UQXhOVEEwTVRFME5Wb1hEVEl6TVRBeApOekEwTURReU9Gb3dNREVYTUJVR0ExVUVDaE1PYzNsemRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41CmMzUmxiVHBoWkcxcGJqQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJKRk9ydVlZNnBQREdnRE8KWWR1cC9xSDNCdFZLbzUwZTdnQUoyaWxCTVR2RnVpY1p4c0hyQlREZWtVL0IyR2JvUjlkSTBSUnVaa2xKNElDVwppKzQxYXZLalNEQkdNQTRHQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBakFmCkJnTlZIU01FR0RBV2dCVDBPZXN0SlA3UHRvbXdqRm91VmNQNGV2RUd0VEFLQmdncWhrak9QUVFEQWdOSEFEQkUKQWlBQlMwUE1iR0JuWGo4SkFsaWlkY1ZMaDRZQTA3SzNnc2xxYlFVbGNOd3RxUUlnSFUrMmpjd3dPNmVaSFhYbApmZUpJY2Jqd1hMWlFhWmVza0Znd01nVG1VSzA9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0KLS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJlRENDQVIyZ0F3SUJBZ0lCQURBS0JnZ3Foa2pPUFFRREFqQWpNU0V3SHdZRFZRUUREQmhyTTNNdFkyeHAKWlc1MExXTmhRREUyTXpReU56RXhNRFV3SGhjTk1qRXhNREUxTURReE1UUTFXaGNOTXpFeE1ERXpNRFF4TVRRMQpXakFqTVNFd0h3WURWUVFEREJock0zTXRZMnhwWlc1MExXTmhRREUyTXpReU56RXhNRFV3V1RBVEJnY3Foa2pPClBRSUJCZ2dxaGtqT1BRTUJCd05DQUFSaHRWVkQyMU9IK3V3Vm4zeHEwUzl5dEsrTERLNVdFRi9tbk5MS0pFejQKN2tONXFXN09kd21ScHpEREtteDUvVkN2d3J1MXM4NzFTcXhHZ0p6a3FHZmlvMEl3UURBT0JnTlZIUThCQWY4RQpCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVTlEbnJMU1QrejdhSnNJeGFMbFhECitIcnhCclV3Q2dZSUtvWkl6ajBFQXdJRFNRQXdSZ0loQUxjM0pTNDU0TlUzTUU3TlU0Myt0MEh4TnFkaVVHeUQKN2hBYjAyaXZ6US9MQWlFQTVUTWRYYnJ4a3Qra2hDT0wzamQ4ek43RStlZWdpTkxGQmF6WVhQQjRtNlU9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
//    client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************`
//		confPath := "/tmp/83.yaml"
//		f, err := os.OpenFile(confPath, os.O_CREATE|os.O_RDWR|os.O_TRUNC, os.ModePerm)
//		if err != nil {
//			t.Fatal(err)
//		}
//		if _, err := f.Write([]byte(confContent)); err != nil {
//			t.Fatal(err)
//		}
//	mc, err := NewMetricClientFromConfPath(confPath)
//	if err != nil {
//		t.Fatal(err)
//	}
	mc, err := newMetricClient(&rest.Config{
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: true,
		},
		Host:        "https://*************",
		BearerToken: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
	})
	if err != nil {
		t.Fatal(err)
	}

	ms, err := mc.MetricsV1beta1().PodMetricses("default").List(context.Background(), v1.ListOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("metrics %+v", ms)

	watcher, err := mc.MetricsV1beta1().PodMetricses("default").Watch(context.Background(), v1.ListOptions{})
	if err != nil {
		// k8s_test.go:234: the server could not find the requested resource (get pods.metrics.k8s.io)
		t.Fatal(err)
	}
	for res := range watcher.ResultChan() {
		t.Logf("%+v", res)
		pm := res.Object.(*v1beta1.PodMetrics)
		t.Logf("%+v", pm)
	}
}
