package main

import (
	"fmt"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
)

var cli *k8s.Client

func main() {
	if err := initK8SCli(); err != nil {
		panic(err)
	}

	count := 3
	interval := time.Second
	fmt.Printf("\n\n1.A Test get pos by lister")
	for i := 0; i < count; i++ {
		s := time.Now()
		pos, err := cli.ListPodsByLabels("autocv", nil)
		if err != nil {
			panic(err)
		}
		fmt.Printf("\n\t [%d]list all pods(%d) in autocv cost %s", i, len(pos), time.Now().Sub(s).String())
		time.Sleep(interval)
	}

	fmt.Printf("\n\n1.B Test get pos from api server")
	for i := 0; i < count; i++ {
		s := time.Now()
		pos, err := cli.ListPodsByLabelsFromAPI("autocv", "")
		if err != nil {
			panic(err)
		}
		fmt.Printf("\n\t [%d]list all pods(%d) in autocv cost %s", i, len(pos), time.Now().Sub(s).String())
		time.Sleep(interval)
	}

	fmt.Printf("\n\n2.A Test get svcs by lister")
	for i := 0; i < count; i++ {
		s := time.Now()
		got, err := cli.ListServices("autocv", "")
		if err != nil {
			panic(err)
		}
		fmt.Printf("\n\t [%d]list all svcs(%d) in autocv cost %s", i, len(got), time.Now().Sub(s).String())
		time.Sleep(interval)
	}

	fmt.Printf("\n\n2.B Test get svcs from api server")
	for i := 0; i < count; i++ {
		s := time.Now()
		got, err := cli.ListServicesFromAPI("autocv", "")
		if err != nil {
			panic(err)
		}
		fmt.Printf("\n\t [%d]list all svcs(%d) in autocv cost %s", i, len(got.Items), time.Now().Sub(s).String())
		time.Sleep(interval)
	}
}

func initK8SCli() (err error) {
	c, err := k8s.NewClient(k8s.Config{
		InCluster: true,
		Timeout:   time.Minute,
	})
	if err != nil {
		return
	}
	cli = c
	return cli.Start()
}
