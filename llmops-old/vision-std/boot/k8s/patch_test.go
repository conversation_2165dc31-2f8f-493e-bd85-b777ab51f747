package k8s

import (
	"fmt"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func TestGeneratePatch(t *testing.T) {
	var replica int32
	replica = 1
	old := &appsv1.Deployment{
		TypeMeta: v1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "v1",
		},
		ObjectMeta: v1.ObjectMeta{
			Annotations: map[string]string{
				"test": "test",
			},
			Name:      "test-thingerDeployments",
			Namespace: "default",
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replica,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: v1.ObjectMeta{
					Namespace: "default",
					Name:      "test-pods",
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						corev1.Container{
							Image: "test",
						},
					},
				},
			},
		},
	}
	new := &appsv1.Deployment{
		TypeMeta: v1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "v1",
		},
		ObjectMeta: v1.ObjectMeta{
			Annotations: map[string]string{
				"test": "test",
				//	"new":  "new",
			},
			Name:      "test-thingerDeployments",
			Namespace: "default",
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replica,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: v1.ObjectMeta{
					Namespace: "default",
					Name:      "test-pods",
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						corev1.Container{
							Image: "test",
							//			Args:  []string{"1111"},
						},
					},
				},
			},
		},
		//Status: appsv1.DeploymentStatus{
		//	Replicas: 5,
		//},
	}

	diff, err := GeneratePatch(old, new)
	if err != nil {
		println(err.Error())
		panic(err)
	}
	if diff == nil {
		fmt.Println("null")
	}
	fmt.Println(string(diff))

	te := old.DeepCopy()
	te.Spec.Template.ClusterName = "tet"
	diff, err = GeneratePatch(old, te)
	if err != nil {
		println("fff")
		panic(err)
	}
	fmt.Println(string(diff))
}

func TestIsObjectNoDiff(t *testing.T) {
	err := stderr.ObjectNoDifference.Cause(nil, "")
	if IsObjectNoDiff(err) {
		println("true")
	}
}
