package k8s

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/boot/conf"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/sys"
)

type BootManager struct {
	boot *BootController
	cfg  conf.ManagerConfig
}

func NewBootManager(cfg conf.ManagerConfig) (*BootManager, error) {
	boot, err := NewBootController(cfg.InCluster, cfg.KubeconfigPath)
	if err != nil {
		return nil, err
	}
	return &BootManager{
		boot: boot,
		cfg:  cfg,
	}, nil
}

func (k *BootManager) SyncServices(expects []*pb.BootService) error {
	return k.boot.Sync(expects)
}

func (k *BootManager) RunService(svc *pb.BootService) error {
	stdlog.Infof("====== Run BootService '%s' ======", svc.Name)
	if svc.Config == nil {
		return stderr.InvalidParam.Error("invalid boot service %+v", svc)
	}
	if ps := k.cfg.ImagePullSecret; ps != "" {
		stdlog.Infof("add default image pull secret '%s'", ps)
		svc.Config.ImagePullSecrets = append(svc.Config.ImagePullSecrets, ps)
	}
	ctx := context.Background()
	return k.boot.CreateOrUpdate(ctx, svc)
}

func (k *BootManager) DestroyService(name string) error {
	stdlog.Infof("====== Destroy BootService '%s' ======", name)

	ctx := context.Background()
	if err := k.boot.Delete(ctx, name); err != nil {
		return err
	}
	return nil
}

func (k *BootManager) HandleStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration, handler func(stats *pb.ServiceStats)) {
	for stats := range k.WatchStats(ctx, serviceType, name, interval) {
		handler(stats)
	}
}

func (k *BootManager) WatchStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration) <-chan *pb.ServiceStats {
	statsChan := make(chan *pb.ServiceStats, 50)
	ticker := time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-ctx.Done():
				stdlog.Infof("stop watching service stats, cause it has been stopped")
				close(statsChan)
				ticker.Stop()
				return
			case <-ticker.C:
				stats := k.Stats(serviceType, name) //FIXME use watch api
				if stats == nil {
					continue
				}
				select {
				case statsChan <- stats:
				default:
					stdlog.Warnf("skip to sync %s of stats; because the channel unwritable", name)
				}
			}
		}
	}()
	return statsChan
}

func (k *BootManager) Stats(ServiceType pb.ServiceType, name string) *pb.ServiceStats {
	var stats = new(pb.ServiceStats)
	var err error
	if ServiceType == pb.K8sJob {
		stats, err = k.boot.JobStats(name)
		if err != nil {
			stdlog.WithError(err).Tracef("failed to stats job:%s", name)
		}
		return stats
	}
	stats, err = k.boot.Stats(name)
	if err != nil {
		stdlog.WithError(err).Tracef("failed to stats statefulset:%s", name)
	}
	return stats
}

func (k *BootManager) NodeStats() []*sys.NodeStats {
	stats, err := k.boot.NodeStats()
	if err != nil {
		stdlog.WithError(err).Error("failed to stats nodes")
		return nil
	}
	return stats
}

func (k *BootManager) NodesInfo() ([]*sys.NodeInfo, error) {
	return k.boot.NodesInfo()
}
