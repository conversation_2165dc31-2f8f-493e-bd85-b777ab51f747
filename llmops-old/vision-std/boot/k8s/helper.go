package k8s

import (
	"fmt"
	"strconv"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/sys"
)

type (
	ApiGroup         = string
	LabelKey         = string
	k8SLabelKey      = string // K8s资源的标签Key, 可用于筛选
	k8SAnnotationKey = string // K8s资源注解的Key
	DefaultResource  = string
)

const (
	AppV1  ApiGroup = "apps/v1"
	CoreV1 ApiGroup = "v1"

	// AnnotationConfigMD5 记录K8S资源创建时，所依赖的资源配置的MD5, 用于判断配置是否发生改变
	AnnotationConfigMD5 k8SAnnotationKey = "boot-config-md5"
	// AnnotationRefKey 为相关系统资源的完整Key， 存入Label会导致长度超限
	AnnotationRefKey k8SAnnotationKey = "ref-key"

	// LabelPrefix 为所有boot manager赋予的Label的同一前缀
	LabelPrefix = "edge.sophon.transwarp.io/"

	// 用于指定相关Vision系统资源的信息
	// LabelRefID 为相关系统资源的ID
	LabelRefID k8SLabelKey = LabelPrefix + "ref-id"
	// LabelRefType 为相关系统资源的类型
	LabelRefType k8SLabelKey = LabelPrefix + "ref-type"
	// LabelOwner 为相关系统资源的拥有者
	LabelRefOwner k8SLabelKey = LabelPrefix + "ref-owner"

	// LabelSvcName 创建K8S资源对应BootService的ID
	LabelSvcName k8SLabelKey = LabelPrefix + "boot-svc"

	// LabelManagedBy 标识由当前组件创建的K8S资源，此处应该恒为 vision-boot
	LabelManagedBy k8SLabelKey = LabelPrefix + "managed-by"

	// ManagedByBoot 为标签 LabelManagedBy 的默认值
	ManagedByBoot = "vision-boot"

	Host = "host"
)

var (
	BootResourceLabelSelector = labels.SelectorFromSet(map[string]string{
		LabelManagedBy: ManagedByBoot,
	})

	podPhase2WorkerStates = map[corev1.PodPhase]pb.WorkerState{
		corev1.PodPending:   pb.WorkerPaused,
		corev1.PodRunning:   pb.WorkerRunning,
		corev1.PodSucceeded: pb.WorkerExited,
		corev1.PodFailed:    pb.WorkerExited,
		corev1.PodUnknown:   pb.WorkerExited,
	}
	jobPodPhase2WorkStates = map[corev1.PodPhase]pb.WorkerState{
		corev1.PodPending:   pb.WorkerPaused,
		corev1.PodRunning:   pb.WorkerRunning,
		corev1.PodSucceeded: pb.WorkerSuccess,
		corev1.PodFailed:    pb.WorkerFailure,
		corev1.PodUnknown:   pb.WorkerExited,
	}
)

func genLabelSet(svcName string, refKey stdhub.RscKey) map[string]string {
	return map[string]string{
		LabelRefID:    refKey.RscID(),
		LabelRefOwner: refKey.RscOwner(),
		LabelRefType:  string(refKey.RscType()),

		LabelSvcName:   svcName,
		LabelManagedBy: ManagedByBoot,
	}
}

func genAnnotationSet(svc *pb.BootService) map[string]string {
	return map[string]string{
		AnnotationConfigMD5: toolkit.MD5(svc.Config),
		AnnotationRefKey:    svc.RefKey.KeyString(),
	}
}

func genPodSpec(namespace string, boot *pb.BootService) (corev1.PodSpec, error) {
	// 重启策略配置
	restartPolicy := corev1.RestartPolicyAlways
	if boot.Config.Restart == nil {
		err := stderr.Internal.Error("%s boot service restart policy is null", boot.Name)
		return corev1.PodSpec{}, err
	}
	switch boot.Config.Restart.Policy {
	case pb.RestartConfig_No:
		restartPolicy = corev1.RestartPolicyNever
	case pb.RestartConfig_Always:
		restartPolicy = corev1.RestartPolicyAlways
	case pb.RestartConfig_OnFailure:
		restartPolicy = corev1.RestartPolicyOnFailure
	}

	// 镜像仓库授权配置
	secrets := make([]corev1.LocalObjectReference, len(boot.Config.ImagePullSecrets))
	for i, secret := range boot.Config.ImagePullSecrets {
		secrets[i] = corev1.LocalObjectReference{
			Name: secret,
		}
	}

	// 容器模板
	initContainers, containers, err := genContainers(boot)
	if err != nil {
		return corev1.PodSpec{}, err
	}
	// 调度相关配置
	nodeName := ""
	var affinity *corev1.Affinity   // Pod调度的亲和力配置
	var na *corev1.NodeAffinity     // 限制调度范围在 TargetNodes 中
	var paa *corev1.PodAntiAffinity // 限制各个Pod调度时不要调度到同一个节点上
	// 如果指定了单个调度节点，则将所有相关Pod均调度至该节点
	// NOTICE !!!
	// 声明了 nodeName，Pod 的部署会跳过节点的选择，直接部署到目标节点，然后该 Pod 就会被因为没有对应容忍度被拒绝，并标注其状态为 Evicted。
	// 可能会导致无限重复：创建Pod —> 被驱逐 -> 再次创建... 流程 可参考（https://zhuanlan.zhihu.com/p/141024805）
	//if len(boot.TargetNodes) == 1 {
	//	nodeName = boot.TargetNodes[0]
	//}
	// 如果指定了多个调度的节点，则Pod的调度范围即为指定的TargetNode列表
	// 通过Node affinity机制实现, 使用官方推荐Label（kubernetes.io/hostname）进行筛选
	// 同时，保证每个TargetNode均被调度一个对应的Pod (模型多副本需求)
	// 详参：
	// https://kubernetes.io/docs/reference/labels-annotations-taints/
	// https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector
	if len(boot.TargetNodes) >= 1 {
		na = &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      corev1.LabelHostname,
								Operator: corev1.NodeSelectorOpIn,
								Values:   boot.TargetNodes,
							},
						},
					},
				},
			},
		}
	} else {
		na = boot.Config.NodeAffinity
	}
	if boot.ReplicaMutex {
		paa = &corev1.PodAntiAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
				{
					LabelSelector: &metav1.LabelSelector{MatchLabels: map[string]string{LabelRefID: boot.RefKey.RscID()}},
					Namespaces:    []string{namespace},
					TopologyKey:   corev1.LabelHostname,
				},
			},
		}
	}
	affinity = &corev1.Affinity{
		NodeAffinity:    na,
		PodAntiAffinity: paa,
	}

	// 挂载配置
	volumes := append(boot.Config.ExtVolumes, convertVolumes(boot.Config.Volumes, true)...)

	// pod内部可用的服务配置
	serviceAccountName := "default"
	if boot.ServiceAccountName != "" {
		serviceAccountName = boot.ServiceAccountName
	}
	spec := corev1.PodSpec{
		Volumes:                       volumes,
		InitContainers:                initContainers,
		Containers:                    containers,
		RestartPolicy:                 restartPolicy,
		HostNetwork:                   boot.Config.Network == Host,
		Affinity:                      affinity,
		NodeName:                      nodeName,
		ImagePullSecrets:              secrets,
		TerminationGracePeriodSeconds: new(int64),
		ServiceAccountName:            serviceAccountName,
	}
	// set a short terminating period
	*spec.TerminationGracePeriodSeconds = 3
	return spec, nil
}

func pathToName(path string) string {
	name := strings.ReplaceAll(path, "/", "-")
	// 不能以'-'开头
	return strings.TrimPrefix(name, "-")
}

// convertVolumes 将 docker 形式的 volume 挂载转换为 hostPath 形式的 k8s pod volume
func convertVolumes(volumes map[string]string, host bool) []corev1.Volume {
	k8sVolumes := make([]corev1.Volume, 0)
	for key := range volumes {
		k8sVolume := corev1.Volume{
			Name: pathToName(key),
			VolumeSource: func() corev1.VolumeSource {
				if host {
					return corev1.VolumeSource{
						HostPath: &corev1.HostPathVolumeSource{
							Path: key,
						},
					}
				} else {
					return corev1.VolumeSource{EmptyDir: &corev1.EmptyDirVolumeSource{}}
				}
			}(),
		}
		k8sVolumes = append(k8sVolumes, k8sVolume)
	}
	return k8sVolumes
}

func convertVolumeMounts(volumes map[string]string) []corev1.VolumeMount {
	k8sVolumeMounts := make([]corev1.VolumeMount, 0)
	for key, value := range volumes {
		k8sVolumeMount := corev1.VolumeMount{
			Name:      pathToName(key),
			MountPath: value,
		}
		k8sVolumeMounts = append(k8sVolumeMounts, k8sVolumeMount)
	}
	return k8sVolumeMounts
}

func convertEnv(envs map[string]string) []corev1.EnvVar {
	envVars := make([]corev1.EnvVar, 0)
	for key, value := range envs {
		e := corev1.EnvVar{
			Name:  key,
			Value: value,
		}
		envVars = append(envVars, e)
	}
	return envVars
}

// convertResource 将资源配置转换为K8S形式
// Note: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
//
//	If you specify a limit for a resource, but do not specify any request, and no admission-time mechanism has
//	applied a default request for that resource, then Kubernetes copies the limit you specified and uses it as the
//	requested value for the resource.
func convertResource(rsc *pb.ResourceConfig) (req corev1.ResourceRequirements, err error) {
	if rsc == nil {
		// do not limit resource
		return
	}
	req.Limits = make(map[corev1.ResourceName]resource.Quantity, 0)
	if rsc.Cpu != nil {
		cpu := strconv.FormatFloat(float64(rsc.Cpu.Cpus), 'f', -1, 32)
		cpuQ, e := resource.ParseQuantity(cpu)
		if e != nil {
			err = stderr.InvalidParam.Cause(e, "invalid resource cpu limit '%s'", cpu)
			return
		}
		req.Limits[corev1.ResourceCPU] = cpuQ
	}
	if rsc.Memory != nil {
		memS := strings.ToUpper(rsc.Memory.Limit)
		memQ, e := resource.ParseQuantity(memS)
		if e != nil {
			err = stderr.InvalidParam.Cause(e, "invalid resource mem limit '%s'", memS)
			return
		}
		req.Limits[corev1.ResourceMemory] = memQ
	}
	// TODO support limit gpu nums with key "nvidia.com/gpu"
	return
}

func genContainer(ctnName string, cfg *pb.BootConfig) (ctn corev1.Container, err error) {
	if ctnName == "" {
		err = stderr.InvalidParam.Error("container name cannot be empty")
		return
	}
	// 容器资源配置
	resources, err := convertResource(cfg.Resources)
	if err != nil {
		err = stderr.Wrap(err, "failed to convert resources")
		return
	}

	// 镜像更新配置
	pullPolicy := corev1.PullAlways
	if !cfg.PullOnExist {
		pullPolicy = corev1.PullIfNotPresent
	}

	// 容器安全配置
	securityCtx := &corev1.SecurityContext{
		Privileged: &cfg.Privileged,
	}

	// 容器挂载卷
	mounts := append(cfg.ExtVolumeMounts, convertVolumeMounts(cfg.Volumes)...)
	ports, err := genContainerPort(cfg)
	if err != nil {
		err = stderr.Wrap(err, "failed to convert container port")
		return
	}
	ctn = corev1.Container{
		Name:            ctnName,
		Image:           cfg.Image,
		Command:         cfg.Cmd,
		Args:            cfg.Args,
		Env:             convertEnv(cfg.Envs),
		Ports:           ports,
		Resources:       resources,
		VolumeMounts:    mounts,
		ImagePullPolicy: pullPolicy,
		SecurityContext: securityCtx,
	}
	return
}
func genContainerPort(cfg *pb.BootConfig) ([]corev1.ContainerPort, error) {
	var containerPorts []corev1.ContainerPort
	if len(cfg.ExposedPorts) == 0 {
		return nil, nil
	}
	for _, exposedPort := range cfg.ExposedPorts {
		port, protocol, err := parseExposePort(exposedPort)
		if err != nil {
			return nil, err
		}
		containerPort := &corev1.ContainerPort{
			ContainerPort: int32(port),
			Protocol:      protocol,
		}
		if cfg.Network == Host || cfg.ExposedAsHost {
			containerPort.HostPort = int32(port)
		}
		containerPorts = append(containerPorts, *containerPort)
	}
	return containerPorts, nil
}

func genContainers(boot *pb.BootService) (initCtns []corev1.Container, ctns []corev1.Container, err error) {
	// zero: convert init container default number is 1
	initCtn := corev1.Container{}
	if boot.InitContainer != nil {
		initCtn, err = genContainer(fmt.Sprintf("%s-init", boot.Name), boot.InitContainer)
		if err != nil {
			return []corev1.Container{}, nil, err
		}
		initCtns = append(initCtns, initCtn)
	} else {
		initCtns = nil
	}
	// first: convert master container
	ctn, err := genContainer(boot.Name, boot.Config)
	if err != nil {
		return []corev1.Container{}, nil, err
	}
	ctns = append(ctns, ctn)

	// second: convert sidecar container
	for i, sidecar := range boot.SideCars {
		ctn, err = genContainer(fmt.Sprintf("%s-sidecar%d", boot.Name, i), sidecar)
		if err != nil {
			return
		}
		ctns = append(ctns, ctn)
	}
	return
}

func convertPort(config *pb.BootConfig) ([]corev1.ServicePort, error) {
	portMap := make(map[int]corev1.ServicePort)
	// 处理指定了NodePort的端口映射
	for _, port := range config.Ports {
		np, p, err := parsePortBind(port)
		if err != nil {
			return nil, err
		}
		portMap[p] = corev1.ServicePort{
			Name:       fmt.Sprintf("%d", p),
			Port:       int32(p),
			TargetPort: intstr.FromInt(p),
			NodePort:   int32(np),
		}
	}

	// 处理未指定NodePort的端口映射
	for _, exposedPort := range config.ExposedPorts {
		port, protocol, err := parseExposePort(exposedPort)
		if err != nil {
			return nil, err
		}
		if _, ok := portMap[port]; ok {
			// 暴露端口已被映射至指定的NodePort
			continue
		}

		portMap[port] = corev1.ServicePort{
			Name:       fmt.Sprintf("%d", port),
			Protocol:   protocol,
			Port:       int32(port),
			TargetPort: intstr.FromInt(port),
		}
	}

	servicePorts := make([]corev1.ServicePort, 0, len(portMap))
	for _, port := range portMap {
		servicePorts = append(servicePorts, port)
	}
	return servicePorts, nil
}

func parseExposePort(exposePort string) (port int, protocol corev1.Protocol, err error) {
	defer func() {
		if err != nil {
			port, protocol = 0, ""
			err = stderr.InvalidParam.Cause(err, "failed to parse exposed port '%s'", exposePort)
		}
	}()
	idx := strings.Index(exposePort, "/")
	if idx > 0 {
		protocol = corev1.Protocol(strings.ToUpper(exposePort[idx+1:]))
		switch protocol {
		case corev1.ProtocolUDP, corev1.ProtocolTCP, corev1.ProtocolSCTP:
		default:
			err = stderr.InvalidParam.Error("invalid port protocol '%s'", protocol)
			return
		}
		// trim protocol suffix
		exposePort = exposePort[:idx]
	}
	port, err = strconv.Atoi(exposePort)
	if err != nil {
		return
	}
	if port < 1 || port > 65535 {
		err = stderr.InvalidParam.Error("port %d exceed range [0,65535]", port)
		return
	}
	return
}

func parsePortBind(port string) (nodePort, targetPort int, err error) {
	p := strings.Split(port, ":")
	if len(p) != 2 {
		return 0, 0, fmt.Errorf("invalid port binding: %s", port)
	}

	po, err := strconv.Atoi(p[1])
	if err != nil {
		return 0, 0, stderr.InvalidParam.Cause(err, "invalid target port : %s", p[1])
	}
	nodePo, err := strconv.Atoi(p[0])
	if err != nil {
		return 0, 0, stderr.InvalidParam.Cause(err, "invalid node port : %s", p[0])
	}

	return nodePo, po, nil
}

func podMetric2WorkerStats(pst corev1.PodStatus, metric *v1beta1.PodMetrics) *pb.WorkerStats {
	if metric == nil {
		return nil
	}

	ws := &pb.WorkerStats{
		Name:    metric.Name,
		State:   podPhase2WorkerStates[pst.Phase],
		Message: pst.Message,
		Stats:   &pb.ContainerStats{},
	}
	if len(metric.Containers) == 0 || len(pst.ContainerStatuses) == 0 {
		stdlog.Warnf("no container metric/status found in %s's pod metrics/statues", metric.Name)
		return ws
	}
	if len(metric.Containers) > 1 {
		stdlog.Warnf("more than 1 container metric found in %s's pod metrics, we could only handle the first one", metric.Name)
	}
	ctn := metric.Containers[0]
	for rn, usage := range ctn.Usage {
		switch rn {
		case corev1.ResourceCPU:
			// 此处 100% 对应 k8s cpu/1m 一个物理核
			ws.Stats.CpuPercent = usage.AsApproximateFloat64() * 100
		case corev1.ResourceMemory:
			// 所用内存字节数
			ws.Stats.MemUsage, _ = usage.AsInt64()
		default:
			// not support
			stdlog.Infof("unexpected resource usage of pod: %s", rn)
		}
	}
	return ws
}

func nodeMetric2NodeStats(ns corev1.NodeStatus, nm v1beta1.NodeMetrics) *sys.NodeStats {
	cpct := ns.Capacity
	usage := nm.Usage
	return &sys.NodeStats{
		Timestamp:       nm.Timestamp.Time,
		Name:            nm.Name,
		CpuTotalCore:    int64(cpct.Cpu().AsApproximateFloat64()),
		CpuUsagePercent: usage.Cpu().AsApproximateFloat64() / cpct.Cpu().AsApproximateFloat64(),
		MemTotalByte:    int64(cpct.Memory().AsApproximateFloat64()),
		MemUsagePercent: usage.Memory().AsApproximateFloat64() / cpct.Memory().AsApproximateFloat64(),
		DiskTotalByte:   int64(cpct.StorageEphemeral().AsApproximateFloat64()),
		// FIXME we can not get the disk usage of storage, maybe we should replace it with pvc usage?
		DiskUsagePercent: 0,
		AdditionalInfo: map[string]interface{}{
			"node_status":  ns,
			"node_metrics": nm,
		},
	}
}
