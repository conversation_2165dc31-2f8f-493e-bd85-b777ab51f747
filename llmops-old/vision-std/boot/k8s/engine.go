package k8s

import (
	"context"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	tpdappsv1 "k8s.io/client-go/kubernetes/typed/apps/v1"
	tpdbatchv1 "k8s.io/client-go/kubernetes/typed/batch/v1"
	tpdcorev1 "k8s.io/client-go/kubernetes/typed/core/v1"
	lstapsv1 "k8s.io/client-go/listers/apps/v1"
	lsbatchv1 "k8s.io/client-go/listers/batch/v1"
	lstcorev1 "k8s.io/client-go/listers/core/v1"
	metricsv1 "k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/sys"
)

type BootController struct {
	curtNS string // 当前所在namespace
	//k8s 客户端
	mc *Client

	// k8s API Server 相关接口， 可用于资源的 "增删改"
	dpIf  tpdappsv1.DeploymentInterface
	svcIf tpdcorev1.ServiceInterface
	jobIf tpdbatchv1.JobInterface
	podIf tpdcorev1.PodInterface

	// k8s 资源的 List&Watch接口， 用于实时接收相关资源的变动，并缓存到内存，可加速资源的查询过程，减轻API Server压力。
	dpLister   lstapsv1.DeploymentNamespaceLister
	jobLister  lsbatchv1.JobNamespaceLister
	svcLister  lstcorev1.ServiceNamespaceLister
	podLister  lstcorev1.PodNamespaceLister
	nodeLister lstcorev1.NodeLister
	stopCh     chan struct{}
}

func NewBootController(inCluster bool, cfgPath string) (*BootController, error) {
	curtNS := CurrentNamespaceInCluster()
	mc, err := NewClient(Config{
		Timeout:    time.Second * 5,
		InCluster:  inCluster,
		ConfigPath: cfgPath,
	})
	if err != nil {
		return nil, err
	}
	if err := mc.Start(); err != nil {
		return nil, stderr.Internal.Cause(err, "failed to start k8s client")
	}
	lister := mc.GetLister()
	bc := &BootController{
		curtNS:     curtNS,
		mc:         mc,
		dpIf:       mc.c.AppsV1().Deployments(curtNS),
		svcIf:      mc.c.CoreV1().Services(curtNS),
		jobIf:      mc.c.BatchV1().Jobs(curtNS),
		podIf:      mc.c.CoreV1().Pods(curtNS),
		dpLister:   lister.Deployments(curtNS),
		jobLister:  lister.Jobs(curtNS),
		svcLister:  lister.Services(curtNS),
		podLister:  lister.Pods(curtNS),
		nodeLister: lister.NodeLister,
	}
	stdlog.Info("success sync informer cache for boot-controller")
	return bc, nil
}

// CreateOrUpdate 创建或更新BootService对应的K8S资源对象
// 若为更新操作，则将先删除已存在资源对象，并重新创建
// 若配置未发生改变，则不进行任何操作
func (c *BootController) CreateOrUpdate(ctx context.Context, boot *pb.BootService) (err error) {
	var createDp bool
	createDp = true
	if boot.Type == pb.K8sJob {
		err = c.createOrUpdateJob(ctx, boot)
		if err != nil {
			return stderr.Wrap(err, "faile to process k8s job '%s'", boot.Name)
		}
		createDp = false
	}
	// 检查资源是否已存在 以及 是否需要更新
	createSvc := true
	cfgMD5 := genAnnotationSet(boot)[AnnotationConfigMD5]
	// 检查deployment
	stdlog.Debugf("checking the existence of k8s deployment '%s'", boot.Name)
	dp, err := c.dpLister.Get(boot.Name)
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of deployment '%s'", boot.Name)
	}
	// dp 已存在
	if dp != nil {
		if dp.Annotations[AnnotationConfigMD5] == cfgMD5 {
			// 配置未改变, 跳过创建deployment
			createDp = false
		} else {
			// 配置改变，删除已存在deployment
			if err := c.deleteDeployment(ctx, boot.Name); err != nil {
				return stderr.Wrap(err, "failed to del existing previous deployment")
			}
		}
	}
	// 检查service
	svc, err := c.svcLister.Get(boot.Name)
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of service '%s'", boot.Name)
	}
	// svc 已存在
	if svc != nil {
		if svc.Annotations[AnnotationConfigMD5] == cfgMD5 {
			// 配置未改变, 跳过创建deployment
			stdlog.Infof("k8s service already existed and the config hasn't changed, skip create service")
			createSvc = false
		} else {
			// 配置改变，删除已存在deployment
			stdlog.Infof("delete existing previous service firstly")
			if err := c.deleteService(ctx, boot.Name); err != nil {
				return stderr.Wrap(err, "failed to del existing previous service")
			}
		}
	}

	if !createDp && !createSvc {
		stdlog.Infof("both of service and deployment's config hasn't changed, nothing to do")
		return nil
	}

	defer func() {
		if err != nil {
			// rollback
			errMsg := "failed to create or update boot service, rollback it"
			stdlog.WithError(err).Errorf(errMsg)
			_ = c.Delete(ctx, boot.Name)

			err = stderr.Wrap(err, errMsg)
			return
		}
		stdlog.Infof("success to create or update boot service: %s", boot.Name)
	}()

	// 按需创建k8s service
	if createSvc {
		// 转换为 k8s 资源
		svc, err = c.genServiceByBoot(boot)
		if err != nil {
			return stderr.Wrap(err, "failed to gen k8s service by boot service")
		}

		if svc == nil {
			// 无需提供对外服务
			stdlog.Infof("need not create server for '%s'", boot.Name)
		} else {
			// 需要提供对外服务
			stdlog.Infof("create k8s service for '%s'", boot.Name)
			if err = c.createService(ctx, svc); err != nil {
				return stderr.Internal.Cause(err, "failed to create k8s service")
			}
		}
	}

	// 按需创建k8s deployment
	if createDp {
		// 转换为 k8s 资源
		dp, err = c.genDeploymentByBoot(boot)
		if err != nil {
			return stderr.Wrap(err, "failed to gen k8s deployment by boot service")
		}

		if err = c.createDeployment(ctx, dp); err != nil {
			return stderr.Internal.Cause(err, "failed to create k8s deployment")
		}
	}

	return nil
}
func (c *BootController) createOrUpdateJob(ctx context.Context, boot *pb.BootService) (err error) {
	createJob := true
	cfgMD5 := genAnnotationSet(boot)[AnnotationConfigMD5]
	job, err := c.jobLister.Get(boot.Name)
	if err != nil && !apierrs.IsNotFound(err) {
		// 非预期错误
		return stderr.Wrap(err, "failed to get the existence of deployment '%s'", boot.Name)
	}
	if job != nil {
		if job.Annotations[AnnotationConfigMD5] == cfgMD5 && job.Name == boot.Name {
			// 配置未改变, 跳过创建job
			createJob = false
		} else {
			// 配置改变，删除已存在job
			if err := c.deleteJob(ctx, boot.Name); err != nil {
				return stderr.Wrap(err, "failed to del existing previous deployment")
			}
		}
	}
	if !createJob {
		stdlog.Infof("job's config hasn't changed, nothing to do")
		return nil
	}
	defer func() {
		if err != nil {
			// rollback
			errMsg := "failed to create or update boot service, rollback it"
			stdlog.WithError(err).Errorf(errMsg)
			_ = c.Delete(ctx, boot.Name)
			err = stderr.Wrap(err, errMsg)
			return
		}
		stdlog.Infof("success to create or update boot service: %s", boot.Name)
	}()
	if createJob {
		// 转换为 k8s 资源
		job, err = c.genJobByBoot(boot)
		if err != nil {
			return stderr.Wrap(err, "failed to gen k8s deployment by boot service")
		}

		if err = c.createJob(ctx, job); err != nil {
			return stderr.Internal.Cause(err, "failed to create k8s deployment")
		}
	}
	return nil
}

// Delete 删除BootService对应的K8S资源对象
func (c *BootController) Delete(ctx context.Context, name string) error {
	var errs []error
	if err := c.deleteService(ctx, name); err != nil {
		errs = append(errs, err)
	}
	if err := c.deleteDeployment(ctx, name); err != nil {
		errs = append(errs, err)
	}
	if err := c.deleteJob(ctx, name); err != nil {
		errs = append(errs, err)
	}

	return stderr.JoinErrors(errs...)
}

// Stats 返回给定资源的状态信息，包括运行状态以及资源占用指标
func (c *BootController) Stats(name string) (*pb.ServiceStats, error) {
	dp, err := c.dpLister.Get(name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get k8s deployment of '%s'", name)
	}

	status := &pb.BasicStatusInfo{
		Expect: *dp.Spec.Replicas,
		Ready:  dp.Status.ReadyReplicas,
		Events: nil,
	}
	events, err := c.mc.GetDeploymentEvents(c.curtNS, name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to search events of dp %s", dp.Name)
	}
	for _, event := range events {
		if event.Type == corev1.EventTypeNormal {
			continue
		}
		status.AddEvt(event.Source.String(), event.Reason, event.Message, dp.GetName())
	}

	// list the pods of this dp and then convert pod metrics to worker stats
	pods, err := c.podLister.List(labels.SelectorFromSet(dp.Spec.Selector.MatchLabels))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list pods of dp '%s'", dp.Name)
	}
	wss := make([]*pb.WorkerStats, len(pods))
	for i, pod := range pods {
		// K8S Pod StatusPhase处于Running时不代表其真正运行
		// 其中包含的 Containers 可能处于Waiting/CrashBackOff 的状态
		// 此时PodMetrics无法获取
		if ready, message := IsPodReady(pod); !ready {
			// 获取运行异常的Pod事件
			evts, err := c.mc.GetPodEvents(c.curtNS, pod.Name)
			if err != nil {
				return nil, stderr.Internal.Cause(err, "failed to search events of pod %s", pod.Name)
			}
			for _, evt := range evts {
				if evt.Type == corev1.EventTypeNormal {
					continue
				}
				status.AddEvt(evt.Source.String(), evt.Reason, evt.Message, pod.Name)
			}
			wss[i] = &pb.WorkerStats{
				Name:     pod.Name,
				NodeName: pod.Spec.NodeName,
				State:    pb.WorkerNotReady,
				Message:  message,
				Stats:    nil,
			}
		} else {
			// pod is running
			metric, err := c.mc.GetPodMetrics(c.curtNS, pod.GetName())
			if err != nil {
				stdlog.Infof("failed to get metric of the running pod '%s', "+
					"replace stats with initialized container stats", pod.GetName())
				wss[i] = &pb.WorkerStats{
					Name:     pod.Name,
					NodeName: pod.Spec.NodeName,
					State:    pb.WorkerRunning,
					Message:  pod.Status.Message,
					Stats:    &pb.ContainerStats{},
				}
			} else {
				wss[i] = podMetric2WorkerStats(pod.Status, metric)
				wss[i].NodeName = pod.Spec.NodeName
			}
		}
	}
	return &pb.ServiceStats{
		Name:        name,
		RefKey:      stdhub.NewRscKeyFromString(dp.Annotations[AnnotationRefKey]),
		StatusInfo:  *status,
		WorkerStats: wss,
	}, nil
}

// JobStats 返回给定Job的状态信息，包括运行状态以及资源占用指标
func (c *BootController) JobStats(name string) (*pb.ServiceStats, error) {
	job, err := c.jobLister.Get(name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get k8s deployment of '%s'", name)
	}
	expect := job.Status.Active + job.Status.Succeeded + job.Status.Failed
	ready := job.Status.Active + job.Status.Succeeded
	status := &pb.BasicStatusInfo{
		Expect: expect,
		Ready:  ready,
		Events: nil,
	}
	events, err := c.mc.GetJobEvents(c.curtNS, name)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to search events of job %s", job.Name)
	}
	for _, event := range events {
		if event.Type == corev1.EventTypeNormal {
			continue
		}
		status.AddEvt(event.Source.String(), event.Reason, event.Message, job.GetName())
	}
	// list the pods of this job and then convert pod metrics to worker stats
	pods, err := c.podLister.List(labels.SelectorFromSet(job.Spec.Selector.MatchLabels))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list pods of job '%s'", job.Name)
	}

	wss := make([]*pb.WorkerStats, len(pods))
	for i, pod := range pods {
		if pod.Status.Phase != corev1.PodSucceeded {
			// 获取运行异常的Pod事件
			evts, err := c.mc.GetPodEvents(c.curtNS, pod.Name)
			if err != nil {
				return nil, stderr.Internal.Cause(err, "failed to search events of pod %s", pod.Name)
			}
			for _, evt := range evts {
				if evt.Type == corev1.EventTypeNormal {
					continue
				}
				status.AddEvt(evt.Source.String(), evt.Reason, evt.Message, pod.Name)
			}
		}
		wss[i] = &pb.WorkerStats{
			Name:     pod.Name,
			NodeName: pod.Spec.NodeName,
			State:    jobPodPhase2WorkStates[pod.Status.Phase],
			Message:  pod.Status.Message,
			Stats:    nil,
		}
	}
	return &pb.ServiceStats{
		Name:        name,
		RefKey:      stdhub.NewRscKeyFromString(job.Annotations[AnnotationRefKey]),
		StatusInfo:  *status,
		WorkerStats: wss,
	}, nil
}

func (c *BootController) NodesInfo() ([]*sys.NodeInfo, error) {
	nodes, err := c.mc.ListNodes()
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list cluster nodes")
	}
	nodeMetrics, err := c.mc.ListNodeMetrics()
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to list cluster node metrics")
	}
	name2idx := make(map[string]int)
	for i, nm := range nodeMetrics {
		name2idx[nm.GetName()] = i
	}
	nis := make([]*sys.NodeInfo, len(nodes))
	for i, node := range nodes {
		metric := metricsv1.NodeMetrics{}
		midx, ok := name2idx[node.Name]
		if ok {
			metric = nodeMetrics[midx]
		}

		host := ""
		if len(node.Status.Addresses) > 0 {
			host = node.Status.Addresses[0].Address
		}

		role := sys.Worker
		if _, ok := node.Labels[sys.MasterLabel]; ok {
			role = sys.Master
		}

		status := sys.NodeDown
		for _, condition := range node.Status.Conditions {
			// TODO show more condition...
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
				status = sys.NodeUp
			}
		}
		nis[i] = &sys.NodeInfo{
			Name:     node.GetName(),
			Host:     host,
			Status:   status,
			Os:       node.Status.NodeInfo.OperatingSystem,
			Kernel:   node.Status.NodeInfo.OSImage,
			Arch:     node.Status.NodeInfo.Architecture,
			Resource: nodeMetric2NodeStats(node.Status, metric),
			Role:     role,
		}
	}
	return nis, nil
}

func (c *BootController) NodeStats() ([]*sys.NodeStats, error) {
	nis, err := c.NodesInfo()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get nodes info")
	}
	nss := make([]*sys.NodeStats, len(nis))
	for i, ni := range nis {
		nss[i] = ni.Resource
	}
	return nss, nil
}

func (c *BootController) Sync(expectSvc []*pb.BootService) error {
	// 期望存在的BootService
	expects := make(map[string]*pb.BootService)
	for _, expectSvc := range expectSvc {
		expects[expectSvc.Name] = expectSvc
	}

	var errResult error
	stses, err := c.dpLister.List(BootResourceLabelSelector)
	if err != nil {
		return err
	}
	for _, sts := range stses {
		sn := sts.Labels[LabelSvcName]

		if sn != "" && expects[sn] != nil {
			// 符合预期
			continue
		}

		// 删除所有 svc name 为空 或 非预期boot svc 的 stateful set
		if err = c.mc.DeleteStatefulSet(c.curtNS, sts.GetName()); err != nil {
			errResult = stderr.JoinErrors(err)
			continue
		}
		stdlog.Infof("stateful set '%s' was cleared while sync with expect", sts.GetName())
	}

	svcs, err := c.svcLister.List(BootResourceLabelSelector)
	if err != nil {
		return err
	}
	for _, svc := range svcs {
		sn := svc.Labels[LabelSvcName]
		if sn != "" && expects[sn] != nil {
			// 符合预期
			continue
		}
		// 删除所有 svc name 为空 或 非预期boot svc 的 stateful set
		if err = c.mc.DeleteService(c.curtNS, svc.GetName()); err != nil {
			errResult = stderr.JoinErrors(err)
			continue
		}
		stdlog.Infof("service '%s' was cleared while sync with expect", svc.GetName())
	}

	// TODO 创建预期存在，但是未找到的资源

	return errResult
}

func (c *BootController) genDeploymentByBoot(svc *pb.BootService) (*appsv1.Deployment, error) {
	if len(svc.TargetNodes) > 1 {
		svc.Replica = int32(len(svc.TargetNodes))
	}
	ls := genLabelSet(svc.Name, svc.RefKey)
	as := genAnnotationSet(svc)
	// 增加自定义label
	for k, v := range svc.Config.Labels {
		ls[k] = v
	}
	// 生成Pod配置
	podSpec, err := genPodSpec(c.curtNS, svc)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to gen pod spec")
	}

	return &appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			APIVersion: AppV1,
			Kind:       RscKindDeployment,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        svc.Name,
			Namespace:   c.curtNS,
			Labels:      ls,
			Annotations: as,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &svc.Replica,
			Selector: &metav1.LabelSelector{
				MatchLabels: ls,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      ls,
					Annotations: as,
				},
				Spec: podSpec,
			},
			Strategy: appsv1.DeploymentStrategy{
				Type: appsv1.RecreateDeploymentStrategyType,
			},
		},
	}, nil
}
func (c *BootController) genJobByBoot(svc *pb.BootService) (*batchv1.Job, error) {
	if len(svc.TargetNodes) > 1 {
		svc.Replica = int32(len(svc.TargetNodes))
	}
	as := genAnnotationSet(svc)
	ls := genLabelSet(svc.Name, svc.RefKey)
	// 生成Pod配置
	podSpec, err := genPodSpec(c.curtNS, svc)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to gen pod spec")
	}
	// k8s默认自动产生job selector，手动设置需要打开此选项
	manualSelector := new(bool)
	*manualSelector = true
	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{
			APIVersion: AppV1,
			Kind:       RscKindJob,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        svc.Name,
			Namespace:   c.curtNS,
			Labels:      ls,
			Annotations: as,
		},
		Spec: batchv1.JobSpec{
			ManualSelector: manualSelector,
			Selector: &metav1.LabelSelector{
				MatchLabels: ls,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      ls,
					Annotations: as,
				},
				Spec: podSpec,
			},
		},
	}, nil
}

func (c *BootController) genServiceByBoot(svc *pb.BootService) (*corev1.Service, error) {
	ports, err := convertPort(svc.Config)
	if err != nil {
		return nil, err
	}
	if len(ports) == 0 {
		stdlog.Infof("no port need to expose of boot service '%s', we won't create its k8s service", svc.Name)
		return nil, nil
	}

	lbs := genLabelSet(svc.Name, svc.RefKey)
	as := genAnnotationSet(svc)
	service := &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			APIVersion: CoreV1,
			Kind:       RscKindService,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        svc.Name,
			Namespace:   c.curtNS,
			Labels:      lbs,
			Annotations: as,
		},

		Spec: corev1.ServiceSpec{
			Selector: lbs,
			Ports:    ports,
			Type:     corev1.ServiceTypeNodePort,
		},
	}

	return service, nil
}

func (c *BootController) createService(ctx context.Context, svc *corev1.Service) error {
	if _, err := c.svcIf.Create(ctx, svc, metav1.CreateOptions{}); err != nil {
		return err
	}
	return nil
}

func (c *BootController) updateService(ctx context.Context, svc *corev1.Service) error {
	if _, err := c.svcIf.Update(ctx, svc, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (c *BootController) deleteService(ctx context.Context, name string) error {
	stdlog.Infof("delete the k8s service of %s", name)
	if err := c.svcIf.Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		if apierrs.IsNotFound(err) {
			stdlog.Infof("not found the k8s service of %s", name)
			return nil
		}
		stdlog.WithError(err).Errorf("failed to delete k8s service of %s", name)
		return err
	}
	stdlog.Infof("succeed to delete the k8s service of %s", name)
	return nil
}

func (c *BootController) createDeployment(ctx context.Context, dp *appsv1.Deployment) error {
	stdlog.Infof("create k8s deployment for '%s'", dp.Name)
	if _, err := c.dpIf.Create(ctx, dp, metav1.CreateOptions{}); err != nil {
		return stderr.Wrap(err, "failed to create k8s deployment")
	}
	return nil
}

func (c *BootController) updateDeployment(ctx context.Context, dp *appsv1.Deployment) error {
	if _, err := c.dpIf.Update(ctx, dp, metav1.UpdateOptions{}); err != nil {
		return stderr.Wrap(err, "failed to update k8s deployment")
	}
	return nil
}

func (c *BootController) deleteDeployment(ctx context.Context, name string) error {
	stdlog.Infof("delete the k8s deployment of %s", name)
	if err := c.dpIf.Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		if apierrs.IsNotFound(err) {
			stdlog.Infof("not found the k8s deployment of %s", name)
			return nil
		}
		stdlog.WithError(err).Errorf("failed to delete k8s deployment of %s", name)
		return err
	}
	stdlog.Infof("succeed to delete the k8s deployment of %s", name)
	return nil
}

func (c *BootController) createJob(ctx context.Context, job *batchv1.Job) error {
	stdlog.Infof("create k8s job for '%s'", job.Name)
	if _, err := c.jobIf.Create(ctx, job, metav1.CreateOptions{}); err != nil {
		return stderr.Wrap(err, "failed to create k8s job")
	}
	return nil
}

func (c *BootController) updateJob(ctx context.Context, job *batchv1.Job) error {
	if _, err := c.jobIf.Update(ctx, job, metav1.UpdateOptions{}); err != nil {
		return stderr.Wrap(err, "failed to update k8s job")
	}
	return nil
}

func (c *BootController) deleteJob(ctx context.Context, name string) error {
	stdlog.Infof("delete the k8s job of %s", name)
	job, err := c.jobLister.Get(name)
	if apierrs.IsNotFound(err) {
		stdlog.Infof("not found the k8s deployment of %s", name)
		return nil
	}
	pods, err := c.podLister.List(labels.SelectorFromSet(job.Spec.Selector.MatchLabels))
	if err != nil {
		return stderr.Internal.Cause(err, "failed to list pods of job '%s'", job.Name)
	}
	for _, pod := range pods {
		if err := c.podIf.Delete(ctx, pod.ObjectMeta.Name, metav1.DeleteOptions{}); err != nil {
			if apierrs.IsNotFound(err) {
				stdlog.Infof("not found the pod of job %s", pod.ObjectMeta.Name)
				return nil
			}
			stdlog.WithError(err).Errorf("failed to delete pod of job of %s, please delete it manually", pod.ObjectMeta.Name)
			return err
		}
	}
	if err := c.jobIf.Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		if apierrs.IsNotFound(err) {
			stdlog.Infof("not found the k8s job of %s", name)
			return nil
		}
		stdlog.WithError(err).Errorf("failed to delete k8s job of %s", name)
		return err
	}
	stdlog.Infof("succeed to delete the k8s job of %s", name)
	return nil
}

// status:
//
//	conditions:
//	- lastProbeTime: null
//	  lastTransitionTime: "2022-10-19T05:20:50Z"
//	  status: "True"
//	  type: Initialized
//	- lastProbeTime: null
//	  lastTransitionTime: "2022-10-19T05:54:26Z"
//	  message: 'containers with unready status: [ui]'
//	  reason: ContainersNotReady
//	  status: "False"
//	  type: Ready
//	- lastProbeTime: null
//	  lastTransitionTime: "2022-10-19T05:54:26Z"
//	  message: 'containers with unready status: [ui]'
//	  reason: ContainersNotReady
//	  status: "False"
//	  type: ContainersReady
//	- lastProbeTime: null
//	  lastTransitionTime: "2022-10-19T05:20:50Z"
//	  status: "True"
//	  type: PodScheduled
//	containerStatuses:
//	- containerID: docker://c6ed5eb6a801bb4fb335b854b9e11ccce127cbc1aadd656e35755293d98226ad
//	  image: ***********/aip/vision-kapacitor:master
//	  imageID: docker-pullable://***********/aip/vision-kapacitor@sha256:a6ea1447983d2f055bb42647955c3d096531b1a78426ec69954c405fe6d6ee8f
//	  lastState: {}
//	  name: server
//	  ready: true
//	  restartCount: 0
//	  started: true
//	  state:
//	    running:
//	      startedAt: "2022-10-19T05:20:52Z"
//	- containerID: docker://c00901cf304996847cbb74c9835ddc8c895d40865b413f7980ef2ac73ba2331d
//	  image: ***********/aip/deps/chronograf-1.6.2:master
//	  imageID: docker-pullable://***********/aip/deps/chronograf-1.6.2@sha256:30c7a3a1fae5e45aa28ff5d5f326eca11aa81d39c5c17ffbd309bc7a307785d3
//	  lastState:
//	    terminated:
//	      containerID: docker://c00901cf304996847cbb74c9835ddc8c895d40865b413f7980ef2ac73ba2331d
//	      exitCode: 0
//	      finishedAt: "2022-10-19T05:54:25Z"
//	      reason: Completed
//	      startedAt: "2022-10-19T05:54:15Z"
//	  name: ui
//	  ready: false
//	  restartCount: 11
//	  started: false
//	  state:
//	    waiting:
//	      message: back-off 5m0s restarting failed container=ui pod=autocv-kapacitor-688484ffc8-vhj5b_default(a7428231-73e8-48b7-814e-08b9b0d20bca)
//	      reason: CrashLoopBackOff
//	hostIP: *************
//	phase: Running
//	podIP: ***********
//	podIPs:
//	- ip: ***********
//	qosClass: BestEffort
//	startTime: "2022-10-19T05:20:50Z"
func IsPodReady(p *corev1.Pod) (bool, string) {
	if p == nil {
		return false, "pod is nil"
	}
	for _, cod := range p.Status.Conditions {
		if cod.Type != corev1.PodReady {
			continue
		}
		return cod.Status == corev1.ConditionTrue, cod.Message
	}
	return false, "Ready condition not found"
}
