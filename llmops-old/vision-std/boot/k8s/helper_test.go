package k8s

import (
	v1 "k8s.io/api/core/v1"
	"testing"
)

func TestGet(t *testing.T) {
}

func Test_parseExposePort(t *testing.T) {
	tests := []struct {
		name         string
		exposePort   string
		wantPort     int
		wantProtocol v1.Protocol
		wantErr      bool
	}{
		{
			name:         "normal udp port",
			exposePort:   "123/udp",
			wantPort:     123,
			wantProtocol: v1.ProtocolUDP,
			wantErr:      false,
		}, {
			name:         "normal tcp port",
			exposePort:   "345/tcp",
			wantPort:     345,
			wantProtocol: v1.ProtocolTCP,
			wantErr:      false,
		}, {
			name:         "normal sctp port",
			exposePort:   "789/SCTP",
			wantPort:     789,
			wantProtocol: v1.ProtocolSCTP,
			wantErr:      false,
		}, {
			name:         "invalid port",
			exposePort:   "345555/tcp",
			wantPort:     0,
			wantProtocol: "",
			wantErr:      true,
		}, {
			name:         "invalid protocol",
			exposePort:   "345/vip",
			wantPort:     0,
			wantProtocol: "",
			wantErr:      true,
		}, {
			name:         "port without protocol",
			exposePort:   "345",
			wantPort:     345,
			wantProtocol: "",
			wantErr:      false,
		}, {
			name:         "exceed min port",
			exposePort:   "0",
			wantPort:     0,
			wantProtocol: "",
			wantErr:      true,
		}, {
			name:         "exceed max port",
			exposePort:   "65536/tcp",
			wantPort:     0,
			wantProtocol: "",
			wantErr:      true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotPort, gotProtocol, err := parseExposePort(tt.exposePort)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseExposePort() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotPort != tt.wantPort {
				t.Errorf("parseExposePort() gotPort = %v, want %v", gotPort, tt.wantPort)
			}
			if gotProtocol != tt.wantProtocol {
				t.Errorf("parseExposePort() gotProtocol = %v, want %v", gotProtocol, tt.wantProtocol)
			}
		})
	}
}
