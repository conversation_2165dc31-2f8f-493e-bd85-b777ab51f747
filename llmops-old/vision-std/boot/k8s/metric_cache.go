package k8s

import (
	"k8s.io/metrics/pkg/apis/metrics/v1beta1"
	"sync"
	"time"
)

type metricCache struct {
	sync.RWMutex
	ns           string // 缓存指标所属pod所在的namespace
	syncInterval time.Duration

	nodes      []v1beta1.NodeMetrics
	name2Nodes map[string]v1beta1.NodeMetrics // notice! do not read/write this map directly

	name2Pos map[string]v1beta1.PodMetrics // notice! do not read/write this map directly
}

func (c *metricCache) setPoMetrics(pms []v1beta1.PodMetrics) {
	c.Lock()
	defer c.Unlock()

	c.name2Pos = make(map[string]v1beta1.PodMetrics, len(pms))
	for _, item := range pms {
		c.name2Pos[item.Name] = item
	}
}

func (c *metricCache) getPoMetrics(name string) (*v1beta1.PodMetrics, bool) {
	c.RLock()
	defer c.RUnlock()

	if c.name2Pos == nil {
		return nil, false
	}

	pm, ok := c.name2Pos[name]
	if !ok {
		return nil, false
	}
	return pm.DeepCopy(), true
}

func (c *metricCache) clearPoMetrics() {
	c.Lock()
	defer c.Unlock()

	c.name2Pos = nil
}

func (c *metricCache) listNodeMetrics() []v1beta1.NodeMetrics {
	c.RLock()
	defer c.RUnlock()

	if c.nodes == nil {
		return nil
	}

	ret := make([]v1beta1.NodeMetrics, len(c.nodes))
	for i, node := range c.nodes {
		ret[i] = *node.DeepCopy()
	}
	return ret
}

func (c *metricCache) setNodeMetrics(nms []v1beta1.NodeMetrics) {
	c.Lock()
	defer c.Unlock()

	c.nodes = nms
	c.name2Nodes = make(map[string]v1beta1.NodeMetrics, len(nms))
	for _, nm := range nms {
		c.name2Nodes[nm.Name] = nm
	}
}

func (c *metricCache) clearNodeMetrics() {
	c.Lock()
	defer c.Unlock()

	c.nodes = nil
	c.name2Nodes = nil
}
