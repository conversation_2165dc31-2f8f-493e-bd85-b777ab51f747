package k8s

import (
	"encoding/json"
	"fmt"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/mergepatch"
	"k8s.io/apimachinery/pkg/util/strategicpatch"
	"k8s.io/client-go/kubernetes/scheme"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

var (
	runtimeScheme = runtime.NewScheme()
)

func init() {
	_ = corev1.AddToScheme(runtimeScheme)
	_ = appsv1.AddToScheme(runtimeScheme)
}

func GeneratePatch(origin interface{}, edited interface{}) ([]byte, error) {
	originUnstruct := &unstructured.Unstructured{}
	originalJS, err := json.<PERSON>(origin)
	if err != nil {
		return nil, err
	}
	if err := runtimeScheme.Convert(origin, originUnstruct, nil); err != nil {
		return nil, err
	}
	editedJS, err := json.Marshal(edited)
	if err != nil {
		return nil, err
	}
	preconditions := []mergepatch.PreconditionFunc{
		mergepatch.RequireKeyUnchanged("apiVersion"),
		mergepatch.RequireKeyUnchanged("kind"),
		//对于部分资源，比如deployment, 它的spec中有metadata字段，所以不能在此做判断
		//mergepatch.RequireKeyUnchanged("metadata"),
		mergepatch.RequireMetadataKeyUnchanged("name"),
		mergepatch.RequireMetadataKeyUnchanged("managedFields"),
		mergepatch.RequireKeyUnchanged("status"),
	}

	//patch,err:=jsonmergepatch.CreateThreeWayJSONMergePatch(nil, editedJS, originalJS, preconditions...)

	// Create the versioned struct from the type defined in the mapping
	// (which is the API version we'll be submitting the patch to)
	versionedObject, err := scheme.Scheme.New(originUnstruct.GroupVersionKind())
	patch, err := strategicpatch.CreateTwoWayMergePatch(originalJS, editedJS, versionedObject, preconditions...)
	if err != nil {
		if mergepatch.IsPreconditionFailed(err) {
			return nil, fmt.Errorf("%s", "At least one of apiVersion, kind, name, managedFields and status was changed")
		}
		return nil, err
	}
	return patch, nil
}

func IsObjectNoDiff(err error) bool {
	switch err.(type) {
	case *stderr.StdErr:
		return err.(*stderr.StdErr).Code == stderr.ObjectNoDifference.Code
	default:
		return false
	}
}
