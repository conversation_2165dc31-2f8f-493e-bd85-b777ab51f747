package docker

import (
	"context"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/client"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/backoff"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type WorkerLogField = string

const (
	containerId   WorkerLogField = "cid"
	containerName WorkerLogField = "worker"

	DefaultRetryInterval = time.Second * 30
)

// containerWorker will be used to control docker container to run and retry
// It is a docker container implementation of api.Worker interface.
type containerWorker struct {
	info       *pb.BootService
	cli        *client.Client
	cid        string // container id
	name       string // container name and worker name e.g. FUNC.XXXX-1
	cfg        *pb.ContainerConfigs
	log        *logrus.Entry
	tomb       utils.Tomb
	monitor    *WorkerStatsMonitor
	supervised bool
	lastStats  types.StatsJSON
}

// NewContainerWorker create a new docker container
func NewContainerWorker(name string, cfg *pb.ContainerConfigs, cli *client.Client, svc *pb.BootService) *containerWorker {
	return &containerWorker{
		info:       svc,
		cli:        cli,
		cid:        "",
		name:       name,
		cfg:        cfg,
		log:        stdlog.WithFields(containerName, name),
		tomb:       utils.Tomb{},
		monitor:    NewWorkerStatsMonitor(svc, make(chan *pb.WorkerStats, 100)),
		supervised: false,
	}
}

// Id will return the first 12 chars of the container id of this container.
func (w *containerWorker) Id() string {
	id := w.cid
	if len(id) > 12 {
		id = id[:12]
	}
	return id
}

func (w *containerWorker) Info() *pb.BootService {
	return w.info
}

// Name will return the name of this container worker
func (w *containerWorker) Name() string {
	return w.name
}

func (w *containerWorker) Policy() *pb.RestartConfig {
	return w.info.Config.Restart
}

// starts and supervises the worker
func (w *containerWorker) Start() error {
	// create container if not exist
	if !w.existContainer() {
		if err := w.createContainer(); err != nil {
			return err
		}
	}

	// start container
	if err := w.startContainer(); err != nil {
		return err
	}

	// monitor container
	if err := w.MonitorStats(); err != nil {
		return err
	}

	// do not supervise worker repeatedly
	if w.supervised {
		return nil
	}

	// supervise this worker
	return w.Supervise()
}

func (w *containerWorker) Supervised() bool {
	return w.supervised
}

// stops container with a grace time
func (w *containerWorker) Stop() error {
	w.log.Info("Worker is stopping")
	// cancel supervise this worker
	if w.supervised {
		w.log.Infof("Stopping supervise goroutine")
		w.tomb.Kill(nil)
		// waiting supervise exit
		if reason := w.tomb.Wait(); reason != nil {
			stdlog.WithError(reason).Errorf("Unexpected dead reason")
		}
		w.log.Infof("Supervise goroutine stopped")
	}

	// no container need to stop and remove
	if !w.existContainer() {
		w.log.Info("Worker already stopped")
		return nil
	}

	// stop container first
	if err := w.stopContainer(); err != nil {
		w.log.Warnf("Stop container failed before removing it")
	}

	// force remove the container no whether it has been stopped successfully
	if err := w.removeContainer(); err != nil {
		w.log.Error("Failed to stop worker")
		return stderr.Wrap(err, "failed to remove container")
	}

	w.log.Info("Worker stopped")
	return nil
}

// Supervise will watch the state of this worker and recover the worker
// from unexpect exit by its restart config until it has been stopped.
func (w *containerWorker) Supervise() error {
	if w.supervised {
		w.log.Infof("Worker is already supervised")
		return nil
	}
	return w.tomb.Go(w.supervising)
}

// Wait returns the channel that can be used to wait until next time
// container's state changes to a non-running state.
func (w *containerWorker) Wait() <-chan error {
	c := make(chan error, 1)
	go func() {
		defer close(c)
		statusChan, errChan := w.cli.ContainerWait(context.Background(), w.Name(), container.WaitConditionNextExit)
		select {
		case err := <-errChan:
			w.log.WithError(err).Warnln("Failed to wait container")
			c <- err
		case status := <-statusChan:
			w.log.Infof("Container exited with status: %+v", status)
			c <- stderr.Internal.Error("Container exited with status: %+v", status)
		}
	}()
	return c
}

func (w *containerWorker) WatchStats() <-chan *pb.WorkerStats {
	return w.monitor.ch
}

func (w *containerWorker) Stats() (*pb.WorkerStats, error) {
	if w.monitor != nil && !w.monitor.closed && w.monitor.crtStats != nil {
		return w.monitor.crtStats, nil
	}
	ctx := context.Background()
	inspect, err := w.cli.ContainerInspect(ctx, w.Name())
	if err != nil {
		w.log.WithError(err).Warnf("failed to inspect container (%s)", w.Id())
		return nil, err
	}

	ctnStats, err := w.cli.ContainerStats(ctx, w.Name(), false)
	if err != nil {
		w.log.WithError(err).Warnf("failed to stats container (%s)", w.Id())
		return nil, err
	}
	jsonStats, err := ioutil.ReadAll(ctnStats.Body)
	_ = ctnStats.Body.Close()
	if err != nil {
		w.log.WithError(err).Warnf("failed to read stats response of container (%s)", w.Id())
		return nil, err
	}
	stats, err := w.buildContainerStats(jsonStats)
	if err != nil {
		w.log.WithError(err).Warnf("failed to inspect container (%s)", w.Id())
		return nil, err
	}
	return &pb.WorkerStats{
		Name:  w.Id(),
		State: inspect.State.Status,
		Stats: stats,
	}, nil
}

// briefRef will return the name and id of corresponding container.
func (w *containerWorker) briefRef() string {
	return fmt.Sprintf("%s(%s)", w.Name(), w.Id())
}

func (w *containerWorker) startContainer() error {
	err := w.cli.ContainerStart(context.Background(), w.Name(), types.ContainerStartOptions{})
	if err != nil {
		w.log.WithError(err).Warnln("Failed to start container")
		return stderr.Internal.Cause(err, "failed to start container")
	}
	w.log.Info("Container started")
	return nil
}

func (w *containerWorker) createContainer() error {
	ctn, err := w.cli.ContainerCreate(context.Background(), w.cfg.Config, w.cfg.HostConfig, w.cfg.NetworkConfig, w.cfg.Platform, w.name)
	if err != nil {
		w.log.WithError(err).Warnln("Failed to create container")
		return stderr.Internal.Cause(err, "error while creating container of worker %q", w.Name())
	}
	w.cid = ctn.ID
	w.log = w.log.WithField(containerId, w.Id())
	w.log.Info("Container created")
	return nil
}

func (w *containerWorker) existContainer() bool {
	containers, err := w.cli.ContainerList(context.Background(), types.ContainerListOptions{
		Filters: filters.NewArgs(filters.KeyValuePair{
			Key:   "name",
			Value: w.Name(),
		}),
		All: true,
	})
	if err != nil {
		return false
	}
	return len(containers) > 0
}

func (w *containerWorker) stopContainer() error {
	err := w.cli.ContainerStop(context.Background(), w.Name(), nil)
	if err != nil {
		w.log.WithError(err).Error("Failed to stop container")
		return err
	} else {
		w.log.Info("Container stopped")
	}
	return nil
}

func (w *containerWorker) removeContainer() error {
	err := w.cli.ContainerRemove(context.Background(), w.Name(), types.ContainerRemoveOptions{Force: true})
	if err != nil {
		w.log.WithError(err).Warnf("Failed to remove container")
		return err
	} else {
		w.log.Info("Container removed")
	}
	return nil
}

// supervising supervise a worker
func (w *containerWorker) supervising() error {
	w.log.Info("Supervise the worker")
	w.supervised = true

	defer func() {
		w.log.Info("Stopped supervise the worker")
		w.supervised = false
	}()

	// check restart policy
	p := w.Policy()
	if _, ok := restartPolicy[p.Policy]; !ok {
		w.log.Errorf("Restart policy (%s) invalid", p.Policy)
		return stderr.InvalidParam.Error("Restart policy invalid")
	}
	if p.Policy == pb.RestartConfig_No {
		w.log.Infof("Worker's restart policy is %s, need not supervised", p.Policy)
		return nil
	}
	backOffMin, err1 := time.ParseDuration(p.Backoff.Min)
	backOffMax, err2 := time.ParseDuration(p.Backoff.Max)
	if err1 != nil || err2 != nil {
		w.log.Errorf("Restart back off interval (min: %s, max: %s) invalid", p.Backoff.Min, p.Backoff.Max)
		return stderr.InvalidParam.Cause(stderr.JoinErrors(err1, err2), "Restart back off invalid")
	}
	b := &backoff.Backoff{
		Min:    backOffMin,
		Max:    backOffMax,
		Factor: p.Backoff.Factor,
	}

	var retryCount int32 = 0
	lastRetry := time.Now()
	resetInterval := DefaultRetryInterval
	for w.supervised {
		w.log.Infof("Keep supervising")
		select {
		case <-w.tomb.Dying(): // worker has been killed, stop supervising
			w.log.Info("Supervise will exit because worker is stopped")
			return nil
		case <-w.Wait(): // container is not running
			w.log.Info("Worker's container exited, restart it")
			goto RESTART
		}

	RESTART:
		if time.Now().After(lastRetry.Add(resetInterval)) {
			// 连续运行超过指定间隔,则重置重启计数
			retryCount = 0
		} else {
			retryCount++
		}
		w.log.Infof("Wait to restart worker %d times", retryCount)
		if p.MaxRetry > 0 && retryCount > p.MaxRetry {
			w.log.Errorf("retry count exceed limit (%d), stop supervising it", retryCount)
			return nil
		}

		select {
		case <-time.After(b.Duration()):
		case <-w.tomb.Dying(): // in case worker has been killed before next restart
			w.log.Info("Supervise will exit because worker is stopped")
			return nil
		}

		lastRetry = time.Now()
		if err := w.Start(); err != nil {
			w.log.Errorf("failed to restart module, keep to restart")
			goto RESTART
		}
		w.log.Info("Worker restart success")
	}
	return nil
}
