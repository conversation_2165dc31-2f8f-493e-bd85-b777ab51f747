package docker

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/client"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"
)

type NetworkId = string
type NetworkName = string

const (
	None       NetworkName = "none"
	Host       NetworkName = "host"
	VisionNode NetworkName = "vision-node" // default
)

var (
	engine *Engine

	// supportNetworks defined all modes of docker network supported by boot.
	// NetworkId will be filled in method initNetwork() cause it is up to specific host.
	supportNetworks = map[NetworkName]NetworkId{
		Host:       "",
		None:       "",
		VisionNode: "",
	}

	// restartPolicy used to map pb.RestartConfig_Policy to Docker restart policy string
	restartPolicy = map[pb.RestartConfig_Policy]string{
		pb.RestartConfig_No:        "no",
		pb.RestartConfig_Always:    "always",
		pb.RestartConfig_OnFailure: "on-failure",
	}

	// restartPolicyReverse used to map Docker restart policy string to pb.RestartConfig_Policy
	restartPolicyReverse = map[string]pb.RestartConfig_Policy{
		"no":         pb.RestartConfig_No,
		"always":     pb.RestartConfig_Always,
		"on-failure": pb.RestartConfig_OnFailure,
	}
)

// Engine docker engine
type Engine struct {
	client *client.Client
}

// NewDockerEngine create a new docker engine
func InitDockerEngine(apiVersion string) error {
	cli, err := client.NewClientWithOpts(client.WithVersion(apiVersion))
	if err != nil {
		return stderr.Wrap(err, "failed to new docker client")
	}
	eng := &Engine{
		client: cli,
	}
	err = eng.initNetwork()
	if err != nil {
		cli.Close()
		return err
	}
	engine = eng
	return nil
}

func (e *Engine) RemoveWorker(ctn string) error {
	if ctn == "" {
		stdlog.Warnf("container has not specified while removing it")
		return nil
	}
	err := e.client.ContainerRemove(context.Background(), ctn, types.ContainerRemoveOptions{Force: true})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to remove container %q", ctn)
		return stderr.Wrap(err, "error while removing container %q", ctn)
	}
	return nil
}

func (e *Engine) RemoveImage(image string) error {
	stdlog.Infof("remove image %q", image)
	images, err := e.client.ImageList(context.Background(), e.buildImageFilter(image))
	if err != nil {
		return stderr.Internal.Cause(err, "error while removing image %q, list images failed", image)
	}
	if len(images) < 1 {
		stdlog.Warnf("image %q has already been removed", image)
		return nil
	}
	_, err = e.client.ImageRemove(context.Background(), images[0].ID, types.ImageRemoveOptions{
		Force:         true,
		PruneChildren: false,
	})
	if err != nil {
		return stderr.Internal.Cause(err, "error while removing image %q", image)
	}
	return nil
}

func (e *Engine) PrepareImage(image, basicToken string) (dockerimage.Progress, error) {
	// The context of method ImagePull will be used to connect to registry and download image layers.
	// Therefore, it could not use TimeoutContext to avoid that connecting registry take too much time.
	ctx, cancel := context.WithCancel(context.Background())
	user, passwd, err := dockerimage.DecodeBasicAuth(basicToken)
	if err != nil {
		cancel()
		return nil, err
	}
	authConfig := types.AuthConfig{
		Username: user,
		Password: passwd,
	}
	encodedJSON, _ := json.Marshal(authConfig)
	authStr := base64.URLEncoding.EncodeToString(encodedJSON)
	out, err := e.client.ImagePull(ctx, image, types.ImagePullOptions{RegistryAuth: authStr})
	if err != nil {
		cancel()
		return nil, stderr.Internal.Cause(err, "failed to pull image '%s'", image)
	}

	return dockerimage.NewPullProgress(out, cancel), nil
}

// Create creates a new docker container
func (e *Engine) CreateWorker(name string, info *pb.BootService) (*containerWorker, error) {
	cfg, err := info.DockerCtnConfigs()
	if err != nil {
		return nil, err
	}
	return NewContainerWorker(name, cfg, e.client, info), nil
}

//// ListWorkers will return all containers that started as BootService worker.
//func (e *Engine) ListWorkers(all bool) (map[string]boot.WorkerKey, error) {
//	workers := make(map[string]boot.WorkerKey)
//
//	// list all containers started by boot
//	cs, err := e.client.ContainerList(context.Background(), types.ContainerListOptions{
//		All:     all,
//		Filters: filters.NewArgs(filters.Arg("label", ServiceName)),
//	})
//	if err != nil {
//		stdlog.WithError(err).Error("error while listing containers started by boot")
//		return nil, err
//	}
//
//	// convert containers to workers
//	for _, c := range cs {
//		name := ""
//		if len(c.Names) != 0 {
//			name = c.Names[0]
//			if strings.HasPrefix(name, "/") {
//				name = name[1:]
//			}
//		}
//		workers[c.Id] = boot.WorkerKey{
//			WorkerName:  name,
//			ServiceName: c.Labels[ServiceName],
//			NameSpace:   c.Labels[NameSpace],
//			MD5Checksum: c.Labels[MD5Checksum],
//		}
//	}
//	return workers, nil
//}

func (e *Engine) ExistImage(image string) (bool, error) {
	images, err := e.client.ImageList(context.Background(), e.buildImageFilter(image))
	if err != nil {
		return false, err
	}
	return len(images) >= 1, nil
}

func (e *Engine) initNetwork() error {
	ctx := context.Background()
	// list networks
	nws, err := e.client.NetworkList(ctx, types.NetworkListOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "error while listing docker networks")
	}

	// cache network ids
	for _, nw := range nws {
		if _, ok := supportNetworks[nw.Name]; !ok {
			continue
		}
		supportNetworks[nw.Name] = nw.ID
	}

	// create default network if it not exist
	if supportNetworks[VisionNode] == "" {
		nw, err := e.client.NetworkCreate(ctx, VisionNode, types.NetworkCreate{Driver: "bridge"})
		if err != nil {
			return stderr.Internal.Cause(err, "error while creating docker network %q", VisionNode)
		}
		if nw.Warning != "" {
			stdlog.Warn(nw.Warning)
		}
		supportNetworks[VisionNode] = nw.ID
		stdlog.Infof("ContainerNetwork (%s) created", nw.ID[:12])
	}
	return nil
}

func (e *Engine) buildImageFilter(image string) types.ImageListOptions {
	return types.ImageListOptions{Filters: filters.NewArgs(filters.Arg("reference", image))}
}
