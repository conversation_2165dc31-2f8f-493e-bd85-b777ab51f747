package docker

import (
	"context"
	cmap "github.com/orcaman/concurrent-map"
	"github.com/sirupsen/logrus"
	"os"
	"runtime"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/boot/conf"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/sys"
)

type BootManager struct {
	services cmap.ConcurrentMap
	log      *logrus.Entry
	cfg      conf.ManagerConfig
}

func NewBootManager(cfg conf.ManagerConfig) (*BootManager, error) {
	if err := InitDockerEngine(cfg.DockerApiVersion); err != nil {
		logrus.Panicf("failed to init the docker engine client: %s", err.<PERSON><PERSON>r())
		return nil, err
	}
	return &BootManager{
		services: cmap.New(),
		log:      stdlog.WithFields("component", "manager"),
		cfg:      cfg,
	}, nil
}

func (m *BootManager) SyncServices(expects []*pb.BootService) error {
	m.log.Infof("Sync services")

	// list current services and containers
	toDelete := m.services.Items()

	// compute md5 of service and find out those unanticipated services
	expectsMD5 := map[string]*pb.BootService{}
	for _, expect := range expects {
		expectsMD5[toolkit.MD5(expect.Config)] = expect
		delete(toDelete, expect.Name)
	}

	// wait group will used to start/stop service asynchronously
	wg := sync.WaitGroup{}

	// destroy needless services
	wg.Add(len(toDelete))
	for _, svc := range toDelete {
		go func(s *pb.BootService) {
			if err := m.DestroyService(s.Name); err != nil {
				m.log.WithError(err).Errorf("Failed to destroy service %q", s.Name)
			}
			wg.Done()
		}(svc.(*Deployment).Info())
	}
	wg.Wait() // wait destroy needless services done

	wg.Add(len(expects))
	for _, expect := range expects {
		go func(svc *pb.BootService) {
			// reset exclusive services
			if err := m.RunService(svc); err != nil {
				m.log.WithError(err).Errorf("Failed to run service %q", svc.Name)
			}
			wg.Done()
		}(expect)
	}
	wg.Wait() // wait run services done
	return nil
}

// RunService is the entry method to start a BootService which was received from rpc call or hub event.
func (m *BootManager) RunService(svc *pb.BootService) error {
	stdlog.Infof("====== Run BootService '%s' ======", svc.Name)

	// validate service
	stdlog.Infof("Validate BootService %q", svc.Name)
	if err := svc.Validate(); err != nil {
		stdlog.WithError(err).Errorf("Invalid pb service")
		return err
	}

	var err error
	token, err := dockerimage.GetBasicTokenByImage(svc.Config.Image, m.cfg.RegistryTokens)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to get token of images:%s", svc.Config.Image)
		return err
	}
	svc.Config.ImageRepoBasicToken = token

	// revert service if run it failed
	defer func() {
		if err != nil {
			stdlog.WithError(err).Errorf("Failed to run BootService '%s', destroying it now", svc.Name)
			if de := m.DestroyService(svc.Name); de != nil {
				stdlog.WithError(err).Errorf("Failed to destroy BootService '%s' after running it failed", svc.Name)
			}
		}
	}()

	// set default value for those empty config
	stdlog.Infof("Apply default config to BootService %q", svc.Name)
	svc.ApplyDefault()

	// start a new service
	s, ok := m.GetService(svc.Name)
	if !ok {
		s, err := NewDeployment(svc)
		if err != nil {
			return stderr.Wrap(err, "Failed to new the service '%s'", svc.Name)
		}
		m.services.Set(svc.Name, s)
		if err := s.Start(); err != nil {
			return stderr.Wrap(err, "Failed to start the service '%s'", svc.Name)
		}
		return nil
	}

	// update an existing service
	if err = s.Adapt(svc); err != nil {
		return stderr.Wrap(err, "Failed to start the service '%s'", svc.Name)
	}
	return nil
}

// DestroyService will stop the service by giving name, then remove the service
// from the memory and db(hub)
func (m *BootManager) DestroyService(name string) error {
	m.log.Infof("====== Destroy BootService '%s' ======", name)
	s, ok := m.GetService(name)
	// service not found in memory
	if !ok {
		m.log.Warnf("Deployment '%s' has been destroyed already", name)
	} else {
		// stop service and remove it from memory
		if err := s.Stop(); err != nil {
			return err
		}
		m.services.Remove(name)
	}
	return nil
}

func (m *BootManager) GetService(name string) (*Deployment, bool) {
	if s, ok := m.services.Get(name); ok {
		return s.(*Deployment), ok
	}
	return nil, false
}

func (m *BootManager) Stats(serviceType pb.ServiceType, name string) *pb.ServiceStats {
	d, exist := m.GetService(name)
	if !exist {
		return nil
	}
	return d.Stats()
}

func (m *BootManager) HandleStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration, handler func(stats *pb.ServiceStats)) {
	for stats := range m.WatchStats(ctx, serviceType, name, interval) {
		handler(stats)
	}
}

func (m *BootManager) WatchStats(ctx context.Context, serviceType pb.ServiceType, name string, interval time.Duration) <-chan *pb.ServiceStats {
	ch := make(chan *pb.ServiceStats, 10)
	ticker := time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-ctx.Done():
				stdlog.Infof("stop watching service stats, cause it has been stopped")
				close(ch)
				ticker.Stop()
				return
			case <-ticker.C:
				s := m.Stats(serviceType, name)
				if s != nil {
					ch <- s
				}
			}
		}
	}()
	return ch
}

func (m *BootManager) NodeStats() []*sys.NodeStats {
	return []*sys.NodeStats{sys.GetNodeStat()}
}

func (m *BootManager) NodesInfo() ([]*sys.NodeInfo, error) {
	hostname, err := os.Hostname()
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes info: host name")
	}
	host, err := sys.GetHostIpAddress()
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to get nodes info: host ip addr")
	}
	edge := &sys.NodeInfo{
		Name:     hostname,
		Host:     host,
		Status:   sys.EdgeClusterUp,
		Arch:     runtime.GOARCH,
		Resource: sys.GetNodeStat(),
		Role:     sys.Master,
	}
	edge.Os, err = sys.GetOSVersion()
	if err != nil {
		return nil, stderr.Internal.Error(err.Error())
	}
	edge.Kernel, err = sys.UnameKernel()
	if err != nil {
		return nil, stderr.Internal.Error(err.Error())
	}
	// edge.Gpus, _ = sys.GetGpus() // 需要将 CUDA 挂载到容器中

	return []*sys.NodeInfo{edge}, nil
}
