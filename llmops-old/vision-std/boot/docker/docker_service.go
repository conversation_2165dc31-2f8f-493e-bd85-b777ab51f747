package docker

import (
	"fmt"
	"github.com/orcaman/concurrent-map"
	"github.com/sirupsen/logrus"
	"strings"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdstatus"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

var (
	BootConflictErr    = stderr.NewCode(stderr.Boot, "操作与服务当前状态冲突", "Operation conflicts with current service status")
	BootPrepareErr     = stderr.NewCode(stderr.Boot, "准备服务镜像失败", "Prepare service image failure")
	BootScaleErr       = stderr.NewCode(stderr.Boot, "调整服务副本数量失败", "Failed to adjust the number of service workers")
	BootConfigErr      = stderr.NewCode(stderr.Boot, "服务配置错误", "Invalid service config")
	BootWorkerExistErr = stderr.NewCode(stderr.Boot, "服务副本已存在", "Deployment worker already exists")
	BootWorkerStartErr = stderr.NewCode(stderr.Boot, "服务副本启动失败", "Failed to start service worker")
)

// Deployment 概念类似 K8S 的Deployment
// 每个 Deployment 可能包含多个Worker, 每个Worker的配置与作用一致;
// Worker的数量取决于配置中的Replica字段.
type Deployment struct {
	info    *pb.BootService
	workers cmap.ConcurrentMap // workers modules from function module
	log     *logrus.Entry
	m       sync.RWMutex
	stopped chan struct{} // whether this service stopped
	state   stdstatus.StdState
	msg     string
}

const StatusUpdateInterval = time.Second * 3

func NewDeployment(svc *pb.BootService) (*Deployment, error) {
	s := &Deployment{
		info:    svc,
		workers: cmap.New(),
		log:     stdlog.WithFields("service", svc.Name),
	}
	return s, nil
}

// Name returns the key of this service.
// Start will running service as expected.
// Stop stops all of workers of this service.
// Info returns the base info(BootService) of this service.
// Adapt updates Deployment.info to specified new BootService.
// Stats return the current stats of this service and its workers.
// WatchStats 依据给定的间隔输出BootService的运行统计信息

// Adapt will stop the old exist service and its workers then it will start the new service.
// It will do nothing while there is no diffs between old and new service.
func (s *Deployment) Adapt(svc *pb.BootService) error {
	s.m.Lock()
	defer s.m.Unlock()
	old := s.info
	s.info = svc

	// check is config changed
	if toolkit.MD5(old.Config) != toolkit.MD5(svc.Config) {
		s.log.Infof("Boot service changed, '%+v'", utils.Diff(old.Config, svc.Config))
		if err := s.stop(); err != nil {
			return err
		}
		if err := s.start(); err != nil {
			return err
		}
		return nil
	}

	s.info = svc
	return s.scale()
}

func (s *Deployment) Workers() []string {
	s.m.RLock()
	defer s.m.RUnlock()
	return s.workers.Keys()
}

func (s *Deployment) Info() *pb.BootService {
	return s.info
}

func (s *Deployment) Name() string {
	return s.info.Name
}

func (s *Deployment) Stats() *pb.ServiceStats {
	s.m.RLock()
	defer s.m.RUnlock()
	r := &pb.ServiceStats{
		Name: s.info.Name,
		RefKey: &stdhub.RscKey{
			ID:    s.info.RefKey.ID,
			Owner: s.info.RefKey.Owner,
			Type:  s.info.RefKey.Type,
		},
		StatusInfo: pb.BasicStatusInfo{
			Expect: s.info.Replica,
		},
		WorkerStats: make([]*pb.WorkerStats, 0),
	}
	for _, item := range s.workers.Items() {
		worker := item.(*containerWorker)
		status, err := worker.Stats()
		if err != nil {
			s.log.Errorf("Failed to stats worker '%s'", worker.Name())
			continue
		}
		r.StatusInfo.Ready++
		r.WorkerStats = append(r.WorkerStats, status)
	}

	return r
}

func (s *Deployment) Start() (err error) {
	s.m.Lock()
	defer s.m.Unlock()

	return s.start()
}

func (s *Deployment) Stop() error {
	s.m.Lock()
	defer s.m.Unlock()

	return s.stop()
}

func (s *Deployment) stop() error {
	safeClose(s.stopped)
	done, target := 0, s.workers.Count()
	s.log.Infof("Stopping service '%s', %d workers to stop", s.info.Name, s.workers.Count())
	s.setState(stdstatus.Stopping, "停止中(%d/%d)", done, target)

	m := sync.Mutex{}
	wg := sync.WaitGroup{}
	errs := make([]error, 0)

	wg.Add(target)
	for _, key := range s.workers.Keys() {
		worker, _ := s.workers.Pop(key)
		go func(w *containerWorker) {
			defer wg.Done()

			err := w.Stop()

			// postprocessing
			m.Lock()
			defer m.Unlock()

			// failed to stop worker, set error message
			if err != nil {
				s.log.WithError(err).Errorf("Failed to stop worker '%s'", w.Name())
				errs = append(errs, err)
				return
			}

			// success to stop worker, update the stopping progress
			done += 1
			s.setState(stdstatus.Stopping, "停止中(%d/%d)", done, target)
		}(worker.(*containerWorker))
	}

	wg.Wait()
	if len(errs) != 0 {
		err := stderr.JoinErrors(errs...)
		s.setState(stdstatus.Exception, "容器停止失败：%s", stderr.Unwrap(err).Message())
		return err
	}

	s.setState(stdstatus.Stopped, "")
	s.log.Infof("Deployment '%s' stopped", s.info.Name)
	return nil
}

func (s *Deployment) prepare() error {
	image := s.info.Config.Image
	s.log.Infof("Preparing image '%s'", image)

	// check the existence of image
	exist, err := engine.ExistImage(image)
	if err != nil {
		return BootPrepareErr.Cause(err, "Failed to check the existence of image %q", image)
	}

	// needn't pull image while it exists locally and param "PullOnExist" is false
	if exist && !s.info.Config.PullOnExist {
		s.log.Infof("Need not to prepare image '%s', cause it is already exist", image)
		return nil
	}

	// try to pull specified image synchronously
	prg, err := engine.PrepareImage(image, s.info.Config.ImageRepoBasicToken)
	if err != nil {
		return BootPrepareErr.Cause(err, "Failed to prepare the image '%s'", image)
	}
	done := make(chan struct{})
	go func() {
		ticker := time.NewTicker(StatusUpdateInterval)
		defer ticker.Stop()
		for {
			select {
			case <-done:
				s.log.Infof("Completed downloading of image '%s'", image)
				s.setState(stdstatus.Starting, "镜像下载完成")
				return
			case <-s.stopped:
				s.log.Infof("Downloading of image '%s' canceled, cause service stopped", image)
				prg.Cancel()
				return
			case <-ticker.C:
				s.setState(stdstatus.Starting, prg.String())
			}
		}
	}()

	// wait preparing done
	err = prg.Wait()
	safeClose(done)
	if err != nil {
		return BootPrepareErr.Cause(err, "Failed to download image '%s' of service '%s'", image, s.info.Name)
	}

	s.log.Infof("Completed preparations of image '%s'", image)
	return nil
}

func (s *Deployment) start() (err error) {
	s.setState(stdstatus.Starting, "")
	s.stopped = make(chan struct{})

	defer func() {
		if err != nil {
			s.setState(stdstatus.Exception, stderr.Unwrap(err).Message())
		} else {
			s.setState(stdstatus.Running, "")
			go s.uploadStatusRegular()
		}
	}()

	if err = s.prepare(); err != nil {
		return
	}
	if err = s.scale(); err != nil {
		return
	}
	return
}

// scale adjusts the number of actually running workers to expected replica number.
func (s *Deployment) scale() error {
	// 重置重启次数超限的worker
	for id, item := range s.workers.Items() {
		worker := item.(*containerWorker)
		if !worker.Supervised() {
			if err := worker.Supervise(); err != nil {
				s.log.WithError(err).Errorf("failed to supervise worker %s while scaling", id)
			}
		}
	}

	n := s.info.Replica
	diff := s.workers.Count() - int(n)
	if diff == 0 {
		s.log.Infof("Nothing to scale for service: %s, worker num: %d", s.info.Name, s.workers.Count())
		return nil
	}

	if diff < 0 {
		// FAIL-FAST: one failure will prevent the rest workers' starting
		for ; diff < 0; diff += 1 {
			if err := s.addWorker(); err != nil {
				return err
			}
		}
	} else {
		// FAIL-FAST: same as above
		for ; diff > 0; diff -= 1 {
			if err := s.destroyWorker(); err != nil {
				return err
			}
		}
	}
	return nil
}

// newWorker will return a new unactivated worker
func (s *Deployment) newWorker(workerName string) (*containerWorker, error) {
	return engine.CreateWorker(workerName, s.info)
}

// destroyWorker destroys, which contains stop and remove, the worker by reverse order.
func (s *Deployment) destroyWorker() (err error) {
	s.log.Infof("Begin to destory a worker, current workers count is '%d'", s.workers.Count())

	wName := s.latestWorkerName()

	s.log.Infof("Destroying worker '%s'", wName)

	// handler destroy result
	defer func() {
		if err != nil {
			s.log.Errorf(stderr.Unwrap(err).Message())
			err = BootScaleErr.Cause(err, "Failed to destroy worker '%s'", wName)
		} else {
			s.log.Infof("Destroyed worker '%s'", wName)
		}
	}()

	// concurrent get and remove worker from cache
	worker, ok := s.workers.Pop(wName)
	if !ok {
		return BootScaleErr.Error("not found")
	}

	// failed to stop worker
	if err = worker.(*containerWorker).Stop(); err != nil {
		return err
	}
	return nil
}

// addWorker creates a worker and saves to cache if start it success
func (s *Deployment) addWorker() (err error) {
	s.log.Infof("Begin to add a new worker, current workers count is '%d'", s.workers.Count())

	// create worker
	wName := s.newWorkerName()

	defer func() {
		if err != nil {
			s.log.WithError(err).Errorf("Failed to add worker '%s'", wName)
			s.workers.Remove(wName)
		}
	}()

	if s.workers.Has(wName) {
		return BootWorkerExistErr.Error("Failed to add worker '%s'", wName)
	}
	worker, err := s.newWorker(wName)
	if err != nil {
		s.log.WithError(err).Errorf("Failed to create worker '%s'", wName)
		return BootConfigErr.Cause(err, "Failed to create worker '%s'", wName)
	}
	// save worker to cache
	s.workers.Set(worker.Name(), worker)

	// start worker
	if err = worker.Start(); err != nil {
		_ = worker.Stop()
		s.log.WithError(err).Errorf("Failed to start worker '%s', stop it anyway", wName)
		return BootWorkerStartErr.Cause(err, "Failed to start worker '%s'", wName)
	}

	s.log.Infof("Worker '%s' added and started", worker.Name())
	return nil
}

func (s *Deployment) State() stdstatus.StdState {
	running, restarting, _ := s.workerStates()
	if running+restarting > 0 {
		return stdstatus.Running
	}
	return stdstatus.Exception
}

func (s *Deployment) workerStates() (running, restarting, exits int) {
	for id, item := range s.workers.Items() {
		worker := item.(*containerWorker)
		status, err := worker.Stats()
		if err != nil {
			s.log.WithError(err).Errorf("failed to get status of worker %s", id)
			continue
		}

		switch status.State {
		case pb.WorkerRunning:
			running++
		case pb.WorkerRestarting:
			restarting++
		case pb.WorkerExited:
			if worker.Supervised() {
				restarting++
			} else {
				exits++
			}
		}
	}
	return
}

// strings.ToLower(s.info.Name) 兼容 k8s PodName 不能大写的问题
func (s *Deployment) newWorkerName() string {
	return fmt.Sprintf("%s-%s-%d", s.info.Namespace, strings.ToLower(s.info.Name), s.workers.Count())
}
func (s *Deployment) latestWorkerName() string {
	return fmt.Sprintf("%s-%s-%d", s.info.Namespace, strings.ToLower(s.info.Name), s.workers.Count())
}

//	若该服务存在至少一个运行中/重启中的副本,则认为该服务为Running
func (s *Deployment) uploadStatusRegular() {
	ticker := time.NewTicker(StatusUpdateInterval)
	defer ticker.Stop()
	for {
		select {
		case <-s.stopped:
			s.log.Infof("Exited state check cause service was stopped")
			return
		case <-ticker.C:
			running, restarting, _ := s.workerStates()
			if running > 0 {
				s.setState(stdstatus.Running, "")
				continue
			}
			if restarting > 0 {
				s.setState(stdstatus.Restarting, "")
				continue
			}
			s.setState(stdstatus.Exception, "重试次数超限:%d", s.info.Config.Restart.MaxRetry)
		}
	}
}

const (
	minWatchInterval = time.Second
	statsMaxCache    = 100
)

func (s *Deployment) WatchStats(interval time.Duration) <-chan *pb.ServiceStats {
	if interval < minWatchInterval {
		interval = minWatchInterval
	}
	ch := make(chan *pb.ServiceStats, statsMaxCache)
	ticker := time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-s.stopped:
				s.log.Infof("stop watching service stats, cause it has been stopped")
				close(ch)
				ticker.Stop()
				return
			case <-ticker.C:
				ch <- s.Stats()
			}
		}
	}()
	return ch
}

func (s *Deployment) setState(state stdstatus.StdState, format string, args ...interface{}) {
	s.state = state
	s.msg = fmt.Sprintf(format, args...)
}

func safeClose(c chan struct{}) {
	if c == nil {
		return
	}
	select {
	case <-c:
		// channel has already closed
	default:
		close(c)
	}
}
