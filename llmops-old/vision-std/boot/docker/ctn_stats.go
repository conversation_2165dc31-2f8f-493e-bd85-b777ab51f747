package docker

import (
	"bufio"
	"context"
	"encoding/json"
	"github.com/docker/docker/api/types"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
)

func (w *containerWorker) MonitorStats() error {
	ctx := context.Background()
	if !w.monitor.closed {
		return nil
	}
	w.monitor.closed = false
	w.log.Info("Monitor the stats of container")
	ctnStats, err := w.cli.ContainerStats(ctx, w.Name(), true)
	if err != nil {
		w.log.WithError(err).Warnf("Failed to stats container")
		return err
	}

	go func() {
		defer w.log.Info("Monitor of container's stats exited")
		scanner := bufio.NewScanner(ctnStats.Body)
		nextTime := time.Now()
		// do loop to scan container stats until it has been canceled or Body has been closed
		for scanner.Scan() && !w.monitor.closed {
			if time.Now().Before(nextTime) {
				continue
			}
			nextTime = nextTime.Add(w.monitor.interval) // set next time to handle worker's stats
			stats, err := w.buildContainerStats(scanner.Bytes())
			if err != nil {
				w.log.WithError(err).Warnf("Failed to build container stats %s", scanner.Text())
				continue
			}

			// get container's state
			inspect, err := w.cli.ContainerInspect(ctx, w.Name())
			if err != nil {
				w.log.WithError(err).Warnf("failed to inspect container %q", w.briefRef())
				continue
			}
			w.monitor.crtStats = &pb.WorkerStats{
				Name:  w.Id(),
				State: inspect.State.Status,
				Stats: stats,
			}
			w.monitor.Handle(w.monitor.crtStats)
		}
		// stream has been stopped
		if !w.monitor.closed {
			w.log.Warn("Container may has been removed, exit monitor")
			w.monitor.closed = true
		}
		w.log.Debugf("stop to monitor stats of container %q", w.briefRef())
		_ = ctnStats.Body.Close()
	}()
	return nil
}

func (w *containerWorker) buildContainerStats(jsonStats []byte) (*pb.ContainerStats, error) {
	// parse container stats
	var stats types.StatsJSON
	if err := json.Unmarshal(jsonStats, &stats); err != nil {
		w.log.WithError(err).Warnf("failed to unmarshal stats response of container (%s)", w.Id())
		return nil, err
	}

	// calculate required fields
	memUsage := stats.MemoryStats.Usage - stats.MemoryStats.Stats["cache"]
	memLimit := stats.MemoryStats.Limit
	memPercent := 0.0
	// MemoryStats.Limit will never be 0 unless the container is not running
	if memLimit != 0 {
		memPercent = float64(memUsage) / float64(memLimit) * 100.00
	}
	dur := stats.Read.Sub(w.lastStats.Read)
	avgBlkRead, avgBlkWrite := calculateAvgBlockIO(dur, w.lastStats.BlkioStats, stats.BlkioStats)
	avgNetRecv, avgNetSent := calculateAvgNetwork(dur, w.lastStats.Networks, stats.Networks)

	w.lastStats = stats
	return &pb.ContainerStats{
		CpuPercent: calculateCPUPercent(stats.CPUStats, stats.PreCPUStats),
		MemPercent: memPercent,
		MemUsage:   int64(memUsage),
		MemLimit:   int64(memLimit),
		NetRecv:    int64(avgNetRecv),
		NetSent:    int64(avgNetSent),
		BlockRead:  int64(avgBlkRead),
		BlockWrite: int64(avgBlkWrite),
		PidsNum:    int64(stats.PidsStats.Current),
	}, nil
}

func calculateBlockIO(blkio types.BlkioStats) (blkRead, blkWrite uint64) {
	for _, bioEntry := range blkio.IoServiceBytesRecursive {
		switch strings.ToLower(bioEntry.Op) {
		case "read":
			blkRead = blkRead + bioEntry.Value
		case "write":
			blkWrite = blkWrite + bioEntry.Value
		}
	}
	return
}

func calculateNetwork(network map[string]types.NetworkStats) (netRecv, netSent uint64) {
	for _, v := range network {
		netRecv += v.RxBytes
		netSent += v.TxBytes
	}
	return
}

func calculateAvgBlockIO(dur time.Duration, prev, curt types.BlkioStats) (avgBlkRead, avgBlkWrite float64) {
	if dur.Seconds() <= 0.0 {
		return
	}
	prevR, prevW := calculateBlockIO(prev)
	curtR, curtW := calculateBlockIO(curt)
	deltaR := float64(curtR) - float64(prevR)
	deltaW := float64(curtW) - float64(prevW)
	if deltaR > 0.0 {
		avgBlkRead = deltaR / dur.Seconds()
	}
	if deltaR > 0.0 {
		avgBlkWrite = deltaW / dur.Seconds()
	}
	return
}

func calculateAvgNetwork(dur time.Duration, prev, curt map[string]types.NetworkStats) (avgNetRecv, avgNetSent float64) {
	prevRecv, prevSent := calculateNetwork(prev)
	curtRecv, curtSent := calculateNetwork(curt)
	deltaRecv := float64(curtRecv) - float64(prevRecv)
	deltaSend := float64(curtSent) - float64(prevSent)
	if deltaRecv > 0.0 {
		avgNetRecv = deltaRecv / dur.Seconds()
	}
	if deltaRecv > 0.0 {
		avgNetSent = deltaSend / dur.Seconds()
	}
	return
}

func calculateCPUPercent(crtStats, preStats types.CPUStats) (cpuPercent float64) {
	var (
		// calculate the change for the cpu usage of the container in between readings
		cpuDelta = float64(crtStats.CPUUsage.TotalUsage) - float64(preStats.CPUUsage.TotalUsage)
		// calculate the change for the entire system between readings
		systemDelta = float64(crtStats.SystemUsage) - float64(preStats.SystemUsage)
	)

	if systemDelta > 0.0 && cpuDelta > 0.0 {
		cpuPercent = (cpuDelta / systemDelta) * float64(len(crtStats.CPUUsage.PercpuUsage)) * 100.0
	}
	return cpuPercent
}

type WorkerStatsMonitor struct {
	closed   bool
	interval time.Duration
	crtStats *pb.WorkerStats
	ch       chan *pb.WorkerStats // 用于发送状态信息
}

const (
	DefaultMonitorInterval = time.Second * 3
)

func NewWorkerStatsMonitor(svc *pb.BootService, ch chan *pb.WorkerStats) *WorkerStatsMonitor {
	return &WorkerStatsMonitor{
		closed:   true,
		interval: DefaultMonitorInterval,
		crtStats: nil,
		ch:       ch,
	}
}

func (w *WorkerStatsMonitor) Start() {
	if !w.closed {
		return
	}
	w.closed = false
}

func (w *WorkerStatsMonitor) Handle(stats *pb.WorkerStats) {
	w.crtStats = stats
	w.ch <- stats
}

func (w *WorkerStatsMonitor) Stop() {
	w.closed = true
	close(w.ch)
}
