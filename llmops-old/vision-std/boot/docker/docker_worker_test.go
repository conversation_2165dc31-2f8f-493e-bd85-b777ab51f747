package docker

import (
	"fmt"
	"testing"
)

func TestDockerContainer_buildContainerStats(t *testing.T) {
	jsonStats := []byte(`{"read":"2019-12-12T08:36:25.675733868Z","preread":"2019-12-12T08:36:24.67228259Z","pids_stats":{"current":10},"blkio_stats":{"io_service_bytes_recursive":[{"major":8,"minor":0,"op":"Read","value":18874368},{"major":8,"minor":0,"op":"Write","value":86016},{"major":8,"minor":0,"op":"Sync","value":18960384},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":18960384},{"major":8,"minor":0,"op":"Read","value":18874368},{"major":8,"minor":0,"op":"Write","value":86016},{"major":8,"minor":0,"op":"Sync","value":18960384},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":18960384}],"io_serviced_recursive":[{"major":8,"minor":0,"op":"Read","value":588},{"major":8,"minor":0,"op":"Write","value":14},{"major":8,"minor":0,"op":"Sync","value":602},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":602},{"major":8,"minor":0,"op":"Read","value":588},{"major":8,"minor":0,"op":"Write","value":14},{"major":8,"minor":0,"op":"Sync","value":602},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":602}],"io_queue_recursive":[{"major":8,"minor":0,"op":"Read","value":0},{"major":8,"minor":0,"op":"Write","value":0},{"major":8,"minor":0,"op":"Sync","value":0},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":0}],"io_service_time_recursive":[{"major":8,"minor":0,"op":"Read","value":291481393},{"major":8,"minor":0,"op":"Write","value":2615939},{"major":8,"minor":0,"op":"Sync","value":294097332},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":294097332}],"io_wait_time_recursive":[{"major":8,"minor":0,"op":"Read","value":83708907},{"major":8,"minor":0,"op":"Write","value":16713347},{"major":8,"minor":0,"op":"Sync","value":100422254},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":100422254}],"io_merged_recursive":[{"major":8,"minor":0,"op":"Read","value":0},{"major":8,"minor":0,"op":"Write","value":0},{"major":8,"minor":0,"op":"Sync","value":0},{"major":8,"minor":0,"op":"Async","value":0},{"major":8,"minor":0,"op":"Total","value":0}],"io_time_recursive":[{"major":8,"minor":0,"op":"","value":439936171}],"sectors_recursive":[{"major":8,"minor":0,"op":"","value":37032}]},"num_procs":0,"storage_stats":{},"cpu_stats":{"cpu_usage":{"total_usage":518849604,"percpu_usage":[360505755,158343849,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"usage_in_kernelmode":150000000,"usage_in_usermode":320000000},"system_cpu_usage":533775930000000,"online_cpus":2,"throttling_data":{"periods":0,"throttled_periods":0,"throttled_time":0}},"precpu_stats":{"cpu_usage":{"total_usage":518849604,"percpu_usage":[360505755,158343849,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"usage_in_kernelmode":150000000,"usage_in_usermode":320000000},"system_cpu_usage":533774020000000,"online_cpus":2,"throttling_data":{"periods":0,"throttled_periods":0,"throttled_time":0}},"memory_stats":{"usage":26017792,"max_usage":27951104,"stats":{"active_anon":2310144,"active_file":1929216,"cache":18649088,"dirty":0,"hierarchical_memory_limit":9223372036854771712,"hierarchical_memsw_limit":0,"inactive_anon":2420736,"inactive_file":16691200,"mapped_file":13578240,"pgfault":3775,"pgmajfault":146,"pgpgin":8611,"pgpgout":2910,"rss":4702208,"rss_huge":0,"total_active_anon":2310144,"total_active_file":1929216,"total_cache":18649088,"total_dirty":0,"total_inactive_anon":2420736,"total_inactive_file":16691200,"total_mapped_file":13578240,"total_pgfault":3775,"total_pgmajfault":146,"total_pgpgin":8611,"total_pgpgout":2910,"total_rss":4702208,"total_rss_huge":0,"total_unevictable":0,"total_writeback":0,"unevictable":0,"writeback":0},"limit":8339873792},"name":"/thinger-chronograf","id":"200ea3d9426e8a2e2ff87c68a5fe31ffa0cdf45cf666f20f16217e31b10b8622","networks":{"eth0":{"rx_bytes":15822,"rx_packets":97,"rx_errors":0,"rx_dropped":0,"tx_bytes":1849,"tx_packets":20,"tx_errors":0,"tx_dropped":0}}}`)
	w := &containerWorker{}
	got, err := w.buildContainerStats(jsonStats)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v", got)
}

