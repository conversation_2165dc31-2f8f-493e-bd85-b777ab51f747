package stdhub

import (
	"encoding/json"
	"fmt"
	"testing"
	stdconf "transwarp.io/applied-ai/aiot/vision-std/conf"
)

const mwhModelRsc RscType = "mwh_model"

type MWHModelRsc struct {
	RscBaseInfo
	F1 string
	F2 bool
	F3 int
}

func (m *MWHModelRsc) SetValue(bs []byte) error {
	return json.Unmarshal(bs, &m)
}
func (m *MWHModelRsc) Value() []byte {
	bs, err := json.Marshal(m)
	if err != nil {
		fmt.Printf("failed unmarshal %e", err)
		return nil
	}
	return bs
}

var testMwhRsc = &MWHModelRsc{
	RscBaseInfo: RscBaseInfo{
		RscKey: RscKey{
			ID:    "mwh-model-xxx",
			Owner: "m3",
			Type:  mwhModelRsc,
		},
		Name: "test-sql-hub",
		Desc: "test-sql-hub",
	},
	F1: "unit test",
	F2: false,
	F3: 0,
}
var mysqlConfig = stdconf.MysqlConfig{
	Username:       "root",
	Password:       "Warp!CV@2022#",
	Host:           "*************",
	Port:           "31398",
	DBName:         "mwh_backend",
	MaxIdle:        0,
	MaxConn:        0,
	NotPrintSql:    false,
	NotCreateTable: false,
}

func Test_SqlHub(t *testing.T) {
	_ = RegisterCustomResource(mwhModelRsc, Rsc)
	rscHub, err := NewSqlResourceHub(&SqlResourceHubConfig{mysqlConfig})
	if err != nil {
		t.Error(err)
		t.Fail()
		return
	}
	t.Run("testing save func", func(t *testing.T) {
		err = rscHub.Save(testMwhRsc)
		if err != nil {
			t.Error(err)
			t.Fail()
			return
		}
		testMwhRsc.Desc = "should update this record"
		err = rscHub.Save(testMwhRsc)
		if err != nil {
			t.Error(err)
			t.Fail()
			return
		}
	})
	t.Run("testing Load func", func(t *testing.T) {
		loadedRsc := new(MWHModelRsc)
		err := rscHub.Load(testMwhRsc.RscKey, loadedRsc)
		if err != nil {
			t.Error(err)
			t.Fail()
			return
		}
		t.Logf("load data %v", loadedRsc)
	})
	t.Run("testing list func", func(t *testing.T) {
		listedRsc := make([]*MWHModelRsc, 0)
		err := rscHub.List("m3", mwhModelRsc, &listedRsc)
		if err != nil {
			t.Error(err)
			t.Fail()
			return
		}
		for _, rsc := range listedRsc {
			t.Log(rsc)
		}
	})
}
