package stdhub

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/dgraph-io/badger"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

const (
	idSep        = "-"
	keySep       = "/"
	keyPrefix    = "/thinger"
	maxListItems = 100000 // 单次获取数据最大键值对数目

	Anyone = "*" // 不对 owner 进行限制
)

// RscTimeMixin 时间戳信息
// 用于记录资源的创建,更新以及删除的时间戳
// 其中删除时间戳可用来恢复误操作导致的资源丢失
type RscTimeMixin struct {
	CreateTimeMills int64 `json:"create_time_mills" deep:"-"` // 资源的创建时间(毫秒)
	UpdateTimeMills int64 `json:"update_time_mills" deep:"-"` // 资源的修改时间(毫秒)
	DeleteTimeMills int64 `json:"delete_time_mills" deep:"-"` // 资源的(软)删除时间(毫秒)
}

func NewRscTimeMixin() RscTimeMixin {
	curtMs := toolkit.NowMS()
	return RscTimeMixin{
		CreateTimeMills: curtMs,
		UpdateTimeMills: curtMs,
		DeleteTimeMills: 0,
	}
}

func (t RscTimeMixin) RscCreateTime() time.Time {
	return time.Unix(0, t.CreateTimeMills*1e6)
}

func (t RscTimeMixin) RscUpdateTime() time.Time {
	return time.Unix(0, t.UpdateTimeMills*1e6)
}

func (t RscTimeMixin) RscDeleteTime() time.Time {
	return time.Unix(0, t.DeleteTimeMills*1e6)
}

func (t RscTimeMixin) IsDeleted() bool {
	return t.DeleteTimeMills != 0
}

func (t *RscTimeMixin) SetUpdateTime() {
	t.UpdateTimeMills = toolkit.NowMS()
}

// RscKey 系统资源唯一标识
// 形如: /thinger/edge-1/DEVICE/DEVICE-1234
type RscKey struct {
	ID    string  `json:"id"`    // 资源ID, 后台自动生成, k8s资源中作为name
	Owner string  `json:"owner"` // 资源的所有者
	Type  RscType `json:"type"`  // 资源类型
}

func NewRscKey(owner string, rscType RscType, id string) RscKey {
	return RscKey{
		ID:    id,
		Owner: owner,
		Type:  rscType,
	}
}

func NewRscKeyFromString(k string) *RscKey {
	parts := strings.Split(k, keySep)
	if len(parts) != 5 {
		return nil
	}
	return &RscKey{
		Owner: parts[2],
		Type:  RscType(parts[3]),
		ID:    parts[4],
	}
}

// GenRscKey 新建一个ResourceKey并自动生成ID
func GenRscKey(owner string, resType RscType) RscKey {
	return RscKey{
		ID:    GenRsKeyId(resType),
		Owner: owner,
		Type:  resType,
	}
}

func GenRsKeyId(resType RscType) string {
	return strings.ToUpper(string(resType)) + idSep + toolkit.NewUUID()
}

// ParseRscTypeFromID 通过解析资源的ID前缀, 得到其对应的资源类型
func ParseRscTypeFromID(rscID string) (RscType, error) {
	parts := strings.Split(rscID, idSep)
	if len(parts) != 2 {
		return "", stderr.InvalidParam.Error("invalid id : '%s', cause id separator '%s' not found", rscID, idSep)
	}
	rt := RscType(strings.ToLower(parts[0]))
	if _, ok := rscTypes[rt]; !ok {
		return "", invalidRscTypeErr(rt)
	}
	return rt, nil
}

func NewRscKeyFromID(owner, id string) (RscKey, error) {
	rt, err := ParseRscTypeFromID(id)
	if err != nil {
		return RscKey{}, err
	}
	return RscKey{
		ID:    id,
		Owner: owner,
		Type:  rt,
	}, nil
}

func (k RscKey) RscID() string {
	return k.ID
}

func (k RscKey) RscOwner() string {
	return k.Owner
}

func (k RscKey) RscType() RscType {
	return k.Type
}

func (k RscKey) RscKey() RscKey {
	return k
}

func (k RscKey) KeyString() string {
	return strings.Join([]string{keyPrefix, k.Owner, string(k.Type), k.ID}, keySep)
}

func (k RscKey) KeyBytes() []byte {
	return []byte(k.KeyString())
}

func (k RscKey) Key() RscKey {
	return k
}

func (k RscKey) Valid() (err error) {
	defer func() {
		if err != nil {
			err = invalidRscKeyErr(err, k)
		}
	}()
	if _, ok := rscTypes[k.Type]; !ok {
		return invalidRscTypeErr(k.Type)
	}
	if k.Owner == "" {
		return fmt.Errorf("resource owner is necessary")
	}
	if k.ID == "" {
		return fmt.Errorf("resource id is necessary")
	}
	return nil
}

// RscBaseInfo 为系统资源的必备的基础信息
type RscBaseInfo struct {
	RscKey
	RscTimeMixin
	Name string `json:"name"` // 资源名称, 用户指定, 不可重复
	Desc string `json:"desc"` // 资源描述, 用户选填
}

func NewRscBaseInfo(rscType RscType, owner, name, desc string) RscBaseInfo {
	return RscBaseInfo{
		RscTimeMixin: NewRscTimeMixin(),
		RscKey:       GenRscKey(owner, rscType),
		Name:         name,
		Desc:         desc,
	}
}

func NewRscBaseInfoWithID(rscType RscType, id, owner, name, desc string) RscBaseInfo {
	base := NewRscBaseInfo(rscType, owner, name, desc)
	base.ID = id
	return base
}

func (r RscBaseInfo) RscName() string {
	return r.Name
}

func (r RscBaseInfo) RscDesc() string {
	return r.Desc
}

// BriefInfo 返回资源的简称, 用于打印日志时输出该资源的简要信息
// 格式为: ${name}(${id})
func (r RscBaseInfo) BriefInfo() string {
	return fmt.Sprintf("%s'%s(%s)'", strings.ToUpper(string(r.RscType())), r.Name, r.ID)
}

type IRscKey interface {
	// ID 返回资源的全局唯一标识符
	RscID() string

	// Owner 返回资源所属的节点ID
	RscOwner() string

	// KeyString 返回资源在数据库中的唯一标识符的字符串形式
	KeyString() string

	// KeyBytes 返回资源在数据库中的唯一标识符的字节数组形式
	KeyBytes() []byte

	Key() RscKey
}

type IRscTimeMixin interface {
	// RscCreateTime 返回资源的创建时间
	RscCreateTime() time.Time

	// RscUpdateTime 返回资源的更新时间
	RscUpdateTime() time.Time

	// RscDeleteTime 返回资源的更新时间
	RscDeleteTime() time.Time

	// IsDeleted 返回当前资源是否已经被标记为已删除
	IsDeleted() bool

	// SetUpdateTime 更新资源的更新时间
	SetUpdateTime()
}

// IRscBase 所有可存储的系统资源的基本信息
type IRscBase interface {
	IRscKey
	IRscTimeMixin

	// RscName 返回资源的名称
	RscName() string

	// RscDesc 返回资源的描述
	RscDesc() string
}

// ThingerResource 可持久化的系统资源
type ThingerResource interface {
	IRscBase

	// SetValue 将字节流反序列化为结构体并覆盖当前内容
	SetValue([]byte) error

	// Value 返回当前结构体的序列化字节流
	Value() []byte
}

type ResourceHub interface {
	// Close 关闭已打开的数据库，关闭后数据无法继续访问
	Close() error

	// Exist 判断指定的系统资源是否存在
	Exist(k RscKey) bool

	// Load 用于获取指定Key的系统资源, 并覆盖传入的r
	// 当资源不存在时, 将返回error
	Load(k RscKey, r ThingerResource) error

	// WatchAll 用于检测本地所有资源的改动,并提供回调机制
	// NOTICE!!!
	// 当handler返回error不为空时, 该事件处理进程将退出
	WatchAll(ctx context.Context, handler RscKVHandler) error

	// WatchKV 用于检测属于指定owner的本地资源的改动,并提供回调机制
	// NOTICE!!!
	// 当handler返回error不为空时, 该事件处理进程将退出
	WatchKV(ctx context.Context, owner string, handler RscKVHandler) error

	// WatchRsc 用于检测本地资源的改动,并提供回调机制
	// NOTICE!!!
	// 当handler返回error不为空时, 该事件处理进程将退出
	WatchRsc(ctx context.Context, owner string, rscType RscType, rsc ThingerResource, handler RscHandler) error

	// Count 统计指定资源类型与符合过滤条件的资源的数量
	// 若统计中出现错误,则返回-1;
	// r 用以指定统计数量的资源类型;
	Count(owner string, rscType RscType, r ThingerResource, selectors ...ResourceSelector) int

	// CountAll 统计指定资源类型的总数量（包括已删除）
	CountAll(owner string, rscType RscType) int

	// List 用于获取指定节点与类型上的符合筛选条件的系统资源列表
	// 结果将被写回传入的rs中, 要求其为一个数组(存放系统资源指针)的指针
	List(owner string, rscType RscType, rs interface{}, selectors ...ResourceSelector) error

	// ListPage 用于获取指定节点与类型上的符合筛选条件的系统资源列表
	// 结果将被写回传入的rs中, 要求其为一个数组(存放系统资源指针)的指针
	// skip 预先跳过多少个key
	// limit 最多返回多少个实体
	ListPage(owner string, rscType RscType, rs interface{}, skip, limit int, selectors ...ResourceSelector) error

	// ListKVs 返回指定prefix下的所有KV
	ListKVs(prefix string) ([]*KV, error)

	Get(key []byte) ([]byte, error)

	Set(key, value []byte) error

	// Delete 用于删除一个指定的系统资源
	Delete(k RscKey) error

	// RawDelete 用于删除任意一个资源
	RawDelete(k []byte) error

	// Save 用于创建或更新一个系统资源
	Save(r ThingerResource) error

	SaveKV(kv *KV) error

	// Clean 清除指定节点和类型的所有资源信息
	Clean(owner string, rscType RscType) error
}

type hubImpl struct {
	db *badger.DB
}

// DropResourceRevision 丢弃指定路径的Hub数据库中所有非最新的历史版本
// backup 为原始数据库文件的备份地址
func DropResourceRevision(path string) (backup string, err error) {
	dir, file := filepath.Split(path)
	if file == "" {
		return "", stderr.InvalidParam.Error("invalid db file path %s", path)
	}
	// 读取原数据库中所有键值对，写入临时数据库
	srcHub, err := NewResourceHub(path, NewGCLoopOption(false))
	if err != nil {
		return "", stderr.Wrap(err, "failed to open source resource hub %s", path)
	}
	kvs, err := srcHub.ListKVs(keyPrefix)
	if err != nil {
		return "", stderr.Wrap(err, "failed to list kvs with prefix '%s'", keyPrefix)
	}

	tmpHub, tmpPath, err := NewTmpResourceHub(dir, NewGCLoopOption(false))
	if err != nil {
		return "", stderr.Wrap(err, "failed to open new temp resource hub")
	}
	stdlog.Infof("cloning %d kvs into new tmp resource hub %s", len(kvs), tmpPath)
	for _, kv := range kvs {
		if err := tmpHub.SaveKV(kv); err != nil {
			return "", stderr.Wrap(err, "failed to write kv into tmp hub")
		}
	}
	stdlog.Infof("cloning %d kvs into resource hub %s done", len(kvs), tmpPath)
	if err := srcHub.Close(); err != nil {
		return "", stderr.Wrap(err, "failed to close original hub")
	}
	if err := tmpHub.Close(); err != nil {
		return "", stderr.Wrap(err, "failed to close temp hub")
	}

	// 备份原有数据库
	backup = filepath.Join(dir, fmt.Sprintf("backup_rschub_of_%s_at_%d", file, time.Now().UnixNano()))
	stdlog.Infof("backing up original resource hub db file '%s' to '%s'", path, backup)
	if err := os.Rename(path, backup); err != nil {
		return "", stderr.Wrap(err, "failed to backup original db")
	}
	// 使用新的数据库替换原有数据库
	if err := os.Rename(tmpPath, path); err != nil {
		return "", stderr.Wrap(err, "failed to mv temp db to formal")
	}

	return backup, nil
}

type ResourceHubConfig struct {
	path         string
	gcLoop       bool // gcLoop=true 是否开启定时垃圾回收（资源压缩合并）功能
	dropRevision bool // dropRevision=false 启动前是否删除所有键值对历史版本数据以节省磁盘空间，提升打开速度
}

type ResourceHubOption func(*ResourceHubConfig)

func NewGCLoopOption(gc bool) ResourceHubOption {
	return func(c *ResourceHubConfig) {
		c.gcLoop = gc
	}
}

func NewDropRevisionOption(drop bool) ResourceHubOption {
	return func(c *ResourceHubConfig) {
		c.dropRevision = drop
	}
}

func NewResourceHub(path string, opts ...ResourceHubOption) (ResourceHub, error) {
	cfg := &ResourceHubConfig{
		path:         path,
		gcLoop:       true,
		dropRevision: false,
	}
	for _, opt := range opts {
		opt(cfg)
	}
	if cfg.dropRevision {
		if _, err := DropResourceRevision(path); err != nil {
			return nil, err
		}
	}

	db, err := badger.Open(badger.DefaultOptions(path).WithTruncate(true))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to open badger db '%s'", path)
	}
	if cfg.gcLoop {
		go gcLoop(db)
	}
	return hubImpl{
		db: db,
	}, nil
}

func NewTmpResourceHub(root string, opts ...ResourceHubOption) (ResourceHub, string, error) {
	if root == "" {
		// 支持手动指定root， 避免rename时 /tmp 与 mount path 之间 invalid cross-device link 错误
		root = "/tmp"
	}
	path := filepath.Join(root, fmt.Sprintf("Temp_Resource_Hub_%d", time.Now().UnixNano()))
	hub, err := NewResourceHub(path, opts...)
	return hub, path, err
}

func gcLoop(db *badger.DB) {
	// 启动时进行一次GC
	gcBadgerDB(db)
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		gcBadgerDB(db)
	}
}

// gcBadgerDB 清理所有VLOG文件, 删除过期的历史操作记录, 重写VLOG文件
func gcBadgerDB(db *badger.DB) {
	count := 0
	var err error
	for err == nil {
		stdlog.Infof("starting badger db value log gc")
		count++
		err = db.RunValueLogGC(0.5)
	}
	if err != badger.ErrNoRewrite {
		stdlog.WithError(err).Errorf("error running value log gc")
		return
	}
	stdlog.Infof("no rewrite needed")
	stdlog.Infof("completed gc, ran %d times", count)
}

//	func (t hubImpl) Merge(k RscKey) bool {
//		t.db.GetMergeOperator(k.KeyBytes(), func(existingVal, newVal []byte) []byte {
//
//		},time.Minute)
//		t.db.PrintHistogram()Flatten()
//		return false
//	}
func (t hubImpl) Close() error {
	return t.db.Close()
}

func (t hubImpl) Exist(k RscKey) bool {
	ts, err := t.GetTs(k.KeyBytes())
	if err != nil {
		// 资源不存在
		return false
	}
	// 资源存在但是已被标记为删除
	if ts.TimeMixin.IsDeleted() {
		return false
	}
	return true
}

func (t hubImpl) Load(k RscKey, r ThingerResource) error {
	v, err := t.Get(k.KeyBytes())
	// 读取资源失败
	if err != nil {
		return err
	}

	// 结构体解析失败
	if err := r.SetValue(v); err != nil {
		return setValueErr(err, v)
	}

	// 资源已被删除
	if r == nil || r.IsDeleted() {
		return stderr.ResourceDeleted.Error("%s", k.KeyString())
	}
	return nil
}

// Get 底层数据库的封装, 返回的value可为已被标记为删除的资源
func (t hubImpl) Get(key []byte) ([]byte, error) {
	var value []byte
	err := t.db.View(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if err != nil {
			// key 不存在
			if err == badger.ErrKeyNotFound {
				return stderr.ResourceNotFound.Cause(err, "key: '%s'", key)
			}
			// 数据库错误
			return stderr.ResourceQueryFailure.Cause(err, "failed to get '%s'", key)
		}
		// key存在,但是value为空
		if item.ValueSize() == 0 {
			return stderr.ResourceValueEmpty.Error("resource '%s' is empty", key)
		}
		// 读取value内容
		value, err = item.ValueCopy(nil)
		if err != nil {
			return copyValueErr(err)
		}
		return nil
	})
	return value, err
}

func (t hubImpl) GetTs(key []byte) (*RscTs, error) {
	v, err := t.Get(key)
	if err != nil {
		return nil, err
	}

	// 仅解析value中的时间戳信息
	mixin, err := parseTimeFromValue(v)
	if err != nil {
		return nil, err
	}
	return &RscTs{
		Key:       string(key),
		TimeMixin: mixin,
	}, nil
}

func (t hubImpl) ListTs(owner string, rscType RscType) ([]*RscTs, error) {
	return nil, nil
}

func (t hubImpl) List(owner string, rscType RscType, rs interface{}, selectors ...ResourceSelector) error {
	return t.ListPage(owner, rscType, rs, 0, maxListItems, selectors...)
}

func (t hubImpl) ListPage(owner string, rscType RscType, rs interface{}, skip, limit int, selectors ...ResourceSelector) error {
	var prefix string
	if owner == "" {
		return fmt.Errorf("owner is necessary while listing resources")
	} else if owner == Anyone {
		prefix = keyPrefix
	} else {
		prefix = KeyPrefixByType(owner, rscType)
	}
	if _, ok := rscTypes[rscType]; !ok {
		return invalidRscTypeErr(rscType)
	}
	selector := JoinSelectorsByAnd(selectors...)
	err := t.db.View(func(txn *badger.Txn) error {

		// parse and check resources type
		rst := reflect.TypeOf(rs) // *[]*struct
		if rst.Kind() != reflect.Ptr || rst.Elem().Kind() != reflect.Slice || rst.Elem().Elem().Kind() != reflect.Ptr {
			return stderr.ResourceQueryFailure.Error("the receiver of resources must be a slice ptr of resource ptr")
		}
		rsv := reflect.ValueOf(rs).Elem()
		rt := rst.Elem().Elem().Elem()

		// get iterator
		opts := badger.DefaultIteratorOptions
		opts.Prefix = []byte(prefix)
		it := txn.NewIterator(opts)
		defer it.Close()
		cnt := 0 // 已选择出的元素数目
		for it.Rewind(); it.Valid(); it.Next() {
			if skip > 0 {
				// 跳过前K个元素
				skip--
				continue
			}
			if it.Item().IsDeletedOrExpired() || it.Item().ValueSize() == 0 {
				continue
			}

			rk := NewRscKeyFromString(string(it.Item().Key()))
			if rk == nil || rk.RscType() != rscType {
				continue
			}

			bs, err := it.Item().ValueCopy(nil)
			if err != nil {
				return copyValueErr(err)
			}

			rv := reflect.New(rt)
			r := rv.Interface().(ThingerResource)
			if err := r.SetValue(bs); err != nil {
				return setValueErr(err, bs)
			}

			// 跳过已删除资源
			if r.IsDeleted() {
				continue
			}

			// 跳过不感兴趣的资源
			if selector != nil && !selector(r) {
				continue
			}
			rsv.Set(reflect.Append(rsv, rv))
			cnt++
			if cnt >= limit {
				// 已达到最大数量，跳过剩余迭代
				return nil
			}
		}
		return nil
	})
	return err
}

func (t hubImpl) ListKVs(prefix string) ([]*KV, error) {
	kvs := make([]*KV, 0)
	err := t.db.View(func(txn *badger.Txn) error {
		// get iterator
		opts := badger.DefaultIteratorOptions
		opts.Prefix = []byte(prefix)
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			if it.Item().IsDeletedOrExpired() || it.Item().ValueSize() == 0 {
				continue
			}
			bs, err := it.Item().ValueCopy(nil)
			if err != nil {
				return copyValueErr(err)
			}

			kvs = append(kvs, &KV{
				Key:   it.Item().KeyCopy(nil),
				Value: bs,
			})
		}
		return nil
	})
	return kvs, err
}

// WatchAll 用于检测资源的改动,并提供回调机制
// 该方法调用将会阻塞, 直到context结束 或者 处理事件中发生error
func (t hubImpl) WatchAll(ctx context.Context, handler RscKVHandler) error {
	return t.doWatch(ctx, keyPrefix, handler)
}

// WatchKV 用于检测资源的改动,并提供回调机制
// 该方法调用将会阻塞, 直到context结束 或者 处理事件中发生error
func (t hubImpl) WatchKV(ctx context.Context, owner string, handler RscKVHandler) error {
	return t.doWatch(ctx, KeyPrefixByOwner(owner), handler)
}

// WatchRsc 用于检测某种类型的资源改动,并提供回调机制
// 该方法调用将会阻塞, 直到context结束 或者 处理事件中发生error
func (t hubImpl) WatchRsc(ctx context.Context, owner string, rscType RscType, rsc ThingerResource, handler RscHandler) error {
	var prefix string
	if owner == Anyone {
		prefix = keyPrefix
	} else {
		prefix = KeyPrefixByType(owner, rscType)
	}
	rt := reflect.TypeOf(rsc).Elem()
	kvHandler := func(kv *KV) error {
		key := NewRscKeyFromString(string(kv.Key))
		if key == nil {
			stdlog.Errorf("invalid resource key : %s", kv.Key)
			return nil
		}
		if key.RscType() != rscType {
			return nil
		}
		if len(kv.Value) == 0 {
			return handler(*key, nil)
		}
		nRsc := reflect.New(rt).Interface().(ThingerResource)
		if err := nRsc.SetValue(kv.Value); err != nil {
			return err
		}
		if nRsc.IsDeleted() {
			return handler(*key, nil)
		}
		return handler(*key, nRsc)
	}
	return t.doWatch(ctx, prefix, kvHandler)
}

func (t hubImpl) CountAll(owner string, rscType RscType) int {
	if owner == "" {
		stdlog.Errorf("owner is necessary while listing resources")
		return -1
	}
	if _, ok := rscTypes[rscType]; !ok {
		stdlog.Errorf("invalid resource type: %s", rscType)
		return -1
	}
	prefix := KeyPrefixByType(owner, rscType)
	count := 0
	err := t.db.View(func(txn *badger.Txn) error {
		// parse and check resources type

		// get iterator
		opts := badger.DefaultIteratorOptions
		opts.Prefix = []byte(prefix)
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			bs, err := it.Item().ValueCopy(nil)
			if err != nil {
				return err
			}
			if bs == nil {
				continue
			}
			count++
		}
		return nil
	})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to view hub databases")
		return -1
	}
	return count
}

func (t hubImpl) Count(owner string, rscType RscType, r ThingerResource, selectors ...ResourceSelector) int {
	if owner == "" {
		stdlog.Errorf("owner is necessary while listing resources")
		return -1
	}
	if _, ok := rscTypes[rscType]; !ok {
		stdlog.Errorf("invalid resource type: %s", rscType)
		return -1
	}
	prefix := KeyPrefixByType(owner, rscType)
	selector := JoinSelectorsByAnd(selectors...)
	rt := reflect.TypeOf(r)
	if rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}
	count := 0
	err := t.db.View(func(txn *badger.Txn) error {
		// parse and check resources type

		// get iterator
		opts := badger.DefaultIteratorOptions
		opts.Prefix = []byte(prefix)
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			bs, err := it.Item().ValueCopy(nil)
			if err != nil {
				return err
			}
			if bs == nil {
				continue
			}
			rv := reflect.New(rt)
			r := rv.Interface().(ThingerResource)
			if err := r.SetValue(bs); err != nil {
				return setValueErr(err, bs)
			}
			if r.IsDeleted() {
				continue
			}
			if selector != nil && !selector(r) {
				continue
			}

			count++

		}
		return nil
	})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to view hub databases")
		return -1
	}
	return count
}

// TODO how to set delete millisecond 反射?
func (t hubImpl) Delete(k RscKey) error {
	if err := k.Valid(); err != nil {
		return err
	}
	return t.RawDelete(k.KeyBytes())
}

func (t hubImpl) RawDelete(k []byte) error {
	return t.db.Update(func(txn *badger.Txn) error {
		return txn.Delete(k)
	})
}

func (t hubImpl) Save(r ThingerResource) error {
	if err := r.Key().Valid(); err != nil {
		return err
	}
	r.SetUpdateTime()

	return t.SaveEntry(badger.NewEntry(r.KeyBytes(), r.Value()))
}

func (t hubImpl) SaveEntry(entry *badger.Entry) error {
	return t.db.Update(func(txn *badger.Txn) error {
		return txn.SetEntry(entry)
	})
}

func (t hubImpl) SaveKV(kv *KV) error {
	return t.SaveEntry(kv2Entry(kv))
}

func (t hubImpl) Set(key, value []byte) error {
	return t.SaveEntry(badger.NewEntry(key, value))
}

// Clean 清除指定节点和类型的所有资源信息
func (t hubImpl) Clean(owner string, rscType RscType) error {
	if owner == "" {
		return fmt.Errorf("owner is necessary while listing resources")
	}
	if _, ok := rscTypes[rscType]; !ok {
		return invalidRscTypeErr(rscType)
	}
	prefix := KeyPrefixByType(owner, rscType)
	return t.db.DropPrefix([]byte(prefix))
}

// type RscEventHandler interface {
//	// OnUpdate 为资源创建/更新成功后触发的处理方法
//	// prev 为更新前的资源, 当操作为创建时, prev 为 nil
//	// curt 为创建/更新后的资源
//	OnSave(prev, curt ThingerResource)
//
//	// OnDelete 为资源删除成功后触发的处理方法
//	OnDelete(ThingerResource)
// }

func (t hubImpl) doWatch(ctx context.Context, prefix string, handle RscKVHandler) error {
	cb := func(kv *badger.KVList) error {
		for _, kv := range kv.Kv {
			if err := handle((*KV)(kv)); err != nil {
				stdlog.WithError(err).Errorf("failed to handle KV while watching db")
				return err
			}
		}
		return nil
	}
	return t.db.Subscribe(ctx, cb, []byte(prefix))
}

type RscKVHandler = func(kv *KV) error
type RscHandler = func(k RscKey, rsc ThingerResource) error

func notfoundErr(err error, k []byte) error {
	if err == nil {
		return nil
	}
	return stderr.ResourceQueryFailure.Cause(err, "failed to get resource '%s'", k)
}

func setValueErr(err error, v []byte) error {
	if err == nil {
		return nil
	}
	return stderr.ResourceQueryFailure.Cause(err, "failed to set resource value '%s'", v)
}

func copyValueErr(err error) error {
	return stderr.Internal.Cause(err, "failed to copy resource value")
}

func invalidRscTypeErr(rscType RscType) error {
	return stderr.ResourceUnsupported.Error(string(rscType))
}

func invalidRscKeyErr(err error, k RscKey) error {
	return stderr.ResourceUnsupported.Cause(err, "invalid resource key : owner='%s', type='%s', id='%s'",
		k.Owner, k.Type, k.ID)
}

func parseTimeFromValue(value []byte) (*RscTimeMixin, error) {
	timeMixin := new(RscTimeMixin)
	if err := json.Unmarshal(value, timeMixin); err != nil {
		return nil, stderr.Internal.Error("failed to unmarshal the time mixin of resource '%s'", value)
	}
	return timeMixin, nil
}

// KeyPrefixByOwner 返回某个边缘节点下属的所有资源信息所公共的前缀
func KeyPrefixByOwner(owner string) string {
	return fmt.Sprintf("%s/%s/", keyPrefix, owner)
}

// KeyPrefixByType 返回某个边缘节点下属的某种类型的所有资源信息所公共的前缀
func KeyPrefixByType(owner string, rscType RscType) string {
	return fmt.Sprintf("%s/%s/%s/", keyPrefix, owner, rscType)
}

// ParseTsFromKV 解析资源操作的时间戳（创建、更新和删除）
func ParseTsFromKV(key, value []byte) (*RscTs, error) {
	timeMix, err := parseTimeFromValue(value)
	if err != nil {
		return nil, err
	}
	return &RscTs{
		Key:       string(key),
		TimeMixin: timeMix,
	}, nil
}
