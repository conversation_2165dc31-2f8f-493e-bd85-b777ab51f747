package stdhub

import "testing"

func TestRscType_HasFlag(t *testing.T) {
	tests := []struct {
		name string
		rt   RscType
		f    RscTypeFlag
		want bool
	}{
		// 	BelongToNode       RscTypeFlag = 1 << iota // 属于边缘端
		//	BelongToHub       RscTypeFlag = 1 << iota // 属于边缘端
		//	NodePubNodeRsc RscTypeFlag = 1 << iota // Node端 是否同步Node 资源的改动
		//	NodePubHubRsc  RscTypeFlag = 1 << iota // Node端 是否同步Hub  资源的改动
		//	HubPubNodeRsc  RscTypeFlag = 1 << iota // Hub端  是否同步Node 资源的改动
		//	HubPubHubRsc   RscTypeFlag = 1 << iota // Hub端  是否同步Hub  资源的改动

		//  RscTypeSceneInstance: BelongToNode | NodePubNodeRsc,
		{
			name: "",
			rt:   RscTypeSceneInstance,
			f:    Rsc,
			want: true,
		}, {
			name: "",
			rt:   RscTypeSceneInstance,
			f:    UpRsc,
			want: false,
		}, {
			name: "",
			rt:   RscTypeSceneInstance,
			f:    DownRsc,
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.rt.HasFlag(tt.f); got != tt.want {
				t.Errorf("HasFlag() = %v, want %v", got, tt.want)
			}
		})
	}
}
