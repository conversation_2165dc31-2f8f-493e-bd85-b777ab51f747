package stdhub

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/dgraph-io/badger"
)

var hub ResourceHub

func init() {
	db, err := badger.Open(badger.DefaultOptions("/tmp/badger"))
	if err != nil {
		panic(any(err))
	}
	hub = hubImpl{
		db: db,
	}
	rscTypes[rscTypeTestRsc] = 0
}

var (
	testOwner = "test-owner"
)

const rscTypeTestRsc RscType = "test_rsc"

type TestRsc struct {
	RscBaseInfo
	F1 string
	F2 bool
	F3 int
}

func (s *TestRsc) SetValue(bs []byte) error {
	return json.Unmarshal(bs, &s)
}
func (s *TestRsc) Value() []byte {
	bs, err := json.Marshal(s)
	if err != nil {
		fmt.Printf("failed unmarshal %e", err)
		return nil
	}
	return bs

}

func Test_hubImpl(t *testing.T) {
	defer hub.Clean(testOwner, rscTypeTestRsc)

	ts := &TestRsc{
		RscBaseInfo: NewRscBaseInfo("test", "TEST", "test_name", "test_desc"),
		F1:          "hehe",
		F2:          false,
		F3:          123,
	}

	// test save
	t.Run("test save resource", func(t *testing.T) {
		err := hub.Save(ts)
		if err != nil {
			t.Fatalf("failed to save: %e", err)
		}
	})

	// test load
	t.Run("test load resource by key", func(t *testing.T) {
		tar := new(TestRsc)
		if err := hub.Load(ts.RscKey, tar); err != nil {
			t.Fatalf("failed to load: %e", err)
		}
		fmt.Printf("%+v", tar)
	})

	// test exist
	t.Run("test exist resource by key", func(t *testing.T) {
		if !hub.Exist(ts.RscKey) {
			t.Fatalf("failed to exist")
		}
	})

	// test delete
	t.Run("test delete resource by key", func(t *testing.T) {
		if err := hub.Delete(ts.RscKey); err != nil {
			t.Fatalf("failed to delete : %e", err)
		}

		if hub.Exist(ts.RscKey) {
			t.Fatalf("failed to delete resource, cause it is still exist")
		}
	})

	// insert 10 resources with prefix
	for i := 0; i < 10; i++ {
		name := "hehe"
		if i%2 == 0 {
			name = "haha"
		}
		err := hub.Save(&TestRsc{
			RscBaseInfo: NewRscBaseInfo(rscTypeTestRsc, testOwner, "test_name", "test_desc"),
			F1:          name,
			F2:          false,
			F3:          123,
		})
		if err != nil {
			t.Fatalf("failed to save resource : %e", err)
		}
	}

	// test list all
	t.Run("test list all resources", func(t *testing.T) {
		all := make([]*TestRsc, 0)
		if err := hub.List(testOwner, rscTypeTestRsc, &all); err != nil {
			t.Fatalf("failed to list all resources: %e", err)
		}
		if len(all) != 10 {
			t.Fatalf("failed to filter resources, expect %d actual %d", 20, len(all))
		}
	})

	// test list by filter
	t.Run("test list resources by filter", func(t *testing.T) {
		res := make([]*TestRsc, 0)
		filter := func(resource ThingerResource) bool {
			return resource.(*TestRsc).F1 == "hehe"
		}

		if err := hub.List(testOwner, rscTypeTestRsc, &res, filter); err != nil {
			t.Fatalf("failed to list resources by filter: %e", err)
		}
		if len(res) != 5 {
			t.Fatalf("failed to filter resources, expect %d actual %d", 5, len(res))
		}
	})

	// test delete all
	t.Run("test delete all reousrces", func(t *testing.T) {
		if err := hub.Clean(testOwner, rscTypeTestRsc); err != nil {
			t.Fatalf("failed to batch delete resources:%e", err)
		}
		all := make([]*TestRsc, 0)
		if err := hub.List(testOwner, rscTypeTestRsc, &all); err != nil {
			t.Fatalf("failed to list all resources: %e", err)
		}
		if len(all) != 0 {
			t.Fatalf("failed to clean all test resources")
		}
	})

	// test get after delete
	t.Run("test get after delete", func(t *testing.T) {
		rsc1 := &TestRsc{
			RscBaseInfo: NewRscBaseInfo(rscTypeTestRsc, testOwner, "test_name", "test_desc"),
			F1:          "a b c",
			F2:          false,
			F3:          123,
		}
		rsc2 := &TestRsc{
			RscBaseInfo: NewRscBaseInfo(rscTypeTestRsc, testOwner, "test_name2", "test_desc2"),
			F1:          "cba",
			F2:          false,
			F3:          123,
		}
		if err := hub.Save(rsc1); err != nil {
			t.Fatalf("failed to save resources %e", err)
		}
		if err := hub.Save(rsc2); err != nil {
			t.Fatalf("failed to save resources %e", err)
		}

		if err := hub.Delete(rsc1.Key()); err != nil {
			t.Fatalf("failed to delete resource")
		}
		all := make([]*TestRsc, 0)
		if err := hub.List(testOwner, rscTypeTestRsc, &all); err != nil {
			t.Fatalf("failed to list all resources: %e", err)
		}
		if len(all) != 1 {
			t.Fatalf("?")
		}
	})
}

func TestBadger(t *testing.T) {
	hub, err := NewResourceHub("/mnt/nfs_share/autocv/vision/badger")
	if err != nil {
		t.Fatal(err)
	}

	h := hub.(hubImpl)
	h.db.PrintHistogram([]byte("/thinger/edge-test"))
	h.db.Size()
	h.db.Flatten(2)
}

func TestDropResourceRevision(t *testing.T) {
	testHub, testPath, err := NewTmpResourceHub("/tmp/test", NewGCLoopOption(false))
	if err != nil {
		t.Fatal(err)
	}
	total := 20     // 写入键值对数量
	frequency := 10 // 每个键值对版本数

	genK := func(j int) []byte {
		return []byte(fmt.Sprintf("%s/%d", keyPrefix, j))
	}
	genV := func(i, j int) []byte {
		return []byte(fmt.Sprintf("%d%d", i, j))
	}
	// 初始化待备份数据库
	for i := 0; i < frequency; i++ {
		t.Logf("saving round i=%d", i)
		for j := 0; j < total; j++ {
			err := testHub.SaveKV(&KV{
				Key:   genK(j),
				Value: genV(i, j),
			})
			if err != nil {
				t.Fatal(err)
			}
		}
	}
	t.Logf("closing testhub")
	if err := testHub.Close(); err != nil {
		t.Fatal(err)
	}
	t.Logf("testhub closed")

	// 清理数据库
	backup, err := DropResourceRevision(testPath)
	if err != nil {
		t.Fatal(err)
	}
	backupDB, err := NewResourceHub(backup, NewGCLoopOption(false))
	if err != nil {
		t.Fatal(err)
	}
	resultDB, err := NewResourceHub(testPath, NewGCLoopOption(false))
	if err != nil {
		t.Fatal(err)
	}
	for j := 0; j < total; j++ {
		k := genK(j)
		v1, err1 := backupDB.Get(k)
		v2, err2 := resultDB.Get(k)
		if err1 != nil || err2 != nil {
			t.Fatal(err1, err2)
		}
		if string(v1) != string(v2) {
			t.Fatalf("v1(%s) != v2(%s)", v1, v2)
		}
		if got, want := string(v1), string(genV(frequency-1, j)); got != want {
			t.Fatalf("got(%s) != want(%s)", v1, v2)
		}
	}
}
