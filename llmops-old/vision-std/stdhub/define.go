package stdhub

//type ResourceHubApi interface {
//	/* only show non-deleted resources */
//	GetResource(edgeId string, resType ResourceType, valueId string) (*Resource, error)
//	ListResources(edgeId string, resType ResourceType) ([]*Resource, error)
//	QueryResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error)
//
//	/* watch for all changes, include the ones editor himself made */
//	WatchResource(edgeId string, resType ResourceType) *ResourceWatcher
//	SaveResource(r *Resource) (*Resource, error)
//
//	/* soft-deletion: mark version=0 */
//	DeleteResource(edgeId string, resType ResourceType, valueId string) (*Resource, error)
//	DeleteResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error)
//
//	/* hard-delete: remove from database */
//	PurgeResource(edgeId string, resType ResourceType, valueId string) (*Resource, error)
//	PurgeResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error)
//
//	/* some helper functions to list available infos */
//	ListOwners() ([]string, error)
//	ListResourceKeys(edgeId string, resType ResourceType) ([]string, error)
//	ExistResource(edgeId string, resType ResourceType, valueId string) (bool, error)
//	ChownResource(fromEdgeId, toEdgeId string) (*MigrateResourceResult, error)
//
//	/* used during edge-cloud sync process */
//	GetResourceSafe(edgeId string, resType ResourceType, valueId string) (*Resource, error)
//	GetResourceTs(edgeId string, resType ResourceType, valueId string) (*RscTs, error)
//	ListResourceTs(edgeId string, resType ResourceType) ([]*RscTs, error)
//
//	Editor() string
//	Close() error
//}

type MigrateResourceResult struct {
	Origin []string `json:"orgin,omitempty"`
	New    []string `json:"new,omitempty"`
	Del    []string `json:"del,omitempty"`
}

type RscTs struct {
	Key       string        `json:"key"`
	TimeMixin *RscTimeMixin `json:"time_mixin"`
}
