package stdhub

import (
	"github.com/dgraph-io/badger"
	"github.com/dgraph-io/badger/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type KVMetaFlag = byte

const (
	FlagEditByRemote KVMetaFlag = 1 << 0
	FlagEditByLocal  KVMetaFlag = 1 << 1
)

// KV 为 badger db 底层使用的数据接口, 包含键值对以及一些额外的描述信息
// 详见 badger@v1.6.2/publisher.go  publishUpdates
// kv.Meta = []byte{entry.UserMeta}
type KV = pb.KV

type KVList = pb.KVList

func kv2Entry(kv *KV) *badger.Entry {
	var userMeta byte
	if len(kv.Meta) > 0 {
		userMeta = kv.Meta[0]
	}
	return &badger.Entry{
		Key:      kv.Key,
		Value:    kv.Value,
		UserMeta: userMeta,
		//ExpiresAt: 0, // 暫未使用
	}
}

// WithFlags 为 KV 键值对添加用户自定义的Flag
func WithFlags(kv *KV, flags ...KVMetaFlag) {
	if len(kv.Meta) == 0 {
		kv.Meta = []byte{0}
	}
	for _, f := range flags {
		// 若设置FlagEditByLocal的标志, 则将其FlagEditByRemote标志为置空
		if f == FlagEditByLocal {
			kv.Meta[0] &= ^FlagEditByRemote
			continue
		}
		// 若设置FlagEditByRemote的标志, 则将其FlagEditByLocal标志为置空
		if f == FlagEditByRemote {
			kv.Meta[0] &= ^FlagEditByLocal
			continue
		}
		kv.Meta[0] |= f
	}
}

// HasFlag 返回给定的KV是否包含指定的MetaFlag
func HasFlag(kv *KV, flag KVMetaFlag) bool {
	if len(kv.Meta) == 0 {
		return false
	}
	return kv.Meta[0]&flag != 0
}

// OnRecvKV 处理接收到的其它节点的 KV 数据库操作，将有效操作同步到本地存储
func OnRecvKV(recvKV *KV, store ResourceHub) {
	//stdlog.Debugf("receive resource: %s", recvKV.Key)
	WithFlags(recvKV, FlagEditByRemote)

	// 对应的资源是否存在, 若不存在则直接将收到的资源写入数据库即可
	prevV, err := store.Get(recvKV.Key)
	if err != nil {
		// 获取资源失败, 打印日志并返回
		switch stderr.Unwrap(err).Code {
		case stderr.ResourceNotFound.Code, stderr.ResourceValueEmpty.Code:
			// 已经删除
			if len(recvKV.Value) == 0 {
				return
			}
		default:
			stdlog.WithError(err).Warnf("failed to get value of %s", recvKV.Key)
		}

		// 资源不存在, 直接将收到的资源写入数据库即可
		if err := store.SaveKV(recvKV); err != nil {
			stdlog.WithError(err).Errorf("failed to save kv %s", recvKV.Key)
		}
		return
	}

	// 接收到删除事件
	if len(recvKV.Value) == 0 {
		rk := NewRscKeyFromString(string(recvKV.Key))
		switch rk.Type {
		case RscTypeCluster:
			// 不同步集群资源的删除事件，由当前集群周期扫描进行删除
		default:
			if err := store.RawDelete(recvKV.Key); err != nil {
				stdlog.WithError(err).Errorf("failed to delete resource by key %s", recvKV.Key)
			}
		}
		return
	}

	// 判断资源内容是否一致, 若一致则无需进行任何操作
	if string(prevV) == string(recvKV.Value) {
		//stdlog.Infof("value is as expected, so do not need to be updated")
		return
	}

	// 资源值不一致, 对比其更新的时间戳
	prevTs, err := ParseTsFromKV(recvKV.Key, prevV)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to parse timestamp from value:'%s'", prevV)
		return
	}
	recvTs, err := ParseTsFromKV(recvKV.Key, recvKV.Value)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to parse timestamp from value:'%s'", recvKV.Value)
		return
	}
	// 接受到的资源时间更早, 则认为该更新已过期, 忽略
	if recvTs.TimeMixin.UpdateTimeMills <= prevTs.TimeMixin.UpdateTimeMills {
		stdlog.Warnf("received a resource with older version, ignore it")
		return
	}

	if err := store.SaveKV(recvKV); err != nil {
		stdlog.WithError(err).Errorf("fail to save kv %s", recvKV.Key)
		return
	}
}
