package stdhub

// ResourceSelector 用于筛选资源
// 返回true时, 表示选择当前资源
// 返回false时, 表示跳过当前资源
type ResourceSelector func(ThingerResource) bool

// JoinSelectorsByAnd 将多个资源过滤器结合起来
// 当其中所有Selector返回True时, 则选择当前资源
func JoinSelectorsByAnd(selectors ...ResourceSelector) ResourceSelector {
	if len(selectors) == 0 {
		return nil
	}
	return func(r ThingerResource) bool {
		for _, selector := range selectors {
			if selector == nil {
				continue
			}
			if !selector(r) {
				return false
			}
		}
		return true
	}
}

// JoinSelectorsByOr 将多个资源过滤器通过或结合起来
// 当其中任意一个Selector返回True时, 则选择当前资源
func JoinSelectorsByOr(selectors ...ResourceSelector) ResourceSelector {
	if len(selectors) == 0 {
		return nil
	}
	return func(r ThingerResource) bool {
		for _, selector := range selectors {
			if selector == nil {
				continue
			}
			if selector(r) {
				return true
			}
		}
		return false
	}
}

var (
	SelectAll = func(r ThingerResource) bool {
		return true
	}
)
