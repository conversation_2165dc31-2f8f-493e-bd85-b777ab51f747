package stdhub

//
//import (
//	"context"
//	"fmt"
//	"strings"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//)
//
//func MockResourceHubClient() ResourceHubApi {
//	cli := &MockResourceHub{
//		resources: make(map[string]*Resource),
//		notify:    make(chan *ResourceEvent),
//		subs:      make(map[string]*ResourceWatcher),
//		revision:  0,
//		editor:    "mock",
//	}
//	go cli.runNotifyLoop()
//	return cli
//}
//
//type MockResourceHub struct {
//	resources map[string]*Resource
//	notify    chan *ResourceEvent
//	subs      map[string]*ResourceWatcher
//	revision  int64
//	editor    string
//}
//
//func (h *MockResourceHub) Editor() string {
//	return h.editor
//}
//
//func (h *MockResourceHub) ChownResource(fromEdgeId, toEdgeId string) (*MigrateResourceResult, error) {
//	result := new(MigrateResourceResult)
//	newResources := make(map[string]*Resource)
//	for k, r := range h.resources {
//		result.Origin = append(result.Origin, k)
//		if r.Owner == fromEdgeId {
//			r.Owner = toEdgeId
//			newResources[r.Key()] = r
//			result.New = append(result.New, r.Key())
//			delete(h.resources, k)
//			result.Del = append(result.Del, k)
//		}
//	}
//	return result, nil
//}
//
//func (h *MockResourceHub) GetResourceTs(edgeId string, resType ResourceType, valueId string) (*RscTs, error) {
//	key := NewResourceKey(edgeId, resType, valueId)
//	if result, ok := h.resources[key]; ok {
//		return &RscTs{result.Key(), result.Version, result.Timestamp}, nil
//	} else {
//		return nil, stderr.ResourceNotFound.Error("key: %s", key)
//	}
//}
//
//func (h *MockResourceHub) ListResourceTs(edgeId string, resType ResourceType) ([]*RscTs, error) {
//	keyPrefix := NewResourceKey(edgeId, resType, ResourceValueIdAny)
//	result := make([]*RscTs, 0)
//	for key, r := range h.resources {
//		if strings.HasPrefix(key, keyPrefix) {
//			result = append(result, &RscTs{key, r.Version, r.Timestamp})
//		}
//	}
//	return result, nil
//}
//
//func (h *MockResourceHub) ExistResource(edgeId string, resType ResourceType, valueId string) (bool, error) {
//	result := h.resources[NewResourceKey(edgeId, resType, valueId)]
//	return result != nil && result.Version != 0, nil
//}
//
//func (h *MockResourceHub) GetResource(edgeId string, resType ResourceType, valueId string) (*Resource, error) {
//	key := NewResourceKey(edgeId, resType, valueId)
//	r, ok := h.resources[key]
//	if !ok || r.Value == nil {
//		return nil, stderr.NotFound.Error("resource '%s'", key)
//	}
//	return r, nil
//}
//
//func (h *MockResourceHub) GetResourceSafe(edgeId string, resType ResourceType, valueId string) (*Resource, error) {
//	key := NewResourceKey(edgeId, resType, valueId)
//	r, ok := h.resources[key]
//	if !ok {
//		return nil, stderr.NotFound.Error("resource '%s'", key)
//	}
//	return r, nil
//}
//
//func (h *MockResourceHub) ListResources(egeId string, resType ResourceType) ([]*Resource, error) {
//	return h.QueryResources(egeId, resType, ResourceValueIdAny)
//}
//
//func (h *MockResourceHub) QueryResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error) {
//	keyPrefix := NewResourceKey(edgeId, resType, valueIdPrefix)
//	rs := make([]*Resource, 0)
//	for key, r := range h.resources {
//		if strings.HasPrefix(key, keyPrefix) {
//			if r.Value != nil {
//				rs = append(rs, r)
//			}
//		}
//	}
//	return rs, nil
//}
//
//func (h *MockResourceHub) ListOwners() ([]string, error) {
//	owners := make([]string, 0)
//	pattern := fmt.Sprintf("/%s/", ResourceType_Node_EDGE.String())
//	for k, _ := range h.resources {
//		if strings.Contains(k, pattern) {
//			owner, _, _ := SplitResourceKey(k)
//			owners = append(owners, owner)
//		}
//	}
//	return owners, nil
//}
//
//func (h *MockResourceHub) ListResourceKeys(edgeId string, resType ResourceType) ([]string, error) {
//	keyPrefix := NewResourceKey(edgeId, resType, "")
//	ks := make([]string, 0)
//	for key, _ := range h.resources {
//		if strings.HasPrefix(key, keyPrefix) {
//			ks = append(ks, key)
//		}
//	}
//	return ks, nil
//}
//
//func (h *MockResourceHub) SaveResource(r *Resource) (*Resource, error) {
//	if r.Editor == "" {
//		r.Editor = h.editor
//	}
//	key := r.Key()
//	prevR, ok := h.resources[key]
//	h.resources[r.Key()] = r
//	h.revision += 1
//	if ok {
//		h.notify <- &ResourceEvent{key, EventType_MODIFY, r.Editor, r, prevR, nil}
//	} else {
//		h.notify <- &ResourceEvent{key, EventType_CREATE, r.Editor, r, prevR, nil}
//	}
//	return prevR, nil
//}
//
//func (h *MockResourceHub) DeleteResource(edgeId string, resType ResourceType, valueId string) (*Resource, error) {
//	key := NewResourceKey(edgeId, resType, valueId)
//	prevR, ok := h.resources[key]
//	if ok {
//		r := ResourceBuilder.NewDeletion(prevR)
//		r.Editor = h.editor
//		h.resources[key] = r
//		h.revision += 1
//		h.notify <- &ResourceEvent{key, EventType_DELETE, r.Editor, r, prevR, nil}
//	}
//	return prevR, nil
//}
//
//func (h *MockResourceHub) DeleteResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error) {
//	rs := make([]*Resource, 0)
//	for key, prevR := range h.resources {
//		if strings.HasPrefix(key, valueIdPrefix) {
//			rs = append(rs, prevR)
//			r := ResourceBuilder.NewDeletion(prevR)
//			r.Editor = h.editor
//			h.resources[key] = r
//			h.revision += 1
//			h.notify <- &ResourceEvent{key, EventType_PURGE, r.Editor, r, prevR, nil}
//		}
//	}
//	return rs, nil
//}
//
//func (h *MockResourceHub) PurgeResource(edgeId string, resType ResourceType, valueId string) (*Resource, error) {
//	key := NewResourceKey(edgeId, resType, valueId)
//	prevR, ok := h.resources[key]
//	if ok {
//		delete(h.resources, key)
//		h.revision += 1
//		h.notify <- &ResourceEvent{key, EventType_PURGE, "", nil, prevR, nil}
//	}
//	return prevR, nil
//}
//
//func (h *MockResourceHub) PurgeResources(edgeId string, resType ResourceType, valueIdPrefix string) ([]*Resource, error) {
//	rs := make([]*Resource, 0)
//	for key, r := range h.resources {
//		if strings.HasPrefix(key, valueIdPrefix) {
//			rs = append(rs, r)
//			delete(h.resources, key)
//			h.revision += 1
//			h.notify <- &ResourceEvent{key, EventType_PURGE, "", nil, r, nil}
//		}
//	}
//	return rs, nil
//}
//
//func (h *MockResourceHub) WatchResource(edgeId string, resType ResourceType) *ResourceWatcher {
//	keyPrefix := NewResourceKey(edgeId, resType, "")
//	result := NewResourceWatcher(context.Background(), keyPrefix)
//	h.subs[keyPrefix] = result
//	return result
//}
//
//func (h *MockResourceHub) Close() error {
//	return nil
//}
//
//func (h *MockResourceHub) runNotifyLoop() {
//	for re := range h.notify {
//		for keyPrefix, watcher := range h.subs {
//			if strings.HasPrefix(re.Key, keyPrefix) {
//				watcher.EventQueue <- re
//			}
//		}
//	}
//}
