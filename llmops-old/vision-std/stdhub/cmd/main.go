package main

import (
	"flag"
	"fmt"
	"github.com/sirupsen/logrus"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
)

const (
	list    = "list"
	get     = "get"
	del     = "del"
	clean   = "clean"
	count   = "count"
	merge   = "merge"
	clone   = "clone"
	flatten = "flatten"
)

var (
	owner     string
	opt       string
	dbPath    string
	rscType   string
	prefix    string
	withValue bool

	opts = map[string]struct{}{
		list:    {},
		get:     {},
		del:     {},
		clean:   {},
		count:   {},
		merge:   {},
		clone:   {},
		flatten: {},
	}
)

func main() {
	flag.StringVar(&opt, "opt", "list", "数据库操作")
	flag.StringVar(&owner, "owner", "cloud", "指定资源的所属节点ID")
	flag.StringVar(&dbPath, "db-path", "badger", "badger数据库所在目录")
	flag.StringVar(&rscType, "type", "", "资源数据类型")
	flag.StringVar(&prefix, "prefix", "/", "资源键值前缀")
	flag.BoolVar(&withValue, "with-value", false, "是否输出资源的具体内容")
	flag.Parse()
	flag.PrintDefaults()
	args := flag.Args()

	fmt.Printf("%+v\n", args)
	fmt.Printf("%+v\n", flag.NArg())
	fmt.Printf("owner: %s\n", owner)
	fmt.Printf("dbPath: %s\n", dbPath)
	fmt.Printf("rscType: %s\n", rscType)
	fmt.Printf("prefix: %s\n", prefix)
	fmt.Printf("withValue: %v\n", withValue)
	//if len(args) == 0 {
	//	fmt.Printf("请指定数据库操作, 可选:%+v\n", opts)
	//	return
	//}
	//opt := args[0]
	if _, ok := opts[opt]; !ok {
		fmt.Printf("非法的数据库操作 %s, 可选:%+v\n", opt, opts)
		return
	}

	hub, err := stdhub.NewResourceHub(dbPath)
	if err != nil {
		logrus.WithError(err).Fatalf("打开数据库失败")
		return
	}

	switch opt {
	case list:
		if prefix == "/" {
			prefix = "/thinger/" + owner + "/" + rscType
		}
		kvs, err := hub.ListKVs(prefix)
		if err != nil {
			logrus.WithError(err).Fatalf("获取数据库资源失败")
			return
		}
		for _, kv := range kvs {
			println(string(kv.Key))
			if withValue {
				println(string(kv.Value))
			}
		}
		println("===\nPrefix:", prefix, "\n资源总数:", len(kvs))

	case clean:
		if rscType == "" {
			println("清理系统资源时必须指定资源类型")
			return
		}
		if err := hub.Clean(owner, stdhub.RscType(rscType)); err != nil {
			logrus.WithError(err).Fatalf("清理系统资源失败")
			return
		}
	case count:
		for _, rt := range stdhub.GetAllResourceTypes() {
			println("===\nType:", rt, "\n资源总数:", hub.CountAll(owner, rt))
		}
	case clone:
		kvs, err := hub.ListKVs(prefix)
		if err != nil {
			logrus.WithError(err).Fatalf("failed to list kvs with prefix '%s'", prefix)
			return
		}
		target := fmt.Sprintf("./Temp_Resource_Hub_%d", time.Now().UnixNano())
		tmpHub, err := stdhub.NewResourceHub(target)
		if err != nil {
			logrus.WithError(err).Fatalf("failed to new tmp resource hub")
			return
		}
		logrus.Infof("cloning %d kvs into new tmp resource hub %s", len(kvs), target)
		for i, kv := range kvs {
			if err := tmpHub.SaveKV(kv); err != nil {
				logrus.WithError(err).Fatalf("failed to write kv into tmp hub")
				return
			}
			if i%50 == 0 {
				logrus.Infof("completed %d", i)
			}
		}
		logrus.Infof("cloning %d kvs into resource hub %s done", len(kvs), target)
	}
}
