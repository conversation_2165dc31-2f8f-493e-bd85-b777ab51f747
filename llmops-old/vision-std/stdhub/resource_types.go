package stdhub

import "errors"

type RscType string
type Namespace = string
type RscTypeFlag byte

const (
	RscTypeCluster         RscType = "cluster"          // 边缘集群
	RscTypeProduct         RscType = "product"          // 设备产品
	RscTypeRemoteService   RscType = "remote_service"   // 服务接口
	RscTypeDevice          RscType = "device"           // 设备
	RscTypeSceneInstance   RscType = "scene_instance"   // 场景实例
	RscTypeModel           RscType = "model"            // 模型
	RscTypeModelDeployment RscType = "model_deployment" // 一个模型具体版本的部署信息,(参数配置,控制启停,自动部署等信息)
	RscTypeDataTask        RscType = "data_task"        // 数据上传或清理任务
	RscTypeModelEvaluation RscType = "model_evaluation" // 模型评估
	RscTypeScene           RscType = "scene"            // 场景模板
	RscTypeSceneRelease    RscType = "scene_release"    // 场景版本
	RscTypeUser            RscType = "user"             // 用户信息
	RscTypeUserRole        RscType = "user_role"        // 用户角色
)

const (
	Rsc     RscTypeFlag = 1 << iota
	UpRsc   RscTypeFlag = 1 << iota // 当前集群是否需要将资源向上级集群同步
	DownRsc RscTypeFlag = 1 << iota // 当前集群是否需要将资源向下级集群同步
)

var rscTypes = map[RscType]RscTypeFlag{
	RscTypeUser:            Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeUserRole:        Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeProduct:         Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeDevice:          Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeModel:           Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeModelDeployment: Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeRemoteService:   Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeModelEvaluation: Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeScene:           Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeSceneRelease:    Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeSceneInstance:   Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeDataTask:        Rsc,                   // 仅在当前集群存在，不需要同步
	RscTypeCluster:         Rsc | UpRsc | DownRsc, // 上级集群会向下级集群同步该资源，下级集群也会向上级集群同步该资源
}

func GetAllResourceTypes() []RscType {
	types := make([]RscType, 0, len(rscTypes))
	for rt := range rscTypes {
		types = append(types, rt)
	}
	return types
}

func RegisterCustomResource(rscType RscType, flag RscTypeFlag) error {
	if _, ok := rscTypes[rscType]; ok {
		return errors.New("resource type already exist: " + string(rscType))
	}

	rscTypes[rscType] = Rsc
	return nil
}

func (rt RscType) HasFlag(f RscTypeFlag) bool {
	flag := rscTypes[rt]
	return flag&f != 0
}
