package stdmetric

import (
	"fmt"
	"strings"

	"k8s.io/apimachinery/pkg/util/validation"
)

type ResourceType string

type Labels map[string]string
type Annotations map[string]string

const (
	ResourceTypeService  ResourceType = "service"
	ResourceTypeTask     ResourceType = "task"
	ResourceTypeCode     ResourceType = "code"
	ResourceTypeTraining ResourceType = "training"
)

const (
	MonitoredPrefix = "llmops-monitored/"
	MonitoredId     = MonitoredPrefix + "id"
	MonitoredName   = MonitoredPrefix + "name"
	MonitoredType   = MonitoredPrefix + "type"
	MonitoredProjId = MonitoredPrefix + "project_id"
)

const (
	LabelPrefix = "ref_"
	LabelId     = LabelPrefix + "id"
	LabelName   = LabelPrefix + "name"
	LabelType   = LabelPrefix + "type"
	LabelProjId = LabelPrefix + "project_id"
)

func WithMonitorKeys(labels Labels, annos Annotations, resourceType ResourceType, id, name, projId string, customLabels ...string) (Labels, Annotations, error) {
	if labels == nil {
		labels = make(map[string]string)
	}
	if annos == nil {
		annos = make(map[string]string)
	}

	if resourceType == "" {
		return nil, nil, fmt.Errorf("resourceType is empty")
	}
	if val := validation.IsValidLabelValue(string(resourceType)); len(val) > 0 {
		return nil, nil, fmt.Errorf("resourceType [%s] is invalid: %s", resourceType, val)
	}

	if id == "" {
		return nil, nil, fmt.Errorf("id is empty")
	}
	if val := validation.IsValidLabelValue(id); len(val) > 0 {
		return nil, nil, fmt.Errorf("id [%s] is invalid: %s", id, val)
	}

	if projId == "" {
		return nil, nil, fmt.Errorf("projId is empty")
	}
	if val := validation.IsValidLabelValue(projId); len(val) > 0 {
		return nil, nil, fmt.Errorf("projId [%s] is invalid: %s", projId, val)
	}

	if name == "" {
		return nil, nil, fmt.Errorf("name is empty")
	}
	labels[MonitoredId] = id
	labels[MonitoredType] = string(resourceType)
	labels[MonitoredProjId] = projId

	annos[MonitoredId] = id
	annos[MonitoredType] = string(resourceType)
	annos[MonitoredProjId] = projId
	annos[MonitoredName] = name

	// custom labels
	if len(customLabels)%2 != 0 {
		return nil, nil, fmt.Errorf("len of customLabels must be even")
	}
	for i := 0; i < len(customLabels); i += 2 {
		k := MonitoredPrefix + customLabels[i]
		if val := validation.IsQualifiedName(k); len(val) > 0 {
			return nil, nil, fmt.Errorf("custom label name [%s] is invalid: %s", k, val)
		}
		annos[k] = customLabels[i+1]
	}

	return labels, annos, nil
}

// PodShortName returns the short suffix name of a pod.
// if podName contains '-', it will return the last part of the name.
// it returns at most 8 characters on the tail end, if the podName is longer than 8 characters, it will be truncated.
func PodShortName(podName string) string {
	i := strings.LastIndex(podName, "-")
	if i != -1 {
		podName = podName[i+1:]
	}

	if len(podName) > 8 {
		podName = podName[len(podName)-8:]
	}
	return podName
}
