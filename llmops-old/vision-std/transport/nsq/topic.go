package nsq

// 有效的话题（topic)名必须是字符[.a-zA-Z0-9_-] 和数字
type Topic string
type Channel string

const (
	workerStats  Topic = "WORKER_STATS"
	stdStatus    Topic = "STD_STATUS"
	stdIndicator Topic = "STD_INDICATOR"
	notification Topic = "notification"

	GatewayChannel Channel = "gateway"
)

func NewWorkerStatsTopic() Topic {
	return workerStats
}

func NewStdStatusTopic() Topic {
	return stdStatus
}

func NewNotificationTopic() Topic {
	return notification
}

func NewStatsIndicatorTopic() Topic {
	return stdIndicator
}

func (t Topic) String() string {
	return string(t)
}

func (c Channel) String() string {
	return string(c)
}
