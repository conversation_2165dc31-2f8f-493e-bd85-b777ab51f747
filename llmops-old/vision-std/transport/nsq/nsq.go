package nsq

import (
	"errors"
	"fmt"
	"github.com/nsqio/go-nsq"
	"time"
)

type Producer = nsq.Producer
type Consumer = nsq.Consumer

const DefaultTimeout = time.Second * 5

func CreateProducer(nsqdAddress string) (*Producer, error) {
	cfg := nsq.NewConfig()
	cfg.DialTimeout = DefaultTimeout
	cfg.WriteTimeout = DefaultTimeout
	return nsq.NewProducer(nsqdAddress, cfg)
}

func CreateConsumer(topicName, channelName string, handler nsq.Handler) (*Consumer, error) {
	config := nsq.NewConfig()
	config.LookupdPollInterval = 10 * time.Second

	consumer, err := nsq.NewConsumer(topicName, channelName, config)
	if err != nil {
		return nil, err
	}

	consumer.AddHandler(handler)
	consumer.ChangeMaxInFlight(2000)

	return consumer, nil
}

func CreateNSQConsumer(topicName, channelName, nsqdAddress string, lookupAddressList []string,
	messageHandler nsq.Handler) (*Consumer, error) {
	configObj := nsq.NewConfig()
	consumerInstance, err := nsq.NewConsumer(topicName, channelName, configObj)
	if err != nil {
		content := fmt.Sprintf("CreateNSQConsumer NewConsumer err:%v topic:%s channel:%s",
			err, topicName, channelName)
		return nil, errors.New(content)
	}
	consumerInstance.AddHandler(messageHandler)
	consumerInstance.ChangeMaxInFlight(2000) // TODO https://github.com/nsqio/go-nsq/issues/179
	if "" != nsqdAddress {
		err = consumerInstance.ConnectToNSQD(nsqdAddress)
		if nil != err {
			content := fmt.Sprintf("CreateNSQConsumer ConnectToNSQD err:%v topic:%s channel:%s",
				err, topicName, channelName)
			return nil, errors.New(content)
		}
		return consumerInstance, nil
	}
	if err := consumerInstance.ConnectToNSQLookupds(lookupAddressList); err != nil {
		content := fmt.Sprintf("CreateNSQConsumer ConnectToNSQLookupds err:%v topic:%s channel:%s",
			err, topicName, channelName)
		return nil, errors.New(content)
	}
	return consumerInstance, nil
}
