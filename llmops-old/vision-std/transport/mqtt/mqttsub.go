package mqtt

import (
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type MQTTSub struct {
	c   MQTTClient
	out chan *Msg
}

func NewMQTTSub(config conf.MqttConfig, out chan *Msg) *MQTTSub {
	return &MQTTSub{
		out: out,
		c:   NewMQTTClient(config),
	}
}

func (this MQTTSub) Start() error {
	if err := this.c.Start(); err != nil {
		return err
	}
	topics := strings.Split(this.c.GetConfig().SubTopic, ",")
	stdlog.Infof("doSub %+v start", topics)
	if err := this.c.SubWithChannel(this.out, topics...); err != nil {
		stdlog.WithError(err).Error("MQTTSub Subscribe Error, topics: " + this.c.GetConfig().SubTopic)
		return err
	}
	return nil
}

func (this MQTTSub) Stop() {
	close(this.out)
	this.c.Stop()
}
