package mqtt

import (
	"fmt"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

func TestSub(t *testing.T) {
	cli := NewMQTTClient(conf.MqttConfig{
		BrokerAddr: "localhost:1883",
		ClientId:   "test-pub-cli",
	})
	if err := cli.Start(); err != nil {
		panic(err)
	}

	if err := cli.Sub(func(msg *Msg) {
		println("received topic: ", msg.Topic)
	}, "test-topic"); err != nil {
		panic(err)
	}
	println("start success")
	time.Sleep(time.Minute * 3)
}

func TestPub(t *testing.T) {
	cli := NewMQTTClient(conf.MqttConfig{
		BrokerAddr: "localhost:1883",
		ClientId:   "test-pub-cli",
	})
	if err := cli.Start(); err != nil {
		panic(err)
	}

	ticker := time.NewTicker(time.Second * 5)
	for {
		<-ticker.C
		fmt.Printf("\npub msg")
		err := cli.Pub(&Msg{

			Topic:   "test-topic",
			Payload: []byte("hahahahaha"),
		})
		if err != nil {
			panic(err)
		}
	}
}
