package mqtt

import (
	"fmt"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	Alert        TopicType = "ALERT"
	AlertUp      TopicType = "ALERT_UP"
	Midmsg       TopicType = "MIDMSG"
	Display      TopicType = "DISPLAY"
	DeviceData   TopicType = "DATA"
	TaskStatus   TopicType = "TASK"
	StdStatus    TopicType = "STATUS"
	StdIndicator TopicType = "INDICATOR"

	TopicSep = "/"

	Time       TopicKey = "time"        // 落库时间
	NodeID     TopicKey = "node_id"     // 规则实例数据展示算子定义的NodeID
	DataID     TopicKey = "data_id"     // 设备数据(属性,时间,服务)的ID
	OptType    TopicKey = "opt_type"    // 设备数据类型
	MidmsgID   TopicKey = "msg_id"      // 中间消息的MsgID
	InstanceID TopicKey = "instance"    // 实例ID
	PipelineID TopicKey = "pipeline"    // 规则ID
	ProtocolID TopicKey = "protocol_id" // 协议ID
	ProductID  TopicKey = "product_id"  // 产品ID
	DeviceID   TopicKey = "device_id"   // 设备ID
	RefType    TopicKey = "ref_type"    // 相关资源类型
	RefID      TopicKey = "ref_id"      // 相关资源ID
	EdgeID     TopicKey = "edge_id"     // 资源所在边缘节点的ID
)

// TopicKey是MQTT消息的Topic中的特定字段, 依据不同的Topic其具备的TopicKey也不同
// 每个TopicKey都有其固定的位置, 不同的Key之间使用分隔符进行分割.
// 当存储mqtt消息至InfluxDB时, 每个TopicKey 对应一个 TagKey
type TopicKey = string

var TopicKeys = map[TopicType][]TopicKey{
	// 告警主题, 使用统一数据结构
	// ALERT/{instance_id}
	Alert: {InstanceID},

	// 数据展示主题, 用以传输实例数据展示算子输出的消息
	// DISPLAY/{instance_id}/{node_id}
	Display: {InstanceID, NodeID},

	// 中间消息主题, 用以传输规则实例间的中间消息, 从而联通多媒体与时序规则
	// MIDMSG/{pipeline_id}/{midmsg_id}/{instance_id}
	Midmsg: {PipelineID, MidmsgID, InstanceID},

	// 设备消息主题, 用以传输设备上报的数据以及用户下发的指令等
	// DATA/{product_id}/{device_id}/{opt_type}/{data_id}
	DeviceData: {ProductID, DeviceID, OptType, DataID},

	// 实例状态主题，用以传输规则实例对应容器的运行状态
	// TASK/{instance_id}
	TaskStatus: {InstanceID},

	// 状态更新主题，用于更新通用资源的最新状态
	// STATUS/{edge_id}/{ref_type}/{ref_id}
	StdStatus: {EdgeID, RefType, RefID},

	// 统计指标更新主题，用于更新通用资源的最新统计指标
	// INDICATOR/{edge_id}/{ref_type}/{ref_id}
	StdIndicator: {EdgeID, RefType, RefID},
}

type TopicType string

type DeviceOptType string

type Topic interface {
	String() string
	Measurement() string
	Tags() map[TopicKey]string
	GetValue(TopicKey) string
	Type() TopicType
}

type commonTopic struct {
	topicType TopicType
	tags      map[TopicKey]string
}

func NewTopic(topic string) (Topic, error) {
	ts := strings.Split(topic, TopicSep)
	if len(ts) == 0 {
		return nil, stderr.InvalidParam.Error("could not handle mqtt topic : %q", topic)
	}
	topicType := TopicType(ts[0])
	keys, ok := TopicKeys[topicType]
	if !ok {
		return nil, stderr.InvalidParam.Error("undefined mqtt topic : %q", topic)
	}
	if len(ts)-1 != len(keys) {
		return nil, stderr.InvalidParam.Error("invalid mqtt topic : %q, keys [%+v] are necessary", topic, keys)
	}
	tags := make(map[TopicKey]string)
	for i, key := range keys {
		tags[key] = ts[i+1]
	}
	return &commonTopic{
		topicType: topicType,
		tags:      tags,
	}, nil
}

func NewAlertTopic(instanceId string) Topic {
	return &commonTopic{
		topicType: Alert,
		tags: map[TopicKey]string{
			InstanceID: instanceId,
		},
	}
}

func NewDisplayTopic(instanceId, nodeId string) Topic {
	return &commonTopic{
		topicType: Display,
		tags: map[TopicKey]string{
			InstanceID: instanceId,
			NodeID:     nodeId,
		},
	}
}

func NewMidmsgTopic(pipelineId, msgId, instanceId string) Topic {
	return &commonTopic{
		topicType: Midmsg,
		tags: map[TopicKey]string{
			InstanceID: instanceId,
			PipelineID: pipelineId,
			MidmsgID:   msgId,
		},
	}
}

func NewDeviceDataTopic(productID, deviceID, optType, dataID string) Topic {
	return &commonTopic{
		topicType: DeviceData,
		tags: map[TopicKey]string{
			DataID:    dataID,
			OptType:   optType,
			DeviceID:  deviceID,
			ProductID: productID,
		},
	}
}

func NewTaskStatusTopic(instanceID string) Topic {
	return &commonTopic{
		topicType: TaskStatus,
		tags: map[TopicKey]string{
			InstanceID: instanceID,
		},
	}
}

func NewStatsIndicatorsTopic(edgeID, refID string, refType string) Topic {
	return &commonTopic{
		topicType: StdIndicator,
		tags: map[TopicKey]string{
			EdgeID:  edgeID,
			RefID:   refID,
			RefType: refType,
		},
	}
}

func NewStdStatusTopic(edgeID, refID string, refType string) Topic {
	return &commonTopic{
		topicType: StdStatus,
		tags: map[TopicKey]string{
			EdgeID:  edgeID,
			RefID:   refID,
			RefType: refType,
		},
	}
}

func (c *commonTopic) Type() TopicType {
	return c.topicType
}

func (c *commonTopic) String() string {
	keys := TopicKeys[c.topicType]
	values := make([]string, len(keys)+1)
	values[0] = string(c.topicType)
	for i, key := range keys {
		values[i+1] = c.tags[key]
	}
	return strings.Join(values, TopicSep)
}

func (c *commonTopic) Measurement() string {
	switch c.topicType {
	case Midmsg:
		return fmt.Sprintf("%s_%s", string(Midmsg), c.tags[PipelineID])
	case Display:
		return fmt.Sprintf("%s_%s", string(Display), c.tags[NodeID])
	case DeviceData:
		return c.tags[ProductID]
	default:
		return string(c.topicType)
	}
}

func (c *commonTopic) Tags() map[TopicKey]string {
	return c.tags
}

func (c *commonTopic) GetValue(key TopicKey) string {
	return c.tags[key]
}

// Topic returns the topic that could be used to subscribe all messages of this type
func (t TopicType) Topic() string {
	return string(t) + "/#"
}

func (t TopicType) String() string {
	return string(t)
}
