package redis

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/transport"
	"transwarp.io/applied-ai/aiot/vision-std/transport/mqtt"
)

type redisMqClient struct {
	redisCli clients.RedisClient
}

func NewRedisMqClient(redisCli clients.RedisClient) (transport.MqClient, error) {
	return &redisMqClient{redisCli: redisCli}, nil
}

func NewRedisMqClientV2(redisCfg conf.RedisConfig) (transport.MqClient, error) {
	redisCli, err := clients.NewRedisClient(redisCfg)
	if err != nil {
		return nil, err
	}
	return &redisMqClient{redisCli: redisCli}, nil
}

//func (c *redisMqClient) Pub(msg *mqtt.Msg) error {
//	valStr := stdsrv.AnyToString(msg)
//	return c.redisCli.Publish(msg.Topic, valStr)
//}
//func (c *redisMqClient) Sub(handler mqtt.MsgHandler, topics ...string) error {
//	sub := c.redisCli.Subscribe(topics...)
//	defer sub.Close()
//	ch := sub.Channel()
//	for oriMsg := range ch {
//		msg := new(mqtt.Msg)
//		if err := stdsrv.StringToAny(oriMsg.Payload, msg); err != nil {
//			return err
//		}
//		handler(msg)
//	}
//	return nil
//}

func (c *redisMqClient) Pub(msg *mqtt.Msg) error {
	valStr := stdsrv.AnyToString(msg)
	return c.redisCli.LPush(msg.Topic, valStr)
}
func (c *redisMqClient) Sub(handler mqtt.MsgHandler, topics ...string) error {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				stderr.Errorf("panic happened: +%v", r)
			}
		}()
		for {
			oriMsg, err := c.redisCli.GetRdb().BRPop(context.Background(), 0*time.Second, topics...).Result()
			if err != nil {
				stderr.Errorf("failed to do bRPop ", err)
				return
			}
			msg := new(mqtt.Msg)
			if err := stdsrv.StringToAny(oriMsg[1], msg); err != nil {
				stderr.Errorf("invalid msg ", err)
				return
			}
			handler(msg)
		}
	}()
	return nil
}
