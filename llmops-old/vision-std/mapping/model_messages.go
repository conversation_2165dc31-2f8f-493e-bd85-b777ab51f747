package mapping

import (
	"bufio"
	"bytes"
	"github.com/google/uuid"
	"regexp"
	"strings"
)

type RoleType int8

const (
	RoleTypeSystem RoleType = iota
	RoleTypeUser
	RoleTypeAssistant
)

var RoleTypeNames = map[RoleType]string{
	RoleTypeSystem:    "System",
	RoleTypeUser:      "User",
	RoleTypeAssistant: "Assistant",
}

type StatusType int8

const (
	StatusTypeSuccess StatusType = iota
	StatusTypeDeleted
	StatusTypeFailure
)

var StatusTypeNames = map[StatusType]string{
	StatusTypeSuccess: "status_success",
	StatusTypeDeleted: "status_deleted",
	StatusTypeFailure: "status_failure",
}

type Content struct {
	ContentType string      `json:"content_type,omitempty"`
	Parts       interface{} `json:"parts,omitempty"`
}

type Message struct {
	Id       string      `json:"id,omitempty"`
	Role     string      `json:"role,omitempty"`
	Content  interface{} `json:"content,omitempty"`
	Status   string      `json:"status,omitempty"`
	ParentId string      `json:"parent_id,omitempty"`
	Children []string    `json:"children,omitempty"`
}

var DefaultMessage = Message{
	Id:     uuid.New().String(),
	Role:   RoleTypeNames[RoleTypeSystem],
	Status: StatusTypeNames[StatusTypeSuccess],
}

func ExtractQuestTion(query map[string]interface{}) []Message {
	var messages []Message

	// 默认只会有一条数据
	message := Message{
		Id:      uuid.New().String(),
		Content: query,
		Role:    RoleTypeNames[RoleTypeUser],
		Status:  StatusTypeNames[StatusTypeSuccess],
	}
	messages = append(messages, message)

	return messages
}

func ExtractAnswer(buffer *bytes.Buffer) []Message {
	// 默认只会有一条数据
	var messages []Message
	var id, part string
	scanner := bufio.NewScanner(buffer)

	regexPattern := `data:\{"response":"(.*?)"\}`
	re := regexp.MustCompile(regexPattern)

	if scanner.Scan() {
		firstLine := scanner.Text()
		id = strings.TrimPrefix(firstLine, "id:")
	}

	for scanner.Scan() {
		line := scanner.Text()
		matches := re.FindAllStringSubmatch(line, -1)
		for _, match := range matches {
			if len(match) > 1 {
				part += matches[0][1]
			}
		}
	}

	if len(id) == 0 {
		id = uuid.New().String()
	}

	message := Message{
		Id:      id,
		Content: part,
		Role:    RoleTypeNames[RoleTypeAssistant],
		Status:  StatusTypeNames[StatusTypeSuccess],
	}

	messages = append(messages, message)

	return messages
}
