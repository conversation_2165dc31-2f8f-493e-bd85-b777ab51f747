package auth

import (
	"time"

	"github.com/emicklei/go-restful/v3"
)

type UserRole string

const (
	// CV 模型开发角色
	UserRoleAdmin = "admin"
	UserRoleGuest = "guest"
	// CV 模型应用角色
	UserRoleCVAdmin = "ROLE_ADMIN"
	UserRoleCVBasic = "ROLE_BASIC"
	// 兼容 SOPHON BASE 产品线角色
	UserRoleSophonAdmin = "SOPHON_ADMIN"
	UserRoleSophonBasic = "SOPHON_BASIC"

	DefaultUserName = "thinger"
	DefaultIss      = "transwarp"
	DefaultSub      = "llmops"
	InnerAuthCtx    = "__auth_ctx__" // InnerAuthCtx 用于完成用户验证后，将用户信息存入 HTTP 请求上下文
)

var defaultToken = &JWToken{
	username:  DefaultUserName,
	roles:     []string{UserRoleSophonAdmin, UserRoleAdmin, UserRoleCVAdmin},
	scope:     JWTTokenScopeInternal,
	expiredAt: time.Now().Add(24 * time.Hour).Unix(),
	issuedAt:  time.Now().Unix(),
	iss:       DefaultIss,
	sub:       DefaultSub,
}

// SetDefaultAuthContext 将默认用户信息保存至请求上下文
func SetDefaultAuthContext(request *restful.Request) {
	request.SetAttribute(InnerAuthCtx, defaultToken)
}

// SetAuthContext 将已通过验证的用户信息保存至请求上下文
func SetAuthContext(request *restful.Request, token *JWToken) {
	request.SetAttribute(InnerAuthCtx, token)
}

// GetAuthContext 获取已通过验证的请求中的用户信息上下文
func GetAuthContext(request *restful.Request) *JWToken {
	u := request.Attribute(InnerAuthCtx)
	if u == nil {
		return nil
	}
	user, ok := u.(*JWToken)
	if !ok {
		return nil
	}
	return user
}
