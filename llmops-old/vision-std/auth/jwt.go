package auth

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type (
	JWTClaim      = string
	JWTTokenScope = string
)

const (
	TokenHeader = "Authorization"
	TokenPrefix = "Bearer "

	JWTClaimUsername   JWTClaim = "username"
	JWTClaimRoles      JWTClaim = "roles"
	JWTClaimScope      JWTClaim = "scope"
	JWTClaimExpiration JWTClaim = "exp"
	JWTClaimIssuedAt   JWTClaim = "iat"
	JWTClaimIss        JWTClaim = "iss"
	JWTClaimSub        JWTClaim = "sub"

	JWTTokenScopeInternal JWTTokenScope = "internal"
	JWTTokenScopeExternal JWTTokenScope = "external"
)

var secret, _ = base64.URLEncoding.DecodeString("simpleSecret")

type JWToken struct {
	username  string
	roles     []string
	scope     string
	expiredAt int64 // unix second
	issuedAt  int64
	iss       string
	sub       string
}

func (j JWToken) Token() string {
	roles, _ := json.Marshal(j.roles)
	token, err := jwt.NewWithClaims(jwt.SigningMethodHS512, jwt.MapClaims{
		JWTClaimUsername:   j.username,
		JWTClaimRoles:      string(roles),
		JWTClaimScope:      j.scope,
		JWTClaimExpiration: j.expiredAt,
		JWTClaimIssuedAt:   j.issuedAt,
		JWTClaimIss:        j.iss,
		JWTClaimSub:        j.sub,
	}).SignedString(secret)
	if err != nil {
		return ""
	}
	return TokenPrefix + token
}

func (j JWToken) GetUsername() string {
	return j.username
}

func (j JWToken) GetRoles() []string {
	return j.roles
}

func (j JWToken) IsAdmin() bool {
	for _, role := range j.roles {
		if role == UserRoleAdmin || role == UserRoleSophonAdmin {
			return true
		}
	}
	return false
}

func (j JWToken) Expired() bool {
	return j.expiredAt < time.Now().Unix()
}

func (j JWToken) External() bool {
	return j.scope == JWTTokenScopeExternal
}

func (j JWToken) Internal() bool {
	return !j.External()
}

func (j JWToken) InternalToken() string {
	j.scope = JWTTokenScopeInternal
	return j.Token()
}

type JWTokenBuilder struct {
	JWToken

	ttl time.Duration // the time to live of token
}

func (b JWTokenBuilder) Username(username string) JWTokenBuilder {
	b.username = username
	return b
}

func (b JWTokenBuilder) Roles(roles ...string) JWTokenBuilder {
	b.roles = roles
	return b
}

func (b JWTokenBuilder) TimeToLive(ttl time.Duration) JWTokenBuilder {
	b.ttl = ttl
	return b
}

func (b JWTokenBuilder) Scope(scope JWTTokenScope) JWTokenBuilder {
	b.scope = scope
	return b
}

func (b JWTokenBuilder) Iss(iss string) JWTokenBuilder {
	b.iss = iss
	return b
}
func (b JWTokenBuilder) Sub(sub string) JWTokenBuilder {
	b.sub = sub
	return b
}

func (b JWTokenBuilder) Build() JWToken {
	scope := JWTTokenScopeInternal
	switch b.scope {
	case JWTTokenScopeExternal:
		scope = JWTTokenScopeExternal
	default:
		// nothing to do
	}

	return JWToken{
		username:  b.username,
		roles:     b.roles,
		scope:     scope,
		expiredAt: time.Now().Add(b.ttl).Unix(),
		issuedAt:  time.Now().Unix(),
		iss:       b.iss,
		sub:       b.sub,
	}
}

func ParseToken(token string) (JWToken, error) {
	if !strings.HasPrefix(token, TokenPrefix) {
		return JWToken{}, fmt.Errorf("invalid auth token, token must start with %s", TokenPrefix)
	}
	token = strings.TrimPrefix(token, TokenPrefix)
	claim, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return secret, nil
	})
	if err != nil {
		return JWToken{}, err
	}
	claims, ok := claim.Claims.(jwt.MapClaims)
	if !ok {
		return JWToken{}, fmt.Errorf("invalid auth token: %s", token)
	}
	username, ok := claims[JWTClaimUsername]
	if !ok {
		return JWToken{}, fmt.Errorf("%s not found in auth token", JWTClaimUsername)
	}

	roles := make([]string, 0)
	x, ok := claims[JWTClaimRoles]
	if !ok {
		return JWToken{}, fmt.Errorf("%s not found in auth token", JWTClaimRoles)
	}
	switch values := x.(type) {
	case string:
		if err := json.Unmarshal([]byte(values), &roles); err != nil {
			return JWToken{}, fmt.Errorf("failed to unmarshal roles, got %s", err.Error())
		}
	case []interface{}:
		for _, value := range values {
			roles = append(roles, value.(string))
		}
	default:
		return JWToken{}, fmt.Errorf("roles represented by unrecognized type: %+v", x)
	}

	scope, ok := claims[JWTClaimScope]
	if !ok {
		return JWToken{}, fmt.Errorf("%s not found in auth token", JWTClaimScope)
	}
	expiredAt, ok := claims[JWTClaimExpiration]
	if !ok {
		return JWToken{}, fmt.Errorf("%s not found in auth token", JWTClaimExpiration)
	}
	issuedAt, ok := claims[JWTClaimIssuedAt]
	if !ok {
		return JWToken{}, fmt.Errorf("%s not found in auth token", JWTClaimIssuedAt)
	}
	iss, ok := claims[JWTClaimIss]
	if !ok {
		iss = ""
	}
	sub, ok := claims[JWTClaimSub]
	if !ok {
		sub = ""
	}
	jt := JWToken{
		username:  username.(string),
		roles:     roles,
		scope:     scope.(string),
		issuedAt:  int64(issuedAt.(float64)),
		expiredAt: int64(expiredAt.(float64)),
		iss:       iss.(string),
		sub:       sub.(string),
	}
	if jt.Expired() {
		return JWToken{}, fmt.Errorf("auth token is expired")
	}
	return jt, nil
}

func ParseTokenFromRequest(request *http.Request) (JWToken, error) {
	token := request.Header.Get(TokenHeader)
	if token == "" {
		return JWToken{}, stderr.Unauthenticated.Errorf("lack of header: %s", TokenHeader)
	}
	return ParseToken(token)
}
