package auth

import (
	"reflect"
	"testing"
)

func TestParseToken(t *testing.T) {
	type args struct {
		token string
	}
	tests := []struct {
		name    string
		args    args
		want    JWToken
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "valid - roles represented by string (ps: marshalled list)",
			args: args{
				token: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTU2MjgwNTUsImlhdCI6MTY1NTM2ODg1NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.FQnGcTa2PQFZQTjRlr0Zo90U64NWhXfrhfapUwC3XXfd7tWhuL1odRKK369qE3722C56hEnj_NGdDLY7I3VRAg",
			},
			want: JWToken{
				username: "thinger",
				roles: []string{
					"SOPHON_ADMIN",
					"admin",
				},
				scope:     "internal",
				expiredAt: 1655628055,
				issuedAt:  1655368855,
			},
			wantErr: false,
		},
		{
			name: "valid - roles' type is []string",
			args: args{
				token: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTU2MjgwNTUsImlhdCI6MTY1NTM2ODg1NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.FQnGcTa2PQFZQTjRlr0Zo90U64NWhXfrhfapUwC3XXfd7tWhuL1odRKK369qE3722C56hEnj_NGdDLY7I3VRAg",
			},
			want: JWToken{
				username: "thinger",
				roles: []string{
					"SOPHON_ADMIN",
					"admin",
				},
				scope:     "internal",
				expiredAt: 1655628055,
				issuedAt:  1655368855,
			},
			wantErr: false,
		},
		{
			name: "invalid - without prefix 'Bearer'",
			args: args{
				token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTU2MjgwNTUsImlhdCI6MTY1NTM2ODg1NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.FQnGcTa2PQFZQTjRlr0Zo90U64NWhXfrhfapUwC3XXfd7tWhuL1odRKK369qE3722C56hEnj_NGdDLY7I3VRAg",
			},
			want:    JWToken{},
			wantErr: true,
		},
		{
			name: "invalid - roles' type is int",
			args: args{
				token: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTU2MjgwNTUsImlhdCI6MTY1NTM2ODg1NSwicm9sZXMiOjEyMzQ1Niwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.sxVF2nktE2qndL3FNyd_FhPaaZvKZCqhpYTl05xnJJnA_vd0py4CTbJHYgNOvJtFYyeR02ZyFHN6w9IEeLJwew",
			},
			want:    JWToken{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseToken(tt.args.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}
