package toolkit

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func RunCommand(cmd *exec.Cmd) error {
	if cmd == nil {
		return stderr.Internal.Error("cmd can not be nil")
	}
	stdlog.Debugf("exec cmd '%s'", cmd.String())

	var cmdErr bytes.Buffer
	cmd.Stderr = &cmdErr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to exec cmd '%s', got: %s", cmd.String(), string(cmdErr.Bytes()))
	}
	return nil
}

func RunCommandWithTimeout(timeout time.Duration, cmd *exec.Cmd) error {
	if cmd == nil {
		return stderr.Internal.Error("cmd can not be nil")
	}
	// 异步启动cmd
	stdlog.Debugf("running cmd '%s' with timeout '%s'", cmd.String(), timeout.String())
	if err := cmd.Start(); err != nil {
		return stderr.Internal.Cause(err, "failed to start command '%s'", cmd.String())
	}

	// 通过协程等待进程处理完毕
	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	// 等待超时
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	select {
	case <-ctx.Done():
		if err := cmd.Process.Kill(); err == nil {
			return stderr.Internal.Cause(err, "failed to kill process after timeout")
		}
		return stderr.Internal.Error("timeout while processing :'%s'", cmd.String())
	case err := <-done:
		if err != nil {
			return stderr.Internal.Cause(err, "error occurred while waiting process done")
		}
	}
	return nil
}
