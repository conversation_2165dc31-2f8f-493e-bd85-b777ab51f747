// Code generated by MockGen. DO NOT EDIT.
// Source: define.go

// Package mock is a generated GoMock package.
package mock

import (
	schema2 "github.com/docker/distribution/manifest/schema2"
	gomock "github.com/golang/mock/gomock"
	reflect "reflect"
	dockerimage "transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"
)

// MockImageHubAPI is a mock of ImageHubAPI interface
type MockImageHubAPI struct {
	ctrl     *gomock.Controller
	recorder *MockImageHubAPIMockRecorder
}

// MockImageHubAPIMockRecorder is the mock recorder for MockImageHubAPI
type MockImageHubAPIMockRecorder struct {
	mock *MockImageHubAPI
}

// NewMockImageHubAPI creates a new mock instance
func NewMockImageHubAPI(ctrl *gomock.Controller) *MockImageHubAPI {
	mock := &MockImageHubAPI{ctrl: ctrl}
	mock.recorder = &MockImageHubAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockImageHubAPI) EXPECT() *MockImageHubAPIMockRecorder {
	return m.recorder
}

// ListImageTags mocks base method
func (m *MockImageHubAPI) GetImageTag(imageRepo string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListImageTags", imageRepo)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListImageTags indicates an expected call of ListImageTags
func (mr *MockImageHubAPIMockRecorder) GetImageTag(imageRepo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListImageTags", reflect.TypeOf((*MockImageHubAPI)(nil).GetImageTag), imageRepo)
}

// GetImageConfig mocks base method
func (m *MockImageHubAPI) GetImageConfig(imageRepo, tag string) (*dockerimage.DockerImage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageConfig", imageRepo, tag)
	ret0, _ := ret[0].(*dockerimage.DockerImage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageConfig indicates an expected call of GetImageConfig
func (mr *MockImageHubAPIMockRecorder) GetImageConfig(imageRepo, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageConfig", reflect.TypeOf((*MockImageHubAPI)(nil).GetImageConfig), imageRepo, tag)
}

// UploadBlob mocks base method
func (m *MockImageHubAPI) UploadBlob(imageRepo string, body []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadBlob", imageRepo, body)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadBlob indicates an expected call of UploadBlob
func (mr *MockImageHubAPIMockRecorder) UploadBlob(imageRepo, body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadBlob", reflect.TypeOf((*MockImageHubAPI)(nil).UploadBlob), imageRepo, body)
}

// UploadManifest mocks base method
func (m *MockImageHubAPI) UploadManifest(imageRepo, tag string, manifest *schema2.Manifest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadManifest", imageRepo, tag, manifest)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadManifest indicates an expected call of UploadManifest
func (mr *MockImageHubAPIMockRecorder) UploadManifest(imageRepo, tag, manifest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadManifest", reflect.TypeOf((*MockImageHubAPI)(nil).UploadManifest), imageRepo, tag, manifest)
}

// GetBlob mocks base method
func (m *MockImageHubAPI) GetBlob(imageRepo, digest string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlob", imageRepo, digest)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlob indicates an expected call of GetBlob
func (mr *MockImageHubAPIMockRecorder) GetBlob(imageRepo, digest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlob", reflect.TypeOf((*MockImageHubAPI)(nil).GetBlob), imageRepo, digest)
}

// GetManifest mocks base method
func (m *MockImageHubAPI) GetManifest(imageRepo, tag string) (*schema2.Manifest, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManifest", imageRepo, tag)
	ret0, _ := ret[0].(*schema2.Manifest)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetManifest indicates an expected call of GetManifest
func (mr *MockImageHubAPIMockRecorder) GetManifest(imageRepo, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifest", reflect.TypeOf((*MockImageHubAPI)(nil).GetManifest), imageRepo, tag)
}

// DeleteImage mocks base method
func (m *MockImageHubAPI) DeleteImage(imageRepo, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteImage", imageRepo, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteImage indicates an expected call of DeleteImage
func (mr *MockImageHubAPIMockRecorder) DeleteImage(imageRepo, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteImage", reflect.TypeOf((*MockImageHubAPI)(nil).DeleteImage), imageRepo, tag)
}

// MockSophonImageAPI is a mock of SophonImageAPI interface
type MockSophonImageAPI struct {
	ctrl     *gomock.Controller
	recorder *MockSophonImageAPIMockRecorder
}

// MockSophonImageAPIMockRecorder is the mock recorder for MockSophonImageAPI
type MockSophonImageAPIMockRecorder struct {
	mock *MockSophonImageAPI
}

// NewMockSophonImageAPI creates a new mock instance
func NewMockSophonImageAPI(ctrl *gomock.Controller) *MockSophonImageAPI {
	mock := &MockSophonImageAPI{ctrl: ctrl}
	mock.recorder = &MockSophonImageAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockSophonImageAPI) EXPECT() *MockSophonImageAPIMockRecorder {
	return m.recorder
}

// GetSophonService mocks base method
func (m *MockSophonImageAPI) GetSophonService(addr, token string) ([]*dockerimage.SophonService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSophonService", addr, token)
	ret0, _ := ret[0].([]*dockerimage.SophonService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSophonService indicates an expected call of GetSophonService
func (mr *MockSophonImageAPIMockRecorder) GetSophonService(addr, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSophonService", reflect.TypeOf((*MockSophonImageAPI)(nil).GetSophonService), addr, token)
}

// GetSophonServiceVersion mocks base method
func (m *MockSophonImageAPI) GetSophonServiceVersion(serviceId, addr, token string) ([]*dockerimage.SophonServiceVersionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSophonServiceVersion", serviceId, addr, token)
	ret0, _ := ret[0].([]*dockerimage.SophonServiceVersionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSophonServiceVersion indicates an expected call of GetSophonServiceVersion
func (mr *MockSophonImageAPIMockRecorder) GetSophonServiceVersion(serviceId, addr, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSophonServiceVersion", reflect.TypeOf((*MockSophonImageAPI)(nil).GetSophonServiceVersion), serviceId, addr, token)
}

// GetSophonServiceTag mocks base method
func (m *MockSophonImageAPI) GetSophonServiceTag(versionId, addr, token string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSophonServiceTag", versionId, addr, token)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSophonServiceTag indicates an expected call of GetSophonServiceTag
func (mr *MockSophonImageAPIMockRecorder) GetSophonServiceTag(versionId, addr, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSophonServiceTag", reflect.TypeOf((*MockSophonImageAPI)(nil).GetSophonServiceTag), versionId, addr, token)
}

// GetSophonImageBuildState mocks base method
func (m *MockSophonImageAPI) GetSophonImageBuildState(versionId, addr, token string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSophonImageBuildState", versionId, addr, token)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSophonImageBuildState indicates an expected call of GetSophonImageBuildState
func (mr *MockSophonImageAPIMockRecorder) GetSophonImageBuildState(versionId, addr, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSophonImageBuildState", reflect.TypeOf((*MockSophonImageAPI)(nil).GetSophonImageBuildState), versionId, addr, token)
}

// GetSophonVersionInfo mocks base method
func (m *MockSophonImageAPI) GetSophonVersionInfo(versionId, addr, token string) (*dockerimage.SophonServiceVersionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSophonVersionInfo", versionId, addr, token)
	ret0, _ := ret[0].(*dockerimage.SophonServiceVersionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSophonVersionInfo indicates an expected call of GetSophonVersionInfo
func (mr *MockSophonImageAPIMockRecorder) GetSophonVersionInfo(versionId, addr, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSophonVersionInfo", reflect.TypeOf((*MockSophonImageAPI)(nil).GetSophonVersionInfo), versionId, addr, token)
}
