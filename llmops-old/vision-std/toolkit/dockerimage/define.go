package dockerimage

import (
	"context"
	"crypto/tls"
	"github.com/docker/distribution/manifest/schema2"
	"net/http"
	"time"
)

type ImageHubAPI interface {
	// 列出仓库所有repo
	ListRepos(registryUrl string, last string, limit int) ([]string, error)

	// ListImageTags 列出repo的所有tag
	ListImageTags(imageRepo string) ([]string, error)

	ListImageTagsWithCtx(ctx context.Context, imageRepo string) ([]string, error)

	// GetImageConfig 查看镜像的信息,与docker inspect作用类似
	GetImageConfig(imageRepo, tag string) (*DockerImage, error)

	GetBlobContentLength(imageRepo, digest string) (string, error)

	GetImageConfigWithCtx(ctx context.Context, imageRepo, tag string) (*DockerImage, error)

	// ExistImages 检测给定的镜像是否存在
	ExistImages(repoTags ...string) error

	ExistImagesWithCtx(ctx context.Context, repoTags ...string) error

	// UploadBlob 向远程repo推送一层新的文件
	UploadBlob(imageRepo string, body []byte) error

	UploadBlobWithCtx(ctx context.Context, imageRepo string, body []byte) error

	// UploadManifest 更新远程镜像的清单
	UploadManifest(imageRepo, tag string, manifest *schema2.Manifest) error

	UploadManifestWithCtx(ctx context.Context, imageRepo, tag string, manifest *schema2.Manifest) error

	// GetBlob 获取远程repo的一层数据,由digest指定
	GetBlob(imageRepo, digest string) ([]byte, error)

	GetBlobWithCtx(ctx context.Context, imageRepo, digest string) ([]byte, error)

	// GetManifest 获取远程镜像的清单
	GetManifest(imageRepo, tag string) (*schema2.Manifest, string, error)

	GetManifestWithCtx(ctx context.Context, imageRepo, tag string) (*schema2.Manifest, string, error)

	// DeleteImage 删除远程镜像
	//FIXME 此接口会删除镜像的tag(注意是同一个镜像的所有tag),
	//https://github.com/docker/distribution/issues/1811
	DeleteImage(imageRepo, tag string) error

	DeleteImageWithCtx(ctx context.Context, imageRepo, tag string) error

	//// LoadImagesFromReader 从 reader 读取镜像推到 registry
	//// https://docs.docker.com/registry/spec/api/#pushing-an-image
	//LoadImagesFromReader(ctx context.Context, reader io.Reader) error
	//
	//// SaveImagesToWriter 从 registry 拉取镜像写入 writer
	//// https://docs.docker.com/registry/spec/api/#pulling-an-image
	//SaveImagesToWriter(ctx context.Context, writer io.Writer, repoTags ...string) error
}

type ImageHub struct {
	tokens string
	client *http.Client
}

// NewImageHubCli 创建并返回ImageHubAPI实例
// timeout 控制每次http请求的超时时间
func NewImageHubCli(tokens string, timeout string) ImageHubAPI {
	to, err := time.ParseDuration(timeout)
	if err != nil {
		to = 3 * time.Second
	}
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
		Timeout: to,
	}
	return &ImageHub{
		tokens: tokens,
		client: client,
	}
}

// mockgen -destination mock/mock_dockerimage.go  -package mock -source define.go
