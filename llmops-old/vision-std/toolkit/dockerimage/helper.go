package dockerimage

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/docker/distribution/reference"
	"github.com/docker/distribution/registry/client/auth/challenge"
	"github.com/pkg/errors"
	"net/http"
	http_url "net/url"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// 此错误定义在 net/http/client.go中261行
//
//	if string(tlsErr.RecordHeader[:]) == "HTTP/" {
//		err = errors.New("http: server gave HTTP response to HTTPS client")
//	}
const ProtocolErr = "http: server gave HTTP response to HTTPS client"

var (
	commonClient = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
		Timeout: 10 * time.Second,
	}
)

func (hub *ImageHub) httpReqWithCtx(ctx context.Context, method, url string, header map[string]string, body []byte) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, strings.ToUpper(method), url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	insecure, err := isInsecure(ctx, hub.client, req.URL.String())
	if err != nil {
		return nil, err
	}
	if insecure {
		req.URL.Scheme = "http"
	} else {
		req.URL.Scheme = "https"
	}
	for key, value := range header {
		req.Header.Set(key, value)
	}
	basicToken, err := GetBasicTokenByDomain(req.Host, hub.tokens)
	if err != nil {
		return nil, err
	}
	if basicToken != "" {
		req.Header.Set("Authorization", "Basic "+basicToken)
	}
	res, err := hub.client.Do(req)
	if err != nil {
		return nil, err
	}
	// 没有指定basicToken的情况下，获取v ***********(harbor)的镜像的信息
	if res.StatusCode == http.StatusUnauthorized {
		res.Body.Close()
		token, err := getBearerToken(res)
		if err != nil {
			return nil, err
		}
		req.Header.Set("Authorization", token)
		res, err = hub.client.Do(req)
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (hub *ImageHub) httpReq(method, url string, header map[string]string, body []byte) (*http.Response, error) {
	return hub.httpReqWithCtx(context.Background(), method, url, header, body)
}

// IsInsecure 判断registry服务器是否使用了tls
func IsInsecure(ctx context.Context, url string) (bool, error) {
	return isInsecure(ctx, commonClient, url)
}

func isInsecure(ctx context.Context, client *http.Client, url string) (bool, error) {
	u, err := http_url.Parse(url)
	if err != nil {
		return false, err
	}
	u.Path = "/v2/"
	u.Scheme = "https"
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return false, err
	}
	_, err = client.Do(req)
	if err == nil {
		return false, nil
	}
	if strings.HasSuffix(err.Error(), ProtocolErr) {
		return true, nil
	}
	return false, err
}

// 详见文档　https://github.com/docker/distribution/blob/master/docs/spec/auth/token.md
func getBearerToken(res *http.Response) (string, error) {
	challenges := challenge.ResponseChallenges(res)
	if len(challenges) != 1 {
		return "", fmt.Errorf("unexpected challenges value:%v", challenges)
	}

	challenge := challenges[0]
	if challenge.Scheme != "bearer" {
		return "", fmt.Errorf("unexpected challenge scheme:%v", challenge.Scheme)
	}
	realm, ok := challenge.Parameters["realm"]
	if !ok {
		return "", fmt.Errorf("no realm parameter in the challenge")
	}
	scope, ok := challenge.Parameters["scope"]
	if !ok {
		return "", fmt.Errorf("no scope parameter in the challenge")
	}
	service, ok := challenge.Parameters["service"]
	if !ok {
		return "", fmt.Errorf("no service parameter in the challenge")
	}
	params := http_url.Values{}
	params["service"] = []string{service}
	params["scope"] = []string{scope}
	url := fmt.Sprintf("%s?%s", realm, params.Encode())
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return "", err
	}
	res, err = commonClient.Do(req)
	if err != nil {
		return "", err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to get execute req, status:%v", res.Status)
	}
	var token struct {
		Token string `json:"token"`
	}
	if err := json.NewDecoder(res.Body).Decode(&token); err != nil {
		return "", err
	}
	return "Bearer " + token.Token, nil
}

// GetBasicTokenAccountByImage param tokens sample: "host1:token1,host2:token2"
func GetBasicTokenAccountByImage(image string, tokens string) (string, string, error) {
	if tokens == "" {
		return "", "", nil
	}
	token, err := GetBasicTokenByImage(image, tokens)
	if err != nil {
		return "", "", err
	}
	user, passwd, err := DecodeBasicAuth(token)
	if err != nil {
		return "", "", fmt.Errorf("failed to decode basicToken:%s, error=%+v", token, err)
	}
	return user, passwd, nil
}

func GetBasicTokenByImage(image string, tokens string) (string, error) {
	if tokens == "" {
		return "", nil
	}
	name, err := reference.ParseNormalizedNamed(image)
	if err != nil {
		return "", fmt.Errorf("invalid image:%s, error=%+v", image, err)
	}
	domain := reference.Domain(name)
	return GetBasicTokenByDomain(domain, tokens)
}

func GetBasicTokenByDomain(domain string, tokens string) (string, error) {
	if tokens == "" {
		return "", nil
	}
	tokenArr := strings.Split(tokens, ",")
	for _, token := range tokenArr {
		index := strings.LastIndex(token, ":")
		if index == -1 {
			return "", fmt.Errorf("failed to parse token:%s, format must as:\n host1:token1,host2:token2", token)
		}
		server := token[:index]
		if strings.Contains(server, domain) {
			return token[index+1:], nil
		}
	}
	return "", nil
}

// DecodeBasicAuth from github.com/docker/cli/cli/config/configfile/file.go
// decodes a base64 encoded string and returns username and password
func DecodeBasicAuth(token string) (string, string, error) {
	if token == "" {
		return "", "", nil
	}

	decLen := base64.StdEncoding.DecodedLen(len(token))
	decoded := make([]byte, decLen)
	authByte := []byte(token)
	n, err := base64.StdEncoding.Decode(decoded, authByte)
	if err != nil {
		return "", "", err
	}
	if n > decLen {
		return "", "", errors.Errorf("Something went wrong decoding auth config")
	}
	arr := strings.SplitN(string(decoded), ":", 2)
	if len(arr) != 2 {
		return "", "", errors.Errorf("Invalid auth configuration file")
	}
	password := strings.Trim(arr[1], "\x00")
	return arr[0], password, nil
}

const DefaultRepoTag = "latest"

// NewImageRepoTag 生成一个镜像的RepoTag
// REPOSITORY                                        TAG
// ***********/aip/vision-frontend                   master
func NewImageRepoTag(repo, tag string) string {
	if tag == "" {
		stdlog.Warnf("tag is empty while new image repo tag, replaced it with default tag '%s'", DefaultRepoTag)
		tag = DefaultRepoTag
	}
	return repo + ":" + tag
}

// ParseImageRepoTag 解析镜像地址的RepoTag, e.g. ***********/aip/vision-frontend:master
// REPOSITORY                                        TAG
// ***********/aip/vision-frontend                   master
func ParseImageRepoTag(repoTag string) (repo, tag string, err error) {
	if repoTag == "" {
		err = stderr.InvalidParam.Error("empty image repo tag")
		return
	}
	idx := strings.LastIndex(repoTag, ":")
	if idx < 0 {
		err = stderr.InvalidParam.Error("invalid image repo tag '%s', not found char ':'", repoTag)
		return
	}
	repo, tag = repoTag[:idx], repoTag[idx+1:]
	return
}
