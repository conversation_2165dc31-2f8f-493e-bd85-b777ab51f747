package dockerimage

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/docker/go-units"
	"io"
)

type Progress interface {
	Cancel()
	Wait() error
	String() string
}

// pullEvent representing events returned from image pulling
type pullEvent struct {
	ID             string         `json:"id"`
	Status         string         `json:"status"`
	Error          string         `json:"error,omitempty"`
	Progress       string         `json:"progress,omitempty"`
	ProgressDetail ProgressDetail `json:"progressDetail"`
}

type ProgressDetail struct {
	Current int `json:"current"`
	Total   int `json:"total"`
}

type PullProgress struct {
	complete   chan struct{}
	err        error
	decoder    *json.Decoder
	cancelF    context.CancelFunc
	progresses map[string]*ProgressDetail
	rc         io.ReadCloser
}

// NewPullProgress returns the progress of image pulling.
// It can be used to wait pulling completed or failed and get
// current pulling progress as string.
func NewPullProgress(rc io.ReadCloser, cancel context.CancelFunc) *PullProgress {
	decoder := json.NewDecoder(rc)
	p := &PullProgress{
		complete:   make(chan struct{}),
		err:        nil,
		decoder:    decoder,
		cancelF:    cancel,
		progresses: make(map[string]*ProgressDetail),
		rc:         rc,
	}

	// read event form image pull response
	go func() {
		for {
			var err error
			var event *pullEvent

			// loop to read pull event
			if err = p.decoder.Decode(&event); err == nil {
				fmt.Printf("\n%+v", event)
				if event.Error != "" {
					p.exit(event.Error)
					return
				}
				p.add(event)
				continue
			}

			// pull complete
			if err == io.EOF {
				p.exit("")
				return
			}

			// exception occurred
			p.exit(err.Error())
			return
		}
	}()
	return p
}

func (p *PullProgress) exit(errMsg string) {
	close(p.complete)
	_ = p.rc.Close()
	if errMsg != "" {
		p.err = fmt.Errorf(errMsg)
	}
	return
}

// Wait will blocking until pulling image completed or some errors occurred
func (p *PullProgress) Wait() error {
	<-p.complete
	return p.err
}

func (p *PullProgress) Cancel() {
	p.cancelF()
}

// String returns the overview of pulling image progress as a string
func (p *PullProgress) String() string {
	return fmt.Sprintf("下载镜像中 : %s/%s",
		units.HumanSize(float64(p.current())),
		units.HumanSize(float64(p.total())),
	)
}

// add adds an image pull event to progress
func (p *PullProgress) add(evt *pullEvent) {
	progress, ok := p.progresses[evt.ID]
	if !ok {
		p.progresses[evt.ID] = &evt.ProgressDetail
		return
	}

	if evt.ProgressDetail.Total != 0 {
		progress.Total = evt.ProgressDetail.Total
	}
	if evt.ProgressDetail.Current != 0 {
		progress.Current = evt.ProgressDetail.Current
	}
}

// total returns the total bytes that need to download
func (p *PullProgress) total() int {
	total := 0
	for _, progress := range p.progresses {
		total += progress.Total
	}
	return total
}

// current returns the total bytes that already downloaded
func (p *PullProgress) current() int {
	current := 0
	for _, progress := range p.progresses {
		current += progress.Current
	}
	return current
}

// percent returns the current total percent of pulling image progress
func (p *PullProgress) percent() float32 {
	total := p.total()
	if total == 0 {
		return 0
	}
	return float32(p.current()) / float32(total)
}
