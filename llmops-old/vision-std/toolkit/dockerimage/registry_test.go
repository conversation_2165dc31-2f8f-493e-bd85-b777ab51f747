package dockerimage

import (
	"fmt"
	"testing"
)

func TestRegistry_ExistImages(t *testing.T) {
	type fields struct {
		tokens string
	}
	type args struct {
		repoTags []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			"existed image from registry",
			fields{tokens: ""},
			args{repoTags: []string{"***********/aip/base/eclipse-mosquitto-1.4.12:sophon-0.0"}},
			false,
		},
		{
			"invalid image from registry",
			fields{tokens: ""},
			args{repoTags: []string{"***********/aip/base/eclipse-mosquitto-1.4.12:sophon-0.0.0"}},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &ImageHub{
				tokens: tt.fields.tokens,
			}
			if err := this.ExistImages(tt.args.repoTags...); (err != nil) != tt.wantErr {
				t.Errorf("ExistImages() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRegistry_GetImageTags(t *testing.T) {
	imageRepo := "***********:5000/modelserver/vehicle" ///this image only support https
	//imageRepo := "***********/aip/base/eclipse-mosquitto-1.4.12:12"
	//imageRepo:="************:5000/aip/base/mysql-5.7"
	reg := new(ImageHub)
	tags, err := reg.ListImageTags(imageRepo)
	if err != nil {
		t.Error(err)
	} else {
		t.Logf("success to get tags: %v", tags)
	}
}

func TestRegistry_GetImageConfigInfo(t *testing.T) {
	//imageRepo := "***********:5000/modelserver/vehicle"
	//tag := "20190626_02"
	imageRepo := "***********/aip/base/eclipse-mosquitto-1.4.12"
	tag := "sophon-0.0"
	reg := new(ImageHub)
	info, err := reg.GetImageConfig(imageRepo, tag)
	if err != nil {
		t.Error(err)
	} else {
		t.Logf("success to get info: %+v", info)
	}
}

//func TestSophonImage_GetSophonVersionInfo(t *testing.T) {
//	cli := new(SophonImage)
//	addr := "************:8711"
//	token := "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRpYW5taW5nIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIl0iLCJleHAiOjE4ODE4MjcwNjMsImlhdCI6MTU2NjQ2NzA2M30.1EhCVljE4-XRI-xyI5SAEYae582TJ3WxGALPJuokqnVZdp9T_3lYatsfL4Uo1e-oZiJBvU7faZSug043SsIz_Q"
//	versionId := "60c67931-58a6-42c8-ae92-9a74df981ca1"
//	res, err := cli.GetSophonVersionInfo(versionId, addr, token)
//	if err != nil {
//		t.Error(err)
//		return
//	}
//	fmt.Printf("%+v", res)
//}

func TestExistImages(t *testing.T) {
	GetImageBootConfig()
}
func GetImageBootConfig() {
	image := "*************:5000/aip/base/fake-cheche:latest"

	fmt.Println(image)
	ImageHub := NewImageHubCli("","1m")
	repo, tag, err := ParseImageRepoTag(image)
	if err != nil {
		return
	}
	c, err := ImageHub.GetImageConfig(repo, tag)
	if err != nil {
		return
	}
	fmt.Println(c)
}
