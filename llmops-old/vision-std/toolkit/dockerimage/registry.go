package dockerimage

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	http_url "net/url"
	"strings"

	"github.com/docker/distribution/manifest/schema2"
	"github.com/docker/distribution/reference"
	"github.com/opencontainers/go-digest"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// https://docs.docker.com/registry/spec/api/

func (hub *ImageHub) ListRepos(registryUrl string, last string, limit int) ([]string, error) {
	return hub.ListReposWithCtx(context.Background(), registryUrl, last, limit)
}

func (hub *ImageHub) ListReposWithCtx(ctx context.Context, registryUrl string, last string, limit int) ([]string, error) {
	url := fmt.Sprintf("https://%s/v2/_catalog?last=%s&n=%d", registryUrl, last, limit)

	res, err := hub.httpReqWithCtx(ctx, http.MethodGet, url, nil, nil)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to access url:%s, method:%s, status:%s", url, http.MethodHead, res.Status)
	}
	body, _ := ioutil.ReadAll(res.Body)
	var repo struct {
		Repos []string `json:"repositories"`
	}
	if err := json.Unmarshal(body, &repo); err != nil {
		return nil, err
	}
	return repo.Repos, nil
}

func (hub *ImageHub) GetBlobContentLength(imageRepo, digest string) (string, error) {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return "", err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/blobs/%s", host, path, digest)
	res, err := hub.httpReqWithCtx(context.Background(), http.MethodGet, url, nil, nil)
	if err != nil {
		return "", err
	}
	if res.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodGet, res.Status)
	}
	header := res.Header.Get("Content-Length")
	return header, nil
}

func (hub *ImageHub) ExistImages(repoTags ...string) error {
	return hub.ExistImagesWithCtx(context.Background(), repoTags...)
}

func (hub *ImageHub) ExistImagesWithCtx(ctx context.Context, repoTags ...string) error {
	if len(repoTags) == 0 {
		return stderr.InvalidParam.Error("repo tags can not be empty")
	}

	notFounds := make([]string, 0)
	for _, rt := range repoTags {
		if err := hub.existImage(ctx, rt); err != nil {
			stdlog.WithError(err).Errorf("%s not found", rt)
			notFounds = append(notFounds, rt)
		}
	}
	if len(notFounds) != 0 {
		return stderr.ImageNotFoundError.Error("following docker images not found : '%+v'", notFounds)
	}

	return nil
}

func (hub *ImageHub) existImage(ctx context.Context, repoTag string) error {
	repo, tag, err := ParseImageRepoTag(repoTag)
	if err != nil {
		return err
	}
	name, err := reference.ParseNormalizedNamed(repo)
	if err != nil {
		return err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/manifests/%s", host, path, tag)
	rsp, err := hub.httpReqWithCtx(ctx, http.MethodHead, url, nil, nil)
	if err != nil {
		return err
	}
	if rsp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to access url:%s, method:%s, status:%s", url, http.MethodHead, rsp.Status)
	}
	return nil
}

func (hub *ImageHub) ListImageTags(imageRepo string) ([]string, error) {
	return hub.ListImageTagsWithCtx(context.Background(), imageRepo)
}

func (hub *ImageHub) ListImageTagsWithCtx(ctx context.Context, imageRepo string) ([]string, error) {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return nil, err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/tags/list", host, path)
	res, err := hub.httpReqWithCtx(ctx, http.MethodGet, url, nil, nil)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodGet, res.Status)
	}
	body, _ := ioutil.ReadAll(res.Body)
	var tag struct {
		Tags []string `json:"tags"`
	}
	if err := json.Unmarshal(body, &tag); err != nil {
		return nil, err
	}
	return tag.Tags, nil
}

func (hub *ImageHub) GetImageConfig(imageRepo, tag string) (*DockerImage, error) {
	manifest, _, err := hub.GetManifest(imageRepo, tag)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get the manifest of '%s:%s'", imageRepo, tag)
	}
	body, err := hub.GetBlob(imageRepo, string(manifest.Config.Digest))
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get the blob of '%s:%s'", imageRepo, string(manifest.Config.Digest))
	}
	image := &DockerImage{}
	if err := json.Unmarshal(body, image); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal blob to image: '%s'", body)
	}
	return image, nil
}

func (hub *ImageHub) GetImageConfigWithCtx(ctx context.Context, imageRepo, tag string) (*DockerImage, error) {
	manifest, _, err := hub.GetManifestWithCtx(ctx, imageRepo, tag)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get the manifest of '%s:%s'", imageRepo, tag)
	}
	body, err := hub.GetBlobWithCtx(ctx, imageRepo, string(manifest.Config.Digest))
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get the blob of '%s:%s'", imageRepo, string(manifest.Config.Digest))
	}
	image := &DockerImage{}
	if err := json.Unmarshal(body, image); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal blob to image: '%s'", body)
	}
	return image, nil
}

func (hub *ImageHub) UploadBlob(imageRepo string, body []byte) error {
	return hub.UploadBlobWithCtx(context.Background(), imageRepo, body)
}

func (hub *ImageHub) UploadBlobWithCtx(ctx context.Context, imageRepo string, body []byte) error {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	sum256 := sha256.Sum256(body)
	digest := digest.NewDigestFromBytes(digest.SHA256, sum256[:])
	url := fmt.Sprintf("https://%s/v2/%s/blobs/uploads/", host, path)
	res, err := hub.httpReqWithCtx(ctx, http.MethodPost, url, nil, nil)
	if err != nil {
		return err
	}
	res.Body.Close()
	if res.StatusCode != http.StatusAccepted {
		return fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodPost, res.Status)
	}
	loc := res.Header.Get("Location")
	if loc == "" {
		return fmt.Errorf("no Location header")
	}
	httpUrl, err := http_url.Parse(loc)
	if err != nil {
		return fmt.Errorf("unable to parse Location: %s", err)
	}
	if httpUrl.RawQuery != "" {
		httpUrl.RawQuery += "&"
	}
	httpUrl.RawQuery += "digest=" + string(digest)
	header := map[string]string{
		"Content-Type": "application/octet-stream",
	}
	res, err = hub.httpReqWithCtx(ctx, http.MethodPut, httpUrl.String(), header, body)
	if err != nil {
		return err
	}
	res.Body.Close()

	if res.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to access url:%s, method:%s, status=%s", httpUrl.String(), http.MethodPut, res.Status)
	}
	return nil
}

func (hub *ImageHub) UploadManifest(imageRepo, tag string, manifest *schema2.Manifest) error {
	return hub.UploadManifestWithCtx(context.Background(), imageRepo, tag, manifest)
}

func (hub *ImageHub) UploadManifestWithCtx(ctx context.Context, imageRepo, tag string, manifest *schema2.Manifest) error {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	header := map[string]string{
		"Content-Type": schema2.MediaTypeManifest,
	}
	body, err := json.Marshal(manifest)
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://%s/v2/%s/manifests/%s", host, path, tag)
	res, err := hub.httpReqWithCtx(ctx, http.MethodPut, url, header, body)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodPut, res.Status)

	}
	return nil
}

func (hub *ImageHub) GetBlob(imageRepo, digest string) ([]byte, error) {
	return hub.GetBlobWithCtx(context.Background(), imageRepo, digest)
}

func (hub *ImageHub) GetBlobWithCtx(ctx context.Context, imageRepo, digest string) ([]byte, error) {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return nil, err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/blobs/%s", host, path, digest)
	res, err := hub.httpReqWithCtx(ctx, http.MethodGet, url, nil, nil)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodGet, res.Status)
	}
	body, _ := ioutil.ReadAll(res.Body)
	return body, nil
}

func (hub *ImageHub) GetManifest(imageRepo, tag string) (*schema2.Manifest, string, error) {
	return hub.GetManifestWithCtx(context.Background(), imageRepo, tag)
}

func (hub *ImageHub) GetManifestWithCtx(ctx context.Context, imageRepo, tag string) (*schema2.Manifest, string, error) {
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return nil, "", err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/manifests/%s", host, path, tag)
	header := map[string]string{
		"Accept": strings.Join([]string{
			schema2.MediaTypeManifest,
			// 兼容OCI格式,以免获取Manifest时出现 OCI manifest found, but accept header does not support OCI manifests
			// application/vnd.oci.image.manifest.v1+json
			// 此处假定 docker 格式 与 OCI格式相互兼容, 因此后续解析逻辑不变
			v1.MediaTypeImageManifest,
		}, ","),
	}
	res, err := hub.httpReqWithCtx(ctx, http.MethodGet, url, header, nil)
	if err != nil {
		return nil, "", err
	}
	if res.StatusCode != http.StatusOK {
		return nil, "", fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodGet, res.Status)
	}
	digest := res.Header.Get("Docker-Content-Digest")
	body, _ := ioutil.ReadAll(res.Body)
	manifest := &schema2.Manifest{} // 约等于 ocischema.Manifest{}
	// digest := manifest.Config.Digest.String()
	if err := json.Unmarshal(body, manifest); err != nil {
		return nil, "", err
	}
	return manifest, digest, nil
}

func (hub *ImageHub) DeleteImage(imageRepo, tag string) error {
	return hub.DeleteImageWithCtx(context.Background(), imageRepo, tag)
}

func (hub *ImageHub) DeleteImageWithCtx(ctx context.Context, imageRepo, tag string) error {
	_, digest, err := hub.GetManifestWithCtx(ctx, imageRepo, tag)
	if err != nil {
		return err
	}
	name, err := reference.ParseNormalizedNamed(imageRepo)
	if err != nil {
		return err
	}
	host := reference.Domain(name)
	path := reference.Path(name)
	url := fmt.Sprintf("https://%s/v2/%s/manifests/%s", host, path, digest)
	header := map[string]string{
		"Accept": schema2.MediaTypeManifest,
	}
	res, err := hub.httpReqWithCtx(ctx, http.MethodDelete, url, header, nil)
	if err != nil {
		return err
	}
	if res.StatusCode == http.StatusAccepted || res.StatusCode == http.StatusNotFound {
		return nil
	}
	return fmt.Errorf("failed to access url:%s, method:%s, status=%s", url, http.MethodDelete, res.Status)
}
