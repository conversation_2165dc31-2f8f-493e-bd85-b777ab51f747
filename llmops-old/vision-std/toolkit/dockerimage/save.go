package dockerimage

import (
	"context"
	"io"
	"os"
	"path"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	docker "github.com/docker/docker/client"
	dockerparser "github.com/novln/docker-parser"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

var (
	dockerCli           *docker.Client
	imgCache            *imagesCache
	DockerVersion       = "1.38"          // 用于设置docker api的版本
	ImageCacheExpireDur = 5 * time.Second // 缓存中的镜像列表的过期时长
)

// SaveImages save one or more images to a tar archive
// output : Write to a file
func SaveImages(output string, repoTags ...string) error {
	exist, err := utils.Exists(output)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to exist path '%s'", output)
	}
	if !exist {
		if err := os.MkdirAll(path.Dir(output), os.ModePerm); err != nil {
			return stderr.Internal.Cause(err, "failed to mkdir all '%s'", output)
		}
	}

	fw, err := os.Create(output)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to create the output file '%s'", output)
	}

	return SaveImagesToWriter(context.Background(), fw, repoTags...)
}

func PullImages(ctx context.Context, repoTags ...string) error {
	if err := initDockerCli(); err != nil {
		return err
	}
	nctx, cancelF := context.WithCancel(ctx)
	defer cancelF()
	for _, repoTag := range repoTags {
		stdlog.Infof("pulling image '%s'", repoTag)
		rc, err := dockerCli.ImagePull(nctx, repoTag, types.ImagePullOptions{})
		if err != nil {
			stdlog.WithError(err).Errorf("failed to begin image pull ")
			return err
		}
		if err := NewPullProgress(rc, cancelF).Wait(); err != nil {
			stdlog.WithError(err).Errorf("failed to complete image pull ")
			return err
		}
	}
	return nil
}

func PushImages(ctx context.Context, repoTags ...string) error {
	if err := initDockerCli(); err != nil {
		return err
	}
	nctx, cancelF := context.WithCancel(ctx)
	defer cancelF()
	for _, repoTag := range repoTags {
		stdlog.Infof("pushing image '%s'", repoTag)
		rc, err := dockerCli.ImagePush(nctx, repoTag, types.ImagePushOptions{
			RegistryAuth: "{}", // This is necessary, or else will cause error : Bad parameters and missing X-Registry-Auth
		})
		if err != nil {
			stdlog.WithError(err).Errorf("failed to begin image push")
			return err
		}
		if err := NewPullProgress(rc, cancelF).Wait(); err != nil {
			stdlog.WithError(err).Errorf("failed to complete image push")
			return err
		}
	}
	return nil
}

func RetagImageRegistry(ctx context.Context, newRegistry string, repoTags ...string) ([]string, error) {
	if err := initDockerCli(); err != nil {
		return nil, err
	}
	if imgCache.needFlush() {
		if err := imgCache.flush(); err != nil {
			return nil, stderr.Wrap(err, "failed to flush images cache while checking the existence of image")
		}
	}
	nctx, cancelF := context.WithCancel(ctx)
	defer cancelF()

	result := make([]string, 0, len(repoTags))
	for _, repoTag := range repoTags {
		reference, err := dockerparser.Parse(repoTag)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to parse image: %s", repoTag)
		}
		newRepoTag := strings.Replace(repoTag, reference.Registry(), newRegistry, 1)
		if err := dockerCli.ImageTag(nctx, repoTag, newRepoTag); err != nil {
			return nil, err
		}
		result = append(result, newRepoTag)
		stdlog.Infof("retag image '%s' as '%s'", repoTag, newRepoTag)
	}
	return result, nil
}

func SaveImagesToWriter(ctx context.Context, w io.Writer, repoTags ...string) error {
	if err := initDockerCli(); err != nil {
		return err
	}

	rc, err := dockerCli.ImageSave(ctx, repoTags)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to save images '%+v'", repoTags)
	}
	defer func() {
		_ = rc.Close()
	}()

	stdlog.Infof("begin to save images...")
	if _, err := io.Copy(w, rc); err != nil {
		return stderr.Internal.Cause(err, "failed to copy images from http resp to file ")
	}
	stdlog.Infof("saving images done")
	return nil
}

// LoadImages load one or more images from a tar archive
// input : Read from tar archive file
func LoadImages(input string) error {
	exist, err := utils.Exists(input)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to exist path '%s'", input)
	}
	if !exist {
		return stderr.NotFound.Error("image file '%s' is not exists", input)
	}

	ctx := context.Background()
	fr, err := os.Open(input)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to open file '%s'", input)
	}

	return LoadImagesFromReader(ctx, fr)
}

func LoadImagesFromReader(ctx context.Context, r io.Reader) error {
	if err := initDockerCli(); err != nil {
		return err
	}

	stdlog.Infof("begin to load images ...")
	if _, err := dockerCli.ImageLoad(ctx, r, false); err != nil {
		return stderr.Internal.Cause(err, "failed to load images")
	}
	stdlog.Infof("loading images done")
	return nil
}

// ExistImages 检查给定的RepoTags是否存在
// 若任一给定的RepoTag不存在, 将返回error
func ExistImages(repoTags ...string) error {
	if len(repoTags) == 0 {
		return stderr.InvalidParam.Error("repo tags can not be empty")
	}
	if err := initDockerCli(); err != nil {
		return err
	}
	if imgCache.needFlush() {
		if err := imgCache.flush(); err != nil {
			return stderr.Wrap(err, "failed to flush images cache while checking the existence of image")
		}
	}
	// 检查给定的repo tags中是否存在未找到的镜像
	notFounds := make([]string, 0)
	for _, rt := range repoTags {
		if _, ok := imgCache.cache[rt]; ok {
			continue
		}
		notFounds = append(notFounds, rt)
	}

	// 若有任一镜像未找到, 则检查失败
	if len(notFounds) != 0 {
		return stderr.ImageNotFoundError.Error("following docker images not found : '%+v'", notFounds)
	}
	return nil
}

func initDockerCli() error {
	if dockerCli == nil {
		if dv := os.Getenv("DOCKER_CLIENT_VERSION"); dv != "" {
			DockerVersion = dv
		}
		dc, err := docker.NewClientWithOpts(docker.WithVersion(DockerVersion))
		if err != nil {
			return stderr.Internal.Cause(err, "failed to new docker client")
		}
		dockerCli = dc
		imgCache = &imagesCache{
			flushTime: time.Now().Add(-ImageCacheExpireDur), // 避免未刷新时被使用
		}
	}
	return nil
}

type imagesCache struct {
	cache      map[string]struct{} // 用于缓存系统镜像列表
	flushTime  time.Time           // 当前缓存中的镜像列表获取时间戳
	expireTime time.Time           // 当前缓存中镜像的过期时间
}

func (c *imagesCache) needFlush() bool {
	return time.Now().After(c.flushTime.Add(ImageCacheExpireDur))
}

func (c *imagesCache) flush() error {
	if err := initDockerCli(); err != nil {
		return err
	}

	// list all normal images
	summaries, err := dockerCli.ImageList(context.Background(), types.ImageListOptions{})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to list docker images")
	}

	// 缓存所有的镜像RepoTag
	newCache := make(map[string]struct{})
	for _, summary := range summaries {
		for _, rt := range summary.RepoTags {
			newCache[rt] = struct{}{}
		}
	}
	// 更新缓存时间
	c.cache = newCache
	c.flushTime = time.Now()
	c.expireTime = c.flushTime.Add(ImageCacheExpireDur)
	return nil
}
