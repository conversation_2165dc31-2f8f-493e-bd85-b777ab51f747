package dockerimage

import (
	"context"
	"testing"
)

func TestSaveImages(t *testing.T) {
	type args struct {
		output   string
		repoTags []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test save images",
			args: args{
				output: "/tmp/test_images.tar",
				repoTags: []string{
					"f2845b71cac1",
					"***********/aip/multimedia-plrunner:master",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SaveImages(tt.args.output, tt.args.repoTags...); (err != nil) != tt.wantErr {
				t.Errorf("SaveImages() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLoadImages(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "test load images",
			args:    args{
				input: "/tmp/test_images.tar",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := LoadImages(tt.args.input); (err != nil) != tt.wantErr {
				t.Errorf("LoadImages() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushImages(t *testing.T) {
	type args struct {
		ctx      context.Context
		repoTags []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test push not existed image",
			args: args{
				ctx:      context.Background(),
				repoTags: []string{"*************:31500/aip/deps/alpine:not-exist"},
			},
			wantErr: true,
		},{
			name: "test push not existed image",
			args: args{
				ctx:      context.Background(),
				repoTags: []string{"*************:31501/aip/deps/alpine:not-exist"},
			},
			wantErr: true,
		},{
			name: "test push exist image",
			args: args{
				ctx:      context.Background(),
				repoTags: []string{"*************:31500/aip/deps/alpine:master"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := PushImages(tt.args.ctx, tt.args.repoTags...); (err != nil) != tt.wantErr {
				t.Errorf("PushImages() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}