package toolkit

import (
	"regexp"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	UserNameRegx = "^[\u4E00-\u9FA5A-Za-z0-9_-]{2,30}$"

	FunctionIDRegx  = `^[a-z0-9-_]{1,50}$` // 由"a-z","0-9","-","_"组成, 长度为至少为1,至多为50
	FunctionIDBegin = `^[a-z0-9].*$`       // 仅能由小写字母或数字开头
	FunctionIDEnd   = `^.*[a-z0-9]$`       // 仅能由小写字母或数字结尾

)

var (
	regx       = regexp.MustCompile(UserNameRegx)
	funcIDRegs = []*regexp.Regexp{
		regexp.MustCompile(FunctionIDRegx),
		regexp.MustCompile(FunctionIDBegin),
		regexp.MustCompile(FunctionIDEnd),
	}
)

func ValidName(name string) error {
	if regx.MatchString(name) {
		return nil
	}
	return stderr.InvalidParam.Error("invalid name, only support letters, numbers, chinese characters, '-' and '_'")
}

// function id need to match FunctionNameRegx cause it is a component of image repo.
func ValidFunctionID(id string) error {
	for _, reg := range funcIDRegs {
		if !reg.MatchString(id) {
			return stderr.InvalidParam.Error("函数名'%s'非法,仅支持'a-z', '0-9', '-', '_'；且'-'和'_'不能出现在开头和结尾", id)
		}
	}
	return nil
}
