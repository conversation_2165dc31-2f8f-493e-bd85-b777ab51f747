package toolkit

import (
	"fmt"
	"os"
	"testing"
	"time"
)

func TestMkdirIfNotExist(t *testing.T) {
	existFile := "/tmp/test_mkdir_as_file"
	_,_ = os.Create(existFile)
	ts := time.Now().UnixNano()
	tests := []struct {
		name    string
		dir     string
		wantErr bool
	}{
		{
			name:    "正常创建",
			dir:     fmt.Sprintf("/tmp/%d/test",ts),
			wantErr: false,
		},
		{
			name:    "重复创建不应报错",
			dir:     fmt.Sprintf("/tmp/%d/test",ts),
			wantErr: false,
		},
		{
			name:    "已经存在同名文件，报错",
			dir:     existFile,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := MkdirIfNotExist(tt.dir); (err != nil) != tt.wantErr {
				t.<PERSON><PERSON>("MkdirIfNotExist() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
