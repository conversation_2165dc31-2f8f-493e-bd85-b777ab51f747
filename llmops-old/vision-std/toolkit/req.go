package toolkit

import (
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func ParseTagFilter(tagFilter string) (map[string]string, error) {
	res := make(map[string]string)
	if tagFilter != "" {
		for _, cond := range strings.Split(tagFilter, ",") {
			kv := strings.Split(cond, ":")
			if len(kv) != 2 {
				return nil, stderr.InvalidParam.Error("illegal filter format, expected format: A:a,B:b")
			}
			res[kv[0]] = kv[1]
		}

	}
	return res, nil
}
