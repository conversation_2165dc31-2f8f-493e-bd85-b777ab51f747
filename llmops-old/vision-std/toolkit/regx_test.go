package toolkit

import (
	"testing"
)

func TestValidName(t *testing.T) {
	tests := []struct {
		desc    string
		name    string
		wantErr bool
	}{
		{
			desc:    "非法字符 空格",
			name:    "哈 哈 哈",
			wantErr: true,
		},
		{
			desc:    "空字符",
			name:    "",
			wantErr: true,
		},
		{
			desc:    "正常",
			name:    "abc_def-ghi_123",
			wantErr: false,
		},
		{
			desc:    "字母长度超限",
			name:    "asalkjaisdflaksdf_adsflkjaiwermndfmnvc",
			wantErr: true,
		},
		{
			desc:    "正常",
			name:    "中石油Faas追踪规则-区域_301",
			wantErr: false,
		},
		{
			desc:    "长度超限",
			name:    "一二三四五一二三四五一二三四五一二三四五一二三四五一二三四五一",
			wantErr: true,
		}, {
			desc:    "长度极限",
			name:    "一二三四五一二三四五一二三四五一二三四五",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidName(tt.name); (err != nil) != tt.wantErr {
				t.Errorf("ValidName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
func TestValidFunctionName(t *testing.T) {
	tests := []struct {
		desc    string
		name    string
		wantErr bool
	}{
		{
			desc:    "valid 1",
			name:    "abcdetadgasd",
			wantErr: false,
		}, {
			desc:    "valid 2",
			name:    "abcdet-adgasd",
			wantErr: false,
		}, {
			desc:    "valid 3",
			name:    "1234123",
			wantErr: false,
		}, {
			desc:    "valid 4",
			name:    "1_a-b_cd",
			wantErr: false,
		}, {
			desc:    "invalid 1",
			name:    "_a-b_cd",
			wantErr: true,
		}, {
			desc:    "invalid 2",
			name:    "d-",
			wantErr: true,
		}, {
			desc:    "invalid 3",
			name:    "_",
			wantErr: true,
		}, {
			desc:    "invalid 4",
			name:    "acb=dsc",
			wantErr: true,
		}, {
			desc:    "invalid 5",
			name:    "哈哈",
			wantErr: true,
		},
		{
			desc:    "单字符",
			name:    "a",
			wantErr: false,
		},
		{
			desc:    "双字符",
			name:    "aa",
			wantErr: false,
		},
		{
			desc:    "下划线",
			name:    "_",
			wantErr: true,
		},
		{
			desc:    "下划线开头",
			name:    "_a",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.desc+tt.name, func(t *testing.T) {
			if err := ValidFunctionID(tt.name); (err != nil) != tt.wantErr {
				t.Errorf("ValidFunctionName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
