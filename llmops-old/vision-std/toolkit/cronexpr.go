package toolkit

import (
	"github.com/gorhill/cronexpr"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type CronExpression = cronexpr.Expression
type CronExprType string

const (
	// Linux Cron job
	// 1 2 3 4 5 Index
	// - - - - -
	// * * * * * command to be executed
	// - - - - -
	// | | | | |
	// | | | | ----- Day of week (0 - 7) (Sunday=0 or 7)
	// | | | ------- Month (1 - 12)
	// | | --------- Day of month (1 - 31)
	// | ----------- Hour (0 - 23)
	// ------------- Minute (0 - 59)
	LinuxCron CronExprType = "linux"

	// Spring Scheduled tasks
	// 1 2 3 4 5 6 7 Index
	// - - - - - - -
	// * * * * * * ? command to be executed
	// - - - - - - -
	// | | | | | | |
	// | | | | | | ----- Year (Optional) (1970 - 2099)
	// | | | | | ------- Day of week (MON - SUN)
	// | | | | --------- Month (1 - 12)
	// | | | ----------- Day of month (1 - 31)
	// | |-------------- Hour (0 - 23)
	// | --------------- Minute (0 - 59)
	// ----------------- Seconds (0 - 59)
	SpringCron CronExprType = "spring"

	CronExprSep       = " "         // Separator of fields in cron expr
	DefaultCronSecond = "0"         // Second field for spring cron expr
	DefaultCronYear   = "1970-2099" // Year field for spring cron expr
)

var CronErrCode = stderr.NewCode(stderr.Base, "Cron表达式错误", "Cron expression error")

// ParseCronExpr returns an CronExpression pointer.
func ParseCronExpr(expr string) (*CronExpression, error) {
	// check the type of cron expression
	fields := strings.Split(expr, CronExprSep)
	switch len(fields) {
	case 5:
		// add default second and year field for linux cron expr
		fields = append([]string{DefaultCronSecond}, append(fields, DefaultCronYear)...)
	case 6:
		// add default year field for spring cron expr
		fields = append(fields, DefaultCronYear)
	case 7:
		// do nothing for complete spring expr
	default:
		return nil, CronErrCode.Error("number of fields '%d' error, expected 5-7 fields", len(expr))
	}

	expr = strings.Join(fields, CronExprSep)
	res, err := cronexpr.Parse(expr)
	if err != nil {
		return nil, CronErrCode.Cause(err, "failed to parse cron expression")
	}
	return res, nil
}
