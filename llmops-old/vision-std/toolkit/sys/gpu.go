package sys

import (
	"github.com/pkg/errors"
	"runtime"
	"strconv"
	"strings"
)

type Gpu struct {
	Id    string `json:"gpu_id,omitempty"`
	Index int64  `json:"gpu_index,omitempty"`
	Model string `json:"gpu_model,omitempty"`

	MemoryFree  int64 `json:"memory_free"`
	MemoryUsed  int64 `json:"memory_used,omitempty"`
	MemoryTotal int64 `json:"memory_total,omitempty"`
}

// GetGpus gets all gpu information.
func GetGpus() ([]Gpu, error) {
	var gpus []Gpu
	if strings.HasPrefix(runtime.GOARCH, "amd") {
		out, err := Exec(`nvidia-smi --query-gpu=index,name,memory.total,memory.free,memory.used --format=csv,noheader,nounits`)
		if err != nil {
			return gpus, errors.Errorf("can't get cpu info: %v", err)
		}

		for _, raw := range strings.Split(out, "\n") {
			if strings.TrimSpace(raw) == "" {
				continue
			}
			var g Gpu
			t := strings.Split(raw, ",")
			total, err := strconv.Atoi(strings.TrimSpace(t[2]))
			if err != nil {
				return nil, err
			}
			free, err := strconv.Atoi(strings.TrimSpace(t[3]))
			if err != nil {
				return nil, err
			}
			used, err := strconv.Atoi(strings.TrimSpace(t[4]))
			if err != nil {
				return nil, err
			}
			g.Id = strings.TrimSpace(t[0])
			g.Model = strings.TrimSpace(t[1])
			g.MemoryTotal = int64(total)
			g.MemoryFree = int64(free)
			g.MemoryUsed = int64(used)
			gpus = append(gpus, g)
		}
		return gpus, nil
	}
	return nil, errors.Errorf("can't support getting gpu info")
}
