package sys

type EdgeClusterStatus = string

type EdgeClusterNodeRole = string
type EdgeClusterNodeStatus = string

const (
	EdgeClusterUp   EdgeClusterStatus   = "up"
	EdgeClusterDown EdgeClusterStatus   = "down"
	Master          EdgeClusterNodeRole = "master"
	Worker          EdgeClusterNodeRole = "worker"

	NodeUp   EdgeClusterNodeStatus = "up"
	NodeDown EdgeClusterNodeStatus = "down"

	MasterLabel = "node-role.kubernetes.io/master"
)

// NodeInfo 节点的基础配置信息
type NodeInfo struct {
	Name     string     `json:"name"` // hostname
	Host     string     `json:"host"` // ipaddress without port
	Status   string     `json:"status"`
	Os       string     `json:"os"`
	Kernel   string     `json:"kernel"`
	Arch     string     `json:"arch"`
	Gpus     []*Gpu     `json:"gpus,omitempty"`
	Resource *NodeStats `json:"resource"`
	Role     string     `json:"role"`
}
