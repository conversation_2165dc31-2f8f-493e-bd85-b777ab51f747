package sys

import (
	"os"
	"runtime"
	"time"

	"github.com/cloudfoundry/gosigar"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const VisionHostIPEnv = "VISION_EDGE_HOST"

// GetHostIpAddress 获取当前节点宿主机的实际 IP 地址
func GetHostIpAddress() (string, error) {
	host := os.Getenv(VisionHostIPEnv)
	if host == "" {
		return "", stderr.Internal.Error("environment ‘%s’ is necessary while getting host ip", VisionHostIPEnv)
	}
	return host, nil
}

// GetNodeStat 统计当前节点资源占用信息
func GetNodeStat() *NodeStats {
	disk := DiskStat("/")
	mem := MemStat()
	cpu := CpuStat()
	return &NodeStats{
		Timestamp:        time.Now(),
		CpuTotalCore:     int64(runtime.NumCPU()),
		CpuUsagePercent:  cpu.UsagePercent(),
		MemUsagePercent:  mem.UsagePercent(),
		DiskUsagePercent: disk.UsagePercent(),
		MemTotalByte:     int64(mem.All),
		DiskTotalByte:    int64(disk.All),
		Name:             "",
	}
}

type NodeResourceUsage struct {
	All  uint64 `json:"all"`
	Used uint64 `json:"used"`
	Free uint64 `json:"free"`
}

func (u NodeResourceUsage) UsagePercent() float64 {
	if u.All == 0 {
		return 0
	}
	return float64(u.Used) / float64(u.All)
}

// disk usage of path/disk
func DiskStat(path string) (disk NodeResourceUsage) {
	fu := sigar.FileSystemUsage{}
	if err := fu.Get(path);err != nil {
		stdlog.WithError(err).Warnf("get file system usage")
		return
	}
	disk = NodeResourceUsage{
		All:  fu.Total,
		Used: fu.Used,
		Free: fu.Free,
	}
	return
}

func MemStat() (mem NodeResourceUsage) {
	m := sigar.Mem{}
	err := m.Get()
	if err != nil {
		stdlog.WithError(err).Warnf("get mem stat")
		return
	}
	mem = NodeResourceUsage{
		All:  m.Total,
		Used: m.Used,
		Free: m.Free,
	}
	return
}

func CpuStat() (cpu NodeResourceUsage) {
	if cpuStat, err := GetCpu(); err == nil {
		cpu.All = cpuStat.Total
		cpu.Free = cpuStat.Idle + cpuStat.Wait
		cpu.Used = cpu.All - cpu.Free
	}
	return
}
