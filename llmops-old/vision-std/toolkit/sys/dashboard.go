package sys

import "time"

type Dashboard struct {
	ID          string   `json:"dashboard_id"`
	UI          string   `json:"dashboard_ui"`
	Name        string   `json:"dashboard_name"`
	Desc        string   `json:"dashboard_desc"`
	Panels      []*Panel `json:"panels"`
	IsDefault   bool     `json:"is_default"`
	CreatedTime int64    `json:"created_time"` // micro second
	UpdatedTime int64    `json:"updated_time"` // micro second
}

type DisplayType string
type PanelDataSrcType string

const (
	// 图表展现形式
	LinePanel  DisplayType = "line"  // 折线图
	CardPanel  DisplayType = "card"  // 卡片
	VideoPanel DisplayType = "video" // 视频
	SlidePanel DisplayType = "slide" // 轮播图

	// 数据来源类型
	AlertDataSrc         PanelDataSrcType = "alert"          // 消息告警->轮播图
	TickDeviceDataSrc    PanelDataSrcType = "tick_device"    // 时序设备(属性,事件字段)->折线图,卡片
	MediaDeviceDataSrc   PanelDataSrcType = "media_device"   // 多媒体设备->视频
	TickInstanceDataSrc  PanelDataSrcType = "tick_instance"  // 时序规则实例->折线图,卡片
	MediaInstanceDataSrc PanelDataSrcType = "media_instance" // 多媒体规则实例->视频
)

type Panel struct {
	ID            string           `json:"panel_id"`
	Name          string           `json:"panel_name"`
	Desc          string           `json:"panel_desc"`
	DisplayType   DisplayType      `json:"display_type"`
	DataSrcType   PanelDataSrcType `json:"data_src_type"`
	DataSrcConfig string           `json:"data_src_config"`
}

// NodeStats 节点资源使用情况统计信息
type NodeStats struct {
	Timestamp time.Time
	Name      string `json:"name"`

	CpuTotalCore    int64   `json:"cpu_total_core"`
	CpuUsagePercent float64 `json:"cpu_usage_percent"`

	GpuTotalByte    int64   `json:"gpu_total_byte"`
	GpuUsagePercent float64 `json:"gpu_usage_percent"`

	MemTotalByte    int64   `json:"mem_total_byte"`
	MemUsagePercent float64 `json:"mem_usage_percent"`

	DiskTotalByte    int64   `json:"disk_total_byte"`
	DiskUsagePercent float64 `json:"disk_usage_percent"`

	// AdditionalInfo 为节点的status以及metrics额外补充信息
	AdditionalInfo map[string]interface{} `json:"external_info,omitempty"`
}
