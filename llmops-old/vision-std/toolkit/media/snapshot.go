package media

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"image"
	"image/jpeg"
	"os/exec"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

const (
	rtspPrefix = "rtsp://"
)

var (
	ffmpegPath = "/usr/local/srs/objs/ffmpeg/bin/ffmpeg" // 默认ffmpeg工具所在路径
	rtspFlags  = []string{"-rtsp_flags", "prefer_tcp"}   // 避免RTSP视频流默认使用UDP建立连接超时问题
)

// SetFFmpegPath 修改截图时所用的ffmpeg工具所在路径
func SetFFmpegPath(p string) {
	ffmpegPath = p
}

// GetSnapshotFromStream 返回JPEG截图文件的所有内容
func GetSnapshotFromStream(timeout time.Duration, stream string) ([]byte, error) {
	var buf bytes.Buffer
	cmd := newFFmpegSnapshotCMD(0, 0, stream, "")
	cmd.Stdout = &buf
	// 执行ffmpeg命令
	if err := toolkit.RunCommandWithTimeout(timeout, cmd); err != nil {
		return nil, stderr.Internal.Cause(err, "could not generate snapshot from input '%s'", stream)
	}
	return buf.Bytes(), nil
}

// GetSnapshotBase64FromStream 返回截图内容的Base64编码后的字符串
func GetSnapshotBase64FromStream(timeout time.Duration, stream string) (string, error) {
	bs, err := GetSnapshotFromStream(timeout, stream)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(bs), nil
}

// SaveThumbnailFromStream 从指定的视频输入地址，获取单帧图片, 并保存至指定路径
func SaveThumbnailFromStream(timeout time.Duration, width, height int, stream string, outPath string) error {
	cmd := newFFmpegSnapshotCMD(width, height, stream, outPath)

	// 执行ffmpeg命令
	if err := toolkit.RunCommandWithTimeout(timeout, cmd); err != nil {
		return stderr.Internal.Cause(err, "could not generate thumbnail from input '%s'", stream)
	}
	return nil
}

// GetThumbnailImageFromStream 从指定的视频输入地址，获取单帧图片，并已image.Image的格式进行返回
func GetThumbnailImageFromStream(timeout time.Duration, width, height int, stream string) (image.Image, error) {
	var buf bytes.Buffer
	cmd := newFFmpegSnapshotCMD(width, height, stream, "")
	cmd.Stdout = &buf

	// 执行ffmpeg命令
	if err := toolkit.RunCommandWithTimeout(timeout, cmd); err != nil {
		return nil, stderr.Internal.Cause(err, "could not generate thumbnail from input '%s'", stream)
	}

	// 将图片二进制数据进行解码
	img, err := jpeg.Decode(bytes.NewReader(buf.Bytes()))
	if err != nil {
		return nil, stderr.Internal.Cause(err, "failed to decode jpeg thumbnail")
	}
	return img, nil
}

// Mp4File2Flv 将mp4文件转换为flv
// e.g.
// ffmpeg -i input.mp4 -vcodec copy -an -flvflags add_keyframe_index output.flv
func Mp4File2Flv(in, out string) error {
	if !strings.HasSuffix(in, ".mp4") {
		return stderr.InvalidParam.Error("input file must be end with .mp4")
	}
	if !strings.HasSuffix(out, ".flv") {
		return stderr.InvalidParam.Error("out file must be end with .flv")
	}
	cmd := newFFmpegConvertFileCMD(in, out)
	return toolkit.RunCommand(cmd)
}

// newFFmpegConvertFileCMD 返回用于视频文件格式转换的命令
func newFFmpegConvertFileCMD(in, out string) *exec.Cmd {
	var args []string

	// 指定ffmpeg执行文件位置
	args = append(args, ffmpegPath)
	// 输入视频流地址（需要用引号括起来，避免流地址中包含特殊字符. e.g. '&'）
	args = append(args, "-i", fmt.Sprintf("'%s'", in))
	// 不修改码流信息
	args = append(args, "-vcodec", "copy")
	// 覆盖可能存在的文件
	args = append(args, "-y")
	// 输出到指定路径
	args = append(args,  fmt.Sprintf("'%s'", out))

	// 指定使用 /bin/sh 运行该命令， 避免不同shell语法不同导致的不兼容
	ffmpegCMD := strings.Join(args, " ")

	return exec.Command("/bin/sh", "-c", ffmpegCMD)
}

// newFFmpegSnapshotCMD 返回用于生成视频流的JPEG格式编码截图的命令
// * 若 width/height 为 0 或 负值，则不对原始视频分辨率进行修改
// * 若 outPath 为空，则JPEG格式编码的图片将输出至Cmd的StdOut中；
// * 若 outPath 非空，则JPEG图片将直接存储至指定的文件路径， 此时Cmd执行输出为空
func newFFmpegSnapshotCMD(width, height int, stream string, outPath string) *exec.Cmd {
	var args []string

	// 指定ffmpeg执行文件位置
	args = append(args, ffmpegPath)
	// 避免RTSP视频流默认使用UDP建立连接超时问题
	if strings.HasPrefix(strings.ToLower(stream), rtspPrefix) {
		args = append(args, rtspFlags...)
	}
	// 输入视频流地址（需要用引号括起来，避免流地址中包含特殊字符. e.g. '&'）
	args = append(args, "-i", fmt.Sprintf("'%s'", stream))
	// 输出视频帧数量
	args = append(args, "-vframes", "1")
	// 输出图片格式
	args = append(args, "-f", "singlejpeg")
	// 配置输出图片尺寸
	if width > 0 && height > 0 {
		args = append(args, "-s", fmt.Sprintf("%dx%d", width, height))
	}
	// 配置输出文件路径
	if outPath != "" {
		// 输出到指定路径，并覆盖已存在文件
		args = append(args, "-y", outPath)
	} else {
		// 输出到Stdout
		args = append(args, "-")
	}

	// 指定使用 /bin/sh 运行该命令， 避免不同shell语法不同导致的不兼容
	ffmpegCMD := strings.Join(args, " ")
	return exec.Command("/bin/sh", "-c", ffmpegCMD)
}
