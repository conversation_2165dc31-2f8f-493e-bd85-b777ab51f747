package media

import (
	"testing"
	"time"
)

func TestSaveThumbnailFromStream(t *testing.T) {
	type args struct {
		timeout time.Duration
		width   int
		height  int
		stream  string
		outPath string
	}
	var tests = []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "",
			args:    args{
				timeout: time.Minute,
				width:   300,
				height:  300,
				stream:  "rtsp://admin:transwarp123@172.16.184.247:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1",
				outPath: "./test.jpg",
			},
			wantErr: false,
		},
	}
	ffmpegPath = "/tmp/ffmpeg"
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SaveThumbnailFromStream(tt.args.timeout, tt.args.width, tt.args.height, tt.args.stream, tt.args.outPath); (err != nil) != tt.wantErr {
				t.Errorf("SaveThumbnailFromStream() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
