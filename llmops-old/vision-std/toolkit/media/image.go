package media

import (
	"bytes"
	"encoding/base64"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"os"
	"path"

	"github.com/nfnt/resize"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

// ResizeImage2Base64 将给定的原始图像resize为给定尺寸，并返回对应的Base64字符串
func ResizeImage2Base64(img image.Image, width, height uint) (string, error) {
	// resize image to giving width and height using Lanczos resampling
	// and preserve aspect ratio
	m := resize.Resize(width, height, img, resize.Lanczos3)

	// write new image to a buffer
	buf := bytes.NewBuffer(nil)
	if err := jpeg.Encode(buf, m, nil); err != nil {
		return "", stderr.Internal.Cause(err, "failed to encode image while resize jpg")
	}

	// convert image data to base64 string
	res := base64.StdEncoding.EncodeToString(buf.Bytes())
	return res, nil
}

// GetImgFromFile 从本地文件系统中读取给定的图片内容
func GetImgFromFile(imagePath string) (image.Image, error) {
	f, err := GetContentFromFile(imagePath)
	if err != nil {
		return nil, err
	}
	return DecodeImage(imagePath, f)
}

// DecodeImage 根据给定内容Reader, 以及对应的文件路径扩展名，返回图像内容
// * jpg/jpeg, png 返回图像内容
func DecodeImage(imgPath string, rc io.ReadCloser) (image.Image, error) {
	defer func(rc io.ReadCloser) {
		_ = rc.Close()
	}(rc)

	var err error
	var img image.Image
	switch path.Ext(imgPath) {
	case ".gif":
		img, err = gif.Decode(rc)
	case ".jpg", "jpeg":
		img, err = jpeg.Decode(rc)
	case ".png":
		img, err = png.Decode(rc)

	default:
		return nil, stderr.Unsupported.Error("unsupported file name extension '%s'", path.Ext(imgPath))
	}
	// decode jpeg/gif into image.Image
	if err != nil {
		return nil, stderr.Internal.Cause(err, "decode image: %s", imgPath)
	}
	return img, nil
}

// GetContentFromFile 从本地文件系统中获取给定路径的文件内容
func GetContentFromFile(path string) (io.ReadCloser, error) {
	// open the image file
	file, err := os.Open(path)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "open image: %s", path)
	}
	return file, nil
}
