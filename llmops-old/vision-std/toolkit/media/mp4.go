package media

import (
	"bytes"
	"encoding/binary"
	"os"
)

func GetMP4Duration(name string) (uint32, error) {
	reader, err := os.Open(name)
	if err != nil {
		return 0, err
	}
	var info = make([]byte, 0x10)
	boxHeader := struct {
		Size       uint32
		FourccType [4]byte
		Size64     uint64
	}{}
	var offset int64 = 0
	// 获取moov结构偏移
	for {
		_, err := reader.ReadAt(info, offset)
		if err != nil {
			return 0, err
		}
		buf := bytes.NewBuffer(info)
		binary.Read(buf, binary.BigEndian, &boxHeader)
		fourccType := string(boxHeader.FourccType[:])
		if fourccType == "moov" {
			break
		}
		// 有一部分mp4 mdat尺寸过大需要特殊处理
		if fourccType == "mdat" {
			if boxHeader.Size == 1 {
				offset += int64(boxHeader.Size64)
				continue
			}
		}
		offset += int64(boxHeader.Size)
	}
	// 获取moov结构开头一部分
	moovStartBytes := make([]byte, 0x100)
	if _, err := reader.ReadAt(moovStartBytes, offset); err != nil {
		return 0, err
	}
	// 定义timeScale与Duration偏移
	timeScaleOffset := 0x1C
	durationOffest := 0x20
	timeScale := binary.BigEndian.Uint32(moovStartBytes[timeScaleOffset : timeScaleOffset+4])
	Duration := binary.BigEndian.Uint32(moovStartBytes[durationOffest : durationOffest+4])
	length := Duration / timeScale
	return length, nil
}
