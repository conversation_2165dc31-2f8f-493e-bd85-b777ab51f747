package media

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/fatih/structs"
	"gopkg.in/vansante/go-ffprobe.v2"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

//TODO  暂时获取分辨率等字段，兼容前端，后续仅保存原生流信息。
func GetStreamMetaInfo(location string, timeout time.Duration) (map[string]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	opts := make([]string, 0)
	if strings.HasPrefix(strings.ToLower(location), rtspPrefix) {
		opts = append(opts, rtspFlags...)
	}
	data, err := ffprobe.ProbeURL(ctx, location, opts...)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to probe stream %s", location)
	}
	if len(data.Streams) == 0 {
		return nil, fmt.Errorf("the %s does not contain stream", location)
	}
	streamInfo := make(map[string]string)
	ma := structs.Map(data.FirstVideoStream())
	for k, v := range ma {
		switch v.(type) {
		case int, uint64:
			streamInfo[k] = fmt.Sprintf("%v", v)
		case string:
			if v.(string) == "" {
				continue
			}
			streamInfo[k] = v.(string)
		default:
			//TODO  存在嵌套结构体等其他信息，如果需要可以获取
			continue
		}
	}
	b, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal:%+v", data)
	}
	streamInfo["ffprobe_info"] = string(b)

	return streamInfo, nil
}

func IsDeviceOffline(err error) bool {
	if strings.Contains(err.Error(), "signal: killed") || strings.Contains(err.Error(), "exit status") {
		return true
	}
	return false
}
