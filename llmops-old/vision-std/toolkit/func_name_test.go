package toolkit

import "testing"

func funcNameTest() {

}

func TestGetFunctionName(t *testing.T) {
	test := struct {
		f               interface{}
		nameWant        string
		nameWithPkgWant string
	}{
		f:               funcNameTest,
		nameWant:        "funcNameTest",
		nameWithPkgWant: "transwarp.io/applied-ai/aiot/vision-std/toolkit.funcNameTest",
	}
	t.Run("test get func name", func(t *testing.T) {
		if got := GetFunctionName(test.f); got != test.nameWant {
			t.Errorf("GetFunctionName() = %v, want %v", got, test.nameWant)
		}
	})

	t.Run("test get func name with pkg", func(t *testing.T) {
		if got := GetFunctionNameWithPkg(test.f); got != test.nameWithPkgWant {
			t.Errorf("GetFunctionName() = %v, want %v", got, test.nameWithPkgWant)
		}
	})
}

func TestGetCallerFunctionName(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		{
			name: "",
			want: "TestGetCallerFunctionName",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetCallerFunctionName(); got != tt.want {
				t.Errorf("GetCallerFunctionName() = %v, want %v", got, tt.want)
			}
		})
	}
}
