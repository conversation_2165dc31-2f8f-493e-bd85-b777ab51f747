package toolkit

import (
	"errors"
	"fmt"
	"time"

	"github.com/beevik/ntp"
)

// NowNS returns a Unix time, the number of nanoseconds elapsed
// since January 1, 1970 UTC.
func NowNS() int64 {
	return time.Now().UnixNano()
}

// NowNS returns a Unix time, the number of millisecond elapsed
// since January 1, 1970 UTC.
func NowMS() int64 {
	return time.Now().UnixNano() / 1e6
}

func FormatMsSeconds(t int64) string {
	return time.Unix(0, t*1e6).Format("20060102T150202.123")
}

func FormatNanoSeconds(t int64) string {
	return time.Unix(0, t).Format("20060102T150405.999999999")
}

func GetCloudTime(host string, port int) (time.Time, error) {
	res, err := ntp.QueryWithOptions(host, ntp.QueryOptions{
		Port: port,
	})
	if err != nil {
		return time.Time{}, err
	}
	if err := ntpValidate(res); err != nil {
		return time.Time{}, err
	}
	return time.Now().Add(res.ClockOffset), nil
}

func ntpValidate(r *ntp.Response) error {
	// Handle invalid stratum values.
	if r.Stratum == 0 {
		return fmt.Errorf("kiss of death received: %s", r.KissCode)
	}
	if r.Stratum >= 16 {
		return errors.New("invalid stratum in response")
	}

	// Handle invalid leap second indicator.
	if r.Leap == ntp.LeapNotInSync {
		return errors.New("invalid leap second")
	}

	// Calculate the peer synchronization distance, lambda:
	//  	lambda := RootDelay/2 + RootDispersion
	// If this value exceeds MAXDISP (16s), then the time is not suitable
	// for synchronization purposes.
	// https://tools.ietf.org/html/rfc5905#appendix-A.5.1.1.
	lambda := r.RootDelay/2 + r.RootDispersion
	if lambda > 16*time.Second {
		return errors.New("invalid dispersion")
	}

	// If the server's transmit time is before its reference time, the
	// response is invalid.
	if r.Time.Before(r.ReferenceTime) {
		return errors.New("invalid time reported")
	}

	// nil means the response is valid.
	return nil
}

var ShanghaiZone = time.FixedZone("UTC", 8*3600)

// TimeString will return the string format of current time with fixed zone (Asia/Shanghai).
// String format is "yyyyMMdd_HHmmss"
func TimeString() string {
	return time.Now().In(ShanghaiZone).Format("20060102_150405")
}

// ParseUnixTimestamp 通过判断时间戳的长度推测其单位, 并解析为time.Time 格式
func ParseUnixTimestamp(timestamp int64) (time.Time, bool) {
	switch {
	case timestamp >= 1e18:
		return time.Unix(0, timestamp), true
	case timestamp >= 1e15:
		return time.UnixMicro(timestamp), true
	case timestamp >= 1e12:
		return time.UnixMilli(timestamp), true
	case timestamp >= 1e9:
		return time.Unix(timestamp, 0), true
	default:
		return time.Time{}, false
	}
}

// ConvertUnixTimestamp 通过判断时间戳的长度推测其单位, 并解析为time.Time 格式
// 解析失败时将返回一个 time.Time.IsZero === true
func ConvertUnixTimestamp(timestamp int64) time.Time {
	t, _ := ParseUnixTimestamp(timestamp)
	return t
}
