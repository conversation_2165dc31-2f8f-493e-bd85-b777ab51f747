package utils

import (
	"net"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type ConnectionStatus string

const (
	NetworkTCP     string        = "tcp"
	TCPTimeOut     time.Duration = 3 * time.Second
	ConnectSucceed               = "succeed"
	ConnectFailed                = "failed"
)

// GetConnStatus 检查tcp连接状态
// 参数：
//   - address: 要检查连接状态的服务器地址，形式为{host}:{port}, eggs: 127.0.0.1:8080
//
// 返回值：
//   - 连接状态，根据连接结果返回不同的状态值 ConnectSucceed ConnectFailed
func GetConnStatus(address string) ConnectionStatus {
	conn, err := net.DialTimeout(NetworkTCP, address, TCPTimeOut)
	if err != nil {
		stdlog.Warnf("Failed to connect to %s, err: %v", address, err)
		return ConnectFailed
	}
	defer conn.Close() // 确保连接在函数返回前关闭
	// 连接成功，返回成功状态
	return ConnectSucceed
}
