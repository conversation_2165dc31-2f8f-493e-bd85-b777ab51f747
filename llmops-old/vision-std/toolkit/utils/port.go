package utils

import (
	"errors"
	"fmt"
	"math"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	MaxDOP = 8192 // 检查端口的最大并行度
)

// GetPortAvailable finds an available port
func GetPortAvailable(host string) (int, error) {
	address, err := net.ResolveTCPAddr("tcp", host+":0")
	if err != nil {
		return 0, err
	}

	listener, err := net.ListenTCP("tcp", address)
	if err != nil {
		return 0, err
	}
	defer listener.Close()
	return listener.Addr().(*net.TCPAddr).Port, nil
}

func GetOutboundIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)

	return localAddr.IP.String()
}

const PortRangeSep = "-"

func BuildPortRange(min, max int) string {
	return fmt.Sprintf("%d%s%d", min, PortRangeSep, max)
}

func ParsePortRange(prange string) (min, max int, err error) {
	min, max = -1, -1
	invalidRangeErr := stderr.InvalidParam.Error("invalid port range '%s'", prange)
	parts := strings.Split(prange, PortRangeSep)
	if len(parts) != 2 {
		err = invalidRangeErr
		return
	}
	min, err1 := strconv.Atoi(parts[0])
	max, err2 := strconv.Atoi(parts[1])
	if err1 != nil || err2 != nil || min > max {
		err = invalidRangeErr
		return
	}

	return min, max, nil
}

// GetContinuousFreePort 返回给定的端口范围中，指定数量与类型的，连续空闲的端口
// network [tcp / udp]
// num 连续端口数量
// min 最小端口号
// max 最大端口号
func GetContinuousFreePort(network string, hostname string, num int, min, max int) (start, end int, err error) {
	notEnoughErr := stderr.InvalidParam.Error("cannot found %d enough port in range [%d, %d]", num, min, max)
	invalidRangeErr := stderr.InvalidParam.Error("invalid port range [%d, %d]", min, max)
	invalidNetworkErr := stderr.InvalidParam.Error("invalid network type %s, optional range is [tcp, udp]", network)
	if min <= 0 || max > math.MaxUint16 || min > max {
		err = invalidRangeErr
		return
	}
	if num > max-min+1 {
		err = notEnoughErr
		return
	}

	var porter Porter
	switch network {
	case "tcp":
		if IsLocalHost(hostname) {
			porter = tcpPorter{}
		} else {
			porter = remoteTcpPorter{hostname}
		}
	case "udp":
		if IsLocalHost(hostname) {
			porter = udpPorter{}
		} else {
			porter = remoteUdpPorter{hostname}
		}
	default:
		err = invalidNetworkErr
		return
	}

	// scan all ports in range
	size := max - min + 1
	ports := make([]bool, size)
	var wg sync.WaitGroup
	wg.Add(size)
	ch := make(chan struct{}, MaxDOP)
	for i := 0; i < size; i++ {
		ch <- struct{}{}
		go func(i int) {
			defer wg.Done()
			ports[i] = porter.IsPortFree(min + i)
			<-ch
		}(i)
	}
	wg.Wait()
	// get continuous range
	start = -1
	end = 0
	for end < size {
		if ports[end] && end-start >= num {
			start += min + 1
			end += min
			return
		}
		if !ports[end] {
			start = end
		}
		end++
	}

	return -1, -1, notEnoughErr
	//for port := min; port <= max; port++ {
	//	free := porter.IsPortFree(port)
	//	if !free {
	//		continue
	//	}
	//
	//	// find enough continuous free port
	//	start = port
	//	success := true
	//	for i := 1; i < num; i++ {
	//		if ok := porter.IsPortFree(port + i); !ok {
	//			// 第i+1个端口不可用
	//			success = false
	//			port = port + i
	//			start, end = -1, -1
	//			break
	//		}
	//	}
	//	// 已找到足够可用的连续端口
	//	if success {
	//		end = start + num - 1
	//		return
	//	}
	//}
	//return -1, -1, notEnoughErr

}

func IsPortFree(network string, port int) bool {
	var porter Porter
	switch network {
	case "tcp":
		porter = tcpPorter{}
	case "udp":
		porter = udpPorter{}
	default:
		stdlog.Warnf("invalid network type %s while checking if port is free, optional range is [tcp, udp]", network)
		return false
	}
	return porter.IsPortFree(port)
}

type Porter interface {
	GetFreePort() (port int, err error)
	IsPortFree(port int) bool
}

type tcpPorter struct{}

// Get a free port.
func (tcpPorter) GetFreePort() (port int, err error) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		return 0, err
	}
	defer func() { _ = listener.Close() }()

	addr := listener.Addr().String()
	_, portString, err := net.SplitHostPort(addr)
	if err != nil {
		return 0, err
	}

	return strconv.Atoi(portString)
}

func (tcpPorter) IsPortFree(port int) bool {
	conn, err := net.Listen("tcp", net.JoinHostPort("127.0.0.1", fmt.Sprintf("%d", port)))
	if err != nil {
		return false
	}
	_ = conn.Close()
	return true
}

type udpPorter struct{}

func (udpPorter) GetFreePort() (port int, err error) {
	conn, err := net.ListenPacket("udp", "127.0.0.1:0")
	if err != nil {
		return 0, err
	}
	defer func() { _ = conn.Close() }()

	addr := conn.LocalAddr().String()
	_, portString, err := net.SplitHostPort(addr)
	if err != nil {
		return 0, err
	}

	return strconv.Atoi(portString)
}

func (udpPorter) IsPortFree(port int) bool {
	conn, err := net.ListenPacket("udp", net.JoinHostPort("127.0.0.1", fmt.Sprintf("%d", port)))
	if err != nil {
		return false
	}
	_ = conn.Close()
	return true
}

type remoteTcpPorter struct {
	hostname string
}

func (r remoteTcpPorter) GetFreePort() (port int, err error) {
	// skip port 0-1023
	for i := 1024; i <= 65535; i++ {
		if r.IsPortFree(i) {
			port = i
			return
		}
	}
	return -1, errors.New("There are no free ports")
}

func (r remoteTcpPorter) IsPortFree(port int) bool {
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(r.hostname, strconv.Itoa(port)), time.Second)
	if err != nil {
		return true
	}
	_ = conn.Close()
	return false
}

type remoteUdpPorter struct {
	hostname string
}

func (r remoteUdpPorter) GetFreePort() (port int, err error) {
	// skip port 0-1023
	for i := 1024; i <= 65535; i++ {
		if r.IsPortFree(i) {
			port = i
			return
		}
	}
	return -1, errors.New("There are no free ports")
}

// IsPortFree checks whether a remote udp port is free
// This function's check isn't entirely right. It assumes that a process binding the remote udp port would respond the message. Otherwise, there are so many disturbances: firewall, network and so on.
func (r remoteUdpPorter) IsPortFree(port int) bool {
	srcAddr := &net.UDPAddr{IP: net.IPv4zero, Port: 0}
	dstAddr := &net.UDPAddr{IP: net.ParseIP(r.hostname), Port: port}
	conn, err := net.DialUDP("udp", srcAddr, dstAddr)
	if err != nil { // 正常情况不应该有error，udp无连接，不管远程是否bind端口，都应能成功调用DialUDP
		stdlog.Errorln("Error when DialUDP in remoteUdpPorter.IsPortFree:", err)
		return true
	}
	defer conn.Close()
	err = conn.SetDeadline(time.Now().Add(time.Second))
	if err != nil {
		stdlog.Errorln("SetDeadline failed:", err)
	}
	// test whether the remote port responds
	_, err = conn.Write([]byte("hello"))
	if err != nil {
		return true
	}
	bs := make([]byte, 8)
	_, err = conn.Read(bs)
	return err != nil // if there is a response, determine the port is not free
}

func IsLocalHost(hostname string) bool {
	return hostname == "" || hostname == "127.0.0.1" || hostname == "localhost"
}
