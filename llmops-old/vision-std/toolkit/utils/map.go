package utils

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func StructToMap(s any) (map[string]any, error) {
	bs, err := json.Marshal(s)
	if err != nil {
		return nil, stderr.Wrap(err, "marshal struct")
	}

	kvs := make(map[string]any)
	if err = json.Unmarshal(bs, &kvs); err != nil {
		return nil, stderr.Wrap(err, "unmarshal to map")
	}
	return kvs, nil
}
func GetEnumKeys(values map[string]int32) []string {
	keys := make([]string, 0)
	for k, _ := range values {
		keys = append(keys, k)
	}
	return keys
}
