package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-test/deep"
)

// Equal compares two struct data
func Equal(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

// Diff compares variables a and b and returns a list of differences,
// or nil if there are none. Some differences may not be found if an error is
// also returned.
//
// If a type has an Equal method, like time.Equal, it is called to check for
// equality.
//
// When comparing a struct, if a field has the tag `deep:"-"` then it will be
// ignored.
func Diff(a, b interface{}) []string {
	return deep.Equal(a, b)
}

func FormatDiffs(diffs []string) string {
	return fmt.Sprintf("diffrences: \n\t%s\n", strings.Join(diffs, "\n\t"))
}

// JsonEqual reports whether json string of x and y are equal.
//
// This method used to avoid the impact of configuration 'omitempty'
func JsonEqual(a, b interface{}) bool {
	aj, _ := json.Marshal(a)
	bj, _ := json.Marshal(b)
	return reflect.DeepEqual(aj, bj)
}

// JsonDiff compares variables a and b and returns a list of differences,
// or nil if there are none.
// It will marshal variables to json then unmarshal to a common struct,
// then do diff.
func JsonDiff(a, b interface{}) []string {
	aj, _ := json.Marshal(a)
	bj, _ := json.Marshal(b)
	as, bs := make(map[string]interface{}), make(map[string]interface{})
	_ = json.Unmarshal(aj, &as)
	_ = json.Unmarshal(bj, &bs)
	return Diff(as, bs)
}
