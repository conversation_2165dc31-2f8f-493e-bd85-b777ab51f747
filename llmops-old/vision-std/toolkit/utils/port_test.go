package utils

import (
	"fmt"
	"net"
	"testing"
)

func TestGetPortAvailable(t *testing.T) {
	got, err := GetPortAvailable("127.0.0.1")
	fmt.Println(got, err)
	got, err = GetPortAvailable("0.0.0.0")
	fmt.Println(got, err)
}

func TestGetFreePort(t *testing.T) {
	tp := tcpPorter{}
	up := udpPorter{}
	for i := 0; i < 5; i++ {
		gotTcpPort, err := tp.GetFreePort()
		if err != nil {
			t.Errorf("GetFreeTCPPort() error = %v", err)
			return
		}
		t.Logf("GetFreeTCPPort() gotPort = %v", gotTcpPort)

		gotUDPPort, err := up.GetFreePort()
		if err != nil {
			t.Errorf("GetFreeUDPPort() error = %v", err)
			return
		}
		t.Logf("GetFreeUDPPort() gotPort = %v", gotUDPPort)
	}

	start, end, err := GetContinuousFreePort("tcp", "localhost", 3, 30100, 30200)
	if err != nil {
		t.Fatalf("failed to GetContinuousFreePort()")
	}
	for i := start; i <= end; i++ {
		if !IsPortFree("tcp", i) {
			t.Fatalf("port %d is not free", i)
		}
	}
	lis, err := net.Listen("tcp", fmt.Sprintf("127.0.0.1:%d", start))
	if err != nil {
		t.Fatal(err)
	}
	defer lis.Close()
	if IsPortFree("tcp", start) {
		t.Fatalf("tcp port %d should not was free", start)
	}
	if !IsPortFree("udp", start) {
		t.Fatalf("udp port %d should was free", start)
	}
}

func TestGetContinuousFreePort(t *testing.T) {
	// assume that 22, 80 port has been used at *************
	start, end, err := GetContinuousFreePort("tcp", "*************", 20, 20, 100)
	if err != nil {
		t.Fatal("GetContinuousFreePort error:", err)
	}
	if start != 23 || end != 42 {
		t.Fatalf("continuous free port range should be 23-42")
	}
}
