package utils

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

var (
	pubKey = []byte(`
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZfKVxz7eoITkGj8GTZuuGyx1l
CjYbyamsA6UFwLtV4gDttaCcumChO8eIrGEEuThhqC2u7WFKjFazmP7DYoPyheUx
DjkUn1CJxaoSTkSlghN4XJ22XAqqrpsjloO3j6UHmsQokHpdrzJv2B/o+ojjkcH5
5IC1aeGBYM4XDb2o8wIDAQAB
-----END PUBLIC KEY-----`)
	priKey = []byte(`
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
)

func TestRSAEncryptDecrypt(t *testing.T) {
	var tests = [][]byte{
		[]byte("hello, tg boy, haha ??????,智能边缘"),
		{0xff, 0xff, 0xff, 0x00, 0x00},
		{0x00, 0x00, 0x00, 0x00, 0x00},
		{0xff, 0xff, 0xff, 0xff, 0xff},
		{0xff},
		{0x00},
		{},
	}
	for _, data := range tests {
		assertPrivateEncryptPublicDecrypt(t, data)
		assertPublicEncryptPrivateDecrypt(t, data)
	}
}

func assertPrivateEncryptPublicDecrypt(t *testing.T, data []byte) {
	EncData, err := RsaPrivateEncrypt(data, priKey)
	assert.Nil(t, err)
	DecData, err := RsaPublicDecrypt(EncData, pubKey)
	assert.Nil(t, err)
	assertArrayEqual(t, data, DecData)
}

func assertPublicEncryptPrivateDecrypt(t *testing.T, data []byte) {
	EncData, err := RsaPublicEncrypt(data, pubKey)
	assert.Nil(t, err)
	DecData, err := RsaPrivateDecrypt(EncData, priKey)
	assert.Nil(t, err)
	assertArrayEqual(t, data, DecData)
}

func assertArrayEqual(t *testing.T, a []byte, b []byte) {
	if len(a) != len(b) {
		t.Fatal("array not equal!")
		return
	}
	for i := 0; i < len(a); i++ {
		if a[i] != b[i] {
			t.Fatalf("array not equal at position %d, %d != %d", i, a[i], b[i])
			return
		}
	}
}
