package utils

import (
	"archive/zip"
	"crypto/md5"
	"encoding/hex"
	"io"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func GetDirectorySize(dirPath string) (int64, error) {
	file, err := os.Stat(dirPath)
	if err != nil {
		if os.IsNotExist(err) {
			return 0, stderr.NotFound.Error(err.Error())
		}
		return 0, err
	}
	if !file.IsDir() {
		return file.Size(), nil
	}

	var size int64 = 0
	subfiles, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return 0, nil
	}
	for _, subfile := range subfiles {
		s1, e := GetDirectorySize(path.Join(dirPath, subfile.Name()))
		if e != nil {
			return 0, e
		}
		size += s1
	}
	return size, nil
}

// Exists returns true if the path is existing
func Exists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return true, err
}

func Unzip(src, dest string) error {
	if _, err := os.Stat(src); err != nil {
		if !os.IsNotExist(err) {
			return stderr.InvalidParam.Cause(err, "input zip file not exist")
		}
		return stderr.Internal.Cause(err, "stat path %s", src)
	}

	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer func() {
		if err := r.Close(); err != nil {
			stdlog.WithError(err).Warnf("close zip reader fail")
			return
		}
	}()

	if err := os.MkdirAll(dest, os.ModePerm); err != nil {
		return stderr.Wrap(err, "cannot init dest directory")
	}

	// Closure to address file descriptors issue with all the deferred .Close() methods
	extractAndWriteFile := func(f *zip.File) error {
		path := filepath.Join(dest, f.Name)

		// 处理压缩包中的目录
		if f.FileInfo().IsDir() {
			if err = os.MkdirAll(path, os.ModePerm); err != nil {
				return stderr.Wrap(err, "mkdir all %s", path)
			}
			return nil
		}

		// 处理压缩包中的文件
		rc, err := f.Open()
		if err != nil {
			return stderr.Wrap(err, "open zip file %s", f.Name)
		}
		defer func() {
			if err := rc.Close(); err != nil {
				stdlog.WithError(err).Warnf("failed to close zip file reader")
				return
			}
		}()

		// Check for ZipSlip (Directory traversal)
		if !strings.HasPrefix(path, filepath.Clean(dest)+string(os.PathSeparator)) {
			return stderr.InvalidParam.Error("illegal file path: %s", path)
		}

		// 若所在目录不存在则创建
		if err := os.MkdirAll(filepath.Dir(path), os.ModePerm); err != nil {
			return stderr.Wrap(err, "mkdir all %s", filepath.Dir(path))
		}
		// 创建对应文件
		file, err := os.Create(path)
		if err != nil {
			return stderr.Wrap(err, "create file %s", path)
		}
		defer file.Close()
		// 解压
		if _, err = io.Copy(file, rc); err != nil {
			return stderr.Wrap(err, "uncompress zip file %s", path)
		}
		return nil
	}

	for _, f := range r.File {
		if err := extractAndWriteFile(f); err != nil {
			return stderr.Wrap(err, "extract and write")
		}
	}

	return nil
}

func CalculateMD5(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建 MD5 哈希对象
	hash := md5.New()

	// 将文件内容读入哈希对象
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	// 计算哈希值
	hashInBytes := hash.Sum(nil)

	// 将哈希值转换为十六进制字符串
	md5String := hex.EncodeToString(hashInBytes)

	return md5String, nil
}

func GetFileSizeBytes(filepath string) (int, error) {
	file, err := os.Stat(filepath)
	if err != nil {
		return 0, err
	}
	return int(file.Size()), nil
}

// CopyFile linkFlag 为true,则对于具有linkExtension扩展名的文件使用hardlink
func CopyFile(src, dst string, linkFlag bool, linkExtensions []string, overwrite bool, dropSrc bool) error {
	dstFileExist := true
	_, err := os.Stat(dst)
	if err != nil && os.IsNotExist(err) {
		dstFileExist = false
	}
	if linkFlag && extInLinkExts(filepath.Ext(src), linkExtensions) {
		if !dstFileExist {
			if err := os.Link(src, dst); err != nil {
				return stderr.Wrap(err, "link file %s -> %s", src, dst)
			}
		}
		// 目标文件存在时 hardlink会失败
		if dstFileExist && overwrite {
			_ = os.Remove(dst)
			if err := os.Link(src, dst); err != nil {
				return stderr.Wrap(err, "link file %s -> %s", src, dst)
			}
		}
		return nil
	}
	// copy方式复制文件
	srcFile, err := os.Open(src)
	if err != nil {
		return stderr.Wrap(err, "open file %s", src)
	}
	defer func() { _ = srcFile.Close() }()
	destFile, err := os.Create(dst)
	if err != nil {
		return stderr.Wrap(err, "create file %s, maybe dir path not exist", dst)
	}
	defer func() { _ = destFile.Close() }()
	if !dstFileExist || overwrite {
		if _, err := io.Copy(destFile, srcFile); err != nil {
			return stderr.Wrap(err, "copy file %s -> %s", src, dst)
		}
	}
	if dropSrc {
		// 不能删除hardlink的源文件
		if linkFlag && extInLinkExts(filepath.Ext(src), linkExtensions) {
			return nil
		}
		stdlog.Debugf("it want to drop source file after copy them, dropping file %s", src)
		err = os.Remove(src)
		if err != nil {
			stdlog.WithError(err).Errorf("drop source file %s failed", src)
		} else {
			stdlog.Debugf("drop source file %s success")
		}
	}
	return nil
}

// CopyDir 把src目录中的所有文件，复制到destination目录中，如果destination目录不存在则创建
// linkFlag 为true,则对于具有linkExtensions扩展名的文件使用hardlink
// overwrite 相同名字的文件是否覆盖
// dropSrc 是否删除源文件
func CopyDir(source string, destination string, linkFlag bool, linkExtensions []string, overwrite bool, dropSrc bool) error {
	// 创建目标文件夹
	err := os.MkdirAll(destination, 0777)
	if err != nil {
		return stderr.Wrap(err, "mkdir %s", destination)
	}

	// 遍历源文件夹中的所有文件和子文件夹
	items, err := os.ReadDir(source)
	if err != nil {
		return stderr.Wrap(err, "read dir %s", source)
	}

	for _, item := range items {
		srcPath := filepath.Join(source, item.Name())
		destPath := filepath.Join(destination, item.Name())
		if item.IsDir() {
			// 如果 item 是一个文件夹，则递归地调用 copyDir 函数
			err = CopyDir(srcPath, destPath, linkFlag, linkExtensions, overwrite, dropSrc)
		} else {
			err = CopyFile(srcPath, destPath, linkFlag, linkExtensions, overwrite, dropSrc)
		}
		if err != nil {
			return stderr.Wrap(err, "copy file %s -> %s", srcPath, destPath)
		}
	}
	return nil
}

func extInLinkExts(ext string, linkExtensions []string) bool {
	if len(linkExtensions) == 0 {
		return true
	}
	for _, extItem := range linkExtensions {
		pureExtItem := strings.TrimPrefix(extItem, ".")
		pureExt := strings.TrimPrefix(ext, ".")
		if pureExtItem == pureExt {
			return true
		}
	}
	return false
}

// DeleteDir 删除文件夹dirPath下的所有文件 be careful
func DeleteDir(dirPath string) error {
	return os.RemoveAll(dirPath)
}
func DeleteFile(filePath string) error {
	return os.Remove(filePath)
}

// Rename 重命名文件或者文件夹 mv fileOrDir1 fileOrDir2
func Rename(srcPath string, dstPath string) error {
	return os.Rename(srcPath, dstPath)
}

func OverwriteFile(srcPath string, dstPath string) error {
	_, err := os.Stat(srcPath)
	if err != nil {
		return stderr.Wrap(err, "stat src file")
	}
	dir := filepath.Dir(dstPath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return stderr.Wrap(err, "mkdir for dst file")
	}
	return CopyFile(srcPath, dstPath, false, []string{}, true, false)
}

// OverwriteDir 将srcPath 合并到dstPath dstPath中相同名字的文件将会被覆盖
func OverwriteDir(srcPath string, dstPath string) error {
	return CopyDir(srcPath, dstPath, false, []string{}, true, false)
}
