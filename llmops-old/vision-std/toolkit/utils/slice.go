package utils

import "transwarp.io/applied-ai/aiot/vision-std/stderr"

func CvcT2AnySlice[T any](vs []T) []any {
	ret := make([]any, len(vs))
	for i, v := range vs {
		ret[i] = v
	}
	return ret
}

func CvtAny2TSlice[T any](vs []any) ([]T, error) {
	ret := make([]T, len(vs))
	for i, v := range vs {
		tv, ok := v.(T)
		if !ok {
			return nil, stderr.Internal.Error("convert %T to %T at %d", v, tv, i)
		}
		ret[i] = tv
	}
	return ret, nil
}

func Duplicate2Slice[T any](t T, length int) []T {
	ret := make([]T, length)
	for i := range ret {
		ret[i] = t
	}
	return ret
}

func MustCvtAny2TSlice[T any](vs []any) []T {
	ts, err := CvtAny2TSlice[T](vs)
	if err != nil {
		panic(err)
	}
	return ts
}

func ReverseSlice[T any](data []T) {
	total := len(data)
	for i := 0; i < total/2; i++ {
		data[i], data[total-i-1] = data[total-i-1], data[i]
	}
}

func BatchifySlice[T any](data []T, batchSize int) [][]T {
	ret := make([][]T, 0, batchSize)
	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}
		ret = append(ret, data[i:end])
	}
	return ret
}

func Set2Slice[T comparable](data map[T]struct{}) []T {
	ret := make([]T, 0, len(data))
	for k := range data {
		ret = append(ret, k)
	}
	return ret
}

func Slice2Set[T comparable](data []T) map[T]struct{} {
	ret := make(map[T]struct{}, len(data))
	for _, v := range data {
		ret[v] = struct{}{}
	}
	return ret
}

func DeDuplicate[T comparable, S comparable](data []T, fn func(i int) S) []T {
	ret := make([]T, 0, len(data))
	set := make(map[S]T, 0)
	for i, item := range data {
		key := fn(i)
		set[key] = item
	}

	for _, v := range set {
		ret = append(ret, v)
	}
	return ret
}

func DelElement[T any](data []T, needDel func(idx int) bool) []T {
	var result []T
	for idx, item := range data {
		if !needDel(idx) {
			result = append(result, item)
		}
	}
	return result
}

func FilterSlice[T any](data []T, pass func(idx int) bool) []T {
	ret := make([]T, 0, len(data))
	for i, v := range data {
		if pass(i) {
			ret = append(ret, v)
		}
	}
	return ret
}

// AppendData 将新数据追加到data中
//
// 参数:
//   - data *any: 指向目标数据的指针。目标数据可以是nil、string类型或[]any类型
//   - newData any: 要追加的新数据。可以是任意类型
//
// 返回值:
//   - error: 如果追加过程中发生错误则返回error，否则返回nil
//
// 功能说明:
//   1. 如果data指向nil:
//      - 若newData为string类型,则将data指向该string
//      - 否则创建包含newData的新切片并将data指向该切片
//   2. 如果data指向string:
//      - 若newData也为string,则将两个string拼接
//      - 否则创建包含两个数据的新切片
//   3. 如果data指向[]any:
//      - 直接将newData追加到切片中
//
// 调用示例:
//   当需要append非string类型数据时，需要当data为nil时就使用AppendData()函数，保证data指向的类型为[]any
//	  var data any = nil
//	  AppendData(&data, 123)
//	  AppendData(&data, "hello")
func AppendData(data *any, newData any) error {
	oldData := *data
	oldDataStr, isOldDataStr := oldData.(string)
	newDataStr, isNewDataStr := newData.(string)
	if oldData == nil {
		if isNewDataStr {
			*data = newDataStr
		} else {
			dataSlice := make([]any, 0)
			dataSlice = append(dataSlice, newData)
			*data = dataSlice
		}
	} else {
		if isOldDataStr && isNewDataStr {
			// 都是字符串可以直接相加
			*data = oldDataStr + newDataStr
		} else if isOldDataStr && !isNewDataStr {
			// 原数据是字符串，新数据不是字符串，改为slice
			dataSlice := make([]any, 0)
			dataSlice = append(dataSlice, oldDataStr)
			dataSlice = append(dataSlice, newData)
			*data = dataSlice
		} else {
			// 原数据是slice，直接追加
			dataSlice, ok := oldData.([]any)
			if !ok {
				return stderr.Errorf("failed to convert %T type old data to type %T", oldData, dataSlice)
			}
			dataSlice = append(dataSlice, newData)
			*data = dataSlice
		}
	}
	return nil
}
