package utils

import (
	"bytes"
	"context"
	"io"
	"os"

	"github.com/hpcloud/tail"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DefaultTailLines = 30
	// blockSize is the block size used in tail.
	BlockSize = 1024

	// EOL is the end-of-line sign in the file.
	EOL = "\n"
)

// 处理文件中的单行数据
type LineHandler = func(string)

// TailFile 返回一个文件最末尾的若干行, 并不断读取新产生的行
// 等价于命令 tail -f -n ${nth} ${path}
func TailFile(ctx context.Context, nth int64, follow bool, path string, lineHandler LineHandler) error {
	if path == "" {
		return stderr.InvalidParam.Error("paths can not empty")
	}
	if nth == 0 {
		nth = DefaultTailLines
	}
	// open the file
	f, err := os.Open(string(path))
	if err != nil {
		return stderr.Internal.Cause(err, "fail to open file: %s", path)
	}

	// find the beginning of tail follower
	index, err := FindTailOffset(f, nth)
	_ = f.Close()
	if err != nil {
		return stderr.Internal.Cause(err, "fail to find the offset of the latest %dth line", nth)
	}

	// tail the log file
	logTail, err := tail.TailFile(string(path), tail.Config{
		Follow:   follow,
		ReOpen:   follow,
		Poll:     true,
		Location: &tail.SeekInfo{Offset: index, Whence: io.SeekStart},
	})
	if err != nil {
		return stderr.Internal.Cause(err, "fail to tail the log file %s", path)
	}

	// forward logs to socket real time
	// this goroutine will exit after tail stopped
	go func(t *tail.Tail) {
		for {
			select {
			case line, ok := <-t.Lines:
				if !ok {
					stdlog.Warnf("the tail lines channel of file '%s' has been closed", path)
					return
				}
				lineHandler(line.Text)
			case <-ctx.Done():
				stdlog.Infof("the tail of file '%s' stopped", path)
				return
			}
		}
	}(logTail)
	return nil
}

// FindTailOffset 返回从文件头到最后第n行日志开始位置的字符偏移量
//
//	If n < 0, return the beginning of the file.
//	If n >= 0, return the beginning of last nth line.
func FindTailOffset(f io.ReadSeeker, n int64) (int64, error) {
	if n < 0 {
		return 0, nil
	}
	size, err := f.Seek(0, io.SeekEnd)
	if err != nil {
		return 0, err
	}
	var left, cnt int64
	buf := make([]byte, BlockSize)
	for right := size; right > 0 && cnt <= n; right -= BlockSize {
		left = right - BlockSize
		if left < 0 {
			left = 0
			buf = make([]byte, right)
		}
		if _, err := f.Seek(left, io.SeekStart); err != nil {
			return 0, err
		}
		if _, err := f.Read(buf); err != nil {
			return 0, err
		}
		cnt += int64(bytes.Count(buf, []byte(EOL)))
	}
	for ; cnt > n; cnt-- {
		idx := bytes.Index(buf, []byte(EOL)) + 1
		buf = buf[idx:]
		left += int64(idx)
	}
	return left, nil
}

// TailFile方法的同步版本, 返回一个文件最末尾的若干行
func TailFileSync(nth int64, path string) (ret []string, err error) {
	if path == "" {
		err = stderr.InvalidParam.Error("paths can not empty")
		return
	}
	if nth == 0 {
		nth = DefaultTailLines
	}
	// open the file
	f, err := os.Open(string(path))
	if err != nil {
		err = stderr.Internal.Cause(err, "fail to open file: %s", path)
		return
	}

	// find the beginning of tail follower
	index, err := FindTailOffset(f, nth)
	_ = f.Close()
	if err != nil {
		err = stderr.Internal.Cause(err, "fail to find the offset of the latest %dth line", nth)
		return
	}

	// tail the log file
	logTail, err := tail.TailFile(string(path), tail.Config{
		Location: &tail.SeekInfo{Offset: index, Whence: io.SeekStart},
	})
	if err != nil {
		err = stderr.Internal.Cause(err, "fail to tail the log file %s", path)
		return
	}

	if nth > 0 {
		ret = make([]string, 0, nth)
	} else {
		ret = []string{}
	}
	for line, ok := <-logTail.Lines; ok; line, ok = <-logTail.Lines {
		ret = append(ret, line.Text)
	}
	return ret, nil
}
