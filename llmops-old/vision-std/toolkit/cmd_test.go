package toolkit

import (
	"os/exec"
	"testing"
)

func TestRunCommand(t *testing.T) {
	type args struct {
		cmd *exec.Cmd
	}
	tests := []struct {
		name    string
		args    args
		wantErr string
	}{
		{
			name: "legal command",
			args: args{
				cmd: exec.Command("/bin/ls", "/usr"),
			},
			wantErr: "",
		},
		{
			name: "illegal command - invalid arguments",
			args: args{
				cmd: exec.Command("/bin/ls", "/invalid"),
			},
			wantErr: "failed to exec cmd '/bin/ls /invalid', got: /bin/ls: 无法访问 '/invalid': 没有那个文件或目录\n",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := RunCommand(tt.args.cmd)
			if err == nil && tt.wantErr != "" {
				if tt.wantErr != "" {
					t.<PERSON><PERSON>("RunCommand() error = %v, wantErr = %v", err, tt.wantErr)
				}
			}
			if err != nil && err.Error() != tt.wantErr {
				t.Errorf("RunCommand() error = %v, wantErr = %v", err.Error(), tt.wantErr)
			}
		})
	}
}
