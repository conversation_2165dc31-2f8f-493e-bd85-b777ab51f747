package backoff

import (
	"context"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// Deprecated: please use RetryWithContext
func Retry(msg string, action func() error, exitLoop bool) {
	b := &Backoff{
		Min:    1 * time.Second,
		Max:    5 * time.Minute,
		Factor: 2,
	}

	d := b.Duration()

	for {
		select {
		case <-time.After(d):
		}
		err := action()
		if err == nil {
			if exitLoop {
				return
			} else {
				b.Reset()
			}
		} else {
			d = b.Duration()
			stdlog.WithError(err).Errorf("%s failed, retry in %s\n", msg, d)
		}
	}
}

func RetryWithContext(ctx context.Context, action func() error, exitLoop bool) {
	b := &Backoff{
		Min:    1 * time.Second,
		Max:    5 * time.Minute,
		Factor: 2,
	}

	d := b.Duration()

	ctxName := "unknown"
	if name := ctx.Value("name"); name != nil {
		ctxName = name.(string)
	}

	for {
		select {
		case <-ctx.Done():
			stdlog.Infof("retry '%s' exit, because context done", ctxName)
			return
		case <-time.After(d):
		}
		err := action()
		if err == nil {
			if exitLoop {
				return
			} else {
				b.Reset()
			}
		} else {
			d = b.Duration()
			stdlog.WithError(err).Errorf("'%s' failed, retry in %s\n", ctxName, d)
		}
	}

}
