package toolkit

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// MD5 will return the MD5 checksum of giving struct.
func MD5(v interface{}) string {
	bs, err := json.Marshal(v)
	if err != nil {
		stdlog.WithError(err).Errorf("error while marshaling %T '%+v'", v, v)
	}
	return fmt.Sprintf("%x", md5.Sum(bs))
}

func MD5Bytes(bs []byte) string {
	return fmt.Sprintf("%x", md5.Sum(bs))
}