package toolkit

import (
	"reflect"
	"runtime"
	"strings"
)

// GetFunctionName 仅返回函数名词
func GetFunctionName(i interface{}) string {
	nameWithPkg := GetFunctionNameWithPkg(i)
	idx := strings.LastIndex(nameWithPkg, ".")
	if idx > 0 {
		return nameWithPkg[idx+1:]
	}
	return nameWithPkg
}

// GetFunctionNameWithPkg 返回包括所在包在内的函数名称 e.g. transwarp.io/applied-ai/aiot/vision-std/toolkit.funcNameTest
func GetFunctionNameWithPkg(i interface{}) string {
	return runtime.FuncForPC(reflect.ValueOf(i).Pointer()).Name()
}
