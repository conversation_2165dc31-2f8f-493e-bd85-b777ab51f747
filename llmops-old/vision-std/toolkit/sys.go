package toolkit

import (
	"os"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func MkdirIfNotExist(dir string) error {
	stat, err := os.Stat(dir)
	if err != nil {
		// stat dir failed
		if !os.IsNotExist(err) {
			return stderr.Internal.Cause(err, "failed to stat the dir: %s", dir)
		}

		// stat dir not exist, so create it first
		if err := os.MkdirAll(dir, os.ModePerm); err != nil {
			return stderr.Internal.Cause(err, "filed to initialize the dir: %s", dir)
		}

		stdlog.Infof("success to initialized the dir:%s", dir)
		return nil
	}

	// stat dir success
	if !stat.IsDir() {
		return stderr.Internal.Error("dir already exist as a file: %s", dir)
	}

	// dir already existed
	return nil
}