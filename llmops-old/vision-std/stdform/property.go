package stdform

type (
	ValueStyle = string // 属性在前端的展示样式
	ValueType  = string // 属性值可选的数据类型
)

const (
	StyleInput           ValueStyle = "input"            // 单行输入文本框
	StylePassword        ValueStyle = "password"         // 单行输入密码框
	StyleTextarea        ValueStyle = "textarea"         // 多行输入文本框
	StyleSelector        ValueStyle = "selector"         // 单选框， 选择范围由Range给定
	StyleSelectorDynamic ValueStyle = "selector_dynamic" // 动态选择框，选择范围通过调用Range中的指定API获取

	ValueTypeInt     ValueType = "int"
	ValueTypeUint    ValueType = "uint"
	ValueTypeFloat   ValueType = "float"
	ValueTypeString  ValueType = "string"
	ValueTypeBoolean ValueType = "boolean"
)

var ValueTypes = []ValueType{ValueTypeInt, ValueTypeUint, ValueTypeFloat, ValueTypeString, ValueTypeBoolean}

// GeneralProperty 对应前端动态表单中的一个属性
type GeneralProperty struct {
	Id           string     `json:"id"`           // Id 为最终存储该属性值时的Key
	Name         string     `json:"name"`         // Name 为属性的展示名称
	Desc         string     `json:"desc"`         // Desc 为属性的描述, 通常以名称旁的?形式进行展示
	Type         ValueType  `json:"type"`         // Type 为该属性的数据类型
	Style        ValueStyle `json:"style"`        // Style 为该属性在前端的展示样式
	Default      string     `json:"default"`      // Default 该属性默认的属性值
	Range        string     `json:"range"`        // Range 为属性值的可选范围
	Precondition string     `json:"precondition"` // Precondition 为当前属性展示的前置条件, 用来实现简单的动态依赖功能
	Required     bool       `json:"required"`     // Required 表示该属性是否为必填项
	Multiple     bool       `json:"multiple"`     // Multiple 表示是否支持多选(下拉框), 列表(输入), Map(K,V)
	MaxLen       int64      `json:"max_len"`      // MaxLen 表示当Multiple为true时, 可选择的最大数量
}
