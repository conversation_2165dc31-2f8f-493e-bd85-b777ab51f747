package cas

import (
	"context"
	"fmt"
	"testing"
	"time"
)

func TestRBACApi(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	api := NewRBACApi("http://localhost:27281", "")
	const (
		objId = "ex2"
		proid = "assets"
	)
	_, err := api.PutObject(ctx, objId, ObjType_CorpusAssets, []*PutObjectReq{
		{
			SubType:   SubType_User,
			Username:  "thinger",
			ProjectId: proid,
			Act:       Act_ReadOnly,
		},
		{
			SubType:   SubType_User,
			Username:  "demo",
			ProjectId: proid,
			Act:       Act_ReadOnly,
		},
		{
			SubType:   SubType_User,
			Username:  "user1",
			ProjectId: proid,
			Act:       Act_ReadOnly,
		},
		{
			SubType:   SubType_Group,
			GroupId:   1001,
			ProjectId: proid,
			Act:       Act_ReadOnly,
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	get, err := api.GetObject(ctx, objId, ObjType_CorpusAssets)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(get)
	rsp, err := api.ListObject(ctx, WithUsername("thinger"))
	if err != nil {
		t.Fatal(err)
	}
	if rsp.AccessType != AccessType_Unrestricted {
		t.Fatal(rsp.AccessType, AccessType_Unrestricted)
	}
	rsp, err = api.ListObject(ctx, WithUsername("user1"))
	if err != nil {
		t.Fatal(err)
	}
	if rsp.AccessType != "" {
		t.Fatal(rsp.AccessType)
	}
	for _, v := range rsp.Objects {
		fmt.Println(v)
		for _, p := range v.Policies {
			fmt.Println(p)
		}
	}
	pub, err := api.PublicObjDefaultAct(ctx, "user1", proid)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(pub)
}
