package cas

import (
	"crypto/tls"
	"net/http"
	"os"
	"time"
)

const (
	CasEndpointEnv = "CAS_ENDPOINT_ENV"
)

var (
	customCasEndpoint = "http://autocv-cas-service:80" // 用户可自行手动指定cas(项目管理服务的) 的链接地址
	httpClient        = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: time.Second * 5,
	}
)

func init() {
	if e := os.Getenv(CasEndpointEnv); e != "" {
		customCasEndpoint = e
	}
}
