package cas

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"slices"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
)

// 公开资产[PermissionMode_Public]
//	默认所有成员全部权限 [PublicType_All] : 有权限进到当前目录下的所有成员具有全部权限
//	支持设置为所有人只读 [PublicType_Readonly] : 有权限进到当前目录下的所有成员(除了超管、空间管理员、创建者), 只有只读权限
//
// 自定义资产[PermissionMode_Customize]
//	手动对不同的对象分配针对用户和用户组的只读/全部权限[RBACApi.PutObject]
//	超管/空间管理员/创建者一直拥有全部权限[AccessType_Unrestricted]
//
// RBACApi 不处理'创建者'相关的权限逻辑需要业务自行实现

// Policy 用户或用户组对于特定对象的访问策略
type Policy struct {
	SubType  SubType `json:"sub_type"` // 主体类型, 区分用户和用户组
	Username string  `json:"username"` // 用户名, SubType=SubType_User时必填, eg: thinger
	GroupId  uint64  `json:"group_id"` // 用户组id, SubType=SubType_Group时必填, eg: 1001

	ObjType   ObjType `json:"obj_type"`
	ObjId     string  `json:"obj_id"`     // 操作对象id
	ProjectId string  `json:"project_id"` // 空间id, eg: assets
	Act       Act     `json:"act"`        // 允许的操作
}

type Act string // 操作
const (
	Act_ReadOnly Act = "readonly" // 只读
	Act_All      Act = "all"      // 全部
)

type SubType string // 主体类型
const (
	SubType_User  SubType = "user"  // 用户
	SubType_Group SubType = "group" // 用户组, 用户会继承来自用户组的[Act]
)

type ObjType string // 对象类型, 需要保证唯一性
const (
	ObjType_CorpusAssets       ObjType = "corpus_assets"     // cvat -资产
	ObjType_ModelWarehouse     ObjType = "mwh_model"         // mwh - 模型
	ObjType_Serving            ObjType = "serving_service"   // serving - 服务
	ObjType_CodeserverInstance ObjType = "csm_code_instance" // csm - 代码实例
	ObjType_ImageManager       ObjType = "csm_image_repo"    // csm - 空间镜像
)

type AccessType string // 用户访问类型
const (
	AccessType_Unrestricted AccessType = "unrestricted" // 不限制访问, 可无视 rbac 控制访问所有对象
)

type PermissionMode string // 资产权限模式

const (
	PermissionMode_Public    PermissionMode = "public"    // 公开
	PermissionMode_Customize PermissionMode = "customize" // 自定义
)

type PublicType string // 公开类型

const (
	PublicType_All      PublicType = "all"      // 全部权限
	PublicType_Readonly PublicType = "readonly" // 只读权限
)

type RBACApi struct {
	baseUrl string
	token   string // Bearer x.x.x
}

// NewRBACApi
//
//	@param baseurl cas 地址: 为空默认:http://autocv-cas-service:80
//	@param token 接口请求使用的 token, 为空则在内部生成
func NewRBACApi(baseurl, token string) *RBACApi {
	if baseurl == "" {
		baseurl = customCasEndpoint
	}
	return &RBACApi{baseurl, token}
}

// PutObject 创建/更新一个对象关联的策略
//
//	@param policies 全量的策略数据, 覆盖更新
//	@return []*PutObjectReq cas 接收到的策略数据
func (r *RBACApi) PutObject(ctx context.Context, objid string, objt ObjType, policies []*PutObjectReq) ([]*PutObjectReq, error) {
	b, err := json.Marshal(policies)
	if err != nil {
		return nil, fmt.Errorf("marshal policies: %w", err)
	}
	q := make(url.Values)
	q.Set("objType", string(objt))
	err = r.doReq(ctx, http.MethodPut, fmt.Sprintf("/api/v1/rbac/objects/%s?%s", objid, q.Encode()), bytes.NewReader(b), &policies)
	return policies, err
}

// DelObject 删除一个对象, 会删除所有关联的策略
func (r *RBACApi) DelObject(ctx context.Context, objid string, objt ObjType) error {
	q := make(url.Values)
	q.Set("objType", string(objt))
	return r.doReq(ctx, http.MethodDelete, fmt.Sprintf("/api/v1/rbac/objects/%s?%s", objid, q.Encode()), nil, nil)
}

// GetObject 获取一个对象和其关联的策略
func (r *RBACApi) GetObject(ctx context.Context, objid string, objt ObjType) (*GetObjectRsp, error) {
	q := make(url.Values)
	q.Set("objType", string(objt))
	rsp := &GetObjectRsp{}
	err := r.doReq(ctx, http.MethodGet, fmt.Sprintf("/api/v1/rbac/objects/%s?%s", objid, q.Encode()), nil, &rsp)
	return rsp, err
}

// ListObject 获取对象列表和其关联的策略
//
//	@param username 用户名筛选, 只返回用户有权限的对象, 同时策略只会返回与用户有关(继承自用户组)的数据
//	@param acts 操作筛选
func (r *RBACApi) ListObject(ctx context.Context, opts ...ListParam) (*ListObjectRsp, error) {
	q := make(url.Values)
	param := &listReq{}
	for _, opt := range opts {
		opt(param)
	}
	q.Set("username", param.username)
	q.Set("project_id", param.projectId)
	q.Set("objType", string(param.objt))
	act := make([]string, len(param.acts))
	for i, a := range param.acts {
		act[i] = string(a)
	}
	q.Set("acts", strings.Join(act, ","))
	rsp := &ListObjectRsp{
		Objects: make([]*GetObjectRsp, 0, 10),
	}
	err := r.doReq(ctx, http.MethodGet, "/api/v1/rbac/objects?"+q.Encode(), nil, rsp)
	return rsp, err
}

// PublicObjDefaultAct 获取指定用户在空间内对公开资产的默认操作
// 主要逻辑是判断用户是否是超管/空间管理员
func (r *RBACApi) PublicObjDefaultAct(ctx context.Context, username, projectId string) (*PublicObjDefaultActRsp, error) {
	if username == "" {
		return nil, errors.New("username can't be empty")
	}
	if projectId == "" {
		return nil, errors.New("project_id can't be empty")
	}
	roleIds, err := r.getUserRoleIds(ctx, username, projectId)
	if err != nil {
		return nil, fmt.Errorf("get user roleIds: %w", err)
	}
	if slices.Contains(roleIds, SuperAdminRoleId) ||
		slices.Contains(roleIds, ProjectOwnerRoleId) {
		return superAdmin, nil
	}
	return normal, nil
}

// getUserRoleIds 获取用户的角色id, 包括平台角色和空间角色
func (r *RBACApi) getUserRoleIds(ctx context.Context, username, projectId string) ([]uint64, error) {
	q := make(url.Values)
	q.Set("project_id", projectId)
	profile := &UserProfileResp{}

	r.token = new(auth.JWTokenBuilder).Username(username).TimeToLive(time.Hour).Build().Token()
	defer func() {
		r.token = ""
	}()

	err := r.doReq(ctx, http.MethodGet, "/api/v1/usermgr/users/profile?"+q.Encode(), nil, profile)
	if err != nil {
		return nil, fmt.Errorf("get user profile: %w", err)
	}
	return append([]uint64{profile.PlatformRoleId}, profile.ProjectRoleIds...), nil
}

func (r *RBACApi) doReq(ctx context.Context, method, uri string, body io.Reader, res any) error {
	req, err := http.NewRequestWithContext(ctx, method, r.baseUrl+uri, body)
	if err != nil {
		return fmt.Errorf("new request: %w", err)
	}
	token := r.token
	if token == "" {
		token = new(auth.JWTokenBuilder).Username("thinger").TimeToLive(time.Hour).Build().Token()
	}
	req.Header.Set(auth.TokenHeader, token)
	req.Header.Set("Content-Type", "application/json")
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("status code: %d, body: %s", resp.StatusCode, b)
	}
	if res != nil {
		err = json.NewDecoder(resp.Body).Decode(res)
		if err != nil {
			return fmt.Errorf("json decode: %w", err)
		}
	}
	return nil
}
