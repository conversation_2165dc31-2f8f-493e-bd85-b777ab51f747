package cas

// PermissionCfg 来自前端的权限配置模型
type PermissionCfg struct {
	PermissionMode PermissionMode `json:"permission_mode"`
	PublicType     PublicType     `json:"public_type"`
	CustomPolicies []*PolicyCfg   `json:"custom_policies"`
}

type PolicyCfg struct {
	SubType  SubType `json:"sub_type"` // 主体类型 user/group，user:Username必填, group:GroupId必填
	Username string  `json:"username"` // 用户名
	GroupId  uint64  `json:"group_id"` // 用户组ID
	Action   Act     `json:"action"`   // 权限操作  readonly/all
}

func (p *PolicyCfg) ToPolicyPutReq(projectId string) *PutObjectReq {
	return &PutObjectReq{
		SubType:   p.SubType,
		Username:  p.Username,
		GroupId:   p.GroupId,
		ProjectId: projectId,
		Act:       p.Action,
	}
}

// PutObjectReq 添加/修改对象权限配置
type PutObjectReq struct {
	SubType   SubType `json:"sub_type"`   // 主体类型 user:Username必填, group:GroupId必填
	Username  string  `json:"username"`   // 用户名: thinger
	GroupId   uint64  `json:"group_id"`   // 用户组id
	ProjectId string  `json:"project_id"` // 空间id: assets
	Act       Act     `json:"act"`        // 允许的操作
}

// GetObjectRsp 获取一个对象的权限配置
type GetObjectRsp struct {
	ObjType  ObjType      `json:"obj_type"`
	ObjId    string       `json:"obj_id"` // 操作对象id
	Policies []*PolicyRsp `json:"policies"`
}

type PolicyRsp struct {
	SubType   SubType `json:"sub_type"`   // 主体类型 user:Username必填, group:GroupId必填
	Username  string  `json:"username"`   // 用户名: thinger
	GroupId   uint64  `json:"group_id"`   // 用户组id
	ProjectId string  `json:"project_id"` // 空间id: assets
	Act       Act     `json:"act"`        // 允许的操作
}

// ListObjectRsp 对象列表
type ListObjectRsp struct {
	Objects []*GetObjectRsp `json:"objects"`
	// 优先于 Objects: [AccessType_Unrestricted] 表示用户可访问所有对象
	AccessType AccessType `json:"access_type"`
}

type (
	ListParam func(*listReq)
	listReq   struct {
		username  string // 用户名筛选, 只返回用户有权限的对象, 同时策略只会返回与用户有关的数据
		projectId string
		objt      ObjType
		acts      []Act // 操作筛选
	}
)

// WithUsername 用户名筛选, 只返回用户有权限的对象, 同时策略只会返回与用户有关的数据
func WithUsername(u string) ListParam {
	return func(c *listReq) {
		c.username = u
	}
}

func WithProjectId(p string) ListParam {
	return func(c *listReq) {
		c.projectId = p
	}
}

func WithObjType(objt ObjType) ListParam {
	return func(c *listReq) {
		c.objt = objt
	}
}

func WithActs(acts ...Act) ListParam {
	return func(c *listReq) {
		c.acts = acts
	}
}

const (
	SuperAdminRoleId   uint64 = 1003 // 超级管理员
	ProjectOwnerRoleId uint64 = 2001 // 空间管理员
)

type UserProfileResp struct {
	Uid            int64    `json:"uid"`
	UserName       string   `json:"user_name"`
	UserGroupNames []string `json:"user_group_names"`
	PlatformRoleId uint64   `json:"platform_role_id"`
	ProjectRoleIds []uint64 `json:"project_role_ids"`
	DefaultProject string   `json:"default_project"`
}

// PublicObjDefaultActRsp 不同用户对于公开资产默认的权限
type PublicObjDefaultActRsp struct {
	DefaultAll      Act // 用户对于默认全部权限的公开资产应有的操作
	DefaultReadonly Act // 用户对于默认只读权限的公开资产应有的操作
}

var (
	// normal 普通用户
	normal = &PublicObjDefaultActRsp{
		DefaultAll:      Act_All,
		DefaultReadonly: Act_ReadOnly,
	}

	// superAdmin 超级管理员/空间管理员
	superAdmin = &PublicObjDefaultActRsp{
		DefaultAll:      Act_All,
		DefaultReadonly: Act_All,
	}
)
