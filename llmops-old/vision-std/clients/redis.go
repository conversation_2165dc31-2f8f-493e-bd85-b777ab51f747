package clients

import (
	"context"
	"strings"
	"time"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	PONG = "PONG"
)

// RedisClient 封装了一些简单的redis方法
type RedisClient interface {
	// Ping 检测是否正常工作
	Ping() bool
	// Get redis get方法
	Get(key string) (string, error)
	// Set redis set方法，expiration为0则标注key没有过期时间
	Set(key, val string, expiration time.Duration) error
	Del(keys ...string) error
	HGet(key, field string) (string, error)
	HGetAll(key string) (map[string]string, error)

	HSet(key, field, val string) error
	HDel(key string, field ...string) error
	// Lock 获取redis分布式锁
	// 获取到的锁需要Release
	Lock(name string, expiration time.Duration) (*redislock.Lock, error)
	// Release 释放锁
	Release(lock *redislock.Lock)
	Expire(key string, expiration time.Duration) error
	SetNx(key, value string, expiration time.Duration) (bool, error)
	RPush(key string, values ...any) error
	LPush(key string, values ...any) error
	RPop(key string) (string, error)
	LPop(key string) (string, error)
	BRPop(key string, timeout time.Duration) (val string, err error)
	BLPop(key string, timeout time.Duration) (val string, err error)

	LRem(key, val string, count int64) error
	LLen(key string) (int64, error)
	LRange(key string, start, stop int64) ([]string, error)
	SAdd(key, value string) error
	SMembers(key string) ([]string, error)
	SPop(key string) error
	SRem(key string, field ...string) error
	GetRdb() redis.UniversalClient
}

type redisClient struct {
	rdb    redis.UniversalClient
	locker *redislock.Client
}

var ctx = context.Background()

func NewRedisClient(conf conf.RedisConfig) (RedisClient, error) {
	option := &redis.UniversalOptions{
		Addrs:        strings.Split(conf.Addrs, ","),
		DB:           conf.Database,
		Password:     conf.Password,
		DialTimeout:  conf.ExpireTime,
		ReadTimeout:  conf.ExpireTime,
		WriteTimeout: conf.ExpireTime,
	}

	if conf.MasterName != "" {
		option.MasterName = conf.MasterName
	}

	if conf.Username != "" {
		option.Username = conf.Username
	}

	temp := redis.NewUniversalClient(option)
	cli := &redisClient{rdb: temp, locker: redislock.New(temp)}
	if !cli.Ping() {
		return nil, stderr.Errorf("failed to ping redis")
	}
	return cli, nil
}

func (r redisClient) Ping() bool {
	ping := r.rdb.Ping(context.Background())
	if ping == nil {

		return false
	}
	val, err := ping.Result()
	if val != PONG {
		stdlog.Errorf("[redis] got unexpected ping result: %s", val)
		return false
	}
	if err != nil {
		stdlog.WithError(err).Errorf("[redis] ping error")
		return false
	}
	return true
}

func (r redisClient) Get(key string) (string, error) {
	return r.rdb.Get(ctx, key).Result()
}

func (r redisClient) Set(key, val string, expiration time.Duration) error {
	return r.rdb.Set(ctx, key, val, expiration).Err()
}

func (r redisClient) Del(keys ...string) error {
	return r.rdb.Del(ctx, keys...).Err()
}

func (r redisClient) HGet(key, field string) (string, error) {
	return r.rdb.HGet(ctx, key, field).Result()
}

func (r redisClient) HGetAll(key string) (map[string]string, error) {
	return r.rdb.HGetAll(ctx, key).Result()
}

func (r redisClient) HSet(key, field, val string) error {
	return r.rdb.HSet(ctx, key, field, val).Err()
}

func (r redisClient) HDel(key string, field ...string) error {
	return r.rdb.HDel(ctx, key, field...).Err()
}

func (r redisClient) Lock(name string, expiration time.Duration) (*redislock.Lock, error) {
	return r.locker.Obtain(ctx, name, expiration, nil)
}

func (r redisClient) Release(lock *redislock.Lock) {
	lock.Release(ctx)
}

func (r redisClient) Expire(key string, expiration time.Duration) error {
	return r.rdb.Expire(ctx, key, expiration).Err()
}

func (r redisClient) SetNx(key, value string, expiration time.Duration) (bool, error) {
	return r.rdb.SetNX(ctx, key, value, expiration).Result()
}

func (r redisClient) LPush(key string, values ...any) error {
	return r.rdb.LPush(ctx, key, values).Err()
}

func (r redisClient) RPush(key string, values ...any) error {
	return r.rdb.RPush(ctx, key, values).Err()
}

func (r redisClient) LPop(key string) (string, error) {
	return r.rdb.LPop(ctx, key).Result()
}
func (r redisClient) RPop(key string) (string, error) {
	return r.rdb.RPop(ctx, key).Result()
}
func (r redisClient) BLPop(key string, timeout time.Duration) (val string, err error) {
	ret, err := r.rdb.BLPop(ctx, timeout, key).Result()
	if err != nil {
		return "", err
	}
	if len(ret) > 1 && ret[0] == key {
		val = ret[1]
	}
	return val, nil
}

func (r redisClient) BRPop(key string, timeout time.Duration) (val string, err error) {
	ret, err := r.rdb.BRPop(ctx, timeout, key).Result()
	if err != nil {
		return "", err
	}
	if len(ret) > 1 && ret[0] == key {
		val = ret[1]
	}
	return val, nil
}

func (r redisClient) LRem(key, val string, count int64) error {
	return r.rdb.LRem(ctx, key, count, val).Err()
}

func (r redisClient) LLen(key string) (int64, error) {
	return r.rdb.LLen(ctx, key).Result()
}

func (r redisClient) LRange(key string, start, stop int64) ([]string, error) {
	return r.rdb.LRange(ctx, key, start, stop).Result()
}

func (r redisClient) SMembers(key string) ([]string, error) {
	return r.rdb.SMembers(ctx, key).Result()
}

func (r redisClient) SAdd(chainId, conId string) error {
	return r.rdb.SAdd(ctx, chainId, conId).Err()
}

func (r redisClient) SPop(key string) error {
	return r.rdb.SPop(ctx, key).Err()
}

func (r redisClient) SRem(key string, field ...string) error {
	return r.rdb.SRem(ctx, key, field).Err()
}

func (r redisClient) GetRdb() redis.UniversalClient {
	return r.rdb
}

// RedisClientHelper  优化RedisClient中对value的处理
// V 交互时，value的具体类型
type RedisClientHelper[V any] struct {
	client RedisClient
}

func GetHelper[T any](client RedisClient) *RedisClientHelper[T] {
	return &RedisClientHelper[T]{
		client: client,
	}
}
func (r *RedisClientHelper[V]) Get(key string) (*V, error) {
	valStr, err := r.client.Get(key)
	if err != nil {
		return nil, err
	}
	ret := new(V)
	if err := stdsrv.StringToAny(valStr, ret); err != nil {
		return nil, err
	}
	return ret, nil
}
func (r *RedisClientHelper[V]) Set(key string, value *V) error {
	valStr := stdsrv.AnyToString(value)
	return r.client.Set(key, valStr, 0)
}

func (r *RedisClientHelper[V]) HGet(key, field string) (*V, error) {
	valStr, err := r.client.HGet(key, field)
	if err != nil {
		return nil, err
	}
	ret := new(V)
	if err := stdsrv.StringToAny(valStr, ret); err != nil {
		return nil, err
	}
	return ret, nil
}

func (r *RedisClientHelper[V]) HGetAll(key string) (map[string]*V, error) {
	kvs, err := r.client.HGetAll(key)
	if err != nil {
		return nil, err
	}
	ret := make(map[string]*V)
	for k, valStr := range kvs {
		val := new(V)
		if err := stdsrv.StringToAny(valStr, val); err != nil {
			return nil, err
		}
		ret[k] = val
	}
	return ret, nil
}

func (r *RedisClientHelper[V]) HSet(key, field string, value *V) error {
	// 将值转换为字符串
	valStr := stdsrv.AnyToString(value)
	return r.client.HSet(key, field, valStr)
}

// RedisMap 通过hash操作模拟map的使用
type RedisMap[V any] struct {
	hashKey     string
	redisHelper *RedisClientHelper[V]
}

func NewRedisMap[V any](client RedisClient, hashKey string) *RedisMap[V] {
	return &RedisMap[V]{
		hashKey:     hashKey,
		redisHelper: GetHelper[V](client),
	}
}

// Get
// 1、当存在批量Get请求时,使用range或GetAll性能可能更好
// 2、当map中不存在对应的key时,error会报错
func (r *RedisMap[V]) Get(key string) (*V, error) {
	return r.redisHelper.HGet(r.hashKey, key)
}
func (r *RedisMap[V]) Set(key string, value *V) error {
	return r.redisHelper.HSet(r.hashKey, key, value)
}

// Range   handler 遍历kv,直到返回false(主动终止),
func (r *RedisMap[V]) Range(handler func(key string, value *V) bool) error {
	kvs, err := r.redisHelper.HGetAll(r.hashKey)
	if err != nil {
		return err
	}
	for k, v := range kvs {
		if !handler(k, v) {
			return nil
		}
	}
	return nil
}
func (r *RedisMap[V]) GetAll() (map[string]*V, error) {
	return r.redisHelper.HGetAll(r.hashKey)
}

func (r *RedisMap[V]) Clear() error {
	rdb := r.redisHelper.client.GetRdb()
	fields, err := rdb.HKeys(ctx, r.hashKey).Result()
	if err != nil {
		return err
	}
	if len(fields) == 0 {
		return nil
	}
	_, err = rdb.HDel(ctx, r.hashKey, fields...).Result()
	return err
}
