package stdes

import (
	"context"

	"github.com/elastic/go-elasticsearch/v6/esapi"
)

const (
	HealthzIndexName = "healthz_created_by_stdes"
)

func (c *Client) Healthz(ctx context.Context, testMapping string, testData map[string]any, testQuery map[string]any) (ok bool, msg string) {
	// create index
	ex, err := c.IndexExists(ctx, HealthzIndexName)
	if err != nil {
		return false, err.Error()
	}
	if ex {
		err = c.DeleteIndex(ctx, HealthzIndexName)
		if err != nil {
			return false, err.Error()
		}
	}
	// create index
	err = c.CreateIndex(ctx, HealthzIndexName, testMapping)
	if err != nil {
		return false, err.Error()
	}
	// insert data
	err = c.IndexWithRequest(ctx, &esapi.IndexRequest{
		Index:   HealthzIndexName,
		Refresh: "true",
	}, testData)
	if err != nil {
		return false, err.Error()
	}
	// search
	rsp := new(RecallResp)
	err = c.Search(ctx, HealthzIndexName, testQuery, rsp)
	if err != nil {
		return false, err.Error()
	}
	if rsp.Hits.Total != 1 {
		return false, "search result total not match"
	}
	return true, ""
}
