package stdes

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/elastic/go-elasticsearch/v6"
	"github.com/elastic/go-elasticsearch/v6/esapi"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func NewESClient(es *elasticsearch.Client) *Client {
	return &Client{es: es}
}

func NewESClientByCfg(cfg elasticsearch.Config) (*Client, error) {
	esClient, err := elasticsearch.NewClient(cfg)
	if err != nil {
		stdlog.Errorf("Error creating the client: %s", err)
		return nil, err
	}
	return &Client{es: esClient}, nil
}

type Client struct {
	es *elasticsearch.Client
}

func (c Client) GetESClient() *elasticsearch.Client {
	return c.es
}

// GetMappings 获取(多个)index的mapping
//
//	resp demo:
//	{
//		"test_fulltexts3" : {
//		  "mappings" : {
//			"default_type_" : {
//			  "properties" : {
//				"text" : {
//				  "analyzer" : "ik_max_word",
//				  "type" : "text"
//				},
//				"title" : {
//				  "type" : "keyword"
//				},
//				"age" : {
//				  "type" : "integer"
//				},
//				"doc" : {
//				  "type" : "text",
//				  "fields" : {
//					"keyword" : {
//					  "ignore_above" : 256,
//					  "type" : "keyword"
//					}
//				  }
//				}
//			  }
//			}
//		  }
//		},
//		"test_fulltexts4" : {
//			...
//		}
//	}
func (c *Client) GetMappings(ctx context.Context, indices []string) (mappings map[string]interface{}, err error) {
	req := &esapi.IndicesGetMappingRequest{
		Index:  indices,
		Pretty: true,
	}

	rsp, err := req.Do(ctx, c.es)
	if err != nil {
		return nil, err
	}

	var ret = make(map[string]any)
	err = parseRes(rsp, &ret)

	return ret, err
}

func (c *Client) IndexExists(ctx context.Context, index string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{index},
	}
	res, err := req.Do(ctx, c.es)
	if err != nil {
		return false, stderr.Wrap(err, "es existIndex")
	}
	if res.IsError() {
		rs := res.String()
		// e.g. 不存在时返回(多个空格?): "[404 Not Found] "
		if strings.HasPrefix(rs, "[404 Not Found]") {
			return false, nil
		}
		return false, stderr.Internal.Error("es  existIndex: %s", res.String())
	}
	return true, nil
}

func (c *Client) CatIndices(ctx context.Context) ([]IndexInfo, error) {
	es := c.es
	var v = true
	req := &esapi.CatIndicesRequest{
		Pretty: true,
		V:      &v,
	}
	rsp, err := req.Do(ctx, es)

	if err != nil {
		return nil, stderr.Wrap(err, "catIndices Do")
	}

	scanner := bufio.NewScanner(rsp.Body)

	// Skip the first line which contains the headers
	scanner.Scan()

	// Slice to hold all the index information
	var indices []IndexInfo

	// Read the response line by line
	for scanner.Scan() {
		line := scanner.Text()

		// Split the line by whitespace
		fields := strings.Fields(line)

		// Map the fields to the IndexInfo struct
		if len(fields) != 10 {
			// e.g.
			// ********** 04:08:46 open  healthz_created_by_stdes                 70b645e9d0d94e63ac8e1f93a1afbc17 1 1   1 0   4.2kb
			// ********** 04:08:46 open  llm-f76e231e-cf2e-4cb0-aaca-fedc568d3ef0 a2a9936f7b6b40feabb2ce4a0e371c71 1 1 471 0 782.6kb
			// ********** 04:08:46 close testtesttest                             c11edcc2ae724a1dbea594a6273cc4e5 1 1
			stdlog.Warnf("skip es index without 10 fields: %s", line)
			continue
		}
		indexInfo := IndexInfo{
			Epoch:       fields[0],
			Timestamp:   fields[1],
			Status:      fields[2],
			Index:       fields[3],
			UUID:        fields[4],
			Pri:         fields[5],
			Rep:         fields[6],
			DocsCount:   fields[7],
			DocsDeleted: fields[8],
			StoreSize:   fields[9],
		}

		// Append the index information to the slice
		indices = append(indices, indexInfo)
	}

	// Check for errors
	if err := scanner.Err(); err != nil {
		return nil, stderr.Wrap(err, "es catIndices parse rsp")
	}
	return indices, nil
}

func (c *Client) CreateIndex(ctx context.Context, name, mapping string) error {

	req := esapi.IndicesCreateRequest{
		Index: name,
		Body:  strings.NewReader(mapping),
	}
	res, err := req.Do(ctx, c.es)
	if err != nil {
		return stderr.Wrap(err, "es createIndex")
	}
	if res.IsError() {
		return stderr.Internal.Error("es createIndex: %s", res.String())
	}
	return nil
}
func (c *Client) CloseIndex(ctx context.Context, name string) error {

	req := esapi.IndicesCloseRequest{
		Index: []string{name},
	}
	res, err := req.Do(ctx, c.es)
	if err != nil {
		return stderr.Wrap(err, "es closeIndex")
	}
	if res.IsError() {
		return stderr.Internal.Error("es closeIndex: %s", res.String())
	}
	return nil
}

func (c *Client) Search(ctx context.Context, index string, query map[string]any, rsp any) error {
	es := c.es
	var buf bytes.Buffer

	if err := json.NewEncoder(&buf).Encode(query); err != nil {
		return stderr.Wrap(err, "es encoding query")
	}
	res, err := es.Search(
		es.Search.WithContext(ctx),
		es.Search.WithIndex(index),
		es.Search.WithBody(&buf),
		es.Search.WithTrackTotalHits(true),
		es.Search.WithPretty(),
	)
	if err != nil {
		return stderr.Wrap(err, "es Search")
	}
	defer res.Body.Close()

	err = parseRes(res, rsp)
	return err
}
func (c *Client) Terms(ctx context.Context, index, field string) (*TermsResp, error) {
	query := map[string]any{
		"size": 0,
		"aggs": map[string]any{
			TermsTitle: map[string]any{
				"terms": map[string]any{
					"field": fmt.Sprintf("%s.keyword", field),
					"size":  100000,
				},
			},
		},
	}

	terms := new(TermsResp)
	err := c.Search(ctx, index, query, terms)
	if err != nil {
		return nil, err
	}
	return terms, nil
}

func (c *Client) Index(ctx context.Context, index string, data map[string]any) error {
	req := &esapi.IndexRequest{
		Index: index,
	}
	return c.IndexWithRequest(ctx, req, data)
}

func (c *Client) IndexWithRequest(ctx context.Context, req *esapi.IndexRequest, data map[string]any) error {
	if req == nil {
		return fmt.Errorf("nil esapi.IndexRequest")
	}
	es := c.es
	bs, err := json.Marshal(data)
	if err != nil {
		return err
	}
	req.Body = bytes.NewReader(bs)
	rsp, err := req.Do(ctx, es)

	if err != nil {
		return err
	}
	defer rsp.Body.Close()

	content, err := parseResText(rsp)
	if err != nil {
		return err
	}
	stdlog.Infof("[stdes] Index succeded with response: %s", content)
	return nil
}

func (c *Client) Bulk(ctx context.Context, index string, ops []*BulkOperation) error {
	req := esapi.BulkRequest{Index: index}
	var builder strings.Builder
	for _, op := range ops {
		source := map[BulkOpActionType]BulkOpSource{op.Action: op.Source}
		bs, err := json.Marshal(source)
		if err != nil {
			return err
		}
		builder.WriteString(string(bs))
		builder.WriteRune('\n')

		bs, err = json.Marshal(op.Data)
		if err != nil {
			return err
		}
		builder.WriteString(string(bs))
		builder.WriteRune('\n')
	}
	req.Body = strings.NewReader(builder.String())
	rsp, err := req.Do(context.Background(), c.es)
	if err != nil {
		return err
	}
	defer rsp.Body.Close()
	content, err := parseResText(rsp)
	if err != nil {
		return err
	}
	stdlog.Infof("[stdes] Bulk succeded with response: %s", content)
	return nil
}

func (c *Client) DeleteByQuery(ctx context.Context, index []string, query map[string]any) error {
	es := c.es
	var buf bytes.Buffer

	if err := json.NewEncoder(&buf).Encode(query); err != nil {
		return stderr.Wrap(err, "es encoding query")
	}
	req := esapi.DeleteByQueryRequest{
		Index: index,
	}
	res, err := req.Do(ctx, es)
	if err != nil {
		return stderr.Wrap(err, "es Search")
	}
	defer res.Body.Close()

	text, err := parseResText(res)
	if err != nil {
		return err
	}
	stdlog.Infof("stdes DeleteByQuery rsp body: %s", text)
	return nil
}

func (c *Client) DeleteIndex(ctx context.Context, index string) error {
	req := esapi.IndicesDeleteRequest{
		Index: []string{index},
	}
	res, err := req.Do(ctx, c.es)
	if err != nil {
		return stderr.Wrap(err, "es deleteIndex")
	}
	if res.IsError() {
		return stderr.Internal.Error("es deleteIndex: %s", res.String())
	}
	return nil
}

func parseRes(res *esapi.Response, v any) error {
	var r map[string]any

	if res.IsError() {
		if err := json.NewDecoder(res.Body).Decode(&r); err != nil {
			return fmt.Errorf("error parsing the err response body: %v", err)
		}
		// Print the response status and error information.
		return fmt.Errorf("[%s] %s: %s",
			res.Status(),
			r["error"].(map[string]any)["type"],
			r["error"].(map[string]any)["reason"],
		)
	}

	if err := json.NewDecoder(res.Body).Decode(v); err != nil {
		return fmt.Errorf("error parsing the response body: %s", err)
	}

	return nil
}

func parseResText(res *esapi.Response) (string, error) {
	var r map[string]any

	if res.IsError() {
		if err := json.NewDecoder(res.Body).Decode(&r); err != nil {
			return "", fmt.Errorf("error parsing the err response body: %v", err)
		}
		// Print the response status and error information.
		return "", fmt.Errorf("[%s] %s: %s",
			res.Status(),
			r["error"].(map[string]any)["type"],
			r["error"].(map[string]any)["reason"],
		)
	}

	bs, err := io.ReadAll(res.Body)
	if err != nil {
		return "", fmt.Errorf("error reading the response body: %v", err)
	}
	return string(bs), nil
}
