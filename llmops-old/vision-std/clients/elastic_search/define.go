package stdes

type IndexInfo struct {
	Epoch       string
	Timestamp   string
	Status      string
	Index       string
	UUID        string
	Pri         string
	Rep         string
	DocsCount   string
	DocsDeleted string
	StoreSize   string
}

type TermsResp struct {
	Aggregations map[string]*Aggregation `json:"aggregations"`
}

type Aggregation struct {
	Buckets []*AggregationBucket `json:"buckets"`
}

type AggregationBucket struct {
	Key      string `json:"key"`
	DocCount int    `json:"doc_count"`
}

type RecallResp struct {
	TimedOut bool        `json:"timed_out"`
	Hits     *SearchHits `json:"hits"`
}

type SearchHits struct {
	Total    int          `json:"total"`
	MaxScore float32      `json:"max_score"`
	Hits     []*SearchHit `json:"hits"`
}

type SearchHit struct {
	Index  string         `json:"_index"`
	Type   string         `json:"_type"`
	ID     string         `json:"_id"`
	Score  float32        `json:"_score"`
	Source map[string]any `json:"_source"`
}

type BulkOpSource struct {
	Index string `json:"_index,omitempty"`
	Id    string `json:"_id,omitempty"`
}

type BulkOpActionType string

const (
	BulkOpIndex  BulkOpActionType = "index"
	BulkOpDelete BulkOpActionType = "delete"
	BulkOpUpdate BulkOpActionType = "update"
	BulkOpCreate BulkOpActionType = "create"
)

type BulkOperation struct {
	Action BulkOpActionType
	Source BulkOpSource
	Data   map[string]any
}

const (
	TermsTitle = "terms_title"
)
