package stdes

import (
	"context"
	"testing"

	"github.com/elastic/go-elasticsearch/v6"
)

var (
	testMapping = `{
		"mappings": {
			"default_type_": {
				"properties": {
					"text": {
						"type": "text",
						"analyzer": "ik_max_word"
					},
					"id": {
						"type": "keyword"
					},
					"ori_id": {
						"type": "keyword"
					},
					"doc_id": {
						"type": "keyword"
					}
				}
			}
		},
		"settings": {
			"index": {
				"number_of_replicas": 1,
				"number_of_shards": 1
			}
		}
	}`

	testData = map[string]any{
		"text":   "test data",
		"id":     "1",
		"ori_id": "1",
		"doc_id": "1",
	}
	testQuery = map[string]any{
		"query": map[string]any{
			"match_all": map[string]any{},
		},
	}

	testCli *Client
)

func init() {
	var err error
	testCli, err = NewESClientByCfg(elasticsearch.Config{
		Addresses: []string{
			// "http://*************:32481",
			"http://**************:31021",
		},
		Username: "shiva",
		Password: "shiva",
	})
	if err != nil {
		panic(err)
	}
}

func TestHealthz(t *testing.T) {
	ok, msg := testCli.Healthz(context.Background(), testMapping, testData, testQuery)
	t.Log(ok, msg)
}
