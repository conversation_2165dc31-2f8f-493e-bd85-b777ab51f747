package oss

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"io"
	"os"
	"path/filepath"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func NewS3Client(cfg *S3Config) *S3Client {
	s3Client := s3.NewFromConfig(aws.Config{Region: cfg.Region}, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(cfg.Endpoint)
		o.Credentials = credentials.NewStaticCredentialsProvider(cfg.AccessKeyId, cfg.AccessKeySecret, "")
	})
	return &S3Client{
		Client: s3Client,
		Config: cfg,
	}
}

func NewS3Config(conn *pb.DataConnection) (*S3Config, error) {
	if conn.StorageType != pb.StorageType_OSS {
		return nil, stderr.Error("The storage type %v is not support s3.", conn.StorageType)
	}
	if conn.StorageConfig == nil || conn.StorageConfig.S3 == nil {
		return nil, stderr.Error("S3 StorageConfig is nil.")
	}
	return &S3Config{
		OssType:         conn.Type,
		Endpoint:        conn.StorageConfig.S3.EndPoint,
		AccessKeyId:     conn.StorageConfig.S3.Ak,
		AccessKeySecret: conn.StorageConfig.S3.Sk,
		Region:          conn.StorageConfig.S3.Region,
	}, nil
}

type S3Config struct {
	OssType         pb.ConnectionType
	Endpoint        string
	AccessKeyId     string
	AccessKeySecret string
	Region          string
}

type S3Client struct {
	Client *s3.Client
	Config *S3Config
}

func (c *S3Client) ConnectionTest() error {
	_, err := c.Client.ListBuckets(context.TODO(), &s3.ListBucketsInput{})
	return err
}

// ListBuckets 获取Bucket列表
func (c *S3Client) ListBuckets() ([]string, error) {
	output, err := c.Client.ListBuckets(context.TODO(), &s3.ListBucketsInput{})
	if err != nil {
		return nil, err
	}
	var bucketNames []string
	for _, bucket := range output.Buckets {
		bucketNames = append(bucketNames, *bucket.Name)
	}
	return bucketNames, nil
}

// ListFiles 获取指定目录下的文件和文件夹
func (c *S3Client) ListFiles(bucketName, directory string) ([]*S3FileInfo, error) {
	if len(bucketName) == 0 {
		return nil, fmt.Errorf("bucket name is empty")
	}
	if !strings.HasSuffix(directory, "/") {
		directory = fmt.Sprintf("%s/", directory)
	}
	input := &s3.ListObjectsV2Input{
		Bucket:    aws.String(bucketName),
		Delimiter: aws.String("/"),
	}
	if directory != "/" {
		input.Prefix = aws.String(directory)
	}
	output, err := c.Client.ListObjectsV2(context.TODO(), input)
	if err != nil {
		return nil, stderr.Wrap(err, "ListFilesInDirectory method error")
	}
	var fileList []*S3FileInfo
	for _, obj := range output.Contents {
		if strings.HasSuffix(*obj.Key, "/") {
			// 阿里云oss返回结果中会包含当前目录，需排除
			continue
		}
		fileList = append(fileList, &S3FileInfo{
			Name:             filepath.Base(*obj.Key),
			IsDir:            false,
			Size:             *obj.Size,
			Path:             *obj.Key,
			ModificationTime: obj.LastModified,
		})
	}
	for _, prefix := range output.CommonPrefixes {
		fileList = append(fileList, &S3FileInfo{
			Name:  filepath.Base(*prefix.Prefix),
			IsDir: true,
			Path:  *prefix.Prefix,
		})
	}
	return fileList, nil
}

// ListFilesByRecursive 获取指定目录下的文件和文件夹，包含子文件和目录
func (c *S3Client) ListFilesByRecursive(bucketName, directory string) ([]*S3FileInfo, error) {
	if len(bucketName) == 0 {
		return nil, fmt.Errorf("bucket name is empty")
	}
	if !strings.HasSuffix(directory, "/") {
		directory = fmt.Sprintf("%s/", directory)
	}
	input := &s3.ListObjectsV2Input{
		Bucket: aws.String(bucketName),
	}
	if directory != "/" {
		input.Prefix = aws.String(directory)
	}
	output, err := c.Client.ListObjectsV2(context.TODO(), input)
	if err != nil {
		return nil, stderr.Wrap(err, "ListFilesInDirectory method error")
	}
	var fileList []*S3FileInfo
	for _, obj := range output.Contents {
		var isDir = false
		if strings.HasSuffix(*obj.Key, "/") {
			isDir = true
		}
		fileList = append(fileList, &S3FileInfo{
			Name:             filepath.Base(*obj.Key),
			IsDir:            isDir,
			Size:             *obj.Size,
			Path:             *obj.Key,
			ModificationTime: obj.LastModified,
		})
	}
	return fileList, nil
}

// DownloadFile 下载s3文件到本地fileName位置
func (c *S3Client) DownloadFile(bucketName string, objectKey string, fileName string) error {
	result, err := c.Client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		return stderr.Wrap(err, "DownloadFile: Couldn't get object %s:%s", bucketName, objectKey)
	}
	defer result.Body.Close()
	file, err := os.Create(fileName)
	if err != nil {
		return stderr.Wrap(err, "DownloadFile: Error creating file: %s", fileName)
	}
	defer file.Close()
	body, err := io.ReadAll(result.Body)
	if err != nil {
		return stderr.Wrap(err, "DownloadFile: Error reading object body from %s", objectKey)
	}
	_, err = file.Write(body)
	if err != nil {
		return stderr.Wrap(err, "DownloadFile: Error writing into file %s", fileName)
	}
	return nil
}

// GetFileMetaInfo 获取文件元信息
func (c *S3Client) GetFileMetaInfo(bucketName, key string) (*S3FileInfo, error) {
	if len(bucketName) == 0 {
		return nil, fmt.Errorf("bucket name is empty")
	}
	input := &s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	}
	output, err := c.Client.HeadObject(context.TODO(), input)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get object metadata for key %s", key)
	}
	fileInfo := &S3FileInfo{
		Name:             filepath.Base(key),
		IsDir:            strings.HasSuffix(key, "/"),
		Size:             *output.ContentLength,
		Path:             key,
		ModificationTime: output.LastModified,
	}
	return fileInfo, nil
}
