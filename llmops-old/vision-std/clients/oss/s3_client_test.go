package oss

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
	"transwarp.io/aip/llmops-common/pb"
)

var s3Client *S3Client

func TestMain(m *testing.M) {
	s3Client = setup()
	m.Run()
}

func setup() *S3Client {
	cfg := &S3Config{
		//OssType:         pb.ConnectionType_TENCENT_COS,
		//Endpoint:        "https://oss-cn-shanghai.aliyuncs.com",
		//AccessKeyId:     "LTAI5t6RiV3yhAfqxVUcFYnd",
		//AccessKeySecret: "******************************",
		//Region:          "oss-cn-shanghai",
		OssType:         pb.ConnectionType_MINIO,
		Endpoint:        "http://127.0.0.1:9000",
		AccessKeyId:     "root",
		AccessKeySecret: "password",
		Region:          "xxx",
	}
	return NewS3Client(cfg)
}

func TestConnection(t *testing.T) {
	type args struct {
		s3Config *S3Config
	}
	tests := []struct {
		name string
		args *args
		want error
	}{
		{
			name: "minio",
			args: &args{
				s3Config: &S3Config{
					OssType:         pb.ConnectionType_MINIO,
					Endpoint:        "http://127.0.0.1:9000",
					AccessKeyId:     "root",
					AccessKeySecret: "password",
					Region:          "xxx",
				},
			},
			want: nil,
		},
		{
			name: "cos",
			args: &args{
				s3Config: &S3Config{
					OssType:         pb.ConnectionType_TENCENT_COS,
					Endpoint:        "http://cos.ap-shanghai.myqcloud.com",
					AccessKeyId:     "AKIDMk9YWnTLGY3gYB5HkUO86CGqYTTI8xz2",
					AccessKeySecret: "LlNmJxh4ZD48GKZvT9whhn5oo22ATMIO",
					Region:          "ap-shanghai",
				},
			},
			want: nil,
		},
		{
			name: "oss",
			args: &args{
				s3Config: &S3Config{
					OssType:         pb.ConnectionType_TENCENT_COS,
					Endpoint:        "https://oss-cn-shanghai.aliyuncs.com",
					AccessKeyId:     "LTAI5t6RiV3yhAfqxVUcFYnd",
					AccessKeySecret: "******************************",
					Region:          "oss-cn-shanghai",
				},
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewS3Client(tt.args.s3Config)
			if got := c.ConnectionTest(); got != tt.want {
				t.Errorf("ConnectionTest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestListBuckets(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		{
			name: "test-minio",
			want: "zaw",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			buckets, err := s3Client.ListBuckets()
			if err != nil {
				t.Fatal(err)
			}
			assert.Contains(t, buckets, test.want)
		})
	}
}

func TestListFiles(t *testing.T) {
	type args struct {
		bucketName string
		directory  string
	}
	tests := []struct {
		name string
		args *args
		want *S3FileInfo
	}{
		{
			name: "test-root",
			args: &args{
				bucketName: "zaw",
				directory:  "/",
			},
			want: &S3FileInfo{
				Name:  "temp-1000.jsonl",
				IsDir: true,
				Path:  "temp-1000.jsonl",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			s3FileInfos, err := s3Client.ListFiles(test.args.bucketName, test.args.directory)
			if err != nil {
				t.Fatal(err)
			}
			assert.Equal(t, test.want.Name, s3FileInfos[0].Name)
			assert.Equal(t, test.want.Path, s3FileInfos[0].Path)
			//for _, info := range s3FileInfos {
			//	fmt.Printf("\nfile obj is: %+v", info)
			//}
		})
	}
}

func TestListFilesByRecursive(t *testing.T) {
	type args struct {
		bucketName string
		directory  string
	}
	tests := []struct {
		name string
		args *args
		want *S3FileInfo
	}{
		{
			name: "test-root",
			args: &args{
				bucketName: "zaw",
				directory:  "/",
			},
			want: &S3FileInfo{
				Name:  "temp-1000.jsonl",
				IsDir: true,
				Path:  "temp-1000.jsonl",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			s3FileInfos, err := s3Client.ListFilesByRecursive(test.args.bucketName, test.args.directory)
			if err != nil {
				t.Fatal(err)
			}
			for _, info := range s3FileInfos {
				fmt.Printf("\nfile obj is: %+v", info)
			}
		})
	}
}

func TestGetFileMetaInfo(t *testing.T) {
	type args struct {
		bucketName string
		key        string
	}
	tests := []struct {
		name string
		args *args
		want *S3FileInfo
	}{
		{
			name: "test-root",
			args: &args{
				bucketName: "zaw",
				key:        "d1/t1.png",
			},
			want: &S3FileInfo{
				Name:  "t1.png",
				IsDir: false,
				Path:  "d1/t1.png",
				Size:  3751657,
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			s3FileInfo, err := s3Client.GetFileMetaInfo(test.args.bucketName, test.args.key)
			if err != nil {
				t.Fatal(err)
			}
			assert.Equal(t, test.want.Name, s3FileInfo.Name)
			assert.Equal(t, test.want.Path, s3FileInfo.Path)
			assert.Equal(t, test.want.Size, s3FileInfo.Size)
			assert.Equal(t, test.want.IsDir, s3FileInfo.IsDir)
		})
	}

}
