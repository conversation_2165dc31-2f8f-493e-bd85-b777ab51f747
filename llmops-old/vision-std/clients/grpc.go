package clients

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func InitGrpcConnect(grpcConfig *conf.GrpcConfig) (*grpc.ClientConn, error) {
	if grpcConfig == nil {
		return nil, stderr.Internal.Error("grpc config is nil")
	}
	creds := insecure.NewCredentials()
	conn, err := grpc.Dial(grpcConfig.ServerHost+grpcConfig.ServerPort, grpc.WithTransportCredentials(creds), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(grpcConfig.GetMaxMessageBytes())))
	if err != nil {
		return nil, stderr.Wrap(err, "connect to grpc server with config: %+v", grpcConfig)
	}
	stdlog.Infof("connected to grpc server successfully")
	return conn, nil
}
