package conf

import (
	"time"
)

// 部署平台加速卡配置
type AcceleratorType = string

const (
	EnvSep                              = ","
	AcceleratorCPU      AcceleratorType = "cpu"
	AcceleratorGPU      AcceleratorType = "gpu"
	AcceleratorAtlas    AcceleratorType = "atlas"
	AcceleratorMLU      AcceleratorType = "mlu"
	AcceleratorJetson   AcceleratorType = "jetson"
	CPUAcceleratorIndex int             = -1
	MB                                  = 1024 * 1024
)

type MicroConfig struct {
	SvcName          string   `yaml:"svc_name"`
	SvcAddr          string   `yaml:"svc_addr"`
	EtcdAddrs        []string `yaml:"etcd_addrs"`
	RegisterTTL      string   `yaml:"register_ttl"`
	RegisterInterval string   `yaml:"register_interval"`
	RequestTimeout   string   `yaml:"request_timeout"`
}

type MysqlConfig struct {
	Username       string `yaml:"username"`
	Password       string `yaml:"password"`
	Host           string `yaml:"host"`
	Port           string `yaml:"port"`
	DBName         string `yaml:"db_name"`
	MaxIdle        int    `yaml:"max_idle"`
	MaxConn        int    `yaml:"max_conn"`
	NotPrintSql    bool   `yaml:"not_print_sql"`
	NotCreateTable bool   `yaml:"not_create_table"`
}

type SqliteConfig struct {
	File string `yaml:"file"`
	// https://www.sqlite.org/c3ref/busy_timeout.html
	BusyTimeoutMs int `yaml:"busy_timeout_ms"` // millisecond
}

type OssgwConfig struct {
	User          string `yaml:"user"`
	Password      string `yaml:"password"`
	Endpoint      string `yaml:"endpoint"`
	Secure        bool   `yaml:"secure"`
	DefaultBucket string `yaml:"default_bucket"`
}

type MqttConfig struct {
	BrokerAddr        string        `json:"broker_addr" yaml:"broker_addr"`
	ClientId          string        `json:"client_id" yaml:"client_id"`
	Qos               int           `json:"qos" yaml:"qos"`
	ConnTimeOut       time.Duration `json:"conn_timeout" yaml:"conn_timeout"`
	PersistentSession bool          `json:"persistent_session" yaml:"persistent_session"`
	SubTopic          string        `json:"sub_topic" yaml:"sub_topic"`
	Store             string        `json:"store" yaml:"store"` // :memory: 在内存中保存同步数据，否则在指定文件目录中保存
}

type InfluxdbConfig struct {
	URL              string        `json:"url" yaml:"url"`
	Database         string        `json:"database" yaml:"database"`
	UserAgent        string        `json:"user_agent" yaml:"user_agent"`
	Precision        string        `json:"precision" yaml:"precision"`
	RetentionPolicy  string        `json:"retention_policy" yaml:"retention_policy"`
	WriteConsistency string        `json:"write_consistency" yaml:"write_consistency"`
	Timeout          time.Duration `json:"timeout" yaml:"timeout"`
	BatchSize        int           `json:"batch_size" yaml:"batch_size"`
}

type LicenseConfig struct {
	VerifierPath  string        `json:"verifier_path" yaml:"verifier_path"`
	LicensorAddr  string        `json:"licensor_addr" yaml:"licensor_addr"`
	CheckInterval time.Duration `json:"check_interval" yaml:"check_interval"`
}

// ActuatorConfig is responsible for reading the configuration that how AutoCV components are registered to the Base gateway.
type ActuatorConfig struct {
	Enable bool `yaml:"enable"`

	InfraServiceID  string        `yaml:"infra_service_id"` // GATEWAY 所属 Sophon Infra 组件的 TDH Service ID，用于构建 ETCD KEY
	EtcdEndpoints   string        `yaml:"etcd_endpoints"`
	EtcdDialTimeout time.Duration `yaml:"etcd_dial_timeout"`

	RegisterIp               string        `yaml:"register_ip"`
	RegisterPort             string        `yaml:"register_port"`
	RegisterComponent        string        `yaml:"register_component"`
	RegisterComponentVersion string        `yaml:"register_component_version"`
	RegisterInterval         time.Duration `yaml:"register_interval"`
}

type GrpcConfig struct {
	MaxMessageMB int    `yaml:"max_message_mb"`
	ServerHost   string `yaml:"server_host"`
	ServerPort   string `yaml:"server_port"`
	Registered   bool   `yaml:"registered"`
}

type RedisConfig struct {
	Addrs      string        `json:"addrs" yaml:"addrs"`
	Database   int           `json:"database" yaml:"database"`
	Username   string        `json:"username" yaml:"username"`
	Password   string        `json:"password" yaml:"password"`
	ExpireTime time.Duration `json:"expire_time" yaml:"expire_time"`
	MasterName string        `json:"masterName" yaml:"masterName"`
}

const (
	TypeRedis = "redis"
	TypeMqtt  = "mqtt"
)

type TransportConfig struct {
	Type string `json:"type" yaml:"type"`
}

func (g *GrpcConfig) GetMaxMessageBytes() int {
	return g.MaxMessageMB * MB
}
