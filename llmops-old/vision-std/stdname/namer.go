package stdname

import "fmt"

type Namer interface {
	GetName() string
}

const (
	CloneNameSuffix = "克隆"       // 副本后缀名称
	CloneNameFormat = "%s_%s_%d" // <origin_name>_<suffix>_<index>
)

// NewCloneName 自动生成包含自增ID的新克隆对象的名称
func NewCloneName(currentName string, exists ...Namer) string {
	existNameMap := map[string]struct{}{}
	for _, v := range exists {
		existNameMap[v.GetName()] = struct{}{}
	}
	i := 0
	for {
		i++
		cloneName := fmt.Sprintf(CloneNameFormat, currentName, CloneNameSuffix, i)
		if _, ok := existNameMap[cloneName]; !ok {
			return cloneName
		}
	}
}
