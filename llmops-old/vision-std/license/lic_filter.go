package license

import (
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/license/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

// MustNewLicenseFilter 一定返回可以用的 restful filter function
// 如果初始化过程中出现错误,则会直接导致 panic, 使用时请注意
func MustNewLicenseFilter(cfg *conf.LicenseConfig, component models.AuthType) (f restful.FilterFunction) {
	f, err := NewLicenseFilter(cfg, component)
	if err != nil {
		stdlog.WithError(err).Errorf("panic while must new license filter")
		panic(err)
	}
	return f
}

// NewLicenseFilter 返回一个可用于作为 go restful v3 框架的 filter 的函数
// 用于控制许可证授权访问权限, 当
func NewLicenseFilter(cfg *conf.LicenseConfig, component models.AuthType) (f restful.FilterFunction, err error) {
	passCheck := false
	ck, err := NewLicenseChecker(cfg, component, func(activated bool) {
		passCheck = activated
	})
	if err != nil {
		return nil, stderr.Wrap(err, "new license checker")
	}
	go ck.Serve()
	f = func(request *restful.Request, response *restful.Response, chain *restful.FilterChain) {
		if !passCheck {
			// 服务未激活, 拦截所有请求
			stdsrv.ErrorResponse(response, stderr.LicenseNotActivatedError.Errorf("component=%s", component))
			return
		}

		// 继续后续处理
		chain.ProcessFilter(request, response)
	}
	return f, nil
}
