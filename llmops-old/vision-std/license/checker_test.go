package license

import (
	"context"
	"crypto/rsa"
	"math/big"
	"os"
	"sync"
	"testing"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/license/models"
)

func Test_checker_Serve(t *testing.T) {
	type fields struct {
		ctx           context.Context
		verifierPath  string
		licensorAddr  string
		checkScope    models.AuthType
		checkInterval time.Duration

		key   string
		value string
	}
	tests := []struct {
		name          string
		fields        fields
		callbacks     int // callback() 执行次数
		wantCallbacks int // 预期的 callback() 执行次数
		activates     int //  activate 次数
		wantActivates int // 预期的 activate 次数
		duration      time.Duration
	}{
		{
			name: "set valid environment skipping to check",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				key:          "LICENSE_DISABLE_SECRET",
				value:        "52autocv",
			},
			wantActivates: 1,
			wantCallbacks: 1,
			duration:      100 * time.Millisecond,
		},
		{
			name: "failed to check: invalid Verifier path",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier1",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				key:          "LICENSE_DISABLE_SECRET",
				value:        "52autocv",
			},
			wantActivates: 0,
			wantCallbacks: 0, // c.activated is false by default
			duration:      100 * time.Millisecond,
		},
		{
			name: "success to check within the given duration",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				key:          "LICENSE_DISABLE_SECRET",
				value:        "52autocv",
			},
			wantActivates: 1,
			wantCallbacks: 1,
			duration:      2*DefaultCheckInterval + 100*time.Millisecond,
		},
		{
			name: "failed to check within the given duration",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier1",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				key:          "LICENSE_DISABLE_SECRET",
				value:        "52autocv",
			},
			wantActivates: 0,
			wantCallbacks: 0,
			duration:      2*DefaultCheckInterval + 100*time.Millisecond,
		},

		// TODO more testcases
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(tt.fields.ctx)
			c, err := NewChecker(ctx, tt.fields.verifierPath, tt.fields.licensorAddr,
				tt.fields.checkScope, tt.fields.checkInterval, func(activated bool) {
					tt.callbacks++
					if activated {
						tt.activates++
					}
				})
			if err != nil {
				t.Fatal(err)
			}
			go c.Serve()

			time.Sleep(tt.duration)
			cancel()
			if tt.callbacks != tt.wantCallbacks {
				t.Errorf("checks = %v, want %v", tt.callbacks, tt.wantCallbacks)
			}
			if tt.activates != tt.wantActivates {
				t.Errorf("checks = %v, want %v", tt.activates, tt.wantActivates)
			}

			once = sync.Once{} // 强制破坏单例模式
		})
	}
}

func Test_checker_check(t *testing.T) {
	type fields struct {
		ctx           context.Context
		verifierPath  string
		licensorAddr  string
		checkScope    models.AuthType
		checkInterval time.Duration
		callback      func(activated bool)
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "failed to check the validity of license: invalid Verifier path",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier1",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				callback:     func(activated bool) {},
			},
			want: false,
		},
		{
			name: "success to check the validity of license",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				callback:     func(activated bool) {},
			},
			want: true,
		},
		// TODO more testcases
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				once = sync.Once{} // 强制破坏单例模式
			}()
			ctx, cancel := context.WithCancel(tt.fields.ctx)
			defer cancel()
			c, err := NewChecker(ctx, tt.fields.verifierPath, tt.fields.licensorAddr,
				tt.fields.checkScope, tt.fields.checkInterval, tt.fields.callback)
			if err != nil {
				t.Fatal(err)
			}
			if got := c.(*checker).check(); got != tt.want {
				t.Errorf("check() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checker_verifyVerifier(t *testing.T) {
	type fields struct {
		ctx           context.Context
		verifierPath  string
		licensorAddr  string
		checkScope    models.AuthType
		checkInterval time.Duration
		callback      func(activated bool)
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "success to verify the validity of Verifier",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				callback:     func(activated bool) {},
			},
			wantErr: false,
		},
		{
			name: "failed to verify the validity of Verifier: invalid Verifier path",
			fields: fields{
				ctx:          context.Background(),
				verifierPath: "./bin/verifier1",
				licensorAddr: "http://autocv-licensor-service:80", // TODO mock this
				checkScope:   models.EdgeService,
				callback:     func(activated bool) {},
			},
			wantErr: true,
		},
		// TODO more testcases
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				once = sync.Once{} // 强制破坏单例模式
			}()
			ctx, cancel := context.WithCancel(tt.fields.ctx)
			defer cancel()
			_, err := NewChecker(ctx, tt.fields.verifierPath, tt.fields.licensorAddr,
				tt.fields.checkScope, tt.fields.checkInterval, tt.fields.callback)
			if err != nil {
				t.Fatal(err)
			}
		})
	}
}

func Test_license_enable(t *testing.T) {
	tests := []struct {
		name  string
		key   string
		value string
		want  bool
	}{
		{
			name:  "enable to check the validity of license: default",
			key:   "",
			value: "",
			want:  true,
		},
		{
			name:  "enable to check the validity of license: invalid environment key",
			key:   "INVALID_LICENSE_DISABLE_SECRET",
			value: "52autocv",
			want:  true,
		},
		{
			name:  "enable to check the validity of license: invalid environment value",
			key:   "LICENSE_DISABLE_SECRET",
			value: "autocv",
			want:  true,
		},
		{
			name:  "disable to check the validity of license: valid environment pair",
			key:   "LICENSE_DISABLE_SECRET",
			value: "52autocv",
			want:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv(tt.key, tt.value)
			defer os.Unsetenv(tt.value)
			if got := (&Verifier{}).LicenseEnable(); got != tt.want {
				t.Errorf("disable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checker_verifyVerifierV101(t *testing.T) {
	type fields struct {
		verifierPath string
		pk           *rsa.PublicKey
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			// 注！！！
			// 二进制文件位于http://gitlab.transwarp.io/applied-ai/aiot/autocv-licensor/blob/45a8da06fed06aa530ba932861c46e8a5a933c2e/bin/Verifier.amd64
			// 考虑保持std仓库大小，不将其内置于bin目录下
			name: "Test Verifier v1.0.1",
			fields: fields{
				verifierPath: "./bin/Verifier.amd64",
				pk:           pk,
			},
			wantErr: false,
		},
		{
			name: "Test Verifier v1.0.1 with wrong path",
			fields: fields{
				verifierPath: "./bin/Verifier.amd644",
				pk:           pk,
			},
			wantErr: true,
		},
		{
			name: "Test Verifier v1.0.1 with wrong pk",
			fields: fields{
				verifierPath: "./bin/Verifier.amd644",
				pk: func() *rsa.PublicKey {
					pk := &rsa.PublicKey{
						N: big.NewInt(0),
						E: PublicKeyE,
					}
					n := []byte(PublicKeyN)
					n[0]--
					pk.N.SetString(string(n), 10)
					return pk
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Verifier{
				verifierPath: tt.fields.verifierPath,
				pk:           tt.fields.pk,
			}
			if err := c.verifySelfV101(); (err != nil) != tt.wantErr {
				t.Errorf("verifySelfV101() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
