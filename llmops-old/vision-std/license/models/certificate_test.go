package models

import (
	"crypto/rsa"
	"encoding/asn1"
	"encoding/json"
	"encoding/pem"
	"math/big"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

func TestLicenseCert_Marshal2PemData(t *testing.T) {
	tests := []struct {
		name    string
		cert    LicenseCert
		wantErr bool
	}{
		{
			name: "",
			cert: LicenseCert{
				Subject: &Subject{
					ApplicantEmail: "<EMAIL>",
					AuthScope: []AuthInfo{
						{
							AuthType: SceneAsset,
							AuthProps: []AuthProp{
								{
									ID:   "test",
									Name: "test",
									Desc: "test",
								},
							},
						},
					},
					HardwareScope: []HardwareInfo{
						{
							SerialNumber: "123456789",
						},
					},
					CustomerInfo: CustomerInfo{
						Customer: "TEST_CUST",
						Project:  "TEST_PORJ",
						Desc:     "TEST_DESC",
					},
				},
				Issuer: &Issuer{
					Signer:        "tester",
					ParentRawCert: "",
				},
				PublicKey: &rsa.PublicKey{
					N: big.NewInt(200),
					E: 100,
				},
				NotBefore: time.Time{},
				NotAfter:  time.Time{},
				Signature: []byte("fake signature"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.cert.Marshal2PemData()
			if (err != nil) != tt.wantErr {
				t.Errorf("Marshal2PemData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("PEM DATA: \n%s", got)

			gotCert, err := ParseLicenseCertFromPEM(got)
			if err != nil {
				t.Fatal("failed to parse license cert from pem ", err)
				return
			}

			if diffs := utils.Diff(&tt.cert, gotCert); diffs != nil {
				for _, diff := range diffs {
					t.Logf(diff)
				}
				t.Fatal("Unexpect differences!")
			}
		})
	}
}

func Test_tbsCertificate_asn1Bytes(t1 *testing.T) {
	type fields struct {
		Raw       asn1.RawContent
		Issuer    asn1.RawValue
		Validity  validity
		Subject   asn1.RawValue
		PublicKey asn1.RawValue
	}
	testString := "TestString"
	asn1Str, err := asn1.Marshal(testString)
	if err != nil {
		t1.Fatal(err)
	}
	tests := []struct {
		name    string
		fields  fields
		want    []byte
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				Issuer: asn1.RawValue{
					FullBytes: asn1Str,
				},
				Subject: asn1.RawValue{
					FullBytes: asn1Str,
				},
				PublicKey: asn1.RawValue{
					FullBytes: asn1Str,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &tbsCertificate{
				Raw:       tt.fields.Raw,
				Issuer:    tt.fields.Issuer,
				Validity:  tt.fields.Validity,
				Subject:   tt.fields.Subject,
				PublicKey: tt.fields.PublicKey,
			}
			got, err := t.asn1Bytes()
			if (err != nil) != tt.wantErr {
				t1.Errorf("asn1Bytes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			pd := pem.EncodeToMemory(&pem.Block{
				Type:    AutoCVLicenseCert,
				Headers: nil,
				Bytes:   got,
			})
			t1.Logf("PEM DATA IS \n%s", pd)

			tbs := new(tbsCertificate)
			_, err = asn1.Unmarshal(got, tbs)
			if err != nil {
				t1.Fatal(err)
			}
			t1.Logf("%+v", tbs)
		})
	}
}

func TestLicenseCert_HasSignAuth(t *testing.T) {
	tests := []struct {
		name   string
		fields *Subject
		want   bool
	}{
		{
			name: "test admin",
			fields: &Subject{
				AuthScope: []AuthInfo{
					{
						AuthType:  AdminAuth,
						AuthProps: nil,
					},
				},
			},
			want: true,
		},
		{
			name: "test sign auth",
			fields: &Subject{
				AuthScope: []AuthInfo{
					{
						AuthType:  SignAuth,
						AuthProps: nil,
					},
				},
			},
			want: true,
		},
		{
			name: "test other auth",
			fields: &Subject{
				AuthScope: []AuthInfo{
					{
						AuthType: CVService,
					}, {
						AuthType: EdgeService,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &LicenseCert{
				Subject: tt.fields,
			}
			if got := c.HasAuth(SignAuth); got != tt.want {
				t.Errorf("HasSignAuth() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLicenseCert_Verify(t *testing.T) {
	tests := []struct {
		name    string
		pem  string
		wantErr bool
	}{
		{
			name:    "TEST Verify cert",
			pem:     "-----BEGIN TRANSWARP SOPHON AUTOCV CERT-----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\neaWdVDO6SZdIOuLNZWyecRQBxv/zBtTg8g==\n-----END TRANSWARP SOPHON AUTOCV CERT-----\n",
			wantErr: false,
		},
		{
			name:    "TEST Verify cert",
			pem:     "-----BEGIN TRANSWARP SOPHON AUTOCV CERT-----\nMIINZTCCDF0wggSMDBllZGdlLXN1cHBvcnRAdHJhbnN3YXJwLmlvDIIEbS0tLS0t\nQkVHSU4gVFJBTlNXQVJQIFNPUEhPTiBBVVRPQ1YgQ0VSVC0tLS0tCk1JSUMvVEND\nQWZVd0VCTU1OR1UzWlRkak1EZGxaakE1RXdBd0tCY1JNakl3TVRBeE1EQXdNREF3\nS3pBNE1EQVkKRXpJd09Ua3dNVEF4TURBd01EQXdLekE0TURBd2dhZ01HV1ZrWjJV\ndGMzVndjRzl5ZEVCMGNtRnVjM2RoY25BdQphVzh3Q3pBSkV3VkJSRTFKVGpBQU1C\nRXdEeE1BRXdBVEFCTUFFd0FUQUF3QktqQnJFd2xVY21GdWMzZGhjbkFUCkVsTnZj\nR2h2YmlCRlpHZGxJRUYxZEc5RFZoTktWR2hsSUhSeWRYTjBaV1FnY205dmRDQnNh\nV05sYm5ObElHTmwKY25RZ2IyWWdZV3hzSUhSeVlXNXpkMkZ5Y0NCemIzQm9iMjRn\nWldSblpTQmhkWFJ2WTNZZ2NISnZaSFZqZEM0dwpnZ0VLQW9JQkFRQ1l5WHNWR1FV\nd05tQmFxSllFbnYzRmhoQXNlZkNIVlhGcEZ2L2JNUlEyb2ZDWGxtTVhFbVBrClFN\nUUpJWU1TOVZvNHltbU9BcWN6Uzc5QndYczBPSk4zTmZ2S2EveHlhbDgwWTJxSGxL\nOUwxMFJiUFpNT3dpVkQKV2JreExjYXp2V2tEWGRjVkxmdEs3c2lJL1h2TDBONGNw\nU29xOVg5Y1ZzR0hOcVFWN2pZZUF3b1ZUbTY1c3ZXTwpCRGVUU0FNSEd5YnlDR1Zm\naGNsbnBJSXJLVjN4b2lIKzMxYUl5WEw5eHZDYWJkZUNGMk02NTFFb2lhbG9ZMFpQ\nCjBta3FySGtnQTJ0WHc3NUZaNzM3aWlBeDVteVFMc0VGbHlPZ2UzVFROaEN0MjR2\najAwSFpWVTB2K3FhZG9qMUsKYmFoS1hoR3hkOXIzVnVqbnd5d2ptVFNxVWxZelFE\nRVhBZ01CQUFFRWdnRUFkc3RKZ0pqbHQ0bVRGZFFHOVBSZQpNL3NGT3pBWE9mYmNG\nYTlxZmVwUVpRLzM1bVpUTEIwU2hDS2psM2Zoa0d4UmN5cThxTEcwSnZzNFJMdkI4\nb2M2Cm1rWXV2UGxvd1ZlQ2RhOERaRkorZFhOdHd1MjJQb0tBVEVQUzcvSmhhcnpS\ncm82T1dINFhrM0VTa1hscjR4VjMKWXpkRFVaSE1EMERLbG0rOGNiL01iNTBDRlN3\nRmdjMHM1TkcxYnluUDhzUHFoZ3lvQUc0eW9US0RLdzRrWndLaQpuRkw4OWxaTXNK\nNGl5anpzbzdzMTVST0JoRDhyN09NS3B1eW5kMzVFL0JJc1poOFM4R1R3d2pPMTh2\nR3ZtNFpVClZ6QXZtWXRjd0Q0RWpzMmFKQkk0NWY4VDIwK1NwbmFadlVoODd2UTI4\nbHR3SXd4MEI1RmZlcjM5cCtwOExXeSsKTEE9PQotLS0tLUVORCBUUkFOU1dBUlAg\nU09QSE9OIEFVVE9DViBDRVJULS0tLS0KMCYXETIyMDcxODIwMTgyNSswODAwFxEy\nMjA4MTcyMDE4MjUrMDgwMDCCBpMTADCCA2UwggEjEwVNT0RFTDCCARgwgZ0MCW1v\nZGVsX2lkcwwS5qih5Z6L5L2/55So6IyD5Zu0DHnop4Tlrprkuoblj6/ku6Xpg6jn\nvbLkvb/nlKjnmoTmqKHlnovnmoRJROiMg+WbtO+8jElE5LmL6Ze05L2/55SoJywn\n6L+b6KGM5YiG5Ymy77yMJyon6KGo56S65LiN5a+55YW35L2T5qih5Z6L6L+b6KGM\n6ZmQ5Yi2DAEqMHYMC21vZGVsX2NvdW50DBLmnIDlpKfmqKHlnovmlbDph48MT+in\nhOWumuS6huaooeWei+S7k+W6k+WPr+S7pea3u+WKoOeahOacgOWkp+aooeWei+aV\nsOmHj++8jCDotJ/mlbDooajnpLrml6DpmZDliLYTAi0xMIIBIhMFU0NFTkUwggEX\nMIGdDAlzY2VuZV9pZHMMEuWcuuaZr+S9v+eUqOiMg+WbtAx56KeE5a6a5LqG5Y+v\n5Lul5L2/55So55qE5Zy65pmv5qih5p2/55qESUTojIPlm7TvvIxJROS5i+mXtOS9\nv+eUqCcsJ+i/m+ihjOWIhuWJsu+8jCcqJ+ihqOekuuS4jeWvueWFt+S9k+WcuuaZ\nr+i/m+ihjOmZkOWItgwBKjB1DAtzY2VuZV9jb3VudAwY5Zy65pmv5L2/55So5pWw\n6YeP6ZmQ5Yi2DEjop4Tlrprkuoblj6/ku6XliJvlu7rkvb/nlKjnmoTlnLrmma/m\nqKHmnb/mlbDph4/vvIzotJ/mlbDooajnpLrml6DpmZDliLYTAi0xMHwTBkRFVklD\nRTByMHAMDGRldmljZV9jb3VudAwY5o6l5YWl6K6+5aSH5pWw6YeP6ZmQ5Yi2DELo\np4Tlrprkuoblj6/ku6XmjqXlhaXnmoTorr7lpIfmnIDlpKfmlbDph4/vvIzotJ/m\nlbDooajnpLrml6DpmZDliLYTAi0xMIGXEwhJTlNUQU5DRTCBijCBhwwOaW5zdGFu\nY2VfbGltaXQMIeWIm+W7uuW6lOeUqOWunuS+i+eahOaVsOmHj+mZkOWItgxO6KeE\n5a6a5LqG5Y+v5Lul5Yib5bu65L2/55So55qE5pyA5aSn5bqU55So5a6e5L6L5pWw\n6YeP77yM6LSf5pWw6KGo56S65peg6ZmQ5Yi2EwItMTCCAxwwgcYTIDhiM2FmMTA5\nOTVhODRlMjhhOTY0NWNiZjZjZWM0NWExEyQ1QTgzRThDNC0xNjM4LUE4NEYtMTYy\nNS1EOEYxOTQ2QThCODQMFTMuMTAuMC05NTcuZWw3Lng4Nl82NBMVQ2VudE9TIExp\nbnV4IDcgKENvcmUpEwVsaW51eBMFYW1kNjQTQDAwY2NmODc0MzNjNTMyMWYxMTYx\nMzUzODkwYmM5YmU0M2ZmYTIzZWM3YWY4MDI3YTlkNDVlZGU2ZTE3YjU1OGQwgcwT\nIGI0MTBhZmE3OTc1ZDRmZjdhZmY2Y2VlMzhmNmM4ZTgxEyQ0QzRDNDU0NC0wMDQz\nLTM2MTAtODA1OS1CN0MwNEY0NzM1MzMMGzMuMTAuMC0xMTYwLjQ5LjEuZWw3Lng4\nNl82NBMVQ2VudE9TIExpbnV4IDcgKENvcmUpEwVsaW51eBMFYW1kNjQTQDFhYWRj\nYmI1YjY2N2ZkZjYwMDk3OTQzNmUwMTBhZTcxNWUxMTk1NjVkOTI1N2NmMzZkMjdk\nNDkxMDI0NjRiNjEwgb8TIGNjNzY5Y2FlOGY2ZTQyYjdiYTcyYjk5YjcwYjY2ZjQ2\nEyQyMzFmYzc1NS0wNmEwLTUxNTgtY2MyZi05NjM2Mjg2MjIwMmETETUuMTMuMC00\nMS1nZW5lcmljExJVYnVudHUgMjAuMDQuMiBMVFMTBWxpbnV4EwVhbWQ2NBNAYTk3\nNjZlMWUxOTUyZGU0ODM0OTBiMTZiNTBmZmU2OTQwZTdkZjM3MGRjNjMxMmEwNmI4\nNDNmODZmM2QzNWNhZDCBvxMgMzY4NzcxYmRjOGRkNDRhOGE5ZTcxNjgzZjYzN2Vi\nYzUTJGRiOWQ3ZDA0LWYzZTQtZTI3Ni04OGQ0LTEwYjQzZjk1ZTRhMhMRNS4xMy4w\nLTM3LWdlbmVyaWMTElVidW50dSAyMC4wNC40IExUUxMFbGludXgTBWFtZDY0E0Ax\nNWMzYjA3ZmFhMzIyY2MzMzA3NzYwZWQ3NDNjMjI5OGNlMjZiM2ZkMGI1YmZiOWI1\nYzI0ZjdkM2JjN2M0MzYyMAYTABMAEwAwggEKAoIBAQDGB+iOT1trxKPSFCkzJb77\ny7bCwGcCxrhmoWvK3qPv54V76R1aPM19E8GIyuDC7cucTKs1Hven/bIgky5gHoil\nqxs4ijnT2ZkTkCCff3SSEOWWJ0zctL2+1+OQfq9AP2EpK81rFlSzqQ3GRLT2FKos\n04ryJBUe/Y2ABXdBcD4zwhDR0vhfuswUHlzsnm8YPRZ9Wk2KqTlZ0rK00K7MSoSS\nqKBlwxz+no1iFUxr4fDj9YGl8RlJfT/adM0vpkEjGYZER6ZTDmun97i2HHu/wkp3\nSQNFUxxmDu/GmptmlfJ3nSJa+7thxo6hfJUqrRYIvUgK8mFVBpXwwhdrfcVZ3I4Z\nAgMBAAEEggEAE47V0xrKqrQnmd212ZAjj8mmwQ8xG2uFXQwiIrM917Lqe3zJyWi3\nALrgA4NjLhFBrj4DVr4SoD0Dszhz4pcOPMolCeeFRQ7DNY+qNNLu7HNN19lztigW\neBitk8aOaB4SXo27MzxRQTiCFfX04lMgIGWagunZbDmVP81eUUpKfUyX6UQskLIr\n5DuIpPeJw4l5PhIhG1Vt2SUNQJeiOV+P3LKBhWEnP04veBRPpw18IuT1viCva8OZ\n2jKWSG6xdX6xnmAnG1Lm5OloiVyF+RmMFYiSHTN6LDJdaGqrliehmd1Pjvs61Xsb\n76yITUXSDCnXD1FsBQ0t3K1K0jWB630xLw==\n-----END TRANSWARP SOPHON AUTOCV CERT-----\n",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c,err := ParseLicenseCertFromPEM([]byte(tt.pem))
			if err != nil {
				panic(err)
			}
			if err := c.Verify(); (err != nil) != tt.wantErr {
				t.Errorf("Verify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestParseLicenseCertFromPEM(t *testing.T) {
	type args struct {
		pemData []byte
	}
	tests := []struct {
		name    string
		pem    string
		wantErr bool
	}{
		{
			name:    "TEST TBS CSQ",
			pem:    "-----BEGIN TRANSWARP SOPHON AUTOCV TBS CERT REQUEST-----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\n-----END TRANSWARP SOPHON AUTOCV TBS CERT REQUEST-----\n",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseLicenseCertFromPEM([]byte(tt.pem))
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseLicenseCertFromPEM() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			js, err := json.Marshal(got)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("%s",js)
		})
	}
}