package models

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	_ "crypto/sha256"
	"encoding/asn1"
	"encoding/pem"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdform"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type PemType = string

const (
	certHashType                 = crypto.SHA256                              // 证书内容加密的摘要算法
	AutoCVLicenseCert    PemType = "TRANSWARP SOPHON AUTOCV CERT"             // 证书内容的PEM Type
	AutoCVTBSLicenseCert PemType = "TRANSWARP SOPHON AUTOCV TBS CERT REQUEST" // 待签名证书内容的PEM Type
	AutoCVRootPublicKey  PemType = "TRANSWARP SOPHON AUTOCV PUBLIC KEY"       // 根证书的公钥的DER编码的PEM格式数据

	HardwareAnyPlatform  = "*" // HardwareAnyPlatform 用于指示授权的硬件范围适用于所有平台
	HardwareAnyMachineID = "*" // HardwareAnyMachineID 用于指示授权的硬件范围可以用于所有机器

	e    = 65537
	nStr = "19287576235693044108585400306672068860052715110432176670225229584144719677910903517257695511773793833030806075178084025512923764850593264572382182848391305925234963989093731988699579562612613094667185131732540356049287179430430450269743226445298523538954409001932618109123604367508006730608226911919405779381071007626724769587479958600915601858940371158700637699648993448819231346365272260819046988324110358740608820342114114745013996166181647630238602434644875011030774603525258694492284633935432996134801982277710512709133847387247091967940769598717848655484313232060129915281637545782872740616212944304475121660183"
)

var (
	predefinedPub rsa.PublicKey // 预置公钥，用于校验根证书的签名

	SuperAdminHardwareScope = HardwareInfo{
		SerialNumber: HardwareAnyMachineID,
	}
	SuperAdminAuthScope = AuthInfo{
		AuthType:  AdminAuth,
		AuthProps: nil,
	}
)

func init() {
	pubK := rsa.PublicKey{
		N: big.NewInt(0),
		E: e,
	}
	pubK.N.SetString(nStr, 10)
	predefinedPub = pubK
}

// AuthInfo 服务授权信息
type AuthInfo struct {
	AuthType  AuthType   `json:"auth_type"`            // AuthType 服务类型，包括不仅限于edge, model, sign
	AuthProps []AuthProp `json:"auth_props"`           // AuthProps 扩展字段，用于存储每种授权可能包含的不同的属性
	Code      string     `json:"code" asn1:"optional"` // Code 权限点，用于前端展示树桩结构以及控制权限
}

type AuthProp struct {
	ID    string `json:"id"`    // ID 授权扩展属性的ID， 系统预定义
	Name  string `json:"name"`  // Name 对应的属性名称，用于前端展示
	Desc  string `json:"desc"`  // Desc 对应的属性描述，用于前端展示
	Value string `json:"value"` // Value 具体授权属性的值，前端填写
}

// IsCountLimiter 表明该授权属性是否是用于限制资源数量的
func (p *AuthProp) IsCountLimiter() bool {
	return strings.HasSuffix(p.ID, CountLimitSuffix)
}

func (p *AuthProp) CountLimit() int {
	if !p.IsCountLimiter() {
		return 0
	}
	if p.Value == InfiniteCount {
		return math.MaxInt64
	}
	limit, err := strconv.Atoi(p.Value)
	if err != nil {
		stdlog.WithError(err).Errorf("invalid count limit value %s", p.Value)
		return 0
	}
	return limit
}

func DefaultAuthProp(g stdform.GeneralProperty) AuthProp {
	return AuthProp{
		ID:    g.Id,
		Name:  g.Name,
		Desc:  g.Desc,
		Value: g.Default,
	}
}

// HardwareInfo 为待授权硬件的相关信息
type HardwareInfo struct {
	MachineID       string `json:"machine_id" `      // MachineID 机器码ID， from /etc/machine-id
	SystemUUID      string `json:"system_uuid"`      // SystemUUID 系统唯一标识，
	KernelVersion   string `json:"kernel_version"`   // KernelVersion 内核版本，from 'uname -r' (e.g. 3.16.0-0.bpo.4-amd64).
	OSImage         string `json:"os_image"`         // OSImage 操作系统具体版本，from /etc/os-release (e.g. Debian GNU/Linux 7 (wheezy)).
	OperatingSystem string `json:"operating_system"` // OperatingSystem 操作系统类型
	Architecture    string `json:"architecture"`     // Architecture cpu架构 amd64/arm64 || x86-64/aarch64
	SerialNumber    string `json:"serial_number"`    // SerialNumber 硬件序列号, 即唯一机器ID, 变更后将导致授权不可用
	// Platform        string `json:"platform"`        // Platform 硬件平台 e.g. [nvidia/intel/huawei/jetson/cambrian]
}

// CustomerInfo 为客户与项目的相关描述信息
type CustomerInfo struct {
	Customer string `json:"customer"` // Customer 客户名称
	Project  string `json:"project"`  // Project 项目名称
	Desc     string `json:"desc"`     // Desc 其它描述信息，包含但不仅限于使用环境、应用场景、备注等
}

// Subject 为License授权的授予对象信息
type Subject struct {
	ApplicantEmail string         `json:"applicant_email"`         // ApplicantEmail 申请人Email，用于接收LicenseCert
	AuthScope      []AuthInfo     `json:"auth_scope"`              // AuthScope 为当前项目需要进行授权的服务范围
	HardwareScope  []HardwareInfo `json:"hardware_scope"`          // HardwareScope 为当前项目需要进行授权的硬件范围
	CustomerInfo   CustomerInfo   `json:"customer_info"`           // CustomerInfo 为当前项目的客户等信息
	Version        string         `json:"version" asn1:"optional"` // Version 为当前生成info时所有的最新的权限版本
}

// Issuer 表明证书的签发者信息
type Issuer struct {
	Signer        string `json:"signer"` // Signer 签发该证书的用户,即LicenseServer对应的用户
	ParentRawCert string `json:"parent"` // ParentRawCert 签发该License的服务自身的证书内容，PEM格式
}

// LicenseCert 为Transwarp Sophon Edge平台服务的授权证书
type LicenseCert struct {
	// 证书中TBS(被签名覆盖的内容)部分
	Subject   *Subject       `json:"subject"`                 // Subject 为该证书授予的对象
	Issuer    *Issuer        `json:"issuer" asn1:"omitempty"` // Issuer 为该证书的颁发者
	PublicKey *rsa.PublicKey `json:"public_key"`              // PublicKey 为用来验证签名的公钥（PEM格式）, PublicKeyAlgorithm === RSA
	NotBefore time.Time      `json:"not_before"`              // NotBefore 为当前项目的授权的起始有效期
	NotAfter  time.Time      `json:"not_after"`               // NotAfter 为当前项目的授权的截止有效期

	// 证书TBS部分内容的签名
	Signature []byte `json:"signature"` // Signature 为当前证书颁发者对证书内容的签名, SignatureAlgorithm === SHA256WithRSA

	// 存放解析过程中的部分原始数据
	Raw                 []byte `json:"-" deep:"-"` // Raw 为该证书的 DER 格式下的原文,包含以上所有字段
	RawTBSCertificate   []byte `json:"-" deep:"-"` // Certificate part of raw ASN.1 DER content.
	RawSubjectPublicKey []byte `json:"-" deep:"-"` // DER encoded SubjectPublicKey.
	RawSubject          []byte `json:"-" deep:"-"` // DER encoded Subject
	RawIssuer           []byte `json:"-" deep:"-"` // DER encoded Issuer
}

type LicenseCSR Subject     // LicenseCSR 为向License Server申请授权许可的请求格式, 由平台结合传入的AuthInfo以及自动获取的机器信息加密签名后生成并返回
type LicenseCRT LicenseCert // LicenseCRT 为签名后的授权证书

func ParseLicenseCertFromPEM(pemData []byte) (*LicenseCert, error) {
	blk, rest := pem.Decode(pemData)
	if len(rest) != 0 {
		return nil, fmt.Errorf("pem cert: unexpected trailing data")
	}
	return ParseLicenseCertFromPEMBlock(blk)
}

func ParseLicenseCertFromPEMBlock(blk *pem.Block) (*LicenseCert, error) {
	if blk.Type != AutoCVLicenseCert && blk.Type != AutoCVTBSLicenseCert {
		return nil, fmt.Errorf("invalid pem block type: %s", blk.Type)
	}
	return ParseLicenseCert(blk.Bytes)
}

func (c *LicenseCert) CSRFileName() (name string) {
	return "[授权申请]" + c.fileNameBase() + ".info"
}
func (c *LicenseCert) CertFileName() string {
	return "[授权证书]" + c.fileNameBase() + ".pem"
}

func (c *LicenseCert) fileNameBase() string {
	if c == nil || c.Subject == nil {
		return "unknown"
	}
	return fmt.Sprintf("%s_%s_节点x%d_至%s@%s",
		c.Subject.CustomerInfo.Customer,
		c.Subject.CustomerInfo.Project,
		len(c.Subject.HardwareScope),
		c.NotAfter.Format("2006-0102_1504"),
		time.Now().Format("2006-0102_1504"),
	)
}
func (c *LicenseCert) Verify() error {
	if c == nil {
		return fmt.Errorf("empty cert")
	}

	if len(c.RawTBSCertificate) == 0 {
		tbs, err := c.TBSCertBytes()
		if err != nil {
			return stderr.Wrap(err, "failed to get tbs part")
		}
		c.RawTBSCertificate = tbs
	}
	if c.IsRoot() {
		if err := checkSignature(c.RawTBSCertificate, c.Signature, &predefinedPub); err != nil {
			return stderr.Wrap(err, "untrusted root cert")
		}
		return nil
	}

	// check
	parent, err := c.IssuerCert()
	if err != nil {
		return fmt.Errorf("failed to get issuer cert, %s", err)
	}
	if err := parent.Verify(); err != nil {
		return fmt.Errorf("failed to verify issuer cert, %s", err)
	}
	if err := checkSignature(c.RawTBSCertificate, c.Signature, parent.PublicKey); err != nil {
		return stderr.Wrap(err, "failed to check the signature")
	}
	return nil
}

func (c *LicenseCert) IsRoot() bool {
	return c.Issuer.ParentRawCert == ""
}

func (c *LicenseCert) GetAuthInfo(authType AuthType) (AuthInfo, bool) {
	for _, auth := range c.Subject.AuthScope {
		if auth.AuthType == AdminAuth {
			return auth, true
		}
		if auth.AuthType == authType {
			return auth, true
		}
	}
	return AuthInfo{}, false
}

func (c *LicenseCert) HasAuth(authType AuthType) bool {
	_, ok := c.GetAuthInfo(authType)
	return ok
}

// ContainHardware 返回当前证书的授予对象中是否包含给定的硬件
func (c *LicenseCert) ContainHardware(info HardwareInfo) bool {
	if c.HasAuth(AdminAuth) {
		return true
	}
	for _, hi := range c.Subject.HardwareScope {
		if info.SerialNumber == hi.SerialNumber {
			return true
		}
	}
	return false
}

func (c *LicenseCert) IssuerCert() (*LicenseCert, error) {
	if c == nil || c.Issuer.ParentRawCert == "" {
		return nil, fmt.Errorf("no issuer cert found")
	}

	issuer, err := ParseLicenseCertFromPEM([]byte(c.Issuer.ParentRawCert))
	if err != nil {
		return nil, fmt.Errorf("invalid issuer cert, %s", err)
	}

	return issuer, nil
}

func (c *LicenseCert) IssuerBytes() ([]byte, error) {
	if c.Issuer == nil {
		return nil, nil
	}
	return asn1.Marshal(*c.Issuer)
}

func (c *LicenseCert) SubjectBytes() ([]byte, error) {
	if c.Subject == nil {
		return nil, nil
	}
	return asn1.Marshal(*c.Subject)
}

func (c *LicenseCert) PublicKeyBytes() ([]byte, error) {
	if c.PublicKey == nil {
		return nil, nil
	}
	return asn1.Marshal(*c.PublicKey)
}

func (c *LicenseCert) tbsCert() (tc tbsCertificate, err error) {
	is, err := c.IssuerBytes()
	if err != nil {
		err = stderr.Wrap(err, "failed to get issuer asn1 bytes")
		return
	}
	sj, err := c.SubjectBytes()
	if err != nil {
		err = stderr.Wrap(err, "failed to get subject asn1 bytes")
		return
	}
	pk, err := c.PublicKeyBytes()
	if err != nil {
		err = stderr.Wrap(err, "failed to get public key asn1 bytes")
		return
	}
	tc = tbsCertificate{
		Issuer: asn1.RawValue{FullBytes: is},
		Validity: validity{
			NotBefore: c.NotBefore,
			NotAfter:  c.NotAfter,
		},
		Subject:   asn1.RawValue{FullBytes: sj},
		PublicKey: asn1.RawValue{FullBytes: pk},
	}
	return
}

func (c *LicenseCert) TBSCertBytes() (bs []byte, err error) {
	tc, err := c.tbsCert()
	if err != nil {
		return nil, err
	}
	bs, err = asn1.Marshal(tc)
	return
}

func (c *LicenseCert) Marshal2Asn1Data() ([]byte, error) {
	tc, err := c.tbsCert()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get tbs cert")
	}
	sign, err := asn1.Marshal(c.Signature)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to marshal signature")
	}
	cert := certificate{
		TBSCertificate: tc,
		SignatureValue: asn1.RawValue{FullBytes: sign},
	}
	return asn1.Marshal(cert)
}

func (c *LicenseCert) Marshal2PemData() ([]byte, error) {
	asn1Data, err := c.Marshal2Asn1Data()
	if err != nil {
		return nil, stderr.Wrap(err, "failed to marshal cert 2 asn1 data")
	}
	pemData := pem.EncodeToMemory(&pem.Block{
		Type:    AutoCVLicenseCert,
		Headers: nil,
		Bytes:   asn1Data,
	})

	return pemData, nil
}

// SetActivationCode 设置该证书对应的激活授权码
func (c *LicenseCert) SetActivationCode(code string) {
	if c == nil || c.Subject == nil {
		stdlog.Warnf("failed to set activation code without subject")
		return
	}
	// FIXME 由于新增字段会导致之前已签发的证书文件等失效，故暂时存放在Desc字段中
	c.Subject.CustomerInfo.Desc = fmt.Sprintf("code=%s", code)
	return
}

// IsActivationCodeMode 返回当前证书是否为由激活码授权生成的
func (c *LicenseCert) IsActivationCodeMode() bool {
	if c == nil || c.Subject == nil {
		return false
	}
	return strings.HasPrefix(c.Subject.CustomerInfo.Desc, "code=")
}

// ParseLicenseCert 从asn1 数据中解析出 AutoCV License Certificate
// 具体实现参考了： x509.ParseCertificate()
func ParseLicenseCert(asn1Data []byte) (*LicenseCert, error) {
	cert := &certificate{}
	rest, err := asn1.Unmarshal(asn1Data, cert)
	if err != nil {
		return nil, err
	}
	if len(rest) > 0 {
		return nil, asn1.SyntaxError{Msg: "trailing data"}
	}
	return parseCertificate(cert)
}

func parseCertificate(in *certificate) (*LicenseCert, error) {
	out := new(LicenseCert)
	out.Raw = in.Raw
	out.RawTBSCertificate = in.TBSCertificate.Raw
	out.RawSubjectPublicKey = in.TBSCertificate.PublicKey.FullBytes
	out.RawSubject = in.TBSCertificate.Subject.FullBytes
	out.RawIssuer = in.TBSCertificate.Issuer.FullBytes

	var err error
	out.PublicKey, err = parsePublicKey(out.RawSubjectPublicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key, %s", err)
	}

	out.Issuer, err = parseIssuer(in.TBSCertificate.Issuer.FullBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse issuer, %s", err)
	}
	out.Subject, err = parseSubject(in.TBSCertificate.Subject.FullBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse subject, %s", err)
	}
	out.Signature, err = parseSignature(in.SignatureValue.FullBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse signature, %s", err)
	}

	out.NotBefore = in.TBSCertificate.Validity.NotBefore
	out.NotAfter = in.TBSCertificate.Validity.NotAfter
	return out, nil
}

// parsePublicKey 从asn1格式的数据中，解析出rsa格式的公钥
func parsePublicKey(pkAsn1Data []byte) (*rsa.PublicKey, error) {

	pk := new(rsa.PublicKey)
	rest, err := asn1.Unmarshal(pkAsn1Data, pk)
	if err != nil {
		return nil, err
	}
	if len(rest) != 0 {
		return nil, fmt.Errorf("x509: trailing data after RSA public key")
	}

	if pk.N.Sign() <= 0 {
		return nil, fmt.Errorf("x509: RSA modulus is not a positive number")
	}
	if pk.E <= 0 {
		return nil, fmt.Errorf("x509: RSA public exponent is not a positive number")
	}
	pub := &rsa.PublicKey{
		E: pk.E,
		N: pk.N,
	}
	return pub, nil
}

func parseSignature(signAsn1Data []byte) ([]byte, error) {
	sig := make([]byte, 0)
	rest, err := asn1.Unmarshal(signAsn1Data, &sig)
	if err != nil {
		return nil, err
	}
	if len(rest) != 0 {
		return nil, fmt.Errorf("x509: trailing data after RSA public key")
	}
	return sig, nil
}

func parseSubject(asn1Data []byte) (*Subject, error) {
	sj := new(Subject)
	rest, err := asn1.Unmarshal(asn1Data, sj)
	if err != nil {
		return nil, err
	}
	if len(rest) != 0 {
		return nil, fmt.Errorf("x509: trailing data after X.509 subject")
	}
	return sj, nil
}

func parseIssuer(asn1Data []byte) (*Issuer, error) {
	is := new(Issuer)
	rest, err := asn1.Unmarshal(asn1Data, is)
	if err != nil {
		return nil, err
	}

	if len(rest) != 0 {
		return nil, fmt.Errorf("x509: trailing data after X.509 subject")
	}

	return is, nil
}

func marshalCertificate(in *certificate) (*LicenseCert, error) {
	out := new(LicenseCert)
	out.Raw = in.Raw
	out.RawTBSCertificate = in.TBSCertificate.Raw
	out.RawSubjectPublicKey = in.TBSCertificate.PublicKey.FullBytes
	out.RawSubject = in.TBSCertificate.Subject.FullBytes
	out.RawIssuer = in.TBSCertificate.Issuer.FullBytes

	var err error
	out.PublicKey, err = parsePublicKey(out.RawSubjectPublicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key, %s", err)
	}

	out.Issuer, err = parseIssuer(in.TBSCertificate.Issuer.FullBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse issuer, %s", err)
	}
	out.Subject, err = parseSubject(in.TBSCertificate.Subject.FullBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse subject, %s", err)
	}

	out.NotBefore = in.TBSCertificate.Validity.NotBefore
	out.NotAfter = in.TBSCertificate.Validity.NotAfter
	return out, nil
}

// 从asn1格式的数据中，解析出rsa格式的公钥
func marshalPublicKey(pk *rsa.PublicKey) (asn1Data []byte, err error) {
	return asn1.Marshal(pk)
}

// checkSignature 检查给定的文本内容以及相应的签名，是否为给定公钥所属私钥所签
func checkSignature(signed, signature []byte, pub *rsa.PublicKey) error {
	h := certHashType.New()
	h.Write(signed)
	signed = h.Sum(nil)
	return rsa.VerifyPKCS1v15(pub, certHashType, signed, signature)
}

// Sign 使用给定的私钥，对传入的 content 消息内容进行消息摘要+签名
func Sign(sk *rsa.PrivateKey, content []byte) ([]byte, error) {
	h := certHashType.New()
	h.Write(content)
	return rsa.SignPKCS1v15(rand.Reader, sk, certHashType.HashFunc(), h.Sum(nil))
}

type certificate struct {
	Raw            asn1.RawContent
	TBSCertificate tbsCertificate
	SignatureValue asn1.RawValue
}

type tbsCertificate struct {
	Raw       asn1.RawContent
	Issuer    asn1.RawValue
	Validity  validity
	Subject   asn1.RawValue
	PublicKey asn1.RawValue
}

func (t *tbsCertificate) asn1Bytes() ([]byte, error) {
	return asn1.Marshal(*t)
}

type validity struct {
	NotBefore, NotAfter time.Time
}

func GetRootPublicKeyPem() ([]byte, error) {
	res, err := asn1.Marshal(predefinedPub)
	if err != nil {
		return nil, err
	}
	return pem.EncodeToMemory(&pem.Block{
		Type:    AutoCVRootPublicKey,
		Headers: nil,
		Bytes:   res,
	}), nil

}
