package models

import (
	"fmt"
	"strings"

	. "transwarp.io/applied-ai/aiot/vision-std/stdform"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type AuthType string // AuthType 为License证书支持的授权类型
const (
	ModelAsset    AuthType = "MODEL"
	SceneAsset    AuthType = "SCENE"
	DeviceAsset   AuthType = "DEVICE"
	InstanceAsset AuthType = "INSTANCE"

	DLIEService     AuthType = "DLIE"
	EdgeService     AuthType = "EDGE"
	DataService     AuthType = "DATA"
	CVService       AuthType = "CV"
	CSMService      AuthType = "CSM"
	OCRService      AuthType = "OCR"
	MWService       AuthType = "MW"
	VLABService     AuthType = "VLAB"
	DISCOVERService AuthType = "DISCOVER"
	MLOPSService    AuthType = "MLOPS"

	LLMOPSService           AuthType = "LLMOPS"
	LLMSampleCubeService    AuthType = "CORPUS"
	LLMModelCubeService     AuthType = "MW"
	LLMModelTrainService    AuthType = "MW"
	LLMAppletCubeService    AuthType = "APPLET"
	LLMServingService       AuthType = "MLOPS"
	LLMCodeServerMgrService AuthType = "CSM"

	SignAuth  AuthType = "SIGN"
	AdminAuth AuthType = "ADMIN"

	InfiniteCount    = "-1"
	CountLimitSuffix = "_count" // 标识该属性为数量限制属性
)

var (
	LLMOpsSubComponents = []AuthType{LLMSampleCubeService, LLMModelCubeService, LLMModelTrainService, LLMAppletCubeService, LLMCodeServerMgrService}
)

func (a AuthType) Category() AuthCategory {
	for _, auth := range GetAuthDefines(GetCurtPermissionVersion().String()) {
		if auth.Type != a {
			continue
		}
		return auth.Category
	}
	return UnknownCategory
}

type AuthCategory string // AuthCategory 为License证书授权类型所属的类别，用于对授权类型进行分类管理
const (
	UnknownCategory   AuthCategory = "unknown"   // UnknownCategory 平台授权类型
	AssetCategory     AuthCategory = "asset"     // AssetCategory 平台资产授权的相关类型
	ComponentCategory AuthCategory = "component" // ComponentCategory 平台组件授权的相关类型
	ManagerCategory   AuthCategory = "manager"   // ManagerCategory 为License相关管理权限
)

// AuthVerifier 用于校验传入的参数params 在指定的配置下是否合法
type AuthVerifier func(scope AuthInfo, params map[string]string) error

// LicenseAuthDefine 描述了License支持的一种授权类型
type LicenseAuthDefine struct {
	Desc     string            `json:"desc" yaml:"desc"`         // Desc 授权类型作用描述
	Type     AuthType          `json:"type" yaml:"type"`         // Type 唯一的授权类型
	Category AuthCategory      `json:"category" yaml:"category"` // Category 授权类型所属类别
	Props    []GeneralProperty `json:"props" yaml:"props"`       // Props 为当前授权类型支持的额外配置属性定义列表
	verifier AuthVerifier      `json:"-" yaml:"-"`               // verifier 为该授权类型的子项校验方法
	Code     string            `json:"code" yaml:"code"`         // Code 权限点
	Version  string            `json:"version" yaml:"version"`   // 该权限点的版本
}

func (ad *LicenseAuthDefine) ToComponent() (*Component, bool) {
	if ad.Category != ComponentCategory {
		return nil, false
	}
	return &Component{
		Auth: ad.Type,
		Code: ad.Code,
	}, true
}

func (ad *LicenseAuthDefine) DefaultAuthInfo() AuthInfo {
	props := make([]AuthProp, 0)
	for _, prop := range ad.Props {
		if prop.Default == "" {
			continue
		}
		props = append(props, DefaultAuthProp(prop))
	}
	return AuthInfo{
		AuthType:  ad.Type,
		AuthProps: props,
		Code:      ad.Code,
	}
}

func (ad *LicenseAuthDefine) AuthInfoWithLimit(count int) AuthInfo {
	props := make([]AuthProp, 0)
	for _, prop := range ad.Props {
		ap := DefaultAuthProp(prop)
		if strings.HasSuffix(ap.ID, CountLimitSuffix) {
			ap.Value = fmt.Sprintf("%d", count)
		}
		if ap.Value == "" {
			continue
		}
		props = append(props, ap)
	}
	return AuthInfo{
		AuthType:  ad.Type,
		AuthProps: props,
	}
}

func (ad *LicenseAuthDefine) Verify(info AuthInfo, params map[string]string) error {
	if ad.Category == ComponentCategory {
		return nil
	}

	switch ad.Type {
	case AdminAuth:
		// 管理员证书具备所有权限
		return nil
	}

	if ad.verifier == nil {
		return nil
	}
	// TODO 扩展verifier实现更细粒度的权限控制
	return ad.verifier(info, params)
}

var (
	model    = LicenseAuthDefine{Category: AssetCategory, Type: ModelAsset, Desc: "使用特定模型资源的权限", Props: modelProps, verifier: nil}
	scene    = LicenseAuthDefine{Category: AssetCategory, Type: SceneAsset, Desc: "使用特定场景包资源的权限", Props: sceneProps, verifier: nil}
	device   = LicenseAuthDefine{Category: AssetCategory, Type: DeviceAsset, Desc: "接入设备资源的权限", Props: deviceProps, verifier: nil}
	instance = LicenseAuthDefine{Category: AssetCategory, Type: InstanceAsset, Desc: "启动应用实例的权限", Props: instanceProps, verifier: nil}

	data     = LicenseAuthDefine{Category: ComponentCategory, Type: DataService, Desc: "使用 Sophon AutoCV 数据管理模块(DATA)的权限", Props: nil, verifier: nil}
	cv       = LicenseAuthDefine{Category: ComponentCategory, Type: CVService, Desc: "使用 Sophon AutoCV 模型训练模块(CV)的权限", Props: nil, verifier: nil}
	csm      = LicenseAuthDefine{Category: ComponentCategory, Type: CSMService, Desc: "使用 Sophon AutoCV 代码仓库模块(CSM)的权限", Props: nil, verifier: nil}
	mw       = LicenseAuthDefine{Category: ComponentCategory, Type: MWService, Desc: "使用 Sophon AutoCV 模型管理模块(MW)的权限", Props: nil, verifier: nil}
	edge     = LicenseAuthDefine{Category: ComponentCategory, Type: EdgeService, Desc: "使用 Sophon AutoCV 模型应用模块(VISION)的权限", Props: nil, verifier: nil}
	ocr      = LicenseAuthDefine{Category: ComponentCategory, Type: OCRService, Desc: "使用 Sophon AutoCV 图文识别模块(OCR)的权限", Props: nil, verifier: nil}
	dlie     = LicenseAuthDefine{Category: ComponentCategory, Type: DLIEService, Desc: "使用 Sophon AutoCV 推理服务引擎(DLIE)的权限(不包括模型授权)", Props: nil, verifier: nil}
	vlab     = LicenseAuthDefine{Category: ComponentCategory, Type: DLIEService, Desc: "使用 Sophon Vlab 可视化建模(VLAB)的权限", Props: nil, verifier: nil}
	discover = LicenseAuthDefine{Category: ComponentCategory, Type: DLIEService, Desc: "使用 Sophon Discover编程式建模 (DISCOVER)的权限", Props: nil, verifier: nil}
	mlops    = LicenseAuthDefine{Category: ComponentCategory, Type: DLIEService, Desc: "使用 Sophon Mlops (MLOPS)的权限", Props: nil, verifier: nil}

	llmOps    = LicenseAuthDefine{Category: ComponentCategory, Type: LLMOPSService, Desc: "使用 Sophon LLMOps 所有模块的权限", Props: llmopsProps, verifier: nil}
	llmSample = LicenseAuthDefine{Category: ComponentCategory, Type: LLMSampleCubeService, Desc: "使用 Sophon LLMOps 样本仓库模块的权限", Props: nil, verifier: nil}
	llmMw     = LicenseAuthDefine{Category: ComponentCategory, Type: LLMModelCubeService, Desc: "使用 Sophon LLMOps 模型仓库模块的权限", Props: nil, verifier: nil}
	llmTrain  = LicenseAuthDefine{Category: ComponentCategory, Type: LLMModelTrainService, Desc: "使用 Sophon LLMOps 模型微调模块的权限", Props: nil, verifier: nil}
	llmMlops  = LicenseAuthDefine{Category: ComponentCategory, Type: LLMServingService, Desc: "使用 Sophon LLMOps 服务管理模块的权限", Props: nil, verifier: nil}
	llmApplet = LicenseAuthDefine{Category: ComponentCategory, Type: LLMAppletCubeService, Desc: "使用 Sophon LLMOps 应用仓库模块的权限", Props: nil, verifier: nil}
	llmCsm    = LicenseAuthDefine{Category: ComponentCategory, Type: LLMCodeServerMgrService, Desc: "使用 Sophon LLMOps 代码空间模块的权限", Props: nil, verifier: nil}

	sign  = LicenseAuthDefine{Category: ManagerCategory, Type: SignAuth, Desc: "为下级节点签发新的子证书的的权限", Props: signProps, verifier: nil, Code: strings.ToLower(string(SignAuth))}
	admin = LicenseAuthDefine{Category: ManagerCategory, Type: AdminAuth, Desc: "最高授权，用于生成License根证书", Props: nil, verifier: nil, Code: strings.ToLower(string(AdminAuth))}

	supportAuths = []LicenseAuthDefine{
		// 管理权限
		sign,
		admin,

		// 组件使用权限
		edge,
		data,
		csm,
		cv,
		ocr,
		mw,
		dlie,
		vlab,
		discover,
		mlops,

		llmOps,
		llmSample,
		llmMw,
		llmTrain,
		llmMlops,
		llmCsm,
		llmApplet,

		// 资产使用权限
		model,
		scene,
		device,
		instance,
	}
)

var (
	modelProps = []GeneralProperty{
		{
			Id:           "model_ids",
			Name:         "模型使用范围",
			Desc:         "规定了可以部署使用的模型的ID范围，ID之间使用','进行分割，'*'表示不对具体模型进行限制",
			Type:         ValueTypeString,
			Style:        StyleInput,
			Default:      "*",
			Range:        "",
			Precondition: "",
			Required:     true,
			Multiple:     true,
		},
		{
			Id:           "model" + CountLimitSuffix,
			Name:         "最大模型数量",
			Desc:         "规定了模型仓库可以添加的最大模型数量， 负数表示无限制",
			Type:         ValueTypeInt,
			Style:        StyleInput,
			Default:      InfiniteCount,
			Range:        "",
			Precondition: "",
			Required:     true,
			Multiple:     true,
		},
	}

	sceneProps = []GeneralProperty{
		{
			Id:           "scene_ids",
			Name:         "场景使用范围",
			Desc:         "规定了可以使用的场景模板的ID范围，ID之间使用','进行分割，'*'表示不对具体场景进行限制",
			Type:         ValueTypeString,
			Style:        StyleInput,
			Default:      "*",
			Range:        "",
			Precondition: "",
			Required:     true,
			Multiple:     true,
		},
		{
			Id:       "scene" + CountLimitSuffix,
			Name:     "场景使用数量限制",
			Desc:     "规定了可以创建使用的场景模板数量，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		},
	}
	deviceProps = []GeneralProperty{
		{
			Id:       "device" + CountLimitSuffix,
			Name:     "接入设备数量限制",
			Desc:     "规定了可以接入的设备最大数量，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		},
	}
	instanceProps = []GeneralProperty{
		{
			Id:       "instance" + CountLimitSuffix,
			Name:     "创建应用实例的数量限制",
			Desc:     "规定了可以创建使用的最大应用实例数量，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		},
	}

	signProps = []GeneralProperty{
		{
			Id:       "node" + CountLimitSuffix,
			Name:     "节点授权数量限制",
			Desc:     "规定了可以签发子授权证书的节点总数，负数表示不对签发数量进行限制，零值表示不具有签发权限",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  "0",
			Required: true,
			Multiple: false,
		},
	}

	llmopsProps = []GeneralProperty{
		{
			Id:       "max_model_deploy",
			Name:     "LLMOps最大模型部署数量",
			Desc:     "规定了LLMOps最大模型部署数量限制，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		}, {
			Id:       "max_applet_count",
			Name:     "最大新建应用数量",
			Desc:     "规定了LLMOps最大可新建的应用数量，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		}, {
			Id:       "max_applet_deploy",
			Name:     "最大应用部署数量限制",
			Desc:     "规定了LLMOps最大应用部署数量限制，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		}, {
			Id:       "max_kb_count",
			Name:     "最大创建的知识库数量",
			Desc:     "规定了LLMOps最大创建的知识库数量，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		},
		{
			Id:       "max_single_kb_slices_count",
			Name:     "单个知识库的最大切片数量",
			Desc:     "规定了LLMOps最大单个知识库的切片数量限制，负数表示无限制",
			Type:     ValueTypeInt,
			Style:    StyleInput,
			Default:  InfiniteCount,
			Required: true,
			Multiple: false,
		},
	}
)

// ListLicenseAuthDefines 返回当前支持的所有授权类型的定义
func ListLicenseAuthDefines() []LicenseAuthDefine {
	return supportAuths
}

// GetLicenseAuthDefine 返回当前支持的所有授权类型的定义
func GetLicenseAuthDefine(authType AuthType) (*LicenseAuthDefine, bool) {
	for _, ad := range supportAuths {
		if string(ad.Type) == strings.ToUpper(string(authType)) {
			return &ad, true
		}
	}
	return nil, false
}

func SupportedAuthTypes() []string {
	res := make([]string, len(supportAuths))
	for i, ad := range supportAuths {
		res[i] = string(ad.Type)
	}
	return res
}

func IsSupportAuthType(at AuthType) bool {
	return utils.NewSet(SupportedAuthTypes()...).Has(string(at))
}
