package models

import (
	"fmt"
	"os"
	"path"
	"regexp"
	"sort"
	"strings"

	"gopkg.in/yaml.v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdver"
)

// Component 用于定义返回给前端的license授权点
// 树状结构
type Component struct {
	Auth     AuthType     `json:"auth"`
	Code     string       `json:"code"`
	Children []*Component `json:"children"`
}

type Permission struct {
	Code   string `json:"code" yaml:"code"`
	Name   string `json:"name" yaml:"name"`
	Action string `json:"action" yaml:"action"` // license暂时不关心action
	Type   string `json:"type" yaml:"type"`     // platform || project
	Parent string `json:"parent" yaml:"parent"`
}

func (p *Permission) Key() string {
	return fmt.Sprintf("%s_%s_%s", p.Action, p.Type, p.Code)
}
func (p *Permission) ParentKey() string {
	return fmt.Sprintf("access_%s_%s", p.Type, p.Parent)
}

const (
	LatestPermissionVersion = "latest"
)

var (
	allVersions map[string]*PermissionVersion // key为版本号，value为PermissionVersion
	curtVersion *PermissionVersion            // 当前版本对应的权限定义
	assets      = []*LicenseAuthDefine{
		&model,
		&scene,
		&device,
		&instance,
	}
	dir = "etc/permission"
)

func MustInitPermission() {
	if err := initPermissions(); err != nil {
		panic(stderr.Trace(err))
	}

}

// GetAuthDefines 从配置文件中获取权限点
// 该配置文件使用和cas permission相同的文件
// 调用前请确认已经完成调用MustInitPermission
func GetAuthDefines(version string) []*LicenseAuthDefine {
	pv, _ := getVersion(version)
	return pv.Permissions.ToAuthDefines()
}
func getVersion(version string) (*PermissionVersion, bool) {
	if version == LatestPermissionVersion || version == "" {
		// 获取最早的permission
		return curtVersion, true
	}
	v, ok := allVersions[version]
	if !ok {
		stdlog.Warnf("the auth defines of version %s not found, returned the latest one", version)
		return curtVersion, false
	}
	return v, true
}
func GetPermissionTree(version string) ([]*PermissionTreeNode, error) {
	pv, ok := getVersion(version)
	if !ok {
		return nil, stderr.NotFound.Errorf("failed to get permissions tree of version %s", version)
	}
	return pv.Permissions.AsTree(true)
}

type Permissions struct {
	Permissions []*Permission
	Version     string
}

// GetCurtPermissionVersion 返回当前Licensor版本对应的权限点
func GetCurtPermissionVersion() *PermissionVersion {
	return curtVersion
}

// initPermissions 从配置文件初始化权限点
func initPermissions() error {
	if allVersions != nil {
		stdlog.Warnf("permissions already initialized")
		return nil
	}

	ver := stdver.GetCurtSemVer()

	allVersions = make(map[string]*PermissionVersion)
	// 查找所有符合条件的文件
	versions, err := findPermissionFiles(dir)
	if err != nil {
		return fmt.Errorf("error finding permission files: %v\n", err)
	}

	// 如果没有找到文件
	if len(versions) == 0 {
		return fmt.Errorf("no permission files found")
	}

	// 按版本号排序
	sort.Sort(ByVersion(versions))

	// 读取所有配置文件
	for _, version := range versions {
		stdlog.Infof("found the permission define of version %s", version.String())
		ps, err := readPermissions(version.String())
		if err != nil {
			return fmt.Errorf("error reading license file: %v\n", err)
		}
		version.Permissions = ps
		allVersions[version.String()] = version
		if version.String() == ver {
			stdlog.Infof("current permission version is %s", ver)
			curtVersion = version
		}
	}
	if curtVersion == nil {
		return stderr.NotFound.Errorf("no permission version found for %s. please set the ENV %s and retry", ver, stdver.LLMOpsVersionEnvKey)
	}
	for _, ad := range curtVersion.Permissions.ToAuthDefines() {
		supportAuths = append(supportAuths, *ad)
	}
	stdlog.Infof("Latest version is %s", curtVersion.String())
	return nil
}

// func initAllVersionDefines() error {
// 	if versionDefines != nil {
// 		stdlog.Warnf("version defines already initialized")
// 		return nil
// 	}
// 	// 读取所有配置文件
// 	for _, version := range allVersions {
// 		// 合并权限点和资产
// 		versionDefines[version.String()] = append(version.Permissions.ToAuthDefines(), assets...)
// 	}
// 	versionDefines[LatestPermissionVersion] = curtVersion.Permissions.ToAuthDefines()
// 	return nil
// }

func newPermissionFileName(v string) string {
	return fmt.Sprintf("permission-%s.yaml", v)
}

// readPermissions 从配置文件读取权限点
// 新增的权限点应新增配置文件
func readPermissions(version string) (*Permissions, error) {
	yamlData, err := os.ReadFile(path.Join(dir, newPermissionFileName(version)))
	if err != nil {
		stdlog.WithError(err).Errorf("failed to read permission.yaml")
		return nil, err
	}
	permissions := make([]*Permission, 0)
	if err := yaml.Unmarshal(yamlData, &permissions); err != nil {
		stdlog.WithError(err).Errorf("failed to parse permission.yaml")
		return nil, err
	}
	return &Permissions{
		Permissions: permissions,
		Version:     version,
	}, nil
}
func (p *Permissions) ToAuthDefines() ([]*LicenseAuthDefine, ) {
	// code去重
	set := make(map[string]struct{})
	for _, permission := range p.Permissions {
		if _, ok := set[permission.Code]; !ok {
			set[permission.Code] = struct{}{}
		}
	}
	result := make([]*LicenseAuthDefine, 0)
	// 姑且将code按.分割后的第一个字符串设置为AuthType, AuthType有可能重复
	for key := range set {
		t := "UNKNOWN"
		if codes := strings.Split(key, "."); len(codes) > 0 {
			t = codes[0]
		}
		result = append(result, &LicenseAuthDefine{
			Type:     AuthType(strings.ToUpper(t)),
			Code:     key,
			Category: ComponentCategory,
			Version:  p.Version,
		})
	}
	return result
}

type PermissionTreeNode struct {
	ID       uint64                `json:"id"`
	Code     string                `json:"code"`
	Name     string                `json:"name"`
	Action   string                `json:"action"`
	Children []*PermissionTreeNode `json:"children"`
}

func (n *PermissionTreeNode) AddChild(child *PermissionTreeNode, withAction bool) {
	if child == nil {
		return
	}
	if child.Code == n.Code && !withAction {
		return
	}

	for _, childNode := range n.Children {
		if childNode.Code != child.Code {
			continue
		}
		if !withAction {
			// 对应权限点已存在, 不需要添加
			return
		}
		if childNode.Action == child.Action {
			// 对应操作权限的权限点已存在, 不需要添加
			return
		}
	}
	// 没找到对应操作权限点
	child.Action = "*"
	n.Children = append(n.Children, child)
	return
}

// AsTree 将权限点转换为树形结构
// withAction 用于控制是否显示权限点对应的动作
// - 如果 withAction 为 true，则将平铺所有的操作类型作为叶结点
// - 如果 withAction 为 false，则将所有操作类型对应的权限点合并为一个具有 "*" action 的叶结点
//   在此情况下, 部分纯操作权限点可能会缺失, e.g project.manage (新建项目)
func (pp *Permissions) AsTree(withAction bool) ([]*PermissionTreeNode, error) {
	permissionTreeNodes := make([]*PermissionTreeNode, 0)
	codeActionNodeMap := map[string]*PermissionTreeNode{}

	// 提前建立 map 与权限点顺序解耦
	for idx, p := range pp.Permissions {
		codeActionNodeMap[p.Key()] = &PermissionTreeNode{
			ID:     uint64(idx),
			Code:   p.Code,
			Name:   p.Name,
			Action: p.Action,
		}
	}
	for _, p := range pp.Permissions {
		pNode := codeActionNodeMap[p.Key()]
		if p.Parent == "" {
			permissionTreeNodes = append(permissionTreeNodes, pNode)
			continue
		}

		parent := codeActionNodeMap[p.ParentKey()]
		if parent == nil {
			return nil, fmt.Errorf("parent not found: %s", p.Parent)
		}
		parent.AddChild(pNode, withAction)
	}

	return permissionTreeNodes, nil
}

// PermissionVersion 定义版本号结构体
// 版本号遵循 语义化版本控制（Semantic Versioning） 的格式:
// e.g. v1.2.3-alpha
// ─ MAJOR: 	1       # 主版本号，重大改动，不向后兼容
// ─ MINOR: 	2       # 次版本号，新功能更新，向后兼容
// ─ PATCH: 	3       # 修订号，Bug 修复或小改动
// ─ PRERELEASE: alpha  # 预发布标识，alpha 表示早期版本（不稳定）
type PermissionVersion struct {
	Major       int
	Minor       int
	Patch       int
	Prerelease  string
	File        string
	Permissions *Permissions
}

// ByVersion 实现 sort.Interface 接口，用于排序
// 版本号从大到小排序
type ByVersion []*PermissionVersion

func (v ByVersion) Len() int      { return len(v) }
func (v ByVersion) Swap(i, j int) { v[i], v[j] = v[j], v[i] }
func (v ByVersion) Less(i, j int) bool {
	if v[i].Major != v[j].Major {
		return v[i].Major > v[j].Major
	}
	if v[i].Minor != v[j].Minor {
		return v[i].Minor > v[j].Minor
	}
	return v[i].Patch > v[j].Patch
}

func (v PermissionVersion) String() string {
	vs := fmt.Sprintf("%d.%d.%d", v.Major, v.Minor, v.Patch)
	if v.Prerelease != "" {
		return fmt.Sprintf("%s-%s", vs, v.Prerelease)
	}
	return vs
}

// 读取目录下的所有文件并过滤出符合条件的文件
func findPermissionFiles(dir string) ([]*PermissionVersion, error) {
	var versions []*PermissionVersion

	// 读取目录下的所有文件
	files, err := os.ReadDir(dir)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory: %v", err)
	}

	// 过滤出以 permission- 开头的 YAML 文件
	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "permission-") && strings.HasSuffix(file.Name(), ".yaml") {
			version, err := extractVersion(file.Name())
			if err != nil {
				fmt.Printf("Skipping invalid file: %s, error: %v\n", file.Name(), err)
				continue
			}
			versions = append(versions, &version)
		}
	}

	return versions, nil
}

// 从文件名中提取版本号
// permission-1.2.3.yaml
// permission-1.2.3-alpha.yaml
// permission-1.2.3-beta.yaml
// permission-1.2.3-rc1.yaml
func extractVersion(filename string) (PermissionVersion, error) {
	// 使用正则表达式匹配版本号
	re := regexp.MustCompile(`permission-(\d+)\.(\d+)\.(\d+)(?:-(\S+))?\.yaml`)
	matches := re.FindStringSubmatch(filename)
	if len(matches) < 1+3 || len(matches) > 1+4 {
		// 除去 permission前缀部分,剩余需要有3-4个match
		return PermissionVersion{}, fmt.Errorf("invalid filename format: %s", filename)
	}

	// 解析版本号
	major := parseInt(matches[1])
	minor := parseInt(matches[2])
	patch := parseInt(matches[3])
	prerelease := ""
	if len(matches) == 5 {
		prerelease = matches[4]
	}
	return PermissionVersion{
		Major:      major,
		Minor:      minor,
		Patch:      patch,
		Prerelease: prerelease,
		File:       filename,
	}, nil
}

// 将字符串转换为整数
func parseInt(s string) int {
	var result int
	fmt.Sscanf(s, "%d", &result)
	return result
}
