package license

import (
	"testing"
)

var content = `
verifiler info: 
md5: a298f3b8951ea55c7c11a4e5a618e1ab
version: v1.0.0
desc: 首个正式版本
public_key:
  "n": 24760843118593492627331911185628386596111476909251873392649538668562508877771867238838858635392044409564638812550834563129896956877867415714798913290188367378399937635355897143042955593759695054230002497548056517493084311874165180664176418053560734541952634792394890892333322725424872448635163576290416004200222188268048414628783918238553420542926601601885943320106870463714817453744694241895727116600429182971387031373593536437586063977556234168916905247369657684210396461903971414358738760044844217894668233661880678201449443206749946878947496621944807395723962054397307167403996626806967227820239104530396341834953
  e: 65537
licensor:
  subject:
    applicantemail: fuxin.li
    authscope:
    - authtype: MW
      authprops: []
    - authtype: CV
      authprops: []
    - authtype: EDGE
      authprops: []
    - authtype: OCR
      authprops: []
    - authtype: DLIE
      authprops: []
    - authtype: MODEL
      authprops:
      - id: model_ids
        name: 模型使用范围
        desc: 规定了可以部署使用的模型的ID范围，ID之间使用','进行分割，'*'表示不对具体模型进行限制
        value: '*'
      - id: model_count
        name: 最大模型数量
        desc: 规定了模型仓库可以添加的最大模型数量， 负数表示无限制
        value: "-1"
    - authtype: SCENE
      authprops:
      - id: scene_ids
        name: 场景使用范围
        desc: 规定了可以使用的场景模板的ID范围，ID之间使用','进行分割，'*'表示不对具体场景进行限制
        value: '*'
      - id: scene_count
        name: 场景使用数量限制
        desc: 规定了可以创建使用的场景模板数量，负数表示无限制
        value: "-1"
    - authtype: DEVICE
      authprops:
      - id: device_count
        name: 接入设备数量限制
        desc: 规定了可以接入的设备最大数量，负数表示无限制
        value: "-1"
    - authtype: INSTANCE
      authprops:
      - id: instance_limit
        name: 创建应用实例的数量限制
        desc: 规定了可以创建使用的最大应用实例数量，负数表示无限制
        value: "-1"
    hardwarescope:
    - machineid: 8b3af10995a84e28a9645cbf6cec45a1
      systemuuid: 5A83E8C4-1638-A84F-1625-D8F1946A8B84
      kernelversion: 3.10.0-957.el7.x86_64
      osimage: CentOS Linux 7 (Core)
      operatingsystem: linux
      architecture: amd64
      serialnumber: 00ccf87433c5321f1161353890bc9be43ffa23ec7af8027a9d45ede6e17b558d
    - machineid: b410afa7975d4ff7aff6cee38f6c8e81
      systemuuid: 4C4C4544-0043-3610-8059-B7C04F473533
      kernelversion: 3.10.0-1160.49.1.el7.x86_64
      osimage: CentOS Linux 7 (Core)
      operatingsystem: linux
      architecture: amd64
      serialnumber: 1aadcbb5b667fdf600979436e010ae715e119565d9257cf36d27d49102464b61
    - machineid: 368771bdc8dd44a8a9e71683f637ebc5
      systemuuid: db9d7d04-f3e4-e276-88d4-10b43f95e4a2
      kernelversion: 5.15.0-46-generic
      osimage: Ubuntu 20.04.4 LTS
      operatingsystem: linux
      architecture: amd64
      serialnumber: 15c3b07faa322cc3307760ed743c2298ce26b3fd0b5bfb9b5c24f7d3bc7c4362
    - machineid: cc769cae8f6e42b7ba72b99b70b66f46
      systemuuid: 231fc755-06a0-5158-cc2f-96362862202a
      kernelversion: 5.15.0-43-generic
      osimage: Ubuntu 20.04.2 LTS
      operatingsystem: linux
      architecture: amd64
      serialnumber: a9766e1e1952de483490b16b50ffe6940e7df370dc6312a06b843f86f3d35cad
    customerinfo:
      customer: Transwarp83
      project: TestLicenseForever
      desc: Test Desc
  issuer:
    signer: <EMAIL>
    parentrawcert: |
      -----BEGIN TRANSWARP SOPHON AUTOCV CERT-----
      MIIC+TCCAfEwDBMIZnV4aW4ubGkTADAoFxEyMjAxMDEwMDAwMDArMDgwMBgTMjA5
      OTAxMDEwMDAwMDArMDgwMDCBqAwZZWRnZS1zdXBwb3J0QHRyYW5zd2FycC5pbzAL
      MAkTBUFETUlOMAAwETAPEwATABMAEwATABMADAEqMGsTCVRyYW5zd2FycBMSU29w
      aG9uIEVkZ2UgQXV0b0NWE0pUaGUgdHJ1c3RlZCByb290IGxpY2Vuc2UgY2VydCBv
      ZiBhbGwgdHJhbnN3YXJwIHNvcGhvbiBlZGdlIGF1dG9jdiBwcm9kdWN0LjCCAQoC
      ggEBAJjJexUZBTA2YFqolgSe/cWGECx58IdVcWkW/9sxFDah8JeWYxcSY+RAxAkh
      gxL1WjjKaY4CpzNLv0HBezQ4k3c1+8pr/HJqXzRjaoeUr0vXRFs9kw7CJUNZuTEt
      xrO9aQNd1xUt+0ruyIj9e8vQ3hylKir1f1xWwYc2pBXuNh4DChVObrmy9Y4EN5NI
      AwcbJvIIZV+FyWekgispXfGiIf7fVojJcv3G8Jpt14IXYzrnUSiJqWhjRk/SaSqs
      eSADa1fDvkVnvfuKIDHmbJAuwQWXI6B7dNM2EK3bi+PTQdlVTS/6pp2iPUptqEpe
      EbF32vdW6OfDLCOZNKpSVjNAMRcCAwEAAQSCAQAZz0oC0OkY6gWgPhCpj/unIT/+
      kTfPrbKgTQZljiPyZxC/rBpzU59qr7FeKxW6b9rG2NDJNBiYzae2ucNP2uBfaxid
      9StFvjjs2fM46woOuFlLmrQtyqkOAwwNgsTOXtzfm6xTKVzqywxe+d2oKebcSFp8
      og5zdBtXKVxYNINtbdxnZKip/g4bwDWvWxZzjUcrr3Rdsspxlgpu+WjMURZZcOM9
      1fygLWkAaosMFSYmDX12Ba2l7jpcGqaJ0cFG0LhXzlhgshR+Kzl6FzHOv+JVKy3O
      sv/3tLTv44NkUB0YcchXypU9yf3uoW3GMDv9qs1Ty8nd8U5IuPoVfIqUEkes
      -----END TRANSWARP SOPHON AUTOCV CERT-----
  publickey:
    "n": 24999084171257546436087080645991425843072417597437956838244787605479329976419628202907999898391410602408079915829290198964842393321381132731034174545406990169215927871636409140689370985857034455288024386643827632711813207865467142711283781483610684948319431673651724220095850656225532390011674140373490930532000516992828584276730085816912283996666795503628029815388385806799491451252707585032771792384893462124730506261230366258504877944438629764011443730344621120462655330794509980941212123240099822647162119220448545100618779435698791718074638852684525325504805922474514905970538733740570147885192014025963779952153
    e: 65537
  notbefore: 2022-08-25T05:14:04Z
  notafter: 2099-08-25T05:14:04Z
  signature: []
  raw: []
  rawtbscertificate: []
  rawsubjectpublickey: []
  rawsubject: []
  rawissuer: []
`

func TestParseLicenseFromOut(t *testing.T) {
	type args struct {
		content []byte
	}

	t.Run("test parse license", func(t *testing.T) {
		got, err := ParseLicenseFromOut([]byte(content))
		if err != nil {
			t.Errorf("ParseLicenseFromOut() error = %v", err)
			return
		}
		if exp, act := 9, len(got.Subject.AuthScope); act != exp {
			t.Errorf("Unexpected auth scope count %d != %d", act, exp)
		}
	})
}
