package license

import (
	"context"
	"crypto/rsa"
	"fmt"
	"math/big"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/license/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DefaultCmdTimeout = time.Second * 5

	VerifierOptCheck = "-check" // 检查指定项的授权
	VerifierOptAddr  = "-addr"  // 指定授权服务器Licensor的地址
	VerifierOptInfo  = "-info"  // 输出Verifier自身与授权服务器的相关信息（yaml格式）
	VerifierOptSign  = "-sign"  // 使用Verifier预置私钥对给定的字节进行签名

	PublicKeyE = 65537
	PublicKeyN = "24760843118593492627331911185628386596111476909251873392649538668562508877771867238838858635392044409564638812550834563129896956877867415714798913290188367378399937635355897143042955593759695054230002497548056517493084311874165180664176418053560734541952634792394890892333322725424872448635163576290416004200222188268048414628783918238553420542926601601885943320106870463714817453744694241895727116600429182971387031373593536437586063977556234168916905247369657684210396461903971414358738760044844217894668233661880678201449443206749946878947496621944807395723962054397307167403996626806967227820239104530396341834953"

	DefaultCheckInterval = 5 * time.Second
)

var (
	once       = sync.Once{}
	c          *checker
	pk         *rsa.PublicKey
	defaultCfg *conf.LicenseConfig
)

func init() {
	pk = &rsa.PublicKey{
		N: big.NewInt(0),
		E: PublicKeyE,
	}
	pk.N.SetString(PublicKeyN, 10)
	defaultCfg = &conf.LicenseConfig{
		VerifierPath:  "/usr/local/bin/verifier",
		LicensorAddr:  "http://autocv-licensor-service:80",
		CheckInterval: DefaultCheckInterval,
	}
}

// NewLicenseChecker 用于从给定的配置中,创建AutoCV/LLMOps平台产品授权的检查器
// 如果 cfg 为空,则将使用默认的license checker config
func NewLicenseChecker(cfg *conf.LicenseConfig, component models.AuthType, callback func(activated bool)) (Checker, error) {
	if string(component) == "" {
		return nil, stderr.InvalidParam.Errorf("the component of license is necessary")
	}
	if !models.IsSupportAuthType(component) {
		return nil, stderr.LicenseUnsupportedAuthError.Errorf("auth type: %s", component)
	}

	if cfg == nil {
		cfg = defaultCfg
	}
	if cfg.CheckInterval == 0 || cfg.CheckInterval < time.Second {
		cfg.CheckInterval = defaultCfg.CheckInterval
	}
	if cfg.VerifierPath == "" {
		cfg.VerifierPath = defaultCfg.VerifierPath
	}
	if cfg.LicensorAddr == "" {
		cfg.LicensorAddr = defaultCfg.LicensorAddr
	}
	return NewChecker(context.Background(), cfg.VerifierPath, cfg.LicensorAddr, component, cfg.CheckInterval, callback)
}

// NewChecker 用于创建AutoCV/LLMOps平台产品授权的检查器
func NewChecker(ctx context.Context, verifierPath string, licensorAddr string, component models.AuthType,
		checkInterval time.Duration, callback func(activated bool)) (Checker, error) {
	if verifierPath == "" {
		return nil, fmt.Errorf("path of Verifier cannot be empty")
	}
	if licensorAddr == "" {
		return nil, fmt.Errorf("addr of licensor cannot be empty")
	}

	d, ok := models.GetLicenseAuthDefine(component)
	if !ok {
		return nil, fmt.Errorf("unrecognized auth type: %s", component)
	}
	if d.Category != models.ComponentCategory {
		return nil, fmt.Errorf("unrecognized componet type: %s", component)
	}

	if checkInterval < DefaultCheckInterval {
		checkInterval = DefaultCheckInterval
	}
	if callback == nil {
		return nil, fmt.Errorf("callback cannot be nil")
	}

	once.Do(func() {

		c = &checker{
			ctx: ctx,

			verifier:   NewVerifier(verifierPath, licensorAddr),
			checkScope: component,

			checkInterval: checkInterval,
			callback:      callback,
		}
	})
	return c, nil
}

type Checker interface {
	Serve()
}

type checker struct {
	ctx context.Context

	activated bool

	verifier   *Verifier
	checkScope models.AuthType

	checkInterval time.Duration
	callback      func(activated bool)
}

func (c *checker) Serve() {
	doCheck := func() {
		if !c.check() == c.activated { // 只有当前组件 license 有效性改变时才会触发回调函数
			c.activated = !c.activated

			if c.activated {
				stdlog.Infoln("success to pass license's checking~~~")
			} else {
				stdlog.Warnln("failed to pass license's checking!!!")
			}
			c.callback(c.activated)
		}
	}
	stdlog.Infof("begin to check license periodically with interval %s", c.checkInterval.String())
	ticker := time.NewTicker(c.checkInterval)
	defer ticker.Stop()
	doCheck()
	for {
		select {
		case <-ticker.C:
			doCheck()
		case <-c.ctx.Done():
			return
		}
	}
}

func (c *checker) check() bool {
	if !c.verifier.LicenseEnable() {
		return true
	}

	if err := c.verifier.CheckAuth(string(c.checkScope)); err != nil {
		stdlog.WithError(err).Errorf("verification of %s failed", c.checkScope)
		return false
	}
	return true
}
