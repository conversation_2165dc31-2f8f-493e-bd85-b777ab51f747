package license

import (
	"bytes"
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"time"

	"gopkg.in/yaml.v2"

	"transwarp.io/applied-ai/aiot/vision-std/license/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

var VerifyTimeOut = 10 * time.Second

const (
	envTimeout   = "LICENSE_VERIFY_TIMEOUT"
	envMgcSecret = "LICENSE_DISABLE_SECRET"
)

func init() {
	durStr := os.Getenv(envTimeout)
	if durStr == "" {
		return
	}
	dur, err := time.ParseDuration(durStr)
	if err != nil {
		stdlog.Warnf("%s specified but not valid: %s", envTimeout, durStr)
		return
	}
	if dur <= VerifyTimeOut {
		stdlog.Warnf("%s specified but less than the min timeout: %s < %s", envTimeout, durStr, VerifyTimeOut.String())
		return
	}
	VerifyTimeOut = dur
}

type Verifier struct {
	verifierPath string
	licensorAddr string
	pk           *rsa.PublicKey
}

func NewVerifier(verifierPath string, licensorAddr string) *Verifier {
	return &Verifier{
		verifierPath: verifierPath,
		licensorAddr: licensorAddr,
		pk:           pk,
	}
}

func (v *Verifier) LicenseEnable() bool {
	env := os.Getenv(envMgcSecret)
	if env == "52autocv" || env == "52llmops" {
		return false
	}
	return true
}

// LicensorCert 返回当前集群授权服务器自身的授权证书信息
func (v *Verifier) LicensorCert() (*models.LicenseCert, error) {
	if err := v.verifySelfV101(); err != nil {
		return nil, err
	}

	buf := bytes.NewBuffer(nil)
	cmd := exec.Command(v.verifierPath, VerifierOptInfo, "all", VerifierOptAddr, v.licensorAddr)
	cmd.Stdout = buf
	if err := toolkit.RunCommandWithTimeout(VerifyTimeOut, cmd); err != nil {
		return nil, stderr.Wrap(err, "failed to get licensor info")
	}
	return ParseLicenseFromOut(buf.Bytes())
}

// CheckAuth 检查是否具有某项权限
func (v *Verifier) CheckAuth(auth string) error {
	if err := v.verifySelfV101(); err != nil {
		return err
	}

	var args []string
	args = append(args, VerifierOptAddr, v.licensorAddr)
	args = append(args, VerifierOptCheck, auth)
	cmd := exec.Command(v.verifierPath, args...)
	var cmdOut bytes.Buffer
	cmd.Stdout = &cmdOut
	if err := toolkit.RunCommand(cmd); err != nil {
		return stderr.Internal.Cause(err, "failed to check auth with cmd: %s, stdout: %s", cmd.String(), cmdOut.String())
	}
	return nil
}

// verifySelfV101 对应新版本的verifier校验机制（https://sophon-edge.yuque.com/ta73lw/eqpq5x/gwyluy#YrAuf）
// 具体步骤如下：
// 	1. 生成任意长度的字节数组 ->rand_bs
//	2. 生成rand_bs的16进制字符串 -> rand_hex
//	3. 使用 /usr/local/bin/verifier的sign方法，生成签名字节数组的16进制字符串 -> sig_hex
//	  a. 通过直接读取 cmd 执行后的 stdout 数据来获取 sig_hex
//	4. 将sig_hex进行解码获取原始的签名数组 -> sig
//	5. 计算rand_bs的哈希值(信息摘要) -> SHA256(rand_bs) = digst
//	6. 使用预置的公钥对签名进行验证 -> verify(digst, sig)
func (v *Verifier) verifySelfV101() error {
	ok, _ := utils.Exists(v.verifierPath)
	if !ok {
		return fmt.Errorf("the Verifier doesn't exist at the given path: %s", v.verifierPath)
	}

	randBs := make([]byte, 2048)
	_, _ = rand.Read(randBs)
	hash := crypto.SHA256.New()
	_, err := hash.Write(randBs)
	if err != nil {
		return err
	}

	randHex := fmt.Sprintf("%x", randBs)

	ctx, cancel := context.WithTimeout(context.Background(), VerifyTimeOut)
	defer cancel()
	cmd := exec.CommandContext(ctx, v.verifierPath, VerifierOptSign, randHex)
	buf := bytes.NewBuffer(nil)
	cmd.Stdout = buf
	if err := cmd.Run(); err != nil {
		return stderr.Internal.Cause(err, "failed to sign random bytes with Verifier")
	}
	sigHex, err := ioutil.ReadAll(buf)
	if err != nil {
		return stderr.Internal.Cause(err, "failed to read signature of random bytes")
	}

	sig, err := hex.DecodeString(string(sigHex))
	if err != nil {
		return stderr.Internal.Cause(err, "failed to decode sig hex")
	}

	if err := rsa.VerifyPKCS1v15(v.pk, crypto.SHA256, hash.Sum(nil), sig); err != nil {
		return stderr.Internal.Cause(err, "failed to verify Verifier")
	}
	return nil
}

func ParseLicenseFromOut(content []byte) (*models.LicenseCert, error) {
	out := struct {
		Licensor *models.LicenseCert `yaml:"licensor"`
	}{
		Licensor: new(models.LicenseCert),
	}

	if err := yaml.Unmarshal(content, out); err != nil {
		return nil, stderr.Internal.Cause(err, "failed to parse license cert from verifier output")
	}

	return out.Licensor, nil
}
