package health

import (
	"encoding/json"
	"fmt"
	"net/http"
)

// 使用方法
// 在main.go文件中直接 import 即可
//  import  (
//	_ "transwarp.io/applied-ai/aiot/vision-std/health"
//  )

func init() {
	http.HandleFunc("/healthz", Health)
}

func Health(w http.ResponseWriter, r *http.Request) {
	w.<PERSON>er().Set("Content-Type", "application/json; charset=utf-8")

	resp := &CheckHealthRsp{
		Code:   "UP",
		Status: "UP",
	}
	b, err := json.Marshal(resp)
	if err != nil {
		w.<PERSON>er().Set("Content-Type", "text/plain; charset=utf-8")
		w.<PERSON><PERSON>ead<PERSON>(http.StatusInternalServerError)
		fmt.Fprintln(w, "Server is not ready.")
	}

	w.Write(b)
}

type CheckHealthRsp struct {
	Code    string            `json:"code"`
	Status  string            `json:"status"`
	Details map[string]string `json:"details"`
}
