package llm

import (
	"context"
	"net/http"
	"net/url"

	"github.com/sasha<PERSON>nov/go-openai"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type OpenAIClient struct {
	*openai.Client
}

func (o *OpenAIClient) GetModel(ctx context.Context, modelLocator string) (*LLMModel, error) {
	return getModel(o.Client, ctx, modelLocator)
}

func (o *OpenAIClient) ListModels(ctx context.Context) ([]*LLMModel, error) {
	return listModels(o.Client, ctx)
}

func (o *OpenAIClient) CreateChatCompletion(ctx context.Context, modelName string, req *triton.LLMChatReq) (string, error) {
	return createChatCompletion(o.Client, ctx, modelName, req)
}

func (o *OpenAIClient) CreateChatCompletionStream(ctx context.Context, modelName string, req *triton.LLMChatReq) error {
	return createChatCompletionStream(o.Client, ctx, modelName, req)
}

func (o *OpenAIClient) CreateEmbeddings(ctx context.Context, modelName string, req *triton.Text2VecReqV2) (*triton.TextVecResV2, error) {
	return createEmbeddings(o.Client, ctx, modelName, req)
}

func newOpenAIClient(cfg *OpenAIConfig) (*OpenAIClient, error) {
	if cfg == nil {
		return nil, stderr.Internal.Error("openai config is nil")
	}
	if cfg.ProxyUrl != "" {
		config := openai.DefaultConfig(cfg.ApiKey)
		proxyUrl, err := url.Parse(cfg.ProxyUrl)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to parse proxy url %s", cfg.ProxyUrl)
		}
		transport := &http.Transport{
			Proxy: http.ProxyURL(proxyUrl),
		}
		config.HTTPClient = &http.Client{Transport: transport}
		c := openai.NewClientWithConfig(config)
		return &OpenAIClient{
			Client: c,
		}, nil
	}
	c := openai.NewClient(cfg.ApiKey)
	return &OpenAIClient{Client: c}, nil
}
