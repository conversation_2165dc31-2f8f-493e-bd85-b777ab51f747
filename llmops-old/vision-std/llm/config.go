package llm

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type LLMInferConfig interface {
	AsJson() string
}
type OpenAIConfig struct {
	ApiKey   string `json:"api_key,omitempty" yaml:"api_key"`
	ProxyUrl string `json:"proxy_url,omitempty" yaml:"proxy_url"`
}

func (o *OpenAIConfig) AsJson() string {
	b, _ := json.Marshal(o)
	return string(b)
}

type AzureAIConfig struct {
	ApiKey   string `json:"api_key,omitempty" yaml:"api_key"`
	Endpoint string `json:"endpoint,omitempty" yaml:"endpoint"`
	ProxyUrl string `json:"proxy_url,omitempty" yaml:"proxy_url"`
}

func (a *AzureAIConfig) AsJson() string {
	b, _ := json.Marshal(a)
	return string(b)
}

type MWHAIConfig struct {
	triton.GrpcConfig
}

func (m *MWHAIConfig) AsJson() string {
	b, _ := json.Marshal(m)
	return string(b)
}
