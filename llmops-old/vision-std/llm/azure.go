package llm

import (
	"context"
	"net/http"
	"net/url"

	"github.com/sasha<PERSON>nov/go-openai"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type AzureAIClient struct {
	*openai.Client
}

func (a *AzureAIClient) GetModel(ctx context.Context, modelLocator string) (*LLMModel, error) {
	return getModel(a.Client, ctx, modelLocator)
}

func (a *AzureAIClient) ListModels(ctx context.Context) ([]*LLMModel, error) {
	return listModels(a.Client, ctx)
}

func (a *AzureAIClient) CreateChatCompletion(ctx context.Context, modelName string, req *triton.LLMChatReq) (string, error) {
	return createChatCompletion(a.Client, ctx, modelName, req)
}

func (a *AzureAIClient) CreateChatCompletionStream(ctx context.Context, modelName string, req *triton.LLMChatReq) error {
	return createChatCompletionStream(a.Client, ctx, modelName, req)
}

func (a *AzureAIClient) CreateEmbeddings(ctx context.Context, modelName string, req *triton.Text2VecReqV2) (*triton.TextVecResV2, error) {
	return createEmbeddings(a.Client, ctx, modelName, req)
}

func newAzureAIClient(cfg *AzureAIConfig) (*AzureAIClient, error) {
	if cfg == nil {
		return nil, stderr.Internal.Error("azure ai config is nil")
	}
	config := openai.DefaultAzureConfig(cfg.ApiKey, cfg.Endpoint)
	if cfg.ProxyUrl != "" {
		proxyUrl, err := url.Parse(cfg.ProxyUrl)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to parse proxy url")
		}
		transport := &http.Transport{
			Proxy: http.ProxyURL(proxyUrl),
		}
		config.HTTPClient = &http.Client{Transport: transport}
	}
	c := openai.NewClientWithConfig(config)
	return &AzureAIClient{Client: c}, nil
}
