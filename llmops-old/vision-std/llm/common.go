package llm

import (
	"context"
	"errors"
	"io"

	"github.com/sashabaranov/go-openai"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type LLMModel struct {
	openai.Model
	Name    string `json:"name,omitempty" yaml:"name"`
	Version string `json:"version,omitempty" yaml:"version"`
	Stat    string `json:"stat,omitempty" yaml:"stat"`
	Reason  string `json:"reason,omitempty" yaml:"reason"`
}

// LLMReq2OpenAIReq 将vision-std定义的llm chat请求转为标准openai的格式
func LLMReq2OpenAIReq(llmReq *triton.LLMChatReq) *openai.ChatCompletionRequest {
	temperature := float32(1)
	customTem, ok := llmReq.Params["temperature"]
	if ok {
		v, _ := customTem.(float32)
		temperature = v
	}
	topP := float32(1)
	customTopP, ok := llmReq.Params["top_p"]
	if ok {
		v, _ := customTopP.(float32)
		topP = v
	}
	maxTokens := 4096
	customMaxLength, ok := llmReq.Params["max_length"]
	if ok {
		v, _ := customMaxLength.(int)
		maxTokens = v
	}
	prompt := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: "You are a helpful assistant.",
	}
	messages := []openai.ChatCompletionMessage{prompt}
	for _, history := range llmReq.History {
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: history.Q,
		}, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleAssistant,
			Content: history.A,
		})
	}
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: llmReq.Query,
	})
	return &openai.ChatCompletionRequest{
		Model:       openai.GPT3Dot5Turbo,
		Messages:    messages,
		Temperature: float32(temperature),
		TopP:        topP,
		Stream:      llmReq.Stream,
		MaxTokens:   maxTokens,
	}
}

func getModel(c *openai.Client, ctx context.Context, modelLocator string) (*LLMModel, error) {
	m, err := c.GetModel(ctx, modelLocator)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get openai models")
	}
	return &LLMModel{Model: m}, nil
}

func listModels(c *openai.Client, ctx context.Context) ([]*LLMModel, error) {
	ms, err := c.ListModels(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get open ai models")
	}
	models := make([]*LLMModel, 0)
	for _, m := range ms.Models {
		models = append(models, &LLMModel{Model: m})
	}
	return models, nil
}

func createChatCompletion(c *openai.Client, ctx context.Context, modelName string, req *triton.LLMChatReq) (string, error) {
	r := LLMReq2OpenAIReq(req)
	rsp, err := c.CreateChatCompletion(ctx, *r)
	if err != nil {
		return "", stderr.Wrap(err, "failed get openai response")
	}
	return rsp.Choices[0].Message.Content, nil
}

func createChatCompletionStream(c *openai.Client, ctx context.Context, modelName string, req *triton.LLMChatReq) error {
	r := LLMReq2OpenAIReq(req)
	stream, err := c.CreateChatCompletionStream(ctx, *r)
	if err != nil {
		return stderr.Wrap(err, "failed to get openai stream chat")
	}
	defer stream.Close()
	handler := req.StreamOutputHandler(triton.StdTextGenOutputRes)
	for {
		response, err := stream.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				stdlog.Info("openai stream chat complete")
				return nil
			}
			return stderr.Wrap(err, "failed to get openai stream chat")
		}
		handler([]byte(response.Choices[0].Delta.Content))
	}
}

func createEmbeddings(c *openai.Client, ctx context.Context, modelName string, req *triton.Text2VecReqV2) (*triton.TextVecResV2, error) {
	rsp, err := c.CreateEmbeddings(ctx, openai.EmbeddingRequestStrings{
		Input: req.Texts,
		Model: openai.AdaEmbeddingV2,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get openai embedding")
	}
	result := make([][]float64, 0)
	for _, embeddings := range rsp.Data {
		col := make([]float64, 0)
		for _, embedding := range embeddings.Embedding {
			col = append(col, float64(embedding))
		}
		result = append(result, col)
	}
	return &triton.TextVecResV2{Results: result}, nil
}
