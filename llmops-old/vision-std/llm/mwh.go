package llm

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type MWHAIClient struct {
	triton.Client
}

func (m *MWHAIClient) GetModel(ctx context.Context, modelLocator string) (*LLMModel, error) {
	ms, err := m.Client.ListModels(true)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get model in mwh")
	}
	for _, m := range ms {
		if m.Name == modelLocator {
			return &LLMModel{
				Name:    m.Name,
				Version: m.Version,
				Stat:    m.State,
				Reason:  m.Reason,
			}, nil

		}
	}
	return nil, stderr.Internal.Error("not found model %s in mwh", modelLocator)
}

func (m *MWHAIClient) ListModels(ctx context.Context) ([]*LLMModel, error) {
	ms, err := m.Client.ListModels(true)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get model in mwh")
	}
	models := make([]*LLMModel, 0)
	for _, m := range ms {
		models = append(models, &LLMModel{
			Name:    m.Name,
			Version: m.Version,
			Stat:    m.State,
			Reason:  m.Reason,
		})
	}
	return models, nil
}

// CreateChatCompletion use CreateChatCompletionStream instead
func (m *MWHAIClient) CreateChatCompletion(ctx context.Context, modelName string, req *triton.LLMChatReq) (string, error) {
	return "", nil
}

func (m *MWHAIClient) CreateChatCompletionStream(ctx context.Context, modelName string, req *triton.LLMChatReq) error {
	return m.StreamModelInfer(ctx, modelName, req)
}

func (m *MWHAIClient) CreateEmbeddings(ctx context.Context, modelName string, req *triton.Text2VecReqV2) (*triton.TextVecResV2, error) {
	result, err := m.SyncModelInfer(modelName, req)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get embedding of mwh")
	}
	return req.ParseResult(result)
}

func newMWHAIClient(cfg *MWHAIConfig) (*MWHAIClient, error) {
	if cfg == nil {
		return nil, stderr.Internal.Error("mwh ai config is nil")
	}
	c, err := triton.NewTritonGrpcClient(cfg.GrpcConfig)
	if err != nil {
		return nil, stderr.Wrap(err, "failed get mwh ai client")
	}
	return &MWHAIClient{Client: c}, nil
}
