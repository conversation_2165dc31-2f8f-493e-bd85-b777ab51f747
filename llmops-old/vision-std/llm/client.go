package llm

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type StreamOutputHandler func(string) error

type LLMInferClient interface {
	GetModel(ctx context.Context, modelLocator string) (*LLMModel, error)
	ListModels(ctx context.Context) ([]*LLMModel, error)
	CreateChatCompletion(ctx context.Context, modelName string, req *triton.LLMChatReq) (string, error)
	CreateChatCompletionStream(ctx context.Context, modelName string, req *triton.LLMChatReq) error
	CreateEmbeddings(ctx context.Context, modelName string, req *triton.Text2VecReqV2) (*triton.TextVecResV2, error)
}

func NewLLMInferClient(cfg LLMInferConfig) (LLMInferClient, error) {
	if openaiCfg, ok := cfg.(*OpenAIConfig); ok {
		return newOpenAIClient(openaiCfg)
	}
	if azureCfg, ok := cfg.(*AzureAIConfig); ok {
		return newAzureAIClient(azureCfg)
	}
	if mwhaiCfg, ok := cfg.(*MWHAIConfig); ok {
		return newMWHAIClient(mwhaiCfg)
	}
	return nil, stderr.Internal.Error("not found llm infer config")
}
