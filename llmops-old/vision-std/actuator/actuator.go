package actuator

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"github.com/google/uuid"
	"go.etcd.io/etcd/api/v3/v3rpc/rpctypes"
	clientv3 "go.etcd.io/etcd/client/v3"

	stdconf "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	KeyPrefix = "/sophon-registry"
)

func NewAPI(root string, c stdconf.ActuatorConfig) *restful.WebService {
	if c.Enable {
		go register2BaseGateway(c)
	}
	return (&Resource{}).WebService(root)
}

func register2BaseGateway(c stdconf.ActuatorConfig) {
	rc, err := newRegistryClient(c)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to create a new registry client: %+v", c)
		return
	}

	if err := rc.register(newServiceInfo(c)); err != nil {
		stdlog.WithError(err).Errorf("failed to register service using %+v", c)
		return
	}
}

func newServiceInfo(c stdconf.ActuatorConfig) *serviceInfo {
	randomId := strings.Replace(uuid.New().String(), "-", "", -1)
	if len(randomId) > 10 {
		randomId = randomId[:10]
	}
	return &serviceInfo{
		infraServiceID: c.InfraServiceID,
		ServiceID:      fmt.Sprintf("%s-%s", randomId, c.RegisterComponent),
		ServiceName:    c.RegisterComponent,
		Address:        c.RegisterIp,
		Port:           c.RegisterPort,
		ConfigInfo: map[string]string{
			"version": c.RegisterComponentVersion,
		},
	}
}

type serviceInfo struct {
	infraServiceID string

	ServiceID   string            `json:"id"`
	ServiceName string            `json:"serviceName"`
	Address     string            `json:"address"`
	Port        string            `json:"port"`
	Secure      bool              `json:"secure"`
	License     string            `json:"license"`
	ConfigInfo  map[string]string `json:"configInfo"`
}

func (s *serviceInfo) Key() (string, error) {
	serviceID := s.infraServiceID
	if serviceID == "" {
		return "", stderr.Internal.Error("infra's service ID is unset")
	}
	return fmt.Sprintf("%s/%s/%s/%s", KeyPrefix, serviceID, s.ServiceName, s.ServiceID), nil
}

func newRegistryClient(c stdconf.ActuatorConfig) (*registryClient, error) {
	endpoints := strings.Split(c.EtcdEndpoints, ",")
	return &registryClient{
		Endpoints:         endpoints,
		DialTimeout:       c.EtcdDialTimeout,
		RegisterComponent: c.RegisterComponent,
		RegisterInterval:  c.RegisterInterval,
	}, nil
}

type registryClient struct {
	Endpoints   []string
	DialTimeout time.Duration

	RegisterComponent string
	RegisterInterval  time.Duration
}

func (r *registryClient) register(info *serviceInfo) error {
	c, err := clientv3.New(clientv3.Config{
		Endpoints:   r.Endpoints,
		DialTimeout: r.DialTimeout,
	})
	if err != nil {
		return stderr.Internal.Cause(err, "failed to connect with etcd: %+v", r.Endpoints)
	}
	defer func() {
		_ = c.Close()
	}()

	kvc, lease := clientv3.NewKV(c), clientv3.NewLease(c)
	var leaseID clientv3.LeaseID = 0

	for {
		if leaseID == 0 {
			leaseRsp, err := lease.Grant(context.TODO(), 5)
			if err != nil {
				return err
			}

			serviceKey, err := info.Key()
			if err != nil {
				return err
			}
			serviceInfo, err := json.Marshal(info)
			if err != nil {
				return err
			}
			if _, err := kvc.Put(context.TODO(), serviceKey, string(serviceInfo), clientv3.WithLease(leaseRsp.ID)); err != nil {
				return err
			}
			leaseID = leaseRsp.ID
		} else {
			if _, err := lease.KeepAliveOnce(context.TODO(), leaseID); err == rpctypes.ErrLeaseNotFound {
				leaseID = 0
				continue
			}
		}
		time.Sleep(r.RegisterInterval)
	}
}

type Resource struct{}

func (r *Resource) WebService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{"actuator"}
	ws.Route(ws.GET("/health").To(r.CheckHealth).
		Doc("提供给 Sophon Base Gateway 使用的探活接口").Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Writes(CheckHealthRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), CheckHealthRsp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	return ws
}

func (r *Resource) CheckHealth(request *restful.Request, response *restful.Response) {
	stdsrv.SuccessResponse(response, &CheckHealthRsp{
		Code:   "UP",
		Status: "UP",
	})
}

type CheckHealthRsp struct {
	Code    string            `json:"code"`
	Status  string            `json:"status"`
	Details map[string]string `json:"details"`
}
