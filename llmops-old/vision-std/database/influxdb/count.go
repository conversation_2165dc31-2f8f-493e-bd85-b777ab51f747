package influxdb

import (
	"fmt"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// InfluxCountReq 对InfluxDB进行数量统计的请求
type InfluxCountReq struct {
	CountField      string        // 进行计数的字段, 必须
	Measurement     string        // 计数的表名, 必须
	GroupByTags     []string      // 进行聚合的Tag字段, 非必须
	RetentionPolicy string        // 计数的持久化策略, 默认为 DefaultRetentionPolicy
	Database        string        // 计数的表所在数据库, 默认为 DefaultDatabase
	GroupByInterval time.Duration // 进行聚合的时间间隔, 默认为 DefaultGroupInterval
	CountDuration   time.Duration // 进行计数的时间段, 默认为 DefaultCountDuration
	TagFilter       *TagFilter    // 用于计数的范围进行筛选
}

const (
	DefaultGroupInterval = time.Minute * 10
	DefaultCountDuration = time.Hour * 12
)

// IsValid 判断当前InfluxDB计数请求是否有效
func (r InfluxCountReq) IsValid() error {
	if r.CountField == "" {
		return stderr.InvalidParam.Error("'CountFiled' is necessary")
	}
	if r.Measurement == "" {
		return stderr.InvalidParam.Error("'Measurement' is necessary")
	}
	return nil
}

// InfluxQL 根据给定的InfluxDB计数请求，返回其对应的InfluxQL
func (r InfluxCountReq) InfluxQL() string {
	groupBy := ""
	if len(r.GroupByTags) != 0 {
		groupBy = fmt.Sprintf(`, "%s"`, strings.Join(r.GroupByTags, `", "`))
	}
	if r.GroupByInterval == 0 {
		stdlog.Infof("GroupByInterval is empty, replace it with default value: %s", DefaultGroupInterval.String())
		r.GroupByInterval = DefaultGroupInterval
	}
	if r.CountDuration == 0 {
		stdlog.Infof("CountDuration is empty, replace it with default value: %s", DefaultCountDuration.String())
		r.CountDuration = DefaultCountDuration
	}
	if r.Database == "" {
		stdlog.Infof("Database is empty, replace it with default value: %s", DefaultDatabase)
		r.Database = DefaultDatabase
	}
	if r.RetentionPolicy == "" {
		stdlog.Infof("RetentionPolicy is empty, replace it with default value: %s", DefaultRetentionPolicy)
		r.RetentionPolicy = DefaultRetentionPolicy
	}

	return fmt.Sprintf(`
SELECT count("%s") AS "count" 
FROM "%s"."%s"."%s" 
WHERE time > now() - %s 
GROUP BY time(%s) %s
FILL(null)
`, r.CountField, r.Database, r.RetentionPolicy, r.Measurement, r.CountDuration.String(), r.GroupByInterval, groupBy)
}

// InfluxCountSeries 具有相同Tag的一组不同时间端的计数
type InfluxCountSeries struct {
	Counts []int64           `json:"counts"` // 各时间段的计数
	Times  []int64           `json:"times"`  // 各时间段的起始时间 nano second
	Tags   map[string]string `json:"tags"`   // Tags标识Tag的分组信息
}

// Match 返回当前Series是否具有给定的tags
func (s *InfluxCountSeries) Match(tags map[string]string) bool {
	if s == nil {
		return false
	}

	for k, v := range tags {
		sv, ok := s.Tags[k]
		if !ok {
			// 不包含指定tag
			return false
		}
		if sv != v {
			// tag不一致
			return false
		}
	}
	return true
}
// Sum 返回当前Series的计数总和
func (s *InfluxCountSeries) Sum() (sum int64) {
	if s == nil {
		return
	}
	// 计数累加
	for _, c := range s.Counts {
		sum += c
	}
	return sum
}

// MillsTimes 将时间格式转换
func (s *InfluxCountSeries) MillsTimes() []int64 {
	res := make([]int64, len(s.Times))
	for i := range s.Times {
		res[i] = s.Times[i] / 1e6
	}
	return res
}

type InfluxCountRsp struct {
	Series []InfluxCountSeries `json:"series"` // 不同Tag组合对应的计数结果
}

// Count 统计具有给定tags的计数
func (r *InfluxCountRsp) Count(tags map[string]string) int64 {
	if r == nil {
		return 0
	}
	s := r.GetSeries(tags)
	if s == nil {
		return 0
	}
	return s.Sum()
}

// GetSerious 返回和指定Tag匹配的计数序列
func (r *InfluxCountRsp) GetSeries(tags map[string]string) *InfluxCountSeries {
	if r == nil {
		return nil
	}
	for _, s := range r.Series {
		// 跳过不匹配的series
		if !s.Match(tags) {
			continue
		}

		return &s
	}
	return nil
}
