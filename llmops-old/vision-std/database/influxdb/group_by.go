package influxdb

import (
	"fmt"
	"strings"
	"time"
)

type AggrFunction string // 聚合时使用的聚合方式
type FillOption string   // 不含数据的时间间隔返回值填充模式,

const (
	SUM      AggrFunction = "SUM"      // 返回字段值的和
	MODE     AggrFunction = "MODE"     // 返回字段中出现频率最高的值
	MEAN     AggrFunction = "MEAN"     // 返回字段的平均值
	COUNT    AggrFunction = "COUNT"    // 返回非空字段值得数目
	MEDIAN   AggrFunction = "MEDIAN"   // 返回排好序的字段的中位数
	SPREAD   AggrFunction = "SPREAD"   // 返回字段中最大和最小值的差值
	STDDEV   AggrFunction = "STDDEV"   // 返回字段的标准差
	INTEGRAL AggrFunction = "INTEGRAL" // 返回字段曲线下的面积，即是积分
	FIRST    AggrFunction = "FIRST"    // 返回区间内的首个点位的值
	LAST     AggrFunction = "LAST"     // 返回区间内的最后一个点位的值

	FillLinear   FillOption = "linear"   // 返回没有数据的时间间隔的线性插值结果。
	FillNone     FillOption = "none"     // 不返回在时间间隔里没有点的数据
	FillPrevious FillOption = "previous" // 返回时间隔间的前一个间隔的数据
	FillNull     FillOption = "null"     // 默认情况下, 没有数据的GROUP BY time()间隔返回为null作为输出列中的值
	Fill0        FillOption = "0"        // 将null值替换为0
)

// NewFillOption 返回"使用给定数值作为没有数据点的时间间隔"的填充模式
func NewFillOption(num float64) FillOption {
	return FillOption(fmt.Sprintf("%f", num))
}

// SELECT <function>(<field_key>)
// FROM_clause
// WHERE <time_range>
// GROUP BY time(<time_interval>),[tag_key] [fill(<fill_option>)]
type groupBy struct {
	tags         []string      // 根据Tag聚合
	timeInterval time.Duration // 聚合时间间隔
	function     AggrFunction  // 聚合函数
	fillOption   FillOption    // 不含数据的时间间隔的填充值
}

func NewGroupBy(tags []string, interval time.Duration, function AggrFunction, fillOption FillOption) *groupBy {
	if len(tags) == 0 && interval == 0 {
		return nil
	}
	if function == "" {
		function = MEAN
	}
	if fillOption == "" {
		fillOption = FillNull
	}
	return &groupBy{
		tags:         tags,
		timeInterval: interval,
		function:     function,
		fillOption:   fillOption,
	}
}

// statement 返回该对象所对应的InfluxQL中的GROUP BY语句
// 通常形式为: GROUP BY time(<time_interval>,<offset_interval>),[tag_key] [fill(<fill_option>)]
// 若该对象为空或非法, 则返回空字符串
func (g *groupBy) statement() string {
	if g == nil {
		return ""
	}
	statement := `GROUP BY `
	keys := make([]string, 0)
	if g.timeInterval != 0 {
		// 最小单位设置为s， 避免出现小数点，从而导致influxdb group by失败
		g.timeInterval = g.timeInterval - (g.timeInterval % time.Second)
		keys = append(keys, fmt.Sprintf(`time(%s)`, g.timeInterval.String()))
	}
	if len(g.tags) != 0 {
		keys = append(keys, g.tags...)
	}
	if len(keys) == 0 {
		return ""
	}
	statement += strings.Join(keys, ",")
	if g.fillOption != "" {
		statement += fmt.Sprintf(` fill(%s) `, g.fillOption)
	}
	return statement
}
