package influxdb

import (
	"reflect"
	"testing"
	"time"
)

func TestParseUnixTimestamp(t *testing.T) {
	// 2024 01/01 00:01
	ts := 1704038401 // unix seconds
	tm := time.Unix(int64(ts), 0)
	tests := []struct {
		name      string
		timestamp int
		want      time.Time
		want1     bool
	}{
		{name: "sec", timestamp: ts, want: tm, want1: true},
		{name: "mills", timestamp: ts * 1e3, want: tm, want1: true},
		{name: "micro", timestamp: ts * 1e6, want: tm, want1: true},
		{name: "nano ", timestamp: ts * 1e9, want: tm, want1: true},
		{name: "error: too big", timestamp: ts * 1e10, want: time.Time{}, want1: false},
		{name: "error: too small", timestamp: ts / 10, want: time.Time{}, want1: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := ParseUnixTimestamp(int64(tt.timestamp))
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseUnixTimestamp() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("ParseUnixTimestamp() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
