package influxdb

import (
	"math"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

var testWriter InfluxWriter
var testCli *InfluxClient

func initCli() {
	w, err := NewInfluxWriter(InfluxWriterConfig{
		Cfg: conf.InfluxdbConfig{
			URL:      "http://172.16.11.11:8086",
			Database: "thinger",
		},
		MaxBatch:      100,
		MaxConcurrent: 5,
	}, nil)
	if err != nil {
		panic(err)
	}
	testWriter = w

	cli, err := NewInfluxClient(conf.InfluxdbConfig{
		URL:      "http://172.16.11.11:8086",
		Database: "thinger",
	})
	testCli = cli
	if err != nil {
		panic(err)
	}
}

func BenchmarkWriter_Write(b *testing.B) {
	initCli()
	for i := 0; i < b.N; i++ {
		testWriter.Write(newTestPoint())
	}
}

func BenchmarkNormal_Write(b *testing.B) {
	initCli()
	for i := 0; i < b.N; i++ {
		np := newTestPoint()
		testCli.WritePoint(np.Measurement, np.Tags, np.Fields, time.Now())
	}
}

func newTestPoint() Point {
	return Point{
		Measurement: "test_influx_writer",
		Tags:        map[string]string{"tag1": "tag1", "tag2": "tag2", "tag3": "tag3", "tag4": "tag4"},
		Time:        time.Now(),
		Fields:      map[string]interface{}{"field1": 123, "field2": "field2", "field3": true, "field4": math.Pi},
	}
}
