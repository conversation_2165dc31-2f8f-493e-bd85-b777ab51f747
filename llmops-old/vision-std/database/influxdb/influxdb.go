package influxdb

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/influxdata/influxdb/client"
	"github.com/influxdata/influxdb/models"
	"github.com/pkg/errors"
	"net/http"
	"net/url"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	DefaultDatabase        = "thinger"
	DefaultRetentionPolicy = "autogen"
)

type InfluxClient struct {
	Client           *client.Client
	URL              string
	Database         string
	UserAgent        string
	Precision        string
	RetentionPolicy  string
	WriteConsistency string
}
type ResultProcessor func(result *QueryResult)

// SetTimeUnitMs 将influxdb查询结果中time时间单位转换为ms
func SetTimeUnitMs() ResultProcessor {
	return func(result *QueryResult) {
		if result == nil {
			return
		}
		for i, column := range result.Columns {
			if column != "time" {
				continue
			}
			for _, value := range result.Values {
				if value == nil {
					continue
				}
				num, ok := value[i].(json.Number)
				if !ok {
					continue
				}
				ns, err := num.Float64()
				if err != nil {
					continue
				}
				value[i] = ns / 1e6
			}
			return
		}
	}
}

func NewInfluxClient(config conf.InfluxdbConfig) (*InfluxClient, error) {
	addr, err := url.Parse(config.URL)
	if err != nil {
		return nil, err
	}
	if config.Database == "" {
		config.Database = DefaultDatabase
	}
	if config.Timeout == 0 {
		config.Timeout = 5 * time.Second
	}
	cli, err := client.NewClient(client.Config{
		URL:       *addr,
		UserAgent: config.UserAgent,
		Proxy:     http.ProxyFromEnvironment,
		Precision: "ns",
		Timeout:   config.Timeout,
	})
	if err != nil {
		return nil, err
	}

	influxClient := &InfluxClient{
		Client:           cli,
		URL:              config.URL,
		Database:         config.Database,
		UserAgent:        config.UserAgent,
		RetentionPolicy:  config.RetentionPolicy,
		WriteConsistency: config.WriteConsistency,
		Precision:        "ns",
	}

	if err := influxClient.CreateDBWithName(config.Database); err != nil {
		return nil, err
	}
	return influxClient, nil
}

func (this *InfluxClient) BatchWrite(bp BatchPoint) error {
	rsp, err := this.Client.Write(bp)
	if err == nil && rsp != nil {
		err = rsp.Error()
	}
	if err != nil {
		stdlog.WithError(err).Errorf("failed to write BatchPoints into InfluxDB")
		return err
	}
	return nil
}

func (this *InfluxClient) Write(data TsDataWriter) error {
	return this.WritePoint(data.Measurement(), data.Tags(), data.Fields(), time.Now())
}

func (this *InfluxClient) WriteWithTime(data TsDataWithTimeWriter) error {
	return this.WritePoint(data.Measurement(), data.Tags(), data.Fields(), data.Time())
}

func (this *InfluxClient) WritePoint(measurement string, tags map[string]string, fields map[string]interface{}, t time.Time) error {
	return this.WritePointWithDb(this.Database, measurement, tags, fields, t)
}

func (this *InfluxClient) WritePointWithDb(db string, measurement string, tags map[string]string, fields map[string]interface{}, t time.Time) error {
	p, err := models.NewPoint(measurement, models.NewTags(tags), fields, t)
	if err != nil {
		return stderr.Database.Cause(err, "failed to build influx point")
	}
	return this.WriteInfluxWithDb(p.String(), db)
}

// measurement,tag keys,tag values,field keys始终是字符串,不用单双引号。
// Field value可以是整数、浮点数、字符串和布尔值
// float－默认; int－i ; string－使用双引号括起来；bool－t/f,true/false
// 当field value是整数，浮点数或是布尔型时，不要使用双引号，不然InfluxDB会假定值是字符串类型
func (this *InfluxClient) WriteInflux(line string) error {
	return this.WriteLineProtocol(line, this.Database, this.RetentionPolicy, this.Precision, this.WriteConsistency)
}

func (this *InfluxClient) WriteInfluxWithRp(line, retentionPolicy string) error {
	return this.WriteLineProtocol(line, this.Database, retentionPolicy, this.Precision, this.WriteConsistency)
}

func (this *InfluxClient) WriteInfluxWithDb(line, db string) error {
	return this.WriteLineProtocol(line, db, this.RetentionPolicy, this.Precision, this.WriteConsistency)
}

func (this *InfluxClient) WriteLineProtocol(line, db, retentionPolicy, precision, writeConsistency string) error {
	rsp, err := this.Client.WriteLineProtocol(line, db, retentionPolicy, precision, writeConsistency)
	if rsp != nil || err != nil {
		return errors.Errorf("write into influx db:[%s] failed using : \"%s\","+
			" error info : \"%s\" ", db, line, err)
	}
	return nil
}

func (this *InfluxClient) QueryInflux(sql string) (*client.Response, error) {
	return this.QueryContext(context.Background(), sql, this.Database, this.RetentionPolicy)
}

func (this *InfluxClient) QueryInfluxContext(ctx context.Context, sql string) (*client.Response, error) {
	return this.QueryContext(ctx, sql, this.Database, this.RetentionPolicy)
}

func (this *InfluxClient) QueryInfluxWithDb(sql, db string) (*client.Response, error) {
	return this.QueryContext(context.Background(), sql, db, this.RetentionPolicy)
}

func (this *InfluxClient) QueryInfluxWithDbContext(ctx context.Context, sql, db string) (*client.Response, error) {
	return this.QueryContext(ctx, sql, db, this.RetentionPolicy)
}

func (this *InfluxClient) QueryContext(ctx context.Context, sql, db, retentionPolicy string) (*client.Response, error) {
	rsp, err := this.Client.QueryContext(ctx,
		client.Query{
			Command:         sql,
			Database:        db,
			RetentionPolicy: retentionPolicy,
		})
	if err != nil {
		stdlog.WithError(err).Errorf("query from influx db failed using : \"%s\"", sql)
		return nil, err
	}
	return rsp, nil
}

// Query 返回使用"单条"查询语句所返回的"单个系列"的数据(没有Group by操作)
// 若查询结构为空,则返回 nil, nil
func (this *InfluxClient) Query(req *QueryInfluxReq, rps ...ResultProcessor) (*QueryResult, error) {
	res, err := this.query(req.Sql(), this.Database)
	if err != nil {
		return nil, err
	}
	for _, rp := range rps {
		rp(res)
	}
	return res, err
}

// Count 用于对于时序数据库中的某个字段进行计数
func (this *InfluxClient) Count(req *InfluxCountReq) (*InfluxCountRsp, error) {
	rsp, err := this.QueryInflux(req.InfluxQL())
	if err != nil {
		return nil, stderr.Database.Cause(err, "failed to do count with influxql '%s'", req.InfluxQL())
	}
	if err := rsp.Error(); err != nil {
		return nil, err
	}
	if len(rsp.Results) != 1 {
		return nil, stderr.Database.Error("failed to count influx db, unexpected results length : %d", len(rsp.Results))
	}

	crs := &InfluxCountRsp{}
	for _, s := range rsp.Results[0].Series {
		cr := InfluxCountSeries{
			Counts: make([]int64, len(s.Values)),
			Times:  make([]int64, len(s.Values)),
			Tags:   s.Tags,
		}
		if len(s.Values) == 0 || len(s.Values[0]) != 2 {
			return nil, stderr.Database.Error("failed to parse count result '%+v'", s)
		}
		for i := 0; i < len(s.Values); i++ {
			cr.Times[i], _ = s.Values[i][0].(json.Number).Int64()
			cr.Counts[i], _ = s.Values[i][1].(json.Number).Int64()
		}
		crs.Series = append(crs.Series, cr)
	}
	return crs, nil
}

// query 使用给定的sql进行influx db查询, 并自动进行拆包
// 注: 该接口仅返回 response.Results[0].Serious[0], 也既
//  * 若sql中包含多条sql语句,则将仅返回第一条sql语句对应的结果
//  * 若第一条语句对结果进行了group by, 则仅返回首个serious
func (this *InfluxClient) query(sql string, db string) (*QueryResult, error) {
	rsp, err := this.QueryInfluxWithDb(sql, db)
	if err == nil {
		err = rsp.Err
	}
	if err != nil {
		return nil, stderr.Database.Cause(err, "error while querying influx database with sql : %+s", sql)
	}
	if len(rsp.Results) == 0 {
		return nil, nil
	}
	series := rsp.Results[0].Series
	if len(series) == 0 {
		return nil, nil
	}
	return &series[0], nil
}

func (this *InfluxClient) CreateDB() error {
	rsp, err := this.Client.Query(client.Query{
		Command: fmt.Sprintf(`CREATE DATABASE "%s"`, this.Database),
	})
	if err != nil {
		return err
	}
	if rsp.Err != nil {
		return rsp.Err
	}
	return nil

}
func (this *InfluxClient) CreateDBWithName(dbName string) error {
	rsp, err := this.Client.Query(client.Query{
		Command: fmt.Sprintf(`CREATE DATABASE "%s"`, dbName),
	})
	if err != nil {
		return err
	}
	if rsp.Err != nil {
		return rsp.Err
	}
	return nil
}
func (this *InfluxClient) CreateRp(rp string) error {
	rsp, err := this.Client.Query(client.Query{
		Command:  fmt.Sprintf(`CREATE RETENTION POLICY "%s" ON "%s" DURATION %s REPLICATION 1 DEFAULT`, rp, this.Database, rp),
		Database: this.Database,
	})
	if err != nil {
		stdlog.WithError(err).Errorf("fail to create rp: \"%s\"", rp)
		return err
	}
	if rsp.Err != nil {
		stdlog.WithError(rsp.Err).Errorf("fail to create rp: \"%s\"", rp)
		return rsp.Err
	}
	return nil

}

// ListMeasurements 查询influx db的默认数据库的表信息
// 若db为空, 则使用默认数据库
func (this *InfluxClient) ListMeasurements(db string) ([]string, error) {
	if db == "" {
		db = this.Database
	}
	res, err := this.query("SHOW MEASUREMENTS", db)
	if err != nil {
		return nil, stderr.Database.Cause(err, "failed to show measurements")
	}
	if res == nil {
		return nil, nil
	}
	measurements := make([]string, 0, len(res.Values))
	for _, value := range res.Values {
		if len(value) != 1 {
			stdlog.Warnf("unexpected influx measurement format: '%+v'", value)
			continue
		}
		measurements = append(measurements, value[0].(string))
	}
	return measurements, nil
}

type MeasurementKey struct {
	ID       string        `json:"id"`        // key的ID
	DataType string        `json:"data_type"` // key的数据类型
	KeyType  InfluxKeyType `json:"key_type"`  // key的类型
}

// ListKeys 查询influx db的指定数据库的表信息
func (this *InfluxClient) ListKeys(db, measurement string, kt InfluxKeyType) ([]MeasurementKey, error) {
	if db == "" {
		db = this.Database
	}
	sql := fmt.Sprintf(`SHOW %s KEYS FROM "%s"`, kt, measurement)
	res, err := this.query(sql, db)
	if err != nil {
		return nil, stderr.Database.Cause(err, "failed to show keys")
	}
	if res == nil {
		return nil, nil
	}
	keys := make([]MeasurementKey, 0, len(res.Values))
	switch kt {
	case InfluxKeyField:
		if len(res.Columns) != 2 {
			return nil, stderr.Internal.Error("unexpected influx result of field keys: '%+v'", res.Columns)
		}
		for _, value := range res.Values {
			keys = append(keys, MeasurementKey{
				ID:       value[0].(string),
				DataType: value[1].(string),
				KeyType:  kt,
			})
		}
	case InfluxKeyTag:
		if len(res.Columns) != 1 {
			return nil, stderr.Internal.Error("unexpected influx result of tag keys: '%+v'", res.Columns)
		}
		for _, value := range res.Values {
			keys = append(keys, MeasurementKey{
				ID:       value[0].(string),
				DataType: TagDataType,
				KeyType:  kt,
			})
		}
	default:
		return nil, stderr.Unsupported.Error("unsupported influx key type '%s'", kt)
	}

	return keys, nil
}

// 查询influxdb的元信息，eg: show measurements, show tags等
func (this *InfluxClient) QuerySchema(cmd string) ([]string, error) {
	res := make([]string, 0)
	rsp, err := this.Client.Query(client.Query{
		Command:         cmd,
		Database:        this.Database,
		RetentionPolicy: this.RetentionPolicy,
	})
	if err != nil {
		stdlog.WithError(err).Errorf("excute [%s] failed", cmd)
		return nil, err
	}
	if rsp.Err != nil {
		stdlog.WithError(rsp.Err).Errorf("execute [%s] failed", cmd)
		return nil, rsp.Err
	}
	if len(rsp.Results) == 0 {
		return nil, nil
	}
	if len(rsp.Results[0].Series) == 0 {
		return nil, nil
	}
	for _, val := range rsp.Results[0].Series[0].Values {
		res = append(res, fmt.Sprintf("%s", val))
	}
	return res, nil
}
