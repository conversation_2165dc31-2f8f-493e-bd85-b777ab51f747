package influxdb

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

type InfluxKeyTypeSuffix = string

const (
	TagSuffix           InfluxKeyTypeSuffix = "::tag"
	FieldSuffix         InfluxKeyTypeSuffix = "::field"
	TimeColumn                              = "time"
	TimeColumnWithQuote                     = "\"time\"" + FieldSuffix
)

type QueryInfluxReq struct {
	Measurement   string
	Filter        *InfluxFilter
	TagFilter     *TagFilter     // TagID : []ExpectValues
	TagLikeFilter *TagLikeFilter // 模糊查询
	Timestamp     int64          // used to get specific record
	Group         *groupBy       // 数据聚合配置

	selectFields []string // 需要查询的Field Keys
	selectTags   []string // 需要查询的Tag Keys
}

func NewQueryInfluxReq(measurement string) *QueryInfluxReq {
	return &QueryInfluxReq{
		Measurement:  measurement,
		selectFields: make([]string, 0),
	}
}

func (r *QueryInfluxReq) AddFields(fields ...string) {
	for _, field := range fields {
		r.selectFields = append(r.selectFields, strconv.Quote(strings.TrimSuffix(field, FieldSuffix))+FieldSuffix)
	}
}

func (r *QueryInfluxReq) AddTags(tags ...string) {
	for _, tag := range tags {
		r.selectTags = append(r.selectTags, strconv.Quote(strings.TrimSuffix(tag, TagSuffix))+TagSuffix)
	}
}

func (r *QueryInfluxReq) AddTagFilter(tag string, expects ...string) {
	if r.TagFilter == nil {
		r.TagFilter = &TagFilter{}
	}
	r.TagFilter.AddFilter(tag, expects...)
}

func (r *QueryInfluxReq) Sql() string {
	conditions := make([]string, 0)
	if r.TagFilter != nil {
		if condition := r.TagFilter.Condition(); condition != "" {
			conditions = append(conditions, condition)
		}
	}
	if r.TagLikeFilter != nil {
		if condition := r.TagLikeFilter.Condition(); condition != "" {
			conditions = append(conditions, condition)
		}
	}
	if r.Filter == nil {
		r.Filter = &InfluxFilter{}
	}
	if r.Filter.Duration != 0 {
		conditions = append(conditions, fmt.Sprintf(`time >= now() - %s`, r.Filter.Duration.String()))
	}
	if r.Filter.BeginTimeMills != 0 {
		conditions = append(conditions, fmt.Sprintf("time >= %d", int64(time.Millisecond)*r.Filter.BeginTimeMills))
	}
	if r.Filter.EndTimeMills != 0 {
		conditions = append(conditions, fmt.Sprintf("time <= %d", int64(time.Millisecond)*r.Filter.EndTimeMills))
	}
	if r.Timestamp != 0 {
		conditions = append(conditions, fmt.Sprintf("time = %d", r.Timestamp))
	}

	// build selectFields
	fields := ""
	if r.Group == nil {
		if len(r.selectFields) == 0 {
			fields = "*"
		} else {
			fields = strings.Join(append(r.selectFields, r.selectTags...), ",")
		}
	} else {
		fs := make([]string, len(r.selectFields))
		for idx, field := range r.selectFields {
			if field == TimeColumn || field == TimeColumnWithQuote {
				// 无需对时间戳字段添加聚合函数
				fs[idx] = field
				continue
			}
			fs[idx] = fmt.Sprintf(`%s(%s) AS %s`, r.Group.function, field, strings.TrimSuffix(field, FieldSuffix))
		}
		fields = strings.Join(fs, ",")
	}
	sql := fmt.Sprintf(`SELECT %s FROM "%s" `, fields, r.Measurement)
	if len(conditions) != 0 {
		sql += fmt.Sprintf(`WHERE %s `, strings.Join(conditions, " AND "))
	}

	sql += r.Group.statement()
	sql += ` ORDER BY time DESC `
	if r.Filter.Size > 0 {
		sql += fmt.Sprintf("LIMIT %d ", r.Filter.Size)
		// The OFFSET clause requires a LIMIT clause.
		if r.Filter.Offset > 0 {
			sql += fmt.Sprintf("OFFSET %d ", r.Filter.Offset)
		}
	}
	return sql
}

// DisplayDataTable 为前端展示图表所用的统一数据结构
// 目前支持：
//   - 设备属性数据
//   - 设备事件数据
//   - 设备调用数据
//   - 仪表盘/时序实例数据
//   - 容器运行时的统计信息
//   - 实例相关指标的数据
type DisplayDataTable struct {
	Id      string
	Name    string
	Type    string
	Columns []string
	Items   []*TableRow
	Groups  []*TableCols
}

func NewDisplayDataTable(id, name string, groups ...string) *DisplayDataTable {
	res := &DisplayDataTable{
		Id:     id,
		Name:   name,
		Type:   id,
		Groups: make([]*TableCols, len(groups)),
	}
	for i, group := range groups {
		res.Groups[i] = &TableCols{
			Name:    group,
			Columns: make(map[string]*TableCol),
		}
	}
	return res
}

type TableCol struct {
	Id    string // 列ID
	Name  string // 列名称
	Index int    // 列序号
}

type TableCols struct {
	Name    string
	Columns map[string]*TableCol
}

// TableRow 表示表数据中的一行,与Table中的Columns对应
type TableRow struct {
	ID     string
	Name   string
	Time   int64 // ms
	Values []interface{}
}

// RealTimeRow 表示一条新产生的数据,Values中包含列信息
type RealTimeRow struct {
	ID     string
	Name   string
	Time   int64                  // ms
	Values map[string]interface{} // col id : value
}
