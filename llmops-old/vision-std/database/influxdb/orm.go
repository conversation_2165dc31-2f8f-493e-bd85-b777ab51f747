package influxdb

import (
	"reflect"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	// TsOrmConfTagKey 字段中关于TS的配置的TagKey， 可以用来指定将数据库查询结果映射会对应的Struct时，字段的对应关系
	// e.g. TsID string `ts:"column:time"` 表示将数据库的 time 列的值映射到结构体的 TsID 字段
	TsOrmConfTagKey = "ts"
	TsOrmTagKVSep   = ":" // ts tag 中属性键与值之间的分隔符
	TsOrmTagKVsSep  = ";" // ts tag 中多个属性键值之间的分隔符

	TsOrmKeyColumn  = "column" // 指定结构体字段对应的数据库列名的Key
	TsOrmKeyKeyType = "key"    // 指定结构体字段对应的数据库列类型， 分为 Tag 和 Field

	ignoredColumn = "-" // 进行数据库字段映射时忽略该字段
)

type TsOrmConfig struct {
	t      reflect.Type
	fields map[string]int // field column name -> field index
}

func NewTsOrmConfig(t reflect.Type) TsOrmConfig {
	// 通过反射获取结构的所有的字段及其Tag
	fields := make(map[string]int, t.NumField())
	for i := 0; i < t.NumField(); i++ {
		tv := TsOrmConfTagValue(t.Field(i).Tag.Get(TsOrmConfTagKey))
		name := tv.ColumnName()
		if name == "" || name == ignoredColumn {
			continue
		}
		fields[name] = i
	}
	return TsOrmConfig{
		t:      t,
		fields: fields,
	}
}

// TsOrmConfTagValue 为结构体字段时序数据库相关的配置内容
type TsOrmConfTagValue string

func (t TsOrmConfTagValue) ColumnName() string {
	kvs := strings.Split(string(t), TsOrmTagKVsSep)
	if len(kvs) == 0 {
		return ""
	}
	for _, kv := range kvs {
		parts := strings.Split(kv, TsOrmTagKVSep)
		if len(parts) != 2 {
			continue
		}
		if parts[0] != TsOrmKeyColumn {
			continue
		}
		return parts[1]
	}
	return ""
}

// Scan 将某个InfluxDB请求结果的Series转换为结构体并添加到给定的切片
// values 必须为 切片的指针
// values 中的元素类型也必须为指针类型
func Scan(r *QueryResult, values interface{}) error {
	if r == nil || r.Values == nil {
		return nil
	}
	valuesType := reflect.TypeOf(values) // *[]*struct
	if valuesType.Kind() != reflect.Ptr ||
		valuesType.Elem().Kind() != reflect.Slice ||
		valuesType.Elem().Elem().Kind() != reflect.Ptr {
		return stderr.ResourceQueryFailure.Error("the receiver of influx query result must be a slice ptr of point ptr")
	}
	valueSlice := reflect.ValueOf(values).Elem()
	valueType := valuesType.Elem().Elem().Elem()
	cfg := NewTsOrmConfig(valueType)
	ci2fis := make([]int, len(r.Columns)) // column index -> filed index
	for ci, col := range r.Columns {
		fi, ok := cfg.fields[col]
		if !ok {
			//return stderr.Internal.Error("field not found which corresponding to influx db column '%s'", col)
			fi = -1
		}
		ci2fis[ci] = fi
	}

	for _, row := range r.Values {
		newV := reflect.New(valueType)

		// set filed keys fields
		for ci := range row {
			if row[ci] == nil || ci2fis[ci] < 0 {
				continue
			}
			field := newV.Elem().Field(ci2fis[ci])
			value := reflect.ValueOf(row[ci])
			if !value.Type().ConvertibleTo(field.Type()) {
				return stderr.Internal.Error("can not set '%s' value to '%s' field", value.Type().String(), field.Type().String())
			}
			field.Set(value.Convert(field.Type()))
		}

		// set tag keys fields
		for tagK, tagV := range r.Tags {
			if fi, ok := cfg.fields[tagK]; ok {
				field := newV.Elem().Field(fi)
				value := reflect.ValueOf(tagV)
				if !value.Type().ConvertibleTo(field.Type()) {
					return stderr.Internal.Error("can not set '%s' value to '%s' field", value.Type().String(), field.Type().String())
				}
				field.Set(value.Convert(field.Type()))
			}
		}
		valueSlice.Set(reflect.Append(valueSlice, newV))
	}
	return nil
}
