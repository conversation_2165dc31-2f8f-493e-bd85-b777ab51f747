package influxdb

import (
	"time"

	"github.com/influxdata/influxdb/models"
)

type TsDataWriter interface {
	Measurement() string
	Tags() map[string]string
	Fields() map[string]interface{}
}

type TsDataWithTimeWriter interface {
	TsDataWriter
	// Time returns the timestamp of this TsData
	Time() time.Time
}

type QueryResult = models.Row
type InfluxKeyType string
type PredefinedColName string // 数据库预定义的一些自动名称

const (
	InfluxKeyTag   InfluxKeyType = "TAG"   // 标签信息,存储在索引中,可用来进行筛选与分组
	InfluxKeyField InfluxKeyType = "FIELD" // 字段信息, 无索引, 进可用来进行筛选, 且筛选速度较慢

	InfluxMeasurementNameCol = "name"      // 表名称
	InfluxTagKeyCol          = "fieldKey"  // Tag名称
	InfluxFieldKeyCol        = "fieldKey"  // Field名称
	InfluxFieldTypeCol       = "fieldType" // Field字段数据类型

	TagDataType = "string" // influx db tag key 的固定数据格式
)

// InfluxFilter 为一个通用的, InfluxDB查询筛选条件
type InfluxFilter struct {
	Size           int64         `json:"size"`
	Offset         int64         `json:"offset"`
	EndTimeMills   int64         `json:"end_time_mills"`
	BeginTimeMills int64         `json:"begin_time_mills"`
	Duration       time.Duration `json:"duration"`
}
