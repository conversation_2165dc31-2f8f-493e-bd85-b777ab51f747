package influxdb

import (
	"testing"
	"time"
)

func TestQueryInfluxReq_Sql(t *testing.T) {
	type fields struct {
		Measurement  string
		Filter       *InfluxFilter
		TagFilter    *TagFilter
		Timestamp    int64
		Group        *groupBy
		selectFields []string
		selectTags   []string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "",
			fields: fields{
				Measurement: "test",
				Filter: &InfluxFilter{
					Size:           100,
					EndTimeMills:   999,
					BeginTimeMills: 111,
					Duration:       time.Minute,
				},
				TagFilter: &TagFilter{
					"tag1": {"v1", "v2"},
					"tag2": {"v3"},
				},
				Timestamp: 0,
				Group: &groupBy{
					tags:         nil,
					timeInterval: time.Minute * 5,
					function:     MEAN,
					fillOption:   "null",
				},
				selectFields: []string{"field1", "field2"},
				selectTags:   []string{"tag1", "tag2"},
			},
			want: `SELECT MEAN(field1) AS field1,MEAN(field2) AS field2 FROM "test" WHERE ("tag1" = 'v1' OR "tag1" = 'v2') AND ("tag2" = 'v3') AND time >= now() - 1m0s AND time >= 111000000 AND time <= 999000000 GROUP BY time(5m0s) fill(null)  ORDER BY time DESC LIMIT 100 `,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &QueryInfluxReq{
				Measurement:  tt.fields.Measurement,
				Filter:       tt.fields.Filter,
				TagFilter:    tt.fields.TagFilter,
				Timestamp:    tt.fields.Timestamp,
				Group:        tt.fields.Group,
				selectFields: tt.fields.selectFields,
				selectTags:   tt.fields.selectTags,
			}
			if got := r.Sql(); got != tt.want {
				t.Errorf("Sql() = %v, want %v", got, tt.want)
			}
		})
	}
}
