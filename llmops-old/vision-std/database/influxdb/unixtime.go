package influxdb

import (
	"time"
)

// ParseUnixTimestamp 通过判断时间戳的长度推测其单位, 并解析为time.Time 格式
func ParseUnixTimestamp(timestamp int64) (time.Time, bool) {
	switch {
	case timestamp >= 1e18:
		return time.Unix(0, timestamp), true
	case timestamp >= 1e15:
		return time.UnixMicro(timestamp), true
	case timestamp >= 1e12:
		return time.UnixMilli(timestamp), true
	case timestamp >= 1e9:
		return time.Unix(timestamp, 0), true
	default:
		return time.Time{}, false
	}
}
