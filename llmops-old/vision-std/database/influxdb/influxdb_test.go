package influxdb

import (
	"testing"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

func TestInfluxClient_QueryInflux(t *testing.T) {
	cli, err := NewInfluxClient(conf.InfluxdbConfig{
		URL:       "http://172.16.11.155:8086",
		UserAgent: "test",
		Database:  "thinger",
	})
	if err != nil {
		t.<PERSON>(err)
	}

	cli.QueryInflux("select * from RESULT where VehicleNo='陕AL7L36'")
}

func TestQuerySchema(t *testing.T) {
	cli, err := NewInfluxClient(conf.InfluxdbConfig{
		URL:       "http://172.16.11.155:8086",
		UserAgent: "test",
		Database:  "thinger",
	})
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	res, err := cli.QuerySchema("SHOW MEASUREMENTS")
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.<PERSON>g(res)
}
