package influxdb

import "testing"

func TestTagFilter_Condition(t *testing.T) {
	type fields struct {
		filters map[string][]string
	}
	tests := []struct {
		name    string
		filters TagFilter
		want    string
	}{
		{
			name:    "test empty filter",
			filters: nil,
			want:    "",
		},
		{
			name: "test single tag filter",
			filters: TagFilter{
				"tag1": {"v1", "v2"},
			},
			want: `("tag1" = 'v1' OR "tag1" = 'v2')`,
		},
		{
			name: "test multi tag filter",
			filters: TagFilter{
				"tag1": {"v1", "v2"},
				"tag2": {"v1"},
				"tag3": {"v1"},
			},
			want: `("tag1" = 'v1' OR "tag1" = 'v2') AND ("tag2" = 'v1') AND ("tag3" = 'v1')`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tf := tt.filters
			if got := tf.Condition(); got != tt.want {
				t.<PERSON><PERSON><PERSON>("TagFilter.Condition() = %v, want %v", got, tt.want)
			}
		})
	}
}
