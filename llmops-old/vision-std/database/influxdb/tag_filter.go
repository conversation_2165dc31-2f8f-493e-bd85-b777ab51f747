package influxdb

import (
	"fmt"
	"sort"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// TagFilter 为Influx查询时的Tag过滤条件
// 每个Tag对应的可能值可以有多个, 之间的关系为OR
// 多个Tag之间的关系为AND
type TagFilter map[string][]string

func (t TagFilter) AddFilter(tag string, excepts ...string) {
	if t == nil {
		stdlog.Errorf("failed to do add tag filter, cause it is nil")
		return
	}
	t[tag] = append(t[tag], excepts...)
}

// Condition 将依据filters 返回对应的 WHERE condition
func (t TagFilter) Condition() string {
	if len(t) == 0 {
		return ""
	}
	conditions := make([]string, 0)
	for tag, vs := range t {
		cs := make([]string, len(vs))
		for idx, v := range vs {
			cs[idx] = fmt.Sprintf(`"%s" = '%s'`, tag, v)
		}
		conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(cs, ` OR `)))
	}
	sort.Strings(conditions)
	return strings.Join(conditions, " AND ")
}

// TagLikeFilter 为Influx查询时的Tag过滤条件
// 每个Tag对应的可能值可以有多个, 之间的关系为OR
// 多个Tag之间的关系为AND
type TagLikeFilter map[string][]string

func (t TagLikeFilter) AddFilter(tag string, excepts ...string) {
	if t == nil {
		stdlog.Errorf("failed to do add tag filter, cause it is nil")
		return
	}
	t[tag] = append(t[tag], excepts...)
}

// Condition 将依据filters 返回对应的 WHERE condition
func (t TagLikeFilter) Condition() string {
	if len(t) == 0 {
		return ""
	}
	conditions := make([]string, 0)
	for tag, vs := range t {
		cs := make([]string, len(vs))
		for idx, v := range vs {
			cs[idx] = fmt.Sprintf(`"%s" =~ /%s/`, tag, v)
		}
		conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(cs, ` OR `)))
	}
	sort.Strings(conditions)
	return strings.Join(conditions, " AND ")
}
