package influxdb

import (
	"github.com/influxdata/influxdb/client"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	MaxBatchSize  = 10000 // 单次写入数据库点数上限
	MaxConcurrent = 100   // 客户端数量(并发数量)上限
)

// Point 为时序数据库一个数据点
type Point = client.Point

// BatchPoint 为多个数据点的组合
type BatchPoint = client.BatchPoints

// InfluxWriterConfig 数据库写入组件的配置
type InfluxWriterConfig struct {
	Cfg           conf.InfluxdbConfig // 数据库基本配置
	MaxBatch      int                 // 单次写入最大数据点数
	MaxConcurrent int                 // 最大客户端并发数量
}

// 写入数据失败时的错误处理函数
type WriteErrorHandler = func(err error)

// defaultErrorHandler 默认的错误处理函数
func defaultErrorHandler(err error) {
	stdlog.WithError(err).Errorf("failed to write point into InfluxDB")
}

// InfluxDB 的写入接口
type InfluxWriter interface {
	Write(p Point)
	Stop()
}

// writer为InfluxWriter的一种高性能实现
// 通过初始化多个数据库客户端,将单点写入请求平均分发到每个客户端;
// 并且通过 channel 对点进行缓存, 每次客户端空闲时,从 channel中读取多个数据点进行批量写入;
type writer struct {
	idx        int           // 用于对写入的数据点进行分流
	chs        []chan Point  // 各客户端缓存数据点的channel
	stop       chan struct{} // 用于关闭所有写入协程
	errHandler WriteErrorHandler
	InfluxWriterConfig
}

// NewInfluxWriter 依据给定配置初始化一个新的InfluxDB的Writer
// 可用于并发的, 批量的, 高性能的进行写入操作;
func NewInfluxWriter(cfg InfluxWriterConfig, handler WriteErrorHandler) (InfluxWriter, error) {
	// config check
	if cfg.Cfg.Database == "" {
		return nil, stderr.InvalidParam.Error("database must be specified")
	}
	if cfg.Cfg.URL == "" {
		return nil, stderr.InvalidParam.Error("url must be specified")
	}
	if cfg.MaxBatch > MaxBatchSize {
		return nil, stderr.InvalidParam.Error("MaxBatch must less than %d", MaxBatchSize)
	}
	if cfg.MaxConcurrent > MaxConcurrent {
		return nil, stderr.InvalidParam.Error("MaxConcurrent must less than %d", MaxConcurrent)
	}

	// set default config
	if cfg.MaxBatch <= 0 {
		cfg.MaxBatch = 100
	}
	if cfg.MaxConcurrent <= 0 {
		cfg.MaxConcurrent = 1
	}
	if handler == nil {
		handler = defaultErrorHandler
	}

	// init influx writer
	w := &writer{
		chs:                make([]chan Point, cfg.MaxConcurrent),
		stop:               make(chan struct{}),
		errHandler:         handler,
		InfluxWriterConfig: cfg,
	}

	// init all influx clients
	clients := make([]*InfluxClient, cfg.MaxConcurrent)
	for i := 0; i < cfg.MaxConcurrent; i++ {
		cli, err := NewInfluxClient(cfg.Cfg)
		if err != nil {
			return nil, stderr.Internal.Cause(err,"failed to create new influx client with config %+v", cfg.Cfg)
		}
		_, _, err = cli.Client.Ping()
		if err != nil {
			return nil, stderr.Database.Cause(err,"failed to do ping operation with config %+v", cfg.Cfg)
		}
		clients[i] = cli
	}

	// new a listening goroutine for each influx db client
	for idx, cli := range clients {
		w.chs[idx] = make(chan Point, cfg.MaxBatch*2)
		go w.listen(cli, w.chs[idx])
	}

	return w, nil
}

func (w *writer) Stop() {
	close(w.stop)
}

// Write 用于向指定的数据库中写入一条数据记录;
// 当存在空闲的Client时, 该点会立即被写入;
// 当无空闲的Client时, 该点会被放入一个Client的channel缓存中, 等待下次批量写入;
func (w *writer) Write(p Point) {
	w.idx = (w.idx + 1) % w.MaxConcurrent
	w.chs[w.idx] <- p
}

// listen 实现了通过channel的方式监听数据写入请求
// 接收到数据点(Point)后, 若该Client为空闲状态,则将最大限度读取未写入的Point列表,并批量进行插入;
// 当该Client正在等待数据写入请求返回时, 将会暂停处理后续接收到的Point, 直到上次请求已完成或失败;
func (w *writer) listen(cli *InfluxClient, ch <-chan Point) {
	for {
		select {
		case <-w.stop: // writer has been stopped
			return
		case point, ok := <-ch:
			// 数据channel是否已关闭
			if !ok {
				stdlog.Warnf("the channel receiving points has been closed")
				return
			}
			// channel中未读元素数量
			size := len(ch) + 1
			if w.MaxBatch != 0 && size > w.MaxBatch {
				size = w.MaxBatch
			}

			// 取出所有或最大数量的未处理Point
			points := make([]Point, size)
			points[0] = point // first Point
			for i := 1; i < size; i++ {
				points[i] = <-ch
			}

			// 写入该批数据点
			bp := BatchPoint{
				Points:   points,
				Database: w.Cfg.Database,
			}

			// handle error
			if err := cli.BatchWrite(bp); err != nil {
				w.errHandler(err)
				continue
			}
		}
	}
}
