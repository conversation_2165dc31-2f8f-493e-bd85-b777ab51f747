package mysql

import (
	"fmt"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	gorm2mysql "gorm.io/driver/mysql"
	gorm2 "gorm.io/gorm"
	"gorm.io/gorm/logger"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/database"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// CreateDB 初始化MYSQL实例
func CreateDB(config conf.MysqlConfig) *gorm.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = config.Port
		dbName   = config.DBName
		maxIdle  = config.MaxIdle
		maxOpen  = config.MaxConn
	)
	// connStr := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8&parseTime=True&loc=Local",
	//	username,
	//	password,
	//	host,
	//	port,
	//	dbName,
	// )
	connStr := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True",
		username,
		password,
		host,
		port,
		"",
	)
	// 由于go连接数据库是惰性连接，在具体操作数据库的时候才会创建连接，Open函数仅初始化了连接池。
	// 为了检查数据库是否存在（不存在创建）。故这里会调用两次Open函数
	stdlog.Infoln("Try to connect to MYSQL host: ", host, ", port: ", port)
	db, err := gorm.Open("mysql", connStr)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect MYSQL %s:%s", host, port)
		panic(err)
	}
	stdlog.Infoln("Connected to MYSQL: ", host, ", port: ", port)
	db = db.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARSET utf8", dbName))
	if db.Error != nil {
		stdlog.WithError(db.Error).Errorf("failed to create database %s", dbName)
		panic(db.Error)
	}
	if err := db.Close(); err != nil {
		stdlog.WithError(err).Errorf("faild to close db")
		panic(err)
	}
	connStr = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True",
		username,
		password,
		host,
		port,
		dbName,
	)
	db, err = gorm.Open("mysql", connStr)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect MYSQL %s:%d/%s", host, port, dbName)
		panic(err)
	}
	db = db.Set("gorm:table_options", "ENGINE=InnoDB CHARSET=utf8")

	if !config.NotPrintSql {
		db.LogMode(true)
	}

	db.SetLogger(database.Logger{})

	db.DB().SetMaxIdleConns(maxIdle)
	db.DB().SetMaxOpenConns(maxOpen)

	return db
}

func CreateGorm2DB(config conf.MysqlConfig) *gorm2.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = config.Port
		dbName   = config.DBName
		maxIdle  = config.MaxIdle
		maxOpen  = config.MaxConn
	)
	connStr := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username,
		password,
		host,
		port,
		"",
	)
	stdlog.Infoln("Try to connect to MYSQL host: ", host, ", port: ", port)

	db, err := gorm2.Open(gorm2mysql.Open(connStr), &gorm2.Config{})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect MYSQL %s:%s", host, port)
		panic(err)
	}
	stdlog.Infoln("Connected to MYSQL: ", host, ", port: ", port)
	db = db.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARSET utf8mb4", dbName))
	if db.Error != nil {
		stdlog.WithError(db.Error).Errorf("failed to create database %s", dbName)
		panic(db.Error)
	}
	connStr = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username,
		password,
		host,
		port,
		dbName,
	)
	db, err = gorm2.Open(gorm2mysql.Open(connStr), &gorm2.Config{})
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect MYSQL %s:%s/%s", host, port, dbName)
		panic(err)
	}
	if !config.NotPrintSql {
		db.Logger = db.Logger.LogMode(logger.Info)
	}

	sqlDB, err := db.DB()
	if err != nil {
		stdlog.WithError(err).Errorf("failed to get SQL DB")
		panic(err)
	}
	sqlDB.SetMaxIdleConns(maxIdle)
	sqlDB.SetMaxOpenConns(maxOpen)

	return db
}
