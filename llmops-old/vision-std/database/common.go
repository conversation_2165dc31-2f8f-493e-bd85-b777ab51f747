package database

import (
	"database/sql/driver"
	"fmt"
	"reflect"
	"regexp"
	"time"
	"unicode"

	"github.com/jinzhu/gorm"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// TimeMixin mixin
/*type TimeMixin struct {
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP(6);default:CURRENT_TIMESTAMP(6);index"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:TIMESTAMP(6);default:CURRENT_TIMESTAMP(6);index"`
	DeletedAt *time.Time `gorm:"column:deleted_at;type:TIMESTAMP(6) NULL;index"`
}
*/
type TimeMixin struct {
	CreatedAt time.Time  `deep:"-"`
	UpdatedAt time.Time  `deep:"-"`
	DeletedAt *time.Time `sql:"index" deep:"-"`
}

var (
	sqlRegexp = regexp.MustCompile(`(\$\d+)|\?`)
)

// <PERSON><PERSON> customizes the gorm logger.
type Logger struct {
}

// Print uses logrus to log SQL.
func (Logger Logger) Print(values ...interface{}) {
	if len(values) > 1 {
		level := values[0]
		currentTime := "[" + gorm.NowFunc().Format("2006-01-02 15:04:05") + "]"
		source := fmt.Sprintf("(%v)", values[1])
		messages := []interface{}{source, currentTime}

		if level == "sql" {
			// duration
			messages = append(messages, fmt.Sprintf(" [%.2fms] ", float64(values[2].(time.Duration).Nanoseconds()/1e4)/100.0))
			// sql
			var sql string
			var formattedValues []string

			for _, value := range values[4].([]interface{}) {
				indirectValue := reflect.Indirect(reflect.ValueOf(value))
				if indirectValue.IsValid() {
					value = indirectValue.Interface()
					if t, ok := value.(time.Time); ok {
						formattedValues = append(formattedValues, fmt.Sprintf("'%v'", t.Format(time.RFC3339)))
					} else if b, ok := value.([]byte); ok {
						if str := string(b); isPrintable(str) {
							formattedValues = append(formattedValues, fmt.Sprintf("'%v'", str))
						} else {
							formattedValues = append(formattedValues, "'<binary>'")
						}
					} else if r, ok := value.(driver.Valuer); ok {
						if value, err := r.Value(); err == nil && value != nil {
							formattedValues = append(formattedValues, fmt.Sprintf("'%v'", value))
						} else {
							formattedValues = append(formattedValues, "NULL")
						}
					} else {
						formattedValues = append(formattedValues, fmt.Sprintf("'%v'", value))
					}
				} else {
					formattedValues = append(formattedValues, fmt.Sprintf("'%v'", value))
				}
			}

			var formattedValuesLength = len(formattedValues)
			for index, value := range sqlRegexp.Split(values[3].(string), -1) {
				sql += value
				if index < formattedValuesLength {
					sql += formattedValues[index]
				}
			}

			messages = append(messages, sql)
		} else {
			messages = append(messages, values[2:]...)
		}
		stdlog.Info(messages)
	}
}

func isPrintable(s string) bool {
	for _, r := range s {
		if !unicode.IsPrint(r) {
			return false
		}
	}
	return true
}
