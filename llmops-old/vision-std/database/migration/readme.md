# 数据库版本管理工具migration文档

### 依赖说明
该工具直接依赖gorm2版本，如果是gorm1则不可用，其他无强制依赖
### 使用文档
#### 示例：
1. 定义数据库变更
````go
package migrations

import (
    "gorm.io/gorm"
    "transwarp.io/applied-ai/aiot/vision-std/database/migration"
)

// 初始化数据库表时建议使用DDL语句执行，后续每个版本更新时可以使用gorm提供的api(https://gorm.io/zh_CN/docs/migration.html)
var (
    MysqlCreateSql = []string{
        "CREATE TABLE `users` (`id` bigint unsigned AUTO_INCREMENT,`created_at` datetime(3) NULL,`updated_at` datetime(3) NULL,`deleted_at` datetime(3) NULL,`name` varchar(100),`password` varchar(50),`confirm_password` varchar(50),`secret` varchar(255),`full_name` varchar(100),`email` varchar(50),`create_user` varchar(50),PRIMARY KEY (`id`),INDEX `idx_users_deleted_at` (`deleted_at`))",
       }
    SqliteCreateSql = []string{
        }
)


// init函数将准备执行的sql注册到migrations工具，并定义一个版本号
// 使用时需要在程序的main.go文件引入init函数所在包，否则init方法不执行会导致该版本在调用update函数时未执行，例如：_ "transwarp.io/applied-ai/central-auth-service/migrations"
func init() {
    // 注册时的版本号为int64类型，执行时将会按照版本号升序执行，版本号不允许重复（未校验）
    err := migration.RegisterUpdate(createTable, 10001)
    if err != nil {
        panic(err)
    }
}

// 自定义函数，需要入参为*gorm.DB格式，返回error，当返回的error != nil 时执行的sql会被回滚，且中断数据库更新
func createTable(tx *gorm.DB) error {
    // 自定义sql执行内容
    createTalbeSql := SqliteCreateSql
    if tx.Dialector.Name() == "mysql" {
        createTalbeSql = MysqlCreateSql
        pkSql = "ALTER TABLE `%s` AUTO_INCREMENT = 10001"
    }

    // 执行建表
    for _, createSql := range createTalbeSql {
        err := tx.Exec(createSql).Error
        if err != nil {
            return err
        }
    }
    return nil
}
````
2. 执行版本升级

````go

package main

import (
    "gorm.io/driver/mysql"
    "gorm.io/gorm"
    "transwarp.io/applied-ai/aiot/vision-std/database/migration"
)

func main() {
    //开启gorm连接
    dsn := "user:pass@tcp(127.0.0.1:3306)/dbname?charset=utf8mb4&parseTime=True&loc=Local"
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
    if err != nil {
        panic(err)
    }

    //调用更新数据库函数，传入gorm db和目标版本号，当版本号<=入参时执行更新。入参=0时更新到最新版本
    err = migration.UpdateDB(db, 0)
    if err != nil {
        panic(err)
    }
}

````
### 常见问题

