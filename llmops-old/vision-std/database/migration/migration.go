package migration

import (
    "fmt"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"
    "sort"
    "transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type (
    ColumnsName   string
    VersionStatus int
)

const (
    Id         ColumnsName = "id"
    VersionId  ColumnsName = "version_id"
    Status     ColumnsName = "status"
    CreateTime ColumnsName = "create_time"
    UpdateTime ColumnsName = "update_time"

    Success VersionStatus = 1
    Failed  VersionStatus = 2
)

var versions []UpVersion

type UpVersion struct {
    Fc      func(tx *gorm.DB) error
    Version int64
}

type DBVersion struct {
    gorm.Model
    VersionId int64 `gorm:"column:version_id"`
    // 0、未执行  1、已执行 2、执行失败
    Status int `gorm:"column:status"`
}

func UpdateDB(db *gorm.DB, version int64) error {
    session := db.Session(&gorm.Session{NewDB: true})
    err := createVersionTable(session)
    if err != nil {
        return err
    }

    upVersions := sortVersions()
    dbVersions, err := queryDBVersions(session)
    if err != nil {
        return err
    }

    var (
        currentVersion = int64(0)
        currentStatus  = false
    )
    // 根据version id 倒序查询所有version。第一条为最新版本
    for _, dbVersion := range dbVersions {
        currentVersion = dbVersion.VersionId
        currentStatus = dbVersion.Status == 1
    }

    for _, upVersion := range upVersions {
        // 当前版本已执行
        if upVersion.Version < currentVersion || upVersion.Version == currentVersion && currentStatus {
            continue
        }
        // 目标版本已执行
        if version != 0 && upVersion.Version > version {
            break
        }

        stdlog.Info(fmt.Sprintf("start to execute update version:%d", upVersion.Version))
        err := executeVersion(session, upVersion)
        if err != nil {
            return err
        }
    }

    return nil
}

// RegisterUpdate 注册数据库修改函数,version代表创建顺序，要求必须唯一,可以在单个version的func里面执行多个sql，但是不可以多个func对应同一个version，将会导致随机其中一个func不执行
func RegisterUpdate(fc func(tx *gorm.DB) error, version int64) error {
    versions = append(versions, UpVersion{Fc: fc, Version: version})
    return nil
}

func executeVersion(db *gorm.DB, version UpVersion) error {
    ndb := db.Session(&gorm.Session{NewDB: true})
    ndb.Logger.LogMode(logger.Info)
    err := ndb.Transaction(func(tx *gorm.DB) error {
        return version.Fc(tx)

    })
    if err != nil {
        _ = fail(version, ndb)
        return err
    }
    return success(version, ndb)
}

func success(version UpVersion, db *gorm.DB) error {
    stdlog.Info(fmt.Sprintf("update version %d success!", version.Version))
    return saveDBVersion(db, version.Version, Success)
}

func fail(version UpVersion, db *gorm.DB) error {
    stdlog.Info(fmt.Sprintf("update version %d failed !", version.Version))
    return saveDBVersion(db, version.Version, Failed)
}

func sortVersions() []UpVersion {
    sort.Slice(versions, func(i, j int) bool {
        return versions[i].Version < versions[j].Version
    })
    return versions
}
func createVersionTable(db *gorm.DB) error {
    return db.AutoMigrate(&DBVersion{})
}

func queryOne(version int64, db *gorm.DB) (*DBVersion, error) {
    var dbVersions []*DBVersion
    err := db.Where(&DBVersion{VersionId: version}).Find(&dbVersions).Error
    if err != nil {
        return nil, err
    }
    if len(dbVersions) > 0 {
        return dbVersions[0], nil
    }
    return nil, nil
}

func saveDBVersion(db *gorm.DB, version int64, status VersionStatus) error {
    existsDBVersion, err := queryOne(version, db)
    if err != nil {
        stdlog.Error(fmt.Sprintf("query exists db version failed,id:%d,error:%s", version, err.Error()), err)
        return err
    }
    save := DBVersion{VersionId: version, Status: int(status)}
    if existsDBVersion != nil && existsDBVersion.ID != 0 {
        save.ID = existsDBVersion.ID
        save.CreatedAt = existsDBVersion.CreatedAt
    }
    return db.Save(&save).Error
}

func queryDBVersions(db *gorm.DB) ([]*DBVersion, error) {
    var dbVersions []*DBVersion
    err := db.Model(&DBVersion{}).Order("version_id").Find(&dbVersions).Error
    if err != nil {
        return nil, err
    }
    return dbVersions, nil
}
