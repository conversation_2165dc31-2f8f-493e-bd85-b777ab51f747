package sqlite

import (
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2Sqlite "gorm.io/driver/sqlite"
	gorm2 "gorm.io/gorm"
	"gorm.io/gorm/logger"
	"os"
	"path/filepath"
	"transwarp.io/applied-ai/aiot/vision-std/database"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func CreateTestDB(logEnable bool) *gorm.DB {
	stdlog.Infoln("Try to connect to in-mem SQLite")
	db, err := gorm.Open("sqlite3", "file::memory:?cache=shared&parseTime=true")
	if err != nil {
		stdlog.WithError(err).Errorf("failed to connect SQLite")
	}
	stdlog.Info("Connected to in-mem SQLite")
	db.LogMode(logEnable)
	//db.SetLogger(Logger{})
	return db
}

func CreateFileDB(file string, timeout int) (*gorm.DB, error) {
	if err := os.MkdirAll(filepath.Dir(file), 0755); err != nil {
		return nil, err
	}

	stdlog.Info("Try to connect to SQLite in " + file)
	if db, err := gorm.Open("sqlite3", fmt.Sprintf("%s?_busy_timeout=%d", file, timeout)); err != nil {
		return nil, err
	} else {
		stdlog.Info("Connected to SQLite in " + file)
		db.LogMode(true)
		db.SetLogger(database.Logger{})
		return db, nil
	}

}

func CreateFileGorm2DB(file string, timeout int) (*gorm2.DB, error) {
	if err := os.MkdirAll(filepath.Dir(file), 0755); err != nil {
		return nil, err
	}

	stdlog.Info("Try to connect to SQLite in " + file)
	if db, err := gorm2.Open(gorm2Sqlite.Open(fmt.Sprintf("%s?_busy_timeout=%d", file, timeout)), &gorm2.Config{}); err != nil {
		return nil, err
	} else {
		stdlog.Info("Connected to SQLite in " + file)
		db.Logger.LogMode(logger.Info)
		return db, nil
	}

}
