package schedule

import (
	"context"
	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

func newExecutor(id string, tp stdhub.RscType, ctx context.Context, crontab string, execute interface{}, args ...interface{}) (*Executor, error) {
	id = strings.TrimSpace(id)
	if id == "" {
		return nil, errors.New("executor's ID can't be empty")
	}
	if tp == "" {
		return nil, errors.New("executor's type can't be empty")
	}

	if crontab == "" {
		return nil, errors.New("executor's crontab hasn't been set")
	}
	when, err := toolkit.ParseCronExpr(crontab)
	if err != nil {
		return nil, err
	}

	if execute == nil {
		return nil, errors.New("executor's execute hasn't been set")
	}

	ctxWithCancel, cancel := context.WithCancel(ctx)
	return &Executor{
		id:      id,
		tp:      tp,
		ctx:     ctxWithCancel,
		cancel:  cancel,
		execute: execute,
		args:    args,
		when:    when,
	}, nil
}

func NewDataTaskExecutor(id string, ctx context.Context, crontab string, execute func() error) (*Executor, error) {
	return newExecutor(id, stdhub.RscTypeDataTask, ctx, crontab, execute)
}

func NewSceneInstanceExecutor(id string, ctx context.Context, crontab string, execute func(id string) error, instanceID string) (*Executor, error) {
	return newExecutor(id, stdhub.RscTypeSceneInstance, ctx, crontab, execute, instanceID)
}

type Executor struct {
	id      string         // executor's id
	tp      stdhub.RscType // executor's type
	ctx     context.Context
	cancel  context.CancelFunc
	execute interface{}
	args    []interface{}
	when    *cronexpr.Expression // when executor should execute
}
