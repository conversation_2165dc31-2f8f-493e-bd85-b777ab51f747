package schedule

import (
	"context"
	"go.uber.org/atomic"
	"testing"
	"time"
)

func TestScheduler(t *testing.T) {
	counter := make(map[string]*atomic.Int32)
	scdl := GetScheduler()

	e1ID := "1"
	counter[e1ID] = atomic.NewInt32(0)
	executor1, err := NewDataTaskExecutor(e1ID, context.Background(), "*/1 * * * * *", func() error {
		counter[e1ID].Add(1)
		return nil
	})
	if err != nil {
		panic(err)
	}

	e2ID := "2"
	counter[e2ID] = atomic.NewInt32(0)
	executor2, err := NewDataTaskExecutor(e2ID, context.Background(), "*/2 * * * * *", func() error {
		counter[e2ID].Add(1)
		return nil
	})
	if err != nil {
		panic(err)
	}

	e3ID := "3"
	counter[e3ID] = atomic.NewInt32(0)
	executor3, err := NewDataTaskExecutor(e3ID, context.Background(), "*/3 * * * * *", func() error {
		counter[e3ID].Add(1)
		return nil
	})
	if err != nil {
		panic(err)
	}

	_ = scdl.Register(executor1)
	_ = scdl.Register(executor2)
	_ = scdl.Register(executor3)

	duration := 10 * time.Second
	time.Sleep(duration)
	// executor1 (every 1s execute once) must execute 10 times in the interval of 10s
	if counter[e1ID].Load() != int32(duration / (1 * time.Second)) {
		t.Fatalf("failed to schedule executor1, expected %d, but got %d",
			int32(duration / (1 * time.Second)), counter[e1ID].Load())
	}
	// executor2 (every 2s execute once) must execute 5 times in the interval of 10s
	if counter[e2ID].Load() != int32(duration / (2 * time.Second)) {
		t.Fatalf("failed to schedule executor2, expected %d, but got %d",
			int32(duration / (2 * time.Second)), counter[e2ID].Load())
	}
	// executor3 (every 3s execute once) maybe execute 3 or 4 times in the interval of 10s
	if counter[e3ID].Load() != int32(duration/(3*time.Second)) &&
		counter[e3ID].Load() != 1+int32(duration/(3*time.Second)) {
		t.Fatalf("failed to schedule executor3, expected %d or %d, but got %d",
			int32(duration / (3 * time.Second)), 1+int32(duration/(3*time.Second)), counter[e3ID].Load())
	}

	duration += 5 * time.Second
	time.Sleep(5 * time.Second)
	_ = scdl.Unregister(e3ID)

	duration += 5 * time.Second
	time.Sleep(5 * time.Second)
	// executor1 (every 1s execute once) must execute 20 times in the interval of 20s
	if counter[e1ID].Load() != int32(duration / (1 * time.Second)) {
		t.Fatalf("failed to schedule executor1, expected %d, but got %d",
			int32(duration / (1 * time.Second)), counter[e1ID].Load())
	}
	// executor2 (every 2s execute once) must execute 10 times in the interval of 20s
	if counter[e2ID].Load() != int32(duration / (2 * time.Second)) {
		t.Fatalf("failed to schedule executor2, expected %d, but got %d",
			int32(duration / (2 * time.Second)), counter[e2ID].Load())
	}
	// executor3 (every 3s execute once) must execute 5 times in the interval of 15s
	if counter[e3ID].Load() != int32((duration-5*time.Second)/(3*time.Second)) {
		t.Fatalf("failed to schedule executor3, expected %d, but got %d",
			int32((duration - 5*time.Second) / (3 * time.Second)), counter[e3ID].Load())
	}
}
