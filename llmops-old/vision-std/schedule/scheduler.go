package schedule

import (
	"context"
	"fmt"
	"github.com/juju/errors"
	"reflect"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

var (
	once sync.Once
	s    *Scheduler
)

type Scheduler struct {
	em        sync.Mutex // protect executors from concurrency
	executors map[string]*Executor
}

func GetScheduler() *Scheduler {
	once.Do(func() {
		s = &Scheduler{
			executors: make(map[string]*Executor),
			em:        sync.Mutex{},
		}
	})
	return s
}

func (s *Scheduler) Register(executor *Executor) error {
	s.em.Lock()
	defer s.em.Unlock()
	if _, ok := s.executors[executor.id]; ok {
		return errors.New(fmt.Sprintf("executor identified by %s has registered", executor.id))
	}
	s.executors[executor.id] = executor

	go func() {
		timer := time.NewTimer(1 * time.Hour)
		for {
			now := time.Now()
			timer.Reset(executor.when.Next(now).Sub(now))
			select {
			case <-timer.C:
				var err error
				switch executor.tp {
				case stdhub.RscTypeDataTask:
					err = executor.execute.(func() error)()
				case stdhub.RscTypeSceneInstance:
					err = executor.execute.(func(id string) error)(executor.args[0].(string))
				default:
					err = errors.New(fmt.Sprintf("unknown executor type %s", executor.tp))
				}
				if err != nil {
					stdlog.WithError(err).Errorf("failed to schedule the executation of '%s'",
						reflect.TypeOf(executor.execute).Name())
				}
			case <-executor.ctx.Done():
				timer.Stop()
				return
			}
		}
	}()
	return nil
}

func (s *Scheduler) Unregister(executorID string) error {
	s.em.Lock()
	defer s.em.Unlock()
	if _, ok := s.executors[executorID]; !ok {
		return errors.New(fmt.Sprintf("executor identified by %s hasn't registered", executorID))
	}
	s.executors[executorID].cancel()
	delete(s.executors, executorID)
	return nil
}

// RunCronScheduler 依据给定的Crontab表达式进行定时的调度
// 该函数将一直阻塞直到Context结束
// ctx  用于需要该调度协程， 可用context.CancelFunc进行取消
// expr 为解析后的Crontab表达式，用以获取下一次执行时间
// cb   为需要被定时调度执行的回调函数
//
func RunCronScheduler(ctx context.Context, expr *toolkit.CronExpression, cb func() error, errHdl func(error)) {
	if expr == nil {
		stdlog.Errorf("failed to run cron scheduler, cause CronExpression is nil")
		return
	}
	timer := time.NewTimer(1 * time.Hour)
	defer timer.Stop()
	for {
		now := time.Now()
		timer.Reset(expr.Next(now).Sub(now))
		select {
		case <-timer.C:
			if err := cb(); err !=nil {
				errHdl(err)
			}
		case <-ctx.Done():
			timer.Stop()
			return
		}
	}
}
