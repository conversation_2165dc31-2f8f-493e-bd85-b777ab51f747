package examine

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	QueryProjectId          = "project_id"
	CasCallbackHeader       = "Cas-Callback"
	CasCallbackResultHeader = "Cas-Callback-Result"
	ExamineApprove          = "approve"
	ExamineRejected         = "rejected"
)

// ExamineCheck 检查该接口是否需要拦截
// 再go-restful项目中添加此filter方法即可
// 在需要配置拦截审批的接口上添加对应rule的metadata
func ExamineCheck(request *restful.Request, response *restful.Response, chain *restful.FilterChain) {
	metadata := request.SelectedRoute().Metadata()
	if request.HeaderParameter(CasCallbackHeader) == "true" {
		// 如果是有cas回调header，则不拦截
		chain.ProcessFilter(request, response)
		return
	}
	for k, v := range metadata {
		if value, ok := v.(string); ok {
			rule := Rule{
				Key:   k,
				Value: value,
			}
			if config, exist := bindingConfigs[rule]; exist {
				// 需要发送给cas判断是否开启了审批开关
				projectId := request.QueryParameter(QueryProjectId)
				header := request.HeaderParameter("Authorization")
				on, err := GetProjectExamineInfo(projectId, header)
				if err != nil {
					stdlog.WithError(err).Errorf("try to get project [%s] info error", projectId)
					chain.ProcessFilter(request, response)
					return
				}
				if on {
					// 打开了开关，判断当前参数是否符合拦截要求
					ok, err := CheckRequestWithParamRules(request, config)
					if err != nil {
						stdlog.WithError(err).Errorf("try to check extra param rule error.")
						chain.ProcessFilter(request, response)
						return
					}
					if ok {
						res := &ExamineResponse{
							Config:      config,
							ExamineFlag: true,
						}
						response.WriteHeaderAndEntity(http.StatusOK, res)
						return
					} else {
						// 不需要拦截
						chain.ProcessFilter(request, response)
						return
					}
				} else {
					// 不需要拦截
					chain.ProcessFilter(request, response)
					return
				}
			}
		}
	}
	// 都不满足
	chain.ProcessFilter(request, response)
}

func CheckRequestWithParamRules(request *restful.Request, config *ExamineBindingConfig) (bool, error) {
	var body map[string]interface{}
	defer func() {
		if body != nil {
			b, _ := stdsrv.Marshal(body)
			r := io.NopCloser(bytes.NewReader(b))
			request.Request.Body = r
		}
	}()
	if config.ExtraRule == nil || len(config.ExtraRule.Or) == 0 {
		// 没有限定条件
		return true, nil
	}
	for _, orRule := range config.ExtraRule.Or {
		flag := true
		for _, andRule := range orRule.And {
			if andRule.Type == QueryParam {
				param := request.QueryParameter(andRule.Name[0])
				if !andRule.CheckValue(param) {
					flag = false
					break
				}
			} else if andRule.Type == PathParam {
				param := request.PathParameter(andRule.Name[0])
				if !andRule.CheckValue(param) {
					flag = false
					break
				}
			} else {
				if body == nil {
					err := request.ReadEntity(&body)
					if err != nil {
						return false, err
					}
				}
				var (
					v     map[string]interface{}
					value interface{}
				)
				v = body
				for i := 0; i < len(andRule.Name); i++ {
					name := andRule.Name[i]
					if val, ok := v[name]; ok {
						if m, o := val.(map[string]interface{}); o {
							v = m
						}
						if i == len(andRule.Name)-1 {
							value = val
						}
					}
				}
				if param, ok := value.(string); ok {
					if !andRule.CheckValue(param) {
						flag = false
						break
					}
				}
			}
		}
		if flag {
			// 满足所有and条件，直接返回true
			return true, nil
		}
	}
	// 代表所有or条件都不满足
	return false, nil
}

func GetProjectExamineInfo(projectId, header string) (bool, error) {
	result, err := stdsrv.GetProjectWithoutCache(projectId, header)
	if err != nil {
		return false, err
	}
	on := result["examine"]
	if v, ok := on.(float64); ok {
		return v == 1, nil
	} else {
		return false, fmt.Errorf("unknown examine type")
	}
}
