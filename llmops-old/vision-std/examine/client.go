package examine

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	ExamineEndpointEnv = "EXAMINE_ENDPOINT_ENV"
	DefaultEndpoint    = "http://autocv-cas-service:80"
	CreateUrl          = "/api/v1/examine/flows"

	innerToken = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4NzYwMjgyNjQsImlhdCI6MTcyMjQyODI2NCwiaXNzIjoiIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInN1YiI6IiIsInVzZXJuYW1lIjoidGhpbmdlciJ9.U4PVADKz2vt0FkP4XduTyxGe4cmLkWHkCmPe6MArnVIpGBNaI3My9mr63EGxbWWVNypuTJnHXB9H28EJ6tSIAg"
)

var (
	endpoint = ""
)

type CreateExamineFlowReq struct {
	Object         json.RawMessage       `json:"object"`
	Module         string                `json:"module"`
	Config         *ExamineBindingConfig `json:"config"`
	Detail         string                `json:"detail"`
	HttpInfo       *HttpInfo             `json:"httpInfo"`
	RejectHttpInfo *HttpInfo             `json:"rejectHttpInfo"` // 拒绝的回调
	User           string                `json:"-"`
	Token          string                `json:"-"`
}

type HttpInfo struct {
	Body   json.RawMessage `json:"body"`
	Url    string          `json:"url"`
	Method string          `json:"method"`
	Token  string          `json:"token"` // 仅后端用
}

type FlowInfo struct {
	ID uint `json:"id"`
}

// SetEndpoint 可以指定审批服务的endpoint
func SetEndpoint(ep string) {
	endpoint = ep
}

func getEndpoint() string {
	if os.Getenv(ExamineEndpointEnv) != "" {
		return os.Getenv(ExamineEndpointEnv)
	}
	if endpoint != "" {
		return endpoint
	}
	return DefaultEndpoint
}

// CreateFlow 调用cas的审批流程创建接口
// 返回审批流程的id
func CreateFlow(projectID, token string, req *CreateExamineFlowReq) (*FlowInfo, error) {
	endpoint = getEndpoint()
	url := endpoint + CreateUrl
	if projectID == "" {
		return nil, stderr.InvalidParam.Errorf("projectID should not be empty")
	}
	url = fmt.Sprintf("%s?project_id=%s", url, projectID)
	if token == "" {
		token = innerToken
	}
	body, err := stdsrv.Marshal(req)
	if err != nil {
		return nil, err
	}
	request, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", token)
	response, err := httpClient.Do(request)
	if err != nil {
		return nil, err
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	if response.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("status: %s; data: %s", response.Status, string(data))
	}
	ret := new(FlowInfo)
	err = stdsrv.Unmarshal(data, ret)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
