package examine

import (
	"crypto/tls"
	"net/http"
	"os"
	"strconv"
	"time"

	"gopkg.in/yaml.v2"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// ExamineBindingConfig 审批的绑定设置
// 每个绑定对应一种任务提交，例如：模型训练、模型共享
type ExamineBindingConfig struct {
	FlowName  string     `yaml:"flow_name" json:"flowName"`   // 绑定的流程模板名称
	Type      string     `yaml:"type" json:"type"`            // 任务类型，用于列表展示
	Rule      *Rule      `yaml:"rule" json:"rule"`            // 触发审批流程的规则
	ExtraRule *ExtraRule `yaml:"extra_rule" json:"extraRule"` // 一些额外的配置的参数匹配规则
}

// Rule 接口拦截触发审核流程的匹配规则
// 这里从简设计，直接使用go-restful接口绑定时配置metadata的方式来进行匹配
// 例如一个接口绑定设置了如下metadata
// Metadata("examine_rule", "model_train")
// 则该接口会匹配Rule为 Rule{Key: "examine_rule", Value: "model_train"}的审批规则
// 当审批状态开启时，会拦截该接口
type Rule struct {
	Key   string `yaml:"key" json:"key"`
	Value string `yaml:"value" json:"value"`
}

// ExtraRule 包含and规则的列表，所有列表以or规则进行判断
type ExtraRule struct {
	Or []*AndRule `yaml:"or" json:"or"`
}

// AndRule 包含额外规则的列表，所以列表以and规则进行判断
type AndRule struct {
	And []*ParamRule `yaml:"and" json:"and"`
}

// ExtraRule一些额外的特殊规则
// 比如参数需要满足的条件
type ParamRule struct {
	Type     ParamType     `yaml:"type" json:"type"`
	Name     []string      `yaml:"name" json:"name"` // 如果是body，且有嵌套情况，可以按嵌套层级设置参数名
	Operator ParamOperator `yaml:"operator" json:"operator"`
	Value    string        `yaml:"value" json:"value"`
}

// CheckValue 判断是否符合配置
func (p *ParamRule) CheckValue(value string) bool {
	if p == nil {
		return false
	}
	return p.Operator.Operate(p.Value, value)
}

type ParamType string
type ParamOperator string

const (
	PathParam  ParamType = "path"
	QueryParam ParamType = "query"
	BodyParam  ParamType = "body"

	OperatorEq  ParamOperator = "eq"
	OperatorNeq ParamOperator = "neq"
	OperatorGt  ParamOperator = "gt"
	OperatorLt  ParamOperator = "lt"
	OperatorGte ParamOperator = "gte"
	OperatorLte ParamOperator = "lte"
)

func (o ParamOperator) Operate(base, param string) bool {
	switch o {
	case OperatorEq:
		return base == param
	case OperatorNeq:
		return !(base == param)
	case OperatorGt:
		// 尝试转成float
		if b, p, ok := string2Float64(base, param); ok {
			return p > b
		} else {
			return false
		}
	case OperatorLt:
		if b, p, ok := string2Float64(base, param); ok {
			return p < b
		} else {
			return false
		}
	case OperatorGte:
		if b, p, ok := string2Float64(base, param); ok {
			return p >= b
		} else {
			return false
		}
	case OperatorLte:
		if b, p, ok := string2Float64(base, param); ok {
			return p <= b
		} else {
			return false
		}
	default:
		return false
	}
}

func string2Float64(base, param string) (float64, float64, bool) {
	if b, err := strconv.ParseFloat(base, 64); err != nil {
		return 0, 0, false
	} else {
		if p, err := strconv.ParseFloat(param, 64); err != nil {
			return 0, 0, false
		} else {
			return b, p, true
		}
	}
}

type ExamineResponse struct {
	ExamineFlag bool                  `json:"examineFlag"`
	Config      *ExamineBindingConfig `json:"config"`
}

var (
	bindingConfigs map[Rule]*ExamineBindingConfig
	httpClient     *http.Client
)

func initHttpClient() {
	httpClient = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: time.Second * 5,
	}
}

// init 读取etc目录的examine_binding.yaml配置文件加载绑定配置
func init() {
	initHttpClient()
	data, err := os.ReadFile("etc/examine_binding.yaml")
	if err != nil {
		stdlog.Warnf("read etc/examine_binding.yaml error. skip load examine binding config.")
		return
	}
	configs := make([]*ExamineBindingConfig, 0)
	err = yaml.Unmarshal(data, &configs)
	if err != nil {
		stdlog.Warnf("unmarshal etc/examine_binding.yaml error. skip load examine binding config.")
		return
	}
	bindingConfigs = make(map[Rule]*ExamineBindingConfig)
	for _, config := range configs {
		bindingConfigs[*config.Rule] = config
	}
	stdlog.Infof("Succeed to load examine binding configs.")
}
