package stdstatus

import (
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type mwStatusMgr struct {
	*Watchers
	*MwStatuses
	*MwConfig
}

func (s *mwStatusMgr) RegisterStatusHandler(handler StatusHandler) {
	//TODO implement me
	panic("implement me")
}

func (s *mwStatusMgr) UpdateStateByKey(key stdhub.RscKey, state StdState, message string) error {
	status := s.MwStatuses.GetStatusByKey(key)
	changed := status.UpdateState(state, message)
	s.broadcast(status)
	if changed {
		s.SaveStatus(status)
	}
	return nil
}

func (s *mwStatusMgr) UpdateHealth(key stdhub.RscKey, state HealthState, message string) error {
	status := s.MwStatuses.GetStatusByKey(key)
	if changed := status.UpdateHealth(state, message); !changed {
		return nil
	}
	return nil
}

type MwConfig struct {
	MWHID string
}

func NewMwMgrWithConfig(cfg *MwConfig) StatusManager {
	return &mwStatusMgr{
		MwConfig:   cfg,
		Watchers:   &Watchers{sync.Map{}},
		MwStatuses: &MwStatuses{sync.Map{}},
	}
}

func (s *mwStatusMgr) Init() error {
	stdlog.Infof("initialized mw status manager with config : %+v", s.MwConfig)
	return nil
}

func (s *mwStatusMgr) UpdateIndicators(key stdhub.RscKey, indicators map[IndicatorK]IndicatorV) error {
	panic("implement me")
}

func (s *mwStatusMgr) UpdateStatus(ns *StdStatus) error {
	// 为避免指标数据未对齐，需要将前后两个状态进行合并
	if ns == nil {
		return stderr.InvalidParam.Error("empty status is not allowed")
	}
	// 为避免指标数据未对齐，需要将前后两个状态进行合并
	status := ns.DeepCopy()
	s.MwStatuses.SaveStatus(status)
	s.broadcast(status)
	return nil
}

func (s *mwStatusMgr) broadcast(status *StdStatus) {
	status.RLock()
	defer status.RUnlock()
	s.notify(status) // 状态推送至所有的观察者
}
