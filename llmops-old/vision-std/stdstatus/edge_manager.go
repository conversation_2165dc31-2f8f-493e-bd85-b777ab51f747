package stdstatus

import (
	"encoding/json"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/transport/nsq"
)

// 维持缓存map
// 云端通过 nsq消息更新
type edgeStatusMgr struct {
	*Watchers
	*Statuses
	*EdgeConfig
	handlers []StatusHandler
	nsqCli   *nsq.Producer
	tsCli    *influxdb.InfluxClient
}

func (s *edgeStatusMgr) RegisterStatusHandler(handler StatusHandler) {
	s.handlers = append(s.handlers, handler)
}

type EdgeConfig struct {
	EdgeId string
	Influx conf.InfluxdbConfig // used to persistence stats
}

var (
	InitStatusManagerErr = stderr.NewCode(stderr.Status, "初始化状态管理组件失败", "Failed to initialize status manager")
)

func NewEdgeStatusMgrWithConfig(cfg *EdgeConfig) StatusManager {
	return &edgeStatusMgr{
		EdgeConfig: cfg,
		Watchers:   &Watchers{sync.Map{}},
		Statuses:   &Statuses{sync.Map{}},
	}
}

func (s *edgeStatusMgr) Init() error {
	stdlog.Infof("initialized edge status manager with config : %+v", s.EdgeConfig)

	var err error
	// init tsdb client
	if s.tsCli, err = influxdb.NewInfluxClient(s.Influx); err != nil {
		return InitStatusManagerErr.Cause(err, "failed to create influxdb client with config %+v", s.Influx)
	}
	if _, _, err = s.tsCli.Client.Ping(); err != nil {
		return InitStatusManagerErr.Cause(err, "failed to connect influxdb client with config %+v", s.Influx)
	}
	return nil
}

func (s *edgeStatusMgr) UpdateIndicators(key stdhub.RscKey, indicators map[IndicatorK]IndicatorV) error {
	status := s.Statuses.GetStatusByKey(key)
	if len(indicators) == 0 {
		return nil
	}
	status.UpsertIndicators(indicators)
	s.notify(&(*status))
	if err := s.insertIndicators(status.Tags(), indicators); err != nil {
		return stderr.Internal.Cause(err, "存储状态指标时失败")
	}
	return nil
}

func (s *edgeStatusMgr) UpdateStateByKey(k stdhub.RscKey, state StdState, message string) error {
	status := s.Statuses.GetStatusByKey(k)
	status.UpdateState(state, message)
	return s.broadcast(status)
}

func (s *edgeStatusMgr) UpdateHealth(key stdhub.RscKey, state HealthState, message string) error {
	status := s.Statuses.GetStatusByKey(key)
	if changed := status.UpdateHealth(state, message); !changed {
		return nil
	}
	return s.broadcast(status)
}

func (s *edgeStatusMgr) UpdateStatus(ns *StdStatus) error {
	if ns == nil {
		return stderr.InvalidParam.Error("empty status is not allowed")
	}
	// 为避免指标数据未对齐，需要将前后两个状态进行合并
	status := ns.DeepCopy()
	s.Statuses.SaveStatus(status)
	if err := s.insertIndicators(status.Tags(), status.Indicators); err != nil {
		return stderr.Internal.Cause(err, "存储状态指标时失败")
	}
	return s.broadcast(status)
}

func (s *edgeStatusMgr) broadcast(status *StdStatus) error {
	status.RLock()
	defer status.RUnlock()
	s.notify(status) // 状态推送至所有的观察者
	// 处理自定义的状态处理方法
	for _, handler := range s.handlers {
		handler(status)
	}
	// marshal status
	bs, err := json.Marshal(status)
	if err != nil {
		return stderr.Marshal.Cause(err, "序列化状态：%+v时失败", status)
	}

	// publish this status to cloud managers through nsq
	if s.nsqCli == nil {
		return nil
	}
	if err = s.nsqCli.Publish(nsq.NewStdStatusTopic().String(), bs); err != nil {
		stdlog.WithError(err).Errorf("NSQ发布状态时失败")
		// do not return error here
	}
	return nil
}

func (s *edgeStatusMgr) insertIndicators(tags map[string]string, indicators map[IndicatorK]IndicatorV) error {
	// 过滤空值
	fields := make(map[string]interface{}, 0)
	for k, v := range indicators {
		if v == nil {
			continue
		}
		fields[k] = v
	}
	if len(fields) == 0 {
		return nil
	}
	return s.tsCli.WritePoint(IndicatorMeasurement, tags, fields, time.Now())
}
