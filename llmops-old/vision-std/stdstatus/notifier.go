package stdstatus

import (
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type MsgSender struct {
	ch chan *pb.Notification
}

type NotificationType string

var (
	NotifyLevel = struct{ Info, Warn, Error, Success string }{"info", "warn", "error", "success"}

	// avalLevels 前端通知组件支持的级别范围
	// PS:  对于 "open", "" 两种类型,前端会展示不带icon的弹窗, 不好看, 故此处不进行支持
	// PPS: 对于其他取值的 level, 前端将直接不展示推送, 因此该接口会自动将其置为 info 级别
	// PPPS: 大小写敏感
	avalLevels       = utils.NewSet(NotifyLevel.Info, NotifyLevel.Warn, NotifyLevel.Error, NotifyLevel.Success)
	defaultNotiLevel = NotifyLevel.Info
)

func CreateMsgSender() *MsgSender {
	return &MsgSender{
		ch: make(chan *pb.Notification, 100),
	}
}

func (s *MsgSender) Send(noti *pb.Notification) {
	stdlog.Debugf("send notification: %+v", noti)
	noti.Level = strings.ToLower(noti.Level)
	if !avalLevels.Has(noti.Level) {
		stdlog.Warnf("received a notification with unsupported level %s, replace it with default %s", noti.Level, defaultNotiLevel)
		noti.Level = defaultNotiLevel
	}
	s.ch <- noti
}
func (s *MsgSender) Recv() chan *pb.Notification {
	// marshal message struct to byte slice
	return s.ch
}
