package stdstatus

// StdState could represent the state of TASK, FUNCTION, MODEL and APP etc.
// It will be mapped to chinese by frontend.
type StdState string

const (
	Starting   StdState = "starting"
	Running    StdState = "running"
	Stopping   StdState = "stopping"
	Restarting StdState = "restarting"
	Stopped    StdState = "stopped"
	Exception  StdState = "exception"
	Building   StdState = "building"
	Successful StdState = "successful"
	Skipped    StdState = "skipped"
	Failure    StdState = "failure"
)

var (
	stateNames = map[StdState]string{
		Starting:   "启动中",
		Running:    "运行中",
		Stopping:   "停止中",
		Restarting: "重启中",
		Stopped:    "已停止",
		Exception:  "异常",
		Building:   "构建中", // build image: such as function release
		Successful: "成功",
		Skipped:    "跳过",
	}

	// 表示一个资源处于互斥状态的列表状态
	exclusiveStates = map[StdState]struct{}{
		Starting: {},
		Stopping: {},
		Building: {},
	}

	// 表示一个资源被启用的列表状态
	enabledStates = map[StdState]struct{}{
		Running:    {},
		Starting:   {},
		Restarting: {},
	}
)

func (s StdState) Name() string {
	return stateNames[s]
}

// Exclusive returns whether is StdState exclusive.
// An exclusive state means it could not do other operations.
func (s StdState) Exclusive() bool {
	_, ok := exclusiveStates[s]
	return ok
}

// IsEnabled returns if a resource was enabled, which means
// its state is one of enabledStates.
func (s StdState) IsEnabled() bool {
	_, ok := enabledStates[s]
	return ok
}
