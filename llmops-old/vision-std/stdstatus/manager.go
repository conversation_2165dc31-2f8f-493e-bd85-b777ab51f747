package stdstatus

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
)

const (
	DefaultWatcherSize = 50
)

type StatusManager interface {
	StatusGetter
	StatusSetter
	StatusWatcher
	StatusManagerInitializer
	RegisterStatusHandler(handler StatusHandler)
}

// StatusHandler 自定义的状态处理方法，比如向特定组件转发状态
type StatusHandler func(status *StdStatus)

// StatusSelector 用于筛选 StdStatus
// 当返回为true时, 则视为选择当前 StdStatus
// 反之,  则认为过滤当前 StdStatus
type StatusSelector func(*StdStatus) bool

type StatusWatcher interface {
	// Watch返回一个状态Channel, 通道中返回的状态符合给定的所有过滤器
	//  - ctx : 用于管理该Watch协程的生命周期
	Watch(ctx context.Context, selectors ...StatusSelector) <-chan *StdStatus
}

type ResourceWithStatus interface {
	stdhub.ThingerResource
	Status() *StdStatus
}

type StatusGetter interface {
	// GetStatusByKey returns the status of specified resource
	GetStatusByKey(key stdhub.RscKey) *StdStatus
}

type StatusSetter interface {
	// UpdateStateByKey 更新指定资源的状态信息（状态与描述）
	UpdateStateByKey(key stdhub.RscKey, state StdState, message string) error

	// UpdateHealth 更新指定资源的服务运行状态信息（状态与描述）
	UpdateHealth(key stdhub.RscKey, state HealthState, message string) error

	// deprecated 推荐全量更新Status结构
	// UpdateIndicators 更新指定资源的指标信息
	// 增量更新：对于已存在的Key,若Value为nil
	UpdateIndicators(key stdhub.RscKey, indicators map[IndicatorK]IndicatorV) error

	// UpdateStatus 更新指定资源的状态与统计信息
	UpdateStatus(status *StdStatus) error
}

type StatusManagerInitializer interface {
	Init() error
}
