package stdstatus

import (
	"encoding/json"
	"github.com/nsqio/go-nsq"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// 维持缓存map
// 边缘端通过 mqtt 消息更新
// 云端通过 nsq 消息更新
type cloudStatusMgr struct {
	Statuses
	Watchers
	CloudStatusConfig
}

type CloudStatusConfig struct {
	SvcName string // 标识使用该StatusManager的服务
}

func NewCloudStatusMgr(cfg CloudStatusConfig) StatusManager {
	return &cloudStatusMgr{
		CloudStatusConfig: cfg,
		Statuses:          Statuses{},
	}
}
func (s *cloudStatusMgr) RegisterStatusHandler(handler StatusHandler) {
	//TODO implement me
	panic("implement me")
}
func (s *cloudStatusMgr) Init() error {
	stdlog.Infof("initialized cloud status manager with config : %+v", s.CloudStatusConfig)

	return nil
}

// HandleMessage is the nsq message handler.
// It will update statuses cache, notifies those watchers and saves received status.
func (s *cloudStatusMgr) HandleMessage(msg *nsq.Message) error {
	// unmarshal payload
	status := new(StdStatus)
	if err := json.Unmarshal(msg.Body, &status); err != nil {
		stdlog.WithError(err).Errorf("error while unmarshal std status from nsq payload")
		return stderr.Unmarshal.Cause(err, "error while unmarshal std status from nsq payload")
	}

	s.SaveStatus(status)
	s.Watchers.notify(status)
	//s.insertStatus(status)
	return nil
}

func (s *cloudStatusMgr) UpdateStateByKey(k stdhub.RscKey, state StdState, message string) error {
	status := s.Statuses.GetStatusByKey(k)
	status.UpdateState(state, message)
	return nil
}

func (s *cloudStatusMgr) UpdateIndicators(key stdhub.RscKey, indicators map[IndicatorK]IndicatorV) error {
	s.Statuses.GetStatusByKey(key).UpsertIndicators(indicators)
	return nil
}

func (s *cloudStatusMgr) UpdateHealth(key stdhub.RscKey, state HealthState, message string) error {
	s.Statuses.GetStatusByKey(key).UpdateHealth(state, message)
	return nil
}

func (s *cloudStatusMgr) UpdateStatus(ns *StdStatus) error {
	status := s.Statuses.GetStatusByKey(ns.Key())
	sChanged := status.UpdateState(ns.State, ns.Message)
	hChanged := status.UpdateHealth(ns.Health, ns.HealthMsg)
	iChanged := status.UpsertIndicators(ns.Indicators)
	if sChanged || iChanged || hChanged {
		s.notify(&(*status))
	}
	return nil
}
