package stdstatus

import (
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/transport/mqtt"
)

// StdStatus 为统一的资源状态描述结构
type StdStatus struct {
	RefID         string                    `json:"ref_id"`             // 相关资源的ID
	RefType       string                    `json:"ref_type"`           // 相关资源的类型
	ReplicaID     string                    `json:"replica_id"`         // 对应的副本ID
	Node          string                    `json:"node"`               // 当前副本运行的节点
	State         StdState                  `json:"state"`              // 当前状态
	EdgeID        string                    `json:"edge_id"`            // BootService所在边缘节点ID
	Message       string                    `json:"message"`            // 状态的额外描述
	Timestamp     int64                     `json:"timestamp" diff:"-"` // 状态时间戳,单位为毫秒（ms）
	Health        HealthState               `json:"health"`             // 相应资源内服务状态是否正常,仅适用于已启动的资源
	HealthMsg     string                    `json:"health_message"`     // 服务状态额外的信息描述
	ServiceStatus *ServiceStatus            `json:"service_status"`
	Indicators    map[IndicatorK]IndicatorV `json:"indicators"` // 运行时的各种指标信息

	sync.RWMutex
}

type PortInfo struct {
	Name     string `json:"port_name"`
	Port     int32  `json:"port"`
	NodePort int32  `json:"node_port"`
	PortDesc string `json:"port_desc"`
}

// ServiceStatus 资源能够对外提供服务，使用该结构推送服务状态
type ServiceStatus struct {
	NodeIP string      `json:"node_ip"`
	Ports  []*PortInfo `json:"ports"`
}

const StatusMeasurement = string(mqtt.StdStatus)
const IndicatorMeasurement = string(mqtt.StdIndicator)

type (
	StatusTag   = string
	StatusField = string
)

const (
	TagClusterID StatusTag = "edge_id"
	TagRefID     StatusTag = "ref_id"
	TagRefType   StatusTag = "ref_type"
	TagReplicaID StatusTag = "replica_id"

	DefaultReplicaID = "-"
)

// Measurement returns the measurement name where StdStatus stored.
func (s *StdStatus) Measurement() string {
	return StatusMeasurement
}

// Tags returns the tags of StdStatus which could be used to gen index.
func (s *StdStatus) Tags() map[string]string {
	if s.ReplicaID == "" {
		s.ReplicaID = DefaultReplicaID
	}
	return map[string]string{
		TagRefID:     s.RefID,
		TagClusterID: s.EdgeID,
		TagRefType:   s.RefType,
		TagReplicaID: s.ReplicaID,
	}
}

// Fields returns other fields' key and value which need to be saved.
func (s *StdStatus) Fields() map[string]interface{} {
	fields := map[string]interface{}{
		"state":     s.State,
		"message":   s.Message,
		"timestamp": s.Timestamp,
	}
	for k, v := range s.Indicators {
		if v == nil {
			// influx client not support write empty field
			continue
		}
		fields[k] = v
	}
	return fields
}

// Copy 返回当前状态的一个deep copy的副本
func (s *StdStatus) DeepCopy() *StdStatus {
	newS := *s
	s.RLock()
	defer s.RUnlock()
	newS.Indicators = make(map[string]interface{}, len(s.Indicators))
	for k, v := range s.Indicators {
		newS.Indicators[k] = v
	}
	return &newS
}

// UpsertIndicators update giving indicators and return whether indicators substantial change.
func (s *StdStatus) UpsertIndicators(kvs map[IndicatorK]IndicatorV) (changed bool) {
	s.Timestamp = toolkit.NowMS()
	if len(kvs) == 0 {
		return false
	}
	s.Lock()
	defer s.Unlock()
	if len(s.Indicators) == 0 {
		s.Indicators = kvs
		return true
	}

	for k, v := range kvs {
		prev, ok := s.Indicators[k]
		if !ok || prev != v {
			changed = true
		}
		s.Indicators[k] = v
	}
	return
}

func (s *StdStatus) UpdateState(state StdState, message string) (changed bool) {
	s.Timestamp = toolkit.NowMS()
	if s.State != state {
		changed = true
		s.State = state
	}
	if s.Message != message {
		changed = true
		s.Message = message
	}
	// clean heath and indicators cache if Resource stopped running
	if s.State != Running {
		s.Health = None
		s.HealthMsg = NoneMessage
		s.Indicators = make(map[IndicatorK]IndicatorV)
	}
	return
}

func (s *StdStatus) UpdateHealth(health HealthState, message string) (changed bool) {
	s.Timestamp = toolkit.NowMS()
	if s.Health != health {
		changed = true
		s.Health = health
	}
	if s.HealthMsg != message {
		changed = true
		s.HealthMsg = message
	}
	return
}

// KeyStr returns the unique identifier of a StdStatus
func (s *StdStatus) KeyStr() string {
	return s.Key().KeyString()
}

// Key returns the unique identifier of a StdStatus
func (s *StdStatus) Key() stdhub.RscKey {
	return newKey(s.EdgeID, s.RefID, s.RefType)
}

func defaultStdStatus(key stdhub.RscKey) *StdStatus {
	return &StdStatus{
		RefID:      key.ID,
		RefType:    string(key.Type),
		ReplicaID:  DefaultReplicaID,
		State:      Stopped,
		EdgeID:     key.Owner,
		Message:    "",
		Timestamp:  0,
		Health:     None,
		HealthMsg:  NoneMessage,
		Indicators: make(map[IndicatorK]IndicatorV),
		RWMutex:    sync.RWMutex{},
	}
}

func newKey(edgeID, refID, refType string) stdhub.RscKey {
	return stdhub.NewRscKey(edgeID, stdhub.RscType(refType), refID)
}
