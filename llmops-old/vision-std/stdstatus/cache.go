package stdstatus

import (
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
)

type Statuses struct {
	cache sync.Map
}

func (s *Statuses) GetStatusByKey(key stdhub.RscKey) *StdStatus {
	if status, ok := s.cache.Load(key.KeyString()); ok {
		return status.(*StdStatus)
	}

	ds := defaultStdStatus(key)
	s.cache.Store(key.KeyString(), ds)
	return ds
}

// SaveStatus 将尝试更新缓存中的状态.
func (s *Statuses) SaveStatus(status *StdStatus) {
	s.cache.Store(status.Key().KeyString(), status)
	return
}
