package stdstatus

import (
	"context"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
)

type statusWatcher struct {
	id       string          //watcher id
	ch       chan *StdStatus // channel used to emit status
	selector StatusSelector  // 当选择器返回true时, 向Watcher发送该状态
	ctx      context.Context
}

type Watchers struct {
	watchers sync.Map
}

// subscribe status
// update statuses
// put into channel
func (w *Watchers) Watch(ctx context.Context, selectors ...StatusSelector) <-chan *StdStatus {
	return w.watch(ctx, selectors...)
}

// JoinSelectors 将多个资源过滤器结合起来
// 当其中所有Selector返回True时, 则选择当前资源
func JoinSelectors(selectors ...StatusSelector) StatusSelector {
	if len(selectors) == 0 {
		// 筛选器为空, 返回所有选择
		return func(r *StdStatus) bool {
			return true
		}
	}
	return func(r *StdStatus) bool {
		for _, selector := range selectors {
			if selector == nil {
				continue
			}
			if !selector(r) {
				return false
			}
		}
		return true
	}
}

func (w *Watchers) watch(ctx context.Context, selectors ...StatusSelector) <-chan *StdStatus {
	watcher := &statusWatcher{
		ctx:      ctx,
		id:       toolkit.NewUUID(),
		ch:       make(chan *StdStatus, DefaultWatcherSize),
		selector: JoinSelectors(selectors...),
	}
	w.watchers.Store(watcher.id, watcher)
	go func() {
		select {
		case <-ctx.Done(): // watcher died
			w.unwatch(watcher.id)
		}
	}()
	return watcher.ch
}

func (w *Watchers) unwatch(watchID string) {
	watcher, ok := w.watchers.Load(watchID)
	if !ok {
		stdlog.Warnf("watcher %s not found", watchID)
		return
	}
	w.watchers.Delete(watchID)
	close(watcher.(*statusWatcher).ch)
}

func (w *Watchers) notify(status *StdStatus) {
	// send status to all statusChs
	w.watchers.Range(func(key, value interface{}) bool {
		watcher := value.(*statusWatcher)
		// 跳过当前状态
		if !watcher.selector(status) {
			return true
		}
		select {
		case watcher.ch <- status:
			// 避免阻塞
		default:
			stdlog.Errorf("channel of watcher '%s'(edge:%s,refType:%s) is blocking, close it", watcher.id, status.EdgeID, status.RefType)
			w.unwatch(key.(string))
		}
		return true
	})
}

// NewEdgeIDSelector 返回根据EdgeID进行选择的筛选器
// 仅选择来自指定的边缘节点的状态
// ClusterID 为空时, 不进行过滤
func NewEdgeIDSelector(edgeID string) StatusSelector {
	if edgeID == "" {
		return nil
	}
	return func(s *StdStatus) bool {
		return s.EdgeID == edgeID
	}
}

// NewRefTypeSelector 返回根据RefType进行选择的筛选器
// 仅选择指定类型资源的状态
func NewRefTypeSelector(refType string) StatusSelector {
	if refType == "" {
		return nil
	}
	return func(s *StdStatus) bool {
		return s.RefType == refType
	}
}

// NewRefIDSelector 返回根据RefID进行选择的筛选器
// 仅选择指定资源的状态
func NewRefIDSelector(refID string) StatusSelector {
	if refID == "" {
		return nil
	}
	return func(s *StdStatus) bool {
		return s.RefID == refID
	}
}

// NewReplicaIDSelector 返回根据ReplicaID进行选择的筛选器
// 仅选择指定副本ID的状态
func NewReplicaIDSelector(replicaID string) StatusSelector {
	if replicaID == "" {
		return nil
	}
	return func(s *StdStatus) bool {
		return s.ReplicaID == replicaID
	}
}
