package stdstatus

import (
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	triton "transwarp.io/applied-ai/aiot/vision-std/triton/pb"
)

// IndicatorK 为指标的key值，标识了该指标所属
// {indicator}.{node_name}
// e.g.
//  - 实例CPU占用： cpu_percent
//  - 实例模型调用延迟： latency.车牌识别
//  - 实例模型调用延迟： latency.车牌识别
type IndicatorK = string
type IndicatorV = interface{}
type IndicatorUnit string

type WidgetNodeType = string // 规则算子的ID

const (
	// common indicator
	KeyCpuPercent IndicatorK = "cpu_percent"
	KeyMemPercent IndicatorK = "mem_percent"
	KeyMemUsage   IndicatorK = "mem_usage"
	KeyMemLimit   IndicatorK = "mem_limit"
	KeyNetRecv    IndicatorK = "net_recv"
	KeyNetSent    IndicatorK = "net_sent"
	KeyPidNum     IndicatorK = "pid_num"

	// node indicator
	KeyFps     IndicatorK = "fps"
	KeyLatency IndicatorK = "latency"
	KeyFailure IndicatorK = "failure"

	//DLIE model indicator
	KeyAverageSuccess     IndicatorK = "dlie_latency_success"
	KeyAverageFailure     IndicatorK = "dlie_latency_failure"
	KeyAverageQueue       IndicatorK = "dlie_latency_queue"
	KeyAverageInput       IndicatorK = "dlie_latency_input"
	KeyAverageInfer       IndicatorK = "dlie_latency_infer"
	KeyAverageOutput      IndicatorK = "dlie_latency_output"
	KeyInferencePerSecond IndicatorK = "dlie_tps_inference"
	KeySuccessPerSecond   IndicatorK = "dlie_tps_success"
	KeyFailurePerSecond   IndicatorK = "dlie_tps_failure"

	UnitNone    IndicatorUnit = ""
	UnitMs      IndicatorUnit = "ms"
	UnitByte    IndicatorUnit = "byte"
	UnitPercent IndicatorUnit = "%"
	UnitFPS     IndicatorUnit = "f/s"
	UnitEPS     IndicatorUnit = "e/s"
	UnitIPS     IndicatorUnit = "i/s"

	IndicatorFieldSep = "." // 数据库中指标字段与其他标识的分隔符
)

var (
	// 通用的指标
	commonIndicators = []CommonIndicator{
		{Name: "CPU使用率", Key: KeyCpuPercent, Unit: UnitPercent},
		{Name: "内存占用", Key: KeyMemUsage, Unit: UnitByte},

		// 以下指标k3s不支持, 暂时弃用
		//{Name: "内存使用率", Key: KeyMemPercent, Unit: UnitPercent},
		//{Name: "网络接收", Key: KeyNetRecv, Unit: UnitByte},
		//{Name: "网络发送", Key: KeyNetSent, Unit: UnitByte},
		//{Name: "文件写入", Key: KeyBlockWrite, Unit: UnitByte},
		//{Name: "文件读取", Key: KeyBlockRead, Unit: UnitByte},
		//{Name: "进程数量", Key: KeyPidNum, Unit: UnitNone},
	}
	// DLIE模型指标
	dlieLatencyIndicators = []CommonIndicator{
		{Name: "推理成功平均时间", Key: KeyAverageSuccess, Unit: UnitMs},
		{Name: "推理失败平均时间", Key: KeyAverageFailure, Unit: UnitMs},
		{Name: "平均排队时间", Key: KeyAverageQueue, Unit: UnitMs},
		{Name: "平均输入时间", Key: KeyAverageInput, Unit: UnitMs},
		{Name: "平均推理时间", Key: KeyAverageInfer, Unit: UnitMs},
		{Name: "平均输出时间", Key: KeyAverageOutput, Unit: UnitMs},
	}
	dlieCountIndicators = []CommonIndicator{
		{Name: "每秒推理次数", Key: KeyInferencePerSecond, Unit: UnitIPS},
		{Name: "每秒推理成功的次数", Key: KeySuccessPerSecond, Unit: UnitIPS},
		{Name: "每秒推理失败的次数", Key: KeyFailurePerSecond, Unit: UnitIPS},
	}
)

type IndicatorType string

// 通用指标
type CommonIndicator struct {
	Name string        `json:"name"` // 指标名称 e.g. CPU占用,内存占用
	Key  IndicatorK    `json:"key"`  // 对应StdStatus.Indicators中的Key
	Unit IndicatorUnit `json:"unit"` // 指标默认单位
}

// IndicatorLegend 指标的图例描述
type IndicatorLegend struct {
	ID   string `json:"id"`   // 指标图例ID, 用于后续数据渲染
	Name string `json:"name"` // 指标图例名称 e.g. 函数调用, 模型调用
}

func NewPipeNodeLegend(pipeID, nodeID, pipeName, nodeName string) IndicatorLegend {
	return IndicatorLegend{
		ID:   strings.Join([]string{pipeID, nodeID}, IndicatorFieldSep),
		Name: strings.Join([]string{pipeName, nodeName}, IndicatorFieldSep),
	}
}

func NewDLIELegend(modelName, keyID, keyName string) IndicatorLegend {
	return IndicatorLegend{
		ID:   strings.Join([]string{keyID, modelName}, IndicatorFieldSep),
		Name: strings.Join([]string{keyName, modelName}, IndicatorFieldSep),
	}
}

// NewDLIESubModelField 返回DLIE模型部署实例的指标在数据库中的字段名
func NewDLIESubModelField(k IndicatorK, subModel string) string {
	return NewIndicatorField(k, subModel)
}

// NewPipeTaskField 返回多媒体规则实例的CPU / MEM指标在数据库中的字段名
func NewPipeTaskField(k IndicatorK, pipeID string) string {
	return NewIndicatorField(k, pipeID)
}

// NewNodeIndicatorField 返回规则实例中某个算子的健康信息在数据库中的字段名
// 如模型调用算子的调用频率,平均延时; 函数调用算子的CPU, MEM占用 等;
func NewNodeIndicatorField(k IndicatorK, pipeID, nodeID string) string {
	return NewIndicatorField(k, pipeID, nodeID)
}

// NewIndicatorField 返回一个指标在数据库中的字段名
func NewIndicatorField(k IndicatorK, flags ...string) string {
	return strings.Join(append([]string{k}, flags...), IndicatorFieldSep)
}

// Indicator 为监控指标信息
type Indicator struct {
	ID       IndicatorK                 `json:"id"`        // 指标ID
	Name     string                     `json:"name"`      // 指标名称 e.g. 延时,调用,出错
	Unit     IndicatorUnit              `json:"unit"`      // 指标默认单位
	UnitName string                     `json:"unit_name"` // 指标中文单位
	Legends  map[string]IndicatorLegend `json:"legends"`   // 图例列表

	canSum bool // 该指标具有多个图例时, 是否可进行汇总求和
}

func NewCPUIndicator() Indicator {
	return Indicator{
		ID:       KeyCpuPercent,
		Name:     "CPU使用率",
		Unit:     UnitPercent,
		UnitName: "%",
		Legends:  make(map[string]IndicatorLegend, 0),
		canSum:   true,
	}
}

// NewMemIndicator 返回内存占用绝对值的指标
func NewMemIndicator() Indicator {
	return Indicator{
		ID:       KeyMemUsage,
		Name:     "内存占用",
		Unit:     UnitByte,
		UnitName: "字节",
		Legends:  make(map[string]IndicatorLegend, 0),
		canSum:   true,
	}
}

// NewMemPercentIndicator 返回内存占用百分比的指标
func NewMemPercentIndicator() Indicator {
	return Indicator{
		ID:       KeyMemPercent,
		Name:     "内存使用率",
		Unit:     UnitPercent,
		UnitName: "%",
		Legends:  make(map[string]IndicatorLegend, 0),
		canSum:   true,
	}
}

func NewVideoFPSIndicator() Indicator {
	return Indicator{
		ID:       KeyFps,
		Name:     "帧率",
		Unit:     UnitFPS,
		UnitName: "帧/秒",
		Legends:  make(map[string]IndicatorLegend, 0),
	}
}

func NewInferEPSIndicator() Indicator {
	return Indicator{
		ID:       KeyFailure,
		Name:     "推理错误",
		Unit:     UnitEPS,
		UnitName: "错误/秒",
		Legends:  make(map[string]IndicatorLegend, 0),
	}
}

func NewInferLatencyIndicator() Indicator {
	return Indicator{
		ID:       KeyLatency,
		Name:     "推理延时",
		Unit:     UnitMs,
		UnitName: "毫秒",
		Legends:  make(map[string]IndicatorLegend, 0),
	}
}

func NewInferCountIndicator() Indicator {
	return Indicator{
		ID:       KeyLatency,
		Name:     "推理次数",
		Unit:     UnitIPS,
		UnitName: "次/秒",
		Legends:  make(map[string]IndicatorLegend, 0),
	}
}

func CommonIndicators() []CommonIndicator {
	return commonIndicators
}
func DLIEIndicators() []CommonIndicator {
	return append(dlieLatencyIndicators, dlieCountIndicators...)
}
func DLIELatencyIndicators() []CommonIndicator {
	return dlieLatencyIndicators
}
func DLIECountIndicators() []CommonIndicator {
	return dlieCountIndicators
}
func DLIEStats2Indicators(prev, curt *triton.ModelStatistics, interval time.Duration) map[IndicatorK]IndicatorV {
	curtS := curt.InferenceStats
	prevS := prev.InferenceStats
	successIPS := avgExecPerSec(prevS.Success, curtS.Success, interval)
	failureIPS := avgExecPerSec(prevS.Fail, curtS.Fail, interval)
	return map[IndicatorK]IndicatorV{
		KeyAverageSuccess:     avgLatencyMills(prevS.Success, curtS.Success),
		KeyAverageFailure:     avgLatencyMills(prevS.Fail, curtS.Fail),
		KeyAverageInput:       avgLatencyMills(prevS.ComputeInput, curtS.ComputeInput),
		KeyAverageQueue:       avgLatencyMills(prevS.Queue, curtS.Queue),
		KeyAverageInfer:       avgLatencyMills(prevS.ComputeInfer, curtS.ComputeInfer),
		KeyAverageOutput:      avgLatencyMills(prevS.ComputeOutput, curtS.ComputeOutput),
		KeyInferencePerSecond: successIPS + failureIPS,
		KeySuccessPerSecond:   successIPS,
		KeyFailurePerSecond:   failureIPS,
	}
}

// avgLatencyMills 返回两个统计信息之间新增的数量对应的平均延时（ms） = 新增耗时 / 新增请求
func avgLatencyMills(prev, curt *triton.StatisticDuration) float64 {
	if prev == nil || curt == nil {
		return 0
	}
	if prev.Count >= curt.Count || prev.Ns >= curt.Ns {
		// should not occur
		return 0
	}
	latencyNs := 0.0
	// 除0 NaN无法存入influxdb
	if curt.Count-prev.Count != 0 {
		latencyNs = float64(curt.Ns-prev.Ns) / float64(curt.Count-prev.Count)
	}
	return latencyNs / float64(time.Millisecond)
}

// avgExecPerSec 返回两个统计信息之间新增的数量对应的平均每秒执行次数 = 新增请求数 / 区间长度
func avgExecPerSec(prev, curt *triton.StatisticDuration, dur time.Duration) float64 {
	if prev == nil || curt == nil {
		return 0
	}
	if prev.Count > curt.Count {
		// should not occur
		return 0
	}
	return float64(curt.Count-prev.Count) / dur.Seconds()
}

func WorkerStats2Indicators(stats *pb.WorkerStats) map[IndicatorK]IndicatorV {
	return CtnStats2Indicators(stats.Stats)
}

func CtnStats2Indicators(stats *pb.ContainerStats) map[IndicatorK]IndicatorV {
	if stats == nil {
		return make(map[IndicatorK]IndicatorV)
	}
	return map[IndicatorK]IndicatorV{
		KeyCpuPercent: stats.CpuPercent,
		KeyMemPercent: stats.MemPercent,
		KeyMemUsage:   stats.MemUsage,
		KeyMemLimit:   stats.MemLimit,
		//KeyReplicaID:  "1", // FIXME k8s 模式下，多副本方案需要重新设计与适配
		KeyNetRecv: stats.NetRecv,
		KeyNetSent: stats.NetSent,
		//KeyBlockWrite: stats.BlockRead,
		//KeyBlockRead:  stats.BlockWrite,
		KeyPidNum: stats.PidsNum,
	}
}

type MonitorLegend struct {
	ID   string            `json:"id"`   // 图例ID, 用于订阅实时数据
	Name string            `json:"name"` // 图例名称
	Tags map[string]string `json:"tags"` // 标签, 用于从所有数据中筛选出所需的数据
}

type MonitorSeries struct {
	Legend MonitorLegend `json:"legend"`
	Data   []interface{} `json:"data"`
}

// MonitorChart 为应用监控中的一个图表
// 以时间为横轴, 指标值为纵轴, 可能包含同单位的多个图例
type MonitorChart struct {
	ID     string          `json:"id"`     // 图表ID
	Name   string          `json:"name"`   // 图表名称
	Unit   string          `json:"unit"`   // Y轴单位
	Times  []int64         `json:"times"`  // X轴时间戳, 毫秒单位
	Series []MonitorSeries `json:"series"` // Y轴一个或多个系列的数据
}

type Legend struct {
	ID   string            `json:"id"`   // 图例ID, 用于订阅实时数据
	Name string            `json:"name"` // 图例名称
	Tags map[string]string `json:"tags"` // 标签, 用于从所有数据中筛选出所需的数据
}
