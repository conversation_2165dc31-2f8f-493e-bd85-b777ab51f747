package stdstatus

import (
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type MwStatuses struct {
	cache sync.Map
}

func (s *MwStatuses) GetStatusByKey(key stdhub.RscKey) *StdStatus {
	if status, ok := s.cache.Load(key.KeyString()); ok {
		return status.(*StdStatus)
	}
	ds := defaultMeStdStatus(key)
	s.cache.Store(key.KeyString(), ds)
	return ds
}

// SaveStatus 将尝试更新缓存中的状态.
// 若状态时间戳小于当前缓存中的时间戳, 则不保存, 返回false
// 若状态时间戳大于等于当前缓存中的时间戳, 则保存, 返回true
func (s *MwStatuses) SaveStatus(status *StdStatus) bool {
	k := status.Key().KeyString()
	if prev, ok := s.cache.Load(k); ok {
		prevTs := prev.(*StdStatus).Timestamp

		// 接收到过期状态
		if prevTs > status.Timestamp {
			stdlog.Warnf("received obsolete status (prev ts: %d, curt ts: %d) : %+v", prevTs, status.Timestamp, status)
			return false
		}
	}

	s.cache.Store(k, status)
	return true
}

// 产品设计要求只有启动 进行 成功 失败 四种状态
func defaultMeStdStatus(key stdhub.RscKey) *StdStatus {
	return &StdStatus{
		RefID:     key.ID,
		EdgeID:    key.Owner,
		RefType:   string(key.Type),
		State:     Failure,
		Message:   "",
		Timestamp: 0,
		Health:    None,
		HealthMsg: NoneMessage,
		RWMutex:   sync.RWMutex{},
	}
}
