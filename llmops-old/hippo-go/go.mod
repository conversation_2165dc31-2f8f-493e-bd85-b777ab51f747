module transwarp.io/applied-ai/hippo-go

replace transwarp.io/applied-ai/aiot/vision-std => ../vision-std

replace transwarp.io/aip/llmops-common => ../llmops-common

require (
	github.com/go-http-utils/headers v0.0.0-20181008091004-fed159eddc2a
	github.com/google/uuid v1.6.0
	transwarp.io/applied-ai/aiot/vision-std v0.0.0
)

require (
	github.com/creasty/defaults v1.3.0 // indirect
	github.com/go-test/deep v1.0.8 // indirect
	github.com/hpcloud/tail v1.0.0 // indirect
	github.com/juju/errors v0.0.0-20181118221551-089d3ea4e4d5 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	gopkg.in/check.v1 v1.0.0-20190902080502-41f04d3bba15 // indirect
	gopkg.in/fsnotify.v1 v1.4.7 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/tomb.v2 v2.0.0-20161208151619-d5d1b5820637 // indirect
	gopkg.in/validator.v2 v2.0.0-20191008145730-5614e8810ea7 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)

go 1.20
