package hippo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type DBType = string

var supportDBs = utils.NewSet(DBTypeHippo, DBTypeMilvus)

const (
	DBTypeMilvus DBType = "milvus"
	DBTypeHippo  DBType = "hippo"

	DefaultDatabase = "default"
	DefaultTable    = "default"

	QueryParamDatabase   string = "db"
	QueryParamTable      string = "table"
	QueryParamCollection string = "table"
	QueryParamPartition  string = "partition"
	QueryParamDimension  string = "dimension"
	QueryParamIndexType  string = "index_type"
	QueryParamIndexName  string = "index_name"
	QueryParamMetricType string = "metric_type"

	VectorDBFieldID     = "id"      // int 文本块/向量ID
	VectorDBFieldTitle  = "title"   // string 文章所属文件名
	VectorDBFieldText   = "text"    // string 文本向量对应的文本内容
	VectorDBFieldVector = "feature" // []float32 文本的特征向量
)

func ValidURL(urlStr string) (*url.URL, error) {
	if urlStr == "" {
		return nil, stderr.InvalidParam.Error("url is necessary")
	}

	u, err := url.Parse(urlStr)
	if err != nil {
		return nil, stderr.InvalidParam.Cause(err, "invalid url: %s", urlStr)
	}

	if u.Host == "" || u.Port() == "" || u.Scheme == "" {
		return nil, stderr.InvalidParam.Error("schema, host and port are all necessary")
	}

	if !supportDBs.Has(u.Scheme) {
		return nil, stderr.Unsupported.Error("vector database type: %s", u.Scheme)
	}
	return u, nil
}

// func NewVecDBClientFromURL(ctx context.Context, u *url.URL) (Client, error) {
// 	cfg := ParseVectorDBConfigFromURL(u)
// 	switch u.Scheme {
// 	// case DBTypeMilvus:
// 	// 	return NewMilvusDBCli(ctx, cfg)
// 	case DBTypeHippo:
// 		return NewHippoDBCli(ctx, cfg)
// 	default:
// 		return nil, stderr.InvalidParam.Error("unsupported db type %s", u.Scheme)
// 	}
// }

// func NewMilvusClientFromURL(ctx context.Context, db string, u *url.URL) (milvus.Client, error) {
// 	if u == nil {
// 		return nil, stderr.InvalidParam.Error("url is nil")
// 	}
// 	un, up := "", ""
// 	if user := u.User; user != nil {
// 		un = user.Username()
// 		up, _ = user.Password()
// 	}
// 	cfg := milvus.Config{
// 		Address:       u.Host,
// 		Username:      un,
// 		Password:      up,
// 		DBName:        db,
// 		Identifier:    fmt.Sprintf("appengine-%s", time.Now().String()),
// 		EnableTLSAuth: false,
// 		APIKey:        "",
// 		ServerVersion: "",
// 		RetryRateLimit: &milvus.RetryRateLimitOption{
// 			MaxRetry:   5,
// 			MaxBackoff: time.Minute,
// 		},
// 		DisableConn: false,
// 	}
// 	stdlog.Debugf("connect to vector database with config: %+v", cfg)
// 	connCtxt, cancel := context.WithTimeout(ctx, 5*time.Second)
// 	defer cancel()
// 	dbc, err := milvus.NewClient(connCtxt, cfg)
// 	if err != nil {
// 		return nil, stderr.Wrap(err, "connect to database with config: %+v", cfg)
// 	}
// 	return dbc, nil
// }

// func MilvusClientCloser(c milvus.Client) func() {
// 	return func() {
// 		if err := c.Close(); err != nil {
// 			stdlog.WithError(err).Errorf("closing vector db client while stopping task")
// 		}
// 	}
// }

func GetTableNameFromUrl(u *url.URL) (cn string, ok bool) {
	return getParamFromUrl(QueryParamTable, u)
}

func GetPartitionFromUrl(u *url.URL) (cn string, ok bool) {
	return getParamFromUrl(QueryParamPartition, u)
}
func GetDimensionFromUrl(u *url.URL) (cn string, ok bool) {
	return getParamFromUrl(QueryParamPartition, u)
}
func GetDimensionWithDefault(u *url.URL, defaultV int64) int64 {
	ds, ok := getParamFromUrl(QueryParamDimension, u)
	if !ok {
		stdlog.Warnf("use default dimension %d", defaultV)
		return defaultV
	}
	v, err := strconv.ParseInt(ds, 10, 64)
	if err != nil {
		stdlog.WithError(err).Warnf("invalid dimension %s", ds)
		return defaultV
	}
	return v
}

func getParamFromUrl(key string, u *url.URL) (cn string, ok bool) {
	if u == nil {
		return
	}
	kvs := u.Query()
	if kvs == nil {
		return
	}
	vs, ok := kvs[key]
	if !ok {
		return
	}
	if len(vs) == 0 {
		return
	}
	return vs[0], true
}

func getParamFromUrlWithDefault(key string, defaultV string, u *url.URL) (cn string) {
	v, ok := getParamFromUrl(key, u)
	if !ok || v == "" {
		stdlog.Warnf("the param '%s' was been set to default value '%s'", key, defaultV)
		return defaultV
	}
	return v
}

// VectorDBConfig 为各个向量数据库通用的配置定义
type VectorDBConfig struct {
	ClientID        string // 选填，标识调用者
	Address         string // 必填，数据库地址
	UserName        string // 选填，连接数据库时所用的用户名
	UserPassword    string // 选填，连接数据库时所用的用户的登录密码
	Database        string // 必填，数据所在数据库
	Table           string // 必填，等于 milvus.Collection
	Partition       string // 选填，仅Milvus具备该概念
	Dimension       int64  // 必填，处理的向量的维度
	MetricType      string // 选填，相似度/距离 的计算方式， 默认为 IP（自动进行归一化）
	IndexType       string // 选填，索引类型， 默认为 FLAT
	IndexName       string // 选填，索引名称， 默认为类型相符的第一个
	AutoCreateTable bool   // 选填，是否自动创建Table
}

func (c *VectorDBConfig) ValidAndSetDefault() error {
	if c.ClientID == "" {
		c.ClientID = fmt.Sprintf("applet-engine-%d", time.Now().UnixNano())
	}
	if c.Address == "" {
		return stderr.InvalidParam.Error("address is necessary")
	}
	if c.Database == "" {
		c.Database = DefaultDatabase
	}
	// if c.Table == "" {
	// 	return stderr.InvalidParam.Error("table is necessary")
	// }
	// if c.Dimension == 0 {
	// 	return stderr.InvalidParam.Error("dimension is necessary")
	// }
	if c.MetricType == "" {
		c.MetricType = string(defaultMetricType)
	}
	if c.IndexType == "" {
		c.IndexType = string(defaultIndexType)
	}
	return nil
}

// ParseVectorDBConfigFromURL 从传入的 URL 地址中解析向量数据库的配置
// e.g.
//   - milvus://***********:19530?table=testcos&dimension=1024
//   - hippo://shiva:shiva@***********:7788?table=testcos&dimension=1024
// func ParseVectorDBConfigFromURL(u *url.URL) VectorDBConfig {
// 	cfg := VectorDBConfig{
// 		Address:      u.Host,
// 		UserName:     "",
// 		UserPassword: "",
// 		Database:     getParamFromUrlWithDefault(QueryParamDatabase, DefaultDatabase, u),
// 		Table:        getParamFromUrlWithDefault(QueryParamTable, DefaultTable, u),
// 		Partition:    getParamFromUrlWithDefault(QueryParamPartition, "", u),
// 		Dimension:    GetDimensionWithDefault(u, 1024),
// 		IndexName:    getParamFromUrlWithDefault(QueryParamIndexName, "", u),
// 		IndexType:    getParamFromUrlWithDefault(QueryParamIndexType, string(entity.Flat), u),
// 		MetricType:   getParamFromUrlWithDefault(QueryParamMetricType, string(entity.IP), u),
// 	}
// 	if user := u.User; user != nil {
// 		cfg.UserName = user.Username()
// 		cfg.UserPassword, _ = user.Password()
// 	}

// 	return cfg
// }

type Client interface {
	Close()

	Insert(ctx context.Context, columns ...FieldColumnData) ([]int64, error)
	Search(ctx context.Context, params *SearchParams) (*SearchResult, error)
}

type SearchParams struct {
	// Table        string         `json:"table"`         // 必填，进行相似度匹配的数据库
	// IndexName    string         `json:"index_name"`    // 非必填，选择的索引名称
	// VectorField  string         `json:"vector_field"`  // 必填，进行相似度匹配的向量字段
	OutputFields []string       `json:"output_fields"` // 必填，期望输出的字段
	TopK         int            `json:"topK"`          // 必填，返回的相似度最高的记录的条数; query时非必填，作为limit
	Vectors      [][]float32    `json:"vectors"`       // 必填，用于查找的向量; query时不填
	Expr         string         `json:"expr"`          // 非必填，字段过滤条件
	Params       map[string]any `json:"params"`        // 非必填，查询的一些额外配置条件
	Table        string         `json:"table"`         // 非必填，client初始化未指定表时需要
	Normalize    bool           `json:"normalize"`     // 非必填，是否对score进行归一化, 需要度量指标为COSINE
}

type SearchResult struct {
	Queries int                `json:"queries"`
	TopK    int                `json:"top_k"`
	Results []SearchResultItem `json:"results"`
}

type SearchResultItem struct {
	Query       int
	ResultCount int                // the returning entry count
	IDs         []int64            // auto generated id, can be mapped to the columns from `Insert` API
	Fields      []*FieldColumnData // output field data
	Scores      []float32          // distance to the target vector
}

func (r *SearchResultItem) GetField(name string) *FieldColumnData {
	if r == nil {
		return nil
	}
	for _, field := range r.Fields {
		if field.FieldName == name {
			return field
		}
	}
	return nil
}

// MetricType 以 Hippo 中支持的为准
// 度量指标
// 欧式距离Euclidean distance (L2)
// 内积Inner product (IP)

type FieldColumnData struct {
	FieldName string `json:"field_name"`
	Data      []any  `json:"data"`
}

// func NewFieldColumnDataFromMilvus(col entity.Column) (*FieldColumnData, error) {
// 	if col == nil {
// 		return nil, stderr.InvalidParam.Error("nil entity column")
// 	}
// 	fcd := &FieldColumnData{
// 		FieldName: col.Name(),
// 		Data:      make([]any, col.Len()),
// 	}
// 	for i := 0; i < col.Len(); i++ {
// 		v, err := col.Get(i)
// 		if err != nil {
// 			return nil, stderr.Wrap(err, "get value with index %d", i)
// 		}
// 		fcd.Data[i] = v
// 	}
// 	return fcd, nil
// }

func NewColumnData[T any](fieldName string, data []T) FieldColumnData {
	return FieldColumnData{
		FieldName: fieldName,
		Data:      utils.CvcT2AnySlice(data),
	}
}

// func (f *FieldColumnData) ToMilvusColumn() (entity.Column, error) {
// 	if f == nil {
// 		return nil, stderr.InvalidParam.Error("convert nil to milvus colum")
// 	}
// 	if len(f.Data) == 0 {
// 		return nil, stderr.InvalidParam.Error("convert to milvus column without data")
// 	}
// 	item := f.Data[0]
// 	var col entity.Column
// 	switch v := item.(type) {
// 	case bool:
// 		col = entity.NewColumnBool(f.FieldName, utils.MustCvtAny2TSlice[bool](f.Data))
// 	case int8:
// 		col = entity.NewColumnInt8(f.FieldName, utils.MustCvtAny2TSlice[int8](f.Data))
// 	case int16:
// 		col = entity.NewColumnInt16(f.FieldName, utils.MustCvtAny2TSlice[int16](f.Data))
// 	case int32:
// 		col = entity.NewColumnInt32(f.FieldName, utils.MustCvtAny2TSlice[int32](f.Data))
// 	case int64:
// 		col = entity.NewColumnInt64(f.FieldName, utils.MustCvtAny2TSlice[int64](f.Data))
// 	case float32:
// 		col = entity.NewColumnFloat(f.FieldName, utils.MustCvtAny2TSlice[float32](f.Data))
// 	case float64:
// 		col = entity.NewColumnDouble(f.FieldName, utils.MustCvtAny2TSlice[float64](f.Data))
// 	case string:
// 		col = entity.NewColumnVarChar(f.FieldName, utils.MustCvtAny2TSlice[string](f.Data))
// 	case []byte:
// 		col = entity.NewColumnBinaryVector(f.FieldName, len(v), utils.MustCvtAny2TSlice[[]byte](f.Data))
// 	case []float32:
// 		col = entity.NewColumnFloatVector(f.FieldName, len(v), utils.MustCvtAny2TSlice[[]float32](f.Data))
// 	default:
// 		return nil, stderr.InvalidParam.Error("invalid filed type %T", item)
// 	}
// 	return col, nil
// }

func (f *FieldColumnData) ToHippoFieldData() (*HippoFieldData, error) {
	return &HippoFieldData{
		FieldName: f.FieldName,
		Field:     f.Data,
	}, nil
}
