package hippo

import (
	"context"
	"math/rand"
	"testing"
)

var c *HippoClient
var ctx = context.Background()

const (
	DB    = "default"
	TABLE = "llm-b338c3f4-6736-429a-a918-40e4d90ad0da"
)

func init() {

	mc, err := NewHippoDBCli(context.Background(), VectorDBConfig{
		ClientID:        "appengine-test",
		Address:         "**************:31860",
		UserName:        "shiva",
		UserPassword:    "shiva",
		Database:        "default",
		Table:           TABLE,
		Partition:       "",
		Dimension:       768,
		MetricType:      "IP",
		IndexType:       "FLAT",
		AutoCreateTable: false,
	})
	if err != nil {
		panic(err)
	}
	c = mc
}
func TestHippoClient_getTable(t *testing.T) {
	tests := []struct {
		name     string
		c        *HippoClient
		database string
		table    string
		want     string
	}{
		{
			name:     "",
			c:        c,
			database: "default",
			table:    "default",
			want:     "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := c
			ctx := context.TODO()
			got, err := client.ListDatabases(ctx)
			if err != nil {
				t.Fatal(err)
			}

			t.Logf("tables: %+v", got.Databases)

			tables, err := client.ListTables(ctx)
			t.Log(tables, err)

			tb := tables.Tables[0]
			cntRsp, err := client.CountTable(ctx, tb)
			if err != nil {
				t.Fatal(err)
			}
			t.Log("count:", cntRsp.Total)

			has, err := client.HasTable(ctx, "default", "")
			if err != nil || !has {
				t.Fatal(err)
			}
			t.Log(has)

			desc, err := client.GetTable(ctx, DB, TABLE)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("%v", *desc)

			feat := make([]float32, 1024)
			for i, _ := range feat {
				feat[i] = rand.Float32()
			}
			insertData := []FieldColumnData{
				{
					FieldName: "text",
					Data:      []any{"文本内容"},
				},
				{
					FieldName: "feature",
					Data:      []any{feat},
				},
				{
					FieldName: "title",
					Data:      []any{"文档标题.txt"},
				},
			}

			ret, err := client.Insert(ctx, tb, insertData...)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("%v", ret)

			ret2, err := client.Search(ctx, &SearchParams{
				OutputFields: []string{"text", "title"},
				TopK:         10,
				Vectors:      [][]float32{feat},
			})
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("%v", ret2)

		})
	}
}

func TestQuery(t *testing.T) {
	ctx := context.Background()

	// Create a mock search params
	params := &SearchParams{
		Table:        "llm-lite-test-3",
		OutputFields: []string{"title", "text"},
		Vectors:      [][]float32{},
	}

	// Call the Search function with the mock params and mock search function
	result, err := c.Query(ctx, params)

	// Check if the error is nil
	if err != nil {
		t.Errorf("Expected error to be nil, got %v", err)
	}

	for _, v := range result.FieldsData {
		t.Log(v.FieldName, v.FieldValues)
	}

	res, err := c.Query(ctx, &SearchParams{
		Table:        "lite-test",
		OutputFields: []string{"id"},
		Expr:         "title in ['文档标题.txt'] ",
	})

	t.Log(res.FieldsData[0], err)

	ids, err := c.ListIdsByDoc(ctx, "lite-test", "文档标题.txt", VectorDBFieldTitle, "id")
	t.Log(ids)

	// Check if the search result is equal to the expected result
	// if !reflect.DeepEqual(result, expectedResult) {
	// 	t.Errorf("Expected result to be %v, got %v", expectedResult, result)
	// }
}

func TestListTables(t *testing.T) {
	ctx := context.Background()
	rsp, err := c.ListTables(ctx)
	if err != nil {
		t.Errorf("Expected error to be nil, got %v", err)
	}
	t.Log(rsp.Tables)
}

//llm-d2b956e8-11eb-4d3d-be52-0905731d8e8c

func TestCreateT(t *testing.T) {

	schema := &TableSchema{
		AutoId: false,
		Fields: []TableField{
			{
				Name:         "id",
				IsPrimaryKey: true,
				DataType:     String,
			}, {
				Name:     "text",
				DataType: String,
			}, {
				Name:     "doc_id",
				DataType: String,
			}, {
				Name:     "vector",
				DataType: FloatVector,
				TypeParams: &FieldTypeParams{
					Dimension: 768,
				},
			},
		},
	}

	req := &CreateTableReq{
		Settings: TableSettings{
			NumberOfShards:   1,
			NumberOfReplicas: 1,
		},
		Schema:    *schema,
		TableName: "test-create-table",
		Database: c.cfg.Database,
	}

	err := c.CreateTable(ctx, req)
	if err != nil {
		t.Fatal(err)
	}

	rsp, err := c.ListTables(ctx)
	if err != nil {
		t.Errorf("Expected error to be nil, got %v", err)
	}
	t.Log(rsp.Tables)

}