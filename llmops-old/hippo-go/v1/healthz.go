package hippo

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	healthzTableName = "hippo-healthz-created-by-hippo-go"
	healthzTimeout   = 100 * time.Second
)

var (
	healthzTableSchema = TableSchema{
		AutoId: false,
		Fields: []TableField{
			{
				Name:         "chunk_id",
				IsPrimaryKey: true,
				DataType:     String,
			}, {
				Name:     "feature",
				DataType: FloatVector,
				TypeParams: &FieldTypeParams{
					Dimension: 1,
				},
			},
		},
	}
)

func (c *HippoClient) Healthz() (ok bool, msg string) {
	startTime := time.Now()
	ctx, _ := context.WithTimeout(context.Background(), healthzTimeout)

	// delete if exists
	if has, err := c.HasTable(ctx, "", healthzTableName); err != nil {
		msg = fmt.Sprintf("Failed to check table: %v", err)
		return
	} else if has {
		stdlog.Warnf("healthzTable already exists, delete it")
		if err = c.DeleteTable(ctx, healthzTableName); err != nil {
			msg = fmt.Sprintf("Failed to delete existed healthz table, err:%v", err)
			return
		}
	}

	// check createTable
	createReq := &CreateTableReq{
		Settings: TableSettings{
			NumberOfShards:   1,
			NumberOfReplicas: 1,
		},
		Schema:    healthzTableSchema,
		TableName: healthzTableName,
		Database:  c.cfg.Database,
	}
	if err := c.CreateTable(ctx, createReq); err != nil {
		msg = fmt.Sprintf("Failed to create table: %v", err)
		return
	}

	// check createIndex
	idx := &Index{
		FieldName:  "feature",
		IndexName:  hippoDefaultIndexName,
		MetricType: defaultMetricType,
		IndexType:  defaultIndexType,
		Params: map[string]any{
			"nlist": 10,
		},
	}
	if _, err := c.CreateIndex(ctx, "", healthzTableName, idx); err != nil {
		msg = fmt.Sprintf("Failed to create index: %v", err)
		return
	}

	// check prepare table  (load & activate index)
	if err := c.PrepareTable(ctx, healthzTableName); err != nil {
		msg = fmt.Sprintf("Failed to prepare table: %v", err)
		return
	}

	// check insert data
	testId := uuid.New().String()
	colId := FieldColumnData{
		FieldName: "chunk_id",
		Data:      []any{testId},
	}
	colFeature := FieldColumnData{
		FieldName: "feature",
		Data:      []any{[]float32{1.0}},
	}
	if _, err := c.Insert(ctx, healthzTableName, colId, colFeature); err != nil {
		msg = fmt.Sprintf("Failed to insert row: %v", err)
		return
	}

	// check query
	if _, err := c.Search(ctx, &SearchParams{
		OutputFields: []string{"chunk_id"},
		TopK:         10,
		Vectors:      [][]float32{{1.0}},
		Table:        healthzTableName,
		Normalize:    true,
		Params:       map[string]any{},
	}); err != nil {
		msg = fmt.Sprintf("Failed to search: %v", err)
		return
	}

	// check delete data
	if _, err := c.DeleteByQuery(ctx, healthzTableName, fmt.Sprintf("chunk_id in ['%s']", testId)); err != nil {
		msg = fmt.Sprintf("Failed to delete row: %v", err)
		return
	}

	// check delete table
	if err := c.DeleteTable(ctx, healthzTableName); err != nil {
		msg = fmt.Sprintf("Failed to delete table: %v", err)
		return
	}

	// check delete trash
	if err := c.DeleteTrash(ctx, healthzTableName); err != nil {
		msg = fmt.Sprintf("Failed to delete trash: %v", err)
		return
	}	

	stdlog.Infof("Health check passed in %v", time.Since(startTime))
	return true, "Health check passed"
}
