PUT '/_database?pretty'
GET '/_database?pretty'
DELETE '/_database?pretty'

PUT '/{table}?database_name={database_name}&pretty'
DELETE '/{table}?database_name={database_name}' // 支持通配
GET '/{{.table}}?database_name={database_name}&pretty'

PUT '/_rename_table?pretty'

GET '/_check_table_existence?pretty'
GET '/_cat/tables/{table}?v'
GET '/_cat/tables?v'
GET '/_cat/shards/{table}?v'
GET '/_cat/shards?v'

GET '/_settings?pretty'
GET '/{table}/_settings?pretty'
GET '/{table}/_setting?pretty'
PUT '/{table}/_settings?pretty'
POST '/_aliases?database_name={database_name}&pretty'
GET '/_cat/aliases?v'
DELETE '/_aliases/{alias}?pretty'
PUT '/{table}/_bulk?pretty'
PUT '/{table}/_bulk?pretty'
PUT '/{table}/_bulk?pretty'

POST '/_copy_by_query?pretty'
POST '/_copy_by_query?pretty'
POST '/_delete_by_query?pretty'
POST '/{table}/_compact_db?database_name={database_name}&pretty'
POST '/{table}/_warmup_db?database_name={database_name}&pretty'
POST 'localhost:7788/_drop_block_cache?pretty'
POST 'localhost:7788/_drop_block_cache/{node}?pretty&drop_index_block_cache=true'
POST '/_standalone_load_data?database_name={database_name}&pretty'
POST '/_standalone_export_data?pretty'
GET '/_get_standalone_job/{job_id}?pretty&delete_finished_jobs=false'

POST ' /{table}/_embedding_index_auto_compaction?database_name={database_name}&pretty'
POST '/{table}/_compact_embedding_index?database_name={database_name}&pretty'
PUT '/{table}/_create_scalar_index?database_name={database_name}&pretty'
DELETE '/{table}/_drop_scalar_index?database_name={database_name}&pretty'
GET '/{table}/_search?database_name={database_name}&pretty'
GET '/{table}/_search?database_name={database_name}&pretty'
GET '/{table}/_query?database_name={database_name}&pretty'
PUT '/book?database_name={database_name}&pretty'
PUT '/book/_create_scalar_index?database_name={database_name}&pretty'
PUT '/book/_bulk?pretty' -H'Content-Type: application/json'
GET 'localhost:9200/book/_search?pretty' -H'Content-Type: application/json'
GET '/{table}/_count?database_name={database_name}&pretty'
GET '/{table}/_count?database_name={database_name}&pretty' -
GET '/_cat/master?v'
GET '/_cat/nodes?v'
GET '/_cat/nodes/{node}?v'
PUT /_security/user/{user_name}?pretty  -d '{
GET /_security/user/{user_name}?pretty
DELETE /_security/user/{user_name}?pretty
POST /_security/user/{user_name}/_alter?pretty  -d '{
POST "/_security/acl/{user_name}?pretty"  -d'
DELETE "/_security/acl/{user_name}?pretty"  -d'
GET "/_security/user/_privileges/{username}?pretty"
GET "/_security/tables/{table}?pretty"
GET "/_jobs?pretty"  -d'
DELETE "/_jobs/{jod_id}?pretty"
GET '/_cat/trash?database_name=book&v'
DELETE '/_trash/{table}?database_name=book&pretty'
