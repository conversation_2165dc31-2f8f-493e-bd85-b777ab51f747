# ARG 指令有生效范围，如果在 FROM 指令之前指定，那么只能用于 FROM 指令中
# 在FROM 之后使用变量，必须在每个阶段分别指定
ARG ARCH=amd64

FROM ***********/aip/base/golang-builder:1.23-ubuntu24.04 AS build
ARG PACKAGE=transwarp.io/applied-ai/aiot/applet-backend
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG ARCH=amd64
ARG COMP=applet-backend

WORKDIR /build

COPY ${COMP} ${COMP}
COPY llmops-common llmops-common
COPY vision-std vision-std
COPY hippo-go hippo-go
COPY tkh-go tkh-go
COPY applet-engine applet-engine
COPY hybase-go hybase-go
COPY doc-engine-go doc-engine-go

RUN cd ${COMP} && \
    GOPROXY=http://*************:1111,https://goproxy.cn/,https://goproxy.io/,https://mirrors.aliyun.com/goproxy,direct \
    GO111MODULE=on \
    GOOS=linux \
    GOARCH=${ARCH} \
    go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
    -o ${COMP} ./main.go

FROM ***********/aip/base/ubuntu-runner:24.04
ARG ARCH=amd64
ARG COMP=applet-backend

WORKDIR /${COMP}
COPY --from=build /build/vision-std/license/bin/verifier /usr/bin/verifier
COPY --from=build /build/${COMP}/etc etc
COPY --from=build /build/${COMP}/public public
COPY --from=build /build/${COMP}/${COMP} ${COMP}
COPY --from=build /build/${COMP}/bin/boot.sh /bin/boot.sh

RUN chmod +x /bin/boot.sh
EXPOSE 80
CMD ["/bin/bash", "/bin/boot.sh"]