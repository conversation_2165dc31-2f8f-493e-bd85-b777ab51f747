{"id": "2297ccb8-5309-4106-923d-e017f150c74c", "schema": "MODEL_SERVICE_SCHEMA_SELDON", "host": "istio-ingressgateway.istio-system", "port": 8001, "type": "MODEL_SERVICE_TYPE_LOCAL", "apis": [{"path": "/atom", "inputs": [{"name": "TEXTS", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "%%text%%"}], "outputs": [{"name": "RESULT", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "{}"}]}], "status": {"state": "running", "message": "", "timestamp": "0", "health": "healthy", "health_msg": "", "nodes": [], "service_status": null, "ref_id": "", "ref_type": "", "replica_id": "", "edge_id": "", "indicators": {}}, "kind": "MODEL_KIND_NLP", "sub_kind": "MODEL_SUB_KIND_NLP_TEXT_VECTOR", "model_name": "bge-m3", "release_name": "m3", "release_version": "v1", "model_id": "MWH-MODEL-cvj5vopvc6cv23gjnvdg", "release_id": "MWH-MODEL-RELEASE-cvj5vr9vc6cv23gjnve0", "inference_params": [], "prompt": "", "namespace": "assets-46i", "seldon_deploy_name": "service-2297ccb8-5309-4106-923d-e017f150c74c", "name": "模型部署为服务_bge-m3_20250328175059", "full_url": "http://istio-ingressgateway.istio-system/seldon/assets-46i/service-2297ccb8-5309-4106-923d-e017f150c74c/8011/openai/v1/embeddings?project_id=assets", "invoke_as_tool": null, "remote_service_config": null, "desc": "", "create_time_ms": "-601233176", "reference_model": {"id": "MWH-MODEL-cvj5vopvc6cv23gjnvdg", "name": "bge-m3", "domain": {"kind": "MODEL_KIND_NLP", "sub_kind": "MODEL_SUB_KIND_NLP_TEXT_VECTOR", "type": "MODEL_TYPE_FILE", "subtype": "MODEL_SUB_TYPE_FILE_TRANSFORMER", "schedule_mode": "MODEL_SCHEDULE_MODE_STATELESS", "output_kind": "MODEL_KIND_UNSPECIFIED", "output_sub_kind": "MODEL_SUB_KIND_UNSPECIFIED", "algorithm": "MO", "components": []}, "detail": {"desc": "", "user_id": "yu.zhang", "thumbnail": "", "is_public": false, "create_time_ms": "1743151075221", "update_time_ms": "1743155343267", "labels": {}, "baselines": {}, "relations": []}, "stats": {"latest_release": null, "baseline_release": null, "release_count": 0, "releases_info": [], "usage_count": {"deploys_count": 0, "views_count": 5, "downloads_count": 0, "invokes_count": 0, "trainings_count": 0, "evaluations_count": 0, "model_id": "MWH-MODEL-cvj5vopvc6cv23gjnvdg"}, "disk_usage": 4587156500}, "apis": [{"path": "/atom", "inputs": [{"name": "TEXTS", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "%%text%%"}], "outputs": [{"name": "RESULT", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "{}"}]}], "attachments": [], "training_template": "TRAINING_TEMPLATE_UNSPECIFIED", "project_id": "assets", "asset_type": "ASSET_SHARED", "source_project_id": "", "is_system_init": false}, "reference_release": {"release_base": {"id": "MWH-MODEL-RELEASE-cvj5vr9vc6cv23gjnve0", "name": "m3", "version": "v1", "repo": "sfs:///tenants/assets-46i/projs/assets/mwh/MWH-MODEL-cvj5vopvc6cv23gjnvdg/releases/MWH-MODEL-RELEASE-cvj5vr9vc6cv23gjnve0/model-files", "model_id": "MWH-MODEL-cvj5vopvc6cv23gjnvdg", "is_baseline": false, "detail": {"desc": "", "model_size": "**********", "create_time_ms": "0", "update_time_ms": "1743155343270", "labels": {}, "relations": [], "attachments": [], "computation_attributes": {"output_vector_dims": "1024"}}, "stats": {"deployment_id": "4121fef3-b7e6-4ab6-a0f5-04a98a2ce63e", "deployment_status": "stopped", "deployment_health": "unhealthy", "model_name": "", "release_status": "MODEL_RELEASE_STATUS_UNEVALUATED"}, "hardware_range": {"arches": ["CPU_ARCH_MULTI"], "acc_types": []}, "default_config": {"runtime": "MODEL_RUNTIME_TYPE_UNSPECIFIED", "resource": null, "values": {}, "deployment_params": [{"id": "USE_VLLM", "name": "USE_VLLM", "desc": "", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED", "number_range": null, "default_value": "1", "datasource": "", "precondition": "", "required": true, "multiple": false, "maxlen": "0", "placeholder": "", "advanced": false, "multiline": false, "hidden": false, "precision": 0, "disabled": false, "comp_props": ""}], "inference_params": [], "default_prompt": "", "use_default_params": false}, "model_apis": [{"path": "/atom", "inputs": [{"name": "TEXTS", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "%%text%%"}], "outputs": [{"name": "RESULT", "data_type": "DATA_TYPE_BYTES", "format": "MODEL_PARAM_FORMAT_NONE", "dims": [1], "optional": false, "default_value": "{}"}]}], "project_id": "assets", "creator": "yu.zhang", "training_template": null, "is_system_init": false}, "model_meta": {"model_type": "MODEL_TYPE_FILE", "file_model_meta": {"raw": "", "encrypt": false, "training_data_distributions": {}}, "image_model_meta": null, "ensemble_model_meta": null}}, "project_id": "assets", "reference_remote_service": null, "update_time_ms": "-601232176", "guardrails_config": {"is_security": false, "guardrails_id": ""}, "custom_service_endpoints": []}