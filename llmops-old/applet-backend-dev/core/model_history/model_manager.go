package model_history

import (
	"context"
	"encoding/json"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/mapping"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

var (
	ConversionIdListSuffix = "_list_suffix"
	ConversionIdKeySuffix  = "_key_suffix"
)

type ModelManager interface {

	//具体业务需要
	HSetMessChildren(conId string, parentId string, currentIds ...string) error
	SetHistory(conversationId string, parentMessageId string, messages []mapping.Message, expiration time.Duration) error
	GetHistory(conId string, total bool) ([]mapping.Message, error)
	DelHistoryRecursive(conId, id string) error
	ClearHistory(conId string) error
	DeleteHistory(mlopsSvcId string) error
	DeleteSingleHistory(chatId string) error
	GetLast(key string) (string, error)
	SetTotalHistory(id string, qMessages []mapping.Message, aMessages []mapping.Message) error
	SetDefaultHead(conversationId string)
	GetTotalHistory(mlopsId, projectId, user string) (map[string][]mapping.Message, error)
	GetConversations(mlopsSvcId, projectId string) ([]string, error)
	GeneConId(ctx context.Context, id string, id2 string, id3 string, id4 string) string
	SAdd(ctx context.Context, param *models.ChainRunReq) error
}

type ModelManagerImpl struct {
}

func (m ModelManagerImpl) HSetMessChildren(conId string, parentId string, currentIds ...string) error {

	var message mapping.Message
	node, err := clients.RedisCli.HGet(conId, parentId)
	err = json.Unmarshal([]byte(node), &message)
	if err != nil {
		return err
	}

	for _, child := range message.Children {
		err = clients.RedisCli.HDel(conId, child)
		if err != nil {
			return err
		}
	}
	message.Children = make([]string, 0)

	for _, id := range currentIds {

		message.Children = append(message.Children, id)
	}

	val, _ := json.Marshal(message)
	err = clients.RedisCli.HSet(conId, parentId, string(val))
	if err != nil {
		return err
	}
	return nil
}

// SetHistory 不做时间过期的限制，这样刷新页面，或者重新登录后，依然可以获取到对应的聊天记录
func (m ModelManagerImpl) SetHistory(conversationId string, parentMessageId string, messages []mapping.Message,
	expiration time.Duration) error {

	currMessages := make([]string, 0)

	for _, message := range messages {
		message.ParentId = parentMessageId
		m, _ := json.Marshal(message)
		err := clients.RedisCli.HSet(conversationId, message.Id, string(m))
		if err != nil {
			return err
		}

		err = clients.RedisCli.RPush(conversationId+ConversionIdListSuffix, message.Id)
		if err != nil {
			return err
		}
		currMessages = append(currMessages, message.Id)
	}

	if len(parentMessageId) != 0 {
		err := m.HSetMessChildren(conversationId, parentMessageId, currMessages...)
		if err != nil {
			return err
		}
	}

	return nil

}

func (m ModelManagerImpl) GetHistory(conversationId string, total bool) ([]mapping.Message, error) {
	conId := "test"
	//没有历史记录
	if val, _ := clients.RedisCli.Get(conId + ConversionIdKeySuffix); len(val) == 0 {
		return nil, nil
	}

	//获取到头节点
	root, err := clients.RedisCli.Get(conId + ConversionIdKeySuffix)
	if err != nil {
		return nil, err
	}

	var message mapping.Message
	node, _ := clients.RedisCli.HGet(conId, root)
	err = json.Unmarshal([]byte(node), &message)
	if err != nil {
		return nil, err
	}
	res := make([]mapping.Message, 0)

	var queue = []mapping.Message{message}
	for len(queue) > 0 {
		counter := len(queue)
		for i := 0; i < counter; i++ {
			current := queue[i]
			//要么拿全部，要么只拿还存在的数据
			if total || current.Status == mapping.StatusTypeNames[mapping.StatusTypeSuccess] {
				res = append(res, current)
			}
			for _, child := range current.Children {
				childData, err := clients.RedisCli.HGet(conId, child)
				if err != nil {
					return nil, err
				}
				var childMessage mapping.Message
				err = json.Unmarshal([]byte(childData), &childMessage)
				if err != nil {
					return nil, err
				}
				queue = append(queue, childMessage)

			}
		}
		queue = queue[counter:]
	}

	return res, nil
}

func (m ModelManagerImpl) DelHistoryRecursive(conId, id string) error {

	//没有历史记录
	if val, _ := clients.RedisCli.Get(conId + ConversionIdKeySuffix); len(val) == 0 {
		return nil
	}

	var message mapping.Message
	node, err := clients.RedisCli.HGet(conId, id)
	//未获取到
	if err != nil {
		return nil
	}

	err = json.Unmarshal([]byte(node), &message)
	if err != nil {
		return err
	}
	var queue = []mapping.Message{message}
	//默认情况下只会有一个子节点(兼容性)
	for len(queue) > 0 {
		counter := len(queue)
		for i := 0; i < counter; i++ {
			current := queue[i]
			err := clients.RedisCli.HDel(conId, current.Id)
			if err != nil {
				return err
			}

			err = clients.RedisCli.LRem(conId+ConversionIdListSuffix, current.Id, -1)
			if err != nil {
				return err
			}

			for _, child := range current.Children {
				childData, err := clients.RedisCli.HGet(conId, child)
				if err != nil {
					return err
				}
				var childMessage mapping.Message
				err = json.Unmarshal([]byte(childData), &childMessage)
				if err != nil {
					return err
				}
				queue = append(queue, childMessage)

			}
		}
		queue = queue[counter:]
	}

	//没有父节点
	if len(message.ParentId) == 0 {
		return nil
	}

	// 父节点删除当前子节点
	var parent mapping.Message
	node, err = clients.RedisCli.HGet(conId, message.ParentId)
	err = json.Unmarshal([]byte(node), &parent)
	if err != nil {
		return err
	}

	for i, childId := range parent.Children {
		if childId == message.Id {
			parent.Children = append(parent.Children[:i], parent.Children[i+1:]...)
		}
	}

	mParent, _ := json.Marshal(parent)
	err = clients.RedisCli.HSet(conId, parent.Id, string(mParent))
	if err != nil {
		return err
	}

	return nil
}

func (m ModelManagerImpl) ClearHistory(conId string) error {

	//没有历史记录
	list, err := clients.RedisCli.LRange(conId+ConversionIdListSuffix, 0, -1)
	if err != nil || len(list) == 0 {
		return nil
	}

	for _, data := range list {
		var message mapping.Message
		childData, err := clients.RedisCli.HGet(conId, data)
		if err != nil {
			return err
		}
		err = json.Unmarshal([]byte(childData), &message)
		message.Status = mapping.StatusTypeNames[mapping.StatusTypeDeleted]
		m, _ := json.Marshal(message)
		err = clients.RedisCli.HSet(conId, message.Id, string(m))
		if err != nil {
			return err
		}
	}

	//删除服务的时候，直接清空掉所有的数据 see: DeleteHistory

	/*
		设置一天后过期, 不进行错误检测，默认有数据
		clients.RedisCli.Expire(conId, 24*time.Hour)
		clients.RedisCli.Expire(conId+ConversionIdListSuffix, 24*time.Hour)
		clients.RedisCli.Expire(conId+ConversionIdKeySuffix, 24*time.Hour)*/

	return nil

}

func (m ModelManagerImpl) DeleteHistory(mlopsSvcId string) error {

	members, err := clients.RedisCli.SMembers(mlopsSvcId)

	//没找到记录
	if err != nil || len(members) == 0 {
		return nil
	}

	for _, member := range members {
		err := clients.RedisCli.Del(member, member+ConversionIdKeySuffix, member+ConversionIdListSuffix)
		if err != nil {
			return err
		}
	}

	err = clients.RedisCli.Del(mlopsSvcId)
	if err != nil {
		return err
	}

	return nil

}

func (m ModelManagerImpl) DeleteSingleHistory(conId string) error {

	err := clients.RedisCli.Del(conId, conId+ConversionIdKeySuffix, conId+ConversionIdListSuffix)
	if err != nil {
		return err
	}

	array := strings.Split(conId, "_")

	err = clients.RedisCli.SRem(array[2], conId)
	if err != nil {
		return err
	}
	return nil

}

func (m ModelManagerImpl) GetLast(key string) (string, error) {
	listLen, _ := clients.RedisCli.LLen(key + ConversionIdListSuffix)
	result, err := clients.RedisCli.LRange(key+ConversionIdListSuffix, listLen-1, listLen)
	if err != nil || len(result) == 0 {
		return "", err
	}
	return result[0], nil
}

func (m ModelManagerImpl) SetTotalHistory(conversationId string, qMessages []mapping.Message, aMessages []mapping.Message) error {

	//设置头节点
	m.SetDefaultHead(conversationId)

	// 将响应内容写入到 Redis 中
	lastId, _ := m.GetLast(conversationId)
	err := m.SetHistory(conversationId, lastId, qMessages, 0)
	if err != nil {
		return err
	}

	lastId, _ = m.GetLast(conversationId)
	err = m.SetHistory(conversationId, lastId, aMessages, 0)
	return err
}

func (m ModelManagerImpl) SetDefaultHead(conversationId string) {

	head, _ := clients.RedisCli.Get(conversationId + ConversionIdKeySuffix)

	if head == "" {
		defaultMessage := mapping.DefaultMessage
		m, _ := json.Marshal(defaultMessage)
		_ = clients.RedisCli.Set(conversationId+ConversionIdKeySuffix, defaultMessage.Id, 0)
		_ = clients.RedisCli.RPush(conversationId+ConversionIdListSuffix, defaultMessage.Id)
		_ = clients.RedisCli.HSet(conversationId, defaultMessage.Id, string(m))

	}

}

func (m ModelManagerImpl) GetTotalHistory(mlopsId, projectId, user string) (map[string][]mapping.Message, error) {
	messages := make(map[string][]mapping.Message)

	members, err := clients.RedisCli.SMembers(mlopsId)

	//没找到记录
	if err != nil || len(members) == 0 {
		return nil, nil
	}

	for _, member := range members {
		if strings.Contains(member, projectId) && strings.Contains(member, user) {
			history, err := m.GetHistory(member, true)
			if err != nil {
				return nil, err
			}
			messages[member] = history
		}
	}

	return messages, nil
}

func (m ModelManagerImpl) GetConversations(mlopsSvcId, projectId string) ([]string, error) {
	conversations := make([]string, 0)

	members, err := clients.RedisCli.SMembers(mlopsSvcId)

	//没找到记录
	if err != nil || len(members) == 0 {
		return nil, nil
	}

	for _, member := range members {
		if strings.Contains(member, projectId) {
			conversations = append(conversations, member)
		}
	}

	return conversations, nil
}
func (m ModelManagerImpl) SAdd(ctx context.Context, param *models.ChainRunReq) error {
	projectID := helper.GetProjectID(ctx)
	chatId := m.GeneConId(ctx, param.ChatId, param.ChainID, projectID, param.MlOpsSvcID)
	err := clients.RedisCli.SAdd(param.ChainID, chatId)

	return err

}

// GeneConId fixme 默认的会话 id 形式： {user}_{project_id}_{mlops_id}_{chain_id}_{con_id}
func (m ModelManagerImpl) GeneConId(ctx context.Context, conId, chain, projectId, mlopsSvcId string) string {

	var conversationId string

	conversationId, _ = helper.GetUser(ctx)
	conversationId += "_" + projectId + "_" + mlopsSvcId + "_" + chain + "_" + conId

	return conversationId
}
