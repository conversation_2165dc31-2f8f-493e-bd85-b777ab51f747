package applet

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"sort"
	"transwarp.io/aip/llmops-common/pb/serving"
	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type Widget interface {
	ListWidgetGroups(ctx context.Context) ([]*models.WidgetGroup, error)
	ListDynamicWidgetsByType(ctx context.Context, widgetType string) ([]*models.DynamicWidgetDesc, error)
	GetDynamicWidgetsByKey(ctx context.Context, widgetType string, widgetKey string) (*widgets.Widget, error)

	// CreateCustomWidget 创建自定义算子
	CreateCustomWidget(ctx context.Context, widget *models.CustomWidgetDO) (string, error)
	// UpdateCustomWidget 更新自定义算子
	UpdateCustomWidget(ctx context.Context, id string, widget *models.CustomWidgetDO) (string, error)
	// GetCustomWidgetByID 获取自定义算子
	GetCustomWidgetByID(ctx context.Context, id string) (*models.CustomWidgetDO, error)
	// DeleteCustomWidget 删除自定义算子
	DeleteCustomWidget(ctx context.Context, id string) (string, error)
	// ListCustomWidgets 按给定条件查询自定义算子
	ListCustomWidgets(ctx context.Context, queryParam *dao.CustomWidgetQueryParam) ([]*models.CustomWidgetDO, error)
	// ListAllCustomWidgets  查询所有自定义算子,不限项目
	ListAllCustomWidgets(ctx context.Context) ([]*models.CustomWidgetDO, error)

	//UpsertTestWidget 创建或更新测试算子
	UpsertTestWidget(ctx context.Context, widget *widgets.Widget) error
	//ListTestWidgets 查询测试算子
	ListTestWidgets(ctx context.Context) ([]*widgets.Widget, error)
	//GetTestWidgetGroup 构造测试算子group
	GetTestWidgetGroup(ctx context.Context) (*models.WidgetGroup, error)
	//CleanTestWidgets 清除测试算子
	CleanTestWidgets(ctx context.Context) error
}

type WidgetImpl struct {
}

func (w *WidgetImpl) DeleteCustomWidget(ctx context.Context, ID string) (string, error) {
	if err := dao.CustomWidgetDAOImpl.DeleteByID(ctx, ID); err != nil {
		return "", err
	}
	return ID, nil
}

var MLOpsSvcStateToString = map[serving.MLOpsSvcState]string{
	serving.MLOpsSvcState_MLOPS_SVC_STATE_OFFLINE:   models.StoppedStatus,
	serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE: models.RunningStatus,
	serving.MLOpsSvcState_MLOPS_SVC_STATE_CREATING:  models.StartingStatus,
	serving.MLOpsSvcState_MLOPS_SVC_STATE_FAILED:    models.FailedStatus,
}

func (w *WidgetImpl) ListCustomWidgets(ctx context.Context,
	queryParam *dao.CustomWidgetQueryParam) ([]*models.CustomWidgetDO, error) {
	widgetPOs, err := dao.CustomWidgetDAOImpl.List(ctx, queryParam)
	if err != nil {
		return nil, err
	}

	// 呼叫serving，把服务列表中的State写入widgetPOs的status，用于展示自定义算子的服务的真实状态
	AllCustomWidgetSvcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_CUSTOM},
	})

	if err != nil {
		return nil, stderr.Wrap(err, "自定义算子列表获取失败")
	}

	imageIdToState := make(map[string]string)

	for _, svcInfo := range AllCustomWidgetSvcs.ServiceInfos {
		if svcInfo.SourceMeta != nil {
			if imageId, ok := svcInfo.SourceMeta.Extra[MLOpsCustomWidgetImageID]; ok {
				imageIdToState[imageId] = MLOpsSvcStateToString[svcInfo.State]
			}
		}
	}

	// 遍历 POs 并根据匹配更新状态
	for _, PO := range widgetPOs {
		if state, ok := imageIdToState[PO.ID]; ok {
			PO.Status = state
		}
	}

	res := make([]*models.CustomWidgetDO, len(widgetPOs))
	for idx, PO := range widgetPOs {
		widgetDO, err := models.CvtCustomWidgetPOToDO(PO)
		if err != nil {
			return nil, stderr.Wrap(err, "convert dynamic widget err")
		}
		res[idx] = widgetDO
	}
	return res, nil
}
func (w *WidgetImpl) ListAllCustomWidgets(ctx context.Context) ([]*models.CustomWidgetDO, error) {
	widgetPOs, err := dao.CustomWidgetDAOImpl.ListAll(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*models.CustomWidgetDO, len(widgetPOs))
	for idx, PO := range widgetPOs {
		widgetDO, err := models.CvtCustomWidgetPOToDO(PO)
		if err != nil {
			return nil, stderr.Wrap(err, "convert dynamic widget err")
		}
		res[idx] = widgetDO
	}
	return res, nil
}

func (w *WidgetImpl) GetCustomWidgetByID(ctx context.Context, id string) (*models.CustomWidgetDO, error) {
	widgetPO, err := dao.CustomWidgetDAOImpl.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	widget, err := models.CvtCustomWidgetPOToDO(widgetPO)
	if err != nil {
		return nil, stderr.Wrap(err, "get custom widget by id err")
	}
	return widget, nil
}

func (w *WidgetImpl) ListDynamicWidgetsByType(ctx context.Context, widgetType string) ([]*models.DynamicWidgetDesc, error) {
	svc, ok := DynamicWidgetSvcFactory[widgetType]
	if !ok {
		return nil, stderr.Internal.Error("no widget :%v", widgetType)
	}
	return svc.ListDynamicWidgets(ctx)
}

func (w *WidgetImpl) GetDynamicWidgetsByKey(ctx context.Context, widgetType string, widgetKey string) (*widgets.Widget, error) {
	svc, ok := DynamicWidgetSvcFactory[widgetType]
	if !ok {
		return nil, stderr.Internal.Error("no widget :%v", widgetType)
	}
	dynamicWidget, err := svc.GetWidget(ctx, widgetKey)
	if err != nil {
		return nil, err
	}
	for _, p := range dynamicWidget.Params {
		switch p.Category {
		case widgets.ParamTypeNodeInPort, widgets.ParamTypeNodeOutPort:
			if p.ParamLimits == nil {
				return nil, stderr.Errorf("the param limits of input or output is nil")
			}
		}
	}
	return dynamicWidget, nil
}

func (w *WidgetImpl) CreateCustomWidget(ctx context.Context, widget *models.CustomWidgetDO) (string, error) {
	// 同一项目下的自定义算子名称不能重复
	res, err := dao.CustomWidgetDAOImpl.List(ctx, &dao.CustomWidgetQueryParam{
		Name:      ptr.String(widget.Name),
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	if err != nil {
		return "", err
	}
	if len(res) != 0 {
		return "", stderr.Internal.Error("the name of widget cannot be duplicated under the same project")
	}
	PO, err := widget.ToPO(ctx)
	if err != nil {
		return "", err
	}
	return dao.CustomWidgetDAOImpl.Create(ctx, PO)
}

func (w *WidgetImpl) UpdateCustomWidget(ctx context.Context, id string, widget *models.CustomWidgetDO) (string, error) {
	customDAO := dao.CustomWidgetDAOImpl
	res, err := customDAO.List(ctx, &dao.CustomWidgetQueryParam{
		Name:      ptr.String(widget.Name),
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	if err != nil {
		return "", err
	}

	if len(res) > 1 {
		return "", stderr.Internal.Error("exist multiple customWidget with the same name under this project")
	}

	if len(res) == 1 && res[0].ID != id {
		return "", stderr.Internal.Error("the name of widget cannot be duplicated under the same project")
	}

	widgetPO, err := widget.ToPO(ctx)
	if err != nil {
		return "", err
	}
	err = customDAO.Update(ctx, id, widgetPO)
	if err != nil {
		return "", err
	}
	return id, nil
}

// func (w *WidgetImpl) RegisterDynamicWidget(ctx context.Context) error {
//	//if err := w.RegisterPromptWidgets(ctx); err != nil {
//	//	return err
//	//}
//	stdlog.Infof("register prompt widget success")
//	if err := w.updateLLMWidgetWidget(ctx); err != nil {
//		return err
//	}
//	stdlog.Infof("register llm widget success")
//	return nil
// }

func (w *WidgetImpl) updateLLMWidgetWidget(ctx context.Context) error {
	srvs, err := clients.MWHCli.ListModelServices(ctx)
	if err != nil {
		return stderr.Wrap(err, "list mw service error")
	}

	llmWidget, err := widgets.WidgetFactoryImpl.GetWidget(widgets.WidgetKeyLLM)
	if err != nil {
		return err
	}
	newParams := make([]widgets.WidgetParam, 0)
	for _, p := range llmWidget.Define().Params {
		if p.Define.Id == widgets.WidgetParamModelServer {
			p.Define.Datasource = widgets.ModelServiceToChainDataSources(srvs.LLMService)
		}
		newParams = append(newParams, p)
	}
	llmWidget.Define().Params = newParams

	vectorModelWidget, err := widgets.WidgetFactoryImpl.GetWidget(widgets.WidgetKeyVectorAIModel)
	if err != nil {
		return err
	}
	newParams = make([]widgets.WidgetParam, 0)
	for _, p := range vectorModelWidget.Define().Params {
		if p.Define.Id == widgets.WidgetParamModelServer {
			p.Define.Datasource = widgets.ModelServiceToChainDataSources(srvs.VectorService)
		}
		newParams = append(newParams, p)
	}
	vectorModelWidget.Define().Params = newParams
	return nil
}

// func (w *WidgetImpl) RegisterPromptWidgets(ctx context.Context) error {
//	promptTemplates, err := clients.PromptCli.ListChainPrompts(ctx)
//	if err != nil {
//		return err
//	}
//	for _, p := range promptTemplates {
//		w, err := p.ToWidget()
//		if err != nil {
//			return err
//		}
//		if err := widgets.WidgetFactoryImpl.RegisterOrUpdate(w); err != nil {
//			return err
//		}
//	}
//	return nil
// }

type WidgetGroupsMapInYaml map[string]WidgetGroupInYaml

type WidgetGroupInYaml struct {
	Name    string         `yaml:"name"`
	Desc    string         `yaml:"desc"`
	Widgets []WidgetInYaml `yaml:"widgets"`
}

type WidgetInYaml struct {
	Name   string        `yaml:"name"`
	Desc   string        `yaml:"desc"`
	Params []ParamInYaml `yaml:"params"`
}

type ParamInYaml struct {
	Name       string `yaml:"name"`
	Desc       string `yaml:"desc"`
	DataSource string `yaml:"dataSource,omitempty"`
}

const (
	enWidgetGroupsFilePath = "etc/i18n/en-widget-groups.json"
)

func (w *WidgetImpl) ListWidgetGroups(ctx context.Context) (ret []*models.WidgetGroup, err error) {
	if helper.IsChinese(ctx) {
		ret, err = w.ListOriWidgetGroups(ctx)
	} else {
		err = helper.ReadFileToStruct(enWidgetGroupsFilePath, &ret)
	}
	if err != nil {
		return nil, err
	}
	testWidgetGroup, err := w.GetTestWidgetGroup(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get test widget group")
	}
	if len(testWidgetGroup.Widgets) != 0 {
		ret = append(ret, testWidgetGroup)
	}
	return ret, nil
}

// ListOriWidgetGroups  WidgetFactory中存储的定义
func (w *WidgetImpl) ListOriWidgetGroups(ctx context.Context) ([]*models.WidgetGroup, error) {
	widgetGroups := make([]*models.WidgetGroup, 0)
	widgetMap := make(map[string][]*widgets.Widget)
	widgets.WidgetFactoryImpl.Range(func(w widgets.IWidget) {
		widget := w.Define()
		// 创建widget的副本，避免修改原始对象
		widgetCopy := *widget
		widgetMap[widget.Group] = append(widgetMap[widget.Group], &widgetCopy)
	})

	for groupKey, ws := range widgetMap {
		// ws排序
		sort.Slice(ws, func(i, j int) bool {
			return ws[i].Id < ws[j].Id
		})
		desc, ok := widgets.WidgetGroupDefines[groupKey]
		if !ok {
			return nil, stderr.Internal.Errorf("group key :%v not exists", groupKey)
		}
		widgetGroups = append(widgetGroups, &models.WidgetGroup{
			Id:       desc.ID,
			Name:     desc.Name,
			Desc:     desc.Desc,
			SortFlag: desc.SortFlag,
			Widgets:  ws,
		})
	}

	sort.Slice(widgetGroups, func(i, j int) bool {
		if widgetGroups[i].SortFlag < widgetGroups[j].SortFlag {
			return true
		}
		return false
	})
	return widgetGroups, nil
}

// 存储测试算子,通过算子id进行区分  widgetKey -> *widget
var testWidgetsMap *clients2.RedisMap[widgets.Widget]

func InitTestWidgets() {
	testWidgetsMap = clients2.NewRedisMap[widgets.Widget](clients.RedisCli, RedisKeyTestWidgets)
}

// UpsertTestWidget 创建或更新测试算子
func (w *WidgetImpl) UpsertTestWidget(ctx context.Context, widget *widgets.Widget) error {
	if widget == nil || widget.Id == "" {
		return stderr.Errorf("the widget is nil or id is empty")
	}
	return testWidgetsMap.Set(widget.Id, widget)
}

// ListTestWidgets 查询测试算子
func (w *WidgetImpl) ListTestWidgets(ctx context.Context) ([]*widgets.Widget, error) {
	res := make([]*widgets.Widget, 0)
	err := testWidgetsMap.Range(func(key string, value *widgets.Widget) bool {
		res = append(res, value)
		return true
	})
	return res, err
}

func (w *WidgetImpl) GetTestWidgetGroup(ctx context.Context) (*models.WidgetGroup, error) {
	testWidgets, err := w.ListTestWidgets(ctx)
	if err != nil {
		return nil, err
	}
	define, exist := widgets.WidgetGroupDefines[widgets.WidgetGroupTestWidgets]
	if !exist {
		return nil, stderr.Errorf("can not find widgets group define for key %s", widgets.WidgetGroupTestWidgets)
	}

	return &models.WidgetGroup{
		Id:       define.ID,
		Name:     define.Name,
		Desc:     define.Desc,
		SortFlag: define.SortFlag,
		Widgets:  testWidgets,
	}, nil
}

// CleanTestWidgets 清除测试算子
func (w *WidgetImpl) CleanTestWidgets(ctx context.Context) error {
	return testWidgetsMap.Clear()
}
