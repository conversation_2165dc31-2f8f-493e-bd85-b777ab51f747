package applet

import (
	"context"
	"os"
	"path/filepath"
	"sort"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	zhBasePath                 = "etc/i18n/chain-templates/zh"
	enBasePath                 = "etc/i18n/chain-templates/en"
	assistantDebugFile         = "0-assistant-debug.json"
	assistantTemplateFile      = "1-assistant-template.json"
	enCacheKeyBuiltInTemplates = "enCacheKeyBuiltInTemplates"
	zhCacheKeyBuiltInTemplates = "zhCacheKeyBuiltInTemplates"
)

var (
	cachedBuiltInTemplates = stdsrv.NewStdCache[[]*models.ChainTemplate](30*time.Minute, 15*time.Minute)
)

type ChainTemplate interface {
	ListAllTemplates(ctx context.Context, simple bool) ([]*models.ChainTemplate, error)
	GetTemplateById(ctx context.Context, tId string) (*models.ChainTemplate, error)
	ListTemplateGroups(ctx context.Context, simple bool) ([]*models.TemplateGroup, error)
	// GetAssistantTemplate 获取助手模板及相应的调试参数
	GetAssistantTemplate(ctx context.Context) (*models.ChainTemplate, *models.ChainParam, error)
}

type ChainTemplateImpl struct {
}

func (ctm ChainTemplateImpl) GetAssistantTemplate(ctx context.Context) (*models.ChainTemplate, *models.ChainParam, error) {
	basePath := zhBasePath
	if !helper.IsChinese(ctx) {
		basePath = enBasePath
	}
	chainDebugReq := new(models.ChainDebugReq)
	chainTemplate := new(models.ChainTemplate)
	if err := helper.ReadFileToStruct(filepath.Join(basePath, assistantDebugFile), chainDebugReq); err != nil {
		return nil, nil, stderr.Wrap(err, "read chain debug template failed")
	}
	if err := helper.ReadFileToStruct(filepath.Join(basePath, assistantTemplateFile), chainTemplate); err != nil {
		return nil, nil, stderr.Wrap(err, "read chain template failed")
	}

	chainParamDemo, err := chainDebugReq.ToChainParam(chainTemplate.Template)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "cvt chain debug template to chain param")
	}
	return chainTemplate, chainParamDemo, nil
}

func (ctm ChainTemplateImpl) GetTemplateById(ctx context.Context, tId string) (*models.ChainTemplate, error) {
	templates, err := ctm.ListAllTemplates(ctx, false)
	if err != nil {
		return nil, err
	}
	for _, t := range templates {
		if t.ID == tId {
			return t, nil
		}
	}
	return nil, stderr.Errorf("can not find result with id: %s", tId)
}

func (ctm ChainTemplateImpl) ListTemplateGroups(ctx context.Context, simple bool) ([]*models.TemplateGroup, error) {
	groupsDesc := models.GetBuiltInGroupDesc(ctx)
	templates, err := ctm.ListAllTemplates(ctx, simple)
	if err != nil {
		return nil, err
	}
	key2Templates := make(map[models.TemplateGroupKey][]*models.ChainTemplate)
	for _, t := range templates {
		key2Templates[t.TemplateGroupKey] = append(key2Templates[t.TemplateGroupKey], t)
	}
	ret := make([]*models.TemplateGroup, 0)
	for k, v := range key2Templates {
		gDesc, ok := groupsDesc[k]
		if !ok {
			return nil, stderr.Errorf("can not find template group dec with key %s", k)
		}
		ret = append(ret, &models.TemplateGroup{
			TemplateGroupDesc: gDesc,
			Templates:         v,
		})
	}
	sort.Slice(ret, func(i, j int) bool {
		if ret[i].SortFlag < ret[j].SortFlag {
			return true
		}
		return false
	})
	return ret, nil
}
func (ctm ChainTemplateImpl) ListAllTemplates(ctx context.Context, simple bool) ([]*models.ChainTemplate, error) {
	templates, err := ctm.ListBuiltInTemplates(ctx)
	if err != nil {
		return nil, err
	}
	if !simple {
		return templates, nil
	}

	simpleTemp, _ := helper.DeepCopy(templates)
	for _, t := range simpleTemp {
		t.Template = nil
	}
	return simpleTemp, nil
}
func (ctm ChainTemplateImpl) ListBuiltInTemplates(ctx context.Context) ([]*models.ChainTemplate, error) {
	basePath := zhBasePath
	cacheKey := zhCacheKeyBuiltInTemplates
	if !helper.IsChinese(ctx) {
		basePath = enBasePath
		cacheKey = enCacheKeyBuiltInTemplates
	}
	templates, found := cachedBuiltInTemplates.Get(cacheKey)
	if found {
		return templates, nil
	}
	start := time.Now()
	res := make([]*models.ChainTemplate, 0)
	files, err := os.ReadDir(basePath)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to open dir: %s", basePath)
	}
	for _, f := range files {
		if f.IsDir() || f.Name() == assistantDebugFile {
			continue
		}
		ct := new(models.ChainTemplate)
		fPath := filepath.Join(basePath, f.Name())
		if err := helper.ReadFileToStruct(fPath, ct); err != nil {
			return nil, stderr.Wrap(err, "the file is valid template: %s", fPath)
		}
		ct.ID = f.Name()
		res = append(res, ct)
	}
	elapsed := time.Since(start)
	stdlog.Infof("cost %v to read templates from files", elapsed)

	for _, t := range res {
		t.MdFullDesc, err = models.GenTemplateMdDesc(ctx, t)
		if err != nil {
			return nil, err
		}
	}
	cachedBuiltInTemplates.Set(cacheKey, res)
	return res, nil
}
