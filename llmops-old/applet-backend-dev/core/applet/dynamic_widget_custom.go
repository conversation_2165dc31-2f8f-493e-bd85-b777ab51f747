package applet

import (
	"context"
	"fmt"
	"github.com/aws/smithy-go/ptr"
	"strconv"
	"strings"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/aip/llmops-common/pb/serving"
	servingState "transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const (
	MLOpsCustomWidgetImageID = "image_id"
)

type WidgetCustomSvc struct {
}

// ListDynamicWidgets 查询Running中的自定义算子,并转为WidgetDesc
func (w WidgetCustomSvc) ListDynamicWidgets(ctx context.Context) ([]*models.DynamicWidgetDesc, error) {
	projectID := helper.GetProjectID(ctx)
	param := &dao.CustomWidgetQueryParam{
		//Status: ptr.String(models.RunningStatus),
	}
	if projectID != "" {
		param.ProjectID = ptr.String(projectID)
	}
	POs, err := dao.CustomWidgetDAOImpl.List(ctx, param)
	if err != nil {
		return nil, stderr.Wrap(err, "dao.CustomWidgetDAOImpl failed to List CustomWidgets")
	}

	// 由于使用serving起服务 查询服务状态，不再使用 dao.CustomWidgetDAOImpl.Update 同步算子状态，
	// 提取所有 自定义的&&状态为Available的服务 的imageID，用来筛选可用的自定义算子
	AvailableCustomSvcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_CUSTOM},
		States:      []string{string(servingState.MLOpsSvcStateAvailable)},
	})

	if err != nil {
		return nil, stderr.Wrap(err, "clients.MLOpsCli.Cli 获取“状态为Available”的“自定义”的服务失败")
	}

	filteredPOs, err := filterPOs(AvailableCustomSvcs.ServiceInfos, POs)
	if err != nil {
		return nil, stderr.Wrap(err, "filterPOs执行失败")
	}

	desc := models.BatchCvtDynamicToWidgetDesc(filteredPOs)
	return desc, nil
}

func filterPOs(AvailableCustomWidgetSvcs []*serving.MLOpsServiceBaseInfo, POs []*generated.CustomWidget) ([]*generated.CustomWidget, error) {
	validImageIDs := make(map[string]struct{})
	for _, svc := range AvailableCustomWidgetSvcs {
		imageID := svc.SourceMeta.Extra[MLOpsCustomWidgetImageID]
		if imageID != "" {
			validImageIDs[imageID] = struct{}{}
		}
	}

	// 筛选 POs
	filteredPOs := []*generated.CustomWidget{}
	for _, po := range POs {
		if _, exists := validImageIDs[po.ID]; exists {
			filteredPOs = append(filteredPOs, po)
		}
	}

	return filteredPOs, nil
}

func (w WidgetCustomSvc) GetWidget(ctx context.Context, widgetKey string) (*widgets.Widget, error) {
	param := &dao.CustomWidgetQueryParam{
		ID: ptr.String(widgetKey),
	}
	projectID := helper.GetProjectID(ctx)
	if projectID != "" {
		param.ProjectID = ptr.String(projectID)
	}
	POs, err := dao.CustomWidgetDAOImpl.List(ctx, param)
	if err != nil {
		return nil, err
	}
	if len(POs) != 1 {
		return nil, stderr.Internal.Error("get widget :%v err", widgetKey)
	}
	funcCall, err := models.CvtCustomWidgetPOToDO(POs[0])
	if err != nil {
		return nil, stderr.Wrap(err, "convert dynamic widget err")
	}
	virtualSvcUrl, err := GetVirtualSvcUrl(ctx, funcCall.ID)
	if err != nil {
		return nil, stderr.Wrap(err, "get virtual service url err")
	}
	return funcCall.ToWidget(ctx, clients.MLOpsCli.GetIstioAddr(), virtualSvcUrl)
}

func GetVirtualSvcUrl(ctx context.Context, widgetID string) (string, error) {
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: buildQueryImageMetaInfo(widgetID),
	})

	if err != nil {
		return "", stderr.Wrap(err, "无法获取自定义算子的服务的 virtualSvcUrl")
	}

	if len(svcs.ServiceInfos) != 1 {
		return "", fmt.Errorf("serving 返回服务数量非预期。预期数量为1")
	}

	svcInfo := svcs.ServiceInfos[0]

	if svcInfo == nil || svcInfo.VirtualSvcUrl == "" {
		return "", fmt.Errorf("自定义算子的服务为空，或者其 virtualSvcUrl 为空")
	}

	return svcInfo.VirtualSvcUrl, nil
}

func (w WidgetCustomSvc) StartCustomWidget(ctx context.Context, widgetID string) (string, error) {
	widgetDO, err := WidgetManager.GetCustomWidgetByID(ctx, widgetID)
	if err != nil {
		return "", err
	}

	svc, err := w.getSvcByImgID(ctx, widgetID)
	if err != nil {
		return "", err
	}

	existSvc := false
	if svc != nil {
		existSvc = true
	}

	imgSvcBaseInfo, err := buildMLopsSvcBaseInfoForImg(widgetDO)
	if err != nil {
		return "", err
	}

	svcID := ""
	if !existSvc {
		// 创建服务
		imgIdInfo, err := clients.MLOpsCli.Cli.CreateService(ctx, imgSvcBaseInfo)
		if err != nil {
			return "", err
		}
		svcID = imgIdInfo.Id
	} else {
		svcID = svc.ServiceInfo.Id
	}

	versionDetail, err := buildMLopsSvcVersionInfoForImg(widgetDO)
	if err != nil {
		return "", stderr.Wrap(err, "build version info err")
	}
	svcVersionInfo := &serving.ServiceIDVersionInfo{
		ServiceId:          svcID,
		ServiceVersionInfo: versionDetail,
	}

	versionIDInfo, err := clients.MLOpsCli.Cli.CreateServiceVersion(ctx, svcVersionInfo)
	if err != nil {
		return "", stderr.Wrap(err, "create service version err")
	}

	// 更新服务版本权重
	strategy := serving.DeployStrategy_DEPLOY_STRATEGY_MAIN_DEPLOY
	imgSvcBaseInfo.DeployCfg = &serving.DeployCfg{
		DeployStrategy:    &strategy,
		MainDeployVersion: versionIDInfo.ServiceVersionId,
	}
	imgSvcBaseInfo.Id = svcID
	_, err = clients.MLOpsCli.Cli.UpdateService(ctx, imgSvcBaseInfo)

	if _, err = clients.MLOpsCli.Cli.Deploy(ctx, &serving.ServiceID{Id: svcID}); err != nil {
		return "", err
	}

	return svcID, nil
}

func (w WidgetCustomSvc) getSvcByImgID(ctx context.Context, imageID string) (*serving.ServiceInfo, error) {
	svcInfo, err := w.getSvcBaseByImgID(ctx, imageID)
	if err != nil {
		return nil, err
	}
	if svcInfo == nil {
		return nil, err
	}
	svc, err := clients.MLOpsCli.Cli.QueryByID(ctx, &serving.ServiceID{Id: svcInfo.Id})
	if err != nil {
		return nil, err
	}
	return svc, nil
}

func (w WidgetCustomSvc) getSvcBaseByImgID(ctx context.Context, imageID string) (*serving.MLOpsServiceBaseInfo, error) {
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: map[string]string{
			MLOpsCustomWidgetImageID: imageID,
		},
	})
	if err != nil {
		return nil, err
	}
	if len(svcs.ServiceInfos) == 0 {
		return nil, nil
	}
	if len(svcs.ServiceInfos) > 1 {
		return nil, stderr.Internal.Error("too many service by tag [%s:%s]",
			MLOpsCustomWidgetImageID, imageID)
	}
	return svcs.ServiceInfos[0], nil
}

func buildMLopsSvcBaseInfoForImg(customWidgetDO *models.CustomWidgetDO) (*serving.MLOpsServiceBaseInfo, error) {
	serviceName := customWidgetDO.DeployInfo.ServiceName
	baseInfo := &serving.MLOpsServiceBaseInfo{
		Name:       fmt.Sprintf("%s", serviceName),
		SourceType: serving.SourceType_SOURCE_TYPE_CUSTOM,
		Cluster:    "default",
		//Apis: []*serving.API{
		//	{
		//		Port: uint32(customWidgetDO.Port),
		//		Type: "http",
		//		Url:  []string{"/"},
		//	},
		//},
		Endpoints: []*common.Endpoint{
			{
				Port: uint32(customWidgetDO.Port),
				Type: common.EndpointType_ENDPOINT_TYPE_HTTP,
				ApiAttrs: []*common.APIAttr{
					{
						ApiPath: "/",
						Method:  common.HttpMethod_HTTP_METHOD_POST,
						ApiType: common.APIType_API_TYPE_OTHERS,
					},
				},
				IsDefault: true,
			},
		},
		LimitTime: DefaultDeployTimeNoLimit,
		BillingConfig: &common.BillingConfig{
			Type: common.BillingConfig_BY_TOKEN,
		},
		IsRollUpdate: false,
		GuardrailsConfig: &serving.GuardrailsConfig{
			IsSecurity: false,
		},
		ProjectId:  customWidgetDO.ProjectID,
		SourceMeta: &serving.SourceMeta{Extra: buildQueryImageMetaInfo(customWidgetDO.ID)},
	}
	return baseInfo, nil
}

func buildQueryImageMetaInfo(imageID string) map[string]string {
	return map[string]string{
		MLOpsCustomWidgetImageID: imageID,
	}
}

func buildMLopsSvcVersionInfoForImg(customWidgetDO *models.CustomWidgetDO) (*serving.MLOpsServiceVersionInfo, error) {
	customWidgetID := customWidgetDO.ID
	customWidgetName := customWidgetDO.Name

	cpuRequestStr, err := GetCpuMillicoresString(conf.Config.ChainDeployCfg.Resources().CpuRequest)
	if err != nil {
		return nil, err
	}

	cpuLimitStr, err := GetCpuMillicoresString(conf.Config.ChainDeployCfg.Resources().CpuLimit)
	if err != nil {
		return nil, err
	}

	memRequestStr, err := GetMemString(conf.Config.ChainDeployCfg.Resources().MemoryRequest)
	if err != nil {
		return nil, err
	}

	memLimitStr, err := GetMemString(conf.Config.ChainDeployCfg.Resources().MemoryLimit)
	if err != nil {
		return nil, err
	}

	svcVersionInfo := &serving.MLOpsServiceVersionInfo{
		SourceId: customWidgetID,
		Name:     customWidgetName,
		NodeChooseCfg: &serving.NodeChooseCfg{
			Strategy: serving.NodeChooseStrategy_NODE_CHOOSE_STRATEGY_RANDOM,
		},
		HpaCfg: &serving.HPACfg{
			Strategy: serving.HPAStrategy_HPA_STRATEGY_DISABLED,
			Replicas: 1,
		},
		Containers: []*serving.MLOpsContainer{
			{
				Image:         customWidgetDO.ImageInfo.ImageUrl,
				ImageType:     serving.ImageType_IMAGE_TYPE_CUSTOM,
				ContainerType: serving.ContainerType_CONTAINER_TYPE_MAIN,
				Resource: &serving.Resource{
					CpuRequest:    cpuRequestStr,
					MemoryRequest: memRequestStr,
					CpuLimit:      cpuLimitStr,
					MemoryLimit:   memLimitStr,
				},
			},
		},
	}
	return svcVersionInfo, nil
}

func GetCpuMillicoresString(cpuCores string) (string, error) {
	// 计算 cpu 微核数， 并格式化为 "Xm" 的字符串
	cpuCoresFloat, err := strconv.ParseFloat(cpuCores, 64)

	if err != nil {
		return "", stderr.Wrap(err, "配置文件cpu核数不合法，无法转换成float类型的cpu核数")
	}

	return fmt.Sprintf("%dm", int64(cpuCoresFloat*1000.0)), nil
}

func GetMemString(input string) (string, error) {
	// 检查输入是否以 "m" 结尾
	if !strings.HasSuffix(input, "m") {
		return "", fmt.Errorf("配置文件输入错误: %s, 需要以 'm' 结尾", input)
	}
	// 去掉 "m" 并尝试转换为整数
	numberStr := strings.TrimSuffix(input, "m")
	number, err := strconv.Atoi(numberStr)
	if err != nil {
		return "", fmt.Errorf("配置文件资源输入不合法: %s", input)
	}
	// 拼接 "Mi" 并返回
	return fmt.Sprintf("%dMi", number), nil
}

func (w WidgetCustomSvc) StopCustomWidget(ctx context.Context, widgetID string) (string, error) {
	svc, err := w.getSvcByImgID(ctx, widgetID)
	if err != nil {
		return "", err
	}

	var svcID string

	if svc != nil {
		svcID = svc.ServiceInfo.Id
	} else {
		return "获取不到服务信息，服务不存在", nil
	}

	_, err = clients.MLOpsCli.Cli.Offline(ctx, &serving.ServiceID{Id: svcID})

	if err != nil {
		return "", err
	}

	return svcID, nil
}
