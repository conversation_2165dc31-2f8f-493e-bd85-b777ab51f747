package applet

//
//type InputAggregation struct {
//}
//
//func (i InputAggregation) GetWidget(ctx context.Context, params []string) (*widgets.Widget, error) {
//	widgetParams := make([]widgets.WidgetParam, 0)
//	for _, p := range params {
//		widgetParams = append(widgetParams, widgets.WidgetParam{
//			DataClass:   widgets.DataClassString,
//			Category:    widgets.ParamTypeNodeInPort,
//			ParamLimits: widgets.AnyAnyLimits(),
//			Define: widgets.DynamicParam{
//				Id:       p,
//				Name:     p,
//				Desc:     p,
//				Required: true,
//			},
//		})
//	}
//	// 添加output
//	widgetParams = append(widgetParams, widgets.WidgetParam{
//		DataClass:   widgets.DataClassString,
//		Category:    widgets.ParamTypeNodeOutPort,
//		ParamLimits: widgets.AnyAnyLimits(),
//		Define: widgets.DynamicParam{
//			Id: "OutPut",
//		},
//	})
//	return &widgets.Widget{
//		Id:             uuid.New().String(),
//		Name:           "数据合并",
//		Desc:           "用于将多个数据输入进行合并",
//		Group:          widgets.WidgetGroupChainTool,
//		Dynamic:        true,
//		OriWidgetKey:   widgets.WidgetKeyInputAggregation,
//		Params:         widgetParams,
//		DynamicEndPoint: true,
//	}, nil
//}
