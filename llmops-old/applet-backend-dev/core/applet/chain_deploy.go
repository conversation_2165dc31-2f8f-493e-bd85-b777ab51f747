package applet

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"transwarp.io/aip/llmops-common/pb/serving"
	clients2 "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

const (
	MLOpsSvcTagChainIDKey        = "chain_id"
	MLOpsSvcTagExperienceKey     = "chain_type"
	MLOpsSvcTagExperienceValue   = "chain_experience"
	DefaultDeployTimeLimit12Hour = 60 * 60 * 12
	DefaultDeployTimeNoLimit     = -1
	ChainTickScriptEnv           = "TICK_SCRIPT"
	ChainTickScriptPathEnv       = "TICK_SCRIPT_PATH"
	SfsPvcMountPath              = "/sfs"
	//ChainSourceMetaTickScript    = "tick_script"
)

type ChainState struct {
	ChainID           string                // 应用链ID
	ServiceID         string                // 对应的mlops service的id
	ServiceName       string                // 服务名称
	VirtualUrl        string                // 服务虚拟地址
	ChainOnlineState  serving.MLOpsSvcState // 应用链上下线状态，online&offline
	ChainHealthyState *health.ChainHealth   // 应用链健康状态，若应用链为上线才有此状态
	ChainStateInfo    *models.StateInfo
}

type ChainDeployer interface {
	// Deploy 部署应用链&更新上线中的应用链
	Deploy(ctx context.Context, chainID string, casInfo *models.CasApprovalInfo) (string, error)
	DeployCasCallBack(ctx context.Context, serviceID string, approvalState models.ApprovalState) error
	// Offline 下线应用链
	Offline(ctx context.Context, chainID string, casInfo *models.CasApprovalInfo) (string, error)
	// ListOnlineChains 获取所有可用/创建中的应用链，返回应用链ID(应用体验页面列表）
	ListOnlineChains(ctx context.Context) ([]string, error)
	// GetTotalState 获取应用链的状态
	GetTotalState(ctx context.Context, chainID string) (*ChainState, error)
	GetSimpleState(ctx context.Context, chainID string) (*ChainState, error)
	listTotalStateAsMap(ctx context.Context) (map[string]*ChainState, error)
	ListSimpleStateAsMap(ctx context.Context) (map[string]*ChainState, error)
	ListTotalStateByIDs(ctx context.Context, chainIds []string) (map[string]*ChainState, error)
	ListCachedTotalState(ctx context.Context) (map[string]*ChainState, error)
}

type MLOpsChainDeploy struct {
}

func buildQueryChainMetaInfo(chainID string) map[string]string {
	return map[string]string{
		MLOpsSvcTagChainIDKey:    chainID,
		MLOpsSvcTagExperienceKey: MLOpsSvcTagExperienceValue,
	}
}

func buildListChainMetaInfo() map[string]string {
	return map[string]string{
		MLOpsSvcTagExperienceKey: MLOpsSvcTagExperienceValue,
	}
}
func (m MLOpsChainDeploy) getSvcBaseByChainID(ctx context.Context, chainID string) (*serving.MLOpsServiceBaseInfo, error) {
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: map[string]string{
			MLOpsSvcTagChainIDKey:    chainID,
			MLOpsSvcTagExperienceKey: MLOpsSvcTagExperienceValue,
		},
	})
	if err != nil {
		return nil, err
	}
	if len(svcs.ServiceInfos) == 0 {
		return nil, nil
	}
	if len(svcs.ServiceInfos) > 1 {
		return nil, stderr.Internal.Error("too many service by tag [%s:%s] and [%s:%s]",
			MLOpsSvcTagExperienceKey, MLOpsSvcTagExperienceValue, MLOpsSvcTagChainIDKey, chainID)
	}
	return svcs.ServiceInfos[0], nil
}

func (m MLOpsChainDeploy) DeployCasCallBack(ctx context.Context, serviceID string, approvalState models.ApprovalState) error {
	state := serving.ApprovalState_MLOpsSvcStateApprovalPassed
	if approvalState == models.ApprovalStateRejected {
		state = serving.ApprovalState_MLOpsSvcStateApprovalRejected
	}
	stdlog.Infof("call back from cas")
	// 更新状态
	if _, err := clients.MLOpsCli.Cli.UpdateApprovalState(ctx, &serving.UpdateApprovalStateReq{
		ServiceId:     serviceID,
		ApprovalState: state,
	}); err != nil {
		return err
	}
	if state == serving.ApprovalState_MLOpsSvcStateApprovalPassed {
		// 部署
		if _, err := clients.MLOpsCli.Cli.Deploy(ctx, &serving.ServiceID{Id: serviceID}); err != nil {
			return err
		}
	}
	return nil
}

func (m MLOpsChainDeploy) getSvcByChainID(ctx context.Context, chainID string) (*serving.ServiceInfo, error) {
	svcInfo, err := m.getSvcBaseByChainID(ctx, chainID)
	if err != nil {
		return nil, err
	}
	if svcInfo == nil {
		return nil, nil
	}
	svc, err := clients.MLOpsCli.Cli.QueryByID(ctx, &serving.ServiceID{Id: svcInfo.Id})
	if err != nil {
		return nil, err
	}
	return svc, nil
}

func buildMLopsSvcBaseInfo(deployCfg *models.ChainDeployCfg) (*serving.MLOpsServiceBaseInfo, error) {
	chainID := deployCfg.ChainID
	chainName := deployCfg.ChainDetail.Base.Name
	baseInfo := &serving.MLOpsServiceBaseInfo{
		Name:       fmt.Sprintf("Chain-%s-%v", chainName, time.Now().Unix()),
		SourceType: serving.SourceType_SOURCE_TYPE_APP_CUBE,
		Cluster:    "default",
		//Apis:       deployCfg.ApiInfos,
		Endpoints:  deployCfg.EndPoints,
		SourceMeta: &serving.SourceMeta{Extra: buildQueryChainMetaInfo(chainID)},
		LimitTime:  DefaultDeployTimeNoLimit,
	}
	return baseInfo, nil
}

func buildMLopsSvcVersionInfo(ctx context.Context, deployCfg *models.ChainDeployCfg) (*serving.MLOpsServiceVersionInfo, error) {
	chainID := deployCfg.ChainID
	chainName := deployCfg.ChainDetail.Base.Name
	mounts := make([]*serving.MountCfg, 0)
	for _, m := range deployCfg.PVCMountCfg {
		mounts = append(mounts, &serving.MountCfg{
			VolumeCfg: &serving.VolumeCfg{
				FileType: serving.MLOpsFileType_MLOPS_File_TYPE_FILE_SYSTEM,
				Path:     m.PVCSubPath,
			},
			MountPaths: []*serving.MountPath{
				{
					MountPath: m.MountPath,
				},
			},
		})
	}
	if deployCfg.ChainDetail == nil {
		return nil, stderr.Internal.Error("build service version err,no chain detail")
	}

	cpuRequestStr, err := GetCpuMillicoresString(conf.Config.ChainDeployCfg.Resources().CpuRequest)
	if err != nil {
		return nil, err
	}

	cpuLimitStr, err := GetCpuMillicoresString(conf.Config.ChainDeployCfg.Resources().CpuLimit)
	if err != nil {
		return nil, err
	}

	memRequestStr, err := GetMemString(conf.Config.ChainDeployCfg.Resources().MemoryRequest)
	if err != nil {
		return nil, err
	}

	memLimitStr, err := GetMemString(conf.Config.ChainDeployCfg.Resources().MemoryLimit)
	if err != nil {
		return nil, err
	}

	svcVersionInfo := &serving.MLOpsServiceVersionInfo{
		SourceId: chainID,
		Name:     chainName,
		NodeChooseCfg: &serving.NodeChooseCfg{
			Strategy: serving.NodeChooseStrategy_NODE_CHOOSE_STRATEGY_RANDOM,
		},
		HpaCfg: &serving.HPACfg{
			Strategy: serving.HPAStrategy_HPA_STRATEGY_DISABLED,
			Replicas: 1,
		},
		Containers: []*serving.MLOpsContainer{
			{
				Image:         deployCfg.Image,
				ImageType:     serving.ImageType_IMAGE_TYPE_CUSTOM,
				ContainerType: serving.ContainerType_CONTAINER_TYPE_MAIN,
				Resource: &serving.Resource{
					CpuRequest:    cpuRequestStr,
					MemoryRequest: memRequestStr,
					CpuLimit:      cpuLimitStr,
					MemoryLimit:   memLimitStr,
				},
				Envs:     deployCfg.Envs,
				MountCfg: mounts,
			},
		},
		SourceMeta: &serving.SourceMeta{
			Extra: map[string]string{
				"chain_snapshot_id": deployCfg.ChainSnapshotId,
			},
		},
	}
	return svcVersionInfo, nil
}

func (m MLOpsChainDeploy) Deploy(ctx context.Context, chainID string, casInfo *models.CasApprovalInfo) (string, error) {
	deployCfg, err := ChainManager.GetDeployCfg(ctx, chainID)
	if err != nil {
		return "", err
	}
	if deployCfg.ChainDetail.ChainDetail == nil {
		return "", stderr.AppletChainParamInvalidError.Error("no chain detail")
	}
	svc, err := m.getSvcByChainID(ctx, chainID)
	if err != nil {
		return "", err
	}
	existSvc := false
	if svc != nil {
		existSvc = true
	}
	svcBaseInfo, err := buildMLopsSvcBaseInfo(deployCfg)
	if err != nil {
		return "", err
	}
	svcID := ""
	if !existSvc {
		// 创建服务
		IDInfo, err := clients.MLOpsCli.Cli.CreateService(ctx, svcBaseInfo)
		if err != nil {
			return "", err
		}
		svcID = IDInfo.Id
	} else {
		svcID = svc.ServiceInfo.Id
	}

	// 创建服务版本
	versionDetail, err := buildMLopsSvcVersionInfo(ctx, deployCfg)
	if err != nil {
		return "", stderr.Wrap(err, "build version info err")
	}
	svcVersionInfo := &serving.ServiceIDVersionInfo{
		ServiceId:          svcID,
		ServiceVersionInfo: versionDetail,
	}
	versionIDInfo, err := clients.MLOpsCli.Cli.CreateServiceVersion(ctx, svcVersionInfo)
	if err != nil {
		return "", stderr.Wrap(err, "create service version err")
	}
	// 更新服务版本权重
	strategy := serving.DeployStrategy_DEPLOY_STRATEGY_MAIN_DEPLOY
	svcBaseInfo.DeployCfg = &serving.DeployCfg{
		DeployStrategy:    &strategy,
		MainDeployVersion: versionIDInfo.ServiceVersionId,
	}
	svcBaseInfo.Id = svcID
	_, err = clients.MLOpsCli.Cli.UpdateService(ctx, svcBaseInfo)
	if err != nil {
		return "", err
	}
	// 如果开启了审批 需要1.创建审批记录，2.更新服务状态为审批中
	if casInfo.EnabledApproval {
		token, err := helper.GetToken(ctx)
		if err != nil {
			return "", err
		}
		httpCallbackReq := helper.ID{
			ID: svcID,
		}
		body, err := json.Marshal(httpCallbackReq)
		if err != nil {
			return "", err
		}
		bodyQuota := json.RawMessage(strconv.Quote(string(body)))
		httpCallBackInfo := &examine.HttpInfo{
			Body:   bodyQuota,
			Url:    fmt.Sprintf("%s?project_id=%s", conf.Config.ChainDeployCfg.CallBackBaseUrl, helper.GetProjectID(ctx)),
			Method: http.MethodPost,
			Token:  token,
		}
		objBytes, err := json.Marshal(casInfo.Examination.Object)
		if err != nil {
			return "", err
		}
		_, err = examine.CreateFlow(helper.GetProjectID(ctx), token, &examine.CreateExamineFlowReq{
			Object: objBytes,
			Module: casInfo.Examination.Module,
			Config: &examine.ExamineBindingConfig{
				FlowName: "base_project_flow",
				Type:     "发布应用",
			},
			Detail:         casInfo.Examination.Detail,
			HttpInfo:       httpCallBackInfo,
			RejectHttpInfo: httpCallBackInfo,
		})
		if err != nil {
			return "", err
		}
		if _, err := clients.MLOpsCli.Cli.UpdateApprovalState(ctx, &serving.UpdateApprovalStateReq{
			ServiceId:     svcBaseInfo.Id,
			ApprovalState: serving.ApprovalState_MLOpsSvcStateUnderApproval,
		}); err != nil {
			return "", err
		}
	} else {
		// 部署
		if _, err = clients.MLOpsCli.Cli.Deploy(ctx, &serving.ServiceID{Id: svcID}); err != nil {
			return "", err
		}
	}
	return svcID, nil
}

func (m MLOpsChainDeploy) Offline(ctx context.Context, chainID string, casInfo *models.CasApprovalInfo) (string, error) {
	svc, err := m.getSvcBaseByChainID(ctx, chainID)
	if err != nil {
		return "", err
	}
	if svc == nil {
		stdlog.Warnf("no service by chain :%v", chainID)
		return chainID, err
	}
	if _, err := clients.MLOpsCli.Cli.Offline(ctx, &serving.ServiceID{Id: svc.Id}); err != nil {
		return "", err
	}
	return svc.Id, nil
}

func (m MLOpsChainDeploy) ListOnlineChains(ctx context.Context) ([]string, error) {
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: buildListChainMetaInfo(),
	})
	if err != nil {
		return nil, err
	}
	onlineChainIDs := make([]string, 0)
	for _, s := range svcs.ServiceInfos {
		if s.State != serving.MLOpsSvcState_MLOPS_SVC_STATE_CREATING &&
			s.State != serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
			continue
		}
		if s.SourceMeta == nil || s.SourceMeta.Extra == nil {
			return nil, stderr.Internal.Error("svc :%v no source meta", s.Id)
		}
		extra := s.SourceMeta.Extra
		if chainID, ok := extra[MLOpsSvcTagChainIDKey]; !ok {
			return nil, stderr.Internal.Error("svc source meta no chain id key")
		} else {
			onlineChainIDs = append(onlineChainIDs, chainID)
		}
	}
	return onlineChainIDs, nil
}

type ChainIDInfo struct {
	ChainID       string
	MLOpsSvcID    string
	VirtualSvcUrl string
}

// listTotalState project_id存储在ctx中, map: chainId -> state
// 查询已发布的应用,并通过应用链health接口,为其检查健康状态。较为耗时,使用ListCachedTotalState
func (m MLOpsChainDeploy) listTotalStateAsMap(ctx context.Context) (map[string]*ChainState, error) {
	resMap, err := m.ListSimpleStateAsMap(ctx)
	if err != nil {
		return nil, err
	}
	// 补充health信息
	tasks := make([]func() error, 0)
	for _, chainState := range resMap {
		if chainState.ChainOnlineState == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
			task := createHealthTask(ctx, chainState)
			tasks = append(tasks, task)
		} else {
			chainState.ChainHealthyState = &health.ChainHealth{ServiceHealth: health.ServiceHealth{Healthy: false}}
		}
	}
	if err := helper.SyncRunTasks(tasks); err != nil {
		return nil, err
	}
	return resMap, nil
}

// ListTotalStateByIDs 最终的map中只包含在serving中存在的服务
func (m MLOpsChainDeploy) ListTotalStateByIDs(ctx context.Context, chainIds []string) (map[string]*ChainState, error) {
	tempMap, err := m.ListSimpleStateAsMap(ctx)
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]*ChainState)
	for _, id := range chainIds {
		state, ok := tempMap[id]
		if ok {
			resMap[id] = state
		}
	}
	// 补充health信息
	tasks := make([]func() error, 0)
	for _, chainState := range resMap {
		if chainState.ChainOnlineState == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
			task := createHealthTask(ctx, chainState)
			tasks = append(tasks, task)
		} else {
			chainState.ChainHealthyState = &health.ChainHealth{ServiceHealth: health.ServiceHealth{Healthy: false}}
		}
	}
	if err := helper.SyncRunTasks(tasks); err != nil {
		return nil, err
	}
	return resMap, nil
}

// ListSimpleStateAsMap 缺少health信息
func (m MLOpsChainDeploy) ListSimpleStateAsMap(ctx context.Context) (map[string]*ChainState, error) {
	resMap := make(map[string]*ChainState)
	if conf.Config.IsSimpleMode {
		return resMap, nil
	}
	start := time.Now()
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: buildListChainMetaInfo(),
	})
	elapsed := time.Since(start)
	stdlog.Infof("cost %v to execute clients.MLOpsCli.Cli.List", elapsed)
	if err != nil {
		return nil, err
	}

	for _, s := range svcs.ServiceInfos {
		if s.SourceMeta == nil || s.SourceMeta.Extra == nil {
			return nil, stderr.Internal.Error("svc :%v no source meta", s.Id)
		}
		extra := s.SourceMeta.Extra
		chainID, ok := extra[MLOpsSvcTagChainIDKey]
		if !ok {
			return nil, stderr.Internal.Error("svc source meta no chain id key")
		}
		chainState := buildState(chainID, s)
		resMap[chainID] = chainState
	}
	return resMap, nil
}

func (m MLOpsChainDeploy) GetSimpleState(ctx context.Context, chainID string) (*ChainState, error) {
	svcs, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceMetaExtra: map[string]string{
			MLOpsSvcTagChainIDKey:    chainID,
			MLOpsSvcTagExperienceKey: MLOpsSvcTagExperienceValue,
		},
	})
	if err != nil {
		return nil, err
	}
	if len(svcs.ServiceInfos) != 1 {
		return nil, stderr.Internal.Error("the nums of mlops-svc must be 1 for chain :%v", chainID)
	}
	mlopsSvc := svcs.ServiceInfos[0]
	simpleState := buildState(chainID, mlopsSvc)
	return simpleState, nil
}
func (m MLOpsChainDeploy) GetTotalState(ctx context.Context, chainID string) (*ChainState, error) {
	res, err := m.GetSimpleState(ctx, chainID)
	if err != nil {
		return nil, err
	}
	// 补充health信息
	if res.ChainOnlineState == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
		healthInfo, err := clients.AppletSvcCli.CheckSvcHealth(ctx, res.ServiceID)
		if err != nil {
			return nil, stderr.Wrap(err, "service health check err")
		}
		res.ChainHealthyState = healthInfo
	}
	return res, nil
}

func buildState(chainID string, mlopsSvc *serving.MLOpsServiceBaseInfo) *ChainState {
	return &ChainState{
		ChainID:           chainID,
		ServiceID:         mlopsSvc.Id,
		ServiceName:       mlopsSvc.Name,
		ChainOnlineState:  mlopsSvc.State,
		VirtualUrl:        mlopsSvc.VirtualSvcUrl,
		ChainStateInfo:    models.GetStateInfo(mlopsSvc.StateInfo),
		ChainHealthyState: nil,
	}
}
func createHealthTask(ctx context.Context, chainState *ChainState) func() error {
	return func() error {
		info, err := clients.AppletSvcCli.CheckSvcHealth(ctx, chainState.ServiceID)
		if err == nil {
			chainState.ChainHealthyState = info
		} else {
			str := fmt.Sprintf("failed to CheckSvcHealth with svc_id:%s ,the err is %v", chainState.ServiceID, err)
			chainState.ChainHealthyState = &health.ChainHealth{
				ServiceHealth: health.ServiceHealth{
					Healthy: false,
					Detail:  str,
				},
			}
			stdlog.Errorf(str)
		}
		return nil
	}
}

var (
	appHealthInfos *clients2.RedisMap[ChainState] // chain_id -> *ChainState
)

// ListCachedTotalState
// 从缓存中获取当前项目下,各个应用的健康状态信息。
func (m MLOpsChainDeploy) ListCachedTotalState(ctx context.Context) (map[string]*ChainState, error) {

	// 基本状态信息直接查询
	resp, err := m.ListSimpleStateAsMap(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "list simple state as map")
	}

	// health 健康状态信息使用缓存
	for chainId, state := range resp {
		temp, err := appHealthInfos.Get(chainId)
		if err != nil {
			return nil, err
		}
		state.ChainHealthyState = temp.ChainHealthyState
	}
	return resp, nil
}

func StartAppHealthCheck() {
	appHealthInfos = clients2.NewRedisMap[ChainState](clients.RedisCli, RedisKeyAppHealthCheck)
	go func() {
		doAppHealthCheck()
		ticker := time.NewTicker(conf.Config.AppSvcConfig.HealthInterval)
		for range ticker.C {
			doAppHealthCheck()
		}
	}()
}

func doAppHealthCheck() {
	ctx := context.Background()
	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "", conf.Config.Token)
	stateMap, err := ChainDeployManager.listTotalStateAsMap(ctx)
	if err != nil {
		stdlog.WithError(err).Error("failed to listTotalStateAsMap")
		return
	}

	for chainId, state := range stateMap {
		err = appHealthInfos.Set(chainId, state)
		if err != nil {
			stdlog.WithError(err).Error("failed to set state")
		}
	}
}
