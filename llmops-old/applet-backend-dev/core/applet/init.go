package applet

import "transwarp.io/applied-ai/applet-backend/core/applet/code_service"

type ChainMetricsType string

const (
	ChainMetricsTypeVisit   ChainMetricsType = "CHAIN_VISIT"
	ChainMetricsTypeClone   ChainMetricsType = "CHAIN_CLONE"
	ChainMetricsTypeExecute ChainMetricsType = "CHAIN_EXECUTE"
)

var (
	ChainManager        Chain
	ExperimentManager   Experiment
	LabelManager        Label
	WidgetManager       Widget
	ChainDebugManager   IChainDebug
	ChainDeployManager  ChainDeployer
	ChainMetricsManager IChainMetrics
	DialogManager       Dialog
	ChainTemplateManger ChainTemplate
	CodeService         code_service.ICodeService
)

func Init() error {
	ChainManager = &ChainImpl{}
	ExperimentManager = &ExperimentImpl{}
	LabelManager = &LabelImpl{}
	WidgetManager = &WidgetImpl{}
	ChainDebugManager = &ChainDebug{}
	ChainDeployManager = &MLOpsChainDeploy{}
	InitChainDebugStateMachine()
	InitDynamicDatasource()
	ChainMetricsManager = &ChainMetricsImpl{}
	DialogManager = &DialogImpl{}
	ChainTemplateManger = &ChainTemplateImpl{}
	CodeService = code_service.NewCodeService()

	return nil
}
