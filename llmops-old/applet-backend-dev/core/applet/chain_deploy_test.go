package applet

//import (
//	"context"
//	"encoding/json"
//	"testing"
//	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
//	"transwarp.io/applied-ai/applet-backend/clients"
//	"transwarp.io/applied-ai/applet-backend/conf"
//	"transwarp.io/applied-ai/applet-backend/dao"
//	"transwarp.io/applied-ai/applet-backend/pkg/helper"
//	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
//)
//
//func TestDeployChain(t *testing.T) {
//	// eb4e2409-c54f-4542-926c-67cda52390ee
//	// e9f3e320-919a-440d-b82e-02e65f8503fd
//	ctx := context.Background()
//	conf.TestInit()
//	dao.Init()
//	Init()
//	widgets.Init()
//	clients.TestInit()
//	//core.Init(ctx)
//	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "default", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
//	res, err := ChainDeployManager.Deploy(ctx, "b005a804-0de6-4786-92ac-db3363c59a3f")
//	if err != nil {
//		t.Fatal(err)
//	}
//	stdlog.Infof("res:%v", res)
//
//}
//
//func TestListChain(t *testing.T) {
//	ctx := context.Background()
//	conf.TestInit()
//	dao.Init()
//	Init()
//	widgets.Init()
//	clients.Init()
//	//core.Init(ctx)
//	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "default", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
//	res, err := ChainDeployManager.ListOnlineChains(ctx)
//	if err != nil {
//		t.Fatal(err)
//	}
//	stdlog.Infof("res :%v", res)
//}
//
//func TestOfflineChain(t *testing.T) {
//	ctx := context.Background()
//	conf.TestInit()
//	dao.Init()
//	Init()
//	widgets.Init()
//	clients.Init()
//	//core.Init(ctx)
//	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "default", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
//	res, err := ChainDeployManager.Offline(ctx, "qweqeqweqeqw")
//	if err != nil {
//		t.Fatal(err)
//	}
//	stdlog.Infof("res :%v", res)
//}
//
//func TestListState(t *testing.T) {
//	ctx := context.Background()
//	conf.TestInit()
//	dao.Init()
//	Init()
//	widgets.Init()
//	clients.Init()
//	//core.Init(ctx)
//	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "default", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
//	res, err := ChainDeployManager.ListState(ctx)
//	if err != nil {
//		t.Fatal(err)
//	}
//	bytes, _ := json.Marshal(res)
//	stdlog.Infof("res :%v", string(bytes))
//
//}
//
//func TestGetstate(t *testing.T) {
//	ctx := context.Background()
//	conf.TestInit()
//	dao.Init()
//	Init()
//	widgets.Init()
//	clients.Init()
//	//core.Init(ctx)
//	ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "default", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw")
//	res, err := ChainDeployManager.GetTotalState(ctx, "12312313xxxx1")
//	if err != nil {
//		t.Fatal(err)
//	}
//	bytes, _ := json.Marshal(res)
//	stdlog.Infof("res :%v", string(bytes))
//
//}
