package applet

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

// Experiment 应用体验
type Experiment interface {
	CreateExperiment(ctx context.Context, widget *models.AppletExperimentDO) (string, error)
	GetExperimentByID(ctx context.Context, chainID string) (*models.AppletExperimentDO, error)
	DeleteExperimentByID(ctx context.Context, chainID string) (string, error)
	ListExperiment(ctx context.Context, queryParam *dao.ExperimentQueryParam) ([]*models.AppletExperimentDO, error)
	UpdateExperiment(ctx context.Context, id string, experiment *models.AppletExperimentDO) (string, error)
}
type ExperimentImpl struct {
}

func (e *ExperimentImpl) CreateExperiment(ctx context.Context, experimentDO *models.AppletExperimentDO) (string, error) {
	experimentDAO := dao.AppletExperimentDAOImpl
	// 同一项目下的体验名称不能重复
	res, err := experimentDAO.List(ctx, &dao.ExperimentQueryParam{
		Name:      ptr.String(experimentDO.Name),
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	if err != nil {
		return "", err
	}
	if len(res) != 0 {
		return "", stderr.Internal.Error("the name of applet experiment cannot be duplicated under the same project")
	}
	// 一个应用链只对应一个发布的体验
	res, err = experimentDAO.List(ctx, &dao.ExperimentQueryParam{
		ChainID:   ptr.String(experimentDO.ChainID),
		ProjectID: ptr.String(helper.GetProjectID(ctx)),
	})
	if err != nil {
		return "", err
	}
	if len(res) != 0 {
		return "", stderr.Internal.Error("an applet chain only corresponds to one published experience")
	}

	experimentPO, err := experimentDO.ToPO(ctx)
	if err != nil {
		return "", err
	}
	return experimentDAO.Create(ctx, experimentPO)
}

func (e *ExperimentImpl) GetExperimentByID(ctx context.Context, chainID string) (*models.AppletExperimentDO, error) {
	experimentDAO := dao.AppletExperimentDAOImpl
	experimentPO, err := experimentDAO.GetByID(ctx, chainID)
	if err != nil {
		return nil, err
	}
	experimentDO, err := models.CvtExperimentPOToDO(experimentPO)
	if err != nil {
		return nil, err
	}
	return experimentDO, nil
}

func (e *ExperimentImpl) DeleteExperimentByID(ctx context.Context, chainID string) (string, error) {
	experimentDAO := dao.AppletExperimentDAOImpl
	return experimentDAO.DeleteByID(ctx, chainID)
}
func (e *ExperimentImpl) ListExperiment(ctx context.Context,
		queryParam *dao.ExperimentQueryParam) ([]*models.AppletExperimentDO, error) {
	experimentDAO := dao.AppletExperimentDAOImpl
	experimentPOs, err := experimentDAO.List(ctx, queryParam)
	if err != nil {
		return nil, err
	}
	res := make([]*models.AppletExperimentDO, len(experimentPOs))
	for idx, PO := range experimentPOs {
		experimentDO, err := models.CvtExperimentPOToDO(PO)
		if err != nil {
			return nil, stderr.Wrap(err, "convert experimentPO err")
		}
		res[idx] = experimentDO
	}
	return res, nil
}

func (e *ExperimentImpl) UpdateExperiment(ctx context.Context,
		id string, experiment *models.AppletExperimentDO) (string, error) {
	experimentDAO := dao.AppletExperimentDAOImpl
	experimentPO, err := experiment.ToPO(ctx)
	if err != nil {
		return "", err
	}
	err = experimentDAO.Update(ctx, id, experimentPO)
	if err != nil {
		return "", err
	}
	return id, nil
}
