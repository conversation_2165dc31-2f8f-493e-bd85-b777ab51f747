package code_service

import (
	"fmt"

	"transwarp.io/applied-ai/applet-backend/conf"
)

const GENERATE_PYTHON_CODE_PROMPT_TEMPLATE = `
# Role
You are an expert programmer. Generate code based on the following instructions.

# Instructions

## Functional Requirements
%s

## Implementation Requirements
Write the code in python.
Please ensure that you meet the following requirements:
1. Define a function named 'handler'.
2. The 'handler' function must have and can only have one input parameter. You may modify the name of the input parameter of the 'handler' function.
3. The handler function is the entry point that will be executed.
4. Python libraries need to be imported before they can be used.
5. You may need to generate code based on existing code and the input value.

# Existing Code
The content within the <existing-code></existing-code> XML tags is existing code. Use it when you are requested to modify existing code.
<existing-code>
%s
</existing-code>

# Handler Function Input Value
The content within the <input-value></input-value> XML tags is the input value of handler function. You may need to generate code based on it.
<input-value>
%s
</input-value>

# Generated Code Format
- Provide ONLY the code without any additional explanations, comments, or markdown formatting.
- DO NOT use markdown code blocks ` +
	"(``` or ``` python). Return the raw code directly." +
	`
- The code should start immediately after this instruction, without any preceding newlines or spaces.       
- The code should be complete, functional, and follow best practices for python.

# Example Generated Code
import json
def handler(data: str) -> any:
    result = json.loads(data)
    return result

# Generated Code
`

func getGeneratePythonCodePrompt(instruction string, existingCode string, inputValue string) string {
	// 限制input长度
	if len(inputValue) > conf.Config.CodeService.MaxInputLen {
		inputValue = inputValue[:conf.Config.CodeService.MaxInputLen] + "..."
	}
	return fmt.Sprintf(GENERATE_PYTHON_CODE_PROMPT_TEMPLATE, instruction, existingCode, inputValue)
}
