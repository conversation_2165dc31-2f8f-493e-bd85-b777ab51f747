package code_service

import (
	"context"
	"encoding/json"
	"net/http"
	"regexp"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	backend_clients "transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	pkg_clients "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/code_service"
)

const (
	CodeLanguagePython  = "python"
	CodeLanguageJsonnet = "jsonnet"
)

// ICodeService 代码服务接口
type ICodeService interface {
	// GenerateCode 生成代码
	GenerateCode(ctx context.Context, generateCodeRequest *code_service.GenerateCodeRequest) (*code_service.GenerateCodeResponse, error)
	// TestCode 测试代码
	TestCode(ctx context.Context, testCodeRequest *code_service.TestCodeRequest) (*code_service.TestCodeResponse, error)
}

// CodeServiceImpl 代码服务实现
type CodeServiceImpl struct{}

// NewCodeService 创建代码服务实例
func NewCodeService() ICodeService {
	return &CodeServiceImpl{}
}

// GenerateCode 生成代码
func (c *CodeServiceImpl) GenerateCode(ctx context.Context, generateCodeRequest *code_service.GenerateCodeRequest) (*code_service.GenerateCodeResponse, error) {
	if generateCodeRequest == nil {
		return nil, stderr.Errorf("generate code req param is nil")
	}
	switch generateCodeRequest.Language {
	case CodeLanguagePython:
		return c.generatePythonCode(ctx, generateCodeRequest)
	default:
		return nil, stderr.InvalidParam.Errorf("unsupported language: %s", generateCodeRequest.Language)
	}
}

// TestCode 测试代码
func (c *CodeServiceImpl) TestCode(ctx context.Context, req *code_service.TestCodeRequest) (*code_service.TestCodeResponse, error) {
	if req == nil {
		return nil, stderr.Errorf("test code req param is nil")
	}
	switch req.Language {
	case CodeLanguagePython:
		return c.testPythonCode(ctx, req)
	default:
		return nil, stderr.InvalidParam.Errorf("unsupported language: %s", req.Language)
	}
}

// testPythonCode 执行Python代码
func (c *CodeServiceImpl) testPythonCode(ctx context.Context, testCodeRequest *code_service.TestCodeRequest) (*code_service.TestCodeResponse, error) {
	testPythonUrl := conf.Config.CodeService.TestPythonUrl

	// 解析输入参数
	var inputs any
	if err := json.Unmarshal([]byte(testCodeRequest.Inputs), &inputs); err != nil {
		return nil, stderr.Wrap(err, "unmarshal inputs failed")
	}

	// 构造请求参数
	reqBody := &code_service.TestPythonCodeRequest{
		Code:   testCodeRequest.Code,
		Inputs: inputs,
	}
	reqJson, err := json.Marshal(reqBody)
	if err != nil {
		return nil, stderr.Wrap(err, "marshal request body failed")
	}

	// 构造HTTP请求参数
	httpParam := &pkg_clients.HttpParam{
		Method:  http.MethodPost,
		Url:     testPythonUrl,
		ReqBody: string(reqJson),
	}

	// 发送HTTP请求
	var resp code_service.TestPythonCodeResponse
	if err := backend_clients.HttpCli.HttpCall(ctx, httpParam, &resp); err != nil {
		return nil, stderr.Wrap(err, "call test python service failed")
	}

	// 处理输出结果
	outputJson, err := json.Marshal(resp.Output)
	if err != nil {
		return nil, stderr.Wrap(err, "marshal response output failed")
	}

	return &code_service.TestCodeResponse{
		Output:   string(outputJson),
		Log:      resp.Log,
		Error:    resp.Error,
		Language: CodeLanguagePython,
	}, nil
}

// chat 与模型交互生成代码
func (c *CodeServiceImpl) chat(ctx context.Context, prompt string, modelService *pb.ModelService) (string, error) {
	req := &triton.OpenAiChatReq{
		Messages: []triton.MultimodalMessageItem{
			{
				Role:    triton.OpenaiSystemMsg,
				Content: prompt,
			},
		},
		Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
	}

	var generatedCode string
	resHandler := func(textContent string) error {
		generatedCode = textContent
		return nil
	}

	err := pkg_clients.SyncChat(ctx, modelService, req, resHandler)
	if err != nil {
		return "", stderr.Wrap(err, "failed to do sync chat")
	}

	return generatedCode, nil
}

// extractPythonCode 从模型响应中提取Python代码。它可以处理以下格式：
// 1. 带有```python标记的代码块
// 2. 带有```标记的普通代码块
// 3. 如果没有找到代码块标记，则返回原始内容
func (c *CodeServiceImpl) extractPythonCode(modelResponse string) string {
	// 正则表达式解释：
	// (?s) - 启用单行模式，使得 . 可以匹配包括换行符在内的任意字符
	// .*? - 非贪婪匹配任意字符，用于匹配代码块前的文本
	// (?:```python\\n?|```\\n?) - 非捕获组，匹配两种情况：
	//   - ```python 后可能跟着换行
	//   - ``` 后可能跟着换行
	// (.*?) - 捕获组，非贪婪匹配代码块内容，这是我们要提取的部分
	// ``` - 匹配结束标记 ```
	// .*? - 非贪婪匹配代码块后的任意文本
	//
	// 示例匹配文本：
	// 1. "这是说明文字\n```python\n代码内容\n```\n后面的说明"
	// 2. "```python\n代码内容\n```"
	// 3. "```\n代码内容\n```"
	pattern := regexp.MustCompile("(?s).*?(?:```python\\n?|```\\n?)(.*?)```.*?")
	// FindStringSubmatch 返回的 matches 数组：
	// matches[0] 是完整的匹配结果
	// matches[1] 是第一个捕获组的内容，即代码部分
	if matches := pattern.FindStringSubmatch(modelResponse); len(matches) > 1 {
		return matches[1]
	}

	// 如果没有找到代码块标记，返回原始内容
	return modelResponse
}

// generatePythonCode 使用LLM生成Python代码
func (c *CodeServiceImpl) generatePythonCode(ctx context.Context, generateCodeRequest *code_service.GenerateCodeRequest) (*code_service.GenerateCodeResponse, error) {
	// 构造提示词
	prompt := getGeneratePythonCodePrompt(generateCodeRequest.Instruction, generateCodeRequest.Code, generateCodeRequest.Inputs)
	modelResponse, err := c.chat(ctx, prompt, generateCodeRequest.ModelService)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to chat with model")
	}

	// Extract Python code from model response
	code := c.extractPythonCode(modelResponse)

	return &code_service.GenerateCodeResponse{
		Code:     code,
		Language: CodeLanguagePython,
	}, nil
}
