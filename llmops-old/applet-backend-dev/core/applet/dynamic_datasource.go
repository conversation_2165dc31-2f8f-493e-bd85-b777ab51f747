package applet

import (
	"context"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

var DynamicDatasourceMap map[string]DynamicDatasource

func InitDynamicDatasource() {
	DynamicDatasourceMap = make(map[string]DynamicDatasource)
	DynamicDatasourceMap[helper.LLMDataSource] = LLMDatasource{}
	DynamicDatasourceMap[helper.VectorDataSource] = VectorDatasource{}
	DynamicDatasourceMap[helper.DlieDataSource] = DlieDatasource{}
	//DynamicDatasourceMap[helper.KnowledgeDataSource] = KnowledgeBaseDatasource{}
	DynamicDatasourceMap[helper.ModelServiceDataSource] = ModelServiceDatasource{}
	DynamicDatasourceMap[helper.TextEnhanceModeDataSource] = TextEnhanceModeDatasource{}
	DynamicDatasourceMap[helper.HippoDataSource] = HippoDataSource{}
	DynamicDatasourceMap[helper.ScopeDataSource] = ScopeDataSource{}
}

type DynamicDatasource interface {
	List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error)
}

type LLMDatasource struct {
}

func (l LLMDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	svcs, err := clients.MWHCli.ListModelServices(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*models.DynamicDatasource, 0)
	for _, l := range svcs.LLMService {
		res = append(res, &models.DynamicDatasource{
			Name: widgets.ModelServiceToDataSourceName(l),
			ID:   widgets.ModelServiceToDataSourceKey(l),
		})
	}
	return res, nil
}

type VectorDatasource struct {
}

func (l VectorDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	svcs, err := clients.MWHCli.ListModelServices(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*models.DynamicDatasource, 0)
	for _, l := range svcs.VectorService {
		res = append(res, &models.DynamicDatasource{
			Name: widgets.ModelServiceToDataSourceName(l),
			ID:   widgets.ModelServiceToDataSourceKey(l),
		})
	}
	return res, nil
}

type DlieDatasource struct {
}

func (l DlieDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	svcs, err := clients.MWHCli.ListModelServices(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*models.DynamicDatasource, 0)
	for _, l := range svcs.DlieService {
		res = append(res, &models.DynamicDatasource{
			Name: widgets.ModelServiceToDataSourceName(l),
			ID:   widgets.ModelServiceToDataSourceKey(l),
		})
	}
	return res, nil
}

// type KnowledgeBaseDatasource struct {
// }
//
// // List KnowledgeBaseDatasource
//
//	func (k KnowledgeBaseDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
//		res := make([]*models.DynamicDatasource, 0)
//		pbUserCtx, err := getUserContext(ctx)
//		if err != nil {
//			return nil, err
//		}
//		rsp, err := knowledge_base.KnowledgeBaseMgr.ListKnowledgeBases(ctx, &pb.ListKnowledgeBasesReq{
//			UserContext:  pbUserCtx,
//			ListSelector: &pb.ListKnowledgeBasesSelector{},
//		})
//		if err != nil {
//			return nil, err
//		}
//		for _, kbInfo := range rsp.Result {
//			res = append(res, &models.DynamicDatasource{
//				Name: kbInfo.KnowledgeBase.Name,
//				ID:   kbInfo.KnowledgeBase.Id,
//			})
//		}
//		return res, nil
//	}
func getUserContext(ctx context.Context) (*pb.UserContext, error) {
	user, err := helper.GetUser(ctx)
	if err != nil {
		return nil, err
	}
	projectId := helper.GetProjectID(ctx)
	ret := &pb.UserContext{
		UserId:    user,
		UserName:  user,
		ProjectId: projectId,
	}
	return ret, nil
}

type ModelServiceDatasource struct {
}

func (m ModelServiceDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	res := make([]*models.DynamicDatasource, 0)
	svcs, err := clients.MWHCli.ListHealthyModelServices(ctx)
	if err != nil {
		return nil, err
	}
	subKindStr := request.QueryParameter(helper.QueryParamSubKind)
	if subKindStr == "" {
		return nil, stderr.Internal.Error("the subKind of model is empty")
	}
	subKind, ok := pb.ModelSubKind_value[subKindStr]
	if !ok {
		return nil, stderr.Internal.Error("model subKind %s not existed", subKindStr)
	}
	for _, svc := range svcs {
		if svc.SubKind != pb.ModelSubKind(subKind) {
			continue
		}

		newSvc := &pb.ModelService{Name: svc.Name, FullUrl: svc.FullUrl}
		bytes, err := stdsrv.DefaultProtoJsonAccessor().Marshal(newSvc)
		if err != nil {
			return nil, err
		}
		res = append(res, &models.DynamicDatasource{
			Name: newSvc.Name,
			ID:   string(bytes),
		})
	}
	return res, nil
}

type TextEnhanceModeDatasource struct {
}

func (t TextEnhanceModeDatasource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	res := make([]*models.DynamicDatasource, 0)
	for modeValue, modeName := range widgets.DefaultTextEnhanceMode {
		res = append(res, &models.DynamicDatasource{
			Name: modeName,
			ID:   modeValue.String(),
		})
	}
	return res, nil
}

func getDataSourceThenConvertToResponse(ctx context.Context, type_selector pb.ConnectionType) ([]*models.DynamicDatasource, error) {
	projectId := helper.GetProjectID(ctx)
	tenantId := helper.GetTenantID(ctx)
	dataConnections, err := clients.CVATCli.ListDataConnections(ctx, type_selector, projectId, tenantId)
	if err != nil {
		stdlog.Errorf("Unable to get data connections from CVAT: %+v", err.Error())
		return nil, err
	}

	res := make([]*models.DynamicDatasource, 0)

	for _, connection := range dataConnections.Result {
		connectionBytes, err := stdsrv.MarshalMixWithProto(connection)
		if err != nil {
			stdlog.Errorln("Failed to Marshal ConnectionInfo in bytes")
			return nil, err
		}

		res = append(res, &models.DynamicDatasource{
			Name: connection.Name,
			ID:   string(connectionBytes),
		})
	}

	return res, nil
}

type HippoDataSource struct {
}

func (h HippoDataSource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	response, err := getDataSourceThenConvertToResponse(ctx, pb.ConnectionType_HIPPO)
	if err != nil {
		stdlog.Errorln("Error getting Hippo Data Connections as response")
		return nil, err
	}
	return response, nil
}

type ScopeDataSource struct {
}

func (s ScopeDataSource) List(request *restful.Request, ctx context.Context) ([]*models.DynamicDatasource, error) {
	response, err := getDataSourceThenConvertToResponse(ctx, pb.ConnectionType_SCOPE)
	if err != nil {
		stdlog.Errorln("Error getting Scope Data Connections as response")
		return nil, err
	}
	return response, nil
}
