package applet

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
)

type IChainMetrics interface {
	Update(ctx context.Context, chainID string, events map[ChainMetricsType]int64) error
	UpdateOnce(ctx context.Context, chainID string, mType ChainMetricsType) error
}

type ChainMetricsImpl struct {
}

func (c ChainMetricsImpl) Update(ctx context.Context, chainID string, events map[ChainMetricsType]int64) error {
	chain, err := ChainManager.GetChainByID(ctx, chainID)
	if err != nil {
		return stderr.Wrap(err, "update chain metrics,query metrics info err")
	}
	metricsInfo := chain.Base.MetricsInfo
	for k, v := range events {
		switch k {
		case ChainMetricsTypeVisit:
			metricsInfo.Visit(v)
		case ChainMetricsTypeClone:
			metricsInfo.Clone(v)
		case ChainMetricsTypeExecute:
			metricsInfo.Execute(v)
		default:
			return stderr.Error("the type only support %s, %s, %s",
				ChainMetricsTypeVisit, ChainMetricsTypeClone, ChainMetricsTypeExecute)
		}
	}
	if err := dao.AppletChainDAOImpl.UpdateBasicInfoV2(ctx, chainID,
		dao.AppletChainParam{MetricsInfo: ptr.String(metricsInfo.Marshal())}); err != nil {
		return stderr.Wrap(err, "update chain metrics info err")
	}
	return nil
}

func (c ChainMetricsImpl) UpdateOnce(ctx context.Context, chainID string, mType ChainMetricsType) error {
	events := map[ChainMetricsType]int64{mType: 1}
	return c.Update(ctx, chainID, events)
}
