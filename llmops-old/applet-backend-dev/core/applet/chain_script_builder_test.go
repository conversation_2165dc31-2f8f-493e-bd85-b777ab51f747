package applet

import (
	"context"
	"encoding/json"
	"testing"

	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func TestNewScriptGenerator(t *testing.T) {
	conf.Config = &conf.AppConfig{
		Kapacitor: conf.KapacitorConfig{
			Addr:           "",
			DBName:         "",
			APIPath:        "",
			APIHealthPath:  "",
			APIPort:        0,
			ExecuteAPIPort: "",
		},
	}
	// var testChain = `{"chain_id":"6618ef93-61f3-499c-82d0-de5d8bb45248","chain_name":"if-else","chain_detail":{"nodes":[{"id":"444241dd-9013-4754-aec7-a568d24353de","name":"文本输入","ui":"{\"id\":\"444241dd-9013-4754-aec7-a568d24353de\",\"type\":\"custom\",\"position\":{\"x\":330,\"y\":225},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":109}}","widget_id":"WidgetKeyTextInput","widget_detail":{"id":"WidgetKeyTextInput","name":"文本输入","desc":"[文本输入]: 用于将输入的文本原样输出","group":"WidgetGroupInput","params":[{"data_class":"string","category":"req-input","preview":false,"define":{"id":"TextInput","name":"文本输入","desc":"文本输入,支持类型: Sync-String: \"text\"。","type":"TYPE_TEXTAREA","data_type":"DATA_TYPE_STRING"},"param_limits":{"types":["Sync-String"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"支持类型: Sync-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-String"]}}]},"values":{}},{"id":"f13ba935-c9e5-403c-984e-23b374188140","name":"条件判断","ui":"{\"id\":\"f13ba935-c9e5-403c-984e-23b374188140\",\"type\":\"custom\",\"position\":{\"x\":748,\"y\":231},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":204},\"selected\":true,\"dragging\":false}","widget_id":"WidgetKeyConditionJudge","widget_detail":{"id":"WidgetKeyConditionJudge","name":"条件判断","desc":"[条件判断]: 把数据流分叉成if分支与else分支，根据输入的条件决定数据流向的分支","group":"WidgetGroupControlFlow","oriWidgetKey":"WidgetKeyConditionJudge","params":[{"data_class":"string","category":"in-port","preview":false,"define":{"id":"Input","name":"输入数据","desc":"待执行判断条件的数据,支持类型: Sync-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-Any"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPutIF","name":"If","desc":"条件成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-Any"]}},{"data_class":"code","category":"attribute","preview":false,"define":{"id":"Code","name":"判断条件","desc":"\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\n可以使用下面的语法表示判断条件, 更复杂的判断条件请参考Jsonnet语法\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"\n","required":true,"type":"TYPE_INPUT","data_type":"DATA_TYPE_STRING"},"param_limits":null},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPutElse","name":"Else","desc":"条件不成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-Any"]}}]},"values":{"Code":"input == \"ni\""}},{"id":"68c289a5-8f95-4c5c-8669-f2ffc05f98b6","name":"数据合流","ui":"{\"id\":\"68c289a5-8f95-4c5c-8669-f2ffc05f98b6\",\"type\":\"custom\",\"position\":{\"x\":1200,\"y\":270},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":108}}","widget_id":"WidgetKeyUnion","widget_detail":{"id":"WidgetKeyUnion","name":"数据合流","desc":"[数据合流]: 将上游多个输入，形成队列串行输出","group":"WidgetGroupControlFlow","oriWidgetKey":"WidgetKeyUnion","params":[{"data_class":"string","category":"in-port","preview":false,"define":{"id":"Input","name":"上游数据，任意类型","desc":"待合流的上游数据，需保证上游只有一个分支有数据流入此算子，否则请用数据合并算子,支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"原样输出输入的上游数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}}]},"values":{}}],"edges":[{"id":"xy-edge__444241dd-9013-4754-aec7-a568d24353de444241dd-9013-4754-aec7-a568d24353de@@OutPut-f13ba935-c9e5-403c-984e-23b374188140f13ba935-c9e5-403c-984e-23b374188140@@Input","source":"444241dd-9013-4754-aec7-a568d24353de","source_param":"444241dd-9013-4754-aec7-a568d24353de@@OutPut","target":"f13ba935-c9e5-403c-984e-23b374188140","target_param":"f13ba935-c9e5-403c-984e-23b374188140@@Input"},{"id":"xy-edge__f13ba935-c9e5-403c-984e-23b374188140f13ba935-c9e5-403c-984e-23b374188140@@OutPutIF-68c289a5-8f95-4c5c-8669-f2ffc05f98b668c289a5-8f95-4c5c-8669-f2ffc05f98b6@@Input","source":"f13ba935-c9e5-403c-984e-23b374188140","source_param":"f13ba935-c9e5-403c-984e-23b374188140@@OutPutIF","target":"68c289a5-8f95-4c5c-8669-f2ffc05f98b6","target_param":"68c289a5-8f95-4c5c-8669-f2ffc05f98b6@@Input"},{"id":"xy-edge__f13ba935-c9e5-403c-984e-23b374188140f13ba935-c9e5-403c-984e-23b374188140@@OutPutElse-68c289a5-8f95-4c5c-8669-f2ffc05f98b668c289a5-8f95-4c5c-8669-f2ffc05f98b6@@Input","source":"f13ba935-c9e5-403c-984e-23b374188140","source_param":"f13ba935-c9e5-403c-984e-23b374188140@@OutPutElse","target":"68c289a5-8f95-4c5c-8669-f2ffc05f98b6","target_param":"68c289a5-8f95-4c5c-8669-f2ffc05f98b6@@Input"}],"viewport":{"x":-81,"y":-24,"zoom":1}},"run_id":"144f2d65-33dd-4cc3-a954-cdcf669abbb5","widget_param":{"444241dd-9013-4754-aec7-a568d24353de##TextInput":"1"}}`
	var testChain = `{"chain_detail":{"nodes":[{"id":"afa62d94-f25c-4f05-a74a-46ce34795282","name":"文本输入","ui":"{\"id\":\"afa62d94-f25c-4f05-a74a-46ce34795282\",\"type\":\"custom\",\"position\":{\"x\":218,\"y\":197},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":109},\"selected\":false,\"dragging\":false}","widget_id":"WidgetKeyTextInput","widget_detail":{"id":"WidgetKeyTextInput","name":"文本输入","desc":"[文本输入]: 用于将输入的文本原样输出","group":"WidgetGroupInput","params":[{"data_class":"string","category":"req-input","preview":false,"define":{"id":"TextInput","name":"文本输入","desc":"文本输入,支持类型: Sync-String: \"text\"。","type":"TYPE_TEXTAREA","data_type":"DATA_TYPE_STRING"},"param_limits":{"types":["Sync-String"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"支持类型: Sync-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-String"]}}]},"values":{}},{"id":"32bc49b7-3bfb-46d4-8763-8b7d67aea40c","name":"通用文本模板","ui":"{\"id\":\"32bc49b7-3bfb-46d4-8763-8b7d67aea40c\",\"type\":\"custom\",\"position\":{\"x\":645.0141828776549,\"y\":171.12598992803228},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":180},\"selected\":false,\"dragging\":false}","widget_id":"WidgetKeyTextTemplate","widget_detail":{"id":"WidgetKeyTextTemplate","name":"通用文本模板","desc":"仔细阅读以下关于文生图模型的提示词最佳实践文章（被<context></context>包裹的部分），然后尝试根据最佳实践指引对用户关于图片的描述进行改写，请注意不要丢失用户最开始的描述中的关键需求。用户的图片描述将被包裹在<user></user>这对标签中。\n注意：\n- 输出最好使用英文\n- 不要输出任何和图像描述不相关的内容\n<context>\nThe Art of Prompt Writing\nKey Elements of an Effective Prompt\nCreating eye-catching images with FLUX.1-dev hinges on your ability to write a precise and descriptive prompt. Here are some key elements to consider when crafting prompts:\n\nSpecific Details: Include clear and vivid descriptions of characters, objects, and environments to ensure accurate rendering.\nSetting: Provide contextual information about the world or scene your character inhabits.\nMood and Atmosphere: Describe the tone or emotion you wish to evoke through the imagery.\nStyle: Specify the desired artistic style, whether it's hyper-realistic, surreal, or another format.\nExample: Crafting a Warrior Character\nLet’s break down a sample prompt to showcase these principles. Imagine you want to create a warrior character within a vibrant alien landscape:\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior Character\n\nWhy This Works:\nSpecific Details: The character’s physical traits, armor type, and weapon are vividly described.\nSetting: A cliff in an alien world with moons and floating islands sets a clear context.\nMood and Atmosphere: The description of the glowing elements and vibrant colors conveys a sense of energy and life.\nStyle: By specifying a hyper-realistic style, the prompt guides the model to produce a visually sharp and immersive image.\nEvolving Your Character\nAdding New Elements: The Dining Warrior\nOnce you’ve established a basic character, you can add depth by incorporating new elements into the scene. For instance, let’s evolve our warrior by introducing a dining scene.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. With her left hand, she's bringing a piece of ethereal, glowing fruit to her mouth, about to take a bite. The fruit hovers slightly above her palm, pulsating with inner light. The background shows two moons in the sky and floating islands with waterfalls. Nearby, a floating crystal table holds an array of otherworldly delicacies. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior with Ethereal Fruit\n\nWhy This Works:\nThe new action (eating glowing fruit) introduces a dynamic pose while maintaining consistency with the original scene.\nIt adds an element of interactivity, enhancing the visual narrative without detracting from the primary character focus.\nChanging Contexts: From Lone Warrior to War Leader\nTo further develop the warrior’s story, you might want to change her role from a solitary fighter to a leader of an army.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors. She is leading a massive army of diverse alien warriors spread out below the cliff, ready for battle.\"\n\nWarrior as War Leader\n\nWhy This Works:\nThe prompt builds on the previous visual elements while introducing a new concept: leadership.\nIt expands the scene, giving the warrior more significance and context, enriching the visual output.\nTips for Mastering FLUX.1-dev\nTo consistently create remarkable images with FLUX.1-dev, consider the following best practices:\n\nBe Precise with Core Elements: Always describe your main subject with specific, vivid details.\nUse Descriptive Adjectives: Adjectives like \"crystalline,\" \"bioluminescent,\" or \"hyper-realistic\" help refine the visual outcome.\nLeave Room for Interpretation: Occasionally, being too specific can stifle creativity. Allow some open-ended aspects for the AI to explore creatively. Phrases such as \"in a fantastical alien world\" allow for creative flexibility.\nIterate on Your Prompts: Do not overchange your prompts. Change one element at a time to better understand how each affects the final image.\nTweak guidance scale and seed value: When you are close to the desired output, experiment with different guidance scales and seed values to refine the image further.\nBy practicing and tweaking these strategies, you'll refine your prompt-writing skills and generate increasingly stunning visuals.\n</context>\n\n<user>\n{{.用户问题}}\n</user>\n","group":"WidgetGroupProcessText","params":[{"data_class":"string","category":"attribute","preview":false,"define":{"id":"Template","name":"模板","default_value":"仔细阅读以下关于文生图模型的提示词最佳实践文章（被<context></context>包裹的部分），然后尝试根据最佳实践指引对用户关于图片的描述进行改写，请注意不要丢失用户最开始的描述中的关键需求。用户的图片描述将被包裹在<user></user>这对标签中。\n注意：\n- 输出最好使用英文\n- 不要输出任何和图像描述不相关的内容\n<context>\nThe Art of Prompt Writing\nKey Elements of an Effective Prompt\nCreating eye-catching images with FLUX.1-dev hinges on your ability to write a precise and descriptive prompt. Here are some key elements to consider when crafting prompts:\n\nSpecific Details: Include clear and vivid descriptions of characters, objects, and environments to ensure accurate rendering.\nSetting: Provide contextual information about the world or scene your character inhabits.\nMood and Atmosphere: Describe the tone or emotion you wish to evoke through the imagery.\nStyle: Specify the desired artistic style, whether it's hyper-realistic, surreal, or another format.\nExample: Crafting a Warrior Character\nLet’s break down a sample prompt to showcase these principles. Imagine you want to create a warrior character within a vibrant alien landscape:\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior Character\n\nWhy This Works:\nSpecific Details: The character’s physical traits, armor type, and weapon are vividly described.\nSetting: A cliff in an alien world with moons and floating islands sets a clear context.\nMood and Atmosphere: The description of the glowing elements and vibrant colors conveys a sense of energy and life.\nStyle: By specifying a hyper-realistic style, the prompt guides the model to produce a visually sharp and immersive image.\nEvolving Your Character\nAdding New Elements: The Dining Warrior\nOnce you’ve established a basic character, you can add depth by incorporating new elements into the scene. For instance, let’s evolve our warrior by introducing a dining scene.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. With her left hand, she's bringing a piece of ethereal, glowing fruit to her mouth, about to take a bite. The fruit hovers slightly above her palm, pulsating with inner light. The background shows two moons in the sky and floating islands with waterfalls. Nearby, a floating crystal table holds an array of otherworldly delicacies. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior with Ethereal Fruit\n\nWhy This Works:\nThe new action (eating glowing fruit) introduces a dynamic pose while maintaining consistency with the original scene.\nIt adds an element of interactivity, enhancing the visual narrative without detracting from the primary character focus.\nChanging Contexts: From Lone Warrior to War Leader\nTo further develop the warrior’s story, you might want to change her role from a solitary fighter to a leader of an army.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors. She is leading a massive army of diverse alien warriors spread out below the cliff, ready for battle.\"\n\nWarrior as War Leader\n\nWhy This Works:\nThe prompt builds on the previous visual elements while introducing a new concept: leadership.\nIt expands the scene, giving the warrior more significance and context, enriching the visual output.\nTips for Mastering FLUX.1-dev\nTo consistently create remarkable images with FLUX.1-dev, consider the following best practices:\n\nBe Precise with Core Elements: Always describe your main subject with specific, vivid details.\nUse Descriptive Adjectives: Adjectives like \"crystalline,\" \"bioluminescent,\" or \"hyper-realistic\" help refine the visual outcome.\nLeave Room for Interpretation: Occasionally, being too specific can stifle creativity. Allow some open-ended aspects for the AI to explore creatively. Phrases such as \"in a fantastical alien world\" allow for creative flexibility.\nIterate on Your Prompts: Do not overchange your prompts. Change one element at a time to better understand how each affects the final image.\nTweak guidance scale and seed value: When you are close to the desired output, experiment with different guidance scales and seed values to refine the image further.\nBy practicing and tweaking these strategies, you'll refine your prompt-writing skills and generate increasingly stunning visuals.\n</context>\n\n<user>\n{{.用户问题}}\n</user>\n","disabled":true,"type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED","value":"仔细阅读以下关于文生图模型的提示词最佳实践文章（被<context></context>包裹的部分），然后尝试根据最佳实践指引对用户关于图片的描述进行改写，请注意不要丢失用户最开始的描述中的关键需求。用户的图片描述将被包裹在<user></user>这对标签中。\n注意：\n- 输出最好使用英文\n- 不要输出任何和图像描述不相关的内容\n<context>\nThe Art of Prompt Writing\nKey Elements of an Effective Prompt\nCreating eye-catching images with FLUX.1-dev hinges on your ability to write a precise and descriptive prompt. Here are some key elements to consider when crafting prompts:\n\nSpecific Details: Include clear and vivid descriptions of characters, objects, and environments to ensure accurate rendering.\nSetting: Provide contextual information about the world or scene your character inhabits.\nMood and Atmosphere: Describe the tone or emotion you wish to evoke through the imagery.\nStyle: Specify the desired artistic style, whether it's hyper-realistic, surreal, or another format.\nExample: Crafting a Warrior Character\nLet’s break down a sample prompt to showcase these principles. Imagine you want to create a warrior character within a vibrant alien landscape:\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior Character\n\nWhy This Works:\nSpecific Details: The character’s physical traits, armor type, and weapon are vividly described.\nSetting: A cliff in an alien world with moons and floating islands sets a clear context.\nMood and Atmosphere: The description of the glowing elements and vibrant colors conveys a sense of energy and life.\nStyle: By specifying a hyper-realistic style, the prompt guides the model to produce a visually sharp and immersive image.\nEvolving Your Character\nAdding New Elements: The Dining Warrior\nOnce you’ve established a basic character, you can add depth by incorporating new elements into the scene. For instance, let’s evolve our warrior by introducing a dining scene.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. With her left hand, she's bringing a piece of ethereal, glowing fruit to her mouth, about to take a bite. The fruit hovers slightly above her palm, pulsating with inner light. The background shows two moons in the sky and floating islands with waterfalls. Nearby, a floating crystal table holds an array of otherworldly delicacies. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior with Ethereal Fruit\n\nWhy This Works:\nThe new action (eating glowing fruit) introduces a dynamic pose while maintaining consistency with the original scene.\nIt adds an element of interactivity, enhancing the visual narrative without detracting from the primary character focus.\nChanging Contexts: From Lone Warrior to War Leader\nTo further develop the warrior’s story, you might want to change her role from a solitary fighter to a leader of an army.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors. She is leading a massive army of diverse alien warriors spread out below the cliff, ready for battle.\"\n\nWarrior as War Leader\n\nWhy This Works:\nThe prompt builds on the previous visual elements while introducing a new concept: leadership.\nIt expands the scene, giving the warrior more significance and context, enriching the visual output.\nTips for Mastering FLUX.1-dev\nTo consistently create remarkable images with FLUX.1-dev, consider the following best practices:\n\nBe Precise with Core Elements: Always describe your main subject with specific, vivid details.\nUse Descriptive Adjectives: Adjectives like \"crystalline,\" \"bioluminescent,\" or \"hyper-realistic\" help refine the visual outcome.\nLeave Room for Interpretation: Occasionally, being too specific can stifle creativity. Allow some open-ended aspects for the AI to explore creatively. Phrases such as \"in a fantastical alien world\" allow for creative flexibility.\nIterate on Your Prompts: Do not overchange your prompts. Change one element at a time to better understand how each affects the final image.\nTweak guidance scale and seed value: When you are close to the desired output, experiment with different guidance scales and seed values to refine the image further.\nBy practicing and tweaking these strategies, you'll refine your prompt-writing skills and generate increasingly stunning visuals.\n</context>\n\n<user>\n{{.用户问题}}\n</user>\n"},"param_limits":null},{"data_class":"string","category":"in-port","preview":false,"define":{"id":"用户问题","name":"用户问题","desc":"用户问题","type":"TYPE_INPUT","data_type":"DATA_TYPE_STRING","required":true},"param_limits":{"types":["Sync-Any"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"支持类型: Sync-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-String"]}}],"dynamic_end_point":true},"values":{"Template":"仔细阅读以下关于文生图模型的提示词最佳实践文章（被<context></context>包裹的部分），然后尝试根据最佳实践指引对用户关于图片的描述进行改写，请注意不要丢失用户最开始的描述中的关键需求。用户的图片描述将被包裹在<user></user>这对标签中。\n注意：\n- 输出最好使用英文\n- 不要输出任何和图像描述不相关的内容\n<context>\nThe Art of Prompt Writing\nKey Elements of an Effective Prompt\nCreating eye-catching images with FLUX.1-dev hinges on your ability to write a precise and descriptive prompt. Here are some key elements to consider when crafting prompts:\n\nSpecific Details: Include clear and vivid descriptions of characters, objects, and environments to ensure accurate rendering.\nSetting: Provide contextual information about the world or scene your character inhabits.\nMood and Atmosphere: Describe the tone or emotion you wish to evoke through the imagery.\nStyle: Specify the desired artistic style, whether it's hyper-realistic, surreal, or another format.\nExample: Crafting a Warrior Character\nLet’s break down a sample prompt to showcase these principles. Imagine you want to create a warrior character within a vibrant alien landscape:\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior Character\n\nWhy This Works:\nSpecific Details: The character’s physical traits, armor type, and weapon are vividly described.\nSetting: A cliff in an alien world with moons and floating islands sets a clear context.\nMood and Atmosphere: The description of the glowing elements and vibrant colors conveys a sense of energy and life.\nStyle: By specifying a hyper-realistic style, the prompt guides the model to produce a visually sharp and immersive image.\nEvolving Your Character\nAdding New Elements: The Dining Warrior\nOnce you’ve established a basic character, you can add depth by incorporating new elements into the scene. For instance, let’s evolve our warrior by introducing a dining scene.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. With her left hand, she's bringing a piece of ethereal, glowing fruit to her mouth, about to take a bite. The fruit hovers slightly above her palm, pulsating with inner light. The background shows two moons in the sky and floating islands with waterfalls. Nearby, a floating crystal table holds an array of otherworldly delicacies. The scene is rendered in a hyper-realistic style with vibrant colors.\"\n\nWarrior with Ethereal Fruit\n\nWhy This Works:\nThe new action (eating glowing fruit) introduces a dynamic pose while maintaining consistency with the original scene.\nIt adds an element of interactivity, enhancing the visual narrative without detracting from the primary character focus.\nChanging Contexts: From Lone Warrior to War Leader\nTo further develop the warrior’s story, you might want to change her role from a solitary fighter to a leader of an army.\n\n\"A battle-hardened female warrior standing atop a crystalline cliff in a fantastical alien world. She has long, braided silver hair and glowing purple eyes. Her armor is a mix of high-tech materials and organic, bioluminescent elements. In her right hand, she wields an energy sword emitting a blue glow. The background shows two moons in the sky and floating islands with waterfalls. The scene is rendered in a hyper-realistic style with vibrant colors. She is leading a massive army of diverse alien warriors spread out below the cliff, ready for battle.\"\n\nWarrior as War Leader\n\nWhy This Works:\nThe prompt builds on the previous visual elements while introducing a new concept: leadership.\nIt expands the scene, giving the warrior more significance and context, enriching the visual output.\nTips for Mastering FLUX.1-dev\nTo consistently create remarkable images with FLUX.1-dev, consider the following best practices:\n\nBe Precise with Core Elements: Always describe your main subject with specific, vivid details.\nUse Descriptive Adjectives: Adjectives like \"crystalline,\" \"bioluminescent,\" or \"hyper-realistic\" help refine the visual outcome.\nLeave Room for Interpretation: Occasionally, being too specific can stifle creativity. Allow some open-ended aspects for the AI to explore creatively. Phrases such as \"in a fantastical alien world\" allow for creative flexibility.\nIterate on Your Prompts: Do not overchange your prompts. Change one element at a time to better understand how each affects the final image.\nTweak guidance scale and seed value: When you are close to the desired output, experiment with different guidance scales and seed values to refine the image further.\nBy practicing and tweaking these strategies, you'll refine your prompt-writing skills and generate increasingly stunning visuals.\n</context>\n\n<user>\n{{.用户问题}}\n</user>\n"}},{"id":"187de96a-c185-4149-9d56-02b03fc2c371","name":"文生图","ui":"{\"id\":\"187de96a-c185-4149-9d56-02b03fc2c371\",\"type\":\"custom\",\"position\":{\"x\":1238.8881899881785,\"y\":296.81101900013545},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":230},\"selected\":false,\"dragging\":false}","widget_id":"WidgetKeyTextToImage","widget_detail":{"id":"WidgetKeyTextToImage","name":"文生图","desc":"[文生图]: 分析文本内容生成对应图片","group":"WidgetGroupAIModel","params":[{"data_class":"json","category":"in-port","preview":false,"define":{"id":"Text","name":"文本","desc":"支持类型: Sync-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-String"]}},{"data_class":"string","category":"attribute","preview":false,"define":{"id":"ModelServer","name":"服务","desc":"服务","datasource":"{\"kind\":\"MODEL_KIND_MULTI\",\"subKind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}","required":true,"type":"TYPE_AGENT_MODEL_API","data_type":"DATA_TYPE_STRING"},"param_limits":null},{"data_class":"string","category":"attribute","preview":false,"define":{"id":"UseBase64","name":"Base64编码","desc":"生成图片是否以base64格式返回,远程及跨空间调用时需启用。","default_value":"false","required":true,"type":"TYPE_SWITCH","data_type":"DATA_TYPE_BOOLEAN"},"param_limits":null},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"支持类型: Sync-ImageGenResp: {\n  \"created\": 1739851338772,\n  \"data\": [\n    {\n      \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n      \"b64_json\": \"{b64_json}\",\n      \"revised_prompt\": \"prompt\"\n    }\n  ]\n}。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-ImageGenResp"]}}]},"values":{"ModelServer":"{\"id\":\"e0bf2980-a2f4-45eb-83ff-9b03ccf60dfb\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/uat-remote/flux1-dev?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[],\"prompt\":\"\",\"namespace\":\"dev-assets\",\"seldon_deploy_name\":\"FLUX.1-dev\",\"name\":\"FLUX.1-dev\",\"full_url\":\"http://istio-ingressgateway.istio-system/uat-remote/flux1-dev?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"FLUX.1-dev\",\"name_for_human\":\"图像生成模型:FLUX.1-dev\",\"desc\":\"按照描述生成图像\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"对图片的描述\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://**************:31380/seldon/llmops-assets/service-eeb21e39-104d-49e5-b449-243b965f1f64/8011/openai/v1/images/generations\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"},{\"name\":\"Authorization\",\"value\":\"Bearer apikey--FTfwSNCYKKdDQ7OFlfxbpneAR_PR_rZ_X8bz49O6oLi\",\"desc\":\"\"}],\"body\":\"{\\n \\\"prompt\\\": \\\"A color photograph of an astronaut riding a horse on Mars\\\",\\n \\\"model\\\": \\\"atom\\\",\\n \\\"response_format\\\": \\\"url\\\",\\n \\\"size\\\": \\\"1024x1024\\\"\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://**************:31380/seldon/llmops-assets/service-eeb21e39-104d-49e5-b449-243b965f1f64/8011/openai/v1/images/generations?path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"\",\"response_process_script\":\"\"},\"desc\":\"\",\"create_time_ms\":\"1739857357000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cuq1r5m93l1ga8e4j4gg\",\"name\":\"FLUX.1-dev\",\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"detail\":{\"desc\":\"\",\"user_id\":\"fuxin.li\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1739857357595\",\"labels\":{}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://**************:31380/seldon/llmops-assets/service-eeb21e39-104d-49e5-b449-243b965f1f64/8011/openai/v1/images/generations\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"},{\"name\":\"Authorization\",\"value\":\"Bearer apikey--FTfwSNCYKKdDQ7OFlfxbpneAR_PR_rZ_X8bz49O6oLi\",\"desc\":\"\"}],\"body\":\"{\\n \\\"prompt\\\": \\\"A color photograph of an astronaut riding a horse on Mars\\\",\\n \\\"model\\\": \\\"atom\\\",\\n \\\"response_format\\\": \\\"url\\\",\\n \\\"size\\\": \\\"1024x1024\\\"\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://**************:31380/seldon/llmops-assets/service-eeb21e39-104d-49e5-b449-243b965f1f64/8011/openai/v1/images/generations?path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"\",\"response_process_script\":\"\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"FLUX.1-dev\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"e0bf2980-a2f4-45eb-83ff-9b03ccf60dfb\",\"virtual_svc_url\":\"uat-remote/flux1-dev\",\"security_config_id\":\"cqq4ggco9vpa43qv83u0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[],\"published_api\":\"uat-remote/flux1-dev\"},\"inference_params\":[],\"logo\":\"sfs://tenants/dev-assets/projs/assets/avatar/925ad93b-9395-4d9d-90c1-43e8310ed9bb_FLUX.png\"},\"update_time_ms\":\"1739857367000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqq4ggco9vpa43qv83u0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8074474741461857:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"FLUX.1-dev\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8074474741461857:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8074474741461857:MODEL_KIND_MULTI\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_KIND_MULTI\"},\"_owner\":null},\"/\",{\"key\":\"8074474741461857:MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"FLUX.1-dev\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8074474741461857:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},null,{\"key\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#029be6\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8074474741461857:MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"e0bf2980-a2f4-45eb-83ff-9b03ccf60dfb\"}","UseBase64":true}},{"id":"2a28cee1-a81a-4641-87a6-216f330c6b27","name":"提示词","ui":"{\"id\":\"2a28cee1-a81a-4641-87a6-216f330c6b27\",\"type\":\"custom\",\"position\":{\"x\":511.2882650356322,\"y\":478.9165587049323},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":152},\"selected\":false,\"dragging\":false}","widget_id":"WidgetKeyPythonWidget","widget_detail":{"id":"WidgetKeyPythonWidget","name":"Python代码","desc":"[Python代码]: 可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖","group":"WidgetGroupCodeTool","params":[{"data_class":"string","category":"in-port","preview":false,"define":{"id":"Content","name":"输入数据","desc":"需要使用python代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}},{"data_class":"code","category":"attribute","preview":false,"define":{"id":"Code","name":"python代码","desc":"python代码，点击可查看或编辑代码","default_value":"\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n","required":true,"type":"TYPE_CODE_PYTHON","data_type":"DATA_TYPE_UNSPECIFIED","comp_props":"\"\""},"param_limits":null},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"python代码中handler函数返回的数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}}]},"values":{"Code":"\ndef handler(data: str) -> any:\n    \n    start_tag = '<think>'\n    end_tag = '</think>'\n    start_index = data.find(start_tag)\n    end_index = data.find(end_tag, start_index + len(start_tag))\n    if start_index != -1 and end_index != -1:\n        data = data[:start_index] + data[end_index + len(end_tag):]\n    return data"}},{"id":"4855faf4-63de-48e1-a779-ade91c781d67","name":"文本生成","ui":"{\"id\":\"4855faf4-63de-48e1-a779-ade91c781d67\",\"type\":\"custom\",\"position\":{\"x\":83.74936112176371,\"y\":484.76384621593536},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":318},\"selected\":false,\"dragging\":false}","widget_id":"WidgetKeyLLMModel","widget_detail":{"id":"WidgetKeyLLMModel","name":"文本生成","desc":"[文本生成]: 用于调用文本生成模型服务","group":"WidgetGroupAIModel","params":[{"data_class":"json","category":"in-port","preview":false,"define":{"id":"Text","name":"文本","desc":"输入给LLM的提示词,支持类型: Sync-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Sync-String"]}},{"data_class":"string","category":"attribute","preview":false,"define":{"id":"SystemPrompt","name":"系统提示词","desc":"模型的系统提示词，用于定义AI行为和回答方式，提高响应准确性","default_value":"You are a helpful assistant.","comp_props":"{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}","type":"TYPE_TEXTAREA","data_type":"DATA_TYPE_STRING"},"param_limits":null},{"data_class":"string","category":"attribute","preview":false,"define":{"id":"ModelServer","name":"LLM模型","desc":"可用的LLM模型服务","datasource":"{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}","required":true,"type":"TYPE_AGENT_MODEL_API","data_type":"DATA_TYPE_STRING"},"param_limits":null},{"data_class":"string","category":"attribute","preview":false,"define":{"id":"DialogHistory","name":"对话历史","desc":"对话历史","hidden":true,"type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":null},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"LLM输出的文本,支持类型: Any-String: \"text\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-String"]}}]},"values":{"SystemPrompt":"","ModelServer":"{\"id\":\"20666e21-0288-434f-8ad3-26c723ca1574\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cuiv5js2kb264a165a80/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"model\",\"name\":\"model\",\"desc\":\"模型名字，根据服务提供者的说明填写,默认为平台模型atom。若请求转换也做了替换，以请求转换为准\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"采样温度在0到2之间。较高的值(如0.8)将使输出更加随机，而较低的值(如0.2)将使输出更加集中和确定。\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.6\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\"温度抽样的另一种选择，称为核抽样，其中模型考虑具有top_p概率质量的令牌的结果。所以0.1意味着只考虑包含前10%概率质量的标记。\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0.1},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"}],\"prompt\":\"\",\"namespace\":\"dev-assets\",\"seldon_deploy_name\":\"DeepSeek-R1\",\"name\":\"DeepSeek-R1\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cuiv5js2kb264a165a80/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"DeepSeek-R1\",\"name_for_human\":\"对话模型:DeepSeek-R1\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://llmops.wuya-ai.com:31380/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"},{\"name\":\"Authorization\",\"value\":\"Bearer apikey-Kn188g1CbDOutpy27tRV7YITrEGsS27BpBwAsfDzvK2Q\",\"desc\":\"\"}],\"body\":\"{\\n  \\\"messages\\\": [\\n    {\\n      \\\"role\\\": \\\"system\\\",\\n      \\\"content\\\": \\\"你是一个善于通过推理解决用户复杂的问题的AI助手\\\"\\n    },\\n    {\\n      \\\"role\\\": \\\"user\\\",\\n      \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n    }\\n  ],\\n  \\\"model\\\": \\\"atom\\\",\\n  \\\"stream_options\\\": {\\\"include_usage\\\": true},\\n  \\\"stream\\\": true\\n}\",\"use_proxy\":true,\"proxy\":{\"scheme\":\"PROXY_SCHEME_HTTP\",\"url\":\"http://*************:3128\"},\"full_url\":\"http://llmops.wuya-ai.com:31380/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(\\r\\n    input, \\r\\n    {\\r\\n        \\\"stream_options\\\": {\\\"include_usage\\\": true}\\r\\n    }\\r\\n)\",\"response_process_script\":\"\"},\"desc\":\"\",\"create_time_ms\":\"1738929054000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cuiv5js2kb264a165a80\",\"name\":\"DeepSeek-R1\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"DeepSeek-R1 满血版本。\\n部署在 llmops.wuya 环境中，有浮云-3节点 x H20(96GB) x 8\\n\",\"user_id\":\"fuxin.li\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1739592272677\",\"labels\":{}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://llmops.wuya-ai.com:31380/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"},{\"name\":\"Authorization\",\"value\":\"Bearer apikey-Kn188g1CbDOutpy27tRV7YITrEGsS27BpBwAsfDzvK2Q\",\"desc\":\"\"}],\"body\":\"{\\n  \\\"messages\\\": [\\n    {\\n      \\\"role\\\": \\\"system\\\",\\n      \\\"content\\\": \\\"你是一个善于通过推理解决用户复杂的问题的AI助手\\\"\\n    },\\n    {\\n      \\\"role\\\": \\\"user\\\",\\n      \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n    }\\n  ],\\n  \\\"model\\\": \\\"atom\\\",\\n  \\\"stream_options\\\": {\\\"include_usage\\\": true},\\n  \\\"stream\\\": true\\n}\",\"use_proxy\":true,\"proxy\":{\"scheme\":\"PROXY_SCHEME_HTTP\",\"url\":\"http://*************:3128\"},\"full_url\":\"http://llmops.wuya-ai.com:31380/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(\\r\\n    input, \\r\\n    {\\r\\n        \\\"stream_options\\\": {\\\"include_usage\\\": true}\\r\\n    }\\r\\n)\",\"response_process_script\":\"\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"DeepSeek-R1\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"20666e21-0288-434f-8ad3-26c723ca1574\",\"virtual_svc_url\":\"remote/dev/MWH-REMOTE-SERVICE-cuiv5js2kb264a165a80/seldon/llmops-assets/service-5a1e01da-a3c4-43f9-bfb0-08e43e238d91/8000/v1/chat/completions\",\"security_config_id\":\"cqq4ggco9vpa43qv83u0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[],\"published_api\":\"\"},\"inference_params\":[{\"id\":\"model\",\"name\":\"model\",\"desc\":\"模型名字，根据服务提供者的说明填写,默认为平台模型atom。若请求转换也做了替换，以请求转换为准\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"采样温度在0到2之间。较高的值(如0.8)将使输出更加随机，而较低的值(如0.2)将使输出更加集中和确定。\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.6\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\"温度抽样的另一种选择，称为核抽样，其中模型考虑具有top_p概率质量的令牌的结果。所以0.1意味着只考虑包含前10%概率质量的标记。\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0.1},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/dev/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fdev-assets%2Fprojs%2Fassets%2Favatar%2F486b4fb2-120c-4903-970b-0e2f174a30cc_ds.png\"},\"update_time_ms\":\"1739327267000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqq4ggco9vpa43qv83u0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8074474741461857:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"DeepSeek-R1\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8074474741461857:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8074474741461857:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8074474741461857:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"DeepSeek-R1\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8074474741461857:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},null,{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8074474741461857:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8074474741461857\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"20666e21-0288-434f-8ad3-26c723ca1574\"}"}},{"id":"74751741-bf32-4ed1-9579-31d3ff3628cd","name":"思考","ui":"{\"id\":\"74751741-bf32-4ed1-9579-31d3ff3628cd\",\"position\":{\"x\":866.8802735253645,\"y\":819.0346292087064},\"selected\":false,\"zIndex\":10000,\"type\":\"custom\",\"measured\":{\"width\":320,\"height\":152},\"dragging\":false}","widget_id":"WidgetKeyPythonWidget","widget_detail":{"id":"WidgetKeyPythonWidget","name":"Python代码","desc":"[Python代码]: 可编写Python代码完成复杂的业务逻辑，预安装了requests、pandas、matplotlib依赖","group":"WidgetGroupCodeTool","params":[{"data_class":"string","category":"in-port","preview":false,"define":{"id":"Content","name":"输入数据","desc":"需要使用python代码处理的数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}},{"data_class":"code","category":"attribute","preview":false,"define":{"id":"Code","name":"python代码","desc":"python代码，点击可查看或编辑代码","default_value":"\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n","required":true,"type":"TYPE_CODE_PYTHON","data_type":"DATA_TYPE_UNSPECIFIED","comp_props":"\"\""},"param_limits":null},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"python代码中handler函数返回的数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}}]},"values":{"Code":"import re\ndef handler(data: str) -> any:\n    data= \"\"\"<think> 好的，用户给的原始描述是“两只可爱的狸花猫”。首先，我需要根据上下文中的最佳实践来改写这个提示词，同时保留用户的关键需求。 首先，检查关键元素：具体细节、场景设定、氛围、风格。用户的原描述比较简洁，需要补充更多细节。狸花猫是主体，所以需要详细描述它们的特征，比如毛色、眼睛、姿势。然后设定场景，可能是在室内还是户外？比如阳光下的花园或者窗台。氛围方面，可爱、温馨的感觉，可能需要添加柔软的光线或舒适的环境。风格可以指定为写实或卡通，但用户没提，可能需要默认或询问，但根据最佳实践例子，可能选择类似“超现实”或“生动色彩”。 接下来，考虑到用户可能需要更生动的画面，加入动态元素，比如玩耍、互动，或者周围的环境细节，比如玩具、植物。同时使用形容词如“毛茸茸的”、“炯炯有神的眼睛”来增强画面感。 还要注意不要丢失原需求的关键点：两只狸花猫，可爱。所以改写时要确保这些核心元素突出，同时添加细节让图像更丰富。例如，描述它们的姿势，是否在嬉戏，周围的环境如何，光线效果，颜色等。 可能的结构：主体（两只猫的特征）→ 动作/互动 → 环境设定 → 氛围/光线 → 风格。例如： “两只毛茸茸的狸花猫在阳光明媚的花园里嬉戏，一只正用爪子轻拍飘落的樱花花瓣，另一只好奇地抬头观望。它们有着琥珀色的大眼睛和醒目的虎斑纹路，毛发在阳光下泛着金光。背景是郁郁葱葱的绿植和木质围栏，画面风格生动逼真，色彩柔和温暖，营造出轻松愉快的春日氛围。” 这样既保留了原需求，又增加了场景、动作、细节和风格描述，符合最佳实践中的各个要素。需要检查是否有足够的形容词，是否设定了明确的场景和氛围，并指定了风格，同时保持自然流畅。 </think> 根据最佳实践优化后的提示词： \"两只毛茸茸的狸花猫在洒满阳光的木质窗台上嬉戏，琥珀色的大眼睛闪烁着调皮的光芒。其中一只正用前爪按住飘落的樱花花瓣，另一只弓着背作势欲扑，它们的虎斑纹路在阳光下泛着金棕色光泽。背景是虚化的春日花园，粉白樱花簇拥着古朴的日式庭院，画面采用温暖柔和的写实风格，绒毛细节清晰可见，整体散发着慵懒惬意的午后氛围。\" 优化点说明： 1. 补充特征细节：添加\"毛茸茸\"\"琥珀色眼睛\"\"金棕色虎斑纹路\"等视觉特征 2. 构建动态场景：通过\"按住花瓣\"\"弓背欲扑\"的动作增加画面故事性 3. 完善环境设定：加入\"日式庭院\"\"樱花\"\"木质窗台\"等东方美学元素 4. 营造氛围：用\"春日花园\"\"慵懒惬意\"\"温暖柔和\"传递治愈感 5. 明确风格要求：指定\"写实风格\"同时保留\"绒毛细节\"等关键特征\"\"\"\n\n    pattern = r'<think>(.*?)</think>'\n    match = re.search(pattern, data, re.DOTALL)\n    if match:\n        return match.group(1).strip()\n    return ''"}},{"id":"5e396be8-ce8b-48b8-86ed-649f691561fd","name":"数据合流","ui":"{\"id\":\"5e396be8-ce8b-48b8-86ed-649f691561fd\",\"type\":\"custom\",\"position\":{\"x\":1485,\"y\":600},\"zIndex\":9999,\"measured\":{\"width\":320,\"height\":108}}","widget_id":"WidgetKeyUnion","widget_detail":{"id":"WidgetKeyUnion","name":"数据合流","desc":"[数据合流]: 将上游多个输入，形成队列串行输出","group":"WidgetGroupControlFlow","params":[{"data_class":"string","category":"in-port","preview":false,"define":{"id":"Input","name":"上游数据，任意类型","desc":"待合流的上游数据，需保证上游只有一个分支有数据流入此算子，否则请用数据合并算子,支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}},{"data_class":"string","category":"out-port","preview":false,"define":{"id":"OutPut","desc":"原样输出输入的上游数据，支持类型: Any-Any: \"任意数据类型\"。","type":"TYPE_UNSPECIFIED","data_type":"DATA_TYPE_UNSPECIFIED"},"param_limits":{"types":["Any-Any"]}}]},"values":{}}],"edges":[{"id":"xy-edge__afa62d94-f25c-4f05-a74a-46ce34795282afa62d94-f25c-4f05-a74a-46ce34795282@@OutPut-32bc49b7-3bfb-46d4-8763-8b7d67aea40c32bc49b7-3bfb-46d4-8763-8b7d67aea40c@@用户问题","source":"afa62d94-f25c-4f05-a74a-46ce34795282","source_param":"afa62d94-f25c-4f05-a74a-46ce34795282@@OutPut","target":"32bc49b7-3bfb-46d4-8763-8b7d67aea40c","target_param":"32bc49b7-3bfb-46d4-8763-8b7d67aea40c@@用户问题"},{"id":"xy-edge__2a28cee1-a81a-4641-87a6-216f330c6b272a28cee1-a81a-4641-87a6-216f330c6b27@@OutPut-187de96a-c185-4149-9d56-02b03fc2c371187de96a-c185-4149-9d56-02b03fc2c371@@Text","source":"2a28cee1-a81a-4641-87a6-216f330c6b27","source_param":"2a28cee1-a81a-4641-87a6-216f330c6b27@@OutPut","target":"187de96a-c185-4149-9d56-02b03fc2c371","target_param":"187de96a-c185-4149-9d56-02b03fc2c371@@Text"},{"id":"xy-edge__32bc49b7-3bfb-46d4-8763-8b7d67aea40c32bc49b7-3bfb-46d4-8763-8b7d67aea40c@@OutPut-4855faf4-63de-48e1-a779-ade91c781d674855faf4-63de-48e1-a779-ade91c781d67@@Text","source":"32bc49b7-3bfb-46d4-8763-8b7d67aea40c","source_param":"32bc49b7-3bfb-46d4-8763-8b7d67aea40c@@OutPut","target":"4855faf4-63de-48e1-a779-ade91c781d67","target_param":"4855faf4-63de-48e1-a779-ade91c781d67@@Text"},{"id":"xy-edge__4855faf4-63de-48e1-a779-ade91c781d674855faf4-63de-48e1-a779-ade91c781d67@@OutPut-2a28cee1-a81a-4641-87a6-216f330c6b272a28cee1-a81a-4641-87a6-216f330c6b27@@Content","source":"4855faf4-63de-48e1-a779-ade91c781d67","source_param":"4855faf4-63de-48e1-a779-ade91c781d67@@OutPut","target":"2a28cee1-a81a-4641-87a6-216f330c6b27","target_param":"2a28cee1-a81a-4641-87a6-216f330c6b27@@Content"},{"id":"xy-edge__4855faf4-63de-48e1-a779-ade91c781d674855faf4-63de-48e1-a779-ade91c781d67@@OutPut-74751741-bf32-4ed1-9579-31d3ff3628cd74751741-bf32-4ed1-9579-31d3ff3628cd@@Content","source":"4855faf4-63de-48e1-a779-ade91c781d67","source_param":"4855faf4-63de-48e1-a779-ade91c781d67@@OutPut","target":"74751741-bf32-4ed1-9579-31d3ff3628cd","target_param":"74751741-bf32-4ed1-9579-31d3ff3628cd@@Content"},{"id":"xy-edge__187de96a-c185-4149-9d56-02b03fc2c371187de96a-c185-4149-9d56-02b03fc2c371@@OutPut-5e396be8-ce8b-48b8-86ed-649f691561fd5e396be8-ce8b-48b8-86ed-649f691561fd@@Input","source":"187de96a-c185-4149-9d56-02b03fc2c371","source_param":"187de96a-c185-4149-9d56-02b03fc2c371@@OutPut","target":"5e396be8-ce8b-48b8-86ed-649f691561fd","target_param":"5e396be8-ce8b-48b8-86ed-649f691561fd@@Input"},{"id":"xy-edge__74751741-bf32-4ed1-9579-31d3ff3628cd74751741-bf32-4ed1-9579-31d3ff3628cd@@OutPut-5e396be8-ce8b-48b8-86ed-649f691561fd5e396be8-ce8b-48b8-86ed-649f691561fd@@Input","source":"74751741-bf32-4ed1-9579-31d3ff3628cd","source_param":"74751741-bf32-4ed1-9579-31d3ff3628cd@@OutPut","target":"5e396be8-ce8b-48b8-86ed-649f691561fd","target_param":"5e396be8-ce8b-48b8-86ed-649f691561fd@@Input"}],"viewport":{"x":-128.46353488796012,"y":-9.66882181549579,"zoom":0.629960542714989}}}`
	type TC struct {
		ChainId     string         `json:"chain_id"`
		ChainName   string         `json:"chain_name"`
		ChainDetail *widgets.Chain `json:"chain_detail"`
	}
	tc := new(TC)
	if err := json.Unmarshal([]byte(testChain), tc); err != nil {
		t.Fatalf("json unmarshal error: %v", err)
	}
	cd := &models.AppletChainDO{
		Base: models.AppletChainBaseDO{
			ID:   tc.ChainId,
			Name: tc.ChainName,
		},
		ChainDetail: tc.ChainDetail,
	}

	ap := new(string)

	t.Run("test", func(t *testing.T) {
		widgets.Init()
		got, err := NewScriptGenerator(context.Background(), cd, ap)
		if err != nil {
			t.Errorf("NewScriptGenerator() error = %v", err)
			return
		}
		t.Logf("%+v", got)

		s, err := got.GenScript()
		if err != nil {
			t.Fatalf("gen script: %s", err)
		}

		t.Logf("%s", s)
	})
}
