package applet

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type WidgetExternalAPISvc struct {
}

func (p WidgetExternalAPISvc) ListDynamicWidgets(ctx context.Context) ([]*models.DynamicWidgetDesc, error) {
	remoteAPIs, err := clients.MWHCli.ListRemoteServices(ctx)
	if err != nil {
		return nil, err
	}
	res := remoteAPIs.ToWidgetDesc()
	return res, err
}

func (p WidgetExternalAPISvc) GetWidget(ctx context.Context, widgetKey string) (*widgets.Widget, error) {
	remoteAPIs, err := clients.MWHCli.ListRemoteServices(ctx)
	if err != nil {
		return nil, err
	}
	return remoteAPIs.GetWidgetModel(widgetKey)
}
