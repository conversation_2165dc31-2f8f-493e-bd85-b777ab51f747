package applet

//
// import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"testing"
//	"transwarp.io/applied-ai/applet-backend/clients"
//	"transwarp.io/applied-ai/applet-backend/dao"
//	"transwarp.io/applied-ai/applet-backend/pkg/models"
//	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
// )
//
// func TestGenScript(t *testing.T) {
//	chainStr := `{
//  "base": {
//    "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//    "name": "test",
//    "creator": "thinger",
//    "type": 2,
//    "desc": "123",
//    "labels": {},
//    "state": 0,
//    "deploy_info": null,
//    "update_time": 1697458302000
//  },
//  "chain_detail": {
//    "nodes": [
//      {
//        "id": "21e16fdb-c82b-4710-bf06-8b73c46dd649",
//        "widget_id": "WidgetKeyFileInput",
//        "ui": "{\"width\":400,\"height\":171,\"id\":\"21e16fdb-c82b-4710-bf06-8b73c46dd649\",\"position\":{\"x\":-480.24773469200215,\"y\":271.91909609370623},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyFileInput\",\"name\":\"文件输入\",\"desc\":\"读取文件内容\",\"group\":\"WidgetGroupInput\",\"params\":[{\"data_class\":\"file\",\"category\":\"req-input\",\"define\":{\"id\":\"FileInput\",\"name\":\"上传文件\",\"desc\":\"上传文件\",\"show\":true,\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":-480.24773469200215,\"y\":271.91909609370623},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "48b4c323-21fb-49b4-b61a-92b4a38d1d4e",
//        "widget_id": "WidgetKeyTextInput",
//        "ui": "{\"width\":400,\"height\":171,\"id\":\"48b4c323-21fb-49b4-b61a-92b4a38d1d4e\",\"position\":{\"x\":-488.4380897220211,\"y\":-111.57486384870714},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyTextInput\",\"name\":\"文本输入\",\"desc\":\"将输入的文本原样输出\",\"group\":\"WidgetGroupInput\",\"params\":[{\"data_class\":\"string\",\"category\":\"req-input\",\"define\":{\"id\":\"TextInput\",\"name\":\"文本\",\"desc\":\"文本\",\"show\":true,\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":-488.4380897220211,\"y\":-111.57486384870714},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "66b0507a-bd0c-4590-a4d8-7f470651b9a6",
//        "widget_id": "WidgetKeyTextSplitter",
//        "ui": "{\"width\":400,\"height\":305,\"id\":\"66b0507a-bd0c-4590-a4d8-7f470651b9a6\",\"position\":{\"x\":1012.7348361960469,\"y\":108.97464512850826},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyTextSplitter\",\"name\":\"文本分割\",\"desc\":\"将文本按照指定的方法分割成多个文本\",\"group\":\"WidgetGroupText\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"Text\",\"name\":\"文本\",\"desc\":\"文本\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"separator\",\"name\":\"分割符\",\"desc\":\"分割符\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"chunkSize\",\"name\":\"分割长度\",\"desc\":\"分割长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":1012.7348361960469,\"y\":108.97464512850826},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2",
//        "widget_id": "WidgetKeyVectorDBInputs",
//        "ui": "{\"width\":400,\"height\":305,\"id\":\"eeb7f040-319a-445b-8d38-7f64d5a4f4f2\",\"position\":{\"x\":486.7900029781015,\"y\":290.9645389078881},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyVectorDBInputs\",\"name\":\"添加向量\",\"desc\":\"添加向量\",\"group\":\"WidgetGroupVD\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"Text\",\"name\":\"文本\",\"desc\":\"文本\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"DataBase\",\"name\":\"数据库\",\"desc\":\"数据库\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"URL\",\"name\":\"URL\",\"desc\":\"向量数据库地址\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"json\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":486.7900029781015,\"y\":290.9645389078881},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "a19333f6-adf6-4140-8976-d9ceac5090d7",
//        "widget_id": "WidgetKeyVectorDBSearch",
//        "ui": "{\"width\":400,\"height\":305,\"id\":\"a19333f6-adf6-4140-8976-d9ceac5090d7\",\"position\":{\"x\":687.4837957875791,\"y\":-281.8882983096123},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"WidgetKeyVectorDBSearch\",\"name\":\"向量搜索\",\"desc\":\"向量搜索\",\"group\":\"WidgetGroupVD\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"SearchText\",\"name\":\"搜索文本\",\"desc\":\"搜索文本\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"DataBase\",\"name\":\"数据库\",\"desc\":\"数据库\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"attribute\",\"define\":{\"id\":\"URL\",\"name\":\"URL\",\"desc\":\"向量数据库地址\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_TEXTAREA\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":687.4837957875791,\"y\":-281.8882983096123},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "926fc661-6863-47be-96c7-c4c0c1e4c412",
//        "widget_id": "270_应用仓库模板",
//        "ui": "{\"width\":400,\"height\":229,\"id\":\"926fc661-6863-47be-96c7-c4c0c1e4c412\",\"position\":{\"x\":7.282541125074147,\"y\":120.36327467504447},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"应用仓库模板\",\"name\":\"应用仓库模板\",\"desc\":\"{{.上下文}}  根据以上背景信息，回答下面的问题： {{.用户输入}}\",\"group\":\"WidgetGroupPrompt\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"上下文\",\"name\":\"上下文\",\"desc\":\"上下文\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"用户输入\",\"name\":\"用户输入\",\"desc\":\"用户输入\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":7.282541125074147,\"y\":120.36327467504447},\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "d3ade491-4b92-4035-af36-b42c5e00f3de",
//        "widget_id": "270_应用仓库模板",
//        "ui": "{\"width\":400,\"height\":229,\"id\":\"d3ade491-4b92-4035-af36-b42c5e00f3de\",\"position\":{\"x\":1464.888365329663,\"y\":-168.79267869579547},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"应用仓库模板\",\"name\":\"应用仓库模板\",\"desc\":\"{{.上下文}}  根据以上背景信息，回答下面的问题： {{.用户输入}}\",\"group\":\"WidgetGroupPrompt\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"上下文\",\"name\":\"上下文\",\"desc\":\"上下文\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"用户输入\",\"name\":\"用户输入\",\"desc\":\"用户输入\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"positionAbsolute\":{\"x\":1464.888365329663,\"y\":-168.79267869579547},\"selected\":false,\"dragging\":false}",
//        "values": {}
//      },
//      {
//        "id": "a4224af3-9cc1-46e7-ab77-6c126e768fdd",
//        "widget_id": "1_标准提示",
//        "ui": "{\"width\":400,\"height\":249,\"id\":\"a4224af3-9cc1-46e7-ab77-6c126e768fdd\",\"position\":{\"x\":1902.5424385835338,\"y\":133.3149886684248},\"type\":\"custom\",\"data\":{\"widget\":{\"id\":\"标准提示\",\"name\":\"标准提示\",\"desc\":\"请根据我给你的{{.上下文}}、{{.问题输入}}生成{{.任务}}\",\"group\":\"WidgetGroupPrompt\",\"params\":[{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"上下文\",\"name\":\"上下文\",\"desc\":\"上下文\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"问题输入\",\"name\":\"问题输入\",\"desc\":\"问题输入\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"in-port\",\"define\":{\"id\":\"任务\",\"name\":\"任务\",\"desc\":\"任务\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}},{\"data_class\":\"string\",\"category\":\"out-port\",\"define\":{\"id\":\"OutPut\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\"}}]},\"values\":{\"name\":\"test\"}},\"selected\":false,\"positionAbsolute\":{\"x\":1902.5424385835338,\"y\":133.3149886684248},\"dragging\":false}",
//        "values": {}
//      }
//    ],
//    "edges": [
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "48b4c323-21fb-49b4-b61a-92b4a38d1d4e",
//        "source_param": "48b4c323-21fb-49b4-b61a-92b4a38d1d4e@@OutPut",
//        "target": "926fc661-6863-47be-96c7-c4c0c1e4c412",
//        "target_param": "926fc661-6863-47be-96c7-c4c0c1e4c412@@上下文"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "21e16fdb-c82b-4710-bf06-8b73c46dd649",
//        "source_param": "21e16fdb-c82b-4710-bf06-8b73c46dd649@@OutPut",
//        "target": "926fc661-6863-47be-96c7-c4c0c1e4c412",
//        "target_param": "926fc661-6863-47be-96c7-c4c0c1e4c412@@用户输入"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "926fc661-6863-47be-96c7-c4c0c1e4c412",
//        "source_param": "926fc661-6863-47be-96c7-c4c0c1e4c412@@OutPut",
//        "target": "a19333f6-adf6-4140-8976-d9ceac5090d7",
//        "target_param": "a19333f6-adf6-4140-8976-d9ceac5090d7@@SearchText"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "926fc661-6863-47be-96c7-c4c0c1e4c412",
//        "source_param": "926fc661-6863-47be-96c7-c4c0c1e4c412@@OutPut",
//        "target": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2",
//        "target_param": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2@@Text"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2",
//        "source_param": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2@@OutPut",
//        "target": "66b0507a-bd0c-4590-a4d8-7f470651b9a6",
//        "target_param": "66b0507a-bd0c-4590-a4d8-7f470651b9a6@@Text"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "66b0507a-bd0c-4590-a4d8-7f470651b9a6",
//        "source_param": "66b0507a-bd0c-4590-a4d8-7f470651b9a6@@OutPut",
//        "target": "d3ade491-4b92-4035-af36-b42c5e00f3de",
//        "target_param": "d3ade491-4b92-4035-af36-b42c5e00f3de@@用户输入"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "a19333f6-adf6-4140-8976-d9ceac5090d7",
//        "source_param": "a19333f6-adf6-4140-8976-d9ceac5090d7@@OutPut",
//        "target": "d3ade491-4b92-4035-af36-b42c5e00f3de",
//        "target_param": "d3ade491-4b92-4035-af36-b42c5e00f3de@@上下文"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "d3ade491-4b92-4035-af36-b42c5e00f3de",
//        "source_param": "d3ade491-4b92-4035-af36-b42c5e00f3de@@OutPut",
//        "target": "a4224af3-9cc1-46e7-ab77-6c126e768fdd",
//        "target_param": "a4224af3-9cc1-46e7-ab77-6c126e768fdd@@上下文"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2",
//        "source_param": "eeb7f040-319a-445b-8d38-7f64d5a4f4f2@@OutPut",
//        "target": "a4224af3-9cc1-46e7-ab77-6c126e768fdd",
//        "target_param": "a4224af3-9cc1-46e7-ab77-6c126e768fdd@@问题输入"
//      },
//      {
//        "id": "e2fe2f2a-4d6d-49b3-b382-fd74b0bfb69f",
//        "source": "21e16fdb-c82b-4710-bf06-8b73c46dd649",
//        "source_param": "21e16fdb-c82b-4710-bf06-8b73c46dd649@@OutPut",
//        "target": "a4224af3-9cc1-46e7-ab77-6c126e768fdd",
//        "target_param": "a4224af3-9cc1-46e7-ab77-6c126e768fdd@@任务"
//      }
//    ],
//    "viewport": {
//      "x": 349.32798805195796,
//      "y": 178.0943634539325,
//      "zoom": 0.5
//    }
//  },
//  "debug_info": {
//    "params": {
//      "WidgetKeyFileInput": {
//        "widget_id": "WidgetKeyFileInput",
//        "params": {
//          "FileInput": {
//            "value": "test"
//          }
//        }
//      },
//      "WidgetKeyTextInput": {
//        "widget_id": "WidgetKeyTextInput",
//        "params": {
//          "TextInput": {
//            "value": "1"
//          }
//        }
//      }
//    },
//    "result": null
//  }
// }`
//	//initTestConf()
//	clients.Init()
//	widgets.Init()
//	dao.Init()
//	Init()
//	ctx := context.Background()
//	var chainDO *models.AppletChainDO
//	if err := json.Unmarshal([]byte(chainStr), &chainDO); err != nil {
//		t.Fatal(err)
//	}
//	gen, err := NewScriptGenerator(ctx, chainDO)
//	if err != nil {
//		t.Fatal(err)
//	}
//	script, err := gen.GenScript()
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(script)
// }
