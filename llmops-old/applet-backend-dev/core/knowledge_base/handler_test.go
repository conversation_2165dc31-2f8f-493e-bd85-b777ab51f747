package knowledge_base

// func TestParsePageReq(t *testing.T) {
// 	var offset, limit int
// 	// 测试用例1：pageReq为nil，total为100
// 	// offset, limit := ParsePageReq(nil, 100)
// 	// if offset != 0 || limit != DefaultPageSize {
// 	// 	t.<PERSON>rrorf("Expected offset=0, limit=%d, but got offset=%d, limit=%d", DefaultPageSize, offset, limit)
// 	// }

// 	// 测试用例2：pageReq非nil，page和pageSize都合法，total为100
// 	pageReq := &pb.PageReq{Page: 2, PageSize: 20}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 20 || limit != 20 {
// 		t.Errorf("Expected offset=20, limit=20, but got offset=%d, limit=%d", offset, limit)
// 	}

// 	// 测试用例3：pageReq非nil，page和pageSize都非法，total为100
// 	pageReq = &pb.PageReq{Page: -1, PageSize: -1}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 0 || limit != DefaultPageSize {
// 		t.Errorf("Expected offset=0, limit=%d, but got offset=%d, limit=%d", DefaultPageSize, offset, limit)
// 	}

// 	// 测试用例4：pageReq非nil，pageSize超过最大值，total为100
// 	pageReq = &pb.PageReq{Page: 1, PageSize: 1000}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 0 || limit != 100 {
// 		t.Errorf("Expected offset=0, limit=%d, but got offset=%d, limit=%d", DefaultPageSize, offset, limit)
// 	}

// 	// 测试用例5：offset超过总记录数，total为100
// 	pageReq = &pb.PageReq{Page: 10, PageSize: 10}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 90 || limit != 10 {
// 		t.Errorf("Expected offset=0, limit=10, but got offset=%d, limit=%d", offset, limit)
// 	}

// 	// 测试用例6：offset+limit超过总记录数，total为100
// 	pageReq = &pb.PageReq{Page: 1, PageSize: 100}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 0 || limit != 100 {
// 		t.Errorf("Expected offset=0, limit=100, but got offset=%d, limit=%d", offset, limit)
// 	}

// 	// 测试用例7：倒序排序，total为100
// 	pageReq = &pb.PageReq{Page: 2, PageSize: 10, IsDesc: true}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 80 || limit != 10 {
// 		t.Errorf("Expected offset=80, limit=10, but got offset=%d, limit=%d", offset, limit)
// 	}

// 	// 测试用例8：倒序排序，total为100
// 	pageReq = &pb.PageReq{Page: 10, PageSize: 10, IsDesc: true}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 0 || limit != 10 {
// 		t.Errorf("Expected offset=0, limit=10, but got offset=%d, limit=%d", offset, limit)
// 	}

// 	// 测试用例9：倒序排序，total为100,超出范围重置第1页
// 	pageReq = &pb.PageReq{Page: 12, PageSize: 10, IsDesc: true}
// 	offset, limit = ParsePageReq(pageReq, 100)
// 	if offset != 90 || limit != 10 {
// 		t.Errorf("Expected offset=90, limit=10, but got offset=%d, limit=%d", offset, limit)
// 	}
// }
