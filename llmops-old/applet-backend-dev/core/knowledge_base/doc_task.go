package knowledge_base

import (
	"context"
	"sync"
	stdclients "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"unicode/utf8"

	"github.com/google/uuid"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	PendingPerc            float32 = 0.0  // 等待处理(排队中)的进度百分比
	StartPerc              float32 = 5.0  // 开始处理时的进度百分比
	DocProcessingPercQuota float32 = 65.0 // 文档处理的进度百分比配额
	StorePercQuota         float32 = 10.0 // 数据存储的进度百分比配额
	IndexingPercQuota      float32 = 20.0 // 索引构建的进度百分比配额

	Epsilon = 1e-10

	ErrMsgDocTaskCanceled = "doc task cacnceled"
)

var (
	tmOnce             sync.Once
	tm                 DocTaskManager
	ErrDocTaskNotFound = stderr.Internal.Errorf("doc task record not found, backend may restared before task finished")
)

// NewDocTask创建一个文档处理任务，parentCtx用于传递tenantID、projectID、token等baseInfo
func NewDocTask(parentCtx context.Context, kbId string, dpc *pb.DocProcessingConfig, doc *pb.Document) *models.DocTask {
	ctx, cancel := context.WithTimeout(context.Background(), conf.Config.KnowlhubConfig.TaskTimeout)
	ctx = helper.CopyBaseInfoBetweenContexts(parentCtx, ctx)
	return &models.DocTask{
		Ctx:                        ctx,
		Cancel:                     cancel,
		DocumentId:                 doc.DocId,
		KnowledgeBaseId:            kbId,
		DocProcessingConfig:        dpc,
		DocumentProcessingProgress: &pb.DocumentProcessingProgress{Document: doc},
	}
}

// RetryDocTask 重试文档的主流程: 停止任务、删除相关数据、重建DocTask后提交
func RetryDocTask(parentCtx context.Context, kbId, docId string, dpc *pb.DocProcessingConfig) error {
	mgr := GetTaskManger()
	mgr.Cancel(docId)
	t := mgr.Get(docId)
	if dpc == nil { // 支持重试时更新文档加工配置
		dpc = t.DocProcessingConfig
	}
	if dpc == nil {
		return stderr.BadRequest.Errorf("DocProcessingConfig of doc task is nil")
	}

	// 删除 chunks、elements的存储
	err := GetDocumentStore().ResetData(parentCtx, docId)
	if err != nil {
		return err
	}
	// 删除索引中的数据
	handler, err := NewHandlerById(parentCtx, kbId)
	if err != nil {
		return err
	}
	err = handler.DeleteChunksByDocId(parentCtx, docId)
	if err != nil {
		return err
	}

	t = NewDocTask(parentCtx, kbId, dpc, t.DocumentProcessingProgress.Document)
	return mgr.Submit(t)
}

type DocTaskManager interface {
	Create(task *models.DocTask) error
	Start(task *models.DocTask)
	// create and start a new task
	Submit(task *models.DocTask) error
	Cancel(docId string)
	Get(docId string) *models.DocTask
	ListByKnowledgeBase(kbId string) []*models.DocTask
	Store(docId string)
	SubmitRebuildIndex(task *models.DocTask) error
}

type docTaskManager struct {
	tasks *stdclients.RedisMap[models.DocTask] // docId -> *models.DocTask
	Q     *query.Query
}

func (m *docTaskManager) Create(task *models.DocTask) error {
	docId := task.DocumentProcessingProgress.Document.DocId
	actualTask, _ := m.tasks.Get(docId)
	if actualTask != nil && !actualTask.DocumentProcessingProgress.Finished {
		return stderr.BadRequest.Errorf("already exists running doc task ")
	}
	return m.tasks.Set(docId, task)
}

func (m *docTaskManager) Start(task *models.DocTask) {
	go pipeline(task.Ctx, task.KnowledgeBaseId, task.DocProcessingConfig, task.DocumentProcessingProgress)
}
func (m *docTaskManager) Submit(task *models.DocTask) error {
	if err := m.Create(task); err != nil {
		return err
	}
	m.Start(task)
	return nil
}

func (m *docTaskManager) SubmitRebuildIndex(task *models.DocTask) error {
	m.Cancel(task.DocumentId)
	if err := m.Create(task); err != nil {
		return err
	}
	go pipelineRebuildIndex(task.Ctx, task.KnowledgeBaseId, task.DocumentId, task.DocumentProcessingProgress)
	return nil
}

func (m *docTaskManager) Cancel(docId string) {
	t := m.Get(docId)
	if t.Cancel != nil {
		t.Cancel()
	}
	if t.DocumentProcessingProgress != nil {
		prog := t.DocumentProcessingProgress
		prog.Finished = true
		if prog.ErrorMessage == "" {
			prog.ErrorMessage = ErrMsgDocTaskCanceled
		}
	}
	m.Store(docId)
}

// Get 从缓存从获取文档任务，不存在则查询数据库，若都没有(可能文档处理中，还未保存时backend重启)则返回进度为0的失败进度
func (m *docTaskManager) Get(docId string) *models.DocTask {
	t, err := m.tasks.Get(docId)
	if err == nil {
		return t
	}
	repo := m.Q.DocTask
	task, err := repo.Where(repo.DocumentId.Eq(docId)).Take()
	if err == nil {
		return task
	}

	task = &models.DocTask{
		DocumentId: docId,
		DocumentProcessingProgress: &pb.DocumentProcessingProgress{
			Percentage:   0,
			Finished:     true,
			ErrorMessage: ErrDocTaskNotFound.Error(),
		},
	}
	return task
}

func (m *docTaskManager) ListByKnowledgeBase(kbId string) []*models.DocTask {
	ret := make([]*models.DocTask, 0)
	m.tasks.Range(func(docId string, docTask *models.DocTask) bool {
		if docTask.KnowledgeBaseId == kbId {
			ret = append(ret, docTask)
		}
		return true //一直遍历
	})
	return ret
}

func (m *docTaskManager) Store(docId string) {
	task := m.Get(docId)
	repo := m.Q.DocTask
	if tx := repo.UnderlyingDB().Save(task); tx.Error != nil {
		stdlog.Errorf("docTaskManager.Store failed: %v", tx.Error)
	}
}

// pipeline 是单文档的处理流水线；包含了文档解析分割、知识增强、索引构建等步骤; 通过更新prog传递处理进度
// 进度分配：5% - 70%: 文档处理（解析分割+知识增强）; 70% - 80%: 存储到数据库; 80% - 100%: 索引构建
func pipeline(ctx context.Context, kbId string, dpc *pb.DocProcessingConfig, prog *pb.DocumentProcessingProgress) {
	doc := prog.Document
	GetTaskManger().Store(doc.DocId)
	var err error
	defer func() {
		if err != nil {
			prog.ErrorMessage = err.Error()
		}
		prog.Finished = true
		GetTaskManger().Store(doc.DocId)
	}()
	prog.Percentage = PendingPerc

	proc, err := GetDocProcessor(dpc)
	if err != nil {
		err = stderr.Wrap(err, "get doc processor")
		return
	}
	rsp, err := proc(ctx, doc.FilePath, dpc, doc.TableConfig, prog, NonPreviewHeadPages)
	if err != nil {
		err = stderr.Wrap(err, "doc processor")
		return
	}

	prog.Percentage = StartPerc + DocProcessingPercQuota

	err = storeAndIndexingChunks(ctx, kbId, rsp, prog)
}

func pipelineRebuildIndex(ctx context.Context, kbId, docId string, prog *pb.DocumentProcessingProgress) {
	doc := prog.Document
	GetTaskManger().Store(doc.DocId)
	var err error
	defer func() {
		if err != nil {
			prog.ErrorMessage = err.Error()
		}
		prog.Finished = true
		GetTaskManger().Store(doc.DocId)
	}()
	prog.Percentage = PendingPerc
	err = rebuildDocIndex(ctx, kbId, docId, prog)
}

func storeAndIndexingChunks(ctx context.Context, kbId string, rsp *pb.DocSvcLoadChunkRsp, prog *pb.DocumentProcessingProgress) (err error) {
	doc := prog.Document
	// store chunks & doc elements
	prog.Stage = pb.DocumentTaskStage_DATA_STORE
	chunks := models.FromChunkPbs(rsp.Chunks, kbId, doc.DocId)
	elements := models.FromDocElementPbs(rsp.Elements)

	for _, c := range chunks {
		c.DocumentId = doc.DocId
		if c.Id == "" {
			c.Id = uuid.NewString()
		}
	}

	// get []*models.ChunkForIndexing & set cut content for too long content
	idxChunks := makeChunksForIndexing(chunks)

	err = GetChunkStore().StoreBatch(chunks)
	if err != nil {
		err = stderr.Wrap(err, "store chunks")
		return
	}

	// store numChars of document using total length of doc elements content
	var numChars int32
	for _, e := range elements {
		numChars += int32(utf8.RuneCountInString(e.Text))
	}

	if er := GetDocumentStore().StoreNumChars(doc.DocId, numChars); er != nil {
		stdlog.Errorf("failed to store numChars %d, doc_id=%s", numChars, doc.DocId)
	}

	// DeDuplicate
	elements = utils.DeDuplicate(elements, func(i int) string {
		return elements[i].Id
	})
	err = GetDocElementStore().StoreBatch(elements)
	if err != nil {
		err = stderr.Wrap(err, "store doc elements")
		return
	}

	prog.Percentage = StartPerc + DocProcessingPercQuota + StorePercQuota
	// build index
	prog.Stage = pb.DocumentTaskStage_INDEX_BUILD
	handler, err := NewHandlerById(ctx, kbId)
	if err != nil {
		err = stderr.Wrap(err, "new handler")
		return
	}

	err = handler.SubmitChunks(ctx, idxChunks)
	if err != nil {
		err = stderr.Wrap(err, "handler: submit chunks")
		return
	}

	prog.Percentage = StartPerc + DocProcessingPercQuota + StorePercQuota + IndexingPercQuota // should equals to 100.0
	prog.Stage = pb.DocumentTaskStage_DONE
	return nil
}

// rebuildDocIndex 重建文档的索引
func rebuildDocIndex(ctx context.Context, kbId, docId string, prog *pb.DocumentProcessingProgress) error {
	if prog == nil {
		return stderr.Internal.Errorf("progress is nil")
	}

	prog.Percentage = StartPerc + DocProcessingPercQuota + StorePercQuota
	prog.Stage = pb.DocumentTaskStage_INDEX_BUILD

	// get chunks from store
	chunks, err := GetChunkStore().LoadByDocument(docId)
	if err != nil {
		return stderr.Wrap(err, "get chunks")
	}

	// get handler
	handler, err := NewHandlerById(ctx, kbId)
	if err != nil {
		return stderr.Wrap(err, "new handler")
	}

	// rebuild index
	idxChunks := makeChunksForIndexing(chunks)
	err = handler.SubmitChunks(ctx, idxChunks)
	if err != nil {
		return err
	}

	// update final progress
	prog.Percentage = 100.0
	prog.Stage = pb.DocumentTaskStage_DONE
	return nil
}
func GetTaskManger() DocTaskManager {
	tmOnce.Do(func() {
		tm = &docTaskManager{
			Q:     dao.InitQuery(),
			tasks: stdclients.NewRedisMap[models.DocTask](clients.RedisCli, applet.RedisKeyKnowledgeDocTask),
		}
	})
	return tm
}
