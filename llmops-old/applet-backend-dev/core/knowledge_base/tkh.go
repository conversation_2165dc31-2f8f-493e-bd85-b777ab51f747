package knowledge_base

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/tkh-go"
)

var (
	TkhNotEnabledErr = stderr.Internal.Error("TKH is not enabled.")
)

type TKHHandler struct {
	HandlerBase
	// api       *tkh.BaseApiService
	// recallApi *tkh.ChatRecallApiService
	repoId int32
}

func (h *TKHHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {

	root, err := h.getFullTree(ctx)
	if err != nil {
		return nil, err
	}

	result := make([]*pb.DocumentInfo, 0)

	var dfs func(t *tkh.FullTree)
	dfs = func(t *tkh.FullTree) {
		node := t.Node
		if node == nil {
			return
		}
		if node.Category == nil {
			stdlog.Warnf("node.Category is nil, skipping")
			return
		}
		cat := nodeCategoryMap[*node.Category]
		switch cat { // 类别 枚举:1=目录 2=文件
		case pb.DocumentNodeCategory_DIR:
			if t.Children == nil {
				return
			}
			for _, child := range *t.Children {
				dfs(&child)
			}
		case pb.DocumentNodeCategory_FILE:
			if node.FileUuid == nil {
				stdlog.Warnf("node.FileUuid is nil, skipping")
				return
			}
			var fileSize int64
			if node.FileSize != nil {
				fileSize = *node.FileSize
			}
			doc := &pb.DocumentInfo{
				Doc: &pb.Document{DocId: *node.FileUuid, DocName: *node.Name, FileSizeBytes: int32(fileSize)},
			}
			result = append(result, doc)
		default:
			stdlog.Warnf("unknown tkh node category, skipping. node: %v", node)
		}
	}
	dfs(root)
	return &pb.ListDocumentsRsp{Result: result}, nil
}

func (h *TKHHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	root, err := h.getFullTree(ctx)
	if err != nil {
		return nil, err
	}

	docTree := tkhTree2DocTree(root)
	return &pb.GetDocumentTreeRsp{Tree: docTree}, nil
}

func (h *TKHHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	return nil, stderr.InvalidParam.Error("ListDocumentChunks is not supported in TKH")
}

func (h *TKHHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	stdlog.Errorf("SubmitFileToKnowledgeBase is not supported in TKH")
}

func (h *TKHHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	var err error
	docs := req.DocRange
	if len(docs) == 0 { // doc range为空则查询全部文档
		if docs, err = h.getAllDocIds(ctx); err != nil {
			return nil, err
		}
	}

	r := clients.TKHClient.ChatRecallApi.RecallInfinityChatRecallPost(ctx)
	sources := make([]tkh.KnowledgeSource, 0, len(docs))
	for i := range docs {
		sources = append(sources, tkh.KnowledgeSource{
			DocId: &docs[i],
		})
	}
	r = r.RecallServiceRequest(tkh.RecallServiceRequest{
		UserQueries: &[]string{req.Query},
		Sources:     &sources,
		TopK:        &req.RetrievalConfig.RecallParams.TopK,
	})
	body, httpRsp, err := r.Execute()
	if err != nil {
		return nil, stderr.Wrap(err, "tkh RecallInfinityChatRecallPost")
	}
	if httpRsp.StatusCode != 200 {
		return nil, stderr.Internal.Error("tkh RecallInfinityChatRecallPost code %v, err: %v", httpRsp.StatusCode, err)
	}

	recallRsp := new(tkh.TkhRecallResponse)
	if err = json.Unmarshal([]byte(body), recallRsp); err != nil {
		return nil, stderr.Wrap(err, "unmarshal tkh.TkhRecallResponse")
	}

	ret := new(pb.RetrieveKnowledgeBaseRsp)
	ret.Request = req
	ret.Result = make([]*pb.ChunkRetrieveResult, 0, len(*recallRsp))

	for _, item := range *recallRsp {
		ret.Result = append(ret.Result, &pb.ChunkRetrieveResult{
			Chunk:           &pb.Chunk{Content: item.PageContent},
			Score:           float32(item.Metadata.Score),
			KnowledgeBaseId: h.KnowledgeBase.Id,
			DocId:           item.Metadata.DocID,
		})
	}
	return ret, nil
}

func (h *TKHHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	// not supported
	return nil
}

func (h *TKHHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	return ErrCouldNotUpdateExternalKB
}

func (h *TKHHandler) getFullTree(ctx context.Context) (*tkh.FullTree, error) {
	req := clients.TKHClient.BaseApi.GetFullTreeByIdUsingPOST(ctx).Id(h.repoId)
	rsp, _, err := req.Execute()
	if err != nil {
		return nil, stderr.Wrap(err, "tkh GetFullTreeByIdUsingPOST")
	}
	if *rsp.Code != 200 {
		return nil, stderr.Internal.Error("tkh GetFullTreeByIdUsingPOST code %v, msg: %s", rsp.Code, *rsp.Message)
	}
	return rsp.Data, nil
}

func (h *TKHHandler) getAllDocIds(ctx context.Context) ([]string, error) {
	rsp, err := h.ListDocuments(ctx)
	if err != nil {
		return nil, err
	}
	ret := make([]string, 0, len(rsp.Result))
	for _, info := range rsp.Result {
		ret = append(ret, info.Doc.DocId)
	}
	return ret, nil
}

func (h *TKHHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	return ErrCouldNotUpdateExternalKB
}

func (h *TKHHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	return ErrCouldNotUpdateExternalKB
}

func (h *TKHHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	return ErrCouldNotUpdateExternalKB
}

func (h *TKHHandler) Drop(ctx context.Context) error {
	panic("not implemented")
}

func (h *TKHHandler) CountDocuments(ctx context.Context) (int32, error) {
	rsp, err := h.ListDocuments(ctx)
	if err != nil {
		return 0, err
	}
	return int32(len(rsp.Result)), nil
}

func NewTKHHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	if !conf.Config.TKHConfig.Enabled {
		return nil, TkhNotEnabledErr
	}
	if kb.TkhRegistry == nil {
		return nil, fmt.Errorf("NewTKHHandler: kb.TkhRegistry is nil. ")
	}
	return &TKHHandler{
		HandlerBase: HandlerBase{
			KnowledgeBase:      kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_MIXED},
		},
		repoId: kb.TkhRegistry.Id,
	}, nil
}

var nodeCategoryMap = map[int32]pb.DocumentNodeCategory{1: pb.DocumentNodeCategory_DIR, 2: pb.DocumentNodeCategory_FILE}

func tkhNode2DocNode(node *tkh.DocumentTree) *pb.DocumentNode {
	var nodeId string
	if node.Category == nil {
		return nil
	}
	cat := nodeCategoryMap[*node.Category]
	switch cat {
	case pb.DocumentNodeCategory_FILE:
		if node.FileUuid == nil {
			return nil
		}
		nodeId = *node.FileUuid
	case pb.DocumentNodeCategory_DIR:
		if node.Id == nil {
			return nil
		}
		nodeId = strconv.FormatInt(*node.Id, 10)
	default:
		stdlog.Warnf("unknown tkh node category, skipping. node: %v", node)
		return nil
	}

	return &pb.DocumentNode{
		Category: cat,
		Id:       nodeId,
		Name:     *node.Name,
	}
}

func tkhTree2DocTree(root *tkh.FullTree) *pb.DocumentTree {
	if root.Node == nil {
		return nil
	}
	ret := new(pb.DocumentTree)
	if ret.Node = tkhNode2DocNode(root.Node); ret.Node == nil {
		return nil
	}
	if root.Children != nil {
		for _, child := range *root.Children {
			sub := tkhTree2DocTree(&child)
			if sub != nil {
				ret.Children = append(ret.Children, sub)
			}
		}
	}
	return ret
}
