package knowledge_base

import (
	"sync"

	"gorm.io/gorm"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	ChunksMaxPageSize = 2000
)

type ChunkStore interface {
	Load(id string) (*models.Chunk, error)
	LoadBatch(ids []string) ([]*models.Chunk, error)
	LoadByDocument(docId string) ([]*models.Chunk, error)
	LoadByRequest(*pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error)
	LoadByKnowledgeBase(kbId string) ([]*models.Chunk, error)

	IndexAugChunk(c *models.Chunk, id string) int
	// insert or update
	Store(*models.Chunk) error
	// insert batch
	StoreBatch([]*models.Chunk) error
	Delete(id string) error
	DeleteBatch(ids []string) error
	DeleteByDocument(docId string) error
	DeleteByKnowledgeBase(kbId string) error
	CountByDocument(docId string) (int64, error)
}

var cs ChunkStore
var cso sync.Once

func GetChunkStore() ChunkStore {
	cso.Do(func() {
		cs = &chunkStore{
			Q: dao.InitQuery(),
		}
	})
	return cs
}

type chunkStore struct {
	Q *query.Query
}

func (cs *chunkStore) LoadBatch(ids []string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk
	return repo.Where(repo.Id.In(ids...)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) Load(id string) (*models.Chunk, error) {
	repo := cs.Q.Chunk
	ret, err := repo.Where(repo.Id.Eq(id)).Take()
	if err == gorm.ErrRecordNotFound {
		return nil, stderr.KnowlChunkNotFound.Errorf("id=%s", id)
	}
	return ret, nil
}

func (cs *chunkStore) Store(c *models.Chunk) error {
	repo := cs.Q.Chunk
	return repo.Save(c)
}

func (cs *chunkStore) StoreBatch(data []*models.Chunk) error {
	repo := cs.Q.Chunk
	return repo.CreateInBatches(data, 100)
}

func (cs *chunkStore) Delete(id string) error {
	repo := cs.Q.Chunk
	_, err := repo.Where(repo.Id.Eq(id)).Delete()
	return err
}
func (cs *chunkStore) DeleteBatch(ids []string) error {
	repo := cs.Q.Chunk
	_, err := repo.Where(repo.Id.In(ids...)).Delete()
	return err
}
func (cs *chunkStore) DeleteByDocument(docId string) error {
	repo := cs.Q.Chunk
	_, err := repo.Where(repo.DocumentId.Eq(docId)).Delete()
	return err
}
func (cs *chunkStore) DeleteByKnowledgeBase(kbId string) error {
	repo := cs.Q.Chunk
	_, err := repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Delete()
	return err
}

func (cs *chunkStore) LoadByDocument(docId string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk
	return repo.Where(repo.DocumentId.Eq(docId)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) LoadByRequest(req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	repo := cs.Q.Chunk
	// 不支持指定排序字段和正反序
	cond := repo.Where(repo.DocumentId.Eq(req.DocId)).Order(repo.OrderId.Asc())
	if len(req.SourceTypeSelector) > 0 {
		s := make([]int32, 0, len(req.SourceTypeSelector))
		for _, v := range req.SourceTypeSelector {
			s = append(s, int32(v))
		}
		cond = cond.Where(repo.SourceType.In(s...))
	}
	if req.SearchContent != "" {
		cond = cond.Where(repo.Content.Like("%" + req.SearchContent + "%"))
	}
	cnt, err := cond.Count()
	if err != nil {
		return nil, stderr.Internal.Errorf("chunkStore.LoadByRequest count: %v", err)
	}
	offset, limit := helper.ParsePageReq(req.PageReq, int(cnt), ChunksMaxPageSize)
	chunks, _, err := cond.FindByPage(offset, limit)
	if err != nil {
		return nil, stderr.Internal.Errorf("chunkStore.LoadByRequest find: %v", err)
	}
	ret := make([]*pb.ChunkInfo, 0, len(chunks))
	for _, c := range chunks {
		ret = append(ret, &pb.ChunkInfo{
			Chunk: c.ToPb(),
		})
	}
	rsp := &pb.ListDocumentChunksRsp{
		Result:   ret,
		Total:    int32(cnt),
		PageNum:  req.PageReq.Page,
		PageSize: req.PageReq.PageSize,
	}
	return rsp, nil
}

func (cs *chunkStore) LoadByKnowledgeBase(kbId string) ([]*models.Chunk, error) {
	repo := cs.Q.Chunk
	return repo.Where(repo.KnowledgeBaseId.Eq(kbId)).Order(repo.OrderId.Asc()).Find()
}

func (cs *chunkStore) IndexAugChunk(c *models.Chunk, id string) int {
	for i, augChunk := range c.AugmentedChunks {
		if augChunk.Id == id {
			return i
		}
	}
	return -1
}
func (cs *chunkStore) CountByDocument(docId string) (int64, error) {
	repo := cs.Q.Chunk
	return repo.Where(repo.DocumentId.Eq(docId)).Count()
}
