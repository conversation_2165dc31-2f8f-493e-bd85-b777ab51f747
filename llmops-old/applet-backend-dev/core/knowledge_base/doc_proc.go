package knowledge_base

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"github.com/google/uuid"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/applet-backend/clients"
)

const (
	FormatTabelFieldChunk = "%s:%s" // 表格字段chunk的格式, 列名:值
)

var (
	ErrUnknowDocProcessingStrategyOrigin = stderr.Internal.Error("unknow doc processing strategy origin")

	// 文档加载分段、知识增强 各占文档处理进度的一半
	DocLoadChunkPercQuota = DocProcessingPercQuota * 0.5
	ChunkAugPercQuota     = DocProcessingPercQuota * 0.5

	// 用于控制调用文档解析的并发度，需要在init.go中初始化
	sem *stdsync.Weighted
)

// DocProcessor定义了不同策略处理文档的接口，当prog不为空时更新进度，处理成功共增加值30
type DocProcessor func(ctx context.Context, filePath string, dpc *pb.DocProcessingConfig, tc *pb.TableConfig, prog *pb.DocumentProcessingProgress, headPages int) (*pb.DocSvcLoadChunkRsp, error)

func GetDocProcessor(dpc *pb.DocProcessingConfig) (f DocProcessor, err error) {
	switch dpc.DocProcessingStrategyOrigin {
	case pb.StrategyOrigin_PRESET:
		f = DocPresetProcessor
	case pb.StrategyOrigin_CUSTOM:
		f = DocCustomProcessor
	default:
		err = ErrUnknowDocProcessingStrategyOrigin
	}
	return
}

var DocCustomProcessor DocProcessor = func(ctx context.Context, filePath string, dpc *pb.DocProcessingConfig, tc *pb.TableConfig, prog *pb.DocumentProcessingProgress, headPages int) (*pb.DocSvcLoadChunkRsp, error) {
	// 应用服务暂时不能传预览的参数,不能更新进度
	if dpc.AppletService == nil {
		return nil, stderr.Internal.Errorf("dpc.AppletService is nil")
	}
	if prog != nil {
		prog.Stage = pb.DocumentTaskStage_LOAD_CHUNK
	}
	svcStart := time.Now()
	rsp, err := clients.AppletSvcCli.CallChunkingStrategySvc(ctx, dpc.AppletService.ServiceId, filePath)
	if err != nil {
		return nil, stderr.Wrap(err, "call chunking strategy svc")
	}
	addProgPercentage(prog, DocLoadChunkPercQuota)
	stdlog.Infof("Doc[%s] AppletService costs %v seconds", filePath, time.Since(svcStart).Seconds())

	// 自定义策略也可以接知识增强
	if len(rsp.Chunks) > 0 && dpc.ChunkAugmentConfig != nil && dpc.ChunkAugmentConfig.Enabled {
		if prog != nil {
			prog.Stage = pb.DocumentTaskStage_KNOWLEDGE_AUGMENT
		}
		augStart := time.Now()
		unit := ChunkAugPercQuota / float32(len(rsp.Chunks))
		err = augmentChunksWithProg(ctx, rsp.Chunks, dpc.ChunkAugmentConfig, prog, unit)
		if err != nil {
			return nil, stderr.Wrap(err, "augment chunks")
		}
		stdlog.Infof("Doc[%s] augmentation costs %v seconds", filePath, time.Since(augStart).Seconds())
	}

	return rsp, nil
}

var DocPresetProcessor DocProcessor = func(ctx context.Context, filePath string, dpc *pb.DocProcessingConfig, tc *pb.TableConfig, prog *pb.DocumentProcessingProgress, headPages int) (rsp *pb.DocSvcLoadChunkRsp, err error) {

	// doc parse & chunk

	if dpc.AutoConfigured {
		dpc = DefaultDocProcessingConfig
	}

	if tc == nil { // 文本知识库
		// 需要控制调用解析服务的最大并发度
		if headPages > NonPreviewHeadPages { // 预览不用排队
			setProg(prog, StartPerc, pb.DocumentTaskStage_LOAD_CHUNK)
			rsp, err = loadChunksFromTextDoc(ctx, filePath, dpc, headPages)
		} else {
			rsp, err = loadChunksFromTextDocWithConcurrencyControl(ctx, filePath, dpc, headPages, prog)
		}
	} else { //表格知识库
		setProg(prog, StartPerc, pb.DocumentTaskStage_LOAD_CHUNK)
		rsp, err = loadChunksFromTableDoc(ctx, filePath, tc, prog.Document.DocumentFileSource)
	}
	if err != nil {
		return nil, err
	}

	addProgPercentage(prog, DocLoadChunkPercQuota)

	// augmentation
	if dpc.ChunkAugmentConfig != nil && (dpc.ChunkAugmentConfig.Enabled || dpc.ChunkAugmentConfig.ImageEnable) && headPages <= 0 { // disable aug when preview
		if prog != nil {
			prog.Stage = pb.DocumentTaskStage_KNOWLEDGE_AUGMENT
		}
		augStart := time.Now()

		unit := ChunkAugPercQuota / float32(len(rsp.Chunks))
		err = augmentChunksWithProg(ctx, rsp.Chunks, dpc.ChunkAugmentConfig, prog, unit)
		if err != nil {
			return nil, stderr.Wrap(err, "augment chunks")
		}
		stdlog.Infof("Doc[%s] augmentation costs %v seconds", filePath, time.Since(augStart).Seconds())
	}
	return rsp, nil
}

func loadChunksFromTextDocWithConcurrencyControl(ctx context.Context, filePath string, dpc *pb.DocProcessingConfig, headPages int, prog *pb.DocumentProcessingProgress) (rsp *pb.DocSvcLoadChunkRsp, err error) {
	if prog != nil {
		semId := uuid.New().String()
		acquired := make(chan struct{})
		go func() {
			t := time.NewTicker(3 * time.Second)
			defer t.Stop()
			for range t.C {
				select {
				case <-acquired:
					return
				default:
					pos, size, ok := sem.GetWaitPos(semId)
					if ok {
						prog.ErrorMessage = fmt.Sprintf("排队进度 %d/%d", pos, size)
					}
				}
			}
		}()
		err = sem.AcquireWithId(ctx, 1, semId)
		acquired <- struct{}{}
		prog.ErrorMessage = ""
	} else {
		err = sem.Acquire(ctx, 1)
	}
	if err != nil {
		return nil, err
	}
	defer sem.Release(1)
	setProg(prog, StartPerc, pb.DocumentTaskStage_LOAD_CHUNK)
	return loadChunksFromTextDoc(ctx, filePath, dpc, headPages)
}

func loadChunksFromTextDoc(ctx context.Context, filePath string, dpc *pb.DocProcessingConfig, headPages int) (rsp *pb.DocSvcLoadChunkRsp, err error) {
	docStart := time.Now()
	docLoadSplitReq, err := NewDocLoadSplitReq(ctx, dpc, filePath, headPages)
	if err != nil {
		return nil, stderr.Wrap(err, "make doc req error.")
	}
	rsp, err = GetDocHandler().LoadChunks(ctx, docLoadSplitReq)
	if err != nil {
		return nil, stderr.Wrap(err, "call LoadChunks")
	}
	stdlog.Infof("Doc[%s] load split costs %v seconds, %d chunks", filePath, time.Since(docStart).Seconds(), len(rsp.Chunks))

	// handle table chunks
	err = GetDocHandler().HandleTableChunks(ctx, rsp)
	if err != nil {
		return nil, stderr.Wrap(err, "handle table chunks")
	}
	return
}

func loadChunksFromTableDoc(ctx context.Context, filepath string, tc *pb.TableConfig, docFileSource pb.DocumentFileSource) (rsp *pb.DocSvcLoadChunkRsp, err error) {
	if tc == nil {
		return nil, stderr.Internal.Errorf("TableConfig is nil")
	}
	if len(tc.FieldsConfig) == 0 {
		return nil, stderr.Internal.Errorf("TableConfig.FieldsConfig is nil")
	}
	var data TableContentLoadRsp
	if docFileSource == pb.DocumentFileSource_LOCAL_FILE {
		req := &TableContentLoadReq{
			SheetName:       tc.SheetName,
			Filepath:        filepath,
			HeaderRow:       tc.HeaderRow,
			DataStartingRow: tc.DataStartingRow,
		}
		data, err = GetDocHandler().LoadTable(ctx, req) // key: col name, value: array of values
		if err != nil {
			return nil, stderr.Wrap(err, "call LoadTable")
		}
	} else if docFileSource == pb.DocumentFileSource_CORPUS_DATA_SET {
		// 语料数据源，解析parquet
		absPath, err := stdfs.GetSFSLocalPath(filepath)
		if err != nil {
			return nil, err
		}
		mapList, err := helper.ReadParquetFileAsMap(absPath, false)
		if err != nil {
			return nil, err
		}
		// 转成key -> []values格式，同时检查key的个数
		data, err = transferMapList(mapList)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, stderr.Internal.Errorf("Unsupported docuemnt file source [%s]", docFileSource)
	}
	// 校验data的所有列长度一致
	var size int
	for _, v := range data {
		if size == 0 {
			size = len(v)
		} else if size != len(v) {
			return nil, stderr.Internal.Errorf("table %s has different column size", filepath)
		}
	}

	contentFields := make([]*pb.TableFieldConfig, 0) // 需要参与召回内容的字段
	idxFields := make([]*pb.TableFieldConfig, 0)     // 需要索引的字段
	for _, c := range tc.FieldsConfig {
		if _, ok := data[c.OriName]; !ok {
			return nil, stderr.Internal.Errorf("field %s not found in table %s", c.OriName, filepath)
		}
		if c.EnableIndex {
			idxFields = append(idxFields, c)
		}
		if c.EnableContentRecall {
			contentFields = append(contentFields, c)
		}
	}

	augChunks := make([][]*pb.AugmentedChunk, size)
	for _, f := range idxFields {
		col := data[f.OriName]
		for i, v := range col {
			content := fmt.Sprintf(FormatTabelFieldChunk, f.Name, v)
			c := &pb.AugmentedChunk{
				Id:            uuid.New().String(),
				Content:       content,
				SourceType:    pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
				AugmentedType: pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD,
			}
			augChunks[i] = append(augChunks[i], c)
		}
	}

	contentMaps := make([]map[string]string, size)
	for i := range contentMaps {
		contentMaps[i] = make(map[string]string)
	}
	for _, f := range contentFields {
		col := data[f.OriName]
		for i, v := range col {
			m := contentMaps[i]
			m[f.Name] = v
		}
	}

	ret := make([]*pb.Chunk, 0, size)
	for i := 0; i < size; i++ {
		buffer := new(bytes.Buffer)
		encoder := json.NewEncoder(buffer)
		encoder.SetEscapeHTML(false)
		err := encoder.Encode(contentMaps[i])
		if err != nil {
			return nil, err
		}
		ret = append(ret, &pb.Chunk{
			Id:                      uuid.New().String(),
			Content:                 buffer.String(),
			SourceType:              pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
			ContentType:             pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_JSON,
			DisableVectorIndexing:   true,
			DisableFullTextIndexing: true,
			AugmentedChunks:         augChunks[i],
		})
	}
	return &pb.DocSvcLoadChunkRsp{Chunks: ret}, nil
}

func transferMapList(data []map[string]string) (TableContentLoadRsp, error) {
	res := make(map[string][]string)
	size := 0
	for _, v := range data {
		if size == 0 {
			size = len(v)
		} else if size != len(v) {
			return nil, stderr.Internal.Errorf("parquet file has different column size")
		}
		for key, value := range v {
			res[key] = append(res[key], value)
		}
	}
	return res, nil
}

func setProg(prog *pb.DocumentProcessingProgress, perc float32, stage pb.DocumentTaskStage) {
	if prog != nil {
		prog.Percentage = perc
		prog.Stage = stage
	}
}

func addProgPercentage(prog *pb.DocumentProcessingProgress, percDelta float32) {
	if prog != nil {
		prog.Percentage += percDelta
	}
}

func isDocFailed(prog *pb.DocumentProcessingProgress) bool {
	return prog != nil && prog.Finished && prog.Percentage < 100
}
