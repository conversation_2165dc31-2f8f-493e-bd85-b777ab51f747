package knowledge_base

import (
	"context"
	"sync"
	"transwarp.io/applied-ai/applet-backend/conf"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

// InnerHandler 用于处理内部知识库的入库与检索
// 可以根据配置切换不同的向量和全文引擎
type InnerHandler struct {
	*HandlerBase
	vector    VectorHandler
	fulltext  FullTextHandler
	docEngine *DocEngineHandler
}

func (h *InnerHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.DocumentInfo, 0, len(docs))

	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		docEngineDocs, err := h.docEngine.ListDocuments(ctx)
		if err != nil {
			return nil, err
		}
		for _, doc := range docEngineDocs.Result {
			ret = append(ret, doc)
		}
	} else {
		var wg sync.WaitGroup
		wg.Add(len(docs))
		for _, doc := range docs {
			strategyOrigin := new(pb.StrategyOrigin)
			if doc.DocProcessingConfig != nil {
				strategyOrigin = &doc.DocProcessingConfig.DocProcessingStrategyOrigin
			}
			di := &pb.DocumentInfo{
				Doc:            doc.ToPb(),
				Prog:           GetTaskManger().Get(doc.Id).DocumentProcessingProgress,
				StrategyOrigin: *strategyOrigin,
			}
			go func(di *pb.DocumentInfo) {
				defer wg.Done()
				if cnt, err := GetChunkStore().CountByDocument(di.Doc.DocId); err != nil {
					stdlog.WithError(err).Warnf("count num_chunks of doc %s", di.Doc.DocId)
				} else {
					di.NumChunks = cnt
				}
			}(di)
			ret = append(ret, di)
		}
		wg.Wait()
	}
	return &pb.ListDocumentsRsp{Result: ret}, nil
}

func (h *InnerHandler) CountDocuments(ctx context.Context) (int32, error) {
	return GetDocumentStore().CountByKnowledgeBase(h.KnowledgeBase.Id)
}

func (h *InnerHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	// 平铺展开树即可
	//    [dir]知识库名称
	//           |
	// [file]doc1, [file]doc2, ...

	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.GetDocumentTree(ctx)
	} else {
		root := new(pb.DocumentTree)
		root.Node = &pb.DocumentNode{
			Category: pb.DocumentNodeCategory_DIR,
			Id:       h.KnowledgeBase.Id,
			Name:     h.KnowledgeBase.Name,
		}

		docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
		if err != nil {
			return nil, err
		}
		for _, doc := range docs {
			subTree := &pb.DocumentTree{
				Node: &pb.DocumentNode{
					Category: pb.DocumentNodeCategory_FILE,
					Id:       doc.Id,
					Name:     doc.Name,
				},
			}
			root.Children = append(root.Children, subTree)
		}
		if len(docs) == 0 {
			subTree := &pb.DocumentTree{
				Node: &pb.DocumentNode{
					Category: pb.DocumentNodeCategory_FILE,
					Id:       "全量文本",
					Name:     "全量文本",
				},
			}
			root.Children = append(root.Children, subTree)
		}
		return &pb.GetDocumentTreeRsp{Tree: root}, nil
	}
}

func (h *InnerHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	ret := make([]*pb.ChunkInfo, 0)
	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.ListDocumentChunks(ctx, docId, pageReq)
	} else {
		chunks, err := GetChunkStore().LoadByDocument(docId)
		if err != nil {
			return nil, err
		}
		for _, chunk := range chunks {
			ret = append(ret, &pb.ChunkInfo{
				Chunk: chunk.ToPb(),
			})
		}
		rsp := &pb.ListDocumentChunksRsp{
			Result: ret,
			Total:  int32(len(ret)),
		}
		return rsp, nil
	}
}

func (h *InnerHandler) ListDocumentChunksByReq(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.ListDocumentChunksByReq(ctx, req)
	} else {
		rsp, err := GetChunkStore().LoadByRequest(req)
		if err != nil {
			return nil, err
		}
		return rsp, nil
	}
}

func (h *InnerHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented") // TODO: Implement
}

func (h *InnerHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (rsp *pb.RetrieveKnowledgeBaseRsp, err error) {
	if req.RetrievalConfig == nil {
		req.RetrievalConfig = h.KnowledgeBase.RetrievalConfig
	}
	if req.RetrievalConfig == nil {
		return nil, stderr.BadRequest.Error("retrieval config is nil")
	}

	switch req.RetrievalConfig.Strategy {
	case pb.KnowledgeBaseRetrieveStrategy_VECTOR:
		rsp, err = h.vector.Recall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT:
		rsp, err = h.fulltext.Recall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_MIXED:
		rsp, err = h.MixedRecall(ctx, req)
	case pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE:
		rsp, err = h.docEngine.Recall(ctx, req)
	default:
		err = stderr.BadRequest.Error("unsupported retrieval strategy")
	}

	if err != nil {
		return nil, err
	}

	// set kb name and doc name
	if req.RetrievalConfig.Strategy == pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE {
		kbName := h.KnowledgeBase.Name
		for _, item := range rsp.Result {
			item.KnowledgeBaseName = kbName
		}
	} else {
		rsp.Result, err = fillRetrieveResults(rsp.Result)
		if err != nil {
			return nil, err
		}

		// set kb name and doc name
		docs, err := GetDocumentStore().LoadByKnowledgeBase(h.KnowledgeBase.Id)
		if err != nil {
			stdlog.Errorf("InnerHandler Recall: GetDocumentStore.LoadByKnowledgeBase: %v", err)
		}
		docsNameMp := make(map[string]string, len(docs))
		for _, doc := range docs {
			docsNameMp[doc.Id] = doc.Name
		}
		kbName := h.KnowledgeBase.Name
		for _, item := range rsp.Result {
			item.KnowledgeBaseName = kbName
			item.DocName = docsNameMp[item.DocId]
		}
	}

	return rsp, nil

}

func (h *InnerHandler) MixedRecall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (rsp *pb.RetrieveKnowledgeBaseRsp, err error) {
	rsp1, err := h.vector.Recall(ctx, req)
	if err != nil {
		return nil, err
	}
	rsp2, err := h.fulltext.Recall(ctx, req)
	if err != nil {
		return nil, err
	}

	// mix
	mixed := mixRetrieveResults(append(rsp1.Result, rsp2.Result...))

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            mixed,
		VectorIndexHits:   rsp1.VectorIndexHits,
		FullTextIndexHits: rsp2.FullTextIndexHits,
	}, nil
}

func (h *InnerHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if err := h.fulltext.DeleteChunksById(ctx, chunkIds); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksById(ctx, chunkIds); err != nil {
		stdlog.Error(err)
	}

	return nil
}

func (h *InnerHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if err := h.fulltext.DeleteChunksByOriId(ctx, oriChunkIds); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksByOriId(ctx, oriChunkIds); err != nil {
		stdlog.Error(err)
	}

	return nil
}

// DeleteChunksByDocId 删除文档相关的索引，还包括数据库中的数据
func (h *InnerHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	if err := GetChunkStore().DeleteByDocument(docId); err != nil {
		return err
	}
	if err := GetDocElementStore().DeleteByDocument(docId); err != nil {
		return err
	}

	// TODO return error比打印日志更合理点？
	if err := h.fulltext.DeleteChunksByDocId(ctx, docId); err != nil {
		stdlog.Error(err)
	}
	if err := h.vector.DeleteChunksByDocId(ctx, docId); err != nil {
		stdlog.Error(err)
	}
	return nil
}

func (h *InnerHandler) Drop(ctx context.Context) error {
	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		return h.docEngine.Drop(ctx)
	} else {
		err := h.vector.Drop(ctx)
		if err != nil {
			return err
		}
		return h.fulltext.Drop(ctx)
	}
}

// fillRetrieveResults 根据id从ChunkStore读取对应内容并填充
func fillRetrieveResults(data []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	ids := make([]string, 0, len(data))
	for _, item := range data {
		ids = append(ids, item.Chunk.Id)
	}
	chunks, err := GetChunkStore().LoadBatch(ids)
	if err != nil {
		return nil, err
	}
	chunksMp := make(map[string]*models.Chunk, len(chunks))
	for _, chunk := range chunks {
		chunksMp[chunk.Id] = chunk
	}

	ret := make([]*pb.ChunkRetrieveResult, 0, len(data))
	for _, item := range data {
		chunk, ok := chunksMp[item.Chunk.Id]
		if !ok {
			stdlog.Errorf("fillRetrieveResults: chunk %s not found", item.Chunk.Id)
			continue
		}
		item.Chunk = chunk.ToPb()
		item.DocId = chunk.DocumentId
		item.KnowledgeBaseId = chunk.KnowledgeBaseId
		ret = append(ret, item)
	}
	return ret, nil
}

func mixRetrieveResults(data []*pb.ChunkRetrieveResult) []*pb.ChunkRetrieveResult {
	// chunk id -> weighted score
	mp := map[string]float32{}

	for _, item := range data {
		id := item.Chunk.Id
		if exScore, ok := mp[id]; ok {
			mp[id] = exScore*0.5 + item.Score*0.5
		} else {
			mp[id] = item.Score
		}
	}

	merged := make([]*pb.ChunkRetrieveResult, 0, len(mp))
	for id, score := range mp {
		merged = append(merged, &pb.ChunkRetrieveResult{
			Chunk: &pb.Chunk{
				Id: id,
			},
			Score: score,
		})
	}
	return merged
}

func (h *InnerHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	if h.KnowledgeBase.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		err := h.docEngine.RemoveFileFromKnowledgeBase(ctx, req)
		if err != nil {
			stdlog.Error("InnerHandler DocEngine.RemoveFileFromKnowledgeBase:", err)
		}
		return err
	} else {
		if err := h.vector.RemoveFileFromKnowledgeBase(ctx, req); err != nil {
			stdlog.Error("InnerHandler hippo.RemoveFileFromKnowledgeBase:", err)
		}
		if err := h.fulltext.RemoveFileFromKnowledgeBase(ctx, req); err != nil {
			stdlog.Error("InnerHandler scope.RemoveFileFromKnowledgeBase:", err)
		}

		if err := GetDocumentStore().Delete(ctx, req.DocId); err != nil {
			return err
		}
		return nil
	}
}

func (h *InnerHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) (err error) {

	if err = h.vector.SubmitChunks(ctx, chunks); err != nil {
		return err
	}

	if err = h.fulltext.SubmitChunks(context.WithValue(ctx, AlreadySubmitVector, "true"), chunks); err != nil {
		return err
	}
	return nil
}

func NewInnerHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	handler := &InnerHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase: kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
				pb.KnowledgeBaseRetrieveStrategy_MIXED,
				pb.KnowledgeBaseRetrieveStrategy_VECTOR,
				pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT,
				pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE,
			},
		},
	}
	vectorEngine, err := GetVectorConstructor(conf.Config.KnowlhubConfig.VectorEngine)
	if err != nil {
		return nil, err
	}
	fulltextEngine, err := GetFullTextConstructor(conf.Config.KnowlhubConfig.FullTextEngine)
	if err != nil {
		return nil, err
	}
	vector, err := vectorEngine(ctx, kb)
	if err != nil {
		return nil, stderr.Wrap(err, "new inner vector handler")
	}
	fulltext, err := fulltextEngine(ctx, kb)
	if err != nil {
		return nil, stderr.Wrap(err, "new inner fulltext handler")
	}
	docEngine, err := NewDocEngineHandler(ctx, kb)

	handler.vector = vector
	handler.fulltext = fulltext
	handler.docEngine = docEngine.(*DocEngineHandler)
	return handler, nil
}
