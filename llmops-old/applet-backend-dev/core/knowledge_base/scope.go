package knowledge_base

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/elastic/go-elasticsearch/v6"
	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	TermsTitle = "terms_title"
)

var (
	checkIndexLockMp = stdsync.NewLockMap()
)

type ScopeHandler struct {
	*HandlerBase
	initOnce   sync.Once
	Cli        *stdes.Client
	IndexName  string
	TextField  string // 文本内容字段名
	TitleField string // 文档标题字段名
	IdField    string
	OriIdField string
	IsExternal bool
}

func (h *ScopeHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "ScopeHandler.getDocumentRows")
	}
	result := make([]*pb.DocumentInfo, 0, len(docRowsMap))
	for doc, count := range docRowsMap {
		result = append(result, &pb.DocumentInfo{
			Doc: &pb.Document{
				DocId:   doc,
				DocName: doc,
			},
			NumChunks: int64(count),
		})
	}
	return &pb.ListDocumentsRsp{Result: result}, nil
}

func (h *ScopeHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	root := new(pb.DocumentTree)
	root.Node = &pb.DocumentNode{
		Category: pb.DocumentNodeCategory_DIR,
		Id:       h.KnowledgeBase.Id,
		Name:     h.KnowledgeBase.Name,
	}

	docRowsMap, err := h.getDocumentRows(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "ScopeHandler.getDocumentRows")
	}
	for doc, _ := range docRowsMap {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       doc,
				Name:     doc,
			},
		}
		root.Children = append(root.Children, subTree)
	}
	if len(docRowsMap) == 0 {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       "全量文本",
				Name:     "全量文本",
			},
		}
		root.Children = append(root.Children, subTree)
	}
	return &pb.GetDocumentTreeRsp{Tree: root}, nil
}

func (h *ScopeHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				h.TitleField: docId,
			},
		},
	}
	rsp := new(stdes.RecallResp)
	err := h.Cli.Search(ctx, h.IndexName, query, rsp)
	if err != nil {
		return nil, err
	}

	var chunks []*pb.ChunkInfo
	for _, hit := range rsp.Hits.Hits {
		item := hit.Source
		text := item[h.TextField].(string)
		chunk := pb.Chunk{
			Id:      hit.ID,
			Content: text,
		}
		displays := []*pb.DisplayInfo{}
		for k, v := range item {
			if k == h.TextField {
				continue
			}
			displays = append(displays, &pb.DisplayInfo{Name: k, Value: fmt.Sprintf("%v", v)})
		}
		chunks = append(chunks, &pb.ChunkInfo{
			Chunk:        &chunk,
			DisplayInfos: displays,
		})
	}
	return &pb.ListDocumentChunksRsp{
		Result: chunks,
	}, nil

}

func (h *ScopeHandler) checkIndex(ctx context.Context) error {
	lock := checkIndexLockMp.Get(h.KnowledgeBase.Id)
	lock.Lock()
	defer lock.Unlock()
	ex, err := h.Cli.IndexExists(ctx, h.IndexName)
	if err != nil {
		return err
	}
	if !ex {
		err = h.Cli.CreateIndex(ctx, h.IndexName, DefaultMapping)
		if err != nil {
			return err
		}
	}
	return nil
}

func (h *ScopeHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	err := h.initStore(ctx)
	if err != nil {
		return err
	}
	startTime := time.Now()
	// create index if not exists

	// 过滤掉不需要全文索引的chunks
	chunks = utils.FilterSlice(chunks, func(i int) bool { return !chunks[i].DisableFullTextIndexing })
	cnt := 0
	batches := utils.BatchifySlice(chunks, conf.Config.KnowlhubConfig.FullTextIndexingBatchSize)
	for _, batch := range batches {
		bulkOps := make([]*stdes.BulkOperation, 0, len(batch))
		for _, chunk := range batch {
			cnt += 1
			op := &stdes.BulkOperation{
				Action: stdes.BulkOpIndex,
				Source: stdes.BulkOpSource{},
				Data:   makeIndexBody(chunk),
			}
			bulkOps = append(bulkOps, op)
		}

		err = h.Cli.Bulk(ctx, h.IndexName, bulkOps)
		if err != nil {
			return err
		}
	}
	stdlog.Infof("SubmitChunks[scope], total costs: %v seconds, inserted %d chunks", time.Since(startTime).Seconds(), cnt)
	return nil
}

func makeIndexBody(c *models.ChunkForIndexing) map[string]any {
	ret := make(map[string]any)
	ret[InnerIdField] = c.Id
	ret[InnerTextField] = c.Text
	ret[InnerOriIdField] = c.OriId
	ret[InnerTitleField] = c.DocId
	return ret
}

func (h *ScopeHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented")
}

func (h *ScopeHandler) Drop(ctx context.Context) error {
	if h.IsExternal {
		return ErrCouldNotUpdateExternalKB
	}
	if err := h.initStore(ctx); err != nil {
		return err
	}

	return h.Cli.DeleteIndex(ctx, h.IndexName)
}

// Recall 从全文索引召回query相关的文档
// 外部库的text字段即为Chunk内容；内部库召回以Chunk Id为准(text可能仅为索引内容，非召回内容)，后续需从Chunk Store查询取回Chunk
func (h *ScopeHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	if err := h.initStore(ctx); err != nil {
		return nil, err
	}
	rc := req.RetrievalConfig
	params := rc.RecallParams

	mustConds := []map[string]any{}
	// text match
	mustConds = append(mustConds, map[string]any{
		"match": map[string]interface{}{
			h.TextField: req.Query,
		}})

	// docId in range
	if len(req.DocRange) > 0 {
		termsCond := map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): req.DocRange,
			},
		}
		mustConds = append(mustConds, termsCond)
	}

	query := map[string]interface{}{
		"size":      params.TopK,
		"min_score": params.ScoreThreshold,
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustConds,
			},
		},
	}

	if bs, err := json.Marshal(query); err == nil {
		stdlog.Infof("scope search query:\n%s", string(bs))
	}

	rsp := new(stdes.RecallResp)
	err := h.Cli.Search(ctx, h.IndexName, query, rsp)
	if err != nil {
		return nil, err
	}

	if rsp.Hits == nil {
		return nil, stderr.Internal.Error("ScopeHandler.Recall: rsp.Hits is nil")
	}

	// normalize hits score
	normalizeSearchHits(rsp.Hits.Hits)

	// construct results
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(rsp.Hits.Hits))
	hitsMap := make(map[string]*pb.Hit)
	if h.IsExternal {
		for _, hit := range rsp.Hits.Hits {
			hitsMap[hit.ID] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{
					Id:      hit.ID,
					Content: hit.Source[h.TextField].(string),
				},
				Score:           hit.Score,
				DocId:           hit.Source[h.TitleField].(string),
				KnowledgeBaseId: h.KnowledgeBase.Id,
			})
		}
	} else {
		// oriId -> maxScore
		mp := map[string]float32{}

		for _, hit := range rsp.Hits.Hits {
			id := hit.Source[h.IdField].(string)
			oriId := hit.Source[h.OriIdField].(string)
			hitsMap[id] = &pb.Hit{
				Hit:   true,
				Score: hit.Score,
			}
			if exScore, ok := mp[oriId]; ok && exScore > hit.Score {
				continue
			} else {
				mp[oriId] = hit.Score
			}
		}
		for oriId, maxScore := range mp {
			chunks = append(chunks, &pb.ChunkRetrieveResult{
				Chunk: &pb.Chunk{Id: oriId},
				Score: maxScore,
			})
		}
	}

	chunks = orderAndFilterRetrieveResults(chunks, params.ScoreThreshold)

	return &pb.RetrieveKnowledgeBaseRsp{
		Request:           req,
		Result:            chunks,
		FullTextIndexHits: hitsMap,
	}, nil
}

func normalizeSearchHits(hits []*stdes.SearchHit) {
	for _, hit := range hits {
		hit.Score = utils.Sigmoid(hit.Score)
	}
}

func (h *ScopeHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): req.DocId,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	return err
}

func (h *ScopeHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.IdField): chunkIds,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}

	return nil
}

func (h *ScopeHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.OriIdField): oriChunkIds,
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}
	return nil
}

func (h *ScopeHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	if err := h.initStore(ctx); err != nil {
		return err
	}
	body := map[string]any{
		"query": map[string]any{
			"terms": map[string]any{
				termsKey(h.TitleField): []string{docId},
			},
		},
	}
	err := h.Cli.DeleteByQuery(ctx, []string{h.IndexName}, body)
	if err != nil {
		return err
	}
	return nil
}

func (h *ScopeHandler) CountDocuments(ctx context.Context) (int32, error) {
	if h.IsExternal {
		docRowsMap, err := h.getDocumentRows(ctx)
		if err != nil {
			return 0, err
		}
		return int32(len(docRowsMap)), nil
	}
	return GetDocumentStore().CountByKnowledgeBase(h.KnowledgeBase.Id)
}

func (h *ScopeHandler) getDocumentRows(ctx context.Context) (mp map[string]int, err error) {

	terms, err := h.Cli.Terms(ctx, h.IndexName, h.TitleField)
	if err != nil {
		return nil, err
	}
	var buckets []*stdes.AggregationBucket
	if agg, ok := terms.Aggregations[TermsTitle]; ok {
		buckets = agg.Buckets
	} else {
		return nil, stderr.Internal.Error("ScopeHandler.ListDocuments: no aggregation found")
	}

	ret := make(map[string]int)
	for _, b := range buckets {
		ret[b.Key] = b.DocCount
	}
	return ret, nil
}

func (h *ScopeHandler) initStore(ctx context.Context) (err error) {
	if h.IsExternal {
		return nil
	}

	h.initOnce.Do(func() {
		if h.Cli != nil {
			return
		}
		var cli *elasticsearch.Client
		if cli, err = clients.GetProjScopeCli(ctx, h.KnowledgeBase.ProjectId); err != nil {
			return
		}
		h.Cli = stdes.NewESClient(cli)

		if err = h.checkIndex(ctx); err != nil {
			return
		}
	})
	return
}

func getInnerScopeIndex(kb *models.KnowledgeBase) string {
	return fmt.Sprintf("llm-%s", kb.Id)
}

func NewInternalScopeHandler(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	h := &ScopeHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase:      kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT},
		},
		Cli:        nil, // lazy init
		IndexName:  getInnerScopeIndex(kb),
		TextField:  InnerTextField,
		TitleField: InnerTitleField,
		IdField:    InnerIdField,
		OriIdField: InnerOriIdField,
		IsExternal: false,
	}
	return h, nil
}
func NewExternalScopeHandler(ctx context.Context, kb *models.KnowledgeBase) (StoreHandler, error) {
	if kb.ConnectionRegistry == nil {
		return nil, fmt.Errorf("NewScopeHandler: kb.ConnectionRegistry is nil")
	}
	connReg := kb.ConnectionRegistry
	getConnRsp, err := clients.CVATCli.DataConnClient.GetDataConnection(context.Background(), &pb.GetConnectionReq{
		Id: connReg.Id,
	})
	if err != nil {
		return nil, err
	}
	conn := getConnRsp.Result

	cli, err := Conn2ESCli(conn)
	if err != nil {
		return nil, err
	}

	return &ScopeHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase:      kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{pb.KnowledgeBaseRetrieveStrategy_FULL_TEXT},
		},
		Cli:        stdes.NewESClient(cli),
		IndexName:  connReg.Table,
		TextField:  connReg.TextField,
		TitleField: connReg.DocField,
		IsExternal: true,
	}, nil
}

func termsKey(key string) string {
	// return fmt.Sprintf("%s.keyword", key)
	// 创建mapping时已经为keyword类型的field，不再需要指定 xxx.keyword
	return key
}

func (h *ScopeHandler) FullTextName() string {
	return "scope"
}

func (h *ScopeHandler) Constructor() func(ctx context.Context, kb *models.KnowledgeBase) (FullTextHandler, error) {
	return NewInternalScopeHandler
}
