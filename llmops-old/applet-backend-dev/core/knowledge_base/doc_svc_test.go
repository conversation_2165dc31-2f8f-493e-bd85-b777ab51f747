package knowledge_base

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
)

var s DocHandler

func init() {
	// ctx := context.Background()
	// _ = ctx
	// viper.AddConfigPath("../../etc/")
	// viper.SetConfigName("app-dev")
	// viper.SetConfigType("yaml")
	// if err := viper.ReadInConfig(); err != nil {
	// 	panic(err)
	// }
	// if err := viper.Unmarshal(&conf.Config, conf.DecConfig); err != nil {
	// 	panic(err)
	// }
	// conf.Config.Engine.KubeconfigPath = path.Join("../../etc/", "k3s.yaml")

	os.Setenv("ETC_PATH", "../../etc/")
	if err := conf.TestInit(); err != nil {
		panic(err)
	}
	s = GetDocHandler()

	clients.TestInit()
}

func TestDocHandler_LoadSplit(t *testing.T) {
	ctx := context.Background()

	s = &docSvcHandler{
		DocSvcConfig: conf.Config.DocSvcConfig,
	}

	req := &DocLoadSplitReq{
		// Filepath:     "sfs:///temp/公司代理服务使用说明1714111646714/公司代理服务使用说明.txt",
		Filepath:     "sfs:///temp/yuangongshouce.pdf",
		Type:         "recursive",
		Strategy:     "auto",
		ChunkSize:    500,
		ChunkOverlap: 200,
		Separators:   []string{"\n\n", "\n", "。"},
	}
	rsp, err := s.LoadSplit(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(len(rsp.Texts))
	for _, text := range rsp.Texts {
		t.Log(text, "\n\n")
	}

	// req := &DocLoadSplitReq{
	// 	Filepath:     "sfs://test.pdf",
	// 	Strategy:     "auto",
	// 	Type:         "page",
	// 	ChunkSize:    1000,
	// 	ChunkOverlap: 200,
	// 	Separator:    "\n",
	// 	SplitHeaders: []string{"Header1", "Header2"},
	// 	Language:     "en",
	// }

	// rsp, err := handler.LoadSplit(ctx, req)
	// if err != nil {
	// 	t.Fatalf("LoadSplit failed: %v", err)
	// }

	// // 验证rsp是否符合预期
	// if len(rsp.Texts) != 2 {
	// 	t.Fatalf("LoadSplit returned unexpected number of texts: got %d, want %d", len(rsp.Texts), 2)
	// }
	// if rsp.Duration <= 0 {
	// 	t.Fatalf("LoadSplit returned unexpected duration: got %f, want >0", rsp.Duration)
	// }
	// if rsp.Filepath != req.Filepath {
	// 	t.Fatalf("LoadSplit returned unexpected filepath: got %s, want %s", rsp.Filepath, req.Filepath)
	// }
}

func TestDocHandler_LoadChunks(t *testing.T) {
	ctx := context.Background()
	// ss := ` {
	// 	"type": "DocElementType_Title",
	// 	"element_id": "fa2da51eca3ea88f711462adee07240f",
	// 	"text": "目前已支持chatGPT访问~",
	// 	"metadata": {
	// 	  "file_directory": "/sfs/temp/公司代理服务使用说明1714111646714",
	// 	  "filename": "公司代理服务使用说明.txt",
	// 	  "languages": [
	// 		"vie"
	// 	  ],
	// 	  "last_modified": "2024-04-26T06:08:07",
	// 	  "filetype": "text/plain"
	// 	}
	//   }`
	// var e = new(pb.DocElement)
	// // err := json.Unmarshal([]byte(ss), e)

	// pj := stdsrv.DefaultProtoJsonAccessor()
	// err := pj.Unmarshal([]byte(ss), e)
	// if err != nil {
	// 	log.Fatal(err)
	// }

	s = &docSvcHandler{
		DocSvcConfig: conf.Config.DocSvcConfig,
	}

	// Filepath:     "sfs:///temp/公司代理服务使用说明1714111646714/公司代理服务使用说明.txt",
	req := &DocLoadSplitReq{
		Filepath:     "sfs:///temp/中航光电p1-31722237947349/中航光电p1-3.pdf",
		Type:         "CHARACTER",
		Strategy:     "auto",
		ChunkSize:    400,
		ChunkOverlap: 80,
		// Separators: []string{"\n\n","\n"},
		Merge: false,
	}
	bs, err := json.Marshal(req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("request:", string(bs))
	rsp, err := s.LoadChunks(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(rsp)
	// for _, text := range rsp.Texts {
	// 	t.Log(text, "\n\n")
	// }

}

func Test2(t *testing.T) {
	t.Log(stdfs.GetSFSLocalPath("sfs:///temp/公司代理服务使用说明1714111646714/公司代理服务使用说明.txt"))
}

func TestDocHandler_LoadTable(t *testing.T) {
	ctx := context.Background()
	s = &docSvcHandler{
		DocSvcConfig: conf.Config.DocSvcConfig,
	}
	req := &TableContentLoadReq{
		Filepath:        "sfs:///temp/question_chatgpt.xlsx",
		SheetName:       "正文",
		HeaderRow:       0,
		DataStartingRow: 1,
	}

	rsp, err := s.LoadTable(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(rsp)
}
