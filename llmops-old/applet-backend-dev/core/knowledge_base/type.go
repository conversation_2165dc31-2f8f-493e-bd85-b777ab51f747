package knowledge_base

import (
	"context"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core/global_llm"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type KnowledgeBaseStoreType int32

const (
	InnerStore KnowledgeBaseStoreType = iota
	ExternalHippoStore
	ExternalScopeStore
	TkhStore
)

var StoreHandlerMap = map[KnowledgeBaseStoreType]func(context.Context, *models.KnowledgeBase) (StoreHandler, error){
	InnerStore:         NewInnerHandler,
	ExternalHippoStore: NewExternalHippoHandler,
	ExternalScopeStore: NewExternalScopeHandler,
	TkhStore:           NewTKHHandler,
}

// DefaultRetrievalConfig 根据默认的检索配置，生成pb.RetrievalConfig
func DefaultRetrievalConfig(projId string) (*pb.RetrievalConfig, error) {
	ms, err := DefaultRerankModelSvc(projId)
	if err != nil {
		return nil, err
	}
	return &pb.RetrievalConfig{
		AutoConfigured:          true,
		RetrievalStrategyOrigin: pb.StrategyOrigin_PRESET,
		Strategy:                pb.KnowledgeBaseRetrieveStrategy_MIXED,
		RecallParams: &pb.RecallParams{
			TopK:           conf.Config.AgentConfig.KBConfig.RecallTopK,
			ScoreThreshold: 0,
		},
		RerankParams: &pb.RerankParams{
			TopK:           conf.Config.AgentConfig.KBConfig.RerankTopK,
			ScoreThreshold: conf.Config.AgentConfig.KBConfig.Threshold,
			Model:          ms,
		},
	}, nil
}

// DefaultVecModelSvc 获取项目空间的默认向量模型
func DefaultVecModelSvc(projId string) (*pb.ModelService, error) {
	var l global_llm.LLModel
	model, err := l.GetModelByProjectID(context.Background(), projId)
	if err != nil {
		return nil, err
	}
	if model.ModelsForAgent.EmbeddingSvc == nil {
		return nil, global_llm.ErrProjEmbModelNotConfigured
	}
	return model.ModelsForAgent.EmbeddingSvc, nil
}

// DefaultRerankModelSvc 获取项目空间的默认重排模型
func DefaultRerankModelSvc(projId string) (*pb.ModelService, error) {
	var l global_llm.LLModel
	model, err := l.GetModelByProjectID(context.Background(), projId)
	if err != nil {
		return nil, err
	}
	if model.ModelsForAgent.RerankSvc == nil {
		return nil, global_llm.ErrProjRerankModelNotConfigured
	}
	return model.ModelsForAgent.RerankSvc, nil
}

// DefaultOcrModelSvc 获取项目空间的默认字符识别模型
func DefaultOcrModelSvc(projId string) (*pb.ModelService, error) {
	var l global_llm.LLModel
	model, err := l.GetModelByProjectID(context.Background(), projId)
	if err != nil {
		return nil, err
	}
	if model.ModelsForAgent.OcrSvc == nil {
		return nil, global_llm.ErrProjOcrModelNotConfigured
	}
	return model.ModelsForAgent.OcrSvc, nil
}
