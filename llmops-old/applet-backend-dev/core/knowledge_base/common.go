package knowledge_base

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/elastic/go-elasticsearch/v6"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/hippo-go/v1"
)

func Conn2HippoCli(conn *pb.DataConnection) (*hippo.HippoClient, error) {
	cfg := hippo.VectorDBConfig{
		ClientID:        "llmops-kb-conn-test",
		Address:         fmt.Sprintf("%s:%s", conn.Address, conn.Port),
		UserName:        conn.Username,
		UserPassword:    conn.Password,
		Database:        conn.Database,
		Table:           "",
		AutoCreateTable: false,
	}
	return hippo.NewHippoDBCli(context.Background(), cfg)
}

func Conn2ESCli(conn *pb.DataConnection) (*elasticsearch.Client, error) {
	u, err := url.Parse(conn.Address)
	if err != nil {
		return nil, stderr.Wrap(err, "Conn2ESCli url.Parse")
	}
	if u.Scheme == "" {
		u.Scheme = "http"
	}

	addr := u.String()
	if u.Port() == "" {
		addr = fmt.Sprintf("%s:%s", addr, conn.Port)
	}
	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{
			addr,
		},
		Username: conn.Username,
		Password: conn.Password,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "conn2ESCli")
	}
	stdlog.Infof("conn2ESCli , conn: %v", conn)
	stdlog.Info(es.Info())
	return es, nil
}

func GetVecModelDim(vecModel *pb.ModelService) (dim int, err error) {
	if vecModel == nil {
		err = stderr.Internal.Error("vector model is nil")
		return
	}
	var dimStr string
	switch vecModel.Type {
	case pb.ModelServiceType_MODEL_SERVICE_TYPE_LOCAL:
		dimStr, err = getDimOfLocalModelSvc(vecModel)
	case pb.ModelServiceType_MODEL_SERVICE_TYPE_REMOTE:
		dimStr, err = getDimOfRemoteModelSvc(vecModel)
	default:
		err = stderr.Internal.Error("unsupported vector model type")
	}
	if err != nil {
		return
	}
	dim, err = strconv.Atoi(dimStr)
	if err != nil {
		err = stderr.Wrap(err, "parse vector dimension")
	}
	return
}

func getDimOfLocalModelSvc(vecModel *pb.ModelService) (dimStr string, err error) {
	if vecModel.ReferenceRelease != nil &&
		vecModel.ReferenceRelease.ReleaseBase != nil &&
		vecModel.ReferenceRelease.ReleaseBase.Detail != nil &&
		vecModel.ReferenceRelease.ReleaseBase.Detail.ComputationAttributes != nil {
		return vecModel.ReferenceRelease.ReleaseBase.Detail.ComputationAttributes[KeyOutputVectorDims], nil
	}
	return "", stderr.Internal.Errorf("failed to fetch dim from local vector model svc")
}

func getDimOfRemoteModelSvc(vecModel *pb.ModelService) (dimStr string, err error) {
	if vecModel.RemoteServiceConfig != nil &&
		vecModel.RemoteServiceConfig.ComputationAttributes != nil {
		return vecModel.RemoteServiceConfig.ComputationAttributes[KeyOutputVectorDims], nil
	}
	return "", stderr.Internal.Errorf("failed to fetch dim from remote vector model svc")
}
