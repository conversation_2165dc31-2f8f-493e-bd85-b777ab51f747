package knowledge_base

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"sync"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb/common"

	"transwarp.io/applied-ai/applet-backend/core/applet"

	"transwarp.io/applied-ai/aiot/vision-std/stdfs"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/applet-backend/clients"
	pclients "transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/applied-ai/applet-backend/conf"

	"github.com/google/uuid"
	"gorm.io/gen/field"
	"gorm.io/gorm"

	"transwarp.io/aip/llmops-common/pb"
	stdes "transwarp.io/applied-ai/aiot/vision-std/clients/elastic_search"
	vstd "transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	docEngine "transwarp.io/applied-ai/doc-engine-go/v1"
)

type KnowledgeBaseManager struct {
	pb.UnimplementedKnowledgeBaseManagerServer
	q *query.Query
}

var (
	kbm                                 *KnowledgeBaseManager
	kbmOnce                             sync.Once
	ErrCouldNotUpdateExternalKB         = stderr.BadRequest.Error("could not update external knowledge base")
	ErrNilDocProcessingConfig           = stderr.Internal.Error("doc processing config is nil")
	ErrNilChunkAugmentConfig            = stderr.Internal.Error("chunk augment config is nil")
	ErrNilRetrievalConfig               = stderr.BadRequest.Error("retrieval config is nil")
	ErrMixedStrategyWithNilRerankParams = stderr.BadRequest.Error("mixed strategy with nil rerank params")

	DefaultDocProcessingConfig = &pb.DocProcessingConfig{
		AutoConfigured:              true,
		DocProcessingStrategyOrigin: pb.StrategyOrigin_PRESET,
		DocSplitConfig: &pb.DocSplitConfig{
			Separators:        []string{"\n\n", "\n", "。"},
			ChunkSize:         500,
			ChunkOverlap:      30,
			SplitStrategyType: pb.DocSplitStrategyType_CHARACTER,
		},
	}
)

const (
	DefaultTopK               = 20
	DefaultScoreThreshold     = 0.0
	EmbeddingContentMaxLength = 500 // 向量化文本的最大长度

	InnerIdField    = "chunk_id" // chunk id
	InnerOriIdField = "ori_id"   // original chunk id
	InnerTitleField = "doc_id"   // document id
	InnerVecField   = "feature"
	InnerTextField  = "text"

	AgentConfigKbNamePrefix = "agent-"
	KeyOutputVectorDims     = "output_vector_dims"

	PreviewHeadPages    = 3
	NonPreviewHeadPages = 0

	AlreadySubmitVector   = "already-submit-vector"
	AlreadySubmitFullText = "already-submit-fulltext"
)

type KbMetricsType string

const (
	KbMetricsTypeVisit   KbMetricsType = "VISIT"
	KbMetricsTypeClone   KbMetricsType = "CLONE"
	KbMetricsTypeExecute KbMetricsType = "EXECUTE"
)

func GetKnowledgeBaseManager() *KnowledgeBaseManager {
	kbmOnce.Do(func() {
		kbm = &KnowledgeBaseManager{
			q: dao.InitQuery(),
		}
	})
	return kbm
}

func (m *KnowledgeBaseManager) ListAllKnowledgeBases(ctx context.Context) ([]*models.KnowledgeBase, error) {
	return m.q.KnowledgeBase.WithContext(ctx).Find()
}

func (m *KnowledgeBaseManager) ListKnowledgeBases(ctx context.Context, req *pb.ListKnowledgeBasesReq) (*pb.ListKnowledgeBasesRsp, error) {
	if err := m.checkUserContextAllowEmptyProjectID(ctx, req.UserContext); err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase

	projectId := req.UserContext.ProjectId
	condition := repo.Where(repo.IsVisible.Is(true))

	if projectId != "" {
		oriCon := repo.Where(repo.ProjectId.Eq(projectId))
		if req.IsPublic {
			oriCon = oriCon.Or(repo.IsPublic.Is(true))
		}
		condition = condition.Where(oriCon)
	} else if req.IsPublic {
		condition = condition.Where(repo.IsPublic.Is(true))
	}

	if req.ListSelector != nil {
		if len(req.ListSelector.ContentTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.ContentTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.ContentType.In(fields...))
		}

		if len(req.ListSelector.RegistryTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.RegistryTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.RegistryType.In(fields...))
		}

		if len(req.ListSelector.SourceTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.SourceTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.SourceType.In(fields...))
		}

		if len(req.ListSelector.IsPublished) == 1 && !conf.Config.IsSimpleMode {
			condition = condition.Where(repo.IsPublished.Is(req.ListSelector.IsPublished[0]))
		}

		if len(req.ListSelector.SceneTypes) > 0 {
			fields := make([]int32, 0)
			for _, t := range req.ListSelector.SceneTypes {
				fields = append(fields, int32(t))
			}
			condition = condition.Where(repo.SceneType.In(fields...))
		}
	}

	kbs, err := condition.Find()
	if err != nil {
		return nil, stderr.Wrap(err, "list kb error.")
	}

	ret := make([]*pb.KnowledgeBaseInfo, len(kbs))
	err = stdsrv.SyncBatchCallGenericWithIdx(kbs, func(i int, kb *models.KnowledgeBase) error {
		info, err := m.queryKnowledgeBaseInfo(ctx, kb)
		if err != nil {
			// skipping
			stdlog.Errorf("query kb info %s, err:%v", kb.Id, err)
			info = &pb.KnowledgeBaseInfo{KnowledgeBase: kb.ToPb()}
		}

		ret[i] = info
		return nil
	})
	if err != nil {
		return nil, err
	}
	var wg sync.WaitGroup
	wg.Add(len(ret))
	for _, kb := range ret {
		go func(k *pb.KnowledgeBaseInfo) {
			defer wg.Done()
			rsp, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
				SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_KNOWLEDGE},
				SourceMetaExtra: map[string]string{
					KbIdMetaKey:        k.KnowledgeBase.Id,
					KbProjectIdMetaKey: k.KnowledgeBase.ProjectId,
				},
			})
			if err != nil {
				stdlog.WithError(err).Error("failed to list serving knowledge base service")
				return
			}
			if len(rsp.ServiceInfos) != 0 {
				k.KnowledgeBase.IsPublished = true
			}

		}(kb)
	}
	wg.Wait()
	start := time.Now()
	if err := ApplyCache(ctx, ret); err != nil {
		return nil, err
	}
	stdlog.Infof("cost %v to apply cache", time.Since(start))
	return &pb.ListKnowledgeBasesRsp{Result: ret}, nil

}

func (m *KnowledgeBaseManager) GetKnowledgeBase(ctx context.Context, req *pb.GetKnowledgeBaseReq) (*pb.GetKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	ret, err := m.takeKnowledgeBase(req.Id)
	if err != nil {
		return nil, err
	}

	info, err := m.queryKnowledgeBaseInfo(ctx, ret)
	if err != nil {
		return nil, stderr.Wrap(err, "query kb info %s", req.Id)
	}

	if err := ApplyCache(ctx, []*pb.KnowledgeBaseInfo{info}); err != nil {
		return nil, err
	}
	return &pb.GetKnowledgeBaseRsp{Result: info}, nil

}

func (m *KnowledgeBaseManager) CreateKnowledgeBase(ctx context.Context, req *pb.CreateKnowledgeBaseReq) (rsp *pb.CreateKnowledgeBaseRsp, err error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb := models.FromKnowledgeBasePb(req.Base)
	kb.Creator = req.UserContext.UserName
	kb.CreateTime = time.Now()
	kb.UpdateTime = time.Now()
	kb.ProjectId = req.UserContext.ProjectId
	kb.Id = uuid.NewString()
	kb.IsVisible = true
	kb.IsRetrievable = true
	kb.CreationType = pb.KnowledgeBaseCreationType_FROM_MANAGEMENT
	if kb.RetrievalConfig == nil || kb.RetrievalConfig.AutoConfigured {
		rc, err := DefaultRetrievalConfig(kb.ProjectId)
		if err != nil {
			return nil, err
		}
		if kb.DocProcessingConfig != nil &&
			kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		kb.RetrievalConfig = rc
	}
	if kb.DocProcessingConfig == nil || kb.DocProcessingConfig.AutoConfigured {
		kb.DocProcessingConfig = DefaultDocProcessingConfig
	}

	if kb.DocProcessingConfig != nil &&
		kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		docKnowledgeBaseReq := new(docEngine.CreateKnowledgeBaseReq)
		docKnowledgeBaseReq.Name = kb.Id
		docKnowledgeBaseReq.ProjectId = kb.ProjectId
		docKnowledgeBaseReq.TenantId = helper.GetTenantID(ctx)
		docKnowledgeBaseReq.IndexBuildConf = docEngine.IndexBuildConfReq{
			IndexTypeArr: docEngine.DefaultIndexType(),
		}
		docSchema := new(docEngine.EntitySchemaConf)
		err = json.Unmarshal([]byte(kb.DocProcessingConfig.DocLoadConfig.EntitySchemaConf), &docSchema)
		if err != nil {
			return nil, stderr.Wrap(err, "transfer the schema of doc engine failed!")
		}
		docKnowledgeBaseReq.EntitySchemaConf = *docSchema
		handler.(*InnerHandler).docEngine.CreateKnowledgeBase(ctx, docKnowledgeBaseReq)
	}

	if kb.VectorModel == nil {
		ms, err := DefaultVecModelSvc(kb.ProjectId)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb: get default vec model")
		}
		kb.VectorModel = ms
	}

	// 检查向量维度
	if kb.VectorModel != nil {
		_, err := GetVecModelDim(kb.VectorModel)
		if err != nil {
			return nil, stderr.Wrap(err, "create kb: GetVecModelDim")
		}
	}

	repo := m.q.KnowledgeBase
	if err := repo.Create(kb); err != nil {
		return nil, stderr.Wrap(err, "create kb err")
	}
	defer func() {
		if err != nil {
			if _, err := repo.Delete(kb); err != nil {
				stdlog.WithError(err).Errorf("delete kb %s after failed to create", kb.Id)
			}
		}
	}()

	ret, err := m.GetKnowledgeBase(ctx, &pb.GetKnowledgeBaseReq{
		UserContext: req.UserContext,
		Id:          kb.Id,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "Create kb: get kb %s", kb.Id)
	}

	return &pb.CreateKnowledgeBaseRsp{Result: ret.Result.KnowledgeBase}, nil

}

func (m *KnowledgeBaseManager) UpdateKnowledgeBase(ctx context.Context, req *pb.UpdateKnowledgeBaseReq) (*pb.UpdateKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase
	kb := models.FromKnowledgeBasePb(req.Base)
	kb.UpdateTime = time.Now()

	if kb.RetrievalConfig == nil || kb.RetrievalConfig.AutoConfigured {
		rc, err := DefaultRetrievalConfig(kb.ProjectId)
		if err != nil {
			return nil, err
		}
		if kb.DocProcessingConfig != nil &&
			kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			rc.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
		}
		kb.RetrievalConfig = rc
	}
	if kb.DocProcessingConfig == nil || kb.DocProcessingConfig.AutoConfigured {
		kb.DocProcessingConfig = DefaultDocProcessingConfig
	}

	if tx := repo.UnderlyingDB().Save(kb); tx.Error != nil {
		return nil, stderr.Wrap(tx.Error, "save kb %s", kb.Id)
	}

	if kb.DocProcessingConfig != nil &&
		kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		handler.(*InnerHandler).docEngine.UpdateKnowledgeBase(ctx)
	}

	return &pb.UpdateKnowledgeBaseRsp{
		Result: kb.ToPb(),
	}, nil
}
func (m *KnowledgeBaseManager) UpdateKnowledgeMetricsInfo(ctx context.Context, kbId string,
	actionType KbMetricsType) error {
	repo := m.q.KnowledgeBase
	model, err := m.takeKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	if model.MetricsInfo == nil {
		model.MetricsInfo = new(pb.MetricsInfo)
	}
	switch actionType {
	case KbMetricsTypeVisit:
		model.MetricsInfo.VisitTimes++
	case KbMetricsTypeClone:
		model.MetricsInfo.CloneTimes++
	case KbMetricsTypeExecute:
		model.MetricsInfo.ExecuteTimes++
	}
	if _, err := repo.Updates(model); err != nil {
		return err
	}
	return nil
}

func (m *KnowledgeBaseManager) DeleteKnowledgeBases(ctx context.Context, req *pb.DeleteKnowledgeBasesReq) (*pb.DeleteKnowledgeBasesRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	projectID := req.UserContext.ProjectId
	repo := m.q.KnowledgeBase
	kbs, err := repo.Where(repo.Id.In(req.Ids...), repo.ProjectId.Eq(projectID)).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "find kbs %s err", req.Ids)
	}

	// Collect the IDs of the found knowledge bases
	foundIDs := make(map[string]bool)
	for _, kb := range kbs {
		foundIDs[kb.Id] = true
	}

	// Check for IDs that were not found
	var notFoundIDs []string
	for _, id := range req.Ids {
		if !foundIDs[id] {
			notFoundIDs = append(notFoundIDs, id)
		}
	}
	if len(notFoundIDs) > 0 {
		return nil, stderr.Internal.Errorf("kb id [%s] is not found in project [%s]", notFoundIDs, projectID)
	}
	// 删除发布的知识库服务
	for _, kb := range kbs {
		if kb.PublishInfo == nil || !kb.IsPublished {
			continue
		}
		_, err := clients.MLOpsCli.Cli.DeleteService(ctx, &serving.ServiceID{
			Id: kb.PublishInfo.Id,
		})
		if err != nil {
			stdlog.WithError(err).Errorf("failed to delete knowledge %s published service %s", kb.Id, kb.PublishInfo.Id)
		}
	}

	// delete kb records
	if _, err := repo.Where(repo.Id.In(req.Ids...)).Delete(); err != nil {
		return nil, stderr.Wrap(err, "delete kb %s err", req.Ids)
	}

	backCtx, _ := context.WithTimeout(context.Background(), time.Minute)
	helper.CopyBaseInfoBetweenContexts(ctx, backCtx)
	go func(ctx context.Context) {
		// delete related docs, chunks & elements
		for _, kb := range kbs {
			id := kb.Id
			err = GetDocumentStore().DeleteByKnowledgeBase(ctx, id)
			if err != nil {
				stdlog.WithError(err).Errorf("delete kb %s", id)
			}
			// delete indices
			handler, err := NewHandler(ctx, kb)
			if err != nil {
				continue
			}
			err = handler.Drop(ctx)
			if err != nil {
				stdlog.WithError(err).Errorf("delete kb %s", id)
			}
		}
	}(backCtx)

	return &pb.DeleteKnowledgeBasesRsp{}, nil
}

// 获取数据连接的库表列表, 包含表描述信息
func (m *KnowledgeBaseManager) ListConnectionTables(ctx context.Context, req *pb.ListConnectionTablesReq) (rsp *pb.ListConnectionTablesRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	var ret []*pb.TableInfo
	conn := req.Connection
	switch conn.Type {
	case pb.ConnectionType_HIPPO:
		ret, err = DetailHippoConn(ctx, conn)
	case pb.ConnectionType_SCOPE:
		ret, err = DetailESConn(ctx, conn)
	default:
		err = stderr.Internal.Error("unsupported conn type")
	}
	if err != nil {
		return nil, err
	}

	return &pb.ListConnectionTablesRsp{Tables: ret}, nil
}

func DetailESConn(ctx context.Context, conn *pb.DataConnection) (ret []*pb.TableInfo, err error) {
	es, err := Conn2ESCli(conn)
	if err != nil {
		return nil, err
	}
	cli := stdes.NewESClient(es)
	indices, err := cli.CatIndices(ctx)
	if err != nil {
		return nil, err
	}

	var names []string
	for _, idx := range indices {
		names = append(names, idx.Index)
	}

	mappings, err := cli.GetMappings(ctx, names)
	if err != nil {
		return nil, err
	}

	for _, index := range indices {
		ti := new(pb.TableInfo)
		ti.Name = index.Index
		ti.Rows, _ = strconv.ParseInt(index.DocsCount, 10, 64)
		bs, err := json.Marshal(mappings[index.Index])
		if err != nil {
			stdlog.Errorf("DetailESConn marshall mapping: %v", mappings[index.Index])
			continue
		}
		ti.Description = string(bs)
		ret = append(ret, ti)
	}

	return ret, nil

}

func DetailHippoConn(ctx context.Context, conn *pb.DataConnection) (ret []*pb.TableInfo, err error) {
	cli, err := Conn2HippoCli(conn)
	if err != nil {
		return nil, stderr.Wrap(err, "conn2HippoCli, conn:%v", conn)
	}

	tableListRsp, err := cli.ListTables(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "list tables")
	}

	tables := tableListRsp.Tables
	for _, tb := range tables {
		if tb == "" {
			continue
		}
		cntRsp, err := cli.CountTable(ctx, tb)
		if err != nil {
			return nil, stderr.Wrap(err, "count table, table:%v", tb)
		}
		tbDesc, err := cli.GetTable(ctx, conn.Database, tb)
		if err != nil {
			return nil, stderr.Wrap(err, "get table description, table:%v", tb)
		}
		descStr, err := json.Marshal(tbDesc)
		if err != nil {
			return nil, stderr.Wrap(err, "marshal table description, table")
		}

		info := &pb.TableInfo{
			Name:        tb,
			Rows:        cntRsp.Total,
			Description: string(descStr),
		}
		ret = append(ret, info)
	}
	return
}

// 获取知识库的文档列表，平铺形式
func (m *KnowledgeBaseManager) ListDocuments(ctx context.Context, req *pb.ListDocumentsReq) (*pb.ListDocumentsRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	return handler.ListDocuments(ctx)
}

// 启用/禁用 知识库文档
func (m *KnowledgeBaseManager) DisableDocument(ctx context.Context, req *pb.DisableDocumentReq) (*pb.DisableDocumentRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}
	repo := m.q.KnowledgeBase
	kb.DisabledDocs = updateDisabledDocs(kb.DisabledDocs, req.DocId, req.Disabled)
	if tx := repo.UnderlyingDB().Save(kb); tx.Error != nil {
		return nil, tx.Error
	}

	return &pb.DisableDocumentRsp{}, nil
}

// 获取知识库文档的分段内容列表，支持排序
func (m *KnowledgeBaseManager) ListDocumentChunks(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return handler.ListDocumentChunks(ctx, req.DocId, req.PageReq)
	}
	return handler.ListDocumentChunksByReq(ctx, req)
}

// 知识库检索接口
func (m *KnowledgeBaseManager) RetrieveKnowledgeBase(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	// 请求的RetrievalConfig为空时，使用知识库设置的RetrievalConfig
	rc := req.RetrievalConfig
	if rc == nil {
		rc = kb.RetrievalConfig
		req.RetrievalConfig = rc
	} else {
		if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
			req.RetrievalConfig.Strategy = pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE
			req.DisableRerank = true
		}
	}
	if rc == nil {
		return nil, ErrNilRetrievalConfig
	}

	if rc.Strategy == pb.KnowledgeBaseRetrieveStrategy_MIXED && rc.RerankParams == nil && !req.DisableRerank {
		return nil, ErrMixedStrategyWithNilRerankParams
	}

	// 1. Recall
	// 某些使用场景 仅指定了RerankRarams
	if rc.RecallParams == nil {
		rc.RecallParams = &pb.RecallParams{
			TopK:           DefaultTopK,
			ScoreThreshold: DefaultScoreThreshold,
		}
	}
	rsp, err := handler.Recall(ctx, req)
	if err != nil {
		return nil, err
	}

	// 1.5. 获取命中切片知识库的上下文
	linkedDocumentBatch := NewLinkedDocumentBatch()
	if req.RetrievalConfig.ContextNum > 0 {
		docs := make(map[string]struct{})
		for _, chunk := range rsp.Result {
			docs[chunk.DocId] = struct{}{}
		}
		go func() {
			linkedDocumentBatch.Start()
			for id, _ := range docs {
				// 如果document数量太多会堵塞一会儿
				linkedDocumentBatch.Submit(id)
			}
			linkedDocumentBatch.End()
		}()
	} else {
		linkedDocumentBatch.Close()
	}

	if req.DisableRerank || rc.RerankParams == nil {
		rsp.Result = linkedDocumentBatch.Process(rsp.Result, req.RetrievalConfig.ContextNum)
		return rsp, nil
	}

	// 2. Rerank
	// RerankParams的模型未指定时，尝试从知识库设置中获取
	if rc.RerankParams.Model == nil {
		if kb.RetrievalConfig.RerankParams != nil && kb.RetrievalConfig.RerankParams.Model != nil {
			rc.RerankParams.Model = kb.RetrievalConfig.RerankParams.Model
		} else {
			return nil, stderr.InvalidParam.Error("rerank model must be set")
		}
	}
	chunks, err := Rerank(ctx, req.Query, req.RetrievalConfig.RerankParams, rsp.Result)
	if err != nil {
		return nil, err
	}

	rsp.Result = linkedDocumentBatch.Process(chunks, req.RetrievalConfig.ContextNum)
	return rsp, nil

}

// 获取知识库的文档列表，文档树形式
func (m *KnowledgeBaseManager) GetDocumentTree(ctx context.Context, req *pb.GetDocumentTreeReq) (*pb.GetDocumentTreeRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	return handler.GetDocumentTree(ctx)
}

// 新增文档到知识库(异步处理),允许自动创建知识库
func (m *KnowledgeBaseManager) SubmitFileToKnowledgeBase(ctx context.Context, req *pb.SubmitFileToKnowledgeBaseReq) (*pb.SubmitFileToKnowledgeBaseRsp, error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	kb, err := m.takeOrCreateKnowledgeBase(req.KnowledgeBaseId, req.UserContext, req.AutoCreateKb)
	if err != nil {
		return nil, err
	}

	doc, err := GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
		FilePath:            req.FilePath,
		TableConfig:         req.TableConfig,
		CorpusConfig:        req.CorpusConfig,
		DocumentFileSource:  pb.DocumentFileSource_LOCAL_FILE, // 姑且都是本地文件
		DocProcessingConfig: req.DocProcessingConfig,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "file %s", req.FilePath)
	}

	dpc := req.DocProcessingConfig
	if dpc == nil {
		dpc = kb.DocProcessingConfig
	}
	if dpc == nil {
		return nil, stderr.Internal.Error("doc processing config must be set.")
	}
	task := NewDocTask(ctx, kb.Id, dpc, doc.ToPb())
	err = GetTaskManger().Submit(task)
	if err != nil {
		return nil, err
	}

	return &pb.SubmitFileToKnowledgeBaseRsp{
		Doc: doc.ToPb(),
	}, nil

}

// 从知识库移除文档
func (m *KnowledgeBaseManager) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) (*pb.RemoveFileFromKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	err = handler.RemoveFileFromKnowledgeBase(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.RemoveFileFromKnowledgeBaseRsp{}, nil
}

// 更新知识库状态(可见、可检索)
func (m *KnowledgeBaseManager) UpdateKnowledgeBaseState(ctx context.Context, req *pb.UpdateKnowledgeBaseStateReq) (*pb.UpdateKnowledgeBaseStateRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	repo := m.q.KnowledgeBase
	cnt, err := repo.Where(repo.Id.Eq(req.Id)).Count()
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState count kb %s", req.Id)
	}
	if cnt < 1 {
		return nil, stderr.InvalidParam.Error("kb %s not found", req.Id)
	}

	_, err = repo.WithContext(ctx).Where(repo.Id.Eq(req.Id)).UpdateSimple(repo.IsVisible.Value(req.IsVisible), repo.IsRetrievable.Value(req.IsRetrievable))
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState update kb %s", req.Id)
	}

	kb, err := repo.Where(repo.Id.Eq(req.Id)).Take()
	if err != nil {
		return nil, stderr.Wrap(err, "UpdateKnowledgeBaseState take kb %s after update", req.Id)
	}

	return &pb.UpdateKnowledgeBaseStateRsp{KnowledgeBase: kb.ToPb()}, nil
}

// 手动新建chunk
func (m *KnowledgeBaseManager) CreateChunk(ctx context.Context, req *pb.CreateChunkReq) (*pb.CreateChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	if req.ContentType != pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT &&
		req.ContentType != pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE &&
		req.ContentType != pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_MARKDOWN {
		// 暂时只支持文本和图像
		return nil, stderr.BadRequest.Errorf("content_type must be text, markdown or image")
	}

	oriChunkId := req.OriChunkId
	var chunk *models.Chunk
	if oriChunkId == "" { // 新增原文chunk
		chunk = &models.Chunk{
			Id:              uuid.NewString(),
			Content:         req.Content,
			SourceType:      pb.ChunkSourceType_SOURCE_TYPE_CREATED,
			ContentType:     req.ContentType,
			DocumentId:      req.DocId,
			KnowledgeBaseId: req.KnowledgeBaseId,
		}
		// augmentation
		dpc := kb.DocProcessingConfig
		if dpc != nil && dpc.ChunkAugmentConfig != nil && (dpc.ChunkAugmentConfig.Enabled || dpc.ChunkAugmentConfig.ImageEnable) {
			chunkPb := chunk.ToPb()
			if err := augmentChunk(ctx, chunkPb, dpc.ChunkAugmentConfig); err != nil {
				return nil, err
			}
			chunk = models.FromChunkPb(chunkPb, chunk.KnowledgeBaseId, chunk.DocumentId)
		}
		// store
		idxChunks := makeChunksForIndexing([]*models.Chunk{chunk})
		err = GetChunkStore().Store(chunk)
		if err != nil {
			return nil, err
		}
		// indexing
		err = handler.SubmitChunks(ctx, idxChunks)
		if err != nil {
			return nil, err
		}

	} else { // 新增知识增强chunk
		chunk, err = GetChunkStore().Load(oriChunkId)
		if err != nil {
			return nil, err
		}
		augChunk := &pb.AugmentedChunk{
			Id:            uuid.NewString(),
			Content:       req.Content,
			SourceType:    pb.ChunkSourceType_SOURCE_TYPE_CREATED,
			AugmentedType: pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_UNDEFINED,
		}
		chunk.AugmentedChunks = append(chunk.AugmentedChunks, augChunk)
		// store
		err = GetChunkStore().Store(chunk)
		if err != nil {
			return nil, err
		}
		// indexing
		idxChunk := &models.ChunkForIndexing{
			Id:                      augChunk.Id,
			OriId:                   oriChunkId,
			DocId:                   chunk.DocumentId,
			KBId:                    chunk.KnowledgeBaseId,
			Text:                    augChunk.Content,
			DisableVectorIndexing:   augChunk.DisableVectorIndexing,
			DisableFullTextIndexing: augChunk.DisableFullTextIndexing,
		}
		idxChunks := []*models.ChunkForIndexing{idxChunk}
		err = handler.SubmitChunks(ctx, idxChunks)
		if err != nil {
			return nil, err
		}

	}

	return &pb.CreateChunkRsp{Chunk: chunk.ToPb()}, nil

}

func (m *KnowledgeBaseManager) DeleteChunk(ctx context.Context, req *pb.DeleteChunkReq) (*pb.DeleteChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}

	chunk, err := GetChunkStore().Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}

	kbid := chunk.KnowledgeBaseId

	kb, err := m.takeKnowledgeBase(kbid)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	rsp := &pb.DeleteChunkRsp{}
	if req.ChunkId == req.OriChunkId { // 删除原文chunk
		if err = handler.DeleteChunksByOriId(ctx, []string{req.OriChunkId}); err != nil {
			return nil, err
		}

		// delete chunk
		if err = GetChunkStore().Delete(req.OriChunkId); err != nil {
			return nil, err
		}
	} else { // 删除增强chunk
		if err = handler.DeleteChunksById(ctx, []string{req.ChunkId}); err != nil {
			return nil, err
		}

		// update chunk
		chunk.AugmentedChunks = utils.DelElement(chunk.AugmentedChunks, func(i int) bool {
			return chunk.AugmentedChunks[i].Id == req.ChunkId
		})

		if err = GetChunkStore().Store(chunk); err != nil {
			return nil, err
		}
		rsp.Chunk = chunk.ToPb()
	}

	return rsp, nil
}

func (m *KnowledgeBaseManager) UpdateChunk(ctx context.Context, req *pb.UpdateChunkReq) (*pb.UpdateChunkRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, err
	}

	if kb.IsExternal() {
		return nil, ErrCouldNotUpdateExternalKB
	}

	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}

	c, err := GetChunkStore().Load(req.OriChunkId)
	if err != nil {
		return nil, err
	}

	ci := new(models.ChunkForIndexing)
	if req.ChunkId == req.OriChunkId {
		// 更新原文切片
		c.Content = req.Content
		c.Edited = true
		ci = refreshChunkIdxSetting(c)
	} else {
		// 更新增强切片
		i := GetChunkStore().IndexAugChunk(c, req.ChunkId)
		if i < 0 {
			return nil, stderr.Errorf("augmented chunk %s not found", req.ChunkId)
		}
		ac := c.AugmentedChunks[i]
		ac.Content = req.Content
		ac.Edited = true
		ci = refreshAugChunkIdxSetting(c, ac)
	}

	if err = GetChunkStore().Store(c); err != nil {
		return nil, err
	}

	if err = handler.DeleteChunksById(ctx, []string{req.ChunkId}); err != nil {
		return nil, err
	}

	if ci != nil {
		if err = handler.SubmitChunks(ctx, []*models.ChunkForIndexing{ci}); err != nil {
			return nil, err
		}
	}

	return &pb.UpdateChunkRsp{Chunk: c.ToPb()}, nil
}

func (m *KnowledgeBaseManager) SyncSubmitFilesToKnowledgeBase(req *pb.SyncSubmitFilesToKnowledgeBaseReq, server pb.KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseServer) error {
	ctx := server.Context()

	ch, err := m.SyncSubmitFilesToKnowledgeBaseMain(ctx, req)
	if err != nil {
		return err
	}
	for rsp := range ch {
		err := server.Send(rsp)
		if err != nil {
			stdlog.Errorf("SyncSubmitFilesToKnowledgeBase send error: %v", err)
			return err
		}
	}
	return nil
}

func (m *KnowledgeBaseManager) SyncSubmitFilesToKnowledgeBaseMain(ctx context.Context, req *pb.SyncSubmitFilesToKnowledgeBaseReq) (chan *pb.SyncSubmitFilesToKnowledgeBaseRsp, error) {
	if err := m.checkUserContext(ctx, req.UserContext); err != nil {
		return nil, err
	}
	kb, err := m.takeOrCreateKnowledgeBase(req.KnowledgeBaseId, req.UserContext, req.AutoCreateKb)
	if err != nil {
		return nil, err
	}
	docs := make([]*pb.Document, 0)
	var filePaths []string
	if req.DocumentFileSource == pb.DocumentFileSource_LOCAL_FILE {
		filePaths = req.FilePaths
	} else {
		// 从CorpusConfig获取parquet文件目录
		if req.CorpusConfig == nil {
			return nil, stderr.Internal.Errorf("CORPUS_DATA_SET source must set corpusConfig")
		}
		dir := req.CorpusConfig.Dir
		relPath, err := stdfs.NewRelativeFilePath(dir)
		if err != nil {
			return nil, err
		}
		absFiles, err := helper.ListFiles(relPath.ToAbsFilePath(), true)
		if err != nil {
			return nil, err
		}
		for _, absFile := range absFiles {
			relFilePath, err := stdfs.NewRelativeFilePath(absFile)
			if err != nil {
				return nil, err
			}
			filePaths = append(filePaths, relFilePath.ToSFSFilePath())
		}
	}

	progs := make([]*pb.DocumentProcessingProgress, 0, len(docs))
	if req.DocProcessingConfig != nil &&
		req.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		files := make([]string, 0, len(filePaths))
		for _, f := range filePaths {
			localPath, err := stdfs.GetSFSLocalPath(f)
			if err != nil {
				return nil, err
			}
			files = append(files, localPath)
		}
		progs, err = handler.(*InnerHandler).docEngine.UploadFiles(ctx, files)
		stdlog.Infof("update successfully")
		if err != nil {
			return nil, err
		}
		docIds := make([]string, 0)
		for _, p := range progs {
			docIds = append(docIds, p.Document.DocId)
		}
	} else {

		for _, filePath := range filePaths {
			doc, err := GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
				FilePath:            filePath,
				TableConfig:         req.TableConfig,
				CorpusConfig:        req.CorpusConfig,
				DocumentFileSource:  req.DocumentFileSource,
				DocProcessingConfig: req.DocProcessingConfig,
			})
			if err != nil {
				return nil, stderr.Wrap(err, "file %s", filePath)
			}
			docs = append(docs, doc.ToPb())
		}

		dpc := req.DocProcessingConfig
		if dpc == nil {
			dpc = kb.DocProcessingConfig
		}
		if dpc == nil && kb.ContentType == pb.KnowledgeBaseContentType_TEXT {
			return nil, stderr.Internal.Errorf("doc processing config must be set.")
		}

		for _, doc := range docs {
			// 主流程
			task := NewDocTask(ctx, kb.Id, dpc, doc)
			err = GetTaskManger().Submit(task)
			if err != nil {
				return nil, err
			}
			progs = append(progs, task.DocumentProcessingProgress)
		}
	}

	rsp := &pb.SyncSubmitFilesToKnowledgeBaseRsp{
		Total:     int32(len(docs)),
		Documents: progs,
	}

	var stat = func(progs []*pb.DocumentProcessingProgress) (int32, int32) {
		var numSucceeded, numFailed int32 = 0, 0
		for _, prog := range progs {
			if prog.Finished {
				if isProgSucceeded(prog) {
					numSucceeded += 1
				} else {
					numFailed += 1
				}
			}
		}
		return numSucceeded, numFailed
	}

	ch := make(chan *pb.SyncSubmitFilesToKnowledgeBaseRsp)
	go func() {
		defer close(ch)
		for {
			select {
			case <-ctx.Done():
				return
			default:
				time.Sleep(time.Second)
				ns, nf := stat(progs)
				rsp.NumSucceeded = ns
				rsp.NumFailed = nf
				if ns+nf == rsp.Total {
					rsp.Done = true
					ch <- rsp
					return
				}
				ch <- rsp
			}
		}
	}()

	return ch, nil

}

func (m *KnowledgeBaseManager) SubmitChunksToKnowledgeBase(ctx context.Context, req *pb.SubmitChunksToKnowledgeBaseReq) (rsp *pb.SubmitChunksToKnowledgeBaseRsp, err error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return
	}

	var doc *models.Document
	if req.FilePath != "" {
		doc, err = GetDocumentStore().Submit(ctx, kb.Id, &models.DocumentSubmitInfo{
			FilePath:            req.FilePath,
			TableConfig:         nil,
			CorpusConfig:        nil,
			DocumentFileSource:  pb.DocumentFileSource_LOCAL_FILE,
			DocProcessingConfig: nil,
		})
		if err != nil {
			return nil, err
		}
	} else {
		//TODO impl file base64
		return nil, stderr.Internal.Error("unsupported file base64")
	}

	// err = GetDocElementStore().StoreBatch(models.FromDocElementPbs(req.Elements))
	// if err != nil {
	// 	return nil, err
	// }
	// chunks := models.FromChunkPbs(req.Chunks, kb.Id, doc.Id)

	// for _, c := range chunks {
	// 	if c.Id == "" {
	// 		c.Id = uuid.NewString()
	// 	}
	// }

	// err = GetChunkStore().StoreBatch(chunks)
	// if err != nil {
	// 	return nil, err
	// }

	// // indexing
	// idxChunks := makeChunksForIndexing(chunks)

	// handler, err := NewHandler(ctx, kb)
	// if err != nil {
	// 	return
	// }

	// err = handler.SubmitChunks(ctx, idxChunks)
	// if err != nil {
	// 	return
	// }
	task := NewDocTask(ctx, kb.Id, nil, doc.ToPb())
	if err = GetTaskManger().Create(task); err != nil {
		return nil, err
	}
	defer func() {
		task.DocumentProcessingProgress.Finished = true
		GetTaskManger().Store(doc.Id)
	}()

	if err = storeAndIndexingChunks(ctx, kb.Id,
		&pb.DocSvcLoadChunkRsp{Chunks: req.Chunks, Elements: req.Elements}, task.DocumentProcessingProgress); err != nil {
		return nil, err
	}

	rsp = &pb.SubmitChunksToKnowledgeBaseRsp{
		Document: doc.ToPb(),
	}
	return
}
func (m *KnowledgeBaseManager) RetrieveKnowledgeBaseInIstioMode(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	kb, err := m.takeKnowledgeBase(req.KnowledgeBaseId)
	if err != nil {
		return nil, stderr.Wrap(err, "knowledge base not found")
	}
	publishInfo := kb.PublishInfo
	if publishInfo == nil {
		return nil, stderr.Wrap(err, "knowledge base publish info not found can not use istio")
	}
	u := url.URL{
		Scheme: "http",
		Host:   conf.Config.MLOps.IstioGatewayAddr,
		Path:   publishInfo.VirtualSvcUrl,
	}
	client := &http.Client{}
	pjm := protojson.MarshalOptions{
		Multiline:       true,
		Indent:          "  ",
		AllowPartial:    true,
		UseProtoNames:   true,
		UseEnumNumbers:  false,
		EmitUnpopulated: true,
	}
	reqBs, err := pjm.Marshal(req)
	if err != nil {
		return nil, stderr.Wrap(err, "marshal RetrieveKnowledgeBaseReq)")
	}
	hr, err := http.NewRequestWithContext(ctx, "POST", u.String(), bytes.NewBuffer(reqBs))
	if err != nil {
		return nil, stderr.Wrap(err, "failed to create http request")
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get token in context")
	}
	hr.Header.Set("Authorization", token)
	rsp, err := client.Do(hr)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do http request")
	}
	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to read response body")
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != 200 {
		return nil, stderr.Internal.Errorf("failed to get retrieve, status code: %d, body: %s", rsp.StatusCode, string(body))
	}
	retrieve := new(pb.RetrieveKnowledgeBaseRsp)
	pju := protojson.UnmarshalOptions{
		AllowPartial:   true,
		DiscardUnknown: true,
	}
	err = pju.Unmarshal(body, retrieve)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal response body")
	}
	return retrieve, nil
}

func (m *KnowledgeBaseManager) RetrieveCrossKnowledgeBase(ctx context.Context, req *pb.RetrieveCrossKnowledgeBaseReq) (rsp *pb.RetrieveCrossKnowledgeBaseRsp, err error) {
	ctx = helper.FillContextWithGrpcInfo(ctx)

	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	var lock sync.Mutex
	results := make([]*pb.ChunkRetrieveResult, 0)
	var docEngine, others []*pb.RetrieveRange

	// 遍历数组并根据条件分组
	repo := m.q.KnowledgeBase
	for _, k := range req.Ranges {
		kb, err := repo.Where(repo.Id.Eq(k.KnowledgeBaseId)).Take()
		if err != nil {
			return nil, err
		}
		if kb.RetrievalConfig != nil && kb.RetrievalConfig.Strategy == pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE {
			docEngine = append(docEngine, k) // 偶数放入 group1
		} else {
			others = append(others, k) // 奇数放入 group2
		}
	}

	if len(docEngine) != 0 {
		kb, err := repo.Where(repo.Id.Eq(docEngine[0].KnowledgeBaseId)).Take()
		handler, err := NewHandler(ctx, kb)
		if err != nil {
			return nil, err
		}
		docEngineReq := &pb.RetrieveCrossKnowledgeBaseReq{
			UserContext:   req.UserContext,
			Query:         req.Query,
			Ranges:        docEngine,
			RerankParams:  req.RerankParams,
			DisableRerank: req.DisableRerank,
		}
		docEngineRsp, err := handler.(*InnerHandler).docEngine.CrossRecall(ctx, docEngineReq)
		if err != nil {
			return nil, err
		}
		results = append(results, docEngineRsp.Result...)
	}

	err = stdsrv.SyncBatchCallGeneric(others, func(rr *pb.RetrieveRange) error {
		rq := &pb.RetrieveKnowledgeBaseReq{
			UserContext:     req.UserContext,
			KnowledgeBaseId: rr.KnowledgeBaseId,
			Query:           req.Query,
			DocRange:        rr.DocRange,
			RetrievalConfig: rr.RetrievalConfig, // 为nil时使用各知识库默认的检索设置
			DisableRerank:   true,               // 仅召回，关闭重排序
		}
		var err error
		var rsp *pb.RetrieveKnowledgeBaseRsp
		if req.IstioMode {
			rsp, err = m.RetrieveKnowledgeBaseInIstioMode(ctx, rq)
		} else {
			rsp, err = m.RetrieveKnowledgeBase(ctx, rq)
		}
		if err != nil {
			return err
		}
		lock.Lock()
		defer lock.Unlock()
		results = append(results, rsp.Result...)
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 仅召回
	if req.DisableRerank {
		return &pb.RetrieveCrossKnowledgeBaseRsp{Request: req, Result: results}, nil
	}

	if req.RerankParams == nil { // 需要重排序但未指定RerankParams时，采用默认的RerankParams
		projId := req.UserContext.ProjectId
		rc, err := DefaultRetrievalConfig(projId)
		if err != nil {
			return nil, err
		}
		req.RerankParams = rc.RerankParams
	}
	results, err = Rerank(ctx, req.Query, req.RerankParams, results)
	if err != nil {
		return nil, err
	}

	return &pb.RetrieveCrossKnowledgeBaseRsp{Request: req, Result: results}, nil
}

func (m *KnowledgeBaseManager) PreviewDocumentProcess(ctx context.Context, req *pb.PreviewDocumentProcessReq) (rsp *pb.DocSvcLoadChunkRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	if req.FilePath == "" || req.DocProcessingConfig == nil {
		return nil, stderr.BadRequest.Error("nil FilePath or DocProcessingConfig")
	}

	dpc := req.DocProcessingConfig

	proc, err := GetDocProcessor(dpc)
	if err != nil {
		return nil, err
	}
	return proc(ctx, req.FilePath, dpc, nil, nil, PreviewHeadPages)
}

func (m *KnowledgeBaseManager) IsDocumentExistent(ctx context.Context, req *pb.IsDocumentExistentReq) (rsp *pb.IsDocumentExistentRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	doc, err := GetDocumentStore().Exists(req.KnowledgeBaseId, req.FilePath)
	if err != nil {
		return
	}
	rsp = &pb.IsDocumentExistentRsp{
		Existent: doc != nil,
		Document: doc.ToPb(),
	}
	return
}

func (m *KnowledgeBaseManager) CollectKnowledgeBaseStats(ctx context.Context, req *pb.CollectKnowledgeBaseStatsReq) (rsp *pb.CollectKnowledgeBaseStatsRsp, err error) {
	uCtx := req.UserContext
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return nil, stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	projectId := uCtx.ProjectId
	rsp = new(pb.CollectKnowledgeBaseStatsRsp)

	kb := m.q.KnowledgeBase
	cond := kb.Select(kb.Id).Where(kb.IsVisible.Is(true))
	if projectId != "" && projectId != "all" {
		cond = cond.Where(kb.ProjectId.Eq(projectId))
	}
	kbs, err := cond.Find()
	if err != nil {
		return nil, err
	}
	rsp.NumKnowledgeBases = int64(len(kbs))

	kbIds := make([]string, 0, len(kbs))
	for _, x := range kbs {
		kbIds = append(kbIds, x.Id)
	}
	doc := m.q.Document
	rsp.NumDocs, err = doc.Where(doc.KnowledgeBaseId.In(kbIds...)).Count()
	if err != nil {
		return nil, err
	}

	// chunk := m.q.Chunk
	// chunks, err := chunk.Select(chunk.AugmentedChunks).Where(chunk.KnowledgeBaseId.In(kbIds...)).Find()
	// if err != nil {
	// 	return nil, err
	// }
	// rsp.NumChunks = int64(len(chunks))
	// for _, x := range chunks {
	// 	rsp.NumsAugChunks += int64(len(x.AugmentedChunks))
	// }

	// 暂时不需要统计增强分段的个数
	chunk := m.q.Chunk
	numChunks, err := chunk.Where(chunk.KnowledgeBaseId.In(kbIds...)).Count()
	if err != nil {
		return nil, err
	}
	rsp.NumChunks = numChunks

	return rsp, nil
}

// CollectKnowledgeBaseStoreStats 统计知识库的存储开销, 按用量排序返回topK条
func (m *KnowledgeBaseManager) CollectKnowledgeBaseStoreStats(projectId string, topK int, asc bool) (rsp []*models.KnowledgeBaseStorageCost, err error) {
	// 查询项目内可见的知识库ids
	kb := m.q.KnowledgeBase
	cond := kb.Select(kb.Id).Where(kb.IsVisible.Is(true))
	if projectId != "" && projectId != "all" {
		cond = cond.Where(kb.ProjectId.Eq(projectId))
	}
	kbs, err := cond.Find()
	if err != nil {
		return nil, err
	}
	allKbIds := make([]string, 0, len(kbs))
	for _, x := range kbs {
		allKbIds = append(allKbIds, x.Id)
	}

	var docCosts []struct {
		KnowledgeBaseId string
		DocFilesCost    int64
	}
	doc := m.q.Document
	var orderExpr field.Expr
	if asc {
		orderExpr = field.NewField("", "doc_files_cost").Asc()
	} else {
		orderExpr = field.NewField("", "doc_files_cost").Desc()
	}
	err = doc.Select(doc.KnowledgeBaseId, doc.FileSizeBytes.Sum().As("doc_files_cost")).
		Where(doc.KnowledgeBaseId.In(allKbIds...)).
		Group(doc.KnowledgeBaseId).
		Order(orderExpr).
		Limit(topK).
		Scan(&docCosts)
	if err != nil {
		return nil, err
	}

	tarKbIds := make([]string, 0, len(docCosts))
	for _, x := range docCosts {
		tarKbIds = append(tarKbIds, x.KnowledgeBaseId)
	}

	kbs, err = kb.Select(kb.Id, kb.Name).Where(kb.Id.In(tarKbIds...)).Find()
	if err != nil {
		return nil, err
	}
	mp := make(map[string]*models.KnowledgeBase)
	for _, x := range kbs {
		mp[x.Id] = x
	}

	// 目前只有文档存储开销的统计
	rsp = make([]*models.KnowledgeBaseStorageCost, 0, len(docCosts))
	for _, c := range docCosts {
		rsp = append(rsp, &models.KnowledgeBaseStorageCost{
			TotalCost:    c.DocFilesCost,
			Id:           c.KnowledgeBaseId,
			Name:         mp[c.KnowledgeBaseId].Name,
			DocFilesCost: c.DocFilesCost,
		})
	}

	return rsp, nil
}

func (m *KnowledgeBaseManager) ShareKnowledgeBase(ctx context.Context, req *pb.ShareKnowledgeBaseReq) (rsp *pb.ShareKnowledgeBaseRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	repo := m.q.KnowledgeBase
	_, err = repo.Where(repo.Id.Eq(req.KnowledgeBaseId)).UpdateColumn(repo.IsPublic, req.IsPublic)
	if err != nil {
		return nil, err
	}

	return new(pb.ShareKnowledgeBaseRsp), nil
}

func (m *KnowledgeBaseManager) RetryDocumentProcess(ctx context.Context, req *pb.RetryDocumentProcessReq) (rsp *pb.RetryDocumentProcessRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}

	var docIds []string

	if len(req.DocIds) == 0 {
		// docId为空时重试所有失败文档
		docRsp, err := m.ListDocuments(ctx, &pb.ListDocumentsReq{KnowledgeBaseId: req.KnowledgeBaseId})
		if err != nil {
			return nil, stderr.Wrap(err, "list documents")
		}
		for _, d := range docRsp.Result {
			if isDocFailed(d.Prog) {
				docIds = append(docIds, d.Doc.DocId)
			}
		}
	} else {
		docIds = req.DocIds
	}
	err = stdsrv.SyncBatchCallGeneric(docIds, func(docId string) error {
		return RetryDocTask(ctx, req.KnowledgeBaseId, docId, nil)
	})
	if err != nil {
		return nil, err
	}

	return &pb.RetryDocumentProcessRsp{}, nil
}

const (
	RetrieveAPI        = "/api/v1/knowlhub/kbs:retrieve"
	AppletBackendSvc   = "autocv-applet-service"
	KbIdMetaKey        = "knowledge_base_id"
	KbProjectIdMetaKey = "project_id"
	KbNameMetaKey      = "model_name" // mlops固定取该字段展示名字
)

func (m *KnowledgeBaseManager) PublishKnowledgeBase(ctx context.Context, req *pb.PublishKnowledgeBaseReq) (rsp *pb.PublishKnowledgeBaseRsp, err error) {
	getKb, err := m.GetKnowledgeBase(ctx, &pb.GetKnowledgeBaseReq{
		UserContext: req.Ctx,
		Id:          req.Id,
	})
	if err != nil {
		return nil, err
	}
	if getKb.Result == nil || getKb.Result.KnowledgeBase == nil {
		return nil, stderr.Internal.Error("failed to get knowledge base %s", req.Id)
	}
	kb := getKb.Result.KnowledgeBase
	namespace := k8s.CurrentNamespaceInCluster()
	rateLimit := req.RateLimit
	retrieveReqExample := &pb.RetrieveKnowledgeBaseReq{
		Query: "xxx",
	}
	example, _ := json.Marshal(retrieveReqExample)
	kbId := ""
	servingKb, err := clients.MLOpsCli.Cli.List(ctx, &serving.ListServiceReq{
		SourceTypes: []serving.SourceType{serving.SourceType_SOURCE_TYPE_KNOWLEDGE},
		SourceMetaExtra: map[string]string{
			KbIdMetaKey:        kb.Id,
			KbProjectIdMetaKey: kb.ProjectId,
		},
	})
	if err != nil {
		return nil, stderr.Wrap(err, "failed list serving knowledge base service")
	}
	if len(servingKb.ServiceInfos) != 0 {
		kbId = servingKb.ServiceInfos[0].Id
	}
	k8sFormatId := vstd.FormatNameAsK8SRsc(kb.Id)
	serviceRsp, err := clients.MLOpsCli.Cli.CreateRemote(ctx, &serving.MLOpsRemoteServiceInfoReq{
		Id:            kbId,
		Name:          req.Name,
		Desc:          req.Desc,
		Creator:       req.Ctx.UserName,
		VirtualSvcUrl: fmt.Sprintf("knowledge_base/%s/%s", namespace, k8sFormatId),
		//Apis: []*serving.API{
		//	{
		//		Port:        80,
		//		Type:        "http",
		//		Url:         []string{RetrieveAPI},
		//		UrlParamMap: map[string]string{RetrieveAPI: string(example)},
		//	},
		//},
		Endpoints: []*common.Endpoint{
			{
				Port: 80,
				Type: common.EndpointType_ENDPOINT_TYPE_HTTP,
				ApiAttrs: []*common.APIAttr{
					{
						ApiPath:    RetrieveAPI,
						ReqExample: string(example),
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					},
				},
				IsDefault: true,
			},
		},
		RateLimit: rateLimit,
		GuardrailsConfig: &serving.GuardrailsConfig{
			IsSecurity: req.IsSecurity,
		},
		QueryParams: map[string]string{
			"project_id":                     req.Ctx.ProjectId,
			helper.QueryParamKnowledgeBaseID: kb.Id,
		},
		RemoteServiceUrl: fmt.Sprintf("http://%s.%s.%s%s", AppletBackendSvc, namespace, conf.Config.InvokeConfig.SvcSuffix, RetrieveAPI),
		SourceMeta: &serving.SourceMeta{Extra: map[string]string{
			KbIdMetaKey:        kb.Id,
			KbProjectIdMetaKey: kb.ProjectId,
			KbNameMetaKey:      kb.Name,
		}},
		SourceType: serving.SourceType_SOURCE_TYPE_KNOWLEDGE,
		LimitTime:  -1, // 无限制
	})
	if err != nil {
		return nil, stderr.Wrap(err, "failed to publish knowledge base service")
	}
	deployResult, err := clients.MLOpsCli.Cli.Deploy(ctx, &serving.ServiceID{Id: serviceRsp.Id})
	if err != nil {
		stdlog.WithError(err).Error("failed to deploy published knowledge base service")
	} else {
		stdlog.Infof("success deploy published knowledge base service %s", deployResult.Id)
	}
	kb.PublishInfo = &pb.KnowledgeBasePublishInfo{
		Id:            serviceRsp.Id,
		VirtualSvcUrl: serviceRsp.VirtualSvcUrl,
		Name:          req.Name,
		Desc:          req.Desc,
		RateLimit:     req.RateLimit,
		IsSecurity:    req.IsSecurity,
	}
	kb.IsPublished = true
	_, _ = m.UpdateKnowledgeBase(ctx, &pb.UpdateKnowledgeBaseReq{
		UserContext: req.Ctx,
		Base:        kb,
	})
	return &pb.PublishKnowledgeBaseRsp{
		Id:            serviceRsp.Id,
		VirtualSvcUrl: serviceRsp.VirtualSvcUrl,
	}, nil
}

func (m *KnowledgeBaseManager) TraceDocElements(ctx context.Context, req *pb.TraceDocElementsReq) (rsp *pb.TraceDocElementsRsp, err error) {
	if err = m.checkUserContext(ctx, req.UserContext); err != nil {
		return
	}
	c := m.q.Chunk
	chunk, err := c.Where(c.Id.Eq(req.ChunkId)).Take()
	if err != nil {
		return nil, err
	}
	if len(chunk.ElementIDs) == 0 {
		return &pb.TraceDocElementsRsp{Elements: []*pb.DocElement{}}, nil
	}
	e := m.q.DocElement
	elements, err := e.Where(e.Id.In(chunk.ElementIDs...)).Find()
	if err != nil {
		return nil, err
	}
	return &pb.TraceDocElementsRsp{Elements: models.ToDocElementPbs(elements)}, nil
}

func (m *KnowledgeBaseManager) RefreshHealthz(ctx context.Context) (err error) {
	TryRefreshHealthz()
	return nil
}

func (m *KnowledgeBaseManager) GetHealthzState(ctx context.Context) HealthzState {
	return GetHealthzState()
}

func (m *KnowledgeBaseManager) RebuildIndex(ctx context.Context, kbId string) error {
	kb, err := m.takeKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return err
	}
	err = handler.Drop(ctx)
	if err != nil {
		return err
	}
	stdlog.Infof("[kb: %s] drop index successfully", kbId)

	docs, err := GetDocumentStore().LoadByKnowledgeBase(kbId)
	if err != nil {
		return err
	}
	for _, doc := range docs {
		task := NewDocTask(ctx, kbId, nil, doc.ToPb())
		err := GetTaskManger().SubmitRebuildIndex(task)
		if err != nil {
			return stderr.Wrap(err, "[kb: %s, doc: %s]submit rebuild index task", kbId, doc.Id)
		}
	}
	return nil
}

func (m *KnowledgeBaseManager) checkUserContext(ctx context.Context, uCtx *pb.UserContext) error {
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	if uCtx == nil {
		return stderr.Internal.Error("user context must be set.")
	}
	if uCtx.ProjectId == "" {
		return stderr.Internal.Error("project id must be set.")
	}
	return nil
}

func (m *KnowledgeBaseManager) checkUserContextAllowEmptyProjectID(ctx context.Context, uCtx *pb.UserContext) error {
	if uCtx == nil {
		pbUCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return stderr.Wrap(err, "try to get user context from http context")
		}
		uCtx = pbUCtx
	}
	if uCtx == nil {
		return stderr.Internal.Error("user context must be set.")
	}
	return nil
}

func (m *KnowledgeBaseManager) queryKnowledgeBaseInfo(ctx context.Context, kb *models.KnowledgeBase) (*pb.KnowledgeBaseInfo, error) {
	handler, err := NewHandler(ctx, kb)
	if err != nil {
		return nil, err
	}
	var numDocs int32
	if kb.DocProcessingConfig != nil && kb.DocProcessingConfig.DocProcessingStrategyOrigin == pb.StrategyOrigin_DOC_ENGINE {
		docEngineKb, err := handler.(*InnerHandler).docEngine.GetKnowledgeBase(ctx)
		if err != nil {
			return nil, stderr.Wrap(err, "CountDocEngineDocuments")
		}
		numDocs = docEngineKb.Result.DocsNum
	} else {
		numDocs, err = handler.CountDocuments(ctx)
	}
	if err != nil {
		return nil, stderr.Wrap(err, "CountDocuments")
	}
	kbInfo := &pb.KnowledgeBaseInfo{
		KnowledgeBase:               kb.ToPb(),
		NumDocs:                     numDocs,
		SupportedRetrieveStrategies: handler.SupportedRetrieveStrategies(),
	}
	return kbInfo, nil
}

func (m *KnowledgeBaseManager) checkProjectID(projectID, id string) error {
	if projectID == "all" {
		return nil
	}
	ret, err := m.takeKnowledgeBase(id)
	if err != nil {
		return err
	}
	if ret.ProjectId != projectID {
		return stderr.Internal.Error("kb id with wrong project id")
	}
	return nil
}

func (m *KnowledgeBaseManager) takeKnowledgeBase(id string) (kb *models.KnowledgeBase, err error) {
	repo := m.q.KnowledgeBase
	kb, err = repo.Where(repo.Id.Eq(id)).Take()
	if err == gorm.ErrRecordNotFound {
		return nil, stderr.KnowlBaseNotFound.Errorf("id=%s", id)
	}
	if err != nil {
		return nil, err
	}
	return kb, nil
}

func (m *KnowledgeBaseManager) takeOrCreateKnowledgeBase(id string, uc *pb.UserContext, autoCreateKb bool) (kb *models.KnowledgeBase, err error) {
	repo := m.q.KnowledgeBase
	cnt, err := repo.Where(repo.Id.Eq(id)).Count()
	if err != nil {
		return nil, stderr.Wrap(err, "count kb, id=%s", id)
	}
	if cnt > 0 {
		kb, err = repo.Where(repo.Id.Eq(id)).Take()
		if err != nil {
			return nil, err
		}
		tp, err := GetStoreType(kb)
		if err != nil || tp != InnerStore {
			return nil, stderr.Wrap(err, "SubmitFileToKnowledgeBase only supports InnerStore")
		}
	} else {
		if !autoCreateKb {
			return nil, fmt.Errorf("SubmitFileToKnowledgeBase: kb not found and AutoCreateKb is false, id:[%v]", id)
		}
		// not exists, create a temp kb
		kb, err = NewKBForAgentConfig(id, uc.UserName, uc.ProjectId)
		if err != nil {
			return nil, stderr.Wrap(err, "NewKBForAgentConfig")
		}
		if err = repo.Create(kb); err != nil {
			return nil, stderr.Wrap(err, "create kb err")
		}
		kb, err = repo.Where(repo.Id.Eq(kb.Id)).Take()
		if err != nil {
			return nil, stderr.Wrap(err, "get kb err")
		}
	}
	return
}

func updateDisabledDocs(disabledDocs []string, docId string, disabled bool) []string {
	index := sort.SearchStrings(disabledDocs, docId)

	// 如果要禁用文档，且文档不存在，则插入
	if disabled && (index >= len(disabledDocs) || disabledDocs[index] != docId) {
		disabledDocs = append(disabledDocs, "")
		copy(disabledDocs[index+1:], disabledDocs[index:])
		disabledDocs[index] = docId
	} else if !disabled && index < len(disabledDocs) && disabledDocs[index] == docId {
		// 如果要启用文档，且文档存在，则删除
		copy(disabledDocs[index:], disabledDocs[index+1:])
		disabledDocs = disabledDocs[:len(disabledDocs)-1]
	}

	return disabledDocs
}

func checkAugmentConfig(kb *models.KnowledgeBase) error {
	if kb.DocProcessingConfig == nil {
		return ErrNilDocProcessingConfig
	}
	if kb.DocProcessingConfig.ChunkAugmentConfig == nil {
		return ErrNilChunkAugmentConfig
	}
	return nil
}

func augmentChunk(ctx context.Context, chunk *pb.Chunk, cfg *pb.ChunkAugmentConfig) error {
	return augmentChunks(ctx, []*pb.Chunk{chunk}, cfg)
}

func augmentChunksWithProg(ctx context.Context, data []*pb.Chunk, cfg *pb.ChunkAugmentConfig, prog *pb.DocumentProcessingProgress, percAddUnit float32) error {
	if cfg == nil || (!cfg.Enabled && !cfg.ImageEnable) {
		return nil
	}
	// 过滤掉表格类型chunk
	txtChunks := make([]*pb.Chunk, 0, len(data))
	tbChunks := make([]*pb.Chunk, 0, len(data)/2)
	imgChunks := make([]*pb.Chunk, 0)

	for _, c := range data {
		switch c.ContentType {
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE:
			tbChunks = append(tbChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT:
			txtChunks = append(txtChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_MARKDOWN:
			txtChunks = append(txtChunks, c)
		case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE:
			imgChunks = append(imgChunks, c)
		}
	}

	lock := new(sync.Mutex)
	getCallBack := func(div float32) func() {
		return func() {
			lock.Lock()
			defer lock.Unlock()
			if prog != nil {
				prog.Percentage += percAddUnit * div
			}
		}
	}

	var reqs []*pclients.EnhanceReq

	if cfg.Enabled {
		divTab := 1.0 / (float32(cfg.TableDescNum+cfg.TableSummaryNum) + Epsilon)
		// table desc & summary
		if len(tbChunks) > 0 {
			if cfg.TableDescNum > 0 {
				reqs = append(reqs, &pclients.EnhanceReq{
					Prompts:  []string{cfg.TableDescPrompt},
					Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION},
					Num:      int(cfg.TableDescNum),
					Chunks:   tbChunks,
					Callback: getCallBack(divTab),
				})
			}
			if cfg.TableSummaryNum > 0 {
				reqs = append(reqs, &pclients.EnhanceReq{
					Prompts:  []string{cfg.TableSummaryPrompt},
					Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY},
					Num:      int(cfg.TableSummaryNum),
					Chunks:   tbChunks,
					Callback: getCallBack(divTab),
				})
			}
		}

		divTxt := 1.0 / (float32(cfg.QuestionNum+cfg.SummaryNum) + Epsilon)
		// text question
		if cfg.QuestionNum > 0 {
			reqs = append(reqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.QuestionPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION},
				Num:      int(cfg.QuestionNum),
				Chunks:   txtChunks,
				Callback: getCallBack(divTxt),
				Params: map[string]any{
					"temperature": conf.Config.KnowlhubConfig.AugmentQuestionsTemperature,
				},
			})
		}
		// text summary
		if cfg.SummaryNum > 0 {
			reqs = append(reqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.SummaryPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY},
				Num:      int(cfg.SummaryNum),
				Chunks:   txtChunks,
				Callback: getCallBack(divTxt),
			})
		}
	}

	divImg := 1.0 / (float32(cfg.ImageDescNum) + Epsilon)
	var imgReqs []*pclients.EnhanceReq
	if cfg.ImageEnable && cfg.ImageDescNum > 0 {
		if len(imgChunks) > 0 {
			imgReqs = append(imgReqs, &pclients.EnhanceReq{
				Prompts:  []string{cfg.ImageDescPrompt},
				Modes:    []pb.AugmentedChunkType{pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION},
				Num:      int(cfg.ImageDescNum),
				Chunks:   imgChunks,
				Callback: getCallBack(divImg),
			})
		}
	}

	wg := sync.WaitGroup{}
	errs := make([]error, 0)
	if len(reqs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := pclients.EnhanceWithReq(ctx, cfg.TextModel, conf.Config.KnowlhubConfig.AugmentDefaultConcurrency, reqs)
			if err != nil {
				errs = append(errs, err)
			}
		}()
	}
	if len(imgReqs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := pclients.EnhanceWithReq(ctx, cfg.ImageModel, conf.Config.KnowlhubConfig.AugmentDefaultConcurrency, imgReqs)
			if err != nil {
				errs = append(errs, err)
			}
		}()
	}
	wg.Wait()
	if len(errs) > 0 {
		return fmt.Errorf("enhance failed: %+v", errs)
	}
	return nil
}

func augmentChunks(ctx context.Context, data []*pb.Chunk, cfg *pb.ChunkAugmentConfig) error {
	return augmentChunksWithProg(ctx, data, cfg, nil, 0)
}

func Rerank(ctx context.Context, query string, params *pb.RerankParams, chunks []*pb.ChunkRetrieveResult) ([]*pb.ChunkRetrieveResult, error) {
	return pclients.RerankChunksAndFilter(ctx, query, params, chunks)
}

func isLongerThanEmedMaxLength(content string) bool {
	return utf8.RuneCountInString(content) > conf.Config.KnowlhubConfig.EmbeddingMaxLength
}

func cutContent(content string) string {
	if isLongerThanEmedMaxLength(content) {
		var truncated []rune
		for i, r := range content {
			if i >= conf.Config.KnowlhubConfig.EmbeddingMaxLength {
				break
			}
			truncated = append(truncated, r)
		}

		return string(truncated)
	}
	return content
}

func defaultChunkIdxSetting(chunk *models.Chunk) (DisableVectorIndexing, DisableFullTextIndexing bool) {
	switch chunk.ContentType {
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT:
		return false, false
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE:
		return true, false
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_JSON:
		return true, true
	case pb.OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE:
		return true, true
	default:
		return false, false
	}
}

func refreshChunkIdxSetting(c *models.Chunk) *models.ChunkForIndexing {
	if c == nil {
		return nil
	}
	ci := new(models.ChunkForIndexing)
	c.DisableVectorIndexing, c.DisableFullTextIndexing = defaultChunkIdxSetting(c)
	ci = ci.FromChunk(c)
	if !c.DisableVectorIndexing && isLongerThanEmedMaxLength(c.Content) {
		//c.DisableVectorIndexing = true
		// 不再跳过向量化，改为截断
		ci.Text = cutContent(c.Content)
	}
	return ci
}

func defaultAugChunkIdxSetting(ac *pb.AugmentedChunk) (DisableVectorIndexing, DisableFullTextIndexing bool) {
	return false, false
}

func refreshAugChunkIdxSetting(c *models.Chunk, ac *pb.AugmentedChunk) *models.ChunkForIndexing {
	if ac == nil {
		return nil
	}
	ci := new(models.ChunkForIndexing)
	ac.DisableVectorIndexing, ac.DisableFullTextIndexing = defaultAugChunkIdxSetting(ac)
	ci = ci.FromAugChunk(c, ac)
	if !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content) {
		//ac.DisableVectorIndexing = true
		// 不再跳过向量化，改为截断
		ci.Text = cutContent(ac.Content)
	}
	return ci
}

func makeChunksForIndexing(chunks []*models.Chunk) []*models.ChunkForIndexing {
	ics := make([]*models.ChunkForIndexing, 0, len(chunks))
	if len(chunks) == 0 {
		return ics
	}
	for _, c := range chunks {
		embContent := c.Content
		if !c.DisableVectorIndexing && isLongerThanEmedMaxLength(c.Content) {
			// 现在不再跳过向量化，改为截断内容
			stdlog.Warnf("chunk content is too long, cut content:[%s]", c.Content)
			//c.DisableVectorIndexing = true
			embContent = cutContent(c.Content)
		}
		ics = append(ics, &models.ChunkForIndexing{
			Id:                      c.Id,
			OriId:                   c.Id,
			DocId:                   c.DocumentId,
			KBId:                    c.KnowledgeBaseId,
			Text:                    embContent,
			DisableVectorIndexing:   c.DisableVectorIndexing,
			DisableFullTextIndexing: c.DisableFullTextIndexing,
		})

		for _, ac := range c.AugmentedChunks {
			embContent := ac.Content
			if !ac.DisableVectorIndexing && isLongerThanEmedMaxLength(ac.Content) {
				// 现在不再跳过向量化，改为截断内容
				stdlog.Warnf("augmented chunk content is too long, cut content, orginal:[%s], aug:[%s]", c.Content, ac.Content)
				//ac.DisableVectorIndexing = true
				embContent = cutContent(ac.Content)
			}
			ics = append(ics, &models.ChunkForIndexing{
				Id:                      ac.Id,
				OriId:                   c.Id,
				DocId:                   c.DocumentId,
				KBId:                    c.KnowledgeBaseId,
				Text:                    embContent,
				DisableVectorIndexing:   ac.DisableVectorIndexing,
				DisableFullTextIndexing: ac.DisableFullTextIndexing,
			})
		}
	}
	return ics
}

func isProgSucceeded(prog *pb.DocumentProcessingProgress) bool {
	return math.Abs(float64(100.0-prog.Percentage)) < 1e-5
}

// NewKBForAgentConfig 用于agent配置-本地文件上传的知识库构建
func NewKBForAgentConfig(uuid, creator, projectId string) (*models.KnowledgeBase, error) {
	ms, err := DefaultVecModelSvc(projectId)
	if err != nil {
		return nil, err
	}
	rc, err := DefaultRetrievalConfig(projectId)
	if err != nil {
		return nil, err
	}
	kb := &models.KnowledgeBase{
		Id:                  uuid,
		Name:                AgentConfigKbNamePrefix + uuid,
		ContentType:         pb.KnowledgeBaseContentType_TEXT,
		SourceType:          pb.KnowledgeBaseSourceType_FROM_SCRATCH,
		RegistryType:        pb.KnowledgeBaseRegistryType_NULL,
		Creator:             creator,
		ProjectId:           projectId,
		IsVisible:           false, // 不可见，待agent配置完成后需要调用接口更新为可见
		IsRetrievable:       true,
		CreationType:        pb.KnowledgeBaseCreationType_FROM_AGENT_CONFIGURATION,
		DocProcessingConfig: DefaultDocProcessingConfig,
		RetrievalConfig:     rc,
		VectorModel:         ms,
	}
	return kb, nil
}

func orderAndFilterRetrieveResults(results []*pb.ChunkRetrieveResult, threshold float32) []*pb.ChunkRetrieveResult {
	// desc order
	sort.Slice(results, func(i int, j int) bool {
		return results[i].Score > results[j].Score
	})
	idx := sort.Search(len(results), func(i int) bool { return results[i].Score < threshold })
	return results[:idx]
}

func GetKbsStats(ctx context.Context) (rsp *pb.CollectKnowledgeBaseStatsRsp, err error) {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, err
	}
	req := new(pb.CollectKnowledgeBaseStatsReq)
	req.UserContext = pbUserCtx
	return kbm.CollectKnowledgeBaseStats(ctx, req)
}

func (m *KnowledgeBaseManager) ListKnowledgeBasesSearchData() (*pb.ListKnowledgeBasesRsp, error) {
	repo := m.q.KnowledgeBase

	condition := repo.Where(repo.IsVisible.Is(true))
	oriCon := repo.Where(repo.IsPublished.Is(true))
	condition = condition.Where(oriCon)

	kbs, err := condition.Find()
	if err != nil {
		return nil, stderr.Wrap(err, "list kb simple error.")
	}

	ret := make([]*pb.KnowledgeBaseInfo, len(kbs))
	for i, kb := range kbs {
		ret[i] = &pb.KnowledgeBaseInfo{KnowledgeBase: kb.ToPb()}
	}

	return &pb.ListKnowledgeBasesRsp{Result: ret}, nil
}
func SetRelatedApps(ctx context.Context, kbInfos []*pb.KnowledgeBaseInfo) error {
	dpInfos, err := applet.ChainManager.GetAppDependencyInfos(ctx)
	if err != nil {
		return stderr.Wrap(err, "get app dependency infos")
	}
	for _, kbInfo := range kbInfos {
		if kbInfo.KnowledgeBase == nil {
			continue
		}
		id := kbInfo.KnowledgeBase.Id
		for _, dpInfo := range dpInfos {
			if dpInfo.KnowledgeBaseIds == nil || dpInfo.SimpleChainInfo == nil {
				continue
			}
			if _, ok := dpInfo.KnowledgeBaseIds[id]; ok {
				kbInfo.AppRelations = append(kbInfo.AppRelations, &pb.AppRelation{
					AppId:   dpInfo.SimpleChainInfo.Id,
					AppName: dpInfo.SimpleChainInfo.Name,
				})
			}
		}
		kbInfo.NumApps = int32(len(kbInfo.AppRelations))
	}
	return nil
}
