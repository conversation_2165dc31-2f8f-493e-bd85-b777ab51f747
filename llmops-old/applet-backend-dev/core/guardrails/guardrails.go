package guardrails

import (
	"context"
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"

	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type Guardrails interface {
	GetSafetyConfig(ctx context.Context, pid string) (*widgets.SafetyConfig, error)
	CreateSafetyConfig(ctx context.Context, config *widgets.SafetyConfig) error
	UpdateSafetyConfig(ctx context.Context, config *widgets.SafetyConfig) error
	DeleteSafetyConfig(ctx context.Context, id string) error
	GetSafetyConfigById(ctx context.Context, id string, needFileContent bool) (*widgets.SafetyConfig, error)
	GetAllSafetyConfig(ctx context.Context, needFileContent bool) ([]*widgets.SafetyConfig, error)

	// UpsertSafetyConfig updateServiceConfig 自动更新安全护栏服务配置
	UpsertSafetyConfig(ctx context.Context, config *widgets.SafetyConfig, updateServiceConfig bool) error
}

type GuardrailsImpl struct {
}

func (g GuardrailsImpl) GetSafetyConfig(ctx context.Context, pid string) (*widgets.SafetyConfig, error) {
	safetyConfig, err := dao.GuardrailsImpl.GetByProjectId(ctx, pid)
	if err != nil {
		stdlog.Errorf("get safety config :%v from db err :%v", pid, err)
		return nil, err
	}
	result, err := safetyConfigWarp(safetyConfig)
	if err != nil {
		stdlog.Errorf("GetSafetyConfig: warp safety config : project_id is %v err :%v", pid, err)
		return nil, err
	}
	return result, nil
}

func (g GuardrailsImpl) CreateSafetyConfig(ctx context.Context, config *widgets.SafetyConfig) error {
	po := &generated.SafetyConfig{
		ProjectID:        config.ProjectID,
		ID:               config.ID,
		InputGuardrails:  StructToJson(config.InputGuardrails),
		OutputGuardrails: StructToJson(config.OutputGuardrails),
	}
	err := dao.GuardrailsImpl.CreateSafetyConfig(ctx, po)
	if err != nil {
		stdlog.Errorf("create safety config :%v to db err :%v", config, err)
		return err
	}
	return nil
}

func (g GuardrailsImpl) UpdateSafetyConfig(ctx context.Context, config *widgets.SafetyConfig) error {
	po := &generated.SafetyConfig{
		ProjectID:        config.ProjectID,
		ID:               config.ID,
		InputGuardrails:  StructToJson(config.InputGuardrails),
		OutputGuardrails: StructToJson(config.OutputGuardrails),
	}
	err := dao.GuardrailsImpl.UpdateSafetyConfig(ctx, po)
	if err != nil {
		stdlog.Errorf("update safety config :%v to db err :%v", config, err)
		return err
	}
	return nil
}

func (g GuardrailsImpl) DeleteSafetyConfig(ctx context.Context, id string) error {
	// TODO implement me
	panic("implement me")
}

// 将struct 转成string 函数
func StructToJson(data interface{}) string {
	jsonStr, err := json.Marshal(data)
	if err != nil {
		stdlog.Errorf("struct to json err :%v", err)
		return ""
	}
	return string(jsonStr)
}

func (g GuardrailsImpl) GetSafetyConfigById(ctx context.Context, id string, needFileContent bool) (*widgets.SafetyConfig, error) {
	safetyConfig, err := dao.GuardrailsImpl.GetByID(ctx, id)
	if err != nil {
		stdlog.Errorf("get safety config :%v from db err :%v", id, err)
		return nil, err
	}
	result, err := safetyConfigWarp(safetyConfig)
	if err != nil {
		stdlog.Errorf("GetSafetyConfigById: warp safety config : id is %v err :%v", id, err)
		return nil, err
	}

	if needFileContent {
		err := widgets.SetFileContent(result)
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}

func safetyConfigWarp(safetyConfig *generated.SafetyConfig) (*widgets.SafetyConfig, error) {
	inputGuardrails := &widgets.InputGuardrails{}
	err := json.Unmarshal([]byte(safetyConfig.InputGuardrails), inputGuardrails)
	if err != nil {
		stdlog.Errorf("unmarshal input guardrails err :%v", err)
		return nil, err
	}
	outputGuardrails := &widgets.OutputGuardrails{}
	err = json.Unmarshal([]byte(safetyConfig.OutputGuardrails), outputGuardrails)
	if err != nil {
		stdlog.Errorf("unmarshal output guardrails err :%v", err)
		return nil, err
	}
	result := &widgets.SafetyConfig{
		ProjectID:        safetyConfig.ProjectID,
		ID:               safetyConfig.ID,
		InputGuardrails:  inputGuardrails,
		OutputGuardrails: outputGuardrails,
	}
	return result, nil
}

func (g GuardrailsImpl) GetAllSafetyConfig(ctx context.Context, needFileContent bool) ([]*widgets.SafetyConfig, error) {
	safetyConfigs, err := dao.GuardrailsImpl.GetAllSafetyConfig(ctx)
	if err != nil {
		stdlog.Errorf("get all safety config from db err :%v", err)
		return nil, err
	}
	result := make([]*widgets.SafetyConfig, 0)
	for _, safetyConfig := range safetyConfigs {
		config, err := safetyConfigWarp(safetyConfig)
		if err != nil {
			return nil, err
		}
		result = append(result, config)
	}

	if needFileContent {
		for _, safetyConfig := range result {
			err = widgets.SetFileContent(safetyConfig)
			if err != nil {
				return nil, err
			}
		}
	}
	return result, nil
}

func (g GuardrailsImpl) UpsertSafetyConfig(ctx context.Context, config *widgets.SafetyConfig, updateGuardrailServiceConfig bool) error {
	po := &generated.SafetyConfig{
		ProjectID:        config.ProjectID,
		ID:               config.ID,
		InputGuardrails:  StructToJson(config.InputGuardrails),
		OutputGuardrails: StructToJson(config.OutputGuardrails),
	}
	err := dao.GuardrailsImpl.UpsertSafetyConfig(ctx, po)
	if err != nil {
		return stderr.Trace(err)
	}
	if updateGuardrailServiceConfig {
		return widgets.UpdateGuardrailsServerConfig(ctx, config)
	}
	return nil
}
