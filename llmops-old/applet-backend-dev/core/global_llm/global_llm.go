package global_llm

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

var (
	ErrProjModelNotConfigured                = stderr.BadRequest.Error("project model not configured")
	ErrProjLLMModelNotConfigured             = stderr.BadRequest.Error("project llm model not configured")
	ErrProjEmbModelNotConfigured             = stderr.BadRequest.Error("project embedding model not configured")
	ErrProjRerankModelNotConfigured          = stderr.BadRequest.Error("project rerank model not configured")
	ErrProjImageUnderstandModelNotConfigured = stderr.BadRequest.Error("project image understand model not configured")
	ErrProjOcrModelNotConfigured             = stderr.BadRequest.Error("project ocr model not configured")
)

type LLModel struct {
}

func (LLModel) ListAllModels() ([]*dao.LLMBasicConfigQueryParam, error) {
	return nil, nil
}

func (LLModel) GetModelByProjectID(ctx context.Context, projectID string) (*models.LLMBasicConfig, error) {
	l := dao.LLMBasicConfigDAO{}
	model, err := l.GetByProjectID(ctx, projectID)
	if err != nil {
		stdlog.Errorf("get llmModel by ProjectID failed: %v", err)
		return nil, err
	}
	if model == nil {
		return nil, ErrProjModelNotConfigured
	}
	llmModel := models.LLMBasicConfig{
		ProjectID: model.ProjectID,
	}
	// 反序列化
	err = helper.String2Struct(model.LlmModelSvc, &llmModel.ModelsForAgent.LLMModelSvc)
	if err != nil {
		stdlog.Errorf("unmarshal llmModel svc failed: %v", err)
		return nil, err
	}
	err = helper.String2Struct(model.EmbeddingSvc, &llmModel.ModelsForAgent.EmbeddingSvc)
	if err != nil {
		stdlog.Errorf("unmarshal embedding svc failed: %v", err)
		return nil, err

	}
	err = helper.String2Struct(model.ImageGenSvc, &llmModel.ModelsForAgent.ImageGenSvc)
	if err != nil {
		stdlog.Errorf("unmarshal image gen svc failed: %v", err)
		return nil, err
	}
	err = helper.String2Struct(model.ReRankSvc, &llmModel.ModelsForAgent.RerankSvc)
	if err != nil {
		stdlog.Errorf("unmarshal rerank svc failed: %v", err)
		return nil, err
	}
	err = helper.String2Struct(model.ImageUnderstandSvc, &llmModel.ModelsForAgent.ImageUnderstandSvc)
	if err != nil {
		stdlog.Errorf("unmarshal image understand svc failed: %v", err)
		return nil, err
	}
	err = helper.String2Struct(model.OcrSvc, &llmModel.ModelsForAgent.OcrSvc)
	if err != nil {
		stdlog.Errorf("unmarshal image understand svc failed: %v", err)
		return nil, err
	}
	return &llmModel, nil
}

func (LLModel) CreateOrUpdateModel(ctx context.Context, model models.LLMBasicConfig) error {
	l := dao.LLMBasicConfigDAO{}
	// 序列化
	LLMModelSvc := helper.Struct2String(model.ModelsForAgent.LLMModelSvc)
	EmbeddingSvc := helper.Struct2String(model.ModelsForAgent.EmbeddingSvc)
	ImageGenSvc := helper.Struct2String(model.ModelsForAgent.ImageGenSvc)
	RerankSvc := helper.Struct2String(model.ModelsForAgent.RerankSvc)
	ImageUnderstandSvc := helper.Struct2String(model.ModelsForAgent.ImageUnderstandSvc)
	OcrSvc := helper.Struct2String(model.ModelsForAgent.OcrSvc)
	llmModel := generated.LlmBasicConfig{
		ProjectID:          model.ProjectID,
		LlmModelSvc:        LLMModelSvc,
		EmbeddingSvc:       EmbeddingSvc,
		ImageGenSvc:        ImageGenSvc,
		ReRankSvc:          RerankSvc,
		ImageUnderstandSvc: ImageUnderstandSvc,
		OcrSvc:             OcrSvc,
	}
	err := l.CreateOrUpdateBySpec(ctx, &llmModel)
	if err != nil {
		stdlog.Errorf("create llmModel failed: %v", err)
		return err
	}
	return nil
}
