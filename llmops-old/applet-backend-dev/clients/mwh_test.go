package clients

import (
	"testing"
)

func TestMWH(t *testing.T) {
	//ctx := context.Background()
	//ctx = helper.SetUser(ctx, "thinger")
	//ctx = helper.SetProjectID(ctx, "huatai-demo")
	//ctx = helper.SetProjectIDAndTokenForGRPC(ctx, "huatai-demo", "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA")
	//viper.AddConfigPath("../etc/")
	//viper.SetConfigName("app")
	//viper.SetConfigType("yaml")
	//if err := viper.ReadInConfig(); err != nil {
	//	t.Fatal(err)
	//}
	//if err := viper.Unmarshal(&conf.Config, conf.DecConfig); err != nil {
	//	t.Fatal(err)
	//}
	//initMWHClient()
	//
	//res, err := MWHCli.ListModelServices(ctx)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//t.Log(res)

}
