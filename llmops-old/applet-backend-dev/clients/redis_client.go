package clients

import (
	"context"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

// RedisClient 封装了一些简单的redis方法

var (
	rc     clients.RedisClient
	rcOnce sync.Once
	ctx    = context.Background()
)

func GetRedisClient(conf conf.RedisConfig) clients.RedisClient {
	rcOnce.Do(func() {
		rc, _ = clients.NewRedisClient(conf)
	})
	return rc
}
