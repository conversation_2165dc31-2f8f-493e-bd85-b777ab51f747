package clients

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type MWHClient struct {
	ServiceManagerClient pb.ModelServiceManagerClient
	RemoteSvcClient      pb.RemoteServiceManagerClient
}

const (
	ServiceStatusRunning   = "running"
	ServiceStatusHealthy   = "healthy"
	ServiceStatusAvailable = "MLOPS_SVC_STATE_AVAILABLE"
)

func (c *MWHClient) initClient() error {
	mwh := conf.Config.MWH
	grpcConfig := conf.Config.GrpcConfig
	creds := insecure.NewCredentials()
	conn, err := grpc.Dial(mwh.Host+":"+mwh.GrpcPort, grpc.WithTransportCredentials(creds), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(grpcConfig.GetMaxMessageMB())))
	if err != nil {
		return stderr.Wrap(err, "can not connected to mwh")
	}
	stdlog.Infof("connected to mwh successfully")
	c.ServiceManagerClient = pb.NewModelServiceManagerClient(conn)
	c.RemoteSvcClient = pb.NewRemoteServiceManagerClient(conn)
	return nil
}

type AIModelService struct {
	LLMService    []*widgets.AIModelService
	VectorService []*widgets.AIModelService
	DlieService   []*widgets.AIModelService
}

func (c *MWHClient) ListModelServices(ctx context.Context) (*AIModelService, error) {
	res := &AIModelService{}
	llm := make([]*widgets.AIModelService, 0)
	vector := make([]*widgets.AIModelService, 0)
	dlie := make([]*widgets.AIModelService, 0)
	services, err := c.ListHealthyModelServices(ctx)
	if err != nil {
		return nil, err
	}
	for _, s := range services {
		for _, r := range s.Apis {
			if !strings.HasPrefix(r.Path, "/") {
				stdlog.Warnf("invalid model service :%v ,service path :%v", s.ModelId, r.Path)
				continue
			}
			aiSvc := &widgets.AIModelService{
				Name: fmt.Sprintf("%s_%s_%s", s.ModelName, s.ReleaseName, r.Path[1:]),
				LLMModelServiceValue: widgets.LLMModelServiceValue{
					ModelName: r.Path[1:],
					IP:        s.Host,
					Port:      s.Port,
				},
			}
			if s.SubKind == pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION {
				llm = append(llm, aiSvc)
			}
			if s.SubKind == pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR {
				vector = append(vector, aiSvc)
			}
			dlie = append(dlie, aiSvc)
		}
	}
	res.LLMService = llm
	res.VectorService = vector
	res.DlieService = dlie
	return res, nil
}

func (c *MWHClient) ListHealthyModelServices(ctx context.Context) ([]*pb.ModelService, error) {
	pbUserCtx, err := helper.GetUserContext(ctx)
	if err != nil {
		return nil, err
	}
	rsp, err := c.ServiceManagerClient.ReadModelService(ctx, &pb.ReadModelServiceReq{
		IsMwMode:     true,
		IsSeldonMode: true,
		OnlyRunning:  true,
		Ctx:          pbUserCtx,
	})
	if err != nil {
		return nil, stderr.Wrap(err, "read model service error")
	}
	if len(rsp.Services) == 0 {
		stdlog.Warnf("no services")
	}
	modelServices := make([]*pb.ModelService, 0)
	for _, service := range rsp.Services {
		// mlops的模型
		if service.Status.State == ServiceStatusAvailable {
			modelServices = append(modelServices, service)
		}
		// mwh的模型
		if service.Status.State == ServiceStatusRunning && service.Status.Health == ServiceStatusHealthy {
			modelServices = append(modelServices, service)
		}
	}
	return modelServices, nil
}

func (c *MWHClient) ListRemoteServices(ctx context.Context) (*models.RemoteAPI, error) {
	user, err := helper.GetUser(ctx)
	if err != nil {
		return nil, err
	}
	rsp, err := c.RemoteSvcClient.ReadRemoteService(ctx, &pb.ReadRemoteServiceReq{
		Ctx: &pb.UserContext{
			UserId:    user,
			ProjectId: helper.GetProjectID(ctx),
		},
		// CheckReachable: true,
	})
	if err != nil {
		return nil, err
	}
	res := make([]*pb.RemoteService, 0)
	for _, s := range rsp.Services {
		if s.ApiConfig.Method != pb.RemoteServiceMethod_REMOTE_SERVICE_METHOD_POST {
			continue
		}
		// if s.Status == nil || !s.Status.IsReachable {
		//	continue
		// }
		res = append(res, s)
	}
	return &models.RemoteAPI{
		APIs: res,
	}, nil
}
