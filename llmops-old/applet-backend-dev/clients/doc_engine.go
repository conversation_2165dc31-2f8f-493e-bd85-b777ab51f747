package clients

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/conf"
	docEngine "transwarp.io/applied-ai/doc-engine-go/v1"
)

func GetDocEngineCli(ctx context.Context) (*docEngine.DocEngineClient, error) {
	cfg := conf.Config.DocEngineConfig
	docEngineConfig := docEngine.DocEngineConfig{Address: cfg.Address}
	cli, err := docEngine.NewDocEngineCli(ctx, docEngineConfig)
	return cli, err
}
