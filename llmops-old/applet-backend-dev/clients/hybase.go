package clients

import (
	"com/trs/hybase/client"
	"com/trs/hybase/client/params"
	"sync"
	"transwarp.io/applied-ai/applet-backend/conf"
)

var (
	hybaseClient *client.TRSConnection
	hybaseOnce   sync.Once
)

func GetHybaseClient() *client.TRSConnection {
	hybaseOnce.Do(func() {
		hybaseConf := conf.Config.HybaseConfig
		connectParams := params.NewConnectParams()
		if len(hybaseConf.Params) > 0 {
			for key, value := range hybaseConf.Params {
				connectParams.SetProperty(key, value)
			}
		}
		if hybaseConf.Timeout > 0 {
			connectParams.SetTimeout(hybaseConf.Timeout)
		}
		hybaseClient = client.NewTRSConnection(hybaseConf.Url, hybaseConf.Username, hybaseConf.Password, connectParams)
	})
	return hybaseClient
}
