package clients

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"net/http"
	"strconv"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

type CVATClient struct {
	DataConnClient pb.DataConnectionManagerClient
}

const (
	DATA_CONNECTION_PATH = "/api/data/connections"
)

type DataConnectionList struct {
	Result []*pb.DataConnection `json:"result"`
}

func (c *CVATClient) initClient() error {
	cvat := conf.Config.CVATConfig
	grpcConfig := conf.Config.GrpcConfig
	creds := insecure.NewCredentials()
	conn, err := grpc.Dial(cvat.Host+":"+cvat.GrpcPort, grpc.WithTransportCredentials(creds), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(grpcConfig.GetMaxMessageMB())))
	if err != nil {
		return stderr.Wrap(err, "can not connected to cvat")
	}
	stdlog.Infof("connected to cvat successfully")
	c.DataConnClient = pb.NewDataConnectionManagerClient(conn)
	return nil
}

func (c *CVATClient) ListDataConnections(ctx context.Context, type_selector pb.ConnectionType, project_id string, tenant_id string) (*DataConnectionList, error) {
	var dataConnectionList DataConnectionList

	urlStr := fmt.Sprintf("http://%s:%s%s", conf.Config.CVATConfig.Host, conf.Config.CVATConfig.HttpPort, DATA_CONNECTION_PATH)

	token, err := helper.GetToken(ctx)
	if err != nil {
		stdlog.Errorln("Unable to get the token")
		return &dataConnectionList, err
	}

	HeaderMap := map[string]string{
		"Authorization": token,
		"Content-Type":  ContentTypeJSON,
	}

	QueryMap := map[string]string{
		"project_id":      project_id,
		"tenant_id":       tenant_id,
		"type_selector":   strconv.Itoa(int(type_selector)),
		"status_selector": strconv.Itoa(1),
	}

	param := &clients.HttpParam{
		Method:     http.MethodGet,
		Url:        urlStr,
		Header:     HeaderMap,
		QueryParam: QueryMap,
	}

	resInString, err := HttpCli.HttpCallString(ctx, param)

	if err != nil {
		return nil, stderr.Wrap(err, "HttpCallString 报错")
	}

	err = stdsrv.UnmarshalMixWithProto(resInString, &dataConnectionList)

	if err != nil {
		stdlog.Errorln("unable to Unmarshal response string into self-defined struct 'Reponse'.")
		return nil, err
	}

	return &dataConnectionList, nil
}
