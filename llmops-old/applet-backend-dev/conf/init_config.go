package conf

import (
	"encoding/json"
	"fmt"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"os"
	"path"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
)

const (
	AppName                 = "applet-backend"
	DefaultConfigPath       = "etc"    // 配置文件所在路径
	DefaultConfigName       = "app"    // 配置文件前缀
	DefaultConfigFormat     = "yaml"   // 配置文件格式
	EnvPrefix               = "applet" // 自动读取环境变量时添加的前缀 applet -> APPLET_
	KeyEnvsForPublishEngine = "envs_for_publish_engine"
)

var Config *AppConfig

func DecConfig(dc *mapstructure.DecoderConfig) {
	dc.TagName = "yaml" // 使用字段的yaml tag进行映射
}

func TestInit() error {
	etcPath := os.Getenv("ETC_PATH")
	viper.AddConfigPath(etcPath)
	viper.SetConfigName("app-dev")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		return err
	}
	if err := viper.Unmarshal(&Config, DecConfig); err != nil {
		return err
	}
	Config.Engine.KubeconfigPath = path.Join(etcPath, "k3s.yaml")
	bs, _ := json.MarshalIndent(Config, "", "  ")
	fmt.Println(string(bs))
	return nil
}

func Init() {
	// NOTICE: 此处自动读取的环境变量必须可以在配置文件中找到对应的配置项,
	// 否则不会自动加载对应的环境变量
	viper.AutomaticEnv()                     // 开启环境变量自动读取
	viper.SetEnvPrefix(EnvPrefix)            // 设置自动添加的环境变量前缀
	viper.AddConfigPath(DefaultConfigPath)   // 配置文件所在路径
	viper.SetConfigName(DefaultConfigName)   // 配置文件前缀
	viper.SetConfigType(DefaultConfigFormat) // 配置文件格式 e.g. json, yaml .etc
	if err := viper.ReadInConfig(); err != nil {
		panic(any(fmt.Errorf("配置读取失败: %s", err.Error())))
	}
	Config = new(AppConfig)
	if err := viper.Unmarshal(&Config, DecConfig); err != nil {
		panic(any(fmt.Errorf("配置加载失败: %s", err.Error())))
	}

	bs, _ := json.MarshalIndent(Config, "", "  ")
	fmt.Println(string(bs))
}

// GetDocSvcAddr 对doc-svc统一携带ns
// http://autocv-applet-doc-service
// -->> eg  http://autocv-applet-doc-service.dev
func GetDocSvcAddr() string {
	if Config == nil {
		panic("must do conf.Init before GetDocSvcAddr")
	}
	addr := Config.DocSvcConfig.Address
	if !strings.Contains(addr, ".") {
		addr = addr + "." + k8s.CurrentNamespaceInCluster()
	}
	return addr
}
