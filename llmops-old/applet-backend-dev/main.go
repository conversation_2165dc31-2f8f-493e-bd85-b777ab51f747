package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	_ "net/http/pprof"

	"github.com/emicklei/go-restful/v3"
	"google.golang.org/grpc"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/license"
	std_model "transwarp.io/applied-ai/aiot/vision-std/license/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/service"
)

func main() {

	// 服务启动context
	ctx := context.Background()
	// init config
	conf.Init()

	// init widgets
	widgets.Init()

	// // init clients
	clients.Init()

	// // init dao
	dao.Init()

	// init core manager
	if err := core.Init(ctx); err != nil {
		panic(err)
	}
	// ... any others managers

	cfg := conf.Config.Server
	stdlog.Infoln("server listen at " + cfg.Addr)
	prepareServer()

	// 注册etcd
	// go func() {
	//	if err := register.RegService(); err != nil {
	//		stdlog.Error("register svc err")
	//	}
	// }()

	// This will run forever until channel receives error
	stdlog.Infof("Staring Sever Template HTTP service on %s ...", cfg.Addr)
	if err := http.ListenAndServe(cfg.Addr, restful.DefaultContainer); err != nil {
		stdlog.Errorf("Could not start serving service due to (error: %s)", err)
	}
}

func prepareServer() {
	_ = stdlog.Init(conf.Config.ThingerLog.Logger, conf.AppName)
	stderr.SetContext(conf.AppName)
	service.Init()
	service.MountBaseAPIModule()

	// 启动grpc server
	go startGrpcServer()
	service.MountCompleteAPIModule()
	// go checkLicenseLoop()
}

func checkLicenseLoop() {
	checker, err := license.NewChecker(
		context.Background(),
		conf.Config.License.VerifierPath,
		conf.Config.License.LicensorAddr,
		std_model.LLMAppletCubeService,
		conf.Config.License.CheckInterval,
		func(activated bool) {
			if activated {
				service.MountCompleteAPIModule()
			} else {
				service.UnmountEdgeCompletedAPIModule()
			}
		},
	)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to initialize license checker")
		return
	}
	checker.Serve()
}

const MB = 1024 * 1024

func startGrpcServer() {
	serverOptions := []grpc.ServerOption{
		grpc.MaxSendMsgSize(conf.Config.GrpcServerConfig.MaxMessageMB * MB), grpc.MaxRecvMsgSize(conf.Config.GrpcServerConfig.MaxMessageMB * MB),
	}
	server := grpc.NewServer(serverOptions...)
	port := conf.Config.GrpcServerConfig.ServerPort
	listener, err := net.Listen("tcp", port)
	if err != nil {
		panic(any(fmt.Sprintf("can not listen on port %s", port)))
	}
	pb.RegisterKnowledgeBaseManagerServer(server, knowledge_base.GetKnowledgeBaseManager())
	stdlog.Infof("grpc server listen at %s", port)
	err = server.Serve(listener)
	if err != nil {
		panic(any(fmt.Sprint("failed to start grpc server")))
	}

}
