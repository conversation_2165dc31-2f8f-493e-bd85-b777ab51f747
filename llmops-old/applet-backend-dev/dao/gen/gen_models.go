package main

import (
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

/**
本脚本用于从数据库生成模型结构
https://gorm.io/zh_CN/gen/database_to_structs.html
*/

type TableNamer interface {
	TableName() string
}

func main() {
	g := gen.NewGenerator(gen.Config{
		ModelPkgPath: "pkg/models/generated",
		OutPath:      "dao/query",
		Mode:         gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	db, _ := gorm.Open(mysql.Open("root:Warp!CV@2022#@(172.17.120.207:31907)/applet_backend?charset=utf8mb4&parseTime=True&loc=Local"))
	g.UseDB(db)

	definedModels := []any{&models.KnowledgeBase{}, &models.Document{}, &models.Chunk{}, &models.DocElement{}, &models.DocTask{}}
	var modelsToApply []any
	modelsToApply = append(modelsToApply, definedModels...)
	// 用户定义的表
	g.ApplyBasic(modelsToApply...)
	// 数据库中的表
	g.ApplyBasic(

		g.GenerateModel("api_tool_collection_demos",
			gen.FieldType("meta_info", "[]byte")),
		g.GenerateModel("api_tool_collections",
			gen.FieldType("meta_info", "[]byte"),
			gen.FieldType("last_publish_time", "*time.Time")),
		g.GenerateModel("api_tools"),
		g.GenerateModel("applet_chains"),
		g.GenerateModel("applet_experiments"),
		g.GenerateModel("applet_labels"),
		g.GenerateModel("chain_debug_histories"),
		g.GenerateModel("custom_widgets"),
		g.GenerateModel("safety_configs"),
		g.GenerateModel("llm_basic_configs"),
		g.GenerateModel("dialogs"),
		g.GenerateModel("dialog_messages"),
		g.GenerateModel("dialog_apps"),
		g.GenerateModel("chain_snapshots"),
	)
	g.Execute()
}
