package main

//import (
//	"gorm.io/driver/mysql"
//	"gorm.io/gen"
//	"gorm.io/gorm"
//	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
//)
//
///**
//本脚本用于自动生成DAO层代码
//*/
//
//// Dynamic SQL
////type Querier interface {
////	// SELECT * FROM @@table WHERE name = @name{{if weight > 0}} AND weight >= @weight{{end}}
////	FilterWithNameAndWeight(name string, weight int) ([]gen.T, error)
////}
//
//func main() {
//	g := gen.NewGenerator(gen.Config{
//		OutPath: "../query",
//		Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
//	})
//
//	db, _ := gorm.Open(mysql.Open("root:Warp!CV@2022#@(172.16.251.83:31398)/applet_backend?charset=utf8mb4&parseTime=True&loc=Local"))
//	g.UseDB(db)
//
//	// Generate basic type-safe DAO API for struct `models.Stuff` following conventions
//	g.ApplyBasic(models.AppletChain{}, models.AppletLabel{}, models.DynamicWidget{})
//
//	//// Generate Type Safe API with Dynamic SQL defined on Querier interface for `model.User` and `model.Company`
//	//g.ApplyInterface(func(Querier) {}, dao.AppletChain{}, dao.AppletLabel{})
//
//	// Generate the code
//	g.Execute()
//}
