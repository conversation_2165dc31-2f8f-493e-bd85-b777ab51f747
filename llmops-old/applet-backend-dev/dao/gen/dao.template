package dao

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

type #{ModelName}DAO struct {
	db *gorm.DB
}

func New#{ModelName}DAO() *#{ModelName}DAO {
	return &#{ModelName}DAO{db: MustInitDB()}
}

func Get#{ModelName}DAO() *#{ModelName}DAO {
	return New#{ModelName}DAO()
}

// Upsert#{ModelName} 创建或更新
func (r *#{ModelName}DAO) Upsert#{ModelName}(ctx context.Context, entity *models.#{ModelName}) error {
	return db.Clauses(clause.OnConflict{
		UpdateAll: true, // 更新所有字段
	}).Create(entity).Error
}

// Get#{ModelName} 根据id查询
func (r *#{ModelName}DAO) Get#{ModelName}(ctx context.Context, entityId string) (*models.#{ModelName}, error) {
	res := new(models.#{ModelName})
	if err := r.db.Where(&models.#{ModelName}{ID: entityId}).Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// List#{ModelName} 根据entity查询
func (r *#{ModelName}DAO) List#{ModelName}(ctx context.Context, entity *models.#{ModelName}) ([]*models.#{ModelName}, error) {
	res := make([]*models.#{ModelName}, 0)
	if err := r.db.Where(entity).Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (r *#{ModelName}DAO) ListAll(ctx context.Context) ([]*models.#{ModelName}, error) {
	res := make([]*models.#{ModelName}, 0)
	if err := r.db.Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (r *#{ModelName}DAO) Delete#{ModelName}(ctx context.Context, entityId string) error {
	res := make([]*models.#{ModelName}, 0)
	if err := r.db.Where(&models.#{ModelName}{ID: entityId}).Delete(&res).Error; err != nil {
		return err
	}
	return nil
}
