package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"testing"
)

const (
	modelNamePlaceholder = "#{ModelName}"
	templateFileName     = "dao.template"
)

func TestGenDAO(t *testing.T) {
	// pkg-models包下 结构体定义
	modelName := "ChainTemplate"
	// dao包下 dao文件名称
	DAOFileName := "../chain_template.go"
	inputFile, _ := os.Open(templateFileName)
	outputFile, _ := os.Create(DAOFileName)
	scanner := bufio.NewScanner(inputFile)
	writer := bufio.NewWriter(outputFile)
	// 遍历每一行并进行替换
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.ReplaceAll(line, modelNamePlaceholder, modelName)
		_, _ = writer.WriteString(line + "\n")
	}
	writer.Flush()
	fmt.Println("Replacement complete. Generated output.go.")
}
