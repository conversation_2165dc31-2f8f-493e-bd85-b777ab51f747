package dao

import (
	"context"
	"github.com/influxdata/kapacitor/uuid"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/dao/query"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type GuardrailsDAO struct {
	BaseDAO
}

func (g GuardrailsDAO) GetByProjectId(ctx context.Context, pid string) (*generated.SafetyConfig, error) {
	safetyConfigQuery := g.getQueryOrDefault().SafetyConfig
	return safetyConfigQuery.WithContext(ctx).Where(query.SafetyConfig.ProjectID.Eq(pid)).First()
}

func (g GuardrailsDAO) CreateSafetyConfig(ctx context.Context, config *generated.SafetyConfig) error {
	safetyConfigQuery := g.getQueryOrDefault().SafetyConfig
	config.ID = uuid.New().String()
	err := safetyConfigQuery.WithContext(ctx).Create(config)
	return err
}

func (g GuardrailsDAO) UpdateSafetyConfig(ctx context.Context, config *generated.SafetyConfig) error {
	safetyConfigQuery := g.getQueryOrDefault().SafetyConfig
	_, err := safetyConfigQuery.WithContext(ctx).Where(safetyConfigQuery.ProjectID.Eq(config.ProjectID)).Updates(config)
	return err
}

func (g GuardrailsDAO) GetByID(ctx context.Context, id string) (*generated.SafetyConfig, error) {
	safetyConfigQuery := g.getQueryOrDefault().SafetyConfig
	return safetyConfigQuery.WithContext(ctx).Where(query.SafetyConfig.ID.Eq(id)).First()
}

func (g GuardrailsDAO) GetAllSafetyConfig(ctx context.Context) ([]*generated.SafetyConfig, error) {
	safetyConfigQuery := g.getQueryOrDefault().SafetyConfig
	return safetyConfigQuery.WithContext(ctx).Find()
}

// UpsertSafetyConfig 如果是更新,config需要全量数据,零值也会更新
func (g GuardrailsDAO) UpsertSafetyConfig(ctx context.Context, config *generated.SafetyConfig) error {
	if config.ID == "" {
		return stderr.Errorf("the id is empty str")
	}
	query := g.getQueryOrDefault().SafetyConfig
	return query.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Create(config)
}
