// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAppletLabel(db *gorm.DB, opts ...gen.DOOption) appletLabel {
	_appletLabel := appletLabel{}

	_appletLabel.appletLabelDo.UseDB(db, opts...)
	_appletLabel.appletLabelDo.UseModel(&generated.AppletLabel{})

	tableName := _appletLabel.appletLabelDo.TableName()
	_appletLabel.ALL = field.NewAsterisk(tableName)
	_appletLabel.ID = field.NewString(tableName, "id")
	_appletLabel.GroupName = field.NewString(tableName, "group_name")
	_appletLabel.Name = field.NewString(tableName, "name")
	_appletLabel.Creator = field.NewString(tableName, "creator")
	_appletLabel.ProjectID = field.NewString(tableName, "project_id")
	_appletLabel.CreateTime = field.NewTime(tableName, "create_time")
	_appletLabel.UpdatedTime = field.NewTime(tableName, "updated_time")

	_appletLabel.fillFieldMap()

	return _appletLabel
}

type appletLabel struct {
	appletLabelDo

	ALL         field.Asterisk
	ID          field.String // 应用链ID
	GroupName   field.String // 标签组名字
	Name        field.String // 标签名字
	Creator     field.String // 创建人
	ProjectID   field.String // 项目ID
	CreateTime  field.Time   // 创建时间
	UpdatedTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a appletLabel) Table(newTableName string) *appletLabel {
	a.appletLabelDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appletLabel) As(alias string) *appletLabel {
	a.appletLabelDo.DO = *(a.appletLabelDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appletLabel) updateTableName(table string) *appletLabel {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.GroupName = field.NewString(table, "group_name")
	a.Name = field.NewString(table, "name")
	a.Creator = field.NewString(table, "creator")
	a.ProjectID = field.NewString(table, "project_id")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")

	a.fillFieldMap()

	return a
}

func (a *appletLabel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appletLabel) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 7)
	a.fieldMap["id"] = a.ID
	a.fieldMap["group_name"] = a.GroupName
	a.fieldMap["name"] = a.Name
	a.fieldMap["creator"] = a.Creator
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["updated_time"] = a.UpdatedTime
}

func (a appletLabel) clone(db *gorm.DB) appletLabel {
	a.appletLabelDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appletLabel) replaceDB(db *gorm.DB) appletLabel {
	a.appletLabelDo.ReplaceDB(db)
	return a
}

type appletLabelDo struct{ gen.DO }

type IAppletLabelDo interface {
	gen.SubQuery
	Debug() IAppletLabelDo
	WithContext(ctx context.Context) IAppletLabelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppletLabelDo
	WriteDB() IAppletLabelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppletLabelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppletLabelDo
	Not(conds ...gen.Condition) IAppletLabelDo
	Or(conds ...gen.Condition) IAppletLabelDo
	Select(conds ...field.Expr) IAppletLabelDo
	Where(conds ...gen.Condition) IAppletLabelDo
	Order(conds ...field.Expr) IAppletLabelDo
	Distinct(cols ...field.Expr) IAppletLabelDo
	Omit(cols ...field.Expr) IAppletLabelDo
	Join(table schema.Tabler, on ...field.Expr) IAppletLabelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppletLabelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppletLabelDo
	Group(cols ...field.Expr) IAppletLabelDo
	Having(conds ...gen.Condition) IAppletLabelDo
	Limit(limit int) IAppletLabelDo
	Offset(offset int) IAppletLabelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletLabelDo
	Unscoped() IAppletLabelDo
	Create(values ...*generated.AppletLabel) error
	CreateInBatches(values []*generated.AppletLabel, batchSize int) error
	Save(values ...*generated.AppletLabel) error
	First() (*generated.AppletLabel, error)
	Take() (*generated.AppletLabel, error)
	Last() (*generated.AppletLabel, error)
	Find() ([]*generated.AppletLabel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletLabel, err error)
	FindInBatches(result *[]*generated.AppletLabel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.AppletLabel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppletLabelDo
	Assign(attrs ...field.AssignExpr) IAppletLabelDo
	Joins(fields ...field.RelationField) IAppletLabelDo
	Preload(fields ...field.RelationField) IAppletLabelDo
	FirstOrInit() (*generated.AppletLabel, error)
	FirstOrCreate() (*generated.AppletLabel, error)
	FindByPage(offset int, limit int) (result []*generated.AppletLabel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppletLabelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appletLabelDo) Debug() IAppletLabelDo {
	return a.withDO(a.DO.Debug())
}

func (a appletLabelDo) WithContext(ctx context.Context) IAppletLabelDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appletLabelDo) ReadDB() IAppletLabelDo {
	return a.Clauses(dbresolver.Read)
}

func (a appletLabelDo) WriteDB() IAppletLabelDo {
	return a.Clauses(dbresolver.Write)
}

func (a appletLabelDo) Session(config *gorm.Session) IAppletLabelDo {
	return a.withDO(a.DO.Session(config))
}

func (a appletLabelDo) Clauses(conds ...clause.Expression) IAppletLabelDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appletLabelDo) Returning(value interface{}, columns ...string) IAppletLabelDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appletLabelDo) Not(conds ...gen.Condition) IAppletLabelDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appletLabelDo) Or(conds ...gen.Condition) IAppletLabelDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appletLabelDo) Select(conds ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appletLabelDo) Where(conds ...gen.Condition) IAppletLabelDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appletLabelDo) Order(conds ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appletLabelDo) Distinct(cols ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appletLabelDo) Omit(cols ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appletLabelDo) Join(table schema.Tabler, on ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appletLabelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appletLabelDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appletLabelDo) Group(cols ...field.Expr) IAppletLabelDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appletLabelDo) Having(conds ...gen.Condition) IAppletLabelDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appletLabelDo) Limit(limit int) IAppletLabelDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appletLabelDo) Offset(offset int) IAppletLabelDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appletLabelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletLabelDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appletLabelDo) Unscoped() IAppletLabelDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appletLabelDo) Create(values ...*generated.AppletLabel) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appletLabelDo) CreateInBatches(values []*generated.AppletLabel, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appletLabelDo) Save(values ...*generated.AppletLabel) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appletLabelDo) First() (*generated.AppletLabel, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletLabel), nil
	}
}

func (a appletLabelDo) Take() (*generated.AppletLabel, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletLabel), nil
	}
}

func (a appletLabelDo) Last() (*generated.AppletLabel, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletLabel), nil
	}
}

func (a appletLabelDo) Find() ([]*generated.AppletLabel, error) {
	result, err := a.DO.Find()
	return result.([]*generated.AppletLabel), err
}

func (a appletLabelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletLabel, err error) {
	buf := make([]*generated.AppletLabel, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appletLabelDo) FindInBatches(result *[]*generated.AppletLabel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appletLabelDo) Attrs(attrs ...field.AssignExpr) IAppletLabelDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appletLabelDo) Assign(attrs ...field.AssignExpr) IAppletLabelDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appletLabelDo) Joins(fields ...field.RelationField) IAppletLabelDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appletLabelDo) Preload(fields ...field.RelationField) IAppletLabelDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appletLabelDo) FirstOrInit() (*generated.AppletLabel, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletLabel), nil
	}
}

func (a appletLabelDo) FirstOrCreate() (*generated.AppletLabel, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletLabel), nil
	}
}

func (a appletLabelDo) FindByPage(offset int, limit int) (result []*generated.AppletLabel, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appletLabelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appletLabelDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appletLabelDo) Delete(models ...*generated.AppletLabel) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appletLabelDo) withDO(do gen.Dao) *appletLabelDo {
	a.DO = *do.(*gen.DO)
	return a
}
