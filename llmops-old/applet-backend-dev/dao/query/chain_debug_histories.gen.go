// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newChainDebugHistory(db *gorm.DB, opts ...gen.DOOption) chainDebugHistory {
	_chainDebugHistory := chainDebugHistory{}

	_chainDebugHistory.chainDebugHistoryDo.UseDB(db, opts...)
	_chainDebugHistory.chainDebugHistoryDo.UseModel(&generated.ChainDebugHistory{})

	tableName := _chainDebugHistory.chainDebugHistoryDo.TableName()
	_chainDebugHistory.ALL = field.NewAsterisk(tableName)
	_chainDebugHistory.ID = field.NewString(tableName, "id")
	_chainDebugHistory.ChainID = field.NewString(tableName, "chain_id")
	_chainDebugHistory.State = field.NewInt32(tableName, "state")
	_chainDebugHistory.Creator = field.NewString(tableName, "creator")
	_chainDebugHistory.ProjectID = field.NewString(tableName, "project_id")
	_chainDebugHistory.CreateTime = field.NewTime(tableName, "create_time")
	_chainDebugHistory.UpdatedTime = field.NewTime(tableName, "updated_time")
	_chainDebugHistory.DebugMessage = field.NewString(tableName, "debug_message")
	_chainDebugHistory.DebugName = field.NewString(tableName, "debug_name")
	_chainDebugHistory.ChainSnapshotID = field.NewInt64(tableName, "chain_snapshot_id")
	_chainDebugHistory.ChainSnapshot = field.NewString(tableName, "chain_snapshot")

	_chainDebugHistory.fillFieldMap()

	return _chainDebugHistory
}

type chainDebugHistory struct {
	chainDebugHistoryDo

	ALL             field.Asterisk
	ID              field.String // ID
	ChainID         field.String // 应用链ID
	State           field.Int32  // 调试状态，0未调试，1调试中，2成功，3失败，4取消
	Creator         field.String // 创建人
	ProjectID       field.String // 项目ID
	CreateTime      field.Time   // 创建时间
	UpdatedTime     field.Time   // 更新时间
	DebugMessage    field.String // EventStream 中 type 为'debug'的数据
	DebugName       field.String // 用户输入（textInput或fileName）作为单条调试记录的名称
	ChainSnapshotID field.Int64  // 应用链快照id
	ChainSnapshot   field.String // 应用链快照详情

	fieldMap map[string]field.Expr
}

func (c chainDebugHistory) Table(newTableName string) *chainDebugHistory {
	c.chainDebugHistoryDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chainDebugHistory) As(alias string) *chainDebugHistory {
	c.chainDebugHistoryDo.DO = *(c.chainDebugHistoryDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chainDebugHistory) updateTableName(table string) *chainDebugHistory {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.ChainID = field.NewString(table, "chain_id")
	c.State = field.NewInt32(table, "state")
	c.Creator = field.NewString(table, "creator")
	c.ProjectID = field.NewString(table, "project_id")
	c.CreateTime = field.NewTime(table, "create_time")
	c.UpdatedTime = field.NewTime(table, "updated_time")
	c.DebugMessage = field.NewString(table, "debug_message")
	c.DebugName = field.NewString(table, "debug_name")
	c.ChainSnapshotID = field.NewInt64(table, "chain_snapshot_id")
	c.ChainSnapshot = field.NewString(table, "chain_snapshot")

	c.fillFieldMap()

	return c
}

func (c *chainDebugHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chainDebugHistory) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 11)
	c.fieldMap["id"] = c.ID
	c.fieldMap["chain_id"] = c.ChainID
	c.fieldMap["state"] = c.State
	c.fieldMap["creator"] = c.Creator
	c.fieldMap["project_id"] = c.ProjectID
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["updated_time"] = c.UpdatedTime
	c.fieldMap["debug_message"] = c.DebugMessage
	c.fieldMap["debug_name"] = c.DebugName
	c.fieldMap["chain_snapshot_id"] = c.ChainSnapshotID
	c.fieldMap["chain_snapshot"] = c.ChainSnapshot
}

func (c chainDebugHistory) clone(db *gorm.DB) chainDebugHistory {
	c.chainDebugHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c chainDebugHistory) replaceDB(db *gorm.DB) chainDebugHistory {
	c.chainDebugHistoryDo.ReplaceDB(db)
	return c
}

type chainDebugHistoryDo struct{ gen.DO }

type IChainDebugHistoryDo interface {
	gen.SubQuery
	Debug() IChainDebugHistoryDo
	WithContext(ctx context.Context) IChainDebugHistoryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IChainDebugHistoryDo
	WriteDB() IChainDebugHistoryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IChainDebugHistoryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IChainDebugHistoryDo
	Not(conds ...gen.Condition) IChainDebugHistoryDo
	Or(conds ...gen.Condition) IChainDebugHistoryDo
	Select(conds ...field.Expr) IChainDebugHistoryDo
	Where(conds ...gen.Condition) IChainDebugHistoryDo
	Order(conds ...field.Expr) IChainDebugHistoryDo
	Distinct(cols ...field.Expr) IChainDebugHistoryDo
	Omit(cols ...field.Expr) IChainDebugHistoryDo
	Join(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo
	Group(cols ...field.Expr) IChainDebugHistoryDo
	Having(conds ...gen.Condition) IChainDebugHistoryDo
	Limit(limit int) IChainDebugHistoryDo
	Offset(offset int) IChainDebugHistoryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IChainDebugHistoryDo
	Unscoped() IChainDebugHistoryDo
	Create(values ...*generated.ChainDebugHistory) error
	CreateInBatches(values []*generated.ChainDebugHistory, batchSize int) error
	Save(values ...*generated.ChainDebugHistory) error
	First() (*generated.ChainDebugHistory, error)
	Take() (*generated.ChainDebugHistory, error)
	Last() (*generated.ChainDebugHistory, error)
	Find() ([]*generated.ChainDebugHistory, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainDebugHistory, err error)
	FindInBatches(result *[]*generated.ChainDebugHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.ChainDebugHistory) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IChainDebugHistoryDo
	Assign(attrs ...field.AssignExpr) IChainDebugHistoryDo
	Joins(fields ...field.RelationField) IChainDebugHistoryDo
	Preload(fields ...field.RelationField) IChainDebugHistoryDo
	FirstOrInit() (*generated.ChainDebugHistory, error)
	FirstOrCreate() (*generated.ChainDebugHistory, error)
	FindByPage(offset int, limit int) (result []*generated.ChainDebugHistory, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IChainDebugHistoryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c chainDebugHistoryDo) Debug() IChainDebugHistoryDo {
	return c.withDO(c.DO.Debug())
}

func (c chainDebugHistoryDo) WithContext(ctx context.Context) IChainDebugHistoryDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chainDebugHistoryDo) ReadDB() IChainDebugHistoryDo {
	return c.Clauses(dbresolver.Read)
}

func (c chainDebugHistoryDo) WriteDB() IChainDebugHistoryDo {
	return c.Clauses(dbresolver.Write)
}

func (c chainDebugHistoryDo) Session(config *gorm.Session) IChainDebugHistoryDo {
	return c.withDO(c.DO.Session(config))
}

func (c chainDebugHistoryDo) Clauses(conds ...clause.Expression) IChainDebugHistoryDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chainDebugHistoryDo) Returning(value interface{}, columns ...string) IChainDebugHistoryDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chainDebugHistoryDo) Not(conds ...gen.Condition) IChainDebugHistoryDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chainDebugHistoryDo) Or(conds ...gen.Condition) IChainDebugHistoryDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chainDebugHistoryDo) Select(conds ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chainDebugHistoryDo) Where(conds ...gen.Condition) IChainDebugHistoryDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chainDebugHistoryDo) Order(conds ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chainDebugHistoryDo) Distinct(cols ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chainDebugHistoryDo) Omit(cols ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chainDebugHistoryDo) Join(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chainDebugHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chainDebugHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chainDebugHistoryDo) Group(cols ...field.Expr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chainDebugHistoryDo) Having(conds ...gen.Condition) IChainDebugHistoryDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chainDebugHistoryDo) Limit(limit int) IChainDebugHistoryDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chainDebugHistoryDo) Offset(offset int) IChainDebugHistoryDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chainDebugHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IChainDebugHistoryDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chainDebugHistoryDo) Unscoped() IChainDebugHistoryDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chainDebugHistoryDo) Create(values ...*generated.ChainDebugHistory) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chainDebugHistoryDo) CreateInBatches(values []*generated.ChainDebugHistory, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chainDebugHistoryDo) Save(values ...*generated.ChainDebugHistory) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chainDebugHistoryDo) First() (*generated.ChainDebugHistory, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainDebugHistory), nil
	}
}

func (c chainDebugHistoryDo) Take() (*generated.ChainDebugHistory, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainDebugHistory), nil
	}
}

func (c chainDebugHistoryDo) Last() (*generated.ChainDebugHistory, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainDebugHistory), nil
	}
}

func (c chainDebugHistoryDo) Find() ([]*generated.ChainDebugHistory, error) {
	result, err := c.DO.Find()
	return result.([]*generated.ChainDebugHistory), err
}

func (c chainDebugHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.ChainDebugHistory, err error) {
	buf := make([]*generated.ChainDebugHistory, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chainDebugHistoryDo) FindInBatches(result *[]*generated.ChainDebugHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chainDebugHistoryDo) Attrs(attrs ...field.AssignExpr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chainDebugHistoryDo) Assign(attrs ...field.AssignExpr) IChainDebugHistoryDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chainDebugHistoryDo) Joins(fields ...field.RelationField) IChainDebugHistoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chainDebugHistoryDo) Preload(fields ...field.RelationField) IChainDebugHistoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chainDebugHistoryDo) FirstOrInit() (*generated.ChainDebugHistory, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainDebugHistory), nil
	}
}

func (c chainDebugHistoryDo) FirstOrCreate() (*generated.ChainDebugHistory, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.ChainDebugHistory), nil
	}
}

func (c chainDebugHistoryDo) FindByPage(offset int, limit int) (result []*generated.ChainDebugHistory, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chainDebugHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chainDebugHistoryDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chainDebugHistoryDo) Delete(models ...*generated.ChainDebugHistory) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chainDebugHistoryDo) withDO(do gen.Dao) *chainDebugHistoryDo {
	c.DO = *do.(*gen.DO)
	return c
}
