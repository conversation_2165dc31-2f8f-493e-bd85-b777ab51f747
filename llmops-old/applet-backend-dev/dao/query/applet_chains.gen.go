// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAppletChain(db *gorm.DB, opts ...gen.DOOption) appletChain {
	_appletChain := appletChain{}

	_appletChain.appletChainDo.UseDB(db, opts...)
	_appletChain.appletChainDo.UseModel(&generated.AppletChain{})

	tableName := _appletChain.appletChainDo.TableName()
	_appletChain.ALL = field.NewAsterisk(tableName)
	_appletChain.ID = field.NewString(tableName, "id")
	_appletChain.Name = field.NewString(tableName, "name")
	_appletChain.Creator = field.NewString(tableName, "creator")
	_appletChain.LastDebugState = field.NewInt32(tableName, "last_debug_state")
	_appletChain.Label = field.NewString(tableName, "label")
	_appletChain.Chain = field.NewString(tableName, "chain")
	_appletChain.Desc = field.NewString(tableName, "desc")
	_appletChain.DebugInfo = field.NewString(tableName, "debug_info")
	_appletChain.ContainsSubChain = field.NewInt32(tableName, "contains_sub_chain")
	_appletChain.ProjectID = field.NewString(tableName, "project_id")
	_appletChain.CreateTime = field.NewTime(tableName, "create_time")
	_appletChain.UpdatedTime = field.NewTime(tableName, "updated_time")
	_appletChain.ChainUpdatedTime = field.NewTime(tableName, "chain_updated_time")
	_appletChain.AssetType = field.NewInt32(tableName, "asset_type")
	_appletChain.UsageType = field.NewString(tableName, "usage_type")
	_appletChain.CreatedType = field.NewString(tableName, "created_type")
	_appletChain.SourceInfo = field.NewString(tableName, "source_info")
	_appletChain.MetricsInfo = field.NewString(tableName, "metrics_info")
	_appletChain.AssistantInfo = field.NewString(tableName, "assistant_info")
	_appletChain.ExamplesInfo = field.NewString(tableName, "examples_info")
	_appletChain.SpaceInfo = field.NewString(tableName, "space_info")

	_appletChain.fillFieldMap()

	return _appletChain
}

type appletChain struct {
	appletChainDo

	ALL              field.Asterisk
	ID               field.String // 应用链ID
	Name             field.String // 应用链名字
	Creator          field.String // 创建人
	LastDebugState   field.Int32  // 最新一次调试状态，0未调试，1调试中，2成功，3失败
	Label            field.String // 标签，map结构
	Chain            field.String // 算子&依赖关系编排
	Desc             field.String // 应用链描述
	DebugInfo        field.String // 调试信息
	ContainsSubChain field.Int32  // 是否包含子链，2不包含，1包含
	ProjectID        field.String // 项目ID
	CreateTime       field.Time   // 创建时间
	UpdatedTime      field.Time   // 数据库记录更新时间
	ChainUpdatedTime field.Time   // 应用链更新时间
	AssetType        field.Int32  // 资产类型[0共享资产，1内嵌资产]
	UsageType        field.String // 用途，LLM/Common
	CreatedType      field.String // 创建类型
	SourceInfo       field.String // 来源信息
	MetricsInfo      field.String // 打点信息，访问次数等
	AssistantInfo    field.String // 智能体相关信息
	ExamplesInfo     field.String // 图片地址、开场白、引导示例
	SpaceInfo        field.String // 机构空间所属行业及首页精选等相关信息

	fieldMap map[string]field.Expr
}

func (a appletChain) Table(newTableName string) *appletChain {
	a.appletChainDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appletChain) As(alias string) *appletChain {
	a.appletChainDo.DO = *(a.appletChainDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appletChain) updateTableName(table string) *appletChain {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.Name = field.NewString(table, "name")
	a.Creator = field.NewString(table, "creator")
	a.LastDebugState = field.NewInt32(table, "last_debug_state")
	a.Label = field.NewString(table, "label")
	a.Chain = field.NewString(table, "chain")
	a.Desc = field.NewString(table, "desc")
	a.DebugInfo = field.NewString(table, "debug_info")
	a.ContainsSubChain = field.NewInt32(table, "contains_sub_chain")
	a.ProjectID = field.NewString(table, "project_id")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")
	a.ChainUpdatedTime = field.NewTime(table, "chain_updated_time")
	a.AssetType = field.NewInt32(table, "asset_type")
	a.UsageType = field.NewString(table, "usage_type")
	a.CreatedType = field.NewString(table, "created_type")
	a.SourceInfo = field.NewString(table, "source_info")
	a.MetricsInfo = field.NewString(table, "metrics_info")
	a.AssistantInfo = field.NewString(table, "assistant_info")
	a.ExamplesInfo = field.NewString(table, "examples_info")
	a.SpaceInfo = field.NewString(table, "space_info")

	a.fillFieldMap()

	return a
}

func (a *appletChain) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appletChain) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 21)
	a.fieldMap["id"] = a.ID
	a.fieldMap["name"] = a.Name
	a.fieldMap["creator"] = a.Creator
	a.fieldMap["last_debug_state"] = a.LastDebugState
	a.fieldMap["label"] = a.Label
	a.fieldMap["chain"] = a.Chain
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["debug_info"] = a.DebugInfo
	a.fieldMap["contains_sub_chain"] = a.ContainsSubChain
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["updated_time"] = a.UpdatedTime
	a.fieldMap["chain_updated_time"] = a.ChainUpdatedTime
	a.fieldMap["asset_type"] = a.AssetType
	a.fieldMap["usage_type"] = a.UsageType
	a.fieldMap["created_type"] = a.CreatedType
	a.fieldMap["source_info"] = a.SourceInfo
	a.fieldMap["metrics_info"] = a.MetricsInfo
	a.fieldMap["assistant_info"] = a.AssistantInfo
	a.fieldMap["examples_info"] = a.ExamplesInfo
	a.fieldMap["space_info"] = a.SpaceInfo
}

func (a appletChain) clone(db *gorm.DB) appletChain {
	a.appletChainDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appletChain) replaceDB(db *gorm.DB) appletChain {
	a.appletChainDo.ReplaceDB(db)
	return a
}

type appletChainDo struct{ gen.DO }

type IAppletChainDo interface {
	gen.SubQuery
	Debug() IAppletChainDo
	WithContext(ctx context.Context) IAppletChainDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppletChainDo
	WriteDB() IAppletChainDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppletChainDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppletChainDo
	Not(conds ...gen.Condition) IAppletChainDo
	Or(conds ...gen.Condition) IAppletChainDo
	Select(conds ...field.Expr) IAppletChainDo
	Where(conds ...gen.Condition) IAppletChainDo
	Order(conds ...field.Expr) IAppletChainDo
	Distinct(cols ...field.Expr) IAppletChainDo
	Omit(cols ...field.Expr) IAppletChainDo
	Join(table schema.Tabler, on ...field.Expr) IAppletChainDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppletChainDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppletChainDo
	Group(cols ...field.Expr) IAppletChainDo
	Having(conds ...gen.Condition) IAppletChainDo
	Limit(limit int) IAppletChainDo
	Offset(offset int) IAppletChainDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletChainDo
	Unscoped() IAppletChainDo
	Create(values ...*generated.AppletChain) error
	CreateInBatches(values []*generated.AppletChain, batchSize int) error
	Save(values ...*generated.AppletChain) error
	First() (*generated.AppletChain, error)
	Take() (*generated.AppletChain, error)
	Last() (*generated.AppletChain, error)
	Find() ([]*generated.AppletChain, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletChain, err error)
	FindInBatches(result *[]*generated.AppletChain, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.AppletChain) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppletChainDo
	Assign(attrs ...field.AssignExpr) IAppletChainDo
	Joins(fields ...field.RelationField) IAppletChainDo
	Preload(fields ...field.RelationField) IAppletChainDo
	FirstOrInit() (*generated.AppletChain, error)
	FirstOrCreate() (*generated.AppletChain, error)
	FindByPage(offset int, limit int) (result []*generated.AppletChain, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppletChainDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appletChainDo) Debug() IAppletChainDo {
	return a.withDO(a.DO.Debug())
}

func (a appletChainDo) WithContext(ctx context.Context) IAppletChainDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appletChainDo) ReadDB() IAppletChainDo {
	return a.Clauses(dbresolver.Read)
}

func (a appletChainDo) WriteDB() IAppletChainDo {
	return a.Clauses(dbresolver.Write)
}

func (a appletChainDo) Session(config *gorm.Session) IAppletChainDo {
	return a.withDO(a.DO.Session(config))
}

func (a appletChainDo) Clauses(conds ...clause.Expression) IAppletChainDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appletChainDo) Returning(value interface{}, columns ...string) IAppletChainDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appletChainDo) Not(conds ...gen.Condition) IAppletChainDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appletChainDo) Or(conds ...gen.Condition) IAppletChainDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appletChainDo) Select(conds ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appletChainDo) Where(conds ...gen.Condition) IAppletChainDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appletChainDo) Order(conds ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appletChainDo) Distinct(cols ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appletChainDo) Omit(cols ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appletChainDo) Join(table schema.Tabler, on ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appletChainDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appletChainDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appletChainDo) Group(cols ...field.Expr) IAppletChainDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appletChainDo) Having(conds ...gen.Condition) IAppletChainDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appletChainDo) Limit(limit int) IAppletChainDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appletChainDo) Offset(offset int) IAppletChainDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appletChainDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppletChainDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appletChainDo) Unscoped() IAppletChainDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appletChainDo) Create(values ...*generated.AppletChain) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appletChainDo) CreateInBatches(values []*generated.AppletChain, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appletChainDo) Save(values ...*generated.AppletChain) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appletChainDo) First() (*generated.AppletChain, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletChain), nil
	}
}

func (a appletChainDo) Take() (*generated.AppletChain, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletChain), nil
	}
}

func (a appletChainDo) Last() (*generated.AppletChain, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletChain), nil
	}
}

func (a appletChainDo) Find() ([]*generated.AppletChain, error) {
	result, err := a.DO.Find()
	return result.([]*generated.AppletChain), err
}

func (a appletChainDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.AppletChain, err error) {
	buf := make([]*generated.AppletChain, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appletChainDo) FindInBatches(result *[]*generated.AppletChain, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appletChainDo) Attrs(attrs ...field.AssignExpr) IAppletChainDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appletChainDo) Assign(attrs ...field.AssignExpr) IAppletChainDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appletChainDo) Joins(fields ...field.RelationField) IAppletChainDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appletChainDo) Preload(fields ...field.RelationField) IAppletChainDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appletChainDo) FirstOrInit() (*generated.AppletChain, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletChain), nil
	}
}

func (a appletChainDo) FirstOrCreate() (*generated.AppletChain, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.AppletChain), nil
	}
}

func (a appletChainDo) FindByPage(offset int, limit int) (result []*generated.AppletChain, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appletChainDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appletChainDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appletChainDo) Delete(models ...*generated.AppletChain) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appletChainDo) withDO(do gen.Dao) *appletChainDo {
	a.DO = *do.(*gen.DO)
	return a
}
