// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newDialogMessage(db *gorm.DB, opts ...gen.DOOption) dialogMessage {
	_dialogMessage := dialogMessage{}

	_dialogMessage.dialogMessageDo.UseDB(db, opts...)
	_dialogMessage.dialogMessageDo.UseModel(&generated.DialogMessage{})

	tableName := _dialogMessage.dialogMessageDo.TableName()
	_dialogMessage.ALL = field.NewAsterisk(tableName)
	_dialogMessage.ID = field.NewInt64(tableName, "id")
	_dialogMessage.ChatID = field.NewString(tableName, "chat_id")
	_dialogMessage.Content = field.NewString(tableName, "content")
	_dialogMessage.Role = field.NewString(tableName, "role")
	_dialogMessage.CreateTime = field.NewTime(tableName, "create_time")
	_dialogMessage.UpdatedTime = field.NewTime(tableName, "updated_time")

	_dialogMessage.fillFieldMap()

	return _dialogMessage
}

type dialogMessage struct {
	dialogMessageDo

	ALL         field.Asterisk
	ID          field.Int64
	ChatID      field.String
	Content     field.String
	Role        field.String // 记录的类型:(question, answer)
	CreateTime  field.Time   // 创建时间
	UpdatedTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (d dialogMessage) Table(newTableName string) *dialogMessage {
	d.dialogMessageDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dialogMessage) As(alias string) *dialogMessage {
	d.dialogMessageDo.DO = *(d.dialogMessageDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dialogMessage) updateTableName(table string) *dialogMessage {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.ChatID = field.NewString(table, "chat_id")
	d.Content = field.NewString(table, "content")
	d.Role = field.NewString(table, "role")
	d.CreateTime = field.NewTime(table, "create_time")
	d.UpdatedTime = field.NewTime(table, "updated_time")

	d.fillFieldMap()

	return d
}

func (d *dialogMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dialogMessage) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 6)
	d.fieldMap["id"] = d.ID
	d.fieldMap["chat_id"] = d.ChatID
	d.fieldMap["content"] = d.Content
	d.fieldMap["role"] = d.Role
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["updated_time"] = d.UpdatedTime
}

func (d dialogMessage) clone(db *gorm.DB) dialogMessage {
	d.dialogMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dialogMessage) replaceDB(db *gorm.DB) dialogMessage {
	d.dialogMessageDo.ReplaceDB(db)
	return d
}

type dialogMessageDo struct{ gen.DO }

type IDialogMessageDo interface {
	gen.SubQuery
	Debug() IDialogMessageDo
	WithContext(ctx context.Context) IDialogMessageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDialogMessageDo
	WriteDB() IDialogMessageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDialogMessageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDialogMessageDo
	Not(conds ...gen.Condition) IDialogMessageDo
	Or(conds ...gen.Condition) IDialogMessageDo
	Select(conds ...field.Expr) IDialogMessageDo
	Where(conds ...gen.Condition) IDialogMessageDo
	Order(conds ...field.Expr) IDialogMessageDo
	Distinct(cols ...field.Expr) IDialogMessageDo
	Omit(cols ...field.Expr) IDialogMessageDo
	Join(table schema.Tabler, on ...field.Expr) IDialogMessageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDialogMessageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDialogMessageDo
	Group(cols ...field.Expr) IDialogMessageDo
	Having(conds ...gen.Condition) IDialogMessageDo
	Limit(limit int) IDialogMessageDo
	Offset(offset int) IDialogMessageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogMessageDo
	Unscoped() IDialogMessageDo
	Create(values ...*generated.DialogMessage) error
	CreateInBatches(values []*generated.DialogMessage, batchSize int) error
	Save(values ...*generated.DialogMessage) error
	First() (*generated.DialogMessage, error)
	Take() (*generated.DialogMessage, error)
	Last() (*generated.DialogMessage, error)
	Find() ([]*generated.DialogMessage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.DialogMessage, err error)
	FindInBatches(result *[]*generated.DialogMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.DialogMessage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDialogMessageDo
	Assign(attrs ...field.AssignExpr) IDialogMessageDo
	Joins(fields ...field.RelationField) IDialogMessageDo
	Preload(fields ...field.RelationField) IDialogMessageDo
	FirstOrInit() (*generated.DialogMessage, error)
	FirstOrCreate() (*generated.DialogMessage, error)
	FindByPage(offset int, limit int) (result []*generated.DialogMessage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDialogMessageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d dialogMessageDo) Debug() IDialogMessageDo {
	return d.withDO(d.DO.Debug())
}

func (d dialogMessageDo) WithContext(ctx context.Context) IDialogMessageDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d dialogMessageDo) ReadDB() IDialogMessageDo {
	return d.Clauses(dbresolver.Read)
}

func (d dialogMessageDo) WriteDB() IDialogMessageDo {
	return d.Clauses(dbresolver.Write)
}

func (d dialogMessageDo) Session(config *gorm.Session) IDialogMessageDo {
	return d.withDO(d.DO.Session(config))
}

func (d dialogMessageDo) Clauses(conds ...clause.Expression) IDialogMessageDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d dialogMessageDo) Returning(value interface{}, columns ...string) IDialogMessageDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d dialogMessageDo) Not(conds ...gen.Condition) IDialogMessageDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d dialogMessageDo) Or(conds ...gen.Condition) IDialogMessageDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d dialogMessageDo) Select(conds ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d dialogMessageDo) Where(conds ...gen.Condition) IDialogMessageDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d dialogMessageDo) Order(conds ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d dialogMessageDo) Distinct(cols ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d dialogMessageDo) Omit(cols ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d dialogMessageDo) Join(table schema.Tabler, on ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d dialogMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d dialogMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d dialogMessageDo) Group(cols ...field.Expr) IDialogMessageDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d dialogMessageDo) Having(conds ...gen.Condition) IDialogMessageDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d dialogMessageDo) Limit(limit int) IDialogMessageDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d dialogMessageDo) Offset(offset int) IDialogMessageDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d dialogMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDialogMessageDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d dialogMessageDo) Unscoped() IDialogMessageDo {
	return d.withDO(d.DO.Unscoped())
}

func (d dialogMessageDo) Create(values ...*generated.DialogMessage) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d dialogMessageDo) CreateInBatches(values []*generated.DialogMessage, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d dialogMessageDo) Save(values ...*generated.DialogMessage) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d dialogMessageDo) First() (*generated.DialogMessage, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogMessage), nil
	}
}

func (d dialogMessageDo) Take() (*generated.DialogMessage, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogMessage), nil
	}
}

func (d dialogMessageDo) Last() (*generated.DialogMessage, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogMessage), nil
	}
}

func (d dialogMessageDo) Find() ([]*generated.DialogMessage, error) {
	result, err := d.DO.Find()
	return result.([]*generated.DialogMessage), err
}

func (d dialogMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.DialogMessage, err error) {
	buf := make([]*generated.DialogMessage, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d dialogMessageDo) FindInBatches(result *[]*generated.DialogMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d dialogMessageDo) Attrs(attrs ...field.AssignExpr) IDialogMessageDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d dialogMessageDo) Assign(attrs ...field.AssignExpr) IDialogMessageDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d dialogMessageDo) Joins(fields ...field.RelationField) IDialogMessageDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d dialogMessageDo) Preload(fields ...field.RelationField) IDialogMessageDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d dialogMessageDo) FirstOrInit() (*generated.DialogMessage, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogMessage), nil
	}
}

func (d dialogMessageDo) FirstOrCreate() (*generated.DialogMessage, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.DialogMessage), nil
	}
}

func (d dialogMessageDo) FindByPage(offset int, limit int) (result []*generated.DialogMessage, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d dialogMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d dialogMessageDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d dialogMessageDo) Delete(models ...*generated.DialogMessage) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *dialogMessageDo) withDO(do gen.Dao) *dialogMessageDo {
	d.DO = *do.(*gen.DO)
	return d
}
