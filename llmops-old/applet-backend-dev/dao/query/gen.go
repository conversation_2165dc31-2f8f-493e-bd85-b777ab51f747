// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                     = new(Query)
	APITool               *aPITool
	APIToolCollection     *aPIToolCollection
	APIToolCollectionDemo *aPIToolCollectionDemo
	AppletChain           *appletChain
	AppletExperiment      *appletExperiment
	AppletLabel           *appletLabel
	ChainDebugHistory     *chainDebugHistory
	ChainSnapshot         *chainSnapshot
	Chunk                 *chunk
	CustomWidget          *customWidget
	Dialog                *dialog
	DialogApp             *dialogApp
	DialogMessage         *dialogMessage
	DocElement            *docElement
	DocTask               *docTask
	Document              *document
	KnowledgeBase         *knowledgeBase
	LlmBasicConfig        *llmBasicConfig
	SafetyConfig          *safetyConfig
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	APITool = &Q.APITool
	APIToolCollection = &Q.APIToolCollection
	APIToolCollectionDemo = &Q.APIToolCollectionDemo
	AppletChain = &Q.AppletChain
	AppletExperiment = &Q.AppletExperiment
	AppletLabel = &Q.AppletLabel
	ChainDebugHistory = &Q.ChainDebugHistory
	ChainSnapshot = &Q.ChainSnapshot
	Chunk = &Q.Chunk
	CustomWidget = &Q.CustomWidget
	Dialog = &Q.Dialog
	DialogApp = &Q.DialogApp
	DialogMessage = &Q.DialogMessage
	DocElement = &Q.DocElement
	DocTask = &Q.DocTask
	Document = &Q.Document
	KnowledgeBase = &Q.KnowledgeBase
	LlmBasicConfig = &Q.LlmBasicConfig
	SafetyConfig = &Q.SafetyConfig
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		APITool:               newAPITool(db, opts...),
		APIToolCollection:     newAPIToolCollection(db, opts...),
		APIToolCollectionDemo: newAPIToolCollectionDemo(db, opts...),
		AppletChain:           newAppletChain(db, opts...),
		AppletExperiment:      newAppletExperiment(db, opts...),
		AppletLabel:           newAppletLabel(db, opts...),
		ChainDebugHistory:     newChainDebugHistory(db, opts...),
		ChainSnapshot:         newChainSnapshot(db, opts...),
		Chunk:                 newChunk(db, opts...),
		CustomWidget:          newCustomWidget(db, opts...),
		Dialog:                newDialog(db, opts...),
		DialogApp:             newDialogApp(db, opts...),
		DialogMessage:         newDialogMessage(db, opts...),
		DocElement:            newDocElement(db, opts...),
		DocTask:               newDocTask(db, opts...),
		Document:              newDocument(db, opts...),
		KnowledgeBase:         newKnowledgeBase(db, opts...),
		LlmBasicConfig:        newLlmBasicConfig(db, opts...),
		SafetyConfig:          newSafetyConfig(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	APITool               aPITool
	APIToolCollection     aPIToolCollection
	APIToolCollectionDemo aPIToolCollectionDemo
	AppletChain           appletChain
	AppletExperiment      appletExperiment
	AppletLabel           appletLabel
	ChainDebugHistory     chainDebugHistory
	ChainSnapshot         chainSnapshot
	Chunk                 chunk
	CustomWidget          customWidget
	Dialog                dialog
	DialogApp             dialogApp
	DialogMessage         dialogMessage
	DocElement            docElement
	DocTask               docTask
	Document              document
	KnowledgeBase         knowledgeBase
	LlmBasicConfig        llmBasicConfig
	SafetyConfig          safetyConfig
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		APITool:               q.APITool.clone(db),
		APIToolCollection:     q.APIToolCollection.clone(db),
		APIToolCollectionDemo: q.APIToolCollectionDemo.clone(db),
		AppletChain:           q.AppletChain.clone(db),
		AppletExperiment:      q.AppletExperiment.clone(db),
		AppletLabel:           q.AppletLabel.clone(db),
		ChainDebugHistory:     q.ChainDebugHistory.clone(db),
		ChainSnapshot:         q.ChainSnapshot.clone(db),
		Chunk:                 q.Chunk.clone(db),
		CustomWidget:          q.CustomWidget.clone(db),
		Dialog:                q.Dialog.clone(db),
		DialogApp:             q.DialogApp.clone(db),
		DialogMessage:         q.DialogMessage.clone(db),
		DocElement:            q.DocElement.clone(db),
		DocTask:               q.DocTask.clone(db),
		Document:              q.Document.clone(db),
		KnowledgeBase:         q.KnowledgeBase.clone(db),
		LlmBasicConfig:        q.LlmBasicConfig.clone(db),
		SafetyConfig:          q.SafetyConfig.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		APITool:               q.APITool.replaceDB(db),
		APIToolCollection:     q.APIToolCollection.replaceDB(db),
		APIToolCollectionDemo: q.APIToolCollectionDemo.replaceDB(db),
		AppletChain:           q.AppletChain.replaceDB(db),
		AppletExperiment:      q.AppletExperiment.replaceDB(db),
		AppletLabel:           q.AppletLabel.replaceDB(db),
		ChainDebugHistory:     q.ChainDebugHistory.replaceDB(db),
		ChainSnapshot:         q.ChainSnapshot.replaceDB(db),
		Chunk:                 q.Chunk.replaceDB(db),
		CustomWidget:          q.CustomWidget.replaceDB(db),
		Dialog:                q.Dialog.replaceDB(db),
		DialogApp:             q.DialogApp.replaceDB(db),
		DialogMessage:         q.DialogMessage.replaceDB(db),
		DocElement:            q.DocElement.replaceDB(db),
		DocTask:               q.DocTask.replaceDB(db),
		Document:              q.Document.replaceDB(db),
		KnowledgeBase:         q.KnowledgeBase.replaceDB(db),
		LlmBasicConfig:        q.LlmBasicConfig.replaceDB(db),
		SafetyConfig:          q.SafetyConfig.replaceDB(db),
	}
}

type queryCtx struct {
	APITool               IAPIToolDo
	APIToolCollection     IAPIToolCollectionDo
	APIToolCollectionDemo IAPIToolCollectionDemoDo
	AppletChain           IAppletChainDo
	AppletExperiment      IAppletExperimentDo
	AppletLabel           IAppletLabelDo
	ChainDebugHistory     IChainDebugHistoryDo
	ChainSnapshot         IChainSnapshotDo
	Chunk                 IChunkDo
	CustomWidget          ICustomWidgetDo
	Dialog                IDialogDo
	DialogApp             IDialogAppDo
	DialogMessage         IDialogMessageDo
	DocElement            IDocElementDo
	DocTask               IDocTaskDo
	Document              IDocumentDo
	KnowledgeBase         IKnowledgeBaseDo
	LlmBasicConfig        ILlmBasicConfigDo
	SafetyConfig          ISafetyConfigDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		APITool:               q.APITool.WithContext(ctx),
		APIToolCollection:     q.APIToolCollection.WithContext(ctx),
		APIToolCollectionDemo: q.APIToolCollectionDemo.WithContext(ctx),
		AppletChain:           q.AppletChain.WithContext(ctx),
		AppletExperiment:      q.AppletExperiment.WithContext(ctx),
		AppletLabel:           q.AppletLabel.WithContext(ctx),
		ChainDebugHistory:     q.ChainDebugHistory.WithContext(ctx),
		ChainSnapshot:         q.ChainSnapshot.WithContext(ctx),
		Chunk:                 q.Chunk.WithContext(ctx),
		CustomWidget:          q.CustomWidget.WithContext(ctx),
		Dialog:                q.Dialog.WithContext(ctx),
		DialogApp:             q.DialogApp.WithContext(ctx),
		DialogMessage:         q.DialogMessage.WithContext(ctx),
		DocElement:            q.DocElement.WithContext(ctx),
		DocTask:               q.DocTask.WithContext(ctx),
		Document:              q.Document.WithContext(ctx),
		KnowledgeBase:         q.KnowledgeBase.WithContext(ctx),
		LlmBasicConfig:        q.LlmBasicConfig.WithContext(ctx),
		SafetyConfig:          q.SafetyConfig.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
