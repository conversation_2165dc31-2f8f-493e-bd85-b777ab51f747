// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newAPIToolCollection(db *gorm.DB, opts ...gen.DOOption) aPIToolCollection {
	_aPIToolCollection := aPIToolCollection{}

	_aPIToolCollection.aPIToolCollectionDo.UseDB(db, opts...)
	_aPIToolCollection.aPIToolCollectionDo.UseModel(&generated.APIToolCollection{})

	tableName := _aPIToolCollection.aPIToolCollectionDo.TableName()
	_aPIToolCollection.ALL = field.NewAsterisk(tableName)
	_aPIToolCollection.ID = field.NewString(tableName, "id")
	_aPIToolCollection.ProjectID = field.NewString(tableName, "project_id")
	_aPIToolCollection.Name = field.NewString(tableName, "name")
	_aPIToolCollection.Creator = field.NewString(tableName, "creator")
	_aPIToolCollection.Desc = field.NewString(tableName, "desc")
	_aPIToolCollection.ReleasedState = field.NewString(tableName, "released_state")
	_aPIToolCollection.MetaType = field.NewString(tableName, "meta_type")
	_aPIToolCollection.Type = field.NewString(tableName, "type")
	_aPIToolCollection.MetaInfo = field.NewBytes(tableName, "meta_info")
	_aPIToolCollection.Headers = field.NewString(tableName, "headers")
	_aPIToolCollection.BaseURL = field.NewString(tableName, "base_url")
	_aPIToolCollection.LastPublishTime = field.NewTime(tableName, "last_publish_time")
	_aPIToolCollection.LogoURL = field.NewString(tableName, "logo_url")
	_aPIToolCollection.ProxyInfo = field.NewString(tableName, "proxy_info")
	_aPIToolCollection.CreateTime = field.NewTime(tableName, "create_time")
	_aPIToolCollection.UpdatedTime = field.NewTime(tableName, "updated_time")

	_aPIToolCollection.fillFieldMap()

	return _aPIToolCollection
}

type aPIToolCollection struct {
	aPIToolCollectionDo

	ALL             field.Asterisk
	ID              field.String // ID
	ProjectID       field.String // 项目ID
	Name            field.String // 工具集名称
	Creator         field.String // 创建人
	Desc            field.String // 工具集描述
	ReleasedState   field.String // 是否发布，released，un_released
	MetaType        field.String // 元信息类型 json/yaml
	Type            field.String // 工具集类型 common/template
	MetaInfo        field.Bytes  // 元信息详情
	Headers         field.String //  请求头，map类型
	BaseURL         field.String //  请求地址
	LastPublishTime field.Time   // 最后一次发布时间
	LogoURL         field.String //  工具集logo
	ProxyInfo       field.String //  代理配置
	CreateTime      field.Time   // 创建时间
	UpdatedTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a aPIToolCollection) Table(newTableName string) *aPIToolCollection {
	a.aPIToolCollectionDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aPIToolCollection) As(alias string) *aPIToolCollection {
	a.aPIToolCollectionDo.DO = *(a.aPIToolCollectionDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aPIToolCollection) updateTableName(table string) *aPIToolCollection {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.ProjectID = field.NewString(table, "project_id")
	a.Name = field.NewString(table, "name")
	a.Creator = field.NewString(table, "creator")
	a.Desc = field.NewString(table, "desc")
	a.ReleasedState = field.NewString(table, "released_state")
	a.MetaType = field.NewString(table, "meta_type")
	a.Type = field.NewString(table, "type")
	a.MetaInfo = field.NewBytes(table, "meta_info")
	a.Headers = field.NewString(table, "headers")
	a.BaseURL = field.NewString(table, "base_url")
	a.LastPublishTime = field.NewTime(table, "last_publish_time")
	a.LogoURL = field.NewString(table, "logo_url")
	a.ProxyInfo = field.NewString(table, "proxy_info")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdatedTime = field.NewTime(table, "updated_time")

	a.fillFieldMap()

	return a
}

func (a *aPIToolCollection) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aPIToolCollection) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 16)
	a.fieldMap["id"] = a.ID
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["name"] = a.Name
	a.fieldMap["creator"] = a.Creator
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["released_state"] = a.ReleasedState
	a.fieldMap["meta_type"] = a.MetaType
	a.fieldMap["type"] = a.Type
	a.fieldMap["meta_info"] = a.MetaInfo
	a.fieldMap["headers"] = a.Headers
	a.fieldMap["base_url"] = a.BaseURL
	a.fieldMap["last_publish_time"] = a.LastPublishTime
	a.fieldMap["logo_url"] = a.LogoURL
	a.fieldMap["proxy_info"] = a.ProxyInfo
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["updated_time"] = a.UpdatedTime
}

func (a aPIToolCollection) clone(db *gorm.DB) aPIToolCollection {
	a.aPIToolCollectionDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aPIToolCollection) replaceDB(db *gorm.DB) aPIToolCollection {
	a.aPIToolCollectionDo.ReplaceDB(db)
	return a
}

type aPIToolCollectionDo struct{ gen.DO }

type IAPIToolCollectionDo interface {
	gen.SubQuery
	Debug() IAPIToolCollectionDo
	WithContext(ctx context.Context) IAPIToolCollectionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAPIToolCollectionDo
	WriteDB() IAPIToolCollectionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAPIToolCollectionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAPIToolCollectionDo
	Not(conds ...gen.Condition) IAPIToolCollectionDo
	Or(conds ...gen.Condition) IAPIToolCollectionDo
	Select(conds ...field.Expr) IAPIToolCollectionDo
	Where(conds ...gen.Condition) IAPIToolCollectionDo
	Order(conds ...field.Expr) IAPIToolCollectionDo
	Distinct(cols ...field.Expr) IAPIToolCollectionDo
	Omit(cols ...field.Expr) IAPIToolCollectionDo
	Join(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo
	Group(cols ...field.Expr) IAPIToolCollectionDo
	Having(conds ...gen.Condition) IAPIToolCollectionDo
	Limit(limit int) IAPIToolCollectionDo
	Offset(offset int) IAPIToolCollectionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolCollectionDo
	Unscoped() IAPIToolCollectionDo
	Create(values ...*generated.APIToolCollection) error
	CreateInBatches(values []*generated.APIToolCollection, batchSize int) error
	Save(values ...*generated.APIToolCollection) error
	First() (*generated.APIToolCollection, error)
	Take() (*generated.APIToolCollection, error)
	Last() (*generated.APIToolCollection, error)
	Find() ([]*generated.APIToolCollection, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APIToolCollection, err error)
	FindInBatches(result *[]*generated.APIToolCollection, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.APIToolCollection) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAPIToolCollectionDo
	Assign(attrs ...field.AssignExpr) IAPIToolCollectionDo
	Joins(fields ...field.RelationField) IAPIToolCollectionDo
	Preload(fields ...field.RelationField) IAPIToolCollectionDo
	FirstOrInit() (*generated.APIToolCollection, error)
	FirstOrCreate() (*generated.APIToolCollection, error)
	FindByPage(offset int, limit int) (result []*generated.APIToolCollection, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAPIToolCollectionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aPIToolCollectionDo) Debug() IAPIToolCollectionDo {
	return a.withDO(a.DO.Debug())
}

func (a aPIToolCollectionDo) WithContext(ctx context.Context) IAPIToolCollectionDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aPIToolCollectionDo) ReadDB() IAPIToolCollectionDo {
	return a.Clauses(dbresolver.Read)
}

func (a aPIToolCollectionDo) WriteDB() IAPIToolCollectionDo {
	return a.Clauses(dbresolver.Write)
}

func (a aPIToolCollectionDo) Session(config *gorm.Session) IAPIToolCollectionDo {
	return a.withDO(a.DO.Session(config))
}

func (a aPIToolCollectionDo) Clauses(conds ...clause.Expression) IAPIToolCollectionDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aPIToolCollectionDo) Returning(value interface{}, columns ...string) IAPIToolCollectionDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aPIToolCollectionDo) Not(conds ...gen.Condition) IAPIToolCollectionDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aPIToolCollectionDo) Or(conds ...gen.Condition) IAPIToolCollectionDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aPIToolCollectionDo) Select(conds ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aPIToolCollectionDo) Where(conds ...gen.Condition) IAPIToolCollectionDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aPIToolCollectionDo) Order(conds ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aPIToolCollectionDo) Distinct(cols ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aPIToolCollectionDo) Omit(cols ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aPIToolCollectionDo) Join(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aPIToolCollectionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aPIToolCollectionDo) RightJoin(table schema.Tabler, on ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aPIToolCollectionDo) Group(cols ...field.Expr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aPIToolCollectionDo) Having(conds ...gen.Condition) IAPIToolCollectionDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aPIToolCollectionDo) Limit(limit int) IAPIToolCollectionDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aPIToolCollectionDo) Offset(offset int) IAPIToolCollectionDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aPIToolCollectionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIToolCollectionDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aPIToolCollectionDo) Unscoped() IAPIToolCollectionDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aPIToolCollectionDo) Create(values ...*generated.APIToolCollection) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aPIToolCollectionDo) CreateInBatches(values []*generated.APIToolCollection, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aPIToolCollectionDo) Save(values ...*generated.APIToolCollection) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aPIToolCollectionDo) First() (*generated.APIToolCollection, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollection), nil
	}
}

func (a aPIToolCollectionDo) Take() (*generated.APIToolCollection, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollection), nil
	}
}

func (a aPIToolCollectionDo) Last() (*generated.APIToolCollection, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollection), nil
	}
}

func (a aPIToolCollectionDo) Find() ([]*generated.APIToolCollection, error) {
	result, err := a.DO.Find()
	return result.([]*generated.APIToolCollection), err
}

func (a aPIToolCollectionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.APIToolCollection, err error) {
	buf := make([]*generated.APIToolCollection, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aPIToolCollectionDo) FindInBatches(result *[]*generated.APIToolCollection, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aPIToolCollectionDo) Attrs(attrs ...field.AssignExpr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aPIToolCollectionDo) Assign(attrs ...field.AssignExpr) IAPIToolCollectionDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aPIToolCollectionDo) Joins(fields ...field.RelationField) IAPIToolCollectionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aPIToolCollectionDo) Preload(fields ...field.RelationField) IAPIToolCollectionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aPIToolCollectionDo) FirstOrInit() (*generated.APIToolCollection, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollection), nil
	}
}

func (a aPIToolCollectionDo) FirstOrCreate() (*generated.APIToolCollection, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.APIToolCollection), nil
	}
}

func (a aPIToolCollectionDo) FindByPage(offset int, limit int) (result []*generated.APIToolCollection, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aPIToolCollectionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aPIToolCollectionDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aPIToolCollectionDo) Delete(models ...*generated.APIToolCollection) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aPIToolCollectionDo) withDO(do gen.Dao) *aPIToolCollectionDo {
	a.DO = *do.(*gen.DO)
	return a
}
