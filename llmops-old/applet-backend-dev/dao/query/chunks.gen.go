// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newChunk(db *gorm.DB, opts ...gen.DOOption) chunk {
	_chunk := chunk{}

	_chunk.chunkDo.UseDB(db, opts...)
	_chunk.chunkDo.UseModel(&models.Chunk{})

	tableName := _chunk.chunkDo.TableName()
	_chunk.ALL = field.NewAsterisk(tableName)
	_chunk.Id = field.NewString(tableName, "id")
	_chunk.Content = field.NewString(tableName, "content")
	_chunk.ElementIDs = field.NewField(tableName, "element_ids")
	_chunk.SourceType = field.NewInt32(tableName, "source_type")
	_chunk.ContentType = field.NewInt32(tableName, "content_type")
	_chunk.DisableVectorIndexing = field.NewBool(tableName, "disable_vector_indexing")
	_chunk.DisableFullTextIndexing = field.NewBool(tableName, "disable_full_text_indexing")
	_chunk.DocumentId = field.NewString(tableName, "document_id")
	_chunk.KnowledgeBaseId = field.NewString(tableName, "knowledge_base_id")
	_chunk.AugmentedChunks = field.NewField(tableName, "augmented_chunks")
	_chunk.OrderId = field.NewInt(tableName, "order_id")
	_chunk.Edited = field.NewBool(tableName, "edited")

	_chunk.fillFieldMap()

	return _chunk
}

type chunk struct {
	chunkDo

	ALL                     field.Asterisk
	Id                      field.String
	Content                 field.String
	ElementIDs              field.Field
	SourceType              field.Int32
	ContentType             field.Int32
	DisableVectorIndexing   field.Bool
	DisableFullTextIndexing field.Bool
	DocumentId              field.String
	KnowledgeBaseId         field.String
	AugmentedChunks         field.Field
	OrderId                 field.Int
	Edited                  field.Bool

	fieldMap map[string]field.Expr
}

func (c chunk) Table(newTableName string) *chunk {
	c.chunkDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chunk) As(alias string) *chunk {
	c.chunkDo.DO = *(c.chunkDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chunk) updateTableName(table string) *chunk {
	c.ALL = field.NewAsterisk(table)
	c.Id = field.NewString(table, "id")
	c.Content = field.NewString(table, "content")
	c.ElementIDs = field.NewField(table, "element_ids")
	c.SourceType = field.NewInt32(table, "source_type")
	c.ContentType = field.NewInt32(table, "content_type")
	c.DisableVectorIndexing = field.NewBool(table, "disable_vector_indexing")
	c.DisableFullTextIndexing = field.NewBool(table, "disable_full_text_indexing")
	c.DocumentId = field.NewString(table, "document_id")
	c.KnowledgeBaseId = field.NewString(table, "knowledge_base_id")
	c.AugmentedChunks = field.NewField(table, "augmented_chunks")
	c.OrderId = field.NewInt(table, "order_id")
	c.Edited = field.NewBool(table, "edited")

	c.fillFieldMap()

	return c
}

func (c *chunk) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chunk) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 12)
	c.fieldMap["id"] = c.Id
	c.fieldMap["content"] = c.Content
	c.fieldMap["element_ids"] = c.ElementIDs
	c.fieldMap["source_type"] = c.SourceType
	c.fieldMap["content_type"] = c.ContentType
	c.fieldMap["disable_vector_indexing"] = c.DisableVectorIndexing
	c.fieldMap["disable_full_text_indexing"] = c.DisableFullTextIndexing
	c.fieldMap["document_id"] = c.DocumentId
	c.fieldMap["knowledge_base_id"] = c.KnowledgeBaseId
	c.fieldMap["augmented_chunks"] = c.AugmentedChunks
	c.fieldMap["order_id"] = c.OrderId
	c.fieldMap["edited"] = c.Edited
}

func (c chunk) clone(db *gorm.DB) chunk {
	c.chunkDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c chunk) replaceDB(db *gorm.DB) chunk {
	c.chunkDo.ReplaceDB(db)
	return c
}

type chunkDo struct{ gen.DO }

type IChunkDo interface {
	gen.SubQuery
	Debug() IChunkDo
	WithContext(ctx context.Context) IChunkDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IChunkDo
	WriteDB() IChunkDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IChunkDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IChunkDo
	Not(conds ...gen.Condition) IChunkDo
	Or(conds ...gen.Condition) IChunkDo
	Select(conds ...field.Expr) IChunkDo
	Where(conds ...gen.Condition) IChunkDo
	Order(conds ...field.Expr) IChunkDo
	Distinct(cols ...field.Expr) IChunkDo
	Omit(cols ...field.Expr) IChunkDo
	Join(table schema.Tabler, on ...field.Expr) IChunkDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IChunkDo
	RightJoin(table schema.Tabler, on ...field.Expr) IChunkDo
	Group(cols ...field.Expr) IChunkDo
	Having(conds ...gen.Condition) IChunkDo
	Limit(limit int) IChunkDo
	Offset(offset int) IChunkDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IChunkDo
	Unscoped() IChunkDo
	Create(values ...*models.Chunk) error
	CreateInBatches(values []*models.Chunk, batchSize int) error
	Save(values ...*models.Chunk) error
	First() (*models.Chunk, error)
	Take() (*models.Chunk, error)
	Last() (*models.Chunk, error)
	Find() ([]*models.Chunk, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Chunk, err error)
	FindInBatches(result *[]*models.Chunk, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Chunk) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IChunkDo
	Assign(attrs ...field.AssignExpr) IChunkDo
	Joins(fields ...field.RelationField) IChunkDo
	Preload(fields ...field.RelationField) IChunkDo
	FirstOrInit() (*models.Chunk, error)
	FirstOrCreate() (*models.Chunk, error)
	FindByPage(offset int, limit int) (result []*models.Chunk, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IChunkDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c chunkDo) Debug() IChunkDo {
	return c.withDO(c.DO.Debug())
}

func (c chunkDo) WithContext(ctx context.Context) IChunkDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chunkDo) ReadDB() IChunkDo {
	return c.Clauses(dbresolver.Read)
}

func (c chunkDo) WriteDB() IChunkDo {
	return c.Clauses(dbresolver.Write)
}

func (c chunkDo) Session(config *gorm.Session) IChunkDo {
	return c.withDO(c.DO.Session(config))
}

func (c chunkDo) Clauses(conds ...clause.Expression) IChunkDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chunkDo) Returning(value interface{}, columns ...string) IChunkDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chunkDo) Not(conds ...gen.Condition) IChunkDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chunkDo) Or(conds ...gen.Condition) IChunkDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chunkDo) Select(conds ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chunkDo) Where(conds ...gen.Condition) IChunkDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chunkDo) Order(conds ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chunkDo) Distinct(cols ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chunkDo) Omit(cols ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chunkDo) Join(table schema.Tabler, on ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chunkDo) LeftJoin(table schema.Tabler, on ...field.Expr) IChunkDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chunkDo) RightJoin(table schema.Tabler, on ...field.Expr) IChunkDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chunkDo) Group(cols ...field.Expr) IChunkDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chunkDo) Having(conds ...gen.Condition) IChunkDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chunkDo) Limit(limit int) IChunkDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chunkDo) Offset(offset int) IChunkDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chunkDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IChunkDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chunkDo) Unscoped() IChunkDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chunkDo) Create(values ...*models.Chunk) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chunkDo) CreateInBatches(values []*models.Chunk, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chunkDo) Save(values ...*models.Chunk) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chunkDo) First() (*models.Chunk, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Chunk), nil
	}
}

func (c chunkDo) Take() (*models.Chunk, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Chunk), nil
	}
}

func (c chunkDo) Last() (*models.Chunk, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Chunk), nil
	}
}

func (c chunkDo) Find() ([]*models.Chunk, error) {
	result, err := c.DO.Find()
	return result.([]*models.Chunk), err
}

func (c chunkDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Chunk, err error) {
	buf := make([]*models.Chunk, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chunkDo) FindInBatches(result *[]*models.Chunk, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chunkDo) Attrs(attrs ...field.AssignExpr) IChunkDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chunkDo) Assign(attrs ...field.AssignExpr) IChunkDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chunkDo) Joins(fields ...field.RelationField) IChunkDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chunkDo) Preload(fields ...field.RelationField) IChunkDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chunkDo) FirstOrInit() (*models.Chunk, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Chunk), nil
	}
}

func (c chunkDo) FirstOrCreate() (*models.Chunk, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Chunk), nil
	}
}

func (c chunkDo) FindByPage(offset int, limit int) (result []*models.Chunk, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chunkDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chunkDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chunkDo) Delete(models ...*models.Chunk) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chunkDo) withDO(do gen.Dao) *chunkDo {
	c.DO = *do.(*gen.DO)
	return c
}
