// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"transwarp.io/applied-ai/applet-backend/pkg/models"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newKnowledgeBase(db *gorm.DB, opts ...gen.DOOption) knowledgeBase {
	_knowledgeBase := knowledgeBase{}

	_knowledgeBase.knowledgeBaseDo.UseDB(db, opts...)
	_knowledgeBase.knowledgeBaseDo.UseModel(&models.KnowledgeBase{})

	tableName := _knowledgeBase.knowledgeBaseDo.TableName()
	_knowledgeBase.ALL = field.NewAsterisk(tableName)
	_knowledgeBase.Id = field.NewString(tableName, "id")
	_knowledgeBase.Name = field.NewString(tableName, "name")
	_knowledgeBase.Description = field.NewString(tableName, "description")
	_knowledgeBase.ContentType = field.NewInt32(tableName, "content_type")
	_knowledgeBase.SourceType = field.NewInt32(tableName, "source_type")
	_knowledgeBase.RegistryType = field.NewInt32(tableName, "registry_type")
	_knowledgeBase.Icon = field.NewString(tableName, "icon")
	_knowledgeBase.ConnectionRegistry = field.NewField(tableName, "connection_registry")
	_knowledgeBase.TkhRegistry = field.NewField(tableName, "tkh_registry")
	_knowledgeBase.Creator = field.NewString(tableName, "creator")
	_knowledgeBase.CreateTime = field.NewTime(tableName, "create_time_mills")
	_knowledgeBase.UpdateTime = field.NewTime(tableName, "update_time_mills")
	_knowledgeBase.DisabledDocs = field.NewField(tableName, "disabled_docs")
	_knowledgeBase.ProjectId = field.NewString(tableName, "project_id")
	_knowledgeBase.IsVisible = field.NewBool(tableName, "is_visible")
	_knowledgeBase.IsRetrievable = field.NewBool(tableName, "is_retrievable")
	_knowledgeBase.CreationType = field.NewInt32(tableName, "creation_type")
	_knowledgeBase.DocProcessingConfig = field.NewField(tableName, "doc_processing_config")
	_knowledgeBase.RetrievalConfig = field.NewField(tableName, "retrieval_config")
	_knowledgeBase.VectorModel = field.NewField(tableName, "vector_model")
	_knowledgeBase.IsPublic = field.NewBool(tableName, "is_public")
	_knowledgeBase.MetricsInfo = field.NewField(tableName, "metrics_info")
	_knowledgeBase.IsPublished = field.NewBool(tableName, "is_published")
	_knowledgeBase.SceneType = field.NewInt32(tableName, "scene_type")
	_knowledgeBase.PublishInfo = field.NewField(tableName, "publish_info")

	_knowledgeBase.fillFieldMap()

	return _knowledgeBase
}

type knowledgeBase struct {
	knowledgeBaseDo

	ALL                 field.Asterisk
	Id                  field.String
	Name                field.String
	Description         field.String
	ContentType         field.Int32
	SourceType          field.Int32
	RegistryType        field.Int32
	Icon                field.String
	ConnectionRegistry  field.Field
	TkhRegistry         field.Field
	Creator             field.String
	CreateTime          field.Time
	UpdateTime          field.Time
	DisabledDocs        field.Field
	ProjectId           field.String
	IsVisible           field.Bool
	IsRetrievable       field.Bool
	CreationType        field.Int32
	DocProcessingConfig field.Field
	RetrievalConfig     field.Field
	VectorModel         field.Field
	IsPublic            field.Bool
	MetricsInfo         field.Field
	IsPublished         field.Bool
	SceneType           field.Int32
	PublishInfo         field.Field

	fieldMap map[string]field.Expr
}

func (k knowledgeBase) Table(newTableName string) *knowledgeBase {
	k.knowledgeBaseDo.UseTable(newTableName)
	return k.updateTableName(newTableName)
}

func (k knowledgeBase) As(alias string) *knowledgeBase {
	k.knowledgeBaseDo.DO = *(k.knowledgeBaseDo.As(alias).(*gen.DO))
	return k.updateTableName(alias)
}

func (k *knowledgeBase) updateTableName(table string) *knowledgeBase {
	k.ALL = field.NewAsterisk(table)
	k.Id = field.NewString(table, "id")
	k.Name = field.NewString(table, "name")
	k.Description = field.NewString(table, "description")
	k.ContentType = field.NewInt32(table, "content_type")
	k.SourceType = field.NewInt32(table, "source_type")
	k.RegistryType = field.NewInt32(table, "registry_type")
	k.Icon = field.NewString(table, "icon")
	k.ConnectionRegistry = field.NewField(table, "connection_registry")
	k.TkhRegistry = field.NewField(table, "tkh_registry")
	k.Creator = field.NewString(table, "creator")
	k.CreateTime = field.NewTime(table, "create_time_mills")
	k.UpdateTime = field.NewTime(table, "update_time_mills")
	k.DisabledDocs = field.NewField(table, "disabled_docs")
	k.ProjectId = field.NewString(table, "project_id")
	k.IsVisible = field.NewBool(table, "is_visible")
	k.IsRetrievable = field.NewBool(table, "is_retrievable")
	k.CreationType = field.NewInt32(table, "creation_type")
	k.DocProcessingConfig = field.NewField(table, "doc_processing_config")
	k.RetrievalConfig = field.NewField(table, "retrieval_config")
	k.VectorModel = field.NewField(table, "vector_model")
	k.IsPublic = field.NewBool(table, "is_public")
	k.MetricsInfo = field.NewField(table, "metrics_info")
	k.IsPublished = field.NewBool(table, "is_published")
	k.SceneType = field.NewInt32(table, "scene_type")
	k.PublishInfo = field.NewField(table, "publish_info")

	k.fillFieldMap()

	return k
}

func (k *knowledgeBase) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := k.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (k *knowledgeBase) fillFieldMap() {
	k.fieldMap = make(map[string]field.Expr, 25)
	k.fieldMap["id"] = k.Id
	k.fieldMap["name"] = k.Name
	k.fieldMap["description"] = k.Description
	k.fieldMap["content_type"] = k.ContentType
	k.fieldMap["source_type"] = k.SourceType
	k.fieldMap["registry_type"] = k.RegistryType
	k.fieldMap["icon"] = k.Icon
	k.fieldMap["connection_registry"] = k.ConnectionRegistry
	k.fieldMap["tkh_registry"] = k.TkhRegistry
	k.fieldMap["creator"] = k.Creator
	k.fieldMap["create_time_mills"] = k.CreateTime
	k.fieldMap["update_time_mills"] = k.UpdateTime
	k.fieldMap["disabled_docs"] = k.DisabledDocs
	k.fieldMap["project_id"] = k.ProjectId
	k.fieldMap["is_visible"] = k.IsVisible
	k.fieldMap["is_retrievable"] = k.IsRetrievable
	k.fieldMap["creation_type"] = k.CreationType
	k.fieldMap["doc_processing_config"] = k.DocProcessingConfig
	k.fieldMap["retrieval_config"] = k.RetrievalConfig
	k.fieldMap["vector_model"] = k.VectorModel
	k.fieldMap["is_public"] = k.IsPublic
	k.fieldMap["metrics_info"] = k.MetricsInfo
	k.fieldMap["is_published"] = k.IsPublished
	k.fieldMap["scene_type"] = k.SceneType
	k.fieldMap["publish_info"] = k.PublishInfo
}

func (k knowledgeBase) clone(db *gorm.DB) knowledgeBase {
	k.knowledgeBaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return k
}

func (k knowledgeBase) replaceDB(db *gorm.DB) knowledgeBase {
	k.knowledgeBaseDo.ReplaceDB(db)
	return k
}

type knowledgeBaseDo struct{ gen.DO }

type IKnowledgeBaseDo interface {
	gen.SubQuery
	Debug() IKnowledgeBaseDo
	WithContext(ctx context.Context) IKnowledgeBaseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IKnowledgeBaseDo
	WriteDB() IKnowledgeBaseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IKnowledgeBaseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IKnowledgeBaseDo
	Not(conds ...gen.Condition) IKnowledgeBaseDo
	Or(conds ...gen.Condition) IKnowledgeBaseDo
	Select(conds ...field.Expr) IKnowledgeBaseDo
	Where(conds ...gen.Condition) IKnowledgeBaseDo
	Order(conds ...field.Expr) IKnowledgeBaseDo
	Distinct(cols ...field.Expr) IKnowledgeBaseDo
	Omit(cols ...field.Expr) IKnowledgeBaseDo
	Join(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo
	RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo
	Group(cols ...field.Expr) IKnowledgeBaseDo
	Having(conds ...gen.Condition) IKnowledgeBaseDo
	Limit(limit int) IKnowledgeBaseDo
	Offset(offset int) IKnowledgeBaseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeBaseDo
	Unscoped() IKnowledgeBaseDo
	Create(values ...*models.KnowledgeBase) error
	CreateInBatches(values []*models.KnowledgeBase, batchSize int) error
	Save(values ...*models.KnowledgeBase) error
	First() (*models.KnowledgeBase, error)
	Take() (*models.KnowledgeBase, error)
	Last() (*models.KnowledgeBase, error)
	Find() ([]*models.KnowledgeBase, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.KnowledgeBase, err error)
	FindInBatches(result *[]*models.KnowledgeBase, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.KnowledgeBase) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IKnowledgeBaseDo
	Assign(attrs ...field.AssignExpr) IKnowledgeBaseDo
	Joins(fields ...field.RelationField) IKnowledgeBaseDo
	Preload(fields ...field.RelationField) IKnowledgeBaseDo
	FirstOrInit() (*models.KnowledgeBase, error)
	FirstOrCreate() (*models.KnowledgeBase, error)
	FindByPage(offset int, limit int) (result []*models.KnowledgeBase, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IKnowledgeBaseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (k knowledgeBaseDo) Debug() IKnowledgeBaseDo {
	return k.withDO(k.DO.Debug())
}

func (k knowledgeBaseDo) WithContext(ctx context.Context) IKnowledgeBaseDo {
	return k.withDO(k.DO.WithContext(ctx))
}

func (k knowledgeBaseDo) ReadDB() IKnowledgeBaseDo {
	return k.Clauses(dbresolver.Read)
}

func (k knowledgeBaseDo) WriteDB() IKnowledgeBaseDo {
	return k.Clauses(dbresolver.Write)
}

func (k knowledgeBaseDo) Session(config *gorm.Session) IKnowledgeBaseDo {
	return k.withDO(k.DO.Session(config))
}

func (k knowledgeBaseDo) Clauses(conds ...clause.Expression) IKnowledgeBaseDo {
	return k.withDO(k.DO.Clauses(conds...))
}

func (k knowledgeBaseDo) Returning(value interface{}, columns ...string) IKnowledgeBaseDo {
	return k.withDO(k.DO.Returning(value, columns...))
}

func (k knowledgeBaseDo) Not(conds ...gen.Condition) IKnowledgeBaseDo {
	return k.withDO(k.DO.Not(conds...))
}

func (k knowledgeBaseDo) Or(conds ...gen.Condition) IKnowledgeBaseDo {
	return k.withDO(k.DO.Or(conds...))
}

func (k knowledgeBaseDo) Select(conds ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Select(conds...))
}

func (k knowledgeBaseDo) Where(conds ...gen.Condition) IKnowledgeBaseDo {
	return k.withDO(k.DO.Where(conds...))
}

func (k knowledgeBaseDo) Order(conds ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Order(conds...))
}

func (k knowledgeBaseDo) Distinct(cols ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Distinct(cols...))
}

func (k knowledgeBaseDo) Omit(cols ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Omit(cols...))
}

func (k knowledgeBaseDo) Join(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Join(table, on...))
}

func (k knowledgeBaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.LeftJoin(table, on...))
}

func (k knowledgeBaseDo) RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.RightJoin(table, on...))
}

func (k knowledgeBaseDo) Group(cols ...field.Expr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Group(cols...))
}

func (k knowledgeBaseDo) Having(conds ...gen.Condition) IKnowledgeBaseDo {
	return k.withDO(k.DO.Having(conds...))
}

func (k knowledgeBaseDo) Limit(limit int) IKnowledgeBaseDo {
	return k.withDO(k.DO.Limit(limit))
}

func (k knowledgeBaseDo) Offset(offset int) IKnowledgeBaseDo {
	return k.withDO(k.DO.Offset(offset))
}

func (k knowledgeBaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeBaseDo {
	return k.withDO(k.DO.Scopes(funcs...))
}

func (k knowledgeBaseDo) Unscoped() IKnowledgeBaseDo {
	return k.withDO(k.DO.Unscoped())
}

func (k knowledgeBaseDo) Create(values ...*models.KnowledgeBase) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Create(values)
}

func (k knowledgeBaseDo) CreateInBatches(values []*models.KnowledgeBase, batchSize int) error {
	return k.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (k knowledgeBaseDo) Save(values ...*models.KnowledgeBase) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Save(values)
}

func (k knowledgeBaseDo) First() (*models.KnowledgeBase, error) {
	if result, err := k.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.KnowledgeBase), nil
	}
}

func (k knowledgeBaseDo) Take() (*models.KnowledgeBase, error) {
	if result, err := k.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.KnowledgeBase), nil
	}
}

func (k knowledgeBaseDo) Last() (*models.KnowledgeBase, error) {
	if result, err := k.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.KnowledgeBase), nil
	}
}

func (k knowledgeBaseDo) Find() ([]*models.KnowledgeBase, error) {
	result, err := k.DO.Find()
	return result.([]*models.KnowledgeBase), err
}

func (k knowledgeBaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.KnowledgeBase, err error) {
	buf := make([]*models.KnowledgeBase, 0, batchSize)
	err = k.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (k knowledgeBaseDo) FindInBatches(result *[]*models.KnowledgeBase, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return k.DO.FindInBatches(result, batchSize, fc)
}

func (k knowledgeBaseDo) Attrs(attrs ...field.AssignExpr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Attrs(attrs...))
}

func (k knowledgeBaseDo) Assign(attrs ...field.AssignExpr) IKnowledgeBaseDo {
	return k.withDO(k.DO.Assign(attrs...))
}

func (k knowledgeBaseDo) Joins(fields ...field.RelationField) IKnowledgeBaseDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Joins(_f))
	}
	return &k
}

func (k knowledgeBaseDo) Preload(fields ...field.RelationField) IKnowledgeBaseDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Preload(_f))
	}
	return &k
}

func (k knowledgeBaseDo) FirstOrInit() (*models.KnowledgeBase, error) {
	if result, err := k.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.KnowledgeBase), nil
	}
}

func (k knowledgeBaseDo) FirstOrCreate() (*models.KnowledgeBase, error) {
	if result, err := k.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.KnowledgeBase), nil
	}
}

func (k knowledgeBaseDo) FindByPage(offset int, limit int) (result []*models.KnowledgeBase, count int64, err error) {
	result, err = k.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = k.Offset(-1).Limit(-1).Count()
	return
}

func (k knowledgeBaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = k.Count()
	if err != nil {
		return
	}

	err = k.Offset(offset).Limit(limit).Scan(result)
	return
}

func (k knowledgeBaseDo) Scan(result interface{}) (err error) {
	return k.DO.Scan(result)
}

func (k knowledgeBaseDo) Delete(models ...*models.KnowledgeBase) (result gen.ResultInfo, err error) {
	return k.DO.Delete(models)
}

func (k *knowledgeBaseDo) withDO(do gen.Dao) *knowledgeBaseDo {
	k.DO = *do.(*gen.DO)
	return k
}
