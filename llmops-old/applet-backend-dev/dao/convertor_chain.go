package dao

import (
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

const (
	ApiPrefix = "https://*************:30443/gateway/applet"
	RunApi    = "api/v1/applet/chains:run"
)

var (
	ChainDebugStateInit = &models.ChainDebugState{
		State: "init",
		Code:  0,
	}
)

func BatchConvertChainPO2DO(chains []*generated.AppletChain, chainStates []*ChainLastDebugState) ([]*models.AppletChainDO, error) {
	chanStateMap := make(map[string]int32)
	for _, c := range chainStates {
		chanStateMap[c.ChainID] = c.LastState
	}
	res := make([]*models.AppletChainDO, 0)
	for _, m := range chains {
		state, ok := chanStateMap[m.ID]
		if !ok {
			state = ChainDebugStateInit.Code
		}
		if modelDO, err := models.ConvertChainPO2ChainDO(m, state); err != nil {
			return res, err
		} else {
			res = append(res, modelDO)
		}
	}
	return res, nil
}

func BatchConvertChainPO2BaseDO(chains []*generated.AppletChain) ([]*models.AppletChainBaseDO, error) {
	res := make([]*models.AppletChainBaseDO, 0)
	for _, m := range chains {
		if modelDO, err := models.ConvertChainPO2BaseDO(m, m.LastDebugState); err != nil {
			return nil, err
		} else {
			res = append(res, modelDO)
		}
	}
	return res, nil
}
func BatchConvertChainPO2ChainDO(chains []*generated.AppletChain) ([]*models.AppletChainDO, error) {
	res := make([]*models.AppletChainDO, 0)
	for _, m := range chains {
		if modelDO, err := models.ConvertChainPO2ChainDO(m, m.LastDebugState); err != nil {
			return nil, err
		} else {
			res = append(res, modelDO)
		}
	}
	return res, nil
}
