package dao

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

type ChainSnapshotDAO struct {
	BaseDAO
}

func (c ChainSnapshotDAO) Save(ctx context.Context, model *generated.ChainSnapshot) (int64, error) {
	// 使用自动生成的 Save 方法插入记录
	if err := c.getQueryOrDefault().ChainSnapshot.WithContext(ctx).Save(model); err != nil {
		return 0, err
	}

	// 插入后，GORM 会自动填充 model.Id
	return model.ID, nil
}

func (c ChainSnapshotDAO) GetByID(ctx context.Context, ID int64) (*generated.ChainSnapshot, error) {
	q := c.getQueryOrDefault().ChainSnapshot
	return q.WithContext(ctx).Where(q.ID.Eq(ID)).First()
}
