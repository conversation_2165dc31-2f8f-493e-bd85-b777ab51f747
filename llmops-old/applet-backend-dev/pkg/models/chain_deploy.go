package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

type ChainDeployCfg struct {
	ChainID     string            `json:"chain_id"`
	ChainDetail *AppletChainDO    `json:"chain_detail"`
	Image       string            `json:"image"`
	TickScript  string            `json:"tick_script"`
	Envs        map[string]string `json:"envs"`
	//API           string             `json:"api"`
	//Port          int32              `json:"port"`
	//QueryTemplate ChainQueryTemplate `json:"query_template"`
	PVCMountCfg []*PVCMountCfg `json:"pvc_mount_cfg"`
	//@Deprecated，用EndPoints
	//ApiInfos        []*serving.API     `json:"api_infos"`
	EndPoints       []*common.Endpoint `json:"endpoints"`
	ChainSnapshotId string             `json:"chain_snapshot_id"`
}

func structToMap(st any) (map[string]any, error) {
	res := make(map[string]any)
	bytes, err := json.Marshal(st)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(bytes, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c ChainDeployCfg) StdMarshal() ([]byte, error) {
	handler := stdsrv.DefaultProtoJsonAccessor()
	mapValue, err := structToMap(c)
	if err != nil {
		return nil, err
	}
	if len(c.EndPoints) > 0 {
		arrValue := make([]map[string]any, 0)
		for _, e := range c.EndPoints {
			endpointValue := make(map[string]any)
			endpointValueBytes, err := handler.Marshal(e)
			if err != nil {
				return nil, err
			}
			if err := json.Unmarshal(endpointValueBytes, &endpointValue); err != nil {
				return nil, err
			}
			arrValue = append(arrValue, endpointValue)
		}
		mapValue["endpoints"] = arrValue
	}
	bytes, err := json.Marshal(mapValue)
	return bytes, err
}

func PVCMountCfgString2DO(cfgs string) ([]*PVCMountCfg, error) {
	res := make([]*PVCMountCfg, 0)
	cfgList := strings.Split(cfgs, ",")
	for _, cfg := range cfgList {
		splits := strings.Split(cfg, ":")
		if len(splits) != 2 {
			return nil, stderr.Internal.Error("invalid pvc mount config :%v", cfg)
		}
		pvc := splits[0]
		mountPath := splits[1]
		pvcSplits := strings.Split(pvc, "##")
		if len(pvcSplits) != 2 {
			return nil, stderr.Internal.Error("invalid pvc mount config :%v", cfg)
		}
		pvcName := pvcSplits[0]
		pvcSubPath := fmt.Sprintf("sfs://%v", pvcSplits[1])
		res = append(res, &PVCMountCfg{
			PVC:        pvcName,
			PVCSubPath: pvcSubPath,
			MountPath:  mountPath,
		})
	}

	return res, nil
}

type PVCMountCfg struct {
	PVC        string `json:"pvc"`
	PVCSubPath string `json:"pvc_sub_path"`
	MountPath  string `json:"mount_path"`
}

type ApprovalState string

const (
	ApprovalStateApproved ApprovalState = "approved"
	ApprovalStateRejected ApprovalState = "rejected"
)

type CasApprovalInfo struct {
	EnabledApproval bool //是否开启了审批
	//IsCallBackRequest bool            //是否是回调的请求
	//ApprovalState     string          //审批通过&拒绝
	Examination ExaminationInfo // 审批表单详情
}
