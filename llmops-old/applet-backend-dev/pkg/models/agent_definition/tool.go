package agent_definition

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

type Tool struct {
	ID           string                    `json:"id"`
	Type         ToolType                  `json:"type"`
	NameForModel string                    `json:"name_for_model"`
	NameForHuman string                    `json:"name_for_human"`
	Description  string                    `json:"description"`
	Parameters   triton.FunctionParameters `json:"parameters"`
}

type ToolType string

const (
	ToolTypeAPITool       ToolType = "api-tool"
	ToolTypeKnowledgeHub  ToolType = "knowledge-hub"
	ToolTypeAppletService ToolType = "applet-service"
	ToolTypeModelService  ToolType = "model-service"
)

// ToolDescriber 所有需要 LLM 模型决定是否调用，以及调用哪个，以什么参数的 "工具（广义）"
// 均需要实现该接口，提供对于工具的描述，以及工具执行的封装逻辑
type ToolDescriber interface {
	// Type 当前工具类型，可用于判断为其匹配哪个执行器
	Type() ToolType
	// Definition 当前工具在被大模型选择时，需要提供的信息，一般为工具的作用描述，参数信息等
	Definition() Tool
}

// ToolExecutor 工具的执行器
type ToolExecutor interface {
	// Type 当前执行器支持的工具类型，可用于判断能够执行什么工具
	Type() ToolType
	// Execute 使用给定的输入参数，执行对应的工具
	// 对应Agent生成的Action的执行过程
	Execute(ctx context.Context, tool ToolDescriber, input any) (string, error)

	ToolHealthChecker
}

type ToolHealthChecker interface {
	// CheckHealth 检查某个工具的健康状态
	CheckHealth(tool ToolDescriber) (health.ServiceHealth, error)
}
