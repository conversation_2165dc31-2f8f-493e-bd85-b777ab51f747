package agent_definition

import (
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type AppletServiceToolDescriber struct {
	ID           string                   `json:"id" description:"应用链id"`
	MlOpsSvcID   string                   `json:"mlops_svc_id" description:"应用服务id"`
	Name         string                   `json:"name"  description:"应用链名称、服务名称"`
	Desc         string                   `json:"desc"  description:"开场白介绍"`
	CreateTimeMs int64                    `json:"create_time_ms" description:"创建时间,毫秒数"`
	Params       []AppletServiceToolParam `json:"params" description:"应用服务调用参数"`
}

type AppletServiceToolParam struct {
	Name         string               `json:"name" description:"参数名称"`
	Desc         string               `json:"desc" description:"参数描述"`
	ValueType    triton.ParameterType `json:"value_type" description:"integer/number/string/array/object/boolean"`
	DefaultValue any                  `json:"default_value" description:"默认值"`
	Required     bool                 `json:"required" description:"是否必传,非必传则使用默认值"`
}

func (a *AppletServiceToolDescriber) Type() ToolType {
	return ToolTypeAppletService
}

func (a *AppletServiceToolDescriber) Definition() Tool {
	params := make(map[string]triton.ParameterProperty)
	required := make([]string, 0)
	for _, p := range a.Params {
		if p.Required {
			required = append(required, p.Name)
			params[p.Name] = triton.ParameterProperty{
				Type:        p.ValueType,
				Description: p.Desc,
			}
		}
	}
	return Tool{
		ID:           a.ID,
		Type:         a.Type(),
		NameForModel: a.Name,
		NameForHuman: a.Name,
		Description:  a.Desc,
		Parameters: triton.FunctionParameters{
			Type:       triton.ParameterTypeObject,
			Properties: params,
			Required:   required,
		},
	}
}
