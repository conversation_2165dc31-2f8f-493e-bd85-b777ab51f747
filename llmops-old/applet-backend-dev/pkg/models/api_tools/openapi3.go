package api_tools

import (
	"encoding/json"

	"github.com/getkin/kin-openapi/openapi3"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

const (
	OpenApiExtensionAvatarUrl = "x-avatar-url"
)

// OpenAPI3Parser openapi3解析器
type OpenAPI3Parser struct {
	Spec openapi3.T
}

func (o OpenAPI3Parser) GetBaseURL() string {

	if len(o.Spec.Servers) == 0 {
		return ""
	}
	return o.Spec.Servers[0].URL
}

func (o OpenAPI3Parser) GetAPIName() string {
	return o.Spec.Info.Title
}

func (o OpenAPI3Parser) GetDesc() string {
	return o.Spec.Info.Description
}

func (o OpenAPI3Parser) GetAvatarUrl() string {
	avatarUrlAny, ok := o.Spec.ExtensionProps.Extensions[OpenApiExtensionAvatarUrl]
	if !ok {
		stdlog.Warnf("builtin api tool avatar url not found")
		return ""
	}
	avatarUrlRaw, ok := avatarUrlAny.(json.RawMessage)
	if !ok {
		stdlog.Warnf("builtin api tool avatar url is not json raw message")
		return ""
	}
	var avatarUrl string
	if err := json.Unmarshal(avatarUrlRaw, &avatarUrl); err != nil {
		stdlog.Warnf("unmarshal builtin api tool avatar url failed")
		return ""
	}
	return avatarUrl
}

func (o OpenAPI3Parser) GetAPIs() ([]*APIToolDO, error) {
	res := make([]*APIToolDO, 0)
	for path, item := range o.Spec.Paths {
		tools, err := parsePathItem(path, item)
		if err != nil {
			return nil, err
		}
		res = append(res, tools...)
	}
	return res, nil
}
func parserOpValue(op *openapi3.Operation) ([]agent_definition.APIToolParam, error) {
	paramValues := make([]agent_definition.APIToolParam, 0)
	if op == nil {
		return paramValues, nil
	}
	for _, p := range op.Parameters {
		paramValue := p.Value
		var defaultValue any
		paramValueType := "string"
		if paramValue.Schema != nil {
			defaultValue = paramValue.Schema.Value.Default
			paramValueType = paramValue.Schema.Value.Type
		}
		param := agent_definition.APIToolParam{
			Name:           paramValue.Name,
			Desc:           paramValue.Description,
			ParamValueType: paramValueType,
			ParamType:      agent_definition.APIToolParamType(paramValue.In),
			Required:       paramValue.Required,
			DefaultValue:   defaultValue,
			ModelIgnore:    false,
		}
		paramValues = append(paramValues, param)
	}
	if op.RequestBody != nil && op.RequestBody.Value != nil {
		for _, mediumItem := range op.RequestBody.Value.Content {
			if mediumItem.Schema == nil || mediumItem.Schema.Value == nil {
				continue
			}
			// array类型
			if mediumItem.Schema.Value.Type == "array" {
				param := agent_definition.APIToolParam{
					Name:           "-",
					Desc:           "-",
					ParamValueType: "-",
					ParamType:      "array",
					Required:       false,
				}
				paramValues = append(paramValues, param)
				break
			}
			// object类型
			properties := mediumItem.Schema.Value.Properties
			requiredMap := make(map[string]struct{})
			for _, requiredParam := range mediumItem.Schema.Value.Required {
				requiredMap[requiredParam] = struct{}{}
			}
			for k, p := range properties {
				required := false
				if _, ok := requiredMap[k]; ok {
					required = true
				}
				param := agent_definition.APIToolParam{
					Name:           k,
					Desc:           p.Value.Description,
					ParamValueType: p.Value.Type,
					ParamType:      "body",
					DefaultValue:   p.Value.Default,
					ModelIgnore:    false,
					Required:       required,
				}
				paramValues = append(paramValues, param)
			}
			// 暂不支持多个content type对应不同的post body结构，默认取第一个结构
			break
		}
	}
	return paramValues, nil
}

func parsePathItemOption(path string, opStr string, item *openapi3.Operation) (*APIToolDO, error) {
	paramValues, err := parserOpValue(item)
	if err != nil {
		return nil, err
	}
	res := &APIToolDO{
		Name:        path,
		Alias:       path,
		Desc:        item.Description,
		Method:      opStr,
		Path:        path,
		TestState:   APIToolTestStateInit,
		ParamValues: paramValues,
	}
	return res, err
}

func parsePathItem(path string, item *openapi3.PathItem) ([]*APIToolDO, error) {
	res := make([]*APIToolDO, 0)
	opMap := make(map[string]*openapi3.Operation)
	opMap["get"] = item.Get
	opMap["post"] = item.Post
	opMap["put"] = item.Put
	opMap["connect"] = item.Connect
	opMap["head"] = item.Head
	opMap["delete"] = item.Delete
	opMap["patch"] = item.Patch
	opMap["options"] = item.Options
	opMap["trace"] = item.Trace
	for op, opItem := range opMap {
		if opItem == nil {
			continue
		}
		tool, err := parsePathItemOption(path, op, opItem)
		if err != nil {
			return nil, err
		}
		res = append(res, tool)
	}
	return res, nil
}
