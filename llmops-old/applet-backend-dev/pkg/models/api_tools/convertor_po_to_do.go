package api_tools

import (
	"encoding/json"
	"github.com/aws/smithy-go/ptr"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func CollectionReleasedStatePO2DO(releasedInt string) *bool {
	if releasedInt == APIToolCollectionStateReleased {
		return ptr.Bool(true)
	}
	return ptr.Bool(false)
}

func (a *APIToolDO) CvtFromPO(tool *generated.APITool) error {
	params := make([]agent_definition.APIToolParam, 0)
	if err := helper.StringToInterface(tool.Params, &params); err != nil {
		return err
	}
	*a = APIToolDO{
		ID:          tool.ID,
		Name:        tool.Name,
		Alias:       tool.Alias_,
		Desc:        tool.Desc,
		Method:      tool.Method,
		Path:        tool.Path,
		TestState:   APIToolTestState(tool.TestState),
		ParamValues: params,
		ProjectID:   tool.ProjectID,
	}
	return nil
}

func (a *APIToolCollectionBaseDO) CvtFromPO(collection *generated.APIToolCollection, toolCnt int32) error {
	*a = APIToolCollectionBaseDO{
		ID:            collection.ID,
		Name:          collection.Name,
		Creator:       collection.Creator,
		Desc:          collection.Desc,
		APIToolCnt:    int64(toolCnt),
		Released:      CollectionReleasedStatePO2DO(collection.ReleasedState),
		Type:          APIToolType(collection.Type),
		ProjectID:     collection.ProjectID,
		LogoUrl:       collection.LogoURL,
		CreateTimeSec: collection.CreateTime.Unix(),
		UpdateTimeSec: collection.UpdatedTime.Unix(),
	}
	if collection.LastPublishTime != nil {
		a.LastReleaseTimeSec = collection.LastPublishTime.Unix()
	}
	return nil
}

func (a *APIToolCollectionDO) CvtFromPO(collection *generated.APIToolCollection, tools []*generated.APITool) error {
	headers, err := helper.StringToMap(collection.Headers)
	if err != nil {
		return err
	}
	toolDOs := make([]*APIToolDO, 0)
	for _, t := range tools {
		DO := &APIToolDO{}
		if err := DO.CvtFromPO(t); err != nil {
			return err
		}
		toolDOs = append(toolDOs, DO)
	}
	baseInfo := &APIToolCollectionBaseDO{}
	if err := baseInfo.CvtFromPO(collection, int32(len(tools))); err != nil {
		return err
	}
	var proxyInfo *ProxyInfo
	if collection.ProxyInfo != "" {
		if err := json.Unmarshal([]byte(collection.ProxyInfo), &proxyInfo); err != nil {
			return stderr.Wrap(err, "unmarshal proxy info :%v ,err :%v", collection.ProxyInfo, err)
		}
	}
	*a = APIToolCollectionDO{
		BaseInfo:  *baseInfo,
		Tools:     toolDOs,
		MetaType:  APIToolMetaType(collection.MetaType),
		MetaInfo:  collection.MetaInfo,
		Headers:   headers,
		BaseURL:   collection.BaseURL,
		ProxyInfo: proxyInfo,
	}
	return nil
}

func (a *APIToolCollectionDemoDO) CvtFromPO(tool *generated.APIToolCollectionDemo) error {
	*a = APIToolCollectionDemoDO{
		Name:      tool.Name,
		Desc:      tool.Desc,
		ProjectID: tool.ProjectID,
		Type:      APIToolMetaType(tool.MetaType),
		Meta:      tool.MetaInfo,
	}
	return nil
}
