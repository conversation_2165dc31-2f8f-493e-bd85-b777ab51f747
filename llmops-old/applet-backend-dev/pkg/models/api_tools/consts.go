package api_tools

type APIToolType string

const (
	APIToolTypeCommon   APIToolType = "common"
	APIToolTypeTemplate APIToolType = "template"
	APIToolTypeBuiltin  APIToolType = "builtin"
)

//type APIToolListReleaseState string
//
//const (
//	APIToolReleaseStateReleased   APIToolListReleaseState = "released"
//	APIToolReleaseStateUnReleased APIToolListReleaseState = "un_released"
//)

type APIToolMetaType string

const (
	APIToolMetaTypeJson = "json"
	APIToolMetaTypeYaml = "yaml"
)

type APIToolTestState string

const (
	APIToolTestStateInit    APIToolTestState = "init"
	APIToolTestStateSuccess APIToolTestState = "success"
	APIToolTestStateFailed  APIToolTestState = "failed"
)

const (
	APIToolCollectionStateUnReleased = "un_released"
	APIToolCollectionStateReleased   = "released"
)
