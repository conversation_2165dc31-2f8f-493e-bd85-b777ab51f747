package models

import (
	"time"

	"transwarp.io/aip/llmops-common/pb"
)

const (
	TableNameDocuments = "documents"
)

type Document struct {
	// 文档id
	Id string `json:"id" gorm:"column:id;primaryKey;type:varchar(191)" description:"文档id"`

	// 文档名称 (Linux 下通常为255, getconf NAME_MAX /mnt/disk1 可得)
	Name string `json:"name" gorm:"column:name;type:varchar(500)" description:"文档名称"`

	// 文档路径(Linux 下通常为4096, getconf PATH_MAX /mnt/disk1 可得)
	FilePath string `json:"file_path" gorm:"column:file_path;type:varchar(8192)" description:"文档路径"`

	// 文档大小
	FileSizeBytes int32 `json:"file_size_bytes" gorm:"column:file_size_bytes" description:"文档大小"`

	// 文档格式
	FileFormat string `json:"file_format" gorm:"column:file_format;type:varchar(200)" description:"文档格式"`

	// 上传时间
	UploadTime time.Time `json:"upload_time" gorm:"column:upload_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" description:"上传时间"`

	// 字符数
	NumChars int32 `json:"num_chars" gorm:"column:num_chars" description:"字符数"`

	// 文件MD5
	FileMD5 string `json:"file_md5" gorm:"column:file_md5" description:"文件MD5"`

	// 知识库ID
	KnowledgeBaseId string `json:"knowledge_base_id" gorm:"column:knowledge_base_id;type:varchar(191)" description:"知识库ID"`

	ProjectId string `gorm:"column:project_id;type:varchar(191)" json:"projectId"`

	TableConfig *pb.TableConfig `json:"table_config" gorm:"column:table_config;serializer:json" description:"表格配置"`

	DocumentFileSource pb.DocumentFileSource `json:"document_file_source" gorm:"column:document_file_source" description:"文件来源"`

	CorpusConfig *pb.CorpusConfig `json:"corpus_config" gorm:"column:corpus_config;serializer:json" description:"表格配置"`

	DocProcessingConfig *pb.DocProcessingConfig `json:"doc_processing_config" gorm:"column:doc_processing_config;serializer:json" description:"文件处理配置"`
}

func (*Document) TableName() string {
	return TableNameDocuments
}

func (doc *Document) ToPb() *pb.Document {
	return &pb.Document{
		DocId:               doc.Id,
		DocName:             doc.Name,
		FilePath:            doc.FilePath,
		FileSizeBytes:       doc.FileSizeBytes,
		FileFormat:          doc.FileFormat,
		UploadTimeMills:     doc.UploadTime.UnixMilli(),
		NumChars:            doc.NumChars,
		FileMd5:             doc.FileMD5,
		KnowledgeBaseId:     doc.KnowledgeBaseId,
		TableConfig:         doc.TableConfig,
		DocumentFileSource:  doc.DocumentFileSource,
		CorpusConfig:        doc.CorpusConfig,
		DocProcessingConfig: doc.DocProcessingConfig,
	}
}

func FromDocumentPb(protoDoc *pb.Document) *Document {
	return &Document{
		Id:                  protoDoc.DocId,
		Name:                protoDoc.DocName,
		FilePath:            protoDoc.FilePath,
		FileSizeBytes:       protoDoc.FileSizeBytes,
		FileFormat:          protoDoc.FileFormat,
		UploadTime:          time.UnixMilli(protoDoc.UploadTimeMills),
		NumChars:            protoDoc.NumChars,
		FileMD5:             protoDoc.FileMd5,
		KnowledgeBaseId:     protoDoc.KnowledgeBaseId,
		TableConfig:         protoDoc.TableConfig,
		DocumentFileSource:  protoDoc.DocumentFileSource,
		CorpusConfig:        protoDoc.CorpusConfig,
		DocProcessingConfig: protoDoc.DocProcessingConfig,
	}
}

type DocumentSubmitInfo struct {
	FilePath            string
	DocumentFileSource  pb.DocumentFileSource
	TableConfig         *pb.TableConfig
	CorpusConfig        *pb.CorpusConfig
	DocProcessingConfig *pb.DocProcessingConfig
}
