package probe_questions

type ProbeQuestionsParam struct {
	UserLastQuestion     string   `json:"last_question" description:"用户的最后一个问题"`
	ModelLastAnswer      string   `json:"last_answer" description:"模型的最后一次回答"`
	UserHistoryQuestions []string `json:"history_questions" description:"用户上文除最后一个问题外的问题"`
	UserProbeTemplate    string   `json:"template" description:"用户自定义的模板"`
}

type ProbeQuestionsResult struct {
	ProbeQuestion string `json:"probe_questions" description:"追问的问题列表"`
}
