package models

import "time"

type UserGroup []Group

type Group struct {
	Gid              int64     `json:"gid"`
	Name             string    `json:"name"`
	PlatformRoleName string    `json:"platform_role_name"`
	PlatformRoleId   int64     `json:"platform_role_id"`
	Description      string    `json:"description"`
	UserNames        []string  `json:"user_names"`
	CreateUser       string    `json:"create_user"`
	CreateTime       time.Time `json:"create_time"`
}
