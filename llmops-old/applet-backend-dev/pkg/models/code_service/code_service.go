package code_service

import (
	"encoding/json"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

// GenerateCodeRequest represents the input parameters for code generation
type GenerateCodeRequest struct {
	Instruction  string           `json:"instruction"`   // 用户输入的生成代码的提示词
	Inputs       string           `json:"inputs"`        // 算子的输入数据，是一个json string
	Code         string           `json:"code"`          // 算子已有的代码
	Language     string           `json:"language"`      // 代码语言，当前为python，后续可扩展为其他语言
	ModelService *pb.ModelService `json:"model_service"` // 下拉列表选择的模型服务
}

// Validate checks if the request parameters are valid
func (r *GenerateCodeRequest) Validate() error {
	if r.Instruction == "" {
		return stderr.Errorf("instruction is required")
	}
	if r.Language == "" {
		return stderr.Errorf("language is required")
	}
	if r.ModelService == nil {
		return stderr.Errorf("model_service is required")
	}
	return nil
}

// GenerateCodeResponse represents the response for code generation
type GenerateCodeResponse struct {
	Code     string `json:"code"`     // 生成的代码内容
	Language string `json:"language"` // 代码语言
}

// TestCodeRequest represents the input parameters for code testing
type TestCodeRequest struct {
	Code     string `json:"code"`     // 要测试的代码
	Language string `json:"language"` // 代码语言
	Inputs   string `json:"inputs"`   // 测试输入数据，json string格式
}

// Validate checks if the request parameters are valid
func (r *TestCodeRequest) Validate() error {
	if r.Code == "" {
		return stderr.Errorf("code is required")
	}
	if r.Language == "" {
		return stderr.Errorf("language is required")
	}
	if r.Inputs == "" {
		return stderr.Errorf("inputs is required")
	}
	// 验证inputs是否是合法的json string
	if !json.Valid([]byte(r.Inputs)) {
		return stderr.Errorf("inputs is not valid json string")
	}
	return nil
}

// TestCodeResponse represents the response for code testing
type TestCodeResponse struct {
	Output   string `json:"output"`   // 函数的执行结果，json string格式
	Log      string `json:"log"`      // 函数打印的输出
	Error    string `json:"error"`    // 执行失败的错误信息
	Language string `json:"language"` // 代码语言
}

type TestPythonCodeRequest struct {
	Code    string `json:"code"`     // 要测试的python函数
	Inputs  any    `json:"inputs"`   // 代码输入参数
}

type TestPythonCodeResponse struct {
	Output any    `json:"output"`  // 函数的执行结果
	Log    string `json:"log"`     // 函数打印的输出
	Error  string `json:"error"`   // 执行失败的错误信息
}
