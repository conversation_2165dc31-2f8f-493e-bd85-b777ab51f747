package models

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

const (
	LabelAppend = "&&"
)

func ConvertDebugInfo2DO(debugInfoStr string) (*ChainDebugInfo, error) {
	if debugInfoStr == "" {
		return nil, nil
	}
	var debugInfo *ChainDebugInfo
	if err := json.Unmarshal([]byte(debugInfoStr), &debugInfo); err != nil {
		return nil, err
	}
	return debugInfo, nil
}

func ConvertDeployDO2Str(deploy *DeployInfo) (string, error) {
	if deploy == nil {
		return "", nil
	}
	deployBytes, err := json.Marshal(deploy)
	if err != nil {
		return "", err
	}
	return string(deployBytes), nil
}

func ConvertDeployStr2DO(deploy string) (*DeployInfo, error) {
	res := &DeployInfo{}
	if deploy == "" {
		return nil, nil
	}
	if err := json.Unmarshal([]byte(deploy), &res); err != nil {
		return res, err
	}
	return res, nil
}

func ConvertLabelsStr2DO(labelsStr string) (LabelGroups, error) {
	labels := make(map[string][]string)
	if labelsStr == "" {
		return labels, nil
	}
	if err := json.Unmarshal([]byte(labelsStr), &labels); err != nil {
		return labels, err
	}
	return labels, nil
}

func ConvertLabelsDO2Str(labels LabelGroups) (string, error) {
	if labels == nil {
		return "", nil
	}
	labelBytes, err := json.Marshal(labels)
	if err != nil {
		return "", err
	}
	return string(labelBytes), nil
}

func ConvertChainDebugStateToInt(state *ChainDebugState) (int32, error) {
	if state == nil {
		return -1, stderr.Error("chain state should not be nil")
	}
	return state.Code, nil
}

func ConvertLabelPO2DO(labelPOs []*generated.AppletLabel) LabelGroups {
	res := make(map[string][]string)
	for _, l := range labelPOs {
		if v, ok := res[l.GroupName]; ok {
			res[l.GroupName] = append(v, l.Name)
		} else {
			res[l.GroupName] = []string{l.Name}
		}
	}
	return res
}

func ConvertLabelDO2PO(label LabelGroups, creator string) []*generated.AppletLabel {
	res := make([]*generated.AppletLabel, 0)
	for k, v := range label {
		for _, l := range v {
			res = append(res, &generated.AppletLabel{
				GroupName: k,
				Name:      l,
				Creator:   creator,
			})
		}
	}
	return res
}
