package models

import "transwarp.io/applied-ai/applet-backend/pkg/models/generated"

func CvtDynamicToWidgetDesc(model *generated.CustomWidget) *DynamicWidgetDesc {
	return &DynamicWidgetDesc{
		Type: DynamicWidgetTypeCustomWidget.Desc,
		Name: model.Name,
		Key:  model.ID,
	}
}

func BatchCvtDynamicToWidgetDesc(models []*generated.CustomWidget) []*DynamicWidgetDesc {
	res := make([]*DynamicWidgetDesc, 0)
	for _, m := range models {
		res = append(res, CvtDynamicToWidgetDesc(m))
	}
	return res
}
