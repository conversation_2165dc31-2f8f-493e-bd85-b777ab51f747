// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameChainDebugHistory = "chain_debug_histories"

// ChainDebugHistory mapped from table <chain_debug_histories>
type ChainDebugHistory struct {
	ID              string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                               // ID
	ChainID         string    `gorm:"column:chain_id;comment:应用链ID" json:"chain_id"`                                           // 应用链ID
	State           int32     `gorm:"column:state;not null;comment:调试状态，0未调试，1调试中，2成功，3失败，4取消" json:"state"`                   // 调试状态，0未调试，1调试中，2成功，3失败，4取消
	Creator         string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                      // 创建人
	ProjectID       string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                               // 项目ID
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	UpdatedTime     time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
	DebugMessage    string    `gorm:"column:debug_message;comment:EventStream 中 type 为'debug'的数据" json:"debug_message"`        // EventStream 中 type 为'debug'的数据
	DebugName       string    `gorm:"column:debug_name;comment:用户输入（textInput或fileName）作为单条调试记录的名称" json:"debug_name"`         // 用户输入（textInput或fileName）作为单条调试记录的名称
	ChainSnapshotID int64     `gorm:"column:chain_snapshot_id;comment:应用链快照id" json:"chain_snapshot_id"`                       // 应用链快照id
	ChainSnapshot   string    `gorm:"column:chain_snapshot;comment:应用链快照详情" json:"chain_snapshot"`                             // 应用链快照详情
}

// TableName ChainDebugHistory's table name
func (*ChainDebugHistory) TableName() string {
	return TableNameChainDebugHistory
}
