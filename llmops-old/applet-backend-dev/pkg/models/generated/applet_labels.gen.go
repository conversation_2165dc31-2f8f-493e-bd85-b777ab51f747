// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAppletLabel = "applet_labels"

// AppletLabel mapped from table <applet_labels>
type AppletLabel struct {
	ID          string    `gorm:"column:id;primaryKey;comment:应用链ID" json:"id"`                                            // 应用链ID
	GroupName   string    `gorm:"column:group_name;not null;comment:标签组名字" json:"group_name"`                              // 标签组名字
	Name        string    `gorm:"column:name;not null;comment:标签名字" json:"name"`                                           // 标签名字
	Creator     string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                      // 创建人
	ProjectID   string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                               // 项目ID
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName AppletLabel's table name
func (*AppletLabel) TableName() string {
	return TableNameAppletLabel
}
