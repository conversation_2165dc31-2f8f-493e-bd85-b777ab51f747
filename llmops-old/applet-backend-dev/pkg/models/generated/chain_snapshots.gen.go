// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

const TableNameChainSnapshot = "chain_snapshots"

// ChainSnapshot mapped from table <chain_snapshots>
type ChainSnapshot struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:ID，和ChainDebugHistory的ChainSnapshot关联" json:"id"` // ID，和ChainDebugHistory的ChainSnapshot关联
	ChainDetail string `gorm:"column:chain_detail;comment:应用链快照" json:"chain_detail"`                                           // 应用链快照
	Base        string `gorm:"column:base;comment:用于service version info 的 base 信息" json:"base"`                                // 用于service version info 的 base 信息
	ChainID     string `gorm:"column:chain_id;comment:应用链id" json:"chain_id"`                                                   // 应用链id
}

// TableName ChainSnapshot's table name
func (*ChainSnapshot) TableName() string {
	return TableNameChainSnapshot
}
