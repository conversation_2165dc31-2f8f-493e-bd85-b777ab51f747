// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAppletExperiment = "applet_experiments"

// AppletExperiment mapped from table <applet_experiments>
type AppletExperiment struct {
	ChainID      string    `gorm:"column:chain_id;primaryKey;comment:相关的应用链ID" json:"chain_id"`                               // 相关的应用链ID
	Name         string    `gorm:"column:name;not null;comment:应用体验名称" json:"name"`                                           // 应用体验名称
	ImageURL     string    `gorm:"column:image_url;not null;comment:封面的图片地址" json:"image_url"`                                // 封面的图片地址
	Introduction string    `gorm:"column:introduction;not null;comment:开场白，应用介绍" json:"introduction"`                         // 开场白，应用介绍
	Examples     string    `gorm:"column:examples;not null;comment:常用问题示例" json:"examples"`                                   // 常用问题示例
	LabelInfo    string    `gorm:"column:label_info;not null;default:{};comment:标签，map结构" json:"label_info"`                  // 标签，map结构
	Creator      string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                        // 创建人
	ProjectID    string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                 // 项目ID
	Status       string    `gorm:"column:status;not null;default:Published;comment:'应用体验是否发布'" json:"status"`                 // '应用体验是否发布'
	ServiceInfo  string    `gorm:"column:service_info;not null;comment:应用体验部署后的服务信息" json:"service_info"`                     // 应用体验部署后的服务信息
	CreatedBy    string    `gorm:"column:created_by;not null;default:applet-chain;comment:'创建途径是智能体还是应用链'" json:"created_by"` // '创建途径是智能体还是应用链'
	CreatedTime  time.Time `gorm:"column:created_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"`   // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"`   // 更新时间
}

// TableName AppletExperiment's table name
func (*AppletExperiment) TableName() string {
	return TableNameAppletExperiment
}
