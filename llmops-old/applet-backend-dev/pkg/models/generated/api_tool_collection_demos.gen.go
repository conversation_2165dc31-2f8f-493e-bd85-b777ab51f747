// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAPIToolCollectionDemo = "api_tool_collection_demos"

// APIToolCollectionDemo mapped from table <api_tool_collection_demos>
type APIToolCollectionDemo struct {
	ID          string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                               // ID
	ProjectID   string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                               // 项目ID
	Name        string    `gorm:"column:name;not null;comment:工具集示例名称" json:"name"`                                        // 工具集示例名称
	Desc        string    `gorm:"column:desc;not null;comment:工具集示例描述" json:"desc"`                                        // 工具集示例描述
	MetaType    string    `gorm:"column:meta_type;not null;comment:元信息类型 json/yaml" json:"meta_type"`                      // 元信息类型 json/yaml
	MetaInfo    []byte    `gorm:"column:meta_info;comment:元信息详情" json:"meta_info"`                                         // 元信息详情
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName APIToolCollectionDemo's table name
func (*APIToolCollectionDemo) TableName() string {
	return TableNameAPIToolCollectionDemo
}
