// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAPIToolCollection = "api_tool_collections"

// APIToolCollection mapped from table <api_tool_collections>
type APIToolCollection struct {
	ID              string     `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                                  // ID
	ProjectID       string     `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                                  // 项目ID
	Name            string     `gorm:"column:name;not null;comment:工具集名称" json:"name"`                                                             // 工具集名称
	Creator         string     `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                                         // 创建人
	Desc            string     `gorm:"column:desc;not null;comment:工具集描述" json:"desc"`                                                             // 工具集描述
	ReleasedState   string     `gorm:"column:released_state;not null;default:un_released;comment:是否发布，released，un_released" json:"released_state"` // 是否发布，released，un_released
	MetaType        string     `gorm:"column:meta_type;not null;comment:元信息类型 json/yaml" json:"meta_type"`                                         // 元信息类型 json/yaml
	Type            string     `gorm:"column:type;not null;default:common;comment:工具集类型 common/template" json:"type"`                              // 工具集类型 common/template
	MetaInfo        []byte     `gorm:"column:meta_info;comment:元信息详情" json:"meta_info"`                                                            // 元信息详情
	Headers         string     `gorm:"column:headers;not null;comment: 请求头，map类型" json:"headers"`                                                  //  请求头，map类型
	BaseURL         string     `gorm:"column:base_url;not null;comment: 请求地址" json:"base_url"`                                                     //  请求地址
	LastPublishTime *time.Time `gorm:"column:last_publish_time;comment:最后一次发布时间" json:"last_publish_time"`                                         // 最后一次发布时间
	LogoURL         string     `gorm:"column:logo_url;not null;comment: 工具集logo" json:"logo_url"`                                                  //  工具集logo
	ProxyInfo       string     `gorm:"column:proxy_info;not null;comment: 代理配置" json:"proxy_info"`                                                 //  代理配置
	CreateTime      time.Time  `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                      // 创建时间
	UpdatedTime     time.Time  `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"`                    // 更新时间
}

// TableName APIToolCollection's table name
func (*APIToolCollection) TableName() string {
	return TableNameAPIToolCollection
}
