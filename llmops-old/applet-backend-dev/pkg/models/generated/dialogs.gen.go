// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameDialog = "dialogs"

// Dialog mapped from table <dialogs>
type Dialog struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	AppID            string    `gorm:"column:app_id" json:"app_id"`
	AppName          string    `gorm:"column:app_name" json:"app_name"`
	ProjectID        string    `gorm:"column:project_id" json:"project_id"`
	ChatID           string    `gorm:"column:chat_id;not null" json:"chat_id"`
	User             string    `gorm:"column:user" json:"user"`
	LatestQuestionID int64     `gorm:"column:latest_question_id;comment:会话最新的提问id" json:"latest_question_id"`                   // 会话最新的提问id
	LatestAnswerID   int64     `gorm:"column:latest_answer_id;comment:会话最新的回答id" json:"latest_answer_id"`                       // 会话最新的回答id
	CreateTime       time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`   // 创建时间
	UpdatedTime      time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName Dialog's table name
func (*Dialog) TableName() string {
	return TableNameDialog
}
