// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameChainTemplate = "chain_templates"

// ChainTemplate mapped from table <chain_templates>
type ChainTemplate struct {
	ID          string    `gorm:"column:id;primaryKey;comment:模板ID" json:"id"`                                             // 模板ID
	Name        string    `gorm:"column:name;not null;comment:模板名称" json:"name"`                                           // 模板名称
	Desc        string    `gorm:"column:desc;not null;comment:模板描述" json:"desc"`                                           // 模板描述
	Template    string    `gorm:"column:template;comment:模板详情(算子编排信息)" json:"template"`                                    // 模板详情(算子编排信息)
	CreatedTime time.Time `gorm:"column:created_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"` // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
}

// TableName ChainTemplate's table name
func (*ChainTemplate) TableName() string {
	return TableNameChainTemplate
}
