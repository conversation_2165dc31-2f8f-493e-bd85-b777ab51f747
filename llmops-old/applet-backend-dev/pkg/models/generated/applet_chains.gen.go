// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package generated

import (
	"time"
)

const TableNameAppletChain = "applet_chains"

// AppletChain mapped from table <applet_chains>
type AppletChain struct {
	ID               string    `gorm:"column:id;primaryKey;comment:应用链ID" json:"id"`                                                           // 应用链ID
	Name             string    `gorm:"column:name;not null;comment:应用链名字" json:"name"`                                                         // 应用链名字
	Creator          string    `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                                     // 创建人
	LastDebugState   int32     `gorm:"column:last_debug_state;not null;comment:最新一次调试状态，0未调试，1调试中，2成功，3失败" json:"last_debug_state"`            // 最新一次调试状态，0未调试，1调试中，2成功，3失败
	Label            string    `gorm:"column:label;comment:标签，map结构" json:"label"`                                                             // 标签，map结构
	Chain            string    `gorm:"column:chain;comment:算子&依赖关系编排" json:"chain"`                                                            // 算子&依赖关系编排
	Desc             string    `gorm:"column:desc;not null;comment:应用链描述" json:"desc"`                                                         // 应用链描述
	DebugInfo        string    `gorm:"column:debug_info;not null;comment:调试信息" json:"debug_info"`                                              // 调试信息
	ContainsSubChain int32     `gorm:"column:contains_sub_chain;not null;comment:是否包含子链，2不包含，1包含" json:"contains_sub_chain"`                   // 是否包含子链，2不包含，1包含
	ProjectID        string    `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                              // 项目ID
	CreateTime       time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                  // 创建时间
	UpdatedTime      time.Time `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:数据库记录更新时间" json:"updated_time"`           // 数据库记录更新时间
	ChainUpdatedTime time.Time `gorm:"column:chain_updated_time;not null;default:CURRENT_TIMESTAMP;comment:应用链更新时间" json:"chain_updated_time"` // 应用链更新时间
	AssetType        int32     `gorm:"column:asset_type;comment:资产类型[0共享资产，1内嵌资产]" json:"asset_type"`                                          // 资产类型[0共享资产，1内嵌资产]
	UsageType        string    `gorm:"column:usage_type;not null;default:Common;comment:用途，LLM/Common" json:"usage_type"`                      // 用途，LLM/Common
	CreatedType      string    `gorm:"column:created_type;not null;default:Applet-Chain;comment:创建类型" json:"created_type"`                     // 创建类型
	SourceInfo       string    `gorm:"column:source_info;not null;comment:来源信息" json:"source_info"`                                            // 来源信息
	MetricsInfo      string    `gorm:"column:metrics_info;not null;comment:打点信息，访问次数等" json:"metrics_info"`                                    // 打点信息，访问次数等
	AssistantInfo    string    `gorm:"column:assistant_info;comment:智能体相关信息" json:"assistant_info"`                                            // 智能体相关信息
	ExamplesInfo     string    `gorm:"column:examples_info;comment:图片地址、开场白、引导示例" json:"examples_info"`                                        // 图片地址、开场白、引导示例
	SpaceInfo        string    `gorm:"column:space_info;default:{};comment:机构空间所属行业及首页精选等相关信息" json:"space_info"`                              // 机构空间所属行业及首页精选等相关信息
}

// TableName AppletChain's table name
func (*AppletChain) TableName() string {
	return TableNameAppletChain
}
