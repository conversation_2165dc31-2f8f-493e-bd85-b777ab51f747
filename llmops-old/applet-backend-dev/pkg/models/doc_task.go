package models

import (
	"context"

	"transwarp.io/aip/llmops-common/pb"
)

const (
	TableNameDocTask = "doc_tasks"
)

type DocTask struct {
	Ctx                        context.Context                `gorm:"-"`
	Cancel                     context.CancelFunc             `gorm:"-"`
	DocumentId                 string                         `gorm:"column:doc_id;primaryKey;type:varchar(191)"`
	KnowledgeBaseId            string                         `gorm:"column:kb_id"`
	DocProcessingConfig        *pb.DocProcessingConfig        `gorm:"column:dpc;serializer:json"`
	DocumentProcessingProgress *pb.DocumentProcessingProgress `gorm:"column:prog;serializer:json"`
}

func (*DocTask) TableName() string {
	return TableNameDocTask
}
