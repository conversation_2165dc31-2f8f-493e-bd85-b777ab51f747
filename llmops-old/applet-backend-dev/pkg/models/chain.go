package models

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/influxdata/kapacitor/uuid"
	"github.com/moul/http2curl"
	"github.com/thoas/go-funk"
	"io"
	"net/http"
	"strings"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type ChainType int32

const (
	ChainTypeUnKnow     ChainType = 0
	ChainTypeTemplate   ChainType = 1
	ChainTypeUserCreate ChainType = 2

	ChainTypeTemplateStr   string = "Template"
	ChainTypeUserCreateStr string = "UserCrated"
)

type ChainDebugState struct {
	State string `json:"state"`
	Code  int32  `json:"code"`
}

var (
	ChainDebugStateInit = &ChainDebugState{
		State: "init",
		Code:  0,
	}
	ChainDebugStateRunning = &ChainDebugState{
		State: "running",
		Code:  1,
	}
	ChainDebugStateSuccess = &ChainDebugState{
		State: "success",
		Code:  2,
	}
	ChainDebugStateFailed = &ChainDebugState{
		State: "failed",
		Code:  3,
	}
	ChainDebugStateCanceled = &ChainDebugState{
		State: "canceled",
		Code:  4,
	}
)

func CodeTODebugState(stateCode int32) *ChainDebugState {
	switch stateCode {
	case 0:
		return ChainDebugStateInit
	case 1:
		return ChainDebugStateRunning
	case 2:
		return ChainDebugStateSuccess
	case 3:
		return ChainDebugStateFailed
	case 4:
		return ChainDebugStateCanceled
	}
	return nil
}
func StateStrTODebugState(stateStr string) (*ChainDebugState, error) {
	switch stateStr {
	case "init":
		return ChainDebugStateInit, nil
	case "running":
		return ChainDebugStateRunning, nil
	case "success":
		return ChainDebugStateSuccess, nil
	case "failed":
		return ChainDebugStateFailed, nil
	case "canceled":
		return ChainDebugStateCanceled, nil
	}
	err := stderr.Error("the str of debug state is error")
	return nil, err
}

// const (
//	// ChainDebugStateUnKnow 未知
//	ChainDebugStateUnKnow ChainDebugState = -1
//	// ChainDebugStateInit 未调试
//	ChainDebugStateInit ChainDebugState = 0
//	// ChainDebugStateRunning 运行中
//	ChainDebugStateRunning ChainDebugState = 1
//	// ChainDebugStateSuccess 调试成功
//	ChainDebugStateSuccess ChainDebugState = 2
//	// ChainDebugStateFailed 调试失败
//	ChainDebugStateFailed ChainDebugState = 3
//	// ChainDebugStateCanceled 调试中止
//	ChainDebugStateCanceled ChainDebugState = 4
// )

type ChainDeployState int32

const (
	// ChainStateOffline 下线
	ChainStateOffline ChainDeployState = 0
	// ChainStateProgressing 上线中
	ChainStateProgressing ChainDeployState = 1
	// ChainStateOnline 上线
	ChainStateOnline ChainDeployState = 2
	// ChainStateFailed 上线失败
	ChainStateFailed ChainDeployState = 3

	ChainContainsSubChain    = 1
	ChainNotContainsSubChain = 2
)

const (
	ChainTemplateKeyLLM = "ChainTemplateKeyLLM"
)

const (
	ChainParamSubChain = "SubChainDetail"
)

const (
	ChainUsageTypeCommon = "Common"
	ChainUsageTypeLLM    = "LLM"
)

const UserIDTemplateCreated = "template@"

type AppletChainDO struct {
	Base        AppletChainBaseDO `json:"base"`         // 基础信息，如name、label等
	ChainDetail *widgets.Chain    `json:"chain_detail"` // 算子编排信息
}

type AppletType = string // 应用的类型
const (
	AppletTypeChain            = "Applet-Chain"     // 应用链
	AppletTypeAssistant        = "Applet-Assistant" // 智能助手
	AppletTypeExternalRegister = "Applet-External"  // 用户通过 web url 注册的外部应用
	AppletTypeCustomDeployed   = "Applet-Custom"    // 用户通过服务管理自行部署的
)

type AppletUsageType = string // 应用交互类型
const (
	AppletUsageTypeLLM    = "LLM"    // 包含LLM大模型的对话型应用
	AppletUsageTypeCommon = "Common" // 其他不含LLM大模型类型的应用
)

// AppletChainBaseDO 基础属性，列表页查询的时候用到
type AppletChainBaseDO struct {
	ID               string           `json:"id"`
	Name             string           `json:"name"`
	Creator          string           `json:"creator"`
	Desc             string           `json:"desc"`
	Labels           LabelGroups      `json:"labels"`
	LastDebugState   ChainDebugState  `json:"debug_state"`
	ContainsSubChain bool             `json:"contains_sub_chain"`
	ProjectID        string           `json:"projectID"`
	UpdateTime       int64            `json:"update_time"`
	CreateTime       int64            `json:"create_time"`
	AssetType        string           `json:"asset_type"`
	CreatedType      AppletType       `json:"created_type"`
	ExperimentInfo   *ExperimentInfo  `json:"experiment_info"` // 体验信息
	AssistantInfo    *AssistantInfo   `json:"assistant_info"`  // 和智能体有关的信息
	SourceInfo       *ChainSourceInfo `json:"source_info"`
	MetricsInfo      ChainMetricsInfo `json:"metrics_info"` // 打点信息
	SpaceInfo        pb.SpaceInfo     `json:"space_info" description:"机构空间所属行业及首页精选等相关信息"`
	UsageType        AppletUsageType  `json:"usage_type"` // 用途
	// 额外信息,数据库不存在
	ExtraInfo
}

type ExtraInfo struct {
	Published    bool            `json:"published"`
	ServiceInfo  *AppServiceInfo `json:"service_info" description:"相关联的服务信息"`
	EnableAccess bool            `json:"enable_access,omitempty" description:"当前用户是否有权限访问该应用"`
}

type ChainSourceInfo struct {
	SourceChainID   string `json:"source_chain_id"`
	SourceProjectID string `json:"source_project_id"`
}

type ChainMetricsInfo struct {
	VisitTimes   int64 `json:"visit_times"`   // 访问次数
	CloneTimes   int64 `json:"clone_times"`   // 克隆次数
	ExecuteTimes int64 `json:"execute_times"` // 执行次数
}

func (c *ChainMetricsInfo) Visit(times int64) {
	c.VisitTimes += times
}

func (c *ChainMetricsInfo) Clone(times int64) {
	c.CloneTimes += times
}

func (c *ChainMetricsInfo) Execute(times int64) {
	c.ExecuteTimes += times
}

func (c *ChainMetricsInfo) Marshal() string {
	bytes, _ := json.Marshal(c)
	return string(bytes)
}

type DialogInfo struct {
	DialLogs []Dialog `json:"dialogs"`
}

type Dialog struct {
	Ask    string `json:"ask"`
	Answer string `json:"answer"`
}

type ChainDebugInfo struct {
	Params     *WidgetParams `json:"params"`      // 调用参数
	Result     *ChainResult  `json:"result"`      // 应用链结果
	DialogInfo *DialogInfo   `json:"dialog_info"` // 对话历史信息
}

func (c ChainDebugInfo) ToString() (string, error) {
	bs, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(bs), nil
}

type DeployInfo struct {
	DeployName string `json:"deploy_name"`
	API        string `json:"api"`
}

func (a AppletChainDO) ContainsSubChain() bool {
	if a.ChainDetail != nil {
		return a.ChainDetail.ContainsSubChain()
	}
	return a.Base.ContainsSubChain
}

// AllNodeHasDetail 所有节点都有子链
func (a AppletChainDO) AllNodeHasDetail() bool {
	for _, c := range a.ChainDetail.Nodes {
		_, err := c.GetWidgetDetail()
		if err != nil {
			return false
		}
	}
	return true
}

type ChainQueryTemplate map[string]interface{}

// GetQueryTemplate 获取用户输入节点
func (a AppletChainDO) GetQueryTemplate(useStdFormat bool) (ChainQueryTemplate, error) {
	ws := make(map[string]interface{})
	chainDetail := a.ChainDetail
	if chainDetail == nil {
		return nil, stderr.Errorf("chain detail is nil")
	}

	queryEx := "${your_question}"
	paramsEx := helper.EmptyRsp{}
	historyEx := []*triton.QAItem{{Q: "Q1", A: "A1"}, {Q: "Q2", A: "A2"}}
	filesEx := engine.GetExampleSFSFiles()

	for _, n := range chainDetail.Nodes {
		widgetDetail, err := n.GetWidgetDetail()
		if err != nil {
			return nil, stderr.Wrap(err, "get widget detail err")
		}

		if !useStdFormat {
			switch widgetDetail.Id {
			case widgets.WidgetKeyChatHistory:
				ws[widgets.ParamIDChatInput] = historyEx
			case widgets.WidgetKeyFileInput:
				ws[widgets.ParamIDFileInput] = filesEx
			case widgets.WidgetKeyUpstreamInput: //json输入
				ws[widgets.ParamIDJsonInput] = paramsEx
			case widgets.WidgetKeyTextInput:
				ws[widgets.ParamIDTextInput] = queryEx
			}
		} else {
			switch widgetDetail.Id {
			case widgets.WidgetKeyChatHistory:
				ws["history"] = historyEx
			case widgets.WidgetKeyFileInput:
				ws["files"] = filesEx
			case widgets.WidgetKeyUpstreamInput: //json输入
				ws["params"] = paramsEx
			case widgets.WidgetKeyTextInput:
				ws["query"] = queryEx
			}
		}

	}
	return ws, nil
}

type ChainDependencyInfo struct {
	DependencyType   ChainDependencyType
	DependencyID     string
	DependencyDetail map[string]string // 依赖详情，预留字段
}

type ChainDependencies struct {
	APIToolDependencies []ChainDependencyInfo // api tool 依赖
}

func GetDependencyFromChainDetail(chain *widgets.Chain) (*ChainDependencies, error) {
	APITools := make([]ChainDependencyInfo, 0)
	// 去重用
	IDMap := make(map[string]struct{}, 0)
	for _, node := range chain.Nodes {
		values := node.Values
		oriWidgetKey := node.WidgetDetail.Id
		switch oriWidgetKey {
		case widgets.WidgetKeyAgent:
			llmAgentConfig := new(agent_definition.LLMAgentConfig)
			if err := helper.AnyToStruct(values, llmAgentConfig); err != nil {
				return nil, stderr.Wrap(err, "node value to agent error , node detail :%v,values :%v", node, values)
			}
			dependencyMap := getDependenciesFromAgent(llmAgentConfig)
			if apis, ok := dependencyMap[ChainDependencyTypeAPITool]; ok {
				for _, apiID := range apis {
					if _, ok := IDMap[apiID]; !ok {
						IDMap[apiID] = struct{}{}
						APITools = append(APITools, ChainDependencyInfo{
							DependencyType: ChainDependencyTypeAPITool,
							DependencyID:   apiID,
						})
					}
				}
			}
		case widgets.WidgetKeyTextKnowledgeSearch:
			// TODO
		}
	}
	return &ChainDependencies{APIToolDependencies: APITools}, nil
}

func (a AppletChainDO) GetDependency() (*ChainDependencies, error) {
	return GetDependencyFromChainDetail(a.ChainDetail)
}

func (a AppletChainDO) getInputsAndOutputs() (inputNodes []widgets.ChainNode, outputNodes []widgets.ChainNode) {
	for _, node := range a.ChainDetail.Nodes {
		if node.WidgetDetail.Group == widgets.WidgetGroupInput {
			inputNodes = append(inputNodes, node)
			continue
		}
		if node.WidgetDetail.Group == widgets.WidgetGroupOutput {
			outputNodes = append(outputNodes, node)
			continue
		}
	}
	return
}

func (a AppletChainDO) IsChunkingChain() bool {
	if a.ChainDetail == nil {
		return false
	}
	inputs, outputs := a.getInputsAndOutputs()
	onlyFileInput := len(inputs) == 1 && inputs[0].WidgetDetail.Id == widgets.WidgetKeyFileInput
	onlyChunksOutput := len(outputs) == 1 && outputs[0].WidgetDetail.Id == widgets.WidgetKeyChunksOutput
	return onlyFileInput && onlyChunksOutput
}

func (a AppletChainDO) IsRecallChain() bool {
	if a.ChainDetail == nil {
		return false
	}
	inputs, outputs := a.getInputsAndOutputs()
	onlyTextInput := len(inputs) == 1 && inputs[0].WidgetDetail.Id == widgets.WidgetKeyTextInput
	onlyChunksOutput := len(outputs) == 1 && outputs[0].WidgetDetail.Id == widgets.WidgetKeyChunksOutput
	return onlyTextInput && onlyChunksOutput
}

func getDependenciesFromAgent(agent *agent_definition.LLMAgentConfig) map[ChainDependencyType][]string {
	// api tool
	res := make(map[ChainDependencyType][]string)
	IDs := make([]string, 0)
	for _, v := range agent.APICollections {
		IDs = append(IDs, v.ID)
	}
	res[ChainDependencyTypeAPITool] = IDs
	return res
}

func updateSubChainInfo(chainDO *AppletChainDO, parentChainID string) *AppletChainDO {
	res := &AppletChainDO{}
	base := chainDO.Base
	// base.ID = fmt.Sprintf("subchain_%s_%s", parentChainID, base.ID)
	base.ID = GenSubChainNodeID(parentChainID, base.ID)
	res.Base = base

	chainDetail := *chainDO.ChainDetail
	newNodes := make([]widgets.ChainNode, 0)
	newEdges := make([]widgets.ChainEdge, 0)
	for _, n := range chainDO.ChainDetail.Nodes {
		// newID := fmt.Sprintf("subchain_%s_%s", parentChainID, n.Id)
		newID := GenSubChainNodeID(parentChainID, n.Id)
		newNodes = append(newNodes, widgets.ChainNode{
			Id:               newID,
			Name:             n.Name,
			WidgetId:         n.WidgetId,
			WidgetDetail:     n.WidgetDetail,
			UI:               n.UI,
			Values:           n.Values,
			SubChainBaseInfo: n.SubChainBaseInfo,
		})
	}

	for _, e := range chainDO.ChainDetail.Edges {
		// newID := fmt.Sprintf("subchain_%s_%s", parentChainID, e.Id)
		newID := GenSubChainNodeID(parentChainID, e.Id)
		oriSourceID := e.SourceNode
		oriTargetID := e.TargetNode
		// newSourceID := fmt.Sprintf("subchain_%s_%s", parentChainID, oriSourceID)
		newSourceID := GenSubChainNodeID(parentChainID, oriSourceID)
		// newTargetID := fmt.Sprintf("subchain_%s_%s", parentChainID, oriTargetID)
		newTargetID := GenSubChainNodeID(parentChainID, oriTargetID)
		newSourceParam := strings.ReplaceAll(e.SourceParam, oriSourceID, newSourceID)
		newTargetParam := strings.ReplaceAll(e.TargetParam, oriTargetID, newTargetID)
		newEdges = append(newEdges, widgets.ChainEdge{
			Id:          newID,
			SourceNode:  newSourceID,
			SourceParam: newSourceParam,
			TargetNode:  newTargetID,
			TargetParam: newTargetParam,
		})
	}
	chainDetail.Nodes = newNodes
	chainDetail.Edges = newEdges
	res.ChainDetail = &chainDetail
	return res
}

func (a AppletChainDO) ContainsGotoNode() bool {
	if a.ChainDetail == nil {
		return false
	}
	return a.ChainDetail.ContainsGOTONode()
}

func (a AppletChainDO) RemoveGotoWidgetEdge() (*AppletChainDO, error) {
	res := &a
	if res.ChainDetail == nil {
		return res, nil
	}
	// nodeA ID -> nodeB ID
	nodeToNextNodeMap := make(map[string]string)
	// nodeA ID -> nodeA
	nodeIDMap := a.GetNodeMap()
	removeEdgeIDs := make([]string, 0)
	for _, edge := range a.ChainDetail.Edges {
		source := edge.SourceNode
		target := edge.TargetNode
		nodeToNextNodeMap[source] = target
		if nodeIDMap[source] != nil && nodeIDMap[source].WidgetId == widgets.WidgetKeyGoTo {
			removeEdgeIDs = append(removeEdgeIDs, edge.Id)
		}
	}
	newEdges := make([]widgets.ChainEdge, 0)
	// 去除goto算子到下一个算子的边
	if len(removeEdgeIDs) != 0 {
		for _, edge := range a.ChainDetail.Edges {
			if funk.ContainsString(removeEdgeIDs, edge.Id) {
				continue
			}
			newEdges = append(newEdges, edge)
		}
	}
	newNodes := make([]widgets.ChainNode, 0)
	for _, n := range a.ChainDetail.Nodes {
		if n.WidgetDetail != nil && n.WidgetDetail.Id == widgets.WidgetKeyGoTo {
			if nextNode, ok := nodeToNextNodeMap[n.Id]; !ok {
				return res, stderr.Internal.Error("goto node has no next node")
			} else {
				n.Values[widgets.GotoNextNodeParamKey] = nextNode
			}
		}
		newNodes = append(newNodes, n)
	}
	res.ChainDetail.Edges = newEdges
	res.ChainDetail.Nodes = newNodes

	return res, nil
}

// ExpandSubChain 展开子链
func (a AppletChainDO) ExpandSubChain() (*AppletChainDO, error) {
	if a.ChainDetail == nil {
		return nil, stderr.Internal.Errorf("chain detail is nil")
	}
	if !a.ChainDetail.ContainsSubChain() {
		return nil, stderr.Internal.Errorf("chain :%v not contains sub chain", a.Base.Name)
	}
	// 子链算子
	subChainWidgets := make(map[string]widgets.ChainNode, 0)
	// 子链基础信息
	subChainNodeBasic := make(map[string]AppletChainBaseDO, 0)
	// 子链展开节点
	subChainNodes := make(map[string]*widgets.Chain, 0)
	// 子链展开后合并到父链
	newWidgetNodes := make(map[string]widgets.ChainNode, 0)
	for _, n := range a.ChainDetail.Nodes {
		if n.WidgetDetail.Id == widgets.WidgetKeySubChain {
			subChainWidgets[n.Id] = n
		} else {
			newWidgetNodes[n.Id] = n
		}
	}
	for k, s := range subChainWidgets {
		subChainValue, ok := s.Values[ChainParamSubChain]
		if !ok {
			return nil, stderr.Internal.Error("sub chain miss param :%v", ChainParamSubChain)
		}
		var chainDO *AppletChainDO
		bytes, err := json.Marshal(subChainValue)
		if err != nil {
			return nil, err
		}
		if err = json.Unmarshal(bytes, &chainDO); err != nil {
			if err != nil {
				return nil, err
			}
		}
		newChain := updateSubChainInfo(chainDO, k)
		subChainNodes[k] = newChain.ChainDetail
		subChainNodeBasic[k] = newChain.Base
	}
	// 校验子链
	for _, c := range subChainNodes {
		containsCommonNode, err := c.ContainsCommonNode()
		if err != nil {
			return nil, err
		}
		if !containsCommonNode {
			return nil, stderr.Internal.Error("meaningless sub chain :%v", c)
		}
	}
	for k, v := range subChainNodes {
		subChainBasic, ok := subChainNodeBasic[k]
		if !ok {
			return nil, stderr.Internal.Error("sub chain node err,no sub chain :%v", k)
		}
		for _, n := range v.Nodes {
			// widget, err := n.GetWidgetDetail()
			// if err != nil {
			//	return nil, err
			// }
			// 子链中的输入和输出算子不应被合并入父链
			// if widget.IsInputNode() || widget.IsOutputNode() {
			//	continue
			// }
			n.SubChainBaseInfo = &widgets.SubChainBaseInfo{
				SubChainName:     subChainBasic.Name,
				SubChainWidgetID: k,
			}
			newWidgetNodes[n.Id] = n
		}
	}
	// 边
	newEdges := make([]widgets.ChainEdge, 0)
	// 子链里面的edge复制到父链中
	for _, v := range subChainNodes {
		for _, e := range v.Edges {
			// 子链中的边两端需要存在
			if _, ok := newWidgetNodes[e.SourceNode]; !ok {
				continue
			}
			if _, ok := newWidgetNodes[e.TargetNode]; !ok {
				continue
			}
			newEdges = append(newEdges, e)
		}
	}
	// 连接父链和子链
	for _, edge := range a.ChainDetail.Edges {
		sourceNode := edge.SourceNode
		sourceParam := edge.SourceParam
		targetNodes := []string{edge.TargetNode}
		targetParams := []string{edge.TargetParam}

		// withSubChain := false
		//
		// 连接子链最后一个节点到父链
		if subChain, ok := subChainNodes[edge.SourceNode]; ok {
			// withSubChain = true
			lastNode, err := subChain.GetLastNode()
			if err != nil {
				return nil, err
			}
			lastWidget, err := lastNode.GetWidgetDetail()
			if err != nil {
				return nil, err
			}
			outPuts := lastWidget.GetOutPortParam()
			if len(outPuts) != 1 {
				return nil, stderr.Internal.Error("invalid sub chain,out put node param error")
			}
			sourceNode = lastNode.Id
			sourceParam = fmt.Sprintf("%s@@%s", lastNode.Id, outPuts[0].Define.Id)
		}
		// 连接父链到子链的输入节点
		if subChain, ok := subChainNodes[edge.TargetNode]; ok {
			targetNodes = make([]string, 0)
			targetParams = make([]string, 0)
			targetParam := edge.TargetParam
			// target param : targetNodeID@@subChainNodeID##subChainParam
			if len(strings.Split(targetParam, "@@")) != 2 {
				return nil, stderr.Internal.Error("invalid param :%v", targetParam)
			}
			nodeSplits := strings.Split(targetParam, "@@")
			if len(nodeSplits) != 2 {
				return nil, stderr.Internal.Error("invalid param :%v", targetParam)
			}
			edgeSplits := strings.Split(nodeSplits[1], "##")
			if len(edgeSplits) != 2 {
				return nil, stderr.Internal.Error("invalid param :%v", targetParam)
			}
			// subID := fmt.Sprintf("subchain_%s_%s", nodeSplits[0], edgeSplits[0])
			subID := GenSubChainNodeID(nodeSplits[0], edgeSplits[0])
			// subParamID := splits[1]
			// 子链中的用户输入节点
			subNode, err := subChain.GetUserInPortNodeByID(subID)
			if err != nil {
				return nil, err
			}
			userInputParams, err := subNode.GetUserInPortParam()
			if err != nil {
				return nil, err
			}
			if len(userInputParams) == 0 {
				return nil, stderr.Internal.Error("node :%v has no user input param", subNode)
			}
			for _, p := range userInputParams {
				targetNodes = append(targetNodes, subNode.Id)
				targetParams = append(targetParams, fmt.Sprintf("%s@@%s", subNode.Id, p.Define.Id))
			}
			// nextNodes, err := subChain.GetNextNodes(subNode.Id)
			// if err != nil {
			//	return nil, err
			// }
			// if len(nextNodes) == 0 {
			//	return nil, stderr.Internal.Error("widget :%v next node", subNode.Id)
			// }
			// for _, n := range nextNodes {
			//	targetNodes = append(targetNodes, n.Node.Id)
			//	targetParams = append(targetParams, fmt.Sprintf("%s@@%s", n.Node.Id, n.Param.Define.Id))
			// }

		}
		for i, _ := range targetNodes {
			newEdges = append(newEdges, widgets.ChainEdge{
				Id:          edge.Id,
				SourceNode:  sourceNode,
				SourceParam: sourceParam,
				TargetNode:  targetNodes[i],
				TargetParam: targetParams[i],
			})
		}
	}
	nodes := make([]widgets.ChainNode, 0)
	for _, v := range newWidgetNodes {
		nodes = append(nodes, v)
	}
	chainDetail := &widgets.Chain{
		Nodes:    nodes,
		Edges:    newEdges,
		Viewport: a.ChainDetail.Viewport,
	}
	res := &AppletChainDO{
		Base:        a.Base,
		ChainDetail: chainDetail,
	}
	return res, nil
}

func (a AppletChainDO) ToSubChainWidget() (*widgets.Widget, error) {
	params := make([]widgets.WidgetParam, 0)
	// 用户输入转成in-put
	userInPortNodes, err := a.ChainDetail.GetUserInPortNodes()
	if err != nil {
		return nil, err
	}
	// 子链详情
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Preview:   true,
		Define: widgets.DynamicParam{
			Id:       ChainParamSubChain,
			Name:     "应用链",
			DataType: pb.DynamicParam_DATA_TYPE_STRING,
			Type:     pb.DynamicParam_TYPE_APPLET_CHAIN,
		},
	})
	for _, c := range userInPortNodes {
		ps, err := c.GetUserInPortParam()
		if err != nil {
			return nil, err
		}
		for _, p := range ps {
			// user input
			define := p.Define
			// define.Id = fmt.Sprintf("%s##%s", c.Id, define.Id)
			define.Id = GenSubChainParamID(c.Id, define.Id)
			params = append(params, widgets.WidgetParam{
				DataClass:   p.DataClass,
				Category:    widgets.ParamTypeNodeInPort,
				ParamLimits: widgets.SyncAnyLimits(),
				Define:      define,
			})
		}
	}

	// 传递子链id
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Define: widgets.DynamicParam{
			Id:           "SubChainId",
			DefaultValue: a.Base.ID,
			Hidden:       true,
			Disabled:     true,
		},
	})

	// 添加output
	params = append(params, widgets.WidgetParam{
		DataClass:   widgets.DataClassString,
		Category:    widgets.ParamTypeNodeOutPort,
		ParamLimits: widgets.AnyAnyLimits(),
		Define: widgets.DynamicParam{
			Id: "OutPut",
		},
	})
	if len(params) == 0 {
		return nil, stderr.Internal.Error("invalid sub chain,no user input widget")
	}

	baseDefine, err := widgets.WidgetFactoryImpl.GetWidgetDefine(widgets.WidgetKeySubChain)
	if err != nil {
		return nil, err
	}
	baseDefine.Params = params
	baseDefine.Desc = a.Base.Name
	baseDefine.Name = fmt.Sprintf("%s-%s", widgets.WidgetNameSubChain, a.Base.Name)

	return baseDefine, nil
}
func (a AppletChainDO) GetNodeMap() map[string]*widgets.ChainNode {
	res := make(map[string]*widgets.ChainNode, 0)
	for _, n := range a.ChainDetail.Nodes {
		realNode := n
		res[n.Id] = &realNode
	}
	return res
}
func (a AppletChainDO) GenAPI() (string, error) {
	api := ""
	// inputWidgets := make([]*widgets.Widget, 0)
	// for _, ch := range a.ChainDetail.Nodes {
	//	w := widgets.WidgetFactoryImpl.GetWidget(ch.WidgetId)
	//	if w == nil {
	//		return "", stderr.Internal.Error("invalid widget id :%v ", ch.WidgetId)
	//	}
	//	if w.Define().IsUserInputWidget() {
	//		inputWidgets = append(inputWidgets, w.Define())
	//	}
	// }
	m := map[string]interface{}{
		"chain_id": a.Base.ID,
		"run_id":   uuid.New().String(),
		"widget_param": map[string]interface{}{
			"${widgetKey1}": map[string]interface{}{
				"params": map[string]interface{}{
					"${widgetParam1}": map[string]interface{}{
						"value": "${inputValue}",
					},
				},
			},
		},
	}
	bs, err := json.Marshal(m)
	if err != nil {
		return "", stderr.Wrap(err, "http to curl json marshal err")
	}
	data := bytes.NewBuffer(bs)
	req, err := http.NewRequest("POST", "https://localhost:8080/gateway/applet/api/v1/applet/chains:run", data)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "application/json")
	if err != nil {
		return "", stderr.Wrap(err, "new http request err")
	}
	cmd, err := http2curl.GetCurlCommand(req)
	if err != nil {
		return "", stderr.Wrap(err, "http to curl command err")
	}
	api = cmd.String()
	api = strings.Replace(api, "localhost:8080", "${ip}:${port}", 1)
	return api, nil
}

func (a AppletChainDO) ToPO() (*generated.AppletChain, error) {
	labelStr, err := ConvertLabelsDO2Str(a.Base.Labels)
	if err != nil {
		return nil, err
	}
	containsSubChain := ChainNotContainsSubChain
	if a.Base.ContainsSubChain {
		containsSubChain = ChainContainsSubChain
	}
	examplesInfo := ""
	if a.Base.ExperimentInfo != nil {
		examplesInfo = helper.Struct2String(a.Base.ExperimentInfo.ExamplesInfo)
	}
	sourceInfo := ""
	if a.Base.SourceInfo != nil {
		bytes, err := json.Marshal(a.Base.SourceInfo)
		if err != nil {
			return nil, stderr.Wrap(err, "invalid chain source info :%v", a.Base.SourceInfo)
		}
		sourceInfo = string(bytes)
	}

	metricsInfoBytes, err := json.Marshal(a.Base.MetricsInfo)
	if err != nil {
		return nil, stderr.Wrap(err, "invalid chain metrics info :%v", a.Base.SourceInfo)
	}

	chain := &generated.AppletChain{
		ID:               a.Base.ID,
		Name:             a.Base.Name,
		Creator:          a.Base.Creator,
		Label:            labelStr,
		Desc:             a.Base.Desc,
		ContainsSubChain: int32(containsSubChain),
		ProjectID:        a.Base.ProjectID,
		LastDebugState:   a.Base.LastDebugState.Code,
		CreatedType:      a.Base.CreatedType,
		Chain:            helper.Struct2String(a.ChainDetail),
		AssistantInfo:    helper.Struct2String(a.Base.AssistantInfo),
		ExamplesInfo:     examplesInfo,
		SourceInfo:       sourceInfo,
		MetricsInfo:      string(metricsInfoBytes),
		SpaceInfo:        helper.Struct2String(a.Base.SpaceInfo),
		UsageType:        a.Base.UsageType,
	}
	if a.Base.UpdateTime > 0 {
		chain.ChainUpdatedTime = time.UnixMilli(a.Base.UpdateTime)
	}
	return chain, nil

}

func (a AppletChainDO) ToWidgetDesc() *DynamicWidgetDesc {
	return &DynamicWidgetDesc{
		Type:             DynamicWidgetTypeSubChain.Desc,
		Name:             a.Base.Name,
		Key:              a.Base.ID,
		ContainsSubChain: a.ContainsSubChain(),
	}
}

type BatchDeleteChainParam struct {
	ChainIDs []string `json:"ids"`
}

type CreatAppletChainParam struct {
	Name      string      `json:"name"`
	Desc      string      `json:"desc"`
	Labels    LabelGroups `json:"labels"`
	AssetType string      `json:"asset_type"`
}

type GetTickScriptResp struct {
	Script string `json:"script"`
}
type ChainParam struct {
	ChainID     string         `json:"chain_id"`
	ChainName   string         `json:"chain_name"`
	ChainDetail *widgets.Chain `json:"chain_detail"`
	//RunID   string       `json:"run_id"`
	Params    WidgetParams `json:"widget_param"`
	DebugName string       `json:"debug_name"`
}

type WidgetParams map[string]*WidgetParam

func (w WidgetParams) ToChainExecuteParam() map[string]interface{} {
	paramMap := make(map[string]interface{})
	for nodeID, widgetValue := range w {
		widgetParams := widgetValue.Params
		for param, paramValue := range widgetParams {
			// paramMap[fmt.Sprintf("%s##%s", nodeID, param)] = paramValue.Value
			paramMap[GenSubChainParamID(nodeID, param)] = paramValue.Value

		}
	}
	return paramMap
}

type ChainResult struct {
	Value interface{} `json:"value"`
}

type WidgetParam struct {
	WidgetID string                       `json:"widget_id"`
	Params   map[string]*WidgetParamValue `json:"params"`
}

type WidgetParamValue struct {
	Value interface{} `json:"value"`
}

type WidgetParamsV2 map[string]interface{}

type ChainDebugReq struct {
	ChainID     string         `json:"chain_id"`
	ChainName   string         `json:"chain_name"`
	ChainDetail *widgets.Chain `json:"chain_detail"` // 算子编排信息
	Params      WidgetParamsV2 `json:"widget_param"`
	//ExperienceModel bool           `json:"experience_model"`
}

type ChainRunReq struct {
	MlOpsSvcID string         `json:"mlops_svc_id"`
	ChainID    string         `json:"chain_id"`
	ChatId     string         `json:"chat_id"`
	Params     WidgetParamsV2 `json:"widget_param"`
}

func (w ChainDebugReq) ToChainParam(chain *widgets.Chain) (*ChainParam, error) {
	ps := make(map[string]*WidgetParam)

	debugName := ""
	currentPriority := 0

	for key, widgetValue := range w.Params {
		// splits := strings.Split(key, "##")
		// if len(splits) != 2 {
		//	return nil, stderr.Internal.Error("invalid key :%v", key)
		// }
		nodeID, err := GetNodeIDFromChainRunParam(key)
		if err != nil {
			return nil, err
		}
		paramID, err := GetParamIDFromChainRunParam(key)
		if err != nil {
			return nil, err
		}
		widget, err := chain.GetWidgetByNodeID(nodeID)
		if err != nil {
			return nil, err
		}
		if widget == nil {
			return nil, stderr.Internal.Error("get widget err, no widget :%v", nodeID)
		}

		// 根据 TextInput > JsonInput > FileInput 的优先级顺序更新 debugName
		err = updateDebugName(widgetValue, &currentPriority, &debugName, paramID, widget.Id)
		if err != nil {
			return nil, stderr.Internal.Error("update debugName err, for widget :%v", nodeID)
		}

		ps[nodeID] = &WidgetParam{
			WidgetID: widget.Id,
			Params: map[string]*WidgetParamValue{
				paramID: {widgetValue},
			},
		}
	}
	return &ChainParam{
		ChainID:     w.ChainID,
		ChainName:   w.ChainName,
		ChainDetail: w.ChainDetail,
		// RunID:   w.RunID,
		Params:    ps,
		DebugName: debugName,
	}, nil
}

func updateDebugName(widgetValue interface{}, currentPriority *int, debugName *string, paramID string, widgetId string) error {

	var err error

	if widgetValue != "" {
		if newPriority, needUpdate := shouldUpdateDebugName(*currentPriority, widgetId); needUpdate {
			// 输入是FileInput时，value是 [{"url":..., "uid":..., "name":...}] 结构，需要特殊处理
			if paramID == "FileInput" {
				if result, ok := widgetValue.([]interface{}); ok {
					inputMap := result[0]
					if result2, ok := inputMap.(map[string]interface{}); ok {
						if result3, ok := result2["name"].(string); ok {
							*debugName = result3
						} else {
							return stderr.Wrap(err, "断言失败")
						}
					} else {
						return stderr.Wrap(err, "断言失败")
					}
				}
			} else {
				if result4, ok := widgetValue.(string); ok {
					*debugName = result4
				}
			}
			*currentPriority = newPriority
		}
	}

	return nil
}

func shouldUpdateDebugName(currentPriority int, currentWidgetKey string) (int, bool) {
	priority := map[string]int{
		widgets.WidgetKeyTextInput:     3, // 最高优先级
		widgets.WidgetKeyUpstreamInput: 2,
		widgets.WidgetKeyFileInput:     1, // 最低优先级
	}

	if p, exist := priority[currentWidgetKey]; exist && p > currentPriority {
		return p, true
	}

	return currentPriority, false
}

func (w WidgetParams) ToWidgetParamMap() (map[string]map[string]interface{}, error) {
	res := make(map[string]map[string]interface{})
	for k, v := range w {
		m := make(map[string]interface{})
		for paramID, value := range v.Params {
			m[paramID] = value.Value
		}
		res[k] = m
	}
	return res, nil
}

func (w *WidgetParam) UpsertParam(key string, value interface{}) {
	if w.Params == nil {
		w.Params = make(map[string]*WidgetParamValue)
	}
	w.Params[key] = &WidgetParamValue{Value: value}
}

type AppsCountResp struct {
	AppNums          int `json:"app_nums"`
	PublishedAppNums int `json:"published_app_nums"`
}

type DependencyInfo struct {
	SimpleChainInfo  *SimpleChainInfo `json:"simple_chain_info"`
	ApiToolIds       map[string]bool  `json:"api_tool_ids"`
	ModelServiceIds  map[string]bool  `json:"model_service_ids"`
	KnowledgeBaseIds map[string]bool  `json:"knowledge_base_ids"`
}

type SimpleChainInfo struct {
	Id        string `json:"id"`
	Name      string `json:"name"`
	ProjectId string `json:"project_id"`
	CreatedBy string `json:"created_by"`
}

func (a AppletChainDO) GetDependencyInfo() (*DependencyInfo, error) {
	resp := &DependencyInfo{
		ApiToolIds:       make(map[string]bool),
		ModelServiceIds:  make(map[string]bool),
		KnowledgeBaseIds: make(map[string]bool),
	}
	resp.SimpleChainInfo = &SimpleChainInfo{
		Id:        a.Base.ID,
		Name:      a.Base.Name,
		ProjectId: a.Base.ProjectID,
		CreatedBy: a.Base.CreatedType,
	}
	if a.ChainDetail == nil {
		return resp, nil
	}

	for _, node := range a.ChainDetail.Nodes {
		oriWidgetKey := node.WidgetDetail.Id
		switch oriWidgetKey {
		case widgets.WidgetKeyAgent:
			// 智能体算子
			widgetParams, err := new(widgets.Agent).GetWidgetParams(node.Values)
			if err != nil {
				return nil, err
			}
			for _, k := range widgetParams.KnowledgeBases.KnowledgeBaseDesc {
				resp.KnowledgeBaseIds[k.ID] = true
			}

		case widgets.WidgetKeyTextKnowledgeSearch:
			// 知识库检索算子
			widgetParams, err := new(widgets.TextKnowledgeSearch).GetWidgetParams(node.Values)
			if err != nil {
				return nil, err
			}
			for _, k := range widgetParams.KnowledgeBaseDesc {
				resp.KnowledgeBaseIds[k.ID] = true
			}

		case widgets.WidgetKeyQaSearch:
			// 标准问答知识库
			widgetParams, err := new(widgets.QaSearch).GetWidgetParams(node.Values)
			if err != nil {
				return nil, err
			}
			if kbs := widgetParams.KnowledgeBases; kbs != nil {
				for _, kb := range kbs.KnowledgeBaseDesc {
					resp.KnowledgeBaseIds[kb.ID] = true
				}
			}
		}
	}

	return resp, nil
}

type ChainDebugParam struct {
	ChainID         string
	ChainName       string
	ChainDetail     *widgets.Chain
	RunID           string
	RunParam        WidgetParams
	RespWriter      io.Writer
	FinishChan      chan struct{}
	ExperienceModel bool
	EventHandler    stdsrv.SSEEventHandler
}

func (c ChainDebugParam) Done() <-chan struct{} {
	return c.FinishChan
}

func (c ChainDebugParam) FinishTask() {
	c.FinishChan <- struct{}{}
}
