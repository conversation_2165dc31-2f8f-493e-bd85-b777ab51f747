package models

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

type RemoteAPI struct {
	APIs []*pb.RemoteService
}

func (r RemoteAPI) ToWidgetDesc() []*DynamicWidgetDesc {
	res := make([]*DynamicWidgetDesc, 0)
	for _, s := range r.APIs {
		res = append(res, &DynamicWidgetDesc{
			Type: DynamicWidgetTypeExternalAPI.Desc,
			Name: s.Name,
			Key:  fmt.Sprintf("%v", s.Id),
		})
	}
	return res
}

// RemoteSvcToWidget 远程模型调用算子
func RemoteSvcToWidget(svc *pb.RemoteService) (*widgets.Widget, error) {
	params := make([]widgets.WidgetParam, 0)
	// url
	parsedURL, err := url.Parse(svc.ApiConfig.Url)
	if err != nil {
		return nil, stderr.Wrap(err, "URL解析失")
	}
	queryPath := fmt.Sprintf("%s://%s%s", parsedURL.Scheme, parsedURL.Host, parsedURL.Path)
	queryParams, err := url.ParseQuery(parsedURL.RawQuery)
	if err != nil {
		return nil, err
	}
	if len(svc.ApiConfig.QueryParams) != 0 {
		for _, q := range svc.ApiConfig.QueryParams {
			queryParams.Add(q.Name, q.Value)
		}
		queryPath = fmt.Sprintf("%s?%s", queryPath, queryParams.Encode())
	}
	for _, p := range svc.ApiConfig.PathParams {
		queryPath = strings.Replace(queryPath, fmt.Sprintf("{%s}", p.Name), p.Value, 1)
	}
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Define: widgets.DynamicParam{
			Id:           "URL",
			Name:         "API地址",
			Disabled:     true,
			DefaultValue: queryPath,
			Type:         pb.DynamicParam_TYPE_INPUT,
			DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		},
	})
	// header 参数
	kvs := make([]KVMap, 0)
	for _, h := range svc.ApiConfig.Headers {
		kvs = append(kvs, KVMap{
			Key:   h.Name,
			Value: h.Value,
		})
	}
	headerBytes, err := json.Marshal(kvs)
	if err != nil {
		return nil, err
	}
	params = append(params, widgets.WidgetParam{
		DataClass: widgets.DataClassString,
		Category:  widgets.ParamTypeAttribute,
		Define: widgets.DynamicParam{
			Id:           "Header",
			Name:         "请求头",
			Type:         pb.DynamicParam_TYPE_KVITEM,
			DataType:     pb.DynamicParam_DATA_TYPE_STRING,
			Multiple:     true,
			DefaultValue: string(headerBytes),
		},
	})
	// 请求体
	params = append(params, widgets.WidgetParam{
		DataClass:   widgets.DataClassString,
		Category:    widgets.ParamTypeNodeInPort,
		ParamLimits: widgets.SyncAnyLimits(),
		Define: widgets.DynamicParam{
			Id:   "RequestBody",
			Name: "请求体",
		},
	})
	// 输出
	params = append(params, widgets.WidgetParam{
		DataClass:   widgets.DataClassString,
		Category:    widgets.ParamTypeNodeOutPort,
		ParamLimits: widgets.AnyAnyLimits(),
		Define: widgets.DynamicParam{
			Id: "OutPut",
		},
	})

	baseDefine, err := widgets.WidgetFactoryImpl.GetWidgetDefine(widgets.WidgetKeyExternalAPICall)
	if err != nil {
		return nil, err
	}
	baseDefine.Name = svc.Name
	baseDefine.Desc = svc.Name
	baseDefine.Params = params
	return baseDefine, nil
}
func (r RemoteAPI) GetWidgetModel(ID string) (*widgets.Widget, error) {
	var remoteSvc *pb.RemoteService
	for _, api := range r.APIs {
		if api.Id == ID {
			remoteSvc = api
			break
		}
	}
	if remoteSvc == nil {
		return nil, stderr.Internal.Error("no remote svc :%v", ID)
	}
	return RemoteSvcToWidget(remoteSvc)
}
