package models

import (
	"strings"
	spb "transwarp.io/aip/llmops-common/pb/serving"
	spk "transwarp.io/aip/llmops-common/pkg/serving"
)

type SimpleSvcState string

const (
	SimpleSvcStateOnline   SimpleSvcState = "Online"   // 在线
	SimpleSvcStateOffLine  SimpleSvcState = "Offline"  // 离线
	SimpleSvcStateGoOnline SimpleSvcState = "GoOnline" // 上线中
)

type StateInfo struct {
	SimpleSvcState SimpleSvcState    `json:"simple_svc_state"  description:"简要状态"`
	MLopsSvcState  spk.MLopsSvcState `json:"mlops_svc_state"  description:"详细状态"`
}

func GetSimpleState(s spk.MLopsSvcState) SimpleSvcState {
	switch s {
	//运行中 => 在线
	case spk.MLOpsSvcStateAvailable:
		return SimpleSvcStateOnline

		// 上线失败、离线、审批拒接 => 离线
	case spk.MLOpsSvcStateFailed, spk.MLOpsSvcStateOffline, spk.MLOpsSvcStateApprovalRejected:
		return SimpleSvcStateOffLine

		// 上线中、等待调度、未发起审批、审批中、审批通过  =>  简化为上线中
	case spk.MLOpsSvcStateCreating, spk.MLOpsSvcStatePending, spk.MLOpsSvcStateApprovalInit,
		spk.MLOpsSvcStateUnderApproval, spk.MLOpsSvcStateApprovalPassed:
		return SimpleSvcStateGoOnline

	default: // 未知
		return SimpleSvcStateOffLine
	}
}

func GetStateInfo(s *spb.MLOpsSvcStateInfo) *StateInfo {
	if s == nil {
		return &StateInfo{
			SimpleSvcState: SimpleSvcStateOffLine,
			MLopsSvcState:  spk.MLOpsSvcStateOffline,
		}
	}
	state := spk.MLopsSvcState(s.State)
	return &StateInfo{
		SimpleSvcState: GetSimpleState(state),
		MLopsSvcState:  state,
	}
}

func GetRunningStateInfo() *StateInfo {
	return &StateInfo{
		SimpleSvcState: GetSimpleState(spk.MLOpsSvcStateAvailable),
		MLopsSvcState:  spk.MLOpsSvcStateAvailable,
	}
}

func GetMlopsSelector(selector string) []spk.MLopsSvcState {
	ret := make([]spk.MLopsSvcState, 0)
	strs := strings.Split(selector, ",")
	for _, str := range strs {
		if str == "" {
			continue
		}
		ret = append(ret, spk.MLopsSvcState(str))
	}
	return ret
}
