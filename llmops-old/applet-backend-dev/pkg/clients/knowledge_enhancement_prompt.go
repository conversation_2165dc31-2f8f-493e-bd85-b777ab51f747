package clients

import (
	"fmt"
	"strings"
)

const PromptTemplateTextQuestion = `
# 任务
%s

# 文本
<text>
%s
</text>

# 已生成问题
%s

# 回复要求
1. 只需要提出一个问题，直接输出问题，不要输出其他额外内容
2. 当存在已生成问题时，请提出与已生成问题不同的全新问题
`

const PromptTemplateTextSummary = `
# 任务
%s

# 文本
<text>
%s
</text>

# 回复要求
1. 直接输出总结，不要输出其他额外内容
`

const PromptTemplateTextCustom = `
# 任务
%s

# 文本
<text>
%s
</text>

# 回复要求
1. 直接输出结果，不要输出xml标签等其他额外信息，除非在任务中另有说明
`

const DefaultInstructionTextQuestion = `请认真阅读学习下方的文本，根据文本内容提出一个有价值的问题，此问题的答案必须包含在文本中，使用与文本相同的语言生成问题。`

const DefaultInstructionTextQuestionEn = `Please carefully read and study the text below, then formulate a meaningful question based on the text content. The answer to this question must be contained within the text, and the question should be generated in the same language as the text.`

const DefaultInstructionTextSummary = `请认真阅读学习下方的文本，对文本进行总结，确保总结在3句话且200字以内，使用与文本相同的语言生成总结。`

const DefaultInstructionTextSummaryEn = `Please carefully read and study the text below, then summarize the text in no more than 3 sentences and within 200 words, using the same language as the text to generate the summary.`

const DefaultInstructionTextCustom = `请认真阅读学习下方的文本，把文本准确地翻译成英文。`

func GetTextQuestionPrompt(instruction, text string, generatedQuestions []string) string {
	return fmt.Sprintf(PromptTemplateTextQuestion, instruction, text, strings.Join(generatedQuestions, "\n"))
}

func GetTextSummaryPrompt(instruction, text string) string {
	return fmt.Sprintf(PromptTemplateTextSummary, instruction, text)
}

func GetTextCustomPrompt(instruction, text string) string {
	return fmt.Sprintf(PromptTemplateTextCustom, instruction, text)
}
