package clients

import (
	"context"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/models/probe_questions"
)

const (
	FormatDefine = "在上述对话中，<contentUser></contentUser>标签内为用户的问题，<contentAI></contentAI>标签内为人工智能助手（AI）的回答"
	BaseTemplate = `
		基于上述对话，请生成1个新的潜在问题或指令，这些问题或指令是用户接下来最可能向人工智能助手（AI）提出的。
		确保您生成的内容不涉及政治敏感、违法违规、暴力伤害、违反公序良俗、种族主义或任何具有攻击性的内容。
		问题应紧密关联用户与人工智能助手（AI）之间的对话。确保问题的语言简洁。每个句子应仅包含一个问题，但也可以不是问句而是一句指令。
		你的回答应该与用户使用的语言相同。
		从你的角度来看，考虑这些是否是作为人工智能助手（AI）能够回答的问题。
		`
	HistoryQuestions = "不要生成和下面例举的相同或类似的问题：$USER_HISTORY_QUESTIONS$。"
	AnswerTemplate   = "生成1个长度不超过20个字的问题或指令。直接返回这个问题或指令，不要有多余的叙述。"
	LastConversion   = "用户:<contentUser>$USER$</contentUser>.AI:<contentAI>$AI$</contentAI>"
)

type ProbeQuestionsUtil struct {
	Model *pb.ModelService
}

func (P ProbeQuestionsUtil) ProbeQuestions(ctx context.Context, params probe_questions.ProbeQuestionsParam) (string, error) {
	history := "[" + strings.Join(params.UserHistoryQuestions, ",") + "]"
	query := ReplaceAndCombine(params.UserLastQuestion, params.ModelLastAnswer, history, params.UserProbeTemplate)
	req := &triton.LLMChatReq{
		Prompt: "你是一个问题推荐系统，请一步步地仔细思考",
		Query:  query,
		Stream: false,
		Stop:   false,
	}
	var answer string
	resHandler := func(textContent string) error {
		answer = textContent
		return nil
	}
	openaiChatReq := Cvt2OpenaiChatReq(req)

	err := SyncChatForTaskMode(ctx, P.Model, openaiChatReq, resHandler)
	if err != nil {
		stdlog.WithError(err).Errorf("sync model infer")
		return answer, stderr.Wrap(err, "sync model infer")
	}

	return answer, nil
}

func NewProbeQuestionsUtil(ctx context.Context, model *pb.ModelService) (probeQuestionsUtil *ProbeQuestionsUtil, err error) {
	/*	isSuccess, err := CheckModelConn(model)
		if !isSuccess {
			return nil, stderr.BadRequest.Cause(err, "failed to access model by tcp")
		}*/
	return &ProbeQuestionsUtil{
		Model: model,
	}, nil
}

func ReplaceAndCombine(user, ai, history, userTemplate string) string {
	conversion := strings.ReplaceAll(LastConversion, "$USER$", user)
	conversion = strings.ReplaceAll(conversion, "$AI$", ai)
	historyQuestions := strings.ReplaceAll(HistoryQuestions, "$USER_HISTORY_QUESTIONS$", history)
	if userTemplate != "" {
		return conversion + FormatDefine + userTemplate + historyQuestions + AnswerTemplate
	} else {
		return conversion + FormatDefine + BaseTemplate + historyQuestions + AnswerTemplate
	}
}
