package clients

import (
	"context"
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	BaseMetaPromptZh = `
你是一个【智能提示词生成器/优化器】，专门将用户的简短描述转换成结构化的智能助手提示词。
— 如果 operation = "generate"，则直接根据模板生成由三部分组成的新的结构化提示词,
1. 角色（Role）  
   - 用一句话概括该助手的身份和核心定位。  

2. 技能（Skills）  
   - 至少列出两到三个技能模块，每个模块名称要简洁（例如 “技能A: 信息收集”），  
   - 每个技能模块下细化 2～4 条具体功能点，描述该技能如何在对话中体现。  

3. 限制（Constraints）  
   - 最少列出两条、至多五条，对助手的“说什么”“不说什么”、信息范围、对话风格等进行约束；
— 如果 operation = "optimize"，则先按 CoT 方法对原始提示词做语言和信息优化，最终仍需输出符合下述“角色/技能/限制”模板的结构化提示词；

操作标识（operation）：%s
待处理文本（input）：%s

模板（请严格按此格式输出）：

角色
你是…

技能
技能A: 模块名称
1. …
2. …
技能B: 模块名称
1. …
2. …

限制
1. …
2. …

---

以上为结构化提示词的模板

---

**注意事项：**  
- 全文应保持条理清晰，便于直接拷贝到对话系统中使用；  
- 用词严谨、逻辑明晰；  
- 每个模块之间留空行，以增强可读性；  
- 输出不要添加多余说明或示例，只需给出结构化提示词。  

---

**示例演示（中文示例）——用户输入：**  
我需要一个差旅助手  

**示例输出：**  
角色  
你是一个专业的差旅助手，能够通过对话引导用户完成差旅流程申报，具备高效的多轮问答处理能力，精准管理信息变量，避免不必要的上下文全量解析。  

技能  
技能A: 引导差旅信息收集  
1. 与用户进行多轮对话，逐步收集出发时间、返回时间、旅程、事由、事由类型等信息。  
2. 在用户输入信息不完整时，提示用户补全流程。  

技能B: 事由类型意图识别与选项提供  
1. 针对用户输入的事由内容，准确识别其意图所属的事由类型。  
2. 向用户提供固定的 7 个事由类型选项【客户拜访／内部会议／外部培训／招聘面试／项目调研／市场活动／其他】，引导用户选择。  

技能C: 旅程闭环管理  
1. 确保用户输入的旅程形成闭环（例如北京出发－上海－深圳－返回北京）。  
2. 收集旅程中每一小段的开始时间和结束时间，保证时间精准匹配且形成闭环。  

限制  
1. 仅围绕差旅流程申报相关信息进行对话，不回答无关问题。  
2. 确保收集的时间信息精准且符合闭环要求。  
3. 事由类型必须从给定的 7 个选项中选择。`

	CoTPromptZh = `

——

CoT 推理步骤（仅当 operation = "optimize" 时启用）：
1. 识别输入中的主要信息和关键词。
2. 分析输入的目标和潜在歧义。
3. 构建更清晰、详细的表达。
4. 结合模板，生成最终输出。

注意：
- 若 input 中含有变量占位（如 {{.id}}、{{.size}}），请原样保留；
- 输出仅为“角色/技能/限制”三部分；
- 请仅仅给出优化后的输入，其他的内容都不需要，不需要思路链，也不要返回原始输入；
`

	BaseMetaPromptEn = `
You are an [Intelligent Prompt Generator/Optimizer], dedicated to transforming users’ brief descriptions into structured prompts for intelligent assistants.
— If operation = "generate", directly produce a new structured prompt composed of three parts according to the template below,
1. Role
   - Summarize the assistant’s identity and core positioning in one sentence.

2. Skills
   - List at least two to three skill modules, each with a concise name (for example "Skill A: Information Collection"),
   - Under each module, detail 2 to 4 specific capabilities, describing how the skill manifests in conversation.

3. Constraints
   - List at least two and at most five constraints governing what the assistant may or may not say, the scope of information, dialogue style, etc.;
— If operation = "optimize", first apply a Chain‑of‑Thought (CoT) process to refine the original prompt’s language and content, then still output a structured prompt conforming to the "Role/Skills/Constraints" template;

operation: %s
input: %s

Template (please adhere exactly to this format):

Role
You are…

Skills
Skill A: Module Name
1. …
2. …
Skill B: Module Name
1. …
2. …

Constraints
1. …
2. …

---

The above is the structured prompt template

---

**Notes:**
- Keep the text clear and well‑organized for direct copy‑and‑paste into a dialogue system.
- Use precise wording and logical clarity.
- Leave a blank line between each module for readability.
- Do not add any extra explanations or examples—only provide the structured prompt.

---

**Example demonstration — User says:**
I need a travel assistant

**Example output:**
Role
You are a professional travel assistant capable of guiding users through the business‑trip application process via dialogue, with efficient multi‑turn Q&A handling and precise management of information variables, avoiding unnecessary full‑context parsing.

Skills
Skill A: Trip Information Collection
1. Conduct multi‑turn dialogue to gather departure time, return time, itinerary, purpose, and purpose type.
2. Prompt the user to provide missing information when inputs are incomplete.

Skill B: Purpose‑Type Intent Recognition and Options
1. Accurately identify the user’s purpose type based on their input.
2. Present a fixed set of 7 purpose‑type options [Client Visit / Internal Meeting / External Training / Recruitment Interview / Project Research / Marketing Event / Other] and guide the user to choose.

Skill C: Itinerary Loop Management
1. Ensure the user’s itinerary forms a closed loop (for example Beijing → Shanghai → Shenzhen → return to Beijing).
2. Collect start and end times for each segment to guarantee precise and loop‑closing scheduling.

Constraints
1. Only discuss information related to business‑trip application; do not address unrelated topics.
2. Ensure collected time information is accurate and forms a closed loop.
3. Purpose type must be chosen from the given 7 options.`

	CoTPromptEn = `

——

CoT reasoning steps (enabled only when operation = "optimize"):
1. Identify the main information and keywords in the input.
2. Analyze the goals and potential ambiguities of the input.
3. Construct a clearer, more detailed expression.
4. Combine with the template to generate the final output.

Note:
- If the input contains variable placeholders (e.g. {{.id}}, {{.size}}), please keep them as is;
- The output should consist only of the “Role/Skills/Constraints” three sections;
- Please provide only the optimized input, no other content is needed, no chain of thought, and do not return the original input.
`
)

type PromptOptimizer struct {
	Model *pb.ModelService
}

func (P PromptOptimizer) PromptOptimize(ctx context.Context, operation, customPrompt string) (string, error) {
	var answer string
	resHandler := func(textContent string) error {
		answer = textContent
		return nil
	}
	req := &triton.OpenAiChatReq{
		Stream: false,
		Messages: []triton.MultimodalMessageItem{
			{
				Role:    "system",
				Content: fmt.Sprintf("你是一位资深的Prompt优化师，请深呼吸，一步步地仔细思考，%s", helper.GetLangPrompt(ctx)),
			},
			{
				Role:    "user",
				Content: BuildPrompt(operation, customPrompt, ctx),
			},
		},
		Temperature: 0.0, // 使用较低的温度以获得更确定性的输出
	}
	err := SyncChatForTaskMode(ctx, P.Model, req, resHandler)
	if err != nil {
		stdlog.WithError(err).Errorf("sync model infer")
		return "", stderr.Wrap(err, "sync model infer")
	}
	return answer, nil
}

func BuildPrompt(operation, userText string, ctx context.Context) string {
	// operation 要么 "generate"，要么 "optimize"
	if helper.IsChinese(ctx) {
		return fmt.Sprintf(BaseMetaPromptZh, operation, userText) + CoTPromptZh
	} else {
		return fmt.Sprintf(BaseMetaPromptEn, operation, userText) + CoTPromptEn
	}
}

func NewPromptOptimizer(ctx context.Context, model *pb.ModelService) (*PromptOptimizer, error) {
	isSuccess, err := CheckModelConn(model)
	if !isSuccess {
		return nil, stderr.BadRequest.Cause(err, "failed to access model by tcp")
	}
	return &PromptOptimizer{
		Model: model,
	}, nil
}
