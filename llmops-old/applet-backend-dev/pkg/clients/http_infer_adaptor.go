package clients

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"h12.io/socks"
	"net/http"
	"net/url"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

const (
	modelField       = "model"
	temperatureField = "temperature"
	topPField        = "top_p"

	frequencyPenaltyField       = "frequency_penalty"
	repetitionPenaltyField      = "repetition_penalty"
	stopField                   = "stop"
	stopWordsField              = "stop_words"
	maxTokensField              = "max_tokens"
	maxLengthField              = "max_length"
	languageField               = "language"
	promptField                 = "prompt"
	responseFormatField         = "response_format"
	timestampGranularitiesField = "timestamp_granularities"

	topKField              = "top_k"
	noRepeatNgramSizeField = "no_repeat_ngram_size"
)

// InferAdaptor 模型适配器,用于对接各种类型、渠道的远程模型
// 内部尽量使用openai格式进行调用
type InferAdaptor interface {

	// ConvertInferReq 将请求由openai格式转换为远程模型支持的格式
	ConvertInferReq(ctx context.Context, model *pb.ModelService,
		oriInferReq ConcreteInferReq) (remoteInferReq ConcreteInferReq, err error)

	// ResolveInferResp 将响应由远程模型的原生格式-string-转换为openai格式
	ResolveInferResp(ctx context.Context, model *pb.ModelService, remoteInferReq ConcreteInferReq,
		remoteInferRespStr string) (expectedInferResp ConcreteInferResp, err error)
}

func getRemoteAdaptor(model *pb.ModelService) (InferAdaptor, error) {
	if !isRemoteModel(model) {
		return nil, stderr.Error("only remote model service need Adaptor")
	}
	format := model.RemoteServiceConfig.InterfaceSpec
	switch format {
	case pb.InterfaceSpecName_INTERFACE_SPEC_OPENAI:
		return new(OpenaiAdaptor), nil
	case pb.InterfaceSpecName_INTERFACE_SPEC_TRANSWARP:
		return new(TranswarpAdaptor), nil
	default:
		return nil, stderr.Errorf(fmt.Sprintf("the format %s is wait to support", format.String()))
	}
}

// TranswarpAdaptor 用于采用openai格式调用transwarp格式的远程模型,
type TranswarpAdaptor struct {
}

func (*TranswarpAdaptor) ConvertInferReq(ctx context.Context, model *pb.ModelService,
	oriInferReq ConcreteInferReq) (remoteInferReq ConcreteInferReq, err error) {
	// 将各类型请求由openai格式转为对应的transwarp格式
	switch oriInferReq.(type) {
	case *triton.OpenAiChatReq:
		remoteInferReq, err = oriInferReq.(*triton.OpenAiChatReq).Cvt2LLMChatReq()
	case *triton.OpenAiCompletionReq:
		remoteInferReq, err = oriInferReq.(*triton.OpenAiCompletionReq).Cvt2LLMChatReq()
	case *triton.OpenAiImageGenReq:
		remoteInferReq, err = oriInferReq.(*triton.OpenAiImageGenReq).Cvt2StdImageGenReq()
	case *triton.OpenAiTextVectorReq:
		remoteInferReq, err = oriInferReq.(*triton.OpenAiTextVectorReq).Cvt2StdTextVectorReq()
	case *triton.OpenAiAudioTransReq:
		remoteInferReq, err = oriInferReq.(*triton.OpenAiAudioTransReq).Cvt2StdAudioTransReq()

	case *triton.OpenAiImageEditReq:
		err = stderr.Error("wait to support OpenAiImageEditReq")
	case *triton.OpenAiImageVariReq:
		err = stderr.Error("wait to support OpenAiImageVariReq")
	default:
		// cv、ml模型、Rerank等模型,不存在openai格式,传入transwarp格式,返回原始格式
		remoteInferReq = oriInferReq
	}
	return remoteInferReq, err
}

func (*TranswarpAdaptor) ResolveInferResp(ctx context.Context, model *pb.ModelService, remoteInferReq ConcreteInferReq,
	remoteInferRespStr string) (expectedInferResp ConcreteInferResp, err error) {
	switch remoteInferReq.(type) {
	case *triton.LLMChatReq:
		LLMChatResp, err := getResultFromStdInferResp[triton.LLMChatResp](remoteInferRespStr)
		if err != nil {
			return nil, err
		}

		OpenAiChatResp := &triton.OpenAiChatResp{
			Id:      LLMChatResp.RequestId,
			Created: LLMChatResp.Created,
			Model:   LLMChatResp.Model,
			Usage:   LLMChatResp.Usage,
			Choices: []*triton.Choice{{Index: 0, FinishReason: LLMChatResp.FinishReason}},
		}
		if remoteInferReq.IsStream() {
			OpenAiChatResp.Object = triton.RespObjectChunkChat
			OpenAiChatResp.Choices[0].Delta = &triton.MessageItem{Role: triton.RoleAssistant, Content: LLMChatResp.Text}
		} else {
			OpenAiChatResp.Object = triton.RespObjectChat
			OpenAiChatResp.Choices[0].Message = &triton.MessageItem{Role: triton.RoleAssistant, Content: LLMChatResp.Text}
		}
		expectedInferResp = OpenAiChatResp

	case *triton.StdAudioTransReq:
		StdLLMInferResp, err := getResultFromStdInferResp[triton.StdLLMInferResp](remoteInferRespStr)
		if err != nil {
			return nil, err
		}

		OpenAiAudioTransResp := &triton.OpenAiAudioTransResp{
			Text: StdLLMInferResp.Text,
		}
		expectedInferResp = OpenAiAudioTransResp

	case *triton.StdImageGenReq:
		StdLLMInferResp, err := getResultFromStdInferResp[triton.StdLLMInferResp](remoteInferRespStr)
		if err != nil {
			return nil, err
		}

		var image triton.Image
		if err := json.Unmarshal([]byte(StdLLMInferResp.Text), &image); err != nil {
			return nil, err
		}
		image.RevisedPrompt = remoteInferReq.(*triton.StdImageGenReq).Prompt
		openAiImageGenResp := &triton.OpenAiImageGenResp{
			Created: StdLLMInferResp.Created,
			Data: []triton.Image{
				image,
			},
		}
		expectedInferResp = openAiImageGenResp

	case *triton.Text2VecReqV2:
		TextVecResV2, err := getResultFromStdInferResp[triton.TextVecResV2](remoteInferRespStr)
		if err != nil {
			return nil, err
		}

		var data []triton.Embedding
		for i, result := range TextVecResV2.Results {
			embedding := triton.Embedding{
				Index:     i,
				Embedding: result,
				Object:    "embedding",
			}
			data = append(data, embedding)
		}
		openAiTextVectorResp := &triton.OpenAiTextVectorResp{
			Object: "list",
			Data:   data,
		}

		expectedInferResp = openAiTextVectorResp

	//不存在相应的openai格式,采用transwarp格式返回
	case *triton.RerankReq:
		expectedInferResp, err = getResultFromStdInferResp[triton.RerankRes](remoteInferRespStr)
	case *triton.StdMLReq:
		expectedInferResp, err = getResultFromStdInferResp[triton.StdMLResp](remoteInferRespStr)
	case *triton.StdCVReq:
		expectedInferResp, err = getResultFromStdInferResp[triton.StdCVResp](remoteInferRespStr)
	case *triton.EntityRecognitionReq:
		expectedInferResp, err = getResultFromStdInferResp[triton.EntityRecognitionResp](remoteInferRespStr)
	case *triton.LLMChatStopReq:
		expectedInferResp, err = getResultFromStdInferResp[triton.LLMChatStopResp](remoteInferRespStr)
	default:
		err = stderr.Error("must use transwarp format to call remote-transwarp-model")
	}
	if err != nil {
		return nil, stderr.Wrap(err, "fail to construct expectedInferResp")
	}
	return expectedInferResp, nil
}

func getResultFromStdInferResp[T any](stdInferStr string) (*T, error) {
	// 兼容之前std/v1下接口推理结果,嵌套一层储存在result字段的情况
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(stdInferStr), &jsonData)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do unmarshal")
	}
	// 获取 result 字段的值
	result, ok := jsonData["result"]
	if ok {
		return triton.Construct2StdInferResp[T](stdsrv.AnyToString(result))
	}
	return triton.Construct2StdInferResp[T](stdInferStr)
}

// OpenaiAdaptor 采用openai格式调用openai格式的远程模型
type OpenaiAdaptor struct {
}

func (*OpenaiAdaptor) ConvertInferReq(ctx context.Context, model *pb.ModelService,
	oriInferReq ConcreteInferReq) (remoteInferReq ConcreteInferReq, err error) {

	// 将各类型请求由openai格式转为对应的openai格式,原始格式返回即可
	// 同时填充请求体的model字段
	modelFiled := getModelField(model)
	switch oriInferReq.(type) {
	case *triton.OpenAiChatReq:
		openaiReq := oriInferReq.(*triton.OpenAiChatReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiCompletionReq:
		openaiReq := oriInferReq.(*triton.OpenAiCompletionReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiImageGenReq:
		openaiReq := oriInferReq.(*triton.OpenAiImageGenReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiTextVectorReq:
		openaiReq := oriInferReq.(*triton.OpenAiTextVectorReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiAudioTransReq:
		openaiReq := oriInferReq.(*triton.OpenAiAudioTransReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiImageEditReq:
		openaiReq := oriInferReq.(*triton.OpenAiImageEditReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	case *triton.OpenAiImageVariReq:
		openaiReq := oriInferReq.(*triton.OpenAiImageVariReq)
		openaiReq.Model = modelFiled
		remoteInferReq = openaiReq

	default:
		remoteInferReq = oriInferReq
	}
	return remoteInferReq, err
}

func getModelField(model *pb.ModelService) string {
	// 1、优先从推理参数中获取
	for _, param := range model.InferenceParams {
		if param.Id == modelField {
			return param.DefaultValue
		}
	}

	// 2、尝试从远程模型配置的请求体中获取 若不存在则使用默认
	if model.RemoteServiceConfig == nil {
		return triton.DefaultInnerModelName
	}
	bodyMap := make(map[string]any)
	if err := json.Unmarshal([]byte(model.RemoteServiceConfig.Body), &bodyMap); err != nil {
		return triton.DefaultInnerModelName
	}
	if model, ok := bodyMap[modelField]; ok {
		if _, ok := model.(string); ok {
			return model.(string)
		}
	}
	return triton.DefaultInnerModelName
}

func (*OpenaiAdaptor) ResolveInferResp(ctx context.Context, model *pb.ModelService, remoteInferReq ConcreteInferReq,
	remoteInferRespStr string) (expectedInferResp ConcreteInferResp, err error) {
	switch remoteInferReq.(type) {
	case *triton.OpenAiChatReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiChatResp](remoteInferRespStr)
	case *triton.OpenAiCompletionReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiCompletionResp](remoteInferRespStr)
	case *triton.OpenAiImageGenReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiImageGenResp](remoteInferRespStr)
	case *triton.OpenAiTextVectorReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiTextVectorResp](remoteInferRespStr)
	case *triton.OpenAiAudioTransReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiAudioTransResp](remoteInferRespStr)
	case *triton.OpenAiImageEditReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiImageEditResp](remoteInferRespStr)
	case *triton.OpenAiImageVariReq:
		expectedInferResp, err = triton.Construct2OpenaiInferResp[triton.OpenAiImageVariResp](remoteInferRespStr)
	default:
		// 远程模型是openai格式,则必定存在对应的openai格式。要求使用openai格式进行调用
		err = stderr.Error("must use openai format to call remote-openai-model")
	}
	return expectedInferResp, err
}

// getChatModelInferResult 从chat模型的输出中获取流式推理的纯文本,
// body为对应响应体的json格式字段
func getChatModelInferResult(inferReq ConcreteInferReq, body string) (string, error) {
	switch inferReq.(type) {
	case *triton.OpenAiChatReq:
		openaiChatResp, err := triton.Construct2OpenaiInferResp[triton.OpenAiChatResp](body)
		if err != nil {
			return "", err
		}
		return openaiChatResp.GetInferResult()
	case *triton.OpenAiCompletionReq:
		openaiCompletionResp, err := triton.Construct2OpenaiInferResp[triton.OpenAiCompletionResp](body)
		if err != nil {
			return "", err
		}
		return openaiCompletionResp.GetInferResult()
	case *triton.LLMChatReq:
		stdInferResp, err := triton.Construct2StdInferResp[triton.LLMChatResp](body)
		if err != nil {
			return "", err
		}
		LLMChatResp := stdInferResp
		return LLMChatResp.Text, nil
	default:
		return "", stderr.Error("only support OpenAiChatReq, OpenAiCompletionReq, LLMChatReq to call chat-model")
	}
}

func getChatModelEventParseHandler(inferReq ConcreteInferReq, stringHandler func(textContent string) error) (func(event stdsrv.SSEEvent) error, error) {
	eventParseHandler := func(event stdsrv.SSEEvent) error {
		if event.Event == stdsrv.SSEEvtError {
			return stderr.Error("received an incorrect SSE message,event = %s ", stdsrv.AnyToString(event))
		}
		data := stdsrv.AnyToString(event.Data)
		if strings.TrimSpace(data) == triton.DONEMessage {
			stdlog.Info("received [DONE] for SSE message")
			return nil
		}
		inferResult, err := getChatModelInferResult(inferReq, data)
		if err != nil {
			return err
		}
		return stringHandler(inferResult)
	}
	return eventParseHandler, nil
}
func getChatModelStringParseHandler(inferReq ConcreteInferReq, stringHandler func(textContent string) error) (func(body string) error, error) {
	stringParseHandler := func(body string) error {
		inferResult, err := getChatModelInferResult(inferReq, body)
		if err != nil {
			return err
		}
		return stringHandler(inferResult)
	}
	return stringParseHandler, nil
}

func getHttpTransport(proxy *pb.RemoteServiceProxy) (*http.Transport, error) {
	defaultTransport := http.DefaultTransport.(*http.Transport)
	defaultTransport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	if proxy == nil {
		return defaultTransport, nil
	}
	scheme, err := proxyScheme(proxy.Scheme)
	if err != nil {
		return nil, err
	}
	if !strings.HasPrefix(proxy.Url, scheme) {
		proxy.Url = scheme + "://" + proxy.Url
	}
	proxyURL, err := url.Parse(proxy.Url)
	if err != nil {
		return nil, err
	}
	tr := defaultTransport.Clone()
	switch proxy.Scheme {
	case pb.ProxyScheme_PROXY_SCHEME_HTTP:
		tr.Proxy = http.ProxyURL(proxyURL)
		return tr, nil

	case pb.ProxyScheme_PROXY_SCHEME_HTTPS:
		tr.Proxy = http.ProxyURL(proxyURL)
		tr.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
		return tr, nil
	case pb.ProxyScheme_PROXY_SCHEME_SOCKS4, pb.ProxyScheme_PROXY_SCHEME_SOCKS5:
		dialer := socks.Dial(proxy.Url)
		return &http.Transport{Dial: dialer}, nil
	default:
		return nil, stderr.Internal.Error("unsupported proxy schema")
	}
}
