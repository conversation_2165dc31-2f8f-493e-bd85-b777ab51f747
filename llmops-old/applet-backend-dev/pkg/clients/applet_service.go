package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

type AppletSvcConfig struct {
	DefaultInnerPort     int    `yaml:"default_inner_port"`      // 部署为服务时的默认监听端口
	DefaultAPIPath       string `yaml:"default_api_path"`        // 部署为服务时的默认API Path
	DefaultHealthAPIPath string `yaml:"default_health_api_path"` // 部署为服务时的默认API Path
}

type AppletSvcClient struct {
	c  AppletSvcConfig
	mc *MlOpsClient
	hc *HttpClient
}

var (
	pj = stdsrv.DefaultProtoJsonAccessor()
)

func NewAppletSvcClient(mlopsCfg MLOpsConfig, cfg AppletSvcConfig, hc *HttpClient) (*AppletSvcClient, error) {
	mc, err := NewMLOpsClient(mlopsCfg)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return NewAppletSvcClientWithMC(mc, cfg, hc)
}

func NewAppletSvcClientWithMC(mc *MlOpsClient, cfg AppletSvcConfig, hc *HttpClient) (*AppletSvcClient, error) {
	if hc == nil {
		hc = &HttpClient{
			Cli: http.DefaultClient,
		}
	}

	return &AppletSvcClient{
		c:  cfg,
		mc: mc,
		hc: hc,
	}, nil
}

// GetSvcCallUrl 获取应用服务的执行接口经过 mlops 转发后的请求地址
func (m *AppletSvcClient) GetSvcCallUrl(ctx context.Context, svcID string) (string, error) {
	u, err := m.mc.GetHttpUrlOfService(ctx, svcID, m.c.DefaultInnerPort, m.c.DefaultAPIPath)
	if err != nil {
		return "", err
	}
	queryParams := helper.GetEngineExecQueryParams(ctx, false, false)
	u.RawQuery = queryParams.Encode()
	return u.String(), nil
}

// GetSvcHealthUrl 获取应用服务的健康接口经过 mlops 转发后的请求地址
func (m *AppletSvcClient) GetSvcHealthUrl(ctx context.Context, svcID string) (string, error) {
	u, err := m.mc.GetHttpUrlOfService(ctx, svcID, m.c.DefaultInnerPort, m.c.DefaultHealthAPIPath)
	if err != nil {
		return "", err
	}
	return u.String(), nil
}

func (m *AppletSvcClient) CheckSvcHealth(ctx context.Context, svcID string) (*health.ChainHealth, error) {
	u, err := m.GetSvcHealthUrl(ctx, svcID)
	if err != nil {
		return nil, stderr.Wrap(err, "get full call url")
	}
	healthInfo := &health.ChainHealthResponse{}
	if err := m.hc.HttpCall(ctx, &HttpParam{
		Method: http.MethodGet,
		Url:    u,
	}, healthInfo); err != nil {
		return nil, err
	}
	if !healthInfo.Success {
		return nil, stderr.Internal.Error("get health info err :%v", healthInfo)
	}
	return healthInfo.Data, nil
}

// CallAppletSvcWithRespHandler 调用应用链服务，并实时接受服务侧推送的所有数据，并使用给定Handler进行处理
func (m *AppletSvcClient) CallAppletSvcWithRespHandler(ctx context.Context, serviceID string, params map[string]any, handler stdsrv.SSEEventHandler) error {
	u, err := m.GetSvcCallUrl(ctx, serviceID)
	if err != nil {
		return stderr.Trace(err)
	}
	req, err := helper.GetHttpReq(ctx, http.MethodPost, u, params, true)
	if err != nil {
		return stderr.Trace(err)
	}
	if err = helper.HttpCallForStream(m.hc.Cli, req, handler); err != nil {
		return stderr.Trace(err)
	}
	return nil
}

// CallChunkingStrategySvc 调用解析策略类型的应用服务-即文档解析类型
// 要求链的输入为FileInputNode,输出格式为{"chunks":[]*pb.Chunk, "elements":[]*pb.DocElement}
func (m *AppletSvcClient) CallChunkingStrategySvc(ctx context.Context, serviceID string, sfsFilePath string) (*pb.DocSvcLoadChunkRsp, error) {
	type fileinfo struct {
		Url  string `json:"url"`
		Uid  string `json:"uid"`
		Name string `json:"name"`
	}

	const sfsPrefix = "sfs:///"
	if !strings.HasPrefix(sfsFilePath, sfsPrefix) {
		return nil, stderr.InvalidParam.Error("invalid filepath , expecting url like: 'sfs:///temp/doctest/doctest.pdf'")
	}
	fi := fileinfo{
		Url:  sfsFilePath,
		Uid:  strings.TrimPrefix(sfsFilePath, sfsPrefix),
		Name: filepath.Base(sfsFilePath),
	}
	rsp, err := m.CallAppletSvc(ctx, serviceID, map[string]any{
		"FileInput": []fileinfo{fi},
	})
	if err != nil {
		return nil, stderr.Trace(err)
	}

	ret := new(pb.DocSvcLoadChunkRsp)
	rsp.Body = helper.ExtractCodeFromMarkdown(rsp.Body)
	if err = pj.Unmarshal([]byte(rsp.Body), ret); err != nil {
		return nil, stderr.Wrap(err, `unexpected resp, expecting {"chunks":[]*pb.Chunk, "elements":[]*pb.DocElement}`)
	}
	return ret, nil
}

// CallRecallStrategySvc 调用recall类型的应用服务
// 要求链的输入为textInputNode,输出格式为{"chunks":[]*pb.Chunk, "elements":[]*pb.DocElement}
func (m *AppletSvcClient) CallRecallStrategySvc(ctx context.Context, serviceID string, textInputContent string) (*pb.DocSvcLoadChunkRsp, error) {
	if textInputContent == "" {
		return nil, stderr.InvalidParam.Error("the textInputContent is empty str")
	}
	rsp, err := m.CallAppletSvc(ctx, serviceID, map[string]any{
		"TextInput": textInputContent,
	})
	if err != nil {
		return nil, stderr.Trace(err)
	}
	ret := new(pb.DocSvcLoadChunkRsp)
	if err = pj.Unmarshal([]byte(rsp.Body), ret); err != nil {
		return nil, stderr.Wrap(err, `unexpected resp, expecting {"chunks":[]*pb.Chunk, "elements":[]*pb.DocElement}`)
	}
	return ret, nil
}

// CallAppletSvc 调用应用链服务，并等待完成后返回对应的响应体
func (m *AppletSvcClient) CallAppletSvc(ctx context.Context, serviceID string, params map[string]any) (*AppletSvcResp, error) {
	rsp := new(AppletSvcResp)
	err := m.CallAppletSvcWithRespHandler(ctx, serviceID, params, rsp.OnEvent)
	return rsp, err
}

// CallAppletSvcRaw 调用应用链服务，并且实时返回原始的服务输出
func (m *AppletSvcClient) CallAppletSvcRaw(ctx context.Context, svcID string, postBody string, writer io.Writer) (*bytes.Buffer, error) {
	u, err := m.GetSvcCallUrl(ctx, svcID)
	if err != nil {
		return nil, stderr.Wrap(err, "get full call url")
	}
	return m.hc.HttpPostStream(ctx, u, postBody, writer)
}

type AppletSvcRespHandler interface {
	OnMessage(any) error
	OnError(error) error
	OnDebug(any) error
	OnTrace(any) error
	OnClose() error
	OnOthers(event stdsrv.SSEEvent) error
}

type AppletSvcResp struct {
	Body  string
	Error error
	Trace []any
	Debug []any
}

func (a *AppletSvcResp) OnTrace(trace any) error {
	a.Trace = append(a.Trace, trace)
	return nil
}

func (a *AppletSvcResp) OnClose() error {

	stdlog.Debugf("completed handling sse")
	return nil
}

func (a *AppletSvcResp) OnOthers(evt stdsrv.SSEEvent) error {
	// do nothing
	stdlog.Debugf("unsupported event type %s: %+v", evt.Event, evt)
	return nil
}

func (a *AppletSvcResp) OnMessage(v any) error {
	strv, ok := v.(string)
	if ok {
		if strings.HasPrefix(strv, `{"response":"`) {
			recv := make(map[string]any)
			if err := json.Unmarshal([]byte(strv), &recv); err != nil {
				return stderr.Wrap(err, "parse applet svc sse message %s", strv)
			}
			v = recv
		}
	}

	wrapped, ok := v.(map[string]any)
	if ok && wrapped["response"] != nil {
		v = wrapped["response"]
	}
	a.Body += string(stdsrv.AnyToBytes(v))
	return nil
}

func (a *AppletSvcResp) OnError(err error) error {
	a.Error = err
	return err
}

func (a *AppletSvcResp) OnDebug(debugInfo any) error {
	a.Debug = append(a.Debug, debugInfo)
	return nil
}

func (a *AppletSvcResp) OnEvent(evt stdsrv.SSEEvent) error {
	stdlog.Debugf("recv evt %+v", evt)
	switch evt.Event {
	case stdsrv.SSEEvtMessage:
		return a.OnMessage(evt.Data)
	case stdsrv.SSEEvtError:
		return a.OnError(stderr.Error("recv error event: " + string(stdsrv.AnyToBytes(evt.Data))))
	case stdsrv.SSEEvtTrace:
		return a.OnTrace(evt.Data)
	case stdsrv.SSEEvtDebug:
		return a.OnDebug(evt.Data)
	case stdsrv.SSEEvtClose:
		return a.OnClose()
	default:
		return a.OnOthers(evt)
		// TODO
		// EventTypeThought     EventType = "thought"
		// EventTypeAction      EventType = "action"
		// EventTypeObservation EventType = "observation"
		// EventTypeStack       EventType = "stack"
	}
}
