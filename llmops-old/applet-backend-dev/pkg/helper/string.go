package helper

import (
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
)

func InterfaceToString(v interface{}) (string, error) {
	if stringValue, ok := v.(string); ok {
		return stringValue, nil
	} else {
		if bytes, err := json.Marshal(v); err != nil {
			return "", err
		} else {
			return string(bytes), nil
		}
	}
}

func StringToMap(str string) (map[string]string, error) {
	res := make(map[string]string, 0)
	if str == "" {
		return res, nil
	}
	if err := json.Unmarshal([]byte(str), &res); err != nil {
		return nil, err
	}
	return res, nil

}

func StringToInterface(str string, target any) error {
	if str == "" {
		return nil
	}
	if err := json.Unmarshal([]byte(str), &target); err != nil {
		return err
	}
	return nil
}

func StringToEnumSlice[T ~int32](str string, mp map[string]int32) []T {
	var ret []T
	for _, s := range strings.Split(str, ",") {
		if enumVal, ok := mp[s]; ok {
			ret = append(ret, T(enumVal))
		}
	}
	return ret
}

func StringToBoolSlice(str string) (ret []bool) {
	for _, s := range strings.Split(str, ",") {
		if strings.ToLower(s) == "true" {
			ret = append(ret, true)
		} else if strings.ToLower(s) == "false" {
			ret = append(ret, false)
		}
	}
	return
}

func ExtractCodeFromMarkdown(markdown string) string {
	// 定义正则表达式模式
	re := regexp.MustCompile("(?s)```.*?\\s(.*?)\\s```")

	// 查找匹配的子串
	matches := re.FindStringSubmatch(markdown)

	// 检查是否有匹配结果
	if len(matches) > 1 {
		// 返回提取的代码
		return matches[1]
	}

	return ""
}

func Vector2String(vec []float32) string {
	if len(vec) == 0 {
		return ""
	}
	builder := strings.Builder{}
	builder.Grow(len(vec) * 12) // 预分配足够的空间，12是一个预估值
	// 添加第一个元素
	builder.WriteString(strconv.FormatFloat(float64(vec[0]), 'f', -1, 32))
	// 添加剩余元素
	for _, v := range vec[1:] {
		builder.WriteByte(',')
		builder.WriteString(strconv.FormatFloat(float64(v), 'f', -1, 32))
	}
	return builder.String()
}
