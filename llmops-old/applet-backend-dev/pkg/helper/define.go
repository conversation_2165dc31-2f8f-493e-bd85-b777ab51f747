package helper

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type (
	Param        = string
	ParamDesc    = string
	ParamType    = string
	ParamDefault = string
)

const (
	PathParamAppletID     Param = "id"
	PathParamAppletRunID  Param = "run_id"
	PathParamAppletNodeID Param = "node_id"
	PathParamAppletSnapID Param = "snap_id"

	//PathParamAppletChatID 一轮对话
	PathParamAppletChatID          Param = "chat_id"
	PathParamDynamicWidgetType           = "dynamic_widget_type"
	PathParamDynamicWidgetKey            = "dynamic_widget_key"
	PathParamKnowledgeBaseID       Param = "base_id"
	PathParamDocId                       = "doc_id"
	QueryParamProjectID                  = "project_id"
	QueryParamTenantID                   = "tenant_id"
	KeyParam                             = "key"
	QueryParamContentTypeSelector        = "content_type_selector"
	QueryParamSourceTypeSelector         = "source_type_selector"
	QueryParamRegistryTypeSelector       = "registry_type_selector"
	QueryParamDocId                      = "doc_id"
	QueryParamIds                        = "ids"
	QueryParamIsPublic                   = "is_public"
	QueryParamIsPublishedSelector        = "is_published_selector"
	QueryParamSceneTypeSelector          = "scene_type_selector"
	QueryParamItemSep                    = "," // 请求参数中， 多个项（数组Array或映射Map）之间的分隔符
	QueryParamKVSep                      = "=" // 请求参数中， 单个Map的键值对之间的分符号
	QueryParamKnowledgeBaseID            = "knowledge_base_id"
	QueryParamSearchContent              = "search_content"
	QueryParamMlopsStateSelector         = "mlops_state_selector"
	LLMBasicConfigProjectID        Param = "id"
	PathParamAppID                 Param = "app_id"
	DefaultLimits                        = 10
	QueryParamUpgrade              Param = "upgrade"
	BackendCallbackCancelFormat          = "/chains/-/runs/{%s}/cancel"
)

var (
	LimitQP  = NewQueryParam("limit", "需要的查询结果数")
	SortByQP = NewQueryParam("sort-by",
		fmt.Sprintf("用于排序的字段,%s", strings.Join(utils.GetEnumKeys(pb.SortBy_value), ",")))
)

type ParamDescriptor struct {
	Name        string
	Desc        string
	IsPathParam bool
}

func NewQueryParam(name, desc string) *ParamDescriptor {
	return &ParamDescriptor{
		Name:        name,
		Desc:        desc,
		IsPathParam: false,
	}
}
func NewPathParam(name, desc string) *ParamDescriptor {
	return &ParamDescriptor{
		Name:        name,
		Desc:        desc,
		IsPathParam: true,
	}
}

func (p *ParamDescriptor) GetName() string {
	return p.Name
}

func (p *ParamDescriptor) Param() *restful.Parameter {
	if p.IsPathParam {
		return restful.PathParameter(p.Name, p.Desc)
	}
	return restful.QueryParameter(p.Name, p.Desc)
}

func (p *ParamDescriptor) GetValue(req *restful.Request) string {
	if p.IsPathParam {
		return req.PathParameter(p.Name)
	}
	return req.QueryParameter(p.Name)
}

// GetNonEmptyValue 获取非空值,空值报错
func (p *ParamDescriptor) GetNonEmptyValue(req *restful.Request) (string, error) {
	param := req.QueryParameter(p.Name)
	if param == "" {
		return "", stderr.Internal.Error("failed to get non empty param %s", p.Name)
	}
	return param, nil
}

// GetRequiredValue 获取对应的参数，并判断是否为空
func (p *ParamDescriptor) GetRequiredValue(req *restful.Request) (string, error) {
	if p.IsPathParam {
		v := req.PathParameter(p.Name)
		if v == "" {
			return "", stderr.Internal.Error("failed to get path param %s", p.Name)
		}
		return v, nil
	}
	v := req.QueryParameter(p.Name)
	if v == "" {
		return "", stderr.Internal.Error("failed to get query param %s", p.Name)
	}
	return v, nil
}

func (p *ParamDescriptor) GetIntValue(req *restful.Request) (int, error) {
	return p.GetIntValueWithDefault(req, 0)
}
func (p *ParamDescriptor) GetBoolValue(req *restful.Request) (bool, error) {
	v := ""
	if p.IsPathParam {
		v = req.PathParameter(p.Name)
	} else {
		v = req.QueryParameter(p.Name)
	}
	stdlog.Debugf("%s original value is %s", p.Name, v)
	if v == "" {
		return false, nil
	}
	bv, err := strconv.ParseBool(v)
	if err != nil {
		return false, stderr.InvalidParam.Cause(err, "invalid param %s", p.Name, v)
	}
	return bv, nil
}
func (p *ParamDescriptor) GetIntValueWithDefault(req *restful.Request, defV int) (int, error) {
	v := ""
	if p.IsPathParam {
		v = req.PathParameter(p.Name)
	} else {
		v = req.QueryParameter(p.Name)
	}
	if v == "" {
		return defV, nil
	}
	iv, err := strconv.Atoi(v)
	if err != nil {
		return 0, stderr.InvalidParam.Cause(err, "invalid param %s : %s", p.Name, v)
	}
	return iv, nil
}

func (p *ParamDescriptor) GetValueAsArr(req *restful.Request) []string {
	vs := strings.Split(p.GetValue(req), QueryParamItemSep)
	for i, v := range vs {
		v = strings.TrimSpace(v)
		if v == "" {
			vs = append(vs[:i], vs[i+1:]...)
		}
	}
	return vs
}

func (p *ParamDescriptor) GetValueAsMap(req *restful.Request) map[string]string {
	items := p.GetValueAsArr(req)
	kvs := make(map[string]string, 0)
	for _, item := range items {
		kv := strings.Split(item, QueryParamKVSep)
		if len(kv) != 2 {
			stdlog.Warnf("invalid map key value pair item in query param: %s", item)
			continue
		}
		kvs[kv[0]] = kv[1]
	}
	return kvs
}
func (p *ParamDescriptor) GetTimeSecondsWithDefault(req *restful.Request, seconds int) (int, error) {
	timeStr := req.QueryParameter(p.Name)
	if timeStr == "" {
		return seconds, nil
	}
	dur, err := time.ParseDuration(timeStr)
	if err != nil {
		return -1, err
	}
	return int(dur.Seconds()), nil
}

func GetCancelPath(taskID string) string {
	pathWithRoot := "api/v1/applet" + BackendCallbackCancelFormat
	path := fmt.Sprintf(pathWithRoot, taskID)
	path = strings.ReplaceAll(path, "{", "")
	path = strings.ReplaceAll(path, "}", "")
	return path
}
