package helper

import "transwarp.io/applied-ai/aiot/vision-std/stderr"

const (
	appletBaseCode     = 150
	appletToolBaseCode = 150
)

var (
	AppletChainInvalidNoWidgetErr      = stderr.NewCode(appletBaseCode, "应用链缺失算子", "Applet chain miss widgets")
	AppletChainBuildScriptErr          = stderr.NewCode(appletBaseCode, "构建应用链引擎脚本失败", "Build applet chain script error")
	AppletChainInvalidErr              = stderr.NewCode(appletBaseCode, "应用链不合法", "Applet chain invalid")
	AppletChainCustomWidgetRegisterErr = stderr.NewCode(appletBaseCode, "自定义算子注册错误", "Custom widget register err")
	AppletChainDebugStateErr           = stderr.NewCode(appletBaseCode, "应用链调试状态错误", "Applet chain debug state error")
	ToolDeleteVerifyErr                = stderr.NewCode(appletToolBaseCode, "工具集删除校验未通过", "Tool delete verify failed")
	ToolUpdateVerifyErr                = stderr.NewCode(appletToolBaseCode, "工具集更新校验未通过", "Tool update verify failed")
	ToolInvalidErr                     = stderr.NewCode(appletToolBaseCode, "工具集不合法", "Tool collection invalid")
	ToolMetaInfoParserErr              = stderr.NewCode(appletToolBaseCode, "工具集元信息解析失败", "Parser tool meta info failed")
	ToolCallAPIErr                     = stderr.NewCode(appletToolBaseCode, "工具调用失败", "Tool call failed")
	ToolPublishVerifyErr               = stderr.NewCode(appletToolBaseCode, "工具集发布校验未通过", "Tool publish verify failed")
	ToolExecuteErr                     = stderr.NewCode(appletToolBaseCode, "工具集调用失败", "Tool execute failed")
)

var HttpErrCodeMsg = map[int]string{
	429: "达到限流限制",
}
