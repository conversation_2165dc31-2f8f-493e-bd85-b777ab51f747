package helper

import (
	"fmt"
	"os"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"transwarp.io/applied-ai/aiot/vision-std/stdsync"
)

const (
	DefaultHippoSvcName = "autocv-hippo-service"
)

var (
	// TODO 项目空间的hippo地址会变化么？是否能缓存
	projHippoAddrCache      = make(map[string]string)
	getProjHippoAddrLockMap = stdsync.NewLockMap()

	hippoAddrOverride = make(map[string]string)
)

func init() {
	s := os.Getenv("HIPPO_ADDR_OVERRIDE")
	if s != "" {
		sp := strings.Split(s, ";")
		for _, p := range sp {
			pair := strings.Split(p, ":")
			if len(pair) != 2 {
				continue
			}
			hippoAddrOverride[pair[0]] = pair[1]
		}
	}
}

// GetProjHippoAddr 查询项目租户空间的hippo地址
func GetProjHippoAddr(projectId, token string) (string, error) {
	if v, ok := hippoAddrOverride[projectId]; ok {
		return v, nil
	}
	l := getProjHippoAddrLockMap.Get(projectId)
	l.Lock()
	defer l.Unlock()
	if addr, ok := projHippoAddrCache[projectId]; ok {
		return addr, nil
	}

	tenant, err := stdsrv.GetProjectTenant(projectId, token)
	if err != nil {
		return "", stderr.Wrap(err, "stdsrv.GetProjectTenant")
	}
	if tenant.TenantUid == "" {
		return "", stderr.Internal.Error("tenant.TenantUid is empty")
	}

	ok, msg := stdsrv.GetTenantHippo(tenant.TenantUid, token)
	if !ok {
		return "", stderr.Internal.Error(msg)
	}

	if tenant.HippoServiceName == "" {
		tenant.HippoServiceName = DefaultHippoSvcName
	}
	addr := fmt.Sprintf("%s.%s", tenant.HippoServiceName, tenant.TenantUid)
	projHippoAddrCache[projectId] = addr
	return addr, nil
}

func StoreDirBase(tenantId, projectId string) string {
	return fmt.Sprintf("tenants/%s/projs/%s/", tenantId, projectId)
}
