package helper

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
)

const (
	DefaultPage     = 1
	DefaultPageSize = 20
)

func ReadEntity[T any](req *restful.Request) (*T, error) {
	ret := new(T)
	if err := req.ReadEntity(ret); err != nil {
		return nil, err
	}
	return ret, nil
}

func ParsePageReq(pageReq *pb.PageReq, total, maxPageSize int) (offset, limit int) {
	var page, pageSize int
	if pageReq == nil {
		page, pageSize = DefaultPage, DefaultPageSize
	} else {
		page, pageSize = int(pageReq.Page), int(pageReq.PageSize)
	}
	if pageSize > maxPageSize {
		pageSize = maxPageSize
	}
	if page <= 0 {
		page = DefaultPage
	}
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	// 计算偏移量和限制数量
	offset = (page - 1) * pageSize
	limit = pageSize

	// 如果偏移量超过总记录数，则将偏移量重置为0
	if offset >= total {
		offset = 0
		page = 1
	}

	// 如果偏移量加上限制数量超过总记录数，则将限制数量调整为剩余的记录数
	if offset+limit > total {
		limit = total - offset
		pageSize = limit
	}

	// 如果是倒序，则需要调整偏移量和限制数量
	if pageReq.IsDesc {
		offset = total - offset - limit
	}

	// 返写page和pagesize
	pageReq.Page, pageReq.PageSize = int32(page), int32(pageSize)

	return
}
