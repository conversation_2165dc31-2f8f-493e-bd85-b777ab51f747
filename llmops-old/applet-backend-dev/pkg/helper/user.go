package helper

import (
	"context"
	"net/http"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"github.com/go-faster/errors"
	"google.golang.org/grpc/metadata"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	userIDKey      = "Ctx-User-ID"
	projectIDKey   = "Ctx-Project-ID"
	tenantIDKey    = "Ctx-Tenant-ID"
	tokenKey       = "Ctx-Token"
	languageKey    = "Ctx-Language"
	AuthHeaderKey  = "Authorization"
	projectIDParam = "project_id"
	tenantIDParam  = "tenantId"
)

func GenNewCtx(req *restful.Request) context.Context {
	ctx := req.Request.Context()
	jwtToken := auth.GetAuthContext(req)
	userID := jwtToken.GetUsername()
	token := jwtToken.Token()
	projectID := req.QueryParameter(projectIDParam)
	if projectID != "" {
		ctx = SetProjectID(ctx, projectID)
	}
	if tenantId := req.QueryParameter(tenantIDParam); tenantId != "" {
		ctx = SetTenantID(ctx, tenantId)
	} else if projectID != "" && token != "" {
		// 尝试调用cas接口获取
		if tenantId, _ = stdsrv.GetProjectTenantUid(projectID, token); tenantId != "" {
			ctx = SetTenantID(ctx, tenantId)
		}
	}
	ctx = SetUser(ctx, userID)
	ctx = SetToken(ctx, token)
	ctx = SetLanguage(ctx, stdsrv.GetLanguage(req))
	ctx = SetProjectIDAndTokenForGRPC(ctx, projectID, token)
	return ctx
}

// GenCtxFromRequest 尝试从http请求体中解析携带的用户授权信息
// 如解析失败，则返回原始 ret == ctx , ok = false
// 如解析成功，则基于原始 ctx 封装出新的 ctx 并返回
func GenCtxFromRequest(r *http.Request) (ret context.Context, ok bool) {
	if r == nil {
		stdlog.Warnf("giving http request is nil")
		return nil, false
	}
	ctx := r.Context()
	jwt, err := auth.ParseTokenFromRequest(r)
	if err != nil {
		stdlog.WithError(err).Warnf("parse token from req")
		return ctx, false
	}
	userID := jwt.GetUsername()
	projectID := r.URL.Query().Get(projectIDParam)
	if projectID != "" {
		ctx = SetProjectID(ctx, projectID)
	}
	if tenantId := r.URL.Query().Get(tenantIDParam); tenantId != "" {
		ctx = SetTenantID(ctx, tenantId)
	}
	ctx = SetUser(ctx, userID)
	ctx = SetToken(ctx, jwt.Token())
	ctx = SetProjectIDAndTokenForGRPC(ctx, projectID, jwt.Token())
	return ctx, true
}

// FillContextWithGrpcInfo 通过grpc-ctx填充token与projectId
func FillContextWithGrpcInfo(ctx context.Context) context.Context {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return ctx
	}
	token, ok := md[strings.ToLower(AuthHeaderKey)]
	if ok && token[0] != "" {
		ctx = SetToken(ctx, token[0])
	}
	projectId, ok := md[strings.ToLower(projectIDKey)]
	if ok && projectId[0] != "" {
		ctx = SetProjectID(ctx, projectId[0])
	}
	tenantId, ok := md[strings.ToLower(tenantIDKey)]
	if ok && tenantId[0] != "" {
		ctx = SetTenantID(ctx, tenantId[0])
	}
	return ctx
}

func GenNewCtxWithCancel(req *restful.Request) (context.Context, context.CancelFunc) {
	ctx := req.Request.Context()
	userID := auth.GetAuthContext(req).GetUsername()
	ctx = SetUser(ctx, userID)
	return context.WithCancel(ctx)
}

func SetUser(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, userIDKey, userID)
}

func SetToken(ctx context.Context, token string) context.Context {
	return context.WithValue(ctx, tokenKey, token)
}

func SetProjectID(ctx context.Context, projectID string) context.Context {
	return context.WithValue(ctx, projectIDKey, projectID)
}

func SetTenantID(ctx context.Context, tenantID string) context.Context {
	return context.WithValue(ctx, tenantIDKey, tenantID)
}

func SetLanguage(ctx context.Context, lang stdsrv.Language) context.Context {
	return context.WithValue(ctx, languageKey, lang)
}

// WithGrpcCtx 透传grpc context
func WithGrpcCtx(ctx context.Context) context.Context {
	// 上游服务传进来的context
	oriMD, exist := metadata.FromIncomingContext(ctx)
	if !exist {
		oriMD = metadata.New(make(map[string]string))
	}
	// 当前服务设置的context
	currMD, exist := metadata.FromOutgoingContext(ctx)
	if !exist {
		currMD = metadata.New(make(map[string]string))
	}
	resMD := metadata.Join(oriMD, currMD)
	ctx = metadata.NewOutgoingContext(ctx, resMD)
	return ctx
}

func SetProjectIDAndTokenForGRPC(ctx context.Context, projectID string, token string) context.Context {
	if projectID != "" {
		ctx = SetContextForGRPC(ctx, projectIDKey, projectID)
	}
	if token != "" {
		ctx = SetContextForGRPC(ctx, AuthHeaderKey, token)
	}
	return ctx
}

func SetContextForGRPC(ctx context.Context, k string, v string) context.Context {
	MD, ok := metadata.FromOutgoingContext(ctx)
	if !ok {
		MD = metadata.New(make(map[string]string))
	}
	MD.Set(k, v)
	return metadata.NewOutgoingContext(ctx, MD)
}

func GetUser(ctx context.Context) (string, error) {
	if v := ctx.Value(userIDKey); v == nil {
		return "", errors.Errorf("no user in context")
	} else if r, ok := v.(string); !ok {
		return "", errors.Errorf("no user in context")
	} else if r == "" {
		return "", errors.Errorf("no user in context")
	} else {
		return r, nil
	}
}

func GetToken(ctx context.Context) (string, error) {
	if v := ctx.Value(tokenKey); v == nil {
		return "", errors.Errorf("no token in context")
	} else if r, ok := v.(string); !ok {
		return "", errors.Errorf("no token in context")
	} else if r == "" {
		return "", errors.Errorf("no token in context")
	} else {
		return r, nil
	}
}

func GetProjectID(ctx context.Context) string {
	if v := ctx.Value(projectIDKey); v == nil {
		return ""
	} else if r, ok := v.(string); !ok {
		return ""
	} else if r == "" {
		return ""
	} else {
		return r
	}
}

func GetLanguage(ctx context.Context) stdsrv.Language {
	value := ctx.Value(languageKey)
	if value == nil {
		return stdsrv.LanguageChinese
	}
	lang, ok := value.(stdsrv.Language)
	if !ok {
		return stdsrv.LanguageChinese
	}
	return lang
}

func IsChinese(ctx context.Context) bool {
	return GetLanguage(ctx) == stdsrv.LanguageChinese
}

func GetTenantID(ctx context.Context) string {
	if v := ctx.Value(tenantIDKey); v == nil {
		return ""
	} else if r, ok := v.(string); !ok {
		return ""
	} else if r == "" {
		return ""
	} else {
		return r
	}
}

func GetUserContext(ctx context.Context) (*pb.UserContext, error) {
	user, err := GetUser(ctx)
	if err != nil {
		return nil, err
	}
	projectId := GetProjectID(ctx)
	ret := &pb.UserContext{
		UserId:    user,
		UserName:  user,
		ProjectId: projectId,
	}
	return ret, nil
}

// NewContextWithBaseInfo 生成一个包含后续http/grpc调用凭证的一个context
// token 可在登录平台后，通过以下方式为指定用户生成新的Token
// curl -X POST \
// "https://172.17.120.207:30745/gateway/cas/api/admins/token" \
// -H "accept: application/json" \
// -H "Content-Type: application/json" \
// -d '
//
//	{
//	  "username": "thinger"
//	}
//	'
func NewContextWithBaseInfo(parent context.Context, tenantID, projID, token string) context.Context {
	ctx := parent
	jwt, err := auth.ParseToken(token)
	if err != nil {
		stdlog.WithError(err).Errorf("parse token '%s'", token)
		// 解析失败也继续
	}
	return newContextWithBaseInfo(ctx, tenantID, projID, jwt)
}

func CopyBaseInfoBetweenContexts(src, dst context.Context) context.Context {
	var tenantID, projID, token string
	tenantID = GetTenantID(src)
	projID = GetProjectID(src)
	token, _ = GetToken(src)
	return NewContextWithBaseInfo(dst, tenantID, projID, token)
}

func newContextWithBaseInfo(parent context.Context, tenantID, projID string, jwt auth.JWToken) context.Context {
	ctx := parent
	userID := jwt.GetUsername()
	if projID != "" {
		ctx = SetProjectID(ctx, projID)
	}
	if tenantID != "" {
		ctx = SetTenantID(ctx, tenantID)
	}
	ctx = SetUser(ctx, userID)
	ctx = SetToken(ctx, jwt.Token())
	ctx = SetProjectIDAndTokenForGRPC(ctx, projID, jwt.Token())
	return ctx
}
