package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyChatHistory        = "WidgetKeyChatHistory"
	ParamIDChatInput            = "ChatInput"
	defaultMaxChatHistoryRounds = 5
)

type chatHistory struct {
}

var widgetChatHistory = &Widget{
	Id:    WidgetKeyChatHistory,
	Name:  "对话历史",
	Desc:  "用于将历史对话作为背景知识给下游提示词模版",
	Group: WidgetGroupInput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeReqInput,
			ParamLimits: BaseSyncLimits(DataTypeQAItems),
			Define: DynamicParam{
				Id:   ParamIDChatInput,
				Name: "对话输入",
				Desc: BaseSyncLimits(DataTypeQAItems).String(), //inPut参数限制
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "MaxRounds",
				Name:     "最大对话轮次",
				Desc:     "历史对话中保留的最大对话轮次, 超出的轮次部分将会被自动截断. 适当的指定该属性可以有效避免超出模型的最大上下文上限",
				DataType: pb.DynamicParam_DATA_TYPE_INT,
				Type:     pb.DynamicParam_TYPE_NUMBER,
				NumberRange: &pb.NumberRange{
					Min:  1,
					Max:  99,
					Step: 1,
				},
				DefaultValue: "5",
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "Tmpl",
				Name:         "对话模板",
				Desc:         "使用GoTemplate把输入的对话历史结构拼接成字符串",
				Type:         pb.DynamicParam_TYPE_CODE_JSONNET,
				DefaultValue: "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (f chatHistory) Define() *Widget {
	return widgetChatHistory
}

func (f chatHistory) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	tmpl, err := getNodeValueToString(nodeValue, "Tmpl", true)
	if err != nil {
		return nil, err
	}
	v := getInt64ValueWithDefault(nodeValue, "MaxRounds", defaultMaxChatHistoryRounds)
	if v <= 0 {
		stdlog.Warnf("invalid max rounds: %d, set to default value: %d", v, defaultMaxChatHistoryRounds)
		v = defaultMaxChatHistoryRounds
	}

	return script.ChatHistory{
		Meta:      nodeMeta.ToBaseScript(),
		Tmpl:      script.MultilineString(tmpl),
		MaxRounds: v,
	}, nil
}
