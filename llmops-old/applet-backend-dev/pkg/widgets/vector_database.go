package widgets

import (
	"fmt"
	"net/url"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

// widgetkey
const (
	WidgetKeyVDInputs = "WidgetKeyVectorDBInputs"
	WidgetKeyVDSearch = "WidgetKeyVectorDBSearch"
)

// widget param
const (
	WidgetVDSearchParamConnection = "DataSourceConnection"
	WidgetVDSearchParamTopK       = "TopK"
	WidgetVDSearchParamThresh     = "Thresh"
	WidgetVDSearchParamTable      = "Table"
	WidgetVDSearchParamDimension  = "Dimension"
)

func init() {
	widgetVectorInput.Params = append(widgetVectorInput.Params, commonVectorParams...)
	widgetVectorInput.Params = append(widgetVectorInput.Params, vectorInputEndParams...)
	widgetVectorSearch.Params = append(widgetVectorSearch.Params, commonVectorParams...)
	widgetVectorSearch.Params = append(widgetVectorSearch.Params, vectorSearchEndParams...)
}

func getHippoDataSource() string {
	path := helper.BaseDynamicDatasourceUrl
	queryParams := url.Values{}
	queryParams.Add(helper.QueryParamKey, helper.HippoDataSource)
	path = fmt.Sprintf("%s?%s", path, queryParams.Encode())
	return path
}

type vectorInputs struct {
}

var widgetVectorInput = &Widget{
	Id:    WidgetKeyVDInputs,
	Name:  "向量库写入",
	Desc:  "用于添加向量索引与对应文本",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeVectorInsert),
			Define: DynamicParam{
				Id:       "Text",
				Name:     "插入数据",
				Desc:     "插入数据," + BaseSyncLimits(DataTypeVectorInsert).String(),
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
	},
}

var commonVectorParams = []WidgetParam{
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:         WidgetVDSearchParamConnection,
			Name:       "数据源",
			Desc:       "选择“空间/运维空间/数据源管理”列表中的一个hippo/milvus数据源",
			Type:       pb.DynamicParam_TYPE_SELECTOR_DYNAMIC,
			Datasource: getHippoDataSource(),
			DataType:   pb.DynamicParam_DATA_TYPE_STRING,
			Required:   true,
			Multiple:   false,
		},
	},
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:       WidgetVDSearchParamTable,
			Name:     "Table",
			Desc:     "向量数据库表名",
			Type:     pb.DynamicParam_TYPE_INPUT,
			DataType: pb.DynamicParam_DATA_TYPE_STRING,
			Required: true,
		},
	},
}

var vectorInputEndParams = []WidgetParam{
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:       WidgetVDSearchParamDimension,
			Name:     "Dimension",
			Desc:     "向量维度",
			Type:     pb.DynamicParam_TYPE_NUMBER,
			DataType: pb.DynamicParam_DATA_TYPE_INT,
			Required: true,
		},
	},
	{
		DataClass:   DataClassJson,
		Category:    ParamTypeNodeOutPort,
		ParamLimits: BaseSyncLimits(DataTypeVecInsertRes),
		Define: DynamicParam{
			Id:   "OutPut",
			Desc: "输出结果," + BaseSyncLimits(DataTypeVecInsertRes).String(),
		},
	},
}

var vectorSearchEndParams = []WidgetParam{
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:       WidgetVDSearchParamTopK,
			Name:     "TopK",
			Desc:     "向量库召回段落的数量限制",
			Type:     pb.DynamicParam_TYPE_NUMBER,
			DataType: pb.DynamicParam_DATA_TYPE_INT,
			NumberRange: &pb.NumberRange{
				Min:  1,
				Max:  100,
				Step: 1,
			},
			Required:     true,
			DefaultValue: "5",
		},
	},
	{
		DataClass: DataClassString,
		Category:  ParamTypeAttribute,
		Define: DynamicParam{
			Id:       WidgetVDSearchParamThresh,
			Name:     "最小匹配度",
			Desc:     "段落召回时，小于改值的段落将被视为无关文本，不予返回",
			Type:     pb.DynamicParam_TYPE_NUMBER,
			DataType: pb.DynamicParam_DATA_TYPE_FLOAT,
			NumberRange: &pb.NumberRange{
				Min:  0.05,
				Max:  1.00,
				Step: 0.05,
			},
			Required:     true,
			DefaultValue: "0.75",
		},
	},
	{
		DataClass:   DataClassJson,
		Category:    ParamTypeNodeOutPort,
		ParamLimits: BaseSyncLimits(DataTypeString),
		Define: DynamicParam{
			Id:   "OutPut",
			Desc: "输出结果," + BaseSyncLimits(DataTypeString).String(),
		},
	},
}

func (v vectorInputs) Define() *Widget {
	return widgetVectorInput
}

func (v vectorInputs) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	urlStr, dbName, err := constructUrlAndDbName(nodeValue)
	if err != nil {
		return nil, err
	}
	return script.VecInsert{
		Meta:     nodeMeta.ToBaseScript(),
		Database: dbName,
		URL:      urlStr,
	}, nil
}

var widgetVectorSearch = &Widget{
	Id:    WidgetKeyVDSearch,
	Name:  "向量库检索",
	Desc:  "用于基于向量相似度的文本检索",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeVectorInsert),
			Define: DynamicParam{
				Id:       "SearchText",
				Name:     "文本向量",
				Desc:     BaseSyncLimits(DataTypeVectorInsert).String(),
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
	},
}

type vectorSearch struct {
}

func (v vectorSearch) Define() *Widget {
	return widgetVectorSearch
}

func (v vectorSearch) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	urlStr, dbName, err := constructUrlAndDbName(nodeValue)
	if err != nil {
		return nil, err
	}
	topK, err := getNodeValueToInt64(nodeValue, WidgetVDSearchParamTopK, true)
	if err != nil {
		return nil, err
	}
	thresh, err := getNodeValueToFloat64(nodeValue, WidgetVDSearchParamThresh, true)
	if err != nil {
		return nil, err
	}
	return script.VecSearch{
		Meta:      nodeMeta.ToBaseScript(),
		Database:  dbName,
		URL:       urlStr,
		TopK:      topK,
		Threshold: thresh,
	}, nil
}

func constructUrlAndDbName(nodeValue map[string]interface{}) (string, string, error) {
	// 下拉框中不包含 dimension 和 table 的信息，仍需手动填写
	tableStr, err := getNodeValueToString(nodeValue, WidgetVDSearchParamTable, true)
	if err != nil {
		return "", "", err
	}

	dimensionInt, err := getNodeValueToInt64(nodeValue, WidgetVDSearchParamDimension, true)
	if err != nil {
		return "", "", err
	}

	// “空间-运维工具-数据源管理”中单条 DataSource Connection 的所有信息被封装成 JSON
	dataConnectionInfo, err := getNodeValueToString(nodeValue, WidgetVDSearchParamConnection, true)

	var dataConnection pb.DataConnection

	// 反序列化，提取出来关键信息，用来拼接 url
	err = stdsrv.UnmarshalMixWithProto(dataConnectionInfo, &dataConnection)

	if err != nil {
		stdlog.Errorln("unable to Unmarshal response string into type: 'pb.DataConnection'.")
		return "", "", err
	}

	urlStr := fmt.Sprintf("%s://%s:%s@%s:%s?table=%s&dimension=%d", strings.ToLower(pb.ConnectionType_name[int32(dataConnection.Type)]), dataConnection.Username, dataConnection.Password, dataConnection.Address, dataConnection.Port, tableStr, dimensionInt)
	return urlStr, dataConnection.Name, nil
}

type NodeOutPortText2Vector = NodeInPortVectorInsert
type NodeInPortVectorSearch = NodeInPortVectorInsert
type NodeInPortVectorInsert struct {
	FileName             string                       `json:"file_name" description:"文件名,目前可不传。"`
	OpenAiTextVectorReq  *triton.OpenAiTextVectorReq  `json:"openai_embedding_req" description:"向量化请求。"`
	OpenAiTextVectorResp *triton.OpenAiTextVectorResp `json:"openai_embedding_resp" description:"向量化响应。"`
}
