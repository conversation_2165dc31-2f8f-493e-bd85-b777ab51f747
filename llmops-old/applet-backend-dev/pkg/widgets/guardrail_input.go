package widgets

import (
	"context"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyInputGuardrail = "WidgetKeyInputGuardrail"

	InputGuardrailParamIDInput      = "Input"
	InputGuardrailParamIDStrategy   = "Strategy"
	InputGuardrailParamIDStrategyID = "StrategyID"
)

type inputGuardrail struct {
}

var widgetInputGuardrail = &Widget{
	Id:    WidgetKeyInputGuardrail,
	Name:  "输入安全护栏",
	Desc:  "针对输入进行安全检测并配置输出干预话术",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:       InputGuardrailParamIDInput,
				Name:     "待检测文本",
				Desc:     BaseSyncLimits(DataTypeString).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       InputGuardrailParamIDStrategy,
				Name:     "输入安全策略",
				Desc:     "点击配置提示词注入、敏感词防护等安全策略详情",
				Type:     pb.DynamicParam_TYPE_GUARDRAIL_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       InputGuardrailParamIDStrategyID,
				Name:     "配置策略id",
				Desc:     "配置策略id",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Hidden:   true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "通过安全防护的文本，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

type WidgetParamsInputGuardrail struct {
	Enable       bool         `json:"enable" description:"是启用该算子"`
	SafetyConfig SafetyConfig `json:"safety_config" description:"安全围栏配置"`
}

func (f inputGuardrail) Define() *Widget {
	return widgetInputGuardrail
}

func (f inputGuardrail) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	inputGuardrails, err := GetStringNodeValueTo[InputGuardrails](nodeValue, InputGuardrailParamIDStrategy)
	if err != nil {
		return nil, err
	}
	widgetParams := WidgetParamsInputGuardrail{
		SafetyConfig: SafetyConfig{
			ID:               nodeMeta.NodeID,
			ProjectID:        nodeMeta.NodeID,
			InputGuardrails:  inputGuardrails,
			OutputGuardrails: &OutputGuardrails{},
		},
	}

	switch conf.Config.IsSimpleMode {
	case true: // 轻量化
		widgetParams.Enable = false
	case false:
		widgetParams.Enable = bool(inputGuardrails.PromptInjectGuard.PromptInjectGuardEnabled) ||
			bool(inputGuardrails.SensitiveProtection.SensitiveProtectionEnabled)
		if err := UpdateGuardrailsServerConfig(context.Background(), &widgetParams.SafetyConfig); err != nil {
			return nil, stderr.Wrap(err, "update guardrail service config")
		}
	}

	return script.InputGuardrail{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(widgetParams)),
	}, nil
}
