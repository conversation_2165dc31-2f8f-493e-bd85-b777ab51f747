package widgets

import (
	"fmt"
	"net/url"
	"strconv"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextEnhance        = "WidgetKeyTextEnhance"
	ParamIDText                 = "Text"
	ParamIDEnhanceMode          = "EnhanceMode"
	ParamIDNum                  = "Num"
	ParamIDModelService         = "ModelService"
	ParamIDCustomSummaryPrompt  = "CustomSummaryPrompt"
	ParamIDCustomQuestionPrompt = "CustomQuestionPrompt"
	ParamIDCustomPrompt         = "CustomPrompt"
)

var (
	AugmentedModeQuestion = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION.String()
	AugmentedModeSummary  = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY.String()
	AugmentedModeCustom   = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_CUSTOM.String()
)

func getTextEnhanceModeDataSource() string {
	path := helper.BaseDynamicDatasourceUrl
	queryParams := url.Values{}
	queryParams.Add(helper.QueryParamKey, helper.TextEnhanceModeDataSource)
	path = fmt.Sprintf("%s?%s", path, queryParams.Encode())
	return path
}

func getTextGenModelDatasource() string {
	path := helper.BaseDynamicDatasourceUrl
	queryParams := url.Values{}
	queryParams.Add(helper.QueryParamKey, helper.ModelServiceDataSource)
	queryParams.Add(helper.QueryParamSubKind, pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION.String())
	path = fmt.Sprintf("%s?%s", path, queryParams.Encode())
	return path
}

// DefaultTextEnhanceMode 支持的运行模式
var DefaultTextEnhanceMode = map[pb.AugmentedChunkType]string{
	pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY:  "段落总结",
	pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION: "段落提问",
	//pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_DESCRIPTION:      "段落描述",
	//pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE: "问题改写",
}

var widgetTextEnhance = &Widget{
	Id:    WidgetKeyTextEnhance,
	Name:  "文本增强",
	Desc:  "对切分的段落进行总结或者尝试提出若干问题",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:   ParamIDText,
				Name: "输入段落",
				Desc: "文本切分后的段落," + BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnhanceMode,
				Name:         "增强模式",
				Desc:         "选择文本增强模式，对每个段落进行总结、提问等，增加语义丰富度，更有利于检索",
				Type:         pb.DynamicParam_TYPE_SELECTOR,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Datasource:   fmt.Sprintf("%s@@段落总结,%s@@段落提问,%s@@自定义增强", AugmentedModeSummary, AugmentedModeQuestion, AugmentedModeCustom),
				DefaultValue: AugmentedModeSummary,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDCustomSummaryPrompt,
				Name:     "总结提示词",
				Desc:     "自定义总结提示词",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				Required: true,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
				DefaultValue: clients.DefaultInstructionTextSummary,
				Precondition: BuildBothPreconditionString(ParamIDEnhanceMode, AugmentedModeSummary),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDNum,
				Name:         "提问数量",
				Desc:         "对每个段落的提问数量限制",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				DefaultValue: strconv.Itoa(script.TextEnhanceDefaultQuestionNum),
				Required:     true,
				NumberRange: &pb.NumberRange{
					Min: script.TextEnhanceMinQuestionNum,
					Max: script.TextEnhanceMaxQuestionNum,
				},
				Precondition: BuildBothPreconditionString(ParamIDEnhanceMode, AugmentedModeQuestion),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDCustomQuestionPrompt,
				Name:     "提问提示词",
				Desc:     "自定义提问提示词",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				Required: true,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
				DefaultValue: clients.DefaultInstructionTextQuestion,
				Precondition: BuildBothPreconditionString(ParamIDEnhanceMode, AugmentedModeQuestion),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDCustomPrompt,
				Name:     "自定义提示词",
				Desc:     "自定义增强提示词",
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				Required: true,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
				DefaultValue: clients.DefaultInstructionTextCustom,
				Precondition: BuildBothPreconditionString(ParamIDEnhanceMode, AugmentedModeCustom),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDModelService,
				Name:       "模型服务",
				Desc:       "用于进行文本增强的模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetLLMModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "附带增强文本的段落，" + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
	},
}

type WidgetParamsTextEnhance struct {
	Modes              []pb.AugmentedChunkType             `json:"modes"`
	Prompts            []string                            `json:"prompts"`
	Num                int                                 `json:"num"`
	ModelToolDescriber agent_definition.ModelToolDescriber `json:"model_tool_describe"`
}

type textEnhance struct {
}

func (te textEnhance) Define() *Widget {
	return widgetTextEnhance
}

func (te textEnhance) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	modeStr, err := getNodeValueToString(nodeValue, ParamIDEnhanceMode, false)
	if err != nil {
		return nil, err
	}

	var mode pb.AugmentedChunkType
	var prompt string
	switch modeStr {
	case AugmentedModeSummary:
		mode = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY
		prompt = getStringValueWithDefault(nodeValue, ParamIDCustomSummaryPrompt, clients.DefaultInstructionTextSummary)
	case AugmentedModeQuestion:
		mode = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION
		prompt = getStringValueWithDefault(nodeValue, ParamIDCustomQuestionPrompt, clients.DefaultInstructionTextQuestion)
	case AugmentedModeCustom:
		mode = pb.AugmentedChunkType_AUGMENTED_CHUNK_TYPE_CUSTOM
		prompt = getStringValueWithDefault(nodeValue, ParamIDCustomPrompt, clients.DefaultInstructionTextCustom)
	default:
		return nil, stderr.Internal.Errorf("unsupported enhance mode: %s", modeStr)
	}

	var num int64 = 1 // 默认生成一个
	if modeStr == AugmentedModeQuestion {
		num, err = getNodeValueToInt64(nodeValue, ParamIDNum, false)
		if err != nil {
			return nil, err
		}
		if num <= 0 {
			return nil, stderr.Error("num must be greater than 0")
		}
	}
	modelServiceStr, err := getNodeValueToString(nodeValue, ParamIDModelService, false)
	if err != nil {
		return nil, err
	}
	modelServiceToolDesc := new(agent_definition.ModelToolDescriber)
	if err := stdsrv.UnmarshalMixWithProto(modelServiceStr, modelServiceToolDesc); err != nil {
		return nil, err
	}

	widgetParams := WidgetParamsTextEnhance{
		Modes:              []pb.AugmentedChunkType{mode},
		Prompts:            []string{prompt},
		Num:                int(num),
		ModelToolDescriber: *modelServiceToolDesc,
	}

	SimpleModelService(&widgetParams.ModelToolDescriber.ModelService)
	return script.TextEnhance{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(widgetParams)),
	}, nil
}
