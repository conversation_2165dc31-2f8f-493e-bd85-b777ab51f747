package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyAudioToText = "WidgetKeyAudioToText"
)

type audioToText struct{}

var widgetKeyAudioToText = &Widget{
	Id:    WidgetKeyAudioToText,
	Name:  "音频转文本",
	Desc:  "将输入的音频转为文本",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassFile,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeSFSFile),
			Define: DynamicParam{
				Id:       "Audio",
				Name:     "音频文件",
				Desc:     BaseSyncLimits(DataTypeSFSFile).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "服务",
				Desc:       "服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetAudioToTextModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAudioTranResp),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "音频文件翻译结果," + BaseSyncLimits(DataTypeAudioTranResp).String(),
			},
		},
	},
}

func (a audioToText) Define() *Widget {
	return widgetKeyAudioToText
}

func (a audioToText) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}
	if params.SubKind != pb.ModelSubKind_MODEL_SUB_KIND_SR_STT {
		return nil, stderr.Error("the sub kind of model is error")
	}

	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}
