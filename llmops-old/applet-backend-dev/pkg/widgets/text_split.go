package widgets

import (
	"fmt"
	"net/url"
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

// 文件处理算子

const (
	WidgetGeneralSplitter          = "WidgetGeneralSplitter"
	WidgetKeyTextSplitter          = "WidgetKeyTextSplitter"
	WidgetKeyRecursiveTextSplitter = "WidgetKeyTextSplitterByRecursive"
	WidgetKeyJsonTextSplitter      = "WidgetKeyTextSplitterByJson"
	WidgetKeyCodeTextSplitter      = "WidgetKeyTextSplitterByJson"
	WidgetKeyHtmlTextSplitter      = "WidgetKeyTextSplitterByHtml"
	WidgetKeyMarkdownTextSplitter  = "WidgetKeyTextSplitterByHtml"
	WidgetKeyTokenTextSplitter     = "WidgetKeyTextSplitterByHtml"
	SelectHTML                     = "html"
	SelectText                     = "character"
	SelectRecursive                = "recursive"
	SelectJson                     = "json"
	SpliterSelected                = "SpliterSelected"
)

var (
	TypeList = []string{
		"character",
		"recursive",
		"json",
		"code",
		"html",
		"markdown",
		"token",
	}
	textSplitStringQuerys = []string{
		"type",
		"separator",
		"language",
	}
	textSplitIntQuerys = []string{
		"chunkSize",
		"chunkOverlap",
	}
	textSplitListQuerys = []string{
		"separators",
		"splitHeaders",
	}
	languageList = []string{
		"cpp",
		"go",
		"java",
		"kotlin",
		"js",
		"ts",
		"php",
		"proto",
		"python",
		"rst",
		"ruby",
		"rust",
		"scala",
		"swift",
		"markdown",
		"latex",
		"html",
		"sol",
		"csharp",
		"cobol",
		"c",
		"lua",
		"perl",
		"haskell",
	}
)

type generalSplitter struct{}

// widgetGeneralSplitter 字符文本/html/json/递归文本（四选一）分割
var widgetGeneralSplitter = &Widget{
	Id:    WidgetGeneralSplitter,
	Name:  "长文本转Chunks",
	Desc:  "长文本转Chunks",
	Group: WidgetGroupProcessKnowledge,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeJsonString, DataTypeHtmlString),
			Define: DynamicParam{
				Id:   "Text",
				Name: "文本/json/html",
				Desc: "待分割的文本/json/html," +
					BaseSyncLimits(DataTypeString, DataTypeJsonString, DataTypeHtmlString).String(),
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度，取值范围50-500。文档的正文如果超过设定的[最大长度]，则截取[最大长度]的片段为新文档，随后回溯[文档重叠]个字符，继续向后检查，直到文档结束",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "当前分片与上一个分片重叠的文本长度，取值范围0-50",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         SpliterSelected,
				Name:       "选择分割类型",
				Desc:       "选择分割类型，请与输入保持一致",
				Type:       pb.DynamicParam_TYPE_SELECTOR,
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: fmt.Sprintf("%s@@html分割,%s@@文本分割,%s@@递归文本分割,%s@@json分割", SelectHTML, SelectText, SelectRecursive, SelectJson),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "separators",
				Name:         "分割符",
				Desc:         "分割符, 可以设置多个，遇到所选符号即截断。策略为递归分割时，靠前的分隔符优先级更高。",
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Multiple:     true,
				Precondition: BuildAnyPreconditionString(SpliterSelected, []string{SelectText, SelectRecursive}),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "splitHeaders",
				Name:         "分割头标识",
				Desc:         "分割头标识，例如<h1>",
				Type:         pb.DynamicParam_TYPE_INPUT,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Required:     true,
				Multiple:     true,
				Precondition: BuildBothPreconditionString(SpliterSelected, SelectHTML),
			},
		},

		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "分割后的文本段落," + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
	},
}

func (t generalSplitter) Define() *Widget {
	return widgetGeneralSplitter
}

func (t generalSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	selectedSpliterType := getStringValueWithDefault(nodeValue, SpliterSelected, "")
	return splitterScript(selectedSpliterType, nodeMeta, nodeValue)
}

// textSplitter 调用doc服务的split接口进行文本切割
type textSplitter struct{}

// widgetTextSplitter 字符文本分割
var widgetTextSplitter = &Widget{
	Id:    WidgetKeyTextSplitter,
	Name:  "文本分割",
	Desc:  "用于将长文本分割为多个段落，遇到所选符号即截断，符号之间没有优先级，最终分割后合并到预计最大长度",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "文本",
				Desc:     "待分割的文本，字符串类型",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "separators",
				Name:     "分割符",
				Desc:     "分割符, 可以设置多个，遇到所选符号即截断，符号之间没有优先级，最终分割后合并到预计最大长度",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
				Multiple: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "分割后的文本段落，[]*pb.Chunk类型",
			},
		},
	},
}

func (t textSplitter) Define() *Widget {
	return widgetTextSplitter
}

func (t textSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("character", nodeMeta, nodeValue)
}

type recursiveTextSplitter struct {
}

// widgetRecursiveTextSplitter 递归文本分割
var widgetRecursiveTextSplitter = &Widget{
	Id:    WidgetKeyRecursiveTextSplitter,
	Name:  "递归文本分割",
	Desc:  "根据配置的字符先后顺序递归分割成多个段落",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "文本",
				Desc:     "待分割的文本，字符串类型",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "separators",
				Name:     "递归多分割符",
				Desc:     "递归多分割符, 分割策略为recursive时生效，越靠前的分隔符越优先使用",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: false,
				Multiple: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "分割后的文本段落，[]*pb.Chunk类型",
			},
		},
	},
}

func (t recursiveTextSplitter) Define() *Widget {
	return widgetRecursiveTextSplitter
}

func (t recursiveTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("recursive", nodeMeta, nodeValue)
}

type jsonTextSplitter struct {
}

// widgetJsonTextSplitter json文本分割
var widgetJsonTextSplitter = &Widget{
	Id:    WidgetKeyJsonTextSplitter,
	Name:  "json分割",
	Desc:  "分割json类型的文本",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "Json文本",
				Desc:     "待分割的json文本，字符串类型",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "分割后的文本段落，[]*pb.Chunk类型",
			},
		},
	},
}

func (t jsonTextSplitter) Define() *Widget {
	return widgetJsonTextSplitter
}

func (t jsonTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("json", nodeMeta, nodeValue)
}

type codeTextSplitter struct {
}

// widgetCodeTextSplitter 代码文本分割
var widgetCodeTextSplitter = &Widget{
	Id:    WidgetKeyCodeTextSplitter,
	Name:  "代码分割",
	Desc:  "分割代码类型的文本",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "文本",
				Desc:     "文本",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "language",
				Name:     "代码语言",
				Desc:     "代码语言",
				Type:     pb.DynamicParam_TYPE_SELECTOR,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
				Datasource: func() string {
					values := make([]string, 0)
					for _, language := range languageList {
						values = append(values, fmt.Sprintf("%s@@%s", language, language))
					}
					return strings.Join(values, ",")
				}(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (t codeTextSplitter) Define() *Widget {
	return widgetCodeTextSplitter
}

func (t codeTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("code", nodeMeta, nodeValue)
}

type htmlTextSplitter struct {
}

// widgetHtmlTextSplitter html文本分割
var widgetHtmlTextSplitter = &Widget{
	Id:    WidgetKeyHtmlTextSplitter,
	Name:  "html文本分割",
	Desc:  "分割html文本",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "html文本",
				Desc:     "待分割的html文本，字符串类型",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "splitHeaders",
				Name:     "分割头标识",
				Desc:     "分割头标识，例如<h1>",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
				Multiple: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "分割后的文本段落，[]*pb.Chunk类型",
			},
		},
	},
}

func (t htmlTextSplitter) Define() *Widget {
	return widgetHtmlTextSplitter
}

func (t htmlTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("html", nodeMeta, nodeValue)
}

type markdownTextSplitter struct {
}

// widgetMarkdownTextSplitter markdown文本分割
var widgetMarkdownTextSplitter = &Widget{
	Id:    WidgetKeyMarkdownTextSplitter,
	Name:  "markdown文本分割",
	Desc:  "分割markdown文本",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "文本",
				Desc:     "文本",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "splitHeaders",
				Name:     "分割头标识",
				Desc:     "分割头标识，例如##",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
				Multiple: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (t markdownTextSplitter) Define() *Widget {
	return widgetMarkdownTextSplitter
}

func (t markdownTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("markdown", nodeMeta, nodeValue)
}

type tokenTextSplitter struct {
}

// widgetTokenTextSplitter token文本分割
var widgetTokenTextSplitter = &Widget{
	Id:    WidgetKeyTokenTextSplitter,
	Name:  "token分割",
	Desc:  "按token分割文本",
	Group: WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeInPort,
			Define: DynamicParam{
				Id:       "Text",
				Name:     "文本",
				Desc:     "文本",
				Type:     pb.DynamicParam_TYPE_INPUT,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkSize",
				Name:         "分割长度",
				Desc:         "分割长度",
				DefaultValue: "500",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           "chunkOverlap",
				Name:         "分段最大重叠",
				Desc:         "分段最大重叠",
				DefaultValue: "0",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeNodeOutPort,
			Define: DynamicParam{
				Id: "OutPut",
			},
		},
	},
}

func (t tokenTextSplitter) Define() *Widget {
	return widgetTokenTextSplitter
}

func (t tokenTextSplitter) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return splitterScript("token", nodeMeta, nodeValue)
}

func genUrl(t string, nodeValue map[string]interface{}) (string, error) {
	querys := make([]string, 0)
	querys = append(querys, fmt.Sprintf("type=%s", t))
	for _, q := range textSplitIntQuerys {
		v, err := getNodeValueToInt64(nodeValue, q, true)
		if err != nil {
			return "", err
		}
		if v != 0 {
			querys = append(querys, fmt.Sprintf("%s=%d", q, v))
		}
	}
	for _, q := range textSplitStringQuerys {
		v, err := getNodeValueToString(nodeValue, q, true)
		if err != nil {
			return "", err
		}
		if v != "" {
			querys = append(querys, fmt.Sprintf("%s=%s", q, url.QueryEscape(v)))
		}
	}
	for _, q := range textSplitListQuerys {
		v, err := getNodeValueToStringList(nodeValue, q, true)
		if err != nil {
			return "", err
		}
		if len(v) > 0 {
			for _, value := range v {
				querys = append(querys, fmt.Sprintf("%s=%s", q, url.QueryEscape(value)))
			}
		}
	}
	url := conf.GetDocSvcAddr() + conf.Config.DocSvcConfig.SplitrawApi.Url
	if len(querys) > 0 {
		url = fmt.Sprintf("%s?%s", url, strings.Join(querys, "&"))
	}
	return url, nil
}

func splitterScript(t string, nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	url, err := genUrl(t, nodeValue)
	if err != nil {
		return nil, err
	}
	return script.HttpCall{
		Meta:        nodeMeta.ToBaseScript(),
		URL:         url,
		Header:      "",
		Method:      "POST",
		Timeout:     60,
		ServiceType: script.HTTPCallServiceTypeTextSplit,
	}, nil
}
