package widgets

import (
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextKnowledgeSearch       = "WidgetKeyTextKnowledgeSearch"
	WidgetKeyTextKnowledgeInsert       = "WidgetKeyTextKnowledgeInsert"
	WidgetKeySingleTextKnowledgeSearch = "WidgetKeySingleTextKnowledgeSearch"
	ParamIDQuestion                    = "Question"
	ParamIDSimpleKbInfo                = "SimpleKbInfo"
	ParamIDEnableDocs                  = "EnableDocs"
	ParamIDRecallParams                = "RecallParams"
	ParamIDRerankModel                 = "RerankModel"
	ParamIDRerankTopK                  = "RerankTopK"
	ParamIDRerankThreshold             = "RerankThreshold"
	ParamIDKnowledgeBaseDesc           = "KnowledgeBaseDesc"
	ParamIDEnableMutil                 = "EnableMutil"
	ParamValueFalse                    = "false"
)

type TextKnowledgeSearch struct {
}

var widgetTextKnowledgeSearch = &Widget{
	Id:    WidgetKeyTextKnowledgeSearch,
	Name:  "知识库检索",
	Desc:  "依据输入文本从知识库检索,并对结果排序和筛选",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:       ParamIDQuestion,
				Name:     "输入",
				Desc:     "检索问题," + BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDRerankTopK,
				Name:         "TopK",
				Desc:         "最终保留的检索结果数量",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_INT,
				DefaultValue: "3",
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDRerankThreshold,
				Name:         "Threshold",
				Desc:         "检索结果被保留的最低阈值要求",
				Type:         pb.DynamicParam_TYPE_NUMBER,
				DataType:     pb.DynamicParam_DATA_TYPE_FLOAT,
				DefaultValue: "0",
				Required:     true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDEnableMutil,
				Name:         "跨知识库检索",
				Desc:         "设置为跨知识库检索时，需配置rerank模型以提升检索效果",
				Type:         pb.DynamicParam_TYPE_SWITCH,
				DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
				Required:     true,
				DefaultValue: ParamValueFalse,
			},
		},
		// 单知识库组件
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDSimpleKbInfo,
				Name:     "知识库信息",
				Desc:     "知识库信息",
				Type:     pb.DynamicParam_TYPE_TEXT_KNOWLEDGE_BASE,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: BuildUrlQueryString(map[string]string{
					helper.UrlQueryNameIsPublishSelector: helper.UrlQueryValueTrue,
				}),
				Precondition: BuildBothPreconditionString(ParamIDEnableMutil, ParamValueFalse),
				Required:     true,
			},
		},

		// 多知识库组件
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:   ParamIDKnowledgeBaseDesc,
				Name: "知识库信息",
				Desc: "限定检索的知识库、及文档范围",
				Type: pb.DynamicParam_TYPE_AGENT_SKILL_KNOW_TOOLS,
				Datasource: BuildUrlQueryString(map[string]string{
					helper.UrlQueryNameIsPublishSelector: helper.UrlQueryValueTrue,
				}),
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Precondition: BuildBothPreconditionString(ParamIDEnableMutil, ParamValueTrue),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDRerankModel,
				Name:         "重排模型",
				Desc:         "选择模型,用于对检索结果进行重排和筛选",
				Type:         pb.DynamicParam_TYPE_AGENT_MODEL_API,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Datasource:   GetRerankingModelSvcConditionsStr(),
				Precondition: BuildBothPreconditionString(ParamIDEnableMutil, ParamValueTrue),
			},
		},
		//输出
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeChunks),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "从知识库中召回的文本段落，" + BaseSyncLimits(DataTypeChunks).String(),
			},
		},
	},
}

type WidgetParamsTextKnowSearch agent_definition.KnowledgeBases

func (t TextKnowledgeSearch) Define() *Widget {
	return widgetTextKnowledgeSearch
}
func (t TextKnowledgeSearch) Script(nodeMeta NodeMeta, nodeValue map[string]any) (script.Generator, error) {
	widgetParams, err := t.GetWidgetParams(nodeValue)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to do GetWidgetParams")
	}
	SimpleModelService(widgetParams.RerankParams.Model)
	paramsStr := stdsrv.AnyToString(widgetParams)
	return script.TextKnowledgeSearch{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(paramsStr),
	}, nil
}

func (t TextKnowledgeSearch) GetWidgetParams(nodeValue map[string]any) (*WidgetParamsTextKnowSearch, error) {
	defaultConfig := conf.Config.AgentConfig.KBConfig

	kbDesc := make([]*agent_definition.KnowlHubDescriber, 0)
	rerankParams := new(pb.RerankParams)
	rerankParams.TopK = getInt32ValueWithDefault(nodeValue, ParamIDRerankTopK, defaultConfig.RerankTopK)
	rerankParams.ScoreThreshold = getFloat32ValueWithDefault(nodeValue, ParamIDRerankThreshold, defaultConfig.Threshold)

	// 跨知识库检索-rerankModel
	if !isEmptyString(nodeValue, ParamIDRerankModel) {
		model, err := getRerankModelFromNodeValue(nodeValue, ParamIDRerankModel)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to get RerankModel from node value %+v", nodeValue)
		}
		rerankParams.Model = model
	}

	// 跨知识库检索-多个知识库信息
	if !isEmptyString(nodeValue, ParamIDKnowledgeBaseDesc) {
		v, err := getKnowledgeBaseFromNodeValue(nodeValue, ParamIDKnowledgeBaseDesc)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to get KnowledgeBase from node value %+v", nodeValue)
		}
		kbDesc = append(kbDesc, v...)
	}

	// 单知识库检索-知识库信息
	if !isEmptyString(nodeValue, ParamIDSimpleKbInfo) {
		simpleKbInfo, err := GetStringNodeValueTo[agent_definition.SimpleKbInfo](nodeValue, ParamIDSimpleKbInfo)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to get simpleKbInfo")
		}
		kbDesc = append(kbDesc, &agent_definition.KnowlHubDescriber{ID: simpleKbInfo.ID, IsPublic: simpleKbInfo.IsPublic})
	}
	widgetParams := &WidgetParamsTextKnowSearch{
		MustUse:           true,
		RerankParams:      rerankParams,
		KnowledgeBaseDesc: kbDesc,
	}
	setKbsRerankParams(widgetParams)
	return widgetParams, nil
}

type textKnowledgeInsert struct {
}

var widgetTextKnowledgeInsert = &Widget{
	Id:    WidgetKeyTextKnowledgeInsert,
	Name:  "知识库写入",
	Desc:  "将文本写入相应的知识库",
	Group: WidgetGroupVD,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:       ParamIDQuestion,
				Name:     "输入",
				Desc:     "需要写入知识库的文本，" + BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks).String(),
				Required: true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDSimpleKbInfo,
				Name:     "知识库信息",
				Desc:     "知识库信息",
				Type:     pb.DynamicParam_TYPE_TEXT_KNOWLEDGE_BASE,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Datasource: BuildUrlQueryString(map[string]string{
					helper.UrlQueryNameIsPublishSelector: helper.UrlQueryValueTrue,
				}),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "文本插入结果，字符串类型",
			},
		},
	},
}

type WidgetParamsTextKnowInsert agent_definition.KnowlHubDescriber

func (t textKnowledgeInsert) Define() *Widget {
	return widgetTextKnowledgeInsert
}

func (t textKnowledgeInsert) Script(nodeMeta NodeMeta, nodeValue map[string]any) (script.Generator, error) {
	simpleKbInfo, err := GetStringNodeValueTo[agent_definition.SimpleKbInfo](nodeValue, ParamIDSimpleKbInfo)
	if err != nil {
		return nil, err
	}
	params := &WidgetParamsTextKnowInsert{ID: simpleKbInfo.ID, IsPublic: simpleKbInfo.IsPublic}
	return script.TextKnowledgeInsert{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}

// getKnowledgeBaseFromNodeValue 获取知识库信息，允许为空
func getKnowledgeBaseFromNodeValue(nodeValue map[string]any, paramID string) ([]*agent_definition.KnowlHubDescriber, error) {
	knowledgeBaseStr := getStringValueWithDefault(nodeValue, paramID, "")
	if strings.TrimSpace(knowledgeBaseStr) == "" {
		return nil, nil
	}

	var kbDescs []*agent_definition.KnowlHubDescriber
	if err := stdsrv.UnmarshalMixWithProto(knowledgeBaseStr, &kbDescs); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal knowledge base info")
	}
	return kbDescs, nil
}

// getRerankModelFromNodeValue 获取重排模型信息，允许为空
func getRerankModelFromNodeValue(nodeValue map[string]any, paramID string) (*pb.ModelService, error) {
	rerankModelStr := getStringValueWithDefault(nodeValue, paramID, "")
	if strings.TrimSpace(rerankModelStr) == "" {
		return nil, nil
	}

	model := new(pb.ModelService)
	if err := stdsrv.UnmarshalMixWithProto(rerankModelStr, model); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal rerank model %s", rerankModelStr)
	}
	return model, nil
}

func setKbsRerankParams(widgetParams *WidgetParamsTextKnowSearch) {
	kbs := agent_definition.KnowledgeBases(*widgetParams)
	agent_definition.SetKbsRerankParams(&kbs)
}
