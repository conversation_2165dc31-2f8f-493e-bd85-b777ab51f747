package widgets

import (
	"strconv"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyGoTo = "WidgetKeyGoTo"
)

const (
	GotoNextNodeParamKey = "targetNode"
	MaxLoopDefault       = 5
)

type wGoTo struct {
}

var widgetWGoTo = &Widget{
	Id:    WidgetKeyGoTo,
	Name:  "循环跳转",
	Desc:  "用于改变数据的流向，把数据传输到之前的算子实现循环",
	Group: WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入数据",
				Desc: "待改变流向的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       "maxLoopRounds",
				Name:     "最大轮次数",
				Desc:     "最大循环次数，要求是正整数",
				DataType: pb.DynamicParam_DATA_TYPE_INT,
				Type:     pb.DynamicParam_TYPE_NUMBER,
				NumberRange: &pb.NumberRange{
					Min: script.GoToMinMaxLoopRounds,
					Max: script.GoToMaxMaxLoopRounds,
				},
				Required:     true,
				DefaultValue: strconv.Itoa(script.GoToDefaultMaxLoopRounds),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "原样输出输入的数据，" + BaseSyncLimits(DataTypeAny).String(),
			},
		},
	},
}

func (f wGoTo) Define() *Widget {
	return widgetWGoTo
}

func (f wGoTo) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	maxLoop, err := getNodeValueToInt64(nodeValue, "maxLoopRounds", true)
	if err != nil {
		return nil, stderr.Wrap(err, "goto widget gen script err")
	}
	if maxLoop == 0 {
		maxLoop = MaxLoopDefault
	}
	targetNode, err := getNodeValueToString(nodeValue, GotoNextNodeParamKey, false)
	if err != nil {
		return nil, stderr.Wrap(err, "goto widget gen script err")
	}
	return script.GOTO{
		Meta:          nodeMeta.ToBaseScript(),
		TargetNode:    targetNode,
		MaxLoopRounds: maxLoop,
	}, nil
}
