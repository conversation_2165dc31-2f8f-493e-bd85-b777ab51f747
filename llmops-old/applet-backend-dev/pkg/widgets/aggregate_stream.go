package widgets

import (
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyAggregateStream = "WidgetKeyAggregateStream"
)

type aggregateStream struct {
}

var widgetAggregateStream = &Widget{
	Id:    WidgetKeyAggregateStream,
	Name:  "流式聚合",
	Desc:  "聚合流式输出的数据, 把上游输出的流式数据聚合成一个完整非流式数据，传递给下游",
	Group: WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       "Input",
				Name:     "流式数据",
				Desc:     "需要聚合的流式数据," + BaseAnyLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "聚合后的完整非流式数据," + BaseSyncLimits(DataTypeAny).String(),
			},
		},
	},
}

func (a aggregateStream) Define() *Widget {
	return widgetAggregateStream
}

func (a aggregateStream) Script(nodeMeta NodeMeta, nodeValue map[string]any) (script.Generator, error) {
	return script.AggregateStream{
		Meta: nodeMeta.ToBaseScript(),
	}, nil
}
