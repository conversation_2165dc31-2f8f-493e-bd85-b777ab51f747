package widgets

import (
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// BuildUrlQueryString 将参数映射转换为 URL 查询字符串。
// 该函数接收一个字符串键值对的 map，将其转换为经过排序和 URL 编码的查询字符串。
// 返回的字符串格式为 "key1=value1&key2=value2"，其中键按字母顺序排序，
// 键值对之间用 & 连接，特殊字符会被正确编码。
func BuildUrlQueryString(params map[string]string) string {
	// 创建一个新的 url.Values
	values := url.Values{}

	// 获取所有的 keys 并排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 按照排序后的 keys 顺序添加参数
	for _, key := range keys {
		values.Add(key, params[key])
	}

	// 编码为 URL 查询字符串并返回
	return values.Encode()
}

const (
	Both = "both"
	Any  = "any"
	Not  = "not"
)

// BuildPreconditionString 将precondition数组序列化为json字符串
//
// precondition 或、与、非的多重判断。约定如下：
// 1. 与判断语法： { both: 'a==value1,b==value2'} 表示 a==value1&&b==value2
// 2. 或判断语法：{any: 'a==value1,b==value2'} 表示 a==value1 || a ==value2
// 3. 非判断语法： {not: 'a==value1,b==value2'} 表示 a!=value1 && b!=value2
// 算子的precondition属性默认返回数组(最终效果是数组的序列化字符串) ',' 表示 '&&' 逻辑:
// 例如:
// 1. name=='jack'  配置为: [{both: 'name==jack'}]
// 2. name=='jack'&&gender!='woman'  配置为：[{both: 'name==jack'},{not: 'gender==woman'}]
func BuildPreconditionString(conditions []map[string]string) string {
	if len(conditions) == 0 {
		return "[]"
	}
	jsonBytes, err := json.Marshal(conditions)
	if err != nil {
		stdlog.Errorf("Error marshaling precondition to JSON: %v", err)
		return "[]"
	}
	return string(jsonBytes)
}

// BuildBothPrecondition 构建单个Both条件的precondition字符串
// 用于简化常见的key==value条件构建
// 例如：BuildBothPreconditionString("paramId", "value")
// 将生成：[{"both":"paramId==value"}]
func BuildBothPreconditionString(key, value string) string {
	return BuildPreconditionString([]map[string]string{
		{Both: fmt.Sprintf("%s==%s", key, value)},
	})
}

// BuildAnyPreconditionString
// 用于简化常见的key==v1||key==v2的条件构建
// 例如：BuildAnyPreconditionString("key",["value1","value2"])
// 将生成：[{"any":"key==value1,key=value2"}]
func BuildAnyPreconditionString(key string, values []string) string {
	sb := strings.Builder{}
	for index, v := range values {
		sb.WriteString(fmt.Sprintf("%s==%s", key, v))
		if index != len(v)-1 {
			sb.WriteString(",")
		}
	}
	anyStr := sb.String()
	paramMap := make([]map[string]string, 0)
	if anyStr != "" {
		paramMap = append(paramMap, map[string]string{Any: sb.String()})
	}
	return BuildPreconditionString(paramMap)
}

const (
	CompPropsAutoSizeDefaultMinRows = 4
	CompPropsAutoSizeDefaultMaxRows = 10
)

type AutoSize struct {
	MinRows int `json:"minRows"`
	MaxRows int `json:"maxRows"`
}

type CompProps struct {
	AutoSize AutoSize `json:"autoSize"`
}

// BuildCompPropsString 将 CompProps 结构体转换为 JSON 字符串表示。
// 如果转换过程中发生错误，将记录错误信息并返回空的 JSON 对象字符串 "{}"。
func BuildCompPropsString(props CompProps) string {
	jsonBytes, err := json.Marshal(props)
	if err != nil {
		stdlog.Errorf("Error marshaling compProps to JSON: %v", err)
		return "{}"
	}
	return string(jsonBytes)
}
