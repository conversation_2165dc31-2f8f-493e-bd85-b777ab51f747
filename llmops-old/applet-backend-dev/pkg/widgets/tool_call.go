package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyToolCall    = "WidgetKeyToolCall"
	ParamIDToolDescriber = "ToolDescriber" // 传入一个工具的描述
	ParamIDParams        = "Params"
)

type toolCall struct {
}

var widgetToolCall = &Widget{
	Id:    WidgetKeyToolCall,
	Name:  "应用插件调用",
	Desc:  "调用已经发布的应用插件",
	Group: WidgetGroupChainTool,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDToolDescriber,
				Name:     "工具信息",
				Desc:     "对工具的具体描述信息",
				Type:     pb.DynamicParam_TYPE_TOOL_CALL,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeGeneralMap),
			Define: DynamicParam{
				Id:   ParamIDParams,
				Name: "工具参数",
				Desc: "工具调用时使用的参数，" + BaseSyncLimits(DataTypeGeneralMap).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "工具调用结果，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (t toolCall) Define() *Widget {
	return widgetToolCall
}
func (t toolCall) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	toolDescriberStr, err := getNodeValueToString(nodeValue, ParamIDToolDescriber, true)
	if err != nil {
		return nil, err
	}
	return script.ToolCall{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(toolDescriberStr),
	}, nil
	return nil, nil
}
