package widgets

import (
	"encoding/json"
	"strings"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyQuestionClassifier              = "WidgetKeyQuestionClassifier"
	ParamIDQuestionClassifierCategory1       = "Category-1"
	ParamIDQuestionClassifierDefaultCategory = "Category-default"
	ParamIDQuestionClassifierOutput1         = "Output-1"
	ParamIDQuestionClassifierDefaultOutput   = "Output-default"
	ParamIDQuestionClassifierCategoryPrefix  = "Category-"
	ParamIDQuestionClassifierOutputPrefix    = "Output-"
	ParamIDQuestionClassifierModelService    = "ModelService"
)

var widgetQuestionClassifier = &Widget{
	DynamicEndPoint: true, // 输出端点不确定
	Id:              WidgetKeyQuestionClassifier,
	Name:            "意图识别(输入分类)",
	Desc:            "对用户输入的问题分类，控制数据流向不同的类别",
	Group:           WidgetGroupControlFlow,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "Input",
				Name: "待分类问题",
				Desc: "待分类的问题文本," + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDQuestionClassifierModelService,
				Name:       "模型服务",
				Desc:       "用于进行问题分类的模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetLLMModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDQuestionClassifierCategory1,
				Name:     "分类1",
				Desc:     "问题类别",
				Required: true,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Type:     pb.DynamicParam_TYPE_INPUT,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDQuestionClassifierOutput1,
				Name: "分类1",
				Desc: "符合分类1的输出端点，原样输出输入的文本，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDQuestionClassifierDefaultCategory,
				Name:         "默认分类",
				Desc:         "未匹配到分类问题或分类失败后默认的执行分支",
				Required:     true,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				Type:         pb.DynamicParam_TYPE_INPUT,
				DefaultValue: "未匹配到上述类别",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDQuestionClassifierDefaultOutput,
				Name: "默认分类",
				Desc: "未匹配到分类问题或分类失败后默认的执行分支，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

type questionClassifier struct {
}

func (q questionClassifier) Define() *Widget {
	return widgetQuestionClassifier
}

func (q questionClassifier) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	// 获取问题类别
	categories, defaultCategoryId, err := q.getCategories(nodeValue)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get categories")
	}
	// 获取对问题分类的模型服务
	modelServiceStr, err := getNodeValueToString(nodeValue, ParamIDQuestionClassifierModelService, false)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get model service node value to string")
	}
	var modelService pb.ModelService
	if err := stdsrv.DefaultProtoJsonAccessor().Unmarshal([]byte(modelServiceStr), &modelService); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal model service")
	}
	params := script.WidgetParamsQuestionClassifier{
		Categories:        categories,
		DefaultCategoryId: defaultCategoryId,
		ModelService:      &modelService,
	}
	paramsBytes, err := json.Marshal(params)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to marshal WidgetParamsQuestionClassifier")
	}
	return script.QuestionClassifier{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(paramsBytes),
	}, nil
}

func (q questionClassifier) getCategories(nodeValue map[string]any) ([]script.Category, string, error) {
	nodeParamId2Category := make(map[string]script.Category)
	for k, v := range nodeValue {
		// 约定问题类别参数id格式为"Category-uuid"
		if !strings.HasPrefix(k, ParamIDQuestionClassifierCategoryPrefix) {
			continue
		}
		nodeParamId := strings.TrimPrefix(k, ParamIDQuestionClassifierCategoryPrefix)
		if nodeParamId == "" {
			return nil, "", stderr.Error("node param id is empty")
		}
		_, exist := nodeParamId2Category[nodeParamId]
		if exist {
			return nil, "", stderr.Error("duplicate node param id: %s", nodeParamId)
		}
		categoryValue, ok := v.(string)
		if !ok {
			return nil, "", stderr.Error("category value %+v is %T,not string", v, v)
		}
		if categoryValue == "" {
			return nil, "", stderr.Error("category value is empty")
		}
		nodeParamId2Category[nodeParamId] = script.Category{
			CategoryId:   "",
			CategoryName: categoryValue,
		}
	}
	outputPorts, err := getNodeValueToOutPut(nodeValue, false)
	if err != nil {
		return nil, "", stderr.Wrap(err, "failed to get output node value to out put")
	}
	defaultCategoryId := ""
	for _, o := range outputPorts.NodeOutputs {
		// 约定输出端点参数id格式为"Output-uuid"，uuid与问题类别参数的uuid相同
		if !strings.HasPrefix(o.CurrentNodeOutputParam, ParamIDQuestionClassifierOutputPrefix) {
			return nil, "", stderr.Error("node output param id %s is not prefix of %s", o.CurrentNodeOutputParam, ParamIDQuestionClassifierOutputPrefix)
		}
		nodeParamId := strings.TrimPrefix(o.CurrentNodeOutputParam, ParamIDQuestionClassifierOutputPrefix)
		if nodeParamId == "" {
			return nil, "", stderr.Error("node param id is empty")
		}
		category, exist := nodeParamId2Category[nodeParamId]
		if !exist {
			return nil, "", stderr.Error("node param id %s not exist", nodeParamId)
		}
		// 使用下一个算子id作为分类id
		categoryWithId := script.Category{
			CategoryId:   o.NextNodeID,
			CategoryName: category.CategoryName,
		}
		nodeParamId2Category[nodeParamId] = categoryWithId
		if o.CurrentNodeOutputParam == ParamIDQuestionClassifierDefaultOutput {
			defaultCategoryId = o.NextNodeID
		}
	}
	if defaultCategoryId == "" {
		stdlog.Warnf("default category output is not found, maybe the node is the earlier version")
	}
	// 校验分类id是否为空，组装categories列表
	categories := make([]script.Category, 0)
	for _, category := range nodeParamId2Category {
		if category.CategoryId == "" {
			return nil, "", stderr.Error("category id of %s is empty", category.CategoryName)
		}
		categories = append(categories, category)
	}
	if len(categories) < 2 {
		return nil, "", stderr.Error("categories must be at least 2")
	}
	return categories, defaultCategoryId, nil
}
