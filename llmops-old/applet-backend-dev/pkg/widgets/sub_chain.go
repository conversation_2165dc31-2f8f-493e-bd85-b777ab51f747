package widgets

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	// WidgetKeySubChain 子链
	WidgetKeySubChain  = "WidgetKeySubChain"
	WidgetNameSubChain = "应用链嵌入"
	WidgetDescSubChain = "一种应用链融合技术，将子链复制为独立的实例融入父链中，多次嵌入的子链互相独立"
)

type subChain struct {
}

var WidgetSubChain = &Widget{
	Id:              WidgetKeySubChain,
	Name:            WidgetNameSubChain,
	Desc:            WidgetDescSubChain,
	Group:           WidgetGroupChainTool,
	DynamicEndPoint: true,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Input",
				Name: "输入",
				Desc: "输入",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "输出",
			},
		},
	},
}

func (t subChain) Define() *Widget {
	return WidgetSubChain
}

func (s subChain) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return nil, stderr.Errorf("sub chain must be expanded as mutil independent node")
}
