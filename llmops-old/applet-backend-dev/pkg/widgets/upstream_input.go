package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyUpstreamInput = "WidgetKeyUpstreamInput"
	ParamIDJsonInput       = "JsonInput"
)

type upstreamInputs struct {
}

var upstreamInput = &Widget{
	Id:    WidgetKeyUpstreamInput,
	Name:  "Json输入",
	Desc:  "输入Json字符串并将其反序列化",
	Group: WidgetGroupInput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeReqInput,
			ParamLimits: BaseSyncLimits(DataTypeJsonString),
			Define: DynamicParam{
				Id:       ParamIDJsonInput,
				Type:     pb.DynamicParam_TYPE_TEXTAREA,
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Name:     "Json输入",
				Desc:     BaseSyncLimits(DataTypeJsonString).String(),
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "把输入的Json反序列化," + BaseSyncLimits(DataTypeAny).String(),
			},
		},
	},
}

func (t upstreamInputs) Define() *Widget {
	return upstreamInput
}

func (t upstreamInputs) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.JSONInput{
		Meta:     nodeMeta.ToBaseScript(),
		InputKey: "JsonInput",
	}, nil
}
