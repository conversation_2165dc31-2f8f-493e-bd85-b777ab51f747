package script

import "transwarp.io/aip/llmops-common/pb"

type QuestionClassifier struct {
	Meta   *BaseScript     `json:"meta"`
	Params MultilineString `json:"params"`
}

func (q QuestionClassifier) ScriptName() string {
	return "questionClassifier"
}

type Category struct {
	CategoryId   string `json:"category_id"`
	CategoryName string `json:"category_name"`
}

type WidgetParamsQuestionClassifier struct {
	Categories        []Category       `json:"categories"`
	DefaultCategoryId string           `json:"default_category_id"`
	ModelService      *pb.ModelService `json:"model_service"`
}
