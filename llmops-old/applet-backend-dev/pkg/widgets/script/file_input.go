package script

import (
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type FileInput struct {
	Meta     *BaseScript     `json:"meta"`
	NodeID   string          `json:"nodeID"`
	InputKey string          `json:"inputKey"`
	Params   MultilineString `json:"params"`
}

func (f FileInput) ScriptName() string {
	return "fileInput"
}

type WidgetParamsFileInput struct {
	IsFileContentRead bool     `json:"is_file_content_read"`
	MaxFileSizeMB     int      `json:"max_file_size_mb"`
	AllowedExtensions []string `json:"allowed_extensions"`
}

func (w *WidgetParamsFileInput) Validate() error {
	if w.MaxFileSizeMB < FileInputMinMaxFileSizeMB || w.MaxFileSizeMB > FileInputMaxMaxFileSizeMB {
		return stderr.Errorf("MaxFileSizeMB must be in range [%d, %d] MB", FileInputMinMaxFileSizeMB, FileInputMaxMaxFileSizeMB)
	}

	for i, ext := range w.AllowedExtensions {
		ext = strings.TrimSpace(strings.ToLower(ext))
		if ext == "" {
			return stderr.Errorf("AllowedExtensions contains an empty or whitespace-only extension")
		}
		if ext != "*" && !strings.HasPrefix(ext, ".") {
			ext = "." + ext
		}
		w.AllowedExtensions[i] = ext
	}
	return nil
}
