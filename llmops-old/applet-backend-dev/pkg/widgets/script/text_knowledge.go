package script

type TextKnowledgeSearch struct {
	Meta   *BaseScript     `json:"meta"`
	Params MultilineString `json:"params"`
}

func (t TextKnowledgeSearch) ScriptName() string {
	return "textKnowledgeSearch"
}

type TextKnowledgeInsert struct {
	Meta   *BaseScript     `json:"meta"`
	Params MultilineString `json:"params"`
}

func (t TextKnowledgeInsert) ScriptName() string {
	return "textKnowledgeInsert"
}
