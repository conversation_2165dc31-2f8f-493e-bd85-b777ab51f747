package widgets

import (
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyTextTemplate       = "WidgetKeyTextTemplate"
	ParamIDTextTemplateTemplate = "Template"
	ParamIDTextTemplateInput1   = "Input1"
	ParamIDTextTemplateInput2   = "Input2"
)

type textTemplate struct {
}

var WidgetTextTemplate = &Widget{
	DynamicEndPoint: true, // InPort端点个数不固定
	Id:              WidgetKeyTextTemplate,
	Name:            "通用文本模板",
	Desc:            "把变量拼接成一段文本，文本模板中允许不引用变量或引用多个变量",
	Group:           WidgetGroupProcessText,
	Params: []WidgetParam{
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDTextTemplateTemplate,
				Name:         "模板",
				Disabled:     true,
				DefaultValue: "第一个变量值:{{.Input1}}, 第二个变量值:{{.Input2}}",
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       ParamIDTextTemplateInput1,
				Name:     ParamIDTextTemplateInput1,
				Desc:     BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:       ParamIDTextTemplateInput2,
				Name:     ParamIDTextTemplateInput2,
				Desc:     BaseSyncLimits(DataTypeAny).String(),
				Required: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeString).String(),
			},
		},
	},
}

func (t textTemplate) Define() *Widget {
	return WidgetTextTemplate
}

func (t textTemplate) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	templateStr, err := getNodeValueToString(nodeValue, ParamIDTextTemplateTemplate, true)
	if err != nil {
		return nil, err
	}
	return script.Prompt{
		Meta:     nodeMeta.ToBaseScript(),
		Template: script.MultilineString(templateStr),
		RawInput: false,
	}, nil
}
