package widgets

import (
	"encoding/json"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyParameterExtractor           = "WidgetKeyParameterExtractor"
	ParamIDParameterExtractorModelService = "ModelService"
	ParamIDParameterExtractorInput        = "Input"
	ParamIDParameterExtractorOutput       = "Output"
	ParamIDParameterExtractorParamDef     = "ParamDef"
)

var widgetParameterExtractor = &Widget{
	Id:    WidgetKeyParameterExtractor,
	Name:  "调用参数提取",
	Desc:  "从用户输入中提取指定的参数",
	Group: WidgetGroupChainTool,
	Params: []WidgetParam{
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   ParamIDParameterExtractorInput,
				Name: "输入文本",
				Desc: "需要提取参数的输入文本，" + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         ParamIDParameterExtractorModelService,
				Name:       "模型服务",
				Desc:       "用于进行参数提取的模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetLLMModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass: DataClassCode,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:       ParamIDParameterExtractorParamDef,
				Name:     "参数定义",
				Desc:     "定义需要提取的参数",
				Required: true,
				Type:     pb.DynamicParam_TYPE_PARAMETER_EXTRACTOR,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeGeneralMap),
			Define: DynamicParam{
				Id:   ParamIDParameterExtractorOutput,
				Name: "提取结果",
				Desc: "提取的参数，" + BaseSyncLimits(DataTypeGeneralMap).String(),
			},
		},
	},
}

type parameterExtractor struct {
}

func (p parameterExtractor) Define() *Widget {
	return widgetParameterExtractor
}

func (p parameterExtractor) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	modelServiceStr, err := getNodeValueToString(nodeValue, ParamIDParameterExtractorModelService, false)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get model service node value to string")
	}
	var modelService pb.ModelService
	if err := stdsrv.DefaultProtoJsonAccessor().Unmarshal([]byte(modelServiceStr), &modelService); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal model service")
	}

	// 获取参数定义
	paramDefStr, err := getNodeValueToString(nodeValue, ParamIDParameterExtractorParamDef, false)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get param definition node value to string")
	}
	var params []*agent_definition.APIToolParam
	if err := json.Unmarshal([]byte(paramDefStr), &params); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal param definition")
	}

	widgetParams := script.WidgetParamsParameterExtractor{
		ModelService: &modelService,
		Params:       params,
	}
	paramsBytes, err := json.Marshal(widgetParams)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to marshal WidgetParamsParameterExtractor")
	}
	return script.ParameterExtractor{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(paramsBytes),
	}, nil
}
