package widgets

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	WidgetKeyChunksOutput = "WidgetKeyChunksOutput"
	ParamIDChunks         = "Chunks"
	ParamIDElements       = "Elements"
	ParamIDLoadChunkResp  = "LoadChunkResp"
)

type chunksOutput struct {
}

var widgetChunksOutput = &Widget{
	Id:    WidgetKeyChunksOutput,
	Name:  "知识加工输出(自定义策略)",
	Desc:  "标记算子,代表链的最终输出为pb.DocSvcLoadChunkRsp",
	Group: WidgetGroupOutput,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks),
			Define: DynamicParam{
				Id:       ParamIDChunks,
				Name:     "chunks",
				Desc:     "chunks文本，" + BaseSyncLimits(DataTypeString, DataTypeStrings, DataTypeChunks).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeElements),
			Define: DynamicParam{
				Id:       ParamIDElements,
				Name:     "elements",
				Desc:     "elements文本，" + BaseSyncLimits(DataTypeElements).String(),
				DataType: pb.DynamicParam_DATA_TYPE_STRING,
				Required: true,
			},
		},
		//{
		//	DataClass: DataClassJson,
		//	Category:  ParamTypeNodeInPort,
		//	Define: DynamicParam{
		//		Id:       ParamIDLoadChunkResp,
		//		Name:     "LoadChunkResp",
		//		Desc:     "要求上游输入数据为pb.DocSvcLoadChunkRsp",
		//		DataType: pb.DynamicParam_DATA_TYPE_STRING,
		//	},
		//},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeLoadChunk),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseSyncLimits(DataTypeLoadChunk).String(),
			},
		},
	},
}

func (c chunksOutput) Define() *Widget {
	return widgetChunksOutput
}

func (c chunksOutput) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	return script.ChunksOutput{
		Meta: nodeMeta.ToBaseScript(),
	}, nil
}
