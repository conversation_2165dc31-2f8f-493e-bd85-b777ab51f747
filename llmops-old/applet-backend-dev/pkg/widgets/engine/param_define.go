package engine

import (
	"fmt"
	"transwarp.io/aip/llmops-common/pb"
)

type SFSFile struct {
	Name    string `json:"name"`
	Uid     string `json:"uid"`
	Url     string `json:"url"`
	HttpUrl string `json:"http_url,omitempty"`
	Content []byte `json:"content"`
}

func (s *SFSFile) GetSfsDownloadUrl() string {
	return fmt.Sprintf("[%s](<%s>)", s.Name, s.Url)
}
func GetExampleSFSFile() *SFSFile {
	return &SFSFile{Name: "name", Uid: "uid", Url: "sfs:///tenants/llmops-assets/projs/assets/a.text", Content: []byte("content")}
}
func GetExampleSFSFiles() []*SFSFile {
	return []*SFSFile{GetExampleSFSFile()}
}

type ElementsV2 struct {
	Duration float64          `json:"duration"` //耗时
	Elements []*pb.DocElement `json:"elements"`
}

type SplitResult struct {
	Texts []string `json:"texts"`
}

type VecInsertRes struct {
	Records   int    `json:"records,omitempty"`   // 插入向量数量
	Dimension int    `json:"dimension,omitempty"` // 插入向量维度
	BodySize  int    `json:"bodySize,omitempty"`  // 插入实体二进制数据总量
	Consumed  string `json:"consumed,omitempty"`  // 插入总耗时
}
