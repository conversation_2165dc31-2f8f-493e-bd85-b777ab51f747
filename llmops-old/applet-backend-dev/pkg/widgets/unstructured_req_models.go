package widgets

// https://gitlab.transwarp.io/llm/vlmodel/unstructured/-/blob/dev/unstructured-main/models.py
// models.py里定义的请求模型，都可以作为 struct 被定义在这个文件里，用于请求 engine，
// 也可以在 engine 中直接复用 / UnMarshal，再作为请求体，请求 applet-doc 服务

type DocLoadRawReqMerge struct {
	Name           string          `json:"name"`
	Uid            string          `json:"uid"`
	Url            string          `json:"url"`
	Content        string          `json:"content"`
	FilePath       string          `json:"file_path,omitempty"`
	SavePath       string          `json:"save_path,omitempty"`
	Bucket         *string         `json:"bucket,omitempty"`
	ExternalConfig *ExternalConfig `json:"external_config,omitempty"`
	DocServiceUrl  string          `json:"docServiceUrl,omitempty"`
	DocApiKey      string          `json:"docApiKey,omitempty"`
	ImageModelUrl  string          `json:"imageModelUrl,omitempty"`
	ImageApiKey    string          `json:"imageApiKey,omitempty"`
}

type ExternalConfig struct {
	Save2MD         bool `json:"save2md"`
	Figure          bool `json:"figure"`
	Table           bool `json:"table"`
	MathFormula     bool `json:"math_formula"`
	ChemicalFormula bool `json:"chemical_formula"`
}
