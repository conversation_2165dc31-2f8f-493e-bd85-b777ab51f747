package widgets

import (
	"fmt"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

// 大模型服务相关算子

const (
	WidgetKeyLLM           = "WidgetKeyLLMModel"
	WidgetParamModelServer = "ModelServer"
	WidgetKeyVectorAIModel = "WidgetKeyVectorModel"
	WidgetKeyDlieAIModel   = "WidgetKeyDlieModel"
	ParamIDLLMSystemPrompt = "SystemPrompt"
	ParamIDLLMTemperature  = "Temperature"
	ParamIDLLMTopP         = "TopP"
	ParamIDLLMMaxTokens    = "MaxTokens"
	ParamIDUseBase64       = "UseBase64"

	// LLM参数ID常量
)

type llm struct {
}

var widgetLLM = &Widget{
	Id:    WidgetKeyLLM,
	Name:  "文本生成",
	Desc:  "用于调用文本生成模型服务",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "Text",
				Name: "文本",
				Desc: "输入给LLM的提示词," + BaseSyncLimits(DataTypeString).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:           ParamIDLLMSystemPrompt,
				Name:         "系统提示词",
				Desc:         "模型的系统提示词，用于定义AI行为和回答方式，提高响应准确性",
				Type:         pb.DynamicParam_TYPE_TEXTAREA,
				DataType:     pb.DynamicParam_DATA_TYPE_STRING,
				DefaultValue: script.DlieInferDefaultSystemPrompt,
				CompProps: BuildCompPropsString(CompProps{
					AutoSize: AutoSize{
						MinRows: CompPropsAutoSizeDefaultMinRows,
						MaxRows: CompPropsAutoSizeDefaultMaxRows,
					},
				}),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "LLM模型",
				Desc:       "可用的LLM模型服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetLLMModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
				Required:   true,
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:     "DialogHistory",
				Name:   "对话历史",
				Desc:   "对话历史",
				Hidden: true,
			},
		},
		{
			DataClass:   DataClassString,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeString),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "LLM输出的文本," + BaseAnyLimits(DataTypeString).String(),
			},
		},
	},
}
var widgetVectorModel = &Widget{
	Id:    WidgetKeyVectorAIModel,
	Name:  "文本向量",
	Desc:  "用于调用文本向量模型服务，把文本转为向量",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeString, DataTypeStrings),
			Define: DynamicParam{
				Id:   "Text",
				Name: "文本",
				Desc: BaseSyncLimits(DataTypeString, DataTypeStrings).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "服务",
				Desc:       "服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetVectorModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseSyncLimits(DataTypeVectorInsert),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: "文本转成的向量," + BaseSyncLimits(DataTypeVectorInsert).String(),
			},
		},
	},
}

var widgetDlieModel = &Widget{
	Id:    WidgetKeyDlieAIModel,
	Name:  "通用推理",
	Desc:  "可调用所有已上线的模型服务",
	Group: WidgetGroupAIModel,
	Params: []WidgetParam{
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeInPort,
			ParamLimits: BaseSyncLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "Text",
				Name: "输入",
				Desc: BaseSyncLimits(DataTypeAny).String(),
			},
		},
		{
			DataClass: DataClassString,
			Category:  ParamTypeAttribute,
			Define: DynamicParam{
				Id:         WidgetParamModelServer,
				Name:       "服务",
				Desc:       "服务",
				Type:       pb.DynamicParam_TYPE_AGENT_MODEL_API,
				Datasource: GetDlieModelSvcConditionsStr(),
				DataType:   pb.DynamicParam_DATA_TYPE_STRING,
			},
		},
		{
			DataClass:   DataClassJson,
			Category:    ParamTypeNodeOutPort,
			ParamLimits: BaseAnyLimits(DataTypeAny),
			Define: DynamicParam{
				Id:   "OutPut",
				Desc: BaseAnyLimits(DataTypeAny).String(),
			},
		},
	},
}

func (l llm) Define() *Widget {
	return widgetLLM
}

type WidgetParamsDlieInfer struct {
	pb.ModelService
}

func (l llm) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	systemPrompt, err := getNodeValueToString(nodeValue, ParamIDLLMSystemPrompt, true)
	if err != nil {
		return nil, err
	}
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}
	if params.SubKind != pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION {
		return nil, stderr.Error("the sub kind of model is error")
	}

	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:   nodeMeta.ToBaseScript(),
		System: systemPrompt,
		Params: script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}

type vectorModel struct {
}

func (l vectorModel) Define() *Widget {
	return widgetVectorModel
}

func (l vectorModel) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}
	if params.SubKind != pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR {
		return nil, stderr.Error("the sub kind of model is error")
	}

	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}

type dlieModel struct {
}

func (l dlieModel) Define() *Widget {
	return widgetDlieModel
}

func (l dlieModel) Script(nodeMeta NodeMeta, nodeValue map[string]interface{}) (script.Generator, error) {
	pbModelSvcStr, err := getNodeValueToString(nodeValue, WidgetParamModelServer, false)
	if err != nil {
		return nil, err
	}
	params := new(WidgetParamsDlieInfer)
	if err = stdsrv.UnmarshalMixWithProto(pbModelSvcStr, params); err != nil {
		return nil, err
	}

	// 通用推理模型-由模型类型来决定
	SimpleModelService(&params.ModelService)
	return script.DlieInfer{
		Meta:   nodeMeta.ToBaseScript(),
		Params: script.MultilineString(stdsrv.AnyToString(params)),
	}, nil
}

type AIModelService struct {
	Name string
	LLMModelServiceValue
}

type LLMModelServiceValue struct {
	ModelName string
	IP        string
	Port      int32
}

func ModelServiceToChainDataSources(services []*AIModelService) string {
	res := ""
	for _, s := range services {
		if res == "" {
			res = ModelServiceToChainDataSource(s)
		} else {
			res = fmt.Sprintf("%s,%s", res, ModelServiceToChainDataSource(s))
		}
	}
	return res
}

func ModelServiceToDataSourceKey(llmService *AIModelService) string {
	return fmt.Sprintf("%s:%d/%s", llmService.IP, llmService.Port, llmService.ModelName)
}

func ModelServiceToDataSourceName(llmService *AIModelService) string {
	return fmt.Sprintf("%s", llmService.Name)
}

func ModelServiceToChainDataSource(llmService *AIModelService) string {
	return fmt.Sprintf("%s:%d/%s@@%s", llmService.IP, llmService.Port, llmService.ModelName, llmService.Name)
}

// SimpleModelService 去除一些无用的大字段
func SimpleModelService(model *pb.ModelService) {
	if model == nil {
		return
	}
	model.ReferenceModel = nil
	model.ReferenceRelease = nil
}
