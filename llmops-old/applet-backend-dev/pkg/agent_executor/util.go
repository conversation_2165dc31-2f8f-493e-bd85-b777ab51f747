package agent_executor

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

var httpCli = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	},
}

type httpParam struct {
	Method     string
	Url        string
	ReqBody    string
	Header     map[string]string
	QueryParam map[string]string
}

func HttpCallString(ctx context.Context, param *httpParam) (string, error) {

	url, err := url.Parse(param.Url)
	if err != nil {
		return "", err
	}
	q := url.Query()
	// 添加 query参数
	for k, v := range param.QueryParam {
		q.Set(k, v)
	}
	var reqBody io.Reader
	if param.ReqBody != "" {
		reqBody = bytes.NewBufferString(param.ReqBody)
	}
	fullPath := fmt.Sprintf("%s?%s", url.String(), q.Encode())
	req, err := http.NewRequestWithContext(ctx, param.Method, fullPath, reqBody)
	if err != nil {
		return "", err
	}
	// 默认application/json
	req.Header.Add("Content-Type", "application/json")
	for k, v := range param.Header {
		req.Header.Add(k, v)
	}
	// 发送请求并获取响应
	resp, err := httpCli.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(body))
	}
	// json编码再解码，解决中文问题
	var res any
	if err := json.Unmarshal(body, &res); err != nil {
		return "", err
	}
	bytes, err := json.Marshal(res)
	if err != nil {
		return "", err
	}
	return string(bytes), nil

}
