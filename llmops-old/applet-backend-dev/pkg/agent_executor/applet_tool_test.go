package agent_executor

import (
	"context"
	"encoding/json"
	"sync"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

const (
	toolDescribeStr = `
{
        "id": "f68a3f25-a525-46a5-b98d-9fc49806da46",
        "mlops_svc_id": "34e87695-f009-4381-a28b-42fcc616c462",
        "name": "split_file",
        "desc": "上传文件，使用换行符分割文本，转成[]pb.Chunk结构返回",
        "mlops_call_url": "http://172.17.120.207:31380/seldon/dev/service-34e87695-f009-4381-a28b-42fcc616c462/1884/api/v1?pretty=true&project_id=default&stack=true&thought=true",
        "create_time_ms": 1714127577000,
        "params": [
            {
                "name": "2baaf9ef-1b32-4678-a155-d13e8a403457##FileInput",
                "desc": "文件上传,用于加载上传的单个文件，输出文件字节流",
                "value_type": "array",
                "default_value": [
                    {
                        "url": ""
                    }
                ],
                "required": false
            }
        ]
    }`
	inputMapStr = `
{
    "2baaf9ef-1b32-4678-a155-d13e8a403457##FileInput": [
        {
            "url": "sfs:///temp/公司代理服务使用说明1714271422298/公司代理服务使用说明.txt",
            "uid": "temp/公司代理服务使用说明1714271422298/公司代理服务使用说明.txt",
            "name": "公司代理服务使用说明.txt"
        }
    ]
}
`
	defaultToken = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTM1OTU5NjUsImlhdCI6MTcxMzUwOTU2NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.STsEqDK8mzBnEoo8fOhC6NRL1UOroEgCHW0vEERkHnbquSpE2qqv-tX_DD73S1UAwxUVWKNIG09NTNLOCv82lQ"
)

// TODO 太久了 10-20秒
func TestAppletServiceExecutor(t *testing.T) {
	ctx := context.Background()
	ctx = helper.SetToken(ctx, defaultToken)
	executor := AppletServiceExecutor{}
	toolDesc := new(agent_definition.AppletServiceToolDescriber)
	json.Unmarshal([]byte(toolDescribeStr), toolDesc)
	inputMap := make(map[string]interface{})
	json.Unmarshal([]byte(inputMapStr), &inputMap)
	res, err := executor.Execute(ctx, toolDesc, inputMap)
	if err != nil {
		stdlog.Info("error = ", err)
	}
	stdlog.Info(res)
}

func TestExecuteWithParams(t *testing.T) {
	ctx := context.Background()
	ctx = helper.SetToken(ctx, defaultToken)
	executor := AppletServiceExecutor{}
	toolDesc := new(agent_definition.AppletServiceToolDescriber)
	json.Unmarshal([]byte(toolDescribeStr), toolDesc)
	inputMap := make(map[string]interface{})
	json.Unmarshal([]byte(inputMapStr), &inputMap)

	res, err := executor.ExecuteWithParams(ctx, toolDesc.MlOpsSvcID, inputMap)
	if err != nil {
		stdlog.Info("error = ", err)
	}
	stdlog.Info(res)
}

func TestSyncRunTasks(t *testing.T) {
	executor := AppletServiceExecutor{}
	toolDesc := new(agent_definition.AppletServiceToolDescriber)
	json.Unmarshal([]byte(toolDescribeStr), toolDesc)
	inputMap := make(map[string]interface{})
	json.Unmarshal([]byte(inputMapStr), &inputMap)

	var mutex sync.Mutex
	answer := make([]string, 0)
	tasks := make([]func() error, 0)
	for i := 0; i < 10; i++ {
		task := func() error {
			ctx := context.Background()
			ctx = helper.SetToken(ctx, defaultToken)
			res, err := executor.ExecuteWithParams(ctx, toolDesc.MlOpsSvcID, inputMap)
			if err != nil {
				return err
			}

			mutex.Lock()
			defer mutex.Unlock()
			answer = append(answer, res)
			return nil
		}
		tasks = append(tasks, task)
	}

	if err := helper.SyncRunTasks(tasks); err != nil {
		stdlog.Info(err)
	}
	stdlog.Info(answer)

}
