package agent_executor

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	tritonpb "transwarp.io/applied-ai/aiot/vision-std/triton/pb"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
)

const (
	SfsFilePathPrefix = "sfs:///"
)

type ModelToolInput interface {
	setTritonReq() (interface{}, error)
	setValue(map[string]string) error
}

func newModelToolInput(tool *agent_definition.ModelToolDescriber) (ModelToolInput, error) {
	switch tool.Kind {
	case pb.ModelKind_MODEL_KIND_OCR, pb.ModelKind_MODEL_KIND_CV:
		return &CVModelInput{}, nil
	case pb.ModelKind_MODEL_KIND_MULTI, pb.ModelKind_MODEL_KIND_SR:
		return &MultiModalInput{}, nil
	}
	switch tool.SubKind {
	case pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION:
		return &MultiModalInput{}, nil
	}
	return nil, stderr.Internal.Error("not support %s/%s service", tool.Kind, tool.SubKind)
}

type CVModelInput struct {
	ImageFilePath string `json:"image_file_path"`
}

func (c *CVModelInput) setValue(value map[string]string) error {
	v, ok := value["image_file_path"]
	if !ok {
		return stderr.Internal.Error("has not key image_file_path")
	}
	c.ImageFilePath = v
	return nil
}

func (c *CVModelInput) setTritonReq() (interface{}, error) {
	imagePath, err := getLocalFileLoc(c.ImageFilePath)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to parse sfs file path")
	}
	imageBs, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to read image")
	}
	imageExt := strings.TrimPrefix(filepath.Ext(imagePath), ".")
	imageName := filepath.Base(imagePath)
	return &triton.StdCVReq{
		ClientId: uuid.NewString(),
		Params: []*tritonpb.InferReq_Param{
			{
				Id:   imageName,
				Type: imageExt,
				Data: base64.StdEncoding.EncodeToString(imageBs),
			},
		},
	}, nil
}

// MultiModalInput  多模态,比如图像理解
type MultiModalInput struct {
	Query  string `json:"query,omitempty" yaml:"query"`
	File   string `json:"file"`
	result string
}

func (m *MultiModalInput) setTritonReq() (interface{}, error) {
	handler := func(raw []byte) error {
		m.result = string(raw)
		return nil
	}
	return &triton.LLMChatReq{
		Query:   m.Query,
		Handler: handler,
		File:    m.File,
		Params: map[string]any{
			"response_format": "url", // 文生图模型需要的参数，其他模型会忽略此参数
		},
	}, nil
}

func (m *MultiModalInput) setValue(value map[string]string) error {
	query, ok := value["query"]
	if !ok {
		return stderr.Internal.Error("has not key query")
	}
	file, _ := value["file"]
	m.Query = query
	m.File = file
	return nil
}
func getLocalFileLoc(sfsFilePath string) (string, error) {
	if !strings.HasPrefix(sfsFilePath, SfsFilePathPrefix) {
		return "", stderr.Internal.Error("%s is not a sfs file path", sfsFilePath)
	}
	localPath := filepath.Join(conf.Config.ChainDeployCfg.SfsLocalRoot, strings.TrimPrefix(sfsFilePath, SfsFilePathPrefix))
	return localPath, nil
}

func getInferEntry(modelTool *agent_definition.ModelToolDescriber) (string, error) {
	if len(modelTool.Apis) == 0 {
		return "", stderr.Internal.Error("model tool api is empty")
	}
	// 默认使用第一个接口进行推理, ensemble模型有多个接口，请将推理入口放在第一个
	inferEntry := strings.TrimPrefix(modelTool.Apis[0].Path, "/")
	return inferEntry, nil
}

type ModelToolExecutor struct {
}

// Execute 智能体执行模型工具
// TODO
// 1、需要支持所有的模型类型吗,限定模型类型是否好一些
// 2、是否也需要支持远程模型
// 3、替换成httpInfer的话,推理请求体如何获得

func (m *ModelToolExecutor) Execute(ctx context.Context, tool agent_definition.ToolDescriber, input any) (string, error) {
	modelTool, ok := tool.(*agent_definition.ModelToolDescriber)
	if !ok {
		return "", stderr.Internal.Error("it is not model tool describer")
	}
	modelToolInput, err := validateModelInput(modelTool, input)
	if err != nil {
		return "", stderr.Wrap(err, "input is not model tool input")
	}
	client, err := triton.NewClientWithSeldonFullUrl(ctx, modelTool.FullUrl)
	if err != nil {
		return "", stderr.Wrap(err, "failed to new triton client")
	}
	defer client.Disconnect()
	inferEntry, err := getInferEntry(modelTool)
	if err != nil {
		return "", stderr.Wrap(err, "failed to get infer entry")
	}
	tritonReq, err := modelToolInput.setTritonReq()
	if err != nil {
		return "", stderr.Wrap(err, "failed to get triton requestor")
	}
	switch modelTool.InvokeAsTool.InvokeMethod {
	case pb.ModelServiceInvokeMethod_MODEL_SERVICE_INVOKE_METHOD_SYNC:
		syncReq, ok := tritonReq.(triton.SyncInferRequestor)
		if !ok {
			return "", stderr.Internal.Error("infer type is sync but request is not match")
		}
		result, err := client.SyncModelInfer(inferEntry, syncReq)
		if err != nil {
			return "", stderr.Wrap(err, "failed to infer")
		}
		outputStr := make([]string, 0)
		for _, outputName := range syncReq.OutputNames() {
			output, ok := result[outputName]
			if !ok {
				stdlog.Errorf("failed to get output %s", outputName)
				continue
			}
			outputStr = append(outputStr, fmt.Sprintf("%s: %s", outputName, output))
		}
		return strings.Join(outputStr, "\n"), nil
	case pb.ModelServiceInvokeMethod_MODEL_SERVICE_INVOKE_METHOD_STREAM:
		streamReq, ok := tritonReq.(triton.StreamInferRequestor)
		if !ok {
			return "", stderr.Internal.Error("infer type is stream but request is not match")
		}
		err := client.StreamModelInfer(ctx, inferEntry, streamReq)
		if err != nil {
			return "", stderr.Wrap(err, "failed to get stream infer")
		}
		multiModalModelInput, ok := modelToolInput.(*MultiModalInput)
		if !ok {
			return "", stderr.Internal.Error("not match stream infer type and infer input")
		}
		return multiModalModelInput.result, nil
	}
	return "", stderr.Internal.Error("%s not matched infer type Sync or Stream", modelTool.InvokeAsTool.InvokeMethod.String())
}

func validateModelInput(tool *agent_definition.ModelToolDescriber, input any) (ModelToolInput, error) {
	tempInputParams, ok := input.(map[string]any)
	if !ok {
		return nil, stderr.Internal.Error("model executor input is not map[string]any")
	}
	inputParams := make(map[string]string)
	for key, value := range tempInputParams {
		strValue, ok := value.(string)
		if !ok {
			return nil, stderr.Internal.Error("failed convert %T type to %T type of model tool input", value, strValue)
		}
		inputParams[key] = strValue
	}
	for _, requiredParam := range tool.Definition().Parameters.Required {
		_, ok := inputParams[requiredParam]
		if !ok {
			return nil, stderr.Internal.Error("there is no required param %s in model tool executor input", requiredParam)
		}
	}
	modelToolInput, err := newModelToolInput(tool)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to construct model tool input")
	}
	err = modelToolInput.setValue(inputParams)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to set model tool input value")
	}
	return modelToolInput, nil
}

func (m *ModelToolExecutor) Type() agent_definition.ToolType {
	return agent_definition.ToolTypeModelService
}

// CheckHealth 检查某个工具的健康状态
func (m *ModelToolExecutor) CheckHealth(tool agent_definition.ToolDescriber) (health.ServiceHealth, error) {
	modelTool, ok := tool.(*agent_definition.ModelToolDescriber)
	if !ok {
		return health.ServiceHealth{}, stderr.Internal.Error("its not model tool describer")
	}
	h := false
	if modelTool.Status.State == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE.String() {
		h = true
	}
	return health.ServiceHealth{
		ID:      tool.Definition().ID,
		Name:    tool.Definition().NameForHuman,
		Healthy: h,
	}, nil
}
