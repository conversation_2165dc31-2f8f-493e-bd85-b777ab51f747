package scripts

import (
	"encoding/json"
	"os"
	"testing"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

func TestTranslateWidgetGroups(t *testing.T) {
	widgets.Init()
	start := time.Now()
	modelServices, ctx, err := GetModelAndCtx()
	if err != nil {
		stdlog.Info("get model field")
		return
	}
	var model *pb.ModelService
	for _, m := range modelServices.Services {
		if m.SubKind == pb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION &&
			m.Name == "qwen2-72b-instruct-on-llm11" {
			model = m
			break
		}
	}

	widgetGroups, err := new(applet.WidgetImpl).ListOriWidgetGroups(ctx)
	if err != nil {
		stdlog.Info("List OriWidgetGroups field")
		return
	}
	tasks := make([]func() error, 0)
	for _, group := range widgetGroups {
		tasks = append(tasks, CreateTranslateTask(ctx, model, &group.Name, true))
		tasks = append(tasks, CreateTranslateTask(ctx, model, &group.Desc, false))
		for _, widget := range group.Widgets {
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widget.Name, true))
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widget.Desc, false))
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widget.MdFullDesc, false))
			tasks = append(tasks, CreateTranslateTask(ctx, model, &widget.MdSummaryDesc, false))

			for pIndex, _ := range widget.Params {
				paramPtr := &(widget.Params[pIndex].Define)

				tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.Name, true))
				tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.Desc, false))
				tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.Datasource, false))
				//tasks = append(tasks, CreateTranslateTask(ctx, model, &paramPtr.DefaultValue))
			}
		}
	}

	err = helper.SyncRunTasks(tasks)
	if err != nil {
		stdlog.Info(err)
	}
	// 将对象转换为格式化的 JSON
	prettyJSON, _ := json.MarshalIndent(widgetGroups, "", "  ")
	// 将 JSON 写入文件
	os.WriteFile("../en-widget-groups.json", prettyJSON, 0644)
	elapsed := time.Since(start)
	stdlog.Infof("cost %v to do translate", elapsed)
}
