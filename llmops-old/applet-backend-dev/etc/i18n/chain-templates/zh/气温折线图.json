{"id": "气温折线图.json", "name": "气温折线图", "desc": "使用天气接口获取最近某个城市的天气信息，给出天气预报和最近的温度折线图。", "template_group_key": "ToolsCall", "template": {"nodes": [{"id": "5cc082d4-909c-4c41-ace5-31117689796c", "name": "参数提取器", "widget_id": "WidgetKeyParameterExtractor", "widget_detail": {"id": "WidgetKeyParameterExtractor", "name": "调用参数提取", "desc": "从用户输入中提取指定的参数", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyParameterExtractor", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入文本", "desc": "需要提取参数的输入文本，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "模型服务", "desc": "用于进行参数提取的模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "ParamDef", "name": "参数定义", "desc": "定义需要提取的参数", "required": true, "type": "TYPE_PARAMETER_EXTRACTOR", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "提取结果", "desc": "提取的参数，支持类型: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}]}, "ui": "{\"dragging\":false,\"height\":323,\"id\":\"5cc082d4-909c-4c41-ace5-31117689796c\",\"position\":{\"x\":816.160516038251,\"y\":585.0503448383544},\"positionAbsolute\":{\"x\":816.160516038251,\"y\":585.0503448383544},\"selected\":true,\"type\":\"custom\",\"width\":320,\"zIndex\":1}", "values": {"ModelService": "{\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"Qwen2.5-72b-instruct-11\",\"name\":\"Qwen2.5-72b-instruct-11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5-72b-instruct-11\",\"name_for_human\":\"对话模型:Qwen2.5-72b-instruct-11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"desc\":\"\",\"create_time_ms\":\"1727252060000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg\",\"name\":\"Qwen2.5-72b-instruct-11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上, 不好用的话找@付鑫或者@天义\",\"user_id\":\"demo\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1730367565346\",\"labels\":{\"场景\":\"文本生成\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"Qwen2.5-72b-instruct-11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"virtual_svc_url\":\"remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions\",\"security_config_id\":\"cqopj4l74ikh414dlqv0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/llmops/tenants/llmops-assets/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fllmops-assets%2Fprojs%2Fassets%2Favatar%2Ffbbc637b-51fe-4270-9906-f200d0e35395_QWEN.png\"},\"update_time_ms\":\"1730795024000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqopj4l74ikh414dlqv0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8644725351081168:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\"}", "ParamDef": "[{\"name\":\"data\",\"type\":\"string\",\"description\":\"用于生成线性图表的数据，数据应为包含数字列表的字符串，\\\"1;2;3;4;5\\\"\",\"required\":true},{\"name\":\"x_axis\",\"type\":\"string\",\"description\":\"线性图表的 x 轴，x 轴应为包含一系列文本的字符串，如 \\\"a;b;c;1;2\\\"，以便与数据匹配。\",\"required\":false}]"}, "sub_chain_base_info": null}, {"id": "9f720b7a-0449-4f7b-a54b-c1e05c575ac4", "name": "文本拼接", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "通用文本模板", "desc": "把变量拼接成一段文本，文本模板中允许不引用变量或引用多个变量", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Template", "name": "模板", "default_value": "画出气温折线图: {{.data}}", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "data", "name": "data", "desc": "data", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持传输方式:Sync,\n支持数据类型:\nString:text\n", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":208,\"id\":\"9f720b7a-0449-4f7b-a54b-c1e05c575ac4\",\"position\":{\"x\":247.74904772353113,\"y\":645.6590604799817},\"positionAbsolute\":{\"x\":247.74904772353113,\"y\":645.6590604799817},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1}", "values": {"Template": "画出气温折线图: {{.data}}"}, "sub_chain_base_info": null}, {"id": "1ebf239b-a828-4a88-9285-07c9e26baaa2", "name": "文本输入", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "文本输入", "desc": "用于将输入的文本原样输出", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "文本输入", "desc": "文本输入,支持类型: Sync-String: \"text\"。", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"1ebf239b-a828-4a88-9285-07c9e26baaa2\",\"position\":{\"x\":50.97028883016014,\"y\":103.8773345450623},\"positionAbsolute\":{\"x\":50.97028883016014,\"y\":103.8773345450623},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "cb48b764-f738-4b89-86fb-d06e806a0269", "name": "工具调用", "widget_id": "WidgetKeyToolCall", "widget_detail": {"id": "WidgetKeyToolCall", "name": "应用插件调用", "desc": "调用已经发布的应用插件", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ToolDescriber", "name": "工具信息", "desc": "对工具的具体描述信息", "type": "TYPE_TOOL_CALL", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Params", "name": "工具参数", "desc": "工具调用时使用的参数，支持类型: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "工具调用结果，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":392,\"id\":\"cb48b764-f738-4b89-86fb-d06e806a0269\",\"position\":{\"x\":819.2642453666535,\"y\":-162.89997461799862},\"positionAbsolute\":{\"x\":819.2642453666535,\"y\":-162.89997461799862},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ToolDescriber": "{\"id\":\"builtin-tool-f42485e3-67ce-3d\",\"base_url\":\"http://autocv-agent-tool-api-service.llmops/v1/\",\"method\":\"post\",\"headers\":{},\"api_path\":\"gaode_weather\",\"name\":\"gaode_weather\",\"desc\":\"当你想询问天气或与天气相关的问题时使用的工具。\",\"params\":[{\"name\":\"city\",\"desc\":\"用于天气查询的城市名称。如果没有城市信息，你可以询问城市名称。你需要从问题中提取出中文城市名称。\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false}],\"collection_id\":\"builtin-collection-bffec22c-89b0-34\",\"collection_name\":\"高德\"}"}, "sub_chain_base_info": null}, {"id": "de9fff8a-6595-4b1c-a068-318cb7c3800d", "name": "文本生成模型", "widget_id": "WidgetKeyLLMModel", "widget_detail": {"id": "WidgetKeyLLMModel", "name": "文本生成", "desc": "用于调用文本生成模型服务", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyLLMModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "文本", "desc": "输入给LLM的提示词,支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "系统提示词", "desc": "模型的系统提示词，用于定义AI行为和回答方式，提高响应准确性", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM模型", "desc": "可用的LLM模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "对话历史", "desc": "对话历史", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "LLM输出的文本,支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":191,\"id\":\"de9fff8a-6595-4b1c-a068-318cb7c3800d\",\"position\":{\"x\":2150.9189760719887,\"y\":-17.270002740996404},\"positionAbsolute\":{\"x\":2150.9189760719887,\"y\":-17.270002740996404},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"c2bd131f-b699-4467-8b1c-cb66d900c1b9\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"Llama3.2\",\"release_name\":\"Llama3.2-3B-Instruct\",\"release_version\":\"v1\",\"model_id\":\"MWH-MODEL-ct1u8efo3t2knhe22fn0\",\"release_id\":\"MWH-MODEL-RELEASE-ct1u9sno3t2knhe22fs0\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-c2bd131f-b699-4467-8b1c-cb66d900c1b9\",\"name\":\"Llama3.2-3B-Instruct\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-c2bd131f-b699-4467-8b1c-cb66d900c1b9/8011/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Llama3.2\",\"name_for_human\":\"对话模型:Llama3.2\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1732504628000\",\"reference_model\":{\"id\":\"MWH-MODEL-ct1u8efo3t2knhe22fn0\",\"name\":\"Llama3.2\",\"domain\":{\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"llama3.2版本模型\",\"user_id\":\"fangyuan.han\",\"thumbnail\":\"\",\"is_public\":false,\"create_time_ms\":\"1732502585964\",\"update_time_ms\":\"1732505299299\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":0,\"releases_info\":[],\"usage_count\":{\"deploys_count\":0,\"views_count\":19,\"downloads_count\":0,\"invokes_count\":0,\"trainings_count\":0,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-ct1u8efo3t2knhe22fn0\"},\"disk_usage\":12862620000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-ct1u9sno3t2knhe22fs0\",\"name\":\"Llama3.2-3B-Instruct\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-ct1u8efo3t2knhe22fn0/releases/MWH-MODEL-RELEASE-ct1u9sno3t2knhe22fs0/model-files\",\"model_id\":\"MWH-MODEL-ct1u8efo3t2knhe22fn0\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"12862620151\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1732505299307\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"c2bd131f-b699-4467-8b1c-cb66d900c1b9\",\"deployment_status\":\"running\",\"deployment_health\":\"healthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.8\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENABLE_CHUNKED_PREFILL\",\"name\":\"ENABLE_CHUNKED_PREFILL\",\"desc\":\"vllm是否开启prefill chunk功能， 默认值为2，1是打开，0是关闭，2是不设置\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"2\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NUM_SCHEDULER_STEP\",\"name\":\"NUM_SCHEDULER_STEP\",\"desc\":\"vllm多步调度， 默认值为1\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_MODEL_LEN\",\"name\":\"MAX_MODEL_LEN\",\"desc\":\"vllm支持的模型最大prompt长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"llama3\",\"content\":\"_register_template(\\n    name=\\\"llama3\\\",\\n    format_user=StringFormatter(\\n        slots=[\\n            (\\n                \\\"<|start_header_id|>user<|end_header_id|>\\\\n\\\\n{{content}}<|eot_id|>\\\"\\n                \\\"<|start_header_id|>assistant<|end_header_id|>\\\\n\\\\n\\\"\\n            )\\n        ]\\n    ),\\n    format_system=StringFormatter(slots=[\\\"<|start_header_id|>system<|end_header_id|>\\\\n\\\\n{{content}}<|eot_id|>\\\"]),\\n    format_observation=StringFormatter(\\n        slots=[\\n            (\\n                \\\"<|start_header_id|>tool<|end_header_id|>\\\\n\\\\n{{content}}<|eot_id|>\\\"\\n                \\\"<|start_header_id|>assistant<|end_header_id|>\\\\n\\\\n\\\"\\n            )\\n        ]\\n    ),\\n    format_prefix=EmptyFormatter(slots=[{\\\"bos_token\\\"}]),\\n    stop_words=[\\\"<|eot_id|>\\\"],\\n    replace_eos=True,\\n    replace_jinja_template=False,\\n)\\n\"}},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1732698540000\",\"guardrails_config\":{\"is_security\":true,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:模型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"Llama3.2\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:版本\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v1\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"Llama3.2-3B-Instruct\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"8644725351081168:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8644725351081168:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Llama3.2-3B-Instruct\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"c2bd131f-b699-4467-8b1c-cb66d900c1b9\"}"}, "sub_chain_base_info": null}, {"id": "54d458c3-22dc-46d6-969e-0b0596f7c191", "name": "工具调用", "widget_id": "WidgetKeyToolCall", "widget_detail": {"id": "WidgetKeyToolCall", "name": "应用插件调用", "desc": "调用已经发布的应用插件", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ToolDescriber", "name": "工具信息", "desc": "对工具的具体描述信息", "type": "TYPE_TOOL_CALL", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Params", "name": "工具参数", "desc": "工具调用时使用的参数，支持类型: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "工具调用结果，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":452,\"id\":\"54d458c3-22dc-46d6-969e-0b0596f7c191\",\"position\":{\"x\":1327.8457503832592,\"y\":494.5843997933205},\"positionAbsolute\":{\"x\":1327.8457503832592,\"y\":494.5843997933205},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ToolDescriber": "{\"id\":\"builtin-tool-f78ea577-c22b-3d\",\"base_url\":\"http://autocv-agent-tool-api-service.llmops/v1/chart/\",\"method\":\"post\",\"headers\":{},\"api_path\":\"line\",\"name\":\"line\",\"desc\":\"生成一张包含输入数据的线性图表。\",\"params\":[{\"name\":\"data\",\"desc\":\"用于生成线性图表的数据，数据应为包含数字列表的字符串，\\\"1;2;3;4;5\\\"\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"x_axis\",\"desc\":\"线性图表的 x 轴，x 轴应为包含一系列文本的字符串，如 \\\"a;b;c;1;2\\\"，以便与数据匹配。\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":false,\"default_value\":null,\"model_ignore\":false}],\"collection_id\":\"builtin-collection-dbc3a549-120c-38\",\"collection_name\":\"图表生成\"}"}, "sub_chain_base_info": null}, {"id": "5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a", "name": "参数提取器", "widget_id": "WidgetKeyParameterExtractor", "widget_detail": {"id": "WidgetKeyParameterExtractor", "name": "调用参数提取", "desc": "从用户输入中提取指定的参数", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyParameterExtractor", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入文本", "desc": "需要提取参数的输入文本，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "模型服务", "desc": "用于进行参数提取的模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "ParamDef", "name": "参数定义", "desc": "定义需要提取的参数", "required": true, "type": "TYPE_PARAMETER_EXTRACTOR", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "提取结果", "desc": "提取的参数，支持类型: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}]}, "ui": "{\"dragging\":false,\"height\":283,\"id\":\"5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a\",\"position\":{\"x\":363.7091395682619,\"y\":-178.63497681836898},\"positionAbsolute\":{\"x\":363.7091395682619,\"y\":-178.63497681836898},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelService": "{\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"Qwen2.5-72b-instruct-11\",\"name\":\"Qwen2.5-72b-instruct-11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5-72b-instruct-11\",\"name_for_human\":\"对话模型:Qwen2.5-72b-instruct-11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"desc\":\"\",\"create_time_ms\":\"1727252060000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg\",\"name\":\"Qwen2.5-72b-instruct-11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上, 不好用的话找@付鑫或者@天义\",\"user_id\":\"demo\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1730367565346\",\"labels\":{\"场景\":\"文本生成\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"Qwen2.5-72b-instruct-11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"virtual_svc_url\":\"remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions\",\"security_config_id\":\"cqopj4l74ikh414dlqv0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/llmops/tenants/llmops-assets/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fllmops-assets%2Fprojs%2Fassets%2Favatar%2Ffbbc637b-51fe-4270-9906-f200d0e35395_QWEN.png\"},\"update_time_ms\":\"1730795024000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqopj4l74ikh414dlqv0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8644725351081168:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8644725351081168:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8644725351081168:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8644725351081168:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8644725351081168\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\"}", "ParamDef": "[{\"name\":\"city\",\"type\":\"string\",\"description\":\"用于天气查询的城市名称。如果没有城市信息，你可以询问城市名称。你需要从问题中提取出中文城市名称。\",\"required\":true}]"}, "sub_chain_base_info": null}, {"id": "02724e72-4a02-4205-8c2b-a837b44de6ae", "name": "文本拼接", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "通用文本模板", "desc": "把变量拼接成一段文本，文本模板中允许不引用变量或引用多个变量", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Template", "name": "模板", "default_value": "Use the following content as your knowledge:\n\"\"\"\ntext: {{.text}}\n气温折线图: {{.image}}\n\"\"\"\n\n\nAnswer requirements: \n- If the user's question is not related to your knowledge, please say you don't know. \n- show image\n- Keep your answer consistent with the description in the data. \n- Answer in Chinese. \n\n\nQuestion: {{.question}}", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "text", "name": "text", "desc": "text", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "image", "name": "image", "desc": "image", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持传输方式:Sync,\n支持数据类型:\nString:text\n", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"02724e72-4a02-4205-8c2b-a837b44de6ae\",\"position\":{\"x\":1595.5403331494435,\"y\":11.381537085110978},\"positionAbsolute\":{\"x\":1595.5403331494435,\"y\":11.381537085110978},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "Use the following content as your knowledge:\n\"\"\"\ntext: {{.text}}\n气温折线图: {{.image}}\n\"\"\"\n\n\nAnswer requirements: \n- If the user's question is not related to your knowledge, please say you don't know. \n- show image\n- Keep your answer consistent with the description in the data. \n- Answer in Chinese. \n\n\nQuestion: {{.question}}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-1ebf239b-a828-4a88-9285-07c9e26baaa21ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut-5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a@@Input", "source": "1ebf239b-a828-4a88-9285-07c9e26baaa2", "source_param": "1ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut", "target": "5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a", "target_param": "5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a@@Input"}, {"id": "reactflow__edge-5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a@@Output-cb48b764-f738-4b89-86fb-d06e806a0269cb48b764-f738-4b89-86fb-d06e806a0269@@Params", "source": "5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a", "source_param": "5bf0cc5a-e6a4-40ea-86ad-f7a9ae3dcd6a@@Output", "target": "cb48b764-f738-4b89-86fb-d06e806a0269", "target_param": "cb48b764-f738-4b89-86fb-d06e806a0269@@Params"}, {"id": "reactflow__edge-cb48b764-f738-4b89-86fb-d06e806a0269cb48b764-f738-4b89-86fb-d06e806a0269@@OutPut-02724e72-4a02-4205-8c2b-a837b44de6ae02724e72-4a02-4205-8c2b-a837b44de6ae@@text", "source": "cb48b764-f738-4b89-86fb-d06e806a0269", "source_param": "cb48b764-f738-4b89-86fb-d06e806a0269@@OutPut", "target": "02724e72-4a02-4205-8c2b-a837b44de6ae", "target_param": "02724e72-4a02-4205-8c2b-a837b44de6ae@@text"}, {"id": "reactflow__edge-54d458c3-22dc-46d6-969e-0b0596f7c19154d458c3-22dc-46d6-969e-0b0596f7c191@@OutPut-02724e72-4a02-4205-8c2b-a837b44de6ae02724e72-4a02-4205-8c2b-a837b44de6ae@@image", "source": "54d458c3-22dc-46d6-969e-0b0596f7c191", "source_param": "54d458c3-22dc-46d6-969e-0b0596f7c191@@OutPut", "target": "02724e72-4a02-4205-8c2b-a837b44de6ae", "target_param": "02724e72-4a02-4205-8c2b-a837b44de6ae@@image"}, {"id": "reactflow__edge-1ebf239b-a828-4a88-9285-07c9e26baaa21ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut-02724e72-4a02-4205-8c2b-a837b44de6ae02724e72-4a02-4205-8c2b-a837b44de6ae@@question", "source": "1ebf239b-a828-4a88-9285-07c9e26baaa2", "source_param": "1ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut", "target": "02724e72-4a02-4205-8c2b-a837b44de6ae", "target_param": "02724e72-4a02-4205-8c2b-a837b44de6ae@@question"}, {"id": "reactflow__edge-02724e72-4a02-4205-8c2b-a837b44de6ae02724e72-4a02-4205-8c2b-a837b44de6ae@@OutPut-de9fff8a-6595-4b1c-a068-318cb7c3800dde9fff8a-6595-4b1c-a068-318cb7c3800d@@Text", "source": "02724e72-4a02-4205-8c2b-a837b44de6ae", "source_param": "02724e72-4a02-4205-8c2b-a837b44de6ae@@OutPut", "target": "de9fff8a-6595-4b1c-a068-318cb7c3800d", "target_param": "de9fff8a-6595-4b1c-a068-318cb7c3800d@@Text"}, {"id": "reactflow__edge-9f720b7a-0449-4f7b-a54b-c1e05c575ac49f720b7a-0449-4f7b-a54b-c1e05c575ac4@@OutPut-5cc082d4-909c-4c41-ace5-31117689796c5cc082d4-909c-4c41-ace5-31117689796c@@Input", "source": "9f720b7a-0449-4f7b-a54b-c1e05c575ac4", "source_param": "9f720b7a-0449-4f7b-a54b-c1e05c575ac4@@OutPut", "target": "5cc082d4-909c-4c41-ace5-31117689796c", "target_param": "5cc082d4-909c-4c41-ace5-31117689796c@@Input"}, {"id": "reactflow__edge-5cc082d4-909c-4c41-ace5-31117689796c5cc082d4-909c-4c41-ace5-31117689796c@@Output-54d458c3-22dc-46d6-969e-0b0596f7c19154d458c3-22dc-46d6-969e-0b0596f7c191@@Params", "source": "5cc082d4-909c-4c41-ace5-31117689796c", "source_param": "5cc082d4-909c-4c41-ace5-31117689796c@@Output", "target": "54d458c3-22dc-46d6-969e-0b0596f7c191", "target_param": "54d458c3-22dc-46d6-969e-0b0596f7c191@@Params"}, {"id": "reactflow__edge-cb48b764-f738-4b89-86fb-d06e806a0269cb48b764-f738-4b89-86fb-d06e806a0269@@OutPut-9f720b7a-0449-4f7b-a54b-c1e05c575ac49f720b7a-0449-4f7b-a54b-c1e05c575ac4@@data", "source": "cb48b764-f738-4b89-86fb-d06e806a0269", "source_param": "cb48b764-f738-4b89-86fb-d06e806a0269@@OutPut", "target": "9f720b7a-0449-4f7b-a54b-c1e05c575ac4", "target_param": "9f720b7a-0449-4f7b-a54b-c1e05c575ac4@@data"}], "viewport": {"x": -124.0347027908806, "y": 95.05187599355963, "zoom": 0.6104732563345672}}, "created_time": 0, "updated_time": 0}