{"id": "1-assistant-template.json", "name": "智能助手示例", "desc": "创建智能助手时,所使用的应用链模板示例", "template_group_key": "BasicQA", "template": {"nodes": [{"id": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"{}、text、[{}]...\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"{}、text、[{}]...\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"2bfc1c23-9770-47ab-ab95-ed51a6310102\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-531.1022008286047,\"y\":2083.4002015700826},\"positionAbsolute\":{\"x\":-531.1022008286047,\"y\":2083.4002015700826},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1001}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.answer\n\n"}, "sub_chain_base_info": null}, {"id": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "name": "输入安全护栏", "widget_id": "WidgetKeyInputGuardrail", "widget_detail": {"id": "WidgetKeyInputGuardrail", "name": "输入安全护栏", "desc": "针对输入进行安全检测并配置输出干预话术", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "待检测文本", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "输入安全策略", "desc": "点击配置提示词注入、敏感词防护等安全策略详情", "type": "TYPE_GUARDRAIL_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "配置策略id", "desc": "配置策略id", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "通过安全防护的文本，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"b9fd414e-1f6d-4186-b471-7c21a6b2046c\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-1263.6277719097873,\"y\":1428.3354739834565},\"positionAbsolute\":{\"x\":-1263.6277719097873,\"y\":1428.3354739834565},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "dbff604d-8775-46f9-ae14-5269ec841494", "name": "输出安全护栏", "widget_id": "WidgetKeyOutputGuardrail", "widget_detail": {"id": "WidgetKeyOutputGuardrail", "name": "输出安全护栏", "desc": "针对输出进行安全检测并配置干预话术", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "待检测文本", "desc": "待检测文本,支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "输出安全策略", "desc": "点击配置提示词注入、敏感词防护等安全策略详情", "type": "TYPE_GUARDRAIL_OUTPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "配置策略id", "desc": "配置策略id", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "通过安全防护的文本，支持类型: Any-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"dbff604d-8775-46f9-ae14-5269ec841494\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":773.064596150652,\"y\":2043.3766807329712},\"positionAbsolute\":{\"x\":773.064596150652,\"y\":2043.3766807329712},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"StrategyID": "dbff604d-8775-46f9-ae14-5269ec841494"}, "sub_chain_base_info": null}, {"id": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "name": "文本输入", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "文本输入", "desc": "用于将输入的文本原样输出", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "文本输入", "desc": "文本输入,支持类型: Sync-String: \"text\"。", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"96fd82ba-c1c5-409a-96d8-cb7957629d22\",\"measured\":{\"height\":133,\"width\":320},\"position\":{\"x\":-1827.0535898037822,\"y\":1824.7613300263015},\"positionAbsolute\":{\"x\":-1827.0535898037822,\"y\":1824.7613300263015},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "d078071f-a43c-4679-9c24-638ab0daab2f", "name": "互联网搜索", "widget_id": "WidgetKeyInternetSearch", "widget_detail": {"id": "WidgetKeyInternetSearch", "name": "互联网搜索", "desc": "使用搜索引擎搜索输入的文本", "group": "WidgetGroupVD", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "文本", "desc": "需要进行联网搜索的文本内容，支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Enable", "name": "是否启用", "desc": "是否启用互联网检索算子", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Engine", "name": "搜索引擎", "desc": "可选的搜索引擎，默认为必应", "default_value": "BingSearch", "datasource": "BingSearch@@必应搜索", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ParseUrl", "name": "解析网页", "desc": "是否解析搜索引擎获取的网页详细内容，默认不解析", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "desc": "搜索后的内容，支持类型: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"d078071f-a43c-4679-9c24-638ab0daab2f\",\"measured\":{\"height\":360,\"width\":320},\"position\":{\"x\":-868.9252138803139,\"y\":1080.2776210797367},\"positionAbsolute\":{\"x\":-868.9252138803139,\"y\":1080.2776210797367},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Enable": false, "Engine": "BingSearch", "ParseUrl": false}, "sub_chain_base_info": null}, {"id": "5d9f1042-70e4-4873-9ab7-df71ef529cc5", "name": "对话历史", "widget_id": "WidgetKeyChatHistory", "widget_detail": {"id": "WidgetKeyChatHistory", "name": "对话历史", "desc": "用于将历史对话作为背景知识给下游提示词模版", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyChatHistory", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "ChatInput", "name": "对话输入", "desc": "支持类型: Sync-QAItems: [\n  {\n    \"Q\": \"Q1\",\n    \"A\": \"A1\"\n  },\n  {\n    \"Q\": \"Q2\",\n    \"A\": \"A2\"\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-QAItems"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxRounds", "name": "最大对话轮次", "desc": "历史对话中保留的最大对话轮次, 超出的轮次部分将会被自动截断. 适当的指定该属性可以有效避免超出模型的最大上下文上限", "number_range": {"min": 1, "max": 99, "step": 1}, "default_value": "5", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Tmpl", "name": "对话模板", "desc": "使用GoTemplate把输入的对话历史结构拼接成字符串", "default_value": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}", "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":257,\"id\":\"5d9f1042-70e4-4873-9ab7-df71ef529cc5\",\"measured\":{\"height\":257,\"width\":320},\"position\":{\"x\":-1329.6703856809781,\"y\":1036.7349866147422},\"positionAbsolute\":{\"x\":-1329.6703856809781,\"y\":1036.7349866147422},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"MaxRounds": 5, "Tmpl": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}"}, "sub_chain_base_info": null}, {"id": "43fa9fee-4688-4adc-83a9-48deac514c32", "name": "文件上传", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "文件上传", "desc": "用于加载上传的单个文件, 输出SFSFile类型数据", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "上传文件", "desc": "上传文件,支持类型: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]。", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "读取文件内容", "desc": "是否读取文件内容，开启后会读取文件内容，向下游节点传递文件字节流；否则只会传递文件路径", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "最大文件大小(MB)", "desc": "允许上传的最大文件大小，单位为MB", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "允许的文件扩展名", "desc": "允许上传的文件扩展名列表，每行只允许输入一个扩展名，可输入*、txt、pdf、docx等，当输入 * 或者未输入任何扩展名时表示允许上传所有格式的文件", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "支持类型: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":401,\"id\":\"43fa9fee-4688-4adc-83a9-48deac514c32\",\"measured\":{\"height\":401,\"width\":320},\"position\":{\"x\":-1729.7974816417811,\"y\":1242.5704323013035},\"positionAbsolute\":{\"x\":-1729.7974816417811,\"y\":1242.5704323013035},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["*"], "IsFileContentRead": true, "MaxFileSizeMB": 100}, "sub_chain_base_info": null}, {"id": "6cb90b69-c481-451f-9ef2-35e4e1071548", "name": "条件判断", "widget_id": "WidgetKeyConditionJudge", "widget_detail": {"id": "WidgetKeyConditionJudge", "name": "条件判断", "desc": "把数据流分叉成if分支与else分支，根据输入的条件决定数据流向的分支", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyConditionJudge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "输入数据", "desc": "待执行判断条件的数据,支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "条件成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "判断条件", "desc": "\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\n可以使用下面的语法表示判断条件, 更复杂的判断条件请参考Jsonnet语法\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"\n", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "条件不成立时的输出端点，原样输出输入的数据，支持类型: Sync-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":236,\"id\":\"6cb90b69-c481-451f-9ef2-35e4e1071548\",\"measured\":{\"height\":236,\"width\":320},\"position\":{\"x\":-914.6344501269707,\"y\":2073.347291437207},\"positionAbsolute\":{\"x\":-914.6344501269707,\"y\":2073.347291437207},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "input.answer==\"\""}, "sub_chain_base_info": null}, {"id": "cd4b5759-107f-46de-826e-ef516e294ca7", "name": "数据合流", "widget_id": "WidgetKeyUnion", "widget_detail": {"id": "WidgetKeyUnion", "name": "数据合流", "desc": "将上游多个输入，形成队列串行输出", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyUnion", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "上游数据，任意类型", "desc": "待合流的上游数据，需保证上游只有一个分支有数据流入此算子，否则请用数据合并算子,支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "原样输出输入的上游数据，支持类型: Any-Any: \"任意数据类型\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":132,\"id\":\"cd4b5759-107f-46de-826e-ef516e294ca7\",\"measured\":{\"height\":132,\"width\":320},\"position\":{\"x\":200.94757521254337,\"y\":2216.4677225264627},\"positionAbsolute\":{\"x\":200.94757521254337,\"y\":2216.4677225264627},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "name": "数据合并", "widget_id": "WidgetKeyInputAggregation", "widget_detail": {"id": "WidgetKeyInputAggregation", "name": "数据合并", "desc": "将上游多个输入，以KV的形式组成一个json输出（所有数据到位才会触发输出）", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyInputAggregation", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "answer", "name": "answer", "desc": "answer", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319\",\"measured\":{\"height\":176,\"width\":320},\"position\":{\"x\":-1268.7093523070885,\"y\":2143.1912845778634},\"positionAbsolute\":{\"x\":-1268.7093523070885,\"y\":2143.1912845778634},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "name": "Jsonnet代码", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet代码", "desc": "可对上游输出数据的结构便捷转换，如提取某个字段的值或更改字段名等", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "输入数据", "desc": "需要使用jsonnet代码处理的数据，支持类型: Any-Any: \"{}、text、[{}]...\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet代码", "desc": "jsonnet代码，点击可查看或编辑代码", "default_value": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput\n\n", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "jsonnet代码的执行结果，支持类型: Any-Any: \"{}、text、[{}]...\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"bdbd625f-4ff6-4b60-b996-0aee6c658139\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-1416.8144144455437,\"y\":1747.7023712005973},\"positionAbsolute\":{\"x\":-1416.8144144455437,\"y\":1747.7023712005973},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.question\n\n"}, "sub_chain_base_info": null}, {"id": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "name": "标准问答检索", "widget_id": "WidgetKeyQaSearch", "widget_detail": {"id": "WidgetKeyQaSearch", "name": "标准问答检索", "desc": "在标准问答知识库中检索相似问题，输出预设答案", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyQaSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "问题", "desc": "需要检索的问题，支持类型: Sync-String: \"text\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ScoreThreshold", "name": "匹配度阈值", "desc": "标准问答知识库的匹配度阈值，当检索到的最相似问题的匹配度低于此阈值时，会输出空字符串，建议设置的值不低于0.6", "number_range": {"max": 1, "step": 0.01}, "default_value": "0.800000", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AnswerField", "name": "预设答案字段", "desc": "检索结果是一个字典，请选择使用哪个字段作为预设答案，默认为 \"Answer\"", "default_value": "Answer", "hidden": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "重排模型", "desc": "选择模型,用于对召回的结果进行重排和筛选", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Knowledge", "name": "标准问答知识库", "desc": "选择标准问答场景的知识库，从该知识库中检索最相似的问题，输出预设答案字段对应的内容", "datasource": "is_published_selector=true&scene_type_selector=SceneType_STANDARD", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "最相似问题的预设答案，未检索到时会输出空,支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":375,\"id\":\"ecdac9c0-9741-463f-a6b8-0eda7cbe5628\",\"measured\":{\"height\":375,\"width\":320},\"position\":{\"x\":-1656.271590440024,\"y\":2099.769581932279},\"positionAbsolute\":{\"x\":-1656.271590440024,\"y\":2099.769581932279},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AnswerField": "Answer", "Knowledge": "[]", "ScoreThreshold": 0.8}, "sub_chain_base_info": null}, {"id": "832f5acc-5628-457e-ad6b-08088a9acef3", "name": "知识库检索", "widget_id": "WidgetKeyTextKnowledgeSearch", "widget_detail": {"id": "WidgetKeyTextKnowledgeSearch", "name": "知识库检索", "desc": "依据输入文本从知识库检索,并对结果排序和筛选", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyTextKnowledgeSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "输入", "desc": "检索问题,支持类型: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "最终保留的检索结果数量", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "检索结果被保留的最低阈值要求", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "跨知识库检索", "desc": "设置为跨知识库检索时，需配置rerank模型以提升检索效果", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "知识库信息", "desc": "知识库信息", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "知识库信息", "desc": "限定检索的知识库、及文档范围", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "重排模型", "desc": "选择模型,用于对检索结果进行重排和筛选", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "desc": "从知识库中召回的文本段落，支持类型: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":527,\"id\":\"832f5acc-5628-457e-ad6b-08088a9acef3\",\"measured\":{\"height\":527,\"width\":320},\"position\":{\"x\":-864.9583180855271,\"y\":1469.864481356415},\"positionAbsolute\":{\"x\":-840.9583180855271,\"y\":1539.864481356415},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"EnableMutil": true, "RerankThreshold": 0, "RerankTopK": 3}, "sub_chain_base_info": null}, {"id": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "name": "智能体", "widget_id": "WidgetKeyAgent", "widget_detail": {"id": "WidgetKeyAgent", "name": "智能体", "desc": "在使用大模型的基础上,能自动使用各种工具", "group": "WidgetGroupAdvanced", "oriWidgetKey": "WidgetKeyAgent", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "问题", "desc": "输入给智能体的问题,支持类型: Sync-String: \"text\"。", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Knowledge", "name": "知识库内容", "desc": "在知识库召回的内容,支持类型: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Internet", "name": "互联网内容", "desc": "通过搜索引擎从互联网检索到的内容,支持类型: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n]。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "History", "name": "对话历史", "desc": "用户问题与模型回答的历史记录,支持类型: Sync-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "File", "name": "文件", "desc": "上传的文件内容,支持类型: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_llm_model_svc_str", "name": "LLM模型", "desc": "可用的LLM模型服务", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_enable_trace", "name": "知识溯源", "desc": "知识溯源,区分知识的引用来源", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_rerank_model_svc_str", "name": "溯源模型", "desc": "溯源模型,根据问题对引用来源进行重排序", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"chain_enable_trace==true\"}]", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_prompt", "name": "提示词", "desc": "设置LLM的指令或者角色", "type": "TYPE_AGENT_INSTRUCTION", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_api_collections_str", "name": "插件API", "desc": "允许智能体调用的插件API，从而扩展智能体的功能和应用程序", "type": "TYPE_AGENT_SKILL_API_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_knowledge_base_desc_str", "name": "知识库", "desc": "添加知识库后智能体即可以引用知识的内容来回答用户问题", "datasource": "is_published_selector=true", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_system_services_str", "name": "平台服务", "desc": "允许智能体调用通过Sophon LLMOps部署的模型服务以及应用服务", "type": "TYPE_AGENT_SKILL_SERVICE_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_created_by_chain", "name": "标记为画布方式创建", "desc": "标记为画布方式创建", "default_value": "true", "hidden": true, "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "输出", "desc": "LLM Agent 综合多轮问答以及各类工具调用结果给出的最终回答，支持类型: Stream-String: \"text\"。", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Stream-String"]}}]}, "ui": "{\"dragging\":false,\"id\":\"efa14ce1-5184-4db9-9735-bbc6bc3b2edf\",\"measured\":{\"height\":938,\"width\":320},\"position\":{\"x\":-228,\"y\":1121},\"selected\":false,\"type\":\"custom\",\"zIndex\":9999}", "values": {"chain_created_by_chain": "true", "chain_enable_trace": false, "chain_llm_model_svc_str": "{\"id\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"测试一下\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0.1,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"model\",\"name\":\"model\",\"desc\":\"\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"flag\",\"name\":\"flag\",\"desc\":\"\",\"type\":\"TYPE_SWITCH\",\"data_type\":\"DATA_TYPE_BOOLEAN\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"false\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"}],\"prompt\":\"\",\"namespace\":\"dev-assets\",\"seldon_deploy_name\":\"qwen2-72b-instruct-on-llm11\",\"name\":\"qwen2-72b-instruct-on-llm11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"qwen2-72b-instruct-on-llm11\",\"name_for_human\":\"对话模型:qwen2-72b-instruct-on-llm11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"},{\"name\":\"id\",\"value\":\"MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ\",\"desc\":\"\"},{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用2\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"llama2\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions?id=MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0&path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"{\\r\\n \\\"messages\\\": input.messages,\\r\\n \\\"model\\\": \\\"atom\\\",\\r\\n \\\"stream\\\": input.stream,\\r\\n \\\"temperature\\\": infer_params.temperature,\\r\\n}\"},\"desc\":\"\",\"create_time_ms\":\"1727402756000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0\",\"name\":\"qwen2-72b-instruct-on-llm11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上\",\"user_id\":\"thinger\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1733308948466\",\"labels\":{\"场景\":\"测试\",\"维护人\":\"测试\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"},{\"name\":\"id\",\"value\":\"MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ\",\"desc\":\"\"},{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用2\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"llama2\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions?id=MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0&path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"{\\r\\n \\\"messages\\\": input.messages,\\r\\n \\\"model\\\": \\\"atom\\\",\\r\\n \\\"stream\\\": input.stream,\\r\\n \\\"temperature\\\": infer_params.temperature,\\r\\n}\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":true,\"publish_info\":{\"name\":\"qwen2-72b-instruct-on-llm11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\",\"virtual_svc_url\":\"remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions\",\"security_config_id\":\"cqq4ggco9vpa43qv83u0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"测试一下\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0.1,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"model\",\"name\":\"model\",\"desc\":\"\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"flag\",\"name\":\"flag\",\"desc\":\"\",\"type\":\"TYPE_SWITCH\",\"data_type\":\"DATA_TYPE_BOOLEAN\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"false\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"\"},\"update_time_ms\":\"1733308948000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqq4ggco9vpa43qv83u0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"4974034704197343:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"qwen2-72b-instruct-on-llm11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"4974034704197343:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"4974034704197343:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"4974034704197343:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"qwen2-72b-instruct-on-llm11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"4974034704197343:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"4974034704197343:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\"}", "chain_prompt": "# 角色\n你是一个善于运用各种工具并结合自身能力解决用户问题的AI助手。\n\n## 技能\n### 技能1：解决问题\n- 对用户提出的问题进行分析，识别其关键词和意图。\n- 运用适合的工具和方法，为用户提供最优的解决方案。\n\n### 技能2：利用工具\n- 熟练使用各种在线工具和资源，如搜索引擎、数据分析工具等。\n- 根据问题的性质和复杂度，选择最合适的工具进行处理。\n\n## 约束条件\n- 只应答和问题解决相关的问题，如果用户提出其他类型的问题，不进行回答。\n- 使用与原始提示相同的语言进行回答。\n- 直接以优化提示开始回答，不添加其他内容。"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut-d078071f-a43c-4679-9c24-638ab0daab2fd078071f-a43c-4679-9c24-638ab0daab2f@@Input", "source": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "source_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut", "target": "d078071f-a43c-4679-9c24-638ab0daab2f", "target_param": "d078071f-a43c-4679-9c24-638ab0daab2f@@Input"}, {"id": "reactflow__edge-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@OutPut-dbff604d-8775-46f9-ae14-5269ec841494dbff604d-8775-46f9-ae14-5269ec841494@@Input", "source": "cd4b5759-107f-46de-826e-ef516e294ca7", "source_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@OutPut", "target": "dbff604d-8775-46f9-ae14-5269ec841494", "target_param": "dbff604d-8775-46f9-ae14-5269ec841494@@Input"}, {"id": "reactflow__edge-96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "target_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@question"}, {"id": "reactflow__edge-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@OutPut-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@Input", "source": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "source_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@OutPut", "target": "6cb90b69-c481-451f-9ef2-35e4e1071548", "target_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@Input"}, {"id": "reactflow__edge-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutIF-bdbd625f-4ff6-4b60-b996-0aee6c658139bdbd625f-4ff6-4b60-b996-0aee6c658139@@Content", "source": "6cb90b69-c481-451f-9ef2-35e4e1071548", "source_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutIF", "target": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "target_param": "bdbd625f-4ff6-4b60-b996-0aee6c658139@@Content"}, {"id": "reactflow__edge-bdbd625f-4ff6-4b60-b996-0aee6c658139bdbd625f-4ff6-4b60-b996-0aee6c658139@@OutPut-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@Input", "source": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "source_param": "bdbd625f-4ff6-4b60-b996-0aee6c658139@@OutPut", "target": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "target_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@Input"}, {"id": "reactflow__edge-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutElse-2bfc1c23-9770-47ab-ab95-ed51a63101022bfc1c23-9770-47ab-ab95-ed51a6310102@@Content", "source": "6cb90b69-c481-451f-9ef2-35e4e1071548", "source_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutElse", "target": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "target_param": "2bfc1c23-9770-47ab-ab95-ed51a6310102@@Content"}, {"id": "reactflow__edge-2bfc1c23-9770-47ab-ab95-ed51a63101022bfc1c23-9770-47ab-ab95-ed51a6310102@@OutPut-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@Input", "source": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "source_param": "2bfc1c23-9770-47ab-ab95-ed51a6310102@@OutPut", "target": "cd4b5759-107f-46de-826e-ef516e294ca7", "target_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@Input"}, {"id": "reactflow__edge-96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-ecdac9c0-9741-463f-a6b8-0eda7cbe5628ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@Question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "target_param": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@Question"}, {"id": "reactflow__edge-ecdac9c0-9741-463f-a6b8-0eda7cbe5628ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@OutPut-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@answer", "source": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "source_param": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@OutPut", "target": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "target_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@answer"}, {"id": "reactflow__edge-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut-832f5acc-5628-457e-ad6b-08088a9acef3832f5acc-5628-457e-ad6b-08088a9acef3@@Question", "source": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "source_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut", "target": "832f5acc-5628-457e-ad6b-08088a9acef3", "target_param": "832f5acc-5628-457e-ad6b-08088a9acef3@@Question"}, {"id": "xy-edge__96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Question"}, {"id": "xy-edge__5d9f1042-70e4-4873-9ab7-df71ef529cc55d9f1042-70e4-4873-9ab7-df71ef529cc5@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@History", "source": "5d9f1042-70e4-4873-9ab7-df71ef529cc5", "source_param": "5d9f1042-70e4-4873-9ab7-df71ef529cc5@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@History"}, {"id": "xy-edge__d078071f-a43c-4679-9c24-638ab0daab2fd078071f-a43c-4679-9c24-638ab0daab2f@@Output-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Internet", "source": "d078071f-a43c-4679-9c24-638ab0daab2f", "source_param": "d078071f-a43c-4679-9c24-638ab0daab2f@@Output", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Internet"}, {"id": "xy-edge__832f5acc-5628-457e-ad6b-08088a9acef3832f5acc-5628-457e-ad6b-08088a9acef3@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Knowledge", "source": "832f5acc-5628-457e-ad6b-08088a9acef3", "source_param": "832f5acc-5628-457e-ad6b-08088a9acef3@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Knowledge"}, {"id": "xy-edge__43fa9fee-4688-4adc-83a9-48deac514c3243fa9fee-4688-4adc-83a9-48deac514c32@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@File", "source": "43fa9fee-4688-4adc-83a9-48deac514c32", "source_param": "43fa9fee-4688-4adc-83a9-48deac514c32@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@File"}, {"id": "xy-edge__efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@OutPut-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@Input", "source": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "source_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@OutPut", "target": "cd4b5759-107f-46de-826e-ef516e294ca7", "target_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@Input"}], "viewport": {"x": 888.684544997741, "y": -411.86923228212083, "zoom": 0.40951834892991396}}, "created_time": 0, "updated_time": 0}