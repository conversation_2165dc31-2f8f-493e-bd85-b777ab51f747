{"id": "邮件发送.json", "name": "Email Sending", "desc": "Send an email to the specified recipient.", "template_group_key": "ToolsCall", "template": {"nodes": [{"id": "1ebf239b-a828-4a88-9285-07c9e26baaa2", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"1ebf239b-a828-4a88-9285-07c9e26baaa2\",\"position\":{\"x\":-37.47948450461149,\"y\":54.483305280189825},\"positionAbsolute\":{\"x\":-37.47948450461149,\"y\":54.483305280189825},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "eadfe26a-8bdd-4640-b089-2affb6f25544", "name": "Parameter Extractor", "widget_id": "WidgetKeyParameterExtractor", "widget_detail": {"id": "WidgetKeyParameterExtractor", "name": "Parameter Extraction for Invocation", "desc": "Extract the specified parameters from user input", "group": "WidgetGroupChainTool", "oriWidgetKey": "WidgetKeyParameterExtractor", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input text", "desc": "Input text that requires parameter extraction, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelService", "name": "Model Service", "desc": "Model service for parameter extraction", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "ParamDef", "name": "Parameter Definition", "desc": "Define the parameters that need to be extracted", "required": true, "type": "TYPE_PARAMETER_EXTRACTOR", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "Extract the result", "desc": "Extracted parameters, supported type: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}]}, "ui": "{\"height\":403,\"id\":\"eadfe26a-8bdd-4640-b089-2affb6f25544\",\"position\":{\"x\":546.5558449533182,\"y\":38.50091793592239},\"positionAbsolute\":{\"x\":546.5558449533182,\"y\":38.50091793592239},\"type\":\"custom\",\"width\":320}", "values": {"ModelService": "{\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"Qwen2.5-72b-instruct-11\",\"name\":\"Qwen2.5-72b-instruct-11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5-72b-instruct-11\",\"name_for_human\":\"对话模型:Qwen2.5-72b-instruct-11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"desc\":\"\",\"create_time_ms\":\"1727252060000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg\",\"name\":\"Qwen2.5-72b-instruct-11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上, 不好用的话找@付鑫或者@天义\",\"user_id\":\"demo\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1730367565346\",\"labels\":{\"场景\":\"文本生成\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[],\"path_params\":[],\"headers\":[{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"atom\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"std.mergePatch(input, {\\\"temperature\\\":0.0})\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":false,\"publish_info\":{\"name\":\"Qwen2.5-72b-instruct-11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\",\"virtual_svc_url\":\"remote/llmops/MWH-REMOTE-SERVICE-cqudbmnua89gepmu8nrg/openai/v1/chat/completions\",\"security_config_id\":\"cqopj4l74ikh414dlqv0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":8096,\"step\":0},\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"/llm/llmops/tenants/llmops-assets/gateway/datamgr/api/v1/datamgr/file?path=tenants%2Fllmops-assets%2Fprojs%2Fassets%2Favatar%2Ffbbc637b-51fe-4270-9906-f200d0e35395_QWEN.png\"},\"update_time_ms\":\"1730795024000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqopj4l74ikh414dlqv0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"8497752960722861:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"8497752960722861:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"8497752960722861:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"8497752960722861:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-72b-instruct-11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8497752960722861:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"8497752960722861:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"8497752960722861\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"3f652030-56b4-48b9-9736-b531f53f6fd5\"}", "ParamDef": "[{\"name\":\"receivers\",\"desc\":\"邮件收件人列表\",\"param_value_type\":\"array\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"subject\",\"desc\":\"电子邮件标题\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"text\",\"desc\":\"电子邮件内容\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"cc\",\"desc\":\"邮件抄送人列表，为可选参数\",\"param_value_type\":\"array\",\"type\":\"body\",\"required\":false,\"default_value\":null,\"model_ignore\":false}]"}, "sub_chain_base_info": null}, {"id": "bef6159d-dd6d-485c-896f-b886a1f483ce", "name": "Tool invocation", "widget_id": "WidgetKeyToolCall", "widget_detail": {"id": "WidgetKeyToolCall", "name": "Application plugin invocation", "desc": "Invoke the published application plugin", "group": "WidgetGroupChainTool", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ToolDescriber", "name": "Tool Information", "desc": "Specific description information of the tool", "type": "TYPE_TOOL_CALL", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Params", "name": "Tool Parameters", "desc": "Parameters used when calling the tool, supported type: Sync-map[string]any: {\n  \"k1\": \"v1\",\n  \"k2\": 2\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-map[string]any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Tool invocation result, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":532,\"id\":\"bef6159d-dd6d-485c-896f-b886a1f483ce\",\"position\":{\"x\":1187.082382983357,\"y\":45.61787946958948},\"positionAbsolute\":{\"x\":1187.082382983357,\"y\":45.61787946958948},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"ToolDescriber": "{\"id\":\"builtin-tool-22d2967f-e5f2-3f\",\"base_url\":\"http://autocv-agent-tool-api-service.llmops/v1/\",\"method\":\"post\",\"headers\":{},\"api_path\":\"send_emails\",\"name\":\"send_emails\",\"desc\":\"一个用来发送电子邮件的工具，需要收件人列表、邮件主题和正文信息\",\"params\":[{\"name\":\"receivers\",\"desc\":\"邮件收件人列表\",\"param_value_type\":\"array\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"subject\",\"desc\":\"电子邮件标题\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"text\",\"desc\":\"电子邮件内容\",\"param_value_type\":\"string\",\"type\":\"body\",\"required\":true,\"default_value\":null,\"model_ignore\":false},{\"name\":\"cc\",\"desc\":\"邮件抄送人列表，为可选参数\",\"param_value_type\":\"array\",\"type\":\"body\",\"required\":false,\"default_value\":null,\"model_ignore\":false}],\"collection_id\":\"builtin-collection-552b29f0-1163-37\",\"collection_name\":\"邮件发送\"}"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-1ebf239b-a828-4a88-9285-07c9e26baaa21ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut-eadfe26a-8bdd-4640-b089-2affb6f25544eadfe26a-8bdd-4640-b089-2affb6f25544@@Input", "source": "1ebf239b-a828-4a88-9285-07c9e26baaa2", "source_param": "1ebf239b-a828-4a88-9285-07c9e26baaa2@@OutPut", "target": "eadfe26a-8bdd-4640-b089-2affb6f25544", "target_param": "eadfe26a-8bdd-4640-b089-2affb6f25544@@Input"}, {"id": "reactflow__edge-eadfe26a-8bdd-4640-b089-2affb6f25544eadfe26a-8bdd-4640-b089-2affb6f25544@@Output-bef6159d-dd6d-485c-896f-b886a1f483cebef6159d-dd6d-485c-896f-b886a1f483ce@@Params", "source": "eadfe26a-8bdd-4640-b089-2affb6f25544", "source_param": "eadfe26a-8bdd-4640-b089-2affb6f25544@@Output", "target": "bef6159d-dd6d-485c-896f-b886a1f483ce", "target_param": "bef6159d-dd6d-485c-896f-b886a1f483ce@@Params"}], "viewport": {"x": 69.01882884443603, "y": 104.95129546942744, "zoom": 0.7025470035698915}}, "created_time": 0, "updated_time": 0}