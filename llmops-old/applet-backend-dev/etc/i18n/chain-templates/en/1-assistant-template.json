{"id": "1-assistant-template.json", "name": "Smart Assistant Example", "desc": "Examples of application chain templates used when creating intelligent assistants", "template_group_key": "BasicQA", "template": {"nodes": [{"id": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "name": "Jsonnet Code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet Code", "desc": "The structure of upstream output data can be conveniently transformed, such as extracting a specific field value or renaming fields.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "jsonnet code", "desc": "jsonnet code, click to view or edit the code", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "jsonnet code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"2bfc1c23-9770-47ab-ab95-ed51a6310102\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-531.1022008286047,\"y\":2083.4002015700826},\"positionAbsolute\":{\"x\":-531.1022008286047,\"y\":2083.4002015700826},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1001}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.answer\n\n"}, "sub_chain_base_info": null}, {"id": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "name": "Input safety guardrail", "widget_id": "WidgetKeyInputGuardrail", "widget_detail": {"id": "WidgetKeyInputGuardrail", "name": "Input safety guardrails", "desc": "Conduct security checks on the input and configure the output intervention script.", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text to be detected", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "Input Security Policy", "desc": "Click to view the details of safety strategies such as configuration prompt injection and sensitive word protection.", "type": "TYPE_GUARDRAIL_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "Configure strategy ID", "desc": "Configure strategy ID", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Through secure protection, the supported type is: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"b9fd414e-1f6d-4186-b471-7c21a6b2046c\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-1263.6277719097873,\"y\":1428.3354739834565},\"positionAbsolute\":{\"x\":-1263.6277719097873,\"y\":1428.3354739834565},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "dbff604d-8775-46f9-ae14-5269ec841494", "name": "Output Safety Guardrails", "widget_id": "WidgetKeyOutputGuardrail", "widget_detail": {"id": "WidgetKeyOutputGuardrail", "name": "Output Safety Guardrails", "desc": "Conduct safety checks on the output and configure intervention scripts", "group": "WidgetGroupProcessText", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text to be detected", "desc": "support type: Any-String: \"text\". \n\nNote: The translation provided is a direct translation of the given text. However, it seems the original text contains a mix of Chinese and English, and the phrase \"Any-String: 'text'\" is already in English and commonly used in programming contexts. If you need a more contextually appropriate translation, please let me know. \n\nFor a more natural English translation:\nText to be detected, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Strategy", "name": "Output Security Policy", "desc": "Click to view the details of safety strategies such as configuration prompt injection and sensitive word protection.", "type": "TYPE_GUARDRAIL_OUTPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "StrategyID", "name": "Configure strategy ID", "desc": "Configure strategy ID", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Through security protection, the supported type is: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"dbff604d-8775-46f9-ae14-5269ec841494\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":773.064596150652,\"y\":2043.3766807329712},\"positionAbsolute\":{\"x\":773.064596150652,\"y\":2043.3766807329712},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"StrategyID": "dbff604d-8775-46f9-ae14-5269ec841494"}, "sub_chain_base_info": null}, {"id": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"96fd82ba-c1c5-409a-96d8-cb7957629d22\",\"measured\":{\"height\":133,\"width\":320},\"position\":{\"x\":-1827.0535898037822,\"y\":1824.7613300263015},\"positionAbsolute\":{\"x\":-1827.0535898037822,\"y\":1824.7613300263015},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "d078071f-a43c-4679-9c24-638ab0daab2f", "name": "Internet search", "widget_id": "WidgetKeyInternetSearch", "widget_detail": {"id": "WidgetKeyInternetSearch", "name": "Internet search", "desc": "Use a search engine to search for the input text", "group": "WidgetGroupVD", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Text", "desc": "The text content that requires online search, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Enable", "name": "Whether to enable", "desc": "Whether to enable the internet search operator", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Engine", "name": "Search Engine", "desc": "Optional search engine, default is Bing", "default_value": "BingSearch", "datasource": "BingSearch@@BingSearch", "required": true, "type": "TYPE_SELECTOR", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ParseUrl", "name": "Parse the webpage", "desc": "Whether to parse the detailed content of web pages obtained from search engines, default is not to parse.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "Output", "name": "Output", "desc": "Search results, supported type: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}]}, "ui": "{\"dragging\":false,\"height\":360,\"id\":\"d078071f-a43c-4679-9c24-638ab0daab2f\",\"measured\":{\"height\":360,\"width\":320},\"position\":{\"x\":-868.9252138803139,\"y\":1080.2776210797367},\"positionAbsolute\":{\"x\":-868.9252138803139,\"y\":1080.2776210797367},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Enable": false, "Engine": "BingSearch", "ParseUrl": false}, "sub_chain_base_info": null}, {"id": "5d9f1042-70e4-4873-9ab7-df71ef529cc5", "name": "Dialogue History", "widget_id": "WidgetKeyChatHistory", "widget_detail": {"id": "WidgetKeyChatHistory", "name": "Dialogue History", "desc": "Used to provide historical dialogue as background knowledge for downstream prompt templates", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyChatHistory", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "ChatInput", "name": "Dialogue Input", "desc": "Support Type: Sync-QAItems: [\n  {\n    \"Q\": \"Q1\",\n    \"A\": \"A1\"\n  },\n  {\n    \"Q\": \"Q2\",\n    \"A\": \"A2\"\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-QAItems"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxRounds", "name": "Maximum number of dialogue turns", "desc": "The maximum number of dialogue turns retained in historical conversations, any turns exceeding this limit will be automatically truncated. Appropriately setting this attribute can effectively prevent exceeding the model's maximum context limit.", "number_range": {"min": 1, "max": 99, "step": 1}, "default_value": "5", "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Tmpl", "name": "Dialogue Template", "desc": "Using GoTemplate to Concatenate the Input Conversation History Structure into a String", "default_value": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}", "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":257,\"id\":\"5d9f1042-70e4-4873-9ab7-df71ef529cc5\",\"measured\":{\"height\":257,\"width\":320},\"position\":{\"x\":-1329.6703856809781,\"y\":1036.7349866147422},\"positionAbsolute\":{\"x\":-1329.6703856809781,\"y\":1036.7349866147422},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"MaxRounds": 5, "Tmpl": "{{/*\ngo text template\n把对话历史消息转换为纯文本，传给下游算子\n原始历史消息结构:\n[\n  {\"Q\": \"question1\", \"A\": \"answer1\"}, \n  {\"Q\": \"question2\", \"A\": \"answer2\"}\n]\n转换后的消息为\n[Round0]\n用户:question1\n助手:answer1\n\n[Round1]\n用户:question2\n助手:answer2\n\n...\n*/}}\n{{range $index, $qa := .}}\n[Round{{$index}}]\n用户：{{$qa.Q}}\n助手：{{$qa.A}}\n{{end}}"}, "sub_chain_base_info": null}, {"id": "43fa9fee-4688-4adc-83a9-48deac514c32", "name": "File Upload", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "File Upload", "desc": "Used for loading a single uploaded file, outputting data of SFSFile type.", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "Upload File", "desc": "Upload file, supported types: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "Read file content", "desc": "Whether to read the file content, if enabled, it will read the file content and pass the file byte stream to the downstream node; otherwise, it will only pass the file path.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "Maximum file size (MB)", "desc": "The maximum file size allowed for upload, in MB.", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "Allowed file extensions", "desc": "List of allowed file extensions, only one extension per line is permitted, you can enter *, txt, pdf, docx, etc. Entering * or leaving it blank means all file formats are allowed.", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Support Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":401,\"id\":\"43fa9fee-4688-4adc-83a9-48deac514c32\",\"measured\":{\"height\":401,\"width\":320},\"position\":{\"x\":-1729.7974816417811,\"y\":1242.5704323013035},\"positionAbsolute\":{\"x\":-1729.7974816417811,\"y\":1242.5704323013035},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["*"], "IsFileContentRead": true, "MaxFileSizeMB": 100}, "sub_chain_base_info": null}, {"id": "6cb90b69-c481-451f-9ef2-35e4e1071548", "name": "Conditional Judgment", "widget_id": "WidgetKeyConditionJudge", "widget_detail": {"id": "WidgetKeyConditionJudge", "name": "Conditional Judgment", "desc": "Fork the data flow into if and else branches, determining the branch based on the input condition.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyConditionJudge", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Input data", "desc": "Data for pending judgment conditions, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutIF", "name": "If", "desc": "Output endpoint when the condition is met, outputs the input data as is, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "Judgment Conditions", "desc": "Use the input variable to store data passed from upstream. Assuming the data stored in input is:\n{\n  \"string\": \"string\",\n  \"number\": 123,\n  \"dict\": { \"k\": \"v\" }\n}\nYou can use the following syntax to represent conditional statements; for more complex conditions, please refer to the Jsonnet syntax:\ninput.number == 123 && input.number > 100 || input.dict.k == \"v\"", "required": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPutElse", "name": "Else", "desc": "Output endpoint when the condition is not met, outputs the input data as is, supported type: Sync-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":236,\"id\":\"6cb90b69-c481-451f-9ef2-35e4e1071548\",\"measured\":{\"height\":236,\"width\":320},\"position\":{\"x\":-914.6344501269707,\"y\":2073.347291437207},\"positionAbsolute\":{\"x\":-914.6344501269707,\"y\":2073.347291437207},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "input.answer==\"\""}, "sub_chain_base_info": null}, {"id": "cd4b5759-107f-46de-826e-ef516e294ca7", "name": "Data Convergence", "widget_id": "WidgetKeyUnion", "widget_detail": {"id": "WidgetKeyUnion", "name": "Data Convergence", "desc": "Form a queue from multiple upstream inputs and output them serially.", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyUnion", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Input", "name": "Upstream data, any type", "desc": "The upstream data to be merged must ensure that only one branch has data flowing into this operator; otherwise, please use the data merging operator. Supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Output the upstream data as is, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":132,\"id\":\"cd4b5759-107f-46de-826e-ef516e294ca7\",\"measured\":{\"height\":132,\"width\":320},\"position\":{\"x\":200.94757521254337,\"y\":2216.4677225264627},\"positionAbsolute\":{\"x\":200.94757521254337,\"y\":2216.4677225264627},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "name": "Data Integration", "widget_id": "WidgetKeyInputAggregation", "widget_detail": {"id": "WidgetKeyInputAggregation", "name": "Data Integration", "desc": "Compose multiple upstream inputs into a JSON output in KV format (output is triggered only when all data is available)", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyInputAggregation", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "answer", "name": "answer", "desc": "answer", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319\",\"measured\":{\"height\":176,\"width\":320},\"position\":{\"x\":-1268.7093523070885,\"y\":2143.1912845778634},\"positionAbsolute\":{\"x\":-1268.7093523070885,\"y\":2143.1912845778634},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "name": "Jsonnet Code", "widget_id": "WidgetKeyJsonnetWidget", "widget_detail": {"id": "WidgetKeyJsonnetWidget", "name": "Jsonnet Code", "desc": "The structure of upstream output data can be conveniently transformed, such as extracting a specific field value or renaming fields.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyJsonnetWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Jsonnet code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "required": true, "type": "TYPE_CODE_JSONNET", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "jsonnet code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"bdbd625f-4ff6-4b60-b996-0aee6c658139\",\"measured\":{\"height\":180,\"width\":320},\"position\":{\"x\":-1416.8144144455437,\"y\":1747.7023712005973},\"positionAbsolute\":{\"x\":-1416.8144144455437,\"y\":1747.7023712005973},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n/*jsonnet\n使用input变量存储上游传入的数据\n假设input存储的数据为\n{\n  \"string\": \"str\",\n  \"number\": 123,\n  \"list\": [1, 2, 3],\n  \"dict\": { \"k\": \"v\" }\n}\n\n如下为几个使用示例\n1.保持原样输出\ninput\n2.取出某个值输出：\ninput.list\n3.组装成新的数据结构\n{ myString: input.string, myList: input.list, myNumber: input.list[0], }\n*/\n\ninput.question\n\n"}, "sub_chain_base_info": null}, {"id": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "name": "Standard Q&A Retrieval", "widget_id": "WidgetKeyQaSearch", "widget_detail": {"id": "WidgetKeyQaSearch", "name": "Standard Q&A Retrieval", "desc": "Retrieve similar questions from the standard Q&A knowledge base and output the preset answers.", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyQaSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Question", "desc": "The question to be searched, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ScoreThreshold", "name": "<PERSON><PERSON><PERSON><PERSON> of Match Degree", "desc": "The matching threshold for the standard Q&A knowledge base. When the similarity of the most similar question retrieved is below this threshold, an empty string will be output. It is recommended to set the value no less than 0.6.", "number_range": {"max": 1, "step": 0.01}, "default_value": "0.800000", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AnswerField", "name": "Preset Answer Fields", "desc": "The search result is a dictionary, please choose which field to use as the default answer, the default is \"Answer\"", "default_value": "Answer", "hidden": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Selecting models for re-ranking and filtering the recalled results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Knowledge", "name": "Standard Q&A Knowledge Base", "desc": "Select a knowledge base for Q&A scenarios, retrieve the most similar question from the knowledge base, and output the content corresponding to the preset answer field.", "datasource": "is_published_selector=true&scene_type_selector=SceneType_STANDARD", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "The preset answer for the most similar question, outputs empty when not retrieved, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":375,\"id\":\"ecdac9c0-9741-463f-a6b8-0eda7cbe5628\",\"measured\":{\"height\":375,\"width\":320},\"position\":{\"x\":-1656.271590440024,\"y\":2099.769581932279},\"positionAbsolute\":{\"x\":-1656.271590440024,\"y\":2099.769581932279},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AnswerField": "Answer", "Knowledge": "[]", "ScoreThreshold": 0.8}, "sub_chain_base_info": null}, {"id": "832f5acc-5628-457e-ad6b-08088a9acef3", "name": "Knowledge Base Search", "widget_id": "WidgetKeyTextKnowledgeSearch", "widget_detail": {"id": "WidgetKeyTextKnowledgeSearch", "name": "Knowledge Base Search", "desc": "Retrieve from the knowledge base based on the input text, and sort and filter the results.", "group": "WidgetGroupVD", "oriWidgetKey": "WidgetKeyTextKnowledgeSearch", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Input", "desc": "Retrieve the issue, supported types: Sync-String: \"text\", Sync-Strings: [\n  \"text1\",\n  \"text2\"\n], Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String", "Sync-Strings", "Sync-Chunks"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankTopK", "name": "TopK", "desc": "The final number of retained search results", "default_value": "3", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Rerank<PERSON><PERSON><PERSON>old", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "The minimum threshold requirement for retaining search results", "default_value": "0", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_FLOAT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "EnableMutil", "name": "Cross-Knowledge Base Retrieval", "desc": "When set to cross-knowledge base search, the rerank model needs to be configured to improve search effectiveness.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SimpleKbInfo", "name": "Knowledge Base Information", "desc": "Knowledge Base Information", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==false\"}]", "required": true, "type": "TYPE_TEXT_KNOWLEDGE_BASE", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "KnowledgeBaseDesc", "name": "Knowledge Base Information", "desc": "Limited knowledge base and document scope", "datasource": "is_published_selector=true", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "RerankModel", "name": "Reordered Model", "desc": "Selecting models for re-ranking and filtering retrieval results", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"EnableMutil==true\"}]", "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Recall paragraphs from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}]}, "ui": "{\"dragging\":false,\"height\":527,\"id\":\"832f5acc-5628-457e-ad6b-08088a9acef3\",\"measured\":{\"height\":527,\"width\":320},\"position\":{\"x\":-864.9583180855271,\"y\":1469.864481356415},\"positionAbsolute\":{\"x\":-840.9583180855271,\"y\":1539.864481356415},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"EnableMutil": true, "RerankThreshold": 0, "RerankTopK": 3}, "sub_chain_base_info": null}, {"id": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "name": "Intelligent Agent", "widget_id": "WidgetKeyAgent", "widget_detail": {"id": "WidgetKeyAgent", "name": "Intelligent Agent", "desc": "Based on the use of large models, it can automatically utilize various tools.", "group": "WidgetGroupAdvanced", "oriWidgetKey": "WidgetKeyAgent", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Question", "desc": "Input questions for the agent, supported type: Sync-String: \"text\".", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Knowledge", "name": "Knowledge Base Content", "desc": "In the content recalled from the knowledge base, supported types: Sync-Chunks: [\n  {\n    \"id\": \"id\",\n    \"content\": \"content\",\n    \"element_ids\": [\n      \"id1\",\n      \"id2\"\n    ]\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-Chunks"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Internet", "name": "Internet content", "desc": "Content retrieved from the internet via search engine, supported type: Sync-InternetCitations: [\n  {\n    \"citation_type\": \"internet_search\",\n    \"content\": \"content\",\n    \"internet_search_details\": {\n      \"title\": \"title\",\n      \"snippet\": \"snippet\",\n      \"url\": \"url\"\n    }\n  }\n]", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-InternetCitations"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "History", "name": "Dialogue History", "desc": "User question and model response history records, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "File", "name": "File", "desc": "Uploaded file content, supported type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_llm_model_svc_str", "name": "LLM model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_enable_trace", "name": "Knowledge Traceability", "desc": "Knowledge Traceability, Distinguishing the Sources of Knowledge References", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_rerank_model_svc_str", "name": "Traceback Model", "desc": "Traceback model, re-rank the reference sources based on the question", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_RERANKING\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "precondition": "[{\"both\":\"chain_enable_trace==true\"}]", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_prompt", "name": "Prompt word", "desc": "Setting instructions or roles for LLM", "type": "TYPE_AGENT_INSTRUCTION", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_api_collections_str", "name": "Plugin API", "desc": "Allowing the agent to call plugin APIs, thereby extending the agent's capabilities and applications", "type": "TYPE_AGENT_SKILL_API_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_knowledge_base_desc_str", "name": "Knowledge Base", "desc": "After adding the knowledge base, the intelligent agent can reference the content of the knowledge to answer user questions.", "datasource": "is_published_selector=true", "type": "TYPE_AGENT_<PERSON><PERSON>L_KNOW_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_system_services_str", "name": "Platform Services", "desc": "Allow the agent to invoke model services and application services deployed through Sophon LLMOps.", "type": "TYPE_AGENT_SKILL_SERVICE_TOOLS", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "chain_created_by_chain", "name": "Marked as created in canvas mode", "desc": "Marked as created in canvas mode", "default_value": "true", "hidden": true, "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "LLM Agent provides the final answer by integrating multi-round Q&A and various tool invocation results, supported type: Stream-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Stream-String"]}}]}, "ui": "{\"dragging\":false,\"id\":\"efa14ce1-5184-4db9-9735-bbc6bc3b2edf\",\"measured\":{\"height\":938,\"width\":320},\"position\":{\"x\":-228,\"y\":1121},\"selected\":false,\"type\":\"custom\",\"zIndex\":9999}", "values": {"chain_created_by_chain": "true", "chain_enable_trace": false, "chain_llm_model_svc_str": "{\"id\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\",\"schema\":\"MODEL_SERVICE_SCHEMA_HTTP\",\"host\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions?project_id=assets\",\"port\":0,\"type\":\"MODEL_SERVICE_TYPE_REMOTE\",\"apis\":[],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"\",\"release_name\":\"\",\"release_version\":\"\",\"model_id\":\"\",\"release_id\":\"\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"测试一下\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0.1,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"model\",\"name\":\"model\",\"desc\":\"\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"},{\"id\":\"flag\",\"name\":\"flag\",\"desc\":\"\",\"type\":\"TYPE_SWITCH\",\"data_type\":\"DATA_TYPE_BOOLEAN\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"false\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\",\"className\":\"w-full nodrag\"}],\"prompt\":\"\",\"namespace\":\"dev-assets\",\"seldon_deploy_name\":\"qwen2-72b-instruct-on-llm11\",\"name\":\"qwen2-72b-instruct-on-llm11\",\"full_url\":\"http://istio-ingressgateway.istio-system/remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"qwen2-72b-instruct-on-llm11\",\"name_for_human\":\"对话模型:qwen2-72b-instruct-on-llm11\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"},{\"name\":\"id\",\"value\":\"MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ\",\"desc\":\"\"},{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用2\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"llama2\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions?id=MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0&path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"{\\r\\n \\\"messages\\\": input.messages,\\r\\n \\\"model\\\": \\\"atom\\\",\\r\\n \\\"stream\\\": input.stream,\\r\\n \\\"temperature\\\": infer_params.temperature,\\r\\n}\"},\"desc\":\"\",\"create_time_ms\":\"1727402756000\",\"reference_model\":null,\"reference_release\":null,\"project_id\":\"assets\",\"reference_remote_service\":{\"id\":\"MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0\",\"name\":\"qwen2-72b-instruct-on-llm11\",\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"detail\":{\"desc\":\"http://*************:8080/?folder=/models\\n\\n启动的Code Server IDE 如上\",\"user_id\":\"thinger\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1733308948466\",\"labels\":{\"场景\":\"测试\",\"维护人\":\"测试\"}},\"api_config\":{\"method\":\"REMOTE_SERVICE_METHOD_POST\",\"url\":\"http://*************:8011/openai/v1/chat/completions\",\"query_params\":[{\"name\":\"path\",\"value\":\"atom\",\"desc\":\"\"},{\"name\":\"id\",\"value\":\"MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0\",\"desc\":\"\"}],\"path_params\":[],\"headers\":[{\"name\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5ODIwNzUsImlhdCI6MTY1NjM4MjA3NSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJleHRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.NAl4iMaAwMiiFRdc35pRRjdyZYzFuJvJMd9lnvz8M3xDzj8ThtokDXqFVE6SXG0vwe20UMwJtpfZJEhvfYgjHQ\",\"desc\":\"\"},{\"name\":\"Content-Type\",\"value\":\"application/json\",\"desc\":\"\"}],\"body\":\"{\\n \\\"messages\\\": [\\n  {\\n   \\\"role\\\": \\\"system\\\",\\n   \\\"content\\\": \\\"你是一个很有用助手\\\"\\n  },\\n  {\\n   \\\"role\\\": \\\"user\\\",\\n   \\\"content\\\": \\\"请简单介绍一下自己的作用2\\\"\\n  }\\n ],\\n \\\"model\\\": \\\"llama2\\\",\\n \\\"stream\\\": true\\n}\",\"use_proxy\":false,\"proxy\":null,\"full_url\":\"http://*************:8011/openai/v1/chat/completions?id=MWH-DEPLOYMENT-cpkr9n2kbjgckft95jd0&path=atom\",\"computation_attributes\":{},\"interface_spec\":\"INTERFACE_SPEC_OPENAI\",\"request_process_script\":\"{\\r\\n \\\"messages\\\": input.messages,\\r\\n \\\"model\\\": \\\"atom\\\",\\r\\n \\\"stream\\\": input.stream,\\r\\n \\\"temperature\\\": infer_params.temperature,\\r\\n}\"},\"status\":null,\"project_id\":\"assets\",\"chat_mode\":false,\"is_published\":true,\"publish_info\":{\"name\":\"qwen2-72b-instruct-on-llm11\",\"desc\":\"\",\"rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"is_security\":false,\"id\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\",\"virtual_svc_url\":\"remote/dev/MWH-REMOTE-SERVICE-cqud1c0qlnfp0gd0pjp0/openai/v1/chat/completions\",\"security_config_id\":\"cqq4ggco9vpa43qv83u0\",\"user_rate_limit\":{\"request_limit\":0,\"unit_interval\":0,\"enabled\":false,\"token_per_minute\":0},\"members\":[]},\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"测试一下\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0.1,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"model\",\"name\":\"model\",\"desc\":\"\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"atom\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"flag\",\"name\":\"flag\",\"desc\":\"\",\"type\":\"TYPE_SWITCH\",\"data_type\":\"DATA_TYPE_BOOLEAN\",\"number_range\":{\"min\":0,\"max\":0,\"step\":0},\"default_value\":\"false\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"logo\":\"\"},\"update_time_ms\":\"1733308948000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"cqq4ggco9vpa43qv83u0\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"4974034704197343:服务名称\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"服务名称\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"disabled\":false,\"children\":\"qwen2-72b-instruct-on-llm11\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"4974034704197343:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"4974034704197343:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"4974034704197343:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"qwen2-72b-instruct-on-llm11\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#05b9c5\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"4974034704197343:MODEL_SERVICE_TYPE_REMOTE\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SERVICE_TYPE_REMOTE\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null},{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"4974034704197343:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"4974034704197343\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"a365e4ad-7315-4dfc-bd72-0af82d37ada5\"}", "chain_prompt": "# Role\n      You are an AI assistant who is good at using various tools and combining their own abilities to solve user problems.\n      \n      ## Skills\n      ### Skill 1: Problem solving\n      - Analyze the questions raised by users and identify their keywords and intentions.\n      - Use the right tools and methods to provide users with the best solution.\n      \n      ### Skill 2：Using Tools\n      - Proficiency in using various online tools and resources, such as search engines, data analysis tools, etc.\n      - Depending on the nature and complexity of the problem, choose the most appropriate tool for processing. \n      \n      ## Constraint\n      - Answer only questions related to problem solving, if the user asks other types of questions, do not answer.\n      - Answer in the same language as the original prompt. \n      - Start your answer directly with the optimization prompt and add nothing else."}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut-d078071f-a43c-4679-9c24-638ab0daab2fd078071f-a43c-4679-9c24-638ab0daab2f@@Input", "source": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "source_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut", "target": "d078071f-a43c-4679-9c24-638ab0daab2f", "target_param": "d078071f-a43c-4679-9c24-638ab0daab2f@@Input"}, {"id": "reactflow__edge-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@OutPut-dbff604d-8775-46f9-ae14-5269ec841494dbff604d-8775-46f9-ae14-5269ec841494@@Input", "source": "cd4b5759-107f-46de-826e-ef516e294ca7", "source_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@OutPut", "target": "dbff604d-8775-46f9-ae14-5269ec841494", "target_param": "dbff604d-8775-46f9-ae14-5269ec841494@@Input"}, {"id": "reactflow__edge-96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "target_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@question"}, {"id": "reactflow__edge-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@OutPut-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@Input", "source": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "source_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@OutPut", "target": "6cb90b69-c481-451f-9ef2-35e4e1071548", "target_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@Input"}, {"id": "reactflow__edge-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutIF-bdbd625f-4ff6-4b60-b996-0aee6c658139bdbd625f-4ff6-4b60-b996-0aee6c658139@@Content", "source": "6cb90b69-c481-451f-9ef2-35e4e1071548", "source_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutIF", "target": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "target_param": "bdbd625f-4ff6-4b60-b996-0aee6c658139@@Content"}, {"id": "reactflow__edge-bdbd625f-4ff6-4b60-b996-0aee6c658139bdbd625f-4ff6-4b60-b996-0aee6c658139@@OutPut-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@Input", "source": "bdbd625f-4ff6-4b60-b996-0aee6c658139", "source_param": "bdbd625f-4ff6-4b60-b996-0aee6c658139@@OutPut", "target": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "target_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@Input"}, {"id": "reactflow__edge-6cb90b69-c481-451f-9ef2-35e4e10715486cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutElse-2bfc1c23-9770-47ab-ab95-ed51a63101022bfc1c23-9770-47ab-ab95-ed51a6310102@@Content", "source": "6cb90b69-c481-451f-9ef2-35e4e1071548", "source_param": "6cb90b69-c481-451f-9ef2-35e4e1071548@@OutPutElse", "target": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "target_param": "2bfc1c23-9770-47ab-ab95-ed51a6310102@@Content"}, {"id": "reactflow__edge-2bfc1c23-9770-47ab-ab95-ed51a63101022bfc1c23-9770-47ab-ab95-ed51a6310102@@OutPut-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@Input", "source": "2bfc1c23-9770-47ab-ab95-ed51a6310102", "source_param": "2bfc1c23-9770-47ab-ab95-ed51a6310102@@OutPut", "target": "cd4b5759-107f-46de-826e-ef516e294ca7", "target_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@Input"}, {"id": "reactflow__edge-96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-ecdac9c0-9741-463f-a6b8-0eda7cbe5628ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@Question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "target_param": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@Question"}, {"id": "reactflow__edge-ecdac9c0-9741-463f-a6b8-0eda7cbe5628ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@OutPut-d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@answer", "source": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628", "source_param": "ecdac9c0-9741-463f-a6b8-0eda7cbe5628@@OutPut", "target": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319", "target_param": "d6a5ebe1-b0a8-45ae-bb7d-70a539d8d319@@answer"}, {"id": "reactflow__edge-b9fd414e-1f6d-4186-b471-7c21a6b2046cb9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut-832f5acc-5628-457e-ad6b-08088a9acef3832f5acc-5628-457e-ad6b-08088a9acef3@@Question", "source": "b9fd414e-1f6d-4186-b471-7c21a6b2046c", "source_param": "b9fd414e-1f6d-4186-b471-7c21a6b2046c@@OutPut", "target": "832f5acc-5628-457e-ad6b-08088a9acef3", "target_param": "832f5acc-5628-457e-ad6b-08088a9acef3@@Question"}, {"id": "xy-edge__96fd82ba-c1c5-409a-96d8-cb7957629d2296fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Question", "source": "96fd82ba-c1c5-409a-96d8-cb7957629d22", "source_param": "96fd82ba-c1c5-409a-96d8-cb7957629d22@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Question"}, {"id": "xy-edge__5d9f1042-70e4-4873-9ab7-df71ef529cc55d9f1042-70e4-4873-9ab7-df71ef529cc5@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@History", "source": "5d9f1042-70e4-4873-9ab7-df71ef529cc5", "source_param": "5d9f1042-70e4-4873-9ab7-df71ef529cc5@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@History"}, {"id": "xy-edge__d078071f-a43c-4679-9c24-638ab0daab2fd078071f-a43c-4679-9c24-638ab0daab2f@@Output-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Internet", "source": "d078071f-a43c-4679-9c24-638ab0daab2f", "source_param": "d078071f-a43c-4679-9c24-638ab0daab2f@@Output", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Internet"}, {"id": "xy-edge__832f5acc-5628-457e-ad6b-08088a9acef3832f5acc-5628-457e-ad6b-08088a9acef3@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Knowledge", "source": "832f5acc-5628-457e-ad6b-08088a9acef3", "source_param": "832f5acc-5628-457e-ad6b-08088a9acef3@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@Knowledge"}, {"id": "xy-edge__43fa9fee-4688-4adc-83a9-48deac514c3243fa9fee-4688-4adc-83a9-48deac514c32@@OutPut-efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@File", "source": "43fa9fee-4688-4adc-83a9-48deac514c32", "source_param": "43fa9fee-4688-4adc-83a9-48deac514c32@@OutPut", "target": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "target_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@File"}, {"id": "xy-edge__efa14ce1-5184-4db9-9735-bbc6bc3b2edfefa14ce1-5184-4db9-9735-bbc6bc3b2edf@@OutPut-cd4b5759-107f-46de-826e-ef516e294ca7cd4b5759-107f-46de-826e-ef516e294ca7@@Input", "source": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf", "source_param": "efa14ce1-5184-4db9-9735-bbc6bc3b2edf@@OutPut", "target": "cd4b5759-107f-46de-826e-ef516e294ca7", "target_param": "cd4b5759-107f-46de-826e-ef516e294ca7@@Input"}], "viewport": {"x": 888.684544997741, "y": -411.86923228212083, "zoom": 0.40951834892991396}}, "created_time": 0, "updated_time": 0}