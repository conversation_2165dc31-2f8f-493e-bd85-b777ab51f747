{"id": "图片理解.json", "name": "Image Understanding", "desc": "Multimodal, image understanding.", "template_group_key": "MultiModality", "template": {"nodes": [{"id": "229667ff-9d67-4bb7-9ea8-05c335c990bd", "name": "File Upload", "widget_id": "WidgetKeyFileInput", "widget_detail": {"id": "WidgetKeyFileInput", "name": "File Upload", "desc": "Used for loading a single uploaded file, outputting data of SFSFile type.", "group": "WidgetGroupInput", "oriWidgetKey": "WidgetKeyFileInput", "params": [{"data_class": "file", "category": "req-input", "preview": false, "define": {"id": "FileInput", "name": "Upload File", "desc": "Upload file, supported types: Sync-SFSFiles: [\n  {\n    \"name\": \"name\",\n    \"uid\": \"uid\",\n    \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n    \"content\": \"Y29udGVudA==\"\n  }\n]", "datasource": "txt", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFiles"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "IsFileContentRead", "name": "Read file content", "desc": "Whether to read the file content, if enabled, it will read the file content and pass the file byte stream to the downstream node; otherwise, it will only pass the file path.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "MaxFileSizeMB", "name": "Maximum file size (MB)", "desc": "The maximum file size allowed for upload, in MB.", "number_range": {"min": 1, "max": 200}, "default_value": "20", "required": true, "type": "TYPE_NUMBER", "data_type": "DATA_TYPE_INT"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "AllowedExtensions", "name": "Allowed file extensions", "desc": "List of allowed file extensions, only one extension per line is permitted, you can enter *, txt, pdf, docx, etc. Entering * or leaving it blank means all file formats are allowed.", "default_value": "[\"*\"]", "required": true, "multiple": true, "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Support Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}]}, "ui": "{\"dragging\":false,\"height\":401,\"id\":\"229667ff-9d67-4bb7-9ea8-05c335c990bd\",\"position\":{\"x\":171.76939878283133,\"y\":29.880874141244277},\"positionAbsolute\":{\"x\":171.76939878283133,\"y\":29.880874141244277},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"AllowedExtensions": ["*"], "IsFileContentRead": true, "MaxFileSizeMB": 20}, "sub_chain_base_info": null}, {"id": "8ac4c77a-d20f-4a3a-976f-543d28cce1aa", "name": "Image Understanding", "widget_id": "WidgetKeyImageAnalysis", "widget_detail": {"id": "WidgetKeyImageAnalysis", "name": "Image Understanding", "desc": "Analyze and understand the uploaded image", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyImageAnalysis", "params": [{"data_class": "file", "category": "in-port", "preview": false, "define": {"id": "File", "name": "picture", "desc": "Support Type: Sync-SFSFile: {\n  \"name\": \"name\",\n  \"uid\": \"uid\",\n  \"url\": \"sfs:///tenants/llmops-assets/projs/assets/a.text\",\n  \"content\": \"Y29udGVudA==\"\n}", "required": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-SFSFile"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "UseBase64", "name": "Base64 encoding", "desc": "Whether to use base64 to encode the image before calling the model, it needs to be enabled for remote and cross-space calls.", "default_value": "false", "required": true, "type": "TYPE_SWITCH", "data_type": "DATA_TYPE_BOOLEAN"}, "param_limits": null}, {"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Question", "name": "Text", "desc": "Text description, assist in understanding and analyzing image content, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "Service", "desc": "Image Understanding Service", "datasource": "{\"kind\":\"<PERSON>ODEL_KIND_MULTI\",\"subKind\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Image understanding text content, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":311,\"id\":\"8ac4c77a-d20f-4a3a-976f-543d28cce1aa\",\"position\":{\"x\":605.2537302408755,\"y\":232.7797748489723},\"positionAbsolute\":{\"x\":605.2537302408755,\"y\":232.7797748489723},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"27fea943-c983-4343-b707-371797f3a115\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"model_name\":\"Demo图像理解\",\"release_name\":\"qwen2-vl-7b-instruct\",\"release_version\":\"v2\",\"model_id\":\"MWH-MODEL-cqhmfdvua89ga518qo9g\",\"release_id\":\"MWH-MODEL-RELEASE-crp9omno3t2pchkmkou0\",\"inference_params\":[],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-27fea943-c983-4343-b707-371797f3a115\",\"name\":\"Qwen2.5-vl-7B-Instruct\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-27fea943-c983-4343-b707-371797f3a115/8011/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Demo图像理解\",\"name_for_human\":\"图像理解模型:Demo图像理解\",\"desc\":\"可以结合图像与提问回答问题\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"file\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"图像文件的位置,需要遵循文件系统规范，以sfs://开头，例：sfs:///image2text.jpg\",\"required\":false},{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1727180120000\",\"reference_model\":{\"id\":\"MWH-MODEL-cqhmfdvua89ga518qo9g\",\"name\":\"Demo图像理解\",\"domain\":{\"kind\":\"MODEL_KIND_MULTI\",\"sub_kind\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"图像理解\",\"user_id\":\"thinger\",\"thumbnail\":\"sfs:///avatar/067fc652-5c2e-44b0-9b27-a567d7362db2_屏幕截图 2024-08-01 164213.png\",\"is_public\":false,\"create_time_ms\":\"1721984951734\",\"update_time_ms\":\"1722501809356\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":1,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-cqhmfrfua89ga518qoa0\",\"name\":\"cogvlm2\",\"version\":\"v1\",\"repo\":\"sfs:///mwh/MWH-MODEL-cqhmfdvua89ga518qo9g/releases/MWH-MODEL-RELEASE-cqhmfrfua89ga518qoa0/model-files\",\"model_id\":\"MWH-MODEL-cqhmfdvua89ga518qo9g\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"116490\",\"create_time_ms\":\"1721985005758\",\"update_time_ms\":\"1721985005808\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":null,\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"fp16\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GENERATION_FOLDER\",\"name\":\"GENERATION_FOLDER\",\"desc\":\"产生的文件存储路径\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"/\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"FILE_SYSTEM_SCHEMA\",\"name\":\"FILE_SYSTEM_SCHEMA\",\"desc\":\"文件系统协议\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"sfs://\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NEED_OPEN_IMAGE_FILE\",\"name\":\"NEED_OPEN_IMAGE_FILE\",\"desc\":\"是否需要打开文件夹\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"thinger\",\"training_template\":null}],\"usage_count\":{\"deploys_count\":0,\"views_count\":345,\"downloads_count\":3,\"invokes_count\":0,\"trainings_count\":0,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-cqhmfdvua89ga518qo9g\"},\"disk_usage\":55610230000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-crp9omno3t2pchkmkou0\",\"name\":\"qwen2-vl-7b-instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-cqhmfdvua89ga518qo9g/releases/MWH-MODEL-RELEASE-crp9omno3t2pchkmkou0/model-files\",\"model_id\":\"MWH-MODEL-cqhmfdvua89ga518qo9g\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"16594437839\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1731662165412\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"27fea943-c983-4343-b707-371797f3a115\",\"deployment_status\":\"running\",\"deployment_health\":\"healthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"fp16\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GENERATION_FOLDER\",\"name\":\"GENERATION_FOLDER\",\"desc\":\"产生的文件存储路径\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"/\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"FILE_SYSTEM_SCHEMA\",\"name\":\"FILE_SYSTEM_SCHEMA\",\"desc\":\"文件系统协议\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"sfs://\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NEED_OPEN_IMAGE_FILE\",\"name\":\"NEED_OPEN_IMAGE_FILE\",\"desc\":\"是否需要打开文件夹\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"启动vllm进行模型加速\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"\",\"type\":\"TYPE_UNSPECIFIED\",\"data_type\":\"DATA_TYPE_UNSPECIFIED\",\"number_range\":null,\"default_value\":\"0.8\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NONE\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":null},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1730981336000\",\"guardrails_config\":{\"is_security\":false,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"Demo图像理解\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v2\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"qwen2-vl-7b-instruct\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_MULTI\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_MULTI\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Qwen2.5-vl-7B-Instruct\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#029be6\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"27fea943-c983-4343-b707-371797f3a115\"}", "UseBase64": true}, "sub_chain_base_info": null}, {"id": "9b8f59af-aa6a-4c55-a3ae-42035653e3c4", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"9b8f59af-aa6a-4c55-a3ae-42035653e3c4\",\"position\":{\"x\":162.5127453615961,\"y\":554.773218397539},\"positionAbsolute\":{\"x\":162.5127453615961,\"y\":554.773218397539},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-229667ff-9d67-4bb7-9ea8-05c335c990bd229667ff-9d67-4bb7-9ea8-05c335c990bd@@OutPut-8ac4c77a-d20f-4a3a-976f-543d28cce1aa8ac4c77a-d20f-4a3a-976f-543d28cce1aa@@File", "source": "229667ff-9d67-4bb7-9ea8-05c335c990bd", "source_param": "229667ff-9d67-4bb7-9ea8-05c335c990bd@@OutPut", "target": "8ac4c77a-d20f-4a3a-976f-543d28cce1aa", "target_param": "8ac4c77a-d20f-4a3a-976f-543d28cce1aa@@File"}, {"id": "reactflow__edge-9b8f59af-aa6a-4c55-a3ae-42035653e3c49b8f59af-aa6a-4c55-a3ae-42035653e3c4@@OutPut-8ac4c77a-d20f-4a3a-976f-543d28cce1aa8ac4c77a-d20f-4a3a-976f-543d28cce1aa@@Question", "source": "9b8f59af-aa6a-4c55-a3ae-42035653e3c4", "source_param": "9b8f59af-aa6a-4c55-a3ae-42035653e3c4@@OutPut", "target": "8ac4c77a-d20f-4a3a-976f-543d28cce1aa", "target_param": "8ac4c77a-d20f-4a3a-976f-543d28cce1aa@@Question"}], "viewport": {"x": 67.98301937067185, "y": 29.846412482016547, "zoom": 0.6708211124411306}}, "created_time": 0, "updated_time": 0}