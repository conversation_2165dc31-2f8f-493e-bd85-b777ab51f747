{"id": "数据库知识问答plus.json", "name": "Database Knowledge Q&A Plus", "desc": "Enter the MySQL database connection URL, obtain the database table metadata, and directly answer the user's question or execute SQL to retrieve data to answer the user's question.", "template_group_key": "DataBaseQA", "template": {"nodes": [{"id": "728ce06e-b58e-4492-b2a3-ccc320ac8a3a", "name": "Execute SQL", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python code", "desc": "Can write Python code to complete complex business logic, with pre-installed dependencies including requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "python code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"728ce06e-b58e-4492-b2a3-ccc320ac8a3a\",\"position\":{\"x\":1269.0496415198206,\"y\":723.0702499127719},\"positionAbsolute\":{\"x\":1269.0496415198206,\"y\":723.0702499127719},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1002}", "values": {"Code": "\n# python解释器版本 3.11\nimport pymysql\nfrom urllib.parse import urlparse, parse_qs\nimport pandas as pd\n\ndef handler(data):\n    url = data.get(\"url\")\n    url = url.replace(\"#\", \"%23\")\n    sql = data.get(\"sql\")\n    # 解析URL\n    parsed_url = urlparse(url)\n\n    # 获取主机名和端口\n    host = parsed_url.hostname\n    port = parsed_url.port\n\n    # 获取数据库名\n    db = parsed_url.path[1:]  # 移除路径前的'/'\n\n    # 解析查询字符串中的参数\n    query_params = parse_qs(parsed_url.query)\n\n    # 获取用户和密码\n    user = query_params.get('user', [None])[0]\n    password = query_params.get('password', [None])[0]\n    print(host, port, user, password, db)\n\n\n    # 创建连接\n    connection = pymysql.connect(host=host, port=port, user=user, password=password, database=db)\n    result = {}\n    try:\n        df = pd.read_sql(sql, connection)\n        result = df.to_dict(orient='records')\n    finally:\n        # 关闭数据库连接\n        connection.close()\n    return result\n"}, "sub_chain_base_info": null}, {"id": "ef230e26-27a8-4e64-8869-0fb616cf97c2", "name": "Extract SQL", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python code", "desc": "Can write Python code to complete complex business logic, with pre-installed dependencies including requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "python code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"ef230e26-27a8-4e64-8869-0fb616cf97c2\",\"position\":{\"x\":2062.8631985232473,\"y\":72.78383409857423},\"positionAbsolute\":{\"x\":2062.8631985232473,\"y\":72.78383409857423},\"selected\":false,\"type\":\"custom\",\"width\":320,\"zIndex\":1001}", "values": {"Code": "import json\nimport re\n\ndef handler(data):\n    # 使用正则表达式匹配可能的JSON字符串\n    match = re.search(r'.*(\\{.*?\\}).*', data, re.DOTALL)\n    if match:\n        # 如果找到匹配，提取JSON字符串\n        json_str = match.group(1)\n\n    return json.loads(json_str).get('sql')\n"}, "sub_chain_base_info": null}, {"id": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da", "name": "Obtain the database schema", "widget_id": "WidgetKeyPythonWidget", "widget_detail": {"id": "WidgetKeyPythonWidget", "name": "Python code", "desc": "Can write Python code to complete complex business logic, with pre-installed dependencies including requests, pandas, and matplotlib.", "group": "WidgetGroupCodeTool", "oriWidgetKey": "WidgetKeyPythonWidget", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "Content", "name": "Input data", "desc": "Data that needs to be processed using Python code, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "code", "category": "attribute", "preview": false, "define": {"id": "Code", "name": "python code", "desc": "python code, click to view or edit the code", "default_value": "\n# python解释器版本 3.11\n\ndef handler(data):\n    \"\"\"\n    确保需要执行的函数名为handler，且函数只包含1个参数，该函数接收上游算子的输出数据，并返回处理后的数据\n   \n    params:\n    data (any): 参数名任意，任意类型\n\n    return:\n    any: 返回任意类型\n\n    \"\"\"\n    # TODO 数据处理\n    # xxx\n    result = data\n\n    return result\n\n", "required": true, "type": "TYPE_CODE_PYTHON", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "python code's handler function returns data, supported types: Any-Any: \"any data type\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}]}, "ui": "{\"dragging\":false,\"height\":180,\"id\":\"155bf196-e6c7-44b4-8f6f-6ba7cdf863da\",\"position\":{\"x\":790.8190278204522,\"y\":-137.21915626858254},\"positionAbsolute\":{\"x\":790.8190278204522,\"y\":-137.21915626858254},\"selected\":true,\"type\":\"custom\",\"width\":320}", "values": {"Code": "\n# python解释器版本 3.11\nimport pymysql\nfrom urllib.parse import urlparse, parse_qs\n\ndef handler(url):\n    url = url.replace(\"#\", \"%23\")\n    # 解析URL\n    parsed_url = urlparse(url)\n\n    # 获取主机名和端口\n    host = parsed_url.hostname\n    port = parsed_url.port\n\n    # 获取数据库名\n    db = parsed_url.path[1:]  # 移除路径前的'/'\n\n    # 解析查询字符串中的参数\n    query_params = parse_qs(parsed_url.query)\n\n    # 获取用户和密码\n    user = query_params.get('user', [None])[0]\n    password = query_params.get('password', [None])[0]\n    print(host, port, user, password, db)\n\n    # host = '**************'\n    # port = 32037\n    # user = 'user'\n    # password = 'password'\n    # db = 'db_name'\n\n    # 创建连接\n    connection = pymysql.connect(host=host, port=port, user=user, password=password, database=db)\n\n    try:\n        schemas = {}\n        # 创建 cursor 对象\n        with connection.cursor() as cursor:\n            # 执行 SQL 查询\n            cursor.execute(\"SHOW TABLES;\")\n            # 获取所有表名\n            tables = cursor.fetchall()\n            for table in tables:\n                table_name = table[0]\n                cursor.execute(\"SHOW CREATE TABLE \" + table_name + \";\")\n                schema = cursor.fetchall()\n                schemas[table_name] = schema[0][1]\n    finally:\n        # 关闭数据库连接\n        connection.close()\n    return schemas\n\n# if __name__ == '__main__':\n#     print(handler(\"mysql://**************:32037/db_name?user=user&password=password\"))"}, "sub_chain_base_info": null}, {"id": "b530985f-3a80-4927-835d-38ea576803b9", "name": "chat-bi-generated-sql", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "General Text Template", "desc": "Concatenate variables into a piece of text, allowing the text template to not reference any variables or reference multiple variables.", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": true, "define": {"id": "Template", "name": "Template", "default_value": "你需要根据MySQL数据库表结构以及用户问题生成正确可执行的sql语句。\n数据库表格结构：\n```\n{{.db_schema}}\n```\n用户问题:\n```\n{{.question}}\n```\n输出要求:\n- 请以json形式输出你的答案，格式为: {\"sql\": \"valid and correct sql;\"}，只生成一个sql语句\n- 只输出json，不要输出任何其他额外信息", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "db_schema", "name": "db_schema", "desc": "db_schema", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":252,\"id\":\"b530985f-3a80-4927-835d-38ea576803b9\",\"position\":{\"x\":1278.3554091339067,\"y\":-196.17664791312905},\"positionAbsolute\":{\"x\":1278.3554091339067,\"y\":-196.17664791312905},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "你需要根据MySQL数据库表结构以及用户问题生成正确可执行的sql语句。\n数据库表格结构：\n```\n{{.db_schema}}\n```\n用户问题:\n```\n{{.question}}\n```\n输出要求:\n- 请以json形式输出你的答案，格式为: {\"sql\": \"valid and correct sql;\"}，只生成一个sql语句\n- 只输出json，不要输出任何其他额外信息"}, "sub_chain_base_info": null}, {"id": "3c3f632a-bc20-444b-a3db-59654c564a98", "name": "Text Input", "widget_id": "WidgetKeyTextInput", "widget_detail": {"id": "WidgetKeyTextInput", "name": "Text Input", "desc": "Used for outputting the input text verbatim", "group": "WidgetGroupInput", "params": [{"data_class": "string", "category": "req-input", "preview": false, "define": {"id": "TextInput", "name": "Text Input", "desc": "Text input, supported type: Sync-String: \"text\".", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}]}, "ui": "{\"dragging\":false,\"height\":133,\"id\":\"3c3f632a-bc20-444b-a3db-59654c564a98\",\"position\":{\"x\":189.71923207051873,\"y\":601.5533514574549},\"positionAbsolute\":{\"x\":189.71923207051873,\"y\":601.5533514574549},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b", "name": "Data Integration", "widget_id": "WidgetKeyInputAggregation", "widget_detail": {"id": "WidgetKeyInputAggregation", "name": "Data Integration", "desc": "Compose multiple upstream inputs into a JSON output in KV format (output is triggered only when all data is available)", "group": "WidgetGroupControlFlow", "oriWidgetKey": "WidgetKeyInputAggregation", "params": [{"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "sql", "name": "SQL", "desc": "SQL", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "url", "name": "url", "desc": "url", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":176,\"id\":\"38f2af8f-8b39-4e44-9f61-1f0dbff2176b\",\"position\":{\"x\":831.0947992823974,\"y\":623.1397528572629},\"positionAbsolute\":{\"x\":831.0947992823974,\"y\":623.1397528572629},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {}, "sub_chain_base_info": null}, {"id": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa", "name": "chat-bi-report generation", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "General Text Template", "desc": "Concatenate variables into a piece of text, allowing the text template to not reference any variables or reference multiple variables.", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": true, "define": {"id": "Template", "name": "Template", "default_value": "## 数据库表schema数据\n```\n{{.schema}}\n```\n## 从数据库中获取到的数据\n```\n{{.data}}\n```\n## 用户问题\n{{.question}}\n\n", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "schema", "name": "schema", "desc": "schema", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "data", "name": "data", "desc": "data", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "in-port", "preview": false, "define": {"id": "question", "name": "question", "desc": "question", "type": "TYPE_INPUT", "data_type": "DATA_TYPE_STRING"}, "param_limits": {"types": ["Any-Any"]}}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":296,\"id\":\"ca94fb7d-867c-4f6c-b8f1-1824ba67affa\",\"position\":{\"x\":1716.6251597699934,\"y\":612.9069827888939},\"positionAbsolute\":{\"x\":1716.6251597699934,\"y\":612.9069827888939},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "## 数据库表schema数据\n```\n{{.schema}}\n```\n## 从数据库中获取到的数据\n```\n{{.data}}\n```\n## 用户问题\n{{.question}}\n\n"}, "sub_chain_base_info": null}, {"id": "a638ca06-b476-4d85-b4e3-8791bccdade4", "name": "Text generation model", "widget_id": "WidgetKeyLLMModel", "widget_detail": {"id": "WidgetKeyLLMModel", "name": "Text Generation", "desc": "Used for invoking text generation model services", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyLLMModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Input prompt words for LLM, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "System Prompt", "desc": "System prompt words for the model, used to define AI behavior and response methods, to improve the accuracy of responses.", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "Dialogue History", "desc": "Dialogue History", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "LLM output text, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":287,\"id\":\"a638ca06-b476-4d85-b4e3-8791bccdade4\",\"position\":{\"x\":1617.468019975549,\"y\":-9.781784492116174},\"positionAbsolute\":{\"x\":1617.468019975549,\"y\":-9.781784492116174},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"Qwen2.5\",\"release_name\":\"Qwen2.5-Coder-7B-Instruct\",\"release_version\":\"v2\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"release_id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"name\":\"Golden-Qwen2.5-Coder-7B-Instruct\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-2c1ac526-8da5-48cb-bfe9-8eace385ab30/8011/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5\",\"name_for_human\":\"对话模型:Qwen2.5\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1731579161000\",\"reference_model\":{\"id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"name\":\"Qwen2.5\",\"domain\":{\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"qwen2.5对话模型\",\"user_id\":\"fangyuan.han\",\"thumbnail\":\"sfs://tenants/llmops-assets/projs/assets/avatar/0b56fcc9-ebbc-4cad-90ad-9c0459ad0fdb_w.jpg\",\"is_public\":false,\"create_time_ms\":\"1726800941008\",\"update_time_ms\":\"1731049523481\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":2,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g\",\"name\":\"Qwen2.5-7B-Instruct\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966687\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727177464182\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"12195919-edf2-4684-94f6-d01292a6e903\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.8\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"71e01c35-480d-436b-8dcb-30bc06555de4\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}}],\"usage_count\":{\"deploys_count\":0,\"views_count\":155,\"downloads_count\":2,\"invokes_count\":0,\"trainings_count\":1,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\"},\"disk_usage\":30485934000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"563be5ac-4460-479d-a991-eca6460f206a\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1732188130000\",\"guardrails_config\":{\"is_security\":true,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"Qwen2.5\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v2\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"Qwen2.5-Coder-7B-Instruct\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Golden-Qwen2.5-Coder-7B-Instruct\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\"}", "SystemPrompt": "You are a helpful assistant."}, "sub_chain_base_info": null}, {"id": "551dfe9a-5691-4d31-b6cb-70d3c37de7f3", "name": "Text generation model", "widget_id": "WidgetKeyLLMModel", "widget_detail": {"id": "WidgetKeyLLMModel", "name": "Text Generation", "desc": "Used for invoking text generation model services", "group": "WidgetGroupAIModel", "oriWidgetKey": "WidgetKeyLLMModel", "params": [{"data_class": "json", "category": "in-port", "preview": false, "define": {"id": "Text", "name": "Text", "desc": "Input prompt words for LLM, supported type: Sync-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Sync-String"]}}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "SystemPrompt", "name": "System Prompt", "desc": "System prompt words for the model, used to define AI behavior and response methods, to improve the accuracy of responses.", "default_value": "You are a helpful assistant.", "comp_props": "{\"autoSize\":{\"minRows\":4,\"maxRows\":10}}", "type": "TYPE_TEXTAREA", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "ModelServer", "name": "LLM model", "desc": "Available LLM Model Services", "datasource": "{\"kind\":\"MODEL_KIND_NLP\",\"subKind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"onlyAvailable\":true,\"showPublic\":true,\"showRemote\":true,\"includeMWService\":true,\"includeSeldonService\":true,\"omitCensored\":false}", "required": true, "type": "TYPE_AGENT_MODEL_API", "data_type": "DATA_TYPE_STRING"}, "param_limits": null}, {"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "DialogHistory", "name": "Dialogue History", "desc": "Dialogue History", "hidden": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "LLM output text, supported type: Any-String: \"text\".", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-String"]}}]}, "ui": "{\"dragging\":false,\"height\":287,\"id\":\"551dfe9a-5691-4d31-b6cb-70d3c37de7f3\",\"position\":{\"x\":2155.2492133879227,\"y\":602.4595819433381},\"positionAbsolute\":{\"x\":2155.2492133879227,\"y\":602.4595819433381},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"ModelServer": "{\"id\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"schema\":\"MODEL_SERVICE_SCHEMA_SELDON\",\"host\":\"istio-ingressgateway.istio-system\",\"port\":8001,\"type\":\"MODEL_SERVICE_TYPE_LOCAL\",\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"status\":{\"state\":\"running\",\"message\":\"\",\"timestamp\":\"0\",\"health\":\"healthy\",\"health_msg\":\"\",\"nodes\":[],\"service_status\":null,\"ref_id\":\"\",\"ref_type\":\"\",\"replica_id\":\"\",\"edge_id\":\"\",\"indicators\":{}},\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"model_name\":\"Qwen2.5\",\"release_name\":\"Qwen2.5-Coder-7B-Instruct\",\"release_version\":\"v2\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"release_id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"prompt\":\"\",\"namespace\":\"llmops-assets\",\"seldon_deploy_name\":\"service-2c1ac526-8da5-48cb-bfe9-8eace385ab30\",\"name\":\"Golden-Qwen2.5-Coder-7B-Instruct\",\"full_url\":\"http://istio-ingressgateway.istio-system/seldon/llmops-assets/service-2c1ac526-8da5-48cb-bfe9-8eace385ab30/8011/openai/v1/chat/completions?project_id=assets\",\"invoke_as_tool\":{\"name_for_model\":\"Qwen2.5\",\"name_for_human\":\"对话模型:Qwen2.5\",\"desc\":\"可以进行专业的对话\",\"invoke_method\":\"MODEL_SERVICE_INVOKE_METHOD_STREAM\",\"invoke_params\":[{\"name\":\"query\",\"default_value\":\"\",\"type\":\"string\",\"desc\":\"提问的内容\",\"required\":false}]},\"remote_service_config\":null,\"desc\":\"\",\"create_time_ms\":\"1731579161000\",\"reference_model\":{\"id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"name\":\"Qwen2.5\",\"domain\":{\"kind\":\"MODEL_KIND_NLP\",\"sub_kind\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"type\":\"MODEL_TYPE_FILE\",\"subtype\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"schedule_mode\":\"MODEL_SCHEDULE_MODE_STATELESS\",\"output_kind\":\"MODEL_KIND_UNSPECIFIED\",\"output_sub_kind\":\"MODEL_SUB_KIND_UNSPECIFIED\",\"algorithm\":\"MO\",\"components\":[]},\"detail\":{\"desc\":\"qwen2.5对话模型\",\"user_id\":\"fangyuan.han\",\"thumbnail\":\"sfs://tenants/llmops-assets/projs/assets/avatar/0b56fcc9-ebbc-4cad-90ad-9c0459ad0fdb_w.jpg\",\"is_public\":false,\"create_time_ms\":\"1726800941008\",\"update_time_ms\":\"1731049523481\",\"labels\":{},\"baselines\":{},\"relations\":[]},\"stats\":{\"latest_release\":null,\"baseline_release\":null,\"release_count\":2,\"releases_info\":[{\"id\":\"MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g\",\"name\":\"Qwen2.5-7B-Instruct\",\"version\":\"v1\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crme8lnua89mgknmmr7g/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966687\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727177464182\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"12195919-edf2-4684-94f6-d01292a6e903\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.8\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"71e01c35-480d-436b-8dcb-30bc06555de4\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}}],\"usage_count\":{\"deploys_count\":0,\"views_count\":155,\"downloads_count\":2,\"invokes_count\":0,\"trainings_count\":1,\"evaluations_count\":0,\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\"},\"disk_usage\":30485934000},\"apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"attachments\":[],\"training_template\":\"TRAINING_TEMPLATE_UNSPECIFIED\",\"project_id\":\"assets\",\"asset_type\":\"ASSET_SHARED\",\"source_project_id\":\"\"},\"reference_release\":{\"release_base\":{\"id\":\"MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0\",\"name\":\"Qwen2.5-Coder-7B-Instruct\",\"version\":\"v2\",\"repo\":\"sfs:///tenants/llmops-assets/projs/assets/mwh/MWH-MODEL-crme8bfua89mgknmmr70/releases/MWH-MODEL-RELEASE-crpv6ufua89tb1ivo8c0/model-files\",\"model_id\":\"MWH-MODEL-crme8bfua89mgknmmr70\",\"is_baseline\":false,\"detail\":{\"desc\":\"\",\"model_size\":\"15242966520\",\"create_time_ms\":\"0\",\"update_time_ms\":\"1727265338138\",\"labels\":{},\"relations\":[],\"attachments\":[],\"computation_attributes\":{}},\"stats\":{\"deployment_id\":\"563be5ac-4460-479d-a991-eca6460f206a\",\"deployment_status\":\"stopped\",\"deployment_health\":\"unhealthy\",\"model_name\":\"\",\"release_status\":\"MODEL_RELEASE_STATUS_UNEVALUATED\"},\"hardware_range\":{\"arches\":[\"CPU_ARCH_MULTI\"],\"acc_types\":[]},\"default_config\":{\"runtime\":\"MODEL_RUNTIME_TYPE_UNSPECIFIED\",\"resource\":null,\"values\":{},\"deployment_params\":[{\"id\":\"USE_VLLM\",\"name\":\"USE_VLLM\",\"desc\":\"是否使用vllm框架\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"GPU_MEMORY_UTILIZATION\",\"name\":\"GPU_MEMORY_UTILIZATION\",\"desc\":\"限制gpu使用率\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0.5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"ENFORCE_EAGER\",\"name\":\"ENFORCE_EAGER\",\"desc\":\"vllm加载模式控制\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"TORCH_DTYPE\",\"name\":\"TORCH_DTYPE\",\"desc\":\"torch类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MODEL_TYPE\",\"name\":\"MODEL_TYPE\",\"desc\":\"model类型\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"auto\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"USE_NTK\",\"name\":\"USE_NTK\",\"desc\":\"使用使用ntk\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"NO_PROMPT\",\"name\":\"NO_PROMPT\",\"desc\":\"禁用prompt\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"STOP_WORDS\",\"name\":\"STOP_WORDS\",\"desc\":\"停止词\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"CHAT_MODE\",\"name\":\"CHAT_MODE\",\"desc\":\"是否使用对话模式\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"chat\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"MAX_INTERACTIVE_TIMES\",\"name\":\"MAX_INTERACTIVE_TIMES\",\"desc\":\"最大交互时间\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"5\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"OBSERVATION_TRUNCATE_LENGTH\",\"name\":\"OBSERVATION_TRUNCATE_LENGTH\",\"desc\":\"观测截断长度\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1024\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"SHOW_OBSERVATION\",\"name\":\"SHOW_OBSERVATION\",\"desc\":\"展示观测\",\"type\":\"TYPE_INPUT\",\"data_type\":\"DATA_TYPE_STRING\",\"number_range\":null,\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":false,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"}],\"inference_params\":[{\"id\":\"temperature\",\"name\":\"temperature\",\"desc\":\"用于调制下一个token概率的值\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":2,\"step\":0.1},\"default_value\":\"0.7\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_k\",\"name\":\"top_k\",\"desc\":\"为top-k过滤保留的最高概率词汇表token的数量\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":1,\"max\":100,\"step\":0},\"default_value\":\"50\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":0,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"top_p\",\"name\":\"top_p\",\"desc\":\" 仅保留概率加起来为top_p或更高的最可能token的最小集合来生成token\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_INT\",\"number_range\":{\"min\":0,\"max\":1,\"step\":0},\"default_value\":\"1\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"repetition_penalty\",\"name\":\"repetition_penalty\",\"desc\":\"重复惩罚的参数,减少重复输出,1.0表示没有惩罚,值越大惩罚越重\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":-2,\"max\":2,\"step\":0.1},\"default_value\":\"1.0\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"},{\"id\":\"max_tokens\",\"name\":\"max_tokens\",\"desc\":\"最大输出长度\",\"type\":\"TYPE_NUMBER\",\"data_type\":\"DATA_TYPE_FLOAT\",\"number_range\":{\"min\":0,\"max\":9999,\"step\":0.1},\"default_value\":\"2048\",\"datasource\":\"\",\"precondition\":\"\",\"required\":true,\"multiple\":false,\"maxlen\":\"0\",\"placeholder\":\"\",\"advanced\":false,\"multiline\":false,\"hidden\":false,\"precision\":1,\"disabled\":false,\"comp_props\":\"\"}],\"default_prompt\":\"\",\"use_default_params\":false},\"model_apis\":[{\"path\":\"/atom\",\"inputs\":[{\"name\":\"JSON\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ\",\"dims\":[1],\"optional\":false,\"default_value\":\"%%json%%\"}],\"outputs\":[{\"name\":\"RESULT\",\"data_type\":\"DATA_TYPE_BYTES\",\"format\":\"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP\",\"dims\":[1],\"optional\":false,\"default_value\":\"{}\"}]}],\"project_id\":\"assets\",\"creator\":\"fangyuan.han\",\"training_template\":{\"name\":\"default\",\"content\":\"name=\\\"default\\\",\\nprefix=[\\n\\t\\\"{{system}}\\\"\\n],\\nprompt=[\\n\\t\\\"Human: {{query}}\\\\nAssistant: \\\"\\n],\\nsystem=(\\n\\t\\\"A chat between a curious user and an artificial intelligence assistant. \\\"\\n\\t\\\"The assistant gives helpful, detailed, and polite answers to the user's questions.\\\"\\n),\\nsep=[\\n\\t\\\"\\\\n\\\"\\n]\\n\"}},\"model_meta\":{\"model_type\":\"MODEL_TYPE_FILE\",\"file_model_meta\":{\"raw\":\"\",\"encrypt\":false,\"training_data_distributions\":{}},\"image_model_meta\":null,\"ensemble_model_meta\":null}},\"project_id\":\"assets\",\"reference_remote_service\":null,\"update_time_ms\":\"1732188130000\",\"guardrails_config\":{\"is_security\":true,\"guardrails_id\":\"\"},\"label\":{\"type\":{},\"key\":null,\"ref\":null,\"props\":{\"title\":null,\"placement\":\"right\",\"zIndex\":999999,\"content\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:模型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"size\":\"small\",\"type\":\"link\",\"children\":\"Qwen2.5\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"children\":[{\"key\":\"5453883370739390:版本\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本\"},\"_owner\":null},\":\",{\"type\":{\"__ANT_BUTTON\":true},\"key\":null,\"ref\":null,\"props\":{\"type\":\"link\",\"size\":\"small\",\"children\":\"v2\"},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:版本别名\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"版本别名\"},\"_owner\":null},\":\",\"Qwen2.5-Coder-7B-Instruct\"]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:模型框架\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"模型框架\"},\"_owner\":null},\":\",{\"key\":\"5453883370739390:MODEL_SUB_TYPE_FILE_TRANSFORMER\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_TYPE_FILE_TRANSFORMER\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"mb-1\",\"children\":[{\"key\":\"5453883370739390:任务类型\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"任务类型\"},\"_owner\":null},\": \",{\"key\":\"5453883370739390:MODEL_KIND_NLP\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_KIND_NLP\"},\"_owner\":null},\"/\",{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null},\"children\":{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex\",\"children\":[{\"type\":\"div\",\"key\":null,\"ref\":null,\"props\":{\"className\":\"flex-1\",\"children\":\"Golden-Qwen2.5-Coder-7B-Instruct\"},\"_owner\":null},{\"key\":null,\"ref\":null,\"props\":{\"children\":[null,{\"key\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"className\":\"ml-2\",\"color\":\"#d546a3\",\"text\":{\"key\":null,\"ref\":null,\"props\":{\"children\":{\"key\":\"5453883370739390:MODEL_SUB_KIND_NLP_TEXT_GENERATION\",\"ref\":null,\"props\":{\"namespace\":\"5453883370739390\",\"children\":\"MODEL_SUB_KIND_NLP_TEXT_GENERATION\"},\"_owner\":null}},\"_owner\":null}},\"_owner\":null}]},\"_owner\":null}]},\"_owner\":null}},\"_owner\":null},\"value\":\"2c1ac526-8da5-48cb-bfe9-8eace385ab30\"}", "SystemPrompt": "## 角色\n你是数据分析助手，当从数据库中获取到的信息不为空时，只有当询问销售额数据时，你才可以使用mermai语法生成\"pie\", \"xyChart\"或\"graph\"等类型的图表图文并茂的分析报告\n## mermaid语法知识\n\"\"\"\n饼图示例\n```mermaid\npie title Pets adopted by volunteers\n    \"Dogs\" : 386\n    \"Cats\" : 85\n    \"Rats\" : 15\n```\n\n折线柱状图示例\n```mermaid\n---\nconfig:\n    themeVariables:\n        xyChart:\n            titleColor: \"#ff0000\"\n            backgroundColor: \"#000000\"\n---\nxychart-beta\n    title \"Sales Revenue\"\n    x-axis [\"product_a\", \"product_b\", \"product_c\", \"product_d\", \"product_e\"]\n    y-axis 4000 --> 10000\n    bar [5000, 6000, 7500, 8200, 9500]\n    line [5000, 6000, 7500, 8200, 9500]\n```\n\"\"\"\n在生成xyChart时，请把背景颜色设置为黑色:\n\nbackgroundColor: \"#000000\"\n"}, "sub_chain_base_info": null}, {"id": "c5a40ee3-8c0c-4049-a9e7-20707896daaa", "name": "Text Concatenation", "widget_id": "WidgetKeyTextTemplate", "widget_detail": {"id": "WidgetKeyTextTemplate", "name": "General Text Template", "desc": "Concatenate variables into a piece of text, allowing the text template to not reference any variables or reference multiple variables.", "group": "WidgetGroupProcessText", "oriWidgetKey": "WidgetKeyTextTemplate", "params": [{"data_class": "string", "category": "attribute", "preview": false, "define": {"id": "Template", "name": "Template", "default_value": "mysql://**************:30123/db_name?user=user&password=password", "disabled": true, "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": null}, {"data_class": "string", "category": "out-port", "preview": false, "define": {"id": "OutPut", "name": "Output", "desc": "Supported transfer methods: Sync  \nSupported data types:  \nString: text", "type": "TYPE_UNSPECIFIED", "data_type": "DATA_TYPE_UNSPECIFIED"}, "param_limits": {"types": ["Any-Any"]}}], "dynamic_end_point": true}, "ui": "{\"dragging\":false,\"height\":164,\"id\":\"c5a40ee3-8c0c-4049-a9e7-20707896daaa\",\"position\":{\"x\":133.0657747133082,\"y\":-37.69272822624626},\"positionAbsolute\":{\"x\":133.0657747133082,\"y\":-37.69272822624626},\"selected\":false,\"type\":\"custom\",\"width\":320}", "values": {"Template": "mysql://**************:30123/db_name?user=user&password=password"}, "sub_chain_base_info": null}], "edges": [{"id": "reactflow__edge-ef230e26-27a8-4e64-8869-0fb616cf97c2ef230e26-27a8-4e64-8869-0fb616cf97c2@@OutPut-38f2af8f-8b39-4e44-9f61-1f0dbff2176b38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@sql", "source": "ef230e26-27a8-4e64-8869-0fb616cf97c2", "source_param": "ef230e26-27a8-4e64-8869-0fb616cf97c2@@OutPut", "target": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b", "target_param": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@sql"}, {"id": "reactflow__edge-38f2af8f-8b39-4e44-9f61-1f0dbff2176b38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@OutPut-728ce06e-b58e-4492-b2a3-ccc320ac8a3a728ce06e-b58e-4492-b2a3-ccc320ac8a3a@@Content", "source": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b", "source_param": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@OutPut", "target": "728ce06e-b58e-4492-b2a3-ccc320ac8a3a", "target_param": "728ce06e-b58e-4492-b2a3-ccc320ac8a3a@@Content"}, {"id": "reactflow__edge-155bf196-e6c7-44b4-8f6f-6ba7cdf863da155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@OutPut-b530985f-3a80-4927-835d-38ea576803b9b530985f-3a80-4927-835d-38ea576803b9@@db_schema", "source": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da", "source_param": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@OutPut", "target": "b530985f-3a80-4927-835d-38ea576803b9", "target_param": "b530985f-3a80-4927-835d-38ea576803b9@@db_schema"}, {"id": "reactflow__edge-b530985f-3a80-4927-835d-38ea576803b9b530985f-3a80-4927-835d-38ea576803b9@@OutPut-a638ca06-b476-4d85-b4e3-8791bccdade4a638ca06-b476-4d85-b4e3-8791bccdade4@@Text", "source": "b530985f-3a80-4927-835d-38ea576803b9", "source_param": "b530985f-3a80-4927-835d-38ea576803b9@@OutPut", "target": "a638ca06-b476-4d85-b4e3-8791bccdade4", "target_param": "a638ca06-b476-4d85-b4e3-8791bccdade4@@Text"}, {"id": "reactflow__edge-a638ca06-b476-4d85-b4e3-8791bccdade4a638ca06-b476-4d85-b4e3-8791bccdade4@@OutPut-ef230e26-27a8-4e64-8869-0fb616cf97c2ef230e26-27a8-4e64-8869-0fb616cf97c2@@Content", "source": "a638ca06-b476-4d85-b4e3-8791bccdade4", "source_param": "a638ca06-b476-4d85-b4e3-8791bccdade4@@OutPut", "target": "ef230e26-27a8-4e64-8869-0fb616cf97c2", "target_param": "ef230e26-27a8-4e64-8869-0fb616cf97c2@@Content"}, {"id": "reactflow__edge-3c3f632a-bc20-444b-a3db-59654c564a983c3f632a-bc20-444b-a3db-59654c564a98@@OutPut-b530985f-3a80-4927-835d-38ea576803b9b530985f-3a80-4927-835d-38ea576803b9@@question", "source": "3c3f632a-bc20-444b-a3db-59654c564a98", "source_param": "3c3f632a-bc20-444b-a3db-59654c564a98@@OutPut", "target": "b530985f-3a80-4927-835d-38ea576803b9", "target_param": "b530985f-3a80-4927-835d-38ea576803b9@@question"}, {"id": "reactflow__edge-728ce06e-b58e-4492-b2a3-ccc320ac8a3a728ce06e-b58e-4492-b2a3-ccc320ac8a3a@@OutPut-ca94fb7d-867c-4f6c-b8f1-1824ba67affaca94fb7d-867c-4f6c-b8f1-1824ba67affa@@data", "source": "728ce06e-b58e-4492-b2a3-ccc320ac8a3a", "source_param": "728ce06e-b58e-4492-b2a3-ccc320ac8a3a@@OutPut", "target": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa", "target_param": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa@@data"}, {"id": "reactflow__edge-155bf196-e6c7-44b4-8f6f-6ba7cdf863da155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@OutPut-ca94fb7d-867c-4f6c-b8f1-1824ba67affaca94fb7d-867c-4f6c-b8f1-1824ba67affa@@schema", "source": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da", "source_param": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@OutPut", "target": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa", "target_param": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa@@schema"}, {"id": "reactflow__edge-3c3f632a-bc20-444b-a3db-59654c564a983c3f632a-bc20-444b-a3db-59654c564a98@@OutPut-ca94fb7d-867c-4f6c-b8f1-1824ba67affaca94fb7d-867c-4f6c-b8f1-1824ba67affa@@question", "source": "3c3f632a-bc20-444b-a3db-59654c564a98", "source_param": "3c3f632a-bc20-444b-a3db-59654c564a98@@OutPut", "target": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa", "target_param": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa@@question"}, {"id": "reactflow__edge-ca94fb7d-867c-4f6c-b8f1-1824ba67affaca94fb7d-867c-4f6c-b8f1-1824ba67affa@@OutPut-551dfe9a-5691-4d31-b6cb-70d3c37de7f3551dfe9a-5691-4d31-b6cb-70d3c37de7f3@@Text", "source": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa", "source_param": "ca94fb7d-867c-4f6c-b8f1-1824ba67affa@@OutPut", "target": "551dfe9a-5691-4d31-b6cb-70d3c37de7f3", "target_param": "551dfe9a-5691-4d31-b6cb-70d3c37de7f3@@Text"}, {"id": "reactflow__edge-c5a40ee3-8c0c-4049-a9e7-20707896daaac5a40ee3-8c0c-4049-a9e7-20707896daaa@@OutPut-155bf196-e6c7-44b4-8f6f-6ba7cdf863da155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@Content", "source": "c5a40ee3-8c0c-4049-a9e7-20707896daaa", "source_param": "c5a40ee3-8c0c-4049-a9e7-20707896daaa@@OutPut", "target": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da", "target_param": "155bf196-e6c7-44b4-8f6f-6ba7cdf863da@@Content"}, {"id": "reactflow__edge-c5a40ee3-8c0c-4049-a9e7-20707896daaac5a40ee3-8c0c-4049-a9e7-20707896daaa@@OutPut-38f2af8f-8b39-4e44-9f61-1f0dbff2176b38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@url", "source": "c5a40ee3-8c0c-4049-a9e7-20707896daaa", "source_param": "c5a40ee3-8c0c-4049-a9e7-20707896daaa@@OutPut", "target": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b", "target_param": "38f2af8f-8b39-4e44-9f61-1f0dbff2176b@@url"}], "viewport": {"x": 63.209328806332906, "y": 189.86792653657238, "zoom": 0.6597539553864479}}, "created_time": 0, "updated_time": 0}