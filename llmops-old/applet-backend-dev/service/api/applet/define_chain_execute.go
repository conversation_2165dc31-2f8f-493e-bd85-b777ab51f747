package applet

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/emicklei/go-restful/v3"
	"github.com/google/uuid"
	"strconv"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

// DebugChain 注册&&执行&&删除
func (r *Resource) DebugChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	reqParam := &models.ChainDebugReq{}
	if err := request.ReadEntity(reqParam); err != nil {
		helper.ErrorSSEResponseV2(response, err)
		return
	}
	if reqParam.ChainID == "" && reqParam.ChainDetail == nil {
		helper.ErrorSSEResponseV2(response, stderr.Internal.Errorf("chain id and chain detail is nil"))
		return
	}

	if reqParam.ChainDetail == nil {
		chainDO, err := applet.ChainManager.GetChainByID(ctx, reqParam.ChainID)
		if err != nil {
			helper.ErrorSSEResponseV2(response, stderr.Wrap(err, "get chain with id:%s", reqParam.ChainID))
			return
		}
		reqParam.ChainName = chainDO.Base.Name
		reqParam.ChainDetail = chainDO.ChainDetail
	}

	if err := reqParam.ChainDetail.IsValid(); err != nil {
		helper.ErrorSSEResponseV2(response, err)
		return
	}

	chainParam, err := reqParam.ToChainParam(reqParam.ChainDetail)
	if err != nil {
		helper.ErrorSSEResponseV2(response, err)
		return
	}
	debugAndResponse(ctx, request, response, chainParam)
}

func debugAndResponse(ctx context.Context, request *restful.Request, response *restful.Response, chainParam *models.ChainParam) {
	stdlog.Infof("[DebugChain] ,req :%v", chainParam)
	response.Header().Set("Access-Control-Allow-Origin", "*")
	response.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	response.Header().Set("Content-Type", "text/event-stream")
	response.Header().Set("Cache-Control", "no-cache")
	response.Header().Set("Connection", "keep-alive")
	helper.SuccessResponse(response, nil)
	response.Flush()

	chainID := chainParam.ChainID
	chainName := chainParam.ChainName
	chainDetail := chainParam.ChainDetail
	executeParams, err := getExecParams(chainParam)
	if err != nil {
		helper.ErrorSSEResponse(response, stderr.Wrap(err, "get exec params "))
		return
	}

	// 体验模式  当应用链不存在时,历史记录等不进行保存
	isExperienceModel := !applet.ChainManager.Exist(ctx, chainID)

	chatID := uuid.New().String()
	if !isExperienceModel {
		chatID, err = applet.ChainDebugManager.Save(ctx, "", chainParam)
		if err != nil {
			helper.ErrorSSEResponse(response, err)
			return
		}
	}

	helper.SSEResponse(response, "chat_id", chatID)
	debugParam := applet.NewChainDebugParam(chainID, chainName, chainDetail, chatID, isExperienceModel, executeParams, NewFlushWriter(response))
	if err := applet.ChainDebugManager.Debug(ctx, debugParam); err != nil {
		helper.ErrorSSEResponse(response, err)
		return
	}
	stdlog.Infof("[DebugChain] success")
}
func getExecParams(debugParams *models.ChainParam) (models.WidgetParams, error) {
	// 把param拆分成两部分，1：构建script的参数，2：运行时的入参
	var executeParams models.WidgetParams
	executeParams = make(map[string]*models.WidgetParam)
	for k, wp := range debugParams.Params {
		w, err := widgets.WidgetFactoryImpl.GetWidget(wp.WidgetID)
		if err != nil || w.Define() == nil {
			return nil, stderr.Wrap(err, "no widget :%v", wp.WidgetID)
		}
		var executeParam *models.WidgetParam
		paramDefineMap := widgets.WidgetParamsToMap(w.Define().Params)
		for key, value := range wp.Params {
			// 只处理用户输入的参数中，在算子定义中存在的，否则忽略（不报错）
			if v, ok := paramDefineMap[key]; ok {
				// 用户输入
				if v.Category == widgets.ParamTypeReqInput {
					if executeParam == nil {
						executeParam = &models.WidgetParam{WidgetID: wp.WidgetID}
					}
					executeParam.UpsertParam(key, value.Value)
				}
			}
		}
		if executeParam != nil {
			executeParams[k] = executeParam
		}
	}
	return executeParams, nil
}

func (r *Resource) RunChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	reqParam := &models.ChainRunReq{}
	if err := request.ReadEntity(reqParam); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	response.Header().Set("Access-Control-Allow-Origin", "*")
	response.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	response.Header().Set("Content-Type", "text/event-stream")
	response.Header().Set("Cache-Control", "no-cache")
	response.Header().Set("Connection", "keep-alive")
	if len(reqParam.ChainID) != 0 {
		response.Header().Set("Chat_id", reqParam.ChainID)
	}
	helper.SuccessResponse(response, nil)
	response.Flush()
	postBody, err := json.Marshal(reqParam.Params)

	chain, err := applet.ChainManager.GetChainByID(ctx, reqParam.ChainID)
	if err != nil {
		helper.ErrorSSEResponse(response, err)
		return
	}

	buffer, err := clients.AppletSvcCli.CallAppletSvcRaw(ctx, reqParam.MlOpsSvcID, string(postBody), NewFlushWriter(response))
	if err != nil {
		helper.ErrorSSEResponse(response, err)
		return
	}

	if len(reqParam.ChatId) != 0 && buffer.Len() != 0 && chain.Base.UsageType == models.ChainUsageTypeLLM {
		err := applet.DialogManager.BatchCreate(ctx, reqParam, buffer)
		if err != nil {
			helper.ErrorSSEResponse(response, err)
			return
		}

		_, err = applet.DialogManager.SaveDialogApp(ctx, reqParam.ChainID)
		if err != nil {
			helper.ErrorSSEResponse(response, err)
			return
		}

	}

	// 打点
	AsyncUpdateChainMetrics(reqParam.ChainID, applet.ChainMetricsTypeExecute)
	stdlog.Infof("run success")
	// helper.SuccessResponse(response, nil)
}

//
//func (r *Resource) UpdateChatDebugState(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	chainID := request.PathParameter(helper.PathParamAppletID)
//	if chainID == "" {
//		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
//		return
//	}
//	chatID := request.PathParameter(helper.PathParamAppletChatID)
//	if chatID == "" {
//		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
//		return
//	}
//
//	stateParam := &models.UpdateDebugStateReq{}
//	if err := request.ReadEntity(stateParam); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if err := stateParam.IsValid(); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	var err error
//	switch stateParam.State {
//	case models.ChainDebugStateSuccess.State:
//		err = applet.ChainDebugManager.DebugSucceed(ctx, chainID, chatID)
//	case models.ChainDebugStateFailed.State:
//		err = applet.ChainDebugManager.DebugFailed(ctx, chainID, chatID)
//	case models.ChainDebugStateCanceled.State:
//		err = applet.ChainDebugManager.DebugCanceled(ctx, chainID, chatID)
//	default:
//		err = stderr.Internal.Error("invalid state :%v", stateParam.State)
//	}
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.ID{ID: chatID})
//}

func (r *Resource) GetChainExecuteParam(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	reqParam := &models.ChainDebugReq{}
	if err := request.ReadEntity(reqParam); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	chain, err := applet.ChainManager.GetChainByID(ctx, reqParam.ChainID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	param, err := reqParam.ToChainParam(chain.ChainDetail)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res := param.Params.ToChainExecuteParam()
	helper.SuccessResponse(response, res)
}

func (r *Resource) GetChainExecuteParamTemplate(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	if chainID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
		return
	}
	res := make(map[string]interface{})
	chain, err := applet.ChainManager.GetChainByID(ctx, chainID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("empty chain id"))
		return
	}
	if chain.ChainDetail == nil {
		helper.ErrorResponse(response, stderr.Internal.Error("chain no param"))
		return
	}
	userInputNodes, err := chain.ChainDetail.GetUserInPortNodes()
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("chain no param"))
		return
	}
	for _, n := range userInputNodes {
		widget, err := n.GetWidgetDetail()
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Error("chain no param"))
			return
		}
		params := widget.GetUserInputParam()
		for _, p := range params {
			if widget.Id == widgets.WidgetKeyFileInput {
				res[fmt.Sprintf("%s@@%s", n.Id, p.Define.Id)] = []struct {
					URL string `json:"url"`
				}{
					{
						URL: fmt.Sprintf("${%v}", p.Define.Name),
					},
				}
			} else {
				res[fmt.Sprintf("%s@@%s", n.Id, p.Define.Id)] = fmt.Sprintf("${%v}", p.Define.Name)

			}
		}
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListChainDebugHistory(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(helper.PathParamAppletID)
	if chainID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("没有提供 chainID"))
		return
	}

	projectId := request.QueryParameter("project_id")
	debugName := request.QueryParameter("debug_name")
	fromTimeStr := request.QueryParameter("from_time")
	toTimeStr := request.QueryParameter("to_time")
	stateStr := request.QueryParameter("states")
	pageSizeStr := request.QueryParameter("page_size")
	pageStr := request.QueryParameter("page")

	fromTime, err := models.ConvertStr2Time(fromTimeStr)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	toTime, err := models.ConvertStr2Time(toTimeStr)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	if fromTime != nil && toTime != nil && fromTime.After(*toTime) {
		helper.ErrorResponse(response, stderr.Internal.Error("fromTime 在 toTime 之后"))
		return
	}

	var states []int32
	if stateStr != "" {
		for _, s := range strings.Split(stateStr, ",") {
			mappedState, err := models.StateStrTODebugState(s)
			if err == nil {
				states = append(states, mappedState.Code)
			}
		}
	}

	var pageSize, page *int
	if pageSizeStr != "" {
		parsedPageSize, err := strconv.Atoi(pageSizeStr)
		if err == nil {
			pageSize = &parsedPageSize
		}
	}
	if pageStr != "" {
		parsedPage, err := strconv.Atoi(pageStr)
		if err == nil {
			page = &parsedPage
		}
	}

	param := &dao.ChainDebugParam{
		ChainID:   &chainID,
		DebugName: &debugName,
		FromTime:  fromTime,
		ToTime:    toTime,
		States:    &states,
		PageSize:  pageSize,
		Page:      page,
		ProjectID: &projectId,
	}

	chainDebugHistoryRsp, err := applet.ChainDebugManager.ListChainDebugHistory(ctx, param)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, chainDebugHistoryRsp)
}

func (r *Resource) GetDebugHistoryByChatID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chatID := request.PathParameter(helper.PathParamAppletRunID)
	if chatID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("没有提供 chatID"))
		return
	}

	chatDebugMsgShort, err := applet.ChainDebugManager.GetDebugHistoryByChatID(ctx, chatID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, chatDebugMsgShort)
}

func (r *Resource) DeleteDebugHistoryByChatID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chatID := request.PathParameter(helper.PathParamAppletRunID)
	if chatID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("没有提供 chatID"))
		return
	}

	if err := applet.ChainDebugManager.DeleteDebugHistoryByChatID(ctx, chatID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetChainSnapshot(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chatID := request.PathParameter(helper.PathParamAppletSnapID)
	if chatID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("没有提供 chatID"))
		return
	}

	ChainSnapshot, err := applet.ChainDebugManager.GetChainSnapshot(ctx, chatID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, ChainSnapshot)
}

func (r *Resource) CancelChat(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	chatID := request.PathParameter(helper.PathParamAppletRunID)
	if chatID == "" {
		helper.ErrorResponse(response, stderr.Internal.Error("没有提供 chatID"))
		return
	}

	debugMessage := &debug.OverallDebugMessage{}
	if err := request.ReadEntity(debugMessage); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	err := applet.ChainDebugManager.CancelChat(ctx, chatID, debugMessage)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	helper.SuccessResponse(response, helper.EmptyRsp{})
}
