package applet

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

const (
	resourceID       = "id"
	projectID        = "project_id"
	QueryParamSimple = "simple"
	QueryValueTrue   = "true"
)

func (r *Resource) ListTemplateGroups(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	simple := request.QueryParameter(QueryParamSimple) == QueryValueTrue
	res, err := applet.ChainTemplateManger.ListTemplateGroups(ctx, simple)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListChainTemplates(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	simple := request.QueryParameter(QueryParamSimple) == QueryValueTrue
	res, err := applet.ChainTemplateManger.ListAllTemplates(ctx, simple)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) GetChainTemplate(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(resourceID)
	if id == "" {
		helper.ErrorResponse(response, stderr.Errorf("the id of template is empty"))
		return
	}
	res, err := applet.ChainTemplateManger.GetTemplateById(ctx, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

//func (r *Resource) UpsertChainTemplate(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	id := request.PathParameter(resourceID)
//
//	entity := new(models.ChainTemplateDO)
//	if err := request.ReadEntity(entity); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if entity.ID == "" || entity.ID != id {
//		helper.ErrorResponse(response, stderr.Errorf("the id of entity is empty or  different with path"))
//		return
//	}
//	if err := applet.ChainTemplateManger.UpsertTemplate(ctx, entity); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.EmptyRsp{})
//}
//
//func (r *Resource) DeleteChainTemplate(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	id := request.PathParameter(resourceID)
//	if id == "" {
//		helper.ErrorResponse(response, stderr.Errorf("the id of template is empty"))
//		return
//	}
//	if err := applet.ChainTemplateManger.DelTemplateById(ctx, id); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.EmptyRsp{})
//}
