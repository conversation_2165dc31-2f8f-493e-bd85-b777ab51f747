package applet

import (
	"encoding/json"
	"fmt"
	"github.com/aws/smithy-go/ptr"
	"github.com/emicklei/go-restful/v3"
	"github.com/influxdata/kapacitor/uuid"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
)

const (
	CustomWidgetServiceNamePrefix = "applet-custom-"
)

var (
	customWidgetSvc = new(applet.WidgetCustomSvc)
)

func (r *Resource) CreateCustomWidget(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call createCustomWidget"))
			return
		}
	}()
	ctx := helper.GenNewCtx(request)
	createReq := new(models.CreateCustomWidgetReq)
	widgetDO := new(models.CustomWidgetDO)
	if err = request.ReadEntity(createReq); err != nil {
		return
	}
	// 拷贝算子创建的基本信息到DO
	if err = helper.CopyTheSameFields(createReq.CustomWidgetInfo, widgetDO); err != nil {
		return
	}
	// 设置额外信息
	creator, err := helper.GetUser(ctx)
	if err != nil {
		return
	}
	widgetDO.Creator = creator
	widgetDO.ID = uuid.New().String()
	widgetDO.Status = models.StoppedStatus
	widgetDO.ProjectID = helper.GetProjectID(ctx)
	widgetDO.DeployInfo = &models.CustomWidgetDeployInfo{
		ServiceName: CustomWidgetServiceNamePrefix + widgetDO.ID,
	}
	// 数据格式检查
	if err = widgetDO.Valid(); err != nil {
		return
	}
	// 创建自定义算子
	widgetID, err := applet.WidgetManager.CreateCustomWidget(ctx, widgetDO)
	if err != nil {
		return
	}
	// 立即启动
	if createReq.Start {
		if _, err = customWidgetSvc.StartCustomWidget(ctx, widgetID); err != nil {
			return
		}
	}
	widget, err := applet.WidgetManager.GetCustomWidgetByID(ctx, widgetID)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) UpdateCustomWidget(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call UpdateCustomWidget"))
			return
		}
	}()
	ctx := helper.GenNewCtx(request)
	widgetDO := new(models.CustomWidgetDO)
	updateReq := new(models.UpdateCustomWidgetReq)
	if err = request.ReadEntity(updateReq); err != nil {
		return
	}
	// 拷贝基本信息
	if err = helper.CopyTheSameFields(updateReq.CustomWidgetInfo, widgetDO); err != nil {
		return
	}
	widgetID, err := applet.WidgetManager.UpdateCustomWidget(ctx, updateReq.ID, widgetDO)
	if err != nil {
		return
	}
	if updateReq.Restart {
		if _, err = customWidgetSvc.StopCustomWidget(ctx, widgetID); err != nil {
			return
		}
		if _, err = customWidgetSvc.StartCustomWidget(ctx, widgetID); err != nil {
			return
		}
	}
	widget, err := applet.WidgetManager.GetCustomWidgetByID(ctx, widgetID)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) ListAllCustomWidgets(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	customWidgets, err := applet.WidgetManager.ListAllCustomWidgets(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, customWidgets)
}
func (r *Resource) ListCustomWidgets(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	queryParam := new(dao.CustomWidgetQueryParam)
	if err := request.ReadEntity(queryParam); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if queryParam.ProjectID == nil {
		queryParam.ProjectID = ptr.String(helper.GetProjectID(ctx))
	}

	widgets, err := applet.WidgetManager.ListCustomWidgets(ctx, queryParam)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widgets)
}
func (r *Resource) DeleteCustomWidget(request *restful.Request, response *restful.Response) {
	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	widget, err := applet.WidgetManager.GetCustomWidgetByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err = customWidgetSvc.StopCustomWidget(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err = applet.WidgetManager.DeleteCustomWidget(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) StartCustomWidget(request *restful.Request, response *restful.Response) {
	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err := customWidgetSvc.StartCustomWidget(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	widget, err := applet.WidgetManager.GetCustomWidgetByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) StopCustomWidget(request *restful.Request, response *restful.Response) {
	id := new(helper.ID)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	_, err := customWidgetSvc.StopCustomWidget(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	widget, err := applet.WidgetManager.GetCustomWidgetByID(ctx, id.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) ListCustomWidgetLabels(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectID := helper.GetProjectID(ctx)
	labels, err := applet.LabelManager.ListCustomWidgetLabel(ctx, projectID)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, labels)
}
func (r *Resource) ListDynamicWidget(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	dynamicWidgetType := request.PathParameter(helper.PathParamDynamicWidgetType)
	if err := models.IsDynamicWidgetTypeValid(dynamicWidgetType); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	widgetDesc, err := applet.WidgetManager.ListDynamicWidgetsByType(ctx, dynamicWidgetType)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widgetDesc)
}
func (r *Resource) GetDynamicWidget(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	dynamicWidgetType := request.PathParameter(helper.PathParamDynamicWidgetType)
	if err := models.IsDynamicWidgetTypeValid(dynamicWidgetType); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	dynamicWidgetKey := request.PathParameter(helper.PathParamDynamicWidgetKey)
	widget, err := applet.WidgetManager.GetDynamicWidgetsByKey(ctx, dynamicWidgetType, dynamicWidgetKey)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widget)
}
func (r *Resource) Stats(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectID := helper.GetProjectID(ctx)
	chains, err := applet.ChainManager.SearchForBaseDO(ctx, &applet.SearchChainParam{
		//ChainType: ptr.Int32(int32(models.ChainTypeUserCreate)),
		ProjectID: projectID,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res := models.ChainState{ChainCount: int32(len(chains))}

	helper.SuccessResponse(response, res)
}

//func (r *Resource) GetDynamicInputWidget(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	svc := applet.InputAggregation{}
//	param := &models.GetDynamicInputWidgetDefineReq{}
//	if err := request.ReadEntity(param); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if err := param.IsValid(); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	paramString := make([]string, 0)
//	for _, p := range param.Params {
//		paramString = append(paramString, p.Name)
//	}
//	widget, err := svc.GetWidget(ctx, paramString)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, widget)
//}

// ListWidgetGroups 获取所有算子
func (r *Resource) ListWidgetGroups(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	widgets, err := applet.WidgetManager.ListWidgetGroups(ctx)
	bytes, _ := json.Marshal(widgets)
	fmt.Println(string(bytes))
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, widgets)
}

// ListWidgetGroupsAsText 获取所有算子转为markdown文档
func (r *Resource) ListWidgetGroupsAsText(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	widgetGroups, err := applet.WidgetManager.ListWidgetGroups(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	var markdown strings.Builder
	// 算子描述
	markdown.WriteString("# " + "算子描述" + " \n\n")
	for _, group := range widgetGroups {
		markdown.WriteString("## " + group.Name + " \n\n")
		for _, widget := range group.Widgets {
			markdown.WriteString("### " + widget.Name + "算子 \n")
			markdown.WriteString("#### " + "算子简介" + "\n")
			markdown.WriteString("| 算子id | 算子名称 | 算子描述 |\n")
			markdown.WriteString("|:--:|:--:|:--:|\n")
			markdown.WriteString(fmt.Sprintf("| %s | %s | %s |\n", widget.Id, widget.Name, widget.Desc))
			markdown.WriteString("\n\n")

			markdown.WriteString("#### " + "算子参数" + "\n")
			markdown.WriteString("| 参数id | 参数类型(输入/属性/输出) | 参数名称 | 参数描述 | 数据类型 |\n")
			markdown.WriteString("|:--:|:--:|:--:|:--:|:--:|\n")
			for _, param := range widget.Params {
				define := param.Define
				if define.Hidden {
					continue
				}
				desc := strings.ReplaceAll(define.Desc, "\n", "")
				dataType := define.DataType.String()
				if param.ParamLimits != nil && len(param.ParamLimits.Types) != 0 {
					dataType = helper.StrsJoin(param.ParamLimits.Types, ",")
				}
				markdown.WriteString(fmt.Sprintf("| %s | %s | %s | %s | %s |\n", define.Id,
					param.Category, define.Name, desc, dataType))
			}
			markdown.WriteString("\n\n\n")
		}
	}

	// 参数描述
	paramFactory := widgets.GetParamsFactory()
	markdown.WriteString("# 参数描述 \n\n")
	markdown.WriteString("## 传输方式 \n\n")
	markdown.WriteString("| 传输方式id | 传输方式名称 | 传输方式描述 |\n")
	markdown.WriteString("|:--:|:--:|:--:|\n")
	for _, mt := range paramFactory.ListModeTypeDesc() {
		markdown.WriteString(fmt.Sprintf("| %s | %s | %s |\n", mt.Type, mt.Name, mt.Desc))
	}
	markdown.WriteString("\n\n")

	markdown.WriteString("## 传输类型 \n\n")
	markdown.WriteString("| 传输类型id | 传输类型描述 | 示例数据 |\n")
	markdown.WriteString("|:--:|:--:|:--:|\n")
	for _, dt := range paramFactory.ListDataTypeDesc() {
		example := strings.ReplaceAll(toolkit.SprintPrettyJson(dt.Example), "\n", "")
		markdown.WriteString(fmt.Sprintf("| %s | %s | %s |\n", dt.Type, dt.Desc, example))
	}
	markdown.WriteString("\n\n")

	// 设置响应头,并将Markdown文本作为响应正文返回
	response.Header().Set("Content-Type", "text/markdown")
	response.Header().Set("Content-Disposition", "attachment; filename=widgets.md")
	response.Write([]byte(markdown.String()))
}

func (r *Resource) UpsertTestWidget(request *restful.Request, response *restful.Response) {
	testWidget := new(widgets.Widget)
	ctx := helper.GenNewCtx(request)
	if err := request.ReadEntity(testWidget); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if err := applet.WidgetManager.UpsertTestWidget(ctx, testWidget); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) CleanTestWidget(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	if err := applet.WidgetManager.CleanTestWidgets(ctx); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
