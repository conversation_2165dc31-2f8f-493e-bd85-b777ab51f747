package applet

import (
	"github.com/emicklei/go-restful/v3"
	"sort"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/applet-backend/core/api_tool"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

//func (r *Resource) ListSelectedExperis(request *restful.Request, response *restful.Response) {
//	var err error
//	defer func() {
//		if err != nil {
//			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call ListSelectedExperis"))
//		}
//	}()
//	ctx := helper.GenNewCtx(request)
//	industry := request.QueryParameter(QueryParamIndustry)
//	apps, err := listSelectedExperisByIndustry(ctx, industry)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, apps)
//}
//
//func (r *Resource) AddSelectedExperis(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	req := new(pb.SetSelectedReq)
//	if err := request.ReadEntity(req); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if req.Industry == "" {
//		err := stderr.Error("the Industry is empty")
//		helper.ErrorResponse(response, err)
//		return
//	}
//
//	if err := applet.ChainManager.SetSelectedApps(ctx, req, applet.ActionTypeAdd); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.EmptyRsp{})
//}
//func (r *Resource) RemoveSelectedExperis(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	req := new(pb.SetSelectedReq)
//	if err := request.ReadEntity(req); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if req.Industry == "" {
//		err := stderr.Error("the Industry is empty")
//		helper.ErrorResponse(response, err)
//		return
//	}
//	if err := applet.ChainManager.SetSelectedApps(ctx, req, applet.ActionTypeRemove); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.EmptyRsp{})
//}

func (r *Resource) CountNums(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	// 查询所有应用(包含内部、外部等各种类型)
	applications, err := listApplications(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 查询所有应用体验
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            "",
		onlyHealth:         false,
		needChainDetail:    false,
		filterEnableAccess: false,
		withExternal:       true,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	pluginNums, err := api_tool.ToolManager.ListToolCollectionBaseInfos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	ret := &CountNumsResp{
		AppNums:    len(applications),
		ExperiNums: len(appExperiServices),
		PluginNums: len(pluginNums),
	}
	helper.SuccessResponse(response, ret)
}

// ListExperis 查询受欢迎的体验对象
func (r *Resource) ListExperis(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	// 查询所有应用(包含内部、外部等各种类型)
	applications, err := listApplications(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 查询所有应用体验
	appExperis, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            "",
		onlyHealth:         false,
		needChainDetail:    false,
		filterEnableAccess: false,
		withExternal:       true,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	resp := make([]*models.AppletChainBaseDO, 0)
	appServiceMap := helper.CvtSlice2Map(appExperis, func(value *models.AppExperimentService) string { return value.ID })
	for _, app := range applications {
		if appService, ok := appServiceMap[app.ID]; ok {
			app.EnableAccess = appService.EnableAccess
			resp = append(resp, app)
		}
	}

	limit, err := helper.LimitQP.GetIntValueWithDefault(request, helper.DefaultLimits)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	sortBy := helper.SortByQP.GetValue(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	switch sortBy {
	case pb.SortBy_Sort_By_Clone_Times.String():
		sort.Slice(resp, func(i, j int) bool {
			return resp[i].MetricsInfo.CloneTimes > resp[j].MetricsInfo.CloneTimes
		})
	case pb.SortBy_Sort_By_Execute_Times.String():
		sort.Slice(resp, func(i, j int) bool {
			return resp[i].MetricsInfo.ExecuteTimes > resp[j].MetricsInfo.ExecuteTimes
		})
	default: //case pb.SortBy_Sort_By_Visit_Times.String():
		sort.Slice(resp, func(i, j int) bool {
			return resp[i].MetricsInfo.VisitTimes > resp[j].MetricsInfo.VisitTimes
		})
	}
	if len(resp) > limit {
		resp = resp[:limit]
	}
	helper.SuccessResponse(response, resp)
}

//func listSelectedExperisByIndustry(ctx context.Context, industry string) ([]*models.AppletChainBaseDO, error) {
//	projectID := helper.GetProjectID(ctx)
//	chainDOs, err := applet.ChainManager.SearchForSimpleChainDO(ctx, &applet.SearchChainParam{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		return nil, err
//	}
//
//	chainStatusMap, err := applet.ChainDeployManager.ListSimpleStateAsMap(ctx)
//	if err != nil {
//		return nil, err
//	}
//	res := make([]*models.AppletChainBaseDO, 0)
//	for _, chainDO := range chainDOs {
//		app := chainDO.Base
//		// 是否为精选应用体验
//		if len(app.SpaceInfo.IsSelected) == 0 {
//			continue
//		}
//		// 是否为该行业的首页精选
//		if industry != "" {
//			isSelected, _ := app.SpaceInfo.IsSelected[industry]
//			if !isSelected {
//				continue
//			}
//		}
//		// 已经发布到serving,成为一个应用体验
//		if status, ok := chainStatusMap[app.ID]; ok &&
//			status.ChainOnlineState == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
//			app.Published = true
//			app.ServiceInfo = cvtChainState2AppServiceInfo(status)
//			res = append(res, &app)
//		}
//	}
//	return res, nil
//}

//func listExperisByIndustry(ctx context.Context, industry string) ([]*models.AppletChainBaseDO, error) {
//	projectID := helper.GetProjectID(ctx)
//	chainDOs, err := applet.ChainManager.SearchForSimpleChainDO(ctx, &applet.SearchChainParam{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		return nil, err
//	}
//
//	chainStatusMap, err := applet.ChainDeployManager.ListSimpleStateAsMap(ctx)
//	if err != nil {
//		return nil, err
//	}
//	res := make([]*models.AppletChainBaseDO, 0)
//	for _, chainDO := range chainDOs {
//		app := chainDO.Base
//		// 行业标签筛选
//		if industry != "" {
//			_, ok := app.SpaceInfo.IndustryLabels[industry]
//			if !ok {
//				continue
//			}
//		}
//
//		// 已经发布到serving,成为一个应用体验对象
//		if status, ok := chainStatusMap[app.ID]; ok &&
//			status.ChainOnlineState == serving.MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE {
//			app.Published = true
//			app.ServiceInfo = cvtChainState2AppServiceInfo(status)
//			res = append(res, &app)
//		}
//	}
//	return res, nil
//}

type CountNumsResp struct {
	AppNums    int `json:"app_nums" description:"应用数量"`
	ExperiNums int `json:"experi_nums" description:"应用体验数量"`
	PluginNums int `json:"plugin_nums" description:"应用插件数量"`
}
