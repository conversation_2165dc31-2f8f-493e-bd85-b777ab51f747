package applet

//
//import (
//	"fmt"
//	"github.com/aws/smithy-go/ptr"
//	"github.com/emicklei/go-restful/v3"
//	"github.com/pkg/errors"
//	"time"
//	"transwarp.io/applied-ai/aiot/vision-std/auth"
//	"transwarp.io/applied-ai/aiot/vision-std/stderr"
//	"transwarp.io/applied-ai/applet-backend/core/applet"
//	"transwarp.io/applied-ai/applet-backend/pkg/helper"
//	"transwarp.io/applied-ai/applet-backend/pkg/models"
//)
//
//// ListChainTemplates 获取应用链模板
//func (r *Resource) ListChainTemplates(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	chains, err := r.chainManager.SearchForBaseDO(ctx, &applet.SearchChainParam{
//		ChainType: ptr.Int32(int32(models.ChainTypeTemplate)),
//	})
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, chains)
//}
//
//// ForkChainFromTemplate 获取应用链模板
//func (r *Resource) ForkChainFromTemplate(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	param := &helper.ID{}
//	if err := request.ReadEntity(param); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	chainTemplateID := param.ID
//	chain, err := r.chainManager.GetChainByID(ctx, chainTemplateID)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	// 是否为模板
//	if chain.Base.Type != models.ChainTypeTemplate {
//		helper.ErrorResponse(response, errors.Errorf("chain :%v not template", chainTemplateID))
//		return
//	}
//	// 变更 basic 信息
//	chain.Base.Type = models.ChainTypeUserCreate
//	chain.Base.ID = ""
//	chain.Base.Creator = auth.GetAuthContext(request).GetUsername()
//	chain.Base.Name = fmt.Sprintf("fork-from-%s#%s", chain.Base.Name, time.Now().Format("2006-01-02_15-04-05"))
//	chain.Base.ProjectID = helper.GetProjectID(ctx)
//	chainID, err := r.chainManager.CreateChain(ctx, chain)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.ID{ID: chainID})
//}
//
//// SaveAsTemplate 分享为模板
//func (r *Resource) SaveAsTemplate(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	param := &models.ForkChainToTemplateReq{}
//	if err := request.ReadEntity(param); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	chainID := request.PathParameter(helper.PathParamAppletID)
//	if chainID == "" {
//		helper.ErrorResponse(response, stderr.Internal.Error("invalid chain id  :%v", chainID))
//		return
//	}
//	if param.AppName == "" {
//		helper.ErrorResponse(response, stderr.Internal.Error("invalid param :%v", param))
//		return
//	}
//	chain, err := r.chainManager.GetChainByID(ctx, chainID)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	// 是否为模板
//	if chain.Base.Type != models.ChainTypeUserCreate {
//		helper.ErrorResponse(response, errors.Errorf("chain :%v not user chain", chainID))
//		return
//	}
//	// 变更 basic 信息
//	chain.Base.Type = models.ChainTypeTemplate
//	chain.Base.ID = ""
//	chain.Base.Creator = auth.GetAuthContext(request).GetUsername()
//	chain.Base.Name = param.AppName
//	newChainID, err := r.chainManager.CreateChain(ctx, chain)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.ID{ID: newChainID})
//}
