package applet

import (
	"github.com/aws/smithy-go/ptr"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/api_tool"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
)

func getCollectionIDFromPath(request *restful.Request) (string, error) {
	collectionID := request.PathParameter("id")
	if collectionID == "" {
		return collectionID, stderr.InvalidParam.Error("collection id is empty")
	}
	return collectionID, nil
}

func (r *Resource) QueryToolCollectionByID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.GetToolCollectionByID(ctx, collectionID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListToolCollections(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListToolCollectionBaseInfos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListPublishedDescriber(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListPublishedDescriber(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListToolCollectionDemos(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListCollectionDemos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) CreateToolCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionDO := &api_tools.APIToolCollectionDO{}
	err := request.ReadEntity(&collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.CreateAPIToolCollection(ctx, collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: res})
}

func (r *Resource) UpdateToolCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	collectionDO := &api_tools.APIToolCollectionDO{}
	err = request.ReadEntity(&collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	existCollection, err := api_tool.ToolManager.GetToolCollectionByID(ctx, collectionID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if ptr.ToBool(existCollection.BaseInfo.Released) {
		helper.ErrorResponse(response, helper.ToolUpdateVerifyErr.Error("released tool can not update"))
		return
	}
	err = api_tool.ToolManager.UpdateAPIToolCollectionByID(ctx, collectionID, collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{
		ID: collectionID,
	})
}

func (r *Resource) ParserMetaData(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	metaInfo := api_tools.APIToolMetaInfo{}
	err := request.ReadEntity(&metaInfo)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.ParserMeta(ctx, metaInfo.Type, metaInfo.Meta)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)

}

func (r *Resource) DeleteCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err = api_tool.ToolManager.BatchDelCollectionsByIDs(ctx, []string{collectionID})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: collectionID})

}

func (r *Resource) BatchDeleteCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	IDs := helper.IDs{}
	if err := request.ReadEntity(&IDs); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.BatchDelCollectionsByIDs(ctx, IDs.IDs)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, IDs)

}

func (r *Resource) PublishCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ID := helper.ID{}
	if err := request.ReadEntity(&ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.PublishCollectionByID(ctx, ID.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID)

}

func (r *Resource) CancelPublishCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ID := helper.ID{}
	if err := request.ReadEntity(&ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.CancelPublishCollectionByID(ctx, ID.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID)

}

func (r *Resource) TestToolAPI(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	param := &api_tools.APIToolTestParamInfo{}
	if err := request.ReadEntity(&param); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.TestToolAPI(ctx, param)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.HttpStringResp{RespBody: res})

}

//func (r *Resource) CallToolAPI(request *restful.Request, response *restful.Response) {
//	ctx := helper.GenNewCtx(request)
//	param := &api_tools.APIToolCallParamInfo{}
//	if err := request.ReadEntity(&param); err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	res, err := api_tool.ToolManager.CallToolAPI(ctx, param)
//	if err != nil {
//		helper.ErrorResponse(response, err)
//		return
//	}
//	helper.SuccessResponse(response, helper.HttpStringResp{RespBody: res})
//
//}
