package applet

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func AsyncUpdateChainMetrics(chainID string, event applet.ChainMetricsType) {
	helper.GoFunWithRecover(func() {
		// 打点
		if err := applet.ChainMetricsManager.Update(context.Background(), chainID, map[applet.ChainMetricsType]int64{event: 1}); err != nil {
			stdlog.Errorf("metrics err ：%v,chain :%v ,event :%v", err, chainID, event)
		}
	})
}
