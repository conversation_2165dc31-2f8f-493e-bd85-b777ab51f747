package guardrails

import (
	"errors"
	"io"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	"github.com/emicklei/go-restful/v3"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/applet-backend/core/guardrails"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func (r *Resource) GetGuardrailsSafetyConfig(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectId := request.PathParameter("pid")
	if projectId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("project id can not be empty"))
		return
	}
	safetyConfig, err := guardrails.GuardrailsManager.GetSafetyConfig(ctx, projectId)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			helper.ErrorResponse(response, err)
			return
		}

		// 记录不存在,新建记录并更新到安全围栏服务
		safetyConfig = widgets.NewSafetyConfig(projectId)
		if err := guardrails.GuardrailsManager.UpsertSafetyConfig(ctx, safetyConfig, true); err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to do UpsertSafetyConfig"))
			return
		}
	}
	helper.SuccessResponse(response, safetyConfig)
}

func (r *Resource) UpdateGuardrailsSafetyConfig(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectId := request.PathParameter("pid")
	if projectId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("project id can not be empty"))
		return
	}
	safetyConfig := &widgets.SafetyConfig{}
	if err := request.ReadEntity(safetyConfig); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	safetyConfig.ProjectID = projectId

	// 先根据ProjectID查询出SafetyConfig,如果存在,复用之前的id,不存在,创建新的id
	config, err := guardrails.GuardrailsManager.GetSafetyConfig(ctx, projectId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		helper.ErrorResponse(response, err)
		return
	}
	if config == nil {
		safetyConfig.ID = toolkit.NewUUID()
	} else {
		safetyConfig.ID = config.ID
	}

	if err = guardrails.GuardrailsManager.UpsertSafetyConfig(ctx, safetyConfig, true); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	// 设置文件内容的值
	err = widgets.SetFileContent(safetyConfig)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, safetyConfig)
	return
}

func (r *Resource) PreCheckGuard(request *restful.Request, response *restful.Response) {
	body, err := io.ReadAll(request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "reading request body"))
		return
	}
	stdlog.Debugf("guard pre checking: %s", string(body))
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) PostCheckGuard(request *restful.Request, response *restful.Response) {
	body, err := io.ReadAll(request.Request.Body)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "reading request body"))
		return
	}
	stdlog.Debugf("guard post checking: %s", string(body))
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetGuardrailsSafetyConfigById(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter("id")
	if id == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("id can not be empty"))
		return
	}
	safetyConfig, err := guardrails.GuardrailsManager.GetSafetyConfigById(ctx, id, true)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, safetyConfig)
}

func (r *Resource) GetAllGuardrailsSafetyConfigs(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	safetyConfigs, err := guardrails.GuardrailsManager.GetAllSafetyConfig(ctx, true)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, safetyConfigs)
}

func pathListAssembly(projectId string, pathList *GuardrailsPathList) (string, error) {
	InputRuleFileContent, err := widgets.ReadFileContent(pathList.InputRuleFilePath)
	if err != nil {
		return "", err
	}
	InputSensitiveFileContent, err := widgets.ReadFileContent(pathList.InputSensitiveFilePath)
	if err != nil {
		return "", err
	}
	OutputSensitiveFileContent, err := widgets.ReadFileContent(pathList.OutputSensitiveFilePath)
	if err != nil {
		return "", err
	}
	fileContent := &GuardrailsFileContent{
		ProjectID:                  projectId,
		InputRuleFileContent:       InputRuleFileContent,
		InputSensitiveFileContent:  InputSensitiveFileContent,
		OutputSensitiveFileContent: OutputSensitiveFileContent,
	}
	return helper.StructToJson(fileContent), nil
}

type GuardrailsPathList struct {
	InputRuleFilePath       string
	InputSensitiveFilePath  string
	OutputSensitiveFilePath string
}

type GuardrailsFileContent struct {
	ProjectID                  string   `json:"project_id"`
	InputRuleFileContent       []string `json:"input_rule_file_content"`
	InputSensitiveFileContent  []string `json:"input_sensitive_file_content"`
	OutputSensitiveFileContent []string `json:"output_sensitive_file_content"`
}
