package guardrails

import (
	"net/http"
	"path"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

func NewAPI(root string, subPath string) *restful.WebService {
	r := &Resource{
		WebService: new(restful.WebService),
	}
	p := path.Join(root, subPath)
	r.GuardrailsService(p)
	return r.WebService
}

type Resource struct {
	*restful.WebService
}

func (r *Resource) GuardrailsService(root string) {
	projectIDPathParam := r.PathParameter("project_id", "项目ID").Required(true)

	tags := []string{"安全中心-安全围栏配置"}
	r.Path(root).Consumes(restful.MIME_JSON, "text/event-stream").Produces(restful.MIME_JSON, "text/event-stream")

	r.Route(r.GET("/projects-safety/{pid}/guardrail").To(r.GetGuardrailsSafetyConfig).
		Doc("根据项目id获取安全围栏配置").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDPathParam).
		Writes(widgets.SafetyConfig{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), widgets.SafetyConfig{}))

	r.Route(r.PUT("/projects-safety/{pid}/guardrail").To(r.UpdateGuardrailsSafetyConfig).
		Doc("更新安全围栏配置").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDPathParam).
		Reads(widgets.SafetyConfig{}).
		Writes(widgets.SafetyConfig{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), widgets.SafetyConfig{}))

	r.Route(r.POST("/projects-safety:preCheck").To(r.PreCheckGuard).
		Doc("服务输入安全校验").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(helper.EmptyRsp{}).
		Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.POST("/projects-safety:postCheck").To(r.PostCheckGuard).
		Doc("服务输出安全校验").Metadata(restfulspec.KeyOpenAPITags, tags).
		Reads(helper.EmptyRsp{}).
		Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	r.Route(r.GET("/projects-safety/-/{id}/guardrail").To(r.GetGuardrailsSafetyConfigById).
		Doc("根据id获取安全围栏配置").Metadata(restfulspec.KeyOpenAPITags, tags).
		Writes(widgets.SafetyConfig{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), widgets.SafetyConfig{}))

	r.Route(r.GET("/projects-safety/guardrail/all").To(r.GetAllGuardrailsSafetyConfigs).
		Doc("获取所有的安全围栏配置").Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []widgets.SafetyConfig{}))

}
