syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message DynamicParam {
  enum DataType {
    DATA_TYPE_UNSPECIFIED = 0;
    DATA_TYPE_INT         = 1;
    DATA_TYPE_FLOAT       = 2;
    DATA_TYPE_STRING      = 3;
    DATA_TYPE_BOOLEAN     = 4;
  }
  enum Type {
    TYPE_UNSPECIFIED = 0;
    TYPE_INPUT       = 1; // 单行输入文本框, 支持开启多选
    TYPE_NUMBER      = 2; // 单行输入数字框, 支持开启多选
    TYPE_PASSWORD    = 3; // 单行输入密码框，不支持多选
    TYPE_TEXTAREA    = 4; // 多行输入文本框，不支持多选
    TYPE_SWITCH      = 5; // 单行开关选择器，不支持多选
    
    // TYPE_SELECTOR 单选框， 选择范围由datasource给定, 支持开启多选
    // e.g. datasource="true@@是,false@@否"
    // 则下拉框中则展示 ["是","否"]
    // 最终用户选择确认后，传递给后端的值应为名称对应的id
    TYPE_SELECTOR = 6;
    
    // TYPE_SELECTOR_DYNAMIC 动态选择框，选择范围通过调用datasource中的指定API获取， 支持开启多选
    // e.g. datasource="name,id@/hub/widgets/range/products?category=tick"
    // 则前端调用API(/hub/widgets/range/products?category=tick) 获取响应结果,
    //  [{"id":"id1","name":"name1"},{"id":"id2","name":"name2"}]
    // 则对应的最终下拉框应为： id1@@name1,id2@@name2
    TYPE_SELECTOR_DYNAMIC = 7;
    
    // TYPE_KVITEM 键值对类型, 用户需要手动成对输入, 支持开启多选
    // 无论是否开启多选，value 中键值对均使用json格式进行, e.g. {"key1":"value1"}， {"key1":"value1","key2":"value2"}
    TYPE_KVITEM = 8;
    
    // 代码类型
    TYPE_CODE_PYTHON  =  9;
    TYPE_CODE_JSONNET = 10;
    
    // 应用链类型
    TYPE_APPLET_CHAIN = 11;
    
    // 智能体算子相关
    TYPE_AGENT_MODEL_API           = 12;
    TYPE_AGENT_INSTRUCTION         = 13;
    TYPE_AGENT_SKILL_API_TOOLS     = 14;
    TYPE_AGENT_SKILL_KNOW_TOOLS    = 15;
    TYPE_AGENT_SKILL_SERVICE_TOOLS = 16;
    
    // 文本知识库算子
    TYPE_TEXT_KNOWLEDGE_BASE = 17;
    
    // 滑动条类型
    TYPE_SLIDER = 18;
    
    // 工具算子调用
    TYPE_TOOL_CALL = 19;
    
    // 统一模型服务下拉框
    TYPE_MODEL_SERVICE = 20;
    
    // 输入安全防护
    TYPE_GUARDRAIL_INPUT = 21;
    
    // 输出安全防护
    TYPE_GUARDRAIL_OUTPUT = 22;
    
    // 参数提取
    TYPE_PARAMETER_EXTRACTOR = 23;
    
    // 代码实例id
    TYPE_CODE_INSTANCE_ID = 24;
  }
  string      id            = 1; // id 为最终存储该属性值时的Key
  string      name          = 2; // name 为属性的展示名称
  string      desc          = 3; // desc 为属性的描述, 通常以名称旁的?形式进行展示
  Type        type          = 4; // type 为该属性在前端的展示样式类型
  DataType    data_type     = 5; // data_type 为该属性的数据类型（仅用作语法校验，前端最终传输values时，全部以string类型传输）
  NumberRange number_range  = 6; // number_range 当data_type为 int 或 float 时, 数值的取值范围
  string      default_value = 7; // default_value 该属性默认的属性值

  // datasource 为属性值的可选范围, 需要结合 ParamType 进行使用

  // 当 PARAM_TYPE_SELECTOR 时， 格式为 <id1>@@<name1>,<id2>@@<name2>
  //  * 多项之间使用 ',' 进行分割
  //  * 每项的id与name之间通过 '@@' 进行分割， 语义为 <id>@@<name>，
  //  * 每项中 ’@@<name>‘ 部分为可选， 即存在 "id1,id2,id3" 的range, 此时默认 name==id
  
  // 当 PARAM_TYPE_SELECTOR_DYNAMIC 时， 格式为 "<name_field>,<id_field>@<api_path>"
  //  * 接口使用GET方式进行调用， 并返回json格式结果
  //  * 返回值最外层是一个数组, e.g.  [{"id":"id1","name":"name1"},{"id":"id2","name":"name2"}]
  //  * <name_field> 和 <id_field> 表明数组中每个item如何提取其 id与name
  //  * 完成 name, id 提取后，处理逻辑同 PARAM_TYPE_SELECTOR
  string datasource   =  8;
  string precondition =  9; // precondition 为当前属性展示的前置条件, 用来实现简单的动态依赖功能
  bool   required     = 10; // required 表示该属性是否为必填项
  bool   multiple     = 11; // multiple 表示是否支持多选(下拉框), 列表(输入), Map(K,V)
  
  //@deprecated
  //  bool readonly = 12;         // readonly 表示该动态参数是否只读
  int64  maxlen      = 13; // maxlen 表示当Multiple为true时, 可选择的最大数量
  string placeholder = 14; // placeholder 输入框在用户填写前的提示信息
  
  //@deprecated
  //  bool show = 15;             // show 是否在前端进行展示
  bool  advanced  = 16; // advanced 是否仅在额外的弹窗中进行编辑， e.g. 一个高级配置界面，通过配置按钮触发
  bool  multiline = 17; // 是否点击后弹出多行编辑框
  bool  hidden    = 18; // hidden 是否编排页面隐藏
  int32 precision = 19; // 精度 表示小数点后几位 e.g. 1表示小数点后一位
  bool  disabled  = 20; // 是否可编辑，替换原先的readonly属性
  
  // comp_props 用于控制前端的组件支持的属性, 可灵活进行配置, 格式为json string
  // 具体每种类型的表单组件支持的具体可配置属性,可以参考前端框架文档,或者咨询相关同事.
  // 以文本输入框(TYPE_INPUT)为例: 可参考 https://ant-design.antgroup.com/components/input-cn#input
  // 部分可配参数如下:
  //    参数	          说明	                      类型	        默认值
  //    allowClear	  可以点击清除图标删除内容	    boolean   	false
  //    showCount	    是否展示字数	              boolean     false
  //    disabled	    是否禁用状态，默认为 false	  boolean	    false
  //    maxLength	    最大长度	                  number	    -
  // e.g.
  // - 控制输入框的最大文本长度, 展示字数统计, 并且允许清空 -> "{\"maxLength\": 1024, \"showCount\":true, \"allowClear\": true }"
  string comp_props = 50;
}

// NumberRange 数字的取值范围
message NumberRange {
  float min = 1;
  float max = 2;
  
  // step 表示数值类型参数每次修改的调整步长
  // 即当 data_type 为 number 时, 前端组件每次 +- 的数值变动量
  // 默认为 1.0, 当 data_type 类型为Int时，step将四舍五入忽略小数部分
  float step = 3;
}

// DynamicParamValues 动态参数值
message DynamicParamValues {
  
  // values 定义了所有填写的动态参数的 id -> value 的映射关系
  // 其中:
  // * id     即为对应的 DynamicParam.id
  // * value  即为最终获取到的动态参数值，支持多种参数类型:
  //   * 数值     "3.14"
  //   * 字符串   "abcd"
  //   * 数组     '["item1","item2","item3"]'
  //   * Map     '{"key1":"value1","key2":"value2"}'
  map <string,string> values = 1;
}

// DynamicSelectRange 动态选择范围，支持多级菜单
// 最终用户选择的多级的值之间使用 @@ 进行分割， e.g. 三级菜单最终设置的值为： l1_value@@l2_value@@l3_value
message DynamicSelectRange {
           int64             layer = 1; // 该选项对应的多级菜单的层级数量
  repeated DynamicSelectItem items = 2; // 第一层菜单的子选项范围
}

// DynamicSelectItem 动态选择的可选项，可包括下一级的子选项
message DynamicSelectItem {
           string            id        = 1; // 可选项的ID, 用于标识用户最终参数选择的值
           string            name      = 2; // 可选项的名称, 用于展示相对用户友好的名称
           string            desc      = 3; // 可选项的描述, 用于Hover时展示额外的一些描述信息
  repeated DynamicSelectItem sub_items = 4; // 该选项的下一级子选项的可选范围
}

