syntax = "proto3";

package proto;

import "proto/knowledge_base.proto";
import "proto/data_connection.proto";
import "proto/common.proto";
import "proto/serving/mlops_service.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service KnowledgeBaseManager {
  rpc ListKnowledgeBases   (ListKnowledgeBasesReq  ) returns (ListKnowledgeBasesRsp  );
  rpc GetKnowledgeBase     (GetKnowledgeBaseReq    ) returns (GetKnowledgeBaseRsp    );
  rpc CreateKnowledgeBase  (CreateKnowledgeBaseReq ) returns (CreateKnowledgeBaseRsp );
  rpc UpdateKnowledgeBase  (UpdateKnowledgeBaseReq ) returns (UpdateKnowledgeBaseRsp );
  rpc DeleteKnowledgeBases (DeleteKnowledgeBasesReq) returns (DeleteKnowledgeBasesRsp);
  
  // 获取数据连接的库表列表, 包含表描述信息
  rpc ListConnectionTables (ListConnectionTablesReq) returns (ListConnectionTablesRsp);

  // // 从数据连接导入-库表预览，获取表结构信息
  // rpc GetTableSchema (GetTableSchemaReq) returns (GetTableSchemaRsp);
  
  // 获取知识库的文档列表，平铺形式
  rpc ListDocuments (ListDocumentsReq) returns (ListDocumentsRsp);
  
  // 启用/禁用 知识库文档
  rpc DisableDocument (DisableDocumentReq) returns (DisableDocumentRsp);
  
  // 获取知识库文档的分段内容列表，支持排序
  rpc ListDocumentChunks (ListDocumentChunksReq) returns (ListDocumentChunksRsp);
  
  // 知识库检索接口
  rpc RetrieveKnowledgeBase (RetrieveKnowledgeBaseReq) returns (RetrieveKnowledgeBaseRsp);
  
  // 获取知识库的文档列表，文档树形式
  rpc GetDocumentTree (GetDocumentTreeReq) returns (GetDocumentTreeRsp);
  
  // 新增文档到知识库(异步处理),允许自动创建知识库
  rpc SubmitFileToKnowledgeBase (SubmitFileToKnowledgeBaseReq) returns (SubmitFileToKnowledgeBaseRsp);
  
  // 从知识库移除文档
  rpc RemoveFileFromKnowledgeBase (RemoveFileFromKnowledgeBaseReq) returns (RemoveFileFromKnowledgeBaseRsp);
  
  // 更新知识库状态(可见、可检索)
  rpc UpdateKnowledgeBaseState (UpdateKnowledgeBaseStateReq) returns (UpdateKnowledgeBaseStateRsp);
  
  // 手动新建切片
  rpc CreateChunk (CreateChunkReq) returns (CreateChunkRsp);
  
  // 删除切片
  rpc DeleteChunk (DeleteChunkReq) returns (DeleteChunkRsp);
  
  // 更新切片
  rpc UpdateChunk (UpdateChunkReq) returns (UpdateChunkRsp);
  
  // 预览文档加工效果(分段预览)
  rpc PreviewDocumentProcess (PreviewDocumentProcessReq) returns (DocSvcLoadChunkRsp);
  
  // 新增文档到知识库(同步处理)，可指定文档处理配置或按照知识库默认值，可批量处理，处理中推送进度
  rpc SyncSubmitFilesToKnowledgeBase (SyncSubmitFilesToKnowledgeBaseReq) returns (stream SyncSubmitFilesToKnowledgeBaseRsp);
  
  // 跨知识库检索
  rpc RetrieveCrossKnowledgeBase (RetrieveCrossKnowledgeBaseReq) returns (RetrieveCrossKnowledgeBaseRsp);
  
  // 提交分段到知识库(chunks落库)
  rpc SubmitChunksToKnowledgeBase (SubmitChunksToKnowledgeBaseReq) returns (SubmitChunksToKnowledgeBaseRsp);
  
  // 判断文档文件是否已经存在于知识库(根据文件MD5)
  rpc IsDocumentExistent (IsDocumentExistentReq) returns (IsDocumentExistentRsp);
  
  // 统计接口
  rpc CollectKnowledgeBaseStats (CollectKnowledgeBaseStatsReq) returns (CollectKnowledgeBaseStatsRsp);
  
  // 共享知识库到公共空间
  rpc ShareKnowledgeBase (ShareKnowledgeBaseReq) returns (ShareKnowledgeBaseRsp);
  
  // 发布知识库服务
  rpc PublishKnowledgeBase (PublishKnowledgeBaseReq) returns (PublishKnowledgeBaseRsp);
  
  // 溯源文档元素
  rpc TraceDocElements (TraceDocElementsReq) returns (TraceDocElementsRsp);
  
  // 重试文档处理落库的流程
  rpc RetryDocumentProcess (RetryDocumentProcessReq) returns (RetryDocumentProcessRsp);
}
message AppRelation {
  string app_id   = 1;
  string app_name = 2;
}

message HealthStatus {
  bool   healthy = 1;
  string message = 2;
}

// 知识库的各项健康状态
message KnowledgeBaseHealthOverview {
  bool         healthy                =  1;
  HealthStatus doc_svc_health         =  2;
  HealthStatus hippo_health           =  3;
  HealthStatus scope_health           =  4;
  HealthStatus embedding_health       =  5;
  HealthStatus rerank_health          =  6;
  HealthStatus augment_health         =  7;
  HealthStatus custom_strategy_health =  8;
  HealthStatus image_augment_health   =  9;
  HealthStatus ocr_layout_health      = 10;
  HealthStatus vl_health              = 11;
}

message KnowledgeBaseInfo {
           KnowledgeBase                 knowledge_base                = 1;
           int32                         num_docs                      = 2;
           int32                         num_apps                      = 3;
  repeated AppRelation                   app_relations                 = 4;
  repeated KnowledgeBaseRetrieveStrategy supported_retrieve_strategies = 5;
           KnowledgeBaseHealthOverview   health                        = 6;
}

message ListKnowledgeBasesReq {
  UserContext                user_context  = 1;
  ListKnowledgeBasesSelector list_selector = 2;
  
  // @gotags: description:"是否查询公共空间；查询公共空间时，额外查询所有项目的公共知识库，否则只查询当前项目下的知识库"
  bool is_public = 3;
}

message ListKnowledgeBasesSelector {
  repeated KnowledgeBaseContentType  content_types  = 1;
  repeated KnowledgeBaseSourceType   source_types   = 2;
  repeated KnowledgeBaseRegistryType registry_types = 3;
  repeated bool                      is_published   = 4;
  repeated KnowledgeBaseSceneType    scene_types    = 5;
}

message ListKnowledgeBasesRsp {
  repeated KnowledgeBaseInfo result = 1;
}

message GetKnowledgeBaseReq {
  UserContext user_context = 1;
  string      id           = 2;
}

message GetKnowledgeBaseRsp {
  KnowledgeBaseInfo result = 1;
}

message CreateKnowledgeBaseReq {
  UserContext   user_context = 1;
  KnowledgeBase base         = 2;
}

message CreateKnowledgeBaseRsp {
  KnowledgeBase result = 1;
}

message UpdateKnowledgeBaseReq {
  UserContext   user_context = 1;
  KnowledgeBase base         = 2;
}

message UpdateKnowledgeBaseRsp {
  KnowledgeBase result = 1;
}

message DeleteKnowledgeBasesReq {
           UserContext user_context = 1;
  repeated string      ids          = 2; // 可同时删除多个
}

message DeleteKnowledgeBasesRsp {}

message RetrieveKnowledgeBaseReq {
  UserContext user_context = 1;
  
  // @gotags: description:"知识库id"
  string knowledge_base_id = 2;
  
  // @gotags: description:"查询内容"
  string query = 3;
  
  // @gotags: description:"检索的文档范围，文档id列表"
  repeated string doc_range = 4;
  
  // @gotags: description:"检索配置"
  RetrievalConfig retrieval_config = 5;
  
  // @gotags: description:"额外的开关，是否关闭Rerank"
  bool disable_rerank = 6;
}

message Hit {
  
  // @gotags: description:"是否命中"
  bool hit = 1;
  
  // @gotags: description:"分数"
  float score = 2;
}

message RetrieveKnowledgeBaseRsp {
  
  // @gotags: description:"原检索请求"
  RetrieveKnowledgeBaseReq request = 1;
  
  // @gotags: description:"分段结果列表"
  repeated ChunkRetrieveResult result = 2;
  
  // @gotags: description:"向量索引召回命中情况, key为chunk id, key不存在表示未命中"
  map <string,Hit> vector_index_hits = 3;
  
  // @gotags: description:"全文索引召回命中情况, key为chunk id, key不存在表示未命中"
  map <string,Hit> full_text_index_hits = 4;
}

message ChunkRetrieveResult {
  Chunk chunk = 1;
  
  // @gotags: description:"召回：原文与相关增强chunk命中里的最高score；重排序：原文score"
  float  score               = 2;
  string knowledge_base_id   = 4;
  string doc_id              = 5;
  string knowledge_base_name = 6;
  string doc_name            = 7;
  
  // @gotags: description:"召回：chunk的前文切片"
  repeated Chunk above_chunks = 8;
  
  // @gotags: description:"召回：chunk的后文切片"
  repeated Chunk later_chunks = 9;
}

message DocumentInfo {
  
  // @gotags: description:"文档"
  Document doc = 1;
  
  // @gotags: description:"召回次数（暂不实现）"
  int64 num_recall = 2;
  
  // @gotags: description:"文档段落数"
  int64 num_chunks = 3;
  
  // @gotags: description:"文档处理进度"
  DocumentProcessingProgress prog = 4;
  
  // @gotags: description:"文档处理策略"
  StrategyOrigin strategy_origin = 5;
}

message ListDocumentsReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
}

message ListDocumentsRsp {
  repeated DocumentInfo result = 1;
}

message DisableDocumentReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  string      doc_id            = 3;
  
  // @gotags: description:"调整知识库文档的启/禁用状态，调整后该文档是否被禁用"
  bool disabled = 4;
}

message DisableDocumentRsp {}

message ListDocumentChunksReq {
           UserContext     user_context         = 1;
           string          knowledge_base_id    = 2;
           string          doc_id               = 3;
           PageReq         page_req             = 4;
  repeated ChunkSourceType source_type_selector = 5;
           string          search_content       = 6;
}

message ListDocumentChunksRsp {
  repeated ChunkInfo result    = 1;
           int32     total     = 2;
           int32     page_num  = 3;
           int32     page_size = 4;
}

message ListConnectionTablesReq {
  UserContext    user_context = 1;
  DataConnection connection   = 2;
}

message ListConnectionTablesRsp {
  repeated TableInfo tables     = 1;
  repeated string    es_indices = 2;
}

// message GetTableSchemaReq {
//   UserContext user_context  = 1;
//   string      connection_id = 2;
//   string      table_name    = 3;
// }
// message GetTableSchemaRsp {
//   string schema = 1;
// }
message GetDocumentTreeReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
}

message GetDocumentTreeRsp {
  
  // http://*************:9999/infinity/base/doc.html#/default/%E7%9F%A5%E8%AF%86%E5%BA%93%E6%96%87%E6%A1%A3%E6%93%8D%E4%BD%9C/getFullTreeByIdUsingPOST
  DocumentTree tree = 1;
}

message SubmitFileToKnowledgeBaseReq {
  UserContext         user_context      = 1;
  FileOperationSource submission_source = 2;
  
  // @gotags: description:"知识库id，若id对应的知识库不存在，则先自动创建临时知识库（可检索但不可见）"
  string knowledge_base_id = 3;
  
  // @gotags: description:"文件上传后的路径"
  string file_path = 4;
  
  // @gotags: description:"是否自动创建知识库"
  bool auto_create_kb = 5;
  
  // @gotags: description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"
  DocProcessingConfig doc_processing_config = 6;
  
  // @gotags: description:"仅表格知识库指定"
  TableConfig table_config = 7;
  
  // @gotags: description:"语料来源配置；仅来源为语料时使用"
  CorpusConfig corpus_config = 8;
}

message SubmitFileToKnowledgeBaseRsp {
  Document doc = 1;
}

message RemoveFileFromKnowledgeBaseReq {
  UserContext         user_context      = 1;
  FileOperationSource submission_source = 2;
  
  // @gotags: description:"知识库id，用于从知识库删除文件"
  string knowledge_base_id = 3;
  
  // @gotags: description:"文档id"
  string doc_id = 4;
}

message RemoveFileFromKnowledgeBaseRsp {}


// 文件上传或删除等操作来源
enum FileOperationSource {
  
  // agent配置页面-本地文件上传
  AGENT_CONFIG = 0;
  
  // 知识库管理
  KB_MANAGE = 1;
}
enum DocumentNodeCategory {
  
  // 目录
  DIR = 0;
  
  // 文件
  FILE = 1;
}
message DocumentNode {
  DocumentNodeCategory category = 1;
  string               id       = 2;
  string               name     = 3;
}

message DocumentTree {
           DocumentNode node     = 1;
  repeated DocumentTree children = 2;
}

message UpdateKnowledgeBaseStateReq {
  UserContext user_context   = 1;
  string      id             = 2;
  bool        is_visible     = 3;
  bool        is_retrievable = 4;
}

message UpdateKnowledgeBaseStateRsp {
  KnowledgeBase knowledge_base = 1;
}

message CreateChunkReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  string      doc_id            = 3;
  string      ori_chunk_id      = 4;
  string      content           = 5;
  
  // @gotags: description:"chunk原文内容类型，目前仅支持原文文本和原文图像"
  OriginalContentType content_type = 6;
}

message CreateChunkRsp {
  Chunk chunk = 1;
}

message DeleteChunkReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  string      doc_id            = 3;
  string      chunk_id          = 4;
  
  // @gotags: description:"原文chunk id, 删除原文chunk id时chunk_id与ori_chunk_id一致"
  string ori_chunk_id = 5;
}

message DeleteChunkRsp {
  
  // @gotags: description:"删除增强切片时返回，删除原文切片则为空"
  Chunk chunk = 1;
}

message UpdateChunkReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  string      doc_id            = 3;
  string      chunk_id          = 4;
  
  // @gotags: description:"更新原文切片时该值与chunk_id一致；更新增强切片时为所属的原文切片的chunk_id"
  string ori_chunk_id = 5;
  string content      = 6;
}

message UpdateChunkRsp {
  Chunk chunk = 1;
}

message PreviewDocumentProcessReq {
  UserContext         user_context          = 1;
  string              file_path             = 2;
  DocProcessingConfig doc_processing_config = 3;
}

message SyncSubmitFilesToKnowledgeBaseReq {
  UserContext         user_context      = 1;
  FileOperationSource submission_source = 2;
  
  // @gotags: description:"知识库id，若id对应的知识库不存在，则根据auto_create_kb确定是否自动创建临时知识库（可检索但不可见）"
  string knowledge_base_id = 3;
  
  // @gotags: description:"文件路径列表, 支持批量上传多个文件；仅本地来源的数据使用"
  repeated string file_paths = 4;
  
  // @gotags: description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"
  DocProcessingConfig doc_processing_config = 5;
  
  // @gotags: description:"是否自动创建知识库"
  bool auto_create_kb = 6;
  
  // @gotags: description:"表格配置, 仅表格知识库指定"
  TableConfig table_config = 7;
  
  // @gotags: description:"数据来源"
  DocumentFileSource document_file_source = 8;
  
  // @gotags: description:"语料来源配置；仅来源为语料时使用"
  CorpusConfig corpus_config = 9;
}

enum DocumentTaskStage {
  
  // @gotags: description:"排队中"
  PENDING = 0;
  
  // @gotags: description:"文档解析分段"
  LOAD_CHUNK = 11;
  
  // @gotags: description:"知识增强"
  KNOWLEDGE_AUGMENT = 12;
  
  // @gotags: description:"数据存储"
  DATA_STORE = 2;
  
  // @gotags: description:"索引构建"
  INDEX_BUILD = 3;
  
  // @gotags: description:"完成"
  DONE = 4;
}
message DocumentProcessingProgress {
  
  // @gotags: description:"文档"
  Document document = 1;
  
  // @gotags: description:"进度百分比" json:"percentage"
  float percentage = 2;
  
  // @gotags: description:"是否完成；完成且进度不为100%表示处理错误" json:"finished"
  bool finished = 3;
  
  // @gotags: description:"错误消息"
  string error_message = 4;
  
  // @gotags: description:"当前处理阶段" json:"stage"
  DocumentTaskStage stage = 5;
}

message SyncSubmitFilesToKnowledgeBaseRsp {
  
  // @gotags: json:"done"
  bool done = 1;
  
  // @gotags: json:"num_succeeded"
  int32 num_succeeded = 2;
  
  // @gotags: json:"num_failed"
  int32 num_failed = 3;
  
  // @gotags: json:"total"
  int32 total = 4;
  
  // @gotags: json:"documents"
  repeated DocumentProcessingProgress documents = 5;
}

message RetrieveRange {
           string          knowledge_base_id = 1;
  repeated string          doc_range         = 2;
           RetrievalConfig retrieval_config  = 3;
}

message RetrieveCrossKnowledgeBaseReq {
           UserContext   user_context   = 1;
           string        query          = 2;
  repeated RetrieveRange ranges         = 3;
           RerankParams  rerank_params  = 4;
           bool          disable_rerank = 5;
  
  // @gotags: description:"是否为istio模式,留下审计记录"
  bool istio_mode = 7;
}

message RetrieveCrossKnowledgeBaseRsp {
  RetrieveCrossKnowledgeBaseReq request = 1;
  
  // @gotags: description:"分段结果列表"
  repeated ChunkRetrieveResult result = 2;
}

message SubmitChunksToKnowledgeBaseReq {
           UserContext user_context      = 1;
           string      knowledge_base_id = 2;
           string      file_path         = 3;
           string      file_base64       = 4;
  repeated Chunk       chunks            = 5;
  repeated DocElement  elements          = 6;
}

message SubmitChunksToKnowledgeBaseRsp {
  Document document = 1;
}

message IsDocumentExistentReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  string      file_path         = 3;
  string      file_base64       = 4;
}

message IsDocumentExistentRsp {
  bool     existent = 1;
  Document document = 2;
}

message DocSvcLoadChunkRsp {
  repeated Chunk      chunks   = 1;
  repeated DocElement elements = 2;
}

message CollectKnowledgeBaseStatsReq {
  UserContext user_context = 1;
}

message CollectKnowledgeBaseStatsRsp {
  int64 num_knowledge_bases = 1;
  int64 num_docs            = 2;
  int64 num_chunks          = 3;
  int64 nums_aug_chunks     = 4;
}

message ShareKnowledgeBaseReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  
  // @gotags: description:"是否共享到公共空间"
  bool is_public = 3;
}

message ShareKnowledgeBaseRsp {}

message PublishKnowledgeBaseReq {
  string            id          = 1;
  string            name        = 2;
  string            desc        = 3;
  serving.RateLimit rate_limit  = 4;
  bool              is_security = 5;
  UserContext       ctx         = 6;
}

message PublishKnowledgeBaseRsp {
  string id              = 1;
  string virtual_svc_url = 2;
}

message TraceDocElementsReq {
  UserContext user_context = 1;
  string      chunk_id     = 2;
}

message TraceDocElementsRsp {
  repeated DocElement elements = 1;
}

message RetryDocumentProcessReq {
  UserContext user_context      = 1;
  string      knowledge_base_id = 2;
  
  // @gotags: description:"文档id， 为空时自动重试知识库的所有失败文档"
  repeated string doc_ids = 3;
}

message RetryDocumentProcessRsp {}

