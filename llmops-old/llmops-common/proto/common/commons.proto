syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/common;common";

package commons;

message DeleteRsp {}

message EmptyReq {}

message EmptyRsp {}

message StringList {
  repeated string items = 1;
}

message PageReq {
  int32  pageSize = 1;
  int32  page     = 2;
  string orderBy  = 3;
  bool   isDesc   = 4;
}

message ResultMsg {
  string result        = 1;
  string errMsg        = 2;
  int32  state         = 3;
  string callbackUrl   = 4;
  string callbackParam = 5;
}

message IdReq {
  string id      = 1;
  string modelId = 2;
}

message BillingConfig {
  
  // @gotags: description:"计费标准，单位元每千tokens"
  float price_per_thousand_tokens = 1;
  
  // @gotags: description:"计费标准，单位元每次"
  float price_per_request = 2;

  enum Type {
    NIL        = 0;
    BY_TOKEN   = 1;
    BY_REQUEST = 2;
  }
  
  // @gotags: description:"计费类型 1: 按token计费 2: 按次计费"
  Type type = 3;
}

message DashboardChart {
  message Frame {
    
    //@gotags:description:"点数据"
    repeated Point points = 1;
  }

  message Point {
    
    //@gotags:description:"时间"
    string time = 1;
    
    //@gotags:description:"值"
    float value = 2;
  }

  
  //@gotags:description:"点线图数据"
  map <string,Frame> frames = 1;
}

enum HttpMethod {
  HTTP_METHOD_POST = 0;
  HTTP_METHOD_GET  = 1;
}
enum EndpointType {
  ENDPOINT_TYPE_HTTP  = 0;
  ENDPOINT_TYPE_GRPC  = 1;
  ENDPOINT_TYPE_HTTPS = 2;
  ENDPOINT_TYPE_TCP   = 3;
  ENDPOINT_TYPE_UDP   = 4;
}
enum APIType {
  API_TYPE_OTHERS             = 0;
  API_TYPE_MODEL_INFER        = 1;
  API_TYPE_PROMETHEUS_METRICS = 2;
  API_TYPE_WEB_APP            = 3;
  API_TYPE_HEALTH_CHECK       = 4;
}
message Endpoint {
  
  // @gotags: description:"端口"
  uint32 port = 1;
  
  // @gotags: description:"类型，http，grpc等"
  commons.EndpointType type = 2;
  
  // @gotags: description:"api与相关属性, api_path -> 相关属性"
  repeated APIAttr api_attrs = 3;
  
  // @gotags: description:"默认的endpoint"
  bool is_default = 4;
}

message APIAttr {
  
  // @gotags: description:"端口对应的API"
  string api_path = 1;
  
  // @gotags: description:"url对应的请求示例"
  string req_example = 2;
  
  // @gotags: description:"url对应method，支持GET/POST，默认POST"
  commons.HttpMethod method = 3;
  
  // @gotags: description:"header请求头"
  map <string,string> headers = 7;
  
  // @gotags: description:"api类型:模型推理 指标 健康检查 网页应用"
  commons.APIType api_type = 8;
  
  // @gotags: description:"推理接口的说明"
  ModelInferSpec infer_spec = 9;
}

message ModelInferSpec {
  
  // @gotags: description:"api功能,使用的是ModelKind与ModelSubkind的定义，但是因为循环引用的问题，这里使用枚举字符串
  string kind = 5;
  
  // @gotags: description:"api功能,使用的是ModelKind与ModelSubkind的定义，但是因为循环引用的问题，这里使用枚举字符串
  string sub_kind = 6;
  
  // @gotags: description:"api所符合的接口规范说明"
  APISpec spec = 4;
}

enum APISpec {
  API_SPEC_OTHERS    = 0;
  API_SPEC_OPENAI    = 1;
  API_SPEC_TRANSWARP = 3;
}
message NameUniqueReq {
  
  // @gotags: description:"判断名字是否唯一：名字"
  string name = 1;
}

message NameUniqueRes {
  
  // @gotags: description:"判断名字是否唯一"
  bool isUnique = 1;
}

