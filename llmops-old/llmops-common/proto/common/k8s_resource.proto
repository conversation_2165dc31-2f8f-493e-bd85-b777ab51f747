syntax = "proto3";

package common;

option go_package = "transwarp.io/aip/llmops-common/pb/common;common";

message Pod {
  
  // @gotags: description:"容器"
  repeated Container containers = 1;
  
  // @gotags: description:"持久卷配置"
  repeated VolumeCfg volume = 2;
}

message Container {
  
  // @gotags: description:"镜像地址"
  string image = 1;
  
  // @gotags: description:"容器类型，0主容器，1sidecar容器"
  ContainerType container_type = 2;
  
  // @gotags: description:"资源限制, Deprecated: 在 V2 接口不再使用"
  ResourceRequirements resource_requirements = 3;
  
  // @gotags: description:"环境变量"
  map <string,string> envs = 5;
  
  // @gotags: description:"启动命令，需要按照空格拆分成数组，注意引号包裹的空格无需拆分"
  repeated string cmds = 6;
  
  // @gotags: description:"挂载配置"
  repeated MountPath mount_paths = 7;
  
  // @gotags: description:"暴露端口，如[80,81]，同时开启80与81的tcp/udp端口"
  repeated int32 ports = 8;
  
  // @gotags: description:"容器启动参数，需要按照空格拆分成数组，注意引号包裹的空格无需拆分"
  repeated string args = 9;

  enum ImagePullPolicy {
    PullAlways       = 0;
    PullNever        = 1;
    PullIfNotPresent = 2;
  }
  
  // @gotags: description:"镜像拉取策略，PullAlways = 0;PullNever = 1;PullIfNotPresent = 2;默认PullAlways
  ImagePullPolicy image_pull_policy = 10;
  
  // @gotags: description:"容器安全策略"
  SecurityContext security_context = 11;
  
  // @gotags: description:"容器名称，非必填"
  string name = 12;
  
  // @gotags: description:"describes actions that the management system should take in response to container lifecycle events."
  Lifecycle lifecycle = 13;
  
  // @gotags: description:"实例规格id"
  optional int32 resource_id    = 14;
           Probe livenessProbe  = 15;
           Probe readinessProbe = 16;
}

message Probe {
  Handler handler             = 1;
  int32   initialDelaySeconds = 2;
  int32   TimeoutSeconds      = 3;
  int32   PeriodSeconds       = 4;
  int32   SuccessThreshold    = 5;
  int32   FailureThreshold    = 6;
}

message Lifecycle {
  
  // @gotags: description:"describes actions that the management system should take in response to container lifecycle events."
  Handler post_start = 1;
  
  // @gotags: description:"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure"
  Handler pre_stop = 2;
}

// One and only one of the following should be specified.
message Handler {
  
  // @gotags: description:"Exec specifies the action to take."
  ExecAction exec = 1;
  
  // @gotags: description:"HTTPGet specifies the http request to perform."
  HTTPGetAction http_get = 2;
  
  // @gotags: description:"TCPSocket specifies an action involving a TCP port."
  TCPSocketAction tcp_socket = 3;
}

message ExecAction {
  repeated string command = 1;
}

message HTTPGetAction {
  
  // @gotags: description:"Path to access on the HTTP server."
  string path = 1;
  
  // @gotags: description:"Name or number of the port to access on the container."
  IntOrString port = 2;
  
  // @gotags: description:"Host name to connect to, defaults to the pod IP. You probably want to set"
  string host = 3;
  
  // @gotags: description:"Scheme to use for connecting to the host."
  URIScheme scheme = 4;
  
  // @gotags: description:"Custom headers to set in the request. HTTP allows repeated headers."
  repeated HTTPHeader http_headers = 5;
}

enum URIScheme {
  HTTP  = 0;
  HTTPS = 1;
}
message HTTPHeader {
  
  // @gotags: description:"The header field name"
  string name = 1;
  
  // @gotags: description:"The header field value"
  string value = 2;
}

message IntOrString {
  enum Type {
    int    = 0;
    string = 1;
  }
  Type   type    = 1;
  int32  int_val = 2;
  string str_val = 3;
}

message TCPSocketAction {
  
  // @gotags: description:"Number or name of the port to access on the container."
  IntOrString port = 1;
  
  // @gotags: description:"Optional: Host name to connect to, defaults to the pod IP."
  string host = 2;
}

message ResourceRequirements {
  
  // @gotags: description:"资源限制"
  Resource resource_limit = 3;
  
  // @gotags: description:"资源限制"
  Resource resource_request = 4;
  
  // @gotags: description:"算力卡，可选 Ascend/Nvidia"
  string gpuType = 16;
  
  // @gotags: description:"指定的gpu卡号"
  repeated string gpuCards = 17;
  
  // @gotags: description:"gpuType=Ascend时使用: ascend配置"
  AscendResourceCfg ascendConfig = 11;
}

message AscendResourceCfg {
  
  // @gotags: description:"npu_name名字"
  string npu_name = 1;
  
  // @gotags: description:"ascend模板名字"
  string template_name = 2;
  
  // @gotags: description:"数量，当template为exclusive的时候，需要填，默认1"
  int64 cnt = 3;
}

message SecurityContext {
  
  // @gotags: description:"开启privileged，默认false"
  bool privileged = 1;
  
  // @gotags: description:"terminal用户 id"
  int64 run_as_user = 2;
}

enum ContainerType {
  
  // @gotags: description:"主容器"
  CONTAINER_TYPE_MAIN = 0;
  
  // @gotags: description:"sidecar"
  CONTAINER_TYPE_SIDECAR = 1;
}
message Resource {
  
  // @gotags: description:"cpu限制，单位为millicore（毫核）"
  int32 cpu = 1;
  
  // @gotags: description:"内存限制， 单位为MiB（Mebibyte，兆字节）"
  int32 memory = 2;
  
  // @gotags: description:"gpu算力，单位 1%核"
  int32 gpu_core = 3;
  
  // @gotags: description:"gpu内存， 单位为MiB（Mebibyte，兆字节）"
  int32 gpu_memory = 4;
  
  // @gotags: description:"gpu数量"
  int32 gpu_count = 5;
}

enum MLOpsVolumeType {
  
  // @gotags: description:"hostpath"
  MLOPS_File_TYPE_HOST_PATH = 0;
  
  // @gotags: description:"文件系统（底层是pvc）"
  MLOPS_File_TYPE_FILE_SYSTEM = 1;
  
  // @gotags: description:"模型仓库（底层是pvc）"
  MLOPS_File_TYPE_MODEL_CUBE = 2;
  
  // @gotags: description:"样本仓库（底层是pvc）"
  MLOPS_File_TYPE_SAMPLE_CUBE = 3;
  
  // @gotags: description:"内存挂载"
  MLOPS_File_TYPE_MEMORY = 4;
  
  // @gotags: description:"sfs(底层是sfs-pvc)"
  MLOPS_File_TYPE_SFS = 5;
  
  // @gotags: description:"pvc"
  MLOPS_File_TYPE_PVC = 6;
  
  // @gotags: description:"config map"
  MLOPS_File_TYPE_CONFIG_MAP = 7;
  
  // @gotags: description:"empty dir"
  MLOPS_File_TYPE_EMPTYDIR = 8;
}

// enum MLOpsSFSFileType {
//   // @gotags: description:"文件系统（底层是pvc）"
//   MLOPS_SFS_File_TYPE_FILE_SYSTEM = 0;
//   // @gotags: description:"模型仓库（底层是pvc）"
//   MLOPS_SFS_File_TYPE_MODEL_CUBE = 1;
//   // @gotags: description:"样本仓库（底层是pvc）"
//   MLOPS_SFS_File_TYPE_SAMPLE_CUBE = 2;
// }
//
// message SfsVolumeCfg {
//   // @gotags:description:"如果持久卷类型为sfs，需要填此项，0文件系统，1模型仓库，2样本仓库"
//   MLOpsSFSFileType sfs_type =2;
// }

message PvcVolumeCfg {
  
  // @gotags: description:"pvc名字"
  string pvcName = 1;
}

message HostPathVolumeCfg {
  
  // @gotags: description:"hostpath路径"
  string path = 1;
}

message KeyPathCfg {
  string key  = 1;
  string path = 2;
}

message EmptyDirVolumeCfg {
  
  // @gotags: description:"emptydir的medium:如Memory"
  string medium = 1;
}

message ConfigMapVolumeCfg {
  
  // @gotags: description:"cm名字"
  string name = 1;
  
  // @gotags: description:"key path配置"
  repeated KeyPathCfg keyPaths = 2;
  
  // @gotags: description:"默认权限，如0644，非必填"
  optional int32 defaultMode = 3;
  
  // @gotags: description:"config map的默认值"
  optional string default_content_value = 4;
}

message VolumeCfg {
  
  // @gotags: description:"持久卷名称"
  string name = 1;
  
  // @gotags: description:"持久卷类型,0hostpath,1sfs，2pvc"
  MLOpsVolumeType volume_type = 2;
  
  // @gotags: description:"sfs配置(底层是sfs-pvc)"
  //  optional SfsVolumeCfg sfs_cfg = 3;
  // @gotags: description:"pvc配置"
  optional PvcVolumeCfg pvc_cfg = 4;
  
  // @gotags: description:"empty dir"
  optional EmptyDirVolumeCfg empty_dir_cfg = 5;
  
  // @gotags: description:"config map配置"
  optional ConfigMapVolumeCfg config_map_cfg = 6;
  
  // @gotags: description:"host path配置"
  optional HostPathVolumeCfg hostpath_cfg = 7;
}

message MountPath {
  
  // @gotags: description:"卷名字"
  string volume_name = 1;
  
  // @gotags: description:"挂载路径"
  string mount_path = 2;
  
  // @gotags: description:"挂载路径"
  string sub_path = 3;
  
  // @gotags: description:"是否只读"
  bool read_only = 4;
}

message PodEvent {
  string type            = 1;
  string reason          = 2;
  int64  first_timestamp = 3;
  int64  last_timestamp  = 4;
  int32  count           = 5;
  string message         = 6;
  string from            = 7;
}

