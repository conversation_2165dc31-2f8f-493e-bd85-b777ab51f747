syntax = "proto3";

package common;

option go_package = "transwarp.io/aip/llmops-common/pb/common;common";

import "proto/common/k8s_resource.proto";
import "proto/common/commons.proto";

message MLOpsServiceTemplate {
  
  // @gotags: description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、11自定义镜像"
  SourceType source_type = 1;
  
  // @gotags: description:"来源ID，如应用链ID，模型版本ID"
  string source_id = 2;
  
  // @gotags: description:"名字"
  optional string name = 3;
  
  // @gotags: description:"描述"
  optional string desc = 4;
  
  // @gotags: description:"容器,至少有一个"
  repeated Container containers = 5;
  
  // @gotags: description:"api列表"
  repeated API apis = 6;
  
  // @gotags: description:"持久卷配置"
  VolumeCfg volume_cfg = 7;
}

message API {
  
  // @gotags: description:"端口"
  uint32 port = 1;
  
  // @gotags: description:"API类型，http，grpc等"
  MLOpsPortType type = 2;
  
  // @gotags: description:"端口对应的API"
  repeated string url = 3;
}

enum MLOpsPortType {
  
  // @gotags: description:"http"
  MLOPS_PORT_TYPE_HTTP = 0;
  
  // @gotags: description:"grpc"
  MLOPS_PORT_TYPE_GRPC = 1;
  
  // @gotags: description:"未知"
  MLOPS_PORT_TYPE_UNKNOW = 11;
}
enum SourceType {
  
  // @gotags: description:"未知"
  SOURCE_TYPE_UNKNOW = 0;
  
  // @gotags: description:"模型仓库"
  SOURCE_TYPE_MODEL_CUBE = 1;
  
  // @gotags: description:"应用仓库"
  SOURCE_TYPE_APP_CUBE = 2;
  
  // @gotags: description:"vlab"
  SOURCE_TYPE_VLAB = 3;
  
  // @gotags: description:"自定义镜像"
  SOURCE_TYPE_CUSTOM = 11;
}
// DeployTemplate 服务部署临时使用
message DeployTemplate {
           string              image                = 1;
           map <string,string> envs                 = 2;
  repeated commons.Endpoint    endpoints            = 3;
           map <string,string> annotations          = 4;
  repeated string              args                 = 5;
  repeated Mount               mounts               = 6;
           HTTPReadinessProbe  http_readiness_probe = 7;
           map <string,string> source_metas         = 8;
}

//Mount TODO之后跟ImageTemplate Mount做一个统一
message Mount {
  VolumeCfg volume     = 1;
  MountPath mount_path = 2;
}

// 其他参数使用默认值
// failureThreshold: 3
// httpGet:
//   path: /v2/health/ready
//   port: 8000
//   scheme: HTTP
// initialDelaySeconds: 10
// periodSeconds: 10
// successThreshold: 1
// timeoutSeconds: 10
message HTTPReadinessProbe {
  string path                  = 1;
  int32  port                  = 2;
  int32  initial_delay_seconds = 3;
  int32  timeout_seconds       = 4;
  int32  period_seconds        = 5;
  int32  success_threshold     = 6;
  int32  failure_threshold     = 7;
}

