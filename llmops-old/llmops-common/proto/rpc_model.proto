syntax = "proto3";

package proto;

import "proto/common.proto";
import "proto/model.proto";
import "proto/relation.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service ModelManager {
  rpc ListModels  (ListModelsReq ) returns (ListModelsRsp );
  rpc CreateModel (CreateModelReq) returns (CreateModelRsp);
  rpc UpdateModel (UpdateModelReq) returns (UpdateModelRsp);
  rpc DeleteModel (DeleteModelReq) returns (DeleteModelRsp);
  
  // CheckModelExistence 判断指定模型是否存在，根据id时全局进行判断，根据name时每个用户进行判断
  rpc CheckModelExistence          (CheckModelReq                  ) returns (CheckResourceExistenceRsp      );
  rpc CheckModelsPackage           (CheckModelsPackageReq          ) returns (CheckModelsPackageRsp          );
  rpc ImportModelsPackage          (ImportModelsPackageReq         ) returns (ImportModelsPackageRsp         );
  rpc ExportModelsPackage          (ExportModelsPackageReq         ) returns (ExportModelsPackageRsp         );
  rpc ListModelReleases            (ListModelReleasesReq           ) returns (ListModelReleasesRsp           );
  rpc CreateModelRelease           (CreateModelReleaseReq          ) returns (CreateModelReleaseRsp          );
  rpc UpdateModelRelease           (UpdateModelReleaseReq          ) returns (UpdateModelReleaseRsp          );
  rpc DeleteModelRelease           (DeleteModelReleaseReq          ) returns (DeleteModelReleaseRsp          );
  rpc GetModelReleaseRelationGraph (GetModelReleaseRelationGraphReq) returns (GetModelReleaseRelationGraphRsp);
  rpc ParseFileModel               (ParseFileModelReq              ) returns (ParseFileModelRsp              );
  rpc CreateModelFileDownloadJobs  (CreateModelFileDownloadJobsReq ) returns (CreateModelFileDownloadJobsRsp );
}
message CheckModelReq {
  UserContext ctx        = 1;
  string      model_id   = 2;
  string      model_name = 3;
}

message CheckModelReleaseReq {
  UserContext ctx          = 1;
  string      release_id   = 2;
  string      release_name = 3;
}

message ListModelsReq {
  UserContext    ctx        = 1;
  string         model_id   = 2;
  string         model_name = 3;
  DomainSelector domain     = 6;
  LabelSelector  labels     = 7;
  PageReq        page_req   = 8;
  string         sorted_by  = 9;
}

message ListModelsRsp {
           int32 total     = 1;
           int32 page_num  = 2;
           int32 page_size = 3;
  repeated Model models    = 4;
}

message CreateModelReq {
  UserContext ctx   = 1;
  Model       model = 2;
}

message CreateModelRsp {
  Model model = 1;
}

message UpdateModelReq {
  UserContext ctx   = 1;
  Model       model = 2;
}

message UpdateModelRsp {
  Model model = 1;
}

message DeleteModelReq {
  UserContext ctx      = 1;
  string      model_id = 2;
}

message DeleteModelRsp {}

message ListModelReleasesReq {
  UserContext    ctx          =  1;
  string         model_id     =  2;
  string         release_id   =  3;
  string         release_name =  4;
  DomainSelector domain       =  6;
  LabelSelector  labels       =  7;
  HardwareRange  hardware     =  8;
  PageReq        page_req     = 13;
}

message ListModelReleasesRsp {
           int32        total     = 1;
           int32        page_num  = 2;
           int32        page_size = 3;
  repeated ModelRelease releases  = 4;
}

message CreateModelReleaseReq {
  UserContext  ctx     = 1;
  ModelRelease release = 2;
}

message CreateModelReleaseRsp {
  ModelRelease release = 1;
}

message UpdateModelReleaseReq {
  UserContext  ctx          = 1;
  ModelRelease release      = 2;
  bool         force_update = 3; // 更新系统内置模型的后门
}

message UpdateModelReleaseRsp {
  ModelRelease release = 1;
}

message DeleteModelReleaseReq {
  UserContext ctx        = 1;
  string      model_id   = 2;
  string      release_id = 3;
}

message DeleteModelReleaseRsp {}

message GetModelReleaseRelationGraphReq {
  UserContext ctx        = 1;
  string      model_id   = 2;
  string      release_id = 3;
}

message GetModelReleaseRelationGraphRsp {
  RelationGraph relation_graph = 1;
}

message CheckModelsPackageReq {
  UserContext   ctx          = 1;
  ModelManifest manifest     = 2;
  string        package_path = 3;
}

message CheckModelsPackageRsp {
  repeated ModelCheckResult models = 1;
}

message ModelCheckResult {
           string                  model_id            = 1;
           string                  model_name          = 2;
           ModelType               model_type          = 3;
  repeated ModelReleaseCheckResult releases            = 4;
           bool                    existed             = 5;
           string                  conflict_with_model = 6;
           bool                    overwrite           = 7;
}

message ModelReleaseCheckResult {
  string release_id            = 1;
  string release_name          = 2;
  string model_id              = 3;
  string release_version       = 4;
  bool   existed               = 5;
  string conflict_with_release = 6;
  bool   overwrite             = 7;
}

// TODO gRPC 将 BatchUploadObjectXXX 替换为 ImportModelsPackageXXX
message ImportModelsPackageReq {
  UserContext ctx          = 1;
  string      package_path = 2;
  
  // Deprecated 兼容性保留，之后批量导入每个模型都会选择是否覆盖
           bool             is_overwrite   = 3;
  repeated ModelCheckResult check_results  = 5;
           bool             is_system_init = 4;
}

message ImportModelsPackageRsp {
  map <string,ModelImportResult> models         = 1;
  bool                           import_success = 2;
  string                         reason         = 3;
  string                         raw_error      = 4;
}

message ModelImportResult {
  string                                model_id   = 1;
  string                                model_name = 2;
  map <string,ModelReleaseImportResult> releases   = 3;
}

message ModelReleaseImportResult {
  string release_id   = 1;
  string release_name = 2;
  string model_id     = 3;
  string model_name   = 4;
}

message ExportModelsPackageReq {
  UserContext                ctx    = 1;
  map <string,ModelManifest> models = 2; // Model.id -> ModelManifest
}

message ExportModelsPackageRsp {
  string package_path = 1;
}

message CheckResourceExistenceRsp {
  bool   exist = 1;
  string msg   = 2;
}

message MigrateModelsReq {
           UserContext ctx      = 1;
  repeated string      releases = 2;
  repeated string      users    = 3;
}

message MigrateModelsRsp {
  repeated ModelMigrateResult        models   = 1;
  repeated ModelReleaseMigrateResult releases = 2;
}

message ModelMigrateResult {
  string model_id   = 1;
  string model_name = 2;
}

message ModelReleaseMigrateResult {
  string release_id   = 1;
  string release_name = 2;
  string model_id     = 3;
}

message ModelManifests {
  map <string,ModelManifest> models = 1; // Model.name -> ModelManifest
}

message ModelManifest {
  map <string,Model>        models      = 1;
  map <string,ModelRelease> releases    = 2; // Release.name -> ModelRelease
  map <string,Attachment>   attachments = 3; // Attachment.id -> Attachment
  string                    version     = 4;
}

message ParseFileModelReq {
  ModelSubType model_sub_type = 1;
  ModelSubKind model_sub_kind = 2;
  string       file_path      = 3;
}

message ParseFileModelRsp {
  repeated ModelApi model_apis = 1;
}

message CreateModelFileDownloadJobsReq {
  UserContext      ctx        = 1;
  string           model_id   = 2;
  string           release_id = 3;
  RepoAddrProtocol protocol   = 4;
  string           repo       = 5;
}

message CreateModelFileDownloadJobsRsp {
  string id = 1;
}


// protoc version 3.6.1
// protoc-gen-go  插件请使用本目录中的protoc-gen-go

// 当前目录执行： protoc  --go_out=. --go-grpc_out=. *.proto
