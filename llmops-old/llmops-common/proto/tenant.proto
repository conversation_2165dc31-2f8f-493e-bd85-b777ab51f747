syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message Tenant {
  string              tenant_name        =  1;
  string              tenant_uid         =  2;
  string              tenant_description =  3;
  string              tenant_logo        =  4;
  TenantResourceQuota tenant_quotas      =  5;
  string              tenant_status      =  6;
  string              creator            =  7;
  string              create_time        =  8;
  map <string,string> tenant_labels      =  9;
  map <string,string> tenant_annotations = 10;
  string              tcc_url            = 11;
  string              hippo_service_name = 12;
}

message ResourceQuotaSpec {
  string limits_cpu          =  1;
  string limits_memory       =  2;
  string requests_cpu        =  3;
  string requests_memory     =  4;
  string requests_storage    =  5;
  string pods                =  6;
  string bandwidth           =  7;
  string egress_bandwidth    =  8;
  string ingress_bandwidth   =  9;
  string gpu                 = 10;
  string gpu_memory          = 11;
  string requests_gpu        = 12;
  string requests_gpu_memory = 13;
  string knowl               = 14;
  string file_storage        = 15;
}

message TenantResourceQuota {
  string            name_space = 1;
  string            quota_name = 2;
  ResourceQuotaSpec hard       = 3;
  ResourceQuotaSpec used       = 4;
}

