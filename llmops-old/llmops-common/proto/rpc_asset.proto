syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum Module {
  mw        = 0;
  sample    = 1;
  mlops     = 2;
  knowledge = 3;
}
enum RscType {
  MODEL          = 0;
  CHAIN          = 1;
  DATASET        = 2;
  KNOWLEDGE_BASE = 3;
}
message AssetReq {
  string project_id = 1;
  string key        = 2;
}

message AssetResp {
           string key    = 1;
  repeated Group  groups = 2;
}

message Group {
           Module  module   = 1;
           RscType rsc_type = 2;
  repeated Item    items    = 3;
}

message Item {
  string              id            =  1;
  string              name          =  2;
  string              desc          =  3;
  map <string,string> labels        =  4;
  string              project_id    =  5;
  Module              module        =  6;
  RscType             rsc_type      =  7;
  string              creator       =  8;
  int64               update_time   =  9;
  int64               release_count = 10;
  string              asset_type    = 11;
  string              create_type   = 12;
}

