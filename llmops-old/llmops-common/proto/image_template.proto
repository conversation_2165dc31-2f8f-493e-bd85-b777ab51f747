syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum TemplateSource {
  TEMPLATE_SOURCE_SYSTEM = 0; // 系统内置
  TEMPLATE_SOURCE_CSM    = 1; // 代码空间
  TEMPLATE_SOURCE_UPLOAD = 2; // 手动上传
}
enum TemplateStatus {
  TEMPLATE_STATUS_INVALID    = 0; // 已失效
  TEMPLATE_STATUS_UNRELEASED = 1; // 待编辑
  TEMPLATE_STATUS_RELEASED   = 2; // 已发布
}
// 和mlops定义保持一致
enum TemplateType {
  TEMPLATE_TYPE_PIPELINE    = 0; // pipeline
  TEMPLATE_TYPE_TRAIN       = 1; // 训练模板
  TEMPLATE_TYPE_MODEL_EVAL  = 2; // 模型评估模板
  TEMPLATE_TYPE_MODEL_QUANT = 3;
}
enum TemplateEnvType {
  TEMPLATE_ENV_TYPE_STRING  = 0; // 字符串
  TEMPLATE_ENV_TYPE_INTEGER = 1; // 整型
  TEMPLATE_ENV_TYPE_FLOAT   = 2; // 浮点型
  TEMPLATE_ENV_TYPE_BOOL    = 3; // 布尔型
}
enum TemplateMountType {
  TEMPLATE_MOUNT_TYPE_SAMPLE     = 0; // 样本
  TEMPLATE_MOUNT_TYPE_ANNOTATION = 1; // 标注集
  TEMPLATE_MOUNT_TYPE_MODEL      = 2; // 模型
  TEMPLATE_MOUNT_TYPE_SFS        = 3; // 文件
  TEMPLATE_MOUNT_TYPE_HOST_PATH  = 4; // hostpath挂载
  TEMPLATE_MOUNT_TYPE_MEMORY     = 5; // 一般用于提高shm size
}
// 模板的大类，目前用于任务模板，后续有新的再加
enum TemplateKind {
  TEMPLATE_KIND_TASK         = 0; // 任务模板
  TEMPLATE_KIND_SERVICE      = 1; // 服务模板
  TEMPLATE_KIND_APP          = 2; // 应用模板
  TEMPLATE_KIND_CORPUS_IMAGE = 3; // 语料镜像模板
}
message ImageTemplate {
           string                  id                   =  1; // 模板的唯一id，由uuid生成
           string                  name                 =  2; // 模板名称
           string                  desc                 =  3; // 模板描述
           ImageInfo               image_info           =  4; // 镜像的信息
           map <string,string>     labels               =  5; // 标签
           TemplateSource          source               =  6; // 模板来源
           TemplateType            type                 =  7; // 模板类型
           TemplateStatus          status               =  8; // 模板状态
           string                  creator              =  9; // 创建者
           int64                   created_at_mills     = 10; // 创建时间
           int64                   updated_at_mills     = 11; // 更新时间
  repeated TemplateMount           template_mounts      = 12; // 数据挂载配置
  repeated TemplateEnv             template_envs        = 13; // 环境变量
  repeated string                  args                 = 14; // 启动参数
  repeated TemplateConfigMap       template_config_maps = 15; // 自定义文件映射
           TemplateDefaultResource default_resource     = 16; // 默认资源
           bytes                   config               = 17; // 各个模块自定义的模板配置，json bytes
           string                  project_id           = 18; // 项目id
           TemplateKind            template_kind        = 19; // 模板大类
}

message TemplateMount {
  TemplateMountType type     = 1; // 挂载类型
  string            path     = 2; // 容器内路径
  bool              required = 3; // 是否必填
}

message TemplateEnv {
  string          name     = 1; // 环境变量名称
  string          key      = 2; // 环境变量的key值
  TemplateEnvType type     = 3; // 数据类型
  string          default  = 4; // 默认值
  float           min      = 5; // 最小值
  float           max      = 6; // 最大值
  string          desc     = 7; // 描述
  bool            required = 8; // 是否必填
}

message TemplateDefaultResource {
  bool  enable         = 1; // 是否配置默认资源
  int32 cpu            = 2; // cpu个数
  int32 memory_mib     = 3; // 内存大小，单位Mi
  int32 gpu            = 4; // gpu个数
  int32 gpu_memory_mib = 5; // gpu显存大小，单位Mi
}

message TemplateConfigMap {
  string path            = 1;
  string default_content = 2;
}

// 和镜像管理结构一致
message ImageInfo {
  string              repo       = 1;
  string              tag        = 2;
  string              arch       = 3;
  string              digest     = 4;
  int64               size       = 5;
  map <string,string> labels     = 6;
  string              desc       = 7;
  string              build_time = 8;
}

