syntax = "proto3";

package proto;

import "proto/model.proto";
import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

//  DeploymentManager 为模型仓库对外提供的关于模型部署相关的服务接口
service DeploymentManager {
  rpc ReadDeployment    (ReadDeploymentReq   ) returns (       ReadDeploymentRsp   );
  rpc CreateDeployment  (CreateDeploymentReq ) returns (       CreateDeploymentRsp );
  rpc UpdateDeployment  (UpdateDeploymentReq ) returns (       UpdateDeploymentRsp );
  rpc DeleteDeployments (DeleteDeploymentsReq) returns (       DeleteDeploymentsRsp);
  rpc StartDeployments  (StartDeploymentsReq ) returns (       StartDeploymentsRsp );
  rpc StopDeployments   (StopDeploymentsReq  ) returns (       StopDeploymentsRsp  );
  rpc WatchDeployment   (WatchDeploymentReq  ) returns (stream WatchDeploymentRsp  );
  
  // UpdateDeploymentState 整体更新Deployment的运行状态 TODO
  rpc UpdateDeploymentState (UpdateDeploymentStateReq) returns (UpdateDeploymentStateRsp);
  
  // UpdateReplicaStatus 更新每个replica的状态与指标 TODO
  rpc UpdateReplicaStatus (UpdateReplicaStatusReq) returns (UpdateReplicaStatusRsp);
  
  // StartDeploymentMonitor mwh-backend为部署系统提供状态更新接口, TODO 暂时作为一种过渡机制直接舍弃mqtt改动较大
  rpc StartDeploymentMonitor (StartDeploymentMonitorReq) returns (StartDeploymentMonitorRsp);
  rpc StopDeploymentMonitor  (StopDeploymentMonitorReq ) returns (StopDeploymentMonitorRsp );
}
message ReadDeploymentReq {
  UserContext    ctx         =  1;
  string         id          =  2;
  string         model_id    =  3;
  string         release_id  =  4;
  DeploymentType type        =  5; // 为空获取全部类型的模型部署，默认为模型服务启动的部署
  PageReq        page_req    =  6;
  bool           with_status =  7;
  ModelKind      kind        =  8;
  ModelSubKind   sub_kind    =  9;
  string         state       = 10;
}

message ReadDeploymentRsp {
           int32           total             = 1;
           int32           page_num          = 2;
           int32           page_size         = 3;
  repeated ModelDeployment model_deployments = 4;
}

message CreateDeploymentReq {
  UserContext ctx        = 1;
  Deployment  deployment = 2;
}

message CreateDeploymentRsp {
  Deployment deployment = 1;
}

message UpdateDeploymentReq {
  UserContext ctx        = 1;
  Deployment  deployment = 2;
}

message UpdateDeploymentRsp {
  Deployment deployment = 1;
}

message DeleteDeploymentsReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteDeploymentsRsp {}

message StartDeploymentsReq {
  repeated string      ids = 1;
           UserContext ctx = 2;
}

message StartDeploymentsRsp {}

message StopDeploymentsReq {
  repeated string      ids = 1;
           UserContext ctx = 2;
}

message StopDeploymentsRsp {}

message WatchDeploymentReq {
  string      deploy_sys_id   = 1;
  string      deploy_sys_name = 2;
  UserContext ctx             = 3;
}

message WatchDeploymentRsp {
  Deployment deployment = 1;
}

message UpdateDeploymentStateReq {
  UserContext ctx        = 1;
  string      id         = 2;
  string      state      = 3;
  string      health     = 4;
  string      health_msg = 5;
}

message UpdateDeploymentStateRsp {}

message UpdateReplicaStatusReq {
  UserContext ctx    = 1;
  Status      status = 2;
}

message UpdateReplicaStatusRsp {}

message StartDeploymentMonitorReq {
  UserContext ctx   = 1;
  string      id    = 2;
  string      topic = 3;
}

message StartDeploymentMonitorRsp {}

message StopDeploymentMonitorReq {
  UserContext ctx   = 1;
  string      id    = 2;
  string      topic = 3;
}

message StopDeploymentMonitorRsp {}

