syntax = "proto3";

package proto;

import "proto/model.proto";
import "proto/common.proto";
import "proto/evaluation.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service ModelEvaluationManager {
  rpc CreateModelEvaluation (CreateModelEvaluationReq) returns (CreateModelEvaluationRsp);
  rpc ReadModelEvaluations  (ReadModelEvaluationReq  ) returns (ReadModelEvaluationRsp  );
  
  // 将多个版本的模型评估结果导出为csv文件
  rpc ExportModelEvaluations (ExportModelEvaluationsReq) returns (ExportModelEvaluationsRsp);
  
  // 仅结构化数据模型，查看csv表数据处理状态
  rpc CheckDataProcessStatus (CheckDataProcessStatusReq) returns (CheckDataProcessStatusRsp);
  rpc UpdateModelEvaluation  (UpdateModelEvaluationReq ) returns (UpdateModelEvaluationRsp );
  rpc DeleteModelEvaluations (DeleteModelEvaluationReq ) returns (DeleteModelEvaluationRsp );
  rpc StartModelEvaluation   (StartModelEvaluationsReq ) returns (StartModelEvaluationsRsp );
  rpc StopModelEvaluation    (StopModelEvaluationsReq  ) returns (StopModelEvaluationsRsp  );
}
message CreateModelEvaluationReq {
  UserContext ctx        = 1;
  Evaluation  evaluation = 2;
}

message CreateModelEvaluationRsp {
  Evaluation evaluation = 1;
}

message ReadModelEvaluationReq {
           string      id          = 1;
           string      model_id    = 2;
  repeated string      release_ids = 3; // 需要对比的版本
           bool        with_result = 4;
           bool        with_status = 5;
           PageReq     page_req    = 6;
           UserContext ctx         = 7;
}

enum EvaluationMetricsKind {
  EVALUATION_METRICS_KIND_UNSPECIFIC  = 0;
  EVALUATION_METRICS_KIND_MODEL       = 1; // 模型指标
  EVALUATION_METRICS_KIND_PERFORMANCE = 2; // 性能指标
}
message ExportModelEvaluationsReq {
  UserContext           ctx      = 1;
  string                model_id = 2;
  EvaluationMetricsKind metrics  = 3;
}

message ExportModelEvaluationsRsp {
           string model_id = 1;
  repeated string ids      = 2;
           Csv    csv      = 3; // CSV文件内容
}

message CheckDataProcessStatusReq {
  UserContext ctx      = 1;
  string      id       = 2;
  PageReq     page_req = 3;
}

// CheckDataProcessStatusRsp 返回的是一张类似于csv的表格
message CheckDataProcessStatusRsp {
           int32  total                  = 1;
           int32  page_num               = 2;
           int32  page_size              = 3;
           Csv    csv                    = 4;
  repeated string original_input_headers = 5;
}

message ReadModelEvaluationRsp {
           int32      total       = 1;
           int32      page_num    = 2;
           int32      page_size   = 3;
  repeated Evaluation evaluations = 4;
}

message UpdateModelEvaluationReq {
  UserContext ctx        = 1;
  Evaluation  evaluation = 2;
}

message UpdateModelEvaluationRsp {
  Evaluation evaluation = 1;
}

message DeleteModelEvaluationReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteModelEvaluationRsp {}

message StartModelEvaluationsReq {
  string      id  = 1;
  UserContext ctx = 2;
}

message StartModelEvaluationsRsp {}

message StopModelEvaluationsReq {
  string      id  = 1;
  UserContext ctx = 2;
}

message StopModelEvaluationsRsp {}


service EvaluationScriptManager {
  rpc CreateEvaluationScript (CreateEvaluationScriptReq) returns (CreateEvaluationScriptRsp);
  rpc ReadEvaluationScript   (ReadEvaluationScriptReq  ) returns (ReadEvaluationScriptRsp  );
  rpc UpdateEvaluationScript (UpdateEvaluationScriptReq) returns (UpdateEvaluationScriptRsp);
  rpc DeleteEvaluationScript (DeleteEvaluationScriptReq) returns (DeleteEvaluationScriptRsp);
  
  // 上传文件使用统一的文件上传组件，这里上传文件直接传递文件上传组件返回的地址
  rpc UploadEvaluationScript         (UploadEvaluationScriptReq        ) returns (UploadEvaluationScriptRsp        );
  rpc DownloadEvaluationScript       (DownloadEvaluationScriptReq      ) returns (DownloadEvaluationScriptRsp      );
  rpc CheckEvaluationScriptExistence (CheckEvaluationScriptExistenceReq) returns (CheckEvaluationScriptExistenceRsp);
}
message CheckEvaluationScriptExistenceReq {
  UserContext ctx      = 3;
  string      model_id = 1;
  string      name     = 2;
}

message CheckEvaluationScriptExistenceRsp {
  bool   exist = 1;
  string msg   = 2;
}

message CreateEvaluationScriptReq {
  UserContext      ctx    = 1;
  EvaluationScript script = 2;
  
  // 通过上传文件的方式创建
  string file_path = 3;
}

message CreateEvaluationScriptRsp {
  EvaluationScript script = 1;
}

message ReadEvaluationScriptReq {
  string      id         = 1;
  string      model_id   = 2;
  string      release_id = 3;
  PageReq     page_req   = 4;
  UserContext ctx        = 5;
}

message ReadEvaluationScriptRsp {
           int32            total     = 1;
           int32            page_num  = 2;
           int32            page_size = 3;
  repeated EvaluationScript scripts   = 4;
}

message UpdateEvaluationScriptReq {
  UserContext      ctx    = 1;
  EvaluationScript script = 2;
}

message UpdateEvaluationScriptRsp {
  UserContext      ctx    = 1;
  EvaluationScript script = 2;
}

message DeleteEvaluationScriptReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteEvaluationScriptRsp {}

message UploadEvaluationScriptReq {
  UserContext     ctx       = 1;
  string          id        = 2;
  EvaluationStage stage     = 3;
  string          file_path = 4;
}

message UploadEvaluationScriptRsp {
  string file_name = 1;
  int64  size      = 2;
  string digest    = 3;
}

message DownloadEvaluationScriptReq {
  UserContext     ctx   = 1;
  string          id    = 2;
  EvaluationStage stage = 4;
}

message DownloadEvaluationScriptRsp {
  string          id           = 1;
  EvaluationStage stage        = 2;
  string          file_name    = 3;
  bytes           file_content = 4;
}

service EvaluationDatasetManager {
  rpc CreateEvaluationDataset         (CreateEvaluationDatasetReq        ) returns (CreateEvaluationDatasetRsp        );
  rpc CheckEvaluationDatasetExistence (CheckEvaluationDatasetExistenceReq) returns (CheckEvaluationDatasetExistenceRsp);
  
  // 仅结构化数据，获取csv表格表头用于数据校验
  rpc GetCsvHeaders             (GetCsvHeadersReq            ) returns (GetCsvHeadersRsp            );
  rpc ValidateEvaluationDataset (ValidateEvaluationDatasetReq) returns (ValidateEvaluationDatasetRsp);
  rpc PreviewEvaluationDataset  (PreviewEvaluationDatasetReq ) returns (PreviewEvaluationDatasetRsp );
  rpc ReadEvaluationDataset     (ReadEvaluationDatasetReq    ) returns (ReadEvaluationDatasetRsp    );
  rpc UpdateEvaluationDataset   (UpdateEvaluationDatasetReq  ) returns (UpdateEvaluationDatasetRsp  );
  rpc DeleteEvaluationDataset   (DeleteEvaluationDatasetReq  ) returns (DeleteEvaluationDatasetRsp  );
  rpc UploadEvaluationDataset   (UploadEvaluationDatasetReq  ) returns (UploadEvaluationDatasetRsp  );
  rpc DownloadEvaluationDataset (DownloadEvaluationDatasetReq) returns (DownloadEvaluationDatasetRsp);
}
message CreateEvaluationDatasetReq {
  UserContext       ctx       = 1;
  EvaluationDataset dataset   = 2;
  string            file_path = 3;
  string            copy_from = 4; // 从一个已有的数据集copy生成新的数据集。
}

message CreateEvaluationDatasetRsp {
  EvaluationDataset dataset = 1;
}

message CheckEvaluationDatasetExistenceReq {
  UserContext ctx      = 3;
  string      model_id = 1;
  string      name     = 2;
}

message CheckEvaluationDatasetExistenceRsp {
  bool   exist = 1;
  string msg   = 2;
}

message GetCsvHeadersReq {
  UserContext ctx       = 1;
  string      file_path = 2;
}

message GetCsvHeadersRsp {
  string    file_name = 1;
  CsvHeader headers   = 2;
}

message ValidateEvaluationDatasetReq {
  UserContext ctx            = 1;
  string      model_id       = 2;
  string      real_value_col = 3;
  string      file_path      = 4;
}

message ValidateEvaluationDatasetRsp {
  repeated string checked  = 1;
  repeated string expected = 2;
           bool   is_valid = 3;
}

// PreviewEvaluationDatasetReq 仅用于预览CSV表格的请求
message PreviewEvaluationDatasetReq {
  UserContext ctx         = 1;
  string      id          = 2;
  string      model_id    = 3;
  int64       start_index = 4;
  int64       size        = 5;
}

message PreviewEvaluationDatasetRsp {
  Csv csv = 1;
}

message ReadEvaluationDatasetReq {
  UserContext ctx        = 1;
  string      id         = 2;
  string      model_id   = 3;
  string      release_id = 4; // 获取组合模型指定版本的子模型的数据集
  PageReq     page_req   = 5;
  bool        only_self  = 6; // 当为model_id所指模型为组合模型时，only_self为true时仅返回自身的数据集不返回子模型数据集
}

message ReadEvaluationDatasetRsp {
           int32             total     = 1;
           int32             page_num  = 2;
           int32             page_size = 3;
  repeated EvaluationDataset datasets  = 4;
}

message UpdateEvaluationDatasetReq {
  UserContext       ctx     = 1;
  EvaluationDataset dataset = 2;
}

message UpdateEvaluationDatasetRsp {
  EvaluationDataset dataset = 1;
}

message DeleteEvaluationDatasetReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteEvaluationDatasetRsp {}

message UploadEvaluationDatasetReq {
  UserContext ctx       = 1;
  string      id        = 2;
  string      file_path = 4;
}

message UploadEvaluationDatasetRsp {
  string file_name = 1;
  int64  size      = 2;
  string digest    = 3;
}

message DownloadEvaluationDatasetReq {
  UserContext ctx = 1;
  string      id  = 2;
}

message DownloadEvaluationDatasetRsp {
  string id           = 1;
  string file_name    = 2;
  bytes  file_content = 4;
}

service EvaluationScriptTemplateManager {
  rpc CreateEvaluationScriptTemplate (CreateEvaluationScriptTemplateReq) returns (CreateEvaluationScriptTemplateRsp);
  rpc ReadEvaluationScriptTemplate   (ReadEvaluationScriptTemplateReq  ) returns (ReadEvaluationScriptTemplateRsp  );
  rpc UpdateEvaluationScriptTemplate (UpdateEvaluationScriptTemplateReq) returns (UpdateEvaluationScriptTemplateRsp);
  rpc DeleteEvaluationScriptTemplate (DeleteEvaluationScriptTemplateReq) returns (DeleteEvaluationScriptTemplateRsp);
}
message CreateEvaluationScriptTemplateReq {
  UserContext              ctx      = 1;
  EvaluationScriptTemplate template = 2;
}

message CreateEvaluationScriptTemplateRsp {
  EvaluationScriptTemplate template = 3;
}

message ReadEvaluationScriptTemplateReq {
  UserContext ctx      = 1;
  string      name     = 2;
  string      model_id = 3;
  PageReq     page_req = 4;
}

message ReadEvaluationScriptTemplateRsp {
           int32                    total     = 1;
           int32                    page_num  = 2;
           int32                    page_size = 3;
  repeated EvaluationScriptTemplate templates = 4;
}

message UpdateEvaluationScriptTemplateReq {
  UserContext              ctx      = 1;
  EvaluationScriptTemplate template = 2;
}

message UpdateEvaluationScriptTemplateRsp {
  EvaluationScriptTemplate template = 1;
}

message DeleteEvaluationScriptTemplateReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteEvaluationScriptTemplateRsp {}

