syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message PmmlProtoc {
           string          version           = 1;
           string          copyright         = 2;
           string          description       = 3;
           Application     application       = 4;
           Timestamp       timestamp         = 5;
  repeated InputFields     input_fields      = 6;
  repeated OutputFields    output_fields     = 7;
           bool            mining_mode       = 8;
  repeated ModelDefinition model_definitions = 9;
}

message Application {
  string name    = 1;
  string version = 2;
}

message Timestamp {
  repeated string content = 1;
}

message InputFields {
  string name       = 1;
  string usage_type = 2;
  string data_type  = 3;
  string optype     = 4;
}

message OutputFields {
  string name      = 1;
  string optype    = 2;
  string data_type = 3;
  string feature   = 4;
  string value     = 5;
}

message ModelDefinition {
  string model_name      = 1;
  string mining_function = 2;
  string function_name   = 3;
}

