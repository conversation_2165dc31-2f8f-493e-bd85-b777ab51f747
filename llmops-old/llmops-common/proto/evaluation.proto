syntax = "proto3";

package proto;

import "proto/model.proto";
import "proto/image_template.proto";
import "proto/serving/mlops_service.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message EvalMission {
  string            id             =  1;
  string            name           =  2;
  string            desc           =  3;
  string            project_id     =  4;
  EvalMissionConfig config         =  5;
  string            status         =  6;
  int64             update_time_ms =  7;
  int64             start_time_ms  =  8;
  int64             stop_time_ms   =  9;
  string            create_user    = 10;
  RunInfo           run_info       = 11;
  int64             create_time_ms = 12;
}

message RunInfo {
  string id      = 1;
  string node_id = 2;
}

message EvalMissionConfig {
  EvalModelConfig model_config = 1;
  EvalDataConfig  data_config  = 2;
  
  //Deprecated use serving.UnifyResource instead
  DeployResource        resource_config       = 3;
  serving.UnifyResource unify_resource_config = 8;
  EvalTemplateConfig    template_config       = 4;
  
  // custom_config_mapping is extra file mapping defined when config mission
  // key: host config value: container config
  repeated MountConfig custom_config_mapping = 5;
  
  // envs is extra environment variables, include 2 parts:
  // fixed env: defined in image template, has fixed key
  // custom env: defined in mission config
  repeated EvalMissionEnv envs      = 6;
           string         boot_args = 7;
}

message EvalMissionEnv {
  string key   = 1;
  string value = 2;
  
  // if evaluation env is built-in (like model service) or from evaluation template, it is fixed
  bool fixed_key = 3;
}

enum ModelSource {
  MODEL_SOURCE_UNKNOWN = 0;
  MODEL_SOURCE_MWH     = 1;
}
enum EvalModelType {
  MODEL_TYPE_SERVICE_INVOKE = 0; // 服务调用的方式
  MODEL_TYPE_FILE_MOUNT     = 1; // 文件挂载的方式
}
message EvalModelConfig {
  string         model_id       = 1;
  string         model_name     = 2;
  string         release_id     = 3;
  string         release_name   = 4;
  ModelKind      model_kind     = 5;
  ModelSubKind   model_sub_kind = 6;
  ModelSource    model_source   = 7;
  string         deployment_id  = 8;
  EvalMissionEnv service_env    = 9;
  
  //model_mount allow user use model by mount host path. key: model location on host: model location in container
  repeated MountConfig   model_mount   = 10;
           string        version       = 11;
           EvalModelType model_type    = 12;
           string        full_url      = 13;
           ModelService  model_service = 14;
}

enum EvalDataInputFormat {
  EVAL_DATA_INPUT_FORMAT_UNKNOWN = 0;
  EVAL_DATA_INPUT_FORMAT_SYS     = 1;
  EVAL_DATA_INPUT_FORMAT_CUSTOM  = 2;
}
message MountConfig {
  
  // fixed_mount is mount point in container, defined in template
  TemplateMount fixed_mount = 1;
  
  // custom_mount is mount path of host or pvc
  string custom_mount = 2;
}

message EvalDataConfig {
           EvalDataInputFormat input_format   =  1;
           string              name_or_link   =  2;
           string              name           =  3;
           ModelKind           kind           =  4;
           ModelSubKind        sub_kind       =  5;
  repeated string              columns        =  6;
           int64               rows_count     =  7;
           string              creator        =  8;
           int64               update_time_ms =  9;
  repeated CsvRow              preview        = 10;
           string              id             = 11;
  
  // data_mount key: mount path of host; value: mount path in container(already defined in image template)
  repeated MountConfig data_mount = 12;
           string      path       = 13;
           string      data_type  = 14;
           string      version_id = 15;
}

// EvalTemplateConfig 是摘要评估模板的摘要信息，存储在mission中
message EvalTemplateConfig {
  string         id       = 1;
  TemplateSource source   = 2;
  string         name     = 3;
  ModelKind      kind     = 4;
  ModelSubKind   sub_kind = 5;
  string         desc     = 6;
}

// EvalTemplateCustomConfig is custom config of evaluation
message EvalTemplateCustomConfig {
           ModelKind           model_kind     = 1;
           ModelSubKind        model_sub_kind = 2;
           EvalDataInputFormat input_format   = 3;
           string              ext_or_link    = 4;
           string              output_path    = 5;
  repeated OutputMetric        output_metrics = 6;
           EvalModelType       model_type     = 7;
}

message OutputMetric {
  string name     = 1;
  string type     = 2;
  string desc     = 3;
  string category = 4;
}

message Evaluation {
  string       id             =  1;
  string       name           = 29;
  string       model_id       =  2;
  string       model_name     =  3;
  ModelKind    model_kind     =  4;
  ModelSubKind model_sub_kind =  5;
  string       release_id     =  6;
  string       release_name   =  7;
  string       version        = 26;
  
  // 由评估任务启动，不显示在评估列表
           string           deployment_id  =  8;
           string           dataset_id     =  9;
           string           dataset_name   = 10;
           string           script_id      = 11;
           string           script_name    = 12;
           bool             enable         = 13;
           string           creator        = 14;
           DeployResource   resource       = 15;
           DeploySelector   selector       = 16;
           int64            start_time_ms  = 17;
           int64            end_time_ms    = 18;
           int64            create_time_ms = 19;
           int64            update_time_ms = 20;
           EvaluationResult result         = 21;
           Status           status         = 22;
           ModelType        model_type     = 23;
           ModelSubType     model_sub_type = 24;
           string           project_id     = 25;
           string           template_id    = 27;
  repeated OutputMetric     metrics_desc   = 28;
}

