syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum ConnectionType {
  HIPPO       =  0;
  SCOPE       =  1;
  STELLAR_DB  =  2;
  AWS_S3      = 10; // aws_s3
  MINIO       = 11; // minio
  ALI_OSS     = 12; // 阿里云OSS
  TENCENT_COS = 13; // 腾讯云COS
}
enum ConnectionStatus {
  NONE      = 0;
  SUCCEEDED = 1;
  FAILED    = 2;
}
enum StorageType {
  DATABASE = 0;
  OSS      = 1;
}
message DataConnection {
  
  // @gotags: description:"数据连接id"
  string id = 1;
  
  // @gotags: description:"数据连接名称"
  string name = 2;
  
  // @gotags: description:"数据连接描述"
  string description = 3;
  
  // @gotags: description:"数据连接地址"
  string address = 4;
  
  // @gotags: description:"数据连接端口"
  string port = 5;
  
  // @gotags: description:"数据连接类型"
  ConnectionType type = 6;
  
  // @gotags: description:"数据连接状态"
  ConnectionStatus status = 7;
  
  // @gotags: description:"创建用户"
  string create_user = 8;
  
  // @gotags: description:"创建时间"
  int64 create_time_mills = 9;
  
  // @gotags: description:"更新时间"
  int64 update_time_mills = 10;
  
  // @gotags: description:"数据连接用户名"
  string username = 11;
  
  // @gotags: description:"数据连接密码"
  string password = 12;
  
  // @gotags: description:"数据所在数据库"
  string database = 13;
  
  // @gotags: description:"额外属性"
  string config = 14;
  
  // @gotags: description:"项目id"
  string project_id = 15;
  
  // @gotags: description:"存储类型"
  StorageType storage_type = 16;
  
  // @gotags: description:"存储配置信息"
  StorageConfig storage_config = 17;
}

message StorageConfig {
  
  // @gotags: description:"s3配置信息"
  S3Config s3 = 1;
}

message S3Config {
  
  // @gotags: description:"s3地址"
  string end_point = 1;
  
  // @gotags: description:"区域"
  string region = 2;
  
  // @gotags: description:"ak"
  string ak = 3;
  
  // @gotags: description:"sk"
  string sk = 4;
}

