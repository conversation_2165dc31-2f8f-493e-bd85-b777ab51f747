syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum QAAuthor {
  QA_AUTHOR_UNSPECIFIC = 0;
  QA_AUTHOR_USER       = 1;
  QA_AUTHOR_ASSISTANT  = 2;
}
enum QAStatus {
  QA_STATUS_UNSPECIFIC = 0;
  QA_STATUS_SUCCESS    = 1;
  QA_STATUS_FAILED     = 2; // 推理出错
  QA_STATUS_STOPPED    = 3; // 被用户手动停止
}
enum QARating {
  QA_RATING_UNSPECIFIC = 0;
  QA_RATING_UPVOTE     = 1;
  QA_RATING_DOWNVOTE   = 2;
}
message QA {
           string              id             =  1;
           string              content        =  2;
           QARating            rating         =  3;
           QAStatus            status         =  4;
           int64               create_time_ms =  5;
           map <string,string> params         =  6;
           string              parent         =  7;
  repeated string              children       =  8;
           bool                is_answer      =  9;
           QAAuthor            author         = 10;
           bool                end_turn       = 11;
}

message Conversation {
  string          id             = 1;
  string          title          = 2;
  int64           create_time_ms = 3;
  int64           update_time_ms = 4;
  map <string,QA> rounds         = 5; // 对话轮次
  string          deployment_id  = 6;
  string          creator        = 7;
  string          project_id     = 8;
}

