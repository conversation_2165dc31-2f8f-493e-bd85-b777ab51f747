syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/serving;serving";

package serving;

import "proto/common/commons.proto";
import "proto/serving/mlops_service.proto";
import "google/protobuf/struct.proto";

service AuditV2Service {
  
  // 服务数量总览
  rpc GetServiceOverview (OverviewReq) returns (DashboardOverview);
  
  // 服务数量趋势
  rpc GetServiceCurveChart (TimeReq) returns (DashboardCurveChart);
  
  // 内存/cpu使用率
  rpc GetResourceUsageCurveChart (ResourceUsageReq) returns (DashboardResourceUsageCurveChart);
  
  // 访问量/耗时总览
  rpc GetRequestOverview (OverviewReq) returns (DashboardRequestOverview);
  
  // 访问量/耗时趋势
  rpc GetRequestBarChart (TimeReq) returns (DashboardRequestBarChart);
  
  // 获取首字时延指标监控指标趋势图
  rpc GetFirstTokenTimeChart (TimeReq) returns (commons.DashboardChart);
  
  // 访问量排名
  rpc GetVisitRank (TimeReq) returns (DashboardVisitRank);
  
  // 统计使用度
  rpc GetServiceDailyVisit (TimeReq) returns (DashboardVisitHotGraph);
  
  // 日志记录
  rpc GetServiceVisitRecordList (ServiceVisitRecordPageReq) returns (ServiceVisitRecordPage);
  
  // 下载日志
  rpc DownloadServiceVisitRecord (DownloadServiceVisitRecordReq) returns (DownloadServiceVisitRecordRes);
  
  // 获取gpu core、gpu memory使用率
  rpc GetGPUResourceCurveChart (GpuResourceUsageReq) returns (GpuCurveChart);
  
  // Tokens趋势
  rpc GetTokenBarChart (GetTokenBarChartReq) returns (DashboardTokenBarChart);
  
  // Tokens统计
  rpc CountToken (CountTokenReq) returns (CountTokenRsp);
  
  // 点赞点踩
  rpc EvaluateAnswer (EvaluateAnswerReq) returns (commons.EmptyRsp);
  
  // 监控大盘-字时延平均值
  rpc GetAvgFirstTokenTime (AvgFirstTimeTokenReq) returns (AvgFirstTimeToken);
  
  // 监控大盘-服务今日访问概述
  rpc GetGlobalOverview (commons.EmptyReq) returns (DashboardGlobalOverView);
  
  // 监控大盘-服务今日访问概述
  rpc GetGlobalDays (commons.EmptyReq) returns (DashboardGlobalDays);
}
message DashboardGlobalOverView {
  AvgFirstTimeToken   first_token = 1;
  DashboardGlobalBase base        = 2;
}

message DashboardGlobalDays {
  repeated Days days = 1;
}

message Days {
  
  //@gotags:description:"总量"
  int32 value = 1;
  
  // @gotags: description:"时间"
  string time = 2;
}

message DashboardGlobalBase {
  
  //@gotags:description:"当日访问量"
  int32 today_count = 1;
  
  //@gotags:description:"昨日访问量"
  int32 yesterday_count = 2;
  
  //@gotags:description:"当日访问平均耗时"
  string today_rtt = 3;
  
  //@gotags:description:"昨天访问平均耗时"
  string yesterday_rtt = 4;
}

message AvgFirstTimeTokenReq {
  
  //@gotags:description:"项目id"
  string project_id = 1;
  
  //@gotags:description:"租户id"
  string tenant_id = 2;
}

message ListBillingRuleReq {
  
  //@gotags:description:"分页参数"
  string project_id = 1;
}

message BillingRuleList {
  
  //@gotags:description:"列表"
  repeated BillingRule billing_rules = 1;
}

message BillingRule {
  
  //@gotags:description:"服务id"
  string service_id = 2;
  
  //@gotags:description:"服务名称"
  string service_name = 3;
  
  //@gotags:description:"描述"
  string desc = 4;
  
  //@gotags:description:"计费单价"
  commons.BillingConfig billing_config = 5;
}

message OperateLogReq {
  
  //@gotags:description:"分页参数"
  commons.PageReq pageReq = 1;
  
  //@gotags:description:"开始时间"
  int64 start = 2;
  
  //@gotags:description:"结束时间"
  int64 end = 3;
}

message OperateLogPage {
  
  //@gotags:description:"操作日志数量"
  int64 size = 1;
  
  //@gotags:description:"操作日志数组"
  repeated OperateLog operateLog = 2;
}

message OperateLog {
  
  //@gotags:description:"唯一标识"
  string id = 1;
  
  //@gotags:description:"操作人员"
  string Operator = 2;
  
  //@gotags:description:"操作时间"
  int64 OperateTime = 3;
  
  //@gotags:description:"操作类型"
  string OperateType = 4;
  
  //@gotags:description:"操作模块"
  string OperateModule = 5;
  
  //@gotags:description:"操作的资源"
  string OperateResource = 6;
  
  //@gotags:description:"操作的具体行为"
  string OperateBehavior = 7;
  
  //@gotags:description:"操作的结果"
  string OperateResult = 8;
}

message PortalModuleStatistics {
  int64 modelCount         =  1;
  int64 modelChange        =  2;
  int64 graphCount         =  3;
  int64 graphChange        =  4;
  int64 nodeCount          =  5;
  int64 nodeChange         =  6;
  int64 dataCount          =  7;
  int64 dataChange         =  8;
  int64 successView        =  9;
  int64 failureView        = 10;
  int64 batchPredictCount  = 11;
  int64 batchPredictChange = 12;
}

message OverviewReq {
  
  //@gotags:description:"服务id"
  string serviceId = 1;
  
  //@gotags:description:"服务版本id"
  string serviceVersion = 2;
}

message FeatureDistribution {
  
  //@gotags:description:"特征列名称"
  string feature = 1;
  
  //@gotags:description:"特征列最小值"
  double min = 2;
  
  //@gotags:description:"特征列最大值"
  double max = 3;
  
  //@gotags:description:"特征列总数量"
  int32 count = 4;
  
  //@gotags:description:"特征分布数组"
  repeated FeatureBucket bucket = 5;
}

// tips: left close right open
message FeatureBucket {
  
  //@gotags:description:"特征区间最小值，包含"
  double left = 1;
  
  //@gotags:description:"特征区间最大值，不包含"
  double right = 2;
  
  //@gotags:description:"特征区间数据量"
  int32 count = 3;
}

message TrainingDataDistribution {
  
  //@gotags:description:"训练数据特征分布"
  repeated FeatureDistribution distributions = 1;
}

message DeviationIndexReq {
  
  //@gotags:description:"服务id"
  string serviceId = 1;
  
  //@gotags:description:"服务版本id"
  string serviceVersion = 2;
  
  //@gotags:description:"特征列名称"
  string feature = 3;
  
  //@gotags:description:"开始时间"
  int64 start = 4;
  
  //@gotags:description:"结束时间"
  int64 end = 5;
}

message DeviationStateReq {
  string requestId = 1;
}

message ServiceVersionDeviationForShow {
  
  //@gotags:description:"日期"
  repeated string date = 1;
  
  //@gotags:description:"输入数据统计"
  repeated ServiceInputStatistic inputStatistic = 2;
  
  //@gotags:description:"输出数据统计"
  repeated ServiceOutputStatistic outputStatistic = 3;
  
  //@gotags:description:"输入参数分布对比"
  map <string,InputDistributionStatistic> inputDistributionStatistic = 4;
  
  //@gotags:description:"输入参数偏移指标"
  repeated ServiceInputDeviationStatistic inputDeviation = 5;
}

message ServiceVersionDeviationForSave {
  
  //@gotags:description:"日期"
  string date = 1;
  
  //@gotags:description:"输入数据统计"
  ServiceInputStatistic inputStatistic = 2;
  
  //@gotags:description:"输出数据统计"
  ServiceOutputStatistic outputStatistic = 3;
  
  //@gotags:description:"输入参数分布对比"
  map <string,InputDistributionStatistic> inputDistributionStatistic = 4;
  
  //@gotags:description:"输入参数各特征列偏移指标"
  ServiceInputDeviationStatistic inputDeviation = 5;
}

message ServiceInputStatistic {
  
  //@gotags:description:"输入特征数据统计"
  map <string,InputStatistic> input = 1;
}

message ServiceOutputStatistic {
  
  //@gotags:description:"输出数据统计"
  map <string,int32> count = 1;
}

message InputDistributionStatistic {
  
  //@gotags:description:"输入特征分布范围"
  repeated string area = 1;
  
  //@gotags:description:"训练集输入特征范围内数量"
  repeated int32 train = 2;
  
  //@gotags:description:"预测集输入特征范围内数量"
  repeated int32 predict = 3;
}

message InputDeviationStatistic {
  double psi = 1;
  double js  = 2;
}

message ServiceInputDeviationStatistic {
  
  //@gotags:description:"输入参数各特征列偏移指标"
  map <string,InputDeviationStatistic> deviationStatistic = 1;
}

message InputStatistic {
  double min               = 1;
  double max               = 2;
  double avg               = 3;
  double standardDeviation = 4;
  int32  count             = 5;
}

message DeviationIndex {
  float psi = 1;
  float js  = 2;
}

message ResourceUsageReq {
  
  //@gotags:description:"资源利用率类别, cpu/memory"
  string type = 1;
  
  //@gotags:description:"时间参数"
  TimeReq timeReq = 2;
}

message GetSvcMlopsGpusRsp {
  map <string,google.protobuf.ListValue> NodeAndGpus = 1;
}

message GpuResourceUsageReq {
  string  type      = 1;
  TimeReq timeReq   = 2;
  string  clusterId = 3;
  string  gpuId     = 4;
  string  node      = 5;
}

message GpuCurveChart {
  message Value {
    repeated float value = 1;
  }

  repeated Value  data  = 2;
  repeated string xAxis = 3;
}

message TimeReq {
  
  //@gotags:description:"开始时间"
  int64 start = 1;
  
  //@gotags:description:"结束时间"
  int64 end = 2;
  
  //@gotags:description:"查询步长,单位s"
  int64 step = 3;
  
  //@gotags:description:"服务id"
  string serviceId = 4;
  
  //@gotags:description:"服务版本id"
  string serviceVersion = 5;
}

message DashboardOverview {
  
  //@gotags:description:"上线中的服务数量"
  int32 onlineService = 1;
  
  //@gotags:description:"同比昨日上线中的服务数量"
  int32 offlineService = 2;
  
  //@gotags:description:"下线中的服务数量"
  int32 newOnlineService = 3;
  
  //@gotags:description:"同比昨日下线中的服务数量"
  int32 newOfflineService = 4;
}

message DashboardResourceUsageCurveChart {
  
  //@gotags:description:"服务资源利用率纵坐标"
  repeated float value = 1;
  
  //@gotags:description:"服务资源利用率横坐标"
  repeated string xAxis = 2;
}

message DashboardCurveChart {
  message Value {
    
    //@gotags:description:"服务数量：总量/上线/下线"
    repeated int32 value = 1;
  }

  
  //@gotags:description:"服务运行趋势纵坐标"
  repeated Value data = 2;
  
  //@gotags:description:"服务运行趋势横坐标"
  repeated string xAxis = 3;
}

message DashboardRequestOverview {
  
  //@gotags:description:"当日访问量"
  int32 pv = 1;
  
  //@gotags:description:"同比昨日访问量"
  int32 newPV = 2;
  
  //@gotags:description:"当日访问平均耗时"
  string rtt = 3;
  
  //@gotags:description:"同比昨日访问耗时百分比"
  string trend = 4;
}

message DashboardRequestBarChart {
  message Value {
    
    //@gotags:description:"服务名称"
    string name = 1;
    
    //@gotags:description:"访问数量"
    int32 num = 2;
    
    //@gotags:description:"访问耗时"
    float cost = 5;
  }

  
  //@gotags:description:"访问量/平均耗时柱状图数据"
  repeated Value data = 1;
}

message DashboardVisitRank {
  message Value {
    
    //@gotags:description:"服务id"
    string serviceId = 1;
    
    //@gotags:description:"服务名称"
    string name = 2;
    
    //@gotags:description:"访问数量"
    int32 number = 3;
  }

  
  //@gotags:description:"访问量排行"
  repeated Value visit = 1;
  message RequestTime {
    
    //@gotags:description:"服务id"
    string serviceId = 1;
    
    //@gotags:description:"服务名称"
    string name = 2;
    
    //@gotags:description:"访问耗时"
    float cost = 3;
  }

  
  //@gotags:description:"访问耗时排行"
  repeated RequestTime time = 2;
}

message DashboardVisitHotGraph {
  message HotGraph {
    
    //@gotags:description:"日期"
    string date = 1;
    
    //@gotags:description:"数量"
    int32 count = 2;
  }

  
  //@gotags:description:"热点图数据"
  repeated HotGraph hotGraph = 1;
}

message ServiceVisitRecordPage {
  
  //@gotags:description:"总数量"
  int32 size = 1;
  
  //@gotags:description:"访问记录数组"
  repeated ServiceVisitRecord records = 2;
  
  //@gotags:description:"服务id-name映射"
  map <string,string> serviceNameMap = 3;
}

message StatsVisitRecordRsp {
  
  //@gotags:description:"记录该时间段的访问总量"
  VisitCount total = 1;
  
  //@gotags:description:"记录该时间段内每天的访问量"
  repeated VisitCount detail = 2;
}

message VisitCount {
  int32  modelCube    = 1;
  int32  appCube      = 2;
  int32  customWidget = 3;
  string dayInfo      = 4;
  int64  sort         = 5;
}

message ServiceVisitRecordPageReq {
  
  //@gotags:description:"分页参数"
  commons.PageReq pageReq = 1;
  
  //@gotags:description:"服务名称"
  string serviceName = 2;
  
  //@gotags:description:"访问开始时间"
  int64 start = 3;
  
  //@gotags:description:"访问结束时间"
  int64 end = 4;
  
  //@gotags:description:"客户端ip"
  string ip = 5;
  
  //@gotags:description:"访问状态"
  string state = 6;
  
  //@gotags:description:"服务id"
  string serviceId = 7;
  
  //@gotags:description:"服务版本"
  string serviceVersion = 8;
  
  //@gotags:description:"访问类型，预测/解释"
  string visitType = 9;
}

message DownloadServiceVisitRecordReq {
  
  //@gotags:description:"服务id数组"
  repeated string serviceIds = 1;
  
  //@gotags:description:"访问开始时间"
  int64 start = 2;
  
  //@gotags:description:"访问结束时间"
  int64 end = 3;
}

message DownloadServiceVisitRecordRes {
  
  //@gotags:description:"访问记录字节数据"
  bytes data = 1;
  
  //@gotags:description:"访问记录文件名称"
  string fileName = 2;
}

message ServiceVisitRecord {
  
  //@gotags:description:"唯一标识"
  string id = 1;
  
  //@gotags:description:"服务名称"
  string name = 2;
  
  //@gotags:description:"服务版本"
  string version = 3;
  
  //@gotags:description:"访问时间"
  string visitTime = 4;
  
  //@gotags:description:"访问状态"
  string state = 5;
  
  //@gotags:description:"请求路径"
  string serviceIp = 6;
  
  //@gotags:description:"访问耗时"
  int64               duration  =  7;
  map <string,string> champion  =  9;
  map <string,string> candidate = 10;
  
  //@gotags:description:"服务类型"
  string refType = 11;
}

message Detail {
  message CommonInfo {
    
    //@gotags:description:"请求路径"
    string requestPath = 1;
    
    //@gotags:description:"请求方法"
    string requestMethod = 2;
    
    //@gotags:description:"服务器地址"
    string remoteAddress = 3;
    
    //@gotags:description:"请求状态码"
    string statusCode = 4;
  }

  message RequestHeader {
    
    //@gotags:description:"请求媒体类型"
    string mime = 1;
    
    //@gotags:description:"客户端信息"
    string clientInfo = 2;
    
    //@gotags:description:"目标服务器"
    string targetServer = 3;
  }

  message ResponseHeader {
    int64  responseBytes = 1;
    string statusCode    = 2;
    string proxyServer   = 3;
    string remoteAddress = 4;
  }

  message ExplainerResponse {
    string response = 1;
    int64  endTime  = 2;
    string state    = 3;
  }

  CommonInfo     commonInfo     = 1;
  RequestHeader  requestHeader  = 2;
  string         requestBody    = 3;
  ResponseHeader responseHeader = 4;
  string         responseBody   = 5;
  string         id             = 6;
}

message AsyncResponse {
  string id       = 1;
  string response = 2;
  int64  endTime  = 3;
  string state    = 4;
}

message ListConsumerDetailsReq {
  
  // @gotags: description:"分页参数"
  commons.PageReq page_req = 1;
  
  // @gotags: description:"项目id"
  string project_id = 2;
}

message ConsumerDetailsPage {
  
  // @gotags: description:"总数量"
  int32 size = 1;
  
  // @gotags: description:"消费明细列表"
  repeated ConsumerDetail consumer_details = 2;
}

message ConsumerDetail {
  
  // @gotags: description:"请求request id"
  string x_request_id = 1;
  
  // @gotags: description:"服务id"
  string service_id = 2;
  
  // @gotags: description:"服务名称"
  string service_name = 3;
  
  // @gotags: description:"消费token"
  TokenUsage token_usage = 4;
  
  // @gotags: description:"单价"
  commons.BillingConfig billing_config = 5;
  
  // @gotags: description:"总价"
  float total_price = 6;
  
  // @gotags: description:"用户名"
  string username = 7;
  
  // @gotags: description:"api key"
  string api_key = 8;
  
  // @gotags: description:"创建时间"
  int64 create_time = 9;

  enum Type {
    INNER   = 0;
    INCOME  = 1;
    CONSUME = 2;
  }
  
  // @gotags: description:"类型: 0:内部调用 1:收入 2:消费"
  Type type = 10;
}

message CountConsumptionReq {
  
  //@gotags:description:"开始时间"
  int64 start = 1;
  
  //@gotags:description:"结束时间"
  int64 end = 2;
  
  //@gotags:description:"项目id"
  string project_id = 4;
}

message CountConsumptionRsp {
  
  // @gotags: description:"消费图表"
  ConsumerDetailsChart chart = 1;
  
  // @gotags: description:"总消费"
  TotalBilling total_consumption = 2;
  
  // @gotags: description:"总收入"
  TotalBilling total_income = 3;
}

// 统计累计(消费/收入)占比
message CountRatioReq {
  
  //@gotags: description:"项目id"
  string project_id = 1;
  
  //@gotags: description:"筛选字段 消费类型: 可选值 0:内部调用 1:收入 2:消费，可多选逗号分隔"
  repeated string types = 2;
  
  //@gotags: description:"开始时间"
  int64 start = 3;
  
  //@gotags: description:"结束时间"
  int64 end = 4;
}

message CountRatioRsp {
  
  // @gotags: description:"模型"
  float model = 1;
  
  // @gotags: description:"应用"
  float application = 2;
  
  // @gotags: description:"知识"
  float knowledge = 3;
}

message ListServiceGroupPage {
           int64            size = 1;
  repeated ListServiceGroup list = 2;
}

message ListServiceGroup {
  
  // @gotags: description:"调用方"
  string username = 1;
  
  // @gotags: description:"类型 1:内部调用 2:收入 3:消费"
  int32 type = 2;
  
  // @gotags: description:"总金额"
  float total = 3;
  
  // @gotags: description:"调用次数"
  int64 call_times = 4;
  
  // @gotags: description:"总token"
  int64 total_tokens = 5;
}

message IncomeGraphItem {
  
  // @gotags: description:"秒时间戳"
  int64 time      = 1;
  float model     = 2;
  float app       = 3;
  float knowledge = 4;
}

message TotalBilling {
  
  // @gotags: description:"日总计费"
  float day = 1;
  
  // @gotags: description:"月总计费"
  float month = 2;
  
  // @gotags: description:"总计费"
  float total = 3;
}

message ConsumerDetailsChart {
  message Value {
    
    //@gotags:description:"时间节点"
    int64 time = 1;
    
    //@gotags:description:"消费金额"
    float price = 2;
  }

  
  //@gotags:description:"消费图表数据"
  repeated Value data = 1;
}

message TokenUsage {
  
  // @gotags: description:"输入提示token数"
  int64 prompt_tokens = 1;
  
  // @gotags: description:"响应token数"
  int64 completion_tokens = 2;
  
  // @gotags: description:"总token数"
  int64 total_tokens = 3;
}

message EvaluateAnswerReq {
  
  //@gotags:description:"X-Request-ID"
  string request_id = 1;
  
  //@gotags:description:"question"
  string question = 2;
  
  //@gotags:description:"answer"
  string answer = 3;
  
  //@gotags:description:"rating, like is 1, unlike is -1"
  int32 rating = 5;
  
  //@gotags:description:"来源类型; 0:SOURCE_TYPE_UNKNOW,1:SOURCE_TYPE_MODEL_CUBE,2:SOURCE_TYPE_APP_CUBE,3:SOURCE_TYPE_VLAB,4:SOURCE_TYPE_REMOTE,5:SOURCE_TYPE_KNOWLEDGE,11:SOURCE_TYPE_CUSTOM"
  serving.SourceType source_type = 6;
  
  //@gotags:description:"来源id"
  string source_id = 7;
}

message CountTokenReq {
  
  //@gotags:description:"开始时间"
  int64 start = 1;
  
  //@gotags:description:"结束时间"
  int64 end = 2;
  
  //@gotags:description:"服务id"
  string service_id = 4;
}

message GetTokenBarChartReq {
  
  //@gotags:description:"开始时间"
  int64 start = 1;
  
  //@gotags:description:"结束时间"
  int64 end = 2;
  
  //@gotags:description:"查询步长,单位s"
  int64 step = 3;
  
  //@gotags:description:"服务id"
  string service_id = 4;

  //@gotags:description:"服务id"
  enum Type {
    ALL    = 0;
    INPUT  = 1;
    OUTPUT = 2;
  }
  Type type = 5;
}

message DashboardTokenBarChart {
  message Value {
    
    //@gotags:description:"时间节点"
    string name = 1;
    
    //@gotags:description:"输出token数量"
    int64 num = 4;
  }

  
  //@gotags:description:"访问量/平均耗时柱状图数据"
  repeated Value data = 1;
}

message CountTokenRsp {
  
  //@gotags:description:"输入token数量"
  int32 input_token = 1;
  
  //@gotags:description:"输出token数量"
  int32 output_token = 2;
}

message ServiceRecordCount {
  
  //@gotags:description:"调用总量"
  int64 total = 1;
  
  //@gotags:description:"调用失败次数"
  int64 fail_count = 2;
  
  //@gotags:description:"调用失败率"
  double fail_rate = 3;
}

message AvgFirstTimeToken {
  
  //@gotags:description:"当日服务平均值"
  double todayAvg = 1;
  
  //@gotags:description:"昨日服务平均值"
  double yesterdayAvg = 2;
  
  //@gotags:description:"趋势百分比"
  string trend = 3;
}

