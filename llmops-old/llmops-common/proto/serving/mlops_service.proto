syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/serving;serving";

package serving;

import "proto/common/commons.proto";
import "proto/common/k8s_resource.proto";

service MLOpsServiceService {
  
  // 创建服务基础信息
  rpc CreateService (MLOpsServiceBaseInfo) returns (ServiceID);
  
  // 创建远程服务
  rpc CreateRemote (MLOpsRemoteServiceInfoReq) returns (RemoteService);
  
  // 创建服务版本信息
  rpc CreateServiceVersion (ServiceIDVersionInfo) returns (ServiceAndVersionID);
  
  // 更新服务基础信息
  rpc UpdateService (MLOpsServiceBaseInfo) returns (UpdateServiceRes);
  
  // 更新服务版本信息
  rpc UpdateServiceVersion (ServiceIDVersionInfo) returns (ServiceAndVersionID);
  
  // 删除服务版本信息
  rpc DeleteServiceVersion (ServiceAndVersionID) returns (ServiceAndVersionID);
  
  // 删除服务
  rpc DeleteService (ServiceID) returns (ServiceID);
  
  // 服务列表
  rpc List (ListServiceReq) returns (ServiceBaseInfoList);
  
  // 查询服务详情
  rpc QueryByID (ServiceID) returns (ServiceInfo);
  
  // 部署服务
  rpc Deploy (ServiceID) returns (DeployID);
  
  // 下线服务
  rpc Offline (ServiceID) returns (ServiceID);
  
  // 运行时信息，包括实例数量、状态，节点数量等
  rpc GetRuntimeInfo (ServiceID) returns (MLOpsSvcRuntimeInfo);
  
  // 获取容器日志
  rpc GetContainerLogs (GetMLOpsContainerLogsReq) returns (stream GetMLOpsContainerLogsRsp);
  
  // 获取容器事件
  rpc GetEvents (GetMLOpsPodEventsReq) returns (GetMLOpsPodEventsRsp);
  
  // 服务测试
  rpc CallAPI (CallAPIReq) returns (CallAPIResp);
  
  // 更新审批状态
  rpc UpdateApprovalState (UpdateApprovalStateReq) returns (UpdateApprovalStateResp);
  
  // 创建/更新yaml配置
  rpc UpsertSvcYaml (YamlConfig) returns (ServiceID);
  
  // 获取yaml配置
  rpc GetSvcYaml      (ServiceID            ) returns (YamlConfig           );
  rpc CheckNameUnique (commons.NameUniqueReq) returns (commons.NameUniqueRes);
  
  // @gotags: description:"检测服务api是否唯一"
  rpc CheckApiUnique (ApiUniqueReq) returns (ApiUniqueRes);
}
message ServiceInfo {
  
  // @gotags: description:"服务基础信息"
  MLOpsServiceBaseInfo service_info = 1;
  
  // @gotags: description:"服务版本信息"
  repeated MLOpsServiceVersionInfo service_version_infos = 2;
}

message TimeRange {
  
  // @gotags: description:"开始时间，秒级时间戳"
  optional int32 start = 1;
  
  // @gotags: description:"结束时间，秒级时间戳"
  optional int32 end = 2;
}

message UpdateApprovalStateReq {
  
  // @gotags: description:"服务id"
  string service_id = 1;
  
  // @gotags: description:"服务审批状态"
  ApprovalState approval_state = 2;
}

message UpdateApprovalStateResp {
  
  // @gotags: description:"服务id"
  string service_id = 1;
  
  // @gotags: description:"服务审批状态"
  ApprovalState approval_state = 2;
}

enum ApprovalState {
  MLOpsSvcStateApprovalInit     = 0; // 未发起审批
  MLOpsSvcStateUnderApproval    = 1; // 审批中
  MLOpsSvcStateApprovalRejected = 2; // 审批拒绝
  MLOpsSvcStateApprovalPassed   = 3; // 审批通过
}
message ListServiceReq {
  
  // @gotags: description:"创建人"
  optional string creator = 1;
  
  // @gotags: description:"来源类型"
  repeated SourceType source_types = 2;
  
  // @gotags: description:"名字"
  optional string name = 3;
  
  // @gotags: description:"集群"
  optional string cluster = 4;
  
  // @gotags: description:"时间范围（创建时间）"
  optional TimeRange create_time_range = 5;
  
  // @gotags: description:""
  repeated string service_ids = 6;
  
  // @gotags: description:"标签"
  map <string,string> source_meta_extra = 7;
  
  // @gotags:description:"服务状态:Creating(上线中),Pending(等待调度),Available(运行中),Failed(上线失败),Offline(下线),ApprovalInit(未发起审批),UnderApproval(审批中),ApprovalRejected(审批拒绝),ApprovalPassed(审批通过)"
  repeated string states    =  8;
           int32  page_size =  9;
           int32  page      = 10;
  
  //@gotags:description:"name(名称),update_time(更新时间),remain_time(剩余时长)"
           string order_by = 11;
           bool   desc     = 12;
  repeated string nodes    = 13;
}

message ServiceBaseInfoList {
  
  // @gotags: description:"服务基础信息"
  repeated MLOpsServiceBaseInfo service_infos = 3;
  
  // @gotags: description:"总数"
  int32 size = 4;
}

message ServiceAndVersionID {
  
  // @gotags: description:"服务ID"
  string service_id = 1;
  
  // @gotags: description:"服务版本ID"
  string service_version_id = 2;
  
  // @gotags: description:"服务版本id"
  int32 version = 3;
  
  // @gotags: description:"是否需要重启"
  bool need_start = 4;
}

message ServiceID {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"排序规则: version(按版本名称:v1,v2), 为空默认按权重"
  string order_by = 2;
}

message UpdateServiceRes {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"是否需要重启"
  bool need_start = 2;
}

message ApiUniqueReq {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"服务api"
  string api = 2;
}

message ApiUniqueRes {
  
  // @gotags: description:"是否唯一"
  bool is_unique = 1;
}

message RemoteService {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"绑定服务的vs url"
  string virtual_svc_url = 2;
}

message DeployID {
  
  // @gotags: description:"服务ID"
  string id = 1;
}

message ServiceIDVersionInfo {
  
  // @gotags: description:"服务ID"
  string service_id = 1;
  
  // @gotags: description:"服务版本基础信息"
  MLOpsServiceVersionInfo service_version_info = 2;
}

message UpdateServiceVersionInfoResp {
  
  // @gotags: description:"服务ID"
  string service_id = 1;
  
  // @gotags: description:"服务版本ID"
  string service_version_id = 2;
}

message MLOpsSvcRuntimeInfo {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"@Deprecated 服务状态 0下线，1上线，2创建中,3失败"
  MLOpsSvcState state = 2;
  
  // @gotags: description:"http调用信息"
  MLOpsSvcHttpCallInfo http_call_info = 3;
  
  // @gotags: description:"namespace"
  string namespace = 4;
  
  // @gotags: description:"namespace"
  string seldon_deployment_name = 5;
  
  // @gotags: description:"服务版本信息"
  repeated MLOpsSvcVersionRuntimeInfo service_versions = 6;
  
  // @gotags: description:"服务状态"
  MLOpsSvcStateInfo state_info = 7;
}

message MLOpsSvcHttpCallInfo {
  
  // @gotags: description:"gateway(istio) 地址"
  string gateway_address = 1;
  
  // @gotags: description:"gateway(istio) 路由路径"
  string virtual_url = 2;
  
  // @gotags: description:"http调用api"
  // Deprecated: 迁移到Endpoint
  repeated API              http_apis = 3;
  repeated commons.Endpoint endpoints = 5;
  
  // @gotags: description:"gateway(istio) 对外暴露的port"
  int32 gateway_node_port = 4;
}

message MLOpsSvcVersionRuntimeInfo {
  
  // @gotags: description:"服务版本ID"
  string id = 1;
  
  // @gotags: description:"服务权重"
  int32 widget = 2;
  
  // @gotags: description:"k8s deployment name"
  string k8s_deploy_name = 3;
  
  // @gotags: description:"k8s service name"
  string k8s_service_name = 4;
  
  // @gotags: description:"pod信息"
  repeated MLOpsSvcVersionPodInfo pods = 5;
}

message MLOpsSvcVersionPodInfo {
  
  // @gotags: description:"容器name"
  string name = 1;
  
  // @gotags: description:"节点ID"
  string node_name = 2;
  
  // @gotags: description:"pod状态"
  string state = 3;
  
  // @gotags: description:"容器信息"
  repeated MLOpsSvcVersionContainerInfo containers = 4;
}

message MLOpsSvcVersionContainerInfo {
  
  // @gotags: description:"容器id-对应k8s中的container name"
  string id = 1;
  
  // @gotags: description:"容器名-对应界面上展示的名称"
  string name = 2;
}

enum SourceType {
  SOURCE_TYPE_UNKNOW     =  0;
  SOURCE_TYPE_MODEL_CUBE =  1;
  SOURCE_TYPE_APP_CUBE   =  2;
  SOURCE_TYPE_VLAB       =  3;
  SOURCE_TYPE_REMOTE     =  4;
  SOURCE_TYPE_KNOWLEDGE  =  5;
  SOURCE_TYPE_CUSTOM     = 11;
}
message SourceMeta {
  
  // @gotags: description:"来源额外信息"
  map <string,string> extra = 1;
  
  // @gotags: description:"部署对象"
  string deploy_desc = 2;
  
  // @gotags: description:"是否是应用的自定义部署"
  bool is_app = 3;
  
  // @gotags: description:"服务的sdep id"
  string sdep_id = 4;
  
  // @gotags: description:"pod列表"
  repeated string pods = 5;
}

message CustomDeployCfg {
  
  // @gotags: description:"detail"
  string detail = 1;
}

message RoutingPath {
  
  // 路由路径
  string url = 1;
  
  // 端口
  string port = 2;
}

message MLOpsServiceBaseInfo {
  
  // @gotags: description:"服务ID(后端默认生成，不需要传)"
  string id = 1;
  
  // @gotags: description:"服务名字(需要传递)"
  string name = 2;
  
  // @gotags:
  // description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型、11自定义镜像(需要传)"
  SourceType source_type = 3;
  
  // @gotags: description:"描述 (可不需要传)"
  string desc = 4;
  
  // @gotags: description:"创建人(不需要传)"
  string creator = 5;
  
  // @gotags: description:"集群信息(不需要传)"
  string cluster = 6;
  
  // @gotags: description:"是否开启异步调用(不需要传)"
  bool is_async = 7;
  
  // @gotags: description:"部署模式(默认为：独立模式 部署，可不传)"
  optional DeployCfg deploy_cfg = 8;
  
  // @gotags: description:"virtual service
  // 路由路径(默认可不传，模型如果自定义了url，可从mw那边拿到)"
  string virtual_svc_url = 9;
  
  // @gotags: description:"api列表(模型对外暴露的api列表，直接传递mw拿到的数据)"
  // Deprecated: 迁移到Endpoint
  repeated API apis = 10;
  
  // @gotags: description:"endpoint列表(模型对外暴露的endpoint列表, 对上边API结构做了修改)"
  repeated commons.Endpoint endpoints = 33;
  
  // @gotags: description:"服务状态,0下线，1上线，2创建中,3失败(不需要传)"
  MLOpsSvcState state = 11;
  
  // @gotags: description:"服务版本数量(不需要传)"
  int32 version_cnt = 12;
  
  // @gotags: description:"创建时间(不需要传)"
  int32 create_time = 13;
  
  // @gotags: description:"更新时间(不需要传)"
  int32 update_time = 14;
  
  // @gotags: description:"是否共享至资产市场(可不需要传)"
  bool is_share = 15;
  
  // @gotags: description:"来源额外信息(直接传递mw拿到的数据)"
  SourceMeta source_meta = 16;
  
  // @gotags: description:"标签(直接传递mw拿到的数据)"
  map <string,commons.StringList> labels       = 17;
  ShareConfig                     share_config = 18;
  
  // @gotags: description:"安全栅栏配置"
  GuardrailsConfig guardrails_config = 20;
  
  // @gotags: description:"计费规则(默认按照token计费 ,需要调用下全局接口拿到
  // 单价/千token)"
  commons.BillingConfig billing_config = 31;
  
  // @gotags: description:"最大运行时长(默认传-1)"
  int32 limit_time = 21;
  
  // @gotags: description:"服务状态信息(不需要传)"
  MLOpsSvcStateInfo state_info = 22;
  
  // @gotags: description:"监听器使用的最大运行时长(不需要传，后端回显)"
  int32 limit_time_db = 23;
  
  // @gotags: description:"是否滚动更新(默认传true)"
  bool is_roll_update = 24;
  
  // @gotags: description:"熔断限流的总qps(后端回显)"
  double total_rate_qps = 25;
  
  // @gotags: description:"是否是文本生成类模型(后端回显)"
  bool is_text_generation = 26;
  
  // @gotags: description:"更新人，创建&更新服务版本的时候不需要传"
  string updater = 27;
  
  // @gotags: description:"空间名(不需要传，后端回显)"
  string cluster_name = 28;
  
  // @gotags: description:"project_id"
  string project_id = 29;
  
  // @gotags: description:"编辑模式，可选commom/yaml"
  string edit_model = 30;
  
  // @gotags: description:"是否允许匿名调用"
  bool enable_anonymous_call = 32;
  
  // @gotags: description:"资源汇总信息"
  ResourceSummaryInfo resource_summary_info = 34;
  
  // @gotags: description:"节点信息"
  repeated string nodes = 35;
  
  // @gotags: description:"模型类型信息"
  commons.ModelInferSpec model_infer_spec = 36;
}

message GuardrailsConfig {
  
  // @gotags: description:"是否开启安全栅栏(默认false)"
  bool is_security = 1;
  
  // @gotags: description:"安全栅栏id"
  string guardrails_id = 2;
}

message RateLimit {
  
  // @gotags: description:"限速间隔时间内允许通过的请求（qps）"
  int32 request_limit = 1;
  
  // @gotags: description:"限速间隔时间，单位为s"
  int32 unit_interval = 2;
  
  // @gotags: description:"是否开启了限流"
  bool enabled = 3;
  
  // @gotags: description:"一分钟内允许通过的token数"
  int32 token_per_minute = 4;
}

message MLOpsRemoteServiceInfoReq {
  
  // @gotags: description:"服务ID"
  string id = 1;
  
  // @gotags: description:"服务名字"
  string name = 2;
  
  // @gotags: description:"描述"
  string desc = 3;
  
  // @gotags: description:"创建人"
  string creator = 4;
  
  // @gotags: description:"virtual service 路由路径"
  string virtual_svc_url = 5;
  
  // @gotags: description:"api列表"
  // Deprecated: 迁移到Endpoint
  repeated API apis = 6;
  
  // @gotags: description:"endpoint列表"
  repeated commons.Endpoint endpoints = 22;
  
  // @gotags: description:"创建时间"
  int32 create_time = 7;
  
  // @gotags: description:"更新时间"
  int32 update_time = 8;
  
  // @gotags: description:"标签"
  map <string,commons.StringList> labels = 9;
  
  // @gotags: description:"服务限流配置"
  RateLimit rate_limit = 11;
  
  // @gotags: description:"用户限流配置"
  RateLimit user_rate_limit = 21;
  
  // @gotags: description:"安全栅栏配置"
  GuardrailsConfig guardrails_config = 12;
  
  // @gotags: description:"计费规则"
  commons.BillingConfig billing_config = 13;
  
  // @gotags: description:"最大运行时长"
  int32 limit_time = 14;
  
  // @gotags: description:"远程模型 headers"
  map <string,string> headers = 16;
  
  // @gotags: description:"远程模型 query"
  map <string,string> query_params = 17;
  
  // @gotags: description:"远程模型 prefix url"
  string remote_service_url = 18;
  
  // @gotags: description:"标签"
  SourceMeta source_meta = 19;
  
  // @gotags:
  // description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型
  // 5、知识库　11、11自定义镜像"
  SourceType source_type = 20;
  
  // @gotags: description:"是否允许匿名调用"
  bool enable_anonymous_call = 31;
}

message ShareConfig {
  
  // @gotags: description:"是否共享至资产市场"
  bool is_share = 1;
  
  // @gotags: description:"共享可查看的用户"
  repeated string share_users = 2;
  
  // @gotags: description:"共享可查看的用户组"
  repeated string share_groups = 3;
}

message MLOpsSvcStateInfo {
  
  // @gotags: description:"状态"
  string state = 1;
  
  // @gotags: description:"状态补充信息"
  string message = 2;
}

enum MLOpsSvcState {
  MLOPS_SVC_STATE_OFFLINE   = 0;
  MLOPS_SVC_STATE_AVAILABLE = 1;
  MLOPS_SVC_STATE_CREATING  = 2;
  MLOPS_SVC_STATE_FAILED    = 3;
}
enum DeployStrategy {
  DEPLOY_STRATEGY_MAIN_DEPLOY   = 0;
  DEPLOY_STRATEGY_GRAY_DEPLOY   = 1;
  DEPLOY_STRATEGY_SHADOW_DEPLOY = 2;
}
message DeployCfg {
  
  // @gotags: description:"部署策略，0独立部署，1灰度部署，2冠军挑战者部署"
  optional DeployStrategy deploy_strategy = 1;
  
  // @gotags: description:"独立部署的版本ID"
  string main_deploy_version = 2;
  
  // @gotags: description:"权重"
  map <string,int32> widget = 3;
  
  // @gotags: description:"冠军实例"
  string champion = 4;
  
  // @gotags: description:"挑战者实例"
  repeated string shadows = 5;
}

// Deprecated: 不再使用，迁移到Endpoint
message API {
  
  // @gotags: description:"端口"
  uint32 port = 1;
  
  // @gotags: description:"API类型，http，grpc等"
  string type = 2;
  
  // @gotags: description:"端口对应的API"
  repeated string url = 3;
  
  // @gotags: description:"url对应的map"
  map <string,string> url_param_map = 4;
  
  // @gotags: description:"url对应method，支持GET/POST，默认POST"
  map <string,string> url_http_method_map = 5;
  
  // @gotags: description:"api所符合的接口规范说明"
  map <string,commons.APISpec> api_spec = 6;
  
  // @gotags: description:"api功能,使用的是ModelKind与ModelSubkind的定义，但是因为循环引用的问题，这里使用枚举字符串，格式为ModelKind/ModelSubkind"
  map <string,string> api_func = 7;
  
  // @gotags: description:"默认url"
  string default_url = 8;
}

message MountCfg {
  
  // @gotags: description:"持久卷配置"
  VolumeCfg volume_cfg = 1;
  
  // @gotags: description:"挂载配置"
  repeated MountPath mount_paths = 2;
}

message VolumeCfg {
  
  // @gotags:
  // description:"持久卷类型,0hostpath,1文件系统，2模型仓库，3样本仓库，4Memory"
  MLOpsFileType file_type = 1;
  
  // @gotags: description:"路径"
  string path = 2;
  
  // @gotags: description:"持久卷对应的名字，后端负责保存与透传，不作任何处理"
  map <string,string> extra = 3;
  
  // @gotags: description:"是否开启内存限制"
  bool enable_memory_size_limit = 4;
  
  // @gotags: description:"内存大小限制"
  float memory_size_limit_gib = 5;
  
  // @gotags: description:"config map 对应的value"
  string config_map_value = 6;
}

message MountPath {
  
  // @gotags: description:"挂载路径"
  string mount_path = 1;
  
  // @gotags: description:"是否只读"
  bool readonly = 2;
}

enum MLOpsFileType {
  MLOPS_File_TYPE_HOST_PATH   = 0;
  MLOPS_File_TYPE_FILE_SYSTEM = 1;
  MLOPS_File_TYPE_MODEL_CUBE  = 2;
  MLOPS_File_TYPE_SAMPLE_CUBE = 3;
  MLOPS_File_TYPE_MEMORY      = 4;
  MLOPS_File_TYPE_CONFIG_MAP  = 5;
}
enum MLOpsVolumeType {
  VOLUME_TYPE_HOST_PATH  = 0;
  VOLUME_TYPE_PVC        = 1;
  VOLUME_TYPE_EMPTY_DIR  = 2;
  VOLUME_TYPE_CONFIG_MAP = 3;
}
message MLOpsServiceVersionInfo {
  
  // @gotags: description:"id，创建&更新服务版本的时候不需要传，后端生成"
  string id = 1;
  
  // @gotags: description:"版本，创建&更新服务版本的时候不需要传，后端生成"
  string version = 2;
  
  // @gotags: description:"来源ID 默认不需要传"
  string source_id = 3;
  
  // @gotags: description:"版本名字，保留字段，目前没用到b不需要传"
  string name = 4;
  
  // @gotags: description:"版本描述"
  string desc = 5;
  
  // @gotags: description:"创建人，创建&更新服务版本的时候不需要传"
  string creator = 6;
  
  // @gotags: description:"节点选择相关配置，默认传递  "node_choose_cfg":
  // {"strategy": 1} 随机节点"
  NodeChooseCfg node_choose_cfg = 7;
  
  // @gotags: description:"横向扩容配置 默认传:' "hpa_cfg": {"strategy":
  // 0,"replicas": 1} '"
  HPACfg hpa_cfg = 8;
  
  // @gotags: description:"gpu不限额，已废弃，移到gpu_cfg中"
  bool unlimited_gpu = 9;
  
  // @gotags: description:"可选gpu列表 废弃不需要传"
  repeated string gpu_group = 10;
  
  // @gotags: description:"容器"
  repeated MLOpsContainer containers = 11;
  
  // @gotags: description:"创建时间 不需要传"
  int32 create_time = 12;
  
  // @gotags: description:"更新时间 不需要传"
  int32 update_time = 13;
  
  // @gotags: description:"当前权重，创建&更新服务版本的时候不需要传"
  int32 widget = 14;
  
  // @gotags: description:"服务ID"
  string service_id = 15;
  
  // @gotags: description:"是否是影子服务，创建&更新服务版本的时候不需要传"
  bool shadow = 16;
  
  // @gotags: description:"是否开启gpu，默认为false"
  bool enable_gpu = 17;
  
  // @gotags: description:"gpu配置,默认都为false"
  GPUConfig gpu_cfg = 18;
  
  // @gotags: description:"deployment级别的注解"
  map <string,string> annotations = 19;
  
  // @gotags: description:"是否开启host网络，默认为false"
  bool host_network = 20;
  
  // @gotags: description:"来源额外信息"
  SourceMeta source_meta = 21;
  
  // @gotags: description:"资源配置来源(规格实例/高级配置)"
  bool is_power_rule = 22;
  
  // @gotags: description:"是否是远程模型，不需要传"
  bool is_remote_version = 23;
  
  // @gotags: description:"远程模型 headers"
  map <string,string> headers = 24;
  
  // @gotags: description:"远程模型 query"
  map <string,string> query_params = 25;
  
  // @gotags: description:"远程模型 url，默认不传"
  string remote_service_url = 26;
  
  // @gotags: description:"服务限流配置(默认限流100 "request_limit:100")"
  RateLimit rate_limit = 27;
  
  // @gotags: description:"用户限流配置"
  RateLimit user_rate_limit = 28;
  
  // @gotags: description:"节点架构，可选amd64/arm64"
  string arch = 29;
}

message ResourceGroupInfo {
  
  // @gotags: description:"资源组ID"
  string id = 1;
}

message NodeSelector {
  
  // @gotags: description:"具体的节点信息"
  string node = 1;
  
  // @gotags: description:"节点卡号选择"
  repeated string gpu_cards = 2;
}

message GPUConfig {
  
  // @gotags: description:"是否独占gpu"
  bool gpu_exclusive = 1;
  
  // @gotags: description:"是否不限额共享gpu"
  bool unlimited_gpu = 24;
}

enum ContainerType {
  CONTAINER_TYPE_MAIN    = 0;
  CONTAINER_TYPE_SIDECAR = 1;
}
enum ImageType {
  IMAGE_TYPE_CUSTOM   = 0;
  IMAGE_TYPE_PLATFORM = 1;
}
message MLOpsHttpGetAction {
  string              path       = 1;
  int32               port       = 2;
  string              host       = 3;
  string              uri_scheme = 4;
  map <string,string> headers    = 5;
}

message MLOpsProbe {
           int32              initial_delay_seconds            = 1;
           int32              timeout_seconds                  = 2;
           int32              period_seconds                   = 3;
           int32              success_threshold                = 4;
           int32              failure_threshold                = 5;
  optional int32              termination_grace_period_seconds = 7;
           MLOpsHttpGetAction http_get_action                  = 8;
}

enum GPU_CORE_UTILIZATION_POLICY {
  GPU_CORE_UTILIZATION_POLICY_DEFAULT = 0;
  GPU_CORE_UTILIZATION_POLICY_FORCE   = 1;
  GPU_CORE_UTILIZATION_POLICY_DISABLE = 2;
}
message MLOpsContainer {
  
  // @gotags: description:"id"
  string id = 1;
  
  // @gotags: description:"镜像地址"
  string image = 2;
  
  // @gotags: description:"镜像类型，0自定义类型，1平台内镜像"
  ImageType image_type = 3;
  
  // @gotags: description:"容器类型，主容器，sidecar容器"
  ContainerType container_type = 4;
  
  // @gotags: description:"资源限制"
  Resource resource = 5;
  
  // 实例规格json
  string resource_rule = 6;
  
  // @gotags: description:"环境变量"
  map <string,string> envs = 9;
  
  // @gotags: description:"启动命令"
  repeated string cmds = 10;
  
  // @gotags: description:"挂载配置"
  repeated MountCfg mount_cfg = 11;
  
  // @gotags: description:"创建时间"
  int32 create_time = 12;
  
  // @gotags: description:"更新时间"
  int32 update_time = 13;
  
  // @gotags: description:"就绪探针"
  MLOpsProbe readiness_probe = 14;
  
  // @gotags: description:"存活探针"
  MLOpsProbe liveness_probe = 15;
  
  // @gotags: description:"实例规格id"
  optional int32 resource_id = 16;
  
  // @gotags: description:"资源组列表"
  repeated ResourceGroupInfo resource_groups = 17;
  
  // @gotags: description:"算力限制策略"
  GPU_CORE_UTILIZATION_POLICY gpu_utilization_policy = 18;
}

message NodeChooseCfg {
  
  // @gotags: description:"节点选择策略，1随机节点，0固定节点"
  NodeChooseStrategy strategy = 1;
  
  // @gotags: description:"固定节点的节点ID"
  repeated NodeSelector nodes = 2;
  
  // @gotags: description:"固定节点的节点ID(string 数组格式)"
  repeated string node_ids = 3;

  //    // @gotags:
  //    description:"当前实际运行节点的节点ID，若为固定节点策略，则与nodeID一致，创建&更新服务版本的时候不需要传"
  //    string curr_node = 3;
  
  // @gotags: description:"是否开启限制调度范围"
  bool enabled = 4;
}

enum NodeChooseStrategy {
  NODE_CHOOSE_STRATEGY_FIX    = 0;
  NODE_CHOOSE_STRATEGY_RANDOM = 1;
}
message HPACfg {
  
  // @gotags: description:"横向扩容配置，0固定分片数量，1动态分片数量"
  HPAStrategy strategy = 1;
  
  // @gotags:
  // description:"若为固定分片，需要指定分片数量，在动态策略下，此分片书不等于实际分片数"
  int32 replicas = 2;
  
  //  // @gotags:
  //  description:"当前实际运行的分片数量，若为固定分片数量，则与replicas一致，创建&更新服务版本的时候不需要传"
  //  int32 currReplicas = 3;
  // @gotags: description:"动态分片配置"
  HPADynamicReplicas hpa = 4;
}

enum HPAStrategy {
  HPA_STRATEGY_DISABLED = 0;
  HPA_STRATEGY_ENABLED  = 1;
}
message HPADynamicReplicas {
  
  // @gotags: description:"最大分片数"
  int32 max_replicas = 1;
  
  // @gotags: description:"最小分片数"
  int32 min_replicas = 2;
  
  // @gotags: description:"扩容条件"
  repeated HpaMetricCfg hpaMetric_cfgs = 3;
}

enum HpaResourceType {
  HPA_RESOURCE_TYPE_CPU    = 0;
  HPA_RESOURCE_TYPE_MEMORY = 1;
}
enum HpaMetricSourceType {
  HPA_METRIC_SOURCE_TYPE_RESOURCE = 0;
  HPA_METRIC_SOURCE_TYPE_POD      = 1;
}
message HpaMetricCfg {
  
  // @gotags: description:"(已弃用)资源类型，0:cpu,1:memory, 仅指标来源为资源指标时生效"
  HpaResourceType resource_type = 1;
  
  // @gotags: description:"(已弃用)横向扩容资源阈值, 请求资源的利用率，用于资源指标"
  int32 target_average_utilization = 2;
  
  // @gotags: description:"指标来源类型，0:资源指标(k8s内建的资源指标如cpu,memory)，1:pod指标(通过自定义指标暴露)"
  HpaMetricSourceType source_type = 3;
  
  // @gotags: description:"指标名称"
  string metric_name = 4;
  
  // @gotags: description:"横向扩容资源阈值, 资源指标时为1-100整数字符串，pod指标时可以为k8s quantity"
  string target_average_value = 5;
}

// 服务列表页用到的结构
message ResourceSummaryInfo {
  
  // @gotags: description:"cpu request限制"
  int32 cpu_m_request = 1;
  
  // @gotags: description:"内存 request限制"
  float memory_gi_request = 2;
  
  // @gotags: description:"cpu limit限制"
  int32 cpu_m_limit = 3;
  
  // @gotags: description:"内存limit限制"
  float memory_gi_limit = 4;
  
  // @gotags: description:"算力"
  int32 gpu_core_percent = 5;
  
  // @gotags: description:"显存"
  float gpu_memory_gi = 6;
  
  // @gotags: description:"gpu个数限制"
  int32 gpu_count = 7;
  
  // @gotags: description:"gpu id"
  repeated string gpus = 8;
  
  // @gotags: description:"算力卡，可选ascend/nvidia"
  string gpu_type = 9;
}

message Resource {
  
  // @gotags: description:"cpu request限制"
  string cpu_request = 1;
  
  // @gotags: description:"内存 request限制"
  string memory_request = 2;
  
  // @gotags: description:"cpu limit限制"
  string cpu_limit = 5;
  
  // @gotags: description:"内存limit限制"
  string memory_limit = 6;
  
  // @gotags: description:"gpu算力"
  string gpu_core = 3;
  
  // @gotags: description:"gpu内存"
  string gpu_memory = 4;
  
  // @gotags: description:"gpu个数限制"
  string gpu_count = 7;
  
  // @gotags: description:"@deprecated cpu request限制(联调前的待删除参数)"
  string cpu = 8;
  
  // @gotags: description:"@deprecated 内存 request限制(联调前的待删除参数)"
  string memory = 9;
  
  // @gotags: description:"算力卡，可选ascend/nvidia"
  string gpu_type = 10;
  
  // @gotags: description:"算力卡，可选ascend/nvidia"
  AscendResourceCfg ascend_config = 11;
  
  // @gotags: description:"运行资源的annotation, 可用于hami指定gpu型号等"
  map <string,string> annotations = 12;
}

message AscendResourceCfg {
  
  // @gotags: description:"npu_name名字"
  string npu_name = 1;
  
  // @gotags: description:"ascend模板名字"
  string template_name = 2;
  
  // @gotags: description:"数量，当template为exclusive的时候，需要填，默认1"
  int32 cnt = 3;
}

message GetMLOpsContainerLogsRsp {
  
  // @gotags: description:"服务后台pod的日志信息"
  bytes file = 1;
}

message GetMLOpsContainerLogsReq {
  
  // @gotags: description:"容器的服务id"
  string service_id = 1;
  
  // @gotags: description:"容器的pod名字"
  string pod_name = 2;
  
  // @gotags: description:"容器的集群id"
  string container_id = 3;
}

message GetMLOpsPodEventsRsp {
  
  // @gotags: description:"服务后台pod的日志信息（事件信息）"
  bytes file = 1;
  
  // @gotags: description:"格式化的pod event"
  repeated common.PodEvent event = 2;
}

message GetMLOpsPodEventsReq {
  
  // @gotags: description:"容器的服务id"
  string service_id = 1;
  
  // @gotags: description:"容器的pod名字"
  string pod_name = 2;
}

message CallAPIReq {
  
  // @gotags: description:"容器的服务id"
  string service_id = 1;
  
  // @gotags: description:"端口"
  int32 port = 2;
  
  // @gotags: description:"请求url"
  string url = 3;
  
  // @gotags: description:"请求体"
  string post_body = 4;
  
  // @gotags: description:"协议，如https/http"
  commons.EndpointType endpoint_type = 5;
  
  // @gotags: description:"http方法，如GET/POST"
  commons.HttpMethod http_method = 6;
}

message CallAPIResp {
  
  // @gotags: description:"api返回"
  string resp_body = 1;
}

message DistributedCfg {
  
  // @gotags: description:"开启分布式"
  bool enabled = 1;
  
  // @gotags: description:"实例数量"
  int32 num_workers = 2;
  
  // @gotags: description:"开启实例节点间互斥"
  bool nodes_exclusive = 3;
  
  // @gotags: description:"开启高性能网络通信"
  bool enable_rdma_hca = 4;
}

message YamlConfig {
  
  // @gotags: description:"服务id"
  string service_id = 1;
  
  // @gotags: description:"yaml"
  string yaml = 2;
}

enum InvokingMethod {
  INVOKING_METHOD_EXTERNAL                       = 0;
  INVOKING_METHOD_ISTIO_GATEWAY_NODE_IP_PORT     = 2;
  INVOKING_METHOD_INTERNAL_ISTIO_GATEWAY_SERVICE = 1;
}
message GetServiceInvokingTemplateReq {
  InvokingMethod invoking_method = 1;
  string         service_id      = 2;
  
  // @gotags: description:"平台域名,外部调用时需要前端传递"
  string platform_host  = 3;
  string api            = 4;
  uint32 port           = 5;
  bool   anonymous_call = 6;
}

message ServiceInvokingTemplate {
           string             service_id     = 1;
           uint32             port           = 2;
           string             api            = 3;
           commons.HttpMethod method         = 5;
  repeated CodeTemplate       code_templates = 6;
}

message CodeTemplate {
  string language = 1;
  string template = 2;
}

enum DashboardTabType {
  
  // @gotags: description:"调用指标"
  DASHBOARD_TAB_TYPE_INVOKE = 0;
  
  // @gotags: description:"算力指标"
  DASHBOARD_TAB_TYPE_DEVICE = 1;
  
  // @gotags: description:"自定义指标"
  DASHBOARD_TAB_TYPE_CUSTOM = 2;
}
// UnifyResource 统一资源配置,对应pkg.PodResourceDTO
message UnifyResource {
  NodeChooseCfg  node_choose_cfg  = 1;
  int32          resource_rule_id = 2;
  string         resource_rule    = 3;
  Resource       resource         = 4;
  string         arch             = 5;
  DistributedCfg distributed      = 6;
  bool           is_advanced_mode = 7;
}

