syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

import "proto/model.proto";
import "proto/common.proto";

// 模型/模型版本 附件相关操作
service AttachmentManager {
  rpc GetAttachment    (GetAttachmentReq   ) returns (GetAttachmentRsp   );
  rpc CreateAttachment (CreateAttachmentReq) returns (CreateAttachmentRsp);
  rpc DeleteAttachment (DeleteAttachmentReq) returns (DeleteAttachmentRsp);
  rpc UpdateAttachment (UpdateAttachmentReq) returns (UpdateAttachmentRsp);
}
message CreateAttachmentReq {
  string      model_id   = 1;
  string      release_id = 2;
  Attachment  attachment = 3;
  UserContext ctx        = 4;
}

message CreateAttachmentRsp {
  Attachment attachment = 1;
}

message DeleteAttachmentReq {
  string      model_id      = 1;
  string      release_id    = 2;
  string      attachment_id = 3;
  UserContext ctx           = 4;
}

message DeleteAttachmentRsp {}

message UpdateAttachmentReq {
  string      model_id      = 1;
  string      release_id    = 2;
  string      attachment_id = 3;
  Attachment  attachment    = 4;
  UserContext ctx           = 5;
}

message UpdateAttachmentRsp {
  Attachment attachment = 1;
}

message GetAttachmentReq {
  
  //    PageReq page_req = 2;
  //    AttachmentFilter filter = 3;
  UserContext ctx = 1;
}

message GetAttachmentRsp {
           int32      total        = 1;
           int32      current_page = 2;
           int32      page_size    = 3;
  repeated Attachment attachments  = 4;
}

