syntax = "proto3";

package proto;

import "proto/data_connection.proto";
import "proto/model.proto";
import "proto/serving/mlops_service.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum KnowledgeBaseContentType {
  
  // 文本知识库
  TEXT = 0;
  
  // 知识图谱
  GRAPH = 1;
  
  // 多模态知识库
  MULTI_MODAL = 2;
  
  // 表格知识库
  TABLE = 3;
}
enum KnowledgeBaseSourceType {
  
  // 新建知识库
  FROM_SCRATCH = 0;
  
  // 注册已有的知识库
  FROM_REGISTRY = 1;
}
enum KnowledgeBaseRegistryType {
  NULL                 = 0;
  FROM_DATA_CONNECTION = 1;
  FROM_TKH             = 2;
  FROM_LANGCHAIN       = 3;
}

// message ModelServiceRef {
//   string id         = 1;
//   string host       = 2;
//   int32  port       = 3;
//   string model_id   = 4;
//   string model_name = 5;
// }

// 从数据连接导入知识库的注册信息
message KnowledgeBaseConnectionRegistry {
  
  // @gotags: description:"数据连接id"
  string id = 1;
  
  // @gotags: description:"数据连接类型"
  ConnectionType data_connection_type = 2;
  
  // @gotags: description:"数据库表"
  string table = 3;
  
  // @gotags: description:"用于排序的字段"
  repeated string sort_fields = 4;
  
  // @gotags: description:"文本字段, 作为召回内容"
  string text_field = 5;
  
  // @gotags: description:"向量字段"
  string vector_field = 6;
  
  // @gotags: description:"展示详情的字段列表，用于召回卡片中展示kv"
  repeated string display_fields = 7;
  
  // @gotags: description:"向量模型"
  ModelService vector_model = 8;
  
  // @gotags: description:"向量维度"
  int32 vector_dim = 9;
  
  // @gotags: description:"默认排序方式，是否降序"
  bool is_desc = 10;
  
  // @gotags: description:"文档名称字段，用于标识文本段来源的文档"
  string doc_field = 11;
  
  // @gotags: description:"段落/向量的ID字段"
  string id_field = 12;
  
  // @gotags: description:"向量索引名称"
  string vector_index_name = 13;
}

// 从TKH导入知识库的注册信息
message KnowledgeBaseTKHRegistry {
  
  // @gotags: description:"TKH知识库id"
  int32 id = 1;
}

enum KnowledgeBaseCreationType {
  
  // @gotags: description:"从知识库管理创建"
  FROM_MANAGEMENT = 0;
  
  // @gotags: description:"从Agent配置创建"
  FROM_AGENT_CONFIGURATION = 1;
}
enum KnowledgeBaseSceneType {
  
  // @gotags: description:"问答场景"
  SceneType_QA = 0;
  
  // @gotags: description:"安全场景"
  SceneType_SAFETY = 1;
  
  // @gotags: description:"标准化问答"
  SceneType_STANDARD = 2;
  
  // @gotags: description:"其他场景"
  SceneType_OTHERS = 99;
}
message KnowledgeBasePublishInfo {
  
  // @gotags: description:"发布的服务id"
  string id = 1;
  
  // @gotags: description:"发布的服务虚拟svc用于istio route"
  string virtual_svc_url = 2;
  
  // @gotags: description:"发布的服务名字"
  string name = 3;
  
  // @gotags: description:"发布的服务的描述"
  string desc = 4;
  
  // @gotags: description:"发布的服务qps速率限制"
  serving.RateLimit rate_limit = 5;
  
  // @gotags: description:"发布的服务是否开启安全围栏"
  bool is_security = 6;
}

message KnowledgeBase {
  
  // @gotags: description:"知识库id"
  string id = 1;
  
  // @gotags: description:"知识库名称"
  string name = 2;
  
  // @gotags: description:"知识库描述"
  string description = 3;
  
  // @gotags: description:"知识库类型"
  KnowledgeBaseContentType content_type = 4;
  
  // @gotags: description:"知识库来源"
  KnowledgeBaseSourceType source_type = 5;
  
  // @gotags: description:"知识库注册类型"
  KnowledgeBaseRegistryType registry_type = 6;
  
  // @gotags: description:"知识库图标"
  string icon = 7;
  
  // @gotags: description:"数据连接类型的注册信息"
  KnowledgeBaseConnectionRegistry connection_registry = 8;
  
  // @gotags: description:"TKH类型的注册信息"
  KnowledgeBaseTKHRegistry tkh_registry = 9;
  
  // @gotags: description:"创建用户"
  string create_user = 10;
  
  // @gotags: description:"创建时间"
  int64 create_time_mills = 11;
  
  // @gotags: description:"更新时间"
  int64 update_time_mills = 12;
  
  // @gotags: description:"禁用文档id列表"
  repeated string disabled_docs = 13;
  
  // @gotags: description:"项目id"
  string project_id = 14;
  
  // @gotags: description:"是否可见"
  bool is_visible = 15;
  
  // @gotags: description:"是否可检索"
  bool is_retrievable = 16;
  
  // @gotags: description:"创建类型"
  KnowledgeBaseCreationType creation_type = 17;
  
  // @gotags: description:"文档加工设置"
  DocProcessingConfig doc_processing_config = 18;
  
  // @gotags: description:"检索设置"
  RetrievalConfig retrieval_config = 19;
  
  // @gotags: description:"向量模型"
  ModelService vector_model = 20;
  
  // @gotags: description:"是否共享到公共空间"
  bool is_public = 21;
  
  // @gotags: description:"是否已发布"
  bool is_published = 22;
  
  // @gotags: description:"业务场景类型"
  KnowledgeBaseSceneType scene_type = 23;
  
  // @gotags: description:"记录知识库的使用次数"
  MetricsInfo metrics_info = 24;
  
  // @gotags: description:"发布信息"
  KnowledgeBasePublishInfo publish_info = 25;
}

message MetricsInfo {
  int64 visit_times   = 1;
  int64 clone_times   = 2;
  int64 execute_times = 3;
}

message AppletServiceRef {
  string application_id = 1;
  string service_id     = 2;
  string name           = 3;
}

message DocProcessingConfig {
  
  // @gotags: description:"文档加工策略来源"
  StrategyOrigin doc_processing_strategy_origin = 1;
  
  // @gotags: description:"文档分割设置"
  DocSplitConfig doc_split_config = 2;
  
  // @gotags: description:"分段增强设置"
  ChunkAugmentConfig chunk_augment_config = 3;
  
  // @gotags: description:"应用链服务；自定义策略时指定"
  AppletServiceRef applet_service = 4;
  
  // @gotags: description:"文档解析设置"
  DocLoadConfig doc_load_config = 5;
  
  // @gotags: description:"是否自动配置"
  bool auto_configured = 6;
}

enum DocLoadStrategyType {
  AUTO     = 0;
  FAST     = 1;
  HI_RES   = 2;
  OCR_ONLY = 3;
  VL_MODEL = 4;
}
message DocLoadConfig {
  
  // 文档解析策略
  DocLoadStrategyType strategy_type = 1;
  
  // 保留图片
  bool remain_figure = 20;
  
  // 保留表格
  bool remain_table = 21;
  
  // 识别数学公式
  bool recog_math_formula = 22;
  
  // 识别化学公式
  bool recog_chemical_formula = 23;
  
  // @gotags: description:"doc-engine配置文件"
  string entity_schema_conf = 24;
  
  // @gotags: description:"deprecated 文本解析服务,当策略为HI_RES或VL_MODEL时必填 弃用"
  serving.MLOpsServiceBaseInfo doc_service = 12;
  
  // @gotags: description:"图像理解模型，当策略为VL_MODEL时必填"
  ModelService image_model = 13;
  
  // @gotags: description:"文本解析模型服务,当策略为HI_RES或VL_MODEL时必填"
  ModelService doc_model = 14;
}

message RetrievalConfig {
  
  // @gotags: description:"检索策略来源"
  StrategyOrigin retrieval_strategy_origin = 1;
  
  // @gotags: description:"检索策略"
  KnowledgeBaseRetrieveStrategy strategy = 2;
  
  // @gotags: description:"召回参数"
  RecallParams recall_params = 3;
  
  // @gotags: description:"重排参数"
  RerankParams rerank_params = 4;
  
  // @gotags: description:"应用链服务；自定义策略时指定" TODO
  string chain_service = 5;
  
  // @gotags: description:"是否自动配置"  
  bool auto_configured = 6;
  
  // @gotags: description:"切片上下文切片数量"
  int32 context_num = 7;
}

enum StrategyOrigin {
  
  // @gotags: description:"平台预置策略"
  PRESET = 0;
  
  // @gotags: description:"自定义策略"
  CUSTOM     = 1;
  DOC_ENGINE = 2;
}
enum DocSplitStrategyType {
  
  // @gotags: description:"按字符等价切分"
  CHARACTER = 0;
  
  // @gotags: description:"递归切分"
  RECURSIVE = 1;
  
  // @gotags: description:"按页切分"
  PAGE = 2;
}
message DocSplitConfig {
  
  // @gotags: description:"分段切分标识符"
  string separator = 1;
  
  // @gotags: description:"分段最大长度"
  int32 chunk_size = 2;
  
  // @gotags: description:"分段重叠长度"
  int32 chunk_overlap = 3;
  
  // @gotags: description:"分段切分标识符列表"
  repeated string separators = 4;
  
  // @gotags: description:"分段策略"
  DocSplitStrategyType split_strategy_type = 5;
  
  // @gotags: description:"是否禁用分段合并"
  bool disable_chunk_merging = 6;
}

message ChunkAugmentConfig {
  
  // @gotags: description:"是否启用文本增强"
  bool enabled = 1;
  
  // @gotags: description:"问题数量"
  int32 question_num = 2;
  
  // @gotags: description:"摘要数量"
  int32 summary_num = 3;
  
  // @gotags: description:"文本生成模型"
  ModelService text_model = 4;
  
  // @gotags: description:"表格描述数量"
  int32 table_desc_num = 5;
  
  // @gotags: description:"表格摘要数量"
  int32 table_summary_num = 6;
  
  // @gotags: description:"用于生成问题的提示词"
  string question_prompt = 7;
  
  // @gotags: description:"用于生成摘要的提示词"
  string summary_prompt = 8;
  
  // @gotags: description:"用于生成表格描述的提示词"
  string table_desc_prompt = 9;
  
  // @gotags: description:"用于生成表格摘要的提示词"
  string table_summary_prompt = 10;
  
  // @gotags: description:"是否开启图像增强"
  bool image_enable = 11;
  
  // @gotags: description:"图像理解模型"
  ModelService image_model = 13;
  
  // @gotags: description:"图像描述数量"
  int32 image_desc_num = 14;
  
  // @gotags: description:"用于生成图像描述提示词"
  string image_desc_prompt = 15;
}

enum KnowledgeBaseRetrieveStrategy {
  
  // 基于文本向量相似度的检索
  VECTOR = 0;
  
  // 全文检索
  FULL_TEXT = 1;
  
  // 混合检索
  MIXED               = 2;
  DOC_ENGINE_RETRIEVE = 3;
}
message RecallParams {
  
  // @gotags: description:"topk，限制召回个数"
  int32 top_k = 1;
  
  // @gotags: description:"分数阈值, 取值范围[0,1)"
  float score_threshold = 2;
  
  // @gotags: description:"检索策略"
  KnowledgeBaseRetrieveStrategy strategy = 3;
}

message RerankParams {
  
  // @gotags: description:"topk，限制召回个数"
  int32 top_k = 1;
  
  // @gotags: description:"分数阈值, 取值范围[0,1)"
  float score_threshold = 2;
  
  // @gotags: description:"rerank模型"
  ModelService model = 3;
  
  // @gotags: description:"是否保留不符合分数阈值的结果"
  bool retain_unqualified = 4;
}

message Document {
  
  // @gotags: description:"文档id"
  string doc_id = 1;
  
  // @gotags: description:"文档名称"
  string doc_name = 2;
  
  // @gotags: description:"文档路径"
  string file_path = 3;
  
  // @gotags: description:"文档大小"
  int32 file_size_bytes = 4;
  
  // @gotags: description:"文档格式"
  string file_format = 5;
  
  // @gotags: description:"上传时间"
  int64 upload_time_mills = 6;
  
  // @gotags: description:"字符数"
  int32  num_chars         = 7;
  string file_md5          = 8;
  string knowledge_base_id = 9;
  
  // @gotags: description:"表格配置, 仅表格文档时有效"
  TableConfig table_config = 10;
  
  // @gotags: description:"数据来源"
  DocumentFileSource document_file_source = 11;
  
  // @gotags: description:"语料来源配置；仅来源为语料时使用"
  CorpusConfig corpus_config = 12;
  
  // @gotags: description:"文档加工设置"
  DocProcessingConfig doc_processing_config = 13;
}

message DisplayInfo {
  string name  = 1;
  string value = 2;
}

enum ChunkSourceType {
  
  // @gotags: description:"自动生成"
  SOURCE_TYPE_GENERATED = 0;
  
  // @gotags: description:"手动创建"
  SOURCE_TYPE_CREATED = 1;
}
enum OriginalContentType {
  
  // @gotags: description:"原文文本"
  ORIGINAL_CONTENT_TYPE_TEXT = 0;
  
  // @gotags: description:"原文表格"
  ORIGINAL_CONTENT_TYPE_TABLE = 1;
  
  // @gotags: description:"表格行数据的json"
  ORIGINAL_CONTENT_TYPE_JSON = 2;
  
  // @gotags: description:"原文中的图像"
  ORIGINAL_CONTENT_TYPE_IMAGE = 3;
  
  // @gotags: description:"按页分割新增，表示切片类型为markdown"
  ORIGINAL_CONTENT_TYPE_MARKDOWN = 4;
}
enum AugmentedChunkType {
  
  // @gotags: description:"知识增强-摘要"
  AUGMENTED_CHUNK_TYPE_SUMMARY = 0;
  
  // @gotags: description:"知识增强-问题"
  AUGMENTED_CHUNK_TYPE_QUESTION = 1;
  
  // @gotags: description:"知识增强-表格描述"
  AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION = 20;
  
  // @gotags: description:"知识增强-表格摘要"
  AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY = 21;
  
  // @gotags: description:"知识增强-问题改写"
  AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE = 3;
  
  // @gotags: description:"知识增强-未定义类型，用于手动新建的增强切片"
  AUGMENTED_CHUNK_TYPE_UNDEFINED = 4;
  
  // @gotags: description:"表格知识库的索引字段作为AgumentedChunk"
  AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD = 5;
  
  // @gotags: description:"上下文内容(用于图片chunk的增强)"
  AUGMENTED_CHUNK_TYPE_CONTEXT = 6;
  
  // @gotags: description:"知识增强-图片描述"
  AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION = 7;
  
  // @gotags: description:"知识增强-自定义增强"
  AUGMENTED_CHUNK_TYPE_CUSTOM = 8;
}
enum DocElementType {
  DocElementType_FigureCaption     =  0;
  DocElementType_NarrativeText     =  1;
  DocElementType_ListItem          =  2;
  DocElementType_Title             =  3;
  DocElementType_Address           =  4;
  DocElementType_Table             =  5;
  DocElementType_PageBreak         =  6;
  DocElementType_Header            =  7;
  DocElementType_Footer            =  8;
  DocElementType_UncategorizedText =  9;
  DocElementType_Image             = 10;
  DocElementType_Formula           = 11;
}
message Point {
  float x = 1;
  float y = 2;
}

message Coordinates {
  
  // @gotags: description:"坐标点，四个点表示一个矩形区域，大于四个点的区域表示一个多边形区域"
  repeated Point points = 1;
  
  // @gotags: description:"版面宽度"
  float layout_width = 2;
  
  // @gotags: description:"版面宽度"
  float layout_height = 3;
}

message DocElement {
  
  // @gotags: description:"文档元素id"
  string element_id = 1;
  
  // @gotags: description:"文档元素类型"
  DocElementType type = 2;
  
  // @gotags: description:"文本"
  string text = 3;
  
  // @gotags: description:"元数据"
  DocElementMetadata metadata = 4;
  
  // @gotags: description:"父元素id列表，自顶向底排序"
  repeated string parent_ids = 5;
  
  // @gotags: description:"父元素内容列表，自顶向底排序"
  repeated string parent_texts = 6;
  
  // @gotags: description:"额外属性"
  map <string,string> extra = 7;
}

message DocElementMetadata {
  
  // @gotags: description:"文档元素所在页码"
  int32 page_number = 1;
  
  // @gotags: description:"文档元素区域坐标"
  Coordinates coordinates = 2;
  
  // 检测置信度
  float detection_class_prob = 3;
  
  // @gotags: description:"如果是表格类型的element，此字段存放html格式的文本"
  string text_as_html = 4;
  string parent_id    = 5;
}

message Chunk {
  
  // @gotags: description:"chunk id"
  string id = 1;
  
  // @gotags: description:"原文内容"
  string content = 2;
  
  // @gotags: description:"chunk对应的文档元素id列表,仅原文类型的chunk需要该属性"
  repeated string element_ids = 3;
  
  // @gotags: description:"chunk来源类型"
  ChunkSourceType source_type = 4;
  
  // @gotags: description:"chunk原文内容类型"
  OriginalContentType content_type = 5;
  
  // @gotags: description:"是否禁用向量化索引"
  bool disable_vector_indexing = 6;
  
  // @gotags: description:"是否禁用全文索引"
  bool disable_full_text_indexing = 7;
  
  // @gotags: description:"知识增强的分段列表"
  repeated AugmentedChunk augmented_chunks = 8;
  
  // @gotags: description:"流转中产生的额外属性，不会进行存储"
  map <string,string> extra = 9;
  
  // @gotags: description:"是否被修改编辑过"
  bool edited = 10;
}

message AugmentedChunk {
  
  // @gotags: description:"chunk id"
  string id = 1;
  
  // @gotags: description:"增强内容"
  string content = 2;
  
  // @gotags: description:"chunk来源类型"
  ChunkSourceType source_type = 3;
  
  // @gotags: description:"增强类型"
  AugmentedChunkType augmented_type = 4;
  
  // @gotags: description:"是否禁用向量化索引"
  bool disable_vector_indexing = 5;
  
  // @gotags: description:"是否禁用全文索引"
  bool disable_full_text_indexing = 6;
  
  // @gotags: description:"是否被修改编辑过"
  bool edited = 7;
}

// ChunkInfo 基于Chunk扩增了一些相关信息
message ChunkInfo {
  
  // @gotags: description:"分段"
  Chunk chunk = 1;
  
  // @gotags: description:"分段的展示信息"
  repeated DisplayInfo display_infos = 2;
  
  // @gotags: description:"文档元素, 预留"
  repeated DocElement elements = 3;
}

// 数据库的表信息
message TableInfo {
  
  // @gotags: description:"表名"
  string name = 1;
  
  // @gotags: description:"表的行数"
  int64 rows = 2;
  
  // @gotags: description:"表描述信息，包含表schema"
  string description = 3;
}

enum TableFieldDataType {
  
  // @gotags: description:"字符串"
  STRING = 0;
  
  // @gotags: description:"整数"
  INT = 1;
  
  // @gotags: description:"时间"
  TIME = 2;
  
  // @gotags: description:"浮点数"
  NUMBER = 3;
  
  // @gotags: description:"布尔值"
  BOOL = 4;
  
  // @gotags: description:"图像"
  IMAGE = 5;
}
// 表格知识库的表格配置
message TableConfig {
  
  // @gotags: description:"sheet名称"
  string sheet_name = 1;
  
  // @gotags: description:"表头行号"
  int32 header_row = 2;
  
  // @gotags: description:"数据起始行号"
  int32 data_starting_row = 3;
  
  // @gotags: description:"表格总行数"  
  int32 num_rows = 4;
  
  // @gotags: description:"字段配置"
  repeated TableFieldConfig fields_config = 5;
}

enum DocumentFileSource {
  
  // @gotags: description:"本地上传"
  LOCAL_FILE = 0;
  
  // @gotags: description:"语料数据集"
  CORPUS_DATA_SET = 1;
}
message CorpusConfig {
  int32  set_id     = 1;
  int32  version_id = 2;
  string dir        = 3;
}

message TableFieldConfig {
  
  // @gotags: description:"字段的列号"
  int32 idx = 1;
  
  // @gotags: description:"字段名称"
  string name = 20;
  
  // @gotags: description:"原字段名称"
  string ori_name = 21;
  
  // @gotags: description:"字段描述"
  string desc = 3;
  
  // @gotags: description:"字段类型"
  TableFieldDataType data_type = 4;
  
  // @gotags: description:"是否启用索引"
  bool enable_index = 5;
  
  // @gotags: description:"字段的索引权重, (0,1]"
  float weight = 6;
  
  // @gotags: description:"是否需要召回内容"
  bool enable_content_recall = 7;
  
  // @gotags: description:"字段示例"
  string example = 8;
}

