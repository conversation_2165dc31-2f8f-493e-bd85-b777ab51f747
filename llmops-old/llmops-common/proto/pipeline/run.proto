syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/pipeline;pipeline";

package pipeline;

import "proto/common/k8s_resource.proto";
import "proto/serving/mlops_service.proto";
import "google/protobuf/any.proto";

message Component {
  
  // @gotags: description:"组件id"
  string id = 1;
  
  // @gotags: description:"组件名称"
  string name = 2;
  
  // @gotags: description:"组件名称En"‘
  string name_en = 16;
  
  // @gotags: description:"区分自定义组件和系统组件"
  bool use_defined = 3;
  
  // @gotags: description:"组件来源信息,保留字段后续拓展"
  TemplateSource component_source = 4;
  
  // @gotags: description:"组件描述"
  string desc = 5;
  
  // @gotags: description:"组件描述En"
  string desc_en = 17;
  
  // @gotags: description:"组件输入描述，key为输入名称，对应节点输入参数名，value为输入对应pod中的路径，对应节点输入参数路径"
  map <string,string> inputs = 6;
  
  // @gotags: description:"组件输出描述，key为输出名称，对应节点输出参数名，value为输出对应pod中的路径，对应节点输出参数路径"
  map <string,string> outputs = 7;
  
  // @gotags: description:"组件参数描述，key为参数名，对应节点输入参数（变量）的key，value参数默认值，对应节点输入参数（变量）的默认显示值"
  map <string,string> params = 8;
  
  // @gotags: description:"组件pod labels设置，后端保留字段"
  map <string,string> node_selector = 9;
  
  // @gotags: description:"组件pod labels设置，后端保留字段"
  map <string,string> annotations = 10;
  
  // @gotags: description:"组件pod labels设置，后端保留字段"
  map <string,string> labels = 11;
  
  // @gotags: description:"持久卷配置，后端保留字段"
  repeated common.VolumeCfg volume = 12;
  
  // @gotags: description:"组件容器信息，后端保留字段"
  common.Container container = 13;
  
  // @gotags: description:"默认高级配置，后端保留字段"
  ComponentAdvancedConfig component_advanced_config = 14;
  
  // @gotags: description:"是否使用主机网络，后端保留字段"
  bool host_network = 15;
}

message ComponentAdvancedConfig {
  
  // @gotags: description:"是否开启缓存功能"
  bool enable_cached = 1;
  
  // @gotags: description:"最大并行数"
  int32 parallelism = 2;
  
  // @gotags: description:"k8s serviceaccount"
  string service_account_name = 3;
  
  // @gotags: description:"任务重试策略"
  RetryStrategy retry_strategy = 4;
}

message RetryStrategy {
  
  // @gotags: description:"最大重试数"
  int32 limit = 1;

  enum RetryPolicy {
    RetryPolicyAlways    = 0;
    RetryPolicyOnFailure = 1;
    RetryPolicyOnError   = 2;
  }
  
  // @gotags: description:"重试策略，RetryPolicyAlways = 0;RetryPolicyOnFailure = 1;RetryPolicyOnError = 2;"
  RetryPolicy retry_policy = 2;
}

message StoragePath {
  string persistent_volume_claim = 1;
  string path                    = 2;
}

message TaskFlow {
  
  // @gotags: description:"节点信息"
  repeated Node nodes = 1;
  
  // @gotags: description:"节点间依赖信息"
  repeated Dependency dependencies = 2;
  
  // @gotags: description:"任务pod是否使用主机网络"
  bool HostNetwork = 3;
}

message TaskFlowV2 {
  
  // @gotags: description:"节点信息"
  repeated NodeV2 nodes = 1;
  
  // @gotags: description:"节点间依赖信息"
  repeated Dependency dependencies = 2;
  
  // @gotags: description:"任务pod是否使用主机网络"
  bool host_network = 3;
}

enum TaskSourceType {
  NONE               = 0;
  PIPELINE           = 1;
  MODEL_TRAINING     = 2;
  MODEL_EVALUATE     = 3;
  CORPUS_PROCESSING  = 4;
  CORPUS_EVAL        = 5;
  MODEL_DOWNlOADER   = 6;
  MODEL_QUANTIZATION = 7;
}
message Dependency {
  
  // @gotags: description:"节点id"
  string node_id = 1;
  
  // @gotags: description:"该节点依赖的节点id集合，依赖的节点即为该节点的上游节点，即只有id集合对应的节点运行完成才会执行该节点"
  repeated string dep_node_id = 2;
}

message TimingConfig {
  
  // @gotags: description:"定时调度cron表达式"
  string cron = 1;

  enum Status {
    Disable = 0;
    Enable  = 1;
    Unknown = 2;
  }
  
  // @gotags: description:"定时器状态 0: 关闭 1:启动 即定时任务是否开启"
  Status status = 2;
  enum ScheduleType {
    Simple   = 0;
    Advanced = 1;
  }
  
  // @gotags: description:"调度设置 0：基础 1：高级 用于前端展示"
  ScheduleType schedule_type = 3; //simple, advanced
  
  // @gotags: description:"最大并行度"
  int32 maximum_concurrent = 4;
}

message Node {
  
  // @gotags: description:"节点id，需要由前端或任务提交方随机生成"
  string id = 1;
  
  // @gotags: description:"存储模板相关信息"
  TemplateSource template_source = 2;
  
  // @gotags: description:"节点输入制品相关信息"
  repeated ArtifactConfig inputs = 3;
  
  // @gotags: description:"节点输出制品相关信息"
  repeated ArtifactConfig outputs = 4;
  
  // @gotags: description:"节点参数"
  map <string,string> params = 5;
  
  // @gotags: description:"节点名称"
  string name = 6;
  
  // @gotags: description:"节点位置，用于前端展示节点位置"
  Position position = 7;
  
  // @gotags: description:"节点容器信息，自定义组件需要用户填写容器相关信息，其他组件由后端直接根据组件信息填充"
  common.Container container = 8;
  
  // @gotags: description:"是否使用主机网络，后端相关参数"
  bool host_network = 9;
  
  // @gotags: description:"根据label选择限制任务执行节点，后端相关参数"
  map <string,string> node_selector = 10;
  
  // @gotags: description:"任务pod annotations设置，后端相关参数"
  map <string,string> annotations = 11;
  
  // @gotags: description:"任务pod labels设置，后端相关参数"
  map <string,string> labels = 12;
  
  // @gotags: description:"工作流高级配置，后端相关参数"
  ComponentAdvancedConfig advanced_config = 13;
  
  // @gotags: description:"持久卷配置，后端保留字段"
  repeated common.VolumeCfg volume = 14;
  
  // @gotags: description:"资源配置来源(规格实例/高级配置)"
  bool is_power_rule = 15;
}

message NodeV2 {
  
  // @gotags: description:"节点id，需要由前端或任务提交方随机生成"
  string id = 1;
  
  // @gotags: description:"存储模板相关信息"
  TemplateSource template_source = 2;
  
  // @gotags: description:"节点输入制品相关信息"
  repeated ArtifactConfig inputs = 3;
  
  // @gotags: description:"节点输出制品相关信息"
  repeated ArtifactConfig outputs = 4;
  
  // @gotags: description:"节点参数"
  map <string,string> params = 5;
  
  // @gotags: description:"节点名称"
  string name = 6;
  
  // @gotags: description:"节点位置，用于前端展示节点位置"
  Position position = 7;
  
  // @gotags: description:"节点容器信息，自定义组件需要用户填写容器相关信息，其他组件由后端直接根据组件信息填充"
  common.Container container = 8;
  
  // @gotags: description:"是否使用主机网络，后端相关参数"
  bool host_network = 9;
  
  // @gotags: description:"工作流高级配置，后端相关参数"
  ComponentAdvancedConfig advanced_config = 13;
  
  // @gotags: description:"持久卷配置，后端保留字段"
  repeated common.VolumeCfg      volume         = 14;
           serving.UnifyResource unify_resource = 15;
}

message Position {
  
  // @gotags: description:"x轴坐标"
  int32 x = 1;
  
  // @gotags: description:"y轴坐标"
  int32 y = 2;
}

message ArtifactConfig {
  
  // @gotags: description:"制品名称"
  string name = 1;
  
  // @gotags: description:"制品在容器中的路径，对于节点输入，工作流会在该节点执行前将在From指定的上一节点的输出移动到该路径，对于节点输出，工作流会在该节点执行结束后，将容器中的该路径作为输出"
  string path = 2;
  
  // @gotags: description:"仅作用于节点输入，指定节点的输入来源于上游节点的某个输出"
  From from = 3;
  
  // @gotags: description:"制品类型 INPUT = 0;OUTPUT = 1"
  ArtifactType type = 4;
}

enum ArtifactType {
  INPUT  = 0;
  OUTPUT = 1;
}
message From {
  
  // @gotags: description:"来源节点的id，必须是依赖节点"
  string node_id = 1;
  
  // @gotags: description:"来源节点的输出的制品名称，上游节点可能会有多个输出制品，需要指定具体输出制品的名称"
  string artifact_name = 2;
}

message Run {
  
  // @gotags: description:"run id"
  string id = 1;
  
  // @gotags: description:"项目id"
  string project_id = 2;
  
  // @gotags: description:"源任务信息"
  TaskSource task_source = 3;
  
  // @gotags: description:"任务流程图"
  TaskFlow task_flow = 4;
  
  // @gotags: description:"运行开始时间"
  int64 start_time = 5;
  
  // @gotags: description:"运行结束时间"
  int64 end_time = 6;

  enum Status {
    Failed    = 0;
    Error     = 1;
    Succeeded = 2;
    Running   = 3;
  }
  
  // @gotags: description:"运行当前状态"
  Status status = 7;
  
  // @gotags: description:"运行调度时间"
  int64 scheduled_time = 8;
  
  // @gotags: description:"运行步骤信息"
  repeated Step   steps       =  9;
           string create_user = 10;
}

message RunV2 {
  
  // @gotags: description:"run id"
  string id = 1;
  
  // @gotags: description:"项目id"
  string project_id = 2;
  
  // @gotags: description:"源任务信息"
  TaskSource task_source = 3;
  
  // @gotags: description:"任务流程图"
  TaskFlowV2 task_flow = 4;
  
  // @gotags: description:"运行开始时间"
  int64 start_time = 5;
  
  // @gotags: description:"运行结束时间"
  int64 end_time = 6;

  enum Status {
    Failed    = 0;
    Error     = 1;
    Succeeded = 2;
    Running   = 3;
  }
  
  // @gotags: description:"运行当前状态"
  Status status = 7;
  
  // @gotags: description:"运行调度时间"
  int64 scheduled_time = 8;
  
  // @gotags: description:"运行步骤信息"
  repeated Step   steps       =  9;
           string create_user = 10;
}

message TaskSource {
  
  // @gotags: description:"任务类型 1:工作流 2:模型训练 3:模型评估 4:语料加工"
  TaskSourceType source_type = 1;
  
  // @gotags: description:"源任务id"
  string source_id = 2;
  
  // @gotags: description:"源任务名称"
  string source_name = 3;
  
  // @gotags: description:"源任务相关的信息，用于任务提交方存储各源任务相关的独特信息"
  string source_info = 4;
}

message TemplateSource {
  
  // @gotags: description:"模板类型 PIPELINE = 1; MODEL_TRAINING = 2; MODEL_EVALUATE = 3; CORPUS_PROCESSING = 4"
  TaskSourceType template_type = 1;
  
  // @gotags: description:"模板id"
  string source_id = 2;
  
  // @gotags: description:"模板名称"
  string source_name = 3;
  
  // @gotags: description:"模板相关的信息，用于任务提交方存储各模板相关的独特信息"
  string source_info = 4;
}

message Step {
  string node_id = 1;
  string name    = 2;

  enum Phase {
    Pending   = 0;
    Succeeded = 1;
    Running   = 2;
    Error     = 3;
    Skipped   = 4;
    Failed    = 5;
  }
           Phase               phase      =  3;
           int64               start_time =  4;
           int64               end_time   =  5;
  repeated Artifact            outputs    =  6;
  repeated Artifact            inputs     =  7;
           map <string,string> params     = 11;
           map <string,string> metrics    = 12;
}

message Artifact {
  string       name           = 1;
  string       container_path = 2;
  string       s3_path        = 3;
  ArtifactType type           = 4;
  StoragePath  storage_path   = 5;
}

message PageReq {
  int32  page_size = 1;
  int32  page      = 2;
  string order_by  = 3;
  bool   is_desc   = 4;
}

