syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/pipeline;pipeline";

package pipeline;

import "proto/common/commons.proto";
import "proto/pipeline/run.proto";
import "proto/common/k8s_resource.proto";

service RunService {
  rpc GetRunStepArtifact (GetRunStepArtifactReq) returns (stream GetRunStepArtifactRsp); // TODO: design stream or abandoned
  
  // Deprecated use V2
  rpc SubmitRun         (SubmitRunReq        ) returns (       RunId               );
  rpc SubmitRunV2       (SubmitRunReqV2      ) returns (       RunId               );
  rpc RetryRun          (RetryRunReq         ) returns (       commons.EmptyRsp    );
  rpc TerminateRun      (TerminateRunReq     ) returns (       commons.EmptyRsp    );
  rpc GetRun            (GetRunReq           ) returns (       Run                 );
  rpc GetRunV2          (GetRunReq           ) returns (       RunV2               );
  rpc GetRunStepLogs    (GetRunStepLogsReq   ) returns (stream GetRunStepLogsRsp   );
  rpc GetRunStepPodInfo (GetRunStepPodInfoReq) returns (       GetRunStepPodInfoRsp);
  rpc GetRunStepEvents  (GetRunStepEventsReq ) returns (       GetRunStepEventsRsp );
  rpc GetEventsByLabels (LabelEventsReq      ) returns (       GetRunStepEventsRsp );
  rpc DeleteRun         (DeleteRunReq        ) returns (       DeleteRunRsq        );
  rpc DeleteRunBatch    (DeleteRunBatchReq   ) returns (       DeleteRunBatchRsp   );
  rpc ListRuns          (ListRunsReq         ) returns (       RunsPage            );
  rpc ListRunsV2        (ListRunsReq         ) returns (       RunsPageV2          );
}
message GetRunStepArtifactReq {
  string runId    = 1;
  string node_id  = 2;
  string artifact = 3;
}

message GetRunStepArtifactRsp {
  string file_name = 2;
  bytes  file      = 1;
}

message SubmitRunReq {
  string        project_id    = 1;
  SubmitRunBody SubmitRunBody = 2;
}

message SubmitRunBody {
  
  // @gotags: description:"源任务信息"
  TaskSource task_source = 1;
  
  // @gotags: description:"任务流程图"
  TaskFlow task_flow = 2;
}

message SubmitRunReqV2 {
  string          project_id      = 1;
  SubmitRunBodyV2 submit_run_body = 2;
}

message SubmitRunBodyV2 {
  
  // @gotags: description:"源任务信息"
  TaskSource task_source = 1;
  
  // @gotags: description:"任务流程图"
  TaskFlowV2 task_flow = 2;
}

message RunId {
  
  // @gotags: description:"运行记录id"
  string run_id = 1;
}

message RetryRunReq {
  string run_id = 2;
}

message TerminateRunReq {
  string run_id = 1;
}

message GetRunReq {
  string run_id = 1;
}

message GetRunStepLogsReq {
  string run_id  = 1;
  string node_id = 2;
  int32  limit   = 3;
}

message GetRunStepLogsRsp {
  bytes log = 1;
}

message GetRunStepPodInfoReq {
  string run_id  = 1;
  string node_id = 2;
}

message GetRunStepPodInfoRsp {
  bytes yaml = 1;
}

message GetRunStepEventsReq {
  string run_id     = 1;
  string node_id    = 2;
  string project_id = 3;
  string tenant_id  = 4;
}

message GetRunStepEventsRsp {
  repeated common.PodEvent event = 1;
}

message DeleteRunReq {
  string id = 1;
}

message DeleteRunRsq {
  string id = 1;
}

message DeleteRunBatchReq {
  
  // @gotags: description:"删除任务id集合"
  repeated string ids = 1;
}

message DeleteRunBatchRsp {
  
  // @gotags: description:"删除任务id集合"
  repeated string ids = 1;
}

message ListRunsReq {
  string  project_id = 1;
  PageReq page_req   = 2;
  message Filter {
    repeated string         source_id   = 1;
             TaskSourceType source_type = 2;
    repeated Run.Status     state       = 3;
             int64          from        = 4;
             int64          to          = 5;
             string         create_user = 6;
             string         source_name = 7;
  }

  Filter filter = 6;
}

message RunsPage {
  
  // @gotags: description:"run列表"
  repeated Run runs = 2;
  
  // @gotags: description:"run总数"
  int64 size = 3;
}

message RunsPageV2 {
  
  // @gotags: description:"run列表"
  repeated RunV2 runs = 2;
  
  // @gotags: description:"run总数"
  int64 size = 3;
}

message LabelEventsReq {
  map <string,string> labels = 1;
}

