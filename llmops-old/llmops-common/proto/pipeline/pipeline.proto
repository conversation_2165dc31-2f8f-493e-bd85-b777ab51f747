syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/pipeline;pipeline";

package pipeline;

import "proto/pipeline/run.proto";

message Pipeline {
  
  // @gotags: description:"工作流id"
  string id = 1;
  
  // @gotags: description:"工作流名称"
  string name = 2;
  
  // @gotags: description:"创建用户"
  string create_user = 3;
  
  // @gotags: description:"创建时间"
  int64 create_time = 4;
  
  // @gotags: description:"描述"
  string desc = 5;
  
  // @gotags: description:"所属项目id"
  string project_id = 6;
  
  // @gotags: description:"版本数"
  int64 version_count = 7;
}

message PipelineVersion {
  
  // @gotags: description:"工作流版本id"
  string id = 1;
  
  // @gotags: description:"所属工作流id"
  string pipeline_id = 2;
  
  // @gotags: description:"版本名称"
  string name = 3;
  
  // @gotags: description:"流程图信息"
  TaskFlow task_flow = 4;
  
  // @gotags: description:"创建时间"
  int64 create_time = 8;
  
  // @gotags: description:"更新时间"
  int64 update_time = 11;
  
  // @gotags: description:"版本描述"
  string desc = 16;

  enum SchedulingStyle {
    IMMEDIATE = 0;
    TIMING    = 1;
  }
  
  // @gotags: description:"调度类型 0:手动调度 1:定时调度"
  SchedulingStyle style = 9;
  
  // @gotags: description:"定时调度配置"
  TimingConfig timing_config = 10;
}

message PipelineVersionV2 {
  
  // @gotags: description:"工作流版本id"
  string id = 1;
  
  // @gotags: description:"所属工作流id"
  string pipeline_id = 2;
  
  // @gotags: description:"版本名称"
  string name = 3;
  
  // @gotags: description:"流程图信息"
  TaskFlowV2 task_flow = 4;
  
  // @gotags: description:"创建时间"
  int64 create_time = 8;
  
  // @gotags: description:"更新时间"
  int64 update_time = 11;
  
  // @gotags: description:"版本描述"
  string desc = 16;

  enum SchedulingStyle {
    IMMEDIATE = 0;
    TIMING    = 1;
  }
  
  // @gotags: description:"调度类型 0:手动调度 1:定时调度"
  SchedulingStyle style = 9;
  
  // @gotags: description:"定时调度配置"
  TimingConfig timing_config = 10;
}

