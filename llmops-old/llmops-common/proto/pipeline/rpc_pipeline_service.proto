syntax = "proto3";

option go_package = "transwarp.io/aip/llmops-common/pb/pipeline;pipeline";

package pipeline;

import "proto/common/commons.proto";
import "proto/pipeline/pipeline.proto";
import "proto/pipeline/run.proto";

service PipelineService {
  rpc CreatePipeline                 (CreatePipelineReq             ) returns (PipelineId              );
  rpc ModifyPipeline                 (ModifyPipelineReq             ) returns (PipelineId              );
  rpc CreatePipelineVersion          (CreatePipelineVersionReq      ) returns (PipelineVersionId       );
  rpc SavePipelineVersion            (SavePipelineVersionReq        ) returns (PipelineVersionId       );
  rpc SavePipelineVersionV2          (SavePipelineVersionReqV2      ) returns (PipelineVersionId       );
  rpc GetPipeline                    (GetPipelineReq                ) returns (Pipeline                );
  rpc GetPipelineVersion             (GetPipelineVersionReq         ) returns (PipelineVersion         );
  rpc GetPipelineVersionV2           (GetPipelineVersionReq         ) returns (PipelineVersionV2       );
  rpc DeletePipeline                 (DeletePipelineReq             ) returns (commons.DeleteRsp       );
  rpc DeletePipelines                (DeletePipelinesReq            ) returns (commons.DeleteRsp       );
  rpc DeletePipelineVersion          (DeletePipelineVersionReq      ) returns (commons.DeleteRsp       );
  rpc ListPipelines                  (ListPipelinesReq              ) returns (PipelinePage            );
  rpc ListPipelineVersions           (ListPipelineVersionsReq       ) returns (PipelineVersionPage     );
  rpc DeletePipelineVersions         (DeletePipelineVersionsReq     ) returns (commons.DeleteRsp       );
  rpc StartPipelineVersion           (StartPipelineVersionReq       ) returns (PipelineVersionId       );
  rpc StopPipelineVersion            (StopPipelineVersionReq        ) returns (PipelineVersionId       );
  rpc OncePipelineVersion            (OncePipelineVersionReq        ) returns (OncePipelineVersionRsp  );
  rpc CreatePipelineVersionByYaml    (CreatePipelineVersionByYamlReq) returns (PipelineVersionId       );
  rpc ExportPipelineVersion          (ExportPipelineVersionReq      ) returns (ExportPipelineVersionRsp);
  rpc GetComponents                  (GetComponentsReq              ) returns (ComponentPage           );
  rpc CheckPipelineNameUnique        (CheckPipelineReq              ) returns (CheckPipelineRsp        );
  rpc CheckPipelineVersionNameUnique (CheckPipelineVersionReq       ) returns (CheckPipelineVersionRsp );
}
message CreatePipelineReq {
  CreatePipelineBody CreatePipelineBody = 1;
  string             projectId          = 3;
}

message CreatePipelineBody {
  
  // @gotags: description:"工作流名称"
  string name = 1;
  
  // @gotags: description:"工作流描述"
  string desc = 2;
}

message CreatePipelineVersionReq {
  CreatePipelineVersionBody create_pipeline_version_body = 1;
}

message PipelineVersionId {
  
  // @gotags: description:"工作流id"
  string pipeline_id = 2;
  
  // @gotags: description:"工作流版本id"
  string pipeline_version_id = 1;
}

message CreatePipelineVersionBody {
  
  // @gotags: description:"所属工作流id"
  string name = 1;
  
  // @gotags: description:"工作流版本名称"
  string desc = 2;
  
  // @gotags: description:"工作流版本描述"
  string pipeline_id = 3;
}

message SavePipelineVersionReq {
  string                  pipeline_version_id        = 2;
  SavePipelineVersionBody save_pipeline_version_body = 3;
}

message SavePipelineVersionBody {
  string name = 2;
  string desc = 3;
  
  // @gotags: description:"工作流流程图"
  TaskFlow task_flow = 1;
  
  // @gotags: description:"调度类型 0:手动调度 1:定时调度"
  PipelineVersion.SchedulingStyle style = 6;
  
  // @gotags: description:"定时调度配置"
  TimingConfig timing_config = 7;
}

message SavePipelineVersionReqV2 {
  string                    pipeline_version_id        = 1;
  SavePipelineVersionBodyV2 save_pipeline_version_body = 2;
}

message SavePipelineVersionBodyV2 {
  string name = 2;
  string desc = 3;
  
  // @gotags: description:"工作流流程图"
  TaskFlowV2 task_flow = 1;
  
  // @gotags: description:"调度类型 0:手动调度 1:定时调度"
  PipelineVersion.SchedulingStyle style = 6;
  
  // @gotags: description:"定时调度配置"
  TimingConfig timing_config = 7;
}

message ModifyPipelineReq {
  string             id                   = 1;
  ModifyPipelineBody modify_pipeline_body = 2;
}

message ModifyPipelineBody {
  
  // @gotags: description:"工作流名称"
  string name = 1;
  
  // @gotags: description:"工作流描述"
  string desc = 2;
}

message ListPipelinesReq {
  string project_id = 1;
}

message PipelineId {
  
  // @gotags: description:"创建pipeline id"
  string pipeline_id = 1;
}

message PipelinePage {
  
  // @gotags: description:"工作流总数"
  int32 size = 1;
  
  // @gotags: description:"工作流列表"
  repeated Pipeline pipelines = 2;
}

message GetPipelineReq {
  string id = 1;
}

message ListPipelineVersionsReq {
  string pipeline_id = 1;
}

message PipelineVersionPage {
  
  // @gotags: description:"版本总数"
  int32 size = 1;
  
  // @gotags: description:"版本列表"
  repeated PipelineVersion pipelineVersions = 2;
}

message DeletePipelineReq {
  string id = 1;
}

message DeletePipelinesReq {
  
  // @gotags: description:"删除工作流id集合"
  repeated string ids = 1;
}

message DeletePipelineVersionReq {
  string id = 1;
}

message DeletePipelineVersionsReq {
  
  // @gotags: description:"删除工作流版本id集合"
  repeated string ids = 1;
}

message ExportPipelineVersionReq {
  string id = 1;
}

message ExportPipelineVersionRsp {
  bytes  pipelineVersionFile = 1;
  string fileName            = 2;
}

message CreatePipelineVersionByYamlReq {
  CreatePipelineVersionByYamlBody create_pipeline_version_by_yaml_body = 2;
}

message CreatePipelineVersionByYamlBody {
  
  // @gotags: description:"工作流名称"
  string name = 1;
  
  // @gotags: description:"工作流描述"
  string desc = 2;
  
  // @gotags: description:"yaml文件内容"
  string yaml = 3;
  
  // @gotags: description:"所属pipeline id"
  string pipeline_id = 4;
}

message CheckPipelineReq {
  CheckPipelineBody body       = 1;
  string            project_id = 2;
}

message CheckPipelineBody {
  string name = 1;
}

message CheckPipelineVersionReq {
  CheckPipelineVersionBody body = 1;
}

message CheckPipelineVersionBody {
  string name        = 1;
  string pipeline_id = 2;
}

message CheckPipelineRsp {}

message CheckPipelineVersionRsp {}

message GetComponentsReq {}

message ComponentPage {
  
  // @gotags: description:"组件数量"
  int32 size = 1;
  
  // @gotags: description:"组件列表"
  repeated Component components = 2;
}

message StartPipelineVersionReq {
  string id = 1;
}

message StopPipelineVersionReq {
  string id = 1;
}

message OncePipelineVersionReq {
  string id = 1;
}

message GetPipelineVersionReq {
  string id = 1;
}

message OncePipelineVersionRsp {
  
  // @gotags: description:"生成run id"
  string run_id = 1;
}

