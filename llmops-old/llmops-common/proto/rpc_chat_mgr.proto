syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

import "proto/common.proto";
import "proto/chat.proto";

// ChatManager 对于llm对话管理
service ChatManager {
  rpc ReadConversations   (ReadConversationsReq  ) returns (ReadConversationsRsp  );
  rpc DeleteConversations (DeleteConversationsReq) returns (DeleteConversationsRsp);
  rpc RatingConversation  (RatingConversationReq ) returns (RatingConversationRsp );
}
message ReadConversationsReq {
  UserContext ctx           = 1;
  string      id            = 2;
  string      deployment_id = 3;
}

message ReadConversationsRsp {
  repeated Conversation conversations = 1;
}

message DeleteConversationsReq {
  UserContext ctx           = 1;
  string      id            = 2;
  string      deployment_id = 3;
}

message DeleteConversationsRsp {}

message RatingConversationReq {
  QARating rating = 1;
}

message RatingConversationRsp {}

