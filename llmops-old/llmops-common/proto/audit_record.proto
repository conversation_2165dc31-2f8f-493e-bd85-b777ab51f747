syntax = "proto3";

package proto;

// import "proto/data_connection.proto";
// import "proto/model.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

enum AuditRecordOperateType {
  CREATE = 0;
  UPDATE = 1;
  DELETE = 2;
}
enum AuditRecordModule {
  
  // @gotags: description:"空间管理"
  AuditRecordModule_SPACE = 0;
  
  // @gotags: description:"模型"
  AuditRecordModule_MODEL = 1;
  
  // @gotags: description:"应用"
  AuditRecordModule_APP = 2;
  
  // @gotags: description:"知识"
  AuditRecordModule_KNOWLEDGE = 3;
  
  // @gotags: description:"语料"
  AuditRecordModule_CORPUS = 4;
  
  // @gotags: description:"运维工具"
  AuditRecordModule_TOOL = 5;
}
// 功能菜单 包含了空间信息, 成员管理, 模型管理, 模型体验, 模型训练, 模型评估, 提示工程, 应用管理, 应用体验, 应用插件, 自定义算子, 应用评估, 知识管理, 服务部署, 安全中心, 代码实例, 工作流
enum AuditRecordSubModule {
  
  // Space
  // @gotags: description:"空间信息管理"
  AuditRecordSubModule_SPACE_INFO_MANAGEMENT = 0;
  
  // @gotags: description:"成员管理"
  AuditRecordSubModule_SPACE_MEMBER_MANAGEMENT = 1;
  
  // Model
  // @gotags: description:"模型管理"
  AuditRecordSubModule_MODEL_MANAGEMENT = 10;
  
  // @gotags: description:"模型体验"
  AuditRecordSubModule_MODEL_EXPERIENCE = 11;
  
  // @gotags: description:"模型训练"
  AuditRecordSubModule_MODEL_TRAINING = 12;
  
  // @gotags: description:"模型评估"
  AuditRecordSubModule_MODEL_EVALUATION = 13;
  
  // @gotags: description:"提示工程"
  AuditRecordSubModule_PROMPT_ENGINEERING = 14;
  
  // App
  // @gotags: description:"应用管理"
  AuditRecordSubModule_APP_MANAGEMENT = 20;
  
  // @gotags: description:"应用体验"
  AuditRecordSubModule_APP_EXPERIENCE = 21;
  
  // @gotags: description:"应用插件管理"
  AuditRecordSubModule_APP_PLUGIN_MANAGEMENT = 22;
  
  // @gotags: description:"自定义算子"
  AuditRecordSubModule_CUSTOM_OPERATOR = 23;
  
  // @gotags: description:"应用评估"
  AuditRecordSubModule_APP_EVALUATION = 24;
  
  // Knowledge
  // @gotags: description:"知识管理"
  AuditRecordSubModule_KNOWLEDGE_MANAGEMENT = 30;
  
  // @gotags: description:"知识体验"
  AuditRecordSubModule_KNOWLEDGE_EXPERIENCE = 31;
  
  // Corpus
  // @gotags: description:"语料管理"
  AuditRecordSubModule_CORPUS_MANAGEMENT = 40;
  
  // @gotags: description:"语料处理"
  AuditRecordSubModule_CORPUS_PROCESSING = 41;
  
  // @gotags: description:"语料标注"
  AuditRecordSubModule_CORPUS_LABELING = 42;
  
  // @gotags: description:"语料评测"
  AuditRecordSubModule_CORPUS_EVALUATING = 43;
  
  // Tool
  // @gotags: description:"服务部署"
  AuditRecordSubModule_SERVICE_DEPLOYMENT = 50;
  
  // @gotags: description:"安全中心"
  AuditRecordSubModule_SECURITY_CENTER = 51;
  
  // @gotags: description:"代码实例"
  AuditRecordSubModule_CODE_EXAMPLES = 52;
  
  // @gotags: description:"工作流管理"
  AuditRecordSubModule_WORKFLOW_MANAGEMENT = 53;
}
message AuditRecord {
  
  // @gotags: description:"事件id" 
  int64 id = 1;
  
  // @gotags: description:"模块"
  AuditRecordModule module = 2;
  
  // @gotags: description:"功能菜单"
  AuditRecordSubModule sub_module = 3;
  
  // @gotags: description:"操作类型"
  AuditRecordOperateType op_type = 4;
  
  // @gotags: description:"事件描述"
  string event = 5;
  
  // @gotags: description:"操作人"
  string user = 6;
  
  // @gotags: description:"操作时间"
  int64 time_mills = 7;
  
  // @gotags: description:"事件所匹配的API"
  AuditRecordAPIConfig matched_api = 8;
  
  // @gotags: description:"实际的请求路径"
  string req_path = 9;
  
  // @gotags: description:"详细信息，包含请求参数、响应等"
  map <string,string> details = 10;
  
  // @gotags: description:"项目id"
  string project_id = 11;
}

// 事件对应的API配置
message AuditRecordAPIConfig {
  
  // @gotags: description:"模块"
  AuditRecordModule module = 1;
  
  // @gotags: description:"功能菜单"
  AuditRecordSubModule sub_module = 2;
  
  // @gotags: description:"操作类型"
  AuditRecordOperateType op_type = 3;
  
  // @gotags: description:"请求方法"
  string api_method = 4;
  
  // @gotags: description:"请求路径的匹配表达式"
  string api_path = 5;
  
  // @gotags: description:"接口说明"
  string api_desc = 6;
  
  // @gotags: description:"事件描述的Go Template"
  string go_template = 7;
  
  // @gotags: description:"接口的gateway模块"
  string api_module = 8;
  
  // @gotags: description:"Go Template语法的判断条件，用于筛选符合事件描述的接口请求；需要渲染true或false；置空不筛选"
  string condition = 9;
}

