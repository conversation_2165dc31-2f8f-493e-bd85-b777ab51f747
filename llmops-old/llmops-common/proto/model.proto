syntax = "proto3";

package proto;

import "proto/relation.proto";
import "proto/dynamic_param.proto";
import "google/protobuf/any.proto";
import "proto/serving/mlops_service.proto";
import "proto/common/k8s_resource.proto";
import "proto/common/commons.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// DataType  Data types supported for input and output tensors.
enum DataType {
  DATA_TYPE_INVALID = 0;
  
  // 各个不同框架的类型映射     Triton	    TensorRT	TensorFlow	    ONNX        PyTorch	    NumPy
  DATA_TYPE_BOOL   =  1; // TYPE_BOOL	kBOOL	    DT_BOOL	        BOOL	    kBool	    bool
  DATA_TYPE_UINT8  =  2; // TYPE_UINT8	kUINT8	    DT_UINT8	    UINT8	    kByte	    uint8
  DATA_TYPE_UINT16 =  3; // TYPE_UINT16	----	    DT_UINT16	    UINT16	    ----	    uint16
  DATA_TYPE_UINT32 =  4; // TYPE_UINT32	----	    DT_UINT32	    UINT32	    ----	    uint32
  DATA_TYPE_UINT64 =  5; // TYPE_UINT64	----	    DT_UINT64	    UINT64	    ----	    uint64
  DATA_TYPE_INT8   =  6; // TYPE_INT8	kINT8	    DT_INT8	        INT8	    kChar	    int8
  DATA_TYPE_INT16  =  7; // TYPE_INT16	----	    DT_INT16	    INT16	    kShort	    int16
  DATA_TYPE_INT32  =  8; // TYPE_INT32	kINT32	    DT_INT32	    INT32	    kInt	    int32
  DATA_TYPE_INT64  =  9; // TYPE_INT64	----	    DT_INT64	    INT64	    kLong	    int64
  DATA_TYPE_FP16   = 10; // TYPE_FP16	kHALF	    DT_HALF	        FLOAT16	    ----	    float16
  DATA_TYPE_FP32   = 11; // TYPE_FP32	kFLOAT	    DT_FLOAT	    FLOAT	    kFloat	    float32
  DATA_TYPE_FP64   = 12; // TYPE_FP64	----	    DT_DOUBLE	    DOUBLE	    kDouble	    float64
  DATA_TYPE_BYTES  = 13; // TYPE_STRING	----	    DT_STRING	    STRING	    ----	    dtype(object)
  DATA_TYPE_BF16   = 14; // TYPE_BF16	----	    ----	        ----	    ----	    ----
}
// AssertType  模型市场判断当前模型类型： 内嵌 / 共享
enum AssetType {
  ASSET_SHARED   = 0; // 共享
  ASSET_EMBEDDED = 1; // 内嵌
}
// Model 模型仓库的统一抽象定义，仅包括一些模型的描述信息，用于汇总具有相同功能模型的不同版本
message Model {
           string           id                =  1; // 模型ID，后端自动生成
           string           name              =  2; // 模型名称，创建后支持修改
           ModelDomain      domain            =  3; // 模型领域信息，标识模型的场景、储存方式、是否有状态
           ModelDetail      detail            =  4; // 模型详情
           ModelStats       stats             =  5; // 模型的版本/部署统计信息，实时进行统计
  repeated ModelApi         apis              =  6; // 模型提供的接口信息，限制模型版本间API保持一致
  repeated Attachment       attachments       =  7; // 模型附件信息，包括模型的使用说明，评估报告等各种相关文件、链接等
           TrainingTemplate training_template =  8;
           string           project_id        =  9;
           AssetType        asset_type        = 10; // 模型资产的类型，分为： 内嵌模型和共享模型
           string           source_project_id = 11;
           bool             is_system_init    = 14; //是否为系统初始化的模型
}

message SpaceInfo {
  map <string,bool> industry_labels = 1; // 空间机构相对应的行业标签
  map <string,bool> is_selected     = 2; // 是否被选中为某行业的首页精选
}

message SetSelectedReq {
           string industry = 1; // 行业
  repeated string ids      = 2; // ids
}

// ModelStats 模型的统计信息（查询时动态生成，不存数据库）
message ModelStats {
           ModelReleaseBase latest_release   = 1; // 模型的最新版本
           ModelReleaseBase baseline_release = 2; // 模型的基线版本
           int32            release_count    = 3; // 模型版本数量
  repeated ModelReleaseBase releases_info    = 4; // 模型版本的基础信息
           ModelUsageCount  usage_count      = 5;
           float            disk_usage       = 6;
}

// ModelUsageCount 模型使用的计数信息，这些计数可以通过接口更新
message ModelUsageCount {
  int32  deploys_count     = 1; // 模型部署数量
  int32  views_count       = 2; // 模型访问量，打开模型详情,或者进行体验时计数增加
  int32  downloads_count   = 3; // 模型下载量, 使用模型clone时计数增加
  int32  invokes_count     = 4; // 模型推理调用计数
  int32  trainings_count   = 5; // 模型训练使用计数
  int32  evaluations_count = 6; // 模型评估使用计数
  string model_id          = 7;
}

// ModelKind 模型场景分类，如果是组合模型，model_kind指定为主场景，实无法确定时使用multi
enum ModelKind {
  MODEL_KIND_UNSPECIFIED = 0; // 未知场景
  MODEL_KIND_MULTI       = 1; // 多模态场景
  MODEL_KIND_CV          = 2; // 图片 -- 计算机视觉
  MODEL_KIND_OCR         = 3; // 图片 -- 文本识别
  MODEL_KIND_NLP         = 4; // 文本 -- 自然语言处理
  MODEL_KIND_ML          = 5; // 结构化二维表 -- 机器学习
  MODEL_KIND_SR          = 6; // 语音识别
}
// ModelSubKind 模型场景子类型
// 序号请遵循 [n][mm] 形式,其中:
//  n 对应 上级ModelKind的序号
// mm 请使用自增序号，从 01 - 99
// ！！！请勿随意调整已有类型序号，以避免前端适配错乱
enum ModelSubKind {
  MODEL_SUB_KIND_UNSPECIFIED               =   0;
  MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE       = 101; // 文字转图片
  MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT       = 102; // 图片转文字
  MODEL_SUB_KIND_MULTI_VIDEO_UNDERSTANDING = 103;
  MODEL_SUB_KIND_CV_CLS                    = 201; // 图片分类
  MODEL_SUB_KIND_CV_DET                    = 202; // 图片目标检测
  MODEL_SUB_KIND_CV_INS_SEG                = 203; // 图片实例分割
  MODEL_SUB_KIND_CV_SEM_SEG                = 204; // 图片语义分割
  MODEL_SUB_KIND_CV_OCR                    = 205; // 图片字符识别
  MODEL_SUB_KIND_CV_IMAGE_EMBEDDING        = 206; // 图片向量
  MODEL_SUB_KIND_OCR_COMMON                = 301; // 通用文本识别
  MODEL_SUB_KIND_OCR_RECEIPTS              = 302; // 票据识别
  MODEL_SUB_KIND_NLP_SINGLE_CLS            = 401; // 单项文本分类
  MODEL_SUB_KIND_NLP_MULTI_CLS             = 402; // 多项文本分类
  MODEL_SUB_KIND_NLP_ENTITY_RECOGNITION    = 403; // 实体识别
  MODEL_SUB_KIND_NLP_SEMANTIC_RELATION     = 404; // 语义关系识别
  MODEL_SUB_KIND_NLP_SENTIMENT_CLS         = 405; // 情感分类
  MODEL_SUB_KIND_NLP_TEXT_GENERATION       = 406; // 文本生成
  MODEL_SUB_KIND_NLP_TEXT_VECTOR           = 407; // 文本向量
  MODEL_SUB_KIND_NLP_RERANKING             = 408; // re-ranking model
  MODEL_SUB_KIND_NLP_TEXT_COMPLETION       = 409; // 文本补全
  MODEL_SUB_KIND_ML_BINARY_CLS             = 501; // 二分类
  MODEL_SUB_KIND_ML_MULTI_CLS              = 502; // 多分类
  MODEL_SUB_KIND_ML_REGRESSION             = 503; // 回归
  MODEL_SUB_KIND_ML_CLUSTER                = 504; // 聚类
  MODEL_SUB_KIND_SR_STT                    = 601; // 语音转文字
  MODEL_SUB_KIND_SR_TTS                    = 602; // 文字转语音
}
// TrainingTemplate 不同算法类型(训练模板)的大模型,微调参数有所不同,需要在模型层级进行标识
enum TrainingTemplate {
  TRAINING_TEMPLATE_UNSPECIFIED = 0;
  TRAINING_TEMPLATE_BLOOM       = 1;
  TRAINING_TEMPLATE_CHATGLM2    = 2;
  TRAINING_TEMPLATE_LLAMA       = 3;
  TRAINING_TEMPLATE_LLAMA2      = 4;
  TRAINING_TEMPLATE_FALCON      = 5;
  TRAINING_TEMPLATE_INTERNLM    = 6;
  TRAINING_TEMPLATE_STARCODER   = 7;
}
// ModelType 为模型的存储类型
enum ModelType {
  MODEL_TYPE_UNSPECIFIED = 0; // 未知
  MODEL_TYPE_FILE        = 1; // 原子文件模型
  MODEL_TYPE_IMAGE       = 2; // 原子镜像模型
  MODEL_TYPE_ENSEMBLE    = 3; // 组合文件模型
}
// ModelSubType 为模型存储类型的补充描述，指示了存储子类型
enum ModelSubType {
  MODEL_SUB_TYPE_UNSPECIFIED = 0;
  
  // 文件模型子类型
  MODEL_SUB_TYPE_FILE_PMML        = 11;
  MODEL_SUB_TYPE_FILE_ONNX        = 12;
  MODEL_SUB_TYPE_FILE_PYTHON      = 13;
  MODEL_SUB_TYPE_FILE_TRANSFORMER = 14; // 能够使用 HuggingFace transformers 库进行load的预训练模型
  MODEL_SUB_TYPE_FILE_PEFT        = 15; // 能够使用 HuggingFace peft 库进行load的微调模型
  MODEL_SUB_TYPE_FILE_PKL         = 16;
  MODEL_SUB_TYPE_FILE_OTHERS      = 17; // 不指定框架，不初始化相关文件
  
  // 镜像模型子类型
  MODEL_SUB_TYPE_DOCKER_CUSTOM   = 21; // 用户自定义镜像
  MODEL_SUB_TYPE_DOCKER_VLAB     = 22; // Vlab 产生的镜像模型
  MODEL_SUB_TYPE_DOCKER_DISCOVER = 23; // Discover 产生的镜像模型
  
  // 组合模型子类型
  MODEL_SUB_TYPE_ENSEMBLE_CVAT   = 31; // 训练平台自动生成
  MODEL_SUB_TYPE_ENSEMBLE_CUSTOM = 32; // 用户自行上传
  MODEL_SUB_TYPE_ENSEMBLE_SELDON = 33; // MLops 推理图生成
}
enum ModelAlgorithm {
  MO                           = 0; // 未指定具体算法类型，默认值
  ALGORITHM_TYPE_XGBOOST       = 1; // XGBoost算法
  ALGORITHM_TYPE_RANDOM_FOREST = 2; // 随机森林算法

  // ... 其他算法类型
}
// ModelScheduleMode 模型的调度模式，取决于模型是否会保留上下文信息（也即有无状态）
enum ModelScheduleMode {
  MODEL_SCHEDULE_MODE_STATELESS = 0; // 无状态模型， 默认值
  MODEL_SCHEDULE_MODE_STATEFUL  = 1; // 有状态模型，模型请求负载均衡时需进行额外设计
}
// DataModality 模型数据类型定义
message DataModality {
  ModelKind    kind     = 1; // 数据模态
  ModelSubKind sub_kind = 2; // 任务类型
}

// ModelDomain 定义了模型的分类信息, 包括存储形式（type）/ 模型功能场景（kind） 两个维度
// TODO 后续可能需要新增字段标识具体的算法类型，e.g. Yolo, LoRA, BERT etc.
message ModelDomain {
           ModelKind         kind            = 1; // 输入的数据模态
           ModelSubKind      sub_kind        = 2; // 输入的任务类型
           ModelType         type            = 3; // 模型类型，[文件、镜像、组合]
           ModelSubType      subtype         = 4; // 模型子类型，[onnx, pmml, py]
           ModelScheduleMode schedule_mode   = 5; // 模型调度方式
           ModelKind         output_kind     = 6; // 组合模型的[输出的数据模态]
           ModelSubKind      output_sub_kind = 7; // 组合模型的[输出的任务类型]
           ModelAlgorithm    algorithm       = 8; // 模型所用算法类型
  repeated DataModality      components      = 9; // 组合模型中所含原子模型的任务类型的列表
}

// ModelApi 为模型单个接口的格式描述
message ModelApi {
           string     path    = 1;
  repeated ModelParam inputs  = 2; // 该接口的所有输入参数
  repeated ModelParam outputs = 3; // 该接口的所有输出参数
}

// ModelParamFormat 某个输入或输出参数的具体格式
enum ModelParamFormat {
  MODEL_PARAM_FORMAT_NONE         = 0; // 参数未指定具体格式. 默认值
  MODEL_PARAM_FORMAT_NHWC         = 1; // 图像的HWC三维Tensor格式
  MODEL_PARAM_FORMAT_NCHW         = 2; // 图像的CHW三维Tensor格式
  MODEL_PARAM_FORMAT_IMAGE_BINARY = 3; // 存放图像的二进制数据，具体解析方式在IMAGE_DESC中定义
  MODEL_PARAM_FORMAT_IMAGE_DESC   = 4; // 符合CV模型推理请求的统一规范的json字符串，包含对于二进制图像数据的描述
  MODEL_PARAM_FORMAT_IMAGE_RESULT = 5; // 符合CV模型推理结果的统一规范的json字符串
  
  // NLP对话模型，标准请求格式：
  //  {
  //    "query": "你叫什么？",
  //    "history": [
  //      {"Q":"你是谁？", "A":"我是Solar"},
  //      {"Q":"猜猜我是谁？", "A":"不猜"}
  //    ]
  //  }
  // 其中：
  // * query 为当前对话轮次用户输入的内容
  // * history 为用户当前会话的历史对话记录， Q为用户提出的问题或指令， A为模型响应的内容； 数组中对象顺序和当前会话中的问答顺序一致， 按照时间升序排列
  MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ = 6;
  
  // NLP对话模型，流式响应格式
  // 以HTTP 的 text/event-stream 格式返回的 无固定结构的 text 文本
  MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP = 7;
}
// ModelParam 定义了模型所需的一个输入或输出参数
message ModelParam {
           string           name          = 1; // 输入输出参数的名称
           DataType         data_type     = 2; // 输入输出参数中包含的数据类型
           ModelParamFormat format        = 3; // 输入输出参数中包含的数据的组织形式
  repeated int32            dims          = 4; // 当调用此模型的API时必须提供的 输入/输出 张量（Tensor）的尺寸(维度)
           bool             optional      = 5; // 该参数在进行推理时是否必须提供, 默认为False
           string           default_value = 6; // 该参数在进行推理时的默认值、示例数据或者模板数据
}

// ModelDetail 定义了模型的详细描述信息
message ModelDetail {
           string              desc           = 1; // desc 模型描述信息
           string              user_id        = 2; // user_id 模型创建者
           string              thumbnail      = 3; // thumbnail 模型展示缩略图
           bool                is_public      = 4; // is_public 模型是否为公开模型
           int64               create_time_ms = 5; // create_time_ms 创建时间戳
           int64               update_time_ms = 6; // update_time_ms 更新时间戳
           map <string,string> labels         = 7; // labels 模型标签列表
           map <string,string> baselines      = 8; // baselines 模型的基线版本列表，key 为 CPUArch (name), value 为对应的版本ID
  repeated Relation            relations      = 9; // relations 模型关联关系
}

// Attachment 定义了模型/模型版本相关的附件，e.g. 训练数据集、评估数据集、模型使用说明、模型评估报告等...
message Attachment {
  string id         = 1; // id 为每个附件的UUID
  string name       = 2; // name 为每个附件的用户自定义名称
  string path       = 3; // path 定义了附件具体的存储路径，可以为 本地路径： file://xxx 或 S3路径 s3://xxx
  string desc       = 4; // desc 关于该附件的描述信息
  string project_id = 5;
  string creator    = 6;
}

enum ModelReleaseStatus {
  MODEL_RELEASE_STATUS_UNSPECIFIED           = 0; // 状态未知
  MODEL_RELEASE_STATUS_UNEVALUATED           = 1; // 未评估
  MODEL_RELEASE_STATUS_EVALUATING            = 2; // 评估中
  MODEL_RELEASE_STATUS_FAILED_EVALUATION     = 3; // 评估失败
  MODEL_RELEASE_STATUS_SUCCESSFUL_EVALUATION = 4; // 评估成功
}
// ModelReleaseDetail
message ModelReleaseDetail {
           string              desc                   = 1; // 描述信息
           int64               model_size             = 7; // 模型的大小（原子模型文件大小 | 镜像大小 | 组合模型包大小）
           int64               create_time_ms         = 2; // 创建时间
           int64               update_time_ms         = 3; // 更新时间
           map <string,string> labels                 = 4; // 自定义标签
  repeated Relation            relations              = 5; // 关联关系
  repeated Attachment          attachments            = 6; // 模型版本附件
           map <string,string> computation_attributes = 8; // 一些计算属性，如模型的输入输出维度等,约定一些key值，输出维度key为output_vector_dims value为逗号分隔的数字, 输入最大token key为input_max_token
}

// RepoAddrProtocol 统一的文件上传协议
enum RepoAddrProtocol {
  REPO_ADDR_PROTOCOL_UNSPECIFIED = 0;
  REPO_ADDR_PROTOCOL_SFS         = 1; // SophonFileSystem 对应 datamgr 的文件管理接口， sfs://autocv-datamgr-service/temp/320cc1c91ed6dfd8b10e81dfdff70ef3/model.onnx
  REPO_ADDR_PROTOCOL_DOCKER      = 2; // 对应 docker images 的 repo tag , e.g. docker://***********/aip/base/metrics-server:v0.3.6
  REPO_ADDR_PROTOCOL_S3          = 3; // 对应 Amazon s3 对象存储接口， e.g. s3://autocv-ossgw-service/public/dir/file
  REPO_ADDR_PROTOCOL_NFS         = 4; // 对应 NFS 文件路径 e.g. nfs://**************/data/temp/file
  REPO_ADDR_PROTOCOL_FTP         = 5; // 对应 FTP 文件路径 e.g. ftp://**************/data/temp/file
  REPO_ADDR_PROTOCOL_MODELSCOPE  = 6; // 对应modelscope协议， e.g. modelscope://model_repo
  REPO_ADDR_PROTOCOL_HUGGINGFACE = 7; //对应huggingface协议， e.g. huggingface://model_repo
}
// FilePath 上传数据的信息
message FilePath {
  RepoAddrProtocol protocol = 1;
  string           Host     = 2;
  string           Path     = 3;
}

message TrainingTemplateConfig {
  string name    = 1;
  string content = 2;
}

// ModelReleaseBase 所有模型版本均具有的公共信息
message ModelReleaseBase {
  string id          =  1; // 模型版本UUID, 后端自动生成
  string name        =  2; // 模型版本用户自定名称， 用户可修改
  string version     =  3; // 模型版本的自增版本号，不可修改
  string repo        =  4; // 模型版本关联的模型存储地址， 支持不同类型的repo地址,详见 RepoAddrProtocol
  string model_id    =  5; // 所属模型ID
  bool   is_baseline = 11; // 是否是基线版本（每个模型的每种硬件架构仅支持一个基线版本）
  
  // ModelReleaseStatus status = 6;          // 状态阶段
           ModelReleaseDetail     detail            =  7; // 模型版本详细描述信息
           ModelReleaseStats      stats             =  8; // 模型版本的动态统计信息，动态生成并返回
           HardwareRange          hardware_range    =  9; // 模型适配的硬件信息
           ModelDefaultConfig     default_config    = 10; // 模型部署时默认配置（包含所用Runtime / 资源 / 参数配置等）
  repeated ModelApi               model_apis        = 12; // 经过用户修改后的model api配置
           string                 project_id        = 13;
           string                 creator           = 14;
           TrainingTemplateConfig training_template = 15;
           bool                   is_system_init    = 16; //是否为系统初始化的模型
           string                 model_weight      = 17;
}

message ModelRelease {
  ModelReleaseBase release_base = 1;
  ModelMeta        model_meta   = 2;
}

message ModelReleaseStats {
  string             deployment_id     = 1; // 模型版本是否已部署，如果已部署，则填充对应的部署实例ID
  string             deployment_status = 4;
  string             deployment_health = 5;
  string             model_name        = 2; // 关联模型名称
  ModelReleaseStatus release_status    = 3; // 评估状态
}

message ModelMeta {
  ModelType         model_type          = 1;
  FileModelMeta     file_model_meta     = 2;
  ImageModelMeta    image_model_meta    = 3;
  EnsembleModelMeta ensemble_model_meta = 4;
  ModelSubType      model_sub_type      = 5;
}

// ModelDefaultConfig 定义了模型默认部署时使用的配置
message ModelDefaultConfig {
           ModelRuntimeType    runtime            = 1;
           DeployResource      resource           = 2;
           map <string,string> values             = 3;
  repeated DynamicParam        deployment_params  = 4;
  repeated DynamicParam        inference_params   = 5;
           string              default_prompt     = 6; // llm默认系统提示词
           bool                use_default_params = 7; // 是否使用默认的推理参数与提示词
}

message FileModelMeta {
  string raw     = 1; // 模型文件自动解析出的
  bool   encrypt = 2; // 是否对模型文件进行加密
  
  // TODO 新增其它文件原子模型通用字段
  //    string metadata = 1;
  //    map<string, string> framework_requirements = 4;
  //    map<string, double> original_evaluation_index = 5;
  map <string,TrainingDataDistribution> training_data_distributions = 3; // key 为 input name
}

// EnsembleSubModelType
enum SubModelType {
  SUB_MODEL_TYPE_UNSPECIFIED     = 0;
  SUB_MODEL_TYPE_ATOM_MODEL      = 1;
  SUB_MODEL_TYPE_ENSEMBLE_CONFIG = 2;
  SUB_MODEL_TYPE_PYTHON_CODE     = 3;
}
// ModelEnsembleMode 组装模型的组合方式
enum ModelEnsembleMode {
  MODEL_ENSEMBLE_MODE_UNSPECIFIED = 0;
  MODEL_ENSEMBLE_MODE_ENSEMBLE    = 1; // Ensemble 模式，用户通过一个特殊的模型中的配置，串联各个模型的输入与输出，实现模型组合。
  MODEL_ENSEMBLE_MODE_BLS         = 2; // BLS(Business Logic Scripting，业务逻辑脚本) 模式， 用户通过自定义逻辑实现循环、条件、数据相关的控制流和其他模型执行的组合。
}
message EnsembleModelMeta {
           string            base_repo  = 1; // 组合模型所在基础目录
           bool              committed  = 2; // 是否是完成了正式提交
           ModelEnsembleMode mode       = 3; // 模型组合模式
  repeated SubModel          sub_models = 4; // 包含的所有子模型
}

enum FileType {
  FILE_TYPE_UNSPECIFIED = 0;
  
  // 文本类型文件
  FILE_TYPE_PBTXT  = 1;
  FILE_TYPE_PYTHON = 2;
  FILE_TYPE_TXT    = 3;
  FILE_TYPE_JSON   = 4;
  FILE_TYPE_YAML   = 5;
  FILE_TYPE_CSV    = 6;
  
  // 二进制类型文件
  FILE_TYPE_PMML = 11;
  FILE_TYPE_ONNX = 12;
  FILE_TYPE_PKL  = 13;
  
  // 通用类别
  FILE_TYPE_GENERIC = 100;
}
message ModelContentFile {
  string   fid         = 1; // 文件ID, 同一个子模型下唯一
  string   name        = 2; // 文件名称
  string   path        = 3; // 文件相对路径
  string   ext         = 4; // 文件扩展名, 前端用来进行不同的展示 e.g. pbtxt, pmml, onnx, py, txt, json, yaml, etc...
  FileType type        = 6; // 文件类型，与扩展名对应
  bool     is_config   = 7; // 该文件是否为模型配置说明文件
  bool     is_model    = 8; // 该文件是否为模型文件本身
  int64    last_update = 9; // 最近修改时间戳
}

message SubModelAlias {
  string       dir_name   = 1; //
  SubModelType model_type = 2;
}

message AtomSubModel {
           SubModelAlias    alias = 1; // 原子模型所在目录
  repeated ModelContentFile files = 6; // 原子模型中包含的模型相关文件信息
}

message AtomModelInfo {
  string model_id      = 2; // 对应的模型ID
  string release_id    = 3; // 对应的模型版本ID
  string release_name  = 4; // 对应的模型版本号（e.g. v1, 自动生成）
  string release_alias = 5; // 对应的模型版本别名（中文，可选）
}

message PythonCodeInfo {}

message EnsembleConfigInfo {}

message SubModelInfo {
  AtomModelInfo      atom_model_info      = 1;
  PythonCodeInfo     python_code_info     = 2;
  EnsembleConfigInfo ensemble_config_info = 3;
}

// SubModel 组合模型中包含的单个子模型的描述
message SubModel {
           string           alias = 1; // 模型别名，用于标识新增原子模型在组装模型中的目录，在配置模型调用顺序与依赖关系时，需使用该模型别名进行标识，由字母与数字构成，不包含除 '_', '-' 以外的符号, 且必须以字母开头
           SubModelType     type  = 2; // 子模型类型 [原子模型, 胶水代码, 组装配置]
           SubModelInfo     info  = 3; // 子模型不同类型的额外的信息
           int64            order = 4; // 子模型的在模型配置中的序号
  repeated ModelContentFile files = 5; // 子模型中包含的模型相关文件信息
}

// FileList 模型中模型文件信息列表
message FileList {
  repeated ModelContentFile files = 1; // 模型文件信息列表
}

// EnsembleModelRelease 组合模型版本
message EnsembleModelRelease {
  ModelReleaseBase  model_release = 1; // 版本基础信息
  EnsembleModelMeta model_meta    = 2; // 模型成分
}

message ImageConfig {
           map <string,string> envs           = 1; // 镜像所需要的环境变量 形式 {"MYSQL_ROOT_PASSWORD": "transwarp123"}
  repeated string              cmds           = 2;
           string              working_dir    = 3;
           string              entry_point    = 4;
           map <string,string> labels         = 5;
  repeated string              args           = 6;
           string              restart_policy = 7; // 镜像重启策略， 保留扩展先不暴露给用户配置
           map <string,string> volumes        = 8; // 挂载存储卷，可以保留需要持久化的数据，  保留扩展先不暴露给用户配置
  repeated string              ports          = 9; // 镜像中服务所在的端口，形式["3306/TCP"]
}

message ImageModelMeta {
           string      id             =  1;
  repeated string      repo_tags      =  2;
  repeated string      repo_digests   =  3;
           string      created        =  4;
           string      docker_version =  5;
           string      author         =  6;
           ImageConfig config         =  7;
           string      architecture   =  8;
           string      os             =  9;
           int64       size           = 10;
}

message ImageModelRelease {
  ModelReleaseBase model_release = 1;
  ImageModelMeta   model_meta    = 2;
}

// DeploymentDetail 定义了部署的详细描述信息
message DeploymentDetail {
  string              desc           = 1; // desc 部署描述信息
  string              user_id        = 2; // user_id 部署创建者
  int64               create_time_ms = 5; // create_time_ms 创建时间戳
  int64               update_time_ms = 6; // update_time_ms 更新时间戳
  map <string,string> labels         = 7; // labels 模型标签列表
}

// DeploymentType 描述部署类型，现在模型评估需要启动模型，模型体验需要启动模型，模型服务也启动模型，但其中前两项不显示在模型部署（服务）列表中
// 根据部署类型进行筛选
enum DeploymentType {
  DEPLOYMENT_TYPE_UNSPECIFIED = 0;
  DEPLOYMENT_TYPE_SERVICE     = 1; // 模型服务，显示在列表中
  DEPLOYMENT_TYPE_EVALUATION  = 2; // 评估启动的模型部署，不显示在模型服务列表中
  DEPLOYMENT_TYPE_TEST        = 3; // 模型体验启动的模型部署 不显示在模型服务列表中
}
message Deployment {
           string              id              =  1; // 模型部署实例UUID, 系统自动生成
           string              model_id        =  2; // 部署使用的模型
           string              model_name      =  3;
           string              release_id      =  4; // 部署使用的模型版本
           string              release_name    =  5; // 模型版本名称
           string              sys_id          =  6; // 部署的目标 DeploySys ID
           bool                enable          =  7; // 是否启用该模型部署实例
           int64               replica         =  8; // 最终预期的副本的数量
           ModelRuntimeType    runtime_type    =  9; // 部署时适用的运行时
           DeployResource      resource        = 10; // （可选）部署时资源配置
           DeploySelector      selector        = 11; // （可选）部署时节点选择配置
           map <string,string> values          = 12; // 提供不同runtime所需的动态参数的配置
           DeploymentDetail    detail          = 13;
           Status              status          = 14;
           DeploymentType      type            = 15;
           int32               port_map_start  = 16; // 端口映射开始端口,k8s中port(映射端口)->targetPort(自定义端口)，屏蔽自定义端口的随机性
           int64               start_time_ms   = 17; // 启动标志位，防止更新Deployment状态时重复启动
           int64               stop_time_ms    = 18;
  repeated string              nodes           = 19; // 运行节点，部署完成后才能确定
           string              release_version = 20;
           bool                auto_schedule   = 21;
           map <string,string> deploy_values   = 22;
           string              project_id      = 23;
           string              name            = 24; // 部署的名称，默认为{model_name}_{release_version}
           SpaceInfo           SpaceInfo       = 25; // 是否首页被选中为某行业的精选
}

// LifeCycle 记录资源生命周期的结构
message LifeCycle {
  string id               = 1;
  int64  last_query_ms    = 2;
  int64  close_timeout_ms = 3;
}

// ModelDeployment 模型及其关联的部署
message ModelDeployment {
  Model        model      = 1;
  Deployment   deployment = 2;
  ModelRelease release    = 3;
}

// NodeStats 节点资源的动态统计数据
message NodeStats {
  int64                      time_stamp = 1;
  string                     name       = 2;
  ResourceStats              cpu_stats  = 3;
  ResourceStats              mem_stats  = 4;
  ResourceStats              disk_stats = 5;
  map <string,ResourceStats> gpus_stats = 6;
}

message ResourceStats {
  int64 total         = 1; // 资源总量 cpu单位 core 存储相关（内存，磁盘） byte
  float usage_percent = 2;
}

// NodeSystemInfo 描述节点系统信息
message SystemInfo {
  string machine_id                = 1;
  string system_uuid               = 2;
  string boot_id                   = 3;
  string kernel_version            = 4;
  string os_image                  = 5;
  string os                        = 6;
  string container_runtime_version = 8;
  string kubelet_version           = 9;
}

// NodeRole 集群中节点角色
enum NodeRole {
  NODE_ROLE_UNSPECIFIED = 0;
  NODE_ROLE_MASTER      = 1; // 集群管理节点
  NODE_ROLE_WORKER      = 2; // 集群工作节点
  NODE_ROLE_EVAL        = 3; // 可用于模型评估任务
  NODE_ROLE_TEST        = 4; // 可用于模型体验任务
  NODE_ROLE_DEPLOY      = 5; // 可用于模型部署任务
}
// NodeInfo 节点硬件与资源信息
message NodeInfo {
           string           name          =  1; // 节点名
           string           address       =  2; // 节点集群内IP
           string           hostname      =  3; // 节点集群内域名
  repeated NodeRole         roles         =  4; // 节点角色, 支持多种角色【e.g. 评估, 部署, 体验 etc ...】
           HardwareResource capacity      =  5; // 节点资源总量
           HardwareResource allocatable   =  6; // 节点可分配资源剩余量
           SystemInfo       system_info   =  7; // 节点系统信息
           HardwareInfo     hardware_info =  8; // 节点硬件架构信息
           NodeStats        node_stats    = 10; // 节点实时统计信息
}

// CPUArch 可能会出现的CPU架构
enum CPUArch {
  CPU_ARCH_UNSPECIFIED = 0; // 模型硬件适配信息未知
  CPU_ARCH_AMD64       = 1; // 模型仅可运行在CPU架构为 amd64 (aka: x86_64) 的硬件上
  CPU_ARCH_ARM64       = 2; // 模型仅可运行在CPU架构 arm64 (aka: aarch64, arm64/v8) 的硬件上
  CPU_ARCH_MULTI       = 3; // 模型与硬件架构无关
}
// AcceleratorType 可能出现的硬件加速类型
enum AcceleratorType {
  ACCELERATOR_TYPE_UNSPECIFIED = 0; // 硬件加速相关信息未知
  ACCELERATOR_TYPE_NONE        = 1; // 不支持硬件加速
  ACCELERATOR_TYPE_GPU         = 2; // 英伟达显卡系列
  ACCELERATOR_TYPE_ATLAS       = 3; // 华为昇腾系列
  ACCELERATOR_TYPE_MLU         = 4; // 寒武纪MLU系列
  ACCELERATOR_TYPE_JETSON      = 5; // 英伟达边缘推理盒子Jetson系列
  ACCELERATOR_TYPE_VAAPI       = 6; // 英特尔开源硬件视频加速接口（Video Acceleration API）
}
// HardwareInfo 硬件信息
message HardwareInfo {
           CPUArch         cpu_arch  = 1; // CPU架构
  repeated AcceleratorCard acc_cards = 2; // 硬件包含的加速卡列表( 保留不同类型加速卡混部的可能性， 粒度为单张卡 )
}

// HardwareRange 定义了硬件范围，可用于模型/Runtime创建时生命所支持的硬件列表
message HardwareRange {
  repeated CPUArch         arches    = 1;
  repeated AcceleratorType acc_types = 2;
}

// DeploySys 对应一个模型部署系统，集群Level, 每个集群可以具备 0-* 个 DeploySys, 之间的资源可能存在竞争
message DeploySys {
           string       id             = 1;
           string       name           = 2;
  repeated NodeInfo     nodes          = 3;
  repeated ModelRuntime runtimes       = 4;
           int64        create_time_ms = 5;
           int64        update_time_ms = 6;
}

// HardwareResource 硬件资源信息，用于描述 机器具备的硬件资源、机器尚可分配的资源、模型部署请求的资源等
// TODO GPU虚拟化：
//     主要依托于TCOS的Krux方案, 支持多个容器同时使用一块GPU。(参考https://wiki.transwarp.io/pages/viewpage.action?pageId=29083993)
//     vGPU Cores来表示GPU的算力（即平均GPU使用率），主要决定了该CUDA应用的运行性能，
//     vGPU Memory来表示GPU的显存，主要决定了该CUDA应用是否有足够的显存来正常运行。
// 虚拟化限制：
// * 仅支持在TOS中使用，且要求TOS版本>= 3.0
// * 仅支持Nvidia GPU
// * 节点必须安装Nvidia GPU卡及Nvidia(内核)驱动，且要求Nvida驱动版本>= 460.32.03
// * 不支持创建Pod时直接指定spec.nodeName
// * 不支持在GPU独占模式下配置显存限制
// * 在容器内禁止覆盖LD_LIBRARY_PATH环境变量
// * 容器禁止使用特权模式
message HardwareResource {
  int64 cpus     = 1; // cpu, 单位 0.001 core
  int64 memory   = 2; // 主存, 单位 1 byte
  int64 vgpus    = 3; // 虚拟gpu个数, 单位 1% gpu 算力 // TODO 具体与GPU映射关系由不同虚拟化插件决定
  int64 vgpu_mem = 4; // 虚拟gpu显存, 单位 1 byte
}

// DeploySelector 模型部署的目标节点与GPU
message DeploySelector {
  repeated NodeSelector nodes = 1; // 节点选择范围
}

// NodeSelector 允许资源在某个节点上进行调度，以及限制了可用的加速卡列表
message NodeSelector {
           string node_id      = 1; // 可用节点的ID
  repeated string accelerators = 2; // 该节点上可用的加速卡 ID列表
}

// DeployResource 模型部署的资源请求与限制
message DeployResource {
           HardwareResource         request       = 1;
           HardwareResource         limit         = 2;
           string                   gpu_type      = 5;
  repeated string                   gpu_cards     = 6;
           int32                    resource_id   = 3;
           bool                     is_power_rule = 4;
           common.AscendResourceCfg ascend_cfg    = 7;
           string                   arch          = 8;
}

// AcceleratorCard 加速卡定义
message AcceleratorCard {
  string          uuid   = 1; // 加速卡UUID
  int64           index  = 2; // 加速卡序号
  string          model  = 3; // 加速卡型号
  int64           memory = 4; // 显存总量，单位 1 Byte
  AcceleratorType type   = 8; // 加速卡类别
}

enum ModelRuntimeType {
  MODEL_RUNTIME_TYPE_UNSPECIFIED = 0;
  MODEL_RUNTIME_TYPE_IMAGE       = 1; // 镜像模型的默认运行时
  MODEL_RUNTIME_TYPE_DLIE_POD    = 2; // DLIE模型的默认运行时（每个模型一个DLIE Server Pod的形式）
  MODEL_RUNTIME_TYPE_DLIE_PROC   = 3; // DLIE模型的默认运行时（高性能模式, 每个模型一个DLIE Server Process的形式）
  MODEL_RUNTIME_TYPE_PMML        = 4; // 专门启动pmml模型的runtime

  // 其它类型的模型均可按照各自需求进行Runtime的自定义
  // 仅需提供Runtime运行模型时所需的参数列表，并实现模型实例的基础管理接口即可
}
// ModelRuntime 对应一个模型部署启动器，每个DeploySys 可以支持 0-* 种ModelRuntime
message ModelRuntime {
           ModelRuntimeType runtime_type   = 1; // 当前Runtime名称
  repeated DynamicParam     runtime_params = 2; // 部署时需要提供的动态参数列表
  repeated ModelType        types          = 3; // 支持部署的ModelTypes
  repeated ModelSubType     sub_types      = 4; // 支持部署的ModelSubTypes
           HardwareRange    hardware_range = 5; // 支持调度的硬件架构范围
}

enum ModelServiceSchema {
  MODEL_SERVICE_SCHEMA_UNSPECIFIED = 0;
  MODEL_SERVICE_SCHEMA_HTTP        = 1;
  MODEL_SERVICE_SCHEMA_DLIE        = 2;
  MODEL_SERVICE_SCHEMA_SELDON      = 3;
}
enum ModelServiceType {
  MODEL_SERVICE_TYPE_UNSPECIFIED = 0;
  MODEL_SERVICE_TYPE_LOCAL       = 1;
  MODEL_SERVICE_TYPE_REMOTE      = 2;
  MODEL_SERVICE_TYPE_CUSTOM      = 3;
}
// ModelService的数组形式
message ModelServices {
  repeated ModelService model_service = 1;
}

// ModelService 部署粒度的服务，一个部署可能对应一个k8s service，可能有多个接口与路径
message ModelService {
           string                   id                       =  1; // id 为产生该服务的Deployment id
           ModelServiceSchema       schema                   =  2; // 使用Triton时 协议为 dlie:// 其他 http://
           string                   host                     =  3; // k8s中service name，为规范后的Deployment id
           int32                    port                     =  4; // 默认每个模型服务仅暴露一个端口，不同服务模型层面的api划分
           ModelServiceType         type                     =  5;
  repeated ModelApi                 apis                     =  6; // 本地启动的模型服务不做存储，避免与模型层面数据不一致
           Status                   status                   =  7;
           ModelKind                kind                     =  8;
           ModelSubKind             sub_kind                 =  9;
           string                   model_name               = 10;
           string                   release_name             = 11;
           string                   release_version          = 12;
           string                   model_id                 = 13;
           string                   release_id               = 14;
  repeated DynamicParam             inference_params         = 15;
           string                   prompt                   = 16;
           string                   namespace                = 17;
           string                   seldon_deploy_name       = 18;
           string                   name                     = 19;
           string                   full_url                 = 20; // 模型服务调用的完整URL，当使用seldon部署时，{istio-gateway-host}:{istio-gateway-port}/{namespace}/{seldon_deploy_name}
           InvokeAsTool             invoke_as_tool           = 21; // 模型服务作为工具调用时需要传入的参数与描述
           RemoteServiceConfig      remote_service_config    = 22;
           string                   desc                     = 23;
           int64                    create_time_ms           = 24;
           Model                    reference_model          = 25; // 带模型的参考信息
           ModelRelease             reference_release        = 26;
           string                   project_id               = 27;
           RemoteService            reference_remote_service = 28;
           int64                    update_time_ms           = 29;
           serving.GuardrailsConfig guardrails_config        = 30;
  repeated commons.Endpoint         custom_service_endpoints = 31;
}

enum ModelServiceInvokeMethod {
  MODEL_SERVICE_INVOKE_METHOD_SYNC   = 0;
  MODEL_SERVICE_INVOKE_METHOD_STREAM = 1;
}
message InvokeAsTool {
           string                   name_for_model = 1;
           string                   name_for_human = 2;
           string                   desc           = 3;
           ModelServiceInvokeMethod invoke_method  = 4;
  repeated InvokeParam              invoke_params  = 5;
}

message InvokeParam {
  string name          = 1;
  string default_value = 2;
  string type          = 3;
  string desc          = 4;
  bool   required      = 5;
}

message RemoteServiceDetail {
  string              desc           = 1; // desc 部署描述信息
  string              user_id        = 2; // user_id 部署创建者
  int64               create_time_ms = 5; // create_time_ms 创建时间戳
  int64               update_time_ms = 6; // update_time_ms 更新时间戳
  map <string,string> labels         = 7; // labels 模型标签列表
}

enum RemoteServiceMethod {
  REMOTE_SERVICE_METHOD_UNSPECIFIED = 0;
  REMOTE_SERVICE_METHOD_GET         = 1;
  REMOTE_SERVICE_METHOD_POST        = 2;
}
enum ProxyScheme {
  PROXY_SCHEME_UNSPECIFIED = 0;
  PROXY_SCHEME_HTTP        = 1;
  PROXY_SCHEME_HTTPS       = 2;
  PROXY_SCHEME_SOCKS4      = 3;
  PROXY_SCHEME_SOCKS5      = 4;
}
message RemoteServiceProxy {
  ProxyScheme scheme = 1;
  string      url    = 2;
}

message Configurable {
  string name  = 1;
  string value = 2;
  string desc  = 3;
}

message RemoteServiceConfig {
           RemoteServiceMethod method                  =  1;
           string              url                     =  2;
  repeated Configurable        query_params            =  3;
  repeated Configurable        path_params             =  4; // 路径参数，这里只存储，由前端填充好url
  repeated Configurable        headers                 =  5;
           string              body                    =  6;
           bool                use_proxy               =  7;
           RemoteServiceProxy  proxy                   =  8;
           string              full_url                = 10;
           map <string,string> computation_attributes  = 11;
           InterfaceSpecName   interface_spec          = 12;
           string              request_process_script  = 13; // 请求的处理jsonnet脚本
           string              response_process_script = 14;
}

enum InterfaceSpecName {
  INTERFACE_SPEC_OTHERS    = 0;
  INTERFACE_SPEC_OPENAI    = 1;
  INTERFACE_SPEC_TRANSWARP = 3;
}
message RemoteServiceStatus {
  bool   is_reachable = 1;
  string msg          = 2;
}

enum RemoteServiceBuiltInType {
  REMOTE_SERVICE_BUILTIN_TYPE_UNSPECIFIC = 0;
  REMOTE_SERVICE_BUILTIN_TYPE_OPENAI     = 1;
  REMOTE_SERVICE_BUILTIN_TYPE_AZURE      = 2;
}
message Member {
  string id        = 1;
  string user_type = 2;
}

message RemoteServicePublishInfo {
           string            name                  =  1;
           string            desc                  =  2;
           serving.RateLimit rate_limit            =  3;
           bool              is_security           =  4;
           string            id                    =  5;
           string            virtual_svc_url       =  6;
           string            security_config_id    =  7;
           serving.RateLimit user_rate_limit       =  8;
  repeated Member            members               =  9;
           string            published_api         = 10;
           bool              enable_anonymous_call = 11;
}

// RemoteService 用添加的自定可调用的远程模型服务
message RemoteService {
           string                   id               =  1;
           string                   name             =  2;
           ModelKind                kind             =  3;
           ModelSubKind             sub_kind         =  4;
           RemoteServiceDetail      detail           =  5;
           RemoteServiceConfig      api_config       =  6;
           RemoteServiceStatus      status           =  7;
           string                   project_id       =  9;
           bool                     chat_mode        = 10;
           bool                     is_published     = 11;
           RemoteServicePublishInfo publish_info     = 12;
  repeated DynamicParam             inference_params = 13;
           string                   logo             = 15;
}

message SupportedInterfaces {
  repeated InterfaceSpec interface_specs = 1;
}

message InterfaceSpec {
           InterfaceSpecName   id                       = 1;
           string              name                     = 2;
           ModelKind           kind                     = 3;
           ModelSubKind        sub_kind                 = 4;
           string              request_template         = 5;
           string              response_template        = 6;
           string              desc                     = 7;
           RemoteServiceConfig api_config_template      = 8;
  repeated DynamicParam        default_inference_params = 9;
}

enum EvaluationDatasetFromType {
  EVALUATION_DATASET_FROM_TYPE_UNSPECIFIED = 0;
  EVALUATION_DATASET_FROM_TYPE_LOCAL       = 1;
  EVALUATION_DATASET_FROM_TYPE_AUTOCV      = 2;
}
// EvaluationDataSet 是一个评估数据集的元数据结构
message EvaluationDataset {
  string                    id              =  1;
  string                    model_id        =  2;
  int32                     autocv_dataset  =  3;
  string                    name            =  4;
  string                    storage_path    =  5; // 系统内评估数据所在的存储路径
  string                    creator         =  6;
  EvaluationDatasetFromType from            =  7; // 数据集来源
  ModelKind                 type            =  8; // 数据模态
  string                    format          =  9; // 数据集格式（文件后缀）
  string                    desc            = 10;
  CsvDataSetAddition        addition        = 11;
  string                    special_column  = 12; // csv 文件时填充
  int64                     create_time_ms  = 13; // create_time_ms 创建时间戳
  int64                     update_time_ms  = 14; // update_time_ms 更新时间戳
  string                    files_extension = 15; // format代表数据集的压缩格式，files_extension代表内部文件的后缀
  string                    project_id      = 16;
}

// CsvDataSetAddition 如果数据集为csv，使用该结构存储csv的额外信息
message CsvDataSetAddition {
  repeated string input_columns  = 1; // 模型输入的列名
           string special_column = 2; // 如真实值列，文本列等校验列
           int64  rows           = 3; // 总行数 包含表头
  repeated string headers        = 4; // headers
}

// Csv csv格式的数据集结构,代表一张数据表。
message Csv {
  CsvHeader          headers = 1;
  map <int64,CsvRow> row     = 2;
}

message CsvRow {
  repeated string values = 1;
}

message CsvHeader {
  repeated string column_names = 1;
  repeated string data_types   = 2;
}

enum EvaluationStage {
  EVALUATION_STAGE_UNSPECIFIED      = 0;
  EVALUATION_STAGE_PREPARE_DATA     = 1;
  EVALUATION_STAGE_INFER            = 2;
  EVALUATION_STAGE_MODEL_METRICS    = 3;
  EVALUATION_STAGE_INTEGRATE_RESULT = 4;
}
// StageScript 单个脚本(模板)的元数据
message StageScript {
  string          name    = 1;
  EvaluationStage stage   = 2;
  string          desc    = 3;
  string          content = 4;
}

// EvaluationScript是评估脚本整体的基础信息
message EvaluationScriptBase {
  string id             = 1;
  string model_id       = 2;
  string release_id     = 3;
  string name           = 4;
  string language       = 5;
  string pip_source     = 6;
  string requirements   = 7;
  int64  create_time_ms = 8; // create_time_ms 创建时间戳
  int64  update_time_ms = 9; // update_time_ms 更新时间戳
}

// EvaluationScript 评估脚本
message EvaluationScript {
           EvaluationScriptBase base_info     = 1;
           string               template_name = 2;
           string               storage_path  = 3;
  repeated StageScript          scripts       = 4;
           string               creator       = 5;
           string               project_id    = 6;
}

message EvaluationScriptTemplate {
           string      name      = 1;
  repeated StageScript templates = 2;
}

// Metrics 描述模型指标的元数据
// e.g. 模型的一个指标
// "f1_score": {
//      "value": 0.8287487073422957,
//      "range": "[0,1]",
//      "unit": "",
//      "desc": ""
//    }
message Metrics {
  float  value    = 1;
  float  original = 2;
  string range    = 3;
  string unit     = 4;
  string desc     = 5;
  string en       = 6;
  string zh       = 7;
  string category = 8;
}

message Curve {
  repeated float  x  = 1;
  repeated float  y  = 2;
           string en = 3;
           string zh = 4;
}

// 标准指标报告输出结果
message StandardMetricReport {
  repeated SingleMetric   single_metric    = 1;
  repeated TableMetric    table_metric     = 2;
  repeated GraphMetric    graph_metric     = 3;
  repeated PieGraphMetric pie_graph_metric = 4;
}

message SingleMetric {
  string name  = 1;
  float  value = 2;
}

message TableMetric {
           string         name     = 1;
           TwoDFloatArray data     = 2;
  repeated string         row_head = 3;
  repeated string         col_head = 4;
}

message FloatArray {
  repeated float value = 1;
}

message TwoDFloatArray {
  repeated FloatArray value = 1;
}

message GraphMetric {
           string     name           = 1;
  repeated string     x_axis_data    = 2;
           FloatArray y_axis_data    = 3;
           string     x_axis_name    = 4;
           string     y_axis_name    = 5;
           string     display_format = 6;
}

message ComplexGraphMetric {
           string     name             = 1;
  repeated string     child_graph_name = 2;
  repeated string     x_axis_data      = 3;
           FloatArray y_axis_data      = 4;
           string     x_axis_name      = 5;
           string     y_axis_name      = 6;
           string     display_format   = 7;
           int32      count            = 8;
}

message PieGraphMetric {
           string     name        = 1;
  repeated string     x_axis_data = 2;
           FloatArray y_axis_data = 3;
}

message EvaluationResult {
           string               hardware               = 1;
           map <string,Metrics> model_metrics          = 2;
           map <string,Curve>   curves                 = 3;
           map <string,Metrics> confusion_matrix_2     = 4;
           map <string,Metrics> performance_metrics    = 5;
  repeated SubModelPerformance  sub_models_performance = 6;
}

// SubModelPerformance 组合模型子模型指标，当前性能指标需要按照子模型进行分开
message SubModelPerformance {
  string               id          = 1;
  string               name        = 2;
  map <string,Metrics> performance = 3;
}

// PortInfo 部署暴露的服务状态,主要是端口信息
message PortInfo {
  string name      = 1;
  int32  port      = 2;
  int32  node_port = 3;
}

message ServiceStatus {
           string   node_ip = 1;
  repeated PortInfo ports   = 2;
}

// Status 状态描述结构，描述评估与部署状态
message Status {
           string                           state          =  1;
           string                           message        =  2;
           int64                            timestamp      =  3;
           string                           health         =  4;
           string                           health_msg     =  5;
  repeated string                           nodes          =  6; // 被调度到的节点列表
           ServiceStatus                    service_status =  7;
           string                           ref_id         =  8;
           string                           ref_type       =  9;
           string                           replica_id     = 10;
           string                           edge_id        = 11;
           map <string,google.protobuf.Any> indicators     = 12;
}

message Bucket {
  string left  = 1;
  string right = 2;
  int32  count = 3;
}

message Feature {
           string name    = 1;
  repeated Bucket buckets = 2;
}

// TrainingDataDistribution 训练数据分布用于pkl模型填写训练数据分布,支持手动填写与上传文件解析
message TrainingDataDistribution {
  repeated Feature features = 1;
}


// Protobuf v3.6.1 安装指南：https://wiki.transwarp.io/display/AA/gRPC+-+Protobuf
// protoc-gen-go-grpc 安装： https://grpc.io/docs/languages/go/quickstart/
//
// protoc --go_out=. --go-grpc_out=. -I=${GOPATH}/src -I=. *.proto
