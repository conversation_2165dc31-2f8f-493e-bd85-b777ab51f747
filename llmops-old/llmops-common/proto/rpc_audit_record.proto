syntax = "proto3";

package proto;

import "proto/audit_record.proto";
import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service AuditRecordManager {
  rpc ListAuditRecords (ListAuditRecordsReq) returns (ListAuditRecordsRsp);
}
message ListAuditRecordsReq {
           UserContext            user_context = 1;
           int64                  since        = 2;
           string                 user_id      = 3;
  repeated AuditRecordModule      modules      = 4;
  repeated AuditRecordSubModule   sub_modules  = 5;
  repeated AuditRecordOperateType op_types     = 6;
  
  // @gotags: description:"限制最大返回条数, 小于等于0时不限制"
  int64 limit = 7;
}

message ListAuditRecordsRsp {
  repeated AuditRecord records = 1;
}

