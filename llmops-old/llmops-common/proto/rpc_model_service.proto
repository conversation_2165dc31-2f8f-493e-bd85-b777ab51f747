syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

import "proto/common.proto";
import "proto/model.proto";
import "google/protobuf/any.proto";
import "proto/serving/mlops_service.proto";

//  ModelServiceManager 为模型仓库对外提供的关于模型服务相关的服务接口
service ModelServiceManager {
  rpc ReadModelService     (ReadModelServiceReq) returns (       ReadModelServiceRsp); // 查询模型服务列表
  rpc TestModelService     (TestModelServiceReq) returns (       TestModelServiceRsp); // 测试模型服务
  rpc SaveModelService     (SaveModelServiceReq) returns (       SaveModelServiceRsp); // 创建或更新模型服务
  rpc TestModelStreamInfer (TestModelServiceReq) returns (stream ModelStreamInferRsp);
}
message ReadModelServiceReq {
  string              id                  =  1;
  string              model_id            =  2;
  string              release_id          =  3;
  PageReq             page_req            =  4;
  UserContext         ctx                 =  5;
  DeploymentType      type                =  6;
  ModelKind           kind                =  7;
  ModelSubKind        sub_kind            =  8;
  bool                is_seldon_mode      =  9;
  map <string,string> match_labels        = 10;
  bool                with_remote_service = 11;
  bool                is_mw_mode          = 12;
  bool                only_running        = 13;
  string              contain             = 14;
  bool                with_asset          = 15;
  string              interface_spec      = 16;
  bool                omit_censored       = 17;
  bool                with_custom_service = 18;
  bool                lite_mode           = 19;
}

message ReadModelServiceRsp {
  repeated ModelService services  = 1;
           int32        total     = 2;
           int32        page_num  = 3;
           int32        page_size = 4;
}

// TestModelServiceReq 是进行模型体验或者模型测试时用户向后端传递的结构，可以通过多种条件进行推理
message TestModelServiceReq {
  string         id              =  1; // 按照 Deployment id进行推理，仅指定该项则默认使用第一个接口
  string         model_id        =  2; // 按照 model id 跟 release id 进行推理
  string         release_id      =  3;
  string         url             =  4; // 按照具体的 url进行推理
  string         body            =  5;
  int32          timeout_seconds =  6;
  UserContext    ctx             =  7;
  string         path            =  8; // 指定path进行推理,与url应该能对应起来
  DeploymentType type            =  9; // 判断是模型服务测试还是模型体验
  string         conversation_id = 10; // LLM 推理时,对话id
  string         question_id     = 11;
  bool           is_seldon_mode  = 12;
  string         seldon_dp       = 13;
  string         namespace       = 14;
  bool           use_grpc        = 15;
}

message TestModelServiceRsp {
  google.protobuf.Any payload = 1;
}

message SaveModelServiceReq {
  ModelService service = 1;
  UserContext  ctx     = 2;
}

message SaveModelServiceRsp {
  ModelService service = 1;
}

message ModelStreamInferRsp {
  string id              = 1;
  string model_name      = 2;
  string model_version   = 3;
  string response        = 4;
  string error_message   = 5;
  string conversation_id = 6;
  string question_id     = 7;
  string service_id      = 8;
}

service RemoteServiceManager {
  rpc CreateRemoteService (CreateRemoteServiceReq) returns (CreateRemoteServiceRsp);
  rpc ReadRemoteService   (ReadRemoteServiceReq  ) returns (ReadRemoteServiceRsp  );
  rpc UpdateRemoteService (UpdateRemoteServiceReq) returns (UpdateRemoteServiceRsp);
  rpc DeleteRemoteService (DeleteRemoteServiceReq) returns (DeleteRemoteServiceRsp);
}
message CreateRemoteServiceReq {
  UserContext   ctx     = 1;
  RemoteService service = 2;
}

message CreateRemoteServiceRsp {
  RemoteService service = 1;
}

message ReadRemoteServiceReq {
  UserContext ctx             = 1;
  string      id              = 2;
  bool        check_reachable = 3;
  bool        is_published_id = 4; // id是否是发布之后的id
}

message ReadRemoteServiceRsp {
  repeated RemoteService services = 2;
}

message UpdateRemoteServiceReq {
  UserContext   ctx     = 1;
  RemoteService service = 2;
}

message UpdateRemoteServiceRsp {
  RemoteService service = 1;
}

message DeleteRemoteServiceReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteRemoteServiceRsp {}

message TestRemoteServiceReq {
           string            id              =  1;
           string            url             =  2;
           string            body            =  3;
           int32             timeout_seconds =  4;
           UserContext       ctx             =  5;
           string            service_id      =  6;
           bool              chat_mode       =  7; // 测试页面是否开启对话界面
           bool              seldon_mode     =  8; // 转发已经发布到mlops seldon的远程模型请求
  repeated MultipartFormdata form_data       =  9;
           string            content_type    = 10;
}

message MultipartFormdata {
  string key     = 1;
  string type    = 2; // text or file
  string content = 3;
  message ContentDesc {
    string name = 1;
    int64  size = 2;
  }

  ContentDesc content_desc = 4;
}

message TestRemoteServiceRsp {
  string id            = 1;
  string service_name  = 2;
  string response      = 3;
  string error_msg     = 4;
  string event_name    = 5;
  int32  response_code = 6;
}

message GetLoraBaseServicesReq {
  UserContext ctx        = 1;
  string      model_id   = 2;
  string      release_id = 3;
}

message GetLoraBaseServicesRsp {
  repeated serving.MLOpsServiceBaseInfo base_model_services = 1;
}

message LoadLoraReq {
  UserContext ctx                   = 1;
  string      model_id              = 2;
  string      release_id            = 3;
  string      base_model_service_id = 4;
  string      lora_name             = 5;
}

message LoadLoraRsp {
  string base_model_service_id = 1;
  string lora_name             = 2;
  int32  load_status_code      = 3;
  string msg                   = 4;
}

