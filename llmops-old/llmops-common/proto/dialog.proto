syntax = "proto3";

package proto;

import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message Message {
  int64  id          = 1;
  string content     = 3;
  string role        = 4;
  int64  create_time = 5;
}

message MessageReq {
  string  chat_id  = 1;
  string  role     = 2;
  string  content  = 3;
  PageReq page_req = 4;
}

message MessageRes {
           int64   size     = 1;
           string  chat_id  = 2;
  repeated Message messages = 3;
}

message Dialog {
  int64         id          = 1;
  string        chat_id     = 2;
  string        app_id      = 3;
  string        project_id  = 4;
  string        user        = 5;
  string        app_name    = 6;
  int64         create_time = 7;
  int64         update_time = 8;
  LatestMessage messages    = 9;
}

message LatestMessage {
  Message answer   = 1;
  Message question = 2;
}

message DialogReq {
  string  app_id     = 1;
  string  content    = 2;
  string  chat_id    = 3;
  PageReq page_req   = 4;
  int64   start_time = 5;
  int64   end_time   = 6;
}

message DialogRes {
           int64  size    = 1;
  repeated Dialog dialogs = 2;
  
  // @gotags: description:"获取到同一个应用链下的数据总量"
  int64 total = 3;
}

message DialogApp {
  int64  id          = 1;
  string app_id      = 2;
  string app_name    = 3;
  string app_image   = 4;
  int64  create_time = 7;
  int64  update_time = 8;
}

message DialogAppRes {
           int64     size       = 1;
           string    project_id = 2;
           string    user       = 3;
  repeated DialogApp apps       = 4;
}

enum DOWNLOAD_TYPE {
  DOWNLOAD_TYPE_ALL      = 0;
  DOWNLOAD_TYPE_ANSWER   = 1;
  DOWNLOAD_TYPE_QUESTION = 2;
}
message DownloadDialogReq {
  
  //@gotags:description:"对话的id数组"
  repeated string chat_ids = 1;
  
  //@gotags:description:"下载的类型(0:所有，1:只下载answer，2:只下载question)"
  DOWNLOAD_TYPE type = 2;
}

message DownloadDialogRes {
  
  //@gotags:description:"消息字节数据"
  bytes data = 1;
  
  //@gotags:description:"记录文件名称"
  string fileName = 2;
}

