syntax = "proto3";

package proto;

import "proto/image_template.proto";
import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// 任务模板对外提供的接口
service ImageTemplateManager {
  rpc ListTemplates   (ListTemplatesReq  ) returns (ListTemplatesRsp  );
  rpc GetTemplate     (GetTemplateReq    ) returns (GetTemplateRsp    );
  rpc CreateTemplate  (CreateTemplateReq ) returns (CreateTemplateRsp );
  rpc UpdateTemplate  (UpdateTemplateReq ) returns (UpdateTemplateRsp );
  rpc DeleteTemplates (DeleteTemplatesReq) returns (DeleteTemplatesRsp);
  rpc CloneTemplate   (CloneTemplateReq  ) returns (CloneTemplateRsp  );
}
message ListTemplatesReq {
  UserContext  user_context  = 1;
  ListSelector list_selector = 2;
  PageReq      page_req      = 3;
}

message ListSelector {
  repeated TemplateType   template_type   = 1;
  repeated TemplateStatus template_status = 2;
  repeated TemplateKind   template_kind   = 3;
  repeated TemplateSource template_source = 4;
}

message ListTemplatesRsp {
  repeated ImageTemplate templates = 1;
           int32         total     = 2;
           int32         page_num  = 3;
           int32         page_size = 4;
}

message GetTemplateReq {
  UserContext user_context = 1;
  string      id           = 2;
}

message GetTemplateRsp {
  ImageTemplate template = 1;
}

message CreateTemplateReq {
  UserContext   user_context = 1;
  ImageTemplate template     = 2;
}

message CreateTemplateRsp {
  ImageTemplate template = 1;
}

message UpdateTemplateReq {
  UserContext   user_context = 1;
  ImageTemplate template     = 2;
}

message UpdateTemplateRsp {
  ImageTemplate template = 1;
}

message DeleteTemplatesReq {
           UserContext user_context = 1;
  repeated string      ids          = 2; // 可同时删除多个模板
}

message DeleteTemplatesRsp {}

message CloneTemplateReq {
  UserContext user_context      = 1;
  string      id                = 2;
  string      target_project_id = 3; // 目标项目，为空或者和user_context一致则为项目内克隆
}

message CloneTemplateRsp {
  ImageTemplate template = 1;
}

