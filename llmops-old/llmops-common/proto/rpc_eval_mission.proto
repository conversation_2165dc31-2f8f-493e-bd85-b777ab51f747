syntax = "proto3";

package proto;

import "proto/common.proto";
import "proto/evaluation.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service EvalMissionMgr {
  rpc ReadEvalMissions     (ReadEvalMissionsReq    ) returns (ReadEvalMissionsRsp    );
  rpc CreateEvalMission    (CreateEvalMissionsReq  ) returns (CreateEvalMissionsRsp  );
  rpc UpdateEvalMission    (UpdateEvalMissionsReq  ) returns (UpdateEvalMissionsRsp  );
  rpc DeleteEvalMissions   (DeleteEvalMissionsReq  ) returns (DeleteEvalMissionsRsp  );
  rpc StartEvalMissions    (StartEvalMissionsReq   ) returns (StartEvalMissionsRsp   );
  rpc StopEvalMissions     (StopEvalMissionsReq    ) returns (StopEvalMissionsRsp    );
  rpc GetEvalMissionResult (GetEvalMissionResultReq) returns (GetEvalMissionResultRsp);
}
message ReadEvalMissionsReq {
  UserContext ctx = 1;
  string      id  = 2;
}

message ReadEvalMissionsRsp {
  repeated EvalMission eval_missions = 1;
}

message CreateEvalMissionsReq {
  UserContext ctx          = 1;
  EvalMission eval_mission = 2;
}

message CreateEvalMissionsRsp {
  EvalMission eval_mission = 1;
}

message UpdateEvalMissionsReq {
  UserContext ctx          = 1;
  EvalMission eval_mission = 2;
}

message UpdateEvalMissionsRsp {
  EvalMission eval_mission = 1;
}

message DeleteEvalMissionsReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message DeleteEvalMissionsRsp {}

message StartEvalMissionsReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message StartEvalMissionsRsp {}

message StopEvalMissionsReq {
           UserContext ctx = 1;
  repeated string      ids = 2;
}

message StopEvalMissionsRsp {}

message GetEvalMissionResultReq {
  UserContext ctx  = 1;
  string      id   = 2;
  string      lang = 3;
}

message GetEvalMissionResultRsp {
  Evaluation result = 1;
}

