syntax = "proto3";

package proto;

import "proto/data_connection.proto";
import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// 数据连接对外提供的接口
service DataConnectionManager {
  
  // 获取数据连接列表，需要实时测试连接状态
  rpc ListDataConnections   (ListConnectionsReq  ) returns (ListConnectionsRsp  );
  rpc GetDataConnection     (GetConnectionReq    ) returns (GetConnectionRsp    );
  rpc CreateDataConnection  (CreateConnectionReq ) returns (CreateConnectionRsp );
  rpc UpdateDataConnection  (UpdateConnectionReq ) returns (UpdateConnectionRsp );
  rpc DeleteDataConnections (DeleteConnectionsReq) returns (DeleteConnectionsRsp);
  rpc CloneDataConnection   (CloneConnectionReq  ) returns (CloneConnectionRsp  );
}
message ListConnectionsReq {
  UserContext             user_context  = 1;
  ListConnectionsSelector list_selector = 2;
}

message ListConnectionsSelector {
  repeated ConnectionType   type   = 1;
  repeated ConnectionStatus status = 2;
}

message ListConnectionsRsp {
  repeated DataConnection result = 1;
}

message GetConnectionReq {
  UserContext user_context = 1;
  string      id           = 2;
}

message GetConnectionRsp {
  DataConnection result = 1;
}

message CreateConnectionReq {
  UserContext    user_context = 1;
  DataConnection result       = 2;
}

message CreateConnectionRsp {
  DataConnection result = 1;
}

message UpdateConnectionReq {
  UserContext    user_context = 1;
  DataConnection result       = 2;
}

message UpdateConnectionRsp {
  DataConnection result = 1;
}

message DeleteConnectionsReq {
           UserContext user_context = 1;
  repeated string      ids          = 2; // 可同时删除多个数据连接
}

message DeleteConnectionsRsp {}

message CloneConnectionReq {
  UserContext user_context      = 1;
  string      id                = 2;
  string      target_project_id = 3; // 目标项目，为空或者和user_context一致则为项目内克隆
}

message CloneConnectionRsp {
  DataConnection result = 1;
}

