syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// RelationType 定义了模型/模型版本/其它资源之间的关联类型
// 除默认值外，考虑成对的关联关系 e.g [ is children & is parent | is inherit from vs is inherited by ]
enum RelationType {
  RELATION_TYPE_UNSPECIFIED        =  0; // 未指定具体关联类型，仅表明存在关联，默认值
  RELATION_TYPE_IS_CHILDREN_OF     =  1; // 是 ... 的组成部分
  RELATION_TYPE_IS_PARENT_OF       =  2; // 由 ... 组成
  RELATION_TYPE_INHERITS           =  3; // 继承自...
  RELATION_TYPE_IS_INHERITED_BY    =  4; // 被...继承
  RELATION_TYPE_FORKS              =  5; // 分叉自...
  RELATION_TYPE_IS_FORKED_BY       =  6; // 分叉出的分支
  RELATION_TYPE_BASE_MODEL_OF      =  7; // 为...的基础大模型
  RELATION_TYPE_FINE_TUNED_FROM    =  8; // 微调模型由...得到
  RELATION_TYPE_IS_TRAINED_ON      =  9; // 由...数据集训练
  RELATION_TYPE_TRAINING_SET_FOR   = 10; // 用于训练...
  RELATION_TYPE_IS_EVALUATED_ON    = 11; // 由...数据集评估
  RELATION_TYPE_EVALUATION_SET_FOR = 12; // 用于评估...
  RELATION_TYPE_QUANT_FROM         = 13; // 由...量化而来

  // ... etc 待补充
}
// PeerType 关系的对端资源类型
enum PeerType {
  PEER_TYPE_UNSPECIFIED   = 0; // 未知对端类型，默认值
  PEER_TYPE_MODEL         = 1; // 模型
  PEER_TYPE_MODEL_RELEASE = 2; // 模型版本
  PEER_TYPE_TRAINING_SET  = 3; // 模型训练集

  // ... etc 待补充
}
/*

Relation 定义了模型/模型版本之间或与其它相关资源之间的关联关系
其中 relation 的主语为当前 Relation 所属的资源，
表达的完整语义为：  current resource is ${relation type} of ${peer_type}:${peer_id} , link page =${link}
e.g. [model release v1] is [RELATION_TYPE_PARENT_OF] [model release v2]
模型仓库会与其他组件(例如样本仓库)的资源，产生某种关联。但是在模型仓库中难以直接获取到其他组件的资源，所以在模型仓库中添加关系时，
需要把关联资源的其他必要信息填充到relation.info字段中。
现对info中通用信息的key做如下约定，在填写信息时需与key的含义对应
info: {
  "name": "我的第一个资源",                                  // 关联资源的名称
  "desc": "这是我创建的第一个资源，该资源用于测试",              // 关联资源的描述
  "labels": "{"维护人": "张三", "类型": "目标检测"}",         // 关联资源的标签
  "version": "v1",                                        // 关联资源的版本
  "raw": "{"key1": "value", "key2": ["item", "item"]}"    // 关联资源的json格式全量字段
  // 其他常用key按需补充
}
*/
message Relation {
  RelationType        relation_type = 1; // 关联类型, 参考JIRA问题关联类型 e.g. is child of /  is integrated from / is trained by / ...
  string              peer_id       = 2; // 关联对象ID
  PeerType            peer_type     = 3; // 关联对象类型, e.g model / release / dataset / vlab / ...
  string              link          = 4; // 关联对象跳转详情页
  map <string,string> info          = 5; // 关联对象的额外描述信息
  string              peer_name     = 6;
}

message RelationGraph {
  message Node {
    string              id           = 1;
    string              name         = 2;
    PeerType            type         = 3;
    string              ext_ref_link = 4;
    map <string,string> node_info    = 5;
  }

  message Link {
    string              source    = 1;
    string              target    = 2;
    RelationType        relation  = 3;
    map <string,string> link_info = 4;
  }

           Node center_node = 1;
  repeated Node nodes       = 2;
  repeated Link links       = 3;
}

