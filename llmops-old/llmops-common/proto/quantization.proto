syntax = "proto3";

package proto;

import "proto/model.proto";
import "proto/dynamic_param.proto";
import "proto/serving/mlops_service.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

message QuantStrategy {
           string       name   = 1;
           string       desc   = 2;
  repeated DynamicParam params = 3;
           string       effect = 4;
}

message ReadQuantStrategiesRsp {
  repeated QuantStrategy strategies = 1;
}

message QuantizationConfig {
           string                model_id              =  1;
           string                release_id            =  2;
           string                model_name            =  3;
           string                release_name          =  4;
           int64                 model_size            =  5;
           string                mode                  =  6;
           string                strategy              =  7;
           DeployResource        resource              =  8;
  repeated DynamicParam          params                =  9;
           string                template_id           = 10;
           string                model_repo            = 11;
           string                result_path           = 12;
           serving.UnifyResource unify_resource_config = 13;

  // Deprecated use serving.UnifyResource instead
}

message QuantizationResult {
  string result_path = 1;
  int64  result_size = 2;
}

message Quantization {
  string             id            =  1;
  string             name          =  2;
  QuantizationConfig config        =  3;
  string             run_id        =  4;
  string             status        =  5;
  QuantizationResult result        =  6;
  string             project_id    =  7;
  int64              created_at    =  8;
  int64              updated_at    =  9;
  string             create_user   = 10;
  int64              start_time_ms = 11;
  int64              stop_time_ms  = 12;
  string             desc          = 13;
  string             node_id       = 14;
}

message ReadQuantizationsRsp {
  repeated Quantization quantizations = 1;
}

