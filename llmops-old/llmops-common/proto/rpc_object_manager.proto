syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// 文件操作相关
service ObjectManager {
  rpc ListObjects    (       ListObjectsReq   ) returns (       ListObjectsRsp   );
  rpc DeleteObject   (       DeleteObjectReq  ) returns (       DeleteObjectRsp  );
  rpc UploadObject   (stream UploadObjectReq  ) returns (       UploadObjectRsp  );
  rpc DownloadObject (       DownloadObjectReq) returns (stream DownloadObjectRsp);
}
message ListObjectsReq {}

message ListObjectsRsp {}

message DeleteObjectReq {}

message DeleteObjectRsp {}

message UploadObjectReq {
  string modelId      = 1;
  string releaseId    = 2;
  string filename     = 3;
  string attachmentId = 4;
  bool   encrypt      = 5;
  bytes  data         = 6;
}

message UploadObjectRsp {

  //    ObjectInfo info = 1;
}

message DownloadObjectReq {
  string model_id      = 1;
  string release_id    = 2;
  string attachment_id = 3;
}

message DownloadObjectRsp {
  string filename = 1;
  bytes  data     = 2;
}

