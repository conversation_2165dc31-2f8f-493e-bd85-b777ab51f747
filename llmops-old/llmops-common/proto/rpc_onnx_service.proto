syntax = "proto3";

package proto;

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

service OnnxService {
  rpc OnnxParse (OnnxParseReq) returns (OnnxParseRsp);
}
message OnnxParseReq {
  string filepath = 1;
}

message OnnxParseRsp {
  OnnxModel model = 1;
}

message OnnxModel {
           int64               ir_version            = 1;
           int64               model_version         = 2;
  repeated int64               op_set_import_version = 3;
           string              producer_name         = 4;
           string              producer_version      = 5;
           map <string,Input>  input                 = 6;
           map <string,Output> output                = 7;
}

message Input {
  InputOrOutputDim input_dim       = 1;
  string           input_elem_type = 2;
}

message Output {
  InputOrOutputDim output_dim       = 1;
  string           output_elem_type = 2;
}

message InputOrOutputDim {
           string param = 1;
  repeated int64  value = 2;
}

