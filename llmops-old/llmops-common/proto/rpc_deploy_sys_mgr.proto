syntax = "proto3";

package proto;

import "proto/model.proto";
import "proto/common.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

//  ModelDeploySysManager 为模型仓库侧对外提供的关于已注册的部署系统的相关服务接口
service ModelDeploySysManager {
  rpc GetModelDeploySys        (GetModelDeploySysReq       ) returns (GetModelDeploySysRsp       );
  rpc RegisterModelDeploySys   (RegisterModelDeploySysReq  ) returns (RegisterModelDeploySysRsp  ); // ModelDeploySys注册以及定时上报心跳的接口
  rpc UnregisterModelDeploySys (UnregisterModelDeploySysReq) returns (UnregisterModelDeploySysRsp); // ModelDeploySys 取消注册接口
}
message GetModelDeploySysReq {
  UserContext ctx = 1;
  string      id  = 2;
}

message GetModelDeploySysRsp {
  repeated DeploySys deploy_systems = 1;
}

message RegisterModelDeploySysReq {
  DeploySys deploy_sys = 1;
}

message RegisterModelDeploySysRsp {
  string id = 1;
}

message UnregisterModelDeploySysReq {
  string id = 1;
}

message UnregisterModelDeploySysRsp {}

