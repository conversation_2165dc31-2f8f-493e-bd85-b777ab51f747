# NOTICE !!!
# 不推荐直接执行该脚本，为保持生成代码的一致性，请使用 `gen.sh` 脚本，使用统一 protogen 镜像进行代码生成
set -x
find ./proto -name "*.proto" | xargs -I {} protofmt -w {}
protoc --proto_path=/usr/include --go_out=/tmp --go-grpc_out=/tmp -I . $(find ./proto -name "*.proto")
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/*.pb.go"
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/*/*.pb.go"
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/serving/*.pb.go" -omitempty=true -remove_tag_comment=true
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/pipeline/*.pb.go" -omitempty=true -remove_tag_comment=true
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/common/*.pb.go" -omitempty=true -remove_tag_comment=true
protoc-go-inject-tag-sophon -input="/tmp/transwarp.io/aip/llmops-common/pb/dialog.pb.go" -omitempty=true -remove_tag_comment=true
rm -rf ./pb
mv /tmp/transwarp.io/aip/llmops-common/pb ./pb
chmod -R 777 ./pb
