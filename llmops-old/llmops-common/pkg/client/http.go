package client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

type HttpCli struct {
	cli *http.Client
}

func (h *HttpCli) Init(cli *http.Client) {
	h.cli = cli
}

func (h *HttpCli) newReq(ctx context.Context, method, requestUrl string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, requestUrl, body)
	if err != nil {
		return nil, err
	}
	// 添加请求头
	tk, err := GetToken(ctx)
	if err != nil {
		return nil, err
	}
	req.Header.Add(AUTH_HEADER, tk)
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

func (h *HttpCli) HttpGet(ctx context.Context, requestUrl string, result interface{}) error {
	req, err := h.newReq(ctx, http.MethodGet, requestUrl, nil)
	if err != nil {
		return err
	}
	// 发送请求并获取响应
	resp, err := h.cli.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(body))
	}
	if err := json.Unmarshal(body, &result); err != nil {
		return errors.New(fmt.Sprintf("format result err :%v", string(body)))
	}
	return nil
}

func (h *HttpCli) HttpPut(ctx context.Context, requestUrl string, body io.Reader, result interface{}) error {
	req, err := h.newReq(ctx, http.MethodPut, requestUrl, body)
	if err != nil {
		return err
	}
	// 发送请求并获取响应
	resp, err := h.cli.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	rsp, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("status code:%d, err message: %s", resp.StatusCode, string(rsp))
	}
	if result != nil {
		if err := json.Unmarshal(rsp, &result); err != nil {
			return fmt.Errorf("unmarshal result: %w : %v", err, string(rsp))
		}
	}
	return nil
}
