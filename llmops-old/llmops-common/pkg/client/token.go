package client

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"github.com/dgrijalva/jwt-go"
	"google.golang.org/grpc/metadata"
	"strings"
	"time"
)

const (
	Secret       = "simpleSecret"
	UserName     = "username"
	Roles        = "roles"
	Scope        = "scope"
	Apikey       = "apikey"
	TOKEN_PREFIX = "Bearer "
	AUTH_HEADER  = "Authorization"
	COOKIE       = "Cookie"
)

type TokenInfo struct {
	UserName string
	Roles    []string
	Scope    string
	ApiKey   string
}

func (this *TokenInfo) toMap() map[string]string {
	params := make(map[string]string, 0)
	params[UserName] = this.UserName
	roles, _ := json.Marshal(this.Roles)
	params[Roles] = string(roles)
	params[Scope] = this.Scope
	params[Apikey] = this.ApiKey
	return params
}

func FromMap(params map[string]string) (*TokenInfo, error) {
	info := &TokenInfo{}
	if _, ok := params[UserName]; ok {
		info.UserName = params[UserName]
	} else {
		return nil, errors.New("invalid username.")
	}
	if _, ok := params[Roles]; ok {
		var roles []string
		json.Unmarshal([]byte(params[Roles]), &roles)
		info.Roles = roles
	} else {
		return nil, errors.New("invalid roles.")
	}
	if _, ok := params[Scope]; ok {
		info.Scope = params[Scope]
	}
	if _, ok := params[Apikey]; ok {
		info.ApiKey = params[Apikey]
	}
	return info, nil
}

func Encode(info *TokenInfo) string {
	token := jwt.New(jwt.SigningMethodHS512)
	claims := make(jwt.MapClaims)
	claims["exp"] = time.Now().Add(time.Hour * time.Duration(48)).Unix()
	claims["iat"] = time.Now().Add(-time.Hour * time.Duration(24)).Unix()
	for key, value := range info.toMap() {
		claims[key] = value
	}
	token.Claims = claims
	mySignKeyBytes, _ := base64.URLEncoding.DecodeString(Secret)
	tokenString, _ := token.SignedString(mySignKeyBytes)
	return TOKEN_PREFIX + tokenString
}

func Decode(token string) (*TokenInfo, error) {
	if strings.HasPrefix(token, TOKEN_PREFIX) {
		token = strings.Replace(token, TOKEN_PREFIX, "", 1)
	}
	mySignKeyBytes, err := base64.URLEncoding.DecodeString(Secret)
	if err != nil {
		return nil, err
	}
	parseAuth, err := jwt.Parse(token, func(*jwt.Token) (interface{}, error) {
		return mySignKeyBytes, nil
	})
	if err != nil {
		return nil, err
	}
	claim := parseAuth.Claims.(jwt.MapClaims)
	info := &TokenInfo{}
	for key, val := range claim {
		if key == UserName {
			info.UserName = val.(string)
		} else if key == Roles {
			var roles []string
			_, ok := val.(string)
			if !ok {
				v := val.([]interface{})
				for _, role := range v {
					roles = append(roles, role.(string))
				}
			} else {
				json.Unmarshal([]byte(val.(string)), &roles)
			}
			info.Roles = roles
		} else if key == Scope {
			info.Scope = val.(string)
		}
	}
	return info, nil
}

func ExtractUsername(ctx context.Context) (string, error) {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		key := strings.ToLower(AUTH_HEADER)
		if md[key] != nil && len(md[key]) > 0 {
			t := md[key][0]
			info, err := Decode(t)
			return info.UserName, err
		}
	}
	return "", errors.New("no token or invalid token")
}

func ExtractRoles(ctx context.Context) ([]string, error) {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		key := strings.ToLower(AUTH_HEADER)
		if md[key] != nil && len(md[key]) > 0 {
			t := md[key][0]
			info, err := Decode(t)
			return info.Roles, err
		}
	}
	return nil, errors.New("no token or invalid token")
}

func GetToken(ctx context.Context) (string, error) {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		key := strings.ToLower(AUTH_HEADER)
		if md[key] != nil && len(md[key]) > 0 {
			t := md[key][0]
			return t, nil
		}
	}
	return "", errors.New("no token or invalid token")
}

func SetToken(ctx context.Context, tk string) context.Context {
	MD, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		MD = metadata.New(make(map[string]string))
	}
	MD.Set(AUTH_HEADER, tk)
	ctx = metadata.NewIncomingContext(ctx, MD)
	return ctx
}
