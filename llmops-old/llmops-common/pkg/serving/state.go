package serving

type MLopsSvcState string

const (
	MLOpsSvcStateCreating  MLopsSvcState = "Creating"  // 上线中
	MLOpsSvcStatePending   MLopsSvcState = "Pending"   // 等待调度
	MLOpsSvcStateAvailable MLopsSvcState = "Available" // 运行中
	MLOpsSvcStateFailed    MLopsSvcState = "Failed"    // 上线失败
	MLOpsSvcStateOffline   MLopsSvcState = "Offline"   // 下线 k8s 中获取不到 待上线 = Offline + 审批状态为空 || Offline + ApprovalInit

	MLOpsSvcStateApprovalInit     MLopsSvcState = "ApprovalInit"     // 未发起审批
	MLOpsSvcStateUnderApproval    MLopsSvcState = "UnderApproval"    // 审批中
	MLOpsSvcStateApprovalRejected MLopsSvcState = "ApprovalRejected" // 审批拒绝
	MLOpsSvcStateApprovalPassed   MLopsSvcState = "ApprovalPassed"   // 审批通过
)
