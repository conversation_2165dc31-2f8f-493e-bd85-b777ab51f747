package serving

import (
	"github.com/google/uuid"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	ResourceGpuCoreHami      = "nvidia.com/gpucores"
	ResourceGpuMemoryHami    = "nvidia.com/gpumem"
	ResourceGPUCntHami       = "nvidia.com/gpu"
	ResourceGPUCardToUseHami = "nvidia.com/use-gpuuuid"
	ResourceGPUTypeToUseHami = "nvidia.com/use-gputype"

	ResourceGPUCardToUseHamiAscend = "hami.io/use-%s-uuid"
	ResourceGPUCntHamiAscend       = "huawei.com/%s"
	ResourceGPUCntHamiAscendMemory = "huawei.com/%s-memory"

	TopologyKeyHostName = "kubernetes.io/hostname"

	DefaultResourceRdmaHca = "rdma/hca_shared_devices_a"
	EnvResourceRdmaHca     = "RDMA_HCA_RESOURCE_NAME"
)

const (
	GPUTypeNvidia = "Nvidia"
	GPUTypeAscend = "Ascend"
)

type NodeArch string

const (
	NodeArchAMD64 NodeArch = "amd64"
	NodeArchARM64 NodeArch = "arm64"

	NodesExclusiveLabelKey = "nodes-exclusive/uuid"
)

type PodResourceSpec struct {
	Annotations map[string]string
	Affinity    *v1.Affinity
	Container   *ContainerSpec
	Labels      map[string]string
}

type ContainerSpec struct {
	Resources *v1.ResourceRequirements
}

// 节点间互斥
func NewNodesExclusive() (paa *v1.PodAntiAffinity, labels map[string]string) {
	labels = make(map[string]string)
	uuid := uuid.NewString()
	labels[NodesExclusiveLabelKey] = uuid

	paa = &v1.PodAntiAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution: []v1.PodAffinityTerm{
			{
				TopologyKey: TopologyKeyHostName,
				LabelSelector: &metav1.LabelSelector{
					MatchLabels: labels,
				},
			},
		},
	}

	return paa, labels
}

func NewX86PodAffinity() *v1.Affinity {
	return &v1.Affinity{
		NodeAffinity: &v1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
				NodeSelectorTerms: []v1.NodeSelectorTerm{
					{
						MatchExpressions: newNewX86PodAffinityMathExpressions(),
					},
				},
			},
		},
	}
}
func newNewX86PodAffinityMathExpressions() []v1.NodeSelectorRequirement {
	return newPodArchAffinityMathExpressions(string(NodeArchAMD64))
}

func newPodArchAffinityMathExpressions(arch string) []v1.NodeSelectorRequirement {
	return []v1.NodeSelectorRequirement{
		{
			Key:      v1.LabelArchStable,
			Operator: v1.NodeSelectorOpIn,
			Values:   []string{arch},
		},
	}
}

func newPodLabelSelectorMathExpressions(label string) v1.NodeSelectorRequirement {
	return v1.NodeSelectorRequirement{
		Key:      label,
		Operator: v1.NodeSelectorOpExists,
	}
}
