package serving

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/aip/llmops-common/pkg/expense"
)

const (
	ExclusiveAscend = "exclusive"
)

var HalfHamiGpuTemplate = map[string]string{
	"Ascend910A": "vir16",
	"Ascend910B": "vir10_3c_32g",
	"Ascend310P": "vir04",
}

// PodResourceDTO 服务资源，对接前端的结构
type PodResourceDTO struct {
	// 限制调度范围
	NodeChooseCfg *serving.NodeChooseCfg `json:"node_choose_cfg"`
	// 实例规格
	ResourceRuleID int64 `json:"resource_rule_id"`
	// 实例规格json
	ResourceRule string `json:"resource_rule"`
	// 高级模式下的，资源配置
	Resource *serving.Resource `json:"resource"`
	// 服务架构
	Arch *string `json:"arch"`
	// 分布式配置
	Distributed *serving.DistributedCfg `json:"distributed"`
	// 是否高级模式
	IsAdvancedMode bool `json:"is_advanced_mode"`
	// 资源组
	ResourceGroups []*serving.ResourceGroupInfo `json:"resource_groups"`
}

func (p *PodResourceDTO) ToPodResourceDO(ctx context.Context) (*PodResourceSpec, error) {
	res := &PodResourceSpec{}
	label, err := LabelSelectorToStr(ctx)
	if err != nil {
		return nil, err
	}
	affinity, err := NodeAffinityCfgToSeldon(p.NodeChooseCfg, p.Arch, label)
	if err != nil {
		return nil, err
	}
	res.Affinity = affinity
	// rule id获取resource
	if !p.IsAdvancedMode {
		p.Resource, p.ResourceRule, err = ResourceRuleIDToResource(ctx, p.ResourceRuleID)
		if err != nil {
			return nil, err
		}
	}

	if p.Resource == nil {
		return nil, errors.New("resource is nil")
	}

	k8sRs, err := ResourceToSeldon(ctx, p.Resource)
	if err != nil {
		return nil, err
	}
	res.Container = &ContainerSpec{Resources: k8sRs}
	anno, err := ResourceToPodAnno(ctx, getGPUCards(p.NodeChooseCfg), p.Resource)
	if err != nil {
		return nil, err
	}
	for k, v := range p.Resource.Annotations {
		anno[k] = v
	}
	res.Annotations = anno

	err = p.handleDistributed(ctx, res)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (p PodResourceDTO) handleDistributed(ctx context.Context, res *PodResourceSpec) error {
	if p.Distributed != nil && p.Distributed.Enabled {
		if p.Distributed.NodesExclusive {
			paa, l := NewNodesExclusive()
			if res.Affinity == nil {
				res.Affinity = &v1.Affinity{}
			}
			if res.Affinity.PodAntiAffinity == nil {
				res.Affinity.PodAntiAffinity = &v1.PodAntiAffinity{}
			}
			res.Affinity.PodAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution = append(res.Affinity.PodAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution, paa.RequiredDuringSchedulingIgnoredDuringExecution...)
			if res.Labels == nil {
				res.Labels = l
			} else {
				for k, v := range l {
					res.Labels[k] = v
				}
			}
		}
		if p.Distributed.EnableRdmaHca {

			if res.Container == nil {
				res.Container = &ContainerSpec{}
			}
			if res.Container.Resources == nil {
				res.Container.Resources = &v1.ResourceRequirements{}
			}
			appendRdmaHcaResource(res.Container.Resources)
		}
	}
	return nil
}

func ResourceToPodAnno(ctx context.Context, gpus []string, rs *serving.Resource) (map[string]string, error) {
	res := make(map[string]string)
	if len(gpus) > 0 {
		gpuAnno := ResourceGPUCardToUseHami
		if rs.GpuType == GPUTypeAscend {
			gpuAnno = fmt.Sprintf(ResourceGPUCardToUseHamiAscend, rs.AscendConfig.NpuName)
			npuIds := make([]string, 0)
			npuINfos, err := expense.QueryNPUIDInfo(ctx)
			if err != nil {
				return nil, err
			}
			for _, g := range gpus {
				npu, err := npuINfos.GetIDInfoFromVDIEID(g)
				if err != nil {
					return nil, err
				}
				npuIds = append(npuIds, npu.HamiID)
			}
			gpus = npuIds
		}
		res[gpuAnno] = GPUCardsToString(gpus)
	}
	return res, nil
}

func GPUCardsToString(cards []string) string {
	return strings.Join(cards, ",")
}

func getGPUCards(node *serving.NodeChooseCfg) []string {
	res := make([]string, 0)
	if node == nil || !node.Enabled {
		return res
	}
	for _, c := range node.Nodes {
		res = append(res, c.GpuCards...)
	}
	return res
}

func ResourceRuleIDToResource(ctx context.Context, ruleID int64) (*serving.Resource, string, error) {
	rule, err := expense.QueryRuleByID(ctx, ruleID)
	if err != nil {
		return nil, "", err
	}
	if rule == nil {
		return nil, "", errors.New("rule not found")
	}
	res, err := resourceRuleResourceInfo(rule)
	if err != nil {
		return nil, "", err
	}
	ruleJson, err := json.Marshal(rule)
	if err != nil {
		return nil, "", err
	}
	return res, string(ruleJson), nil
}

func LabelSelectorToStr(ctx context.Context) (string, error) {
	label, err := expense.QueryLabelSelector(ctx)
	if err != nil {
		return "", err
	}
	if len(label.LabelSelector) == 0 {
		return "", nil
	}
	return label.LabelSelector, nil
}

func ResourceToSeldon(ctx context.Context, rs *serving.Resource) (*v1.ResourceRequirements, error) {
	CPUQuantityLimit, err := resource.ParseQuantity(rs.CpuLimit)
	if err != nil {
		return nil, err
	}
	MemoryQuantityLimit, err := resource.ParseQuantity(rs.MemoryLimit)
	if err != nil {
		return nil, err
	}
	CPUQuantityRequest, err := resource.ParseQuantity(rs.CpuRequest)
	if err != nil {
		return nil, err
	}
	MemoryQuantityRequest, err := resource.ParseQuantity(rs.MemoryRequest)
	if err != nil {
		return nil, err
	}
	limits, requests := v1.ResourceList{
		v1.ResourceCPU:    CPUQuantityLimit,
		v1.ResourceMemory: MemoryQuantityLimit,
	}, v1.ResourceList{
		v1.ResourceCPU:    CPUQuantityRequest,
		v1.ResourceMemory: MemoryQuantityRequest,
	}

	if rs.GpuType != "" && (rs.GpuMemory != "" || rs.GpuCore != "" || rs.AscendConfig != nil) {
		resourceLimitRequest := []v1.ResourceList{limits, requests}
		for _, r := range resourceLimitRequest {
			var cnt int
			if strings.EqualFold(rs.GpuType, GPUTypeAscend) {
				if cnt, err = appendAscendNpuCoreResource(ctx, &r, rs.AscendConfig); err != nil {
					return nil, err
				}
			} else {
				if cnt, err = appendGPUResource(&r, rs.GpuCore, rs.GpuMemory); err != nil {
					return nil, err
				}
			}
			rs.GpuCount = strconv.Itoa(cnt)
		}
	}
	return &v1.ResourceRequirements{
		Limits:   limits,
		Requests: requests,
	}, nil
}

func getRdmaHcaResourceNameKey() string {
	if k := os.Getenv(EnvResourceRdmaHca); k != "" {
		return k
	}
	return DefaultResourceRdmaHca
}

func appendRdmaHcaResource(r *v1.ResourceRequirements) {
	k := v1.ResourceName(getRdmaHcaResourceNameKey())
	r.Requests[k] = resource.MustParse("1")
	r.Limits[k] = resource.MustParse("1")
}

func appendGPUResource(resourceList *v1.ResourceList, gpuCore string, gpuMemory string) (int, error) {
	var cnt int
	if gpuCore != "" {
		core, err := resource.ParseQuantity(gpuCore)
		if err != nil {
			return 0, err
		}
		if core.Value() < 100 {
			cnt = 1
			(*resourceList)[ResourceGPUCntHami] = resource.MustParse("1")
			(*resourceList)[ResourceGpuCoreHami] = core
		} else {
			cnt = int(core.Value() / 100)
			(*resourceList)[ResourceGPUCntHami] = resource.MustParse(strconv.Itoa(cnt))
			(*resourceList)[ResourceGpuCoreHami] = resource.MustParse("100")
		}
	}
	if gpuMemory != "" {
		memory, err := resource.ParseQuantity(gpuMemory)
		if err != nil {
			return 0, err
		}
		memMi := strconv.Itoa(int(memory.Value() / 1024 / 1024))
		(*resourceList)[ResourceGpuMemoryHami] = resource.MustParse(memMi)
	}
	return cnt, nil
}

func appendAscendNpuCoreResource(ctx context.Context, resourceList *v1.ResourceList, cfg *serving.AscendResourceCfg) (int, error) {
	ascendCfg, err := expense.GetAscendCfg(ctx)
	if err != nil {
		return 0, err
	}
	tmp, err := ascendCfg.GetCfgByTemplate(cfg.NpuName, cfg.TemplateName)
	if err != nil {
		return 0, err
	}
	core := fmt.Sprintf(ResourceGPUCntHamiAscend, cfg.NpuName)
	mem := fmt.Sprintf(ResourceGPUCntHamiAscendMemory, cfg.NpuName)
	npuCnt := 1
	if cfg.TemplateName == expense.AscendExclusiveTemplateName {
		// 独占
		npuCnt = int(cfg.Cnt)
	} else {
		// 非独占
		(*resourceList)[v1.ResourceName(mem)] = resource.MustParse(fmt.Sprintf("%d", tmp.Memory))
	}
	(*resourceList)[v1.ResourceName(core)] = resource.MustParse(strconv.Itoa(npuCnt))
	return npuCnt, nil
}

func resourceRuleResourceInfo(rule *expense.SourceModel) (*serving.Resource, error) {
	res := &serving.Resource{
		CpuRequest:    "200m",
		MemoryRequest: "200Mi",
		CpuLimit:      strconv.Itoa(rule.Cpu),
		MemoryLimit:   fmt.Sprintf("%dGi", rule.Memory),
		GpuType:       rule.GpuVendor,
	}
	if rule.Gpu > 0 {
		res.GpuCore = strconv.Itoa(int(rule.Gpu * float64(100)))
	}
	if rule.GpuMemory > 0 {
		res.GpuMemory = fmt.Sprintf("%dGi", rule.GpuMemory)
	}
	if rule.GpuVendor == "Ascend" {
		template := ExclusiveAscend
		if rule.Gpu < 1 {
			template = HalfHamiGpuTemplate[rule.GpuName]
		}
		ascendRuleCnt := rule.Gpu
		if ascendRuleCnt < 1 {
			ascendRuleCnt = 1
		}
		res.AscendConfig = &serving.AscendResourceCfg{
			NpuName:      rule.GpuName,
			TemplateName: template,
			Cnt:          int32(ascendRuleCnt),
		}
	} else if strings.ToLower(rule.GpuVendor) == "nvidia" {
		if rule.GpuModel != "" {
			if res.Annotations == nil {
				res.Annotations = make(map[string]string)
			}
			res.Annotations[ResourceGPUTypeToUseHami] = rule.GpuModel
		}
	}
	return res, nil
}

func NodeAffinityCfgToSeldon(cfg *serving.NodeChooseCfg, arch *string, label string) (*v1.Affinity, error) {
	res := make([]string, 0)
	expressions := make([]v1.NodeSelectorRequirement, 0)

	// 默认不添加架构亲和
	if arch != nil && *arch != "" {
		expressions = append(expressions, newPodArchAffinityMathExpressions(*arch)...)
	}
	if cfg != nil && cfg.Strategy == serving.NodeChooseStrategy_NODE_CHOOSE_STRATEGY_FIX && cfg.Enabled {
		if len(cfg.Nodes) == 0 {
			return nil, errors.New("empty node id to choose")
		}
		// todo 只统计节点的信息
		for _, node := range cfg.Nodes {
			res = append(res, node.Node)
		}
		expressions = append(expressions, v1.NodeSelectorRequirement{
			Key:      v1.LabelHostname,
			Operator: v1.NodeSelectorOpIn,
			Values:   res,
		})
	}

	// filter by label
	if len(label) != 0 {
		expressions = append(expressions, newPodLabelSelectorMathExpressions(label))
	}

	if len(expressions) != 0 {
		return &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: expressions,
						},
					},
				},
			},
		}, nil
	}
	return nil, nil
}
