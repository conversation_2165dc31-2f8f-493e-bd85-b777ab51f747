package expense

import (
	"github.com/davecgh/go-spew/spew"
	"log"
	"testing"
)

func TestPrintAscendCfg(t *testing.T) {
	ascendCfg = &AscendConfig{
		VNPUs: []*VNPUConfig{&VNPUConfig{
			CommonWord:         "111",
			ChipName:           "222",
			ResourceName:       "",
			ResourceMemoryName: "",
			MemoryAllocatable:  0,
			MemoryCapacity:     0,
			AICore:             0,
			AICPU:              0,
			Templates:          nil,
		}, &VNPUConfig{
			CommonWord:         "1",
			ChipName:           "2",
			ResourceName:       "",
			ResourceMemoryName: "",
			MemoryAllocatable:  0,
			MemoryCapacity:     0,
			AICore:             0,
			AICPU:              0,
			Templates:          nil,
		}},
	}
	log.Default().Printf("config :%+v", ascendCfg)
	log.Default().Printf("config :%v", spew.Sdump(nil))
	log.Default().Printf("config :%+v", spew.Sdump(ascendCfg))
}
