package expense

import (
	"context"
	"errors"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"log"
)

//const (
//	DefaultAscendCfg = "    vnpus:\n      - chipName: 910B\n        commonWord: Ascend910A\n        resourceName: huawei.com/Ascend910A\n        resourceMemoryName: huawei.com/Ascend910A-memory\n        memoryAllocatable: 32768\n        memoryCapacity: 32768\n        aiCore: 30\n        templates:\n          - name: vir02\n            memory: 2184\n            aiCore: 2\n          - name: vir04\n            memory: 4369\n            aiCore: 4\n          - name: vir08\n            memory: 8738\n            aiCore: 8\n          - name: vir16\n            memory: 17476\n            aiCore: 16\n      - chipName: 910B2\n        commonWord: Ascend910B\n        resourceName: huawei.com/Ascend910B\n        resourceMemoryName: huawei.com/Ascend910B-memory\n        memoryAllocatable: 65536\n        memoryCapacity: 65536\n        aiCore: 24\n        aiCPU: 6\n        templates:\n          - name: vir12_3c_32g\n            memory: 32896\n            aiCore: 12\n            aiCPU: 3\n          - name: vir06_1c_16g\n            memory: 16384\n            aiCore: 6\n            aiCPU: 1\n          - name: vir03_1c_8g\n            memory: 8192\n            aiCore: 3\n            aiCPU: 1\n      - chipName: 910B3\n        commonWord: Ascend910B\n        resourceName: huawei.com/Ascend910B\n        resourceMemoryName: huawei.com/Ascend910B-memory\n        memoryAllocatable: 65536\n        memoryCapacity: 65536\n        aiCore: 20\n        aiCPU: 7\n        templates:\n          - name: vir05_1c_16g\n            memory: 16384\n            aiCore: 5\n            aiCPU: 1\n          - name: vir10_3c_32g\n            memory: 32768\n            aiCore: 10\n            aiCPU: 3\n      - chipName: 910B4\n        commonWord: Ascend910B\n        resourceName: huawei.com/Ascend910B\n        resourceMemoryName: huawei.com/Ascend910B-memory\n        memoryAllocatable: 32768\n        memoryCapacity: 32768\n        aiCore: 20\n        aiCPU: 6\n        templates:\n          - name: vir05_1c_8g\n            memory: 8192\n            aiCore: 5\n            aiCPU: 1\n          - name: vir10_3c_16g\n            memory: 16384\n            aiCore: 10\n            aiCPU: 3\n      - chipName: 310P3\n        commonWord: Ascend310P\n        resourceName: huawei.com/Ascend310P\n        resourceMemoryName: huawei.com/Ascend310P-memory\n        memoryAllocatable: 21527\n        memoryCapacity: 24576\n        aiCore: 8\n        aiCPU: 7\n        templates:\n          - name: vir01\n            memory: 3072\n            aiCore: 1\n            aiCPU: 1\n          - name: vir02\n            memory: 6144\n            aiCore: 2\n            aiCPU: 2\n          - name: vir04\n            memory: 12288\n            aiCore: 4\n            aiCPU: 4"
//)

const (
	//AscendCMNamespace           = "kube-system"
	//AscendCMName                = "hami-scheduler-device-ascend"
	//AscendCfgPath               = "ascend-config.yaml"
	AscendExclusiveTemplateName = "exclusive"
)

var ascendCfg *AscendConfig

type AscendConfig struct {
	VNPUs []*VNPUConfig `json:"vnpus"`
}

type VNPUConfig struct {
	CommonWord         string      `json:"commonWord"`
	ChipName           string      `json:"chipName"`
	ResourceName       string      `json:"resourceName"`
	ResourceMemoryName string      `json:"resourceMemoryName"`
	MemoryAllocatable  int64       `json:"memoryAllocatable"`
	MemoryCapacity     int64       `json:"memoryCapacity"`
	AICore             int32       `json:"aiCore"`
	AICPU              int32       `json:"aiCPU"`
	Templates          []*Template `json:"templates"`
}

type Template struct {
	Name   string `json:"name"`
	Memory int64  `json:"memory"`
	AICore int32  `json:"aiCore,omitempty"`
	AICPU  int32  `json:"aiCPU,omitempty"`
	Desc   string `json:"desc"`
}

func (a *AscendConfig) GetNPUNames() []string {
	res := make([]string, 0)
	for _, v := range a.VNPUs {
		res = append(res, v.CommonWord)
	}
	return res
}

func (a *AscendConfig) FillConfig() {
	for _, v := range a.VNPUs {
		v.addExclusiveTemplate()
		for _, t := range v.Templates {
			t.fillDesc()
		}
	}
}

func (a *AscendConfig) GetCfgByTemplate(npuName string, template string) (*Template, error) {
	for _, v := range a.VNPUs {
		if v.CommonWord != npuName {
			continue
		}
		for _, t := range v.Templates {
			if t.Name == template {
				return t, nil
			}
		}
	}
	return nil, errors.New(fmt.Sprintf("template %v not found", template))
}

func (t *Template) fillDesc() {
	if t.Desc == "" {
		t.Desc = fmt.Sprintf("%s-%dG-%d核", t.Name, t.Memory/1024, t.AICore)
	}
}

func (a *VNPUConfig) addExclusiveTemplate() {
	a.Templates = append([]*Template{
		&Template{
			Name:   AscendExclusiveTemplateName,
			Memory: a.MemoryCapacity,
			AICore: a.AICore,
			AICPU:  a.AICPU,
			Desc:   fmt.Sprintf("独占-%dG-%d核", a.MemoryCapacity/1024, a.AICore),
		},
	}, a.Templates...)
}

func GetAscendCfg(ctx context.Context) (*AscendConfig, error) {
	if ascendCfg == nil {
		cfg, err := QueryAscendConfig(ctx)
		if err != nil {
			log.Default().Printf("init ascend config from expense failed, err :%v", err)
			return nil, err
		}
		ascendCfg = cfg
		spew.Dump()
		log.Default().Printf("init ascend config from expense, result is :%v", spew.Sdump(ascendCfg))
	}
	return ascendCfg, nil
}
