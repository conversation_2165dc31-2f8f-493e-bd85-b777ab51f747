package expense

import (
	"errors"
	"fmt"
)

type RuleStatus int

const (
	RuleStatus_Enable  RuleStatus = 1 // 启用
	RuleStatus_Disable RuleStatus = 2
)

type AscendNPUIDInfos struct {
	Infos []*AscendNPUIDInfo
}

type AscendNPUIDInfo struct {
	HamiID string // hami 的 uuid id
	Node   string
	VDIEID string // npu exporter 的uuid
	NPUIdx string
}

func (a AscendNPUIDInfos) GetIDInfoFromVDIEID(vdieID string) (*AscendNPUIDInfo, error) {
	for _, n := range a.Infos {
		if n.VDIEID == vdieID {
			return n, nil
		}
	}
	return nil, errors.New(fmt.Sprintf("can not find npu by vdie id :%v", vdieID))
}

type ExpenseRuleListReq struct {
	Id     uint            `json:"id"`
	Type   ExpenseRuleType `json:"type"`
	Locale string          `json:"locale"` // en, zh, ..
}

type ExpenseRuleResp struct {
	Id            uint            `json:"id"`
	Name          string          `json:"name"`
	Description   string          `json:"description"`
	Price         float64         `json:"price"`
	Unit          string          `json:"unit"`
	Type          ExpenseRuleType `json:"type"`
	CreatedUser   string          `json:"created_user"`
	CreatedAtUnix int64           `json:"created_at_unix" description:"秒时间戳"` // 秒
	Status        RuleStatus      `json:"status" description:"1:启用, 2:禁用"`
	Source        *SourceModel    `json:"source"`
}

type SourceModel struct {
	GpuName    string  `json:"gpuName"`
	FloatPower string  `json:"floatPower"`
	Cpu        int     `json:"cpu"`
	Memory     int     `json:"memory"`
	Gpu        float64 `json:"gpu"`
	GpuMemory  int     `json:"gpuMemory"`
	GpuVendor  string  `json:"gpuVendor"`
	GpuModel   string  `json:"gpuModel"`
	Disk       int     `json:"disk"`
}

type ExpenseRuleType string

const (
	ExpenseRuleTypeToken     ExpenseRuleType = "TOKEN"
	ExpenseRuleTypePower     ExpenseRuleType = "POWER"
	ExpenseRuleTypeDisk      ExpenseRuleType = "DISK"
	ExpenseRuleTypeKnowledge ExpenseRuleType = "KNOWLEDGE"
	ExpenseRuleTypeStorage   ExpenseRuleType = "STORAGE"

	// 单独资源计费，页面不予展示

	ExpenseRuleTypeCpu             ExpenseRuleType = "CPU"
	ExpenseRuleTypeGpu             ExpenseRuleType = "GPU"
	ExpenseRuleTypeMemory          ExpenseRuleType = "MEMORY"
	ExpenseRuleTypeSampleDisk      ExpenseRuleType = "SAMPLED_DISK"
	ExpenseRuleTypeSampleToken     ExpenseRuleType = "SAMPLED_TOKEN"
	ExpenseRuleTypeSampleKnowledge ExpenseRuleType = "SAMPLED_KNOWLEDGE"
)
