package expense

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"transwarp.io/aip/llmops-common/pkg/client"
)

const (
	APIQueryRule           = "/api/v1/expense/rule/%d"
	APIQueryNPUIdInfo      = "/api/v1/resource/npu-id-info"
	APIQueryAscendTemplate = "/api/v1/resource/npu-template"
	defaultUrl             = "http://llmops-expense:9527"
	EnvUrlKey              = "EXPENSE_BASE_URL"
	NodeQuery              = "/api/v1/resource/cluster"
	LabelQuery             = "/api/v1/resource/node/labelselector"
)

func getExpenseUrl() string {
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	return url
}

func QueryRuleByID(ctx context.Context, ID int64) (*SourceModel, error) {
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	api := fmt.Sprintf("%s%s", url, fmt.Sprintf(APIQueryRule, ID))
	res := &ExpenseRuleResp{}
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	if err := cli.HttpGet(ctx, api, res); err != nil {
		return nil, err
	}
	return res.Source, nil
}

func QueryNPUIDInfo(ctx context.Context) (*AscendNPUIDInfos, error) {
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	api := fmt.Sprintf("%s%s", url, APIQueryNPUIdInfo)
	res := &AscendNPUIDInfos{}
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	if err := cli.HttpGet(ctx, api, res); err != nil {
		return nil, err
	}
	return res, nil
}

func QueryComputeNodes(ctx context.Context) (*ClusterResourceInfo, error) {
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	api := fmt.Sprintf("%s%s", url, NodeQuery)
	res := &ClusterResourceInfo{}
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	if err := cli.HttpGet(ctx, api, res); err != nil {
		return nil, err
	}
	return res, nil
}

func QueryLabelSelector(ctx context.Context) (*LabelSelectorResp, error) {
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	api := fmt.Sprintf("%s%s", url, LabelQuery)
	res := &LabelSelectorResp{}
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	if err := cli.HttpGet(ctx, api, res); err != nil {
		return nil, err
	}
	return res, nil
}

func QueryAscendConfig(ctx context.Context) (*AscendConfig, error) {
	res := &AscendConfig{}
	url := os.Getenv(EnvUrlKey)
	if url == "" {
		url = defaultUrl
	}
	api := fmt.Sprintf("%s%s", url, APIQueryAscendTemplate)
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	if err := cli.HttpGet(ctx, api, res); err != nil {
		log.Default().Printf("query ascend config failed :%v", err)
		return nil, err
	}
	return res, nil
}

type ListXPUGroupParam struct {
	Tenants []string // 租户ID，为空则查询所有ResourceGroup
}

// ListNodeXPUGroup 获取xpu group列表
func ListNodeXPUGroup(ctx context.Context, param *ListXPUGroupParam) ([]ResourceGroup, error) {
	url := getExpenseUrl()
	api := fmt.Sprintf("%s%s", url, "/api/v1/resource/group")
	if len(param.Tenants) > 0 {
		api = fmt.Sprintf("%s?tenant_id=%s", api, strings.Join(param.Tenants, ","))
	}
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)
	res := &[]ResourceGroup{}
	if err := cli.HttpGet(ctx, api, res); err != nil {
		log.Default().Printf("list group failed :%v", err)
		return nil, err
	}
	return *res, nil
}

type BindingXPUGroupParam struct {
	XPUGroup []string // xpu group id
	TenantID string   // 租户ID
}

// BindingXPUGroupToTenant 绑定租户与xpu group
// 注意点：XPUGroupBinding是一个xpuGroup对多个tenant，业务场景是一个tenant 绑定多个XPUGroup
// 接口为upsert语义，若不存在xpuGroup则新建binding，若存在则添加binding租户，租户有可能已存在与binding中则不处理
func BindingXPUGroupToTenant(ctx context.Context, param *BindingXPUGroupParam) error {
	if param.TenantID == "" || len(param.XPUGroup) == 0 {
		return errors.New("param is empty")
	}
	url := getExpenseUrl()
	api := fmt.Sprintf("%s%s/%s", url, "/api/v1/resource/group/tenant", param.TenantID)
	cli := client.HttpCli{}
	cli.Init(http.DefaultClient)

	body, _ := json.Marshal(param.XPUGroup)
	err := cli.HttpPut(ctx, api, bytes.NewReader(body), nil)
	if err != nil {
		return fmt.Errorf("bind tenant group: %w", err)
	}
	return nil
}
