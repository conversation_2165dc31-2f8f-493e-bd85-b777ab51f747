package expense

type NodeStatus string

type ClusterResourceInfo struct {
	Clusters []ClusterInfo      `json:"clusters"`
	Nodes    []NodeResourceInfo `json:"nodes"`
}

type ClusterInfo struct {
	Id        string `json:"id"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type NodeResourceInfo struct {
	Name           string            `json:"name"`
	Cpu            ResourceInfo      `json:"cpu"`
	Memory         ResourceInfo      `json:"memory"`
	GpuCard        ResourceInfo      `json:"gpu_card"`    // 卡数量
	VGpuCore       ResourceInfo      `json:"vgpu_core"`   // gpu 核心
	VGpuMemory     ResourceInfo      `json:"vgpu_memory"` // gpu 显存
	Pod            ResourceInfo      `json:"pod"`
	Gpus           []NodeGpu         `json:"gpus"`
	Status         []NodeStatus      `json:"status"` // 节点状态 Ready, NotReady, Unknown, SchedulingDisabled
	Arch           string            `json:"arch"`
	CreateTimeUnix int64             `json:"create_time_unix"`
	Labels         map[string]string `json:"labels"` // node 的 labels
	NodeSystemInfo NodeSystemInfo    `json:"node_system_info"`
}

type NodeGpu struct {
	Number             string  `json:"number"`             // gpu index
	ID                 string  `json:"id"`                 // gpu uuid
	Name               string  `json:"name"`               // gpu name
	TotalVCore         int     `json:"totalVCore"`         // gpu core 总量
	TotalMemory        int64   `json:"totalMemory"`        // gpu 显存总量
	TotalMemoryGIB     float64 `json:"totalMemoryGiB"`     // gpu 显存总量
	Node               string  `json:"node"`               // 节点名称
	Model              string  `json:"model"`              // gpu 型号
	Vendor             string  `json:"vendor"`             // gpu 厂商
	AllocatedVCore     int     `json:"allocatedVCore"`     // 已分配gpu core
	AllocatedMemory    int64   `json:"allocatedMemory"`    //已分配gpu 显存
	AllocatedMemoryGIB float64 `json:"allocatedMemoryGiB"` //已分配gpu 显存 GIB
}

type ResourceInfo struct {
	Allocatable float64 `json:"allocatable"` // 可分配的
	Available   float64 `json:"available"`   // 可用的
	Used        float64 `json:"used"`        // 已分配的
	Unit        string  `json:"unit"`
}

type NodeSystemInfo struct {
	Architecture            string `json:"architecture"`              // "architecture": "amd64",
	BootID                  string `json:"boot_id"`                   // "bootID": "9d3d4eac-7fa7-4d90-8822-9fe4904bc39c",
	ContainerRuntimeVersion string `json:"container_runtime_version"` // "containerRuntimeVersion": "docker://20.10.14",
	KernelVersion           string `json:"kernel_version"`            // "kernelVersion": "3.10.0-1160.el7.x86_64",
	KubeProxyVersion        string `json:"kube_proxy_version"`        // "kubeProxyVersion": "v1.19.15",
	KubeletVersion          string `json:"kubelet_version"`           // "kubeletVersion": "v1.19.15",
	MachineID               string `json:"machine_id"`                // "machineID": "3a0a47a9c867433bab1e93984313c1c1",
	OperatingSystem         string `json:"operating_system"`          // "operatingSystem": "linux",
	OSImage                 string `json:"os_image"`                  // "osImage": "CentOS Linux 7 (Core)",
	SystemUUID              string `json:"system_uuid"`               // "systemUUID": "10049600-C258-11ED-8000-3CECEF36CE9C"
}

type LabelSelectorResp struct {
	LabelSelector string `json:"label_selector"`
}
