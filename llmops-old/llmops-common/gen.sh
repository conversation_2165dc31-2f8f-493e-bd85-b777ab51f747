#!/bin/bash
set -x
SCRIPT=$(readlink -f "$0")
CURT_DIR=$(dirname "$SCRIPT")
docker pull  ***********/aip/deps/protogen:with-inject-tag
docker run \
  --rm \
  --net host \
  -u root:root \
  -v "$CURT_DIR":/llmops-common:rw \
  -v "${GOPATH}/include:/usr/include" \
  ***********/aip/deps/protogen:with-inject-tag \
  bash \
  -c \
  "set -x &&
  apk add protobuf-dev &&
  cd /llmops-common &&
  ./proto-gen.sh"
