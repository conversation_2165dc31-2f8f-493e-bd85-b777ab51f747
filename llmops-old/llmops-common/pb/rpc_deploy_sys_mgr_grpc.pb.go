// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_deploy_sys_mgr.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelDeploySysManager_GetModelDeploySys_FullMethodName        = "/proto.ModelDeploySysManager/GetModelDeploySys"
	ModelDeploySysManager_RegisterModelDeploySys_FullMethodName   = "/proto.ModelDeploySysManager/RegisterModelDeploySys"
	ModelDeploySysManager_UnregisterModelDeploySys_FullMethodName = "/proto.ModelDeploySysManager/UnregisterModelDeploySys"
)

// ModelDeploySysManagerClient is the client API for ModelDeploySysManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelDeploySysManagerClient interface {
	GetModelDeploySys(ctx context.Context, in *GetModelDeploySysReq, opts ...grpc.CallOption) (*GetModelDeploySysRsp, error)
	RegisterModelDeploySys(ctx context.Context, in *RegisterModelDeploySysReq, opts ...grpc.CallOption) (*RegisterModelDeploySysRsp, error)
	UnregisterModelDeploySys(ctx context.Context, in *UnregisterModelDeploySysReq, opts ...grpc.CallOption) (*UnregisterModelDeploySysRsp, error)
}

type modelDeploySysManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewModelDeploySysManagerClient(cc grpc.ClientConnInterface) ModelDeploySysManagerClient {
	return &modelDeploySysManagerClient{cc}
}

func (c *modelDeploySysManagerClient) GetModelDeploySys(ctx context.Context, in *GetModelDeploySysReq, opts ...grpc.CallOption) (*GetModelDeploySysRsp, error) {
	out := new(GetModelDeploySysRsp)
	err := c.cc.Invoke(ctx, ModelDeploySysManager_GetModelDeploySys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelDeploySysManagerClient) RegisterModelDeploySys(ctx context.Context, in *RegisterModelDeploySysReq, opts ...grpc.CallOption) (*RegisterModelDeploySysRsp, error) {
	out := new(RegisterModelDeploySysRsp)
	err := c.cc.Invoke(ctx, ModelDeploySysManager_RegisterModelDeploySys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelDeploySysManagerClient) UnregisterModelDeploySys(ctx context.Context, in *UnregisterModelDeploySysReq, opts ...grpc.CallOption) (*UnregisterModelDeploySysRsp, error) {
	out := new(UnregisterModelDeploySysRsp)
	err := c.cc.Invoke(ctx, ModelDeploySysManager_UnregisterModelDeploySys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelDeploySysManagerServer is the server API for ModelDeploySysManager service.
// All implementations must embed UnimplementedModelDeploySysManagerServer
// for forward compatibility
type ModelDeploySysManagerServer interface {
	GetModelDeploySys(context.Context, *GetModelDeploySysReq) (*GetModelDeploySysRsp, error)
	RegisterModelDeploySys(context.Context, *RegisterModelDeploySysReq) (*RegisterModelDeploySysRsp, error)
	UnregisterModelDeploySys(context.Context, *UnregisterModelDeploySysReq) (*UnregisterModelDeploySysRsp, error)
	mustEmbedUnimplementedModelDeploySysManagerServer()
}

// UnimplementedModelDeploySysManagerServer must be embedded to have forward compatible implementations.
type UnimplementedModelDeploySysManagerServer struct {
}

func (UnimplementedModelDeploySysManagerServer) GetModelDeploySys(context.Context, *GetModelDeploySysReq) (*GetModelDeploySysRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelDeploySys not implemented")
}
func (UnimplementedModelDeploySysManagerServer) RegisterModelDeploySys(context.Context, *RegisterModelDeploySysReq) (*RegisterModelDeploySysRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterModelDeploySys not implemented")
}
func (UnimplementedModelDeploySysManagerServer) UnregisterModelDeploySys(context.Context, *UnregisterModelDeploySysReq) (*UnregisterModelDeploySysRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnregisterModelDeploySys not implemented")
}
func (UnimplementedModelDeploySysManagerServer) mustEmbedUnimplementedModelDeploySysManagerServer() {}

// UnsafeModelDeploySysManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelDeploySysManagerServer will
// result in compilation errors.
type UnsafeModelDeploySysManagerServer interface {
	mustEmbedUnimplementedModelDeploySysManagerServer()
}

func RegisterModelDeploySysManagerServer(s grpc.ServiceRegistrar, srv ModelDeploySysManagerServer) {
	s.RegisterService(&ModelDeploySysManager_ServiceDesc, srv)
}

func _ModelDeploySysManager_GetModelDeploySys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelDeploySysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelDeploySysManagerServer).GetModelDeploySys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelDeploySysManager_GetModelDeploySys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelDeploySysManagerServer).GetModelDeploySys(ctx, req.(*GetModelDeploySysReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelDeploySysManager_RegisterModelDeploySys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterModelDeploySysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelDeploySysManagerServer).RegisterModelDeploySys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelDeploySysManager_RegisterModelDeploySys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelDeploySysManagerServer).RegisterModelDeploySys(ctx, req.(*RegisterModelDeploySysReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelDeploySysManager_UnregisterModelDeploySys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnregisterModelDeploySysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelDeploySysManagerServer).UnregisterModelDeploySys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelDeploySysManager_UnregisterModelDeploySys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelDeploySysManagerServer).UnregisterModelDeploySys(ctx, req.(*UnregisterModelDeploySysReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelDeploySysManager_ServiceDesc is the grpc.ServiceDesc for ModelDeploySysManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelDeploySysManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ModelDeploySysManager",
	HandlerType: (*ModelDeploySysManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetModelDeploySys",
			Handler:    _ModelDeploySysManager_GetModelDeploySys_Handler,
		},
		{
			MethodName: "RegisterModelDeploySys",
			Handler:    _ModelDeploySysManager_RegisterModelDeploySys_Handler,
		},
		{
			MethodName: "UnregisterModelDeploySys",
			Handler:    _ModelDeploySysManager_UnregisterModelDeploySys_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_deploy_sys_mgr.proto",
}
