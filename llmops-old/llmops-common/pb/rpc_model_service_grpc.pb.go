// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_model_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelServiceManager_ReadModelService_FullMethodName     = "/proto.ModelServiceManager/ReadModelService"
	ModelServiceManager_TestModelService_FullMethodName     = "/proto.ModelServiceManager/TestModelService"
	ModelServiceManager_SaveModelService_FullMethodName     = "/proto.ModelServiceManager/SaveModelService"
	ModelServiceManager_TestModelStreamInfer_FullMethodName = "/proto.ModelServiceManager/TestModelStreamInfer"
)

// ModelServiceManagerClient is the client API for ModelServiceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelServiceManagerClient interface {
	ReadModelService(ctx context.Context, in *ReadModelServiceReq, opts ...grpc.CallOption) (*ReadModelServiceRsp, error)
	TestModelService(ctx context.Context, in *TestModelServiceReq, opts ...grpc.CallOption) (*TestModelServiceRsp, error)
	SaveModelService(ctx context.Context, in *SaveModelServiceReq, opts ...grpc.CallOption) (*SaveModelServiceRsp, error)
	TestModelStreamInfer(ctx context.Context, in *TestModelServiceReq, opts ...grpc.CallOption) (ModelServiceManager_TestModelStreamInferClient, error)
}

type modelServiceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewModelServiceManagerClient(cc grpc.ClientConnInterface) ModelServiceManagerClient {
	return &modelServiceManagerClient{cc}
}

func (c *modelServiceManagerClient) ReadModelService(ctx context.Context, in *ReadModelServiceReq, opts ...grpc.CallOption) (*ReadModelServiceRsp, error) {
	out := new(ReadModelServiceRsp)
	err := c.cc.Invoke(ctx, ModelServiceManager_ReadModelService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceManagerClient) TestModelService(ctx context.Context, in *TestModelServiceReq, opts ...grpc.CallOption) (*TestModelServiceRsp, error) {
	out := new(TestModelServiceRsp)
	err := c.cc.Invoke(ctx, ModelServiceManager_TestModelService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceManagerClient) SaveModelService(ctx context.Context, in *SaveModelServiceReq, opts ...grpc.CallOption) (*SaveModelServiceRsp, error) {
	out := new(SaveModelServiceRsp)
	err := c.cc.Invoke(ctx, ModelServiceManager_SaveModelService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceManagerClient) TestModelStreamInfer(ctx context.Context, in *TestModelServiceReq, opts ...grpc.CallOption) (ModelServiceManager_TestModelStreamInferClient, error) {
	stream, err := c.cc.NewStream(ctx, &ModelServiceManager_ServiceDesc.Streams[0], ModelServiceManager_TestModelStreamInfer_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &modelServiceManagerTestModelStreamInferClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ModelServiceManager_TestModelStreamInferClient interface {
	Recv() (*ModelStreamInferRsp, error)
	grpc.ClientStream
}

type modelServiceManagerTestModelStreamInferClient struct {
	grpc.ClientStream
}

func (x *modelServiceManagerTestModelStreamInferClient) Recv() (*ModelStreamInferRsp, error) {
	m := new(ModelStreamInferRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ModelServiceManagerServer is the server API for ModelServiceManager service.
// All implementations must embed UnimplementedModelServiceManagerServer
// for forward compatibility
type ModelServiceManagerServer interface {
	ReadModelService(context.Context, *ReadModelServiceReq) (*ReadModelServiceRsp, error)
	TestModelService(context.Context, *TestModelServiceReq) (*TestModelServiceRsp, error)
	SaveModelService(context.Context, *SaveModelServiceReq) (*SaveModelServiceRsp, error)
	TestModelStreamInfer(*TestModelServiceReq, ModelServiceManager_TestModelStreamInferServer) error
	mustEmbedUnimplementedModelServiceManagerServer()
}

// UnimplementedModelServiceManagerServer must be embedded to have forward compatible implementations.
type UnimplementedModelServiceManagerServer struct {
}

func (UnimplementedModelServiceManagerServer) ReadModelService(context.Context, *ReadModelServiceReq) (*ReadModelServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadModelService not implemented")
}
func (UnimplementedModelServiceManagerServer) TestModelService(context.Context, *TestModelServiceReq) (*TestModelServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestModelService not implemented")
}
func (UnimplementedModelServiceManagerServer) SaveModelService(context.Context, *SaveModelServiceReq) (*SaveModelServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveModelService not implemented")
}
func (UnimplementedModelServiceManagerServer) TestModelStreamInfer(*TestModelServiceReq, ModelServiceManager_TestModelStreamInferServer) error {
	return status.Errorf(codes.Unimplemented, "method TestModelStreamInfer not implemented")
}
func (UnimplementedModelServiceManagerServer) mustEmbedUnimplementedModelServiceManagerServer() {}

// UnsafeModelServiceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelServiceManagerServer will
// result in compilation errors.
type UnsafeModelServiceManagerServer interface {
	mustEmbedUnimplementedModelServiceManagerServer()
}

func RegisterModelServiceManagerServer(s grpc.ServiceRegistrar, srv ModelServiceManagerServer) {
	s.RegisterService(&ModelServiceManager_ServiceDesc, srv)
}

func _ModelServiceManager_ReadModelService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadModelServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceManagerServer).ReadModelService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelServiceManager_ReadModelService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceManagerServer).ReadModelService(ctx, req.(*ReadModelServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelServiceManager_TestModelService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestModelServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceManagerServer).TestModelService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelServiceManager_TestModelService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceManagerServer).TestModelService(ctx, req.(*TestModelServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelServiceManager_SaveModelService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveModelServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceManagerServer).SaveModelService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelServiceManager_SaveModelService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceManagerServer).SaveModelService(ctx, req.(*SaveModelServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelServiceManager_TestModelStreamInfer_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TestModelServiceReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ModelServiceManagerServer).TestModelStreamInfer(m, &modelServiceManagerTestModelStreamInferServer{stream})
}

type ModelServiceManager_TestModelStreamInferServer interface {
	Send(*ModelStreamInferRsp) error
	grpc.ServerStream
}

type modelServiceManagerTestModelStreamInferServer struct {
	grpc.ServerStream
}

func (x *modelServiceManagerTestModelStreamInferServer) Send(m *ModelStreamInferRsp) error {
	return x.ServerStream.SendMsg(m)
}

// ModelServiceManager_ServiceDesc is the grpc.ServiceDesc for ModelServiceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelServiceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ModelServiceManager",
	HandlerType: (*ModelServiceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadModelService",
			Handler:    _ModelServiceManager_ReadModelService_Handler,
		},
		{
			MethodName: "TestModelService",
			Handler:    _ModelServiceManager_TestModelService_Handler,
		},
		{
			MethodName: "SaveModelService",
			Handler:    _ModelServiceManager_SaveModelService_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TestModelStreamInfer",
			Handler:       _ModelServiceManager_TestModelStreamInfer_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/rpc_model_service.proto",
}

const (
	RemoteServiceManager_CreateRemoteService_FullMethodName = "/proto.RemoteServiceManager/CreateRemoteService"
	RemoteServiceManager_ReadRemoteService_FullMethodName   = "/proto.RemoteServiceManager/ReadRemoteService"
	RemoteServiceManager_UpdateRemoteService_FullMethodName = "/proto.RemoteServiceManager/UpdateRemoteService"
	RemoteServiceManager_DeleteRemoteService_FullMethodName = "/proto.RemoteServiceManager/DeleteRemoteService"
)

// RemoteServiceManagerClient is the client API for RemoteServiceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RemoteServiceManagerClient interface {
	CreateRemoteService(ctx context.Context, in *CreateRemoteServiceReq, opts ...grpc.CallOption) (*CreateRemoteServiceRsp, error)
	ReadRemoteService(ctx context.Context, in *ReadRemoteServiceReq, opts ...grpc.CallOption) (*ReadRemoteServiceRsp, error)
	UpdateRemoteService(ctx context.Context, in *UpdateRemoteServiceReq, opts ...grpc.CallOption) (*UpdateRemoteServiceRsp, error)
	DeleteRemoteService(ctx context.Context, in *DeleteRemoteServiceReq, opts ...grpc.CallOption) (*DeleteRemoteServiceRsp, error)
}

type remoteServiceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewRemoteServiceManagerClient(cc grpc.ClientConnInterface) RemoteServiceManagerClient {
	return &remoteServiceManagerClient{cc}
}

func (c *remoteServiceManagerClient) CreateRemoteService(ctx context.Context, in *CreateRemoteServiceReq, opts ...grpc.CallOption) (*CreateRemoteServiceRsp, error) {
	out := new(CreateRemoteServiceRsp)
	err := c.cc.Invoke(ctx, RemoteServiceManager_CreateRemoteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *remoteServiceManagerClient) ReadRemoteService(ctx context.Context, in *ReadRemoteServiceReq, opts ...grpc.CallOption) (*ReadRemoteServiceRsp, error) {
	out := new(ReadRemoteServiceRsp)
	err := c.cc.Invoke(ctx, RemoteServiceManager_ReadRemoteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *remoteServiceManagerClient) UpdateRemoteService(ctx context.Context, in *UpdateRemoteServiceReq, opts ...grpc.CallOption) (*UpdateRemoteServiceRsp, error) {
	out := new(UpdateRemoteServiceRsp)
	err := c.cc.Invoke(ctx, RemoteServiceManager_UpdateRemoteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *remoteServiceManagerClient) DeleteRemoteService(ctx context.Context, in *DeleteRemoteServiceReq, opts ...grpc.CallOption) (*DeleteRemoteServiceRsp, error) {
	out := new(DeleteRemoteServiceRsp)
	err := c.cc.Invoke(ctx, RemoteServiceManager_DeleteRemoteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RemoteServiceManagerServer is the server API for RemoteServiceManager service.
// All implementations must embed UnimplementedRemoteServiceManagerServer
// for forward compatibility
type RemoteServiceManagerServer interface {
	CreateRemoteService(context.Context, *CreateRemoteServiceReq) (*CreateRemoteServiceRsp, error)
	ReadRemoteService(context.Context, *ReadRemoteServiceReq) (*ReadRemoteServiceRsp, error)
	UpdateRemoteService(context.Context, *UpdateRemoteServiceReq) (*UpdateRemoteServiceRsp, error)
	DeleteRemoteService(context.Context, *DeleteRemoteServiceReq) (*DeleteRemoteServiceRsp, error)
	mustEmbedUnimplementedRemoteServiceManagerServer()
}

// UnimplementedRemoteServiceManagerServer must be embedded to have forward compatible implementations.
type UnimplementedRemoteServiceManagerServer struct {
}

func (UnimplementedRemoteServiceManagerServer) CreateRemoteService(context.Context, *CreateRemoteServiceReq) (*CreateRemoteServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRemoteService not implemented")
}
func (UnimplementedRemoteServiceManagerServer) ReadRemoteService(context.Context, *ReadRemoteServiceReq) (*ReadRemoteServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadRemoteService not implemented")
}
func (UnimplementedRemoteServiceManagerServer) UpdateRemoteService(context.Context, *UpdateRemoteServiceReq) (*UpdateRemoteServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRemoteService not implemented")
}
func (UnimplementedRemoteServiceManagerServer) DeleteRemoteService(context.Context, *DeleteRemoteServiceReq) (*DeleteRemoteServiceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRemoteService not implemented")
}
func (UnimplementedRemoteServiceManagerServer) mustEmbedUnimplementedRemoteServiceManagerServer() {}

// UnsafeRemoteServiceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RemoteServiceManagerServer will
// result in compilation errors.
type UnsafeRemoteServiceManagerServer interface {
	mustEmbedUnimplementedRemoteServiceManagerServer()
}

func RegisterRemoteServiceManagerServer(s grpc.ServiceRegistrar, srv RemoteServiceManagerServer) {
	s.RegisterService(&RemoteServiceManager_ServiceDesc, srv)
}

func _RemoteServiceManager_CreateRemoteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRemoteServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RemoteServiceManagerServer).CreateRemoteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RemoteServiceManager_CreateRemoteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RemoteServiceManagerServer).CreateRemoteService(ctx, req.(*CreateRemoteServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RemoteServiceManager_ReadRemoteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadRemoteServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RemoteServiceManagerServer).ReadRemoteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RemoteServiceManager_ReadRemoteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RemoteServiceManagerServer).ReadRemoteService(ctx, req.(*ReadRemoteServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RemoteServiceManager_UpdateRemoteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRemoteServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RemoteServiceManagerServer).UpdateRemoteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RemoteServiceManager_UpdateRemoteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RemoteServiceManagerServer).UpdateRemoteService(ctx, req.(*UpdateRemoteServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RemoteServiceManager_DeleteRemoteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRemoteServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RemoteServiceManagerServer).DeleteRemoteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RemoteServiceManager_DeleteRemoteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RemoteServiceManagerServer).DeleteRemoteService(ctx, req.(*DeleteRemoteServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RemoteServiceManager_ServiceDesc is the grpc.ServiceDesc for RemoteServiceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RemoteServiceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.RemoteServiceManager",
	HandlerType: (*RemoteServiceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRemoteService",
			Handler:    _RemoteServiceManager_CreateRemoteService_Handler,
		},
		{
			MethodName: "ReadRemoteService",
			Handler:    _RemoteServiceManager_ReadRemoteService_Handler,
		},
		{
			MethodName: "UpdateRemoteService",
			Handler:    _RemoteServiceManager_UpdateRemoteService_Handler,
		},
		{
			MethodName: "DeleteRemoteService",
			Handler:    _RemoteServiceManager_DeleteRemoteService_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model_service.proto",
}
