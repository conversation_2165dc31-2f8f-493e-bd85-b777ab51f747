// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_model_deployment.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DeploymentManager_ReadDeployment_FullMethodName         = "/proto.DeploymentManager/ReadDeployment"
	DeploymentManager_CreateDeployment_FullMethodName       = "/proto.DeploymentManager/CreateDeployment"
	DeploymentManager_UpdateDeployment_FullMethodName       = "/proto.DeploymentManager/UpdateDeployment"
	DeploymentManager_DeleteDeployments_FullMethodName      = "/proto.DeploymentManager/DeleteDeployments"
	DeploymentManager_StartDeployments_FullMethodName       = "/proto.DeploymentManager/StartDeployments"
	DeploymentManager_StopDeployments_FullMethodName        = "/proto.DeploymentManager/StopDeployments"
	DeploymentManager_WatchDeployment_FullMethodName        = "/proto.DeploymentManager/WatchDeployment"
	DeploymentManager_UpdateDeploymentState_FullMethodName  = "/proto.DeploymentManager/UpdateDeploymentState"
	DeploymentManager_UpdateReplicaStatus_FullMethodName    = "/proto.DeploymentManager/UpdateReplicaStatus"
	DeploymentManager_StartDeploymentMonitor_FullMethodName = "/proto.DeploymentManager/StartDeploymentMonitor"
	DeploymentManager_StopDeploymentMonitor_FullMethodName  = "/proto.DeploymentManager/StopDeploymentMonitor"
)

// DeploymentManagerClient is the client API for DeploymentManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeploymentManagerClient interface {
	ReadDeployment(ctx context.Context, in *ReadDeploymentReq, opts ...grpc.CallOption) (*ReadDeploymentRsp, error)
	CreateDeployment(ctx context.Context, in *CreateDeploymentReq, opts ...grpc.CallOption) (*CreateDeploymentRsp, error)
	UpdateDeployment(ctx context.Context, in *UpdateDeploymentReq, opts ...grpc.CallOption) (*UpdateDeploymentRsp, error)
	DeleteDeployments(ctx context.Context, in *DeleteDeploymentsReq, opts ...grpc.CallOption) (*DeleteDeploymentsRsp, error)
	StartDeployments(ctx context.Context, in *StartDeploymentsReq, opts ...grpc.CallOption) (*StartDeploymentsRsp, error)
	StopDeployments(ctx context.Context, in *StopDeploymentsReq, opts ...grpc.CallOption) (*StopDeploymentsRsp, error)
	WatchDeployment(ctx context.Context, in *WatchDeploymentReq, opts ...grpc.CallOption) (DeploymentManager_WatchDeploymentClient, error)
	// UpdateDeploymentState 整体更新Deployment的运行状态 TODO
	UpdateDeploymentState(ctx context.Context, in *UpdateDeploymentStateReq, opts ...grpc.CallOption) (*UpdateDeploymentStateRsp, error)
	// UpdateReplicaStatus 更新每个replica的状态与指标 TODO
	UpdateReplicaStatus(ctx context.Context, in *UpdateReplicaStatusReq, opts ...grpc.CallOption) (*UpdateReplicaStatusRsp, error)
	// StartDeploymentMonitor mwh-backend为部署系统提供状态更新接口, TODO 暂时作为一种过渡机制直接舍弃mqtt改动较大
	StartDeploymentMonitor(ctx context.Context, in *StartDeploymentMonitorReq, opts ...grpc.CallOption) (*StartDeploymentMonitorRsp, error)
	StopDeploymentMonitor(ctx context.Context, in *StopDeploymentMonitorReq, opts ...grpc.CallOption) (*StopDeploymentMonitorRsp, error)
}

type deploymentManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewDeploymentManagerClient(cc grpc.ClientConnInterface) DeploymentManagerClient {
	return &deploymentManagerClient{cc}
}

func (c *deploymentManagerClient) ReadDeployment(ctx context.Context, in *ReadDeploymentReq, opts ...grpc.CallOption) (*ReadDeploymentRsp, error) {
	out := new(ReadDeploymentRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_ReadDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) CreateDeployment(ctx context.Context, in *CreateDeploymentReq, opts ...grpc.CallOption) (*CreateDeploymentRsp, error) {
	out := new(CreateDeploymentRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_CreateDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) UpdateDeployment(ctx context.Context, in *UpdateDeploymentReq, opts ...grpc.CallOption) (*UpdateDeploymentRsp, error) {
	out := new(UpdateDeploymentRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_UpdateDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) DeleteDeployments(ctx context.Context, in *DeleteDeploymentsReq, opts ...grpc.CallOption) (*DeleteDeploymentsRsp, error) {
	out := new(DeleteDeploymentsRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_DeleteDeployments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) StartDeployments(ctx context.Context, in *StartDeploymentsReq, opts ...grpc.CallOption) (*StartDeploymentsRsp, error) {
	out := new(StartDeploymentsRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_StartDeployments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) StopDeployments(ctx context.Context, in *StopDeploymentsReq, opts ...grpc.CallOption) (*StopDeploymentsRsp, error) {
	out := new(StopDeploymentsRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_StopDeployments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) WatchDeployment(ctx context.Context, in *WatchDeploymentReq, opts ...grpc.CallOption) (DeploymentManager_WatchDeploymentClient, error) {
	stream, err := c.cc.NewStream(ctx, &DeploymentManager_ServiceDesc.Streams[0], DeploymentManager_WatchDeployment_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &deploymentManagerWatchDeploymentClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type DeploymentManager_WatchDeploymentClient interface {
	Recv() (*WatchDeploymentRsp, error)
	grpc.ClientStream
}

type deploymentManagerWatchDeploymentClient struct {
	grpc.ClientStream
}

func (x *deploymentManagerWatchDeploymentClient) Recv() (*WatchDeploymentRsp, error) {
	m := new(WatchDeploymentRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *deploymentManagerClient) UpdateDeploymentState(ctx context.Context, in *UpdateDeploymentStateReq, opts ...grpc.CallOption) (*UpdateDeploymentStateRsp, error) {
	out := new(UpdateDeploymentStateRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_UpdateDeploymentState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) UpdateReplicaStatus(ctx context.Context, in *UpdateReplicaStatusReq, opts ...grpc.CallOption) (*UpdateReplicaStatusRsp, error) {
	out := new(UpdateReplicaStatusRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_UpdateReplicaStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) StartDeploymentMonitor(ctx context.Context, in *StartDeploymentMonitorReq, opts ...grpc.CallOption) (*StartDeploymentMonitorRsp, error) {
	out := new(StartDeploymentMonitorRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_StartDeploymentMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deploymentManagerClient) StopDeploymentMonitor(ctx context.Context, in *StopDeploymentMonitorReq, opts ...grpc.CallOption) (*StopDeploymentMonitorRsp, error) {
	out := new(StopDeploymentMonitorRsp)
	err := c.cc.Invoke(ctx, DeploymentManager_StopDeploymentMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeploymentManagerServer is the server API for DeploymentManager service.
// All implementations must embed UnimplementedDeploymentManagerServer
// for forward compatibility
type DeploymentManagerServer interface {
	ReadDeployment(context.Context, *ReadDeploymentReq) (*ReadDeploymentRsp, error)
	CreateDeployment(context.Context, *CreateDeploymentReq) (*CreateDeploymentRsp, error)
	UpdateDeployment(context.Context, *UpdateDeploymentReq) (*UpdateDeploymentRsp, error)
	DeleteDeployments(context.Context, *DeleteDeploymentsReq) (*DeleteDeploymentsRsp, error)
	StartDeployments(context.Context, *StartDeploymentsReq) (*StartDeploymentsRsp, error)
	StopDeployments(context.Context, *StopDeploymentsReq) (*StopDeploymentsRsp, error)
	WatchDeployment(*WatchDeploymentReq, DeploymentManager_WatchDeploymentServer) error
	// UpdateDeploymentState 整体更新Deployment的运行状态 TODO
	UpdateDeploymentState(context.Context, *UpdateDeploymentStateReq) (*UpdateDeploymentStateRsp, error)
	// UpdateReplicaStatus 更新每个replica的状态与指标 TODO
	UpdateReplicaStatus(context.Context, *UpdateReplicaStatusReq) (*UpdateReplicaStatusRsp, error)
	// StartDeploymentMonitor mwh-backend为部署系统提供状态更新接口, TODO 暂时作为一种过渡机制直接舍弃mqtt改动较大
	StartDeploymentMonitor(context.Context, *StartDeploymentMonitorReq) (*StartDeploymentMonitorRsp, error)
	StopDeploymentMonitor(context.Context, *StopDeploymentMonitorReq) (*StopDeploymentMonitorRsp, error)
	mustEmbedUnimplementedDeploymentManagerServer()
}

// UnimplementedDeploymentManagerServer must be embedded to have forward compatible implementations.
type UnimplementedDeploymentManagerServer struct {
}

func (UnimplementedDeploymentManagerServer) ReadDeployment(context.Context, *ReadDeploymentReq) (*ReadDeploymentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadDeployment not implemented")
}
func (UnimplementedDeploymentManagerServer) CreateDeployment(context.Context, *CreateDeploymentReq) (*CreateDeploymentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDeployment not implemented")
}
func (UnimplementedDeploymentManagerServer) UpdateDeployment(context.Context, *UpdateDeploymentReq) (*UpdateDeploymentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDeployment not implemented")
}
func (UnimplementedDeploymentManagerServer) DeleteDeployments(context.Context, *DeleteDeploymentsReq) (*DeleteDeploymentsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDeployments not implemented")
}
func (UnimplementedDeploymentManagerServer) StartDeployments(context.Context, *StartDeploymentsReq) (*StartDeploymentsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDeployments not implemented")
}
func (UnimplementedDeploymentManagerServer) StopDeployments(context.Context, *StopDeploymentsReq) (*StopDeploymentsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDeployments not implemented")
}
func (UnimplementedDeploymentManagerServer) WatchDeployment(*WatchDeploymentReq, DeploymentManager_WatchDeploymentServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchDeployment not implemented")
}
func (UnimplementedDeploymentManagerServer) UpdateDeploymentState(context.Context, *UpdateDeploymentStateReq) (*UpdateDeploymentStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDeploymentState not implemented")
}
func (UnimplementedDeploymentManagerServer) UpdateReplicaStatus(context.Context, *UpdateReplicaStatusReq) (*UpdateReplicaStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReplicaStatus not implemented")
}
func (UnimplementedDeploymentManagerServer) StartDeploymentMonitor(context.Context, *StartDeploymentMonitorReq) (*StartDeploymentMonitorRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDeploymentMonitor not implemented")
}
func (UnimplementedDeploymentManagerServer) StopDeploymentMonitor(context.Context, *StopDeploymentMonitorReq) (*StopDeploymentMonitorRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDeploymentMonitor not implemented")
}
func (UnimplementedDeploymentManagerServer) mustEmbedUnimplementedDeploymentManagerServer() {}

// UnsafeDeploymentManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeploymentManagerServer will
// result in compilation errors.
type UnsafeDeploymentManagerServer interface {
	mustEmbedUnimplementedDeploymentManagerServer()
}

func RegisterDeploymentManagerServer(s grpc.ServiceRegistrar, srv DeploymentManagerServer) {
	s.RegisterService(&DeploymentManager_ServiceDesc, srv)
}

func _DeploymentManager_ReadDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadDeploymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).ReadDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_ReadDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).ReadDeployment(ctx, req.(*ReadDeploymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_CreateDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDeploymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).CreateDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_CreateDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).CreateDeployment(ctx, req.(*CreateDeploymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_UpdateDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDeploymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).UpdateDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_UpdateDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).UpdateDeployment(ctx, req.(*UpdateDeploymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_DeleteDeployments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDeploymentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).DeleteDeployments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_DeleteDeployments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).DeleteDeployments(ctx, req.(*DeleteDeploymentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_StartDeployments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDeploymentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).StartDeployments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_StartDeployments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).StartDeployments(ctx, req.(*StartDeploymentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_StopDeployments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDeploymentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).StopDeployments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_StopDeployments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).StopDeployments(ctx, req.(*StopDeploymentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_WatchDeployment_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchDeploymentReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(DeploymentManagerServer).WatchDeployment(m, &deploymentManagerWatchDeploymentServer{stream})
}

type DeploymentManager_WatchDeploymentServer interface {
	Send(*WatchDeploymentRsp) error
	grpc.ServerStream
}

type deploymentManagerWatchDeploymentServer struct {
	grpc.ServerStream
}

func (x *deploymentManagerWatchDeploymentServer) Send(m *WatchDeploymentRsp) error {
	return x.ServerStream.SendMsg(m)
}

func _DeploymentManager_UpdateDeploymentState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDeploymentStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).UpdateDeploymentState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_UpdateDeploymentState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).UpdateDeploymentState(ctx, req.(*UpdateDeploymentStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_UpdateReplicaStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReplicaStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).UpdateReplicaStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_UpdateReplicaStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).UpdateReplicaStatus(ctx, req.(*UpdateReplicaStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_StartDeploymentMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDeploymentMonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).StartDeploymentMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_StartDeploymentMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).StartDeploymentMonitor(ctx, req.(*StartDeploymentMonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeploymentManager_StopDeploymentMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDeploymentMonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeploymentManagerServer).StopDeploymentMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeploymentManager_StopDeploymentMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeploymentManagerServer).StopDeploymentMonitor(ctx, req.(*StopDeploymentMonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DeploymentManager_ServiceDesc is the grpc.ServiceDesc for DeploymentManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeploymentManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.DeploymentManager",
	HandlerType: (*DeploymentManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadDeployment",
			Handler:    _DeploymentManager_ReadDeployment_Handler,
		},
		{
			MethodName: "CreateDeployment",
			Handler:    _DeploymentManager_CreateDeployment_Handler,
		},
		{
			MethodName: "UpdateDeployment",
			Handler:    _DeploymentManager_UpdateDeployment_Handler,
		},
		{
			MethodName: "DeleteDeployments",
			Handler:    _DeploymentManager_DeleteDeployments_Handler,
		},
		{
			MethodName: "StartDeployments",
			Handler:    _DeploymentManager_StartDeployments_Handler,
		},
		{
			MethodName: "StopDeployments",
			Handler:    _DeploymentManager_StopDeployments_Handler,
		},
		{
			MethodName: "UpdateDeploymentState",
			Handler:    _DeploymentManager_UpdateDeploymentState_Handler,
		},
		{
			MethodName: "UpdateReplicaStatus",
			Handler:    _DeploymentManager_UpdateReplicaStatus_Handler,
		},
		{
			MethodName: "StartDeploymentMonitor",
			Handler:    _DeploymentManager_StartDeploymentMonitor_Handler,
		},
		{
			MethodName: "StopDeploymentMonitor",
			Handler:    _DeploymentManager_StopDeploymentMonitor_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WatchDeployment",
			Handler:       _DeploymentManager_WatchDeployment_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/rpc_model_deployment.proto",
}
