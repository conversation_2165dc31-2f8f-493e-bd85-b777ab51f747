// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_attachment_manager.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateAttachmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId    string       `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId  string       `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	Attachment *Attachment  `protobuf:"bytes,3,opt,name=attachment,proto3" json:"attachment,omitempty"`
	Ctx        *UserContext `protobuf:"bytes,4,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *CreateAttachmentReq) Reset() {
	*x = CreateAttachmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAttachmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAttachmentReq) ProtoMessage() {}

func (x *CreateAttachmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAttachmentReq.ProtoReflect.Descriptor instead.
func (*CreateAttachmentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAttachmentReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CreateAttachmentReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *CreateAttachmentReq) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *CreateAttachmentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type CreateAttachmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attachment *Attachment `protobuf:"bytes,1,opt,name=attachment,proto3" json:"attachment,omitempty"`
}

func (x *CreateAttachmentRsp) Reset() {
	*x = CreateAttachmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAttachmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAttachmentRsp) ProtoMessage() {}

func (x *CreateAttachmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAttachmentRsp.ProtoReflect.Descriptor instead.
func (*CreateAttachmentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAttachmentRsp) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

type DeleteAttachmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string       `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId    string       `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	AttachmentId string       `protobuf:"bytes,3,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	Ctx          *UserContext `protobuf:"bytes,4,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *DeleteAttachmentReq) Reset() {
	*x = DeleteAttachmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAttachmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAttachmentReq) ProtoMessage() {}

func (x *DeleteAttachmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAttachmentReq.ProtoReflect.Descriptor instead.
func (*DeleteAttachmentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteAttachmentReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *DeleteAttachmentReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *DeleteAttachmentReq) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

func (x *DeleteAttachmentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type DeleteAttachmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteAttachmentRsp) Reset() {
	*x = DeleteAttachmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAttachmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAttachmentRsp) ProtoMessage() {}

func (x *DeleteAttachmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAttachmentRsp.ProtoReflect.Descriptor instead.
func (*DeleteAttachmentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{3}
}

type UpdateAttachmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string       `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId    string       `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	AttachmentId string       `protobuf:"bytes,3,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	Attachment   *Attachment  `protobuf:"bytes,4,opt,name=attachment,proto3" json:"attachment,omitempty"`
	Ctx          *UserContext `protobuf:"bytes,5,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *UpdateAttachmentReq) Reset() {
	*x = UpdateAttachmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAttachmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAttachmentReq) ProtoMessage() {}

func (x *UpdateAttachmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAttachmentReq.ProtoReflect.Descriptor instead.
func (*UpdateAttachmentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAttachmentReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *UpdateAttachmentReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *UpdateAttachmentReq) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

func (x *UpdateAttachmentReq) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *UpdateAttachmentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type UpdateAttachmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attachment *Attachment `protobuf:"bytes,1,opt,name=attachment,proto3" json:"attachment,omitempty"`
}

func (x *UpdateAttachmentRsp) Reset() {
	*x = UpdateAttachmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAttachmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAttachmentRsp) ProtoMessage() {}

func (x *UpdateAttachmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAttachmentRsp.ProtoReflect.Descriptor instead.
func (*UpdateAttachmentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateAttachmentRsp) GetAttachment() *Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

type GetAttachmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PageReq page_req = 2;
	// AttachmentFilter filter = 3;
	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *GetAttachmentReq) Reset() {
	*x = GetAttachmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttachmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachmentReq) ProtoMessage() {}

func (x *GetAttachmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachmentReq.ProtoReflect.Descriptor instead.
func (*GetAttachmentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{6}
}

func (x *GetAttachmentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type GetAttachmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total       int32         `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	CurrentPage int32         `protobuf:"varint,2,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize    int32         `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Attachments []*Attachment `protobuf:"bytes,4,rep,name=attachments,proto3" json:"attachments,omitempty"`
}

func (x *GetAttachmentRsp) Reset() {
	*x = GetAttachmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_attachment_manager_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttachmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachmentRsp) ProtoMessage() {}

func (x *GetAttachmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_attachment_manager_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachmentRsp.ProtoReflect.Descriptor instead.
func (*GetAttachmentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_attachment_manager_proto_rawDescGZIP(), []int{7}
}

func (x *GetAttachmentRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAttachmentRsp) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *GetAttachmentRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAttachmentRsp) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

var File_proto_rpc_attachment_manager_proto protoreflect.FileDescriptor

var file_proto_rpc_attachment_manager_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa8, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x48, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0xcd, 0x01, 0x0a, 0x13,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x48, 0x0a, 0x13, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x38, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22,
	0x9d, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x32,
	0xba, 0x02, 0x0a, 0x11, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x4a, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_attachment_manager_proto_rawDescOnce sync.Once
	file_proto_rpc_attachment_manager_proto_rawDescData = file_proto_rpc_attachment_manager_proto_rawDesc
)

func file_proto_rpc_attachment_manager_proto_rawDescGZIP() []byte {
	file_proto_rpc_attachment_manager_proto_rawDescOnce.Do(func() {
		file_proto_rpc_attachment_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_attachment_manager_proto_rawDescData)
	})
	return file_proto_rpc_attachment_manager_proto_rawDescData
}

var file_proto_rpc_attachment_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_rpc_attachment_manager_proto_goTypes = []interface{}{
	(*CreateAttachmentReq)(nil), // 0: proto.CreateAttachmentReq
	(*CreateAttachmentRsp)(nil), // 1: proto.CreateAttachmentRsp
	(*DeleteAttachmentReq)(nil), // 2: proto.DeleteAttachmentReq
	(*DeleteAttachmentRsp)(nil), // 3: proto.DeleteAttachmentRsp
	(*UpdateAttachmentReq)(nil), // 4: proto.UpdateAttachmentReq
	(*UpdateAttachmentRsp)(nil), // 5: proto.UpdateAttachmentRsp
	(*GetAttachmentReq)(nil),    // 6: proto.GetAttachmentReq
	(*GetAttachmentRsp)(nil),    // 7: proto.GetAttachmentRsp
	(*Attachment)(nil),          // 8: proto.Attachment
	(*UserContext)(nil),         // 9: proto.UserContext
}
var file_proto_rpc_attachment_manager_proto_depIdxs = []int32{
	8,  // 0: proto.CreateAttachmentReq.attachment:type_name -> proto.Attachment
	9,  // 1: proto.CreateAttachmentReq.ctx:type_name -> proto.UserContext
	8,  // 2: proto.CreateAttachmentRsp.attachment:type_name -> proto.Attachment
	9,  // 3: proto.DeleteAttachmentReq.ctx:type_name -> proto.UserContext
	8,  // 4: proto.UpdateAttachmentReq.attachment:type_name -> proto.Attachment
	9,  // 5: proto.UpdateAttachmentReq.ctx:type_name -> proto.UserContext
	8,  // 6: proto.UpdateAttachmentRsp.attachment:type_name -> proto.Attachment
	9,  // 7: proto.GetAttachmentReq.ctx:type_name -> proto.UserContext
	8,  // 8: proto.GetAttachmentRsp.attachments:type_name -> proto.Attachment
	6,  // 9: proto.AttachmentManager.GetAttachment:input_type -> proto.GetAttachmentReq
	0,  // 10: proto.AttachmentManager.CreateAttachment:input_type -> proto.CreateAttachmentReq
	2,  // 11: proto.AttachmentManager.DeleteAttachment:input_type -> proto.DeleteAttachmentReq
	4,  // 12: proto.AttachmentManager.UpdateAttachment:input_type -> proto.UpdateAttachmentReq
	7,  // 13: proto.AttachmentManager.GetAttachment:output_type -> proto.GetAttachmentRsp
	1,  // 14: proto.AttachmentManager.CreateAttachment:output_type -> proto.CreateAttachmentRsp
	3,  // 15: proto.AttachmentManager.DeleteAttachment:output_type -> proto.DeleteAttachmentRsp
	5,  // 16: proto.AttachmentManager.UpdateAttachment:output_type -> proto.UpdateAttachmentRsp
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_proto_rpc_attachment_manager_proto_init() }
func file_proto_rpc_attachment_manager_proto_init() {
	if File_proto_rpc_attachment_manager_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_attachment_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAttachmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAttachmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAttachmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAttachmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAttachmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAttachmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttachmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_attachment_manager_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttachmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_attachment_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_attachment_manager_proto_goTypes,
		DependencyIndexes: file_proto_rpc_attachment_manager_proto_depIdxs,
		MessageInfos:      file_proto_rpc_attachment_manager_proto_msgTypes,
	}.Build()
	File_proto_rpc_attachment_manager_proto = out.File
	file_proto_rpc_attachment_manager_proto_rawDesc = nil
	file_proto_rpc_attachment_manager_proto_goTypes = nil
	file_proto_rpc_attachment_manager_proto_depIdxs = nil
}
