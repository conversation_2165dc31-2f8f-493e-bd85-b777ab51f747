// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_asset.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Module int32

const (
	Module_mw        Module = 0
	Module_sample    Module = 1
	Module_mlops     Module = 2
	Module_knowledge Module = 3
)

// Enum value maps for Module.
var (
	Module_name = map[int32]string{
		0: "mw",
		1: "sample",
		2: "mlops",
		3: "knowledge",
	}
	Module_value = map[string]int32{
		"mw":        0,
		"sample":    1,
		"mlops":     2,
		"knowledge": 3,
	}
)

func (x Module) Enum() *Module {
	p := new(Module)
	*p = x
	return p
}

func (x Module) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Module) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_asset_proto_enumTypes[0].Descriptor()
}

func (Module) Type() protoreflect.EnumType {
	return &file_proto_rpc_asset_proto_enumTypes[0]
}

func (x Module) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Module.Descriptor instead.
func (Module) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{0}
}

type RscType int32

const (
	RscType_MODEL          RscType = 0
	RscType_CHAIN          RscType = 1
	RscType_DATASET        RscType = 2
	RscType_KNOWLEDGE_BASE RscType = 3
)

// Enum value maps for RscType.
var (
	RscType_name = map[int32]string{
		0: "MODEL",
		1: "CHAIN",
		2: "DATASET",
		3: "KNOWLEDGE_BASE",
	}
	RscType_value = map[string]int32{
		"MODEL":          0,
		"CHAIN":          1,
		"DATASET":        2,
		"KNOWLEDGE_BASE": 3,
	}
)

func (x RscType) Enum() *RscType {
	p := new(RscType)
	*p = x
	return p
}

func (x RscType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RscType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_asset_proto_enumTypes[1].Descriptor()
}

func (RscType) Type() protoreflect.EnumType {
	return &file_proto_rpc_asset_proto_enumTypes[1]
}

func (x RscType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RscType.Descriptor instead.
func (RscType) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{1}
}

type AssetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Key       string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *AssetReq) Reset() {
	*x = AssetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_asset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetReq) ProtoMessage() {}

func (x *AssetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_asset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetReq.ProtoReflect.Descriptor instead.
func (*AssetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{0}
}

func (x *AssetReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AssetReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type AssetResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key    string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Groups []*Group `protobuf:"bytes,2,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *AssetResp) Reset() {
	*x = AssetResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_asset_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetResp) ProtoMessage() {}

func (x *AssetResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_asset_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetResp.ProtoReflect.Descriptor instead.
func (*AssetResp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{1}
}

func (x *AssetResp) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AssetResp) GetGroups() []*Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

type Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Module  Module  `protobuf:"varint,1,opt,name=module,proto3,enum=proto.Module" json:"module,omitempty"`
	RscType RscType `protobuf:"varint,2,opt,name=rsc_type,json=rscType,proto3,enum=proto.RscType" json:"rsc_type,omitempty"`
	Items   []*Item `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Group) Reset() {
	*x = Group{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_asset_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Group) ProtoMessage() {}

func (x *Group) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_asset_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Group.ProtoReflect.Descriptor instead.
func (*Group) Descriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{2}
}

func (x *Group) GetModule() Module {
	if x != nil {
		return x.Module
	}
	return Module_mw
}

func (x *Group) GetRscType() RscType {
	if x != nil {
		return x.RscType
	}
	return RscType_MODEL
}

func (x *Group) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc         string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Labels       map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ProjectId    string            `protobuf:"bytes,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Module       Module            `protobuf:"varint,6,opt,name=module,proto3,enum=proto.Module" json:"module,omitempty"`
	RscType      RscType           `protobuf:"varint,7,opt,name=rsc_type,json=rscType,proto3,enum=proto.RscType" json:"rsc_type,omitempty"`
	Creator      string            `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	UpdateTime   int64             `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ReleaseCount int64             `protobuf:"varint,10,opt,name=release_count,json=releaseCount,proto3" json:"release_count,omitempty"`
	AssetType    string            `protobuf:"bytes,11,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	CreateType   string            `protobuf:"bytes,12,opt,name=create_type,json=createType,proto3" json:"create_type,omitempty"`
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_asset_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_asset_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_proto_rpc_asset_proto_rawDescGZIP(), []int{3}
}

func (x *Item) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Item) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Item) GetModule() Module {
	if x != nil {
		return x.Module
	}
	return Module_mw
}

func (x *Item) GetRscType() RscType {
	if x != nil {
		return x.RscType
	}
	return RscType_MODEL
}

func (x *Item) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Item) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Item) GetReleaseCount() int64 {
	if x != nil {
		return x.ReleaseCount
	}
	return 0
}

func (x *Item) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *Item) GetCreateType() string {
	if x != nil {
		return x.CreateType
	}
	return ""
}

var File_proto_rpc_asset_proto protoreflect.FileDescriptor

var file_proto_rpc_asset_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3b,
	0x0a, 0x08, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x43, 0x0a, 0x09, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x06, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x22, 0x7c, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x0a, 0x06, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x29, 0x0a, 0x08, 0x72, 0x73, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x73, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x72, 0x73, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xbb,
	0x03, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x08, 0x72, 0x73, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x73, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x72, 0x73, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x36, 0x0a, 0x06,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x06, 0x0a, 0x02, 0x6d, 0x77, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x6d, 0x6c,
	0x6f, 0x70, 0x73, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x10, 0x03, 0x2a, 0x40, 0x0a, 0x07, 0x52, 0x73, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x48,
	0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x41, 0x54, 0x41, 0x53, 0x45, 0x54,
	0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f,
	0x42, 0x41, 0x53, 0x45, 0x10, 0x03, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77,
	0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70,
	0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_asset_proto_rawDescOnce sync.Once
	file_proto_rpc_asset_proto_rawDescData = file_proto_rpc_asset_proto_rawDesc
)

func file_proto_rpc_asset_proto_rawDescGZIP() []byte {
	file_proto_rpc_asset_proto_rawDescOnce.Do(func() {
		file_proto_rpc_asset_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_asset_proto_rawDescData)
	})
	return file_proto_rpc_asset_proto_rawDescData
}

var file_proto_rpc_asset_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_rpc_asset_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_rpc_asset_proto_goTypes = []interface{}{
	(Module)(0),       // 0: proto.Module
	(RscType)(0),      // 1: proto.RscType
	(*AssetReq)(nil),  // 2: proto.AssetReq
	(*AssetResp)(nil), // 3: proto.AssetResp
	(*Group)(nil),     // 4: proto.Group
	(*Item)(nil),      // 5: proto.Item
	nil,               // 6: proto.Item.LabelsEntry
}
var file_proto_rpc_asset_proto_depIdxs = []int32{
	4, // 0: proto.AssetResp.groups:type_name -> proto.Group
	0, // 1: proto.Group.module:type_name -> proto.Module
	1, // 2: proto.Group.rsc_type:type_name -> proto.RscType
	5, // 3: proto.Group.items:type_name -> proto.Item
	6, // 4: proto.Item.labels:type_name -> proto.Item.LabelsEntry
	0, // 5: proto.Item.module:type_name -> proto.Module
	1, // 6: proto.Item.rsc_type:type_name -> proto.RscType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_proto_rpc_asset_proto_init() }
func file_proto_rpc_asset_proto_init() {
	if File_proto_rpc_asset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_asset_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_asset_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_asset_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Group); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_asset_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_asset_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_rpc_asset_proto_goTypes,
		DependencyIndexes: file_proto_rpc_asset_proto_depIdxs,
		EnumInfos:         file_proto_rpc_asset_proto_enumTypes,
		MessageInfos:      file_proto_rpc_asset_proto_msgTypes,
	}.Build()
	File_proto_rpc_asset_proto = out.File
	file_proto_rpc_asset_proto_rawDesc = nil
	file_proto_rpc_asset_proto_goTypes = nil
	file_proto_rpc_asset_proto_depIdxs = nil
}
