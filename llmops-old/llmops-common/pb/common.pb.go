// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/common.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SortBy int32

const (
	SortBy_Sort_By_UNSPECIFIED    SortBy = 0
	SortBy_Sort_By_Visit_Times    SortBy = 1
	SortBy_Sort_By_Clone_Times    SortBy = 2
	SortBy_Sort_By_Execute_Times  SortBy = 3
	SortBy_Sort_By_Download_Times SortBy = 4
)

// Enum value maps for SortBy.
var (
	SortBy_name = map[int32]string{
		0: "Sort_By_UNSPECIFIED",
		1: "Sort_By_Visit_Times",
		2: "Sort_By_Clone_Times",
		3: "Sort_By_Execute_Times",
		4: "Sort_By_Download_Times",
	}
	SortBy_value = map[string]int32{
		"Sort_By_UNSPECIFIED":    0,
		"Sort_By_Visit_Times":    1,
		"Sort_By_Clone_Times":    2,
		"Sort_By_Execute_Times":  3,
		"Sort_By_Download_Times": 4,
	}
)

func (x SortBy) Enum() *SortBy {
	p := new(SortBy)
	*p = x
	return p
}

func (x SortBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortBy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_proto_enumTypes[0].Descriptor()
}

func (SortBy) Type() protoreflect.EnumType {
	return &file_proto_common_proto_enumTypes[0]
}

func (x SortBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortBy.Descriptor instead.
func (SortBy) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{0}
}

type UserRole int32

const (
	UserRole_USER_ROLE_UNSPECIFIED UserRole = 0
	UserRole_USER_ROLE_ADMIN       UserRole = 1
	UserRole_USER_ROLE_BASIC       UserRole = 2
)

// Enum value maps for UserRole.
var (
	UserRole_name = map[int32]string{
		0: "USER_ROLE_UNSPECIFIED",
		1: "USER_ROLE_ADMIN",
		2: "USER_ROLE_BASIC",
	}
	UserRole_value = map[string]int32{
		"USER_ROLE_UNSPECIFIED": 0,
		"USER_ROLE_ADMIN":       1,
		"USER_ROLE_BASIC":       2,
	}
)

func (x UserRole) Enum() *UserRole {
	p := new(UserRole)
	*p = x
	return p
}

func (x UserRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserRole) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_proto_enumTypes[1].Descriptor()
}

func (UserRole) Type() protoreflect.EnumType {
	return &file_proto_common_proto_enumTypes[1]
}

func (x UserRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserRole.Descriptor instead.
func (UserRole) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{1}
}

// PageReq 资源查询时的一些分页相关请求
// TODO 支持 sort_by & is desc
type PageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SortBy   string `protobuf:"bytes,3,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	IsDesc   bool   `protobuf:"varint,4,opt,name=is_desc,json=isDesc,proto3" json:"is_desc,omitempty"`
}

func (x *PageReq) Reset() {
	*x = PageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageReq) ProtoMessage() {}

func (x *PageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageReq.ProtoReflect.Descriptor instead.
func (*PageReq) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{0}
}

func (x *PageReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageReq) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *PageReq) GetIsDesc() bool {
	if x != nil {
		return x.IsDesc
	}
	return false
}

// UserContext 用于grpc之间传输请求调用的用户相关上下文,即发起该请求的用户信息
type UserContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string     `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName  string     `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Roles     []UserRole `protobuf:"varint,3,rep,packed,name=roles,proto3,enum=proto.UserRole" json:"roles,omitempty"`
	ProjectId string     `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Token     string     `protobuf:"bytes,5,opt,name=token,proto3" json:"token,omitempty"`
	TenantId  string     `protobuf:"bytes,6,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *UserContext) Reset() {
	*x = UserContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContext) ProtoMessage() {}

func (x *UserContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContext.ProtoReflect.Descriptor instead.
func (*UserContext) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{1}
}

func (x *UserContext) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserContext) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserContext) GetRoles() []UserRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *UserContext) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UserContext) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserContext) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

type Enum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                            // 枚举型的值
	Name     string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                         // 枚举型的名称
	SubEnums []*Enum `protobuf:"bytes,3,rep,name=sub_enums,json=subEnums,proto3" json:"sub_enums,omitempty"` // 子枚举类型
}

func (x *Enum) Reset() {
	*x = Enum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enum) ProtoMessage() {}

func (x *Enum) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enum.ProtoReflect.Descriptor instead.
func (*Enum) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{2}
}

func (x *Enum) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Enum) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Enum) GetSubEnums() []*Enum {
	if x != nil {
		return x.SubEnums
	}
	return nil
}

type Enums struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enums []*Enum `protobuf:"bytes,1,rep,name=enums,proto3" json:"enums,omitempty"` // 一组枚举类型的定义，主要用于获取枚举型范围
}

func (x *Enums) Reset() {
	*x = Enums{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enums) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enums) ProtoMessage() {}

func (x *Enums) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enums.ProtoReflect.Descriptor instead.
func (*Enums) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{3}
}

func (x *Enums) GetEnums() []*Enum {
	if x != nil {
		return x.Enums
	}
	return nil
}

type LabelSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *LabelSelector) Reset() {
	*x = LabelSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelSelector) ProtoMessage() {}

func (x *LabelSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelSelector.ProtoReflect.Descriptor instead.
func (*LabelSelector) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{4}
}

func (x *LabelSelector) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type DomainSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind     ModelKind      `protobuf:"varint,1,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKinds []ModelSubKind `protobuf:"varint,2,rep,packed,name=sub_kinds,json=subKinds,proto3,enum=proto.ModelSubKind" json:"sub_kinds,omitempty"`
	Types    []ModelType    `protobuf:"varint,3,rep,packed,name=types,proto3,enum=proto.ModelType" json:"types,omitempty"`
	SubTypes []ModelSubType `protobuf:"varint,4,rep,packed,name=sub_types,json=subTypes,proto3,enum=proto.ModelSubType" json:"sub_types,omitempty"`
}

func (x *DomainSelector) Reset() {
	*x = DomainSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DomainSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainSelector) ProtoMessage() {}

func (x *DomainSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainSelector.ProtoReflect.Descriptor instead.
func (*DomainSelector) Descriptor() ([]byte, []int) {
	return file_proto_common_proto_rawDescGZIP(), []int{5}
}

func (x *DomainSelector) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *DomainSelector) GetSubKinds() []ModelSubKind {
	if x != nil {
		return x.SubKinds
	}
	return nil
}

func (x *DomainSelector) GetTypes() []ModelType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *DomainSelector) GetSubTypes() []ModelSubType {
	if x != nil {
		return x.SubTypes
	}
	return nil
}

var File_proto_common_proto protoreflect.FileDescriptor

var file_proto_common_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6c,
	0x0a, 0x07, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6f,
	0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x72,
	0x74, 0x42, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x22, 0xbc, 0x01, 0x0a,
	0x0b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x73, 0x75, 0x62, 0x45, 0x6e, 0x75, 0x6d,
	0x73, 0x22, 0x2a, 0x0a, 0x05, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x22, 0x84, 0x01,
	0x0a, 0x0d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x38, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xc2, 0x01, 0x0a, 0x0e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x30, 0x0a,
	0x09, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75,
	0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x08, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x12,
	0x26, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2a, 0x8a, 0x01, 0x0a, 0x06, 0x53, 0x6f,
	0x72, 0x74, 0x42, 0x79, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x42, 0x79, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x42, 0x79, 0x5f, 0x56, 0x69, 0x73, 0x69, 0x74, 0x5f, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x42,
	0x79, 0x5f, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x42, 0x79, 0x5f, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x6f,
	0x72, 0x74, 0x5f, 0x42, 0x79, 0x5f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x10, 0x04, 0x2a, 0x4f, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f,
	0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x02, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f,
	0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_common_proto_rawDescOnce sync.Once
	file_proto_common_proto_rawDescData = file_proto_common_proto_rawDesc
)

func file_proto_common_proto_rawDescGZIP() []byte {
	file_proto_common_proto_rawDescOnce.Do(func() {
		file_proto_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_common_proto_rawDescData)
	})
	return file_proto_common_proto_rawDescData
}

var file_proto_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_common_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proto_common_proto_goTypes = []interface{}{
	(SortBy)(0),            // 0: proto.SortBy
	(UserRole)(0),          // 1: proto.UserRole
	(*PageReq)(nil),        // 2: proto.PageReq
	(*UserContext)(nil),    // 3: proto.UserContext
	(*Enum)(nil),           // 4: proto.Enum
	(*Enums)(nil),          // 5: proto.Enums
	(*LabelSelector)(nil),  // 6: proto.LabelSelector
	(*DomainSelector)(nil), // 7: proto.DomainSelector
	nil,                    // 8: proto.LabelSelector.LabelsEntry
	(ModelKind)(0),         // 9: proto.ModelKind
	(ModelSubKind)(0),      // 10: proto.ModelSubKind
	(ModelType)(0),         // 11: proto.ModelType
	(ModelSubType)(0),      // 12: proto.ModelSubType
}
var file_proto_common_proto_depIdxs = []int32{
	1,  // 0: proto.UserContext.roles:type_name -> proto.UserRole
	4,  // 1: proto.Enum.sub_enums:type_name -> proto.Enum
	4,  // 2: proto.Enums.enums:type_name -> proto.Enum
	8,  // 3: proto.LabelSelector.labels:type_name -> proto.LabelSelector.LabelsEntry
	9,  // 4: proto.DomainSelector.kind:type_name -> proto.ModelKind
	10, // 5: proto.DomainSelector.sub_kinds:type_name -> proto.ModelSubKind
	11, // 6: proto.DomainSelector.types:type_name -> proto.ModelType
	12, // 7: proto.DomainSelector.sub_types:type_name -> proto.ModelSubType
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_proto_common_proto_init() }
func file_proto_common_proto_init() {
	if File_proto_common_proto != nil {
		return
	}
	file_proto_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enums); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DomainSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_common_proto_goTypes,
		DependencyIndexes: file_proto_common_proto_depIdxs,
		EnumInfos:         file_proto_common_proto_enumTypes,
		MessageInfos:      file_proto_common_proto_msgTypes,
	}.Build()
	File_proto_common_proto = out.File
	file_proto_common_proto_rawDesc = nil
	file_proto_common_proto_goTypes = nil
	file_proto_common_proto_depIdxs = nil
}
