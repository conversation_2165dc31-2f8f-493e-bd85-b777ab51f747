// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_chat_mgr.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadConversationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx          *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id           string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	DeploymentId string       `protobuf:"bytes,3,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"`
}

func (x *ReadConversationsReq) Reset() {
	*x = ReadConversationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadConversationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadConversationsReq) ProtoMessage() {}

func (x *ReadConversationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadConversationsReq.ProtoReflect.Descriptor instead.
func (*ReadConversationsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{0}
}

func (x *ReadConversationsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadConversationsReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadConversationsReq) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

type ReadConversationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Conversations []*Conversation `protobuf:"bytes,1,rep,name=conversations,proto3" json:"conversations,omitempty"`
}

func (x *ReadConversationsRsp) Reset() {
	*x = ReadConversationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadConversationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadConversationsRsp) ProtoMessage() {}

func (x *ReadConversationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadConversationsRsp.ProtoReflect.Descriptor instead.
func (*ReadConversationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{1}
}

func (x *ReadConversationsRsp) GetConversations() []*Conversation {
	if x != nil {
		return x.Conversations
	}
	return nil
}

type DeleteConversationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx          *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id           string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	DeploymentId string       `protobuf:"bytes,3,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"`
}

func (x *DeleteConversationsReq) Reset() {
	*x = DeleteConversationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConversationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConversationsReq) ProtoMessage() {}

func (x *DeleteConversationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConversationsReq.ProtoReflect.Descriptor instead.
func (*DeleteConversationsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteConversationsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteConversationsReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteConversationsReq) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

type DeleteConversationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteConversationsRsp) Reset() {
	*x = DeleteConversationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConversationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConversationsRsp) ProtoMessage() {}

func (x *DeleteConversationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConversationsRsp.ProtoReflect.Descriptor instead.
func (*DeleteConversationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{3}
}

type RatingConversationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rating QARating `protobuf:"varint,1,opt,name=rating,proto3,enum=proto.QARating" json:"rating,omitempty"`
}

func (x *RatingConversationReq) Reset() {
	*x = RatingConversationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingConversationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingConversationReq) ProtoMessage() {}

func (x *RatingConversationReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingConversationReq.ProtoReflect.Descriptor instead.
func (*RatingConversationReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{4}
}

func (x *RatingConversationReq) GetRating() QARating {
	if x != nil {
		return x.Rating
	}
	return QARating_QA_RATING_UNSPECIFIC
}

type RatingConversationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RatingConversationRsp) Reset() {
	*x = RatingConversationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_chat_mgr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingConversationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingConversationRsp) ProtoMessage() {}

func (x *RatingConversationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_chat_mgr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingConversationRsp.ProtoReflect.Descriptor instead.
func (*RatingConversationRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_chat_mgr_proto_rawDescGZIP(), []int{5}
}

var File_proto_rpc_chat_mgr_proto protoreflect.FileDescriptor

var file_proto_rpc_chat_mgr_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x6d, 0x67, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x71, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x14, 0x52, 0x65,
	0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x39, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x73, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x40, 0x0a, 0x15,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x41,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x17,
	0x0a, 0x15, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x32, 0x83, 0x02, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x11, 0x52, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x52,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a,
	0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69,
	0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_chat_mgr_proto_rawDescOnce sync.Once
	file_proto_rpc_chat_mgr_proto_rawDescData = file_proto_rpc_chat_mgr_proto_rawDesc
)

func file_proto_rpc_chat_mgr_proto_rawDescGZIP() []byte {
	file_proto_rpc_chat_mgr_proto_rawDescOnce.Do(func() {
		file_proto_rpc_chat_mgr_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_chat_mgr_proto_rawDescData)
	})
	return file_proto_rpc_chat_mgr_proto_rawDescData
}

var file_proto_rpc_chat_mgr_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_rpc_chat_mgr_proto_goTypes = []interface{}{
	(*ReadConversationsReq)(nil),   // 0: proto.ReadConversationsReq
	(*ReadConversationsRsp)(nil),   // 1: proto.ReadConversationsRsp
	(*DeleteConversationsReq)(nil), // 2: proto.DeleteConversationsReq
	(*DeleteConversationsRsp)(nil), // 3: proto.DeleteConversationsRsp
	(*RatingConversationReq)(nil),  // 4: proto.RatingConversationReq
	(*RatingConversationRsp)(nil),  // 5: proto.RatingConversationRsp
	(*UserContext)(nil),            // 6: proto.UserContext
	(*Conversation)(nil),           // 7: proto.Conversation
	(QARating)(0),                  // 8: proto.QARating
}
var file_proto_rpc_chat_mgr_proto_depIdxs = []int32{
	6, // 0: proto.ReadConversationsReq.ctx:type_name -> proto.UserContext
	7, // 1: proto.ReadConversationsRsp.conversations:type_name -> proto.Conversation
	6, // 2: proto.DeleteConversationsReq.ctx:type_name -> proto.UserContext
	8, // 3: proto.RatingConversationReq.rating:type_name -> proto.QARating
	0, // 4: proto.ChatManager.ReadConversations:input_type -> proto.ReadConversationsReq
	2, // 5: proto.ChatManager.DeleteConversations:input_type -> proto.DeleteConversationsReq
	4, // 6: proto.ChatManager.RatingConversation:input_type -> proto.RatingConversationReq
	1, // 7: proto.ChatManager.ReadConversations:output_type -> proto.ReadConversationsRsp
	3, // 8: proto.ChatManager.DeleteConversations:output_type -> proto.DeleteConversationsRsp
	5, // 9: proto.ChatManager.RatingConversation:output_type -> proto.RatingConversationRsp
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_rpc_chat_mgr_proto_init() }
func file_proto_rpc_chat_mgr_proto_init() {
	if File_proto_rpc_chat_mgr_proto != nil {
		return
	}
	file_proto_common_proto_init()
	file_proto_chat_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_chat_mgr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadConversationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_chat_mgr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadConversationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_chat_mgr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConversationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_chat_mgr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConversationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_chat_mgr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingConversationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_chat_mgr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingConversationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_chat_mgr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_chat_mgr_proto_goTypes,
		DependencyIndexes: file_proto_rpc_chat_mgr_proto_depIdxs,
		MessageInfos:      file_proto_rpc_chat_mgr_proto_msgTypes,
	}.Build()
	File_proto_rpc_chat_mgr_proto = out.File
	file_proto_rpc_chat_mgr_proto_rawDesc = nil
	file_proto_rpc_chat_mgr_proto_goTypes = nil
	file_proto_rpc_chat_mgr_proto_depIdxs = nil
}
