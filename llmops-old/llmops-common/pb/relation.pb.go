// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/relation.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RelationType 定义了模型/模型版本/其它资源之间的关联类型
// 除默认值外，考虑成对的关联关系 e.g [ is children & is parent | is inherit from vs is inherited by ]
type RelationType int32

const (
	RelationType_RELATION_TYPE_UNSPECIFIED        RelationType = 0  // 未指定具体关联类型，仅表明存在关联，默认值
	RelationType_RELATION_TYPE_IS_CHILDREN_OF     RelationType = 1  // 是 ... 的组成部分
	RelationType_RELATION_TYPE_IS_PARENT_OF       RelationType = 2  // 由 ... 组成
	RelationType_RELATION_TYPE_INHERITS           RelationType = 3  // 继承自...
	RelationType_RELATION_TYPE_IS_INHERITED_BY    RelationType = 4  // 被...继承
	RelationType_RELATION_TYPE_FORKS              RelationType = 5  // 分叉自...
	RelationType_RELATION_TYPE_IS_FORKED_BY       RelationType = 6  // 分叉出的分支
	RelationType_RELATION_TYPE_BASE_MODEL_OF      RelationType = 7  // 为...的基础大模型
	RelationType_RELATION_TYPE_FINE_TUNED_FROM    RelationType = 8  // 微调模型由...得到
	RelationType_RELATION_TYPE_IS_TRAINED_ON      RelationType = 9  // 由...数据集训练
	RelationType_RELATION_TYPE_TRAINING_SET_FOR   RelationType = 10 // 用于训练...
	RelationType_RELATION_TYPE_IS_EVALUATED_ON    RelationType = 11 // 由...数据集评估
	RelationType_RELATION_TYPE_EVALUATION_SET_FOR RelationType = 12 // 用于评估...
	RelationType_RELATION_TYPE_QUANT_FROM         RelationType = 13 // 由...量化而来
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0:  "RELATION_TYPE_UNSPECIFIED",
		1:  "RELATION_TYPE_IS_CHILDREN_OF",
		2:  "RELATION_TYPE_IS_PARENT_OF",
		3:  "RELATION_TYPE_INHERITS",
		4:  "RELATION_TYPE_IS_INHERITED_BY",
		5:  "RELATION_TYPE_FORKS",
		6:  "RELATION_TYPE_IS_FORKED_BY",
		7:  "RELATION_TYPE_BASE_MODEL_OF",
		8:  "RELATION_TYPE_FINE_TUNED_FROM",
		9:  "RELATION_TYPE_IS_TRAINED_ON",
		10: "RELATION_TYPE_TRAINING_SET_FOR",
		11: "RELATION_TYPE_IS_EVALUATED_ON",
		12: "RELATION_TYPE_EVALUATION_SET_FOR",
		13: "RELATION_TYPE_QUANT_FROM",
	}
	RelationType_value = map[string]int32{
		"RELATION_TYPE_UNSPECIFIED":        0,
		"RELATION_TYPE_IS_CHILDREN_OF":     1,
		"RELATION_TYPE_IS_PARENT_OF":       2,
		"RELATION_TYPE_INHERITS":           3,
		"RELATION_TYPE_IS_INHERITED_BY":    4,
		"RELATION_TYPE_FORKS":              5,
		"RELATION_TYPE_IS_FORKED_BY":       6,
		"RELATION_TYPE_BASE_MODEL_OF":      7,
		"RELATION_TYPE_FINE_TUNED_FROM":    8,
		"RELATION_TYPE_IS_TRAINED_ON":      9,
		"RELATION_TYPE_TRAINING_SET_FOR":   10,
		"RELATION_TYPE_IS_EVALUATED_ON":    11,
		"RELATION_TYPE_EVALUATION_SET_FOR": 12,
		"RELATION_TYPE_QUANT_FROM":         13,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_relation_proto_enumTypes[0].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_proto_relation_proto_enumTypes[0]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{0}
}

// PeerType 关系的对端资源类型
type PeerType int32

const (
	PeerType_PEER_TYPE_UNSPECIFIED   PeerType = 0 // 未知对端类型，默认值
	PeerType_PEER_TYPE_MODEL         PeerType = 1 // 模型
	PeerType_PEER_TYPE_MODEL_RELEASE PeerType = 2 // 模型版本
	PeerType_PEER_TYPE_TRAINING_SET  PeerType = 3 // 模型训练集
)

// Enum value maps for PeerType.
var (
	PeerType_name = map[int32]string{
		0: "PEER_TYPE_UNSPECIFIED",
		1: "PEER_TYPE_MODEL",
		2: "PEER_TYPE_MODEL_RELEASE",
		3: "PEER_TYPE_TRAINING_SET",
	}
	PeerType_value = map[string]int32{
		"PEER_TYPE_UNSPECIFIED":   0,
		"PEER_TYPE_MODEL":         1,
		"PEER_TYPE_MODEL_RELEASE": 2,
		"PEER_TYPE_TRAINING_SET":  3,
	}
)

func (x PeerType) Enum() *PeerType {
	p := new(PeerType)
	*p = x
	return p
}

func (x PeerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PeerType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_relation_proto_enumTypes[1].Descriptor()
}

func (PeerType) Type() protoreflect.EnumType {
	return &file_proto_relation_proto_enumTypes[1]
}

func (x PeerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PeerType.Descriptor instead.
func (PeerType) EnumDescriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{1}
}

// Relation 定义了模型/模型版本之间或与其它相关资源之间的关联关系
// 其中 relation 的主语为当前 Relation 所属的资源，
// 表达的完整语义为：  current resource is ${relation type} of ${peer_type}:${peer_id} , link page =${link}
// e.g. [model release v1] is [RELATION_TYPE_PARENT_OF] [model release v2]
// 模型仓库会与其他组件(例如样本仓库)的资源，产生某种关联。但是在模型仓库中难以直接获取到其他组件的资源，所以在模型仓库中添加关系时，
// 需要把关联资源的其他必要信息填充到relation.info字段中。
// 现对info中通用信息的key做如下约定，在填写信息时需与key的含义对应
// info: {
// "name": "我的第一个资源",                                  // 关联资源的名称
// "desc": "这是我创建的第一个资源，该资源用于测试",              // 关联资源的描述
// "labels": "{"维护人": "张三", "类型": "目标检测"}",         // 关联资源的标签
// "version": "v1",                                        // 关联资源的版本
// "raw": "{"key1": "value", "key2": ["item", "item"]}"    // 关联资源的json格式全量字段
// // 其他常用key按需补充
// }
type Relation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RelationType RelationType      `protobuf:"varint,1,opt,name=relation_type,json=relationType,proto3,enum=proto.RelationType" json:"relation_type,omitempty"`                            // 关联类型, 参考JIRA问题关联类型 e.g. is child of /  is integrated from / is trained by / ...
	PeerId       string            `protobuf:"bytes,2,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`                                                                       // 关联对象ID
	PeerType     PeerType          `protobuf:"varint,3,opt,name=peer_type,json=peerType,proto3,enum=proto.PeerType" json:"peer_type,omitempty"`                                            // 关联对象类型, e.g model / release / dataset / vlab / ...
	Link         string            `protobuf:"bytes,4,opt,name=link,proto3" json:"link,omitempty"`                                                                                         // 关联对象跳转详情页
	Info         map[string]string `protobuf:"bytes,5,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 关联对象的额外描述信息
	PeerName     string            `protobuf:"bytes,6,opt,name=peer_name,json=peerName,proto3" json:"peer_name,omitempty"`
}

func (x *Relation) Reset() {
	*x = Relation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_relation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_relation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{0}
}

func (x *Relation) GetRelationType() RelationType {
	if x != nil {
		return x.RelationType
	}
	return RelationType_RELATION_TYPE_UNSPECIFIED
}

func (x *Relation) GetPeerId() string {
	if x != nil {
		return x.PeerId
	}
	return ""
}

func (x *Relation) GetPeerType() PeerType {
	if x != nil {
		return x.PeerType
	}
	return PeerType_PEER_TYPE_UNSPECIFIED
}

func (x *Relation) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Relation) GetInfo() map[string]string {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Relation) GetPeerName() string {
	if x != nil {
		return x.PeerName
	}
	return ""
}

type RelationGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CenterNode *RelationGraph_Node   `protobuf:"bytes,1,opt,name=center_node,json=centerNode,proto3" json:"center_node,omitempty"`
	Nodes      []*RelationGraph_Node `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Links      []*RelationGraph_Link `protobuf:"bytes,3,rep,name=links,proto3" json:"links,omitempty"`
}

func (x *RelationGraph) Reset() {
	*x = RelationGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_relation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationGraph) ProtoMessage() {}

func (x *RelationGraph) ProtoReflect() protoreflect.Message {
	mi := &file_proto_relation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationGraph.ProtoReflect.Descriptor instead.
func (*RelationGraph) Descriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{1}
}

func (x *RelationGraph) GetCenterNode() *RelationGraph_Node {
	if x != nil {
		return x.CenterNode
	}
	return nil
}

func (x *RelationGraph) GetNodes() []*RelationGraph_Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *RelationGraph) GetLinks() []*RelationGraph_Link {
	if x != nil {
		return x.Links
	}
	return nil
}

type RelationGraph_Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name       string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type       PeerType          `protobuf:"varint,3,opt,name=type,proto3,enum=proto.PeerType" json:"type,omitempty"`
	ExtRefLink string            `protobuf:"bytes,4,opt,name=ext_ref_link,json=extRefLink,proto3" json:"ext_ref_link,omitempty"`
	NodeInfo   map[string]string `protobuf:"bytes,5,rep,name=node_info,json=nodeInfo,proto3" json:"node_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RelationGraph_Node) Reset() {
	*x = RelationGraph_Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_relation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationGraph_Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationGraph_Node) ProtoMessage() {}

func (x *RelationGraph_Node) ProtoReflect() protoreflect.Message {
	mi := &file_proto_relation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationGraph_Node.ProtoReflect.Descriptor instead.
func (*RelationGraph_Node) Descriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RelationGraph_Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RelationGraph_Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RelationGraph_Node) GetType() PeerType {
	if x != nil {
		return x.Type
	}
	return PeerType_PEER_TYPE_UNSPECIFIED
}

func (x *RelationGraph_Node) GetExtRefLink() string {
	if x != nil {
		return x.ExtRefLink
	}
	return ""
}

func (x *RelationGraph_Node) GetNodeInfo() map[string]string {
	if x != nil {
		return x.NodeInfo
	}
	return nil
}

type RelationGraph_Link struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source   string            `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Target   string            `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	Relation RelationType      `protobuf:"varint,3,opt,name=relation,proto3,enum=proto.RelationType" json:"relation,omitempty"`
	LinkInfo map[string]string `protobuf:"bytes,4,rep,name=link_info,json=linkInfo,proto3" json:"link_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RelationGraph_Link) Reset() {
	*x = RelationGraph_Link{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_relation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationGraph_Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationGraph_Link) ProtoMessage() {}

func (x *RelationGraph_Link) ProtoReflect() protoreflect.Message {
	mi := &file_proto_relation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationGraph_Link.ProtoReflect.Descriptor instead.
func (*RelationGraph_Link) Descriptor() ([]byte, []int) {
	return file_proto_relation_proto_rawDescGZIP(), []int{1, 1}
}

func (x *RelationGraph_Link) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *RelationGraph_Link) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *RelationGraph_Link) GetRelation() RelationType {
	if x != nil {
		return x.Relation
	}
	return RelationType_RELATION_TYPE_UNSPECIFIED
}

func (x *RelationGraph_Link) GetLinkInfo() map[string]string {
	if x != nil {
		return x.LinkInfo
	}
	return nil
}

var File_proto_relation_proto protoreflect.FileDescriptor

var file_proto_relation_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x02,
	0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x0d, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x65, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x09, 0x70, 0x65, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x70, 0x65, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x65, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x65, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x49,
	0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x05, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x3a, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x05, 0x6c,
	0x69, 0x6e, 0x6b, 0x73, 0x1a, 0xf4, 0x01, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65,
	0x66, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x44, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xea, 0x01, 0x0a, 0x04,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x4c,
	0x69, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x3b, 0x0a, 0x0d, 0x4c,
	0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0xd7, 0x03, 0x0a, 0x0c, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x4c,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x43, 0x48, 0x49,
	0x4c, 0x44, 0x52, 0x45, 0x4e, 0x5f, 0x4f, 0x46, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45,
	0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x50,
	0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45,
	0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x48, 0x45,
	0x52, 0x49, 0x54, 0x53, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x49, 0x4e, 0x48, 0x45, 0x52,
	0x49, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x4c,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4b, 0x53,
	0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x4b, 0x45, 0x44, 0x5f, 0x42, 0x59,
	0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4f,
	0x46, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x45, 0x5f, 0x54, 0x55, 0x4e, 0x45, 0x44, 0x5f,
	0x46, 0x52, 0x4f, 0x4d, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e,
	0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x10, 0x0a, 0x12, 0x21, 0x0a, 0x1d, 0x52,
	0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f,
	0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x24,
	0x0a, 0x20, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x46,
	0x4f, 0x52, 0x10, 0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x4d,
	0x10, 0x0d, 0x2a, 0x73, 0x0a, 0x08, 0x50, 0x65, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x45, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x50, 0x45, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x45, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x03, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f,
	0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_relation_proto_rawDescOnce sync.Once
	file_proto_relation_proto_rawDescData = file_proto_relation_proto_rawDesc
)

func file_proto_relation_proto_rawDescGZIP() []byte {
	file_proto_relation_proto_rawDescOnce.Do(func() {
		file_proto_relation_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_relation_proto_rawDescData)
	})
	return file_proto_relation_proto_rawDescData
}

var file_proto_relation_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_relation_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proto_relation_proto_goTypes = []interface{}{
	(RelationType)(0),          // 0: proto.RelationType
	(PeerType)(0),              // 1: proto.PeerType
	(*Relation)(nil),           // 2: proto.Relation
	(*RelationGraph)(nil),      // 3: proto.RelationGraph
	nil,                        // 4: proto.Relation.InfoEntry
	(*RelationGraph_Node)(nil), // 5: proto.RelationGraph.Node
	(*RelationGraph_Link)(nil), // 6: proto.RelationGraph.Link
	nil,                        // 7: proto.RelationGraph.Node.NodeInfoEntry
	nil,                        // 8: proto.RelationGraph.Link.LinkInfoEntry
}
var file_proto_relation_proto_depIdxs = []int32{
	0,  // 0: proto.Relation.relation_type:type_name -> proto.RelationType
	1,  // 1: proto.Relation.peer_type:type_name -> proto.PeerType
	4,  // 2: proto.Relation.info:type_name -> proto.Relation.InfoEntry
	5,  // 3: proto.RelationGraph.center_node:type_name -> proto.RelationGraph.Node
	5,  // 4: proto.RelationGraph.nodes:type_name -> proto.RelationGraph.Node
	6,  // 5: proto.RelationGraph.links:type_name -> proto.RelationGraph.Link
	1,  // 6: proto.RelationGraph.Node.type:type_name -> proto.PeerType
	7,  // 7: proto.RelationGraph.Node.node_info:type_name -> proto.RelationGraph.Node.NodeInfoEntry
	0,  // 8: proto.RelationGraph.Link.relation:type_name -> proto.RelationType
	8,  // 9: proto.RelationGraph.Link.link_info:type_name -> proto.RelationGraph.Link.LinkInfoEntry
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_relation_proto_init() }
func file_proto_relation_proto_init() {
	if File_proto_relation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_relation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Relation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_relation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_relation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationGraph_Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_relation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationGraph_Link); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_relation_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_relation_proto_goTypes,
		DependencyIndexes: file_proto_relation_proto_depIdxs,
		EnumInfos:         file_proto_relation_proto_enumTypes,
		MessageInfos:      file_proto_relation_proto_msgTypes,
	}.Build()
	File_proto_relation_proto = out.File
	file_proto_relation_proto_rawDesc = nil
	file_proto_relation_proto_goTypes = nil
	file_proto_relation_proto_depIdxs = nil
}
