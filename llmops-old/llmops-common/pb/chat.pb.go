// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/chat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QAAuthor int32

const (
	QAAuthor_QA_AUTHOR_UNSPECIFIC QAAuthor = 0
	QAAuthor_QA_AUTHOR_USER       QAAuthor = 1
	QAAuthor_QA_AUTHOR_ASSISTANT  QAAuthor = 2
)

// Enum value maps for QAAuthor.
var (
	QAAuthor_name = map[int32]string{
		0: "QA_AUTHOR_UNSPECIFIC",
		1: "QA_AUTHOR_USER",
		2: "QA_AUTHOR_ASSISTANT",
	}
	QAAuthor_value = map[string]int32{
		"QA_AUTHOR_UNSPECIFIC": 0,
		"QA_AUTHOR_USER":       1,
		"QA_AUTHOR_ASSISTANT":  2,
	}
)

func (x QAAuthor) Enum() *QAAuthor {
	p := new(QAAuthor)
	*p = x
	return p
}

func (x QAAuthor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QAAuthor) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_chat_proto_enumTypes[0].Descriptor()
}

func (QAAuthor) Type() protoreflect.EnumType {
	return &file_proto_chat_proto_enumTypes[0]
}

func (x QAAuthor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QAAuthor.Descriptor instead.
func (QAAuthor) EnumDescriptor() ([]byte, []int) {
	return file_proto_chat_proto_rawDescGZIP(), []int{0}
}

type QAStatus int32

const (
	QAStatus_QA_STATUS_UNSPECIFIC QAStatus = 0
	QAStatus_QA_STATUS_SUCCESS    QAStatus = 1
	QAStatus_QA_STATUS_FAILED     QAStatus = 2 // 推理出错
	QAStatus_QA_STATUS_STOPPED    QAStatus = 3 // 被用户手动停止
)

// Enum value maps for QAStatus.
var (
	QAStatus_name = map[int32]string{
		0: "QA_STATUS_UNSPECIFIC",
		1: "QA_STATUS_SUCCESS",
		2: "QA_STATUS_FAILED",
		3: "QA_STATUS_STOPPED",
	}
	QAStatus_value = map[string]int32{
		"QA_STATUS_UNSPECIFIC": 0,
		"QA_STATUS_SUCCESS":    1,
		"QA_STATUS_FAILED":     2,
		"QA_STATUS_STOPPED":    3,
	}
)

func (x QAStatus) Enum() *QAStatus {
	p := new(QAStatus)
	*p = x
	return p
}

func (x QAStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QAStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_chat_proto_enumTypes[1].Descriptor()
}

func (QAStatus) Type() protoreflect.EnumType {
	return &file_proto_chat_proto_enumTypes[1]
}

func (x QAStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QAStatus.Descriptor instead.
func (QAStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_chat_proto_rawDescGZIP(), []int{1}
}

type QARating int32

const (
	QARating_QA_RATING_UNSPECIFIC QARating = 0
	QARating_QA_RATING_UPVOTE     QARating = 1
	QARating_QA_RATING_DOWNVOTE   QARating = 2
)

// Enum value maps for QARating.
var (
	QARating_name = map[int32]string{
		0: "QA_RATING_UNSPECIFIC",
		1: "QA_RATING_UPVOTE",
		2: "QA_RATING_DOWNVOTE",
	}
	QARating_value = map[string]int32{
		"QA_RATING_UNSPECIFIC": 0,
		"QA_RATING_UPVOTE":     1,
		"QA_RATING_DOWNVOTE":   2,
	}
)

func (x QARating) Enum() *QARating {
	p := new(QARating)
	*p = x
	return p
}

func (x QARating) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QARating) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_chat_proto_enumTypes[2].Descriptor()
}

func (QARating) Type() protoreflect.EnumType {
	return &file_proto_chat_proto_enumTypes[2]
}

func (x QARating) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QARating.Descriptor instead.
func (QARating) EnumDescriptor() ([]byte, []int) {
	return file_proto_chat_proto_rawDescGZIP(), []int{2}
}

type QA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content      string            `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Rating       QARating          `protobuf:"varint,3,opt,name=rating,proto3,enum=proto.QARating" json:"rating,omitempty"`
	Status       QAStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=proto.QAStatus" json:"status,omitempty"`
	CreateTimeMs int64             `protobuf:"varint,5,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
	Params       map[string]string `protobuf:"bytes,6,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Parent       string            `protobuf:"bytes,7,opt,name=parent,proto3" json:"parent,omitempty"`
	Children     []string          `protobuf:"bytes,8,rep,name=children,proto3" json:"children,omitempty"`
	IsAnswer     bool              `protobuf:"varint,9,opt,name=is_answer,json=isAnswer,proto3" json:"is_answer,omitempty"`
	Author       QAAuthor          `protobuf:"varint,10,opt,name=author,proto3,enum=proto.QAAuthor" json:"author,omitempty"`
	EndTurn      bool              `protobuf:"varint,11,opt,name=end_turn,json=endTurn,proto3" json:"end_turn,omitempty"`
}

func (x *QA) Reset() {
	*x = QA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_chat_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QA) ProtoMessage() {}

func (x *QA) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chat_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QA.ProtoReflect.Descriptor instead.
func (*QA) Descriptor() ([]byte, []int) {
	return file_proto_chat_proto_rawDescGZIP(), []int{0}
}

func (x *QA) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *QA) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *QA) GetRating() QARating {
	if x != nil {
		return x.Rating
	}
	return QARating_QA_RATING_UNSPECIFIC
}

func (x *QA) GetStatus() QAStatus {
	if x != nil {
		return x.Status
	}
	return QAStatus_QA_STATUS_UNSPECIFIC
}

func (x *QA) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *QA) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *QA) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *QA) GetChildren() []string {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *QA) GetIsAnswer() bool {
	if x != nil {
		return x.IsAnswer
	}
	return false
}

func (x *QA) GetAuthor() QAAuthor {
	if x != nil {
		return x.Author
	}
	return QAAuthor_QA_AUTHOR_UNSPECIFIC
}

func (x *QA) GetEndTurn() bool {
	if x != nil {
		return x.EndTurn
	}
	return false
}

type Conversation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title        string         `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CreateTimeMs int64          `protobuf:"varint,3,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
	UpdateTimeMs int64          `protobuf:"varint,4,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
	Rounds       map[string]*QA `protobuf:"bytes,5,rep,name=rounds,proto3" json:"rounds,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 对话轮次
	DeploymentId string         `protobuf:"bytes,6,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"`
	Creator      string         `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	ProjectId    string         `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *Conversation) Reset() {
	*x = Conversation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_chat_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Conversation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Conversation) ProtoMessage() {}

func (x *Conversation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chat_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Conversation.ProtoReflect.Descriptor instead.
func (*Conversation) Descriptor() ([]byte, []int) {
	return file_proto_chat_proto_rawDescGZIP(), []int{1}
}

func (x *Conversation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Conversation) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Conversation) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *Conversation) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *Conversation) GetRounds() map[string]*QA {
	if x != nil {
		return x.Rounds
	}
	return nil
}

func (x *Conversation) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

func (x *Conversation) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Conversation) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

var File_proto_chat_proto protoreflect.FileDescriptor

var file_proto_chat_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa5, 0x03, 0x0a, 0x02, 0x51, 0x41,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x51, 0x41, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x41, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x41, 0x2e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x12, 0x27, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x41, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x75, 0x72, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x75, 0x72, 0x6e, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xdd, 0x02, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24,
	0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x1a, 0x44, 0x0a, 0x0b, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x41, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x2a, 0x51, 0x0a, 0x08, 0x51, 0x41, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x14, 0x51, 0x41, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x43, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x51, 0x41, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x4f, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x51,
	0x41, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41,
	0x4e, 0x54, 0x10, 0x02, 0x2a, 0x68, 0x0a, 0x08, 0x51, 0x41, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x14, 0x51, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x43, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x51, 0x41,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x51, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x52,
	0x0a, 0x08, 0x51, 0x41, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x51, 0x41,
	0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x43, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x41, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x55, 0x50, 0x56, 0x4f, 0x54, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x51, 0x41,
	0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x56, 0x4f, 0x54, 0x45,
	0x10, 0x02, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_chat_proto_rawDescOnce sync.Once
	file_proto_chat_proto_rawDescData = file_proto_chat_proto_rawDesc
)

func file_proto_chat_proto_rawDescGZIP() []byte {
	file_proto_chat_proto_rawDescOnce.Do(func() {
		file_proto_chat_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_chat_proto_rawDescData)
	})
	return file_proto_chat_proto_rawDescData
}

var file_proto_chat_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_chat_proto_goTypes = []interface{}{
	(QAAuthor)(0),        // 0: proto.QAAuthor
	(QAStatus)(0),        // 1: proto.QAStatus
	(QARating)(0),        // 2: proto.QARating
	(*QA)(nil),           // 3: proto.QA
	(*Conversation)(nil), // 4: proto.Conversation
	nil,                  // 5: proto.QA.ParamsEntry
	nil,                  // 6: proto.Conversation.RoundsEntry
}
var file_proto_chat_proto_depIdxs = []int32{
	2, // 0: proto.QA.rating:type_name -> proto.QARating
	1, // 1: proto.QA.status:type_name -> proto.QAStatus
	5, // 2: proto.QA.params:type_name -> proto.QA.ParamsEntry
	0, // 3: proto.QA.author:type_name -> proto.QAAuthor
	6, // 4: proto.Conversation.rounds:type_name -> proto.Conversation.RoundsEntry
	3, // 5: proto.Conversation.RoundsEntry.value:type_name -> proto.QA
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_proto_chat_proto_init() }
func file_proto_chat_proto_init() {
	if File_proto_chat_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_chat_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_chat_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Conversation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_chat_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_chat_proto_goTypes,
		DependencyIndexes: file_proto_chat_proto_depIdxs,
		EnumInfos:         file_proto_chat_proto_enumTypes,
		MessageInfos:      file_proto_chat_proto_msgTypes,
	}.Build()
	File_proto_chat_proto = out.File
	file_proto_chat_proto_rawDesc = nil
	file_proto_chat_proto_goTypes = nil
	file_proto_chat_proto_depIdxs = nil
}
