// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_image_template.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListTemplatesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext  *UserContext  `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	ListSelector *ListSelector `protobuf:"bytes,2,opt,name=list_selector,json=listSelector,proto3" json:"list_selector,omitempty"`
	PageReq      *PageReq      `protobuf:"bytes,3,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
}

func (x *ListTemplatesReq) Reset() {
	*x = ListTemplatesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesReq) ProtoMessage() {}

func (x *ListTemplatesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesReq.ProtoReflect.Descriptor instead.
func (*ListTemplatesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{0}
}

func (x *ListTemplatesReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListTemplatesReq) GetListSelector() *ListSelector {
	if x != nil {
		return x.ListSelector
	}
	return nil
}

func (x *ListTemplatesReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

type ListSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateType   []TemplateType   `protobuf:"varint,1,rep,packed,name=template_type,json=templateType,proto3,enum=proto.TemplateType" json:"template_type,omitempty"`
	TemplateStatus []TemplateStatus `protobuf:"varint,2,rep,packed,name=template_status,json=templateStatus,proto3,enum=proto.TemplateStatus" json:"template_status,omitempty"`
	TemplateKind   []TemplateKind   `protobuf:"varint,3,rep,packed,name=template_kind,json=templateKind,proto3,enum=proto.TemplateKind" json:"template_kind,omitempty"`
	TemplateSource []TemplateSource `protobuf:"varint,4,rep,packed,name=template_source,json=templateSource,proto3,enum=proto.TemplateSource" json:"template_source,omitempty"`
}

func (x *ListSelector) Reset() {
	*x = ListSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSelector) ProtoMessage() {}

func (x *ListSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSelector.ProtoReflect.Descriptor instead.
func (*ListSelector) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{1}
}

func (x *ListSelector) GetTemplateType() []TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return nil
}

func (x *ListSelector) GetTemplateStatus() []TemplateStatus {
	if x != nil {
		return x.TemplateStatus
	}
	return nil
}

func (x *ListSelector) GetTemplateKind() []TemplateKind {
	if x != nil {
		return x.TemplateKind
	}
	return nil
}

func (x *ListSelector) GetTemplateSource() []TemplateSource {
	if x != nil {
		return x.TemplateSource
	}
	return nil
}

type ListTemplatesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Templates []*ImageTemplate `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	Total     int32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageNum   int32            `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize  int32            `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListTemplatesRsp) Reset() {
	*x = ListTemplatesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesRsp) ProtoMessage() {}

func (x *ListTemplatesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesRsp.ProtoReflect.Descriptor instead.
func (*ListTemplatesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{2}
}

func (x *ListTemplatesRsp) GetTemplates() []*ImageTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *ListTemplatesRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTemplatesRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ListTemplatesRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id          string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTemplateReq) Reset() {
	*x = GetTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateReq) ProtoMessage() {}

func (x *GetTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateReq.ProtoReflect.Descriptor instead.
func (*GetTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{3}
}

func (x *GetTemplateReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *GetTemplateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *ImageTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *GetTemplateRsp) Reset() {
	*x = GetTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRsp) ProtoMessage() {}

func (x *GetTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRsp.ProtoReflect.Descriptor instead.
func (*GetTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{4}
}

func (x *GetTemplateRsp) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type CreateTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext   `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Template    *ImageTemplate `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CreateTemplateReq) Reset() {
	*x = CreateTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateReq) ProtoMessage() {}

func (x *CreateTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateReq.ProtoReflect.Descriptor instead.
func (*CreateTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{5}
}

func (x *CreateTemplateReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CreateTemplateReq) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type CreateTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *ImageTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CreateTemplateRsp) Reset() {
	*x = CreateTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateRsp) ProtoMessage() {}

func (x *CreateTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateRsp.ProtoReflect.Descriptor instead.
func (*CreateTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{6}
}

func (x *CreateTemplateRsp) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type UpdateTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext   `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Template    *ImageTemplate `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *UpdateTemplateReq) Reset() {
	*x = UpdateTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateReq) ProtoMessage() {}

func (x *UpdateTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateReq.ProtoReflect.Descriptor instead.
func (*UpdateTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateTemplateReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *UpdateTemplateReq) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type UpdateTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *ImageTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *UpdateTemplateRsp) Reset() {
	*x = UpdateTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateRsp) ProtoMessage() {}

func (x *UpdateTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateRsp.ProtoReflect.Descriptor instead.
func (*UpdateTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateTemplateRsp) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type DeleteTemplatesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Ids         []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"` // 可同时删除多个模板
}

func (x *DeleteTemplatesReq) Reset() {
	*x = DeleteTemplatesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTemplatesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplatesReq) ProtoMessage() {}

func (x *DeleteTemplatesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplatesReq.ProtoReflect.Descriptor instead.
func (*DeleteTemplatesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteTemplatesReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *DeleteTemplatesReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteTemplatesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteTemplatesRsp) Reset() {
	*x = DeleteTemplatesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTemplatesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplatesRsp) ProtoMessage() {}

func (x *DeleteTemplatesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplatesRsp.ProtoReflect.Descriptor instead.
func (*DeleteTemplatesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{10}
}

type CloneTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id              string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	TargetProjectId string       `protobuf:"bytes,3,opt,name=target_project_id,json=targetProjectId,proto3" json:"target_project_id,omitempty"` // 目标项目，为空或者和user_context一致则为项目内克隆
}

func (x *CloneTemplateReq) Reset() {
	*x = CloneTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloneTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloneTemplateReq) ProtoMessage() {}

func (x *CloneTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloneTemplateReq.ProtoReflect.Descriptor instead.
func (*CloneTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{11}
}

func (x *CloneTemplateReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CloneTemplateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CloneTemplateReq) GetTargetProjectId() string {
	if x != nil {
		return x.TargetProjectId
	}
	return ""
}

type CloneTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *ImageTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CloneTemplateRsp) Reset() {
	*x = CloneTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_image_template_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloneTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloneTemplateRsp) ProtoMessage() {}

func (x *CloneTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_image_template_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloneTemplateRsp.ProtoReflect.Descriptor instead.
func (*CloneTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_image_template_proto_rawDescGZIP(), []int{12}
}

func (x *CloneTemplateRsp) GetTemplate() *ImageTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

var File_proto_rpc_image_template_proto protoreflect.FileDescriptor

var file_proto_rpc_image_template_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x0c, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x22, 0x82, 0x02, 0x0a, 0x0c, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x0d, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52,
	0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x3e, 0x0a,
	0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x94, 0x01,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x32, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x22, 0x57, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x42, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x30, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x7c, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x30, 0x0a,
	0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22,
	0x45, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x7c, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x22, 0x45, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x08, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x5d, 0x0a, 0x12, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x73, 0x70,
	0x22, 0x85, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x10, 0x43, 0x6c, 0x6f, 0x6e,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x08,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x32, 0xae,
	0x03, 0x0a, 0x14, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42,
	0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f,
	0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_image_template_proto_rawDescOnce sync.Once
	file_proto_rpc_image_template_proto_rawDescData = file_proto_rpc_image_template_proto_rawDesc
)

func file_proto_rpc_image_template_proto_rawDescGZIP() []byte {
	file_proto_rpc_image_template_proto_rawDescOnce.Do(func() {
		file_proto_rpc_image_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_image_template_proto_rawDescData)
	})
	return file_proto_rpc_image_template_proto_rawDescData
}

var file_proto_rpc_image_template_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_rpc_image_template_proto_goTypes = []interface{}{
	(*ListTemplatesReq)(nil),   // 0: proto.ListTemplatesReq
	(*ListSelector)(nil),       // 1: proto.ListSelector
	(*ListTemplatesRsp)(nil),   // 2: proto.ListTemplatesRsp
	(*GetTemplateReq)(nil),     // 3: proto.GetTemplateReq
	(*GetTemplateRsp)(nil),     // 4: proto.GetTemplateRsp
	(*CreateTemplateReq)(nil),  // 5: proto.CreateTemplateReq
	(*CreateTemplateRsp)(nil),  // 6: proto.CreateTemplateRsp
	(*UpdateTemplateReq)(nil),  // 7: proto.UpdateTemplateReq
	(*UpdateTemplateRsp)(nil),  // 8: proto.UpdateTemplateRsp
	(*DeleteTemplatesReq)(nil), // 9: proto.DeleteTemplatesReq
	(*DeleteTemplatesRsp)(nil), // 10: proto.DeleteTemplatesRsp
	(*CloneTemplateReq)(nil),   // 11: proto.CloneTemplateReq
	(*CloneTemplateRsp)(nil),   // 12: proto.CloneTemplateRsp
	(*UserContext)(nil),        // 13: proto.UserContext
	(*PageReq)(nil),            // 14: proto.PageReq
	(TemplateType)(0),          // 15: proto.TemplateType
	(TemplateStatus)(0),        // 16: proto.TemplateStatus
	(TemplateKind)(0),          // 17: proto.TemplateKind
	(TemplateSource)(0),        // 18: proto.TemplateSource
	(*ImageTemplate)(nil),      // 19: proto.ImageTemplate
}
var file_proto_rpc_image_template_proto_depIdxs = []int32{
	13, // 0: proto.ListTemplatesReq.user_context:type_name -> proto.UserContext
	1,  // 1: proto.ListTemplatesReq.list_selector:type_name -> proto.ListSelector
	14, // 2: proto.ListTemplatesReq.page_req:type_name -> proto.PageReq
	15, // 3: proto.ListSelector.template_type:type_name -> proto.TemplateType
	16, // 4: proto.ListSelector.template_status:type_name -> proto.TemplateStatus
	17, // 5: proto.ListSelector.template_kind:type_name -> proto.TemplateKind
	18, // 6: proto.ListSelector.template_source:type_name -> proto.TemplateSource
	19, // 7: proto.ListTemplatesRsp.templates:type_name -> proto.ImageTemplate
	13, // 8: proto.GetTemplateReq.user_context:type_name -> proto.UserContext
	19, // 9: proto.GetTemplateRsp.template:type_name -> proto.ImageTemplate
	13, // 10: proto.CreateTemplateReq.user_context:type_name -> proto.UserContext
	19, // 11: proto.CreateTemplateReq.template:type_name -> proto.ImageTemplate
	19, // 12: proto.CreateTemplateRsp.template:type_name -> proto.ImageTemplate
	13, // 13: proto.UpdateTemplateReq.user_context:type_name -> proto.UserContext
	19, // 14: proto.UpdateTemplateReq.template:type_name -> proto.ImageTemplate
	19, // 15: proto.UpdateTemplateRsp.template:type_name -> proto.ImageTemplate
	13, // 16: proto.DeleteTemplatesReq.user_context:type_name -> proto.UserContext
	13, // 17: proto.CloneTemplateReq.user_context:type_name -> proto.UserContext
	19, // 18: proto.CloneTemplateRsp.template:type_name -> proto.ImageTemplate
	0,  // 19: proto.ImageTemplateManager.ListTemplates:input_type -> proto.ListTemplatesReq
	3,  // 20: proto.ImageTemplateManager.GetTemplate:input_type -> proto.GetTemplateReq
	5,  // 21: proto.ImageTemplateManager.CreateTemplate:input_type -> proto.CreateTemplateReq
	7,  // 22: proto.ImageTemplateManager.UpdateTemplate:input_type -> proto.UpdateTemplateReq
	9,  // 23: proto.ImageTemplateManager.DeleteTemplates:input_type -> proto.DeleteTemplatesReq
	11, // 24: proto.ImageTemplateManager.CloneTemplate:input_type -> proto.CloneTemplateReq
	2,  // 25: proto.ImageTemplateManager.ListTemplates:output_type -> proto.ListTemplatesRsp
	4,  // 26: proto.ImageTemplateManager.GetTemplate:output_type -> proto.GetTemplateRsp
	6,  // 27: proto.ImageTemplateManager.CreateTemplate:output_type -> proto.CreateTemplateRsp
	8,  // 28: proto.ImageTemplateManager.UpdateTemplate:output_type -> proto.UpdateTemplateRsp
	10, // 29: proto.ImageTemplateManager.DeleteTemplates:output_type -> proto.DeleteTemplatesRsp
	12, // 30: proto.ImageTemplateManager.CloneTemplate:output_type -> proto.CloneTemplateRsp
	25, // [25:31] is the sub-list for method output_type
	19, // [19:25] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_proto_rpc_image_template_proto_init() }
func file_proto_rpc_image_template_proto_init() {
	if File_proto_rpc_image_template_proto != nil {
		return
	}
	file_proto_image_template_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_image_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTemplatesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTemplatesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloneTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_image_template_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloneTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_image_template_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_image_template_proto_goTypes,
		DependencyIndexes: file_proto_rpc_image_template_proto_depIdxs,
		MessageInfos:      file_proto_rpc_image_template_proto_msgTypes,
	}.Build()
	File_proto_rpc_image_template_proto = out.File
	file_proto_rpc_image_template_proto_rawDesc = nil
	file_proto_rpc_image_template_proto_goTypes = nil
	file_proto_rpc_image_template_proto_depIdxs = nil
}
