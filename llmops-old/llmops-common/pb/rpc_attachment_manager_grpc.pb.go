// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_attachment_manager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AttachmentManager_GetAttachment_FullMethodName    = "/proto.AttachmentManager/GetAttachment"
	AttachmentManager_CreateAttachment_FullMethodName = "/proto.AttachmentManager/CreateAttachment"
	AttachmentManager_DeleteAttachment_FullMethodName = "/proto.AttachmentManager/DeleteAttachment"
	AttachmentManager_UpdateAttachment_FullMethodName = "/proto.AttachmentManager/UpdateAttachment"
)

// AttachmentManagerClient is the client API for AttachmentManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AttachmentManagerClient interface {
	GetAttachment(ctx context.Context, in *GetAttachmentReq, opts ...grpc.CallOption) (*GetAttachmentRsp, error)
	CreateAttachment(ctx context.Context, in *CreateAttachmentReq, opts ...grpc.CallOption) (*CreateAttachmentRsp, error)
	DeleteAttachment(ctx context.Context, in *DeleteAttachmentReq, opts ...grpc.CallOption) (*DeleteAttachmentRsp, error)
	UpdateAttachment(ctx context.Context, in *UpdateAttachmentReq, opts ...grpc.CallOption) (*UpdateAttachmentRsp, error)
}

type attachmentManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewAttachmentManagerClient(cc grpc.ClientConnInterface) AttachmentManagerClient {
	return &attachmentManagerClient{cc}
}

func (c *attachmentManagerClient) GetAttachment(ctx context.Context, in *GetAttachmentReq, opts ...grpc.CallOption) (*GetAttachmentRsp, error) {
	out := new(GetAttachmentRsp)
	err := c.cc.Invoke(ctx, AttachmentManager_GetAttachment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentManagerClient) CreateAttachment(ctx context.Context, in *CreateAttachmentReq, opts ...grpc.CallOption) (*CreateAttachmentRsp, error) {
	out := new(CreateAttachmentRsp)
	err := c.cc.Invoke(ctx, AttachmentManager_CreateAttachment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentManagerClient) DeleteAttachment(ctx context.Context, in *DeleteAttachmentReq, opts ...grpc.CallOption) (*DeleteAttachmentRsp, error) {
	out := new(DeleteAttachmentRsp)
	err := c.cc.Invoke(ctx, AttachmentManager_DeleteAttachment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentManagerClient) UpdateAttachment(ctx context.Context, in *UpdateAttachmentReq, opts ...grpc.CallOption) (*UpdateAttachmentRsp, error) {
	out := new(UpdateAttachmentRsp)
	err := c.cc.Invoke(ctx, AttachmentManager_UpdateAttachment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AttachmentManagerServer is the server API for AttachmentManager service.
// All implementations must embed UnimplementedAttachmentManagerServer
// for forward compatibility
type AttachmentManagerServer interface {
	GetAttachment(context.Context, *GetAttachmentReq) (*GetAttachmentRsp, error)
	CreateAttachment(context.Context, *CreateAttachmentReq) (*CreateAttachmentRsp, error)
	DeleteAttachment(context.Context, *DeleteAttachmentReq) (*DeleteAttachmentRsp, error)
	UpdateAttachment(context.Context, *UpdateAttachmentReq) (*UpdateAttachmentRsp, error)
	mustEmbedUnimplementedAttachmentManagerServer()
}

// UnimplementedAttachmentManagerServer must be embedded to have forward compatible implementations.
type UnimplementedAttachmentManagerServer struct {
}

func (UnimplementedAttachmentManagerServer) GetAttachment(context.Context, *GetAttachmentReq) (*GetAttachmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttachment not implemented")
}
func (UnimplementedAttachmentManagerServer) CreateAttachment(context.Context, *CreateAttachmentReq) (*CreateAttachmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAttachment not implemented")
}
func (UnimplementedAttachmentManagerServer) DeleteAttachment(context.Context, *DeleteAttachmentReq) (*DeleteAttachmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAttachment not implemented")
}
func (UnimplementedAttachmentManagerServer) UpdateAttachment(context.Context, *UpdateAttachmentReq) (*UpdateAttachmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAttachment not implemented")
}
func (UnimplementedAttachmentManagerServer) mustEmbedUnimplementedAttachmentManagerServer() {}

// UnsafeAttachmentManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AttachmentManagerServer will
// result in compilation errors.
type UnsafeAttachmentManagerServer interface {
	mustEmbedUnimplementedAttachmentManagerServer()
}

func RegisterAttachmentManagerServer(s grpc.ServiceRegistrar, srv AttachmentManagerServer) {
	s.RegisterService(&AttachmentManager_ServiceDesc, srv)
}

func _AttachmentManager_GetAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttachmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentManagerServer).GetAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttachmentManager_GetAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentManagerServer).GetAttachment(ctx, req.(*GetAttachmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttachmentManager_CreateAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAttachmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentManagerServer).CreateAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttachmentManager_CreateAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentManagerServer).CreateAttachment(ctx, req.(*CreateAttachmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttachmentManager_DeleteAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAttachmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentManagerServer).DeleteAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttachmentManager_DeleteAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentManagerServer).DeleteAttachment(ctx, req.(*DeleteAttachmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttachmentManager_UpdateAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAttachmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentManagerServer).UpdateAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttachmentManager_UpdateAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentManagerServer).UpdateAttachment(ctx, req.(*UpdateAttachmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AttachmentManager_ServiceDesc is the grpc.ServiceDesc for AttachmentManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AttachmentManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.AttachmentManager",
	HandlerType: (*AttachmentManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAttachment",
			Handler:    _AttachmentManager_GetAttachment_Handler,
		},
		{
			MethodName: "CreateAttachment",
			Handler:    _AttachmentManager_CreateAttachment_Handler,
		},
		{
			MethodName: "DeleteAttachment",
			Handler:    _AttachmentManager_DeleteAttachment_Handler,
		},
		{
			MethodName: "UpdateAttachment",
			Handler:    _AttachmentManager_UpdateAttachment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_attachment_manager.proto",
}
