// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/model.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DataType  Data types supported for input and output tensors.
type DataType int32

const (
	DataType_DATA_TYPE_INVALID DataType = 0
	// 各个不同框架的类型映射     Triton	    TensorRT	TensorFlow	    ONNX        PyTorch	    NumPy
	DataType_DATA_TYPE_BOOL   DataType = 1  // TYPE_BOOL	kBOOL	    DT_BOOL	        BOOL	    kBool	    bool
	DataType_DATA_TYPE_UINT8  DataType = 2  // TYPE_UINT8	kUINT8	    DT_UINT8	    UINT8	    kByte	    uint8
	DataType_DATA_TYPE_UINT16 DataType = 3  // TYPE_UINT16	----	    DT_UINT16	    UINT16	    ----	    uint16
	DataType_DATA_TYPE_UINT32 DataType = 4  // TYPE_UINT32	----	    DT_UINT32	    UINT32	    ----	    uint32
	DataType_DATA_TYPE_UINT64 DataType = 5  // TYPE_UINT64	----	    DT_UINT64	    UINT64	    ----	    uint64
	DataType_DATA_TYPE_INT8   DataType = 6  // TYPE_INT8	kINT8	    DT_INT8	        INT8	    kChar	    int8
	DataType_DATA_TYPE_INT16  DataType = 7  // TYPE_INT16	----	    DT_INT16	    INT16	    kShort	    int16
	DataType_DATA_TYPE_INT32  DataType = 8  // TYPE_INT32	kINT32	    DT_INT32	    INT32	    kInt	    int32
	DataType_DATA_TYPE_INT64  DataType = 9  // TYPE_INT64	----	    DT_INT64	    INT64	    kLong	    int64
	DataType_DATA_TYPE_FP16   DataType = 10 // TYPE_FP16	kHALF	    DT_HALF	        FLOAT16	    ----	    float16
	DataType_DATA_TYPE_FP32   DataType = 11 // TYPE_FP32	kFLOAT	    DT_FLOAT	    FLOAT	    kFloat	    float32
	DataType_DATA_TYPE_FP64   DataType = 12 // TYPE_FP64	----	    DT_DOUBLE	    DOUBLE	    kDouble	    float64
	DataType_DATA_TYPE_BYTES  DataType = 13 // TYPE_STRING	----	    DT_STRING	    STRING	    ----	    dtype(object)
	DataType_DATA_TYPE_BF16   DataType = 14 // TYPE_BF16	----	    ----	        ----	    ----	    ----
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0:  "DATA_TYPE_INVALID",
		1:  "DATA_TYPE_BOOL",
		2:  "DATA_TYPE_UINT8",
		3:  "DATA_TYPE_UINT16",
		4:  "DATA_TYPE_UINT32",
		5:  "DATA_TYPE_UINT64",
		6:  "DATA_TYPE_INT8",
		7:  "DATA_TYPE_INT16",
		8:  "DATA_TYPE_INT32",
		9:  "DATA_TYPE_INT64",
		10: "DATA_TYPE_FP16",
		11: "DATA_TYPE_FP32",
		12: "DATA_TYPE_FP64",
		13: "DATA_TYPE_BYTES",
		14: "DATA_TYPE_BF16",
	}
	DataType_value = map[string]int32{
		"DATA_TYPE_INVALID": 0,
		"DATA_TYPE_BOOL":    1,
		"DATA_TYPE_UINT8":   2,
		"DATA_TYPE_UINT16":  3,
		"DATA_TYPE_UINT32":  4,
		"DATA_TYPE_UINT64":  5,
		"DATA_TYPE_INT8":    6,
		"DATA_TYPE_INT16":   7,
		"DATA_TYPE_INT32":   8,
		"DATA_TYPE_INT64":   9,
		"DATA_TYPE_FP16":    10,
		"DATA_TYPE_FP32":    11,
		"DATA_TYPE_FP64":    12,
		"DATA_TYPE_BYTES":   13,
		"DATA_TYPE_BF16":    14,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{0}
}

// AssertType  模型市场判断当前模型类型： 内嵌 / 共享
type AssetType int32

const (
	AssetType_ASSET_SHARED   AssetType = 0 // 共享
	AssetType_ASSET_EMBEDDED AssetType = 1 // 内嵌
)

// Enum value maps for AssetType.
var (
	AssetType_name = map[int32]string{
		0: "ASSET_SHARED",
		1: "ASSET_EMBEDDED",
	}
	AssetType_value = map[string]int32{
		"ASSET_SHARED":   0,
		"ASSET_EMBEDDED": 1,
	}
)

func (x AssetType) Enum() *AssetType {
	p := new(AssetType)
	*p = x
	return p
}

func (x AssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[1].Descriptor()
}

func (AssetType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[1]
}

func (x AssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetType.Descriptor instead.
func (AssetType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{1}
}

// ModelKind 模型场景分类，如果是组合模型，model_kind指定为主场景，实无法确定时使用multi
type ModelKind int32

const (
	ModelKind_MODEL_KIND_UNSPECIFIED ModelKind = 0 // 未知场景
	ModelKind_MODEL_KIND_MULTI       ModelKind = 1 // 多模态场景
	ModelKind_MODEL_KIND_CV          ModelKind = 2 // 图片 -- 计算机视觉
	ModelKind_MODEL_KIND_OCR         ModelKind = 3 // 图片 -- 文本识别
	ModelKind_MODEL_KIND_NLP         ModelKind = 4 // 文本 -- 自然语言处理
	ModelKind_MODEL_KIND_ML          ModelKind = 5 // 结构化二维表 -- 机器学习
	ModelKind_MODEL_KIND_SR          ModelKind = 6 // 语音识别
)

// Enum value maps for ModelKind.
var (
	ModelKind_name = map[int32]string{
		0: "MODEL_KIND_UNSPECIFIED",
		1: "MODEL_KIND_MULTI",
		2: "MODEL_KIND_CV",
		3: "MODEL_KIND_OCR",
		4: "MODEL_KIND_NLP",
		5: "MODEL_KIND_ML",
		6: "MODEL_KIND_SR",
	}
	ModelKind_value = map[string]int32{
		"MODEL_KIND_UNSPECIFIED": 0,
		"MODEL_KIND_MULTI":       1,
		"MODEL_KIND_CV":          2,
		"MODEL_KIND_OCR":         3,
		"MODEL_KIND_NLP":         4,
		"MODEL_KIND_ML":          5,
		"MODEL_KIND_SR":          6,
	}
)

func (x ModelKind) Enum() *ModelKind {
	p := new(ModelKind)
	*p = x
	return p
}

func (x ModelKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelKind) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[2].Descriptor()
}

func (ModelKind) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[2]
}

func (x ModelKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelKind.Descriptor instead.
func (ModelKind) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{2}
}

// ModelSubKind 模型场景子类型
// 序号请遵循 [n][mm] 形式,其中:
//
//	n 对应 上级ModelKind的序号
//
// mm 请使用自增序号，从 01 - 99
// ！！！请勿随意调整已有类型序号，以避免前端适配错乱
type ModelSubKind int32

const (
	ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED               ModelSubKind = 0
	ModelSubKind_MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE       ModelSubKind = 101 // 文字转图片
	ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT       ModelSubKind = 102 // 图片转文字
	ModelSubKind_MODEL_SUB_KIND_MULTI_VIDEO_UNDERSTANDING ModelSubKind = 103
	ModelSubKind_MODEL_SUB_KIND_CV_CLS                    ModelSubKind = 201 // 图片分类
	ModelSubKind_MODEL_SUB_KIND_CV_DET                    ModelSubKind = 202 // 图片目标检测
	ModelSubKind_MODEL_SUB_KIND_CV_INS_SEG                ModelSubKind = 203 // 图片实例分割
	ModelSubKind_MODEL_SUB_KIND_CV_SEM_SEG                ModelSubKind = 204 // 图片语义分割
	ModelSubKind_MODEL_SUB_KIND_CV_OCR                    ModelSubKind = 205 // 图片字符识别
	ModelSubKind_MODEL_SUB_KIND_CV_IMAGE_EMBEDDING        ModelSubKind = 206 // 图片向量
	ModelSubKind_MODEL_SUB_KIND_OCR_COMMON                ModelSubKind = 301 // 通用文本识别
	ModelSubKind_MODEL_SUB_KIND_OCR_RECEIPTS              ModelSubKind = 302 // 票据识别
	ModelSubKind_MODEL_SUB_KIND_NLP_SINGLE_CLS            ModelSubKind = 401 // 单项文本分类
	ModelSubKind_MODEL_SUB_KIND_NLP_MULTI_CLS             ModelSubKind = 402 // 多项文本分类
	ModelSubKind_MODEL_SUB_KIND_NLP_ENTITY_RECOGNITION    ModelSubKind = 403 // 实体识别
	ModelSubKind_MODEL_SUB_KIND_NLP_SEMANTIC_RELATION     ModelSubKind = 404 // 语义关系识别
	ModelSubKind_MODEL_SUB_KIND_NLP_SENTIMENT_CLS         ModelSubKind = 405 // 情感分类
	ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION       ModelSubKind = 406 // 文本生成
	ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR           ModelSubKind = 407 // 文本向量
	ModelSubKind_MODEL_SUB_KIND_NLP_RERANKING             ModelSubKind = 408 // re-ranking model
	ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_COMPLETION       ModelSubKind = 409 // 文本补全
	ModelSubKind_MODEL_SUB_KIND_ML_BINARY_CLS             ModelSubKind = 501 // 二分类
	ModelSubKind_MODEL_SUB_KIND_ML_MULTI_CLS              ModelSubKind = 502 // 多分类
	ModelSubKind_MODEL_SUB_KIND_ML_REGRESSION             ModelSubKind = 503 // 回归
	ModelSubKind_MODEL_SUB_KIND_ML_CLUSTER                ModelSubKind = 504 // 聚类
	ModelSubKind_MODEL_SUB_KIND_SR_STT                    ModelSubKind = 601 // 语音转文字
	ModelSubKind_MODEL_SUB_KIND_SR_TTS                    ModelSubKind = 602 // 文字转语音
)

// Enum value maps for ModelSubKind.
var (
	ModelSubKind_name = map[int32]string{
		0:   "MODEL_SUB_KIND_UNSPECIFIED",
		101: "MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE",
		102: "MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT",
		103: "MODEL_SUB_KIND_MULTI_VIDEO_UNDERSTANDING",
		201: "MODEL_SUB_KIND_CV_CLS",
		202: "MODEL_SUB_KIND_CV_DET",
		203: "MODEL_SUB_KIND_CV_INS_SEG",
		204: "MODEL_SUB_KIND_CV_SEM_SEG",
		205: "MODEL_SUB_KIND_CV_OCR",
		206: "MODEL_SUB_KIND_CV_IMAGE_EMBEDDING",
		301: "MODEL_SUB_KIND_OCR_COMMON",
		302: "MODEL_SUB_KIND_OCR_RECEIPTS",
		401: "MODEL_SUB_KIND_NLP_SINGLE_CLS",
		402: "MODEL_SUB_KIND_NLP_MULTI_CLS",
		403: "MODEL_SUB_KIND_NLP_ENTITY_RECOGNITION",
		404: "MODEL_SUB_KIND_NLP_SEMANTIC_RELATION",
		405: "MODEL_SUB_KIND_NLP_SENTIMENT_CLS",
		406: "MODEL_SUB_KIND_NLP_TEXT_GENERATION",
		407: "MODEL_SUB_KIND_NLP_TEXT_VECTOR",
		408: "MODEL_SUB_KIND_NLP_RERANKING",
		409: "MODEL_SUB_KIND_NLP_TEXT_COMPLETION",
		501: "MODEL_SUB_KIND_ML_BINARY_CLS",
		502: "MODEL_SUB_KIND_ML_MULTI_CLS",
		503: "MODEL_SUB_KIND_ML_REGRESSION",
		504: "MODEL_SUB_KIND_ML_CLUSTER",
		601: "MODEL_SUB_KIND_SR_STT",
		602: "MODEL_SUB_KIND_SR_TTS",
	}
	ModelSubKind_value = map[string]int32{
		"MODEL_SUB_KIND_UNSPECIFIED":               0,
		"MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE":       101,
		"MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT":       102,
		"MODEL_SUB_KIND_MULTI_VIDEO_UNDERSTANDING": 103,
		"MODEL_SUB_KIND_CV_CLS":                    201,
		"MODEL_SUB_KIND_CV_DET":                    202,
		"MODEL_SUB_KIND_CV_INS_SEG":                203,
		"MODEL_SUB_KIND_CV_SEM_SEG":                204,
		"MODEL_SUB_KIND_CV_OCR":                    205,
		"MODEL_SUB_KIND_CV_IMAGE_EMBEDDING":        206,
		"MODEL_SUB_KIND_OCR_COMMON":                301,
		"MODEL_SUB_KIND_OCR_RECEIPTS":              302,
		"MODEL_SUB_KIND_NLP_SINGLE_CLS":            401,
		"MODEL_SUB_KIND_NLP_MULTI_CLS":             402,
		"MODEL_SUB_KIND_NLP_ENTITY_RECOGNITION":    403,
		"MODEL_SUB_KIND_NLP_SEMANTIC_RELATION":     404,
		"MODEL_SUB_KIND_NLP_SENTIMENT_CLS":         405,
		"MODEL_SUB_KIND_NLP_TEXT_GENERATION":       406,
		"MODEL_SUB_KIND_NLP_TEXT_VECTOR":           407,
		"MODEL_SUB_KIND_NLP_RERANKING":             408,
		"MODEL_SUB_KIND_NLP_TEXT_COMPLETION":       409,
		"MODEL_SUB_KIND_ML_BINARY_CLS":             501,
		"MODEL_SUB_KIND_ML_MULTI_CLS":              502,
		"MODEL_SUB_KIND_ML_REGRESSION":             503,
		"MODEL_SUB_KIND_ML_CLUSTER":                504,
		"MODEL_SUB_KIND_SR_STT":                    601,
		"MODEL_SUB_KIND_SR_TTS":                    602,
	}
)

func (x ModelSubKind) Enum() *ModelSubKind {
	p := new(ModelSubKind)
	*p = x
	return p
}

func (x ModelSubKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelSubKind) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[3].Descriptor()
}

func (ModelSubKind) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[3]
}

func (x ModelSubKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelSubKind.Descriptor instead.
func (ModelSubKind) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{3}
}

// TrainingTemplate 不同算法类型(训练模板)的大模型,微调参数有所不同,需要在模型层级进行标识
type TrainingTemplate int32

const (
	TrainingTemplate_TRAINING_TEMPLATE_UNSPECIFIED TrainingTemplate = 0
	TrainingTemplate_TRAINING_TEMPLATE_BLOOM       TrainingTemplate = 1
	TrainingTemplate_TRAINING_TEMPLATE_CHATGLM2    TrainingTemplate = 2
	TrainingTemplate_TRAINING_TEMPLATE_LLAMA       TrainingTemplate = 3
	TrainingTemplate_TRAINING_TEMPLATE_LLAMA2      TrainingTemplate = 4
	TrainingTemplate_TRAINING_TEMPLATE_FALCON      TrainingTemplate = 5
	TrainingTemplate_TRAINING_TEMPLATE_INTERNLM    TrainingTemplate = 6
	TrainingTemplate_TRAINING_TEMPLATE_STARCODER   TrainingTemplate = 7
)

// Enum value maps for TrainingTemplate.
var (
	TrainingTemplate_name = map[int32]string{
		0: "TRAINING_TEMPLATE_UNSPECIFIED",
		1: "TRAINING_TEMPLATE_BLOOM",
		2: "TRAINING_TEMPLATE_CHATGLM2",
		3: "TRAINING_TEMPLATE_LLAMA",
		4: "TRAINING_TEMPLATE_LLAMA2",
		5: "TRAINING_TEMPLATE_FALCON",
		6: "TRAINING_TEMPLATE_INTERNLM",
		7: "TRAINING_TEMPLATE_STARCODER",
	}
	TrainingTemplate_value = map[string]int32{
		"TRAINING_TEMPLATE_UNSPECIFIED": 0,
		"TRAINING_TEMPLATE_BLOOM":       1,
		"TRAINING_TEMPLATE_CHATGLM2":    2,
		"TRAINING_TEMPLATE_LLAMA":       3,
		"TRAINING_TEMPLATE_LLAMA2":      4,
		"TRAINING_TEMPLATE_FALCON":      5,
		"TRAINING_TEMPLATE_INTERNLM":    6,
		"TRAINING_TEMPLATE_STARCODER":   7,
	}
)

func (x TrainingTemplate) Enum() *TrainingTemplate {
	p := new(TrainingTemplate)
	*p = x
	return p
}

func (x TrainingTemplate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainingTemplate) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[4].Descriptor()
}

func (TrainingTemplate) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[4]
}

func (x TrainingTemplate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainingTemplate.Descriptor instead.
func (TrainingTemplate) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{4}
}

// ModelType 为模型的存储类型
type ModelType int32

const (
	ModelType_MODEL_TYPE_UNSPECIFIED ModelType = 0 // 未知
	ModelType_MODEL_TYPE_FILE        ModelType = 1 // 原子文件模型
	ModelType_MODEL_TYPE_IMAGE       ModelType = 2 // 原子镜像模型
	ModelType_MODEL_TYPE_ENSEMBLE    ModelType = 3 // 组合文件模型
)

// Enum value maps for ModelType.
var (
	ModelType_name = map[int32]string{
		0: "MODEL_TYPE_UNSPECIFIED",
		1: "MODEL_TYPE_FILE",
		2: "MODEL_TYPE_IMAGE",
		3: "MODEL_TYPE_ENSEMBLE",
	}
	ModelType_value = map[string]int32{
		"MODEL_TYPE_UNSPECIFIED": 0,
		"MODEL_TYPE_FILE":        1,
		"MODEL_TYPE_IMAGE":       2,
		"MODEL_TYPE_ENSEMBLE":    3,
	}
)

func (x ModelType) Enum() *ModelType {
	p := new(ModelType)
	*p = x
	return p
}

func (x ModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[5].Descriptor()
}

func (ModelType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[5]
}

func (x ModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelType.Descriptor instead.
func (ModelType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{5}
}

// ModelSubType 为模型存储类型的补充描述，指示了存储子类型
type ModelSubType int32

const (
	ModelSubType_MODEL_SUB_TYPE_UNSPECIFIED ModelSubType = 0
	// 文件模型子类型
	ModelSubType_MODEL_SUB_TYPE_FILE_PMML        ModelSubType = 11
	ModelSubType_MODEL_SUB_TYPE_FILE_ONNX        ModelSubType = 12
	ModelSubType_MODEL_SUB_TYPE_FILE_PYTHON      ModelSubType = 13
	ModelSubType_MODEL_SUB_TYPE_FILE_TRANSFORMER ModelSubType = 14 // 能够使用 HuggingFace transformers 库进行load的预训练模型
	ModelSubType_MODEL_SUB_TYPE_FILE_PEFT        ModelSubType = 15 // 能够使用 HuggingFace peft 库进行load的微调模型
	ModelSubType_MODEL_SUB_TYPE_FILE_PKL         ModelSubType = 16
	ModelSubType_MODEL_SUB_TYPE_FILE_OTHERS      ModelSubType = 17 // 不指定框架，不初始化相关文件
	// 镜像模型子类型
	ModelSubType_MODEL_SUB_TYPE_DOCKER_CUSTOM   ModelSubType = 21 // 用户自定义镜像
	ModelSubType_MODEL_SUB_TYPE_DOCKER_VLAB     ModelSubType = 22 // Vlab 产生的镜像模型
	ModelSubType_MODEL_SUB_TYPE_DOCKER_DISCOVER ModelSubType = 23 // Discover 产生的镜像模型
	// 组合模型子类型
	ModelSubType_MODEL_SUB_TYPE_ENSEMBLE_CVAT   ModelSubType = 31 // 训练平台自动生成
	ModelSubType_MODEL_SUB_TYPE_ENSEMBLE_CUSTOM ModelSubType = 32 // 用户自行上传
	ModelSubType_MODEL_SUB_TYPE_ENSEMBLE_SELDON ModelSubType = 33 // MLops 推理图生成
)

// Enum value maps for ModelSubType.
var (
	ModelSubType_name = map[int32]string{
		0:  "MODEL_SUB_TYPE_UNSPECIFIED",
		11: "MODEL_SUB_TYPE_FILE_PMML",
		12: "MODEL_SUB_TYPE_FILE_ONNX",
		13: "MODEL_SUB_TYPE_FILE_PYTHON",
		14: "MODEL_SUB_TYPE_FILE_TRANSFORMER",
		15: "MODEL_SUB_TYPE_FILE_PEFT",
		16: "MODEL_SUB_TYPE_FILE_PKL",
		17: "MODEL_SUB_TYPE_FILE_OTHERS",
		21: "MODEL_SUB_TYPE_DOCKER_CUSTOM",
		22: "MODEL_SUB_TYPE_DOCKER_VLAB",
		23: "MODEL_SUB_TYPE_DOCKER_DISCOVER",
		31: "MODEL_SUB_TYPE_ENSEMBLE_CVAT",
		32: "MODEL_SUB_TYPE_ENSEMBLE_CUSTOM",
		33: "MODEL_SUB_TYPE_ENSEMBLE_SELDON",
	}
	ModelSubType_value = map[string]int32{
		"MODEL_SUB_TYPE_UNSPECIFIED":      0,
		"MODEL_SUB_TYPE_FILE_PMML":        11,
		"MODEL_SUB_TYPE_FILE_ONNX":        12,
		"MODEL_SUB_TYPE_FILE_PYTHON":      13,
		"MODEL_SUB_TYPE_FILE_TRANSFORMER": 14,
		"MODEL_SUB_TYPE_FILE_PEFT":        15,
		"MODEL_SUB_TYPE_FILE_PKL":         16,
		"MODEL_SUB_TYPE_FILE_OTHERS":      17,
		"MODEL_SUB_TYPE_DOCKER_CUSTOM":    21,
		"MODEL_SUB_TYPE_DOCKER_VLAB":      22,
		"MODEL_SUB_TYPE_DOCKER_DISCOVER":  23,
		"MODEL_SUB_TYPE_ENSEMBLE_CVAT":    31,
		"MODEL_SUB_TYPE_ENSEMBLE_CUSTOM":  32,
		"MODEL_SUB_TYPE_ENSEMBLE_SELDON":  33,
	}
)

func (x ModelSubType) Enum() *ModelSubType {
	p := new(ModelSubType)
	*p = x
	return p
}

func (x ModelSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[6].Descriptor()
}

func (ModelSubType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[6]
}

func (x ModelSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelSubType.Descriptor instead.
func (ModelSubType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{6}
}

type ModelAlgorithm int32

const (
	ModelAlgorithm_MO                           ModelAlgorithm = 0 // 未指定具体算法类型，默认值
	ModelAlgorithm_ALGORITHM_TYPE_XGBOOST       ModelAlgorithm = 1 // XGBoost算法
	ModelAlgorithm_ALGORITHM_TYPE_RANDOM_FOREST ModelAlgorithm = 2 // 随机森林算法
)

// Enum value maps for ModelAlgorithm.
var (
	ModelAlgorithm_name = map[int32]string{
		0: "MO",
		1: "ALGORITHM_TYPE_XGBOOST",
		2: "ALGORITHM_TYPE_RANDOM_FOREST",
	}
	ModelAlgorithm_value = map[string]int32{
		"MO":                           0,
		"ALGORITHM_TYPE_XGBOOST":       1,
		"ALGORITHM_TYPE_RANDOM_FOREST": 2,
	}
)

func (x ModelAlgorithm) Enum() *ModelAlgorithm {
	p := new(ModelAlgorithm)
	*p = x
	return p
}

func (x ModelAlgorithm) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelAlgorithm) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[7].Descriptor()
}

func (ModelAlgorithm) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[7]
}

func (x ModelAlgorithm) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelAlgorithm.Descriptor instead.
func (ModelAlgorithm) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{7}
}

// ModelScheduleMode 模型的调度模式，取决于模型是否会保留上下文信息（也即有无状态）
type ModelScheduleMode int32

const (
	ModelScheduleMode_MODEL_SCHEDULE_MODE_STATELESS ModelScheduleMode = 0 // 无状态模型， 默认值
	ModelScheduleMode_MODEL_SCHEDULE_MODE_STATEFUL  ModelScheduleMode = 1 // 有状态模型，模型请求负载均衡时需进行额外设计
)

// Enum value maps for ModelScheduleMode.
var (
	ModelScheduleMode_name = map[int32]string{
		0: "MODEL_SCHEDULE_MODE_STATELESS",
		1: "MODEL_SCHEDULE_MODE_STATEFUL",
	}
	ModelScheduleMode_value = map[string]int32{
		"MODEL_SCHEDULE_MODE_STATELESS": 0,
		"MODEL_SCHEDULE_MODE_STATEFUL":  1,
	}
)

func (x ModelScheduleMode) Enum() *ModelScheduleMode {
	p := new(ModelScheduleMode)
	*p = x
	return p
}

func (x ModelScheduleMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelScheduleMode) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[8].Descriptor()
}

func (ModelScheduleMode) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[8]
}

func (x ModelScheduleMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelScheduleMode.Descriptor instead.
func (ModelScheduleMode) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{8}
}

// ModelParamFormat 某个输入或输出参数的具体格式
type ModelParamFormat int32

const (
	ModelParamFormat_MODEL_PARAM_FORMAT_NONE         ModelParamFormat = 0 // 参数未指定具体格式. 默认值
	ModelParamFormat_MODEL_PARAM_FORMAT_NHWC         ModelParamFormat = 1 // 图像的HWC三维Tensor格式
	ModelParamFormat_MODEL_PARAM_FORMAT_NCHW         ModelParamFormat = 2 // 图像的CHW三维Tensor格式
	ModelParamFormat_MODEL_PARAM_FORMAT_IMAGE_BINARY ModelParamFormat = 3 // 存放图像的二进制数据，具体解析方式在IMAGE_DESC中定义
	ModelParamFormat_MODEL_PARAM_FORMAT_IMAGE_DESC   ModelParamFormat = 4 // 符合CV模型推理请求的统一规范的json字符串，包含对于二进制图像数据的描述
	ModelParamFormat_MODEL_PARAM_FORMAT_IMAGE_RESULT ModelParamFormat = 5 // 符合CV模型推理结果的统一规范的json字符串
	// NLP对话模型，标准请求格式：
	//
	//	{
	//	  "query": "你叫什么？",
	//	  "history": [
	//	    {"Q":"你是谁？", "A":"我是Solar"},
	//	    {"Q":"猜猜我是谁？", "A":"不猜"}
	//	  ]
	//	}
	//
	// 其中：
	// * query 为当前对话轮次用户输入的内容
	// * history 为用户当前会话的历史对话记录， Q为用户提出的问题或指令， A为模型响应的内容； 数组中对象顺序和当前会话中的问答顺序一致， 按照时间升序排列
	ModelParamFormat_MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ ModelParamFormat = 6
	// NLP对话模型，流式响应格式
	// 以HTTP 的 text/event-stream 格式返回的 无固定结构的 text 文本
	ModelParamFormat_MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP ModelParamFormat = 7
)

// Enum value maps for ModelParamFormat.
var (
	ModelParamFormat_name = map[int32]string{
		0: "MODEL_PARAM_FORMAT_NONE",
		1: "MODEL_PARAM_FORMAT_NHWC",
		2: "MODEL_PARAM_FORMAT_NCHW",
		3: "MODEL_PARAM_FORMAT_IMAGE_BINARY",
		4: "MODEL_PARAM_FORMAT_IMAGE_DESC",
		5: "MODEL_PARAM_FORMAT_IMAGE_RESULT",
		6: "MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ",
		7: "MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP",
	}
	ModelParamFormat_value = map[string]int32{
		"MODEL_PARAM_FORMAT_NONE":                0,
		"MODEL_PARAM_FORMAT_NHWC":                1,
		"MODEL_PARAM_FORMAT_NCHW":                2,
		"MODEL_PARAM_FORMAT_IMAGE_BINARY":        3,
		"MODEL_PARAM_FORMAT_IMAGE_DESC":          4,
		"MODEL_PARAM_FORMAT_IMAGE_RESULT":        5,
		"MODEL_PARAM_FORMAT_NLP_CHAT_JSON_REQ":   6,
		"MODEL_PARAM_FORMAT_NLP_CHAT_STREAM_RSP": 7,
	}
)

func (x ModelParamFormat) Enum() *ModelParamFormat {
	p := new(ModelParamFormat)
	*p = x
	return p
}

func (x ModelParamFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelParamFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[9].Descriptor()
}

func (ModelParamFormat) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[9]
}

func (x ModelParamFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelParamFormat.Descriptor instead.
func (ModelParamFormat) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{9}
}

type ModelReleaseStatus int32

const (
	ModelReleaseStatus_MODEL_RELEASE_STATUS_UNSPECIFIED           ModelReleaseStatus = 0 // 状态未知
	ModelReleaseStatus_MODEL_RELEASE_STATUS_UNEVALUATED           ModelReleaseStatus = 1 // 未评估
	ModelReleaseStatus_MODEL_RELEASE_STATUS_EVALUATING            ModelReleaseStatus = 2 // 评估中
	ModelReleaseStatus_MODEL_RELEASE_STATUS_FAILED_EVALUATION     ModelReleaseStatus = 3 // 评估失败
	ModelReleaseStatus_MODEL_RELEASE_STATUS_SUCCESSFUL_EVALUATION ModelReleaseStatus = 4 // 评估成功
)

// Enum value maps for ModelReleaseStatus.
var (
	ModelReleaseStatus_name = map[int32]string{
		0: "MODEL_RELEASE_STATUS_UNSPECIFIED",
		1: "MODEL_RELEASE_STATUS_UNEVALUATED",
		2: "MODEL_RELEASE_STATUS_EVALUATING",
		3: "MODEL_RELEASE_STATUS_FAILED_EVALUATION",
		4: "MODEL_RELEASE_STATUS_SUCCESSFUL_EVALUATION",
	}
	ModelReleaseStatus_value = map[string]int32{
		"MODEL_RELEASE_STATUS_UNSPECIFIED":           0,
		"MODEL_RELEASE_STATUS_UNEVALUATED":           1,
		"MODEL_RELEASE_STATUS_EVALUATING":            2,
		"MODEL_RELEASE_STATUS_FAILED_EVALUATION":     3,
		"MODEL_RELEASE_STATUS_SUCCESSFUL_EVALUATION": 4,
	}
)

func (x ModelReleaseStatus) Enum() *ModelReleaseStatus {
	p := new(ModelReleaseStatus)
	*p = x
	return p
}

func (x ModelReleaseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelReleaseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[10].Descriptor()
}

func (ModelReleaseStatus) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[10]
}

func (x ModelReleaseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelReleaseStatus.Descriptor instead.
func (ModelReleaseStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{10}
}

// RepoAddrProtocol 统一的文件上传协议
type RepoAddrProtocol int32

const (
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_UNSPECIFIED RepoAddrProtocol = 0
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_SFS         RepoAddrProtocol = 1 // SophonFileSystem 对应 datamgr 的文件管理接口， sfs://autocv-datamgr-service/temp/320cc1c91ed6dfd8b10e81dfdff70ef3/model.onnx
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_DOCKER      RepoAddrProtocol = 2 // 对应 docker images 的 repo tag , e.g. docker://***********/aip/base/metrics-server:v0.3.6
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_S3          RepoAddrProtocol = 3 // 对应 Amazon s3 对象存储接口， e.g. s3://autocv-ossgw-service/public/dir/file
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_NFS         RepoAddrProtocol = 4 // 对应 NFS 文件路径 e.g. nfs://**************/data/temp/file
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_FTP         RepoAddrProtocol = 5 // 对应 FTP 文件路径 e.g. ftp://**************/data/temp/file
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_MODELSCOPE  RepoAddrProtocol = 6 // 对应modelscope协议， e.g. modelscope://model_repo
	RepoAddrProtocol_REPO_ADDR_PROTOCOL_HUGGINGFACE RepoAddrProtocol = 7 //对应huggingface协议， e.g. huggingface://model_repo
)

// Enum value maps for RepoAddrProtocol.
var (
	RepoAddrProtocol_name = map[int32]string{
		0: "REPO_ADDR_PROTOCOL_UNSPECIFIED",
		1: "REPO_ADDR_PROTOCOL_SFS",
		2: "REPO_ADDR_PROTOCOL_DOCKER",
		3: "REPO_ADDR_PROTOCOL_S3",
		4: "REPO_ADDR_PROTOCOL_NFS",
		5: "REPO_ADDR_PROTOCOL_FTP",
		6: "REPO_ADDR_PROTOCOL_MODELSCOPE",
		7: "REPO_ADDR_PROTOCOL_HUGGINGFACE",
	}
	RepoAddrProtocol_value = map[string]int32{
		"REPO_ADDR_PROTOCOL_UNSPECIFIED": 0,
		"REPO_ADDR_PROTOCOL_SFS":         1,
		"REPO_ADDR_PROTOCOL_DOCKER":      2,
		"REPO_ADDR_PROTOCOL_S3":          3,
		"REPO_ADDR_PROTOCOL_NFS":         4,
		"REPO_ADDR_PROTOCOL_FTP":         5,
		"REPO_ADDR_PROTOCOL_MODELSCOPE":  6,
		"REPO_ADDR_PROTOCOL_HUGGINGFACE": 7,
	}
)

func (x RepoAddrProtocol) Enum() *RepoAddrProtocol {
	p := new(RepoAddrProtocol)
	*p = x
	return p
}

func (x RepoAddrProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepoAddrProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[11].Descriptor()
}

func (RepoAddrProtocol) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[11]
}

func (x RepoAddrProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepoAddrProtocol.Descriptor instead.
func (RepoAddrProtocol) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{11}
}

// EnsembleSubModelType
type SubModelType int32

const (
	SubModelType_SUB_MODEL_TYPE_UNSPECIFIED     SubModelType = 0
	SubModelType_SUB_MODEL_TYPE_ATOM_MODEL      SubModelType = 1
	SubModelType_SUB_MODEL_TYPE_ENSEMBLE_CONFIG SubModelType = 2
	SubModelType_SUB_MODEL_TYPE_PYTHON_CODE     SubModelType = 3
)

// Enum value maps for SubModelType.
var (
	SubModelType_name = map[int32]string{
		0: "SUB_MODEL_TYPE_UNSPECIFIED",
		1: "SUB_MODEL_TYPE_ATOM_MODEL",
		2: "SUB_MODEL_TYPE_ENSEMBLE_CONFIG",
		3: "SUB_MODEL_TYPE_PYTHON_CODE",
	}
	SubModelType_value = map[string]int32{
		"SUB_MODEL_TYPE_UNSPECIFIED":     0,
		"SUB_MODEL_TYPE_ATOM_MODEL":      1,
		"SUB_MODEL_TYPE_ENSEMBLE_CONFIG": 2,
		"SUB_MODEL_TYPE_PYTHON_CODE":     3,
	}
)

func (x SubModelType) Enum() *SubModelType {
	p := new(SubModelType)
	*p = x
	return p
}

func (x SubModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[12].Descriptor()
}

func (SubModelType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[12]
}

func (x SubModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubModelType.Descriptor instead.
func (SubModelType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{12}
}

// ModelEnsembleMode 组装模型的组合方式
type ModelEnsembleMode int32

const (
	ModelEnsembleMode_MODEL_ENSEMBLE_MODE_UNSPECIFIED ModelEnsembleMode = 0
	ModelEnsembleMode_MODEL_ENSEMBLE_MODE_ENSEMBLE    ModelEnsembleMode = 1 // Ensemble 模式，用户通过一个特殊的模型中的配置，串联各个模型的输入与输出，实现模型组合。
	ModelEnsembleMode_MODEL_ENSEMBLE_MODE_BLS         ModelEnsembleMode = 2 // BLS(Business Logic Scripting，业务逻辑脚本) 模式， 用户通过自定义逻辑实现循环、条件、数据相关的控制流和其他模型执行的组合。
)

// Enum value maps for ModelEnsembleMode.
var (
	ModelEnsembleMode_name = map[int32]string{
		0: "MODEL_ENSEMBLE_MODE_UNSPECIFIED",
		1: "MODEL_ENSEMBLE_MODE_ENSEMBLE",
		2: "MODEL_ENSEMBLE_MODE_BLS",
	}
	ModelEnsembleMode_value = map[string]int32{
		"MODEL_ENSEMBLE_MODE_UNSPECIFIED": 0,
		"MODEL_ENSEMBLE_MODE_ENSEMBLE":    1,
		"MODEL_ENSEMBLE_MODE_BLS":         2,
	}
)

func (x ModelEnsembleMode) Enum() *ModelEnsembleMode {
	p := new(ModelEnsembleMode)
	*p = x
	return p
}

func (x ModelEnsembleMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelEnsembleMode) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[13].Descriptor()
}

func (ModelEnsembleMode) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[13]
}

func (x ModelEnsembleMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelEnsembleMode.Descriptor instead.
func (ModelEnsembleMode) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{13}
}

type FileType int32

const (
	FileType_FILE_TYPE_UNSPECIFIED FileType = 0
	// 文本类型文件
	FileType_FILE_TYPE_PBTXT  FileType = 1
	FileType_FILE_TYPE_PYTHON FileType = 2
	FileType_FILE_TYPE_TXT    FileType = 3
	FileType_FILE_TYPE_JSON   FileType = 4
	FileType_FILE_TYPE_YAML   FileType = 5
	FileType_FILE_TYPE_CSV    FileType = 6
	// 二进制类型文件
	FileType_FILE_TYPE_PMML FileType = 11
	FileType_FILE_TYPE_ONNX FileType = 12
	FileType_FILE_TYPE_PKL  FileType = 13
	// 通用类别
	FileType_FILE_TYPE_GENERIC FileType = 100
)

// Enum value maps for FileType.
var (
	FileType_name = map[int32]string{
		0:   "FILE_TYPE_UNSPECIFIED",
		1:   "FILE_TYPE_PBTXT",
		2:   "FILE_TYPE_PYTHON",
		3:   "FILE_TYPE_TXT",
		4:   "FILE_TYPE_JSON",
		5:   "FILE_TYPE_YAML",
		6:   "FILE_TYPE_CSV",
		11:  "FILE_TYPE_PMML",
		12:  "FILE_TYPE_ONNX",
		13:  "FILE_TYPE_PKL",
		100: "FILE_TYPE_GENERIC",
	}
	FileType_value = map[string]int32{
		"FILE_TYPE_UNSPECIFIED": 0,
		"FILE_TYPE_PBTXT":       1,
		"FILE_TYPE_PYTHON":      2,
		"FILE_TYPE_TXT":         3,
		"FILE_TYPE_JSON":        4,
		"FILE_TYPE_YAML":        5,
		"FILE_TYPE_CSV":         6,
		"FILE_TYPE_PMML":        11,
		"FILE_TYPE_ONNX":        12,
		"FILE_TYPE_PKL":         13,
		"FILE_TYPE_GENERIC":     100,
	}
)

func (x FileType) Enum() *FileType {
	p := new(FileType)
	*p = x
	return p
}

func (x FileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[14].Descriptor()
}

func (FileType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[14]
}

func (x FileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileType.Descriptor instead.
func (FileType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{14}
}

// DeploymentType 描述部署类型，现在模型评估需要启动模型，模型体验需要启动模型，模型服务也启动模型，但其中前两项不显示在模型部署（服务）列表中
// 根据部署类型进行筛选
type DeploymentType int32

const (
	DeploymentType_DEPLOYMENT_TYPE_UNSPECIFIED DeploymentType = 0
	DeploymentType_DEPLOYMENT_TYPE_SERVICE     DeploymentType = 1 // 模型服务，显示在列表中
	DeploymentType_DEPLOYMENT_TYPE_EVALUATION  DeploymentType = 2 // 评估启动的模型部署，不显示在模型服务列表中
	DeploymentType_DEPLOYMENT_TYPE_TEST        DeploymentType = 3 // 模型体验启动的模型部署 不显示在模型服务列表中
)

// Enum value maps for DeploymentType.
var (
	DeploymentType_name = map[int32]string{
		0: "DEPLOYMENT_TYPE_UNSPECIFIED",
		1: "DEPLOYMENT_TYPE_SERVICE",
		2: "DEPLOYMENT_TYPE_EVALUATION",
		3: "DEPLOYMENT_TYPE_TEST",
	}
	DeploymentType_value = map[string]int32{
		"DEPLOYMENT_TYPE_UNSPECIFIED": 0,
		"DEPLOYMENT_TYPE_SERVICE":     1,
		"DEPLOYMENT_TYPE_EVALUATION":  2,
		"DEPLOYMENT_TYPE_TEST":        3,
	}
)

func (x DeploymentType) Enum() *DeploymentType {
	p := new(DeploymentType)
	*p = x
	return p
}

func (x DeploymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeploymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[15].Descriptor()
}

func (DeploymentType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[15]
}

func (x DeploymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeploymentType.Descriptor instead.
func (DeploymentType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{15}
}

// NodeRole 集群中节点角色
type NodeRole int32

const (
	NodeRole_NODE_ROLE_UNSPECIFIED NodeRole = 0
	NodeRole_NODE_ROLE_MASTER      NodeRole = 1 // 集群管理节点
	NodeRole_NODE_ROLE_WORKER      NodeRole = 2 // 集群工作节点
	NodeRole_NODE_ROLE_EVAL        NodeRole = 3 // 可用于模型评估任务
	NodeRole_NODE_ROLE_TEST        NodeRole = 4 // 可用于模型体验任务
	NodeRole_NODE_ROLE_DEPLOY      NodeRole = 5 // 可用于模型部署任务
)

// Enum value maps for NodeRole.
var (
	NodeRole_name = map[int32]string{
		0: "NODE_ROLE_UNSPECIFIED",
		1: "NODE_ROLE_MASTER",
		2: "NODE_ROLE_WORKER",
		3: "NODE_ROLE_EVAL",
		4: "NODE_ROLE_TEST",
		5: "NODE_ROLE_DEPLOY",
	}
	NodeRole_value = map[string]int32{
		"NODE_ROLE_UNSPECIFIED": 0,
		"NODE_ROLE_MASTER":      1,
		"NODE_ROLE_WORKER":      2,
		"NODE_ROLE_EVAL":        3,
		"NODE_ROLE_TEST":        4,
		"NODE_ROLE_DEPLOY":      5,
	}
)

func (x NodeRole) Enum() *NodeRole {
	p := new(NodeRole)
	*p = x
	return p
}

func (x NodeRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeRole) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[16].Descriptor()
}

func (NodeRole) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[16]
}

func (x NodeRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeRole.Descriptor instead.
func (NodeRole) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{16}
}

// CPUArch 可能会出现的CPU架构
type CPUArch int32

const (
	CPUArch_CPU_ARCH_UNSPECIFIED CPUArch = 0 // 模型硬件适配信息未知
	CPUArch_CPU_ARCH_AMD64       CPUArch = 1 // 模型仅可运行在CPU架构为 amd64 (aka: x86_64) 的硬件上
	CPUArch_CPU_ARCH_ARM64       CPUArch = 2 // 模型仅可运行在CPU架构 arm64 (aka: aarch64, arm64/v8) 的硬件上
	CPUArch_CPU_ARCH_MULTI       CPUArch = 3 // 模型与硬件架构无关
)

// Enum value maps for CPUArch.
var (
	CPUArch_name = map[int32]string{
		0: "CPU_ARCH_UNSPECIFIED",
		1: "CPU_ARCH_AMD64",
		2: "CPU_ARCH_ARM64",
		3: "CPU_ARCH_MULTI",
	}
	CPUArch_value = map[string]int32{
		"CPU_ARCH_UNSPECIFIED": 0,
		"CPU_ARCH_AMD64":       1,
		"CPU_ARCH_ARM64":       2,
		"CPU_ARCH_MULTI":       3,
	}
)

func (x CPUArch) Enum() *CPUArch {
	p := new(CPUArch)
	*p = x
	return p
}

func (x CPUArch) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CPUArch) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[17].Descriptor()
}

func (CPUArch) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[17]
}

func (x CPUArch) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CPUArch.Descriptor instead.
func (CPUArch) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{17}
}

// AcceleratorType 可能出现的硬件加速类型
type AcceleratorType int32

const (
	AcceleratorType_ACCELERATOR_TYPE_UNSPECIFIED AcceleratorType = 0 // 硬件加速相关信息未知
	AcceleratorType_ACCELERATOR_TYPE_NONE        AcceleratorType = 1 // 不支持硬件加速
	AcceleratorType_ACCELERATOR_TYPE_GPU         AcceleratorType = 2 // 英伟达显卡系列
	AcceleratorType_ACCELERATOR_TYPE_ATLAS       AcceleratorType = 3 // 华为昇腾系列
	AcceleratorType_ACCELERATOR_TYPE_MLU         AcceleratorType = 4 // 寒武纪MLU系列
	AcceleratorType_ACCELERATOR_TYPE_JETSON      AcceleratorType = 5 // 英伟达边缘推理盒子Jetson系列
	AcceleratorType_ACCELERATOR_TYPE_VAAPI       AcceleratorType = 6 // 英特尔开源硬件视频加速接口（Video Acceleration API）
)

// Enum value maps for AcceleratorType.
var (
	AcceleratorType_name = map[int32]string{
		0: "ACCELERATOR_TYPE_UNSPECIFIED",
		1: "ACCELERATOR_TYPE_NONE",
		2: "ACCELERATOR_TYPE_GPU",
		3: "ACCELERATOR_TYPE_ATLAS",
		4: "ACCELERATOR_TYPE_MLU",
		5: "ACCELERATOR_TYPE_JETSON",
		6: "ACCELERATOR_TYPE_VAAPI",
	}
	AcceleratorType_value = map[string]int32{
		"ACCELERATOR_TYPE_UNSPECIFIED": 0,
		"ACCELERATOR_TYPE_NONE":        1,
		"ACCELERATOR_TYPE_GPU":         2,
		"ACCELERATOR_TYPE_ATLAS":       3,
		"ACCELERATOR_TYPE_MLU":         4,
		"ACCELERATOR_TYPE_JETSON":      5,
		"ACCELERATOR_TYPE_VAAPI":       6,
	}
)

func (x AcceleratorType) Enum() *AcceleratorType {
	p := new(AcceleratorType)
	*p = x
	return p
}

func (x AcceleratorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcceleratorType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[18].Descriptor()
}

func (AcceleratorType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[18]
}

func (x AcceleratorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcceleratorType.Descriptor instead.
func (AcceleratorType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{18}
}

type ModelRuntimeType int32

const (
	ModelRuntimeType_MODEL_RUNTIME_TYPE_UNSPECIFIED ModelRuntimeType = 0
	ModelRuntimeType_MODEL_RUNTIME_TYPE_IMAGE       ModelRuntimeType = 1 // 镜像模型的默认运行时
	ModelRuntimeType_MODEL_RUNTIME_TYPE_DLIE_POD    ModelRuntimeType = 2 // DLIE模型的默认运行时（每个模型一个DLIE Server Pod的形式）
	ModelRuntimeType_MODEL_RUNTIME_TYPE_DLIE_PROC   ModelRuntimeType = 3 // DLIE模型的默认运行时（高性能模式, 每个模型一个DLIE Server Process的形式）
	ModelRuntimeType_MODEL_RUNTIME_TYPE_PMML        ModelRuntimeType = 4 // 专门启动pmml模型的runtime
)

// Enum value maps for ModelRuntimeType.
var (
	ModelRuntimeType_name = map[int32]string{
		0: "MODEL_RUNTIME_TYPE_UNSPECIFIED",
		1: "MODEL_RUNTIME_TYPE_IMAGE",
		2: "MODEL_RUNTIME_TYPE_DLIE_POD",
		3: "MODEL_RUNTIME_TYPE_DLIE_PROC",
		4: "MODEL_RUNTIME_TYPE_PMML",
	}
	ModelRuntimeType_value = map[string]int32{
		"MODEL_RUNTIME_TYPE_UNSPECIFIED": 0,
		"MODEL_RUNTIME_TYPE_IMAGE":       1,
		"MODEL_RUNTIME_TYPE_DLIE_POD":    2,
		"MODEL_RUNTIME_TYPE_DLIE_PROC":   3,
		"MODEL_RUNTIME_TYPE_PMML":        4,
	}
)

func (x ModelRuntimeType) Enum() *ModelRuntimeType {
	p := new(ModelRuntimeType)
	*p = x
	return p
}

func (x ModelRuntimeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelRuntimeType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[19].Descriptor()
}

func (ModelRuntimeType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[19]
}

func (x ModelRuntimeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelRuntimeType.Descriptor instead.
func (ModelRuntimeType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{19}
}

type ModelServiceSchema int32

const (
	ModelServiceSchema_MODEL_SERVICE_SCHEMA_UNSPECIFIED ModelServiceSchema = 0
	ModelServiceSchema_MODEL_SERVICE_SCHEMA_HTTP        ModelServiceSchema = 1
	ModelServiceSchema_MODEL_SERVICE_SCHEMA_DLIE        ModelServiceSchema = 2
	ModelServiceSchema_MODEL_SERVICE_SCHEMA_SELDON      ModelServiceSchema = 3
)

// Enum value maps for ModelServiceSchema.
var (
	ModelServiceSchema_name = map[int32]string{
		0: "MODEL_SERVICE_SCHEMA_UNSPECIFIED",
		1: "MODEL_SERVICE_SCHEMA_HTTP",
		2: "MODEL_SERVICE_SCHEMA_DLIE",
		3: "MODEL_SERVICE_SCHEMA_SELDON",
	}
	ModelServiceSchema_value = map[string]int32{
		"MODEL_SERVICE_SCHEMA_UNSPECIFIED": 0,
		"MODEL_SERVICE_SCHEMA_HTTP":        1,
		"MODEL_SERVICE_SCHEMA_DLIE":        2,
		"MODEL_SERVICE_SCHEMA_SELDON":      3,
	}
)

func (x ModelServiceSchema) Enum() *ModelServiceSchema {
	p := new(ModelServiceSchema)
	*p = x
	return p
}

func (x ModelServiceSchema) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelServiceSchema) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[20].Descriptor()
}

func (ModelServiceSchema) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[20]
}

func (x ModelServiceSchema) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelServiceSchema.Descriptor instead.
func (ModelServiceSchema) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{20}
}

type ModelServiceType int32

const (
	ModelServiceType_MODEL_SERVICE_TYPE_UNSPECIFIED ModelServiceType = 0
	ModelServiceType_MODEL_SERVICE_TYPE_LOCAL       ModelServiceType = 1
	ModelServiceType_MODEL_SERVICE_TYPE_REMOTE      ModelServiceType = 2
	ModelServiceType_MODEL_SERVICE_TYPE_CUSTOM      ModelServiceType = 3
)

// Enum value maps for ModelServiceType.
var (
	ModelServiceType_name = map[int32]string{
		0: "MODEL_SERVICE_TYPE_UNSPECIFIED",
		1: "MODEL_SERVICE_TYPE_LOCAL",
		2: "MODEL_SERVICE_TYPE_REMOTE",
		3: "MODEL_SERVICE_TYPE_CUSTOM",
	}
	ModelServiceType_value = map[string]int32{
		"MODEL_SERVICE_TYPE_UNSPECIFIED": 0,
		"MODEL_SERVICE_TYPE_LOCAL":       1,
		"MODEL_SERVICE_TYPE_REMOTE":      2,
		"MODEL_SERVICE_TYPE_CUSTOM":      3,
	}
)

func (x ModelServiceType) Enum() *ModelServiceType {
	p := new(ModelServiceType)
	*p = x
	return p
}

func (x ModelServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[21].Descriptor()
}

func (ModelServiceType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[21]
}

func (x ModelServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelServiceType.Descriptor instead.
func (ModelServiceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{21}
}

type ModelServiceInvokeMethod int32

const (
	ModelServiceInvokeMethod_MODEL_SERVICE_INVOKE_METHOD_SYNC   ModelServiceInvokeMethod = 0
	ModelServiceInvokeMethod_MODEL_SERVICE_INVOKE_METHOD_STREAM ModelServiceInvokeMethod = 1
)

// Enum value maps for ModelServiceInvokeMethod.
var (
	ModelServiceInvokeMethod_name = map[int32]string{
		0: "MODEL_SERVICE_INVOKE_METHOD_SYNC",
		1: "MODEL_SERVICE_INVOKE_METHOD_STREAM",
	}
	ModelServiceInvokeMethod_value = map[string]int32{
		"MODEL_SERVICE_INVOKE_METHOD_SYNC":   0,
		"MODEL_SERVICE_INVOKE_METHOD_STREAM": 1,
	}
)

func (x ModelServiceInvokeMethod) Enum() *ModelServiceInvokeMethod {
	p := new(ModelServiceInvokeMethod)
	*p = x
	return p
}

func (x ModelServiceInvokeMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelServiceInvokeMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[22].Descriptor()
}

func (ModelServiceInvokeMethod) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[22]
}

func (x ModelServiceInvokeMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelServiceInvokeMethod.Descriptor instead.
func (ModelServiceInvokeMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{22}
}

type RemoteServiceMethod int32

const (
	RemoteServiceMethod_REMOTE_SERVICE_METHOD_UNSPECIFIED RemoteServiceMethod = 0
	RemoteServiceMethod_REMOTE_SERVICE_METHOD_GET         RemoteServiceMethod = 1
	RemoteServiceMethod_REMOTE_SERVICE_METHOD_POST        RemoteServiceMethod = 2
)

// Enum value maps for RemoteServiceMethod.
var (
	RemoteServiceMethod_name = map[int32]string{
		0: "REMOTE_SERVICE_METHOD_UNSPECIFIED",
		1: "REMOTE_SERVICE_METHOD_GET",
		2: "REMOTE_SERVICE_METHOD_POST",
	}
	RemoteServiceMethod_value = map[string]int32{
		"REMOTE_SERVICE_METHOD_UNSPECIFIED": 0,
		"REMOTE_SERVICE_METHOD_GET":         1,
		"REMOTE_SERVICE_METHOD_POST":        2,
	}
)

func (x RemoteServiceMethod) Enum() *RemoteServiceMethod {
	p := new(RemoteServiceMethod)
	*p = x
	return p
}

func (x RemoteServiceMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RemoteServiceMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[23].Descriptor()
}

func (RemoteServiceMethod) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[23]
}

func (x RemoteServiceMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RemoteServiceMethod.Descriptor instead.
func (RemoteServiceMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{23}
}

type ProxyScheme int32

const (
	ProxyScheme_PROXY_SCHEME_UNSPECIFIED ProxyScheme = 0
	ProxyScheme_PROXY_SCHEME_HTTP        ProxyScheme = 1
	ProxyScheme_PROXY_SCHEME_HTTPS       ProxyScheme = 2
	ProxyScheme_PROXY_SCHEME_SOCKS4      ProxyScheme = 3
	ProxyScheme_PROXY_SCHEME_SOCKS5      ProxyScheme = 4
)

// Enum value maps for ProxyScheme.
var (
	ProxyScheme_name = map[int32]string{
		0: "PROXY_SCHEME_UNSPECIFIED",
		1: "PROXY_SCHEME_HTTP",
		2: "PROXY_SCHEME_HTTPS",
		3: "PROXY_SCHEME_SOCKS4",
		4: "PROXY_SCHEME_SOCKS5",
	}
	ProxyScheme_value = map[string]int32{
		"PROXY_SCHEME_UNSPECIFIED": 0,
		"PROXY_SCHEME_HTTP":        1,
		"PROXY_SCHEME_HTTPS":       2,
		"PROXY_SCHEME_SOCKS4":      3,
		"PROXY_SCHEME_SOCKS5":      4,
	}
)

func (x ProxyScheme) Enum() *ProxyScheme {
	p := new(ProxyScheme)
	*p = x
	return p
}

func (x ProxyScheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProxyScheme) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[24].Descriptor()
}

func (ProxyScheme) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[24]
}

func (x ProxyScheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProxyScheme.Descriptor instead.
func (ProxyScheme) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{24}
}

type InterfaceSpecName int32

const (
	InterfaceSpecName_INTERFACE_SPEC_OTHERS    InterfaceSpecName = 0
	InterfaceSpecName_INTERFACE_SPEC_OPENAI    InterfaceSpecName = 1
	InterfaceSpecName_INTERFACE_SPEC_TRANSWARP InterfaceSpecName = 3
)

// Enum value maps for InterfaceSpecName.
var (
	InterfaceSpecName_name = map[int32]string{
		0: "INTERFACE_SPEC_OTHERS",
		1: "INTERFACE_SPEC_OPENAI",
		3: "INTERFACE_SPEC_TRANSWARP",
	}
	InterfaceSpecName_value = map[string]int32{
		"INTERFACE_SPEC_OTHERS":    0,
		"INTERFACE_SPEC_OPENAI":    1,
		"INTERFACE_SPEC_TRANSWARP": 3,
	}
)

func (x InterfaceSpecName) Enum() *InterfaceSpecName {
	p := new(InterfaceSpecName)
	*p = x
	return p
}

func (x InterfaceSpecName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InterfaceSpecName) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[25].Descriptor()
}

func (InterfaceSpecName) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[25]
}

func (x InterfaceSpecName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InterfaceSpecName.Descriptor instead.
func (InterfaceSpecName) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{25}
}

type RemoteServiceBuiltInType int32

const (
	RemoteServiceBuiltInType_REMOTE_SERVICE_BUILTIN_TYPE_UNSPECIFIC RemoteServiceBuiltInType = 0
	RemoteServiceBuiltInType_REMOTE_SERVICE_BUILTIN_TYPE_OPENAI     RemoteServiceBuiltInType = 1
	RemoteServiceBuiltInType_REMOTE_SERVICE_BUILTIN_TYPE_AZURE      RemoteServiceBuiltInType = 2
)

// Enum value maps for RemoteServiceBuiltInType.
var (
	RemoteServiceBuiltInType_name = map[int32]string{
		0: "REMOTE_SERVICE_BUILTIN_TYPE_UNSPECIFIC",
		1: "REMOTE_SERVICE_BUILTIN_TYPE_OPENAI",
		2: "REMOTE_SERVICE_BUILTIN_TYPE_AZURE",
	}
	RemoteServiceBuiltInType_value = map[string]int32{
		"REMOTE_SERVICE_BUILTIN_TYPE_UNSPECIFIC": 0,
		"REMOTE_SERVICE_BUILTIN_TYPE_OPENAI":     1,
		"REMOTE_SERVICE_BUILTIN_TYPE_AZURE":      2,
	}
)

func (x RemoteServiceBuiltInType) Enum() *RemoteServiceBuiltInType {
	p := new(RemoteServiceBuiltInType)
	*p = x
	return p
}

func (x RemoteServiceBuiltInType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RemoteServiceBuiltInType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[26].Descriptor()
}

func (RemoteServiceBuiltInType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[26]
}

func (x RemoteServiceBuiltInType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RemoteServiceBuiltInType.Descriptor instead.
func (RemoteServiceBuiltInType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{26}
}

type EvaluationDatasetFromType int32

const (
	EvaluationDatasetFromType_EVALUATION_DATASET_FROM_TYPE_UNSPECIFIED EvaluationDatasetFromType = 0
	EvaluationDatasetFromType_EVALUATION_DATASET_FROM_TYPE_LOCAL       EvaluationDatasetFromType = 1
	EvaluationDatasetFromType_EVALUATION_DATASET_FROM_TYPE_AUTOCV      EvaluationDatasetFromType = 2
)

// Enum value maps for EvaluationDatasetFromType.
var (
	EvaluationDatasetFromType_name = map[int32]string{
		0: "EVALUATION_DATASET_FROM_TYPE_UNSPECIFIED",
		1: "EVALUATION_DATASET_FROM_TYPE_LOCAL",
		2: "EVALUATION_DATASET_FROM_TYPE_AUTOCV",
	}
	EvaluationDatasetFromType_value = map[string]int32{
		"EVALUATION_DATASET_FROM_TYPE_UNSPECIFIED": 0,
		"EVALUATION_DATASET_FROM_TYPE_LOCAL":       1,
		"EVALUATION_DATASET_FROM_TYPE_AUTOCV":      2,
	}
)

func (x EvaluationDatasetFromType) Enum() *EvaluationDatasetFromType {
	p := new(EvaluationDatasetFromType)
	*p = x
	return p
}

func (x EvaluationDatasetFromType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvaluationDatasetFromType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[27].Descriptor()
}

func (EvaluationDatasetFromType) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[27]
}

func (x EvaluationDatasetFromType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvaluationDatasetFromType.Descriptor instead.
func (EvaluationDatasetFromType) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{27}
}

type EvaluationStage int32

const (
	EvaluationStage_EVALUATION_STAGE_UNSPECIFIED      EvaluationStage = 0
	EvaluationStage_EVALUATION_STAGE_PREPARE_DATA     EvaluationStage = 1
	EvaluationStage_EVALUATION_STAGE_INFER            EvaluationStage = 2
	EvaluationStage_EVALUATION_STAGE_MODEL_METRICS    EvaluationStage = 3
	EvaluationStage_EVALUATION_STAGE_INTEGRATE_RESULT EvaluationStage = 4
)

// Enum value maps for EvaluationStage.
var (
	EvaluationStage_name = map[int32]string{
		0: "EVALUATION_STAGE_UNSPECIFIED",
		1: "EVALUATION_STAGE_PREPARE_DATA",
		2: "EVALUATION_STAGE_INFER",
		3: "EVALUATION_STAGE_MODEL_METRICS",
		4: "EVALUATION_STAGE_INTEGRATE_RESULT",
	}
	EvaluationStage_value = map[string]int32{
		"EVALUATION_STAGE_UNSPECIFIED":      0,
		"EVALUATION_STAGE_PREPARE_DATA":     1,
		"EVALUATION_STAGE_INFER":            2,
		"EVALUATION_STAGE_MODEL_METRICS":    3,
		"EVALUATION_STAGE_INTEGRATE_RESULT": 4,
	}
)

func (x EvaluationStage) Enum() *EvaluationStage {
	p := new(EvaluationStage)
	*p = x
	return p
}

func (x EvaluationStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvaluationStage) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_model_proto_enumTypes[28].Descriptor()
}

func (EvaluationStage) Type() protoreflect.EnumType {
	return &file_proto_model_proto_enumTypes[28]
}

func (x EvaluationStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvaluationStage.Descriptor instead.
func (EvaluationStage) EnumDescriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{28}
}

// Model 模型仓库的统一抽象定义，仅包括一些模型的描述信息，用于汇总具有相同功能模型的不同版本
type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                   // 模型ID，后端自动生成
	Name             string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`               // 模型名称，创建后支持修改
	Domain           *ModelDomain     `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`           // 模型领域信息，标识模型的场景、储存方式、是否有状态
	Detail           *ModelDetail     `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`           // 模型详情
	Stats            *ModelStats      `protobuf:"bytes,5,opt,name=stats,proto3" json:"stats,omitempty"`             // 模型的版本/部署统计信息，实时进行统计
	Apis             []*ModelApi      `protobuf:"bytes,6,rep,name=apis,proto3" json:"apis,omitempty"`               // 模型提供的接口信息，限制模型版本间API保持一致
	Attachments      []*Attachment    `protobuf:"bytes,7,rep,name=attachments,proto3" json:"attachments,omitempty"` // 模型附件信息，包括模型的使用说明，评估报告等各种相关文件、链接等
	TrainingTemplate TrainingTemplate `protobuf:"varint,8,opt,name=training_template,json=trainingTemplate,proto3,enum=proto.TrainingTemplate" json:"training_template,omitempty"`
	ProjectId        string           `protobuf:"bytes,9,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	AssetType        AssetType        `protobuf:"varint,10,opt,name=asset_type,json=assetType,proto3,enum=proto.AssetType" json:"asset_type,omitempty"` // 模型资产的类型，分为： 内嵌模型和共享模型
	SourceProjectId  string           `protobuf:"bytes,11,opt,name=source_project_id,json=sourceProjectId,proto3" json:"source_project_id,omitempty"`
	IsSystemInit     bool             `protobuf:"varint,14,opt,name=is_system_init,json=isSystemInit,proto3" json:"is_system_init,omitempty"` //是否为系统初始化的模型
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{0}
}

func (x *Model) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Model) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Model) GetDomain() *ModelDomain {
	if x != nil {
		return x.Domain
	}
	return nil
}

func (x *Model) GetDetail() *ModelDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *Model) GetStats() *ModelStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *Model) GetApis() []*ModelApi {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *Model) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *Model) GetTrainingTemplate() TrainingTemplate {
	if x != nil {
		return x.TrainingTemplate
	}
	return TrainingTemplate_TRAINING_TEMPLATE_UNSPECIFIED
}

func (x *Model) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Model) GetAssetType() AssetType {
	if x != nil {
		return x.AssetType
	}
	return AssetType_ASSET_SHARED
}

func (x *Model) GetSourceProjectId() string {
	if x != nil {
		return x.SourceProjectId
	}
	return ""
}

func (x *Model) GetIsSystemInit() bool {
	if x != nil {
		return x.IsSystemInit
	}
	return false
}

type SpaceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IndustryLabels map[string]bool `protobuf:"bytes,1,rep,name=industry_labels,json=industryLabels,proto3" json:"industry_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 空间机构相对应的行业标签
	IsSelected     map[string]bool `protobuf:"bytes,2,rep,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`             // 是否被选中为某行业的首页精选
}

func (x *SpaceInfo) Reset() {
	*x = SpaceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpaceInfo) ProtoMessage() {}

func (x *SpaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpaceInfo.ProtoReflect.Descriptor instead.
func (*SpaceInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{1}
}

func (x *SpaceInfo) GetIndustryLabels() map[string]bool {
	if x != nil {
		return x.IndustryLabels
	}
	return nil
}

func (x *SpaceInfo) GetIsSelected() map[string]bool {
	if x != nil {
		return x.IsSelected
	}
	return nil
}

type SetSelectedReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Industry string   `protobuf:"bytes,1,opt,name=industry,proto3" json:"industry,omitempty"` // 行业
	Ids      []string `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`           // ids
}

func (x *SetSelectedReq) Reset() {
	*x = SetSelectedReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSelectedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSelectedReq) ProtoMessage() {}

func (x *SetSelectedReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSelectedReq.ProtoReflect.Descriptor instead.
func (*SetSelectedReq) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{2}
}

func (x *SetSelectedReq) GetIndustry() string {
	if x != nil {
		return x.Industry
	}
	return ""
}

func (x *SetSelectedReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

// ModelStats 模型的统计信息（查询时动态生成，不存数据库）
type ModelStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LatestRelease   *ModelReleaseBase   `protobuf:"bytes,1,opt,name=latest_release,json=latestRelease,proto3" json:"latest_release,omitempty"`       // 模型的最新版本
	BaselineRelease *ModelReleaseBase   `protobuf:"bytes,2,opt,name=baseline_release,json=baselineRelease,proto3" json:"baseline_release,omitempty"` // 模型的基线版本
	ReleaseCount    int32               `protobuf:"varint,3,opt,name=release_count,json=releaseCount,proto3" json:"release_count,omitempty"`         // 模型版本数量
	ReleasesInfo    []*ModelReleaseBase `protobuf:"bytes,4,rep,name=releases_info,json=releasesInfo,proto3" json:"releases_info,omitempty"`          // 模型版本的基础信息
	UsageCount      *ModelUsageCount    `protobuf:"bytes,5,opt,name=usage_count,json=usageCount,proto3" json:"usage_count,omitempty"`
	DiskUsage       float32             `protobuf:"fixed32,6,opt,name=disk_usage,json=diskUsage,proto3" json:"disk_usage,omitempty"`
}

func (x *ModelStats) Reset() {
	*x = ModelStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStats) ProtoMessage() {}

func (x *ModelStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStats.ProtoReflect.Descriptor instead.
func (*ModelStats) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{3}
}

func (x *ModelStats) GetLatestRelease() *ModelReleaseBase {
	if x != nil {
		return x.LatestRelease
	}
	return nil
}

func (x *ModelStats) GetBaselineRelease() *ModelReleaseBase {
	if x != nil {
		return x.BaselineRelease
	}
	return nil
}

func (x *ModelStats) GetReleaseCount() int32 {
	if x != nil {
		return x.ReleaseCount
	}
	return 0
}

func (x *ModelStats) GetReleasesInfo() []*ModelReleaseBase {
	if x != nil {
		return x.ReleasesInfo
	}
	return nil
}

func (x *ModelStats) GetUsageCount() *ModelUsageCount {
	if x != nil {
		return x.UsageCount
	}
	return nil
}

func (x *ModelStats) GetDiskUsage() float32 {
	if x != nil {
		return x.DiskUsage
	}
	return 0
}

// ModelUsageCount 模型使用的计数信息，这些计数可以通过接口更新
type ModelUsageCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploysCount     int32  `protobuf:"varint,1,opt,name=deploys_count,json=deploysCount,proto3" json:"deploys_count,omitempty"`             // 模型部署数量
	ViewsCount       int32  `protobuf:"varint,2,opt,name=views_count,json=viewsCount,proto3" json:"views_count,omitempty"`                   // 模型访问量，打开模型详情,或者进行体验时计数增加
	DownloadsCount   int32  `protobuf:"varint,3,opt,name=downloads_count,json=downloadsCount,proto3" json:"downloads_count,omitempty"`       // 模型下载量, 使用模型clone时计数增加
	InvokesCount     int32  `protobuf:"varint,4,opt,name=invokes_count,json=invokesCount,proto3" json:"invokes_count,omitempty"`             // 模型推理调用计数
	TrainingsCount   int32  `protobuf:"varint,5,opt,name=trainings_count,json=trainingsCount,proto3" json:"trainings_count,omitempty"`       // 模型训练使用计数
	EvaluationsCount int32  `protobuf:"varint,6,opt,name=evaluations_count,json=evaluationsCount,proto3" json:"evaluations_count,omitempty"` // 模型评估使用计数
	ModelId          string `protobuf:"bytes,7,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *ModelUsageCount) Reset() {
	*x = ModelUsageCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelUsageCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelUsageCount) ProtoMessage() {}

func (x *ModelUsageCount) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelUsageCount.ProtoReflect.Descriptor instead.
func (*ModelUsageCount) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{4}
}

func (x *ModelUsageCount) GetDeploysCount() int32 {
	if x != nil {
		return x.DeploysCount
	}
	return 0
}

func (x *ModelUsageCount) GetViewsCount() int32 {
	if x != nil {
		return x.ViewsCount
	}
	return 0
}

func (x *ModelUsageCount) GetDownloadsCount() int32 {
	if x != nil {
		return x.DownloadsCount
	}
	return 0
}

func (x *ModelUsageCount) GetInvokesCount() int32 {
	if x != nil {
		return x.InvokesCount
	}
	return 0
}

func (x *ModelUsageCount) GetTrainingsCount() int32 {
	if x != nil {
		return x.TrainingsCount
	}
	return 0
}

func (x *ModelUsageCount) GetEvaluationsCount() int32 {
	if x != nil {
		return x.EvaluationsCount
	}
	return 0
}

func (x *ModelUsageCount) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

// DataModality 模型数据类型定义
type DataModality struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind    ModelKind    `protobuf:"varint,1,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`                         // 数据模态
	SubKind ModelSubKind `protobuf:"varint,2,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"` // 任务类型
}

func (x *DataModality) Reset() {
	*x = DataModality{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataModality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataModality) ProtoMessage() {}

func (x *DataModality) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataModality.ProtoReflect.Descriptor instead.
func (*DataModality) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{5}
}

func (x *DataModality) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *DataModality) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

// ModelDomain 定义了模型的分类信息, 包括存储形式（type）/ 模型功能场景（kind） 两个维度
// TODO 后续可能需要新增字段标识具体的算法类型，e.g. Yolo, LoRA, BERT etc.
type ModelDomain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind          ModelKind         `protobuf:"varint,1,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`                                             // 输入的数据模态
	SubKind       ModelSubKind      `protobuf:"varint,2,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`                     // 输入的任务类型
	Type          ModelType         `protobuf:"varint,3,opt,name=type,proto3,enum=proto.ModelType" json:"type,omitempty"`                                             // 模型类型，[文件、镜像、组合]
	Subtype       ModelSubType      `protobuf:"varint,4,opt,name=subtype,proto3,enum=proto.ModelSubType" json:"subtype,omitempty"`                                    // 模型子类型，[onnx, pmml, py]
	ScheduleMode  ModelScheduleMode `protobuf:"varint,5,opt,name=schedule_mode,json=scheduleMode,proto3,enum=proto.ModelScheduleMode" json:"schedule_mode,omitempty"` // 模型调度方式
	OutputKind    ModelKind         `protobuf:"varint,6,opt,name=output_kind,json=outputKind,proto3,enum=proto.ModelKind" json:"output_kind,omitempty"`               // 组合模型的[输出的数据模态]
	OutputSubKind ModelSubKind      `protobuf:"varint,7,opt,name=output_sub_kind,json=outputSubKind,proto3,enum=proto.ModelSubKind" json:"output_sub_kind,omitempty"` // 组合模型的[输出的任务类型]
	Algorithm     ModelAlgorithm    `protobuf:"varint,8,opt,name=algorithm,proto3,enum=proto.ModelAlgorithm" json:"algorithm,omitempty"`                              // 模型所用算法类型
	Components    []*DataModality   `protobuf:"bytes,9,rep,name=components,proto3" json:"components,omitempty"`                                                       // 组合模型中所含原子模型的任务类型的列表
}

func (x *ModelDomain) Reset() {
	*x = ModelDomain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDomain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDomain) ProtoMessage() {}

func (x *ModelDomain) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDomain.ProtoReflect.Descriptor instead.
func (*ModelDomain) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{6}
}

func (x *ModelDomain) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *ModelDomain) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ModelDomain) GetType() ModelType {
	if x != nil {
		return x.Type
	}
	return ModelType_MODEL_TYPE_UNSPECIFIED
}

func (x *ModelDomain) GetSubtype() ModelSubType {
	if x != nil {
		return x.Subtype
	}
	return ModelSubType_MODEL_SUB_TYPE_UNSPECIFIED
}

func (x *ModelDomain) GetScheduleMode() ModelScheduleMode {
	if x != nil {
		return x.ScheduleMode
	}
	return ModelScheduleMode_MODEL_SCHEDULE_MODE_STATELESS
}

func (x *ModelDomain) GetOutputKind() ModelKind {
	if x != nil {
		return x.OutputKind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *ModelDomain) GetOutputSubKind() ModelSubKind {
	if x != nil {
		return x.OutputSubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ModelDomain) GetAlgorithm() ModelAlgorithm {
	if x != nil {
		return x.Algorithm
	}
	return ModelAlgorithm_MO
}

func (x *ModelDomain) GetComponents() []*DataModality {
	if x != nil {
		return x.Components
	}
	return nil
}

// ModelApi 为模型单个接口的格式描述
type ModelApi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path    string        `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Inputs  []*ModelParam `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty"`   // 该接口的所有输入参数
	Outputs []*ModelParam `protobuf:"bytes,3,rep,name=outputs,proto3" json:"outputs,omitempty"` // 该接口的所有输出参数
}

func (x *ModelApi) Reset() {
	*x = ModelApi{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelApi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelApi) ProtoMessage() {}

func (x *ModelApi) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelApi.ProtoReflect.Descriptor instead.
func (*ModelApi) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{7}
}

func (x *ModelApi) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ModelApi) GetInputs() []*ModelParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *ModelApi) GetOutputs() []*ModelParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

// ModelParam 定义了模型所需的一个输入或输出参数
type ModelParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                              // 输入输出参数的名称
	DataType     DataType         `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=proto.DataType" json:"data_type,omitempty"` // 输入输出参数中包含的数据类型
	Format       ModelParamFormat `protobuf:"varint,3,opt,name=format,proto3,enum=proto.ModelParamFormat" json:"format,omitempty"`             // 输入输出参数中包含的数据的组织形式
	Dims         []int32          `protobuf:"varint,4,rep,packed,name=dims,proto3" json:"dims,omitempty"`                                      // 当调用此模型的API时必须提供的 输入/输出 张量（Tensor）的尺寸(维度)
	Optional     bool             `protobuf:"varint,5,opt,name=optional,proto3" json:"optional,omitempty"`                                     // 该参数在进行推理时是否必须提供, 默认为False
	DefaultValue string           `protobuf:"bytes,6,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`          // 该参数在进行推理时的默认值、示例数据或者模板数据
}

func (x *ModelParam) Reset() {
	*x = ModelParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelParam) ProtoMessage() {}

func (x *ModelParam) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelParam.ProtoReflect.Descriptor instead.
func (*ModelParam) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{8}
}

func (x *ModelParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelParam) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_DATA_TYPE_INVALID
}

func (x *ModelParam) GetFormat() ModelParamFormat {
	if x != nil {
		return x.Format
	}
	return ModelParamFormat_MODEL_PARAM_FORMAT_NONE
}

func (x *ModelParam) GetDims() []int32 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (x *ModelParam) GetOptional() bool {
	if x != nil {
		return x.Optional
	}
	return false
}

func (x *ModelParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

// ModelDetail 定义了模型的详细描述信息
type ModelDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Desc         string            `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                                   // desc 模型描述信息
	UserId       string            `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                                 // user_id 模型创建者
	Thumbnail    string            `protobuf:"bytes,3,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`                                                                                         // thumbnail 模型展示缩略图
	IsPublic     bool              `protobuf:"varint,4,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`                                                                          // is_public 模型是否为公开模型
	CreateTimeMs int64             `protobuf:"varint,5,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`                                                            // create_time_ms 创建时间戳
	UpdateTimeMs int64             `protobuf:"varint,6,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`                                                            // update_time_ms 更新时间戳
	Labels       map[string]string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`       // labels 模型标签列表
	Baselines    map[string]string `protobuf:"bytes,8,rep,name=baselines,proto3" json:"baselines,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // baselines 模型的基线版本列表，key 为 CPUArch (name), value 为对应的版本ID
	Relations    []*Relation       `protobuf:"bytes,9,rep,name=relations,proto3" json:"relations,omitempty"`                                                                                         // relations 模型关联关系
}

func (x *ModelDetail) Reset() {
	*x = ModelDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDetail) ProtoMessage() {}

func (x *ModelDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDetail.ProtoReflect.Descriptor instead.
func (*ModelDetail) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{9}
}

func (x *ModelDetail) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModelDetail) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ModelDetail) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *ModelDetail) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *ModelDetail) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *ModelDetail) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *ModelDetail) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ModelDetail) GetBaselines() map[string]string {
	if x != nil {
		return x.Baselines
	}
	return nil
}

func (x *ModelDetail) GetRelations() []*Relation {
	if x != nil {
		return x.Relations
	}
	return nil
}

// Attachment 定义了模型/模型版本相关的附件，e.g. 训练数据集、评估数据集、模型使用说明、模型评估报告等...
type Attachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     // id 为每个附件的UUID
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // name 为每个附件的用户自定义名称
	Path      string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"` // path 定义了附件具体的存储路径，可以为 本地路径： file://xxx 或 S3路径 s3://xxx
	Desc      string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"` // desc 关于该附件的描述信息
	ProjectId string `protobuf:"bytes,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Creator   string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{10}
}

func (x *Attachment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Attachment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Attachment) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Attachment) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Attachment) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Attachment) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

// ModelReleaseDetail
type ModelReleaseDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Desc                  string            `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                                                                                        // 描述信息
	ModelSize             int64             `protobuf:"varint,7,opt,name=model_size,json=modelSize,proto3" json:"model_size,omitempty"`                                                                                                                            // 模型的大小（原子模型文件大小 | 镜像大小 | 组合模型包大小）
	CreateTimeMs          int64             `protobuf:"varint,2,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`                                                                                                                 // 创建时间
	UpdateTimeMs          int64             `protobuf:"varint,3,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`                                                                                                                 // 更新时间
	Labels                map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                                            // 自定义标签
	Relations             []*Relation       `protobuf:"bytes,5,rep,name=relations,proto3" json:"relations,omitempty"`                                                                                                                                              // 关联关系
	Attachments           []*Attachment     `protobuf:"bytes,6,rep,name=attachments,proto3" json:"attachments,omitempty"`                                                                                                                                          // 模型版本附件
	ComputationAttributes map[string]string `protobuf:"bytes,8,rep,name=computation_attributes,json=computationAttributes,proto3" json:"computation_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 一些计算属性，如模型的输入输出维度等,约定一些key值，输出维度key为output_vector_dims value为逗号分隔的数字
}

func (x *ModelReleaseDetail) Reset() {
	*x = ModelReleaseDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseDetail) ProtoMessage() {}

func (x *ModelReleaseDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseDetail.ProtoReflect.Descriptor instead.
func (*ModelReleaseDetail) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{11}
}

func (x *ModelReleaseDetail) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModelReleaseDetail) GetModelSize() int64 {
	if x != nil {
		return x.ModelSize
	}
	return 0
}

func (x *ModelReleaseDetail) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *ModelReleaseDetail) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *ModelReleaseDetail) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ModelReleaseDetail) GetRelations() []*Relation {
	if x != nil {
		return x.Relations
	}
	return nil
}

func (x *ModelReleaseDetail) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ModelReleaseDetail) GetComputationAttributes() map[string]string {
	if x != nil {
		return x.ComputationAttributes
	}
	return nil
}

// FilePath 上传数据的信息
type FilePath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Protocol RepoAddrProtocol `protobuf:"varint,1,opt,name=protocol,proto3,enum=proto.RepoAddrProtocol" json:"protocol,omitempty"`
	Host     string           `protobuf:"bytes,2,opt,name=Host,proto3" json:"Host,omitempty"`
	Path     string           `protobuf:"bytes,3,opt,name=Path,proto3" json:"Path,omitempty"`
}

func (x *FilePath) Reset() {
	*x = FilePath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilePath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilePath) ProtoMessage() {}

func (x *FilePath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilePath.ProtoReflect.Descriptor instead.
func (*FilePath) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{12}
}

func (x *FilePath) GetProtocol() RepoAddrProtocol {
	if x != nil {
		return x.Protocol
	}
	return RepoAddrProtocol_REPO_ADDR_PROTOCOL_UNSPECIFIED
}

func (x *FilePath) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *FilePath) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type TrainingTemplateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *TrainingTemplateConfig) Reset() {
	*x = TrainingTemplateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainingTemplateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainingTemplateConfig) ProtoMessage() {}

func (x *TrainingTemplateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainingTemplateConfig.ProtoReflect.Descriptor instead.
func (*TrainingTemplateConfig) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{13}
}

func (x *TrainingTemplateConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrainingTemplateConfig) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// ModelReleaseBase 所有模型版本均具有的公共信息
type ModelReleaseBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                     // 模型版本UUID, 后端自动生成
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                 // 模型版本用户自定名称， 用户可修改
	Version    string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                           // 模型版本的自增版本号，不可修改
	Repo       string `protobuf:"bytes,4,opt,name=repo,proto3" json:"repo,omitempty"`                                 // 模型版本关联的模型存储地址， 支持不同类型的repo地址,详见 RepoAddrProtocol
	ModelId    string `protobuf:"bytes,5,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`            // 所属模型ID
	IsBaseline bool   `protobuf:"varint,11,opt,name=is_baseline,json=isBaseline,proto3" json:"is_baseline,omitempty"` // 是否是基线版本（每个模型的每种硬件架构仅支持一个基线版本）
	// ModelReleaseStatus status = 6;          // 状态阶段
	Detail           *ModelReleaseDetail     `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail,omitempty"`                                     // 模型版本详细描述信息
	Stats            *ModelReleaseStats      `protobuf:"bytes,8,opt,name=stats,proto3" json:"stats,omitempty"`                                       // 模型版本的动态统计信息，动态生成并返回
	HardwareRange    *HardwareRange          `protobuf:"bytes,9,opt,name=hardware_range,json=hardwareRange,proto3" json:"hardware_range,omitempty"`  // 模型适配的硬件信息
	DefaultConfig    *ModelDefaultConfig     `protobuf:"bytes,10,opt,name=default_config,json=defaultConfig,proto3" json:"default_config,omitempty"` // 模型部署时默认配置（包含所用Runtime / 资源 / 参数配置等）
	ModelApis        []*ModelApi             `protobuf:"bytes,12,rep,name=model_apis,json=modelApis,proto3" json:"model_apis,omitempty"`             // 经过用户修改后的model api配置
	ProjectId        string                  `protobuf:"bytes,13,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Creator          string                  `protobuf:"bytes,14,opt,name=creator,proto3" json:"creator,omitempty"`
	TrainingTemplate *TrainingTemplateConfig `protobuf:"bytes,15,opt,name=training_template,json=trainingTemplate,proto3" json:"training_template,omitempty"`
	IsSystemInit     bool                    `protobuf:"varint,16,opt,name=is_system_init,json=isSystemInit,proto3" json:"is_system_init,omitempty"` //是否为系统初始化的模型
	ModelWeight      string                  `protobuf:"bytes,17,opt,name=model_weight,json=modelWeight,proto3" json:"model_weight,omitempty"`
}

func (x *ModelReleaseBase) Reset() {
	*x = ModelReleaseBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseBase) ProtoMessage() {}

func (x *ModelReleaseBase) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseBase.ProtoReflect.Descriptor instead.
func (*ModelReleaseBase) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{14}
}

func (x *ModelReleaseBase) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModelReleaseBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelReleaseBase) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModelReleaseBase) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *ModelReleaseBase) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelReleaseBase) GetIsBaseline() bool {
	if x != nil {
		return x.IsBaseline
	}
	return false
}

func (x *ModelReleaseBase) GetDetail() *ModelReleaseDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *ModelReleaseBase) GetStats() *ModelReleaseStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *ModelReleaseBase) GetHardwareRange() *HardwareRange {
	if x != nil {
		return x.HardwareRange
	}
	return nil
}

func (x *ModelReleaseBase) GetDefaultConfig() *ModelDefaultConfig {
	if x != nil {
		return x.DefaultConfig
	}
	return nil
}

func (x *ModelReleaseBase) GetModelApis() []*ModelApi {
	if x != nil {
		return x.ModelApis
	}
	return nil
}

func (x *ModelReleaseBase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ModelReleaseBase) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ModelReleaseBase) GetTrainingTemplate() *TrainingTemplateConfig {
	if x != nil {
		return x.TrainingTemplate
	}
	return nil
}

func (x *ModelReleaseBase) GetIsSystemInit() bool {
	if x != nil {
		return x.IsSystemInit
	}
	return false
}

func (x *ModelReleaseBase) GetModelWeight() string {
	if x != nil {
		return x.ModelWeight
	}
	return ""
}

type ModelRelease struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseBase *ModelReleaseBase `protobuf:"bytes,1,opt,name=release_base,json=releaseBase,proto3" json:"release_base,omitempty"`
	ModelMeta   *ModelMeta        `protobuf:"bytes,2,opt,name=model_meta,json=modelMeta,proto3" json:"model_meta,omitempty"`
}

func (x *ModelRelease) Reset() {
	*x = ModelRelease{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRelease) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRelease) ProtoMessage() {}

func (x *ModelRelease) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRelease.ProtoReflect.Descriptor instead.
func (*ModelRelease) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{15}
}

func (x *ModelRelease) GetReleaseBase() *ModelReleaseBase {
	if x != nil {
		return x.ReleaseBase
	}
	return nil
}

func (x *ModelRelease) GetModelMeta() *ModelMeta {
	if x != nil {
		return x.ModelMeta
	}
	return nil
}

type ModelReleaseStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploymentId     string             `protobuf:"bytes,1,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"` // 模型版本是否已部署，如果已部署，则填充对应的部署实例ID
	DeploymentStatus string             `protobuf:"bytes,4,opt,name=deployment_status,json=deploymentStatus,proto3" json:"deployment_status,omitempty"`
	DeploymentHealth string             `protobuf:"bytes,5,opt,name=deployment_health,json=deploymentHealth,proto3" json:"deployment_health,omitempty"`
	ModelName        string             `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`                                            // 关联模型名称
	ReleaseStatus    ModelReleaseStatus `protobuf:"varint,3,opt,name=release_status,json=releaseStatus,proto3,enum=proto.ModelReleaseStatus" json:"release_status,omitempty"` // 评估状态
}

func (x *ModelReleaseStats) Reset() {
	*x = ModelReleaseStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseStats) ProtoMessage() {}

func (x *ModelReleaseStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseStats.ProtoReflect.Descriptor instead.
func (*ModelReleaseStats) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{16}
}

func (x *ModelReleaseStats) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

func (x *ModelReleaseStats) GetDeploymentStatus() string {
	if x != nil {
		return x.DeploymentStatus
	}
	return ""
}

func (x *ModelReleaseStats) GetDeploymentHealth() string {
	if x != nil {
		return x.DeploymentHealth
	}
	return ""
}

func (x *ModelReleaseStats) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelReleaseStats) GetReleaseStatus() ModelReleaseStatus {
	if x != nil {
		return x.ReleaseStatus
	}
	return ModelReleaseStatus_MODEL_RELEASE_STATUS_UNSPECIFIED
}

type ModelMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelType         ModelType          `protobuf:"varint,1,opt,name=model_type,json=modelType,proto3,enum=proto.ModelType" json:"model_type,omitempty"`
	FileModelMeta     *FileModelMeta     `protobuf:"bytes,2,opt,name=file_model_meta,json=fileModelMeta,proto3" json:"file_model_meta,omitempty"`
	ImageModelMeta    *ImageModelMeta    `protobuf:"bytes,3,opt,name=image_model_meta,json=imageModelMeta,proto3" json:"image_model_meta,omitempty"`
	EnsembleModelMeta *EnsembleModelMeta `protobuf:"bytes,4,opt,name=ensemble_model_meta,json=ensembleModelMeta,proto3" json:"ensemble_model_meta,omitempty"`
	ModelSubType      ModelSubType       `protobuf:"varint,5,opt,name=model_sub_type,json=modelSubType,proto3,enum=proto.ModelSubType" json:"model_sub_type,omitempty"`
}

func (x *ModelMeta) Reset() {
	*x = ModelMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMeta) ProtoMessage() {}

func (x *ModelMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMeta.ProtoReflect.Descriptor instead.
func (*ModelMeta) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{17}
}

func (x *ModelMeta) GetModelType() ModelType {
	if x != nil {
		return x.ModelType
	}
	return ModelType_MODEL_TYPE_UNSPECIFIED
}

func (x *ModelMeta) GetFileModelMeta() *FileModelMeta {
	if x != nil {
		return x.FileModelMeta
	}
	return nil
}

func (x *ModelMeta) GetImageModelMeta() *ImageModelMeta {
	if x != nil {
		return x.ImageModelMeta
	}
	return nil
}

func (x *ModelMeta) GetEnsembleModelMeta() *EnsembleModelMeta {
	if x != nil {
		return x.EnsembleModelMeta
	}
	return nil
}

func (x *ModelMeta) GetModelSubType() ModelSubType {
	if x != nil {
		return x.ModelSubType
	}
	return ModelSubType_MODEL_SUB_TYPE_UNSPECIFIED
}

// ModelDefaultConfig 定义了模型默认部署时使用的配置
type ModelDefaultConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Runtime          ModelRuntimeType  `protobuf:"varint,1,opt,name=runtime,proto3,enum=proto.ModelRuntimeType" json:"runtime,omitempty"`
	Resource         *DeployResource   `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	Values           map[string]string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeploymentParams []*DynamicParam   `protobuf:"bytes,4,rep,name=deployment_params,json=deploymentParams,proto3" json:"deployment_params,omitempty"`
	InferenceParams  []*DynamicParam   `protobuf:"bytes,5,rep,name=inference_params,json=inferenceParams,proto3" json:"inference_params,omitempty"`
	DefaultPrompt    string            `protobuf:"bytes,6,opt,name=default_prompt,json=defaultPrompt,proto3" json:"default_prompt,omitempty"`             // llm默认系统提示词
	UseDefaultParams bool              `protobuf:"varint,7,opt,name=use_default_params,json=useDefaultParams,proto3" json:"use_default_params,omitempty"` // 是否使用默认的推理参数与提示词
}

func (x *ModelDefaultConfig) Reset() {
	*x = ModelDefaultConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDefaultConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDefaultConfig) ProtoMessage() {}

func (x *ModelDefaultConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDefaultConfig.ProtoReflect.Descriptor instead.
func (*ModelDefaultConfig) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{18}
}

func (x *ModelDefaultConfig) GetRuntime() ModelRuntimeType {
	if x != nil {
		return x.Runtime
	}
	return ModelRuntimeType_MODEL_RUNTIME_TYPE_UNSPECIFIED
}

func (x *ModelDefaultConfig) GetResource() *DeployResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ModelDefaultConfig) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *ModelDefaultConfig) GetDeploymentParams() []*DynamicParam {
	if x != nil {
		return x.DeploymentParams
	}
	return nil
}

func (x *ModelDefaultConfig) GetInferenceParams() []*DynamicParam {
	if x != nil {
		return x.InferenceParams
	}
	return nil
}

func (x *ModelDefaultConfig) GetDefaultPrompt() string {
	if x != nil {
		return x.DefaultPrompt
	}
	return ""
}

func (x *ModelDefaultConfig) GetUseDefaultParams() bool {
	if x != nil {
		return x.UseDefaultParams
	}
	return false
}

type FileModelMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Raw     string `protobuf:"bytes,1,opt,name=raw,proto3" json:"raw,omitempty"`          // 模型文件自动解析出的
	Encrypt bool   `protobuf:"varint,2,opt,name=encrypt,proto3" json:"encrypt,omitempty"` // 是否对模型文件进行加密
	// TODO 新增其它文件原子模型通用字段
	//
	//	string metadata = 1;
	//	map<string, string> framework_requirements = 4;
	//	map<string, double> original_evaluation_index = 5;
	TrainingDataDistributions map[string]*TrainingDataDistribution `protobuf:"bytes,3,rep,name=training_data_distributions,json=trainingDataDistributions,proto3" json:"training_data_distributions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key 为 input name
}

func (x *FileModelMeta) Reset() {
	*x = FileModelMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileModelMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileModelMeta) ProtoMessage() {}

func (x *FileModelMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileModelMeta.ProtoReflect.Descriptor instead.
func (*FileModelMeta) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{19}
}

func (x *FileModelMeta) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

func (x *FileModelMeta) GetEncrypt() bool {
	if x != nil {
		return x.Encrypt
	}
	return false
}

func (x *FileModelMeta) GetTrainingDataDistributions() map[string]*TrainingDataDistribution {
	if x != nil {
		return x.TrainingDataDistributions
	}
	return nil
}

type EnsembleModelMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRepo  string            `protobuf:"bytes,1,opt,name=base_repo,json=baseRepo,proto3" json:"base_repo,omitempty"`       // 组合模型所在基础目录
	Committed bool              `protobuf:"varint,2,opt,name=committed,proto3" json:"committed,omitempty"`                    // 是否是完成了正式提交
	Mode      ModelEnsembleMode `protobuf:"varint,3,opt,name=mode,proto3,enum=proto.ModelEnsembleMode" json:"mode,omitempty"` // 模型组合模式
	SubModels []*SubModel       `protobuf:"bytes,4,rep,name=sub_models,json=subModels,proto3" json:"sub_models,omitempty"`    // 包含的所有子模型
}

func (x *EnsembleModelMeta) Reset() {
	*x = EnsembleModelMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnsembleModelMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnsembleModelMeta) ProtoMessage() {}

func (x *EnsembleModelMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnsembleModelMeta.ProtoReflect.Descriptor instead.
func (*EnsembleModelMeta) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{20}
}

func (x *EnsembleModelMeta) GetBaseRepo() string {
	if x != nil {
		return x.BaseRepo
	}
	return ""
}

func (x *EnsembleModelMeta) GetCommitted() bool {
	if x != nil {
		return x.Committed
	}
	return false
}

func (x *EnsembleModelMeta) GetMode() ModelEnsembleMode {
	if x != nil {
		return x.Mode
	}
	return ModelEnsembleMode_MODEL_ENSEMBLE_MODE_UNSPECIFIED
}

func (x *EnsembleModelMeta) GetSubModels() []*SubModel {
	if x != nil {
		return x.SubModels
	}
	return nil
}

type ModelContentFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fid        string   `protobuf:"bytes,1,opt,name=fid,proto3" json:"fid,omitempty"`                                  // 文件ID, 同一个子模型下唯一
	Name       string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                // 文件名称
	Path       string   `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                // 文件相对路径
	Ext        string   `protobuf:"bytes,4,opt,name=ext,proto3" json:"ext,omitempty"`                                  // 文件扩展名, 前端用来进行不同的展示 e.g. pbtxt, pmml, onnx, py, txt, json, yaml, etc...
	Type       FileType `protobuf:"varint,6,opt,name=type,proto3,enum=proto.FileType" json:"type,omitempty"`           // 文件类型，与扩展名对应
	IsConfig   bool     `protobuf:"varint,7,opt,name=is_config,json=isConfig,proto3" json:"is_config,omitempty"`       // 该文件是否为模型配置说明文件
	IsModel    bool     `protobuf:"varint,8,opt,name=is_model,json=isModel,proto3" json:"is_model,omitempty"`          // 该文件是否为模型文件本身
	LastUpdate int64    `protobuf:"varint,9,opt,name=last_update,json=lastUpdate,proto3" json:"last_update,omitempty"` // 最近修改时间戳
}

func (x *ModelContentFile) Reset() {
	*x = ModelContentFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelContentFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelContentFile) ProtoMessage() {}

func (x *ModelContentFile) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelContentFile.ProtoReflect.Descriptor instead.
func (*ModelContentFile) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{21}
}

func (x *ModelContentFile) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *ModelContentFile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelContentFile) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ModelContentFile) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *ModelContentFile) GetType() FileType {
	if x != nil {
		return x.Type
	}
	return FileType_FILE_TYPE_UNSPECIFIED
}

func (x *ModelContentFile) GetIsConfig() bool {
	if x != nil {
		return x.IsConfig
	}
	return false
}

func (x *ModelContentFile) GetIsModel() bool {
	if x != nil {
		return x.IsModel
	}
	return false
}

func (x *ModelContentFile) GetLastUpdate() int64 {
	if x != nil {
		return x.LastUpdate
	}
	return 0
}

type SubModelAlias struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DirName   string       `protobuf:"bytes,1,opt,name=dir_name,json=dirName,proto3" json:"dir_name,omitempty"` //
	ModelType SubModelType `protobuf:"varint,2,opt,name=model_type,json=modelType,proto3,enum=proto.SubModelType" json:"model_type,omitempty"`
}

func (x *SubModelAlias) Reset() {
	*x = SubModelAlias{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubModelAlias) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubModelAlias) ProtoMessage() {}

func (x *SubModelAlias) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubModelAlias.ProtoReflect.Descriptor instead.
func (*SubModelAlias) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{22}
}

func (x *SubModelAlias) GetDirName() string {
	if x != nil {
		return x.DirName
	}
	return ""
}

func (x *SubModelAlias) GetModelType() SubModelType {
	if x != nil {
		return x.ModelType
	}
	return SubModelType_SUB_MODEL_TYPE_UNSPECIFIED
}

type AtomSubModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alias *SubModelAlias      `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"` // 原子模型所在目录
	Files []*ModelContentFile `protobuf:"bytes,6,rep,name=files,proto3" json:"files,omitempty"` // 原子模型中包含的模型相关文件信息
}

func (x *AtomSubModel) Reset() {
	*x = AtomSubModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtomSubModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtomSubModel) ProtoMessage() {}

func (x *AtomSubModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtomSubModel.ProtoReflect.Descriptor instead.
func (*AtomSubModel) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{23}
}

func (x *AtomSubModel) GetAlias() *SubModelAlias {
	if x != nil {
		return x.Alias
	}
	return nil
}

func (x *AtomSubModel) GetFiles() []*ModelContentFile {
	if x != nil {
		return x.Files
	}
	return nil
}

type AtomModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`                // 对应的模型ID
	ReleaseId    string `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`          // 对应的模型版本ID
	ReleaseName  string `protobuf:"bytes,4,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`    // 对应的模型版本号（e.g. v1, 自动生成）
	ReleaseAlias string `protobuf:"bytes,5,opt,name=release_alias,json=releaseAlias,proto3" json:"release_alias,omitempty"` // 对应的模型版本别名（中文，可选）
}

func (x *AtomModelInfo) Reset() {
	*x = AtomModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtomModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtomModelInfo) ProtoMessage() {}

func (x *AtomModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtomModelInfo.ProtoReflect.Descriptor instead.
func (*AtomModelInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{24}
}

func (x *AtomModelInfo) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *AtomModelInfo) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *AtomModelInfo) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *AtomModelInfo) GetReleaseAlias() string {
	if x != nil {
		return x.ReleaseAlias
	}
	return ""
}

type PythonCodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PythonCodeInfo) Reset() {
	*x = PythonCodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PythonCodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PythonCodeInfo) ProtoMessage() {}

func (x *PythonCodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PythonCodeInfo.ProtoReflect.Descriptor instead.
func (*PythonCodeInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{25}
}

type EnsembleConfigInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnsembleConfigInfo) Reset() {
	*x = EnsembleConfigInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnsembleConfigInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnsembleConfigInfo) ProtoMessage() {}

func (x *EnsembleConfigInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnsembleConfigInfo.ProtoReflect.Descriptor instead.
func (*EnsembleConfigInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{26}
}

type SubModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AtomModelInfo      *AtomModelInfo      `protobuf:"bytes,1,opt,name=atom_model_info,json=atomModelInfo,proto3" json:"atom_model_info,omitempty"`
	PythonCodeInfo     *PythonCodeInfo     `protobuf:"bytes,2,opt,name=python_code_info,json=pythonCodeInfo,proto3" json:"python_code_info,omitempty"`
	EnsembleConfigInfo *EnsembleConfigInfo `protobuf:"bytes,3,opt,name=ensemble_config_info,json=ensembleConfigInfo,proto3" json:"ensemble_config_info,omitempty"`
}

func (x *SubModelInfo) Reset() {
	*x = SubModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubModelInfo) ProtoMessage() {}

func (x *SubModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubModelInfo.ProtoReflect.Descriptor instead.
func (*SubModelInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{27}
}

func (x *SubModelInfo) GetAtomModelInfo() *AtomModelInfo {
	if x != nil {
		return x.AtomModelInfo
	}
	return nil
}

func (x *SubModelInfo) GetPythonCodeInfo() *PythonCodeInfo {
	if x != nil {
		return x.PythonCodeInfo
	}
	return nil
}

func (x *SubModelInfo) GetEnsembleConfigInfo() *EnsembleConfigInfo {
	if x != nil {
		return x.EnsembleConfigInfo
	}
	return nil
}

// SubModel 组合模型中包含的单个子模型的描述
type SubModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alias string              `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`                        // 模型别名，用于标识新增原子模型在组装模型中的目录，在配置模型调用顺序与依赖关系时，需使用该模型别名进行标识，由字母与数字构成，不包含除 '_', '-' 以外的符号, 且必须以字母开头
	Type  SubModelType        `protobuf:"varint,2,opt,name=type,proto3,enum=proto.SubModelType" json:"type,omitempty"` // 子模型类型 [原子模型, 胶水代码, 组装配置]
	Info  *SubModelInfo       `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`                          // 子模型不同类型的额外的信息
	Order int64               `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`                       // 子模型的在模型配置中的序号
	Files []*ModelContentFile `protobuf:"bytes,5,rep,name=files,proto3" json:"files,omitempty"`                        // 子模型中包含的模型相关文件信息
}

func (x *SubModel) Reset() {
	*x = SubModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubModel) ProtoMessage() {}

func (x *SubModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubModel.ProtoReflect.Descriptor instead.
func (*SubModel) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{28}
}

func (x *SubModel) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *SubModel) GetType() SubModelType {
	if x != nil {
		return x.Type
	}
	return SubModelType_SUB_MODEL_TYPE_UNSPECIFIED
}

func (x *SubModel) GetInfo() *SubModelInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *SubModel) GetOrder() int64 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *SubModel) GetFiles() []*ModelContentFile {
	if x != nil {
		return x.Files
	}
	return nil
}

// FileList 模型中模型文件信息列表
type FileList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []*ModelContentFile `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"` // 模型文件信息列表
}

func (x *FileList) Reset() {
	*x = FileList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileList) ProtoMessage() {}

func (x *FileList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileList.ProtoReflect.Descriptor instead.
func (*FileList) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{29}
}

func (x *FileList) GetFiles() []*ModelContentFile {
	if x != nil {
		return x.Files
	}
	return nil
}

// EnsembleModelRelease 组合模型版本
type EnsembleModelRelease struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelRelease *ModelReleaseBase  `protobuf:"bytes,1,opt,name=model_release,json=modelRelease,proto3" json:"model_release,omitempty"` // 版本基础信息
	ModelMeta    *EnsembleModelMeta `protobuf:"bytes,2,opt,name=model_meta,json=modelMeta,proto3" json:"model_meta,omitempty"`          // 模型成分
}

func (x *EnsembleModelRelease) Reset() {
	*x = EnsembleModelRelease{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnsembleModelRelease) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnsembleModelRelease) ProtoMessage() {}

func (x *EnsembleModelRelease) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnsembleModelRelease.ProtoReflect.Descriptor instead.
func (*EnsembleModelRelease) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{30}
}

func (x *EnsembleModelRelease) GetModelRelease() *ModelReleaseBase {
	if x != nil {
		return x.ModelRelease
	}
	return nil
}

func (x *EnsembleModelRelease) GetModelMeta() *EnsembleModelMeta {
	if x != nil {
		return x.ModelMeta
	}
	return nil
}

type ImageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Envs          map[string]string `protobuf:"bytes,1,rep,name=envs,proto3" json:"envs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 镜像所需要的环境变量 形式 {"MYSQL_ROOT_PASSWORD": "transwarp123"}
	Cmds          []string          `protobuf:"bytes,2,rep,name=cmds,proto3" json:"cmds,omitempty"`
	WorkingDir    string            `protobuf:"bytes,3,opt,name=working_dir,json=workingDir,proto3" json:"working_dir,omitempty"`
	EntryPoint    string            `protobuf:"bytes,4,opt,name=entry_point,json=entryPoint,proto3" json:"entry_point,omitempty"`
	Labels        map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Args          []string          `protobuf:"bytes,6,rep,name=args,proto3" json:"args,omitempty"`
	RestartPolicy string            `protobuf:"bytes,7,opt,name=restart_policy,json=restartPolicy,proto3" json:"restart_policy,omitempty"`                                                        // 镜像重启策略， 保留扩展先不暴露给用户配置
	Volumes       map[string]string `protobuf:"bytes,8,rep,name=volumes,proto3" json:"volumes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 挂载存储卷，可以保留需要持久化的数据，  保留扩展先不暴露给用户配置
	Ports         []string          `protobuf:"bytes,9,rep,name=ports,proto3" json:"ports,omitempty"`                                                                                             // 镜像中服务所在的端口，形式["3306/TCP"]
}

func (x *ImageConfig) Reset() {
	*x = ImageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageConfig) ProtoMessage() {}

func (x *ImageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageConfig.ProtoReflect.Descriptor instead.
func (*ImageConfig) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{31}
}

func (x *ImageConfig) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *ImageConfig) GetCmds() []string {
	if x != nil {
		return x.Cmds
	}
	return nil
}

func (x *ImageConfig) GetWorkingDir() string {
	if x != nil {
		return x.WorkingDir
	}
	return ""
}

func (x *ImageConfig) GetEntryPoint() string {
	if x != nil {
		return x.EntryPoint
	}
	return ""
}

func (x *ImageConfig) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ImageConfig) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ImageConfig) GetRestartPolicy() string {
	if x != nil {
		return x.RestartPolicy
	}
	return ""
}

func (x *ImageConfig) GetVolumes() map[string]string {
	if x != nil {
		return x.Volumes
	}
	return nil
}

func (x *ImageConfig) GetPorts() []string {
	if x != nil {
		return x.Ports
	}
	return nil
}

type ImageModelMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RepoTags      []string     `protobuf:"bytes,2,rep,name=repo_tags,json=repoTags,proto3" json:"repo_tags,omitempty"`
	RepoDigests   []string     `protobuf:"bytes,3,rep,name=repo_digests,json=repoDigests,proto3" json:"repo_digests,omitempty"`
	Created       string       `protobuf:"bytes,4,opt,name=created,proto3" json:"created,omitempty"`
	DockerVersion string       `protobuf:"bytes,5,opt,name=docker_version,json=dockerVersion,proto3" json:"docker_version,omitempty"`
	Author        string       `protobuf:"bytes,6,opt,name=author,proto3" json:"author,omitempty"`
	Config        *ImageConfig `protobuf:"bytes,7,opt,name=config,proto3" json:"config,omitempty"`
	Architecture  string       `protobuf:"bytes,8,opt,name=architecture,proto3" json:"architecture,omitempty"`
	Os            string       `protobuf:"bytes,9,opt,name=os,proto3" json:"os,omitempty"`
	Size          int64        `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *ImageModelMeta) Reset() {
	*x = ImageModelMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageModelMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageModelMeta) ProtoMessage() {}

func (x *ImageModelMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageModelMeta.ProtoReflect.Descriptor instead.
func (*ImageModelMeta) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{32}
}

func (x *ImageModelMeta) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ImageModelMeta) GetRepoTags() []string {
	if x != nil {
		return x.RepoTags
	}
	return nil
}

func (x *ImageModelMeta) GetRepoDigests() []string {
	if x != nil {
		return x.RepoDigests
	}
	return nil
}

func (x *ImageModelMeta) GetCreated() string {
	if x != nil {
		return x.Created
	}
	return ""
}

func (x *ImageModelMeta) GetDockerVersion() string {
	if x != nil {
		return x.DockerVersion
	}
	return ""
}

func (x *ImageModelMeta) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ImageModelMeta) GetConfig() *ImageConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ImageModelMeta) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *ImageModelMeta) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *ImageModelMeta) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ImageModelRelease struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelRelease *ModelReleaseBase `protobuf:"bytes,1,opt,name=model_release,json=modelRelease,proto3" json:"model_release,omitempty"`
	ModelMeta    *ImageModelMeta   `protobuf:"bytes,2,opt,name=model_meta,json=modelMeta,proto3" json:"model_meta,omitempty"`
}

func (x *ImageModelRelease) Reset() {
	*x = ImageModelRelease{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageModelRelease) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageModelRelease) ProtoMessage() {}

func (x *ImageModelRelease) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageModelRelease.ProtoReflect.Descriptor instead.
func (*ImageModelRelease) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{33}
}

func (x *ImageModelRelease) GetModelRelease() *ModelReleaseBase {
	if x != nil {
		return x.ModelRelease
	}
	return nil
}

func (x *ImageModelRelease) GetModelMeta() *ImageModelMeta {
	if x != nil {
		return x.ModelMeta
	}
	return nil
}

// DeploymentDetail 定义了部署的详细描述信息
type DeploymentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Desc         string            `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                             // desc 部署描述信息
	UserId       string            `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                           // user_id 部署创建者
	CreateTimeMs int64             `protobuf:"varint,5,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`                                                      // create_time_ms 创建时间戳
	UpdateTimeMs int64             `protobuf:"varint,6,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`                                                      // update_time_ms 更新时间戳
	Labels       map[string]string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // labels 模型标签列表
}

func (x *DeploymentDetail) Reset() {
	*x = DeploymentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentDetail) ProtoMessage() {}

func (x *DeploymentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentDetail.ProtoReflect.Descriptor instead.
func (*DeploymentDetail) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{34}
}

func (x *DeploymentDetail) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DeploymentDetail) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeploymentDetail) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *DeploymentDetail) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *DeploymentDetail) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Deployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                          // 模型部署实例UUID, 系统自动生成
	ModelId        string            `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"` // 部署使用的模型
	ModelName      string            `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ReleaseId      string            `protobuf:"bytes,4,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`                                                                   // 部署使用的模型版本
	ReleaseName    string            `protobuf:"bytes,5,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`                                                             // 模型版本名称
	SysId          string            `protobuf:"bytes,6,opt,name=sys_id,json=sysId,proto3" json:"sys_id,omitempty"`                                                                               // 部署的目标 DeploySys ID
	Enable         bool              `protobuf:"varint,7,opt,name=enable,proto3" json:"enable,omitempty"`                                                                                         // 是否启用该模型部署实例
	Replica        int64             `protobuf:"varint,8,opt,name=replica,proto3" json:"replica,omitempty"`                                                                                       // 最终预期的副本的数量
	RuntimeType    ModelRuntimeType  `protobuf:"varint,9,opt,name=runtime_type,json=runtimeType,proto3,enum=proto.ModelRuntimeType" json:"runtime_type,omitempty"`                                // 部署时适用的运行时
	Resource       *DeployResource   `protobuf:"bytes,10,opt,name=resource,proto3" json:"resource,omitempty"`                                                                                     // （可选）部署时资源配置
	Selector       *DeploySelector   `protobuf:"bytes,11,opt,name=selector,proto3" json:"selector,omitempty"`                                                                                     // （可选）部署时节点选择配置
	Values         map[string]string `protobuf:"bytes,12,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 提供不同runtime所需的动态参数的配置
	Detail         *DeploymentDetail `protobuf:"bytes,13,opt,name=detail,proto3" json:"detail,omitempty"`
	Status         *Status           `protobuf:"bytes,14,opt,name=status,proto3" json:"status,omitempty"`
	Type           DeploymentType    `protobuf:"varint,15,opt,name=type,proto3,enum=proto.DeploymentType" json:"type,omitempty"`
	PortMapStart   int32             `protobuf:"varint,16,opt,name=port_map_start,json=portMapStart,proto3" json:"port_map_start,omitempty"` // 端口映射开始端口,k8s中port(映射端口)->targetPort(自定义端口)，屏蔽自定义端口的随机性
	StartTimeMs    int64             `protobuf:"varint,17,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`    // 启动标志位，防止更新Deployment状态时重复启动
	StopTimeMs     int64             `protobuf:"varint,18,opt,name=stop_time_ms,json=stopTimeMs,proto3" json:"stop_time_ms,omitempty"`
	Nodes          []string          `protobuf:"bytes,19,rep,name=nodes,proto3" json:"nodes,omitempty"` // 运行节点，部署完成后才能确定
	ReleaseVersion string            `protobuf:"bytes,20,opt,name=release_version,json=releaseVersion,proto3" json:"release_version,omitempty"`
	AutoSchedule   bool              `protobuf:"varint,21,opt,name=auto_schedule,json=autoSchedule,proto3" json:"auto_schedule,omitempty"`
	DeployValues   map[string]string `protobuf:"bytes,22,rep,name=deploy_values,json=deployValues,proto3" json:"deploy_values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ProjectId      string            `protobuf:"bytes,23,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Name           string            `protobuf:"bytes,24,opt,name=name,proto3" json:"name,omitempty"`           // 部署的名称，默认为{model_name}_{release_version}
	SpaceInfo      *SpaceInfo        `protobuf:"bytes,25,opt,name=SpaceInfo,proto3" json:"SpaceInfo,omitempty"` // 是否首页被选中为某行业的精选
}

func (x *Deployment) Reset() {
	*x = Deployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment) ProtoMessage() {}

func (x *Deployment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment.ProtoReflect.Descriptor instead.
func (*Deployment) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{35}
}

func (x *Deployment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Deployment) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *Deployment) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *Deployment) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *Deployment) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *Deployment) GetSysId() string {
	if x != nil {
		return x.SysId
	}
	return ""
}

func (x *Deployment) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Deployment) GetReplica() int64 {
	if x != nil {
		return x.Replica
	}
	return 0
}

func (x *Deployment) GetRuntimeType() ModelRuntimeType {
	if x != nil {
		return x.RuntimeType
	}
	return ModelRuntimeType_MODEL_RUNTIME_TYPE_UNSPECIFIED
}

func (x *Deployment) GetResource() *DeployResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *Deployment) GetSelector() *DeploySelector {
	if x != nil {
		return x.Selector
	}
	return nil
}

func (x *Deployment) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *Deployment) GetDetail() *DeploymentDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *Deployment) GetStatus() *Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Deployment) GetType() DeploymentType {
	if x != nil {
		return x.Type
	}
	return DeploymentType_DEPLOYMENT_TYPE_UNSPECIFIED
}

func (x *Deployment) GetPortMapStart() int32 {
	if x != nil {
		return x.PortMapStart
	}
	return 0
}

func (x *Deployment) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *Deployment) GetStopTimeMs() int64 {
	if x != nil {
		return x.StopTimeMs
	}
	return 0
}

func (x *Deployment) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Deployment) GetReleaseVersion() string {
	if x != nil {
		return x.ReleaseVersion
	}
	return ""
}

func (x *Deployment) GetAutoSchedule() bool {
	if x != nil {
		return x.AutoSchedule
	}
	return false
}

func (x *Deployment) GetDeployValues() map[string]string {
	if x != nil {
		return x.DeployValues
	}
	return nil
}

func (x *Deployment) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Deployment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deployment) GetSpaceInfo() *SpaceInfo {
	if x != nil {
		return x.SpaceInfo
	}
	return nil
}

// LifeCycle 记录资源生命周期的结构
type LifeCycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LastQueryMs    int64  `protobuf:"varint,2,opt,name=last_query_ms,json=lastQueryMs,proto3" json:"last_query_ms,omitempty"`
	CloseTimeoutMs int64  `protobuf:"varint,3,opt,name=close_timeout_ms,json=closeTimeoutMs,proto3" json:"close_timeout_ms,omitempty"`
}

func (x *LifeCycle) Reset() {
	*x = LifeCycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LifeCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LifeCycle) ProtoMessage() {}

func (x *LifeCycle) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LifeCycle.ProtoReflect.Descriptor instead.
func (*LifeCycle) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{36}
}

func (x *LifeCycle) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LifeCycle) GetLastQueryMs() int64 {
	if x != nil {
		return x.LastQueryMs
	}
	return 0
}

func (x *LifeCycle) GetCloseTimeoutMs() int64 {
	if x != nil {
		return x.CloseTimeoutMs
	}
	return 0
}

// ModelDeployment 模型及其关联的部署
type ModelDeployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model      *Model        `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	Deployment *Deployment   `protobuf:"bytes,2,opt,name=deployment,proto3" json:"deployment,omitempty"`
	Release    *ModelRelease `protobuf:"bytes,3,opt,name=release,proto3" json:"release,omitempty"`
}

func (x *ModelDeployment) Reset() {
	*x = ModelDeployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDeployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDeployment) ProtoMessage() {}

func (x *ModelDeployment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDeployment.ProtoReflect.Descriptor instead.
func (*ModelDeployment) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{37}
}

func (x *ModelDeployment) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *ModelDeployment) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

func (x *ModelDeployment) GetRelease() *ModelRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

// NodeStats 节点资源的动态统计数据
type NodeStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeStamp int64                     `protobuf:"varint,1,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Name      string                    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CpuStats  *ResourceStats            `protobuf:"bytes,3,opt,name=cpu_stats,json=cpuStats,proto3" json:"cpu_stats,omitempty"`
	MemStats  *ResourceStats            `protobuf:"bytes,4,opt,name=mem_stats,json=memStats,proto3" json:"mem_stats,omitempty"`
	DiskStats *ResourceStats            `protobuf:"bytes,5,opt,name=disk_stats,json=diskStats,proto3" json:"disk_stats,omitempty"`
	GpusStats map[string]*ResourceStats `protobuf:"bytes,6,rep,name=gpus_stats,json=gpusStats,proto3" json:"gpus_stats,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NodeStats) Reset() {
	*x = NodeStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStats) ProtoMessage() {}

func (x *NodeStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStats.ProtoReflect.Descriptor instead.
func (*NodeStats) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{38}
}

func (x *NodeStats) GetTimeStamp() int64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (x *NodeStats) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeStats) GetCpuStats() *ResourceStats {
	if x != nil {
		return x.CpuStats
	}
	return nil
}

func (x *NodeStats) GetMemStats() *ResourceStats {
	if x != nil {
		return x.MemStats
	}
	return nil
}

func (x *NodeStats) GetDiskStats() *ResourceStats {
	if x != nil {
		return x.DiskStats
	}
	return nil
}

func (x *NodeStats) GetGpusStats() map[string]*ResourceStats {
	if x != nil {
		return x.GpusStats
	}
	return nil
}

type ResourceStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total        int64   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"` // 资源总量 cpu单位 core 存储相关（内存，磁盘） byte
	UsagePercent float32 `protobuf:"fixed32,2,opt,name=usage_percent,json=usagePercent,proto3" json:"usage_percent,omitempty"`
}

func (x *ResourceStats) Reset() {
	*x = ResourceStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceStats) ProtoMessage() {}

func (x *ResourceStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceStats.ProtoReflect.Descriptor instead.
func (*ResourceStats) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{39}
}

func (x *ResourceStats) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResourceStats) GetUsagePercent() float32 {
	if x != nil {
		return x.UsagePercent
	}
	return 0
}

// NodeSystemInfo 描述节点系统信息
type SystemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId               string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	SystemUuid              string `protobuf:"bytes,2,opt,name=system_uuid,json=systemUuid,proto3" json:"system_uuid,omitempty"`
	BootId                  string `protobuf:"bytes,3,opt,name=boot_id,json=bootId,proto3" json:"boot_id,omitempty"`
	KernelVersion           string `protobuf:"bytes,4,opt,name=kernel_version,json=kernelVersion,proto3" json:"kernel_version,omitempty"`
	OsImage                 string `protobuf:"bytes,5,opt,name=os_image,json=osImage,proto3" json:"os_image,omitempty"`
	Os                      string `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`
	ContainerRuntimeVersion string `protobuf:"bytes,8,opt,name=container_runtime_version,json=containerRuntimeVersion,proto3" json:"container_runtime_version,omitempty"`
	KubeletVersion          string `protobuf:"bytes,9,opt,name=kubelet_version,json=kubeletVersion,proto3" json:"kubelet_version,omitempty"`
}

func (x *SystemInfo) Reset() {
	*x = SystemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemInfo) ProtoMessage() {}

func (x *SystemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemInfo.ProtoReflect.Descriptor instead.
func (*SystemInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{40}
}

func (x *SystemInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *SystemInfo) GetSystemUuid() string {
	if x != nil {
		return x.SystemUuid
	}
	return ""
}

func (x *SystemInfo) GetBootId() string {
	if x != nil {
		return x.BootId
	}
	return ""
}

func (x *SystemInfo) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

func (x *SystemInfo) GetOsImage() string {
	if x != nil {
		return x.OsImage
	}
	return ""
}

func (x *SystemInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SystemInfo) GetContainerRuntimeVersion() string {
	if x != nil {
		return x.ContainerRuntimeVersion
	}
	return ""
}

func (x *SystemInfo) GetKubeletVersion() string {
	if x != nil {
		return x.KubeletVersion
	}
	return ""
}

// NodeInfo 节点硬件与资源信息
type NodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                     // 节点名
	Address      string            `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`                               // 节点集群内IP
	Hostname     string            `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname,omitempty"`                             // 节点集群内域名
	Roles        []NodeRole        `protobuf:"varint,4,rep,packed,name=roles,proto3,enum=proto.NodeRole" json:"roles,omitempty"`       // 节点角色, 支持多种角色【e.g. 评估, 部署, 体验 etc ...】
	Capacity     *HardwareResource `protobuf:"bytes,5,opt,name=capacity,proto3" json:"capacity,omitempty"`                             // 节点资源总量
	Allocatable  *HardwareResource `protobuf:"bytes,6,opt,name=allocatable,proto3" json:"allocatable,omitempty"`                       // 节点可分配资源剩余量
	SystemInfo   *SystemInfo       `protobuf:"bytes,7,opt,name=system_info,json=systemInfo,proto3" json:"system_info,omitempty"`       // 节点系统信息
	HardwareInfo *HardwareInfo     `protobuf:"bytes,8,opt,name=hardware_info,json=hardwareInfo,proto3" json:"hardware_info,omitempty"` // 节点硬件架构信息
	NodeStats    *NodeStats        `protobuf:"bytes,10,opt,name=node_stats,json=nodeStats,proto3" json:"node_stats,omitempty"`         // 节点实时统计信息
}

func (x *NodeInfo) Reset() {
	*x = NodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeInfo) ProtoMessage() {}

func (x *NodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeInfo.ProtoReflect.Descriptor instead.
func (*NodeInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{41}
}

func (x *NodeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *NodeInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *NodeInfo) GetRoles() []NodeRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *NodeInfo) GetCapacity() *HardwareResource {
	if x != nil {
		return x.Capacity
	}
	return nil
}

func (x *NodeInfo) GetAllocatable() *HardwareResource {
	if x != nil {
		return x.Allocatable
	}
	return nil
}

func (x *NodeInfo) GetSystemInfo() *SystemInfo {
	if x != nil {
		return x.SystemInfo
	}
	return nil
}

func (x *NodeInfo) GetHardwareInfo() *HardwareInfo {
	if x != nil {
		return x.HardwareInfo
	}
	return nil
}

func (x *NodeInfo) GetNodeStats() *NodeStats {
	if x != nil {
		return x.NodeStats
	}
	return nil
}

// HardwareInfo 硬件信息
type HardwareInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpuArch  CPUArch            `protobuf:"varint,1,opt,name=cpu_arch,json=cpuArch,proto3,enum=proto.CPUArch" json:"cpu_arch,omitempty"` // CPU架构
	AccCards []*AcceleratorCard `protobuf:"bytes,2,rep,name=acc_cards,json=accCards,proto3" json:"acc_cards,omitempty"`                  // 硬件包含的加速卡列表( 保留不同类型加速卡混部的可能性， 粒度为单张卡 )
}

func (x *HardwareInfo) Reset() {
	*x = HardwareInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HardwareInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HardwareInfo) ProtoMessage() {}

func (x *HardwareInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HardwareInfo.ProtoReflect.Descriptor instead.
func (*HardwareInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{42}
}

func (x *HardwareInfo) GetCpuArch() CPUArch {
	if x != nil {
		return x.CpuArch
	}
	return CPUArch_CPU_ARCH_UNSPECIFIED
}

func (x *HardwareInfo) GetAccCards() []*AcceleratorCard {
	if x != nil {
		return x.AccCards
	}
	return nil
}

// HardwareRange 定义了硬件范围，可用于模型/Runtime创建时生命所支持的硬件列表
type HardwareRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Arches   []CPUArch         `protobuf:"varint,1,rep,packed,name=arches,proto3,enum=proto.CPUArch" json:"arches,omitempty"`
	AccTypes []AcceleratorType `protobuf:"varint,2,rep,packed,name=acc_types,json=accTypes,proto3,enum=proto.AcceleratorType" json:"acc_types,omitempty"`
}

func (x *HardwareRange) Reset() {
	*x = HardwareRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HardwareRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HardwareRange) ProtoMessage() {}

func (x *HardwareRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HardwareRange.ProtoReflect.Descriptor instead.
func (*HardwareRange) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{43}
}

func (x *HardwareRange) GetArches() []CPUArch {
	if x != nil {
		return x.Arches
	}
	return nil
}

func (x *HardwareRange) GetAccTypes() []AcceleratorType {
	if x != nil {
		return x.AccTypes
	}
	return nil
}

// DeploySys 对应一个模型部署系统，集群Level, 每个集群可以具备 0-* 个 DeploySys, 之间的资源可能存在竞争
type DeploySys struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Nodes        []*NodeInfo     `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Runtimes     []*ModelRuntime `protobuf:"bytes,4,rep,name=runtimes,proto3" json:"runtimes,omitempty"`
	CreateTimeMs int64           `protobuf:"varint,5,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
	UpdateTimeMs int64           `protobuf:"varint,6,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
}

func (x *DeploySys) Reset() {
	*x = DeploySys{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploySys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploySys) ProtoMessage() {}

func (x *DeploySys) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploySys.ProtoReflect.Descriptor instead.
func (*DeploySys) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{44}
}

func (x *DeploySys) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeploySys) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeploySys) GetNodes() []*NodeInfo {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *DeploySys) GetRuntimes() []*ModelRuntime {
	if x != nil {
		return x.Runtimes
	}
	return nil
}

func (x *DeploySys) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *DeploySys) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

// HardwareResource 硬件资源信息，用于描述 机器具备的硬件资源、机器尚可分配的资源、模型部署请求的资源等
// TODO GPU虚拟化：
//
//	主要依托于TCOS的Krux方案, 支持多个容器同时使用一块GPU。(参考https://wiki.transwarp.io/pages/viewpage.action?pageId=29083993)
//	vGPU Cores来表示GPU的算力（即平均GPU使用率），主要决定了该CUDA应用的运行性能，
//	vGPU Memory来表示GPU的显存，主要决定了该CUDA应用是否有足够的显存来正常运行。
//
// 虚拟化限制：
// * 仅支持在TOS中使用，且要求TOS版本>= 3.0
// * 仅支持Nvidia GPU
// * 节点必须安装Nvidia GPU卡及Nvidia(内核)驱动，且要求Nvida驱动版本>= 460.32.03
// * 不支持创建Pod时直接指定spec.nodeName
// * 不支持在GPU独占模式下配置显存限制
// * 在容器内禁止覆盖LD_LIBRARY_PATH环境变量
// * 容器禁止使用特权模式
type HardwareResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cpus    int64 `protobuf:"varint,1,opt,name=cpus,proto3" json:"cpus,omitempty"`                      // cpu, 单位 0.001 core
	Memory  int64 `protobuf:"varint,2,opt,name=memory,proto3" json:"memory,omitempty"`                  // 主存, 单位 1 byte
	Vgpus   int64 `protobuf:"varint,3,opt,name=vgpus,proto3" json:"vgpus,omitempty"`                    // 虚拟gpu个数, 单位 1% gpu 算力 // TODO 具体与GPU映射关系由不同虚拟化插件决定
	VgpuMem int64 `protobuf:"varint,4,opt,name=vgpu_mem,json=vgpuMem,proto3" json:"vgpu_mem,omitempty"` // 虚拟gpu显存, 单位 1 byte
}

func (x *HardwareResource) Reset() {
	*x = HardwareResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HardwareResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HardwareResource) ProtoMessage() {}

func (x *HardwareResource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HardwareResource.ProtoReflect.Descriptor instead.
func (*HardwareResource) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{45}
}

func (x *HardwareResource) GetCpus() int64 {
	if x != nil {
		return x.Cpus
	}
	return 0
}

func (x *HardwareResource) GetMemory() int64 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *HardwareResource) GetVgpus() int64 {
	if x != nil {
		return x.Vgpus
	}
	return 0
}

func (x *HardwareResource) GetVgpuMem() int64 {
	if x != nil {
		return x.VgpuMem
	}
	return 0
}

// DeploySelector 模型部署的目标节点与GPU
type DeploySelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*NodeSelector `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"` // 节点选择范围
}

func (x *DeploySelector) Reset() {
	*x = DeploySelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploySelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploySelector) ProtoMessage() {}

func (x *DeploySelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploySelector.ProtoReflect.Descriptor instead.
func (*DeploySelector) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{46}
}

func (x *DeploySelector) GetNodes() []*NodeSelector {
	if x != nil {
		return x.Nodes
	}
	return nil
}

// NodeSelector 允许资源在某个节点上进行调度，以及限制了可用的加速卡列表
type NodeSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId       string   `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"` // 可用节点的ID
	Accelerators []string `protobuf:"bytes,2,rep,name=accelerators,proto3" json:"accelerators,omitempty"`   // 该节点上可用的加速卡 ID列表
}

func (x *NodeSelector) Reset() {
	*x = NodeSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSelector) ProtoMessage() {}

func (x *NodeSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSelector.ProtoReflect.Descriptor instead.
func (*NodeSelector) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{47}
}

func (x *NodeSelector) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeSelector) GetAccelerators() []string {
	if x != nil {
		return x.Accelerators
	}
	return nil
}

// DeployResource 模型部署的资源请求与限制
type DeployResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request     *HardwareResource         `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	Limit       *HardwareResource         `protobuf:"bytes,2,opt,name=limit,proto3" json:"limit,omitempty"`
	GpuType     string                    `protobuf:"bytes,5,opt,name=gpu_type,json=gpuType,proto3" json:"gpu_type,omitempty"`
	GpuCards    []string                  `protobuf:"bytes,6,rep,name=gpu_cards,json=gpuCards,proto3" json:"gpu_cards,omitempty"`
	ResourceId  int32                     `protobuf:"varint,3,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	IsPowerRule bool                      `protobuf:"varint,4,opt,name=is_power_rule,json=isPowerRule,proto3" json:"is_power_rule,omitempty"`
	AscendCfg   *common.AscendResourceCfg `protobuf:"bytes,7,opt,name=ascend_cfg,json=ascendCfg,proto3" json:"ascend_cfg,omitempty"`
	Arch        string                    `protobuf:"bytes,8,opt,name=arch,proto3" json:"arch,omitempty"`
}

func (x *DeployResource) Reset() {
	*x = DeployResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployResource) ProtoMessage() {}

func (x *DeployResource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployResource.ProtoReflect.Descriptor instead.
func (*DeployResource) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{48}
}

func (x *DeployResource) GetRequest() *HardwareResource {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *DeployResource) GetLimit() *HardwareResource {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *DeployResource) GetGpuType() string {
	if x != nil {
		return x.GpuType
	}
	return ""
}

func (x *DeployResource) GetGpuCards() []string {
	if x != nil {
		return x.GpuCards
	}
	return nil
}

func (x *DeployResource) GetResourceId() int32 {
	if x != nil {
		return x.ResourceId
	}
	return 0
}

func (x *DeployResource) GetIsPowerRule() bool {
	if x != nil {
		return x.IsPowerRule
	}
	return false
}

func (x *DeployResource) GetAscendCfg() *common.AscendResourceCfg {
	if x != nil {
		return x.AscendCfg
	}
	return nil
}

func (x *DeployResource) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

// AcceleratorCard 加速卡定义
type AcceleratorCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid   string          `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`                             // 加速卡UUID
	Index  int64           `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`                          // 加速卡序号
	Model  string          `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`                           // 加速卡型号
	Memory int64           `protobuf:"varint,4,opt,name=memory,proto3" json:"memory,omitempty"`                        // 显存总量，单位 1 Byte
	Type   AcceleratorType `protobuf:"varint,8,opt,name=type,proto3,enum=proto.AcceleratorType" json:"type,omitempty"` // 加速卡类别
}

func (x *AcceleratorCard) Reset() {
	*x = AcceleratorCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceleratorCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceleratorCard) ProtoMessage() {}

func (x *AcceleratorCard) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceleratorCard.ProtoReflect.Descriptor instead.
func (*AcceleratorCard) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{49}
}

func (x *AcceleratorCard) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AcceleratorCard) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AcceleratorCard) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *AcceleratorCard) GetMemory() int64 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *AcceleratorCard) GetType() AcceleratorType {
	if x != nil {
		return x.Type
	}
	return AcceleratorType_ACCELERATOR_TYPE_UNSPECIFIED
}

// ModelRuntime 对应一个模型部署启动器，每个DeploySys 可以支持 0-* 种ModelRuntime
type ModelRuntime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuntimeType   ModelRuntimeType `protobuf:"varint,1,opt,name=runtime_type,json=runtimeType,proto3,enum=proto.ModelRuntimeType" json:"runtime_type,omitempty"` // 当前Runtime名称
	RuntimeParams []*DynamicParam  `protobuf:"bytes,2,rep,name=runtime_params,json=runtimeParams,proto3" json:"runtime_params,omitempty"`                        // 部署时需要提供的动态参数列表
	Types         []ModelType      `protobuf:"varint,3,rep,packed,name=types,proto3,enum=proto.ModelType" json:"types,omitempty"`                                // 支持部署的ModelTypes
	SubTypes      []ModelSubType   `protobuf:"varint,4,rep,packed,name=sub_types,json=subTypes,proto3,enum=proto.ModelSubType" json:"sub_types,omitempty"`       // 支持部署的ModelSubTypes
	HardwareRange *HardwareRange   `protobuf:"bytes,5,opt,name=hardware_range,json=hardwareRange,proto3" json:"hardware_range,omitempty"`                        // 支持调度的硬件架构范围
}

func (x *ModelRuntime) Reset() {
	*x = ModelRuntime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRuntime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRuntime) ProtoMessage() {}

func (x *ModelRuntime) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRuntime.ProtoReflect.Descriptor instead.
func (*ModelRuntime) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{50}
}

func (x *ModelRuntime) GetRuntimeType() ModelRuntimeType {
	if x != nil {
		return x.RuntimeType
	}
	return ModelRuntimeType_MODEL_RUNTIME_TYPE_UNSPECIFIED
}

func (x *ModelRuntime) GetRuntimeParams() []*DynamicParam {
	if x != nil {
		return x.RuntimeParams
	}
	return nil
}

func (x *ModelRuntime) GetTypes() []ModelType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ModelRuntime) GetSubTypes() []ModelSubType {
	if x != nil {
		return x.SubTypes
	}
	return nil
}

func (x *ModelRuntime) GetHardwareRange() *HardwareRange {
	if x != nil {
		return x.HardwareRange
	}
	return nil
}

// ModelService的数组形式
type ModelServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelService []*ModelService `protobuf:"bytes,1,rep,name=model_service,json=modelService,proto3" json:"model_service,omitempty"`
}

func (x *ModelServices) Reset() {
	*x = ModelServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelServices) ProtoMessage() {}

func (x *ModelServices) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelServices.ProtoReflect.Descriptor instead.
func (*ModelServices) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{51}
}

func (x *ModelServices) GetModelService() []*ModelService {
	if x != nil {
		return x.ModelService
	}
	return nil
}

// ModelService 部署粒度的服务，一个部署可能对应一个k8s service，可能有多个接口与路径
type ModelService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                        // id 为产生该服务的Deployment id
	Schema                 ModelServiceSchema        `protobuf:"varint,2,opt,name=schema,proto3,enum=proto.ModelServiceSchema" json:"schema,omitempty"` // 使用Triton时 协议为 dlie:// 其他 http://
	Host                   string                    `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`                                    // k8s中service name，为规范后的Deployment id
	Port                   int32                     `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`                                   // 默认每个模型服务仅暴露一个端口，不同服务模型层面的api划分
	Type                   ModelServiceType          `protobuf:"varint,5,opt,name=type,proto3,enum=proto.ModelServiceType" json:"type,omitempty"`
	Apis                   []*ModelApi               `protobuf:"bytes,6,rep,name=apis,proto3" json:"apis,omitempty"` // 本地启动的模型服务不做存储，避免与模型层面数据不一致
	Status                 *Status                   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	Kind                   ModelKind                 `protobuf:"varint,8,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind                ModelSubKind              `protobuf:"varint,9,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	ModelName              string                    `protobuf:"bytes,10,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ReleaseName            string                    `protobuf:"bytes,11,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ReleaseVersion         string                    `protobuf:"bytes,12,opt,name=release_version,json=releaseVersion,proto3" json:"release_version,omitempty"`
	ModelId                string                    `protobuf:"bytes,13,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId              string                    `protobuf:"bytes,14,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	InferenceParams        []*DynamicParam           `protobuf:"bytes,15,rep,name=inference_params,json=inferenceParams,proto3" json:"inference_params,omitempty"`
	Prompt                 string                    `protobuf:"bytes,16,opt,name=prompt,proto3" json:"prompt,omitempty"`
	Namespace              string                    `protobuf:"bytes,17,opt,name=namespace,proto3" json:"namespace,omitempty"`
	SeldonDeployName       string                    `protobuf:"bytes,18,opt,name=seldon_deploy_name,json=seldonDeployName,proto3" json:"seldon_deploy_name,omitempty"`
	Name                   string                    `protobuf:"bytes,19,opt,name=name,proto3" json:"name,omitempty"`
	FullUrl                string                    `protobuf:"bytes,20,opt,name=full_url,json=fullUrl,proto3" json:"full_url,omitempty"`                  // 模型服务调用的完整URL，当使用seldon部署时，{istio-gateway-host}:{istio-gateway-port}/{namespace}/{seldon_deploy_name}
	InvokeAsTool           *InvokeAsTool             `protobuf:"bytes,21,opt,name=invoke_as_tool,json=invokeAsTool,proto3" json:"invoke_as_tool,omitempty"` // 模型服务作为工具调用时需要传入的参数与描述
	RemoteServiceConfig    *RemoteServiceConfig      `protobuf:"bytes,22,opt,name=remote_service_config,json=remoteServiceConfig,proto3" json:"remote_service_config,omitempty"`
	Desc                   string                    `protobuf:"bytes,23,opt,name=desc,proto3" json:"desc,omitempty"`
	CreateTimeMs           int64                     `protobuf:"varint,24,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
	ReferenceModel         *Model                    `protobuf:"bytes,25,opt,name=reference_model,json=referenceModel,proto3" json:"reference_model,omitempty"` // 带模型的参考信息
	ReferenceRelease       *ModelRelease             `protobuf:"bytes,26,opt,name=reference_release,json=referenceRelease,proto3" json:"reference_release,omitempty"`
	ProjectId              string                    `protobuf:"bytes,27,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ReferenceRemoteService *RemoteService            `protobuf:"bytes,28,opt,name=reference_remote_service,json=referenceRemoteService,proto3" json:"reference_remote_service,omitempty"`
	UpdateTimeMs           int64                     `protobuf:"varint,29,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
	GuardrailsConfig       *serving.GuardrailsConfig `protobuf:"bytes,30,opt,name=guardrails_config,json=guardrailsConfig,proto3" json:"guardrails_config,omitempty"`
	CustomServiceEndpoints []*common.Endpoint        `protobuf:"bytes,31,rep,name=custom_service_endpoints,json=customServiceEndpoints,proto3" json:"custom_service_endpoints,omitempty"`
}

func (x *ModelService) Reset() {
	*x = ModelService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelService) ProtoMessage() {}

func (x *ModelService) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelService.ProtoReflect.Descriptor instead.
func (*ModelService) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{52}
}

func (x *ModelService) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModelService) GetSchema() ModelServiceSchema {
	if x != nil {
		return x.Schema
	}
	return ModelServiceSchema_MODEL_SERVICE_SCHEMA_UNSPECIFIED
}

func (x *ModelService) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ModelService) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ModelService) GetType() ModelServiceType {
	if x != nil {
		return x.Type
	}
	return ModelServiceType_MODEL_SERVICE_TYPE_UNSPECIFIED
}

func (x *ModelService) GetApis() []*ModelApi {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *ModelService) GetStatus() *Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ModelService) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *ModelService) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ModelService) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelService) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ModelService) GetReleaseVersion() string {
	if x != nil {
		return x.ReleaseVersion
	}
	return ""
}

func (x *ModelService) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelService) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ModelService) GetInferenceParams() []*DynamicParam {
	if x != nil {
		return x.InferenceParams
	}
	return nil
}

func (x *ModelService) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *ModelService) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ModelService) GetSeldonDeployName() string {
	if x != nil {
		return x.SeldonDeployName
	}
	return ""
}

func (x *ModelService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelService) GetFullUrl() string {
	if x != nil {
		return x.FullUrl
	}
	return ""
}

func (x *ModelService) GetInvokeAsTool() *InvokeAsTool {
	if x != nil {
		return x.InvokeAsTool
	}
	return nil
}

func (x *ModelService) GetRemoteServiceConfig() *RemoteServiceConfig {
	if x != nil {
		return x.RemoteServiceConfig
	}
	return nil
}

func (x *ModelService) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModelService) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *ModelService) GetReferenceModel() *Model {
	if x != nil {
		return x.ReferenceModel
	}
	return nil
}

func (x *ModelService) GetReferenceRelease() *ModelRelease {
	if x != nil {
		return x.ReferenceRelease
	}
	return nil
}

func (x *ModelService) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ModelService) GetReferenceRemoteService() *RemoteService {
	if x != nil {
		return x.ReferenceRemoteService
	}
	return nil
}

func (x *ModelService) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *ModelService) GetGuardrailsConfig() *serving.GuardrailsConfig {
	if x != nil {
		return x.GuardrailsConfig
	}
	return nil
}

func (x *ModelService) GetCustomServiceEndpoints() []*common.Endpoint {
	if x != nil {
		return x.CustomServiceEndpoints
	}
	return nil
}

type InvokeAsTool struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameForModel string                   `protobuf:"bytes,1,opt,name=name_for_model,json=nameForModel,proto3" json:"name_for_model,omitempty"`
	NameForHuman string                   `protobuf:"bytes,2,opt,name=name_for_human,json=nameForHuman,proto3" json:"name_for_human,omitempty"`
	Desc         string                   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	InvokeMethod ModelServiceInvokeMethod `protobuf:"varint,4,opt,name=invoke_method,json=invokeMethod,proto3,enum=proto.ModelServiceInvokeMethod" json:"invoke_method,omitempty"`
	InvokeParams []*InvokeParam           `protobuf:"bytes,5,rep,name=invoke_params,json=invokeParams,proto3" json:"invoke_params,omitempty"`
}

func (x *InvokeAsTool) Reset() {
	*x = InvokeAsTool{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvokeAsTool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvokeAsTool) ProtoMessage() {}

func (x *InvokeAsTool) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvokeAsTool.ProtoReflect.Descriptor instead.
func (*InvokeAsTool) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{53}
}

func (x *InvokeAsTool) GetNameForModel() string {
	if x != nil {
		return x.NameForModel
	}
	return ""
}

func (x *InvokeAsTool) GetNameForHuman() string {
	if x != nil {
		return x.NameForHuman
	}
	return ""
}

func (x *InvokeAsTool) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *InvokeAsTool) GetInvokeMethod() ModelServiceInvokeMethod {
	if x != nil {
		return x.InvokeMethod
	}
	return ModelServiceInvokeMethod_MODEL_SERVICE_INVOKE_METHOD_SYNC
}

func (x *InvokeAsTool) GetInvokeParams() []*InvokeParam {
	if x != nil {
		return x.InvokeParams
	}
	return nil
}

type InvokeParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DefaultValue string `protobuf:"bytes,2,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	Type         string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Desc         string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Required     bool   `protobuf:"varint,5,opt,name=required,proto3" json:"required,omitempty"`
}

func (x *InvokeParam) Reset() {
	*x = InvokeParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvokeParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvokeParam) ProtoMessage() {}

func (x *InvokeParam) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvokeParam.ProtoReflect.Descriptor instead.
func (*InvokeParam) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{54}
}

func (x *InvokeParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InvokeParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *InvokeParam) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InvokeParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *InvokeParam) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type RemoteServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Desc         string            `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                             // desc 部署描述信息
	UserId       string            `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                           // user_id 部署创建者
	CreateTimeMs int64             `protobuf:"varint,5,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`                                                      // create_time_ms 创建时间戳
	UpdateTimeMs int64             `protobuf:"varint,6,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`                                                      // update_time_ms 更新时间戳
	Labels       map[string]string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // labels 模型标签列表
}

func (x *RemoteServiceDetail) Reset() {
	*x = RemoteServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteServiceDetail) ProtoMessage() {}

func (x *RemoteServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteServiceDetail.ProtoReflect.Descriptor instead.
func (*RemoteServiceDetail) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{55}
}

func (x *RemoteServiceDetail) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RemoteServiceDetail) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RemoteServiceDetail) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *RemoteServiceDetail) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *RemoteServiceDetail) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type RemoteServiceProxy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scheme ProxyScheme `protobuf:"varint,1,opt,name=scheme,proto3,enum=proto.ProxyScheme" json:"scheme,omitempty"`
	Url    string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *RemoteServiceProxy) Reset() {
	*x = RemoteServiceProxy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteServiceProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteServiceProxy) ProtoMessage() {}

func (x *RemoteServiceProxy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteServiceProxy.ProtoReflect.Descriptor instead.
func (*RemoteServiceProxy) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{56}
}

func (x *RemoteServiceProxy) GetScheme() ProxyScheme {
	if x != nil {
		return x.Scheme
	}
	return ProxyScheme_PROXY_SCHEME_UNSPECIFIED
}

func (x *RemoteServiceProxy) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Configurable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Desc  string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *Configurable) Reset() {
	*x = Configurable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Configurable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Configurable) ProtoMessage() {}

func (x *Configurable) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Configurable.ProtoReflect.Descriptor instead.
func (*Configurable) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{57}
}

func (x *Configurable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Configurable) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Configurable) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type RemoteServiceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method                RemoteServiceMethod `protobuf:"varint,1,opt,name=method,proto3,enum=proto.RemoteServiceMethod" json:"method,omitempty"`
	Url                   string              `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	QueryParams           []*Configurable     `protobuf:"bytes,3,rep,name=query_params,json=queryParams,proto3" json:"query_params,omitempty"`
	PathParams            []*Configurable     `protobuf:"bytes,4,rep,name=path_params,json=pathParams,proto3" json:"path_params,omitempty"` // 路径参数，这里只存储，由前端填充好url
	Headers               []*Configurable     `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers,omitempty"`
	Body                  string              `protobuf:"bytes,6,opt,name=body,proto3" json:"body,omitempty"`
	UseProxy              bool                `protobuf:"varint,7,opt,name=use_proxy,json=useProxy,proto3" json:"use_proxy,omitempty"`
	Proxy                 *RemoteServiceProxy `protobuf:"bytes,8,opt,name=proxy,proto3" json:"proxy,omitempty"`
	FullUrl               string              `protobuf:"bytes,10,opt,name=full_url,json=fullUrl,proto3" json:"full_url,omitempty"`
	ComputationAttributes map[string]string   `protobuf:"bytes,11,rep,name=computation_attributes,json=computationAttributes,proto3" json:"computation_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	InterfaceSpec         InterfaceSpecName   `protobuf:"varint,12,opt,name=interface_spec,json=interfaceSpec,proto3,enum=proto.InterfaceSpecName" json:"interface_spec,omitempty"`
	RequestProcessScript  string              `protobuf:"bytes,13,opt,name=request_process_script,json=requestProcessScript,proto3" json:"request_process_script,omitempty"` // 请求的处理jsonnet脚本
	ResponseProcessScript string              `protobuf:"bytes,14,opt,name=response_process_script,json=responseProcessScript,proto3" json:"response_process_script,omitempty"`
}

func (x *RemoteServiceConfig) Reset() {
	*x = RemoteServiceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteServiceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteServiceConfig) ProtoMessage() {}

func (x *RemoteServiceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteServiceConfig.ProtoReflect.Descriptor instead.
func (*RemoteServiceConfig) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{58}
}

func (x *RemoteServiceConfig) GetMethod() RemoteServiceMethod {
	if x != nil {
		return x.Method
	}
	return RemoteServiceMethod_REMOTE_SERVICE_METHOD_UNSPECIFIED
}

func (x *RemoteServiceConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RemoteServiceConfig) GetQueryParams() []*Configurable {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *RemoteServiceConfig) GetPathParams() []*Configurable {
	if x != nil {
		return x.PathParams
	}
	return nil
}

func (x *RemoteServiceConfig) GetHeaders() []*Configurable {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *RemoteServiceConfig) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *RemoteServiceConfig) GetUseProxy() bool {
	if x != nil {
		return x.UseProxy
	}
	return false
}

func (x *RemoteServiceConfig) GetProxy() *RemoteServiceProxy {
	if x != nil {
		return x.Proxy
	}
	return nil
}

func (x *RemoteServiceConfig) GetFullUrl() string {
	if x != nil {
		return x.FullUrl
	}
	return ""
}

func (x *RemoteServiceConfig) GetComputationAttributes() map[string]string {
	if x != nil {
		return x.ComputationAttributes
	}
	return nil
}

func (x *RemoteServiceConfig) GetInterfaceSpec() InterfaceSpecName {
	if x != nil {
		return x.InterfaceSpec
	}
	return InterfaceSpecName_INTERFACE_SPEC_OTHERS
}

func (x *RemoteServiceConfig) GetRequestProcessScript() string {
	if x != nil {
		return x.RequestProcessScript
	}
	return ""
}

func (x *RemoteServiceConfig) GetResponseProcessScript() string {
	if x != nil {
		return x.ResponseProcessScript
	}
	return ""
}

type RemoteServiceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsReachable bool   `protobuf:"varint,1,opt,name=is_reachable,json=isReachable,proto3" json:"is_reachable,omitempty"`
	Msg         string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *RemoteServiceStatus) Reset() {
	*x = RemoteServiceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteServiceStatus) ProtoMessage() {}

func (x *RemoteServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteServiceStatus.ProtoReflect.Descriptor instead.
func (*RemoteServiceStatus) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{59}
}

func (x *RemoteServiceStatus) GetIsReachable() bool {
	if x != nil {
		return x.IsReachable
	}
	return false
}

func (x *RemoteServiceStatus) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type Member struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserType string `protobuf:"bytes,2,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
}

func (x *Member) Reset() {
	*x = Member{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Member) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Member) ProtoMessage() {}

func (x *Member) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Member.ProtoReflect.Descriptor instead.
func (*Member) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{60}
}

func (x *Member) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Member) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type RemoteServicePublishInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                string             `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	RateLimit           *serving.RateLimit `protobuf:"bytes,3,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty"`
	IsSecurity          bool               `protobuf:"varint,4,opt,name=is_security,json=isSecurity,proto3" json:"is_security,omitempty"`
	Id                  string             `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	VirtualSvcUrl       string             `protobuf:"bytes,6,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url,omitempty"`
	SecurityConfigId    string             `protobuf:"bytes,7,opt,name=security_config_id,json=securityConfigId,proto3" json:"security_config_id,omitempty"`
	UserRateLimit       *serving.RateLimit `protobuf:"bytes,8,opt,name=user_rate_limit,json=userRateLimit,proto3" json:"user_rate_limit,omitempty"`
	Members             []*Member          `protobuf:"bytes,9,rep,name=members,proto3" json:"members,omitempty"`
	PublishedApi        string             `protobuf:"bytes,10,opt,name=published_api,json=publishedApi,proto3" json:"published_api,omitempty"`
	EnableAnonymousCall bool               `protobuf:"varint,11,opt,name=enable_anonymous_call,json=enableAnonymousCall,proto3" json:"enable_anonymous_call,omitempty"`
}

func (x *RemoteServicePublishInfo) Reset() {
	*x = RemoteServicePublishInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteServicePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteServicePublishInfo) ProtoMessage() {}

func (x *RemoteServicePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteServicePublishInfo.ProtoReflect.Descriptor instead.
func (*RemoteServicePublishInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{61}
}

func (x *RemoteServicePublishInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetRateLimit() *serving.RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *RemoteServicePublishInfo) GetIsSecurity() bool {
	if x != nil {
		return x.IsSecurity
	}
	return false
}

func (x *RemoteServicePublishInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetSecurityConfigId() string {
	if x != nil {
		return x.SecurityConfigId
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetUserRateLimit() *serving.RateLimit {
	if x != nil {
		return x.UserRateLimit
	}
	return nil
}

func (x *RemoteServicePublishInfo) GetMembers() []*Member {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *RemoteServicePublishInfo) GetPublishedApi() string {
	if x != nil {
		return x.PublishedApi
	}
	return ""
}

func (x *RemoteServicePublishInfo) GetEnableAnonymousCall() bool {
	if x != nil {
		return x.EnableAnonymousCall
	}
	return false
}

// RemoteService 用添加的自定可调用的远程模型服务
type RemoteService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Kind            ModelKind                 `protobuf:"varint,3,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind         ModelSubKind              `protobuf:"varint,4,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	Detail          *RemoteServiceDetail      `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	ApiConfig       *RemoteServiceConfig      `protobuf:"bytes,6,opt,name=api_config,json=apiConfig,proto3" json:"api_config,omitempty"`
	Status          *RemoteServiceStatus      `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	ProjectId       string                    `protobuf:"bytes,9,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ChatMode        bool                      `protobuf:"varint,10,opt,name=chat_mode,json=chatMode,proto3" json:"chat_mode,omitempty"`
	IsPublished     bool                      `protobuf:"varint,11,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
	PublishInfo     *RemoteServicePublishInfo `protobuf:"bytes,12,opt,name=publish_info,json=publishInfo,proto3" json:"publish_info,omitempty"`
	InferenceParams []*DynamicParam           `protobuf:"bytes,13,rep,name=inference_params,json=inferenceParams,proto3" json:"inference_params,omitempty"`
	Logo            string                    `protobuf:"bytes,15,opt,name=logo,proto3" json:"logo,omitempty"`
}

func (x *RemoteService) Reset() {
	*x = RemoteService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteService) ProtoMessage() {}

func (x *RemoteService) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteService.ProtoReflect.Descriptor instead.
func (*RemoteService) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{62}
}

func (x *RemoteService) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoteService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RemoteService) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *RemoteService) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *RemoteService) GetDetail() *RemoteServiceDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *RemoteService) GetApiConfig() *RemoteServiceConfig {
	if x != nil {
		return x.ApiConfig
	}
	return nil
}

func (x *RemoteService) GetStatus() *RemoteServiceStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RemoteService) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RemoteService) GetChatMode() bool {
	if x != nil {
		return x.ChatMode
	}
	return false
}

func (x *RemoteService) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

func (x *RemoteService) GetPublishInfo() *RemoteServicePublishInfo {
	if x != nil {
		return x.PublishInfo
	}
	return nil
}

func (x *RemoteService) GetInferenceParams() []*DynamicParam {
	if x != nil {
		return x.InferenceParams
	}
	return nil
}

func (x *RemoteService) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

type SupportedInterfaces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InterfaceSpecs []*InterfaceSpec `protobuf:"bytes,1,rep,name=interface_specs,json=interfaceSpecs,proto3" json:"interface_specs,omitempty"`
}

func (x *SupportedInterfaces) Reset() {
	*x = SupportedInterfaces{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportedInterfaces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedInterfaces) ProtoMessage() {}

func (x *SupportedInterfaces) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedInterfaces.ProtoReflect.Descriptor instead.
func (*SupportedInterfaces) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{63}
}

func (x *SupportedInterfaces) GetInterfaceSpecs() []*InterfaceSpec {
	if x != nil {
		return x.InterfaceSpecs
	}
	return nil
}

type InterfaceSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     InterfaceSpecName    `protobuf:"varint,1,opt,name=id,proto3,enum=proto.InterfaceSpecName" json:"id,omitempty"`
	Name                   string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Kind                   ModelKind            `protobuf:"varint,3,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind                ModelSubKind         `protobuf:"varint,4,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	RequestTemplate        string               `protobuf:"bytes,5,opt,name=request_template,json=requestTemplate,proto3" json:"request_template,omitempty"`
	ResponseTemplate       string               `protobuf:"bytes,6,opt,name=response_template,json=responseTemplate,proto3" json:"response_template,omitempty"`
	Desc                   string               `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	ApiConfigTemplate      *RemoteServiceConfig `protobuf:"bytes,8,opt,name=api_config_template,json=apiConfigTemplate,proto3" json:"api_config_template,omitempty"`
	DefaultInferenceParams []*DynamicParam      `protobuf:"bytes,9,rep,name=default_inference_params,json=defaultInferenceParams,proto3" json:"default_inference_params,omitempty"`
}

func (x *InterfaceSpec) Reset() {
	*x = InterfaceSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterfaceSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceSpec) ProtoMessage() {}

func (x *InterfaceSpec) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceSpec.ProtoReflect.Descriptor instead.
func (*InterfaceSpec) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{64}
}

func (x *InterfaceSpec) GetId() InterfaceSpecName {
	if x != nil {
		return x.Id
	}
	return InterfaceSpecName_INTERFACE_SPEC_OTHERS
}

func (x *InterfaceSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceSpec) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *InterfaceSpec) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *InterfaceSpec) GetRequestTemplate() string {
	if x != nil {
		return x.RequestTemplate
	}
	return ""
}

func (x *InterfaceSpec) GetResponseTemplate() string {
	if x != nil {
		return x.ResponseTemplate
	}
	return ""
}

func (x *InterfaceSpec) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *InterfaceSpec) GetApiConfigTemplate() *RemoteServiceConfig {
	if x != nil {
		return x.ApiConfigTemplate
	}
	return nil
}

func (x *InterfaceSpec) GetDefaultInferenceParams() []*DynamicParam {
	if x != nil {
		return x.DefaultInferenceParams
	}
	return nil
}

// EvaluationDataSet 是一个评估数据集的元数据结构
type EvaluationDataset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelId        string                    `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	AutocvDataset  int32                     `protobuf:"varint,3,opt,name=autocv_dataset,json=autocvDataset,proto3" json:"autocv_dataset,omitempty"`
	Name           string                    `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	StoragePath    string                    `protobuf:"bytes,5,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"` // 系统内评估数据所在的存储路径
	Creator        string                    `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	From           EvaluationDatasetFromType `protobuf:"varint,7,opt,name=from,proto3,enum=proto.EvaluationDatasetFromType" json:"from,omitempty"` // 数据集来源
	Type           ModelKind                 `protobuf:"varint,8,opt,name=type,proto3,enum=proto.ModelKind" json:"type,omitempty"`                 // 数据模态
	Format         string                    `protobuf:"bytes,9,opt,name=format,proto3" json:"format,omitempty"`                                   // 数据集格式（文件后缀）
	Desc           string                    `protobuf:"bytes,10,opt,name=desc,proto3" json:"desc,omitempty"`
	Addition       *CsvDataSetAddition       `protobuf:"bytes,11,opt,name=addition,proto3" json:"addition,omitempty"`
	SpecialColumn  string                    `protobuf:"bytes,12,opt,name=special_column,json=specialColumn,proto3" json:"special_column,omitempty"`    // csv 文件时填充
	CreateTimeMs   int64                     `protobuf:"varint,13,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`    // create_time_ms 创建时间戳
	UpdateTimeMs   int64                     `protobuf:"varint,14,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`    // update_time_ms 更新时间戳
	FilesExtension string                    `protobuf:"bytes,15,opt,name=files_extension,json=filesExtension,proto3" json:"files_extension,omitempty"` // format代表数据集的压缩格式，files_extension代表内部文件的后缀
	ProjectId      string                    `protobuf:"bytes,16,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *EvaluationDataset) Reset() {
	*x = EvaluationDataset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationDataset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationDataset) ProtoMessage() {}

func (x *EvaluationDataset) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationDataset.ProtoReflect.Descriptor instead.
func (*EvaluationDataset) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{65}
}

func (x *EvaluationDataset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EvaluationDataset) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *EvaluationDataset) GetAutocvDataset() int32 {
	if x != nil {
		return x.AutocvDataset
	}
	return 0
}

func (x *EvaluationDataset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationDataset) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *EvaluationDataset) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *EvaluationDataset) GetFrom() EvaluationDatasetFromType {
	if x != nil {
		return x.From
	}
	return EvaluationDatasetFromType_EVALUATION_DATASET_FROM_TYPE_UNSPECIFIED
}

func (x *EvaluationDataset) GetType() ModelKind {
	if x != nil {
		return x.Type
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *EvaluationDataset) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *EvaluationDataset) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *EvaluationDataset) GetAddition() *CsvDataSetAddition {
	if x != nil {
		return x.Addition
	}
	return nil
}

func (x *EvaluationDataset) GetSpecialColumn() string {
	if x != nil {
		return x.SpecialColumn
	}
	return ""
}

func (x *EvaluationDataset) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *EvaluationDataset) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *EvaluationDataset) GetFilesExtension() string {
	if x != nil {
		return x.FilesExtension
	}
	return ""
}

func (x *EvaluationDataset) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

// CsvDataSetAddition 如果数据集为csv，使用该结构存储csv的额外信息
type CsvDataSetAddition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InputColumns  []string `protobuf:"bytes,1,rep,name=input_columns,json=inputColumns,proto3" json:"input_columns,omitempty"`    // 模型输入的列名
	SpecialColumn string   `protobuf:"bytes,2,opt,name=special_column,json=specialColumn,proto3" json:"special_column,omitempty"` // 如真实值列，文本列等校验列
	Rows          int64    `protobuf:"varint,3,opt,name=rows,proto3" json:"rows,omitempty"`                                       // 总行数 包含表头
	Headers       []string `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty"`                                  // headers
}

func (x *CsvDataSetAddition) Reset() {
	*x = CsvDataSetAddition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CsvDataSetAddition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CsvDataSetAddition) ProtoMessage() {}

func (x *CsvDataSetAddition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CsvDataSetAddition.ProtoReflect.Descriptor instead.
func (*CsvDataSetAddition) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{66}
}

func (x *CsvDataSetAddition) GetInputColumns() []string {
	if x != nil {
		return x.InputColumns
	}
	return nil
}

func (x *CsvDataSetAddition) GetSpecialColumn() string {
	if x != nil {
		return x.SpecialColumn
	}
	return ""
}

func (x *CsvDataSetAddition) GetRows() int64 {
	if x != nil {
		return x.Rows
	}
	return 0
}

func (x *CsvDataSetAddition) GetHeaders() []string {
	if x != nil {
		return x.Headers
	}
	return nil
}

// Csv csv格式的数据集结构,代表一张数据表。
type Csv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Headers *CsvHeader        `protobuf:"bytes,1,opt,name=headers,proto3" json:"headers,omitempty"`
	Row     map[int64]*CsvRow `protobuf:"bytes,2,rep,name=row,proto3" json:"row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Csv) Reset() {
	*x = Csv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Csv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Csv) ProtoMessage() {}

func (x *Csv) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Csv.ProtoReflect.Descriptor instead.
func (*Csv) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{67}
}

func (x *Csv) GetHeaders() *CsvHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Csv) GetRow() map[int64]*CsvRow {
	if x != nil {
		return x.Row
	}
	return nil
}

type CsvRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *CsvRow) Reset() {
	*x = CsvRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CsvRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CsvRow) ProtoMessage() {}

func (x *CsvRow) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CsvRow.ProtoReflect.Descriptor instead.
func (*CsvRow) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{68}
}

func (x *CsvRow) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type CsvHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ColumnNames []string `protobuf:"bytes,1,rep,name=column_names,json=columnNames,proto3" json:"column_names,omitempty"`
	DataTypes   []string `protobuf:"bytes,2,rep,name=data_types,json=dataTypes,proto3" json:"data_types,omitempty"`
}

func (x *CsvHeader) Reset() {
	*x = CsvHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CsvHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CsvHeader) ProtoMessage() {}

func (x *CsvHeader) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CsvHeader.ProtoReflect.Descriptor instead.
func (*CsvHeader) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{69}
}

func (x *CsvHeader) GetColumnNames() []string {
	if x != nil {
		return x.ColumnNames
	}
	return nil
}

func (x *CsvHeader) GetDataTypes() []string {
	if x != nil {
		return x.DataTypes
	}
	return nil
}

// StageScript 单个脚本(模板)的元数据
type StageScript struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Stage   EvaluationStage `protobuf:"varint,2,opt,name=stage,proto3,enum=proto.EvaluationStage" json:"stage,omitempty"`
	Desc    string          `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Content string          `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *StageScript) Reset() {
	*x = StageScript{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageScript) ProtoMessage() {}

func (x *StageScript) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageScript.ProtoReflect.Descriptor instead.
func (*StageScript) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{70}
}

func (x *StageScript) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StageScript) GetStage() EvaluationStage {
	if x != nil {
		return x.Stage
	}
	return EvaluationStage_EVALUATION_STAGE_UNSPECIFIED
}

func (x *StageScript) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *StageScript) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// EvaluationScript是评估脚本整体的基础信息
type EvaluationScriptBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelId      string `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId    string `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	Name         string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Language     string `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	PipSource    string `protobuf:"bytes,6,opt,name=pip_source,json=pipSource,proto3" json:"pip_source,omitempty"`
	Requirements string `protobuf:"bytes,7,opt,name=requirements,proto3" json:"requirements,omitempty"`
	CreateTimeMs int64  `protobuf:"varint,8,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"` // create_time_ms 创建时间戳
	UpdateTimeMs int64  `protobuf:"varint,9,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"` // update_time_ms 更新时间戳
}

func (x *EvaluationScriptBase) Reset() {
	*x = EvaluationScriptBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationScriptBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationScriptBase) ProtoMessage() {}

func (x *EvaluationScriptBase) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationScriptBase.ProtoReflect.Descriptor instead.
func (*EvaluationScriptBase) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{71}
}

func (x *EvaluationScriptBase) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EvaluationScriptBase) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *EvaluationScriptBase) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *EvaluationScriptBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationScriptBase) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *EvaluationScriptBase) GetPipSource() string {
	if x != nil {
		return x.PipSource
	}
	return ""
}

func (x *EvaluationScriptBase) GetRequirements() string {
	if x != nil {
		return x.Requirements
	}
	return ""
}

func (x *EvaluationScriptBase) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *EvaluationScriptBase) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

// EvaluationScript 评估脚本
type EvaluationScript struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo     *EvaluationScriptBase `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	TemplateName string                `protobuf:"bytes,2,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`
	StoragePath  string                `protobuf:"bytes,3,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"`
	Scripts      []*StageScript        `protobuf:"bytes,4,rep,name=scripts,proto3" json:"scripts,omitempty"`
	Creator      string                `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	ProjectId    string                `protobuf:"bytes,6,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *EvaluationScript) Reset() {
	*x = EvaluationScript{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationScript) ProtoMessage() {}

func (x *EvaluationScript) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationScript.ProtoReflect.Descriptor instead.
func (*EvaluationScript) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{72}
}

func (x *EvaluationScript) GetBaseInfo() *EvaluationScriptBase {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *EvaluationScript) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *EvaluationScript) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *EvaluationScript) GetScripts() []*StageScript {
	if x != nil {
		return x.Scripts
	}
	return nil
}

func (x *EvaluationScript) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *EvaluationScript) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type EvaluationScriptTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Templates []*StageScript `protobuf:"bytes,2,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *EvaluationScriptTemplate) Reset() {
	*x = EvaluationScriptTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationScriptTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationScriptTemplate) ProtoMessage() {}

func (x *EvaluationScriptTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationScriptTemplate.ProtoReflect.Descriptor instead.
func (*EvaluationScriptTemplate) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{73}
}

func (x *EvaluationScriptTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationScriptTemplate) GetTemplates() []*StageScript {
	if x != nil {
		return x.Templates
	}
	return nil
}

// Metrics 描述模型指标的元数据
// e.g. 模型的一个指标
//
//	"f1_score": {
//	     "value": 0.8287487073422957,
//	     "range": "[0,1]",
//	     "unit": "",
//	     "desc": ""
//	   }
type Metrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    float32 `protobuf:"fixed32,1,opt,name=value,proto3" json:"value,omitempty"`
	Original float32 `protobuf:"fixed32,2,opt,name=original,proto3" json:"original,omitempty"`
	Range    string  `protobuf:"bytes,3,opt,name=range,proto3" json:"range,omitempty"`
	Unit     string  `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	Desc     string  `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	En       string  `protobuf:"bytes,6,opt,name=en,proto3" json:"en,omitempty"`
	Zh       string  `protobuf:"bytes,7,opt,name=zh,proto3" json:"zh,omitempty"`
	Category string  `protobuf:"bytes,8,opt,name=category,proto3" json:"category,omitempty"`
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{74}
}

func (x *Metrics) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *Metrics) GetOriginal() float32 {
	if x != nil {
		return x.Original
	}
	return 0
}

func (x *Metrics) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *Metrics) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *Metrics) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Metrics) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

func (x *Metrics) GetZh() string {
	if x != nil {
		return x.Zh
	}
	return ""
}

func (x *Metrics) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type Curve struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X  []float32 `protobuf:"fixed32,1,rep,packed,name=x,proto3" json:"x,omitempty"`
	Y  []float32 `protobuf:"fixed32,2,rep,packed,name=y,proto3" json:"y,omitempty"`
	En string    `protobuf:"bytes,3,opt,name=en,proto3" json:"en,omitempty"`
	Zh string    `protobuf:"bytes,4,opt,name=zh,proto3" json:"zh,omitempty"`
}

func (x *Curve) Reset() {
	*x = Curve{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Curve) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Curve) ProtoMessage() {}

func (x *Curve) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Curve.ProtoReflect.Descriptor instead.
func (*Curve) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{75}
}

func (x *Curve) GetX() []float32 {
	if x != nil {
		return x.X
	}
	return nil
}

func (x *Curve) GetY() []float32 {
	if x != nil {
		return x.Y
	}
	return nil
}

func (x *Curve) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

func (x *Curve) GetZh() string {
	if x != nil {
		return x.Zh
	}
	return ""
}

// 标准指标报告输出结果
type StandardMetricReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SingleMetric   []*SingleMetric   `protobuf:"bytes,1,rep,name=single_metric,json=singleMetric,proto3" json:"single_metric,omitempty"`
	TableMetric    []*TableMetric    `protobuf:"bytes,2,rep,name=table_metric,json=tableMetric,proto3" json:"table_metric,omitempty"`
	GraphMetric    []*GraphMetric    `protobuf:"bytes,3,rep,name=graph_metric,json=graphMetric,proto3" json:"graph_metric,omitempty"`
	PieGraphMetric []*PieGraphMetric `protobuf:"bytes,4,rep,name=pie_graph_metric,json=pieGraphMetric,proto3" json:"pie_graph_metric,omitempty"`
}

func (x *StandardMetricReport) Reset() {
	*x = StandardMetricReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StandardMetricReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StandardMetricReport) ProtoMessage() {}

func (x *StandardMetricReport) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StandardMetricReport.ProtoReflect.Descriptor instead.
func (*StandardMetricReport) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{76}
}

func (x *StandardMetricReport) GetSingleMetric() []*SingleMetric {
	if x != nil {
		return x.SingleMetric
	}
	return nil
}

func (x *StandardMetricReport) GetTableMetric() []*TableMetric {
	if x != nil {
		return x.TableMetric
	}
	return nil
}

func (x *StandardMetricReport) GetGraphMetric() []*GraphMetric {
	if x != nil {
		return x.GraphMetric
	}
	return nil
}

func (x *StandardMetricReport) GetPieGraphMetric() []*PieGraphMetric {
	if x != nil {
		return x.PieGraphMetric
	}
	return nil
}

type SingleMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value float32 `protobuf:"fixed32,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SingleMetric) Reset() {
	*x = SingleMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleMetric) ProtoMessage() {}

func (x *SingleMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleMetric.ProtoReflect.Descriptor instead.
func (*SingleMetric) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{77}
}

func (x *SingleMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SingleMetric) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type TableMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Data    *TwoDFloatArray `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	RowHead []string        `protobuf:"bytes,3,rep,name=row_head,json=rowHead,proto3" json:"row_head,omitempty"`
	ColHead []string        `protobuf:"bytes,4,rep,name=col_head,json=colHead,proto3" json:"col_head,omitempty"`
}

func (x *TableMetric) Reset() {
	*x = TableMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableMetric) ProtoMessage() {}

func (x *TableMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableMetric.ProtoReflect.Descriptor instead.
func (*TableMetric) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{78}
}

func (x *TableMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TableMetric) GetData() *TwoDFloatArray {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TableMetric) GetRowHead() []string {
	if x != nil {
		return x.RowHead
	}
	return nil
}

func (x *TableMetric) GetColHead() []string {
	if x != nil {
		return x.ColHead
	}
	return nil
}

type FloatArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []float32 `protobuf:"fixed32,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *FloatArray) Reset() {
	*x = FloatArray{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatArray) ProtoMessage() {}

func (x *FloatArray) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatArray.ProtoReflect.Descriptor instead.
func (*FloatArray) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{79}
}

func (x *FloatArray) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type TwoDFloatArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []*FloatArray `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *TwoDFloatArray) Reset() {
	*x = TwoDFloatArray{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TwoDFloatArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwoDFloatArray) ProtoMessage() {}

func (x *TwoDFloatArray) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwoDFloatArray.ProtoReflect.Descriptor instead.
func (*TwoDFloatArray) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{80}
}

func (x *TwoDFloatArray) GetValue() []*FloatArray {
	if x != nil {
		return x.Value
	}
	return nil
}

type GraphMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XAxisData     []string    `protobuf:"bytes,2,rep,name=x_axis_data,json=xAxisData,proto3" json:"x_axis_data,omitempty"`
	YAxisData     *FloatArray `protobuf:"bytes,3,opt,name=y_axis_data,json=yAxisData,proto3" json:"y_axis_data,omitempty"`
	XAxisName     string      `protobuf:"bytes,4,opt,name=x_axis_name,json=xAxisName,proto3" json:"x_axis_name,omitempty"`
	YAxisName     string      `protobuf:"bytes,5,opt,name=y_axis_name,json=yAxisName,proto3" json:"y_axis_name,omitempty"`
	DisplayFormat string      `protobuf:"bytes,6,opt,name=display_format,json=displayFormat,proto3" json:"display_format,omitempty"`
}

func (x *GraphMetric) Reset() {
	*x = GraphMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraphMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraphMetric) ProtoMessage() {}

func (x *GraphMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraphMetric.ProtoReflect.Descriptor instead.
func (*GraphMetric) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{81}
}

func (x *GraphMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GraphMetric) GetXAxisData() []string {
	if x != nil {
		return x.XAxisData
	}
	return nil
}

func (x *GraphMetric) GetYAxisData() *FloatArray {
	if x != nil {
		return x.YAxisData
	}
	return nil
}

func (x *GraphMetric) GetXAxisName() string {
	if x != nil {
		return x.XAxisName
	}
	return ""
}

func (x *GraphMetric) GetYAxisName() string {
	if x != nil {
		return x.YAxisName
	}
	return ""
}

func (x *GraphMetric) GetDisplayFormat() string {
	if x != nil {
		return x.DisplayFormat
	}
	return ""
}

type ComplexGraphMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ChildGraphName []string    `protobuf:"bytes,2,rep,name=child_graph_name,json=childGraphName,proto3" json:"child_graph_name,omitempty"`
	XAxisData      []string    `protobuf:"bytes,3,rep,name=x_axis_data,json=xAxisData,proto3" json:"x_axis_data,omitempty"`
	YAxisData      *FloatArray `protobuf:"bytes,4,opt,name=y_axis_data,json=yAxisData,proto3" json:"y_axis_data,omitempty"`
	XAxisName      string      `protobuf:"bytes,5,opt,name=x_axis_name,json=xAxisName,proto3" json:"x_axis_name,omitempty"`
	YAxisName      string      `protobuf:"bytes,6,opt,name=y_axis_name,json=yAxisName,proto3" json:"y_axis_name,omitempty"`
	DisplayFormat  string      `protobuf:"bytes,7,opt,name=display_format,json=displayFormat,proto3" json:"display_format,omitempty"`
	Count          int32       `protobuf:"varint,8,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ComplexGraphMetric) Reset() {
	*x = ComplexGraphMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComplexGraphMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComplexGraphMetric) ProtoMessage() {}

func (x *ComplexGraphMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComplexGraphMetric.ProtoReflect.Descriptor instead.
func (*ComplexGraphMetric) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{82}
}

func (x *ComplexGraphMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ComplexGraphMetric) GetChildGraphName() []string {
	if x != nil {
		return x.ChildGraphName
	}
	return nil
}

func (x *ComplexGraphMetric) GetXAxisData() []string {
	if x != nil {
		return x.XAxisData
	}
	return nil
}

func (x *ComplexGraphMetric) GetYAxisData() *FloatArray {
	if x != nil {
		return x.YAxisData
	}
	return nil
}

func (x *ComplexGraphMetric) GetXAxisName() string {
	if x != nil {
		return x.XAxisName
	}
	return ""
}

func (x *ComplexGraphMetric) GetYAxisName() string {
	if x != nil {
		return x.YAxisName
	}
	return ""
}

func (x *ComplexGraphMetric) GetDisplayFormat() string {
	if x != nil {
		return x.DisplayFormat
	}
	return ""
}

func (x *ComplexGraphMetric) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type PieGraphMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XAxisData []string    `protobuf:"bytes,2,rep,name=x_axis_data,json=xAxisData,proto3" json:"x_axis_data,omitempty"`
	YAxisData *FloatArray `protobuf:"bytes,3,opt,name=y_axis_data,json=yAxisData,proto3" json:"y_axis_data,omitempty"`
}

func (x *PieGraphMetric) Reset() {
	*x = PieGraphMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PieGraphMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PieGraphMetric) ProtoMessage() {}

func (x *PieGraphMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PieGraphMetric.ProtoReflect.Descriptor instead.
func (*PieGraphMetric) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{83}
}

func (x *PieGraphMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PieGraphMetric) GetXAxisData() []string {
	if x != nil {
		return x.XAxisData
	}
	return nil
}

func (x *PieGraphMetric) GetYAxisData() *FloatArray {
	if x != nil {
		return x.YAxisData
	}
	return nil
}

type EvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hardware             string                 `protobuf:"bytes,1,opt,name=hardware,proto3" json:"hardware,omitempty"`
	ModelMetrics         map[string]*Metrics    `protobuf:"bytes,2,rep,name=model_metrics,json=modelMetrics,proto3" json:"model_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Curves               map[string]*Curve      `protobuf:"bytes,3,rep,name=curves,proto3" json:"curves,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ConfusionMatrix_2    map[string]*Metrics    `protobuf:"bytes,4,rep,name=confusion_matrix_2,json=confusionMatrix2,proto3" json:"confusion_matrix_2,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PerformanceMetrics   map[string]*Metrics    `protobuf:"bytes,5,rep,name=performance_metrics,json=performanceMetrics,proto3" json:"performance_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SubModelsPerformance []*SubModelPerformance `protobuf:"bytes,6,rep,name=sub_models_performance,json=subModelsPerformance,proto3" json:"sub_models_performance,omitempty"`
}

func (x *EvaluationResult) Reset() {
	*x = EvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationResult) ProtoMessage() {}

func (x *EvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationResult.ProtoReflect.Descriptor instead.
func (*EvaluationResult) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{84}
}

func (x *EvaluationResult) GetHardware() string {
	if x != nil {
		return x.Hardware
	}
	return ""
}

func (x *EvaluationResult) GetModelMetrics() map[string]*Metrics {
	if x != nil {
		return x.ModelMetrics
	}
	return nil
}

func (x *EvaluationResult) GetCurves() map[string]*Curve {
	if x != nil {
		return x.Curves
	}
	return nil
}

func (x *EvaluationResult) GetConfusionMatrix_2() map[string]*Metrics {
	if x != nil {
		return x.ConfusionMatrix_2
	}
	return nil
}

func (x *EvaluationResult) GetPerformanceMetrics() map[string]*Metrics {
	if x != nil {
		return x.PerformanceMetrics
	}
	return nil
}

func (x *EvaluationResult) GetSubModelsPerformance() []*SubModelPerformance {
	if x != nil {
		return x.SubModelsPerformance
	}
	return nil
}

// SubModelPerformance 组合模型子模型指标，当前性能指标需要按照子模型进行分开
type SubModelPerformance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Performance map[string]*Metrics `protobuf:"bytes,3,rep,name=performance,proto3" json:"performance,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SubModelPerformance) Reset() {
	*x = SubModelPerformance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubModelPerformance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubModelPerformance) ProtoMessage() {}

func (x *SubModelPerformance) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubModelPerformance.ProtoReflect.Descriptor instead.
func (*SubModelPerformance) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{85}
}

func (x *SubModelPerformance) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SubModelPerformance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubModelPerformance) GetPerformance() map[string]*Metrics {
	if x != nil {
		return x.Performance
	}
	return nil
}

// PortInfo 部署暴露的服务状态,主要是端口信息
type PortInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Port     int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	NodePort int32  `protobuf:"varint,3,opt,name=node_port,json=nodePort,proto3" json:"node_port,omitempty"`
}

func (x *PortInfo) Reset() {
	*x = PortInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortInfo) ProtoMessage() {}

func (x *PortInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortInfo.ProtoReflect.Descriptor instead.
func (*PortInfo) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{86}
}

func (x *PortInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PortInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *PortInfo) GetNodePort() int32 {
	if x != nil {
		return x.NodePort
	}
	return 0
}

type ServiceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeIp string      `protobuf:"bytes,1,opt,name=node_ip,json=nodeIp,proto3" json:"node_ip,omitempty"`
	Ports  []*PortInfo `protobuf:"bytes,2,rep,name=ports,proto3" json:"ports,omitempty"`
}

func (x *ServiceStatus) Reset() {
	*x = ServiceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceStatus) ProtoMessage() {}

func (x *ServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceStatus.ProtoReflect.Descriptor instead.
func (*ServiceStatus) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{87}
}

func (x *ServiceStatus) GetNodeIp() string {
	if x != nil {
		return x.NodeIp
	}
	return ""
}

func (x *ServiceStatus) GetPorts() []*PortInfo {
	if x != nil {
		return x.Ports
	}
	return nil
}

// Status 状态描述结构，描述评估与部署状态
type Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State         string                `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Message       string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     int64                 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Health        string                `protobuf:"bytes,4,opt,name=health,proto3" json:"health,omitempty"`
	HealthMsg     string                `protobuf:"bytes,5,opt,name=health_msg,json=healthMsg,proto3" json:"health_msg,omitempty"`
	Nodes         []string              `protobuf:"bytes,6,rep,name=nodes,proto3" json:"nodes,omitempty"` // 被调度到的节点列表
	ServiceStatus *ServiceStatus        `protobuf:"bytes,7,opt,name=service_status,json=serviceStatus,proto3" json:"service_status,omitempty"`
	RefId         string                `protobuf:"bytes,8,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	RefType       string                `protobuf:"bytes,9,opt,name=ref_type,json=refType,proto3" json:"ref_type,omitempty"`
	ReplicaId     string                `protobuf:"bytes,10,opt,name=replica_id,json=replicaId,proto3" json:"replica_id,omitempty"`
	EdgeId        string                `protobuf:"bytes,11,opt,name=edge_id,json=edgeId,proto3" json:"edge_id,omitempty"`
	Indicators    map[string]*anypb.Any `protobuf:"bytes,12,rep,name=indicators,proto3" json:"indicators,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Status) Reset() {
	*x = Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status) ProtoMessage() {}

func (x *Status) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status.ProtoReflect.Descriptor instead.
func (*Status) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{88}
}

func (x *Status) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Status) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Status) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Status) GetHealth() string {
	if x != nil {
		return x.Health
	}
	return ""
}

func (x *Status) GetHealthMsg() string {
	if x != nil {
		return x.HealthMsg
	}
	return ""
}

func (x *Status) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Status) GetServiceStatus() *ServiceStatus {
	if x != nil {
		return x.ServiceStatus
	}
	return nil
}

func (x *Status) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *Status) GetRefType() string {
	if x != nil {
		return x.RefType
	}
	return ""
}

func (x *Status) GetReplicaId() string {
	if x != nil {
		return x.ReplicaId
	}
	return ""
}

func (x *Status) GetEdgeId() string {
	if x != nil {
		return x.EdgeId
	}
	return ""
}

func (x *Status) GetIndicators() map[string]*anypb.Any {
	if x != nil {
		return x.Indicators
	}
	return nil
}

type Bucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Left  string `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	Right string `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
	Count int32  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *Bucket) Reset() {
	*x = Bucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bucket) ProtoMessage() {}

func (x *Bucket) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bucket.ProtoReflect.Descriptor instead.
func (*Bucket) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{89}
}

func (x *Bucket) GetLeft() string {
	if x != nil {
		return x.Left
	}
	return ""
}

func (x *Bucket) GetRight() string {
	if x != nil {
		return x.Right
	}
	return ""
}

func (x *Bucket) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type Feature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Buckets []*Bucket `protobuf:"bytes,2,rep,name=buckets,proto3" json:"buckets,omitempty"`
}

func (x *Feature) Reset() {
	*x = Feature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feature) ProtoMessage() {}

func (x *Feature) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feature.ProtoReflect.Descriptor instead.
func (*Feature) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{90}
}

func (x *Feature) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Feature) GetBuckets() []*Bucket {
	if x != nil {
		return x.Buckets
	}
	return nil
}

// TrainingDataDistribution 训练数据分布用于pkl模型填写训练数据分布,支持手动填写与上传文件解析
type TrainingDataDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Features []*Feature `protobuf:"bytes,1,rep,name=features,proto3" json:"features,omitempty"`
}

func (x *TrainingDataDistribution) Reset() {
	*x = TrainingDataDistribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainingDataDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainingDataDistribution) ProtoMessage() {}

func (x *TrainingDataDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainingDataDistribution.ProtoReflect.Descriptor instead.
func (*TrainingDataDistribution) Descriptor() ([]byte, []int) {
	return file_proto_model_proto_rawDescGZIP(), []int{91}
}

func (x *TrainingDataDistribution) GetFeatures() []*Feature {
	if x != nil {
		return x.Features
	}
	return nil
}

var File_proto_model_proto protoreflect.FileDescriptor

var file_proto_model_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x03, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x2a, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x61, 0x70, 0x69, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x41, 0x70, 0x69, 0x52, 0x04, 0x61, 0x70, 0x69, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x44, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x10, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x6e, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x69, 0x74, 0x22, 0x9f, 0x02, 0x0a, 0x09, 0x53, 0x70, 0x61, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72,
	0x79, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x73, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x69, 0x73, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x1a, 0x41, 0x0a, 0x13, 0x49, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x49, 0x73,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3e, 0x0a, 0x0e, 0x53, 0x65, 0x74,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xcb, 0x02, 0x0a, 0x0a, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0f, 0x62, 0x61, 0x73,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x3c, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x37, 0x0a, 0x0b, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b,
	0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x69,
	0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x65, 0x77, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x76, 0x69, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e,
	0x76, 0x6f, 0x6b, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64,
	0x22, 0x64, 0x0a, 0x0c, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64,
	0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73,
	0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0xd1, 0x03, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08,
	0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x31, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4b,
	0x69, 0x6e, 0x64, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x75,
	0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e,
	0x64, 0x52, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64,
	0x12, 0x33, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f,
	0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x76, 0x0a, 0x08, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x41, 0x70, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x29, 0x0a, 0x06, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe2, 0x03, 0x0a, 0x0b, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e,
	0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x36, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x62, 0x61, 0x73,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3c, 0x0a, 0x0e, 0x42, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x91,
	0x01, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x22, 0xa8, 0x04, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x3d, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x6b, 0x0a, 0x16, 0x63,
	0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x48, 0x0a, 0x1a, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x67, 0x0a,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x48, 0x6f,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x22, 0x46, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x80,
	0x05, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x42, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x31, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x40, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x2e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x61, 0x70, 0x69,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x70, 0x69, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41,
	0x70, 0x69, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x11,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x69, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x69, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x22, 0x7b, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2f, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xf3,
	0x01, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xc0, 0x02, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x3f, 0x0a, 0x10, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x48, 0x0a, 0x13, 0x65, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x11, 0x65, 0x6e, 0x73, 0x65, 0x6d,
	0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcb, 0x03, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31,
	0x0a, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x31, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x10, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3e, 0x0a, 0x10, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x0f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x75, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x75, 0x73, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9f, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x61, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x12, 0x73, 0x0a, 0x1b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x19, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x6d, 0x0a, 0x1e, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xac, 0x01, 0x0a, 0x11, 0x45, 0x6e, 0x73, 0x65,
	0x6d, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xdc, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x5e, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x69, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x69, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x32, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75,
	0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x69, 0x0a, 0x0c, 0x41, 0x74, 0x6f, 0x6d, 0x53, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61,
	0x73, 0x12, 0x2d, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x22, 0x91, 0x01, 0x0a, 0x0d, 0x41, 0x74, 0x6f, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x41,
	0x6c, 0x69, 0x61, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x14, 0x0a, 0x12, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xda, 0x01, 0x0a,
	0x0c, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a,
	0x0f, 0x61, 0x74, 0x6f, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41,
	0x74, 0x6f, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x61, 0x74,
	0x6f, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x10, 0x70,
	0x79, 0x74, 0x68, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x79,
	0x74, 0x68, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x70, 0x79,
	0x74, 0x68, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4b, 0x0a, 0x14,
	0x65, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x65, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb7, 0x01, 0x0a, 0x08, 0x53, 0x75,
	0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x27, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x22, 0x39, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x2d, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x8d,
	0x01, 0x0a, 0x14, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x6e, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x89,
	0x04, 0x0a, 0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30,
	0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6d, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6d, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x69, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x69, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72,
	0x67, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x39, 0x0a, 0x07, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e,
	0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a,
	0x0a, 0x0c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xad, 0x02, 0x0a, 0x0e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x54, 0x61, 0x67, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x70, 0x6f, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x63, 0x6b, 0x65,
	0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x11, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x12, 0x3c, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x34,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x83, 0x02, 0x0a, 0x10, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x3b, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a,
	0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb6, 0x08, 0x0a, 0x0a, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x79, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x12, 0x3a,
	0x0a, 0x0c, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a,
	0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x35, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x75,
	0x74, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x69, 0x0a, 0x09, 0x4c, 0x69, 0x66, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73, 0x22, 0x97,
	0x01, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52,
	0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x22, 0xed, 0x02, 0x0a, 0x09, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x70, 0x75,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x52, 0x08, 0x63, 0x70, 0x75, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x09,
	0x6d, 0x65, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12,
	0x33, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x47, 0x70, 0x75, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x67, 0x70, 0x75, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x1a, 0x52, 0x0a, 0x0e, 0x47, 0x70, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x23, 0x0a, 0x0d, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x75, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x22, 0x9c, 0x02, 0x0a, 0x0a, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x55,
	0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x73, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x73, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x3a,
	0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x6b, 0x75,
	0x62, 0x65, 0x6c, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6b, 0x75, 0x62, 0x65, 0x6c, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x8a, 0x03, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x12, 0x33, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2f, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x22, 0x6e, 0x0a, 0x0c, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x29, 0x0a, 0x08, 0x63, 0x70, 0x75, 0x5f, 0x61, 0x72, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x50, 0x55, 0x41, 0x72,
	0x63, 0x68, 0x52, 0x07, 0x63, 0x70, 0x75, 0x41, 0x72, 0x63, 0x68, 0x12, 0x33, 0x0a, 0x09, 0x61,
	0x63, 0x63, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x61, 0x72, 0x64, 0x52, 0x08, 0x61, 0x63, 0x63, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x22, 0x6c, 0x0a, 0x0d, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x26, 0x0a, 0x06, 0x61, 0x72, 0x63, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x50, 0x55, 0x41, 0x72, 0x63,
	0x68, 0x52, 0x06, 0x61, 0x72, 0x63, 0x68, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x61, 0x63, 0x63,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x61, 0x63, 0x63, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xd3,
	0x01, 0x0a, 0x09, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x08,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24,
	0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x73, 0x22, 0x6f, 0x0a, 0x10, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x70, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x70, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x67, 0x70, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x67, 0x70, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x67,
	0x70, 0x75, 0x5f, 0x6d, 0x65, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x67,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x22, 0x3b, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x6e, 0x6f, 0x64,
	0x65, 0x73, 0x22, 0x4b, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x22,
	0xbd, 0x02, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x70, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x70, 0x75, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x38, 0x0a, 0x0a, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x66, 0x67, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41,
	0x73, 0x63, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x66, 0x67,
	0x52, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x43, 0x66, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x22,
	0x95, 0x01, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x61, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9d, 0x02, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x0d, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x26, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x49, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0xa8, 0x0a, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x61,
	0x70, 0x69, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x70, 0x69, 0x52, 0x04, 0x61, 0x70, 0x69, 0x73,
	0x12, 0x25, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62,
	0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x10, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x0f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x6c, 0x64,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x75,
	0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f,
	0x61, 0x73, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x41, 0x73, 0x54, 0x6f,
	0x6f, 0x6c, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x41, 0x73, 0x54, 0x6f, 0x6f, 0x6c,
	0x12, 0x4e, 0x0a, 0x15, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x35, 0x0a, 0x0f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x4e, 0x0a, 0x18, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x16, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x46, 0x0a, 0x11, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x75,
	0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x4b, 0x0a, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x1f, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0xed, 0x01,
	0x0a, 0x0c, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x41, 0x73, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x24,
	0x0a, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x68, 0x75, 0x6d, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x61,
	0x6d, 0x65, 0x46, 0x6f, 0x72, 0x48, 0x75, 0x6d, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x44,
	0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x37, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x0c, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x8a, 0x01,
	0x0a, 0x0b, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x89, 0x02, 0x0a, 0x13, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x12, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x2a, 0x0a, 0x06,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x4c, 0x0a, 0x0c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0xdc, 0x05, 0x0a, 0x13, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x32, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x36, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x34,
	0x0a, 0x0b, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x74, 0x68, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x78, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x52, 0x05,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x75, 0x6c, 0x6c, 0x55, 0x72, 0x6c,
	0x12, 0x6c, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x3f,
	0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12,
	0x34, 0x0a, 0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x1a, 0x48, 0x0a,
	0x1a, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x13, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x35, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xba, 0x03, 0x0a, 0x18, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x31, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x73,
	0x76, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x76, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x41, 0x70, 0x69, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
	0x6f, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x22, 0xa3, 0x04, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b,
	0x69, 0x6e, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x39, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x61, 0x70, 0x69, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x10, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67,
	0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0x54, 0x0a,
	0x13, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x73, 0x22, 0xaa, 0x03, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x28, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x4a, 0x0a, 0x13, 0x61, 0x70, 0x69, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11,
	0x61, 0x70, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x4d, 0x0a, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0xb0, 0x04, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x63, 0x76, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x63,
	0x76, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x46, 0x72, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12,
	0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x12, 0x35, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x44,
	0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12,
	0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x12, 0x43, 0x73, 0x76, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x03, 0x43, 0x73, 0x76, 0x12, 0x2a, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73,
	0x76, 0x2e, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x72, 0x6f, 0x77, 0x1a,
	0x45, 0x0a, 0x08, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x52, 0x6f, 0x77, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x20, 0x0a, 0x06, 0x43, 0x73, 0x76, 0x52, 0x6f, 0x77,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x4d, 0x0a, 0x09, 0x43, 0x73, 0x76, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x9f, 0x02, 0x0a, 0x14, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x42, 0x61, 0x73, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x70,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x69, 0x70, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x22, 0xfb, 0x01, 0x0a, 0x10, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x38, 0x0a,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x2c, 0x0a, 0x07, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x52, 0x07, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x18, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x09, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x07, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x7a, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x7a, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x22, 0x43, 0x0a, 0x05, 0x43, 0x75, 0x72, 0x76, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x7a, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x7a, 0x68, 0x22, 0xff, 0x01, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x38,
	0x0a, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0c, 0x73, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x52, 0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12,
	0x35, 0x0a, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x3f, 0x0a, 0x10, 0x70, 0x69, 0x65, 0x5f, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x69, 0x65, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0e, 0x70, 0x69, 0x65, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x38, 0x0a, 0x0c, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x82, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x77, 0x6f, 0x44,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x77, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x77, 0x48, 0x65, 0x61, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x6f, 0x6c, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6c, 0x48, 0x65, 0x61, 0x64, 0x22, 0x22, 0x0a, 0x0a, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41,
	0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x39, 0x0a, 0x0e, 0x54, 0x77,
	0x6f, 0x44, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x27, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x0b, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x78, 0x5f, 0x61,
	0x78, 0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x78, 0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x0b, 0x79, 0x5f, 0x61,
	0x78, 0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41, 0x72, 0x72, 0x61,
	0x79, 0x52, 0x09, 0x79, 0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b,
	0x78, 0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x78, 0x41, 0x78, 0x69, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b,
	0x79, 0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x79, 0x41, 0x78, 0x69, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x22, 0xa2, 0x02, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x78, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x78, 0x5f, 0x61, 0x78,
	0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x78,
	0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x0b, 0x79, 0x5f, 0x61, 0x78,
	0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79,
	0x52, 0x09, 0x79, 0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b, 0x78,
	0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x78, 0x41, 0x78, 0x69, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x79,
	0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x79, 0x41, 0x78, 0x69, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x77, 0x0a, 0x0e, 0x50, 0x69, 0x65, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0b, 0x78, 0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x78, 0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31,
	0x0a, 0x0b, 0x79, 0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x09, 0x79, 0x41, 0x78, 0x69, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x92, 0x06, 0x0a, 0x10, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x3b, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x72, 0x76,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x75, 0x72, 0x76, 0x65, 0x73, 0x12,
	0x5b, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x5f, 0x32, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x74, 0x72, 0x69, 0x78, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x66,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x32, 0x12, 0x60, 0x0a, 0x13,
	0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x50,
	0x0a, 0x16, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x73, 0x75, 0x62, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x1a, 0x4f, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x47, 0x0a, 0x0b, 0x43, 0x75, 0x72, 0x76, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x75, 0x72, 0x76, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x53, 0x0a, 0x15, 0x43, 0x6f,
	0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x32, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x55, 0x0a, 0x17, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd8, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x1a, 0x4e, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x4f, 0x0a, 0x08, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x72, 0x74, 0x22, 0x4f, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x70, 0x12, 0x25, 0x0a, 0x05,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x22, 0xde, 0x03, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x6d,
	0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x4d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x65, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x64, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x3d, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x1a,
	0x53, 0x0a, 0x0f, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x48, 0x0a, 0x06, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x65,
	0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x46,
	0x0a, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x22, 0x46, 0x0a, 0x18, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2a, 0xc4,
	0x02, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x38, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x31, 0x36, 0x10,
	0x03, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x49, 0x4e, 0x54, 0x33, 0x32, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x36, 0x34, 0x10, 0x05, 0x12, 0x12, 0x0a,
	0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x38, 0x10,
	0x06, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x4e, 0x54, 0x31, 0x36, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x33, 0x32, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x36, 0x34, 0x10, 0x09,
	0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x50,
	0x31, 0x36, 0x10, 0x0a, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x50, 0x33, 0x32, 0x10, 0x0b, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x50, 0x36, 0x34, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x59, 0x54, 0x45, 0x53, 0x10,
	0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x46, 0x31, 0x36, 0x10, 0x0e, 0x2a, 0x31, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x48, 0x41, 0x52,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x4d,
	0x42, 0x45, 0x44, 0x44, 0x45, 0x44, 0x10, 0x01, 0x2a, 0x9e, 0x01, 0x0a, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4b, 0x49, 0x4e, 0x44,
	0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x43, 0x56, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4f, 0x43, 0x52, 0x10, 0x03, 0x12,
	0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c,
	0x50, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x4d, 0x4c, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x53, 0x52, 0x10, 0x06, 0x2a, 0xc9, 0x07, 0x0a, 0x0c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x55, 0x4c,
	0x54, 0x49, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x10, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x66, 0x12, 0x2c, 0x0a, 0x28, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x55, 0x4c,
	0x54, 0x49, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x53, 0x54,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x67, 0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x43, 0x56, 0x5f, 0x43, 0x4c,
	0x53, 0x10, 0xc9, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x43, 0x56, 0x5f, 0x44, 0x45, 0x54, 0x10, 0xca, 0x01,
	0x12, 0x1e, 0x0a, 0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x43, 0x56, 0x5f, 0x49, 0x4e, 0x53, 0x5f, 0x53, 0x45, 0x47, 0x10, 0xcb, 0x01,
	0x12, 0x1e, 0x0a, 0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x43, 0x56, 0x5f, 0x53, 0x45, 0x4d, 0x5f, 0x53, 0x45, 0x47, 0x10, 0xcc, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x43, 0x56, 0x5f, 0x4f, 0x43, 0x52, 0x10, 0xcd, 0x01, 0x12, 0x26, 0x0a, 0x21,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x43,
	0x56, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4d, 0x42, 0x45, 0x44, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0xce, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4f, 0x43, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x4e, 0x10, 0xad, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4f, 0x43, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49,
	0x50, 0x54, 0x53, 0x10, 0xae, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50, 0x5f, 0x53, 0x49, 0x4e,
	0x47, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x53, 0x10, 0x91, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50,
	0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x43, 0x4c, 0x53, 0x10, 0x92, 0x03, 0x12, 0x2a, 0x0a,
	0x25, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f,
	0x4e, 0x4c, 0x50, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x47,
	0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x93, 0x03, 0x12, 0x29, 0x0a, 0x24, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50, 0x5f,
	0x53, 0x45, 0x4d, 0x41, 0x4e, 0x54, 0x49, 0x43, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x94, 0x03, 0x12, 0x25, 0x0a, 0x20, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x49,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x53, 0x10, 0x95, 0x03, 0x12, 0x27, 0x0a, 0x22, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c,
	0x50, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x96, 0x03, 0x12, 0x23, 0x0a, 0x1e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f,
	0x56, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x97, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x4c, 0x50, 0x5f,
	0x52, 0x45, 0x52, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x98, 0x03, 0x12, 0x27, 0x0a, 0x22,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e,
	0x4c, 0x50, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x99, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x4c, 0x5f, 0x42, 0x49, 0x4e, 0x41, 0x52,
	0x59, 0x5f, 0x43, 0x4c, 0x53, 0x10, 0xf5, 0x03, 0x12, 0x20, 0x0a, 0x1b, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x4c, 0x5f, 0x4d, 0x55,
	0x4c, 0x54, 0x49, 0x5f, 0x43, 0x4c, 0x53, 0x10, 0xf6, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x4c, 0x5f,
	0x52, 0x45, 0x47, 0x52, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0xf7, 0x03, 0x12, 0x1e, 0x0a,
	0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f,
	0x4d, 0x4c, 0x5f, 0x43, 0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0xf8, 0x03, 0x12, 0x1a, 0x0a,
	0x15, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f,
	0x53, 0x52, 0x5f, 0x53, 0x54, 0x54, 0x10, 0xd9, 0x04, 0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x53, 0x52, 0x5f, 0x54,
	0x54, 0x53, 0x10, 0xda, 0x04, 0x2a, 0x8c, 0x02, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x52,
	0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a,
	0x17, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x42, 0x4c, 0x4f, 0x4f, 0x4d, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52,
	0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x43, 0x48, 0x41, 0x54, 0x47, 0x4c, 0x4d, 0x32, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52,
	0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x4c, 0x4c, 0x41, 0x4d, 0x41, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x41, 0x49, 0x4e,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x4c, 0x41,
	0x4d, 0x41, 0x32, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e,
	0x47, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x4c, 0x43, 0x4f,
	0x4e, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x4c,
	0x4d, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x43, 0x4f, 0x44,
	0x45, 0x52, 0x10, 0x07, 0x2a, 0x6b, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c, 0x45, 0x10,
	0x03, 0x2a, 0xda, 0x03, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x4d, 0x4d, 0x4c, 0x10, 0x0b,
	0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x4e, 0x4e, 0x58, 0x10, 0x0c, 0x12, 0x1e,
	0x0a, 0x1a, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x23,
	0x0a, 0x1f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x4f, 0x52, 0x4d, 0x45,
	0x52, 0x10, 0x0e, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x46, 0x54, 0x10,
	0x0f, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x4b, 0x4c, 0x10, 0x10, 0x12, 0x1e,
	0x0a, 0x1a, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x11, 0x12, 0x20,
	0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x15,
	0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x56, 0x4c, 0x41, 0x42, 0x10, 0x16,
	0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x56,
	0x45, 0x52, 0x10, 0x17, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c, 0x45, 0x5f,
	0x43, 0x56, 0x41, 0x54, 0x10, 0x1f, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x20, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x53,
	0x45, 0x4d, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x44, 0x4f, 0x4e, 0x10, 0x21, 0x2a, 0x56,
	0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x12, 0x06, 0x0a, 0x02, 0x4d, 0x4f, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x4c, 0x47, 0x4f,
	0x52, 0x49, 0x54, 0x48, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x47, 0x42, 0x4f, 0x4f,
	0x53, 0x54, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x4c, 0x47, 0x4f, 0x52, 0x49, 0x54, 0x48,
	0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x44, 0x4f, 0x4d, 0x5f, 0x46, 0x4f,
	0x52, 0x45, 0x53, 0x54, 0x10, 0x02, 0x2a, 0x58, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x20,
	0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x46, 0x55, 0x4c, 0x10, 0x01,
	0x2a, 0xac, 0x02, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50,
	0x41, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e, 0x48, 0x57, 0x43, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e, 0x43, 0x48, 0x57, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d,
	0x41, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x42, 0x49, 0x4e, 0x41, 0x52, 0x59, 0x10,
	0x03, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f,
	0x4e, 0x4c, 0x50, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e, 0x4c, 0x50, 0x5f, 0x43, 0x48,
	0x41, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x07, 0x2a,
	0xe1, 0x01, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x45, 0x4c, 0x45,
	0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x4f, 0x44, 0x45, 0x4c,
	0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x03, 0x12, 0x2e, 0x0a, 0x2a, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x45, 0x4c,
	0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x04, 0x2a, 0x8b, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x50, 0x4f,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16,
	0x52, 0x45, 0x50, 0x4f, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43,
	0x4f, 0x4c, 0x5f, 0x53, 0x46, 0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x50, 0x4f,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x44,
	0x4f, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x50, 0x4f, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x53, 0x33,
	0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x50, 0x4f, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f,
	0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x4e, 0x46, 0x53, 0x10, 0x04, 0x12, 0x1a,
	0x0a, 0x16, 0x52, 0x45, 0x50, 0x4f, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54,
	0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x46, 0x54, 0x50, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45,
	0x50, 0x4f, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x10, 0x06, 0x12, 0x22, 0x0a,
	0x1e, 0x52, 0x45, 0x50, 0x4f, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f,
	0x43, 0x4f, 0x4c, 0x5f, 0x48, 0x55, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x46, 0x41, 0x43, 0x45, 0x10,
	0x07, 0x2a, 0x91, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x54, 0x4f, 0x4d, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x47, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x03, 0x2a, 0x77, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x6e,
	0x73, 0x65, 0x6d, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x20, 0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c,
	0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d, 0x42, 0x4c, 0x45, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x45, 0x4e, 0x53, 0x45, 0x4d,
	0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x4c, 0x53, 0x10, 0x02, 0x2a, 0xf0,
	0x01, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x42, 0x54, 0x58, 0x54, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10,
	0x02, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x58, 0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x59, 0x41, 0x4d, 0x4c, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x53, 0x56, 0x10, 0x06, 0x12,
	0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4d, 0x4d,
	0x4c, 0x10, 0x0b, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4f, 0x4e, 0x4e, 0x58, 0x10, 0x0c, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4b, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x10,
	0x64, 0x2a, 0x88, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10, 0x03, 0x2a, 0x8f, 0x01, 0x0a,
	0x08, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4e, 0x4f, 0x44,
	0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4c,
	0x45, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f,
	0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x45, 0x56,
	0x41, 0x4c, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4c,
	0x45, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x44, 0x45,
	0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x10, 0x05, 0x2a, 0x5f,
	0x0a, 0x07, 0x43, 0x50, 0x55, 0x41, 0x72, 0x63, 0x68, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x50, 0x55,
	0x5f, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x50, 0x55, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x41, 0x4d, 0x44, 0x36, 0x34, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x50, 0x55, 0x5f, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x41, 0x52, 0x4d, 0x36, 0x34, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x43,
	0x50, 0x55, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x10, 0x03, 0x2a,
	0xd7, 0x01, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54,
	0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52,
	0x41, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x50, 0x55, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43,
	0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x54, 0x4c, 0x41, 0x53, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45,
	0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4c, 0x55, 0x10, 0x04,
	0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x45, 0x54, 0x53, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x1a, 0x0a,
	0x16, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x56, 0x41, 0x41, 0x50, 0x49, 0x10, 0x06, 0x2a, 0xb4, 0x01, 0x0a, 0x10, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22,
	0x0a, 0x1e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x55, 0x4e, 0x54,
	0x49, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x01,
	0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4c, 0x49, 0x45, 0x5f, 0x50, 0x4f, 0x44, 0x10,
	0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x55, 0x4e, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4c, 0x49, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52, 0x55, 0x4e,
	0x54, 0x49, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4d, 0x4d, 0x4c, 0x10, 0x04,
	0x2a, 0x99, 0x01, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x4f, 0x44, 0x45, 0x4c,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x41, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53,
	0x43, 0x48, 0x45, 0x4d, 0x41, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43,
	0x48, 0x45, 0x4d, 0x41, 0x5f, 0x44, 0x4c, 0x49, 0x45, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x4d, 0x41, 0x5f, 0x53, 0x45, 0x4c, 0x44, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0x92, 0x01, 0x0a,
	0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45,
	0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10,
	0x03, 0x2a, 0x68, 0x0a, 0x18, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x24, 0x0a,
	0x20, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x59, 0x4e,
	0x43, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x10, 0x01, 0x2a, 0x7b, 0x0a, 0x13, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x4d,
	0x4f, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x4d, 0x4f,
	0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x10, 0x02, 0x2a, 0x8c, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f,
	0x78, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x58,
	0x59, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x5f,
	0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x48, 0x54,
	0x54, 0x50, 0x53, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x5f, 0x53,
	0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x53, 0x4f, 0x43, 0x4b, 0x53, 0x34, 0x10, 0x03, 0x12, 0x17,
	0x0a, 0x13, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x53,
	0x4f, 0x43, 0x4b, 0x53, 0x35, 0x10, 0x04, 0x2a, 0x67, 0x0a, 0x11, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x41, 0x49,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x53, 0x50, 0x45, 0x43, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x57, 0x41, 0x52, 0x50, 0x10, 0x03,
	0x2a, 0x95, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x26, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x42, 0x55, 0x49, 0x4c, 0x54, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x43, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x4d,
	0x4f, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x42, 0x55, 0x49, 0x4c,
	0x54, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x41, 0x49, 0x10,
	0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x54, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x5a, 0x55, 0x52, 0x45, 0x10, 0x02, 0x2a, 0x9a, 0x01, 0x0a, 0x19, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x46, 0x72,
	0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23,
	0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x53,
	0x45, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54,
	0x4f, 0x43, 0x56, 0x10, 0x02, 0x2a, 0xbd, 0x01, 0x0a, 0x0f, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x41,
	0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x45,
	0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x50, 0x52, 0x45, 0x50, 0x41, 0x52, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x01, 0x12, 0x1a,
	0x0a, 0x16, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x45, 0x52, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56,
	0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x10, 0x03, 0x12, 0x25,
	0x0a, 0x21, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x53,
	0x55, 0x4c, 0x54, 0x10, 0x04, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61,
	0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73,
	0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_model_proto_rawDescOnce sync.Once
	file_proto_model_proto_rawDescData = file_proto_model_proto_rawDesc
)

func file_proto_model_proto_rawDescGZIP() []byte {
	file_proto_model_proto_rawDescOnce.Do(func() {
		file_proto_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_model_proto_rawDescData)
	})
	return file_proto_model_proto_rawDescData
}

var file_proto_model_proto_enumTypes = make([]protoimpl.EnumInfo, 29)
var file_proto_model_proto_msgTypes = make([]protoimpl.MessageInfo, 116)
var file_proto_model_proto_goTypes = []interface{}{
	(DataType)(0),                    // 0: proto.DataType
	(AssetType)(0),                   // 1: proto.AssetType
	(ModelKind)(0),                   // 2: proto.ModelKind
	(ModelSubKind)(0),                // 3: proto.ModelSubKind
	(TrainingTemplate)(0),            // 4: proto.TrainingTemplate
	(ModelType)(0),                   // 5: proto.ModelType
	(ModelSubType)(0),                // 6: proto.ModelSubType
	(ModelAlgorithm)(0),              // 7: proto.ModelAlgorithm
	(ModelScheduleMode)(0),           // 8: proto.ModelScheduleMode
	(ModelParamFormat)(0),            // 9: proto.ModelParamFormat
	(ModelReleaseStatus)(0),          // 10: proto.ModelReleaseStatus
	(RepoAddrProtocol)(0),            // 11: proto.RepoAddrProtocol
	(SubModelType)(0),                // 12: proto.SubModelType
	(ModelEnsembleMode)(0),           // 13: proto.ModelEnsembleMode
	(FileType)(0),                    // 14: proto.FileType
	(DeploymentType)(0),              // 15: proto.DeploymentType
	(NodeRole)(0),                    // 16: proto.NodeRole
	(CPUArch)(0),                     // 17: proto.CPUArch
	(AcceleratorType)(0),             // 18: proto.AcceleratorType
	(ModelRuntimeType)(0),            // 19: proto.ModelRuntimeType
	(ModelServiceSchema)(0),          // 20: proto.ModelServiceSchema
	(ModelServiceType)(0),            // 21: proto.ModelServiceType
	(ModelServiceInvokeMethod)(0),    // 22: proto.ModelServiceInvokeMethod
	(RemoteServiceMethod)(0),         // 23: proto.RemoteServiceMethod
	(ProxyScheme)(0),                 // 24: proto.ProxyScheme
	(InterfaceSpecName)(0),           // 25: proto.InterfaceSpecName
	(RemoteServiceBuiltInType)(0),    // 26: proto.RemoteServiceBuiltInType
	(EvaluationDatasetFromType)(0),   // 27: proto.EvaluationDatasetFromType
	(EvaluationStage)(0),             // 28: proto.EvaluationStage
	(*Model)(nil),                    // 29: proto.Model
	(*SpaceInfo)(nil),                // 30: proto.SpaceInfo
	(*SetSelectedReq)(nil),           // 31: proto.SetSelectedReq
	(*ModelStats)(nil),               // 32: proto.ModelStats
	(*ModelUsageCount)(nil),          // 33: proto.ModelUsageCount
	(*DataModality)(nil),             // 34: proto.DataModality
	(*ModelDomain)(nil),              // 35: proto.ModelDomain
	(*ModelApi)(nil),                 // 36: proto.ModelApi
	(*ModelParam)(nil),               // 37: proto.ModelParam
	(*ModelDetail)(nil),              // 38: proto.ModelDetail
	(*Attachment)(nil),               // 39: proto.Attachment
	(*ModelReleaseDetail)(nil),       // 40: proto.ModelReleaseDetail
	(*FilePath)(nil),                 // 41: proto.FilePath
	(*TrainingTemplateConfig)(nil),   // 42: proto.TrainingTemplateConfig
	(*ModelReleaseBase)(nil),         // 43: proto.ModelReleaseBase
	(*ModelRelease)(nil),             // 44: proto.ModelRelease
	(*ModelReleaseStats)(nil),        // 45: proto.ModelReleaseStats
	(*ModelMeta)(nil),                // 46: proto.ModelMeta
	(*ModelDefaultConfig)(nil),       // 47: proto.ModelDefaultConfig
	(*FileModelMeta)(nil),            // 48: proto.FileModelMeta
	(*EnsembleModelMeta)(nil),        // 49: proto.EnsembleModelMeta
	(*ModelContentFile)(nil),         // 50: proto.ModelContentFile
	(*SubModelAlias)(nil),            // 51: proto.SubModelAlias
	(*AtomSubModel)(nil),             // 52: proto.AtomSubModel
	(*AtomModelInfo)(nil),            // 53: proto.AtomModelInfo
	(*PythonCodeInfo)(nil),           // 54: proto.PythonCodeInfo
	(*EnsembleConfigInfo)(nil),       // 55: proto.EnsembleConfigInfo
	(*SubModelInfo)(nil),             // 56: proto.SubModelInfo
	(*SubModel)(nil),                 // 57: proto.SubModel
	(*FileList)(nil),                 // 58: proto.FileList
	(*EnsembleModelRelease)(nil),     // 59: proto.EnsembleModelRelease
	(*ImageConfig)(nil),              // 60: proto.ImageConfig
	(*ImageModelMeta)(nil),           // 61: proto.ImageModelMeta
	(*ImageModelRelease)(nil),        // 62: proto.ImageModelRelease
	(*DeploymentDetail)(nil),         // 63: proto.DeploymentDetail
	(*Deployment)(nil),               // 64: proto.Deployment
	(*LifeCycle)(nil),                // 65: proto.LifeCycle
	(*ModelDeployment)(nil),          // 66: proto.ModelDeployment
	(*NodeStats)(nil),                // 67: proto.NodeStats
	(*ResourceStats)(nil),            // 68: proto.ResourceStats
	(*SystemInfo)(nil),               // 69: proto.SystemInfo
	(*NodeInfo)(nil),                 // 70: proto.NodeInfo
	(*HardwareInfo)(nil),             // 71: proto.HardwareInfo
	(*HardwareRange)(nil),            // 72: proto.HardwareRange
	(*DeploySys)(nil),                // 73: proto.DeploySys
	(*HardwareResource)(nil),         // 74: proto.HardwareResource
	(*DeploySelector)(nil),           // 75: proto.DeploySelector
	(*NodeSelector)(nil),             // 76: proto.NodeSelector
	(*DeployResource)(nil),           // 77: proto.DeployResource
	(*AcceleratorCard)(nil),          // 78: proto.AcceleratorCard
	(*ModelRuntime)(nil),             // 79: proto.ModelRuntime
	(*ModelServices)(nil),            // 80: proto.ModelServices
	(*ModelService)(nil),             // 81: proto.ModelService
	(*InvokeAsTool)(nil),             // 82: proto.InvokeAsTool
	(*InvokeParam)(nil),              // 83: proto.InvokeParam
	(*RemoteServiceDetail)(nil),      // 84: proto.RemoteServiceDetail
	(*RemoteServiceProxy)(nil),       // 85: proto.RemoteServiceProxy
	(*Configurable)(nil),             // 86: proto.Configurable
	(*RemoteServiceConfig)(nil),      // 87: proto.RemoteServiceConfig
	(*RemoteServiceStatus)(nil),      // 88: proto.RemoteServiceStatus
	(*Member)(nil),                   // 89: proto.Member
	(*RemoteServicePublishInfo)(nil), // 90: proto.RemoteServicePublishInfo
	(*RemoteService)(nil),            // 91: proto.RemoteService
	(*SupportedInterfaces)(nil),      // 92: proto.SupportedInterfaces
	(*InterfaceSpec)(nil),            // 93: proto.InterfaceSpec
	(*EvaluationDataset)(nil),        // 94: proto.EvaluationDataset
	(*CsvDataSetAddition)(nil),       // 95: proto.CsvDataSetAddition
	(*Csv)(nil),                      // 96: proto.Csv
	(*CsvRow)(nil),                   // 97: proto.CsvRow
	(*CsvHeader)(nil),                // 98: proto.CsvHeader
	(*StageScript)(nil),              // 99: proto.StageScript
	(*EvaluationScriptBase)(nil),     // 100: proto.EvaluationScriptBase
	(*EvaluationScript)(nil),         // 101: proto.EvaluationScript
	(*EvaluationScriptTemplate)(nil), // 102: proto.EvaluationScriptTemplate
	(*Metrics)(nil),                  // 103: proto.Metrics
	(*Curve)(nil),                    // 104: proto.Curve
	(*StandardMetricReport)(nil),     // 105: proto.StandardMetricReport
	(*SingleMetric)(nil),             // 106: proto.SingleMetric
	(*TableMetric)(nil),              // 107: proto.TableMetric
	(*FloatArray)(nil),               // 108: proto.FloatArray
	(*TwoDFloatArray)(nil),           // 109: proto.TwoDFloatArray
	(*GraphMetric)(nil),              // 110: proto.GraphMetric
	(*ComplexGraphMetric)(nil),       // 111: proto.ComplexGraphMetric
	(*PieGraphMetric)(nil),           // 112: proto.PieGraphMetric
	(*EvaluationResult)(nil),         // 113: proto.EvaluationResult
	(*SubModelPerformance)(nil),      // 114: proto.SubModelPerformance
	(*PortInfo)(nil),                 // 115: proto.PortInfo
	(*ServiceStatus)(nil),            // 116: proto.ServiceStatus
	(*Status)(nil),                   // 117: proto.Status
	(*Bucket)(nil),                   // 118: proto.Bucket
	(*Feature)(nil),                  // 119: proto.Feature
	(*TrainingDataDistribution)(nil), // 120: proto.TrainingDataDistribution
	nil,                              // 121: proto.SpaceInfo.IndustryLabelsEntry
	nil,                              // 122: proto.SpaceInfo.IsSelectedEntry
	nil,                              // 123: proto.ModelDetail.LabelsEntry
	nil,                              // 124: proto.ModelDetail.BaselinesEntry
	nil,                              // 125: proto.ModelReleaseDetail.LabelsEntry
	nil,                              // 126: proto.ModelReleaseDetail.ComputationAttributesEntry
	nil,                              // 127: proto.ModelDefaultConfig.ValuesEntry
	nil,                              // 128: proto.FileModelMeta.TrainingDataDistributionsEntry
	nil,                              // 129: proto.ImageConfig.EnvsEntry
	nil,                              // 130: proto.ImageConfig.LabelsEntry
	nil,                              // 131: proto.ImageConfig.VolumesEntry
	nil,                              // 132: proto.DeploymentDetail.LabelsEntry
	nil,                              // 133: proto.Deployment.ValuesEntry
	nil,                              // 134: proto.Deployment.DeployValuesEntry
	nil,                              // 135: proto.NodeStats.GpusStatsEntry
	nil,                              // 136: proto.RemoteServiceDetail.LabelsEntry
	nil,                              // 137: proto.RemoteServiceConfig.ComputationAttributesEntry
	nil,                              // 138: proto.Csv.RowEntry
	nil,                              // 139: proto.EvaluationResult.ModelMetricsEntry
	nil,                              // 140: proto.EvaluationResult.CurvesEntry
	nil,                              // 141: proto.EvaluationResult.ConfusionMatrix2Entry
	nil,                              // 142: proto.EvaluationResult.PerformanceMetricsEntry
	nil,                              // 143: proto.SubModelPerformance.PerformanceEntry
	nil,                              // 144: proto.Status.IndicatorsEntry
	(*Relation)(nil),                 // 145: proto.Relation
	(*DynamicParam)(nil),             // 146: proto.DynamicParam
	(*common.AscendResourceCfg)(nil), // 147: common.AscendResourceCfg
	(*serving.GuardrailsConfig)(nil), // 148: serving.GuardrailsConfig
	(*common.Endpoint)(nil),          // 149: commons.Endpoint
	(*serving.RateLimit)(nil),        // 150: serving.RateLimit
	(*anypb.Any)(nil),                // 151: google.protobuf.Any
}
var file_proto_model_proto_depIdxs = []int32{
	35,  // 0: proto.Model.domain:type_name -> proto.ModelDomain
	38,  // 1: proto.Model.detail:type_name -> proto.ModelDetail
	32,  // 2: proto.Model.stats:type_name -> proto.ModelStats
	36,  // 3: proto.Model.apis:type_name -> proto.ModelApi
	39,  // 4: proto.Model.attachments:type_name -> proto.Attachment
	4,   // 5: proto.Model.training_template:type_name -> proto.TrainingTemplate
	1,   // 6: proto.Model.asset_type:type_name -> proto.AssetType
	121, // 7: proto.SpaceInfo.industry_labels:type_name -> proto.SpaceInfo.IndustryLabelsEntry
	122, // 8: proto.SpaceInfo.is_selected:type_name -> proto.SpaceInfo.IsSelectedEntry
	43,  // 9: proto.ModelStats.latest_release:type_name -> proto.ModelReleaseBase
	43,  // 10: proto.ModelStats.baseline_release:type_name -> proto.ModelReleaseBase
	43,  // 11: proto.ModelStats.releases_info:type_name -> proto.ModelReleaseBase
	33,  // 12: proto.ModelStats.usage_count:type_name -> proto.ModelUsageCount
	2,   // 13: proto.DataModality.kind:type_name -> proto.ModelKind
	3,   // 14: proto.DataModality.sub_kind:type_name -> proto.ModelSubKind
	2,   // 15: proto.ModelDomain.kind:type_name -> proto.ModelKind
	3,   // 16: proto.ModelDomain.sub_kind:type_name -> proto.ModelSubKind
	5,   // 17: proto.ModelDomain.type:type_name -> proto.ModelType
	6,   // 18: proto.ModelDomain.subtype:type_name -> proto.ModelSubType
	8,   // 19: proto.ModelDomain.schedule_mode:type_name -> proto.ModelScheduleMode
	2,   // 20: proto.ModelDomain.output_kind:type_name -> proto.ModelKind
	3,   // 21: proto.ModelDomain.output_sub_kind:type_name -> proto.ModelSubKind
	7,   // 22: proto.ModelDomain.algorithm:type_name -> proto.ModelAlgorithm
	34,  // 23: proto.ModelDomain.components:type_name -> proto.DataModality
	37,  // 24: proto.ModelApi.inputs:type_name -> proto.ModelParam
	37,  // 25: proto.ModelApi.outputs:type_name -> proto.ModelParam
	0,   // 26: proto.ModelParam.data_type:type_name -> proto.DataType
	9,   // 27: proto.ModelParam.format:type_name -> proto.ModelParamFormat
	123, // 28: proto.ModelDetail.labels:type_name -> proto.ModelDetail.LabelsEntry
	124, // 29: proto.ModelDetail.baselines:type_name -> proto.ModelDetail.BaselinesEntry
	145, // 30: proto.ModelDetail.relations:type_name -> proto.Relation
	125, // 31: proto.ModelReleaseDetail.labels:type_name -> proto.ModelReleaseDetail.LabelsEntry
	145, // 32: proto.ModelReleaseDetail.relations:type_name -> proto.Relation
	39,  // 33: proto.ModelReleaseDetail.attachments:type_name -> proto.Attachment
	126, // 34: proto.ModelReleaseDetail.computation_attributes:type_name -> proto.ModelReleaseDetail.ComputationAttributesEntry
	11,  // 35: proto.FilePath.protocol:type_name -> proto.RepoAddrProtocol
	40,  // 36: proto.ModelReleaseBase.detail:type_name -> proto.ModelReleaseDetail
	45,  // 37: proto.ModelReleaseBase.stats:type_name -> proto.ModelReleaseStats
	72,  // 38: proto.ModelReleaseBase.hardware_range:type_name -> proto.HardwareRange
	47,  // 39: proto.ModelReleaseBase.default_config:type_name -> proto.ModelDefaultConfig
	36,  // 40: proto.ModelReleaseBase.model_apis:type_name -> proto.ModelApi
	42,  // 41: proto.ModelReleaseBase.training_template:type_name -> proto.TrainingTemplateConfig
	43,  // 42: proto.ModelRelease.release_base:type_name -> proto.ModelReleaseBase
	46,  // 43: proto.ModelRelease.model_meta:type_name -> proto.ModelMeta
	10,  // 44: proto.ModelReleaseStats.release_status:type_name -> proto.ModelReleaseStatus
	5,   // 45: proto.ModelMeta.model_type:type_name -> proto.ModelType
	48,  // 46: proto.ModelMeta.file_model_meta:type_name -> proto.FileModelMeta
	61,  // 47: proto.ModelMeta.image_model_meta:type_name -> proto.ImageModelMeta
	49,  // 48: proto.ModelMeta.ensemble_model_meta:type_name -> proto.EnsembleModelMeta
	6,   // 49: proto.ModelMeta.model_sub_type:type_name -> proto.ModelSubType
	19,  // 50: proto.ModelDefaultConfig.runtime:type_name -> proto.ModelRuntimeType
	77,  // 51: proto.ModelDefaultConfig.resource:type_name -> proto.DeployResource
	127, // 52: proto.ModelDefaultConfig.values:type_name -> proto.ModelDefaultConfig.ValuesEntry
	146, // 53: proto.ModelDefaultConfig.deployment_params:type_name -> proto.DynamicParam
	146, // 54: proto.ModelDefaultConfig.inference_params:type_name -> proto.DynamicParam
	128, // 55: proto.FileModelMeta.training_data_distributions:type_name -> proto.FileModelMeta.TrainingDataDistributionsEntry
	13,  // 56: proto.EnsembleModelMeta.mode:type_name -> proto.ModelEnsembleMode
	57,  // 57: proto.EnsembleModelMeta.sub_models:type_name -> proto.SubModel
	14,  // 58: proto.ModelContentFile.type:type_name -> proto.FileType
	12,  // 59: proto.SubModelAlias.model_type:type_name -> proto.SubModelType
	51,  // 60: proto.AtomSubModel.alias:type_name -> proto.SubModelAlias
	50,  // 61: proto.AtomSubModel.files:type_name -> proto.ModelContentFile
	53,  // 62: proto.SubModelInfo.atom_model_info:type_name -> proto.AtomModelInfo
	54,  // 63: proto.SubModelInfo.python_code_info:type_name -> proto.PythonCodeInfo
	55,  // 64: proto.SubModelInfo.ensemble_config_info:type_name -> proto.EnsembleConfigInfo
	12,  // 65: proto.SubModel.type:type_name -> proto.SubModelType
	56,  // 66: proto.SubModel.info:type_name -> proto.SubModelInfo
	50,  // 67: proto.SubModel.files:type_name -> proto.ModelContentFile
	50,  // 68: proto.FileList.files:type_name -> proto.ModelContentFile
	43,  // 69: proto.EnsembleModelRelease.model_release:type_name -> proto.ModelReleaseBase
	49,  // 70: proto.EnsembleModelRelease.model_meta:type_name -> proto.EnsembleModelMeta
	129, // 71: proto.ImageConfig.envs:type_name -> proto.ImageConfig.EnvsEntry
	130, // 72: proto.ImageConfig.labels:type_name -> proto.ImageConfig.LabelsEntry
	131, // 73: proto.ImageConfig.volumes:type_name -> proto.ImageConfig.VolumesEntry
	60,  // 74: proto.ImageModelMeta.config:type_name -> proto.ImageConfig
	43,  // 75: proto.ImageModelRelease.model_release:type_name -> proto.ModelReleaseBase
	61,  // 76: proto.ImageModelRelease.model_meta:type_name -> proto.ImageModelMeta
	132, // 77: proto.DeploymentDetail.labels:type_name -> proto.DeploymentDetail.LabelsEntry
	19,  // 78: proto.Deployment.runtime_type:type_name -> proto.ModelRuntimeType
	77,  // 79: proto.Deployment.resource:type_name -> proto.DeployResource
	75,  // 80: proto.Deployment.selector:type_name -> proto.DeploySelector
	133, // 81: proto.Deployment.values:type_name -> proto.Deployment.ValuesEntry
	63,  // 82: proto.Deployment.detail:type_name -> proto.DeploymentDetail
	117, // 83: proto.Deployment.status:type_name -> proto.Status
	15,  // 84: proto.Deployment.type:type_name -> proto.DeploymentType
	134, // 85: proto.Deployment.deploy_values:type_name -> proto.Deployment.DeployValuesEntry
	30,  // 86: proto.Deployment.SpaceInfo:type_name -> proto.SpaceInfo
	29,  // 87: proto.ModelDeployment.model:type_name -> proto.Model
	64,  // 88: proto.ModelDeployment.deployment:type_name -> proto.Deployment
	44,  // 89: proto.ModelDeployment.release:type_name -> proto.ModelRelease
	68,  // 90: proto.NodeStats.cpu_stats:type_name -> proto.ResourceStats
	68,  // 91: proto.NodeStats.mem_stats:type_name -> proto.ResourceStats
	68,  // 92: proto.NodeStats.disk_stats:type_name -> proto.ResourceStats
	135, // 93: proto.NodeStats.gpus_stats:type_name -> proto.NodeStats.GpusStatsEntry
	16,  // 94: proto.NodeInfo.roles:type_name -> proto.NodeRole
	74,  // 95: proto.NodeInfo.capacity:type_name -> proto.HardwareResource
	74,  // 96: proto.NodeInfo.allocatable:type_name -> proto.HardwareResource
	69,  // 97: proto.NodeInfo.system_info:type_name -> proto.SystemInfo
	71,  // 98: proto.NodeInfo.hardware_info:type_name -> proto.HardwareInfo
	67,  // 99: proto.NodeInfo.node_stats:type_name -> proto.NodeStats
	17,  // 100: proto.HardwareInfo.cpu_arch:type_name -> proto.CPUArch
	78,  // 101: proto.HardwareInfo.acc_cards:type_name -> proto.AcceleratorCard
	17,  // 102: proto.HardwareRange.arches:type_name -> proto.CPUArch
	18,  // 103: proto.HardwareRange.acc_types:type_name -> proto.AcceleratorType
	70,  // 104: proto.DeploySys.nodes:type_name -> proto.NodeInfo
	79,  // 105: proto.DeploySys.runtimes:type_name -> proto.ModelRuntime
	76,  // 106: proto.DeploySelector.nodes:type_name -> proto.NodeSelector
	74,  // 107: proto.DeployResource.request:type_name -> proto.HardwareResource
	74,  // 108: proto.DeployResource.limit:type_name -> proto.HardwareResource
	147, // 109: proto.DeployResource.ascend_cfg:type_name -> common.AscendResourceCfg
	18,  // 110: proto.AcceleratorCard.type:type_name -> proto.AcceleratorType
	19,  // 111: proto.ModelRuntime.runtime_type:type_name -> proto.ModelRuntimeType
	146, // 112: proto.ModelRuntime.runtime_params:type_name -> proto.DynamicParam
	5,   // 113: proto.ModelRuntime.types:type_name -> proto.ModelType
	6,   // 114: proto.ModelRuntime.sub_types:type_name -> proto.ModelSubType
	72,  // 115: proto.ModelRuntime.hardware_range:type_name -> proto.HardwareRange
	81,  // 116: proto.ModelServices.model_service:type_name -> proto.ModelService
	20,  // 117: proto.ModelService.schema:type_name -> proto.ModelServiceSchema
	21,  // 118: proto.ModelService.type:type_name -> proto.ModelServiceType
	36,  // 119: proto.ModelService.apis:type_name -> proto.ModelApi
	117, // 120: proto.ModelService.status:type_name -> proto.Status
	2,   // 121: proto.ModelService.kind:type_name -> proto.ModelKind
	3,   // 122: proto.ModelService.sub_kind:type_name -> proto.ModelSubKind
	146, // 123: proto.ModelService.inference_params:type_name -> proto.DynamicParam
	82,  // 124: proto.ModelService.invoke_as_tool:type_name -> proto.InvokeAsTool
	87,  // 125: proto.ModelService.remote_service_config:type_name -> proto.RemoteServiceConfig
	29,  // 126: proto.ModelService.reference_model:type_name -> proto.Model
	44,  // 127: proto.ModelService.reference_release:type_name -> proto.ModelRelease
	91,  // 128: proto.ModelService.reference_remote_service:type_name -> proto.RemoteService
	148, // 129: proto.ModelService.guardrails_config:type_name -> serving.GuardrailsConfig
	149, // 130: proto.ModelService.custom_service_endpoints:type_name -> commons.Endpoint
	22,  // 131: proto.InvokeAsTool.invoke_method:type_name -> proto.ModelServiceInvokeMethod
	83,  // 132: proto.InvokeAsTool.invoke_params:type_name -> proto.InvokeParam
	136, // 133: proto.RemoteServiceDetail.labels:type_name -> proto.RemoteServiceDetail.LabelsEntry
	24,  // 134: proto.RemoteServiceProxy.scheme:type_name -> proto.ProxyScheme
	23,  // 135: proto.RemoteServiceConfig.method:type_name -> proto.RemoteServiceMethod
	86,  // 136: proto.RemoteServiceConfig.query_params:type_name -> proto.Configurable
	86,  // 137: proto.RemoteServiceConfig.path_params:type_name -> proto.Configurable
	86,  // 138: proto.RemoteServiceConfig.headers:type_name -> proto.Configurable
	85,  // 139: proto.RemoteServiceConfig.proxy:type_name -> proto.RemoteServiceProxy
	137, // 140: proto.RemoteServiceConfig.computation_attributes:type_name -> proto.RemoteServiceConfig.ComputationAttributesEntry
	25,  // 141: proto.RemoteServiceConfig.interface_spec:type_name -> proto.InterfaceSpecName
	150, // 142: proto.RemoteServicePublishInfo.rate_limit:type_name -> serving.RateLimit
	150, // 143: proto.RemoteServicePublishInfo.user_rate_limit:type_name -> serving.RateLimit
	89,  // 144: proto.RemoteServicePublishInfo.members:type_name -> proto.Member
	2,   // 145: proto.RemoteService.kind:type_name -> proto.ModelKind
	3,   // 146: proto.RemoteService.sub_kind:type_name -> proto.ModelSubKind
	84,  // 147: proto.RemoteService.detail:type_name -> proto.RemoteServiceDetail
	87,  // 148: proto.RemoteService.api_config:type_name -> proto.RemoteServiceConfig
	88,  // 149: proto.RemoteService.status:type_name -> proto.RemoteServiceStatus
	90,  // 150: proto.RemoteService.publish_info:type_name -> proto.RemoteServicePublishInfo
	146, // 151: proto.RemoteService.inference_params:type_name -> proto.DynamicParam
	93,  // 152: proto.SupportedInterfaces.interface_specs:type_name -> proto.InterfaceSpec
	25,  // 153: proto.InterfaceSpec.id:type_name -> proto.InterfaceSpecName
	2,   // 154: proto.InterfaceSpec.kind:type_name -> proto.ModelKind
	3,   // 155: proto.InterfaceSpec.sub_kind:type_name -> proto.ModelSubKind
	87,  // 156: proto.InterfaceSpec.api_config_template:type_name -> proto.RemoteServiceConfig
	146, // 157: proto.InterfaceSpec.default_inference_params:type_name -> proto.DynamicParam
	27,  // 158: proto.EvaluationDataset.from:type_name -> proto.EvaluationDatasetFromType
	2,   // 159: proto.EvaluationDataset.type:type_name -> proto.ModelKind
	95,  // 160: proto.EvaluationDataset.addition:type_name -> proto.CsvDataSetAddition
	98,  // 161: proto.Csv.headers:type_name -> proto.CsvHeader
	138, // 162: proto.Csv.row:type_name -> proto.Csv.RowEntry
	28,  // 163: proto.StageScript.stage:type_name -> proto.EvaluationStage
	100, // 164: proto.EvaluationScript.base_info:type_name -> proto.EvaluationScriptBase
	99,  // 165: proto.EvaluationScript.scripts:type_name -> proto.StageScript
	99,  // 166: proto.EvaluationScriptTemplate.templates:type_name -> proto.StageScript
	106, // 167: proto.StandardMetricReport.single_metric:type_name -> proto.SingleMetric
	107, // 168: proto.StandardMetricReport.table_metric:type_name -> proto.TableMetric
	110, // 169: proto.StandardMetricReport.graph_metric:type_name -> proto.GraphMetric
	112, // 170: proto.StandardMetricReport.pie_graph_metric:type_name -> proto.PieGraphMetric
	109, // 171: proto.TableMetric.data:type_name -> proto.TwoDFloatArray
	108, // 172: proto.TwoDFloatArray.value:type_name -> proto.FloatArray
	108, // 173: proto.GraphMetric.y_axis_data:type_name -> proto.FloatArray
	108, // 174: proto.ComplexGraphMetric.y_axis_data:type_name -> proto.FloatArray
	108, // 175: proto.PieGraphMetric.y_axis_data:type_name -> proto.FloatArray
	139, // 176: proto.EvaluationResult.model_metrics:type_name -> proto.EvaluationResult.ModelMetricsEntry
	140, // 177: proto.EvaluationResult.curves:type_name -> proto.EvaluationResult.CurvesEntry
	141, // 178: proto.EvaluationResult.confusion_matrix_2:type_name -> proto.EvaluationResult.ConfusionMatrix2Entry
	142, // 179: proto.EvaluationResult.performance_metrics:type_name -> proto.EvaluationResult.PerformanceMetricsEntry
	114, // 180: proto.EvaluationResult.sub_models_performance:type_name -> proto.SubModelPerformance
	143, // 181: proto.SubModelPerformance.performance:type_name -> proto.SubModelPerformance.PerformanceEntry
	115, // 182: proto.ServiceStatus.ports:type_name -> proto.PortInfo
	116, // 183: proto.Status.service_status:type_name -> proto.ServiceStatus
	144, // 184: proto.Status.indicators:type_name -> proto.Status.IndicatorsEntry
	118, // 185: proto.Feature.buckets:type_name -> proto.Bucket
	119, // 186: proto.TrainingDataDistribution.features:type_name -> proto.Feature
	120, // 187: proto.FileModelMeta.TrainingDataDistributionsEntry.value:type_name -> proto.TrainingDataDistribution
	68,  // 188: proto.NodeStats.GpusStatsEntry.value:type_name -> proto.ResourceStats
	97,  // 189: proto.Csv.RowEntry.value:type_name -> proto.CsvRow
	103, // 190: proto.EvaluationResult.ModelMetricsEntry.value:type_name -> proto.Metrics
	104, // 191: proto.EvaluationResult.CurvesEntry.value:type_name -> proto.Curve
	103, // 192: proto.EvaluationResult.ConfusionMatrix2Entry.value:type_name -> proto.Metrics
	103, // 193: proto.EvaluationResult.PerformanceMetricsEntry.value:type_name -> proto.Metrics
	103, // 194: proto.SubModelPerformance.PerformanceEntry.value:type_name -> proto.Metrics
	151, // 195: proto.Status.IndicatorsEntry.value:type_name -> google.protobuf.Any
	196, // [196:196] is the sub-list for method output_type
	196, // [196:196] is the sub-list for method input_type
	196, // [196:196] is the sub-list for extension type_name
	196, // [196:196] is the sub-list for extension extendee
	0,   // [0:196] is the sub-list for field type_name
}

func init() { file_proto_model_proto_init() }
func file_proto_model_proto_init() {
	if File_proto_model_proto != nil {
		return
	}
	file_proto_relation_proto_init()
	file_proto_dynamic_param_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpaceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSelectedReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelUsageCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataModality); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDomain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelApi); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilePath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainingTemplateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRelease); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDefaultConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileModelMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnsembleModelMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelContentFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubModelAlias); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtomSubModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtomModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PythonCodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnsembleConfigInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnsembleModelRelease); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageModelMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageModelRelease); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LifeCycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDeployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HardwareInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HardwareRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploySys); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HardwareResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploySelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceleratorCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRuntime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvokeAsTool); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvokeParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteServiceProxy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Configurable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteServiceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteServiceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Member); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteServicePublishInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportedInterfaces); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterfaceSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationDataset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CsvDataSetAddition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Csv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CsvRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CsvHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageScript); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationScriptBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationScript); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationScriptTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Curve); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StandardMetricReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FloatArray); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TwoDFloatArray); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraphMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComplexGraphMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PieGraphMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubModelPerformance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainingDataDistribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_model_proto_rawDesc,
			NumEnums:      29,
			NumMessages:   116,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_model_proto_goTypes,
		DependencyIndexes: file_proto_model_proto_depIdxs,
		EnumInfos:         file_proto_model_proto_enumTypes,
		MessageInfos:      file_proto_model_proto_msgTypes,
	}.Build()
	File_proto_model_proto = out.File
	file_proto_model_proto_rawDesc = nil
	file_proto_model_proto_goTypes = nil
	file_proto_model_proto_depIdxs = nil
}
