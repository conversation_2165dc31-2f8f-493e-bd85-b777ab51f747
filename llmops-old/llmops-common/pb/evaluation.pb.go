// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/evaluation.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelSource int32

const (
	ModelSource_MODEL_SOURCE_UNKNOWN ModelSource = 0
	ModelSource_MODEL_SOURCE_MWH     ModelSource = 1
)

// Enum value maps for ModelSource.
var (
	ModelSource_name = map[int32]string{
		0: "MODEL_SOURCE_UNKNOWN",
		1: "MODEL_SOURCE_MWH",
	}
	ModelSource_value = map[string]int32{
		"MODEL_SOURCE_UNKNOWN": 0,
		"MODEL_SOURCE_MWH":     1,
	}
)

func (x ModelSource) Enum() *ModelSource {
	p := new(ModelSource)
	*p = x
	return p
}

func (x ModelSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelSource) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_evaluation_proto_enumTypes[0].Descriptor()
}

func (ModelSource) Type() protoreflect.EnumType {
	return &file_proto_evaluation_proto_enumTypes[0]
}

func (x ModelSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelSource.Descriptor instead.
func (ModelSource) EnumDescriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{0}
}

type EvalModelType int32

const (
	EvalModelType_MODEL_TYPE_SERVICE_INVOKE EvalModelType = 0 // 服务调用的方式
	EvalModelType_MODEL_TYPE_FILE_MOUNT     EvalModelType = 1 // 文件挂载的方式
)

// Enum value maps for EvalModelType.
var (
	EvalModelType_name = map[int32]string{
		0: "MODEL_TYPE_SERVICE_INVOKE",
		1: "MODEL_TYPE_FILE_MOUNT",
	}
	EvalModelType_value = map[string]int32{
		"MODEL_TYPE_SERVICE_INVOKE": 0,
		"MODEL_TYPE_FILE_MOUNT":     1,
	}
)

func (x EvalModelType) Enum() *EvalModelType {
	p := new(EvalModelType)
	*p = x
	return p
}

func (x EvalModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvalModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_evaluation_proto_enumTypes[1].Descriptor()
}

func (EvalModelType) Type() protoreflect.EnumType {
	return &file_proto_evaluation_proto_enumTypes[1]
}

func (x EvalModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvalModelType.Descriptor instead.
func (EvalModelType) EnumDescriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{1}
}

type EvalDataInputFormat int32

const (
	EvalDataInputFormat_EVAL_DATA_INPUT_FORMAT_UNKNOWN EvalDataInputFormat = 0
	EvalDataInputFormat_EVAL_DATA_INPUT_FORMAT_SYS     EvalDataInputFormat = 1
	EvalDataInputFormat_EVAL_DATA_INPUT_FORMAT_CUSTOM  EvalDataInputFormat = 2
)

// Enum value maps for EvalDataInputFormat.
var (
	EvalDataInputFormat_name = map[int32]string{
		0: "EVAL_DATA_INPUT_FORMAT_UNKNOWN",
		1: "EVAL_DATA_INPUT_FORMAT_SYS",
		2: "EVAL_DATA_INPUT_FORMAT_CUSTOM",
	}
	EvalDataInputFormat_value = map[string]int32{
		"EVAL_DATA_INPUT_FORMAT_UNKNOWN": 0,
		"EVAL_DATA_INPUT_FORMAT_SYS":     1,
		"EVAL_DATA_INPUT_FORMAT_CUSTOM":  2,
	}
)

func (x EvalDataInputFormat) Enum() *EvalDataInputFormat {
	p := new(EvalDataInputFormat)
	*p = x
	return p
}

func (x EvalDataInputFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvalDataInputFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_evaluation_proto_enumTypes[2].Descriptor()
}

func (EvalDataInputFormat) Type() protoreflect.EnumType {
	return &file_proto_evaluation_proto_enumTypes[2]
}

func (x EvalDataInputFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvalDataInputFormat.Descriptor instead.
func (EvalDataInputFormat) EnumDescriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{2}
}

type EvalMission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc         string             `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ProjectId    string             `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Config       *EvalMissionConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	Status       string             `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	UpdateTimeMs int64              `protobuf:"varint,7,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
	StartTimeMs  int64              `protobuf:"varint,8,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	StopTimeMs   int64              `protobuf:"varint,9,opt,name=stop_time_ms,json=stopTimeMs,proto3" json:"stop_time_ms,omitempty"`
	CreateUser   string             `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty"`
	RunInfo      *RunInfo           `protobuf:"bytes,11,opt,name=run_info,json=runInfo,proto3" json:"run_info,omitempty"`
	CreateTimeMs int64              `protobuf:"varint,12,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
}

func (x *EvalMission) Reset() {
	*x = EvalMission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalMission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalMission) ProtoMessage() {}

func (x *EvalMission) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalMission.ProtoReflect.Descriptor instead.
func (*EvalMission) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{0}
}

func (x *EvalMission) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EvalMission) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvalMission) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *EvalMission) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *EvalMission) GetConfig() *EvalMissionConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *EvalMission) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EvalMission) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *EvalMission) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *EvalMission) GetStopTimeMs() int64 {
	if x != nil {
		return x.StopTimeMs
	}
	return 0
}

func (x *EvalMission) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *EvalMission) GetRunInfo() *RunInfo {
	if x != nil {
		return x.RunInfo
	}
	return nil
}

func (x *EvalMission) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

type RunInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	NodeId string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
}

func (x *RunInfo) Reset() {
	*x = RunInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunInfo) ProtoMessage() {}

func (x *RunInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunInfo.ProtoReflect.Descriptor instead.
func (*RunInfo) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{1}
}

func (x *RunInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RunInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

type EvalMissionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelConfig *EvalModelConfig `protobuf:"bytes,1,opt,name=model_config,json=modelConfig,proto3" json:"model_config,omitempty"`
	DataConfig  *EvalDataConfig  `protobuf:"bytes,2,opt,name=data_config,json=dataConfig,proto3" json:"data_config,omitempty"`
	// Deprecated use serving.UnifyResource instead
	ResourceConfig      *DeployResource        `protobuf:"bytes,3,opt,name=resource_config,json=resourceConfig,proto3" json:"resource_config,omitempty"`
	UnifyResourceConfig *serving.UnifyResource `protobuf:"bytes,8,opt,name=unify_resource_config,json=unifyResourceConfig,proto3" json:"unify_resource_config,omitempty"`
	TemplateConfig      *EvalTemplateConfig    `protobuf:"bytes,4,opt,name=template_config,json=templateConfig,proto3" json:"template_config,omitempty"`
	// custom_config_mapping is extra file mapping defined when config mission
	// key: host config value: container config
	CustomConfigMapping []*MountConfig `protobuf:"bytes,5,rep,name=custom_config_mapping,json=customConfigMapping,proto3" json:"custom_config_mapping,omitempty"`
	// envs is extra environment variables, include 2 parts:
	// fixed env: defined in image template, has fixed key
	// custom env: defined in mission config
	Envs     []*EvalMissionEnv `protobuf:"bytes,6,rep,name=envs,proto3" json:"envs,omitempty"`
	BootArgs string            `protobuf:"bytes,7,opt,name=boot_args,json=bootArgs,proto3" json:"boot_args,omitempty"`
}

func (x *EvalMissionConfig) Reset() {
	*x = EvalMissionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalMissionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalMissionConfig) ProtoMessage() {}

func (x *EvalMissionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalMissionConfig.ProtoReflect.Descriptor instead.
func (*EvalMissionConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{2}
}

func (x *EvalMissionConfig) GetModelConfig() *EvalModelConfig {
	if x != nil {
		return x.ModelConfig
	}
	return nil
}

func (x *EvalMissionConfig) GetDataConfig() *EvalDataConfig {
	if x != nil {
		return x.DataConfig
	}
	return nil
}

func (x *EvalMissionConfig) GetResourceConfig() *DeployResource {
	if x != nil {
		return x.ResourceConfig
	}
	return nil
}

func (x *EvalMissionConfig) GetUnifyResourceConfig() *serving.UnifyResource {
	if x != nil {
		return x.UnifyResourceConfig
	}
	return nil
}

func (x *EvalMissionConfig) GetTemplateConfig() *EvalTemplateConfig {
	if x != nil {
		return x.TemplateConfig
	}
	return nil
}

func (x *EvalMissionConfig) GetCustomConfigMapping() []*MountConfig {
	if x != nil {
		return x.CustomConfigMapping
	}
	return nil
}

func (x *EvalMissionConfig) GetEnvs() []*EvalMissionEnv {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *EvalMissionConfig) GetBootArgs() string {
	if x != nil {
		return x.BootArgs
	}
	return ""
}

type EvalMissionEnv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// if evaluation env is built-in (like model service) or from evaluation template, it is fixed
	FixedKey bool `protobuf:"varint,3,opt,name=fixed_key,json=fixedKey,proto3" json:"fixed_key,omitempty"`
}

func (x *EvalMissionEnv) Reset() {
	*x = EvalMissionEnv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalMissionEnv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalMissionEnv) ProtoMessage() {}

func (x *EvalMissionEnv) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalMissionEnv.ProtoReflect.Descriptor instead.
func (*EvalMissionEnv) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{3}
}

func (x *EvalMissionEnv) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *EvalMissionEnv) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *EvalMissionEnv) GetFixedKey() bool {
	if x != nil {
		return x.FixedKey
	}
	return false
}

type EvalModelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string          `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName    string          `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ReleaseId    string          `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName  string          `protobuf:"bytes,4,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ModelKind    ModelKind       `protobuf:"varint,5,opt,name=model_kind,json=modelKind,proto3,enum=proto.ModelKind" json:"model_kind,omitempty"`
	ModelSubKind ModelSubKind    `protobuf:"varint,6,opt,name=model_sub_kind,json=modelSubKind,proto3,enum=proto.ModelSubKind" json:"model_sub_kind,omitempty"`
	ModelSource  ModelSource     `protobuf:"varint,7,opt,name=model_source,json=modelSource,proto3,enum=proto.ModelSource" json:"model_source,omitempty"`
	DeploymentId string          `protobuf:"bytes,8,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"`
	ServiceEnv   *EvalMissionEnv `protobuf:"bytes,9,opt,name=service_env,json=serviceEnv,proto3" json:"service_env,omitempty"`
	// model_mount allow user use model by mount host path. key: model location on host: model location in container
	ModelMount   []*MountConfig `protobuf:"bytes,10,rep,name=model_mount,json=modelMount,proto3" json:"model_mount,omitempty"`
	Version      string         `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`
	ModelType    EvalModelType  `protobuf:"varint,12,opt,name=model_type,json=modelType,proto3,enum=proto.EvalModelType" json:"model_type,omitempty"`
	FullUrl      string         `protobuf:"bytes,13,opt,name=full_url,json=fullUrl,proto3" json:"full_url,omitempty"`
	ModelService *ModelService  `protobuf:"bytes,14,opt,name=model_service,json=modelService,proto3" json:"model_service,omitempty"`
}

func (x *EvalModelConfig) Reset() {
	*x = EvalModelConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalModelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalModelConfig) ProtoMessage() {}

func (x *EvalModelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalModelConfig.ProtoReflect.Descriptor instead.
func (*EvalModelConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{4}
}

func (x *EvalModelConfig) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *EvalModelConfig) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *EvalModelConfig) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *EvalModelConfig) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *EvalModelConfig) GetModelKind() ModelKind {
	if x != nil {
		return x.ModelKind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *EvalModelConfig) GetModelSubKind() ModelSubKind {
	if x != nil {
		return x.ModelSubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *EvalModelConfig) GetModelSource() ModelSource {
	if x != nil {
		return x.ModelSource
	}
	return ModelSource_MODEL_SOURCE_UNKNOWN
}

func (x *EvalModelConfig) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

func (x *EvalModelConfig) GetServiceEnv() *EvalMissionEnv {
	if x != nil {
		return x.ServiceEnv
	}
	return nil
}

func (x *EvalModelConfig) GetModelMount() []*MountConfig {
	if x != nil {
		return x.ModelMount
	}
	return nil
}

func (x *EvalModelConfig) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *EvalModelConfig) GetModelType() EvalModelType {
	if x != nil {
		return x.ModelType
	}
	return EvalModelType_MODEL_TYPE_SERVICE_INVOKE
}

func (x *EvalModelConfig) GetFullUrl() string {
	if x != nil {
		return x.FullUrl
	}
	return ""
}

func (x *EvalModelConfig) GetModelService() *ModelService {
	if x != nil {
		return x.ModelService
	}
	return nil
}

type MountConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fixed_mount is mount point in container, defined in template
	FixedMount *TemplateMount `protobuf:"bytes,1,opt,name=fixed_mount,json=fixedMount,proto3" json:"fixed_mount,omitempty"`
	// custom_mount is mount path of host or pvc
	CustomMount string `protobuf:"bytes,2,opt,name=custom_mount,json=customMount,proto3" json:"custom_mount,omitempty"`
}

func (x *MountConfig) Reset() {
	*x = MountConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountConfig) ProtoMessage() {}

func (x *MountConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountConfig.ProtoReflect.Descriptor instead.
func (*MountConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{5}
}

func (x *MountConfig) GetFixedMount() *TemplateMount {
	if x != nil {
		return x.FixedMount
	}
	return nil
}

func (x *MountConfig) GetCustomMount() string {
	if x != nil {
		return x.CustomMount
	}
	return ""
}

type EvalDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InputFormat  EvalDataInputFormat `protobuf:"varint,1,opt,name=input_format,json=inputFormat,proto3,enum=proto.EvalDataInputFormat" json:"input_format,omitempty"`
	NameOrLink   string              `protobuf:"bytes,2,opt,name=name_or_link,json=nameOrLink,proto3" json:"name_or_link,omitempty"`
	Name         string              `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Kind         ModelKind           `protobuf:"varint,4,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind      ModelSubKind        `protobuf:"varint,5,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	Columns      []string            `protobuf:"bytes,6,rep,name=columns,proto3" json:"columns,omitempty"`
	RowsCount    int64               `protobuf:"varint,7,opt,name=rows_count,json=rowsCount,proto3" json:"rows_count,omitempty"`
	Creator      string              `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator,omitempty"`
	UpdateTimeMs int64               `protobuf:"varint,9,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
	Preview      []*CsvRow           `protobuf:"bytes,10,rep,name=preview,proto3" json:"preview,omitempty"`
	Id           string              `protobuf:"bytes,11,opt,name=id,proto3" json:"id,omitempty"`
	// data_mount key: mount path of host; value: mount path in container(already defined in image template)
	DataMount []*MountConfig `protobuf:"bytes,12,rep,name=data_mount,json=dataMount,proto3" json:"data_mount,omitempty"`
	Path      string         `protobuf:"bytes,13,opt,name=path,proto3" json:"path,omitempty"`
	DataType  string         `protobuf:"bytes,14,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	VersionId string         `protobuf:"bytes,15,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
}

func (x *EvalDataConfig) Reset() {
	*x = EvalDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalDataConfig) ProtoMessage() {}

func (x *EvalDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalDataConfig.ProtoReflect.Descriptor instead.
func (*EvalDataConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{6}
}

func (x *EvalDataConfig) GetInputFormat() EvalDataInputFormat {
	if x != nil {
		return x.InputFormat
	}
	return EvalDataInputFormat_EVAL_DATA_INPUT_FORMAT_UNKNOWN
}

func (x *EvalDataConfig) GetNameOrLink() string {
	if x != nil {
		return x.NameOrLink
	}
	return ""
}

func (x *EvalDataConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvalDataConfig) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *EvalDataConfig) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *EvalDataConfig) GetColumns() []string {
	if x != nil {
		return x.Columns
	}
	return nil
}

func (x *EvalDataConfig) GetRowsCount() int64 {
	if x != nil {
		return x.RowsCount
	}
	return 0
}

func (x *EvalDataConfig) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *EvalDataConfig) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *EvalDataConfig) GetPreview() []*CsvRow {
	if x != nil {
		return x.Preview
	}
	return nil
}

func (x *EvalDataConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EvalDataConfig) GetDataMount() []*MountConfig {
	if x != nil {
		return x.DataMount
	}
	return nil
}

func (x *EvalDataConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *EvalDataConfig) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *EvalDataConfig) GetVersionId() string {
	if x != nil {
		return x.VersionId
	}
	return ""
}

// EvalTemplateConfig 是摘要评估模板的摘要信息，存储在mission中
type EvalTemplateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Source  TemplateSource `protobuf:"varint,2,opt,name=source,proto3,enum=proto.TemplateSource" json:"source,omitempty"`
	Name    string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Kind    ModelKind      `protobuf:"varint,4,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind ModelSubKind   `protobuf:"varint,5,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	Desc    string         `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *EvalTemplateConfig) Reset() {
	*x = EvalTemplateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalTemplateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalTemplateConfig) ProtoMessage() {}

func (x *EvalTemplateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalTemplateConfig.ProtoReflect.Descriptor instead.
func (*EvalTemplateConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{7}
}

func (x *EvalTemplateConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EvalTemplateConfig) GetSource() TemplateSource {
	if x != nil {
		return x.Source
	}
	return TemplateSource_TEMPLATE_SOURCE_SYSTEM
}

func (x *EvalTemplateConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvalTemplateConfig) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *EvalTemplateConfig) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *EvalTemplateConfig) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// EvalTemplateCustomConfig is custom config of evaluation
type EvalTemplateCustomConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelKind     ModelKind           `protobuf:"varint,1,opt,name=model_kind,json=modelKind,proto3,enum=proto.ModelKind" json:"model_kind,omitempty"`
	ModelSubKind  ModelSubKind        `protobuf:"varint,2,opt,name=model_sub_kind,json=modelSubKind,proto3,enum=proto.ModelSubKind" json:"model_sub_kind,omitempty"`
	InputFormat   EvalDataInputFormat `protobuf:"varint,3,opt,name=input_format,json=inputFormat,proto3,enum=proto.EvalDataInputFormat" json:"input_format,omitempty"`
	ExtOrLink     string              `protobuf:"bytes,4,opt,name=ext_or_link,json=extOrLink,proto3" json:"ext_or_link,omitempty"`
	OutputPath    string              `protobuf:"bytes,5,opt,name=output_path,json=outputPath,proto3" json:"output_path,omitempty"`
	OutputMetrics []*OutputMetric     `protobuf:"bytes,6,rep,name=output_metrics,json=outputMetrics,proto3" json:"output_metrics,omitempty"`
	ModelType     EvalModelType       `protobuf:"varint,7,opt,name=model_type,json=modelType,proto3,enum=proto.EvalModelType" json:"model_type,omitempty"`
}

func (x *EvalTemplateCustomConfig) Reset() {
	*x = EvalTemplateCustomConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvalTemplateCustomConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvalTemplateCustomConfig) ProtoMessage() {}

func (x *EvalTemplateCustomConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvalTemplateCustomConfig.ProtoReflect.Descriptor instead.
func (*EvalTemplateCustomConfig) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{8}
}

func (x *EvalTemplateCustomConfig) GetModelKind() ModelKind {
	if x != nil {
		return x.ModelKind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *EvalTemplateCustomConfig) GetModelSubKind() ModelSubKind {
	if x != nil {
		return x.ModelSubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *EvalTemplateCustomConfig) GetInputFormat() EvalDataInputFormat {
	if x != nil {
		return x.InputFormat
	}
	return EvalDataInputFormat_EVAL_DATA_INPUT_FORMAT_UNKNOWN
}

func (x *EvalTemplateCustomConfig) GetExtOrLink() string {
	if x != nil {
		return x.ExtOrLink
	}
	return ""
}

func (x *EvalTemplateCustomConfig) GetOutputPath() string {
	if x != nil {
		return x.OutputPath
	}
	return ""
}

func (x *EvalTemplateCustomConfig) GetOutputMetrics() []*OutputMetric {
	if x != nil {
		return x.OutputMetrics
	}
	return nil
}

func (x *EvalTemplateCustomConfig) GetModelType() EvalModelType {
	if x != nil {
		return x.ModelType
	}
	return EvalModelType_MODEL_TYPE_SERVICE_INVOKE
}

type OutputMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type     string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Desc     string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Category string `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
}

func (x *OutputMetric) Reset() {
	*x = OutputMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutputMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputMetric) ProtoMessage() {}

func (x *OutputMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputMetric.ProtoReflect.Descriptor instead.
func (*OutputMetric) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{9}
}

func (x *OutputMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OutputMetric) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OutputMetric) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *OutputMetric) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type Evaluation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string       `protobuf:"bytes,29,opt,name=name,proto3" json:"name,omitempty"`
	ModelId      string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName    string       `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ModelKind    ModelKind    `protobuf:"varint,4,opt,name=model_kind,json=modelKind,proto3,enum=proto.ModelKind" json:"model_kind,omitempty"`
	ModelSubKind ModelSubKind `protobuf:"varint,5,opt,name=model_sub_kind,json=modelSubKind,proto3,enum=proto.ModelSubKind" json:"model_sub_kind,omitempty"`
	ReleaseId    string       `protobuf:"bytes,6,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName  string       `protobuf:"bytes,7,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	Version      string       `protobuf:"bytes,26,opt,name=version,proto3" json:"version,omitempty"`
	// 由评估任务启动，不显示在评估列表
	DeploymentId string            `protobuf:"bytes,8,opt,name=deployment_id,json=deploymentId,proto3" json:"deployment_id,omitempty"`
	DatasetId    string            `protobuf:"bytes,9,opt,name=dataset_id,json=datasetId,proto3" json:"dataset_id,omitempty"`
	DatasetName  string            `protobuf:"bytes,10,opt,name=dataset_name,json=datasetName,proto3" json:"dataset_name,omitempty"`
	ScriptId     string            `protobuf:"bytes,11,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`
	ScriptName   string            `protobuf:"bytes,12,opt,name=script_name,json=scriptName,proto3" json:"script_name,omitempty"`
	Enable       bool              `protobuf:"varint,13,opt,name=enable,proto3" json:"enable,omitempty"`
	Creator      string            `protobuf:"bytes,14,opt,name=creator,proto3" json:"creator,omitempty"`
	Resource     *DeployResource   `protobuf:"bytes,15,opt,name=resource,proto3" json:"resource,omitempty"`
	Selector     *DeploySelector   `protobuf:"bytes,16,opt,name=selector,proto3" json:"selector,omitempty"`
	StartTimeMs  int64             `protobuf:"varint,17,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	EndTimeMs    int64             `protobuf:"varint,18,opt,name=end_time_ms,json=endTimeMs,proto3" json:"end_time_ms,omitempty"`
	CreateTimeMs int64             `protobuf:"varint,19,opt,name=create_time_ms,json=createTimeMs,proto3" json:"create_time_ms,omitempty"`
	UpdateTimeMs int64             `protobuf:"varint,20,opt,name=update_time_ms,json=updateTimeMs,proto3" json:"update_time_ms,omitempty"`
	Result       *EvaluationResult `protobuf:"bytes,21,opt,name=result,proto3" json:"result,omitempty"`
	Status       *Status           `protobuf:"bytes,22,opt,name=status,proto3" json:"status,omitempty"`
	ModelType    ModelType         `protobuf:"varint,23,opt,name=model_type,json=modelType,proto3,enum=proto.ModelType" json:"model_type,omitempty"`
	ModelSubType ModelSubType      `protobuf:"varint,24,opt,name=model_sub_type,json=modelSubType,proto3,enum=proto.ModelSubType" json:"model_sub_type,omitempty"`
	ProjectId    string            `protobuf:"bytes,25,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TemplateId   string            `protobuf:"bytes,27,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	MetricsDesc  []*OutputMetric   `protobuf:"bytes,28,rep,name=metrics_desc,json=metricsDesc,proto3" json:"metrics_desc,omitempty"`
}

func (x *Evaluation) Reset() {
	*x = Evaluation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_evaluation_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Evaluation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Evaluation) ProtoMessage() {}

func (x *Evaluation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_evaluation_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Evaluation.ProtoReflect.Descriptor instead.
func (*Evaluation) Descriptor() ([]byte, []int) {
	return file_proto_evaluation_proto_rawDescGZIP(), []int{10}
}

func (x *Evaluation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Evaluation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Evaluation) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *Evaluation) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *Evaluation) GetModelKind() ModelKind {
	if x != nil {
		return x.ModelKind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *Evaluation) GetModelSubKind() ModelSubKind {
	if x != nil {
		return x.ModelSubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *Evaluation) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *Evaluation) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *Evaluation) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Evaluation) GetDeploymentId() string {
	if x != nil {
		return x.DeploymentId
	}
	return ""
}

func (x *Evaluation) GetDatasetId() string {
	if x != nil {
		return x.DatasetId
	}
	return ""
}

func (x *Evaluation) GetDatasetName() string {
	if x != nil {
		return x.DatasetName
	}
	return ""
}

func (x *Evaluation) GetScriptId() string {
	if x != nil {
		return x.ScriptId
	}
	return ""
}

func (x *Evaluation) GetScriptName() string {
	if x != nil {
		return x.ScriptName
	}
	return ""
}

func (x *Evaluation) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Evaluation) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Evaluation) GetResource() *DeployResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *Evaluation) GetSelector() *DeploySelector {
	if x != nil {
		return x.Selector
	}
	return nil
}

func (x *Evaluation) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *Evaluation) GetEndTimeMs() int64 {
	if x != nil {
		return x.EndTimeMs
	}
	return 0
}

func (x *Evaluation) GetCreateTimeMs() int64 {
	if x != nil {
		return x.CreateTimeMs
	}
	return 0
}

func (x *Evaluation) GetUpdateTimeMs() int64 {
	if x != nil {
		return x.UpdateTimeMs
	}
	return 0
}

func (x *Evaluation) GetResult() *EvaluationResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *Evaluation) GetStatus() *Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Evaluation) GetModelType() ModelType {
	if x != nil {
		return x.ModelType
	}
	return ModelType_MODEL_TYPE_UNSPECIFIED
}

func (x *Evaluation) GetModelSubType() ModelSubType {
	if x != nil {
		return x.ModelSubType
	}
	return ModelSubType_MODEL_SUB_TYPE_UNSPECIFIED
}

func (x *Evaluation) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Evaluation) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Evaluation) GetMetricsDesc() []*OutputMetric {
	if x != nil {
		return x.MetricsDesc
	}
	return nil
}

var File_proto_evaluation_proto protoreflect.FileDescriptor

var file_proto_evaluation_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x6c,
	0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x8c, 0x03, 0x0a, 0x0b, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x20, 0x0a, 0x0c,
	0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x29, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73,
	0x22, 0x32, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x22, 0xe6, 0x03, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x0c, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3e, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a,
	0x15, 0x75, 0x6e, 0x69, 0x66, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x13, 0x75, 0x6e, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x0a, 0x0f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x46, 0x0a,
	0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x76, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x41, 0x72, 0x67, 0x73, 0x22, 0x55, 0x0a,
	0x0e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x76, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x4b, 0x65, 0x79, 0x22, 0xe6, 0x04, 0x0a, 0x0f, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69,
	0x6e, 0x64, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64,
	0x12, 0x35, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x76, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x6e, 0x76, 0x12, 0x33, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x75, 0x6c, 0x6c,
	0x55, 0x72, 0x6c, 0x12, 0x38, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x67, 0x0a,
	0x0b, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x0a, 0x0b,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a, 0x66, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x90, 0x04, 0x0a, 0x0e, 0x45, 0x76, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0b, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6f, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x61, 0x6d, 0x65, 0x4f, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x4b, 0x69, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x27, 0x0a,
	0x07, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x52, 0x6f, 0x77, 0x52, 0x07, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xd1, 0x01, 0x0a, 0x12, 0x45, 0x76,
	0x61, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x2d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0xf7, 0x02,
	0x0a, 0x18, 0x45, 0x76, 0x61, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x39, 0x0a, 0x0e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x72, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x4f,
	0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3a, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x66, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0xbb, 0x08, 0x0a, 0x0a, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0a,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69,
	0x6e, 0x64, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x39, 0x0a,
	0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31,
	0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x52, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x73, 0x63, 0x2a, 0x3d, 0x0a,
	0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x14,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4d, 0x57, 0x48, 0x10, 0x01, 0x2a, 0x49, 0x0a, 0x0d,
	0x45, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x2a, 0x7c, 0x0a, 0x13, 0x45, 0x76, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x22,
	0x0a, 0x1e, 0x45, 0x56, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x49, 0x4e, 0x50, 0x55,
	0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x56, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x53, 0x59, 0x53,
	0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x10, 0x02, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61,
	0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73,
	0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_evaluation_proto_rawDescOnce sync.Once
	file_proto_evaluation_proto_rawDescData = file_proto_evaluation_proto_rawDesc
)

func file_proto_evaluation_proto_rawDescGZIP() []byte {
	file_proto_evaluation_proto_rawDescOnce.Do(func() {
		file_proto_evaluation_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_evaluation_proto_rawDescData)
	})
	return file_proto_evaluation_proto_rawDescData
}

var file_proto_evaluation_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_evaluation_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_proto_evaluation_proto_goTypes = []interface{}{
	(ModelSource)(0),                 // 0: proto.ModelSource
	(EvalModelType)(0),               // 1: proto.EvalModelType
	(EvalDataInputFormat)(0),         // 2: proto.EvalDataInputFormat
	(*EvalMission)(nil),              // 3: proto.EvalMission
	(*RunInfo)(nil),                  // 4: proto.RunInfo
	(*EvalMissionConfig)(nil),        // 5: proto.EvalMissionConfig
	(*EvalMissionEnv)(nil),           // 6: proto.EvalMissionEnv
	(*EvalModelConfig)(nil),          // 7: proto.EvalModelConfig
	(*MountConfig)(nil),              // 8: proto.MountConfig
	(*EvalDataConfig)(nil),           // 9: proto.EvalDataConfig
	(*EvalTemplateConfig)(nil),       // 10: proto.EvalTemplateConfig
	(*EvalTemplateCustomConfig)(nil), // 11: proto.EvalTemplateCustomConfig
	(*OutputMetric)(nil),             // 12: proto.OutputMetric
	(*Evaluation)(nil),               // 13: proto.Evaluation
	(*DeployResource)(nil),           // 14: proto.DeployResource
	(*serving.UnifyResource)(nil),    // 15: serving.UnifyResource
	(ModelKind)(0),                   // 16: proto.ModelKind
	(ModelSubKind)(0),                // 17: proto.ModelSubKind
	(*ModelService)(nil),             // 18: proto.ModelService
	(*TemplateMount)(nil),            // 19: proto.TemplateMount
	(*CsvRow)(nil),                   // 20: proto.CsvRow
	(TemplateSource)(0),              // 21: proto.TemplateSource
	(*DeploySelector)(nil),           // 22: proto.DeploySelector
	(*EvaluationResult)(nil),         // 23: proto.EvaluationResult
	(*Status)(nil),                   // 24: proto.Status
	(ModelType)(0),                   // 25: proto.ModelType
	(ModelSubType)(0),                // 26: proto.ModelSubType
}
var file_proto_evaluation_proto_depIdxs = []int32{
	5,  // 0: proto.EvalMission.config:type_name -> proto.EvalMissionConfig
	4,  // 1: proto.EvalMission.run_info:type_name -> proto.RunInfo
	7,  // 2: proto.EvalMissionConfig.model_config:type_name -> proto.EvalModelConfig
	9,  // 3: proto.EvalMissionConfig.data_config:type_name -> proto.EvalDataConfig
	14, // 4: proto.EvalMissionConfig.resource_config:type_name -> proto.DeployResource
	15, // 5: proto.EvalMissionConfig.unify_resource_config:type_name -> serving.UnifyResource
	10, // 6: proto.EvalMissionConfig.template_config:type_name -> proto.EvalTemplateConfig
	8,  // 7: proto.EvalMissionConfig.custom_config_mapping:type_name -> proto.MountConfig
	6,  // 8: proto.EvalMissionConfig.envs:type_name -> proto.EvalMissionEnv
	16, // 9: proto.EvalModelConfig.model_kind:type_name -> proto.ModelKind
	17, // 10: proto.EvalModelConfig.model_sub_kind:type_name -> proto.ModelSubKind
	0,  // 11: proto.EvalModelConfig.model_source:type_name -> proto.ModelSource
	6,  // 12: proto.EvalModelConfig.service_env:type_name -> proto.EvalMissionEnv
	8,  // 13: proto.EvalModelConfig.model_mount:type_name -> proto.MountConfig
	1,  // 14: proto.EvalModelConfig.model_type:type_name -> proto.EvalModelType
	18, // 15: proto.EvalModelConfig.model_service:type_name -> proto.ModelService
	19, // 16: proto.MountConfig.fixed_mount:type_name -> proto.TemplateMount
	2,  // 17: proto.EvalDataConfig.input_format:type_name -> proto.EvalDataInputFormat
	16, // 18: proto.EvalDataConfig.kind:type_name -> proto.ModelKind
	17, // 19: proto.EvalDataConfig.sub_kind:type_name -> proto.ModelSubKind
	20, // 20: proto.EvalDataConfig.preview:type_name -> proto.CsvRow
	8,  // 21: proto.EvalDataConfig.data_mount:type_name -> proto.MountConfig
	21, // 22: proto.EvalTemplateConfig.source:type_name -> proto.TemplateSource
	16, // 23: proto.EvalTemplateConfig.kind:type_name -> proto.ModelKind
	17, // 24: proto.EvalTemplateConfig.sub_kind:type_name -> proto.ModelSubKind
	16, // 25: proto.EvalTemplateCustomConfig.model_kind:type_name -> proto.ModelKind
	17, // 26: proto.EvalTemplateCustomConfig.model_sub_kind:type_name -> proto.ModelSubKind
	2,  // 27: proto.EvalTemplateCustomConfig.input_format:type_name -> proto.EvalDataInputFormat
	12, // 28: proto.EvalTemplateCustomConfig.output_metrics:type_name -> proto.OutputMetric
	1,  // 29: proto.EvalTemplateCustomConfig.model_type:type_name -> proto.EvalModelType
	16, // 30: proto.Evaluation.model_kind:type_name -> proto.ModelKind
	17, // 31: proto.Evaluation.model_sub_kind:type_name -> proto.ModelSubKind
	14, // 32: proto.Evaluation.resource:type_name -> proto.DeployResource
	22, // 33: proto.Evaluation.selector:type_name -> proto.DeploySelector
	23, // 34: proto.Evaluation.result:type_name -> proto.EvaluationResult
	24, // 35: proto.Evaluation.status:type_name -> proto.Status
	25, // 36: proto.Evaluation.model_type:type_name -> proto.ModelType
	26, // 37: proto.Evaluation.model_sub_type:type_name -> proto.ModelSubType
	12, // 38: proto.Evaluation.metrics_desc:type_name -> proto.OutputMetric
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_proto_evaluation_proto_init() }
func file_proto_evaluation_proto_init() {
	if File_proto_evaluation_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_image_template_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_evaluation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalMission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalMissionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalMissionEnv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalModelConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalTemplateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvalTemplateCustomConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutputMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_evaluation_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Evaluation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_evaluation_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_evaluation_proto_goTypes,
		DependencyIndexes: file_proto_evaluation_proto_depIdxs,
		EnumInfos:         file_proto_evaluation_proto_enumTypes,
		MessageInfos:      file_proto_evaluation_proto_msgTypes,
	}.Build()
	File_proto_evaluation_proto = out.File
	file_proto_evaluation_proto_rawDesc = nil
	file_proto_evaluation_proto_goTypes = nil
	file_proto_evaluation_proto_depIdxs = nil
}
