// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_deploy_sys_mgr.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetModelDeploySysReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id  string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetModelDeploySysReq) Reset() {
	*x = GetModelDeploySysReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelDeploySysReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelDeploySysReq) ProtoMessage() {}

func (x *GetModelDeploySysReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelDeploySysReq.ProtoReflect.Descriptor instead.
func (*GetModelDeploySysReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{0}
}

func (x *GetModelDeploySysReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *GetModelDeploySysReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetModelDeploySysRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploySystems []*DeploySys `protobuf:"bytes,1,rep,name=deploy_systems,json=deploySystems,proto3" json:"deploy_systems,omitempty"`
}

func (x *GetModelDeploySysRsp) Reset() {
	*x = GetModelDeploySysRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelDeploySysRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelDeploySysRsp) ProtoMessage() {}

func (x *GetModelDeploySysRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelDeploySysRsp.ProtoReflect.Descriptor instead.
func (*GetModelDeploySysRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{1}
}

func (x *GetModelDeploySysRsp) GetDeploySystems() []*DeploySys {
	if x != nil {
		return x.DeploySystems
	}
	return nil
}

type RegisterModelDeploySysReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploySys *DeploySys `protobuf:"bytes,1,opt,name=deploy_sys,json=deploySys,proto3" json:"deploy_sys,omitempty"`
}

func (x *RegisterModelDeploySysReq) Reset() {
	*x = RegisterModelDeploySysReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterModelDeploySysReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterModelDeploySysReq) ProtoMessage() {}

func (x *RegisterModelDeploySysReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterModelDeploySysReq.ProtoReflect.Descriptor instead.
func (*RegisterModelDeploySysReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterModelDeploySysReq) GetDeploySys() *DeploySys {
	if x != nil {
		return x.DeploySys
	}
	return nil
}

type RegisterModelDeploySysRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RegisterModelDeploySysRsp) Reset() {
	*x = RegisterModelDeploySysRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterModelDeploySysRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterModelDeploySysRsp) ProtoMessage() {}

func (x *RegisterModelDeploySysRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterModelDeploySysRsp.ProtoReflect.Descriptor instead.
func (*RegisterModelDeploySysRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{3}
}

func (x *RegisterModelDeploySysRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UnregisterModelDeploySysReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UnregisterModelDeploySysReq) Reset() {
	*x = UnregisterModelDeploySysReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnregisterModelDeploySysReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnregisterModelDeploySysReq) ProtoMessage() {}

func (x *UnregisterModelDeploySysReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnregisterModelDeploySysReq.ProtoReflect.Descriptor instead.
func (*UnregisterModelDeploySysReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{4}
}

func (x *UnregisterModelDeploySysReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UnregisterModelDeploySysRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnregisterModelDeploySysRsp) Reset() {
	*x = UnregisterModelDeploySysRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnregisterModelDeploySysRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnregisterModelDeploySysRsp) ProtoMessage() {}

func (x *UnregisterModelDeploySysRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_deploy_sys_mgr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnregisterModelDeploySysRsp.ProtoReflect.Descriptor instead.
func (*UnregisterModelDeploySysRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP(), []int{5}
}

var File_proto_rpc_deploy_sys_mgr_proto protoreflect.FileDescriptor

var file_proto_rpc_deploy_sys_mgr_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x5f, 0x73, 0x79, 0x73, 0x5f, 0x6d, 0x67, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4c,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x53, 0x79, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4f, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x0d,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4c, 0x0a,
	0x19, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x73, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73,
	0x52, 0x09, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x22, 0x2b, 0x0a, 0x19, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2d, 0x0a, 0x1b, 0x55, 0x6e, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x53, 0x79, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x55, 0x6e, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x53, 0x79, 0x73, 0x52, 0x73, 0x70, 0x32, 0xa8, 0x02, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x12, 0x4d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x53, 0x79, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x5c, 0x0a, 0x16, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a,
	0x18, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x12, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x6e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x52, 0x73,
	0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69,
	0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_rpc_deploy_sys_mgr_proto_rawDescOnce sync.Once
	file_proto_rpc_deploy_sys_mgr_proto_rawDescData = file_proto_rpc_deploy_sys_mgr_proto_rawDesc
)

func file_proto_rpc_deploy_sys_mgr_proto_rawDescGZIP() []byte {
	file_proto_rpc_deploy_sys_mgr_proto_rawDescOnce.Do(func() {
		file_proto_rpc_deploy_sys_mgr_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_deploy_sys_mgr_proto_rawDescData)
	})
	return file_proto_rpc_deploy_sys_mgr_proto_rawDescData
}

var file_proto_rpc_deploy_sys_mgr_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_rpc_deploy_sys_mgr_proto_goTypes = []interface{}{
	(*GetModelDeploySysReq)(nil),        // 0: proto.GetModelDeploySysReq
	(*GetModelDeploySysRsp)(nil),        // 1: proto.GetModelDeploySysRsp
	(*RegisterModelDeploySysReq)(nil),   // 2: proto.RegisterModelDeploySysReq
	(*RegisterModelDeploySysRsp)(nil),   // 3: proto.RegisterModelDeploySysRsp
	(*UnregisterModelDeploySysReq)(nil), // 4: proto.UnregisterModelDeploySysReq
	(*UnregisterModelDeploySysRsp)(nil), // 5: proto.UnregisterModelDeploySysRsp
	(*UserContext)(nil),                 // 6: proto.UserContext
	(*DeploySys)(nil),                   // 7: proto.DeploySys
}
var file_proto_rpc_deploy_sys_mgr_proto_depIdxs = []int32{
	6, // 0: proto.GetModelDeploySysReq.ctx:type_name -> proto.UserContext
	7, // 1: proto.GetModelDeploySysRsp.deploy_systems:type_name -> proto.DeploySys
	7, // 2: proto.RegisterModelDeploySysReq.deploy_sys:type_name -> proto.DeploySys
	0, // 3: proto.ModelDeploySysManager.GetModelDeploySys:input_type -> proto.GetModelDeploySysReq
	2, // 4: proto.ModelDeploySysManager.RegisterModelDeploySys:input_type -> proto.RegisterModelDeploySysReq
	4, // 5: proto.ModelDeploySysManager.UnregisterModelDeploySys:input_type -> proto.UnregisterModelDeploySysReq
	1, // 6: proto.ModelDeploySysManager.GetModelDeploySys:output_type -> proto.GetModelDeploySysRsp
	3, // 7: proto.ModelDeploySysManager.RegisterModelDeploySys:output_type -> proto.RegisterModelDeploySysRsp
	5, // 8: proto.ModelDeploySysManager.UnregisterModelDeploySys:output_type -> proto.UnregisterModelDeploySysRsp
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proto_rpc_deploy_sys_mgr_proto_init() }
func file_proto_rpc_deploy_sys_mgr_proto_init() {
	if File_proto_rpc_deploy_sys_mgr_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelDeploySysReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelDeploySysRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterModelDeploySysReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterModelDeploySysRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnregisterModelDeploySysReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_deploy_sys_mgr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnregisterModelDeploySysRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_deploy_sys_mgr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_deploy_sys_mgr_proto_goTypes,
		DependencyIndexes: file_proto_rpc_deploy_sys_mgr_proto_depIdxs,
		MessageInfos:      file_proto_rpc_deploy_sys_mgr_proto_msgTypes,
	}.Build()
	File_proto_rpc_deploy_sys_mgr_proto = out.File
	file_proto_rpc_deploy_sys_mgr_proto_rawDesc = nil
	file_proto_rpc_deploy_sys_mgr_proto_goTypes = nil
	file_proto_rpc_deploy_sys_mgr_proto_depIdxs = nil
}
