// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/dialog.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DOWNLOAD_TYPE int32

const (
	DOWNLOAD_TYPE_DOWNLOAD_TYPE_ALL      DOWNLOAD_TYPE = 0
	DOWNLOAD_TYPE_DOWNLOAD_TYPE_ANSWER   DOWNLOAD_TYPE = 1
	DOWNLOAD_TYPE_DOWNLOAD_TYPE_QUESTION DOWNLOAD_TYPE = 2
)

// Enum value maps for DOWNLOAD_TYPE.
var (
	DOWNLOAD_TYPE_name = map[int32]string{
		0: "DOWNLOAD_TYPE_ALL",
		1: "DOWNLOAD_TYPE_ANSWER",
		2: "DOWNLOAD_TYPE_QUESTION",
	}
	DOWNLOAD_TYPE_value = map[string]int32{
		"DOWNLOAD_TYPE_ALL":      0,
		"DOWNLOAD_TYPE_ANSWER":   1,
		"DOWNLOAD_TYPE_QUESTION": 2,
	}
)

func (x DOWNLOAD_TYPE) Enum() *DOWNLOAD_TYPE {
	p := new(DOWNLOAD_TYPE)
	*p = x
	return p
}

func (x DOWNLOAD_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DOWNLOAD_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_dialog_proto_enumTypes[0].Descriptor()
}

func (DOWNLOAD_TYPE) Type() protoreflect.EnumType {
	return &file_proto_dialog_proto_enumTypes[0]
}

func (x DOWNLOAD_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DOWNLOAD_TYPE.Descriptor instead.
func (DOWNLOAD_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{0}
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Content    string `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	Role       string `protobuf:"bytes,4,opt,name=role,proto3" json:"role"`
	CreateTime int64  `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Message) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Message) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Message) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type MessageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatId  string   `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	Role    string   `protobuf:"bytes,2,opt,name=role,proto3" json:"role"`
	Content string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	PageReq *PageReq `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req"`
}

func (x *MessageReq) Reset() {
	*x = MessageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageReq) ProtoMessage() {}

func (x *MessageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageReq.ProtoReflect.Descriptor instead.
func (*MessageReq) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{1}
}

func (x *MessageReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *MessageReq) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *MessageReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MessageReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

type MessageRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size     int64      `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	ChatId   string     `protobuf:"bytes,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	Messages []*Message `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages"`
}

func (x *MessageRes) Reset() {
	*x = MessageRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageRes) ProtoMessage() {}

func (x *MessageRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageRes.ProtoReflect.Descriptor instead.
func (*MessageRes) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{2}
}

func (x *MessageRes) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *MessageRes) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *MessageRes) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

type Dialog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64          `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ChatId     string         `protobuf:"bytes,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	AppId      string         `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id"`
	ProjectId  string         `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	User       string         `protobuf:"bytes,5,opt,name=user,proto3" json:"user"`
	AppName    string         `protobuf:"bytes,6,opt,name=app_name,json=appName,proto3" json:"app_name"`
	CreateTime int64          `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime int64          `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Messages   *LatestMessage `protobuf:"bytes,9,opt,name=messages,proto3" json:"messages"`
}

func (x *Dialog) Reset() {
	*x = Dialog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dialog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dialog) ProtoMessage() {}

func (x *Dialog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dialog.ProtoReflect.Descriptor instead.
func (*Dialog) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{3}
}

func (x *Dialog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dialog) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *Dialog) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Dialog) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Dialog) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Dialog) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Dialog) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Dialog) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Dialog) GetMessages() *LatestMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

type LatestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Answer   *Message `protobuf:"bytes,1,opt,name=answer,proto3" json:"answer"`
	Question *Message `protobuf:"bytes,2,opt,name=question,proto3" json:"question"`
}

func (x *LatestMessage) Reset() {
	*x = LatestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestMessage) ProtoMessage() {}

func (x *LatestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestMessage.ProtoReflect.Descriptor instead.
func (*LatestMessage) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{4}
}

func (x *LatestMessage) GetAnswer() *Message {
	if x != nil {
		return x.Answer
	}
	return nil
}

func (x *LatestMessage) GetQuestion() *Message {
	if x != nil {
		return x.Question
	}
	return nil
}

type DialogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id"`
	Content   string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	ChatId    string   `protobuf:"bytes,3,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	PageReq   *PageReq `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req"`
	StartTime int64    `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime   int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
}

func (x *DialogReq) Reset() {
	*x = DialogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DialogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogReq) ProtoMessage() {}

func (x *DialogReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogReq.ProtoReflect.Descriptor instead.
func (*DialogReq) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{5}
}

func (x *DialogReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DialogReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DialogReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *DialogReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *DialogReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DialogReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type DialogRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size    int64     `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	Dialogs []*Dialog `protobuf:"bytes,2,rep,name=dialogs,proto3" json:"dialogs"`
	 
	Total int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total" description:"获取到同一个应用链下的数据总量"`
}

func (x *DialogRes) Reset() {
	*x = DialogRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DialogRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogRes) ProtoMessage() {}

func (x *DialogRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogRes.ProtoReflect.Descriptor instead.
func (*DialogRes) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{6}
}

func (x *DialogRes) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DialogRes) GetDialogs() []*Dialog {
	if x != nil {
		return x.Dialogs
	}
	return nil
}

func (x *DialogRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DialogApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	AppId      string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id"`
	AppName    string `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name"`
	AppImage   string `protobuf:"bytes,4,opt,name=app_image,json=appImage,proto3" json:"app_image"`
	CreateTime int64  `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime int64  `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *DialogApp) Reset() {
	*x = DialogApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DialogApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogApp) ProtoMessage() {}

func (x *DialogApp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogApp.ProtoReflect.Descriptor instead.
func (*DialogApp) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{7}
}

func (x *DialogApp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DialogApp) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DialogApp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DialogApp) GetAppImage() string {
	if x != nil {
		return x.AppImage
	}
	return ""
}

func (x *DialogApp) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DialogApp) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type DialogAppRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size      int64        `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	ProjectId string       `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	User      string       `protobuf:"bytes,3,opt,name=user,proto3" json:"user"`
	Apps      []*DialogApp `protobuf:"bytes,4,rep,name=apps,proto3" json:"apps"`
}

func (x *DialogAppRes) Reset() {
	*x = DialogAppRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DialogAppRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogAppRes) ProtoMessage() {}

func (x *DialogAppRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogAppRes.ProtoReflect.Descriptor instead.
func (*DialogAppRes) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{8}
}

func (x *DialogAppRes) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DialogAppRes) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DialogAppRes) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *DialogAppRes) GetApps() []*DialogApp {
	if x != nil {
		return x.Apps
	}
	return nil
}

type DownloadDialogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ChatIds []string `protobuf:"bytes,1,rep,name=chat_ids,json=chatIds,proto3" json:"chat_ids" description:"对话的id数组"`
	 
	Type DOWNLOAD_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=proto.DOWNLOAD_TYPE" json:"type" description:"下载的类型(0:所有，1:只下载answer，2:只下载question)"`
}

func (x *DownloadDialogReq) Reset() {
	*x = DownloadDialogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadDialogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadDialogReq) ProtoMessage() {}

func (x *DownloadDialogReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadDialogReq.ProtoReflect.Descriptor instead.
func (*DownloadDialogReq) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{9}
}

func (x *DownloadDialogReq) GetChatIds() []string {
	if x != nil {
		return x.ChatIds
	}
	return nil
}

func (x *DownloadDialogReq) GetType() DOWNLOAD_TYPE {
	if x != nil {
		return x.Type
	}
	return DOWNLOAD_TYPE_DOWNLOAD_TYPE_ALL
}

type DownloadDialogRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data" description:"消息字节数据"`
	 
	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName" description:"记录文件名称"`
}

func (x *DownloadDialogRes) Reset() {
	*x = DownloadDialogRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dialog_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadDialogRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadDialogRes) ProtoMessage() {}

func (x *DownloadDialogRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dialog_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadDialogRes.ProtoReflect.Descriptor instead.
func (*DownloadDialogRes) Descriptor() ([]byte, []int) {
	return file_proto_dialog_proto_rawDescGZIP(), []int{10}
}

func (x *DownloadDialogRes) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DownloadDialogRes) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

var File_proto_dialog_proto protoreflect.FileDescriptor

var file_proto_dialog_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x68, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7e, 0x0a, 0x0a, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x29,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x22, 0x65, 0x0a, 0x0a, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x22, 0x8a, 0x02, 0x0a, 0x06, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x08, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0x63, 0x0a,
	0x0d, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26,
	0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xba, 0x01, 0x0a, 0x09, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x5e, 0x0a, 0x09, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x27, 0x0a, 0x07, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67,
	0x52, 0x07, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0xac, 0x01, 0x0a, 0x09, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7b,
	0x0a, 0x0c, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x61, 0x6c,
	0x6f, 0x67, 0x41, 0x70, 0x70, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x22, 0x58, 0x0a, 0x11, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x43, 0x0a, 0x11, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0x5c, 0x0a, 0x0d, 0x44, 0x4f,
	0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x15, 0x0a, 0x11, 0x44,
	0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c,
	0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x4e, 0x53, 0x57, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d,
	0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_dialog_proto_rawDescOnce sync.Once
	file_proto_dialog_proto_rawDescData = file_proto_dialog_proto_rawDesc
)

func file_proto_dialog_proto_rawDescGZIP() []byte {
	file_proto_dialog_proto_rawDescOnce.Do(func() {
		file_proto_dialog_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_dialog_proto_rawDescData)
	})
	return file_proto_dialog_proto_rawDescData
}

var file_proto_dialog_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_dialog_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_proto_dialog_proto_goTypes = []interface{}{
	(DOWNLOAD_TYPE)(0),        // 0: proto.DOWNLOAD_TYPE
	(*Message)(nil),           // 1: proto.Message
	(*MessageReq)(nil),        // 2: proto.MessageReq
	(*MessageRes)(nil),        // 3: proto.MessageRes
	(*Dialog)(nil),            // 4: proto.Dialog
	(*LatestMessage)(nil),     // 5: proto.LatestMessage
	(*DialogReq)(nil),         // 6: proto.DialogReq
	(*DialogRes)(nil),         // 7: proto.DialogRes
	(*DialogApp)(nil),         // 8: proto.DialogApp
	(*DialogAppRes)(nil),      // 9: proto.DialogAppRes
	(*DownloadDialogReq)(nil), // 10: proto.DownloadDialogReq
	(*DownloadDialogRes)(nil), // 11: proto.DownloadDialogRes
	(*PageReq)(nil),           // 12: proto.PageReq
}
var file_proto_dialog_proto_depIdxs = []int32{
	12, // 0: proto.MessageReq.page_req:type_name -> proto.PageReq
	1,  // 1: proto.MessageRes.messages:type_name -> proto.Message
	5,  // 2: proto.Dialog.messages:type_name -> proto.LatestMessage
	1,  // 3: proto.LatestMessage.answer:type_name -> proto.Message
	1,  // 4: proto.LatestMessage.question:type_name -> proto.Message
	12, // 5: proto.DialogReq.page_req:type_name -> proto.PageReq
	4,  // 6: proto.DialogRes.dialogs:type_name -> proto.Dialog
	8,  // 7: proto.DialogAppRes.apps:type_name -> proto.DialogApp
	0,  // 8: proto.DownloadDialogReq.type:type_name -> proto.DOWNLOAD_TYPE
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_proto_dialog_proto_init() }
func file_proto_dialog_proto_init() {
	if File_proto_dialog_proto != nil {
		return
	}
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_dialog_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dialog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DialogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DialogRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DialogApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DialogAppRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadDialogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dialog_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadDialogRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_dialog_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_dialog_proto_goTypes,
		DependencyIndexes: file_proto_dialog_proto_depIdxs,
		EnumInfos:         file_proto_dialog_proto_enumTypes,
		MessageInfos:      file_proto_dialog_proto_msgTypes,
	}.Build()
	File_proto_dialog_proto = out.File
	file_proto_dialog_proto_rawDesc = nil
	file_proto_dialog_proto_goTypes = nil
	file_proto_dialog_proto_depIdxs = nil
}
