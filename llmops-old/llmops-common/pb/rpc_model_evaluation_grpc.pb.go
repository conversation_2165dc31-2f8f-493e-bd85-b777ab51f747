// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_model_evaluation.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelEvaluationManager_CreateModelEvaluation_FullMethodName  = "/proto.ModelEvaluationManager/CreateModelEvaluation"
	ModelEvaluationManager_ReadModelEvaluations_FullMethodName   = "/proto.ModelEvaluationManager/ReadModelEvaluations"
	ModelEvaluationManager_ExportModelEvaluations_FullMethodName = "/proto.ModelEvaluationManager/ExportModelEvaluations"
	ModelEvaluationManager_CheckDataProcessStatus_FullMethodName = "/proto.ModelEvaluationManager/CheckDataProcessStatus"
	ModelEvaluationManager_UpdateModelEvaluation_FullMethodName  = "/proto.ModelEvaluationManager/UpdateModelEvaluation"
	ModelEvaluationManager_DeleteModelEvaluations_FullMethodName = "/proto.ModelEvaluationManager/DeleteModelEvaluations"
	ModelEvaluationManager_StartModelEvaluation_FullMethodName   = "/proto.ModelEvaluationManager/StartModelEvaluation"
	ModelEvaluationManager_StopModelEvaluation_FullMethodName    = "/proto.ModelEvaluationManager/StopModelEvaluation"
)

// ModelEvaluationManagerClient is the client API for ModelEvaluationManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelEvaluationManagerClient interface {
	CreateModelEvaluation(ctx context.Context, in *CreateModelEvaluationReq, opts ...grpc.CallOption) (*CreateModelEvaluationRsp, error)
	ReadModelEvaluations(ctx context.Context, in *ReadModelEvaluationReq, opts ...grpc.CallOption) (*ReadModelEvaluationRsp, error)
	// 将多个版本的模型评估结果导出为csv文件
	ExportModelEvaluations(ctx context.Context, in *ExportModelEvaluationsReq, opts ...grpc.CallOption) (*ExportModelEvaluationsRsp, error)
	// 仅结构化数据模型，查看csv表数据处理状态
	CheckDataProcessStatus(ctx context.Context, in *CheckDataProcessStatusReq, opts ...grpc.CallOption) (*CheckDataProcessStatusRsp, error)
	UpdateModelEvaluation(ctx context.Context, in *UpdateModelEvaluationReq, opts ...grpc.CallOption) (*UpdateModelEvaluationRsp, error)
	DeleteModelEvaluations(ctx context.Context, in *DeleteModelEvaluationReq, opts ...grpc.CallOption) (*DeleteModelEvaluationRsp, error)
	StartModelEvaluation(ctx context.Context, in *StartModelEvaluationsReq, opts ...grpc.CallOption) (*StartModelEvaluationsRsp, error)
	StopModelEvaluation(ctx context.Context, in *StopModelEvaluationsReq, opts ...grpc.CallOption) (*StopModelEvaluationsRsp, error)
}

type modelEvaluationManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewModelEvaluationManagerClient(cc grpc.ClientConnInterface) ModelEvaluationManagerClient {
	return &modelEvaluationManagerClient{cc}
}

func (c *modelEvaluationManagerClient) CreateModelEvaluation(ctx context.Context, in *CreateModelEvaluationReq, opts ...grpc.CallOption) (*CreateModelEvaluationRsp, error) {
	out := new(CreateModelEvaluationRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_CreateModelEvaluation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) ReadModelEvaluations(ctx context.Context, in *ReadModelEvaluationReq, opts ...grpc.CallOption) (*ReadModelEvaluationRsp, error) {
	out := new(ReadModelEvaluationRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_ReadModelEvaluations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) ExportModelEvaluations(ctx context.Context, in *ExportModelEvaluationsReq, opts ...grpc.CallOption) (*ExportModelEvaluationsRsp, error) {
	out := new(ExportModelEvaluationsRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_ExportModelEvaluations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) CheckDataProcessStatus(ctx context.Context, in *CheckDataProcessStatusReq, opts ...grpc.CallOption) (*CheckDataProcessStatusRsp, error) {
	out := new(CheckDataProcessStatusRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_CheckDataProcessStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) UpdateModelEvaluation(ctx context.Context, in *UpdateModelEvaluationReq, opts ...grpc.CallOption) (*UpdateModelEvaluationRsp, error) {
	out := new(UpdateModelEvaluationRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_UpdateModelEvaluation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) DeleteModelEvaluations(ctx context.Context, in *DeleteModelEvaluationReq, opts ...grpc.CallOption) (*DeleteModelEvaluationRsp, error) {
	out := new(DeleteModelEvaluationRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_DeleteModelEvaluations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) StartModelEvaluation(ctx context.Context, in *StartModelEvaluationsReq, opts ...grpc.CallOption) (*StartModelEvaluationsRsp, error) {
	out := new(StartModelEvaluationsRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_StartModelEvaluation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelEvaluationManagerClient) StopModelEvaluation(ctx context.Context, in *StopModelEvaluationsReq, opts ...grpc.CallOption) (*StopModelEvaluationsRsp, error) {
	out := new(StopModelEvaluationsRsp)
	err := c.cc.Invoke(ctx, ModelEvaluationManager_StopModelEvaluation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelEvaluationManagerServer is the server API for ModelEvaluationManager service.
// All implementations must embed UnimplementedModelEvaluationManagerServer
// for forward compatibility
type ModelEvaluationManagerServer interface {
	CreateModelEvaluation(context.Context, *CreateModelEvaluationReq) (*CreateModelEvaluationRsp, error)
	ReadModelEvaluations(context.Context, *ReadModelEvaluationReq) (*ReadModelEvaluationRsp, error)
	// 将多个版本的模型评估结果导出为csv文件
	ExportModelEvaluations(context.Context, *ExportModelEvaluationsReq) (*ExportModelEvaluationsRsp, error)
	// 仅结构化数据模型，查看csv表数据处理状态
	CheckDataProcessStatus(context.Context, *CheckDataProcessStatusReq) (*CheckDataProcessStatusRsp, error)
	UpdateModelEvaluation(context.Context, *UpdateModelEvaluationReq) (*UpdateModelEvaluationRsp, error)
	DeleteModelEvaluations(context.Context, *DeleteModelEvaluationReq) (*DeleteModelEvaluationRsp, error)
	StartModelEvaluation(context.Context, *StartModelEvaluationsReq) (*StartModelEvaluationsRsp, error)
	StopModelEvaluation(context.Context, *StopModelEvaluationsReq) (*StopModelEvaluationsRsp, error)
	mustEmbedUnimplementedModelEvaluationManagerServer()
}

// UnimplementedModelEvaluationManagerServer must be embedded to have forward compatible implementations.
type UnimplementedModelEvaluationManagerServer struct {
}

func (UnimplementedModelEvaluationManagerServer) CreateModelEvaluation(context.Context, *CreateModelEvaluationReq) (*CreateModelEvaluationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModelEvaluation not implemented")
}
func (UnimplementedModelEvaluationManagerServer) ReadModelEvaluations(context.Context, *ReadModelEvaluationReq) (*ReadModelEvaluationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadModelEvaluations not implemented")
}
func (UnimplementedModelEvaluationManagerServer) ExportModelEvaluations(context.Context, *ExportModelEvaluationsReq) (*ExportModelEvaluationsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportModelEvaluations not implemented")
}
func (UnimplementedModelEvaluationManagerServer) CheckDataProcessStatus(context.Context, *CheckDataProcessStatusReq) (*CheckDataProcessStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDataProcessStatus not implemented")
}
func (UnimplementedModelEvaluationManagerServer) UpdateModelEvaluation(context.Context, *UpdateModelEvaluationReq) (*UpdateModelEvaluationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateModelEvaluation not implemented")
}
func (UnimplementedModelEvaluationManagerServer) DeleteModelEvaluations(context.Context, *DeleteModelEvaluationReq) (*DeleteModelEvaluationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteModelEvaluations not implemented")
}
func (UnimplementedModelEvaluationManagerServer) StartModelEvaluation(context.Context, *StartModelEvaluationsReq) (*StartModelEvaluationsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartModelEvaluation not implemented")
}
func (UnimplementedModelEvaluationManagerServer) StopModelEvaluation(context.Context, *StopModelEvaluationsReq) (*StopModelEvaluationsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopModelEvaluation not implemented")
}
func (UnimplementedModelEvaluationManagerServer) mustEmbedUnimplementedModelEvaluationManagerServer() {
}

// UnsafeModelEvaluationManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelEvaluationManagerServer will
// result in compilation errors.
type UnsafeModelEvaluationManagerServer interface {
	mustEmbedUnimplementedModelEvaluationManagerServer()
}

func RegisterModelEvaluationManagerServer(s grpc.ServiceRegistrar, srv ModelEvaluationManagerServer) {
	s.RegisterService(&ModelEvaluationManager_ServiceDesc, srv)
}

func _ModelEvaluationManager_CreateModelEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelEvaluationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).CreateModelEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_CreateModelEvaluation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).CreateModelEvaluation(ctx, req.(*CreateModelEvaluationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_ReadModelEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadModelEvaluationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).ReadModelEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_ReadModelEvaluations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).ReadModelEvaluations(ctx, req.(*ReadModelEvaluationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_ExportModelEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportModelEvaluationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).ExportModelEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_ExportModelEvaluations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).ExportModelEvaluations(ctx, req.(*ExportModelEvaluationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_CheckDataProcessStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDataProcessStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).CheckDataProcessStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_CheckDataProcessStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).CheckDataProcessStatus(ctx, req.(*CheckDataProcessStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_UpdateModelEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateModelEvaluationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).UpdateModelEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_UpdateModelEvaluation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).UpdateModelEvaluation(ctx, req.(*UpdateModelEvaluationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_DeleteModelEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteModelEvaluationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).DeleteModelEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_DeleteModelEvaluations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).DeleteModelEvaluations(ctx, req.(*DeleteModelEvaluationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_StartModelEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartModelEvaluationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).StartModelEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_StartModelEvaluation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).StartModelEvaluation(ctx, req.(*StartModelEvaluationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelEvaluationManager_StopModelEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopModelEvaluationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelEvaluationManagerServer).StopModelEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelEvaluationManager_StopModelEvaluation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelEvaluationManagerServer).StopModelEvaluation(ctx, req.(*StopModelEvaluationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelEvaluationManager_ServiceDesc is the grpc.ServiceDesc for ModelEvaluationManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelEvaluationManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ModelEvaluationManager",
	HandlerType: (*ModelEvaluationManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateModelEvaluation",
			Handler:    _ModelEvaluationManager_CreateModelEvaluation_Handler,
		},
		{
			MethodName: "ReadModelEvaluations",
			Handler:    _ModelEvaluationManager_ReadModelEvaluations_Handler,
		},
		{
			MethodName: "ExportModelEvaluations",
			Handler:    _ModelEvaluationManager_ExportModelEvaluations_Handler,
		},
		{
			MethodName: "CheckDataProcessStatus",
			Handler:    _ModelEvaluationManager_CheckDataProcessStatus_Handler,
		},
		{
			MethodName: "UpdateModelEvaluation",
			Handler:    _ModelEvaluationManager_UpdateModelEvaluation_Handler,
		},
		{
			MethodName: "DeleteModelEvaluations",
			Handler:    _ModelEvaluationManager_DeleteModelEvaluations_Handler,
		},
		{
			MethodName: "StartModelEvaluation",
			Handler:    _ModelEvaluationManager_StartModelEvaluation_Handler,
		},
		{
			MethodName: "StopModelEvaluation",
			Handler:    _ModelEvaluationManager_StopModelEvaluation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model_evaluation.proto",
}

const (
	EvaluationScriptManager_CreateEvaluationScript_FullMethodName         = "/proto.EvaluationScriptManager/CreateEvaluationScript"
	EvaluationScriptManager_ReadEvaluationScript_FullMethodName           = "/proto.EvaluationScriptManager/ReadEvaluationScript"
	EvaluationScriptManager_UpdateEvaluationScript_FullMethodName         = "/proto.EvaluationScriptManager/UpdateEvaluationScript"
	EvaluationScriptManager_DeleteEvaluationScript_FullMethodName         = "/proto.EvaluationScriptManager/DeleteEvaluationScript"
	EvaluationScriptManager_UploadEvaluationScript_FullMethodName         = "/proto.EvaluationScriptManager/UploadEvaluationScript"
	EvaluationScriptManager_DownloadEvaluationScript_FullMethodName       = "/proto.EvaluationScriptManager/DownloadEvaluationScript"
	EvaluationScriptManager_CheckEvaluationScriptExistence_FullMethodName = "/proto.EvaluationScriptManager/CheckEvaluationScriptExistence"
)

// EvaluationScriptManagerClient is the client API for EvaluationScriptManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationScriptManagerClient interface {
	CreateEvaluationScript(ctx context.Context, in *CreateEvaluationScriptReq, opts ...grpc.CallOption) (*CreateEvaluationScriptRsp, error)
	ReadEvaluationScript(ctx context.Context, in *ReadEvaluationScriptReq, opts ...grpc.CallOption) (*ReadEvaluationScriptRsp, error)
	UpdateEvaluationScript(ctx context.Context, in *UpdateEvaluationScriptReq, opts ...grpc.CallOption) (*UpdateEvaluationScriptRsp, error)
	DeleteEvaluationScript(ctx context.Context, in *DeleteEvaluationScriptReq, opts ...grpc.CallOption) (*DeleteEvaluationScriptRsp, error)
	// 上传文件使用统一的文件上传组件，这里上传文件直接传递文件上传组件返回的地址
	UploadEvaluationScript(ctx context.Context, in *UploadEvaluationScriptReq, opts ...grpc.CallOption) (*UploadEvaluationScriptRsp, error)
	DownloadEvaluationScript(ctx context.Context, in *DownloadEvaluationScriptReq, opts ...grpc.CallOption) (*DownloadEvaluationScriptRsp, error)
	CheckEvaluationScriptExistence(ctx context.Context, in *CheckEvaluationScriptExistenceReq, opts ...grpc.CallOption) (*CheckEvaluationScriptExistenceRsp, error)
}

type evaluationScriptManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationScriptManagerClient(cc grpc.ClientConnInterface) EvaluationScriptManagerClient {
	return &evaluationScriptManagerClient{cc}
}

func (c *evaluationScriptManagerClient) CreateEvaluationScript(ctx context.Context, in *CreateEvaluationScriptReq, opts ...grpc.CallOption) (*CreateEvaluationScriptRsp, error) {
	out := new(CreateEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_CreateEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) ReadEvaluationScript(ctx context.Context, in *ReadEvaluationScriptReq, opts ...grpc.CallOption) (*ReadEvaluationScriptRsp, error) {
	out := new(ReadEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_ReadEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) UpdateEvaluationScript(ctx context.Context, in *UpdateEvaluationScriptReq, opts ...grpc.CallOption) (*UpdateEvaluationScriptRsp, error) {
	out := new(UpdateEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_UpdateEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) DeleteEvaluationScript(ctx context.Context, in *DeleteEvaluationScriptReq, opts ...grpc.CallOption) (*DeleteEvaluationScriptRsp, error) {
	out := new(DeleteEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_DeleteEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) UploadEvaluationScript(ctx context.Context, in *UploadEvaluationScriptReq, opts ...grpc.CallOption) (*UploadEvaluationScriptRsp, error) {
	out := new(UploadEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_UploadEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) DownloadEvaluationScript(ctx context.Context, in *DownloadEvaluationScriptReq, opts ...grpc.CallOption) (*DownloadEvaluationScriptRsp, error) {
	out := new(DownloadEvaluationScriptRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_DownloadEvaluationScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptManagerClient) CheckEvaluationScriptExistence(ctx context.Context, in *CheckEvaluationScriptExistenceReq, opts ...grpc.CallOption) (*CheckEvaluationScriptExistenceRsp, error) {
	out := new(CheckEvaluationScriptExistenceRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptManager_CheckEvaluationScriptExistence_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvaluationScriptManagerServer is the server API for EvaluationScriptManager service.
// All implementations must embed UnimplementedEvaluationScriptManagerServer
// for forward compatibility
type EvaluationScriptManagerServer interface {
	CreateEvaluationScript(context.Context, *CreateEvaluationScriptReq) (*CreateEvaluationScriptRsp, error)
	ReadEvaluationScript(context.Context, *ReadEvaluationScriptReq) (*ReadEvaluationScriptRsp, error)
	UpdateEvaluationScript(context.Context, *UpdateEvaluationScriptReq) (*UpdateEvaluationScriptRsp, error)
	DeleteEvaluationScript(context.Context, *DeleteEvaluationScriptReq) (*DeleteEvaluationScriptRsp, error)
	// 上传文件使用统一的文件上传组件，这里上传文件直接传递文件上传组件返回的地址
	UploadEvaluationScript(context.Context, *UploadEvaluationScriptReq) (*UploadEvaluationScriptRsp, error)
	DownloadEvaluationScript(context.Context, *DownloadEvaluationScriptReq) (*DownloadEvaluationScriptRsp, error)
	CheckEvaluationScriptExistence(context.Context, *CheckEvaluationScriptExistenceReq) (*CheckEvaluationScriptExistenceRsp, error)
	mustEmbedUnimplementedEvaluationScriptManagerServer()
}

// UnimplementedEvaluationScriptManagerServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationScriptManagerServer struct {
}

func (UnimplementedEvaluationScriptManagerServer) CreateEvaluationScript(context.Context, *CreateEvaluationScriptReq) (*CreateEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) ReadEvaluationScript(context.Context, *ReadEvaluationScriptReq) (*ReadEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) UpdateEvaluationScript(context.Context, *UpdateEvaluationScriptReq) (*UpdateEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) DeleteEvaluationScript(context.Context, *DeleteEvaluationScriptReq) (*DeleteEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) UploadEvaluationScript(context.Context, *UploadEvaluationScriptReq) (*UploadEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) DownloadEvaluationScript(context.Context, *DownloadEvaluationScriptReq) (*DownloadEvaluationScriptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadEvaluationScript not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) CheckEvaluationScriptExistence(context.Context, *CheckEvaluationScriptExistenceReq) (*CheckEvaluationScriptExistenceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEvaluationScriptExistence not implemented")
}
func (UnimplementedEvaluationScriptManagerServer) mustEmbedUnimplementedEvaluationScriptManagerServer() {
}

// UnsafeEvaluationScriptManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationScriptManagerServer will
// result in compilation errors.
type UnsafeEvaluationScriptManagerServer interface {
	mustEmbedUnimplementedEvaluationScriptManagerServer()
}

func RegisterEvaluationScriptManagerServer(s grpc.ServiceRegistrar, srv EvaluationScriptManagerServer) {
	s.RegisterService(&EvaluationScriptManager_ServiceDesc, srv)
}

func _EvaluationScriptManager_CreateEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).CreateEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_CreateEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).CreateEvaluationScript(ctx, req.(*CreateEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_ReadEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).ReadEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_ReadEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).ReadEvaluationScript(ctx, req.(*ReadEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_UpdateEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).UpdateEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_UpdateEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).UpdateEvaluationScript(ctx, req.(*UpdateEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_DeleteEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).DeleteEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_DeleteEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).DeleteEvaluationScript(ctx, req.(*DeleteEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_UploadEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).UploadEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_UploadEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).UploadEvaluationScript(ctx, req.(*UploadEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_DownloadEvaluationScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadEvaluationScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).DownloadEvaluationScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_DownloadEvaluationScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).DownloadEvaluationScript(ctx, req.(*DownloadEvaluationScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptManager_CheckEvaluationScriptExistence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEvaluationScriptExistenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptManagerServer).CheckEvaluationScriptExistence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptManager_CheckEvaluationScriptExistence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptManagerServer).CheckEvaluationScriptExistence(ctx, req.(*CheckEvaluationScriptExistenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// EvaluationScriptManager_ServiceDesc is the grpc.ServiceDesc for EvaluationScriptManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationScriptManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.EvaluationScriptManager",
	HandlerType: (*EvaluationScriptManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEvaluationScript",
			Handler:    _EvaluationScriptManager_CreateEvaluationScript_Handler,
		},
		{
			MethodName: "ReadEvaluationScript",
			Handler:    _EvaluationScriptManager_ReadEvaluationScript_Handler,
		},
		{
			MethodName: "UpdateEvaluationScript",
			Handler:    _EvaluationScriptManager_UpdateEvaluationScript_Handler,
		},
		{
			MethodName: "DeleteEvaluationScript",
			Handler:    _EvaluationScriptManager_DeleteEvaluationScript_Handler,
		},
		{
			MethodName: "UploadEvaluationScript",
			Handler:    _EvaluationScriptManager_UploadEvaluationScript_Handler,
		},
		{
			MethodName: "DownloadEvaluationScript",
			Handler:    _EvaluationScriptManager_DownloadEvaluationScript_Handler,
		},
		{
			MethodName: "CheckEvaluationScriptExistence",
			Handler:    _EvaluationScriptManager_CheckEvaluationScriptExistence_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model_evaluation.proto",
}

const (
	EvaluationDatasetManager_CreateEvaluationDataset_FullMethodName         = "/proto.EvaluationDatasetManager/CreateEvaluationDataset"
	EvaluationDatasetManager_CheckEvaluationDatasetExistence_FullMethodName = "/proto.EvaluationDatasetManager/CheckEvaluationDatasetExistence"
	EvaluationDatasetManager_GetCsvHeaders_FullMethodName                   = "/proto.EvaluationDatasetManager/GetCsvHeaders"
	EvaluationDatasetManager_ValidateEvaluationDataset_FullMethodName       = "/proto.EvaluationDatasetManager/ValidateEvaluationDataset"
	EvaluationDatasetManager_PreviewEvaluationDataset_FullMethodName        = "/proto.EvaluationDatasetManager/PreviewEvaluationDataset"
	EvaluationDatasetManager_ReadEvaluationDataset_FullMethodName           = "/proto.EvaluationDatasetManager/ReadEvaluationDataset"
	EvaluationDatasetManager_UpdateEvaluationDataset_FullMethodName         = "/proto.EvaluationDatasetManager/UpdateEvaluationDataset"
	EvaluationDatasetManager_DeleteEvaluationDataset_FullMethodName         = "/proto.EvaluationDatasetManager/DeleteEvaluationDataset"
	EvaluationDatasetManager_UploadEvaluationDataset_FullMethodName         = "/proto.EvaluationDatasetManager/UploadEvaluationDataset"
	EvaluationDatasetManager_DownloadEvaluationDataset_FullMethodName       = "/proto.EvaluationDatasetManager/DownloadEvaluationDataset"
)

// EvaluationDatasetManagerClient is the client API for EvaluationDatasetManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationDatasetManagerClient interface {
	CreateEvaluationDataset(ctx context.Context, in *CreateEvaluationDatasetReq, opts ...grpc.CallOption) (*CreateEvaluationDatasetRsp, error)
	CheckEvaluationDatasetExistence(ctx context.Context, in *CheckEvaluationDatasetExistenceReq, opts ...grpc.CallOption) (*CheckEvaluationDatasetExistenceRsp, error)
	// 仅结构化数据，获取csv表格表头用于数据校验
	GetCsvHeaders(ctx context.Context, in *GetCsvHeadersReq, opts ...grpc.CallOption) (*GetCsvHeadersRsp, error)
	ValidateEvaluationDataset(ctx context.Context, in *ValidateEvaluationDatasetReq, opts ...grpc.CallOption) (*ValidateEvaluationDatasetRsp, error)
	PreviewEvaluationDataset(ctx context.Context, in *PreviewEvaluationDatasetReq, opts ...grpc.CallOption) (*PreviewEvaluationDatasetRsp, error)
	ReadEvaluationDataset(ctx context.Context, in *ReadEvaluationDatasetReq, opts ...grpc.CallOption) (*ReadEvaluationDatasetRsp, error)
	UpdateEvaluationDataset(ctx context.Context, in *UpdateEvaluationDatasetReq, opts ...grpc.CallOption) (*UpdateEvaluationDatasetRsp, error)
	DeleteEvaluationDataset(ctx context.Context, in *DeleteEvaluationDatasetReq, opts ...grpc.CallOption) (*DeleteEvaluationDatasetRsp, error)
	UploadEvaluationDataset(ctx context.Context, in *UploadEvaluationDatasetReq, opts ...grpc.CallOption) (*UploadEvaluationDatasetRsp, error)
	DownloadEvaluationDataset(ctx context.Context, in *DownloadEvaluationDatasetReq, opts ...grpc.CallOption) (*DownloadEvaluationDatasetRsp, error)
}

type evaluationDatasetManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationDatasetManagerClient(cc grpc.ClientConnInterface) EvaluationDatasetManagerClient {
	return &evaluationDatasetManagerClient{cc}
}

func (c *evaluationDatasetManagerClient) CreateEvaluationDataset(ctx context.Context, in *CreateEvaluationDatasetReq, opts ...grpc.CallOption) (*CreateEvaluationDatasetRsp, error) {
	out := new(CreateEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_CreateEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) CheckEvaluationDatasetExistence(ctx context.Context, in *CheckEvaluationDatasetExistenceReq, opts ...grpc.CallOption) (*CheckEvaluationDatasetExistenceRsp, error) {
	out := new(CheckEvaluationDatasetExistenceRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_CheckEvaluationDatasetExistence_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) GetCsvHeaders(ctx context.Context, in *GetCsvHeadersReq, opts ...grpc.CallOption) (*GetCsvHeadersRsp, error) {
	out := new(GetCsvHeadersRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_GetCsvHeaders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) ValidateEvaluationDataset(ctx context.Context, in *ValidateEvaluationDatasetReq, opts ...grpc.CallOption) (*ValidateEvaluationDatasetRsp, error) {
	out := new(ValidateEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_ValidateEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) PreviewEvaluationDataset(ctx context.Context, in *PreviewEvaluationDatasetReq, opts ...grpc.CallOption) (*PreviewEvaluationDatasetRsp, error) {
	out := new(PreviewEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_PreviewEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) ReadEvaluationDataset(ctx context.Context, in *ReadEvaluationDatasetReq, opts ...grpc.CallOption) (*ReadEvaluationDatasetRsp, error) {
	out := new(ReadEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_ReadEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) UpdateEvaluationDataset(ctx context.Context, in *UpdateEvaluationDatasetReq, opts ...grpc.CallOption) (*UpdateEvaluationDatasetRsp, error) {
	out := new(UpdateEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_UpdateEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) DeleteEvaluationDataset(ctx context.Context, in *DeleteEvaluationDatasetReq, opts ...grpc.CallOption) (*DeleteEvaluationDatasetRsp, error) {
	out := new(DeleteEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_DeleteEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) UploadEvaluationDataset(ctx context.Context, in *UploadEvaluationDatasetReq, opts ...grpc.CallOption) (*UploadEvaluationDatasetRsp, error) {
	out := new(UploadEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_UploadEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationDatasetManagerClient) DownloadEvaluationDataset(ctx context.Context, in *DownloadEvaluationDatasetReq, opts ...grpc.CallOption) (*DownloadEvaluationDatasetRsp, error) {
	out := new(DownloadEvaluationDatasetRsp)
	err := c.cc.Invoke(ctx, EvaluationDatasetManager_DownloadEvaluationDataset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvaluationDatasetManagerServer is the server API for EvaluationDatasetManager service.
// All implementations must embed UnimplementedEvaluationDatasetManagerServer
// for forward compatibility
type EvaluationDatasetManagerServer interface {
	CreateEvaluationDataset(context.Context, *CreateEvaluationDatasetReq) (*CreateEvaluationDatasetRsp, error)
	CheckEvaluationDatasetExistence(context.Context, *CheckEvaluationDatasetExistenceReq) (*CheckEvaluationDatasetExistenceRsp, error)
	// 仅结构化数据，获取csv表格表头用于数据校验
	GetCsvHeaders(context.Context, *GetCsvHeadersReq) (*GetCsvHeadersRsp, error)
	ValidateEvaluationDataset(context.Context, *ValidateEvaluationDatasetReq) (*ValidateEvaluationDatasetRsp, error)
	PreviewEvaluationDataset(context.Context, *PreviewEvaluationDatasetReq) (*PreviewEvaluationDatasetRsp, error)
	ReadEvaluationDataset(context.Context, *ReadEvaluationDatasetReq) (*ReadEvaluationDatasetRsp, error)
	UpdateEvaluationDataset(context.Context, *UpdateEvaluationDatasetReq) (*UpdateEvaluationDatasetRsp, error)
	DeleteEvaluationDataset(context.Context, *DeleteEvaluationDatasetReq) (*DeleteEvaluationDatasetRsp, error)
	UploadEvaluationDataset(context.Context, *UploadEvaluationDatasetReq) (*UploadEvaluationDatasetRsp, error)
	DownloadEvaluationDataset(context.Context, *DownloadEvaluationDatasetReq) (*DownloadEvaluationDatasetRsp, error)
	mustEmbedUnimplementedEvaluationDatasetManagerServer()
}

// UnimplementedEvaluationDatasetManagerServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationDatasetManagerServer struct {
}

func (UnimplementedEvaluationDatasetManagerServer) CreateEvaluationDataset(context.Context, *CreateEvaluationDatasetReq) (*CreateEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) CheckEvaluationDatasetExistence(context.Context, *CheckEvaluationDatasetExistenceReq) (*CheckEvaluationDatasetExistenceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEvaluationDatasetExistence not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) GetCsvHeaders(context.Context, *GetCsvHeadersReq) (*GetCsvHeadersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCsvHeaders not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) ValidateEvaluationDataset(context.Context, *ValidateEvaluationDatasetReq) (*ValidateEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) PreviewEvaluationDataset(context.Context, *PreviewEvaluationDatasetReq) (*PreviewEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) ReadEvaluationDataset(context.Context, *ReadEvaluationDatasetReq) (*ReadEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) UpdateEvaluationDataset(context.Context, *UpdateEvaluationDatasetReq) (*UpdateEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) DeleteEvaluationDataset(context.Context, *DeleteEvaluationDatasetReq) (*DeleteEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) UploadEvaluationDataset(context.Context, *UploadEvaluationDatasetReq) (*UploadEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) DownloadEvaluationDataset(context.Context, *DownloadEvaluationDatasetReq) (*DownloadEvaluationDatasetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadEvaluationDataset not implemented")
}
func (UnimplementedEvaluationDatasetManagerServer) mustEmbedUnimplementedEvaluationDatasetManagerServer() {
}

// UnsafeEvaluationDatasetManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationDatasetManagerServer will
// result in compilation errors.
type UnsafeEvaluationDatasetManagerServer interface {
	mustEmbedUnimplementedEvaluationDatasetManagerServer()
}

func RegisterEvaluationDatasetManagerServer(s grpc.ServiceRegistrar, srv EvaluationDatasetManagerServer) {
	s.RegisterService(&EvaluationDatasetManager_ServiceDesc, srv)
}

func _EvaluationDatasetManager_CreateEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).CreateEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_CreateEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).CreateEvaluationDataset(ctx, req.(*CreateEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_CheckEvaluationDatasetExistence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEvaluationDatasetExistenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).CheckEvaluationDatasetExistence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_CheckEvaluationDatasetExistence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).CheckEvaluationDatasetExistence(ctx, req.(*CheckEvaluationDatasetExistenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_GetCsvHeaders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCsvHeadersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).GetCsvHeaders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_GetCsvHeaders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).GetCsvHeaders(ctx, req.(*GetCsvHeadersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_ValidateEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).ValidateEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_ValidateEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).ValidateEvaluationDataset(ctx, req.(*ValidateEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_PreviewEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).PreviewEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_PreviewEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).PreviewEvaluationDataset(ctx, req.(*PreviewEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_ReadEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).ReadEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_ReadEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).ReadEvaluationDataset(ctx, req.(*ReadEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_UpdateEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).UpdateEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_UpdateEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).UpdateEvaluationDataset(ctx, req.(*UpdateEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_DeleteEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).DeleteEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_DeleteEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).DeleteEvaluationDataset(ctx, req.(*DeleteEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_UploadEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).UploadEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_UploadEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).UploadEvaluationDataset(ctx, req.(*UploadEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationDatasetManager_DownloadEvaluationDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadEvaluationDatasetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationDatasetManagerServer).DownloadEvaluationDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationDatasetManager_DownloadEvaluationDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationDatasetManagerServer).DownloadEvaluationDataset(ctx, req.(*DownloadEvaluationDatasetReq))
	}
	return interceptor(ctx, in, info, handler)
}

// EvaluationDatasetManager_ServiceDesc is the grpc.ServiceDesc for EvaluationDatasetManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationDatasetManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.EvaluationDatasetManager",
	HandlerType: (*EvaluationDatasetManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEvaluationDataset",
			Handler:    _EvaluationDatasetManager_CreateEvaluationDataset_Handler,
		},
		{
			MethodName: "CheckEvaluationDatasetExistence",
			Handler:    _EvaluationDatasetManager_CheckEvaluationDatasetExistence_Handler,
		},
		{
			MethodName: "GetCsvHeaders",
			Handler:    _EvaluationDatasetManager_GetCsvHeaders_Handler,
		},
		{
			MethodName: "ValidateEvaluationDataset",
			Handler:    _EvaluationDatasetManager_ValidateEvaluationDataset_Handler,
		},
		{
			MethodName: "PreviewEvaluationDataset",
			Handler:    _EvaluationDatasetManager_PreviewEvaluationDataset_Handler,
		},
		{
			MethodName: "ReadEvaluationDataset",
			Handler:    _EvaluationDatasetManager_ReadEvaluationDataset_Handler,
		},
		{
			MethodName: "UpdateEvaluationDataset",
			Handler:    _EvaluationDatasetManager_UpdateEvaluationDataset_Handler,
		},
		{
			MethodName: "DeleteEvaluationDataset",
			Handler:    _EvaluationDatasetManager_DeleteEvaluationDataset_Handler,
		},
		{
			MethodName: "UploadEvaluationDataset",
			Handler:    _EvaluationDatasetManager_UploadEvaluationDataset_Handler,
		},
		{
			MethodName: "DownloadEvaluationDataset",
			Handler:    _EvaluationDatasetManager_DownloadEvaluationDataset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model_evaluation.proto",
}

const (
	EvaluationScriptTemplateManager_CreateEvaluationScriptTemplate_FullMethodName = "/proto.EvaluationScriptTemplateManager/CreateEvaluationScriptTemplate"
	EvaluationScriptTemplateManager_ReadEvaluationScriptTemplate_FullMethodName   = "/proto.EvaluationScriptTemplateManager/ReadEvaluationScriptTemplate"
	EvaluationScriptTemplateManager_UpdateEvaluationScriptTemplate_FullMethodName = "/proto.EvaluationScriptTemplateManager/UpdateEvaluationScriptTemplate"
	EvaluationScriptTemplateManager_DeleteEvaluationScriptTemplate_FullMethodName = "/proto.EvaluationScriptTemplateManager/DeleteEvaluationScriptTemplate"
)

// EvaluationScriptTemplateManagerClient is the client API for EvaluationScriptTemplateManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationScriptTemplateManagerClient interface {
	CreateEvaluationScriptTemplate(ctx context.Context, in *CreateEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*CreateEvaluationScriptTemplateRsp, error)
	ReadEvaluationScriptTemplate(ctx context.Context, in *ReadEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*ReadEvaluationScriptTemplateRsp, error)
	UpdateEvaluationScriptTemplate(ctx context.Context, in *UpdateEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*UpdateEvaluationScriptTemplateRsp, error)
	DeleteEvaluationScriptTemplate(ctx context.Context, in *DeleteEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*DeleteEvaluationScriptTemplateRsp, error)
}

type evaluationScriptTemplateManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationScriptTemplateManagerClient(cc grpc.ClientConnInterface) EvaluationScriptTemplateManagerClient {
	return &evaluationScriptTemplateManagerClient{cc}
}

func (c *evaluationScriptTemplateManagerClient) CreateEvaluationScriptTemplate(ctx context.Context, in *CreateEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*CreateEvaluationScriptTemplateRsp, error) {
	out := new(CreateEvaluationScriptTemplateRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptTemplateManager_CreateEvaluationScriptTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptTemplateManagerClient) ReadEvaluationScriptTemplate(ctx context.Context, in *ReadEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*ReadEvaluationScriptTemplateRsp, error) {
	out := new(ReadEvaluationScriptTemplateRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptTemplateManager_ReadEvaluationScriptTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptTemplateManagerClient) UpdateEvaluationScriptTemplate(ctx context.Context, in *UpdateEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*UpdateEvaluationScriptTemplateRsp, error) {
	out := new(UpdateEvaluationScriptTemplateRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptTemplateManager_UpdateEvaluationScriptTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationScriptTemplateManagerClient) DeleteEvaluationScriptTemplate(ctx context.Context, in *DeleteEvaluationScriptTemplateReq, opts ...grpc.CallOption) (*DeleteEvaluationScriptTemplateRsp, error) {
	out := new(DeleteEvaluationScriptTemplateRsp)
	err := c.cc.Invoke(ctx, EvaluationScriptTemplateManager_DeleteEvaluationScriptTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvaluationScriptTemplateManagerServer is the server API for EvaluationScriptTemplateManager service.
// All implementations must embed UnimplementedEvaluationScriptTemplateManagerServer
// for forward compatibility
type EvaluationScriptTemplateManagerServer interface {
	CreateEvaluationScriptTemplate(context.Context, *CreateEvaluationScriptTemplateReq) (*CreateEvaluationScriptTemplateRsp, error)
	ReadEvaluationScriptTemplate(context.Context, *ReadEvaluationScriptTemplateReq) (*ReadEvaluationScriptTemplateRsp, error)
	UpdateEvaluationScriptTemplate(context.Context, *UpdateEvaluationScriptTemplateReq) (*UpdateEvaluationScriptTemplateRsp, error)
	DeleteEvaluationScriptTemplate(context.Context, *DeleteEvaluationScriptTemplateReq) (*DeleteEvaluationScriptTemplateRsp, error)
	mustEmbedUnimplementedEvaluationScriptTemplateManagerServer()
}

// UnimplementedEvaluationScriptTemplateManagerServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationScriptTemplateManagerServer struct {
}

func (UnimplementedEvaluationScriptTemplateManagerServer) CreateEvaluationScriptTemplate(context.Context, *CreateEvaluationScriptTemplateReq) (*CreateEvaluationScriptTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluationScriptTemplate not implemented")
}
func (UnimplementedEvaluationScriptTemplateManagerServer) ReadEvaluationScriptTemplate(context.Context, *ReadEvaluationScriptTemplateReq) (*ReadEvaluationScriptTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadEvaluationScriptTemplate not implemented")
}
func (UnimplementedEvaluationScriptTemplateManagerServer) UpdateEvaluationScriptTemplate(context.Context, *UpdateEvaluationScriptTemplateReq) (*UpdateEvaluationScriptTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluationScriptTemplate not implemented")
}
func (UnimplementedEvaluationScriptTemplateManagerServer) DeleteEvaluationScriptTemplate(context.Context, *DeleteEvaluationScriptTemplateReq) (*DeleteEvaluationScriptTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvaluationScriptTemplate not implemented")
}
func (UnimplementedEvaluationScriptTemplateManagerServer) mustEmbedUnimplementedEvaluationScriptTemplateManagerServer() {
}

// UnsafeEvaluationScriptTemplateManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationScriptTemplateManagerServer will
// result in compilation errors.
type UnsafeEvaluationScriptTemplateManagerServer interface {
	mustEmbedUnimplementedEvaluationScriptTemplateManagerServer()
}

func RegisterEvaluationScriptTemplateManagerServer(s grpc.ServiceRegistrar, srv EvaluationScriptTemplateManagerServer) {
	s.RegisterService(&EvaluationScriptTemplateManager_ServiceDesc, srv)
}

func _EvaluationScriptTemplateManager_CreateEvaluationScriptTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationScriptTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptTemplateManagerServer).CreateEvaluationScriptTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptTemplateManager_CreateEvaluationScriptTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptTemplateManagerServer).CreateEvaluationScriptTemplate(ctx, req.(*CreateEvaluationScriptTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptTemplateManager_ReadEvaluationScriptTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadEvaluationScriptTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptTemplateManagerServer).ReadEvaluationScriptTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptTemplateManager_ReadEvaluationScriptTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptTemplateManagerServer).ReadEvaluationScriptTemplate(ctx, req.(*ReadEvaluationScriptTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptTemplateManager_UpdateEvaluationScriptTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationScriptTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptTemplateManagerServer).UpdateEvaluationScriptTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptTemplateManager_UpdateEvaluationScriptTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptTemplateManagerServer).UpdateEvaluationScriptTemplate(ctx, req.(*UpdateEvaluationScriptTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationScriptTemplateManager_DeleteEvaluationScriptTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvaluationScriptTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationScriptTemplateManagerServer).DeleteEvaluationScriptTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvaluationScriptTemplateManager_DeleteEvaluationScriptTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationScriptTemplateManagerServer).DeleteEvaluationScriptTemplate(ctx, req.(*DeleteEvaluationScriptTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// EvaluationScriptTemplateManager_ServiceDesc is the grpc.ServiceDesc for EvaluationScriptTemplateManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationScriptTemplateManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.EvaluationScriptTemplateManager",
	HandlerType: (*EvaluationScriptTemplateManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEvaluationScriptTemplate",
			Handler:    _EvaluationScriptTemplateManager_CreateEvaluationScriptTemplate_Handler,
		},
		{
			MethodName: "ReadEvaluationScriptTemplate",
			Handler:    _EvaluationScriptTemplateManager_ReadEvaluationScriptTemplate_Handler,
		},
		{
			MethodName: "UpdateEvaluationScriptTemplate",
			Handler:    _EvaluationScriptTemplateManager_UpdateEvaluationScriptTemplate_Handler,
		},
		{
			MethodName: "DeleteEvaluationScriptTemplate",
			Handler:    _EvaluationScriptTemplateManager_DeleteEvaluationScriptTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model_evaluation.proto",
}
