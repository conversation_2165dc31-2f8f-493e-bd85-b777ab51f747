// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_model.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelManager_ListModels_FullMethodName                   = "/proto.ModelManager/ListModels"
	ModelManager_CreateModel_FullMethodName                  = "/proto.ModelManager/CreateModel"
	ModelManager_UpdateModel_FullMethodName                  = "/proto.ModelManager/UpdateModel"
	ModelManager_DeleteModel_FullMethodName                  = "/proto.ModelManager/DeleteModel"
	ModelManager_CheckModelExistence_FullMethodName          = "/proto.ModelManager/CheckModelExistence"
	ModelManager_CheckModelsPackage_FullMethodName           = "/proto.ModelManager/CheckModelsPackage"
	ModelManager_ImportModelsPackage_FullMethodName          = "/proto.ModelManager/ImportModelsPackage"
	ModelManager_ExportModelsPackage_FullMethodName          = "/proto.ModelManager/ExportModelsPackage"
	ModelManager_ListModelReleases_FullMethodName            = "/proto.ModelManager/ListModelReleases"
	ModelManager_CreateModelRelease_FullMethodName           = "/proto.ModelManager/CreateModelRelease"
	ModelManager_UpdateModelRelease_FullMethodName           = "/proto.ModelManager/UpdateModelRelease"
	ModelManager_DeleteModelRelease_FullMethodName           = "/proto.ModelManager/DeleteModelRelease"
	ModelManager_GetModelReleaseRelationGraph_FullMethodName = "/proto.ModelManager/GetModelReleaseRelationGraph"
	ModelManager_ParseFileModel_FullMethodName               = "/proto.ModelManager/ParseFileModel"
	ModelManager_CreateModelFileDownloadJobs_FullMethodName  = "/proto.ModelManager/CreateModelFileDownloadJobs"
)

// ModelManagerClient is the client API for ModelManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelManagerClient interface {
	ListModels(ctx context.Context, in *ListModelsReq, opts ...grpc.CallOption) (*ListModelsRsp, error)
	CreateModel(ctx context.Context, in *CreateModelReq, opts ...grpc.CallOption) (*CreateModelRsp, error)
	UpdateModel(ctx context.Context, in *UpdateModelReq, opts ...grpc.CallOption) (*UpdateModelRsp, error)
	DeleteModel(ctx context.Context, in *DeleteModelReq, opts ...grpc.CallOption) (*DeleteModelRsp, error)
	// CheckModelExistence 判断指定模型是否存在，根据id时全局进行判断，根据name时每个用户进行判断
	CheckModelExistence(ctx context.Context, in *CheckModelReq, opts ...grpc.CallOption) (*CheckResourceExistenceRsp, error)
	CheckModelsPackage(ctx context.Context, in *CheckModelsPackageReq, opts ...grpc.CallOption) (*CheckModelsPackageRsp, error)
	ImportModelsPackage(ctx context.Context, in *ImportModelsPackageReq, opts ...grpc.CallOption) (*ImportModelsPackageRsp, error)
	ExportModelsPackage(ctx context.Context, in *ExportModelsPackageReq, opts ...grpc.CallOption) (*ExportModelsPackageRsp, error)
	ListModelReleases(ctx context.Context, in *ListModelReleasesReq, opts ...grpc.CallOption) (*ListModelReleasesRsp, error)
	CreateModelRelease(ctx context.Context, in *CreateModelReleaseReq, opts ...grpc.CallOption) (*CreateModelReleaseRsp, error)
	UpdateModelRelease(ctx context.Context, in *UpdateModelReleaseReq, opts ...grpc.CallOption) (*UpdateModelReleaseRsp, error)
	DeleteModelRelease(ctx context.Context, in *DeleteModelReleaseReq, opts ...grpc.CallOption) (*DeleteModelReleaseRsp, error)
	GetModelReleaseRelationGraph(ctx context.Context, in *GetModelReleaseRelationGraphReq, opts ...grpc.CallOption) (*GetModelReleaseRelationGraphRsp, error)
	ParseFileModel(ctx context.Context, in *ParseFileModelReq, opts ...grpc.CallOption) (*ParseFileModelRsp, error)
	CreateModelFileDownloadJobs(ctx context.Context, in *CreateModelFileDownloadJobsReq, opts ...grpc.CallOption) (*CreateModelFileDownloadJobsRsp, error)
}

type modelManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewModelManagerClient(cc grpc.ClientConnInterface) ModelManagerClient {
	return &modelManagerClient{cc}
}

func (c *modelManagerClient) ListModels(ctx context.Context, in *ListModelsReq, opts ...grpc.CallOption) (*ListModelsRsp, error) {
	out := new(ListModelsRsp)
	err := c.cc.Invoke(ctx, ModelManager_ListModels_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) CreateModel(ctx context.Context, in *CreateModelReq, opts ...grpc.CallOption) (*CreateModelRsp, error) {
	out := new(CreateModelRsp)
	err := c.cc.Invoke(ctx, ModelManager_CreateModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) UpdateModel(ctx context.Context, in *UpdateModelReq, opts ...grpc.CallOption) (*UpdateModelRsp, error) {
	out := new(UpdateModelRsp)
	err := c.cc.Invoke(ctx, ModelManager_UpdateModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) DeleteModel(ctx context.Context, in *DeleteModelReq, opts ...grpc.CallOption) (*DeleteModelRsp, error) {
	out := new(DeleteModelRsp)
	err := c.cc.Invoke(ctx, ModelManager_DeleteModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) CheckModelExistence(ctx context.Context, in *CheckModelReq, opts ...grpc.CallOption) (*CheckResourceExistenceRsp, error) {
	out := new(CheckResourceExistenceRsp)
	err := c.cc.Invoke(ctx, ModelManager_CheckModelExistence_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) CheckModelsPackage(ctx context.Context, in *CheckModelsPackageReq, opts ...grpc.CallOption) (*CheckModelsPackageRsp, error) {
	out := new(CheckModelsPackageRsp)
	err := c.cc.Invoke(ctx, ModelManager_CheckModelsPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) ImportModelsPackage(ctx context.Context, in *ImportModelsPackageReq, opts ...grpc.CallOption) (*ImportModelsPackageRsp, error) {
	out := new(ImportModelsPackageRsp)
	err := c.cc.Invoke(ctx, ModelManager_ImportModelsPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) ExportModelsPackage(ctx context.Context, in *ExportModelsPackageReq, opts ...grpc.CallOption) (*ExportModelsPackageRsp, error) {
	out := new(ExportModelsPackageRsp)
	err := c.cc.Invoke(ctx, ModelManager_ExportModelsPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) ListModelReleases(ctx context.Context, in *ListModelReleasesReq, opts ...grpc.CallOption) (*ListModelReleasesRsp, error) {
	out := new(ListModelReleasesRsp)
	err := c.cc.Invoke(ctx, ModelManager_ListModelReleases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) CreateModelRelease(ctx context.Context, in *CreateModelReleaseReq, opts ...grpc.CallOption) (*CreateModelReleaseRsp, error) {
	out := new(CreateModelReleaseRsp)
	err := c.cc.Invoke(ctx, ModelManager_CreateModelRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) UpdateModelRelease(ctx context.Context, in *UpdateModelReleaseReq, opts ...grpc.CallOption) (*UpdateModelReleaseRsp, error) {
	out := new(UpdateModelReleaseRsp)
	err := c.cc.Invoke(ctx, ModelManager_UpdateModelRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) DeleteModelRelease(ctx context.Context, in *DeleteModelReleaseReq, opts ...grpc.CallOption) (*DeleteModelReleaseRsp, error) {
	out := new(DeleteModelReleaseRsp)
	err := c.cc.Invoke(ctx, ModelManager_DeleteModelRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) GetModelReleaseRelationGraph(ctx context.Context, in *GetModelReleaseRelationGraphReq, opts ...grpc.CallOption) (*GetModelReleaseRelationGraphRsp, error) {
	out := new(GetModelReleaseRelationGraphRsp)
	err := c.cc.Invoke(ctx, ModelManager_GetModelReleaseRelationGraph_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) ParseFileModel(ctx context.Context, in *ParseFileModelReq, opts ...grpc.CallOption) (*ParseFileModelRsp, error) {
	out := new(ParseFileModelRsp)
	err := c.cc.Invoke(ctx, ModelManager_ParseFileModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelManagerClient) CreateModelFileDownloadJobs(ctx context.Context, in *CreateModelFileDownloadJobsReq, opts ...grpc.CallOption) (*CreateModelFileDownloadJobsRsp, error) {
	out := new(CreateModelFileDownloadJobsRsp)
	err := c.cc.Invoke(ctx, ModelManager_CreateModelFileDownloadJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelManagerServer is the server API for ModelManager service.
// All implementations must embed UnimplementedModelManagerServer
// for forward compatibility
type ModelManagerServer interface {
	ListModels(context.Context, *ListModelsReq) (*ListModelsRsp, error)
	CreateModel(context.Context, *CreateModelReq) (*CreateModelRsp, error)
	UpdateModel(context.Context, *UpdateModelReq) (*UpdateModelRsp, error)
	DeleteModel(context.Context, *DeleteModelReq) (*DeleteModelRsp, error)
	// CheckModelExistence 判断指定模型是否存在，根据id时全局进行判断，根据name时每个用户进行判断
	CheckModelExistence(context.Context, *CheckModelReq) (*CheckResourceExistenceRsp, error)
	CheckModelsPackage(context.Context, *CheckModelsPackageReq) (*CheckModelsPackageRsp, error)
	ImportModelsPackage(context.Context, *ImportModelsPackageReq) (*ImportModelsPackageRsp, error)
	ExportModelsPackage(context.Context, *ExportModelsPackageReq) (*ExportModelsPackageRsp, error)
	ListModelReleases(context.Context, *ListModelReleasesReq) (*ListModelReleasesRsp, error)
	CreateModelRelease(context.Context, *CreateModelReleaseReq) (*CreateModelReleaseRsp, error)
	UpdateModelRelease(context.Context, *UpdateModelReleaseReq) (*UpdateModelReleaseRsp, error)
	DeleteModelRelease(context.Context, *DeleteModelReleaseReq) (*DeleteModelReleaseRsp, error)
	GetModelReleaseRelationGraph(context.Context, *GetModelReleaseRelationGraphReq) (*GetModelReleaseRelationGraphRsp, error)
	ParseFileModel(context.Context, *ParseFileModelReq) (*ParseFileModelRsp, error)
	CreateModelFileDownloadJobs(context.Context, *CreateModelFileDownloadJobsReq) (*CreateModelFileDownloadJobsRsp, error)
	mustEmbedUnimplementedModelManagerServer()
}

// UnimplementedModelManagerServer must be embedded to have forward compatible implementations.
type UnimplementedModelManagerServer struct {
}

func (UnimplementedModelManagerServer) ListModels(context.Context, *ListModelsReq) (*ListModelsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListModels not implemented")
}
func (UnimplementedModelManagerServer) CreateModel(context.Context, *CreateModelReq) (*CreateModelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModel not implemented")
}
func (UnimplementedModelManagerServer) UpdateModel(context.Context, *UpdateModelReq) (*UpdateModelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateModel not implemented")
}
func (UnimplementedModelManagerServer) DeleteModel(context.Context, *DeleteModelReq) (*DeleteModelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteModel not implemented")
}
func (UnimplementedModelManagerServer) CheckModelExistence(context.Context, *CheckModelReq) (*CheckResourceExistenceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckModelExistence not implemented")
}
func (UnimplementedModelManagerServer) CheckModelsPackage(context.Context, *CheckModelsPackageReq) (*CheckModelsPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckModelsPackage not implemented")
}
func (UnimplementedModelManagerServer) ImportModelsPackage(context.Context, *ImportModelsPackageReq) (*ImportModelsPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportModelsPackage not implemented")
}
func (UnimplementedModelManagerServer) ExportModelsPackage(context.Context, *ExportModelsPackageReq) (*ExportModelsPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportModelsPackage not implemented")
}
func (UnimplementedModelManagerServer) ListModelReleases(context.Context, *ListModelReleasesReq) (*ListModelReleasesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListModelReleases not implemented")
}
func (UnimplementedModelManagerServer) CreateModelRelease(context.Context, *CreateModelReleaseReq) (*CreateModelReleaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModelRelease not implemented")
}
func (UnimplementedModelManagerServer) UpdateModelRelease(context.Context, *UpdateModelReleaseReq) (*UpdateModelReleaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateModelRelease not implemented")
}
func (UnimplementedModelManagerServer) DeleteModelRelease(context.Context, *DeleteModelReleaseReq) (*DeleteModelReleaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteModelRelease not implemented")
}
func (UnimplementedModelManagerServer) GetModelReleaseRelationGraph(context.Context, *GetModelReleaseRelationGraphReq) (*GetModelReleaseRelationGraphRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelReleaseRelationGraph not implemented")
}
func (UnimplementedModelManagerServer) ParseFileModel(context.Context, *ParseFileModelReq) (*ParseFileModelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseFileModel not implemented")
}
func (UnimplementedModelManagerServer) CreateModelFileDownloadJobs(context.Context, *CreateModelFileDownloadJobsReq) (*CreateModelFileDownloadJobsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModelFileDownloadJobs not implemented")
}
func (UnimplementedModelManagerServer) mustEmbedUnimplementedModelManagerServer() {}

// UnsafeModelManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelManagerServer will
// result in compilation errors.
type UnsafeModelManagerServer interface {
	mustEmbedUnimplementedModelManagerServer()
}

func RegisterModelManagerServer(s grpc.ServiceRegistrar, srv ModelManagerServer) {
	s.RegisterService(&ModelManager_ServiceDesc, srv)
}

func _ModelManager_ListModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListModelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).ListModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_ListModels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).ListModels(ctx, req.(*ListModelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_CreateModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).CreateModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_CreateModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).CreateModel(ctx, req.(*CreateModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_UpdateModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).UpdateModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_UpdateModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).UpdateModel(ctx, req.(*UpdateModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_DeleteModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).DeleteModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_DeleteModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).DeleteModel(ctx, req.(*DeleteModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_CheckModelExistence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).CheckModelExistence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_CheckModelExistence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).CheckModelExistence(ctx, req.(*CheckModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_CheckModelsPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckModelsPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).CheckModelsPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_CheckModelsPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).CheckModelsPackage(ctx, req.(*CheckModelsPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_ImportModelsPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportModelsPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).ImportModelsPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_ImportModelsPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).ImportModelsPackage(ctx, req.(*ImportModelsPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_ExportModelsPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportModelsPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).ExportModelsPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_ExportModelsPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).ExportModelsPackage(ctx, req.(*ExportModelsPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_ListModelReleases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListModelReleasesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).ListModelReleases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_ListModelReleases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).ListModelReleases(ctx, req.(*ListModelReleasesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_CreateModelRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelReleaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).CreateModelRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_CreateModelRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).CreateModelRelease(ctx, req.(*CreateModelReleaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_UpdateModelRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateModelReleaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).UpdateModelRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_UpdateModelRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).UpdateModelRelease(ctx, req.(*UpdateModelReleaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_DeleteModelRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteModelReleaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).DeleteModelRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_DeleteModelRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).DeleteModelRelease(ctx, req.(*DeleteModelReleaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_GetModelReleaseRelationGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelReleaseRelationGraphReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).GetModelReleaseRelationGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_GetModelReleaseRelationGraph_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).GetModelReleaseRelationGraph(ctx, req.(*GetModelReleaseRelationGraphReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_ParseFileModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseFileModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).ParseFileModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_ParseFileModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).ParseFileModel(ctx, req.(*ParseFileModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelManager_CreateModelFileDownloadJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelFileDownloadJobsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelManagerServer).CreateModelFileDownloadJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelManager_CreateModelFileDownloadJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelManagerServer).CreateModelFileDownloadJobs(ctx, req.(*CreateModelFileDownloadJobsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelManager_ServiceDesc is the grpc.ServiceDesc for ModelManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ModelManager",
	HandlerType: (*ModelManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListModels",
			Handler:    _ModelManager_ListModels_Handler,
		},
		{
			MethodName: "CreateModel",
			Handler:    _ModelManager_CreateModel_Handler,
		},
		{
			MethodName: "UpdateModel",
			Handler:    _ModelManager_UpdateModel_Handler,
		},
		{
			MethodName: "DeleteModel",
			Handler:    _ModelManager_DeleteModel_Handler,
		},
		{
			MethodName: "CheckModelExistence",
			Handler:    _ModelManager_CheckModelExistence_Handler,
		},
		{
			MethodName: "CheckModelsPackage",
			Handler:    _ModelManager_CheckModelsPackage_Handler,
		},
		{
			MethodName: "ImportModelsPackage",
			Handler:    _ModelManager_ImportModelsPackage_Handler,
		},
		{
			MethodName: "ExportModelsPackage",
			Handler:    _ModelManager_ExportModelsPackage_Handler,
		},
		{
			MethodName: "ListModelReleases",
			Handler:    _ModelManager_ListModelReleases_Handler,
		},
		{
			MethodName: "CreateModelRelease",
			Handler:    _ModelManager_CreateModelRelease_Handler,
		},
		{
			MethodName: "UpdateModelRelease",
			Handler:    _ModelManager_UpdateModelRelease_Handler,
		},
		{
			MethodName: "DeleteModelRelease",
			Handler:    _ModelManager_DeleteModelRelease_Handler,
		},
		{
			MethodName: "GetModelReleaseRelationGraph",
			Handler:    _ModelManager_GetModelReleaseRelationGraph_Handler,
		},
		{
			MethodName: "ParseFileModel",
			Handler:    _ModelManager_ParseFileModel_Handler,
		},
		{
			MethodName: "CreateModelFileDownloadJobs",
			Handler:    _ModelManager_CreateModelFileDownloadJobs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_model.proto",
}
