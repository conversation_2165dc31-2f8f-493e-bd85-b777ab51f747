// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_onnx_service.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OnnxParseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filepath string `protobuf:"bytes,1,opt,name=filepath,proto3" json:"filepath,omitempty"`
}

func (x *OnnxParseReq) Reset() {
	*x = OnnxParseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnnxParseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnnxParseReq) ProtoMessage() {}

func (x *OnnxParseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnnxParseReq.ProtoReflect.Descriptor instead.
func (*OnnxParseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{0}
}

func (x *OnnxParseReq) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

type OnnxParseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model *OnnxModel `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *OnnxParseRsp) Reset() {
	*x = OnnxParseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnnxParseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnnxParseRsp) ProtoMessage() {}

func (x *OnnxParseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnnxParseRsp.ProtoReflect.Descriptor instead.
func (*OnnxParseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{1}
}

func (x *OnnxParseRsp) GetModel() *OnnxModel {
	if x != nil {
		return x.Model
	}
	return nil
}

type OnnxModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IrVersion          int64              `protobuf:"varint,1,opt,name=ir_version,json=irVersion,proto3" json:"ir_version,omitempty"`
	ModelVersion       int64              `protobuf:"varint,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	OpSetImportVersion []int64            `protobuf:"varint,3,rep,packed,name=op_set_import_version,json=opSetImportVersion,proto3" json:"op_set_import_version,omitempty"`
	ProducerName       string             `protobuf:"bytes,4,opt,name=producer_name,json=producerName,proto3" json:"producer_name,omitempty"`
	ProducerVersion    string             `protobuf:"bytes,5,opt,name=producer_version,json=producerVersion,proto3" json:"producer_version,omitempty"`
	Input              map[string]*Input  `protobuf:"bytes,6,rep,name=input,proto3" json:"input,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Output             map[string]*Output `protobuf:"bytes,7,rep,name=output,proto3" json:"output,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *OnnxModel) Reset() {
	*x = OnnxModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnnxModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnnxModel) ProtoMessage() {}

func (x *OnnxModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnnxModel.ProtoReflect.Descriptor instead.
func (*OnnxModel) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{2}
}

func (x *OnnxModel) GetIrVersion() int64 {
	if x != nil {
		return x.IrVersion
	}
	return 0
}

func (x *OnnxModel) GetModelVersion() int64 {
	if x != nil {
		return x.ModelVersion
	}
	return 0
}

func (x *OnnxModel) GetOpSetImportVersion() []int64 {
	if x != nil {
		return x.OpSetImportVersion
	}
	return nil
}

func (x *OnnxModel) GetProducerName() string {
	if x != nil {
		return x.ProducerName
	}
	return ""
}

func (x *OnnxModel) GetProducerVersion() string {
	if x != nil {
		return x.ProducerVersion
	}
	return ""
}

func (x *OnnxModel) GetInput() map[string]*Input {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *OnnxModel) GetOutput() map[string]*Output {
	if x != nil {
		return x.Output
	}
	return nil
}

type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InputDim      *InputOrOutputDim `protobuf:"bytes,1,opt,name=input_dim,json=inputDim,proto3" json:"input_dim,omitempty"`
	InputElemType string            `protobuf:"bytes,2,opt,name=input_elem_type,json=inputElemType,proto3" json:"input_elem_type,omitempty"`
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{3}
}

func (x *Input) GetInputDim() *InputOrOutputDim {
	if x != nil {
		return x.InputDim
	}
	return nil
}

func (x *Input) GetInputElemType() string {
	if x != nil {
		return x.InputElemType
	}
	return ""
}

type Output struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutputDim      *InputOrOutputDim `protobuf:"bytes,1,opt,name=output_dim,json=outputDim,proto3" json:"output_dim,omitempty"`
	OutputElemType string            `protobuf:"bytes,2,opt,name=output_elem_type,json=outputElemType,proto3" json:"output_elem_type,omitempty"`
}

func (x *Output) Reset() {
	*x = Output{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Output) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Output) ProtoMessage() {}

func (x *Output) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Output.ProtoReflect.Descriptor instead.
func (*Output) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{4}
}

func (x *Output) GetOutputDim() *InputOrOutputDim {
	if x != nil {
		return x.OutputDim
	}
	return nil
}

func (x *Output) GetOutputElemType() string {
	if x != nil {
		return x.OutputElemType
	}
	return ""
}

type InputOrOutputDim struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Param string  `protobuf:"bytes,1,opt,name=param,proto3" json:"param,omitempty"`
	Value []int64 `protobuf:"varint,2,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *InputOrOutputDim) Reset() {
	*x = InputOrOutputDim{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_onnx_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputOrOutputDim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputOrOutputDim) ProtoMessage() {}

func (x *InputOrOutputDim) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_onnx_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputOrOutputDim.ProtoReflect.Descriptor instead.
func (*InputOrOutputDim) Descriptor() ([]byte, []int) {
	return file_proto_rpc_onnx_service_proto_rawDescGZIP(), []int{5}
}

func (x *InputOrOutputDim) GetParam() string {
	if x != nil {
		return x.Param
	}
	return ""
}

func (x *InputOrOutputDim) GetValue() []int64 {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_proto_rpc_onnx_service_proto protoreflect.FileDescriptor

var file_proto_rpc_onnx_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6f, 0x6e, 0x6e, 0x78,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x0c, 0x4f, 0x6e, 0x6e, 0x78, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74,
	0x68, 0x22, 0x36, 0x0a, 0x0c, 0x4f, 0x6e, 0x6e, 0x78, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x26, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x6e, 0x6e, 0x78, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0xcd, 0x03, 0x0a, 0x09, 0x4f, 0x6e,
	0x6e, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x72, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x72, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x15, 0x6f,
	0x70, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x6f, 0x70, 0x53, 0x65,
	0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x6e, 0x6e, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x6e, 0x6e, 0x78, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x1a, 0x46, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x48, 0x0a, 0x0b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x65, 0x0a, 0x05, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x12, 0x34, 0x0a, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x64, 0x69, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x4f, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x44, 0x69, 0x6d, 0x52, 0x08,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x6a, 0x0a, 0x06, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x36, 0x0a, 0x0a, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x5f, 0x64, 0x69, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x72, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x44, 0x69, 0x6d, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x44,
	0x69, 0x6d, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3e, 0x0a, 0x10,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x44, 0x69, 0x6d,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x44, 0x0a, 0x0b,
	0x4f, 0x6e, 0x6e, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x4f,
	0x6e, 0x6e, 0x78, 0x50, 0x61, 0x72, 0x73, 0x65, 0x12, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4f, 0x6e, 0x6e, 0x78, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x6e, 0x6e, 0x78, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52,
	0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_rpc_onnx_service_proto_rawDescOnce sync.Once
	file_proto_rpc_onnx_service_proto_rawDescData = file_proto_rpc_onnx_service_proto_rawDesc
)

func file_proto_rpc_onnx_service_proto_rawDescGZIP() []byte {
	file_proto_rpc_onnx_service_proto_rawDescOnce.Do(func() {
		file_proto_rpc_onnx_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_onnx_service_proto_rawDescData)
	})
	return file_proto_rpc_onnx_service_proto_rawDescData
}

var file_proto_rpc_onnx_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_rpc_onnx_service_proto_goTypes = []interface{}{
	(*OnnxParseReq)(nil),     // 0: proto.OnnxParseReq
	(*OnnxParseRsp)(nil),     // 1: proto.OnnxParseRsp
	(*OnnxModel)(nil),        // 2: proto.OnnxModel
	(*Input)(nil),            // 3: proto.Input
	(*Output)(nil),           // 4: proto.Output
	(*InputOrOutputDim)(nil), // 5: proto.InputOrOutputDim
	nil,                      // 6: proto.OnnxModel.InputEntry
	nil,                      // 7: proto.OnnxModel.OutputEntry
}
var file_proto_rpc_onnx_service_proto_depIdxs = []int32{
	2, // 0: proto.OnnxParseRsp.model:type_name -> proto.OnnxModel
	6, // 1: proto.OnnxModel.input:type_name -> proto.OnnxModel.InputEntry
	7, // 2: proto.OnnxModel.output:type_name -> proto.OnnxModel.OutputEntry
	5, // 3: proto.Input.input_dim:type_name -> proto.InputOrOutputDim
	5, // 4: proto.Output.output_dim:type_name -> proto.InputOrOutputDim
	3, // 5: proto.OnnxModel.InputEntry.value:type_name -> proto.Input
	4, // 6: proto.OnnxModel.OutputEntry.value:type_name -> proto.Output
	0, // 7: proto.OnnxService.OnnxParse:input_type -> proto.OnnxParseReq
	1, // 8: proto.OnnxService.OnnxParse:output_type -> proto.OnnxParseRsp
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_proto_rpc_onnx_service_proto_init() }
func file_proto_rpc_onnx_service_proto_init() {
	if File_proto_rpc_onnx_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_onnx_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnnxParseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_onnx_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnnxParseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_onnx_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnnxModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_onnx_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_onnx_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Output); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_onnx_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputOrOutputDim); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_onnx_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_onnx_service_proto_goTypes,
		DependencyIndexes: file_proto_rpc_onnx_service_proto_depIdxs,
		MessageInfos:      file_proto_rpc_onnx_service_proto_msgTypes,
	}.Build()
	File_proto_rpc_onnx_service_proto = out.File
	file_proto_rpc_onnx_service_proto_rawDesc = nil
	file_proto_rpc_onnx_service_proto_goTypes = nil
	file_proto_rpc_onnx_service_proto_depIdxs = nil
}
