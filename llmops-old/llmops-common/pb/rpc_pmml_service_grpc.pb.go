// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_pmml_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PmmlService_PmmlParse_FullMethodName = "/proto.PmmlService/PmmlParse"
)

// PmmlServiceClient is the client API for PmmlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PmmlServiceClient interface {
	PmmlParse(ctx context.Context, in *PmmlParseReq, opts ...grpc.CallOption) (*PmmlParseRsp, error)
}

type pmmlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPmmlServiceClient(cc grpc.ClientConnInterface) PmmlServiceClient {
	return &pmmlServiceClient{cc}
}

func (c *pmmlServiceClient) PmmlParse(ctx context.Context, in *PmmlParseReq, opts ...grpc.CallOption) (*PmmlParseRsp, error) {
	out := new(PmmlParseRsp)
	err := c.cc.Invoke(ctx, PmmlService_PmmlParse_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PmmlServiceServer is the server API for PmmlService service.
// All implementations must embed UnimplementedPmmlServiceServer
// for forward compatibility
type PmmlServiceServer interface {
	PmmlParse(context.Context, *PmmlParseReq) (*PmmlParseRsp, error)
	mustEmbedUnimplementedPmmlServiceServer()
}

// UnimplementedPmmlServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPmmlServiceServer struct {
}

func (UnimplementedPmmlServiceServer) PmmlParse(context.Context, *PmmlParseReq) (*PmmlParseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PmmlParse not implemented")
}
func (UnimplementedPmmlServiceServer) mustEmbedUnimplementedPmmlServiceServer() {}

// UnsafePmmlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PmmlServiceServer will
// result in compilation errors.
type UnsafePmmlServiceServer interface {
	mustEmbedUnimplementedPmmlServiceServer()
}

func RegisterPmmlServiceServer(s grpc.ServiceRegistrar, srv PmmlServiceServer) {
	s.RegisterService(&PmmlService_ServiceDesc, srv)
}

func _PmmlService_PmmlParse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PmmlParseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PmmlServiceServer).PmmlParse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PmmlService_PmmlParse_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PmmlServiceServer).PmmlParse(ctx, req.(*PmmlParseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PmmlService_ServiceDesc is the grpc.ServiceDesc for PmmlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PmmlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.PmmlService",
	HandlerType: (*PmmlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PmmlParse",
			Handler:    _PmmlService_PmmlParse_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_pmml_service.proto",
}
