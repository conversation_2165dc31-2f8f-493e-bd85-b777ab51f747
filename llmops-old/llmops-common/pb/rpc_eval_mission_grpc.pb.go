// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_eval_mission.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	EvalMissionMgr_ReadEvalMissions_FullMethodName     = "/proto.EvalMissionMgr/ReadEvalMissions"
	EvalMissionMgr_CreateEvalMission_FullMethodName    = "/proto.EvalMissionMgr/CreateEvalMission"
	EvalMissionMgr_UpdateEvalMission_FullMethodName    = "/proto.EvalMissionMgr/UpdateEvalMission"
	EvalMissionMgr_DeleteEvalMissions_FullMethodName   = "/proto.EvalMissionMgr/DeleteEvalMissions"
	EvalMissionMgr_StartEvalMissions_FullMethodName    = "/proto.EvalMissionMgr/StartEvalMissions"
	EvalMissionMgr_StopEvalMissions_FullMethodName     = "/proto.EvalMissionMgr/StopEvalMissions"
	EvalMissionMgr_GetEvalMissionResult_FullMethodName = "/proto.EvalMissionMgr/GetEvalMissionResult"
)

// EvalMissionMgrClient is the client API for EvalMissionMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvalMissionMgrClient interface {
	ReadEvalMissions(ctx context.Context, in *ReadEvalMissionsReq, opts ...grpc.CallOption) (*ReadEvalMissionsRsp, error)
	CreateEvalMission(ctx context.Context, in *CreateEvalMissionsReq, opts ...grpc.CallOption) (*CreateEvalMissionsRsp, error)
	UpdateEvalMission(ctx context.Context, in *UpdateEvalMissionsReq, opts ...grpc.CallOption) (*UpdateEvalMissionsRsp, error)
	DeleteEvalMissions(ctx context.Context, in *DeleteEvalMissionsReq, opts ...grpc.CallOption) (*DeleteEvalMissionsRsp, error)
	StartEvalMissions(ctx context.Context, in *StartEvalMissionsReq, opts ...grpc.CallOption) (*StartEvalMissionsRsp, error)
	StopEvalMissions(ctx context.Context, in *StopEvalMissionsReq, opts ...grpc.CallOption) (*StopEvalMissionsRsp, error)
	GetEvalMissionResult(ctx context.Context, in *GetEvalMissionResultReq, opts ...grpc.CallOption) (*GetEvalMissionResultRsp, error)
}

type evalMissionMgrClient struct {
	cc grpc.ClientConnInterface
}

func NewEvalMissionMgrClient(cc grpc.ClientConnInterface) EvalMissionMgrClient {
	return &evalMissionMgrClient{cc}
}

func (c *evalMissionMgrClient) ReadEvalMissions(ctx context.Context, in *ReadEvalMissionsReq, opts ...grpc.CallOption) (*ReadEvalMissionsRsp, error) {
	out := new(ReadEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_ReadEvalMissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) CreateEvalMission(ctx context.Context, in *CreateEvalMissionsReq, opts ...grpc.CallOption) (*CreateEvalMissionsRsp, error) {
	out := new(CreateEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_CreateEvalMission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) UpdateEvalMission(ctx context.Context, in *UpdateEvalMissionsReq, opts ...grpc.CallOption) (*UpdateEvalMissionsRsp, error) {
	out := new(UpdateEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_UpdateEvalMission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) DeleteEvalMissions(ctx context.Context, in *DeleteEvalMissionsReq, opts ...grpc.CallOption) (*DeleteEvalMissionsRsp, error) {
	out := new(DeleteEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_DeleteEvalMissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) StartEvalMissions(ctx context.Context, in *StartEvalMissionsReq, opts ...grpc.CallOption) (*StartEvalMissionsRsp, error) {
	out := new(StartEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_StartEvalMissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) StopEvalMissions(ctx context.Context, in *StopEvalMissionsReq, opts ...grpc.CallOption) (*StopEvalMissionsRsp, error) {
	out := new(StopEvalMissionsRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_StopEvalMissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evalMissionMgrClient) GetEvalMissionResult(ctx context.Context, in *GetEvalMissionResultReq, opts ...grpc.CallOption) (*GetEvalMissionResultRsp, error) {
	out := new(GetEvalMissionResultRsp)
	err := c.cc.Invoke(ctx, EvalMissionMgr_GetEvalMissionResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvalMissionMgrServer is the server API for EvalMissionMgr service.
// All implementations must embed UnimplementedEvalMissionMgrServer
// for forward compatibility
type EvalMissionMgrServer interface {
	ReadEvalMissions(context.Context, *ReadEvalMissionsReq) (*ReadEvalMissionsRsp, error)
	CreateEvalMission(context.Context, *CreateEvalMissionsReq) (*CreateEvalMissionsRsp, error)
	UpdateEvalMission(context.Context, *UpdateEvalMissionsReq) (*UpdateEvalMissionsRsp, error)
	DeleteEvalMissions(context.Context, *DeleteEvalMissionsReq) (*DeleteEvalMissionsRsp, error)
	StartEvalMissions(context.Context, *StartEvalMissionsReq) (*StartEvalMissionsRsp, error)
	StopEvalMissions(context.Context, *StopEvalMissionsReq) (*StopEvalMissionsRsp, error)
	GetEvalMissionResult(context.Context, *GetEvalMissionResultReq) (*GetEvalMissionResultRsp, error)
	mustEmbedUnimplementedEvalMissionMgrServer()
}

// UnimplementedEvalMissionMgrServer must be embedded to have forward compatible implementations.
type UnimplementedEvalMissionMgrServer struct {
}

func (UnimplementedEvalMissionMgrServer) ReadEvalMissions(context.Context, *ReadEvalMissionsReq) (*ReadEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadEvalMissions not implemented")
}
func (UnimplementedEvalMissionMgrServer) CreateEvalMission(context.Context, *CreateEvalMissionsReq) (*CreateEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvalMission not implemented")
}
func (UnimplementedEvalMissionMgrServer) UpdateEvalMission(context.Context, *UpdateEvalMissionsReq) (*UpdateEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvalMission not implemented")
}
func (UnimplementedEvalMissionMgrServer) DeleteEvalMissions(context.Context, *DeleteEvalMissionsReq) (*DeleteEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvalMissions not implemented")
}
func (UnimplementedEvalMissionMgrServer) StartEvalMissions(context.Context, *StartEvalMissionsReq) (*StartEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartEvalMissions not implemented")
}
func (UnimplementedEvalMissionMgrServer) StopEvalMissions(context.Context, *StopEvalMissionsReq) (*StopEvalMissionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopEvalMissions not implemented")
}
func (UnimplementedEvalMissionMgrServer) GetEvalMissionResult(context.Context, *GetEvalMissionResultReq) (*GetEvalMissionResultRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvalMissionResult not implemented")
}
func (UnimplementedEvalMissionMgrServer) mustEmbedUnimplementedEvalMissionMgrServer() {}

// UnsafeEvalMissionMgrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvalMissionMgrServer will
// result in compilation errors.
type UnsafeEvalMissionMgrServer interface {
	mustEmbedUnimplementedEvalMissionMgrServer()
}

func RegisterEvalMissionMgrServer(s grpc.ServiceRegistrar, srv EvalMissionMgrServer) {
	s.RegisterService(&EvalMissionMgr_ServiceDesc, srv)
}

func _EvalMissionMgr_ReadEvalMissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).ReadEvalMissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_ReadEvalMissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).ReadEvalMissions(ctx, req.(*ReadEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_CreateEvalMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).CreateEvalMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_CreateEvalMission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).CreateEvalMission(ctx, req.(*CreateEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_UpdateEvalMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).UpdateEvalMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_UpdateEvalMission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).UpdateEvalMission(ctx, req.(*UpdateEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_DeleteEvalMissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).DeleteEvalMissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_DeleteEvalMissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).DeleteEvalMissions(ctx, req.(*DeleteEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_StartEvalMissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).StartEvalMissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_StartEvalMissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).StartEvalMissions(ctx, req.(*StartEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_StopEvalMissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopEvalMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).StopEvalMissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_StopEvalMissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).StopEvalMissions(ctx, req.(*StopEvalMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvalMissionMgr_GetEvalMissionResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvalMissionResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvalMissionMgrServer).GetEvalMissionResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvalMissionMgr_GetEvalMissionResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvalMissionMgrServer).GetEvalMissionResult(ctx, req.(*GetEvalMissionResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

// EvalMissionMgr_ServiceDesc is the grpc.ServiceDesc for EvalMissionMgr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvalMissionMgr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.EvalMissionMgr",
	HandlerType: (*EvalMissionMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadEvalMissions",
			Handler:    _EvalMissionMgr_ReadEvalMissions_Handler,
		},
		{
			MethodName: "CreateEvalMission",
			Handler:    _EvalMissionMgr_CreateEvalMission_Handler,
		},
		{
			MethodName: "UpdateEvalMission",
			Handler:    _EvalMissionMgr_UpdateEvalMission_Handler,
		},
		{
			MethodName: "DeleteEvalMissions",
			Handler:    _EvalMissionMgr_DeleteEvalMissions_Handler,
		},
		{
			MethodName: "StartEvalMissions",
			Handler:    _EvalMissionMgr_StartEvalMissions_Handler,
		},
		{
			MethodName: "StopEvalMissions",
			Handler:    _EvalMissionMgr_StopEvalMissions_Handler,
		},
		{
			MethodName: "GetEvalMissionResult",
			Handler:    _EvalMissionMgr_GetEvalMissionResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_eval_mission.proto",
}
