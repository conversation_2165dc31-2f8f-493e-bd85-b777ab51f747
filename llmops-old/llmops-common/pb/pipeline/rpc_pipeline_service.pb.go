// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/pipeline/rpc_pipeline_service.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreatePipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreatePipelineBody *CreatePipelineBody `protobuf:"bytes,1,opt,name=CreatePipelineBody,proto3" json:"CreatePipelineBody"`
	ProjectId          string              `protobuf:"bytes,3,opt,name=projectId,proto3" json:"projectId"`
}

func (x *CreatePipelineReq) Reset() {
	*x = CreatePipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineReq) ProtoMessage() {}

func (x *CreatePipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineReq.ProtoReflect.Descriptor instead.
func (*CreatePipelineReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePipelineReq) GetCreatePipelineBody() *CreatePipelineBody {
	if x != nil {
		return x.CreatePipelineBody
	}
	return nil
}

func (x *CreatePipelineReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type CreatePipelineBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"工作流名称"`
	 
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc" description:"工作流描述"`
}

func (x *CreatePipelineBody) Reset() {
	*x = CreatePipelineBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineBody) ProtoMessage() {}

func (x *CreatePipelineBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineBody.ProtoReflect.Descriptor instead.
func (*CreatePipelineBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePipelineBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePipelineBody) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type CreatePipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreatePipelineVersionBody *CreatePipelineVersionBody `protobuf:"bytes,1,opt,name=create_pipeline_version_body,json=createPipelineVersionBody,proto3" json:"create_pipeline_version_body"`
}

func (x *CreatePipelineVersionReq) Reset() {
	*x = CreatePipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineVersionReq) ProtoMessage() {}

func (x *CreatePipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineVersionReq.ProtoReflect.Descriptor instead.
func (*CreatePipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePipelineVersionReq) GetCreatePipelineVersionBody() *CreatePipelineVersionBody {
	if x != nil {
		return x.CreatePipelineVersionBody
	}
	return nil
}

type PipelineVersionId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PipelineId string `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"工作流id"`
	 
	PipelineVersionId string `protobuf:"bytes,1,opt,name=pipeline_version_id,json=pipelineVersionId,proto3" json:"pipeline_version_id" description:"工作流版本id"`
}

func (x *PipelineVersionId) Reset() {
	*x = PipelineVersionId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineVersionId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineVersionId) ProtoMessage() {}

func (x *PipelineVersionId) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineVersionId.ProtoReflect.Descriptor instead.
func (*PipelineVersionId) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{3}
}

func (x *PipelineVersionId) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineVersionId) GetPipelineVersionId() string {
	if x != nil {
		return x.PipelineVersionId
	}
	return ""
}

type CreatePipelineVersionBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"所属工作流id"`
	 
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc" description:"工作流版本名称"`
	 
	PipelineId string `protobuf:"bytes,3,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"工作流版本描述"`
}

func (x *CreatePipelineVersionBody) Reset() {
	*x = CreatePipelineVersionBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineVersionBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineVersionBody) ProtoMessage() {}

func (x *CreatePipelineVersionBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineVersionBody.ProtoReflect.Descriptor instead.
func (*CreatePipelineVersionBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePipelineVersionBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePipelineVersionBody) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreatePipelineVersionBody) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

type SavePipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineVersionId       string                   `protobuf:"bytes,2,opt,name=pipeline_version_id,json=pipelineVersionId,proto3" json:"pipeline_version_id"`
	SavePipelineVersionBody *SavePipelineVersionBody `protobuf:"bytes,3,opt,name=save_pipeline_version_body,json=savePipelineVersionBody,proto3" json:"save_pipeline_version_body"`
}

func (x *SavePipelineVersionReq) Reset() {
	*x = SavePipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavePipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavePipelineVersionReq) ProtoMessage() {}

func (x *SavePipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavePipelineVersionReq.ProtoReflect.Descriptor instead.
func (*SavePipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{5}
}

func (x *SavePipelineVersionReq) GetPipelineVersionId() string {
	if x != nil {
		return x.PipelineVersionId
	}
	return ""
}

func (x *SavePipelineVersionReq) GetSavePipelineVersionBody() *SavePipelineVersionBody {
	if x != nil {
		return x.SavePipelineVersionBody
	}
	return nil
}

type SavePipelineVersionBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	 
	TaskFlow *TaskFlow `protobuf:"bytes,1,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"工作流流程图"`
	 
	Style PipelineVersion_SchedulingStyle `protobuf:"varint,6,opt,name=style,proto3,enum=pipeline.PipelineVersion_SchedulingStyle" json:"style" description:"调度类型 0:手动调度 1:定时调度"`
	 
	TimingConfig *TimingConfig `protobuf:"bytes,7,opt,name=timing_config,json=timingConfig,proto3" json:"timing_config" description:"定时调度配置"`
}

func (x *SavePipelineVersionBody) Reset() {
	*x = SavePipelineVersionBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavePipelineVersionBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavePipelineVersionBody) ProtoMessage() {}

func (x *SavePipelineVersionBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavePipelineVersionBody.ProtoReflect.Descriptor instead.
func (*SavePipelineVersionBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{6}
}

func (x *SavePipelineVersionBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SavePipelineVersionBody) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SavePipelineVersionBody) GetTaskFlow() *TaskFlow {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *SavePipelineVersionBody) GetStyle() PipelineVersion_SchedulingStyle {
	if x != nil {
		return x.Style
	}
	return PipelineVersion_IMMEDIATE
}

func (x *SavePipelineVersionBody) GetTimingConfig() *TimingConfig {
	if x != nil {
		return x.TimingConfig
	}
	return nil
}

type SavePipelineVersionReqV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineVersionId       string                     `protobuf:"bytes,1,opt,name=pipeline_version_id,json=pipelineVersionId,proto3" json:"pipeline_version_id"`
	SavePipelineVersionBody *SavePipelineVersionBodyV2 `protobuf:"bytes,2,opt,name=save_pipeline_version_body,json=savePipelineVersionBody,proto3" json:"save_pipeline_version_body"`
}

func (x *SavePipelineVersionReqV2) Reset() {
	*x = SavePipelineVersionReqV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavePipelineVersionReqV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavePipelineVersionReqV2) ProtoMessage() {}

func (x *SavePipelineVersionReqV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavePipelineVersionReqV2.ProtoReflect.Descriptor instead.
func (*SavePipelineVersionReqV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{7}
}

func (x *SavePipelineVersionReqV2) GetPipelineVersionId() string {
	if x != nil {
		return x.PipelineVersionId
	}
	return ""
}

func (x *SavePipelineVersionReqV2) GetSavePipelineVersionBody() *SavePipelineVersionBodyV2 {
	if x != nil {
		return x.SavePipelineVersionBody
	}
	return nil
}

type SavePipelineVersionBodyV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	 
	TaskFlow *TaskFlowV2 `protobuf:"bytes,1,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"工作流流程图"`
	 
	Style PipelineVersion_SchedulingStyle `protobuf:"varint,6,opt,name=style,proto3,enum=pipeline.PipelineVersion_SchedulingStyle" json:"style" description:"调度类型 0:手动调度 1:定时调度"`
	 
	TimingConfig *TimingConfig `protobuf:"bytes,7,opt,name=timing_config,json=timingConfig,proto3" json:"timing_config" description:"定时调度配置"`
}

func (x *SavePipelineVersionBodyV2) Reset() {
	*x = SavePipelineVersionBodyV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavePipelineVersionBodyV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavePipelineVersionBodyV2) ProtoMessage() {}

func (x *SavePipelineVersionBodyV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavePipelineVersionBodyV2.ProtoReflect.Descriptor instead.
func (*SavePipelineVersionBodyV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{8}
}

func (x *SavePipelineVersionBodyV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SavePipelineVersionBodyV2) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SavePipelineVersionBodyV2) GetTaskFlow() *TaskFlowV2 {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *SavePipelineVersionBodyV2) GetStyle() PipelineVersion_SchedulingStyle {
	if x != nil {
		return x.Style
	}
	return PipelineVersion_IMMEDIATE
}

func (x *SavePipelineVersionBodyV2) GetTimingConfig() *TimingConfig {
	if x != nil {
		return x.TimingConfig
	}
	return nil
}

type ModifyPipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string              `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	ModifyPipelineBody *ModifyPipelineBody `protobuf:"bytes,2,opt,name=modify_pipeline_body,json=modifyPipelineBody,proto3" json:"modify_pipeline_body"`
}

func (x *ModifyPipelineReq) Reset() {
	*x = ModifyPipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPipelineReq) ProtoMessage() {}

func (x *ModifyPipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPipelineReq.ProtoReflect.Descriptor instead.
func (*ModifyPipelineReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{9}
}

func (x *ModifyPipelineReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyPipelineReq) GetModifyPipelineBody() *ModifyPipelineBody {
	if x != nil {
		return x.ModifyPipelineBody
	}
	return nil
}

type ModifyPipelineBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"工作流名称"`
	 
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc" description:"工作流描述"`
}

func (x *ModifyPipelineBody) Reset() {
	*x = ModifyPipelineBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPipelineBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPipelineBody) ProtoMessage() {}

func (x *ModifyPipelineBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPipelineBody.ProtoReflect.Descriptor instead.
func (*ModifyPipelineBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{10}
}

func (x *ModifyPipelineBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModifyPipelineBody) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type ListPipelinesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id"`
}

func (x *ListPipelinesReq) Reset() {
	*x = ListPipelinesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPipelinesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPipelinesReq) ProtoMessage() {}

func (x *ListPipelinesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPipelinesReq.ProtoReflect.Descriptor instead.
func (*ListPipelinesReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListPipelinesReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type PipelineId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PipelineId string `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"创建pipeline id"`
}

func (x *PipelineId) Reset() {
	*x = PipelineId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineId) ProtoMessage() {}

func (x *PipelineId) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineId.ProtoReflect.Descriptor instead.
func (*PipelineId) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{12}
}

func (x *PipelineId) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

type PipelinePage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"工作流总数"`
	 
	Pipelines []*Pipeline `protobuf:"bytes,2,rep,name=pipelines,proto3" json:"pipelines" description:"工作流列表"`
}

func (x *PipelinePage) Reset() {
	*x = PipelinePage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelinePage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelinePage) ProtoMessage() {}

func (x *PipelinePage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelinePage.ProtoReflect.Descriptor instead.
func (*PipelinePage) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{13}
}

func (x *PipelinePage) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PipelinePage) GetPipelines() []*Pipeline {
	if x != nil {
		return x.Pipelines
	}
	return nil
}

type GetPipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *GetPipelineReq) Reset() {
	*x = GetPipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineReq) ProtoMessage() {}

func (x *GetPipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineReq.ProtoReflect.Descriptor instead.
func (*GetPipelineReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetPipelineReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListPipelineVersionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineId string `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id"`
}

func (x *ListPipelineVersionsReq) Reset() {
	*x = ListPipelineVersionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPipelineVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPipelineVersionsReq) ProtoMessage() {}

func (x *ListPipelineVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPipelineVersionsReq.ProtoReflect.Descriptor instead.
func (*ListPipelineVersionsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListPipelineVersionsReq) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

type PipelineVersionPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"版本总数"`
	 
	PipelineVersions []*PipelineVersion `protobuf:"bytes,2,rep,name=pipelineVersions,proto3" json:"pipelineVersions" description:"版本列表"`
}

func (x *PipelineVersionPage) Reset() {
	*x = PipelineVersionPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineVersionPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineVersionPage) ProtoMessage() {}

func (x *PipelineVersionPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineVersionPage.ProtoReflect.Descriptor instead.
func (*PipelineVersionPage) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{16}
}

func (x *PipelineVersionPage) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PipelineVersionPage) GetPipelineVersions() []*PipelineVersion {
	if x != nil {
		return x.PipelineVersions
	}
	return nil
}

type DeletePipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DeletePipelineReq) Reset() {
	*x = DeletePipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePipelineReq) ProtoMessage() {}

func (x *DeletePipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePipelineReq.ProtoReflect.Descriptor instead.
func (*DeletePipelineReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{17}
}

func (x *DeletePipelineReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeletePipelinesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids" description:"删除工作流id集合"`
}

func (x *DeletePipelinesReq) Reset() {
	*x = DeletePipelinesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePipelinesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePipelinesReq) ProtoMessage() {}

func (x *DeletePipelinesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePipelinesReq.ProtoReflect.Descriptor instead.
func (*DeletePipelinesReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{18}
}

func (x *DeletePipelinesReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeletePipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DeletePipelineVersionReq) Reset() {
	*x = DeletePipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePipelineVersionReq) ProtoMessage() {}

func (x *DeletePipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePipelineVersionReq.ProtoReflect.Descriptor instead.
func (*DeletePipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{19}
}

func (x *DeletePipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeletePipelineVersionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids" description:"删除工作流版本id集合"`
}

func (x *DeletePipelineVersionsReq) Reset() {
	*x = DeletePipelineVersionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePipelineVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePipelineVersionsReq) ProtoMessage() {}

func (x *DeletePipelineVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePipelineVersionsReq.ProtoReflect.Descriptor instead.
func (*DeletePipelineVersionsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{20}
}

func (x *DeletePipelineVersionsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ExportPipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *ExportPipelineVersionReq) Reset() {
	*x = ExportPipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportPipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPipelineVersionReq) ProtoMessage() {}

func (x *ExportPipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPipelineVersionReq.ProtoReflect.Descriptor instead.
func (*ExportPipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{21}
}

func (x *ExportPipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ExportPipelineVersionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineVersionFile []byte `protobuf:"bytes,1,opt,name=pipelineVersionFile,proto3" json:"pipelineVersionFile"`
	FileName            string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName"`
}

func (x *ExportPipelineVersionRsp) Reset() {
	*x = ExportPipelineVersionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportPipelineVersionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPipelineVersionRsp) ProtoMessage() {}

func (x *ExportPipelineVersionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPipelineVersionRsp.ProtoReflect.Descriptor instead.
func (*ExportPipelineVersionRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{22}
}

func (x *ExportPipelineVersionRsp) GetPipelineVersionFile() []byte {
	if x != nil {
		return x.PipelineVersionFile
	}
	return nil
}

func (x *ExportPipelineVersionRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type CreatePipelineVersionByYamlReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreatePipelineVersionByYamlBody *CreatePipelineVersionByYamlBody `protobuf:"bytes,2,opt,name=create_pipeline_version_by_yaml_body,json=createPipelineVersionByYamlBody,proto3" json:"create_pipeline_version_by_yaml_body"`
}

func (x *CreatePipelineVersionByYamlReq) Reset() {
	*x = CreatePipelineVersionByYamlReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineVersionByYamlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineVersionByYamlReq) ProtoMessage() {}

func (x *CreatePipelineVersionByYamlReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineVersionByYamlReq.ProtoReflect.Descriptor instead.
func (*CreatePipelineVersionByYamlReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{23}
}

func (x *CreatePipelineVersionByYamlReq) GetCreatePipelineVersionByYamlBody() *CreatePipelineVersionByYamlBody {
	if x != nil {
		return x.CreatePipelineVersionByYamlBody
	}
	return nil
}

type CreatePipelineVersionByYamlBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"工作流名称"`
	 
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc" description:"工作流描述"`
	 
	Yaml string `protobuf:"bytes,3,opt,name=yaml,proto3" json:"yaml" description:"yaml文件内容"`
	 
	PipelineId string `protobuf:"bytes,4,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"所属pipeline id"`
}

func (x *CreatePipelineVersionByYamlBody) Reset() {
	*x = CreatePipelineVersionByYamlBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePipelineVersionByYamlBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePipelineVersionByYamlBody) ProtoMessage() {}

func (x *CreatePipelineVersionByYamlBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePipelineVersionByYamlBody.ProtoReflect.Descriptor instead.
func (*CreatePipelineVersionByYamlBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{24}
}

func (x *CreatePipelineVersionByYamlBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePipelineVersionByYamlBody) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreatePipelineVersionByYamlBody) GetYaml() string {
	if x != nil {
		return x.Yaml
	}
	return ""
}

func (x *CreatePipelineVersionByYamlBody) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

type CheckPipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body      *CheckPipelineBody `protobuf:"bytes,1,opt,name=body,proto3" json:"body"`
	ProjectId string             `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id"`
}

func (x *CheckPipelineReq) Reset() {
	*x = CheckPipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineReq) ProtoMessage() {}

func (x *CheckPipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineReq.ProtoReflect.Descriptor instead.
func (*CheckPipelineReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{25}
}

func (x *CheckPipelineReq) GetBody() *CheckPipelineBody {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *CheckPipelineReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type CheckPipelineBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
}

func (x *CheckPipelineBody) Reset() {
	*x = CheckPipelineBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineBody) ProtoMessage() {}

func (x *CheckPipelineBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineBody.ProtoReflect.Descriptor instead.
func (*CheckPipelineBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{26}
}

func (x *CheckPipelineBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckPipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body *CheckPipelineVersionBody `protobuf:"bytes,1,opt,name=body,proto3" json:"body"`
}

func (x *CheckPipelineVersionReq) Reset() {
	*x = CheckPipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineVersionReq) ProtoMessage() {}

func (x *CheckPipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineVersionReq.ProtoReflect.Descriptor instead.
func (*CheckPipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{27}
}

func (x *CheckPipelineVersionReq) GetBody() *CheckPipelineVersionBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type CheckPipelineVersionBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	PipelineId string `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id"`
}

func (x *CheckPipelineVersionBody) Reset() {
	*x = CheckPipelineVersionBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineVersionBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineVersionBody) ProtoMessage() {}

func (x *CheckPipelineVersionBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineVersionBody.ProtoReflect.Descriptor instead.
func (*CheckPipelineVersionBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{28}
}

func (x *CheckPipelineVersionBody) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckPipelineVersionBody) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

type CheckPipelineRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckPipelineRsp) Reset() {
	*x = CheckPipelineRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineRsp) ProtoMessage() {}

func (x *CheckPipelineRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineRsp.ProtoReflect.Descriptor instead.
func (*CheckPipelineRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{29}
}

type CheckPipelineVersionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckPipelineVersionRsp) Reset() {
	*x = CheckPipelineVersionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPipelineVersionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPipelineVersionRsp) ProtoMessage() {}

func (x *CheckPipelineVersionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPipelineVersionRsp.ProtoReflect.Descriptor instead.
func (*CheckPipelineVersionRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{30}
}

type GetComponentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetComponentsReq) Reset() {
	*x = GetComponentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetComponentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetComponentsReq) ProtoMessage() {}

func (x *GetComponentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetComponentsReq.ProtoReflect.Descriptor instead.
func (*GetComponentsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{31}
}

type ComponentPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"组件数量"`
	 
	Components []*Component `protobuf:"bytes,2,rep,name=components,proto3" json:"components" description:"组件列表"`
}

func (x *ComponentPage) Reset() {
	*x = ComponentPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentPage) ProtoMessage() {}

func (x *ComponentPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentPage.ProtoReflect.Descriptor instead.
func (*ComponentPage) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{32}
}

func (x *ComponentPage) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ComponentPage) GetComponents() []*Component {
	if x != nil {
		return x.Components
	}
	return nil
}

type StartPipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *StartPipelineVersionReq) Reset() {
	*x = StartPipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPipelineVersionReq) ProtoMessage() {}

func (x *StartPipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPipelineVersionReq.ProtoReflect.Descriptor instead.
func (*StartPipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{33}
}

func (x *StartPipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type StopPipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *StopPipelineVersionReq) Reset() {
	*x = StopPipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopPipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopPipelineVersionReq) ProtoMessage() {}

func (x *StopPipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopPipelineVersionReq.ProtoReflect.Descriptor instead.
func (*StopPipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{34}
}

func (x *StopPipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type OncePipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *OncePipelineVersionReq) Reset() {
	*x = OncePipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OncePipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OncePipelineVersionReq) ProtoMessage() {}

func (x *OncePipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OncePipelineVersionReq.ProtoReflect.Descriptor instead.
func (*OncePipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{35}
}

func (x *OncePipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetPipelineVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *GetPipelineVersionReq) Reset() {
	*x = GetPipelineVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineVersionReq) ProtoMessage() {}

func (x *GetPipelineVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineVersionReq.ProtoReflect.Descriptor instead.
func (*GetPipelineVersionReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetPipelineVersionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type OncePipelineVersionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	RunId string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id" description:"生成run id"`
}

func (x *OncePipelineVersionRsp) Reset() {
	*x = OncePipelineVersionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OncePipelineVersionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OncePipelineVersionRsp) ProtoMessage() {}

func (x *OncePipelineVersionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OncePipelineVersionRsp.ProtoReflect.Descriptor instead.
func (*OncePipelineVersionRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP(), []int{37}
}

func (x *OncePipelineVersionRsp) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

var File_proto_pipeline_rpc_pipeline_service_proto protoreflect.FileDescriptor

var file_proto_pipeline_rpc_pipeline_service_proto_rawDesc = []byte{
	0x0a, 0x29, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2f, 0x72, 0x70, 0x63, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2f, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x4c, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x12, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x80, 0x01, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x64, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64,
	0x79, 0x52, 0x19, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x64, 0x0a, 0x11,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x64, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x16, 0x53, 0x61, 0x76,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x1a, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x17, 0x73, 0x61, 0x76, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x6f, 0x64, 0x79, 0x22, 0xf0, 0x01, 0x0a, 0x17, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x74, 0x69, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xac, 0x01, 0x0a, 0x18, 0x53, 0x61, 0x76, 0x65, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x56, 0x32, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x1a, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x56, 0x32, 0x52, 0x17, 0x73, 0x61,
	0x76, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x6f, 0x64, 0x79, 0x22, 0xf4, 0x01, 0x0a, 0x19, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64,
	0x79, 0x56, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x56, 0x32, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x3f,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x3b, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c,
	0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x73, 0x0a, 0x11,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x4e, 0x0a, 0x14, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x12, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64,
	0x79, 0x22, 0x3c, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22,
	0x31, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x22, 0x2d, 0x0a, 0x0a, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x22, 0x54, 0x0a, 0x0c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x09, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x20, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3a, 0x0a, 0x17, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x13, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x45, 0x0a, 0x10, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x23, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x26, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x22, 0x2a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x2d, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x2a, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x68, 0x0a, 0x18, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9a, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x79, 0x59, 0x61, 0x6d, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x78, 0x0a, 0x24, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x79, 0x61, 0x6d, 0x6c, 0x5f, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x59, 0x61, 0x6d, 0x6c, 0x42, 0x6f, 0x64,
	0x79, 0x52, 0x1f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x59, 0x61, 0x6d, 0x6c, 0x42, 0x6f,
	0x64, 0x79, 0x22, 0x7e, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x59, 0x61, 0x6d,
	0x6c, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a,
	0x04, 0x79, 0x61, 0x6d, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x79, 0x61, 0x6d,
	0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x22, 0x62, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64,
	0x79, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x51, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x22, 0x4f, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x22, 0x58, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x29, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x28, 0x0a, 0x16, 0x53,
	0x74, 0x6f, 0x70, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x28, 0x0a, 0x16, 0x4f, 0x6e, 0x63, 0x65, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x16, 0x4f, 0x6e, 0x63, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x32, 0xa6, 0x0e, 0x0a, 0x0f, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a,
	0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12,
	0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x54, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x32,
	0x12, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x56, 0x32, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x50,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x54, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x41, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x0f, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x1c, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4f,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x43, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x58, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x67, 0x65, 0x12, 0x51,
	0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x56, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x13, 0x53, 0x74, 0x6f,
	0x70, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x70,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x59, 0x0a, 0x13, 0x4f, 0x6e, 0x63, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x4f, 0x6e, 0x63, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x4f, 0x6e, 0x63, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x64, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x42, 0x79, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x28, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x59, 0x61, 0x6d, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x5f, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x12, 0x44, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x12, 0x66, 0x0a, 0x1e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x21, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x42, 0x35, 0x5a, 0x33, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x3b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_pipeline_rpc_pipeline_service_proto_rawDescOnce sync.Once
	file_proto_pipeline_rpc_pipeline_service_proto_rawDescData = file_proto_pipeline_rpc_pipeline_service_proto_rawDesc
)

func file_proto_pipeline_rpc_pipeline_service_proto_rawDescGZIP() []byte {
	file_proto_pipeline_rpc_pipeline_service_proto_rawDescOnce.Do(func() {
		file_proto_pipeline_rpc_pipeline_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pipeline_rpc_pipeline_service_proto_rawDescData)
	})
	return file_proto_pipeline_rpc_pipeline_service_proto_rawDescData
}

var file_proto_pipeline_rpc_pipeline_service_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_proto_pipeline_rpc_pipeline_service_proto_goTypes = []interface{}{
	(*CreatePipelineReq)(nil),               // 0: pipeline.CreatePipelineReq
	(*CreatePipelineBody)(nil),              // 1: pipeline.CreatePipelineBody
	(*CreatePipelineVersionReq)(nil),        // 2: pipeline.CreatePipelineVersionReq
	(*PipelineVersionId)(nil),               // 3: pipeline.PipelineVersionId
	(*CreatePipelineVersionBody)(nil),       // 4: pipeline.CreatePipelineVersionBody
	(*SavePipelineVersionReq)(nil),          // 5: pipeline.SavePipelineVersionReq
	(*SavePipelineVersionBody)(nil),         // 6: pipeline.SavePipelineVersionBody
	(*SavePipelineVersionReqV2)(nil),        // 7: pipeline.SavePipelineVersionReqV2
	(*SavePipelineVersionBodyV2)(nil),       // 8: pipeline.SavePipelineVersionBodyV2
	(*ModifyPipelineReq)(nil),               // 9: pipeline.ModifyPipelineReq
	(*ModifyPipelineBody)(nil),              // 10: pipeline.ModifyPipelineBody
	(*ListPipelinesReq)(nil),                // 11: pipeline.ListPipelinesReq
	(*PipelineId)(nil),                      // 12: pipeline.PipelineId
	(*PipelinePage)(nil),                    // 13: pipeline.PipelinePage
	(*GetPipelineReq)(nil),                  // 14: pipeline.GetPipelineReq
	(*ListPipelineVersionsReq)(nil),         // 15: pipeline.ListPipelineVersionsReq
	(*PipelineVersionPage)(nil),             // 16: pipeline.PipelineVersionPage
	(*DeletePipelineReq)(nil),               // 17: pipeline.DeletePipelineReq
	(*DeletePipelinesReq)(nil),              // 18: pipeline.DeletePipelinesReq
	(*DeletePipelineVersionReq)(nil),        // 19: pipeline.DeletePipelineVersionReq
	(*DeletePipelineVersionsReq)(nil),       // 20: pipeline.DeletePipelineVersionsReq
	(*ExportPipelineVersionReq)(nil),        // 21: pipeline.ExportPipelineVersionReq
	(*ExportPipelineVersionRsp)(nil),        // 22: pipeline.ExportPipelineVersionRsp
	(*CreatePipelineVersionByYamlReq)(nil),  // 23: pipeline.CreatePipelineVersionByYamlReq
	(*CreatePipelineVersionByYamlBody)(nil), // 24: pipeline.CreatePipelineVersionByYamlBody
	(*CheckPipelineReq)(nil),                // 25: pipeline.CheckPipelineReq
	(*CheckPipelineBody)(nil),               // 26: pipeline.CheckPipelineBody
	(*CheckPipelineVersionReq)(nil),         // 27: pipeline.CheckPipelineVersionReq
	(*CheckPipelineVersionBody)(nil),        // 28: pipeline.CheckPipelineVersionBody
	(*CheckPipelineRsp)(nil),                // 29: pipeline.CheckPipelineRsp
	(*CheckPipelineVersionRsp)(nil),         // 30: pipeline.CheckPipelineVersionRsp
	(*GetComponentsReq)(nil),                // 31: pipeline.GetComponentsReq
	(*ComponentPage)(nil),                   // 32: pipeline.ComponentPage
	(*StartPipelineVersionReq)(nil),         // 33: pipeline.StartPipelineVersionReq
	(*StopPipelineVersionReq)(nil),          // 34: pipeline.StopPipelineVersionReq
	(*OncePipelineVersionReq)(nil),          // 35: pipeline.OncePipelineVersionReq
	(*GetPipelineVersionReq)(nil),           // 36: pipeline.GetPipelineVersionReq
	(*OncePipelineVersionRsp)(nil),          // 37: pipeline.OncePipelineVersionRsp
	(*TaskFlow)(nil),                        // 38: pipeline.TaskFlow
	(PipelineVersion_SchedulingStyle)(0),    // 39: pipeline.PipelineVersion.SchedulingStyle
	(*TimingConfig)(nil),                    // 40: pipeline.TimingConfig
	(*TaskFlowV2)(nil),                      // 41: pipeline.TaskFlowV2
	(*Pipeline)(nil),                        // 42: pipeline.Pipeline
	(*PipelineVersion)(nil),                 // 43: pipeline.PipelineVersion
	(*Component)(nil),                       // 44: pipeline.Component
	(*PipelineVersionV2)(nil),               // 45: pipeline.PipelineVersionV2
	(*common.DeleteRsp)(nil),                // 46: commons.DeleteRsp
}
var file_proto_pipeline_rpc_pipeline_service_proto_depIdxs = []int32{
	1,  // 0: pipeline.CreatePipelineReq.CreatePipelineBody:type_name -> pipeline.CreatePipelineBody
	4,  // 1: pipeline.CreatePipelineVersionReq.create_pipeline_version_body:type_name -> pipeline.CreatePipelineVersionBody
	6,  // 2: pipeline.SavePipelineVersionReq.save_pipeline_version_body:type_name -> pipeline.SavePipelineVersionBody
	38, // 3: pipeline.SavePipelineVersionBody.task_flow:type_name -> pipeline.TaskFlow
	39, // 4: pipeline.SavePipelineVersionBody.style:type_name -> pipeline.PipelineVersion.SchedulingStyle
	40, // 5: pipeline.SavePipelineVersionBody.timing_config:type_name -> pipeline.TimingConfig
	8,  // 6: pipeline.SavePipelineVersionReqV2.save_pipeline_version_body:type_name -> pipeline.SavePipelineVersionBodyV2
	41, // 7: pipeline.SavePipelineVersionBodyV2.task_flow:type_name -> pipeline.TaskFlowV2
	39, // 8: pipeline.SavePipelineVersionBodyV2.style:type_name -> pipeline.PipelineVersion.SchedulingStyle
	40, // 9: pipeline.SavePipelineVersionBodyV2.timing_config:type_name -> pipeline.TimingConfig
	10, // 10: pipeline.ModifyPipelineReq.modify_pipeline_body:type_name -> pipeline.ModifyPipelineBody
	42, // 11: pipeline.PipelinePage.pipelines:type_name -> pipeline.Pipeline
	43, // 12: pipeline.PipelineVersionPage.pipelineVersions:type_name -> pipeline.PipelineVersion
	24, // 13: pipeline.CreatePipelineVersionByYamlReq.create_pipeline_version_by_yaml_body:type_name -> pipeline.CreatePipelineVersionByYamlBody
	26, // 14: pipeline.CheckPipelineReq.body:type_name -> pipeline.CheckPipelineBody
	28, // 15: pipeline.CheckPipelineVersionReq.body:type_name -> pipeline.CheckPipelineVersionBody
	44, // 16: pipeline.ComponentPage.components:type_name -> pipeline.Component
	0,  // 17: pipeline.PipelineService.CreatePipeline:input_type -> pipeline.CreatePipelineReq
	9,  // 18: pipeline.PipelineService.ModifyPipeline:input_type -> pipeline.ModifyPipelineReq
	2,  // 19: pipeline.PipelineService.CreatePipelineVersion:input_type -> pipeline.CreatePipelineVersionReq
	5,  // 20: pipeline.PipelineService.SavePipelineVersion:input_type -> pipeline.SavePipelineVersionReq
	7,  // 21: pipeline.PipelineService.SavePipelineVersionV2:input_type -> pipeline.SavePipelineVersionReqV2
	14, // 22: pipeline.PipelineService.GetPipeline:input_type -> pipeline.GetPipelineReq
	36, // 23: pipeline.PipelineService.GetPipelineVersion:input_type -> pipeline.GetPipelineVersionReq
	36, // 24: pipeline.PipelineService.GetPipelineVersionV2:input_type -> pipeline.GetPipelineVersionReq
	17, // 25: pipeline.PipelineService.DeletePipeline:input_type -> pipeline.DeletePipelineReq
	18, // 26: pipeline.PipelineService.DeletePipelines:input_type -> pipeline.DeletePipelinesReq
	19, // 27: pipeline.PipelineService.DeletePipelineVersion:input_type -> pipeline.DeletePipelineVersionReq
	11, // 28: pipeline.PipelineService.ListPipelines:input_type -> pipeline.ListPipelinesReq
	15, // 29: pipeline.PipelineService.ListPipelineVersions:input_type -> pipeline.ListPipelineVersionsReq
	20, // 30: pipeline.PipelineService.DeletePipelineVersions:input_type -> pipeline.DeletePipelineVersionsReq
	33, // 31: pipeline.PipelineService.StartPipelineVersion:input_type -> pipeline.StartPipelineVersionReq
	34, // 32: pipeline.PipelineService.StopPipelineVersion:input_type -> pipeline.StopPipelineVersionReq
	35, // 33: pipeline.PipelineService.OncePipelineVersion:input_type -> pipeline.OncePipelineVersionReq
	23, // 34: pipeline.PipelineService.CreatePipelineVersionByYaml:input_type -> pipeline.CreatePipelineVersionByYamlReq
	21, // 35: pipeline.PipelineService.ExportPipelineVersion:input_type -> pipeline.ExportPipelineVersionReq
	31, // 36: pipeline.PipelineService.GetComponents:input_type -> pipeline.GetComponentsReq
	25, // 37: pipeline.PipelineService.CheckPipelineNameUnique:input_type -> pipeline.CheckPipelineReq
	27, // 38: pipeline.PipelineService.CheckPipelineVersionNameUnique:input_type -> pipeline.CheckPipelineVersionReq
	12, // 39: pipeline.PipelineService.CreatePipeline:output_type -> pipeline.PipelineId
	12, // 40: pipeline.PipelineService.ModifyPipeline:output_type -> pipeline.PipelineId
	3,  // 41: pipeline.PipelineService.CreatePipelineVersion:output_type -> pipeline.PipelineVersionId
	3,  // 42: pipeline.PipelineService.SavePipelineVersion:output_type -> pipeline.PipelineVersionId
	3,  // 43: pipeline.PipelineService.SavePipelineVersionV2:output_type -> pipeline.PipelineVersionId
	42, // 44: pipeline.PipelineService.GetPipeline:output_type -> pipeline.Pipeline
	43, // 45: pipeline.PipelineService.GetPipelineVersion:output_type -> pipeline.PipelineVersion
	45, // 46: pipeline.PipelineService.GetPipelineVersionV2:output_type -> pipeline.PipelineVersionV2
	46, // 47: pipeline.PipelineService.DeletePipeline:output_type -> commons.DeleteRsp
	46, // 48: pipeline.PipelineService.DeletePipelines:output_type -> commons.DeleteRsp
	46, // 49: pipeline.PipelineService.DeletePipelineVersion:output_type -> commons.DeleteRsp
	13, // 50: pipeline.PipelineService.ListPipelines:output_type -> pipeline.PipelinePage
	16, // 51: pipeline.PipelineService.ListPipelineVersions:output_type -> pipeline.PipelineVersionPage
	46, // 52: pipeline.PipelineService.DeletePipelineVersions:output_type -> commons.DeleteRsp
	3,  // 53: pipeline.PipelineService.StartPipelineVersion:output_type -> pipeline.PipelineVersionId
	3,  // 54: pipeline.PipelineService.StopPipelineVersion:output_type -> pipeline.PipelineVersionId
	37, // 55: pipeline.PipelineService.OncePipelineVersion:output_type -> pipeline.OncePipelineVersionRsp
	3,  // 56: pipeline.PipelineService.CreatePipelineVersionByYaml:output_type -> pipeline.PipelineVersionId
	22, // 57: pipeline.PipelineService.ExportPipelineVersion:output_type -> pipeline.ExportPipelineVersionRsp
	32, // 58: pipeline.PipelineService.GetComponents:output_type -> pipeline.ComponentPage
	29, // 59: pipeline.PipelineService.CheckPipelineNameUnique:output_type -> pipeline.CheckPipelineRsp
	30, // 60: pipeline.PipelineService.CheckPipelineVersionNameUnique:output_type -> pipeline.CheckPipelineVersionRsp
	39, // [39:61] is the sub-list for method output_type
	17, // [17:39] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_proto_pipeline_rpc_pipeline_service_proto_init() }
func file_proto_pipeline_rpc_pipeline_service_proto_init() {
	if File_proto_pipeline_rpc_pipeline_service_proto != nil {
		return
	}
	file_proto_pipeline_pipeline_proto_init()
	file_proto_pipeline_run_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineVersionId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineVersionBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavePipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavePipelineVersionBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavePipelineVersionReqV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavePipelineVersionBodyV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPipelineBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPipelinesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelinePage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPipelineVersionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineVersionPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePipelinesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePipelineVersionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportPipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportPipelineVersionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineVersionByYamlReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePipelineVersionByYamlBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineVersionBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPipelineVersionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetComponentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComponentPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopPipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OncePipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_pipeline_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OncePipelineVersionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pipeline_rpc_pipeline_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_pipeline_rpc_pipeline_service_proto_goTypes,
		DependencyIndexes: file_proto_pipeline_rpc_pipeline_service_proto_depIdxs,
		MessageInfos:      file_proto_pipeline_rpc_pipeline_service_proto_msgTypes,
	}.Build()
	File_proto_pipeline_rpc_pipeline_service_proto = out.File
	file_proto_pipeline_rpc_pipeline_service_proto_rawDesc = nil
	file_proto_pipeline_rpc_pipeline_service_proto_goTypes = nil
	file_proto_pipeline_rpc_pipeline_service_proto_depIdxs = nil
}
