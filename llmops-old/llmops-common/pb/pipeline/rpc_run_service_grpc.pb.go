// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/pipeline/rpc_run_service.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "transwarp.io/aip/llmops-common/pb/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RunService_GetRunStepArtifact_FullMethodName = "/pipeline.RunService/GetRunStepArtifact"
	RunService_SubmitRun_FullMethodName          = "/pipeline.RunService/SubmitRun"
	RunService_SubmitRunV2_FullMethodName        = "/pipeline.RunService/SubmitRunV2"
	RunService_RetryRun_FullMethodName           = "/pipeline.RunService/RetryRun"
	RunService_TerminateRun_FullMethodName       = "/pipeline.RunService/TerminateRun"
	RunService_GetRun_FullMethodName             = "/pipeline.RunService/GetRun"
	RunService_GetRunV2_FullMethodName           = "/pipeline.RunService/GetRunV2"
	RunService_GetRunStepLogs_FullMethodName     = "/pipeline.RunService/GetRunStepLogs"
	RunService_GetRunStepPodInfo_FullMethodName  = "/pipeline.RunService/GetRunStepPodInfo"
	RunService_GetRunStepEvents_FullMethodName   = "/pipeline.RunService/GetRunStepEvents"
	RunService_GetEventsByLabels_FullMethodName  = "/pipeline.RunService/GetEventsByLabels"
	RunService_DeleteRun_FullMethodName          = "/pipeline.RunService/DeleteRun"
	RunService_DeleteRunBatch_FullMethodName     = "/pipeline.RunService/DeleteRunBatch"
	RunService_ListRuns_FullMethodName           = "/pipeline.RunService/ListRuns"
	RunService_ListRunsV2_FullMethodName         = "/pipeline.RunService/ListRunsV2"
)

// RunServiceClient is the client API for RunService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RunServiceClient interface {
	GetRunStepArtifact(ctx context.Context, in *GetRunStepArtifactReq, opts ...grpc.CallOption) (RunService_GetRunStepArtifactClient, error)
	// Deprecated use V2
	SubmitRun(ctx context.Context, in *SubmitRunReq, opts ...grpc.CallOption) (*RunId, error)
	SubmitRunV2(ctx context.Context, in *SubmitRunReqV2, opts ...grpc.CallOption) (*RunId, error)
	RetryRun(ctx context.Context, in *RetryRunReq, opts ...grpc.CallOption) (*common.EmptyRsp, error)
	TerminateRun(ctx context.Context, in *TerminateRunReq, opts ...grpc.CallOption) (*common.EmptyRsp, error)
	GetRun(ctx context.Context, in *GetRunReq, opts ...grpc.CallOption) (*Run, error)
	GetRunV2(ctx context.Context, in *GetRunReq, opts ...grpc.CallOption) (*RunV2, error)
	GetRunStepLogs(ctx context.Context, in *GetRunStepLogsReq, opts ...grpc.CallOption) (RunService_GetRunStepLogsClient, error)
	GetRunStepPodInfo(ctx context.Context, in *GetRunStepPodInfoReq, opts ...grpc.CallOption) (*GetRunStepPodInfoRsp, error)
	GetRunStepEvents(ctx context.Context, in *GetRunStepEventsReq, opts ...grpc.CallOption) (*GetRunStepEventsRsp, error)
	GetEventsByLabels(ctx context.Context, in *LabelEventsReq, opts ...grpc.CallOption) (*GetRunStepEventsRsp, error)
	DeleteRun(ctx context.Context, in *DeleteRunReq, opts ...grpc.CallOption) (*DeleteRunRsq, error)
	DeleteRunBatch(ctx context.Context, in *DeleteRunBatchReq, opts ...grpc.CallOption) (*DeleteRunBatchRsp, error)
	ListRuns(ctx context.Context, in *ListRunsReq, opts ...grpc.CallOption) (*RunsPage, error)
	ListRunsV2(ctx context.Context, in *ListRunsReq, opts ...grpc.CallOption) (*RunsPageV2, error)
}

type runServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRunServiceClient(cc grpc.ClientConnInterface) RunServiceClient {
	return &runServiceClient{cc}
}

func (c *runServiceClient) GetRunStepArtifact(ctx context.Context, in *GetRunStepArtifactReq, opts ...grpc.CallOption) (RunService_GetRunStepArtifactClient, error) {
	stream, err := c.cc.NewStream(ctx, &RunService_ServiceDesc.Streams[0], RunService_GetRunStepArtifact_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &runServiceGetRunStepArtifactClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RunService_GetRunStepArtifactClient interface {
	Recv() (*GetRunStepArtifactRsp, error)
	grpc.ClientStream
}

type runServiceGetRunStepArtifactClient struct {
	grpc.ClientStream
}

func (x *runServiceGetRunStepArtifactClient) Recv() (*GetRunStepArtifactRsp, error) {
	m := new(GetRunStepArtifactRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *runServiceClient) SubmitRun(ctx context.Context, in *SubmitRunReq, opts ...grpc.CallOption) (*RunId, error) {
	out := new(RunId)
	err := c.cc.Invoke(ctx, RunService_SubmitRun_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) SubmitRunV2(ctx context.Context, in *SubmitRunReqV2, opts ...grpc.CallOption) (*RunId, error) {
	out := new(RunId)
	err := c.cc.Invoke(ctx, RunService_SubmitRunV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) RetryRun(ctx context.Context, in *RetryRunReq, opts ...grpc.CallOption) (*common.EmptyRsp, error) {
	out := new(common.EmptyRsp)
	err := c.cc.Invoke(ctx, RunService_RetryRun_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) TerminateRun(ctx context.Context, in *TerminateRunReq, opts ...grpc.CallOption) (*common.EmptyRsp, error) {
	out := new(common.EmptyRsp)
	err := c.cc.Invoke(ctx, RunService_TerminateRun_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) GetRun(ctx context.Context, in *GetRunReq, opts ...grpc.CallOption) (*Run, error) {
	out := new(Run)
	err := c.cc.Invoke(ctx, RunService_GetRun_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) GetRunV2(ctx context.Context, in *GetRunReq, opts ...grpc.CallOption) (*RunV2, error) {
	out := new(RunV2)
	err := c.cc.Invoke(ctx, RunService_GetRunV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) GetRunStepLogs(ctx context.Context, in *GetRunStepLogsReq, opts ...grpc.CallOption) (RunService_GetRunStepLogsClient, error) {
	stream, err := c.cc.NewStream(ctx, &RunService_ServiceDesc.Streams[1], RunService_GetRunStepLogs_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &runServiceGetRunStepLogsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RunService_GetRunStepLogsClient interface {
	Recv() (*GetRunStepLogsRsp, error)
	grpc.ClientStream
}

type runServiceGetRunStepLogsClient struct {
	grpc.ClientStream
}

func (x *runServiceGetRunStepLogsClient) Recv() (*GetRunStepLogsRsp, error) {
	m := new(GetRunStepLogsRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *runServiceClient) GetRunStepPodInfo(ctx context.Context, in *GetRunStepPodInfoReq, opts ...grpc.CallOption) (*GetRunStepPodInfoRsp, error) {
	out := new(GetRunStepPodInfoRsp)
	err := c.cc.Invoke(ctx, RunService_GetRunStepPodInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) GetRunStepEvents(ctx context.Context, in *GetRunStepEventsReq, opts ...grpc.CallOption) (*GetRunStepEventsRsp, error) {
	out := new(GetRunStepEventsRsp)
	err := c.cc.Invoke(ctx, RunService_GetRunStepEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) GetEventsByLabels(ctx context.Context, in *LabelEventsReq, opts ...grpc.CallOption) (*GetRunStepEventsRsp, error) {
	out := new(GetRunStepEventsRsp)
	err := c.cc.Invoke(ctx, RunService_GetEventsByLabels_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) DeleteRun(ctx context.Context, in *DeleteRunReq, opts ...grpc.CallOption) (*DeleteRunRsq, error) {
	out := new(DeleteRunRsq)
	err := c.cc.Invoke(ctx, RunService_DeleteRun_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) DeleteRunBatch(ctx context.Context, in *DeleteRunBatchReq, opts ...grpc.CallOption) (*DeleteRunBatchRsp, error) {
	out := new(DeleteRunBatchRsp)
	err := c.cc.Invoke(ctx, RunService_DeleteRunBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) ListRuns(ctx context.Context, in *ListRunsReq, opts ...grpc.CallOption) (*RunsPage, error) {
	out := new(RunsPage)
	err := c.cc.Invoke(ctx, RunService_ListRuns_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runServiceClient) ListRunsV2(ctx context.Context, in *ListRunsReq, opts ...grpc.CallOption) (*RunsPageV2, error) {
	out := new(RunsPageV2)
	err := c.cc.Invoke(ctx, RunService_ListRunsV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RunServiceServer is the server API for RunService service.
// All implementations must embed UnimplementedRunServiceServer
// for forward compatibility
type RunServiceServer interface {
	GetRunStepArtifact(*GetRunStepArtifactReq, RunService_GetRunStepArtifactServer) error
	// Deprecated use V2
	SubmitRun(context.Context, *SubmitRunReq) (*RunId, error)
	SubmitRunV2(context.Context, *SubmitRunReqV2) (*RunId, error)
	RetryRun(context.Context, *RetryRunReq) (*common.EmptyRsp, error)
	TerminateRun(context.Context, *TerminateRunReq) (*common.EmptyRsp, error)
	GetRun(context.Context, *GetRunReq) (*Run, error)
	GetRunV2(context.Context, *GetRunReq) (*RunV2, error)
	GetRunStepLogs(*GetRunStepLogsReq, RunService_GetRunStepLogsServer) error
	GetRunStepPodInfo(context.Context, *GetRunStepPodInfoReq) (*GetRunStepPodInfoRsp, error)
	GetRunStepEvents(context.Context, *GetRunStepEventsReq) (*GetRunStepEventsRsp, error)
	GetEventsByLabels(context.Context, *LabelEventsReq) (*GetRunStepEventsRsp, error)
	DeleteRun(context.Context, *DeleteRunReq) (*DeleteRunRsq, error)
	DeleteRunBatch(context.Context, *DeleteRunBatchReq) (*DeleteRunBatchRsp, error)
	ListRuns(context.Context, *ListRunsReq) (*RunsPage, error)
	ListRunsV2(context.Context, *ListRunsReq) (*RunsPageV2, error)
	mustEmbedUnimplementedRunServiceServer()
}

// UnimplementedRunServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRunServiceServer struct {
}

func (UnimplementedRunServiceServer) GetRunStepArtifact(*GetRunStepArtifactReq, RunService_GetRunStepArtifactServer) error {
	return status.Errorf(codes.Unimplemented, "method GetRunStepArtifact not implemented")
}
func (UnimplementedRunServiceServer) SubmitRun(context.Context, *SubmitRunReq) (*RunId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitRun not implemented")
}
func (UnimplementedRunServiceServer) SubmitRunV2(context.Context, *SubmitRunReqV2) (*RunId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitRunV2 not implemented")
}
func (UnimplementedRunServiceServer) RetryRun(context.Context, *RetryRunReq) (*common.EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryRun not implemented")
}
func (UnimplementedRunServiceServer) TerminateRun(context.Context, *TerminateRunReq) (*common.EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TerminateRun not implemented")
}
func (UnimplementedRunServiceServer) GetRun(context.Context, *GetRunReq) (*Run, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRun not implemented")
}
func (UnimplementedRunServiceServer) GetRunV2(context.Context, *GetRunReq) (*RunV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRunV2 not implemented")
}
func (UnimplementedRunServiceServer) GetRunStepLogs(*GetRunStepLogsReq, RunService_GetRunStepLogsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetRunStepLogs not implemented")
}
func (UnimplementedRunServiceServer) GetRunStepPodInfo(context.Context, *GetRunStepPodInfoReq) (*GetRunStepPodInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRunStepPodInfo not implemented")
}
func (UnimplementedRunServiceServer) GetRunStepEvents(context.Context, *GetRunStepEventsReq) (*GetRunStepEventsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRunStepEvents not implemented")
}
func (UnimplementedRunServiceServer) GetEventsByLabels(context.Context, *LabelEventsReq) (*GetRunStepEventsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEventsByLabels not implemented")
}
func (UnimplementedRunServiceServer) DeleteRun(context.Context, *DeleteRunReq) (*DeleteRunRsq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRun not implemented")
}
func (UnimplementedRunServiceServer) DeleteRunBatch(context.Context, *DeleteRunBatchReq) (*DeleteRunBatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRunBatch not implemented")
}
func (UnimplementedRunServiceServer) ListRuns(context.Context, *ListRunsReq) (*RunsPage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRuns not implemented")
}
func (UnimplementedRunServiceServer) ListRunsV2(context.Context, *ListRunsReq) (*RunsPageV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRunsV2 not implemented")
}
func (UnimplementedRunServiceServer) mustEmbedUnimplementedRunServiceServer() {}

// UnsafeRunServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RunServiceServer will
// result in compilation errors.
type UnsafeRunServiceServer interface {
	mustEmbedUnimplementedRunServiceServer()
}

func RegisterRunServiceServer(s grpc.ServiceRegistrar, srv RunServiceServer) {
	s.RegisterService(&RunService_ServiceDesc, srv)
}

func _RunService_GetRunStepArtifact_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetRunStepArtifactReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RunServiceServer).GetRunStepArtifact(m, &runServiceGetRunStepArtifactServer{stream})
}

type RunService_GetRunStepArtifactServer interface {
	Send(*GetRunStepArtifactRsp) error
	grpc.ServerStream
}

type runServiceGetRunStepArtifactServer struct {
	grpc.ServerStream
}

func (x *runServiceGetRunStepArtifactServer) Send(m *GetRunStepArtifactRsp) error {
	return x.ServerStream.SendMsg(m)
}

func _RunService_SubmitRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).SubmitRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_SubmitRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).SubmitRun(ctx, req.(*SubmitRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_SubmitRunV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitRunReqV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).SubmitRunV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_SubmitRunV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).SubmitRunV2(ctx, req.(*SubmitRunReqV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_RetryRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).RetryRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_RetryRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).RetryRun(ctx, req.(*RetryRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_TerminateRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminateRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).TerminateRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_TerminateRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).TerminateRun(ctx, req.(*TerminateRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_GetRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).GetRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_GetRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).GetRun(ctx, req.(*GetRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_GetRunV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).GetRunV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_GetRunV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).GetRunV2(ctx, req.(*GetRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_GetRunStepLogs_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetRunStepLogsReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RunServiceServer).GetRunStepLogs(m, &runServiceGetRunStepLogsServer{stream})
}

type RunService_GetRunStepLogsServer interface {
	Send(*GetRunStepLogsRsp) error
	grpc.ServerStream
}

type runServiceGetRunStepLogsServer struct {
	grpc.ServerStream
}

func (x *runServiceGetRunStepLogsServer) Send(m *GetRunStepLogsRsp) error {
	return x.ServerStream.SendMsg(m)
}

func _RunService_GetRunStepPodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunStepPodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).GetRunStepPodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_GetRunStepPodInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).GetRunStepPodInfo(ctx, req.(*GetRunStepPodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_GetRunStepEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunStepEventsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).GetRunStepEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_GetRunStepEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).GetRunStepEvents(ctx, req.(*GetRunStepEventsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_GetEventsByLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelEventsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).GetEventsByLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_GetEventsByLabels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).GetEventsByLabels(ctx, req.(*LabelEventsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_DeleteRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).DeleteRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_DeleteRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).DeleteRun(ctx, req.(*DeleteRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_DeleteRunBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRunBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).DeleteRunBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_DeleteRunBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).DeleteRunBatch(ctx, req.(*DeleteRunBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_ListRuns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRunsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).ListRuns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_ListRuns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).ListRuns(ctx, req.(*ListRunsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RunService_ListRunsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRunsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunServiceServer).ListRunsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RunService_ListRunsV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunServiceServer).ListRunsV2(ctx, req.(*ListRunsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RunService_ServiceDesc is the grpc.ServiceDesc for RunService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RunService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.RunService",
	HandlerType: (*RunServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitRun",
			Handler:    _RunService_SubmitRun_Handler,
		},
		{
			MethodName: "SubmitRunV2",
			Handler:    _RunService_SubmitRunV2_Handler,
		},
		{
			MethodName: "RetryRun",
			Handler:    _RunService_RetryRun_Handler,
		},
		{
			MethodName: "TerminateRun",
			Handler:    _RunService_TerminateRun_Handler,
		},
		{
			MethodName: "GetRun",
			Handler:    _RunService_GetRun_Handler,
		},
		{
			MethodName: "GetRunV2",
			Handler:    _RunService_GetRunV2_Handler,
		},
		{
			MethodName: "GetRunStepPodInfo",
			Handler:    _RunService_GetRunStepPodInfo_Handler,
		},
		{
			MethodName: "GetRunStepEvents",
			Handler:    _RunService_GetRunStepEvents_Handler,
		},
		{
			MethodName: "GetEventsByLabels",
			Handler:    _RunService_GetEventsByLabels_Handler,
		},
		{
			MethodName: "DeleteRun",
			Handler:    _RunService_DeleteRun_Handler,
		},
		{
			MethodName: "DeleteRunBatch",
			Handler:    _RunService_DeleteRunBatch_Handler,
		},
		{
			MethodName: "ListRuns",
			Handler:    _RunService_ListRuns_Handler,
		},
		{
			MethodName: "ListRunsV2",
			Handler:    _RunService_ListRunsV2_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetRunStepArtifact",
			Handler:       _RunService_GetRunStepArtifact_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetRunStepLogs",
			Handler:       _RunService_GetRunStepLogs_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/pipeline/rpc_run_service.proto",
}
