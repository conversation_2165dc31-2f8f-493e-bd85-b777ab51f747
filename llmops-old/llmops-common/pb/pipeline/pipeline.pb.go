// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/pipeline/pipeline.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PipelineVersion_SchedulingStyle int32

const (
	PipelineVersion_IMMEDIATE PipelineVersion_SchedulingStyle = 0
	PipelineVersion_TIMING    PipelineVersion_SchedulingStyle = 1
)

// Enum value maps for PipelineVersion_SchedulingStyle.
var (
	PipelineVersion_SchedulingStyle_name = map[int32]string{
		0: "IMMEDIATE",
		1: "TIMING",
	}
	PipelineVersion_SchedulingStyle_value = map[string]int32{
		"IMMEDIATE": 0,
		"TIMING":    1,
	}
)

func (x PipelineVersion_SchedulingStyle) Enum() *PipelineVersion_SchedulingStyle {
	p := new(PipelineVersion_SchedulingStyle)
	*p = x
	return p
}

func (x PipelineVersion_SchedulingStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PipelineVersion_SchedulingStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_pipeline_proto_enumTypes[0].Descriptor()
}

func (PipelineVersion_SchedulingStyle) Type() protoreflect.EnumType {
	return &file_proto_pipeline_pipeline_proto_enumTypes[0]
}

func (x PipelineVersion_SchedulingStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PipelineVersion_SchedulingStyle.Descriptor instead.
func (PipelineVersion_SchedulingStyle) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_pipeline_proto_rawDescGZIP(), []int{1, 0}
}

type PipelineVersionV2_SchedulingStyle int32

const (
	PipelineVersionV2_IMMEDIATE PipelineVersionV2_SchedulingStyle = 0
	PipelineVersionV2_TIMING    PipelineVersionV2_SchedulingStyle = 1
)

// Enum value maps for PipelineVersionV2_SchedulingStyle.
var (
	PipelineVersionV2_SchedulingStyle_name = map[int32]string{
		0: "IMMEDIATE",
		1: "TIMING",
	}
	PipelineVersionV2_SchedulingStyle_value = map[string]int32{
		"IMMEDIATE": 0,
		"TIMING":    1,
	}
)

func (x PipelineVersionV2_SchedulingStyle) Enum() *PipelineVersionV2_SchedulingStyle {
	p := new(PipelineVersionV2_SchedulingStyle)
	*p = x
	return p
}

func (x PipelineVersionV2_SchedulingStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PipelineVersionV2_SchedulingStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_pipeline_proto_enumTypes[1].Descriptor()
}

func (PipelineVersionV2_SchedulingStyle) Type() protoreflect.EnumType {
	return &file_proto_pipeline_pipeline_proto_enumTypes[1]
}

func (x PipelineVersionV2_SchedulingStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PipelineVersionV2_SchedulingStyle.Descriptor instead.
func (PipelineVersionV2_SchedulingStyle) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_pipeline_proto_rawDescGZIP(), []int{2, 0}
}

type Pipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"工作流id"`
	 
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"工作流名称"`
	 
	CreateUser string `protobuf:"bytes,3,opt,name=create_user,json=createUser,proto3" json:"create_user" description:"创建用户"`
	 
	CreateTime int64 `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`
	 
	Desc string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc" description:"描述"`
	 
	ProjectId string `protobuf:"bytes,6,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"所属项目id"`
	 
	VersionCount int64 `protobuf:"varint,7,opt,name=version_count,json=versionCount,proto3" json:"version_count" description:"版本数"`
}

func (x *Pipeline) Reset() {
	*x = Pipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_pipeline_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pipeline) ProtoMessage() {}

func (x *Pipeline) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_pipeline_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pipeline.ProtoReflect.Descriptor instead.
func (*Pipeline) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_pipeline_proto_rawDescGZIP(), []int{0}
}

func (x *Pipeline) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Pipeline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Pipeline) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *Pipeline) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Pipeline) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Pipeline) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Pipeline) GetVersionCount() int64 {
	if x != nil {
		return x.VersionCount
	}
	return 0
}

type PipelineVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"工作流版本id"`
	 
	PipelineId string `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"所属工作流id"`
	 
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name" description:"版本名称"`
	 
	TaskFlow *TaskFlow `protobuf:"bytes,4,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"流程图信息"`
	 
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`
	 
	UpdateTime int64 `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间"`
	 
	Desc string `protobuf:"bytes,16,opt,name=desc,proto3" json:"desc" description:"版本描述"`
	 
	Style PipelineVersion_SchedulingStyle `protobuf:"varint,9,opt,name=style,proto3,enum=pipeline.PipelineVersion_SchedulingStyle" json:"style" description:"调度类型 0:手动调度 1:定时调度"`
	 
	TimingConfig *TimingConfig `protobuf:"bytes,10,opt,name=timing_config,json=timingConfig,proto3" json:"timing_config" description:"定时调度配置"`
}

func (x *PipelineVersion) Reset() {
	*x = PipelineVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_pipeline_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineVersion) ProtoMessage() {}

func (x *PipelineVersion) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_pipeline_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineVersion.ProtoReflect.Descriptor instead.
func (*PipelineVersion) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_pipeline_proto_rawDescGZIP(), []int{1}
}

func (x *PipelineVersion) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PipelineVersion) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineVersion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineVersion) GetTaskFlow() *TaskFlow {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *PipelineVersion) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PipelineVersion) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *PipelineVersion) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *PipelineVersion) GetStyle() PipelineVersion_SchedulingStyle {
	if x != nil {
		return x.Style
	}
	return PipelineVersion_IMMEDIATE
}

func (x *PipelineVersion) GetTimingConfig() *TimingConfig {
	if x != nil {
		return x.TimingConfig
	}
	return nil
}

type PipelineVersionV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"工作流版本id"`
	 
	PipelineId string `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id" description:"所属工作流id"`
	 
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name" description:"版本名称"`
	 
	TaskFlow *TaskFlowV2 `protobuf:"bytes,4,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"流程图信息"`
	 
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`
	 
	UpdateTime int64 `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间"`
	 
	Desc string `protobuf:"bytes,16,opt,name=desc,proto3" json:"desc" description:"版本描述"`
	 
	Style PipelineVersionV2_SchedulingStyle `protobuf:"varint,9,opt,name=style,proto3,enum=pipeline.PipelineVersionV2_SchedulingStyle" json:"style" description:"调度类型 0:手动调度 1:定时调度"`
	 
	TimingConfig *TimingConfig `protobuf:"bytes,10,opt,name=timing_config,json=timingConfig,proto3" json:"timing_config" description:"定时调度配置"`
}

func (x *PipelineVersionV2) Reset() {
	*x = PipelineVersionV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_pipeline_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineVersionV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineVersionV2) ProtoMessage() {}

func (x *PipelineVersionV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_pipeline_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineVersionV2.ProtoReflect.Descriptor instead.
func (*PipelineVersionV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_pipeline_proto_rawDescGZIP(), []int{2}
}

func (x *PipelineVersionV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PipelineVersionV2) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineVersionV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineVersionV2) GetTaskFlow() *TaskFlowV2 {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *PipelineVersionV2) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PipelineVersionV2) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *PipelineVersionV2) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *PipelineVersionV2) GetStyle() PipelineVersionV2_SchedulingStyle {
	if x != nil {
		return x.Style
	}
	return PipelineVersionV2_IMMEDIATE
}

func (x *PipelineVersionV2) GetTimingConfig() *TimingConfig {
	if x != nil {
		return x.TimingConfig
	}
	return nil
}

var File_proto_pipeline_pipeline_proto protoreflect.FileDescriptor

var file_proto_pipeline_pipeline_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x01, 0x0a, 0x08, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x89,
	0x03, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3f,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x3b, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c,
	0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x2c, 0x0a, 0x0f,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x0d, 0x0a, 0x09, 0x49, 0x4d, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x49, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x22, 0x8f, 0x03, 0x0a, 0x11, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x32,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x32, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x41,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0c, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x2c,
	0x0a, 0x0f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4d, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x54, 0x49, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x42, 0x35, 0x5a, 0x33,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_pipeline_pipeline_proto_rawDescOnce sync.Once
	file_proto_pipeline_pipeline_proto_rawDescData = file_proto_pipeline_pipeline_proto_rawDesc
)

func file_proto_pipeline_pipeline_proto_rawDescGZIP() []byte {
	file_proto_pipeline_pipeline_proto_rawDescOnce.Do(func() {
		file_proto_pipeline_pipeline_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pipeline_pipeline_proto_rawDescData)
	})
	return file_proto_pipeline_pipeline_proto_rawDescData
}

var file_proto_pipeline_pipeline_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_pipeline_pipeline_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_pipeline_pipeline_proto_goTypes = []interface{}{
	(PipelineVersion_SchedulingStyle)(0),   // 0: pipeline.PipelineVersion.SchedulingStyle
	(PipelineVersionV2_SchedulingStyle)(0), // 1: pipeline.PipelineVersionV2.SchedulingStyle
	(*Pipeline)(nil),                       // 2: pipeline.Pipeline
	(*PipelineVersion)(nil),                // 3: pipeline.PipelineVersion
	(*PipelineVersionV2)(nil),              // 4: pipeline.PipelineVersionV2
	(*TaskFlow)(nil),                       // 5: pipeline.TaskFlow
	(*TimingConfig)(nil),                   // 6: pipeline.TimingConfig
	(*TaskFlowV2)(nil),                     // 7: pipeline.TaskFlowV2
}
var file_proto_pipeline_pipeline_proto_depIdxs = []int32{
	5, // 0: pipeline.PipelineVersion.task_flow:type_name -> pipeline.TaskFlow
	0, // 1: pipeline.PipelineVersion.style:type_name -> pipeline.PipelineVersion.SchedulingStyle
	6, // 2: pipeline.PipelineVersion.timing_config:type_name -> pipeline.TimingConfig
	7, // 3: pipeline.PipelineVersionV2.task_flow:type_name -> pipeline.TaskFlowV2
	1, // 4: pipeline.PipelineVersionV2.style:type_name -> pipeline.PipelineVersionV2.SchedulingStyle
	6, // 5: pipeline.PipelineVersionV2.timing_config:type_name -> pipeline.TimingConfig
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_proto_pipeline_pipeline_proto_init() }
func file_proto_pipeline_pipeline_proto_init() {
	if File_proto_pipeline_pipeline_proto != nil {
		return
	}
	file_proto_pipeline_run_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_pipeline_pipeline_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_pipeline_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_pipeline_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineVersionV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pipeline_pipeline_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_pipeline_pipeline_proto_goTypes,
		DependencyIndexes: file_proto_pipeline_pipeline_proto_depIdxs,
		EnumInfos:         file_proto_pipeline_pipeline_proto_enumTypes,
		MessageInfos:      file_proto_pipeline_pipeline_proto_msgTypes,
	}.Build()
	File_proto_pipeline_pipeline_proto = out.File
	file_proto_pipeline_pipeline_proto_rawDesc = nil
	file_proto_pipeline_pipeline_proto_goTypes = nil
	file_proto_pipeline_pipeline_proto_depIdxs = nil
}
