// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/pipeline/rpc_run_service.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRunStepArtifactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId    string `protobuf:"bytes,1,opt,name=runId,proto3" json:"runId"`
	NodeId   string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
	Artifact string `protobuf:"bytes,3,opt,name=artifact,proto3" json:"artifact"`
}

func (x *GetRunStepArtifactReq) Reset() {
	*x = GetRunStepArtifactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepArtifactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepArtifactReq) ProtoMessage() {}

func (x *GetRunStepArtifactReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepArtifactReq.ProtoReflect.Descriptor instead.
func (*GetRunStepArtifactReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetRunStepArtifactReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *GetRunStepArtifactReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *GetRunStepArtifactReq) GetArtifact() string {
	if x != nil {
		return x.Artifact
	}
	return ""
}

type GetRunStepArtifactRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name"`
	File     []byte `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
}

func (x *GetRunStepArtifactRsp) Reset() {
	*x = GetRunStepArtifactRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepArtifactRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepArtifactRsp) ProtoMessage() {}

func (x *GetRunStepArtifactRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepArtifactRsp.ProtoReflect.Descriptor instead.
func (*GetRunStepArtifactRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetRunStepArtifactRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GetRunStepArtifactRsp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type SubmitRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId     string         `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	SubmitRunBody *SubmitRunBody `protobuf:"bytes,2,opt,name=SubmitRunBody,proto3" json:"SubmitRunBody"`
}

func (x *SubmitRunReq) Reset() {
	*x = SubmitRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRunReq) ProtoMessage() {}

func (x *SubmitRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRunReq.ProtoReflect.Descriptor instead.
func (*SubmitRunReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{2}
}

func (x *SubmitRunReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SubmitRunReq) GetSubmitRunBody() *SubmitRunBody {
	if x != nil {
		return x.SubmitRunBody
	}
	return nil
}

type SubmitRunBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	TaskSource *TaskSource `protobuf:"bytes,1,opt,name=task_source,json=taskSource,proto3" json:"task_source" description:"源任务信息"`
	 
	TaskFlow *TaskFlow `protobuf:"bytes,2,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"任务流程图"`
}

func (x *SubmitRunBody) Reset() {
	*x = SubmitRunBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRunBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRunBody) ProtoMessage() {}

func (x *SubmitRunBody) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRunBody.ProtoReflect.Descriptor instead.
func (*SubmitRunBody) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{3}
}

func (x *SubmitRunBody) GetTaskSource() *TaskSource {
	if x != nil {
		return x.TaskSource
	}
	return nil
}

func (x *SubmitRunBody) GetTaskFlow() *TaskFlow {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

type SubmitRunReqV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId     string           `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	SubmitRunBody *SubmitRunBodyV2 `protobuf:"bytes,2,opt,name=submit_run_body,json=submitRunBody,proto3" json:"submit_run_body"`
}

func (x *SubmitRunReqV2) Reset() {
	*x = SubmitRunReqV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRunReqV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRunReqV2) ProtoMessage() {}

func (x *SubmitRunReqV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRunReqV2.ProtoReflect.Descriptor instead.
func (*SubmitRunReqV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{4}
}

func (x *SubmitRunReqV2) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SubmitRunReqV2) GetSubmitRunBody() *SubmitRunBodyV2 {
	if x != nil {
		return x.SubmitRunBody
	}
	return nil
}

type SubmitRunBodyV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	TaskSource *TaskSource `protobuf:"bytes,1,opt,name=task_source,json=taskSource,proto3" json:"task_source" description:"源任务信息"`
	 
	TaskFlow *TaskFlowV2 `protobuf:"bytes,2,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"任务流程图"`
}

func (x *SubmitRunBodyV2) Reset() {
	*x = SubmitRunBodyV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRunBodyV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRunBodyV2) ProtoMessage() {}

func (x *SubmitRunBodyV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRunBodyV2.ProtoReflect.Descriptor instead.
func (*SubmitRunBodyV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{5}
}

func (x *SubmitRunBodyV2) GetTaskSource() *TaskSource {
	if x != nil {
		return x.TaskSource
	}
	return nil
}

func (x *SubmitRunBodyV2) GetTaskFlow() *TaskFlowV2 {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

type RunId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	RunId string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id" description:"运行记录id"`
}

func (x *RunId) Reset() {
	*x = RunId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunId) ProtoMessage() {}

func (x *RunId) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunId.ProtoReflect.Descriptor instead.
func (*RunId) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{6}
}

func (x *RunId) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

type RetryRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId string `protobuf:"bytes,2,opt,name=run_id,json=runId,proto3" json:"run_id"`
}

func (x *RetryRunReq) Reset() {
	*x = RetryRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryRunReq) ProtoMessage() {}

func (x *RetryRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryRunReq.ProtoReflect.Descriptor instead.
func (*RetryRunReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{7}
}

func (x *RetryRunReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

type TerminateRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id"`
}

func (x *TerminateRunReq) Reset() {
	*x = TerminateRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminateRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminateRunReq) ProtoMessage() {}

func (x *TerminateRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminateRunReq.ProtoReflect.Descriptor instead.
func (*TerminateRunReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{8}
}

func (x *TerminateRunReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

type GetRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id"`
}

func (x *GetRunReq) Reset() {
	*x = GetRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunReq) ProtoMessage() {}

func (x *GetRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunReq.ProtoReflect.Descriptor instead.
func (*GetRunReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetRunReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

type GetRunStepLogsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId  string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id"`
	NodeId string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
	Limit  int32  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit"`
}

func (x *GetRunStepLogsReq) Reset() {
	*x = GetRunStepLogsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepLogsReq) ProtoMessage() {}

func (x *GetRunStepLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepLogsReq.ProtoReflect.Descriptor instead.
func (*GetRunStepLogsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetRunStepLogsReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *GetRunStepLogsReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *GetRunStepLogsReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetRunStepLogsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Log []byte `protobuf:"bytes,1,opt,name=log,proto3" json:"log"`
}

func (x *GetRunStepLogsRsp) Reset() {
	*x = GetRunStepLogsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepLogsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepLogsRsp) ProtoMessage() {}

func (x *GetRunStepLogsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepLogsRsp.ProtoReflect.Descriptor instead.
func (*GetRunStepLogsRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetRunStepLogsRsp) GetLog() []byte {
	if x != nil {
		return x.Log
	}
	return nil
}

type GetRunStepPodInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId  string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id"`
	NodeId string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
}

func (x *GetRunStepPodInfoReq) Reset() {
	*x = GetRunStepPodInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepPodInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepPodInfoReq) ProtoMessage() {}

func (x *GetRunStepPodInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepPodInfoReq.ProtoReflect.Descriptor instead.
func (*GetRunStepPodInfoReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetRunStepPodInfoReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *GetRunStepPodInfoReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

type GetRunStepPodInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Yaml []byte `protobuf:"bytes,1,opt,name=yaml,proto3" json:"yaml"`
}

func (x *GetRunStepPodInfoRsp) Reset() {
	*x = GetRunStepPodInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepPodInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepPodInfoRsp) ProtoMessage() {}

func (x *GetRunStepPodInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepPodInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRunStepPodInfoRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetRunStepPodInfoRsp) GetYaml() []byte {
	if x != nil {
		return x.Yaml
	}
	return nil
}

type GetRunStepEventsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId     string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id"`
	NodeId    string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
	ProjectId string `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	TenantId  string `protobuf:"bytes,4,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id"`
}

func (x *GetRunStepEventsReq) Reset() {
	*x = GetRunStepEventsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepEventsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepEventsReq) ProtoMessage() {}

func (x *GetRunStepEventsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepEventsReq.ProtoReflect.Descriptor instead.
func (*GetRunStepEventsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetRunStepEventsReq) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *GetRunStepEventsReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *GetRunStepEventsReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetRunStepEventsReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

type GetRunStepEventsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event []*common.PodEvent `protobuf:"bytes,1,rep,name=event,proto3" json:"event"`
}

func (x *GetRunStepEventsRsp) Reset() {
	*x = GetRunStepEventsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunStepEventsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunStepEventsRsp) ProtoMessage() {}

func (x *GetRunStepEventsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunStepEventsRsp.ProtoReflect.Descriptor instead.
func (*GetRunStepEventsRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetRunStepEventsRsp) GetEvent() []*common.PodEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type DeleteRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DeleteRunReq) Reset() {
	*x = DeleteRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRunReq) ProtoMessage() {}

func (x *DeleteRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRunReq.ProtoReflect.Descriptor instead.
func (*DeleteRunReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteRunReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteRunRsq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DeleteRunRsq) Reset() {
	*x = DeleteRunRsq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRunRsq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRunRsq) ProtoMessage() {}

func (x *DeleteRunRsq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRunRsq.ProtoReflect.Descriptor instead.
func (*DeleteRunRsq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteRunRsq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteRunBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids" description:"删除任务id集合"`
}

func (x *DeleteRunBatchReq) Reset() {
	*x = DeleteRunBatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRunBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRunBatchReq) ProtoMessage() {}

func (x *DeleteRunBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRunBatchReq.ProtoReflect.Descriptor instead.
func (*DeleteRunBatchReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteRunBatchReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteRunBatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids" description:"删除任务id集合"`
}

func (x *DeleteRunBatchRsp) Reset() {
	*x = DeleteRunBatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRunBatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRunBatchRsp) ProtoMessage() {}

func (x *DeleteRunBatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRunBatchRsp.ProtoReflect.Descriptor instead.
func (*DeleteRunBatchRsp) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteRunBatchRsp) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListRunsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string              `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id"`
	PageReq   *PageReq            `protobuf:"bytes,2,opt,name=page_req,json=pageReq,proto3" json:"page_req"`
	Filter    *ListRunsReq_Filter `protobuf:"bytes,6,opt,name=filter,proto3" json:"filter"`
}

func (x *ListRunsReq) Reset() {
	*x = ListRunsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRunsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRunsReq) ProtoMessage() {}

func (x *ListRunsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRunsReq.ProtoReflect.Descriptor instead.
func (*ListRunsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListRunsReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ListRunsReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ListRunsReq) GetFilter() *ListRunsReq_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RunsPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Runs []*Run `protobuf:"bytes,2,rep,name=runs,proto3" json:"runs" description:"run列表"`
	 
	Size int64 `protobuf:"varint,3,opt,name=size,proto3" json:"size" description:"run总数"`
}

func (x *RunsPage) Reset() {
	*x = RunsPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunsPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunsPage) ProtoMessage() {}

func (x *RunsPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunsPage.ProtoReflect.Descriptor instead.
func (*RunsPage) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{21}
}

func (x *RunsPage) GetRuns() []*Run {
	if x != nil {
		return x.Runs
	}
	return nil
}

func (x *RunsPage) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type RunsPageV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Runs []*RunV2 `protobuf:"bytes,2,rep,name=runs,proto3" json:"runs" description:"run列表"`
	 
	Size int64 `protobuf:"varint,3,opt,name=size,proto3" json:"size" description:"run总数"`
}

func (x *RunsPageV2) Reset() {
	*x = RunsPageV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunsPageV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunsPageV2) ProtoMessage() {}

func (x *RunsPageV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunsPageV2.ProtoReflect.Descriptor instead.
func (*RunsPageV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{22}
}

func (x *RunsPageV2) GetRuns() []*RunV2 {
	if x != nil {
		return x.Runs
	}
	return nil
}

func (x *RunsPageV2) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type LabelEventsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *LabelEventsReq) Reset() {
	*x = LabelEventsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelEventsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelEventsReq) ProtoMessage() {}

func (x *LabelEventsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelEventsReq.ProtoReflect.Descriptor instead.
func (*LabelEventsReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{23}
}

func (x *LabelEventsReq) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ListRunsReq_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId   []string       `protobuf:"bytes,1,rep,name=source_id,json=sourceId,proto3" json:"source_id"`
	SourceType TaskSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=pipeline.TaskSourceType" json:"source_type"`
	State      []Run_Status   `protobuf:"varint,3,rep,packed,name=state,proto3,enum=pipeline.Run_Status" json:"state"`
	From       int64          `protobuf:"varint,4,opt,name=from,proto3" json:"from"`
	To         int64          `protobuf:"varint,5,opt,name=to,proto3" json:"to"`
	CreateUser string         `protobuf:"bytes,6,opt,name=create_user,json=createUser,proto3" json:"create_user"`
	SourceName string         `protobuf:"bytes,7,opt,name=source_name,json=sourceName,proto3" json:"source_name"`
}

func (x *ListRunsReq_Filter) Reset() {
	*x = ListRunsReq_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRunsReq_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRunsReq_Filter) ProtoMessage() {}

func (x *ListRunsReq_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_rpc_run_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRunsReq_Filter.ProtoReflect.Descriptor instead.
func (*ListRunsReq_Filter) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_rpc_run_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ListRunsReq_Filter) GetSourceId() []string {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *ListRunsReq_Filter) GetSourceType() TaskSourceType {
	if x != nil {
		return x.SourceType
	}
	return TaskSourceType_NONE
}

func (x *ListRunsReq_Filter) GetState() []Run_Status {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *ListRunsReq_Filter) GetFrom() int64 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *ListRunsReq_Filter) GetTo() int64 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *ListRunsReq_Filter) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *ListRunsReq_Filter) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

var File_proto_pipeline_rpc_run_service_proto protoreflect.FileDescriptor

var file_proto_pipeline_rpc_run_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2f, 0x72, 0x70, 0x63, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x72, 0x75, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x62, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x75,
	0x6e, 0x53, 0x74, 0x65, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x22, 0x48, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x6c, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52,
	0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75,
	0x6e, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e,
	0x42, 0x6f, 0x64, 0x79, 0x52, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x42,
	0x6f, 0x64, 0x79, 0x22, 0x77, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e,
	0x42, 0x6f, 0x64, 0x79, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0x72, 0x0a, 0x0e,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x56, 0x32, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a,
	0x0f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x56,
	0x32, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x42, 0x6f, 0x64, 0x79,
	0x22, 0x7b, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x42, 0x6f, 0x64,
	0x79, 0x56, 0x32, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0a,
	0x74, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x56, 0x32, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0x1e, 0x0a,
	0x05, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x24, 0x0a,
	0x0b, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75,
	0x6e, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x0f, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x22, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49,
	0x64, 0x22, 0x59, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4c,
	0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x25, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03,
	0x6c, 0x6f, 0x67, 0x22, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65,
	0x70, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52,
	0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x64, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x1e, 0x0a, 0x0c, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1e, 0x0a, 0x0c, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x25, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x22, 0x25, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x85, 0x03, 0x0a, 0x0b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xf2, 0x01, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x41, 0x0a, 0x08, 0x52, 0x75, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x04,
	0x72, 0x75, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x52, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x45, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x56,
	0x32, 0x12, 0x23, 0x0a, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x56, 0x32,
	0x52, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xe8, 0x07, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53,
	0x74, 0x65, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x1f, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65,
	0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74,
	0x65, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x73, 0x70, 0x30, 0x01, 0x12,
	0x34, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x12, 0x16, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52,
	0x75, 0x6e, 0x56, 0x32, 0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x56, 0x32, 0x1a, 0x0f,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12,
	0x34, 0x0a, 0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x12, 0x15, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x52, 0x75, 0x6e, 0x12, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x12, 0x13, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x0d, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75,
	0x6e, 0x12, 0x30, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x56, 0x32, 0x12, 0x13, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75,
	0x6e, 0x56, 0x32, 0x12, 0x4c, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65,
	0x70, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x73, 0x70, 0x30,
	0x01, 0x12, 0x53, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x50,
	0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x50, 0x6f, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x50, 0x6f, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e,
	0x53, 0x74, 0x65, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x75, 0x6e, 0x12, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e,
	0x52, 0x73, 0x71, 0x12, 0x4a, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12,
	0x35, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x15, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75,
	0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75,
	0x6e, 0x73, 0x56, 0x32, 0x12, 0x15, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x56,
	0x32, 0x42, 0x35, 0x5a, 0x33, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69,
	0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_pipeline_rpc_run_service_proto_rawDescOnce sync.Once
	file_proto_pipeline_rpc_run_service_proto_rawDescData = file_proto_pipeline_rpc_run_service_proto_rawDesc
)

func file_proto_pipeline_rpc_run_service_proto_rawDescGZIP() []byte {
	file_proto_pipeline_rpc_run_service_proto_rawDescOnce.Do(func() {
		file_proto_pipeline_rpc_run_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pipeline_rpc_run_service_proto_rawDescData)
	})
	return file_proto_pipeline_rpc_run_service_proto_rawDescData
}

var file_proto_pipeline_rpc_run_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_proto_pipeline_rpc_run_service_proto_goTypes = []interface{}{
	(*GetRunStepArtifactReq)(nil), // 0: pipeline.GetRunStepArtifactReq
	(*GetRunStepArtifactRsp)(nil), // 1: pipeline.GetRunStepArtifactRsp
	(*SubmitRunReq)(nil),          // 2: pipeline.SubmitRunReq
	(*SubmitRunBody)(nil),         // 3: pipeline.SubmitRunBody
	(*SubmitRunReqV2)(nil),        // 4: pipeline.SubmitRunReqV2
	(*SubmitRunBodyV2)(nil),       // 5: pipeline.SubmitRunBodyV2
	(*RunId)(nil),                 // 6: pipeline.RunId
	(*RetryRunReq)(nil),           // 7: pipeline.RetryRunReq
	(*TerminateRunReq)(nil),       // 8: pipeline.TerminateRunReq
	(*GetRunReq)(nil),             // 9: pipeline.GetRunReq
	(*GetRunStepLogsReq)(nil),     // 10: pipeline.GetRunStepLogsReq
	(*GetRunStepLogsRsp)(nil),     // 11: pipeline.GetRunStepLogsRsp
	(*GetRunStepPodInfoReq)(nil),  // 12: pipeline.GetRunStepPodInfoReq
	(*GetRunStepPodInfoRsp)(nil),  // 13: pipeline.GetRunStepPodInfoRsp
	(*GetRunStepEventsReq)(nil),   // 14: pipeline.GetRunStepEventsReq
	(*GetRunStepEventsRsp)(nil),   // 15: pipeline.GetRunStepEventsRsp
	(*DeleteRunReq)(nil),          // 16: pipeline.DeleteRunReq
	(*DeleteRunRsq)(nil),          // 17: pipeline.DeleteRunRsq
	(*DeleteRunBatchReq)(nil),     // 18: pipeline.DeleteRunBatchReq
	(*DeleteRunBatchRsp)(nil),     // 19: pipeline.DeleteRunBatchRsp
	(*ListRunsReq)(nil),           // 20: pipeline.ListRunsReq
	(*RunsPage)(nil),              // 21: pipeline.RunsPage
	(*RunsPageV2)(nil),            // 22: pipeline.RunsPageV2
	(*LabelEventsReq)(nil),        // 23: pipeline.LabelEventsReq
	(*ListRunsReq_Filter)(nil),    // 24: pipeline.ListRunsReq.Filter
	nil,                           // 25: pipeline.LabelEventsReq.LabelsEntry
	(*TaskSource)(nil),            // 26: pipeline.TaskSource
	(*TaskFlow)(nil),              // 27: pipeline.TaskFlow
	(*TaskFlowV2)(nil),            // 28: pipeline.TaskFlowV2
	(*common.PodEvent)(nil),       // 29: common.PodEvent
	(*PageReq)(nil),               // 30: pipeline.PageReq
	(*Run)(nil),                   // 31: pipeline.Run
	(*RunV2)(nil),                 // 32: pipeline.RunV2
	(TaskSourceType)(0),           // 33: pipeline.TaskSourceType
	(Run_Status)(0),               // 34: pipeline.Run.Status
	(*common.EmptyRsp)(nil),       // 35: commons.EmptyRsp
}
var file_proto_pipeline_rpc_run_service_proto_depIdxs = []int32{
	3,  // 0: pipeline.SubmitRunReq.SubmitRunBody:type_name -> pipeline.SubmitRunBody
	26, // 1: pipeline.SubmitRunBody.task_source:type_name -> pipeline.TaskSource
	27, // 2: pipeline.SubmitRunBody.task_flow:type_name -> pipeline.TaskFlow
	5,  // 3: pipeline.SubmitRunReqV2.submit_run_body:type_name -> pipeline.SubmitRunBodyV2
	26, // 4: pipeline.SubmitRunBodyV2.task_source:type_name -> pipeline.TaskSource
	28, // 5: pipeline.SubmitRunBodyV2.task_flow:type_name -> pipeline.TaskFlowV2
	29, // 6: pipeline.GetRunStepEventsRsp.event:type_name -> common.PodEvent
	30, // 7: pipeline.ListRunsReq.page_req:type_name -> pipeline.PageReq
	24, // 8: pipeline.ListRunsReq.filter:type_name -> pipeline.ListRunsReq.Filter
	31, // 9: pipeline.RunsPage.runs:type_name -> pipeline.Run
	32, // 10: pipeline.RunsPageV2.runs:type_name -> pipeline.RunV2
	25, // 11: pipeline.LabelEventsReq.labels:type_name -> pipeline.LabelEventsReq.LabelsEntry
	33, // 12: pipeline.ListRunsReq.Filter.source_type:type_name -> pipeline.TaskSourceType
	34, // 13: pipeline.ListRunsReq.Filter.state:type_name -> pipeline.Run.Status
	0,  // 14: pipeline.RunService.GetRunStepArtifact:input_type -> pipeline.GetRunStepArtifactReq
	2,  // 15: pipeline.RunService.SubmitRun:input_type -> pipeline.SubmitRunReq
	4,  // 16: pipeline.RunService.SubmitRunV2:input_type -> pipeline.SubmitRunReqV2
	7,  // 17: pipeline.RunService.RetryRun:input_type -> pipeline.RetryRunReq
	8,  // 18: pipeline.RunService.TerminateRun:input_type -> pipeline.TerminateRunReq
	9,  // 19: pipeline.RunService.GetRun:input_type -> pipeline.GetRunReq
	9,  // 20: pipeline.RunService.GetRunV2:input_type -> pipeline.GetRunReq
	10, // 21: pipeline.RunService.GetRunStepLogs:input_type -> pipeline.GetRunStepLogsReq
	12, // 22: pipeline.RunService.GetRunStepPodInfo:input_type -> pipeline.GetRunStepPodInfoReq
	14, // 23: pipeline.RunService.GetRunStepEvents:input_type -> pipeline.GetRunStepEventsReq
	23, // 24: pipeline.RunService.GetEventsByLabels:input_type -> pipeline.LabelEventsReq
	16, // 25: pipeline.RunService.DeleteRun:input_type -> pipeline.DeleteRunReq
	18, // 26: pipeline.RunService.DeleteRunBatch:input_type -> pipeline.DeleteRunBatchReq
	20, // 27: pipeline.RunService.ListRuns:input_type -> pipeline.ListRunsReq
	20, // 28: pipeline.RunService.ListRunsV2:input_type -> pipeline.ListRunsReq
	1,  // 29: pipeline.RunService.GetRunStepArtifact:output_type -> pipeline.GetRunStepArtifactRsp
	6,  // 30: pipeline.RunService.SubmitRun:output_type -> pipeline.RunId
	6,  // 31: pipeline.RunService.SubmitRunV2:output_type -> pipeline.RunId
	35, // 32: pipeline.RunService.RetryRun:output_type -> commons.EmptyRsp
	35, // 33: pipeline.RunService.TerminateRun:output_type -> commons.EmptyRsp
	31, // 34: pipeline.RunService.GetRun:output_type -> pipeline.Run
	32, // 35: pipeline.RunService.GetRunV2:output_type -> pipeline.RunV2
	11, // 36: pipeline.RunService.GetRunStepLogs:output_type -> pipeline.GetRunStepLogsRsp
	13, // 37: pipeline.RunService.GetRunStepPodInfo:output_type -> pipeline.GetRunStepPodInfoRsp
	15, // 38: pipeline.RunService.GetRunStepEvents:output_type -> pipeline.GetRunStepEventsRsp
	15, // 39: pipeline.RunService.GetEventsByLabels:output_type -> pipeline.GetRunStepEventsRsp
	17, // 40: pipeline.RunService.DeleteRun:output_type -> pipeline.DeleteRunRsq
	19, // 41: pipeline.RunService.DeleteRunBatch:output_type -> pipeline.DeleteRunBatchRsp
	21, // 42: pipeline.RunService.ListRuns:output_type -> pipeline.RunsPage
	22, // 43: pipeline.RunService.ListRunsV2:output_type -> pipeline.RunsPageV2
	29, // [29:44] is the sub-list for method output_type
	14, // [14:29] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_proto_pipeline_rpc_run_service_proto_init() }
func file_proto_pipeline_rpc_run_service_proto_init() {
	if File_proto_pipeline_rpc_run_service_proto != nil {
		return
	}
	file_proto_pipeline_run_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_pipeline_rpc_run_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepArtifactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepArtifactRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRunBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRunReqV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRunBodyV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminateRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepLogsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepLogsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepPodInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepPodInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepEventsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunStepEventsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRunRsq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRunBatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRunBatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRunsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunsPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunsPageV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelEventsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_rpc_run_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRunsReq_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pipeline_rpc_run_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_pipeline_rpc_run_service_proto_goTypes,
		DependencyIndexes: file_proto_pipeline_rpc_run_service_proto_depIdxs,
		MessageInfos:      file_proto_pipeline_rpc_run_service_proto_msgTypes,
	}.Build()
	File_proto_pipeline_rpc_run_service_proto = out.File
	file_proto_pipeline_rpc_run_service_proto_rawDesc = nil
	file_proto_pipeline_rpc_run_service_proto_goTypes = nil
	file_proto_pipeline_rpc_run_service_proto_depIdxs = nil
}
