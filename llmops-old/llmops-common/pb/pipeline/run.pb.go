// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/pipeline/run.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskSourceType int32

const (
	TaskSourceType_NONE               TaskSourceType = 0
	TaskSourceType_PIPELINE           TaskSourceType = 1
	TaskSourceType_MODEL_TRAINING     TaskSourceType = 2
	TaskSourceType_MODEL_EVALUATE     TaskSourceType = 3
	TaskSourceType_CORPUS_PROCESSING  TaskSourceType = 4
	TaskSourceType_CORPUS_EVAL        TaskSourceType = 5
	TaskSourceType_MODEL_DOWNlOADER   TaskSourceType = 6
	TaskSourceType_MODEL_QUANTIZATION TaskSourceType = 7
)

// Enum value maps for TaskSourceType.
var (
	TaskSourceType_name = map[int32]string{
		0: "NONE",
		1: "PIPELINE",
		2: "MODEL_TRAINING",
		3: "MODEL_EVALUATE",
		4: "CORPUS_PROCESSING",
		5: "CORPUS_EVAL",
		6: "MODEL_DOWNlOADER",
		7: "MODEL_QUANTIZATION",
	}
	TaskSourceType_value = map[string]int32{
		"NONE":               0,
		"PIPELINE":           1,
		"MODEL_TRAINING":     2,
		"MODEL_EVALUATE":     3,
		"CORPUS_PROCESSING":  4,
		"CORPUS_EVAL":        5,
		"MODEL_DOWNlOADER":   6,
		"MODEL_QUANTIZATION": 7,
	}
)

func (x TaskSourceType) Enum() *TaskSourceType {
	p := new(TaskSourceType)
	*p = x
	return p
}

func (x TaskSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[0].Descriptor()
}

func (TaskSourceType) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[0]
}

func (x TaskSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskSourceType.Descriptor instead.
func (TaskSourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{0}
}

type ArtifactType int32

const (
	ArtifactType_INPUT  ArtifactType = 0
	ArtifactType_OUTPUT ArtifactType = 1
)

// Enum value maps for ArtifactType.
var (
	ArtifactType_name = map[int32]string{
		0: "INPUT",
		1: "OUTPUT",
	}
	ArtifactType_value = map[string]int32{
		"INPUT":  0,
		"OUTPUT": 1,
	}
)

func (x ArtifactType) Enum() *ArtifactType {
	p := new(ArtifactType)
	*p = x
	return p
}

func (x ArtifactType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArtifactType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[1].Descriptor()
}

func (ArtifactType) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[1]
}

func (x ArtifactType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArtifactType.Descriptor instead.
func (ArtifactType) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{1}
}

type RetryStrategy_RetryPolicy int32

const (
	RetryStrategy_RetryPolicyAlways    RetryStrategy_RetryPolicy = 0
	RetryStrategy_RetryPolicyOnFailure RetryStrategy_RetryPolicy = 1
	RetryStrategy_RetryPolicyOnError   RetryStrategy_RetryPolicy = 2
)

// Enum value maps for RetryStrategy_RetryPolicy.
var (
	RetryStrategy_RetryPolicy_name = map[int32]string{
		0: "RetryPolicyAlways",
		1: "RetryPolicyOnFailure",
		2: "RetryPolicyOnError",
	}
	RetryStrategy_RetryPolicy_value = map[string]int32{
		"RetryPolicyAlways":    0,
		"RetryPolicyOnFailure": 1,
		"RetryPolicyOnError":   2,
	}
)

func (x RetryStrategy_RetryPolicy) Enum() *RetryStrategy_RetryPolicy {
	p := new(RetryStrategy_RetryPolicy)
	*p = x
	return p
}

func (x RetryStrategy_RetryPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RetryStrategy_RetryPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[2].Descriptor()
}

func (RetryStrategy_RetryPolicy) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[2]
}

func (x RetryStrategy_RetryPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RetryStrategy_RetryPolicy.Descriptor instead.
func (RetryStrategy_RetryPolicy) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{2, 0}
}

type TimingConfig_Status int32

const (
	TimingConfig_Disable TimingConfig_Status = 0
	TimingConfig_Enable  TimingConfig_Status = 1
	TimingConfig_Unknown TimingConfig_Status = 2
)

// Enum value maps for TimingConfig_Status.
var (
	TimingConfig_Status_name = map[int32]string{
		0: "Disable",
		1: "Enable",
		2: "Unknown",
	}
	TimingConfig_Status_value = map[string]int32{
		"Disable": 0,
		"Enable":  1,
		"Unknown": 2,
	}
)

func (x TimingConfig_Status) Enum() *TimingConfig_Status {
	p := new(TimingConfig_Status)
	*p = x
	return p
}

func (x TimingConfig_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimingConfig_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[3].Descriptor()
}

func (TimingConfig_Status) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[3]
}

func (x TimingConfig_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimingConfig_Status.Descriptor instead.
func (TimingConfig_Status) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{7, 0}
}

type TimingConfig_ScheduleType int32

const (
	TimingConfig_Simple   TimingConfig_ScheduleType = 0
	TimingConfig_Advanced TimingConfig_ScheduleType = 1
)

// Enum value maps for TimingConfig_ScheduleType.
var (
	TimingConfig_ScheduleType_name = map[int32]string{
		0: "Simple",
		1: "Advanced",
	}
	TimingConfig_ScheduleType_value = map[string]int32{
		"Simple":   0,
		"Advanced": 1,
	}
)

func (x TimingConfig_ScheduleType) Enum() *TimingConfig_ScheduleType {
	p := new(TimingConfig_ScheduleType)
	*p = x
	return p
}

func (x TimingConfig_ScheduleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimingConfig_ScheduleType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[4].Descriptor()
}

func (TimingConfig_ScheduleType) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[4]
}

func (x TimingConfig_ScheduleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimingConfig_ScheduleType.Descriptor instead.
func (TimingConfig_ScheduleType) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{7, 1}
}

type Run_Status int32

const (
	Run_Failed    Run_Status = 0
	Run_Error     Run_Status = 1
	Run_Succeeded Run_Status = 2
	Run_Running   Run_Status = 3
)

// Enum value maps for Run_Status.
var (
	Run_Status_name = map[int32]string{
		0: "Failed",
		1: "Error",
		2: "Succeeded",
		3: "Running",
	}
	Run_Status_value = map[string]int32{
		"Failed":    0,
		"Error":     1,
		"Succeeded": 2,
		"Running":   3,
	}
)

func (x Run_Status) Enum() *Run_Status {
	p := new(Run_Status)
	*p = x
	return p
}

func (x Run_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Run_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[5].Descriptor()
}

func (Run_Status) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[5]
}

func (x Run_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Run_Status.Descriptor instead.
func (Run_Status) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{13, 0}
}

type RunV2_Status int32

const (
	RunV2_Failed    RunV2_Status = 0
	RunV2_Error     RunV2_Status = 1
	RunV2_Succeeded RunV2_Status = 2
	RunV2_Running   RunV2_Status = 3
)

// Enum value maps for RunV2_Status.
var (
	RunV2_Status_name = map[int32]string{
		0: "Failed",
		1: "Error",
		2: "Succeeded",
		3: "Running",
	}
	RunV2_Status_value = map[string]int32{
		"Failed":    0,
		"Error":     1,
		"Succeeded": 2,
		"Running":   3,
	}
)

func (x RunV2_Status) Enum() *RunV2_Status {
	p := new(RunV2_Status)
	*p = x
	return p
}

func (x RunV2_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunV2_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[6].Descriptor()
}

func (RunV2_Status) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[6]
}

func (x RunV2_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunV2_Status.Descriptor instead.
func (RunV2_Status) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{14, 0}
}

type Step_Phase int32

const (
	Step_Pending   Step_Phase = 0
	Step_Succeeded Step_Phase = 1
	Step_Running   Step_Phase = 2
	Step_Error     Step_Phase = 3
	Step_Skipped   Step_Phase = 4
	Step_Failed    Step_Phase = 5
)

// Enum value maps for Step_Phase.
var (
	Step_Phase_name = map[int32]string{
		0: "Pending",
		1: "Succeeded",
		2: "Running",
		3: "Error",
		4: "Skipped",
		5: "Failed",
	}
	Step_Phase_value = map[string]int32{
		"Pending":   0,
		"Succeeded": 1,
		"Running":   2,
		"Error":     3,
		"Skipped":   4,
		"Failed":    5,
	}
)

func (x Step_Phase) Enum() *Step_Phase {
	p := new(Step_Phase)
	*p = x
	return p
}

func (x Step_Phase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Step_Phase) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_pipeline_run_proto_enumTypes[7].Descriptor()
}

func (Step_Phase) Type() protoreflect.EnumType {
	return &file_proto_pipeline_run_proto_enumTypes[7]
}

func (x Step_Phase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Step_Phase.Descriptor instead.
func (Step_Phase) EnumDescriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{17, 0}
}

type Component struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"组件id"`
	 
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"组件名称"`
	 
	NameEn string `protobuf:"bytes,16,opt,name=name_en,json=nameEn,proto3" json:"name_en" description:"组件名称En"`
	 
	UseDefined bool `protobuf:"varint,3,opt,name=use_defined,json=useDefined,proto3" json:"use_defined" description:"区分自定义组件和系统组件"`
	 
	ComponentSource *TemplateSource `protobuf:"bytes,4,opt,name=component_source,json=componentSource,proto3" json:"component_source" description:"组件来源信息,保留字段后续拓展"`
	 
	Desc string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc" description:"组件描述"`
	 
	DescEn string `protobuf:"bytes,17,opt,name=desc_en,json=descEn,proto3" json:"desc_en" description:"组件描述En"`
	 
	Inputs map[string]string `protobuf:"bytes,6,rep,name=inputs,proto3" json:"inputs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件输入描述，key为输入名称，对应节点输入参数名，value为输入对应pod中的路径，对应节点输入参数路径"`
	 
	Outputs map[string]string `protobuf:"bytes,7,rep,name=outputs,proto3" json:"outputs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件输出描述，key为输出名称，对应节点输出参数名，value为输出对应pod中的路径，对应节点输出参数路径"`
	 
	Params map[string]string `protobuf:"bytes,8,rep,name=params,proto3" json:"params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件参数描述，key为参数名，对应节点输入参数（变量）的key，value参数默认值，对应节点输入参数（变量）的默认显示值"`
	 
	NodeSelector map[string]string `protobuf:"bytes,9,rep,name=node_selector,json=nodeSelector,proto3" json:"node_selector" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件pod labels设置，后端保留字段"`
	 
	Annotations map[string]string `protobuf:"bytes,10,rep,name=annotations,proto3" json:"annotations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件pod labels设置，后端保留字段"`
	 
	Labels map[string]string `protobuf:"bytes,11,rep,name=labels,proto3" json:"labels" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"组件pod labels设置，后端保留字段"`
	 
	Volume []*common.VolumeCfg `protobuf:"bytes,12,rep,name=volume,proto3" json:"volume" description:"持久卷配置，后端保留字段"`
	 
	Container *common.Container `protobuf:"bytes,13,opt,name=container,proto3" json:"container" description:"组件容器信息，后端保留字段"`
	 
	ComponentAdvancedConfig *ComponentAdvancedConfig `protobuf:"bytes,14,opt,name=component_advanced_config,json=componentAdvancedConfig,proto3" json:"component_advanced_config" description:"默认高级配置，后端保留字段"`
	 
	HostNetwork bool `protobuf:"varint,15,opt,name=host_network,json=hostNetwork,proto3" json:"host_network" description:"是否使用主机网络，后端保留字段"`
}

func (x *Component) Reset() {
	*x = Component{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Component) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Component) ProtoMessage() {}

func (x *Component) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Component.ProtoReflect.Descriptor instead.
func (*Component) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{0}
}

func (x *Component) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Component) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Component) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *Component) GetUseDefined() bool {
	if x != nil {
		return x.UseDefined
	}
	return false
}

func (x *Component) GetComponentSource() *TemplateSource {
	if x != nil {
		return x.ComponentSource
	}
	return nil
}

func (x *Component) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Component) GetDescEn() string {
	if x != nil {
		return x.DescEn
	}
	return ""
}

func (x *Component) GetInputs() map[string]string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Component) GetOutputs() map[string]string {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *Component) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *Component) GetNodeSelector() map[string]string {
	if x != nil {
		return x.NodeSelector
	}
	return nil
}

func (x *Component) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Component) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Component) GetVolume() []*common.VolumeCfg {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *Component) GetContainer() *common.Container {
	if x != nil {
		return x.Container
	}
	return nil
}

func (x *Component) GetComponentAdvancedConfig() *ComponentAdvancedConfig {
	if x != nil {
		return x.ComponentAdvancedConfig
	}
	return nil
}

func (x *Component) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

type ComponentAdvancedConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	EnableCached bool `protobuf:"varint,1,opt,name=enable_cached,json=enableCached,proto3" json:"enable_cached" description:"是否开启缓存功能"`
	 
	Parallelism int32 `protobuf:"varint,2,opt,name=parallelism,proto3" json:"parallelism" description:"最大并行数"`
	 
	ServiceAccountName string `protobuf:"bytes,3,opt,name=service_account_name,json=serviceAccountName,proto3" json:"service_account_name" description:"k8s serviceaccount"`
	 
	RetryStrategy *RetryStrategy `protobuf:"bytes,4,opt,name=retry_strategy,json=retryStrategy,proto3" json:"retry_strategy" description:"任务重试策略"`
}

func (x *ComponentAdvancedConfig) Reset() {
	*x = ComponentAdvancedConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentAdvancedConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentAdvancedConfig) ProtoMessage() {}

func (x *ComponentAdvancedConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentAdvancedConfig.ProtoReflect.Descriptor instead.
func (*ComponentAdvancedConfig) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{1}
}

func (x *ComponentAdvancedConfig) GetEnableCached() bool {
	if x != nil {
		return x.EnableCached
	}
	return false
}

func (x *ComponentAdvancedConfig) GetParallelism() int32 {
	if x != nil {
		return x.Parallelism
	}
	return 0
}

func (x *ComponentAdvancedConfig) GetServiceAccountName() string {
	if x != nil {
		return x.ServiceAccountName
	}
	return ""
}

func (x *ComponentAdvancedConfig) GetRetryStrategy() *RetryStrategy {
	if x != nil {
		return x.RetryStrategy
	}
	return nil
}

type RetryStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Limit int32 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit" description:"最大重试数"`
	 
	RetryPolicy RetryStrategy_RetryPolicy `protobuf:"varint,2,opt,name=retry_policy,json=retryPolicy,proto3,enum=pipeline.RetryStrategy_RetryPolicy" json:"retry_policy" description:"重试策略，RetryPolicyAlways = 0;RetryPolicyOnFailure = 1;RetryPolicyOnError = 2;"`
}

func (x *RetryStrategy) Reset() {
	*x = RetryStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryStrategy) ProtoMessage() {}

func (x *RetryStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryStrategy.ProtoReflect.Descriptor instead.
func (*RetryStrategy) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{2}
}

func (x *RetryStrategy) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *RetryStrategy) GetRetryPolicy() RetryStrategy_RetryPolicy {
	if x != nil {
		return x.RetryPolicy
	}
	return RetryStrategy_RetryPolicyAlways
}

type StoragePath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersistentVolumeClaim string `protobuf:"bytes,1,opt,name=persistent_volume_claim,json=persistentVolumeClaim,proto3" json:"persistent_volume_claim"`
	Path                  string `protobuf:"bytes,2,opt,name=path,proto3" json:"path"`
}

func (x *StoragePath) Reset() {
	*x = StoragePath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoragePath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoragePath) ProtoMessage() {}

func (x *StoragePath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoragePath.ProtoReflect.Descriptor instead.
func (*StoragePath) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{3}
}

func (x *StoragePath) GetPersistentVolumeClaim() string {
	if x != nil {
		return x.PersistentVolumeClaim
	}
	return ""
}

func (x *StoragePath) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type TaskFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Nodes []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes" description:"节点信息"`
	 
	Dependencies []*Dependency `protobuf:"bytes,2,rep,name=dependencies,proto3" json:"dependencies" description:"节点间依赖信息"`
	 
	HostNetwork bool `protobuf:"varint,3,opt,name=HostNetwork,proto3" json:"HostNetwork" description:"任务pod是否使用主机网络"`
}

func (x *TaskFlow) Reset() {
	*x = TaskFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskFlow) ProtoMessage() {}

func (x *TaskFlow) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskFlow.ProtoReflect.Descriptor instead.
func (*TaskFlow) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{4}
}

func (x *TaskFlow) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *TaskFlow) GetDependencies() []*Dependency {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *TaskFlow) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

type TaskFlowV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Nodes []*NodeV2 `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes" description:"节点信息"`
	 
	Dependencies []*Dependency `protobuf:"bytes,2,rep,name=dependencies,proto3" json:"dependencies" description:"节点间依赖信息"`
	 
	HostNetwork bool `protobuf:"varint,3,opt,name=host_network,json=hostNetwork,proto3" json:"host_network" description:"任务pod是否使用主机网络"`
}

func (x *TaskFlowV2) Reset() {
	*x = TaskFlowV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskFlowV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskFlowV2) ProtoMessage() {}

func (x *TaskFlowV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskFlowV2.ProtoReflect.Descriptor instead.
func (*TaskFlowV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{5}
}

func (x *TaskFlowV2) GetNodes() []*NodeV2 {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *TaskFlowV2) GetDependencies() []*Dependency {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *TaskFlowV2) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

type Dependency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	NodeId string `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id" description:"节点id"`
	 
	DepNodeId []string `protobuf:"bytes,2,rep,name=dep_node_id,json=depNodeId,proto3" json:"dep_node_id" description:"该节点依赖的节点id集合，依赖的节点即为该节点的上游节点，即只有id集合对应的节点运行完成才会执行该节点"`
}

func (x *Dependency) Reset() {
	*x = Dependency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dependency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dependency) ProtoMessage() {}

func (x *Dependency) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dependency.ProtoReflect.Descriptor instead.
func (*Dependency) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{6}
}

func (x *Dependency) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *Dependency) GetDepNodeId() []string {
	if x != nil {
		return x.DepNodeId
	}
	return nil
}

type TimingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Cron string `protobuf:"bytes,1,opt,name=cron,proto3" json:"cron" description:"定时调度cron表达式"`
	 
	Status TimingConfig_Status `protobuf:"varint,2,opt,name=status,proto3,enum=pipeline.TimingConfig_Status" json:"status" description:"定时器状态 0: 关闭 1:启动 即定时任务是否开启"`
	 
	ScheduleType TimingConfig_ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=pipeline.TimingConfig_ScheduleType" json:"schedule_type" description:"调度设置 0：基础 1：高级 用于前端展示"` //simple, advanced
	 
	MaximumConcurrent int32 `protobuf:"varint,4,opt,name=maximum_concurrent,json=maximumConcurrent,proto3" json:"maximum_concurrent" description:"最大并行度"`
}

func (x *TimingConfig) Reset() {
	*x = TimingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimingConfig) ProtoMessage() {}

func (x *TimingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimingConfig.ProtoReflect.Descriptor instead.
func (*TimingConfig) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{7}
}

func (x *TimingConfig) GetCron() string {
	if x != nil {
		return x.Cron
	}
	return ""
}

func (x *TimingConfig) GetStatus() TimingConfig_Status {
	if x != nil {
		return x.Status
	}
	return TimingConfig_Disable
}

func (x *TimingConfig) GetScheduleType() TimingConfig_ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return TimingConfig_Simple
}

func (x *TimingConfig) GetMaximumConcurrent() int32 {
	if x != nil {
		return x.MaximumConcurrent
	}
	return 0
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"节点id，需要由前端或任务提交方随机生成"`
	 
	TemplateSource *TemplateSource `protobuf:"bytes,2,opt,name=template_source,json=templateSource,proto3" json:"template_source" description:"存储模板相关信息"`
	 
	Inputs []*ArtifactConfig `protobuf:"bytes,3,rep,name=inputs,proto3" json:"inputs" description:"节点输入制品相关信息"`
	 
	Outputs []*ArtifactConfig `protobuf:"bytes,4,rep,name=outputs,proto3" json:"outputs" description:"节点输出制品相关信息"`
	 
	Params map[string]string `protobuf:"bytes,5,rep,name=params,proto3" json:"params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"节点参数"`
	 
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name" description:"节点名称"`
	 
	Position *Position `protobuf:"bytes,7,opt,name=position,proto3" json:"position" description:"节点位置，用于前端展示节点位置"`
	 
	Container *common.Container `protobuf:"bytes,8,opt,name=container,proto3" json:"container" description:"节点容器信息，自定义组件需要用户填写容器相关信息，其他组件由后端直接根据组件信息填充"`
	 
	HostNetwork bool `protobuf:"varint,9,opt,name=host_network,json=hostNetwork,proto3" json:"host_network" description:"是否使用主机网络，后端相关参数"`
	 
	NodeSelector map[string]string `protobuf:"bytes,10,rep,name=node_selector,json=nodeSelector,proto3" json:"node_selector" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"根据label选择限制任务执行节点，后端相关参数"`
	 
	Annotations map[string]string `protobuf:"bytes,11,rep,name=annotations,proto3" json:"annotations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"任务pod annotations设置，后端相关参数"`
	 
	Labels map[string]string `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"任务pod labels设置，后端相关参数"`
	 
	AdvancedConfig *ComponentAdvancedConfig `protobuf:"bytes,13,opt,name=advanced_config,json=advancedConfig,proto3" json:"advanced_config" description:"工作流高级配置，后端相关参数"`
	 
	Volume []*common.VolumeCfg `protobuf:"bytes,14,rep,name=volume,proto3" json:"volume" description:"持久卷配置，后端保留字段"`
	 
	IsPowerRule bool `protobuf:"varint,15,opt,name=is_power_rule,json=isPowerRule,proto3" json:"is_power_rule" description:"资源配置来源(规格实例/高级配置)"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{8}
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetTemplateSource() *TemplateSource {
	if x != nil {
		return x.TemplateSource
	}
	return nil
}

func (x *Node) GetInputs() []*ArtifactConfig {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Node) GetOutputs() []*ArtifactConfig {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *Node) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetPosition() *Position {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *Node) GetContainer() *common.Container {
	if x != nil {
		return x.Container
	}
	return nil
}

func (x *Node) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

func (x *Node) GetNodeSelector() map[string]string {
	if x != nil {
		return x.NodeSelector
	}
	return nil
}

func (x *Node) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Node) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Node) GetAdvancedConfig() *ComponentAdvancedConfig {
	if x != nil {
		return x.AdvancedConfig
	}
	return nil
}

func (x *Node) GetVolume() []*common.VolumeCfg {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *Node) GetIsPowerRule() bool {
	if x != nil {
		return x.IsPowerRule
	}
	return false
}

type NodeV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"节点id，需要由前端或任务提交方随机生成"`
	 
	TemplateSource *TemplateSource `protobuf:"bytes,2,opt,name=template_source,json=templateSource,proto3" json:"template_source" description:"存储模板相关信息"`
	 
	Inputs []*ArtifactConfig `protobuf:"bytes,3,rep,name=inputs,proto3" json:"inputs" description:"节点输入制品相关信息"`
	 
	Outputs []*ArtifactConfig `protobuf:"bytes,4,rep,name=outputs,proto3" json:"outputs" description:"节点输出制品相关信息"`
	 
	Params map[string]string `protobuf:"bytes,5,rep,name=params,proto3" json:"params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"节点参数"`
	 
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name" description:"节点名称"`
	 
	Position *Position `protobuf:"bytes,7,opt,name=position,proto3" json:"position" description:"节点位置，用于前端展示节点位置"`
	 
	Container *common.Container `protobuf:"bytes,8,opt,name=container,proto3" json:"container" description:"节点容器信息，自定义组件需要用户填写容器相关信息，其他组件由后端直接根据组件信息填充"`
	 
	HostNetwork bool `protobuf:"varint,9,opt,name=host_network,json=hostNetwork,proto3" json:"host_network" description:"是否使用主机网络，后端相关参数"`
	 
	AdvancedConfig *ComponentAdvancedConfig `protobuf:"bytes,13,opt,name=advanced_config,json=advancedConfig,proto3" json:"advanced_config" description:"工作流高级配置，后端相关参数"`
	 
	Volume        []*common.VolumeCfg    `protobuf:"bytes,14,rep,name=volume,proto3" json:"volume" description:"持久卷配置，后端保留字段"`
	UnifyResource *serving.UnifyResource `protobuf:"bytes,15,opt,name=unify_resource,json=unifyResource,proto3" json:"unify_resource"`
}

func (x *NodeV2) Reset() {
	*x = NodeV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeV2) ProtoMessage() {}

func (x *NodeV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeV2.ProtoReflect.Descriptor instead.
func (*NodeV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{9}
}

func (x *NodeV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NodeV2) GetTemplateSource() *TemplateSource {
	if x != nil {
		return x.TemplateSource
	}
	return nil
}

func (x *NodeV2) GetInputs() []*ArtifactConfig {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *NodeV2) GetOutputs() []*ArtifactConfig {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *NodeV2) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *NodeV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeV2) GetPosition() *Position {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *NodeV2) GetContainer() *common.Container {
	if x != nil {
		return x.Container
	}
	return nil
}

func (x *NodeV2) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

func (x *NodeV2) GetAdvancedConfig() *ComponentAdvancedConfig {
	if x != nil {
		return x.AdvancedConfig
	}
	return nil
}

func (x *NodeV2) GetVolume() []*common.VolumeCfg {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *NodeV2) GetUnifyResource() *serving.UnifyResource {
	if x != nil {
		return x.UnifyResource
	}
	return nil
}

type Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x" description:"x轴坐标"`
	 
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y" description:"y轴坐标"`
}

func (x *Position) Reset() {
	*x = Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position) ProtoMessage() {}

func (x *Position) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position.ProtoReflect.Descriptor instead.
func (*Position) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{10}
}

func (x *Position) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Position) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type ArtifactConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"制品名称"`
	 
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path" description:"制品在容器中的路径，对于节点输入，工作流会在该节点执行前将在From指定的上一节点的输出移动到该路径，对于节点输出，工作流会在该节点执行结束后，将容器中的该路径作为输出"`
	 
	From *From `protobuf:"bytes,3,opt,name=from,proto3" json:"from" description:"仅作用于节点输入，指定节点的输入来源于上游节点的某个输出"`
	 
	Type ArtifactType `protobuf:"varint,4,opt,name=type,proto3,enum=pipeline.ArtifactType" json:"type" description:"制品类型 INPUT = 0;OUTPUT = 1"`
}

func (x *ArtifactConfig) Reset() {
	*x = ArtifactConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArtifactConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtifactConfig) ProtoMessage() {}

func (x *ArtifactConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtifactConfig.ProtoReflect.Descriptor instead.
func (*ArtifactConfig) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{11}
}

func (x *ArtifactConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ArtifactConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ArtifactConfig) GetFrom() *From {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *ArtifactConfig) GetType() ArtifactType {
	if x != nil {
		return x.Type
	}
	return ArtifactType_INPUT
}

type From struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	NodeId string `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id" description:"来源节点的id，必须是依赖节点"`
	 
	ArtifactName string `protobuf:"bytes,2,opt,name=artifact_name,json=artifactName,proto3" json:"artifact_name" description:"来源节点的输出的制品名称，上游节点可能会有多个输出制品，需要指定具体输出制品的名称"`
}

func (x *From) Reset() {
	*x = From{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *From) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*From) ProtoMessage() {}

func (x *From) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use From.ProtoReflect.Descriptor instead.
func (*From) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{12}
}

func (x *From) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *From) GetArtifactName() string {
	if x != nil {
		return x.ArtifactName
	}
	return ""
}

type Run struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"run id"`
	 
	ProjectId string `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
	 
	TaskSource *TaskSource `protobuf:"bytes,3,opt,name=task_source,json=taskSource,proto3" json:"task_source" description:"源任务信息"`
	 
	TaskFlow *TaskFlow `protobuf:"bytes,4,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"任务流程图"`
	 
	StartTime int64 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time" description:"运行开始时间"`
	 
	EndTime int64 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time" description:"运行结束时间"`
	 
	Status Run_Status `protobuf:"varint,7,opt,name=status,proto3,enum=pipeline.Run_Status" json:"status" description:"运行当前状态"`
	 
	ScheduledTime int64 `protobuf:"varint,8,opt,name=scheduled_time,json=scheduledTime,proto3" json:"scheduled_time" description:"运行调度时间"`
	 
	Steps      []*Step `protobuf:"bytes,9,rep,name=steps,proto3" json:"steps" description:"运行步骤信息"`
	CreateUser string  `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" json:"create_user"`
}

func (x *Run) Reset() {
	*x = Run{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Run) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Run) ProtoMessage() {}

func (x *Run) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Run.ProtoReflect.Descriptor instead.
func (*Run) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{13}
}

func (x *Run) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Run) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Run) GetTaskSource() *TaskSource {
	if x != nil {
		return x.TaskSource
	}
	return nil
}

func (x *Run) GetTaskFlow() *TaskFlow {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *Run) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Run) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Run) GetStatus() Run_Status {
	if x != nil {
		return x.Status
	}
	return Run_Failed
}

func (x *Run) GetScheduledTime() int64 {
	if x != nil {
		return x.ScheduledTime
	}
	return 0
}

func (x *Run) GetSteps() []*Step {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *Run) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

type RunV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"run id"`
	 
	ProjectId string `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
	 
	TaskSource *TaskSource `protobuf:"bytes,3,opt,name=task_source,json=taskSource,proto3" json:"task_source" description:"源任务信息"`
	 
	TaskFlow *TaskFlowV2 `protobuf:"bytes,4,opt,name=task_flow,json=taskFlow,proto3" json:"task_flow" description:"任务流程图"`
	 
	StartTime int64 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time" description:"运行开始时间"`
	 
	EndTime int64 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time" description:"运行结束时间"`
	 
	Status RunV2_Status `protobuf:"varint,7,opt,name=status,proto3,enum=pipeline.RunV2_Status" json:"status" description:"运行当前状态"`
	 
	ScheduledTime int64 `protobuf:"varint,8,opt,name=scheduled_time,json=scheduledTime,proto3" json:"scheduled_time" description:"运行调度时间"`
	 
	Steps      []*Step `protobuf:"bytes,9,rep,name=steps,proto3" json:"steps" description:"运行步骤信息"`
	CreateUser string  `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" json:"create_user"`
}

func (x *RunV2) Reset() {
	*x = RunV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunV2) ProtoMessage() {}

func (x *RunV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunV2.ProtoReflect.Descriptor instead.
func (*RunV2) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{14}
}

func (x *RunV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RunV2) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RunV2) GetTaskSource() *TaskSource {
	if x != nil {
		return x.TaskSource
	}
	return nil
}

func (x *RunV2) GetTaskFlow() *TaskFlowV2 {
	if x != nil {
		return x.TaskFlow
	}
	return nil
}

func (x *RunV2) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *RunV2) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *RunV2) GetStatus() RunV2_Status {
	if x != nil {
		return x.Status
	}
	return RunV2_Failed
}

func (x *RunV2) GetScheduledTime() int64 {
	if x != nil {
		return x.ScheduledTime
	}
	return 0
}

func (x *RunV2) GetSteps() []*Step {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *RunV2) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

type TaskSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	SourceType TaskSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=pipeline.TaskSourceType" json:"source_type" description:"任务类型 1:工作流 2:模型训练 3:模型评估 4:语料加工"`
	 
	SourceId string `protobuf:"bytes,2,opt,name=source_id,json=sourceId,proto3" json:"source_id" description:"源任务id"`
	 
	SourceName string `protobuf:"bytes,3,opt,name=source_name,json=sourceName,proto3" json:"source_name" description:"源任务名称"`
	 
	SourceInfo string `protobuf:"bytes,4,opt,name=source_info,json=sourceInfo,proto3" json:"source_info" description:"源任务相关的信息，用于任务提交方存储各源任务相关的独特信息"`
}

func (x *TaskSource) Reset() {
	*x = TaskSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSource) ProtoMessage() {}

func (x *TaskSource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSource.ProtoReflect.Descriptor instead.
func (*TaskSource) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{15}
}

func (x *TaskSource) GetSourceType() TaskSourceType {
	if x != nil {
		return x.SourceType
	}
	return TaskSourceType_NONE
}

func (x *TaskSource) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *TaskSource) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *TaskSource) GetSourceInfo() string {
	if x != nil {
		return x.SourceInfo
	}
	return ""
}

type TemplateSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	TemplateType TaskSourceType `protobuf:"varint,1,opt,name=template_type,json=templateType,proto3,enum=pipeline.TaskSourceType" json:"template_type" description:"模板类型 PIPELINE = 1; MODEL_TRAINING = 2; MODEL_EVALUATE = 3; CORPUS_PROCESSING = 4"`
	 
	SourceId string `protobuf:"bytes,2,opt,name=source_id,json=sourceId,proto3" json:"source_id" description:"模板id"`
	 
	SourceName string `protobuf:"bytes,3,opt,name=source_name,json=sourceName,proto3" json:"source_name" description:"模板名称"`
	 
	SourceInfo string `protobuf:"bytes,4,opt,name=source_info,json=sourceInfo,proto3" json:"source_info" description:"模板相关的信息，用于任务提交方存储各模板相关的独特信息"`
}

func (x *TemplateSource) Reset() {
	*x = TemplateSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateSource) ProtoMessage() {}

func (x *TemplateSource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateSource.ProtoReflect.Descriptor instead.
func (*TemplateSource) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{16}
}

func (x *TemplateSource) GetTemplateType() TaskSourceType {
	if x != nil {
		return x.TemplateType
	}
	return TaskSourceType_NONE
}

func (x *TemplateSource) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *TemplateSource) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *TemplateSource) GetSourceInfo() string {
	if x != nil {
		return x.SourceInfo
	}
	return ""
}

type Step struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId    string            `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
	Name      string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Phase     Step_Phase        `protobuf:"varint,3,opt,name=phase,proto3,enum=pipeline.Step_Phase" json:"phase"`
	StartTime int64             `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime   int64             `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Outputs   []*Artifact       `protobuf:"bytes,6,rep,name=outputs,proto3" json:"outputs"`
	Inputs    []*Artifact       `protobuf:"bytes,7,rep,name=inputs,proto3" json:"inputs"`
	Params    map[string]string `protobuf:"bytes,11,rep,name=params,proto3" json:"params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Metrics   map[string]string `protobuf:"bytes,12,rep,name=metrics,proto3" json:"metrics" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Step) Reset() {
	*x = Step{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Step) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Step) ProtoMessage() {}

func (x *Step) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Step.ProtoReflect.Descriptor instead.
func (*Step) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{17}
}

func (x *Step) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *Step) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Step) GetPhase() Step_Phase {
	if x != nil {
		return x.Phase
	}
	return Step_Pending
}

func (x *Step) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Step) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Step) GetOutputs() []*Artifact {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *Step) GetInputs() []*Artifact {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Step) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *Step) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type Artifact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	ContainerPath string       `protobuf:"bytes,2,opt,name=container_path,json=containerPath,proto3" json:"container_path"`
	S3Path        string       `protobuf:"bytes,3,opt,name=s3_path,json=s3Path,proto3" json:"s3_path"`
	Type          ArtifactType `protobuf:"varint,4,opt,name=type,proto3,enum=pipeline.ArtifactType" json:"type"`
	StoragePath   *StoragePath `protobuf:"bytes,5,opt,name=storage_path,json=storagePath,proto3" json:"storage_path"`
}

func (x *Artifact) Reset() {
	*x = Artifact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Artifact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Artifact) ProtoMessage() {}

func (x *Artifact) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Artifact.ProtoReflect.Descriptor instead.
func (*Artifact) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{18}
}

func (x *Artifact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Artifact) GetContainerPath() string {
	if x != nil {
		return x.ContainerPath
	}
	return ""
}

func (x *Artifact) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

func (x *Artifact) GetType() ArtifactType {
	if x != nil {
		return x.Type
	}
	return ArtifactType_INPUT
}

func (x *Artifact) GetStoragePath() *StoragePath {
	if x != nil {
		return x.StoragePath
	}
	return nil
}

type PageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize int32  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	OrderBy  string `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	IsDesc   bool   `protobuf:"varint,4,opt,name=is_desc,json=isDesc,proto3" json:"is_desc"`
}

func (x *PageReq) Reset() {
	*x = PageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pipeline_run_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageReq) ProtoMessage() {}

func (x *PageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pipeline_run_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageReq.ProtoReflect.Descriptor instead.
func (*PageReq) Descriptor() ([]byte, []int) {
	return file_proto_pipeline_run_proto_rawDescGZIP(), []int{19}
}

func (x *PageReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageReq) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *PageReq) GetIsDesc() bool {
	if x != nil {
		return x.IsDesc
	}
	return false
}

var File_proto_pipeline_run_proto protoreflect.FileDescriptor

var file_proto_pipeline_run_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2f, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x09, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x12,
	0x43, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x63,
	0x5f, 0x65, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x73, 0x63, 0x45,
	0x6e, 0x12, 0x37, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x07, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x4a, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6e,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x46, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x06,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x52,
	0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x19, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68,
	0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x1a, 0x39, 0x0a, 0x0b, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11,
	0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a,
	0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd2, 0x01, 0x0a, 0x17, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x72,
	0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x6d, 0x12, 0x30, 0x0a, 0x14, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a,
	0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0d,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x22, 0xc5, 0x01,
	0x0a, 0x0d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x56, 0x0a,
	0x0b, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x41, 0x6c, 0x77, 0x61, 0x79,
	0x73, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x02, 0x22, 0x59, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x22, 0x8c, 0x01, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x24, 0x0a,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x48, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x48, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22,
	0x91, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x32, 0x12, 0x26,
	0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x32, 0x52,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x22, 0x45, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x65,
	0x70, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x64, 0x65, 0x70, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0xac, 0x02, 0x0a, 0x0c, 0x54,
	0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x72, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x6e, 0x12,
	0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61,
	0x78, 0x69, 0x6d, 0x75, 0x6d, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x22,
	0x2e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x02, 0x22,
	0x28, 0x0a, 0x0c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x41,
	0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x01, 0x22, 0xdb, 0x07, 0x0a, 0x04, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x41, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x45, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x41,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x32, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0e, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x29, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x43, 0x66, 0x67, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x69, 0x73, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x80, 0x05, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65,
	0x56, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x41, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x32, 0x2e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x6f,
	0x73, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x64, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x12, 0x3d, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x66, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x0d, 0x75, 0x6e, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a,
	0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26, 0x0a, 0x08, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x79, 0x22, 0x88, 0x01, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66,
	0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x44, 0x0a,
	0x04, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0xaf, 0x03, 0x0a, 0x03, 0x52, 0x75, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x65, 0x70,
	0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x22, 0x3b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x10, 0x03, 0x22, 0xb5, 0x03, 0x0a, 0x05, 0x52, 0x75, 0x6e, 0x56, 0x32, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x32, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75,
	0x6e, 0x56, 0x32, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x22, 0x3b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x22, 0xa6, 0x01,
	0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xae, 0x01, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xab, 0x04, 0x0a, 0x04, 0x53, 0x74, 0x65, 0x70,
	0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x2a, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x74,
	0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x32, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x35, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74,
	0x65, 0x70, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x54, 0x0a, 0x05, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64,
	0x65, 0x64, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10,
	0x02, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x10, 0x05, 0x22, 0xc4, 0x01, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61,
	0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x17, 0x0a,
	0x07, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x52,
	0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x6e, 0x0a, 0x07,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x2a, 0xa6, 0x01, 0x0a,
	0x0e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x49, 0x50,
	0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x44, 0x45, 0x4c,
	0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12,
	0x15, 0x0a, 0x11, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53,
	0x5f, 0x45, 0x56, 0x41, 0x4c, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x4c,
	0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x6c, 0x4f, 0x41, 0x44, 0x45, 0x52, 0x10, 0x06, 0x12, 0x16, 0x0a,
	0x12, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x5a, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x07, 0x2a, 0x25, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54, 0x10, 0x01, 0x42, 0x35, 0x5a, 0x33,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_pipeline_run_proto_rawDescOnce sync.Once
	file_proto_pipeline_run_proto_rawDescData = file_proto_pipeline_run_proto_rawDesc
)

func file_proto_pipeline_run_proto_rawDescGZIP() []byte {
	file_proto_pipeline_run_proto_rawDescOnce.Do(func() {
		file_proto_pipeline_run_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pipeline_run_proto_rawDescData)
	})
	return file_proto_pipeline_run_proto_rawDescData
}

var file_proto_pipeline_run_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_proto_pipeline_run_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_proto_pipeline_run_proto_goTypes = []interface{}{
	(TaskSourceType)(0),             // 0: pipeline.TaskSourceType
	(ArtifactType)(0),               // 1: pipeline.ArtifactType
	(RetryStrategy_RetryPolicy)(0),  // 2: pipeline.RetryStrategy.RetryPolicy
	(TimingConfig_Status)(0),        // 3: pipeline.TimingConfig.Status
	(TimingConfig_ScheduleType)(0),  // 4: pipeline.TimingConfig.ScheduleType
	(Run_Status)(0),                 // 5: pipeline.Run.Status
	(RunV2_Status)(0),               // 6: pipeline.RunV2.Status
	(Step_Phase)(0),                 // 7: pipeline.Step.Phase
	(*Component)(nil),               // 8: pipeline.Component
	(*ComponentAdvancedConfig)(nil), // 9: pipeline.ComponentAdvancedConfig
	(*RetryStrategy)(nil),           // 10: pipeline.RetryStrategy
	(*StoragePath)(nil),             // 11: pipeline.StoragePath
	(*TaskFlow)(nil),                // 12: pipeline.TaskFlow
	(*TaskFlowV2)(nil),              // 13: pipeline.TaskFlowV2
	(*Dependency)(nil),              // 14: pipeline.Dependency
	(*TimingConfig)(nil),            // 15: pipeline.TimingConfig
	(*Node)(nil),                    // 16: pipeline.Node
	(*NodeV2)(nil),                  // 17: pipeline.NodeV2
	(*Position)(nil),                // 18: pipeline.Position
	(*ArtifactConfig)(nil),          // 19: pipeline.ArtifactConfig
	(*From)(nil),                    // 20: pipeline.From
	(*Run)(nil),                     // 21: pipeline.Run
	(*RunV2)(nil),                   // 22: pipeline.RunV2
	(*TaskSource)(nil),              // 23: pipeline.TaskSource
	(*TemplateSource)(nil),          // 24: pipeline.TemplateSource
	(*Step)(nil),                    // 25: pipeline.Step
	(*Artifact)(nil),                // 26: pipeline.Artifact
	(*PageReq)(nil),                 // 27: pipeline.PageReq
	nil,                             // 28: pipeline.Component.InputsEntry
	nil,                             // 29: pipeline.Component.OutputsEntry
	nil,                             // 30: pipeline.Component.ParamsEntry
	nil,                             // 31: pipeline.Component.NodeSelectorEntry
	nil,                             // 32: pipeline.Component.AnnotationsEntry
	nil,                             // 33: pipeline.Component.LabelsEntry
	nil,                             // 34: pipeline.Node.ParamsEntry
	nil,                             // 35: pipeline.Node.NodeSelectorEntry
	nil,                             // 36: pipeline.Node.AnnotationsEntry
	nil,                             // 37: pipeline.Node.LabelsEntry
	nil,                             // 38: pipeline.NodeV2.ParamsEntry
	nil,                             // 39: pipeline.Step.ParamsEntry
	nil,                             // 40: pipeline.Step.MetricsEntry
	(*common.VolumeCfg)(nil),        // 41: common.VolumeCfg
	(*common.Container)(nil),        // 42: common.Container
	(*serving.UnifyResource)(nil),   // 43: serving.UnifyResource
}
var file_proto_pipeline_run_proto_depIdxs = []int32{
	24, // 0: pipeline.Component.component_source:type_name -> pipeline.TemplateSource
	28, // 1: pipeline.Component.inputs:type_name -> pipeline.Component.InputsEntry
	29, // 2: pipeline.Component.outputs:type_name -> pipeline.Component.OutputsEntry
	30, // 3: pipeline.Component.params:type_name -> pipeline.Component.ParamsEntry
	31, // 4: pipeline.Component.node_selector:type_name -> pipeline.Component.NodeSelectorEntry
	32, // 5: pipeline.Component.annotations:type_name -> pipeline.Component.AnnotationsEntry
	33, // 6: pipeline.Component.labels:type_name -> pipeline.Component.LabelsEntry
	41, // 7: pipeline.Component.volume:type_name -> common.VolumeCfg
	42, // 8: pipeline.Component.container:type_name -> common.Container
	9,  // 9: pipeline.Component.component_advanced_config:type_name -> pipeline.ComponentAdvancedConfig
	10, // 10: pipeline.ComponentAdvancedConfig.retry_strategy:type_name -> pipeline.RetryStrategy
	2,  // 11: pipeline.RetryStrategy.retry_policy:type_name -> pipeline.RetryStrategy.RetryPolicy
	16, // 12: pipeline.TaskFlow.nodes:type_name -> pipeline.Node
	14, // 13: pipeline.TaskFlow.dependencies:type_name -> pipeline.Dependency
	17, // 14: pipeline.TaskFlowV2.nodes:type_name -> pipeline.NodeV2
	14, // 15: pipeline.TaskFlowV2.dependencies:type_name -> pipeline.Dependency
	3,  // 16: pipeline.TimingConfig.status:type_name -> pipeline.TimingConfig.Status
	4,  // 17: pipeline.TimingConfig.schedule_type:type_name -> pipeline.TimingConfig.ScheduleType
	24, // 18: pipeline.Node.template_source:type_name -> pipeline.TemplateSource
	19, // 19: pipeline.Node.inputs:type_name -> pipeline.ArtifactConfig
	19, // 20: pipeline.Node.outputs:type_name -> pipeline.ArtifactConfig
	34, // 21: pipeline.Node.params:type_name -> pipeline.Node.ParamsEntry
	18, // 22: pipeline.Node.position:type_name -> pipeline.Position
	42, // 23: pipeline.Node.container:type_name -> common.Container
	35, // 24: pipeline.Node.node_selector:type_name -> pipeline.Node.NodeSelectorEntry
	36, // 25: pipeline.Node.annotations:type_name -> pipeline.Node.AnnotationsEntry
	37, // 26: pipeline.Node.labels:type_name -> pipeline.Node.LabelsEntry
	9,  // 27: pipeline.Node.advanced_config:type_name -> pipeline.ComponentAdvancedConfig
	41, // 28: pipeline.Node.volume:type_name -> common.VolumeCfg
	24, // 29: pipeline.NodeV2.template_source:type_name -> pipeline.TemplateSource
	19, // 30: pipeline.NodeV2.inputs:type_name -> pipeline.ArtifactConfig
	19, // 31: pipeline.NodeV2.outputs:type_name -> pipeline.ArtifactConfig
	38, // 32: pipeline.NodeV2.params:type_name -> pipeline.NodeV2.ParamsEntry
	18, // 33: pipeline.NodeV2.position:type_name -> pipeline.Position
	42, // 34: pipeline.NodeV2.container:type_name -> common.Container
	9,  // 35: pipeline.NodeV2.advanced_config:type_name -> pipeline.ComponentAdvancedConfig
	41, // 36: pipeline.NodeV2.volume:type_name -> common.VolumeCfg
	43, // 37: pipeline.NodeV2.unify_resource:type_name -> serving.UnifyResource
	20, // 38: pipeline.ArtifactConfig.from:type_name -> pipeline.From
	1,  // 39: pipeline.ArtifactConfig.type:type_name -> pipeline.ArtifactType
	23, // 40: pipeline.Run.task_source:type_name -> pipeline.TaskSource
	12, // 41: pipeline.Run.task_flow:type_name -> pipeline.TaskFlow
	5,  // 42: pipeline.Run.status:type_name -> pipeline.Run.Status
	25, // 43: pipeline.Run.steps:type_name -> pipeline.Step
	23, // 44: pipeline.RunV2.task_source:type_name -> pipeline.TaskSource
	13, // 45: pipeline.RunV2.task_flow:type_name -> pipeline.TaskFlowV2
	6,  // 46: pipeline.RunV2.status:type_name -> pipeline.RunV2.Status
	25, // 47: pipeline.RunV2.steps:type_name -> pipeline.Step
	0,  // 48: pipeline.TaskSource.source_type:type_name -> pipeline.TaskSourceType
	0,  // 49: pipeline.TemplateSource.template_type:type_name -> pipeline.TaskSourceType
	7,  // 50: pipeline.Step.phase:type_name -> pipeline.Step.Phase
	26, // 51: pipeline.Step.outputs:type_name -> pipeline.Artifact
	26, // 52: pipeline.Step.inputs:type_name -> pipeline.Artifact
	39, // 53: pipeline.Step.params:type_name -> pipeline.Step.ParamsEntry
	40, // 54: pipeline.Step.metrics:type_name -> pipeline.Step.MetricsEntry
	1,  // 55: pipeline.Artifact.type:type_name -> pipeline.ArtifactType
	11, // 56: pipeline.Artifact.storage_path:type_name -> pipeline.StoragePath
	57, // [57:57] is the sub-list for method output_type
	57, // [57:57] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_proto_pipeline_run_proto_init() }
func file_proto_pipeline_run_proto_init() {
	if File_proto_pipeline_run_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_pipeline_run_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Component); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComponentAdvancedConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoragePath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskFlowV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dependency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArtifactConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*From); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Run); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Step); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Artifact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pipeline_run_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pipeline_run_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_pipeline_run_proto_goTypes,
		DependencyIndexes: file_proto_pipeline_run_proto_depIdxs,
		EnumInfos:         file_proto_pipeline_run_proto_enumTypes,
		MessageInfos:      file_proto_pipeline_run_proto_msgTypes,
	}.Build()
	File_proto_pipeline_run_proto = out.File
	file_proto_pipeline_run_proto_rawDesc = nil
	file_proto_pipeline_run_proto_goTypes = nil
	file_proto_pipeline_run_proto_depIdxs = nil
}
