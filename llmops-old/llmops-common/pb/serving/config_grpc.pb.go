// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/serving/config.proto

package serving

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "transwarp.io/aip/llmops-common/pb/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MLOpsConfigService_ExternalConfig_FullMethodName = "/serving.MLOpsConfigService/ExternalConfig"
)

// MLOpsConfigServiceClient is the client API for MLOpsConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MLOpsConfigServiceClient interface {
	ExternalConfig(ctx context.Context, in *common.EmptyRsp, opts ...grpc.CallOption) (*MlopsExternalConfig, error)
}

type mLOpsConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMLOpsConfigServiceClient(cc grpc.ClientConnInterface) MLOpsConfigServiceClient {
	return &mLOpsConfigServiceClient{cc}
}

func (c *mLOpsConfigServiceClient) ExternalConfig(ctx context.Context, in *common.EmptyRsp, opts ...grpc.CallOption) (*MlopsExternalConfig, error) {
	out := new(MlopsExternalConfig)
	err := c.cc.Invoke(ctx, MLOpsConfigService_ExternalConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MLOpsConfigServiceServer is the server API for MLOpsConfigService service.
// All implementations must embed UnimplementedMLOpsConfigServiceServer
// for forward compatibility
type MLOpsConfigServiceServer interface {
	ExternalConfig(context.Context, *common.EmptyRsp) (*MlopsExternalConfig, error)
	mustEmbedUnimplementedMLOpsConfigServiceServer()
}

// UnimplementedMLOpsConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMLOpsConfigServiceServer struct {
}

func (UnimplementedMLOpsConfigServiceServer) ExternalConfig(context.Context, *common.EmptyRsp) (*MlopsExternalConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExternalConfig not implemented")
}
func (UnimplementedMLOpsConfigServiceServer) mustEmbedUnimplementedMLOpsConfigServiceServer() {}

// UnsafeMLOpsConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MLOpsConfigServiceServer will
// result in compilation errors.
type UnsafeMLOpsConfigServiceServer interface {
	mustEmbedUnimplementedMLOpsConfigServiceServer()
}

func RegisterMLOpsConfigServiceServer(s grpc.ServiceRegistrar, srv MLOpsConfigServiceServer) {
	s.RegisterService(&MLOpsConfigService_ServiceDesc, srv)
}

func _MLOpsConfigService_ExternalConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRsp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsConfigServiceServer).ExternalConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsConfigService_ExternalConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsConfigServiceServer).ExternalConfig(ctx, req.(*common.EmptyRsp))
	}
	return interceptor(ctx, in, info, handler)
}

// MLOpsConfigService_ServiceDesc is the grpc.ServiceDesc for MLOpsConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MLOpsConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "serving.MLOpsConfigService",
	HandlerType: (*MLOpsConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExternalConfig",
			Handler:    _MLOpsConfigService_ExternalConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/serving/config.proto",
}
