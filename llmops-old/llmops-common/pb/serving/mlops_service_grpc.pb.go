// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/serving/mlops_service.proto

package serving

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "transwarp.io/aip/llmops-common/pb/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MLOpsServiceService_CreateService_FullMethodName        = "/serving.MLOpsServiceService/CreateService"
	MLOpsServiceService_CreateRemote_FullMethodName         = "/serving.MLOpsServiceService/CreateRemote"
	MLOpsServiceService_CreateServiceVersion_FullMethodName = "/serving.MLOpsServiceService/CreateServiceVersion"
	MLOpsServiceService_UpdateService_FullMethodName        = "/serving.MLOpsServiceService/UpdateService"
	MLOpsServiceService_UpdateServiceVersion_FullMethodName = "/serving.MLOpsServiceService/UpdateServiceVersion"
	MLOpsServiceService_DeleteServiceVersion_FullMethodName = "/serving.MLOpsServiceService/DeleteServiceVersion"
	MLOpsServiceService_DeleteService_FullMethodName        = "/serving.MLOpsServiceService/DeleteService"
	MLOpsServiceService_List_FullMethodName                 = "/serving.MLOpsServiceService/List"
	MLOpsServiceService_QueryByID_FullMethodName            = "/serving.MLOpsServiceService/QueryByID"
	MLOpsServiceService_Deploy_FullMethodName               = "/serving.MLOpsServiceService/Deploy"
	MLOpsServiceService_Offline_FullMethodName              = "/serving.MLOpsServiceService/Offline"
	MLOpsServiceService_GetRuntimeInfo_FullMethodName       = "/serving.MLOpsServiceService/GetRuntimeInfo"
	MLOpsServiceService_GetContainerLogs_FullMethodName     = "/serving.MLOpsServiceService/GetContainerLogs"
	MLOpsServiceService_GetEvents_FullMethodName            = "/serving.MLOpsServiceService/GetEvents"
	MLOpsServiceService_CallAPI_FullMethodName              = "/serving.MLOpsServiceService/CallAPI"
	MLOpsServiceService_UpdateApprovalState_FullMethodName  = "/serving.MLOpsServiceService/UpdateApprovalState"
	MLOpsServiceService_UpsertSvcYaml_FullMethodName        = "/serving.MLOpsServiceService/UpsertSvcYaml"
	MLOpsServiceService_GetSvcYaml_FullMethodName           = "/serving.MLOpsServiceService/GetSvcYaml"
	MLOpsServiceService_CheckNameUnique_FullMethodName      = "/serving.MLOpsServiceService/CheckNameUnique"
	MLOpsServiceService_CheckApiUnique_FullMethodName       = "/serving.MLOpsServiceService/CheckApiUnique"
)

// MLOpsServiceServiceClient is the client API for MLOpsServiceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MLOpsServiceServiceClient interface {
	// 创建服务基础信息
	CreateService(ctx context.Context, in *MLOpsServiceBaseInfo, opts ...grpc.CallOption) (*ServiceID, error)
	// 创建远程服务
	CreateRemote(ctx context.Context, in *MLOpsRemoteServiceInfoReq, opts ...grpc.CallOption) (*RemoteService, error)
	// 创建服务版本信息
	CreateServiceVersion(ctx context.Context, in *ServiceIDVersionInfo, opts ...grpc.CallOption) (*ServiceAndVersionID, error)
	// 更新服务基础信息
	UpdateService(ctx context.Context, in *MLOpsServiceBaseInfo, opts ...grpc.CallOption) (*UpdateServiceRes, error)
	// 更新服务版本信息
	UpdateServiceVersion(ctx context.Context, in *ServiceIDVersionInfo, opts ...grpc.CallOption) (*ServiceAndVersionID, error)
	// 删除服务版本信息
	DeleteServiceVersion(ctx context.Context, in *ServiceAndVersionID, opts ...grpc.CallOption) (*ServiceAndVersionID, error)
	// 删除服务
	DeleteService(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceID, error)
	// 服务列表
	List(ctx context.Context, in *ListServiceReq, opts ...grpc.CallOption) (*ServiceBaseInfoList, error)
	// 查询服务详情
	QueryByID(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceInfo, error)
	// 部署服务
	Deploy(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*DeployID, error)
	// 下线服务
	Offline(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceID, error)
	// 运行时信息，包括实例数量、状态，节点数量等
	GetRuntimeInfo(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*MLOpsSvcRuntimeInfo, error)
	// 获取容器日志
	GetContainerLogs(ctx context.Context, in *GetMLOpsContainerLogsReq, opts ...grpc.CallOption) (MLOpsServiceService_GetContainerLogsClient, error)
	// 获取容器事件
	GetEvents(ctx context.Context, in *GetMLOpsPodEventsReq, opts ...grpc.CallOption) (*GetMLOpsPodEventsRsp, error)
	// 服务测试
	CallAPI(ctx context.Context, in *CallAPIReq, opts ...grpc.CallOption) (*CallAPIResp, error)
	// 更新审批状态
	UpdateApprovalState(ctx context.Context, in *UpdateApprovalStateReq, opts ...grpc.CallOption) (*UpdateApprovalStateResp, error)
	// 创建/更新yaml配置
	UpsertSvcYaml(ctx context.Context, in *YamlConfig, opts ...grpc.CallOption) (*ServiceID, error)
	// 获取yaml配置
	GetSvcYaml(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*YamlConfig, error)
	CheckNameUnique(ctx context.Context, in *common.NameUniqueReq, opts ...grpc.CallOption) (*common.NameUniqueRes, error)
	// @gotags: description:"检测服务api是否唯一"
	CheckApiUnique(ctx context.Context, in *ApiUniqueReq, opts ...grpc.CallOption) (*ApiUniqueRes, error)
}

type mLOpsServiceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMLOpsServiceServiceClient(cc grpc.ClientConnInterface) MLOpsServiceServiceClient {
	return &mLOpsServiceServiceClient{cc}
}

func (c *mLOpsServiceServiceClient) CreateService(ctx context.Context, in *MLOpsServiceBaseInfo, opts ...grpc.CallOption) (*ServiceID, error) {
	out := new(ServiceID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CreateService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) CreateRemote(ctx context.Context, in *MLOpsRemoteServiceInfoReq, opts ...grpc.CallOption) (*RemoteService, error) {
	out := new(RemoteService)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CreateRemote_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) CreateServiceVersion(ctx context.Context, in *ServiceIDVersionInfo, opts ...grpc.CallOption) (*ServiceAndVersionID, error) {
	out := new(ServiceAndVersionID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CreateServiceVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) UpdateService(ctx context.Context, in *MLOpsServiceBaseInfo, opts ...grpc.CallOption) (*UpdateServiceRes, error) {
	out := new(UpdateServiceRes)
	err := c.cc.Invoke(ctx, MLOpsServiceService_UpdateService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) UpdateServiceVersion(ctx context.Context, in *ServiceIDVersionInfo, opts ...grpc.CallOption) (*ServiceAndVersionID, error) {
	out := new(ServiceAndVersionID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_UpdateServiceVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) DeleteServiceVersion(ctx context.Context, in *ServiceAndVersionID, opts ...grpc.CallOption) (*ServiceAndVersionID, error) {
	out := new(ServiceAndVersionID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_DeleteServiceVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) DeleteService(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceID, error) {
	out := new(ServiceID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_DeleteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) List(ctx context.Context, in *ListServiceReq, opts ...grpc.CallOption) (*ServiceBaseInfoList, error) {
	out := new(ServiceBaseInfoList)
	err := c.cc.Invoke(ctx, MLOpsServiceService_List_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) QueryByID(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceInfo, error) {
	out := new(ServiceInfo)
	err := c.cc.Invoke(ctx, MLOpsServiceService_QueryByID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) Deploy(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*DeployID, error) {
	out := new(DeployID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_Deploy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) Offline(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*ServiceID, error) {
	out := new(ServiceID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_Offline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) GetRuntimeInfo(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*MLOpsSvcRuntimeInfo, error) {
	out := new(MLOpsSvcRuntimeInfo)
	err := c.cc.Invoke(ctx, MLOpsServiceService_GetRuntimeInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) GetContainerLogs(ctx context.Context, in *GetMLOpsContainerLogsReq, opts ...grpc.CallOption) (MLOpsServiceService_GetContainerLogsClient, error) {
	stream, err := c.cc.NewStream(ctx, &MLOpsServiceService_ServiceDesc.Streams[0], MLOpsServiceService_GetContainerLogs_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &mLOpsServiceServiceGetContainerLogsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type MLOpsServiceService_GetContainerLogsClient interface {
	Recv() (*GetMLOpsContainerLogsRsp, error)
	grpc.ClientStream
}

type mLOpsServiceServiceGetContainerLogsClient struct {
	grpc.ClientStream
}

func (x *mLOpsServiceServiceGetContainerLogsClient) Recv() (*GetMLOpsContainerLogsRsp, error) {
	m := new(GetMLOpsContainerLogsRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *mLOpsServiceServiceClient) GetEvents(ctx context.Context, in *GetMLOpsPodEventsReq, opts ...grpc.CallOption) (*GetMLOpsPodEventsRsp, error) {
	out := new(GetMLOpsPodEventsRsp)
	err := c.cc.Invoke(ctx, MLOpsServiceService_GetEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) CallAPI(ctx context.Context, in *CallAPIReq, opts ...grpc.CallOption) (*CallAPIResp, error) {
	out := new(CallAPIResp)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CallAPI_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) UpdateApprovalState(ctx context.Context, in *UpdateApprovalStateReq, opts ...grpc.CallOption) (*UpdateApprovalStateResp, error) {
	out := new(UpdateApprovalStateResp)
	err := c.cc.Invoke(ctx, MLOpsServiceService_UpdateApprovalState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) UpsertSvcYaml(ctx context.Context, in *YamlConfig, opts ...grpc.CallOption) (*ServiceID, error) {
	out := new(ServiceID)
	err := c.cc.Invoke(ctx, MLOpsServiceService_UpsertSvcYaml_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) GetSvcYaml(ctx context.Context, in *ServiceID, opts ...grpc.CallOption) (*YamlConfig, error) {
	out := new(YamlConfig)
	err := c.cc.Invoke(ctx, MLOpsServiceService_GetSvcYaml_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) CheckNameUnique(ctx context.Context, in *common.NameUniqueReq, opts ...grpc.CallOption) (*common.NameUniqueRes, error) {
	out := new(common.NameUniqueRes)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CheckNameUnique_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mLOpsServiceServiceClient) CheckApiUnique(ctx context.Context, in *ApiUniqueReq, opts ...grpc.CallOption) (*ApiUniqueRes, error) {
	out := new(ApiUniqueRes)
	err := c.cc.Invoke(ctx, MLOpsServiceService_CheckApiUnique_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MLOpsServiceServiceServer is the server API for MLOpsServiceService service.
// All implementations must embed UnimplementedMLOpsServiceServiceServer
// for forward compatibility
type MLOpsServiceServiceServer interface {
	// 创建服务基础信息
	CreateService(context.Context, *MLOpsServiceBaseInfo) (*ServiceID, error)
	// 创建远程服务
	CreateRemote(context.Context, *MLOpsRemoteServiceInfoReq) (*RemoteService, error)
	// 创建服务版本信息
	CreateServiceVersion(context.Context, *ServiceIDVersionInfo) (*ServiceAndVersionID, error)
	// 更新服务基础信息
	UpdateService(context.Context, *MLOpsServiceBaseInfo) (*UpdateServiceRes, error)
	// 更新服务版本信息
	UpdateServiceVersion(context.Context, *ServiceIDVersionInfo) (*ServiceAndVersionID, error)
	// 删除服务版本信息
	DeleteServiceVersion(context.Context, *ServiceAndVersionID) (*ServiceAndVersionID, error)
	// 删除服务
	DeleteService(context.Context, *ServiceID) (*ServiceID, error)
	// 服务列表
	List(context.Context, *ListServiceReq) (*ServiceBaseInfoList, error)
	// 查询服务详情
	QueryByID(context.Context, *ServiceID) (*ServiceInfo, error)
	// 部署服务
	Deploy(context.Context, *ServiceID) (*DeployID, error)
	// 下线服务
	Offline(context.Context, *ServiceID) (*ServiceID, error)
	// 运行时信息，包括实例数量、状态，节点数量等
	GetRuntimeInfo(context.Context, *ServiceID) (*MLOpsSvcRuntimeInfo, error)
	// 获取容器日志
	GetContainerLogs(*GetMLOpsContainerLogsReq, MLOpsServiceService_GetContainerLogsServer) error
	// 获取容器事件
	GetEvents(context.Context, *GetMLOpsPodEventsReq) (*GetMLOpsPodEventsRsp, error)
	// 服务测试
	CallAPI(context.Context, *CallAPIReq) (*CallAPIResp, error)
	// 更新审批状态
	UpdateApprovalState(context.Context, *UpdateApprovalStateReq) (*UpdateApprovalStateResp, error)
	// 创建/更新yaml配置
	UpsertSvcYaml(context.Context, *YamlConfig) (*ServiceID, error)
	// 获取yaml配置
	GetSvcYaml(context.Context, *ServiceID) (*YamlConfig, error)
	CheckNameUnique(context.Context, *common.NameUniqueReq) (*common.NameUniqueRes, error)
	// @gotags: description:"检测服务api是否唯一"
	CheckApiUnique(context.Context, *ApiUniqueReq) (*ApiUniqueRes, error)
	mustEmbedUnimplementedMLOpsServiceServiceServer()
}

// UnimplementedMLOpsServiceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMLOpsServiceServiceServer struct {
}

func (UnimplementedMLOpsServiceServiceServer) CreateService(context.Context, *MLOpsServiceBaseInfo) (*ServiceID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) CreateRemote(context.Context, *MLOpsRemoteServiceInfoReq) (*RemoteService, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRemote not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) CreateServiceVersion(context.Context, *ServiceIDVersionInfo) (*ServiceAndVersionID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceVersion not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) UpdateService(context.Context, *MLOpsServiceBaseInfo) (*UpdateServiceRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) UpdateServiceVersion(context.Context, *ServiceIDVersionInfo) (*ServiceAndVersionID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceVersion not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) DeleteServiceVersion(context.Context, *ServiceAndVersionID) (*ServiceAndVersionID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceVersion not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) DeleteService(context.Context, *ServiceID) (*ServiceID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) List(context.Context, *ListServiceReq) (*ServiceBaseInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) QueryByID(context.Context, *ServiceID) (*ServiceInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByID not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) Deploy(context.Context, *ServiceID) (*DeployID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Deploy not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) Offline(context.Context, *ServiceID) (*ServiceID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Offline not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) GetRuntimeInfo(context.Context, *ServiceID) (*MLOpsSvcRuntimeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRuntimeInfo not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) GetContainerLogs(*GetMLOpsContainerLogsReq, MLOpsServiceService_GetContainerLogsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetContainerLogs not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) GetEvents(context.Context, *GetMLOpsPodEventsReq) (*GetMLOpsPodEventsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvents not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) CallAPI(context.Context, *CallAPIReq) (*CallAPIResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallAPI not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) UpdateApprovalState(context.Context, *UpdateApprovalStateReq) (*UpdateApprovalStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateApprovalState not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) UpsertSvcYaml(context.Context, *YamlConfig) (*ServiceID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertSvcYaml not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) GetSvcYaml(context.Context, *ServiceID) (*YamlConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSvcYaml not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) CheckNameUnique(context.Context, *common.NameUniqueReq) (*common.NameUniqueRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckNameUnique not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) CheckApiUnique(context.Context, *ApiUniqueReq) (*ApiUniqueRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckApiUnique not implemented")
}
func (UnimplementedMLOpsServiceServiceServer) mustEmbedUnimplementedMLOpsServiceServiceServer() {}

// UnsafeMLOpsServiceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MLOpsServiceServiceServer will
// result in compilation errors.
type UnsafeMLOpsServiceServiceServer interface {
	mustEmbedUnimplementedMLOpsServiceServiceServer()
}

func RegisterMLOpsServiceServiceServer(s grpc.ServiceRegistrar, srv MLOpsServiceServiceServer) {
	s.RegisterService(&MLOpsServiceService_ServiceDesc, srv)
}

func _MLOpsServiceService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MLOpsServiceBaseInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CreateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CreateService(ctx, req.(*MLOpsServiceBaseInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_CreateRemote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MLOpsRemoteServiceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CreateRemote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CreateRemote_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CreateRemote(ctx, req.(*MLOpsRemoteServiceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_CreateServiceVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceIDVersionInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CreateServiceVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CreateServiceVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CreateServiceVersion(ctx, req.(*ServiceIDVersionInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MLOpsServiceBaseInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_UpdateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).UpdateService(ctx, req.(*MLOpsServiceBaseInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_UpdateServiceVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceIDVersionInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).UpdateServiceVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_UpdateServiceVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).UpdateServiceVersion(ctx, req.(*ServiceIDVersionInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_DeleteServiceVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceAndVersionID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).DeleteServiceVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_DeleteServiceVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).DeleteServiceVersion(ctx, req.(*ServiceAndVersionID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_DeleteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).DeleteService(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).List(ctx, req.(*ListServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_QueryByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).QueryByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_QueryByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).QueryByID(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_Deploy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).Deploy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_Deploy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).Deploy(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_Offline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).Offline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_Offline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).Offline(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_GetRuntimeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).GetRuntimeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_GetRuntimeInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).GetRuntimeInfo(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_GetContainerLogs_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetMLOpsContainerLogsReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(MLOpsServiceServiceServer).GetContainerLogs(m, &mLOpsServiceServiceGetContainerLogsServer{stream})
}

type MLOpsServiceService_GetContainerLogsServer interface {
	Send(*GetMLOpsContainerLogsRsp) error
	grpc.ServerStream
}

type mLOpsServiceServiceGetContainerLogsServer struct {
	grpc.ServerStream
}

func (x *mLOpsServiceServiceGetContainerLogsServer) Send(m *GetMLOpsContainerLogsRsp) error {
	return x.ServerStream.SendMsg(m)
}

func _MLOpsServiceService_GetEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMLOpsPodEventsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).GetEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_GetEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).GetEvents(ctx, req.(*GetMLOpsPodEventsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_CallAPI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallAPIReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CallAPI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CallAPI_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CallAPI(ctx, req.(*CallAPIReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_UpdateApprovalState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateApprovalStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).UpdateApprovalState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_UpdateApprovalState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).UpdateApprovalState(ctx, req.(*UpdateApprovalStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_UpsertSvcYaml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YamlConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).UpsertSvcYaml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_UpsertSvcYaml_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).UpsertSvcYaml(ctx, req.(*YamlConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_GetSvcYaml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).GetSvcYaml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_GetSvcYaml_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).GetSvcYaml(ctx, req.(*ServiceID))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_CheckNameUnique_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.NameUniqueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CheckNameUnique(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CheckNameUnique_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CheckNameUnique(ctx, req.(*common.NameUniqueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MLOpsServiceService_CheckApiUnique_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApiUniqueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MLOpsServiceServiceServer).CheckApiUnique(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MLOpsServiceService_CheckApiUnique_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MLOpsServiceServiceServer).CheckApiUnique(ctx, req.(*ApiUniqueReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MLOpsServiceService_ServiceDesc is the grpc.ServiceDesc for MLOpsServiceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MLOpsServiceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "serving.MLOpsServiceService",
	HandlerType: (*MLOpsServiceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateService",
			Handler:    _MLOpsServiceService_CreateService_Handler,
		},
		{
			MethodName: "CreateRemote",
			Handler:    _MLOpsServiceService_CreateRemote_Handler,
		},
		{
			MethodName: "CreateServiceVersion",
			Handler:    _MLOpsServiceService_CreateServiceVersion_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _MLOpsServiceService_UpdateService_Handler,
		},
		{
			MethodName: "UpdateServiceVersion",
			Handler:    _MLOpsServiceService_UpdateServiceVersion_Handler,
		},
		{
			MethodName: "DeleteServiceVersion",
			Handler:    _MLOpsServiceService_DeleteServiceVersion_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _MLOpsServiceService_DeleteService_Handler,
		},
		{
			MethodName: "List",
			Handler:    _MLOpsServiceService_List_Handler,
		},
		{
			MethodName: "QueryByID",
			Handler:    _MLOpsServiceService_QueryByID_Handler,
		},
		{
			MethodName: "Deploy",
			Handler:    _MLOpsServiceService_Deploy_Handler,
		},
		{
			MethodName: "Offline",
			Handler:    _MLOpsServiceService_Offline_Handler,
		},
		{
			MethodName: "GetRuntimeInfo",
			Handler:    _MLOpsServiceService_GetRuntimeInfo_Handler,
		},
		{
			MethodName: "GetEvents",
			Handler:    _MLOpsServiceService_GetEvents_Handler,
		},
		{
			MethodName: "CallAPI",
			Handler:    _MLOpsServiceService_CallAPI_Handler,
		},
		{
			MethodName: "UpdateApprovalState",
			Handler:    _MLOpsServiceService_UpdateApprovalState_Handler,
		},
		{
			MethodName: "UpsertSvcYaml",
			Handler:    _MLOpsServiceService_UpsertSvcYaml_Handler,
		},
		{
			MethodName: "GetSvcYaml",
			Handler:    _MLOpsServiceService_GetSvcYaml_Handler,
		},
		{
			MethodName: "CheckNameUnique",
			Handler:    _MLOpsServiceService_CheckNameUnique_Handler,
		},
		{
			MethodName: "CheckApiUnique",
			Handler:    _MLOpsServiceService_CheckApiUnique_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetContainerLogs",
			Handler:       _MLOpsServiceService_GetContainerLogs_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/serving/mlops_service.proto",
}
