// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/serving/audit.proto

package serving

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "transwarp.io/aip/llmops-common/pb/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AuditV2Service_GetServiceOverview_FullMethodName         = "/serving.AuditV2Service/GetServiceOverview"
	AuditV2Service_GetServiceCurveChart_FullMethodName       = "/serving.AuditV2Service/GetServiceCurveChart"
	AuditV2Service_GetResourceUsageCurveChart_FullMethodName = "/serving.AuditV2Service/GetResourceUsageCurveChart"
	AuditV2Service_GetRequestOverview_FullMethodName         = "/serving.AuditV2Service/GetRequestOverview"
	AuditV2Service_GetRequestBarChart_FullMethodName         = "/serving.AuditV2Service/GetRequestBarChart"
	AuditV2Service_GetFirstTokenTimeChart_FullMethodName     = "/serving.AuditV2Service/GetFirstTokenTimeChart"
	AuditV2Service_GetVisitRank_FullMethodName               = "/serving.AuditV2Service/GetVisitRank"
	AuditV2Service_GetServiceDailyVisit_FullMethodName       = "/serving.AuditV2Service/GetServiceDailyVisit"
	AuditV2Service_GetServiceVisitRecordList_FullMethodName  = "/serving.AuditV2Service/GetServiceVisitRecordList"
	AuditV2Service_DownloadServiceVisitRecord_FullMethodName = "/serving.AuditV2Service/DownloadServiceVisitRecord"
	AuditV2Service_GetGPUResourceCurveChart_FullMethodName   = "/serving.AuditV2Service/GetGPUResourceCurveChart"
	AuditV2Service_GetTokenBarChart_FullMethodName           = "/serving.AuditV2Service/GetTokenBarChart"
	AuditV2Service_CountToken_FullMethodName                 = "/serving.AuditV2Service/CountToken"
	AuditV2Service_EvaluateAnswer_FullMethodName             = "/serving.AuditV2Service/EvaluateAnswer"
	AuditV2Service_GetAvgFirstTokenTime_FullMethodName       = "/serving.AuditV2Service/GetAvgFirstTokenTime"
	AuditV2Service_GetGlobalOverview_FullMethodName          = "/serving.AuditV2Service/GetGlobalOverview"
	AuditV2Service_GetGlobalDays_FullMethodName              = "/serving.AuditV2Service/GetGlobalDays"
)

// AuditV2ServiceClient is the client API for AuditV2Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuditV2ServiceClient interface {
	// 服务数量总览
	GetServiceOverview(ctx context.Context, in *OverviewReq, opts ...grpc.CallOption) (*DashboardOverview, error)
	// 服务数量趋势
	GetServiceCurveChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardCurveChart, error)
	// 内存/cpu使用率
	GetResourceUsageCurveChart(ctx context.Context, in *ResourceUsageReq, opts ...grpc.CallOption) (*DashboardResourceUsageCurveChart, error)
	// 访问量/耗时总览
	GetRequestOverview(ctx context.Context, in *OverviewReq, opts ...grpc.CallOption) (*DashboardRequestOverview, error)
	// 访问量/耗时趋势
	GetRequestBarChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardRequestBarChart, error)
	// 获取首字时延指标监控指标趋势图
	GetFirstTokenTimeChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*common.DashboardChart, error)
	// 访问量排名
	GetVisitRank(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardVisitRank, error)
	// 统计使用度
	GetServiceDailyVisit(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardVisitHotGraph, error)
	// 日志记录
	GetServiceVisitRecordList(ctx context.Context, in *ServiceVisitRecordPageReq, opts ...grpc.CallOption) (*ServiceVisitRecordPage, error)
	// 下载日志
	DownloadServiceVisitRecord(ctx context.Context, in *DownloadServiceVisitRecordReq, opts ...grpc.CallOption) (*DownloadServiceVisitRecordRes, error)
	// 获取gpu core、gpu memory使用率
	GetGPUResourceCurveChart(ctx context.Context, in *GpuResourceUsageReq, opts ...grpc.CallOption) (*GpuCurveChart, error)
	// Tokens趋势
	GetTokenBarChart(ctx context.Context, in *GetTokenBarChartReq, opts ...grpc.CallOption) (*DashboardTokenBarChart, error)
	// Tokens统计
	CountToken(ctx context.Context, in *CountTokenReq, opts ...grpc.CallOption) (*CountTokenRsp, error)
	// 点赞点踩
	EvaluateAnswer(ctx context.Context, in *EvaluateAnswerReq, opts ...grpc.CallOption) (*common.EmptyRsp, error)
	// 监控大盘-字时延平均值
	GetAvgFirstTokenTime(ctx context.Context, in *AvgFirstTimeTokenReq, opts ...grpc.CallOption) (*AvgFirstTimeToken, error)
	// 监控大盘-服务今日访问概述
	GetGlobalOverview(ctx context.Context, in *common.EmptyReq, opts ...grpc.CallOption) (*DashboardGlobalOverView, error)
	// 监控大盘-服务今日访问概述
	GetGlobalDays(ctx context.Context, in *common.EmptyReq, opts ...grpc.CallOption) (*DashboardGlobalDays, error)
}

type auditV2ServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuditV2ServiceClient(cc grpc.ClientConnInterface) AuditV2ServiceClient {
	return &auditV2ServiceClient{cc}
}

func (c *auditV2ServiceClient) GetServiceOverview(ctx context.Context, in *OverviewReq, opts ...grpc.CallOption) (*DashboardOverview, error) {
	out := new(DashboardOverview)
	err := c.cc.Invoke(ctx, AuditV2Service_GetServiceOverview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetServiceCurveChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardCurveChart, error) {
	out := new(DashboardCurveChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetServiceCurveChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetResourceUsageCurveChart(ctx context.Context, in *ResourceUsageReq, opts ...grpc.CallOption) (*DashboardResourceUsageCurveChart, error) {
	out := new(DashboardResourceUsageCurveChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetResourceUsageCurveChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetRequestOverview(ctx context.Context, in *OverviewReq, opts ...grpc.CallOption) (*DashboardRequestOverview, error) {
	out := new(DashboardRequestOverview)
	err := c.cc.Invoke(ctx, AuditV2Service_GetRequestOverview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetRequestBarChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardRequestBarChart, error) {
	out := new(DashboardRequestBarChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetRequestBarChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetFirstTokenTimeChart(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*common.DashboardChart, error) {
	out := new(common.DashboardChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetFirstTokenTimeChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetVisitRank(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardVisitRank, error) {
	out := new(DashboardVisitRank)
	err := c.cc.Invoke(ctx, AuditV2Service_GetVisitRank_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetServiceDailyVisit(ctx context.Context, in *TimeReq, opts ...grpc.CallOption) (*DashboardVisitHotGraph, error) {
	out := new(DashboardVisitHotGraph)
	err := c.cc.Invoke(ctx, AuditV2Service_GetServiceDailyVisit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetServiceVisitRecordList(ctx context.Context, in *ServiceVisitRecordPageReq, opts ...grpc.CallOption) (*ServiceVisitRecordPage, error) {
	out := new(ServiceVisitRecordPage)
	err := c.cc.Invoke(ctx, AuditV2Service_GetServiceVisitRecordList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) DownloadServiceVisitRecord(ctx context.Context, in *DownloadServiceVisitRecordReq, opts ...grpc.CallOption) (*DownloadServiceVisitRecordRes, error) {
	out := new(DownloadServiceVisitRecordRes)
	err := c.cc.Invoke(ctx, AuditV2Service_DownloadServiceVisitRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetGPUResourceCurveChart(ctx context.Context, in *GpuResourceUsageReq, opts ...grpc.CallOption) (*GpuCurveChart, error) {
	out := new(GpuCurveChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetGPUResourceCurveChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetTokenBarChart(ctx context.Context, in *GetTokenBarChartReq, opts ...grpc.CallOption) (*DashboardTokenBarChart, error) {
	out := new(DashboardTokenBarChart)
	err := c.cc.Invoke(ctx, AuditV2Service_GetTokenBarChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) CountToken(ctx context.Context, in *CountTokenReq, opts ...grpc.CallOption) (*CountTokenRsp, error) {
	out := new(CountTokenRsp)
	err := c.cc.Invoke(ctx, AuditV2Service_CountToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) EvaluateAnswer(ctx context.Context, in *EvaluateAnswerReq, opts ...grpc.CallOption) (*common.EmptyRsp, error) {
	out := new(common.EmptyRsp)
	err := c.cc.Invoke(ctx, AuditV2Service_EvaluateAnswer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetAvgFirstTokenTime(ctx context.Context, in *AvgFirstTimeTokenReq, opts ...grpc.CallOption) (*AvgFirstTimeToken, error) {
	out := new(AvgFirstTimeToken)
	err := c.cc.Invoke(ctx, AuditV2Service_GetAvgFirstTokenTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetGlobalOverview(ctx context.Context, in *common.EmptyReq, opts ...grpc.CallOption) (*DashboardGlobalOverView, error) {
	out := new(DashboardGlobalOverView)
	err := c.cc.Invoke(ctx, AuditV2Service_GetGlobalOverview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditV2ServiceClient) GetGlobalDays(ctx context.Context, in *common.EmptyReq, opts ...grpc.CallOption) (*DashboardGlobalDays, error) {
	out := new(DashboardGlobalDays)
	err := c.cc.Invoke(ctx, AuditV2Service_GetGlobalDays_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuditV2ServiceServer is the server API for AuditV2Service service.
// All implementations must embed UnimplementedAuditV2ServiceServer
// for forward compatibility
type AuditV2ServiceServer interface {
	// 服务数量总览
	GetServiceOverview(context.Context, *OverviewReq) (*DashboardOverview, error)
	// 服务数量趋势
	GetServiceCurveChart(context.Context, *TimeReq) (*DashboardCurveChart, error)
	// 内存/cpu使用率
	GetResourceUsageCurveChart(context.Context, *ResourceUsageReq) (*DashboardResourceUsageCurveChart, error)
	// 访问量/耗时总览
	GetRequestOverview(context.Context, *OverviewReq) (*DashboardRequestOverview, error)
	// 访问量/耗时趋势
	GetRequestBarChart(context.Context, *TimeReq) (*DashboardRequestBarChart, error)
	// 获取首字时延指标监控指标趋势图
	GetFirstTokenTimeChart(context.Context, *TimeReq) (*common.DashboardChart, error)
	// 访问量排名
	GetVisitRank(context.Context, *TimeReq) (*DashboardVisitRank, error)
	// 统计使用度
	GetServiceDailyVisit(context.Context, *TimeReq) (*DashboardVisitHotGraph, error)
	// 日志记录
	GetServiceVisitRecordList(context.Context, *ServiceVisitRecordPageReq) (*ServiceVisitRecordPage, error)
	// 下载日志
	DownloadServiceVisitRecord(context.Context, *DownloadServiceVisitRecordReq) (*DownloadServiceVisitRecordRes, error)
	// 获取gpu core、gpu memory使用率
	GetGPUResourceCurveChart(context.Context, *GpuResourceUsageReq) (*GpuCurveChart, error)
	// Tokens趋势
	GetTokenBarChart(context.Context, *GetTokenBarChartReq) (*DashboardTokenBarChart, error)
	// Tokens统计
	CountToken(context.Context, *CountTokenReq) (*CountTokenRsp, error)
	// 点赞点踩
	EvaluateAnswer(context.Context, *EvaluateAnswerReq) (*common.EmptyRsp, error)
	// 监控大盘-字时延平均值
	GetAvgFirstTokenTime(context.Context, *AvgFirstTimeTokenReq) (*AvgFirstTimeToken, error)
	// 监控大盘-服务今日访问概述
	GetGlobalOverview(context.Context, *common.EmptyReq) (*DashboardGlobalOverView, error)
	// 监控大盘-服务今日访问概述
	GetGlobalDays(context.Context, *common.EmptyReq) (*DashboardGlobalDays, error)
	mustEmbedUnimplementedAuditV2ServiceServer()
}

// UnimplementedAuditV2ServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAuditV2ServiceServer struct {
}

func (UnimplementedAuditV2ServiceServer) GetServiceOverview(context.Context, *OverviewReq) (*DashboardOverview, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceOverview not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetServiceCurveChart(context.Context, *TimeReq) (*DashboardCurveChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceCurveChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetResourceUsageCurveChart(context.Context, *ResourceUsageReq) (*DashboardResourceUsageCurveChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceUsageCurveChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetRequestOverview(context.Context, *OverviewReq) (*DashboardRequestOverview, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRequestOverview not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetRequestBarChart(context.Context, *TimeReq) (*DashboardRequestBarChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRequestBarChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetFirstTokenTimeChart(context.Context, *TimeReq) (*common.DashboardChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFirstTokenTimeChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetVisitRank(context.Context, *TimeReq) (*DashboardVisitRank, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisitRank not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetServiceDailyVisit(context.Context, *TimeReq) (*DashboardVisitHotGraph, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceDailyVisit not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetServiceVisitRecordList(context.Context, *ServiceVisitRecordPageReq) (*ServiceVisitRecordPage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceVisitRecordList not implemented")
}
func (UnimplementedAuditV2ServiceServer) DownloadServiceVisitRecord(context.Context, *DownloadServiceVisitRecordReq) (*DownloadServiceVisitRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadServiceVisitRecord not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetGPUResourceCurveChart(context.Context, *GpuResourceUsageReq) (*GpuCurveChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGPUResourceCurveChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetTokenBarChart(context.Context, *GetTokenBarChartReq) (*DashboardTokenBarChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokenBarChart not implemented")
}
func (UnimplementedAuditV2ServiceServer) CountToken(context.Context, *CountTokenReq) (*CountTokenRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountToken not implemented")
}
func (UnimplementedAuditV2ServiceServer) EvaluateAnswer(context.Context, *EvaluateAnswerReq) (*common.EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EvaluateAnswer not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetAvgFirstTokenTime(context.Context, *AvgFirstTimeTokenReq) (*AvgFirstTimeToken, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvgFirstTokenTime not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetGlobalOverview(context.Context, *common.EmptyReq) (*DashboardGlobalOverView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGlobalOverview not implemented")
}
func (UnimplementedAuditV2ServiceServer) GetGlobalDays(context.Context, *common.EmptyReq) (*DashboardGlobalDays, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGlobalDays not implemented")
}
func (UnimplementedAuditV2ServiceServer) mustEmbedUnimplementedAuditV2ServiceServer() {}

// UnsafeAuditV2ServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuditV2ServiceServer will
// result in compilation errors.
type UnsafeAuditV2ServiceServer interface {
	mustEmbedUnimplementedAuditV2ServiceServer()
}

func RegisterAuditV2ServiceServer(s grpc.ServiceRegistrar, srv AuditV2ServiceServer) {
	s.RegisterService(&AuditV2Service_ServiceDesc, srv)
}

func _AuditV2Service_GetServiceOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverviewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetServiceOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetServiceOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetServiceOverview(ctx, req.(*OverviewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetServiceCurveChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetServiceCurveChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetServiceCurveChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetServiceCurveChart(ctx, req.(*TimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetResourceUsageCurveChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResourceUsageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetResourceUsageCurveChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetResourceUsageCurveChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetResourceUsageCurveChart(ctx, req.(*ResourceUsageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetRequestOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverviewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetRequestOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetRequestOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetRequestOverview(ctx, req.(*OverviewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetRequestBarChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetRequestBarChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetRequestBarChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetRequestBarChart(ctx, req.(*TimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetFirstTokenTimeChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetFirstTokenTimeChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetFirstTokenTimeChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetFirstTokenTimeChart(ctx, req.(*TimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetVisitRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetVisitRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetVisitRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetVisitRank(ctx, req.(*TimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetServiceDailyVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetServiceDailyVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetServiceDailyVisit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetServiceDailyVisit(ctx, req.(*TimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetServiceVisitRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceVisitRecordPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetServiceVisitRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetServiceVisitRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetServiceVisitRecordList(ctx, req.(*ServiceVisitRecordPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_DownloadServiceVisitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadServiceVisitRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).DownloadServiceVisitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_DownloadServiceVisitRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).DownloadServiceVisitRecord(ctx, req.(*DownloadServiceVisitRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetGPUResourceCurveChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GpuResourceUsageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetGPUResourceCurveChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetGPUResourceCurveChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetGPUResourceCurveChart(ctx, req.(*GpuResourceUsageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetTokenBarChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenBarChartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetTokenBarChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetTokenBarChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetTokenBarChart(ctx, req.(*GetTokenBarChartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_CountToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).CountToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_CountToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).CountToken(ctx, req.(*CountTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_EvaluateAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvaluateAnswerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).EvaluateAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_EvaluateAnswer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).EvaluateAnswer(ctx, req.(*EvaluateAnswerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetAvgFirstTokenTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AvgFirstTimeTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetAvgFirstTokenTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetAvgFirstTokenTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetAvgFirstTokenTime(ctx, req.(*AvgFirstTimeTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetGlobalOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetGlobalOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetGlobalOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetGlobalOverview(ctx, req.(*common.EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditV2Service_GetGlobalDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditV2ServiceServer).GetGlobalDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditV2Service_GetGlobalDays_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditV2ServiceServer).GetGlobalDays(ctx, req.(*common.EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AuditV2Service_ServiceDesc is the grpc.ServiceDesc for AuditV2Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuditV2Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "serving.AuditV2Service",
	HandlerType: (*AuditV2ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceOverview",
			Handler:    _AuditV2Service_GetServiceOverview_Handler,
		},
		{
			MethodName: "GetServiceCurveChart",
			Handler:    _AuditV2Service_GetServiceCurveChart_Handler,
		},
		{
			MethodName: "GetResourceUsageCurveChart",
			Handler:    _AuditV2Service_GetResourceUsageCurveChart_Handler,
		},
		{
			MethodName: "GetRequestOverview",
			Handler:    _AuditV2Service_GetRequestOverview_Handler,
		},
		{
			MethodName: "GetRequestBarChart",
			Handler:    _AuditV2Service_GetRequestBarChart_Handler,
		},
		{
			MethodName: "GetFirstTokenTimeChart",
			Handler:    _AuditV2Service_GetFirstTokenTimeChart_Handler,
		},
		{
			MethodName: "GetVisitRank",
			Handler:    _AuditV2Service_GetVisitRank_Handler,
		},
		{
			MethodName: "GetServiceDailyVisit",
			Handler:    _AuditV2Service_GetServiceDailyVisit_Handler,
		},
		{
			MethodName: "GetServiceVisitRecordList",
			Handler:    _AuditV2Service_GetServiceVisitRecordList_Handler,
		},
		{
			MethodName: "DownloadServiceVisitRecord",
			Handler:    _AuditV2Service_DownloadServiceVisitRecord_Handler,
		},
		{
			MethodName: "GetGPUResourceCurveChart",
			Handler:    _AuditV2Service_GetGPUResourceCurveChart_Handler,
		},
		{
			MethodName: "GetTokenBarChart",
			Handler:    _AuditV2Service_GetTokenBarChart_Handler,
		},
		{
			MethodName: "CountToken",
			Handler:    _AuditV2Service_CountToken_Handler,
		},
		{
			MethodName: "EvaluateAnswer",
			Handler:    _AuditV2Service_EvaluateAnswer_Handler,
		},
		{
			MethodName: "GetAvgFirstTokenTime",
			Handler:    _AuditV2Service_GetAvgFirstTokenTime_Handler,
		},
		{
			MethodName: "GetGlobalOverview",
			Handler:    _AuditV2Service_GetGlobalOverview_Handler,
		},
		{
			MethodName: "GetGlobalDays",
			Handler:    _AuditV2Service_GetGlobalDays_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/serving/audit.proto",
}
