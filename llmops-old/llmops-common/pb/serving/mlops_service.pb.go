// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/serving/mlops_service.proto

package serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApprovalState int32

const (
	ApprovalState_MLOpsSvcStateApprovalInit     ApprovalState = 0 // 未发起审批
	ApprovalState_MLOpsSvcStateUnderApproval    ApprovalState = 1 // 审批中
	ApprovalState_MLOpsSvcStateApprovalRejected ApprovalState = 2 // 审批拒绝
	ApprovalState_MLOpsSvcStateApprovalPassed   ApprovalState = 3 // 审批通过
)

// Enum value maps for ApprovalState.
var (
	ApprovalState_name = map[int32]string{
		0: "MLOpsSvcStateApprovalInit",
		1: "MLOpsSvcStateUnderApproval",
		2: "MLOpsSvcStateApprovalRejected",
		3: "MLOpsSvcStateApprovalPassed",
	}
	ApprovalState_value = map[string]int32{
		"MLOpsSvcStateApprovalInit":     0,
		"MLOpsSvcStateUnderApproval":    1,
		"MLOpsSvcStateApprovalRejected": 2,
		"MLOpsSvcStateApprovalPassed":   3,
	}
)

func (x ApprovalState) Enum() *ApprovalState {
	p := new(ApprovalState)
	*p = x
	return p
}

func (x ApprovalState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApprovalState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[0].Descriptor()
}

func (ApprovalState) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[0]
}

func (x ApprovalState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApprovalState.Descriptor instead.
func (ApprovalState) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{0}
}

type SourceType int32

const (
	SourceType_SOURCE_TYPE_UNKNOW     SourceType = 0
	SourceType_SOURCE_TYPE_MODEL_CUBE SourceType = 1
	SourceType_SOURCE_TYPE_APP_CUBE   SourceType = 2
	SourceType_SOURCE_TYPE_VLAB       SourceType = 3
	SourceType_SOURCE_TYPE_REMOTE     SourceType = 4
	SourceType_SOURCE_TYPE_KNOWLEDGE  SourceType = 5
	SourceType_SOURCE_TYPE_CUSTOM     SourceType = 11
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0:  "SOURCE_TYPE_UNKNOW",
		1:  "SOURCE_TYPE_MODEL_CUBE",
		2:  "SOURCE_TYPE_APP_CUBE",
		3:  "SOURCE_TYPE_VLAB",
		4:  "SOURCE_TYPE_REMOTE",
		5:  "SOURCE_TYPE_KNOWLEDGE",
		11: "SOURCE_TYPE_CUSTOM",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNKNOW":     0,
		"SOURCE_TYPE_MODEL_CUBE": 1,
		"SOURCE_TYPE_APP_CUBE":   2,
		"SOURCE_TYPE_VLAB":       3,
		"SOURCE_TYPE_REMOTE":     4,
		"SOURCE_TYPE_KNOWLEDGE":  5,
		"SOURCE_TYPE_CUSTOM":     11,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[1].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[1]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{1}
}

type MLOpsSvcState int32

const (
	MLOpsSvcState_MLOPS_SVC_STATE_OFFLINE   MLOpsSvcState = 0
	MLOpsSvcState_MLOPS_SVC_STATE_AVAILABLE MLOpsSvcState = 1
	MLOpsSvcState_MLOPS_SVC_STATE_CREATING  MLOpsSvcState = 2
	MLOpsSvcState_MLOPS_SVC_STATE_FAILED    MLOpsSvcState = 3
)

// Enum value maps for MLOpsSvcState.
var (
	MLOpsSvcState_name = map[int32]string{
		0: "MLOPS_SVC_STATE_OFFLINE",
		1: "MLOPS_SVC_STATE_AVAILABLE",
		2: "MLOPS_SVC_STATE_CREATING",
		3: "MLOPS_SVC_STATE_FAILED",
	}
	MLOpsSvcState_value = map[string]int32{
		"MLOPS_SVC_STATE_OFFLINE":   0,
		"MLOPS_SVC_STATE_AVAILABLE": 1,
		"MLOPS_SVC_STATE_CREATING":  2,
		"MLOPS_SVC_STATE_FAILED":    3,
	}
)

func (x MLOpsSvcState) Enum() *MLOpsSvcState {
	p := new(MLOpsSvcState)
	*p = x
	return p
}

func (x MLOpsSvcState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MLOpsSvcState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[2].Descriptor()
}

func (MLOpsSvcState) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[2]
}

func (x MLOpsSvcState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MLOpsSvcState.Descriptor instead.
func (MLOpsSvcState) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{2}
}

type DeployStrategy int32

const (
	DeployStrategy_DEPLOY_STRATEGY_MAIN_DEPLOY   DeployStrategy = 0
	DeployStrategy_DEPLOY_STRATEGY_GRAY_DEPLOY   DeployStrategy = 1
	DeployStrategy_DEPLOY_STRATEGY_SHADOW_DEPLOY DeployStrategy = 2
)

// Enum value maps for DeployStrategy.
var (
	DeployStrategy_name = map[int32]string{
		0: "DEPLOY_STRATEGY_MAIN_DEPLOY",
		1: "DEPLOY_STRATEGY_GRAY_DEPLOY",
		2: "DEPLOY_STRATEGY_SHADOW_DEPLOY",
	}
	DeployStrategy_value = map[string]int32{
		"DEPLOY_STRATEGY_MAIN_DEPLOY":   0,
		"DEPLOY_STRATEGY_GRAY_DEPLOY":   1,
		"DEPLOY_STRATEGY_SHADOW_DEPLOY": 2,
	}
)

func (x DeployStrategy) Enum() *DeployStrategy {
	p := new(DeployStrategy)
	*p = x
	return p
}

func (x DeployStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeployStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[3].Descriptor()
}

func (DeployStrategy) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[3]
}

func (x DeployStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeployStrategy.Descriptor instead.
func (DeployStrategy) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{3}
}

type MLOpsFileType int32

const (
	MLOpsFileType_MLOPS_File_TYPE_HOST_PATH   MLOpsFileType = 0
	MLOpsFileType_MLOPS_File_TYPE_FILE_SYSTEM MLOpsFileType = 1
	MLOpsFileType_MLOPS_File_TYPE_MODEL_CUBE  MLOpsFileType = 2
	MLOpsFileType_MLOPS_File_TYPE_SAMPLE_CUBE MLOpsFileType = 3
	MLOpsFileType_MLOPS_File_TYPE_MEMORY      MLOpsFileType = 4
	MLOpsFileType_MLOPS_File_TYPE_CONFIG_MAP  MLOpsFileType = 5
)

// Enum value maps for MLOpsFileType.
var (
	MLOpsFileType_name = map[int32]string{
		0: "MLOPS_File_TYPE_HOST_PATH",
		1: "MLOPS_File_TYPE_FILE_SYSTEM",
		2: "MLOPS_File_TYPE_MODEL_CUBE",
		3: "MLOPS_File_TYPE_SAMPLE_CUBE",
		4: "MLOPS_File_TYPE_MEMORY",
		5: "MLOPS_File_TYPE_CONFIG_MAP",
	}
	MLOpsFileType_value = map[string]int32{
		"MLOPS_File_TYPE_HOST_PATH":   0,
		"MLOPS_File_TYPE_FILE_SYSTEM": 1,
		"MLOPS_File_TYPE_MODEL_CUBE":  2,
		"MLOPS_File_TYPE_SAMPLE_CUBE": 3,
		"MLOPS_File_TYPE_MEMORY":      4,
		"MLOPS_File_TYPE_CONFIG_MAP":  5,
	}
)

func (x MLOpsFileType) Enum() *MLOpsFileType {
	p := new(MLOpsFileType)
	*p = x
	return p
}

func (x MLOpsFileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MLOpsFileType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[4].Descriptor()
}

func (MLOpsFileType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[4]
}

func (x MLOpsFileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MLOpsFileType.Descriptor instead.
func (MLOpsFileType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{4}
}

type MLOpsVolumeType int32

const (
	MLOpsVolumeType_VOLUME_TYPE_HOST_PATH  MLOpsVolumeType = 0
	MLOpsVolumeType_VOLUME_TYPE_PVC        MLOpsVolumeType = 1
	MLOpsVolumeType_VOLUME_TYPE_EMPTY_DIR  MLOpsVolumeType = 2
	MLOpsVolumeType_VOLUME_TYPE_CONFIG_MAP MLOpsVolumeType = 3
)

// Enum value maps for MLOpsVolumeType.
var (
	MLOpsVolumeType_name = map[int32]string{
		0: "VOLUME_TYPE_HOST_PATH",
		1: "VOLUME_TYPE_PVC",
		2: "VOLUME_TYPE_EMPTY_DIR",
		3: "VOLUME_TYPE_CONFIG_MAP",
	}
	MLOpsVolumeType_value = map[string]int32{
		"VOLUME_TYPE_HOST_PATH":  0,
		"VOLUME_TYPE_PVC":        1,
		"VOLUME_TYPE_EMPTY_DIR":  2,
		"VOLUME_TYPE_CONFIG_MAP": 3,
	}
)

func (x MLOpsVolumeType) Enum() *MLOpsVolumeType {
	p := new(MLOpsVolumeType)
	*p = x
	return p
}

func (x MLOpsVolumeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MLOpsVolumeType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[5].Descriptor()
}

func (MLOpsVolumeType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[5]
}

func (x MLOpsVolumeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MLOpsVolumeType.Descriptor instead.
func (MLOpsVolumeType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{5}
}

type ContainerType int32

const (
	ContainerType_CONTAINER_TYPE_MAIN    ContainerType = 0
	ContainerType_CONTAINER_TYPE_SIDECAR ContainerType = 1
)

// Enum value maps for ContainerType.
var (
	ContainerType_name = map[int32]string{
		0: "CONTAINER_TYPE_MAIN",
		1: "CONTAINER_TYPE_SIDECAR",
	}
	ContainerType_value = map[string]int32{
		"CONTAINER_TYPE_MAIN":    0,
		"CONTAINER_TYPE_SIDECAR": 1,
	}
)

func (x ContainerType) Enum() *ContainerType {
	p := new(ContainerType)
	*p = x
	return p
}

func (x ContainerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContainerType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[6].Descriptor()
}

func (ContainerType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[6]
}

func (x ContainerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContainerType.Descriptor instead.
func (ContainerType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{6}
}

type ImageType int32

const (
	ImageType_IMAGE_TYPE_CUSTOM   ImageType = 0
	ImageType_IMAGE_TYPE_PLATFORM ImageType = 1
)

// Enum value maps for ImageType.
var (
	ImageType_name = map[int32]string{
		0: "IMAGE_TYPE_CUSTOM",
		1: "IMAGE_TYPE_PLATFORM",
	}
	ImageType_value = map[string]int32{
		"IMAGE_TYPE_CUSTOM":   0,
		"IMAGE_TYPE_PLATFORM": 1,
	}
)

func (x ImageType) Enum() *ImageType {
	p := new(ImageType)
	*p = x
	return p
}

func (x ImageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[7].Descriptor()
}

func (ImageType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[7]
}

func (x ImageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImageType.Descriptor instead.
func (ImageType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{7}
}

type GPU_CORE_UTILIZATION_POLICY int32

const (
	GPU_CORE_UTILIZATION_POLICY_GPU_CORE_UTILIZATION_POLICY_DEFAULT GPU_CORE_UTILIZATION_POLICY = 0
	GPU_CORE_UTILIZATION_POLICY_GPU_CORE_UTILIZATION_POLICY_FORCE   GPU_CORE_UTILIZATION_POLICY = 1
	GPU_CORE_UTILIZATION_POLICY_GPU_CORE_UTILIZATION_POLICY_DISABLE GPU_CORE_UTILIZATION_POLICY = 2
)

// Enum value maps for GPU_CORE_UTILIZATION_POLICY.
var (
	GPU_CORE_UTILIZATION_POLICY_name = map[int32]string{
		0: "GPU_CORE_UTILIZATION_POLICY_DEFAULT",
		1: "GPU_CORE_UTILIZATION_POLICY_FORCE",
		2: "GPU_CORE_UTILIZATION_POLICY_DISABLE",
	}
	GPU_CORE_UTILIZATION_POLICY_value = map[string]int32{
		"GPU_CORE_UTILIZATION_POLICY_DEFAULT": 0,
		"GPU_CORE_UTILIZATION_POLICY_FORCE":   1,
		"GPU_CORE_UTILIZATION_POLICY_DISABLE": 2,
	}
)

func (x GPU_CORE_UTILIZATION_POLICY) Enum() *GPU_CORE_UTILIZATION_POLICY {
	p := new(GPU_CORE_UTILIZATION_POLICY)
	*p = x
	return p
}

func (x GPU_CORE_UTILIZATION_POLICY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GPU_CORE_UTILIZATION_POLICY) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[8].Descriptor()
}

func (GPU_CORE_UTILIZATION_POLICY) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[8]
}

func (x GPU_CORE_UTILIZATION_POLICY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GPU_CORE_UTILIZATION_POLICY.Descriptor instead.
func (GPU_CORE_UTILIZATION_POLICY) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{8}
}

type NodeChooseStrategy int32

const (
	NodeChooseStrategy_NODE_CHOOSE_STRATEGY_FIX    NodeChooseStrategy = 0
	NodeChooseStrategy_NODE_CHOOSE_STRATEGY_RANDOM NodeChooseStrategy = 1
)

// Enum value maps for NodeChooseStrategy.
var (
	NodeChooseStrategy_name = map[int32]string{
		0: "NODE_CHOOSE_STRATEGY_FIX",
		1: "NODE_CHOOSE_STRATEGY_RANDOM",
	}
	NodeChooseStrategy_value = map[string]int32{
		"NODE_CHOOSE_STRATEGY_FIX":    0,
		"NODE_CHOOSE_STRATEGY_RANDOM": 1,
	}
)

func (x NodeChooseStrategy) Enum() *NodeChooseStrategy {
	p := new(NodeChooseStrategy)
	*p = x
	return p
}

func (x NodeChooseStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeChooseStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[9].Descriptor()
}

func (NodeChooseStrategy) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[9]
}

func (x NodeChooseStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeChooseStrategy.Descriptor instead.
func (NodeChooseStrategy) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{9}
}

type HPAStrategy int32

const (
	HPAStrategy_HPA_STRATEGY_DISABLED HPAStrategy = 0
	HPAStrategy_HPA_STRATEGY_ENABLED  HPAStrategy = 1
)

// Enum value maps for HPAStrategy.
var (
	HPAStrategy_name = map[int32]string{
		0: "HPA_STRATEGY_DISABLED",
		1: "HPA_STRATEGY_ENABLED",
	}
	HPAStrategy_value = map[string]int32{
		"HPA_STRATEGY_DISABLED": 0,
		"HPA_STRATEGY_ENABLED":  1,
	}
)

func (x HPAStrategy) Enum() *HPAStrategy {
	p := new(HPAStrategy)
	*p = x
	return p
}

func (x HPAStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HPAStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[10].Descriptor()
}

func (HPAStrategy) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[10]
}

func (x HPAStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HPAStrategy.Descriptor instead.
func (HPAStrategy) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{10}
}

type HpaResourceType int32

const (
	HpaResourceType_HPA_RESOURCE_TYPE_CPU    HpaResourceType = 0
	HpaResourceType_HPA_RESOURCE_TYPE_MEMORY HpaResourceType = 1
)

// Enum value maps for HpaResourceType.
var (
	HpaResourceType_name = map[int32]string{
		0: "HPA_RESOURCE_TYPE_CPU",
		1: "HPA_RESOURCE_TYPE_MEMORY",
	}
	HpaResourceType_value = map[string]int32{
		"HPA_RESOURCE_TYPE_CPU":    0,
		"HPA_RESOURCE_TYPE_MEMORY": 1,
	}
)

func (x HpaResourceType) Enum() *HpaResourceType {
	p := new(HpaResourceType)
	*p = x
	return p
}

func (x HpaResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HpaResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[11].Descriptor()
}

func (HpaResourceType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[11]
}

func (x HpaResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HpaResourceType.Descriptor instead.
func (HpaResourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{11}
}

type HpaMetricSourceType int32

const (
	HpaMetricSourceType_HPA_METRIC_SOURCE_TYPE_RESOURCE HpaMetricSourceType = 0
	HpaMetricSourceType_HPA_METRIC_SOURCE_TYPE_POD      HpaMetricSourceType = 1
)

// Enum value maps for HpaMetricSourceType.
var (
	HpaMetricSourceType_name = map[int32]string{
		0: "HPA_METRIC_SOURCE_TYPE_RESOURCE",
		1: "HPA_METRIC_SOURCE_TYPE_POD",
	}
	HpaMetricSourceType_value = map[string]int32{
		"HPA_METRIC_SOURCE_TYPE_RESOURCE": 0,
		"HPA_METRIC_SOURCE_TYPE_POD":      1,
	}
)

func (x HpaMetricSourceType) Enum() *HpaMetricSourceType {
	p := new(HpaMetricSourceType)
	*p = x
	return p
}

func (x HpaMetricSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HpaMetricSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[12].Descriptor()
}

func (HpaMetricSourceType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[12]
}

func (x HpaMetricSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HpaMetricSourceType.Descriptor instead.
func (HpaMetricSourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{12}
}

type InvokingMethod int32

const (
	InvokingMethod_INVOKING_METHOD_EXTERNAL                       InvokingMethod = 0
	InvokingMethod_INVOKING_METHOD_ISTIO_GATEWAY_NODE_IP_PORT     InvokingMethod = 2
	InvokingMethod_INVOKING_METHOD_INTERNAL_ISTIO_GATEWAY_SERVICE InvokingMethod = 1
)

// Enum value maps for InvokingMethod.
var (
	InvokingMethod_name = map[int32]string{
		0: "INVOKING_METHOD_EXTERNAL",
		2: "INVOKING_METHOD_ISTIO_GATEWAY_NODE_IP_PORT",
		1: "INVOKING_METHOD_INTERNAL_ISTIO_GATEWAY_SERVICE",
	}
	InvokingMethod_value = map[string]int32{
		"INVOKING_METHOD_EXTERNAL":                       0,
		"INVOKING_METHOD_ISTIO_GATEWAY_NODE_IP_PORT":     2,
		"INVOKING_METHOD_INTERNAL_ISTIO_GATEWAY_SERVICE": 1,
	}
)

func (x InvokingMethod) Enum() *InvokingMethod {
	p := new(InvokingMethod)
	*p = x
	return p
}

func (x InvokingMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvokingMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[13].Descriptor()
}

func (InvokingMethod) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[13]
}

func (x InvokingMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvokingMethod.Descriptor instead.
func (InvokingMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{13}
}

type DashboardTabType int32

const (
	// @gotags: description:"调用指标"
	DashboardTabType_DASHBOARD_TAB_TYPE_INVOKE DashboardTabType = 0
	// @gotags: description:"算力指标"
	DashboardTabType_DASHBOARD_TAB_TYPE_DEVICE DashboardTabType = 1
	// @gotags: description:"自定义指标"
	DashboardTabType_DASHBOARD_TAB_TYPE_CUSTOM DashboardTabType = 2
)

// Enum value maps for DashboardTabType.
var (
	DashboardTabType_name = map[int32]string{
		0: "DASHBOARD_TAB_TYPE_INVOKE",
		1: "DASHBOARD_TAB_TYPE_DEVICE",
		2: "DASHBOARD_TAB_TYPE_CUSTOM",
	}
	DashboardTabType_value = map[string]int32{
		"DASHBOARD_TAB_TYPE_INVOKE": 0,
		"DASHBOARD_TAB_TYPE_DEVICE": 1,
		"DASHBOARD_TAB_TYPE_CUSTOM": 2,
	}
)

func (x DashboardTabType) Enum() *DashboardTabType {
	p := new(DashboardTabType)
	*p = x
	return p
}

func (x DashboardTabType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DashboardTabType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_mlops_service_proto_enumTypes[14].Descriptor()
}

func (DashboardTabType) Type() protoreflect.EnumType {
	return &file_proto_serving_mlops_service_proto_enumTypes[14]
}

func (x DashboardTabType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DashboardTabType.Descriptor instead.
func (DashboardTabType) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{14}
}

type ServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceInfo *MLOpsServiceBaseInfo `protobuf:"bytes,1,opt,name=service_info,json=serviceInfo,proto3" json:"service_info" description:"服务基础信息"`

	ServiceVersionInfos []*MLOpsServiceVersionInfo `protobuf:"bytes,2,rep,name=service_version_infos,json=serviceVersionInfos,proto3" json:"service_version_infos" description:"服务版本信息"`
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetServiceInfo() *MLOpsServiceBaseInfo {
	if x != nil {
		return x.ServiceInfo
	}
	return nil
}

func (x *ServiceInfo) GetServiceVersionInfos() []*MLOpsServiceVersionInfo {
	if x != nil {
		return x.ServiceVersionInfos
	}
	return nil
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start *int32 `protobuf:"varint,1,opt,name=start,proto3,oneof" json:"start" description:"开始时间，秒级时间戳"`

	End *int32 `protobuf:"varint,2,opt,name=end,proto3,oneof" json:"end" description:"结束时间，秒级时间戳"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{1}
}

func (x *TimeRange) GetStart() int32 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *TimeRange) GetEnd() int32 {
	if x != nil && x.End != nil {
		return *x.End
	}
	return 0
}

type UpdateApprovalStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`

	ApprovalState ApprovalState `protobuf:"varint,2,opt,name=approval_state,json=approvalState,proto3,enum=serving.ApprovalState" json:"approval_state" description:"服务审批状态"`
}

func (x *UpdateApprovalStateReq) Reset() {
	*x = UpdateApprovalStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApprovalStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApprovalStateReq) ProtoMessage() {}

func (x *UpdateApprovalStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApprovalStateReq.ProtoReflect.Descriptor instead.
func (*UpdateApprovalStateReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateApprovalStateReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *UpdateApprovalStateReq) GetApprovalState() ApprovalState {
	if x != nil {
		return x.ApprovalState
	}
	return ApprovalState_MLOpsSvcStateApprovalInit
}

type UpdateApprovalStateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`

	ApprovalState ApprovalState `protobuf:"varint,2,opt,name=approval_state,json=approvalState,proto3,enum=serving.ApprovalState" json:"approval_state" description:"服务审批状态"`
}

func (x *UpdateApprovalStateResp) Reset() {
	*x = UpdateApprovalStateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApprovalStateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApprovalStateResp) ProtoMessage() {}

func (x *UpdateApprovalStateResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApprovalStateResp.ProtoReflect.Descriptor instead.
func (*UpdateApprovalStateResp) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateApprovalStateResp) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *UpdateApprovalStateResp) GetApprovalState() ApprovalState {
	if x != nil {
		return x.ApprovalState
	}
	return ApprovalState_MLOpsSvcStateApprovalInit
}

type ListServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Creator *string `protobuf:"bytes,1,opt,name=creator,proto3,oneof" json:"creator" description:"创建人"`

	SourceTypes []SourceType `protobuf:"varint,2,rep,packed,name=source_types,json=sourceTypes,proto3,enum=serving.SourceType" json:"source_types" description:"来源类型"`

	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name" description:"名字"`

	Cluster *string `protobuf:"bytes,4,opt,name=cluster,proto3,oneof" json:"cluster" description:"集群"`

	CreateTimeRange *TimeRange `protobuf:"bytes,5,opt,name=create_time_range,json=createTimeRange,proto3,oneof" json:"create_time_range" description:"时间范围（创建时间）"`

	ServiceIds []string `protobuf:"bytes,6,rep,name=service_ids,json=serviceIds,proto3" json:"service_ids"`

	SourceMetaExtra map[string]string `protobuf:"bytes,7,rep,name=source_meta_extra,json=sourceMetaExtra,proto3" json:"source_meta_extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"标签"`

	States   []string `protobuf:"bytes,8,rep,name=states,proto3" json:"states" description:"服务状态:Creating(上线中),Pending(等待调度),Available(运行中),Failed(上线失败),Offline(下线),ApprovalInit(未发起审批),UnderApproval(审批中),ApprovalRejected(审批拒绝),ApprovalPassed(审批通过)"`
	PageSize int32    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Page     int32    `protobuf:"varint,10,opt,name=page,proto3" json:"page"`

	OrderBy string   `protobuf:"bytes,11,opt,name=order_by,json=orderBy,proto3" json:"order_by" description:"name(名称),update_time(更新时间),remain_time(剩余时长)"`
	Desc    bool     `protobuf:"varint,12,opt,name=desc,proto3" json:"desc"`
	Nodes   []string `protobuf:"bytes,13,rep,name=nodes,proto3" json:"nodes"`
}

func (x *ListServiceReq) Reset() {
	*x = ListServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceReq) ProtoMessage() {}

func (x *ListServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceReq.ProtoReflect.Descriptor instead.
func (*ListServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListServiceReq) GetCreator() string {
	if x != nil && x.Creator != nil {
		return *x.Creator
	}
	return ""
}

func (x *ListServiceReq) GetSourceTypes() []SourceType {
	if x != nil {
		return x.SourceTypes
	}
	return nil
}

func (x *ListServiceReq) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListServiceReq) GetCluster() string {
	if x != nil && x.Cluster != nil {
		return *x.Cluster
	}
	return ""
}

func (x *ListServiceReq) GetCreateTimeRange() *TimeRange {
	if x != nil {
		return x.CreateTimeRange
	}
	return nil
}

func (x *ListServiceReq) GetServiceIds() []string {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ListServiceReq) GetSourceMetaExtra() map[string]string {
	if x != nil {
		return x.SourceMetaExtra
	}
	return nil
}

func (x *ListServiceReq) GetStates() []string {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListServiceReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListServiceReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListServiceReq) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListServiceReq) GetDesc() bool {
	if x != nil {
		return x.Desc
	}
	return false
}

func (x *ListServiceReq) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type ServiceBaseInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceInfos []*MLOpsServiceBaseInfo `protobuf:"bytes,3,rep,name=service_infos,json=serviceInfos,proto3" json:"service_infos" description:"服务基础信息"`

	Size int32 `protobuf:"varint,4,opt,name=size,proto3" json:"size" description:"总数"`
}

func (x *ServiceBaseInfoList) Reset() {
	*x = ServiceBaseInfoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceBaseInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceBaseInfoList) ProtoMessage() {}

func (x *ServiceBaseInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceBaseInfoList.ProtoReflect.Descriptor instead.
func (*ServiceBaseInfoList) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceBaseInfoList) GetServiceInfos() []*MLOpsServiceBaseInfo {
	if x != nil {
		return x.ServiceInfos
	}
	return nil
}

func (x *ServiceBaseInfoList) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ServiceAndVersionID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务ID"`

	ServiceVersionId string `protobuf:"bytes,2,opt,name=service_version_id,json=serviceVersionId,proto3" json:"service_version_id" description:"服务版本ID"`

	Version int32 `protobuf:"varint,3,opt,name=version,proto3" json:"version" description:"服务版本id"`

	NeedStart bool `protobuf:"varint,4,opt,name=need_start,json=needStart,proto3" json:"need_start" description:"是否需要重启"`
}

func (x *ServiceAndVersionID) Reset() {
	*x = ServiceAndVersionID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAndVersionID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAndVersionID) ProtoMessage() {}

func (x *ServiceAndVersionID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAndVersionID.ProtoReflect.Descriptor instead.
func (*ServiceAndVersionID) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceAndVersionID) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceAndVersionID) GetServiceVersionId() string {
	if x != nil {
		return x.ServiceVersionId
	}
	return ""
}

func (x *ServiceAndVersionID) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *ServiceAndVersionID) GetNeedStart() bool {
	if x != nil {
		return x.NeedStart
	}
	return false
}

type ServiceID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	OrderBy string `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by" description:"排序规则: version(按版本名称:v1,v2), 为空默认按权重"`
}

func (x *ServiceID) Reset() {
	*x = ServiceID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceID) ProtoMessage() {}

func (x *ServiceID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceID.ProtoReflect.Descriptor instead.
func (*ServiceID) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{7}
}

func (x *ServiceID) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceID) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type UpdateServiceRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	NeedStart bool `protobuf:"varint,2,opt,name=need_start,json=needStart,proto3" json:"need_start" description:"是否需要重启"`
}

func (x *UpdateServiceRes) Reset() {
	*x = UpdateServiceRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRes) ProtoMessage() {}

func (x *UpdateServiceRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRes.ProtoReflect.Descriptor instead.
func (*UpdateServiceRes) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateServiceRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateServiceRes) GetNeedStart() bool {
	if x != nil {
		return x.NeedStart
	}
	return false
}

type ApiUniqueReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	Api string `protobuf:"bytes,2,opt,name=api,proto3" json:"api" description:"服务api"`
}

func (x *ApiUniqueReq) Reset() {
	*x = ApiUniqueReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiUniqueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiUniqueReq) ProtoMessage() {}

func (x *ApiUniqueReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiUniqueReq.ProtoReflect.Descriptor instead.
func (*ApiUniqueReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{9}
}

func (x *ApiUniqueReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ApiUniqueReq) GetApi() string {
	if x != nil {
		return x.Api
	}
	return ""
}

type ApiUniqueRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUnique bool `protobuf:"varint,1,opt,name=is_unique,json=isUnique,proto3" json:"is_unique" description:"是否唯一"`
}

func (x *ApiUniqueRes) Reset() {
	*x = ApiUniqueRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiUniqueRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiUniqueRes) ProtoMessage() {}

func (x *ApiUniqueRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiUniqueRes.ProtoReflect.Descriptor instead.
func (*ApiUniqueRes) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{10}
}

func (x *ApiUniqueRes) GetIsUnique() bool {
	if x != nil {
		return x.IsUnique
	}
	return false
}

type RemoteService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	VirtualSvcUrl string `protobuf:"bytes,2,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url" description:"绑定服务的vs url"`
}

func (x *RemoteService) Reset() {
	*x = RemoteService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteService) ProtoMessage() {}

func (x *RemoteService) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteService.ProtoReflect.Descriptor instead.
func (*RemoteService) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{11}
}

func (x *RemoteService) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoteService) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

type DeployID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`
}

func (x *DeployID) Reset() {
	*x = DeployID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployID) ProtoMessage() {}

func (x *DeployID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployID.ProtoReflect.Descriptor instead.
func (*DeployID) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{12}
}

func (x *DeployID) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ServiceIDVersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务ID"`

	ServiceVersionInfo *MLOpsServiceVersionInfo `protobuf:"bytes,2,opt,name=service_version_info,json=serviceVersionInfo,proto3" json:"service_version_info" description:"服务版本基础信息"`
}

func (x *ServiceIDVersionInfo) Reset() {
	*x = ServiceIDVersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceIDVersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceIDVersionInfo) ProtoMessage() {}

func (x *ServiceIDVersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceIDVersionInfo.ProtoReflect.Descriptor instead.
func (*ServiceIDVersionInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceIDVersionInfo) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceIDVersionInfo) GetServiceVersionInfo() *MLOpsServiceVersionInfo {
	if x != nil {
		return x.ServiceVersionInfo
	}
	return nil
}

type UpdateServiceVersionInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务ID"`

	ServiceVersionId string `protobuf:"bytes,2,opt,name=service_version_id,json=serviceVersionId,proto3" json:"service_version_id" description:"服务版本ID"`
}

func (x *UpdateServiceVersionInfoResp) Reset() {
	*x = UpdateServiceVersionInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceVersionInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceVersionInfoResp) ProtoMessage() {}

func (x *UpdateServiceVersionInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceVersionInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateServiceVersionInfoResp) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateServiceVersionInfoResp) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *UpdateServiceVersionInfoResp) GetServiceVersionId() string {
	if x != nil {
		return x.ServiceVersionId
	}
	return ""
}

type MLOpsSvcRuntimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	State MLOpsSvcState `protobuf:"varint,2,opt,name=state,proto3,enum=serving.MLOpsSvcState" json:"state" description:"@Deprecated 服务状态 0下线，1上线，2创建中,3失败"`

	HttpCallInfo *MLOpsSvcHttpCallInfo `protobuf:"bytes,3,opt,name=http_call_info,json=httpCallInfo,proto3" json:"http_call_info" description:"http调用信息"`

	Namespace string `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace" description:"namespace"`

	SeldonDeploymentName string `protobuf:"bytes,5,opt,name=seldon_deployment_name,json=seldonDeploymentName,proto3" json:"seldon_deployment_name" description:"namespace"`

	ServiceVersions []*MLOpsSvcVersionRuntimeInfo `protobuf:"bytes,6,rep,name=service_versions,json=serviceVersions,proto3" json:"service_versions" description:"服务版本信息"`

	StateInfo *MLOpsSvcStateInfo `protobuf:"bytes,7,opt,name=state_info,json=stateInfo,proto3" json:"state_info" description:"服务状态"`
}

func (x *MLOpsSvcRuntimeInfo) Reset() {
	*x = MLOpsSvcRuntimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcRuntimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcRuntimeInfo) ProtoMessage() {}

func (x *MLOpsSvcRuntimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcRuntimeInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcRuntimeInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{15}
}

func (x *MLOpsSvcRuntimeInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsSvcRuntimeInfo) GetState() MLOpsSvcState {
	if x != nil {
		return x.State
	}
	return MLOpsSvcState_MLOPS_SVC_STATE_OFFLINE
}

func (x *MLOpsSvcRuntimeInfo) GetHttpCallInfo() *MLOpsSvcHttpCallInfo {
	if x != nil {
		return x.HttpCallInfo
	}
	return nil
}

func (x *MLOpsSvcRuntimeInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *MLOpsSvcRuntimeInfo) GetSeldonDeploymentName() string {
	if x != nil {
		return x.SeldonDeploymentName
	}
	return ""
}

func (x *MLOpsSvcRuntimeInfo) GetServiceVersions() []*MLOpsSvcVersionRuntimeInfo {
	if x != nil {
		return x.ServiceVersions
	}
	return nil
}

func (x *MLOpsSvcRuntimeInfo) GetStateInfo() *MLOpsSvcStateInfo {
	if x != nil {
		return x.StateInfo
	}
	return nil
}

type MLOpsSvcHttpCallInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GatewayAddress string `protobuf:"bytes,1,opt,name=gateway_address,json=gatewayAddress,proto3" json:"gateway_address" description:"gateway(istio) 地址"`

	VirtualUrl string `protobuf:"bytes,2,opt,name=virtual_url,json=virtualUrl,proto3" json:"virtual_url" description:"gateway(istio) 路由路径"`

	// Deprecated: 迁移到Endpoint
	HttpApis  []*API             `protobuf:"bytes,3,rep,name=http_apis,json=httpApis,proto3" json:"http_apis" description:"http调用api"`
	Endpoints []*common.Endpoint `protobuf:"bytes,5,rep,name=endpoints,proto3" json:"endpoints"`

	GatewayNodePort int32 `protobuf:"varint,4,opt,name=gateway_node_port,json=gatewayNodePort,proto3" json:"gateway_node_port" description:"gateway(istio) 对外暴露的port"`
}

func (x *MLOpsSvcHttpCallInfo) Reset() {
	*x = MLOpsSvcHttpCallInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcHttpCallInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcHttpCallInfo) ProtoMessage() {}

func (x *MLOpsSvcHttpCallInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcHttpCallInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcHttpCallInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{16}
}

func (x *MLOpsSvcHttpCallInfo) GetGatewayAddress() string {
	if x != nil {
		return x.GatewayAddress
	}
	return ""
}

func (x *MLOpsSvcHttpCallInfo) GetVirtualUrl() string {
	if x != nil {
		return x.VirtualUrl
	}
	return ""
}

func (x *MLOpsSvcHttpCallInfo) GetHttpApis() []*API {
	if x != nil {
		return x.HttpApis
	}
	return nil
}

func (x *MLOpsSvcHttpCallInfo) GetEndpoints() []*common.Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MLOpsSvcHttpCallInfo) GetGatewayNodePort() int32 {
	if x != nil {
		return x.GatewayNodePort
	}
	return 0
}

type MLOpsSvcVersionRuntimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务版本ID"`

	Widget int32 `protobuf:"varint,2,opt,name=widget,proto3" json:"widget" description:"服务权重"`

	K8SDeployName string `protobuf:"bytes,3,opt,name=k8s_deploy_name,json=k8sDeployName,proto3" json:"k8s_deploy_name" description:"k8s deployment name"`

	K8SServiceName string `protobuf:"bytes,4,opt,name=k8s_service_name,json=k8sServiceName,proto3" json:"k8s_service_name" description:"k8s service name"`

	Pods []*MLOpsSvcVersionPodInfo `protobuf:"bytes,5,rep,name=pods,proto3" json:"pods" description:"pod信息"`
}

func (x *MLOpsSvcVersionRuntimeInfo) Reset() {
	*x = MLOpsSvcVersionRuntimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcVersionRuntimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcVersionRuntimeInfo) ProtoMessage() {}

func (x *MLOpsSvcVersionRuntimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcVersionRuntimeInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcVersionRuntimeInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{17}
}

func (x *MLOpsSvcVersionRuntimeInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsSvcVersionRuntimeInfo) GetWidget() int32 {
	if x != nil {
		return x.Widget
	}
	return 0
}

func (x *MLOpsSvcVersionRuntimeInfo) GetK8SDeployName() string {
	if x != nil {
		return x.K8SDeployName
	}
	return ""
}

func (x *MLOpsSvcVersionRuntimeInfo) GetK8SServiceName() string {
	if x != nil {
		return x.K8SServiceName
	}
	return ""
}

func (x *MLOpsSvcVersionRuntimeInfo) GetPods() []*MLOpsSvcVersionPodInfo {
	if x != nil {
		return x.Pods
	}
	return nil
}

type MLOpsSvcVersionPodInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"容器name"`

	NodeName string `protobuf:"bytes,2,opt,name=node_name,json=nodeName,proto3" json:"node_name" description:"节点ID"`

	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state" description:"pod状态"`

	Containers []*MLOpsSvcVersionContainerInfo `protobuf:"bytes,4,rep,name=containers,proto3" json:"containers" description:"容器信息"`
}

func (x *MLOpsSvcVersionPodInfo) Reset() {
	*x = MLOpsSvcVersionPodInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcVersionPodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcVersionPodInfo) ProtoMessage() {}

func (x *MLOpsSvcVersionPodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcVersionPodInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcVersionPodInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{18}
}

func (x *MLOpsSvcVersionPodInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MLOpsSvcVersionPodInfo) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *MLOpsSvcVersionPodInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *MLOpsSvcVersionPodInfo) GetContainers() []*MLOpsSvcVersionContainerInfo {
	if x != nil {
		return x.Containers
	}
	return nil
}

type MLOpsSvcVersionContainerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"容器id-对应k8s中的container name"`

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"容器名-对应界面上展示的名称"`
}

func (x *MLOpsSvcVersionContainerInfo) Reset() {
	*x = MLOpsSvcVersionContainerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcVersionContainerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcVersionContainerInfo) ProtoMessage() {}

func (x *MLOpsSvcVersionContainerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcVersionContainerInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcVersionContainerInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{19}
}

func (x *MLOpsSvcVersionContainerInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsSvcVersionContainerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SourceMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Extra map[string]string `protobuf:"bytes,1,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"来源额外信息"`

	DeployDesc string `protobuf:"bytes,2,opt,name=deploy_desc,json=deployDesc,proto3" json:"deploy_desc" description:"部署对象"`

	IsApp bool `protobuf:"varint,3,opt,name=is_app,json=isApp,proto3" json:"is_app" description:"是否是应用的自定义部署"`

	SdepId string `protobuf:"bytes,4,opt,name=sdep_id,json=sdepId,proto3" json:"sdep_id" description:"服务的sdep id"`

	Pods []string `protobuf:"bytes,5,rep,name=pods,proto3" json:"pods" description:"pod列表"`
}

func (x *SourceMeta) Reset() {
	*x = SourceMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceMeta) ProtoMessage() {}

func (x *SourceMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceMeta.ProtoReflect.Descriptor instead.
func (*SourceMeta) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{20}
}

func (x *SourceMeta) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *SourceMeta) GetDeployDesc() string {
	if x != nil {
		return x.DeployDesc
	}
	return ""
}

func (x *SourceMeta) GetIsApp() bool {
	if x != nil {
		return x.IsApp
	}
	return false
}

func (x *SourceMeta) GetSdepId() string {
	if x != nil {
		return x.SdepId
	}
	return ""
}

func (x *SourceMeta) GetPods() []string {
	if x != nil {
		return x.Pods
	}
	return nil
}

type CustomDeployCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detail string `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail" description:"detail"`
}

func (x *CustomDeployCfg) Reset() {
	*x = CustomDeployCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomDeployCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomDeployCfg) ProtoMessage() {}

func (x *CustomDeployCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomDeployCfg.ProtoReflect.Descriptor instead.
func (*CustomDeployCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{21}
}

func (x *CustomDeployCfg) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

type RoutingPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 路由路径
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	// 端口
	Port string `protobuf:"bytes,2,opt,name=port,proto3" json:"port"`
}

func (x *RoutingPath) Reset() {
	*x = RoutingPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoutingPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoutingPath) ProtoMessage() {}

func (x *RoutingPath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoutingPath.ProtoReflect.Descriptor instead.
func (*RoutingPath) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{22}
}

func (x *RoutingPath) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RoutingPath) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

type MLOpsServiceBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID(后端默认生成，不需要传)"`

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"服务名字(需要传递)"`
	// @gotags:
	// description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型、11自定义镜像(需要传)"
	SourceType SourceType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=serving.SourceType" json:"source_type"`

	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc" description:"描述 (可不需要传)"`

	Creator string `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator" description:"创建人(不需要传)"`

	Cluster string `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster" description:"集群信息(不需要传)"`

	IsAsync bool `protobuf:"varint,7,opt,name=is_async,json=isAsync,proto3" json:"is_async" description:"是否开启异步调用(不需要传)"`

	DeployCfg *DeployCfg `protobuf:"bytes,8,opt,name=deploy_cfg,json=deployCfg,proto3,oneof" json:"deploy_cfg" description:"部署模式(默认为：独立模式 部署，可不传)"`

	// 路由路径(默认可不传，模型如果自定义了url，可从mw那边拿到)"
	VirtualSvcUrl string `protobuf:"bytes,9,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url"`

	// Deprecated: 迁移到Endpoint
	Apis []*API `protobuf:"bytes,10,rep,name=apis,proto3" json:"apis" description:"api列表(模型对外暴露的api列表，直接传递mw拿到的数据)"`

	Endpoints []*common.Endpoint `protobuf:"bytes,33,rep,name=endpoints,proto3" json:"endpoints" description:"endpoint列表(模型对外暴露的endpoint列表, 对上边API结构做了修改)"`

	State MLOpsSvcState `protobuf:"varint,11,opt,name=state,proto3,enum=serving.MLOpsSvcState" json:"state" description:"服务状态,0下线，1上线，2创建中,3失败(不需要传)"`

	VersionCnt int32 `protobuf:"varint,12,opt,name=version_cnt,json=versionCnt,proto3" json:"version_cnt" description:"服务版本数量(不需要传)"`

	CreateTime int32 `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间(不需要传)"`

	UpdateTime int32 `protobuf:"varint,14,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间(不需要传)"`

	IsShare bool `protobuf:"varint,15,opt,name=is_share,json=isShare,proto3" json:"is_share" description:"是否共享至资产市场(可不需要传)"`

	SourceMeta *SourceMeta `protobuf:"bytes,16,opt,name=source_meta,json=sourceMeta,proto3" json:"source_meta" description:"来源额外信息(直接传递mw拿到的数据)"`

	Labels      map[string]*common.StringList `protobuf:"bytes,17,rep,name=labels,proto3" json:"labels" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"标签(直接传递mw拿到的数据)"`
	ShareConfig *ShareConfig                  `protobuf:"bytes,18,opt,name=share_config,json=shareConfig,proto3" json:"share_config"`

	GuardrailsConfig *GuardrailsConfig `protobuf:"bytes,20,opt,name=guardrails_config,json=guardrailsConfig,proto3" json:"guardrails_config" description:"安全栅栏配置"`

	// 单价/千token)"
	BillingConfig *common.BillingConfig `protobuf:"bytes,31,opt,name=billing_config,json=billingConfig,proto3" json:"billing_config"`

	LimitTime int32 `protobuf:"varint,21,opt,name=limit_time,json=limitTime,proto3" json:"limit_time" description:"最大运行时长(默认传-1)"`

	StateInfo *MLOpsSvcStateInfo `protobuf:"bytes,22,opt,name=state_info,json=stateInfo,proto3" json:"state_info" description:"服务状态信息(不需要传)"`

	LimitTimeDb int32 `protobuf:"varint,23,opt,name=limit_time_db,json=limitTimeDb,proto3" json:"limit_time_db" description:"监听器使用的最大运行时长(不需要传，后端回显)"`

	IsRollUpdate bool `protobuf:"varint,24,opt,name=is_roll_update,json=isRollUpdate,proto3" json:"is_roll_update" description:"是否滚动更新(默认传true)"`

	TotalRateQps float64 `protobuf:"fixed64,25,opt,name=total_rate_qps,json=totalRateQps,proto3" json:"total_rate_qps" description:"熔断限流的总qps(后端回显)"`

	IsTextGeneration bool `protobuf:"varint,26,opt,name=is_text_generation,json=isTextGeneration,proto3" json:"is_text_generation" description:"是否是文本生成类模型(后端回显)"`

	Updater string `protobuf:"bytes,27,opt,name=updater,proto3" json:"updater" description:"更新人，创建&更新服务版本的时候不需要传"`

	ClusterName string `protobuf:"bytes,28,opt,name=cluster_name,json=clusterName,proto3" json:"cluster_name" description:"空间名(不需要传，后端回显)"`

	ProjectId string `protobuf:"bytes,29,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"project_id"`

	EditModel string `protobuf:"bytes,30,opt,name=edit_model,json=editModel,proto3" json:"edit_model" description:"编辑模式，可选commom/yaml"`

	EnableAnonymousCall bool `protobuf:"varint,32,opt,name=enable_anonymous_call,json=enableAnonymousCall,proto3" json:"enable_anonymous_call" description:"是否允许匿名调用"`

	ResourceSummaryInfo *ResourceSummaryInfo `protobuf:"bytes,34,opt,name=resource_summary_info,json=resourceSummaryInfo,proto3" json:"resource_summary_info" description:"资源汇总信息"`

	Nodes []string `protobuf:"bytes,35,rep,name=nodes,proto3" json:"nodes" description:"节点信息"`

	ModelInferSpec *common.ModelInferSpec `protobuf:"bytes,36,opt,name=model_infer_spec,json=modelInferSpec,proto3" json:"model_infer_spec" description:"模型类型信息"`
}

func (x *MLOpsServiceBaseInfo) Reset() {
	*x = MLOpsServiceBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsServiceBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsServiceBaseInfo) ProtoMessage() {}

func (x *MLOpsServiceBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsServiceBaseInfo.ProtoReflect.Descriptor instead.
func (*MLOpsServiceBaseInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{23}
}

func (x *MLOpsServiceBaseInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNKNOW
}

func (x *MLOpsServiceBaseInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetIsAsync() bool {
	if x != nil {
		return x.IsAsync
	}
	return false
}

func (x *MLOpsServiceBaseInfo) GetDeployCfg() *DeployCfg {
	if x != nil {
		return x.DeployCfg
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetApis() []*API {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetEndpoints() []*common.Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetState() MLOpsSvcState {
	if x != nil {
		return x.State
	}
	return MLOpsSvcState_MLOPS_SVC_STATE_OFFLINE
}

func (x *MLOpsServiceBaseInfo) GetVersionCnt() int32 {
	if x != nil {
		return x.VersionCnt
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetUpdateTime() int32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetIsShare() bool {
	if x != nil {
		return x.IsShare
	}
	return false
}

func (x *MLOpsServiceBaseInfo) GetSourceMeta() *SourceMeta {
	if x != nil {
		return x.SourceMeta
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetLabels() map[string]*common.StringList {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetShareConfig() *ShareConfig {
	if x != nil {
		return x.ShareConfig
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetGuardrailsConfig() *GuardrailsConfig {
	if x != nil {
		return x.GuardrailsConfig
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetBillingConfig() *common.BillingConfig {
	if x != nil {
		return x.BillingConfig
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetLimitTime() int32 {
	if x != nil {
		return x.LimitTime
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetStateInfo() *MLOpsSvcStateInfo {
	if x != nil {
		return x.StateInfo
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetLimitTimeDb() int32 {
	if x != nil {
		return x.LimitTimeDb
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetIsRollUpdate() bool {
	if x != nil {
		return x.IsRollUpdate
	}
	return false
}

func (x *MLOpsServiceBaseInfo) GetTotalRateQps() float64 {
	if x != nil {
		return x.TotalRateQps
	}
	return 0
}

func (x *MLOpsServiceBaseInfo) GetIsTextGeneration() bool {
	if x != nil {
		return x.IsTextGeneration
	}
	return false
}

func (x *MLOpsServiceBaseInfo) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetEditModel() string {
	if x != nil {
		return x.EditModel
	}
	return ""
}

func (x *MLOpsServiceBaseInfo) GetEnableAnonymousCall() bool {
	if x != nil {
		return x.EnableAnonymousCall
	}
	return false
}

func (x *MLOpsServiceBaseInfo) GetResourceSummaryInfo() *ResourceSummaryInfo {
	if x != nil {
		return x.ResourceSummaryInfo
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *MLOpsServiceBaseInfo) GetModelInferSpec() *common.ModelInferSpec {
	if x != nil {
		return x.ModelInferSpec
	}
	return nil
}

type GuardrailsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSecurity bool `protobuf:"varint,1,opt,name=is_security,json=isSecurity,proto3" json:"is_security" description:"是否开启安全栅栏(默认false)"`

	GuardrailsId string `protobuf:"bytes,2,opt,name=guardrails_id,json=guardrailsId,proto3" json:"guardrails_id" description:"安全栅栏id"`
}

func (x *GuardrailsConfig) Reset() {
	*x = GuardrailsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuardrailsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuardrailsConfig) ProtoMessage() {}

func (x *GuardrailsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuardrailsConfig.ProtoReflect.Descriptor instead.
func (*GuardrailsConfig) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{24}
}

func (x *GuardrailsConfig) GetIsSecurity() bool {
	if x != nil {
		return x.IsSecurity
	}
	return false
}

func (x *GuardrailsConfig) GetGuardrailsId() string {
	if x != nil {
		return x.GuardrailsId
	}
	return ""
}

type RateLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestLimit int32 `protobuf:"varint,1,opt,name=request_limit,json=requestLimit,proto3" json:"request_limit" description:"限速间隔时间内允许通过的请求（qps）"`

	UnitInterval int32 `protobuf:"varint,2,opt,name=unit_interval,json=unitInterval,proto3" json:"unit_interval" description:"限速间隔时间，单位为s"`

	Enabled bool `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled" description:"是否开启了限流"`

	TokenPerMinute int32 `protobuf:"varint,4,opt,name=token_per_minute,json=tokenPerMinute,proto3" json:"token_per_minute" description:"一分钟内允许通过的token数"`
}

func (x *RateLimit) Reset() {
	*x = RateLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimit) ProtoMessage() {}

func (x *RateLimit) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimit.ProtoReflect.Descriptor instead.
func (*RateLimit) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{25}
}

func (x *RateLimit) GetRequestLimit() int32 {
	if x != nil {
		return x.RequestLimit
	}
	return 0
}

func (x *RateLimit) GetUnitInterval() int32 {
	if x != nil {
		return x.UnitInterval
	}
	return 0
}

func (x *RateLimit) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *RateLimit) GetTokenPerMinute() int32 {
	if x != nil {
		return x.TokenPerMinute
	}
	return 0
}

type MLOpsRemoteServiceInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"服务ID"`

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"服务名字"`

	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc" description:"描述"`

	Creator string `protobuf:"bytes,4,opt,name=creator,proto3" json:"creator" description:"创建人"`

	VirtualSvcUrl string `protobuf:"bytes,5,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url" description:"virtual service 路由路径"`

	// Deprecated: 迁移到Endpoint
	Apis []*API `protobuf:"bytes,6,rep,name=apis,proto3" json:"apis" description:"api列表"`

	Endpoints []*common.Endpoint `protobuf:"bytes,22,rep,name=endpoints,proto3" json:"endpoints" description:"endpoint列表"`

	CreateTime int32 `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`

	UpdateTime int32 `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间"`

	Labels map[string]*common.StringList `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"标签"`

	RateLimit *RateLimit `protobuf:"bytes,11,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit" description:"服务限流配置"`

	UserRateLimit *RateLimit `protobuf:"bytes,21,opt,name=user_rate_limit,json=userRateLimit,proto3" json:"user_rate_limit" description:"用户限流配置"`

	GuardrailsConfig *GuardrailsConfig `protobuf:"bytes,12,opt,name=guardrails_config,json=guardrailsConfig,proto3" json:"guardrails_config" description:"安全栅栏配置"`

	BillingConfig *common.BillingConfig `protobuf:"bytes,13,opt,name=billing_config,json=billingConfig,proto3" json:"billing_config" description:"计费规则"`

	LimitTime int32 `protobuf:"varint,14,opt,name=limit_time,json=limitTime,proto3" json:"limit_time" description:"最大运行时长"`

	Headers map[string]string `protobuf:"bytes,16,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"远程模型 headers"`

	QueryParams map[string]string `protobuf:"bytes,17,rep,name=query_params,json=queryParams,proto3" json:"query_params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"远程模型 query"`

	RemoteServiceUrl string `protobuf:"bytes,18,opt,name=remote_service_url,json=remoteServiceUrl,proto3" json:"remote_service_url" description:"远程模型 prefix url"`

	SourceMeta *SourceMeta `protobuf:"bytes,19,opt,name=source_meta,json=sourceMeta,proto3" json:"source_meta" description:"标签"`
	// @gotags:
	// description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型
	// 5、知识库　11、11自定义镜像"
	SourceType SourceType `protobuf:"varint,20,opt,name=source_type,json=sourceType,proto3,enum=serving.SourceType" json:"source_type"`

	EnableAnonymousCall bool `protobuf:"varint,31,opt,name=enable_anonymous_call,json=enableAnonymousCall,proto3" json:"enable_anonymous_call" description:"是否允许匿名调用"`
}

func (x *MLOpsRemoteServiceInfoReq) Reset() {
	*x = MLOpsRemoteServiceInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsRemoteServiceInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsRemoteServiceInfoReq) ProtoMessage() {}

func (x *MLOpsRemoteServiceInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsRemoteServiceInfoReq.ProtoReflect.Descriptor instead.
func (*MLOpsRemoteServiceInfoReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{26}
}

func (x *MLOpsRemoteServiceInfoReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetApis() []*API {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetEndpoints() []*common.Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MLOpsRemoteServiceInfoReq) GetUpdateTime() int32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *MLOpsRemoteServiceInfoReq) GetLabels() map[string]*common.StringList {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetUserRateLimit() *RateLimit {
	if x != nil {
		return x.UserRateLimit
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetGuardrailsConfig() *GuardrailsConfig {
	if x != nil {
		return x.GuardrailsConfig
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetBillingConfig() *common.BillingConfig {
	if x != nil {
		return x.BillingConfig
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetLimitTime() int32 {
	if x != nil {
		return x.LimitTime
	}
	return 0
}

func (x *MLOpsRemoteServiceInfoReq) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetQueryParams() map[string]string {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetRemoteServiceUrl() string {
	if x != nil {
		return x.RemoteServiceUrl
	}
	return ""
}

func (x *MLOpsRemoteServiceInfoReq) GetSourceMeta() *SourceMeta {
	if x != nil {
		return x.SourceMeta
	}
	return nil
}

func (x *MLOpsRemoteServiceInfoReq) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNKNOW
}

func (x *MLOpsRemoteServiceInfoReq) GetEnableAnonymousCall() bool {
	if x != nil {
		return x.EnableAnonymousCall
	}
	return false
}

type ShareConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsShare bool `protobuf:"varint,1,opt,name=is_share,json=isShare,proto3" json:"is_share" description:"是否共享至资产市场"`

	ShareUsers []string `protobuf:"bytes,2,rep,name=share_users,json=shareUsers,proto3" json:"share_users" description:"共享可查看的用户"`

	ShareGroups []string `protobuf:"bytes,3,rep,name=share_groups,json=shareGroups,proto3" json:"share_groups" description:"共享可查看的用户组"`
}

func (x *ShareConfig) Reset() {
	*x = ShareConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareConfig) ProtoMessage() {}

func (x *ShareConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareConfig.ProtoReflect.Descriptor instead.
func (*ShareConfig) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{27}
}

func (x *ShareConfig) GetIsShare() bool {
	if x != nil {
		return x.IsShare
	}
	return false
}

func (x *ShareConfig) GetShareUsers() []string {
	if x != nil {
		return x.ShareUsers
	}
	return nil
}

func (x *ShareConfig) GetShareGroups() []string {
	if x != nil {
		return x.ShareGroups
	}
	return nil
}

type MLOpsSvcStateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state" description:"状态"`

	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message" description:"状态补充信息"`
}

func (x *MLOpsSvcStateInfo) Reset() {
	*x = MLOpsSvcStateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsSvcStateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsSvcStateInfo) ProtoMessage() {}

func (x *MLOpsSvcStateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsSvcStateInfo.ProtoReflect.Descriptor instead.
func (*MLOpsSvcStateInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{28}
}

func (x *MLOpsSvcStateInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *MLOpsSvcStateInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeployCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeployStrategy *DeployStrategy `protobuf:"varint,1,opt,name=deploy_strategy,json=deployStrategy,proto3,enum=serving.DeployStrategy,oneof" json:"deploy_strategy" description:"部署策略，0独立部署，1灰度部署，2冠军挑战者部署"`

	MainDeployVersion string `protobuf:"bytes,2,opt,name=main_deploy_version,json=mainDeployVersion,proto3" json:"main_deploy_version" description:"独立部署的版本ID"`

	Widget map[string]int32 `protobuf:"bytes,3,rep,name=widget,proto3" json:"widget" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3" description:"权重"`

	Champion string `protobuf:"bytes,4,opt,name=champion,proto3" json:"champion" description:"冠军实例"`

	Shadows []string `protobuf:"bytes,5,rep,name=shadows,proto3" json:"shadows" description:"挑战者实例"`
}

func (x *DeployCfg) Reset() {
	*x = DeployCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployCfg) ProtoMessage() {}

func (x *DeployCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployCfg.ProtoReflect.Descriptor instead.
func (*DeployCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{29}
}

func (x *DeployCfg) GetDeployStrategy() DeployStrategy {
	if x != nil && x.DeployStrategy != nil {
		return *x.DeployStrategy
	}
	return DeployStrategy_DEPLOY_STRATEGY_MAIN_DEPLOY
}

func (x *DeployCfg) GetMainDeployVersion() string {
	if x != nil {
		return x.MainDeployVersion
	}
	return ""
}

func (x *DeployCfg) GetWidget() map[string]int32 {
	if x != nil {
		return x.Widget
	}
	return nil
}

func (x *DeployCfg) GetChampion() string {
	if x != nil {
		return x.Champion
	}
	return ""
}

func (x *DeployCfg) GetShadows() []string {
	if x != nil {
		return x.Shadows
	}
	return nil
}

// Deprecated: 不再使用，迁移到Endpoint
type API struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port uint32 `protobuf:"varint,1,opt,name=port,proto3" json:"port" description:"端口"`

	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type" description:"API类型，http，grpc等"`

	Url []string `protobuf:"bytes,3,rep,name=url,proto3" json:"url" description:"端口对应的API"`

	UrlParamMap map[string]string `protobuf:"bytes,4,rep,name=url_param_map,json=urlParamMap,proto3" json:"url_param_map" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"url对应的map"`

	UrlHttpMethodMap map[string]string `protobuf:"bytes,5,rep,name=url_http_method_map,json=urlHttpMethodMap,proto3" json:"url_http_method_map" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"url对应method，支持GET/POST，默认POST"`

	ApiSpec map[string]common.APISpec `protobuf:"bytes,6,rep,name=api_spec,json=apiSpec,proto3" json:"api_spec" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=commons.APISpec" description:"api所符合的接口规范说明"`

	ApiFunc map[string]string `protobuf:"bytes,7,rep,name=api_func,json=apiFunc,proto3" json:"api_func" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"api功能,使用的是ModelKind与ModelSubkind的定义，但是因为循环引用的问题，这里使用枚举字符串，格式为ModelKind/ModelSubkind"`

	DefaultUrl string `protobuf:"bytes,8,opt,name=default_url,json=defaultUrl,proto3" json:"default_url" description:"默认url"`
}

func (x *API) Reset() {
	*x = API{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *API) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*API) ProtoMessage() {}

func (x *API) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use API.ProtoReflect.Descriptor instead.
func (*API) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{30}
}

func (x *API) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *API) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *API) GetUrl() []string {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *API) GetUrlParamMap() map[string]string {
	if x != nil {
		return x.UrlParamMap
	}
	return nil
}

func (x *API) GetUrlHttpMethodMap() map[string]string {
	if x != nil {
		return x.UrlHttpMethodMap
	}
	return nil
}

func (x *API) GetApiSpec() map[string]common.APISpec {
	if x != nil {
		return x.ApiSpec
	}
	return nil
}

func (x *API) GetApiFunc() map[string]string {
	if x != nil {
		return x.ApiFunc
	}
	return nil
}

func (x *API) GetDefaultUrl() string {
	if x != nil {
		return x.DefaultUrl
	}
	return ""
}

type MountCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeCfg *VolumeCfg `protobuf:"bytes,1,opt,name=volume_cfg,json=volumeCfg,proto3" json:"volume_cfg" description:"持久卷配置"`

	MountPaths []*MountPath `protobuf:"bytes,2,rep,name=mount_paths,json=mountPaths,proto3" json:"mount_paths" description:"挂载配置"`
}

func (x *MountCfg) Reset() {
	*x = MountCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountCfg) ProtoMessage() {}

func (x *MountCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountCfg.ProtoReflect.Descriptor instead.
func (*MountCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{31}
}

func (x *MountCfg) GetVolumeCfg() *VolumeCfg {
	if x != nil {
		return x.VolumeCfg
	}
	return nil
}

func (x *MountCfg) GetMountPaths() []*MountPath {
	if x != nil {
		return x.MountPaths
	}
	return nil
}

type VolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags:
	// description:"持久卷类型,0hostpath,1文件系统，2模型仓库，3样本仓库，4Memory"
	FileType MLOpsFileType `protobuf:"varint,1,opt,name=file_type,json=fileType,proto3,enum=serving.MLOpsFileType" json:"file_type"`

	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path" description:"路径"`

	Extra map[string]string `protobuf:"bytes,3,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"持久卷对应的名字，后端负责保存与透传，不作任何处理"`

	EnableMemorySizeLimit bool `protobuf:"varint,4,opt,name=enable_memory_size_limit,json=enableMemorySizeLimit,proto3" json:"enable_memory_size_limit" description:"是否开启内存限制"`

	MemorySizeLimitGib float32 `protobuf:"fixed32,5,opt,name=memory_size_limit_gib,json=memorySizeLimitGib,proto3" json:"memory_size_limit_gib" description:"内存大小限制"`

	ConfigMapValue string `protobuf:"bytes,6,opt,name=config_map_value,json=configMapValue,proto3" json:"config_map_value" description:"config map 对应的value"`
}

func (x *VolumeCfg) Reset() {
	*x = VolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeCfg) ProtoMessage() {}

func (x *VolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeCfg.ProtoReflect.Descriptor instead.
func (*VolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{32}
}

func (x *VolumeCfg) GetFileType() MLOpsFileType {
	if x != nil {
		return x.FileType
	}
	return MLOpsFileType_MLOPS_File_TYPE_HOST_PATH
}

func (x *VolumeCfg) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *VolumeCfg) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *VolumeCfg) GetEnableMemorySizeLimit() bool {
	if x != nil {
		return x.EnableMemorySizeLimit
	}
	return false
}

func (x *VolumeCfg) GetMemorySizeLimitGib() float32 {
	if x != nil {
		return x.MemorySizeLimitGib
	}
	return 0
}

func (x *VolumeCfg) GetConfigMapValue() string {
	if x != nil {
		return x.ConfigMapValue
	}
	return ""
}

type MountPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MountPath string `protobuf:"bytes,1,opt,name=mount_path,json=mountPath,proto3" json:"mount_path" description:"挂载路径"`

	Readonly bool `protobuf:"varint,2,opt,name=readonly,proto3" json:"readonly" description:"是否只读"`
}

func (x *MountPath) Reset() {
	*x = MountPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountPath) ProtoMessage() {}

func (x *MountPath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountPath.ProtoReflect.Descriptor instead.
func (*MountPath) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{33}
}

func (x *MountPath) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *MountPath) GetReadonly() bool {
	if x != nil {
		return x.Readonly
	}
	return false
}

type MLOpsServiceVersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"id，创建&更新服务版本的时候不需要传，后端生成"`

	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version" description:"版本，创建&更新服务版本的时候不需要传，后端生成"`

	SourceId string `protobuf:"bytes,3,opt,name=source_id,json=sourceId,proto3" json:"source_id" description:"来源ID 默认不需要传"`

	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name" description:"版本名字，保留字段，目前没用到b不需要传"`

	Desc string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc" description:"版本描述"`

	Creator string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator" description:"创建人，创建&更新服务版本的时候不需要传"`

	// {"strategy": 1} 随机节点"
	NodeChooseCfg *NodeChooseCfg `protobuf:"bytes,7,opt,name=node_choose_cfg,json=nodeChooseCfg,proto3" json:"node_choose_cfg" description:"节点选择相关配置，默认传递  "`

	// 0,"replicas": 1} '"
	HpaCfg *HPACfg `protobuf:"bytes,8,opt,name=hpa_cfg,json=hpaCfg,proto3" json:"hpa_cfg" description:"横向扩容配置 默认传:' "`

	UnlimitedGpu bool `protobuf:"varint,9,opt,name=unlimited_gpu,json=unlimitedGpu,proto3" json:"unlimited_gpu" description:"gpu不限额，已废弃，移到gpu_cfg中"`

	GpuGroup []string `protobuf:"bytes,10,rep,name=gpu_group,json=gpuGroup,proto3" json:"gpu_group" description:"可选gpu列表 废弃不需要传"`

	Containers []*MLOpsContainer `protobuf:"bytes,11,rep,name=containers,proto3" json:"containers" description:"容器"`

	CreateTime int32 `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间 不需要传"`

	UpdateTime int32 `protobuf:"varint,13,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间 不需要传"`

	Widget int32 `protobuf:"varint,14,opt,name=widget,proto3" json:"widget" description:"当前权重，创建&更新服务版本的时候不需要传"`

	ServiceId string `protobuf:"bytes,15,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务ID"`

	Shadow bool `protobuf:"varint,16,opt,name=shadow,proto3" json:"shadow" description:"是否是影子服务，创建&更新服务版本的时候不需要传"`

	EnableGpu bool `protobuf:"varint,17,opt,name=enable_gpu,json=enableGpu,proto3" json:"enable_gpu" description:"是否开启gpu，默认为false"`

	GpuCfg *GPUConfig `protobuf:"bytes,18,opt,name=gpu_cfg,json=gpuCfg,proto3" json:"gpu_cfg" description:"gpu配置,默认都为false"`

	Annotations map[string]string `protobuf:"bytes,19,rep,name=annotations,proto3" json:"annotations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"deployment级别的注解"`

	HostNetwork bool `protobuf:"varint,20,opt,name=host_network,json=hostNetwork,proto3" json:"host_network" description:"是否开启host网络，默认为false"`

	SourceMeta *SourceMeta `protobuf:"bytes,21,opt,name=source_meta,json=sourceMeta,proto3" json:"source_meta" description:"来源额外信息"`

	IsPowerRule bool `protobuf:"varint,22,opt,name=is_power_rule,json=isPowerRule,proto3" json:"is_power_rule" description:"资源配置来源(规格实例/高级配置)"`

	IsRemoteVersion bool `protobuf:"varint,23,opt,name=is_remote_version,json=isRemoteVersion,proto3" json:"is_remote_version" description:"是否是远程模型，不需要传"`

	Headers map[string]string `protobuf:"bytes,24,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"远程模型 headers"`

	QueryParams map[string]string `protobuf:"bytes,25,rep,name=query_params,json=queryParams,proto3" json:"query_params" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"远程模型 query"`

	RemoteServiceUrl string `protobuf:"bytes,26,opt,name=remote_service_url,json=remoteServiceUrl,proto3" json:"remote_service_url" description:"远程模型 url，默认不传"`

	RateLimit *RateLimit `protobuf:"bytes,27,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit" description:"服务限流配置(默认限流100 "`

	UserRateLimit *RateLimit `protobuf:"bytes,28,opt,name=user_rate_limit,json=userRateLimit,proto3" json:"user_rate_limit" description:"用户限流配置"`

	Arch string `protobuf:"bytes,29,opt,name=arch,proto3" json:"arch" description:"节点架构，可选amd64/arm64"`
}

func (x *MLOpsServiceVersionInfo) Reset() {
	*x = MLOpsServiceVersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsServiceVersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsServiceVersionInfo) ProtoMessage() {}

func (x *MLOpsServiceVersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsServiceVersionInfo.ProtoReflect.Descriptor instead.
func (*MLOpsServiceVersionInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{34}
}

func (x *MLOpsServiceVersionInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetNodeChooseCfg() *NodeChooseCfg {
	if x != nil {
		return x.NodeChooseCfg
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetHpaCfg() *HPACfg {
	if x != nil {
		return x.HpaCfg
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetUnlimitedGpu() bool {
	if x != nil {
		return x.UnlimitedGpu
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetGpuGroup() []string {
	if x != nil {
		return x.GpuGroup
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetContainers() []*MLOpsContainer {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MLOpsServiceVersionInfo) GetUpdateTime() int32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *MLOpsServiceVersionInfo) GetWidget() int32 {
	if x != nil {
		return x.Widget
	}
	return 0
}

func (x *MLOpsServiceVersionInfo) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetShadow() bool {
	if x != nil {
		return x.Shadow
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetEnableGpu() bool {
	if x != nil {
		return x.EnableGpu
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetGpuCfg() *GPUConfig {
	if x != nil {
		return x.GpuCfg
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetHostNetwork() bool {
	if x != nil {
		return x.HostNetwork
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetSourceMeta() *SourceMeta {
	if x != nil {
		return x.SourceMeta
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetIsPowerRule() bool {
	if x != nil {
		return x.IsPowerRule
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetIsRemoteVersion() bool {
	if x != nil {
		return x.IsRemoteVersion
	}
	return false
}

func (x *MLOpsServiceVersionInfo) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetQueryParams() map[string]string {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetRemoteServiceUrl() string {
	if x != nil {
		return x.RemoteServiceUrl
	}
	return ""
}

func (x *MLOpsServiceVersionInfo) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetUserRateLimit() *RateLimit {
	if x != nil {
		return x.UserRateLimit
	}
	return nil
}

func (x *MLOpsServiceVersionInfo) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type ResourceGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"资源组ID"`
}

func (x *ResourceGroupInfo) Reset() {
	*x = ResourceGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceGroupInfo) ProtoMessage() {}

func (x *ResourceGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceGroupInfo.ProtoReflect.Descriptor instead.
func (*ResourceGroupInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{35}
}

func (x *ResourceGroupInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type NodeSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node string `protobuf:"bytes,1,opt,name=node,proto3" json:"node" description:"具体的节点信息"`

	GpuCards []string `protobuf:"bytes,2,rep,name=gpu_cards,json=gpuCards,proto3" json:"gpu_cards" description:"节点卡号选择"`
}

func (x *NodeSelector) Reset() {
	*x = NodeSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSelector) ProtoMessage() {}

func (x *NodeSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSelector.ProtoReflect.Descriptor instead.
func (*NodeSelector) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{36}
}

func (x *NodeSelector) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *NodeSelector) GetGpuCards() []string {
	if x != nil {
		return x.GpuCards
	}
	return nil
}

type GPUConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GpuExclusive bool `protobuf:"varint,1,opt,name=gpu_exclusive,json=gpuExclusive,proto3" json:"gpu_exclusive" description:"是否独占gpu"`

	UnlimitedGpu bool `protobuf:"varint,24,opt,name=unlimited_gpu,json=unlimitedGpu,proto3" json:"unlimited_gpu" description:"是否不限额共享gpu"`
}

func (x *GPUConfig) Reset() {
	*x = GPUConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUConfig) ProtoMessage() {}

func (x *GPUConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUConfig.ProtoReflect.Descriptor instead.
func (*GPUConfig) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{37}
}

func (x *GPUConfig) GetGpuExclusive() bool {
	if x != nil {
		return x.GpuExclusive
	}
	return false
}

func (x *GPUConfig) GetUnlimitedGpu() bool {
	if x != nil {
		return x.UnlimitedGpu
	}
	return false
}

type MLOpsHttpGetAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path      string            `protobuf:"bytes,1,opt,name=path,proto3" json:"path"`
	Port      int32             `protobuf:"varint,2,opt,name=port,proto3" json:"port"`
	Host      string            `protobuf:"bytes,3,opt,name=host,proto3" json:"host"`
	UriScheme string            `protobuf:"bytes,4,opt,name=uri_scheme,json=uriScheme,proto3" json:"uri_scheme"`
	Headers   map[string]string `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MLOpsHttpGetAction) Reset() {
	*x = MLOpsHttpGetAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsHttpGetAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsHttpGetAction) ProtoMessage() {}

func (x *MLOpsHttpGetAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsHttpGetAction.ProtoReflect.Descriptor instead.
func (*MLOpsHttpGetAction) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{38}
}

func (x *MLOpsHttpGetAction) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *MLOpsHttpGetAction) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *MLOpsHttpGetAction) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *MLOpsHttpGetAction) GetUriScheme() string {
	if x != nil {
		return x.UriScheme
	}
	return ""
}

func (x *MLOpsHttpGetAction) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

type MLOpsProbe struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InitialDelaySeconds           int32               `protobuf:"varint,1,opt,name=initial_delay_seconds,json=initialDelaySeconds,proto3" json:"initial_delay_seconds"`
	TimeoutSeconds                int32               `protobuf:"varint,2,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds"`
	PeriodSeconds                 int32               `protobuf:"varint,3,opt,name=period_seconds,json=periodSeconds,proto3" json:"period_seconds"`
	SuccessThreshold              int32               `protobuf:"varint,4,opt,name=success_threshold,json=successThreshold,proto3" json:"success_threshold"`
	FailureThreshold              int32               `protobuf:"varint,5,opt,name=failure_threshold,json=failureThreshold,proto3" json:"failure_threshold"`
	TerminationGracePeriodSeconds *int32              `protobuf:"varint,7,opt,name=termination_grace_period_seconds,json=terminationGracePeriodSeconds,proto3,oneof" json:"termination_grace_period_seconds"`
	HttpGetAction                 *MLOpsHttpGetAction `protobuf:"bytes,8,opt,name=http_get_action,json=httpGetAction,proto3" json:"http_get_action"`
}

func (x *MLOpsProbe) Reset() {
	*x = MLOpsProbe{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsProbe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsProbe) ProtoMessage() {}

func (x *MLOpsProbe) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsProbe.ProtoReflect.Descriptor instead.
func (*MLOpsProbe) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{39}
}

func (x *MLOpsProbe) GetInitialDelaySeconds() int32 {
	if x != nil {
		return x.InitialDelaySeconds
	}
	return 0
}

func (x *MLOpsProbe) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *MLOpsProbe) GetPeriodSeconds() int32 {
	if x != nil {
		return x.PeriodSeconds
	}
	return 0
}

func (x *MLOpsProbe) GetSuccessThreshold() int32 {
	if x != nil {
		return x.SuccessThreshold
	}
	return 0
}

func (x *MLOpsProbe) GetFailureThreshold() int32 {
	if x != nil {
		return x.FailureThreshold
	}
	return 0
}

func (x *MLOpsProbe) GetTerminationGracePeriodSeconds() int32 {
	if x != nil && x.TerminationGracePeriodSeconds != nil {
		return *x.TerminationGracePeriodSeconds
	}
	return 0
}

func (x *MLOpsProbe) GetHttpGetAction() *MLOpsHttpGetAction {
	if x != nil {
		return x.HttpGetAction
	}
	return nil
}

type MLOpsContainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"id"`

	Image string `protobuf:"bytes,2,opt,name=image,proto3" json:"image" description:"镜像地址"`

	ImageType ImageType `protobuf:"varint,3,opt,name=image_type,json=imageType,proto3,enum=serving.ImageType" json:"image_type" description:"镜像类型，0自定义类型，1平台内镜像"`

	ContainerType ContainerType `protobuf:"varint,4,opt,name=container_type,json=containerType,proto3,enum=serving.ContainerType" json:"container_type" description:"容器类型，主容器，sidecar容器"`

	Resource *Resource `protobuf:"bytes,5,opt,name=resource,proto3" json:"resource" description:"资源限制"`
	// 实例规格json
	ResourceRule string `protobuf:"bytes,6,opt,name=resource_rule,json=resourceRule,proto3" json:"resource_rule"`

	Envs map[string]string `protobuf:"bytes,9,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"环境变量"`

	Cmds []string `protobuf:"bytes,10,rep,name=cmds,proto3" json:"cmds" description:"启动命令"`

	MountCfg []*MountCfg `protobuf:"bytes,11,rep,name=mount_cfg,json=mountCfg,proto3" json:"mount_cfg" description:"挂载配置"`

	CreateTime int32 `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`

	UpdateTime int32 `protobuf:"varint,13,opt,name=update_time,json=updateTime,proto3" json:"update_time" description:"更新时间"`

	ReadinessProbe *MLOpsProbe `protobuf:"bytes,14,opt,name=readiness_probe,json=readinessProbe,proto3" json:"readiness_probe" description:"就绪探针"`

	LivenessProbe *MLOpsProbe `protobuf:"bytes,15,opt,name=liveness_probe,json=livenessProbe,proto3" json:"liveness_probe" description:"存活探针"`

	ResourceId *int32 `protobuf:"varint,16,opt,name=resource_id,json=resourceId,proto3,oneof" json:"resource_id" description:"实例规格id"`

	ResourceGroups []*ResourceGroupInfo `protobuf:"bytes,17,rep,name=resource_groups,json=resourceGroups,proto3" json:"resource_groups" description:"资源组列表"`

	GpuUtilizationPolicy GPU_CORE_UTILIZATION_POLICY `protobuf:"varint,18,opt,name=gpu_utilization_policy,json=gpuUtilizationPolicy,proto3,enum=serving.GPU_CORE_UTILIZATION_POLICY" json:"gpu_utilization_policy" description:"算力限制策略"`
}

func (x *MLOpsContainer) Reset() {
	*x = MLOpsContainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsContainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsContainer) ProtoMessage() {}

func (x *MLOpsContainer) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsContainer.ProtoReflect.Descriptor instead.
func (*MLOpsContainer) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{40}
}

func (x *MLOpsContainer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MLOpsContainer) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *MLOpsContainer) GetImageType() ImageType {
	if x != nil {
		return x.ImageType
	}
	return ImageType_IMAGE_TYPE_CUSTOM
}

func (x *MLOpsContainer) GetContainerType() ContainerType {
	if x != nil {
		return x.ContainerType
	}
	return ContainerType_CONTAINER_TYPE_MAIN
}

func (x *MLOpsContainer) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *MLOpsContainer) GetResourceRule() string {
	if x != nil {
		return x.ResourceRule
	}
	return ""
}

func (x *MLOpsContainer) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *MLOpsContainer) GetCmds() []string {
	if x != nil {
		return x.Cmds
	}
	return nil
}

func (x *MLOpsContainer) GetMountCfg() []*MountCfg {
	if x != nil {
		return x.MountCfg
	}
	return nil
}

func (x *MLOpsContainer) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MLOpsContainer) GetUpdateTime() int32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *MLOpsContainer) GetReadinessProbe() *MLOpsProbe {
	if x != nil {
		return x.ReadinessProbe
	}
	return nil
}

func (x *MLOpsContainer) GetLivenessProbe() *MLOpsProbe {
	if x != nil {
		return x.LivenessProbe
	}
	return nil
}

func (x *MLOpsContainer) GetResourceId() int32 {
	if x != nil && x.ResourceId != nil {
		return *x.ResourceId
	}
	return 0
}

func (x *MLOpsContainer) GetResourceGroups() []*ResourceGroupInfo {
	if x != nil {
		return x.ResourceGroups
	}
	return nil
}

func (x *MLOpsContainer) GetGpuUtilizationPolicy() GPU_CORE_UTILIZATION_POLICY {
	if x != nil {
		return x.GpuUtilizationPolicy
	}
	return GPU_CORE_UTILIZATION_POLICY_GPU_CORE_UTILIZATION_POLICY_DEFAULT
}

type NodeChooseCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Strategy NodeChooseStrategy `protobuf:"varint,1,opt,name=strategy,proto3,enum=serving.NodeChooseStrategy" json:"strategy" description:"节点选择策略，1随机节点，0固定节点"`

	Nodes []*NodeSelector `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes" description:"固定节点的节点ID"`

	NodeIds []string `protobuf:"bytes,3,rep,name=node_ids,json=nodeIds,proto3" json:"node_ids" description:"固定节点的节点ID(string 数组格式)"`

	Enabled bool `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled" description:"是否开启限制调度范围"`
}

func (x *NodeChooseCfg) Reset() {
	*x = NodeChooseCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeChooseCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeChooseCfg) ProtoMessage() {}

func (x *NodeChooseCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeChooseCfg.ProtoReflect.Descriptor instead.
func (*NodeChooseCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{41}
}

func (x *NodeChooseCfg) GetStrategy() NodeChooseStrategy {
	if x != nil {
		return x.Strategy
	}
	return NodeChooseStrategy_NODE_CHOOSE_STRATEGY_FIX
}

func (x *NodeChooseCfg) GetNodes() []*NodeSelector {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *NodeChooseCfg) GetNodeIds() []string {
	if x != nil {
		return x.NodeIds
	}
	return nil
}

func (x *NodeChooseCfg) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type HPACfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Strategy HPAStrategy `protobuf:"varint,1,opt,name=strategy,proto3,enum=serving.HPAStrategy" json:"strategy" description:"横向扩容配置，0固定分片数量，1动态分片数量"`
	// @gotags:
	// description:"若为固定分片，需要指定分片数量，在动态策略下，此分片书不等于实际分片数"
	Replicas int32 `protobuf:"varint,2,opt,name=replicas,proto3" json:"replicas"`
	//	// @gotags:
	//	description:"当前实际运行的分片数量，若为固定分片数量，则与replicas一致，创建&更新服务版本的时候不需要传"
	//	int32 currReplicas = 3;
	//

	Hpa *HPADynamicReplicas `protobuf:"bytes,4,opt,name=hpa,proto3" json:"hpa" description:"动态分片配置"`
}

func (x *HPACfg) Reset() {
	*x = HPACfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HPACfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HPACfg) ProtoMessage() {}

func (x *HPACfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HPACfg.ProtoReflect.Descriptor instead.
func (*HPACfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{42}
}

func (x *HPACfg) GetStrategy() HPAStrategy {
	if x != nil {
		return x.Strategy
	}
	return HPAStrategy_HPA_STRATEGY_DISABLED
}

func (x *HPACfg) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *HPACfg) GetHpa() *HPADynamicReplicas {
	if x != nil {
		return x.Hpa
	}
	return nil
}

type HPADynamicReplicas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxReplicas int32 `protobuf:"varint,1,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas" description:"最大分片数"`

	MinReplicas int32 `protobuf:"varint,2,opt,name=min_replicas,json=minReplicas,proto3" json:"min_replicas" description:"最小分片数"`

	HpaMetricCfgs []*HpaMetricCfg `protobuf:"bytes,3,rep,name=hpaMetric_cfgs,json=hpaMetricCfgs,proto3" json:"hpaMetric_cfgs" description:"扩容条件"`
}

func (x *HPADynamicReplicas) Reset() {
	*x = HPADynamicReplicas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HPADynamicReplicas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HPADynamicReplicas) ProtoMessage() {}

func (x *HPADynamicReplicas) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HPADynamicReplicas.ProtoReflect.Descriptor instead.
func (*HPADynamicReplicas) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{43}
}

func (x *HPADynamicReplicas) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

func (x *HPADynamicReplicas) GetMinReplicas() int32 {
	if x != nil {
		return x.MinReplicas
	}
	return 0
}

func (x *HPADynamicReplicas) GetHpaMetricCfgs() []*HpaMetricCfg {
	if x != nil {
		return x.HpaMetricCfgs
	}
	return nil
}

type HpaMetricCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType HpaResourceType `protobuf:"varint,1,opt,name=resource_type,json=resourceType,proto3,enum=serving.HpaResourceType" json:"resource_type" description:"(已弃用)资源类型，0:cpu,1:memory, 仅指标来源为资源指标时生效"`

	TargetAverageUtilization int32 `protobuf:"varint,2,opt,name=target_average_utilization,json=targetAverageUtilization,proto3" json:"target_average_utilization" description:"(已弃用)横向扩容资源阈值, 请求资源的利用率，用于资源指标"`

	SourceType HpaMetricSourceType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=serving.HpaMetricSourceType" json:"source_type" description:"指标来源类型，0:资源指标(k8s内建的资源指标如cpu,memory)，1:pod指标(通过自定义指标暴露)"`

	MetricName string `protobuf:"bytes,4,opt,name=metric_name,json=metricName,proto3" json:"metric_name" description:"指标名称"`

	TargetAverageValue string `protobuf:"bytes,5,opt,name=target_average_value,json=targetAverageValue,proto3" json:"target_average_value" description:"横向扩容资源阈值, 资源指标时为1-100整数字符串，pod指标时可以为k8s quantity"`
}

func (x *HpaMetricCfg) Reset() {
	*x = HpaMetricCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HpaMetricCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HpaMetricCfg) ProtoMessage() {}

func (x *HpaMetricCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HpaMetricCfg.ProtoReflect.Descriptor instead.
func (*HpaMetricCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{44}
}

func (x *HpaMetricCfg) GetResourceType() HpaResourceType {
	if x != nil {
		return x.ResourceType
	}
	return HpaResourceType_HPA_RESOURCE_TYPE_CPU
}

func (x *HpaMetricCfg) GetTargetAverageUtilization() int32 {
	if x != nil {
		return x.TargetAverageUtilization
	}
	return 0
}

func (x *HpaMetricCfg) GetSourceType() HpaMetricSourceType {
	if x != nil {
		return x.SourceType
	}
	return HpaMetricSourceType_HPA_METRIC_SOURCE_TYPE_RESOURCE
}

func (x *HpaMetricCfg) GetMetricName() string {
	if x != nil {
		return x.MetricName
	}
	return ""
}

func (x *HpaMetricCfg) GetTargetAverageValue() string {
	if x != nil {
		return x.TargetAverageValue
	}
	return ""
}

// 服务列表页用到的结构
type ResourceSummaryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpuMRequest int32 `protobuf:"varint,1,opt,name=cpu_m_request,json=cpuMRequest,proto3" json:"cpu_m_request" description:"cpu request限制"`

	MemoryGiRequest float32 `protobuf:"fixed32,2,opt,name=memory_gi_request,json=memoryGiRequest,proto3" json:"memory_gi_request" description:"内存 request限制"`

	CpuMLimit int32 `protobuf:"varint,3,opt,name=cpu_m_limit,json=cpuMLimit,proto3" json:"cpu_m_limit" description:"cpu limit限制"`

	MemoryGiLimit float32 `protobuf:"fixed32,4,opt,name=memory_gi_limit,json=memoryGiLimit,proto3" json:"memory_gi_limit" description:"内存limit限制"`

	GpuCorePercent int32 `protobuf:"varint,5,opt,name=gpu_core_percent,json=gpuCorePercent,proto3" json:"gpu_core_percent" description:"算力"`

	GpuMemoryGi float32 `protobuf:"fixed32,6,opt,name=gpu_memory_gi,json=gpuMemoryGi,proto3" json:"gpu_memory_gi" description:"显存"`

	GpuCount int32 `protobuf:"varint,7,opt,name=gpu_count,json=gpuCount,proto3" json:"gpu_count" description:"gpu个数限制"`

	Gpus []string `protobuf:"bytes,8,rep,name=gpus,proto3" json:"gpus" description:"gpu id"`

	GpuType string `protobuf:"bytes,9,opt,name=gpu_type,json=gpuType,proto3" json:"gpu_type" description:"算力卡，可选ascend/nvidia"`
}

func (x *ResourceSummaryInfo) Reset() {
	*x = ResourceSummaryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceSummaryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceSummaryInfo) ProtoMessage() {}

func (x *ResourceSummaryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceSummaryInfo.ProtoReflect.Descriptor instead.
func (*ResourceSummaryInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{45}
}

func (x *ResourceSummaryInfo) GetCpuMRequest() int32 {
	if x != nil {
		return x.CpuMRequest
	}
	return 0
}

func (x *ResourceSummaryInfo) GetMemoryGiRequest() float32 {
	if x != nil {
		return x.MemoryGiRequest
	}
	return 0
}

func (x *ResourceSummaryInfo) GetCpuMLimit() int32 {
	if x != nil {
		return x.CpuMLimit
	}
	return 0
}

func (x *ResourceSummaryInfo) GetMemoryGiLimit() float32 {
	if x != nil {
		return x.MemoryGiLimit
	}
	return 0
}

func (x *ResourceSummaryInfo) GetGpuCorePercent() int32 {
	if x != nil {
		return x.GpuCorePercent
	}
	return 0
}

func (x *ResourceSummaryInfo) GetGpuMemoryGi() float32 {
	if x != nil {
		return x.GpuMemoryGi
	}
	return 0
}

func (x *ResourceSummaryInfo) GetGpuCount() int32 {
	if x != nil {
		return x.GpuCount
	}
	return 0
}

func (x *ResourceSummaryInfo) GetGpus() []string {
	if x != nil {
		return x.Gpus
	}
	return nil
}

func (x *ResourceSummaryInfo) GetGpuType() string {
	if x != nil {
		return x.GpuType
	}
	return ""
}

type Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CpuRequest string `protobuf:"bytes,1,opt,name=cpu_request,json=cpuRequest,proto3" json:"cpu_request" description:"cpu request限制"`

	MemoryRequest string `protobuf:"bytes,2,opt,name=memory_request,json=memoryRequest,proto3" json:"memory_request" description:"内存 request限制"`

	CpuLimit string `protobuf:"bytes,5,opt,name=cpu_limit,json=cpuLimit,proto3" json:"cpu_limit" description:"cpu limit限制"`

	MemoryLimit string `protobuf:"bytes,6,opt,name=memory_limit,json=memoryLimit,proto3" json:"memory_limit" description:"内存limit限制"`

	GpuCore string `protobuf:"bytes,3,opt,name=gpu_core,json=gpuCore,proto3" json:"gpu_core" description:"gpu算力"`

	GpuMemory string `protobuf:"bytes,4,opt,name=gpu_memory,json=gpuMemory,proto3" json:"gpu_memory" description:"gpu内存"`

	GpuCount string `protobuf:"bytes,7,opt,name=gpu_count,json=gpuCount,proto3" json:"gpu_count" description:"gpu个数限制"`

	Cpu string `protobuf:"bytes,8,opt,name=cpu,proto3" json:"cpu" description:"@deprecated cpu request限制(联调前的待删除参数)"`

	Memory string `protobuf:"bytes,9,opt,name=memory,proto3" json:"memory" description:"@deprecated 内存 request限制(联调前的待删除参数)"`

	GpuType string `protobuf:"bytes,10,opt,name=gpu_type,json=gpuType,proto3" json:"gpu_type" description:"算力卡，可选ascend/nvidia"`

	AscendConfig *AscendResourceCfg `protobuf:"bytes,11,opt,name=ascend_config,json=ascendConfig,proto3" json:"ascend_config" description:"算力卡，可选ascend/nvidia"`

	Annotations map[string]string `protobuf:"bytes,12,rep,name=annotations,proto3" json:"annotations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"运行资源的annotation, 可用于hami指定gpu型号等"`
}

func (x *Resource) Reset() {
	*x = Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{46}
}

func (x *Resource) GetCpuRequest() string {
	if x != nil {
		return x.CpuRequest
	}
	return ""
}

func (x *Resource) GetMemoryRequest() string {
	if x != nil {
		return x.MemoryRequest
	}
	return ""
}

func (x *Resource) GetCpuLimit() string {
	if x != nil {
		return x.CpuLimit
	}
	return ""
}

func (x *Resource) GetMemoryLimit() string {
	if x != nil {
		return x.MemoryLimit
	}
	return ""
}

func (x *Resource) GetGpuCore() string {
	if x != nil {
		return x.GpuCore
	}
	return ""
}

func (x *Resource) GetGpuMemory() string {
	if x != nil {
		return x.GpuMemory
	}
	return ""
}

func (x *Resource) GetGpuCount() string {
	if x != nil {
		return x.GpuCount
	}
	return ""
}

func (x *Resource) GetCpu() string {
	if x != nil {
		return x.Cpu
	}
	return ""
}

func (x *Resource) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *Resource) GetGpuType() string {
	if x != nil {
		return x.GpuType
	}
	return ""
}

func (x *Resource) GetAscendConfig() *AscendResourceCfg {
	if x != nil {
		return x.AscendConfig
	}
	return nil
}

func (x *Resource) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type AscendResourceCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NpuName string `protobuf:"bytes,1,opt,name=npu_name,json=npuName,proto3" json:"npu_name" description:"npu_name名字"`

	TemplateName string `protobuf:"bytes,2,opt,name=template_name,json=templateName,proto3" json:"template_name" description:"ascend模板名字"`

	Cnt int32 `protobuf:"varint,3,opt,name=cnt,proto3" json:"cnt" description:"数量，当template为exclusive的时候，需要填，默认1"`
}

func (x *AscendResourceCfg) Reset() {
	*x = AscendResourceCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AscendResourceCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AscendResourceCfg) ProtoMessage() {}

func (x *AscendResourceCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AscendResourceCfg.ProtoReflect.Descriptor instead.
func (*AscendResourceCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{47}
}

func (x *AscendResourceCfg) GetNpuName() string {
	if x != nil {
		return x.NpuName
	}
	return ""
}

func (x *AscendResourceCfg) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *AscendResourceCfg) GetCnt() int32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

type GetMLOpsContainerLogsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File []byte `protobuf:"bytes,1,opt,name=file,proto3" json:"file" description:"服务后台pod的日志信息"`
}

func (x *GetMLOpsContainerLogsRsp) Reset() {
	*x = GetMLOpsContainerLogsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMLOpsContainerLogsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMLOpsContainerLogsRsp) ProtoMessage() {}

func (x *GetMLOpsContainerLogsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMLOpsContainerLogsRsp.ProtoReflect.Descriptor instead.
func (*GetMLOpsContainerLogsRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetMLOpsContainerLogsRsp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type GetMLOpsContainerLogsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"容器的服务id"`

	PodName string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name" description:"容器的pod名字"`

	ContainerId string `protobuf:"bytes,3,opt,name=container_id,json=containerId,proto3" json:"container_id" description:"容器的集群id"`
}

func (x *GetMLOpsContainerLogsReq) Reset() {
	*x = GetMLOpsContainerLogsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMLOpsContainerLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMLOpsContainerLogsReq) ProtoMessage() {}

func (x *GetMLOpsContainerLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMLOpsContainerLogsReq.ProtoReflect.Descriptor instead.
func (*GetMLOpsContainerLogsReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetMLOpsContainerLogsReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *GetMLOpsContainerLogsReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *GetMLOpsContainerLogsReq) GetContainerId() string {
	if x != nil {
		return x.ContainerId
	}
	return ""
}

type GetMLOpsPodEventsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File []byte `protobuf:"bytes,1,opt,name=file,proto3" json:"file" description:"服务后台pod的日志信息（事件信息）"`

	Event []*common.PodEvent `protobuf:"bytes,2,rep,name=event,proto3" json:"event" description:"格式化的pod event"`
}

func (x *GetMLOpsPodEventsRsp) Reset() {
	*x = GetMLOpsPodEventsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMLOpsPodEventsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMLOpsPodEventsRsp) ProtoMessage() {}

func (x *GetMLOpsPodEventsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMLOpsPodEventsRsp.ProtoReflect.Descriptor instead.
func (*GetMLOpsPodEventsRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetMLOpsPodEventsRsp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *GetMLOpsPodEventsRsp) GetEvent() []*common.PodEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type GetMLOpsPodEventsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"容器的服务id"`

	PodName string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name" description:"容器的pod名字"`
}

func (x *GetMLOpsPodEventsReq) Reset() {
	*x = GetMLOpsPodEventsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMLOpsPodEventsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMLOpsPodEventsReq) ProtoMessage() {}

func (x *GetMLOpsPodEventsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMLOpsPodEventsReq.ProtoReflect.Descriptor instead.
func (*GetMLOpsPodEventsReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetMLOpsPodEventsReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *GetMLOpsPodEventsReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

type CallAPIReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"容器的服务id"`

	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port" description:"端口"`

	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url" description:"请求url"`

	PostBody string `protobuf:"bytes,4,opt,name=post_body,json=postBody,proto3" json:"post_body" description:"请求体"`

	EndpointType common.EndpointType `protobuf:"varint,5,opt,name=endpoint_type,json=endpointType,proto3,enum=commons.EndpointType" json:"endpoint_type" description:"协议，如https/http"`

	HttpMethod common.HttpMethod `protobuf:"varint,6,opt,name=http_method,json=httpMethod,proto3,enum=commons.HttpMethod" json:"http_method" description:"http方法，如GET/POST"`
}

func (x *CallAPIReq) Reset() {
	*x = CallAPIReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallAPIReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAPIReq) ProtoMessage() {}

func (x *CallAPIReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAPIReq.ProtoReflect.Descriptor instead.
func (*CallAPIReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{52}
}

func (x *CallAPIReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *CallAPIReq) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CallAPIReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CallAPIReq) GetPostBody() string {
	if x != nil {
		return x.PostBody
	}
	return ""
}

func (x *CallAPIReq) GetEndpointType() common.EndpointType {
	if x != nil {
		return x.EndpointType
	}
	return common.EndpointType(0)
}

func (x *CallAPIReq) GetHttpMethod() common.HttpMethod {
	if x != nil {
		return x.HttpMethod
	}
	return common.HttpMethod(0)
}

type CallAPIResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespBody string `protobuf:"bytes,1,opt,name=resp_body,json=respBody,proto3" json:"resp_body" description:"api返回"`
}

func (x *CallAPIResp) Reset() {
	*x = CallAPIResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallAPIResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAPIResp) ProtoMessage() {}

func (x *CallAPIResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAPIResp.ProtoReflect.Descriptor instead.
func (*CallAPIResp) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{53}
}

func (x *CallAPIResp) GetRespBody() string {
	if x != nil {
		return x.RespBody
	}
	return ""
}

type DistributedCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled" description:"开启分布式"`

	NumWorkers int32 `protobuf:"varint,2,opt,name=num_workers,json=numWorkers,proto3" json:"num_workers" description:"实例数量"`

	NodesExclusive bool `protobuf:"varint,3,opt,name=nodes_exclusive,json=nodesExclusive,proto3" json:"nodes_exclusive" description:"开启实例节点间互斥"`

	EnableRdmaHca bool `protobuf:"varint,4,opt,name=enable_rdma_hca,json=enableRdmaHca,proto3" json:"enable_rdma_hca" description:"开启高性能网络通信"`
}

func (x *DistributedCfg) Reset() {
	*x = DistributedCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributedCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributedCfg) ProtoMessage() {}

func (x *DistributedCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributedCfg.ProtoReflect.Descriptor instead.
func (*DistributedCfg) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{54}
}

func (x *DistributedCfg) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *DistributedCfg) GetNumWorkers() int32 {
	if x != nil {
		return x.NumWorkers
	}
	return 0
}

func (x *DistributedCfg) GetNodesExclusive() bool {
	if x != nil {
		return x.NodesExclusive
	}
	return false
}

func (x *DistributedCfg) GetEnableRdmaHca() bool {
	if x != nil {
		return x.EnableRdmaHca
	}
	return false
}

type YamlConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`

	Yaml string `protobuf:"bytes,2,opt,name=yaml,proto3" json:"yaml" description:"yaml"`
}

func (x *YamlConfig) Reset() {
	*x = YamlConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YamlConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YamlConfig) ProtoMessage() {}

func (x *YamlConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YamlConfig.ProtoReflect.Descriptor instead.
func (*YamlConfig) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{55}
}

func (x *YamlConfig) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *YamlConfig) GetYaml() string {
	if x != nil {
		return x.Yaml
	}
	return ""
}

type GetServiceInvokingTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvokingMethod InvokingMethod `protobuf:"varint,1,opt,name=invoking_method,json=invokingMethod,proto3,enum=serving.InvokingMethod" json:"invoking_method"`
	ServiceId      string         `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id"`

	PlatformHost  string `protobuf:"bytes,3,opt,name=platform_host,json=platformHost,proto3" json:"platform_host" description:"平台域名,外部调用时需要前端传递"`
	Api           string `protobuf:"bytes,4,opt,name=api,proto3" json:"api"`
	Port          uint32 `protobuf:"varint,5,opt,name=port,proto3" json:"port"`
	AnonymousCall bool   `protobuf:"varint,6,opt,name=anonymous_call,json=anonymousCall,proto3" json:"anonymous_call"`
}

func (x *GetServiceInvokingTemplateReq) Reset() {
	*x = GetServiceInvokingTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceInvokingTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInvokingTemplateReq) ProtoMessage() {}

func (x *GetServiceInvokingTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInvokingTemplateReq.ProtoReflect.Descriptor instead.
func (*GetServiceInvokingTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetServiceInvokingTemplateReq) GetInvokingMethod() InvokingMethod {
	if x != nil {
		return x.InvokingMethod
	}
	return InvokingMethod_INVOKING_METHOD_EXTERNAL
}

func (x *GetServiceInvokingTemplateReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *GetServiceInvokingTemplateReq) GetPlatformHost() string {
	if x != nil {
		return x.PlatformHost
	}
	return ""
}

func (x *GetServiceInvokingTemplateReq) GetApi() string {
	if x != nil {
		return x.Api
	}
	return ""
}

func (x *GetServiceInvokingTemplateReq) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *GetServiceInvokingTemplateReq) GetAnonymousCall() bool {
	if x != nil {
		return x.AnonymousCall
	}
	return false
}

type ServiceInvokingTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId     string            `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id"`
	Port          uint32            `protobuf:"varint,2,opt,name=port,proto3" json:"port"`
	Api           string            `protobuf:"bytes,3,opt,name=api,proto3" json:"api"`
	Method        common.HttpMethod `protobuf:"varint,5,opt,name=method,proto3,enum=commons.HttpMethod" json:"method"`
	CodeTemplates []*CodeTemplate   `protobuf:"bytes,6,rep,name=code_templates,json=codeTemplates,proto3" json:"code_templates"`
}

func (x *ServiceInvokingTemplate) Reset() {
	*x = ServiceInvokingTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInvokingTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInvokingTemplate) ProtoMessage() {}

func (x *ServiceInvokingTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInvokingTemplate.ProtoReflect.Descriptor instead.
func (*ServiceInvokingTemplate) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{57}
}

func (x *ServiceInvokingTemplate) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceInvokingTemplate) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServiceInvokingTemplate) GetApi() string {
	if x != nil {
		return x.Api
	}
	return ""
}

func (x *ServiceInvokingTemplate) GetMethod() common.HttpMethod {
	if x != nil {
		return x.Method
	}
	return common.HttpMethod(0)
}

func (x *ServiceInvokingTemplate) GetCodeTemplates() []*CodeTemplate {
	if x != nil {
		return x.CodeTemplates
	}
	return nil
}

type CodeTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language"`
	Template string `protobuf:"bytes,2,opt,name=template,proto3" json:"template"`
}

func (x *CodeTemplate) Reset() {
	*x = CodeTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeTemplate) ProtoMessage() {}

func (x *CodeTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeTemplate.ProtoReflect.Descriptor instead.
func (*CodeTemplate) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{58}
}

func (x *CodeTemplate) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CodeTemplate) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

// UnifyResource 统一资源配置,对应pkg.PodResourceDTO
type UnifyResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeChooseCfg  *NodeChooseCfg  `protobuf:"bytes,1,opt,name=node_choose_cfg,json=nodeChooseCfg,proto3" json:"node_choose_cfg"`
	ResourceRuleId int32           `protobuf:"varint,2,opt,name=resource_rule_id,json=resourceRuleId,proto3" json:"resource_rule_id"`
	ResourceRule   string          `protobuf:"bytes,3,opt,name=resource_rule,json=resourceRule,proto3" json:"resource_rule"`
	Resource       *Resource       `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource"`
	Arch           string          `protobuf:"bytes,5,opt,name=arch,proto3" json:"arch"`
	Distributed    *DistributedCfg `protobuf:"bytes,6,opt,name=distributed,proto3" json:"distributed"`
	IsAdvancedMode bool            `protobuf:"varint,7,opt,name=is_advanced_mode,json=isAdvancedMode,proto3" json:"is_advanced_mode"`
}

func (x *UnifyResource) Reset() {
	*x = UnifyResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_mlops_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnifyResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnifyResource) ProtoMessage() {}

func (x *UnifyResource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_mlops_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnifyResource.ProtoReflect.Descriptor instead.
func (*UnifyResource) Descriptor() ([]byte, []int) {
	return file_proto_serving_mlops_service_proto_rawDescGZIP(), []int{59}
}

func (x *UnifyResource) GetNodeChooseCfg() *NodeChooseCfg {
	if x != nil {
		return x.NodeChooseCfg
	}
	return nil
}

func (x *UnifyResource) GetResourceRuleId() int32 {
	if x != nil {
		return x.ResourceRuleId
	}
	return 0
}

func (x *UnifyResource) GetResourceRule() string {
	if x != nil {
		return x.ResourceRule
	}
	return ""
}

func (x *UnifyResource) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *UnifyResource) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *UnifyResource) GetDistributed() *DistributedCfg {
	if x != nil {
		return x.Distributed
	}
	return nil
}

func (x *UnifyResource) GetIsAdvancedMode() bool {
	if x != nil {
		return x.IsAdvancedMode
	}
	return false
}

var File_proto_serving_mlops_service_proto protoreflect.FileDescriptor

var file_proto_serving_mlops_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f,
	0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x1a, 0x1a, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa5, 0x01, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x54, 0x0a, 0x15, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x22, 0x4f, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x19,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x65,
	0x6e, 0x64, 0x22, 0x76, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x77, 0x0a, 0x17, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x22, 0xe8, 0x04, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x17, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x48, 0x03, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x58, 0x0a, 0x11, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x6d,
	0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x9b, 0x01,
	0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x22, 0x36, 0x0a, 0x09, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x22, 0x41, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6e, 0x65, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x22, 0x30, 0x0a, 0x0c, 0x41, 0x70, 0x69, 0x55, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x70, 0x69, 0x22, 0x2b, 0x0a, 0x0c, 0x41, 0x70, 0x69, 0x55,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x22, 0x47, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x5f, 0x73, 0x76, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x76, 0x63, 0x55, 0x72, 0x6c, 0x22, 0x1a,
	0x0a, 0x08, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x14, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x52, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6b, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0xf7, 0x02, 0x0a, 0x13, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63,
	0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70,
	0x73, 0x53, 0x76, 0x63, 0x48, 0x74, 0x74, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x68, 0x74, 0x74, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x16,
	0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65,
	0x6c, 0x64, 0x6f, 0x6e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe8, 0x01,
	0x0a, 0x14, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x48, 0x74, 0x74, 0x70, 0x43, 0x61,
	0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x0f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x55, 0x72, 0x6c,
	0x12, 0x29, 0x0a, 0x09, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50,
	0x49, 0x52, 0x08, 0x68, 0x74, 0x74, 0x70, 0x41, 0x70, 0x69, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xcb, 0x01, 0x0a, 0x1a, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x53, 0x76, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x6b, 0x38, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x38, 0x73, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x38, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6b, 0x38, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x33, 0x0a, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53,
	0x76, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x16, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x53, 0x76, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x22,
	0x42, 0x0a, 0x1c, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xe1, 0x01, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x34, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x44, 0x65, 0x73, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f,
	0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x70, 0x70,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x64, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x64, 0x65, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x1a, 0x38, 0x0a,
	0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x29, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x66, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x33, 0x0a, 0x0b, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x9c, 0x0c, 0x0a, 0x14, 0x4d, 0x4c, 0x4f, 0x70,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x36, 0x0a,
	0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x43, 0x66, 0x67, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43,
	0x66, 0x67, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x5f, 0x73, 0x76, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x76, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a,
	0x04, 0x61, 0x70, 0x69, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49, 0x52, 0x04, 0x61, 0x70, 0x69, 0x73, 0x12,
	0x2f, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x21, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53,
	0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x41, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x46,
	0x0a, 0x11, 0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x22, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x62,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x62, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52,
	0x6f, 0x6c, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x71, 0x70, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x51, 0x70, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x54,
	0x65, 0x78, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x64, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x6c,
	0x6c, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x50, 0x0a, 0x15,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x1a, 0x4e, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x5f, 0x63, 0x66, 0x67, 0x22, 0x58, 0x0a, 0x10, 0x47, 0x75, 0x61, 0x72, 0x64, 0x72,
	0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x69, 0x73, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x49, 0x64,
	0x22, 0x99, 0x01, 0x0a, 0x09, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x75, 0x6e, 0x69, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x22, 0xc4, 0x09, 0x0a,
	0x19, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0f,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x76, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x76,
	0x63, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x04, 0x61, 0x70, 0x69, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49,
	0x52, 0x04, 0x61, 0x70, 0x69, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x46, 0x0a, 0x11, 0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69, 0x6c, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x67, 0x75, 0x61, 0x72, 0x64, 0x72, 0x61, 0x69,
	0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x56, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x34, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x34, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
	0x6f, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x1a, 0x4e, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x6c, 0x0a, 0x0b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x22, 0x43, 0x0a, 0x11, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xbf, 0x02, 0x0a, 0x09, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x43, 0x66, 0x67, 0x12, 0x45, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x13, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x06, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x66, 0x67, 0x2e,
	0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6d, 0x70, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6d, 0x70, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x57, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x22, 0xf1, 0x04, 0x0a, 0x03, 0x41, 0x50, 0x49,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x0d, 0x75, 0x72,
	0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49, 0x2e,
	0x55, 0x72, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x75, 0x72, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4d, 0x61, 0x70, 0x12, 0x51, 0x0a,
	0x13, 0x75, 0x72, 0x6c, 0x5f, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49, 0x2e, 0x55, 0x72, 0x6c, 0x48, 0x74, 0x74, 0x70,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10,
	0x75, 0x72, 0x6c, 0x48, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x61, 0x70,
	0x12, 0x34, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49,
	0x2e, 0x41, 0x70, 0x69, 0x53, 0x70, 0x65, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x61,
	0x70, 0x69, 0x53, 0x70, 0x65, 0x63, 0x12, 0x34, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x66, 0x75,
	0x6e, 0x63, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x41, 0x50, 0x49, 0x2e, 0x41, 0x70, 0x69, 0x46, 0x75, 0x6e, 0x63, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x61, 0x70, 0x69, 0x46, 0x75, 0x6e, 0x63, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x72, 0x6c, 0x1a, 0x3e, 0x0a,
	0x10, 0x55, 0x72, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x43, 0x0a,
	0x15, 0x55, 0x72, 0x6c, 0x48, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x4c, 0x0a, 0x0c, 0x41, 0x70, 0x69, 0x53, 0x70, 0x65, 0x63, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x50,
	0x49, 0x53, 0x70, 0x65, 0x63, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3a, 0x0a, 0x0c, 0x41, 0x70, 0x69, 0x46, 0x75, 0x6e, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x72, 0x0a, 0x08,
	0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x66, 0x67, 0x12, 0x31, 0x0a, 0x0a, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67,
	0x52, 0x09, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x12, 0x33, 0x0a, 0x0b, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x73,
	0x22, 0xd9, 0x02, 0x0a, 0x09, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x12, 0x33,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x37, 0x0a, 0x18,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x67, 0x69, 0x62, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47, 0x69, 0x62, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x46, 0x0a, 0x09,
	0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x22, 0xee, 0x0a, 0x0a, 0x17, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x0f, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x63, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x43, 0x66, 0x67, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65,
	0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x43, 0x66, 0x67, 0x12, 0x28, 0x0a, 0x07, 0x68, 0x70, 0x61,
	0x5f, 0x63, 0x66, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x50, 0x41, 0x43, 0x66, 0x67, 0x52, 0x06, 0x68, 0x70, 0x61,
	0x43, 0x66, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x6e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x5f, 0x67, 0x70, 0x75, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x75, 0x6e, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x47, 0x70, 0x75, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x70, 0x75,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f,
	0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x70, 0x75, 0x12, 0x2b,
	0x0a, 0x07, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x50, 0x55, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x67, 0x70, 0x75, 0x43, 0x66, 0x67, 0x12, 0x53, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x34, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x54, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x09,
	0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x23, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x0c, 0x4e, 0x6f,
	0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x70, 0x75, 0x43, 0x61, 0x72, 0x64, 0x73, 0x22, 0x55, 0x0a, 0x09, 0x47,
	0x50, 0x55, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x70, 0x75, 0x5f,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x67, 0x70, 0x75, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x75, 0x6e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x67, 0x70, 0x75, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x75, 0x6e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x47,
	0x70, 0x75, 0x22, 0xef, 0x01, 0x0a, 0x12, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x48, 0x74, 0x74, 0x70,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x72, 0x69, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x72, 0x69, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x48, 0x74, 0x74, 0x70, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xa2, 0x03, 0x0a, 0x0a, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x12, 0x4c, 0x0a, 0x20, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x1d, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x43, 0x0a, 0x0f, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x48, 0x74, 0x74, 0x70, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x68, 0x74, 0x74, 0x70, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x23, 0x0a, 0x21, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0xc3, 0x06, 0x0a, 0x0e, 0x4d, 0x4c,
	0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6d, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6d, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x66, 0x67,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x66, 0x67, 0x52, 0x08, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x66, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x12, 0x3a, 0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12,
	0x24, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x5a, 0x0a, 0x16, 0x67, 0x70,
	0x75, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x50, 0x55, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x55, 0x54,
	0x49, 0x4c, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x52, 0x14, 0x67, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22,
	0xaa, 0x01, 0x0a, 0x0d, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x43, 0x66,
	0x67, 0x12, 0x37, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x85, 0x01, 0x0a,
	0x06, 0x48, 0x50, 0x41, 0x43, 0x66, 0x67, 0x12, 0x30, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x48, 0x50, 0x41, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x2d, 0x0a, 0x03, 0x68, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x50, 0x41,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x52,
	0x03, 0x68, 0x70, 0x61, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x48, 0x50, 0x41, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x78, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x68, 0x70, 0x61, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x63,
	0x66, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x48, 0x70, 0x61, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x66, 0x67,
	0x52, 0x0d, 0x68, 0x70, 0x61, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x66, 0x67, 0x73, 0x22,
	0x9d, 0x02, 0x0a, 0x0c, 0x48, 0x70, 0x61, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x66, 0x67,
	0x12, 0x3d, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x48, 0x70, 0x61, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3c, 0x0a, 0x1a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x70, 0x61,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xc7, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x70, 0x75, 0x5f, 0x6d,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x70, 0x75, 0x4d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x47, 0x69,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x70, 0x75, 0x5f, 0x6d,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x70,
	0x75, 0x4d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x5f, 0x67, 0x69, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x47, 0x69, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x28, 0x0a, 0x10, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x67, 0x70, 0x75, 0x43, 0x6f,
	0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x67, 0x70, 0x75,
	0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x47, 0x69, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x67, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x70,
	0x75, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x67, 0x70, 0x75, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x67, 0x70, 0x75, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf5, 0x03, 0x0a, 0x08, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x70, 0x75, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x70, 0x75,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x70, 0x75, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x67, 0x70, 0x75, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x70, 0x75,
	0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x70, 0x75,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x70, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x73,
	0x63, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x73, 0x63, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x66, 0x67, 0x52, 0x0c, 0x61,
	0x73, 0x63, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x65, 0x0a, 0x11, 0x41, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x43, 0x66, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x70, 0x75, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x70, 0x75, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6e, 0x74, 0x22, 0x2e, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d,
	0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x77, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d,
	0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x6f, 0x64,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a,
	0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x05,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x50, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x4c, 0x4f, 0x70,
	0x73, 0x50, 0x6f, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe0, 0x01, 0x0a, 0x0a, 0x43, 0x61, 0x6c, 0x6c,
	0x41, 0x50, 0x49, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x3a, 0x0a, 0x0d, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x73, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0a,
	0x68, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x2a, 0x0a, 0x0b, 0x43, 0x61,
	0x6c, 0x6c, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x73, 0x70, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x9c, 0x01, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x66, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x57, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x5f, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x64, 0x6d, 0x61, 0x5f, 0x68, 0x63, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x64,
	0x6d, 0x61, 0x48, 0x63, 0x61, 0x22, 0x3f, 0x0a, 0x0a, 0x59, 0x61, 0x6d, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x22, 0xf2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x76, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x70, 0x69,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x22, 0xc9, 0x01, 0x0a, 0x17,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70,
	0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12, 0x2b, 0x0a, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0x46, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22,
	0xc6, 0x02, 0x0a, 0x0d, 0x55, 0x6e, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x3e, 0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x68, 0x6f, 0x6f, 0x73, 0x65,
	0x5f, 0x63, 0x66, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x43,
	0x66, 0x67, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x43, 0x66,
	0x67, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61,
	0x72, 0x63, 0x68, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x43, 0x66,
	0x67, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x41, 0x64, 0x76, 0x61,
	0x6e, 0x63, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x2a, 0x92, 0x01, 0x0a, 0x0d, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x4c,
	0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x69, 0x74, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b,
	0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x10, 0x03, 0x2a, 0xbb, 0x01,
	0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x4c, 0x41, 0x42, 0x10, 0x03,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47,
	0x45, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x0b, 0x2a, 0x85, 0x01, 0x0a, 0x0d,
	0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x76, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x53, 0x56, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x4c,
	0x4f, 0x50, 0x53, 0x5f, 0x53, 0x56, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x56,
	0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4c, 0x4f,
	0x50, 0x53, 0x5f, 0x53, 0x56, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x4c, 0x4f, 0x50, 0x53,
	0x5f, 0x53, 0x56, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x2a, 0x75, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x5f,
	0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x44, 0x45,
	0x50, 0x4c, 0x4f, 0x59, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59,
	0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x47, 0x52, 0x41, 0x59, 0x5f, 0x44,
	0x45, 0x50, 0x4c, 0x4f, 0x59, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45, 0x50, 0x4c, 0x4f,
	0x59, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x53, 0x48, 0x41, 0x44, 0x4f,
	0x57, 0x5f, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x10, 0x02, 0x2a, 0xcc, 0x01, 0x0a, 0x0d, 0x4d,
	0x4c, 0x4f, 0x70, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19,
	0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x48, 0x4f, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a,
	0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b,
	0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a,
	0x16, 0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4c, 0x4f,
	0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x47, 0x5f, 0x4d, 0x41, 0x50, 0x10, 0x05, 0x2a, 0x78, 0x0a, 0x0f, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x56, 0x4f, 0x4c, 0x55, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54,
	0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x4f, 0x4c, 0x55, 0x4d,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x56, 0x43, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15,
	0x56, 0x4f, 0x4c, 0x55, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54,
	0x59, 0x5f, 0x44, 0x49, 0x52, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x4f, 0x4c, 0x55, 0x4d,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x4d, 0x41,
	0x50, 0x10, 0x03, 0x2a, 0x44, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x1a, 0x0a,
	0x16, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x49, 0x44, 0x45, 0x43, 0x41, 0x52, 0x10, 0x01, 0x2a, 0x3b, 0x0a, 0x09, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54,
	0x46, 0x4f, 0x52, 0x4d, 0x10, 0x01, 0x2a, 0x96, 0x01, 0x0a, 0x1b, 0x47, 0x50, 0x55, 0x5f, 0x43,
	0x4f, 0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x12, 0x27, 0x0a, 0x23, 0x47, 0x50, 0x55, 0x5f, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12,
	0x25, 0x0a, 0x21, 0x47, 0x50, 0x55, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c,
	0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x46,
	0x4f, 0x52, 0x43, 0x45, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x47, 0x50, 0x55, 0x5f, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a,
	0x53, 0x0a, 0x12, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48,
	0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x46, 0x49,
	0x58, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x4f, 0x4f,
	0x53, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x52, 0x41, 0x4e, 0x44,
	0x4f, 0x4d, 0x10, 0x01, 0x2a, 0x42, 0x0a, 0x0b, 0x48, 0x50, 0x41, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x12, 0x19, 0x0a, 0x15, 0x48, 0x50, 0x41, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x45, 0x47, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18,
	0x0a, 0x14, 0x48, 0x50, 0x41, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x45,
	0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x2a, 0x4a, 0x0a, 0x0f, 0x48, 0x70, 0x61, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x48,
	0x50, 0x41, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x50, 0x55, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x48, 0x50, 0x41, 0x5f, 0x52, 0x45,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x4f,
	0x52, 0x59, 0x10, 0x01, 0x2a, 0x5a, 0x0a, 0x13, 0x48, 0x70, 0x61, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x48,
	0x50, 0x41, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x00,
	0x12, 0x1e, 0x0a, 0x1a, 0x48, 0x50, 0x41, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x44, 0x10, 0x01,
	0x2a, 0x92, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x5f, 0x49, 0x53, 0x54, 0x49, 0x4f, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x57,
	0x41, 0x59, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x50, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x10,
	0x02, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x53,
	0x54, 0x49, 0x4f, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x57, 0x41, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x10, 0x01, 0x2a, 0x6f, 0x0a, 0x10, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x41, 0x53,
	0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x41, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x41, 0x53, 0x48,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x41, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x41, 0x53, 0x48, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x41, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x32, 0xf0, 0x0a, 0x0a, 0x13, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42,
	0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x44, 0x12, 0x4a, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x53,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x12, 0x49, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d,
	0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x12, 0x53,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x12, 0x52, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x37, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x1a, 0x12, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44,
	0x12, 0x3d, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x35, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49, 0x44, 0x12, 0x12, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44,
	0x1a, 0x14, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x06, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x12, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x44, 0x1a, 0x11, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x49, 0x44, 0x12, 0x31, 0x0a, 0x07, 0x4f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x1a, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x42, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44,
	0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x53, 0x76, 0x63, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5a,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f,
	0x67, 0x73, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x73, 0x70, 0x30, 0x01, 0x12, 0x49, 0x0a, 0x09, 0x47, 0x65,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x6f, 0x64, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x6f, 0x64, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x07, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x50, 0x49,
	0x12, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x50, 0x49, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x61, 0x6c, 0x6c, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x13, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x53,
	0x76, 0x63, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x59, 0x61, 0x6d, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x12, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12,
	0x35, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x76, 0x63, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x12, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x44, 0x1a, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x59, 0x61, 0x6d, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x41, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x41, 0x70, 0x69, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x15, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x69, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x69,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x65, 0x73, 0x42, 0x33, 0x5a, 0x31, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c,
	0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_serving_mlops_service_proto_rawDescOnce sync.Once
	file_proto_serving_mlops_service_proto_rawDescData = file_proto_serving_mlops_service_proto_rawDesc
)

func file_proto_serving_mlops_service_proto_rawDescGZIP() []byte {
	file_proto_serving_mlops_service_proto_rawDescOnce.Do(func() {
		file_proto_serving_mlops_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_serving_mlops_service_proto_rawDescData)
	})
	return file_proto_serving_mlops_service_proto_rawDescData
}

var file_proto_serving_mlops_service_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_proto_serving_mlops_service_proto_msgTypes = make([]protoimpl.MessageInfo, 78)
var file_proto_serving_mlops_service_proto_goTypes = []interface{}{
	(ApprovalState)(0),                    // 0: serving.ApprovalState
	(SourceType)(0),                       // 1: serving.SourceType
	(MLOpsSvcState)(0),                    // 2: serving.MLOpsSvcState
	(DeployStrategy)(0),                   // 3: serving.DeployStrategy
	(MLOpsFileType)(0),                    // 4: serving.MLOpsFileType
	(MLOpsVolumeType)(0),                  // 5: serving.MLOpsVolumeType
	(ContainerType)(0),                    // 6: serving.ContainerType
	(ImageType)(0),                        // 7: serving.ImageType
	(GPU_CORE_UTILIZATION_POLICY)(0),      // 8: serving.GPU_CORE_UTILIZATION_POLICY
	(NodeChooseStrategy)(0),               // 9: serving.NodeChooseStrategy
	(HPAStrategy)(0),                      // 10: serving.HPAStrategy
	(HpaResourceType)(0),                  // 11: serving.HpaResourceType
	(HpaMetricSourceType)(0),              // 12: serving.HpaMetricSourceType
	(InvokingMethod)(0),                   // 13: serving.InvokingMethod
	(DashboardTabType)(0),                 // 14: serving.DashboardTabType
	(*ServiceInfo)(nil),                   // 15: serving.ServiceInfo
	(*TimeRange)(nil),                     // 16: serving.TimeRange
	(*UpdateApprovalStateReq)(nil),        // 17: serving.UpdateApprovalStateReq
	(*UpdateApprovalStateResp)(nil),       // 18: serving.UpdateApprovalStateResp
	(*ListServiceReq)(nil),                // 19: serving.ListServiceReq
	(*ServiceBaseInfoList)(nil),           // 20: serving.ServiceBaseInfoList
	(*ServiceAndVersionID)(nil),           // 21: serving.ServiceAndVersionID
	(*ServiceID)(nil),                     // 22: serving.ServiceID
	(*UpdateServiceRes)(nil),              // 23: serving.UpdateServiceRes
	(*ApiUniqueReq)(nil),                  // 24: serving.ApiUniqueReq
	(*ApiUniqueRes)(nil),                  // 25: serving.ApiUniqueRes
	(*RemoteService)(nil),                 // 26: serving.RemoteService
	(*DeployID)(nil),                      // 27: serving.DeployID
	(*ServiceIDVersionInfo)(nil),          // 28: serving.ServiceIDVersionInfo
	(*UpdateServiceVersionInfoResp)(nil),  // 29: serving.UpdateServiceVersionInfoResp
	(*MLOpsSvcRuntimeInfo)(nil),           // 30: serving.MLOpsSvcRuntimeInfo
	(*MLOpsSvcHttpCallInfo)(nil),          // 31: serving.MLOpsSvcHttpCallInfo
	(*MLOpsSvcVersionRuntimeInfo)(nil),    // 32: serving.MLOpsSvcVersionRuntimeInfo
	(*MLOpsSvcVersionPodInfo)(nil),        // 33: serving.MLOpsSvcVersionPodInfo
	(*MLOpsSvcVersionContainerInfo)(nil),  // 34: serving.MLOpsSvcVersionContainerInfo
	(*SourceMeta)(nil),                    // 35: serving.SourceMeta
	(*CustomDeployCfg)(nil),               // 36: serving.CustomDeployCfg
	(*RoutingPath)(nil),                   // 37: serving.RoutingPath
	(*MLOpsServiceBaseInfo)(nil),          // 38: serving.MLOpsServiceBaseInfo
	(*GuardrailsConfig)(nil),              // 39: serving.GuardrailsConfig
	(*RateLimit)(nil),                     // 40: serving.RateLimit
	(*MLOpsRemoteServiceInfoReq)(nil),     // 41: serving.MLOpsRemoteServiceInfoReq
	(*ShareConfig)(nil),                   // 42: serving.ShareConfig
	(*MLOpsSvcStateInfo)(nil),             // 43: serving.MLOpsSvcStateInfo
	(*DeployCfg)(nil),                     // 44: serving.DeployCfg
	(*API)(nil),                           // 45: serving.API
	(*MountCfg)(nil),                      // 46: serving.MountCfg
	(*VolumeCfg)(nil),                     // 47: serving.VolumeCfg
	(*MountPath)(nil),                     // 48: serving.MountPath
	(*MLOpsServiceVersionInfo)(nil),       // 49: serving.MLOpsServiceVersionInfo
	(*ResourceGroupInfo)(nil),             // 50: serving.ResourceGroupInfo
	(*NodeSelector)(nil),                  // 51: serving.NodeSelector
	(*GPUConfig)(nil),                     // 52: serving.GPUConfig
	(*MLOpsHttpGetAction)(nil),            // 53: serving.MLOpsHttpGetAction
	(*MLOpsProbe)(nil),                    // 54: serving.MLOpsProbe
	(*MLOpsContainer)(nil),                // 55: serving.MLOpsContainer
	(*NodeChooseCfg)(nil),                 // 56: serving.NodeChooseCfg
	(*HPACfg)(nil),                        // 57: serving.HPACfg
	(*HPADynamicReplicas)(nil),            // 58: serving.HPADynamicReplicas
	(*HpaMetricCfg)(nil),                  // 59: serving.HpaMetricCfg
	(*ResourceSummaryInfo)(nil),           // 60: serving.ResourceSummaryInfo
	(*Resource)(nil),                      // 61: serving.Resource
	(*AscendResourceCfg)(nil),             // 62: serving.AscendResourceCfg
	(*GetMLOpsContainerLogsRsp)(nil),      // 63: serving.GetMLOpsContainerLogsRsp
	(*GetMLOpsContainerLogsReq)(nil),      // 64: serving.GetMLOpsContainerLogsReq
	(*GetMLOpsPodEventsRsp)(nil),          // 65: serving.GetMLOpsPodEventsRsp
	(*GetMLOpsPodEventsReq)(nil),          // 66: serving.GetMLOpsPodEventsReq
	(*CallAPIReq)(nil),                    // 67: serving.CallAPIReq
	(*CallAPIResp)(nil),                   // 68: serving.CallAPIResp
	(*DistributedCfg)(nil),                // 69: serving.DistributedCfg
	(*YamlConfig)(nil),                    // 70: serving.YamlConfig
	(*GetServiceInvokingTemplateReq)(nil), // 71: serving.GetServiceInvokingTemplateReq
	(*ServiceInvokingTemplate)(nil),       // 72: serving.ServiceInvokingTemplate
	(*CodeTemplate)(nil),                  // 73: serving.CodeTemplate
	(*UnifyResource)(nil),                 // 74: serving.UnifyResource
	nil,                                   // 75: serving.ListServiceReq.SourceMetaExtraEntry
	nil,                                   // 76: serving.SourceMeta.ExtraEntry
	nil,                                   // 77: serving.MLOpsServiceBaseInfo.LabelsEntry
	nil,                                   // 78: serving.MLOpsRemoteServiceInfoReq.LabelsEntry
	nil,                                   // 79: serving.MLOpsRemoteServiceInfoReq.HeadersEntry
	nil,                                   // 80: serving.MLOpsRemoteServiceInfoReq.QueryParamsEntry
	nil,                                   // 81: serving.DeployCfg.WidgetEntry
	nil,                                   // 82: serving.API.UrlParamMapEntry
	nil,                                   // 83: serving.API.UrlHttpMethodMapEntry
	nil,                                   // 84: serving.API.ApiSpecEntry
	nil,                                   // 85: serving.API.ApiFuncEntry
	nil,                                   // 86: serving.VolumeCfg.ExtraEntry
	nil,                                   // 87: serving.MLOpsServiceVersionInfo.AnnotationsEntry
	nil,                                   // 88: serving.MLOpsServiceVersionInfo.HeadersEntry
	nil,                                   // 89: serving.MLOpsServiceVersionInfo.QueryParamsEntry
	nil,                                   // 90: serving.MLOpsHttpGetAction.HeadersEntry
	nil,                                   // 91: serving.MLOpsContainer.EnvsEntry
	nil,                                   // 92: serving.Resource.AnnotationsEntry
	(*common.Endpoint)(nil),               // 93: commons.Endpoint
	(*common.BillingConfig)(nil),          // 94: commons.BillingConfig
	(*common.ModelInferSpec)(nil),         // 95: commons.ModelInferSpec
	(*common.PodEvent)(nil),               // 96: common.PodEvent
	(common.EndpointType)(0),              // 97: commons.EndpointType
	(common.HttpMethod)(0),                // 98: commons.HttpMethod
	(*common.StringList)(nil),             // 99: commons.StringList
	(common.APISpec)(0),                   // 100: commons.APISpec
	(*common.NameUniqueReq)(nil),          // 101: commons.NameUniqueReq
	(*common.NameUniqueRes)(nil),          // 102: commons.NameUniqueRes
}
var file_proto_serving_mlops_service_proto_depIdxs = []int32{
	38,  // 0: serving.ServiceInfo.service_info:type_name -> serving.MLOpsServiceBaseInfo
	49,  // 1: serving.ServiceInfo.service_version_infos:type_name -> serving.MLOpsServiceVersionInfo
	0,   // 2: serving.UpdateApprovalStateReq.approval_state:type_name -> serving.ApprovalState
	0,   // 3: serving.UpdateApprovalStateResp.approval_state:type_name -> serving.ApprovalState
	1,   // 4: serving.ListServiceReq.source_types:type_name -> serving.SourceType
	16,  // 5: serving.ListServiceReq.create_time_range:type_name -> serving.TimeRange
	75,  // 6: serving.ListServiceReq.source_meta_extra:type_name -> serving.ListServiceReq.SourceMetaExtraEntry
	38,  // 7: serving.ServiceBaseInfoList.service_infos:type_name -> serving.MLOpsServiceBaseInfo
	49,  // 8: serving.ServiceIDVersionInfo.service_version_info:type_name -> serving.MLOpsServiceVersionInfo
	2,   // 9: serving.MLOpsSvcRuntimeInfo.state:type_name -> serving.MLOpsSvcState
	31,  // 10: serving.MLOpsSvcRuntimeInfo.http_call_info:type_name -> serving.MLOpsSvcHttpCallInfo
	32,  // 11: serving.MLOpsSvcRuntimeInfo.service_versions:type_name -> serving.MLOpsSvcVersionRuntimeInfo
	43,  // 12: serving.MLOpsSvcRuntimeInfo.state_info:type_name -> serving.MLOpsSvcStateInfo
	45,  // 13: serving.MLOpsSvcHttpCallInfo.http_apis:type_name -> serving.API
	93,  // 14: serving.MLOpsSvcHttpCallInfo.endpoints:type_name -> commons.Endpoint
	33,  // 15: serving.MLOpsSvcVersionRuntimeInfo.pods:type_name -> serving.MLOpsSvcVersionPodInfo
	34,  // 16: serving.MLOpsSvcVersionPodInfo.containers:type_name -> serving.MLOpsSvcVersionContainerInfo
	76,  // 17: serving.SourceMeta.extra:type_name -> serving.SourceMeta.ExtraEntry
	1,   // 18: serving.MLOpsServiceBaseInfo.source_type:type_name -> serving.SourceType
	44,  // 19: serving.MLOpsServiceBaseInfo.deploy_cfg:type_name -> serving.DeployCfg
	45,  // 20: serving.MLOpsServiceBaseInfo.apis:type_name -> serving.API
	93,  // 21: serving.MLOpsServiceBaseInfo.endpoints:type_name -> commons.Endpoint
	2,   // 22: serving.MLOpsServiceBaseInfo.state:type_name -> serving.MLOpsSvcState
	35,  // 23: serving.MLOpsServiceBaseInfo.source_meta:type_name -> serving.SourceMeta
	77,  // 24: serving.MLOpsServiceBaseInfo.labels:type_name -> serving.MLOpsServiceBaseInfo.LabelsEntry
	42,  // 25: serving.MLOpsServiceBaseInfo.share_config:type_name -> serving.ShareConfig
	39,  // 26: serving.MLOpsServiceBaseInfo.guardrails_config:type_name -> serving.GuardrailsConfig
	94,  // 27: serving.MLOpsServiceBaseInfo.billing_config:type_name -> commons.BillingConfig
	43,  // 28: serving.MLOpsServiceBaseInfo.state_info:type_name -> serving.MLOpsSvcStateInfo
	60,  // 29: serving.MLOpsServiceBaseInfo.resource_summary_info:type_name -> serving.ResourceSummaryInfo
	95,  // 30: serving.MLOpsServiceBaseInfo.model_infer_spec:type_name -> commons.ModelInferSpec
	45,  // 31: serving.MLOpsRemoteServiceInfoReq.apis:type_name -> serving.API
	93,  // 32: serving.MLOpsRemoteServiceInfoReq.endpoints:type_name -> commons.Endpoint
	78,  // 33: serving.MLOpsRemoteServiceInfoReq.labels:type_name -> serving.MLOpsRemoteServiceInfoReq.LabelsEntry
	40,  // 34: serving.MLOpsRemoteServiceInfoReq.rate_limit:type_name -> serving.RateLimit
	40,  // 35: serving.MLOpsRemoteServiceInfoReq.user_rate_limit:type_name -> serving.RateLimit
	39,  // 36: serving.MLOpsRemoteServiceInfoReq.guardrails_config:type_name -> serving.GuardrailsConfig
	94,  // 37: serving.MLOpsRemoteServiceInfoReq.billing_config:type_name -> commons.BillingConfig
	79,  // 38: serving.MLOpsRemoteServiceInfoReq.headers:type_name -> serving.MLOpsRemoteServiceInfoReq.HeadersEntry
	80,  // 39: serving.MLOpsRemoteServiceInfoReq.query_params:type_name -> serving.MLOpsRemoteServiceInfoReq.QueryParamsEntry
	35,  // 40: serving.MLOpsRemoteServiceInfoReq.source_meta:type_name -> serving.SourceMeta
	1,   // 41: serving.MLOpsRemoteServiceInfoReq.source_type:type_name -> serving.SourceType
	3,   // 42: serving.DeployCfg.deploy_strategy:type_name -> serving.DeployStrategy
	81,  // 43: serving.DeployCfg.widget:type_name -> serving.DeployCfg.WidgetEntry
	82,  // 44: serving.API.url_param_map:type_name -> serving.API.UrlParamMapEntry
	83,  // 45: serving.API.url_http_method_map:type_name -> serving.API.UrlHttpMethodMapEntry
	84,  // 46: serving.API.api_spec:type_name -> serving.API.ApiSpecEntry
	85,  // 47: serving.API.api_func:type_name -> serving.API.ApiFuncEntry
	47,  // 48: serving.MountCfg.volume_cfg:type_name -> serving.VolumeCfg
	48,  // 49: serving.MountCfg.mount_paths:type_name -> serving.MountPath
	4,   // 50: serving.VolumeCfg.file_type:type_name -> serving.MLOpsFileType
	86,  // 51: serving.VolumeCfg.extra:type_name -> serving.VolumeCfg.ExtraEntry
	56,  // 52: serving.MLOpsServiceVersionInfo.node_choose_cfg:type_name -> serving.NodeChooseCfg
	57,  // 53: serving.MLOpsServiceVersionInfo.hpa_cfg:type_name -> serving.HPACfg
	55,  // 54: serving.MLOpsServiceVersionInfo.containers:type_name -> serving.MLOpsContainer
	52,  // 55: serving.MLOpsServiceVersionInfo.gpu_cfg:type_name -> serving.GPUConfig
	87,  // 56: serving.MLOpsServiceVersionInfo.annotations:type_name -> serving.MLOpsServiceVersionInfo.AnnotationsEntry
	35,  // 57: serving.MLOpsServiceVersionInfo.source_meta:type_name -> serving.SourceMeta
	88,  // 58: serving.MLOpsServiceVersionInfo.headers:type_name -> serving.MLOpsServiceVersionInfo.HeadersEntry
	89,  // 59: serving.MLOpsServiceVersionInfo.query_params:type_name -> serving.MLOpsServiceVersionInfo.QueryParamsEntry
	40,  // 60: serving.MLOpsServiceVersionInfo.rate_limit:type_name -> serving.RateLimit
	40,  // 61: serving.MLOpsServiceVersionInfo.user_rate_limit:type_name -> serving.RateLimit
	90,  // 62: serving.MLOpsHttpGetAction.headers:type_name -> serving.MLOpsHttpGetAction.HeadersEntry
	53,  // 63: serving.MLOpsProbe.http_get_action:type_name -> serving.MLOpsHttpGetAction
	7,   // 64: serving.MLOpsContainer.image_type:type_name -> serving.ImageType
	6,   // 65: serving.MLOpsContainer.container_type:type_name -> serving.ContainerType
	61,  // 66: serving.MLOpsContainer.resource:type_name -> serving.Resource
	91,  // 67: serving.MLOpsContainer.envs:type_name -> serving.MLOpsContainer.EnvsEntry
	46,  // 68: serving.MLOpsContainer.mount_cfg:type_name -> serving.MountCfg
	54,  // 69: serving.MLOpsContainer.readiness_probe:type_name -> serving.MLOpsProbe
	54,  // 70: serving.MLOpsContainer.liveness_probe:type_name -> serving.MLOpsProbe
	50,  // 71: serving.MLOpsContainer.resource_groups:type_name -> serving.ResourceGroupInfo
	8,   // 72: serving.MLOpsContainer.gpu_utilization_policy:type_name -> serving.GPU_CORE_UTILIZATION_POLICY
	9,   // 73: serving.NodeChooseCfg.strategy:type_name -> serving.NodeChooseStrategy
	51,  // 74: serving.NodeChooseCfg.nodes:type_name -> serving.NodeSelector
	10,  // 75: serving.HPACfg.strategy:type_name -> serving.HPAStrategy
	58,  // 76: serving.HPACfg.hpa:type_name -> serving.HPADynamicReplicas
	59,  // 77: serving.HPADynamicReplicas.hpaMetric_cfgs:type_name -> serving.HpaMetricCfg
	11,  // 78: serving.HpaMetricCfg.resource_type:type_name -> serving.HpaResourceType
	12,  // 79: serving.HpaMetricCfg.source_type:type_name -> serving.HpaMetricSourceType
	62,  // 80: serving.Resource.ascend_config:type_name -> serving.AscendResourceCfg
	92,  // 81: serving.Resource.annotations:type_name -> serving.Resource.AnnotationsEntry
	96,  // 82: serving.GetMLOpsPodEventsRsp.event:type_name -> common.PodEvent
	97,  // 83: serving.CallAPIReq.endpoint_type:type_name -> commons.EndpointType
	98,  // 84: serving.CallAPIReq.http_method:type_name -> commons.HttpMethod
	13,  // 85: serving.GetServiceInvokingTemplateReq.invoking_method:type_name -> serving.InvokingMethod
	98,  // 86: serving.ServiceInvokingTemplate.method:type_name -> commons.HttpMethod
	73,  // 87: serving.ServiceInvokingTemplate.code_templates:type_name -> serving.CodeTemplate
	56,  // 88: serving.UnifyResource.node_choose_cfg:type_name -> serving.NodeChooseCfg
	61,  // 89: serving.UnifyResource.resource:type_name -> serving.Resource
	69,  // 90: serving.UnifyResource.distributed:type_name -> serving.DistributedCfg
	99,  // 91: serving.MLOpsServiceBaseInfo.LabelsEntry.value:type_name -> commons.StringList
	99,  // 92: serving.MLOpsRemoteServiceInfoReq.LabelsEntry.value:type_name -> commons.StringList
	100, // 93: serving.API.ApiSpecEntry.value:type_name -> commons.APISpec
	38,  // 94: serving.MLOpsServiceService.CreateService:input_type -> serving.MLOpsServiceBaseInfo
	41,  // 95: serving.MLOpsServiceService.CreateRemote:input_type -> serving.MLOpsRemoteServiceInfoReq
	28,  // 96: serving.MLOpsServiceService.CreateServiceVersion:input_type -> serving.ServiceIDVersionInfo
	38,  // 97: serving.MLOpsServiceService.UpdateService:input_type -> serving.MLOpsServiceBaseInfo
	28,  // 98: serving.MLOpsServiceService.UpdateServiceVersion:input_type -> serving.ServiceIDVersionInfo
	21,  // 99: serving.MLOpsServiceService.DeleteServiceVersion:input_type -> serving.ServiceAndVersionID
	22,  // 100: serving.MLOpsServiceService.DeleteService:input_type -> serving.ServiceID
	19,  // 101: serving.MLOpsServiceService.List:input_type -> serving.ListServiceReq
	22,  // 102: serving.MLOpsServiceService.QueryByID:input_type -> serving.ServiceID
	22,  // 103: serving.MLOpsServiceService.Deploy:input_type -> serving.ServiceID
	22,  // 104: serving.MLOpsServiceService.Offline:input_type -> serving.ServiceID
	22,  // 105: serving.MLOpsServiceService.GetRuntimeInfo:input_type -> serving.ServiceID
	64,  // 106: serving.MLOpsServiceService.GetContainerLogs:input_type -> serving.GetMLOpsContainerLogsReq
	66,  // 107: serving.MLOpsServiceService.GetEvents:input_type -> serving.GetMLOpsPodEventsReq
	67,  // 108: serving.MLOpsServiceService.CallAPI:input_type -> serving.CallAPIReq
	17,  // 109: serving.MLOpsServiceService.UpdateApprovalState:input_type -> serving.UpdateApprovalStateReq
	70,  // 110: serving.MLOpsServiceService.UpsertSvcYaml:input_type -> serving.YamlConfig
	22,  // 111: serving.MLOpsServiceService.GetSvcYaml:input_type -> serving.ServiceID
	101, // 112: serving.MLOpsServiceService.CheckNameUnique:input_type -> commons.NameUniqueReq
	24,  // 113: serving.MLOpsServiceService.CheckApiUnique:input_type -> serving.ApiUniqueReq
	22,  // 114: serving.MLOpsServiceService.CreateService:output_type -> serving.ServiceID
	26,  // 115: serving.MLOpsServiceService.CreateRemote:output_type -> serving.RemoteService
	21,  // 116: serving.MLOpsServiceService.CreateServiceVersion:output_type -> serving.ServiceAndVersionID
	23,  // 117: serving.MLOpsServiceService.UpdateService:output_type -> serving.UpdateServiceRes
	21,  // 118: serving.MLOpsServiceService.UpdateServiceVersion:output_type -> serving.ServiceAndVersionID
	21,  // 119: serving.MLOpsServiceService.DeleteServiceVersion:output_type -> serving.ServiceAndVersionID
	22,  // 120: serving.MLOpsServiceService.DeleteService:output_type -> serving.ServiceID
	20,  // 121: serving.MLOpsServiceService.List:output_type -> serving.ServiceBaseInfoList
	15,  // 122: serving.MLOpsServiceService.QueryByID:output_type -> serving.ServiceInfo
	27,  // 123: serving.MLOpsServiceService.Deploy:output_type -> serving.DeployID
	22,  // 124: serving.MLOpsServiceService.Offline:output_type -> serving.ServiceID
	30,  // 125: serving.MLOpsServiceService.GetRuntimeInfo:output_type -> serving.MLOpsSvcRuntimeInfo
	63,  // 126: serving.MLOpsServiceService.GetContainerLogs:output_type -> serving.GetMLOpsContainerLogsRsp
	65,  // 127: serving.MLOpsServiceService.GetEvents:output_type -> serving.GetMLOpsPodEventsRsp
	68,  // 128: serving.MLOpsServiceService.CallAPI:output_type -> serving.CallAPIResp
	18,  // 129: serving.MLOpsServiceService.UpdateApprovalState:output_type -> serving.UpdateApprovalStateResp
	22,  // 130: serving.MLOpsServiceService.UpsertSvcYaml:output_type -> serving.ServiceID
	70,  // 131: serving.MLOpsServiceService.GetSvcYaml:output_type -> serving.YamlConfig
	102, // 132: serving.MLOpsServiceService.CheckNameUnique:output_type -> commons.NameUniqueRes
	25,  // 133: serving.MLOpsServiceService.CheckApiUnique:output_type -> serving.ApiUniqueRes
	114, // [114:134] is the sub-list for method output_type
	94,  // [94:114] is the sub-list for method input_type
	94,  // [94:94] is the sub-list for extension type_name
	94,  // [94:94] is the sub-list for extension extendee
	0,   // [0:94] is the sub-list for field type_name
}

func init() { file_proto_serving_mlops_service_proto_init() }
func file_proto_serving_mlops_service_proto_init() {
	if File_proto_serving_mlops_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_serving_mlops_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApprovalStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApprovalStateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceBaseInfoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceAndVersionID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiUniqueReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiUniqueRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceIDVersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceVersionInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcRuntimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcHttpCallInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcVersionRuntimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcVersionPodInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcVersionContainerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomDeployCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoutingPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsServiceBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuardrailsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsRemoteServiceInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsSvcStateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*API); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsServiceVersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPUConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsHttpGetAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsProbe); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsContainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeChooseCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HPACfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HPADynamicReplicas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HpaMetricCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceSummaryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AscendResourceCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMLOpsContainerLogsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMLOpsContainerLogsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMLOpsPodEventsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMLOpsPodEventsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallAPIReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallAPIResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DistributedCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YamlConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceInvokingTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInvokingTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_mlops_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnifyResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_serving_mlops_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_proto_serving_mlops_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_proto_serving_mlops_service_proto_msgTypes[23].OneofWrappers = []interface{}{}
	file_proto_serving_mlops_service_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_proto_serving_mlops_service_proto_msgTypes[39].OneofWrappers = []interface{}{}
	file_proto_serving_mlops_service_proto_msgTypes[40].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_serving_mlops_service_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   78,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_serving_mlops_service_proto_goTypes,
		DependencyIndexes: file_proto_serving_mlops_service_proto_depIdxs,
		EnumInfos:         file_proto_serving_mlops_service_proto_enumTypes,
		MessageInfos:      file_proto_serving_mlops_service_proto_msgTypes,
	}.Build()
	File_proto_serving_mlops_service_proto = out.File
	file_proto_serving_mlops_service_proto_rawDesc = nil
	file_proto_serving_mlops_service_proto_goTypes = nil
	file_proto_serving_mlops_service_proto_depIdxs = nil
}
