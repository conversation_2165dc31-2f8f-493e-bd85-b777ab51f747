// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/serving/config.proto

package serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MlopsExternalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalConfig map[string]string `protobuf:"bytes,1,rep,name=external_config,json=externalConfig,proto3" json:"external_config" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MlopsExternalConfig) Reset() {
	*x = MlopsExternalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MlopsExternalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MlopsExternalConfig) ProtoMessage() {}

func (x *MlopsExternalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MlopsExternalConfig.ProtoReflect.Descriptor instead.
func (*MlopsExternalConfig) Descriptor() ([]byte, []int) {
	return file_proto_serving_config_proto_rawDescGZIP(), []int{0}
}

func (x *MlopsExternalConfig) GetExternalConfig() map[string]string {
	if x != nil {
		return x.ExternalConfig
	}
	return nil
}

var File_proto_serving_config_proto protoreflect.FileDescriptor

var file_proto_serving_config_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb3, 0x01, 0x0a, 0x13, 0x4d, 0x6c, 0x6f, 0x70, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59, 0x0a, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6c, 0x6f,
	0x70, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x1a, 0x41, 0x0a, 0x13, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x57, 0x0a, 0x12, 0x4d, 0x4c, 0x4f, 0x70, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41, 0x0a,
	0x0e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x73, 0x70, 0x1a, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6c, 0x6f,
	0x70, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x42, 0x33, 0x5a, 0x31, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f,
	0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x3b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_serving_config_proto_rawDescOnce sync.Once
	file_proto_serving_config_proto_rawDescData = file_proto_serving_config_proto_rawDesc
)

func file_proto_serving_config_proto_rawDescGZIP() []byte {
	file_proto_serving_config_proto_rawDescOnce.Do(func() {
		file_proto_serving_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_serving_config_proto_rawDescData)
	})
	return file_proto_serving_config_proto_rawDescData
}

var file_proto_serving_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_serving_config_proto_goTypes = []interface{}{
	(*MlopsExternalConfig)(nil), // 0: serving.MlopsExternalConfig
	nil,                         // 1: serving.MlopsExternalConfig.ExternalConfigEntry
	(*common.EmptyRsp)(nil),     // 2: commons.EmptyRsp
}
var file_proto_serving_config_proto_depIdxs = []int32{
	1, // 0: serving.MlopsExternalConfig.external_config:type_name -> serving.MlopsExternalConfig.ExternalConfigEntry
	2, // 1: serving.MLOpsConfigService.ExternalConfig:input_type -> commons.EmptyRsp
	0, // 2: serving.MLOpsConfigService.ExternalConfig:output_type -> serving.MlopsExternalConfig
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_serving_config_proto_init() }
func file_proto_serving_config_proto_init() {
	if File_proto_serving_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_serving_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MlopsExternalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_serving_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_serving_config_proto_goTypes,
		DependencyIndexes: file_proto_serving_config_proto_depIdxs,
		MessageInfos:      file_proto_serving_config_proto_msgTypes,
	}.Build()
	File_proto_serving_config_proto = out.File
	file_proto_serving_config_proto_rawDesc = nil
	file_proto_serving_config_proto_goTypes = nil
	file_proto_serving_config_proto_depIdxs = nil
}
