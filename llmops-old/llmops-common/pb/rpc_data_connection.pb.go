// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_data_connection.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListConnectionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext  *UserContext             `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	ListSelector *ListConnectionsSelector `protobuf:"bytes,2,opt,name=list_selector,json=listSelector,proto3" json:"list_selector,omitempty"`
}

func (x *ListConnectionsReq) Reset() {
	*x = ListConnectionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConnectionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConnectionsReq) ProtoMessage() {}

func (x *ListConnectionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConnectionsReq.ProtoReflect.Descriptor instead.
func (*ListConnectionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{0}
}

func (x *ListConnectionsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListConnectionsReq) GetListSelector() *ListConnectionsSelector {
	if x != nil {
		return x.ListSelector
	}
	return nil
}

type ListConnectionsSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   []ConnectionType   `protobuf:"varint,1,rep,packed,name=type,proto3,enum=proto.ConnectionType" json:"type,omitempty"`
	Status []ConnectionStatus `protobuf:"varint,2,rep,packed,name=status,proto3,enum=proto.ConnectionStatus" json:"status,omitempty"`
}

func (x *ListConnectionsSelector) Reset() {
	*x = ListConnectionsSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConnectionsSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConnectionsSelector) ProtoMessage() {}

func (x *ListConnectionsSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConnectionsSelector.ProtoReflect.Descriptor instead.
func (*ListConnectionsSelector) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{1}
}

func (x *ListConnectionsSelector) GetType() []ConnectionType {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ListConnectionsSelector) GetStatus() []ConnectionStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListConnectionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result []*DataConnection `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty"`
}

func (x *ListConnectionsRsp) Reset() {
	*x = ListConnectionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConnectionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConnectionsRsp) ProtoMessage() {}

func (x *ListConnectionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConnectionsRsp.ProtoReflect.Descriptor instead.
func (*ListConnectionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{2}
}

func (x *ListConnectionsRsp) GetResult() []*DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type GetConnectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id          string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetConnectionReq) Reset() {
	*x = GetConnectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionReq) ProtoMessage() {}

func (x *GetConnectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionReq.ProtoReflect.Descriptor instead.
func (*GetConnectionReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{3}
}

func (x *GetConnectionReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *GetConnectionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetConnectionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *DataConnection `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GetConnectionRsp) Reset() {
	*x = GetConnectionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionRsp) ProtoMessage() {}

func (x *GetConnectionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionRsp.ProtoReflect.Descriptor instead.
func (*GetConnectionRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{4}
}

func (x *GetConnectionRsp) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type CreateConnectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext    `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Result      *DataConnection `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CreateConnectionReq) Reset() {
	*x = CreateConnectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConnectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConnectionReq) ProtoMessage() {}

func (x *CreateConnectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConnectionReq.ProtoReflect.Descriptor instead.
func (*CreateConnectionReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{5}
}

func (x *CreateConnectionReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CreateConnectionReq) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type CreateConnectionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *DataConnection `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CreateConnectionRsp) Reset() {
	*x = CreateConnectionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConnectionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConnectionRsp) ProtoMessage() {}

func (x *CreateConnectionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConnectionRsp.ProtoReflect.Descriptor instead.
func (*CreateConnectionRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{6}
}

func (x *CreateConnectionRsp) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdateConnectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext    `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Result      *DataConnection `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateConnectionReq) Reset() {
	*x = UpdateConnectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateConnectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConnectionReq) ProtoMessage() {}

func (x *UpdateConnectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConnectionReq.ProtoReflect.Descriptor instead.
func (*UpdateConnectionReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateConnectionReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *UpdateConnectionReq) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdateConnectionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *DataConnection `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateConnectionRsp) Reset() {
	*x = UpdateConnectionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateConnectionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConnectionRsp) ProtoMessage() {}

func (x *UpdateConnectionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConnectionRsp.ProtoReflect.Descriptor instead.
func (*UpdateConnectionRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateConnectionRsp) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeleteConnectionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Ids         []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"` // 可同时删除多个数据连接
}

func (x *DeleteConnectionsReq) Reset() {
	*x = DeleteConnectionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConnectionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConnectionsReq) ProtoMessage() {}

func (x *DeleteConnectionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConnectionsReq.ProtoReflect.Descriptor instead.
func (*DeleteConnectionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteConnectionsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *DeleteConnectionsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteConnectionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteConnectionsRsp) Reset() {
	*x = DeleteConnectionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConnectionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConnectionsRsp) ProtoMessage() {}

func (x *DeleteConnectionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConnectionsRsp.ProtoReflect.Descriptor instead.
func (*DeleteConnectionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{10}
}

type CloneConnectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id              string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	TargetProjectId string       `protobuf:"bytes,3,opt,name=target_project_id,json=targetProjectId,proto3" json:"target_project_id,omitempty"` // 目标项目，为空或者和user_context一致则为项目内克隆
}

func (x *CloneConnectionReq) Reset() {
	*x = CloneConnectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloneConnectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloneConnectionReq) ProtoMessage() {}

func (x *CloneConnectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloneConnectionReq.ProtoReflect.Descriptor instead.
func (*CloneConnectionReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{11}
}

func (x *CloneConnectionReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CloneConnectionReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CloneConnectionReq) GetTargetProjectId() string {
	if x != nil {
		return x.TargetProjectId
	}
	return ""
}

type CloneConnectionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *DataConnection `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CloneConnectionRsp) Reset() {
	*x = CloneConnectionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_data_connection_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloneConnectionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloneConnectionRsp) ProtoMessage() {}

func (x *CloneConnectionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_data_connection_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloneConnectionRsp.ProtoReflect.Descriptor instead.
func (*CloneConnectionRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_data_connection_proto_rawDescGZIP(), []int{12}
}

func (x *CloneConnectionRsp) GetResult() *DataConnection {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_proto_rpc_data_connection_proto protoreflect.FileDescriptor

var file_proto_rpc_data_connection_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x43, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0c,
	0x6c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x75, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x43, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x59, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7b, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x44, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7b, 0x0a, 0x13, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x44, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5f, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x16, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x87, 0x01, 0x0a, 0x12, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22,
	0x43, 0x0a, 0x12, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x32, 0xeb, 0x03, 0x0a, 0x15, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x4b,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x4e, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x4e, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x51, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x13, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_rpc_data_connection_proto_rawDescOnce sync.Once
	file_proto_rpc_data_connection_proto_rawDescData = file_proto_rpc_data_connection_proto_rawDesc
)

func file_proto_rpc_data_connection_proto_rawDescGZIP() []byte {
	file_proto_rpc_data_connection_proto_rawDescOnce.Do(func() {
		file_proto_rpc_data_connection_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_data_connection_proto_rawDescData)
	})
	return file_proto_rpc_data_connection_proto_rawDescData
}

var file_proto_rpc_data_connection_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_rpc_data_connection_proto_goTypes = []interface{}{
	(*ListConnectionsReq)(nil),      // 0: proto.ListConnectionsReq
	(*ListConnectionsSelector)(nil), // 1: proto.ListConnectionsSelector
	(*ListConnectionsRsp)(nil),      // 2: proto.ListConnectionsRsp
	(*GetConnectionReq)(nil),        // 3: proto.GetConnectionReq
	(*GetConnectionRsp)(nil),        // 4: proto.GetConnectionRsp
	(*CreateConnectionReq)(nil),     // 5: proto.CreateConnectionReq
	(*CreateConnectionRsp)(nil),     // 6: proto.CreateConnectionRsp
	(*UpdateConnectionReq)(nil),     // 7: proto.UpdateConnectionReq
	(*UpdateConnectionRsp)(nil),     // 8: proto.UpdateConnectionRsp
	(*DeleteConnectionsReq)(nil),    // 9: proto.DeleteConnectionsReq
	(*DeleteConnectionsRsp)(nil),    // 10: proto.DeleteConnectionsRsp
	(*CloneConnectionReq)(nil),      // 11: proto.CloneConnectionReq
	(*CloneConnectionRsp)(nil),      // 12: proto.CloneConnectionRsp
	(*UserContext)(nil),             // 13: proto.UserContext
	(ConnectionType)(0),             // 14: proto.ConnectionType
	(ConnectionStatus)(0),           // 15: proto.ConnectionStatus
	(*DataConnection)(nil),          // 16: proto.DataConnection
}
var file_proto_rpc_data_connection_proto_depIdxs = []int32{
	13, // 0: proto.ListConnectionsReq.user_context:type_name -> proto.UserContext
	1,  // 1: proto.ListConnectionsReq.list_selector:type_name -> proto.ListConnectionsSelector
	14, // 2: proto.ListConnectionsSelector.type:type_name -> proto.ConnectionType
	15, // 3: proto.ListConnectionsSelector.status:type_name -> proto.ConnectionStatus
	16, // 4: proto.ListConnectionsRsp.result:type_name -> proto.DataConnection
	13, // 5: proto.GetConnectionReq.user_context:type_name -> proto.UserContext
	16, // 6: proto.GetConnectionRsp.result:type_name -> proto.DataConnection
	13, // 7: proto.CreateConnectionReq.user_context:type_name -> proto.UserContext
	16, // 8: proto.CreateConnectionReq.result:type_name -> proto.DataConnection
	16, // 9: proto.CreateConnectionRsp.result:type_name -> proto.DataConnection
	13, // 10: proto.UpdateConnectionReq.user_context:type_name -> proto.UserContext
	16, // 11: proto.UpdateConnectionReq.result:type_name -> proto.DataConnection
	16, // 12: proto.UpdateConnectionRsp.result:type_name -> proto.DataConnection
	13, // 13: proto.DeleteConnectionsReq.user_context:type_name -> proto.UserContext
	13, // 14: proto.CloneConnectionReq.user_context:type_name -> proto.UserContext
	16, // 15: proto.CloneConnectionRsp.result:type_name -> proto.DataConnection
	0,  // 16: proto.DataConnectionManager.ListDataConnections:input_type -> proto.ListConnectionsReq
	3,  // 17: proto.DataConnectionManager.GetDataConnection:input_type -> proto.GetConnectionReq
	5,  // 18: proto.DataConnectionManager.CreateDataConnection:input_type -> proto.CreateConnectionReq
	7,  // 19: proto.DataConnectionManager.UpdateDataConnection:input_type -> proto.UpdateConnectionReq
	9,  // 20: proto.DataConnectionManager.DeleteDataConnections:input_type -> proto.DeleteConnectionsReq
	11, // 21: proto.DataConnectionManager.CloneDataConnection:input_type -> proto.CloneConnectionReq
	2,  // 22: proto.DataConnectionManager.ListDataConnections:output_type -> proto.ListConnectionsRsp
	4,  // 23: proto.DataConnectionManager.GetDataConnection:output_type -> proto.GetConnectionRsp
	6,  // 24: proto.DataConnectionManager.CreateDataConnection:output_type -> proto.CreateConnectionRsp
	8,  // 25: proto.DataConnectionManager.UpdateDataConnection:output_type -> proto.UpdateConnectionRsp
	10, // 26: proto.DataConnectionManager.DeleteDataConnections:output_type -> proto.DeleteConnectionsRsp
	12, // 27: proto.DataConnectionManager.CloneDataConnection:output_type -> proto.CloneConnectionRsp
	22, // [22:28] is the sub-list for method output_type
	16, // [16:22] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proto_rpc_data_connection_proto_init() }
func file_proto_rpc_data_connection_proto_init() {
	if File_proto_rpc_data_connection_proto != nil {
		return
	}
	file_proto_data_connection_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_data_connection_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConnectionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConnectionsSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConnectionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConnectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConnectionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateConnectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateConnectionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConnectionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConnectionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloneConnectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_data_connection_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloneConnectionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_data_connection_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_data_connection_proto_goTypes,
		DependencyIndexes: file_proto_rpc_data_connection_proto_depIdxs,
		MessageInfos:      file_proto_rpc_data_connection_proto_msgTypes,
	}.Build()
	File_proto_rpc_data_connection_proto = out.File
	file_proto_rpc_data_connection_proto_rawDesc = nil
	file_proto_rpc_data_connection_proto_goTypes = nil
	file_proto_rpc_data_connection_proto_depIdxs = nil
}
