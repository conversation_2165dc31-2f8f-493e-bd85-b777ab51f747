//
// WARNING: This file is automatically generated!  Please edit onnx.in.proto.
//
// SPDX-License-Identifier: Apache-2.0

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/onnx.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Versioning
//
// ONNX versioning is specified in docs/IR.md and elaborated on in docs/Versioning.md
//
// To be compatible with both proto2 and proto3, we will use a version number
// that is not defined by the default value but an explicit enum number.
type Version int32

const (
	// proto3 requires the first enum value to be zero.
	// We add this just to appease the compiler.
	Version__START_VERSION Version = 0
	// The version field is always serialized and we will use it to store the
	// version that the  graph is generated from. This helps us set up version
	// control.
	// For the IR, we are using simple numbers starting with 0x00000001,
	// which was the version we published on Oct 10, 2017.
	Version_IR_VERSION_2017_10_10 Version = 1
	// IR_VERSION 2 published on Oct 30, 2017
	// - Added type discriminator to AttributeProto to support proto3 users
	Version_IR_VERSION_2017_10_30 Version = 2
	// IR VERSION 3 published on Nov 3, 2017
	// - For operator versioning:
	//   - Added new message OperatorSetIdProto
	//   - Added opset_import in ModelProto
	//
	// - For vendor extensions, added domain in NodeProto
	Version_IR_VERSION_2017_11_3 Version = 3
	// IR VERSION 4 published on Jan 22, 2019
	// - Relax constraint that initializers should be a subset of graph inputs
	// - Add type BFLOAT16
	Version_IR_VERSION_2019_1_22 Version = 4
	// IR VERSION 5 published on March 18, 2019
	// - Add message TensorAnnotation.
	// - Add quantization annotation in GraphProto to map tensor with its scale and zero point quantization parameters.
	Version_IR_VERSION_2019_3_18 Version = 5
	// IR VERSION 6 published on Sep 19, 2019
	// - Add support for sparse tensor constants stored in model.
	//   - Add message SparseTensorProto
	//   - Add sparse initializers
	Version_IR_VERSION_2019_9_19 Version = 6
	// IR VERSION 7 published on May 8, 2020
	//   - Add support to allow function body graph to rely on multiple external opreator sets.
	//   - Add a list to promote inference graph's initializers to global and
	//     mutable variables. Global variables are visible in all graphs of the
	//     stored models.
	//   - Add message TrainingInfoProto to store initialization
	//     method and training algorithm. The execution of TrainingInfoProto
	//     can modify the values of mutable variables.
	//   - Implicitly add inference graph into each TrainingInfoProto's algorithm.
	Version_IR_VERSION_2020_5_8 Version = 7
	// IR VERSION 8 published on <TBD>
	// Introduce TypeProto.SparseTensor
	// Introduce TypeProto.Optional
	// Added a list of FunctionProtos local to the model
	// Deprecated since_version and operator status from FunctionProto
	Version_IR_VERSION Version = 8
)

// Enum value maps for Version.
var (
	Version_name = map[int32]string{
		0: "_START_VERSION",
		1: "IR_VERSION_2017_10_10",
		2: "IR_VERSION_2017_10_30",
		3: "IR_VERSION_2017_11_3",
		4: "IR_VERSION_2019_1_22",
		5: "IR_VERSION_2019_3_18",
		6: "IR_VERSION_2019_9_19",
		7: "IR_VERSION_2020_5_8",
		8: "IR_VERSION",
	}
	Version_value = map[string]int32{
		"_START_VERSION":        0,
		"IR_VERSION_2017_10_10": 1,
		"IR_VERSION_2017_10_30": 2,
		"IR_VERSION_2017_11_3":  3,
		"IR_VERSION_2019_1_22":  4,
		"IR_VERSION_2019_3_18":  5,
		"IR_VERSION_2019_9_19":  6,
		"IR_VERSION_2020_5_8":   7,
		"IR_VERSION":            8,
	}
)

func (x Version) Enum() *Version {
	p := new(Version)
	*p = x
	return p
}

func (x Version) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Version) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_onnx_proto_enumTypes[0].Descriptor()
}

func (Version) Type() protoreflect.EnumType {
	return &file_proto_onnx_proto_enumTypes[0]
}

func (x Version) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Version.Descriptor instead.
func (Version) EnumDescriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{0}
}

// Operator/function status.
type OperatorStatus int32

const (
	OperatorStatus_EXPERIMENTAL OperatorStatus = 0
	OperatorStatus_STABLE       OperatorStatus = 1
)

// Enum value maps for OperatorStatus.
var (
	OperatorStatus_name = map[int32]string{
		0: "EXPERIMENTAL",
		1: "STABLE",
	}
	OperatorStatus_value = map[string]int32{
		"EXPERIMENTAL": 0,
		"STABLE":       1,
	}
)

func (x OperatorStatus) Enum() *OperatorStatus {
	p := new(OperatorStatus)
	*p = x
	return p
}

func (x OperatorStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatorStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_onnx_proto_enumTypes[1].Descriptor()
}

func (OperatorStatus) Type() protoreflect.EnumType {
	return &file_proto_onnx_proto_enumTypes[1]
}

func (x OperatorStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatorStatus.Descriptor instead.
func (OperatorStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{1}
}

// Note: this enum is structurally identical to the OpSchema::AttrType
// enum defined in schema.h.  If you rev one, you likely need to rev the other.
type AttributeProto_AttributeType int32

const (
	AttributeProto_UNDEFINED      AttributeProto_AttributeType = 0
	AttributeProto_FLOAT          AttributeProto_AttributeType = 1
	AttributeProto_INT            AttributeProto_AttributeType = 2
	AttributeProto_STRING         AttributeProto_AttributeType = 3
	AttributeProto_TENSOR         AttributeProto_AttributeType = 4
	AttributeProto_GRAPH          AttributeProto_AttributeType = 5
	AttributeProto_SPARSE_TENSOR  AttributeProto_AttributeType = 11
	AttributeProto_TYPE_PROTO     AttributeProto_AttributeType = 13
	AttributeProto_FLOATS         AttributeProto_AttributeType = 6
	AttributeProto_INTS           AttributeProto_AttributeType = 7
	AttributeProto_STRINGS        AttributeProto_AttributeType = 8
	AttributeProto_TENSORS        AttributeProto_AttributeType = 9
	AttributeProto_GRAPHS         AttributeProto_AttributeType = 10
	AttributeProto_SPARSE_TENSORS AttributeProto_AttributeType = 12
	AttributeProto_TYPE_PROTOS    AttributeProto_AttributeType = 14
)

// Enum value maps for AttributeProto_AttributeType.
var (
	AttributeProto_AttributeType_name = map[int32]string{
		0:  "UNDEFINED",
		1:  "FLOAT",
		2:  "INT",
		3:  "STRING",
		4:  "TENSOR",
		5:  "GRAPH",
		11: "SPARSE_TENSOR",
		13: "TYPE_PROTO",
		6:  "FLOATS",
		7:  "INTS",
		8:  "STRINGS",
		9:  "TENSORS",
		10: "GRAPHS",
		12: "SPARSE_TENSORS",
		14: "TYPE_PROTOS",
	}
	AttributeProto_AttributeType_value = map[string]int32{
		"UNDEFINED":      0,
		"FLOAT":          1,
		"INT":            2,
		"STRING":         3,
		"TENSOR":         4,
		"GRAPH":          5,
		"SPARSE_TENSOR":  11,
		"TYPE_PROTO":     13,
		"FLOATS":         6,
		"INTS":           7,
		"STRINGS":        8,
		"TENSORS":        9,
		"GRAPHS":         10,
		"SPARSE_TENSORS": 12,
		"TYPE_PROTOS":    14,
	}
)

func (x AttributeProto_AttributeType) Enum() *AttributeProto_AttributeType {
	p := new(AttributeProto_AttributeType)
	*p = x
	return p
}

func (x AttributeProto_AttributeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeProto_AttributeType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_onnx_proto_enumTypes[2].Descriptor()
}

func (AttributeProto_AttributeType) Type() protoreflect.EnumType {
	return &file_proto_onnx_proto_enumTypes[2]
}

func (x AttributeProto_AttributeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeProto_AttributeType.Descriptor instead.
func (AttributeProto_AttributeType) EnumDescriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{0, 0}
}

type TensorProto_DataType int32

const (
	TensorProto_UNDEFINED TensorProto_DataType = 0
	// Basic types.
	TensorProto_FLOAT  TensorProto_DataType = 1 // float
	TensorProto_UINT8  TensorProto_DataType = 2 // uint8_t
	TensorProto_INT8   TensorProto_DataType = 3 // int8_t
	TensorProto_UINT16 TensorProto_DataType = 4 // uint16_t
	TensorProto_INT16  TensorProto_DataType = 5 // int16_t
	TensorProto_INT32  TensorProto_DataType = 6 // int32_t
	TensorProto_INT64  TensorProto_DataType = 7 // int64_t
	TensorProto_STRING TensorProto_DataType = 8 // string
	TensorProto_BOOL   TensorProto_DataType = 9 // bool
	// IEEE754 half-precision floating-point format (16 bits wide).
	// This format has 1 sign bit, 5 exponent bits, and 10 mantissa bits.
	TensorProto_FLOAT16    TensorProto_DataType = 10
	TensorProto_DOUBLE     TensorProto_DataType = 11
	TensorProto_UINT32     TensorProto_DataType = 12
	TensorProto_UINT64     TensorProto_DataType = 13
	TensorProto_COMPLEX64  TensorProto_DataType = 14 // complex with float32 real and imaginary components
	TensorProto_COMPLEX128 TensorProto_DataType = 15 // complex with float64 real and imaginary components
	// Non-IEEE floating-point format based on IEEE754 single-precision
	// floating-point number truncated to 16 bits.
	// This format has 1 sign bit, 8 exponent bits, and 7 mantissa bits.
	TensorProto_BFLOAT16 TensorProto_DataType = 16
)

// Enum value maps for TensorProto_DataType.
var (
	TensorProto_DataType_name = map[int32]string{
		0:  "UNDEFINED",
		1:  "FLOAT",
		2:  "UINT8",
		3:  "INT8",
		4:  "UINT16",
		5:  "INT16",
		6:  "INT32",
		7:  "INT64",
		8:  "STRING",
		9:  "BOOL",
		10: "FLOAT16",
		11: "DOUBLE",
		12: "UINT32",
		13: "UINT64",
		14: "COMPLEX64",
		15: "COMPLEX128",
		16: "BFLOAT16",
	}
	TensorProto_DataType_value = map[string]int32{
		"UNDEFINED":  0,
		"FLOAT":      1,
		"UINT8":      2,
		"INT8":       3,
		"UINT16":     4,
		"INT16":      5,
		"INT32":      6,
		"INT64":      7,
		"STRING":     8,
		"BOOL":       9,
		"FLOAT16":    10,
		"DOUBLE":     11,
		"UINT32":     12,
		"UINT64":     13,
		"COMPLEX64":  14,
		"COMPLEX128": 15,
		"BFLOAT16":   16,
	}
)

func (x TensorProto_DataType) Enum() *TensorProto_DataType {
	p := new(TensorProto_DataType)
	*p = x
	return p
}

func (x TensorProto_DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TensorProto_DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_onnx_proto_enumTypes[3].Descriptor()
}

func (TensorProto_DataType) Type() protoreflect.EnumType {
	return &file_proto_onnx_proto_enumTypes[3]
}

func (x TensorProto_DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TensorProto_DataType.Descriptor instead.
func (TensorProto_DataType) EnumDescriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{8, 0}
}

// Location of the data for this tensor. MUST be one of:
// - DEFAULT - data stored inside the protobuf message. Data is stored in raw_data (if set) otherwise in type-specified field.
// - EXTERNAL - data stored in an external location as described by external_data field.
type TensorProto_DataLocation int32

const (
	TensorProto_DEFAULT  TensorProto_DataLocation = 0
	TensorProto_EXTERNAL TensorProto_DataLocation = 1
)

// Enum value maps for TensorProto_DataLocation.
var (
	TensorProto_DataLocation_name = map[int32]string{
		0: "DEFAULT",
		1: "EXTERNAL",
	}
	TensorProto_DataLocation_value = map[string]int32{
		"DEFAULT":  0,
		"EXTERNAL": 1,
	}
)

func (x TensorProto_DataLocation) Enum() *TensorProto_DataLocation {
	p := new(TensorProto_DataLocation)
	*p = x
	return p
}

func (x TensorProto_DataLocation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TensorProto_DataLocation) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_onnx_proto_enumTypes[4].Descriptor()
}

func (TensorProto_DataLocation) Type() protoreflect.EnumType {
	return &file_proto_onnx_proto_enumTypes[4]
}

func (x TensorProto_DataLocation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TensorProto_DataLocation.Descriptor instead.
func (TensorProto_DataLocation) EnumDescriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{8, 1}
}

// Attributes
//
// A named attribute containing either singular float, integer, string, graph,
// and tensor values, or repeated float, integer, string, graph, and tensor values.
// An AttributeProto MUST contain the name field, and *only one* of the
// following content fields, effectively enforcing a C/C++ union equivalent.
type AttributeProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name field MUST be present for this version of the IR.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // namespace Attribute
	// if ref_attr_name is not empty, ref_attr_name is the attribute name in parent function.
	// In this case, this AttributeProto does not contain data, and it's a reference of attribute
	// in parent scope.
	// NOTE: This should ONLY be used in function (sub-graph). It's invalid to be used in main graph.
	RefAttrName string `protobuf:"bytes,21,opt,name=ref_attr_name,json=refAttrName,proto3" json:"ref_attr_name,omitempty"`
	// A human-readable documentation for this attribute. Markdown is allowed.
	DocString string `protobuf:"bytes,13,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
	// The type field MUST be present for this version of the IR.
	// For 0.0.1 versions of the IR, this field was not defined, and
	// implementations needed to use has_field heuristics to determine
	// which value field was in use.  For IR_VERSION 0.0.2 or later, this
	// field MUST be set and match the f|i|s|t|... field in use.  This
	// change was made to accommodate proto3 implementations.
	Type AttributeProto_AttributeType `protobuf:"varint,20,opt,name=type,proto3,enum=proto.AttributeProto_AttributeType" json:"type,omitempty"` // discriminator that indicates which field below is in use
	// Exactly ONE of the following fields must be present for this version of the IR
	F            float32            `protobuf:"fixed32,2,opt,name=f,proto3" json:"f,omitempty"`                                          // float
	I            int64              `protobuf:"varint,3,opt,name=i,proto3" json:"i,omitempty"`                                           // int
	S            []byte             `protobuf:"bytes,4,opt,name=s,proto3" json:"s,omitempty"`                                            // UTF-8 string
	T            *TensorProto       `protobuf:"bytes,5,opt,name=t,proto3" json:"t,omitempty"`                                            // tensor value
	G            *GraphProto        `protobuf:"bytes,6,opt,name=g,proto3" json:"g,omitempty"`                                            // graph
	SparseTensor *SparseTensorProto `protobuf:"bytes,22,opt,name=sparse_tensor,json=sparseTensor,proto3" json:"sparse_tensor,omitempty"` // sparse tensor value
	// Do not use field below, it's deprecated.
	// optional ValueProto v = 12;         // value - subsumes everything but graph
	Tp            *TypeProto           `protobuf:"bytes,14,opt,name=tp,proto3" json:"tp,omitempty"`                                            // type proto
	Floats        []float32            `protobuf:"fixed32,7,rep,packed,name=floats,proto3" json:"floats,omitempty"`                            // list of floats
	Ints          []int64              `protobuf:"varint,8,rep,packed,name=ints,proto3" json:"ints,omitempty"`                                 // list of ints
	Strings       [][]byte             `protobuf:"bytes,9,rep,name=strings,proto3" json:"strings,omitempty"`                                   // list of UTF-8 strings
	Tensors       []*TensorProto       `protobuf:"bytes,10,rep,name=tensors,proto3" json:"tensors,omitempty"`                                  // list of tensors
	Graphs        []*GraphProto        `protobuf:"bytes,11,rep,name=graphs,proto3" json:"graphs,omitempty"`                                    // list of graph
	SparseTensors []*SparseTensorProto `protobuf:"bytes,23,rep,name=sparse_tensors,json=sparseTensors,proto3" json:"sparse_tensors,omitempty"` // list of sparse tensors
	TypeProtos    []*TypeProto         `protobuf:"bytes,15,rep,name=type_protos,json=typeProtos,proto3" json:"type_protos,omitempty"`          // list of type protos
}

func (x *AttributeProto) Reset() {
	*x = AttributeProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttributeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttributeProto) ProtoMessage() {}

func (x *AttributeProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttributeProto.ProtoReflect.Descriptor instead.
func (*AttributeProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{0}
}

func (x *AttributeProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AttributeProto) GetRefAttrName() string {
	if x != nil {
		return x.RefAttrName
	}
	return ""
}

func (x *AttributeProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

func (x *AttributeProto) GetType() AttributeProto_AttributeType {
	if x != nil {
		return x.Type
	}
	return AttributeProto_UNDEFINED
}

func (x *AttributeProto) GetF() float32 {
	if x != nil {
		return x.F
	}
	return 0
}

func (x *AttributeProto) GetI() int64 {
	if x != nil {
		return x.I
	}
	return 0
}

func (x *AttributeProto) GetS() []byte {
	if x != nil {
		return x.S
	}
	return nil
}

func (x *AttributeProto) GetT() *TensorProto {
	if x != nil {
		return x.T
	}
	return nil
}

func (x *AttributeProto) GetG() *GraphProto {
	if x != nil {
		return x.G
	}
	return nil
}

func (x *AttributeProto) GetSparseTensor() *SparseTensorProto {
	if x != nil {
		return x.SparseTensor
	}
	return nil
}

func (x *AttributeProto) GetTp() *TypeProto {
	if x != nil {
		return x.Tp
	}
	return nil
}

func (x *AttributeProto) GetFloats() []float32 {
	if x != nil {
		return x.Floats
	}
	return nil
}

func (x *AttributeProto) GetInts() []int64 {
	if x != nil {
		return x.Ints
	}
	return nil
}

func (x *AttributeProto) GetStrings() [][]byte {
	if x != nil {
		return x.Strings
	}
	return nil
}

func (x *AttributeProto) GetTensors() []*TensorProto {
	if x != nil {
		return x.Tensors
	}
	return nil
}

func (x *AttributeProto) GetGraphs() []*GraphProto {
	if x != nil {
		return x.Graphs
	}
	return nil
}

func (x *AttributeProto) GetSparseTensors() []*SparseTensorProto {
	if x != nil {
		return x.SparseTensors
	}
	return nil
}

func (x *AttributeProto) GetTypeProtos() []*TypeProto {
	if x != nil {
		return x.TypeProtos
	}
	return nil
}

// Defines information on value, including the name, the type, and
// the shape of the value.
type ValueInfoProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This field MUST be present in this version of the IR.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // namespace Value
	// This field MUST be present in this version of the IR for
	// inputs and outputs of the top-level graph.
	Type *TypeProto `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// A human-readable documentation for this value. Markdown is allowed.
	DocString string `protobuf:"bytes,3,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
}

func (x *ValueInfoProto) Reset() {
	*x = ValueInfoProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueInfoProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueInfoProto) ProtoMessage() {}

func (x *ValueInfoProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueInfoProto.ProtoReflect.Descriptor instead.
func (*ValueInfoProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{1}
}

func (x *ValueInfoProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ValueInfoProto) GetType() *TypeProto {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ValueInfoProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

// Nodes
//
// Computation graphs are made up of a DAG of nodes, which represent what is
// commonly called a "layer" or "pipeline stage" in machine learning frameworks.
//
// For example, it can be a node of type "Conv" that takes in an image, a filter
// tensor and a bias tensor, and produces the convolved output.
type NodeProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Input  []string `protobuf:"bytes,1,rep,name=input,proto3" json:"input,omitempty"`   // namespace Value
	Output []string `protobuf:"bytes,2,rep,name=output,proto3" json:"output,omitempty"` // namespace Value
	// An optional identifier for this node in a graph.
	// This field MAY be absent in ths version of the IR.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"` // namespace Node
	// The symbolic identifier of the Operator to execute.
	OpType string `protobuf:"bytes,4,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"` // namespace Operator
	// The domain of the OperatorSet that specifies the operator named by op_type.
	Domain string `protobuf:"bytes,7,opt,name=domain,proto3" json:"domain,omitempty"` // namespace Domain
	// Additional named attributes.
	Attribute []*AttributeProto `protobuf:"bytes,5,rep,name=attribute,proto3" json:"attribute,omitempty"`
	// A human-readable documentation for this node. Markdown is allowed.
	DocString string `protobuf:"bytes,6,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
}

func (x *NodeProto) Reset() {
	*x = NodeProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeProto) ProtoMessage() {}

func (x *NodeProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeProto.ProtoReflect.Descriptor instead.
func (*NodeProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{2}
}

func (x *NodeProto) GetInput() []string {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *NodeProto) GetOutput() []string {
	if x != nil {
		return x.Output
	}
	return nil
}

func (x *NodeProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeProto) GetOpType() string {
	if x != nil {
		return x.OpType
	}
	return ""
}

func (x *NodeProto) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *NodeProto) GetAttribute() []*AttributeProto {
	if x != nil {
		return x.Attribute
	}
	return nil
}

func (x *NodeProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

// Training information
// TrainingInfoProto stores information for training a model.
// In particular, this defines two functionalities: an initialization-step
// and a training-algorithm-step. Initialization resets the model
// back to its original state as if no training has been performed.
// Training algorithm improves the model based on input data.
//
// The semantics of the initialization-step is that the initializers
// in ModelProto.graph and in TrainingInfoProto.algorithm are first
// initialized as specified by the initializers in the graph, and then
// updated by the "initialization_binding" in every instance in
// ModelProto.training_info.
//
// The field "algorithm" defines a computation graph which represents a
// training algorithm's step. After the execution of a
// TrainingInfoProto.algorithm, the initializers specified by "update_binding"
// may be immediately updated. If the targeted training algorithm contains
// consecutive update steps (such as block coordinate descent methods),
// the user needs to create a TrainingInfoProto for each step.
type TrainingInfoProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// By default, this field is an empty graph and its evaluation does not
	// produce any output. Thus, no initializer would be changed by default.
	Initialization *GraphProto `protobuf:"bytes,1,opt,name=initialization,proto3" json:"initialization,omitempty"`
	// By default, this field is an empty graph and its evaluation does not
	// produce any output. Evaluating the default training step never
	// update any initializers.
	Algorithm *GraphProto `protobuf:"bytes,2,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	// By default, this field is empty and no initializer would be changed
	// by the execution of "initialization".
	InitializationBinding []*StringStringEntryProto `protobuf:"bytes,3,rep,name=initialization_binding,json=initializationBinding,proto3" json:"initialization_binding,omitempty"`
	// By default, this field is empty and no initializer would be changed
	// by the execution of "algorithm".
	UpdateBinding []*StringStringEntryProto `protobuf:"bytes,4,rep,name=update_binding,json=updateBinding,proto3" json:"update_binding,omitempty"`
}

func (x *TrainingInfoProto) Reset() {
	*x = TrainingInfoProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainingInfoProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainingInfoProto) ProtoMessage() {}

func (x *TrainingInfoProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainingInfoProto.ProtoReflect.Descriptor instead.
func (*TrainingInfoProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{3}
}

func (x *TrainingInfoProto) GetInitialization() *GraphProto {
	if x != nil {
		return x.Initialization
	}
	return nil
}

func (x *TrainingInfoProto) GetAlgorithm() *GraphProto {
	if x != nil {
		return x.Algorithm
	}
	return nil
}

func (x *TrainingInfoProto) GetInitializationBinding() []*StringStringEntryProto {
	if x != nil {
		return x.InitializationBinding
	}
	return nil
}

func (x *TrainingInfoProto) GetUpdateBinding() []*StringStringEntryProto {
	if x != nil {
		return x.UpdateBinding
	}
	return nil
}

// Models
//
// ModelProto is a top-level file/container format for bundling a ML model and
// associating its computation graph with metadata.
//
// The semantics of the model are described by the associated GraphProto's.
type ModelProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The version of the IR this model targets. See Version enum above.
	// This field MUST be present.
	IrVersion int64 `protobuf:"varint,1,opt,name=ir_version,json=irVersion,proto3" json:"ir_version,omitempty"`
	// All nodes in the ModelProto's graph will bind against the operator
	// with the same-domain/same-op_type operator with the HIGHEST version
	// in the referenced operator sets.
	OpsetImport []*OperatorSetIdProto `protobuf:"bytes,8,rep,name=opset_import,json=opsetImport,proto3" json:"opset_import,omitempty"`
	// The name of the framework or tool used to generate this model.
	// This field SHOULD be present to indicate which implementation/tool/framework
	// emitted the model.
	ProducerName string `protobuf:"bytes,2,opt,name=producer_name,json=producerName,proto3" json:"producer_name,omitempty"`
	// The version of the framework or tool used to generate this model.
	// This field SHOULD be present to indicate which implementation/tool/framework
	// emitted the model.
	ProducerVersion string `protobuf:"bytes,3,opt,name=producer_version,json=producerVersion,proto3" json:"producer_version,omitempty"`
	// Together with `model_version` and GraphProto.name, this forms the unique identity of
	// the graph.
	Domain string `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	// The version of the graph encoded. See Version enum below.
	ModelVersion int64 `protobuf:"varint,5,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// A human-readable documentation for this model. Markdown is allowed.
	DocString string `protobuf:"bytes,6,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
	// The parameterized graph that is evaluated to execute the model.
	Graph *GraphProto `protobuf:"bytes,7,opt,name=graph,proto3" json:"graph,omitempty"`
	// Named metadata values; keys should be distinct.
	MetadataProps []*StringStringEntryProto `protobuf:"bytes,14,rep,name=metadata_props,json=metadataProps,proto3" json:"metadata_props,omitempty"`
	// If this field is empty, the training behavior of the model is undefined.
	TrainingInfo []*TrainingInfoProto `protobuf:"bytes,20,rep,name=training_info,json=trainingInfo,proto3" json:"training_info,omitempty"`
	// One FunctionProto can reference other FunctionProto in the model, however, recursive reference
	// is not allowed.
	Functions []*FunctionProto `protobuf:"bytes,25,rep,name=functions,proto3" json:"functions,omitempty"`
}

func (x *ModelProto) Reset() {
	*x = ModelProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelProto) ProtoMessage() {}

func (x *ModelProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelProto.ProtoReflect.Descriptor instead.
func (*ModelProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{4}
}

func (x *ModelProto) GetIrVersion() int64 {
	if x != nil {
		return x.IrVersion
	}
	return 0
}

func (x *ModelProto) GetOpsetImport() []*OperatorSetIdProto {
	if x != nil {
		return x.OpsetImport
	}
	return nil
}

func (x *ModelProto) GetProducerName() string {
	if x != nil {
		return x.ProducerName
	}
	return ""
}

func (x *ModelProto) GetProducerVersion() string {
	if x != nil {
		return x.ProducerVersion
	}
	return ""
}

func (x *ModelProto) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ModelProto) GetModelVersion() int64 {
	if x != nil {
		return x.ModelVersion
	}
	return 0
}

func (x *ModelProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

func (x *ModelProto) GetGraph() *GraphProto {
	if x != nil {
		return x.Graph
	}
	return nil
}

func (x *ModelProto) GetMetadataProps() []*StringStringEntryProto {
	if x != nil {
		return x.MetadataProps
	}
	return nil
}

func (x *ModelProto) GetTrainingInfo() []*TrainingInfoProto {
	if x != nil {
		return x.TrainingInfo
	}
	return nil
}

func (x *ModelProto) GetFunctions() []*FunctionProto {
	if x != nil {
		return x.Functions
	}
	return nil
}

// StringStringEntryProto follows the pattern for cross-proto-version maps.
// See https://developers.google.com/protocol-buffers/docs/proto3#maps
type StringStringEntryProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringStringEntryProto) Reset() {
	*x = StringStringEntryProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringStringEntryProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringStringEntryProto) ProtoMessage() {}

func (x *StringStringEntryProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringStringEntryProto.ProtoReflect.Descriptor instead.
func (*StringStringEntryProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{5}
}

func (x *StringStringEntryProto) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *StringStringEntryProto) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TensorAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TensorName string `protobuf:"bytes,1,opt,name=tensor_name,json=tensorName,proto3" json:"tensor_name,omitempty"`
	// <key, value> pairs to annotate tensor specified by <tensor_name> above.
	// The keys used in the mapping below must be pre-defined in ONNX spec.
	// For example, for 8-bit linear quantization case, 'SCALE_TENSOR', 'ZERO_POINT_TENSOR' will be pre-defined as
	// quantization parameter keys.
	QuantParameterTensorNames []*StringStringEntryProto `protobuf:"bytes,2,rep,name=quant_parameter_tensor_names,json=quantParameterTensorNames,proto3" json:"quant_parameter_tensor_names,omitempty"`
}

func (x *TensorAnnotation) Reset() {
	*x = TensorAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorAnnotation) ProtoMessage() {}

func (x *TensorAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorAnnotation.ProtoReflect.Descriptor instead.
func (*TensorAnnotation) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{6}
}

func (x *TensorAnnotation) GetTensorName() string {
	if x != nil {
		return x.TensorName
	}
	return ""
}

func (x *TensorAnnotation) GetQuantParameterTensorNames() []*StringStringEntryProto {
	if x != nil {
		return x.QuantParameterTensorNames
	}
	return nil
}

// Graphs
//
// A graph defines the computational logic of a model and is comprised of a parameterized
// list of nodes that form a directed acyclic graph based on their inputs and outputs.
// This is the equivalent of the "network" or "graph" in many deep learning
// frameworks.
type GraphProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The nodes in the graph, sorted topologically.
	Node []*NodeProto `protobuf:"bytes,1,rep,name=node,proto3" json:"node,omitempty"`
	// The name of the graph.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // namespace Graph
	// A list of named tensor values, used to specify constant inputs of the graph.
	// Each initializer (both TensorProto as well SparseTensorProto) MUST have a name.
	// The name MUST be unique across both initializer and sparse_initializer,
	// but the name MAY also appear in the input list.
	Initializer []*TensorProto `protobuf:"bytes,5,rep,name=initializer,proto3" json:"initializer,omitempty"`
	// Initializers (see above) stored in sparse format.
	SparseInitializer []*SparseTensorProto `protobuf:"bytes,15,rep,name=sparse_initializer,json=sparseInitializer,proto3" json:"sparse_initializer,omitempty"`
	// A human-readable documentation for this graph. Markdown is allowed.
	DocString string `protobuf:"bytes,10,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
	// The inputs and outputs of the graph.
	Input  []*ValueInfoProto `protobuf:"bytes,11,rep,name=input,proto3" json:"input,omitempty"`
	Output []*ValueInfoProto `protobuf:"bytes,12,rep,name=output,proto3" json:"output,omitempty"`
	// Information for the values in the graph. The ValueInfoProto.name's
	// must be distinct. It is optional for a value to appear in value_info list.
	ValueInfo []*ValueInfoProto `protobuf:"bytes,13,rep,name=value_info,json=valueInfo,proto3" json:"value_info,omitempty"`
	// This field carries information to indicate the mapping among a tensor and its
	// quantization parameter tensors. For example:
	// For tensor 'a', it may have {'SCALE_TENSOR', 'a_scale'} and {'ZERO_POINT_TENSOR', 'a_zero_point'} annotated,
	// which means, tensor 'a_scale' and tensor 'a_zero_point' are scale and zero point of tensor 'a' in the model.
	QuantizationAnnotation []*TensorAnnotation `protobuf:"bytes,14,rep,name=quantization_annotation,json=quantizationAnnotation,proto3" json:"quantization_annotation,omitempty"`
}

func (x *GraphProto) Reset() {
	*x = GraphProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraphProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraphProto) ProtoMessage() {}

func (x *GraphProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraphProto.ProtoReflect.Descriptor instead.
func (*GraphProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{7}
}

func (x *GraphProto) GetNode() []*NodeProto {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *GraphProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GraphProto) GetInitializer() []*TensorProto {
	if x != nil {
		return x.Initializer
	}
	return nil
}

func (x *GraphProto) GetSparseInitializer() []*SparseTensorProto {
	if x != nil {
		return x.SparseInitializer
	}
	return nil
}

func (x *GraphProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

func (x *GraphProto) GetInput() []*ValueInfoProto {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *GraphProto) GetOutput() []*ValueInfoProto {
	if x != nil {
		return x.Output
	}
	return nil
}

func (x *GraphProto) GetValueInfo() []*ValueInfoProto {
	if x != nil {
		return x.ValueInfo
	}
	return nil
}

func (x *GraphProto) GetQuantizationAnnotation() []*TensorAnnotation {
	if x != nil {
		return x.QuantizationAnnotation
	}
	return nil
}

// Tensors
//
// A serialized tensor value.
type TensorProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The shape of the tensor.
	Dims []int64 `protobuf:"varint,1,rep,packed,name=dims,proto3" json:"dims,omitempty"`
	// The data type of the tensor.
	// This field MUST have a valid TensorProto.DataType value
	DataType int32                `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Segment  *TensorProto_Segment `protobuf:"bytes,3,opt,name=segment,proto3" json:"segment,omitempty"`
	// For float and complex64 values
	// Complex64 tensors are encoded as a single array of floats,
	// with the real components appearing in odd numbered positions,
	// and the corresponding imaginary component appearing in the
	// subsequent even numbered position. (e.g., [1.0 + 2.0i, 3.0 + 4.0i]
	// is encoded as [1.0, 2.0 ,3.0 ,4.0]
	// When this field is present, the data_type field MUST be FLOAT or COMPLEX64.
	FloatData []float32 `protobuf:"fixed32,4,rep,packed,name=float_data,json=floatData,proto3" json:"float_data,omitempty"`
	// For int32, uint8, int8, uint16, int16, bool, and float16 values
	// float16 values must be bit-wise converted to an uint16_t prior
	// to writing to the buffer.
	// When this field is present, the data_type field MUST be
	// INT32, INT16, INT8, UINT16, UINT8, BOOL, FLOAT16 or BFLOAT16
	Int32Data []int32 `protobuf:"varint,5,rep,packed,name=int32_data,json=int32Data,proto3" json:"int32_data,omitempty"`
	// For strings.
	// Each element of string_data is a UTF-8 encoded Unicode
	// string. No trailing null, no leading BOM. The protobuf "string"
	// scalar type is not used to match ML community conventions.
	// When this field is present, the data_type field MUST be STRING
	StringData [][]byte `protobuf:"bytes,6,rep,name=string_data,json=stringData,proto3" json:"string_data,omitempty"`
	// For int64.
	// When this field is present, the data_type field MUST be INT64
	Int64Data []int64 `protobuf:"varint,7,rep,packed,name=int64_data,json=int64Data,proto3" json:"int64_data,omitempty"`
	// Optionally, a name for the tensor.
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"` // namespace Value
	// A human-readable documentation for this tensor. Markdown is allowed.
	DocString string `protobuf:"bytes,12,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
	// Note: the advantage of specific field rather than the raw_data field is
	// that in some cases (e.g. int data), protobuf does a better packing via
	// variable length storage, and may lead to smaller binary footprint.
	// When this field is present, the data_type field MUST NOT be STRING or UNDEFINED
	RawData []byte `protobuf:"bytes,9,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
	// Data can be stored inside the protobuf file using type-specific fields or raw_data.
	// Alternatively, raw bytes data can be stored in an external file, using the external_data field.
	// external_data stores key-value pairs describing data location. Recognized keys are:
	//   - "location" (required) - POSIX filesystem path relative to the directory where the ONNX
	//     protobuf model was stored
	//   - "offset" (optional) - position of byte at which stored data begins. Integer stored as string.
	//     Offset values SHOULD be multiples 4096 (page size) to enable mmap support.
	//   - "length" (optional) - number of bytes containing data. Integer stored as string.
	//   - "checksum" (optional) - SHA1 digest of file specified in under 'location' key.
	ExternalData []*StringStringEntryProto `protobuf:"bytes,13,rep,name=external_data,json=externalData,proto3" json:"external_data,omitempty"`
	// If value not set, data is stored in raw_data (if set) otherwise in type-specified field.
	DataLocation TensorProto_DataLocation `protobuf:"varint,14,opt,name=data_location,json=dataLocation,proto3,enum=proto.TensorProto_DataLocation" json:"data_location,omitempty"`
	// For double
	// Complex128 tensors are encoded as a single array of doubles,
	// with the real components appearing in odd numbered positions,
	// and the corresponding imaginary component appearing in the
	// subsequent even numbered position. (e.g., [1.0 + 2.0i, 3.0 + 4.0i]
	// is encoded as [1.0, 2.0 ,3.0 ,4.0]
	// When this field is present, the data_type field MUST be DOUBLE or COMPLEX128
	DoubleData []float64 `protobuf:"fixed64,10,rep,packed,name=double_data,json=doubleData,proto3" json:"double_data,omitempty"`
	// For uint64 and uint32 values
	// When this field is present, the data_type field MUST be
	// UINT32 or UINT64
	Uint64Data []uint64 `protobuf:"varint,11,rep,packed,name=uint64_data,json=uint64Data,proto3" json:"uint64_data,omitempty"`
}

func (x *TensorProto) Reset() {
	*x = TensorProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorProto) ProtoMessage() {}

func (x *TensorProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorProto.ProtoReflect.Descriptor instead.
func (*TensorProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{8}
}

func (x *TensorProto) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

func (x *TensorProto) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *TensorProto) GetSegment() *TensorProto_Segment {
	if x != nil {
		return x.Segment
	}
	return nil
}

func (x *TensorProto) GetFloatData() []float32 {
	if x != nil {
		return x.FloatData
	}
	return nil
}

func (x *TensorProto) GetInt32Data() []int32 {
	if x != nil {
		return x.Int32Data
	}
	return nil
}

func (x *TensorProto) GetStringData() [][]byte {
	if x != nil {
		return x.StringData
	}
	return nil
}

func (x *TensorProto) GetInt64Data() []int64 {
	if x != nil {
		return x.Int64Data
	}
	return nil
}

func (x *TensorProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TensorProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

func (x *TensorProto) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

func (x *TensorProto) GetExternalData() []*StringStringEntryProto {
	if x != nil {
		return x.ExternalData
	}
	return nil
}

func (x *TensorProto) GetDataLocation() TensorProto_DataLocation {
	if x != nil {
		return x.DataLocation
	}
	return TensorProto_DEFAULT
}

func (x *TensorProto) GetDoubleData() []float64 {
	if x != nil {
		return x.DoubleData
	}
	return nil
}

func (x *TensorProto) GetUint64Data() []uint64 {
	if x != nil {
		return x.Uint64Data
	}
	return nil
}

// A serialized sparse-tensor value
type SparseTensorProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The sequence of non-default values are encoded as a tensor of shape [NNZ].
	// The default-value is zero for numeric tensors, and empty-string for string tensors.
	// values must have a non-empty name present which serves as a name for SparseTensorProto
	// when used in sparse_initializer list.
	Values *TensorProto `protobuf:"bytes,1,opt,name=values,proto3" json:"values,omitempty"`
	// The indices of the non-default values, which may be stored in one of two formats.
	// (a) Indices can be a tensor of shape [NNZ, rank] with the [i,j]-th value
	// corresponding to the j-th index of the i-th value (in the values tensor).
	// (b) Indices can be a tensor of shape [NNZ], in which case the i-th value
	// must be the linearized-index of the i-th value (in the values tensor).
	// The linearized-index can be converted into an index tuple (k_1,...,k_rank)
	// using the shape provided below.
	// The indices must appear in ascending order without duplication.
	// In the first format, the ordering is lexicographic-ordering:
	// e.g., index-value [1,4] must appear before [2,1]
	Indices *TensorProto `protobuf:"bytes,2,opt,name=indices,proto3" json:"indices,omitempty"`
	// The shape of the underlying dense-tensor: [dim_1, dim_2, ... dim_rank]
	Dims []int64 `protobuf:"varint,3,rep,packed,name=dims,proto3" json:"dims,omitempty"`
}

func (x *SparseTensorProto) Reset() {
	*x = SparseTensorProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SparseTensorProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SparseTensorProto) ProtoMessage() {}

func (x *SparseTensorProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SparseTensorProto.ProtoReflect.Descriptor instead.
func (*SparseTensorProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{9}
}

func (x *SparseTensorProto) GetValues() *TensorProto {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *SparseTensorProto) GetIndices() *TensorProto {
	if x != nil {
		return x.Indices
	}
	return nil
}

func (x *SparseTensorProto) GetDims() []int64 {
	if x != nil {
		return x.Dims
	}
	return nil
}

// Defines a tensor shape. A dimension can be either an integer value
// or a symbolic variable. A symbolic variable represents an unknown
// dimension.
type TensorShapeProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dim []*TensorShapeProto_Dimension `protobuf:"bytes,1,rep,name=dim,proto3" json:"dim,omitempty"`
}

func (x *TensorShapeProto) Reset() {
	*x = TensorShapeProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorShapeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto) ProtoMessage() {}

func (x *TensorShapeProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto.ProtoReflect.Descriptor instead.
func (*TensorShapeProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{10}
}

func (x *TensorShapeProto) GetDim() []*TensorShapeProto_Dimension {
	if x != nil {
		return x.Dim
	}
	return nil
}

// Types
//
// The standard ONNX data types.
type TypeProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*TypeProto_TensorType
	//	*TypeProto_SequenceType
	//	*TypeProto_MapType
	//	*TypeProto_OptionalType
	//	*TypeProto_SparseTensorType
	Value isTypeProto_Value `protobuf_oneof:"value"`
	// An optional denotation can be used to denote the whole
	// type with a standard semantic description as to what is
	// stored inside. Refer to https://github.com/onnx/onnx/blob/main/docs/TypeDenotation.md#type-denotation-definition
	// for pre-defined type denotations.
	Denotation string `protobuf:"bytes,6,opt,name=denotation,proto3" json:"denotation,omitempty"`
}

func (x *TypeProto) Reset() {
	*x = TypeProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto) ProtoMessage() {}

func (x *TypeProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto.ProtoReflect.Descriptor instead.
func (*TypeProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11}
}

func (m *TypeProto) GetValue() isTypeProto_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *TypeProto) GetTensorType() *TypeProto_Tensor {
	if x, ok := x.GetValue().(*TypeProto_TensorType); ok {
		return x.TensorType
	}
	return nil
}

func (x *TypeProto) GetSequenceType() *TypeProto_Sequence {
	if x, ok := x.GetValue().(*TypeProto_SequenceType); ok {
		return x.SequenceType
	}
	return nil
}

func (x *TypeProto) GetMapType() *TypeProto_Map {
	if x, ok := x.GetValue().(*TypeProto_MapType); ok {
		return x.MapType
	}
	return nil
}

func (x *TypeProto) GetOptionalType() *TypeProto_Optional {
	if x, ok := x.GetValue().(*TypeProto_OptionalType); ok {
		return x.OptionalType
	}
	return nil
}

func (x *TypeProto) GetSparseTensorType() *TypeProto_SparseTensor {
	if x, ok := x.GetValue().(*TypeProto_SparseTensorType); ok {
		return x.SparseTensorType
	}
	return nil
}

func (x *TypeProto) GetDenotation() string {
	if x != nil {
		return x.Denotation
	}
	return ""
}

type isTypeProto_Value interface {
	isTypeProto_Value()
}

type TypeProto_TensorType struct {
	// The type of a tensor.
	TensorType *TypeProto_Tensor `protobuf:"bytes,1,opt,name=tensor_type,json=tensorType,proto3,oneof"`
}

type TypeProto_SequenceType struct {
	// The type of a sequence.
	SequenceType *TypeProto_Sequence `protobuf:"bytes,4,opt,name=sequence_type,json=sequenceType,proto3,oneof"`
}

type TypeProto_MapType struct {
	// The type of a map.
	MapType *TypeProto_Map `protobuf:"bytes,5,opt,name=map_type,json=mapType,proto3,oneof"`
}

type TypeProto_OptionalType struct {
	// The type of an optional.
	OptionalType *TypeProto_Optional `protobuf:"bytes,9,opt,name=optional_type,json=optionalType,proto3,oneof"`
}

type TypeProto_SparseTensorType struct {
	// Type of the sparse tensor
	SparseTensorType *TypeProto_SparseTensor `protobuf:"bytes,8,opt,name=sparse_tensor_type,json=sparseTensorType,proto3,oneof"`
}

func (*TypeProto_TensorType) isTypeProto_Value() {}

func (*TypeProto_SequenceType) isTypeProto_Value() {}

func (*TypeProto_MapType) isTypeProto_Value() {}

func (*TypeProto_OptionalType) isTypeProto_Value() {}

func (*TypeProto_SparseTensorType) isTypeProto_Value() {}

// Operator Sets
//
// OperatorSets are uniquely identified by a (domain, opset_version) pair.
type OperatorSetIdProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The domain of the operator set being identified.
	// The empty string ("") or absence of this field implies the operator
	// set that is defined as part of the ONNX specification.
	// This field MUST be present in this version of the IR when referring to any other operator set.
	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	// The version of the operator set being identified.
	// This field MUST be present in this version of the IR.
	Version int64 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *OperatorSetIdProto) Reset() {
	*x = OperatorSetIdProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperatorSetIdProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperatorSetIdProto) ProtoMessage() {}

func (x *OperatorSetIdProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperatorSetIdProto.ProtoReflect.Descriptor instead.
func (*OperatorSetIdProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{12}
}

func (x *OperatorSetIdProto) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *OperatorSetIdProto) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type FunctionProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the function, similar usage of op_type in OperatorProto.
	// Combined with FunctionProto.domain, this forms the unique identity of
	// the FunctionProto.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The inputs and outputs of the function.
	Input  []string `protobuf:"bytes,4,rep,name=input,proto3" json:"input,omitempty"`
	Output []string `protobuf:"bytes,5,rep,name=output,proto3" json:"output,omitempty"`
	// The attributes of the function.
	Attribute []string `protobuf:"bytes,6,rep,name=attribute,proto3" json:"attribute,omitempty"`
	// The nodes in the function.
	Node []*NodeProto `protobuf:"bytes,7,rep,name=node,proto3" json:"node,omitempty"`
	// A human-readable documentation for this function. Markdown is allowed.
	DocString string `protobuf:"bytes,8,opt,name=doc_string,json=docString,proto3" json:"doc_string,omitempty"`
	// The operator sets imported by FunctionProto should be compatible with the ones
	// imported by ModelProto. Example, if same operator set say 'A' is imported by FunctionProto
	// and ModelProto then versions for the operator set may be different but,
	// the operator schema returned for op_type, domain, version combination
	// for both the versions should be same.
	OpsetImport []*OperatorSetIdProto `protobuf:"bytes,9,rep,name=opset_import,json=opsetImport,proto3" json:"opset_import,omitempty"`
	// The domain which this function belongs to. Combined with FunctionProto.name, this forms the unique identity of
	// the FunctionProto.
	Domain string `protobuf:"bytes,10,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *FunctionProto) Reset() {
	*x = FunctionProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunctionProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionProto) ProtoMessage() {}

func (x *FunctionProto) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionProto.ProtoReflect.Descriptor instead.
func (*FunctionProto) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{13}
}

func (x *FunctionProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FunctionProto) GetInput() []string {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *FunctionProto) GetOutput() []string {
	if x != nil {
		return x.Output
	}
	return nil
}

func (x *FunctionProto) GetAttribute() []string {
	if x != nil {
		return x.Attribute
	}
	return nil
}

func (x *FunctionProto) GetNode() []*NodeProto {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *FunctionProto) GetDocString() string {
	if x != nil {
		return x.DocString
	}
	return ""
}

func (x *FunctionProto) GetOpsetImport() []*OperatorSetIdProto {
	if x != nil {
		return x.OpsetImport
	}
	return nil
}

func (x *FunctionProto) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

// For very large tensors, we may want to store them in chunks, in which
// case the following fields will specify the segment that is stored in
// the current TensorProto.
type TensorProto_Segment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Begin int64 `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	End   int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *TensorProto_Segment) Reset() {
	*x = TensorProto_Segment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorProto_Segment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorProto_Segment) ProtoMessage() {}

func (x *TensorProto_Segment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorProto_Segment.ProtoReflect.Descriptor instead.
func (*TensorProto_Segment) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{8, 0}
}

func (x *TensorProto_Segment) GetBegin() int64 {
	if x != nil {
		return x.Begin
	}
	return 0
}

func (x *TensorProto_Segment) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type TensorShapeProto_Dimension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*TensorShapeProto_Dimension_DimValue
	//	*TensorShapeProto_Dimension_DimParam
	Value isTensorShapeProto_Dimension_Value `protobuf_oneof:"value"`
	// Standard denotation can optionally be used to denote tensor
	// dimensions with standard semantic descriptions to ensure
	// that operations are applied to the correct axis of a tensor.
	// Refer to https://github.com/onnx/onnx/blob/main/docs/DimensionDenotation.md#denotation-definition
	// for pre-defined dimension denotations.
	Denotation string `protobuf:"bytes,3,opt,name=denotation,proto3" json:"denotation,omitempty"`
}

func (x *TensorShapeProto_Dimension) Reset() {
	*x = TensorShapeProto_Dimension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorShapeProto_Dimension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto_Dimension) ProtoMessage() {}

func (x *TensorShapeProto_Dimension) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto_Dimension.ProtoReflect.Descriptor instead.
func (*TensorShapeProto_Dimension) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{10, 0}
}

func (m *TensorShapeProto_Dimension) GetValue() isTensorShapeProto_Dimension_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *TensorShapeProto_Dimension) GetDimValue() int64 {
	if x, ok := x.GetValue().(*TensorShapeProto_Dimension_DimValue); ok {
		return x.DimValue
	}
	return 0
}

func (x *TensorShapeProto_Dimension) GetDimParam() string {
	if x, ok := x.GetValue().(*TensorShapeProto_Dimension_DimParam); ok {
		return x.DimParam
	}
	return ""
}

func (x *TensorShapeProto_Dimension) GetDenotation() string {
	if x != nil {
		return x.Denotation
	}
	return ""
}

type isTensorShapeProto_Dimension_Value interface {
	isTensorShapeProto_Dimension_Value()
}

type TensorShapeProto_Dimension_DimValue struct {
	DimValue int64 `protobuf:"varint,1,opt,name=dim_value,json=dimValue,proto3,oneof"`
}

type TensorShapeProto_Dimension_DimParam struct {
	DimParam string `protobuf:"bytes,2,opt,name=dim_param,json=dimParam,proto3,oneof"`
}

func (*TensorShapeProto_Dimension_DimValue) isTensorShapeProto_Dimension_Value() {}

func (*TensorShapeProto_Dimension_DimParam) isTensorShapeProto_Dimension_Value() {}

type TypeProto_Tensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This field MUST NOT have the value of UNDEFINED
	// This field MUST have a valid TensorProto.DataType value
	// This field MUST be present for this version of the IR.
	ElemType int32             `protobuf:"varint,1,opt,name=elem_type,json=elemType,proto3" json:"elem_type,omitempty"`
	Shape    *TensorShapeProto `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
}

func (x *TypeProto_Tensor) Reset() {
	*x = TypeProto_Tensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto_Tensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto_Tensor) ProtoMessage() {}

func (x *TypeProto_Tensor) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto_Tensor.ProtoReflect.Descriptor instead.
func (*TypeProto_Tensor) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11, 0}
}

func (x *TypeProto_Tensor) GetElemType() int32 {
	if x != nil {
		return x.ElemType
	}
	return 0
}

func (x *TypeProto_Tensor) GetShape() *TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

// repeated T
type TypeProto_Sequence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type and optional shape of each element of the sequence.
	// This field MUST be present for this version of the IR.
	ElemType *TypeProto `protobuf:"bytes,1,opt,name=elem_type,json=elemType,proto3" json:"elem_type,omitempty"`
}

func (x *TypeProto_Sequence) Reset() {
	*x = TypeProto_Sequence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto_Sequence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto_Sequence) ProtoMessage() {}

func (x *TypeProto_Sequence) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto_Sequence.ProtoReflect.Descriptor instead.
func (*TypeProto_Sequence) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11, 1}
}

func (x *TypeProto_Sequence) GetElemType() *TypeProto {
	if x != nil {
		return x.ElemType
	}
	return nil
}

// map<K,V>
type TypeProto_Map struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This field MUST have a valid TensorProto.DataType value
	// This field MUST be present for this version of the IR.
	// This field MUST refer to an integral type ([U]INT{8|16|32|64}) or STRING
	KeyType int32 `protobuf:"varint,1,opt,name=key_type,json=keyType,proto3" json:"key_type,omitempty"`
	// This field MUST be present for this version of the IR.
	ValueType *TypeProto `protobuf:"bytes,2,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
}

func (x *TypeProto_Map) Reset() {
	*x = TypeProto_Map{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto_Map) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto_Map) ProtoMessage() {}

func (x *TypeProto_Map) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto_Map.ProtoReflect.Descriptor instead.
func (*TypeProto_Map) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11, 2}
}

func (x *TypeProto_Map) GetKeyType() int32 {
	if x != nil {
		return x.KeyType
	}
	return 0
}

func (x *TypeProto_Map) GetValueType() *TypeProto {
	if x != nil {
		return x.ValueType
	}
	return nil
}

// wrapper for Tensor, Sequence, or Map
type TypeProto_Optional struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type and optional shape of the element wrapped.
	// This field MUST be present for this version of the IR.
	// Possible values correspond to OptionalProto.DataType enum
	ElemType *TypeProto `protobuf:"bytes,1,opt,name=elem_type,json=elemType,proto3" json:"elem_type,omitempty"`
}

func (x *TypeProto_Optional) Reset() {
	*x = TypeProto_Optional{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto_Optional) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto_Optional) ProtoMessage() {}

func (x *TypeProto_Optional) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto_Optional.ProtoReflect.Descriptor instead.
func (*TypeProto_Optional) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11, 3}
}

func (x *TypeProto_Optional) GetElemType() *TypeProto {
	if x != nil {
		return x.ElemType
	}
	return nil
}

type TypeProto_SparseTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This field MUST NOT have the value of UNDEFINED
	// This field MUST have a valid TensorProto.DataType value
	// This field MUST be present for this version of the IR.
	ElemType int32             `protobuf:"varint,1,opt,name=elem_type,json=elemType,proto3" json:"elem_type,omitempty"`
	Shape    *TensorShapeProto `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
}

func (x *TypeProto_SparseTensor) Reset() {
	*x = TypeProto_SparseTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_onnx_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeProto_SparseTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeProto_SparseTensor) ProtoMessage() {}

func (x *TypeProto_SparseTensor) ProtoReflect() protoreflect.Message {
	mi := &file_proto_onnx_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeProto_SparseTensor.ProtoReflect.Descriptor instead.
func (*TypeProto_SparseTensor) Descriptor() ([]byte, []int) {
	return file_proto_onnx_proto_rawDescGZIP(), []int{11, 4}
}

func (x *TypeProto_SparseTensor) GetElemType() int32 {
	if x != nil {
		return x.ElemType
	}
	return 0
}

func (x *TypeProto_SparseTensor) GetShape() *TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

var File_proto_onnx_proto protoreflect.FileDescriptor

var file_proto_onnx_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6f, 0x6e, 0x6e, 0x78, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdd, 0x06, 0x0a, 0x0e, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x41, 0x74, 0x74, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x37, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01,
	0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x66, 0x12, 0x0c, 0x0a, 0x01, 0x69, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x01, 0x69, 0x12, 0x0c, 0x0a, 0x01, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x01, 0x73, 0x12, 0x20, 0x0a, 0x01, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x01, 0x74, 0x12, 0x1f, 0x0a, 0x01, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x01, 0x67, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x72, 0x73, 0x65, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0c, 0x73, 0x70, 0x61, 0x72,
	0x73, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x02, 0x74, 0x70, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x02, 0x74, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x02, 0x52, 0x06, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x04, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x07, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x2c, 0x0a, 0x07, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x07, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x29,
	0x0a, 0x06, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x06, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x73, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0d, 0x73, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x31, 0x0a, 0x0b, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x22, 0xd9, 0x01,
	0x0a, 0x0d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52,
	0x41, 0x50, 0x48, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x41, 0x52, 0x53, 0x45, 0x5f,
	0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x4c, 0x4f, 0x41,
	0x54, 0x53, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x07, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x54,
	0x45, 0x4e, 0x53, 0x4f, 0x52, 0x53, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x52, 0x41, 0x50,
	0x48, 0x53, 0x10, 0x0a, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x41, 0x52, 0x53, 0x45, 0x5f, 0x54,
	0x45, 0x4e, 0x53, 0x4f, 0x52, 0x53, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x53, 0x10, 0x0e, 0x22, 0x69, 0x0a, 0x0e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x22, 0xd2, 0x01, 0x0a, 0x09, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f,
	0x63, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x6f, 0x63, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x9b, 0x02, 0x0a, 0x11, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x39, 0x0a, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x09, 0x61, 0x6c,
	0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x54, 0x0a, 0x16, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x15, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x44, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xf7, 0x03, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x72, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x72, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x6f, 0x70, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x74, 0x49,
	0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0b, 0x6f, 0x70, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x27, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x44, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x3d,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a,
	0x09, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x09, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x40, 0x0a, 0x16, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x10, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x1c, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x19,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x8e, 0x04, 0x0a, 0x0a, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0b, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x12, 0x73, 0x70, 0x61, 0x72,
	0x73, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x11,
	0x73, 0x70, 0x61, 0x72, 0x73, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x2b, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x2d, 0x0a,
	0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x34, 0x0a, 0x0a,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x50, 0x0a, 0x17, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05,
	0x4a, 0x04, 0x08, 0x06, 0x10, 0x0a, 0x52, 0x0a, 0x69, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f, 0x74,
	0x61, 0x67, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0xdb, 0x06, 0x0a, 0x0b, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x21, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x02, 0x42, 0x02, 0x10, 0x01, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x42, 0x02, 0x10, 0x01, 0x52, 0x09, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0d, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a,
	0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0b, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0b, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x31, 0x0a,
	0x07, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x22, 0xda, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a,
	0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x49, 0x4e, 0x54, 0x38,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x54, 0x38, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06,
	0x55, 0x49, 0x4e, 0x54, 0x31, 0x36, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x54, 0x31,
	0x36, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x54, 0x33, 0x32, 0x10, 0x06, 0x12, 0x09,
	0x0a, 0x05, 0x49, 0x4e, 0x54, 0x36, 0x34, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52,
	0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x09, 0x12,
	0x0b, 0x0a, 0x07, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x31, 0x36, 0x10, 0x0a, 0x12, 0x0a, 0x0a, 0x06,
	0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x49, 0x4e, 0x54,
	0x33, 0x32, 0x10, 0x0c, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x49, 0x4e, 0x54, 0x36, 0x34, 0x10, 0x0d,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x58, 0x36, 0x34, 0x10, 0x0e, 0x12,
	0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x58, 0x31, 0x32, 0x38, 0x10, 0x0f, 0x12,
	0x0c, 0x0a, 0x08, 0x42, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x31, 0x36, 0x10, 0x10, 0x22, 0x29, 0x0a,
	0x0c, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a,
	0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x58,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x22, 0x81, 0x01, 0x0a, 0x11, 0x53, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2a,
	0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x69, 0x6e,
	0x64, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x07, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x6d, 0x73, 0x22, 0xbb, 0x01, 0x0a,
	0x10, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x33, 0x0a, 0x03, 0x64, 0x69, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61,
	0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x64, 0x69, 0x6d, 0x1a, 0x72, 0x0a, 0x09, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x6d, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x6d, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xf1, 0x05, 0x0a, 0x09, 0x54,
	0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x3a, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x48, 0x00,
	0x52, 0x07, 0x6d, 0x61, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0c, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x12, 0x73,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x10, 0x73, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x54, 0x0a, 0x06, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x1a, 0x39, 0x0a, 0x08, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x09,
	0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x51, 0x0a, 0x03, 0x4d,
	0x61, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a,
	0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x39,
	0x0a, 0x08, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x09, 0x65, 0x6c,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x08, 0x65, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x5a, 0x0a, 0x0c, 0x53, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6c, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6c,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x46,
	0x0a, 0x12, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x74, 0x49, 0x64, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xad, 0x02, 0x0a, 0x0d, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x0a,
	0x0c, 0x6f, 0x70, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x74, 0x49, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0b,
	0x6f, 0x70, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x52,
	0x0d, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0xe4, 0x01, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x0e, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52,
	0x53, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x31, 0x37, 0x5f, 0x31, 0x30, 0x5f, 0x31, 0x30, 0x10,
	0x01, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x32, 0x30, 0x31, 0x37, 0x5f, 0x31, 0x30, 0x5f, 0x33, 0x30, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x49, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x31, 0x37, 0x5f,
	0x31, 0x31, 0x5f, 0x33, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x31, 0x39, 0x5f, 0x31, 0x5f, 0x32, 0x32, 0x10, 0x04,
	0x12, 0x18, 0x0a, 0x14, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x32,
	0x30, 0x31, 0x39, 0x5f, 0x33, 0x5f, 0x31, 0x38, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x52,
	0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x31, 0x39, 0x5f, 0x39, 0x5f,
	0x31, 0x39, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x32, 0x30, 0x5f, 0x35, 0x5f, 0x38, 0x10, 0x07, 0x12, 0x0e, 0x0a,
	0x0a, 0x49, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x2a, 0x2e, 0x0a,
	0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x10, 0x0a, 0x0c, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x42, 0x28, 0x48,
	0x03, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f,
	0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_onnx_proto_rawDescOnce sync.Once
	file_proto_onnx_proto_rawDescData = file_proto_onnx_proto_rawDesc
)

func file_proto_onnx_proto_rawDescGZIP() []byte {
	file_proto_onnx_proto_rawDescOnce.Do(func() {
		file_proto_onnx_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_onnx_proto_rawDescData)
	})
	return file_proto_onnx_proto_rawDescData
}

var file_proto_onnx_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_proto_onnx_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_proto_onnx_proto_goTypes = []interface{}{
	(Version)(0),                       // 0: proto.Version
	(OperatorStatus)(0),                // 1: proto.OperatorStatus
	(AttributeProto_AttributeType)(0),  // 2: proto.AttributeProto.AttributeType
	(TensorProto_DataType)(0),          // 3: proto.TensorProto.DataType
	(TensorProto_DataLocation)(0),      // 4: proto.TensorProto.DataLocation
	(*AttributeProto)(nil),             // 5: proto.AttributeProto
	(*ValueInfoProto)(nil),             // 6: proto.ValueInfoProto
	(*NodeProto)(nil),                  // 7: proto.NodeProto
	(*TrainingInfoProto)(nil),          // 8: proto.TrainingInfoProto
	(*ModelProto)(nil),                 // 9: proto.ModelProto
	(*StringStringEntryProto)(nil),     // 10: proto.StringStringEntryProto
	(*TensorAnnotation)(nil),           // 11: proto.TensorAnnotation
	(*GraphProto)(nil),                 // 12: proto.GraphProto
	(*TensorProto)(nil),                // 13: proto.TensorProto
	(*SparseTensorProto)(nil),          // 14: proto.SparseTensorProto
	(*TensorShapeProto)(nil),           // 15: proto.TensorShapeProto
	(*TypeProto)(nil),                  // 16: proto.TypeProto
	(*OperatorSetIdProto)(nil),         // 17: proto.OperatorSetIdProto
	(*FunctionProto)(nil),              // 18: proto.FunctionProto
	(*TensorProto_Segment)(nil),        // 19: proto.TensorProto.Segment
	(*TensorShapeProto_Dimension)(nil), // 20: proto.TensorShapeProto.Dimension
	(*TypeProto_Tensor)(nil),           // 21: proto.TypeProto.Tensor
	(*TypeProto_Sequence)(nil),         // 22: proto.TypeProto.Sequence
	(*TypeProto_Map)(nil),              // 23: proto.TypeProto.Map
	(*TypeProto_Optional)(nil),         // 24: proto.TypeProto.Optional
	(*TypeProto_SparseTensor)(nil),     // 25: proto.TypeProto.SparseTensor
}
var file_proto_onnx_proto_depIdxs = []int32{
	2,  // 0: proto.AttributeProto.type:type_name -> proto.AttributeProto.AttributeType
	13, // 1: proto.AttributeProto.t:type_name -> proto.TensorProto
	12, // 2: proto.AttributeProto.g:type_name -> proto.GraphProto
	14, // 3: proto.AttributeProto.sparse_tensor:type_name -> proto.SparseTensorProto
	16, // 4: proto.AttributeProto.tp:type_name -> proto.TypeProto
	13, // 5: proto.AttributeProto.tensors:type_name -> proto.TensorProto
	12, // 6: proto.AttributeProto.graphs:type_name -> proto.GraphProto
	14, // 7: proto.AttributeProto.sparse_tensors:type_name -> proto.SparseTensorProto
	16, // 8: proto.AttributeProto.type_protos:type_name -> proto.TypeProto
	16, // 9: proto.ValueInfoProto.type:type_name -> proto.TypeProto
	5,  // 10: proto.NodeProto.attribute:type_name -> proto.AttributeProto
	12, // 11: proto.TrainingInfoProto.initialization:type_name -> proto.GraphProto
	12, // 12: proto.TrainingInfoProto.algorithm:type_name -> proto.GraphProto
	10, // 13: proto.TrainingInfoProto.initialization_binding:type_name -> proto.StringStringEntryProto
	10, // 14: proto.TrainingInfoProto.update_binding:type_name -> proto.StringStringEntryProto
	17, // 15: proto.ModelProto.opset_import:type_name -> proto.OperatorSetIdProto
	12, // 16: proto.ModelProto.graph:type_name -> proto.GraphProto
	10, // 17: proto.ModelProto.metadata_props:type_name -> proto.StringStringEntryProto
	8,  // 18: proto.ModelProto.training_info:type_name -> proto.TrainingInfoProto
	18, // 19: proto.ModelProto.functions:type_name -> proto.FunctionProto
	10, // 20: proto.TensorAnnotation.quant_parameter_tensor_names:type_name -> proto.StringStringEntryProto
	7,  // 21: proto.GraphProto.node:type_name -> proto.NodeProto
	13, // 22: proto.GraphProto.initializer:type_name -> proto.TensorProto
	14, // 23: proto.GraphProto.sparse_initializer:type_name -> proto.SparseTensorProto
	6,  // 24: proto.GraphProto.input:type_name -> proto.ValueInfoProto
	6,  // 25: proto.GraphProto.output:type_name -> proto.ValueInfoProto
	6,  // 26: proto.GraphProto.value_info:type_name -> proto.ValueInfoProto
	11, // 27: proto.GraphProto.quantization_annotation:type_name -> proto.TensorAnnotation
	19, // 28: proto.TensorProto.segment:type_name -> proto.TensorProto.Segment
	10, // 29: proto.TensorProto.external_data:type_name -> proto.StringStringEntryProto
	4,  // 30: proto.TensorProto.data_location:type_name -> proto.TensorProto.DataLocation
	13, // 31: proto.SparseTensorProto.values:type_name -> proto.TensorProto
	13, // 32: proto.SparseTensorProto.indices:type_name -> proto.TensorProto
	20, // 33: proto.TensorShapeProto.dim:type_name -> proto.TensorShapeProto.Dimension
	21, // 34: proto.TypeProto.tensor_type:type_name -> proto.TypeProto.Tensor
	22, // 35: proto.TypeProto.sequence_type:type_name -> proto.TypeProto.Sequence
	23, // 36: proto.TypeProto.map_type:type_name -> proto.TypeProto.Map
	24, // 37: proto.TypeProto.optional_type:type_name -> proto.TypeProto.Optional
	25, // 38: proto.TypeProto.sparse_tensor_type:type_name -> proto.TypeProto.SparseTensor
	7,  // 39: proto.FunctionProto.node:type_name -> proto.NodeProto
	17, // 40: proto.FunctionProto.opset_import:type_name -> proto.OperatorSetIdProto
	15, // 41: proto.TypeProto.Tensor.shape:type_name -> proto.TensorShapeProto
	16, // 42: proto.TypeProto.Sequence.elem_type:type_name -> proto.TypeProto
	16, // 43: proto.TypeProto.Map.value_type:type_name -> proto.TypeProto
	16, // 44: proto.TypeProto.Optional.elem_type:type_name -> proto.TypeProto
	15, // 45: proto.TypeProto.SparseTensor.shape:type_name -> proto.TensorShapeProto
	46, // [46:46] is the sub-list for method output_type
	46, // [46:46] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_proto_onnx_proto_init() }
func file_proto_onnx_proto_init() {
	if File_proto_onnx_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_onnx_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttributeProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueInfoProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainingInfoProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringStringEntryProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraphProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SparseTensorProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorShapeProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperatorSetIdProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunctionProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorProto_Segment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorShapeProto_Dimension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto_Tensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto_Sequence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto_Map); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto_Optional); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_onnx_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeProto_SparseTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_onnx_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*TypeProto_TensorType)(nil),
		(*TypeProto_SequenceType)(nil),
		(*TypeProto_MapType)(nil),
		(*TypeProto_OptionalType)(nil),
		(*TypeProto_SparseTensorType)(nil),
	}
	file_proto_onnx_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*TensorShapeProto_Dimension_DimValue)(nil),
		(*TensorShapeProto_Dimension_DimParam)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_onnx_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_onnx_proto_goTypes,
		DependencyIndexes: file_proto_onnx_proto_depIdxs,
		EnumInfos:         file_proto_onnx_proto_enumTypes,
		MessageInfos:      file_proto_onnx_proto_msgTypes,
	}.Build()
	File_proto_onnx_proto = out.File
	file_proto_onnx_proto_rawDesc = nil
	file_proto_onnx_proto_goTypes = nil
	file_proto_onnx_proto_depIdxs = nil
}
