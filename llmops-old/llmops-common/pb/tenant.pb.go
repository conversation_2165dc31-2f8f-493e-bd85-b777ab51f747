// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/tenant.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Tenant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TenantName        string               `protobuf:"bytes,1,opt,name=tenant_name,json=tenantName,proto3" json:"tenant_name,omitempty"`
	TenantUid         string               `protobuf:"bytes,2,opt,name=tenant_uid,json=tenantUid,proto3" json:"tenant_uid,omitempty"`
	TenantDescription string               `protobuf:"bytes,3,opt,name=tenant_description,json=tenantDescription,proto3" json:"tenant_description,omitempty"`
	TenantLogo        string               `protobuf:"bytes,4,opt,name=tenant_logo,json=tenantLogo,proto3" json:"tenant_logo,omitempty"`
	TenantQuotas      *TenantResourceQuota `protobuf:"bytes,5,opt,name=tenant_quotas,json=tenantQuotas,proto3" json:"tenant_quotas,omitempty"`
	TenantStatus      string               `protobuf:"bytes,6,opt,name=tenant_status,json=tenantStatus,proto3" json:"tenant_status,omitempty"`
	Creator           string               `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime        string               `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	TenantLabels      map[string]string    `protobuf:"bytes,9,rep,name=tenant_labels,json=tenantLabels,proto3" json:"tenant_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TenantAnnotations map[string]string    `protobuf:"bytes,10,rep,name=tenant_annotations,json=tenantAnnotations,proto3" json:"tenant_annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TccUrl            string               `protobuf:"bytes,11,opt,name=tcc_url,json=tccUrl,proto3" json:"tcc_url,omitempty"`
	HippoServiceName  string               `protobuf:"bytes,12,opt,name=hippo_service_name,json=hippoServiceName,proto3" json:"hippo_service_name,omitempty"`
}

func (x *Tenant) Reset() {
	*x = Tenant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_tenant_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tenant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tenant) ProtoMessage() {}

func (x *Tenant) ProtoReflect() protoreflect.Message {
	mi := &file_proto_tenant_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tenant.ProtoReflect.Descriptor instead.
func (*Tenant) Descriptor() ([]byte, []int) {
	return file_proto_tenant_proto_rawDescGZIP(), []int{0}
}

func (x *Tenant) GetTenantName() string {
	if x != nil {
		return x.TenantName
	}
	return ""
}

func (x *Tenant) GetTenantUid() string {
	if x != nil {
		return x.TenantUid
	}
	return ""
}

func (x *Tenant) GetTenantDescription() string {
	if x != nil {
		return x.TenantDescription
	}
	return ""
}

func (x *Tenant) GetTenantLogo() string {
	if x != nil {
		return x.TenantLogo
	}
	return ""
}

func (x *Tenant) GetTenantQuotas() *TenantResourceQuota {
	if x != nil {
		return x.TenantQuotas
	}
	return nil
}

func (x *Tenant) GetTenantStatus() string {
	if x != nil {
		return x.TenantStatus
	}
	return ""
}

func (x *Tenant) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Tenant) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Tenant) GetTenantLabels() map[string]string {
	if x != nil {
		return x.TenantLabels
	}
	return nil
}

func (x *Tenant) GetTenantAnnotations() map[string]string {
	if x != nil {
		return x.TenantAnnotations
	}
	return nil
}

func (x *Tenant) GetTccUrl() string {
	if x != nil {
		return x.TccUrl
	}
	return ""
}

func (x *Tenant) GetHippoServiceName() string {
	if x != nil {
		return x.HippoServiceName
	}
	return ""
}

type ResourceQuotaSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LimitsCpu         string `protobuf:"bytes,1,opt,name=limits_cpu,json=limitsCpu,proto3" json:"limits_cpu,omitempty"`
	LimitsMemory      string `protobuf:"bytes,2,opt,name=limits_memory,json=limitsMemory,proto3" json:"limits_memory,omitempty"`
	RequestsCpu       string `protobuf:"bytes,3,opt,name=requests_cpu,json=requestsCpu,proto3" json:"requests_cpu,omitempty"`
	RequestsMemory    string `protobuf:"bytes,4,opt,name=requests_memory,json=requestsMemory,proto3" json:"requests_memory,omitempty"`
	RequestsStorage   string `protobuf:"bytes,5,opt,name=requests_storage,json=requestsStorage,proto3" json:"requests_storage,omitempty"`
	Pods              string `protobuf:"bytes,6,opt,name=pods,proto3" json:"pods,omitempty"`
	Bandwidth         string `protobuf:"bytes,7,opt,name=bandwidth,proto3" json:"bandwidth,omitempty"`
	EgressBandwidth   string `protobuf:"bytes,8,opt,name=egress_bandwidth,json=egressBandwidth,proto3" json:"egress_bandwidth,omitempty"`
	IngressBandwidth  string `protobuf:"bytes,9,opt,name=ingress_bandwidth,json=ingressBandwidth,proto3" json:"ingress_bandwidth,omitempty"`
	Gpu               string `protobuf:"bytes,10,opt,name=gpu,proto3" json:"gpu,omitempty"`
	GpuMemory         string `protobuf:"bytes,11,opt,name=gpu_memory,json=gpuMemory,proto3" json:"gpu_memory,omitempty"`
	RequestsGpu       string `protobuf:"bytes,12,opt,name=requests_gpu,json=requestsGpu,proto3" json:"requests_gpu,omitempty"`
	RequestsGpuMemory string `protobuf:"bytes,13,opt,name=requests_gpu_memory,json=requestsGpuMemory,proto3" json:"requests_gpu_memory,omitempty"`
	Knowl             string `protobuf:"bytes,14,opt,name=knowl,proto3" json:"knowl,omitempty"`
	FileStorage       string `protobuf:"bytes,15,opt,name=file_storage,json=fileStorage,proto3" json:"file_storage,omitempty"`
}

func (x *ResourceQuotaSpec) Reset() {
	*x = ResourceQuotaSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_tenant_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceQuotaSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceQuotaSpec) ProtoMessage() {}

func (x *ResourceQuotaSpec) ProtoReflect() protoreflect.Message {
	mi := &file_proto_tenant_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceQuotaSpec.ProtoReflect.Descriptor instead.
func (*ResourceQuotaSpec) Descriptor() ([]byte, []int) {
	return file_proto_tenant_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceQuotaSpec) GetLimitsCpu() string {
	if x != nil {
		return x.LimitsCpu
	}
	return ""
}

func (x *ResourceQuotaSpec) GetLimitsMemory() string {
	if x != nil {
		return x.LimitsMemory
	}
	return ""
}

func (x *ResourceQuotaSpec) GetRequestsCpu() string {
	if x != nil {
		return x.RequestsCpu
	}
	return ""
}

func (x *ResourceQuotaSpec) GetRequestsMemory() string {
	if x != nil {
		return x.RequestsMemory
	}
	return ""
}

func (x *ResourceQuotaSpec) GetRequestsStorage() string {
	if x != nil {
		return x.RequestsStorage
	}
	return ""
}

func (x *ResourceQuotaSpec) GetPods() string {
	if x != nil {
		return x.Pods
	}
	return ""
}

func (x *ResourceQuotaSpec) GetBandwidth() string {
	if x != nil {
		return x.Bandwidth
	}
	return ""
}

func (x *ResourceQuotaSpec) GetEgressBandwidth() string {
	if x != nil {
		return x.EgressBandwidth
	}
	return ""
}

func (x *ResourceQuotaSpec) GetIngressBandwidth() string {
	if x != nil {
		return x.IngressBandwidth
	}
	return ""
}

func (x *ResourceQuotaSpec) GetGpu() string {
	if x != nil {
		return x.Gpu
	}
	return ""
}

func (x *ResourceQuotaSpec) GetGpuMemory() string {
	if x != nil {
		return x.GpuMemory
	}
	return ""
}

func (x *ResourceQuotaSpec) GetRequestsGpu() string {
	if x != nil {
		return x.RequestsGpu
	}
	return ""
}

func (x *ResourceQuotaSpec) GetRequestsGpuMemory() string {
	if x != nil {
		return x.RequestsGpuMemory
	}
	return ""
}

func (x *ResourceQuotaSpec) GetKnowl() string {
	if x != nil {
		return x.Knowl
	}
	return ""
}

func (x *ResourceQuotaSpec) GetFileStorage() string {
	if x != nil {
		return x.FileStorage
	}
	return ""
}

type TenantResourceQuota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string             `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`
	QuotaName string             `protobuf:"bytes,2,opt,name=quota_name,json=quotaName,proto3" json:"quota_name,omitempty"`
	Hard      *ResourceQuotaSpec `protobuf:"bytes,3,opt,name=hard,proto3" json:"hard,omitempty"`
	Used      *ResourceQuotaSpec `protobuf:"bytes,4,opt,name=used,proto3" json:"used,omitempty"`
}

func (x *TenantResourceQuota) Reset() {
	*x = TenantResourceQuota{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_tenant_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantResourceQuota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantResourceQuota) ProtoMessage() {}

func (x *TenantResourceQuota) ProtoReflect() protoreflect.Message {
	mi := &file_proto_tenant_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantResourceQuota.ProtoReflect.Descriptor instead.
func (*TenantResourceQuota) Descriptor() ([]byte, []int) {
	return file_proto_tenant_proto_rawDescGZIP(), []int{2}
}

func (x *TenantResourceQuota) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *TenantResourceQuota) GetQuotaName() string {
	if x != nil {
		return x.QuotaName
	}
	return ""
}

func (x *TenantResourceQuota) GetHard() *ResourceQuotaSpec {
	if x != nil {
		return x.Hard
	}
	return nil
}

func (x *TenantResourceQuota) GetUsed() *ResourceQuotaSpec {
	if x != nil {
		return x.Used
	}
	return nil
}

var File_proto_tenant_proto protoreflect.FileDescriptor

var file_proto_tenant_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x05, 0x0a, 0x06,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x55, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f,
	0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x3f, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x0c, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0c, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x53, 0x0a,
	0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x11, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x63, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x63, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x68,
	0x69, 0x70, 0x70, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x68, 0x69, 0x70, 0x70, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x95, 0x04, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x5f, 0x63, 0x70, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x43, 0x70, 0x75, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x43, 0x70, 0x75, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x6f, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61,
	0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x2b,
	0x0a, 0x11, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x67,
	0x70, 0x75, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x67, 0x70, 0x75, 0x12, 0x1d, 0x0a,
	0x0a, 0x67, 0x70, 0x75, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x67, 0x70, 0x75, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x47, 0x70, 0x75, 0x12,
	0x2e, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x67, 0x70, 0x75, 0x5f,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0xaf, 0x01, 0x0a, 0x13, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c,
	0x0a, 0x04, 0x68, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x68, 0x61, 0x72, 0x64, 0x12, 0x2c, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c,
	0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_tenant_proto_rawDescOnce sync.Once
	file_proto_tenant_proto_rawDescData = file_proto_tenant_proto_rawDesc
)

func file_proto_tenant_proto_rawDescGZIP() []byte {
	file_proto_tenant_proto_rawDescOnce.Do(func() {
		file_proto_tenant_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_tenant_proto_rawDescData)
	})
	return file_proto_tenant_proto_rawDescData
}

var file_proto_tenant_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_tenant_proto_goTypes = []interface{}{
	(*Tenant)(nil),              // 0: proto.Tenant
	(*ResourceQuotaSpec)(nil),   // 1: proto.ResourceQuotaSpec
	(*TenantResourceQuota)(nil), // 2: proto.TenantResourceQuota
	nil,                         // 3: proto.Tenant.TenantLabelsEntry
	nil,                         // 4: proto.Tenant.TenantAnnotationsEntry
}
var file_proto_tenant_proto_depIdxs = []int32{
	2, // 0: proto.Tenant.tenant_quotas:type_name -> proto.TenantResourceQuota
	3, // 1: proto.Tenant.tenant_labels:type_name -> proto.Tenant.TenantLabelsEntry
	4, // 2: proto.Tenant.tenant_annotations:type_name -> proto.Tenant.TenantAnnotationsEntry
	1, // 3: proto.TenantResourceQuota.hard:type_name -> proto.ResourceQuotaSpec
	1, // 4: proto.TenantResourceQuota.used:type_name -> proto.ResourceQuotaSpec
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_tenant_proto_init() }
func file_proto_tenant_proto_init() {
	if File_proto_tenant_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_tenant_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tenant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_tenant_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceQuotaSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_tenant_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantResourceQuota); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_tenant_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_tenant_proto_goTypes,
		DependencyIndexes: file_proto_tenant_proto_depIdxs,
		MessageInfos:      file_proto_tenant_proto_msgTypes,
	}.Build()
	File_proto_tenant_proto = out.File
	file_proto_tenant_proto_rawDesc = nil
	file_proto_tenant_proto_goTypes = nil
	file_proto_tenant_proto_depIdxs = nil
}
