// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_image_template.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ImageTemplateManager_ListTemplates_FullMethodName   = "/proto.ImageTemplateManager/ListTemplates"
	ImageTemplateManager_GetTemplate_FullMethodName     = "/proto.ImageTemplateManager/GetTemplate"
	ImageTemplateManager_CreateTemplate_FullMethodName  = "/proto.ImageTemplateManager/CreateTemplate"
	ImageTemplateManager_UpdateTemplate_FullMethodName  = "/proto.ImageTemplateManager/UpdateTemplate"
	ImageTemplateManager_DeleteTemplates_FullMethodName = "/proto.ImageTemplateManager/DeleteTemplates"
	ImageTemplateManager_CloneTemplate_FullMethodName   = "/proto.ImageTemplateManager/CloneTemplate"
)

// ImageTemplateManagerClient is the client API for ImageTemplateManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ImageTemplateManagerClient interface {
	ListTemplates(ctx context.Context, in *ListTemplatesReq, opts ...grpc.CallOption) (*ListTemplatesRsp, error)
	GetTemplate(ctx context.Context, in *GetTemplateReq, opts ...grpc.CallOption) (*GetTemplateRsp, error)
	CreateTemplate(ctx context.Context, in *CreateTemplateReq, opts ...grpc.CallOption) (*CreateTemplateRsp, error)
	UpdateTemplate(ctx context.Context, in *UpdateTemplateReq, opts ...grpc.CallOption) (*UpdateTemplateRsp, error)
	DeleteTemplates(ctx context.Context, in *DeleteTemplatesReq, opts ...grpc.CallOption) (*DeleteTemplatesRsp, error)
	CloneTemplate(ctx context.Context, in *CloneTemplateReq, opts ...grpc.CallOption) (*CloneTemplateRsp, error)
}

type imageTemplateManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewImageTemplateManagerClient(cc grpc.ClientConnInterface) ImageTemplateManagerClient {
	return &imageTemplateManagerClient{cc}
}

func (c *imageTemplateManagerClient) ListTemplates(ctx context.Context, in *ListTemplatesReq, opts ...grpc.CallOption) (*ListTemplatesRsp, error) {
	out := new(ListTemplatesRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_ListTemplates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageTemplateManagerClient) GetTemplate(ctx context.Context, in *GetTemplateReq, opts ...grpc.CallOption) (*GetTemplateRsp, error) {
	out := new(GetTemplateRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_GetTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageTemplateManagerClient) CreateTemplate(ctx context.Context, in *CreateTemplateReq, opts ...grpc.CallOption) (*CreateTemplateRsp, error) {
	out := new(CreateTemplateRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_CreateTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageTemplateManagerClient) UpdateTemplate(ctx context.Context, in *UpdateTemplateReq, opts ...grpc.CallOption) (*UpdateTemplateRsp, error) {
	out := new(UpdateTemplateRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_UpdateTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageTemplateManagerClient) DeleteTemplates(ctx context.Context, in *DeleteTemplatesReq, opts ...grpc.CallOption) (*DeleteTemplatesRsp, error) {
	out := new(DeleteTemplatesRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_DeleteTemplates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageTemplateManagerClient) CloneTemplate(ctx context.Context, in *CloneTemplateReq, opts ...grpc.CallOption) (*CloneTemplateRsp, error) {
	out := new(CloneTemplateRsp)
	err := c.cc.Invoke(ctx, ImageTemplateManager_CloneTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImageTemplateManagerServer is the server API for ImageTemplateManager service.
// All implementations must embed UnimplementedImageTemplateManagerServer
// for forward compatibility
type ImageTemplateManagerServer interface {
	ListTemplates(context.Context, *ListTemplatesReq) (*ListTemplatesRsp, error)
	GetTemplate(context.Context, *GetTemplateReq) (*GetTemplateRsp, error)
	CreateTemplate(context.Context, *CreateTemplateReq) (*CreateTemplateRsp, error)
	UpdateTemplate(context.Context, *UpdateTemplateReq) (*UpdateTemplateRsp, error)
	DeleteTemplates(context.Context, *DeleteTemplatesReq) (*DeleteTemplatesRsp, error)
	CloneTemplate(context.Context, *CloneTemplateReq) (*CloneTemplateRsp, error)
	mustEmbedUnimplementedImageTemplateManagerServer()
}

// UnimplementedImageTemplateManagerServer must be embedded to have forward compatible implementations.
type UnimplementedImageTemplateManagerServer struct {
}

func (UnimplementedImageTemplateManagerServer) ListTemplates(context.Context, *ListTemplatesReq) (*ListTemplatesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplates not implemented")
}
func (UnimplementedImageTemplateManagerServer) GetTemplate(context.Context, *GetTemplateReq) (*GetTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplate not implemented")
}
func (UnimplementedImageTemplateManagerServer) CreateTemplate(context.Context, *CreateTemplateReq) (*CreateTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTemplate not implemented")
}
func (UnimplementedImageTemplateManagerServer) UpdateTemplate(context.Context, *UpdateTemplateReq) (*UpdateTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTemplate not implemented")
}
func (UnimplementedImageTemplateManagerServer) DeleteTemplates(context.Context, *DeleteTemplatesReq) (*DeleteTemplatesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTemplates not implemented")
}
func (UnimplementedImageTemplateManagerServer) CloneTemplate(context.Context, *CloneTemplateReq) (*CloneTemplateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloneTemplate not implemented")
}
func (UnimplementedImageTemplateManagerServer) mustEmbedUnimplementedImageTemplateManagerServer() {}

// UnsafeImageTemplateManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ImageTemplateManagerServer will
// result in compilation errors.
type UnsafeImageTemplateManagerServer interface {
	mustEmbedUnimplementedImageTemplateManagerServer()
}

func RegisterImageTemplateManagerServer(s grpc.ServiceRegistrar, srv ImageTemplateManagerServer) {
	s.RegisterService(&ImageTemplateManager_ServiceDesc, srv)
}

func _ImageTemplateManager_ListTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).ListTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_ListTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).ListTemplates(ctx, req.(*ListTemplatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageTemplateManager_GetTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).GetTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_GetTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).GetTemplate(ctx, req.(*GetTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageTemplateManager_CreateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).CreateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_CreateTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).CreateTemplate(ctx, req.(*CreateTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageTemplateManager_UpdateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).UpdateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_UpdateTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).UpdateTemplate(ctx, req.(*UpdateTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageTemplateManager_DeleteTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTemplatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).DeleteTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_DeleteTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).DeleteTemplates(ctx, req.(*DeleteTemplatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageTemplateManager_CloneTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloneTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageTemplateManagerServer).CloneTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageTemplateManager_CloneTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageTemplateManagerServer).CloneTemplate(ctx, req.(*CloneTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ImageTemplateManager_ServiceDesc is the grpc.ServiceDesc for ImageTemplateManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ImageTemplateManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ImageTemplateManager",
	HandlerType: (*ImageTemplateManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListTemplates",
			Handler:    _ImageTemplateManager_ListTemplates_Handler,
		},
		{
			MethodName: "GetTemplate",
			Handler:    _ImageTemplateManager_GetTemplate_Handler,
		},
		{
			MethodName: "CreateTemplate",
			Handler:    _ImageTemplateManager_CreateTemplate_Handler,
		},
		{
			MethodName: "UpdateTemplate",
			Handler:    _ImageTemplateManager_UpdateTemplate_Handler,
		},
		{
			MethodName: "DeleteTemplates",
			Handler:    _ImageTemplateManager_DeleteTemplates_Handler,
		},
		{
			MethodName: "CloneTemplate",
			Handler:    _ImageTemplateManager_CloneTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_image_template.proto",
}
